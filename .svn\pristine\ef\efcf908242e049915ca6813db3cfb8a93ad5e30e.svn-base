﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    public partial class ReasonPanelBase : UserControl
    {
        public ReasonPanelBase()
        {
            InitializeComponent();
        }

        protected virtual string Title
        {
            get { return grp.Text; }
            set { grp.Text = value; }
        }

        protected ReasonBase reason = null;
        public virtual void AttachReason(ReasonBase reason)
        {
            this.reason = reason;
            this.Title = reason.Name;
        }
    }
}
