using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NoXNearestCellInfoForm : MinCloseForm
    {
        public NoXNearestCellInfoForm()
            : base()
        {
            InitializeComponent();
        }
        public void FillData(List<ScanTestPointNearestCellInfo> datas)
        {
            dataGridView.AutoGenerateColumns = false;
            dataGridView.DataSource = null;
            dataGridView.DataSource = datas;
        }

        private void tsmiExport2Xls_Click(object sender, EventArgs e)
        {
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(dataGridView);
            //ExcelOperator.ByTxtExcel(dataGridView);
        }
    }
}