﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    [Serializable]
    public class IFHOChangedCause : CauseBase
    {
        public override string Name
        {
            get { return "异频切换"; }
        }
        
        public int Second { get; set; } = 10;
        public override string Desc
        {
            get { return string.Format("判断低速率发生前{0}秒是否进行过异频切换", Second); }
        }

        public override string Suggestion
        {
            get { return null; }
        }

        public override void Judge(LowSpeedSeg segItem, List<Event> evts, List<TestPoint> allTP)
        {
            foreach (TestPoint pnt in segItem.TestPoints)
            {
                if (segItem.IsNeedJudge(pnt))
                {
                    dealTPs(segItem, allTP, pnt);
                }
            }
        }

        private void dealTPs(LowSpeedSeg segItem, List<TestPoint> allTP, TestPoint pnt)
        {
            int iFlag = 0;
            int bTime = pnt.Time - Second;
            foreach (TestPoint testPoint in allTP)
            {
                if (bTime <= testPoint.Time && testPoint.Time <= pnt.Time)
                {
                    bool setReason = judgeCellChanged(segItem, pnt, ref iFlag, testPoint);
                    if (setReason)
                    {
                        break;
                    }
                }
            }
        }

        private bool judgeCellChanged(LowSpeedSeg segItem, TestPoint pnt, ref int iFlag, TestPoint testPoint)
        {
            //判断是否发生过D->F,F->D切换(同时存在D,F频段)
            LTECell cellLow = testPoint.GetMainLTECell_TdOrFdd();
            if (cellLow != null)
            {
                if (LTECell.GetBandTypeByJT(cellLow.EARFCN) == LTEBandTypeJT.D1
                        || LTECell.GetBandTypeByJT(cellLow.EARFCN) == LTEBandTypeJT.D2
                        || LTECell.GetBandTypeByJT(cellLow.EARFCN) == LTEBandTypeJT.D3)
                {
                    if (iFlag == 2)
                    {
                        IFHOChangedCause cln = this.Clone() as IFHOChangedCause;
                        segItem.SetReason(new LowSpeedPointDetail(pnt, cln));
                        return true;
                    }
                    else
                    {
                        iFlag = 1;
                    }
                }
                else if (LTECell.GetBandTypeByJT(cellLow.EARFCN) == LTEBandTypeJT.F)
                {
                    if (iFlag == 1)
                    {
                        IFHOChangedCause cln = this.Clone() as IFHOChangedCause;
                        segItem.SetReason(new LowSpeedPointDetail(pnt, cln));
                        return true;
                    }
                    else
                    {
                        iFlag = 2;
                    }
                }
            }

            return false;
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                paramDic["second"] = this.Second;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.Second = (int)value["second"];
            }
        }
    }
}
