﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Model.Interface;
namespace MasterCom.RAMS.ZTFunc
{
    public class ZTWeakCoverRoadStatByEvent : ZTDIYQueryScanAnalysis_LTE
    {
        public ZTWeakCoverRoadStatByEvent(MainModel mainModel)
            : base(mainModel)
        {
            isAddEventToDTDataManager = false;
        }
        public override string Name
        {
            get { return "弱覆盖路段(按事件)"; }
        }
        public override string IconName
        {
            get { return "Images/event/handover.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22067, this.Name);
        }

        #region 变量        
        string strCityName = "";
        private int iTimeSpan = 0;
        private int iDistanceSpan = 0;
        private int iCurTime = 0;
        private double dCurLng = 0;
        private double dCurLat = 0;
        List<WeakCoverRoadStatInfo> eventList = null;
        List<WeakCoverRoadStatInfo> weakCoverRoadInfoLists = null;
        private ZTWeakCoverByEventSetForm setForm = null;
        Dictionary<CityGridKey, WeakCoverRoadSummary> weakCoverRoadSummaryInfoDic = null;
        #endregion

        protected override void query()
        {
            MainModel.ClearDTData();
            if (!getConditionBeforeQuery() || !prepareAskWhatEvent())
            {
                return;
            }
            InitRegionMop2();
            ClientProxy clientProxy = null;
            try
            {
                foreach (int DistrictID in condition.DistrictIDs)
                {
                    eventList = new List<WeakCoverRoadStatInfo>();
                    strCityName = DistrictManager.GetInstance().getDistrictName(DistrictID);
                    WaitBox.CanCancel = true;
                    WaitBox.Text = "正在查询 " + strCityName + "的事件信息...";
                    clientProxy = new ClientProxy();
                    if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName
                        , MainModel.User.Password, DistrictID) != ConnectResult.Success)
                    {
                        ErrorInfo = "连接服务器失败!";
                        return;
                    }
                    WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
                    clientProxy.Close();
                }
                MainModel.FireDTDataChanged(this);
                fireShowFormAfterQuery();
            }
            finally
            {
                //
            }
        }

        protected override bool getConditionBeforeQuery()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new ZTWeakCoverByEventSetForm();
            }
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                setForm.GetSetCond(ref iTimeSpan, ref iDistanceSpan);
                iCurTime = 0;
                dCurLng = 0;
                dCurLat = 0;
                mutRegionMopDic = new Dictionary<string, Dictionary<string, MapOperation2>>();
                weakCoverRoadInfoLists = new List<WeakCoverRoadStatInfo>();
                weakCoverRoadSummaryInfoDic = new Dictionary<CityGridKey, WeakCoverRoadSummary>();
                return true;
            }
            return false;
        }

        protected override bool prepareAskWhatEvent()
        {
            List<int> selectedEventIDs = new List<int>();
            selectedEventIDs.Add(1168);
            Condition.EventIDs = selectedEventIDs;
            return true;
        }

        protected override void doWithDTData(Event evt)
        {
            if (evt["Value3"] == null || evt["Value4"] == null
                || evt["Value3"].ToString() == "" || evt["Value4"].ToString() == null)
            {
                return;
            }
            WeakCoverRoadStatInfo evtItem = new WeakCoverRoadStatInfo();
            evtItem.ITime = evt.Time;
            evtItem.Evt = evt;
            eventList.Add(evtItem);
        }

        protected override void getResultAfterQuery()
        {
            WaitBox.Text = strCityName + "事件详情查询完毕，过滤同车弱覆盖事件...";
            List<WeakCoverRoadStatInfo> weakCoverRoadInfos = getWeakCoverRoadInfos();          
            weakCoverRoadInfoLists.AddRange(weakCoverRoadInfos);
            WaitBox.Text = strCityName + " 正在汇总处理...";
            List<CityGridKey> cityKeys = getCityGridTypeList(weakCoverRoadInfos);
            foreach (CityGridKey item in cityKeys)
            {
                WeakCoverRoadSummary weakCoverRoad = new WeakCoverRoadSummary();
                weakCoverRoad.StrCityName = item.strCityName;
                weakCoverRoad.StrGridType = item.strGridType;
                weakCoverRoad.StrGridName = item.strGridName;
                foreach (WeakCoverRoadStatInfo itemInfo in weakCoverRoadInfos)
                {
                    WeakCoverRoadSummary tmp = new WeakCoverRoadSummary();
                    tmp.StrCityName = itemInfo.StrCityName;
                    tmp.StrGridType = itemInfo.StrGridType;
                    tmp.StrGridName = itemInfo.StrGridName;
                    if (weakCoverRoad.StrGridType.Equals("汇总"))
                    {
                        tmp.StrGridType = "汇总";
                    }
                    if (weakCoverRoad.StrGridName.Equals("汇总"))
                    {
                        tmp.StrGridName = "汇总";
                    }
                    if (weakCoverRoad.StrCityName.Equals(tmp.StrCityName)
                        && weakCoverRoad.StrGridType.Equals(tmp.StrGridType)
                        && weakCoverRoad.StrGridName.Equals(tmp.StrGridName))
                    {
                        weakCoverRoad.IRoadNum++;
                        weakCoverRoad.DTimeSum += Convert.ToDouble(itemInfo.StrLastTime);
                        weakCoverRoad.DDistanceSum += Convert.ToDouble(itemInfo.StrLastDistance);
                    }
                }
                if (!weakCoverRoadSummaryInfoDic.ContainsKey(item))
                {
                    weakCoverRoadSummaryInfoDic[item] = weakCoverRoad;
                }
            }
            weakCoverRoadInfos.Clear();
        }

        private List<WeakCoverRoadStatInfo> getWeakCoverRoadInfos()
        {
            List<WeakCoverRoadStatInfo> weakCoverRoadInfos = new List<WeakCoverRoadStatInfo>();
            eventList.Sort(WeakCoverRoadStatInfo.GetCompareByTime());
            foreach (WeakCoverRoadStatInfo evtItem in eventList)
            {
                string gridTypeGrid = "";
                isContainPoint(evtItem.Evt.Longitude, evtItem.Evt.Latitude, ref gridTypeGrid);
                if (gridTypeGrid.Split(',').Length > 1 && !gridTypeGrid.Split(',')[1].Contains("当前区域")
                    && condition.DistrictIDs.Count > 1 && !gridTypeGrid.Contains(strCityName))
                {
                    continue;
                }
                double dDistance = MathFuncs.GetDistance(dCurLng, dCurLat, evtItem.Evt.Longitude, evtItem.Evt.Latitude);
                int iTime = evtItem.Evt.Time - iCurTime;
                if (iTime < iTimeSpan && dDistance < iDistanceSpan)
                {
                    continue;
                }
                iCurTime = evtItem.Evt.Time;
                dCurLng = evtItem.Evt.Longitude;
                dCurLat = evtItem.Evt.Latitude;
                WeakCoverRoadStatInfo weakCoverItem = new WeakCoverRoadStatInfo();
                weakCoverItem.StrCityName = strCityName;
                setStrGridInfo(evtItem, weakCoverItem);
                weakCoverItem.Evt = evtItem.Evt;
                weakCoverRoadInfos.Add(weakCoverItem);
            }
            eventList.Clear();
            return weakCoverRoadInfos;
        }

        private void setStrGridInfo(WeakCoverRoadStatInfo evtItem, WeakCoverRoadStatInfo weakCoverItem)
        {
            if (MainModel.AreaManager[evtItem.Evt.AreaTypeID] != null)
            {
                foreach (CategoryEnumItem itemArea in MainModel.AreaManager[evtItem.Evt.AreaTypeID])
                {
                    if (itemArea.ID == evtItem.Evt.AreaID)
                        weakCoverItem.StrGridName = itemArea.Name;
                }
            }
            if (evtItem.Evt.AreaTypeID == 2)
            {
                weakCoverItem.StrGridType = "高速";
            }
            else if (evtItem.Evt.AreaTypeID == 3)
            {
                weakCoverItem.StrGridType = "国道";
            }
            else if (evtItem.Evt.AreaTypeID == 4)
            {
                weakCoverItem.StrGridType = "铁路";
            }
            else
            {
                weakCoverItem.StrGridType = "道路干线";
                weakCoverItem.StrGridName = "无网格名称";
                string gridTypeGrid = "";
                isContainPoint(evtItem.Evt.Longitude, evtItem.Evt.Latitude, ref gridTypeGrid);
                if (gridTypeGrid != "")
                {
                    weakCoverItem.StrGridName = gridTypeGrid.Split(',')[1];
                }
            }
        }

        private List<CityGridKey> getCityGridTypeList(List<WeakCoverRoadStatInfo> weakCoverRoadInfos)
        {
            List<CityGridKey> cityKeys = new List<CityGridKey>();
            foreach (WeakCoverRoadStatInfo item in weakCoverRoadInfos)
            {
                CityGridKey cityItem = new CityGridKey(item.StrCityName, item.StrGridType, item.StrGridName);
                if (!cityKeys.Contains(cityItem))
                {
                    cityKeys.Add(cityItem);
                }
            }
            foreach (WeakCoverRoadStatInfo item in weakCoverRoadInfos)
            {
                CityGridKey cityItem = new CityGridKey(item.StrCityName, item.StrGridType, "汇总");
                if (!cityKeys.Contains(cityItem))
                {
                    cityKeys.Add(cityItem);
                }
            }
            cityKeys.Add(new CityGridKey(strCityName, "汇总", "汇总"));
            return cityKeys;
        }

        protected override void fireShowFormAfterQuery()
        {
            if (weakCoverRoadInfoLists.Count == 0)
            {
                MessageBox.Show("所选条件没有结果，请检查条件！");
                return;
            }
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTWeakCoverByEventResultForm).FullName);
            ZTWeakCoverByEventResultForm dataForm = obj == null ? null : obj as ZTWeakCoverByEventResultForm;
            if (dataForm == null || dataForm.IsDisposed)
            {
                dataForm = new ZTWeakCoverByEventResultForm(MainModel);
            }
            dataForm.FillData(weakCoverRoadInfoLists, weakCoverRoadSummaryInfoDic);
            if (!dataForm.Visible)
            {
                dataForm.Show(MainModel.MainForm);
            }
        }
    }

    public class WeakCoverRoadStatInfo
    {
        /// <summary>
        /// 事件时间(用于排序)
        /// </summary>
        public int ITime { get; set; }
        /// <summary>
        /// 地市名称
        /// </summary>
        public string StrCityName { get; set; }
        /// <summary>
        /// 网格类型
        /// </summary>
        public string StrGridType { get; set; }
        /// <summary>
        /// 网格名称
        /// </summary>
        public string StrGridName { get; set; }
        /// <summary>
        /// 事件信息
        /// </summary>
        public Event Evt { get; set; }
        /// <summary>
        /// 事件时间
        /// </summary>
        public string StrTime
        {
            get 
            {
                string value = "";
                if (this.Evt != null)
                {
                    value = Evt.DateTime.ToString().Replace("/","-");
                }
                return value;
            }
        }
        /// <summary>
        /// 事件经度
        /// </summary>
        public double DLng
        {
            get
            {
                double value = 0;
                if (this.Evt != null)
                {
                    value = Evt.Longitude;
                }
                return value;
            }
        }
        /// <summary>
        /// 事件纬度
        /// </summary>
        public double DLat
        {
            get
            {
                double value = 0;
                if (this.Evt != null)
                {
                    value = Evt.Latitude;
                }
                return value;
            }
        }
        /// <summary>
        /// 开始时间
        /// </summary>
        public string StrBeginTime
        {
            get
            {
                long timeValueStartAt = 0;
                object obj = Evt["Value1"];
                if (obj != null && obj is long)
                {
                    timeValueStartAt = (long)obj;
                    if (timeValueStartAt > 0)
                    {
                        return JavaDate.GetDateTimeFromMilliseconds(timeValueStartAt).ToString("yyyy-MM-dd HH:mm:ss");
                    }
                }
                return string.Empty;
            }
        }
        /// <summary>
        /// 结束时间
        /// </summary>
        public string StrEndTime
        {
            get 
            {
                long timeValueEndAt = 0;
                object obj = Evt["Value2"];
                if (obj != null && obj is long)
                {
                    timeValueEndAt = (long)obj;
                    if (timeValueEndAt > 0)
                    {
                        return JavaDate.GetDateTimeFromMilliseconds(timeValueEndAt).ToString("yyyy-MM-dd HH:mm:ss");
                    }
                }
                return string.Empty;
            }
        }
        /// <summary>
        /// 持续时间
        /// </summary>
        public string StrLastTime
        {
            get
            {
                long timeValueSpan = 0;
                timeValueSpan = (long)Evt["Value3"];
                if (timeValueSpan >= 0)
                {
                    return Math.Round(1.0 * timeValueSpan / 1000,2).ToString();
                }
                return "0";
            }
        }
        /// <summary>
        /// 持续距离
        /// </summary>
        public string StrLastDistance
        {
            get
            {
                long distance = 0;
                distance = (long)Evt["Value4"];
                if (distance >= 0)
                {
                    return Math.Round(1.0 * distance / 1000, 2).ToString();
                }
                return "0";
            }
        }

        public static IComparer<WeakCoverRoadStatInfo> GetCompareByTime()
        {
            if (comparerTime == null)
            {
                comparerTime = new ComparerByTime();
            }
            return comparerTime;
        }
        public class ComparerByTime : IComparer<WeakCoverRoadStatInfo>
        {
            public int Compare(WeakCoverRoadStatInfo x, WeakCoverRoadStatInfo y)
            {
                return x.ITime - y.ITime;
            }
        }
        private static IComparer<WeakCoverRoadStatInfo> comparerTime;
    }

    public class CityGridKey
    {
        public string strCityName { get; private set; }
        public string strGridType { get; private set; }
        public string strGridName { get; private set; }

        public CityGridKey()
        {
            strCityName = "";
            strGridType = "";
            strGridName = "";
        }

        public CityGridKey(string strCity,string strType, string strName)
        {
            this.strCityName = strCity;
            this.strGridType = strType;
            this.strGridName = strName;
        }

        public override bool Equals(object obj)
        {
            CityGridKey other = obj as CityGridKey;
            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.strCityName.Equals(other.strCityName) 
                && this.strGridType.Equals(other.strGridType) 
                && this.strGridName.Equals(other.strGridName));
        }

        public override int GetHashCode()
        {
            string strKey = strCityName.ToString() + "_" + strGridType.ToString() + "_" + strGridName.ToString();
            return strKey.GetHashCode();
        }
    }

    public class WeakCoverRoadSummary
    {
        /// <summary>
        /// 地市
        /// </summary>
        public string StrCityName { get; set; }
        /// <summary>
        /// 网格类型
        /// </summary>
        public string StrGridType { get; set; }
        /// <summary>
        /// 网格名称
        /// </summary>
        public string StrGridName { get; set; }
        /// <summary>
        /// 弱覆盖路段数
        /// </summary>
        public int IRoadNum { get; set; }
        /// <summary>
        /// 平均持续时长
        /// </summary>
        public double DTimeAvg
        {
            get
            {
                if (IRoadNum > 0)
                {
                    return Math.Round(dTimeSum / IRoadNum, 2);
                }
                else
                {
                    return 0;
                }
            }
        }

        private double dTimeSum;
        /// <summary>
        /// 持续总时长
        /// </summary>
        public double DTimeSum
        {
            get { return Math.Round(dTimeSum,2); }
            set { dTimeSum = value; }
        }
        /// <summary>
        /// 平均持续距离
        /// </summary>
        public double DDistanceAvg
        {
            get
            {
                if (IRoadNum > 0)
                {
                    return Math.Round(dDistanceSum / IRoadNum, 2);
                }
                else
                {
                    return 0;
                }
            }
        }

        private double dDistanceSum;
        /// <summary>
        /// 持续总距离
        /// </summary>
        public double DDistanceSum
        {
            get { return Math.Round(dDistanceSum,2); }
            set { dDistanceSum = value; }
        }
    }
}
