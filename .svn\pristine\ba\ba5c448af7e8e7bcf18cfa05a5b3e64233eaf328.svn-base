﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using Microsoft.Office.Interop.Excel;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class DropPerceptionForm : MinCloseForm
    {
        public DropPerceptionForm()
            : base()
        {
            InitializeComponent();

            listView404.ListViewItemSorter = new ListViewSorter(listView404);
            listView9893.ListViewItemSorter = new ListViewSorter(listView9893);
        }

        public void LoadDropPerceptionInfo()
        {
            try
            {
                listView404.ListViewItemSorter = null;
                listView9893.ListViewItemSorter = null;
                listView404.Items.Clear();
                listView9893.Items.Clear();

                int i404 = 1;
                if (MainModel.EventDropPerceptionDic.ContainsKey("404"))
                {
                    foreach (DropPerceptionInfo info in MainModel.EventDropPerceptionDic["404"])
                    {
                        ListViewItem listViewItem = new System.Windows.Forms.ListViewItem();
                        listViewItem.Tag = info;
                        listViewItem.Text = i404.ToString();
                        listViewItem.SubItems[0].Tag = i404;
                        listViewItem.SubItems.Add(info.district);
                        listViewItem.SubItems.Add(info.fileName);
                        listViewItem.SubItems.Add(info.eventHappenTime.ToString());
                        listViewItem.SubItems.Add(info.eventName);
                        listViewItem.SubItems.Add(info.evtLongitude.ToString());
                        listViewItem.SubItems.Add(info.evtLatitude.ToString());
                        listViewItem.SubItems.Add(info.evtLac.ToString());
                        listViewItem.SubItems.Add(info.evtCi.ToString());
                        listViewItem.SubItems.Add(info.kind);
                        listViewItem.SubItems.Add(info.dropTpLongitude.ToString());
                        listViewItem.SubItems.Add(info.dropTpLatitude.ToString());
                        listViewItem.SubItems.Add(info.dropTpLac.ToString());
                        listViewItem.SubItems.Add(info.dropTpCi.ToString());
                        listViewItem.SubItems.Add(info.cutoffStarttimeTp.ToString());
                        listViewItem.SubItems.Add(info.arfcn.ToString());
                        listViewItem.SubItems.Add(info.cpi.ToString());
                        listViewItem.SubItems.Add(info.dpch.ToString());
                        listViewItem.SubItems.Add(info.HS_PDSCH_RSCP.ToString());
                        listViewItem.SubItems.Add(info.HS_PDSCH_CI.ToString());
                        listViewItem.SubItems.Add(info.HS_WorkUARFCN.ToString());
                        listViewItem.SubItems.Add(info.avgPCCPCH_RSCP.ToString());
                        listViewItem.SubItems.Add(info.avgPCCPCH_C2I.ToString());
                        listViewItem.SubItems.Add(info.avgDPCH_RSCP.ToString());
                        listViewItem.SubItems.Add(info.avgDPCH_C2I.ToString());
                        listViewItem.SubItems.Add(info.switchTimes.ToString());
                        listViewItem.SubItems.Add(info.nbCell1Arfcn.ToString());
                        listViewItem.SubItems.Add(info.nbCell1Cpi.ToString());
                        listViewItem.SubItems.Add(info.nbCell1PCCPCH_RSCP.ToString());
                        listViewItem.SubItems.Add(info.nbCell2Arfcn.ToString());
                        listViewItem.SubItems.Add(info.nbCell2Cpi.ToString());
                        listViewItem.SubItems.Add(info.nbCell2PCCPCH_RSCP.ToString());
                        listViewItem.SubItems.Add(info.nbCell3Arfcn.ToString());
                        listViewItem.SubItems.Add(info.nbCell3Cpi.ToString());
                        listViewItem.SubItems.Add(info.nbCell3PCCPCH_RSCP.ToString());
                        listViewItem.SubItems.Add(info.nbCell4Arfcn.ToString());
                        listViewItem.SubItems.Add(info.nbCell4Cpi.ToString());
                        listViewItem.SubItems.Add(info.nbCell4PCCPCH_RSCP.ToString());
                        listViewItem.SubItems.Add(info.nbCell5Arfcn.ToString());
                        listViewItem.SubItems.Add(info.nbCell5Cpi.ToString());
                        listViewItem.SubItems.Add(info.nbCell5PCCPCH_RSCP.ToString());
                        listView404.Items.Add(listViewItem);
                        i404++;
                    }
                }

                int i9893 = 1;
                if (MainModel.EventDropPerceptionDic.ContainsKey("9893"))
                {
                    foreach (DropPerceptionInfo info in MainModel.EventDropPerceptionDic["9893"])
                    {
                        ListViewItem listViewItem = new System.Windows.Forms.ListViewItem();
                        listViewItem.Tag = info;
                        listViewItem.Text = i9893.ToString();
                        listViewItem.SubItems[0].Tag = i9893;
                        listViewItem.SubItems.Add(info.district);
                        listViewItem.SubItems.Add(info.fileName);
                        listViewItem.SubItems.Add(info.eventHappenTime.ToString());
                        listViewItem.SubItems.Add(info.eventName);
                        listViewItem.SubItems.Add(info.evtLongitude.ToString());
                        listViewItem.SubItems.Add(info.evtLatitude.ToString());
                        listViewItem.SubItems.Add(info.evtLac.ToString());
                        listViewItem.SubItems.Add(info.evtCi.ToString());
                        listViewItem.SubItems.Add(info.kind);
                        listViewItem.SubItems.Add(info.arfcn.ToString());
                        listViewItem.SubItems.Add(info.cpi.ToString());
                        listViewItem.SubItems.Add(info.dpch.ToString());
                        listViewItem.SubItems.Add(info.HS_PDSCH_RSCP.ToString());
                        listViewItem.SubItems.Add(info.HS_PDSCH_CI.ToString());
                        listViewItem.SubItems.Add(info.HS_WorkUARFCN.ToString());
                        listViewItem.SubItems.Add(info.avgPCCPCH_RSCP.ToString());
                        listViewItem.SubItems.Add(info.avgPCCPCH_C2I.ToString());
                        listViewItem.SubItems.Add(info.avgDPCH_RSCP.ToString());
                        listViewItem.SubItems.Add(info.avgDPCH_C2I.ToString());
                        listViewItem.SubItems.Add(info.switchTimes.ToString());
                        listViewItem.SubItems.Add(info.nbCell1Arfcn.ToString());
                        listViewItem.SubItems.Add(info.nbCell1Cpi.ToString());
                        listViewItem.SubItems.Add(info.nbCell1PCCPCH_RSCP.ToString());
                        listViewItem.SubItems.Add(info.nbCell2Arfcn.ToString());
                        listViewItem.SubItems.Add(info.nbCell2Cpi.ToString());
                        listViewItem.SubItems.Add(info.nbCell2PCCPCH_RSCP.ToString());
                        listViewItem.SubItems.Add(info.nbCell3Arfcn.ToString());
                        listViewItem.SubItems.Add(info.nbCell3Cpi.ToString());
                        listViewItem.SubItems.Add(info.nbCell3PCCPCH_RSCP.ToString());
                        listViewItem.SubItems.Add(info.nbCell4Arfcn.ToString());
                        listViewItem.SubItems.Add(info.nbCell4Cpi.ToString());
                        listViewItem.SubItems.Add(info.nbCell4PCCPCH_RSCP.ToString());
                        listViewItem.SubItems.Add(info.nbCell5Arfcn.ToString());
                        listViewItem.SubItems.Add(info.nbCell5Cpi.ToString());
                        listViewItem.SubItems.Add(info.nbCell5PCCPCH_RSCP.ToString());
                        listView9893.Items.Add(listViewItem);
                        i9893++;
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            WaitTextBox.Show("正在导出到Excel...", export2ExcelPaging);
        }

        private void makeTitle(_Worksheet worksheet, int col, string title, int width)
        {
            Range range = worksheet.Cells[1, col] as Range;
            range.Value2 = title;
            range.Font.Bold = true;
            range.ColumnWidth = width;
        }

        private void export2ExcelPaging()
        {
            Microsoft.Office.Interop.Excel.Application app = new Microsoft.Office.Interop.Excel.Application();
            try
            {
                if (app == null)
                {
                    throw (new Exception("ERROR: EXCEL couldn''t be started!"));
                }
                app.Visible = false;
                app.UserControl = false;
                Workbooks workbooks = app.Workbooks;
                _Workbook workbook = workbooks.Add(XlWBATemplate.xlWBATWorksheet);
                Sheets sheets = workbook.Worksheets;
                _Worksheet worksheet = (_Worksheet)sheets.get_Item(1);
                if (worksheet == null)
                {
                    throw (new Exception("ERROR: ERROR: worksheet == null"));
                }
                worksheet.Name = "感知掉线";
                //====列标题
                int colCount = 1;
                int idx = 1;
                makeTitle(worksheet, idx++, "序号", 10);
                makeTitle(worksheet, idx++, "地市", 10);
                makeTitle(worksheet, idx++, "文件名称", 30);
                makeTitle(worksheet, idx++, "事件发生时间", 15);
                makeTitle(worksheet, idx++, "事件类型", 10);
                makeTitle(worksheet, idx++, "事件经度", 10);
                makeTitle(worksheet, idx++, "事件纬度", 10);
                makeTitle(worksheet, idx++, "事件LAC", 10);
                makeTitle(worksheet, idx++, "事件CI", 10);
                makeTitle(worksheet, idx++, "开始断流点网络类型", 10);
                makeTitle(worksheet, idx++, "开始断流点经度", 10);
                makeTitle(worksheet, idx++, "开始断流点纬度", 10);
                makeTitle(worksheet, idx++, "开始断流点LAC", 10);
                makeTitle(worksheet, idx++, "开始断流点CI", 10);
                makeTitle(worksheet, idx++, "断流开始时间", 10);
                makeTitle(worksheet, idx++, "开始断流频点", 10);
                makeTitle(worksheet, idx++, "开始断流扰码", 10);
                makeTitle(worksheet, idx++, "开始断流DPCH频点", 10);
                makeTitle(worksheet, idx++, "开始断流TD_HSDPA_HS_SCCH_RSCP", 10);
                makeTitle(worksheet, idx++, "开始断流TD_HSDPA_HS_SCCH_CI", 10);
                makeTitle(worksheet, idx++, "开始断流TD_HSDPA_HS_WorkUARFCN", 10);
                makeTitle(worksheet, idx++, "断流5秒前平均PCCPCH_RSCP", 10);
                makeTitle(worksheet, idx++, "断流5秒前平均PCCPCH_C2I", 10);
                makeTitle(worksheet, idx++, "断流5秒前平均DPCH_RSCP", 10);
                makeTitle(worksheet, idx++, "断流5秒前平均DPCH_C2I", 10);
                makeTitle(worksheet, idx++, "断流十秒前切换次数", 10);
                makeTitle(worksheet, idx++, "邻区1主频", 10);
                makeTitle(worksheet, idx++, "邻区1扰码", 10);
                makeTitle(worksheet, idx++, "邻区1 PCCPCH_RSCP", 10);
                makeTitle(worksheet, idx++, "邻区2主频", 10);
                makeTitle(worksheet, idx++, "邻区2扰码", 10);
                makeTitle(worksheet, idx++, "邻区2 PCCPCH_RSCP", 10);
                makeTitle(worksheet, idx++, "邻区3主频", 10);
                makeTitle(worksheet, idx++, "邻区3扰码", 10);
                makeTitle(worksheet, idx++, "邻区3 PCCPCH_RSCP", 10);
                makeTitle(worksheet, idx++, "邻区4主频", 10);
                makeTitle(worksheet, idx++, "邻区4扰码", 10);
                makeTitle(worksheet, idx++, "邻区4 PCCPCH_RSCP", 10);
                makeTitle(worksheet, idx++, "邻区5主频", 10);
                makeTitle(worksheet, idx++, "邻区5扰码", 10);
                makeTitle(worksheet, idx++, "邻区5 PCCPCH_RSCP", 10);

                colCount = idx - 1;
                int rowAt = 2;
                int iloop = 0;
                if (MainModel.EventDropPerceptionDic.ContainsKey("404"))
                {
                    foreach (DropPerceptionInfo dInfo in MainModel.EventDropPerceptionDic["404"])
                    {
                        Range firstGroupRge_1 = worksheet.Cells[rowAt, 1] as Range;
                        firstGroupRge_1.Value2 = ++iloop;
                        Range firstGroupRge_2 = worksheet.Cells[rowAt, 2] as Range;
                        firstGroupRge_2.Value2 = dInfo.district;
                        Range firstGroupRge_3 = worksheet.Cells[rowAt, 3] as Range;
                        firstGroupRge_3.Value2 = dInfo.fileName;
                        Range firstGroupRge_4 = worksheet.Cells[rowAt, 4] as Range;
                        firstGroupRge_4.Value2 = dInfo.eventHappenTime;
                        Range firstGroupRge_5 = worksheet.Cells[rowAt, 5] as Range;
                        firstGroupRge_5.Value2 = dInfo.eventName;
                        Range firstGroupRge_6 = worksheet.Cells[rowAt, 6] as Range;
                        firstGroupRge_6.Value2 = dInfo.evtLongitude;
                        Range firstGroupRge_7 = worksheet.Cells[rowAt, 7] as Range;
                        firstGroupRge_7.Value2 = dInfo.evtLatitude;
                        Range firstGroupRge_8 = worksheet.Cells[rowAt, 8] as Range;
                        firstGroupRge_8.Value2 = dInfo.evtLac;
                        Range firstGroupRge_9 = worksheet.Cells[rowAt, 9] as Range;
                        firstGroupRge_9.Value2 = dInfo.evtCi;
                        Range firstGroupRge_10 = worksheet.Cells[rowAt, 10] as Range;
                        firstGroupRge_10.Value2 = dInfo.kind;
                        Range firstGroupRge_11 = worksheet.Cells[rowAt, 11] as Range;
                        firstGroupRge_11.Value2 = dInfo.dropTpLongitude;
                        Range firstGroupRge_12 = worksheet.Cells[rowAt, 12] as Range;
                        firstGroupRge_12.Value2 = dInfo.dropTpLatitude;
                        Range firstGroupRge_13 = worksheet.Cells[rowAt, 13] as Range;
                        firstGroupRge_13.Value2 = dInfo.dropTpLac;
                        Range firstGroupRge_14 = worksheet.Cells[rowAt, 14] as Range;
                        firstGroupRge_14.Value2 = dInfo.dropTpCi;
                        Range firstGroupRge_15 = worksheet.Cells[rowAt, 15] as Range;
                        firstGroupRge_15.Value2 = dInfo.cutoffStarttimeTp;
                        Range firstGroupRge_16 = worksheet.Cells[rowAt, 16] as Range;
                        firstGroupRge_16.Value2 = dInfo.arfcn;
                        Range firstGroupRge_17 = worksheet.Cells[rowAt, 17] as Range;
                        firstGroupRge_17.Value2 = dInfo.cpi;
                        Range firstGroupRge_18 = worksheet.Cells[rowAt, 18] as Range;
                        firstGroupRge_18.Value2 = dInfo.dpch;
                        Range firstGroupRge_19 = worksheet.Cells[rowAt, 19] as Range;
                        firstGroupRge_19.Value2 = dInfo.HS_PDSCH_RSCP;
                        Range firstGroupRge_20 = worksheet.Cells[rowAt, 20] as Range;
                        firstGroupRge_20.Value2 = dInfo.HS_PDSCH_CI;
                        Range firstGroupRge_21= worksheet.Cells[rowAt, 21] as Range;
                        firstGroupRge_21.Value2 = dInfo.HS_WorkUARFCN;
                        Range firstGroupRge_22 = worksheet.Cells[rowAt, 22] as Range;
                        firstGroupRge_22.Value2 = dInfo.avgPCCPCH_RSCP;
                        Range firstGroupRge_23 = worksheet.Cells[rowAt, 23] as Range;
                        firstGroupRge_23.Value2 = dInfo.avgPCCPCH_C2I;
                        Range firstGroupRge_24 = worksheet.Cells[rowAt, 24] as Range;
                        firstGroupRge_24.Value2 = dInfo.avgDPCH_RSCP;
                        Range firstGroupRge_25 = worksheet.Cells[rowAt, 25] as Range;
                        firstGroupRge_25.Value2 = dInfo.avgDPCH_C2I;
                        Range firstGroupRge_26 = worksheet.Cells[rowAt, 26] as Range;
                        firstGroupRge_26.Value2 = dInfo.switchTimes;
                        Range firstGroupRge_27 = worksheet.Cells[rowAt, 27] as Range;
                        firstGroupRge_27.Value2 = dInfo.nbCell1Arfcn;
                        Range firstGroupRge_28 = worksheet.Cells[rowAt, 28] as Range;
                        firstGroupRge_28.Value2 = dInfo.nbCell1Cpi;
                        Range firstGroupRge_29 = worksheet.Cells[rowAt, 29] as Range;
                        firstGroupRge_29.Value2 = dInfo.nbCell1PCCPCH_RSCP;
                        Range firstGroupRge_30 = worksheet.Cells[rowAt, 30] as Range;
                        firstGroupRge_30.Value2 = dInfo.nbCell2Arfcn;
                        Range firstGroupRge_31 = worksheet.Cells[rowAt, 31] as Range;
                        firstGroupRge_31.Value2 = dInfo.nbCell2Cpi;
                        Range firstGroupRge_32 = worksheet.Cells[rowAt, 32] as Range;
                        firstGroupRge_32.Value2 = dInfo.nbCell2PCCPCH_RSCP;
                        Range firstGroupRge_33 = worksheet.Cells[rowAt, 33] as Range;
                        firstGroupRge_33.Value2 = dInfo.nbCell3Arfcn;
                        Range firstGroupRge_34 = worksheet.Cells[rowAt, 34] as Range;
                        firstGroupRge_34.Value2 = dInfo.nbCell3Cpi;
                        Range firstGroupRge_35 = worksheet.Cells[rowAt, 35] as Range;
                        firstGroupRge_35.Value2 = dInfo.nbCell3PCCPCH_RSCP;
                        Range firstGroupRge_36 = worksheet.Cells[rowAt, 36] as Range;
                        firstGroupRge_36.Value2 = dInfo.nbCell4Arfcn;
                        Range firstGroupRge_37 = worksheet.Cells[rowAt, 37] as Range;
                        firstGroupRge_37.Value2 = dInfo.nbCell4Cpi;
                        Range firstGroupRge_38 = worksheet.Cells[rowAt, 38] as Range;
                        firstGroupRge_38.Value2 = dInfo.nbCell4PCCPCH_RSCP;
                        Range firstGroupRge_39 = worksheet.Cells[rowAt, 39] as Range;
                        firstGroupRge_39.Value2 = dInfo.nbCell5Arfcn;
                        Range firstGroupRge_40 = worksheet.Cells[rowAt, 40] as Range;
                        firstGroupRge_40.Value2 = dInfo.nbCell5Cpi;
                        Range firstGroupRge_41 = worksheet.Cells[rowAt, 41] as Range;
                        firstGroupRge_41.Value2 = dInfo.nbCell5PCCPCH_RSCP;
                        rowAt++;
                    }
                }
                for (int c = 1; c <= colCount; c++)
                {
                    Range range = worksheet.Cells[1, c] as Range;
                    range.EntireColumn.AutoFit();
                }
                for (int r = 1; r <= rowAt; r++)
                {
                    Range range = worksheet.Cells[r, 1] as Range;
                    range.EntireRow.AutoFit();
                }
                Range oRng = worksheet.get_Range("A1", "J" + rowAt);
                oRng.AutoFilter(1, Type.Missing, XlAutoFilterOperator.xlAnd, Type.Missing, true);
                

                _Worksheet worksheetOther = (_Worksheet)sheets.Add(Type.Missing, worksheet, Type.Missing, Type.Missing);
                worksheet = worksheetOther;
                if (worksheet == null)
                {
                    throw (new Exception("ERROR: ERROR: worksheet == null"));
                }
                worksheet.Name = "网络PDP去激活或者路由区更新拒绝";
                //====列标题
                idx = 1;
                makeTitle(worksheet, idx++, "序号", 10);
                makeTitle(worksheet, idx++, "地市", 10);
                makeTitle(worksheet, idx++, "文件名称", 30);
                makeTitle(worksheet, idx++, "事件发生时间", 15);
                makeTitle(worksheet, idx++, "事件类型", 10);
                makeTitle(worksheet, idx++, "经度", 10);
                makeTitle(worksheet, idx++, "纬度", 10);
                makeTitle(worksheet, idx++, "LAC", 10);
                makeTitle(worksheet, idx++, "CI", 10);
                makeTitle(worksheet, idx++, "当前频点网络类型", 10);
                makeTitle(worksheet, idx++, "当前频点频点", 10);
                makeTitle(worksheet, idx++, "当前频点扰码", 10);
                makeTitle(worksheet, idx++, "当前频点DPCH频点", 10);
                makeTitle(worksheet, idx++, "当前频点TD_HSDPA_HS_SCCH_RSCP", 10);
                makeTitle(worksheet, idx++, "当前频点TD_HSDPA_HS_SCCH_CI", 10);
                makeTitle(worksheet, idx++, "当前频点TD_HSDPA_HS_WorkUARFCN", 10);
                makeTitle(worksheet, idx++, "5秒前平均PCCPCH_RSCP", 10);
                makeTitle(worksheet, idx++, "5秒前平均PCCPCH_C2I", 10);
                makeTitle(worksheet, idx++, "5秒前平均DPCH_RSCP", 10);
                makeTitle(worksheet, idx++, "5秒前平均DPCH_C2I", 10);
                makeTitle(worksheet, idx++, "十秒前切换次数", 10);
                makeTitle(worksheet, idx++, "邻区1主频", 10);
                makeTitle(worksheet, idx++, "邻区1扰码", 10);
                makeTitle(worksheet, idx++, "邻区1 PCCPCH_RSCP", 10);
                makeTitle(worksheet, idx++, "邻区2主频", 10);
                makeTitle(worksheet, idx++, "邻区2扰码", 10);
                makeTitle(worksheet, idx++, "邻区2 PCCPCH_RSCP", 10);
                makeTitle(worksheet, idx++, "邻区3主频", 10);
                makeTitle(worksheet, idx++, "邻区3扰码", 10);
                makeTitle(worksheet, idx++, "邻区3 PCCPCH_RSCP", 10);
                makeTitle(worksheet, idx++, "邻区4主频", 10);
                makeTitle(worksheet, idx++, "邻区4扰码", 10);
                makeTitle(worksheet, idx++, "邻区4 PCCPCH_RSCP", 10);
                makeTitle(worksheet, idx++, "邻区5主频", 10);
                makeTitle(worksheet, idx++, "邻区5扰码", 10);
                makeTitle(worksheet, idx++, "邻区5 PCCPCH_RSCP", 10);

                colCount = idx - 1;
                rowAt = 2;
                iloop = 0;
                if (MainModel.EventDropPerceptionDic.ContainsKey("9893"))
                {
                    foreach (DropPerceptionInfo dInfo in MainModel.EventDropPerceptionDic["9893"])
                    {
                        Range firstGroupRge_1 = worksheet.Cells[rowAt, 1] as Range;
                        firstGroupRge_1.Value2 = ++iloop;
                        Range firstGroupRge_2 = worksheet.Cells[rowAt, 2] as Range;
                        firstGroupRge_2.Value2 = dInfo.district;
                        Range firstGroupRge_3 = worksheet.Cells[rowAt, 3] as Range;
                        firstGroupRge_3.Value2 = dInfo.fileName;
                        Range firstGroupRge_4 = worksheet.Cells[rowAt, 4] as Range;
                        firstGroupRge_4.Value2 = dInfo.eventHappenTime;
                        Range firstGroupRge_5 = worksheet.Cells[rowAt, 5] as Range;
                        firstGroupRge_5.Value2 = dInfo.eventName;
                        Range firstGroupRge_6 = worksheet.Cells[rowAt, 6] as Range;
                        firstGroupRge_6.Value2 = dInfo.evtLongitude;
                        Range firstGroupRge_7 = worksheet.Cells[rowAt, 7] as Range;
                        firstGroupRge_7.Value2 = dInfo.evtLatitude;
                        Range firstGroupRge_8 = worksheet.Cells[rowAt, 8] as Range;
                        firstGroupRge_8.Value2 = dInfo.evtLac;
                        Range firstGroupRge_9 = worksheet.Cells[rowAt, 9] as Range;
                        firstGroupRge_9.Value2 = dInfo.evtCi;
                        Range firstGroupRge_10 = worksheet.Cells[rowAt, 10] as Range;
                        firstGroupRge_10.Value2 = dInfo.kind;
                        Range firstGroupRge_11 = worksheet.Cells[rowAt, 11] as Range;
                        firstGroupRge_11.Value2 = dInfo.arfcn;
                        Range firstGroupRge_12 = worksheet.Cells[rowAt, 12] as Range;
                        firstGroupRge_12.Value2 = dInfo.cpi;
                        Range firstGroupRge_13 = worksheet.Cells[rowAt, 13] as Range;
                        firstGroupRge_13.Value2 = dInfo.dpch;
                        Range firstGroupRge_14 = worksheet.Cells[rowAt, 14] as Range;
                        firstGroupRge_14.Value2 = dInfo.HS_PDSCH_RSCP;
                        Range firstGroupRge_15 = worksheet.Cells[rowAt, 15] as Range;
                        firstGroupRge_15.Value2 = dInfo.HS_PDSCH_CI;
                        Range firstGroupRge_16= worksheet.Cells[rowAt, 16] as Range;
                        firstGroupRge_16.Value2 = dInfo.HS_WorkUARFCN;
                        Range firstGroupRge_17 = worksheet.Cells[rowAt, 17] as Range;
                        firstGroupRge_17.Value2 = dInfo.avgPCCPCH_RSCP;
                        Range firstGroupRge_18 = worksheet.Cells[rowAt, 18] as Range;
                        firstGroupRge_18.Value2 = dInfo.avgPCCPCH_C2I;
                        Range firstGroupRge_19 = worksheet.Cells[rowAt, 19] as Range;
                        firstGroupRge_19.Value2 = dInfo.avgDPCH_RSCP;
                        Range firstGroupRge_20 = worksheet.Cells[rowAt, 20] as Range;
                        firstGroupRge_20.Value2 = dInfo.avgDPCH_C2I;
                        Range firstGroupRge_21 = worksheet.Cells[rowAt, 21] as Range;
                        firstGroupRge_21.Value2 = dInfo.switchTimes;
                        Range firstGroupRge_22 = worksheet.Cells[rowAt, 22] as Range;
                        firstGroupRge_22.Value2 = dInfo.nbCell1Arfcn;
                        Range firstGroupRge_23 = worksheet.Cells[rowAt, 23] as Range;
                        firstGroupRge_23.Value2 = dInfo.nbCell1Cpi;
                        Range firstGroupRge_24 = worksheet.Cells[rowAt, 24] as Range;
                        firstGroupRge_24.Value2 = dInfo.nbCell1PCCPCH_RSCP;
                        Range firstGroupRge_25 = worksheet.Cells[rowAt, 25] as Range;
                        firstGroupRge_25.Value2 = dInfo.nbCell2Arfcn;
                        Range firstGroupRge_26 = worksheet.Cells[rowAt, 26] as Range;
                        firstGroupRge_26.Value2 = dInfo.nbCell2Cpi;
                        Range firstGroupRge_27 = worksheet.Cells[rowAt, 27] as Range;
                        firstGroupRge_27.Value2 = dInfo.nbCell2PCCPCH_RSCP;
                        Range firstGroupRge_28 = worksheet.Cells[rowAt, 28] as Range;
                        firstGroupRge_28.Value2 = dInfo.nbCell3Arfcn;
                        Range firstGroupRge_29 = worksheet.Cells[rowAt, 29] as Range;
                        firstGroupRge_29.Value2 = dInfo.nbCell3Cpi;
                        Range firstGroupRge_30 = worksheet.Cells[rowAt, 30] as Range;
                        firstGroupRge_30.Value2 = dInfo.nbCell3PCCPCH_RSCP;
                        Range firstGroupRge_31 = worksheet.Cells[rowAt, 31] as Range;
                        firstGroupRge_31.Value2 = dInfo.nbCell4Arfcn;
                        Range firstGroupRge_32 = worksheet.Cells[rowAt, 32] as Range;
                        firstGroupRge_32.Value2 = dInfo.nbCell4Cpi;
                        Range firstGroupRge_33 = worksheet.Cells[rowAt, 33] as Range;
                        firstGroupRge_33.Value2 = dInfo.nbCell4PCCPCH_RSCP;
                        Range firstGroupRge_34 = worksheet.Cells[rowAt, 34] as Range;
                        firstGroupRge_34.Value2 = dInfo.nbCell5Arfcn;
                        Range firstGroupRge_35 = worksheet.Cells[rowAt, 35] as Range;
                        firstGroupRge_35.Value2 = dInfo.nbCell5Cpi;
                        Range firstGroupRge_36 = worksheet.Cells[rowAt, 36] as Range;
                        firstGroupRge_36.Value2 = dInfo.nbCell5PCCPCH_RSCP;
                        rowAt++;
                    }
                }
                for (int c = 1; c <= colCount; c++)
                {
                    Range range = worksheet.Cells[1, c] as Range;
                    range.EntireColumn.AutoFit();
                }
                for (int r = 1; r <= rowAt; r++)
                {
                    Range range = worksheet.Cells[r, 1] as Range;
                    range.EntireRow.AutoFit();
                }
                oRng = worksheet.get_Range("A1", "J" + rowAt);
                oRng.AutoFilter(1, Type.Missing, XlAutoFilterOperator.xlAnd, Type.Missing, true);
             
                app.Visible = true;
                app.UserControl = true;
            }
            catch (System.Exception ex)
            {
                MessageBox.Show(this, "导出Excel出错：" + ex.Message);
            }
            finally
            {
                WaitTextBox.Close();
            }
        }
    }
}