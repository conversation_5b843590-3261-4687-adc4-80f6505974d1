﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors.Controls;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public partial class VillagePanel : UserControl
    {
        public VillagePanel(EventHandler btnAddHandler)
        {
            InitializeComponent();

            simpleButtonAdd.Click += btnAddHandler;
        }

        public void FillData(List<AreaBase> leafs)
        {
            checkedListBoxControlVillage.Items.Clear();

            checkedListBoxControlVillage.DataSource = leafs;
            checkEditAll_CheckedChanged(null, null);
        }

        private void checkEditAll_CheckedChanged(object sender, EventArgs e)
        {
            checkedListBoxControlVillage.BeginUpdate();
            if (checkEditAll.Checked)
            {
                checkedListBoxControlVillage.CheckAll();
            }
            else
            {
                checkedListBoxControlVillage.UnCheckAll();
            }
            checkedListBoxControlVillage.EndUpdate();
        }

        public List<AreaBase> GetChecks()
        {
            List<AreaBase> checks = new List<AreaBase>();
            foreach (AreaBase area in checkedListBoxControlVillage.CheckedItems)
            {
                checks.Add(area);
            }
            return checks;
        }
    }
}
