﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System.IO;

namespace MasterCom.RAMS.ZTFunc
{
    public class HighSpeedRailPrivateNetQuery : DIYAnalyseByFileBackgroundBase
    {
        /// <summary>
        /// 当前区域的地市列表,用于匹配采样点所属地市
        /// </summary>
        private List<ResvRegion> resvRegionList = null;

        /// <summary>
        /// 界面设置的条件
        /// </summary>
        protected HighSpeedRailPrivateNetCondition privateNetCondition = new HighSpeedRailPrivateNetCondition();

        #region 单例
        protected static readonly object lockObj = new object();
        private static HighSpeedRailPrivateNetQuery intance = null;
        public static HighSpeedRailPrivateNetQuery GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new HighSpeedRailPrivateNetQuery();
                    }
                }
            }
            return intance;
        }
        #endregion

        /// <summary>
        /// 最终的弱覆盖路段结果集合
        /// </summary>
        protected List<PrivateNetWeakCoverInfo> ZTWeakCovRoadInfoList = new List<PrivateNetWeakCoverInfo>();
        /// <summary>
        /// 最终的质差路段结果集合
        /// </summary>
        protected List<PrivateNetWeakSinrInfo> ZTWeakSINRInfoList = new List<PrivateNetWeakSinrInfo>();
        /// <summary>
        /// 最终的出网结果集合
        /// </summary>
        protected List<OutPrivateNetInfo> ZTOutPrivateNetInfoList = new List<OutPrivateNetInfo>();

        protected HighSpeedRailPrivateNetQuery()
            : base(MainModel.GetInstance())
        {
            if (intance != null)
            {
                return;
            }
            ServiceTypes.Clear();
#if LT
            carrierID = CarrierType.ChinaUnicom;
#else
            carrierID = CarrierType.ChinaMobile;
#endif
            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetLteAllServiceTypes());
        }

        public override string Name
        {
            get { return "高铁专项(按区域)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22120, this.Name);
        }

        protected override bool getCondition()
        {
            resvRegionList = getRegionList();

            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            HighSpeedRailPrivateNetDlg dlg = new HighSpeedRailPrivateNetDlg(privateNetCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                privateNetCondition = dlg.GetConditon();
                return true;
            }
            privateNetCondition = dlg.GetConditon();
            return false;
        }

        /// <summary>
        /// 根据地图中的shp文件获取当前地市的地市区域列表
        /// </summary>
        /// <returns></returns>
        private List<ResvRegion> getRegionList()
        {
            List<ResvRegion> regionList = new List<ResvRegion>();
            string folderPath = getFolderPath();
            bool mapDirectoryExist = Directory.Exists(folderPath);
            if (!mapDirectoryExist)
            {
                return regionList;
            }

            DirectoryInfo theFolder = new DirectoryInfo(folderPath);
            System.IO.FileInfo[] files = theFolder.GetFiles("*.shp");
            foreach (var file in files)
            {
                if (file.Name.Contains("行政区"))
                {
                    regionList = getRegionListByFile(file);
                    return regionList;
                }
            }
            return regionList;
        }

        private string getFolderPath()
        {
            string folderPath = Application.StartupPath + @"\GEOGRAPHIC";
            string city = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
            folderPath += @"\" + city;
            return folderPath;
        }

        private string getWithoutTypeName(string fileName)
        {
            string fileNameWithoutType = fileName;
            if (fileNameWithoutType.LastIndexOf('.') >= 0)
            {
                fileNameWithoutType = fileNameWithoutType.Substring(0, fileNameWithoutType.LastIndexOf('.'));
            }
            return fileNameWithoutType;
        }

        private List<ResvRegion> getRegionListByFile(System.IO.FileInfo file)
        {
            List<ResvRegion> regionList = new List<ResvRegion>();
            string fileNameWithoutType = getWithoutTypeName(file.Name);
            MapWinGIS.Shapefile table = new MapWinGIS.Shapefile();
            try
            {
                if (!table.Open(file.FullName, null))
                {
                    return regionList;
                }

                string columnName = "NAME";
                int columnIndex = MapOperation.GetColumnFieldIndex(table, columnName);

                for (int i = 0; i < table.NumShapes; i++)
                {
                    IRegionShape region = getValidRegionShape(table, i, columnIndex, fileNameWithoutType);
                    if (region.JudgeRegionNotNull())
                    {
                        regionList.Add((ResvRegion)region);
                    }
                }
                return regionList;
            }
            catch
            {
                return regionList;
            }
            finally
            {
                table.Close();
            }
        }

        private IRegionShape getValidRegionShape(MapWinGIS.Shapefile table, int shapeIndex, int columnIndex, string fileNameWithoutType)
        {
            ResvRegion region = null;
            MapWinGIS.Shape shape = table.get_Shape(shapeIndex);
            if (shape != null)
            {
                String regionName = table.get_CellValue(columnIndex, shapeIndex).ToString();
                if (regionName != null && regionName.Trim().Length > 0
                    && (shape.ShapeType == MapWinGIS.ShpfileType.SHP_POLYGON
                        || shape.ShapeType == MapWinGIS.ShpfileType.SHP_POLYGONZ
                        || shape.ShapeType == MapWinGIS.ShpfileType.SHP_POLYLINE
                        || shape.ShapeType == MapWinGIS.ShpfileType.SHP_POLYLINEZ))
                {
                    region = new ResvRegion();
                    region.RootNodeName = fileNameWithoutType;
                    region.RegionName = regionName;
                    region.Shape = shape;
                    return region;
                }
            }
            return new NullRegionShape();
        }

        protected override void fireShowForm()
        {
            MainModel.FireSetDefaultMapSerialTheme("lte_RSRP");

            HighSpeedRailPrivateNetForm frm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(HighSpeedRailPrivateNetForm)) as HighSpeedRailPrivateNetForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new HighSpeedRailPrivateNetForm(MainModel);
            }
            frm.PrivateNetCondition = privateNetCondition;
            frm.FillData(ZTWeakCovRoadInfoList, ZTWeakSINRInfoList, ZTOutPrivateNetInfoList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            ZTWeakCovRoadInfoList = null;
            ZTWeakSINRInfoList = null;
            ZTOutPrivateNetInfoList = null;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            ZTWeakCovRoadInfoList = new List<PrivateNetWeakCoverInfo>();
            ZTWeakSINRInfoList = new List<PrivateNetWeakSinrInfo>();
            ZTOutPrivateNetInfoList = new List<OutPrivateNetInfo>();
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    #region 通用参数
                    string lineName = "";
                    string fileName = "";
                    string[] fileNames = fileDataManager.FileName.Split(new char[1] { '_' }, 2);
                    if (fileNames.Length == 2)
                    {
                        lineName = fileNames[0];
                        fileName = fileNames[1];
                    }
                    else
                    {
                        fileName = fileDataManager.FileName;
                    }
                    #endregion
                    PrivateNetDataDeal.Init(lineName, fileName, resvRegionList, privateNetCondition);
                    dealWithData(fileDataManager.TestPoints);
                    PrivateNetDataDeal.Clear();
                }
            }
            catch (Exception e)
            {
                log.Error(e.Message + Environment.NewLine + e.StackTrace);
            }
        }

        /// <summary>
        /// 按采样点对数据进行处理
        /// </summary>
        /// <param name="testPoints">文件中的所有采样点</param>
        private void dealWithData(List<TestPoint> testPoints)
        {
            double weakCoverDuration = 0;
            long lastWeakCoverTime = 0;
            List<TestPoint> tpWeakCoverLists = new List<TestPoint>();
            PrivateNetWeakOverDataDeal saveWeakCover = new PrivateNetWeakOverDataDeal(ZTWeakCovRoadInfoList);

            double weakSINRduration = 0;
            long lastWeakSINRTime = 0;
            List<TestPoint> tpWeakSINRLists = new List<TestPoint>();
            PrivateNetWeakSinrDataDeal saveWeakSINR = new PrivateNetWeakSinrDataDeal(ZTWeakSINRInfoList);

            double outPrivateNetDuration = 0;
            long lastOutPrivateNetTime = 0;
            List<TestPoint> tpOutPrivateNetLists = new List<TestPoint>();
            PrivateNetOutNetDataDeal dealOutNet = new PrivateNetOutNetDataDeal(ZTOutPrivateNetInfoList);

            //记录每一次出专网的第一个采样点的index,用于判断出网原因
            int outPrivateNetIndex = -1;
            //记录上一次专网内第一个采样点的Index
            int privateNetIndex = -1;

            for (int i = 0; i < testPoints.Count; i++)
            {
                //由于专网小区只为LTE小区,所以只判断lte的TAC和ECI
                int? tac = (int?)(ushort?)testPoints[i]["lte_TAC"];
                int? eci = (int?)testPoints[i]["lte_ECI"];
                if (tac != null && eci != null)
                {
                    if (!dealOutNet.judgePrivateNetCell((int)tac, (int)eci, testPoints[i], ref outPrivateNetDuration, ref lastOutPrivateNetTime, tpOutPrivateNetLists))
                    {
                        outPrivateNetIndex = setIndex(outPrivateNetIndex, i);

                        //如果出网前一直连续弱覆盖或质差则保存(如果,出网原因的条件与弱覆盖和质差一样,此时的出网原因就是这个)
                        saveWeakCover.SaveDataInfo(ref weakCoverDuration, ref lastWeakCoverTime, tpWeakCoverLists, new PrivateNetWeakCoverInfo());
                        saveWeakSINR.SaveDataInfo(ref weakSINRduration, ref lastWeakSINRTime, tpWeakSINRLists, new PrivateNetWeakSinrInfo());
                    }
                    else
                    {
                        dealOutNet.SaveDataInfo(testPoints, ref outPrivateNetIndex, ref privateNetIndex, ref outPrivateNetDuration,
                            ref lastOutPrivateNetTime, tpOutPrivateNetLists, testPoints[i].DateTime.ToString());

                        privateNetIndex = setIndex(privateNetIndex, i);

                        //判断是否有连续弱覆盖
                        saveWeakCover.judgeContinuousTPs(testPoints[i], ref weakCoverDuration, ref lastWeakCoverTime, tpWeakCoverLists, new PrivateNetWeakCoverInfo());
                        //判断是否有连续质差
                        saveWeakSINR.judgeContinuousTPs(testPoints[i], ref weakSINRduration, ref lastWeakSINRTime, tpWeakSINRLists, new PrivateNetWeakSinrInfo());
                    }
                }
            }
            //结束时还处于异常状态(出网,弱覆盖,质差)
            dealOutNet.SaveDataInfo(testPoints, ref outPrivateNetIndex, ref privateNetIndex, ref outPrivateNetDuration, ref lastOutPrivateNetTime,
                tpOutPrivateNetLists, "");
            saveWeakCover.SaveDataInfo(ref weakCoverDuration, ref lastWeakCoverTime, tpWeakCoverLists, new PrivateNetWeakCoverInfo());
            saveWeakSINR.SaveDataInfo(ref weakSINRduration, ref lastWeakSINRTime, tpWeakSINRLists, new PrivateNetWeakSinrInfo());
        }

        private int setIndex(int index, int i)
        {
            if (index == -1)
            {
                index = i;
            }

            return index;
        }
    }

    /// <summary>
    /// 数据处理类的父类
    /// </summary>
    public class PrivateNetDataDeal
    {
        public PrivateNetDataDeal()
        {
            LineName = "";
            FileName = "";
        }

        /// <summary>
        /// 线路名
        /// </summary>
        public static string LineName { get; set; }
        /// <summary>
        /// 文件名
        /// </summary>
        public static string FileName { get; set; }
        /// <summary>
        /// 地市列表
        /// </summary>
        public static List<ResvRegion> ResvRegionList { get; set; }
        /// <summary>
        /// 界面设置的条件
        /// </summary>
        public static HighSpeedRailPrivateNetCondition PrivateNetCondition { get; set; }
        /// <summary>
        /// 初始化参数
        /// </summary>
        public static void Init(string lineName, string fileName, List<ResvRegion> resvRegionList, HighSpeedRailPrivateNetCondition privateNetCondition)
        {
            LineName = lineName;
            FileName = fileName;
            ResvRegionList = resvRegionList;
            PrivateNetCondition = privateNetCondition;
        }

        public static void Clear()
        {
            LineName = "";
            FileName = "";
            ResvRegionList = null;
            PrivateNetCondition = null;
        }

        #region 判断满足条件保存最终结果到相应结果集中
        /// <summary>
        /// 保存满足条件的最终结果到相应结果集中
        /// </summary>
        /// <param name="duration">持续时长</param>
        /// <param name="lastWeakTPTime">上个满足条件的采样点时间</param>
        /// <param name="tpLists">满足条件的采样点集合</param>
        /// <param name="info">结果类的数据</param>
        public virtual void SaveDataInfo(ref double duration, ref long lastWeakTPTime, List<TestPoint> tpLists, PrivateNetInfo info)
        {
            if (tpLists.Count > 0)
            {
                if (duration >= getDurationCondition())
                {
                    info.Duration = duration;
                    info.LineName = LineName;
                    info.StartTime = tpLists[0].DateTime.ToString();
                    info.FileName = FileName;
                    info.Longitude = tpLists[0].Longitude;
                    info.Latitude = tpLists[0].Latitude;
                    foreach (TestPoint tp in tpLists)
                    {
                        int? eci = (int?)tp["lte_ECI"];
                        if (eci != null && !info.ECILists.Contains(eci.ToString()))
                        {
                            info.ECILists.Add(eci.ToString());
                        }

                        info.TPLists.Add(tp);

                        setCityName(info, tp);
                    }
                    info.SetReason(judgeReasonType(tpLists));

                    addResultData(info);
                }
                duration = 0;
                lastWeakTPTime = 0;
                tpLists.Clear();
            }
        }

        private static void setCityName(PrivateNetInfo info, TestPoint tp)
        {
            if (string.IsNullOrEmpty(info.CityName))
            {
                foreach (ResvRegion region in ResvRegionList)
                {
                    if (region.GeoOp.CheckPointInRegion(tp.Longitude, tp.Latitude))
                    {
                        info.CityName = region.RegionName;
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// 在SaveDataInfo()中判断的持续时长条件
        /// 依赖于SaveDataInfo(),如果SaveDataInfo()被子类重写本函数将无用
        /// </summary>
        /// <returns></returns>
        protected virtual double getDurationCondition()
        {
            return 0;
        }

        /// <summary>
        /// 在SaveDataInfo()中判断对应结果的原因
        /// 依赖于SaveDataInfo(),如果SaveDataInfo()被子类重写本函数将无用
        /// </summary>
        /// <param name="tpLists"></param>
        /// <returns></returns>
        protected virtual int judgeReasonType(List<TestPoint> tpLists)
        {
            return 0;
        }

        /// <summary>
        /// 在SaveDataInfo()中添加最终结果的合集
        /// 依赖于SaveDataInfo(),如果SaveDataInfo()被子类重写本函数将无用
        /// </summary>
        /// <param name="info"></param>
        protected virtual void addResultData(PrivateNetInfo info)
        {

        }
        #endregion

        #region 判断满足条件的连续采样点
        /// <summary>
        /// 判断满足条件的连续采样点
        /// </summary>
        /// <param name="testPoint">当前需判断的采样点</param>
        /// <param name="weakduration">持续时长</param>
        /// <param name="lastWeakTPTime">上个满足条件的采样点时间</param>
        /// <param name="tpWeakLists">满足条件的采样点合集</param>
        /// <param name="dataInfo">结果类的数据</param>
        public virtual void judgeContinuousTPs(TestPoint testPoint, ref double weakduration, ref long lastWeakTPTime,
            List<TestPoint> tpWeakLists, PrivateNetInfo dataInfo)
        {
            float? dataValue = getDataValue(testPoint);
            //此时dataValue为null忽略
            if (dataValue != null)
            {
                //dataValue小于条件值,累加持续时间
                if (dataValue < getDataValueCondition())
                {
                    addLTEWeakPoint(ref weakduration, ref lastWeakTPTime, tpWeakLists, testPoint);
                }
                else
                {
                    //当dataValue不满足条件时,结束连续的累加,此时判断持续时间是否满足条件
                    SaveDataInfo(ref weakduration, ref lastWeakTPTime, tpWeakLists, dataInfo);
                }
            }
        }

        /// <summary>
        /// 在judgeContinuousTPs()中需要判断的条件
        /// 依赖于judgeContinuousTPs(),如果judgeContinuousTPs()被子类重写本函数将无用
        /// </summary>
        /// <returns></returns>
        protected virtual int getDataValueCondition()
        {
            return 0;
        }

        /// <summary>
        /// 在judgeContinuousTPs()中需要判断的采样点的值
        /// 依赖于judgeContinuousTPs(),如果judgeContinuousTPs()被子类重写本函数将无用
        /// </summary>
        /// <param name="testPoint"></param>
        /// <returns></returns>
        protected virtual float? getDataValue(TestPoint testPoint)
        {
            return null;
        }

        /// <summary>
        /// 在judgeContinuousTPs()中,添加满足条件的采样点并累计持续时间
        /// 依赖于judgeContinuousTPs(),如果judgeContinuousTPs()被子类重写本函数将无用
        /// </summary>
        /// <param name="duration">持续时长</param>
        /// <param name="lastWeakTPTime">上个满足弱覆盖或质差的采样点的时间</param>
        /// <param name="tpLists">本次连续弱覆盖或质差的采样点合集</param>
        /// <param name="curTP">当前弱覆盖或质差的采样点</param>
        protected virtual void addLTEWeakPoint(ref double duration, ref long lastWeakTPTime, List<TestPoint> tpLists,
            TestPoint curTP)
        {
            if (lastWeakTPTime != 0)
            {
                double timeGap = Math.Abs(1.0 * (lastWeakTPTime - curTP.lTimeWithMillsecond) / 1000);
                duration += timeGap;
            }
            lastWeakTPTime = curTP.lTimeWithMillsecond;
            tpLists.Add(curTP);
        }
        #endregion
    }

    public class PrivateNetWeakOverDataDeal : PrivateNetDataDeal
    {
        protected List<PrivateNetWeakCoverInfo> ZTWeakCovRoadInfoList;

        public PrivateNetWeakOverDataDeal(List<PrivateNetWeakCoverInfo> ZTWeakCovRoadInfoList)
        {
            this.ZTWeakCovRoadInfoList = ZTWeakCovRoadInfoList;
        }

        protected override double getDurationCondition()
        {
            return PrivateNetCondition.WeakCoverDuration;
        }

        protected override int getDataValueCondition()
        {
            return PrivateNetCondition.WeakCoverRSRP;
        }

        protected override float? getDataValue(TestPoint testPoint)
        {
            return (float?)testPoint["lte_RSRP"];
        }

        /// <summary>
        /// 判断弱覆盖的原因
        /// </summary>
        /// <param name="tpLists">连续弱覆盖采样点合集</param>
        protected override int judgeReasonType(List<TestPoint> tpLists)
        {
            //查询问题点小区800米范围内是否有专网小区
            foreach (LTECell lteCell in PrivateNetCondition.PrivateNetCell)
            {
                double distance = tpLists[0].Distance2(lteCell.Longitude, lteCell.Latitude);
                if (distance < PrivateNetCondition.CoverReasonDistance)
                {
                    //有专网小区为其他
                    return (int)PrivateNetWeakCoverInfo.ReasonList.Other;
                }
            }
            //无专网小区为缺站
            return (int)PrivateNetWeakCoverInfo.ReasonList.LackStation;
        }

        protected override void addResultData(PrivateNetInfo info)
        {
            ZTWeakCovRoadInfoList.Add((PrivateNetWeakCoverInfo)info);
        }
    }

    public class PrivateNetWeakSinrDataDeal : PrivateNetDataDeal
    {
        protected List<PrivateNetWeakSinrInfo> ZTWeakSINRInfoList;

        public PrivateNetWeakSinrDataDeal(List<PrivateNetWeakSinrInfo> ZTWeakSINRInfoList)
        {
            this.ZTWeakSINRInfoList = ZTWeakSINRInfoList;
        }

        protected override double getDurationCondition()
        {
            return PrivateNetCondition.WeakSINRDuration;
        }

        protected override int getDataValueCondition()
        {
            return PrivateNetCondition.WeakSINR;
        }

        protected override float? getDataValue(TestPoint testPoint)
        {
            return (float?)testPoint["lte_SINR"];
        }

        /// <summary>
        /// 判断质差的原因
        /// </summary>
        /// <param name="tpLists">连续质差采样点合集</param>
        protected override int judgeReasonType(List<TestPoint> tpLists)
        {
            if (judgeSINRSubWeakCover(tpLists))
            {
                //弱覆盖
                return (int)PrivateNetWeakSinrInfo.ReasonList.CoveringProblem;
            }

            if (judgeSINRSubOverCover(tpLists[0]))
            {
                //过覆盖
                return (int)PrivateNetWeakSinrInfo.ReasonList.CoveringProblem;
            }

            if (judgeSINRSubMod3(tpLists[0]))
            {
                //模三干扰
                return (int)PrivateNetWeakSinrInfo.ReasonList.ScanMod3Index;
            }
            return (int)PrivateNetWeakSinrInfo.ReasonList.Other;
        }

        /// <summary>
        /// 判断连续质差的弱覆盖原因
        /// </summary>
        /// <param name="tpLists"></param>
        /// <returns></returns>
        private bool judgeSINRSubWeakCover(List<TestPoint> tpLists)
        {
            foreach (TestPoint tp in tpLists)
            {
                float? rsrp = (float?)tp["lte_RSRP"];
                //rsrp为空的采样点忽略(也就是当做弱覆盖采样点)
                if (rsrp != null && rsrp > PrivateNetCondition.SINRReasonRSRP)
                {
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 判断连续质差的过覆盖原因
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        private bool judgeSINRSubOverCover(TestPoint tp)
        {
            LTECell lteCell = tp.GetMainCell_LTE();
            if (lteCell != null && lteCell.Longitude != 0 && lteCell.Latitude != 0)
            {
                double distance = tp.Distance2(lteCell.Longitude, lteCell.Latitude);
                if (distance > PrivateNetCondition.SINRReasonDistance)
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 判断连续质差的模三干扰
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        private bool judgeSINRSubMod3(TestPoint tp)
        {
            LTECell lteCell = tp.GetMainCell_LTE();
            if (lteCell != null)
            {
                for (int i = 0; i < 10; i++)
                {
                    LTECell nbCell = tp.GetNBCell_LTE(i);
                    if (nbCell != null && lteCell.PCI % 3 == nbCell.PCI % 3)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        protected override void addResultData(PrivateNetInfo info)
        {
            ZTWeakSINRInfoList.Add((PrivateNetWeakSinrInfo)info);
        }
    }

    public class PrivateNetOutNetDataDeal : PrivateNetDataDeal
    {
        protected List<OutPrivateNetInfo> ZTOutPrivateNetInfoList;
        public PrivateNetOutNetDataDeal(List<OutPrivateNetInfo> ZTOutPrivateNetInfoList)
        {
            this.ZTOutPrivateNetInfoList = ZTOutPrivateNetInfoList;
        }

        /// <summary>
        /// 保存出网信息
        /// </summary>
        /// <param name="testPoints">文件中的所有采样点</param>
        /// <param name="outPrivateNetIndex">本次第一个出网采样点的Index</param>
        /// <param name="privateNetIndex">本次出网前第一个专网采样点的Index</param>
        /// <param name="outPrivateNetDuration">出网持续时长</param>
        /// <param name="lastOutPrivateNetTime">上个出网采样点的时间</param>
        /// <param name="tpOutPrivateNetLists">出网采样点合集</param>
        /// <param name="backTime">返回专网时间</param>
        public void SaveDataInfo(List<TestPoint> testPoints, ref int outPrivateNetIndex, ref int privateNetIndex,
            ref double outPrivateNetDuration, ref long lastOutPrivateNetTime, List<TestPoint> tpOutPrivateNetLists, string backTime)
        {
            //专网小区,判断弱覆盖和质差,如果之前出专网需记录返回专网的相关数据
            if (tpOutPrivateNetLists.Count > 0)
            {
                //之前有出网,则此时返回专网,保存此次出网数据
                OutPrivateNetInfo info = new OutPrivateNetInfo();
                info.LineName = LineName;
                info.FileName = FileName;
                info.StartTime = tpOutPrivateNetLists[0].DateTime.ToString();
                info.Longitude = tpOutPrivateNetLists[0].Longitude;
                info.Latitude = tpOutPrivateNetLists[0].Latitude;

                if (outPrivateNetIndex > 0)
                {
                    int? tac = (int?)(ushort?)testPoints[outPrivateNetIndex - 1]["lte_TAC"];
                    if (tac != null)
                    {
                        info.TAC = tac.ToString();
                    }
                    int? eci = (int?)testPoints[outPrivateNetIndex - 1]["lte_ECI"];
                    if (eci != null)
                    {
                        info.ECI = eci.ToString();
                    }
                }

                addTPDataList(tpOutPrivateNetLists, info);

                //对出网原因判断
                info.SetReason(judgeOutPrivateNetReason(testPoints, outPrivateNetIndex, privateNetIndex));

                info.BackTime = backTime;
                info.Duration = outPrivateNetDuration;

                ZTOutPrivateNetInfoList.Add(info);

                outPrivateNetIndex = -1;
                privateNetIndex = -1;
                outPrivateNetDuration = 0;
                lastOutPrivateNetTime = 0;
                tpOutPrivateNetLists.Clear();
            }
        }

        private void addTPDataList(List<TestPoint> tpOutPrivateNetLists, OutPrivateNetInfo info)
        {
            for (int j = 0; j < tpOutPrivateNetLists.Count; j++)
            {
                TestPoint tp = tpOutPrivateNetLists[j];
                int? eci;
                int? tac;

                getTacEci(tp, out eci, out tac);

                if (eci != null && !info.AfterECILists.Contains(eci.ToString()))
                {
                    info.AfterECILists.Add(eci.ToString());
                }
                if (tac != null && !info.AfterTACLists.Contains(tac.ToString()))
                {
                    info.AfterTACLists.Add(tac.ToString());
                }

                info.TPLists.Add(tp);

                setCityName(info, tp);
            }
        }

        private static void setCityName(OutPrivateNetInfo info, TestPoint tp)
        {
            if (string.IsNullOrEmpty(info.CityName))
            {
                foreach (ResvRegion region in ResvRegionList)
                {
                    if (region.GeoOp.CheckPointInRegion(tp.Longitude, tp.Latitude))
                    {
                        info.CityName = region.RegionName;
                        break;
                    }
                }
            }
        }

        private static void getTacEci(TestPoint tp, out int? eci, out int? tac)
        {
            if (tp is LTETestPointDetail)
            {
                tac = (int?)(ushort?)tp["lte_TAC"];
                eci = (int?)tp["lte_ECI"];
            }
            else if (tp is TDTestPointDetail)
            {
                tac = (int?)tp["TD_SCell_LAC"];
                eci = (int?)tp["TD_SCell_CI"];
            }
            else
            {
                tac = (int?)tp["LAC"];
                eci = (int?)tp["CI"];
            }
        }

        /// <summary>
        /// 判断出网原因
        /// </summary>
        /// <param name="testPoints">文件中的所有采样点</param>
        /// <param name="outPrivateNetIndex">本次第一个出网采样点的Index</param>
        /// <param name="privateNetIndex">本次出网前第一个专网采样点的Index</param>
        /// <returns>原因</returns>
        private int judgeOutPrivateNetReason(List<TestPoint> testPoints, int outPrivateNetIndex, int privateNetIndex)
        {
            if (outPrivateNetIndex == 0)
            {
                return (int)OutPrivateNetInfo.ReasonList.Other;
            }
            //如果一开始就出网
            if (privateNetIndex < 0)
            {
                privateNetIndex = 0;
            }

            bool continuousWeakCover = true;
            long weakCoverTime = 0;
            double weakCoverDuration = 0;
            bool continuousWeakSINR = true;
            long weakSINRTime = 0;
            double weakSINRDuration = 0;
            //倒序遍历上一次在专网内的所有采样点,看是否满足弱覆盖和质差
            for (int i = outPrivateNetIndex - 1; i >= privateNetIndex; i--)
            {
                TestPoint testPoint = testPoints[i];
                bool isWeakCover = judgeIsWeakCover(ref continuousWeakCover, ref weakCoverTime, ref weakCoverDuration, testPoint);
                if (isWeakCover)
                {
                    return (int)OutPrivateNetInfo.ReasonList.WeakCover;
                }

                bool isWeakSinr = judgeIsWeakCoSinr(ref continuousWeakSINR, ref weakSINRTime, ref weakSINRDuration, testPoint);
                if (isWeakSinr)
                {
                    return (int)OutPrivateNetInfo.ReasonList.WeakSINR;
                }

                if (!continuousWeakCover && !continuousWeakSINR)
                {
                    return (int)OutPrivateNetInfo.ReasonList.Other;
                }
            }

            return (int)OutPrivateNetInfo.ReasonList.Other;
        }

        private bool judgeIsWeakCoSinr(ref bool continuousWeakSINR, ref long weakSINRTime, ref double weakSINRDuration, TestPoint testPoint)
        {
            if (continuousWeakSINR)
            {
                float? sinr = (float?)testPoint["lte_SINR"];
                if (sinr != null)
                {
                    if (sinr <= PrivateNetCondition.PrivateNetWeakSINR)
                    {
                        if (weakSINRTime != 0)
                        {
                            double timeGap = Math.Abs(1.0 * (weakSINRTime - testPoint.lTimeWithMillsecond) / 1000);
                            weakSINRDuration += timeGap;
                        }
                        weakSINRTime = testPoint.lTimeWithMillsecond;
                    }
                    else
                    {
                        continuousWeakSINR = false;
                    }
                    if (weakSINRDuration >= PrivateNetCondition.PrivateNetWeakSINRDuration)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        private bool judgeIsWeakCover(ref bool continuousWeakCover, ref long weakCoverTime, ref double weakCoverDuration, TestPoint testPoint)
        {
            if (continuousWeakCover)
            {
                float? rsrp = (float?)testPoint["lte_RSRP"];
                if (rsrp != null)
                {
                    if (rsrp <= PrivateNetCondition.PrivateNetWeakCoverRSRP)
                    {
                        if (weakCoverTime != 0)
                        {
                            double timeGap = Math.Abs(1.0 * (weakCoverTime - testPoint.lTimeWithMillsecond) / 1000);
                            weakCoverDuration += timeGap;
                        }
                        weakCoverTime = testPoint.lTimeWithMillsecond;
                    }
                    else
                    {
                        continuousWeakCover = false;
                    }

                    if (weakCoverDuration >= PrivateNetCondition.PrivateNetWeakCoverDuration)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 判断是否为专网小区(如果出网,则还要添加出网信息到出网合集中)
        /// </summary>
        /// <param name="tac">当前采样点tac</param>
        /// <param name="eci">当前采样点eci</param>
        /// <param name="testPoint">当前需判断的采样点</param>
        /// <param name="outPrivateNetDuration">出网持续时长</param>
        /// <param name="lastOutPrivateNetTime">上个出网采样点时间</param>
        /// <param name="tpOutPrivateNetLists">出网采样点合集</param>
        /// <returns>true为专网小区</returns>
        public bool judgePrivateNetCell(int tac, int eci, TestPoint testPoint, ref double outPrivateNetDuration,
            ref long lastOutPrivateNetTime, List<TestPoint> tpOutPrivateNetLists)
        {
            foreach (LTECell privateNetCell in PrivateNetCondition.PrivateNetCell)
            {
                if (privateNetCell.TAC == tac && privateNetCell.ECI == eci)
                {
                    return true;
                }
            }
            addOutPrivateNetInfo(testPoint, ref outPrivateNetDuration, ref lastOutPrivateNetTime, tpOutPrivateNetLists);
            return false;
        }

        /// <summary>
        /// 添加出网信息(累加出网时间,添加采样点和对应主服小区)
        /// </summary>
        /// <param name="testPoint">当前出网采样点</param>
        /// <param name="outPrivateNetDuration">出网时长</param>
        /// <param name="lastOutPrivateNetTime">上个出网采样点时间</param>
        /// <param name="tpOutPrivateNetLists">出网采样点合集</param>
        private void addOutPrivateNetInfo(TestPoint testPoint, ref double outPrivateNetDuration, ref long lastOutPrivateNetTime,
            List<TestPoint> tpOutPrivateNetLists)
        {
            //累加持续时间
            if (lastOutPrivateNetTime != 0)
            {
                double timeGap = Math.Abs(1.0 * (lastOutPrivateNetTime - testPoint.lTimeWithMillsecond) / 1000);
                outPrivateNetDuration += timeGap;
            }
            //上个出网点时间赋值
            lastOutPrivateNetTime = testPoint.lTimeWithMillsecond;
            //添加出网采样点
            tpOutPrivateNetLists.Add(testPoint);
        }
    }

    #region 结果合集类
    public class PrivateNetInfo
    {
        public PrivateNetInfo()
        {
            Duration = 0;
            ECILists = new List<string>();
            TPLists = new List<TestPoint>();
        }

        /// <summary>
        /// 线路名称
        /// </summary>
        public string LineName { get; set; }
        /// <summary>
        /// 省份
        /// </summary>
        public string ProvinceName
        {
            get { return "河南"; }
        }
        /// <summary>
        /// 地市
        /// </summary>
        public string CityName { get; set; }
        /// <summary>
        /// 事件类型
        /// </summary>
        public virtual string EventType
        {
            get { return ""; }
        }
        /// <summary>
        /// 开始时间
        /// </summary>
        public string StartTime { get; set; }
        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }
        /// <summary>
        /// 经度
        /// </summary>
        public double Longitude { get; set; }
        /// <summary>
        /// 纬度
        /// </summary>
        public double Latitude { get; set; }
        public List<string> ECILists { get; set; }
        /// <summary>
        /// 连续弱覆盖事件的主服务小区的ECI
        /// </summary>
        public string ECI
        {
            get
            {
                StringBuilder eciResult = new StringBuilder();
                foreach (string eci in ECILists)
                {
                    eciResult.Append(eci);
                    eciResult.Append("|");
                }
                return eciResult.ToString().TrimEnd('|');
            }
        }

        protected int reason = 0;
        public virtual string Reason
        {
            get
            {
                switch (reason)
                {
                    default:
                        return "其他";
                }
            }
        }

        public void SetReason(int reason)
        {
            this.reason = reason;
        }

        public List<TestPoint> TPLists { get; set; }
        public double Duration { get; set; }
    }

    public class PrivateNetWeakCoverInfo : PrivateNetInfo
    {
        /// <summary>
        /// 事件类型
        /// </summary>
        public override string EventType
        {
            get { return "连续弱覆盖"; }
        }

        public override string Reason
        {
            get
            {
                switch (reason)
                {
                    case 1:
                        return "缺站";
                    case 0:
                    default:
                        return "其他";
                }
            }
        }

        public enum ReasonList
        {
            /// <summary>
            /// 其他
            /// </summary>
            Other = 0,
            /// <summary>
            /// 缺站
            /// 发生问题点的周围800米无专网小区则输出缺站
            /// </summary>
            LackStation = 1,
        };
    }

    public class PrivateNetWeakSinrInfo : PrivateNetInfo
    {
        public override string EventType
        {
            get { return "连续质差"; }
        }

        public override string Reason
        {
            get
            {
                switch (reason)
                {
                    case 1:
                        return "覆盖问题";
                    case 2:
                        return "模三干扰";
                    case 0:
                    default:
                        return "其他";
                }
            }
        }

        public enum ReasonList
        {
            /// <summary>
            /// 其他
            /// </summary>
            Other = 0,
            /// <summary>
            /// 覆盖问题（包含弱覆盖&过覆盖）
            /// 弱覆盖就是接收RSRP低于-105dBm,过覆盖指问题点与小区距离超过800米
            /// </summary>
            CoveringProblem = 1,
            /// <summary>
            /// 模三干扰
            /// 模三干扰就是与邻区中有模三干扰
            /// </summary>
            ScanMod3Index = 2
        };
    }

    public class OutPrivateNetInfo : PrivateNetInfo
    {
        public OutPrivateNetInfo()
        {
            ECI = "";
            TAC = "";
            AfterECILists = new List<string>();
            AfterTACLists = new List<string>();
            BackTime = "";
        }

        /// <summary>
        /// 出网前ECI
        /// </summary>
        public new string ECI { get; set; }

        /// <summary>
        /// 出网前TAC
        /// </summary>
        public string TAC { get; set; }

        public List<string> AfterECILists { get; set; }
        /// <summary>
        /// 出网后ECI
        /// </summary>
        public string AfterECI
        {
            get
            {
                StringBuilder eciResult = new StringBuilder();
                foreach (string eci in AfterECILists)
                {
                    eciResult.Append(eci);
                    eciResult.Append("|");
                }
                return eciResult.ToString().TrimEnd('|');
            }
        }

        public List<string> AfterTACLists { get; set; }
        /// <summary>
        /// 出网后TAC
        /// </summary>
        public string AfterTAC
        {
            get
            {
                StringBuilder tacResult = new StringBuilder();
                foreach (string tac in AfterTACLists)
                {
                    tacResult.Append(tac);
                    tacResult.Append("|");
                }
                return tacResult.ToString().TrimEnd('|');
            }
        }

        /// <summary>
        /// 返回专网的时间
        /// </summary>
        public string BackTime { get; set; }

        public override string Reason
        {
            get
            {
                switch (reason)
                {
                    case 1:
                        return "弱覆盖";
                    case 2:
                        return "质差";
                    case 0:
                    default:
                        return "其他";
                }
            }
        }

        public enum ReasonList
        {
            /// <summary>
            /// 其他
            /// </summary>
            Other = 0,
            /// <summary>
            /// 弱覆盖
            /// </summary>
            WeakCover = 1,
            /// <summary>
            /// 质差
            /// </summary>
            WeakSINR = 2
        };
    }
    #endregion

}
