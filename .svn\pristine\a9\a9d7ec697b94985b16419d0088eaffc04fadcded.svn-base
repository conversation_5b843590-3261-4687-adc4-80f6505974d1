﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public class CreateBriefDataGridFormEvent : CreateChildForm
    {
        public CreateBriefDataGridFormEvent(MainModel mm)
            : base(mm)
        { 
        }
        public override string Description
        {
            get
            {
                return "创建事件信令查看窗口 BriefDataGridFormEvent ";
            }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 20000, 20018, this.Name);
        }
        public override string Name
        {
            get
            {
                return "创建事件窗口";
            }
        }

        protected override void initAction()
        {
            Dictionary<string, object> actionParam = new Dictionary<string, object>();
            actionParam["MainForm"] = MainModel.MainForm;
            actionParam["AssemblyName"] = "RAMS.exe";
            actionParam["TypeName"] = "MasterCom.RAMS.Func.BriefDataGridFormEvent";
            actionParam["Text"] = "事件列表";
            actionParam["ImageFilePath"] = @"images\frame_info.gif";
            action = new ActionCreateChildFrame();
            action.Param = actionParam;
        }
    }
}
