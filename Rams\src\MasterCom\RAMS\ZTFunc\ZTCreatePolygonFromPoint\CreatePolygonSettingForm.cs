﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CreatePolygonSettingForm : BaseForm
    {
        public string ExcelFile
        {
            get;
            private set;
        }

        public string ShpFile
        {
            get;
            private set;
        }

        public CreatePolygonSettingForm()
        {
            InitializeComponent();
            btnCancel.Click += BtnCancel_Click;
            btnOK.Click += BtnOK_Click;
            btnExcel.Click += BtnExcel_Click;
            btnShp.Click += BtnShp_Click;
            txtExcel.Text = "";
            txtShp.Text = "";
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (txtExcel.Text == "" || txtShp.Text == "")
            {
                DialogResult = DialogResult.None;
            }
            else
            {
                ExcelFile = txtExcel.Text;
                ShpFile = txtShp.Text;
                DialogResult = DialogResult.OK;
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void BtnExcel_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = FilterHelper.Excel;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            txtExcel.Text = dlg.FileName;
        }

        private void BtnShp_Click(object sender, EventArgs e)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.Filter = FilterHelper.Shp;
            dlg.InitialDirectory = Application.StartupPath + @"\userdata";
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            txtShp.Text = dlg.FileName;
        }
    }
}
