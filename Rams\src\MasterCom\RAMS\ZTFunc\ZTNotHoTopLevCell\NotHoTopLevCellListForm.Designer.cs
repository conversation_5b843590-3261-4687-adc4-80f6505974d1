﻿namespace MasterCom.RAMS.ZTFunc.ZTNotHoTopLevCell
{
    partial class NotHoTopLevCellListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.gridCtrl = new DevExpress.XtraGrid.GridControl();
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.gv = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colFileName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colOrgCell = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colOrgLac = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colOrgCi = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colTarCell = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colTarLac = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colTarCi = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colTarDistance = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colTarRxLev = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colTopCell = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colTopLac = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colTopCi = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colTopDistance = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colTopRxLev = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrl)).BeginInit();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gv)).BeginInit();
            this.SuspendLayout();
            // 
            // gridCtrl
            // 
            this.gridCtrl.ContextMenuStrip = this.ctxMenu;
            this.gridCtrl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridCtrl.Location = new System.Drawing.Point(0, 0);
            this.gridCtrl.MainView = this.gv;
            this.gridCtrl.Name = "gridCtrl";
            this.gridCtrl.ShowOnlyPredefinedDetails = true;
            this.gridCtrl.Size = new System.Drawing.Size(990, 656);
            this.gridCtrl.TabIndex = 0;
            this.gridCtrl.UseEmbeddedNavigator = true;
            this.gridCtrl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gv});
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExport2Xls});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(139, 26);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(138, 22);
            this.miExport2Xls.Text = "导出Excel...";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // gv
            // 
            this.gv.ColumnPanelRowHeight = 50;
            this.gv.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colFileName,
            this.colOrgCell,
            this.colOrgLac,
            this.colOrgCi,
            this.colTarCell,
            this.colTarLac,
            this.colTarCi,
            this.colTarDistance,
            this.colTarRxLev,
            this.colTopCell,
            this.colTopLac,
            this.colTopCi,
            this.colTopDistance,
            this.colTopRxLev});
            this.gv.GridControl = this.gridCtrl;
            this.gv.Name = "gv";
            this.gv.OptionsBehavior.Editable = false;
            this.gv.OptionsView.ColumnAutoWidth = false;
            this.gv.OptionsView.EnableAppearanceEvenRow = true;
            this.gv.OptionsView.ShowDetailButtons = false;
            this.gv.OptionsView.ShowGroupPanel = false;
            this.gv.DoubleClick += new System.EventHandler(this.gv_DoubleClick);
            // 
            // colFileName
            // 
            this.colFileName.Caption = "文件";
            this.colFileName.FieldName = "FileName";
            this.colFileName.Name = "colFileName";
            this.colFileName.Visible = true;
            this.colFileName.VisibleIndex = 0;
            // 
            // colOrgCell
            // 
            this.colOrgCell.AppearanceHeader.BackColor = System.Drawing.Color.Lime;
            this.colOrgCell.AppearanceHeader.BackColor2 = System.Drawing.Color.Lime;
            this.colOrgCell.AppearanceHeader.Options.UseBackColor = true;
            this.colOrgCell.Caption = "源小区";
            this.colOrgCell.FieldName = "OrgCellName";
            this.colOrgCell.Name = "colOrgCell";
            this.colOrgCell.Visible = true;
            this.colOrgCell.VisibleIndex = 1;
            // 
            // colOrgLac
            // 
            this.colOrgLac.AppearanceHeader.BackColor = System.Drawing.Color.Lime;
            this.colOrgLac.AppearanceHeader.Options.UseBackColor = true;
            this.colOrgLac.Caption = "源LAC";
            this.colOrgLac.FieldName = "OrgLac";
            this.colOrgLac.Name = "colOrgLac";
            this.colOrgLac.Visible = true;
            this.colOrgLac.VisibleIndex = 2;
            // 
            // colOrgCi
            // 
            this.colOrgCi.AppearanceHeader.BackColor = System.Drawing.Color.Lime;
            this.colOrgCi.AppearanceHeader.BackColor2 = System.Drawing.Color.Lime;
            this.colOrgCi.AppearanceHeader.Options.UseBackColor = true;
            this.colOrgCi.Caption = "源CI";
            this.colOrgCi.FieldName = "OrgCi";
            this.colOrgCi.Name = "colOrgCi";
            this.colOrgCi.Visible = true;
            this.colOrgCi.VisibleIndex = 3;
            // 
            // colTarCell
            // 
            this.colTarCell.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.colTarCell.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.colTarCell.AppearanceHeader.Options.UseBackColor = true;
            this.colTarCell.Caption = "目标小区";
            this.colTarCell.FieldName = "TarCellName";
            this.colTarCell.Name = "colTarCell";
            this.colTarCell.Visible = true;
            this.colTarCell.VisibleIndex = 4;
            // 
            // colTarLac
            // 
            this.colTarLac.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.colTarLac.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.colTarLac.AppearanceHeader.Options.UseBackColor = true;
            this.colTarLac.Caption = "目标LAC";
            this.colTarLac.FieldName = "TarLac";
            this.colTarLac.Name = "colTarLac";
            this.colTarLac.Visible = true;
            this.colTarLac.VisibleIndex = 5;
            // 
            // colTarCi
            // 
            this.colTarCi.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.colTarCi.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.colTarCi.AppearanceHeader.Options.UseBackColor = true;
            this.colTarCi.Caption = "目标CI";
            this.colTarCi.FieldName = "TarCi";
            this.colTarCi.Name = "colTarCi";
            this.colTarCi.Visible = true;
            this.colTarCi.VisibleIndex = 6;
            // 
            // colTarDistance
            // 
            this.colTarDistance.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.colTarDistance.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.colTarDistance.AppearanceHeader.Options.UseBackColor = true;
            this.colTarDistance.AppearanceHeader.Options.UseTextOptions = true;
            this.colTarDistance.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.colTarDistance.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.colTarDistance.Caption = "目标小区与源小区距离(m)";
            this.colTarDistance.FieldName = "TarDistance";
            this.colTarDistance.Name = "colTarDistance";
            this.colTarDistance.Visible = true;
            this.colTarDistance.VisibleIndex = 7;
            // 
            // colTarRxLev
            // 
            this.colTarRxLev.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.colTarRxLev.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.colTarRxLev.AppearanceHeader.Options.UseBackColor = true;
            this.colTarRxLev.AppearanceHeader.Options.UseTextOptions = true;
            this.colTarRxLev.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.colTarRxLev.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.colTarRxLev.Caption = "目标小区场强(dBm)";
            this.colTarRxLev.FieldName = "TarLev";
            this.colTarRxLev.Name = "colTarRxLev";
            this.colTarRxLev.Visible = true;
            this.colTarRxLev.VisibleIndex = 8;
            // 
            // colTopCell
            // 
            this.colTopCell.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.colTopCell.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.colTopCell.AppearanceHeader.Options.UseBackColor = true;
            this.colTopCell.Caption = "最优小区";
            this.colTopCell.FieldName = "TopCellName";
            this.colTopCell.Name = "colTopCell";
            this.colTopCell.Visible = true;
            this.colTopCell.VisibleIndex = 9;
            // 
            // colTopLac
            // 
            this.colTopLac.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.colTopLac.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.colTopLac.AppearanceHeader.Options.UseBackColor = true;
            this.colTopLac.Caption = "最优LAC";
            this.colTopLac.FieldName = "TopLac";
            this.colTopLac.Name = "colTopLac";
            this.colTopLac.Visible = true;
            this.colTopLac.VisibleIndex = 10;
            // 
            // colTopCi
            // 
            this.colTopCi.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.colTopCi.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.colTopCi.AppearanceHeader.Options.UseBackColor = true;
            this.colTopCi.Caption = "最优CI";
            this.colTopCi.FieldName = "TopCi";
            this.colTopCi.Name = "colTopCi";
            this.colTopCi.Visible = true;
            this.colTopCi.VisibleIndex = 11;
            // 
            // colTopDistance
            // 
            this.colTopDistance.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.colTopDistance.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.colTopDistance.AppearanceHeader.Options.UseBackColor = true;
            this.colTopDistance.AppearanceHeader.Options.UseTextOptions = true;
            this.colTopDistance.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.colTopDistance.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.colTopDistance.Caption = "最优小区与主服小区距离(m)";
            this.colTopDistance.FieldName = "TopDistance";
            this.colTopDistance.Name = "colTopDistance";
            this.colTopDistance.Visible = true;
            this.colTopDistance.VisibleIndex = 12;
            // 
            // colTopRxLev
            // 
            this.colTopRxLev.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.colTopRxLev.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.colTopRxLev.AppearanceHeader.Options.UseBackColor = true;
            this.colTopRxLev.AppearanceHeader.Options.UseTextOptions = true;
            this.colTopRxLev.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.colTopRxLev.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.colTopRxLev.Caption = "最优小区场强(dBm)";
            this.colTopRxLev.FieldName = "TopLev";
            this.colTopRxLev.Name = "colTopRxLev";
            this.colTopRxLev.Visible = true;
            this.colTopRxLev.VisibleIndex = 13;
            // 
            // NotHoTopLevCellListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(990, 656);
            this.Controls.Add(this.gridCtrl);
            this.Name = "NotHoTopLevCellListForm";
            this.Text = "未切换到最优小区列表";
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrl)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gv)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridCtrl;
        private DevExpress.XtraGrid.Views.Grid.GridView gv;
        private DevExpress.XtraGrid.Columns.GridColumn colFileName;
        private DevExpress.XtraGrid.Columns.GridColumn colOrgCell;
        private DevExpress.XtraGrid.Columns.GridColumn colOrgLac;
        private DevExpress.XtraGrid.Columns.GridColumn colOrgCi;
        private DevExpress.XtraGrid.Columns.GridColumn colTarCell;
        private DevExpress.XtraGrid.Columns.GridColumn colTarLac;
        private DevExpress.XtraGrid.Columns.GridColumn colTarCi;
        private DevExpress.XtraGrid.Columns.GridColumn colTarDistance;
        private DevExpress.XtraGrid.Columns.GridColumn colTarRxLev;
        private DevExpress.XtraGrid.Columns.GridColumn colTopCell;
        private DevExpress.XtraGrid.Columns.GridColumn colTopLac;
        private DevExpress.XtraGrid.Columns.GridColumn colTopCi;
        private DevExpress.XtraGrid.Columns.GridColumn colTopDistance;
        private DevExpress.XtraGrid.Columns.GridColumn colTopRxLev;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
    }
}