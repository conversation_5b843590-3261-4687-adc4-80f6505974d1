﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTReportEventInfo
    {
        public int SN { get; set; }
        public int FileID { get; set; }
        public int ProjID { get; set; }
        public int SeqID { get; set; }
        public int TimeSec { get; set; }
        public int TimeMs { get; set; }
        public int Bms { get; set; }
        public EventInfo EventInfo { get; set; }
        public int Longitude { get; set; }
        public int Latitude { get; set; }
        public int CqtID { get; set; }
        public int LAC { get; set; }
        public int RAC { get; set; }
        public int CI { get; set; }
        public int TargetLAC { get; set; }
        public int TargetRAC { get; set; }
        public int TargetCI { get; set; }
        public int AnaTime { get; set; }
        public string PreTypeDesc { get; set; }
        public string ReasonDesc { get; set; }
        public int ImportTime { get; set; }
        public string CellName { get; set; }
        public string AreaName { get; set; }
        public string RoadName { get; set; }
        public string FileName { get; set; }
        public string SavePath { get; set; }     
        //处理方法
        public string Method { get; set; }
        //处理方案简述
        public string Solution { get; set; }
        //处理人
        public string DwUserName { get; set; }
        //处理时间
        public int DwTime { get; set; }
        //审核结果
        public string AuditResult { get; set; }
        //审核人
        public string Auditor { get; set; }
        //审核时间
        public int TimeAudit { get; set; }
        //当前状态
        public int Status { get; set; }
        //备注
        public string Remark { get; set; }
        //效果评估
        public string OpResult { get; set; }
        public int ServiceID { get; set; }
        public string SampleTbName { get; set; }

        public ZTReportEventInfo()
        {

        }

        public void Fill(MasterCom.RAMS.Net.Content content)
        {
            FileID = content.GetParamInt();
            ProjID = content.GetParamInt();
            SeqID = content.GetParamInt();
            TimeSec = content.GetParamInt();
            TimeMs = content.GetParamInt();
            Bms = content.GetParamInt();
            EventInfo = MasterCom.RAMS.Model.EventInfoManager.GetInstance()[content.GetParamInt()];//eventId
            Longitude = content.GetParamInt();
            Latitude = content.GetParamInt();
            CqtID = content.GetParamInt();
            LAC = content.GetParamInt();
            RAC = content.GetParamInt();
            CI = content.GetParamInt();
            TargetLAC = content.GetParamInt();
            TargetRAC = content.GetParamInt();
            TargetCI = content.GetParamInt();
            AnaTime = content.GetParamInt();
            PreTypeDesc = content.GetParamString();
            ReasonDesc = content.GetParamString();
            ImportTime = content.GetParamInt();
            CellName = content.GetParamString();
            AreaName = content.GetParamString();
            RoadName = content.GetParamString();
            FileName = content.GetParamString();
            SavePath = content.GetParamString();
            Method = content.GetParamString();
            Solution = content.GetParamString();
            DwUserName = content.GetParamString();
            DwTime = content.GetParamInt();
            AuditResult = content.GetParamString();
            Auditor = content.GetParamString();
            TimeAudit = content.GetParamInt();
            Status = content.GetParamInt();
            Remark = content.GetParamString();
            OpResult = content.GetParamString();
            ServiceID = content.GetParamInt();
            SampleTbName = content.GetParamString();            
        }
    }
}
