﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCsfbCellJudgeByRegionNew : ZTCsfbCellJudgeBaseNew
    {

        public ZTCsfbCellJudgeByRegionNew(MainModel mainModel)
            : base(mainModel)
        {

        }

        public override string Name
        {
            get
            {
                return "CSFB回落非最佳判断(按区域)";
            }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22093, this.Name);
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }

    public class ZTCsfbCellJudgeByRegionNew_FDD : ZTCsfbCellJudgeNew_Fdd
    {
        public ZTCsfbCellJudgeByRegionNew_FDD(MainModel mainModel)
            : base(mainModel)
        {
            carrierID = CarrierType.ChinaUnicom;
        }
        public override string Name
        {
            get
            {
                return "LTE_FDD CSFB回落非最佳判断(按区域)";
            }
        }
        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            if (testPoint is LTEFddTestPoint)
            {
                return true;
            }
            return false;
        }
    }
}
