﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRWeakMosReasonHelper : WeakMosReasonHelperBase
    {
        public NRWeakMosReasonHelper(MainModel mainModel, List<ServiceType> serviceTypes)
            : base(mainModel, serviceTypes)
        {

        }

        public List<int> CallBeginEvtIdList { get; } = new List<int>
        {
            (int)NREventManager.VoLTE_Audio_MO_Call_Established,
            (int)NREventManager.VoLTE_Audio_MT_Call_Established,
            (int)NREventManager.VoLTE_Video_MO_Call_Established,
            (int)NREventManager.VoLTE_Video_MT_Call_Established,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MO_Call_Established,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MT_Call_Established,
            (int)NREventManager.VoLTE_eSRVCC_Video_MO_Call_Established,
            (int)NREventManager.VoLTE_eSRVCC_Video_MT_Call_Established,

            (int)NREventManager.EPSFB_Audio_MO_Call_Established,
            (int)NREventManager.EPSFB_Audio_MT_Call_Established,
            (int)NREventManager.EPSFB_Video_MO_Call_Established,
            (int)NREventManager.EPSFB_Video_MT_Call_Established,
            (int)NREventManager.EPSFB_eSRVCC_Audio_MO_Call_Established,
            (int)NREventManager.EPSFB_eSRVCC_Audio_MT_Call_Established,
            (int)NREventManager.EPSFB_eSRVCC_Video_MO_Call_Established,
            (int)NREventManager.EPSFB_eSRVCC_Video_MT_Call_Established,

            (int)NREventManager.CSFB_MO_Call_Established,
            (int)NREventManager.CSFB_MT_Call_Established,
            (int)NREventManager.GSM_MO_Call_Established,
            (int)NREventManager.GSM_MT_Call_Established,
        };

        public List<int> CallEndEvtIdList { get; } = new List<int>
        {
            (int)NREventManager.VoLTE_eSRVCC_Audio_MO_Call_End,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MT_Call_End,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MO_Drop_Call,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MT_Drop_Call,
            (int)NREventManager.VoLTE_eSRVCC_Video_MO_Call_End,
            (int)NREventManager.VoLTE_eSRVCC_Video_MT_Call_End,
            (int)NREventManager.VoLTE_eSRVCC_Video_MO_Drop_Call,
            (int)NREventManager.VoLTE_eSRVCC_Video_MT_Drop_Call,
        };

        public int P2CHandoverSuccess { get; } = (int)NREventManager.VoLTE_eSRVCC_HandOver_Success;

        public List<int> CSFBEvtIdList { get; } = new List<int>
        {
            (int)NREventManager.CSFB_MO_Call_Established,
            (int)NREventManager.CSFB_MT_Call_Established,
            (int)NREventManager.GSM_MO_Call_Established,
            (int)NREventManager.GSM_MT_Call_Established,
        };

        public List<int> HandOverEvtIdList { get; } = NREventHelper.HandoverHelper.GetHandoverSuccessEvt(false);

        public List<int> ReestablishmentRequestMsg { get; } = new List<int>
        {
            (int)MessageManager.LTE_RRC_Connection_Reestablishment_Request,
            (int)MessageManager.NR_RRC_RRCReestablishmentRequest
        };

        public int? GetRtpPacketsLostNum(TestPoint tp)
        {
            return (int?)tp["NR_VONR_RTP_Packets_Lost_Num"];
        }

        public bool JudgeCsfb(int evtID)
        {
            if (CSFBEvtIdList.Contains(evtID))
            {
                return true;
            }
            return false;
        }

        public List<int> NRCallList { get; } = new List<int>()
        {
            (int)NREventManager.VoNR_Audio_MO_Call_Established,
            (int)NREventManager.VoNR_Audio_MT_Call_Established,
            (int)NREventManager.VoNR_Video_MO_Call_Established,
            (int)NREventManager.VoNR_Video_MT_Call_Established
        };

        public List<int> LTECallList { get; } = new List<int>()
        {
            (int)NREventManager.VoLTE_Audio_MO_Call_Established,
            (int)NREventManager.VoLTE_Audio_MT_Call_Established,
            (int)NREventManager.VoLTE_Video_MO_Call_Established,
            (int)NREventManager.VoLTE_Video_MT_Call_Established,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MO_Call_Established,
            (int)NREventManager.VoLTE_eSRVCC_Audio_MT_Call_Established,
            (int)NREventManager.VoLTE_eSRVCC_Video_MO_Call_Established,
            (int)NREventManager.VoLTE_eSRVCC_Video_MT_Call_Established,

            (int)NREventManager.EPSFB_Audio_MO_Call_Established,
            (int)NREventManager.EPSFB_Audio_MT_Call_Established,
            (int)NREventManager.EPSFB_Video_MO_Call_Established,
            (int)NREventManager.EPSFB_Video_MT_Call_Established,
            (int)NREventManager.EPSFB_eSRVCC_Audio_MO_Call_Established,
            (int)NREventManager.EPSFB_eSRVCC_Audio_MT_Call_Established,
            (int)NREventManager.EPSFB_eSRVCC_Video_MO_Call_Established,
            (int)NREventManager.EPSFB_eSRVCC_Video_MT_Call_Established,
            //暂把CSFB算作LTE
            (int)NREventManager.CSFB_MO_Call_Established,
            (int)NREventManager.CSFB_MT_Call_Established,
        };

        public List<int> GSMCallList { get; } = new List<int>()
        {
            (int)NREventManager.GSM_MO_Call_Established,
            (int)NREventManager.GSM_MT_Call_Established,
        };

        public WeakMosReasonCallType JudgeCallType(int evtID)
        {
            if (NRCallList.Contains(evtID))
            {
                return WeakMosReasonCallType.NR;
            }
            else if (LTECallList.Contains(evtID))
            {
                return WeakMosReasonCallType.LTE;
            }
            else if (GSMCallList.Contains(evtID))
            {
                return WeakMosReasonCallType.GSM;
            }
            else
            {
                return WeakMosReasonCallType.Unknown;
            }
        }
    }
}
