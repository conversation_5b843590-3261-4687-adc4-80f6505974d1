﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.NOP
{
    public partial class KPIForm : MinCloseForm
    {
        public KPIForm()
            : base()
        {
            InitializeComponent();
        }

        public void FillData(DataTable dataTable)
        {
            gridCtrl.DataSource = dataTable;
            gv.PopulateColumns();
            for (int i = 0; i < gv.Columns.Count; i++)
            {
                gv.Columns[i].Caption = dataTable.Columns[i].ColumnName;
            }
            gridCtrl.RefreshDataSource();
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(gv);
        }

    }
}
