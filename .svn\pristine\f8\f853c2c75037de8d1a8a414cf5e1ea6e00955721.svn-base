﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CellImportForm : BaseDialog
    {
        public CellImportForm()
        {
            InitializeComponent();
        }

        public static CellImportForm GetDlg()
        {
            if (dlg==null)
            {
                dlg = new CellImportForm();
            }
            return dlg;
        }
        private static CellImportForm dlg = null;
        private string[] strGCName = { "NAME", "UARFCN", "PRIMARYSCRAMBLINGCODE", "LAC", "CID", "LONGITUDE", "LATITUDE", "DIR", "COVER_TYPE" };
        private int sameCount = 0;
        public void fillComboBoxs(List<string> heads)
        {
            this.cbxCellname.Items.Clear();
            this.cbxFreq.Items.Clear();
            this.cbxCpi.Items.Clear();
            this.cbxLAC.Items.Clear();
            this.cbxCI.Items.Clear();
            this.cbxLongitude.Items.Clear();
            this.cbxLatitude.Items.Clear();
            this.cbxDirection.Items.Clear();
            this.cbxIndoor.Items.Clear();

            this.cbxCellname.Items.Add("一一");
            this.cbxFreq.Items.Add("一一");
            this.cbxCpi.Items.Add("一一");
            this.cbxLAC.Items.Add("一一");
            this.cbxCI.Items.Add("一一");
            this.cbxLongitude.Items.Add("一一");
            this.cbxLatitude.Items.Add("一一");
            this.cbxDirection.Items.Add("一一");
            this.cbxIndoor.Items.Add("一一");

            sameCount = 0;
            foreach (string head in heads)
            {
                this.cbxCellname.Items.Add(head);
                this.cbxFreq.Items.Add(head);
                this.cbxCpi.Items.Add(head);
                this.cbxLAC.Items.Add(head);
                this.cbxCI.Items.Add(head);
                this.cbxLongitude.Items.Add(head);
                this.cbxLatitude.Items.Add(head);
                this.cbxDirection.Items.Add(head);
                this.cbxIndoor.Items.Add(head);

                for (int k = 0; k < strGCName.Length; k++)
                {
                    if (head == strGCName[k])
                        sameCount++;
                }
            }

            if (sameCount < 9)
            {
                this.cbxCellname.SelectedIndex = 0;
                this.cbxFreq.SelectedIndex = 0;
                this.cbxCpi.SelectedIndex = 0;
                this.cbxLAC.SelectedIndex = 0;
                this.cbxCI.SelectedIndex = 0;
                this.cbxLongitude.SelectedIndex = 0;
                this.cbxLatitude.SelectedIndex = 0;
                this.cbxDirection.SelectedIndex = 0;
                this.cbxIndoor.SelectedIndex = 0;
            }
            else
            {
                this.cbxCellname.SelectedItem = strGCName[0];
                this.cbxFreq.SelectedItem = strGCName[1];
                this.cbxCpi.SelectedItem = strGCName[2];
                this.cbxLAC.SelectedItem = strGCName[3];
                this.cbxCI.SelectedItem = strGCName[4];
                this.cbxLongitude.SelectedItem = strGCName[5];
                this.cbxLatitude.SelectedItem = strGCName[6];
                this.cbxDirection.SelectedItem = strGCName[7];
                this.cbxIndoor.SelectedItem = strGCName[8];
            }
        }

        public CellParamColumn cellParamColumn { get; set; }
        private void btnOK_Click(object sender, EventArgs e)
        {
            if (cbxCellname.SelectedItem.ToString() == "一一" || cbxFreq.SelectedItem.ToString() == "一一" || cbxCpi.SelectedItem.ToString() == "一一" || cbxLAC.SelectedItem.ToString() == "一一" || cbxCI.SelectedItem.ToString() == "一一"
                || cbxLongitude.SelectedItem.ToString() == "一一" || cbxLatitude.SelectedItem.ToString() == "一一" || cbxDirection.SelectedItem.ToString() == "一一" || cbxIndoor.SelectedItem.ToString() == "一一")
            {
                string msg = "";
                msg = getMsg(msg);
                XtraMessageBox.Show(msg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                cellParamColumn = new CellParamColumn();
                cellParamColumn.col_cellname = cbxCellname.SelectedItem.ToString();
                cellParamColumn.col_freq = cbxFreq.SelectedItem.ToString();
                cellParamColumn.col_cpi = cbxCpi.SelectedItem.ToString();
                cellParamColumn.col_Longitude = cbxLongitude.SelectedItem.ToString();
                cellParamColumn.col_latitude = cbxLatitude.SelectedItem.ToString();
                cellParamColumn.col_Lac = cbxLAC.SelectedItem.ToString();
                cellParamColumn.col_ci = cbxCI.SelectedItem.ToString();
                cellParamColumn.col_direction = cbxDirection.SelectedItem.ToString();
                cellParamColumn.col_indoor = cbxIndoor.SelectedItem.ToString();
                this.DialogResult = DialogResult.OK;
            }
        }

        private string getMsg(string msg)
        {
            if (cbxCellname.SelectedItem.ToString() == "一一")
            {
                msg += "小区名称";
            }
            if (cbxFreq.SelectedItem.ToString() == "一一")
            {
                msg += ",频点";
            }
            if (cbxCpi.SelectedItem.ToString() == "一一")
            {
                msg += ",扰码";
            }
            if (cbxLAC.SelectedItem.ToString() == "一一")
            {
                msg += ",LAC";
            }
            if (cbxCI.SelectedItem.ToString() == "一一")
            {
                msg += ",CI";
            }
            if (cbxLongitude.SelectedItem.ToString() == "一一")
            {
                msg += ",经度";
            }
            if (cbxLatitude.SelectedItem.ToString() == "一一")
            {
                msg += ",纬度";
            }
            if (cbxDirection.SelectedItem.ToString() == "一一")
            {
                msg += ",方向角";
            }
            if (cbxIndoor.SelectedItem.ToString() == "一一")
            {
                msg += ",是否室分";
            }
            msg += "未被选择指定列，请选择！";
            return msg;
        }
    }

    public class CellParamColumn
    {
        public string col_cellname{ get; set; }
        public string col_freq{ get; set; }
        public string col_cpi{ get; set; }
        public string col_Longitude{ get; set; }
        public string col_latitude{ get; set; }
        public string col_Lac{ get; set; }
        public string col_ci{ get; set; }
        public string col_direction{ get; set; }
        public string col_indoor{ get; set; }
    }
}
