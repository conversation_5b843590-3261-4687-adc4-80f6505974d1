﻿using System;
using System.Collections.Generic;
using System.Text;
using System.IO;
using System.Xml.Serialization;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 门限处理
    /// </summary>
    public class IndicatorThresholdInfProcessor
    {
        //文件路径名
        private readonly string tdTestFilePath = Directory.GetCurrentDirectory() + "\\userData\\td测试门限-西安.xml";
        private readonly string tdPerfFilePath = Directory.GetCurrentDirectory() + "\\userData\\td性能门限-西安.xml";
        private readonly string gsmTestFilePath = Directory.GetCurrentDirectory() + "\\userData\\gsm测试门限-西安.xml";
        private readonly string gsmPerfFilePath = Directory.GetCurrentDirectory() + "\\userData\\gsm性能门限-西安.xml";
        
        /// <summary>
        /// TD测试 四种类型的门限
        /// </summary>
        public TDTestThreadholdInf TDTestThreadholdInf { get; set; }
        /// <summary>
        /// TD性能
        /// </summary>
        public TDPerformanceThreadholdInf TDPerformanceThreadholdInf { get; set; }
        /// <summary>
        /// GSM测试
        /// </summary>
        public GSMTestThreadholdInf GSMTestThreadholdInf { get; set; }
        /// <summary>
        /// GSM性能
        /// </summary>
        public GSMPerformanceThreadholdInf GSMPerformanceThreadholdInf { get; set; }

        private static IndicatorThresholdInfProcessor processor;
        private IndicatorThresholdInfProcessor()
        {
            IniAllThreadholdInf();
        }
        private void IniAllThreadholdInf()
        {
            //1
            if (File.Exists(tdTestFilePath))
            {
                XmlSerializer serializer = new XmlSerializer(typeof(TDTestThreadholdInf));
                FileStream fileSteream = new FileStream(tdTestFilePath, FileMode.Open);
                TDTestThreadholdInf = (TDTestThreadholdInf)serializer.Deserialize(fileSteream);
                fileSteream.Close();
            }
            else
            {
                TDTestThreadholdInf = new TDTestThreadholdInf();
                XmlSerializer serializer = new XmlSerializer(typeof(TDTestThreadholdInf));
                FileStream fileSteream = new FileStream(tdTestFilePath, FileMode.Create);
                serializer.Serialize(fileSteream, TDTestThreadholdInf);
                fileSteream.Close();
            }

            //2
            if (File.Exists(gsmTestFilePath))
            {
                XmlSerializer serializer = new XmlSerializer(typeof(GSMTestThreadholdInf));
                FileStream fileSteream = new FileStream(gsmTestFilePath, FileMode.Open);
                GSMTestThreadholdInf = (GSMTestThreadholdInf)serializer.Deserialize(fileSteream);
                fileSteream.Close();
            }
            else
            {
                GSMTestThreadholdInf = new GSMTestThreadholdInf();
                XmlSerializer serializer = new XmlSerializer(typeof(GSMTestThreadholdInf));
                FileStream fileSteream = new FileStream(gsmTestFilePath, FileMode.Create);
                serializer.Serialize(fileSteream, GSMTestThreadholdInf);
                fileSteream.Close();
            }

            //3
            if (File.Exists(tdPerfFilePath))
            {
                XmlSerializer serializer = new XmlSerializer(typeof(TDPerformanceThreadholdInf));
                FileStream fileSteream = new FileStream(tdPerfFilePath, FileMode.Open);
                TDPerformanceThreadholdInf = (TDPerformanceThreadholdInf)serializer.Deserialize(fileSteream);
                fileSteream.Close();
            }
            else
            {
                TDPerformanceThreadholdInf = new TDPerformanceThreadholdInf();
                XmlSerializer serializer = new XmlSerializer(typeof(TDPerformanceThreadholdInf));
                FileStream fileSteream = new FileStream(tdPerfFilePath, FileMode.Create);
                serializer.Serialize(fileSteream, TDPerformanceThreadholdInf);
                fileSteream.Close();
            }

            //4
            if (File.Exists(gsmPerfFilePath))
            {
                XmlSerializer serializer = new XmlSerializer(typeof(GSMPerformanceThreadholdInf));
                FileStream fileSteream = new FileStream(gsmPerfFilePath, FileMode.Open);
                GSMPerformanceThreadholdInf = (GSMPerformanceThreadholdInf)serializer.Deserialize(fileSteream);
                fileSteream.Close();
            }
            else
            {
                GSMPerformanceThreadholdInf = new GSMPerformanceThreadholdInf();
                XmlSerializer serializer = new XmlSerializer(typeof(GSMPerformanceThreadholdInf));
                FileStream fileSteream = new FileStream(gsmPerfFilePath, FileMode.Create);
                serializer.Serialize(fileSteream, GSMPerformanceThreadholdInf);
                fileSteream.Close();
            }
        }

        public static IndicatorThresholdInfProcessor Ini()
        {
            if (processor == null)
            {
                processor = new IndicatorThresholdInfProcessor();
            }
            return processor;
        }
    }
}
