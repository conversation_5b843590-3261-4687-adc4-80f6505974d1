﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using System.Drawing;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYNoSignalCell_GScan : DIYSampleByRegion
    {
        private readonly Dictionary<ICell, NoSignalCell> noSignalCellDic = new Dictionary<ICell, NoSignalCell>();
        public ZTDIYNoSignalCell_GScan(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
        }

        public override string Name
        {
            get { return "无信号小区核查"; }
        }
        
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 15000, 15009, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_RxLev";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"GSM_SCAN_RxLev");
            tmpDic.Add("themeName", (object)"GSM_SCAN_RxLev");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override bool getConditionBeforeQuery()
        {
            noSignalCellDic.Clear();
            return ResetBTSToMop2Dict();
        }

        protected Dictionary<BTS, MapOperation2> btsToMop2 = new Dictionary<BTS, MapOperation2>();
        protected bool ResetBTSToMop2Dict()
        {
            btsToMop2.Clear();
            GSMBTSVoiCoverManager voiManager = GSMBTSVoiCoverManager.GetInstance();
            if (!AppendBTSToMop2Dict(voiManager))
            {
                return false;
            }
            voiManager.Clear();

            voiManager = DCSBTSVoiCoverManager.GetInstance();
            if (!AppendBTSToMop2Dict(voiManager))
            {
                return false;
            }
            voiManager.Clear();
            return true;
        }

        private bool AppendBTSToMop2Dict(GSMBTSVoiCoverManager voiManager)
        {
            voiManager.Condition = new VoiCoverCondition();
            switch (voiManager.Construct(false, true))
            {
                case VoiCoverResult.Cancel:
                    return false;
                case VoiCoverResult.Failed:
                    System.Windows.Forms.MessageBox.Show(voiManager.LastErrorText);
                    return false;
                default:
                    break;
            }

            Dictionary<BTS, List<Vertex[]>> btsToPoly = voiManager.BtsToPolygonDict;
            foreach (BTS bts in btsToPoly.Keys)
            {
                List<PointF[]> pfList = new List<PointF[]>();
                foreach (Vertex[] vs in btsToPoly[bts])
                {
                    PointF[] pf = new PointF[vs.Length];
                    for (int i = 0; i < vs.Length; ++i)
                    {
                        pf[i] = vs[i].ToPointF();
                    }
                    pfList.Add(pf);
                }
                MapOperation2 mop2 = new MapOperation2();
                mop2.FillPolygon(pfList);
                btsToMop2.Add(bts, mop2);
            }
            return true;
        }

        protected override void doWithDTData(TestPoint tp)
        {
            // 扫频点的理论覆盖基站
            BTS curBts = null;
            foreach (BTS bts in btsToMop2.Keys)
            {
                if (!btsToMop2[bts].CheckPointInRegion(tp.Longitude, tp.Latitude))
                {
                    continue;
                }
                curBts = bts;
                break;
            }
            if (curBts == null)
            {
                return;
            }

            // 扫频点的理论覆盖小区
            Cell curCell = null;
            foreach (Cell cell in curBts.Cells)
            {
                if (!MathFuncs.JudgePoint(cell.Longitude, cell.Latitude, tp.Longitude, tp.Latitude, (int)cell.Direction))
                {
                    continue;
                }
                curCell = cell;
                break;
            }
            if (curCell == null)
            {
                return;
            }

            // 扫频点是否扫出理论覆盖小区
            bool isCover = judgeIsCover(tp, curCell);

            if (isCover)
            {
                return;
            }
            if (!noSignalCellDic.ContainsKey(curCell))
            {
                NoSignalCell noSignalCell = new NoSignalCell(curCell);
                noSignalCell.XH = noSignalCellDic.Count + 1;
                noSignalCellDic[curCell] = noSignalCell;
            }
            noSignalCellDic[curCell].TestPointList.Add(tp);
        }

        private bool judgeIsCover(TestPoint tp, Cell curCell)
        {
            bool isCover = false;
            for (int i = 0; i < 50; ++i)
            {
                int? bcch = (int?)tp["GSCAN_BCCH", i];
                int? bsic = (int?)tp["GSCAN_BSIC", i];
                if (bcch == null && bsic == null)
                {
                    break;
                }
                if (bcch == curCell.BCCH && bsic == curCell.BSIC)
                {
                    isCover = true;
                    break;
                }
            }

            return isCover;
        }

        /**
        protected override void doWithDTData2(MasterCom.RAMS.Model.TestPoint tp)
        {
            foreach (BTSAnalogCoverGroup btsGroup in MainModel.BTSAnalogCoverGroupDic.Keys)
            {
                TrianglePointSets pointSets = MainModel.BTSAnalogCoverGroupDic[btsGroup];
                if (pointSets.ContainsPoint(tp.Longitude, tp.Latitude))
                {
                    List<Cell> curCells = new List<Cell>();
                    foreach (BTS bts in btsGroup.btsList)
                    {
                        foreach (Cell cell in bts.Cells)
                        {
                            Cell curCell = cell.CurrentSnapshot == null ? null : cell.CurrentSnapshot.Value;
                            if (curCell != null)
                            {
                                curCells.Add(curCell);
                            }
                        }
                    }
                    if (curCells.Count == 0)
                    {
                        continue;
                    }
                    bool bCover = false;
                    for (int i = 0; i < 50; i++)
                    {
                        if (bCover)
                        {
                            break;
                        }
                        int? bcch = (int?)tp["GSCAN_BCCH", i];
                        int? bsic = (int?)tp["GSCAN_BSIC", i];
                        if (bcch == null && bsic == null)
                        {
                            break;
                        }
                        foreach (Cell cell in curCells)
                        {
                            if (bcch == cell.BCCH && bsic == cell.BSIC)
                            {
                                bCover = true;
                                break;
                            }
                        }
                    }
                    if (!bCover)
                    {
                        foreach (Cell cell in curCells)
                        {
                            if (MathFuncs.JudgePoint(cell.Longitude, cell.Latitude, tp.Longitude, tp.Latitude, (int)cell.Direction))
                            {
                                if (!noSignalCellDic.ContainsKey(cell))
                                {
                                    NoSignalCell noSignalCell = new NoSignalCell(cell);
                                    noSignalCell.XH = noSignalCellDic.Count + 1;
                                    noSignalCellDic[cell] = noSignalCell;
                                }
                                noSignalCellDic[cell].TestPointList.Add(tp);
                            }
                        }
                    }
                }
            }
        }
         */

        protected override void FireShowFormAfterQuery()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(NoSignalCellForm).FullName);
            NoSignalCellForm noSignalCellForm = obj == null ? null : obj as NoSignalCellForm;
            if (noSignalCellForm == null || noSignalCellForm.IsDisposed)
            {
                noSignalCellForm = new NoSignalCellForm(MainModel);
            }
            noSignalCellForm.FillData(noSignalCellDic);
            if (!noSignalCellForm.Visible)
            {
                noSignalCellForm.Show(MainModel.MainForm);
            }
        }
    }

    public class NoSignalCell
    {
        public NoSignalCell(ICell cell)
        {
            this.cell = cell;
        }

        public int XH { get; set; }

        public ICell cell { get; set; }

        public double LongitudeCell
        {
            get
            {
                if (cell is Cell)
                {
                    return (cell as Cell).Latitude;
                }
                else if (cell is LTECell)
                {
                    return (cell as LTECell).Latitude;
                }
                else if (cell is TDCell)
                {
                    return (cell as TDCell).Latitude;
                }
                else
                {
                    return ((WCell)cell).Latitude;
                }
            }
        }
        public double LatitudeCell
        {
            get
            {
                if (cell is Cell)
                {
                    return (cell as Cell).Latitude;
                }
                else if (cell is LTECell)
                {
                    return (cell as LTECell).Latitude;
                }
                else if (cell is TDCell)
                {
                    return (cell as TDCell).Latitude;
                }
                else
                {
                    return ((WCell)cell).Latitude;
                }
            }
        }
        public string CellName
        {
            get
            {
                return cell.Name;
            }
        }
        public int LAC
        {
            get
            {
                if (cell is Cell)
                {
                    return (cell as Cell).LAC;
                }
                else if (cell is TDCell)
                {
                    return (cell as TDCell).LAC;
                }
                else if (cell is LTECell)
                {
                    return (cell as LTECell).TAC;
                }
                else
                {
                    return ((WCell)cell).LAC;
                }
            }
        }
        public int CI
        {
            get
            {
                if (cell is Cell)
                {
                    return (cell as Cell).CI;
                }
                else if (cell is TDCell)
                {
                    return (cell as TDCell).CI;
                }
                else if (cell is LTECell)
                {
                    return (cell as LTECell).ECI;
                }
                else
                {
                    return ((WCell)cell).CI;
                }
            }

        }
        public int BCCH
        {
            get
            {
                if (cell is Cell)
                {
                    return (cell as Cell).BCCH;
                }
                else if (cell is TDCell)
                {
                    return (cell as TDCell).FREQ;
                }
                else if (cell is LTECell)
                {
                    return (cell as LTECell).EARFCN;
                }
                else
                {
                    return ((WCell)cell).UARFCN;
                }
            }
        }
        public int BSIC
        {
            get
            {
                if (cell is Cell)
                {
                    return (cell as Cell).BSIC;
                }

                else if (cell is TDCell)
                {
                    return (cell as TDCell).CPI;
                }
                else if (cell is LTECell)
                {
                    return (cell as LTECell).PCI;
                }
                else
                {
                    return ((WCell)cell).PSC;
                }

            }
        }

        public int CellID
        {
            get
            {
                if (cell is LTECell)
                {
                    return (cell as LTECell).SCellID;
                }
                else
                {
                    return 0;
                }
            }
        }

        private readonly List<TestPoint> testPointList = new List<TestPoint>();
        public List<TestPoint> TestPointList
        {
            get { return testPointList; }
        }

        public int TestPointCount
        {
            get { return testPointList.Count; }
        }
    }
}
