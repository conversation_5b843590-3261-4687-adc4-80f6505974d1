﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// LTE扫频模干扰查询条件
    /// </summary>
    public class ScanLTEModRoadCondition : ModRoadConditionBase
    {
        public double DiffRxlev { get; set; }
    }

    /// <summary>
    /// LTE扫频模干扰道路
    /// </summary>
    public class ScanLTEModRoadExtent : ModRoadItemBase
    {
        public List<LTECell> Cells
        {
            get;
            private set;
        }

        public Dictionary<LTECell, List<LTECell>> ScanCellsDic
        {
            get;
            private set;
        }

        public int RelLevel
        {
            get;
            private set;
        }

        public int InvalidSampleCount
        {
            get;
            private set;
        }

        public ScanLTEModRoadExtent(TestPoint firstPoint, string fileName, ScanLTEModRoadCondition cond)
            : base(firstPoint, fileName, cond)
        {
            cellPairCountDic = new Dictionary<string, int>();
            idCellDic = new Dictionary<int, LTECell>();
        }

        public int GetScanCellPairTime(LTECell tarCell, LTECell srcCell)
        {
            string key = string.Format("{0}_{1}", tarCell.ID, srcCell.ID);
            return cellPairCountDic.ContainsKey(key) ? cellPairCountDic[key] : 0;
        }

        protected override void ProcessInfo()
        {
            foreach (TestPoint tp in TestPoints)
            {
                ParsePoint(tp);
            }
            Cells = new List<LTECell>(idCellDic.Values);
            RelLevel = TestPoints.Count - InvalidSampleCount == 0 ? 0 : relLevelSum / (TestPoints.Count - InvalidSampleCount);

            ScanCellsDic = new Dictionary<LTECell, List<LTECell>>();
            foreach (int tarId in idCellDic.Keys)
            {
                LTECell key = idCellDic[tarId];
                List<LTECell> value = new List<LTECell>();
                foreach (int srcId in idCellDic.Keys)
                {
                    if (tarId == srcId)
                    {
                        continue;
                    }

                    string tmpKey = string.Format("{0}_{1}", tarId, srcId);
                    if (!cellPairCountDic.ContainsKey(tmpKey))
                    {
                        continue;
                    }

                    value.Add(idCellDic[srcId]);
                }
                ScanCellsDic.Add(key, value);
            }
        }

        private void ParsePoint(TestPoint tp)
        {
            float? maxRxlev = null;
            LTECell maxCell = null;

            for (int i = 0; i < 50; ++i)
            {
                int? earfcn = (int?)tp["LTESCAN_TopN_EARFCN", i];
                short? pci = (short?)tp["LTESCAN_TopN_PCI", i];

                // 信号强度判断
                float? rxlev = (float?)tp["LTESCAN_TopN_CELL_Specific_RSRP", i];
                bool flag = rxlev == null || rxlev < cond.MaxRxlev;// 无效频点
                bool isValid = judgeValidSample(i, flag);
                if (!isValid)
                {
                    break;
                }

                // 相对覆盖度处理
                isValid = judgeValidRxlev(ref maxRxlev, rxlev);
                if (!isValid)
                {
                    break;
                }

                // 小区匹配
                LTECell cell = CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(tp.DateTime, earfcn, pci, tp.Longitude, tp.Latitude);
                flag = cell == null;
                isValid = judgeValidSample(i, flag);
                if (!isValid)
                {
                    break;
                }

                // 小区对出现情况收集
                maxCell = getMaxCellInfo(maxCell, cell);

                // 所有小区集合
                if (!idCellDic.ContainsKey(cell.ID))
                {
                    idCellDic.Add(cell.ID, cell);
                }
            }
        }

        private bool judgeValidSample(int i, bool flag)
        {
            if (flag)
            {
                if (i == 0)
                {
                    InvalidSampleCount += 1;
                }
                return false;
            }
            return true;
        }

        private bool judgeValidRxlev(ref float? maxRxlev, float? rxlev)
        {
            if (maxRxlev == null)                       // 第一强
            {
                maxRxlev = rxlev;
                relLevelSum += 1;                       // 相对覆盖度包括第一强
            }
            else if (maxRxlev - rxlev > (cond as ScanLTEModRoadCondition).DiffRxlev) // 不满足相对覆盖条件，后面小区不再考虑
            {
                return false;
            }
            else
            {
                relLevelSum += 1;
            }

            return true;
        }

        private LTECell getMaxCellInfo(LTECell maxCell, LTECell cell)
        {
            if (maxCell == null)
            {
                maxCell = cell;
            }
            else
            {
                string key = string.Format("{0}_{1}", maxCell.ID, cell.ID);
                if (!cellPairCountDic.ContainsKey(key))
                {
                    cellPairCountDic.Add(key, 0);
                }
                ++cellPairCountDic[key];
            }

            return maxCell;
        }

        private readonly Dictionary<string, int> cellPairCountDic;
        private readonly Dictionary<int, LTECell> idCellDic;
        private int relLevelSum;
    }

    /// <summary>
    /// LTE扫频模干扰结果分析基类
    /// 与ScanLTEModRoadExtent关系密切
    /// </summary>
    public class ScanLTEModRoadStater : ModRoadStaterBase
    {
        public ScanLTEModRoadStater(MainModel mm, ScanLTEModRoadCondition cond) : base(mm, cond)
        {
            this.lteCond = cond;
        }

        public override object GetStatResult(object param)
        {
            LTEModInterfereCond interCond = param as LTEModInterfereCond;
            List<ScanLTEModRoadInfo> retList = new List<ScanLTEModRoadInfo>();
            foreach (ModRoadItemBase road in this.roadList)
            {
                ScanLTEModRoadExtent exRoad = road as ScanLTEModRoadExtent;
                ScanLTEModRoadInfo roadInfo = new ScanLTEModRoadInfo();
                roadInfo.TestPoints = exRoad.TestPoints;
                roadInfo.FileName = exRoad.FileName;
                roadInfo.Length = exRoad.Length;
                roadInfo.RelLevel = exRoad.RelLevel;
                roadInfo.CellsCount = exRoad.Cells.Count;
                roadInfo.InvalidSampleCount = exRoad.InvalidSampleCount;
                roadInfo.RoadDesc = exRoad.RoadDesc;
                roadInfo.TotalSampleCount = exRoad.TestPoints.Count;
                roadInfo.ModCells = GetCellInfo(exRoad, interCond);
                roadInfo.ModCellsCount = roadInfo.ModCells.Count;
                if (roadInfo.ModCellsCount != 0)
                {
                    roadInfo.SN = retList.Count + 1;
                    retList.Add(roadInfo);
                }
            }
            return retList;
        }

        private List<ScanLTEModCellInfo> GetCellInfo(ScanLTEModRoadExtent exRoad, LTEModInterfereCond interCond)
        {
            List<ScanLTEModCellInfo> retList = new List<ScanLTEModCellInfo>();
            Dictionary<LTECell, List<LTECell>> scanCellDic = exRoad.ScanCellsDic;
            foreach (LTECell tarCell in scanCellDic.Keys)
            {
                List<LTEModInterfereCell> interCells = LTEModInterferer.Instance.Stat(tarCell, scanCellDic[tarCell], interCond);
                if (interCells.Count == 0)
                {
                    continue;
                }

                ScanLTEModCellInfo tarInfo = new ScanLTEModCellInfo(tarCell, interCond.ModX);
                tarInfo.SrcCells = new List<ScanLTEModCellInfo>();
                foreach (LTEModInterfereCell srcInterCell in interCells)
                {
                    ScanLTEModCellInfo srcInfo = new ScanLTEModCellInfo(srcInterCell.LteCell, interCond.ModX);
                    srcInfo.TarDistance = srcInterCell.Distance;
                    srcInfo.TarInterfereCount = exRoad.GetScanCellPairTime(tarCell, srcInterCell.LteCell);
                    tarInfo.SrcCells.Add(srcInfo);
                }
                tarInfo.SrcCellsCount = tarInfo.SrcCells.Count;
                retList.Add(tarInfo);
            }
            return retList;
        }

        private ScanLTEModRoadCondition lteCond { get; set; }
    }

    /// <summary>
    /// 结果窗口显示的道路信息
    /// </summary>
    public class ScanLTEModRoadInfo
    {
        public List<TestPoint> TestPoints { get; set; }
        public int SN
        {
            get;
            set;
        }
        public string FileName
        {
            get;
            set;
        }
        public string RoadDesc
        {
            get;
            set;
        }
        public double Length
        {
            get;
            set;
        }
        public int TotalSampleCount
        {
            get;
            set;
        }
        public int InvalidSampleCount
        {
            get;
            set;
        }
        public int CellsCount
        {
            get;
            set;
        }
        public int ModCellsCount
        {
            get;
            set;
        }
        public int RelLevel
        {
            get;
            set;
        }
        public List<ScanLTEModCellInfo> ModCells
        {
            get;
            set;
        }
    }

    /// <summary>
    /// 结果窗口显示的小区信息
    /// </summary>
    public class ScanLTEModCellInfo
    {
        public LTECell LteCell { get; set; }
        public string CellName
        {
            get;
            set;
        }
        public int CellID
        {
            get;
            set;
        }
        public double Longitude
        {
            get;
            set;
        }
        public double Latitude
        {
            get;
            set;
        }
        public int Direction
        {
            get;
            set;
        }
        public int EARFCN
        {
            get;
            set;
        }
        public int PCI
        {
            get;
            set;
        }
        public int SID
        {
            get;
            set;
        }

        public double TarDistance
        {
            get;
            set;
        }
        public int TarInterfereCount
        {
            get;
            set;
        }
        public List<ScanLTEModCellInfo> SrcCells
        {
            get;
            set;
        }
        public int SrcCellsCount
        {
            get;
            set;
        }

        public ScanLTEModCellInfo()
        {
        }
        public ScanLTEModCellInfo(LTECell cell, int modX)
        {
            LteCell = cell;
            CellName = cell.Name;
            CellID = cell.ID;
            Longitude = cell.Longitude;
            Latitude = cell.Latitude;
            Direction = cell.Direction;
            EARFCN = cell.EARFCN;
            PCI = cell.PCI;
            SID = cell.PCI % modX;
        }
    }
}
