﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.UserMng;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.ZTFunc;
using System.Drawing;
using DBDataViewer;

namespace MasterCom.RAMS.Net
{
    public class ZTGridCompareCombineBase : DIYGridQuery
    {
        public ZTGridCompareCombineBase(MainModel mainModel)
            : base(mainModel)
        {
            this.mainModel = mainModel;
        }
        public override string Name
        {
            get { return "特殊竞对分析(基类)"; }
        }
        public override string IconName
        {
            get { return null; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22010, 3416, this.Name);
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        protected override void AddGeographicFilter(Package package)
        {
            this.AddDIYRegion_Intersect(package);
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override bool isValidStatImg(double lng, double lat)
        {
            return true;
        }

        #region 全局变量
        public string strCarrName { get; set; } = "";
        public string strCityName { get; set; } = "";
        public string strProjectName { get; set; } = "";
        public string statImgIDSet { get; set; } = "";
        public Dictionary<string, Dictionary<string, MapOperation2>> mutRegionMopDic { get; set; }
        public Dictionary<CenterLongLat, List<string>> gridFileNameListDic { get; set; }
        public Dictionary<int, string> fileIDNameDic { get; set; }
        public Dictionary<GridTypeName,GridMatrix<GridColorUnit>> gridColorUnit { get; set; }
        public Dictionary<string, List<GridCompareCombineInfo>> cityGridCompareInfoDic { get; set; }
        public GridCountInfo gridCountInfo { get; set; }
        public bool isShowSetForm { get; set; } = false;
        public bool isShowGridDataInfo { get; set; } = false;
        public bool GetShowGridDataInfo { get; set; } = false;
        public bool isInsertGridInfo { get; set; } = false;
        public bool isAddInsetGridInfo { get; set; } = false;
        public bool isNeedSearchFileInfo { get; set; } = true;
        public double dPercent { get; set; } = 1.0;
        public double dSpeed { get; set; } = 25;
        public int iCurCityID { get; set; } = -1;
        public string strTDDDownTime { get; } = "(Lte_052164020102+Lte_05216402010202+Lte_05216402010302)/1000";
        public string strTDDDownSize { get; } = "(Lte_052164020101+Lte_05216402010201+Lte_05216402010301)*8/(1024*1024)";
        public string strTDDSampleNum { get; } = "(Lte_052164020103+Lte_05216402010203+Lte_05216402010303)";
        public string strFDDDownTime { get; } = "(Lf_052D64020102+Lf_052D6402010202+Lf_052D6402010302)/1000";
        public string strFDDDownSize { get; } = "(Lf_052D64020101+Lf_052D6402010201+Lf_052D6402010301)*8/(1024*1024)";
        public string strFDDSampleNum { get; } = "(Lf_052D64020103+Lf_052D6402010203+Lf_052D6402010303)";
        #endregion

        protected override void query()
        {
            //pareKeyList = new List<string>();//节省内存，需要重新赋值
            if (!getConditionBeforeQuery())
            {
                return;
            }

            if (isShowSetForm)
            {
                GridCompareCombieSetForm gridForm = new GridCompareCombieSetForm(isShowGridDataInfo, isInsertGridInfo, false, new List<string>());
                if (gridForm.ShowDialog() == DialogResult.OK)
                {
                    dPercent = 1.0 * gridForm.GetPercent / 100 ;
                    dSpeed = gridForm.GetSpeed;
                    GetShowGridDataInfo = gridForm.GetIsShowGridDataInfo;
                    isInsertGridInfo = gridForm.GetIsInsertGridDataInfoToDB;
                    isAddInsetGridInfo = gridForm.GetIsAddInsertGridDataInfo;
                }
                else
                {
                    return;
                }
            }

            strProjectName = "";
            StringBuilder sb = new StringBuilder();
            foreach (int iPro in condition.Projects)
            {
                sb.Append(ProjectManager.GetInstance().GetStrProjetName(iPro) + " ");
            }
            strProjectName = sb.ToString();
            gridFileNameListDic = new Dictionary<CenterLongLat, List<string>>();

            mutRegionMopDic = new Dictionary<string, Dictionary<string, MapOperation2>>();
            cityGridCompareInfoDic = new Dictionary<string, List<GridCompareCombineInfo>>();
            InitRegionMop2();
            foreach (int districtID in condition.DistrictIDs)
            {
                fileIDNameDic = new Dictionary<int, string>();
                if (condition != null && isNeedSearchFileInfo)
                {
                    DIYQueryFileInfo diyFilie = new DIYQueryFileInfo(MainModel);
                    diyFilie.IsShowFileInfoForm = false;
                    diyFilie.SetQueryCondition(condition);
                    diyFilie.Query();
                }
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                foreach (FileInfo file in MainModel.FileInfos)
                {
                    if (!fileIDNameDic.ContainsKey(file.ID))
                    {
                        fileIDNameDic[file.ID] = file.Name;
                    }
                }
                MainModel.FileInfos.Clear();
                strCityName = DistrictManager.GetInstance().getDistrictName(districtID);
                gridColorUnit = new Dictionary<GridTypeName,GridMatrix<GridColorUnit>>();
                iCurCityID = districtID;
                queryDistrictData(districtID);
                gridColorUnit = new Dictionary<GridTypeName, GridMatrix<GridColorUnit>>();
                fileIDNameDic = new Dictionary<int, string>();
            }
            afterRecieveAllData();
            fireShowResult();
        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;

                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                MainModel.CurGridCoverData = null;
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                WaitBox.Text = strCityName + " 开始统计查询栅格数据...";
                WaitBox.CanCancel = true;
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();

                foreach (TimePeriod period in condition.Periods)
                {
                    queryPeriodInfo(period, clientProxy, statImgIDSet, MainModel.CurGridColorUnitMatrix);
                }
                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                MainModel.CurGridCoverData = null;

                WaitBox.Text = strCityName + " 数据获取完毕，进行对比处理...";
                doCompare();
                WaitBox.Text = strCityName + " 对比完毕，进行栅格汇聚处理...";
                doCombine();
                gridFileNameListDic.Clear();

                if (MainModel.CurGridCoverData != null)
                {
                    MainModel.CurGridCoverData.Clear();
                }
            }
            catch (Exception e)
            {
                MessageBox.Show("Error" + e.StackTrace + e.ToString());
            }
            finally
            {
                WaitBox.Close();
            }
            MapGridLayer.NeedFreshFullImg = true;
        }

        /// <summary>
        /// 接收栅格信息
        /// </summary>
        /// <param name="clientProxy"></param>
        /// <param name="reservedParams"></param>
        protected override void recieveInfo_ImgGrid(ClientProxy clientProxy, params object[] reservedParams)
        {
            Package package = clientProxy.Package;
            int counter = 0;
            bool recved = false;
            int curPercent = 11;
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (!recved)
                {
                    WaitBox.Text = strCityName + " 正在从服务器接收" + strCarrName + "数据...";
                }
                recved = true;
                KPIStatDataBase singleStatData = null;
                if (isFileHeaderContentType(package.Content.Type))
                {
                    DTDataHeader fileInfo = recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
                    if (fileInfo != null)
                    {
                        DTDataHeaderManager.GetInstance().AddDTDataHeader(fileInfo);
                    }
                }
                else if (isImgColDefContent(package, curImgColumnDef))
                {
                    //
                }
                else if (isKPIDataContent(package, out singleStatData))
                {
                    fillData(package, curImgColumnDef, singleStatData);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    MessageBox.Show("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref counter, ref curPercent);
            }
        }

        private void fillData(Package package, List<StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            fillStatData(package, curImgColumnDef, singleStatData);
            ColorUnit cu = new ColorUnit();
            cu.LTLng = lng;
            cu.LTLat = lat;
            int rAt, cAt;
            GridHelper.GetIndexOfDefaultSizeGrid(cu.CenterLng, cu.CenterLat, out rAt, out cAt);
            cu = MainModel.CurGridColorUnitMatrix[rAt, cAt];
            if (cu != null || isValidStatImg(lng, lat))
            {
                if (cu == null)
                {
                    cu = new ColorUnit();
                    cu.LTLng = lng;
                    cu.LTLat = lat;
                    MainModel.CurGridColorUnitMatrix[rAt, cAt] = cu;
                }

                cu.Status = 1;
                cu.DataHub.AddStatData(singleStatData, false);

                if (isNeedSearchFileInfo)
                {
                    CenterLongLat cll = new CenterLongLat(cu.CenterLng, cu.CenterLat);
                    if (!gridFileNameListDic.ContainsKey(cll))
                    {
                        gridFileNameListDic[cll] = new List<string>();
                    }
                    gridFileNameListDic[cll].Add(fileIDNameDic[singleStatData.FileID]);
                }
            }
        }

        /// <summary>
        /// 对比过程
        /// </summary>
        protected virtual void doCompare()
        {

        }
        /// <summary>
        /// 分析连续栅格
        /// </summary>
        protected virtual void doCombine()
        {
            List<GridCompareCombineInfo> gridCompareCombineInfoList = new List<GridCompareCombineInfo>();
            foreach(GridTypeName gridTypeName in gridColorUnit.Keys)
            {
                List<GridCompareCombineBlock> blocks = new List<GridCompareCombineBlock>();
                List<GridCompareCombineBlock> canMergeBlks = new List<GridCompareCombineBlock>();
                List<GridColorUnit> gridList = doShortGrid(gridColorUnit[gridTypeName].Grids);
                getBlocks(gridTypeName, blocks, canMergeBlks, gridList);

                addGridCompareCombineInfoList(gridCompareCombineInfoList, gridTypeName, blocks);
            }
            if (!cityGridCompareInfoDic.ContainsKey(strCityName))
            {
                cityGridCompareInfoDic.Add(strCityName, gridCompareCombineInfoList);
            }
        }

        private void getBlocks(GridTypeName gridTypeName, List<GridCompareCombineBlock> blocks, List<GridCompareCombineBlock> canMergeBlks, List<GridColorUnit> gridList)
        {
            foreach (GridColorUnit griCu in gridList)
            {
                canMergeBlks.Clear();
                foreach (GridCompareCombineBlock blk in blocks)
                {
                    if (blk.CanCollect(griCu))
                    {
                        canMergeBlks.Add(blk);
                    }
                }
                if (canMergeBlks.Count == 0)
                {
                    blocks.Add(new GridCompareCombineBlock(gridTypeName, griCu));
                }
                else
                {
                    GridCompareCombineBlock firstBlk = canMergeBlks[0];
                    firstBlk.Collects(griCu);
                    for (int i = 1; i < canMergeBlks.Count; i++)
                    {
                        GridCompareCombineBlock blk2Merge = canMergeBlks[i];
                        firstBlk.Merge(blk2Merge);
                        blocks.Remove(blk2Merge);
                    }
                }
            }
        }

        private void addGridCompareCombineInfoList(List<GridCompareCombineInfo> gridCompareCombineInfoList, GridTypeName gridTypeName, List<GridCompareCombineBlock> blocks)
        {
            foreach (GridCompareCombineBlock block in blocks)
            {
                GridCompareCombineInfo gridItem = doChangGridCompareResult(block);
                if (gridItem != null)
                {
                    gridItem.ISN = gridCompareCombineInfoList.Count + 1;
                    gridItem.StrCityName = strCityName;
                    gridItem.StrGridType = gridTypeName.strGridType;
                    gridItem.StrGridName = gridTypeName.strGridName;
                    gridItem.StrDataSrc = strProjectName;
                    gridItem.IHostAllGridCount = gridCountInfo.IHostGridCount;
                    gridItem.IGuestAllGridCount = gridCountInfo.IGuestGridCount;
                    gridItem.IGuestDXAllGridCount = gridCountInfo.IGuestDXGridCount;
                    gridItem.IAllGridCount = gridCountInfo.IAllGridCount;
                    gridItem.ICompareGridCount = gridCountInfo.ICompareGridCount;
                    gridItem.IGridCnt = block.GridCnt;
                    gridItem.StrRoadName = GISManager.GetInstance().GetRoadPlaceDesc(gridItem.DLng, gridItem.DLat);
                    gridItem.StrGridABCName = GISManager.GetInstance().GetGridDesc(gridItem.DLng, gridItem.DLat);

                    gridCompareCombineInfoList.Add(gridItem);
                }
            }
        }

        /// <summary>
        /// 对栅格矩阵进行排序
        /// </summary>
        private List<GridColorUnit> doShortGrid(List<GridColorUnit> gridList)
        {
            List<GridColorUnit> gridListTmp = new List<GridColorUnit>();
            List<string> strList = new List<string>();
            foreach (GridColorUnit gridCu in gridList)
            {
                string str = gridCu.CuUnit.RowIdx + "_" + gridCu.CuUnit.ColIdx;
                if (!strList.Contains(str))
                {
                    strList.Add(str);
                }
            }
            strList.Sort();
            foreach (string str in strList)
            {
                foreach (GridColorUnit gridCu in gridList)
                {
                    string strTmp = gridCu.CuUnit.RowIdx + "_" + gridCu.CuUnit.ColIdx;
                    if (str.Equals(strTmp))
                    {
                        gridListTmp.Add(gridCu);
                        break;
                    }
                }
            }
            return gridListTmp;
        }
        /// <summary>
        /// 分析连续栅格
        /// </summary>
        protected virtual GridCompareCombineInfo doChangGridCompareResult(GridCompareCombineBlock block)
        {
            return null;
        }
        /// <summary>
        /// 获取预存或圈选的区域
        /// </summary>
        private void InitRegionMop2()
        {
            Dictionary<string, List<ResvRegion>> resvRegionsDic = MainModel.SearchGeometrys.SelectedResvRegionDic;
            MapWinGIS.Shape gmt = MainModel.SearchGeometrys.Region;
            if (resvRegionsDic != null && resvRegionsDic.Count > 0)
            {
                foreach (string strGridType in resvRegionsDic.Keys)
                {
                    addRegion(resvRegionsDic, strGridType);
                }
            }
            else if (gmt != null)//单个区域
            {
                Dictionary<string, MapOperation2> regionMopDic =
                    new Dictionary<string, MapOperation2>();
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(gmt);
                regionMopDic.Add("当前区域", mapOp2);
                mutRegionMopDic.Add("无网格类型", regionMopDic);
            }
        }

        private void addRegion(Dictionary<string, List<ResvRegion>> resvRegionsDic, string strGridType)
        {
            if (!mutRegionMopDic.ContainsKey(strGridType))
            {
                Dictionary<string, MapOperation2> regionMop = new Dictionary<string, MapOperation2>();
                foreach (ResvRegion region in resvRegionsDic[strGridType])
                {
                    if (!regionMop.ContainsKey(region.RegionName))
                    {
                        MapOperation2 mapOp2 = new MapOperation2();
                        mapOp2.FillPolygon(region.Shape);
                        regionMop.Add(region.RegionName, mapOp2);
                    }
                }
                mutRegionMopDic.Add(strGridType, regionMop);
            }
        }

        /// <summary>
        /// 定位所在网格
        /// </summary>
        public GridTypeName strContainDbRect(DbRect dRect)
        {
            GridTypeName gridTypeName = new GridTypeName("","");
            foreach (string gridType in mutRegionMopDic.Keys)
            {
                foreach (string grid in mutRegionMopDic[gridType].Keys)
                {
                    if (((gridType + grid).Contains("行政区") || (gridType + grid).Contains("规划区")
                        || (gridType + grid).Contains("AB网格"))
                        && !(gridType + grid).Contains(strCityName)
                        && !(gridType + grid).Contains("无网格"))
                    {
                        continue;
                    }
                    if (mutRegionMopDic[gridType][grid].CheckRectIntersectWithRegion(dRect))
                    {
                        gridTypeName.strGridType = gridType;
                        gridTypeName.strGridName = grid;
                    }
                }
            }
            return gridTypeName;
        }
        /// <summary>
        /// 显示结果集
        /// </summary>
        protected override void fireShowResult()
        {
            if (cityGridCompareInfoDic.Count == 0)
            {
                MessageBox.Show("没有结果");
                return;
            }
            GridCompareCombineForm showForm = null;
            object obj = MainModel.GetObjectFromBlackboard(typeof(GridCompareCombineForm).FullName);
            showForm = obj == null ? null : obj as GridCompareCombineForm;
            if (showForm == null || showForm.IsDisposed)
            {
                showForm = new GridCompareCombineForm(MainModel);
            }
            List<GridCompareCombineInfo> gridInfoList = new List<GridCompareCombineInfo>();
            foreach(string strCity in cityGridCompareInfoDic.Keys)
            {
                gridInfoList.AddRange(cityGridCompareInfoDic[strCity]);
            }
            cityGridCompareInfoDic.Clear();
            showForm.FillData(gridInfoList);
            showForm.Show(MainModel.MainForm);
        }
    }

    public class GridCompareCombineInfo
    {
        /// <summary>
        /// 序号
        /// </summary>
        public int ISN { get; set; }
        /// <summary>
        /// 地市
        /// </summary>
        public string StrCityName { get; set; }
        /// <summary>
        /// 网格类型
        /// </summary>
        public string StrGridType { get; set; }
        /// <summary>
        /// 网格号
        /// </summary>
        public string StrGridName { get; set; }
        /// <summary>
        /// ABC网格所属
        /// </summary>
        public string StrGridABCName { get; set; }
        /// <summary>
        /// 主队原总栅格数
        /// </summary>
        public int IHostAllGridCount { get; set; }
        /// <summary>
        /// 客队原总栅格数
        /// </summary>
        public int IGuestAllGridCount { get; set; }
        /// <summary>
        /// 客队电信原总栅格数
        /// </summary>
        public int IGuestDXAllGridCount { get; set; }
        /// <summary>
        /// 原始总栅格数
        /// </summary>
        public int IAllGridCount { get; set; }
        /// <summary>
        /// 对比总栅格数
        /// </summary>
        public int ICompareGridCount { get; set; }
        /// <summary>
        /// 数据来源
        /// </summary>
        public string StrDataSrc { get; set; }
        /// <summary>
        /// 连续栅格数
        /// </summary>
        public int IGridCnt { get; set; }
        /// <summary>
        /// 涉及LOG
        /// </summary>
        public string StrFileName { get; set; }
        /// <summary>
        /// 道路名称
        /// </summary>
        public string StrRoadName { get; set; }
        /// <summary>
        /// 中心经度
        /// </summary>
        public double DLng { get; set; }
        /// <summary>
        /// 中心纬度
        /// </summary>
        public double DLat { get; set; }
        /// <summary>
        /// 经度列表
        /// </summary>
        public string StrLngList { get; set; }
        /// <summary>
        /// 纬度度列表
        /// </summary>
        public string StrLatList { get; set; }
        /// <summary>
        /// 与对手对比劣势
        /// </summary>
        public string StrCompareInfo { get; set; }
        /// <summary>
        /// 问题描述
        /// </summary>
        public string StrProblemInfo { get; set; }
        /// <summary>
        /// 问题归类
        /// </summary>
        public string StrProblemType { get; set; }
    }

    public class GridColorUnit : GridUnitBase
    {
        public ColorUnit CuUnit { get; set; }
        public double DHostSpeed { get; set; }
        public double DGuestSpeed { get; set; }
        public float FHostRsrp { get; set; }
        public float FGuestRsrp { get; set; }
        public float FHostSinr { get; set; }
        public float FGuestSinr { get; set; }
        public string StreetInfo { get; set; }
        public double DHostMo { get; set; }
        public double DHostBase { get; set; }
        public double DGuestMo { get; set; }
        public double DGuestBase { get; set; }
        public int IHostRsrpSample { get; set; }
        public int IHostSinrSample { get; set; }
        public int IGuestRsrpSample { get; set; }
        public int IGuestSinrSample { get; set; }

        public double DGuestSpeed_dx { get; set; }
        public float FGuestRsrp_dx { get; set; }
        public float FGuestSinr_dx { get; set; }
        public double DGuestMo_dx { get; set; }
        public double DGuestBase_dx { get; set; }
        public int IGuestRsrpSample_dx { get; set; }
        public int IGuestSinrSample_dx { get; set; }
    }

    public class GridTypeName
    {
        public string strGridType { get; set; }
        public string strGridName { get; set; }

        public GridTypeName()
        {
            strGridType = "";
            strGridName = "";
        }

        public GridTypeName(string strType, string strName)
        {
            this.strGridType = strType;
            this.strGridName = strName;
        }

        public override bool Equals(object obj)
        {
            GridTypeName other = obj as GridTypeName;
            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.strGridType.Equals(other.strGridType) &&
                    this.strGridName.Equals(other.strGridName));
        }

        public override int GetHashCode()
        {
            string strKey = strGridType.ToString() + "_" + strGridName.ToString();
            return strKey.GetHashCode();
        }
    }

    public class GridCompareCombineBlock
    {
        public GridCompareCombineBlock(GridTypeName gridTypeName, GridColorUnit grid)
        {
            this.gridTypeName = gridTypeName;
            Collects(grid);
        }
        private GridTypeName gridTypeName { get; set; }
        
        public int GridCnt
        {
            get { return matrix.Length; }
        }
        public List<GridColorUnit> Grids
        {
            get { return matrix.Grids; }
        }

        public DbRect GetBounds()
        {
            return matrix.GetBounds();
        }

        /// <summary>
        /// 如果相邻，返回true，否则返回false
        /// </summary>
        /// <param name="grid">待判定的Grid</param>
        /// <returns>能汇聚：true;否则：false</returns>
        public bool CanCollect(GridColorUnit grid)
        {
            bool canCombine = false;
            int rowIdx = grid.CuUnit.RowIdx;
            int colIdx = grid.CuUnit.ColIdx;
            //先与扩大一圈的block的矩形边界判断，只有在这区域内的栅格才有可能汇聚
            if (rowIdx >= (matrix.MinRowIdx - 1) && rowIdx <= (matrix.MaxRowIdx + 1)
             && colIdx >= (matrix.MinColIdx - 1) && colIdx <= (matrix.MaxColIdx + 1))
            {
                return judgeGrid(rowIdx, colIdx);
            }
            return canCombine;
        }

        private bool judgeGrid(int rowIdx, int colIdx)
        {
            for (int r = -1; r < 2; r++)
            {
                int newRowIdx = rowIdx + r;
                for (int c = -1; c < 2; c++)
                {
                    bool isNear = judgeNearGrid(colIdx, r, newRowIdx, c);
                    if (isNear)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        private bool judgeNearGrid(int colIdx, int r, int newRowIdx, int c)
        {
            if (r != 0 || c != 0)
            {
                int newColIdx = colIdx + c;
                if (matrix[newRowIdx, newColIdx] != null)
                {
                    return true;
                }
            }
            return false;
        }

        readonly GridMatrix<GridColorUnit> matrix = new GridMatrix<GridColorUnit>();
        public void Collects(GridColorUnit grid)
        {
            matrix[grid.CuUnit.RowIdx, grid.CuUnit.ColIdx] = grid;
        }

        public void Merge(GridCompareCombineBlock block)
        {
            matrix.Merge(block.matrix);
        }

        /// <summary>
        /// 道路信息
        /// </summary>
        public string StrRoadName
        {
            get
            {
                string strRoadInfo = "";
                List<double> lngSet = new List<double>();
                List<double> latSet = new List<double>();
                foreach (GridColorUnit gridCU in Grids)
                {
                    lngSet.Add(gridCU.CuUnit.CenterLng);
                    latSet.Add(gridCU.CuUnit.CenterLat);
                }
                Dictionary<string, DbPoint> roadPntDic = null;
                strRoadInfo = GISManager.GetInstance().GetRoadNames(lngSet, latSet, 1, out roadPntDic);

                return strRoadInfo;
            }
        }
    }

    public class GridCountInfo
    {
        public int IHostGridCount { get; set; }
        public int IGuestGridCount { get; set; }
        public int IGuestDXGridCount { get; set; }
        public int IAllGridCount { get; set; }
        public int ICompareGridCount { get; set; }
    }

    public class GridFormalValeInfo
    {
        public double dHostDownTime  { get; set; } = -999;
        public double dHostDownSize  { get; set; } = -999;
        public double dHostDownSpeed  { get; set; } = -999;

        public double dGuestDownTime  { get; set; } = -999;
        public double dGuestDownSize  { get; set; } = -999;
        public double dGuestDownSpeed  { get; set; } = -999;

        public double dGuestDownTime_dx  { get; set; } = -999;
        public double dGuestDownSize_dx  { get; set; } = -999;
        public double dGuestDownSpeed_dx  { get; set; } = -999;

        public float fHostRsrp  { get; set; } = -999;
        public float fHostSinr  { get; set; } = -999;
        public int iHostRsrpSample  { get; set; } = -999;
        public int iHostSinrSample  { get; set; } = -999;

        public float fGuestRsrp  { get; set; } = -999;
        public float fGuestSinr  { get; set; } = -999;
        public int iGuestRsrpSample  { get; set; } = -999;
        public int iGuestSinrSample  { get; set; } = -999;

        public float fGuestRsrp_dx  { get; set; } = -999;
        public float fGuestSinr_dx  { get; set; } = -999;
        public int iGuestRsrpSample_dx  { get; set; } = -999;
        public int iGuestSinrSample_dx  { get; set; } = -999;
    }
}
