﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.Util.UiEx;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class DiyInsertDeviceManageInfo : DiySqlMultiNonQuery
    {
        readonly List<DeviceManageInfo> deviceInfolist = null;
        public DiyInsertDeviceManageInfo(List<DeviceManageInfo> deviceInfolist)
        {
            MainDB = true;
            this.deviceInfolist = deviceInfolist;
        }

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Text = "正在向数据库导入数据......";
                queryInThread(clientProxy);
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override void queryInThread(object o)
        {
            base.queryInThread(o);
            System.Threading.Thread.Sleep(200);
            WaitBox.Text = "导入完毕.....";
        }

        protected override string getSqlTextString()
        {
            StringBuilder strb = new StringBuilder();
            foreach (DeviceManageInfo info in deviceInfolist)
            {
                strb.AppendFormat(@"IF EXISTS(SELECT 1 FROM [tb_testing_device_manage] where [BoxID] = '{0}') update [tb_testing_device_manage] set Vendor='{1}',SoftwareVersion='{2}',CurVersion='{3}',Area='{4}',Operation='{5}',Status='{6}' where [BoxID] = '{0}' ELSE insert into [tb_testing_device_manage]([BoxID],[Vendor],[SoftwareVersion],[CurVersion],[Area],[Operation],[Status],[DeviceStatus]) values ('{0}','{1}','{2}','{3}','{4}','{5}','{6}','{7}');", 
                info.BoxID, info.Vendor, info.SoftwareVersion, info.CurVersion, info.Area, info.Operation, info.Status, info.DeviceStatus);
            }

            return strb.ToString();
        }

        public override string Name
        {
            get { return "导入设备信息表"; }
        }
    }
}
