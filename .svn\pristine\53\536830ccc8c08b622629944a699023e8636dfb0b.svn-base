﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model.CellParam
{
    public class CellParamInfo
    {
        public override string ToString()
        {
            if (cell==null)
            {
                return string.Empty;
            }
            return cell.ToString();
        }
        public CellParamInfo(object cell, CellSign cellSign)
        {
            this.cell = cell;
            this.cellSign = cellSign;
        }
        private readonly Dictionary<DateTime, CellParamRecord> timeRcordDic = new Dictionary<DateTime, CellParamRecord>();
        public List<CellParamRecord> Records
        {
            get { return new List<CellParamRecord>(timeRcordDic.Values); }
        }
        public void AddRecord(DateTime date, CellParamTable table, Dictionary<CellParamColumn, object> paramValueDic)
        {
            CellParamRecord record = null;
            if (!timeRcordDic.TryGetValue(date, out record))
            {
                record = new CellParamRecord(date);
                timeRcordDic.Add(date,record);
            }
            record.AddTableRecord(table, paramValueDic);
        }
        private readonly object cell = null;
        public object Cell
        {
            get { return cell; }
        }
        private readonly CellSign cellSign = null;
        public CellSign CellSign
        {
            get { return cellSign; }
        }
    }

    /// <summary>
    /// 按记录时间汇总的记录，某个时间点，多张表的记录情况
    /// </summary>
    public class CellParamRecord
    {
        public CellParamRecord(DateTime recordTime)
        {
            this.recordTime = recordTime;
        }
        private readonly DateTime recordTime;
        public DateTime RecordTime
        {
            get { return recordTime; }
        }
        private readonly List<TableParamRecord> tableRecords = new List<TableParamRecord>();
        public List<TableParamRecord> TableRecords
        {
            get { return tableRecords; }
        }
        public void AddTableRecord(CellParamTable table, Dictionary<CellParamColumn, object> paramValueDic)
        {
            TableParamRecord tableValue = new TableParamRecord(table, recordTime, paramValueDic);
            tableRecords.Add(tableValue);
        }
    }

    public class TableParamRecord
    {
        public TableParamRecord(CellParamTable table, DateTime date, Dictionary<CellParamColumn, object> valueDic)
        {
            this.table = table;
            this.checkDate = date;
            colValueDic = valueDic;
        }

        private readonly DateTime checkDate;
        public DateTime RecordDate
        {
            get { return checkDate; }
        }
        private readonly CellParamTable table;
        public CellParamTable Table
        {
            get { return table; }
        }
        private readonly Dictionary<CellParamColumn, object> colValueDic = null;
        public Dictionary<CellParamColumn, object> ColValueDic
        {
            get { return colValueDic; }
        }
    }

}
