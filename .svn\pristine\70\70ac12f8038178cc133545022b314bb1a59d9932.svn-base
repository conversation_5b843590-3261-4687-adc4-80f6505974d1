﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDiyLteFddCellSetQueryByFile : ZTDiyLteCellSetQueryByFile
    {
        public ZTDiyLteFddCellSetQueryByFile(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "LTEFDD小区集（按文件）"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26012, this.Name);
        }
        
        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            DIYSampleGroup cellSetGroup = new DIYSampleGroup();
            cellSetGroup.ThemeName = "---";

            DTParameter parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_TAC");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_ECI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_EARFCN");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_PCI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_RSRQ");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_RSRP");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_SINR");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
       
            return cellSetGroup;
        }

        protected override void doWithDTData(TestPoint tp)
        {
            if (!isValidLTEData(tp))
                return;

            string fileName = tp.FileName;
            if (!fileNames.Contains(fileName))
            {
                fileNames.Add(fileName);
            }

            LTECell cell = tp.GetMainCell_LTE_FDD();
            if (cell == null)
            {
                addNotFindCellInfo(tp);
                return;
            }

            Dictionary<LTECell, LteCellInfo> lteCellInfoDic = null;
            if(!fileLteCellInfoDic.TryGetValue(fileName, out lteCellInfoDic))
            {
                lteCellInfoDic = new Dictionary<LTECell, LteCellInfo>();
                fileLteCellInfoDic.Add(fileName, lteCellInfoDic);
            }

            if (lteCellInfoDic.ContainsKey(cell))
            {
                LteCellInfo lcInfo = lteCellInfoDic[cell];
                lcInfo.calInfo(cell, tp);
            }
            else
            {
                LteCellInfo lcInfo = new LteCellInfo();
                lcInfo.FileName = fileName;//文件名
                lcInfo.Cellname = cell.Name;
                lcInfo.CellCode = cell.Code;
                lcInfo.Tac = (int)(ushort)tp["lte_fdd_TAC"];
                lcInfo.Eci = (int)tp["lte_fdd_ECI"];
                lcInfo.SampleCount = 1;

                lteCellInfoDic.Add(cell, lcInfo);
            }

            if (fileSampleCount.ContainsKey(fileName))
            {
                fileSampleCount[fileName]++;
            }
            else
            {
                fileSampleCount[fileName] = 1;
            }
        }

        private bool isValidLTEData(TestPoint tp)
        {
            float? rsrp = (float?)tp["lte_fdd_RSRP"];
            float? rsrq = (float?)tp["lte_fdd_RSRQ"];
            float? sinr = (float?)tp["lte_fdd_SINR"];

            if (tp["lte_fdd_TAC"] == null || tp["lte_fdd_ECI"] == null
                || tp["lte_fdd_EARFCN"] == null || tp["lte_fdd_PCI"] == null
                || rsrp == null || rsrq == null || sinr == null)
            {
                return false;
            }
            else if (rsrp < -140 || rsrp > -10 || rsrq < -100 || rsrq > 100 || sinr < -100 || sinr > 100)
            {
                return false;
            }
            return true;
        }

        private void addNotFindCellInfo(TestPoint tp)
        {
            int iTAC = (int)(ushort)tp["lte_fdd_TAC"];
            int iEci = (int)tp["lte_fdd_ECI"];
            string strTAC_ECI = "" + iTAC + "_" + iEci;
            string fileName = tp.FileName;//文件名

            Dictionary<string, LteCellInfo> notFindCellInfoDic = null;

            if (!fileNotFindCellInfoDic.TryGetValue(fileName, out notFindCellInfoDic))
            {
                notFindCellInfoDic = new Dictionary<string, LteCellInfo>();
                fileNotFindCellInfoDic.Add(fileName, notFindCellInfoDic);
            }

            if (notFindCellInfoDic.ContainsKey(strTAC_ECI))
            {
                setNotFindCellInfoDic(tp, strTAC_ECI, notFindCellInfoDic);

                notFindCellInfoDic[strTAC_ECI].AvgRsrp = Math.Round((notFindCellInfoDic[strTAC_ECI].AvgRsrp * notFindCellInfoDic[strTAC_ECI].SampleCount
                    + (float)tp["lte_fdd_RSRP"]) / (notFindCellInfoDic[strTAC_ECI].SampleCount + 1), 2);
                notFindCellInfoDic[strTAC_ECI].AvgRsrq = Math.Round((notFindCellInfoDic[strTAC_ECI].AvgRsrq * notFindCellInfoDic[strTAC_ECI].SampleCount
                    + (float)tp["lte_fdd_RSRQ"]) / (notFindCellInfoDic[strTAC_ECI].SampleCount + 1), 2);
                notFindCellInfoDic[strTAC_ECI].AvgSinr = Math.Round((notFindCellInfoDic[strTAC_ECI].AvgSinr * notFindCellInfoDic[strTAC_ECI].SampleCount
                    + (float)tp["lte_fdd_SINR"]) / (notFindCellInfoDic[strTAC_ECI].SampleCount + 1), 2);

                notFindCellInfoDic[strTAC_ECI].SampleCount++;
            }
            else
            {
                LteCellInfo lcInfo = new LteCellInfo();
                lcInfo.FileName = fileName;//文件名
                lcInfo.Cellname = strTAC_ECI;//匹配不上的小区显示为空
                lcInfo.Tac = iTAC;
                lcInfo.Eci = iEci;
                lcInfo.SampleCount = 1;

                notFindCellInfoDic.Add(strTAC_ECI, lcInfo);
            }

            if(fileSampleCount.ContainsKey(fileName))
            {
                fileSampleCount[fileName]++;
            }
            else
            {
                fileSampleCount[fileName] = 1;
            }
        }

        private static void setNotFindCellInfoDic(TestPoint tp, string strTAC_ECI, Dictionary<string, LteCellInfo> notFindCellInfoDic)
        {
            if (notFindCellInfoDic[strTAC_ECI].MaxRsrp < (float)tp["lte_fdd_RSRP"])
            {
                notFindCellInfoDic[strTAC_ECI].MaxRsrp = (float)tp["lte_fdd_RSRP"];
            }
            if (notFindCellInfoDic[strTAC_ECI].MaxRsrq < (float)tp["lte_fdd_RSRQ"])
            {
                notFindCellInfoDic[strTAC_ECI].MaxRsrq = (float)tp["lte_fdd_RSRQ"];
            }
            if (notFindCellInfoDic[strTAC_ECI].MaxSinr < (float)tp["lte_fdd_SINR"])
            {
                notFindCellInfoDic[strTAC_ECI].MaxSinr = (float)tp["lte_fdd_SINR"];
            }

            if (notFindCellInfoDic[strTAC_ECI].MinRsrp > (float)tp["lte_fdd_RSRP"])
            {
                notFindCellInfoDic[strTAC_ECI].MinRsrp = (float)tp["lte_fdd_RSRP"];
            }
            if (notFindCellInfoDic[strTAC_ECI].MinRsrq > (float)tp["lte_fdd_RSRQ"])
            {
                notFindCellInfoDic[strTAC_ECI].MinRsrq = (float)tp["lte_fdd_RSRQ"];
            }
            if (notFindCellInfoDic[strTAC_ECI].MinSinr > (float)tp["lte_fdd_SINR"])
            {
                notFindCellInfoDic[strTAC_ECI].MinSinr = (float)tp["lte_fdd_SINR"];
            }
        }
    }

    public class ZTDiyLteFddCellSetQueryByFile_VOLTE : ZTDiyLteFddCellSetQueryByFile
    {
        public ZTDiyLteFddCellSetQueryByFile_VOLTE(MainModel mainModel)
            : base(mainModel)
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }
        public override string Name
        {
            get { return "VOTLE_FDD小区集（按文件）"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30003, this.Name);
        }
        protected override bool isValidTestPoint(TestPoint tp)
        {
            if (ServiceTypes.Count > 0 && !ServiceTypes.Contains((ServiceType)tp.ServiceType)) return false;
            return true;
        }
    }
}
