﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.NOP
{
    public class ProcRoutineManager
    {
        private bool inited = false;
        protected ProcRoutineManager()
        {
        }


        private static ProcRoutineManager instance = null;
        public static ProcRoutineManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new ProcRoutineManager();
                }
                return instance;
            }
        }

        public Dictionary<int, ProcRoutine> IdProcDic { get; set; } = new Dictionary<int, ProcRoutine>();

        public Dictionary<int, ProcRelation> IdProcRelationDic { get; set; } = new Dictionary<int, ProcRelation>();

        public Dictionary<string, List<ProcRoutine>> NameProcDic { get; set; } = new Dictionary<string, List<ProcRoutine>>();

        public ProcRoutine this[string name, int id]
        {
            get
            {
                ProcRoutine proc = null;
                List<ProcRoutine> list = null;
                if (NameProcDic.TryGetValue(name, out list))
                {
                    proc = list.Find(delegate (ProcRoutine x) { return x.ID == id; });
                }
                return proc;
            }
        }

        public ProcRoutine this[int id]
        {
            get
            {
                ProcRoutine proc = null;
                IdProcDic.TryGetValue(id, out proc);
                return proc;
            }
        }

        public List<ProcRelation> Relations
        {
            get
            {
                List<ProcRelation> list = new List<ProcRelation>(IdProcRelationDic.Values);
                return list;
            }
        }

        public ProcRelation GetRelation(int id)
        {
            ProcRelation r;
            IdProcRelationDic.TryGetValue(id, out r);
            return r;
        }

        public virtual void Init()
        {
            if (inited)
            {
                return;
            }
            IdProcDic = new Dictionary<int, ProcRoutine>();
            IdProcRelationDic = new Dictionary<int, ProcRelation>();
            NameProcDic = new Dictionary<string, List<ProcRoutine>>();
            try
            {
                QueryProcNodeCfg qryProc = new QueryProcNodeCfg();
                qryProc.Query();

                QueryProcRelationCfg qryR = new QueryProcRelationCfg();
                qryR.Query();
                inited = true;
            }
            finally
            {
                WaitTextBox.Close();
            }
        }


        internal void AddProc(ProcRoutine proc)
        {
            IdProcDic[proc.ID] = proc;
            List<ProcRoutine> list = null;
            if (!NameProcDic.TryGetValue(proc.Name, out list))
            {
                list = new List<ProcRoutine>();
                NameProcDic[proc.Name] = list;
            }
            list.Insert(0, proc);
        }

        internal void AddProcRelation(int rId, int pId, int cId)
        {
            ProcRelation relation;
            if (!IdProcRelationDic.TryGetValue(rId, out relation))
            {
                relation = new ProcRelation();
                relation.RId = rId;
                relation.Proc = this[pId];
                IdProcRelationDic[rId] = relation;
            }
            ProcRoutine childProc = this[cId];
            if (childProc != null)
            {
                relation[childProc.Name] = childProc;
            }
        }
    }

    public class ProcRelation
    {
        public override string ToString()
        {
            if (Proc == null)
            {
                return string.Empty;
            }
            return string.Format("{0} Ver.{1}", Proc.Name, VersionId);
        }

        public int RId { get; set; }
        public ProcRoutine Proc
        { get; set; }
        public int VersionId { get; set; }

        public ProcRelation()
        {
            VersionId = 1;
        }

        private readonly Dictionary<string, ProcRoutine> childProc = new Dictionary<string, ProcRoutine>();

        public ProcRoutine this[string nodeName]
        {
            get
            {
                ProcRoutine procNode;
                childProc.TryGetValue(nodeName, out procNode);
                return procNode;
            }
            set
            {
                if (nodeName == null)
                {
                    return;
                }
                childProc[nodeName] = value;
            }
        }
    }

}
