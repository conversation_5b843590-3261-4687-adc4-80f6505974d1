﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS
{
    public partial class CPModeAddForm : Form
    {
        private CompareMode cpMode = null;

        public CPModeAddForm(CompareMode cpMode)
        {
            InitializeComponent();
            this.cpMode = cpMode;
        }

        public string ModeName
        {
            get { return textEditModeName.Text.Trim(); }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (ModeName.Length == 0)
            {
                MessageBox.Show("不能输入空字符串!", "提示");
            }
            else if (cpMode.compareConfigDic.ContainsKey(ModeName))
            {
                MessageBox.Show("名称已存在，请检查!", "提示");
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
