﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryLastRoadFileStatus : QueryBase
    {
        protected static readonly object lockObj = new object();
        private static QueryLastRoadFileStatus instance = null;
        public static QueryLastRoadFileStatus GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new QueryLastRoadFileStatus();
                    }
                }
            }
            return instance;
        }
        public QueryLastRoadFileStatus()
            : base(MainModel.GetInstance())
        {

        }
        public override string Name
        {
            get { return "平面里程文件处理状态"; }
        }       
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18041, this.Name);
        }
        protected override bool isValidCondition()
        {
            return true;
        }

        #region  变量
        int iAreaType = 1;
        List<string> rejectMapList;
        List<RoundCfg> roundCfgList;
        Dictionary<string, MapCfg> mapCfgDic;
        Dictionary<int, List<RoundCfg>> roundCfgDic;
        List<FileIDStatusInfo> cityFileStatusList;
        #endregion
        
        protected override void query()
        {
            initData();
            WaitBox.Show("正在查询图层及轮次表...", getMapSetCfg);
            ZTDIYLastRoadSetTimeForm timeForm = new ZTDIYLastRoadSetTimeForm(mapCfgDic, rejectMapList, roundCfgDic, false);
            if (timeForm.ShowDialog() != DialogResult.OK)
                return;
            if (!mapCfgDic.ContainsKey(timeForm.MapName))
            {
                MessageBox.Show("所选择的底图有误，请重新选择！", "信息提示", MessageBoxButtons.OK);
                return;
            }
            iAreaType = timeForm.iAreaType;
            MapCfg mapCfg = mapCfgDic[timeForm.MapName];
            foreach (RoundCfg rCfg in roundCfgDic[mapCfg.IMapId])
            {
                if (int.Parse(rCfg.StrRound) >= int.Parse(timeForm.StrSRound)
                    && int.Parse(rCfg.StrRound) <= int.Parse(timeForm.StrERound))
                    roundCfgList.Add(rCfg);
            }
            WaitBox.CanCancel = true;
            WaitBox.Show("开始查询文件状态...", doWithFileStatusByCtiyRound);
            showbackResultForm();
        }

        private void initData()
        {
            mapCfgDic = new Dictionary<string, MapCfg>();
            roundCfgDic = new Dictionary<int, List<RoundCfg>>();
            rejectMapList = new List<string>();
            roundCfgList = new List<RoundCfg>();
            cityFileStatusList = new List<FileIDStatusInfo>();
        }

        private void getMapSetCfg()
        {
            WaitBox.Text = "正在查询轮次表...";
            if (roundCfgDic.Count == 0)
            {
                DiySqlRoundSetCfg sqlRoundSetCfg = new DiySqlRoundSetCfg(MainModel);
                sqlRoundSetCfg.SetQueryCondition(condition);
                sqlRoundSetCfg.Query();
                this.roundCfgDic = sqlRoundSetCfg.roundCfgDic;
            }
            WaitBox.ProgressPercent = 30;

            WaitBox.Text = "正在查询图层配置表...";
            if (mapCfgDic.Count == 0)
            {
                List<bool> falsTrue = new List<bool>() {false,true };
                foreach (bool isMain in falsTrue)
                {
                    DiySqlMapSetCfg sqlMapSetCfg = new DiySqlMapSetCfg(MainModel, isMain);
                    sqlMapSetCfg.SetQueryCondition(condition);
                    sqlMapSetCfg.Query();
                    foreach (string strMap in sqlMapSetCfg.mapCfgDic.Keys)
                    {
                        if (!this.mapCfgDic.ContainsKey(strMap))
                        {
                            this.mapCfgDic.Add(strMap, sqlMapSetCfg.mapCfgDic[strMap]);
                        }
                    }
                }
            }
            WaitBox.ProgressPercent = 60;

            WaitBox.Text = "正在查询剔除图层表...";
            if (rejectMapList.Count == 0)
            {
                DiySqlRejectMap sqlMapRejCfg = new DiySqlRejectMap(MainModel);
                sqlMapRejCfg.SetQueryCondition(condition);
                sqlMapRejCfg.Query();
                rejectMapList = sqlMapRejCfg.rejectMapList;
            }
            WaitBox.ProgressPercent = 90;

            WaitBox.Close();
        }

        private void doWithFileStatusByCtiyRound()
        {
            string strProj = getStr(condition.Projects);
            string strServ = getStr(condition.ServiceTypes);
            int iDistrictID = MainModel.DistrictID;
            foreach (int districtID in condition.DistrictIDs)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                MainModel.DistrictID = districtID;
                string strCity = DistrictManager.GetInstance().getDistrictName(districtID);
                foreach (RoundCfg round in roundCfgList)
                {
                    FileIDStatusInfo fileIDStatusInfo = new FileIDStatusInfo();
                    fileIDStatusInfo.I序号 = cityFileStatusList.Count + 1;
                    fileIDStatusInfo.Str地市名称 = strCity;
                    fileIDStatusInfo.Str轮次 = round.StrRound;
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    WaitBox.Text = string.Format("正在查询处理{0}_地市{1}_轮次的文件状态...", fileIDStatusInfo.Str地市名称, round.StrRound);
                    setFileIDStatusInfo(strProj, strServ, round, fileIDStatusInfo);
                    cityFileStatusList.Add(fileIDStatusInfo);
                }
            }
            MainModel.DistrictID = iDistrictID;
            WaitBox.Close();
        }

        private string getStr(List<int> list)
        {
            StringBuilder str = new StringBuilder();

            foreach (int i in list)
            {
                str.Append(i + ",");
            }

            return str.ToString().Remove(str.Length - 1);
        }

        private void setFileIDStatusInfo(string strProj, string strServ, RoundCfg round, FileIDStatusInfo fileIDStatusInfo)
        {
            foreach (string strListFile in round.FileNameList)
            {
                string strLogFile = strListFile.Replace("tb_auto_filelist", "tb_log_file");
                string strSQL = string.Format("select ifileid,convert(varchar,statstatus) as statstatus,strfilename from {0} where icarriertype = 1 and iprojecttype in ({1}) and iservicetype in ({2}) "
                       + "and istime >= {3} and istime <= {4} and iareatype in ({5})", strLogFile, strProj, strServ
                       , condition.Periods[0].IBeginTime > round.IStime ? condition.Periods[0].IBeginTime : round.IStime
                       , condition.Periods[0].IEndTime < round.IEtiem ? condition.Periods[0].IEndTime : round.IEtiem, iAreaType);
                List<FileIDStatus> logFileList = getFileList(strSQL);
                WaitBox.ProgressPercent = 30;
                strSQL = strSQL.Replace("tb_log_file", "tb_auto_filelist");
                List<FileIDStatus> listFileList = getFileList(strSQL);
                WaitBox.ProgressPercent = 50;
                strSQL = string.Format("select ifileid,convert(varchar,statstatus) as statstatus,'' as strfilename from tb_auto_filelist_status where statstatus >= {2} and imapid = {1} " +
                    "and strfilelist = '{0}' and strcomment = '{3}'", strListFile, round.IMapId, 1, round.Iid);
                List<FileIDStatus> lastFileList = getFileList(strSQL);
                WaitBox.ProgressPercent = 70;
                fileIDStatusInfo.I文件总数 += logFileList.Count;
                fileIDStatusInfo.I有效文件数 += getValidFileNum(logFileList);
                fileIDStatusInfo.I同步文件数 += listFileList.Count;
                fileIDStatusInfo.I平面里程文件数 += getLastFileNum(listFileList, lastFileList);
                strSQL = string.Format("select ifileid,convert(varchar,statstatus) as statstatus,'' as strfilename from tb_auto_filelist_status where statstatus >= {2} and imapid = {1} " +
                    "and strfilelist = '{0}' and strcomment = '{3}'", strListFile, round.IMapId, 2, round.Iid);
                lastFileList = getFileList(strSQL);
                WaitBox.ProgressPercent = 90;
                fileIDStatusInfo.I实际里程文件数 += getLastFileNum(listFileList, lastFileList);
            }
        }

        private List<FileIDStatus> getFileList(string strSQL)
        {
            DIYQueryFileStatus query = new DIYQueryFileStatus(MainModel);
            query.SetCondition(strSQL);
            query.Query();
            return query.fileStatusList;
        }

        private int getValidFileNum(List<FileIDStatus> logFileList)
        {
            int iFileNum = 0;
            foreach (FileIDStatus fId in logFileList)
            {
                if (fId.iStatus >= 3 && fId.iStatus <= 10)
                {
                    iFileNum++;
                }
            }
            return iFileNum;
        }

        private int getLastFileNum(List<FileIDStatus> listFileList, List<FileIDStatus> lastFileList)
        {
            int iLastFileNum = 0;
            Dictionary<int, FileIDStatus> listFileDic = new Dictionary<int, FileIDStatus>();
            foreach (FileIDStatus fId in listFileList)
            {
                if (!listFileDic.ContainsKey(fId.iFileID))
                {
                    listFileDic.Add(fId.iFileID, fId);
                }
            }
            foreach (FileIDStatus fId in lastFileList)
            {
                if (listFileDic.ContainsKey(fId.iFileID))
                {
                    iLastFileNum++;
                }
            }
            return iLastFileNum;
        }

        private void showbackResultForm()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(LastRoadFileStatusForm).FullName);
            LastRoadFileStatusForm lastbackShowForm = obj == null ? null : obj as LastRoadFileStatusForm;
            if (lastbackShowForm == null || lastbackShowForm.IsDisposed)
            {
                lastbackShowForm = new LastRoadFileStatusForm(MainModel);
            }
            lastbackShowForm.FillData(cityFileStatusList);
            lastbackShowForm.Show(MainModel.MainForm);
        }
    }

    public class DIYQueryFileStatus : DIYSQLBase
    {
        string sql = @"";
        public DIYQueryFileStatus(MainModel mm)
            : base(mm)
        {
            mainModel = mm;
        }

        public override string Name
        {
            get { return "查询平面里程文件信息"; }
        }   

        public void SetCondition(string strSql)
        {
            this.sql = strSql;
        }

        protected override string getSqlTextString()
        {
            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[3];
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx] = E_VType.E_String;
            return rType;
        }
        public List<FileIDStatus> fileStatusList { get; set; }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            fileStatusList = new List<FileIDStatus>();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    int iFileID = package.Content.GetParamInt();
                    int iStatus = Convert.ToInt32(package.Content.GetParamString());
                    string strFileName = package.Content.GetParamString();
                    FileIDStatus fileIDStatus = new FileIDStatus(iFileID, iStatus, strFileName);
                    fileStatusList.Add(fileIDStatus);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class FileIDStatus
    {
        public int iFileID { get; set; }
        public int iStatus { get; set; }
        public string strFileName { get; set; }

        public FileIDStatus(int iFileID, int iStatus,string strFileName)
        {
            this.iFileID = iFileID;
            this.iStatus = iStatus;
            this.strFileName = strFileName;
        }
    }

    public class FileIDStatusInfo
    {
        public int I序号 { get; set; }
        public string Str地市名称 { get; set; } = "";
        public string Str轮次 { get; set; } = "";
        public int I文件总数 { get; set; }
        public int I有效文件数 { get; set; }
        public int I同步文件数 { get; set; }
        public int I平面里程文件数 { get; set; }
        public int I实际里程文件数 { get; set; }
    }
}
