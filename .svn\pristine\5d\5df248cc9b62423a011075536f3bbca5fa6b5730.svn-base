using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Net
{
    public static partial class RequestType
    {
        public const byte REQTYPE_GIS_AREA_ADD = 0x22;
        public const byte REQTYPE_GIS_AREA_DELETE = 0x23;
        public const byte REQTYPE_GIS_AREA_EDIT = 0x24;

        public const byte REQTYPE_BLACKBLOCK_EDIT = 0xD9;
    }
    public static partial class ResponseType
    {
        public const byte RESTYPE_TEMP_INT = 0xDE;
    }
    public class EasyOpHelper
    {
        private readonly MainModel model;
        public EasyOpHelper(MainModel model)
        {
            this.model = model;
        }
        public int DoDeleteCQTAddress(CQTAddressItem item)
        {
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(model.Server.IP, model.Server.Port, model.User.LoginName, model.User.Password, model.DistrictID) != ConnectResult.Success)
            {
                return -1;
            }
            try
            {
                Package package = clientProxy.Package;
                package.Command = Command.CellConfigManage;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_GIS_AREA_DELETE;
                package.Content.PrepareAddParam();
                package.Content.AddParam(item.cqtTypeId);
                package.Content.AddParam(item.id);
                clientProxy.Send();
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                int ret = package.Content.GetParamInt();
                if (ret > 0)
                {
                    return ret;
                }
                else
                {
                    return -1;
                }
            }
            finally
            {
                clientProxy.Close();
            }
        }
        public int DoEditCQTAddress(CQTAddressItem item)
        {
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(model.Server.IP, model.Server.Port, model.User.LoginName, model.User.Password, model.DistrictID) != ConnectResult.Success)
            {
                return -1;
            }
            try
            {
                Package package = clientProxy.Package;
                package.Command = Command.CellConfigManage;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_GIS_AREA_EDIT;
                package.Content.PrepareAddParam();
                package.Content.AddParam(item.cqtTypeId);
                package.Content.AddParam(item.id);
                package.Content.AddParam(item.jd);
                package.Content.AddParam(item.wd);
                package.Content.AddParam(item.name);
                package.Content.AddParam(item.desc);
                clientProxy.Send();
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                int ret = package.Content.GetParamInt();
                if (ret > 0)
                {
                    return ret;
                }
                else
                {
                    return -1;
                }
            }
            finally
            {
                clientProxy.Close();
            }
        }
        public int DoCreateNewCQTAddress(CQTAddressItem item)
        {
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(model.Server.IP, model.Server.Port, model.User.LoginName, model.User.Password, model.DistrictID) != ConnectResult.Success)
            {
                return -1 ;
            }
            try
            {
                Package package = clientProxy.Package;
                package.Command = Command.CellConfigManage;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_GIS_AREA_ADD;
                package.Content.PrepareAddParam();
                package.Content.AddParam(item.cqtTypeId);
                package.Content.AddParam(item.jd);
                package.Content.AddParam(item.wd);
                package.Content.AddParam(item.name);
                package.Content.AddParam(item.desc);
                clientProxy.Send();
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                int ret = package.Content.GetParamInt();
                if(ret>0)
                {
                    return ret;
                }
                else
                {
                    return -1;
                }
            }
            finally
            {
                clientProxy.Close();
            }
        }
        public int DoModifyBlackBlockInfo(int blockid,string name,string reasondesc)
        {
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(model.Server.IP, model.Server.Port, model.User.LoginName, model.User.Password, model.DistrictID) != ConnectResult.Success)
            {
                return -1;
            }
            try
            {
                Package package = clientProxy.Package;
                package.Command = Command.InfoQuery;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_BLACKBLOCK_EDIT;
                package.Content.PrepareAddParam();
                package.Content.AddParam(blockid);
                package.Content.AddParam(name);
                package.Content.AddParam(reasondesc);
                clientProxy.Send();
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_TEMP_INT)
                {
                    int ret = package.Content.GetParamInt();
                    return ret;
                }
                else
                {
                    return -1;
                }
            }
            finally
            {
                clientProxy.Close();
            }
        }
    }
}
