﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTRtpPacketsLostBase : DIYAnalyseByFileBackgroundBase
    {
        protected ZTRtpPacketsLostSetCondition hoCondition = new ZTRtpPacketsLostSetCondition();
        protected List<ZTRtpPacketsLostList> resultList;
        public ZTRtpPacketsLostBase(MainModel mainModel)
            : base(mainModel)
        {
            this.Columns = new List<string>();
            this.IncludeMessage = true;
            this.Columns.Add("lte_volte_RTP_Packets_Lost_Num");
            this.Columns.Add("lte_volte_Source_SSRC");
            this.Columns.Add("lte_volte_RTP_Loss_Rate");
            this.Columns.Add("lte_TAC");
            this.Columns.Add("lte_ECI");
            this.Columns.Add("lte_volte_RTP_Packets_Num");
            this.Columns.Add("lte_volte_RTP_Direction");
            this.Columns.Add("lte_EARFCN");
            this.Columns.Add("lte_PCI");
            this.Columns.Add("lte_RSRP");
            this.Columns.Add("lte_SINR");
            this.Columns.Add("lte_volte_RTP_Media_Type");
        }

        ZTRtpPacketsLostSetConditionForm setForm = null;

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new ZTRtpPacketsLostSetConditionForm();
            }
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                resultList = new List<ZTRtpPacketsLostList>();
                setForm.GetCondition(out hoCondition);
                return true;
            }
            return false;
        }

        protected override void doStatWithQuery()
        {
            List<ZTRtpPacketsLost> lostList=new List<ZTRtpPacketsLost>();
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                ZTRtpPacketsLost pLost = null;
                for (int i = 0; i < file.TestPoints.Count - 1; i++)
                {
                    TestPoint curTP = file.TestPoints[i];
                    TestPoint nextTP = file.TestPoints[i + 1];
                    if (curTP["lte_volte_RTP_Packets_Lost_Num"] == null
                        || nextTP["lte_volte_RTP_Packets_Lost_Num"] == null
                        || curTP["lte_volte_Source_SSRC"] == null
                        || nextTP["lte_volte_Source_SSRC"] == null
                        || nextTP["lte_volte_RTP_Loss_Rate"] == null)
                    {
                        continue;
                    }

                    int curPacketLostNum = Convert.ToInt32(curTP["lte_volte_RTP_Packets_Lost_Num"].ToString());
                    int nextPacketLostNum = Convert.ToInt32(nextTP["lte_volte_RTP_Packets_Lost_Num"].ToString());
                    double curSSRC = Convert.ToDouble(curTP["lte_volte_Source_SSRC"].ToString());
                    double nextSSRC = Convert.ToDouble(nextTP["lte_volte_Source_SSRC"].ToString());
                    double curLossRate = Convert.ToDouble(nextTP["lte_volte_RTP_Loss_Rate"].ToString());

                    if (curPacketLostNum < nextPacketLostNum
                        && curSSRC == nextSSRC
                        && curLossRate >= hoCondition.RtpLossRate)
                    {
                        pLost = new ZTRtpPacketsLost();
                        pLost.Longitude = nextTP.Longitude;
                        pLost.Latitude = nextTP.Latitude;
                        pLost.BeginTime = nextTP.DateTime;
                        if (nextTP["lte_EARFCN"] != null)
                        {
                            pLost.EARFCN = Convert.ToInt32(nextTP["lte_EARFCN"].ToString());
                        }
                        if (nextTP["lte_PCI"] != null)
                        {
                            pLost.PCI = Convert.ToInt32(nextTP["lte_PCI"].ToString());
                        }
                        if (nextTP["lte_volte_RTP_Loss_Rate"] != null)
                        {
                            pLost.LossRate = Convert.ToDouble(nextTP["lte_volte_RTP_Loss_Rate"].ToString());
                        }
                        if (nextTP["lte_volte_RTP_Packets_Lost_Num"] != null)
                        {
                            pLost.PacketsLostNum = nextPacketLostNum;
                        }
                        if (nextTP["lte_volte_RTP_Packets_Num"] != null)
                        {
                            pLost.PacketsNum = Convert.ToInt32(nextTP["lte_volte_RTP_Packets_Num"].ToString());
                        }
                        //if (nextTP["lte_volte_RTP_Direction"] != null)
                        //{
                        //    pLost.Direction = Convert.ToInt32(nextTP["lte_volte_RTP_Direction"].ToString());
                        //}
                        if (nextTP["lte_volte_Source_SSRC"] != null)
                        {
                            pLost.SourceSSRC = nextSSRC;
                        }
                        if (nextTP["lte_volte_RTP_Media_Type"] != null)
                        {
                            pLost.MediaType = Convert.ToInt32(nextTP["lte_volte_RTP_Media_Type"].ToString());
                        }
                        pLost.file = file;
                        pLost.AddTestPoint(nextTP);
                        lostList.Add(pLost);
                    }
                }

                float? sumSINRBefore = 0;
                float? sumSINRAfter = 0;
                float? sumRSRPBefore = 0;
                float? sumRSRPAfter = 0;
                float? maxSINRBefore = float.MinValue;
                float? maxSINRAfter = float.MinValue;
                float? maxRSRPBefore = float.MinValue;
                float? maxRSRPAfter = float.MinValue;
                int numBefore = 0;
                int numAfter = 0;
                foreach (ZTRtpPacketsLost lost1 in lostList)
                {
                    foreach (TestPoint tp in file.TestPoints)
                    {
                        if ((lost1.BeginTime - tp.DateTime).TotalSeconds >= hoCondition.TimeSpan)
                        {
                            addTPInfo(ref sumSINRBefore, ref sumRSRPBefore, ref maxSINRBefore, ref maxRSRPBefore, ref numBefore, tp);
                        }
                        if ((tp.DateTime - lost1.BeginTime).TotalSeconds >= hoCondition.TimeSpan)
                        {
                            addTPInfo(ref sumSINRAfter, ref sumRSRPAfter, ref maxSINRAfter, ref maxRSRPAfter, ref numAfter, tp);
                        }
                    }
                    if (numBefore != 0)
                    {
                        lost1.SINRBefore = (float)Math.Round((float)sumSINRBefore / numBefore, 2);
                        lost1.RSRPBefore = (float)Math.Round((float)sumRSRPBefore / numBefore, 2);
                        lost1.MaxSINRBefore = (float)maxSINRBefore;
                        lost1.MaxRSRPBefore = (float)maxRSRPBefore;
                    }
                    if (numAfter != 0)
                    {
                        lost1.SINRAfter = (float)Math.Round((float)sumSINRAfter / numAfter, 2);
                        lost1.RSRPAfter = (float)Math.Round((float)sumRSRPAfter / numAfter, 2);
                        lost1.MaxSINRAfter = (float)maxSINRAfter;
                        lost1.MaxRSRPAfter = (float)maxRSRPAfter;
                    }
                }
                double? ssrc = 0.0;

                foreach (ZTRtpPacketsLost lost2 in lostList)
                {
                    if (lost2.SourceSSRC == ssrc)
                    {
                        continue;
                    }
                    ssrc = lost2.SourceSSRC;
                    ZTRtpPacketsLostList listLost = new ZTRtpPacketsLostList(lostList, lost2.SourceSSRC, resultList.Count + 1);
                    resultList.Add(listLost);
                }
                foreach (ZTRtpPacketsLostList list in resultList)
                {
                    foreach (ZTRtpPacketsLost lost in list.LostList)
                    {
                        list.TestPoints.AddRange(lost.TestPoints);
                    }
                }
            }
        }

        private void addTPInfo(ref float? sumSINR, ref float? sumRSRP, ref float? maxSINR, ref float? maxRSRP, ref int num, TestPoint tp)
        {
            if (tp["lte_SINR"] != null && tp["lte_RSRP"] != null)
            {
                if (maxRSRP < (float?)tp["lte_RSRP"])
                {
                    maxRSRP = (float?)tp["lte_RSRP"];
                }
                if (maxSINR < (float?)tp["lte_SINR"])
                {
                    maxSINR = (float?)tp["lte_SINR"];
                }
                sumSINR += (float?)tp["lte_SINR"];
                sumRSRP += (float?)tp["lte_RSRP"];
                num++;
            }
        }

        protected override void fireShowForm()
        {
            if (resultList.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }

            ZTRtpPacketsLostListForm frm = MainModel.GetInstance().CreateResultForm(typeof(ZTRtpPacketsLostListForm)) as ZTRtpPacketsLostListForm;
            frm.FillData(resultList);
            frm.Visible = true;
            frm.BringToFront();
            resultList = null;
        }
    }

    public class ZTRtpPacketsLostBase_FDD : ZTRtpPacketsLostBase
    {
        public ZTRtpPacketsLostBase_FDD(MainModel mainModel)
            : base(mainModel)
        {
            this.Columns = new List<string>();
            this.IncludeMessage = true;
            this.Columns.Add("lte_fdd_volte_RTP_Packets_Lost_Num");
            this.Columns.Add("lte_fdd_volte_Source_SSRC");
            this.Columns.Add("lte_fdd_volte_RTP_Loss_Rate");
            this.Columns.Add("lte_fdd_TAC");
            this.Columns.Add("lte_fdd_ECI");
            this.Columns.Add("lte_fdd_volte_RTP_Packets_Num");
            this.Columns.Add("lte_fdd_volte_RTP_Direction");
            this.Columns.Add("lte_fdd_EARFCN");
            this.Columns.Add("lte_fdd_PCI");
            this.Columns.Add("lte_fdd_RSRP");
            this.Columns.Add("lte_fdd_SINR");
            this.Columns.Add("lte_fdd_volte_RTP_Media_Type");
        }
        protected override void doStatWithQuery()
        {
            List<ZTRtpPacketsLost> lostList = new List<ZTRtpPacketsLost>();
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                ZTRtpPacketsLost pLost = null;
                for (int i = 0; i < file.TestPoints.Count - 1; i++)
                {
                    TestPoint tpi = file.TestPoints[i];
                    if (tpi["lte_fdd_volte_RTP_Direction"] != null && tpi["lte_fdd_volte_RTP_Direction"].ToString() == "1")
                    {
                        int j = i + 1;
                        TestPoint tpj = file.TestPoints[j];
                        if (tpj["lte_fdd_volte_RTP_Direction"] != null
                            && tpj["lte_fdd_volte_RTP_Direction"].ToString() == "1"
                            && Convert.ToDouble(tpi["lte_fdd_volte_RTP_Packets_Lost_Num"].ToString()) < Convert.ToDouble(tpj["lte_fdd_volte_RTP_Packets_Lost_Num"].ToString())
                            && Convert.ToDouble(tpi["lte_fdd_volte_Source_SSRC"].ToString()) == Convert.ToDouble(tpj["lte_fdd_volte_Source_SSRC"].ToString())
                            && Convert.ToDouble(tpj["lte_fdd_volte_RTP_Loss_Rate"].ToString()) >= hoCondition.RtpLossRate)
                        {
                            pLost = new ZTRtpPacketsLost();
                            pLost.Longitude = tpj.Longitude;
                            pLost.Latitude = tpj.Latitude;
                            pLost.BeginTime = tpj.DateTime;
                            if (file.TestPoints[j]["lte_fdd_EARFCN"] != null)
                            {
                                pLost.EARFCN = Convert.ToInt32(file.TestPoints[j]["lte_fdd_EARFCN"].ToString());
                            }
                            if (file.TestPoints[j]["lte_fdd_PCI"] != null)
                            {
                                pLost.PCI = Convert.ToInt32(file.TestPoints[j]["lte_fdd_PCI"].ToString());
                            }
                            if (file.TestPoints[j]["lte_fdd_volte_RTP_Loss_Rate"] != null)
                            {
                                pLost.LossRate = Convert.ToDouble(file.TestPoints[j]["lte_fdd_volte_RTP_Loss_Rate"].ToString());
                            }
                            if (file.TestPoints[j]["lte_fdd_volte_RTP_Packets_Lost_Num"] != null)
                            {
                                pLost.PacketsLostNum = Convert.ToInt32(file.TestPoints[j]["lte_fdd_volte_RTP_Packets_Lost_Num"].ToString());
                            }
                            if (file.TestPoints[j]["lte_fdd_volte_RTP_Packets_Num"] != null)
                            {
                                pLost.PacketsNum = Convert.ToInt32(file.TestPoints[j]["lte_fdd_volte_RTP_Packets_Num"].ToString());
                            }
                            if (file.TestPoints[j]["lte_fdd_volte_RTP_Direction"] != null)
                            {
                                pLost.Direction = Convert.ToInt32(file.TestPoints[j]["lte_fdd_volte_RTP_Direction"].ToString());
                            }
                            if (file.TestPoints[j]["lte_fdd_volte_Source_SSRC"] != null)
                            {
                                pLost.SourceSSRC = Convert.ToDouble(file.TestPoints[j]["lte_fdd_volte_Source_SSRC"].ToString());
                            }
                            if (file.TestPoints[j]["lte_fdd_volte_RTP_Media_Type"] != null)
                            {
                                pLost.MediaType = Convert.ToInt32(file.TestPoints[j]["lte_fdd_volte_RTP_Media_Type"].ToString());
                            }
                            pLost.file = file;
                            pLost.AddTestPoint(tpj);
                            lostList.Add(pLost);
                        }
                    }
                }

                float? sumSINRBefore = 0;
                float? sumSINRAfter = 0;
                float? sumRSRPBefore = 0;
                float? sumRSRPAfter = 0;
                float? maxSINRBefore = float.MinValue;
                float? maxSINRAfter = float.MinValue;
                float? maxRSRPBefore = float.MinValue;
                float? maxRSRPAfter = float.MinValue;
                int numBefore = 0;
                int numAfter = 0;
                foreach (ZTRtpPacketsLost lost1 in lostList)
                {
                    foreach (TestPoint tp in file.TestPoints)
                    {
                        if ((lost1.BeginTime - tp.DateTime).TotalSeconds >= hoCondition.TimeSpan)
                        {
                            addTPInfo(ref sumSINRBefore, ref sumRSRPBefore, ref maxSINRBefore, ref maxRSRPBefore, ref numBefore, tp);
                        }
                        if ((tp.DateTime - lost1.BeginTime).TotalSeconds >= hoCondition.TimeSpan)
                        {
                            addTPInfo(ref sumSINRAfter, ref sumRSRPAfter, ref maxSINRAfter, ref maxRSRPAfter, ref numAfter, tp);
                        }
                    }
                    if (numBefore != 0)
                    {
                        lost1.SINRBefore = (float)Math.Round((float)sumSINRBefore / numBefore, 2);
                        lost1.RSRPBefore = (float)Math.Round((float)sumRSRPBefore / numBefore, 2);
                        lost1.MaxSINRBefore = (float)maxSINRBefore;
                        lost1.MaxRSRPBefore = (float)maxRSRPBefore;
                    }
                    if (numAfter != 0)
                    {
                        lost1.SINRAfter = (float)Math.Round((float)sumSINRAfter / numAfter, 2);
                        lost1.RSRPAfter = (float)Math.Round((float)sumRSRPAfter / numAfter, 2);
                        lost1.MaxSINRAfter = (float)maxSINRAfter;
                        lost1.MaxRSRPAfter = (float)maxRSRPAfter;
                    }
                }
                double? ssrc = 0.0;

                foreach (ZTRtpPacketsLost lost2 in lostList)
                {
                    if (lost2.SourceSSRC == ssrc)
                    {
                        continue;
                    }
                    ssrc = lost2.SourceSSRC;
                    ZTRtpPacketsLostList listLost = new ZTRtpPacketsLostList(lostList, lost2.SourceSSRC, resultList.Count + 1);
                    resultList.Add(listLost);
                }
                foreach (ZTRtpPacketsLostList list in resultList)
                {
                    foreach (ZTRtpPacketsLost lost in list.LostList)
                    {
                        list.TestPoints.AddRange(lost.TestPoints);
                    }
                }
            }
        }

        private void addTPInfo(ref float? sumSINR, ref float? sumRSRP, ref float? maxSINR, ref float? maxRSRP, ref int num, TestPoint tp)
        {
            if (tp["lte_fdd_SINR"] != null && tp["lte_fdd_RSRP"] != null)
            {
                if (maxRSRP < (float?)tp["lte_fdd_RSRP"])
                {
                    maxRSRP = (float?)tp["lte_fdd_RSRP"];
                }
                if (maxSINR < (float?)tp["lte_fdd_SINR"])
                {
                    maxSINR = (float?)tp["lte_fdd_SINR"];
                }
                sumSINR += (float?)tp["lte_fdd_SINR"];
                sumRSRP += (float?)tp["lte_fdd_RSRP"];
                num++;
            }
        }
    }

    public class ZTRtpPacketsLostSetCondition
    {
        public double RtpLossRate { get; set; }
        public int TimeSpan { get; set; }
        public ZTRtpPacketsLostSetCondition()
        {
            RtpLossRate = 0.5;
            TimeSpan = 5;
        }
    }

    public class ZTRtpPacketsLost
    {
        public int SN { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public DateTime BeginTime { get; set; }
        public float? SINRBefore { get; set; }
        public float? RSRPBefore { get; set; }
        public float? MaxSINRBefore { get; set; }
        public float? MaxRSRPBefore { get; set; }
        public float? MaxSINRAfter { get; set; }
        public float? MaxRSRPAfter { get; set; }
        public float? SINRAfter { get; set; }
        public float? RSRPAfter { get; set; }
        public int? TAC { get; set; }
        public int? ECI { get; set; }
        public int? EARFCN { get; set; }
        public int? PCI { get; set; }
        public int? Direction { get; set; }
        public int? PacketsLostNum { get; set; }
        public int? PacketsNum { get; set; }
        public double? LossRate { get; set; }
        public double? MaxRate { get; set; }
        public int? MediaType { get; set; }
        public double? SourceSSRC { get; set; }
        public DTFileDataManager file { get; set; }

        private readonly List<TestPoint> tpList = new List<TestPoint>();
        public void AddTestPoint(TestPoint tp)
        {
            tpList.Add(tp);
        }
        public List<TestPoint> TestPoints
        {
            get { return tpList; }
        }

        private readonly List<Event> evtList = new List<Event>();
        public void AddEvent(Event evt)
        {
            evtList.Add(evt);
        }
        public List<Event> Events
        {
            get { return evtList; }
        }

        private readonly List<MasterCom.RAMS.Model.Message> msgList = new List<Model.Message>();
        public void AddMessage(Model.Message msg)
        {
            msgList.Add(msg);
        }
        public List<Model.Message> Messages
        {
            get { return msgList; }
        }
    }

    public class ZTRtpPacketsLostList
    {
        public int SN { get; set; }
        public double? SourceSSRC { get; set; }
        public string FileName { get; set; }
        public List<ZTRtpPacketsLost> LostList
        {
            get;
            set;
        }
        public ZTRtpPacketsLostList(List<ZTRtpPacketsLost> lostList,double? ssrc,int sn)
        {
            this.SN = sn;
            this.SourceSSRC = ssrc;
            this.LostList = new List<ZTRtpPacketsLost>();
            foreach (ZTRtpPacketsLost lost in lostList)
            {
                if (lost.SourceSSRC == ssrc)
                {
                    LostList.Add(lost);
                    this.FileName = lost.file.FileName;
                }
            }
        }

        private readonly List<TestPoint> tpList = new List<TestPoint>();
        public void AddTestPoint(TestPoint tp)
        {
            tpList.Add(tp);
        }
        public List<TestPoint> TestPoints
        {
            get { return tpList; }
        }

        private readonly List<Event> evtList = new List<Event>();
        public void AddEvent(Event evt)
        {
            evtList.Add(evt);
        }
        public List<Event> Events
        {
            get { return evtList; }
        }

        private readonly List<MasterCom.RAMS.Model.Message> msgList = new List<Model.Message>();
        public void AddMessage(Model.Message msg)
        {
            msgList.Add(msg);
        }
        public List<Model.Message> Messages
        {
            get { return msgList; }
        }
    }
}
