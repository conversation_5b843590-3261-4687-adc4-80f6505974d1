﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class CellSetByBackgroundProperties
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.numMinRxLev = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl = new DevExpress.XtraEditors.GroupControl();
            this.chkBackgroundStat = new DevExpress.XtraEditors.CheckEdit();
            this.cbxBandType = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMinRxLev.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl)).BeginInit();
            this.groupControl.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkBackgroundStat.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxBandType.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.labelControl3);
            this.groupControl1.Controls.Add(this.cbxBandType);
            this.groupControl1.Controls.Add(this.labelControl2);
            this.groupControl1.Controls.Add(this.numMinRxLev);
            this.groupControl1.Controls.Add(this.labelControl1);
            this.groupControl1.Location = new System.Drawing.Point(3, 62);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(448, 142);
            this.groupControl1.TabIndex = 5;
            this.groupControl1.Text = "条件设置";
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(239, 29);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(24, 14);
            this.labelControl2.TabIndex = 2;
            this.labelControl2.Text = "dBm";
            // 
            // numMinRxLev
            // 
            this.numMinRxLev.EditValue = new decimal(new int[] {
            90,
            0,
            0,
            -2147483648});
            this.numMinRxLev.Location = new System.Drawing.Point(151, 26);
            this.numMinRxLev.Name = "numMinRxLev";
            this.numMinRxLev.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMinRxLev.Properties.IsFloatValue = false;
            this.numMinRxLev.Properties.Mask.EditMask = "N00";
            this.numMinRxLev.Size = new System.Drawing.Size(82, 21);
            this.numMinRxLev.TabIndex = 1;
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(88, 29);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(57, 14);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "信号强度≥";
            // 
            // groupControl
            // 
            this.groupControl.Controls.Add(this.chkBackgroundStat);
            this.groupControl.Location = new System.Drawing.Point(3, 3);
            this.groupControl.Name = "groupControl";
            this.groupControl.Size = new System.Drawing.Size(448, 53);
            this.groupControl.TabIndex = 4;
            this.groupControl.Text = "开关设置";
            // 
            // chkBackgroundStat
            // 
            this.chkBackgroundStat.Location = new System.Drawing.Point(15, 26);
            this.chkBackgroundStat.Name = "chkBackgroundStat";
            this.chkBackgroundStat.Properties.Caption = "启用";
            this.chkBackgroundStat.Size = new System.Drawing.Size(75, 19);
            this.chkBackgroundStat.TabIndex = 0;
            // 
            // cbxBandType
            // 
            this.cbxBandType.Location = new System.Drawing.Point(151, 61);
            this.cbxBandType.Name = "cbxBandType";
            this.cbxBandType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxBandType.Size = new System.Drawing.Size(100, 21);
            this.cbxBandType.TabIndex = 3;
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(88, 64);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(28, 14);
            this.labelControl3.TabIndex = 4;
            this.labelControl3.Text = "频段:";
            // 
            // CellSetByBackgroundProperties
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.groupControl1);
            this.Controls.Add(this.groupControl);
            this.Name = "CellSetByBackgroundProperties";
            this.Size = new System.Drawing.Size(454, 280);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMinRxLev.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl)).EndInit();
            this.groupControl.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.chkBackgroundStat.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxBandType.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SpinEdit numMinRxLev;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.GroupControl groupControl;
        private DevExpress.XtraEditors.CheckEdit chkBackgroundStat;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.ComboBoxEdit cbxBandType;
    }
}
