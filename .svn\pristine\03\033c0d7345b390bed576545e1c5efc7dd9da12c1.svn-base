﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Net
{
    public class ZTDIYCellSetByStreet : ZTDIYCellSetByRegion
    {
        public ZTDIYCellSetByStreet(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get { return "道路小区集分析"; }
        }
        public override string IconName
        {
            get { return "Images/streetq.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12008, this.Name);
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.SelectedStreets.Count <= 0)
            {
                return false;
            }
            return true;
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Street;
        }
        protected override void prepareStatPackage_Sample_SampleFilter(Package package, TimePeriod period)
        {
            AddDIYStreets_Sample(package);
            AddDIYEndOpFlag(package);
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            return intersects(tp.Longitude, tp.Latitude);
        }

        private bool intersects(double longitude, double latitude)
        {
            return Condition.Geometorys.GeoOp.Contains(longitude, latitude);
        }
    }
}
