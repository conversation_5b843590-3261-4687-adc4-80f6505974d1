﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.UserMng;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRUnKnownDisturbAnaByRegion : DIYAnalyseByFileBackgroundBase
    {
        NRUnknownDisturbCondition disturbenceCondition = null;
        protected bool saveTestPoints = true;
        List<NRUnknownDisturbInfo> disturbenceList = null;

        private static readonly object lockObj = new object();
        private static NRUnKnownDisturbAnaByRegion intance = null;
        public static NRUnKnownDisturbAnaByRegion GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new NRUnKnownDisturbAnaByRegion();
                    }
                }
            }
            return intance;
        }
        protected NRUnKnownDisturbAnaByRegion()
            : base(MainModel.GetInstance())
        {
            init();
        }

        protected void init()
        {
            if (intance != null)
            {
                return;
            }
            Columns = NRTpHelper.InitBaseReplayParamBackground(false, true);
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
        }
        public override string Name
        {
            get { return "不明干扰(按区域)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35019, "NR不明干扰");
        }
        protected override void fireShowForm()
        {
            if (disturbenceList.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }
            NRUnknownDisturbInfoForm frm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(NRUnknownDisturbInfoForm)) as NRUnknownDisturbInfoForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new NRUnknownDisturbInfoForm(MainModel);
            }
            frm.FillData(disturbenceList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                saveTestPoints = false;
                return true;
            }
            NRUnknownDisturbDlg dlg = new NRUnknownDisturbDlg(disturbenceCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                disturbenceCondition = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            disturbenceList = new List<NRUnknownDisturbInfo>();
        }

        protected virtual float? getNMaxRsrp(TestPoint tp)
        {
            for (int i = 0; i < 16; i++)
            {
                NRTpHelper.NRNCellType type = NRTpHelper.NrTpManager.GetNCellType(tp, i);
                if (type == NRTpHelper.NRNCellType.NCELL)
                {
                    float? rsrp = NRTpHelper.NrTpManager.GetNCellRsrp(tp, i);
                    return rsrp;
                }
            }
            return null;
        }
        protected override void doStatWithQuery()
        {
            List<TestPoint> testPointList = MainModel.DTDataManager.FileDataManagers[0].TestPoints;
            NRUnknownDisturbInfo disturbenceCover = null;
            TestPoint prePoint = null;//前一点

            for (int i = 0; i < testPointList.Count; i++)
            {
                TestPoint testPoint = testPointList[i];
                if (isValidTestPoint(testPoint))
                {
                    disturbenceCover = getNRUnknownDisturbInfo(testPointList, disturbenceCover, prePoint, i, testPoint);
                    prePoint = testPoint;
                }
                else//区域外的采样点
                {
                    saveWeakCoverInfo(disturbenceCover);
                    disturbenceCover = null;//重置
                }
            }
        }

        private NRUnknownDisturbInfo getNRUnknownDisturbInfo(List<TestPoint> testPointList, NRUnknownDisturbInfo disturbenceCover, TestPoint prePoint, int i, TestPoint testPoint)
        {
            float? sinr = NRTpHelper.NrTpManager.GetSCellSinr(testPoint);
            float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(testPoint);
            float? nMaxRsrp = getNMaxRsrp(testPoint);
            if (sinr == null || rsrp == null || nMaxRsrp == null)
            {
                return disturbenceCover;
            }

            TPInfo tpInfo = new TPInfo(sinr, rsrp, nMaxRsrp);
            if (!disturbenceCondition.IsMatchIndicator(tpInfo.Rsrp, tpInfo.Sinr, tpInfo.NMaxRsrp))//先作指标的判断
            {
                saveWeakCoverInfo(disturbenceCover);
                disturbenceCover = null;
            }
            else
            {
                if (disturbenceCover == null)
                {
                    disturbenceCover = new NRUnknownDisturbInfo();
                    addWeakCoverInfo(testPointList, disturbenceCover, i, testPoint, tpInfo, 0);
                }
                else
                {
                    double dis = prePoint.Distance2(testPoint.Longitude, testPoint.Latitude);
                    if (disturbenceCondition.IsMatchIndicator(tpInfo.Rsrp, tpInfo.Sinr, tpInfo.NMaxRsrp))
                    {
                        addWeakCoverInfo(testPointList, disturbenceCover, i, testPoint, tpInfo, dis);
                    }
                    else
                    {//两采样点距离不符合，该点开始新的路段
                        saveWeakCoverInfo(disturbenceCover);
                        disturbenceCover = new NRUnknownDisturbInfo();
                        disturbenceCover.AddTestPoint(testPoint, tpInfo, 0);
                    }
                }
            }

            return disturbenceCover;
        }

        private void addWeakCoverInfo(List<TestPoint> testPointList, NRUnknownDisturbInfo disturbenceCover, int i, TestPoint testPoint, TPInfo tpInfo, double dis)
        {
            //符合两采样点之间的距离门限
            disturbenceCover.AddTestPoint(testPoint, tpInfo, dis);
            if (i == testPointList.Count - 1)//最后一采样点
            {
                saveWeakCoverInfo(disturbenceCover);
            }
        }

        private void saveWeakCoverInfo(NRUnknownDisturbInfo info)
        {
            if (info == null
                || !disturbenceCondition.checkStayDistance(info.StayDistance)
                || !disturbenceCondition.CheckStayTime(info.StaySecond))
            {//不符合 最小持续距离 or 时间
                return;
            }
            //save 2 list
            if (disturbenceList == null)
            {
                disturbenceList = new List<NRUnknownDisturbInfo>();
            }
            info.SN = disturbenceList.Count + 1;
            info.FindRoadName();

            disturbenceList.Add(info);
        }

        public class TPInfo
        {
            public TPInfo(float? sinr, float? rsrp, float? nMaxRsrp)
            {
                Sinr = (float)sinr;
                Rsrp = (float)rsrp;
                NMaxRsrp = (float)nMaxRsrp;
            }

            public float Sinr { get; set; }
            public float Rsrp { get; set; }
            public float NMaxRsrp { get; set; }
        }
    }
}
