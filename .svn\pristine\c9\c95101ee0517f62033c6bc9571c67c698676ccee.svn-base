﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using DevExpress.XtraTreeList.Nodes;
using MasterCom.Util;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTKPITreeListForm : MinCloseForm
    {
        public CQTKPITreeListForm(MainModel mm)
            : base(mm)
        {
            InitializeComponent();
        }
        CQTPointMapLayer layer = null;
        TimePeriod curStatPeriod = null;
        CQTKPIDataManager dataManager = null;
        public void FillData(TimePeriod statPeriod,CQTKPIDataManager dataMng, CQTKPIReport report)
        {
            curStatPeriod = statPeriod;
            dataManager = dataMng;
            dataMng.MakeReportData(treeList, statPeriod, report);
            fillCombBoxReport(report);
            refreshLayer();
        }

        private void refreshLayer()
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf == null)
            {
                return;
            }
            mModel.MainForm.RefreshLegend();
            MasterCom.MTGis.CustomDrawLayer cLayer = mf.GetCustomLayer(typeof(CQTPointMapLayer));
            if (cLayer == null)
            {
                layer = new CQTPointMapLayer(mf.GetMapOperation(), "CQT");
                mf.AddTempCustomLayer(layer);
            }
            else
            {
                layer = cLayer as CQTPointMapLayer;
            }
            layer.CQTPoints2Show = CQTPointManager.GetInstance().CQTPoints;
            layer.Invalidate();
        }

        private void fillCombBoxReport(CQTKPIReport report)
        {
            cbeReport.Properties.Items.Clear();
            cbeReport.Properties.Items.Add(report);
            cbeReport.SelectedItem = report;
        }
        private void cbeReport_SelectedValueChanged(object sender, EventArgs e)
        {
            cbeCurColumn.Properties.Items.Clear();
            if (cbeReport.SelectedItem == null)
            {
                return;
            }
            CQTKPIReport report = cbeReport.SelectedItem as CQTKPIReport;
            if (report==null)
            {
                return;
            }
            foreach (CQTKPIReportColumn col in report.Columns)
            {
                cbeCurColumn.Properties.Items.Add(col);
            }
            foreach (CQTKPISummaryColumn sCol in report.SummaryColumns)
            {
                cbeCurColumn.Properties.Items.Add(sCol);
            }
            if (cbeCurColumn.Properties.Items.Count>0)
            {
                cbeCurColumn.SelectedIndex = 0;
            }
        }

        private void cbeCurColumn_SelectedValueChanged(object sender, EventArgs e)
        {
            MasterCom.RAMS.CQT.CQTKPIDataManager.ColumnShowEventArgs eA = new MasterCom.RAMS.CQT.CQTKPIDataManager.ColumnShowEventArgs();
            eA.column=cbeCurColumn.SelectedItem;
            dataManager.FireGisShowColumnChange(this, eA);
        }

        private void treeList_DoubleClick(object sender, EventArgs e)
        {
            TreeListNode fNode = treeList.FocusedNode;
            MainModel.SelCQTPoint = null;
            if (fNode!=null)
            {
                if (fNode.Tag is CQTMainPointKPI)
                {
                    CQTMainPointKPI mainPoint = fNode.Tag as CQTMainPointKPI;
                    MainModel.SelCQTPoint = mainPoint.CQTPoint;
                    MainModel.MainForm.GetMapForm().GoToView(mainPoint.CQTPoint.Longitude, mainPoint.CQTPoint.Latitude);
                }
                else if(fNode.Tag is CQTSubPointKPI)
                {
                    //CQTSubPointKPI subPoint = fNode.Tag as CQTSubPointKPI;
                }
                else if (fNode.Tag is CQTFileKPIData)
                {
                    //CQTFileKPIData fileData = fNode.Tag as CQTFileKPIData;
                }
            }
        }

        private void miReplayFile_Click(object sender, EventArgs e)
        {
              TreeListNode fNode = treeList.FocusedNode;
              if (fNode != null)
              {
                  if (fNode.Tag is CQTMainPointKPI)
                  {
                      CQTMainPointKPI mainPoint = fNode.Tag as CQTMainPointKPI;
                      DIYReplayFileQuery qry = new DIYReplayFileQuery(MainModel);
                      QueryCondition codition = new QueryCondition();
                      codition.DistrictID = MainModel.DistrictID;
                      codition.FileInfos = mainPoint.GetFileInfo((cbeReport.SelectedItem as CQTKPIReport).CarreerID);
                      qry.SetQueryCondition(codition);
                      qry.Query();
                  }
                  else if (fNode.Tag is CQTSubPointKPI)
                  {
                      CQTSubPointKPI subPoint = fNode.Tag as CQTSubPointKPI;
                      DIYReplayFileQuery qry = new DIYReplayFileQuery(MainModel);
                      QueryCondition codition = new QueryCondition();
                      codition.DistrictID = MainModel.DistrictID;
                      codition.FileInfos = subPoint.GetFileInfo((cbeReport.SelectedItem as CQTKPIReport).CarreerID);
                      qry.SetQueryCondition(codition);
                      qry.Query();
                  }
                  else if (fNode.Tag is CQTFileKPIData)
                  {
                      CQTFileKPIData fileData = fNode.Tag as CQTFileKPIData;
                      DIYReplayFileQuery qry = new DIYReplayFileQuery(MainModel);
                      QueryCondition codition = new QueryCondition();
                      codition.DistrictID = MainModel.DistrictID;
                      codition.FileInfos.Add(fileData.DataHeader);
                      qry.SetQueryCondition(codition);
                      qry.Query();
                  }
              }
        }

        private void treeList_CustomDrawNodeCell(object sender, DevExpress.XtraTreeList.CustomDrawNodeCellEventArgs e)
        {
            if (e.Node == null || e.Column == null || e.Node.Tag == null || e.Column == null)
            {
                return;
            }

            if (e.Column.Tag is CQTKPIReportColumn)
            {
                CQTKPIReportColumn col = e.Column.Tag as CQTKPIReportColumn;
                if (e.Node.Tag is CQTMainPointKPI)
                {
                    e.Appearance.BackColor = (e.Node.Tag as CQTMainPointKPI).ColumnKPIResultDic[col].Color;
                }
                else if (e.Node.Tag is CQTSubPointKPI)
                {
                    e.Appearance.BackColor = (e.Node.Tag as CQTSubPointKPI).ColumnKPIResultDic[col].Color;
                }
                else if (e.Node.Tag is CQTFileKPIData)
                {
                    e.Appearance.BackColor = (e.Node.Tag as CQTFileKPIData).ColumnKPIResultDic[col].Color;
                }
                e.Appearance.BorderColor = Color.Red;
            }
            else if(e.Column.Tag is CQTKPISummaryColumn)
            {
                CQTKPISummaryColumn col = e.Column.Tag as CQTKPISummaryColumn;
                if (e.Node.Tag is CQTMainPointKPI)
                {
                    e.Appearance.BackColor = (e.Node.Tag as CQTMainPointKPI).SummaryResultDic[col].Color;
                }
                else if (e.Node.Tag is CQTSubPointKPI)
                {
                    e.Appearance.BackColor = (e.Node.Tag as CQTSubPointKPI).SummaryResultDic[col].Color;
                }
                else if (e.Node.Tag is CQTFileKPIData)
                {
                    e.Appearance.BackColor = (e.Node.Tag as CQTFileKPIData).SummaryResultDic[col].Color;
                }
                e.Appearance.BorderColor = Color.Red;
            }
        }

   

      

     

    }
}
