﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    [Serializable]
    public class PRBLowSchedulingCause : CauseBase
    {
        public override string Name
        {
            get { return "PRB调度低"; }
        }

        public int PRBMin { get; set; } = 10;
        public int PRBMax { get; set; } = 10;
        public override string Desc
        {
            get { return string.Format("低速率发生期间PRB调度为{0}到{1}", PRBMin, PRBMax); }
        }

        public override string Suggestion
        {
            get { return null; }
        }

        public override void Judge(LowSpeedSeg segItem, List<Event> evts, List<TestPoint> allTP)
        {
            int i = 0;
            foreach (TestPoint pnt in segItem.TestPoints)
            {
                i++;
                if (!segItem.IsNeedJudge(pnt))
                {
                    continue;
                }
                int? pdsch_prb_num_s = (int?)pnt["lte_PDSCH_PRb_Num_s"];

                if (pdsch_prb_num_s != null && PRBMin <= (int)pdsch_prb_num_s && (int)pdsch_prb_num_s <= PRBMax)
                {
                    PRBLowSchedulingCause cln = this.Clone() as PRBLowSchedulingCause;
                    segItem.SetReason(new LowSpeedPointDetail(pnt, cln));
                }
            }
        }


        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                paramDic["prbBefore"] = this.PRBMin;
                paramDic["prbAfter"] = this.PRBMax;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.PRBMin = (int)value["prbBefore"];
                this.PRBMax = (int)value["prbAfter"];
            }
        }
    }
}
