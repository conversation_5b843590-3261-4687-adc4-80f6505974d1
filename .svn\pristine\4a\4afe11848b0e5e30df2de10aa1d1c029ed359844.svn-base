﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.UserMng;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.ZTFunc;
using System.Drawing;

namespace MasterCom.RAMS.Net
{
    public class ZTDiyQueryLteEdgeSpeedAnaByGrid : DIYGridQuery
    {
        public ZTDiyQueryLteEdgeSpeedAnaByGrid(MainModel mainModel)
            : base(mainModel)
        {
            this.mainModel = mainModel;
            this.size = "Lte_052164020101";
            this.time = "Lte_052164020102";
        }
        public override string Name
        {
            get { return "边缘下载速率分析_LTE(按栅格)"; }
        }
        public override string IconName
        {
            get { return null; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
         
            return new MasterCom.RAMS.UserMng.LogInfoItem(3, 13000, 13056, "查询");
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        protected override void AddGeographicFilter(Package package)
        {
            this.AddDIYRegion_Intersect(package);
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override bool isValidStatImg(double lng, double lat)
        {
            MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(lng, lat);
            return condition.Geometorys.GeoOp.ContainsRectCenter(grid.Bounds);
        }
        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            return getTriadIDIgnoreServiceType(new string[] { this.size, this.time });
        }
        #region 全局变量
        public Dictionary<string, Dictionary<string, MapOperation2>> mutRegionMopDic { get; set; }
        ZTDiyQueryLteEdgeSpeedAnaSetForm ztLteEdgeSpeedAnaForm = null;
        public Dictionary<string,Dictionary<string, List<float>>> cityGridTypeNameEdgeSpeedDic { get; set; }
        public List<LteEdgeSpeedInfo> lteEdgeSpeedInfoList { get; set; }
        string strCityName = "";
        protected LteEdgeSpeedCondtion curCondition = new LteEdgeSpeedCondtion();
        protected string size;
        protected string time;
        protected LteEdgeSpeedAnaHelper helper = new LteEdgeSpeedAnaHelper();
        #endregion

        public string strContainPoint(double x, double y)
        {
            string gridTypeGrid = "";
            foreach (string gridType in mutRegionMopDic.Keys)
            {
                foreach (string grid in mutRegionMopDic[gridType].Keys)
                {
                    if (mutRegionMopDic[gridType][grid].CheckPointInRegion(x, y))
                    {
                        gridTypeGrid = gridType + "," + grid;
                        break;
                    }
                }
            }
            return gridTypeGrid;
        }

        protected override bool getConditionBeforeQuery()
        {
            base.getConditionBeforeQuery();
            if (ztLteEdgeSpeedAnaForm == null)
            {
                ztLteEdgeSpeedAnaForm = new ZTDiyQueryLteEdgeSpeedAnaSetForm();
            }
            if (ztLteEdgeSpeedAnaForm.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            curCondition.EdgeSpeedThreshold = ztLteEdgeSpeedAnaForm.FSpeedRate;
            return true;
        }

        protected override void query()
        {
            if (!getConditionBeforeQuery())
            {
                return;
            }
            mutRegionMopDic = new Dictionary<string, Dictionary<string, MapOperation2>>();
            cityGridTypeNameEdgeSpeedDic = new Dictionary<string, Dictionary<string, List<float>>>();
            lteEdgeSpeedInfoList = new List<LteEdgeSpeedInfo>();
            mutRegionMopDic = EdgeSpeedAnaHelper.InitRegionMop2();
            foreach (int districtID in condition.DistrictIDs)
            {
                strCityName = DistrictManager.GetInstance().getDistrictName(districtID);
                queryDistrictData(districtID);
                changGridDataFormula();
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
            }
            afterRecieveAllData();
            fireShowResult();
        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                MainModel.CurGridCoverData = null;
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                MainModel.MainForm.GetMapForm().GetGridShowLayer();
                WaitBox.Text = "开始统计查询栅格数据...";
                WaitBox.CanCancel = true;
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();

                string statImgIDSet = this.getStatImgNeededTriadID();// "10,1,1082"
                if (condition.IsByRound)
                {
                    queryPeriodInfo(null, clientProxy, statImgIDSet, MainModel.CurGridColorUnitMatrix);
                }
                else
                {
                    foreach (TimePeriod period in condition.Periods)
                    {
                        queryPeriodInfo(period, clientProxy, statImgIDSet, MainModel.CurGridColorUnitMatrix);
                    }
                }
                WaitBox.Text = "数据获取完毕，进行显示预处理...";
                if (MainModel.CurGridCoverData != null)
                {
                    MainModel.CurGridCoverData.Clear();
                }
            }
            catch (Exception e)
            {
                MessageBox.Show("Error" + e.StackTrace + e.ToString());
            }
            finally
            {
                WaitBox.Close();
            }
            MapGridLayer.NeedFreshFullImg = true;
        }

        private void changGridDataFormula()
        {
            float fDownLoadSize = 0;
            float fDownLoadTime = 0;
            float fEdgeSpeed = 0;
            foreach (ColorUnit cu in MainModel.CurGridColorUnitMatrix)
            {
                fDownLoadSize = (float)cu.DataHub.CalcValueByFormula(this.size);
                fDownLoadTime = (float)cu.DataHub.CalcValueByFormula(this.time);
                fEdgeSpeed = fDownLoadSize / fDownLoadTime / 1024 / 1024 * 8000;
                if (fDownLoadTime > 0)
                {
                    helper.AddValidData(cu.LTLng, cu.LTLat, fEdgeSpeed, strCityName, mutRegionMopDic, cityGridTypeNameEdgeSpeedDic);
                }
            }
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            List<EdgeSpeedInfo> infoList = helper.GetResultAfterAllQuery(cityGridTypeNameEdgeSpeedDic, curCondition.EdgeSpeedThreshold);
            foreach (var info in infoList)
            {
                lteEdgeSpeedInfoList.Add(info as LteEdgeSpeedInfo);
            }
        }

        protected override void fireShowResult()
        {
            ZTDiyQueryLteEdgeSpeedAnaDataForm showForm = null;
            object obj = MainModel.GetObjectFromBlackboard(typeof(ZTDiyQueryLteEdgeSpeedAnaDataForm));
            showForm = obj as ZTDiyQueryLteEdgeSpeedAnaDataForm;
            if (showForm == null || showForm.IsDisposed)
            {
                showForm = new ZTDiyQueryLteEdgeSpeedAnaDataForm(MainModel, "有效栅格数");
            }
            showForm.FillData(lteEdgeSpeedInfoList);
            showForm.Show(MainModel.MainForm);
        }  
    }

    public class ZTDiyQueryLteEdgeSpeedAnaByGrid_FDD : ZTDiyQueryLteEdgeSpeedAnaByGrid
    {
        public ZTDiyQueryLteEdgeSpeedAnaByGrid_FDD(MainModel mainModel)
            : base(mainModel)
        {
            this.mainModel = mainModel;
            this.size = "Lf_052D64020101";
            this.time = "Lf_052D64020102";
        }
        public override string Name
        {
            get { return "边缘下载速率分析_LTE_FDD(按栅格)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {

            return new MasterCom.RAMS.UserMng.LogInfoItem(3, 26000, 26060, "查询");
        }
    }
}
