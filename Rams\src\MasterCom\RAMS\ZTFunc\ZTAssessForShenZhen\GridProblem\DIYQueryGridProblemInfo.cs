﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util;

namespace MasterCom.RAMS.Net
{
    public class DIYQueryGridProblemInfo : QueryBase
    {
        /// <summary>
        /// 栅格数据类型，0：GSM，1：TD
        /// </summary>
        public int dataType { get; set; }
        public int year { get; set; }
        public int batch { get; set; }
        public int netGridID { get; set; }
        public List<GridKPIMonth> GridKPIList { get; set; } = new List<GridKPIMonth>();
        private DIYQueryGridProblemInfo(MainModel mainModel)
            : base(mainModel)
        {

        }

        private static DIYQueryGridProblemInfo queryGridProblemInfo = null;

        public static DIYQueryGridProblemInfo GetInstance()
        {
            if (queryGridProblemInfo == null)
            {
                queryGridProblemInfo = new DIYQueryGridProblemInfo(MainModel.GetInstance());
            }
            return queryGridProblemInfo;
        }

        public override string Name
        {
            get { return "栅格问题点详情"; }
        }

        public override string IconName
        {
            get { return ""; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18002, this.Name);
        }

        protected override bool isValidCondition()
        {
            return true;
        }
        protected override void query()
        {
            queryInThread();
        }

        private void queryInThread()
        {
            try
            {
                if (dataType == 0)
                {
                    string sqlGridMonthKPI = "select year, batch, netGridID, isPoor, rxqualTotal, rxqual0_4Pct, rxqual5Pct," +
                    "rxqual6_7Pct, mosTotal, mos2d8Pct from tb_sz_netgrid_gsm_batch_result where netGridID = " + netGridID +
                    " and (year > " + year + " or (year = " + year + " and batch >= " + batch + "))" +
                    " order by year desc, batch desc";
                    DIYSQLGridKPIMonth_GSM gridKPIQuery = new DIYSQLGridKPIMonth_GSM(MainModel, sqlGridMonthKPI);
                    gridKPIQuery.Query();
                    GridKPIList = gridKPIQuery.GridKPIList;
                }
                else if (dataType == 1)
                {
                    string sqlGridMonthKPI = "select year, batch, netGridID, isPoor, blerTotal, bler5Pct, blerAvg " +
                    " from tb_sz_netgrid_td_batch_result where netGridID = " + netGridID +
                    " and (year > " + year + " or (year = " + year + " and batch >= " + batch + "))" +
                    " order by year desc, batch desc";
                    DIYSQLGridKPIMonth_TD gridKPIQuery = new DIYSQLGridKPIMonth_TD(MainModel, sqlGridMonthKPI);
                    gridKPIQuery.Query();
                    GridKPIList = gridKPIQuery.GridKPIList;
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                //WaitBox.Close()
            }
        }

        private class DIYSQLGridKPIMonth_GSM : DIYSQLBase
        {
            public List<GridKPIMonth> GridKPIList = new List<GridKPIMonth>();
            public string SQL { get; set; }
            public DIYSQLGridKPIMonth_GSM(MainModel mainModel, string sql)
                : base(mainModel)
            {
                SQL = sql;
            }

            protected override string getSqlTextString()
            {
                return SQL;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[10];
                rType[0] = E_VType.E_Int;
                rType[1] = E_VType.E_Int;
                rType[2] = E_VType.E_Int;
                rType[3] = E_VType.E_Int;
                rType[4] = E_VType.E_Int;
                rType[5] = E_VType.E_Float;
                rType[6] = E_VType.E_Float;
                rType[7] = E_VType.E_Float;
                rType[8] = E_VType.E_Int;
                rType[9] = E_VType.E_Float;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                GridKPIList.Clear();
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        GridKPIMonthGSM gridKPI = new GridKPIMonthGSM();
                        gridKPI.Fill(package.Content);
                        GridKPIList.Add(gridKPI);
                        //do your code here
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                    {
                        progress++;
                        if (progress > 95)
                        {
                            progress = 5;
                            index = 0;
                        }
                        if (progress % 5 == 0)
                        {
                            WaitBox.ProgressPercent = progress;
                        }
                    }
                }
            }

            public override string Name
            {
                get { return "DIYSQLGridKPIMonth_GSM"; }
            }
        }

        private class DIYSQLGridKPIMonth_TD : DIYSQLBase
        {
            public List<GridKPIMonth> GridKPIList = new List<GridKPIMonth>();
            public string SQL { get; set; }
            public DIYSQLGridKPIMonth_TD(MainModel mainModel, string sql)
                : base(mainModel)
            {
                SQL = sql;
            }

            protected override string getSqlTextString()
            {
                return SQL;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[7];
                rType[0] = E_VType.E_Int;
                rType[1] = E_VType.E_Int;
                rType[2] = E_VType.E_Int;
                rType[3] = E_VType.E_Int;
                rType[4] = E_VType.E_Int;
                rType[5] = E_VType.E_Float;
                rType[6] = E_VType.E_Float;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                GridKPIList.Clear();
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        GridKPIMonthTD gridKPI = new GridKPIMonthTD();
                        gridKPI.Fill(package.Content);
                        GridKPIList.Add(gridKPI);
                        //do your code here
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                    {
                        progress++;
                        if (progress > 95)
                        {
                            progress = 5;
                            index = 0;
                        }
                        if (progress % 5 == 0)
                        {
                            WaitBox.ProgressPercent = progress;
                        }
                    }
                }
            }

            public override string Name
            {
                get { return "DIYSQLGridKPIMonth_TD"; }
            }
        }
    }
}
