﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LowSpeedCellDlg_W
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.chkSaveTestPoint = new DevExpress.XtraEditors.CheckEdit();
            this.label5 = new System.Windows.Forms.Label();
            this.chkFTPUpLoad = new System.Windows.Forms.CheckBox();
            this.numFTPUpLoadMax = new DevExpress.XtraEditors.SpinEdit();
            this.numFTPUpLoadMin = new DevExpress.XtraEditors.SpinEdit();
            this.label1 = new System.Windows.Forms.Label();
            this.chkFTPDownLoad = new System.Windows.Forms.CheckBox();
            this.numFTPDownLoadMax = new DevExpress.XtraEditors.SpinEdit();
            this.numFTPDownLoadMin = new DevExpress.XtraEditors.SpinEdit();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.numMinRscp = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.numMaxRscp = new DevExpress.XtraEditors.SpinEdit();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.numProblemCount = new DevExpress.XtraEditors.SpinEdit();
            this.numProblemRate = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.chkSaveTestPoint.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPUpLoadMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPUpLoadMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPDownLoadMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPDownLoadMin.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMinRscp.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxRscp.Properties)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numProblemCount.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numProblemRate.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(272, 218);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(72, 23);
            this.btnOK.TabIndex = 2;
            this.btnOK.Text = "确定";
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(367, 218);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(72, 23);
            this.btnCancel.TabIndex = 3;
            this.btnCancel.Text = "取消";
            // 
            // chkSaveTestPoint
            // 
            this.chkSaveTestPoint.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.chkSaveTestPoint.Location = new System.Drawing.Point(23, 197);
            this.chkSaveTestPoint.Name = "chkSaveTestPoint";
            this.chkSaveTestPoint.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkSaveTestPoint.Properties.Appearance.Options.UseFont = true;
            this.chkSaveTestPoint.Properties.Caption = "保留采样点";
            this.chkSaveTestPoint.Size = new System.Drawing.Size(94, 19);
            this.chkSaveTestPoint.TabIndex = 10;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(125, 62);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(119, 12);
            this.label5.TabIndex = 27;
            this.label5.Text = "≤  Upload  Rate ≤";
            // 
            // chkFTPUpLoad
            // 
            this.chkFTPUpLoad.AutoSize = true;
            this.chkFTPUpLoad.Checked = true;
            this.chkFTPUpLoad.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkFTPUpLoad.Location = new System.Drawing.Point(18, 58);
            this.chkFTPUpLoad.Name = "chkFTPUpLoad";
            this.chkFTPUpLoad.Size = new System.Drawing.Size(15, 14);
            this.chkFTPUpLoad.TabIndex = 26;
            this.chkFTPUpLoad.UseVisualStyleBackColor = true;
            // 
            // numFTPUpLoadMax
            // 
            this.numFTPUpLoadMax.EditValue = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numFTPUpLoadMax.Location = new System.Drawing.Point(259, 57);
            this.numFTPUpLoadMax.Name = "numFTPUpLoadMax";
            this.numFTPUpLoadMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numFTPUpLoadMax.Properties.IsFloatValue = false;
            this.numFTPUpLoadMax.Properties.Mask.EditMask = "N00";
            this.numFTPUpLoadMax.Size = new System.Drawing.Size(73, 21);
            this.numFTPUpLoadMax.TabIndex = 25;
            // 
            // numFTPUpLoadMin
            // 
            this.numFTPUpLoadMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numFTPUpLoadMin.Location = new System.Drawing.Point(39, 55);
            this.numFTPUpLoadMin.Name = "numFTPUpLoadMin";
            this.numFTPUpLoadMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numFTPUpLoadMin.Properties.IsFloatValue = false;
            this.numFTPUpLoadMin.Properties.Mask.EditMask = "N00";
            this.numFTPUpLoadMin.Size = new System.Drawing.Size(73, 21);
            this.numFTPUpLoadMin.TabIndex = 24;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(125, 27);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(119, 12);
            this.label1.TabIndex = 23;
            this.label1.Text = "≤ Download Rate ≤";
            // 
            // chkFTPDownLoad
            // 
            this.chkFTPDownLoad.AutoSize = true;
            this.chkFTPDownLoad.Checked = true;
            this.chkFTPDownLoad.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkFTPDownLoad.Location = new System.Drawing.Point(18, 24);
            this.chkFTPDownLoad.Name = "chkFTPDownLoad";
            this.chkFTPDownLoad.Size = new System.Drawing.Size(15, 14);
            this.chkFTPDownLoad.TabIndex = 22;
            this.chkFTPDownLoad.UseVisualStyleBackColor = true;
            // 
            // numFTPDownLoadMax
            // 
            this.numFTPDownLoadMax.EditValue = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numFTPDownLoadMax.Location = new System.Drawing.Point(259, 22);
            this.numFTPDownLoadMax.Name = "numFTPDownLoadMax";
            this.numFTPDownLoadMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numFTPDownLoadMax.Properties.IsFloatValue = false;
            this.numFTPDownLoadMax.Properties.Mask.EditMask = "N00";
            this.numFTPDownLoadMax.Size = new System.Drawing.Size(73, 21);
            this.numFTPDownLoadMax.TabIndex = 21;
            // 
            // numFTPDownLoadMin
            // 
            this.numFTPDownLoadMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numFTPDownLoadMin.Location = new System.Drawing.Point(39, 21);
            this.numFTPDownLoadMin.Name = "numFTPDownLoadMin";
            this.numFTPDownLoadMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numFTPDownLoadMin.Properties.IsFloatValue = false;
            this.numFTPDownLoadMin.Properties.Mask.EditMask = "N00";
            this.numFTPDownLoadMin.Size = new System.Drawing.Size(73, 21);
            this.numFTPDownLoadMin.TabIndex = 20;
            // 
            // groupBox1
            // 
            this.groupBox1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox1.Controls.Add(this.labelControl2);
            this.groupBox1.Controls.Add(this.labelControl4);
            this.groupBox1.Controls.Add(this.numMinRscp);
            this.groupBox1.Controls.Add(this.labelControl1);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.labelControl3);
            this.groupBox1.Controls.Add(this.label5);
            this.groupBox1.Controls.Add(this.numMaxRscp);
            this.groupBox1.Controls.Add(this.numFTPDownLoadMin);
            this.groupBox1.Controls.Add(this.chkFTPUpLoad);
            this.groupBox1.Controls.Add(this.numFTPDownLoadMax);
            this.groupBox1.Controls.Add(this.numFTPUpLoadMax);
            this.groupBox1.Controls.Add(this.chkFTPDownLoad);
            this.groupBox1.Controls.Add(this.numFTPUpLoadMin);
            this.groupBox1.Location = new System.Drawing.Point(12, 10);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(425, 119);
            this.groupBox1.TabIndex = 28;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "采样点条件";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(338, 27);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(24, 12);
            this.labelControl2.TabIndex = 34;
            this.labelControl2.Text = "Kbps";
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(338, 62);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(24, 12);
            this.labelControl4.TabIndex = 33;
            this.labelControl4.Text = "Kbps";
            // 
            // numMinRscp
            // 
            this.numMinRscp.EditValue = new decimal(new int[] {
            125,
            0,
            0,
            -2147483648});
            this.numMinRscp.Location = new System.Drawing.Point(39, 88);
            this.numMinRscp.Name = "numMinRscp";
            this.numMinRscp.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numMinRscp.Properties.Appearance.Options.UseFont = true;
            this.numMinRscp.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMinRscp.Properties.IsFloatValue = false;
            this.numMinRscp.Properties.Mask.EditMask = "N00";
            this.numMinRscp.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numMinRscp.Properties.MinValue = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numMinRscp.Size = new System.Drawing.Size(73, 21);
            this.numMinRscp.TabIndex = 29;
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(148, 95);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(60, 12);
            this.labelControl1.TabIndex = 31;
            this.labelControl1.Text = "≤ RSCP ≤";
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(338, 95);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(18, 12);
            this.labelControl3.TabIndex = 32;
            this.labelControl3.Text = "dBm";
            // 
            // numMaxRscp
            // 
            this.numMaxRscp.EditValue = new decimal(new int[] {
            25,
            0,
            0,
            -2147483648});
            this.numMaxRscp.Location = new System.Drawing.Point(259, 90);
            this.numMaxRscp.Name = "numMaxRscp";
            this.numMaxRscp.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numMaxRscp.Properties.Appearance.Options.UseFont = true;
            this.numMaxRscp.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMaxRscp.Properties.IsFloatValue = false;
            this.numMaxRscp.Properties.Mask.EditMask = "N00";
            this.numMaxRscp.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numMaxRscp.Properties.MinValue = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numMaxRscp.Size = new System.Drawing.Size(73, 21);
            this.numMaxRscp.TabIndex = 30;
            // 
            // groupBox2
            // 
            this.groupBox2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox2.Controls.Add(this.labelControl9);
            this.groupBox2.Controls.Add(this.labelControl7);
            this.groupBox2.Controls.Add(this.numProblemCount);
            this.groupBox2.Controls.Add(this.numProblemRate);
            this.groupBox2.Controls.Add(this.labelControl8);
            this.groupBox2.Location = new System.Drawing.Point(12, 135);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(425, 53);
            this.groupBox2.TabIndex = 29;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "过滤条件(且关系)";
            // 
            // labelControl9
            // 
            this.labelControl9.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControl9.Appearance.Options.UseFont = true;
            this.labelControl9.Location = new System.Drawing.Point(383, 25);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(6, 12);
            this.labelControl9.TabIndex = 39;
            this.labelControl9.Text = "%";
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControl7.Appearance.Options.UseFont = true;
            this.labelControl7.Location = new System.Drawing.Point(33, 25);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(72, 12);
            this.labelControl7.TabIndex = 0;
            this.labelControl7.Text = "问题点个数≥";
            // 
            // numProblemCount
            // 
            this.numProblemCount.EditValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numProblemCount.Location = new System.Drawing.Point(114, 22);
            this.numProblemCount.Name = "numProblemCount";
            this.numProblemCount.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numProblemCount.Properties.Appearance.Options.UseFont = true;
            this.numProblemCount.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numProblemCount.Properties.IsFloatValue = false;
            this.numProblemCount.Properties.Mask.EditMask = "N00";
            this.numProblemCount.Properties.MaxValue = new decimal(new int[] {
            500000,
            0,
            0,
            0});
            this.numProblemCount.Size = new System.Drawing.Size(73, 20);
            this.numProblemCount.TabIndex = 0;
            // 
            // numProblemRate
            // 
            this.numProblemRate.EditValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numProblemRate.Location = new System.Drawing.Point(308, 22);
            this.numProblemRate.Name = "numProblemRate";
            this.numProblemRate.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numProblemRate.Properties.Appearance.Options.UseFont = true;
            this.numProblemRate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numProblemRate.Properties.IsFloatValue = false;
            this.numProblemRate.Properties.Mask.EditMask = "N00";
            this.numProblemRate.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numProblemRate.Size = new System.Drawing.Size(73, 20);
            this.numProblemRate.TabIndex = 1;
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControl8.Appearance.Options.UseFont = true;
            this.labelControl8.Location = new System.Drawing.Point(228, 25);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(72, 12);
            this.labelControl8.TabIndex = 2;
            this.labelControl8.Text = "问题点占比≥";
            // 
            // LowSpeedCellDlg_W
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(451, 253);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.chkSaveTestPoint);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Name = "LowSpeedCellDlg_W";
            this.Text = "低速率小区条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.chkSaveTestPoint.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPUpLoadMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPUpLoadMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPDownLoadMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPDownLoadMin.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMinRscp.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxRscp.Properties)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numProblemCount.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numProblemRate.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.CheckEdit chkSaveTestPoint;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.CheckBox chkFTPUpLoad;
        private DevExpress.XtraEditors.SpinEdit numFTPUpLoadMax;
        private DevExpress.XtraEditors.SpinEdit numFTPUpLoadMin;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.CheckBox chkFTPDownLoad;
        private DevExpress.XtraEditors.SpinEdit numFTPDownLoadMax;
        private DevExpress.XtraEditors.SpinEdit numFTPDownLoadMin;
        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraEditors.SpinEdit numMinRscp;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.SpinEdit numMaxRscp;
        private System.Windows.Forms.GroupBox groupBox2;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.SpinEdit numProblemCount;
        private DevExpress.XtraEditors.SpinEdit numProblemRate;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl2;
    }
}