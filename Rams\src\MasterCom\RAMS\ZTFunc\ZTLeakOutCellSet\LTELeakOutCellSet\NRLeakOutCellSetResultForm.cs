﻿using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Grid;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util;
using System;
using System.Collections.Generic;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRLeakOutCellSetResultForm : MinCloseForm
    {
        public NRLeakOutCellSetResultForm() : base()
        {
            InitializeComponent();

            gridView1.DoubleClick += GridView_DoubleClick;
            gridView2.DoubleClick += GridView_DoubleClick;
            gridView3.DoubleClick += NRLteGridView_DoubleClick;
            gridView4.DoubleClick += NRLteGridView_DoubleClick;
        }

        /// <summary>
        /// NR双击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        public void GridView_DoubleClick(object sender, EventArgs e)
        {
            MainModel.FireSetDefaultMapSerialTheme("NR:SS_RSRP");
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            NRLeakOutCell leakOutCell = gv.GetRow(gv.GetSelectedRows()[0]) as NRLeakOutCell;

            MainModel.DTDataManager.Clear();
            foreach (TestPoint tp in leakOutCell.TestPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }

            MainModel.SetSelectedNRCell(leakOutCell.NRCell);
            MainModel.FireDTDataChanged(this);
            MainModel.FireSelectedCellChanged(this);
            MainModel.MainForm.GetMapForm().GoToView(leakOutCell.NRCell.Longitude, leakOutCell.NRCell.Latitude);
        }

        /// <summary>
        /// NR Lte双击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        public void NRLteGridView_DoubleClick(object sender, EventArgs e)
        {
            MainModel.FireSetDefaultMapSerialTheme("NR_lte_RSRP");
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            LteLeakOutCell leakOutCell = gv.GetRow(gv.GetSelectedRows()[0]) as LteLeakOutCell;

            MainModel.DTDataManager.Clear();
            foreach (TestPoint tp in leakOutCell.TestPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }

            MainModel.SelectedLTECell = leakOutCell.LteCell;
            MainModel.FireDTDataChanged(this);
            MainModel.FireSelectedCellChanged(this);
            MainModel.MainForm.GetMapForm().GoToView(leakOutCell.LteCell.Longitude, leakOutCell.LteCell.Latitude);
        }

        //数据填充   update by youq 新添加 2020/8/4  5G NR室分外泄
        public void FillData(List<NRLeakOutIndoorCell> indoorCells, List<LteLeakOutIndoorCell> LteIndoorCells)
        {
            //加载NR数据
            gridControl1.DataSource = indoorCells;
            gridControl1.RefreshDataSource();

            //加载NR Lte数据
            gridControl2.DataSource = LteIndoorCells;
            gridControl2.RefreshDataSource();

            //显示NR采样点数据
            showAllLNRTestPoints();
        }

        #region 导出
        /// <summary>
        /// NR导出
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void miExportSimpleExcel_Click(object sender, EventArgs e)
        {
            exportExcel(this.gridView1, this.gridView2);
        }

        private void miExportNRLteSimpleExcel_Click(object sender, EventArgs e)
        {
            exportExcel(this.gridView4, this.gridView3);
        }

        private void exportExcel(GridView MainGV, GridView ChildGV)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            foreach (GridColumn col in MainGV.Columns)
            {
                row.AddCellValue(col.Caption);
            }
            foreach (GridColumn col in ChildGV.Columns)
            {
                row.AddCellValue(col.Caption);
            }
            rows.Add(row);
            for (int i = 0; i < MainGV.RowCount; i++)
            {
                row = new NPOIRow();
                rows.Add(row);
                foreach (GridColumn col in MainGV.Columns)
                {
                    row.AddCellValue(MainGV.GetRowCellDisplayText(i, col));
                }
                MainGV.ExpandMasterRow(i);
                DevExpress.XtraGrid.Views.Grid.GridView view = MainGV.GetDetailView(i, 0) as DevExpress.XtraGrid.Views.Grid.GridView;
                if (view != null)
                {
                    for (int j = 0; j < view.RowCount; j++)
                    {
                        NPOIRow subRow = new NPOIRow();
                        row.AddSubRow(subRow);
                        foreach (GridColumn subCol in view.Columns)
                        {
                            subRow.AddCellValue(view.GetRowCellDisplayText(j, subCol));
                        }
                    }
                }
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }
        #endregion

        #region 显示全部采样点
        /// <summary>
        /// NR Lte显示全部采样点
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void MI_ShowAllNRLteTestPoints_Click(object sender, EventArgs e)
        {
            showAllLLteTestPoints();
        }

        /// <summary>
        /// NR 显示全部采样点
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void MI_ShowAllNRTestPoints_Click(object sender, EventArgs e)
        {
            showAllLNRTestPoints();
        }

        private void showAllLNRTestPoints()
        {
            List<NRLeakOutIndoorCell> indoorCells = gridControl1.DataSource as List<NRLeakOutIndoorCell>;

            if (indoorCells == null)
                return;

            MainModel.DTDataManager.Clear();
            MainModel.SelectedNRCells.Clear();
            foreach (NRLeakOutCell cell in indoorCells)
            {
                MainModel.SelectedNRCells.Add(cell.NRCell);
                foreach (TestPoint tp in cell.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            MainModel.FireSetDefaultMapSerialTheme("NR:SS_RSRP");
            MainModel.FireDTDataChanged(this);
            MainModel.FireSelectedCellChanged(this);
        }

        private void showAllLLteTestPoints()
        {
            List<LteLeakOutIndoorCell> indoorCells = gridControl2.DataSource as List<LteLeakOutIndoorCell>;

            if (indoorCells == null)
                return;

            MainModel.DTDataManager.Clear();
            MainModel.SelectedLTECells.Clear();
            foreach (LteLeakOutCell cell in indoorCells)
            {
                MainModel.SelectedLTECells.Add(cell.LteCell);
                foreach (TestPoint tp in cell.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            MainModel.FireSetDefaultMapSerialTheme("NR_lte_RSRP");
            MainModel.FireDTDataChanged(this);
            MainModel.FireSelectedCellChanged(this);
        }

        #endregion
    }
}
