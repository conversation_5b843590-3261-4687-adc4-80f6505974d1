﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System.Windows.Forms;
using MasterCom.RAMS.Util;
using MasterCom.Util;
using MasterCom.RAMS.ZTFunc.ZTFileCompare;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CityFileStateForm : MinCloseForm
    {
        private MainModel mainModel;
        List<FileState> existList;
        List<FileState> notExistList;
        public CityFileStateForm(MainModel mm)
            : base(mm)
        {
            mainModel = mm;
            existList = new List<FileState>();
            notExistList = new List<FileState>();
            DisposeWhenClose = true;
            InitializeComponent();
            listViewDateCityInfo.ListViewItemSorter = new ListViewSorter(listViewDateCityInfo);
        }
        public void SetTime(DateTime bTime, DateTime eTime, int fileCount, int notExistCount)
        {
            Text = "集团文件推送情况（从" + bTime.ToString("yyyy-MM-dd HH:mm:ss") + "到" + eTime.ToString("yyyy-MM-dd HH:mm:ss") + "）";
            dateEditDateStart.EditValue = bTime;
            dateEditDateEnd.EditValue = eTime;
            //textEditNotExistPercent.Text = fileCount == 0 ? "0" : Math.Round(notExistCount * 100.0 / fileCount, 4).ToString();
            spinEditNotExistPercent.Value = 0;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="dateFileDic"></param>
        /// <param name="plans">北京测试计划文件推送情况，无开关TestPlanFileCompare时应该为null</param>
        public void FillData(Dictionary<string, Dictionary<string, Dictionary<string, CityFileCompare>>> dateFileDic
            ,List<TestPlan_Beijing> plans)
        {
            pageTestPlan.PageVisible = plans != null;
            gridTestPlan.DataSource = plans;
            gridTestPlan.RefreshDataSource();
            viewTestPlan.BestFitColumns();

            existList.Clear();
            notExistList.Clear();
            listViewDateCityInfo.Items.Clear();
            bool haveNone = true;
            List<string> keyList = new List<string>(dateFileDic.Keys);
            keyList.Sort();
            keyList.Reverse();
            foreach (string date in keyList)
            {
                foreach (string city in dateFileDic[date].Keys)
                {
                    foreach (string type in dateFileDic[date][city].Keys)
                    {
                        existList.AddRange(dateFileDic[date][city][type].FileExist);
                        notExistList.AddRange(dateFileDic[date][city][type].FileNotExist);
                        if (dateFileDic[date][city][type].NotExistPercent < ((double)spinEditNotExistPercent.Value / 100.0))
                        {
                            continue;
                        }
                        haveNone = false;
                        string[] arrayCityInfo = new string[8];

                        arrayCityInfo[0] = date;
                        arrayCityInfo[1] = city;
                        arrayCityInfo[2] = type;
                        arrayCityInfo[3] = dateFileDic[date][city][type].FileCount.ToString();
                        arrayCityInfo[4] = dateFileDic[date][city][type].ExistCount.ToString();
                        arrayCityInfo[5] = dateFileDic[date][city][type].ExistPercent;
                        arrayCityInfo[6] = dateFileDic[date][city][type].NotExistCount.ToString();
                        arrayCityInfo[7] = dateFileDic[date][city][type].NotExistPercent.ToString("p2");
                        ListViewItem lvi = new ListViewItem(arrayCityInfo);
                        lvi.Tag = dateFileDic[date][city][type];
                        listViewDateCityInfo.Items.Add(lvi);
                    }
                }
            }
            if (haveNone)
            {
                labelControlInfo.Visible = true;
            }
            else
            {
                labelControlInfo.Visible = false;
            }
        }

        private void exportBasic()
        {
            List<List<NPOIRow>> rowsList = new List<List<NPOIRow>>();
            List<string> sheetNames = new List<string>();
            try
            {
                addDateCityInfo(rowsList, sheetNames);
                addExistFileInfo(rowsList, sheetNames);
                addNotExist(rowsList, sheetNames);
                ExcelNPOIManager.ExportToExcel(rowsList, sheetNames);
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show("导出Excel失败!" + ex.Message);
            }
        }

        private void addDateCityInfo(List<List<NPOIRow>> rowsList, List<string> sheetNames)
        {
            if (listViewDateCityInfo.Items.Count > 0)
            {
                sheetNames.Add("推送详情");
                List<NPOIRow> rows = new List<NPOIRow>();
                NPOIRow row = new NPOIRow();
                for (int i = 0; i < listViewDateCityInfo.Columns.Count; i++)
                {
                    row.AddCellValue(listViewDateCityInfo.Columns[i].Text);
                }
                rows.Add(row);
                foreach (ListViewItem lvi in listViewDateCityInfo.Items)
                {
                    row = new NPOIRow();
                    CityFileCompare cFile = lvi.Tag as CityFileCompare;
                    row.AddCellValue(cFile.DateString);
                    row.AddCellValue(cFile.City);
                    row.AddCellValue(cFile.NetType);
                    row.AddCellValue(cFile.FileCount);
                    row.AddCellValue(cFile.ExistCount);
                    row.AddCellValue(cFile.ExistPercent);
                    row.AddCellValue(cFile.NotExistCount);
                    row.AddCellValue(cFile.NotExistPercent.ToString("p2"));
                    rows.Add(row);
                }
                rowsList.Add(rows);
            }
        }

        private void addExistFileInfo(List<List<NPOIRow>> rowsList, List<string> sheetNames)
        {
            if (listViewFileInfo.Items.Count > 0)
            {
                sheetNames.Add("已推送文件");
                List<NPOIRow> rows = new List<NPOIRow>();
                NPOIRow row = new NPOIRow();
                for (int i = 0; i < listViewFileInfo.Columns.Count; i++)
                {
                    row.AddCellValue(listViewFileInfo.Columns[i].Text);
                }
                rows.Add(row);
                foreach (FileState file in existList)
                {
                    row = new NPOIRow();
                    row.AddCellValue(file.DateString);
                    row.AddCellValue(file.CityName);
                    row.AddCellValue(file.FileName);
                    row.AddCellValue(file.BeginTimeString);
                    row.AddCellValue(file.EndTimeString);
                    row.AddCellValue(file.Desc);
                    rows.Add(row);
                }
                rowsList.Add(rows);
            }
        }

        private void addNotExist(List<List<NPOIRow>> rowsList, List<string> sheetNames)
        {
            if (listViewNotExist.Items.Count > 0)
            {
                sheetNames.Add("未推送文件");
                List<NPOIRow> rows = new List<NPOIRow>();
                NPOIRow row = new NPOIRow();
                for (int i = 0; i < listViewNotExist.Columns.Count; i++)
                {
                    row.AddCellValue(listViewNotExist.Columns[i].Text);
                }
                rows.Add(row);
                foreach (FileState file in notExistList)
                {
                    row = new NPOIRow();
                    row.AddCellValue(file.DateString);
                    row.AddCellValue(file.CityName);
                    row.AddCellValue(file.FileName);
                    row.AddCellValue(file.BeginTimeString);
                    row.AddCellValue(file.EndTimeString);
                    row.AddCellValue(file.Desc);
                    rows.Add(row);
                }
                rowsList.Add(rows);
            }
        }

        private void exportTestPlan()
        {
            List<List<NPOIRow>> rowsList = new List<List<NPOIRow>>();
            List<string> sheetNames = new List<string>();
            try
            {
                if (viewTestPlan.DataRowCount > 0)
                {
                    sheetNames.Add("测试计划文件推送情况");
                    addRowData(rowsList, viewTestPlan);
                }
                if (viewPlanExist.RowCount > 0)
                {
                    sheetNames.Add("已推送文件");
                    addRowData(rowsList, viewPlanExist);
                }
                if (viewPlanNotExist.RowCount > 0)
                {
                    sheetNames.Add("未推送文件");
                    addRowData(rowsList, viewPlanNotExist);
                }
                ExcelNPOIManager.ExportToExcel(rowsList, sheetNames);
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show("导出Excel失败!" + ex.Message);
            }
        }

        private void addRowData(List<List<NPOIRow>> rowsList, GridView gv)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            foreach (GridColumn col in gv.Columns)
            {
                if (col.Visible)
                {
                    row.AddCellValue(col.Caption);
                }
            }
            rows.Add(row);
            for (int r = 0; r < gv.RowCount; r++)
            {
                row = new NPOIRow();
                for (int c = 0; c < gv.Columns.Count; c++)
                {
                    if (gv.Columns[c].Visible)
                    {
                        object value = gv.GetRowCellValue(r, gv.Columns[c]);
                        row.AddCellValue(value == null ? string.Empty : value.ToString());
                    }
                }
                rows.Add(row);
            }
            rowsList.Add(rows);
        }

        private void miExp2Xls_Click(object sender, EventArgs e)
        {
            if (tabCtrlNavi.SelectedTabPageIndex==0)
            {
                exportBasic();
            }
            else
            {
                exportTestPlan();
            }
        }

        private void listViewDateCityInfo_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            listViewFileInfo.Items.Clear();
            listViewNotExist.Items.Clear();
            if (listViewDateCityInfo.SelectedItems.Count > 0 && listViewDateCityInfo.SelectedItems[0].Tag is CityFileCompare)
            {
                CityFileCompare cFile = listViewDateCityInfo.SelectedItems[0].Tag as CityFileCompare;
                foreach (FileState file in cFile.FileExist)
                {
                    string[] arrayCityInfo = new string[6];
                    arrayCityInfo[0] = file.DateString;
                    arrayCityInfo[1] = file.CityName;
                    arrayCityInfo[2] = file.FileName;
                    arrayCityInfo[3] = file.BeginTimeString;
                    arrayCityInfo[4] = file.EndTimeString;
                    arrayCityInfo[5] = file.Desc;
                    ListViewItem lvi = new ListViewItem(arrayCityInfo);
                    lvi.Tag = file;
                    listViewFileInfo.Items.Add(lvi);
                }
                foreach (FileState file in cFile.FileNotExist)
                {
                    string[] arrayCityInfo = new string[6];
                    arrayCityInfo[0] = file.DateString;
                    arrayCityInfo[1] = file.CityName;
                    arrayCityInfo[2] = file.FileName;
                    arrayCityInfo[3] = file.BeginTimeString;
                    arrayCityInfo[4] = file.EndTimeString;
                    arrayCityInfo[5] = file.Desc;
                    ListViewItem lvi = new ListViewItem(arrayCityInfo);
                    lvi.Tag = file;
                    listViewNotExist.Items.Add(lvi);
                }
            }
        }

        private void simpleButtonSearch_Click(object sender, EventArgs e)
        {
            ZTDIYFileCompare query = new ZTDIYFileCompare(mainModel);
            query.bShowResult = false;
            query.DtBegin = (DateTime)dateEditDateStart.EditValue;
            query.DtEnd = (DateTime)dateEditDateEnd.EditValue;
            query.Query();
            Text = "集团文件推送情况（从" + query.DtBegin.ToString("yyyy-MM-dd HH:mm:ss") + "到" + query.DtEnd.ToString("yyyy-MM-dd HH:mm:ss") + "）";
            FillData(query.dateFileDic, query.Plans);
            Visible = true;
            BringToFront();
        }

        private void viewTestPlan_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            GridView view = sender as GridView;
            if (view == null)
            {
                return;
            }
            gridPlanExist.DataSource = null;
            gridPlanNotExist.DataSource = null;

            TestPlan_Beijing plan = view.GetRow(e.FocusedRowHandle) as TestPlan_Beijing;
            if (plan != null)
            {
                gridPlanExist.DataSource = plan.FilesExist;
                gridPlanNotExist.DataSource = plan.FilesNotExist;
            }
            gridPlanExist.RefreshDataSource();
            gridPlanNotExist.RefreshDataSource();
            viewPlanExist.BestFitColumns();
            viewPlanNotExist.BestFitColumns();
        }

    }
}
