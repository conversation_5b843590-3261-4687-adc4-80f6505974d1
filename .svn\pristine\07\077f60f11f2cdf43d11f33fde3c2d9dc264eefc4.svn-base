﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    [Serializable]
    public class MessCoverCause : CauseBase
    {
        public MessCoverCause()
        {
            AddSubReason(new MessCoverSCellOverCover());
            AddSubReason(new MessCoverNCellOverCover());
            AddSubReason(new MessCoverUnreasonableCover());
        }
        public override string Name
        {
            get { return "覆盖杂乱"; }
        }
        public float RSRP { get; set; } = 6;
        public int CellCountMin { get; set; } = 3;
        public override string Desc
        {
            get
            {
                return string.Format("覆盖带{0}dB的小区数量≥{1}个", RSRP, CellCountMin);
            }
        }

        public override string Suggestion
        {
            get
            {
                return null;
            }
        }

        public override void Judge(LowSpeedSeg segItem, List<Event> evts, List<TestPoint> allTP)
        {
            foreach (TestPoint pnt in segItem.TestPoints)
            {
                if (segItem.IsNeedJudge(pnt))
                {
                    List<float> rsrpSet = getRsrpSet(pnt);
                    if (rsrpSet.Count >= CellCountMin)
                    {
                        rsrpSet.Sort();
                        int inCvr = getInCvr(rsrpSet);

                        bool needJudge = judgeSegItem(segItem, pnt, inCvr);
                        if (!needJudge)
                        {
                            return;
                        }
                    }
                }
            }
        }

        private List<float> getRsrpSet(TestPoint pnt)
        {
            List<float> rsrpSet = new List<float>();
            float? rsrp = (float?)GetRSRP(pnt);
            if (rsrp != null)
            {
                rsrpSet.Add((float)rsrp);
            }
            for (int i = 0; i < 6; i++)
            {
                float? nRsrp = (float?)GetNRSRP(pnt, i);
                if (nRsrp != null)
                {
                    rsrpSet.Add((float)nRsrp);
                }
            }

            return rsrpSet;
        }

        private int getInCvr(List<float> rsrpSet)
        {
            int inCvr = 0;
            for (int i = rsrpSet.Count - 1; i >= 0; i--)
            {
                if (rsrpSet[rsrpSet.Count - 1] - rsrpSet[i] <= this.RSRP)
                {
                    inCvr++;
                    if (inCvr >= CellCountMin)
                    {
                        break;
                    }
                }
                else
                {
                    break;
                }
            }

            return inCvr;
        }

        private bool judgeSegItem(LowSpeedSeg segItem, TestPoint pnt, int inCvr)
        {
            if (inCvr >= CellCountMin)
            {
                foreach (CauseBase subReason in SubCauses)
                {
                    if (!segItem.IsNeedJudge(pnt))
                    {
                        break;
                    }
                    subReason.JudgeSinglePoint(segItem, pnt);
                }

                if (segItem.IsNeedJudge(pnt))
                {
                    UnknowReason r = new UnknowReason();
                    r.Parent = this;
                    segItem.SetReason(new LowSpeedPointDetail(pnt, r));
                }
                if (!segItem.NeedJudge)
                {
                    return false;
                }
            }
            return true;
        }

        protected object GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_RSRP"];
            }
            return tp["lte_RSRP"];
        }
        protected object GetNRSRP(TestPoint tp, int index)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_NCell_RSRP", index];
            }
            return tp["lte_NCell_RSRP", index];
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;

                paramDic["rsrp"] = this.RSRP;
                paramDic["cellCnt"] = this.CellCountMin;

                List<object> list = new List<object>();
                foreach (CauseBase cause in SubCauses)
                {
                    list.Add(cause.CfgParam);
                }
                paramDic["SubCauseSet"] = list;

                return paramDic;

            }
            set
            {
                if (value == null)
                {
                    return;
                }
                this.RSRP = (float)value["rsrp"];
                this.CellCountMin = (int)value["cellCnt"];

                SubCauses = new List<CauseBase>();
                List<object> list = value["SubCauseSet"] as List<object>;
                foreach (object item in list)
                {
                    Dictionary<string, object> dic = item as Dictionary<string, object>;
                    string typeName = dic["TypeName"].ToString();
                    System.Reflection.Assembly assembly = System.Reflection.Assembly.GetExecutingAssembly();
                    CauseBase cause = (CauseBase)assembly.CreateInstance(typeName);
                    cause.CfgParam = dic;
                    AddSubReason(cause);
                }
            }
        }
    }

    [Serializable]
    public class MessCoverSCellOverCover : CauseBase
    {
        public override string Name
        {
            get { return "主服小区过覆盖"; }
        }
        public float OverRatio { get; set; } = 1.6f;
        public override string Desc
        {
            get
            {
                return string.Format("主服小区距离采样点≥理想覆盖半径的{0}倍", OverRatio);
            }
        }
        public ICell serverCell { get; set; }
        public override string Suggestion
        {
            get
            {
                return string.Format("调整主服小区{0}的覆盖范围", serverCell != null ? serverCell.Name : "");
            }
        }

        public override void JudgeSinglePoint(LowSpeedSeg segItem, TestPoint testPoint)
        {
            LTECell sCell = testPoint.GetMainLTECell_TdOrFdd();
            if (sCell == null)
            {
                return;
            }
            double sDistance = testPoint.Distance2(sCell.Longitude, sCell.Latitude);
            double radiusOfCell = MasterCom.ES.Data.CfgDataProvider.CalculateRadius(sCell, 3);
            if (sDistance >= radiusOfCell * OverRatio)
            {
                MessCoverSCellOverCover cln = this.Clone() as MessCoverSCellOverCover;
                segItem.SetReason(new LowSpeedPointDetail(testPoint, cln));
            }
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;

                paramDic["overRatio"] = this.OverRatio;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.OverRatio = (float)value["overRatio"];
            }
        }
    }

    [Serializable]
    public class MessCoverNCellOverCover : CauseBase
    {
        public override string Name
        {
            get { return "邻区存在过覆盖"; }
        }
        public float OverRatio { get; set; } = 1.6f;
        public override string Desc
        {
            get
            {
                return string.Format("邻区距离采样点≥理想覆盖半径的{0}倍", OverRatio);
            }
        }
        public ICell nCell { get; set; }
        public override string Suggestion
        {
            get
            {
                return string.Format("调整主服小区{0}的覆盖范围", nCell != null ? nCell.Name : "");
            }
        }

        public override void JudgeSinglePoint(LowSpeedSeg segItem, TestPoint testPoint)
        {
            for (int i = 0; i < 10; i++)
            {
                LTECell cell = testPoint.GetNBLTECell_TdOrFdd(i);
                if (cell == null)
                {
                    break;
                }
                double sDistance = testPoint.Distance2(cell.Longitude, cell.Latitude);
                double radiusOfCell = MasterCom.ES.Data.CfgDataProvider.CalculateRadius(cell, 3);
                if (sDistance >= radiusOfCell * OverRatio)
                {
                    MessCoverNCellOverCover cln = this.Clone() as MessCoverNCellOverCover;
                    segItem.SetReason(new LowSpeedPointDetail(testPoint, cln));
                }
            }
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                paramDic["overRatio"] = this.OverRatio;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.OverRatio = (float)value["overRatio"];
            }
        }
    }

    [Serializable]
    public class MessCoverUnreasonableCover : CauseBase
    {
        public override string Name
        {
            get { return "小区覆盖不合理"; }
        }
        public double DistanceMin { get; set; } = 300;
        public override string Desc
        {
            get
            {
                return string.Format("邻区中距离最远的小区覆盖≥{0}米", DistanceMin);
            }
        }
        public ICell nCell { get; set; }
        public override string Suggestion
        {
            get
            {
                return string.Format("建议将小区{0}的覆盖范围缩小", nCell != null ? nCell.Name : "");
            }
        }

        public override void JudgeSinglePoint(LowSpeedSeg segItem, TestPoint testPoint)
        {
            double farestDis = -1;
            for (int i = 0; i < 10; i++)
            {
                LTECell cell = testPoint.GetNBLTECell_TdOrFdd(i);
                if (cell == null)
                {
                    return;
                }
                double dis = testPoint.Distance2(cell.Longitude, cell.Latitude);
                if (dis > farestDis)
                {
                    farestDis = dis;
                }
            }
            if (farestDis > DistanceMin)
            {
                MessCoverUnreasonableCover cln = this.Clone() as MessCoverUnreasonableCover;
                segItem.SetReason(new LowSpeedPointDetail(testPoint, cln));
            }
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;

                paramDic["distance"] = this.DistanceMin;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                this.DistanceMin = (double)value["distance"];
            }
        }
    }

}
