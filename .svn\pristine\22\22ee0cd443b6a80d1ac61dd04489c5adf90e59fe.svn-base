﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTNotHoTopLevCell
{
    public class NotHoTopLevCellInfo
    {
        public ICell OrgCell
        { get; protected set; }
        public string OrgCellName
        {
            get
            {
                if (OrgCell!=null)
                {
                    return OrgCell.Name;
                }
                else if (OrgCell is TDCell)
                {
                    return (OrgCell as TDCell).Name;
                }
                return string.Empty;
            }
        }
        public int? OrgLac
        {
            get
            {
                if (OrgCell is Cell)
                {
                    return (OrgCell as Cell).LAC;
                }
                else if (OrgCell is TDCell)
                {
                    return (OrgCell as TDCell).LAC;
                }
                return null;
            }
        }
        public int? OrgCi
        {
            get
            {
                if (OrgCell is Cell)
                {
                    return (OrgCell as Cell).CI;
                }
                else if (OrgCell is TDCell)
                {
                    return (OrgCell as TDCell).CI;
                }
                return null;
            }
        }
        public float OrgLev
        { get; protected set; }

        public ICell TarCell
        { get; protected set; }
        public string TarCellName
        {
            get
            {
                if (TarCell != null)
                {
                    return TarCell.Name;
                }
                else if (TarCell is TDCell)
                {
                    return (TarCell as TDCell).Name;
                }
                return string.Empty;
            }
        }
        public int? TarLac
        {
            get
            {
                if (TarCell is Cell)
                {
                    return (TarCell as Cell).LAC;
                }
                else if (TarCell is TDCell)
                {
                    return (TarCell as TDCell).LAC;
                }
                return null;
            }
        }
        public int? TarCi
        {
            get
            {
                if (TarCell is Cell)
                {
                    return (TarCell as Cell).CI;
                }
                else if (TarCell is TDCell)
                {
                    return (TarCell as TDCell).CI;
                }
                return null;
            }
        }
        public double? TarDistance
        {
            get;
            private set;
        }
        public float TarLev
        {
            get;
            private set;
        }
        public ICell TopLevCell
        { get; protected set; }

        public string TopCellName
        {
            get
            {
                if (TopLevCell != null)
                {
                    return TopLevCell.Name;
                }
                else if (TopLevCell is TDCell)
                {
                    return (TopLevCell as TDCell).Name;
                }
                return string.Empty;
            }
        }
        public int? TopLac
        {
            get
            {
                if (TopLevCell is Cell)
                {
                    return (TopLevCell as Cell).LAC;
                }
                else if (TopLevCell is TDCell)
                {
                    return (TopLevCell as TDCell).LAC;
                }
                return null;
            }
        }
        public int? TopCi
        {
            get
            {
                if (TopLevCell is Cell)
                {
                    return (TopLevCell as Cell).CI;
                }
                else if (TopLevCell is TDCell)
                {
                    return (TopLevCell as TDCell).CI;
                }
                return null;
            }
        }
        public double? TopDistance
        {
            get;
            private set;
        }

        public float TopLev
        { get; protected set; }

        public List<TestPoint> TestPoints { get; set; } = new List<TestPoint>();
        public Event HoSuccessEvt { get; set; }
        public string FileName
        {
            get { return TestPoints[0].FileName; }
        }

        public NotHoTopLevCellInfo(ICell orgCell, float orgRxLev
            , ICell tarCell, float tarRxLev
            , ICell topNCell, float topNRxLev)
        {
            this.OrgCell = orgCell;
            this.OrgLev = orgRxLev;
            this.TarCell = tarCell;
            TarDistance = Math.Round(MasterCom.Util.MathFuncs.GetDistance(orgCell.Longitude, orgCell.Latitude
                , tarCell.Longitude, tarCell.Latitude), 2);
            this.TarLev = tarRxLev;
            this.TopLevCell = topNCell;
            TopDistance = Math.Round(MasterCom.Util.MathFuncs.GetDistance(orgCell.Longitude, orgCell.Latitude
                , topNCell.Longitude, topNCell.Latitude), 2);
            this.TopLev = topNRxLev;
        }

    }

}
