﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using MasterCom.Util.GDI;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    class NR700MAcpGoodPointFtpDownload : NR700MStationAcceptThroughput
    {
        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("下载") && fileInfo.Name.Contains("好点");
        }

        protected override void addData(TestPoint tp, NR700MCellInfo cell, NR700MStationAcceptCondition condition)
        {
            curCellServiceInfo.GoodSampleDL.Add(tp, true);
        }

        protected override void verifyThroughput(NR700MCellInfo cell, NR700MStationAcceptCondition condition,  ThroughputStandard standard)
        {
            curCellServiceInfo.GoodSampleDL.Calculate();
            dealValidGoodThroughput(curCellServiceInfo.GoodSampleDL, standard.DownThroughput);
        }
    }

    class NR700MAcpBadPointFtpDownload : NR700MStationAcceptThroughput
    {
        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("下载") && fileInfo.Name.Contains("差点");
        }

        protected override void addData(TestPoint tp, NR700MCellInfo cell, NR700MStationAcceptCondition condition)
        {
            curCellServiceInfo.BadSampleDL.Add(tp, true);
        }

        protected override void verifyThroughput(NR700MCellInfo cell, NR700MStationAcceptCondition condition, ThroughputStandard standard)
        {
            curCellServiceInfo.BadSampleDL.Calculate();
            dealValidBadThroughput(curCellServiceInfo.BadSampleDL, standard.DownThroughput);
        }
    }

    class NR700MAcpGoodPointFtpUpload : NR700MStationAcceptThroughput
    {
        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("上传") && fileInfo.Name.Contains("好点");
        }

        protected override void addData(TestPoint tp, NR700MCellInfo cell, NR700MStationAcceptCondition condition)
        {
            curCellServiceInfo.GoodSampleUL.Add(tp, false);
        }

        protected override void verifyThroughput(NR700MCellInfo cell, NR700MStationAcceptCondition condition, ThroughputStandard standard)
        {
            curCellServiceInfo.GoodSampleUL.Calculate();
            dealValidGoodThroughput(curCellServiceInfo.GoodSampleUL, standard.UpThroughput);
        }
    }

    class NR700MAcpBadPointFtpUpload : NR700MStationAcceptThroughput
    {
        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("上传") && fileInfo.Name.Contains("差点");
        }

        protected override void addData(TestPoint tp, NR700MCellInfo cell, NR700MStationAcceptCondition condition)
        {
            curCellServiceInfo.BadSampleUL.Add(tp, false);
        }

        protected override void verifyThroughput(NR700MCellInfo cell, NR700MStationAcceptCondition condition, ThroughputStandard standard)
        {
            curCellServiceInfo.BadSampleUL.Calculate();
            dealValidBadThroughput(curCellServiceInfo.BadSampleUL, standard.UpThroughput);
        }
    }

    class NR700MAcpAccRate : NR700MStationAcceptEvent
    {
        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("接入") || fileInfo.Name.Contains("附着");
        }

        protected override SuccessRateKpiInfo initEventKpiInfo(FileInfo fileInfo, NR700MCellInfo cell
            , NR700MStationAcceptCondition condition)
        {
            if (condition.NRServiceType == NRServiceName.SA)
            {
                evtRequList = new List<int> { (int)NREventManager.NR_Registration_Request };
                evtSuccList = new List<int> { (int)NREventManager.NR_Registration_Accept };
            }
            else if (condition.NRServiceType == NRServiceName.NSA)
            {
                evtRequList = new List<int> { (int)NREventManager.Attach_Request };
                evtSuccList = new List<int> { (int)NREventManager.Attach_Accept };
            }

            return curCellServiceInfo.AccessInfo;
        }

        protected override void verifyFileResult(FileInfo fileInfo, DTFileDataManager fileManager, NR700MBtsInfo bts, NR700MCellInfo cell, NR700MStationAcceptCondition condition)
        {
            curCellServiceInfo.AccessInfo.Calculate();
            curCellServiceInfo.AccessInfo.Judge(10, 1);
        }
    }

    #region EPSFB
    class NR700MAcpEPSFB : NR700MAcpPing
    {
        NR700MCellServiceInfo.EvtInfo curFREvtInfo;
        double frDelayThreshold;
        protected void resetEvtList(string fileName, bool isFR
            , ref NR700MCellServiceInfo.EvtInfo evtInfo, ref double delayThreshold)
        {
            if (isFR)
            {
                evtRequList = new List<int>();
                evtSuccList = new List<int> { (int)NREventManager.EPSFB_FR_LessThan2point5 };
                evtFailList = new List<int> { (int)NREventManager.EPSFB_FR_Fail, (int)NREventManager.EPSFB_FR_MoreThan2point5 };
                if (fileName.Contains("5-5"))
                {
                    delayThreshold = 2.5;
                    evtInfo = curCellServiceInfo.EpsfbNrCallNrFR;
                }
                else if (fileName.Contains("5-4"))
                {
                    delayThreshold = 2.5;
                    evtInfo = curCellServiceInfo.EpsfbNrCallLteFR;
                }
                else
                {
                    evtInfo = null;
                }
            }
            else
            {
                evtRequList = new List<int> { (int)NREventManager.EPSFB_Audio_MO_Call_Attempt
                , (int)NREventManager.EPSFB_Audio_MT_Call_Attempt };
                evtSuccList = new List<int> { (int)NREventManager.EPSFB_Audio_MO_Call_Setup
                , (int)NREventManager.EPSFB_Audio_MT_Call_Setup };
                evtFailList = new List<int>();
                if (fileName.Contains("5-5"))
                {
                    delayThreshold = 4;
                    evtInfo = curCellServiceInfo.EpsfbNrCallNr;
                }
                else if (fileName.Contains("5-4"))
                {
                    delayThreshold = 3;
                    evtInfo = curCellServiceInfo.EpsfbNrCallLte;
                }
                else
                {
                    evtInfo = null;
                }
            }
        }

        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("EPS语音");
        }

        protected override void analyzeNRFile(FileInfo fileInfo, DTFileDataManager fileManager, NR700MBtsInfo bts, NR700MCellInfo cell, NR700MStationAcceptCondition condition)
        {
            dealData(fileInfo, fileManager, false, ref curEvtInfo, ref delayThreshold);
            dealData(fileInfo, fileManager, true, ref curFREvtInfo, ref frDelayThreshold);
        }

        private void dealData(FileInfo fileInfo, DTFileDataManager fileManager, bool isFR
            , ref NR700MCellServiceInfo.EvtInfo evtInfo, ref double delayThreshold)
        {
            resetEvtList(fileInfo.Name, isFR, ref evtInfo, ref delayThreshold);
            if (evtInfo == null)
            {
                return;
            }

            List<int> delayList = new List<int>();
            foreach (Event evt in fileManager.Events)
            {
                dealEvtData(evt, delayList, evtInfo);
            }

            if (isFR)
            {
                evtInfo.RateInfo.CalculateRequestEvt();
            }

            double shake = getShake(delayList);
            evtInfo.Shake.Data = Math.Round(shake / 1000, 2);//单位s
        }

        private void dealEvtData(Event evt, List<int> delayList, NR700MCellServiceInfo.EvtInfo evtInfo)
        {
            if (evtRequList.Contains(evt.ID))
            {
                evtInfo.RateInfo.RequestCnt++;
            }
            else if (evtSuccList.Contains(evt.ID))
            {
                int delay = int.Parse(evt["Value1"].ToString());
                evtInfo.Delay.Add(delay / 1000d);//单位s
                evtInfo.RateInfo.SucceedCnt++;
                delayList.Add(delay);
            }
            else if (evtFailList.Contains(evt.ID))
            {
                evtInfo.RateInfo.FailedCnt++;
            }
        }

        protected override void verifyFileResult(FileInfo fileInfo, DTFileDataManager fileManager, NR700MBtsInfo bts, NR700MCellInfo cell, NR700MStationAcceptCondition condition)
        {
            verifyResult(curEvtInfo, delayThreshold);
            verifyResult(curFREvtInfo, frDelayThreshold);
        }

        protected void verifyResult(NR700MCellServiceInfo.EvtInfo evtInfo, double delayThreshold)
        {
            if (evtInfo == null)
            {
                return;
            }

            evtInfo.RateInfo.Calculate();
            evtInfo.RateInfo.Judge(10, 1);
            evtInfo.Delay.Calculate();
            evtInfo.Delay.IsValid = true;
            //if (evtInfo.Delay.Data < delayThreshold)
            //{
            //    evtInfo.Delay.IsValid = true;
            //}
            //evtInfo.Shake.IsValid = true;
        }
    }
    #endregion

    #region Ping
    class NR700MAcpPing : NR700MStationAcceptBase
    {
        protected NR700MCellServiceInfo.EvtInfo curEvtInfo;
        protected double delayThreshold;

        protected List<int> evtRequList;
        protected List<int> evtSuccList;
        protected List<int> evtFailList;
        public NR700MAcpPing()
        {
            evtSuccList = new List<int> { (int)NREventManager.Ping_Success };
            evtFailList = new List<int> { (int)NREventManager.Ping_Fail };
        }

        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.ToUpper().Contains("PING");
        }

        protected override void analyzeNRFile(FileInfo fileInfo, DTFileDataManager fileManager
            , NR700MBtsInfo bts, NR700MCellInfo cell, NR700MStationAcceptCondition condition)
        {
            if (fileInfo.Name.Contains("大包"))
            {
                delayThreshold = 16;
                curEvtInfo = curCellServiceInfo.BigPackagePing;
            }
            else if (fileInfo.Name.Contains("小包"))
            {
                delayThreshold = 14;
                curEvtInfo = curCellServiceInfo.SmallPackagePing;
            }
            else
            {
                return;
            }

            Event preEvt = null;
            List<int> delayList = new List<int>();
            foreach (Event evt in fileManager.Events)
            {
                dealEvtData(evt, ref preEvt, delayList);
            }

            curEvtInfo.RateInfo.CalculateRequestEvt();

            double shake = getShake(delayList);
            curEvtInfo.Shake.Data = Math.Round(shake, 2);//单位ms
        }

        private void dealEvtData(Event evt, ref Event preEvt, List<int> delayList)
        {
            if (!evtSuccList.Contains(evt.ID) && !evtFailList.Contains(evt.ID))
            {
                return;
            }
            //测试间隔2s
            if (preEvt == null || evt.DateTime.Subtract(preEvt.DateTime).TotalMilliseconds >= 2000)
            {
                dealPingData(evt, delayList);
            }
            preEvt = evt;
        }

        private void dealPingData(Event evt, List<int> delayList)
        {
            if (evtSuccList.Contains(evt.ID))
            {
                int delay = int.Parse(evt["Value1"].ToString());
                curEvtInfo.Delay.Add(delay);//单位ms
                curEvtInfo.RateInfo.SucceedCnt++;
                delayList.Add(delay);
            }
            else
            {
                curEvtInfo.RateInfo.FailedCnt++;
            }
        }

        protected double getShake(List<int> delayList)
        {
            double dataPowerSum = 0;
            double dataSum = 0;
            foreach (var data in delayList)
            {
                dataSum += data;
                double dataPower = Math.Pow(data, 2);
                dataPowerSum += dataPower;
            }

            //公式 : √￣(x1^2 + x2^2 + x3^2) / 3 - (x1 + x2 + x3)^2 / 3
            double res = Math.Sqrt(dataPowerSum / delayList.Count - Math.Pow(dataSum / delayList.Count, 2));
            return res;
        }

        protected override void verifyFileResult(FileInfo fileInfo, DTFileDataManager fileManager, NR700MBtsInfo bts, NR700MCellInfo cell, NR700MStationAcceptCondition condition)
        {
            if (curEvtInfo == null)
            {
                return;
            }

            //连续测试次数要求最少50次
            //32Bytes小包：时延平均不大于14ms，抖动不大于3ms，成功率大于99%；
            //2000Bytes大包：时延平均不大于16ms，抖动不大于3ms，成功率大于99%。
            //(仅统计RAN侧时延，需扣除传输链路和核心网侧时延）
            curEvtInfo.RateInfo.Calculate();
            curEvtInfo.RateInfo.Judge(50, 0.99);
            curEvtInfo.Delay.Calculate();
            if (curEvtInfo.Delay.Data <= delayThreshold)
            {
                curEvtInfo.Delay.IsValid = true;
            }
            curEvtInfo.Shake.IsValid = true;
        }
    }
    #endregion

    class NR700MAcpHandoverRate : NR700MStationAcceptBase
    {
        protected List<int> evtSuccList;
        protected List<int> evtRequList;

        protected SuccessRateKpiInfo resetEvtList(string fileName, Bts700MHandoverInfo info)
        {
            if (fileName.Contains("站间"))
            {
                evtRequList = new List<int> { (int)NREventManager.NRInterHandoverRequest };
                evtSuccList = new List<int> { (int)NREventManager.NRInterHandoverSuccess };
                return info.InterHandoverRate;
            }
            else if (fileName.Contains("站内"))
            {
                evtRequList = new List<int> { (int)NREventManager.NRIntraHandoverRequest };
                evtSuccList = new List<int> { (int)NREventManager.NRIntraHandoverSuccess };
                return info.IntraHandoverRate;
            }
            return null;
        }

        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("切换");
        }

        protected override void analyzeNRFile(FileInfo fileInfo, DTFileDataManager fileManager, NR700MBtsInfo bts, NR700MCellInfo cell, NR700MStationAcceptCondition condition)
        {
            if (condition.NRServiceType != NRServiceName.SA)
            {
                return;
            }
            if (!bts.SABtsInfo.FileBtsHandOverInfoDic.TryGetValue(bts.FileBtsName, out Bts700MHandoverInfo info))
            {
                info = new Bts700MHandoverInfo(bts.FileBtsName);
                bts.SABtsInfo.FileBtsHandOverInfoDic.Add(bts.FileBtsName, info);
            }

            var handoverRate = resetEvtList(fileInfo.Name, info);
            if (handoverRate != null)
            {
                dealEvtCount(fileManager, handoverRate, evtSuccList, evtRequList);
                handoverRate.CalculateFailEvt();
                if (fileInfo.Name.Contains("站间") && handoverRate.RequestCnt == 0
                    && handoverRate.SucceedCnt == 0)
                {
                    //站间切换数为0,使用4G切换
                    evtRequList = new List<int> { (int)NREventManager.IRAT_NR_LTE_HO_Request };
                    evtSuccList = new List<int> { (int)NREventManager.IRAT_NR_LTE_HO_Success };
                    dealEvtCount(fileManager, handoverRate, evtSuccList, evtRequList);
                }
            }
        }

        private void dealEvtCount(DTFileDataManager fileManager, SuccessRateKpiInfo handOverInfo
            , List<int> evtSuccList, List<int> evtRequList)
        {
            foreach (Event evt in fileManager.Events)
            {
                if (evtRequList.Contains(evt.ID))
                {
                    handOverInfo.RequestCnt++;
                }
                else if (evtSuccList.Contains(evt.ID))
                {
                    handOverInfo.SucceedCnt++;
                }
            }
        }

        protected override void verifyFileResult(FileInfo fileInfo, DTFileDataManager fileManager, NR700MBtsInfo bts, NR700MCellInfo cell, NR700MStationAcceptCondition condition)
        {
            //1.成功率100%
            foreach (var handOverInfo in curBtsServiceInfo.FileBtsHandOverInfoDic.Values)
            {
                if (fileInfo.Name.Contains("站间"))
                {
                    handOverInfo.InterHandoverRate.Calculate();
                    handOverInfo.InterHandoverRate.Judge(0, 1);
                }
                else if (fileInfo.Name.Contains("站内"))
                {
                    handOverInfo.IntraHandoverRate.Calculate();
                    handOverInfo.IntraHandoverRate.Judge(0, 1);
                }
            }
        }
    }

    class NR700MAcpChartLinePic : NR700MStationAcceptCoverPic
    {
        public NR700MAcpChartLinePic(string picPath)
            : base(picPath)
        {
        }

        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("好点上传") || fileInfo.Name.Contains("好点下载");
        }

        protected override void analyzeFile(FileInfo fileInfo, DTFileDataManager fileManager
           , NR700MBtsInfo bts, NR700MCellServiceInfo cellTypeInfo
           , NR700MStationAcceptCondition condition)
        {
            string paramName;
            string path;

            string serviceType = "";
            if (condition.NRServiceType == NRServiceName.SA)
            {
                serviceType = "SA_";
            }
            string cellName = serviceType + cellTypeInfo.Cell.Name;

            if (fileInfo.Name.Contains("上传"))
            {
                paramName = "NR_Throughput_MAC_UL_Mb";
                path = getCoverPicPath(bts.BtsName, $"{cellName}_Line", paramName);
                DateTime dt = fileManager.TestPoints[0].DateTime;
                var xDic = getXCoordinateAxisFields(dt);
                var yDic = getYCoordinateAxisFields(false, cellTypeInfo.GoodSampleUL.Throughput.MaxData);

                var info = new GDIHelper.ChartLineCondition(paramName, path, "时间", "bps"
                    , xDic.Count, yDic.Count);

                cellTypeInfo.NRULLineChartPic.PicPath =
                GDIHelper.DrawTestPointChartLine(fileManager, info, xDic, yDic);
            }
            else
            {
                paramName = "NR_Throughput_MAC_DL_Mb";
                path = getCoverPicPath(bts.BtsName, $"{cellName}_Line", paramName);
                DateTime dt = fileManager.TestPoints[0].DateTime;
                var xDic = getXCoordinateAxisFields(dt);
                var yDic = getYCoordinateAxisFields(true, cellTypeInfo.GoodSampleDL.Throughput.MaxData);

                var info = new GDIHelper.ChartLineCondition(paramName, path, "时间", "bps"
                    , xDic.Count, yDic.Count);

                cellTypeInfo.NRDLLineChartPic.PicPath =
                GDIHelper.DrawTestPointChartLine(fileManager, info, xDic, yDic);
            }
        }

        #region 折线图参数
        private Dictionary<DateTime, string> getXCoordinateAxisFields(DateTime dt)
        {
            var xDic = MasterCom.Util.GDI.LineChart.DateTimeValue.GetDateTimeFields(dt, 10
                , MasterCom.Util.GDI.LineChart.DateTimeValue.DateTimeType.Second, 10);

            return xDic;
        }

        private Dictionary<double, string> getYCoordinateAxisFields(bool isDL, double maxData
            , int levelCount = 5)
        {
            double max = Math.Ceiling(maxData / 100) * 100;
            if (isDL)
            {
                max += 100;
            }
            else
            {
                max += 50;
            }

            double gap = Math.Ceiling(max / levelCount);
            var yDic = new Dictionary<double, string>();
            yDic.Add(0, "0");
            for (int i = 1; i <= levelCount - 1; i++)
            {
                var data = i * gap;
                yDic.Add(data, $"{data}M");
            }
            yDic.Add(max, $"{max}M");
            return yDic;
        }
        #endregion
    }

    class NR700MAcpCoverPic : NR700MStationAcceptCoverPic
    {
        public NR700MAcpCoverPic(string picPath)
            : base(picPath)
        {
            reSetMapView("NR_SS_RSRP", new Func(getRsrpRanges), "");
            reSetMapView("NR_SS_SINR", new Func(getSinrRanges), "");
        }

        protected List<RangeInfo> getRsrpRanges(string band)
        {
            List<RangeInfo> ranges = new List<RangeInfo>();
            ranges.Add(new RangeInfo(false, false, -145, -110, Color.Red));
            ranges.Add(new RangeInfo(true, false, -110, -90, Color.Orange));
            ranges.Add(new RangeInfo(true, false, -90, -80, Color.Yellow));
            ranges.Add(new RangeInfo(true, false, -80, -70, Color.Green));
            ranges.Add(new RangeInfo(true, false, -70, -30, Color.Blue));
            return ranges;
        }

        protected List<RangeInfo> getSinrRanges(string band)
        {
            List<RangeInfo> ranges = new List<RangeInfo>();
            ranges.Add(new RangeInfo(false, false, -20, 3, Color.Red));
            ranges.Add(new RangeInfo(true, false, 3, 10, Color.Orange));
            ranges.Add(new RangeInfo(true, false, 10, 16, Color.Yellow));
            ranges.Add(new RangeInfo(true, false, 16, 25, Color.Green));
            ranges.Add(new RangeInfo(true, false, 25, 50, Color.Blue));
            return ranges;
        }

        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("DT上传") || fileInfo.Name.Contains("DT下载");
        }

        protected override void analyzeFile(FileInfo fileInfo, DTFileDataManager fileManager
            , NR700MBtsInfo bts, NR700MCellServiceInfo cellTypeInfo
            , NR700MStationAcceptCondition condition)
        {
            MTGis.DbRect bounds = getCoverBounds(fileManager, cellTypeInfo.Cell.Longitude, cellTypeInfo.Cell.Latitude);
            string paramName;
            string path;
            string band = getBandWidth(fileInfo.Name);

            var mf = initMap(bounds);

            string serviceType = "";
            if (condition.NRServiceType == NRServiceName.SA)
            {
                serviceType = "SA_";
            }
            string cellName = serviceType + cellTypeInfo.Cell.Name;

            if (fileInfo.Name.Contains("DT下载") || string.IsNullOrEmpty(cellTypeInfo.NRRsrpPic.PicPath))
            {
                paramName = "NR_SS_RSRP";
                path = getCoverPicPath(bts.BtsName, cellName, paramName);
                cellTypeInfo.NRRsrpPic.PicPath = fireMapAndTakePic(mf, paramName, path);

                paramName = "NR_SS_SINR";
                path = getCoverPicPath(bts.BtsName, cellName, paramName);
                cellTypeInfo.NRSinrPic.PicPath = fireMapAndTakePic(mf, paramName, path);
            }

            if (fileInfo.Name.Contains("DT上传"))
            {
                paramName = "NR_Throughput_MAC_UL_Mb";
                reSetMapView(paramName, new Func(getULRanges), band);
                path = getCoverPicPath(bts.BtsName, cellName, paramName);
                cellTypeInfo.NRULPic.PicPath = fireMapAndTakePic(mf, paramName, path);
            }
            else
            {
                paramName = "NR_Throughput_MAC_DL_Mb";
                reSetMapView(paramName, new Func(getDLRanges), band);
                path = getCoverPicPath(bts.BtsName, cellName, paramName);
                cellTypeInfo.NRDLPic.PicPath = fireMapAndTakePic(mf, paramName, path);
            }
        }

        protected List<RangeInfo> getDLRanges(string band)
        {
            List<RangeInfo> ranges = new List<RangeInfo>();
            ranges.Add(new RangeInfo(false, false, 0, 5, Color.Red));
            ranges.Add(new RangeInfo(true, false, 5, 33, Color.Orange));
            ranges.Add(new RangeInfo(true, false, 33, 67, Color.Yellow));
            ranges.Add(new RangeInfo(true, false, 67, 200, Color.Green));
            ranges.Add(new RangeInfo(true, false, 200, 10000, Color.Blue));
            return ranges;
        }

        protected List<RangeInfo> getULRanges(string band)
        {
            List<RangeInfo> ranges = new List<RangeInfo>();
            ranges.Add(new RangeInfo(false, false, 0, 1, Color.Red));
            ranges.Add(new RangeInfo(true, false, 1, 15, Color.Orange));
            ranges.Add(new RangeInfo(true, false, 15, 30, Color.Yellow));
            ranges.Add(new RangeInfo(true, false, 30, 90, Color.Green));
            ranges.Add(new RangeInfo(true, false, 90, 10000, Color.Blue));
            return ranges;
        }
    }

    class NR700MAcpHandoverPic : StationAcceptHanOverPic
    {
        public NR700MAcpHandoverPic(string picPath)
            : base(picPath)
        {

        }

        private const string pciParamName = "NR_PCI";

        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("站内切换");
        }

        protected override void analyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, BtsInfoBase bts, CellInfoBase cell, StationAcceptConditionBase condition)
        {
            NR700MBtsInfo nrBts = bts as NR700MBtsInfo;
            NR700MStationAcceptCondition nrCondition = condition as NR700MStationAcceptCondition;
            reSetPCIMapView(nrBts.Bts, nrBts.FileBtsName, pciParamName);

            MTGis.DbRect bounds;
            if (nrBts.FileBtsName == nrBts.BtsName)
            {
                //当前站点范围 = 采样点+基站
                bounds = getCoverBounds(fileManager, nrBts.Bts.Longitude, nrBts.Bts.Latitude);
            }
            else
            {
                //拉远站点范围 = 采样点
                bounds = getCoverBounds(fileManager);
            }

            var mf = initMap(bounds);
            MainModel.GetInstance().DrawDifferentServerColor = true;
            if (nrCondition.NRServiceType == NRServiceName.SA)
            {
                if (!nrBts.SABtsInfo.FileBtsHandOverInfoDic.TryGetValue(nrBts.FileBtsName, out Bts700MHandoverInfo info))
                {
                    info = new Bts700MHandoverInfo(nrBts.FileBtsName);
                    nrBts.SABtsInfo.FileBtsHandOverInfoDic.Add(nrBts.FileBtsName, info);
                }

                string path = getCoverPicPath(nrBts.BtsName, "SA700M_" + nrBts.FileBtsName, pciParamName);
                info.PCIPicInfo.PicPath = fireMapAndTakePic(mf, pciParamName, path);
            }

            MainModel.GetInstance().DrawDifferentServerColor = false;
        }

        protected override string fireMapAndTakePic(RAMS.Func.MapForm mf, string paramName, string filePath)
        {
            //要让小区显示在采样点之上
            var nrLayer = mf.GetNRCellLayer();
            mf.MakeSureCustomLayerVisible(nrLayer, true);

            return base.fireMapAndTakePic(mf, paramName, filePath);
        }

        protected override List<ICell> getValidCells(ISite bts, string btsNamePostfix)
        {
            List<ICell> cellList = new List<ICell>();
            if (bts is NRBTS)
            {
                NRBTS nrBts = bts as NRBTS;
                foreach (NRCell cell in nrBts.Cells)
                {
                    if (cell.Name.Contains(btsNamePostfix))
                    {
                        cellList.Add(cell);
                    }
                }
            }
            return cellList;
        }

        protected override List<int> getPCIList(List<ICell> cellList)
        {
            List<int> pciList = new List<int>();
            foreach (ICell cell in cellList)
            {
                NRCell nrCell = cell as NRCell;
                pciList.Add(nrCell.PCI);
            }
            return pciList;
        }

        protected override void setServerCellColorByPCI(List<ICell> cellList, List<RangeInfo> ranges)
        {
            foreach (ICell cell in cellList)
            {
                NRCell nrCell = cell as NRCell;
                foreach (RangeInfo range in ranges)
                {
                    if (nrCell.PCI >= range.Min && nrCell.PCI < range.Max)
                    {
                        nrCell.ServerCellColor = range.RangeColor;
                        break;
                    }
                }
            }
        }
    }

}
