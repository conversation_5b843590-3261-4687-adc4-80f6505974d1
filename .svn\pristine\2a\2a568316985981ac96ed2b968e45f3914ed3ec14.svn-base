﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class QueryKpiStatByFiles : QueryKPIStatAll
    {

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }
        public override string Name
        {
            get { return "文件KPI统计"; }
        }
        public override string IconName
        {
            get { return "Images/stat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11025, this.Name);
        }

        protected FileInfo curFile = null;

        protected override void queryInThread(object o)
        {
            System.Threading.Thread.Sleep(100);
            WaitBox.Text = "开始查询KPI统计数据...";
            WaitBox.CanCancel = true;
            ClientProxy clientProxy = (ClientProxy)o;
            try
            {
                string imgTriadIDSet = getStatImgNeededTriadID();
                int idx=1;
                foreach (FileInfo file in condition.FileInfos)
                {
                    if (file.DistrictID!=clientProxy.DbID)
                    {
                        continue;
                    }
                    curFile = file;
                    WaitBox.Text = "(" + (idx++) + "/" + condition.FileInfos.Count + ")正在统计文件[" + file.Name + "]...";
                    WaitBox.ProgressPercent = 10;
                    //添加一个初始数据,用于"按文件分开统计"时显示没有数据的文件
                    Event evt = new Event();
                    handleStatEvent(evt);
                    queryPeriodInfo(null, clientProxy, imgTriadIDSet, file);
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
            }
            catch (Exception e)
            {
                MessageBox.Show("Error" + e.StackTrace + e.ToString());
            }
            finally
            {
                curFile = null;
                System.Threading.Thread.Sleep(100);
                WaitBox.Close();
            }
        }


        protected override void preparePackageCondition(Package package, TimePeriod period, params object[] reservedParams)
        {
            FileInfo fi = reservedParams[1] as FileInfo;
            TimePeriod logTbPeriod;
            DateTime bTime = getFirstDateFromLogTbName(fi.LogTable);
            DateTime eTime = bTime.AddMonths(1).AddSeconds(-1);
            logTbPeriod = new TimePeriod(bTime, eTime);
            AddDIYPeriod(package, logTbPeriod);
            AddDIYFileID(package, fi.ID);
            AddDIYStatStatus(package);
            AddGeographicFilter(package);
        }

        private DateTime getFirstDateFromLogTbName(string strname)
        {
            string[] vec = strname.Split('_');
            if (vec.Length == 5)
            {
                int year;
                int month;
                int.TryParse(vec[3], out year);
                int.TryParse(vec[4], out month);
                return new DateTime(year, month, 1);
            }
            throw (new Exception("格式错误，时间获取失败！"));
        }

        protected void AddDIYFileID(Package package, int fileID)
        {
            package.Content.AddParam((byte)OpOptionDef.Equal);
            package.Content.AddParam("0,1,1");//fileid
            package.Content.AddParam("" + fileID);
        }

        protected override void recieveAndHandleSpecificStatData(Net.Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            fillStatData(package, curImgColumnDef, singleStatData);
            KpiDataManager.AddStatData(string.Empty, curFile, curFile, singleStatData, false);
        }

        protected override void handleStatEvent(Model.Event evt)
        {
            StatDataEvent eventData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
            KpiDataManager.AddStatData(string.Empty, curFile, curFile, eventData, false);
        }



    }
}
