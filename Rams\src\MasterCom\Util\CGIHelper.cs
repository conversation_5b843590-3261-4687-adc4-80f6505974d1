﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.Util
{
    public static class CGIHelper
    {
        public const long LteThreshold = 256;
        public const long NrThreshold = 4096;

        #region 根据CI获取CGI
        public static string GetNrCellCgiByNci(long nci)
        {
            return getCgiByCi(nci, NrThreshold);
        }

        public static string GetLteCellCgiByEci(int eci)
        {
            return getCgiByCi(eci, LteThreshold);
        }

        private static string getCgiByCi(long ci, long threshold)
        {
            if (ci <= 0)
            {
                return "";
            }

            long btsID = ci / threshold;
            long cellID = ci % threshold;
            string cgi = $"460-00-{btsID}-{cellID}";
            return cgi;
        }
        #endregion

        #region 根据CGI获取CI
        public static long GetNciCgiByCgi(string cgi)
        {
            return getCiCgiByCgi(cgi, NrThreshold);
        }

        public static long GetEciCgiByCgi(string cgi)
        {
            return getCiCgiByCgi(cgi, LteThreshold);
        }

        private static long getCiCgiByCgi(string cgi, long threshold)
        {
            if (string.IsNullOrEmpty(cgi))
            {
                return 0;
            }

            var strs = cgi.Split('-');
            if (strs.Length != 4)
            {
                return 0;
            }
            int enodebid = int.Parse(strs[2]);
            int cellid = int.Parse(strs[3]);
            long ci = enodebid * threshold + cellid;
            return ci;
        }
        #endregion

        #region 通过采样点参数获取CGI
        public static string GetNrCgiByNrTp(TestPoint tp)
        {
            long? nci = (long?)NRTpHelper.NrTpManager.GetNCI(tp);
            if (nci is null)
            {
                return "";
            }

            return GetNrCellCgiByNci((long)nci);
        }

        public static string GetLteCgiByNrTp(TestPoint tp)
        {
            int? eci = (int?)NRTpHelper.NrLteTpManager.GetNCI(tp);
            if (eci is null)
            {
                return "";
            }

            return GetLteCellCgiByEci((int)eci);
        }

        public static string GetLteCgiByLteTp(TestPoint tp)
        {
            int? eci = (int?)tp["lte_ECI"];
            if (eci is null)
            {
                return "";
            }

            return GetLteCellCgiByEci((int)eci);
        }
        #endregion
    }
}
