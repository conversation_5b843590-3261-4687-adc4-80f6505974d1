﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.MTGis;
using System.Drawing;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLowSpeedReasonAnaByRegion : DIYSampleByRegion
    {
        private static List<ReasonDealBase> rdbList = null;
        readonly LowSpeedTempData data;
        Dictionary<string, MapOperation2> regionMopDic = null;
        public LowSpeedCondi lowSpeedCondition { get; set; }
        Dictionary<string, LowSpeedRegionInfo> regionInfoDic = null;   //区域维度
        Dictionary<Cell4LowSpeed, LowSpeedCellInfo> cellInfoDic = null;  //小区维度
        public int SpeedThreshold { get; set; } = 200;

        public ZTLowSpeedReasonAnaByRegion(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;

            data = new LowSpeedTempData();
            lowSpeedCondition = new LowSpeedCondi();
            if (rdbList == null || rdbList.Count <= 0)
            {
                initRDB();
            }
        }

        public override string Name
        {
            get { return "低速率原因分析"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 13038, this.Name);
        }


        public static List<ReasonDealBase> RDBList
        {
            get
            {
                if (rdbList == null)
                {
                    initRDB();
                }
                return rdbList;
            }
        }

        private static void initRDB()
        {
            rdbList = new List<ReasonDealBase>();
            rdbList.Add(new Reason_GSM());
            rdbList.Add(new Reason_TD_R4());
            rdbList.Add(new Reason_WeakCover());
            rdbList.Add(new Reason_CoverLap());
            rdbList.Add(new Reason_BackCover());
            rdbList.Add(new Reason_PilotPollution());
            rdbList.Add(new Reason_CornerEffect());
            rdbList.Add(new Reason_PoorC2I());
            rdbList.Add(new Reason_UpInterrupt());
            rdbList.Add(new Reason_CoCpi());
            rdbList.Add(new Reason_PCConfig());
            rdbList.Add(new Reason_HODelay());
            rdbList.Add(new Reason_HOProblem());
            rdbList.Add(new Reason_AntennaFault());
            rdbList.Add(new Reason_LowSchedule());
            rdbList.Add(new Reason_Other());
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "TD_PCCPCH_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_SCell_LAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_APP_DataStatus_DL";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_SCell_CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_SCell_UARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_SCell_CPI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_TxPower";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_PCCPCH_C2I";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_DPCH_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_BLER";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_NCell_PCCPCH_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_NCell_CPI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_DPCH_C2I";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_HSDPA_HS_SCCH_CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_HSDPA_HS_PDSCH_CI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_APP_ThroughputDL";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_TS_TimeSlot";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TD_HSDPA_HS_ScchScheduled_Rate";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"TD_APP_ThroughputDL");
            tmpDic.Add("themeName", (object)"TD_APP_ThroughputDL");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        private void initData()
        {
            data.Clear();
            regionInfoDic = new Dictionary<string, LowSpeedRegionInfo>();
            cellInfoDic = new Dictionary<Cell4LowSpeed, LowSpeedCellInfo>();
            regionMopDic = new Dictionary<string, MapOperation2>();
        }

        protected override bool getConditionBeforeQuery()
        {
            initData();
            ZTLowSpeedSholdSettingDlg speedDlg = new ZTLowSpeedSholdSettingDlg();
            if (speedDlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            SpeedThreshold = speedDlg.SpeedShold;
            ZTLowSpeedReasonSettingDlg dlg = new ZTLowSpeedReasonSettingDlg();
            dlg.InitData(lowSpeedCondition, rdbList);
            if (dlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            LowSpeedCondi lowSpeedConditionTmp;
            dlg.getCondition(out lowSpeedConditionTmp, out rdbList);
            lowSpeedCondition = lowSpeedConditionTmp;
            PrepareEvents();
            InitRegionMop2();
            return true;
        }

        /// <summary>
        ///准备查询质差原因所需的事件
        /// </summary>
        private void PrepareEvents()
        {
            DIYEventByRegion queryEvent = new DIYEventByRegion(MainModel);
            queryEvent.SetIsAddEventToDTDataManager(false);
            queryEvent.SetSaveAsFileEventsDic(true);
            queryEvent.showEventChooser = false;
            queryEvent.IsQueryAllEvents = false;
            List<int> eventIds = new List<int>();
            eventIds.Add(964);//切换不合理
            eventIds.Add(145);//切换成功
            eventIds.Add(148);//切换成功
            eventIds.Add(239);//拐角效应
            eventIds.Add(240);//切换不及时
            eventIds.Add(241);//孤岛效应
            eventIds.Add(428);//切换频繁
            eventIds.Add(102);//接通事件
            eventIds.Add(105);//未接通事件
            eventIds.Add(189);//未接通事件
            eventIds.Add(104);//掉话事件
            eventIds.Add(112);//呼叫结束事件
            condition.EventIDs = eventIds;
            queryEvent.SetQueryCondition(condition);
            queryEvent.Query();
            data.fileEventDic = queryEvent.fileEventsDic;
        }

        private void InitRegionMop2()
        {
            List<ResvRegion> resvRegions = MainModel.SearchGeometrys.SelectedResvRegions;
            MapWinGIS.Shape gmt = MainModel.SearchGeometrys.Region;
            
            if (resvRegions != null && resvRegions.Count > 0)  //预存区域
            {
                foreach (ResvRegion region in resvRegions)
                {
                    if (!regionMopDic.ContainsKey(region.RegionName))
                    {
                        MapOperation2 mapOp2 = new MapOperation2();
                        mapOp2.FillPolygon(region.Shape);
                        regionMopDic.Add(region.RegionName, mapOp2);
                    }
                }
            }
            else if (gmt != null)//单个区域
            {
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(gmt);
                regionMopDic.Add("当前区域", mapOp2);
            }
        }

        /// <summary>
        /// 定位是否为包括的采样点
        /// </summary>
        /// <param name="iType"></param>
        /// <returns>返回区域名称</returns>
        private string isContainPoint(DbPoint dPoint)
        {
            foreach (string strKey in regionMopDic.Keys)
            {
                if (regionMopDic[strKey].CheckPointInRegion(dPoint.x, dPoint.y))
                {
                    return strKey;
                }
            }
            return null;
        }

        protected override void FireShowFormAfterQuery()
        {
            dealPendingList();
            fillCellOtherInfo();
            showResult();
        }

        protected void dealPendingList()
        {
            foreach (LowSpeedPendingPointInfo tpPending in data.tpList_Pending)
            {
                LowSpeedRegionInfo regionInfo = regionInfoDic[tpPending.regionTag];
                LowSpeedCellInfo cellInfo = cellInfoDic[tpPending.cellTag];

                //在同一个文件中找
                string reason = "";
                reason = getReasonFromNearestPoint(cellInfo, tpPending);
                addToRegion(reason, tpPending.tp, ref regionInfo);
                addToCell(reason, tpPending.tp, ref cellInfo);
            }
        }

        protected string getReasonFromNearestPoint(LowSpeedCellInfo cellInfo, LowSpeedPendingPointInfo tpPending)
        {
            string strFileName = tpPending.tp.FileName;

            if (cellInfo.fileDic.ContainsKey(strFileName))  //取出文件对应的所有点
            {
                List<LowSpeedPointInfo> fileTpList = cellInfo.fileDic[strFileName];

                LowSpeedPointInfo afterTp = null;
                LowSpeedPointInfo beforeTp = null;

                getBeforeAfterTp(tpPending, fileTpList, ref afterTp, ref beforeTp);

                if (afterTp != null)
                {
                    return afterTp.reason;
                }
                else if (beforeTp != null)
                {
                    return beforeTp.reason;
                }
                else
                {
                    return "其它";
                }
            }
            else
            {
                return "其它";
            }
        }

        private static void getBeforeAfterTp(LowSpeedPendingPointInfo tpPending, List<LowSpeedPointInfo> fileTpList, ref LowSpeedPointInfo afterTp, ref LowSpeedPointInfo beforeTp)
        {
            foreach (LowSpeedPointInfo tpFile in fileTpList)
            {
                if ((tpPending.tp.Time - tpFile.reasonTP.tp.Time) <= 0 && (tpPending.tp.Time - tpFile.reasonTP.tp.Time) <= -5)   //向后看5秒内
                {
                    if (afterTp == null)
                    {
                        afterTp = tpFile;
                    }
                    else if (afterTp.reasonTP.tp.Time > tpFile.reasonTP.tp.Time)  //更近
                    {
                        afterTp = tpFile;
                    }
                }
                else if ((tpPending.tp.Time - tpFile.reasonTP.tp.Time) > 0 && (tpPending.tp.Time - tpFile.reasonTP.tp.Time) <= 5)  //向前看5秒内
                {
                    if (beforeTp == null)
                    {
                        beforeTp = tpFile;
                    }
                    else if (beforeTp.reasonTP.tp.Time < tpFile.reasonTP.tp.Time)  //更近
                    {
                        beforeTp = tpFile;
                    }
                }
            }
        }

        protected void fillCellOtherInfo()
        {
            System.Windows.Forms.Cursor.Current = System.Windows.Forms.Cursors.WaitCursor;

            foreach (Cell4LowSpeed cell in cellInfoDic.Keys)
            {
                if (cell.Name == "未知小区")
                {
                    cellInfoDic[cell].areaName = "";
                    cellInfoDic[cell].roadName = "";
                }
                else
                {
                    cellInfoDic[cell].areaName = GISManager.GetInstance().GetGridDesc(cell.Longitude, cell.Latitude);
                    cellInfoDic[cell].roadName = GISManager.GetInstance().GetRoadPlaceDesc(cell.Longitude, cell.Latitude);
                }
            }

            System.Windows.Forms.Cursor.Current = System.Windows.Forms.Cursors.Default;
        }

        protected void showResult()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(LowSpeedReasonForm).FullName);
            LowSpeedReasonForm form = obj == null ? null : obj as LowSpeedReasonForm;
            if (form == null || form.IsDisposed)
            {
                form = new LowSpeedReasonForm(MainModel);
            }
            form.FillData(regionInfoDic, cellInfoDic);
            form.Show(MainModel.MainForm);
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            bool isValid = false;
            if (tp is TDTestPointDetail)
            {
                int? speed = (int?)tp["TD_APP_ThroughputDL"];
                if (speed != null && speed >= 0 && base.isValidTestPoint(tp))
                {
                    isValid = true;
                }
            }
            return isValid;
        }

        protected LowSpeedRegionInfo getCurRegion(TestPoint tp)
        {
            //获取区域名称
            string strRegionName = isContainPoint(new DbPoint(tp.Longitude, tp.Latitude));
            if (strRegionName == null)
            {
                return null;
            }

            LowSpeedRegionInfo curRegion = null;
            if (regionInfoDic.ContainsKey(strRegionName))
            {
                curRegion = regionInfoDic[strRegionName];
                curRegion.tpTotal++;
            }
            else
            {
                curRegion = new LowSpeedRegionInfo(strRegionName);
                regionInfoDic[strRegionName] = curRegion;
            }

            return curRegion;
        }

        protected LowSpeedCellInfo getCurCell(TestPoint tp)
        {
            TDCell tdCell = tp.GetMainCell_TD_TDCell();
            Cell4LowSpeed cellTag = Cell4LowSpeed.GetCellTag(tp, tdCell);

            LowSpeedCellInfo curCell = null;
            if (cellInfoDic.ContainsKey(cellTag))
            {
                curCell = cellInfoDic[cellTag];
                curCell.tpTotal++;
            }
            else
            {
                curCell = new LowSpeedCellInfo(tdCell);
                cellInfoDic[cellTag] = curCell;
            }
            return curCell;
        }

        protected override void doWithDTData(TestPoint tp)
        {
            LowSpeedRegionInfo curRegion = getCurRegion(tp);
            LowSpeedCellInfo curCell = getCurCell(tp);

            //用于天线故障判断,保留最近N个采样点
            data.AddAntennaList(tp);

            if (!isLowSpeed(tp)) return;

            //如果没找到原因，暂时放入pending表中，等最后进行处理
            string reason = getResult(tp);
            if (reason == "其它")
            {
                string regionTag = isContainPoint(new DbPoint(tp.Longitude, tp.Latitude));
                Cell4LowSpeed cellTag = Cell4LowSpeed.GetCellTag(tp, curCell.cell);
                LowSpeedPendingPointInfo pendingInfo = new LowSpeedPendingPointInfo(tp, cellTag, regionTag);
                data.tpList_Pending.Add(pendingInfo);
                return;
            }
            addToRegion(reason, tp, ref curRegion);
            addToCell(reason, tp, ref curCell);
        }

        /// <summary>
        /// 各类采样点累计处理
        /// </summary>
        private void addToRegion(string strReason, TestPoint tp, ref LowSpeedRegionInfo curRegion)
        {
            curRegion.speedTotal++;

            if (curRegion.reasonDic.ContainsKey(strReason))
            {
                curRegion.reasonDic[strReason].speedTotal++;
                curRegion.reasonDic[strReason].reasonTPList.Add(new ReasonColorTestPoint(strReason, tp));
            }
        }

        /// <summary>
        /// 按小区归分问题详情
        /// </summary>
        public void addToCell(string strReason, TestPoint tp, ref LowSpeedCellInfo curCell)
        {
            curCell.speedTotal++;

            if (curCell.reasonDic.ContainsKey(strReason))
            {
                curCell.reasonDic[strReason].speedTotal++;
                curCell.reasonDic[strReason].reasonTPList.Add(new ReasonColorTestPoint(strReason, tp));
            }

            if (curCell.fileDic.ContainsKey(tp.FileName))
            {
                int SN = curCell.fileDic[tp.FileName].Count + 1;
                curCell.fileDic[tp.FileName].Add(new LowSpeedPointInfo(SN, strReason, tp));
            }
            else
            {
                LowSpeedPointInfo fileInfo = new LowSpeedPointInfo(1, strReason, tp);
                List<LowSpeedPointInfo> tpList = new List<LowSpeedPointInfo>();
                tpList.Add(fileInfo);
                curCell.fileDic.Add(tp.FileName, tpList);
            }
        }

        private bool isLowSpeed(TestPoint tp)
        {
            int? speed = (int?)tp["TD_APP_ThroughputDL"];
            return speed != null && speed < SpeedThreshold * 1024;
        }

        protected string getResult(TestPoint tp)
        {
            foreach (ReasonDealBase rdb in rdbList)
            {
                if (rdb.IsCheck && rdb.IsAchieve(tp, lowSpeedCondition))
                {
                    return rdb.GetReasonName();
                }
            }
            return "其它";
        }
    }

    public class LowSpeedTempData
    {
        public Dictionary<int, List<Event>> fileEventDic { get; set; }
        public List<TestPoint> tpList_Antenna { get; set; }
        public List<LowSpeedPendingPointInfo> tpList_Pending { get; set; }   //用于存放未找到原因的采样点，用于后续再次判断

        public LowSpeedTempData()
        {
            fileEventDic = new Dictionary<int, List<Event>>();
            tpList_Antenna = new List<TestPoint>();
            tpList_Pending = new List<LowSpeedPendingPointInfo>();
        }

        public void Clear()
        {
            fileEventDic.Clear();
            tpList_Antenna.Clear();
            tpList_Pending.Clear();
        }

        public void AddAntennaList(TestPoint tp)
        {
            tpList_Antenna.Add(tp);
            if (tpList_Antenna.Count > 6)    //只保留最近6个点
            {
                tpList_Antenna.RemoveAt(0);
            }
        }
    }

    public class Cell4LowSpeed : Cell4TDBler
    {
        public static Cell4LowSpeed GetCellTag(TestPoint tp, TDCell tdCell)
        {
            Cell4LowSpeed cellTag = new Cell4LowSpeed();
            if (tdCell != null)
            {
                cellTag.Name = tdCell.Name;
                cellTag.LAC = tdCell.LAC;
                cellTag.CI = tdCell.CI;
                cellTag.Longitude = tdCell.Longitude;
                cellTag.Latitude = tdCell.Latitude;
            }
            else
            {
                cellTag.Name = "未知小区";
                cellTag.LAC = tp["TD_SCell_LAC"] != null ? (int)tp["TD_SCell_LAC"] : -10000000;
                cellTag.CI = tp["TD_SCell_CI"] != null ? (int)tp["TD_SCell_CI"] : -10000000;
                cellTag.Longitude = 0;
                cellTag.Latitude = 0;
            }
            return cellTag;
        }
    }

    public class LowSpeedRegionInfo
    {
        public string regionName { get; set; }
        public int tpTotal { get; set; }
        public int speedTotal { get; set; }

        public Dictionary<string, LowSpeedReasonInfo> reasonDic { get; set; }

        public LowSpeedRegionInfo(string regionName)
        {
            this.regionName = regionName;
            this.tpTotal = 1;
            this.speedTotal = 0;
            this.reasonDic = LowSpeedReasonInfo.InitReasonDic();
        }
    }

    public class LowSpeedCellInfo
    {
        public TDCell cell { get; set; }
        public int tpTotal { get; set; }
        public int speedTotal { get; set; }

        public string areaName { get; set; }
        public string roadName { get; set; }
        public Dictionary<string, LowSpeedReasonInfo> reasonDic { get; set; }
        public Dictionary<string, List<LowSpeedPointInfo>> fileDic { get; set; }

        public LowSpeedCellInfo(TDCell cell)
        {
            this.cell = cell;
            this.tpTotal = 1;
            this.speedTotal = 0;
            this.reasonDic = LowSpeedReasonInfo.InitReasonDic();
            this.fileDic = new Dictionary<string, List<LowSpeedPointInfo>>();
        }
    }

    public class LowSpeedReasonInfo
    {
        public string reason { get; set; }
        public int speedTotal { get; set; }
        public Color color { get; set; }
        public List<ReasonColorTestPoint> reasonTPList { get; set; }

        public LowSpeedReasonInfo(string reason, Color color)
        {
            this.reason = reason;
            this.speedTotal = 0;
            this.color = color;
            this.reasonTPList = new List<ReasonColorTestPoint>();
        }

        public static Dictionary<string, LowSpeedReasonInfo> InitReasonDic()
        {
            Dictionary<string, LowSpeedReasonInfo> reasonDic = new Dictionary<string, LowSpeedReasonInfo>();

            foreach (ReasonDealBase rdb in ZTLowSpeedReasonAnaByRegion.RDBList)
            {
                reasonDic.Add(rdb.GetReasonName(), new LowSpeedReasonInfo(rdb.GetReasonName(), rdb.TPColor));
            }
            return reasonDic;
        }
    }

    public class LowSpeedPendingPointInfo
    {
        public TestPoint tp { get; set; }
        public Cell4LowSpeed cellTag { get; set; }
        public string regionTag { get; set; }

        public LowSpeedPendingPointInfo(TestPoint tp, Cell4LowSpeed cellTag, string regionTag)
        {
            this.tp = tp;
            this.cellTag = cellTag;
            this.regionTag = regionTag;
        }
    }

    public class LowSpeedPointInfo
    {
        public int SN { get; set; }
        public string reason { get; set; }
        public string tpTime { get; set; }
        public float pccpchRscp { get; set; }
        public float pccpchC2i { get; set; }
        public float dpchC2i { get; set; }
        public float hsscchC2i { get; set; }
        public float hspdschC2i { get; set; }
        public Color color { get; set; }
        public float scheduleRate { get; set; }
        public ReasonColorTestPoint reasonTP { get; set; }

        public LowSpeedPointInfo(int SN, string reason, TestPoint tp)
        {
            this.SN = SN;
            this.reason = reason;
            this.tpTime = tp.DateTimeStringWithMillisecond;

            float? pccpch_rscp = (float?)tp["TD_PCCPCH_RSCP"];
            if (pccpch_rscp != null && pccpch_rscp >= -140 && pccpch_rscp <= -10)
            {
                this.pccpchRscp = (float)pccpch_rscp;
            }

            int? pccpch_c2i = (int?)tp["TD_PCCPCH_C2I"];
            if (pccpch_c2i != null && pccpch_c2i >= -25 && pccpch_c2i <= 40)
            {
                this.pccpchC2i = (float)pccpch_c2i;
            }

            int? dpch_c2i = (int?)tp["TD_DPCH_C2I"];
            if (dpch_c2i != null && dpch_c2i >= -25 && dpch_c2i <= 30)
            {
                this.dpchC2i = (float)dpch_c2i;
            }


            int? hsscch_c2i = (int?)tp["TD_HSDPA_HS_SCCH_CI"];
            if (hsscch_c2i != null && hsscch_c2i >= -25 && hsscch_c2i <= 40)
            {
                this.hsscchC2i = (float)hsscch_c2i;
            }

            int? hspdsch_c2i = (int?)tp["TD_HSDPA_HS_PDSCH_CI"];
            if (hspdsch_c2i != null && hspdsch_c2i >= -25 && hspdsch_c2i <= 40)
            {
                this.hspdschC2i = (float)hspdsch_c2i;
            }

            float? schedule_rate = (int?)tp["TD_HSDPA_HS_ScchScheduled_Rate"];
            if (schedule_rate != null && schedule_rate >= 0 && schedule_rate <= 100)
            {
                this.scheduleRate = (float)Math.Round((float)schedule_rate, 2);
            }
            this.reasonTP = new ReasonColorTestPoint(reason, tp);
        }
    }
}
