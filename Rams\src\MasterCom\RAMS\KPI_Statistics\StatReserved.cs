﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.KPI_Statistics
{
    public enum ReservedField
    {
        None,
        FileIDs,
        FileNames
    }

    static class StatReserved
    {
        public static bool IsContainsReserved(string exp,out ReservedField reserved)
        {
            reserved = ReservedField.None;
            if (string.IsNullOrEmpty(exp))
            {
                return false;
            }
            foreach (string name in Enum.GetNames(typeof(ReservedField)))
            {
                if (exp.Contains(name))
                {
                    reserved = (ReservedField)(Enum.Parse(typeof(ReservedField), name));
                    return true;
                }
            }
            return false;
        }

    }
}
