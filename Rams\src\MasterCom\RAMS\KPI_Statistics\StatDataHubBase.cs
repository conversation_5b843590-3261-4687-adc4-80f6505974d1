﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class StatDataHubBase
    {
        protected Dictionary<int, FileInfo> fileIDDic = new Dictionary<int, FileInfo>();

        protected Dictionary<string, KPIStatDataBase> tokenStatDataDic = new Dictionary<string, KPIStatDataBase>();
        public Dictionary<string, KPIStatDataBase> TokenStatDataDic
        {
            get { return tokenStatDataDic; }
        }

        public virtual StatDataHubBase Clone()
        {
            StatDataHubBase hub = new StatDataHubBase();
            foreach (int id in fileIDDic.Keys)
            {
                hub.fileIDDic[id] = fileIDDic[id];
            }
            foreach (KPIStatDataBase statData in tokenStatDataDic.Values)
            {
                hub.tokenStatDataDic.Add(statData.Token, statData.Clone());
            }

            if (gridMatrix != null)
            {
                hub.gridMatrix = new GridMatrix<GridDataUnit>();
                foreach (GridDataUnit grid in gridMatrix)
                {
                    hub.gridMatrix[grid.RowIdx, grid.ColIdx] = grid.Clone();
                }
            }
            return hub;
        }

        public virtual void Merge(StatDataHubBase otherHub)
        {
            bool asGrid = otherHub.gridMatrix != null;
            foreach (KPIStatDataBase singleData in otherHub.tokenStatDataDic.Values)
            {
                AddStatData(singleData, false);
            }
            if (asGrid)
            {
                if (gridMatrix == null)
                {
                    gridMatrix = new GridMatrix<GridDataUnit>();
                }

                foreach (GridDataUnit gridOther in otherHub.gridMatrix)
                {
                    int rowIndex = 0;
                    int colIndex = 0;
                    GridHelper.GetIndexOfDefaultSizeGrid(gridOther.CenterLng, gridOther.CenterLat, out rowIndex, out colIndex);
                    GridDataUnit grid = gridMatrix[rowIndex, colIndex];
                    if (grid == null)
                    {
                        //grid = new GridDataUnit(gridOther.LTLng, gridOther.LTLat);
                        gridMatrix[rowIndex, colIndex] = gridOther.Clone();
                    }
                    else
                    {
                        grid.Gather(gridOther);
                    }
                }
            }
        }

        protected GridMatrix<GridDataUnit> gridMatrix = null;
        public GridMatrix<GridDataUnit> GridMatrix
        {
            get { return gridMatrix; }
        }

        public List<GridDataUnit> Grids
        {
            get
            {
                if (gridMatrix == null)
                {
                    return new List<GridDataUnit>();
                }
                return gridMatrix.Grids;
            }
        }

        public void AddStatData(KPIStatDataBase statData, bool makeGridMatrix)
        {
            KPIStatDataBase data = null;
            if (tokenStatDataDic.TryGetValue(statData.Token, out data))
            {
                data.GatherStatData(statData);
            }
            else
            {
                tokenStatDataDic.Add(statData.Token, statData.Clone());
            }

            if (makeGridMatrix)
            {
                if (gridMatrix == null)
                {
                    gridMatrix = new GridMatrix<GridDataUnit>();
                }

                int rowIndex = 0;
                int colIndex = 0;
                GridDataUnit grid = new GridDataUnit(statData.LTLng, statData.LTLat);
                GridHelper.GetIndexOfDefaultSizeGrid(grid.CenterLng, grid.CenterLat, out rowIndex, out colIndex);
                grid = gridMatrix[rowIndex, colIndex];
                if (grid == null)
                {
                    grid = new GridDataUnit(statData.LTLng, statData.LTLat);
                    gridMatrix[rowIndex, colIndex] = grid;
                }
                grid.AddStatData(statData);
            }
        }

        /// <summary>
        /// 获取
        /// </summary>
        /// <param name="fieldNameWithToken"></param>
        /// <param name="reservedArgs"></param>
        /// <returns></returns>
        public double GetFieldValue(string fieldNameWithToken, params object[] reservedArgs)
        {
            double value = double.NaN;
            string token = StatDataEvent.StaticToken;//缺省值为事件相关的token
            string keyWithoutToken = fieldNameWithToken;//缺省值为事件相关，事件相关公式没有前缀token,如evtIdCount,valuex[y]
            int splitIdx = fieldNameWithToken.IndexOf('_');
            if (splitIdx != -1)
            {
                token = fieldNameWithToken.Substring(0, splitIdx + 1);
                keyWithoutToken = fieldNameWithToken.Substring(splitIdx + 1);
            }
            KPIStatDataBase data = null;
            if (tokenStatDataDic.TryGetValue(token, out data))
            {
                List<int> servIDSet = null;
                if (reservedArgs.Length > 0)
                {
                    servIDSet = reservedArgs[0] as List<int>;
                }

                List<string> fileNameKeylist = new List<string>();
                if (reservedArgs.Length > 1 && reservedArgs[1] is List<string>)
                {
                    fileNameKeylist = reservedArgs[1] as List<string>;
                }
                value = data.GetValue(keyWithoutToken, servIDSet, fileNameKeylist);
            }
            return value;
        }

        public double CalcValueByFormula(string formula, params object[] extraParams)
        {
            double value = double.NaN;
            if (string.IsNullOrEmpty(formula))
            {
                return value;
            }

            string realFormula = formula;
            int beginIdx = formula.IndexOf("{");
            int endIdx = formula.IndexOf("}");
            if (beginIdx != -1 && endIdx != -1)
            {//  {}里面的才是需要计算的公式
                realFormula = formula.Substring(beginIdx + 1, endIdx - beginIdx - 1);
            }

            if (formula.Contains("@"))
            {//栅格比例计算公式样式：{公式}@>0.5
                value = calcGridPercentage(formula);
            }
            else if (extraParams.Length > 0 && extraParams[0] != null &&
                extraParams[0].Equals(MasterCom.RAMS.Stat.RptCell.GridAvg))
            {//区域栅格平均化，汇总指标A=（栅格1指标A+栅格2指标A+...+栅格n指标A）/栅格个数n
                value = calcAvgValue(realFormula);
            }
            else
            {
                List<int> servIDSet = new List<int>();
                if (extraParams.Length > 1 && extraParams[1] is List<int>)
                {
                    servIDSet = extraParams[1] as List<int>;
                }

                List<string> fileNameKeylist = new List<string>();
                if (extraParams.Length > 2 && extraParams[2] is List<string>)
                {
                    fileNameKeylist = extraParams[2] as List<string>;
                }
                value = calcValue(realFormula, servIDSet, fileNameKeylist);
            }
            return value;
        }
        public double CalcValueByFormula(string formula, int decPlace, params object[] extraParams)
        {
            double value = double.NaN;
            if (string.IsNullOrEmpty(formula))
            {
                return value;
            }

            string realFormula = formula;
            int beginIdx = formula.IndexOf("{");
            int endIdx = formula.IndexOf("}");
            if (beginIdx != -1 && endIdx != -1)
            {//  {}里面的才是需要计算的公式
                realFormula = formula.Substring(beginIdx + 1, endIdx - beginIdx - 1);
            }

            if (formula.Contains("@"))
            {//栅格比例计算公式样式：{公式}@>0.5
                value = calcGridPercentage(formula, decPlace);
            }
            else if (extraParams != null && extraParams.Length > 0
                && extraParams[0] != null
                && extraParams[0].Equals(MasterCom.RAMS.Stat.RptCell.GridAvg))
            {//区域栅格平均化，汇总指标A=（栅格1指标A+栅格2指标A+...+栅格n指标A）/栅格个数n
                value = calcAvgValue(realFormula, decPlace);
            }
            else
            {
                List<int> servIDSet = new List<int>();
                if (extraParams != null && extraParams.Length > 1 && extraParams[1] is List<int>)
                {
                    servIDSet = extraParams[1] as List<int>;
                }
                List<string> fileNameKeylist = new List<string>();
                if (extraParams != null && extraParams.Length > 2 && extraParams[2] is List<string>)
                {
                    fileNameKeylist = extraParams[2] as List<string>;
                }
                value = calcValue(realFormula, decPlace, servIDSet, fileNameKeylist);
            }
            return value;
        }

        private double calcAvgValue(string realFormula)
        {
            if (gridMatrix == null)
            {
                return calcValue(realFormula);
            }
            else
            {
                int validCount = 0;
                double sum = 0;
                foreach (GridDataUnit unit in gridMatrix)
                {
                    double gridValue = unit.DataHub.CalcValueByFormula(realFormula);
                    if (double.IsNaN(gridValue))
                    {
                        continue;
                    }
                    validCount++;
                    sum += gridValue;
                }
                return Math.Round(sum / validCount, 4);
            }
        }
        private double calcAvgValue(string realFormula, int decPlace)
        {
            if (gridMatrix == null)
            {
                return calcValue(realFormula, decPlace);
            }
            else
            {
                int validCount = 0;
                double sum = 0;
                foreach (GridDataUnit unit in gridMatrix)
                {
                    double gridValue = unit.DataHub.CalcValueByFormula(realFormula, decPlace);
                    if (double.IsNaN(gridValue))
                    {
                        continue;
                    }
                    validCount++;
                    sum += gridValue;
                }
                return Math.Round(sum / validCount, decPlace);
            }
        }

        /// <summary>
        /// 栅格比例计算。符合条件的栅格个数除以总的栅格数。
        /// </summary>
        /// <param name="formula"></param>
        /// <returns></returns>
        private double calcGridPercentage(string formula, params int?[] decPlace)
        {
            if (gridMatrix == null)
            {
                return double.NaN;
            }
            formula = formula.Replace(" ", "");
            string exp = formula.Substring(0, formula.IndexOf('@'));
            StringBuilder operSb = new StringBuilder();
            StringBuilder constSb = new StringBuilder();
            NewMethod(formula, operSb, constSb);
            string oper = operSb.ToString();
            string constStr = constSb.ToString();
            double constValue;
            if (string.IsNullOrEmpty(exp) || string.IsNullOrEmpty(oper) || string.IsNullOrEmpty(constStr)
                || !double.TryParse(constStr, out constValue))
            {
                return double.NaN;
            }

            double totalGrid = gridMatrix.Length;
            double validGrid = 0;
            foreach (GridDataUnit unit in gridMatrix)
            {
                double gridValue;
                if (decPlace != null && decPlace.Length > 0 && decPlace[0] != null)
                {
                    gridValue = unit.DataHub.CalcValueByFormula(exp, (int)decPlace[0]);
                }
                else
                {
                    gridValue = unit.DataHub.CalcValueByFormula(exp);
                }

                if (double.IsNaN(gridValue) || gridValue == -99999)
                {
                    totalGrid--;
                    continue;
                }

                validGrid = addValidGrid(oper, constValue, validGrid, gridValue);
            }
            if (decPlace != null && decPlace.Length > 0 && decPlace[0] != null)
            {
                return Math.Round(100.0 * validGrid / totalGrid, (int)decPlace[0]);
            }
            else
            {
                return Math.Round(100.0 * validGrid / totalGrid, 2);
            }
        }

        private static void NewMethod(string formula, StringBuilder operSb, StringBuilder constSb)
        {
            for (int i = formula.IndexOf('@') + 1; i < formula.Length; i++)
            {
                char ch = formula[i];
                if (ch == '<' || ch == '>' || ch == '=')
                {
                    operSb.Append(ch);
                }
                else
                {
                    constSb.Append(ch);
                }
            }
        }

        private static double addValidGrid(string oper, double constValue, double validGrid, double gridValue)
        {
            if (oper.Equals("<") && gridValue < constValue)
            {
                validGrid++;
            }
            else if (oper.Equals(">") && gridValue > constValue)
            {
                validGrid++;
            }
            else if (oper.Equals("=") && gridValue == constValue)
            {
                validGrid++;
            }
            else if (oper.Equals("<=") && gridValue <= constValue)
            {
                validGrid++;
            }
            else if (oper.Equals(">=") && gridValue >= constValue)
            {
                validGrid++;
            }
            else if (oper.Equals("<>") && gridValue != constValue)
            {
                validGrid++;
            }

            return validGrid;
        }

        private double calcValue(string formula, int decPlace, params object[] reservedArgs)
        {
            return Math.Round(calcValueBase(formula, reservedArgs), decPlace);
        }
        private double calcValue(string formula, params object[] reservedArgs)
        {
            return Math.Round(calcValueBase(formula, reservedArgs), 4);
        }
        private double calcValueBase(string formula, params object[] reservedArgs)
        {
            int askPos = formula.IndexOf('?');
            if (askPos != -1)//有bool?1:2运算符
            {
                int spltPos = formula.IndexOf(':');
                if (spltPos != -1)//bool?1:2运算符完备
                {
                    string askstrformula = formula.Substring(0, askPos);
                    double vAskRet = calcValue(askstrformula);
                    if (vAskRet == 1)
                    {
                        string part1str = formula.Substring(askPos + 1, spltPos - askPos - 1);
                        return calcValue(part1str);
                    }
                    else//part 2
                    {
                        string part2str = formula.Substring(spltPos + 1, formula.Length - spltPos - 1);
                        return calcValue(part2str);
                    }
                }
            }
            StringBuilder sb = new StringBuilder();
            int len = formula.Length;
            StringBuilder tokenSB = new StringBuilder();
            bool tokenStarted = false;
            StringBuilder arg = new StringBuilder();
            bool argStarted = false;
            int i = 0;
            while (i < len)
            {
                char ch = formula[i];
                if (argStarted)
                {
                    if ((ch >= '0' && ch <= '9') || ch == ',')
                    {
                        arg.Append(ch);
                        i++;
                        continue;
                    }
                    if (ch == ']')
                    {
                        string evtExp = string.Format(tokenSB.ToString() + "[{0}]", arg.ToString());
                        object v = GetFieldValue(evtExp, reservedArgs);
                        i++;
                        tokenSB.Remove(0, tokenSB.Length);//clear it
                        tokenStarted = false;
                        sb.Append("(" + v + ")");
                        arg = new StringBuilder();
                        argStarted = false;
                        continue;
                    }
                    throw new InvalidOperationException("公式有误");
                }
                if (ch == '(')
                {
                    if (tokenStarted)
                    {
                        int effected = tokenSB.Length + 1;
                        return i - effected;
                    }
                    sb.Append(ch);
                    i++;
                    continue;
                }
                else if (ch == ')')
                {
                    if (tokenStarted)
                    {
                        object v = GetFieldValue(tokenSB.ToString(), reservedArgs);
                        i++;
                        tokenSB.Remove(0, tokenSB.Length);
                        sb.Append("(" + v + ")");
                        tokenStarted = false;
                        sb.Append(ch);
                        continue;
                    }
                    else
                    {
                        sb.Append(ch);
                        i++;
                        continue;
                    }
                }
                if (ch == '+' || ch == '-' || ch == '*' || ch == '/')
                {
                    if (tokenStarted)
                    {
                        object v = GetFieldValue(tokenSB.ToString(), reservedArgs);
                        i++;
                        tokenSB.Remove(0, tokenSB.Length);
                        sb.Append("(" + v + ")");
                        tokenStarted = false;
                        sb.Append(ch);
                        continue;
                    }
                    else
                    {
                        sb.Append(ch);
                        i++;
                        continue;
                    }
                }
                if (ch == '[')
                {
                    if (tokenStarted)
                    {
                        argStarted = true;
                        i++;
                        continue;
                    }
                    else
                    {
                        return double.NaN;
                    }
                }
                if (tokenStarted)
                {
                    if (ch >= '0' && ch <= '9' || ch >= 'a' && ch <= 'z' || ch >= 'A' && ch <= 'Z' || ch == '_')
                    {
                        tokenSB.Append(ch);
                        i++;
                    }
                    else if (ch == ' ')
                    {
                        object v = GetFieldValue(tokenSB.ToString(), reservedArgs);
                        i++;
                        tokenSB.Remove(0, tokenSB.Length);
                        sb.Append("(" + v + ")");
                        tokenStarted = false;
                        sb.Append(ch);
                    }
                    else
                    {
                        return double.NaN;
                    }
                }
                else
                {
                    if (ch >= 'a' && ch <= 'z' || ch >= 'A' && ch <= 'Z')
                    {
                        tokenStarted = true;
                        tokenSB.Append(ch);
                        i++;
                    }
                    else
                    {
                        sb.Append(ch);
                        i++;
                    }
                }
            }
            if (tokenStarted)
            {
                if (argStarted)
                {
                    int effected = arg.Length + 1;
                    return formula.Length - effected;
                }
                object v = GetFieldValue(tokenSB.ToString(), reservedArgs);
                tokenSB.Remove(0, tokenSB.Length);
                sb.Append("(" + v + ")");
            }
            string expression = sb.ToString();

            double value = double.NaN;
            string pattern = $@"((\+|\-|\*|\/)\s*\({double.NaN})|({double.NaN}\)\s*(\+|\-|\*|\/))";
            bool isMatch = System.Text.RegularExpressions.Regex.IsMatch(expression, pattern);
            if (isMatch)
            {
                expression = expression.Replace(string.Format("{0}", double.NaN), "0");
            }

            if (expression.Contains(">") || expression.Contains("<"))
            {
                MasterCom.Util.ExpressionEx parser = new MasterCom.Util.ExpressionEx();
                string str = parser.ParseCommand(expression);
                double temp;
                if (double.TryParse(str, out temp))
                {
                    value = temp;
                }
            }
            else
            {
                value = MasterCom.Util.SimpleCalculator.Compute(expression);
            }
            return value;
        }

        internal KPIStatDataBase GetStatData(Type type)
        {
            foreach (KPIStatDataBase data in tokenStatDataDic.Values)
            {
                if (data.GetType().Equals(type))
                {
                    return data;
                }
            }
            return null;
        }
    }
}
