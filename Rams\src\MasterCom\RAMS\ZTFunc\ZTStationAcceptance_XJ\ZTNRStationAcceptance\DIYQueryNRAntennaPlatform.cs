﻿using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class DIYQueryNRAntennaPlatform : DiyQueryDataBase
    {
        public string tableName { get; set; } = "tb_xinjiang_NRAntennaPlatform";
        public List<NRAntennaPlatformDBInfo> NRAntennaPlatformDBInfoList { get; private set; }

        public DIYQueryNRAntennaPlatform()
            : base()
        { }

        public override string Name { get { return "查询NR天姿平台"; } }

        protected override string getSqlTextString()
        {
            string name = $"{tableName}_" + DateTime.Now.ToString("yyyyMMdd");
            StringBuilder selectSQL = new StringBuilder();
            selectSQL.AppendFormat(@"SELECT [基站名称],[小区名称],[主站经度],[主站纬度],[经度],[纬度],[天线挂高],[方位角]
,[机械下倾角] FROM {0} where [基站名称]='{1}'", name, btsName);

            return selectSQL.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[9];
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Float;
            rType[idx++] = E_VType.E_Float;
            rType[idx] = E_VType.E_Float;
            return rType;
        }

        protected override void initData()
        {
            NRAntennaPlatformDBInfoList = new List<NRAntennaPlatformDBInfo>();
        }

        protected override void dealReceiveData(Package package)
        {
            NRAntennaPlatformDBInfo info = new NRAntennaPlatformDBInfo();
            info.FillData(package);
            NRAntennaPlatformDBInfoList.Add(info);
        }
    }

    public class NRAntennaPlatformDBInfo
    {
        public string BtsName { get; set; }
        public string CellName { get; set; }
        public double BtsLongitude { get; set; }
        public double BtsLatitude { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        /// <summary>
        /// 天线挂高
        /// </summary>
        public double Altitude { get; set; }
        /// <summary>
        /// 方位角
        /// </summary>
        public double Direction { get; set; }

        /// <summary>
        /// 机械下倾角
        /// </summary>
        public double MechanicalTilt { get; set; }

        public void FillData(Package package)
        {
            BtsName = package.Content.GetParamString();
            CellName = package.Content.GetParamString();
            BtsLongitude = package.Content.GetParamInt() / 10000000d;
            BtsLatitude = package.Content.GetParamInt() / 10000000d;
            Longitude = package.Content.GetParamInt() / 10000000d;
            Latitude = package.Content.GetParamInt() / 10000000d;
            Altitude = package.Content.GetParamFloat();
            Direction = package.Content.GetParamFloat();
            MechanicalTilt = package.Content.GetParamFloat();
        }
    }
}
