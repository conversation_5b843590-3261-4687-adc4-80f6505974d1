using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using DevExpress.XtraGrid.Views.Grid;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTCellCheckInfoForm : MinCloseForm
    {
        public CQTCellCheckInfoForm(MainModel mm):base(mm)
        {
            InitializeComponent();
        }

        public void FillData(Dictionary<CQTPoint, CQTCellCheckInfo> cqtPointList)
        {
            List<CQTCellCheckInfo> pointInfoList = new List<CQTCellCheckInfo>(cqtPointList.Values);
            gridControl1.DataSource = pointInfoList;
        }

        private void gridViewPnt_DoubleClick(object sender, EventArgs e)
        {
            CQTCellCheckInfo pnt = gridViewPnt.GetRow(gridViewPnt.FocusedRowHandle) as CQTCellCheckInfo;
            if (pnt!=null)
            {
                MapForm mf=MainModel.MainForm.GetMapForm();
                if (mf!=null)
                {
                    mf.GoToView(pnt.CqtPoint.Longitude, pnt.CqtPoint.Latitude, 8000);
                }
            }
        }

        private void gridViewCell_DoubleClick(object sender, EventArgs e)
        {
            //
        }

        private void miReplayFiles_Click(object sender, EventArgs e)
        {
            GridView gv = gridControl1.FocusedView as GridView;
            if (gv!=null)
            {
                CQTCellCheckInfo pnt = gv.GetRow(gv.FocusedRowHandle) as CQTCellCheckInfo;
                if (pnt != null)
                {
                    List<FileInfo> files = new List<FileInfo>();
                    foreach (CQTFileInfo fi in pnt.CqtFileInfoList)
                    {
                        files.Add(fi.FileInfo);
                    }
                    QueryCondition qc = new QueryCondition();
                    qc.FileInfos = files;
                    DIYReplayFileQuery qry = new DIYReplayFileQuery(MainModel);
                    qry.SetQueryCondition(qc);
                    qry.Query();
                }
                else
                {
                    CQTFileInfo fileInfo = gv.GetRow(gv.FocusedRowHandle) as CQTFileInfo;
                    if (fileInfo!=null)
                    {
                        List<FileInfo> files = new List<FileInfo>();
                        files.Add(fileInfo.FileInfo);
                        QueryCondition qc = new QueryCondition();
                        qc.FileInfos = files;
                        DIYReplayFileQuery qry = new DIYReplayFileQuery(MainModel);
                        qry.SetQueryCondition(qc);
                        qry.Query();
                    }
                }
            }
        }

        private void gridViewCell_CustomDrawCell(object sender, DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e)
        {
            CQTCellInfo cellInfo = ((GridView)sender).GetRow(e.RowHandle) as CQTCellInfo;
            if (cellInfo != null && cellInfo.IsLight)
            {
                DevExpress.Utils.AppearanceDefault appRed = new DevExpress.Utils.AppearanceDefault(
                Color.Black, Color.Red, Color.Empty, Color.SeaShell, System.Drawing.Drawing2D.LinearGradientMode.Horizontal);
                DevExpress.Utils.AppearanceHelper.Apply(e.Appearance, appRed);
            }
        }
    }
}