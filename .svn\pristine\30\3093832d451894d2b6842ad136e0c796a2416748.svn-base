﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class HandoverBehindTimeSettingDlg_NR : BaseForm
    {
        public HandoverBehindTimeSettingDlg_NR(HandoverBehindTimeCondition_NR condition)
            : base()
        {
            InitializeComponent();
            initUIValue(condition);
        }

        private void initUIValue(HandoverBehindTimeCondition_NR condition)
        {
            if (condition == null)
            {
                return;
            }
            numNrSvrRsrp.Value = (decimal)condition.HandoverCondition_NR.SvrPccpchMax;
            numNrMaxNCellRsrp.Value = (decimal)condition.HandoverCondition_NR.NCellPccpch;
            numNrRsrpDiff.Value = (decimal)condition.HandoverCondition_NR.PccpchDiffMin;
            numNrStaySecond.Value = (decimal)condition.HandoverCondition_NR.StaySecondsMin;
            chkNrConnect.Checked = condition.HandoverCondition_NR.CheckType;

            chkLteHandover.Checked = condition.IsAnaLteHandover;
            numLteSvrRsrp.Value = (decimal)condition.HandoverCondition_LTE.SvrPccpchMax;
            numLteMaxNCellRsrp.Value = (decimal)condition.HandoverCondition_LTE.NCellPccpch;
            numLteRsrpDiff.Value = (decimal)condition.HandoverCondition_LTE.PccpchDiffMin;
            numLteStaySecond.Value = (decimal)condition.HandoverCondition_LTE.StaySecondsMin;
            chkLteConnect.Checked = condition.HandoverCondition_LTE.CheckType;
        }

        public HandoverBehindTimeCondition_NR GetCondition()
        {
            HandoverBehindTimeCondition_NR condition = new HandoverBehindTimeCondition_NR();
            condition.HandoverCondition_NR = new NrHandoverBehindTimeCond_NR((float)numNrSvrRsrp.Value
                , (float)numNrMaxNCellRsrp.Value, (float)numNrRsrpDiff.Value, (int)numNrStaySecond.Value
                , chkNrConnect.Checked);

            condition.IsAnaLteHandover = chkLteHandover.Checked;
            condition.HandoverCondition_LTE = new NrHandoverBehindTimeCond_LTE((float)numLteSvrRsrp.Value
                , (float)numLteMaxNCellRsrp.Value, (float)numLteRsrpDiff.Value, (int)numLteStaySecond.Value
                , chkLteConnect.Checked);
            return condition;
        }

        private void chkLteHandover_CheckedChanged(object sender, EventArgs e)
        {
            groupBoxLTE.Enabled = chkLteHandover.Checked;
        }
    }

}
