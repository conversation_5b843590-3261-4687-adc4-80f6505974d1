﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat;

namespace MasterCom.RAMS.KPI_Statistics
{
    public partial class NewReportDlg : BaseDialog
    {
        List<string> oldNames = null;
        public NewReportDlg(List<string> oldNames)
            : base()
        {
            InitializeComponent();
            this.oldNames = oldNames;
            txtName.SelectAll();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            string name = txtName.Text;
            if (name.Trim().Length == 0)
            {
                MessageBox.Show("报表名称不能为空！");
                return;
            }
            if (!KPIReportManager.Instance.IsReportCanEdit(name))
            {
                return;
            }

            if (oldNames.Contains(name))
            {
                MessageBox.Show("已存在相同名称的报表，请输入其它名称！");
                return;
            }
            DialogResult = DialogResult.OK;
        }

        public ReportStyle Report
        {
            get
            {
                RptCell cell = new RptCell();
                cell.rowAt = (int)numRow.Value - 1;
                cell.colAt = (int)numCol.Value - 1;
                ReportStyle rpt = new ReportStyle();
                rpt.name = txtName.Text;
                rpt.AddCell(cell);
                return rpt;
            }
        }






    }
}
