﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.ES.Data;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using DTParameter = MasterCom.RAMS.Model.DTParameter;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCellCoverLapByRegion_W : ZTCellCoverLapByRegion
    {
        private static ZTCellCoverLapByRegion_W intance = null;
        public new static ZTCellCoverLapByRegion_W GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTCellCoverLapByRegion_W();
                    }
                }
            }
            return intance;
        }
        protected ZTCellCoverLapByRegion_W()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.WCDMA_VOICE);
            carrierID = CarrierType.ChinaUnicom;
        }
        public override string Name
        {
            get { return "过覆盖分析"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 14000, 14021, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            DIYSampleGroup leakGroup = new DIYSampleGroup();
            DTParameter parameter = DTParameterManager.GetInstance().GetParameter("W_SysLAI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("W_SysCellID");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("W_frequency");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("W_Reference_PSC");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("W_TotalRSCP");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            leakGroup.ThemeName = "WCDMA_RSCP";
            return leakGroup;
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            try
            {
                bool inRegion = Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
                //进行过覆盖算法运算
                if (inRegion && tp is WCDMATestPointDetail)
                {
                    float? pccpchRSCP = (float?)tp["W_TotalRSCP"];
                    if (pccpchRSCP == null)
                    {
                        return false;
                    }
                    if (pccpchRSCP < curFilterRxlev)
                    {
                        return false;
                    }
                    WCell cell = CellManager.GetInstance().GetNearestWCell(tp.DateTime, (int?)tp["W_SysLAI"], (int?)tp["W_SysCellID"],
                        (int?)tp["W_frequency"], (int?)tp["W_Reference_PSC"], tp.Longitude, tp.Latitude);
                    if (cell != null)
                    {
                        return judgeCoverLap(tp, pccpchRSCP, cell);
                    }
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        private bool judgeCoverLap(TestPoint tp, float? pccpchRSCP, WCell cell)
        {
            if (cell.Antennas.Count == 0)
            {
                return false;//忽略掉
            }
            if (cell.Type != WNodeBType.Outdoor)
            {
                return false;
            }
            CellCoverLap_W covLap = getCellCoverLap(cell);
            double distanceToCell = MathFuncs.GetDistance(tp.Longitude, tp.Latitude, cell.Longitude, cell.Latitude);
            bool isBadCheck = distanceToCell > covLap.rationalDistance;
            if (isBadCheck)
            {
                covLap.AddBadSample(tp, distanceToCell, (float)pccpchRSCP);
                return true;
            }
            else
            {
                covLap.goodSampleCount++;
                return false;
            }
        }

        private CellCoverLap_W getCellCoverLap(WCell cell)
        {
            CellCoverLap_W covLap = null;
            CellCoverLap clTmp = null;
            if (!cellLapRetDic.TryGetValue(cell.Name, out clTmp))
            {
                double radiusOfCell = CfgDataProvider.CalculateRadius(cell, nearestCellCount);
                covLap = new CellCoverLap_W();
                covLap._CellCovRadius = radiusOfCell;
                covLap.rationalDistance = radiusOfCell * disFactor;
                covLap.wCell = cell;
                covLap.nearestBTSs = CfgDataProvider.GetNearestBTSs(cell, nearestCellCount);
                covLap.mnger = new DTDataManager(MainModel.GetInstance());
                cellLapRetDic[cell.Name] = covLap;
            }
            else
            {
                covLap = (CellCoverLap_W)clTmp;
            }

            return covLap;
        }

        protected override void getResultAfterQuery()
        {
            FilterCellCoverLap();
        }
    };
}
