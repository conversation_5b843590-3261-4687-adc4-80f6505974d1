﻿using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;

namespace MasterCom.RAMS.Model
{
    public class NRCell : Snapshot<NRCell>, ICell, IComparable<NRCell>
    {
        public NRCell()
        {
            Value = this;
        }

        #region 小区属性
        public string Name { get; set; }
        public string Code { get; set; }
        public int CellID { get; set; }
        public int SectorID { get; set; }
        public int TAC { get; set; }
        public long NCI { get; set; }
        public int ARFCN { get; set; }
        public int PCI { get; set; }
        public int SSBARFCN { get; set; }
        public string CGI { get; set; }
        public string DESC { get; set; }
        public Color ServerCellColor { get; set; }

        //private readonly List<LTEAntenna> antennas = new List<LTEAntenna>();
        private readonly List<Cell> neighbourGsmCells = new List<Cell>();
        private readonly List<WCell> neighbourWcdmaCells = new List<WCell>();
        private readonly List<LTECell> neighbourLteCells = new List<LTECell>();
        private readonly List<NRCell> neighbourNrCells = new List<NRCell>();

        public string Token
        {
            get { return string.Format("{0}_{1}", TAC, NCI); }
        }

        public string DetailInfo
        {
            get
            {
                StringBuilder info = new StringBuilder();
                info.Append(this.BelongBTS.DetailInfo);
                info.Append("\r\nName:").Append(Name);
                info.Append("\r\nCode:").Append(Code);
                info.Append("\r\nTAC:").Append(TAC);
                info.Append("\r\nNCI:").Append(NCI);
                info.Append("\r\n扇区号:").Append(SectorID.ToString());
                info.Append("\r\n主频:").Append(SSBARFCN);
                info.Append("\r\n扰码:").Append(PCI);
                info.Append("\r\n站高:").Append(Altitude);
                info.Append("\r\n下倾角:").Append(Downward);
                info.Append("\r\n方向角:").Append(Direction);
                info.Append("\r\nCGI:").Append(CGI);
                info.Append("\r\n描述:").Append(DESC);
                info.Append("\r\n");
                return info.ToString();
            }
        }
        #endregion

        #region 基站属性
        public NRBTS BelongBTS { get; set; }
        public string BTSName
        {
            get { return BelongBTS.Name; }
        }

        public NRBTSType Type
        {
            get { return BelongBTS.Type; }
        }

        public ISite Site
        {
            get { return BelongBTS; }
        }

        public double Longitude
        {
            get
            {
                if (MainModel.GetInstance().SystemConfigInfo.IsBtsLngLat)
                {
                    return BelongBTS.Longitude;
                }
                else
                {
                    return Antennas.Count > 0 ? Antennas[0].Longitude : 0;
                }
            }
        }

        public double Latitude
        {
            get
            {
                if (MainModel.GetInstance().SystemConfigInfo.IsBtsLngLat)
                {
                    return BelongBTS.Latitude;
                }
                else
                {
                    return Antennas.Count > 0 ? Antennas[0].Latitude : 0;
                }
            }
        }

        public double EndPointLongitude
        {
            get
            {
                if (this.Type == NRBTSType.Indoor)  //室内
                {
                    return Longitude;
                }
                else
                {
                    double offset = Math.Cos(-(Direction - 90) * Math.PI / 180) * 0.000024
                            * MasterCom.RAMS.Func.MapLTECellLayer.CellDefaultDisplayLength
                            * MasterCom.RAMS.Func.MapLTECellLayer.ShapeLengthScale;
                    return Longitude + offset;
                }
            }
        }

        public double EndPointLatitude
        {
            get
            {
                if (this.Type == NRBTSType.Indoor)  //室内
                {
                    return Latitude;
                }
                else
                {
                    double offset = Math.Sin(-(Direction - 90) * Math.PI / 180) * 0.000024
                            * MasterCom.RAMS.Func.MapLTECellLayer.CellDefaultDisplayLength
                            * MasterCom.RAMS.Func.MapLTECellLayer.ShapeLengthScale;
                    return Latitude + offset;
                }
            }
        }
        #endregion

        #region 天线属性
        public List<NRAntenna> Antennas { get; private set; } = new List<NRAntenna>();
        public short Direction
        {
            get { return Antennas.Count > 0 ? Antennas[0].Direction : (short)0; }
        }

        public short Downward
        {
            get { return Antennas.Count > 0 ? Antennas[0].Downward : (short)0; }
        }

        public int Altitude
        {
            get { return Antennas.Count > 0 ? Antennas[0].Altitude : (short)0; }
        }
        #endregion

        public void Fill(MasterCom.RAMS.Net.Content content, CellManager cellManager)
        {
            base.Fill(content.GetParamInt(), content.GetParamInt(), content.GetParamInt());
            Code = content.GetParamString();
            Name = content.GetParamString();
            int btsID = content.GetParamInt();
            foreach (NRBTS bts in cellManager.GetNRBTSs(btsID, ValidPeriod))
            {
                if (bts.ValidPeriod.Contains(ValidPeriod.BeginTime) || ValidPeriod.IsIntersect(bts.ValidPeriod))
                {
                    bts.Cells.Add(this);
                    BelongBTS = bts;
                }
            }

            TAC = content.GetParamInt();
            NCI = NRDTDataHelper.AnalyseParamNCI(content);
            CellID = content.GetParamInt();
            SectorID = content.GetParamInt();
            PCI = content.GetParamInt();
            ARFCN = content.GetParamInt();
            SSBARFCN = content.GetParamInt();
            CGI = content.GetParamString();
            DESC = content.GetParamString();

            //SCellID = (NCI / 256) * 10 + SectorID;
        }

        public bool Within(double x1, double y1, double x2, double y2)
        {
            if (Longitude < x1 || Longitude > x2 || Latitude < y1 || Latitude > y2)
            {
                return false;
            }
            return true;
        }

        public bool Within(MTGis.DbRect dRect)
        {
            if (Longitude < dRect.x1 || Longitude > dRect.x2 || Latitude < dRect.y1 || Latitude > dRect.y2)
            {
                return false;
            }
            return true;
        }

        public int CompareTo(NRCell other)
        {
            if (other == null)
            {
                return 1;
            }
            return this.Name.CompareTo(other.Name);
        }

        public double GetDistance(double x, double y)
        {
            if (this.Antennas.Count == 0)
            {
                return MathFuncs.GetDistance(Longitude, Latitude, x, y);
            }
            else
            {
                double dDistance = double.MaxValue;
                foreach (NRAntenna antenna in this.Antennas)
                {
                    double dDistanceTmp = MathFuncs.GetDistance(antenna.Longitude, antenna.Latitude, x, y);
                    if (dDistanceTmp < dDistance)
                    {
                        dDistance = dDistanceTmp;
                    }
                }
                return dDistance;
            }
        }


        public List<Cell> NeighbourGSMCells
        {
            get { return neighbourGsmCells; }
        }

        public List<WCell> NeighbourWCDMACells
        {
            get { return neighbourWcdmaCells; }
        }

        public List<LTECell> NeighbourLTECells
        {
            get { return neighbourLteCells; }
        }

        public List<NRCell> NeighbourNRCells
        {
            get { return neighbourNrCells; }
        }

        public void AddNeighbourCell(Cell cell)
        {
            if (!neighbourGsmCells.Contains(cell))
            {
                neighbourGsmCells.Add(cell);
            }
        }
        public void AddNeighbourCell(WCell cell)
        {
            if (!neighbourWcdmaCells.Contains(cell))
            {
                neighbourWcdmaCells.Add(cell);
            }
        }
        public void AddNeighbourCell(LTECell cell)
        {
            if (!neighbourLteCells.Contains(cell))
            {
                neighbourLteCells.Add(cell);
            }
        }
        public void AddNeighbourCell(NRCell cell)
        {
            if (!neighbourNrCells.Contains(cell))
            {
                neighbourNrCells.Add(cell);
            }
        }









        public NRBandType BandType
        {
            get
            {
                return NRCell.GetBandTypeByArfcn(ARFCN);
            }
        }


        public static NRBandType GetBandTypeByArfcn(int arfcn)
        {
            NRBandType band = NRBandType.Undefined;
            if (503000 <= arfcn && arfcn <= 535000)
            {
                band = NRBandType.移动Band41;
            }
            else if (720000 <= arfcn && arfcn <= 726667)
            {
                band = NRBandType.移动Band79;
            }
            else if (633333 <= arfcn && arfcn <= 640000)
            {
                band = NRBandType.联通Band78;
            }
            else if (626667 <= arfcn && arfcn <= 633333)
            {
                band = NRBandType.电信Band78;
            }
            return band;
        }
    }

    public enum NRBandType
    {
        Undefined,
        移动Band41,
        移动Band79,
        联通Band78,
        电信Band78
    }
}
