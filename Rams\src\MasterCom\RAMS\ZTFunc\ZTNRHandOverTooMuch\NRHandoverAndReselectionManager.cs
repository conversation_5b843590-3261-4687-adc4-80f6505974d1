﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public static class NRHandoverAndReselectionManager
    {
        /// <summary>
        /// 获取切换(重选)过频繁结果
        /// </summary>
        /// <param name="dtFileDataManager">文件列表</param>
        /// <param name="timeLimit">时间限制(多少秒内发生的)</param>
        /// <param name="distanceLimit">距离限制</param>
        /// <param name="handoverCount">切换次数</param>
        /// <param name="handoverEvents">输出的问题点涉及到的事件列表</param>
        /// <returns>形成过频繁问题的文件列表</returns>
        public static NRHandoverFileDataManager GetHandoverTooMuchResult(DTFileDataManager dtFileDataManager, int timeLimit, int distanceLimit,
            int handoverCount)
        {
            DTFileDataManager curDTFileDataManager = new DTFileDataManager(dtFileDataManager.FileID, dtFileDataManager.FileName,
                dtFileDataManager.ProjectType, dtFileDataManager.TestType, dtFileDataManager.CarrierType, dtFileDataManager.LogTable,
                dtFileDataManager.SampleTableName, dtFileDataManager.ServiceType, dtFileDataManager.MoMtFlag);
            NRHandoverFileDataManager item = new NRHandoverFileDataManager(curDTFileDataManager);
            List<Event> events;
            List<List<Event>> eventsList;
            //在切换的事件中查询频繁切换的事件并记录
            if (isTooFrequent(dtFileDataManager.Events, timeLimit, distanceLimit, handoverCount, out events, out eventsList))
            {
                foreach (Event e in events)
                {
                    curDTFileDataManager.Add(e);
                }
                item.Events.AddRange(events);
                item.EventsList = eventsList;
                item.HandoverTimes = eventsList.Count;
            }
            return item;
        }

        //判断是否有对应条件下的过频繁事件发生
        private static bool isTooFrequent(List<Event> events, int secondLimit, int distanceLimit, int timesLimit, out List<Event> resultEvents,
            out List<List<Event>> eventsList)
        {
            return NRHandoverFileDataManager.isTooFrequent(events, secondLimit, distanceLimit, timesLimit,
                out resultEvents, out eventsList);
        }

        /// <summary>
        /// 根据频繁切换事件和采样点获取对应的详细数据并保存
        /// </summary>
        /// <param name="handoverFile"></param>
        /// <param name="fileDataManager"></param>
        public static void GetHandoverToMuchDetails(NRHandoverFileDataManager handoverFile, DTFileDataManager fileDataManager)
        {
            if (handoverFile.EventsList.Count <= 0 || fileDataManager == null)
                return;

            for (int i = 0; i < handoverFile.Events.Count; i++)
            {
                Event ev = handoverFile.Events[i];
                List<NRHandoverCellItem> hoCellList = statHandoverCells(fileDataManager.TestPoints, ev);
                NRHandoverItem handoverItem = new NRHandoverItem(ev);
                foreach (NRHandoverCellItem cellItem in hoCellList)
                {
                    if (cellItem == null) continue;
                    cellItem.Ev = ev;
                    if (cellItem.CellType == NRHandoverCellType.BeforeHandover)
                        handoverItem.SetCellItemBefore(cellItem);
                    else
                        handoverItem.SetCellItemAfter(cellItem);
                }
                handoverFile.HandoverEventDic[ev] = handoverItem;
            }
            getHandoverItems(handoverFile);
            fileDataManager.TestPoints.Clear();
        }

        #region statHandoverCells
        /// <summary>
        /// 根据切换事件获取切换前后小区数据
        /// </summary>
        /// <param name="dtfdm"></param>
        /// <param name="e"></param>
        /// <returns></returns>
        private static List<NRHandoverCellItem> statHandoverCells(List<TestPoint> testPoints, Event e)
        {
            List<NRHandoverCellItem> hoCellList = new List<NRHandoverCellItem>();
            NRHandOverType type = NREventHelper.HandoverHelper.GetHandoverType(e.ID, false);
            if (type == NRHandOverType.UNKNOWN)
            {
                return hoCellList;
            }

            NRHandoverCellItem tps1 = new NRHandoverCellItem(e, NRHandoverCellType.BeforeHandover, type);  //切换前
            NRHandoverCellItem tps2 = new NRHandoverCellItem(e, NRHandoverCellType.AfterHandover, type);  //切换后

            hoCellList.Add(tps1);
            hoCellList.Add(tps2);

            int index = getChangeTestpointIndex(testPoints, e);
            if (index == -1)
            {
                return hoCellList;
            }

            TestPoint tp;

            //根据Index统计相关的点
            for (int i = index; i >= 0; i--)
            {//切换前3秒
                tp = testPoints[i];

                if (!checkTestPoint(tp, e, NRHandoverCellType.BeforeHandover, tps1, type))
                {
                    break;
                }
                AddTp2CellItem(tps1, tp, type);
            }

            for (int i = index + 1; i < testPoints.Count; i++)
            {
                tp = testPoints[i];

                if (!checkTestPoint(tp, e, NRHandoverCellType.AfterHandover, tps2, type))//切换后3秒
                {
                    break;
                }
                AddTp2CellItem(tps2, tp, type);
            }
            return hoCellList;
        }

        /// <summary>
        /// 查找切换点的索引
        /// </summary>
        /// <param name="cellItemAfter">TestPoint 集合</param>
        /// <param name="events">Event  集合</param>
        /// <returns></returns>
        private static int getChangeTestpointIndex(List<TestPoint> tpList, Event e)
        {
            int index = -1;
            for (int i = 0; i < tpList.Count; i++)
            {
                if (tpList[i].SN > e.SN)
                {
                    index = i - 1;
                    break;
                }
                if (tpList[i].SN == e.SN)
                {
                    index = i;
                    break;
                }
            }

            return index;
        }

        /// <summary>
        /// 检测是否属于有效范围内的切换点
        /// 1、切换前后 3 秒
        /// </summary>
        /// <param name="tp">切换所在的点</param>
        /// <param name="e">切换事件</param>
        /// <returns>true or false</returns>
        private static bool checkTestPoint(TestPoint tp, Event e, NRHandoverCellType cellType, NRHandoverCellItem item, NRHandOverType type)
        {
            bool isFlag = false;
            int? earfcn = getEarfcn(tp, type);
            int? pci = getPci(tp, type);
            int? eEarfcn = item.EARFCN;
            int? ePci = item.PCI;

            if (earfcn == null || pci == null)
            {
                return isFlag;
            }

            long timeTestpoint = tp.Time * 1000L + tp.Millisecond;
            long tiemEvent = e.Time * 1000L + e.Millisecond;

            if ((cellType == NRHandoverCellType.BeforeHandover)
                    && ((timeTestpoint + 3 * 1000) >= tiemEvent)//比较时间范围(小于它的3秒内）
                && earfcn == eEarfcn && pci == ePci)
            {
                isFlag = true;
            }
            else if ((cellType == NRHandoverCellType.AfterHandover)
                        && ((timeTestpoint - 3 * 1000) <= tiemEvent)) //比较时间范围(大于它的3秒内）
            {
                isFlag = true;
            }

            return isFlag;
        }

        private static int? getEarfcn(TestPoint tp, NRHandOverType cellType)
        {
            int? earfcn = null;
            if (cellType == NRHandOverType.LTE)
            {
                earfcn = (int?)NRTpHelper.NrLteTpManager.GetEARFCN(tp);
            }
            else if (cellType == NRHandOverType.NSA || cellType == NRHandOverType.SA)
            {
                earfcn = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
            }

            return earfcn;
        }

        private static int? getPci(TestPoint tp, NRHandOverType cellType)
        {
            int? pci = null;
            if (cellType == NRHandOverType.LTE)
            {
                pci = (int?)NRTpHelper.NrLteTpManager.GetPCI(tp);
            }
            else if (cellType == NRHandOverType.NSA || cellType == NRHandOverType.SA)
            {
                pci = (int?)NRTpHelper.NrTpManager.GetPCI(tp);
            }
            return pci;
        }

        /// <summary>
        /// 采样点算入相应小区信息
        /// </summary>
        /// <param name="cellItem">小区信息对象</param>
        /// <param name="tp">TestPoint</param>
        private static void AddTp2CellItem(NRHandoverCellItem cellItem, TestPoint tp, NRHandOverType type)
        {
            int? earfcn = getEarfcn(tp, type);
            int? pci = getPci(tp, type);

            if (earfcn == null || pci == null || earfcn == 0 || pci == 0)
            {
                return;
            }

            if (cellItem.EARFCN == earfcn && cellItem.PCI == pci) // || cellItem.Ev.ID == 853 || cellItem.Ev.ID == 3172)
            {
                if (type == NRHandOverType.LTE)
                {
                    setNRLTECellItem(cellItem, tp);
                }
                else if (type == NRHandOverType.NSA || type == NRHandOverType.SA)
                {
                    setNRCellItem(cellItem, tp);
                }
            }
        }


        private static void setNRCellItem(NRHandoverCellItem cellItem, TestPoint tp)
        {
            float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp, true);
            float? sinr = NRTpHelper.NrTpManager.GetSCellSinr(tp, true);
            if (rsrp != null)
            {
                cellItem.RSRP += (float)rsrp;
                cellItem.RSRPCount++;
            }
            if (rsrp != null)
            {
                cellItem.SINR += (float)sinr;
                cellItem.SINRCount++;
            }
        }

        private static void setNRLTECellItem(NRHandoverCellItem cellItem, TestPoint tp)
        {
            float? rsrp = NRTpHelper.NrLteTpManager.GetSCellRsrp(tp, true);
            float? sinr = NRTpHelper.NrLteTpManager.GetSCellSinr(tp, true);
            if (rsrp != null)
            {
                cellItem.RSRP += (float)rsrp;
                cellItem.RSRPCount++;
            }
            if (sinr != null)
            {
                cellItem.SINR += (float)sinr;
                cellItem.SINRCount++;
            }
        }

        //private static int getChangeEventIndex(List<Event> evtList, Event e)
        //{
        //    int index = -1;
        //    for (int i = 0; i < evtList.Count; i++)
        //    {
        //        if (evtList[i].SN > e.SN)
        //        {
        //            index = i - 1;
        //            break;
        //        }
        //        if (evtList[i].SN == e.SN)
        //        {
        //            index = i;
        //            break;
        //        }
        //    }

        //    return index;
        //}
        #endregion

        #region getHandoverItems 获取三级HandoverItems数据
        /// <summary>
        /// 获取采样点的详细指标数据
        /// </summary>
        /// <param name="dtfdmi"></param>
        /// <param name="fileMngr"></param>
        private static void getHandoverItems(NRHandoverFileDataManager dtfdmi)
        {
            if (dtfdmi.EventsList.Count <= 0)
            {
                return;
            }
            foreach (List<Event> eList in dtfdmi.EventsList)
            {
                if (eList.Count > 1)
                {
                    NRHandoverProblemItem item = new NRHandoverProblemItem();
                    item.Index = dtfdmi.HandoverItems.Count + 1;
                    dtfdmi.HandoverItems.Add(item);
                    for (int i = 0; i < eList.Count; i++)
                    {
                        Event e = eList[i];
                        // 同一个e可能存在不同切换组中，导致hoItem被重新修改Index，故加上Clone修正
                        NRHandoverItem hoItem = dtfdmi.HandoverEventDic[e].Clone();
                        hoItem.Index = i + 1;
                        item.HandoverItems.Add(hoItem);
                    }
                    fillHandoverItem(item);

                    //if (fileMngr.ServiceType == (int)ServiceType.LTE_TDD_DATA
                    //    || fileMngr.ServiceType == (int)ServiceType.LTE_TDD_MULTI
                    //    || fileMngr.ServiceType == (int)ServiceType.LTE_TDD_VOICE
                    //    || fileMngr.ServiceType == (int)ServiceType.LTE_FDD_DATA
                    //    || fileMngr.ServiceType == (int)ServiceType.LTE_FDD_MULTI
                    //    || fileMngr.ServiceType == (int)ServiceType.LTE_FDD_VOICE)
                    //{
                    //    setTestPointData(fileMngr, eList, item);
                    //}
                }
            }
        }

        //private static void setTestPointData(DTFileDataManager fileMngr, List<Event> eList, NRHandoverProblemItem item)
        //{
        //    if (eList.Count > 1)
        //    {
        //        int bSn = eList[0].SN;
        //        int eSn = eList[eList.Count - 1].SN;
        //        int rsrpNum = 0;
        //        double rsrpSum = 0;
        //        int sinrNum = 0;
        //        double sinrSum = 0;
        //        int speedNum = 0;
        //        double speedSum = 0;
        //        int grantNum = 0;
        //        double grantSum = 0;
        //        bool isfdd = false;
        //        int iAppSeepNum = 0;
        //        double dAppSeepSum = 0;
        //        foreach (TestPoint tp in fileMngr.TestPoints)
        //        {
        //            if (tp is LTEFddTestPoint)
        //            {
        //                isfdd = true;
        //            }
        //            else if (tp is LTETestPointDetail)
        //            {
        //                isfdd = false;
        //            }
        //            if (tp.SN < bSn)
        //            {
        //                continue;
        //            }
        //            if (tp.SN > eSn)
        //            {
        //                break;
        //            }
        //            setRsrp(ref rsrpNum, ref rsrpSum, tp);
        //            setSinr(ref sinrNum, ref sinrSum, tp);
        //            setSpeed(ref speedNum, ref speedSum, tp);
        //            setGrant(ref grantNum, ref grantSum, tp);
        //            setAppSeep(ref iAppSeepNum, ref dAppSeepSum, tp);
        //        }
        //        setRsrpNum(item, rsrpNum, rsrpSum, isfdd);
        //        setSinrNum(item, sinrNum, sinrSum, isfdd);
        //        setSpeedNum(item, speedNum, speedSum, isfdd);
        //        setGrantNum(item, grantNum, grantSum, isfdd);
        //        setAppSeepNum(item, isfdd, iAppSeepNum, dAppSeepSum);
        //    }
        //}

        //private static void setRsrp(ref int rsrpNum, ref double rsrpSum, TestPoint tp)
        //{
        //    float? rsrp = (float?)tp["lte_RSRP"];
        //    if (tp is LTEFddTestPoint)
        //    {
        //        rsrp = (float?)tp["lte_fdd_RSRP"];
        //    }

        //    if (-141 <= rsrp && rsrp <= 25)
        //    {
        //        rsrpNum++;
        //        rsrpSum += (float)rsrp;
        //    }
        //}

        //private static void setSinr(ref int sinrNum, ref double sinrSum, TestPoint tp)
        //{
        //    float? sinr = (float?)tp["lte_SINR"];
        //    if (tp is LTEFddTestPoint)
        //    {
        //        sinr = (float?)tp["lte_fdd_SINR"];
        //    }

        //    if (-50 <= sinr && sinr <= 50)
        //    {
        //        sinrNum++;
        //        sinrSum += (float)sinr;
        //    }
        //}

        //private static void setSpeed(ref int speedNum, ref double speedSum, TestPoint tp)
        //{
        //    double? speed = (double?)tp["lte_PDCP_DL_Mb"];
        //    if (tp is LTEFddTestPoint)
        //    {
        //        speed = (double?)tp["lte_fdd_PDCP_DL_Mb"];
        //    }

        //    if (0 < speed)
        //    {
        //        speedNum++;
        //        speedSum += (double)speed;
        //    }
        //}

        //private static void setGrant(ref int grantNum, ref double grantSum, TestPoint tp)
        //{
        //    short? grant = (short?)tp["lte_PDCCH_DL_Grant_Count"];
        //    if (tp is LTEFddTestPoint)
        //    {
        //        grant = (short?)tp["lte_fdd_PDCCH_DL_Grant_Count"];
        //    }
        //    if (0 <= grant)
        //    {
        //        grantNum++;
        //        grantSum += (short)grant;
        //    }
        //}

        //private static void setAppSeep(ref int iAppSeepNum, ref double dAppSeepSum, TestPoint tp)
        //{
        //    double? dAppSeep = (double?)(int?)tp["lte_APP_ThroughputDL"];
        //    if (tp.FileName.Contains("上传"))
        //    {
        //        dAppSeep = (double?)(int?)tp["lte_APP_ThroughputUL"];
        //    }
        //    if (tp is LTEFddTestPoint)
        //    {
        //        dAppSeep = (double?)(int?)tp["lte_fdd_APP_ThroughputDL"];
        //    }
        //    if (dAppSeep >= 0)
        //    {
        //        iAppSeepNum++;
        //        dAppSeepSum += (double)dAppSeep;
        //    }
        //}

        //private static void setRsrpNum(NRHandoverProblemItem item, int rsrpNum, double rsrpSum, bool isfdd)
        //{
        //    if (rsrpNum != 0)
        //    {
        //        if (isfdd)
        //        {
        //            item.Param["lte_fdd_RSRP_Avg"] = Math.Round(rsrpSum / rsrpNum, 2);
        //        }
        //        else
        //        {
        //            item.Param["lte_RSRP_Avg"] = Math.Round(rsrpSum / rsrpNum, 2);
        //        }
        //    }
        //}

        //private static void setSinrNum(NRHandoverProblemItem item, int sinrNum, double sinrSum, bool isfdd)
        //{
        //    if (sinrNum != 0)
        //    {
        //        if (isfdd)
        //        {
        //            item.Param["lte_fdd_SINR_Avg"] = Math.Round(sinrSum / sinrNum, 2);
        //        }
        //        else
        //        {
        //            item.Param["lte_SINR_Avg"] = Math.Round(sinrSum / sinrNum, 2);
        //        }
        //    }
        //}

        //private static void setSpeedNum(NRHandoverProblemItem item, int speedNum, double speedSum, bool isfdd)
        //{
        //    if (speedNum != 0)
        //    {
        //        if (isfdd)
        //        {
        //            item.Param["lte_fdd_PDCP_DL_Mb_Avg"] = Math.Round(speedSum / speedNum, 2);
        //        }
        //        else
        //        {
        //            item.Param["lte_PDCP_DL_Mb_Avg"] = Math.Round(speedSum / speedNum, 2);
        //        }
        //    }
        //}

        //private static void setGrantNum(NRHandoverProblemItem item, int grantNum, double grantSum, bool isfdd)
        //{
        //    if (grantNum != 0)
        //    {
        //        if (isfdd)
        //        {
        //            item.Param["lte_fdd_PDCCH_DL_Grant_Count_Avg"] = Math.Round(grantSum / grantNum, 2);
        //        }
        //        else
        //        {
        //            item.Param["lte_PDCCH_DL_Grant_Count_Avg"] = Math.Round(grantSum / grantNum, 2);
        //        }
        //    }
        //}

        //private static void setAppSeepNum(NRHandoverProblemItem item, bool isfdd, int iAppSeepNum, double dAppSeepSum)
        //{
        //    if (iAppSeepNum != 0)
        //    {
        //        if (isfdd)
        //        {
        //            item.Param["lte_fdd_APP_ThroughputDL_Num"] = iAppSeepNum;
        //            item.Param["lte_fdd_APP_ThroughputDL_Avg"] = Math.Round(dAppSeepSum / iAppSeepNum / 1024.0 / 1024.0, 2);
        //        }
        //        else
        //        {
        //            item.Param["lte_APP_ThroughputDL_Num"] = iAppSeepNum;
        //            item.Param["lte_APP_ThroughputDL_Avg"] = Math.Round(dAppSeepSum / iAppSeepNum / 1024.0 / 1024.0, 2);
        //        }
        //    }
        //}

        private static void fillHandoverItem(NRHandoverProblemItem item)
        {
            List<Event> eList = new List<Event>();
            foreach (NRHandoverItem cellItem in item.HandoverItems)
            {
                if (!eList.Contains(cellItem.CurEvent))
                {
                    eList.Add(cellItem.CurEvent);
                }
            }
            item.Name = MakeCellUpdateStr(eList);
            item.Events = eList;
        }
        #endregion

        #region 添加小区描述
        /// <summary>
        /// 获取切换重新事件小区变更序列描述
        /// </summary>
        /// <param name="list">切换重新事件列表</param>
        /// <returns></returns>
        public static string MakeCellUpdateStr(List<List<Event>> list)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < list.Count; i++)
            {
                sb.Append(MakeCellUpdateStr(list[i]));
                sb.Append("；\r\n");
            }
            return sb.ToString();
        }

        /// <summary>
        /// 获取切换重新事件小区变更序列描述
        /// </summary>
        /// <param name="eList"></param>
        /// <returns></returns>
        public static string MakeCellUpdateStr(List<Event> eList)
        {
            string lastEarfcnPci = "";
            StringBuilder sb = new StringBuilder();
            for (int j = 0; j < eList.Count; j++)
            {
                Event e = eList[j];

                NRHandOverType cellType = NREventHelper.HandoverHelper.GetHandoverType(e.ID, false);

                HandOverEventBase.CellInfo srcCellInfo = new HandOverEventBase.CellInfo();
                HandOverEventBase.CellInfo tarCellInfo = new HandOverEventBase.CellInfo();
                if (cellType == NRHandOverType.LTE)
                {
                    srcCellInfo = NREventHelper.HandoverHelper.NSALTE.GetSrcCellInfo(e);
                    tarCellInfo = NREventHelper.HandoverHelper.NSALTE.GetTarCellInfo(e);
                }
                else if (cellType == NRHandOverType.NSA || cellType == NRHandOverType.SA)
                {
                    srcCellInfo = NREventHelper.HandoverHelper.NSANR.GetSrcCellInfo(e);
                    tarCellInfo = NREventHelper.HandoverHelper.NSANR.GetTarCellInfo(e);
                }

                string cellName;
                if (lastEarfcnPci != (srcCellInfo.ARFCN + "_" + srcCellInfo.PCI))
                {
                    //addUpdateSign2StringBuilder(ref sb, e);
                    sb.Append("->");
                    if (srcCellInfo.Cell != null)
                    {
                        cellName = srcCellInfo.Cell.Name + "(" + srcCellInfo.ARFCN + "," + srcCellInfo.PCI + ")";
                    }
                    else
                    {
                        cellName = "(EARFCN:" + srcCellInfo.ARFCN + ",PCI:" + srcCellInfo.PCI + ")";
                    }
                    sb.Append(cellName);
                }

                //addUpdateSign2StringBuilder(ref sb, e);
                sb.Append("->");
                if (tarCellInfo.Cell != null)
                {
                    cellName = tarCellInfo.Cell.Name + "(" + tarCellInfo.ARFCN + "," + tarCellInfo.PCI + ")";
                }
                else
                {
                    cellName = "(EARFCN:" + tarCellInfo.ARFCN + ",PCI:" + tarCellInfo.PCI + ")";
                }
                sb.Append(cellName);
                lastEarfcnPci = tarCellInfo.ARFCN + "_" + tarCellInfo.PCI;
            }
            return sb.ToString();
        }

        //private static void addUpdateSign2StringBuilder(ref StringBuilder sb, Event e)
        //{
        //    if (e.ID == 40 || e.ID == 137 || e.ID == 139 || e.ID == 179 || e.ID == 537
        //            || e.ID == 539 || e.ID == 579)//小区重选
        //    {
        //        sb.Append("=>");
        //    }
        //    else
        //    {
        //        sb.Append("->");
        //    }
        //}
        #endregion
    }

    /// <summary>
    /// 一级切换文件列表
    /// </summary>
    public class NRHandoverFileDataManager
    {
        public DTFileDataManager fmnger { get; set; }
        public NRHandoverFileDataManager(DTFileDataManager fmnger)
        {
            this.fmnger = fmnger;
        }

        /// <summary>
        /// 文件序号 用于导出
        /// </summary>
        public int Index { get; set; }

        /// <summary>
        /// 文件名 用于导出和TreeView显示
        /// </summary>
        public string Name
        {
            get { return fmnger.FileName; }
        }

        public List<Event> Events { get; } = new List<Event>();

        public List<List<Event>> EventsList { get; set; } = new List<List<Event>>();

        /// <summary>
        /// 过频繁组数
        /// </summary>
        public int HandoverTimes { get; set; }

        //事件对应的切换数据
        public Dictionary<Event, NRHandoverItem> HandoverEventDic { get; set; } = new Dictionary<Event, NRHandoverItem>();

        //切换数据集合
        public List<NRHandoverProblemItem> HandoverItems { get; set; } = new List<NRHandoverProblemItem>();

        //判断是否过频繁
        public static bool isTooFrequent(List<Event> events, int secondLimit, int distanceLimit, int timesLimit,
            out List<Event> resultEvents, out List<List<Event>> eventsList)
        {
            resultEvents = new List<Event>();
            eventsList = new List<List<Event>>();
            if (events.Count == 0)
            {
                return false;
            }

            WindowSplitter splitter = new WindowSplitter(secondLimit, distanceLimit, timesLimit, events);
            foreach (WindowSplitter.EventWindow win in splitter.WindowSet)
            {
                resultEvents.AddRange(win.GetEventSet());
                eventsList.Add(win.GetEventSet());
            }
            return resultEvents.Count > 0;
        }

        public void Merge(NRHandoverFileDataManager data)
        {
            Events.AddRange(data.Events);
            EventsList.AddRange(data.EventsList);
            HandoverItems.AddRange(data.HandoverItems);
        }
    }

    /// <summary>
    /// 二级切换集合(切换或者重选的问题类)
    /// </summary>
    public class NRHandoverProblemItem
    {
        /// <summary>
        /// 切换序号 用于导出
        /// </summary>
        public int Index { get; set; }

        /// <summary>
        /// 切换名称 用于导出
        /// </summary>
        public string Name { get; set; } = "";

        /// <summary>
        /// 对应的三级切换详细数据
        /// </summary>
        public List<NRHandoverItem> HandoverItems { get; set; } = new List<NRHandoverItem>();

        /// <summary>
        /// 事件合集
        /// </summary>       
        public List<Event> Events { get; set; } = new List<Event>();

        /// <summary>
        /// 参数合集
        /// </summary>
        public Dictionary<string, object> Param { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// 三级切换详细数据(单个切换或者重选事件类)
    /// </summary>
    public class NRHandoverItem
    {
        public NRHandoverItem(Event e)
        {
            CurEvent = e;
        }

        /// <summary>
        /// 小区序号 用于导出
        /// </summary>
        public int Index { get; set; }

        public Event CurEvent { get; set; }

        /// <summary>
        /// 切换前小区
        /// </summary>
        public string CellNameSrc { get; private set; }

        public string TypeSrc { get; private set; }

        /// <summary>
        /// 切换后小区
        /// </summary>
        public string CellNameTarget { get; private set; }

        /// <summary>
        /// 详细数据中的时间格式
        /// </summary>
        public string TimeString
        {
            get { return CurEvent.DateTime.ToString("HH:mm:ss.fff"); }
        }

        /// <summary>
        /// 格式化切换小区
        /// </summary>
        public string HandOverDec
        {
            get
            {
                StringBuilder sbHandover = new StringBuilder();
                sbHandover.Append($"[{TypeSrc}]:【{CellNameSrc}】 -> 【{CellNameTarget}】");
                return sbHandover.ToString();
            }
        }

        public NRHandoverCellItem CellItemBefore { get; private set; }
        public NRHandoverCellItem CellItemAfter { get; private set; }

        public void SetCellItemBefore(NRHandoverCellItem item)
        {
            CellItemBefore = item;
            CellNameSrc = CellItemBefore.CellName;

            TypeSrc = NREventHelper.HandoverHelper.GetHandoverTypeDesc(CellItemBefore.HandoverType);
        }

        public void SetCellItemAfter(NRHandoverCellItem item)
        {
            CellItemAfter = item;
            CellNameTarget = CellItemAfter.CellName;
            Tag = CellItemAfter;
        }

        #region Excel导出数据
        /// <summary>
        /// 小区名称
        /// </summary>
        public string Name
        {
            get
            {
                return CellItemBefore.CellName + "->" + CellItemAfter.CellName;
            }
        }

        public string DateTimeString
        {
            get { return CurEvent.DateTime.ToString("yyyy-MM-dd HH:mm:ss"); }
        }

        public double Longitude
        {
            get { return CurEvent.Longitude; }
        }

        public double Latitude
        {
            get { return CurEvent.Latitude; }
        }

        /// <summary>
        /// 切换前RSRP
        /// </summary>
        public string RsrpBefore
        {
            get { return getValidData(CellItemBefore.RSRP, CellItemBefore.RSRPCount); }
        }

        /// <summary>
        /// 切换后RSRP
        /// </summary>
        public string RsrpAfter
        {
            get { return getValidData(CellItemAfter.RSRP, CellItemAfter.RSRPCount); }
        }

        /// <summary>
        /// 切换前SINR
        /// </summary>
        public string SinrBefore
        {
            get { return getValidData(CellItemBefore.SINR, CellItemBefore.SINRCount); }
        }

        /// <summary>
        /// 切换后SINR
        /// </summary>
        public string SinrAfter
        {
            get { return getValidData(CellItemAfter.SINR, CellItemAfter.SINRCount); }
        }

        public string getValidData(double value1, double value2)
        {
            if (value2 == 0)
            {
                return " _  ";
            }
            else
            {
                return (value1 / value2).ToString("0.00");
            }
        }
        #endregion

        public object Tag { get; private set; }

        public NRHandoverItem Clone()
        {
            return this.MemberwiseClone() as NRHandoverItem;
        }
    }

    /// 切换或者重选涉及小区信息类
    /// </summary>
    public class NRHandoverCellItem
    {
        public NRHandoverCellItem(Event e, NRHandoverCellType type, NRHandOverType cellType)
        {
            this.Ev = e;
            this.CellType = type;
            HandoverType = cellType;

            if (e != null)
            {
                if (cellType == NRHandOverType.LTE)
                {
                    getCellInfo(e, type, NREventHelper.HandoverHelper.LTE);
                }
                else if (cellType == NRHandOverType.NSA)
                {
                    getCellInfo(e, type, NREventHelper.HandoverHelper.NSANR);
                }
                else if (cellType == NRHandOverType.SA)
                {
                    getCellInfo(e, type, NREventHelper.HandoverHelper.SA);
                }
            }
        }

        private void getCellInfo(Event e, NRHandoverCellType type, HandOverEventBase helper)
        {
            if (type == NRHandoverCellType.BeforeHandover)
            {
                HandOverEventBase.CellInfo srcCellInfo = helper.GetSrcCellInfo(e);
                setCellInfo(srcCellInfo);
            }
            else
            {
                HandOverEventBase.CellInfo tarCellInfo = helper.GetTarCellInfo(e);
                setCellInfo(tarCellInfo);
            }
        }

        private void setCellInfo(HandOverEventBase.CellInfo info)
        {
            EARFCN = info.ARFCN;
            PCI = info.PCI;
            if (info.Cell != null)
            {
                CellName = info.Cell.Name;
            }
            else
            {
                CellName = EARFCN + "_" + PCI;
            }
        }

        public Event Ev { get; set; }
        public void AddStat(NRHandoverCellItem tps)
        {
            this.RSRP += tps.RSRP;
            this.RSRPCount += tps.RSRPCount;
            this.SINR += tps.SINR;
            this.SINRCount += tps.SINRCount;
        }

        public int EARFCN { get; set; } = 0;
        public int PCI { get; set; } = 0;
        public float RSRP { get; set; } = 0;
        public float SINR { get; set; } = 0;
        public int RSRPCount { get; set; } = 0;
        public int SINRCount { get; set; } = 0;
        public string CellName { get; set; } = "";

        /// <summary>
        /// 切换前后类型，切换前或切换后
        /// </summary>
        public NRHandoverCellType CellType { get; set; }

        public NRHandOverType HandoverType { get; set; }
    }

    /// <summary>
    /// 切换小区类型：切换前，切换后
    /// </summary>
    public enum NRHandoverCellType
    {
        BeforeHandover,
        AfterHandover
    }
}
