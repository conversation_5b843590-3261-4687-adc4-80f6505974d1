﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using System.Windows.Forms;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTMultiCoverQuery_LTE : ZTDiyRoadMultiCoverageQueryByRegion_TD
    {
        protected bool bFreqType = false;
        protected FreqBandInfo frFreqType = null;

        public ZTMultiCoverQuery_LTE(MainModel mainModel)
            : base(mainModel)
        {
            themeName = "TD_LTE_RSRP";
        }
        public override string Name
        {
            get { return "重叠覆盖分析"; }
        }

        public ZTMultiCoverQuery_LTE(ServiceName serviceName)
            : this(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22016, this.Name);
        }
        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            rsrpParamInfo = DTDisplayParameterManager.GetInstance()["LTE", "RSRP"];
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "lte_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_TAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_ECI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            for (int i = 0; i < 6; i++)
            {
                param = new Dictionary<string, object>();
                param["param_name"] = "lte_NCell_EARFCN";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "lte_NCell_PCI";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "lte_NCell_RSRP";
                param["param_arg"] = i;
                columnsDef.Add((object)param);
            }

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }
        protected override bool getConditionBeforeQuery()
        {
            if (xtraSetRoadfrom == null)
            {
                xtraSetRoadfrom = new XtraSetRoadMultiForm("TDLTE");
            }
            if (xtraSetRoadfrom.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            xtraSetRoadfrom.GetSettingFilterRet(out setRxlev, out setRxlevDiff, out coFreq, out interferenceType,
                out saveTestPoint, out invalidPointRxLev);

            bFreqType = xtraSetRoadfrom.BFreq;
            frFreqType = xtraSetRoadfrom.FRFreq;
            InitRegionMop2();
            return !bFreqType || frFreqType != null;
        }
        protected override void doWithDTData(TestPoint testPoint)
        {
            string strRegionName = isContainPoint(new DbPoint(testPoint.Longitude, testPoint.Latitude));
            if (strRegionName == null) return;

            int relativeLevel = 0;
            List<string> relCovCellsName = new List<string>();
            int absoluteLevel = 0;
            List<string> absCovCellsName = new List<string>();

            //根据需求新加的数据集  by 小雷
            int relANDabsLevel = 0;
            List<string> relANDabsCovCellsName = new List<string>();
            float maxRxlev = 0;
            string mainCellName = "";
            LTECell mainCell = null;
            Dictionary<string, LteCellCoverData> cellRscpDic = getCellList(testPoint, ref maxRxlev, ref mainCellName, ref mainCell);
            if (cellRscpDic.Count == 0)
            {
                return;
            }
            foreach (var data in cellRscpDic)
            {
                LTECell curCell = data.Value.LteCell;
                float rscp = data.Value.Rsrp;
                bool isCoFreq = IsCoFreq(mainCell, mainCellName, curCell, data.Value.Earfcn);
                if (rscp > setRxlev)//绝对覆盖度
                {
                    addCovData(ref absoluteLevel, absCovCellsName, data, isCoFreq);
                }
                if (rscp - maxRxlev > -setRxlevDiff)//相对覆盖度
                {
                    addCovData(ref relativeLevel, relCovCellsName, data, isCoFreq);
                }
                if ((rscp > setRxlev) && (rscp - maxRxlev > -setRxlevDiff))
                {
                    addCovData(ref relANDabsLevel, relANDabsCovCellsName, data, isCoFreq);
                }
            }

            bool invalidate = maxRxlev < invalidPointRxLev;
            RoadMultiCoverageInfo_TD info = new RoadMultiCoverageInfo_TD(testPoint, maxRxlev, mainCellName, relativeLevel, relCovCellsName, absoluteLevel, absCovCellsName,
            relANDabsLevel, relANDabsCovCellsName, saveTestPoint, invalidate);
            MainModel.RegionRoadMultiCoveragePoints_TD[strRegionName].Add(info);
            MainModel.RegionRoadMultiCoveragePoints_TD["全部汇总"].Add(info);
        }

        private void addCovData(ref int level, List<string> covCellsName, KeyValuePair<string, LteCellCoverData> data, bool isCoFreq)
        {
            if (!coFreq || isCoFreq)
            {
                level++;
                covCellsName.Add(data.Key);
            }
        }

        private DTDisplayParameterInfo rsrpParamInfo = null;

        private bool judgeInFreqRange(int? earfcn)
        {
            if (bFreqType)
            {
                if (earfcn == null)
                {
                    return false;
                }
                foreach (var range in frFreqType.RangeList)
                {
                    bool inRange = range.JudgeInRange((int)earfcn);
                    if (inRange)
                    {
                        return true;
                    }
                }
                return false;
            }
            else
            {
                return true;
            }
        }

        /// <summary>
        /// 获取信号列表
        /// </summary>
        /// <param name="testPoint"></param>
        /// <returns></returns>
        private Dictionary<string, LteCellCoverData> getCellList(TestPoint testPoint, ref float maxRxlev, ref string mainCellName, ref LTECell mainCell)
        {
            Dictionary<string, LteCellCoverData> cellRscpList = new Dictionary<string, LteCellCoverData>();
            maxRxlev = float.MinValue;
            mainCellName = dealSCell(testPoint, ref maxRxlev, ref mainCell, cellRscpList);
            dealNCell(testPoint, ref maxRxlev, ref mainCellName, ref mainCell, cellRscpList);
            return cellRscpList;
        }

        private string dealSCell(TestPoint testPoint, ref float maxRxlev, ref LTECell mainCell, Dictionary<string, LteCellCoverData> cellRscpList)
        {
            string mainCellName;
            float? rxlev = (float?)testPoint["lte_RSRP"];
            mainCellName = string.Empty;
            if (rxlev != null && rxlev >= rsrpParamInfo.ValueMin && rxlev <= rsrpParamInfo.ValueMax)
            {
                int? earfcn = (int?)testPoint["lte_EARFCN"];
                if (judgeInFreqRange(earfcn))
                {
                    short? pci = (short?)testPoint["lte_PCI"];
                    LteCellCoverData data = getCellData(testPoint, ref maxRxlev, ref mainCellName, ref mainCell, rxlev, earfcn, pci);
                    if (data != null)
                    {
                        cellRscpList[data.CellName] = data;
                    }
                }
            }

            return mainCellName;
        }

        private void dealNCell(TestPoint testPoint, ref float maxRxlev, ref string mainCellName, ref LTECell mainCell, Dictionary<string, LteCellCoverData> cellRscpList)
        {
            for (int i = 0; i < 6; i++)
            {
                float? rxlev = (float?)testPoint["lte_NCell_RSRP", i];
                if (rxlev != null && rxlev <= rsrpParamInfo.ValueMax && rxlev >= rsrpParamInfo.ValueMin)
                {
                    int? nEarfcn = (int?)testPoint["lte_NCell_EARFCN", i];
                    if (!judgeInFreqRange(nEarfcn))
                    {
                        continue;
                    }
                    short? nPci = (short?)testPoint["lte_NCell_PCI", i];

                    LteCellCoverData data = getCellData(testPoint, ref maxRxlev, ref mainCellName, ref mainCell, rxlev, nEarfcn, nPci);
                    if (data != null)
                    {
                        cellRscpList[data.CellName] = data;
                    }
                }
            }
        }

        private LteCellCoverData getCellData(TestPoint testPoint, ref float maxRxlev, ref string mainCellName, ref LTECell mainCell, float? rxlev, int? earfcn, short? pci)
        {
            if (earfcn != null && pci != null)
            {
                LteCellCoverData data = new LteCellCoverData();
                data.CellName = earfcn.ToString() + "_" + pci.ToString();
                data.Rsrp = (float)rxlev;
                LTECell lteCell = CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(testPoint.DateTime, earfcn, pci, testPoint.Longitude, testPoint.Latitude);
                data.LteCell = lteCell;
                data.Earfcn = (int)earfcn;

                if (rxlev > maxRxlev)
                {
                    maxRxlev = (float)rxlev;
                    mainCellName = data.CellName;
                    mainCell = lteCell;
                }
                return data;
            }
            return null;
        }

        protected bool IsCoFreq(LTECell mainCell, string mainCellName, LTECell curCell, int earfcn)
        {
            if (mainCell != null)
            {
                if (curCell != null)
                {
                    if (mainCell.IsCo_Freq(earfcn))
                    {
                        return true;
                    }
                }
                else
                {
                    return InCellEarfcn(mainCell, earfcn);
                }
            }
            else
            {
                int mainEarfcn = int.Parse(mainCellName.Substring(0, mainCellName.IndexOf("_")));
                if (curCell != null)
                {
                    return InCellEarfcn(curCell, earfcn);
                }
                else
                {
                    if (earfcn == mainEarfcn)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        private bool InCellEarfcn(LTECell cell, int earfcn)
        {
            if (interferenceType == MapForm.DisplayInterferenceType.BCCH_CPI)
            {
                if (cell.IsCo_Freq(earfcn))
                {
                    return true;
                }
            }
            else if (interferenceType == MapForm.DisplayInterferenceType.TCH_FREQ)
            {
                if (cell.IsCo_FreqList(earfcn))
                {
                    return true;
                }
            }
            else
            {
                if (cell.IsCo_Freq(earfcn) || cell.IsCo_FreqList(earfcn))
                {
                    return true;
                }
            }
            return false;
        }
    }

    //重叠覆盖***************************************************************
    public class ZTMultiCoverQuery_LteFdd : ZTMultiCoverQuery_LTE
    {
        public ZTMultiCoverQuery_LteFdd(MainModel mainModel)
            : base(mainModel)
        {
            themeName = "LTE_FDD:RSRP";
        }

        public override string Name
        {
            get { return "重叠覆盖分析_LTE_FDD"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26007, this.Name);
        }
        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            rsrpParamInfo = DTDisplayParameterManager.GetInstance()["LTE", "RSRP"];
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_TAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_ECI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            for (int i = 0; i < 6; i++)
            {
                param = new Dictionary<string, object>();
                param["param_name"] = "lte_fdd_NCell_EARFCN";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "lte_fdd_NCell_PCI";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "lte_fdd_NCell_RSRP";
                param["param_arg"] = i;
                columnsDef.Add((object)param);
            }

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            //tmpDic.Add("name", (object)"TD_LTE_RSRP");
            //tmpDic.Add("themeName", (object)"TD_LTE_RSRP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        private DTDisplayParameterInfo rsrpParamInfo = null;

        /// <summary>
        /// 获取信号列表
        /// </summary>
        /// <param name="testPoint"></param>
        /// <returns></returns>
        private Dictionary<string, float> getCellList(TestPoint testPoint, ref float maxRxlev, ref string mainCellName)
        {
            Dictionary<string, float> cellRscpDic = new Dictionary<string, float>();
            maxRxlev = float.MinValue;

            //////////////////////////////////////////////////////////////////////////主服小区 begin
            float? rxlev = (float?)testPoint["lte_fdd_RSRP"];
            mainCellName = string.Empty;
            if (rxlev != null && rxlev >= rsrpParamInfo.ValueMin && rxlev <= rsrpParamInfo.ValueMax)
            {
                int? bcch = (int?)testPoint["lte_fdd_EARFCN"];
                short? bsic = (short?)testPoint["lte_fdd_PCI"];
                if (bcch != null && bsic != null)
                {
                    mainCellName = bcch.ToString() + "_" + bsic.ToString();
                    maxRxlev = (float)rxlev;
                    cellRscpDic[mainCellName] = (float)rxlev;
                }
            }
            //////////////////////////////////////////////////////////////////////////主服小区 end

            //////////////////////////////////////////////////////////////////////////邻区 begin
            for (int i = 0; i < 6; i++)
            {
                rxlev = (float?)testPoint["lte_fdd_NCell_RSRP", i];
                int? nBcch = (int?)testPoint["lte_fdd_NCell_EARFCN", i];
                short? nCpi = (short?)testPoint["lte_fdd_NCell_PCI", i];
                if (rxlev == null || nBcch == null || nCpi == null
                    || rxlev > rsrpParamInfo.ValueMax || rxlev < rsrpParamInfo.ValueMin)
                {
                    continue;
                }

                string cellName = nBcch.ToString() + "_" + nCpi.ToString();
                cellRscpDic[cellName] = (float)rxlev;
                if (rxlev > maxRxlev)
                {
                    mainCellName = cellName;
                    maxRxlev = (float)rxlev;
                }
            }
            //////////////////////////////////////////////////////////////////////////邻区 end

            return cellRscpDic;
        }

        protected override void doWithDTData(TestPoint testPoint)
        {
            string strRegionName = isContainPoint(new DbPoint(testPoint.Longitude, testPoint.Latitude));
            if (strRegionName == null) return;

            int relativeLevel = 0;
            List<string> relCovCellsName = new List<string>();
            int absoluteLevel = 0;
            List<string> absCovCellsName = new List<string>();

            //根据需求新加的数据集  by 小雷
            int relANDabsLevel = 0;
            List<string> relANDabsCovCellsName = new List<string>();
            float maxRxlev = 0;
            string mainCellName = "";
            Dictionary<string, float> cellRscpDic = getCellList(testPoint, ref maxRxlev, ref mainCellName);
            if (cellRscpDic.Count == 0)
            {
                return;
            }
            foreach (string cellName in cellRscpDic.Keys)
            {
                float rscp = cellRscpDic[cellName];
                if (rscp > setRxlev)//绝对覆盖度
                {
                    absoluteLevel++;
                    absCovCellsName.Add(cellName);
                }
                if (rscp - maxRxlev > -setRxlevDiff)//相对覆盖度
                {
                    relativeLevel++;
                    relCovCellsName.Add(cellName);
                }

                if ((rscp > setRxlev) && (rscp - maxRxlev > -setRxlevDiff))
                {
                    relANDabsLevel++;
                    relANDabsCovCellsName.Add(cellName);
                }
            }
            bool invalidate = maxRxlev < invalidPointRxLev;
            RoadMultiCoverageInfo_TD info = new RoadMultiCoverageInfo_TD(testPoint, maxRxlev, mainCellName, relativeLevel, relCovCellsName, absoluteLevel, absCovCellsName,
            relANDabsLevel, relANDabsCovCellsName, saveTestPoint, invalidate);
            MainModel.RegionRoadMultiCoveragePoints_TD[strRegionName].Add(info);
            MainModel.RegionRoadMultiCoveragePoints_TD["全部汇总"].Add(info);
        }
    }

    public class ZTMultiCoverQuery_LteFdd_VOLTE : ZTMultiCoverQuery_LteFdd
    {
        public ZTMultiCoverQuery_LteFdd_VOLTE(MainModel mainModel)
            : base(mainModel)
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }
        public override string Name
        {
            get { return "VOLTE_FDD重叠覆盖分析"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30009, this.Name);
        }
        protected override bool isValidTestPoint(TestPoint tp)
        {
            if (ServiceTypes.Count > 0 && !ServiceTypes.Contains((ServiceType)tp.ServiceType)) return false;
            return true;
        }
    }

    class LteCellCoverData
    {
        public LTECell LteCell { get; set; }
        public string CellName { get; set; }
        public float Rsrp { get; set; }
        public int Earfcn { get; set; }
    }
}
