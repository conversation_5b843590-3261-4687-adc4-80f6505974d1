﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 查询网络类型数据
    /// </summary>
    public class ZTRegionComplainInitNetworkSQLQuery:DIYSQLBase
    {
        public ZTRegionComplainInitNetworkSQLQuery(MainModel mainModel)
            : base(mainModel)
        { 
            
        }

        private readonly List<string> networks = new List<string>();
        public List<string> Networks
        {
            get { return networks; }
        }

        protected override string getSqlTextString()
        {
            return "select distinct netType from complaint_xj..tb_complain_item where netType is not null";
        }

        protected override MasterCom.RAMS.Model.Interface.E_VType[] getSqlRetTypeArr()
        {
            E_VType[] vtypes = new E_VType[1];
            vtypes[0] = E_VType.E_String;
            return vtypes;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            networks.Clear();
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    string network = package.Content.GetParamString();
                    this.networks.Add(network);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    break;
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    break;
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        public override string Name
        {
            get { return ""; }
        }
    }
}
