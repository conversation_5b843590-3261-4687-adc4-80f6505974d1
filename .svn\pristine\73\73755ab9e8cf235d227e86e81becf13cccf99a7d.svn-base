﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class PesudoBaseStationSettingDlg : Form
    {
        public List<CRange> CRangeList { get; set; }
        public PesudoBaseStationSettingDlg(int ci, List<CRange> cRangeList,
            bool bChkCI, bool bChkLAC)
        {
            InitializeComponent();
            spinEditCI.Value = ci;
            this.CRangeList = cRangeList;
            if (this.CRangeList == null)
            {
                this.CRangeList = new List<CRange>();
            }
            simpleBtnDel.Enabled = false;
            checkBoxCI.Checked = bChkCI;
            checkBoxLAC.Checked = bChkLAC;
            refreshListBoxLAC();
            if (listBoxLAC.Items.Count > 0)
            {
                setListBoxSelectedIndex(0);
            }
        }

        public bool ChkPesudoCI
        {
            get { return checkBoxCI.Checked; }
        }

        public int PesudoCI
        {
            get { return (int)spinEditCI.Value; }
        }

        public bool ChkPesudoLAC
        {
            get { return checkBoxLAC.Checked; }
        }

        private void simBtnOK_Click(object sender, EventArgs e)
        {
            if (CRangeList == null ||
                CRangeList.Count <= 0)
            {
                MessageBox.Show("尚未设置LAC", "提示");
                return;
            }
            this.DialogResult = DialogResult.OK;
        }

        private void simBtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void refreshListBoxLAC()
        {
            listBoxLAC.Items.Clear();
            foreach (CRange range in CRangeList)
            {
                listBoxLAC.Items.Add(range);
            }
        }

        private void setListBoxSelectedIndex(int idx)
        {
            if (idx >= 0 && listBoxLAC.Items.Count > idx)
            {
                listBoxLAC.SelectedIndex = idx;
            }
            else if (idx >= 0 && listBoxLAC.Items.Count > idx - 1)
            {
                listBoxLAC.SelectedIndex = idx - 1;
            }
        }

        private void simpleBtnAdd_Click(object sender, EventArgs e)
        {
            RangeSetting dlg = new RangeSetting();
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                CRange cRange = dlg.GetRange();
                CRangeList.Add(cRange);
                refreshListBoxLAC();
                setListBoxSelectedIndex(CRangeList.IndexOf(cRange));
            }
        }

        private void simpleBtnDel_Click(object sender, EventArgs e)
        {
            if (listBoxLAC.SelectedItems.Count <= 0) return;
            int idx = listBoxLAC.SelectedIndex;
            CRange cRange = listBoxLAC.SelectedItems[0] as CRange;
            CRangeList.Remove(cRange);
            refreshListBoxLAC();
            if (listBoxLAC.Items.Count > 0)
            {
                setListBoxSelectedIndex(idx);
            }
        }

        private void checkBoxCI_CheckedChanged(object sender, EventArgs e)
        {
            if (checkBoxCI.Checked)
            {
                spinEditCI.Enabled = true;
            }
            else
            {
                spinEditCI.Enabled = false;
            }
        }

        private void checkBoxLAC_CheckedChanged(object sender, EventArgs e)
        {
            if (checkBoxLAC.Checked)
            {
                listBoxLAC.Enabled = true;
                simpleBtnAdd.Enabled = true;
                simpleBtnDel.Enabled = true;
            }
            else
            {
                listBoxLAC.Enabled = false;
                simpleBtnAdd.Enabled = false;
                simpleBtnDel.Enabled = false;
            }
        }

        private void listBoxLAC_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listBoxLAC.SelectedItems.Count > 0)
            {
                simpleBtnDel.Enabled = true;
            }
            else
            {
                simpleBtnDel.Enabled = false;
            }
        }
    }
}
