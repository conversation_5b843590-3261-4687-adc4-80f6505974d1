﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.UserMng;
using MasterCom.MTGis;
using MasterCom.RAMS.Grid;

namespace MasterCom.RAMS.Net
{
    public class DIYQueryCoverScanGridByRegion : QueryCellGridScan
    {
        public DIYQueryCoverScanGridByRegion(MainModel mainModel)
            : base(mainModel)
        {
        } 
        public override string Name
        {
            get { return "扫频栅格查询(按区域)"; }
        }
        public override string IconName
        {
            get { return null; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 1200, 1201, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        protected override void AddGeographicFilter(Package package)
        {
            base.AddDIYRegion_Intersect(package);
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override bool isValidStatImg(GridUnitBase gridBase)
        {
            return Condition.Geometorys.GeoOp.CheckRectCenterInRegion(gridBase.Bounds);
        }
    }
}
