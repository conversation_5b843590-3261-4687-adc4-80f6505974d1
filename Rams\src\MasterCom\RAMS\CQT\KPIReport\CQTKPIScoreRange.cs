﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using System.Drawing;
using MasterCom.Util;
using System.Xml;

namespace MasterCom.RAMS.CQT
{
    /// <summary>
    /// 打分方案
    /// </summary>
    public class CQTKPIScoreScheme
    {
        public CQTKPIScoreScheme()
        { }
        public CQTKPIScoreScheme(float kpiValueRangeMin,float kpiValueRangeMax,double scoreRangeMin,double scoreRangeMax,ScoreOrderType scoreType)
        {
            minKpiValue = kpiValueRangeMin;
            maxKpiValue = kpiValueRangeMax;
            minScore = scoreRangeMin;
            maxScore = scoreRangeMax;
           this.scoreType = scoreType;
        }
        private bool isSmoothScore = true;
        public bool IsSmoothScore
        {
            get { return isSmoothScore; }
            set
            {
                if (value != isSmoothScore)
                {
                    isSmoothScore = value;
                    refreshRanges();
                }
            }
        }

        private ScoreOrderType scoreType=ScoreOrderType.Positive;
        public virtual ScoreOrderType ScoreOrderType
        {
            get { return scoreType; }
            set {
                if (value != scoreType)
                {
                    scoreType = value;
                    refreshRanges();
                }
            }
        }
        protected virtual void refreshRanges()
        {
            foreach (CQTKPIScoreColorRange range in ScoreColorRanges)
            {
                range.ScoreOrderType = scoreType;
                range.IsSmoonthScore = isSmoothScore;
                if (range.Min<minKpiValue)
                {
                    range.Min = (float)minKpiValue;
                }
                if (range.Min >maxKpiValue)
                {
                    range.Min = (float)maxKpiValue;
                }
                if (range.Max>maxKpiValue)
                {
                    range.Max = (float)maxKpiValue;
                }
                if (range.ScoreRangeMin<minScore)
                {
                    range.ScoreRangeMin = minScore;
                }
                if (range.ScoreRangeMin>maxScore)
                {
                    range.ScoreRangeMin = maxScore;
                }
                if (range.ScoreRangeMax>maxScore)
                {
                    range.ScoreRangeMax = maxScore;
                }
            }
        }
        public string Name { get; set; } = "";
        private double minScore = 0;
        public virtual double MinScore
        {
            get { return minScore; }
            set
            {
                if (minScore != value)
                {
                    minScore = value;
                    refreshRanges();
                }
            }
        }
        private double maxScore = 100;
        public virtual double MaxScore
        {
            get { return maxScore; }
            set
            {
                if (maxScore != value)
                {
                    maxScore = value;
                    refreshRanges();
                }
            }
        }
        private double minKpiValue = 0;
        public virtual double MinKPIValue
        {
            get { return minKpiValue; }
            set
            {
                if (minKpiValue != value)
                {
                    minKpiValue = value;
                    refreshRanges();
                }
            }
        }
        private double maxKpiValue = 100;
        public virtual double MaxKPIValue
        {
            get { return maxKpiValue; }
            set
            {
                if (maxKpiValue != value)
                {
                    maxKpiValue = value;
                    refreshRanges();
                }
            }
        }
        public List<CQTKPIScoreColorRange> ScoreColorRanges { get; set; } = new List<CQTKPIScoreColorRange>();

        public CQTKPIScoreColorRange GetRangeByKPIValue(double kpiValue, out double score)
        {
            score = double.NaN;
            foreach (CQTKPIScoreColorRange sr in ScoreColorRanges)
            {
                if (sr.Within((float)kpiValue, out score))
                {
                    return sr;
                }
            }
            return null;
        }

        public virtual XmlElement AddItem(XmlConfigFile configFile, XmlElement config, string name, object value)
        {
            if (value is CQTKPIScoreScheme)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                configFile.AddItem(item, "Name", Name);
                configFile.AddItem(item, "MinScore", MinScore);
                configFile.AddItem(item, "MaxScore", MaxScore);
                configFile.AddItem(item, "MinKPIValue", MinKPIValue);
                configFile.AddItem(item, "MaxKPIValue", MaxKPIValue);
                configFile.AddItem(item, "ScoreOrderType", (int)scoreType);
                configFile.AddItem(item, "IsSmoothScore", isSmoothScore);
                configFile.AddItem(item, "ScoreColorRanges", ScoreColorRanges, AddItem);
                return item;
            }
            else if (value is CQTKPIScoreColorRange)
            {
                return (value as CQTKPIScoreColorRange).AddItem(configFile, config, null, value);
            }
            return null;
        }

        public virtual object GetItemValue(XmlConfigFile configFile, XmlElement item, string itemName)
        {
            if (itemName == this.GetType().Name)
            {
                setValidConfigData(configFile, item);
                return this;
            }
            else if (itemName == typeof(CQTKPIScoreColorRange).Name)
            {
                CQTKPIScoreColorRange range = new CQTKPIScoreColorRange();
                range.GetItemValue(configFile, item, itemName);
                range.ScoreOrderType = scoreType;
                range.IsSmoonthScore = isSmoothScore;
                return range;
            }
            return null;
        }

        protected virtual void setValidConfigData(XmlConfigFile configFile, XmlElement item)
        {
            Name = configFile.GetItemValue(item, "Name") as string;
            MinScore = getValidData(configFile, item, "MinScore", MinScore);
            MaxScore = getValidData(configFile, item, "MaxScore", MaxScore);
            MinKPIValue = getValidData(configFile, item, "MinKPIValue", MinKPIValue);
            MaxKPIValue = getValidData(configFile, item, "MaxKPIValue", MaxKPIValue);
            scoreType = getValidData(configFile, item, "ScoreOrderType", scoreType);
            isSmoothScore = getValidData(configFile, item, "IsSmoothScore", isSmoothScore);

            getValidData(configFile, item, "ScoreColorRanges", ScoreColorRanges);
        }

        protected T getValidData<T>(XmlConfigFile configFile, XmlElement item, string name, T defaultValue)
        {
            T res = defaultValue;
            object value = configFile.GetItemValue(item, name);
            if (value != null)
            {
                res = (T)value;
            }
            return res;
        }

        protected void getValidData<T>(XmlConfigFile configFile, XmlElement item, string name, List<T> ranges)
        {
            List<object> list = configFile.GetItemValue(item, name, GetItemValue) as List<object>;
            foreach (object o in list)
            {
                if (o != null && o is T)
                {
                    ranges.Add((T)o);
                }
            }
        }
    }

    public class CQTKPIScoreScheme_PK : CQTKPIScoreScheme
    {
        public CQTKPIScoreScheme_PK()
        { }
        //public CQTKPIScoreScheme_PK(double scoreMin, double scoreMax, float kpiValueMin1, float kpiValueMax1,ScoreOrderType scoreType1, float kpiValueMin2, float kpiValueMax2,ScoreOrderType scoreType2)
        //    : base(kpiValueMin1, kpiValueMax1, scoreMin, scoreMax, scoreType1)
        //{
        //    MinKPIValue2 = kpiValueMin2;
        //    MaxKPIValue2 = kpiValueMax2;
        //    ScoreOrderType2 = scoreType2;
        //}
        private double minKpiValue2;
        public double MinKPIValue2
        {
            get { return minKpiValue2; }
            set
            {
                if (minKpiValue2!=value)
                {
                    minKpiValue2 = value;
                    refreshRanges();
                }
            }
        }
        private double maxKpiValue2;
        public double MaxKPIValue2
        {
            get { return maxKpiValue2; }
            set
            {
                if (maxKpiValue2 != value)
                {
                    maxKpiValue2 = value;
                    refreshRanges();
                }
            }
        }
        private ScoreOrderType scoreType;
        public ScoreOrderType ScoreOrderType2
        {
            get { return scoreType; }
            set
            {
                if (scoreType != value)
                {
                    scoreType = value;
                    refreshRanges();
                }
            }
        }
        protected override void refreshRanges()
        {
            base.refreshRanges();
            setScoreColorRanges2();
            setPKScoreColorRanges();
        }

        private void setPKScoreColorRanges()
        {
            foreach (DTParameterRangeColor pkColorRange in PKScoreColorRanges)
            {
                if (pkColorRange.Min < MinScore)
                {
                    pkColorRange.Min = (float)MinScore;
                }
                if (pkColorRange.Min > MaxScore)
                {
                    pkColorRange.Min = (float)MaxScore;
                }
                if (pkColorRange.Max > MaxScore)
                {
                    pkColorRange.Max = (float)MaxScore;
                }
            }
        }

        private void setScoreColorRanges2()
        {
            foreach (CQTKPIScoreColorRange range in ScoreColorRanges2)
            {
                range.ScoreOrderType = scoreType;
                if (range.Min < minKpiValue2)
                {
                    range.Min = (float)minKpiValue2;
                }
                if (range.Max > maxKpiValue2)
                {
                    range.Max = (float)maxKpiValue2;
                }
                if (range.ScoreRangeMin < MinScore)
                {
                    range.ScoreRangeMin = MinScore;
                }
                if (range.ScoreRangeMax > MaxScore)
                {
                    range.ScoreRangeMax = MaxScore;
                }
            }
        }

        public List<CQTKPIScoreColorRange> ScoreColorRanges2 { get; set; } = new List<CQTKPIScoreColorRange>();
        /// <summary>
        /// 竞争对比 评分着色
        /// </summary>
        public List<DTParameterRangeColor> PKScoreColorRanges { get; set; } = new List<DTParameterRangeColor>();
        public override XmlElement AddItem(XmlConfigFile configFile, XmlElement config, string name, object value)
        {
            if (value is CQTKPIScoreScheme)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                configFile.AddItem(item, "Name", Name);
                configFile.AddItem(item, "MinScore", MinScore);
                configFile.AddItem(item, "MaxScore", MaxScore);
                configFile.AddItem(item, "MinKPIValue1", MinKPIValue);
                configFile.AddItem(item, "MaxKPIValue1", MaxKPIValue);
                configFile.AddItem(item, "MinKPIValue2", MinKPIValue2);
                configFile.AddItem(item, "MaxKPIValue2", MaxKPIValue2);
                configFile.AddItem(item, "ScoreColorRanges1", ScoreColorRanges, AddItem);
                configFile.AddItem(item, "ScoreColorRanges2", ScoreColorRanges2, AddItem);
                configFile.AddItem(item, "ScoreRangeColors", PKScoreColorRanges, AddItem);
                return item;
            }
            else if (value is CQTKPIScoreColorRange)
            {
                return (value as CQTKPIScoreColorRange).AddItem(configFile, config, null, value);
            }
            else if (value is DTParameterRangeColor)
            {
                XmlElement item = configFile.AddItem(config, null, value.GetType());
                (value as DTParameterRangeColor).FillItem(configFile, item);
                return item;
            }
            return null;
        }

        public override object GetItemValue(XmlConfigFile configFile, XmlElement item, string itemName)
        {
            if (itemName == this.GetType().Name)
            {
                setValidConfigData(configFile, item);
                return this;
            }
            else if (itemName == typeof(CQTKPIScoreColorRange).Name)
            {
                CQTKPIScoreColorRange range = new CQTKPIScoreColorRange();
                range.GetItemValue(configFile, item, itemName);
                return range;
            }
            else if (itemName.Equals(typeof(DTParameterRangeColor).Name))
            {
                DTParameterRangeColor range = new DTParameterRangeColor();
                range.FillItemValue(configFile, item);
                return range;
            }
            return null;
        }

        protected override void setValidConfigData(XmlConfigFile configFile, XmlElement item)
        {
            Name = configFile.GetItemValue(item, "Name") as string;
            MinScore = getValidData(configFile, item, "MinScore", MinScore);
            MaxScore = getValidData(configFile, item, "MaxScore", MaxScore);
            MinKPIValue = getValidData(configFile, item, "MinKPIValue1", MinKPIValue);
            MaxKPIValue = getValidData(configFile, item, "MaxKPIValue1", MaxKPIValue);
            MinKPIValue2 = getValidData(configFile, item, "MinKPIValue2", MinKPIValue);
            MaxKPIValue2 = getValidData(configFile, item, "MaxKPIValue2", MaxKPIValue);

            getValidData(configFile, item, "ScoreColorRanges1", ScoreColorRanges);
            getValidData(configFile, item, "ScoreColorRanges2", ScoreColorRanges);
            getValidData(configFile, item, "ScoreRangeColors", PKScoreColorRanges);
        }
    }

    /// <summary>
    /// 评分颜色区间
    /// </summary>
    public class CQTKPIScoreColorRange : DTParameterRangeColor
    {
        /// <summary>
        /// true:平滑打分（按比例打分）;false 阶梯打分
        /// </summary>
        public bool IsSmoonthScore { get; set; }
        public CQTKPIScoreColorRange() { DesInfo = ""; }
        public CQTKPIScoreColorRange(float valueRangeMin, float valueRangeMax, double scoreRangeMin, double scoreRangeMax, Color color, string desc,ScoreOrderType scoreType)
            : base(valueRangeMin, valueRangeMax, color)
        {
            ScoreRangeMin = scoreRangeMin;
            ScoreRangeMax = scoreRangeMax;
            this.Value = color;
            this.DesInfo = desc;
            MinIncluded = true;
            MaxIncluded = true;
            ScoreOrderType = scoreType;
        }
        public Color Color
        {
            get { return Value; }
            set { this.Value = value; }
        }
        public double ScoreRangeMin { get; set; }
        public double ScoreRangeMax { get; set; }
        public double Score { get; set; } = double.NaN;
        public ScoreOrderType ScoreOrderType { get; set; } = ScoreOrderType.Positive;
        public bool IncludeMinScore
        {
            get
            {
                if (!IsSmoonthScore)
                {
                    return true;
                }
                else
                {
                    if (ScoreOrderType == ScoreOrderType.Positive)
                    {//降序 评分最小值与指标最小值一致
                        return MinIncluded;
                    }
                    else
                    {
                        return MaxIncluded;
                    }
                }
            }
        }
        public bool IncludeMaxScore
        {
            get
            {
                if (!IsSmoonthScore)
                {
                    return true;
                }
                else
                {
                    if (ScoreOrderType == ScoreOrderType.Positive)
                    {//降序 评分最大值与指标最大值一致
                        return MaxIncluded;
                    }
                    else
                    {
                        return MinIncluded;
                    }
                }
            }
        }

        public string ScoreRangeDescription
        {
            get
            {
                string ret = ScoreRangeMin.ToString();
                if (IsSmoonthScore)
                {
                    string minScoreStr;
                    string maxSocreStr;

                    if (ScoreOrderType == ScoreOrderType.Positive)
                    {
                        minScoreStr = getPositiveScoreStr(IncludeMinScore);
                        maxSocreStr = getPositiveScoreStr(IncludeMaxScore);
                        ret = string.Format("{0:F1} {2} x {3} {1:F1}", ScoreRangeMin, ScoreRangeMax, minScoreStr, maxSocreStr);
                    }
                    else
                    {
                        minScoreStr = getNegativeScoreStr(IncludeMinScore);
                        maxSocreStr = getNegativeScoreStr(IncludeMaxScore);
                        ret = string.Format("{0:F1} {2} x {3} {1:F1}", ScoreRangeMax, ScoreRangeMin, maxSocreStr, minScoreStr);
                    }
                }
                return ret;
            }
        }

        private string getPositiveScoreStr(bool IncludeScore)
        {
            string scoreStr;
            if (IncludeScore)
            {
                scoreStr = "<=";
            }
            else
            {
                scoreStr = "<";
            }

            return scoreStr;
        }

        private string getNegativeScoreStr(bool IncludeScore)
        {
            string scoreStr;
            if (IncludeScore)
            {
                scoreStr = ">=";
            }
            else
            {
                scoreStr = ">";
            }

            return scoreStr;
        }

        public bool Within(float value, out double score)
        {
            bool within = Within(value);
            score = double.NaN;
            if (within)
            {
                score = GetScoreByValue(value);
            }
            return within;
        }

        public double GetScoreByValue(double value)
        {
            double score = ScoreRangeMin;
            if (IsSmoonthScore)
            {
                if (ScoreOrderType == ScoreOrderType.Positive)
                {
                    score = ScoreRangeMin + ((value - Min) / (Max - Min)) * (ScoreRangeMax - ScoreRangeMin);
                }
                else
                {
                    score = ScoreRangeMax - ((value - Min) / (Max - Min)) * (ScoreRangeMax - ScoreRangeMin);
                }
            }
            return score;
        }

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="configFile"></param>
        /// <param name="config"></param>
        /// <param name="name"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public XmlElement AddItem(XmlConfigFile configFile, XmlElement config, string name, object value)
        {
            if (value is CQTKPIScoreColorRange)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                configFile.AddTypeAttribute(item, typeof(CQTKPIScoreColorRange));
                configFile.AddItem(item, "ScoreRangeMin", ScoreRangeMin);
                configFile.AddItem(item, "ScoreRangeMax", ScoreRangeMax);
                configFile.AddItem(item, "MinKPIValue", Min);
                configFile.AddItem(item, "MaxKPIValue", Max);
                configFile.AddItem(item, "MinIncluded", MinIncluded);
                configFile.AddItem(item, "MaxIncluded", MaxIncluded);
                configFile.AddItem(item, "R", Color.R);
                configFile.AddItem(item, "G", Color.G);
                configFile.AddItem(item, "B", Color.B);
                configFile.AddItem(item, "Desc", DesInfo);
                configFile.AddItem(item, "Visible", Visible);
                configFile.AddItem(item, "IsSmoothScore", IsSmoonthScore);
                return item;
            }
            return null;
        }

        /// <summary>
        /// 加载
        /// </summary>
        /// <param name="configFile"></param>
        /// <param name="item"></param>
        /// <param name="typeName"></param>
        /// <returns></returns>
        public object GetItemValue(XmlConfigFile configFile, XmlElement item, string typeName)
        {
            if (typeName == "CQTKPIScoreColorRange")
            {
                setScoreRange(configFile, item);
                setKPIValue(configFile, item);
                setColor(configFile, item);
                object value = configFile.GetItemValue(item, "MinIncluded");
                if (value != null)
                {
                    MinIncluded = (bool)value;
                }
                value = configFile.GetItemValue(item, "MaxIncluded");
                if (value != null)
                {
                    MaxIncluded = (bool)value;
                }
                value = configFile.GetItemValue(item, "Visible");
                if (value != null)
                {
                    Visible = (bool)value;
                }
                DesInfo = configFile.GetItemValue(item, "Desc") as string;
                object o = configFile.GetItemValue(item, "IsSmoothScore");
                if (o != null)
                {
                    IsSmoonthScore = (bool)o;
                }
                return this;
            }
            return null;
        }

        private void setScoreRange(XmlConfigFile configFile, XmlElement item)
        {
            object value = configFile.GetItemValue(item, "ScoreRangeMin");
            if (value != null)
            {
                ScoreRangeMin = (double)value;
            }
            value = configFile.GetItemValue(item, "ScoreRangeMax");
            if (value != null)
            {
                ScoreRangeMax = (double)value;
            }
        }

        private void setKPIValue(XmlConfigFile configFile, XmlElement item)
        {
            object value = configFile.GetItemValue(item, "MinKPIValue");
            if (value != null)
            {
                this.Min = (float)value;
            }
            value = configFile.GetItemValue(item, "MaxKPIValue");
            if (value != null)
            {
                this.Max = (float)value;
            }
        }

        private void setColor(XmlConfigFile configFile, XmlElement item)
        {
            object value;
            int r = 0, g = 0, b = 0;
            value = configFile.GetItemValue(item, "R");
            if (value != null)
            {
                r = (int)value;
            }
            value = configFile.GetItemValue(item, "G");
            if (value != null)
            {
                g = (int)value;
            }
            value = configFile.GetItemValue(item, "B");
            if (value != null)
            {
                b = (int)value;
            }
            this.Color = Color.FromArgb(r, g, b);
        }
    }

    public enum ScoreOrderType
    {
        /// <summary>
        /// 正向打分，指标数值大得分大
        /// </summary>
        Positive = 0,
        /// <summary>
        /// 逆向打分，指标数值小得分大
        /// </summary>
        Negative
    }

}
