<?xml version="1.0" encoding="UTF-8"?>
<Configs>
	<Config name="StatParamCfg">
		<Item name="configs" typeName="IList">
			<Item typeName="IDictionary">
				<Item typeName="String" key="Name">扫频参数</Item>
				<Item typeName="String" key="FName"/>
				<Item typeName="IList" key="children">
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">测试时长 scDuration</Item>
						<Item typeName="String" key="FName">scDuration</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">测试距离 scDistance</Item>
						<Item typeName="String" key="FName">scDistance</Item>
						<Item typeName="Int32" key="FTag">-1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">第一远距离 smMaxDist</Item>
						<Item typeName="String" key="FName">smMaxDist</Item>
						<Item typeName="Int32" key="FTag">0</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">第二远距离 smMaxDist</Item>
						<Item typeName="String" key="FName">smMaxDist</Item>
						<Item typeName="Int32" key="FTag">1</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">第三远距离 smMaxDist</Item>
						<Item typeName="String" key="FName">smMaxDist</Item>
						<Item typeName="Int32" key="FTag">2</Item>
						<Item typeName="IList" key="children"/>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">第一强小区</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">采样点数 scRxLevNum1</Item>
								<Item typeName="String" key="FName">scRxLevNum1</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">最大值 scRxLev1Max</Item>
								<Item typeName="String" key="FName">scRxLev1Max</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">最小值 scRxLev1Min</Item>
								<Item typeName="String" key="FName">scRxLev1Min</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">平均值 scRxLev1Mean</Item>
								<Item typeName="String" key="FName">scRxLev1Mean</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-10,-45]的数目 scRxLev1[0]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">0</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-46,-50]的数目 scRxLev1[1]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-51,-55]的数目 scRxLev1[2]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">2</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-56,-60]的数目 scRxLev1[3]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">3</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-61,-65]的数目 scRxLev1[4]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">4</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-66,-70]的数目 scRxLev1[5]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">5</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-71,-75]的数目 scRxLev1[6]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">6</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-76,-80]的数目 scRxLev1[7]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">7</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-81db的数目 scRxLev1[8]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">8</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-82db的数目 scRxLev1[9]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">9</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-83db的数目 scRxLev1[10]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">10</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-84db的数目 scRxLev1[11]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">11</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-85db的数目 scRxLev1[12]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">12</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-86db的数目 scRxLev1[13]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">13</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-87db的数目 scRxLev1[14]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">14</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-88db的数目 scRxLev1[15]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">15</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-89db的数目 scRxLev1[16]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">16</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-90db的数目 scRxLev1[17]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">17</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-91db的数目 scRxLev1[18]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">18</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-92db的数目 scRxLev1[19]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">19</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-93db的数目 scRxLev1[20]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">20</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-94db的数目 scRxLev1[21]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">21</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-95db的数目 scRxLev1[22]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">22</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-96db的数目 scRxLev1[23]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">23</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-97db的数目 scRxLev1[24]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">24</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-98db的数目 scRxLev1[25]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">25</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-99,-120]的数目 scRxLev1[26]</Item>
								<Item typeName="String" key="FName">scRxLev1</Item>
								<Item typeName="Int32" key="FTag">26</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">第二强小区</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">采样点数 scRxLevNum2</Item>
								<Item typeName="String" key="FName">scRxLevNum2</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">最大值 scRxLev2Max</Item>
								<Item typeName="String" key="FName">scRxLev2Max</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">最小值 scRxLev2Min</Item>
								<Item typeName="String" key="FName">scRxLev2Min</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">平均值 scRxLev2Mean</Item>
								<Item typeName="String" key="FName">scRxLev2Mean</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-10,-45]的数目 scRxLev2[0]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">0</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-46,-50]的数目 scRxLev2[1]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-51,-55]的数目 scRxLev2[2]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">2</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-56,-60]的数目 scRxLev2[3]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">3</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-61,-65]的数目 scRxLev2[4]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">4</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-66,-70]的数目 scRxLev2[5]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">5</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-71,-75]的数目 scRxLev2[6]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">6</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-76,-80]的数目 scRxLev2[7]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">7</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-81db的数目 scRxLev2[8]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">8</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-82db的数目 scRxLev2[9]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">9</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-83db的数目 scRxLev2[10]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">10</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-84db的数目 scRxLev2[11]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">11</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-85db的数目 scRxLev2[12]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">12</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-86db的数目 scRxLev2[13]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">13</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-87db的数目 scRxLev2[14]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">14</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-88db的数目 scRxLev2[15]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">15</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-89db的数目 scRxLev2[16]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">16</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-90db的数目 scRxLev2[17]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">17</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-91db的数目 scRxLev2[18]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">18</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-92db的数目 scRxLev2[19]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">19</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-93db的数目 scRxLev2[20]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">20</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-94db的数目 scRxLev2[21]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">21</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-95db的数目 scRxLev2[22]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">22</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-96db的数目 scRxLev2[23]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">23</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-97db的数目 scRxLev2[24]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">24</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-98db的数目 scRxLev2[25]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">25</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-99,-120]的数目 scRxLev2[26]</Item>
								<Item typeName="String" key="FName">scRxLev2</Item>
								<Item typeName="Int32" key="FTag">26</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">第三强小区</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">采样点数 scRxLevNum3</Item>
								<Item typeName="String" key="FName">scRxLevNum3</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">最大值 scRxLev3Max</Item>
								<Item typeName="String" key="FName">scRxLev3Max</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">最小值 scRxLev3Min</Item>
								<Item typeName="String" key="FName">scRxLev3Min</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-10,-45]的数目 scRxLev3[0]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">0</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-46,-50]的数目 scRxLev3[1]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-51,-55]的数目 scRxLev3[2]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">2</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-56,-60]的数目 scRxLev3[3]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">3</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-61,-65]的数目 scRxLev3[4]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">4</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-66,-70]的数目 scRxLev3[5]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">5</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-71,-75]的数目 scRxLev3[6]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">6</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-76,-80]的数目 scRxLev3[7]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">7</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-81db的数目 scRxLev3[8]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">8</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-82db的数目 scRxLev3[9]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">9</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-83db的数目 scRxLev3[10]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">10</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-84db的数目 scRxLev3[11]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">11</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-85db的数目 scRxLev3[12]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">12</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-86db的数目 scRxLev3[13]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">13</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-87db的数目 scRxLev3[14]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">14</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-88db的数目 scRxLev3[15]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">15</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-89db的数目 scRxLev3[16]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">16</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-90db的数目 scRxLev3[17]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">17</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-91db的数目 scRxLev3[18]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">18</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-92db的数目 scRxLev3[19]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">19</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-93db的数目 scRxLev3[20]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">20</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-94db的数目 scRxLev3[21]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">21</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-95db的数目 scRxLev3[22]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">22</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-96db的数目 scRxLev3[23]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">23</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-97db的数目 scRxLev3[24]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">24</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">-98db的数目 scRxLev3[25]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">25</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-99,-120]的数目 scRxLev3[26]</Item>
								<Item typeName="String" key="FName">scRxLev3</Item>
								<Item typeName="Int32" key="FTag">26</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">第一干扰</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">最大值 scC_I1Max</Item>
								<Item typeName="String" key="FName">scC_I1Max</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">最小值 scC_I1Min</Item>
								<Item typeName="String" key="FName">scC_I1Max</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-20,-9]的数目 scC_I1[0]</Item>
								<Item typeName="String" key="FName">scC_I1</Item>
								<Item typeName="Int32" key="FTag">0</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-9,12)的数目 scC_I1[1]</Item>
								<Item typeName="String" key="FName">scC_I1</Item>
								<Item typeName="Int32" key="FTag">1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[12,15)的数目 scC_I1[2]</Item>
								<Item typeName="String" key="FName">scC_I1</Item>
								<Item typeName="Int32" key="FTag">2</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[15,20)的数目 scC_I1[3]</Item>
								<Item typeName="String" key="FName">scC_I1</Item>
								<Item typeName="Int32" key="FTag">3</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[20,40]的数目 scC_I1[4]</Item>
								<Item typeName="String" key="FName">scC_I1</Item>
								<Item typeName="Int32" key="FTag">4</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-20,-9]值之和 scC_I1Value[0]</Item>
								<Item typeName="String" key="FName">scC_I1Value</Item>
								<Item typeName="Int32" key="FTag">0</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-9,12)值之和scC_I1Value[1]</Item>
								<Item typeName="String" key="FName">scC_I1Value</Item>
								<Item typeName="Int32" key="FTag">1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[12,15)值之和 scC_I1Value[2]</Item>
								<Item typeName="String" key="FName">scC_I1Value</Item>
								<Item typeName="Int32" key="FTag">2</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[15,20)值之和 scC_I1Value[3]</Item>
								<Item typeName="String" key="FName">scC_I1Value</Item>
								<Item typeName="Int32" key="FTag">3</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[20,40]值之和 scC_I1Value[4]</Item>
								<Item typeName="String" key="FName">scC_I1Value</Item>
								<Item typeName="Int32" key="FTag">4</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">第二干扰</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">最大值 scC_I2Max</Item>
								<Item typeName="String" key="FName">scC_I2Max</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">最小值 scC_I2Min</Item>
								<Item typeName="String" key="FName">scC_I2Max</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-20,-9]的数目 scC_I2[0]</Item>
								<Item typeName="String" key="FName">scC_I2</Item>
								<Item typeName="Int32" key="FTag">0</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-9,12)的数目 scC_I2[1]</Item>
								<Item typeName="String" key="FName">scC_I2</Item>
								<Item typeName="Int32" key="FTag">1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[12,15)的数目 scC_I2[2]</Item>
								<Item typeName="String" key="FName">scC_I2</Item>
								<Item typeName="Int32" key="FTag">2</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[15,20)的数目 scC_I2[3]</Item>
								<Item typeName="String" key="FName">scC_I2</Item>
								<Item typeName="Int32" key="FTag">3</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[20,40]的数目 scC_I2[4]</Item>
								<Item typeName="String" key="FName">scC_I2</Item>
								<Item typeName="Int32" key="FTag">4</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-20,-9]值之和 scC_I2Value[0]</Item>
								<Item typeName="String" key="FName">scC_I2Value</Item>
								<Item typeName="Int32" key="FTag">0</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-9,12)值之和scC_I2Value[1]</Item>
								<Item typeName="String" key="FName">scC_I2Value</Item>
								<Item typeName="Int32" key="FTag">1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[12,15)值之和 scC_I2Value[2]</Item>
								<Item typeName="String" key="FName">scC_I2Value</Item>
								<Item typeName="Int32" key="FTag">2</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[15,20)值之和 scC_I2Value[3]</Item>
								<Item typeName="String" key="FName">scC_I2Value</Item>
								<Item typeName="Int32" key="FTag">3</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[20,40]值之和 scC_I2Value[4]</Item>
								<Item typeName="String" key="FName">scC_I2Value</Item>
								<Item typeName="Int32" key="FTag">4</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">第三干扰</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">最大值 scC_I3Max</Item>
								<Item typeName="String" key="FName">scC_I3Max</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">最小值 scC_I3Min</Item>
								<Item typeName="String" key="FName">scC_I3Max</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-20,-9]的数目 scC_I3[0]</Item>
								<Item typeName="String" key="FName">scC_I3</Item>
								<Item typeName="Int32" key="FTag">0</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-9,12)的数目 scC_I3[1]</Item>
								<Item typeName="String" key="FName">scC_I3</Item>
								<Item typeName="Int32" key="FTag">1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[12,15)的数目 scC_I3[2]</Item>
								<Item typeName="String" key="FName">scC_I3</Item>
								<Item typeName="Int32" key="FTag">2</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[15,20)的数目 scC_I3[3]</Item>
								<Item typeName="String" key="FName">scC_I3</Item>
								<Item typeName="Int32" key="FTag">3</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[20,40]的数目 scC_I3[4]</Item>
								<Item typeName="String" key="FName">scC_I3</Item>
								<Item typeName="Int32" key="FTag">4</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-20,-9]值之和 scC_I3Value[0]</Item>
								<Item typeName="String" key="FName">scC_I3Value</Item>
								<Item typeName="Int32" key="FTag">0</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[-9,12)值之和scC_I3Value[1]</Item>
								<Item typeName="String" key="FName">scC_I3Value</Item>
								<Item typeName="Int32" key="FTag">1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[12,15)值之和 scC_I3Value[2]</Item>
								<Item typeName="String" key="FName">scC_I3Value</Item>
								<Item typeName="Int32" key="FTag">2</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[15,20)值之和 scC_I3Value[3]</Item>
								<Item typeName="String" key="FName">scC_I3Value</Item>
								<Item typeName="Int32" key="FTag">3</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">[20,40]值之和 scC_I3Value[4]</Item>
								<Item typeName="String" key="FName">scC_I3Value</Item>
								<Item typeName="Int32" key="FTag">4</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
				</Item>
			</Item>
		</Item>
	</Config>
</Configs>
