﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NoCoverRoadSettingDlg_NR
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.numRSRP = new System.Windows.Forms.NumericUpDown();
            this.numMinDistance = new System.Windows.Forms.NumericUpDown();
            this.label7 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.numMaxTPDistance = new System.Windows.Forms.NumericUpDown();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.label15 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.numMinNoCoverPercent = new System.Windows.Forms.NumericUpDown();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label1 = new System.Windows.Forms.Label();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.chkMinDuration = new System.Windows.Forms.CheckBox();
            this.chkMinDistance = new System.Windows.Forms.CheckBox();
            this.label18 = new System.Windows.Forms.Label();
            this.numMinDuration = new System.Windows.Forms.NumericUpDown();
            this.chkFilterMultiVoice = new System.Windows.Forms.CheckBox();
            ((System.ComponentModel.ISupportInitialize)(this.numRSRP)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxTPDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinNoCoverPercent)).BeginInit();
            this.groupBox2.SuspendLayout();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDuration)).BeginInit();
            this.SuspendLayout();
            // 
            // numRSRP
            // 
            this.numRSRP.Location = new System.Drawing.Point(96, 20);
            this.numRSRP.Maximum = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numRSRP.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numRSRP.Name = "numRSRP";
            this.numRSRP.Size = new System.Drawing.Size(80, 21);
            this.numRSRP.TabIndex = 0;
            this.numRSRP.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRSRP.Value = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            // 
            // numMinDistance
            // 
            this.numMinDistance.Location = new System.Drawing.Point(375, 20);
            this.numMinDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numMinDistance.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numMinDistance.Name = "numMinDistance";
            this.numMinDistance.Size = new System.Drawing.Size(80, 21);
            this.numMinDistance.TabIndex = 3;
            this.numMinDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMinDistance.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(224, 59);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(17, 12);
            this.label7.TabIndex = 55;
            this.label7.Text = "米";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(182, 24);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(113, 12);
            this.label3.TabIndex = 47;
            this.label3.Text = "dBm 或 无SS-RSRP值";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(31, 59);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(101, 12);
            this.label6.TabIndex = 53;
            this.label6.Text = "相邻采样点距离≤";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(462, 25);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(17, 12);
            this.label4.TabIndex = 48;
            this.label4.Text = "米";
            // 
            // numMaxTPDistance
            // 
            this.numMaxTPDistance.Location = new System.Drawing.Point(137, 54);
            this.numMaxTPDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numMaxTPDistance.Name = "numMaxTPDistance";
            this.numMaxTPDistance.Size = new System.Drawing.Size(80, 21);
            this.numMaxTPDistance.TabIndex = 4;
            this.numMaxTPDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMaxTPDistance.Value = new decimal(new int[] {
            20,
            0,
            0,
            0});
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.Location = new System.Drawing.Point(336, 214);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 5;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(437, 214);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 6;
            this.btnCancel.Text = "取消";
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(212, 25);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(11, 12);
            this.label15.TabIndex = 62;
            this.label15.Text = "%";
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(31, 26);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(89, 12);
            this.label16.TabIndex = 61;
            this.label16.Text = "弱覆盖点占比≥";
            // 
            // numMinNoCoverPercent
            // 
            this.numMinNoCoverPercent.Location = new System.Drawing.Point(123, 20);
            this.numMinNoCoverPercent.Name = "numMinNoCoverPercent";
            this.numMinNoCoverPercent.Size = new System.Drawing.Size(80, 21);
            this.numMinNoCoverPercent.TabIndex = 60;
            this.numMinNoCoverPercent.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMinNoCoverPercent.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.chkFilterMultiVoice);
            this.groupBox2.Controls.Add(this.label1);
            this.groupBox2.Controls.Add(this.label3);
            this.groupBox2.Controls.Add(this.numRSRP);
            this.groupBox2.Location = new System.Drawing.Point(12, 12);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(500, 84);
            this.groupBox2.TabIndex = 63;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "采样点指标限定";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(31, 26);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(59, 12);
            this.label1.TabIndex = 80;
            this.label1.Text = "SS-RSRP≤";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.chkMinDuration);
            this.groupBox3.Controls.Add(this.chkMinDistance);
            this.groupBox3.Controls.Add(this.label18);
            this.groupBox3.Controls.Add(this.numMinDuration);
            this.groupBox3.Controls.Add(this.numMaxTPDistance);
            this.groupBox3.Controls.Add(this.label15);
            this.groupBox3.Controls.Add(this.label4);
            this.groupBox3.Controls.Add(this.label16);
            this.groupBox3.Controls.Add(this.label6);
            this.groupBox3.Controls.Add(this.numMinNoCoverPercent);
            this.groupBox3.Controls.Add(this.label7);
            this.groupBox3.Controls.Add(this.numMinDistance);
            this.groupBox3.Location = new System.Drawing.Point(12, 102);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(500, 96);
            this.groupBox3.TabIndex = 64;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "持续性";
            // 
            // chkMinDuration
            // 
            this.chkMinDuration.AutoSize = true;
            this.chkMinDuration.Location = new System.Drawing.Point(287, 59);
            this.chkMinDuration.Name = "chkMinDuration";
            this.chkMinDuration.Size = new System.Drawing.Size(84, 16);
            this.chkMinDuration.TabIndex = 67;
            this.chkMinDuration.Text = "持续时长≥";
            this.chkMinDuration.UseVisualStyleBackColor = true;
            this.chkMinDuration.CheckedChanged += new System.EventHandler(this.chkMinDuration_CheckedChanged);
            // 
            // chkMinDistance
            // 
            this.chkMinDistance.AutoSize = true;
            this.chkMinDistance.Checked = true;
            this.chkMinDistance.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkMinDistance.Location = new System.Drawing.Point(286, 24);
            this.chkMinDistance.Name = "chkMinDistance";
            this.chkMinDistance.Size = new System.Drawing.Size(84, 16);
            this.chkMinDistance.TabIndex = 66;
            this.chkMinDistance.Text = "持续距离≥";
            this.chkMinDistance.UseVisualStyleBackColor = true;
            this.chkMinDistance.CheckedChanged += new System.EventHandler(this.chkMinDistance_CheckedChanged);
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(462, 59);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(17, 12);
            this.label18.TabIndex = 65;
            this.label18.Text = "秒";
            // 
            // numMinDuration
            // 
            this.numMinDuration.Enabled = false;
            this.numMinDuration.Location = new System.Drawing.Point(375, 54);
            this.numMinDuration.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numMinDuration.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numMinDuration.Name = "numMinDuration";
            this.numMinDuration.Size = new System.Drawing.Size(80, 21);
            this.numMinDuration.TabIndex = 63;
            this.numMinDuration.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMinDuration.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // chkFilterMultiVoice
            // 
            this.chkFilterMultiVoice.AutoSize = true;
            this.chkFilterMultiVoice.Checked = true;
            this.chkFilterMultiVoice.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkFilterMultiVoice.Location = new System.Drawing.Point(33, 55);
            this.chkFilterMultiVoice.Name = "chkFilterMultiVoice";
            this.chkFilterMultiVoice.Size = new System.Drawing.Size(192, 16);
            this.chkFilterMultiVoice.TabIndex = 81;
            this.chkFilterMultiVoice.Text = "剔除并发业务中的语音通话过程";
            this.chkFilterMultiVoice.UseVisualStyleBackColor = true;
            // 
            // NoCoverRoadSettingDlg_NR
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(527, 251);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Name = "NoCoverRoadSettingDlg_NR";
            this.Text = "无覆盖路段设置";
            ((System.ComponentModel.ISupportInitialize)(this.numRSRP)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxTPDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinNoCoverPercent)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDuration)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.NumericUpDown numRSRP;
        private System.Windows.Forms.NumericUpDown numMinDistance;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown numMaxTPDistance;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.NumericUpDown numMinNoCoverPercent;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.NumericUpDown numMinDuration;
        private System.Windows.Forms.CheckBox chkMinDuration;
        private System.Windows.Forms.CheckBox chkMinDistance;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.CheckBox chkFilterMultiVoice;
    }
}