﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.Model.RoadProtection
{
    public partial class XtraFormStatus : DevExpress.XtraEditors.XtraForm
    {
        public XtraFormStatus()
        {
            InitializeComponent();
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (!checkBox0.Checked && !checkBox1.Checked && !checkBox2.Checked && !checkBox3.Checked)
            {
                this.DialogResult = DialogResult.Cancel;
            }
            else
            {
                this.DialogResult = DialogResult.OK;
            }
            
        }

        private void button1_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }


        public void getStatusSelect(out List<bool> cb)
        {
            cb = new List<bool>();
            cb.Add(checkBox0.Checked);
            cb.Add(checkBox1.Checked);
            cb.Add(checkBox2.Checked);
            cb.Add(checkBox3.Checked);
        }
    }
}