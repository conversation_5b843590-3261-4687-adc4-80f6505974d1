﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTTDCpiInterfereQuery : QueryBase
    {
        public ZTTDCpiInterfereQuery(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override string Name
        {
            get { return "TD扰码相关性查询"; }
        }

        public override string IconName
        {
            get { return "images/通用专题/小区集.png"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 19000, 19014, this.Name);
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(CpiInterfereForm).FullName);
            CpiInterfereForm cpiInterfereForm = obj == null ? null : obj as CpiInterfereForm;
            if (cpiInterfereForm == null || cpiInterfereForm.IsDisposed)
            {
                cpiInterfereForm = new CpiInterfereForm(MainModel);
            }

            cpiInterfereForm.Show(MainModel.MainForm);
        }
    }
}
