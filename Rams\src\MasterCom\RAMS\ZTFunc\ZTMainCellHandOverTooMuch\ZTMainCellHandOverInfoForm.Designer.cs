﻿namespace MasterCom.RAMS.Frame
{
    partial class ZTMainCellHandOverInfoForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.listViewTotal = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnFileName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHandOverTimes = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLength = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLengthMean = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLongitudeFirst = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLatitudeFirst = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLongitudeLast = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLatitudeLast = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCells = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLac = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCI = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemFoldAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemExport = new System.Windows.Forms.ToolStripMenuItem();
            this.olvColumnBCCH = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBSIC = new BrightIdeasSoftware.OLVColumn();
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // listViewTotal
            // 
            this.listViewTotal.AllColumns.Add(this.olvColumnSN);
            this.listViewTotal.AllColumns.Add(this.olvColumnFileName);
            this.listViewTotal.AllColumns.Add(this.olvColumnHandOverTimes);
            this.listViewTotal.AllColumns.Add(this.olvColumnLength);
            this.listViewTotal.AllColumns.Add(this.olvColumnLengthMean);
            this.listViewTotal.AllColumns.Add(this.olvColumnLongitudeFirst);
            this.listViewTotal.AllColumns.Add(this.olvColumnLatitudeFirst);
            this.listViewTotal.AllColumns.Add(this.olvColumnLongitudeLast);
            this.listViewTotal.AllColumns.Add(this.olvColumnLatitudeLast);
            this.listViewTotal.AllColumns.Add(this.olvColumnCells);
            this.listViewTotal.AllColumns.Add(this.olvColumnLac);
            this.listViewTotal.AllColumns.Add(this.olvColumnCI);
            this.listViewTotal.AllColumns.Add(this.olvColumnBCCH);
            this.listViewTotal.AllColumns.Add(this.olvColumnBSIC);
            this.listViewTotal.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnFileName,
            this.olvColumnHandOverTimes,
            this.olvColumnLength,
            this.olvColumnLengthMean,
            this.olvColumnLongitudeFirst,
            this.olvColumnLatitudeFirst,
            this.olvColumnLongitudeLast,
            this.olvColumnLatitudeLast,
            this.olvColumnCells,
            this.olvColumnLac,
            this.olvColumnCI,
            this.olvColumnBCCH,
            this.olvColumnBSIC});
            this.listViewTotal.ContextMenuStrip = this.contextMenuStrip1;
            this.listViewTotal.Cursor = System.Windows.Forms.Cursors.Default;
            this.listViewTotal.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewTotal.FullRowSelect = true;
            this.listViewTotal.GridLines = true;
            this.listViewTotal.HeaderWordWrap = true;
            this.listViewTotal.IsNeedShowOverlay = false;
            this.listViewTotal.Location = new System.Drawing.Point(0, 0);
            this.listViewTotal.Name = "listViewTotal";
            this.listViewTotal.OwnerDraw = true;
            this.listViewTotal.ShowGroups = false;
            this.listViewTotal.Size = new System.Drawing.Size(897, 353);
            this.listViewTotal.TabIndex = 4;
            this.listViewTotal.UseCompatibleStateImageBehavior = false;
            this.listViewTotal.View = System.Windows.Forms.View.Details;
            this.listViewTotal.VirtualMode = true;
            this.listViewTotal.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewTotal_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "Index";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 87;
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名";
            this.olvColumnFileName.Width = 120;
            // 
            // olvColumnHandOverTimes
            // 
            this.olvColumnHandOverTimes.HeaderFont = null;
            this.olvColumnHandOverTimes.Text = "小区变化次数";
            this.olvColumnHandOverTimes.Width = 80;
            // 
            // olvColumnLength
            // 
            this.olvColumnLength.HeaderFont = null;
            this.olvColumnLength.Text = "持续距离";
            this.olvColumnLength.Width = 80;
            // 
            // olvColumnLengthMean
            // 
            this.olvColumnLengthMean.HeaderFont = null;
            this.olvColumnLengthMean.Text = "平均间隔";
            this.olvColumnLengthMean.Width = 80;
            // 
            // olvColumnLongitudeFirst
            // 
            this.olvColumnLongitudeFirst.HeaderFont = null;
            this.olvColumnLongitudeFirst.Text = "起始点经度";
            this.olvColumnLongitudeFirst.Width = 80;
            // 
            // olvColumnLatitudeFirst
            // 
            this.olvColumnLatitudeFirst.HeaderFont = null;
            this.olvColumnLatitudeFirst.Text = "起始点纬度";
            this.olvColumnLatitudeFirst.Width = 80;
            // 
            // olvColumnLongitudeLast
            // 
            this.olvColumnLongitudeLast.HeaderFont = null;
            this.olvColumnLongitudeLast.Text = "末尾点经度";
            this.olvColumnLongitudeLast.Width = 80;
            // 
            // olvColumnLatitudeLast
            // 
            this.olvColumnLatitudeLast.HeaderFont = null;
            this.olvColumnLatitudeLast.Text = "末尾点纬度";
            this.olvColumnLatitudeLast.Width = 80;
            // 
            // olvColumnCells
            // 
            this.olvColumnCells.HeaderFont = null;
            this.olvColumnCells.Text = "小区变化列表";
            this.olvColumnCells.Width = 100;
            // 
            // olvColumnLac
            // 
            this.olvColumnLac.HeaderFont = null;
            this.olvColumnLac.Text = "LAC";
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI";
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExpandAll,
            this.ToolStripMenuItemFoldAll,
            this.ToolStripMenuItemExport});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(119, 70);
            // 
            // ToolStripMenuItemExpandAll
            // 
            this.ToolStripMenuItemExpandAll.Name = "ToolStripMenuItemExpandAll";
            this.ToolStripMenuItemExpandAll.Size = new System.Drawing.Size(118, 22);
            this.ToolStripMenuItemExpandAll.Text = "全部展开";
            this.ToolStripMenuItemExpandAll.Click += new System.EventHandler(this.ToolStripMenuItemExpandAll_Click);
            // 
            // ToolStripMenuItemFoldAll
            // 
            this.ToolStripMenuItemFoldAll.Name = "ToolStripMenuItemFoldAll";
            this.ToolStripMenuItemFoldAll.Size = new System.Drawing.Size(118, 22);
            this.ToolStripMenuItemFoldAll.Text = "全部折叠";
            this.ToolStripMenuItemFoldAll.Click += new System.EventHandler(this.ToolStripMenuItemFoldAll_Click);
            // 
            // ToolStripMenuItemExport
            // 
            this.ToolStripMenuItemExport.Name = "ToolStripMenuItemExport";
            this.ToolStripMenuItemExport.Size = new System.Drawing.Size(118, 22);
            this.ToolStripMenuItemExport.Text = "导出列表";
            this.ToolStripMenuItemExport.Click += new System.EventHandler(this.ToolStripMenuItemExport_Click);
            // 
            // olvColumnBCCH
            // 
            this.olvColumnBCCH.HeaderFont = null;
            this.olvColumnBCCH.Text = "BCCH";
            // 
            // olvColumnBSIC
            // 
            this.olvColumnBSIC.HeaderFont = null;
            this.olvColumnBSIC.Text = "BSIC";
            // 
            // ZTMainCellHandOverInfoForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(897, 353);
            this.Controls.Add(this.listViewTotal);
            this.Name = "ZTMainCellHandOverInfoForm";
            this.Text = "频繁切换路段分析列表";
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView listViewTotal;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnHandOverTimes;
        private BrightIdeasSoftware.OLVColumn olvColumnLength;
        private BrightIdeasSoftware.OLVColumn olvColumnLengthMean;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitudeFirst;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitudeFirst;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitudeLast;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitudeLast;
        private BrightIdeasSoftware.OLVColumn olvColumnCells;
        private BrightIdeasSoftware.OLVColumn olvColumnLac;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExpandAll;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemFoldAll;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExport;
        private BrightIdeasSoftware.OLVColumn olvColumnBCCH;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC;
    }
}