﻿using MasterCom.MControls;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRScanCellMultiCoverageColorRanges : ScanMultiCoverageColorRanges
    {
        private static NRScanCellMultiCoverageColorRanges instance = null;
        public static NRScanCellMultiCoverageColorRanges Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new NRScanCellMultiCoverageColorRanges();
                }
                return instance;
            }
        }

        public NRScanCellMultiCoverageColorRanges()
        {
            initialize();
        }

        public override string RangeName { get { return "NRScanCellMultiCoverageColorRanges"; } }

        protected override void initialize()
        {
            ColorRanges = new List<ColorRange>();
            ColorRanges.Add(new ColorRange(0, 2, Color.Cyan));
            ColorRanges.Add(new ColorRange(2, 4, Color.Lime));
            ColorRanges.Add(new ColorRange(4, 6, Color.Yellow));
            ColorRanges.Add(new ColorRange(6, 10, Color.Orange));
            ColorRanges.Add(new ColorRange(10, 50, Color.Red));
        }
    }
}
