﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRLTECollaborativeAnaByGridDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.label1 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.num4GWeakCover = new DevExpress.XtraEditors.SpinEdit();
            this.num5GWeakCover = new DevExpress.XtraEditors.SpinEdit();
            this.num4GBetterThan5G = new DevExpress.XtraEditors.SpinEdit();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.num5GBetterThan4G = new DevExpress.XtraEditors.SpinEdit();
            this.label12 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.groupControl4 = new DevExpress.XtraEditors.GroupControl();
            this.chkContiansNone = new DevExpress.XtraEditors.CheckEdit();
            this.chkContainsNRLTE = new DevExpress.XtraEditors.CheckEdit();
            ((System.ComponentModel.ISupportInitialize)(this.num4GWeakCover.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.num5GWeakCover.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.num4GBetterThan5G.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.num5GBetterThan4G.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).BeginInit();
            this.groupControl4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkContiansNone.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkContainsNRLTE.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(309, 331);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 12;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(214, 331);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 11;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label1.Location = new System.Drawing.Point(27, 37);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(77, 12);
            this.label1.TabIndex = 40;
            this.label1.Text = "4G覆盖差门限";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label5.Location = new System.Drawing.Point(27, 75);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(77, 12);
            this.label5.TabIndex = 42;
            this.label5.Text = "5G覆盖差门限";
            // 
            // num4GWeakCover
            // 
            this.num4GWeakCover.EditValue = new decimal(new int[] {
            95,
            0,
            0,
            -2147483648});
            this.num4GWeakCover.Location = new System.Drawing.Point(110, 33);
            this.num4GWeakCover.Name = "num4GWeakCover";
            this.num4GWeakCover.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.num4GWeakCover.Properties.Appearance.Options.UseFont = true;
            this.num4GWeakCover.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.num4GWeakCover.Properties.Mask.EditMask = "f0";
            this.num4GWeakCover.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.num4GWeakCover.Properties.MinValue = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.num4GWeakCover.Size = new System.Drawing.Size(70, 20);
            this.num4GWeakCover.TabIndex = 41;
            // 
            // num5GWeakCover
            // 
            this.num5GWeakCover.EditValue = new decimal(new int[] {
            93,
            0,
            0,
            -2147483648});
            this.num5GWeakCover.Location = new System.Drawing.Point(110, 71);
            this.num5GWeakCover.Name = "num5GWeakCover";
            this.num5GWeakCover.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.num5GWeakCover.Properties.Appearance.Options.UseFont = true;
            this.num5GWeakCover.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.num5GWeakCover.Properties.Mask.EditMask = "f0";
            this.num5GWeakCover.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.num5GWeakCover.Properties.MinValue = new decimal(new int[] {
            200,
            0,
            0,
            -2147483648});
            this.num5GWeakCover.Size = new System.Drawing.Size(70, 20);
            this.num5GWeakCover.TabIndex = 43;
            // 
            // num4GBetterThan5G
            // 
            this.num4GBetterThan5G.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.num4GBetterThan5G.Location = new System.Drawing.Point(158, 33);
            this.num4GBetterThan5G.Name = "num4GBetterThan5G";
            this.num4GBetterThan5G.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.num4GBetterThan5G.Properties.Appearance.Options.UseFont = true;
            this.num4GBetterThan5G.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.num4GBetterThan5G.Properties.Mask.EditMask = "f0";
            this.num4GBetterThan5G.Properties.MaxValue = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.num4GBetterThan5G.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.num4GBetterThan5G.Size = new System.Drawing.Size(70, 20);
            this.num4GBetterThan5G.TabIndex = 25;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label2.Location = new System.Drawing.Point(27, 36);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(125, 12);
            this.label2.TabIndex = 24;
            this.label2.Text = "4G场强大于5G平均场强";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label3.Location = new System.Drawing.Point(234, 36);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(41, 12);
            this.label3.TabIndex = 23;
            this.label3.Text = "db以上";
            // 
            // num5GBetterThan4G
            // 
            this.num5GBetterThan4G.EditValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.num5GBetterThan4G.Location = new System.Drawing.Point(158, 34);
            this.num5GBetterThan4G.Name = "num5GBetterThan4G";
            this.num5GBetterThan4G.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.num5GBetterThan4G.Properties.Appearance.Options.UseFont = true;
            this.num5GBetterThan4G.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.num5GBetterThan4G.Properties.Mask.EditMask = "f0";
            this.num5GBetterThan4G.Properties.MaxValue = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.num5GBetterThan4G.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.num5GBetterThan4G.Size = new System.Drawing.Size(70, 20);
            this.num5GBetterThan4G.TabIndex = 22;
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label12.Location = new System.Drawing.Point(27, 37);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(125, 12);
            this.label12.TabIndex = 20;
            this.label12.Text = "5G场强大于4G平均场强";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.Location = new System.Drawing.Point(234, 37);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(41, 12);
            this.label4.TabIndex = 18;
            this.label4.Text = "db以上";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.label1);
            this.groupControl1.Controls.Add(this.num5GWeakCover);
            this.groupControl1.Controls.Add(this.num4GWeakCover);
            this.groupControl1.Controls.Add(this.label5);
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(398, 105);
            this.groupControl1.TabIndex = 83;
            this.groupControl1.Text = "通用设置";
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.num4GBetterThan5G);
            this.groupControl2.Controls.Add(this.label2);
            this.groupControl2.Controls.Add(this.label3);
            this.groupControl2.Location = new System.Drawing.Point(0, 105);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(398, 62);
            this.groupControl2.TabIndex = 84;
            this.groupControl2.Text = "4G优于5G";
            // 
            // groupControl3
            // 
            this.groupControl3.Controls.Add(this.num5GBetterThan4G);
            this.groupControl3.Controls.Add(this.label12);
            this.groupControl3.Controls.Add(this.label4);
            this.groupControl3.Location = new System.Drawing.Point(0, 167);
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(398, 62);
            this.groupControl3.TabIndex = 85;
            this.groupControl3.Text = "5G优于4G";
            // 
            // groupControl4
            // 
            this.groupControl4.Controls.Add(this.chkContiansNone);
            this.groupControl4.Controls.Add(this.chkContainsNRLTE);
            this.groupControl4.Location = new System.Drawing.Point(0, 229);
            this.groupControl4.Name = "groupControl4";
            this.groupControl4.Size = new System.Drawing.Size(398, 89);
            this.groupControl4.TabIndex = 84;
            this.groupControl4.Text = "栅格设置";
            // 
            // chkContiansNone
            // 
            this.chkContiansNone.Location = new System.Drawing.Point(27, 61);
            this.chkContiansNone.Name = "chkContiansNone";
            this.chkContiansNone.Properties.Caption = "将无数据的栅格算入结果";
            this.chkContiansNone.Size = new System.Drawing.Size(184, 19);
            this.chkContiansNone.TabIndex = 1;
            // 
            // chkContainsNRLTE
            // 
            this.chkContainsNRLTE.Location = new System.Drawing.Point(27, 36);
            this.chkContainsNRLTE.Name = "chkContainsNRLTE";
            this.chkContainsNRLTE.Properties.Caption = "将锚点站数据算入4G栅格";
            this.chkContainsNRLTE.Size = new System.Drawing.Size(184, 19);
            this.chkContainsNRLTE.TabIndex = 0;
            // 
            // NRLTECollaborativeAnaByGridDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(398, 366);
            this.Controls.Add(this.groupControl4);
            this.Controls.Add(this.groupControl3);
            this.Controls.Add(this.groupControl2);
            this.Controls.Add(this.groupControl1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Name = "NRLTECollaborativeAnaByGridDlg";
            this.Text = "4/5G协同按栅格";
            ((System.ComponentModel.ISupportInitialize)(this.num4GWeakCover.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.num5GWeakCover.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.num4GBetterThan5G.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.num5GBetterThan4G.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.groupControl2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            this.groupControl3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).EndInit();
            this.groupControl4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.chkContiansNone.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkContainsNRLTE.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label5;
        private DevExpress.XtraEditors.SpinEdit num4GWeakCover;
        private DevExpress.XtraEditors.SpinEdit num5GWeakCover;
        private DevExpress.XtraEditors.SpinEdit num4GBetterThan5G;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private DevExpress.XtraEditors.SpinEdit num5GBetterThan4G;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label4;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private DevExpress.XtraEditors.GroupControl groupControl4;
        private DevExpress.XtraEditors.CheckEdit chkContainsNRLTE;
        private DevExpress.XtraEditors.CheckEdit chkContiansNone;
    }
}