﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.Stat
{
    public partial class DIYQueryAbnormalDataGridSettingForm : BaseDialog
    {
        public DIYQueryAbnormalDataGridSettingForm()
        {
            InitializeComponent();
        }

        public void GetAbnormalDataGridSet(out int GridSampleNum, out double GridRate , out float Smax)
        {
            GridSampleNum = int.Parse(editGridSampleNum.Value.ToString());
            GridRate = double.Parse(editGridRate.Value.ToString()) / 100;
            Smax = float.Parse(editSmax.Value.ToString());
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
