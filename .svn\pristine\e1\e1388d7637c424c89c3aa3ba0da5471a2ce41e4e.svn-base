﻿using System;
using System.IO;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTCellReverseDlg : BaseDialog
    {
        private static ZTCellReverseDlg instance = null;
        public static ZTCellReverseDlg GetInstance()
        {
            if (instance == null)
            {
                instance = new ZTCellReverseDlg();
            }
            return instance;
        }

        public ZTCellReverseDlg()
        {
            InitializeComponent();
            this.btnChooseFile.Focus();
        }

        private void btnChooseFile_Click(object sender, EventArgs e)
        {
            OpenFileDialog fileDlg = new OpenFileDialog();
            if(fileDlg.ShowDialog() == DialogResult.OK)
            {
                this.tBoxFile.Text = fileDlg.FileName;
            }
        }

        private void btnChooseFolder_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog folderDlg = new FolderBrowserDialog();
            if(folderDlg.ShowDialog() == DialogResult.OK)
            {
                this.tBoxFile.Text = folderDlg.SelectedPath;
            }
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            this.tBoxFile.Text = string.Empty;
        }
        
        private void btnOK_Click(object sender, EventArgs e)
        {
            if(string.Empty.Equals(tBoxFile.Text))
            {
                MessageBox.Show("请选择数据来源!!!");
            }
            else 
            {
                if(File.Exists(this.tBoxFile.Text))
                {
                    this.DialogResult = System.Windows.Forms.DialogResult.OK;
                    ZTCellReverse.file = this.tBoxFile.Text;
                    ZTCellReverse.fileDir = string.Empty;
                    ZTCellReverse.wrongPercent = (int)nUDWrongPercent.Value;
                }
                else if(Directory.Exists(this.tBoxFile.Text))
                {
                    this.DialogResult = System.Windows.Forms.DialogResult.OK;
                    ZTCellReverse.fileDir = this.tBoxFile.Text;
                    ZTCellReverse.file = string.Empty;
                    ZTCellReverse.wrongPercent = (int)nUDWrongPercent.Value;
                }
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }
    }

    public class WatermakTextBox : TextBox
    {
        private const int WM_PAINT = 0xF;
        private readonly string emptyTextTip = "请选择文件或文件夹路径";
        protected override void WndProc(ref Message m)
        {
            base.WndProc(ref m);
            if (m.Msg == WM_PAINT)
            {
                WmPaint();
            }
        }

        private void WmPaint()
        {
            using (Graphics graphics = Graphics.FromHwnd(base.Handle))
            {
                if (Text.Length == 0
                   && !string.IsNullOrEmpty(emptyTextTip)
                   && !Focused)
                {
                    TextFormatFlags format = TextFormatFlags.EndEllipsis | TextFormatFlags.VerticalCenter;

                    if (RightToLeft == RightToLeft.Yes)
                    {
                        format |= TextFormatFlags.RightToLeft | TextFormatFlags.Right;
                    }

                    TextRenderer.DrawText(graphics, emptyTextTip, Font, base.ClientRectangle, Color.FromArgb(100, Color.Gray), format);
                }
            }
        }
    }
}
