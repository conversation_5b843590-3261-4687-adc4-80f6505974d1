﻿namespace MasterCom.RAMS.Func.PopShow
{
    partial class BlackBlockInfoPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.panel1 = new System.Windows.Forms.Panel();
            this.label1 = new System.Windows.Forms.Label();
            this.splitMain = new System.Windows.Forms.SplitContainer();
            this.dataGridView = new System.Windows.Forms.DataGridView();
            this.tChartBlock = new Steema.TeeChart.TChart();
            this.panel2 = new System.Windows.Forms.Panel();
            this.btnPopShow = new System.Windows.Forms.Button();
            this.cbxTypeSel = new System.Windows.Forms.ComboBox();
            this.cbxShowType = new System.Windows.Forms.ComboBox();
            this.label4 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.cbxContentType = new System.Windows.Forms.ComboBox();
            this.label3 = new System.Windows.Forms.Label();
            this.btnGoBlackBlockQuery = new System.Windows.Forms.LinkLabel();
            this.ColumnTime = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnArea = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnCreated = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnClosed = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnRemainCount = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnCloseRate = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.panel1.SuspendLayout();
            this.splitMain.Panel1.SuspendLayout();
            this.splitMain.Panel2.SuspendLayout();
            this.splitMain.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).BeginInit();
            this.panel2.SuspendLayout();
            this.SuspendLayout();
            // 
            // panel1
            // 
            this.panel1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.panel1.BackColor = System.Drawing.Color.Silver;
            this.panel1.Controls.Add(this.label1);
            this.panel1.Location = new System.Drawing.Point(3, 3);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(1095, 30);
            this.panel1.TabIndex = 1;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label1.ForeColor = System.Drawing.Color.White;
            this.label1.Location = new System.Drawing.Point(19, 9);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(97, 14);
            this.label1.TabIndex = 0;
            this.label1.Text = "道路黑点情况";
            // 
            // splitMain
            // 
            this.splitMain.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.splitMain.Location = new System.Drawing.Point(13, 66);
            this.splitMain.Name = "splitMain";
            // 
            // splitMain.Panel1
            // 
            this.splitMain.Panel1.Controls.Add(this.dataGridView);
            // 
            // splitMain.Panel2
            // 
            this.splitMain.Panel2.Controls.Add(this.tChartBlock);
            this.splitMain.Size = new System.Drawing.Size(1085, 298);
            this.splitMain.SplitterDistance = 529;
            this.splitMain.TabIndex = 9;
            // 
            // dataGridView
            // 
            this.dataGridView.AllowUserToAddRows = false;
            this.dataGridView.AllowUserToDeleteRows = false;
            this.dataGridView.BackgroundColor = System.Drawing.Color.White;
            this.dataGridView.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.dataGridView.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.ColumnTime,
            this.ColumnArea,
            this.ColumnCreated,
            this.ColumnClosed,
            this.ColumnRemainCount,
            this.ColumnCloseRate});
            this.dataGridView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridView.Location = new System.Drawing.Point(0, 0);
            this.dataGridView.Name = "dataGridView";
            this.dataGridView.ReadOnly = true;
            this.dataGridView.RowHeadersVisible = false;
            this.dataGridView.RowTemplate.Height = 23;
            this.dataGridView.Size = new System.Drawing.Size(529, 298);
            this.dataGridView.TabIndex = 3;
            this.dataGridView.CellClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dataGridView_CellClick);
            // 
            // tChartBlock
            // 
            // 
            // 
            // 
            this.tChartBlock.Aspect.ElevationFloat = 345;
            this.tChartBlock.Aspect.RotationFloat = 345;
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartBlock.Axes.Bottom.Automatic = true;
            // 
            // 
            // 
            this.tChartBlock.Axes.Bottom.Grid.Style = System.Drawing.Drawing2D.DashStyle.Dash;
            this.tChartBlock.Axes.Bottom.Grid.ZPosition = 0;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartBlock.Axes.Bottom.Labels.Font.Shadow.Visible = false;
            this.tChartBlock.Axes.Bottom.Labels.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartBlock.Axes.Bottom.Labels.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartBlock.Axes.Bottom.Title.Font.Shadow.Visible = false;
            this.tChartBlock.Axes.Bottom.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartBlock.Axes.Bottom.Title.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartBlock.Axes.Depth.Automatic = true;
            // 
            // 
            // 
            this.tChartBlock.Axes.Depth.Grid.Style = System.Drawing.Drawing2D.DashStyle.Dash;
            this.tChartBlock.Axes.Depth.Grid.ZPosition = 0;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartBlock.Axes.Depth.Labels.Font.Shadow.Visible = false;
            this.tChartBlock.Axes.Depth.Labels.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartBlock.Axes.Depth.Labels.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartBlock.Axes.Depth.Title.Font.Shadow.Visible = false;
            this.tChartBlock.Axes.Depth.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartBlock.Axes.Depth.Title.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartBlock.Axes.DepthTop.Automatic = true;
            // 
            // 
            // 
            this.tChartBlock.Axes.DepthTop.Grid.Style = System.Drawing.Drawing2D.DashStyle.Dash;
            this.tChartBlock.Axes.DepthTop.Grid.ZPosition = 0;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartBlock.Axes.DepthTop.Labels.Font.Shadow.Visible = false;
            this.tChartBlock.Axes.DepthTop.Labels.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartBlock.Axes.DepthTop.Labels.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartBlock.Axes.DepthTop.Title.Font.Shadow.Visible = false;
            this.tChartBlock.Axes.DepthTop.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartBlock.Axes.DepthTop.Title.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartBlock.Axes.Left.Automatic = true;
            // 
            // 
            // 
            this.tChartBlock.Axes.Left.Grid.Style = System.Drawing.Drawing2D.DashStyle.Dash;
            this.tChartBlock.Axes.Left.Grid.ZPosition = 0;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartBlock.Axes.Left.Labels.Font.Shadow.Visible = false;
            this.tChartBlock.Axes.Left.Labels.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartBlock.Axes.Left.Labels.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartBlock.Axes.Left.Title.Font.Shadow.Visible = false;
            this.tChartBlock.Axes.Left.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartBlock.Axes.Left.Title.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartBlock.Axes.Right.Automatic = true;
            // 
            // 
            // 
            this.tChartBlock.Axes.Right.Grid.Style = System.Drawing.Drawing2D.DashStyle.Dash;
            this.tChartBlock.Axes.Right.Grid.ZPosition = 0;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartBlock.Axes.Right.Labels.Font.Shadow.Visible = false;
            this.tChartBlock.Axes.Right.Labels.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartBlock.Axes.Right.Labels.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartBlock.Axes.Right.Title.Font.Shadow.Visible = false;
            this.tChartBlock.Axes.Right.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartBlock.Axes.Right.Title.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartBlock.Axes.Top.Automatic = true;
            // 
            // 
            // 
            this.tChartBlock.Axes.Top.Grid.Style = System.Drawing.Drawing2D.DashStyle.Dash;
            this.tChartBlock.Axes.Top.Grid.ZPosition = 0;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartBlock.Axes.Top.Labels.Font.Shadow.Visible = false;
            this.tChartBlock.Axes.Top.Labels.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartBlock.Axes.Top.Labels.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartBlock.Axes.Top.Title.Font.Shadow.Visible = false;
            this.tChartBlock.Axes.Top.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartBlock.Axes.Top.Title.Shadow.Visible = false;
            this.tChartBlock.BackColor = System.Drawing.Color.Transparent;
            this.tChartBlock.Dock = System.Windows.Forms.DockStyle.Fill;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartBlock.Footer.Font.Shadow.Visible = false;
            this.tChartBlock.Footer.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartBlock.Footer.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartBlock.Header.Font.Shadow.Visible = false;
            this.tChartBlock.Header.Font.Unit = System.Drawing.GraphicsUnit.World;
            this.tChartBlock.Header.Lines = new string[] {
        "TeeChart"};
            // 
            // 
            // 
            this.tChartBlock.Header.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartBlock.Legend.Font.Shadow.Visible = false;
            this.tChartBlock.Legend.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartBlock.Legend.Title.Font.Bold = true;
            // 
            // 
            // 
            this.tChartBlock.Legend.Title.Font.Shadow.Visible = false;
            this.tChartBlock.Legend.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartBlock.Legend.Title.Pen.Visible = false;
            // 
            // 
            // 
            this.tChartBlock.Legend.Title.Shadow.Visible = false;
            this.tChartBlock.Location = new System.Drawing.Point(0, 0);
            this.tChartBlock.Name = "tChartBlock";
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartBlock.Panel.Brush.Color = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            // 
            // 
            // 
            this.tChartBlock.Panel.Shadow.Visible = false;
            this.tChartBlock.Size = new System.Drawing.Size(552, 298);
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartBlock.SubFooter.Font.Shadow.Visible = false;
            this.tChartBlock.SubFooter.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartBlock.SubFooter.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartBlock.SubHeader.Font.Shadow.Visible = false;
            this.tChartBlock.SubHeader.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartBlock.SubHeader.Shadow.Visible = false;
            this.tChartBlock.TabIndex = 2;
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartBlock.Walls.Back.AutoHide = false;
            // 
            // 
            // 
            this.tChartBlock.Walls.Back.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartBlock.Walls.Bottom.AutoHide = false;
            // 
            // 
            // 
            this.tChartBlock.Walls.Bottom.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartBlock.Walls.Left.AutoHide = false;
            // 
            // 
            // 
            this.tChartBlock.Walls.Left.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartBlock.Walls.Right.AutoHide = false;
            // 
            // 
            // 
            this.tChartBlock.Walls.Right.Shadow.Visible = false;
            // 
            // panel2
            // 
            this.panel2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.panel2.Controls.Add(this.btnPopShow);
            this.panel2.Controls.Add(this.cbxTypeSel);
            this.panel2.Controls.Add(this.cbxShowType);
            this.panel2.Controls.Add(this.label4);
            this.panel2.Controls.Add(this.label2);
            this.panel2.Controls.Add(this.cbxContentType);
            this.panel2.Controls.Add(this.label3);
            this.panel2.Location = new System.Drawing.Point(13, 36);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(1085, 27);
            this.panel2.TabIndex = 8;
            // 
            // btnPopShow
            // 
            this.btnPopShow.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnPopShow.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btnPopShow.Location = new System.Drawing.Point(1041, 4);
            this.btnPopShow.Name = "btnPopShow";
            this.btnPopShow.Size = new System.Drawing.Size(41, 20);
            this.btnPopShow.TabIndex = 6;
            this.btnPopShow.Text = "弹出";
            this.btnPopShow.UseVisualStyleBackColor = true;
            this.btnPopShow.Click += new System.EventHandler(this.btnPopShow_Click);
            // 
            // cbxTypeSel
            // 
            this.cbxTypeSel.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxTypeSel.FormattingEnabled = true;
            this.cbxTypeSel.Location = new System.Drawing.Point(64, 3);
            this.cbxTypeSel.Name = "cbxTypeSel";
            this.cbxTypeSel.Size = new System.Drawing.Size(113, 20);
            this.cbxTypeSel.TabIndex = 4;
            this.cbxTypeSel.SelectedIndexChanged += new System.EventHandler(this.cbxTypeSel_SelectedIndexChanged);
            // 
            // cbxShowType
            // 
            this.cbxShowType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxShowType.FormattingEnabled = true;
            this.cbxShowType.Location = new System.Drawing.Point(223, 3);
            this.cbxShowType.Name = "cbxShowType";
            this.cbxShowType.Size = new System.Drawing.Size(110, 20);
            this.cbxShowType.TabIndex = 4;
            this.cbxShowType.SelectedIndexChanged += new System.EventHandler(this.cbxShowType_SelectedIndexChanged);
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(3, 7);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(65, 12);
            this.label4.TabIndex = 5;
            this.label4.Text = "黑点类型：";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(184, 7);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(41, 12);
            this.label2.TabIndex = 5;
            this.label2.Text = "方式：";
            // 
            // cbxContentType
            // 
            this.cbxContentType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxContentType.FormattingEnabled = true;
            this.cbxContentType.Location = new System.Drawing.Point(382, 3);
            this.cbxContentType.Name = "cbxContentType";
            this.cbxContentType.Size = new System.Drawing.Size(128, 20);
            this.cbxContentType.TabIndex = 4;
            this.cbxContentType.SelectedIndexChanged += new System.EventHandler(this.cbxContentType_SelectedIndexChanged);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(339, 8);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(41, 12);
            this.label3.TabIndex = 5;
            this.label3.Text = "内容：";
            // 
            // btnGoBlackBlockQuery
            // 
            this.btnGoBlackBlockQuery.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnGoBlackBlockQuery.AutoSize = true;
            this.btnGoBlackBlockQuery.Location = new System.Drawing.Point(979, 371);
            this.btnGoBlackBlockQuery.Name = "btnGoBlackBlockQuery";
            this.btnGoBlackBlockQuery.Size = new System.Drawing.Size(89, 12);
            this.btnGoBlackBlockQuery.TabIndex = 11;
            this.btnGoBlackBlockQuery.TabStop = true;
            this.btnGoBlackBlockQuery.Text = "问题黑点查询>>";
            this.btnGoBlackBlockQuery.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.btnGoBlackBlockQuery_LinkClicked);
            // 
            // ColumnTime
            // 
            this.ColumnTime.HeaderText = "时间";
            this.ColumnTime.Name = "ColumnTime";
            this.ColumnTime.ReadOnly = true;
            this.ColumnTime.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
            // 
            // ColumnArea
            // 
            this.ColumnArea.HeaderText = "地区";
            this.ColumnArea.Name = "ColumnArea";
            this.ColumnArea.ReadOnly = true;
            this.ColumnArea.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
            // 
            // ColumnCreated
            // 
            this.ColumnCreated.HeaderText = "创建数量";
            this.ColumnCreated.Name = "ColumnCreated";
            this.ColumnCreated.ReadOnly = true;
            this.ColumnCreated.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
            // 
            // ColumnClosed
            // 
            this.ColumnClosed.HeaderText = "关闭数量";
            this.ColumnClosed.Name = "ColumnClosed";
            this.ColumnClosed.ReadOnly = true;
            this.ColumnClosed.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
            // 
            // ColumnRemainCount
            // 
            this.ColumnRemainCount.HeaderText = "剩余数量";
            this.ColumnRemainCount.Name = "ColumnRemainCount";
            this.ColumnRemainCount.ReadOnly = true;
            this.ColumnRemainCount.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
            // 
            // ColumnCloseRate
            // 
            this.ColumnCloseRate.HeaderText = "关闭率（%）";
            this.ColumnCloseRate.Name = "ColumnCloseRate";
            this.ColumnCloseRate.ReadOnly = true;
            // 
            // BlackBlockInfoPanel
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.Transparent;
            this.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.Controls.Add(this.btnGoBlackBlockQuery);
            this.Controls.Add(this.splitMain);
            this.Controls.Add(this.panel2);
            this.Controls.Add(this.panel1);
            this.Name = "BlackBlockInfoPanel";
            this.Size = new System.Drawing.Size(1101, 392);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.splitMain.Panel1.ResumeLayout(false);
            this.splitMain.Panel2.ResumeLayout(false);
            this.splitMain.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).EndInit();
            this.panel2.ResumeLayout(false);
            this.panel2.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.SplitContainer splitMain;
        private System.Windows.Forms.DataGridView dataGridView;
        private Steema.TeeChart.TChart tChartBlock;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Button btnPopShow;
        private System.Windows.Forms.ComboBox cbxTypeSel;
        private System.Windows.Forms.ComboBox cbxShowType;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.ComboBox cbxContentType;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.LinkLabel btnGoBlackBlockQuery;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnTime;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnArea;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnCreated;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnClosed;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnRemainCount;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnCloseRate;
    }
}
