﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public class CreateTableViewerForm : CreateChildForm
    {
        public CreateTableViewerForm(MainModel mm)
            : base(mm)
        { 
        }
        public override string Description
        {
            get
            {
                return "创建采样点数据列表窗口 TableViewerForm ";
            }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 20000, 20021, this.Name);
        }
        public override string Name
        {
            get
            {
                return "创建采样点数据列表窗口";
            }
        }

        protected override void initAction()
        {
            Dictionary<string, object> actionParam = new Dictionary<string, object>();
            actionParam["MainForm"] = MainModel.MainForm;
            actionParam["AssemblyName"] = "RAMS.exe";
            actionParam["TypeName"] = "MasterCom.RAMS.TableViewer.TableViewerForm";
            actionParam["Text"] = "数据列表";
            actionParam["ImageFilePath"] = @"images\dataview.gif";
            action = new ActionCreateChildFrame();
            action.Param = actionParam;
        }
    }
}
