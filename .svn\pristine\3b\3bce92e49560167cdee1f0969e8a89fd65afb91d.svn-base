﻿using System;
using System.Collections.Generic;
using System.Text;
using System.IO;
using System.Windows.Forms;
using System.Data;
using DevExpress.XtraEditors;

namespace MasterCom.Util
{
    /// <summary>
    /// 本类主要用来读写CSV文件。
    /// 格式说明：
    ///     @用文本模式存储，可以用文本编辑器打开csv文件。
    ///     @用英文的逗号【,】来分隔每个单元格的内容。
    ///     @用excel软件打开所看见的一行内容同样存储为一行内容。
    ///     @存储格式：（以下格式涉及到的逗号和双引号都是英文的，即半角符号）
	///            @没有逗号
	    ///	            @没有双引号
	    ///		                @规则A：单元格的内容通过逗号分隔。			
	    ///	            @有双引号
	        ///		            @开头没有双引号
	        ///			                @适用规则A
	        ///		            @开头有双引号
	            ///			            @规则B： 单元格内的每个双引号前都要加一个双引号作为转义字符，
	            ///			                            即两个双引号表示一个双引号，
	            ///			                            单元格内容再通过一对双引号包含起来，再通过逗号和其他单元格内容分隔。		
	///
	///            @有逗号
	    ///	            @没有双引号
	    ///		                @适用规则B（忽略对双引号的处理）。
        ///	            @有双引号
            ///		            @开头没有双引号
            ///			                @适用规则B。
            ///		            @开头有双引号
            ///			                @适用规则B。
            ///			                
    ///     例子：
    ///         用excel打开文件可见的2个单元格内容：nihao  wohao       则存储为：nihao,wohao
    ///         用excel打开文件可见的2个单元格内容：ni,hao wohao       则存储为:   "ni,hao",wohao
    ///         用excel打开文件可见的2个单元格内容：ni,h"ao wohao       则存储为:   "ni,h""ao",wohao
    ///         用excel打开文件可见的2个单元格内容："ni,h"ao wohao       则存储为:   """ni,h""ao",wohao
    /// </summary>
    class CSVReader
    {
        private readonly string fileName = null;
        public string lastError { get; set; }
        public CSVReader(string fileName)
        {
            this.fileName = fileName;
            this.lineCounter = 1;
        }

        /// <summary>
        /// 获取csv文件中的所有数据，不区分标题和数据
        /// </summary>
        /// <returns></returns>
        public List<string[]> GetAllData()
        {
            try
            {
                List<string[]> list = new List<string[]>();
                StreamReader reader = new StreamReader(this.fileName, Encoding.Default);
                string line = null;
                this.lineCounter = 0;
                while ((line = reader.ReadLine()) != null)
                {
                    this.lineCounter++;
                    string[] cells = this.getLine(line);
                    if (cells == null) return new List<string[]>();
                    list.Add(cells);
                }
                reader.Close();
                return list;
            }
            catch (Exception ex)
            {
                this.lastError = ex.Message;
                return new List<string[]>();
            }
        }

        private string[] getLine(string line)
        {
            List<string> list = new List<string>();
            int index = 0;
            this.cellCounter = 0;
            while(index < line.Length)
            {
                this.cellCounter++;
                string cell = this.getCell(line, ref index);
                if (cell == null)
                {
                    return new string[0];
                }
                list.Add(cell);
            }
            if (line.EndsWith(","))
            {
                list.Add("");
            }
            return list.ToArray();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="line"></param>
        /// <param name="index">进入函数的时候指向单元格的开始，返回的时候指向下一个单元格的开始，若到达line的最后，则等于line的长度。</param>
        /// <returns>成功则返回单元格内容，出错则null</returns>
        private string getCell(string line, ref int index)
        {
            if (index >= line.Length)
            {
                this.lastError = string.Format("out of bound, line: {0}  cell: {1}", this.lineCounter, this.cellCounter);
                return null;
            }
            StringBuilder sb = new StringBuilder();
            if (line[index] == ',')
            {
                //empty cell
                index++;
                return sb.ToString();
            }
            if (line[index] != '"')
            {
                char tem;
                for (int i = index; i < line.Length; i++)
                {
                    tem = line[i];
                    if (tem == ',')
                    {
                        index = i + 1;
                        return sb.ToString();
                    }
                    sb.Append(tem);
                }
                index = line.Length;
                return sb.ToString();
            }
            else
            {
                return getEscapeCharacterInfo(line, ref index, sb);
            }
        }

        private string getEscapeCharacterInfo(string line, ref int index, StringBuilder sb)
        {
            index++;
            char tem;
            for (int i = index; i < line.Length; i++)
            {
                tem = line[i];
                if (tem != '"')
                {
                    sb.Append(tem);
                    continue;
                }
                if (++i >= line.Length)
                {
                    index = line.Length;
                    return sb.ToString();
                }
                //遇到转义字符，需要分析下一个字符来确定实际字符。
                tem = line[i];
                if (tem == '"')
                {
                    sb.Append('"');
                }
                else if (tem == ',')
                {
                    index = i + 1;
                    return sb.ToString();
                }
                else
                {
                    /**
                     * 双引号后面可以跟一个双引号表示一个双引号，
                     * 也可以跟一个用于分隔单元格内容的逗号表示一个单元格内容的结束，
                     * 其他情况则视为异常。
                     * 若发现其他规则，欢迎增加此序列。
                     */
                    this.lastError = string.Format("unrecognized format, line: {0}  cell: {1}", this.lineCounter, this.cellCounter);
                    return null;
                }
            }
            index = line.Length;
            return sb.ToString();
        }

        private int lineCounter;
        private int cellCounter = 0;
    }

    class CSVWriter
    {
        private readonly string fileName = null;
        public string  lastError = null;

        public CSVWriter(string fileName)
        {
            this.fileName = fileName;    
        }

        public bool WriteAllData(List<string[]> listContent)
        {
            try
            {
                StreamWriter writer = new StreamWriter(this.fileName);
                foreach(string[] line in listContent)
                {
                    writer.WriteLine(this.makeLine(line));
                }
                writer.Flush();
                writer.Close();
                return true;
            }
            catch (Exception ex)
            {
                this.lastError = ex.Message;
                return false;
            }
        }

        private string makeLine(string[] cells)
        {
            StringBuilder sb = new StringBuilder();
            foreach(string cellValue in cells)
            {
                sb.Append(this.makeCellValue(cellValue) + ',');
            }
            return sb.ToString(0, sb.Length-1);
        }
        private string makeCellValue(string cellValue)
        {
            bool comma = cellValue.IndexOf(',') >= 0;
            int doubleQuotes = cellValue.IndexOf('"') ;

            if (!comma && doubleQuotes != 0)
            {
                //内容不变
                return cellValue;
            }
            else if (doubleQuotes <0)
            {
                //只需要头尾加双引号
                return string.Format("\"{0}\"", cellValue);
            }
            //头尾需要加双引号，且中间的双引号需要转义
            StringBuilder sb = new StringBuilder();
            sb.Append("\"");
            char tem;
            for(int i=0;i<cellValue.Length;i++)
            {
                tem = cellValue[i];
                if (tem == '"')
                {
                    sb.Append('"');
                }
                sb.Append(tem);
            }
            sb.Append('"');
            return sb.ToString();
        }

        public static void ExportToCsv(List<NPOIRow> rowList)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = false;
            dlg.Filter = FilterHelper.Csv;
            dlg.FilterIndex = 1;//默认csv
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            ExportToCsv(rowList, dlg.FileName); 
        }
        public static void ExportToCsv(List<NPOIRow> rowList, string fileName)
        {
            try
            {
                if (File.Exists(fileName))
                {
                    File.Delete(fileName);
                }

                FileStream fileStream = new FileStream(fileName, FileMode.CreateNew, FileAccess.Write, FileShare.Read);
                using (StreamWriter streamWriter = new StreamWriter(fileStream, Encoding.Default))
                {
                    foreach (NPOIRow nrow in rowList)
                    {
                        streamWriter.Write(npoiRowConvertToCsvRow(nrow));
                    }

                    streamWriter.Flush();
                    streamWriter.Close();
                }
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
            finally
            {
                //GC.Collect();
            }
        }
        private static string npoiRowConvertToCsvRow(NPOIRow npoiRow, string lastLevelString = "")
        {
            StringBuilder sb = new StringBuilder();
            sb.Append(lastLevelString);
            int iCellNum = npoiRow.cellValues.Count;
            for (int j = 0; j < iCellNum; j++)
            {
                object cellValue = npoiRow.cellValues[j];
                if (cellValue == null)
                    cellValue = "";
                if (cellValue.ToString().IndexOf(",") >= 0)
                {
                    cellValue = cellValue.ToString().Replace(",", "，");
                }
                sb.Append(cellValue + ",");
            }

            if (npoiRow.subRows.Count > 0)
            {
                string strValue = sb.ToString();
                sb = new StringBuilder();

                foreach (NPOIRow nrow in npoiRow.subRows)
                {
                    sb.Append(npoiRowConvertToCsvRow(nrow, strValue));
                }
            }
            else
            {
                sb.AppendLine();
            }
            return sb.ToString();
        }

        public static void ExportToCsv(List<List<NPOIRow>> resultList,List<string> filePaths)
        {
            try
            {
                for (int i = 0; i < filePaths.Count; i++)
                {
                    string filePath = filePaths[i];
                    List<NPOIRow> rows = resultList[i];

                    ExportToCsv(rows, filePath);
                }
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
            finally
            {
                //GC.Collect();
            }
        }

        #region 批量导出到csv
        public static void ExportToCsvBatch(object obj,string exportFileName)
        {
            readyToCsvBatch(obj,exportFileName);
        }
        private static void readyToCsvBatch(object obj,string exportFileName)
        {
            toExportCsvBatch(new object[] { obj }, exportFileName);
        }
        /// <summary>
        /// 批量导出csv通用界面函数
        /// </summary>
        /// <param name="objs">导出时传的参数</param>
        /// <param name="bRemindOpen">保存完成后是否打开</param>
        private static void toExportCsvBatch(object[] objs,string exportFileName)
        {
            errMessage = "";
            string extend = Path.GetExtension(exportFileName);
            csvFileName = exportFileName.Replace(extend, ".csv");
            object obj = objs;
            WaitBox.Show("正在导出到csv...", doExportCsv, obj);
            if (error && errMessage != "")
            {
                XtraMessageBox.Show(errMessage, "错误", MessageBoxButtons.YesNo);
            }
        }
        #endregion

        /// <summary>
        /// 将内容保存为csv文件
        /// </summary>
        /// <param name="dataTable"></param>
        public static void ExportToCsv(object obj)
        {
            readyToCsv(obj, true);
        }
        private static void readyToCsv(object obj, bool bRemindOpen)
        {
            toExportCsv(new object[] { obj }, bRemindOpen);
        }


        static bool error = false;
        static string csvFileName;
        static string errMessage = "";
        /// <summary>
        /// 导出csv通用界面函数
        /// </summary>
        /// <param name="objs">导出时传的参数</param>
        /// <param name="bRemindOpen">保存完成后是否打开</param>
        private static void toExportCsv(object[] objs, bool bRemindOpen)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = false;
            dlg.Filter = FilterHelper.Csv;
            dlg.FilterIndex = 1;//默认csv
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                errMessage = "";
                csvFileName = dlg.FileName;
                object obj = objs;
                WaitBox.Show("正在导出到csv...", doExportCsv, obj);
                if (!error)
                {
                    afterExoprt(bRemindOpen);
                }
                else
                {
                    if (errMessage != "")
                    {
                        XtraMessageBox.Show(errMessage, "错误", MessageBoxButtons.YesNo);
                    }
                }
            }
        }

        private static void afterExoprt(bool bRemindOpen)
        {
            if (bRemindOpen)
            {
                if (DialogResult.Yes == XtraMessageBox.Show("csv文件保存完毕，是否现在打开?", "打开文件", MessageBoxButtons.YesNo))  //添加了自动打开文件的提示
                {
                    try
                    {
                        System.Diagnostics.Process.Start(csvFileName);
                    }
                    catch
                    {
                        XtraMessageBox.Show("打开失败!\r\n文件名:" + csvFileName);
                    }
                }
            }
            else
            {
                XtraMessageBox.Show("csv导出完成！");
            }
        }

        /// <summary>
        /// csv文件通用导出函数
        /// </summary>
        /// <param name="objValue">可以传递多种格式的数据类型（目前只支持datatable，其它类型方法需要另行添加）</param>
        private static void doExportCsv(object objValue)
        {

            try
            {
                object[] objs = (object[])objValue;
                if (objs.Length == 1)
                {
                    object obj = objs[0];
                    if (obj is DataTable)
                    {
                        DataTable dataTable = obj as DataTable;
                        exportToCsv(dataTable);
                    }
                    else if (obj is DevExpress.XtraTreeList.TreeList)
                    {
                        DevExpress.XtraTreeList.TreeList treelistView = obj as DevExpress.XtraTreeList.TreeList;
                        exportToCsv(treelistView);
                    }
                }
            }
            catch (Exception e)
            {
                error = true;
                errMessage = "csv导出出错：" + e.ToString();
            }
            finally
            {
                System.Threading.Thread.Sleep(300);
                WaitBox.Close();
                //GC.Collect();
            }
        }

        #region 导出datatable类型数据到csv的具体方法
        /// <summary>
        /// 导出datatable类型数据到csv的具体方法
        /// </summary>
        /// <param name="dataTable"></param>
        private static void exportToCsv(DataTable dataTable)
        {
            if (File.Exists(csvFileName))
            {
                try
                {
                    File.Delete(csvFileName);
                }
                catch (Exception)
                {
                    errMessage = "csv文件已经被占用，无法覆盖。";
                    error = true;
                    return;
                }
                
            }

            //先打印标头
            StringBuilder strColu = new StringBuilder();
            StringBuilder strValue = new StringBuilder();
            int i = 0;
            try
            {
                WaitBox.ProgressPercent = 2;
                StreamWriter sw = new StreamWriter(new FileStream(csvFileName, FileMode.CreateNew), Encoding.GetEncoding("GB2312"));
                for (i = 0; i <= dataTable.Columns.Count - 1; i++)
                {
                    strColu.Append(dataTable.Columns[i].Caption);
                    strColu.Append(",");
                }
                strColu.Remove(strColu.Length - 1, 1);//移出掉最后一个,字符
                sw.WriteLine(strColu);
                int dataTableRowCount = dataTable.Rows.Count;
                for (int j = 0; j < dataTableRowCount; j++)
                {
                    DataRow dr = dataTable.Rows[j];
                    strValue.Remove(0, strValue.Length);//移出
                    for (i = 0; i <= dataTable.Columns.Count - 1; i++)
                    {
                        string tempStr = "";
                        try
                        {
                            tempStr = dr[i].ToString().Replace(",", ";");
                        }
                        catch
                        {
                            //continue
                        }
                        tempStr = tempStr.Replace("\r", "");
                        tempStr = tempStr.Replace("\n", "");
                        strValue.Append(tempStr);
                        strValue.Append(",");
                    }
                    strValue.Remove(strValue.Length - 1, 1);//移出掉最后一个,字符
                    sw.WriteLine(strValue);
                    WaitBox.ProgressPercent = (int)(100.0 * (j + 1) / dataTableRowCount);
                }
                sw.Close();
                error = false;
            }
            catch (Exception e)
            {
                errMessage = "csv导出出错：" + e.ToString();
                error = true;
            }
        }
        #endregion

        #region 导出TreeList类型数据到csv的具体方法
        /// <summary>
        /// 导出TreeList类型数据到csv的具体方法
        /// </summary>
        /// <param name="treelist"></param>
        private static void exportToCsv(DevExpress.XtraTreeList.TreeList treelist)
        {
            if (File.Exists(csvFileName))
            {
                try
                {
                    File.Delete(csvFileName);
                }
                catch (Exception)
                {
                    errMessage = "csv文件已经被占用，无法覆盖。";
                    error = true;
                    return;
                }

            }

            //先打印标头
            StringBuilder strColu = new StringBuilder();
            StringBuilder strValue = new StringBuilder();
            int i = 0;
            try
            {
                WaitBox.ProgressPercent = 2;
                StreamWriter sw = new StreamWriter(new FileStream(csvFileName, FileMode.CreateNew), Encoding.GetEncoding("GB2312"));
                int columnCount = treelist.Columns.Count;
                for (i = 0; i <= columnCount - 1; i++)
                {
                    strColu.Append(treelist.Columns[i].Caption);
                    strColu.Append(",");
                }
                strColu.Remove(strColu.Length - 1, 1);//移出掉最后一个,字符
                sw.WriteLine(strColu);
                foreach (DevExpress.XtraTreeList.Nodes.TreeListNode item in treelist.Nodes)
                {
                    strValue.Remove(0, strValue.Length);//移出
                    for (i = 0; i < columnCount; i++)
                    {
                        string tempStr = "";
                        try
                        {
                            tempStr = item[i].ToString().Replace(",", ";");
                        }
                        catch
                        {
                            //continue
                        }
                        tempStr = tempStr.Replace("\r", "");
                        tempStr = tempStr.Replace("\n", "");
                        strValue.Append(tempStr);
                        strValue.Append(",");
                    }
                    strValue.Remove(strValue.Length - 1, 1);//移出掉最后一个,字符
                    sw.WriteLine(strValue);
                    AddDataToCSV(sw, strValue, item, columnCount);
                }
                sw.Close();
                error = false;
            }
            catch (Exception e)
            {
                errMessage = "csv导出出错：" + e.ToString();
                error = true;
            }
        }
        /// <summary>
        /// 循环遍历数据的节点数据
        /// </summary>
        private static void AddDataToCSV(StreamWriter sw,StringBuilder strValue, DevExpress.XtraTreeList.Nodes.TreeListNode myNode,int columnCount)
        {

            int current = 1;
            int allCount = myNode.TreeList.AllNodesCount;
            foreach (DevExpress.XtraTreeList.Nodes.TreeListNode item in myNode.Nodes)
            {
                strValue.Remove(0, strValue.Length);//移出
                for (int i = 0; i < columnCount; i++)
                {
                    string tempStr = "";
                    try
                    {
                        tempStr = item[i].ToString().Replace(",", ";");
                    }
                    catch
                    {
                        //continue
                    }
                    tempStr = tempStr.Replace("\r", "");
                    tempStr = tempStr.Replace("\n", "");
                    strValue.Append(tempStr);
                    strValue.Append(",");
                    
                }
                strValue.Remove(strValue.Length - 1, 1);//移出掉最后一个,字符
                sw.WriteLine(strValue);
                AddDataToCSV(sw, strValue, item, columnCount);
                WaitBox.ProgressPercent = (int)(100.0 * current / allCount);
                current++;
            }
        }
        #endregion

    }
}
