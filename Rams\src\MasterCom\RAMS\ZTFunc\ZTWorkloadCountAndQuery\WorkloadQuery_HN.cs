﻿using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.Util.UiEx;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.Linq;

namespace MasterCom.RAMS.ZTFunc
{
    class WorkloadQuery_HN : DIYSQLBase
    {
        DateTime beginTime;
        DateTime endTime;
        WorkloadType_HN type;
        readonly List<WorkloadCountAndQueryInfo> resultList = new List<WorkloadCountAndQueryInfo>();
        public WorkloadQuery_HN()
            : base(MainModel.GetInstance())
        {
            MainDB = true;
        }
        public override string Name
        {
            get { return "工作量统计查询"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18047, this.Name);
        }
        public override bool IsNeedSetQueryCondition
        {
            get { return true; }
        }
        WorkloadCountAndQueryDlg_HN dlg;
        protected override bool isValidCondition()
        {
            if (dlg == null)
            {
                dlg = new WorkloadCountAndQueryDlg_HN();
            } 
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            beginTime = condition.Periods[0].BeginTime;
            endTime = condition.Periods[0].EndTime;
            type = dlg.GetType_HN();
            return true;
        }
        private void clearData()
        {
            resultList.Clear();
        }
        protected override void query()
        {
            clearData();
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitTextBox.Show("正在查询工作量数据", queryInThread, clientProxy);
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
            finally
            {
                clientProxy.Close();
            }
            showResultForm();
        }
        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                string strsql = getSqlTextString();
                E_VType[] retArrDef = getSqlRetTypeArr();//获得枚举类型数组
                package.Command = Command.DIYSearch;//枚举类型：DIY接口
                package.SubCommand = SubCommand.Request;//枚举类型：请求
                if (MainDB)
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
                }
                else
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
                }
                package.Content.PrepareAddParam();
                package.Content.AddParam(strsql);//添加查询语句
                StringBuilder sb = new StringBuilder();
                if (retArrDef != null)
                {
                    for (int i = 0; i < retArrDef.Length; i++)
                    {
                        sb.Append((int)retArrDef[i]);
                        sb.Append(",");
                    }
                }
                package.Content.AddParam(sb.ToString().TrimEnd(','));
                clientProxy.Send();
                receiveRetData(clientProxy);
            }
            catch (Exception e)
            {
                MessageBox.Show(e.ToString());
            }
            finally
            {
                System.Threading.Thread.Sleep(200);
                WaitTextBox.Close();
            }
        }
        protected override string getSqlTextString()
        {
            string sql = "";
            if (type == WorkloadType_HN.测试厂家)//不同类型的表结构和查询数据不一样
            {
                sql = " select[SceneType],[RequirementTheme],[OrderSN],[OrderTime],[OverTime],[RequirementPerson],[OrderStatus],[TestTimes],[RequirementFeedback] "
                    + " from tb_henan_Workload_Test where [OrderTime] between'" + beginTime.ToString() + "' and '" + endTime.ToString() + "'";
            }
            else
            {
                sql = " select[SceneType],[RequirementTheme],[OrderSN],[OrderTime],[OverTime],[RequirementPerson],[OrderStatus],[RequirementFeedback] "
                    + " from tb_henan_Workload_Analysis where [OrderTime] between'" + beginTime.ToString() + "' and '" + endTime.ToString() + "'";
            }
            return sql ;
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType;
            if (type == WorkloadType_HN.测试厂家)//不同类型的数据不一样，分析厂家的会缺少测试组数
            {
                rType = new E_VType[9];
                rType[0] = E_VType.E_String;
                rType[1] = E_VType.E_String;
                rType[2] = E_VType.E_Int;
                rType[3] = E_VType.E_String;
                rType[4] = E_VType.E_String;
                rType[5] = E_VType.E_String;
                rType[6] = E_VType.E_String;
                rType[7] = E_VType.E_String;
                rType[8] = E_VType.E_String;
            }
            else
            {
                rType = new E_VType[8];
                rType[0] = E_VType.E_String;
                rType[1] = E_VType.E_String;
                rType[2] = E_VType.E_Int;
                rType[3] = E_VType.E_String;
                rType[4] = E_VType.E_String;
                rType[5] = E_VType.E_String;
                rType[6] = E_VType.E_String;
                rType[7] = E_VType.E_String;
            }
            return rType;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    WorkloadCountAndQueryInfo info = new WorkloadCountAndQueryInfo();
                    info.SceneType = package.Content.GetParamString();
                    info.RequirementTheme= package.Content.GetParamString();
                    info.OrderSN = package.Content.GetParamInt();
                    info.OrderTime = Convert.ToDateTime(package.Content.GetParamString());
                    info.OverTime = Convert.ToDateTime(package.Content.GetParamString());
                    info.RequirementPerson = package.Content.GetParamString();
                    info.OrderStatus = package.Content.GetParamString();
                    if (type == WorkloadType_HN.测试厂家)//如果是测试类型，会有测试组数的项
                    {
                        info.TestTimes = package.Content.GetParamString();
                    }
                    info.RequirementFeedback = package.Content.GetParamString();
                    info.SN = resultList.Count + 1;
                    resultList.Add(info);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
        protected void showResultForm()
        {
            if (resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            WorkloadCountAndQueryInfoForm_HN frm = MainModel.CreateResultForm(typeof(WorkloadCountAndQueryInfoForm_HN)) as WorkloadCountAndQueryInfoForm_HN;
            frm.SetType_HN(type);
            frm.FillData(resultList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
    }
    public enum WorkloadType_HN
    {
        测试厂家=1,
        分析厂家 = 2,
    }
    public class WorkloadCountAndQueryInfo
    {
        public int SN { get; set; }
        public string SceneType { get; set; }
        public string RequirementTheme { get; set; }
        public int OrderSN { get; set; }
        public DateTime OrderTime { get; set; }
        public DateTime OverTime { get; set; }
        public string RequirementPerson { get; set; }
        public string OrderStatus { get; set; }
        public string TestTimes { get; set; }
        public string RequirementFeedback { get; set; }
    }
    public class LoadFileForWorkload : DiySqlMultiNonQuery
    {
        readonly List<WorkloadCountAndQueryInfo> infoList = new List<WorkloadCountAndQueryInfo>();
        readonly string filePath;
        readonly WorkloadType_HN type ;
        public LoadFileForWorkload(string path, WorkloadType_HN type)
        {
            MainDB = true;
            this.filePath = path;
            this.type = type;
        }
        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitTextBox.Show("正在向数据库导入数据......", queryInThread, clientProxy);
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
            finally
            {
                clientProxy.Close();
            }
        }
        protected override void queryInThread(object o)
        {
            base.queryInThread(o);
            System.Threading.Thread.Sleep(200);
            WaitTextBox.Close();
        }
        protected override string getSqlTextString()
        {
            getFileData();
            StringBuilder strb = new StringBuilder();
            if (type == WorkloadType_HN.测试厂家)
            {
                foreach (WorkloadCountAndQueryInfo info in infoList)
                {
                    strb.Append(string.Format(@"insert into tb_henan_Workload_Test(
[SceneType],[RequirementTheme],[OrderSN],[OrderTime],[OverTime],[RequirementPerson],[OrderStatus],[RequirementFeedback],[TestTimes]) 
values ('{0}', '{1}',{2} ,'{3}','{4}','{5}','{6}','{7}','{8}');"
                        , info.SceneType, info.RequirementTheme, info.OrderSN, info.OrderTime
                        , info.OverTime, info.RequirementPerson, info.OrderStatus, info.RequirementFeedback
                        ,info.TestTimes));
                }
                strb.Append("delete from tb_henan_Workload_Test where iid not in(select MIN(iid) from tb_henan_Workload_Test group by OrderSN)  ");
            }
            else
            {
                foreach (WorkloadCountAndQueryInfo info in infoList)
                {
                    strb.Append(string.Format(@"insert into tb_henan_Workload_Analysis([SceneType],[RequirementTheme],[OrderSN]
,[OrderTime],[OverTime],[RequirementPerson],[OrderStatus],[RequirementFeedback]) values ('{0}', '{1}',{2},'{3}','{4}', '{5}','{6}','{7}');"
                        , info.SceneType, info.RequirementTheme, info.OrderSN, info.OrderTime
                        , info.OverTime, info.RequirementPerson, info.OrderStatus, info.RequirementFeedback));
                }
                strb.Append("delete from tb_henan_Workload_Analysis  where iid not in(select MIN(iid) from tb_henan_Workload_Analysis  group by OrderSN)");
            }
            return strb.ToString();
        }
        private void getFileData()
        {
            ExcelNPOIReader reader = new ExcelNPOIReader(filePath);
            List<string> sheetsName = reader.GetSheets();
            for (int i = 0; i < sheetsName.Count; i++)
            {
                //多个工作表根据表名和导入类型判断是否需要导入
                if (sheetsName[i].Contains("测试") && type == WorkloadType_HN.测试厂家
                    || sheetsName[i].Contains("分析") && type == WorkloadType_HN.分析厂家)
                {
                    List<string> cloNames = new List<string>();
                    cloNames.Add("场景类型");
                    cloNames.Add("需求主题");
                    cloNames.Add("工单编号");
                    cloNames.Add("派单时间");
                    cloNames.Add("超时时间");
                    cloNames.Add("需求人");
                    cloNames.Add("工单状态");
                    cloNames.Add("需求反馈");
                    if (type == WorkloadType_HN.测试厂家)
                    {
                        cloNames.Add("测试组数");
                    }
                    getTableRow(reader, sheetsName, i, cloNames);
                }
            }
        }

        private void getTableRow(ExcelNPOIReader reader, List<string> sheetsName, int i, List<string> cloNames)
        {
            ExcelNPOITable table = reader.GetTable(sheetsName[i], cloNames);
            for (int j = 0; j < table.CellValues.Count; j++)
            {
                WorkloadCountAndQueryInfo info = new WorkloadCountAndQueryInfo();
                List<string> resultList;
                if (!checkIsValueEmpty(table.CellValues[j], out resultList))
                {
                    info.SceneType = resultList[0];
                    info.RequirementTheme = resultList[1];
                    int orderSN = 0;
                    if (!int.TryParse(resultList[2], out orderSN))
                    {
                        continue;
                    }
                    info.OrderSN = orderSN;
                    string ordertime = resultList[3];
                    info.OrderTime = getDateTime(ordertime);
                    string overtime = resultList[4];
                    info.OverTime = getDateTime(overtime);
                    info.RequirementPerson = resultList[5];
                    info.OrderStatus = resultList[6];
                    info.RequirementFeedback = resultList[7];
                    if (type == WorkloadType_HN.测试厂家)
                    {
                        info.TestTimes = resultList[8];
                    }
                    infoList.Add(info);
                }
            }
        }

        /// <summary>
        /// 处理时间格式,excel中double类型的时间需用FromOADate
        /// </summary>
        /// <param name="time"></param>
        /// <returns></returns>
        private DateTime getDateTime(string time)
        {
            DateTime dt;
            double dtime = 0;
            if (double.TryParse(time, out dtime))
            {
                dt = DateTime.FromOADate(dtime);
            }
            else
            {
                DateTime.TryParse(time, out dt);
            }
            return dt; 
        }

        /// <summary>
        /// 检测数据是否为空
        /// </summary>
        /// <param name="cellValues"></param>
        /// <param name="resultList"></param>
        /// <returns>true 为空</returns>
        private bool checkIsValueEmpty(object[] cellValues, out List<string> resultList)
        {
            resultList = new List<string>();
            foreach (object cellValue in cellValues)
            {
                if (cellValue == null)
                {
                    resultList = null;
                    return true;
                }
                string result = cellValue.ToString();
                if (string.IsNullOrEmpty(result))
                {
                    resultList = null;
                    return true;
                }
                resultList.Add(result);
            }
            return false;
        }
    }
}
