﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class CoverDistanceForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.edtRxLevMax = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.edtRxLevMin = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportToExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnCellName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLAC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBCCH = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnBSIC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnSampleCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnRxLevAvg = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDistanceAvg = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edtRxLevMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtRxLevMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            this.SuspendLayout();
            // 
            // panelControl1
            // 
            this.panelControl1.Controls.Add(this.edtRxLevMax);
            this.panelControl1.Controls.Add(this.labelControl2);
            this.panelControl1.Controls.Add(this.edtRxLevMin);
            this.panelControl1.Controls.Add(this.labelControl1);
            this.panelControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panelControl1.Location = new System.Drawing.Point(0, 0);
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new System.Drawing.Size(701, 34);
            this.panelControl1.TabIndex = 0;
            // 
            // edtRxLevMax
            // 
            this.edtRxLevMax.EditValue = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            this.edtRxLevMax.Location = new System.Drawing.Point(137, 6);
            this.edtRxLevMax.Name = "edtRxLevMax";
            this.edtRxLevMax.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.edtRxLevMax.Properties.Appearance.Options.UseFont = true;
            this.edtRxLevMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.edtRxLevMax.Properties.IsFloatValue = false;
            this.edtRxLevMax.Properties.Mask.EditMask = "N00";
            this.edtRxLevMax.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.edtRxLevMax.Properties.MinValue = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.edtRxLevMax.Size = new System.Drawing.Size(71, 20);
            this.edtRxLevMax.TabIndex = 3;
            this.edtRxLevMax.EditValueChanged += new System.EventHandler(this.edtRxLevThreshold_EditValueChanged);
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(214, 9);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(18, 12);
            this.labelControl2.TabIndex = 2;
            this.labelControl2.Text = "dBm";
            // 
            // edtRxLevMin
            // 
            this.edtRxLevMin.EditValue = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            this.edtRxLevMin.Location = new System.Drawing.Point(12, 6);
            this.edtRxLevMin.Name = "edtRxLevMin";
            this.edtRxLevMin.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.edtRxLevMin.Properties.Appearance.Options.UseFont = true;
            this.edtRxLevMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.edtRxLevMin.Properties.IsFloatValue = false;
            this.edtRxLevMin.Properties.Mask.EditMask = "N00";
            this.edtRxLevMin.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.edtRxLevMin.Properties.MinValue = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.edtRxLevMin.Size = new System.Drawing.Size(71, 20);
            this.edtRxLevMin.TabIndex = 1;
            this.edtRxLevMin.EditValueChanged += new System.EventHandler(this.edtRxLevThreshold_EditValueChanged);
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(89, 9);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(48, 12);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "≤场强≤";
            // 
            // gridControl
            // 
            this.gridControl.ContextMenuStrip = this.contextMenuStrip;
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControl.Location = new System.Drawing.Point(0, 34);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(701, 311);
            this.gridControl.TabIndex = 1;
            this.gridControl.UseEmbeddedNavigator = true;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportToExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(139, 26);
            // 
            // miExportToExcel
            // 
            this.miExportToExcel.Name = "miExportToExcel";
            this.miExportToExcel.Size = new System.Drawing.Size(138, 22);
            this.miExportToExcel.Text = "导出Excel...";
            this.miExportToExcel.Click += new System.EventHandler(this.miExportToExcel_Click);
            // 
            // gridView
            // 
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnCellName,
            this.gridColumnLAC,
            this.gridColumnCI,
            this.gridColumnBCCH,
            this.gridColumnBSIC,
            this.gridColumnSampleCount,
            this.gridColumnRxLevAvg,
            this.gridColumnDistanceAvg});
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsSelection.MultiSelect = true;
            this.gridView.OptionsView.ColumnAutoWidth = false;
            this.gridView.OptionsView.ShowDetailButtons = false;
            this.gridView.OptionsView.ShowFooter = true;
            this.gridView.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumnCellName
            // 
            this.gridColumnCellName.Caption = "小区名";
            this.gridColumnCellName.FieldName = "CellName";
            this.gridColumnCellName.Name = "gridColumnCellName";
            this.gridColumnCellName.Visible = true;
            this.gridColumnCellName.VisibleIndex = 0;
            this.gridColumnCellName.Width = 119;
            // 
            // gridColumnLAC
            // 
            this.gridColumnLAC.Caption = "LAC";
            this.gridColumnLAC.FieldName = "LAC";
            this.gridColumnLAC.Name = "gridColumnLAC";
            this.gridColumnLAC.Visible = true;
            this.gridColumnLAC.VisibleIndex = 1;
            this.gridColumnLAC.Width = 80;
            // 
            // gridColumnCI
            // 
            this.gridColumnCI.Caption = "CI";
            this.gridColumnCI.FieldName = "CI";
            this.gridColumnCI.Name = "gridColumnCI";
            this.gridColumnCI.Visible = true;
            this.gridColumnCI.VisibleIndex = 2;
            this.gridColumnCI.Width = 69;
            // 
            // gridColumnBCCH
            // 
            this.gridColumnBCCH.Caption = "频点";
            this.gridColumnBCCH.FieldName = "BCCH";
            this.gridColumnBCCH.Name = "gridColumnBCCH";
            this.gridColumnBCCH.Visible = true;
            this.gridColumnBCCH.VisibleIndex = 3;
            // 
            // gridColumnBSIC
            // 
            this.gridColumnBSIC.Caption = "扰码";
            this.gridColumnBSIC.FieldName = "BSIC";
            this.gridColumnBSIC.Name = "gridColumnBSIC";
            this.gridColumnBSIC.Visible = true;
            this.gridColumnBSIC.VisibleIndex = 4;
            // 
            // gridColumnSampleCount
            // 
            this.gridColumnSampleCount.Caption = "采样点数";
            this.gridColumnSampleCount.FieldName = "ValidSampleCount";
            this.gridColumnSampleCount.Name = "gridColumnSampleCount";
            this.gridColumnSampleCount.SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum;
            this.gridColumnSampleCount.Visible = true;
            this.gridColumnSampleCount.VisibleIndex = 5;
            // 
            // gridColumnRxLevAvg
            // 
            this.gridColumnRxLevAvg.Caption = "平均电平";
            this.gridColumnRxLevAvg.FieldName = "RxLevAvg";
            this.gridColumnRxLevAvg.Name = "gridColumnRxLevAvg";
            this.gridColumnRxLevAvg.SummaryItem.DisplayFormat = "{0:f}";
            this.gridColumnRxLevAvg.SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Average;
            this.gridColumnRxLevAvg.Visible = true;
            this.gridColumnRxLevAvg.VisibleIndex = 6;
            // 
            // gridColumnDistanceAvg
            // 
            this.gridColumnDistanceAvg.Caption = "平均距离(米)";
            this.gridColumnDistanceAvg.FieldName = "DistanceAvg";
            this.gridColumnDistanceAvg.Name = "gridColumnDistanceAvg";
            this.gridColumnDistanceAvg.SummaryItem.DisplayFormat = "{0:f}";
            this.gridColumnDistanceAvg.SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Average;
            this.gridColumnDistanceAvg.Visible = true;
            this.gridColumnDistanceAvg.VisibleIndex = 7;
            this.gridColumnDistanceAvg.Width = 100;
            // 
            // CoverDistanceForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(701, 345);
            this.Controls.Add(this.gridControl);
            this.Controls.Add(this.panelControl1);
            this.Name = "CoverDistanceForm";
            this.Text = "小区覆盖距离";
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            this.panelControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.edtRxLevMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtRxLevMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SpinEdit edtRxLevMin;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCellName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLAC;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCI;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBCCH;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnBSIC;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnSampleCount;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnRxLevAvg;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDistanceAvg;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcel;
        private DevExpress.XtraEditors.SpinEdit edtRxLevMax;
    }
}