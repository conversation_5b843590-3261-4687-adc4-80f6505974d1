﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using System.Data.SqlClient;
using MasterCom;
using System.Data;
using System.Threading;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class SINRToPCI
    {
        public SINRToPCI()
        {
            beforenbcellname = "";
            afternbcellname = "";
            beforePccpchNB = "";
            afterPccpchNB = "";

            FileName = "";
        }

        public string cityname { get; set; }
        public int cityid { get; set; }
        private string rptime { get; set; }
        public string reporttime { get; set; }
        public string stage { get; set; }
        public string type { get; set; }
        public int sample { get; set; }
        public string cellname { get; set; }
        public string nbcellname { get; set; }
        public double longitude { get; set; }
        public double latitude { get; set; }
        public int pccpchMain { get; set; }
        public int pccpchNB { get; set; }
        public int fileid { get; set; }
        public int sampletime { get; set; }

        public LTECell mainCell { get; set; }
        public LTECell nbCellBefore { get; set; }
        public LTECell nbCellAfter { get; set; }
        private string rt_orig_iScrambleCode;
        public string orig_iScrambleCode 
        {
            get
            {
                if (mainCell != null)
                {
                    return Convert.ToString(mainCell.PCI);
                }
                return rt_orig_iScrambleCode;
            }
        }
        public string iScrambleCode { get; set; }
        
        private DateTime sampleDTtime;

        public int Sn { get; set; }
        public string beforenbcellname { get; set; }
        public string afternbcellname { get; set; }

        public string beforePccpchNB { get; set; }
        public string afterPccpchNB { get; set; }
        
        public string FileName { get; set; }

        public string BeforeNbCellName
        {
            get
            {
                if (nbCellBefore != null)
                    return nbCellBefore.Name;
                return "";
            }
        }

        public string AfterNbCellName
        {
            get
            {
                if (nbCellAfter != null)
                    return nbCellAfter.Name;
                return "";
            }
        }

        public string BeforeNbCellPCI
        {
            get
            {
                if (nbCellBefore != null)
                {
                    return nbCellBefore.PCI.ToString();
                }
                return "";
            }
        }

        public string AfterNbCellPCI
        {
            get
            {
                if (nbCellAfter != null)
                {
                    return nbCellAfter.PCI.ToString();
                }
                return "";
            }
        }

        public string PccpchMain
        {
            get
            {
                return string.Format("{0}", (double)pccpchMain / 100);
            }
        }

        public string BeforePccpchNB
        {
            get
            {
                int iPccpch;
                if (!int.TryParse(beforePccpchNB, out iPccpch))
                {
                    return "";
                }
                return string.Format("{0}", (double)iPccpch / 100);
            }
        }

        public string AfterPccpchNB
        {
            get
            {
                int iPccpch;
                if (!int.TryParse(afterPccpchNB, out iPccpch))
                {
                    return "";
                }
                return string.Format("{0}", (double)iPccpch / 100);
            }
        }

        public string Stage
        {
            get
            {
                string str = "";
                if (stage.Contains("优化前") && stage.Contains("优化后"))
                {
                    str = "不变";
                }
                else if (stage.Contains("优化前"))
                {
                    str = "消除";
                }
                else if (stage.Contains("优化后"))
                {
                    str = "新增";
                }
                return str;
            }
        }

        public System.Drawing.Brush GetBrush()
        {
            System.Drawing.Brush brush = null;
            switch (Stage)
            {
                case "不变":
                    brush = new System.Drawing.SolidBrush(System.Drawing.Color.Yellow);
                    break;
                case "消除":
                    brush = new System.Drawing.SolidBrush(System.Drawing.Color.Green);
                    break;
                case "新增":
                    brush = new System.Drawing.SolidBrush(System.Drawing.Color.Red);
                    break;
                default:
                    break;
            }
            return brush;
        }

        public LTECell GetNBCell()
        {
            if (nbCellAfter != null)
            {
                return nbCellAfter;
            }
            return nbCellBefore;
        }

        public string GetLogTable()
        {
            return string.Format("tb_log_file_{0}_{1:d2}", this.sampleDTtime.Year, this.sampleDTtime.Month);
        }

        public void Fill(SqlDataReader reader, string cityname)
        {
            this.cityname = cityname;
            int idx = 0;
            this.cityid = reader.GetInt32(idx++);
            this.rptime = reader.GetString(idx++);
            this.stage = reader.GetString(idx++);
            this.type = reader.GetString(idx++);
            this.sample = reader.GetInt32(idx++);
            this.cellname = reader.GetString(idx++);
            this.nbcellname = reader.GetString(idx++);
            this.longitude = reader.IsDBNull(idx) ? 0 : reader.GetInt32(idx) / 10000000.0; idx++;
            this.latitude = reader.IsDBNull(idx) ? 0 : reader.GetInt32(idx) / 10000000.0; idx++;
            this.pccpchMain = reader.IsDBNull(idx) ? 0 : reader.GetInt32(idx); idx++;
            this.pccpchNB = reader.IsDBNull(idx) ? 0 : reader.GetInt32(idx); idx++;
            this.fileid = reader.IsDBNull(idx) ? 0 : reader.GetInt32(idx); idx++;
            this.sampletime = reader.IsDBNull(idx) ? 0 : reader.GetInt32(idx); idx++;

            if (reader.FieldCount > idx)
            {
                this.iScrambleCode = reader.GetInt32(idx).ToString();
            }

            if (stage.Contains("优化前"))
            {
                beforenbcellname = nbcellname;
                beforePccpchNB = pccpchNB.ToString();
            }
            else
            {
                afternbcellname = nbcellname;
                afterPccpchNB = pccpchNB.ToString();
            }

            UInt64 year = Convert.ToUInt64(rptime);
            int second = (int)(year % 100);
            year /= 100;
            int minute = (int)(year % 100);
            year /= 100;
            int hour = (int)(year % 100);
            year /= 100;
            int day = (int)(year % 100);
            year /= 100;
            int month = (int)(year % 100);
            year /= 100;
            DateTime pntTime = new DateTime((int)year, month, day, hour, minute, second);
            reporttime = string.Format("{0}年{1}月{2}日 {3}时{4}分{5}秒", pntTime.Year, pntTime.Month, pntTime.Day, pntTime.Hour, pntTime.Minute, pntTime.Second);
            sampleDTtime = JavaDate.GetDateTimeFromMilliseconds((long)sampletime * 1000);
        }

        public void merge(SINRToPCI sinr)
        {
            this.stage += sinr.stage;
            switch (sinr.stage)
            {
                case "优化前":
                    {
                        this.beforenbcellname = sinr.nbcellname;
                        this.beforePccpchNB = sinr.pccpchNB.ToString();
                    }
                    break;
                case "优化后":
                    {
                        this.afternbcellname = sinr.nbcellname;
                        this.afterPccpchNB = sinr.pccpchNB.ToString();
                    }
                    break;
                default: 
                    break;
            }
        }

        public void FinalDeal(Dictionary<string, LTECell> allNameCellDic, string oldPCI, string newPCI)
        {
            this.rt_orig_iScrambleCode = oldPCI;
            this.iScrambleCode = newPCI;
            if (allNameCellDic.ContainsKey(this.cellname))
            {
                this.mainCell = allNameCellDic[this.cellname];
            }
            if (allNameCellDic.ContainsKey(this.afternbcellname))
            {
                this.nbCellAfter = allNameCellDic[this.afternbcellname];
            }
            if (allNameCellDic.ContainsKey(this.beforenbcellname))
            {
                this.nbCellBefore = allNameCellDic[this.beforenbcellname];
            }
        }

        public DbPoint GetLTEAntennaEndPoint(LTECell cell)
        {
            return new DbPoint(cell.EndPointLongitude, cell.EndPointLatitude);
        }
    }

    public class DiyQueryLTESINRFixDB
    {
        private readonly CityInfo curCity;
        private readonly string cellname;
        readonly string strConnDB;
        public List<SINRToPCI> SinrList { get; set; }

        public DiyQueryLTESINRFixDB(CityInfo curCity, string cellname)
        {
            this.cellname = cellname;
            this.curCity = curCity;
            if (curCity != null)
            {
                strConnDB = curCity.GetDBConn();
            }
            SinrList = new List<SINRToPCI>();
        }

        public void Query()
        {
            WaitBox.Show("正在查询LTE质差点信息...", query);
        }

        private void query()
        {
            try
            {
                string sql = "select cityid,reporttime,strstage,strtype,isample,strcellname,strnbcellname,ilongitude1,ilatitude1,ipccpch1,ipccpch2 from tb_tdlte_sinr_info where strcellname = '"
                    + cellname + "' and cityid = " + curCity.serverid + " and reporttime in (select max(reporttime) from tb_tdlte_sinr_info)";

                using (SqlDataReader reader = SqlHelper.ExecuteReader(strConnDB, CommandType.Text, sql))
                {
                    while (reader.Read())
                    {
                        SINRToPCI sinr = new SINRToPCI();
                        sinr.Fill(reader, curCity.servername);
                        SinrList.Add(sinr);
                    }
                }
            }
            finally
            {
                Thread.Sleep(10);
                WaitBox.Close();
            }
        }
    }

    public class DiyQueryAllLteOptTimeFixDB
    {
        private readonly CityInfo curCity;
        readonly string strConnDB;
        public List<string> Times { get; set; }

        public DiyQueryAllLteOptTimeFixDB(CityInfo curCity)
        {
            this.curCity = curCity;
            Times = new List<string>();
            if (curCity != null)
            {
                strConnDB = curCity.GetDBConn();
            }
        }

        public void Query()
        {
            WaitBox.Show("正在查询LTE质差点信息...", query);
        }

        private void query()
        {
            try
            {
                string sql = "select distinct reporttime from tb_tdlte_sinr_info where "
                    + " cityid = " + curCity.serverid + " order by reporttime desc";

                using (SqlDataReader reader = SqlHelper.ExecuteReader(strConnDB, CommandType.Text, sql))
                {
                    while (reader.Read())
                    {
                        string time = Convert.ToString(reader[0]);

                        if (time != string.Empty)
                        {
                            Times.Add(time);
                        }
                    }
                    reader.Close();
                }
            }
            finally
            {
                Thread.Sleep(10);
                WaitBox.Close();
            }
        }
    }

    public class DiyQuerySINRByTimeFixDB
    {
        private readonly CityInfo curCity;
        private readonly string reporttime;
        private readonly List<string> cellnameLst;
        readonly string strConnDB;
        public Dictionary<string, Dictionary<string, SINRToPCI>> CellPntSinrDic { get; set; }
        public List<int> FileIDLst { get; set; }

        public DiyQuerySINRByTimeFixDB(CityInfo curCity, string reporttime, List<string> cellnameLst)
        {
            CellPntSinrDic = new Dictionary<string, Dictionary<string, SINRToPCI>>();
            FileIDLst = new List<int>();
            this.cellnameLst = cellnameLst;
            this.curCity = curCity;
            this.reporttime = reporttime;
            if (curCity != null)
            {
                strConnDB = curCity.GetDBConn();
            }
        }

        public void Query()
        {
            WaitBox.Show("正在查询LTE质差点信息...", query);
        }

        private void query()
        {
            try
            {
                string sql = "select a.cityid,a.reporttime,strstage,strtype,isample,strcellname,strnbcellname,ilongitude1,ilatitude1,ipccpch1,ipccpch2,"
                    + " ifileid,itime,ISNULL(新PCI,0) from tb_tdlte_sinr_info a left join tb_rpt_lte_result b on a.cityid=b.cityid and "
                    + " a.reporttime=b.reporttime and (a.strcellname=b.小区名称 or a.strnbcellname=b.小区名称) where "
                    + " a.cityid = " + curCity.serverid + " and a.reporttime = '" + reporttime + "' order by isample,ilongitude1,ilatitude1";

                DateTime dt;
                if (reporttime.Length == "20151114192516".Length &&
                    (DateTime.TryParse(string.Format("{0}-{1}-{2} {3}:{4}:{5}",
                        reporttime.Substring(0, 4),
                        reporttime.Substring(4, 2),
                        reporttime.Substring(6, 2),
                        reporttime.Substring(8, 2),
                        reporttime.Substring(10, 2),
                        reporttime.Substring(12, 2)), out dt))
                    )
                {
                    sql = "select a.cityid,a.reporttime,strstage,strtype,isample,strcellname,strnbcellname,ilongitude1,ilatitude1,ipccpch1,ipccpch2,"
                    + " ifileid,itime,ISNULL(新PCI,0) from tb_tdlte_sinr_info a left join tb_rpt_lte_result b on a.cityid=b.cityid and "
                    + string.Format("'{0:yyyy-MM-dd HH:mm:ss}'", dt) + "=b.reporttime and (a.strcellname=b.小区名称 or a.strnbcellname=b.小区名称) where "
                    + " a.cityid = " + curCity.serverid + " and a.reporttime = '" + reporttime + "' order by isample,ilongitude1,ilatitude1";
                }

                using (SqlDataReader reader = SqlHelper.ExecuteReader(strConnDB, CommandType.Text, sql))
                {
                    while (reader.Read())
                    {
                        SINRToPCI sinr = new SINRToPCI();
                        sinr.Fill(reader, curCity.servername);
                        string cellname;
                        if (cellnameLst.Contains(sinr.cellname))
                        {
                            cellname = sinr.cellname;
                        }
                        else if (cellnameLst.Contains(sinr.nbcellname))
                        {
                            cellname = sinr.nbcellname;
                        }
                        else
                        {
                            continue;
                        }
                        Dictionary<string, SINRToPCI> pntSinrDic;
                        SINRToPCI s;
                        string key = string.Format("{0}_{1}_{2}_{3}_{4}", 
                            sinr.sample, sinr.longitude, sinr.latitude, sinr.cellname, sinr.nbcellname);
                        if (!CellPntSinrDic.TryGetValue(cellname, out pntSinrDic))
                        {
                            pntSinrDic = new Dictionary<string, SINRToPCI>();
                            CellPntSinrDic[cellname] = pntSinrDic;
                        }
                        if (!pntSinrDic.TryGetValue(key, out s))
                        {
                            pntSinrDic[key] = sinr;
                        }
                        else
                        {
                            pntSinrDic[key].merge(sinr);
                        }
                        if (!FileIDLst.Contains(sinr.fileid))
                            FileIDLst.Add(sinr.fileid);
                    }
                    reader.Close();
                }
            }
            finally
            {
                Thread.Sleep(10);
                WaitBox.Close();
            }
        }
    }

    public class DiyQueryAllLTESINRFixDB
    {
        private readonly CityInfo curCity;
        private readonly List<string> cellnameLst;
        readonly string strConnDB;
        public Dictionary<string, Dictionary<string, SINRToPCI>> CellPntSinrDic { get; set; }
        public List<int> FileIDLst { get; set; }

        public DiyQueryAllLTESINRFixDB(CityInfo curCity, List<string> cellnameLst)
        {
            CellPntSinrDic = new Dictionary<string, Dictionary<string, SINRToPCI>>();
            FileIDLst = new List<int>();
            this.cellnameLst = cellnameLst;
            this.curCity = curCity;
            if (curCity != null)
            {
                strConnDB = curCity.GetDBConn();
            }
        }

        public void Query()
        {
            WaitBox.Show("正在查询LTE质差点信息...", query);
        }

        private void query()
        {
            try
            {
                string sql = "select cityid,reporttime,strstage,strtype,isample,strcellname,strnbcellname,ilongitude1,ilatitude1,ipccpch1,ipccpch2,ifileid,itime from tb_tdlte_sinr_info where "
                    + " cityid = " + curCity.serverid + " and reporttime in (select max(reporttime) from tb_tdlte_sinr_info) order by isample,ilongitude1,ilatitude1";

                using (SqlDataReader reader = SqlHelper.ExecuteReader(strConnDB, CommandType.Text, sql))
                {
                    while (reader.Read())
                    {
                        SINRToPCI sinr = new SINRToPCI();
                        sinr.Fill(reader, curCity.servername);
                        string cellname;
                        if (cellnameLst.Contains(sinr.cellname))
                        {
                            cellname = sinr.cellname;
                        }
                        else if (cellnameLst.Contains(sinr.nbcellname))
                        {
                            cellname = sinr.nbcellname;
                        }
                        else
                        {
                            continue;
                        }
                        Dictionary<string, SINRToPCI> pntSinrDic;
                        SINRToPCI s;
                        string key = string.Format("{0}_{1}_{2}_{3}_{4}",
                            sinr.sample, sinr.longitude, sinr.latitude, sinr.cellname, sinr.nbcellname);
                        if (!CellPntSinrDic.TryGetValue(cellname, out pntSinrDic))
                        {
                            pntSinrDic = new Dictionary<string, SINRToPCI>();
                            CellPntSinrDic[cellname] = pntSinrDic;
                        }
                        if (!pntSinrDic.TryGetValue(key, out s))
                        {
                            pntSinrDic[key] = sinr;
                        }
                        else
                        {
                            pntSinrDic[key].merge(sinr);
                        }
                        if (!FileIDLst.Contains(sinr.fileid))
                            FileIDLst.Add(sinr.fileid);
                    }
                    reader.Close();
                }
            }
            finally
            {
                Thread.Sleep(10);
                WaitBox.Close();
            }
        }
    }

    public class SearchFileByFileID : QueryBase
    {
        private SINRToPCI sinr;
        public FileInfo fileInfo { get; set; }

        public SearchFileByFileID(MainModel mainModel)
            : base(mainModel) 
        { }

        public override string Name
        {
            get { return ""; }
        }

        public override string IconName
        {
            get { return ""; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        public void SetSinr(SINRToPCI sinr)
        {
            this.sinr = sinr;
        }

        protected override void query()
        {
            WaitBox.Show("质差点关联文件...", waitBoxQuery);
        }

        private void waitBoxQuery()
        {
            try
            {
                ClientProxy clientProxy = new ClientProxy();
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                Package package = clientProxy.Package;
                prepareInfoQueryPackage(package, sinr);
                clientProxy.Send();
                fileInfo = reciveFileInfo(clientProxy);
            }
            catch (Exception ex)
            {
                log.Error(ex.StackTrace);
            }
            finally
            {
                Thread.Sleep(100);
                WaitBox.Close();
            }
        }

        private void prepareInfoQueryPackage(Package package, SINRToPCI sinr)
        {
            package.Command = Command.InfoQuery;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_LOG_SEARCH_BYFILEID;
            package.Content.PrepareAddParam();
            package.Content.AddParam(sinr.fileid);
            package.Content.AddParam(sinr.GetLogTable());
        }

        private FileInfo reciveFileInfo(ClientProxy clientProxy)
        {
            FileInfo curFileInfo = null;
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_LOG_SEARCH_BYFILEID)
                {
                    curFileInfo = new FileInfo();
                    curFileInfo.Fill(package.Content);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
            }
            return curFileInfo;
        }
    }

    public class SearchFileByFileIDs : DIYSQLBase
    {
        public Dictionary<int, string> IdNameDic { get; set; }
        public string FileIDs { get; set; }

        public SearchFileByFileIDs(MainModel mModel, List<int> fileIDLst)
            : base(mModel)
        {
            IdNameDic = new Dictionary<int, string>();
            FileIDs = "";
            StringBuilder sb = new StringBuilder();
            foreach (int fileID in fileIDLst)
            {
                sb.Append(fileID + ",");
            }
            FileIDs = sb.ToString();
            if (FileIDs.EndsWith(","))
                FileIDs = FileIDs.Remove(FileIDs.Length - 1);
            if (FileIDs.Length == 0)
                FileIDs = "0";
        }

        protected override string getSqlTextString()
        {
            return string.Format("select ifileid,strfilename from tb_log_event_predeal where ifileid in({0})", FileIDs);
        }

        protected override MasterCom.RAMS.Model.Interface.E_VType[] getSqlRetTypeArr()
        {
            E_VType[] type = new E_VType[2];
            type[0] = E_VType.E_Int;
            type[1] = E_VType.E_String;
            return type;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    int fileid = package.Content.GetParamInt();
                    string fileName = package.Content.GetParamString();
                    if (!IdNameDic.ContainsKey(fileid))
                    {
                        IdNameDic.Add(fileid, fileName);
                    }
                    //do your code here
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }
    }
}
