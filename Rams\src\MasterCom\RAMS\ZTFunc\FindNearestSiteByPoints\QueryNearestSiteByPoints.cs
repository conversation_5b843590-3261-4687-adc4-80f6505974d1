﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.FindNearestSiteByPoints;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryNearestSiteByPoints : QueryBase
    {
        public QueryNearestSiteByPoints()
            : base(MainModel.GetInstance())
        {
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override string Name
        {
            get { return "查找与指定坐标点距离最近的基站"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 19000, 19044, this.Name);
        }

        ConditionDlg dlg = null;
        protected override bool isValidCondition()
        {
            if (dlg == null || dlg.IsDisposed)
            {
                dlg = new ConditionDlg();
                dlg.Owner = MainModel.MainForm;
                dlg.Runner = this;
            }
            dlg.Visible = true;
            dlg.Focus();
            return false;
        }

        protected override void query()
        {
        }

        public void Search(IEnumerable<PointInfo> points, bool includeIndoor)
        {
            if (points == null)
            {
                return;
            }
            List<BTS> btsSet = CellManager.GetInstance().GetCurrentBTSs();
            List<TDNodeB> tdBtsSet = CellManager.GetInstance().GetCurrentTDBTSs();
            List<LTEBTS> lteBtsSet = CellManager.GetInstance().GetCurrentLTEBTSs();
            List<PointNearestSiteInfo> results = new List<PointNearestSiteInfo>();
            foreach (PointInfo pi in points)
            {
                PointNearestSiteInfo pnsInfo = new PointNearestSiteInfo();
                pnsInfo.Point = pi;
                results.Add(pnsInfo);
                double dis;
                ISite site;
                dealGSMBts(includeIndoor, btsSet, pi, pnsInfo, out dis, out site);

                dealTDBts(includeIndoor, tdBtsSet, pi, pnsInfo, out dis, out site);

                dealLTEBts(includeIndoor, lteBtsSet, pi, pnsInfo, out dis, out site);

            }

            ResultForm resultFrm = MainModel.CreateResultForm(typeof(ResultForm)) as ResultForm;
            resultFrm.FillData(results);
            resultFrm.Visible = true;
        }

        private static void dealGSMBts(bool includeIndoor, List<BTS> btsSet, PointInfo pi, PointNearestSiteInfo pnsInfo, out double dis, out ISite site)
        {
            dis = double.MaxValue;
            site = null;
            foreach (BTS bts in btsSet)
            {
                if (bts.Type == BTSType.Indoor && !includeIndoor)
                {
                    continue;
                }
                if (Math.Abs(bts.Longitude - pi.Longitude) > 0.05
                    || Math.Abs(bts.Latitude - pi.Latitude) > 0.05)
                {
                    continue;
                }
                double temp = MathFuncs.GetDistance(pi.Longitude, pi.Latitude, bts.Longitude, bts.Latitude);
                if (temp < dis)
                {
                    dis = temp;
                    site = bts;
                }
            }
            if (site != null)
            {
                pnsInfo.NearestSites.Add(new NearestSiteInfo(pnsInfo, site, dis));
            }
        }

        private static void dealTDBts(bool includeIndoor, List<TDNodeB> tdBtsSet, PointInfo pi, PointNearestSiteInfo pnsInfo, out double dis, out ISite site)
        {
            dis = double.MaxValue;
            site = null;
            foreach (TDNodeB tdNode in tdBtsSet)
            {
                if (tdNode.Type == TDNodeBType.Indoor && !includeIndoor)
                {
                    continue;
                }
                if (Math.Abs(tdNode.Longitude - pi.Longitude) > 0.05
                    || Math.Abs(tdNode.Latitude - pi.Latitude) > 0.05)
                {
                    continue;
                }
                double temp = MathFuncs.GetDistance(pi.Longitude, pi.Latitude, tdNode.Longitude, tdNode.Latitude);
                if (temp < dis)
                {
                    dis = temp;
                    site = tdNode;
                }
            }
            if (site != null)
            {
                pnsInfo.NearestSites.Add(new NearestSiteInfo(pnsInfo, site, dis));
            }
        }

        private void dealLTEBts(bool includeIndoor, List<LTEBTS> lteBtsSet, PointInfo pi, PointNearestSiteInfo pnsInfo, out double dis, out ISite site)
        {
            dis = double.MaxValue;
            site = null;
            foreach (LTEBTS lteBts in lteBtsSet)
            {
                if (lteBts.Type == LTEBTSType.Indoor && !includeIndoor)
                {
                    continue;
                }
                if (Math.Abs(lteBts.Longitude - pi.Longitude) > 0.05
                    || Math.Abs(lteBts.Latitude - pi.Latitude) > 0.05)
                {
                    continue;
                }
                double temp = MathFuncs.GetDistance(pi.Longitude, pi.Latitude, lteBts.Longitude, lteBts.Latitude);
                if (temp < dis)
                {
                    dis = temp;
                    site = lteBts;
                }
            }
            if (site != null)
            {
                pnsInfo.NearestSites.Add(new NearestSiteInfo(pnsInfo, site, dis));
            }
        }

        public void NearSearch(IEnumerable<PointInfo> points, bool includeIndoor, double radius)
        {
            double r = radius * 0.000012;
            if (points == null)
            {
                return;
            }
            List<BTS> btsSet = CellManager.GetInstance().GetCurrentBTSs();
            List<TDNodeB> tdBtsSet = CellManager.GetInstance().GetCurrentTDBTSs();
            List<LTEBTS> lteBtsSet = CellManager.GetInstance().GetCurrentLTEBTSs();
            List<PointNearestSiteInfo> results = new List<PointNearestSiteInfo>();
            foreach (PointInfo pi in points)
            {
                PointNearestSiteInfo pnsInfo = new PointNearestSiteInfo();
                pnsInfo.Point = pi;
                results.Add(pnsInfo);
                dealGSMBts(includeIndoor, radius, r, btsSet, pi, pnsInfo);

                dealTDBts(includeIndoor, radius, r, tdBtsSet, pi, pnsInfo);

                dealLTEBts(includeIndoor, radius, r, lteBtsSet, pi, pnsInfo);

                pnsInfo.NearestSites.Sort();
            }
        }

        private void dealGSMBts(bool includeIndoor, double radius, double r, List<BTS> btsSet, PointInfo pi, PointNearestSiteInfo pnsInfo)
        {
            foreach (BTS bts in btsSet)
            {
                if (bts.Type == BTSType.Indoor && !includeIndoor)
                {
                    continue;
                }
                if (Math.Abs(bts.Longitude - pi.Longitude) > r
                    || Math.Abs(bts.Latitude - pi.Latitude) > r)
                {
                    continue;
                }
                double dis = MathFuncs.GetDistance(pi.Longitude, pi.Latitude, bts.Longitude, bts.Latitude);
                if (dis > radius)
                {
                    continue;
                }
                pnsInfo.NearestSites.Add(new NearestSiteInfo(pnsInfo, bts, dis));

            }
        }

        private void dealTDBts(bool includeIndoor, double radius, double r, List<TDNodeB> tdBtsSet, PointInfo pi, PointNearestSiteInfo pnsInfo)
        {
            foreach (TDNodeB tdNode in tdBtsSet)
            {
                if (tdNode.Type == TDNodeBType.Indoor && !includeIndoor)
                {
                    continue;
                }
                if (Math.Abs(tdNode.Longitude - pi.Longitude) > r
                    || Math.Abs(tdNode.Latitude - pi.Latitude) > r)
                {
                    continue;
                }
                double dis = MathFuncs.GetDistance(pi.Longitude, pi.Latitude, tdNode.Longitude, tdNode.Latitude);
                if (dis > radius)
                {
                    continue;
                }
                pnsInfo.NearestSites.Add(new NearestSiteInfo(pnsInfo, tdNode, dis));
            }
        }

        private void dealLTEBts(bool includeIndoor, double radius, double r, List<LTEBTS> lteBtsSet, PointInfo pi, PointNearestSiteInfo pnsInfo)
        {
            foreach (LTEBTS lteBts in lteBtsSet)
            {
                if (lteBts.Type == LTEBTSType.Indoor && !includeIndoor)
                {
                    continue;
                }
                if (Math.Abs(lteBts.Longitude - pi.Longitude) > r
                    || Math.Abs(lteBts.Latitude - pi.Latitude) > r)
                {
                    continue;
                }
                double dis = MathFuncs.GetDistance(pi.Longitude, pi.Latitude, lteBts.Longitude, lteBts.Latitude);
                if (dis > radius)
                {
                    continue;
                }
                pnsInfo.NearestSites.Add(new NearestSiteInfo(pnsInfo, lteBts, dis));
            }
        }
    }
}
