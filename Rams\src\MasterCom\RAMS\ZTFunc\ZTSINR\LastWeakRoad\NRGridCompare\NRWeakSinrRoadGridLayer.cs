﻿using MasterCom.MTGis;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRWeakSinrRoadGridLayer : LayerBase
    {
        public NRWeakSinrRoadGridLayer()
            : base("NR质差对比图层")
        {
         
        }

        public List<NRWeakSinrRoadGrid> Peroid1WeakGrids { get; set; } = new List<NRWeakSinrRoadGrid>();
        public List<NRWeakSinrRoadGrid> Peroid2Grids { get; set; } = new List<NRWeakSinrRoadGrid>();

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            updateRect.Inflate((int)(40 * 10000 / MapScale), (int)(40 * 10000 / MapScale));
            DbRect dRect;
            gisAdapter.FromDisplay(updateRect, out dRect);
            graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
            drawWeakGrid(dRect, graphics);
        }

        public bool ShowPeriod1Grid { get; set; } = true;
        SolidBrush period1Brush = new SolidBrush(Color.FromArgb(200, Color.Yellow));
        public Color Period1GridColor
        {
            get { return period1Brush.Color; }
            set { period1Brush = new SolidBrush(Color.FromArgb(200, value)); }
        }

        public bool ShowPeriod2RepeatGrid { get; set; } = true;
        SolidBrush period2RepeatBrush = new SolidBrush(Color.FromArgb(200, Color.Red));
        public Color Period2RepeatGridColor
        {
            get { return period2RepeatBrush.Color; }
            set { period2RepeatBrush = new SolidBrush(Color.FromArgb(200, value)); }
        }

        public bool ShowPeriod2NewGrid { get; set; } = true;
        SolidBrush period2NewBrush = new SolidBrush(Color.FromArgb(200, Color.Blue));
        public Color Period2NewGridColor
        {
            get { return period2NewBrush.Color; }
            set { period2NewBrush = new SolidBrush(Color.FromArgb(200, value)); }
        }

        private void drawWeakGrid(DbRect curViewRect, Graphics g)
        {
            if (ShowPeriod1Grid)
            {
                foreach (NRWeakSinrRoadGrid grid in Peroid1WeakGrids)
                {
                    foreach (DbRect rect in grid.Grids)
                    {
                        fillGrid(curViewRect, rect, g, period1Brush);
                    }
                }
            }

            if (ShowPeriod2NewGrid)
            {
                foreach (NRWeakSinrRoadGrid grid in Peroid2Grids)
                {
                    SolidBrush brs = period2NewBrush;
                    if (grid.IntersectSegNum > 0)
                    {
                        brs = period2RepeatBrush;
                    }
                    foreach (DbRect rect in grid.Grids)
                    {
                        fillGrid(curViewRect, rect, g, brs);
                    }
                }
            }
        }

        private void fillGrid(DbRect curViewRect, DbRect gridRect, Graphics g, Brush brush)
        {
            if (gridRect.x2 > curViewRect.x1 && gridRect.x1 < curViewRect.x2 && gridRect.y2 > curViewRect.y1 && gridRect.y1 < curViewRect.y2)
            {
                DbPoint ltPoint = new DbPoint(gridRect.x1, gridRect.y2);
                PointF pointLt;
                gisAdapter.ToDisplay(ltPoint, out pointLt);
                DbPoint brPoint = new DbPoint(gridRect.x2, gridRect.y1);
                PointF pointBr;
                gisAdapter.ToDisplay(brPoint, out pointBr);
                g.FillRectangle(brush, pointLt.X, pointLt.Y, pointBr.X - pointLt.X, pointBr.Y - pointLt.Y);
            }
        }
    }
}
