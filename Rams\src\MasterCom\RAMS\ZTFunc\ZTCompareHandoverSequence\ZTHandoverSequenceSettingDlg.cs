﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTHandoverSequenceSettingDlg : BaseFormStyle
    {
        private ComboBox comboBoxRoadName;
        private ComboBox comboBoxNetType;
        private ComboBox comboBoxCoverType;
        private List<HandoverSequenceCondition> hosqConditionLst;

        public ZTHandoverSequenceSettingDlg(List<FileInfo> files)
        {
            InitializeComponent();
            refreshCombobox();
            buildHOSCond(files);
            refreshDataGridView();
        }

        private void refreshDataGridView()
        {
            DataGridViewColumn colSn = new DataGridViewTextBoxColumn();
            DataGridViewColumn colFileName = new DataGridViewTextBoxColumn();
            DataGridViewColumn colProject = new DataGridViewTextBoxColumn();
            DataGridViewColumn colService = new DataGridViewTextBoxColumn();
            DataGridViewColumn colCarrier = new DataGridViewTextBoxColumn();
            DataGridViewColumn colRoadName = new DataGridViewTextBoxColumn();
            DataGridViewColumn colNetType = new DataGridViewTextBoxColumn();
            DataGridViewColumn colCoverType = new DataGridViewTextBoxColumn();
            colSn.DataPropertyName = "Sn";
            colSn.HeaderText = "序号";
            colFileName.DataPropertyName = "FileName";
            colFileName.HeaderText = "文件名";
            colProject.DataPropertyName = "ProjectType";
            colProject.HeaderText = "项目类型";
            colService.DataPropertyName = "ServiceType";
            colService.HeaderText = "业务类型";
            colCarrier.DataPropertyName = "Carrier";
            colCarrier.HeaderText = "运营商";
            colRoadName.DataPropertyName = "StreetNameStr";
            colRoadName.HeaderText = "道路名称";
            colNetType.DataPropertyName = "NetTypeStr";
            colNetType.HeaderText = "网络类型";
            colCoverType.DataPropertyName = "CoverTypeStr";
            colCoverType.HeaderText = "覆盖类型";

            dataGridViewFiles.Columns.Add(colSn);
            dataGridViewFiles.Columns.Add(colFileName);
            dataGridViewFiles.Columns.Add(colProject);
            dataGridViewFiles.Columns.Add(colService);
            dataGridViewFiles.Columns.Add(colCarrier);
            dataGridViewFiles.Columns.Add(colRoadName);
            dataGridViewFiles.Columns.Add(colNetType);
            dataGridViewFiles.Columns.Add(colCoverType);

            this.dataGridViewFiles.AllowUserToAddRows = false;
            this.dataGridViewFiles.DataSource = hosqConditionLst;
            this.dataGridViewFiles.Controls.Clear();
            this.dataGridViewFiles.Controls.Add(comboBoxRoadName);
            this.dataGridViewFiles.Controls.Add(comboBoxNetType);
            this.dataGridViewFiles.Controls.Add(comboBoxCoverType);

            if (comboBoxRoadName.Items.Count > 0)
                comboBoxRoadName.SelectedIndex = 0;
            if (comboBoxNetType.Items.Count > 0)
                comboBoxNetType.SelectedIndex = 0;
            if (comboBoxCoverType.Items.Count > 0)
                comboBoxCoverType.SelectedIndex = 0;
        }

        private void dataGridViewFiles_CurrentCellChanged(object sender, EventArgs e)
        {
            try
            {
                if (dataGridViewFiles.CurrentCell == null) return;
                Rectangle rect = dataGridViewFiles.GetCellDisplayRectangle(dataGridViewFiles.CurrentCell.ColumnIndex, dataGridViewFiles.CurrentCell.RowIndex, false);

                if (dataGridViewFiles.Columns[dataGridViewFiles.CurrentCell.ColumnIndex].DataPropertyName == "StreetNameStr")
                {
                    comboBoxRoadName.Left = rect.Left;
                    comboBoxRoadName.Top = rect.Top;
                    comboBoxRoadName.Width = rect.Width;
                    comboBoxRoadName.Height = rect.Height;
                    comboBoxRoadName.Visible = true;
                    comboBoxRoadName.SelectedItem = dataGridViewFiles.CurrentCell.Value;
                }
                else
                    comboBoxRoadName.Visible = false;

                if (dataGridViewFiles.Columns[dataGridViewFiles.CurrentCell.ColumnIndex].DataPropertyName == "NetTypeStr")
                {
                    comboBoxNetType.Left = rect.Left;
                    comboBoxNetType.Top = rect.Top;
                    comboBoxNetType.Width = rect.Width;
                    comboBoxNetType.Height = rect.Height;
                    comboBoxNetType.Visible = true;
                    comboBoxNetType.SelectedItem = dataGridViewFiles.CurrentCell.Value;
                }
                else
                    comboBoxNetType.Visible = false;

                if (dataGridViewFiles.Columns[dataGridViewFiles.CurrentCell.ColumnIndex].DataPropertyName == "CoverTypeStr")
                {
                    comboBoxCoverType.Left = rect.Left;
                    comboBoxCoverType.Top = rect.Top;
                    comboBoxCoverType.Width = rect.Width;
                    comboBoxCoverType.Height = rect.Height;
                    comboBoxCoverType.Visible = true;
                    comboBoxCoverType.SelectedItem = dataGridViewFiles.CurrentCell.Value;
                }
                else
                    comboBoxCoverType.Visible = false;
            }
            catch
            {
                //continue
            }
        }

        private void refreshCombobox()
        {
            comboBoxRoadName = new ComboBox();
            comboBoxNetType = new ComboBox();
            comboBoxCoverType = new ComboBox();
            comboBoxRoadName.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBoxNetType.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBoxCoverType.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBoxRoadName.SelectedIndexChanged += new EventHandler(comboBoxRoadName_SelectedIndexChanged);
            comboBoxNetType.SelectedIndexChanged += new EventHandler(comboBoxNetType_SelectedIndexChanged);
            comboBoxCoverType.SelectedIndexChanged += new EventHandler(comboBoxCoverType_SelectedIndexChanged);

            this.comboBoxRoadName.Items.Clear();
            foreach (string roadName in HandoverStreetCellCfgManager.GetInstance().StreetNameLst)
            {
                this.comboBoxRoadName.Items.Add(roadName);
            }

            this.comboBoxNetType.Items.Clear();
            foreach (string netType in HandoverStreetCellCfgManager.GetInstance().NetTypeLst)
            {
                this.comboBoxNetType.Items.Add(netType);
            }

            this.comboBoxCoverType.Items.Clear();
            foreach (string coverType in HandoverStreetCellCfgManager.GetInstance().CoverTypeLst)
            {
                this.comboBoxCoverType.Items.Add(coverType);
            }
        }

        private void buildHOSCond(List<FileInfo> files)
        {
            hosqConditionLst = new List<HandoverSequenceCondition>();
            foreach (FileInfo file in files)
            {
                hosqConditionLst.Add(new HandoverSequenceCondition(hosqConditionLst.Count + 1, file));
            }
        }

        private void simpleButtonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleButtonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void comboBoxRoadName_SelectedIndexChanged(object sender, EventArgs e)
        {
            foreach (DataGridViewCell cell in dataGridViewFiles.SelectedCells)
            {
                if (cell != null && dataGridViewFiles.Columns[cell.ColumnIndex].DataPropertyName == "StreetNameStr")
                {
                    cell.Value = ((ComboBox)sender).Text;
                }
            }
        }

        private void comboBoxNetType_SelectedIndexChanged(object sender, EventArgs e)
        {
            foreach (DataGridViewCell cell in dataGridViewFiles.SelectedCells)
            {
                if (cell != null && dataGridViewFiles.Columns[cell.ColumnIndex].DataPropertyName == "NetTypeStr")
                {
                    cell.Value = ((ComboBox)sender).Text;
                }
            }
        }

        private void comboBoxCoverType_SelectedIndexChanged(object sender, EventArgs e)
        {
            foreach (DataGridViewCell cell in dataGridViewFiles.SelectedCells)
            {
                if (cell != null && dataGridViewFiles.Columns[cell.ColumnIndex].DataPropertyName == "CoverTypeStr")
                {
                    cell.Value = ((ComboBox)sender).Text;
                }
            }
        }

        public List<HandoverSequenceCondition> GetCondition()
        {
            return hosqConditionLst;
        }

        private void dataGridViewFiles_Scroll(object sender, ScrollEventArgs e)
        {
            setVisible();
        }

        private void dataGridViewFiles_ColumnWidthChanged(object sender, DataGridViewColumnEventArgs e)
        {
            setVisible();
        }

        private void setVisible()
        {
            comboBoxRoadName.Visible = false;
            comboBoxNetType.Visible = false;
            comboBoxCoverType.Visible = false;
        }
    }

    public class HandoverSequenceCondition
    {
        public int Sn { get; set; }
        public FileInfo HOSFile { get; set; }
        public string FileName { get { return HOSFile.Name; } }
        public string ProjectType { get { return HOSFile.ProjectDescription; } }
        public string ServiceType { get { return HOSFile.ServiceTypeDescription; } }
        public string Carrier { get { return HOSFile.CarrierTypeDescription; } }
        public string StreetNameStr { get; set; }
        public string NetTypeStr { get; set; }
        public string CoverTypeStr { get; set; }

        public HandoverSequenceCondition(int sn, FileInfo file)
        {
            this.Sn = sn;
            this.HOSFile = file;
            this.StreetNameStr = HandoverStreetCellCfgManager.GetInstance().StreetNameLst[0];
            this.NetTypeStr = HandoverStreetCellCfgManager.GetInstance().NetTypeLst[0];
            this.CoverTypeStr = HandoverStreetCellCfgManager.GetInstance().CoverTypeLst[0];
        }
    }
}
