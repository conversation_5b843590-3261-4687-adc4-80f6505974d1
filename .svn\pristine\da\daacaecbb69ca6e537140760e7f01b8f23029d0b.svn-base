﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class NRIndoorStationAcceptQuery : NRStationAcceptQuery
    {
        public NRIndoorStationAcceptQuery(MainModel mainModel)
           : base(mainModel)
        {
        }

        public override string Name
        {
            get
            {
                return "NR室分验收";
            }
        }

        NRIndoorStationSettingDlgConfigModel_XJ curCondition = null;
        protected override bool isValidCondition()
        {
            List<FileInfo> nrFiles = new List<FileInfo>();
            foreach (var file in Condition.FileInfos)
            {
                ServiceName name = ServiceTypeManager.getServiceNameFromTypeID(file.ServiceType);
                if (name == ServiceName.NR)
                {
                    nrFiles.Add(file);
                }
            }
            if (nrFiles.Count == 0)
            {
                MessageBox.Show("请选择5G文件进行单验");
                return false;
            }
            Condition.FileInfos = nrFiles;

            curCondition = NRIndoorStationSettingDlgConfig_XJ.Instance.LoadConfig();
            NRIndoorStationSettingDlg_XJ dlg = new NRIndoorStationSettingDlg_XJ();
            dlg.setCondition(curCondition);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }

            NRIndoorStationAcceptCondition cond = new NRIndoorStationAcceptCondition();
            cond.Init();
            FolderBrowserDialog Folderdlg = new FolderBrowserDialog();
            if (Folderdlg.ShowDialog() == DialogResult.OK)
            {
                cond.SaveFolder = Folderdlg.SelectedPath;
                initManager(cond);
                return true;
            }
            return false;
        }

        protected override void initManager(StationAcceptConditionBase cond)
        {
            manager = new NRIndoorStationAcceptManager();
            manager.SetAcceptCond(cond);
        }

        protected override List<string> queryColumns
        {
            get
            {
                return new List<string>()
                {
                     "isampleid",
                     "itime",
                     "ilongitude",
                     "ilatitude",
                     "NR_SSB_ARFCN",
                     "NR_PCI",
                     "NR_SS_RSRP",
                     "NR_SS_SINR",
                     "NR_Throughput_MAC_DL",
                     "NR_Throughput_MAC_UL",
                     "NR_APP_type",
                     "NR_APP_Status",

                     "NR_lte_TAC",
                     "NR_lte_ECI",
                     "NR_lte_EARFCN",
                     "NR_lte_PCI",
                     "NR_lte_RSRP",
                     "NR_lte_RSRQ",
                     "NR_lte_RSSI",
                     "NR_lte_SINR",

                     "NR_NCell_CellType",
                     "NR_NCell_RSRP",
                     "NR_NCell_ARFCN",
                     "NR_NCell_PCI"
                };
            }
        }
    }

    public class NRIndoorStationAcceptCondition : NRStationAcceptCondition
    {
        public override void Init()
        {
            Standard = new NRIndoorThroughputStandard();
            Standard.Init();
        }
    }

    public class NRIndoorThroughputStandard : NRThroughputStandard
    {
        public override void Init()
        {
            ThroughputStandardList = new List<ThroughputStandard>()
            {
                new ThroughputStandard(NRServiceName.SA, 60, 1,
                  new ThroughputStandard.DataInfo( 120, 0, 72),
                  new ThroughputStandard.DataInfo(12, 0, 3)),
                new ThroughputStandard(NRServiceName.SA, 60, 2,
                  new ThroughputStandard.DataInfo( 180, 0, 120),
                  new ThroughputStandard.DataInfo(18, 0, 3)),
                new ThroughputStandard(NRServiceName.SA, 60, 4,
                  new ThroughputStandard.DataInfo( 360, 0, 210),
                  new ThroughputStandard.DataInfo(42, 0, 3)),

                new ThroughputStandard(NRServiceName.SA, 100, 1,
                  new ThroughputStandard.DataInfo( 200, 0, 120),
                  new ThroughputStandard.DataInfo(20, 0, 5)),
                new ThroughputStandard(NRServiceName.SA, 100, 2,
                  new ThroughputStandard.DataInfo( 300, 0, 200),
                  new ThroughputStandard.DataInfo(30, 0, 5)),
                new ThroughputStandard(NRServiceName.SA, 100, 4,
                  new ThroughputStandard.DataInfo( 600, 0, 350),
                  new ThroughputStandard.DataInfo(70, 0, 5)),

                new ThroughputStandard(NRServiceName.NSA, 60, 1,
                  new ThroughputStandard.DataInfo( 120, 0, 72),
                  new ThroughputStandard.DataInfo(12, 0, 1.2)),
                new ThroughputStandard(NRServiceName.NSA, 60, 2,
                  new ThroughputStandard.DataInfo( 180, 0, 120),
                  new ThroughputStandard.DataInfo(12, 0, 1.2)),
                new ThroughputStandard(NRServiceName.NSA, 60, 4,
                  new ThroughputStandard.DataInfo( 360, 0, 210),
                  new ThroughputStandard.DataInfo(12, 0, 1.2)),

                new ThroughputStandard(NRServiceName.NSA, 100, 1,
                  new ThroughputStandard.DataInfo( 200, 0, 120),
                  new ThroughputStandard.DataInfo(20, 0, 2)),
                new ThroughputStandard(NRServiceName.NSA, 100, 2,
                  new ThroughputStandard.DataInfo( 300, 0, 200),
                  new ThroughputStandard.DataInfo(20, 0, 2)),
                new ThroughputStandard(NRServiceName.NSA, 100, 4,
                  new ThroughputStandard.DataInfo( 600, 0, 350),
                  new ThroughputStandard.DataInfo(20, 0, 2)),
            };
        }
    }
}
