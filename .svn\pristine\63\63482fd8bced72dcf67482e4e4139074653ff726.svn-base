using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Drawing.Drawing2D;

namespace MasterCom.RAMS.Model
{
    public class SymbolManager
    {
        public static SymbolManager GetInstance()
        {
            return instance;
        }

        private static SymbolManager instance = new SymbolManager();

        private SymbolManager()
        {
            init();
        }

        public int Count
        {
            get { return Points.Count; }
        }
        
        public List<PointF[]> Points { get; set; } = new List<PointF[]>();

        public List<GraphicsPath> Paths { get; set; } = new List<GraphicsPath>();

        public List<Image> Images { get; set; } = new List<Image>();

        private void init()
        {
            int radius = SymbolWidth / 2;
            Points.Add(new PointF[4] { new PointF(-radius, -radius), new PointF(radius, -radius), new PointF(radius, radius), new PointF(-radius, radius) });
            Points.Add(new PointF[4] { new PointF(-radius, -radius), new PointF(radius, -radius), new PointF(radius, radius), new PointF(-radius, radius) });
            Points.Add(new PointF[3] { new PointF(0, -radius), new PointF(-radius, radius), new PointF(radius, radius) });
            Points.Add(new PointF[3] { new PointF(-radius, -radius), new PointF(radius, -radius), new PointF(0, radius) });
            Points.Add(new PointF[4] { new PointF(0, -radius), new PointF(-radius, 0), new PointF(0, radius), new PointF(radius, 0) });
            Points.Add(new PointF[3] { new PointF(-radius, -radius), new PointF(-radius, radius), new PointF(radius, radius) });
            Points.Add(new PointF[3] { new PointF(-radius, radius), new PointF(radius, radius), new PointF(radius, -radius) });
            Points.Add(new PointF[3] { new PointF(radius, radius), new PointF(radius, -radius), new PointF(-radius, -radius) });
            Points.Add(new PointF[3] { new PointF(radius, -radius), new PointF(-radius, -radius), new PointF(-radius, radius) });
            GraphicsPath path = new GraphicsPath();
            path.AddEllipse(Points[0][0].X, Points[0][0].Y, SymbolWidth , SymbolWidth);
            Paths.Add(path);
            path = new GraphicsPath();
            path.AddRectangle(new RectangleF(Points[1][0].X, Points[1][0].Y, SymbolWidth, SymbolWidth));
            Paths.Add(path);
            for (int i = 2; i < Points.Count; i++)
            {
                path = new GraphicsPath();
                path.AddPolygon(Points[i]);
                Paths.Add(path);
            }
            for (int i = 0; i < Paths.Count; i++)
            {
                Bitmap image = new Bitmap(SymbolWidth, SymbolWidth);
                Graphics g = Graphics.FromImage(image);
                g.TranslateTransform((float)SymbolWidth / 2, (float)SymbolWidth / 2);
                g.FillPath(Brushes.Green, Paths[i]);
                Images.Add(image);
            }
        }

        public const int SymbolWidth = 16;
    }
}
