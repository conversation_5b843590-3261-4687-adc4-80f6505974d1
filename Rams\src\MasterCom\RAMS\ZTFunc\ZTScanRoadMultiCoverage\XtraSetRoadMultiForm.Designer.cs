﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class XtraSetRoadMultiForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.labelControlBand = new DevExpress.XtraEditors.LabelControl();
            this.numRxLevDValue = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.label4 = new System.Windows.Forms.Label();
            this.numRxLevThreshold = new DevExpress.XtraEditors.SpinEdit();
            this.simpleButton1 = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButton2 = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.cbxCoFreqType = new DevExpress.XtraEditors.ComboBoxEdit();
            this.chkCoFreq = new DevExpress.XtraEditors.CheckEdit();
            this.chkSaveTestPoint = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditInvalidThresold = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.numDiffMin = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.checkBandEditDiff = new DevExpress.XtraEditors.CheckEdit();
            this.grpNoneMainPnt = new System.Windows.Forms.GroupBox();
            this.labelControl11 = new System.Windows.Forms.Label();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevThreshold.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxCoFreqType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCoFreq.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSaveTestPoint.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditInvalidThresold.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDiffMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkBandEditDiff.Properties)).BeginInit();
            this.grpNoneMainPnt.SuspendLayout();
            this.SuspendLayout();
            // 
            // labelControlBand
            // 
            this.labelControlBand.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControlBand.Appearance.Options.UseFont = true;
            this.labelControlBand.Location = new System.Drawing.Point(33, 28);
            this.labelControlBand.Name = "labelControlBand";
            this.labelControlBand.Size = new System.Drawing.Size(168, 12);
            this.labelControlBand.TabIndex = 3;
            this.labelControlBand.Text = "相对覆盖带：与最强信号差异 <";
            // 
            // numRxLevDValue
            // 
            this.numRxLevDValue.EditValue = new decimal(new int[] {
            6,
            0,
            0,
            0});
            this.numRxLevDValue.Location = new System.Drawing.Point(209, 24);
            this.numRxLevDValue.Name = "numRxLevDValue";
            this.numRxLevDValue.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numRxLevDValue.Properties.Appearance.Options.UseFont = true;
            this.numRxLevDValue.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRxLevDValue.Properties.IsFloatValue = false;
            this.numRxLevDValue.Properties.Mask.AutoComplete = DevExpress.XtraEditors.Mask.AutoCompleteType.None;
            this.numRxLevDValue.Properties.Mask.EditMask = "N00";
            this.numRxLevDValue.Size = new System.Drawing.Size(82, 20);
            this.numRxLevDValue.TabIndex = 0;
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(296, 29);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(18, 12);
            this.labelControl1.TabIndex = 5;
            this.labelControl1.Text = "dBm";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.Location = new System.Drawing.Point(65, 59);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(137, 12);
            this.label4.TabIndex = 8;
            this.label4.Text = "绝对覆盖带：信号强度 >";
            // 
            // numRxLevThreshold
            // 
            this.numRxLevThreshold.EditValue = new decimal(new int[] {
            80,
            0,
            0,
            -2147483648});
            this.numRxLevThreshold.Location = new System.Drawing.Point(208, 56);
            this.numRxLevThreshold.Name = "numRxLevThreshold";
            this.numRxLevThreshold.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numRxLevThreshold.Properties.Appearance.Options.UseFont = true;
            this.numRxLevThreshold.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRxLevThreshold.Properties.IsFloatValue = false;
            this.numRxLevThreshold.Properties.Mask.EditMask = "N00";
            this.numRxLevThreshold.Size = new System.Drawing.Size(82, 20);
            this.numRxLevThreshold.TabIndex = 1;
            // 
            // simpleButton1
            // 
            this.simpleButton1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.simpleButton1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButton1.Appearance.Options.UseFont = true;
            this.simpleButton1.Location = new System.Drawing.Point(396, 318);
            this.simpleButton1.Name = "simpleButton1";
            this.simpleButton1.Size = new System.Drawing.Size(75, 23);
            this.simpleButton1.TabIndex = 5;
            this.simpleButton1.Text = "确定";
            this.simpleButton1.Click += new System.EventHandler(this.simpleButton1_Click);
            // 
            // simpleButton2
            // 
            this.simpleButton2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.simpleButton2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButton2.Appearance.Options.UseFont = true;
            this.simpleButton2.Location = new System.Drawing.Point(488, 318);
            this.simpleButton2.Name = "simpleButton2";
            this.simpleButton2.Size = new System.Drawing.Size(75, 23);
            this.simpleButton2.TabIndex = 6;
            this.simpleButton2.Text = "取消";
            this.simpleButton2.Click += new System.EventHandler(this.simpleButton2_Click);
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(296, 59);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(18, 12);
            this.labelControl2.TabIndex = 12;
            this.labelControl2.Text = "dBm";
            // 
            // cbxCoFreqType
            // 
            this.cbxCoFreqType.Location = new System.Drawing.Point(94, 148);
            this.cbxCoFreqType.Name = "cbxCoFreqType";
            this.cbxCoFreqType.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbxCoFreqType.Properties.Appearance.Options.UseFont = true;
            this.cbxCoFreqType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxCoFreqType.Properties.Items.AddRange(new object[] {
            "BCCH&TCH",
            "BCCH Only",
            "TCH Only"});
            this.cbxCoFreqType.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxCoFreqType.Size = new System.Drawing.Size(102, 20);
            this.cbxCoFreqType.TabIndex = 3;
            // 
            // chkCoFreq
            // 
            this.chkCoFreq.Location = new System.Drawing.Point(31, 148);
            this.chkCoFreq.Name = "chkCoFreq";
            this.chkCoFreq.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkCoFreq.Properties.Appearance.Options.UseFont = true;
            this.chkCoFreq.Properties.Caption = "同频";
            this.chkCoFreq.Size = new System.Drawing.Size(57, 19);
            this.chkCoFreq.TabIndex = 2;
            this.chkCoFreq.CheckedChanged += new System.EventHandler(this.chkCoFreq_CheckedChanged);
            // 
            // chkSaveTestPoint
            // 
            this.chkSaveTestPoint.Location = new System.Drawing.Point(31, 121);
            this.chkSaveTestPoint.Name = "chkSaveTestPoint";
            this.chkSaveTestPoint.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkSaveTestPoint.Properties.Appearance.Options.UseFont = true;
            this.chkSaveTestPoint.Properties.Caption = "保留采样点信息";
            this.chkSaveTestPoint.Size = new System.Drawing.Size(115, 19);
            this.chkSaveTestPoint.TabIndex = 4;
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.ForeColor = System.Drawing.Color.Red;
            this.labelControl3.Appearance.Options.UseForeColor = true;
            this.labelControl3.Location = new System.Drawing.Point(142, 123);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(180, 14);
            this.labelControl3.TabIndex = 18;
            this.labelControl3.Text = "（用于导出采样点，内存占用大）";
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(51, 90);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(150, 12);
            this.labelControl4.TabIndex = 3;
            this.labelControl4.Text = "计算有效性：最强信号强度>";
            // 
            // spinEditInvalidThresold
            // 
            this.spinEditInvalidThresold.EditValue = new decimal(new int[] {
            95,
            0,
            0,
            -2147483648});
            this.spinEditInvalidThresold.Location = new System.Drawing.Point(209, 87);
            this.spinEditInvalidThresold.Name = "spinEditInvalidThresold";
            this.spinEditInvalidThresold.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditInvalidThresold.Properties.Appearance.Options.UseFont = true;
            this.spinEditInvalidThresold.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditInvalidThresold.Properties.IsFloatValue = false;
            this.spinEditInvalidThresold.Properties.Mask.EditMask = "N00";
            this.spinEditInvalidThresold.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.spinEditInvalidThresold.Properties.MinValue = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.spinEditInvalidThresold.Size = new System.Drawing.Size(82, 20);
            this.spinEditInvalidThresold.TabIndex = 1;
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(296, 90);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(18, 12);
            this.labelControl5.TabIndex = 12;
            this.labelControl5.Text = "dBm";
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.ForeColor = System.Drawing.Color.Red;
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Appearance.Options.UseForeColor = true;
            this.labelControl6.Location = new System.Drawing.Point(320, 92);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(216, 12);
            this.labelControl6.TabIndex = 3;
            this.labelControl6.Text = "（不符合该条件的采样点，视为无效点）";
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControl7.Appearance.Options.UseFont = true;
            this.labelControl7.Location = new System.Drawing.Point(136, 33);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(132, 12);
            this.labelControl7.TabIndex = 3;
            this.labelControl7.Text = "异频段最强信号强度差≥";
            // 
            // numDiffMin
            // 
            this.numDiffMin.EditValue = new decimal(new int[] {
            12,
            0,
            0,
            0});
            this.numDiffMin.Enabled = false;
            this.numDiffMin.Location = new System.Drawing.Point(275, 29);
            this.numDiffMin.Name = "numDiffMin";
            this.numDiffMin.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numDiffMin.Properties.Appearance.Options.UseFont = true;
            this.numDiffMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDiffMin.Properties.IsFloatValue = false;
            this.numDiffMin.Properties.Mask.EditMask = "N00";
            this.numDiffMin.Properties.MaxValue = new decimal(new int[] {
            110,
            0,
            0,
            0});
            this.numDiffMin.Properties.MinValue = new decimal(new int[] {
            130,
            0,
            0,
            -2147483648});
            this.numDiffMin.Size = new System.Drawing.Size(82, 20);
            this.numDiffMin.TabIndex = 1;
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl8.Appearance.Options.UseFont = true;
            this.labelControl8.Location = new System.Drawing.Point(74, 61);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(408, 12);
            this.labelControl8.TabIndex = 3;
            this.labelControl8.Text = "1.分析1800采样点时，900最强信号-1800最强信号符合设定值，视为非主控点";
            // 
            // labelControl9
            // 
            this.labelControl9.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl9.Appearance.Options.UseFont = true;
            this.labelControl9.Location = new System.Drawing.Point(74, 80);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(402, 12);
            this.labelControl9.TabIndex = 3;
            this.labelControl9.Text = "2.分析900采样点时，1800最强信号-900最强信号符合设定值，视为非主控点";
            // 
            // checkBandEditDiff
            // 
            this.checkBandEditDiff.Location = new System.Drawing.Point(27, 31);
            this.checkBandEditDiff.Name = "checkBandEditDiff";
            this.checkBandEditDiff.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkBandEditDiff.Properties.Appearance.Options.UseFont = true;
            this.checkBandEditDiff.Properties.Caption = "计算非主控点";
            this.checkBandEditDiff.Size = new System.Drawing.Size(99, 19);
            this.checkBandEditDiff.TabIndex = 2;
            this.checkBandEditDiff.CheckedChanged += new System.EventHandler(this.chkCoFreq_CheckedChanged);
            // 
            // grpNoneMainPnt
            // 
            this.grpNoneMainPnt.Controls.Add(this.labelControl11);
            this.grpNoneMainPnt.Controls.Add(this.labelControl10);
            this.grpNoneMainPnt.Controls.Add(this.checkBandEditDiff);
            this.grpNoneMainPnt.Controls.Add(this.labelControl7);
            this.grpNoneMainPnt.Controls.Add(this.labelControl8);
            this.grpNoneMainPnt.Controls.Add(this.labelControl9);
            this.grpNoneMainPnt.Controls.Add(this.numDiffMin);
            this.grpNoneMainPnt.Location = new System.Drawing.Point(33, 189);
            this.grpNoneMainPnt.Name = "grpNoneMainPnt";
            this.grpNoneMainPnt.Size = new System.Drawing.Size(529, 113);
            this.grpNoneMainPnt.TabIndex = 19;
            this.grpNoneMainPnt.TabStop = false;
            this.grpNoneMainPnt.Text = "非主控点定义";
            // 
            // labelControl11
            // 
            this.labelControl11.AutoSize = true;
            this.labelControl11.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl11.Location = new System.Drawing.Point(27, 61);
            this.labelControl11.Name = "labelControl11";
            this.labelControl11.Size = new System.Drawing.Size(41, 12);
            this.labelControl11.TabIndex = 21;
            this.labelControl11.Text = "说明：";
            // 
            // labelControl10
            // 
            this.labelControl10.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl10.Appearance.Options.UseFont = true;
            this.labelControl10.Location = new System.Drawing.Point(362, 32);
            this.labelControl10.Name = "labelControl10";
            this.labelControl10.Size = new System.Drawing.Size(12, 12);
            this.labelControl10.TabIndex = 20;
            this.labelControl10.Text = "dB";
            // 
            // labelControl12
            // 
            this.labelControl12.Appearance.ForeColor = System.Drawing.Color.Red;
            this.labelControl12.Appearance.Options.UseForeColor = true;
            this.labelControl12.Location = new System.Drawing.Point(202, 151);
            this.labelControl12.Name = "labelControl12";
            this.labelControl12.Size = new System.Drawing.Size(240, 14);
            this.labelControl12.TabIndex = 18;
            this.labelControl12.Text = "（勾选，则覆盖度内小区需与最强小区同频）";
            // 
            // XtraSetRoadMultiForm
            // 
            this.Appearance.BackColor = System.Drawing.SystemColors.Control;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseBackColor = true;
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(590, 361);
            this.Controls.Add(this.grpNoneMainPnt);
            this.Controls.Add(this.labelControl12);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.chkSaveTestPoint);
            this.Controls.Add(this.cbxCoFreqType);
            this.Controls.Add(this.chkCoFreq);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.simpleButton2);
            this.Controls.Add(this.simpleButton1);
            this.Controls.Add(this.spinEditInvalidThresold);
            this.Controls.Add(this.numRxLevThreshold);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.numRxLevDValue);
            this.Controls.Add(this.labelControl6);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.labelControlBand);
            this.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.Name = "XtraSetRoadMultiForm";
            this.Text = "重叠覆盖度分析条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevThreshold.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxCoFreqType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCoFreq.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSaveTestPoint.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditInvalidThresold.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDiffMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkBandEditDiff.Properties)).EndInit();
            this.grpNoneMainPnt.ResumeLayout(false);
            this.grpNoneMainPnt.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControlBand;
        private DevExpress.XtraEditors.SpinEdit numRxLevDValue;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private System.Windows.Forms.Label label4;
        private DevExpress.XtraEditors.SpinEdit numRxLevThreshold;
        private DevExpress.XtraEditors.SimpleButton simpleButton1;
        private DevExpress.XtraEditors.SimpleButton simpleButton2;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.ComboBoxEdit cbxCoFreqType;
        private DevExpress.XtraEditors.CheckEdit chkCoFreq;
        private DevExpress.XtraEditors.CheckEdit chkSaveTestPoint;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.SpinEdit spinEditInvalidThresold;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.SpinEdit numDiffMin;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.CheckEdit checkBandEditDiff;
        private System.Windows.Forms.GroupBox grpNoneMainPnt;
        private System.Windows.Forms.Label labelControl11;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraEditors.LabelControl labelControl12;
    }
}