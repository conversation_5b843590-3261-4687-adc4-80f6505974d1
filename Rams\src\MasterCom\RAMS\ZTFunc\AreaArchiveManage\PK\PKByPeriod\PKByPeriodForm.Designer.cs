﻿namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    partial class PKByPeriodForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.treeList = new DevExpress.XtraTreeList.TreeList();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.spinEditOffsetY = new DevExpress.XtraEditors.SpinEdit();
            this.label5 = new System.Windows.Forms.Label();
            this.spinEditOffsetX = new DevExpress.XtraEditors.SpinEdit();
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.cbxRank = new System.Windows.Forms.ComboBox();
            this.lbxLegend = new System.Windows.Forms.ListBox();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeList)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditOffsetY.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditOffsetX.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Style3D;
            this.splitContainerControl1.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel2;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.Panel2;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.treeList);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.groupControl2);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(871, 477);
            this.splitContainerControl1.SplitterPosition = 222;
            this.splitContainerControl1.TabIndex = 2;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // treeList
            // 
            this.treeList.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeList.Location = new System.Drawing.Point(0, 0);
            this.treeList.Name = "treeList";
            this.treeList.OptionsView.AutoWidth = false;
            this.treeList.Size = new System.Drawing.Size(639, 473);
            this.treeList.TabIndex = 0;
            this.treeList.NodeCellStyle += new DevExpress.XtraTreeList.GetCustomNodeCellStyleEventHandler(this.treeList_NodeCellStyle);
            this.treeList.DoubleClick += new System.EventHandler(this.treeList_DoubleClick);
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.spinEditOffsetY);
            this.groupControl2.Controls.Add(this.label5);
            this.groupControl2.Controls.Add(this.spinEditOffsetX);
            this.groupControl2.Controls.Add(this.label4);
            this.groupControl2.Controls.Add(this.label3);
            this.groupControl2.Controls.Add(this.label1);
            this.groupControl2.Controls.Add(this.label2);
            this.groupControl2.Controls.Add(this.cbxRank);
            this.groupControl2.Controls.Add(this.lbxLegend);
            this.groupControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl2.Location = new System.Drawing.Point(0, 0);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(222, 473);
            this.groupControl2.TabIndex = 0;
            this.groupControl2.Text = "图例";
            // 
            // spinEditOffsetY
            // 
            this.spinEditOffsetY.EditValue = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.spinEditOffsetY.Location = new System.Drawing.Point(85, 95);
            this.spinEditOffsetY.Name = "spinEditOffsetY";
            this.spinEditOffsetY.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditOffsetY.Properties.Mask.EditMask = "f0";
            this.spinEditOffsetY.Size = new System.Drawing.Size(100, 21);
            this.spinEditOffsetY.TabIndex = 68;
            this.spinEditOffsetY.ValueChanged += new System.EventHandler(this.spinEditOffsetY_ValueChanged);
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.BackColor = System.Drawing.Color.Blue;
            this.label5.Location = new System.Drawing.Point(186, 98);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(19, 14);
            this.label5.TabIndex = 67;
            this.label5.Text = "米";
            // 
            // spinEditOffsetX
            // 
            this.spinEditOffsetX.EditValue = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.spinEditOffsetX.Location = new System.Drawing.Point(85, 68);
            this.spinEditOffsetX.Name = "spinEditOffsetX";
            this.spinEditOffsetX.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditOffsetX.Properties.Mask.EditMask = "f0";
            this.spinEditOffsetX.Size = new System.Drawing.Size(100, 21);
            this.spinEditOffsetX.TabIndex = 68;
            this.spinEditOffsetX.ValueChanged += new System.EventHandler(this.spinEditOffsetX_ValueChanged);
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.BackColor = System.Drawing.Color.Blue;
            this.label4.Location = new System.Drawing.Point(10, 98);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(75, 14);
            this.label4.TabIndex = 67;
            this.label4.Text = "栅格Y偏移：";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.BackColor = System.Drawing.Color.Pink;
            this.label3.Location = new System.Drawing.Point(186, 71);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(19, 14);
            this.label3.TabIndex = 67;
            this.label3.Text = "米";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.BackColor = System.Drawing.Color.Pink;
            this.label1.Location = new System.Drawing.Point(10, 71);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(74, 14);
            this.label1.TabIndex = 67;
            this.label1.Text = "栅格X偏移：";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(12, 39);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(67, 14);
            this.label2.TabIndex = 67;
            this.label2.Text = "渲染级别：";
            // 
            // cbxRank
            // 
            this.cbxRank.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.cbxRank.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxRank.FormattingEnabled = true;
            this.cbxRank.Location = new System.Drawing.Point(85, 35);
            this.cbxRank.Name = "cbxRank";
            this.cbxRank.Size = new System.Drawing.Size(125, 22);
            this.cbxRank.TabIndex = 66;
            this.cbxRank.SelectedIndexChanged += new System.EventHandler(this.cbxRank_SelectedIndexChanged);
            // 
            // lbxLegend
            // 
            this.lbxLegend.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.lbxLegend.DrawMode = System.Windows.Forms.DrawMode.OwnerDrawFixed;
            this.lbxLegend.FormattingEnabled = true;
            this.lbxLegend.IntegralHeight = false;
            this.lbxLegend.ItemHeight = 18;
            this.lbxLegend.Location = new System.Drawing.Point(5, 122);
            this.lbxLegend.Name = "lbxLegend";
            this.lbxLegend.SelectionMode = System.Windows.Forms.SelectionMode.None;
            this.lbxLegend.Size = new System.Drawing.Size(214, 346);
            this.lbxLegend.TabIndex = 65;
            // 
            // PKByPeriodForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(871, 477);
            this.Controls.Add(this.splitContainerControl1);
            this.Name = "PKByPeriodForm";
            this.Text = "不同时段竞比结果";
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.treeList)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.groupControl2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditOffsetY.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditOffsetX.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraTreeList.TreeList treeList;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.ComboBox cbxRank;
        private System.Windows.Forms.ListBox lbxLegend;
        private DevExpress.XtraEditors.SpinEdit spinEditOffsetX;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.SpinEdit spinEditOffsetY;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label4;
    }
}