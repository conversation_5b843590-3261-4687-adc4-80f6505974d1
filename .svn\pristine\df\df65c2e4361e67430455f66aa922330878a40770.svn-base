﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class TDPccpchRscpWeakCoverConditionDlg : BaseForm
    {
        public TDPccpchRscpWeakCoverConditionDlg()
        {
            InitializeComponent();
        }

        public ZTWeakCovRoadCondition_TD GetSetCondition()
        {
            ZTWeakCovRoadCondition_TD weakCovRoadCond = new ZTWeakCovRoadCondition_TD();
            weakCovRoadCond.rscpThreshold = (int)numPccpchRscpThreshold.Value;
            weakCovRoadCond.pcchC_iThreshold = (int)numPccpchC_I.Value;
            weakCovRoadCond.dpchC_iThreshold = (int)numDpchC_I.Value;
            weakCovRoadCond.checkPccpchC2I = checkPccpchC2I.Checked;
            weakCovRoadCond.checkDpchC2I = checkDpchC2I.Checked;
            weakCovRoadCond.roadDistance = (int)numDistance.Value;
            weakCovRoadCond.sampleDistance = (int)numMaxDistance.Value;
            weakCovRoadCond.sampleCellDistance = (int)numSampleCellDistance.Value;
            weakCovRoadCond.sampleCellAngle = (int)numSampleCellAngle.Value;
            weakCovRoadCond.checkNCellRscp = chbNCellRSCP.Checked;
            weakCovRoadCond.ncellRscpMax = (int)numNCellRSCP.Value;
            return weakCovRoadCond;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void checkPccpchC2I_CheckedChanged(object sender, EventArgs e)
        {
            numPccpchC_I.Enabled = checkPccpchC2I.Checked;
        }

        private void checkDpchC2I_CheckedChanged(object sender, EventArgs e)
        {
            numDpchC_I.Enabled = checkDpchC2I.Checked;
        }

        public bool CheckPccpchC2I
        {
            get { return checkPccpchC2I.Checked; }
        }

        public bool CheckDpchC2I
        {
            get { return checkDpchC2I.Checked; }
        }

        private void chbNCellRSCP_CheckedChanged(object sender, EventArgs e)
        {
            numNCellRSCP.Enabled = chbNCellRSCP.Checked;
        }

       
    }
}
