﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.ES.Data;
using MasterCom.RAMS.Model;

namespace MasterCom.ES.Core
{
    public class DataGatherProxy
    {
      
        private static DataGatherProxy proxy = null;
        private readonly Dictionary<string, DataProvider> dataProviderDic = new Dictionary<string, DataProvider>();
        private readonly ResvStore pubResvStore = new ResvStore();
        public DataGatherProxy()
        {
            initProxy();
        }
        public static DataGatherProxy GetInstance()
        {
            if(proxy==null)
            {
                proxy = new DataGatherProxy();
            }
            return proxy;
        }
        public ResvStore PublicResvStore
        {
            get 
            {
                return pubResvStore;
            }
        }
        public Dictionary<string,DataProvider> DataProviderDic
        {
            get
            {
                return dataProviderDic;
            }
        }
        public DataProvider GetProviderByName(string provName)
        {
            DataProvider prov = null;
            if (dataProviderDic.TryGetValue(provName, out prov))
            {
                return prov;
            }
            throw (new Exception("Can't find " + provName));
        }
        public void processFunction(string provName,string functionName,string paramStr,out object outRet)
        {
            DataProvider prov = null;
            if(dataProviderDic.TryGetValue(provName,out prov))
            {
                prov.processFunc(functionName,paramStr,out outRet);
            }
            else
            {
                throw (new Exception("Can't find " + provName));
            }
        }
        public void fireSetData(object obj)
        {
            foreach(DataProvider dp in dataProviderDic.Values)
            {
                dp.fireDataFill(obj);
            }
        }

        private void initProxy()
        {
            dataProviderDic.Clear();
            DTDataProvider dtProv = DTDataProvider.GetDTProviderInstance();
            dataProviderDic[dtProv.Name] = dtProv;
            CfgDataProvider cfgProv = new CfgDataProvider();
            dataProviderDic[cfgProv.Name] = cfgProv;
        }
    }
}
