﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class FRFrequencyConVerifyByFile : FRFrequencyConVerifyByRegion
    {
        private FRFrequencyConVerifyByFile()
            :base()
        {
        }

        private static FRFrequencyConVerifyByFile intance = null;
        public static new FRFrequencyConVerifyByFile GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new FRFrequencyConVerifyByFile();
                    }
                }
            }
            return intance;
        }
        public override string Name
        {
            get { return "FR频点配置核查分析(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }

    public class FRFrequencyConVerifyByFile_FDD : FRFrequencyConVerifyByRegion_FDD
    {
        private static FRFrequencyConVerifyByFile_FDD instance = null;
        public static new FRFrequencyConVerifyByFile_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new FRFrequencyConVerifyByFile_FDD();
                    }
                }
            }
            return instance;
        }
        protected FRFrequencyConVerifyByFile_FDD()
            : base()
        {

        }
        public override string Name
        {
            get { return "LTE_FDD FR频点配置核查分析(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }
}
