﻿
namespace MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage.HighReverseFlowCoverageImport
{
    partial class HighReverseFlowCoverageImportDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.importDataPanel = new MasterCom.RAMS.Func.ImportDataPanel();
            this.dataBaseConnection = new MasterCom.RAMS.Func.DataBaseConnection();
            this.SuspendLayout();
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(395, 313);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(89, 27);
            this.btnCancel.TabIndex = 38;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Location = new System.Drawing.Point(300, 313);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(89, 27);
            this.btnOK.TabIndex = 37;
            this.btnOK.Text = "确认";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // importDataPanel
            // 
            this.importDataPanel.Location = new System.Drawing.Point(1, 6);
            this.importDataPanel.Name = "importDataPanel";
            this.importDataPanel.Size = new System.Drawing.Size(520, 89);
            this.importDataPanel.TabIndex = 39;
            // 
            // dataBaseConnection
            // 
            this.dataBaseConnection.Location = new System.Drawing.Point(0, 96);
            this.dataBaseConnection.Name = "dataBaseConnection";
            this.dataBaseConnection.Size = new System.Drawing.Size(520, 200);
            this.dataBaseConnection.TabIndex = 40;
            // 
            // HighReverseFlowCoverageImportDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(520, 354);
            this.Controls.Add(this.dataBaseConnection);
            this.Controls.Add(this.importDataPanel);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Name = "HighReverseFlowCoverageImportDlg";
            this.Text = "高倒流共覆盖工参导入";
            this.ResumeLayout(false);

        }

        #endregion
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private Func.ImportDataPanel importDataPanel;
        private Func.DataBaseConnection dataBaseConnection;
    }
}