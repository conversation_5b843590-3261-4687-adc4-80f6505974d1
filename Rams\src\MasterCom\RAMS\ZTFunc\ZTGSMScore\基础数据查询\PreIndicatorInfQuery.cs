﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System.Globalization;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 性能指标数据查询
    /// </summary>
    class PreIndicatorInfQuery: DIYSQLBase
    {
        private readonly bool IsTDIn = false;
        public DateTime TimeStart { get; set; }
        public DateTime TimeEnd { get; set; }
        public PreIndicatorInfQuery(MainModel mainModel, bool IsTDIn)
            : base(mainModel)
        {
            this.IsTDIn = IsTDIn;
            TimeStart = DateTime.MaxValue;
            TimeEnd = DateTime.MaxValue;
        }

        protected override string getSqlTextString()
        {
            //this.SetQueryCondition(
            if (TimeStart == DateTime.MaxValue && TimeEnd == DateTime.MaxValue)
            {
                TimePeriod TimePeriod = condition.Periods[0];
                TimeStart = TimePeriod.BeginTime;
                TimeEnd = TimePeriod.EndTime;
            }
            
            //MainModel.
            if (IsTDIn)
            {
                return "exec sp_para_utrancell_score_qry '"+TimeStart.ToString("yyyy-MM-dd 00:00:00")
                    + "' , '" + TimeEnd.ToString("yyyy-MM-dd 00:00:00")+"'";
            }
            else
            {
                return "exec sp_para_cell_score_qry '" + TimeStart.ToString("yyyy-MM-dd 00:00:00")
                     + "' , '" + TimeEnd.ToString("yyyy-MM-dd 00:00:00") + "'"; 
            }
        }

        
        public List<Indicator> ScoreList { get; set; } = new List<Indicator>();

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[2];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_Float;
            return rType;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            ScoreList = new List<Indicator>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    try
                    {
                        Indicator indicator = new Indicator();
                        indicator.Name = package.Content.GetParamString();
                        indicator.KPI = package.Content.GetParamFloat();
                        ScoreList.Add(indicator);
                    }
                    catch
                    {
                        //continue
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgress(ref index, ref progress);
            }
            GetIndicatorThresholdInf();
        }

        private static void setProgress(ref int index, ref int progress)
        {
            if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
            {
                progress++;
                if (progress > 95)
                {
                    progress = 5;
                    index = 0;
                }
                if (progress % 5 == 0)
                {
                    WaitBox.ProgressPercent = progress;
                }
            }
        }

        private void GetIndicatorThresholdInf()
        {
            if (ScoreList != null)
            {
                IndicatorThresholdInfProcessor thresholdProcessor = IndicatorThresholdInfProcessor.Ini();
                for (int i = 0; i < ScoreList.Count; i++)
                {
                    if (IsTDIn)
                    {
                        setTdScore(thresholdProcessor, i);
                    }
                    else
                    {
                        setGsmScore(thresholdProcessor, i);
                    }
                }
            }
        }

        private void setTdScore(IndicatorThresholdInfProcessor thresholdProcessor, int i)
        {
            for (int j = 0; j < thresholdProcessor.TDPerformanceThreadholdInf.ThresholdInf.Length; j++)
            {
                if (ScoreList[i].Name == thresholdProcessor.TDPerformanceThreadholdInf.ThresholdInf[j].Name)
                {
                    ScoreList[i].GetScore(thresholdProcessor.TDPerformanceThreadholdInf.ThresholdInf[j]);
                    break;
                }
            }
        }

        private void setGsmScore(IndicatorThresholdInfProcessor thresholdProcessor, int i)
        {
            for (int j = 0; j < thresholdProcessor.GSMPerformanceThreadholdInf.ThresholdInf.Length; j++)
            {
                if (ScoreList[i].Name == thresholdProcessor.GSMPerformanceThreadholdInf.ThresholdInf[j].Name)
                {
                    ScoreList[i].GetScore(thresholdProcessor.GSMPerformanceThreadholdInf.ThresholdInf[j]);
                    break;
                }
            }
        }

        public override string Name
        {
            get { return "ScoreQuery"; }
        }
    }
}
