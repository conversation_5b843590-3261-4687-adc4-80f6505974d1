﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    public partial class WrongCoverPnl : UserControl
    {
        public WrongCoverPnl()
        {
            InitializeComponent();
        }

        WrongCover mainReason = null;
        public void LinkCondition(WrongCover reason)
        {
            this.mainReason = reason;
            numDir.Value = mainReason.Angle;
            numDir.ValueChanged += numDir_ValueChanged;
        }

        void numDir_ValueChanged(object sender, EventArgs e)
        {
            mainReason.Angle = (int)numDir.Value;
        }

    }
}
