<?xml version="1.0" encoding="UTF-8"?>
<Configs>
	<Config name="StatParamCfg">
		<Item name="configs" typeName="IList">
			<Item typeName="IDictionary">
				<Item typeName="String" key="Name">CDMA参数</Item>
				<Item typeName="String" key="FName"/>
				<Item typeName="IList" key="children">
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">CDMA语音业务参数</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">测试时长 cxDuration</Item>
								<Item typeName="String" key="FName">cxDuration</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">测试距离 cxDistance</Item>
								<Item typeName="String" key="FName">cxDistance</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">采样点总数 cxSampleTotle</Item>
								<Item typeName="String" key="FName">cxSampleTotle</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Ec_Io</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">Ec_Io[-32,-15]的数目 cxEcIo[0]</Item>
										<Item typeName="String" key="FName">cxEcIo</Item>
										<Item typeName="Int32" key="FTag">0</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">Ec_Io(-15,-12]的数目 cxEcIo[1]</Item>
										<Item typeName="String" key="FName">cxEcIo</Item>
										<Item typeName="Int32" key="FTag">1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">Ec_Io(-12,-9]的数目 cxEcIo[2]</Item>
										<Item typeName="String" key="FName">cxEcIo</Item>
										<Item typeName="Int32" key="FTag">2</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">Ec_Io(-9,-6]的数目 cxEcIo[3]</Item>
										<Item typeName="String" key="FName">cxEcIo</Item>
										<Item typeName="Int32" key="FTag">3</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">Ec_Io(-6,0]的数目 cxEcIo[4]</Item>
										<Item typeName="String" key="FName">cxEcIo</Item>
										<Item typeName="Int32" key="FTag">4</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">Ec_Io[-32,-15]的累计值 cxEcIoTotal[0]</Item>
										<Item typeName="String" key="FName">cxEcIoTotal</Item>
										<Item typeName="Int32" key="FTag">0</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">Ec_Io(-15,-12]的累计值 cxEcIoTotal[1]</Item>
										<Item typeName="String" key="FName">cxEcIoTotal</Item>
										<Item typeName="Int32" key="FTag">1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">Ec_Io(-12,-9]的累计值 cxEcIoTotal[2]</Item>
										<Item typeName="String" key="FName">cxEcIoTotal</Item>
										<Item typeName="Int32" key="FTag">2</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">Ec_Io(-9,-6]的累计值 cxEcIoTotal[3]</Item>
										<Item typeName="String" key="FName">cxEcIoTotal</Item>
										<Item typeName="Int32" key="FTag">3</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">Ec_Io(-6,0]的累计值 cxEcIoTotal[4]</Item>
										<Item typeName="String" key="FName">cxEcIoTotal</Item>
										<Item typeName="Int32" key="FTag">4</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">FFER</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">FFER[0,1]的数目 cxFFER[0]</Item>
										<Item typeName="String" key="FName">cxFFER</Item>
										<Item typeName="Int32" key="FTag">0</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">FFER(1,2]的数目 cxFFER[1]</Item>
										<Item typeName="String" key="FName">cxFFER</Item>
										<Item typeName="Int32" key="FTag">1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">FFER(2,3]的数目 cxFFER[2]</Item>
										<Item typeName="String" key="FName">cxFFER</Item>
										<Item typeName="Int32" key="FTag">2</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">FFER(3,4]的数目 cxFFER[3]</Item>
										<Item typeName="String" key="FName">cxFFER</Item>
										<Item typeName="Int32" key="FTag">3</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">FFER(4,5]的数目 cxFFER[4]</Item>
										<Item typeName="String" key="FName">cxFFER</Item>
										<Item typeName="Int32" key="FTag">4</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">FFER(5,6]的数目 cxFFER[5]</Item>
										<Item typeName="String" key="FName">cxFFER</Item>
										<Item typeName="Int32" key="FTag">5</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">FFER(6,7]的数目 cxFFER[6]</Item>
										<Item typeName="String" key="FName">cxFFER</Item>
										<Item typeName="Int32" key="FTag">6</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">FFER(7,8]的数目 cxFFER[7]</Item>
										<Item typeName="String" key="FName">cxFFER</Item>
										<Item typeName="Int32" key="FTag">7</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">FFER(8,9]的数目 cxFFER[8]</Item>
										<Item typeName="String" key="FName">cxFFER</Item>
										<Item typeName="Int32" key="FTag">8</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">FFER(9,10]的数目 cxFFER[9]</Item>
										<Item typeName="String" key="FName">cxFFER</Item>
										<Item typeName="Int32" key="FTag">9</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">FFER(10,100]的数目 cxFFER[10]</Item>
										<Item typeName="String" key="FName">cxFFER</Item>
										<Item typeName="Int32" key="FTag">10</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">FFER[0,1]的累计值 cxFFERTotal[0]</Item>
										<Item typeName="String" key="FName">cxFFERTotal</Item>
										<Item typeName="Int32" key="FTag">0</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">FFER(1,2]的累计值 cxFFERTotal[1]</Item>
										<Item typeName="String" key="FName">cxFFERTotal</Item>
										<Item typeName="Int32" key="FTag">1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">FFER(2,3]的累计值 cxFFERTotal[2]</Item>
										<Item typeName="String" key="FName">cxFFERTotal</Item>
										<Item typeName="Int32" key="FTag">2</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">FFER(3,4]的累计值 cxFFERTotal[3]</Item>
										<Item typeName="String" key="FName">cxFFERTotal</Item>
										<Item typeName="Int32" key="FTag">3</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">FFER(4,5]的累计值 cxFFERTotal[4]</Item>
										<Item typeName="String" key="FName">cxFFERTotal</Item>
										<Item typeName="Int32" key="FTag">4</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">FFER(5,6]的累计值 cxFFERTotal[5]</Item>
										<Item typeName="String" key="FName">cxFFERTotal</Item>
										<Item typeName="Int32" key="FTag">5</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">FFER(6,7]的累计值 cxFFERTotal[6]</Item>
										<Item typeName="String" key="FName">cxFFERTotal</Item>
										<Item typeName="Int32" key="FTag">6</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">FFER(7,8]的累计值 cxFFERTotal[7]</Item>
										<Item typeName="String" key="FName">cxFFERTotal</Item>
										<Item typeName="Int32" key="FTag">7</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">FFER(8,9]的累计值 cxFFERTotal[8]</Item>
										<Item typeName="String" key="FName">cxFFERTotal</Item>
										<Item typeName="Int32" key="FTag">8</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">FFER(9,10]的累计值 cxFFERTotal[9]</Item>
										<Item typeName="String" key="FName">cxFFERTotal</Item>
										<Item typeName="Int32" key="FTag">9</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">FFER(10,100]的累计值 cxFFERTotal[10]</Item>
										<Item typeName="String" key="FName">cxFFERTotal</Item>
										<Item typeName="Int32" key="FTag">10</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TxPower</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TxPower[-127,-20]的数目 cxTxPower[0]</Item>
										<Item typeName="String" key="FName">cxTxPower</Item>
										<Item typeName="Int32" key="FTag">0</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TxPower(-20,-15]的数目 cxTxPower[1]</Item>
										<Item typeName="String" key="FName">cxTxPower</Item>
										<Item typeName="Int32" key="FTag">1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TxPower(-15,-10]的数目 cxTxPower[2]</Item>
										<Item typeName="String" key="FName">cxTxPower</Item>
										<Item typeName="Int32" key="FTag">2</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TxPower(-10,-5]的数目 cxTxPower[3]</Item>
										<Item typeName="String" key="FName">cxTxPower</Item>
										<Item typeName="Int32" key="FTag">3</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TxPower(-5,0]的数目 cxTxPower[4]</Item>
										<Item typeName="String" key="FName">cxTxPower</Item>
										<Item typeName="Int32" key="FTag">4</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TxPower(0,5]的数目 cxTxPower[5]</Item>
										<Item typeName="String" key="FName">cxTxPower</Item>
										<Item typeName="Int32" key="FTag">5</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TxPower(5,10]的数目 cxTxPower[6]</Item>
										<Item typeName="String" key="FName">cxTxPower</Item>
										<Item typeName="Int32" key="FTag">6</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TxPower(10,15]的数目 cxTxPower[7]</Item>
										<Item typeName="String" key="FName">cxTxPower</Item>
										<Item typeName="Int32" key="FTag">7</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TxPower(15,20]的数目 cxTxPower[8]</Item>
										<Item typeName="String" key="FName">cxTxPower</Item>
										<Item typeName="Int32" key="FTag">8</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TxPower(20,36]的数目 cxTxPower[9]</Item>
										<Item typeName="String" key="FName">cxTxPower</Item>
										<Item typeName="Int32" key="FTag">9</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TxPower[-127,-20]的累计值 cxTxPowerTotal[0]</Item>
										<Item typeName="String" key="FName">cxTxPowerTotal</Item>
										<Item typeName="Int32" key="FTag">0</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TxPower(-20,-15]的累计值 cxTxPowerTotal[1]</Item>
										<Item typeName="String" key="FName">cxTxPowerTotal</Item>
										<Item typeName="Int32" key="FTag">1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TxPower(-15,-10]的累计值 cxTxPowerTotal[2]</Item>
										<Item typeName="String" key="FName">cxTxPowerTotal</Item>
										<Item typeName="Int32" key="FTag">2</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TxPower(-10,-5]的累计值 cxTxPowerTotal[3]</Item>
										<Item typeName="String" key="FName">cxTxPowerTotal</Item>
										<Item typeName="Int32" key="FTag">3</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TxPower(-5,0]的累计值 cxTxPowerTotal[4]</Item>
										<Item typeName="String" key="FName">cxTxPowerTotal</Item>
										<Item typeName="Int32" key="FTag">4</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TxPower(0,5]的累计值 cxTxPowerTotal[5]</Item>
										<Item typeName="String" key="FName">cxTxPowerTotal</Item>
										<Item typeName="Int32" key="FTag">5</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TxPower(5,10]的累计值 cxTxPowerTotal[6]</Item>
										<Item typeName="String" key="FName">cxTxPowerTotal</Item>
										<Item typeName="Int32" key="FTag">6</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TxPower(10,15]的累计值 cxTxPowerTotal[7]</Item>
										<Item typeName="String" key="FName">cxTxPowerTotal</Item>
										<Item typeName="Int32" key="FTag">7</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TxPower(15,20]的累计值 cxTxPowerTotal[8]</Item>
										<Item typeName="String" key="FName">cxTxPowerTotal</Item>
										<Item typeName="Int32" key="FTag">8</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">TxPower(20,36]的累计值 cxTxPowerTotal[9]</Item>
										<Item typeName="String" key="FName">cxTxPowerTotal</Item>
										<Item typeName="Int32" key="FTag">9</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RxPower</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">RXPower[-120,-94]的数目 cxRxPower[0]</Item>
										<Item typeName="String" key="FName">cxRxPower</Item>
										<Item typeName="Int32" key="FTag">0</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">RXPower(-94,-90]的数目 cxRxPower[1]</Item>
										<Item typeName="String" key="FName">cxRxPower</Item>
										<Item typeName="Int32" key="FTag">1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">RXPower(-90,-85]的数目 cxRxPower[2]</Item>
										<Item typeName="String" key="FName">cxRxPower</Item>
										<Item typeName="Int32" key="FTag">2</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">RXPower(-85,-80]的数目 cxRxPower[3]</Item>
										<Item typeName="String" key="FName">cxRxPower</Item>
										<Item typeName="Int32" key="FTag">3</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">RXPower(-80,-75]的数目 cxRxPower[4]</Item>
										<Item typeName="String" key="FName">cxRxPower</Item>
										<Item typeName="Int32" key="FTag">4</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">RXPower(-75,-65]的数目 cxRxPower[5]</Item>
										<Item typeName="String" key="FName">cxRxPower</Item>
										<Item typeName="Int32" key="FTag">5</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">RXPower(-65,10]的数目 cxRxPower[6]</Item>
										<Item typeName="String" key="FName">cxRxPower</Item>
										<Item typeName="Int32" key="FTag">6</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">RXPower[-120,-94]的累计值 cxRxPowerTotal[0]</Item>
										<Item typeName="String" key="FName">cxRxPowerTotal</Item>
										<Item typeName="Int32" key="FTag">0</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">RXPower(-94,-90]的累计值 cxRxPowerTotal[1]</Item>
										<Item typeName="String" key="FName">cxRxPowerTotal</Item>
										<Item typeName="Int32" key="FTag">1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">RXPower(-90,-85]的累计值 cxRxPowerTotal[2]</Item>
										<Item typeName="String" key="FName">cxRxPowerTotal</Item>
										<Item typeName="Int32" key="FTag">2</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">RXPower(-85,-80]的累计值 cxRxPowerTotal[3]</Item>
										<Item typeName="String" key="FName">cxRxPowerTotal</Item>
										<Item typeName="Int32" key="FTag">3</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">RXPower(-80,-75]的累计值 cxRxPowerTotal[4]</Item>
										<Item typeName="String" key="FName">cxRxPowerTotal</Item>
										<Item typeName="Int32" key="FTag">4</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">RXPower(-75,-65]的累计值 cxRxPowerTotal[5]</Item>
										<Item typeName="String" key="FName">cxRxPowerTotal</Item>
										<Item typeName="Int32" key="FTag">5</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">RXPower(-65,10]的累计值 cxRxPowerTotal[6]</Item>
										<Item typeName="String" key="FName">cxRxPowerTotal</Item>
										<Item typeName="Int32" key="FTag">6</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Ec_Io大于-12且TxPower小于等于15且RxPower>=-90 cxEcIoTxRx90</Item>
								<Item typeName="String" key="FName">cxEcIoTxRx90</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Ec_Io大于-12且TxPower小于等于15且RxPower>=-95 cxEcIoTxRx95</Item>
								<Item typeName="String" key="FName">cxEcIoTxRx95</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Ec_Io最大值 cxEcIoMax</Item>
								<Item typeName="String" key="FName">cxEcIoMax</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Ec_Io最小值 cxEcIoMin</Item>
								<Item typeName="String" key="FName">cxEcIoMin</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">FFER最大值 cxFFERMax</Item>
								<Item typeName="String" key="FName">cxFFERMax</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">FFER最小值 cxFFERMin</Item>
								<Item typeName="String" key="FName">cxFFERMin</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TxPower最大值 cxTxPowerMax</Item>
								<Item typeName="String" key="FName">cxTxPowerMax</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TxPower最小值 cxTxPowerMin</Item>
								<Item typeName="String" key="FName">cxTxPowerMin</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RxPower最大值 cxRxPowerMax</Item>
								<Item typeName="String" key="FName">cxRxPowerMax</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RxPower最小值 cxRxPowerMin</Item>
								<Item typeName="String" key="FName">cxRxPowerMin</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PesqLQ数量 cxPesqLQNum</Item>
								<Item typeName="String" key="FName">cxPesqLQNum</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PesqLQ累计值 cxPesqLQTotal</Item>
								<Item typeName="String" key="FName">cxPesqLQTotal</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PesqLQ最大值 cxPesqLQMax</Item>
								<Item typeName="String" key="FName">cxPesqLQMax</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PesqLQ最小值 cxPesqLQMin</Item>
								<Item typeName="String" key="FName">cxPesqLQMin</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">FTP下载采样点数 cxFtpSampleNum</Item>
								<Item typeName="String" key="FName">cxFtpSampleNum</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">FTP下载采样点总和 cxFtpTotle</Item>
								<Item typeName="String" key="FName">cxFtpTotle</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">CDMA数据业务参数</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">CDMA测试采样点数</Item>
								<Item typeName="String" key="FName">gSampleTotleCDMA</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">CDMA测试时长</Item>
								<Item typeName="String" key="FName">gDurationCDMA</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">CDMA测试距离</Item>
								<Item typeName="String" key="FName">gDistanceCDMA</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Rxlev</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">Ec_Io大于-12且TxPower小于等于15且RxPower>=-90 CDMA_DT_CDMA_COVERNNUM12_15_F90</Item>
										<Item typeName="String" key="FName">Gx_02070233</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">Ec_Io大于-12且TxPower小于等于15且RxPower>=-95 CDMA_DT_CDMA_COVERNUM12_15_F95</Item>
										<Item typeName="String" key="FName">Gx_02070234</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_DT_CDMA_COVERNUM</Item>
										<Item typeName="String" key="FName">Gx_02070235</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RLC</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_DT_CDMA1X_RLC_DL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_03070601</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_DT_CDMA1X_RLC_DL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_03070602</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_DT_CDMA1X_RLC_UL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_03070603</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_DT_CDMA1X_RLC_UL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_03070604</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BLER</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_DT_CDMA1X_RLC_BLER_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_04070601</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_DT_CDMA1X_RLC_BLER_SUM</Item>
										<Item typeName="String" key="FName">Gx_04070602</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_DT_CDMA1X_RLC_BLER_MAX</Item>
										<Item typeName="String" key="FName">Gx_04070603</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_DT_CDMA1X_RLC_BLER_MIN</Item>
										<Item typeName="String" key="FName">Gx_04070604</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">APP</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_CDMA1X_FTP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050701060201</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_CDMA1X_FTP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050701060202</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_CDMA1X_FTP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050701060203</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_CDMA1X_FTP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050701060204</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_CDMA1X_WAP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050701060601</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_CDMA1X_WAP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050701060602</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_CDMA1X_WAP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050701060603</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_CDMA1X_WAP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050701060604</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_CDMA1X_Http_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050701060C01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_CDMA1X_Http_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050701060C02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_CDMA1X_Http_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050701060C03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_CDMA1X_Http_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050701060C04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_CDMA1X_FTP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050702060301</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_CDMA1X_FTP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050702060302</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_CDMA1X_FTP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050702060303</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_CDMA1X_FTP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050702060304</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_CDMA1X_WAP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050702060701</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_CDMA1X_WAP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050702060702</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_CDMA1X_WAP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050702060703</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_CDMA1X_WAP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050702060704</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_CDMA1X_Http_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050702060D01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_CDMA1X_Http_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050702060D02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_CDMA1X_Http_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050702060D03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">CDMA_CDMA1X_Http_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050702060D04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">EVDO数据业务参数</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">EVDO测试采样点数</Item>
								<Item typeName="String" key="FName">gSampleTotleEVDO</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">EVDO测试时长</Item>
								<Item typeName="String" key="FName">gDurationEVDO</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">EVDO测试距离</Item>
								<Item typeName="String" key="FName">gDistanceEVDO</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">CDMA事件参数</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">CDMA MO Call Attempt 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">300</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">CDMA MT Call Attempt 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">301</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">CDMA Call Attempt Retry 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">302</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">CDMA MO Call ServiceConnect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">303</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">CDMA MT Call ServiceConnect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">304</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">CDMA MO Drop Call 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">305</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">CDMA MT Drop Call 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">306</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">CDMA MO Call Fail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">307</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">CDMA MT Call Fail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">308</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">CDMA MO Block Call 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">309</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">CDMA MT Block Call 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">310</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">CDMA MO Call End 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">311</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">CDMA MT Call End 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">312</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">CDMA MT Call Connect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">313</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">CDMA MT Call Alerting 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">314</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">CDMA SoftHandover command 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">315</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">CDMA SoftHandover Success 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">316</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">CDMA SoftHandover Failure 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">317</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">CDMA Cell Reselection 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">318</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">CDMA Weak Coverage 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">319</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">CDMA Weak Quality 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">320</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
				</Item>
			</Item>
		</Item>
	</Config>
</Configs>
