﻿using MapWinGIS;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTWirelessNetTest
{
    public class WirelessNetTestInject
    {
        private static WirelessNetTestInject instance = null;
        public static WirelessNetTestInject Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new WirelessNetTestInject();
                }
                return instance;
            }
        }

        public bool initInjectGrid<T>(int districtID, TimePeriod period, Shape shape, List<T> files
            , GetFileID<T> func)
        {
            Grid.GridMatrix<InjectGridUnit> injectGridMatrix = null;
            QueryCondition cond = new QueryCondition();
            cond.DistrictID = districtID;
            cond.DistrictIDs = new List<int>() { districtID };
            cond.Areas = new Dictionary<int, List<int>>();
            cond.Periods.Add(period);
            cond.CarrierTypes.Add(1);
            cond.NameFilterType = FileFilterType.ByMark_ID;
            cond.Geometorys = new SearchGeometrys();
            cond.Geometorys.Region = shape;

            int curIdx = 0;
            while (curIdx < files.Count)//分批次发送包
            {
                StringBuilder strb = getCurFiles(files, ref curIdx, func);
                string fileIDs = strb.ToString().TrimEnd(',');
                cond.FileName = fileIDs;

                DiyQueryInjectGrid query = new DiyQueryInjectGrid(districtID);
                query.SetQueryCondition(cond);
                query.Query();
                if (injectGridMatrix == null)
                {
                    injectGridMatrix = query.injectGridMatrix;
                }
                else
                {
                    mergeInjectGridMatrix(injectGridMatrix, query.injectGridMatrix);
                }
            }
            bool isValid = StreetInjectHelper.Init(injectGridMatrix, true);
            return isValid;
        }

        private StringBuilder getCurFiles<T>(List<T> files, ref int curIdx, GetFileID<T> func)
        {
            StringBuilder strb = new StringBuilder();
            for (; curIdx < files.Count; curIdx++)
            {
                string curStr = func(files[curIdx]);
                int bufferLength = Encoding.Default.GetBytes(strb + curStr).Length;
                if (bufferLength > 4000)
                {
                    break;
                }
                strb.Append(curStr + ",");
            }

            return strb;
        }

        public delegate string GetFileID<in T>(T data);

        private void mergeInjectGridMatrix(Grid.GridMatrix<InjectGridUnit> injectGridMatrix, Grid.GridMatrix<InjectGridUnit> queryInjectGridMatrix)
        {
            foreach (var item in queryInjectGridMatrix)
            {
                InjectGridUnit igu = injectGridMatrix[item.RowIdx, item.ColIdx];
                if (igu == null)
                {
                    igu = new InjectGridUnit();
                    igu.LTLng = item.LTLng;
                    igu.LTLat = item.LTLat;
                    injectGridMatrix[item.RowIdx, item.ColIdx] = igu;
                    igu.Status++;
                }
                igu.repeatCount++;
                foreach (var repeat in item.fileRepeatDic)
                {
                    if (!igu.fileRepeatDic.ContainsKey(repeat.Key))
                        igu.fileRepeatDic.Add(repeat.Key, repeat.Value);
                }
                igu.TestDistance += item.TestDistance;
                igu.TestDuration += item.TestDuration;
            }
        }

        public StreetInjectInfoTotal GetInjectDatByShp(ResvRegion region, string sceneName
            , Dictionary<string, Shapefile> roadShpNames)
        {
            var regionInjectDic = new Dictionary<string, StreetInjectInfoTotal>();
            var regionTotal = new StreetInjectInfoTotal();
            regionTotal.RegionName = "汇总";

            List<string> shpList = new List<string>(roadShpNames.Keys);
            foreach (string shp in shpList)
            {
                Shapefile streetTable = new Shapefile();
                if (roadShpNames[shp] == null)
                {
                    streetTable.Open(shp, null);
                    roadShpNames[shp] = streetTable;
                }
                else
                {
                    streetTable = roadShpNames[shp];
                }

                //每个道路图层计算渗透率
                var coveredGridDic = new Dictionary<InjectGridUnit, int>();

                List<StreetInjectInfo> resList = StreetInjectHelper.ParseStreetsOfTable(streetTable, region.GeoOp, "Name", "", sceneName, ref coveredGridDic);

                var regionMileageInfoDic = new Dictionary<string, RegionMileageInfo>();

                StreetInjectHelper.DealGridInfo(region, coveredGridDic, regionMileageInfoDic);

                foreach (StreetInjectInfo res in resList)
                {
                    StreetInjectHelper.AddRegionInject(res, regionInjectDic, regionTotal, regionMileageInfoDic);
                }
            }

            return regionTotal;
        }
    }
}
