using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public class Repeater : Snapshot<Repeater>
    {
        public Repeater()
        {
            Value = this;
        }

        public string Name { get; set; }

        public double Longitude { get; set; }

        public double Latitude { get; set; }

        public BTSBandType BandType { get; set; }

        public string Address { get; set; }

        public int DonarCellID { get; set; }

        public List<Cell> DonarCells { get; set; } = new List<Cell>();

        public Cell CurrentDonarCell
        {
            get
            {
                foreach (Cell cell in DonarCells)
                {
                    if (cell == cell.Current)
                    {
                        return cell;
                    }
                }
                return null;
            }
        }

        public Cell GetDonarCellByTime(DateTime datetime)
        {
            foreach (Cell cell in DonarCells)
            {
                if (cell == cell.Get(datetime))
                {
                    return cell;
                }
            }
            return null;
        }

        public string DetailInfo
        {
            get
            {
                StringBuilder info = new StringBuilder();
                info.Append("Name:").Append(Name);
                info.Append("\r\nLongitude:").Append(Longitude);
                info.Append("\r\nLatitude:").Append(Latitude);
                info.Append("\r\nBandType:").Append(BandType.ToString());
                info.Append("\r\nAddress:").Append(Address);
                info.Append("\r\nDonarCell:").Append(CurrentDonarCell != null ? CurrentDonarCell.Name : "None");
                return info.ToString();
            }
        }

        public bool Within(double x1, double y1, double x2, double y2)
        {
            if (Longitude < x1 || Longitude > x2 || Latitude < y1 || Latitude > y2)
            {
                return false;
            }
            return true;
        }

        public void Fill(MasterCom.RAMS.Net.Content content, CellManager cellManager)
        {
            base.Fill(content.GetParamInt(), content.GetParamInt(), content.GetParamInt());
            Name = content.GetParamString();
            Longitude = content.GetParamDouble();
            Latitude = content.GetParamDouble();
            BandType = (BTSBandType)content.GetParamInt();
            Address = content.GetParamString();
            foreach (Cell cell in cellManager.GetCells(content.GetParamInt(), ValidPeriod))
            {
                cell.AddRepeater(this);
            }
        }

        public static IComparer<Repeater> GetCompareByName()
        {
            if (comparerByName == null)
            {
                comparerByName = new ComparerByName();
            }
            return comparerByName;
        }

        private static IComparer<Repeater> comparerByName;

        public class ComparerByName : IComparer<Repeater>
        {
            public int Compare(Repeater x, Repeater y)
            {
                return x.Name.CompareTo(y.Name);
            }
        }
    }
}
