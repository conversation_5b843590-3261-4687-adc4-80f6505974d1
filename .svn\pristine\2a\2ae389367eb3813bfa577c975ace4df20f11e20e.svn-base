﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class StationAcceptFileAnaManager_GZ
    {
        public CellFileAnaInfoBase_GZ AnalyzeFile(DTFileDataManager fileManager)
        {
            try
            {
                LTECell testCell = getTestCell(fileManager);
                if (testCell == null)
                {
                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo(string.Format("文件{0}未找到目标小区", fileManager.FileName));
                    return null;
                }
                string fileName = fileManager.FileName.ToUpper().Trim();
                if (fileName.Contains("CQT"))
                {
                    if (fileName.Contains("定点上传"))
                    {
                        return statKpi_CQT_Ul(fileManager, testCell);
                    }
                    else if (fileName.Contains("定点下载"))
                    {
                        return statKpi_CQT_Dl(fileManager, testCell);
                    }
                    else if (fileName.Contains("CSFB"))
                    {
                        return statKpi_CQT_CSFB(fileManager, testCell);
                    }
                }
                else if (fileName.Contains("_DT"))
                {
                    return statKpi_DT(fileManager, testCell);
                }
            }
            catch(Exception ex)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundError(ex);
            }
            return null;
        }

        protected CellFileAnaInfoBase_GZ statKpi_CQT_Ul(DTFileDataManager fileManager, LTECell testCell)
        {
            CellFileAnaInfo_CqtUl_GZ kpiInfo = new CellFileAnaInfo_CqtUl_GZ(fileManager.GetFileInfo(), testCell);

            foreach (TestPoint tp in fileManager.TestPoints)
            {
                LTECell cell = getTpSrcCell(tp);
                if (!isSrcTestCell(cell, fileManager.FileName))
                {
                    continue;
                }

                float? rsrp = (float?)tp["lte_RSRP"];
                if (rsrp != null && rsrp >= -141 && rsrp <= 25)
                {
                    kpiInfo.RsrpInfo.PointCount++;
                    kpiInfo.RsrpInfo.KpiSum += (float)rsrp;
                }

                float? sinr = (float?)tp["lte_SINR"];
                if (sinr != null && sinr >= -50 && sinr <= 50)
                {
                    kpiInfo.SinrInfo.PointCount++;
                    kpiInfo.SinrInfo.KpiSum += (float)sinr;
                }

                float? speed = getSpeed(tp, false);
                if (speed != null)
                {
                    kpiInfo.MaxUlSpeed = Math.Max(kpiInfo.MaxUlSpeed, (float)speed);
                    kpiInfo.UlInfo.PointCount++;
                    kpiInfo.UlInfo.KpiSum += (float)speed;
                }
            }
            return kpiInfo;
        }
        protected CellFileAnaInfoBase_GZ statKpi_CQT_Dl(DTFileDataManager fileManager, LTECell testCell)
        {
            CellFileAnaInfo_CqtDl_GZ kpiInfo = new CellFileAnaInfo_CqtDl_GZ(fileManager.GetFileInfo(), testCell);

            foreach (TestPoint tp in fileManager.TestPoints)
            {
                LTECell cell = getTpSrcCell(tp);
                if (!isSrcTestCell(cell, fileManager.FileName))
                {
                    continue;
                }

                float? rsrp = (float?)tp["lte_RSRP"];
                if (rsrp != null && rsrp >= -141 && rsrp <= 25)
                {
                    kpiInfo.RsrpInfo.PointCount++;
                    kpiInfo.RsrpInfo.KpiSum += (float)rsrp;
                }

                float? sinr = (float?)tp["lte_SINR"];
                if (sinr != null && sinr >= -50 && sinr <= 50)
                {
                    kpiInfo.SinrInfo.PointCount++;
                    kpiInfo.SinrInfo.KpiSum += (float)sinr;
                }

                float? speed = getSpeed(tp, true);
                if (speed != null)
                {
                    kpiInfo.MaxDlSpeed = Math.Max(kpiInfo.MaxDlSpeed, (float)speed);
                    kpiInfo.DlInfo.PointCount++;
                    kpiInfo.DlInfo.KpiSum += (float)speed;
                }
            }
            return kpiInfo;
        }
        protected CellFileAnaInfoBase_GZ statKpi_CQT_CSFB(DTFileDataManager fileManager, LTECell testCell)
        {
            CellFileAnaInfo_CqtCsfb_GZ kpiInfo = new CellFileAnaInfo_CqtCsfb_GZ(fileManager.GetFileInfo(), testCell);
            foreach (Event evt in fileManager.Events)
            {
                switch (evt.ID)
                {
                    case 870:
                    case 1040:
                    case 1100:
                    case 1147:
                        kpiInfo.CsfbCallInfo.AddHandFailEvt(evt);
                        break;
                    case 1006:
                    case 1007:
                    case 1016:
                    case 1017:
                    case 1026:
                    case 1027:
                    case 1036:
                    case 1037:
                    case 1046:
                    case 1047:
                    case 1056:
                    case 1057:
                        kpiInfo.CsfbCallInfo.AddDropEvt(evt);
                        break;
                    case 1001:
                    case 1021:
                    case 1041:
                        kpiInfo.CsfbCallInfo.AddMoCallAttemptEvt(evt);
                        break;
                    case 1002:
                    case 1022:
                    case 1042:
                        kpiInfo.CsfbCallInfo.AddAttemptEvt(evt);
                        break;
                    case 1008:
                    case 1028:
                    case 1048:
                    case 1058:
                        kpiInfo.CsfbCallInfo.AddMoCallBlockEvt(evt);
                        break;
                    case 1009:
                    case 1029:
                    case 1049:
                    case 1059:
                        kpiInfo.CsfbCallInfo.AddBlockEvt(evt);
                        break;

                }
            }
            return kpiInfo;
        }
        protected CellFileAnaInfo_DT_GZ statKpi_DT(DTFileDataManager fileManager, LTECell testCell)
        {
            CellFileAnaInfo_DT_GZ kpiInfo = new CellFileAnaInfo_DT_GZ(fileManager.GetFileInfo(), testCell);

            foreach (TestPoint tp in fileManager.TestPoints)
            {
                LTECell cell = getTpSrcCell(tp);
                if (!isSrcTestCell(cell, fileManager.FileName))
                {
                    continue;
                }

                float? rsrp = (float?)tp["lte_RSRP"];
                if (rsrp != null && rsrp >= -141 && rsrp <= 25)
                {
                    kpiInfo.RsrpInfo.PointCount++;
                    kpiInfo.RsrpInfo.KpiSum += (float)rsrp;
                }

                float? sinr = (float?)tp["lte_SINR"];
                if (sinr != null && sinr >= -50 && sinr <= 50)
                {
                    kpiInfo.SinrInfo.PointCount++;
                    kpiInfo.SinrInfo.KpiSum += (float)sinr;
                }
            }
            return kpiInfo;
        }

        protected float? getSpeed(TestPoint tp, bool isDl)
        {
            if (tp.ServiceType == (int)ServiceType.LTE_TDD_DATA)
            {
                return getAppSpeed(tp, isDl);
            }
            else
            {
                return getPdcpSpeed(tp, isDl);
            }
        }
        private float? getAppSpeed(TestPoint tp, bool isDl)
        {
            short? type = (short?)tp["lte_APP_type"];
            if (type == null)
            {
                return null;
            }

            float? speed = (float?)tp["lte_APP_Speed_Mb"];
            if (speed == null || speed < 0)
            {
                return null;
            }

            if (isDl && type == (int)AppType.FTP_Download)
            {
                return speed;
            }
            else if (!isDl && type == (int)AppType.FTP_Upload)
            {
                return speed;
            }
            return null;
        }
        private float? getPdcpSpeed(TestPoint tp, bool isDl)
        {
            float? speed = null;
            if (isDl)
            {
                speed = (float?)(double?)tp["lte_PDCP_DL_Mb"];
            }
            else
            {
                speed = (float?)(double?)tp["lte_PDCP_UL_Mb"];
            }
            return speed;
        }

        #region 关联小区
        private LTECell getTestCell(DTFileDataManager fileManager)
        {
            LTECell targeCell = null;
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                LTECell cell = getTpSrcCell(tp);
                if (isSrcTestCell(cell, fileManager.FileName))
                {
                    targeCell = cell;
                    break;
                }
            }
            return targeCell;
        }

        protected static LTECell getTpSrcCell(TestPoint tp)
        {
            LTECell cell = null;
            if (tp is LTETestPointDetail)
            {
                cell = CellManager.GetInstance().GetNearestLTECellByTACCI(tp.DateTime, (int?)(ushort?)tp["lte_TAC"], (int?)tp["lte_ECI"]
                    , tp.Longitude, tp.Latitude);

                int? earfcn = (int?)tp["lte_EARFCN"];
                int? pci = (int?)(short?)tp["lte_PCI"];

                if (cell == null)
                {
                    cell = getLTECellByEARFCNPCI(tp.DateTime, earfcn, pci, tp.Longitude, tp.Latitude, tp.FileName);
                }
                else if (earfcn != null && pci != null && (cell.EARFCN != (int)earfcn || cell.PCI != (int)pci))
                {
                    cell = getLTECellByEARFCNPCI(tp.DateTime, earfcn, pci, tp.Longitude, tp.Latitude, tp.FileName);
                }
            }
            return cell;
        }
        private static LTECell getLTECellByEARFCNPCI(DateTime time, int? earfcn, int? pci, double longitude
            , double latitude, string fileName)
        {
            if (earfcn == null || pci == null)
            {
                return null;
            }
            List<LTECell> cells = CellManager.GetInstance().GetLTECellListByEARFCNPCI(time, earfcn, pci);
            if (cells != null && cells.Count > 0)
            {
                #region 先根据文件名匹配小区，匹配不到再取最近的一个小区
                if (!string.IsNullOrEmpty(fileName))
                {
                    LTECell cell = getInRegionCell(time, longitude, latitude, fileName, cells);
                    if (cell != null)
                    {
                        return cell;
                    }
                }
                #endregion

                return CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(time, earfcn, pci, longitude, latitude);
            }
            return null;
        }

        private static LTECell getInRegionCell(DateTime time, double longitude, double latitude, string fileName, List<LTECell> cells)
        {
            foreach (LTECell cell in cells)
            {
                if (cell.ValidPeriod.Contains(time))
                {
                    if (longitude > 0 && latitude > 0 && CellManager.GetInstance().SystemConfigInfo.distLimit
                        && cell.GetDistance(longitude, latitude) > CD.MAX_COV_DISTANCE_LTE)//距离限制设置
                    {
                        continue;
                    }
                    if (isSrcTestCell(cell, fileName))
                    {
                        return cell;
                    }
                }
            }
            return null;
        }

        private static bool isSrcTestCell(LTECell cell, string fileName)
        {
            if (cell == null)
            {
                return false;
            }
            fileName = fileName.Trim().ToUpper();
            string cellName = cell.Name.Trim().ToUpper();
            if (fileName.Contains(cellName))
            {
                return true;
            }
            return false;
        }
        #endregion
    }
}
