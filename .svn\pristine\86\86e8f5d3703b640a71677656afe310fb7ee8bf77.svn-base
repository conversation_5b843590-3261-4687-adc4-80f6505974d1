﻿using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class EdgeSpeedAnaHelper
    {
        public static Dictionary<string, Dictionary<string, MapOperation2>> InitRegionMop2()
        {
            Dictionary<string, Dictionary<string, MapOperation2>> mutRegionMapDic = new Dictionary<string, Dictionary<string, MapOperation2>>();
            Dictionary<string, List<ResvRegion>> resvRegionsDic = MainModel.GetInstance().SearchGeometrys.SelectedResvRegionDic;
            MapWinGIS.Shape gmt = MainModel.GetInstance().SearchGeometrys.Region;
            if (resvRegionsDic != null && resvRegionsDic.Count > 0)
            {
                foreach (string strGridType in resvRegionsDic.Keys)
                {
                    addRegionMap(mutRegionMapDic, resvRegionsDic, strGridType);
                }
            }
            else if (gmt != null)//单个区域
            {
                Dictionary<string, MapOperation2> regionMopDic = new Dictionary<string, MapOperation2>();
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(gmt);
                regionMopDic.Add("当前区域", mapOp2);
                mutRegionMapDic.Add("无网格类型", regionMopDic);
            }
            return mutRegionMapDic;
        }

        private static void addRegionMap(Dictionary<string, Dictionary<string, MapOperation2>> mutRegionMapDic, Dictionary<string, List<ResvRegion>> resvRegionsDic, string strGridType)
        {
            if (!mutRegionMapDic.ContainsKey(strGridType))
            {
                Dictionary<string, MapOperation2> regionMop = new Dictionary<string, MapOperation2>();
                foreach (ResvRegion region in resvRegionsDic[strGridType])
                {
                    if (!regionMop.ContainsKey(region.RegionName))
                    {
                        MapOperation2 mapOp2 = new MapOperation2();
                        mapOp2.FillPolygon(region.Shape);
                        regionMop.Add(region.RegionName, mapOp2);
                    }
                }
                mutRegionMapDic.Add(strGridType, regionMop);
            }
        }

        public virtual void AddValidData(double x, double y, float fEdgeSpeed, string strCityName, Dictionary<string, Dictionary<string, MapOperation2>> mutRegionMopDic, Dictionary<string, Dictionary<string, List<float>>> cityGridTypeNameEdgeSpeedDic)
        {
            string strGridTypeName = strContainPoint(x, y, mutRegionMopDic);
            if (strGridTypeName == "")
            {
                strGridTypeName = "未知网格类型,未知网格号";
            }
            if (!cityGridTypeNameEdgeSpeedDic.ContainsKey(strCityName))
            {
                List<float> dRateListTmp = new List<float>();
                dRateListTmp.Add(fEdgeSpeed);
                Dictionary<string, List<float>> gridTypeNameEdgeSpeedDic = new Dictionary<string, List<float>>();
                gridTypeNameEdgeSpeedDic.Add(strGridTypeName, dRateListTmp);
                cityGridTypeNameEdgeSpeedDic.Add(strCityName, gridTypeNameEdgeSpeedDic);
            }
            else
            {
                if (!cityGridTypeNameEdgeSpeedDic[strCityName].ContainsKey(strGridTypeName))
                {
                    List<float> dRateListTmp = new List<float>();
                    dRateListTmp.Add(fEdgeSpeed);
                    cityGridTypeNameEdgeSpeedDic[strCityName].Add(strGridTypeName, dRateListTmp);
                }
                else
                {
                    cityGridTypeNameEdgeSpeedDic[strCityName][strGridTypeName].Add(fEdgeSpeed);
                }
            }
        }

        protected virtual string strContainPoint(double x, double y, Dictionary<string, Dictionary<string, MapOperation2>> mutRegionMopDic)
        {
            string gridTypeGrid = "";
            foreach (string gridType in mutRegionMopDic.Keys)
            {
                foreach (string grid in mutRegionMopDic[gridType].Keys)
                {
                    if (mutRegionMopDic[gridType][grid].CheckPointInRegion(x, y))
                    {
                        gridTypeGrid = gridType + "," + grid;
                        break;
                    }
                }
            }
            return gridTypeGrid;
        }

        public virtual List<EdgeSpeedInfo> GetResultAfterAllQuery(Dictionary<string, Dictionary<string, List<float>>> cityGridTypeNameEdgeSpeedDicTem, float edgeSpeedThreshold)
        {
            List<EdgeSpeedInfo> edgeSpeedInfoListTem = new List<EdgeSpeedInfo>();
            foreach (string city in cityGridTypeNameEdgeSpeedDicTem.Keys)
            {
                foreach (string strGrid in cityGridTypeNameEdgeSpeedDicTem[city].Keys)
                {
                    EdgeSpeedInfo edgeSpeedInfo = init();
                    edgeSpeedInfo.StrCity = city;
                    edgeSpeedInfo.StrGridType = strGrid.Split(',')[0];
                    edgeSpeedInfo.StrGridName = strGrid.Split(',')[1];
                    edgeSpeedInfo.ITestPointNum = cityGridTypeNameEdgeSpeedDicTem[city][strGrid].Count;
                    edgeSpeedInfo.FEdgeSpeed = getFEgedeSpeed(cityGridTypeNameEdgeSpeedDicTem[city][strGrid], edgeSpeedThreshold);

                    edgeSpeedInfoListTem.Add(edgeSpeedInfo);
                }
            }

            edgeSpeedInfoListTem.AddRange(sumGridGata(cityGridTypeNameEdgeSpeedDicTem, edgeSpeedThreshold));
            edgeSpeedInfoListTem.AddRange(sumCityGata(cityGridTypeNameEdgeSpeedDicTem, edgeSpeedThreshold));
            return edgeSpeedInfoListTem;
        }

        /// <summary>
        /// 网格汇总
        /// </summary>
        protected virtual List<EdgeSpeedInfo> sumGridGata(Dictionary<string, Dictionary<string, List<float>>> cityGridTypeNameEdgeSpeedDicTem, float edgeSpeedThreshold)
        {
            List<EdgeSpeedInfo> edgeSpeedInfoListTem = new List<EdgeSpeedInfo>();
            foreach (string city in cityGridTypeNameEdgeSpeedDicTem.Keys)
            {
                List<string> strGridList = new List<string>();
                foreach (string strGrid in cityGridTypeNameEdgeSpeedDicTem[city].Keys)
                {
                    string strGridType = strGrid.Split(',')[0];
                    if (strGridList.Contains(strGridType))
                    {
                        continue;
                    }
                    List<float> fSpeedTem = new List<float>();
                    EdgeSpeedInfo edgeSpeedInfo = init();
                    edgeSpeedInfo.StrCity = city;
                    foreach (string strGridTypeTem in cityGridTypeNameEdgeSpeedDicTem[city].Keys)
                    {
                        if (strGridType.Equals(strGridTypeTem.Split(',')[0]))
                        {
                            fSpeedTem.AddRange(cityGridTypeNameEdgeSpeedDicTem[city][strGridTypeTem]);
                        }
                    }
                    strGridList.Add(strGridType);
                    edgeSpeedInfo.StrGridType = strGridType;
                    edgeSpeedInfo.StrGridName = "汇总";
                    edgeSpeedInfo.ITestPointNum = fSpeedTem.Count;
                    edgeSpeedInfo.FEdgeSpeed = getFEgedeSpeed(fSpeedTem, edgeSpeedThreshold);
                    edgeSpeedInfoListTem.Add(edgeSpeedInfo);
                }
            }
            return edgeSpeedInfoListTem;
        }

        /// <summary>
        /// 地市汇总
        /// </summary>
        protected virtual List<EdgeSpeedInfo> sumCityGata(Dictionary<string, Dictionary<string, List<float>>> cityGridTypeNameEdgeSpeedDicTem, float edgeSpeedThreshold)
        {
            List<EdgeSpeedInfo> edgeSpeedInfoListTem = new List<EdgeSpeedInfo>();
            foreach (string city in cityGridTypeNameEdgeSpeedDicTem.Keys)
            {
                EdgeSpeedInfo edgeSpeedInfo = init();
                edgeSpeedInfo.StrCity = city;
                edgeSpeedInfo.StrGridType = "汇总";
                edgeSpeedInfo.StrGridName = "汇总";
                List<float> fSpeedTem = new List<float>();
                foreach (string strGrid in cityGridTypeNameEdgeSpeedDicTem[city].Keys)
                {
                    fSpeedTem.AddRange(cityGridTypeNameEdgeSpeedDicTem[city][strGrid]);
                }
                edgeSpeedInfo.ITestPointNum = fSpeedTem.Count;
                edgeSpeedInfo.FEdgeSpeed = getFEgedeSpeed(fSpeedTem, edgeSpeedThreshold);
                edgeSpeedInfoListTem.Add(edgeSpeedInfo);
            }
            return edgeSpeedInfoListTem;
        }

        protected virtual float getFEgedeSpeed(List<float> fSpeedList, float edgeSpeedThreshold)
        {
            fSpeedList.Sort();
            int index = (int)(0.01 * edgeSpeedThreshold * fSpeedList.Count) - 1;
            if (index < 0)
            {
                index = 0;
            }
            return fSpeedList[index];
        }

        protected virtual EdgeSpeedInfo init()
        { 
            return new EdgeSpeedInfo();
        }
    }

    public class EdgeSpeedInfo
    {
        public string StrCity { get; set; }
        public string StrGridType { get; set; }
        public string StrGridName { get; set; }
        public int ITestPointNum { get; set; }
        public float FEdgeSpeed { get; set; }

        public string StrEdgeSpeed
        {
            get
            {
                return FEdgeSpeed.ToString("0.00");
            }
        }
    }

    public class EdgeSpeedCondtion
    {
        public float EdgeSpeedThreshold { get; set; } = 5;
    }
}
