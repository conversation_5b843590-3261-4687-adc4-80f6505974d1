﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class CellWeakCoverByCellDirConditionDlg
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.simpleButtonCancel = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonOK = new DevExpress.XtraEditors.SimpleButton();
            this.numCellCoverDistance = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.numWeakGridCount = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.numJudgeScale = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.chkJudgeByScale = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.numRxLevMax = new DevExpress.XtraEditors.SpinEdit();
            ((System.ComponentModel.ISupportInitialize)(this.numCellCoverDistance.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakGridCount.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numJudgeScale.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkJudgeByScale.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevMax.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // simpleButtonCancel
            // 
            this.simpleButtonCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButtonCancel.Appearance.Options.UseFont = true;
            this.simpleButtonCancel.Location = new System.Drawing.Point(238, 204);
            this.simpleButtonCancel.Name = "simpleButtonCancel";
            this.simpleButtonCancel.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonCancel.TabIndex = 3;
            this.simpleButtonCancel.Text = "取消";
            this.simpleButtonCancel.Click += new System.EventHandler(this.simpleButtonCancel_Click);
            // 
            // simpleButtonOK
            // 
            this.simpleButtonOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButtonOK.Appearance.Options.UseFont = true;
            this.simpleButtonOK.Location = new System.Drawing.Point(141, 204);
            this.simpleButtonOK.Name = "simpleButtonOK";
            this.simpleButtonOK.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonOK.TabIndex = 2;
            this.simpleButtonOK.Text = "确定";
            this.simpleButtonOK.Click += new System.EventHandler(this.simpleButtonOK_Click);
            // 
            // numCellCoverDistance
            // 
            this.numCellCoverDistance.EditValue = new decimal(new int[] {
            300,
            0,
            0,
            0});
            this.numCellCoverDistance.Location = new System.Drawing.Point(173, 118);
            this.numCellCoverDistance.Name = "numCellCoverDistance";
            this.numCellCoverDistance.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numCellCoverDistance.Properties.Appearance.Options.UseFont = true;
            this.numCellCoverDistance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numCellCoverDistance.Properties.Mask.EditMask = "f0";
            this.numCellCoverDistance.Size = new System.Drawing.Size(96, 20);
            this.numCellCoverDistance.TabIndex = 0;
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(280, 122);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(12, 12);
            this.labelControl3.TabIndex = 15;
            this.labelControl3.Text = "米";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(81, 122);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(84, 12);
            this.labelControl2.TabIndex = 14;
            this.labelControl2.Text = "小区覆盖距离≤";
            // 
            // numWeakGridCount
            // 
            this.numWeakGridCount.EditValue = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.numWeakGridCount.Location = new System.Drawing.Point(173, 154);
            this.numWeakGridCount.Name = "numWeakGridCount";
            this.numWeakGridCount.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numWeakGridCount.Properties.Appearance.Options.UseFont = true;
            this.numWeakGridCount.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numWeakGridCount.Properties.Mask.EditMask = "f0";
            this.numWeakGridCount.Size = new System.Drawing.Size(96, 20);
            this.numWeakGridCount.TabIndex = 1;
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(81, 158);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(84, 12);
            this.labelControl4.TabIndex = 20;
            this.labelControl4.Text = "弱覆盖栅格数≥";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.labelControl5);
            this.groupControl1.Controls.Add(this.numJudgeScale);
            this.groupControl1.Controls.Add(this.labelControl1);
            this.groupControl1.Controls.Add(this.chkJudgeByScale);
            this.groupControl1.Controls.Add(this.labelControl6);
            this.groupControl1.Controls.Add(this.numRxLevMax);
            this.groupControl1.Location = new System.Drawing.Point(23, 12);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(290, 89);
            this.groupControl1.TabIndex = 22;
            this.groupControl1.Text = "弱覆盖栅格判定";
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(259, 61);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(6, 12);
            this.labelControl5.TabIndex = 27;
            this.labelControl5.Text = "%";
            // 
            // numJudgeScale
            // 
            this.numJudgeScale.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numJudgeScale.Location = new System.Drawing.Point(152, 57);
            this.numJudgeScale.Name = "numJudgeScale";
            this.numJudgeScale.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numJudgeScale.Properties.Appearance.Options.UseFont = true;
            this.numJudgeScale.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numJudgeScale.Properties.Mask.EditMask = "f0";
            this.numJudgeScale.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numJudgeScale.Size = new System.Drawing.Size(96, 20);
            this.numJudgeScale.TabIndex = 2;
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(84, 35);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(60, 12);
            this.labelControl1.TabIndex = 25;
            this.labelControl1.Text = "信号强度 <";
            // 
            // chkJudgeByScale
            // 
            this.chkJudgeByScale.Location = new System.Drawing.Point(14, 58);
            this.chkJudgeByScale.Name = "chkJudgeByScale";
            this.chkJudgeByScale.Properties.Caption = "弱覆盖采样点比例 >";
            this.chkJudgeByScale.Size = new System.Drawing.Size(130, 19);
            this.chkJudgeByScale.TabIndex = 1;
            this.chkJudgeByScale.CheckedChanged += new System.EventHandler(this.chkJudgeByScale_CheckedChanged);
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Location = new System.Drawing.Point(259, 35);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(18, 12);
            this.labelControl6.TabIndex = 22;
            this.labelControl6.Text = "dBm";
            // 
            // numRxLevMax
            // 
            this.numRxLevMax.EditValue = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            this.numRxLevMax.Location = new System.Drawing.Point(152, 31);
            this.numRxLevMax.Name = "numRxLevMax";
            this.numRxLevMax.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numRxLevMax.Properties.Appearance.Options.UseFont = true;
            this.numRxLevMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRxLevMax.Properties.Mask.EditMask = "f0";
            this.numRxLevMax.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numRxLevMax.Properties.MinValue = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numRxLevMax.Size = new System.Drawing.Size(96, 20);
            this.numRxLevMax.TabIndex = 0;
            // 
            // CellWeakCoverByCellDirConditionDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(339, 251);
            this.Controls.Add(this.groupControl1);
            this.Controls.Add(this.numWeakGridCount);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.simpleButtonCancel);
            this.Controls.Add(this.simpleButtonOK);
            this.Controls.Add(this.numCellCoverDistance);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.labelControl2);
            this.Name = "CellWeakCoverByCellDirConditionDlg";
            this.Text = "弱覆盖小区设置";
            ((System.ComponentModel.ISupportInitialize)(this.numCellCoverDistance.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakGridCount.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numJudgeScale.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkJudgeByScale.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevMax.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton simpleButtonCancel;
        private DevExpress.XtraEditors.SimpleButton simpleButtonOK;
        private DevExpress.XtraEditors.SpinEdit numCellCoverDistance;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SpinEdit numWeakGridCount;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.SpinEdit numRxLevMax;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.SpinEdit numJudgeScale;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.CheckEdit chkJudgeByScale;
    }
}