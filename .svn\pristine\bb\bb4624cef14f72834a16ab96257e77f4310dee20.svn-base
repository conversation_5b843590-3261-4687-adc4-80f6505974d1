﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.RAMS.UserMng;


namespace MasterCom.RAMS.ZTFunc
{
    public class LtePlanningWorkingParameterQuery : DIYReplayFileQuery
    {
        public LtePlanningWorkingParameterQuery(MainModel mainModel) : base(mainModel)
        {
            IsAddSampleToDTDataManager = false;
        }

        public override string Name
        {
            get { return "评估工参导出"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
#if !DEBUG
            throw new Exception("评估工参导出 未分配功能点ID");
#endif
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 19000, 19040, this.Name);
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override DIYReplayContentOption getDIYReplayContent()
        {
            DIYReplayContentOption option = new DIYReplayContentOption();
            option.DefaultSerialThemeName = null;

            List<ColumnDefItem> cols = null;
            foreach (string item in this.Columns)
            {
                cols = InterfaceManager.GetInstance().GetColumnDefByShowName(item);
                option.SampleColumns.AddRange(cols);
            }

            return option;
        }

        protected override void doWithDTData(TestPoint tp)
        {
            int? tac = (int?)(ushort?)tp["lte_TAC"];
            int? eci = (int?)tp["lte_ECI"];
            int? earfcn = (int?)tp["lte_EARFCN"];
            int? pci = (int?)(short?)tp["lte_PCI"];
            LTECell cell = CellManager.GetInstance().GetNearestLTECell(tp.DateTime, tac, eci, earfcn, pci, tp.Longitude, tp.Latitude);
            if (cell == null)
            {
                return;
            }
            if (!cellDic.ContainsKey(cell))
            {
                cellDic.Add(cell, 0);
            }
            ++cellDic[cell];
        }

        protected override void fireShowResult()
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow titleRow = new NPOIRow();
            titleRow.AddCellValue("基站名称");
            titleRow.AddCellValue("ENodeBID");
            titleRow.AddCellValue("经度");
            titleRow.AddCellValue("纬度");
            titleRow.AddCellValue("地址");
            titleRow.AddCellValue("小区名称");
            titleRow.AddCellValue("TAC");
            titleRow.AddCellValue("ECI");
            titleRow.AddCellValue("CellID");
            titleRow.AddCellValue("SectorID");
            titleRow.AddCellValue("ARFCN");
            titleRow.AddCellValue("PCI");
            titleRow.AddCellValue("方向角");
            titleRow.AddCellValue("机械下倾角");
            titleRow.AddCellValue("电子下倾角");
            titleRow.AddCellValue("挂高");
            rows.Add(titleRow);

            foreach (LTECell cell in cellDic.Keys)
            {
                NPOIRow curRow = new NPOIRow();
                curRow.AddCellValue(cell.BTSName);
                curRow.AddCellValue(cell.BelongBTS.BTSID);
                curRow.AddCellValue(cell.Longitude);
                curRow.AddCellValue(cell.Latitude);
                curRow.AddCellValue("");
                curRow.AddCellValue(cell.Name);
                curRow.AddCellValue(cell.TAC);
                curRow.AddCellValue(cell.ECI);
                curRow.AddCellValue(cell.SCellID);
                curRow.AddCellValue(cell.SectorID);
                curRow.AddCellValue(cell.EARFCN);
                curRow.AddCellValue(cell.PCI);
                curRow.AddCellValue(cell.Direction);
                curRow.AddCellValue(cell.Downward);
                curRow.AddCellValue("");
                curRow.AddCellValue(cell.Altitude);
                rows.Add(curRow);
            }

            cellDic.Clear();
            try
            {
                ExportFuncResultManager.GetInstance().SetCurLogItem(this.getRecLogItem());
                ExcelNPOIManager.ExportToExcel(rows);
            }
            finally
            {
                ExportFuncResultManager.GetInstance().SetCurLogItem(null);
            }
        }

        public List<string> Columns
        {
            get
            {
                return new List<string>()
                {
                    "isampleid",
                    "itime",
                    "ilongitude",
                    "ilatitude",
                    "lte_TAC",
                    "lte_ECI",
                    "lte_SCell_CI",
                    "lte_EARFCN",
                    "lte_PCI",
                    "lte_RSRQ",
                    "lte_RSRP",
                    "lte_RSSI",
                    "lte_SINR",
                    "lte_NCell_EARFCN",
                    "lte_NCell_PCI",
                    "lte_NCell_RSRP",
                    "lte_NCell_RSRQ",
                    "lte_NCell_RSSI",
                    "lte_NCell_SINR",
                    "lte_PDCP_DL",
                };
            }
        }
        
        private readonly Dictionary<LTECell, int> cellDic = new Dictionary<LTECell,int>();
    }
}
