﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Frame;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.Net
{
    public class ZTDIYLeakOutCellSetScanNR : DIYAnalyseByCellBackgroundBaseByFile
    {
        public int rsrpThreshold { get; set; } = -85; //最强信号门限
        public int rsrpDValue { get; set; } = 10;//相对最强信号门限
        public int nbSampleDistanceLimit { get; set; } = 50;//相邻采样点距离
        public int validDistance { get; set; } = 50;//连续覆盖长度
        readonly Dictionary<NRCell, LeakOutCell_NRSCAN> leakOutCellDic = new Dictionary<NRCell, LeakOutCell_NRSCAN>();

        private static ZTDIYLeakOutCellSetScanNR intance = null;
        protected static readonly object lockObj = new object();
        public static ZTDIYLeakOutCellSetScanNR GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTDIYLeakOutCellSetScanNR();
                    }
                }
            }
            return intance;
        }

        protected ZTDIYLeakOutCellSetScanNR()
            : base(MainModel.GetInstance())
        {
            FilterSampleByRegion = false;
            IncludeEvent = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.NR_Scan);
            carrierID = CarrierType.ChinaMobile;
        }

        public ZTDIYLeakOutCellSetScanNR(ServiceName serviceName)
            : this()
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        }

        public override string Name
        {
            get { return "室分外泄分析_NR扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 36000, 36008, this.Name);
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            LeakOutCellSetConditionDlg conditionDlg = LeakOutCellSetConditionDlg.GetDlg();
            if (conditionDlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            rsrpThreshold = conditionDlg.RscpLow;
            rsrpDValue = conditionDlg.RscpLimit;
            nbSampleDistanceLimit = conditionDlg.MaxDistance;
            validDistance = conditionDlg.ValidDistance;
            return true;
        }

        protected override void fireShowForm()
        {
            if (MainModel.LeakOutCell_SCANList.Count == 0)
            {
                MessageBox.Show("没有符合条件的数据。");
                return;
            }
            MainModel.MainForm.FireLeakOutCellSetScanTDForm();
        }

        protected override void fireSetDefaultMapSerialTheme()
        {
            MainModel.FireSetDefaultMapSerialTheme(NRTpHelper.NrScanTpManager.RsrpFullThemeName);
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            MainModel.LeakOutCell_SCANList.Clear();
        }

        protected override void doStatWithQuery()
        {
            try
            {
                leakOutCellDic.Clear();
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    List<TestPoint> testPointList = fileDataManager.TestPoints;
                    for (int i = 0; i < testPointList.Count; i++)
                    {
                        TestPoint testPoint = testPointList[i];
                        if (i == 0)
                        {
                            doWithDTData(testPoint);
                        }
                        else
                        {
                            addLeakOutCell_SCANList(testPointList, i, testPoint);
                        }
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        private void addLeakOutCell_SCANList(List<TestPoint> testPointList, int i, TestPoint testPoint)
        {
            double betweenDistance = MathFuncs.GetDistance(testPointList[i - 1].Longitude, testPointList[i - 1].Latitude, testPoint.Longitude, testPoint.Latitude);
            if (betweenDistance > nbSampleDistanceLimit || !doWithDTData(testPoint))
            {
                foreach (LeakOutCell_NRSCAN leakCell in leakOutCellDic.Values)
                {
                    if (leakCell.Distance >= validDistance)
                    {
                        MainModel.LeakOutCell_SCANList.Add(leakCell);
                    }
                }
                leakOutCellDic.Clear();
            }
        }

        private bool doWithDTData(TestPoint testPoint)
        {
            if (!Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude))
            {
                return false;
            }
            bool getLeak = false;
            float? rsrpMain = NRTpHelper.NrScanTpManager.GetCellRsrp(testPoint, 0);
            if (rsrpMain <= rsrpThreshold)
            {
                return getLeak;
            }

            Dictionary<int, int> groupDic = NRTpHelper.NrScanTpManager.GetCellMaxBeam(testPoint);
            foreach (var i in groupDic.Values)
            {
                float? rsrpCur = NRTpHelper.NrScanTpManager.GetCellRsrp(testPoint, i);
                if (rsrpCur == null || rsrpMain - rsrpCur >= rsrpDValue)
                {
                    break;
                }
                NRCell curCell = testPoint.GetCell_NRScan(i);
                if (curCell != null && curCell.Type == NRBTSType.Indoor)
                {
                    if (!leakOutCellDic.ContainsKey(curCell))
                    {
                        var leakOutCell = new LeakOutCell_NRSCAN(curCell, testPoint, (float)rsrpCur);
                        leakOutCellDic[curCell] = leakOutCell;
                    }
                    else
                    {
                        leakOutCellDic[curCell].AddTestPoint(testPoint, (float)rsrpCur);
                    }
                    getLeak = true;
                    break;
                }
            }
            return getLeak;
        }

        protected override void getResultsAfterQuery()
        {
            foreach (LeakOutCell_SCAN item in MainModel.LeakOutCell_SCANList)
            {
                item.GetResult();
            }
        }
    }

    public class LeakOutCell_NRSCAN : LeakOutCell_SCAN
    {
        public NRCell nrCell { get; set; }

        public LeakOutCell_NRSCAN(NRCell nrCell, TestPoint tp, float rsrp)
        {
            this.nrCell = nrCell;
            AddTestPoint(tp, rsrp);
        }

        public override string CellName
        {
            get
            {
                if (nrCell != null)
                {
                    return nrCell.Name;
                }
                return "";
            }
        }

        public override string CellID
        {
            get
            {
                if (nrCell != null)
                {
                    return nrCell.CellID.ToString();
                }
                return "";
            }
        }

        public override string BCCH
        {
            get
            {
                if (nrCell != null)
                {
                    return nrCell.PCI.ToString();
                }
                return "";
            }
        }

        public override string BSIC
        {
            get
            {
                if (nrCell != null)
                {
                    return nrCell.SSBARFCN.ToString();
                }
                return "";
            }
        }

        public override string LAC
        {
            get
            {
                if (nrCell != null)
                {
                    return nrCell.TAC.ToString();
                }
                return "";
            }
        }

        public override string CI
        {
            get
            {
                if (nrCell != null)
                {
                    return nrCell.NCI.ToString();
                }
                return "";
            }
        }
    }
}

