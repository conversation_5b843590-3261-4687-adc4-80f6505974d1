﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.Util
{
    public class FloatDotFormatter : IFormatProvider, ICustomFormatter
    {
        private readonly int dotPro;
        public FloatDotFormatter(int dot = 2)
        {
            this.dotPro = dot;
        }
        public object GetFormat(Type formatType)
        {
            if (formatType == typeof(ICustomFormatter))
            {
                return this;
            }
            else return null;
        }

        public string Format(string format, object arg, IFormatProvider formatProvider)
        {
            if (arg == null)
            {
                return "";
            }
            else if (arg is float)
            {
                if (float.IsNaN((float)arg))
                {
                    return "-";
                }
                else
                {
                    return ((float)arg).ToString("F" + dotPro);
                }
            }
            else if (arg is double)
            {
                if (double.IsNaN((double)arg))
                {
                    return "-";
                }
                else
                {
                    return ((double)arg).ToString("F" + dotPro);
                }
            }
            else
            {
                return arg.ToString();
            }
        }
    }
}
