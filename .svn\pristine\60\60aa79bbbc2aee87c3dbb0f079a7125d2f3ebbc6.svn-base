﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.Text.RegularExpressions;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCsfbCellJudgeNew_Fdd : ZTCsfbCellJudgeBaseNew
    {
        public ZTCsfbCellJudgeNew_Fdd(MainModel mainModel)
            : base(mainModel)
        {
            curLteServiceType = (int)ServiceType.LTE_FDD_VOICE;
            curFallServiceType = (int)ServiceType.WCDMA_VOICE;
            EvtIdMtCsfbLteRelease = 3073;
            EvtIdMoCsfbLteRelease = 3072;
            EvtIdLte_CellReselection_L2G = 3302;//L2W FDD回落WCDMA
            isFDD = true;
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26066, this.Name);
        }

        protected override void initColumns()
        {
            this.Columns = new List<string>();

            #region WCDMA
            Columns.Add("W_SysLAI");
            Columns.Add("W_SysCellID");
            Columns.Add("W_frequency");
            Columns.Add("W_Reference_PSC");
            Columns.Add("W_Reference_RSCP");
            Columns.Add("W_SNeiFreq");
            Columns.Add("W_SNeiPSC");
            Columns.Add("W_SNeiRSCP");
            #endregion

            #region LTE-FDD
            Columns.Add("lte_fdd_wcdma_SysLAI");
            Columns.Add("lte_fdd_wcdma_SysCellID");
            Columns.Add("lte_fdd_wcdma_frequency");
            Columns.Add("lte_fdd_wcdma_Reference_PSC");
            Columns.Add("lte_fdd_wcdma_TotalRSCP");
            Columns.Add("lte_fdd_wcdma_SNeiFreq");
            Columns.Add("lte_fdd_wcdma_SNeiPSC");
            Columns.Add("lte_fdd_wcdma_SNeiRSCP");
            #endregion
        }


        protected override ICell getSrcICellByEvt(Event evt)
        {
            return CellManager.GetInstance().GetNearestWCell(evt.DateTime, (int?)evt["TargetLAC"]
                , (int?)evt["TargetCI"], evt.Longitude, evt.Latitude);
        }
        protected override void addInfoToResultList(ZTCsfbCellJudgeNew judge, int maxRxLev, double maxLongitude, double maxLatitude)
        {
            WCell cell = iCell as WCell;
            if (cell == null)
            {
                return;
            }
            judge.Sort();
            if (judge.Contains(cell))
            {
                if (isContainsKey(judge.CellRxLevList, cell))
                {
                    judge.IsInList = true;
                }
                else
                {
                    judge.IsInList = false;
                }
                judge.SN = resultList.Count + 1;
                judge.CellName = cell.Name;
                judge.CellID = cell.CI;
                judge.LAC = cell.LAC;
                judge.FallLongitude = cell.Longitude;
                judge.FallLatitude = cell.Latitude;
                judge.MaxFallDistance = Math.Round(MathFuncs.GetDistance(judge.Longitude, judge.Latitude, maxLongitude, maxLatitude), 2);
                judge.MaxFallRxLev = maxRxLev;
                resultList.Add(judge);
            }
        }

        protected override void getNBCellInfo(ZTCsfbCellJudgeNew judge, TestPoint curTp)
        {
            for (int j = 0; j < 32; j++)
            {
                ICell nbCell = curTp.GetNBCell(j);

                int? nRxlev = (int?)curTp.GetNbRxlev(j);
                if (nbCell != null && nRxlev != null)
                {
                    if (nRxlev >= -140 && nRxlev <= 10)
                    {
                        dealNbCell(judge, nbCell, nRxlev);
                    }
                }
                else
                {
                    break;
                }
            }
        }

        private void dealNbCell(ZTCsfbCellJudgeNew judge, ICell nbCell, int? nRxlev)
        {
            Cell gCell = CellManager.GetInstance().GetCellByName(nbCell.Name);
            if (gCell != null)
            {
                if (isContainsKey(levDc, nbCell))
                {
                    levDc[nbCell.Name] += (int)nRxlev;
                    cellCountlist[nbCell.Name]++;
                }
                else
                {
                    levDc.Add(nbCell.Name, (int)nRxlev);
                    cellCountlist.Add(nbCell.Name, 1);
                }

                if (!judge.Contains(nbCell))
                {
                    judge.AddCell(nbCell);
                }
            }
        }

        protected override ICell getCell(TestPoint curTp)
        {
            return curTp.GetMainCell_W();
        }

        protected override void getMaxData(ZTCsfbCellJudgeNew judge, int maxRxLev, ref double maxLongitude, ref double maxLatitude)
        {
            foreach (KeyValuePair<string, int> pair in judge.CellRxLevList)
            {
                CellManager cm = CellManager.GetInstance();
                WCell cell = cm.GetWCellByName(pair.Key);
                if (maxRxLev == pair.Value && cell != null)
                {
                    maxLongitude = cell.Longitude;
                    maxLatitude = cell.Latitude;
                    break;
                }
            }
        }

        protected override bool isInSix(Dictionary<string, int> levDc, ICell cl)
        {
            int i = 0;
            foreach (KeyValuePair<string, int> pair in levDc)
            {
                i++;
                if (i <= 6)
                {
                    WCell cell = CellManager.GetInstance().GetWCellByName(pair.Key);
                    if (cell != null && cell.CI.ToString() == cl.Code)
                    {
                        return true;
                    }
                }
                else
                {
                    break;
                }
            }
            return false;
        }

    }
}
