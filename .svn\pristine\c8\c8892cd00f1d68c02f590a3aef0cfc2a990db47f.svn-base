﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Net
{
    public class StationFilesCountDBOperator : DIYSQLBase
    {
        readonly Dictionary<int, int> stationFileCountDic;
        public StationFilesCountDBOperator(Dictionary<int, int> btsFileCountDic, int dbid)
            : base(MainModel.GetInstance(), dbid)
        {
            stationFileCountDic = btsFileCountDic;
        }
        protected override string getSqlTextString()
        {
            StringBuilder sb = new StringBuilder();
            foreach (var varPair in stationFileCountDic)
            {
                sb.Append(string.Format("exec sp_probchk_singleStation_filecount_add {0},{1};", varPair.Key, varPair.Value));
            }
            return sb.ToString();
        }
        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                if (MainDB)
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
                }
                else
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
                }

                E_VType[] retArrDef = getSqlRetTypeArr();
                string strsql = getSqlTextString();
                string[] strArr = strsql.Split(';');
                int curIdx = 0;
                while (curIdx < strArr.Length)
                {
                    StringBuilder txt = new StringBuilder();
                    for (; curIdx < strArr.Length; curIdx++)
                    {
                        txt.Append(strArr[curIdx] + ";");
                        if (txt.Length > 6000)
                        {
                            break;
                        }
                    }
                    package.Content.PrepareAddParam();
                    package.Content.AddParam(txt.ToString());
                    StringBuilder sb = getRetArrDefStr(retArrDef);
                    package.Content.AddParam(sb.ToString());
                    clientProxy.Send();
                    System.Threading.Thread.Sleep(100);
                }
                receiveRetData(clientProxy);
            }
            catch
            {
                //continue
            }
        }

        private static StringBuilder getRetArrDefStr(E_VType[] retArrDef)
        {
            StringBuilder sb = new StringBuilder();
            if (retArrDef != null)
            {
                for (int i = 0; i < retArrDef.Length; i++)
                {
                    sb.Append((int)retArrDef[i]);
                    if (i < retArrDef.Length - 1)
                    {
                        sb.Append(",");
                    }
                }
            }

            return sb;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_Int;
            return rType;
        }

        public override string Name
        {
            get { return "更新新站文件数量表"; }
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
        }
    }
}
