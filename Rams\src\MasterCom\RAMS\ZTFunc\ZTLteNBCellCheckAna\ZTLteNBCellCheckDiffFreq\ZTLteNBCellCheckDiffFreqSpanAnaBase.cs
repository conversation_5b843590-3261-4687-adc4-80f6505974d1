﻿using System.Windows.Forms;
using MasterCom.RAMS.Model;
using System.Collections.Generic;

/*
 *  本功能的添加由于湖北移动按照功能点进行费用的计算，因此将异频和异频测量过长需要分开为两个功能点
 *  本功能的分析代码与异频代码相同，只是输出结果不同
 *  输出只需要保留：文件、主服小区、切换小区、下发测量到切换之间的时长（如果未下发异频测量，不显示）
 */
namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteNBCellCheckDiffFreqSpanAnaBase : ZTLteNBCellCheckDiffFreqAnaBase
    {
        public ZTLteNBCellCheckDiffFreqSpanAnaBase(MainModel mainModel)
            : base(mainModel)
        {

        }

        /// <summary>
        /// 显示结果
        /// </summary>
        protected override void fireShowForm()
        {
            //过滤掉没有下发异频测量的记录
            List<ZTLteNBCellCheckDiffFreqFileItem> resultListNew;
            resultListNew = filterResult();           

            if (resultListNew.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }

            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTLteNBCellCheckDiffFreqSpanAnaListForm).FullName);
            ZTLteNBCellCheckDiffFreqSpanAnaListForm lteNBCellCheckListForm = obj == null ? null : obj as ZTLteNBCellCheckDiffFreqSpanAnaListForm;
            if (lteNBCellCheckListForm == null || lteNBCellCheckListForm.IsDisposed)
            {
                lteNBCellCheckListForm = new ZTLteNBCellCheckDiffFreqSpanAnaListForm(MainModel);
            }

            lteNBCellCheckListForm.FillData(resultListNew);
            if (!lteNBCellCheckListForm.Visible)
            {
                lteNBCellCheckListForm.Show(MainModel.MainForm);
            }
        }

        private List<ZTLteNBCellCheckDiffFreqFileItem> filterResult()
        {
            //过滤掉没有下发异频测量的记录
            List<ZTLteNBCellCheckDiffFreqFileItem> resultListNew = new List<ZTLteNBCellCheckDiffFreqFileItem>();

            foreach (ZTLteNBCellCheckDiffFreqFileItem fileItem in resultList)
            {
                ZTLteNBCellCheckDiffFreqFileItem fileItemNew = new ZTLteNBCellCheckDiffFreqFileItem();

                foreach (ZTLteNBCellCheckDiffFreqCellItem cellItem in fileItem.CellList)
                {
                    if (cellItem.SpanReConfig2HO != "")  //没有差值，没有下发异频测量报告
                    {
                        cellItem.SN = fileItemNew.CellList.Count + 1;
                        fileItemNew.CellList.Add(cellItem);
                    }
                }

                if (fileItemNew.CellList.Count > 0)
                {
                    fileItemNew.SN = resultListNew.Count + 1;
                    fileItemNew.FileName = fileItem.FileName;
                    resultListNew.Add(fileItemNew);
                }
            }

            return resultListNew;
        }
    }
}