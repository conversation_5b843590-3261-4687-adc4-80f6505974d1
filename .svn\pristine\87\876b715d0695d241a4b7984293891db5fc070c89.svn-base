﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

using MasterCom.Util;
using MasterCom.MTGis;
using DevExpress.XtraCharts;
using MasterCom.Util.UiEx;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteMgrsRsrpRangeResult : LteMgrsResultControlBase
    {
        LteMgrsFuncItem funcItem = null;

        public LteMgrsRsrpRangeResult()
        {
            InitializeComponent();
            InitCbxFreqType();
            miExportExcel.Click += MiExportExcel_Click;
            miEditRange.Click += MiEditRange_Click;
            miShowChart.Click += MiShowChart_Click;
            cbxFreqType.SelectedIndexChanged += CbxFreqType_SelectedChanged;

            miExportAllExcel.Click += base.MiExportExcelAll_Click;
            miExportWord.Click += base.MiExportWord_Click;
        }

        public override string Desc
        {
            get { return "场强分布"; }
        }

        public override void DrawOnLayer()
        {
            if (funcItem == null)
            {
                return;
            }

            LteMgrsRsrpRangeStater stater = funcItem.Stater as LteMgrsRsrpRangeStater;
            List<LteMgrsDrawItem> drawList = stater.GetDrawList(funcItem.CurQueryCitys[funcItem.SelectedCityIndex],
                (LteMgrsRsrpBandType)EnumDescriptionAttribute.Parse(typeof(LteMgrsRsrpBandType), cbxFreqType.SelectedItem as string));
            LteMgrsLayer.DrawList = drawList;
            DbRect rect = LteMgrsLayer.GetDrawBound(drawList);
            if (rect != null)
            {
                MainModel.MainForm.GetMapForm().GoToView(rect);
                SetNormalMapScale();
            }
            LteMgrsLayer.LegendGroup = stater.GetLegend();
            MainModel.RefreshLegend();
        }

        public void FillData(LteMgrsFuncItem funcItem)
        {
            this.funcItem = funcItem;
            cbxFreqType.SelectedItem = EnumDescriptionAttribute.GetText((LteMgrsRsrpBandType)funcItem.FuncCondtion);
            RefreshResult();
        }

        protected override void ExportAllExcel(string savePath)
        {
            for (int i = 0; i < cbxFreqType.Items.Count; ++i)
            {
                cbxFreqType.SelectedIndex = i;
                string sheetName = cbxFreqType.SelectedItem.ToString() + Desc;
                string fileName = System.IO.Path.Combine(savePath, sheetName + ".xlsx");
                ExcelNPOIManager.ExportToExcel(gridView1, fileName, sheetName);
            }
        }

        protected override bool ExportWord(WordControl word, string title)
        {
            if (title == "GIS场强分布")
            {
                WaitTextBox.Text = "正在导出 " + title + "...";
                ExportWord_A(word);
                return true;
            }
            else if (title == "场强分布图表")
            {
                WaitTextBox.Text = "正在导出 " + title + "...";
                ExportWord_B(word);
                return true;
            }
            return false;
        }

        private void CbxFreqType_SelectedChanged(object sender, EventArgs e)
        {
            RefreshResult();
            DrawOnLayer();
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView1);
        }

        private void MiEditRange_Click(object sender, EventArgs e)
        {
            LteMgrsColorRangeSettingDlg dlg = new LteMgrsColorRangeSettingDlg();
            dlg.FixMinMax(LteMgrsRsrpRangeStater.Ranges.Minimum, LteMgrsRsrpRangeStater.Ranges.Maximum);
            dlg.MakeRangeModeOnly();
            dlg.FillColorRanges(LteMgrsRsrpRangeStater.Ranges.ColorRanges);
            dlg.InvalidatePointColor = LteMgrsRsrpRangeStater.Ranges.InvalidColor;

            if (DialogResult.OK != dlg.ShowDialog(this))
            {
                return;
            }
            LteMgrsRsrpRangeStater.Ranges.ColorRanges = new List<MasterCom.MControls.ColorRange>(dlg.ColorRanges);
            LteMgrsRsrpRangeStater.Ranges.InvalidColor = dlg.InvalidatePointColor;
            LteMgrsRsrpRangeStater.Ranges.SaveColorRange("Rsrp");
            RefreshResult();
            LteMgrsRsrpRangeStater stater = funcItem.Stater as LteMgrsRsrpRangeStater;
            LteMgrsLayer.LegendGroup = stater.GetLegend();
            DrawOnLayer();
        }

        private void MiShowChart_Click(object sender, EventArgs e)
        {
            LteMgrsChartForm chartForm = MainModel.GetObjectFromBlackboard(typeof(LteMgrsChartForm).FullName) as LteMgrsChartForm;
            if (chartForm == null || chartForm.IsDisposed)
            {
                chartForm = new LteMgrsChartForm(MainModel);
            }

            LteMgrsRsrpRangeStater stater = funcItem.Stater as LteMgrsRsrpRangeStater;
            ChartControl chart = stater.GetChart(gridControl1.DataSource as DataTable);
            chartForm.FillChart(chart, "场强分布占比图表");
            if (!chartForm.Visible)
            {
                chartForm.Show(MainModel.MainForm);
            }
        }

        private void InitCbxFreqType()
        {
            cbxFreqType.Items.Clear();
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsRsrpBandType.Top));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsRsrpBandType.SingleD));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsRsrpBandType.SingleF));
            cbxFreqType.SelectedIndex = 0;
        }

        private void RefreshResult()
        {
            LteMgrsRsrpRangeStater stater = funcItem.Stater as LteMgrsRsrpRangeStater;
            DataTable table = stater.GetTable(funcItem.CurQueryCitys[funcItem.SelectedCityIndex],
                (LteMgrsRsrpBandType)EnumDescriptionAttribute.Parse(typeof(LteMgrsRsrpBandType), cbxFreqType.SelectedItem as string));
            gridControl1.DataSource = table;
            gridControl1.RefreshDataSource();
            gridView1.PopulateColumns();

            LteMgrsChartForm chartForm = MainModel.GetObjectFromBlackboard(typeof(LteMgrsChartForm).FullName) as LteMgrsChartForm;
            if (chartForm != null && !chartForm.IsDisposed)
            {
                miShowChart.PerformClick();
            }
        }

        private void ExportWord_A(WordControl word)
        {
            cbxFreqType.SelectedIndex = 0;
            string text = cbxFreqType.SelectedItem.ToString() + "场强分布";
            word.InsertText(text + "渲染如下：", "正文");
            word.NewLine();
            DataTable curTable = gridControl1.DataSource as DataTable;
            if (curTable.Rows.Count != 0)
            {
                this.DrawOnLayer();
                word.InsertPicture(LteMgrsScreenShoter.GisScreenshot("LTE_" + text));
                word.NewLine();
            }

            cbxFreqType.SelectedIndex = 1;
            text = cbxFreqType.SelectedItem.ToString() + "场强分布";
            word.InsertText(text + "渲染如下：", "正文");
            word.NewLine();
            curTable = gridControl1.DataSource as DataTable;
            if (curTable.Rows.Count != 0)
            {
                this.DrawOnLayer();
                word.InsertPicture(LteMgrsScreenShoter.GisScreenshot("LTE_" + text));
                word.NewLine();
            }

            cbxFreqType.SelectedIndex = 2;
            text = cbxFreqType.SelectedItem.ToString() + "场强分布";
            word.InsertText(text + "渲染如下：", "正文");
            word.NewLine();
            curTable = gridControl1.DataSource as DataTable;
            if (curTable.Rows.Count != 0)
            {
                this.DrawOnLayer();
                word.InsertPicture(LteMgrsScreenShoter.GisScreenshot("LTE_" + text));
                word.NewLine();
            }
        }

        private void ExportWord_B(WordControl word)
        {
            cbxFreqType.SelectedIndex = 0;
            LteMgrsRsrpRangeStater stater = funcItem.Stater as LteMgrsRsrpRangeStater;
            DataTable dt = gridControl1.DataSource as DataTable;

            string text = "最强信号区间分布如下：";
            word.InsertText(text, "正文");
            word.NewLine();
            (word as LteMgrsWordControl).InsertGridView(gridView1, LteMgrsRsrpRangeStater.Ranges.ColorRanges);

            text = "最强信号占比图表如下：";
            word.InsertText(text, "正文");
            word.NewLine();

            ChartControl chart = stater.GetChart(dt);
            chart.Dock = DockStyle.Fill;
            Form tmpForm = new Form();
            tmpForm.Size = new Size(600, 400);
            tmpForm.Controls.Add(chart);

            string fileName = "LTE_最强信号占比图表";
            string picFile = LteMgrsScreenShoter.ChartScreenshot(chart, dt.Rows.Count, fileName);
            word.InsertPicture(picFile);
            tmpForm.Dispose();
        }

    }
}
