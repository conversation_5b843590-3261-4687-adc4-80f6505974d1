﻿namespace MasterCom.RAMS.ZTFunc.ZTLastWeakRoadAna
{
    partial class TdXtraLastWeakPoadForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraCharts.XYDiagram xyDiagram2 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series3 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel4 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.Series series4 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel5 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel6 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.tableLayoutPanel2 = new System.Windows.Forms.TableLayoutPanel();
            this.chartControl1 = new DevExpress.XtraCharts.ChartControl();
            this.gridControl5 = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemDIYReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemClearFly = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemShowFly = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemToExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemLable = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView9 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn129 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn130 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn131 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn135 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn132 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn136 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn133 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn134 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.bandedGridColumn24 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn25 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn26 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn27 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn28 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn29 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn30 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn31 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn32 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn33 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn34 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn5 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn6 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn7 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn8 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn9 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn10 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn11 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn12 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn13 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn14 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn15 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn16 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn17 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn18 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn19 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn20 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn21 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn22 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn23 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn137 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn39 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn44 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn45 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn46 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn47 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn48 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn49 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn50 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn51 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn109 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn110 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn111 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn112 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn113 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn207 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.xtraTabPage3 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControl2 = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn52 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn53 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn54 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn55 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn56 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn57 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn58 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn59 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn138 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn208 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView4 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.xtraTabPage4 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControl3 = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridView5 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn60 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn61 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn62 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn63 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn64 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn65 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn66 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn67 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn68 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn69 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn70 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn71 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn72 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn73 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn74 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn75 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn76 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn77 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn78 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn79 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn80 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn81 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn82 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn83 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn84 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn85 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn86 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn87 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn88 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn89 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn90 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn91 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn139 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn209 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView6 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.xtraTabPage5 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControl4 = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridView7 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn92 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn93 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn94 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn95 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn96 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn97 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn98 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn99 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn100 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn101 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn102 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn103 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn104 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn105 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn106 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn107 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn108 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn114 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn115 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn116 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn117 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn118 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn119 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn120 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn121 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn122 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn123 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn124 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn125 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn126 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn127 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn128 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn140 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn210 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView8 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.xtraTabPage6 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControl6 = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView5 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridView10 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn141 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn142 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn143 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn144 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn145 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn146 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn147 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn148 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn149 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn150 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn151 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn152 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn153 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn154 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn155 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn156 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn157 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn158 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn159 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn160 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn161 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn162 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn163 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn164 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn165 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn166 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn167 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn168 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn169 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn170 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn171 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn172 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn173 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn211 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView11 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.xtraTabPage7 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControl7 = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView6 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridView12 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn174 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn175 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn176 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn177 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn178 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn179 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn180 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn181 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn182 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn183 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn184 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn185 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn186 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn187 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn188 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn189 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn190 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn191 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn192 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn193 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn194 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn195 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn196 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn197 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn198 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn199 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn200 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn201 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn202 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn203 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn204 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn205 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn206 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn212 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView13 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.btnClearFly = new System.Windows.Forms.Button();
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.panel1 = new System.Windows.Forms.Panel();
            this.label1 = new System.Windows.Forms.Label();
            this.btnNext1 = new System.Windows.Forms.Button();
            this.gridBand7 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand1 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand8 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand9 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand10 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand11 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn35 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn36 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn37 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn38 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn39 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn40 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn41 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn42 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn43 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn44 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn45 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn46 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn47 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn48 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn49 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn50 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn51 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn52 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn53 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn54 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn55 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn56 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn57 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn58 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn59 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn60 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn61 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn62 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn63 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn64 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn65 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn66 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn67 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn68 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand2 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand12 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand13 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand14 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand15 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand16 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn69 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn70 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn71 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn72 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn73 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn74 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn75 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn76 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn77 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn78 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn79 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn80 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn81 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn82 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn83 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn84 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn85 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn86 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn87 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn88 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn89 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn90 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn91 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn92 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn93 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn94 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn95 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn96 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn97 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn98 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn99 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn100 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn101 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn102 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand3 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand17 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand18 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand19 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand20 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand21 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn103 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn104 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn105 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn106 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn107 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn108 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn109 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn110 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn111 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn112 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn113 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn114 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn115 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn116 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn117 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn118 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn119 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn120 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn121 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn122 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn123 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn124 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn125 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn126 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn127 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn128 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn129 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn130 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn131 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn132 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn133 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn134 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn135 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn136 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand4 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand22 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand23 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand24 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand25 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand26 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn137 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn138 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn139 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn140 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn141 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn142 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn143 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn144 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn145 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn146 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn147 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn148 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn149 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn150 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn151 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn152 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn153 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn154 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn155 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn156 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn157 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn158 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn159 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn160 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn161 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn162 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn163 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn164 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn165 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn166 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn167 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn168 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn169 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn170 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand5 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand27 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand28 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand29 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand30 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand31 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn171 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn172 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn173 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn174 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn175 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn176 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn177 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn178 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn179 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn180 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn181 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn182 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn183 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn184 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn185 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn186 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn187 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn188 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn189 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn190 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn191 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn192 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn193 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn194 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn195 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn196 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn197 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn198 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn199 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn200 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn201 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn202 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn203 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn204 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand6 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand32 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand33 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand34 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand35 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand36 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            this.tableLayoutPanel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl5)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView9)).BeginInit();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            this.xtraTabPage3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).BeginInit();
            this.xtraTabPage4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).BeginInit();
            this.xtraTabPage5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView8)).BeginInit();
            this.xtraTabPage6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView11)).BeginInit();
            this.xtraTabPage7.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView13)).BeginInit();
            this.tableLayoutPanel1.SuspendLayout();
            this.panel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(3, 3);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(843, 411);
            this.xtraTabControl1.TabIndex = 0;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2,
            this.xtraTabPage3,
            this.xtraTabPage4,
            this.xtraTabPage5,
            this.xtraTabPage6,
            this.xtraTabPage7});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.tableLayoutPanel2);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(836, 381);
            this.xtraTabPage1.Text = "概况";
            // 
            // tableLayoutPanel2
            // 
            this.tableLayoutPanel2.ColumnCount = 1;
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel2.Controls.Add(this.chartControl1, 0, 1);
            this.tableLayoutPanel2.Controls.Add(this.gridControl5, 0, 0);
            this.tableLayoutPanel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel2.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel2.Name = "tableLayoutPanel2";
            this.tableLayoutPanel2.RowCount = 2;
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 150F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel2.Size = new System.Drawing.Size(836, 381);
            this.tableLayoutPanel2.TabIndex = 3;
            // 
            // chartControl1
            // 
            xyDiagram2.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram2.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram2.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram2.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram2.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram2.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControl1.Diagram = xyDiagram2;
            this.chartControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControl1.Location = new System.Drawing.Point(3, 153);
            this.chartControl1.Name = "chartControl1";
            sideBySideBarSeriesLabel4.LineVisible = true;
            series3.Label = sideBySideBarSeriesLabel4;
            series3.Name = "Series 1";
            sideBySideBarSeriesLabel5.LineVisible = true;
            series4.Label = sideBySideBarSeriesLabel5;
            series4.Name = "Series 2";
            this.chartControl1.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series3,
        series4};
            sideBySideBarSeriesLabel6.LineVisible = true;
            this.chartControl1.SeriesTemplate.Label = sideBySideBarSeriesLabel6;
            this.chartControl1.Size = new System.Drawing.Size(830, 225);
            this.chartControl1.TabIndex = 0;
            // 
            // gridControl5
            // 
            this.gridControl5.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl5.Location = new System.Drawing.Point(3, 3);
            this.gridControl5.MainView = this.gridView9;
            this.gridControl5.Name = "gridControl5";
            this.gridControl5.Size = new System.Drawing.Size(830, 144);
            this.gridControl5.TabIndex = 0;
            this.gridControl5.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView9});
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemDIYReplay,
            this.ToolStripMenuItemClearFly,
            this.ToolStripMenuItemShowFly,
            this.ToolStripMenuItemToExcel,
            this.ToolStripMenuItemLable});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(135, 114);
            // 
            // ToolStripMenuItemDIYReplay
            // 
            this.ToolStripMenuItemDIYReplay.Name = "ToolStripMenuItemDIYReplay";
            this.ToolStripMenuItemDIYReplay.Size = new System.Drawing.Size(134, 22);
            this.ToolStripMenuItemDIYReplay.Text = "回放";
            this.ToolStripMenuItemDIYReplay.Click += new System.EventHandler(this.ToolStripMenuItemDIYReplay_Click);
            // 
            // ToolStripMenuItemClearFly
            // 
            this.ToolStripMenuItemClearFly.Name = "ToolStripMenuItemClearFly";
            this.ToolStripMenuItemClearFly.Size = new System.Drawing.Size(134, 22);
            this.ToolStripMenuItemClearFly.Text = "清除飞线";
            this.ToolStripMenuItemClearFly.Click += new System.EventHandler(this.ToolStripMenuItemClearFly_Click);
            // 
            // ToolStripMenuItemShowFly
            // 
            this.ToolStripMenuItemShowFly.Name = "ToolStripMenuItemShowFly";
            this.ToolStripMenuItemShowFly.Size = new System.Drawing.Size(134, 22);
            this.ToolStripMenuItemShowFly.Text = "显示飞线";
            this.ToolStripMenuItemShowFly.Click += new System.EventHandler(this.ToolStripMenuItemShowFly_Click);
            // 
            // ToolStripMenuItemToExcel
            // 
            this.ToolStripMenuItemToExcel.Name = "ToolStripMenuItemToExcel";
            this.ToolStripMenuItemToExcel.Size = new System.Drawing.Size(134, 22);
            this.ToolStripMenuItemToExcel.Text = "导出EXCEL";
            this.ToolStripMenuItemToExcel.Click += new System.EventHandler(this.ToolStripMenuItemToExcel_Click);
            // 
            // ToolStripMenuItemLable
            // 
            this.ToolStripMenuItemLable.Name = "ToolStripMenuItemLable";
            this.ToolStripMenuItemLable.Size = new System.Drawing.Size(134, 22);
            this.ToolStripMenuItemLable.Text = "显示标签";
            this.ToolStripMenuItemLable.Click += new System.EventHandler(this.ToolStripMenuItemLable_Click);
            // 
            // gridView9
            // 
            this.gridView9.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn129,
            this.gridColumn130,
            this.gridColumn131,
            this.gridColumn135,
            this.gridColumn132,
            this.gridColumn136,
            this.gridColumn133,
            this.gridColumn134});
            this.gridView9.GridControl = this.gridControl5;
            this.gridView9.Name = "gridView9";
            this.gridView9.OptionsBehavior.Editable = false;
            this.gridView9.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView9.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gridView9.OptionsView.ShowGroupPanel = false;
            this.gridView9.EndSorting += new System.EventHandler(this.gridView9_EndSorting);
            this.gridView9.RowCellClick += new DevExpress.XtraGrid.Views.Grid.RowCellClickEventHandler(this.gridView9_RowCellClick);
            // 
            // gridColumn129
            // 
            this.gridColumn129.Caption = "类型";
            this.gridColumn129.FieldName = "StrType";
            this.gridColumn129.Name = "gridColumn129";
            this.gridColumn129.Visible = true;
            this.gridColumn129.VisibleIndex = 0;
            this.gridColumn129.Width = 59;
            // 
            // gridColumn130
            // 
            this.gridColumn130.Caption = "数量";
            this.gridColumn130.FieldName = "INum";
            this.gridColumn130.Name = "gridColumn130";
            this.gridColumn130.Visible = true;
            this.gridColumn130.VisibleIndex = 1;
            this.gridColumn130.Width = 39;
            // 
            // gridColumn131
            // 
            this.gridColumn131.Caption = "时间(s)";
            this.gridColumn131.FieldName = "IDuration";
            this.gridColumn131.Name = "gridColumn131";
            this.gridColumn131.Visible = true;
            this.gridColumn131.VisibleIndex = 2;
            this.gridColumn131.Width = 49;
            // 
            // gridColumn135
            // 
            this.gridColumn135.Caption = "总里程(m)";
            this.gridColumn135.FieldName = "IAllDistance";
            this.gridColumn135.Name = "gridColumn135";
            this.gridColumn135.Visible = true;
            this.gridColumn135.VisibleIndex = 3;
            this.gridColumn135.Width = 66;
            // 
            // gridColumn132
            // 
            this.gridColumn132.Caption = "问题里程(m)";
            this.gridColumn132.FieldName = "IDistance";
            this.gridColumn132.Name = "gridColumn132";
            this.gridColumn132.Visible = true;
            this.gridColumn132.VisibleIndex = 4;
            this.gridColumn132.Width = 54;
            // 
            // gridColumn136
            // 
            this.gridColumn136.Caption = "差道路占比(%)";
            this.gridColumn136.DisplayFormat.FormatString = "00.00%";
            this.gridColumn136.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn136.FieldName = "FRate";
            this.gridColumn136.GroupFormat.FormatString = "00.00%";
            this.gridColumn136.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn136.Name = "gridColumn136";
            this.gridColumn136.Visible = true;
            this.gridColumn136.VisibleIndex = 5;
            this.gridColumn136.Width = 92;
            // 
            // gridColumn133
            // 
            this.gridColumn133.Caption = "小区数量";
            this.gridColumn133.FieldName = "ICellNum";
            this.gridColumn133.Name = "gridColumn133";
            this.gridColumn133.Visible = true;
            this.gridColumn133.VisibleIndex = 6;
            this.gridColumn133.Width = 65;
            // 
            // gridColumn134
            // 
            this.gridColumn134.Caption = "采样点";
            this.gridColumn134.FieldName = "ISampleId";
            this.gridColumn134.Name = "gridColumn134";
            this.gridColumn134.Visible = true;
            this.gridColumn134.VisibleIndex = 7;
            this.gridColumn134.Width = 62;
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.gridControl1);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(836, 381);
            this.xtraTabPage2.Text = "TD弱覆盖";
            // 
            // gridControl1
            // 
            this.gridControl1.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl1.Location = new System.Drawing.Point(0, 0);
            this.gridControl1.MainView = this.bandedGridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.Size = new System.Drawing.Size(836, 381);
            this.gridControl1.TabIndex = 0;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView1,
            this.gridView1,
            this.gridView2});
            this.gridControl1.DoubleClick += new System.EventHandler(this.gridControl1_DoubleClick);
            // 
            // bandedGridView1
            // 
            this.bandedGridView1.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand7,
            this.gridBand1,
            this.gridBand8,
            this.gridBand9,
            this.gridBand10,
            this.gridBand11});
            this.bandedGridView1.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn1,
            this.bandedGridColumn2,
            this.bandedGridColumn3,
            this.bandedGridColumn4,
            this.bandedGridColumn5,
            this.bandedGridColumn6,
            this.bandedGridColumn7,
            this.bandedGridColumn8,
            this.bandedGridColumn9,
            this.bandedGridColumn10,
            this.bandedGridColumn11,
            this.bandedGridColumn12,
            this.bandedGridColumn13,
            this.bandedGridColumn14,
            this.bandedGridColumn15,
            this.bandedGridColumn16,
            this.bandedGridColumn17,
            this.bandedGridColumn18,
            this.bandedGridColumn19,
            this.bandedGridColumn20,
            this.bandedGridColumn21,
            this.bandedGridColumn22,
            this.bandedGridColumn23,
            this.bandedGridColumn24,
            this.bandedGridColumn25,
            this.bandedGridColumn26,
            this.bandedGridColumn27,
            this.bandedGridColumn28,
            this.bandedGridColumn29,
            this.bandedGridColumn30,
            this.bandedGridColumn31,
            this.bandedGridColumn32,
            this.bandedGridColumn33,
            this.bandedGridColumn34});
            this.bandedGridView1.GridControl = this.gridControl1;
            this.bandedGridView1.Name = "bandedGridView1";
            this.bandedGridView1.OptionsBehavior.Editable = false;
            this.bandedGridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.bandedGridView1.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView1.OptionsView.ShowGroupPanel = false;
            // 
            // bandedGridColumn24
            // 
            this.bandedGridColumn24.Caption = "TOP1小区";
            this.bandedGridColumn24.CustomizationCaption = "TOP1小区";
            this.bandedGridColumn24.FieldName = "Cell1";
            this.bandedGridColumn24.Name = "bandedGridColumn24";
            this.bandedGridColumn24.Visible = true;
            this.bandedGridColumn24.Width = 85;
            // 
            // bandedGridColumn25
            // 
            this.bandedGridColumn25.Caption = "TOP2小区";
            this.bandedGridColumn25.CustomizationCaption = "TOP2小区";
            this.bandedGridColumn25.FieldName = "Cell2";
            this.bandedGridColumn25.Name = "bandedGridColumn25";
            this.bandedGridColumn25.Visible = true;
            this.bandedGridColumn25.Width = 85;
            // 
            // bandedGridColumn26
            // 
            this.bandedGridColumn26.Caption = "TOP3小区";
            this.bandedGridColumn26.CustomizationCaption = "TOP3小区";
            this.bandedGridColumn26.FieldName = "Cell3";
            this.bandedGridColumn26.Name = "bandedGridColumn26";
            this.bandedGridColumn26.Visible = true;
            this.bandedGridColumn26.Width = 85;
            // 
            // bandedGridColumn27
            // 
            this.bandedGridColumn27.Caption = "所属网格";
            this.bandedGridColumn27.CustomizationCaption = "所属网格";
            this.bandedGridColumn27.FieldName = "Strgrid";
            this.bandedGridColumn27.Name = "bandedGridColumn27";
            this.bandedGridColumn27.Visible = true;
            // 
            // bandedGridColumn28
            // 
            this.bandedGridColumn28.Caption = "所属道路";
            this.bandedGridColumn28.CustomizationCaption = "所属道路";
            this.bandedGridColumn28.FieldName = "Strroad";
            this.bandedGridColumn28.Name = "bandedGridColumn28";
            this.bandedGridColumn28.Visible = true;
            // 
            // bandedGridColumn29
            // 
            this.bandedGridColumn29.Caption = "经度";
            this.bandedGridColumn29.CustomizationCaption = "经度";
            this.bandedGridColumn29.FieldName = "Imlongitude";
            this.bandedGridColumn29.Name = "bandedGridColumn29";
            this.bandedGridColumn29.Visible = true;
            // 
            // bandedGridColumn30
            // 
            this.bandedGridColumn30.Caption = "纬度";
            this.bandedGridColumn30.CustomizationCaption = "纬度";
            this.bandedGridColumn30.FieldName = "Imlatitude";
            this.bandedGridColumn30.Name = "bandedGridColumn30";
            this.bandedGridColumn30.Visible = true;
            // 
            // bandedGridColumn1
            // 
            this.bandedGridColumn1.Caption = "距离(米)";
            this.bandedGridColumn1.CustomizationCaption = "距离(米)";
            this.bandedGridColumn1.FieldName = "Idistance";
            this.bandedGridColumn1.Name = "bandedGridColumn1";
            this.bandedGridColumn1.Visible = true;
            // 
            // bandedGridColumn2
            // 
            this.bandedGridColumn2.Caption = "时长(秒)";
            this.bandedGridColumn2.CustomizationCaption = "时长(秒)";
            this.bandedGridColumn2.FieldName = "Iduration";
            this.bandedGridColumn2.Name = "bandedGridColumn2";
            this.bandedGridColumn2.Visible = true;
            // 
            // bandedGridColumn31
            // 
            this.bandedGridColumn31.Caption = "ifileid";
            this.bandedGridColumn31.CustomizationCaption = "ifileid";
            this.bandedGridColumn31.FieldName = "Ifileid";
            this.bandedGridColumn31.Name = "bandedGridColumn31";
            // 
            // bandedGridColumn32
            // 
            this.bandedGridColumn32.Caption = "istime";
            this.bandedGridColumn32.CustomizationCaption = "istime";
            this.bandedGridColumn32.FieldName = "Istime";
            this.bandedGridColumn32.Name = "bandedGridColumn32";
            // 
            // bandedGridColumn33
            // 
            this.bandedGridColumn33.Caption = "ietime";
            this.bandedGridColumn33.CustomizationCaption = "ietime";
            this.bandedGridColumn33.FieldName = "Ietime";
            this.bandedGridColumn33.Name = "bandedGridColumn33";
            // 
            // bandedGridColumn34
            // 
            this.bandedGridColumn34.Caption = "gridColumn207";
            this.bandedGridColumn34.CustomizationCaption = "gridColumn207";
            this.bandedGridColumn34.FieldName = "Iid";
            this.bandedGridColumn34.Name = "bandedGridColumn34";
            // 
            // bandedGridColumn3
            // 
            this.bandedGridColumn3.Caption = "均值";
            this.bandedGridColumn3.CustomizationCaption = "电平均值";
            this.bandedGridColumn3.FieldName = "Rxlmean";
            this.bandedGridColumn3.Name = "bandedGridColumn3";
            this.bandedGridColumn3.Visible = true;
            // 
            // bandedGridColumn4
            // 
            this.bandedGridColumn4.Caption = "[-75,-10]";
            this.bandedGridColumn4.CustomizationCaption = "电平[-75,-10]";
            this.bandedGridColumn4.FieldName = "Rxl75";
            this.bandedGridColumn4.Name = "bandedGridColumn4";
            this.bandedGridColumn4.Visible = true;
            // 
            // bandedGridColumn5
            // 
            this.bandedGridColumn5.Caption = "[-80,-75]";
            this.bandedGridColumn5.CustomizationCaption = "电平[-80,-75]";
            this.bandedGridColumn5.FieldName = "Rxl76_80";
            this.bandedGridColumn5.Name = "bandedGridColumn5";
            this.bandedGridColumn5.Visible = true;
            // 
            // bandedGridColumn6
            // 
            this.bandedGridColumn6.Caption = "[-85,-80]";
            this.bandedGridColumn6.CustomizationCaption = "电平[-85,-80]";
            this.bandedGridColumn6.FieldName = "Rxl81_85";
            this.bandedGridColumn6.Name = "bandedGridColumn6";
            this.bandedGridColumn6.Visible = true;
            // 
            // bandedGridColumn7
            // 
            this.bandedGridColumn7.Caption = "[-90,-85]";
            this.bandedGridColumn7.CustomizationCaption = "电平[-90,-85]";
            this.bandedGridColumn7.FieldName = "Rxl86_90";
            this.bandedGridColumn7.Name = "bandedGridColumn7";
            this.bandedGridColumn7.Visible = true;
            // 
            // bandedGridColumn8
            // 
            this.bandedGridColumn8.Caption = "[-94,-90]";
            this.bandedGridColumn8.CustomizationCaption = "电平[-94,-90]";
            this.bandedGridColumn8.FieldName = "Rxl91_94";
            this.bandedGridColumn8.Name = "bandedGridColumn8";
            this.bandedGridColumn8.Visible = true;
            // 
            // bandedGridColumn9
            // 
            this.bandedGridColumn9.Caption = "[-140,-94]";
            this.bandedGridColumn9.CustomizationCaption = "电平[-140,-94]";
            this.bandedGridColumn9.FieldName = "Rxl94";
            this.bandedGridColumn9.Name = "bandedGridColumn9";
            this.bandedGridColumn9.Visible = true;
            // 
            // bandedGridColumn10
            // 
            this.bandedGridColumn10.Caption = "均值";
            this.bandedGridColumn10.CustomizationCaption = "DPCH C/I均值";
            this.bandedGridColumn10.FieldName = "Dc2imean";
            this.bandedGridColumn10.Name = "bandedGridColumn10";
            this.bandedGridColumn10.Visible = true;
            // 
            // bandedGridColumn11
            // 
            this.bandedGridColumn11.Caption = "[-20,-10]";
            this.bandedGridColumn11.CustomizationCaption = "C/I[-20,-10]";
            this.bandedGridColumn11.FieldName = "Dc2iF10";
            this.bandedGridColumn11.Name = "bandedGridColumn11";
            this.bandedGridColumn11.Visible = true;
            // 
            // bandedGridColumn12
            // 
            this.bandedGridColumn12.Caption = "[-10,-3]";
            this.bandedGridColumn12.CustomizationCaption = "C/I[-10,-3]";
            this.bandedGridColumn12.FieldName = "Dc2iF10TF3";
            this.bandedGridColumn12.Name = "bandedGridColumn12";
            this.bandedGridColumn12.Visible = true;
            // 
            // bandedGridColumn13
            // 
            this.bandedGridColumn13.Caption = "[-3,15]";
            this.bandedGridColumn13.CustomizationCaption = "C/I[-3,15]";
            this.bandedGridColumn13.FieldName = "Dc2iF3T15";
            this.bandedGridColumn13.Name = "bandedGridColumn13";
            this.bandedGridColumn13.Visible = true;
            // 
            // bandedGridColumn14
            // 
            this.bandedGridColumn14.Caption = "[15,25]";
            this.bandedGridColumn14.CustomizationCaption = "C/I[15,25]";
            this.bandedGridColumn14.FieldName = "Dc2i15";
            this.bandedGridColumn14.Name = "bandedGridColumn14";
            this.bandedGridColumn14.Visible = true;
            // 
            // bandedGridColumn15
            // 
            this.bandedGridColumn15.Caption = "均值";
            this.bandedGridColumn15.CustomizationCaption = "MOS均值";
            this.bandedGridColumn15.FieldName = "Pesqmean";
            this.bandedGridColumn15.Name = "bandedGridColumn15";
            this.bandedGridColumn15.Visible = true;
            // 
            // bandedGridColumn16
            // 
            this.bandedGridColumn16.Caption = "[0,2.8]";
            this.bandedGridColumn16.CustomizationCaption = "MOS[0,2.8]";
            this.bandedGridColumn16.FieldName = "Pesq28";
            this.bandedGridColumn16.Name = "bandedGridColumn16";
            this.bandedGridColumn16.Visible = true;
            // 
            // bandedGridColumn17
            // 
            this.bandedGridColumn17.Caption = "[2.8,3]";
            this.bandedGridColumn17.CustomizationCaption = "MOS[2.8,3]";
            this.bandedGridColumn17.FieldName = "Pesq28_30";
            this.bandedGridColumn17.Name = "bandedGridColumn17";
            this.bandedGridColumn17.Visible = true;
            // 
            // bandedGridColumn18
            // 
            this.bandedGridColumn18.Caption = "[3,5]";
            this.bandedGridColumn18.CustomizationCaption = "MOS[3,5]";
            this.bandedGridColumn18.FieldName = "Pesq30";
            this.bandedGridColumn18.Name = "bandedGridColumn18";
            this.bandedGridColumn18.Visible = true;
            // 
            // bandedGridColumn19
            // 
            this.bandedGridColumn19.Caption = "均值";
            this.bandedGridColumn19.CustomizationCaption = "TxPower均值";
            this.bandedGridColumn19.FieldName = "Txpowermean";
            this.bandedGridColumn19.Name = "bandedGridColumn19";
            this.bandedGridColumn19.Visible = true;
            // 
            // bandedGridColumn20
            // 
            this.bandedGridColumn20.Caption = "[-50,-20]";
            this.bandedGridColumn20.CustomizationCaption = "Txpower[-50,-20]";
            this.bandedGridColumn20.FieldName = "TxpowerF20";
            this.bandedGridColumn20.Name = "bandedGridColumn20";
            this.bandedGridColumn20.Visible = true;
            // 
            // bandedGridColumn21
            // 
            this.bandedGridColumn21.Caption = "[-20,0]";
            this.bandedGridColumn21.CustomizationCaption = "TxPower[-20,0]";
            this.bandedGridColumn21.FieldName = "TxpowerF20T0";
            this.bandedGridColumn21.Name = "bandedGridColumn21";
            this.bandedGridColumn21.Visible = true;
            // 
            // bandedGridColumn22
            // 
            this.bandedGridColumn22.Caption = "[0,15]";
            this.bandedGridColumn22.CustomizationCaption = "TxPower[0,15]";
            this.bandedGridColumn22.FieldName = "Txpower0T15";
            this.bandedGridColumn22.Name = "bandedGridColumn22";
            this.bandedGridColumn22.Visible = true;
            // 
            // bandedGridColumn23
            // 
            this.bandedGridColumn23.Caption = "[15,34]";
            this.bandedGridColumn23.CustomizationCaption = "TxPower[15,34]";
            this.bandedGridColumn23.FieldName = "Txpower15";
            this.bandedGridColumn23.Name = "bandedGridColumn23";
            this.bandedGridColumn23.Visible = true;
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn33,
            this.gridColumn34,
            this.gridColumn35,
            this.gridColumn36,
            this.gridColumn37,
            this.gridColumn137,
            this.gridColumn38,
            this.gridColumn39,
            this.gridColumn40,
            this.gridColumn41,
            this.gridColumn42,
            this.gridColumn43,
            this.gridColumn44,
            this.gridColumn45,
            this.gridColumn46,
            this.gridColumn47,
            this.gridColumn48,
            this.gridColumn49,
            this.gridColumn50,
            this.gridColumn51,
            this.gridColumn109,
            this.gridColumn110,
            this.gridColumn111,
            this.gridColumn112,
            this.gridColumn113,
            this.gridColumn207});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView1.OptionsView.ColumnAutoWidth = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "距离(米)";
            this.gridColumn1.FieldName = "Idistance";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "时长(秒)";
            this.gridColumn2.FieldName = "Iduration";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "电平均值";
            this.gridColumn3.FieldName = "Rxlmean";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "电平[-75,-10]";
            this.gridColumn4.FieldName = "Rxl75";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "电平[-80,-75]";
            this.gridColumn5.FieldName = "Rxl76_80";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 4;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "电平[-85,-80]";
            this.gridColumn6.FieldName = "Rxl81_85";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 5;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "电平[-90,-85]";
            this.gridColumn7.FieldName = "Rxl86_90";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 6;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "电平[-94,-90]";
            this.gridColumn8.FieldName = "Rxl91_94";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 7;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "电平[-140,-94]";
            this.gridColumn33.FieldName = "Rxl94";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 8;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "DPCH C/I均值";
            this.gridColumn34.FieldName = "Dc2imean";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 9;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "C/I[-20,-10]";
            this.gridColumn35.FieldName = "Dc2iF10";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 10;
            // 
            // gridColumn36
            // 
            this.gridColumn36.Caption = "C/I[-10,-3]";
            this.gridColumn36.FieldName = "Dc2iF10TF3";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.Visible = true;
            this.gridColumn36.VisibleIndex = 11;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "C/I[-3,15]";
            this.gridColumn37.FieldName = "Dc2iF3T15";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.Visible = true;
            this.gridColumn37.VisibleIndex = 13;
            // 
            // gridColumn137
            // 
            this.gridColumn137.Caption = "C/I[15,25]";
            this.gridColumn137.FieldName = "Dc2i15";
            this.gridColumn137.Name = "gridColumn137";
            this.gridColumn137.Visible = true;
            this.gridColumn137.VisibleIndex = 12;
            // 
            // gridColumn38
            // 
            this.gridColumn38.Caption = "MOS均值";
            this.gridColumn38.FieldName = "Pesqmean";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.Visible = true;
            this.gridColumn38.VisibleIndex = 14;
            // 
            // gridColumn39
            // 
            this.gridColumn39.Caption = "MOS[0,2.8]";
            this.gridColumn39.FieldName = "Pesq28";
            this.gridColumn39.Name = "gridColumn39";
            this.gridColumn39.Visible = true;
            this.gridColumn39.VisibleIndex = 15;
            // 
            // gridColumn40
            // 
            this.gridColumn40.Caption = "MOS[2.8,3]";
            this.gridColumn40.FieldName = "Pesq28_30";
            this.gridColumn40.Name = "gridColumn40";
            this.gridColumn40.Visible = true;
            this.gridColumn40.VisibleIndex = 16;
            // 
            // gridColumn41
            // 
            this.gridColumn41.Caption = "MOS[3,5]";
            this.gridColumn41.FieldName = "Pesq30";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.Visible = true;
            this.gridColumn41.VisibleIndex = 17;
            // 
            // gridColumn42
            // 
            this.gridColumn42.Caption = "TxPower均值";
            this.gridColumn42.FieldName = "Txpowermean";
            this.gridColumn42.Name = "gridColumn42";
            this.gridColumn42.Visible = true;
            this.gridColumn42.VisibleIndex = 18;
            // 
            // gridColumn43
            // 
            this.gridColumn43.Caption = "Txpower[-50,-20]";
            this.gridColumn43.FieldName = "TxpowerF20";
            this.gridColumn43.Name = "gridColumn43";
            this.gridColumn43.Visible = true;
            this.gridColumn43.VisibleIndex = 19;
            // 
            // gridColumn44
            // 
            this.gridColumn44.Caption = "TxPower[-20,0]";
            this.gridColumn44.FieldName = "TxpowerF20T0";
            this.gridColumn44.Name = "gridColumn44";
            this.gridColumn44.Visible = true;
            this.gridColumn44.VisibleIndex = 20;
            // 
            // gridColumn45
            // 
            this.gridColumn45.Caption = "TxPower[0,15]";
            this.gridColumn45.FieldName = "Txpower0T15";
            this.gridColumn45.Name = "gridColumn45";
            this.gridColumn45.Visible = true;
            this.gridColumn45.VisibleIndex = 21;
            // 
            // gridColumn46
            // 
            this.gridColumn46.Caption = "TxPower[15,34]";
            this.gridColumn46.FieldName = "Txpower15";
            this.gridColumn46.Name = "gridColumn46";
            this.gridColumn46.Visible = true;
            this.gridColumn46.VisibleIndex = 22;
            // 
            // gridColumn47
            // 
            this.gridColumn47.Caption = "小区1";
            this.gridColumn47.FieldName = "Cell1";
            this.gridColumn47.Name = "gridColumn47";
            this.gridColumn47.Visible = true;
            this.gridColumn47.VisibleIndex = 23;
            // 
            // gridColumn48
            // 
            this.gridColumn48.Caption = "小区2";
            this.gridColumn48.FieldName = "Cell2";
            this.gridColumn48.Name = "gridColumn48";
            this.gridColumn48.Visible = true;
            this.gridColumn48.VisibleIndex = 24;
            // 
            // gridColumn49
            // 
            this.gridColumn49.Caption = "小区3";
            this.gridColumn49.FieldName = "Cell3";
            this.gridColumn49.Name = "gridColumn49";
            this.gridColumn49.Visible = true;
            this.gridColumn49.VisibleIndex = 25;
            // 
            // gridColumn50
            // 
            this.gridColumn50.Caption = "所属网格";
            this.gridColumn50.FieldName = "Strgrid";
            this.gridColumn50.Name = "gridColumn50";
            this.gridColumn50.Visible = true;
            this.gridColumn50.VisibleIndex = 26;
            // 
            // gridColumn51
            // 
            this.gridColumn51.Caption = "所属道路";
            this.gridColumn51.FieldName = "Strroad";
            this.gridColumn51.Name = "gridColumn51";
            this.gridColumn51.Visible = true;
            this.gridColumn51.VisibleIndex = 27;
            // 
            // gridColumn109
            // 
            this.gridColumn109.Caption = "经度";
            this.gridColumn109.FieldName = "Imlongitude";
            this.gridColumn109.Name = "gridColumn109";
            // 
            // gridColumn110
            // 
            this.gridColumn110.Caption = "纬度";
            this.gridColumn110.FieldName = "Imlatitude";
            this.gridColumn110.Name = "gridColumn110";
            // 
            // gridColumn111
            // 
            this.gridColumn111.Caption = "ifileid";
            this.gridColumn111.FieldName = "Ifileid";
            this.gridColumn111.Name = "gridColumn111";
            // 
            // gridColumn112
            // 
            this.gridColumn112.Caption = "istime";
            this.gridColumn112.FieldName = "Istime";
            this.gridColumn112.Name = "gridColumn112";
            // 
            // gridColumn113
            // 
            this.gridColumn113.Caption = "ietime";
            this.gridColumn113.FieldName = "Ietime";
            this.gridColumn113.Name = "gridColumn113";
            // 
            // gridColumn207
            // 
            this.gridColumn207.Caption = "gridColumn207";
            this.gridColumn207.FieldName = "Iid";
            this.gridColumn207.Name = "gridColumn207";
            // 
            // gridView2
            // 
            this.gridView2.GridControl = this.gridControl1;
            this.gridView2.Name = "gridView2";
            // 
            // xtraTabPage3
            // 
            this.xtraTabPage3.Controls.Add(this.gridControl2);
            this.xtraTabPage3.Name = "xtraTabPage3";
            this.xtraTabPage3.Size = new System.Drawing.Size(836, 381);
            this.xtraTabPage3.Text = "TD弱MOS";
            // 
            // gridControl2
            // 
            this.gridControl2.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl2.Location = new System.Drawing.Point(0, 0);
            this.gridControl2.MainView = this.bandedGridView2;
            this.gridControl2.Name = "gridControl2";
            this.gridControl2.Size = new System.Drawing.Size(836, 381);
            this.gridControl2.TabIndex = 1;
            this.gridControl2.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView2,
            this.gridView3,
            this.gridView4});
            this.gridControl2.DoubleClick += new System.EventHandler(this.gridControl2_DoubleClick);
            // 
            // bandedGridView2
            // 
            this.bandedGridView2.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand2,
            this.gridBand12,
            this.gridBand13,
            this.gridBand14,
            this.gridBand15,
            this.gridBand16});
            this.bandedGridView2.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn35,
            this.bandedGridColumn36,
            this.bandedGridColumn37,
            this.bandedGridColumn38,
            this.bandedGridColumn39,
            this.bandedGridColumn40,
            this.bandedGridColumn41,
            this.bandedGridColumn42,
            this.bandedGridColumn43,
            this.bandedGridColumn44,
            this.bandedGridColumn45,
            this.bandedGridColumn46,
            this.bandedGridColumn47,
            this.bandedGridColumn48,
            this.bandedGridColumn49,
            this.bandedGridColumn50,
            this.bandedGridColumn51,
            this.bandedGridColumn52,
            this.bandedGridColumn53,
            this.bandedGridColumn54,
            this.bandedGridColumn55,
            this.bandedGridColumn56,
            this.bandedGridColumn57,
            this.bandedGridColumn58,
            this.bandedGridColumn59,
            this.bandedGridColumn60,
            this.bandedGridColumn61,
            this.bandedGridColumn62,
            this.bandedGridColumn63,
            this.bandedGridColumn64,
            this.bandedGridColumn65,
            this.bandedGridColumn66,
            this.bandedGridColumn67,
            this.bandedGridColumn68});
            this.bandedGridView2.GridControl = this.gridControl2;
            this.bandedGridView2.Name = "bandedGridView2";
            this.bandedGridView2.OptionsBehavior.Editable = false;
            this.bandedGridView2.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.bandedGridView2.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView2.OptionsView.ShowGroupPanel = false;
            // 
            // gridView3
            // 
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn24,
            this.gridColumn25,
            this.gridColumn26,
            this.gridColumn27,
            this.gridColumn28,
            this.gridColumn29,
            this.gridColumn30,
            this.gridColumn31,
            this.gridColumn32,
            this.gridColumn52,
            this.gridColumn53,
            this.gridColumn54,
            this.gridColumn55,
            this.gridColumn56,
            this.gridColumn57,
            this.gridColumn58,
            this.gridColumn59,
            this.gridColumn138,
            this.gridColumn208});
            this.gridView3.GridControl = this.gridControl2;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsBehavior.Editable = false;
            this.gridView3.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView3.OptionsView.ColumnAutoWidth = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            this.gridView3.DoubleClick += new System.EventHandler(this.gridControl2_DoubleClick);
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "距离(米)";
            this.gridColumn9.FieldName = "Idistance";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 0;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "时长(秒)";
            this.gridColumn10.FieldName = "Iduration";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 1;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "电平均值";
            this.gridColumn11.FieldName = "Rxlmean";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 2;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "电平[-75,-10]";
            this.gridColumn12.FieldName = "Rxl75";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 3;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "电平[-80,-75]";
            this.gridColumn13.FieldName = "Rxl76_80";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 4;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "电平[-85,-80]";
            this.gridColumn14.FieldName = "Rxl81_85";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 5;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "电平[-90,-85]";
            this.gridColumn15.FieldName = "Rxl86_90";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 6;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "电平[-94,-90]";
            this.gridColumn16.FieldName = "Rxl91_94";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 7;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "电平[-140,-94]";
            this.gridColumn17.FieldName = "Rxl94";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 8;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "DPCH C/I均值";
            this.gridColumn18.FieldName = "Dc2imean";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 9;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "C/I[-20,-10]";
            this.gridColumn19.FieldName = "Dc2iF10";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 10;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "C/I[-10,-3]";
            this.gridColumn20.FieldName = "Dc2iF10TF3";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 11;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "C/I[-3,15]";
            this.gridColumn21.FieldName = "Dc2iF3T15";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 13;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "C/I[15,25]";
            this.gridColumn22.FieldName = "Dc2i15";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 12;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "MOS均值";
            this.gridColumn23.FieldName = "Pesqmean";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 14;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "MOS[0,2.8]";
            this.gridColumn24.FieldName = "Pesq28";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 15;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "MOS[2.8,3]";
            this.gridColumn25.FieldName = "Pesq28_30";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 16;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "MOS[3,5]";
            this.gridColumn26.FieldName = "Pesq30";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 17;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "TxPower均值";
            this.gridColumn27.FieldName = "Txpowermean";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 18;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "Txpower[-50,-20]";
            this.gridColumn28.FieldName = "TxpowerF20";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 19;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "TxPower[-20,0]";
            this.gridColumn29.FieldName = "TxpowerF20T0";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 20;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "TxPower[0,15]";
            this.gridColumn30.FieldName = "Txpower0T15";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 21;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "TxPower[15,34]";
            this.gridColumn31.FieldName = "Txpower15";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 22;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "小区1";
            this.gridColumn32.FieldName = "Cell1";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 23;
            // 
            // gridColumn52
            // 
            this.gridColumn52.Caption = "小区2";
            this.gridColumn52.FieldName = "Cell2";
            this.gridColumn52.Name = "gridColumn52";
            this.gridColumn52.Visible = true;
            this.gridColumn52.VisibleIndex = 24;
            // 
            // gridColumn53
            // 
            this.gridColumn53.Caption = "小区3";
            this.gridColumn53.FieldName = "Cell3";
            this.gridColumn53.Name = "gridColumn53";
            this.gridColumn53.Visible = true;
            this.gridColumn53.VisibleIndex = 25;
            // 
            // gridColumn54
            // 
            this.gridColumn54.Caption = "所属网格";
            this.gridColumn54.FieldName = "Strgrid";
            this.gridColumn54.Name = "gridColumn54";
            this.gridColumn54.Visible = true;
            this.gridColumn54.VisibleIndex = 26;
            // 
            // gridColumn55
            // 
            this.gridColumn55.Caption = "所属道路";
            this.gridColumn55.FieldName = "Strroad";
            this.gridColumn55.Name = "gridColumn55";
            this.gridColumn55.Visible = true;
            this.gridColumn55.VisibleIndex = 27;
            // 
            // gridColumn56
            // 
            this.gridColumn56.Caption = "经度";
            this.gridColumn56.FieldName = "Imlongitude";
            this.gridColumn56.Name = "gridColumn56";
            // 
            // gridColumn57
            // 
            this.gridColumn57.Caption = "纬度";
            this.gridColumn57.FieldName = "Imlatitude";
            this.gridColumn57.Name = "gridColumn57";
            // 
            // gridColumn58
            // 
            this.gridColumn58.Caption = "ifileid";
            this.gridColumn58.FieldName = "Ifileid";
            this.gridColumn58.Name = "gridColumn58";
            // 
            // gridColumn59
            // 
            this.gridColumn59.Caption = "istime";
            this.gridColumn59.FieldName = "Istime";
            this.gridColumn59.Name = "gridColumn59";
            // 
            // gridColumn138
            // 
            this.gridColumn138.Caption = "ietime";
            this.gridColumn138.FieldName = "Ietime";
            this.gridColumn138.Name = "gridColumn138";
            // 
            // gridColumn208
            // 
            this.gridColumn208.Caption = "gridColumn208";
            this.gridColumn208.FieldName = "Iid";
            this.gridColumn208.Name = "gridColumn208";
            // 
            // gridView4
            // 
            this.gridView4.GridControl = this.gridControl2;
            this.gridView4.Name = "gridView4";
            // 
            // xtraTabPage4
            // 
            this.xtraTabPage4.Controls.Add(this.gridControl3);
            this.xtraTabPage4.Name = "xtraTabPage4";
            this.xtraTabPage4.Size = new System.Drawing.Size(836, 381);
            this.xtraTabPage4.Text = "TD干扰";
            // 
            // gridControl3
            // 
            this.gridControl3.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl3.Location = new System.Drawing.Point(0, 0);
            this.gridControl3.MainView = this.bandedGridView3;
            this.gridControl3.Name = "gridControl3";
            this.gridControl3.Size = new System.Drawing.Size(836, 381);
            this.gridControl3.TabIndex = 1;
            this.gridControl3.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView3,
            this.gridView5,
            this.gridView6});
            this.gridControl3.DoubleClick += new System.EventHandler(this.gridControl3_DoubleClick);
            // 
            // bandedGridView3
            // 
            this.bandedGridView3.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand3,
            this.gridBand17,
            this.gridBand18,
            this.gridBand19,
            this.gridBand20,
            this.gridBand21});
            this.bandedGridView3.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn69,
            this.bandedGridColumn70,
            this.bandedGridColumn71,
            this.bandedGridColumn72,
            this.bandedGridColumn73,
            this.bandedGridColumn74,
            this.bandedGridColumn75,
            this.bandedGridColumn76,
            this.bandedGridColumn77,
            this.bandedGridColumn78,
            this.bandedGridColumn79,
            this.bandedGridColumn80,
            this.bandedGridColumn81,
            this.bandedGridColumn82,
            this.bandedGridColumn83,
            this.bandedGridColumn84,
            this.bandedGridColumn85,
            this.bandedGridColumn86,
            this.bandedGridColumn87,
            this.bandedGridColumn88,
            this.bandedGridColumn89,
            this.bandedGridColumn90,
            this.bandedGridColumn91,
            this.bandedGridColumn92,
            this.bandedGridColumn93,
            this.bandedGridColumn94,
            this.bandedGridColumn95,
            this.bandedGridColumn96,
            this.bandedGridColumn97,
            this.bandedGridColumn98,
            this.bandedGridColumn99,
            this.bandedGridColumn100,
            this.bandedGridColumn101,
            this.bandedGridColumn102});
            this.bandedGridView3.GridControl = this.gridControl3;
            this.bandedGridView3.Name = "bandedGridView3";
            this.bandedGridView3.OptionsBehavior.Editable = false;
            this.bandedGridView3.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.bandedGridView3.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView3.OptionsView.ShowGroupPanel = false;
            // 
            // gridView5
            // 
            this.gridView5.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn60,
            this.gridColumn61,
            this.gridColumn62,
            this.gridColumn63,
            this.gridColumn64,
            this.gridColumn65,
            this.gridColumn66,
            this.gridColumn67,
            this.gridColumn68,
            this.gridColumn69,
            this.gridColumn70,
            this.gridColumn71,
            this.gridColumn72,
            this.gridColumn73,
            this.gridColumn74,
            this.gridColumn75,
            this.gridColumn76,
            this.gridColumn77,
            this.gridColumn78,
            this.gridColumn79,
            this.gridColumn80,
            this.gridColumn81,
            this.gridColumn82,
            this.gridColumn83,
            this.gridColumn84,
            this.gridColumn85,
            this.gridColumn86,
            this.gridColumn87,
            this.gridColumn88,
            this.gridColumn89,
            this.gridColumn90,
            this.gridColumn91,
            this.gridColumn139,
            this.gridColumn209});
            this.gridView5.GridControl = this.gridControl3;
            this.gridView5.Name = "gridView5";
            this.gridView5.OptionsBehavior.Editable = false;
            this.gridView5.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView5.OptionsView.ColumnAutoWidth = false;
            this.gridView5.OptionsView.ShowGroupPanel = false;
            this.gridView5.DoubleClick += new System.EventHandler(this.gridControl3_DoubleClick);
            // 
            // gridColumn60
            // 
            this.gridColumn60.Caption = "距离(米)";
            this.gridColumn60.FieldName = "Idistance";
            this.gridColumn60.Name = "gridColumn60";
            this.gridColumn60.Visible = true;
            this.gridColumn60.VisibleIndex = 0;
            // 
            // gridColumn61
            // 
            this.gridColumn61.Caption = "时长(秒)";
            this.gridColumn61.FieldName = "Iduration";
            this.gridColumn61.Name = "gridColumn61";
            this.gridColumn61.Visible = true;
            this.gridColumn61.VisibleIndex = 1;
            // 
            // gridColumn62
            // 
            this.gridColumn62.Caption = "电平均值";
            this.gridColumn62.FieldName = "Rxlmean";
            this.gridColumn62.Name = "gridColumn62";
            this.gridColumn62.Visible = true;
            this.gridColumn62.VisibleIndex = 2;
            // 
            // gridColumn63
            // 
            this.gridColumn63.Caption = "电平[-75,-10]";
            this.gridColumn63.FieldName = "Rxl75";
            this.gridColumn63.Name = "gridColumn63";
            this.gridColumn63.Visible = true;
            this.gridColumn63.VisibleIndex = 3;
            // 
            // gridColumn64
            // 
            this.gridColumn64.Caption = "电平[-80,-75]";
            this.gridColumn64.FieldName = "Rxl76_80";
            this.gridColumn64.Name = "gridColumn64";
            this.gridColumn64.Visible = true;
            this.gridColumn64.VisibleIndex = 4;
            // 
            // gridColumn65
            // 
            this.gridColumn65.Caption = "电平[-85,-80]";
            this.gridColumn65.FieldName = "Rxl81_85";
            this.gridColumn65.Name = "gridColumn65";
            this.gridColumn65.Visible = true;
            this.gridColumn65.VisibleIndex = 5;
            // 
            // gridColumn66
            // 
            this.gridColumn66.Caption = "电平[-90,-85]";
            this.gridColumn66.FieldName = "Rxl86_90";
            this.gridColumn66.Name = "gridColumn66";
            this.gridColumn66.Visible = true;
            this.gridColumn66.VisibleIndex = 6;
            // 
            // gridColumn67
            // 
            this.gridColumn67.Caption = "电平[-94,-90]";
            this.gridColumn67.FieldName = "Rxl91_94";
            this.gridColumn67.Name = "gridColumn67";
            this.gridColumn67.Visible = true;
            this.gridColumn67.VisibleIndex = 7;
            // 
            // gridColumn68
            // 
            this.gridColumn68.Caption = "电平[-140,-94]";
            this.gridColumn68.FieldName = "Rxl94";
            this.gridColumn68.Name = "gridColumn68";
            this.gridColumn68.Visible = true;
            this.gridColumn68.VisibleIndex = 8;
            // 
            // gridColumn69
            // 
            this.gridColumn69.Caption = "DPCH C/I均值";
            this.gridColumn69.FieldName = "Dc2imean";
            this.gridColumn69.Name = "gridColumn69";
            this.gridColumn69.Visible = true;
            this.gridColumn69.VisibleIndex = 9;
            // 
            // gridColumn70
            // 
            this.gridColumn70.Caption = "C/I[-20,-10]";
            this.gridColumn70.FieldName = "Dc2iF10";
            this.gridColumn70.Name = "gridColumn70";
            this.gridColumn70.Visible = true;
            this.gridColumn70.VisibleIndex = 10;
            // 
            // gridColumn71
            // 
            this.gridColumn71.Caption = "C/I[-10,-3]";
            this.gridColumn71.FieldName = "Dc2iF10TF3";
            this.gridColumn71.Name = "gridColumn71";
            this.gridColumn71.Visible = true;
            this.gridColumn71.VisibleIndex = 11;
            // 
            // gridColumn72
            // 
            this.gridColumn72.Caption = "C/I[-3,15]";
            this.gridColumn72.FieldName = "Dc2iF3T15";
            this.gridColumn72.Name = "gridColumn72";
            this.gridColumn72.Visible = true;
            this.gridColumn72.VisibleIndex = 13;
            // 
            // gridColumn73
            // 
            this.gridColumn73.Caption = "C/I[15,25]";
            this.gridColumn73.FieldName = "Dc2i15";
            this.gridColumn73.Name = "gridColumn73";
            this.gridColumn73.Visible = true;
            this.gridColumn73.VisibleIndex = 12;
            // 
            // gridColumn74
            // 
            this.gridColumn74.Caption = "MOS均值";
            this.gridColumn74.FieldName = "Pesqmean";
            this.gridColumn74.Name = "gridColumn74";
            this.gridColumn74.Visible = true;
            this.gridColumn74.VisibleIndex = 14;
            // 
            // gridColumn75
            // 
            this.gridColumn75.Caption = "MOS[0,2.8]";
            this.gridColumn75.FieldName = "Pesq28";
            this.gridColumn75.Name = "gridColumn75";
            this.gridColumn75.Visible = true;
            this.gridColumn75.VisibleIndex = 15;
            // 
            // gridColumn76
            // 
            this.gridColumn76.Caption = "MOS[2.8,3]";
            this.gridColumn76.FieldName = "Pesq28_30";
            this.gridColumn76.Name = "gridColumn76";
            this.gridColumn76.Visible = true;
            this.gridColumn76.VisibleIndex = 16;
            // 
            // gridColumn77
            // 
            this.gridColumn77.Caption = "MOS[3,5]";
            this.gridColumn77.FieldName = "Pesq30";
            this.gridColumn77.Name = "gridColumn77";
            this.gridColumn77.Visible = true;
            this.gridColumn77.VisibleIndex = 17;
            // 
            // gridColumn78
            // 
            this.gridColumn78.Caption = "TxPower均值";
            this.gridColumn78.FieldName = "Txpowermean";
            this.gridColumn78.Name = "gridColumn78";
            this.gridColumn78.Visible = true;
            this.gridColumn78.VisibleIndex = 18;
            // 
            // gridColumn79
            // 
            this.gridColumn79.Caption = "Txpower[-50,-20]";
            this.gridColumn79.FieldName = "TxpowerF20";
            this.gridColumn79.Name = "gridColumn79";
            this.gridColumn79.Visible = true;
            this.gridColumn79.VisibleIndex = 19;
            // 
            // gridColumn80
            // 
            this.gridColumn80.Caption = "TxPower[-20,0]";
            this.gridColumn80.FieldName = "TxpowerF20T0";
            this.gridColumn80.Name = "gridColumn80";
            this.gridColumn80.Visible = true;
            this.gridColumn80.VisibleIndex = 20;
            // 
            // gridColumn81
            // 
            this.gridColumn81.Caption = "TxPower[0,15]";
            this.gridColumn81.FieldName = "Txpower0T15";
            this.gridColumn81.Name = "gridColumn81";
            this.gridColumn81.Visible = true;
            this.gridColumn81.VisibleIndex = 21;
            // 
            // gridColumn82
            // 
            this.gridColumn82.Caption = "TxPower[15,34]";
            this.gridColumn82.FieldName = "Txpower15";
            this.gridColumn82.Name = "gridColumn82";
            this.gridColumn82.Visible = true;
            this.gridColumn82.VisibleIndex = 22;
            // 
            // gridColumn83
            // 
            this.gridColumn83.Caption = "小区1";
            this.gridColumn83.FieldName = "Cell1";
            this.gridColumn83.Name = "gridColumn83";
            this.gridColumn83.Visible = true;
            this.gridColumn83.VisibleIndex = 23;
            // 
            // gridColumn84
            // 
            this.gridColumn84.Caption = "小区2";
            this.gridColumn84.FieldName = "Cell2";
            this.gridColumn84.Name = "gridColumn84";
            this.gridColumn84.Visible = true;
            this.gridColumn84.VisibleIndex = 24;
            // 
            // gridColumn85
            // 
            this.gridColumn85.Caption = "小区3";
            this.gridColumn85.FieldName = "Cell3";
            this.gridColumn85.Name = "gridColumn85";
            this.gridColumn85.Visible = true;
            this.gridColumn85.VisibleIndex = 25;
            // 
            // gridColumn86
            // 
            this.gridColumn86.Caption = "所属网格";
            this.gridColumn86.FieldName = "Strgrid";
            this.gridColumn86.Name = "gridColumn86";
            this.gridColumn86.Visible = true;
            this.gridColumn86.VisibleIndex = 26;
            // 
            // gridColumn87
            // 
            this.gridColumn87.Caption = "所属道路";
            this.gridColumn87.FieldName = "Strroad";
            this.gridColumn87.Name = "gridColumn87";
            this.gridColumn87.Visible = true;
            this.gridColumn87.VisibleIndex = 27;
            // 
            // gridColumn88
            // 
            this.gridColumn88.Caption = "经度";
            this.gridColumn88.FieldName = "Imlongitude";
            this.gridColumn88.Name = "gridColumn88";
            // 
            // gridColumn89
            // 
            this.gridColumn89.Caption = "纬度";
            this.gridColumn89.FieldName = "Imlatitude";
            this.gridColumn89.Name = "gridColumn89";
            // 
            // gridColumn90
            // 
            this.gridColumn90.Caption = "ifileid";
            this.gridColumn90.FieldName = "Ifileid";
            this.gridColumn90.Name = "gridColumn90";
            // 
            // gridColumn91
            // 
            this.gridColumn91.Caption = "istime";
            this.gridColumn91.FieldName = "Istime";
            this.gridColumn91.Name = "gridColumn91";
            // 
            // gridColumn139
            // 
            this.gridColumn139.Caption = "ietime";
            this.gridColumn139.FieldName = "Ietime";
            this.gridColumn139.Name = "gridColumn139";
            // 
            // gridColumn209
            // 
            this.gridColumn209.Caption = "gridColumn209";
            this.gridColumn209.FieldName = "Iid";
            this.gridColumn209.Name = "gridColumn209";
            // 
            // gridView6
            // 
            this.gridView6.GridControl = this.gridControl3;
            this.gridView6.Name = "gridView6";
            // 
            // xtraTabPage5
            // 
            this.xtraTabPage5.Controls.Add(this.gridControl4);
            this.xtraTabPage5.Name = "xtraTabPage5";
            this.xtraTabPage5.Size = new System.Drawing.Size(836, 381);
            this.xtraTabPage5.Text = "TD上行链路差";
            // 
            // gridControl4
            // 
            this.gridControl4.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl4.Location = new System.Drawing.Point(0, 0);
            this.gridControl4.MainView = this.bandedGridView4;
            this.gridControl4.Name = "gridControl4";
            this.gridControl4.Size = new System.Drawing.Size(836, 381);
            this.gridControl4.TabIndex = 1;
            this.gridControl4.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView4,
            this.gridView7,
            this.gridView8});
            this.gridControl4.DoubleClick += new System.EventHandler(this.gridControl4_DoubleClick);
            // 
            // bandedGridView4
            // 
            this.bandedGridView4.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand4,
            this.gridBand22,
            this.gridBand23,
            this.gridBand24,
            this.gridBand25,
            this.gridBand26});
            this.bandedGridView4.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn103,
            this.bandedGridColumn104,
            this.bandedGridColumn105,
            this.bandedGridColumn106,
            this.bandedGridColumn107,
            this.bandedGridColumn108,
            this.bandedGridColumn109,
            this.bandedGridColumn110,
            this.bandedGridColumn111,
            this.bandedGridColumn112,
            this.bandedGridColumn113,
            this.bandedGridColumn114,
            this.bandedGridColumn115,
            this.bandedGridColumn116,
            this.bandedGridColumn117,
            this.bandedGridColumn118,
            this.bandedGridColumn119,
            this.bandedGridColumn120,
            this.bandedGridColumn121,
            this.bandedGridColumn122,
            this.bandedGridColumn123,
            this.bandedGridColumn124,
            this.bandedGridColumn125,
            this.bandedGridColumn126,
            this.bandedGridColumn127,
            this.bandedGridColumn128,
            this.bandedGridColumn129,
            this.bandedGridColumn130,
            this.bandedGridColumn131,
            this.bandedGridColumn132,
            this.bandedGridColumn133,
            this.bandedGridColumn134,
            this.bandedGridColumn135,
            this.bandedGridColumn136});
            this.bandedGridView4.GridControl = this.gridControl4;
            this.bandedGridView4.Name = "bandedGridView4";
            this.bandedGridView4.OptionsBehavior.Editable = false;
            this.bandedGridView4.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.bandedGridView4.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView4.OptionsView.ShowGroupPanel = false;
            // 
            // gridView7
            // 
            this.gridView7.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn92,
            this.gridColumn93,
            this.gridColumn94,
            this.gridColumn95,
            this.gridColumn96,
            this.gridColumn97,
            this.gridColumn98,
            this.gridColumn99,
            this.gridColumn100,
            this.gridColumn101,
            this.gridColumn102,
            this.gridColumn103,
            this.gridColumn104,
            this.gridColumn105,
            this.gridColumn106,
            this.gridColumn107,
            this.gridColumn108,
            this.gridColumn114,
            this.gridColumn115,
            this.gridColumn116,
            this.gridColumn117,
            this.gridColumn118,
            this.gridColumn119,
            this.gridColumn120,
            this.gridColumn121,
            this.gridColumn122,
            this.gridColumn123,
            this.gridColumn124,
            this.gridColumn125,
            this.gridColumn126,
            this.gridColumn127,
            this.gridColumn128,
            this.gridColumn140,
            this.gridColumn210});
            this.gridView7.GridControl = this.gridControl4;
            this.gridView7.Name = "gridView7";
            this.gridView7.OptionsBehavior.Editable = false;
            this.gridView7.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView7.OptionsView.ColumnAutoWidth = false;
            this.gridView7.OptionsView.ShowGroupPanel = false;
            this.gridView7.DoubleClick += new System.EventHandler(this.gridControl4_DoubleClick);
            // 
            // gridColumn92
            // 
            this.gridColumn92.Caption = "距离(米)";
            this.gridColumn92.FieldName = "Idistance";
            this.gridColumn92.Name = "gridColumn92";
            this.gridColumn92.Visible = true;
            this.gridColumn92.VisibleIndex = 0;
            // 
            // gridColumn93
            // 
            this.gridColumn93.Caption = "时长(秒)";
            this.gridColumn93.FieldName = "Iduration";
            this.gridColumn93.Name = "gridColumn93";
            this.gridColumn93.Visible = true;
            this.gridColumn93.VisibleIndex = 1;
            // 
            // gridColumn94
            // 
            this.gridColumn94.Caption = "电平均值";
            this.gridColumn94.FieldName = "Rxlmean";
            this.gridColumn94.Name = "gridColumn94";
            this.gridColumn94.Visible = true;
            this.gridColumn94.VisibleIndex = 2;
            // 
            // gridColumn95
            // 
            this.gridColumn95.Caption = "电平[-75,-10]";
            this.gridColumn95.FieldName = "Rxl75";
            this.gridColumn95.Name = "gridColumn95";
            this.gridColumn95.Visible = true;
            this.gridColumn95.VisibleIndex = 3;
            // 
            // gridColumn96
            // 
            this.gridColumn96.Caption = "电平[-80,-75]";
            this.gridColumn96.FieldName = "Rxl76_80";
            this.gridColumn96.Name = "gridColumn96";
            this.gridColumn96.Visible = true;
            this.gridColumn96.VisibleIndex = 4;
            // 
            // gridColumn97
            // 
            this.gridColumn97.Caption = "电平[-85,-80]";
            this.gridColumn97.FieldName = "Rxl81_85";
            this.gridColumn97.Name = "gridColumn97";
            this.gridColumn97.Visible = true;
            this.gridColumn97.VisibleIndex = 5;
            // 
            // gridColumn98
            // 
            this.gridColumn98.Caption = "电平[-90,-85]";
            this.gridColumn98.FieldName = "Rxl86_90";
            this.gridColumn98.Name = "gridColumn98";
            this.gridColumn98.Visible = true;
            this.gridColumn98.VisibleIndex = 6;
            // 
            // gridColumn99
            // 
            this.gridColumn99.Caption = "电平[-94,-90]";
            this.gridColumn99.FieldName = "Rxl91_94";
            this.gridColumn99.Name = "gridColumn99";
            this.gridColumn99.Visible = true;
            this.gridColumn99.VisibleIndex = 7;
            // 
            // gridColumn100
            // 
            this.gridColumn100.Caption = "电平[-140,-94]";
            this.gridColumn100.FieldName = "Rxl94";
            this.gridColumn100.Name = "gridColumn100";
            this.gridColumn100.Visible = true;
            this.gridColumn100.VisibleIndex = 8;
            // 
            // gridColumn101
            // 
            this.gridColumn101.Caption = "DPCH C/I均值";
            this.gridColumn101.FieldName = "Dc2imean";
            this.gridColumn101.Name = "gridColumn101";
            this.gridColumn101.Visible = true;
            this.gridColumn101.VisibleIndex = 9;
            // 
            // gridColumn102
            // 
            this.gridColumn102.Caption = "C/I[-20,-10]";
            this.gridColumn102.FieldName = "Dc2iF10";
            this.gridColumn102.Name = "gridColumn102";
            this.gridColumn102.Visible = true;
            this.gridColumn102.VisibleIndex = 10;
            // 
            // gridColumn103
            // 
            this.gridColumn103.Caption = "C/I[-10,-3]";
            this.gridColumn103.FieldName = "Dc2iF10TF3";
            this.gridColumn103.Name = "gridColumn103";
            this.gridColumn103.Visible = true;
            this.gridColumn103.VisibleIndex = 11;
            // 
            // gridColumn104
            // 
            this.gridColumn104.Caption = "C/I[-3,15]";
            this.gridColumn104.FieldName = "Dc2iF3T15";
            this.gridColumn104.Name = "gridColumn104";
            this.gridColumn104.Visible = true;
            this.gridColumn104.VisibleIndex = 13;
            // 
            // gridColumn105
            // 
            this.gridColumn105.Caption = "C/I[15,25]";
            this.gridColumn105.FieldName = "Dc2i15";
            this.gridColumn105.Name = "gridColumn105";
            this.gridColumn105.Visible = true;
            this.gridColumn105.VisibleIndex = 12;
            // 
            // gridColumn106
            // 
            this.gridColumn106.Caption = "MOS均值";
            this.gridColumn106.FieldName = "Pesqmean";
            this.gridColumn106.Name = "gridColumn106";
            this.gridColumn106.Visible = true;
            this.gridColumn106.VisibleIndex = 14;
            // 
            // gridColumn107
            // 
            this.gridColumn107.Caption = "MOS[0,2.8]";
            this.gridColumn107.FieldName = "Pesq28";
            this.gridColumn107.Name = "gridColumn107";
            this.gridColumn107.Visible = true;
            this.gridColumn107.VisibleIndex = 15;
            // 
            // gridColumn108
            // 
            this.gridColumn108.Caption = "MOS[2.8,3]";
            this.gridColumn108.FieldName = "Pesq28_30";
            this.gridColumn108.Name = "gridColumn108";
            this.gridColumn108.Visible = true;
            this.gridColumn108.VisibleIndex = 16;
            // 
            // gridColumn114
            // 
            this.gridColumn114.Caption = "MOS[3,5]";
            this.gridColumn114.FieldName = "Pesq30";
            this.gridColumn114.Name = "gridColumn114";
            this.gridColumn114.Visible = true;
            this.gridColumn114.VisibleIndex = 17;
            // 
            // gridColumn115
            // 
            this.gridColumn115.Caption = "TxPower均值";
            this.gridColumn115.FieldName = "Txpowermean";
            this.gridColumn115.Name = "gridColumn115";
            this.gridColumn115.Visible = true;
            this.gridColumn115.VisibleIndex = 18;
            // 
            // gridColumn116
            // 
            this.gridColumn116.Caption = "Txpower[-50,-20]";
            this.gridColumn116.FieldName = "TxpowerF20";
            this.gridColumn116.Name = "gridColumn116";
            this.gridColumn116.Visible = true;
            this.gridColumn116.VisibleIndex = 19;
            // 
            // gridColumn117
            // 
            this.gridColumn117.Caption = "TxPower[-20,0]";
            this.gridColumn117.FieldName = "TxpowerF20T0";
            this.gridColumn117.Name = "gridColumn117";
            this.gridColumn117.Visible = true;
            this.gridColumn117.VisibleIndex = 20;
            // 
            // gridColumn118
            // 
            this.gridColumn118.Caption = "TxPower[0,15]";
            this.gridColumn118.FieldName = "Txpower0T15";
            this.gridColumn118.Name = "gridColumn118";
            this.gridColumn118.Visible = true;
            this.gridColumn118.VisibleIndex = 21;
            // 
            // gridColumn119
            // 
            this.gridColumn119.Caption = "TxPower[15,34]";
            this.gridColumn119.FieldName = "Txpower15";
            this.gridColumn119.Name = "gridColumn119";
            this.gridColumn119.Visible = true;
            this.gridColumn119.VisibleIndex = 22;
            // 
            // gridColumn120
            // 
            this.gridColumn120.Caption = "小区1";
            this.gridColumn120.FieldName = "Cell1";
            this.gridColumn120.Name = "gridColumn120";
            this.gridColumn120.Visible = true;
            this.gridColumn120.VisibleIndex = 23;
            // 
            // gridColumn121
            // 
            this.gridColumn121.Caption = "小区2";
            this.gridColumn121.FieldName = "Cell2";
            this.gridColumn121.Name = "gridColumn121";
            this.gridColumn121.Visible = true;
            this.gridColumn121.VisibleIndex = 24;
            // 
            // gridColumn122
            // 
            this.gridColumn122.Caption = "小区3";
            this.gridColumn122.FieldName = "Cell3";
            this.gridColumn122.Name = "gridColumn122";
            this.gridColumn122.Visible = true;
            this.gridColumn122.VisibleIndex = 25;
            // 
            // gridColumn123
            // 
            this.gridColumn123.Caption = "所属网格";
            this.gridColumn123.FieldName = "Strgrid";
            this.gridColumn123.Name = "gridColumn123";
            this.gridColumn123.Visible = true;
            this.gridColumn123.VisibleIndex = 26;
            // 
            // gridColumn124
            // 
            this.gridColumn124.Caption = "所属道路";
            this.gridColumn124.FieldName = "Strroad";
            this.gridColumn124.Name = "gridColumn124";
            this.gridColumn124.Visible = true;
            this.gridColumn124.VisibleIndex = 27;
            // 
            // gridColumn125
            // 
            this.gridColumn125.Caption = "经度";
            this.gridColumn125.FieldName = "Imlongitude";
            this.gridColumn125.Name = "gridColumn125";
            // 
            // gridColumn126
            // 
            this.gridColumn126.Caption = "纬度";
            this.gridColumn126.FieldName = "Imlatitude";
            this.gridColumn126.Name = "gridColumn126";
            // 
            // gridColumn127
            // 
            this.gridColumn127.Caption = "ifileid";
            this.gridColumn127.FieldName = "Ifileid";
            this.gridColumn127.Name = "gridColumn127";
            // 
            // gridColumn128
            // 
            this.gridColumn128.Caption = "istime";
            this.gridColumn128.FieldName = "Istime";
            this.gridColumn128.Name = "gridColumn128";
            // 
            // gridColumn140
            // 
            this.gridColumn140.Caption = "ietime";
            this.gridColumn140.FieldName = "Ietime";
            this.gridColumn140.Name = "gridColumn140";
            // 
            // gridColumn210
            // 
            this.gridColumn210.Caption = "gridColumn210";
            this.gridColumn210.FieldName = "Iid";
            this.gridColumn210.Name = "gridColumn210";
            // 
            // gridView8
            // 
            this.gridView8.GridControl = this.gridControl4;
            this.gridView8.Name = "gridView8";
            // 
            // xtraTabPage6
            // 
            this.xtraTabPage6.Controls.Add(this.gridControl6);
            this.xtraTabPage6.Name = "xtraTabPage6";
            this.xtraTabPage6.Size = new System.Drawing.Size(836, 381);
            this.xtraTabPage6.Text = "TD深度覆盖不足";
            // 
            // gridControl6
            // 
            this.gridControl6.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl6.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl6.Location = new System.Drawing.Point(0, 0);
            this.gridControl6.MainView = this.bandedGridView5;
            this.gridControl6.Name = "gridControl6";
            this.gridControl6.Size = new System.Drawing.Size(836, 381);
            this.gridControl6.TabIndex = 1;
            this.gridControl6.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView5,
            this.gridView10,
            this.gridView11});
            this.gridControl6.DoubleClick += new System.EventHandler(this.gridControl6_DoubleClick);
            // 
            // bandedGridView5
            // 
            this.bandedGridView5.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand5,
            this.gridBand27,
            this.gridBand28,
            this.gridBand29,
            this.gridBand30,
            this.gridBand31});
            this.bandedGridView5.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn137,
            this.bandedGridColumn138,
            this.bandedGridColumn139,
            this.bandedGridColumn140,
            this.bandedGridColumn141,
            this.bandedGridColumn142,
            this.bandedGridColumn143,
            this.bandedGridColumn144,
            this.bandedGridColumn145,
            this.bandedGridColumn146,
            this.bandedGridColumn147,
            this.bandedGridColumn148,
            this.bandedGridColumn149,
            this.bandedGridColumn150,
            this.bandedGridColumn151,
            this.bandedGridColumn152,
            this.bandedGridColumn153,
            this.bandedGridColumn154,
            this.bandedGridColumn155,
            this.bandedGridColumn156,
            this.bandedGridColumn157,
            this.bandedGridColumn158,
            this.bandedGridColumn159,
            this.bandedGridColumn160,
            this.bandedGridColumn161,
            this.bandedGridColumn162,
            this.bandedGridColumn163,
            this.bandedGridColumn164,
            this.bandedGridColumn165,
            this.bandedGridColumn166,
            this.bandedGridColumn167,
            this.bandedGridColumn168,
            this.bandedGridColumn169,
            this.bandedGridColumn170});
            this.bandedGridView5.GridControl = this.gridControl6;
            this.bandedGridView5.Name = "bandedGridView5";
            this.bandedGridView5.OptionsBehavior.Editable = false;
            this.bandedGridView5.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.bandedGridView5.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView5.OptionsView.ShowGroupPanel = false;
            // 
            // gridView10
            // 
            this.gridView10.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn141,
            this.gridColumn142,
            this.gridColumn143,
            this.gridColumn144,
            this.gridColumn145,
            this.gridColumn146,
            this.gridColumn147,
            this.gridColumn148,
            this.gridColumn149,
            this.gridColumn150,
            this.gridColumn151,
            this.gridColumn152,
            this.gridColumn153,
            this.gridColumn154,
            this.gridColumn155,
            this.gridColumn156,
            this.gridColumn157,
            this.gridColumn158,
            this.gridColumn159,
            this.gridColumn160,
            this.gridColumn161,
            this.gridColumn162,
            this.gridColumn163,
            this.gridColumn164,
            this.gridColumn165,
            this.gridColumn166,
            this.gridColumn167,
            this.gridColumn168,
            this.gridColumn169,
            this.gridColumn170,
            this.gridColumn171,
            this.gridColumn172,
            this.gridColumn173,
            this.gridColumn211});
            this.gridView10.GridControl = this.gridControl6;
            this.gridView10.Name = "gridView10";
            this.gridView10.OptionsBehavior.Editable = false;
            this.gridView10.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView10.OptionsView.ColumnAutoWidth = false;
            this.gridView10.OptionsView.ShowGroupPanel = false;
            this.gridView10.DoubleClick += new System.EventHandler(this.gridView10_DoubleClick);
            // 
            // gridColumn141
            // 
            this.gridColumn141.Caption = "距离(米)";
            this.gridColumn141.FieldName = "Idistance";
            this.gridColumn141.Name = "gridColumn141";
            this.gridColumn141.Visible = true;
            this.gridColumn141.VisibleIndex = 0;
            // 
            // gridColumn142
            // 
            this.gridColumn142.Caption = "时长(秒)";
            this.gridColumn142.FieldName = "Iduration";
            this.gridColumn142.Name = "gridColumn142";
            this.gridColumn142.Visible = true;
            this.gridColumn142.VisibleIndex = 1;
            // 
            // gridColumn143
            // 
            this.gridColumn143.Caption = "电平均值";
            this.gridColumn143.FieldName = "Rxlmean";
            this.gridColumn143.Name = "gridColumn143";
            this.gridColumn143.Visible = true;
            this.gridColumn143.VisibleIndex = 2;
            // 
            // gridColumn144
            // 
            this.gridColumn144.Caption = "电平[-75,-10]";
            this.gridColumn144.FieldName = "Rxl75";
            this.gridColumn144.Name = "gridColumn144";
            this.gridColumn144.Visible = true;
            this.gridColumn144.VisibleIndex = 3;
            // 
            // gridColumn145
            // 
            this.gridColumn145.Caption = "电平[-80,-75]";
            this.gridColumn145.FieldName = "Rxl76_80";
            this.gridColumn145.Name = "gridColumn145";
            this.gridColumn145.Visible = true;
            this.gridColumn145.VisibleIndex = 4;
            // 
            // gridColumn146
            // 
            this.gridColumn146.Caption = "电平[-85,-80]";
            this.gridColumn146.FieldName = "Rxl81_85";
            this.gridColumn146.Name = "gridColumn146";
            this.gridColumn146.Visible = true;
            this.gridColumn146.VisibleIndex = 5;
            // 
            // gridColumn147
            // 
            this.gridColumn147.Caption = "电平[-90,-85]";
            this.gridColumn147.FieldName = "Rxl86_90";
            this.gridColumn147.Name = "gridColumn147";
            this.gridColumn147.Visible = true;
            this.gridColumn147.VisibleIndex = 6;
            // 
            // gridColumn148
            // 
            this.gridColumn148.Caption = "电平[-94,-90]";
            this.gridColumn148.FieldName = "Rxl91_94";
            this.gridColumn148.Name = "gridColumn148";
            this.gridColumn148.Visible = true;
            this.gridColumn148.VisibleIndex = 7;
            // 
            // gridColumn149
            // 
            this.gridColumn149.Caption = "电平[-140,-94]";
            this.gridColumn149.FieldName = "Rxl94";
            this.gridColumn149.Name = "gridColumn149";
            this.gridColumn149.Visible = true;
            this.gridColumn149.VisibleIndex = 8;
            // 
            // gridColumn150
            // 
            this.gridColumn150.Caption = "DPCH C/I均值";
            this.gridColumn150.FieldName = "Dc2imean";
            this.gridColumn150.Name = "gridColumn150";
            this.gridColumn150.Visible = true;
            this.gridColumn150.VisibleIndex = 9;
            // 
            // gridColumn151
            // 
            this.gridColumn151.Caption = "C/I[-20,-10]";
            this.gridColumn151.FieldName = "Dc2iF10";
            this.gridColumn151.Name = "gridColumn151";
            this.gridColumn151.Visible = true;
            this.gridColumn151.VisibleIndex = 10;
            // 
            // gridColumn152
            // 
            this.gridColumn152.Caption = "C/I[-10,-3]";
            this.gridColumn152.FieldName = "Dc2iF10TF3";
            this.gridColumn152.Name = "gridColumn152";
            this.gridColumn152.Visible = true;
            this.gridColumn152.VisibleIndex = 11;
            // 
            // gridColumn153
            // 
            this.gridColumn153.Caption = "C/I[-3,15]";
            this.gridColumn153.FieldName = "Dc2iF3T15";
            this.gridColumn153.Name = "gridColumn153";
            this.gridColumn153.Visible = true;
            this.gridColumn153.VisibleIndex = 13;
            // 
            // gridColumn154
            // 
            this.gridColumn154.Caption = "C/I[15,25]";
            this.gridColumn154.FieldName = "Dc2i15";
            this.gridColumn154.Name = "gridColumn154";
            this.gridColumn154.Visible = true;
            this.gridColumn154.VisibleIndex = 12;
            // 
            // gridColumn155
            // 
            this.gridColumn155.Caption = "MOS均值";
            this.gridColumn155.FieldName = "Pesqmean";
            this.gridColumn155.Name = "gridColumn155";
            this.gridColumn155.Visible = true;
            this.gridColumn155.VisibleIndex = 14;
            // 
            // gridColumn156
            // 
            this.gridColumn156.Caption = "MOS[0,2.8]";
            this.gridColumn156.FieldName = "Pesq28";
            this.gridColumn156.Name = "gridColumn156";
            this.gridColumn156.Visible = true;
            this.gridColumn156.VisibleIndex = 15;
            // 
            // gridColumn157
            // 
            this.gridColumn157.Caption = "MOS[2.8,3]";
            this.gridColumn157.FieldName = "Pesq28_30";
            this.gridColumn157.Name = "gridColumn157";
            this.gridColumn157.Visible = true;
            this.gridColumn157.VisibleIndex = 16;
            // 
            // gridColumn158
            // 
            this.gridColumn158.Caption = "MOS[3,5]";
            this.gridColumn158.FieldName = "Pesq30";
            this.gridColumn158.Name = "gridColumn158";
            this.gridColumn158.Visible = true;
            this.gridColumn158.VisibleIndex = 17;
            // 
            // gridColumn159
            // 
            this.gridColumn159.Caption = "TxPower均值";
            this.gridColumn159.FieldName = "Txpowermean";
            this.gridColumn159.Name = "gridColumn159";
            this.gridColumn159.Visible = true;
            this.gridColumn159.VisibleIndex = 18;
            // 
            // gridColumn160
            // 
            this.gridColumn160.Caption = "Txpower[-50,-20]";
            this.gridColumn160.FieldName = "TxpowerF20";
            this.gridColumn160.Name = "gridColumn160";
            this.gridColumn160.Visible = true;
            this.gridColumn160.VisibleIndex = 19;
            // 
            // gridColumn161
            // 
            this.gridColumn161.Caption = "TxPower[-20,0]";
            this.gridColumn161.FieldName = "TxpowerF20T0";
            this.gridColumn161.Name = "gridColumn161";
            this.gridColumn161.Visible = true;
            this.gridColumn161.VisibleIndex = 20;
            // 
            // gridColumn162
            // 
            this.gridColumn162.Caption = "TxPower[0,15]";
            this.gridColumn162.FieldName = "Txpower0T15";
            this.gridColumn162.Name = "gridColumn162";
            this.gridColumn162.Visible = true;
            this.gridColumn162.VisibleIndex = 21;
            // 
            // gridColumn163
            // 
            this.gridColumn163.Caption = "TxPower[15,34]";
            this.gridColumn163.FieldName = "Txpower15";
            this.gridColumn163.Name = "gridColumn163";
            this.gridColumn163.Visible = true;
            this.gridColumn163.VisibleIndex = 22;
            // 
            // gridColumn164
            // 
            this.gridColumn164.Caption = "小区1";
            this.gridColumn164.FieldName = "Cell1";
            this.gridColumn164.Name = "gridColumn164";
            this.gridColumn164.Visible = true;
            this.gridColumn164.VisibleIndex = 23;
            // 
            // gridColumn165
            // 
            this.gridColumn165.Caption = "小区2";
            this.gridColumn165.FieldName = "Cell2";
            this.gridColumn165.Name = "gridColumn165";
            this.gridColumn165.Visible = true;
            this.gridColumn165.VisibleIndex = 24;
            // 
            // gridColumn166
            // 
            this.gridColumn166.Caption = "小区3";
            this.gridColumn166.FieldName = "Cell3";
            this.gridColumn166.Name = "gridColumn166";
            this.gridColumn166.Visible = true;
            this.gridColumn166.VisibleIndex = 25;
            // 
            // gridColumn167
            // 
            this.gridColumn167.Caption = "所属网格";
            this.gridColumn167.FieldName = "Strgrid";
            this.gridColumn167.Name = "gridColumn167";
            this.gridColumn167.Visible = true;
            this.gridColumn167.VisibleIndex = 26;
            // 
            // gridColumn168
            // 
            this.gridColumn168.Caption = "所属道路";
            this.gridColumn168.FieldName = "Strroad";
            this.gridColumn168.Name = "gridColumn168";
            this.gridColumn168.Visible = true;
            this.gridColumn168.VisibleIndex = 27;
            // 
            // gridColumn169
            // 
            this.gridColumn169.Caption = "经度";
            this.gridColumn169.FieldName = "Imlongitude";
            this.gridColumn169.Name = "gridColumn169";
            // 
            // gridColumn170
            // 
            this.gridColumn170.Caption = "纬度";
            this.gridColumn170.FieldName = "Imlatitude";
            this.gridColumn170.Name = "gridColumn170";
            // 
            // gridColumn171
            // 
            this.gridColumn171.Caption = "ifileid";
            this.gridColumn171.FieldName = "Ifileid";
            this.gridColumn171.Name = "gridColumn171";
            // 
            // gridColumn172
            // 
            this.gridColumn172.Caption = "istime";
            this.gridColumn172.FieldName = "Istime";
            this.gridColumn172.Name = "gridColumn172";
            // 
            // gridColumn173
            // 
            this.gridColumn173.Caption = "ietime";
            this.gridColumn173.FieldName = "Ietime";
            this.gridColumn173.Name = "gridColumn173";
            // 
            // gridColumn211
            // 
            this.gridColumn211.Caption = "gridColumn211";
            this.gridColumn211.FieldName = "Iid";
            this.gridColumn211.Name = "gridColumn211";
            // 
            // gridView11
            // 
            this.gridView11.GridControl = this.gridControl6;
            this.gridView11.Name = "gridView11";
            // 
            // xtraTabPage7
            // 
            this.xtraTabPage7.Controls.Add(this.gridControl7);
            this.xtraTabPage7.Name = "xtraTabPage7";
            this.xtraTabPage7.Size = new System.Drawing.Size(836, 381);
            this.xtraTabPage7.Text = "不连续覆盖";
            // 
            // gridControl7
            // 
            this.gridControl7.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl7.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl7.Location = new System.Drawing.Point(0, 0);
            this.gridControl7.MainView = this.bandedGridView6;
            this.gridControl7.Name = "gridControl7";
            this.gridControl7.Size = new System.Drawing.Size(836, 381);
            this.gridControl7.TabIndex = 1;
            this.gridControl7.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView6,
            this.gridView12,
            this.gridView13});
            this.gridControl7.DoubleClick += new System.EventHandler(this.gridControl7_DoubleClick);
            // 
            // bandedGridView6
            // 
            this.bandedGridView6.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand6,
            this.gridBand32,
            this.gridBand33,
            this.gridBand34,
            this.gridBand35,
            this.gridBand36});
            this.bandedGridView6.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn171,
            this.bandedGridColumn172,
            this.bandedGridColumn173,
            this.bandedGridColumn174,
            this.bandedGridColumn175,
            this.bandedGridColumn176,
            this.bandedGridColumn177,
            this.bandedGridColumn178,
            this.bandedGridColumn179,
            this.bandedGridColumn180,
            this.bandedGridColumn181,
            this.bandedGridColumn182,
            this.bandedGridColumn183,
            this.bandedGridColumn184,
            this.bandedGridColumn185,
            this.bandedGridColumn186,
            this.bandedGridColumn187,
            this.bandedGridColumn188,
            this.bandedGridColumn189,
            this.bandedGridColumn190,
            this.bandedGridColumn191,
            this.bandedGridColumn192,
            this.bandedGridColumn193,
            this.bandedGridColumn194,
            this.bandedGridColumn195,
            this.bandedGridColumn196,
            this.bandedGridColumn197,
            this.bandedGridColumn198,
            this.bandedGridColumn199,
            this.bandedGridColumn200,
            this.bandedGridColumn201,
            this.bandedGridColumn202,
            this.bandedGridColumn203,
            this.bandedGridColumn204});
            this.bandedGridView6.GridControl = this.gridControl7;
            this.bandedGridView6.Name = "bandedGridView6";
            this.bandedGridView6.OptionsBehavior.Editable = false;
            this.bandedGridView6.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.bandedGridView6.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView6.OptionsView.ShowGroupPanel = false;
            // 
            // gridView12
            // 
            this.gridView12.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn174,
            this.gridColumn175,
            this.gridColumn176,
            this.gridColumn177,
            this.gridColumn178,
            this.gridColumn179,
            this.gridColumn180,
            this.gridColumn181,
            this.gridColumn182,
            this.gridColumn183,
            this.gridColumn184,
            this.gridColumn185,
            this.gridColumn186,
            this.gridColumn187,
            this.gridColumn188,
            this.gridColumn189,
            this.gridColumn190,
            this.gridColumn191,
            this.gridColumn192,
            this.gridColumn193,
            this.gridColumn194,
            this.gridColumn195,
            this.gridColumn196,
            this.gridColumn197,
            this.gridColumn198,
            this.gridColumn199,
            this.gridColumn200,
            this.gridColumn201,
            this.gridColumn202,
            this.gridColumn203,
            this.gridColumn204,
            this.gridColumn205,
            this.gridColumn206,
            this.gridColumn212});
            this.gridView12.GridControl = this.gridControl7;
            this.gridView12.Name = "gridView12";
            this.gridView12.OptionsBehavior.Editable = false;
            this.gridView12.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView12.OptionsView.ColumnAutoWidth = false;
            this.gridView12.OptionsView.ShowGroupPanel = false;
            this.gridView12.DoubleClick += new System.EventHandler(this.gridView12_DoubleClick);
            // 
            // gridColumn174
            // 
            this.gridColumn174.Caption = "距离(米)";
            this.gridColumn174.FieldName = "Idistance";
            this.gridColumn174.Name = "gridColumn174";
            this.gridColumn174.Visible = true;
            this.gridColumn174.VisibleIndex = 0;
            // 
            // gridColumn175
            // 
            this.gridColumn175.Caption = "时长(秒)";
            this.gridColumn175.FieldName = "Iduration";
            this.gridColumn175.Name = "gridColumn175";
            this.gridColumn175.Visible = true;
            this.gridColumn175.VisibleIndex = 1;
            // 
            // gridColumn176
            // 
            this.gridColumn176.Caption = "电平均值";
            this.gridColumn176.FieldName = "Rxlmean";
            this.gridColumn176.Name = "gridColumn176";
            this.gridColumn176.Visible = true;
            this.gridColumn176.VisibleIndex = 2;
            // 
            // gridColumn177
            // 
            this.gridColumn177.Caption = "电平[-75,-10]";
            this.gridColumn177.FieldName = "Rxl75";
            this.gridColumn177.Name = "gridColumn177";
            this.gridColumn177.Visible = true;
            this.gridColumn177.VisibleIndex = 3;
            // 
            // gridColumn178
            // 
            this.gridColumn178.Caption = "电平[-80,-75]";
            this.gridColumn178.FieldName = "Rxl76_80";
            this.gridColumn178.Name = "gridColumn178";
            this.gridColumn178.Visible = true;
            this.gridColumn178.VisibleIndex = 4;
            // 
            // gridColumn179
            // 
            this.gridColumn179.Caption = "电平[-85,-80]";
            this.gridColumn179.FieldName = "Rxl81_85";
            this.gridColumn179.Name = "gridColumn179";
            this.gridColumn179.Visible = true;
            this.gridColumn179.VisibleIndex = 5;
            // 
            // gridColumn180
            // 
            this.gridColumn180.Caption = "电平[-90,-85]";
            this.gridColumn180.FieldName = "Rxl86_90";
            this.gridColumn180.Name = "gridColumn180";
            this.gridColumn180.Visible = true;
            this.gridColumn180.VisibleIndex = 6;
            // 
            // gridColumn181
            // 
            this.gridColumn181.Caption = "电平[-94,-90]";
            this.gridColumn181.FieldName = "Rxl91_94";
            this.gridColumn181.Name = "gridColumn181";
            this.gridColumn181.Visible = true;
            this.gridColumn181.VisibleIndex = 7;
            // 
            // gridColumn182
            // 
            this.gridColumn182.Caption = "电平[-140,-94]";
            this.gridColumn182.FieldName = "Rxl94";
            this.gridColumn182.Name = "gridColumn182";
            this.gridColumn182.Visible = true;
            this.gridColumn182.VisibleIndex = 8;
            // 
            // gridColumn183
            // 
            this.gridColumn183.Caption = "DPCH C/I均值";
            this.gridColumn183.FieldName = "Dc2imean";
            this.gridColumn183.Name = "gridColumn183";
            this.gridColumn183.Visible = true;
            this.gridColumn183.VisibleIndex = 9;
            // 
            // gridColumn184
            // 
            this.gridColumn184.Caption = "C/I[-20,-10]";
            this.gridColumn184.FieldName = "Dc2iF10";
            this.gridColumn184.Name = "gridColumn184";
            this.gridColumn184.Visible = true;
            this.gridColumn184.VisibleIndex = 10;
            // 
            // gridColumn185
            // 
            this.gridColumn185.Caption = "C/I[-10,-3]";
            this.gridColumn185.FieldName = "Dc2iF10TF3";
            this.gridColumn185.Name = "gridColumn185";
            this.gridColumn185.Visible = true;
            this.gridColumn185.VisibleIndex = 11;
            // 
            // gridColumn186
            // 
            this.gridColumn186.Caption = "C/I[-3,15]";
            this.gridColumn186.FieldName = "Dc2iF3T15";
            this.gridColumn186.Name = "gridColumn186";
            this.gridColumn186.Visible = true;
            this.gridColumn186.VisibleIndex = 13;
            // 
            // gridColumn187
            // 
            this.gridColumn187.Caption = "C/I[15,25]";
            this.gridColumn187.FieldName = "Dc2i15";
            this.gridColumn187.Name = "gridColumn187";
            this.gridColumn187.Visible = true;
            this.gridColumn187.VisibleIndex = 12;
            // 
            // gridColumn188
            // 
            this.gridColumn188.Caption = "MOS均值";
            this.gridColumn188.FieldName = "Pesqmean";
            this.gridColumn188.Name = "gridColumn188";
            this.gridColumn188.Visible = true;
            this.gridColumn188.VisibleIndex = 14;
            // 
            // gridColumn189
            // 
            this.gridColumn189.Caption = "MOS[0,2.8]";
            this.gridColumn189.FieldName = "Pesq28";
            this.gridColumn189.Name = "gridColumn189";
            this.gridColumn189.Visible = true;
            this.gridColumn189.VisibleIndex = 15;
            // 
            // gridColumn190
            // 
            this.gridColumn190.Caption = "MOS[2.8,3]";
            this.gridColumn190.FieldName = "Pesq28_30";
            this.gridColumn190.Name = "gridColumn190";
            this.gridColumn190.Visible = true;
            this.gridColumn190.VisibleIndex = 16;
            // 
            // gridColumn191
            // 
            this.gridColumn191.Caption = "MOS[3,5]";
            this.gridColumn191.FieldName = "Pesq30";
            this.gridColumn191.Name = "gridColumn191";
            this.gridColumn191.Visible = true;
            this.gridColumn191.VisibleIndex = 17;
            // 
            // gridColumn192
            // 
            this.gridColumn192.Caption = "TxPower均值";
            this.gridColumn192.FieldName = "Txpowermean";
            this.gridColumn192.Name = "gridColumn192";
            this.gridColumn192.Visible = true;
            this.gridColumn192.VisibleIndex = 18;
            // 
            // gridColumn193
            // 
            this.gridColumn193.Caption = "Txpower[-50,-20]";
            this.gridColumn193.FieldName = "TxpowerF20";
            this.gridColumn193.Name = "gridColumn193";
            this.gridColumn193.Visible = true;
            this.gridColumn193.VisibleIndex = 19;
            // 
            // gridColumn194
            // 
            this.gridColumn194.Caption = "TxPower[-20,0]";
            this.gridColumn194.FieldName = "TxpowerF20T0";
            this.gridColumn194.Name = "gridColumn194";
            this.gridColumn194.Visible = true;
            this.gridColumn194.VisibleIndex = 20;
            // 
            // gridColumn195
            // 
            this.gridColumn195.Caption = "TxPower[0,15]";
            this.gridColumn195.FieldName = "Txpower0T15";
            this.gridColumn195.Name = "gridColumn195";
            this.gridColumn195.Visible = true;
            this.gridColumn195.VisibleIndex = 21;
            // 
            // gridColumn196
            // 
            this.gridColumn196.Caption = "TxPower[15,34]";
            this.gridColumn196.FieldName = "Txpower15";
            this.gridColumn196.Name = "gridColumn196";
            this.gridColumn196.Visible = true;
            this.gridColumn196.VisibleIndex = 22;
            // 
            // gridColumn197
            // 
            this.gridColumn197.Caption = "小区1";
            this.gridColumn197.FieldName = "Cell1";
            this.gridColumn197.Name = "gridColumn197";
            this.gridColumn197.Visible = true;
            this.gridColumn197.VisibleIndex = 23;
            // 
            // gridColumn198
            // 
            this.gridColumn198.Caption = "小区2";
            this.gridColumn198.FieldName = "Cell2";
            this.gridColumn198.Name = "gridColumn198";
            this.gridColumn198.Visible = true;
            this.gridColumn198.VisibleIndex = 24;
            // 
            // gridColumn199
            // 
            this.gridColumn199.Caption = "小区3";
            this.gridColumn199.FieldName = "Cell3";
            this.gridColumn199.Name = "gridColumn199";
            this.gridColumn199.Visible = true;
            this.gridColumn199.VisibleIndex = 25;
            // 
            // gridColumn200
            // 
            this.gridColumn200.Caption = "所属网格";
            this.gridColumn200.FieldName = "Strgrid";
            this.gridColumn200.Name = "gridColumn200";
            this.gridColumn200.Visible = true;
            this.gridColumn200.VisibleIndex = 26;
            // 
            // gridColumn201
            // 
            this.gridColumn201.Caption = "所属道路";
            this.gridColumn201.FieldName = "Strroad";
            this.gridColumn201.Name = "gridColumn201";
            this.gridColumn201.Visible = true;
            this.gridColumn201.VisibleIndex = 27;
            // 
            // gridColumn202
            // 
            this.gridColumn202.Caption = "经度";
            this.gridColumn202.FieldName = "Imlongitude";
            this.gridColumn202.Name = "gridColumn202";
            // 
            // gridColumn203
            // 
            this.gridColumn203.Caption = "纬度";
            this.gridColumn203.FieldName = "Imlatitude";
            this.gridColumn203.Name = "gridColumn203";
            // 
            // gridColumn204
            // 
            this.gridColumn204.Caption = "ifileid";
            this.gridColumn204.FieldName = "Ifileid";
            this.gridColumn204.Name = "gridColumn204";
            // 
            // gridColumn205
            // 
            this.gridColumn205.Caption = "istime";
            this.gridColumn205.FieldName = "Istime";
            this.gridColumn205.Name = "gridColumn205";
            // 
            // gridColumn206
            // 
            this.gridColumn206.Caption = "ietime";
            this.gridColumn206.FieldName = "Ietime";
            this.gridColumn206.Name = "gridColumn206";
            // 
            // gridColumn212
            // 
            this.gridColumn212.Caption = "gridColumn212";
            this.gridColumn212.FieldName = "Iid";
            this.gridColumn212.Name = "gridColumn212";
            // 
            // gridView13
            // 
            this.gridView13.GridControl = this.gridControl7;
            this.gridView13.Name = "gridView13";
            // 
            // btnClearFly
            // 
            this.btnClearFly.Location = new System.Drawing.Point(0, 0);
            this.btnClearFly.Name = "btnClearFly";
            this.btnClearFly.Size = new System.Drawing.Size(75, 23);
            this.btnClearFly.TabIndex = 0;
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.ColumnCount = 1;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.Controls.Add(this.panel1, 0, 0);
            this.tableLayoutPanel1.Controls.Add(this.xtraTabControl1, 0, 1);
            this.tableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 2;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 0F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(849, 417);
            this.tableLayoutPanel1.TabIndex = 1;
            // 
            // panel1
            // 
            this.panel1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.panel1.Controls.Add(this.label1);
            this.panel1.Controls.Add(this.btnNext1);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel1.Location = new System.Drawing.Point(3, 3);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(843, 1);
            this.panel1.TabIndex = 2;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(26, 20);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(66, 14);
            this.label1.TabIndex = 1;
            this.label1.Text = "1.道路汇聚";
            // 
            // btnNext1
            // 
            this.btnNext1.Enabled = false;
            this.btnNext1.Location = new System.Drawing.Point(101, 16);
            this.btnNext1.Name = "btnNext1";
            this.btnNext1.Size = new System.Drawing.Size(75, 23);
            this.btnNext1.TabIndex = 0;
            this.btnNext1.Text = "下一步";
            this.btnNext1.UseVisualStyleBackColor = true;
            this.btnNext1.Click += new System.EventHandler(this.btnNext1_Click);
            // 
            // gridBand7
            // 
            this.gridBand7.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand7.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand7.Caption = "基础信息";
            this.gridBand7.Columns.Add(this.bandedGridColumn27);
            this.gridBand7.Columns.Add(this.bandedGridColumn28);
            this.gridBand7.Columns.Add(this.bandedGridColumn24);
            this.gridBand7.Columns.Add(this.bandedGridColumn25);
            this.gridBand7.Columns.Add(this.bandedGridColumn26);
            this.gridBand7.Columns.Add(this.bandedGridColumn29);
            this.gridBand7.Columns.Add(this.bandedGridColumn30);
            this.gridBand7.Name = "gridBand7";
            this.gridBand7.Width = 555;
            // 
            // gridBand1
            // 
            this.gridBand1.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand1.Caption = "测试信息";
            this.gridBand1.Columns.Add(this.bandedGridColumn1);
            this.gridBand1.Columns.Add(this.bandedGridColumn2);
            this.gridBand1.Columns.Add(this.bandedGridColumn31);
            this.gridBand1.Columns.Add(this.bandedGridColumn32);
            this.gridBand1.Columns.Add(this.bandedGridColumn33);
            this.gridBand1.Columns.Add(this.bandedGridColumn34);
            this.gridBand1.Name = "gridBand1";
            this.gridBand1.Width = 150;
            // 
            // gridBand8
            // 
            this.gridBand8.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand8.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand8.Caption = "电平";
            this.gridBand8.Columns.Add(this.bandedGridColumn3);
            this.gridBand8.Columns.Add(this.bandedGridColumn4);
            this.gridBand8.Columns.Add(this.bandedGridColumn5);
            this.gridBand8.Columns.Add(this.bandedGridColumn6);
            this.gridBand8.Columns.Add(this.bandedGridColumn7);
            this.gridBand8.Columns.Add(this.bandedGridColumn8);
            this.gridBand8.Columns.Add(this.bandedGridColumn9);
            this.gridBand8.Name = "gridBand8";
            this.gridBand8.Width = 525;
            // 
            // gridBand9
            // 
            this.gridBand9.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand9.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand9.Caption = "C/I";
            this.gridBand9.Columns.Add(this.bandedGridColumn10);
            this.gridBand9.Columns.Add(this.bandedGridColumn11);
            this.gridBand9.Columns.Add(this.bandedGridColumn12);
            this.gridBand9.Columns.Add(this.bandedGridColumn13);
            this.gridBand9.Columns.Add(this.bandedGridColumn14);
            this.gridBand9.Name = "gridBand9";
            this.gridBand9.Width = 375;
            // 
            // gridBand10
            // 
            this.gridBand10.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand10.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand10.Caption = "MOS";
            this.gridBand10.Columns.Add(this.bandedGridColumn15);
            this.gridBand10.Columns.Add(this.bandedGridColumn16);
            this.gridBand10.Columns.Add(this.bandedGridColumn17);
            this.gridBand10.Columns.Add(this.bandedGridColumn18);
            this.gridBand10.Name = "gridBand10";
            this.gridBand10.Width = 300;
            // 
            // gridBand11
            // 
            this.gridBand11.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand11.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand11.Caption = "TxPower";
            this.gridBand11.Columns.Add(this.bandedGridColumn19);
            this.gridBand11.Columns.Add(this.bandedGridColumn20);
            this.gridBand11.Columns.Add(this.bandedGridColumn21);
            this.gridBand11.Columns.Add(this.bandedGridColumn22);
            this.gridBand11.Columns.Add(this.bandedGridColumn23);
            this.gridBand11.Name = "gridBand11";
            this.gridBand11.Width = 375;
            // 
            // bandedGridColumn35
            // 
            this.bandedGridColumn35.Caption = "距离(米)";
            this.bandedGridColumn35.CustomizationCaption = "距离(米)";
            this.bandedGridColumn35.FieldName = "Idistance";
            this.bandedGridColumn35.Name = "bandedGridColumn35";
            this.bandedGridColumn35.Visible = true;
            // 
            // bandedGridColumn36
            // 
            this.bandedGridColumn36.Caption = "时长(秒)";
            this.bandedGridColumn36.CustomizationCaption = "时长(秒)";
            this.bandedGridColumn36.FieldName = "Iduration";
            this.bandedGridColumn36.Name = "bandedGridColumn36";
            this.bandedGridColumn36.Visible = true;
            // 
            // bandedGridColumn37
            // 
            this.bandedGridColumn37.Caption = "均值";
            this.bandedGridColumn37.CustomizationCaption = "电平均值";
            this.bandedGridColumn37.FieldName = "Rxlmean";
            this.bandedGridColumn37.Name = "bandedGridColumn37";
            this.bandedGridColumn37.Visible = true;
            // 
            // bandedGridColumn38
            // 
            this.bandedGridColumn38.Caption = "[-75,-10]";
            this.bandedGridColumn38.CustomizationCaption = "电平[-75,-10]";
            this.bandedGridColumn38.FieldName = "Rxl75";
            this.bandedGridColumn38.Name = "bandedGridColumn38";
            this.bandedGridColumn38.Visible = true;
            // 
            // bandedGridColumn39
            // 
            this.bandedGridColumn39.Caption = "[-80,-75]";
            this.bandedGridColumn39.CustomizationCaption = "电平[-80,-75]";
            this.bandedGridColumn39.FieldName = "Rxl76_80";
            this.bandedGridColumn39.Name = "bandedGridColumn39";
            this.bandedGridColumn39.Visible = true;
            // 
            // bandedGridColumn40
            // 
            this.bandedGridColumn40.Caption = "[-85,-80]";
            this.bandedGridColumn40.CustomizationCaption = "电平[-85,-80]";
            this.bandedGridColumn40.FieldName = "Rxl81_85";
            this.bandedGridColumn40.Name = "bandedGridColumn40";
            this.bandedGridColumn40.Visible = true;
            // 
            // bandedGridColumn41
            // 
            this.bandedGridColumn41.Caption = "[-90,-85]";
            this.bandedGridColumn41.CustomizationCaption = "电平[-90,-85]";
            this.bandedGridColumn41.FieldName = "Rxl86_90";
            this.bandedGridColumn41.Name = "bandedGridColumn41";
            this.bandedGridColumn41.Visible = true;
            // 
            // bandedGridColumn42
            // 
            this.bandedGridColumn42.Caption = "[-94,-90]";
            this.bandedGridColumn42.CustomizationCaption = "电平[-94,-90]";
            this.bandedGridColumn42.FieldName = "Rxl91_94";
            this.bandedGridColumn42.Name = "bandedGridColumn42";
            this.bandedGridColumn42.Visible = true;
            // 
            // bandedGridColumn43
            // 
            this.bandedGridColumn43.Caption = "[-140,-94]";
            this.bandedGridColumn43.CustomizationCaption = "电平[-140,-94]";
            this.bandedGridColumn43.FieldName = "Rxl94";
            this.bandedGridColumn43.Name = "bandedGridColumn43";
            this.bandedGridColumn43.Visible = true;
            // 
            // bandedGridColumn44
            // 
            this.bandedGridColumn44.Caption = "均值";
            this.bandedGridColumn44.CustomizationCaption = "DPCH C/I均值";
            this.bandedGridColumn44.FieldName = "Dc2imean";
            this.bandedGridColumn44.Name = "bandedGridColumn44";
            this.bandedGridColumn44.Visible = true;
            // 
            // bandedGridColumn45
            // 
            this.bandedGridColumn45.Caption = "[-20,-10]";
            this.bandedGridColumn45.CustomizationCaption = "C/I[-20,-10]";
            this.bandedGridColumn45.FieldName = "Dc2iF10";
            this.bandedGridColumn45.Name = "bandedGridColumn45";
            this.bandedGridColumn45.Visible = true;
            // 
            // bandedGridColumn46
            // 
            this.bandedGridColumn46.Caption = "[-10,-3]";
            this.bandedGridColumn46.CustomizationCaption = "C/I[-10,-3]";
            this.bandedGridColumn46.FieldName = "Dc2iF10TF3";
            this.bandedGridColumn46.Name = "bandedGridColumn46";
            this.bandedGridColumn46.Visible = true;
            // 
            // bandedGridColumn47
            // 
            this.bandedGridColumn47.Caption = "[-3,15]";
            this.bandedGridColumn47.CustomizationCaption = "C/I[-3,15]";
            this.bandedGridColumn47.FieldName = "Dc2iF3T15";
            this.bandedGridColumn47.Name = "bandedGridColumn47";
            this.bandedGridColumn47.Visible = true;
            // 
            // bandedGridColumn48
            // 
            this.bandedGridColumn48.Caption = "[15,25]";
            this.bandedGridColumn48.CustomizationCaption = "C/I[15,25]";
            this.bandedGridColumn48.FieldName = "Dc2i15";
            this.bandedGridColumn48.Name = "bandedGridColumn48";
            this.bandedGridColumn48.Visible = true;
            // 
            // bandedGridColumn49
            // 
            this.bandedGridColumn49.Caption = "均值";
            this.bandedGridColumn49.CustomizationCaption = "MOS均值";
            this.bandedGridColumn49.FieldName = "Pesqmean";
            this.bandedGridColumn49.Name = "bandedGridColumn49";
            this.bandedGridColumn49.Visible = true;
            // 
            // bandedGridColumn50
            // 
            this.bandedGridColumn50.Caption = "[0,2.8]";
            this.bandedGridColumn50.CustomizationCaption = "MOS[0,2.8]";
            this.bandedGridColumn50.FieldName = "Pesq28";
            this.bandedGridColumn50.Name = "bandedGridColumn50";
            this.bandedGridColumn50.Visible = true;
            // 
            // bandedGridColumn51
            // 
            this.bandedGridColumn51.Caption = "[2.8,3]";
            this.bandedGridColumn51.CustomizationCaption = "MOS[2.8,3]";
            this.bandedGridColumn51.FieldName = "Pesq28_30";
            this.bandedGridColumn51.Name = "bandedGridColumn51";
            this.bandedGridColumn51.Visible = true;
            // 
            // bandedGridColumn52
            // 
            this.bandedGridColumn52.Caption = "[3,5]";
            this.bandedGridColumn52.CustomizationCaption = "MOS[3,5]";
            this.bandedGridColumn52.FieldName = "Pesq30";
            this.bandedGridColumn52.Name = "bandedGridColumn52";
            this.bandedGridColumn52.Visible = true;
            // 
            // bandedGridColumn53
            // 
            this.bandedGridColumn53.Caption = "均值";
            this.bandedGridColumn53.CustomizationCaption = "TxPower均值";
            this.bandedGridColumn53.FieldName = "Txpowermean";
            this.bandedGridColumn53.Name = "bandedGridColumn53";
            this.bandedGridColumn53.Visible = true;
            // 
            // bandedGridColumn54
            // 
            this.bandedGridColumn54.Caption = "[-50,-20]";
            this.bandedGridColumn54.CustomizationCaption = "Txpower[-50,-20]";
            this.bandedGridColumn54.FieldName = "TxpowerF20";
            this.bandedGridColumn54.Name = "bandedGridColumn54";
            this.bandedGridColumn54.Visible = true;
            // 
            // bandedGridColumn55
            // 
            this.bandedGridColumn55.Caption = "[-20,0]";
            this.bandedGridColumn55.CustomizationCaption = "TxPower[-20,0]";
            this.bandedGridColumn55.FieldName = "TxpowerF20T0";
            this.bandedGridColumn55.Name = "bandedGridColumn55";
            this.bandedGridColumn55.Visible = true;
            // 
            // bandedGridColumn56
            // 
            this.bandedGridColumn56.Caption = "[0,15]";
            this.bandedGridColumn56.CustomizationCaption = "TxPower[0,15]";
            this.bandedGridColumn56.FieldName = "Txpower0T15";
            this.bandedGridColumn56.Name = "bandedGridColumn56";
            this.bandedGridColumn56.Visible = true;
            // 
            // bandedGridColumn57
            // 
            this.bandedGridColumn57.Caption = "[15,34]";
            this.bandedGridColumn57.CustomizationCaption = "TxPower[15,34]";
            this.bandedGridColumn57.FieldName = "Txpower15";
            this.bandedGridColumn57.Name = "bandedGridColumn57";
            this.bandedGridColumn57.Visible = true;
            // 
            // bandedGridColumn58
            // 
            this.bandedGridColumn58.Caption = "TOP1小区";
            this.bandedGridColumn58.CustomizationCaption = "TOP1小区";
            this.bandedGridColumn58.FieldName = "Cell1";
            this.bandedGridColumn58.Name = "bandedGridColumn58";
            this.bandedGridColumn58.Visible = true;
            this.bandedGridColumn58.Width = 85;
            // 
            // bandedGridColumn59
            // 
            this.bandedGridColumn59.Caption = "TOP2小区";
            this.bandedGridColumn59.CustomizationCaption = "TOP2小区";
            this.bandedGridColumn59.FieldName = "Cell2";
            this.bandedGridColumn59.Name = "bandedGridColumn59";
            this.bandedGridColumn59.Visible = true;
            this.bandedGridColumn59.Width = 85;
            // 
            // bandedGridColumn60
            // 
            this.bandedGridColumn60.Caption = "TOP3小区";
            this.bandedGridColumn60.CustomizationCaption = "TOP3小区";
            this.bandedGridColumn60.FieldName = "Cell3";
            this.bandedGridColumn60.Name = "bandedGridColumn60";
            this.bandedGridColumn60.Visible = true;
            this.bandedGridColumn60.Width = 85;
            // 
            // bandedGridColumn61
            // 
            this.bandedGridColumn61.Caption = "所属网格";
            this.bandedGridColumn61.CustomizationCaption = "所属网格";
            this.bandedGridColumn61.FieldName = "Strgrid";
            this.bandedGridColumn61.Name = "bandedGridColumn61";
            this.bandedGridColumn61.Visible = true;
            // 
            // bandedGridColumn62
            // 
            this.bandedGridColumn62.Caption = "所属道路";
            this.bandedGridColumn62.CustomizationCaption = "所属道路";
            this.bandedGridColumn62.FieldName = "Strroad";
            this.bandedGridColumn62.Name = "bandedGridColumn62";
            this.bandedGridColumn62.Visible = true;
            // 
            // bandedGridColumn63
            // 
            this.bandedGridColumn63.Caption = "经度";
            this.bandedGridColumn63.CustomizationCaption = "经度";
            this.bandedGridColumn63.FieldName = "Imlongitude";
            this.bandedGridColumn63.Name = "bandedGridColumn63";
            this.bandedGridColumn63.Visible = true;
            // 
            // bandedGridColumn64
            // 
            this.bandedGridColumn64.Caption = "纬度";
            this.bandedGridColumn64.CustomizationCaption = "纬度";
            this.bandedGridColumn64.FieldName = "Imlatitude";
            this.bandedGridColumn64.Name = "bandedGridColumn64";
            this.bandedGridColumn64.Visible = true;
            // 
            // bandedGridColumn65
            // 
            this.bandedGridColumn65.Caption = "ifileid";
            this.bandedGridColumn65.CustomizationCaption = "ifileid";
            this.bandedGridColumn65.FieldName = "Ifileid";
            this.bandedGridColumn65.Name = "bandedGridColumn65";
            // 
            // bandedGridColumn66
            // 
            this.bandedGridColumn66.Caption = "istime";
            this.bandedGridColumn66.CustomizationCaption = "istime";
            this.bandedGridColumn66.FieldName = "Istime";
            this.bandedGridColumn66.Name = "bandedGridColumn66";
            // 
            // bandedGridColumn67
            // 
            this.bandedGridColumn67.Caption = "ietime";
            this.bandedGridColumn67.CustomizationCaption = "ietime";
            this.bandedGridColumn67.FieldName = "Ietime";
            this.bandedGridColumn67.Name = "bandedGridColumn67";
            // 
            // bandedGridColumn68
            // 
            this.bandedGridColumn68.Caption = "gridColumn207";
            this.bandedGridColumn68.CustomizationCaption = "gridColumn207";
            this.bandedGridColumn68.FieldName = "Iid";
            this.bandedGridColumn68.Name = "bandedGridColumn68";
            // 
            // gridBand2
            // 
            this.gridBand2.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand2.Caption = "基础信息";
            this.gridBand2.Columns.Add(this.bandedGridColumn61);
            this.gridBand2.Columns.Add(this.bandedGridColumn62);
            this.gridBand2.Columns.Add(this.bandedGridColumn58);
            this.gridBand2.Columns.Add(this.bandedGridColumn59);
            this.gridBand2.Columns.Add(this.bandedGridColumn60);
            this.gridBand2.Columns.Add(this.bandedGridColumn63);
            this.gridBand2.Columns.Add(this.bandedGridColumn64);
            this.gridBand2.MinWidth = 20;
            this.gridBand2.Name = "gridBand2";
            this.gridBand2.Width = 555;
            // 
            // gridBand12
            // 
            this.gridBand12.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand12.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand12.Caption = "测试信息";
            this.gridBand12.Columns.Add(this.bandedGridColumn35);
            this.gridBand12.Columns.Add(this.bandedGridColumn36);
            this.gridBand12.Columns.Add(this.bandedGridColumn65);
            this.gridBand12.Columns.Add(this.bandedGridColumn66);
            this.gridBand12.Columns.Add(this.bandedGridColumn67);
            this.gridBand12.Columns.Add(this.bandedGridColumn68);
            this.gridBand12.MinWidth = 20;
            this.gridBand12.Name = "gridBand12";
            this.gridBand12.Width = 150;
            // 
            // gridBand13
            // 
            this.gridBand13.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand13.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand13.Caption = "电平";
            this.gridBand13.Columns.Add(this.bandedGridColumn37);
            this.gridBand13.Columns.Add(this.bandedGridColumn38);
            this.gridBand13.Columns.Add(this.bandedGridColumn39);
            this.gridBand13.Columns.Add(this.bandedGridColumn40);
            this.gridBand13.Columns.Add(this.bandedGridColumn41);
            this.gridBand13.Columns.Add(this.bandedGridColumn42);
            this.gridBand13.Columns.Add(this.bandedGridColumn43);
            this.gridBand13.MinWidth = 20;
            this.gridBand13.Name = "gridBand13";
            this.gridBand13.Width = 525;
            // 
            // gridBand14
            // 
            this.gridBand14.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand14.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand14.Caption = "C/I";
            this.gridBand14.Columns.Add(this.bandedGridColumn44);
            this.gridBand14.Columns.Add(this.bandedGridColumn45);
            this.gridBand14.Columns.Add(this.bandedGridColumn46);
            this.gridBand14.Columns.Add(this.bandedGridColumn47);
            this.gridBand14.Columns.Add(this.bandedGridColumn48);
            this.gridBand14.MinWidth = 20;
            this.gridBand14.Name = "gridBand14";
            this.gridBand14.Width = 375;
            // 
            // gridBand15
            // 
            this.gridBand15.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand15.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand15.Caption = "MOS";
            this.gridBand15.Columns.Add(this.bandedGridColumn49);
            this.gridBand15.Columns.Add(this.bandedGridColumn50);
            this.gridBand15.Columns.Add(this.bandedGridColumn51);
            this.gridBand15.Columns.Add(this.bandedGridColumn52);
            this.gridBand15.MinWidth = 20;
            this.gridBand15.Name = "gridBand15";
            this.gridBand15.Width = 300;
            // 
            // gridBand16
            // 
            this.gridBand16.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand16.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand16.Caption = "TxPower";
            this.gridBand16.Columns.Add(this.bandedGridColumn53);
            this.gridBand16.Columns.Add(this.bandedGridColumn54);
            this.gridBand16.Columns.Add(this.bandedGridColumn55);
            this.gridBand16.Columns.Add(this.bandedGridColumn56);
            this.gridBand16.Columns.Add(this.bandedGridColumn57);
            this.gridBand16.MinWidth = 20;
            this.gridBand16.Name = "gridBand16";
            this.gridBand16.Width = 375;
            // 
            // bandedGridColumn69
            // 
            this.bandedGridColumn69.Caption = "距离(米)";
            this.bandedGridColumn69.CustomizationCaption = "距离(米)";
            this.bandedGridColumn69.FieldName = "Idistance";
            this.bandedGridColumn69.Name = "bandedGridColumn69";
            this.bandedGridColumn69.Visible = true;
            // 
            // bandedGridColumn70
            // 
            this.bandedGridColumn70.Caption = "时长(秒)";
            this.bandedGridColumn70.CustomizationCaption = "时长(秒)";
            this.bandedGridColumn70.FieldName = "Iduration";
            this.bandedGridColumn70.Name = "bandedGridColumn70";
            this.bandedGridColumn70.Visible = true;
            // 
            // bandedGridColumn71
            // 
            this.bandedGridColumn71.Caption = "均值";
            this.bandedGridColumn71.CustomizationCaption = "电平均值";
            this.bandedGridColumn71.FieldName = "Rxlmean";
            this.bandedGridColumn71.Name = "bandedGridColumn71";
            this.bandedGridColumn71.Visible = true;
            // 
            // bandedGridColumn72
            // 
            this.bandedGridColumn72.Caption = "[-75,-10]";
            this.bandedGridColumn72.CustomizationCaption = "电平[-75,-10]";
            this.bandedGridColumn72.FieldName = "Rxl75";
            this.bandedGridColumn72.Name = "bandedGridColumn72";
            this.bandedGridColumn72.Visible = true;
            // 
            // bandedGridColumn73
            // 
            this.bandedGridColumn73.Caption = "[-80,-75]";
            this.bandedGridColumn73.CustomizationCaption = "电平[-80,-75]";
            this.bandedGridColumn73.FieldName = "Rxl76_80";
            this.bandedGridColumn73.Name = "bandedGridColumn73";
            this.bandedGridColumn73.Visible = true;
            // 
            // bandedGridColumn74
            // 
            this.bandedGridColumn74.Caption = "[-85,-80]";
            this.bandedGridColumn74.CustomizationCaption = "电平[-85,-80]";
            this.bandedGridColumn74.FieldName = "Rxl81_85";
            this.bandedGridColumn74.Name = "bandedGridColumn74";
            this.bandedGridColumn74.Visible = true;
            // 
            // bandedGridColumn75
            // 
            this.bandedGridColumn75.Caption = "[-90,-85]";
            this.bandedGridColumn75.CustomizationCaption = "电平[-90,-85]";
            this.bandedGridColumn75.FieldName = "Rxl86_90";
            this.bandedGridColumn75.Name = "bandedGridColumn75";
            this.bandedGridColumn75.Visible = true;
            // 
            // bandedGridColumn76
            // 
            this.bandedGridColumn76.Caption = "[-94,-90]";
            this.bandedGridColumn76.CustomizationCaption = "电平[-94,-90]";
            this.bandedGridColumn76.FieldName = "Rxl91_94";
            this.bandedGridColumn76.Name = "bandedGridColumn76";
            this.bandedGridColumn76.Visible = true;
            // 
            // bandedGridColumn77
            // 
            this.bandedGridColumn77.Caption = "[-140,-94]";
            this.bandedGridColumn77.CustomizationCaption = "电平[-140,-94]";
            this.bandedGridColumn77.FieldName = "Rxl94";
            this.bandedGridColumn77.Name = "bandedGridColumn77";
            this.bandedGridColumn77.Visible = true;
            // 
            // bandedGridColumn78
            // 
            this.bandedGridColumn78.Caption = "均值";
            this.bandedGridColumn78.CustomizationCaption = "DPCH C/I均值";
            this.bandedGridColumn78.FieldName = "Dc2imean";
            this.bandedGridColumn78.Name = "bandedGridColumn78";
            this.bandedGridColumn78.Visible = true;
            // 
            // bandedGridColumn79
            // 
            this.bandedGridColumn79.Caption = "[-20,-10]";
            this.bandedGridColumn79.CustomizationCaption = "C/I[-20,-10]";
            this.bandedGridColumn79.FieldName = "Dc2iF10";
            this.bandedGridColumn79.Name = "bandedGridColumn79";
            this.bandedGridColumn79.Visible = true;
            // 
            // bandedGridColumn80
            // 
            this.bandedGridColumn80.Caption = "[-10,-3]";
            this.bandedGridColumn80.CustomizationCaption = "C/I[-10,-3]";
            this.bandedGridColumn80.FieldName = "Dc2iF10TF3";
            this.bandedGridColumn80.Name = "bandedGridColumn80";
            this.bandedGridColumn80.Visible = true;
            // 
            // bandedGridColumn81
            // 
            this.bandedGridColumn81.Caption = "[-3,15]";
            this.bandedGridColumn81.CustomizationCaption = "C/I[-3,15]";
            this.bandedGridColumn81.FieldName = "Dc2iF3T15";
            this.bandedGridColumn81.Name = "bandedGridColumn81";
            this.bandedGridColumn81.Visible = true;
            // 
            // bandedGridColumn82
            // 
            this.bandedGridColumn82.Caption = "[15,25]";
            this.bandedGridColumn82.CustomizationCaption = "C/I[15,25]";
            this.bandedGridColumn82.FieldName = "Dc2i15";
            this.bandedGridColumn82.Name = "bandedGridColumn82";
            this.bandedGridColumn82.Visible = true;
            // 
            // bandedGridColumn83
            // 
            this.bandedGridColumn83.Caption = "均值";
            this.bandedGridColumn83.CustomizationCaption = "MOS均值";
            this.bandedGridColumn83.FieldName = "Pesqmean";
            this.bandedGridColumn83.Name = "bandedGridColumn83";
            this.bandedGridColumn83.Visible = true;
            // 
            // bandedGridColumn84
            // 
            this.bandedGridColumn84.Caption = "[0,2.8]";
            this.bandedGridColumn84.CustomizationCaption = "MOS[0,2.8]";
            this.bandedGridColumn84.FieldName = "Pesq28";
            this.bandedGridColumn84.Name = "bandedGridColumn84";
            this.bandedGridColumn84.Visible = true;
            // 
            // bandedGridColumn85
            // 
            this.bandedGridColumn85.Caption = "[2.8,3]";
            this.bandedGridColumn85.CustomizationCaption = "MOS[2.8,3]";
            this.bandedGridColumn85.FieldName = "Pesq28_30";
            this.bandedGridColumn85.Name = "bandedGridColumn85";
            this.bandedGridColumn85.Visible = true;
            // 
            // bandedGridColumn86
            // 
            this.bandedGridColumn86.Caption = "[3,5]";
            this.bandedGridColumn86.CustomizationCaption = "MOS[3,5]";
            this.bandedGridColumn86.FieldName = "Pesq30";
            this.bandedGridColumn86.Name = "bandedGridColumn86";
            this.bandedGridColumn86.Visible = true;
            // 
            // bandedGridColumn87
            // 
            this.bandedGridColumn87.Caption = "均值";
            this.bandedGridColumn87.CustomizationCaption = "TxPower均值";
            this.bandedGridColumn87.FieldName = "Txpowermean";
            this.bandedGridColumn87.Name = "bandedGridColumn87";
            this.bandedGridColumn87.Visible = true;
            // 
            // bandedGridColumn88
            // 
            this.bandedGridColumn88.Caption = "[-50,-20]";
            this.bandedGridColumn88.CustomizationCaption = "Txpower[-50,-20]";
            this.bandedGridColumn88.FieldName = "TxpowerF20";
            this.bandedGridColumn88.Name = "bandedGridColumn88";
            this.bandedGridColumn88.Visible = true;
            // 
            // bandedGridColumn89
            // 
            this.bandedGridColumn89.Caption = "[-20,0]";
            this.bandedGridColumn89.CustomizationCaption = "TxPower[-20,0]";
            this.bandedGridColumn89.FieldName = "TxpowerF20T0";
            this.bandedGridColumn89.Name = "bandedGridColumn89";
            this.bandedGridColumn89.Visible = true;
            // 
            // bandedGridColumn90
            // 
            this.bandedGridColumn90.Caption = "[0,15]";
            this.bandedGridColumn90.CustomizationCaption = "TxPower[0,15]";
            this.bandedGridColumn90.FieldName = "Txpower0T15";
            this.bandedGridColumn90.Name = "bandedGridColumn90";
            this.bandedGridColumn90.Visible = true;
            // 
            // bandedGridColumn91
            // 
            this.bandedGridColumn91.Caption = "[15,34]";
            this.bandedGridColumn91.CustomizationCaption = "TxPower[15,34]";
            this.bandedGridColumn91.FieldName = "Txpower15";
            this.bandedGridColumn91.Name = "bandedGridColumn91";
            this.bandedGridColumn91.Visible = true;
            // 
            // bandedGridColumn92
            // 
            this.bandedGridColumn92.Caption = "TOP1小区";
            this.bandedGridColumn92.CustomizationCaption = "TOP1小区";
            this.bandedGridColumn92.FieldName = "Cell1";
            this.bandedGridColumn92.Name = "bandedGridColumn92";
            this.bandedGridColumn92.Visible = true;
            this.bandedGridColumn92.Width = 85;
            // 
            // bandedGridColumn93
            // 
            this.bandedGridColumn93.Caption = "TOP2小区";
            this.bandedGridColumn93.CustomizationCaption = "TOP2小区";
            this.bandedGridColumn93.FieldName = "Cell2";
            this.bandedGridColumn93.Name = "bandedGridColumn93";
            this.bandedGridColumn93.Visible = true;
            this.bandedGridColumn93.Width = 85;
            // 
            // bandedGridColumn94
            // 
            this.bandedGridColumn94.Caption = "TOP3小区";
            this.bandedGridColumn94.CustomizationCaption = "TOP3小区";
            this.bandedGridColumn94.FieldName = "Cell3";
            this.bandedGridColumn94.Name = "bandedGridColumn94";
            this.bandedGridColumn94.Visible = true;
            this.bandedGridColumn94.Width = 85;
            // 
            // bandedGridColumn95
            // 
            this.bandedGridColumn95.Caption = "所属网格";
            this.bandedGridColumn95.CustomizationCaption = "所属网格";
            this.bandedGridColumn95.FieldName = "Strgrid";
            this.bandedGridColumn95.Name = "bandedGridColumn95";
            this.bandedGridColumn95.Visible = true;
            // 
            // bandedGridColumn96
            // 
            this.bandedGridColumn96.Caption = "所属道路";
            this.bandedGridColumn96.CustomizationCaption = "所属道路";
            this.bandedGridColumn96.FieldName = "Strroad";
            this.bandedGridColumn96.Name = "bandedGridColumn96";
            this.bandedGridColumn96.Visible = true;
            // 
            // bandedGridColumn97
            // 
            this.bandedGridColumn97.Caption = "经度";
            this.bandedGridColumn97.CustomizationCaption = "经度";
            this.bandedGridColumn97.FieldName = "Imlongitude";
            this.bandedGridColumn97.Name = "bandedGridColumn97";
            this.bandedGridColumn97.Visible = true;
            // 
            // bandedGridColumn98
            // 
            this.bandedGridColumn98.Caption = "纬度";
            this.bandedGridColumn98.CustomizationCaption = "纬度";
            this.bandedGridColumn98.FieldName = "Imlatitude";
            this.bandedGridColumn98.Name = "bandedGridColumn98";
            this.bandedGridColumn98.Visible = true;
            // 
            // bandedGridColumn99
            // 
            this.bandedGridColumn99.Caption = "ifileid";
            this.bandedGridColumn99.CustomizationCaption = "ifileid";
            this.bandedGridColumn99.FieldName = "Ifileid";
            this.bandedGridColumn99.Name = "bandedGridColumn99";
            // 
            // bandedGridColumn100
            // 
            this.bandedGridColumn100.Caption = "istime";
            this.bandedGridColumn100.CustomizationCaption = "istime";
            this.bandedGridColumn100.FieldName = "Istime";
            this.bandedGridColumn100.Name = "bandedGridColumn100";
            // 
            // bandedGridColumn101
            // 
            this.bandedGridColumn101.Caption = "ietime";
            this.bandedGridColumn101.CustomizationCaption = "ietime";
            this.bandedGridColumn101.FieldName = "Ietime";
            this.bandedGridColumn101.Name = "bandedGridColumn101";
            // 
            // bandedGridColumn102
            // 
            this.bandedGridColumn102.Caption = "gridColumn207";
            this.bandedGridColumn102.CustomizationCaption = "gridColumn207";
            this.bandedGridColumn102.FieldName = "Iid";
            this.bandedGridColumn102.Name = "bandedGridColumn102";
            // 
            // gridBand3
            // 
            this.gridBand3.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand3.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand3.Caption = "基础信息";
            this.gridBand3.Columns.Add(this.bandedGridColumn95);
            this.gridBand3.Columns.Add(this.bandedGridColumn96);
            this.gridBand3.Columns.Add(this.bandedGridColumn92);
            this.gridBand3.Columns.Add(this.bandedGridColumn93);
            this.gridBand3.Columns.Add(this.bandedGridColumn94);
            this.gridBand3.Columns.Add(this.bandedGridColumn97);
            this.gridBand3.Columns.Add(this.bandedGridColumn98);
            this.gridBand3.MinWidth = 20;
            this.gridBand3.Name = "gridBand3";
            this.gridBand3.Width = 555;
            // 
            // gridBand17
            // 
            this.gridBand17.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand17.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand17.Caption = "测试信息";
            this.gridBand17.Columns.Add(this.bandedGridColumn69);
            this.gridBand17.Columns.Add(this.bandedGridColumn70);
            this.gridBand17.Columns.Add(this.bandedGridColumn99);
            this.gridBand17.Columns.Add(this.bandedGridColumn100);
            this.gridBand17.Columns.Add(this.bandedGridColumn101);
            this.gridBand17.Columns.Add(this.bandedGridColumn102);
            this.gridBand17.MinWidth = 20;
            this.gridBand17.Name = "gridBand17";
            this.gridBand17.Width = 150;
            // 
            // gridBand18
            // 
            this.gridBand18.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand18.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand18.Caption = "电平";
            this.gridBand18.Columns.Add(this.bandedGridColumn71);
            this.gridBand18.Columns.Add(this.bandedGridColumn72);
            this.gridBand18.Columns.Add(this.bandedGridColumn73);
            this.gridBand18.Columns.Add(this.bandedGridColumn74);
            this.gridBand18.Columns.Add(this.bandedGridColumn75);
            this.gridBand18.Columns.Add(this.bandedGridColumn76);
            this.gridBand18.Columns.Add(this.bandedGridColumn77);
            this.gridBand18.MinWidth = 20;
            this.gridBand18.Name = "gridBand18";
            this.gridBand18.Width = 525;
            // 
            // gridBand19
            // 
            this.gridBand19.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand19.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand19.Caption = "C/I";
            this.gridBand19.Columns.Add(this.bandedGridColumn78);
            this.gridBand19.Columns.Add(this.bandedGridColumn79);
            this.gridBand19.Columns.Add(this.bandedGridColumn80);
            this.gridBand19.Columns.Add(this.bandedGridColumn81);
            this.gridBand19.Columns.Add(this.bandedGridColumn82);
            this.gridBand19.MinWidth = 20;
            this.gridBand19.Name = "gridBand19";
            this.gridBand19.Width = 375;
            // 
            // gridBand20
            // 
            this.gridBand20.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand20.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand20.Caption = "MOS";
            this.gridBand20.Columns.Add(this.bandedGridColumn83);
            this.gridBand20.Columns.Add(this.bandedGridColumn84);
            this.gridBand20.Columns.Add(this.bandedGridColumn85);
            this.gridBand20.Columns.Add(this.bandedGridColumn86);
            this.gridBand20.MinWidth = 20;
            this.gridBand20.Name = "gridBand20";
            this.gridBand20.Width = 300;
            // 
            // gridBand21
            // 
            this.gridBand21.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand21.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand21.Caption = "TxPower";
            this.gridBand21.Columns.Add(this.bandedGridColumn87);
            this.gridBand21.Columns.Add(this.bandedGridColumn88);
            this.gridBand21.Columns.Add(this.bandedGridColumn89);
            this.gridBand21.Columns.Add(this.bandedGridColumn90);
            this.gridBand21.Columns.Add(this.bandedGridColumn91);
            this.gridBand21.MinWidth = 20;
            this.gridBand21.Name = "gridBand21";
            this.gridBand21.Width = 375;
            // 
            // bandedGridColumn103
            // 
            this.bandedGridColumn103.Caption = "距离(米)";
            this.bandedGridColumn103.CustomizationCaption = "距离(米)";
            this.bandedGridColumn103.FieldName = "Idistance";
            this.bandedGridColumn103.Name = "bandedGridColumn103";
            this.bandedGridColumn103.Visible = true;
            // 
            // bandedGridColumn104
            // 
            this.bandedGridColumn104.Caption = "时长(秒)";
            this.bandedGridColumn104.CustomizationCaption = "时长(秒)";
            this.bandedGridColumn104.FieldName = "Iduration";
            this.bandedGridColumn104.Name = "bandedGridColumn104";
            this.bandedGridColumn104.Visible = true;
            // 
            // bandedGridColumn105
            // 
            this.bandedGridColumn105.Caption = "均值";
            this.bandedGridColumn105.CustomizationCaption = "电平均值";
            this.bandedGridColumn105.FieldName = "Rxlmean";
            this.bandedGridColumn105.Name = "bandedGridColumn105";
            this.bandedGridColumn105.Visible = true;
            // 
            // bandedGridColumn106
            // 
            this.bandedGridColumn106.Caption = "[-75,-10]";
            this.bandedGridColumn106.CustomizationCaption = "电平[-75,-10]";
            this.bandedGridColumn106.FieldName = "Rxl75";
            this.bandedGridColumn106.Name = "bandedGridColumn106";
            this.bandedGridColumn106.Visible = true;
            // 
            // bandedGridColumn107
            // 
            this.bandedGridColumn107.Caption = "[-80,-75]";
            this.bandedGridColumn107.CustomizationCaption = "电平[-80,-75]";
            this.bandedGridColumn107.FieldName = "Rxl76_80";
            this.bandedGridColumn107.Name = "bandedGridColumn107";
            this.bandedGridColumn107.Visible = true;
            // 
            // bandedGridColumn108
            // 
            this.bandedGridColumn108.Caption = "[-85,-80]";
            this.bandedGridColumn108.CustomizationCaption = "电平[-85,-80]";
            this.bandedGridColumn108.FieldName = "Rxl81_85";
            this.bandedGridColumn108.Name = "bandedGridColumn108";
            this.bandedGridColumn108.Visible = true;
            // 
            // bandedGridColumn109
            // 
            this.bandedGridColumn109.Caption = "[-90,-85]";
            this.bandedGridColumn109.CustomizationCaption = "电平[-90,-85]";
            this.bandedGridColumn109.FieldName = "Rxl86_90";
            this.bandedGridColumn109.Name = "bandedGridColumn109";
            this.bandedGridColumn109.Visible = true;
            // 
            // bandedGridColumn110
            // 
            this.bandedGridColumn110.Caption = "[-94,-90]";
            this.bandedGridColumn110.CustomizationCaption = "电平[-94,-90]";
            this.bandedGridColumn110.FieldName = "Rxl91_94";
            this.bandedGridColumn110.Name = "bandedGridColumn110";
            this.bandedGridColumn110.Visible = true;
            // 
            // bandedGridColumn111
            // 
            this.bandedGridColumn111.Caption = "[-140,-94]";
            this.bandedGridColumn111.CustomizationCaption = "电平[-140,-94]";
            this.bandedGridColumn111.FieldName = "Rxl94";
            this.bandedGridColumn111.Name = "bandedGridColumn111";
            this.bandedGridColumn111.Visible = true;
            // 
            // bandedGridColumn112
            // 
            this.bandedGridColumn112.Caption = "均值";
            this.bandedGridColumn112.CustomizationCaption = "DPCH C/I均值";
            this.bandedGridColumn112.FieldName = "Dc2imean";
            this.bandedGridColumn112.Name = "bandedGridColumn112";
            this.bandedGridColumn112.Visible = true;
            // 
            // bandedGridColumn113
            // 
            this.bandedGridColumn113.Caption = "[-20,-10]";
            this.bandedGridColumn113.CustomizationCaption = "C/I[-20,-10]";
            this.bandedGridColumn113.FieldName = "Dc2iF10";
            this.bandedGridColumn113.Name = "bandedGridColumn113";
            this.bandedGridColumn113.Visible = true;
            // 
            // bandedGridColumn114
            // 
            this.bandedGridColumn114.Caption = "[-10,-3]";
            this.bandedGridColumn114.CustomizationCaption = "C/I[-10,-3]";
            this.bandedGridColumn114.FieldName = "Dc2iF10TF3";
            this.bandedGridColumn114.Name = "bandedGridColumn114";
            this.bandedGridColumn114.Visible = true;
            // 
            // bandedGridColumn115
            // 
            this.bandedGridColumn115.Caption = "[-3,15]";
            this.bandedGridColumn115.CustomizationCaption = "C/I[-3,15]";
            this.bandedGridColumn115.FieldName = "Dc2iF3T15";
            this.bandedGridColumn115.Name = "bandedGridColumn115";
            this.bandedGridColumn115.Visible = true;
            // 
            // bandedGridColumn116
            // 
            this.bandedGridColumn116.Caption = "[15,25]";
            this.bandedGridColumn116.CustomizationCaption = "C/I[15,25]";
            this.bandedGridColumn116.FieldName = "Dc2i15";
            this.bandedGridColumn116.Name = "bandedGridColumn116";
            this.bandedGridColumn116.Visible = true;
            // 
            // bandedGridColumn117
            // 
            this.bandedGridColumn117.Caption = "均值";
            this.bandedGridColumn117.CustomizationCaption = "MOS均值";
            this.bandedGridColumn117.FieldName = "Pesqmean";
            this.bandedGridColumn117.Name = "bandedGridColumn117";
            this.bandedGridColumn117.Visible = true;
            // 
            // bandedGridColumn118
            // 
            this.bandedGridColumn118.Caption = "[0,2.8]";
            this.bandedGridColumn118.CustomizationCaption = "MOS[0,2.8]";
            this.bandedGridColumn118.FieldName = "Pesq28";
            this.bandedGridColumn118.Name = "bandedGridColumn118";
            this.bandedGridColumn118.Visible = true;
            // 
            // bandedGridColumn119
            // 
            this.bandedGridColumn119.Caption = "[2.8,3]";
            this.bandedGridColumn119.CustomizationCaption = "MOS[2.8,3]";
            this.bandedGridColumn119.FieldName = "Pesq28_30";
            this.bandedGridColumn119.Name = "bandedGridColumn119";
            this.bandedGridColumn119.Visible = true;
            // 
            // bandedGridColumn120
            // 
            this.bandedGridColumn120.Caption = "[3,5]";
            this.bandedGridColumn120.CustomizationCaption = "MOS[3,5]";
            this.bandedGridColumn120.FieldName = "Pesq30";
            this.bandedGridColumn120.Name = "bandedGridColumn120";
            this.bandedGridColumn120.Visible = true;
            // 
            // bandedGridColumn121
            // 
            this.bandedGridColumn121.Caption = "均值";
            this.bandedGridColumn121.CustomizationCaption = "TxPower均值";
            this.bandedGridColumn121.FieldName = "Txpowermean";
            this.bandedGridColumn121.Name = "bandedGridColumn121";
            this.bandedGridColumn121.Visible = true;
            // 
            // bandedGridColumn122
            // 
            this.bandedGridColumn122.Caption = "[-50,-20]";
            this.bandedGridColumn122.CustomizationCaption = "Txpower[-50,-20]";
            this.bandedGridColumn122.FieldName = "TxpowerF20";
            this.bandedGridColumn122.Name = "bandedGridColumn122";
            this.bandedGridColumn122.Visible = true;
            // 
            // bandedGridColumn123
            // 
            this.bandedGridColumn123.Caption = "[-20,0]";
            this.bandedGridColumn123.CustomizationCaption = "TxPower[-20,0]";
            this.bandedGridColumn123.FieldName = "TxpowerF20T0";
            this.bandedGridColumn123.Name = "bandedGridColumn123";
            this.bandedGridColumn123.Visible = true;
            // 
            // bandedGridColumn124
            // 
            this.bandedGridColumn124.Caption = "[0,15]";
            this.bandedGridColumn124.CustomizationCaption = "TxPower[0,15]";
            this.bandedGridColumn124.FieldName = "Txpower0T15";
            this.bandedGridColumn124.Name = "bandedGridColumn124";
            this.bandedGridColumn124.Visible = true;
            // 
            // bandedGridColumn125
            // 
            this.bandedGridColumn125.Caption = "[15,34]";
            this.bandedGridColumn125.CustomizationCaption = "TxPower[15,34]";
            this.bandedGridColumn125.FieldName = "Txpower15";
            this.bandedGridColumn125.Name = "bandedGridColumn125";
            this.bandedGridColumn125.Visible = true;
            // 
            // bandedGridColumn126
            // 
            this.bandedGridColumn126.Caption = "TOP1小区";
            this.bandedGridColumn126.CustomizationCaption = "TOP1小区";
            this.bandedGridColumn126.FieldName = "Cell1";
            this.bandedGridColumn126.Name = "bandedGridColumn126";
            this.bandedGridColumn126.Visible = true;
            this.bandedGridColumn126.Width = 85;
            // 
            // bandedGridColumn127
            // 
            this.bandedGridColumn127.Caption = "TOP2小区";
            this.bandedGridColumn127.CustomizationCaption = "TOP2小区";
            this.bandedGridColumn127.FieldName = "Cell2";
            this.bandedGridColumn127.Name = "bandedGridColumn127";
            this.bandedGridColumn127.Visible = true;
            this.bandedGridColumn127.Width = 85;
            // 
            // bandedGridColumn128
            // 
            this.bandedGridColumn128.Caption = "TOP3小区";
            this.bandedGridColumn128.CustomizationCaption = "TOP3小区";
            this.bandedGridColumn128.FieldName = "Cell3";
            this.bandedGridColumn128.Name = "bandedGridColumn128";
            this.bandedGridColumn128.Visible = true;
            this.bandedGridColumn128.Width = 85;
            // 
            // bandedGridColumn129
            // 
            this.bandedGridColumn129.Caption = "所属网格";
            this.bandedGridColumn129.CustomizationCaption = "所属网格";
            this.bandedGridColumn129.FieldName = "Strgrid";
            this.bandedGridColumn129.Name = "bandedGridColumn129";
            this.bandedGridColumn129.Visible = true;
            // 
            // bandedGridColumn130
            // 
            this.bandedGridColumn130.Caption = "所属道路";
            this.bandedGridColumn130.CustomizationCaption = "所属道路";
            this.bandedGridColumn130.FieldName = "Strroad";
            this.bandedGridColumn130.Name = "bandedGridColumn130";
            this.bandedGridColumn130.Visible = true;
            // 
            // bandedGridColumn131
            // 
            this.bandedGridColumn131.Caption = "经度";
            this.bandedGridColumn131.CustomizationCaption = "经度";
            this.bandedGridColumn131.FieldName = "Imlongitude";
            this.bandedGridColumn131.Name = "bandedGridColumn131";
            this.bandedGridColumn131.Visible = true;
            // 
            // bandedGridColumn132
            // 
            this.bandedGridColumn132.Caption = "纬度";
            this.bandedGridColumn132.CustomizationCaption = "纬度";
            this.bandedGridColumn132.FieldName = "Imlatitude";
            this.bandedGridColumn132.Name = "bandedGridColumn132";
            this.bandedGridColumn132.Visible = true;
            // 
            // bandedGridColumn133
            // 
            this.bandedGridColumn133.Caption = "ifileid";
            this.bandedGridColumn133.CustomizationCaption = "ifileid";
            this.bandedGridColumn133.FieldName = "Ifileid";
            this.bandedGridColumn133.Name = "bandedGridColumn133";
            // 
            // bandedGridColumn134
            // 
            this.bandedGridColumn134.Caption = "istime";
            this.bandedGridColumn134.CustomizationCaption = "istime";
            this.bandedGridColumn134.FieldName = "Istime";
            this.bandedGridColumn134.Name = "bandedGridColumn134";
            // 
            // bandedGridColumn135
            // 
            this.bandedGridColumn135.Caption = "ietime";
            this.bandedGridColumn135.CustomizationCaption = "ietime";
            this.bandedGridColumn135.FieldName = "Ietime";
            this.bandedGridColumn135.Name = "bandedGridColumn135";
            // 
            // bandedGridColumn136
            // 
            this.bandedGridColumn136.Caption = "gridColumn207";
            this.bandedGridColumn136.CustomizationCaption = "gridColumn207";
            this.bandedGridColumn136.FieldName = "Iid";
            this.bandedGridColumn136.Name = "bandedGridColumn136";
            // 
            // gridBand4
            // 
            this.gridBand4.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand4.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand4.Caption = "基础信息";
            this.gridBand4.Columns.Add(this.bandedGridColumn129);
            this.gridBand4.Columns.Add(this.bandedGridColumn130);
            this.gridBand4.Columns.Add(this.bandedGridColumn126);
            this.gridBand4.Columns.Add(this.bandedGridColumn127);
            this.gridBand4.Columns.Add(this.bandedGridColumn128);
            this.gridBand4.Columns.Add(this.bandedGridColumn131);
            this.gridBand4.Columns.Add(this.bandedGridColumn132);
            this.gridBand4.MinWidth = 20;
            this.gridBand4.Name = "gridBand4";
            this.gridBand4.Width = 555;
            // 
            // gridBand22
            // 
            this.gridBand22.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand22.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand22.Caption = "测试信息";
            this.gridBand22.Columns.Add(this.bandedGridColumn103);
            this.gridBand22.Columns.Add(this.bandedGridColumn104);
            this.gridBand22.Columns.Add(this.bandedGridColumn133);
            this.gridBand22.Columns.Add(this.bandedGridColumn134);
            this.gridBand22.Columns.Add(this.bandedGridColumn135);
            this.gridBand22.Columns.Add(this.bandedGridColumn136);
            this.gridBand22.MinWidth = 20;
            this.gridBand22.Name = "gridBand22";
            this.gridBand22.Width = 150;
            // 
            // gridBand23
            // 
            this.gridBand23.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand23.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand23.Caption = "电平";
            this.gridBand23.Columns.Add(this.bandedGridColumn105);
            this.gridBand23.Columns.Add(this.bandedGridColumn106);
            this.gridBand23.Columns.Add(this.bandedGridColumn107);
            this.gridBand23.Columns.Add(this.bandedGridColumn108);
            this.gridBand23.Columns.Add(this.bandedGridColumn109);
            this.gridBand23.Columns.Add(this.bandedGridColumn110);
            this.gridBand23.Columns.Add(this.bandedGridColumn111);
            this.gridBand23.MinWidth = 20;
            this.gridBand23.Name = "gridBand23";
            this.gridBand23.Width = 525;
            // 
            // gridBand24
            // 
            this.gridBand24.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand24.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand24.Caption = "C/I";
            this.gridBand24.Columns.Add(this.bandedGridColumn112);
            this.gridBand24.Columns.Add(this.bandedGridColumn113);
            this.gridBand24.Columns.Add(this.bandedGridColumn114);
            this.gridBand24.Columns.Add(this.bandedGridColumn115);
            this.gridBand24.Columns.Add(this.bandedGridColumn116);
            this.gridBand24.MinWidth = 20;
            this.gridBand24.Name = "gridBand24";
            this.gridBand24.Width = 375;
            // 
            // gridBand25
            // 
            this.gridBand25.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand25.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand25.Caption = "MOS";
            this.gridBand25.Columns.Add(this.bandedGridColumn117);
            this.gridBand25.Columns.Add(this.bandedGridColumn118);
            this.gridBand25.Columns.Add(this.bandedGridColumn119);
            this.gridBand25.Columns.Add(this.bandedGridColumn120);
            this.gridBand25.MinWidth = 20;
            this.gridBand25.Name = "gridBand25";
            this.gridBand25.Width = 300;
            // 
            // gridBand26
            // 
            this.gridBand26.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand26.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand26.Caption = "TxPower";
            this.gridBand26.Columns.Add(this.bandedGridColumn121);
            this.gridBand26.Columns.Add(this.bandedGridColumn122);
            this.gridBand26.Columns.Add(this.bandedGridColumn123);
            this.gridBand26.Columns.Add(this.bandedGridColumn124);
            this.gridBand26.Columns.Add(this.bandedGridColumn125);
            this.gridBand26.MinWidth = 20;
            this.gridBand26.Name = "gridBand26";
            this.gridBand26.Width = 375;
            // 
            // bandedGridColumn137
            // 
            this.bandedGridColumn137.Caption = "距离(米)";
            this.bandedGridColumn137.CustomizationCaption = "距离(米)";
            this.bandedGridColumn137.FieldName = "Idistance";
            this.bandedGridColumn137.Name = "bandedGridColumn137";
            this.bandedGridColumn137.Visible = true;
            // 
            // bandedGridColumn138
            // 
            this.bandedGridColumn138.Caption = "时长(秒)";
            this.bandedGridColumn138.CustomizationCaption = "时长(秒)";
            this.bandedGridColumn138.FieldName = "Iduration";
            this.bandedGridColumn138.Name = "bandedGridColumn138";
            this.bandedGridColumn138.Visible = true;
            // 
            // bandedGridColumn139
            // 
            this.bandedGridColumn139.Caption = "均值";
            this.bandedGridColumn139.CustomizationCaption = "电平均值";
            this.bandedGridColumn139.FieldName = "Rxlmean";
            this.bandedGridColumn139.Name = "bandedGridColumn139";
            this.bandedGridColumn139.Visible = true;
            // 
            // bandedGridColumn140
            // 
            this.bandedGridColumn140.Caption = "[-75,-10]";
            this.bandedGridColumn140.CustomizationCaption = "电平[-75,-10]";
            this.bandedGridColumn140.FieldName = "Rxl75";
            this.bandedGridColumn140.Name = "bandedGridColumn140";
            this.bandedGridColumn140.Visible = true;
            // 
            // bandedGridColumn141
            // 
            this.bandedGridColumn141.Caption = "[-80,-75]";
            this.bandedGridColumn141.CustomizationCaption = "电平[-80,-75]";
            this.bandedGridColumn141.FieldName = "Rxl76_80";
            this.bandedGridColumn141.Name = "bandedGridColumn141";
            this.bandedGridColumn141.Visible = true;
            // 
            // bandedGridColumn142
            // 
            this.bandedGridColumn142.Caption = "[-85,-80]";
            this.bandedGridColumn142.CustomizationCaption = "电平[-85,-80]";
            this.bandedGridColumn142.FieldName = "Rxl81_85";
            this.bandedGridColumn142.Name = "bandedGridColumn142";
            this.bandedGridColumn142.Visible = true;
            // 
            // bandedGridColumn143
            // 
            this.bandedGridColumn143.Caption = "[-90,-85]";
            this.bandedGridColumn143.CustomizationCaption = "电平[-90,-85]";
            this.bandedGridColumn143.FieldName = "Rxl86_90";
            this.bandedGridColumn143.Name = "bandedGridColumn143";
            this.bandedGridColumn143.Visible = true;
            // 
            // bandedGridColumn144
            // 
            this.bandedGridColumn144.Caption = "[-94,-90]";
            this.bandedGridColumn144.CustomizationCaption = "电平[-94,-90]";
            this.bandedGridColumn144.FieldName = "Rxl91_94";
            this.bandedGridColumn144.Name = "bandedGridColumn144";
            this.bandedGridColumn144.Visible = true;
            // 
            // bandedGridColumn145
            // 
            this.bandedGridColumn145.Caption = "[-140,-94]";
            this.bandedGridColumn145.CustomizationCaption = "电平[-140,-94]";
            this.bandedGridColumn145.FieldName = "Rxl94";
            this.bandedGridColumn145.Name = "bandedGridColumn145";
            this.bandedGridColumn145.Visible = true;
            // 
            // bandedGridColumn146
            // 
            this.bandedGridColumn146.Caption = "均值";
            this.bandedGridColumn146.CustomizationCaption = "DPCH C/I均值";
            this.bandedGridColumn146.FieldName = "Dc2imean";
            this.bandedGridColumn146.Name = "bandedGridColumn146";
            this.bandedGridColumn146.Visible = true;
            // 
            // bandedGridColumn147
            // 
            this.bandedGridColumn147.Caption = "[-20,-10]";
            this.bandedGridColumn147.CustomizationCaption = "C/I[-20,-10]";
            this.bandedGridColumn147.FieldName = "Dc2iF10";
            this.bandedGridColumn147.Name = "bandedGridColumn147";
            this.bandedGridColumn147.Visible = true;
            // 
            // bandedGridColumn148
            // 
            this.bandedGridColumn148.Caption = "[-10,-3]";
            this.bandedGridColumn148.CustomizationCaption = "C/I[-10,-3]";
            this.bandedGridColumn148.FieldName = "Dc2iF10TF3";
            this.bandedGridColumn148.Name = "bandedGridColumn148";
            this.bandedGridColumn148.Visible = true;
            // 
            // bandedGridColumn149
            // 
            this.bandedGridColumn149.Caption = "[-3,15]";
            this.bandedGridColumn149.CustomizationCaption = "C/I[-3,15]";
            this.bandedGridColumn149.FieldName = "Dc2iF3T15";
            this.bandedGridColumn149.Name = "bandedGridColumn149";
            this.bandedGridColumn149.Visible = true;
            // 
            // bandedGridColumn150
            // 
            this.bandedGridColumn150.Caption = "[15,25]";
            this.bandedGridColumn150.CustomizationCaption = "C/I[15,25]";
            this.bandedGridColumn150.FieldName = "Dc2i15";
            this.bandedGridColumn150.Name = "bandedGridColumn150";
            this.bandedGridColumn150.Visible = true;
            // 
            // bandedGridColumn151
            // 
            this.bandedGridColumn151.Caption = "均值";
            this.bandedGridColumn151.CustomizationCaption = "MOS均值";
            this.bandedGridColumn151.FieldName = "Pesqmean";
            this.bandedGridColumn151.Name = "bandedGridColumn151";
            this.bandedGridColumn151.Visible = true;
            // 
            // bandedGridColumn152
            // 
            this.bandedGridColumn152.Caption = "[0,2.8]";
            this.bandedGridColumn152.CustomizationCaption = "MOS[0,2.8]";
            this.bandedGridColumn152.FieldName = "Pesq28";
            this.bandedGridColumn152.Name = "bandedGridColumn152";
            this.bandedGridColumn152.Visible = true;
            // 
            // bandedGridColumn153
            // 
            this.bandedGridColumn153.Caption = "[2.8,3]";
            this.bandedGridColumn153.CustomizationCaption = "MOS[2.8,3]";
            this.bandedGridColumn153.FieldName = "Pesq28_30";
            this.bandedGridColumn153.Name = "bandedGridColumn153";
            this.bandedGridColumn153.Visible = true;
            // 
            // bandedGridColumn154
            // 
            this.bandedGridColumn154.Caption = "[3,5]";
            this.bandedGridColumn154.CustomizationCaption = "MOS[3,5]";
            this.bandedGridColumn154.FieldName = "Pesq30";
            this.bandedGridColumn154.Name = "bandedGridColumn154";
            this.bandedGridColumn154.Visible = true;
            // 
            // bandedGridColumn155
            // 
            this.bandedGridColumn155.Caption = "均值";
            this.bandedGridColumn155.CustomizationCaption = "TxPower均值";
            this.bandedGridColumn155.FieldName = "Txpowermean";
            this.bandedGridColumn155.Name = "bandedGridColumn155";
            this.bandedGridColumn155.Visible = true;
            // 
            // bandedGridColumn156
            // 
            this.bandedGridColumn156.Caption = "[-50,-20]";
            this.bandedGridColumn156.CustomizationCaption = "Txpower[-50,-20]";
            this.bandedGridColumn156.FieldName = "TxpowerF20";
            this.bandedGridColumn156.Name = "bandedGridColumn156";
            this.bandedGridColumn156.Visible = true;
            // 
            // bandedGridColumn157
            // 
            this.bandedGridColumn157.Caption = "[-20,0]";
            this.bandedGridColumn157.CustomizationCaption = "TxPower[-20,0]";
            this.bandedGridColumn157.FieldName = "TxpowerF20T0";
            this.bandedGridColumn157.Name = "bandedGridColumn157";
            this.bandedGridColumn157.Visible = true;
            // 
            // bandedGridColumn158
            // 
            this.bandedGridColumn158.Caption = "[0,15]";
            this.bandedGridColumn158.CustomizationCaption = "TxPower[0,15]";
            this.bandedGridColumn158.FieldName = "Txpower0T15";
            this.bandedGridColumn158.Name = "bandedGridColumn158";
            this.bandedGridColumn158.Visible = true;
            // 
            // bandedGridColumn159
            // 
            this.bandedGridColumn159.Caption = "[15,34]";
            this.bandedGridColumn159.CustomizationCaption = "TxPower[15,34]";
            this.bandedGridColumn159.FieldName = "Txpower15";
            this.bandedGridColumn159.Name = "bandedGridColumn159";
            this.bandedGridColumn159.Visible = true;
            // 
            // bandedGridColumn160
            // 
            this.bandedGridColumn160.Caption = "TOP1小区";
            this.bandedGridColumn160.CustomizationCaption = "TOP1小区";
            this.bandedGridColumn160.FieldName = "Cell1";
            this.bandedGridColumn160.Name = "bandedGridColumn160";
            this.bandedGridColumn160.Visible = true;
            this.bandedGridColumn160.Width = 85;
            // 
            // bandedGridColumn161
            // 
            this.bandedGridColumn161.Caption = "TOP2小区";
            this.bandedGridColumn161.CustomizationCaption = "TOP2小区";
            this.bandedGridColumn161.FieldName = "Cell2";
            this.bandedGridColumn161.Name = "bandedGridColumn161";
            this.bandedGridColumn161.Visible = true;
            this.bandedGridColumn161.Width = 85;
            // 
            // bandedGridColumn162
            // 
            this.bandedGridColumn162.Caption = "TOP3小区";
            this.bandedGridColumn162.CustomizationCaption = "TOP3小区";
            this.bandedGridColumn162.FieldName = "Cell3";
            this.bandedGridColumn162.Name = "bandedGridColumn162";
            this.bandedGridColumn162.Visible = true;
            this.bandedGridColumn162.Width = 85;
            // 
            // bandedGridColumn163
            // 
            this.bandedGridColumn163.Caption = "所属网格";
            this.bandedGridColumn163.CustomizationCaption = "所属网格";
            this.bandedGridColumn163.FieldName = "Strgrid";
            this.bandedGridColumn163.Name = "bandedGridColumn163";
            this.bandedGridColumn163.Visible = true;
            // 
            // bandedGridColumn164
            // 
            this.bandedGridColumn164.Caption = "所属道路";
            this.bandedGridColumn164.CustomizationCaption = "所属道路";
            this.bandedGridColumn164.FieldName = "Strroad";
            this.bandedGridColumn164.Name = "bandedGridColumn164";
            this.bandedGridColumn164.Visible = true;
            // 
            // bandedGridColumn165
            // 
            this.bandedGridColumn165.Caption = "经度";
            this.bandedGridColumn165.CustomizationCaption = "经度";
            this.bandedGridColumn165.FieldName = "Imlongitude";
            this.bandedGridColumn165.Name = "bandedGridColumn165";
            this.bandedGridColumn165.Visible = true;
            // 
            // bandedGridColumn166
            // 
            this.bandedGridColumn166.Caption = "纬度";
            this.bandedGridColumn166.CustomizationCaption = "纬度";
            this.bandedGridColumn166.FieldName = "Imlatitude";
            this.bandedGridColumn166.Name = "bandedGridColumn166";
            this.bandedGridColumn166.Visible = true;
            // 
            // bandedGridColumn167
            // 
            this.bandedGridColumn167.Caption = "ifileid";
            this.bandedGridColumn167.CustomizationCaption = "ifileid";
            this.bandedGridColumn167.FieldName = "Ifileid";
            this.bandedGridColumn167.Name = "bandedGridColumn167";
            // 
            // bandedGridColumn168
            // 
            this.bandedGridColumn168.Caption = "istime";
            this.bandedGridColumn168.CustomizationCaption = "istime";
            this.bandedGridColumn168.FieldName = "Istime";
            this.bandedGridColumn168.Name = "bandedGridColumn168";
            // 
            // bandedGridColumn169
            // 
            this.bandedGridColumn169.Caption = "ietime";
            this.bandedGridColumn169.CustomizationCaption = "ietime";
            this.bandedGridColumn169.FieldName = "Ietime";
            this.bandedGridColumn169.Name = "bandedGridColumn169";
            // 
            // bandedGridColumn170
            // 
            this.bandedGridColumn170.Caption = "gridColumn207";
            this.bandedGridColumn170.CustomizationCaption = "gridColumn207";
            this.bandedGridColumn170.FieldName = "Iid";
            this.bandedGridColumn170.Name = "bandedGridColumn170";
            // 
            // gridBand5
            // 
            this.gridBand5.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand5.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand5.Caption = "基础信息";
            this.gridBand5.Columns.Add(this.bandedGridColumn163);
            this.gridBand5.Columns.Add(this.bandedGridColumn164);
            this.gridBand5.Columns.Add(this.bandedGridColumn160);
            this.gridBand5.Columns.Add(this.bandedGridColumn161);
            this.gridBand5.Columns.Add(this.bandedGridColumn162);
            this.gridBand5.Columns.Add(this.bandedGridColumn165);
            this.gridBand5.Columns.Add(this.bandedGridColumn166);
            this.gridBand5.MinWidth = 20;
            this.gridBand5.Name = "gridBand5";
            this.gridBand5.Width = 555;
            // 
            // gridBand27
            // 
            this.gridBand27.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand27.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand27.Caption = "测试信息";
            this.gridBand27.Columns.Add(this.bandedGridColumn137);
            this.gridBand27.Columns.Add(this.bandedGridColumn138);
            this.gridBand27.Columns.Add(this.bandedGridColumn167);
            this.gridBand27.Columns.Add(this.bandedGridColumn168);
            this.gridBand27.Columns.Add(this.bandedGridColumn169);
            this.gridBand27.Columns.Add(this.bandedGridColumn170);
            this.gridBand27.MinWidth = 20;
            this.gridBand27.Name = "gridBand27";
            this.gridBand27.Width = 150;
            // 
            // gridBand28
            // 
            this.gridBand28.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand28.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand28.Caption = "电平";
            this.gridBand28.Columns.Add(this.bandedGridColumn139);
            this.gridBand28.Columns.Add(this.bandedGridColumn140);
            this.gridBand28.Columns.Add(this.bandedGridColumn141);
            this.gridBand28.Columns.Add(this.bandedGridColumn142);
            this.gridBand28.Columns.Add(this.bandedGridColumn143);
            this.gridBand28.Columns.Add(this.bandedGridColumn144);
            this.gridBand28.Columns.Add(this.bandedGridColumn145);
            this.gridBand28.MinWidth = 20;
            this.gridBand28.Name = "gridBand28";
            this.gridBand28.Width = 525;
            // 
            // gridBand29
            // 
            this.gridBand29.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand29.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand29.Caption = "C/I";
            this.gridBand29.Columns.Add(this.bandedGridColumn146);
            this.gridBand29.Columns.Add(this.bandedGridColumn147);
            this.gridBand29.Columns.Add(this.bandedGridColumn148);
            this.gridBand29.Columns.Add(this.bandedGridColumn149);
            this.gridBand29.Columns.Add(this.bandedGridColumn150);
            this.gridBand29.MinWidth = 20;
            this.gridBand29.Name = "gridBand29";
            this.gridBand29.Width = 375;
            // 
            // gridBand30
            // 
            this.gridBand30.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand30.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand30.Caption = "MOS";
            this.gridBand30.Columns.Add(this.bandedGridColumn151);
            this.gridBand30.Columns.Add(this.bandedGridColumn152);
            this.gridBand30.Columns.Add(this.bandedGridColumn153);
            this.gridBand30.Columns.Add(this.bandedGridColumn154);
            this.gridBand30.MinWidth = 20;
            this.gridBand30.Name = "gridBand30";
            this.gridBand30.Width = 300;
            // 
            // gridBand31
            // 
            this.gridBand31.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand31.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand31.Caption = "TxPower";
            this.gridBand31.Columns.Add(this.bandedGridColumn155);
            this.gridBand31.Columns.Add(this.bandedGridColumn156);
            this.gridBand31.Columns.Add(this.bandedGridColumn157);
            this.gridBand31.Columns.Add(this.bandedGridColumn158);
            this.gridBand31.Columns.Add(this.bandedGridColumn159);
            this.gridBand31.MinWidth = 20;
            this.gridBand31.Name = "gridBand31";
            this.gridBand31.Width = 375;
            // 
            // bandedGridColumn171
            // 
            this.bandedGridColumn171.Caption = "距离(米)";
            this.bandedGridColumn171.CustomizationCaption = "距离(米)";
            this.bandedGridColumn171.FieldName = "Idistance";
            this.bandedGridColumn171.Name = "bandedGridColumn171";
            this.bandedGridColumn171.Visible = true;
            // 
            // bandedGridColumn172
            // 
            this.bandedGridColumn172.Caption = "时长(秒)";
            this.bandedGridColumn172.CustomizationCaption = "时长(秒)";
            this.bandedGridColumn172.FieldName = "Iduration";
            this.bandedGridColumn172.Name = "bandedGridColumn172";
            this.bandedGridColumn172.Visible = true;
            // 
            // bandedGridColumn173
            // 
            this.bandedGridColumn173.Caption = "均值";
            this.bandedGridColumn173.CustomizationCaption = "电平均值";
            this.bandedGridColumn173.FieldName = "Rxlmean";
            this.bandedGridColumn173.Name = "bandedGridColumn173";
            this.bandedGridColumn173.Visible = true;
            // 
            // bandedGridColumn174
            // 
            this.bandedGridColumn174.Caption = "[-75,-10]";
            this.bandedGridColumn174.CustomizationCaption = "电平[-75,-10]";
            this.bandedGridColumn174.FieldName = "Rxl75";
            this.bandedGridColumn174.Name = "bandedGridColumn174";
            this.bandedGridColumn174.Visible = true;
            // 
            // bandedGridColumn175
            // 
            this.bandedGridColumn175.Caption = "[-80,-75]";
            this.bandedGridColumn175.CustomizationCaption = "电平[-80,-75]";
            this.bandedGridColumn175.FieldName = "Rxl76_80";
            this.bandedGridColumn175.Name = "bandedGridColumn175";
            this.bandedGridColumn175.Visible = true;
            // 
            // bandedGridColumn176
            // 
            this.bandedGridColumn176.Caption = "[-85,-80]";
            this.bandedGridColumn176.CustomizationCaption = "电平[-85,-80]";
            this.bandedGridColumn176.FieldName = "Rxl81_85";
            this.bandedGridColumn176.Name = "bandedGridColumn176";
            this.bandedGridColumn176.Visible = true;
            // 
            // bandedGridColumn177
            // 
            this.bandedGridColumn177.Caption = "[-90,-85]";
            this.bandedGridColumn177.CustomizationCaption = "电平[-90,-85]";
            this.bandedGridColumn177.FieldName = "Rxl86_90";
            this.bandedGridColumn177.Name = "bandedGridColumn177";
            this.bandedGridColumn177.Visible = true;
            // 
            // bandedGridColumn178
            // 
            this.bandedGridColumn178.Caption = "[-94,-90]";
            this.bandedGridColumn178.CustomizationCaption = "电平[-94,-90]";
            this.bandedGridColumn178.FieldName = "Rxl91_94";
            this.bandedGridColumn178.Name = "bandedGridColumn178";
            this.bandedGridColumn178.Visible = true;
            // 
            // bandedGridColumn179
            // 
            this.bandedGridColumn179.Caption = "[-140,-94]";
            this.bandedGridColumn179.CustomizationCaption = "电平[-140,-94]";
            this.bandedGridColumn179.FieldName = "Rxl94";
            this.bandedGridColumn179.Name = "bandedGridColumn179";
            this.bandedGridColumn179.Visible = true;
            // 
            // bandedGridColumn180
            // 
            this.bandedGridColumn180.Caption = "均值";
            this.bandedGridColumn180.CustomizationCaption = "DPCH C/I均值";
            this.bandedGridColumn180.FieldName = "Dc2imean";
            this.bandedGridColumn180.Name = "bandedGridColumn180";
            this.bandedGridColumn180.Visible = true;
            // 
            // bandedGridColumn181
            // 
            this.bandedGridColumn181.Caption = "[-20,-10]";
            this.bandedGridColumn181.CustomizationCaption = "C/I[-20,-10]";
            this.bandedGridColumn181.FieldName = "Dc2iF10";
            this.bandedGridColumn181.Name = "bandedGridColumn181";
            this.bandedGridColumn181.Visible = true;
            // 
            // bandedGridColumn182
            // 
            this.bandedGridColumn182.Caption = "[-10,-3]";
            this.bandedGridColumn182.CustomizationCaption = "C/I[-10,-3]";
            this.bandedGridColumn182.FieldName = "Dc2iF10TF3";
            this.bandedGridColumn182.Name = "bandedGridColumn182";
            this.bandedGridColumn182.Visible = true;
            // 
            // bandedGridColumn183
            // 
            this.bandedGridColumn183.Caption = "[-3,15]";
            this.bandedGridColumn183.CustomizationCaption = "C/I[-3,15]";
            this.bandedGridColumn183.FieldName = "Dc2iF3T15";
            this.bandedGridColumn183.Name = "bandedGridColumn183";
            this.bandedGridColumn183.Visible = true;
            // 
            // bandedGridColumn184
            // 
            this.bandedGridColumn184.Caption = "[15,25]";
            this.bandedGridColumn184.CustomizationCaption = "C/I[15,25]";
            this.bandedGridColumn184.FieldName = "Dc2i15";
            this.bandedGridColumn184.Name = "bandedGridColumn184";
            this.bandedGridColumn184.Visible = true;
            // 
            // bandedGridColumn185
            // 
            this.bandedGridColumn185.Caption = "均值";
            this.bandedGridColumn185.CustomizationCaption = "MOS均值";
            this.bandedGridColumn185.FieldName = "Pesqmean";
            this.bandedGridColumn185.Name = "bandedGridColumn185";
            this.bandedGridColumn185.Visible = true;
            // 
            // bandedGridColumn186
            // 
            this.bandedGridColumn186.Caption = "[0,2.8]";
            this.bandedGridColumn186.CustomizationCaption = "MOS[0,2.8]";
            this.bandedGridColumn186.FieldName = "Pesq28";
            this.bandedGridColumn186.Name = "bandedGridColumn186";
            this.bandedGridColumn186.Visible = true;
            // 
            // bandedGridColumn187
            // 
            this.bandedGridColumn187.Caption = "[2.8,3]";
            this.bandedGridColumn187.CustomizationCaption = "MOS[2.8,3]";
            this.bandedGridColumn187.FieldName = "Pesq28_30";
            this.bandedGridColumn187.Name = "bandedGridColumn187";
            this.bandedGridColumn187.Visible = true;
            // 
            // bandedGridColumn188
            // 
            this.bandedGridColumn188.Caption = "[3,5]";
            this.bandedGridColumn188.CustomizationCaption = "MOS[3,5]";
            this.bandedGridColumn188.FieldName = "Pesq30";
            this.bandedGridColumn188.Name = "bandedGridColumn188";
            this.bandedGridColumn188.Visible = true;
            // 
            // bandedGridColumn189
            // 
            this.bandedGridColumn189.Caption = "均值";
            this.bandedGridColumn189.CustomizationCaption = "TxPower均值";
            this.bandedGridColumn189.FieldName = "Txpowermean";
            this.bandedGridColumn189.Name = "bandedGridColumn189";
            this.bandedGridColumn189.Visible = true;
            // 
            // bandedGridColumn190
            // 
            this.bandedGridColumn190.Caption = "[-50,-20]";
            this.bandedGridColumn190.CustomizationCaption = "Txpower[-50,-20]";
            this.bandedGridColumn190.FieldName = "TxpowerF20";
            this.bandedGridColumn190.Name = "bandedGridColumn190";
            this.bandedGridColumn190.Visible = true;
            // 
            // bandedGridColumn191
            // 
            this.bandedGridColumn191.Caption = "[-20,0]";
            this.bandedGridColumn191.CustomizationCaption = "TxPower[-20,0]";
            this.bandedGridColumn191.FieldName = "TxpowerF20T0";
            this.bandedGridColumn191.Name = "bandedGridColumn191";
            this.bandedGridColumn191.Visible = true;
            // 
            // bandedGridColumn192
            // 
            this.bandedGridColumn192.Caption = "[0,15]";
            this.bandedGridColumn192.CustomizationCaption = "TxPower[0,15]";
            this.bandedGridColumn192.FieldName = "Txpower0T15";
            this.bandedGridColumn192.Name = "bandedGridColumn192";
            this.bandedGridColumn192.Visible = true;
            // 
            // bandedGridColumn193
            // 
            this.bandedGridColumn193.Caption = "[15,34]";
            this.bandedGridColumn193.CustomizationCaption = "TxPower[15,34]";
            this.bandedGridColumn193.FieldName = "Txpower15";
            this.bandedGridColumn193.Name = "bandedGridColumn193";
            this.bandedGridColumn193.Visible = true;
            // 
            // bandedGridColumn194
            // 
            this.bandedGridColumn194.Caption = "TOP1小区";
            this.bandedGridColumn194.CustomizationCaption = "TOP1小区";
            this.bandedGridColumn194.FieldName = "Cell1";
            this.bandedGridColumn194.Name = "bandedGridColumn194";
            this.bandedGridColumn194.Visible = true;
            this.bandedGridColumn194.Width = 85;
            // 
            // bandedGridColumn195
            // 
            this.bandedGridColumn195.Caption = "TOP2小区";
            this.bandedGridColumn195.CustomizationCaption = "TOP2小区";
            this.bandedGridColumn195.FieldName = "Cell2";
            this.bandedGridColumn195.Name = "bandedGridColumn195";
            this.bandedGridColumn195.Visible = true;
            this.bandedGridColumn195.Width = 85;
            // 
            // bandedGridColumn196
            // 
            this.bandedGridColumn196.Caption = "TOP3小区";
            this.bandedGridColumn196.CustomizationCaption = "TOP3小区";
            this.bandedGridColumn196.FieldName = "Cell3";
            this.bandedGridColumn196.Name = "bandedGridColumn196";
            this.bandedGridColumn196.Visible = true;
            this.bandedGridColumn196.Width = 85;
            // 
            // bandedGridColumn197
            // 
            this.bandedGridColumn197.Caption = "所属网格";
            this.bandedGridColumn197.CustomizationCaption = "所属网格";
            this.bandedGridColumn197.FieldName = "Strgrid";
            this.bandedGridColumn197.Name = "bandedGridColumn197";
            this.bandedGridColumn197.Visible = true;
            // 
            // bandedGridColumn198
            // 
            this.bandedGridColumn198.Caption = "所属道路";
            this.bandedGridColumn198.CustomizationCaption = "所属道路";
            this.bandedGridColumn198.FieldName = "Strroad";
            this.bandedGridColumn198.Name = "bandedGridColumn198";
            this.bandedGridColumn198.Visible = true;
            // 
            // bandedGridColumn199
            // 
            this.bandedGridColumn199.Caption = "经度";
            this.bandedGridColumn199.CustomizationCaption = "经度";
            this.bandedGridColumn199.FieldName = "Imlongitude";
            this.bandedGridColumn199.Name = "bandedGridColumn199";
            this.bandedGridColumn199.Visible = true;
            // 
            // bandedGridColumn200
            // 
            this.bandedGridColumn200.Caption = "纬度";
            this.bandedGridColumn200.CustomizationCaption = "纬度";
            this.bandedGridColumn200.FieldName = "Imlatitude";
            this.bandedGridColumn200.Name = "bandedGridColumn200";
            this.bandedGridColumn200.Visible = true;
            // 
            // bandedGridColumn201
            // 
            this.bandedGridColumn201.Caption = "ifileid";
            this.bandedGridColumn201.CustomizationCaption = "ifileid";
            this.bandedGridColumn201.FieldName = "Ifileid";
            this.bandedGridColumn201.Name = "bandedGridColumn201";
            // 
            // bandedGridColumn202
            // 
            this.bandedGridColumn202.Caption = "istime";
            this.bandedGridColumn202.CustomizationCaption = "istime";
            this.bandedGridColumn202.FieldName = "Istime";
            this.bandedGridColumn202.Name = "bandedGridColumn202";
            // 
            // bandedGridColumn203
            // 
            this.bandedGridColumn203.Caption = "ietime";
            this.bandedGridColumn203.CustomizationCaption = "ietime";
            this.bandedGridColumn203.FieldName = "Ietime";
            this.bandedGridColumn203.Name = "bandedGridColumn203";
            // 
            // bandedGridColumn204
            // 
            this.bandedGridColumn204.Caption = "gridColumn207";
            this.bandedGridColumn204.CustomizationCaption = "gridColumn207";
            this.bandedGridColumn204.FieldName = "Iid";
            this.bandedGridColumn204.Name = "bandedGridColumn204";
            // 
            // gridBand6
            // 
            this.gridBand6.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand6.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand6.Caption = "基础信息";
            this.gridBand6.Columns.Add(this.bandedGridColumn197);
            this.gridBand6.Columns.Add(this.bandedGridColumn198);
            this.gridBand6.Columns.Add(this.bandedGridColumn194);
            this.gridBand6.Columns.Add(this.bandedGridColumn195);
            this.gridBand6.Columns.Add(this.bandedGridColumn196);
            this.gridBand6.Columns.Add(this.bandedGridColumn199);
            this.gridBand6.Columns.Add(this.bandedGridColumn200);
            this.gridBand6.MinWidth = 20;
            this.gridBand6.Name = "gridBand6";
            this.gridBand6.Width = 555;
            // 
            // gridBand32
            // 
            this.gridBand32.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand32.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand32.Caption = "测试信息";
            this.gridBand32.Columns.Add(this.bandedGridColumn171);
            this.gridBand32.Columns.Add(this.bandedGridColumn172);
            this.gridBand32.Columns.Add(this.bandedGridColumn201);
            this.gridBand32.Columns.Add(this.bandedGridColumn202);
            this.gridBand32.Columns.Add(this.bandedGridColumn203);
            this.gridBand32.Columns.Add(this.bandedGridColumn204);
            this.gridBand32.MinWidth = 20;
            this.gridBand32.Name = "gridBand32";
            this.gridBand32.Width = 150;
            // 
            // gridBand33
            // 
            this.gridBand33.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand33.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand33.Caption = "电平";
            this.gridBand33.Columns.Add(this.bandedGridColumn173);
            this.gridBand33.Columns.Add(this.bandedGridColumn174);
            this.gridBand33.Columns.Add(this.bandedGridColumn175);
            this.gridBand33.Columns.Add(this.bandedGridColumn176);
            this.gridBand33.Columns.Add(this.bandedGridColumn177);
            this.gridBand33.Columns.Add(this.bandedGridColumn178);
            this.gridBand33.Columns.Add(this.bandedGridColumn179);
            this.gridBand33.MinWidth = 20;
            this.gridBand33.Name = "gridBand33";
            this.gridBand33.Width = 525;
            // 
            // gridBand34
            // 
            this.gridBand34.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand34.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand34.Caption = "C/I";
            this.gridBand34.Columns.Add(this.bandedGridColumn180);
            this.gridBand34.Columns.Add(this.bandedGridColumn181);
            this.gridBand34.Columns.Add(this.bandedGridColumn182);
            this.gridBand34.Columns.Add(this.bandedGridColumn183);
            this.gridBand34.Columns.Add(this.bandedGridColumn184);
            this.gridBand34.MinWidth = 20;
            this.gridBand34.Name = "gridBand34";
            this.gridBand34.Width = 375;
            // 
            // gridBand35
            // 
            this.gridBand35.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand35.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand35.Caption = "MOS";
            this.gridBand35.Columns.Add(this.bandedGridColumn185);
            this.gridBand35.Columns.Add(this.bandedGridColumn186);
            this.gridBand35.Columns.Add(this.bandedGridColumn187);
            this.gridBand35.Columns.Add(this.bandedGridColumn188);
            this.gridBand35.MinWidth = 20;
            this.gridBand35.Name = "gridBand35";
            this.gridBand35.Width = 300;
            // 
            // gridBand36
            // 
            this.gridBand36.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand36.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand36.Caption = "TxPower";
            this.gridBand36.Columns.Add(this.bandedGridColumn189);
            this.gridBand36.Columns.Add(this.bandedGridColumn190);
            this.gridBand36.Columns.Add(this.bandedGridColumn191);
            this.gridBand36.Columns.Add(this.bandedGridColumn192);
            this.gridBand36.Columns.Add(this.bandedGridColumn193);
            this.gridBand36.MinWidth = 20;
            this.gridBand36.Name = "gridBand36";
            this.gridBand36.Width = 375;
            // 
            // TdXtraLastWeakPoadForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(849, 417);
            this.Controls.Add(this.tableLayoutPanel1);
            this.MaximizeBox = false;
            this.Name = "TdXtraLastWeakPoadForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "TD持续差道路详细信息";
            this.Deactivate += new System.EventHandler(this.TdXtraLastWeakPoadForm_Deactivate);
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            this.tableLayoutPanel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl5)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView9)).EndInit();
            this.xtraTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            this.xtraTabPage3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).EndInit();
            this.xtraTabPage4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).EndInit();
            this.xtraTabPage5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView8)).EndInit();
            this.xtraTabPage6.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView11)).EndInit();
            this.xtraTabPage7.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView13)).EndInit();
            this.tableLayoutPanel1.ResumeLayout(false);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage3;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage4;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn39;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn40;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn44;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn45;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn46;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn47;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn48;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn49;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn50;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn51;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn109;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn110;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemDIYReplay;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn111;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn112;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn113;
        private DevExpress.XtraGrid.GridControl gridControl5;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn129;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn130;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn131;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn132;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn133;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn134;
        private DevExpress.XtraCharts.ChartControl chartControl1;
        private System.Windows.Forms.Button btnClearFly;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemClearFly;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn135;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn136;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemShowFly;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn137;
        private DevExpress.XtraGrid.GridControl gridControl2;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn52;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn53;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn54;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn55;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn56;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn57;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn58;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn59;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn138;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView4;
        private DevExpress.XtraGrid.GridControl gridControl3;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn60;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn61;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn62;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn63;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn64;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn65;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn66;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn67;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn68;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn69;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn70;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn71;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn72;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn73;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn74;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn75;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn76;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn77;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn78;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn79;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn80;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn81;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn82;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn83;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn84;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn85;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn86;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn87;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn88;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn89;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn90;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn91;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn139;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView6;
        private DevExpress.XtraGrid.GridControl gridControl4;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn92;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn93;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn94;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn95;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn96;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn97;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn98;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn99;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn100;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn101;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn102;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn103;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn104;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn105;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn106;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn107;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn108;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn114;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn115;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn116;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn117;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn118;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn119;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn120;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn121;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn122;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn123;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn124;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn125;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn126;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn127;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn128;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn140;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView8;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage6;
        private DevExpress.XtraGrid.GridControl gridControl6;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn141;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn142;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn143;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn144;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn145;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn146;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn147;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn148;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn149;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn150;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn151;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn152;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn153;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn154;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn155;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn156;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn157;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn158;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn159;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn160;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn161;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn162;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn163;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn164;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn165;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn166;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn167;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn168;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn169;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn170;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn171;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn172;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn173;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView11;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage7;
        private DevExpress.XtraGrid.GridControl gridControl7;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn174;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn175;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn176;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn177;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn178;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn179;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn180;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn181;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn182;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn183;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn184;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn185;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn186;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn187;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn188;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn189;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn190;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn191;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn192;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn193;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn194;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn195;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn196;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn197;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn198;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn199;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn200;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn201;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn202;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn203;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn204;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn205;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn206;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView13;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemToExcel;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btnNext1;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemLable;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn207;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn208;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn209;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn210;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn211;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn212;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn5;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn6;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn7;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn8;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn9;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn10;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn11;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn12;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn13;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn14;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn15;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn16;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn17;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn18;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn19;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn20;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn21;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn22;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn23;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn24;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn25;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn26;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn27;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn28;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn29;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn30;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn31;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn32;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn33;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn34;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView5;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView6;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand7;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand1;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand8;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand9;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand10;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand11;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn61;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn62;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn58;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn59;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn60;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn63;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn64;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand12;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn35;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn36;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn65;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn66;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn67;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn68;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand13;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn37;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn38;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn39;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn40;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn41;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn42;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn43;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand14;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn44;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn45;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn46;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn47;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn48;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand15;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn49;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn50;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn51;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn52;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand16;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn53;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn54;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn55;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn56;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn57;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn95;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn96;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn92;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn93;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn94;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn97;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn98;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand17;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn69;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn70;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn99;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn100;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn101;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn102;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand18;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn71;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn72;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn73;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn74;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn75;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn76;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn77;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand19;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn78;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn79;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn80;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn81;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn82;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand20;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn83;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn84;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn85;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn86;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand21;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn87;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn88;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn89;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn90;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn91;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn129;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn130;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn126;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn127;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn128;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn131;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn132;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand22;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn103;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn104;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn133;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn134;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn135;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn136;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand23;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn105;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn106;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn107;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn108;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn109;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn110;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn111;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand24;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn112;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn113;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn114;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn115;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn116;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand25;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn117;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn118;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn119;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn120;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand26;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn121;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn122;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn123;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn124;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn125;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand5;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn163;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn164;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn160;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn161;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn162;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn165;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn166;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand27;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn137;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn138;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn167;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn168;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn169;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn170;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand28;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn139;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn140;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn141;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn142;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn143;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn144;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn145;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand29;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn146;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn147;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn148;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn149;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn150;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand30;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn151;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn152;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn153;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn154;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand31;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn155;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn156;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn157;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn158;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn159;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand6;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn197;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn198;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn194;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn195;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn196;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn199;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn200;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand32;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn171;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn172;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn201;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn202;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn203;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn204;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand33;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn173;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn174;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn175;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn176;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn177;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn178;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn179;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand34;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn180;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn181;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn182;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn183;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn184;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand35;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn185;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn186;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn187;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn188;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand36;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn189;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn190;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn191;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn192;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn193;
    }
}