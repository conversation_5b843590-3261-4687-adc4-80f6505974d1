﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRNCellLevelHigherDlg : BaseDialog
    {
        public NRNCellLevelHigherDlg()
        {
            InitializeComponent();
        }

        public void SetCondtion(NRNCellLevelHigherCond cond)
        {
            numLevelDB.Value = (int)cond.RsrpDiff;
            numRate.Value = (decimal)cond.Rate;
            chkMinDistance.Checked = cond.CheckDistance;
            numMinDistance.Value = (decimal)cond.Distance;
            chkMinDuration.Checked = cond.CheckDuration;
            numMinDuration.Value = (decimal)cond.Duration;
            chkCoOnly.Checked = cond.CoFreqOnly;
        }

        public NRNCellLevelHigherCond GetCondition()
        {
            NRNCellLevelHigherCond cond = new NRNCellLevelHigherCond();
            cond.RsrpDiff = (int)numLevelDB.Value;
            cond.Rate = (double)numRate.Value;
            cond.CheckDistance = chkMinDistance.Checked;
            cond.Distance = (double)numMinDistance.Value;
            cond.CheckDuration = chkMinDuration.Checked;
            cond.Duration = (double)numMinDuration.Value;
            cond.CoFreqOnly = chkCoOnly.Checked;
            return cond;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }

    public class NRNCellLevelHigherCond
    {
        public double RsrpDiff { get; set; } = 3;
        public bool CheckDistance { get; set; } = true;
        public double Distance { get; set; } = 50;
        public bool CheckDuration { get; set; } = false;
        public double Duration { get; set; } = 10;
        public double Rate { get; set; } = 100;
        public bool CoFreqOnly { get; set; } = false;
    }
}
