﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class StationAcceptAna_HB : SingleStationAcceptBase
    {
        int snapShotId = 0;
        readonly List<LTEBTS> btsList = new List<LTEBTS>();
        readonly List<LTECell> cellList = new List<LTECell>();
        readonly List<LTEAntenna> antennaList = new List<LTEAntenna>();

        StationAcceptInfo_HB curBtsAcceptInfo = null;
        CellAcceptInfo_HB curCellInfo = null;

        private static StationAcceptAna_HB instance = null;
        public static StationAcceptAna_HB GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new StationAcceptAna_HB();
                    }
                }
            }
            return instance;
        }

        protected StationAcceptAna_HB()
            : base()
        {
#if LT
            carrierID = CarrierType.ChinaUnicom;
#endif
            this.isAnaByAcceptRules = true;
        }
        public override string Name
        {
            get
            {
#if DEBUG
                return "湖北单站验收";
#else
                 return "单站验收";
#endif
            }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 22000, 22070, "查询");
        }

        public override void DealBeforeBackgroundQueryByCity()
        {
            strWorkParamTime = "";
            if (!getWorkParams(FuncSet.IsAutoWorkParamFilePath, FuncSet.ExcelPath))
            {
                return;
            }
            if (workParamSumDic.Count <= 0)
            {
                folderPath = FuncSet.FilePath;
            }
            folderPath = FuncSet.FilePath + "\\" + strWorkParamTime;

            sumRows.Clear();
            if (!System.IO.Directory.Exists(folderPath))
            {
                System.IO.Directory.CreateDirectory(folderPath);
            }

            NPOIRow row = new NPOIRow();
            row.AddCellValue("地市");
            row.AddCellValue("基站名称");
            row.AddCellValue("ENodeBID");
            row.AddCellValue("覆盖类型");
            row.AddCellValue("是否找到文件");
            row.AddCellValue("是否通过验收");
            row.AddCellValue("工参推送时间");
            row.AddCellValue("单站报告原因");
            sumRows.Add(row);
        }

        protected override bool getCondition()
        {
            snapShotId = 0;
            FilterSampleByRegion = false;
            FilterEventByRegion = false;
            MainModel.DrawLinesPntToCells = true;
            curBtsAcceptInfo = new StationAcceptInfo_HB();
            curDistrictName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);

            if (workParamSumDic.Count <= 0 || !workParamSumDic.ContainsKey(curDistrictName.Replace("市", "")))
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo("未读取到" + curDistrictName + "的待验收小区工参");
                return false;
            }

            StationFilesCountManager.AddBtsFileCountToDBAuto();
            MainModel.MainForm.GetMapForm().updateMap();

            return true;
        }

        protected override void queryInThread(ClientProxy clientProxy, Dictionary<int, Dictionary<int, CellWorkParam>> curDistrictWorkParamDic)
        {
            foreach (var btsValuePair in curDistrictWorkParamDic)
            {
                btsList.Clear();
                cellList.Clear();
                antennaList.Clear();
                LTEBTS bts = new LTEBTS();

                curBtsAcceptInfo = new StationAcceptInfo_HB();
                Dictionary<int, CellWorkParam> cellParamDic = btsValuePair.Value;
                if (cellParamDic != null && cellParamDic.Count > 0)
                {
                    List<CellWorkParam> cellParamList = new List<CellWorkParam>(cellParamDic.Values);

                    CellWorkParam firstCellParam = cellParamList[0];
                    #region 设置基站工参
                    curBtsAcceptInfo.FileNameEnodType = FuncSet.FileNameEnodType;
                    curBtsAcceptInfo.CellParamInfo = new CellWorkParam();
                    curBtsAcceptInfo.CellParamInfo.IsOutDoor = firstCellParam.IsOutDoor;
                    curBtsAcceptInfo.CellParamInfo.BtsName = firstCellParam.BtsName;
                    curBtsAcceptInfo.CellParamInfo.ENodeBID = firstCellParam.ENodeBID;
                    curBtsAcceptInfo.CellParamInfo.Tac = firstCellParam.Tac;
                    curBtsAcceptInfo.CellParamInfo.ARFCN = firstCellParam.ARFCN;
                    curBtsAcceptInfo.CellParamInfo.Latitude = firstCellParam.Latitude;
                    curBtsAcceptInfo.CellParamInfo.Longitude = firstCellParam.Longitude;
                    curBtsAcceptInfo.CellParamInfo.AcceptDateTime = firstCellParam.AcceptDateTime;
                    #endregion

                    bool isStop = dealResult(clientProxy, bts, cellParamList, firstCellParam);
                    if (isStop)
                    {
                        return;
                    }
                }
            }
        }

        private bool dealResult(ClientProxy clientProxy, LTEBTS bts, List<CellWorkParam> cellParamList, CellWorkParam firstCellParam)
        {
            string fileCountStatausDesc = "";
            int fileCountStatus = getFileCountStataus(curBtsAcceptInfo.CellParamInfo.ENodeBID, ref fileCountStatausDesc);
            if (fileCountStatus == 11)
            {
                curBtsAcceptInfo.HasFoundFile = true;
                curBtsAcceptInfo.IsPassAccept = true;
                addToSumInfo(curBtsAcceptInfo, "已是");
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo(curBtsAcceptInfo.CellParamInfo.BtsName + "：  " + fileCountStatausDesc);
            }
            else if (FuncSet.FileCountAutoIsCheck && fileCountStatus != 1)
            {
                curBtsAcceptInfo.StrErrorInfo = fileCountStatausDesc;
                addToSumInfo(curBtsAcceptInfo, "");
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo(curBtsAcceptInfo.CellParamInfo.BtsName + "：  " + fileCountStatausDesc);

                saveNoAcceptResultInfoToDB(curBtsAcceptInfo, cellParamList);
            }
            else
            {
                List<LTECell> oldCells = MainModel.CellManager.GetLTECellsByECI(firstCellParam.Eci);
                removeCell(oldCells);
                foreach (CellWorkParam cellParam in cellParamList)//先添加小区工参到CellManager
                {
                    addCellInfoToCellManager(ref snapShotId, cellParam, ref bts);
                }

                foreach (CellWorkParam cellParam in cellParamList)//统计本站各单个小区的测试信息
                {
                    curCellInfo = new CellAcceptInfo_HB(cellParam, FuncSet.FileNameEnodType);
                    curBtsAcceptInfo.CellAcceptInfoDic[cellParam.CellId] = curCellInfo;
                    doBackgroundStatByFile(clientProxy);
                    if (MainModel.BackgroundStopRequest)
                    {
                        return true;
                    }
                }

                if (curBtsAcceptInfo.CellParamInfo.IsOutDoor)//室外站需再统计本站的环测信息
                {
                    curCellInfo = new CellAcceptInfo_HB(curBtsAcceptInfo.CellParamInfo, FuncSet.FileNameEnodType);
                    curCellInfo.IsCircleTest = true;
                    curCellInfo.CellParamInfo.CellName = curBtsAcceptInfo.CellParamInfo.BtsName + "—环测";
                    curBtsAcceptInfo.CellCircleTestInfo = curCellInfo;
                    doBackgroundStatByFile(clientProxy);
                }

                if (!MainModel.CellManager.GetCurrentLTEBTSs().Contains(bts))
                {
                    btsList.Add(bts);
                    MainModel.CellManager.Add(bts);
                }

                saveStationResultAfterAna(curBtsAcceptInfo);
                reCoverCells(oldCells);
            }

            return false;
        }

        protected override void doSomethingAfterQueryThread()
        {
            curBtsAcceptInfo = null;
            curCellInfo = null;
        }
        private void removeCell(List<LTECell> cells)
        {
            if (cells != null)
            {
                cells = new List<LTECell>(cells);
                foreach (LTECell oldCell in cells)
                {
                    MainModel.CellManager.Remove(oldCell.BelongBTS);
                    foreach (LTECell lteCell in oldCell.BelongBTS.Cells)
                    {
                        MainModel.CellManager.Remove(lteCell);
                        foreach (LTEAntenna antenna in lteCell.Antennas)
                        {
                            MainModel.CellManager.Remove(antenna);
                        }
                    }
                }
            }
        }
        private void reCoverCells(List<LTECell> cells)
        {
            if (cells != null)
            {
                List<LTEBTS> lteBtsList = MainModel.CellManager.GetCurrentLTEBTSs();
                cells = new List<LTECell>(cells);
                foreach (LTECell oldCell in cells)
                {
                    foreach (LTECell lteCell in oldCell.BelongBTS.Cells)
                    {
                        MainModel.CellManager.Add(lteCell);
                        foreach (LTEAntenna antenna in lteCell.Antennas)
                        {
                            MainModel.CellManager.Add(antenna);
                        }
                    }

                    if (!lteBtsList.Contains(oldCell.BelongBTS))
                    {
                        MainModel.CellManager.Add(oldCell.BelongBTS);
                    }
                }
            }
        }
        private void addCellInfoToCellManager(ref int snapShotId, CellWorkParam cellParam, ref LTEBTS bts)
        {
            #region 暂时动态添加工参到CellManager，稍后移除
            snapShotId--;

            bts.Fill(snapShotId, 0, 2147483647);
            bts.Name = cellParam.BtsName;
            bts.BTSID = cellParam.ENodeBID;
            bts.Longitude = cellParam.Longitude;
            bts.Latitude = cellParam.Latitude;
            bts.Type = cellParam.IsOutDoor ? LTEBTSType.Outdoor : LTEBTSType.Indoor;

            LTECell cell = new LTECell();
            cell.Fill(snapShotId, 0, 2147483647);
            cell.BelongBTS = bts;
            cell.Name = cellParam.CellName;
            cell.TAC = cellParam.Tac;
            cell.ECI = cellParam.Eci;
            cell.CellID = cellParam.CellId;
            cell.SectorID = cellParam.SectorID;
            cell.PCI = cellParam.PCI;
            cell.EARFCN = cellParam.ARFCN;
            cell.SCellID = (cell.ECI / 256) * 10 + cell.SectorID;
            bts.AddCell(cell);
            cellList.Add(cell);
            MainModel.CellManager.Add(cell);

            LTEAntenna antenna = new LTEAntenna();
            antenna.Fill(snapShotId, 0, 2147483647);
            antenna.Cell = cell;
            antenna.Longitude = cellParam.Longitude;
            antenna.Latitude = cellParam.Latitude;
            antenna.Direction = (short)cellParam.Direction;
            antennaList.Add(antenna);
            MainModel.CellManager.Add(antenna);
            #endregion
        }

        private int getFileCountStataus(int enodeBid, ref string fileCountStatausDesc)
        {
            StationFilesCountQuery fileCountQuery = new StationFilesCountQuery(mainModel);
            fileCountQuery.SetCondition(FuncSet.DiyStatsRecentDays, enodeBid, ServiceTypeString, "");
            fileCountQuery.Query();
            fileCountStatausDesc = fileCountQuery.FileCountStatausDesc;

            return fileCountQuery.FileCountStataus;
        }
        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    ServiceType serviceType = (ServiceType)fileDataManager.ServiceType;
                    bool isLteDataOrIdle = (serviceType == ServiceType.LTE_TDD_DATA
                        || serviceType == ServiceType.LTE_TDD_IDLE
                        || serviceType == ServiceType.LTE_FDD_DATA
                        || serviceType == ServiceType.LTE_FDD_IDLE);

                    if (serviceType.ToString().Contains("FDD"))
                    {
                        curBtsAcceptInfo.IsFddBts = true;
                    }

                    bool isValidDl = fileDataManager.FileName.ToUpper().Contains("DL") && isLteDataOrIdle;
                    bool isValidUl = fileDataManager.FileName.ToUpper().Contains("UL") && isLteDataOrIdle;
                        
                    if (curCellInfo.CellParamInfo.IsOutDoor)
                    {
                        doStatWithOutDoorCell(curCellInfo, fileDataManager, isValidDl, isValidUl);
                    }
                    else
                    {
                        doStatWithInDoorCell(curCellInfo, fileDataManager, isValidDl, isValidUl);
                    }

                }
            }
            catch (Exception ee)
            {
                writeLog(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }
        protected void doStatWithOutDoorCell(CellAcceptInfo_HB currentCellInfo, DTFileDataManager fileDataManager
            , bool isValidDl, bool isValidUl)
        {
            currentCellInfo.TestTimeList.Add(JavaDate.GetDateTimeFromMilliseconds(Condition.FileInfos[0].BeginTime * 1000L));
            currentCellInfo.HasFoundFile = true;

            if (!currentCellInfo.IsCircleTest || fileDataManager.FileName.Contains("好点") || fileDataManager.FileName.Contains("中点")
                || fileDataManager.FileName.Contains("差点"))//小区单测文件和 有环测补点的文件 只统计占用小区的覆盖指标
            {
                bool hasFoundValidPositionTp = false;
                bool hasFoundValidPCI = false;
                bool hasFoundValidTAC = false;
                bool hasFoundValidENodeBID = false;
                bool hasFoundValidEARFCN = false;

                foreach (TestPoint tp in fileDataManager.TestPoints)
                {
                    checkTpHadFoundValidInfo(tp, currentCellInfo.CellParamInfo, ref hasFoundValidPCI
                   , ref hasFoundValidTAC, ref hasFoundValidENodeBID, ref hasFoundValidEARFCN);

                    bool isValidPositionTp = false;
                    if (tp.Longitude > 0 && tp.Latitude > 0)
                    {
                        isValidPositionTp = true;
                        hasFoundValidPositionTp = true;
                    }

                    LTECell cell = GetMainLteCell(tp);
                    CellAcceptInfo_HB srcCellInfo;
                    if (cell != null && curBtsAcceptInfo.CellAcceptInfoDic.TryGetValue(cell.CellID, out srcCellInfo))
                    {
                        srcCellInfo.AddTp(tp, isValidPositionTp, isValidDl, isValidUl);
                    }
                }

                getFileHadFoundValidInfo(currentCellInfo, fileDataManager.FileName, hasFoundValidPositionTp
                    , hasFoundValidPCI, hasFoundValidTAC, hasFoundValidENodeBID, hasFoundValidEARFCN);
            }
            else
            {
                doStatWithOutDoorCircleTest(currentCellInfo, fileDataManager, isValidDl, isValidUl);
            }
        }
        protected void doStatWithOutDoorCircleTest(CellAcceptInfo_HB currentCellInfo, DTFileDataManager fileDataManager
            , bool isValidDl, bool isValidUl)
        {
            currentCellInfo.AddEvents(isValidDl, fileDataManager.Events, fileDataManager.ServiceType);

            bool isDl = fileDataManager.FileName.ToUpper().Contains("DL") 
                || fileDataManager.FileName.Contains("下载");

            bool isUl = fileDataManager.FileName.ToUpper().Contains("UL") 
                || fileDataManager.FileName.Contains("上传");

            bool hasFoundValidPositionTp = false;
            bool hasFoundValidPCI = false;
            bool hasFoundValidTAC = false;
            bool hasFoundValidENodeBID = false;
            bool hasFoundValidEARFCN = false;
            foreach (TestPoint tp in fileDataManager.TestPoints)
            {
                int? tpPci = tp.GetBSIC();
                int? tpEARFCN = tp.GetBCCH();

                foreach (CellAcceptInfo_HB cellInfo in curBtsAcceptInfo.CellAcceptInfoDic.Values)
                {
                    judgeHasFound(ref hasFoundValidPCI, cellInfo.CellParamInfo.PCI, tpPci);
                    judgeHasFound(ref hasFoundValidEARFCN, cellInfo.CellParamInfo.ARFCN, tpEARFCN);

                    if (hasFoundValidPCI && hasFoundValidEARFCN)
                    {
                        break;
                    }
                }

                judgeHasFound(ref hasFoundValidTAC, currentCellInfo.CellParamInfo.Tac, tp.GetLAC());
                judgeHasFound(ref hasFoundValidENodeBID, currentCellInfo.CellParamInfo.ENodeBID, getTpENodeBID(tp));

                bool isValidPositionTp = false;
                if (tp.Longitude > 0 && tp.Latitude > 0)
                {
                    isValidPositionTp = true;
                    hasFoundValidPositionTp = true;
                }
                currentCellInfo.AddCircleCallTp(tp, isValidPositionTp);

                setSpeed(currentCellInfo, isDl, isUl, tp, isValidPositionTp);

                setSrcCellInfo(isValidDl, isValidUl, tp, isValidPositionTp);
            }

            getFileHadFoundValidInfo(currentCellInfo, fileDataManager.FileName, hasFoundValidPositionTp
                    , hasFoundValidPCI, hasFoundValidTAC, hasFoundValidENodeBID, hasFoundValidEARFCN);
        }

        private void setSpeed(CellAcceptInfo_HB currentCellInfo, bool isDl, bool isUl, TestPoint tp, bool isValidPositionTp)
        {
            if (isUl)
            {
                currentCellInfo.ULTpList.Add(tp);
                if (isValidPositionTp)
                {
                    currentCellInfo.HasFoundValidPositionUlTp = true;
                }
            }
            if (isDl)
            {
                currentCellInfo.DLTpList.Add(tp);
                if (isValidPositionTp)
                {
                    currentCellInfo.HasFoundValidPositionDlTp = true;
                }
            }
        }

        private void setSrcCellInfo(bool isValidDl, bool isValidUl, TestPoint tp, bool isValidPositionTp)
        {
            CellAcceptInfo_HB srcCellInfo = null;
            if (isValidPositionTp)
            {
                LTECell cell = GetMainLteCell(tp);
                if (cell != null && curBtsAcceptInfo.CellAcceptInfoDic.TryGetValue(cell.CellID, out srcCellInfo))
                {
                    srcCellInfo.AddTp(tp, false, isValidDl, isValidUl);
                    srcCellInfo.AddCircleKpiInfo(tp, isValidDl, isValidUl);

                    srcCellInfo.TpCountSrcSum++;
                    double tpCellAngle = srcCellInfo.GetTp_CellAngle(tp);
                    if (tpCellAngle > 90)
                    {
                        srcCellInfo.TpCountAntennaOpposite++;
                    }
                    if (!srcCellInfo.IsCircleTestHasValidTp)
                    {
                        srcCellInfo.IsCircleTestHasValidTp = tpCellAngle < 60;
                    }
                }
            }
        }

        protected void doStatWithInDoorCell(CellAcceptInfo_HB currentCellInfo, DTFileDataManager fileDataManager
            , bool isValidDl, bool isValidUl)
        {
            bool isValidFile = false;
            string strEnode_CellId = string.Format("{0}_{1}", currentCellInfo.StrENodeBID, currentCellInfo.CellParamInfo.CellId);

            if (fileDataManager.FileName.ToUpper().Contains(strEnode_CellId + "_QH")
                || fileDataManager.FileName.ToUpper().Contains(strEnode_CellId + "_HO")
                || fileDataManager.FileName.ToUpper().Contains(strEnode_CellId + "_切换"))
            {
                isValidFile = true;
                currentCellInfo.AddAndRecordHandOverEvents(fileDataManager.Events);
            }
            else if (fileDataManager.FileName.ToUpper().Contains(strEnode_CellId + "_WX")
                || fileDataManager.FileName.ToUpper().Contains(strEnode_CellId + "_外泄"))
            {
                isValidFile = true;
                doStatWithInDoorLeakOutLog(currentCellInfo, fileDataManager);
            }
            else
            {
                currentCellInfo.AddEvents(false, fileDataManager.Events, fileDataManager.ServiceType);

                int keyIndex = -1;
                string fileNameKey = strEnode_CellId + "_";
                keyIndex = fileDataManager.FileName.IndexOf(fileNameKey);
                keyIndex += fileNameKey.Length;

                string testPosition = "";
                if (getTestPosition(keyIndex, fileDataManager.FileName, ref testPosition))
                {
                    isValidFile = true;

                    doStatTPInfo(currentCellInfo, fileDataManager, isValidDl, isValidUl, testPosition);
                }
            }

            if (isValidFile)
            {
                currentCellInfo.TestTimeList.Add(JavaDate.GetDateTimeFromMilliseconds(Condition.FileInfos[0].BeginTime * 1000L));
                currentCellInfo.HasFoundFile = true;
            }
            else
            {
                currentCellInfo.ErrorInfoList.Add(string.Format("{0} LOG命名错误", fileDataManager.FileName));
            }
        }

        private void doStatTPInfo(CellAcceptInfo_HB currentCellInfo, DTFileDataManager fileDataManager, bool isValidDl, bool isValidUl, string testPosition)
        {
            AcceptInfoBase_HB testPositionInfo;
            if (!curCellInfo.TestPositionAcceptInfoDic.TryGetValue(testPosition, out testPositionInfo))
            {
                testPositionInfo = new AcceptInfoBase_HB();
                testPositionInfo.TestPosition = testPosition;
                curCellInfo.TestPositionAcceptInfoDic.Add(testPosition, testPositionInfo);
            }

            bool hasFoundValidPCI = false;
            bool hasFoundValidTAC = false;
            bool hasFoundValidENodeBID = false;
            bool hasFoundValidEARFCN = false;
            foreach (TestPoint tp in fileDataManager.TestPoints)
            {
                int? tpPci = tp.GetBSIC();
                int? tpTac = tp.GetLAC();
                int? tpENodeBid = getTpENodeBID(tp);
                int? tpEarfcn = tp.GetBCCH();
                judgeHasFound(ref hasFoundValidPCI, currentCellInfo.CellParamInfo.PCI, tpPci);
                judgeHasFound(ref hasFoundValidTAC, currentCellInfo.CellParamInfo.Tac, tpTac);
                judgeHasFound(ref hasFoundValidENodeBID, currentCellInfo.CellParamInfo.ENodeBID, tpENodeBid);
                judgeHasFound(ref hasFoundValidEARFCN, currentCellInfo.CellParamInfo.ARFCN, tpEarfcn);

                bool isValidPositionTp = false;
                if (tp.Longitude > 0 && tp.Latitude > 0)
                {
                    isValidPositionTp = true;
                }

                currentCellInfo.AddTp(tp, isValidPositionTp, isValidDl, isValidUl);

                testPositionInfo.AddTp(tp, isValidPositionTp, isValidDl, isValidUl);
                testPositionInfo.AddSrcCellBaseInfo(tpPci, tpTac, tpENodeBid, tpEarfcn);
            }
            getFileHadFoundValidInfo(currentCellInfo, fileDataManager.FileName, true
              , hasFoundValidPCI, hasFoundValidTAC, hasFoundValidENodeBID, hasFoundValidEARFCN);
        }

        private void judgeHasFound(ref bool hasFoundValid, int  data, int? tpData)
        {
            if (!hasFoundValid && data == tpData)
            {
                hasFoundValid = true;
            }
        }

        protected void doStatWithInDoorLeakOutLog(CellAcceptInfo_HB currentCellInfo, DTFileDataManager fileDataManager)
        {
            currentCellInfo.RatioInfoWx.HasFoundValidFile = true;

            bool hasFoundValidPCI = false;
            bool hasFoundValidTAC = false;
            bool hasFoundValidENodeBID = false;
            bool hasFoundValidEARFCN = false;
            foreach (TestPoint tp in fileDataManager.TestPoints)
            {
                checkTpHadFoundValidInfo(tp, currentCellInfo.CellParamInfo, ref hasFoundValidPCI
                    , ref hasFoundValidTAC, ref hasFoundValidENodeBID, ref hasFoundValidEARFCN);

                LTECell cell = GetMainLteCell(tp);
                float? rsrp = tp.GetRxlev();

                if (cell != null && cell.Name == currentCellInfo.CellParamInfo.CellName)
                {
                    currentCellInfo.RatioInfoWx.CountDenominator++;
                }
                else if (rsrp != null)
                {
                    addRatioInfoWxCount(currentCellInfo, tp, rsrp);
                }
            }
            getFileHadFoundValidInfo(currentCellInfo, fileDataManager.FileName, true
                  , hasFoundValidPCI, hasFoundValidTAC, hasFoundValidENodeBID, hasFoundValidEARFCN);
        }

        private void addRatioInfoWxCount(CellAcceptInfo_HB currentCellInfo, TestPoint tp, float? rsrp)
        {
            for (int i = 0; i < 10; i++)
            {
                float? nRsrp = tp.GetNbRxlev(i);
                if (nRsrp == null)
                {
                    break;
                }

                LTECell nCell;
                if (tp is LTEFddTestPoint)
                {
                    nCell = tp.GetNBCell_LTE_FDD(i);
                }
                else
                {
                    nCell = tp.GetNBCell_LTE(i);
                }
                if (nCell != null && nCell.Name == currentCellInfo.CellParamInfo.CellName)
                {
                    currentCellInfo.RatioInfoWx.CountDenominator++;
                    if (rsrp - nRsrp > 10)
                    {
                        currentCellInfo.RatioInfoWx.CountNumerator++;
                    }
                }
            }
        }

        private bool getTestPosition(int startIndex, string fileName, ref string testPosition)
        {
            testPosition = "";
            if (startIndex > 0)
            {
                char[] strArray = fileName.ToCharArray(startIndex, fileName.Length - startIndex);
                StringBuilder sb = new StringBuilder();
                foreach (char ch in strArray)
                {
                    if (ch == '_')
                    {
                        testPosition = sb.ToString().ToUpper();
                        if (testPosition == "WX" || testPosition == "HO" || testPosition == "CSFB" || testPosition == "VOLTE"
                            || testPosition == "单测")
                        {
                            return false;
                        }
                        return !string.IsNullOrEmpty(testPosition);
                    }
                    else
                    {
                        sb.Append(ch);
                    }
                }
            }
            return false;
        }
        private int? getTpENodeBID(TestPoint tp)
        {
            LTECell lteCell = GetMainLteCell(tp);
            if (lteCell != null)
            {
                return lteCell.BelongBTS.BTSID;
            }
            return null;
        }
        protected LTECell GetMainLteCell(TestPoint tp)
        {
            LTECell cell = null;
            if (tp is LTETestPointDetail || tp is LTEFddTestPoint)
            {
                cell = CellManager.GetInstance().GetNearestLTECellByTACCI(tp.DateTime, tp.GetLAC(), tp.GetCI()
                    , tp.Longitude, tp.Latitude);

                int? earfcn = tp.GetBCCH();
                int? pci = tp.GetBSIC();

                if (cell == null)
                {
                    cell = getLTECellByEARFCNPCI(tp.DateTime, earfcn, pci, tp.Longitude, tp.Latitude);
                }
                else if (earfcn != null && pci != null && (cell.EARFCN != (int)earfcn || cell.PCI != (int)pci))
                {
                    cell = getLTECellByEARFCNPCI(tp.DateTime, earfcn, pci, tp.Longitude, tp.Latitude);
                }
            }
            return cell;
        }
        private LTECell getLTECellByEARFCNPCI(DateTime time, int? earfcn, int? pci, double longitude, double latitude)
        {
            if (earfcn == null || pci == null)
            {
                return null;
            }
            foreach (LTECell cell in cellList)
            {
                if (longitude > 0 && latitude > 0 && CellManager.GetInstance().SystemConfigInfo.distLimit
                    && cell.GetDistance(longitude, latitude) > CD.MAX_COV_DISTANCE_LTE)//距离限制设置
                {
                    continue;
                }
                if (cell.EARFCN == earfcn && cell.PCI == pci)
                {
                    return cell;
                }
            }
            return CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(time, earfcn, pci, longitude, latitude);
        }

        private void checkTpHadFoundValidInfo(TestPoint tp, CellWorkParam cellParamInfo
            , ref bool hasFoundValidPCI, ref bool hasFoundValidTAC, ref bool hasFoundValidENodeBID, ref bool hasFoundValidEARFCN)
        {
            if (!hasFoundValidPCI && cellParamInfo.PCI == tp.GetBSIC())
            {
                hasFoundValidPCI = true;
            }
            if (!hasFoundValidTAC && cellParamInfo.Tac == tp.GetLAC())
            {
                hasFoundValidTAC = true;
            }
            if (!hasFoundValidENodeBID && cellParamInfo.ENodeBID == getTpENodeBID(tp))
            {
                hasFoundValidENodeBID = true;
            }
            if (!hasFoundValidEARFCN && cellParamInfo.ARFCN == tp.GetBCCH())
            {
                hasFoundValidEARFCN = true;
            }
        }
        private void getFileHadFoundValidInfo(CellAcceptInfo_HB currentCellInfo, string fileName, bool hasFoundValidPositionTp
            , bool hasFoundValidPCI, bool hasFoundValidTAC, bool hasFoundValidENodeBID, bool hasFoundValidEARFCN)
        {
            if (!hasFoundValidPositionTp)
            {
                currentCellInfo.ErrorInfoList.Add(string.Format("{0} LOG不存在GPS点", fileName));
            }

            StringBuilder strb = new StringBuilder();
            if (!hasFoundValidENodeBID)
            {
                strb.Append("ENodeBID、");
            }
            if (!hasFoundValidTAC)
            {
                strb.Append("TAC、");
            }
            if (!hasFoundValidEARFCN)
            {
                strb.Append("ARFCN、");
            }
            if (!hasFoundValidPCI)
            {
                strb.Append("PCI、");
            }
            if (strb.Length > 1)
            {
                currentCellInfo.ErrorInfoList.Add(string.Format("{0} LOG的{1}与该站点工参不匹配", fileName, strb.Remove(strb.Length - 1, 1)));
            }
        }

        protected void saveStationResultAfterAna(StationAcceptInfo_HB btsAcceptInfo)
        {
            try
            {
                checkInValidFileInfos(btsAcceptInfo);
                btsAcceptInfo.JudgeAcceptInfo(FuncSet);
                addToSumInfo(btsAcceptInfo);

                string strFileName = "";
                if (btsAcceptInfo.HasFoundFile)
                {
                    string reportSavePath = FuncSet.FilePath + Path.DirectorySeparatorChar + btsAcceptInfo.CellParamInfo.AcceptDateTime.ToString("yyyyMMdd");
                    if (!System.IO.Directory.Exists(reportSavePath))
                    {
                        System.IO.Directory.CreateDirectory(reportSavePath);
                    }
                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo("正在导出" + btsAcceptInfo.CellParamInfo.BtsName + "的单验报告...");
                    if (btsAcceptInfo.CellParamInfo.IsOutDoor)
                    {
                        strFileName = ExportAcceptReportManager.ExportOutDoorReport(btsAcceptInfo, cellList, reportSavePath);
                    }
                    else
                    {
                        strFileName = ExportAcceptReportManager.ExportInDoorReport(btsAcceptInfo, cellList, reportSavePath);
                    }
                }
                saveAcceptResultInfoToDB(btsAcceptInfo, strFileName);

                MainModel.CellManager.Remove(btsList);
                MainModel.CellManager.Remove(cellList);
                MainModel.CellManager.Remove(antennaList);
            }
            catch (Exception ee)
            {
                writeLog(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }
        private void checkInValidFileInfos(StationAcceptInfo_HB curBtsAcceptInfo)
        {
            BackgroundFuncQueryManager.GetInstance().GetFile_SingleStation(GetSubFuncID(), ServiceTypeString, curBtsAcceptInfo.StrENodeBID
                , -1, "", FuncSet.DiyStatsRecentDays);

            foreach (MasterCom.RAMS.Model.FileInfo file in MainModel.FileInfos)
            {
                bool isValid = false;
                foreach (CellAcceptInfo_HB cellInfo in curBtsAcceptInfo.CellAcceptInfoDic.Values)
                {
                    if (file.Name.Contains(string.Format("{0}_{1}", cellInfo.StrENodeBID, cellInfo.CellParamInfo.CellId)))
                    {
                        isValid = true;
                        break;
                    }
                }
                if (!isValid && !file.Name.ToUpper().Contains(string.Format("{0}_UL", curBtsAcceptInfo.StrENodeBID))
                    && !file.Name.ToUpper().Contains(string.Format("{0}_DL", curBtsAcceptInfo.StrENodeBID))
                    && !file.Name.ToUpper().Contains(string.Format("{0}_CSFB", curBtsAcceptInfo.StrENodeBID))
                    && !file.Name.ToUpper().Contains(string.Format("{0}_VOLTE", curBtsAcceptInfo.StrENodeBID)) 
                    && !file.Name.Contains(string.Format("{0}_环测", curBtsAcceptInfo.StrENodeBID)))
                {
                    curBtsAcceptInfo.ErrorInfoList.Add(string.Format("{0} LOG命名错误", file.Name));
                }
            }
            MainModel.FileInfos.Clear();
        }
        private void saveNoAcceptResultInfoToDB(StationAcceptInfo_HB btsAcceptInfo, List<CellWorkParam> cellParamList)
        {
            if (cellParamList == null || btsAcceptInfo == null)
            {
                return;
            }
            foreach (CellWorkParam cellParam in cellParamList)
            {
                btsAcceptInfo.CellAcceptInfoDic[cellParam.CellId] = new CellAcceptInfo_HB(cellParam, FuncSet.FileNameEnodType);
            }
            saveAcceptResultInfoToDB(btsAcceptInfo, "");
        }
        private void saveAcceptResultInfoToDB(StationAcceptInfo_HB btsAcceptInfo, string strBtsReportFileName)
        {
            bool hasFoundCircleFile = false;
            if (btsAcceptInfo.CellCircleTestInfo != null)
            {
                hasFoundCircleFile = btsAcceptInfo.CellCircleTestInfo.HasFoundFile;
            }
            foreach (var cellAcceptInfo in btsAcceptInfo.CellAcceptInfoDic)
            {
                CellAcceptInfo_HB cellInfo = cellAcceptInfo.Value;
                BackgroundFuncQueryManager.GetInstance().SaveResult_SingleStation(cellInfo, hasFoundCircleFile
                    , btsAcceptInfo.IsPassAccept, btsAcceptInfo.StrErrorInfo, strBtsReportFileName);
            }
        }
        private void addToSumInfo(StationAcceptInfo_HB btsAcceptInfo)
        {
            string hasFoundFile = btsAcceptInfo.HasFoundFile ? "是" : "否";
            addToSumInfo(btsAcceptInfo, hasFoundFile);
        }
        private void addToSumInfo(StationAcceptInfo_HB btsAcceptInfo, string hasFoundFile)
        {
            NPOIRow row = new NPOIRow();
            row.AddCellValue(curDistrictName);
            row.AddCellValue(btsAcceptInfo.CellParamInfo.BtsName);
            row.AddCellValue(btsAcceptInfo.CellParamInfo.ENodeBID);
            row.AddCellValue(btsAcceptInfo.CellParamInfo.CoverType);
            row.AddCellValue(hasFoundFile);
            row.AddCellValue(btsAcceptInfo.IsPassAccept ? "是" : "否");
            row.AddCellValue(btsAcceptInfo.CellParamInfo.AcceptDateTime);
            row.AddCellValue(btsAcceptInfo.StrErrorInfo);
            sumRows.Add(row);
        }
        public override void DealAfterBackgroundQueryByCity()
        {
            if (!string.IsNullOrEmpty(folderPath))
            {
                string filePath = folderPath + "\\单站验收结果汇总.xls";
                ExcelNPOIManager.ExportToExcel(sumRows, filePath, "验收结果");

                try
                {
                    if (File.Exists(filePath))
                    {
                        string filePathNew = folderPath + "\\单站验收结果汇总" + DateTime.Now.ToString("HHmmss") + ".xls";
                        File.Copy(filePath, filePathNew, true);
                    }
                }
                catch
                {
                    //continue
                }
            }
            
            MainModel.MainForm.GetMapForm().updateMap();

            sumRows.Clear();
            workParamSumDic.Clear();
        }

        #region Background
        protected override void getFilesForAnalyse()
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo("开始分析" + curCellInfo.CellParamInfo.CellName + "的文件");
            BackgroundFuncQueryManager.GetInstance().GetFile_SingleStation(GetSubFuncID(), ServiceTypeString, curCellInfo.StrENodeBID
                , curCellInfo.CellParamInfo.CellId, "", FuncSet.DiyStatsRecentDays);
        }
        #endregion
    }
}
