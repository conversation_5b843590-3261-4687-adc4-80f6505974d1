﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    class LteURLAnalyzerByFile : LteURLAnalyzer
    {
        protected List<LteURLModel> results = null;
        protected Dictionary<string, List<Event>> dicBro = null;
        protected Dictionary<string, List<Event>> dicDown = null;
        protected Dictionary<string, List<Event>> dicVideo = null;

        public void Analyze(List<DTFileDataManager> fileManagers, Dictionary<int, string> dic)
        {
            results = new List<LteURLModel>();
            foreach (DTFileDataManager dtFile in fileManagers)
            {
                Dictionary<int, string> dicName = dic;
                Analyze(dtFile, dicName);
            }
            SetSN();
        }

        protected void Analyze(DTFileDataManager dtFile, Dictionary<int, string> dic)
        {
            LteURLModel dtlModel = new LteURLModel(dtFile);
            if (dic.ContainsKey(dtFile.FileID))
            {
                dtlModel.DistrictName = dic[dtFile.FileID];
            }
            DTFileDataManager f = dtFile;
            dicBro = new Dictionary<string, List<Event>>();
            dicDown = new Dictionary<string, List<Event>>();
            dicVideo = new Dictionary<string, List<Event>>();
            AnaToDictionary(f);
            List<LteURLBroURL> dtlBros = HttpAnalyze(dicBro);
            dtlModel.Bros = dtlBros;

            List<LteURLDowURL> dtlDowns = DownAnalyze(dicDown);
            dtlModel.Downs = dtlDowns;

            List<LteURLVideoURL> dtlVideos = VideoAnalyze(dicVideo);
            dtlModel.Videos = dtlVideos;
            results.Add(dtlModel);
        }

        protected void AnaToDictionary(DTFileDataManager dtFile)
        {
            string broURL = "";
            string downURL = "";
            string videoURL = "";
            foreach (Event evt in dtFile.Events)
            {
                if (IsInHttpID(evt.ID))
                {
                    broURL = addValidData(dtFile, broURL, evt, dicBro, (int)LteURLCheckMsg.HttpRequestEventID, (int)LteURLCheckMsg.HttpRequestEventID_FDD);
                }
                else if (IsInDownloadID(evt.ID))
                {
                    downURL = addValidData(dtFile, downURL, evt, dicDown, (int)LteURLCheckMsg.DownRequestEventID, (int)LteURLCheckMsg.DownRequestEventID_FDD);
                }
                else if (IsInVideoID(evt.ID))
                {
                    videoURL = addValidData(dtFile, videoURL, evt, dicVideo, (int)LteURLCheckMsg.VideoRequestEventID, (int)LteURLCheckMsg.VideoRequestEventID_FDD);
                }
            }
        }

        private string addValidData(DTFileDataManager dtFile, string url, Event evt, Dictionary<string, List<Event>> dic,
            int tddRequestEventID, int fddRequestEventID)
        {
            if (evt.ID == tddRequestEventID || evt.ID == fddRequestEventID)
            {
                url = GetURL(evt.SN, dtFile.Messages);
                if (dic.ContainsKey(url))
                {
                    dic[url].Add(evt);
                }
                else
                {
                    List<Event> listTemp = new List<Event>();
                    listTemp.Add(evt);
                    dic.Add(url, listTemp);
                }
            }
            else
            {
                if (dic.ContainsKey(url))
                {
                    dic[url].Add(evt);
                }
            }

            return url;
        }

        protected void SetSN()
        {
            int fileSN = 0;
            foreach (LteURLModel logFile in results)
            {
                logFile.SN = ++fileSN;

                int BroSN = 0;
                foreach (LteURLBroURL logBro in logFile.Bros)
                {
                    logBro.SN = ++BroSN;
                    setEvtSN(logBro.Events);
                }

                int DowSN = 0;
                foreach (LteURLDowURL logDow in logFile.Downs)
                {
                    logDow.SN = ++DowSN;
                    setEvtSN(logDow.Events);
                }
                int VideoSN = 0;
                foreach (LteURLVideoURL logVideo in logFile.Videos)
                {
                    logVideo.SN = ++VideoSN;
                    setEvtSN(logVideo.Events);
                }
            }
        }

        private void setEvtSN(List<LteURLEvent> logEvts)
        {
            int evtSN = 0;
            foreach (LteURLEvent logEvt in logEvts)
            {
                logEvt.SN = ++evtSN;
            }
        }

        public object GetResult()
        {
            object ret = results;
            results = null;
            return ret;
        }
    }
}
