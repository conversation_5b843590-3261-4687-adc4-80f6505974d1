﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    class NRPingPangFile
    {
        public NRPingPangFile(DTFileDataManager fileManager)
        {
            this.Cells = new List<NRPingPangICell>();
            this.Pairs = new List<NRPingPangPair>();
            this.FileName = fileManager.FileName;
        }

        public int SN { get; set; }
        public string FileName { get; private set; }
        public int PairCount { get { return Pairs.Count; } } 
        public int CellCount { get { return Cells.Count; } }
        public List<NRPingPangPair> Pairs { get; private set; }
        public List<NRPingPangICell> Cells { get; private set; }

        public void CalcResult()
        {
            Dictionary<string, NRPingPangICell> tokenDic = new Dictionary<string, NRPingPangICell>();
            foreach (NRPingPangPair pair in Pairs)
            {
                foreach (NRPingPangEvent evt in pair.Events)
                {
                    ICell iSrcCell = evt.SrcCell;
                    if (!tokenDic.ContainsKey(iSrcCell.Token))
                    {
                        tokenDic.Add(iSrcCell.Token, new NRPingPangICell(iSrcCell));
                    }
                    ++tokenDic[iSrcCell.Token].Count;

                    ICell iTarCell = evt.TarCell;
                    if (!tokenDic.ContainsKey(iTarCell.Token))
                    {
                        tokenDic.Add(iTarCell.Token, new NRPingPangICell(iTarCell));
                    }
                    ++tokenDic[iTarCell.Token].Count;
                }
            }

            this.Cells.Clear();
            this.Cells.AddRange(tokenDic.Values);
        }
    }

    class NRPingPangPair
    {
        public NRPingPangPair(NRPingPangEvent evtA, NRPingPangEvent evtB)
        {
            this.Events = new List<NRPingPangEvent>() { evtA, evtB };
            this.Desc = string.Format("[{0}] -> [{1}] -> [{0}]", evtA.SrcCell.Name, evtA.TarCell.Name);
            this.Interval = Math.Round((evtB.Time - evtA.Time).TotalSeconds, 2).ToString();
            Type = evtA.Type;
        }

        public int SN { get; set; }
        public string Desc { get; private set; }
        public string Interval { get; private set; }
        public List<NRPingPangEvent> Events { get; private set; }
        public string Type { get; private set; }
    }

    class NRPingPangEvent
    {
        public NRPingPangEvent(DTFileDataManager fileManager, NRPingPangAnalyzer.EventCellInfo evtCellInfo)
        {
            Evt = evtCellInfo.Evt;
            Time = evtCellInfo.Evt.DateTime;
            TimeString = evtCellInfo.Evt.DateTime.ToString("HH:mm:ss.fff");
            Type = NREventHelper.HandoverHelper.GetHandoverTypeDesc(evtCellInfo.Type);

            SetCells(evtCellInfo);

            if (NREventHelper.HandoverHelper.JudgeIncludeLTEHandover(evtCellInfo.Type))
            {
                SetKpis(evtCellInfo.Evt, fileManager, NRTpHelper.NrLteTpManager);
            }
            else if(NREventHelper.HandoverHelper.JudgeIncludeNRHandover(evtCellInfo.Type))
            {
                SetKpis(evtCellInfo.Evt, fileManager, NRTpHelper.NrTpManager);
            }
        }

        public string Type { get; private set; }
        public int SN { get; set; }
        public ICell SrcCell { get; private set; }
        public ICell TarCell { get; private set; }
        public string SrcCellName { get { return SrcCell.Name; } }
        public string TarCellName { get { return TarCell.Name; } }
        public Event Evt { get; private set; }
        public DateTime Time { get; private set; }
        public string TimeString { get; private set; }
        public double? BeforeRsrp { get; private set; }
        public double? AfterRsrp { get; private set; }
        public double? BeforeSinr { get; private set; }
        public double? AfterSinr { get; private set; }

        private void SetCells(NRPingPangAnalyzer.EventCellInfo evtCellInfo)
        {
            if (NREventHelper.HandoverHelper.JudgeIncludeLTEHandover(evtCellInfo.Type))
            {
                SrcCell = evtCellInfo.CellInfo.LTESrcCell.Cell;
                TarCell = evtCellInfo.CellInfo.LTETarCell.Cell;
                if (SrcCell == null)
                {
                    SrcCell = new UnknowCell(evtCellInfo.CellInfo.LTESrcCell.ARFCN, evtCellInfo.CellInfo.LTESrcCell.PCI);
                }
                if (TarCell == null)
                {
                    TarCell = new UnknowCell(evtCellInfo.CellInfo.LTETarCell.ARFCN, evtCellInfo.CellInfo.LTETarCell.PCI);
                }
            }
            else if (NREventHelper.HandoverHelper.JudgeIncludeNRHandover(evtCellInfo.Type))
            {
                SrcCell = evtCellInfo.CellInfo.NRSrcCell.Cell;
                TarCell = evtCellInfo.CellInfo.NRTarCell.Cell;
                if (SrcCell == null)
                {
                    SrcCell = new UnknowCell(evtCellInfo.CellInfo.NRSrcCell.ARFCN, evtCellInfo.CellInfo.NRSrcCell.PCI);
                }
                if (TarCell == null)
                {
                    TarCell = new UnknowCell(evtCellInfo.CellInfo.NRTarCell.ARFCN, evtCellInfo.CellInfo.NRTarCell.PCI);
                }
            }
        }

        private void SetKpis(Event evt, DTFileDataManager fileManager, NRTpManagerBase nrCond)
        {
            int tpCnt = 1;
            int preIdx = BinSearchPreSN(fileManager.TestPoints, evt.SN);

            double? rsrp = null, sinr = null;
            if (preIdx >= 0) // before exists
            {
                int startIdx = preIdx - tpCnt + 1;
                if (startIdx < 0)
                {
                    startIdx = 0;
                }

                GetKpis(fileManager.TestPoints.GetRange(startIdx , tpCnt), out rsrp, out sinr, nrCond);
                this.BeforeRsrp = rsrp;
                this.BeforeSinr = sinr;
            }
            
            if (preIdx + 1 <= fileManager.TestPoints.Count - 1) // after exists
            {
                GetKpis(fileManager.TestPoints.GetRange(preIdx + 1, tpCnt), out rsrp, out sinr, nrCond);
                this.AfterRsrp = rsrp;
                this.AfterSinr = sinr;
            }
        }

        private void GetKpis(List<TestPoint> tpList, out double? rsrp, out double? sinr, NRTpManagerBase nrCond)
        {
            int rsrpCnt = 0, sinrCnt = 0;
            double rsrpSum = 0, sinrSum = 0;
            foreach (TestPoint tp in tpList)
            {
                double? tmpRsrp = nrCond.GetSCellRsrp(tp);
                if (tmpRsrp != null)
                {
                    rsrpSum += (double)tmpRsrp;
                    ++rsrpCnt;
                }

                double? tmpSinr = nrCond.GetSCellSinr(tp);
                if (tmpSinr != null)
                {
                    sinrSum += (double)tmpSinr;
                    ++sinrCnt;
                }
            }

            rsrp = sinr = null;
            if (rsrpCnt != 0)
            {
                rsrp = Math.Round(rsrpSum / rsrpCnt, 2);
            }
            if (sinrCnt != 0)
            {
                sinr = Math.Round(sinrSum / sinrCnt, 2);
            }
        }

        /// <summary>
        /// 查找事件前一个采样点的索引下标
        /// 当采样点不存在时返回小于0的值
        /// </summary>
        /// <param name="tpList"></param>
        /// <param name="evtSn"></param>
        /// <returns></returns>
        private int BinSearchPreSN(List<TestPoint> tpList, int evtSn)
        {
            int left = 0, right = tpList.Count - 1;
            while (left < right)
            {
                int mid = (left + right) / 2;
                if (tpList[mid].SN > evtSn)
                {
                    right = mid - 1;
                }
                else if (tpList[mid].SN < evtSn)
                {
                    left = mid + 1;
                }
                else // ==
                {
                    throw (new Exception("发现采样点SN跟事件SN相等！"));
                }
            }

            // left == right
            // when tpList.Count == 0, left > right, then let exception throw
            return tpList[left].SN > evtSn ? left - 1 : left;
        }
    }

    class NRPingPangICell
    {
        public NRPingPangICell(ICell iCell)
        {
            if (iCell == null)
            {
                throw (new Exception("小区为空"));
            }
            else if (iCell is LTECell)
            {
                EARFCN = (iCell as LTECell).EARFCN;
                PCI = (iCell as LTECell).PCI;
                TAC = (iCell as LTECell).TAC;
                NCI = (iCell as LTECell).ECI;
            }
            else if (iCell is NRCell)
            {
                EARFCN = (iCell as NRCell).SSBARFCN;
                PCI = (iCell as NRCell).PCI;
                TAC = (iCell as NRCell).TAC;
                NCI = (iCell as NRCell).NCI;
            }
            else if (iCell is UnknowCell)
            {
                TAC = (iCell as UnknowCell).LAC;
                NCI = (iCell as UnknowCell).CI;
            }

            Name = iCell.Name;
            ICell = iCell;
        }

        public int SN { get; set; }
        public int Count { get; set; }
        public ICell ICell { get; private set; }
        public string Name { get; private set; }
        public int EARFCN { get; private set; }
        public int PCI { get; private set; }
        public int TAC { get; private set; }
        public long NCI { get; private set; }
    }
}
