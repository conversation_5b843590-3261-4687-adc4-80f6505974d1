using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.IO;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.Grid;
using MasterCom.MControls;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System.Collections;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class WeakC_IRoadForm : MinCloseForm
    {
        List<WeakC_IRoad> weakC_IRoadList;
        public WeakC_IRoadForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            mapForm = mainModel.MainForm.GetMapForm();
            edtDistanceMin.Value = 100;
            edtDistanceMax.Value = 10000;
            edtTimeMin.Value = 0;
            edtTimeMax.Value = 10000;
            DisposeWhenClose = true;
        }
        private MapForm mapForm = null;

        public void FillData(List<WeakC_IRoad> weakC_IRoadList, float distanceMin)
        {
            this.weakC_IRoadList = weakC_IRoadList;
            edtDistanceMin.Value = (decimal)distanceMin;
            if (edtDistanceMax.Value <= edtDistanceMin.Value)
            {
                edtDistanceMax.Value = 10000;
            }
            filterData();
        }

        private void filterData()
        {
            List<WeakC_IRoad> noCoverRoadList = new List<WeakC_IRoad>();
            MainModel.ClearDTData();
            foreach (WeakC_IRoad item in weakC_IRoadList)
            {
                if (item.Distance > (double)edtDistanceMin.Value && item.Distance < (double)edtDistanceMax.Value &&
                    item.Second > edtTimeMin.Value && item.Second < edtTimeMax.Value)
                {
                    foreach (TestPoint tp in item.TestPoints)
                    {
                        MainModel.DTDataManager.Add(tp);
                    }
                    noCoverRoadList.Add(item);
                }
            }
            BindingSource source = new BindingSource();
            source.DataSource = noCoverRoadList;
            gridControl.DataSource = source;
            gridControl.RefreshDataSource();
            MainModel.FireDTDataChanged(this);
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView);
        }

        private void gridControl_DoubleClick(object sender, EventArgs e)
        {
            if (gridView.SelectedRowsCount > 0)
            {
                WeakC_IRoad info = gridView.GetRow(gridView.GetSelectedRows()[0]) as WeakC_IRoad;
                MainModel.ClearDTData();
                foreach (TestPoint tp in info.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                string serialInfoName = "GSM_SCAN_RxLev";
                if (info.TestPoints[0] is ScanTestPoint_TD)
                {
                    serialInfoName = "TDSCAN_PCCPCH_C/I";
                }
                foreach (MapSerialInfo serialInfo in mModel.MainForm.GetMapForm().GetDTLayer().SerialInfos)
                {
                    if (serialInfo.Name.Equals(serialInfoName))
                    {
                        mModel.MainForm.GetMapForm().GetDTLayer().CurFlyLinesSerialInfo = serialInfo;
                    }
                }
                mModel.DrawFlyLines = true;
                MainModel.FireDTDataChanged(this);
            }
        }

        private void btnFilter_Click(object sender, EventArgs e)
        {
            filterData();
        }
    }
}