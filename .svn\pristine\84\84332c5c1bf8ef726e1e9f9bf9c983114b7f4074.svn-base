﻿namespace MasterCom.RAMS.ZTFunc.KPISheet_HaiNan
{
    partial class CustomReportForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.groupControl4 = new DevExpress.XtraEditors.GroupControl();
            this.btnRemoveTemplate = new DevExpress.XtraEditors.SimpleButton();
            this.gridCtrlTmpl = new DevExpress.XtraGrid.GridControl();
            this.gvTmpl = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.btnSave = new DevExpress.XtraEditors.SimpleButton();
            this.btnNewReport = new DevExpress.XtraEditors.SimpleButton();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.gridCtrlCol = new DevExpress.XtraGrid.GridControl();
            this.gvCol = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.btnDown = new DevExpress.XtraEditors.SimpleButton();
            this.btnUp = new DevExpress.XtraEditors.SimpleButton();
            this.grpCol = new System.Windows.Forms.GroupBox();
            this.btnApply = new DevExpress.XtraEditors.SimpleButton();
            this.chkFrozen = new System.Windows.Forms.CheckBox();
            this.txtCaption = new System.Windows.Forms.TextBox();
            this.grpCell = new System.Windows.Forms.GroupBox();
            this.rngColorPnl = new MasterCom.RAMS.CQT.ScoreRangeColorSettingPanel();
            this.numRngMax = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.colorStatic = new DevExpress.XtraEditors.ColorEdit();
            this.rBtnDynamic = new System.Windows.Forms.RadioButton();
            this.numRngMin = new System.Windows.Forms.NumericUpDown();
            this.rBtnStaticBk = new System.Windows.Forms.RadioButton();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.colorColBk = new DevExpress.XtraEditors.ColorEdit();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.treeList = new DevExpress.XtraTreeList.TreeList();
            this.treeListColumn1 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).BeginInit();
            this.groupControl4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlTmpl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvTmpl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlCol)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvCol)).BeginInit();
            this.grpCol.SuspendLayout();
            this.grpCell.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRngMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorStatic.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRngMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorColBk.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeList)).BeginInit();
            this.SuspendLayout();
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Default;
            this.splitContainerControl1.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl1.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.groupControl4);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.AutoScroll = true;
            this.splitContainerControl1.Panel2.Controls.Add(this.groupControl3);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1184, 662);
            this.splitContainerControl1.SplitterPosition = 283;
            this.splitContainerControl1.TabIndex = 11;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // groupControl4
            // 
            this.groupControl4.Controls.Add(this.btnRemoveTemplate);
            this.groupControl4.Controls.Add(this.gridCtrlTmpl);
            this.groupControl4.Controls.Add(this.btnSave);
            this.groupControl4.Controls.Add(this.btnNewReport);
            this.groupControl4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl4.Location = new System.Drawing.Point(0, 0);
            this.groupControl4.Name = "groupControl4";
            this.groupControl4.Size = new System.Drawing.Size(283, 658);
            this.groupControl4.TabIndex = 0;
            this.groupControl4.Text = "模板列表";
            // 
            // btnRemoveTemplate
            // 
            this.btnRemoveTemplate.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRemoveTemplate.Location = new System.Drawing.Point(98, 621);
            this.btnRemoveTemplate.Name = "btnRemoveTemplate";
            this.btnRemoveTemplate.Size = new System.Drawing.Size(87, 27);
            this.btnRemoveTemplate.TabIndex = 3;
            this.btnRemoveTemplate.Text = "删除";
            this.btnRemoveTemplate.Click += new System.EventHandler(this.btnRemoveTemplate_Click);
            // 
            // gridCtrlTmpl
            // 
            this.gridCtrlTmpl.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.gridCtrlTmpl.Location = new System.Drawing.Point(3, 26);
            this.gridCtrlTmpl.MainView = this.gvTmpl;
            this.gridCtrlTmpl.Name = "gridCtrlTmpl";
            this.gridCtrlTmpl.ShowOnlyPredefinedDetails = true;
            this.gridCtrlTmpl.Size = new System.Drawing.Size(275, 589);
            this.gridCtrlTmpl.TabIndex = 1;
            this.gridCtrlTmpl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvTmpl});
            // 
            // gvTmpl
            // 
            this.gvTmpl.Appearance.FocusedCell.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvTmpl.Appearance.FocusedCell.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvTmpl.Appearance.FocusedCell.BorderColor = System.Drawing.Color.Black;
            this.gvTmpl.Appearance.FocusedCell.Options.UseBackColor = true;
            this.gvTmpl.Appearance.FocusedCell.Options.UseBorderColor = true;
            this.gvTmpl.Appearance.FocusedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvTmpl.Appearance.FocusedRow.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvTmpl.Appearance.FocusedRow.BorderColor = System.Drawing.Color.Black;
            this.gvTmpl.Appearance.FocusedRow.Options.UseBackColor = true;
            this.gvTmpl.Appearance.FocusedRow.Options.UseBorderColor = true;
            this.gvTmpl.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1});
            this.gvTmpl.GridControl = this.gridCtrlTmpl;
            this.gvTmpl.Name = "gvTmpl";
            this.gvTmpl.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gvTmpl.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gvTmpl.OptionsDetail.ShowDetailTabs = false;
            this.gvTmpl.OptionsView.EnableAppearanceEvenRow = true;
            this.gvTmpl.OptionsView.ShowDetailButtons = false;
            this.gvTmpl.OptionsView.ShowGroupPanel = false;
            this.gvTmpl.OptionsView.ShowIndicator = false;
            this.gvTmpl.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.gvTmpl_FocusedRowChanged);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "名称";
            this.gridColumn1.FieldName = "Name";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // btnSave
            // 
            this.btnSave.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSave.Location = new System.Drawing.Point(191, 621);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(87, 27);
            this.btnSave.TabIndex = 0;
            this.btnSave.Text = "保存";
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // btnNewReport
            // 
            this.btnNewReport.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnNewReport.Location = new System.Drawing.Point(6, 621);
            this.btnNewReport.Name = "btnNewReport";
            this.btnNewReport.Size = new System.Drawing.Size(87, 27);
            this.btnNewReport.TabIndex = 2;
            this.btnNewReport.Text = "新建";
            this.btnNewReport.Click += new System.EventHandler(this.btnNewReport_Click);
            // 
            // groupControl3
            // 
            this.groupControl3.Controls.Add(this.groupControl2);
            this.groupControl3.Controls.Add(this.groupControl1);
            this.groupControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl3.Enabled = false;
            this.groupControl3.Location = new System.Drawing.Point(0, 0);
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(893, 658);
            this.groupControl3.TabIndex = 0;
            this.groupControl3.Text = "模板样式";
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.gridCtrlCol);
            this.groupControl2.Controls.Add(this.btnDown);
            this.groupControl2.Controls.Add(this.btnUp);
            this.groupControl2.Controls.Add(this.grpCol);
            this.groupControl2.Location = new System.Drawing.Point(357, 26);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(531, 629);
            this.groupControl2.TabIndex = 14;
            this.groupControl2.Text = "报表列";
            // 
            // gridCtrlCol
            // 
            this.gridCtrlCol.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.gridCtrlCol.Location = new System.Drawing.Point(5, 26);
            this.gridCtrlCol.MainView = this.gvCol;
            this.gridCtrlCol.Name = "gridCtrlCol";
            this.gridCtrlCol.ShowOnlyPredefinedDetails = true;
            this.gridCtrlCol.Size = new System.Drawing.Size(483, 309);
            this.gridCtrlCol.TabIndex = 15;
            this.gridCtrlCol.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvCol});
            // 
            // gvCol
            // 
            this.gvCol.Appearance.FocusedCell.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvCol.Appearance.FocusedCell.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvCol.Appearance.FocusedCell.BorderColor = System.Drawing.Color.Black;
            this.gvCol.Appearance.FocusedCell.Options.UseBackColor = true;
            this.gvCol.Appearance.FocusedCell.Options.UseBorderColor = true;
            this.gvCol.Appearance.FocusedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvCol.Appearance.FocusedRow.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvCol.Appearance.FocusedRow.BorderColor = System.Drawing.Color.Black;
            this.gvCol.Appearance.FocusedRow.Options.UseBackColor = true;
            this.gvCol.Appearance.FocusedRow.Options.UseBorderColor = true;
            this.gvCol.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn2});
            this.gvCol.GridControl = this.gridCtrlCol;
            this.gvCol.Name = "gvCol";
            this.gvCol.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gvCol.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gvCol.OptionsBehavior.Editable = false;
            this.gvCol.OptionsDetail.ShowDetailTabs = false;
            this.gvCol.OptionsView.EnableAppearanceEvenRow = true;
            this.gvCol.OptionsView.ShowColumnHeaders = false;
            this.gvCol.OptionsView.ShowDetailButtons = false;
            this.gvCol.OptionsView.ShowGroupPanel = false;
            this.gvCol.OptionsView.ShowIndicator = false;
            this.gvCol.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.gvCol_FocusedRowChanged);
            this.gvCol.DoubleClick += new System.EventHandler(this.gvCol_DoubleClick);
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "名称";
            this.gridColumn2.FieldName = "FullName";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 0;
            // 
            // btnDown
            // 
            this.btnDown.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold);
            this.btnDown.Appearance.Options.UseFont = true;
            this.btnDown.Location = new System.Drawing.Point(494, 181);
            this.btnDown.Name = "btnDown";
            this.btnDown.Size = new System.Drawing.Size(32, 23);
            this.btnDown.TabIndex = 14;
            this.btnDown.Text = "↓";
            this.btnDown.Click += new System.EventHandler(this.btnDown_Click);
            // 
            // btnUp
            // 
            this.btnUp.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold);
            this.btnUp.Appearance.Options.UseFont = true;
            this.btnUp.Location = new System.Drawing.Point(494, 152);
            this.btnUp.Name = "btnUp";
            this.btnUp.Size = new System.Drawing.Size(32, 23);
            this.btnUp.TabIndex = 14;
            this.btnUp.Text = "↑";
            this.btnUp.Click += new System.EventHandler(this.btnUp_Click);
            // 
            // grpCol
            // 
            this.grpCol.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.grpCol.Controls.Add(this.btnApply);
            this.grpCol.Controls.Add(this.chkFrozen);
            this.grpCol.Controls.Add(this.txtCaption);
            this.grpCol.Controls.Add(this.grpCell);
            this.grpCol.Controls.Add(this.label2);
            this.grpCol.Controls.Add(this.label1);
            this.grpCol.Controls.Add(this.colorColBk);
            this.grpCol.Location = new System.Drawing.Point(5, 341);
            this.grpCol.Name = "grpCol";
            this.grpCol.Size = new System.Drawing.Size(521, 286);
            this.grpCol.TabIndex = 13;
            this.grpCol.TabStop = false;
            this.grpCol.Text = "列属性";
            // 
            // btnApply
            // 
            this.btnApply.Location = new System.Drawing.Point(440, 252);
            this.btnApply.Name = "btnApply";
            this.btnApply.Size = new System.Drawing.Size(75, 23);
            this.btnApply.TabIndex = 15;
            this.btnApply.Text = "应用";
            this.btnApply.Click += new System.EventHandler(this.btnApply_Click);
            // 
            // chkFrozen
            // 
            this.chkFrozen.AutoSize = true;
            this.chkFrozen.Location = new System.Drawing.Point(315, 24);
            this.chkFrozen.Name = "chkFrozen";
            this.chkFrozen.Size = new System.Drawing.Size(62, 18);
            this.chkFrozen.TabIndex = 14;
            this.chkFrozen.Text = "冻结列";
            this.chkFrozen.UseVisualStyleBackColor = true;
            // 
            // txtCaption
            // 
            this.txtCaption.Location = new System.Drawing.Point(69, 20);
            this.txtCaption.Name = "txtCaption";
            this.txtCaption.Size = new System.Drawing.Size(220, 22);
            this.txtCaption.TabIndex = 2;
            // 
            // grpCell
            // 
            this.grpCell.Controls.Add(this.rngColorPnl);
            this.grpCell.Controls.Add(this.numRngMax);
            this.grpCell.Controls.Add(this.label3);
            this.grpCell.Controls.Add(this.colorStatic);
            this.grpCell.Controls.Add(this.rBtnDynamic);
            this.grpCell.Controls.Add(this.numRngMin);
            this.grpCell.Controls.Add(this.rBtnStaticBk);
            this.grpCell.Location = new System.Drawing.Point(6, 48);
            this.grpCell.Name = "grpCell";
            this.grpCell.Size = new System.Drawing.Size(428, 233);
            this.grpCell.TabIndex = 13;
            this.grpCell.TabStop = false;
            this.grpCell.Text = "指标单元格";
            // 
            // rngColorPnl
            // 
            this.rngColorPnl.DescColumnsVisible = true;
            this.rngColorPnl.Location = new System.Drawing.Point(6, 92);
            this.rngColorPnl.Name = "rngColorPnl";
            this.rngColorPnl.Size = new System.Drawing.Size(416, 135);
            this.rngColorPnl.TabIndex = 5;
            // 
            // numRngMax
            // 
            this.numRngMax.Location = new System.Drawing.Point(213, 16);
            this.numRngMax.Name = "numRngMax";
            this.numRngMax.Size = new System.Drawing.Size(70, 22);
            this.numRngMax.TabIndex = 4;
            this.numRngMax.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(143, 20);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(61, 14);
            this.label3.TabIndex = 0;
            this.label3.Text = "≤指标值≤";
            // 
            // colorStatic
            // 
            this.colorStatic.EditValue = System.Drawing.Color.Transparent;
            this.colorStatic.Location = new System.Drawing.Point(63, 44);
            this.colorStatic.Name = "colorStatic";
            this.colorStatic.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorStatic.Size = new System.Drawing.Size(46, 21);
            this.colorStatic.TabIndex = 1;
            // 
            // rBtnDynamic
            // 
            this.rBtnDynamic.AutoSize = true;
            this.rBtnDynamic.Location = new System.Drawing.Point(10, 68);
            this.rBtnDynamic.Name = "rBtnDynamic";
            this.rBtnDynamic.Size = new System.Drawing.Size(49, 18);
            this.rBtnDynamic.TabIndex = 2;
            this.rBtnDynamic.Text = "动态";
            this.rBtnDynamic.UseVisualStyleBackColor = true;
            // 
            // numRngMin
            // 
            this.numRngMin.Location = new System.Drawing.Point(63, 16);
            this.numRngMin.Name = "numRngMin";
            this.numRngMin.Size = new System.Drawing.Size(70, 22);
            this.numRngMin.TabIndex = 3;
            // 
            // rBtnStaticBk
            // 
            this.rBtnStaticBk.AutoSize = true;
            this.rBtnStaticBk.Checked = true;
            this.rBtnStaticBk.Location = new System.Drawing.Point(10, 44);
            this.rBtnStaticBk.Name = "rBtnStaticBk";
            this.rBtnStaticBk.Size = new System.Drawing.Size(49, 18);
            this.rBtnStaticBk.TabIndex = 0;
            this.rBtnStaticBk.TabStop = true;
            this.rBtnStaticBk.Text = "静态";
            this.rBtnStaticBk.UseVisualStyleBackColor = true;
            this.rBtnStaticBk.CheckedChanged += new System.EventHandler(this.rBtnStaticBk_CheckedChanged);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(396, 25);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(67, 14);
            this.label2.TabIndex = 0;
            this.label2.Text = "背景颜色：";
            this.label2.Visible = false;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(22, 24);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(43, 14);
            this.label1.TabIndex = 0;
            this.label1.Text = "列名：";
            // 
            // colorColBk
            // 
            this.colorColBk.EditValue = System.Drawing.Color.White;
            this.colorColBk.Location = new System.Drawing.Point(469, 21);
            this.colorColBk.Name = "colorColBk";
            this.colorColBk.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorColBk.Size = new System.Drawing.Size(46, 21);
            this.colorColBk.TabIndex = 1;
            this.colorColBk.Visible = false;
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.treeList);
            this.groupControl1.Location = new System.Drawing.Point(5, 26);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(346, 629);
            this.groupControl1.TabIndex = 14;
            this.groupControl1.Text = "可选列";
            // 
            // treeList
            // 
            this.treeList.Appearance.FocusedCell.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.treeList.Appearance.FocusedCell.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.treeList.Appearance.FocusedCell.Options.UseBackColor = true;
            this.treeList.Appearance.FocusedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.treeList.Appearance.FocusedRow.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(128)))));
            this.treeList.Appearance.FocusedRow.Options.UseBackColor = true;
            this.treeList.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[] {
            this.treeListColumn1});
            this.treeList.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeList.Location = new System.Drawing.Point(2, 23);
            this.treeList.Name = "treeList";
            this.treeList.OptionsBehavior.AllowIndeterminateCheckState = true;
            this.treeList.OptionsBehavior.Editable = false;
            this.treeList.OptionsView.EnableAppearanceEvenRow = true;
            this.treeList.OptionsView.ShowCheckBoxes = true;
            this.treeList.OptionsView.ShowColumns = false;
            this.treeList.Size = new System.Drawing.Size(342, 604);
            this.treeList.TabIndex = 13;
            // 
            // treeListColumn1
            // 
            this.treeListColumn1.Caption = "treeListColumn1";
            this.treeListColumn1.FieldName = "treeListColumn1";
            this.treeListColumn1.Name = "treeListColumn1";
            this.treeListColumn1.Visible = true;
            this.treeListColumn1.VisibleIndex = 0;
            // 
            // CustomReportForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1184, 662);
            this.Controls.Add(this.splitContainerControl1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.Name = "CustomReportForm";
            this.Text = "报表定制";
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).EndInit();
            this.groupControl4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlTmpl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvTmpl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlCol)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvCol)).EndInit();
            this.grpCol.ResumeLayout(false);
            this.grpCol.PerformLayout();
            this.grpCell.ResumeLayout(false);
            this.grpCell.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRngMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorStatic.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRngMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorColBk.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.treeList)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.GroupControl groupControl4;
        private DevExpress.XtraEditors.SimpleButton btnRemoveTemplate;
        private DevExpress.XtraGrid.GridControl gridCtrlTmpl;
        private DevExpress.XtraGrid.Views.Grid.GridView gvTmpl;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraEditors.SimpleButton btnSave;
        private DevExpress.XtraEditors.SimpleButton btnNewReport;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraTreeList.TreeList treeList;
        private System.Windows.Forms.GroupBox grpCell;
        private System.Windows.Forms.NumericUpDown numRngMax;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.NumericUpDown numRngMin;
        private CQT.ScoreRangeColorSettingPanel rngColorPnl;
        private DevExpress.XtraEditors.ColorEdit colorStatic;
        private System.Windows.Forms.RadioButton rBtnDynamic;
        private System.Windows.Forms.RadioButton rBtnStaticBk;
        private DevExpress.XtraEditors.SimpleButton btnDown;
        private DevExpress.XtraEditors.SimpleButton btnUp;
        private System.Windows.Forms.GroupBox grpCol;
        private System.Windows.Forms.TextBox txtCaption;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.ColorEdit colorColBk;
        private System.Windows.Forms.CheckBox chkFrozen;
        private DevExpress.XtraGrid.GridControl gridCtrlCol;
        private DevExpress.XtraGrid.Views.Grid.GridView gvCol;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraTreeList.Columns.TreeListColumn treeListColumn1;
        private DevExpress.XtraEditors.SimpleButton btnApply;
    }
}