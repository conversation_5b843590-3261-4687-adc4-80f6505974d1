﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class CityRoadMapSetDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.groupControl4 = new DevExpress.XtraEditors.GroupControl();
            this.listBoxAllSettings = new System.Windows.Forms.ListBox();
            this.btnSaveSetting = new DevExpress.XtraEditors.SimpleButton();
            this.btnRemoveSetting = new DevExpress.XtraEditors.SimpleButton();
            this.btnNewSetting = new DevExpress.XtraEditors.SimpleButton();
            this.groupCtrGrid = new DevExpress.XtraEditors.GroupControl();
            this.cbxGridColumn = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.edtGridMap = new DevExpress.XtraEditors.ButtonEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.txtMapsName = new DevExpress.XtraEditors.TextEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.groupCtrRoad = new DevExpress.XtraEditors.GroupControl();
            this.cbxRoadIdColumn = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.listBoxRoadMap = new System.Windows.Forms.ListBox();
            this.btnRoadMapSelectNone = new DevExpress.XtraEditors.SimpleButton();
            this.btnRoadMapAdd = new DevExpress.XtraEditors.SimpleButton();
            this.cbxRoadNameColumn = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.edtRoadMap = new DevExpress.XtraEditors.ButtonEdit();
            this.btnRoadMapDelete = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).BeginInit();
            this.groupControl4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupCtrGrid)).BeginInit();
            this.groupCtrGrid.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxGridColumn.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtGridMap.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtMapsName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupCtrRoad)).BeginInit();
            this.groupCtrRoad.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxRoadIdColumn.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxRoadNameColumn.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtRoadMap.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.groupControl4);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.groupCtrGrid);
            this.splitContainerControl1.Panel2.Controls.Add(this.groupControl2);
            this.splitContainerControl1.Panel2.Controls.Add(this.groupCtrRoad);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(720, 446);
            this.splitContainerControl1.SplitterPosition = 212;
            this.splitContainerControl1.TabIndex = 14;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // groupControl4
            // 
            this.groupControl4.Controls.Add(this.listBoxAllSettings);
            this.groupControl4.Controls.Add(this.btnSaveSetting);
            this.groupControl4.Controls.Add(this.btnRemoveSetting);
            this.groupControl4.Controls.Add(this.btnNewSetting);
            this.groupControl4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl4.Location = new System.Drawing.Point(0, 0);
            this.groupControl4.Name = "groupControl4";
            this.groupControl4.Size = new System.Drawing.Size(212, 446);
            this.groupControl4.TabIndex = 8;
            this.groupControl4.Text = "已有图层集";
            // 
            // listBoxAllSettings
            // 
            this.listBoxAllSettings.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.listBoxAllSettings.DrawMode = System.Windows.Forms.DrawMode.OwnerDrawFixed;
            this.listBoxAllSettings.FormattingEnabled = true;
            this.listBoxAllSettings.ItemHeight = 12;
            this.listBoxAllSettings.Location = new System.Drawing.Point(5, 26);
            this.listBoxAllSettings.Name = "listBoxAllSettings";
            this.listBoxAllSettings.Size = new System.Drawing.Size(204, 352);
            this.listBoxAllSettings.Sorted = true;
            this.listBoxAllSettings.TabIndex = 2;
            this.listBoxAllSettings.DrawItem += new System.Windows.Forms.DrawItemEventHandler(this.listBoxAllSettings_DrawItem);
            // 
            // btnSaveSetting
            // 
            this.btnSaveSetting.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSaveSetting.Location = new System.Drawing.Point(146, 404);
            this.btnSaveSetting.Name = "btnSaveSetting";
            this.btnSaveSetting.Size = new System.Drawing.Size(50, 27);
            this.btnSaveSetting.TabIndex = 1;
            this.btnSaveSetting.Text = "保存";
            this.btnSaveSetting.Click += new System.EventHandler(this.btnSaveSetting_Click);
            // 
            // btnRemoveSetting
            // 
            this.btnRemoveSetting.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRemoveSetting.Enabled = false;
            this.btnRemoveSetting.Location = new System.Drawing.Point(79, 404);
            this.btnRemoveSetting.Name = "btnRemoveSetting";
            this.btnRemoveSetting.Size = new System.Drawing.Size(50, 27);
            this.btnRemoveSetting.TabIndex = 1;
            this.btnRemoveSetting.Text = "删除";
            this.btnRemoveSetting.Click += new System.EventHandler(this.btnRemoveSetting_Click);
            // 
            // btnNewSetting
            // 
            this.btnNewSetting.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnNewSetting.Location = new System.Drawing.Point(12, 404);
            this.btnNewSetting.Name = "btnNewSetting";
            this.btnNewSetting.Size = new System.Drawing.Size(50, 27);
            this.btnNewSetting.TabIndex = 1;
            this.btnNewSetting.Text = "新建";
            this.btnNewSetting.Click += new System.EventHandler(this.btnNewSetting_Click);
            // 
            // groupCtrGrid
            // 
            this.groupCtrGrid.Controls.Add(this.cbxGridColumn);
            this.groupCtrGrid.Controls.Add(this.labelControl3);
            this.groupCtrGrid.Controls.Add(this.edtGridMap);
            this.groupCtrGrid.Controls.Add(this.labelControl4);
            this.groupCtrGrid.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupCtrGrid.Location = new System.Drawing.Point(0, 68);
            this.groupCtrGrid.Name = "groupCtrGrid";
            this.groupCtrGrid.Size = new System.Drawing.Size(502, 98);
            this.groupCtrGrid.TabIndex = 16;
            this.groupCtrGrid.Text = "网格图层设置";
            // 
            // cbxGridColumn
            // 
            this.cbxGridColumn.Location = new System.Drawing.Point(102, 65);
            this.cbxGridColumn.Name = "cbxGridColumn";
            this.cbxGridColumn.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxGridColumn.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxGridColumn.Size = new System.Drawing.Size(130, 21);
            this.cbxGridColumn.TabIndex = 7;
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(14, 68);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(84, 14);
            this.labelControl3.TabIndex = 5;
            this.labelControl3.Text = "网格名称列名：";
            // 
            // edtGridMap
            // 
            this.edtGridMap.Location = new System.Drawing.Point(102, 32);
            this.edtGridMap.Name = "edtGridMap";
            this.edtGridMap.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.edtGridMap.Size = new System.Drawing.Size(388, 21);
            this.edtGridMap.TabIndex = 6;
            this.edtGridMap.ButtonClick += new DevExpress.XtraEditors.Controls.ButtonPressedEventHandler(this.edtGridMap_ButtonClick);
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(14, 35);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(84, 14);
            this.labelControl4.TabIndex = 4;
            this.labelControl4.Text = "地市网格图层：";
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.txtMapsName);
            this.groupControl2.Controls.Add(this.labelControl5);
            this.groupControl2.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl2.Location = new System.Drawing.Point(0, 0);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(502, 68);
            this.groupControl2.TabIndex = 15;
            this.groupControl2.Text = "图层集名称";
            // 
            // txtMapsName
            // 
            this.txtMapsName.Location = new System.Drawing.Point(102, 35);
            this.txtMapsName.Name = "txtMapsName";
            this.txtMapsName.Size = new System.Drawing.Size(388, 21);
            this.txtMapsName.TabIndex = 15;
            // 
            // labelControl5
            // 
            this.labelControl5.Location = new System.Drawing.Point(14, 38);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(72, 14);
            this.labelControl5.TabIndex = 5;
            this.labelControl5.Text = "图层集名称：";
            // 
            // groupCtrRoad
            // 
            this.groupCtrRoad.Controls.Add(this.cbxRoadIdColumn);
            this.groupCtrRoad.Controls.Add(this.labelControl6);
            this.groupCtrRoad.Controls.Add(this.listBoxRoadMap);
            this.groupCtrRoad.Controls.Add(this.btnRoadMapSelectNone);
            this.groupCtrRoad.Controls.Add(this.btnRoadMapAdd);
            this.groupCtrRoad.Controls.Add(this.cbxRoadNameColumn);
            this.groupCtrRoad.Controls.Add(this.labelControl2);
            this.groupCtrRoad.Controls.Add(this.edtRoadMap);
            this.groupCtrRoad.Controls.Add(this.btnRoadMapDelete);
            this.groupCtrRoad.Controls.Add(this.labelControl1);
            this.groupCtrRoad.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.groupCtrRoad.Location = new System.Drawing.Point(0, 166);
            this.groupCtrRoad.Name = "groupCtrRoad";
            this.groupCtrRoad.Size = new System.Drawing.Size(502, 280);
            this.groupCtrRoad.TabIndex = 14;
            this.groupCtrRoad.Text = "道路图层设置";
            // 
            // cbxRoadIdColumn
            // 
            this.cbxRoadIdColumn.Location = new System.Drawing.Point(360, 65);
            this.cbxRoadIdColumn.Name = "cbxRoadIdColumn";
            this.cbxRoadIdColumn.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxRoadIdColumn.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxRoadIdColumn.Size = new System.Drawing.Size(130, 21);
            this.cbxRoadIdColumn.TabIndex = 13;
            // 
            // labelControl6
            // 
            this.labelControl6.Location = new System.Drawing.Point(282, 68);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(72, 14);
            this.labelControl6.TabIndex = 12;
            this.labelControl6.Text = "道路ID列名：";
            // 
            // listBoxRoadMap
            // 
            this.listBoxRoadMap.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.listBoxRoadMap.DrawMode = System.Windows.Forms.DrawMode.OwnerDrawFixed;
            this.listBoxRoadMap.FormattingEnabled = true;
            this.listBoxRoadMap.ItemHeight = 12;
            this.listBoxRoadMap.Location = new System.Drawing.Point(14, 102);
            this.listBoxRoadMap.Name = "listBoxRoadMap";
            this.listBoxRoadMap.Size = new System.Drawing.Size(411, 160);
            this.listBoxRoadMap.Sorted = true;
            this.listBoxRoadMap.TabIndex = 11;
            this.listBoxRoadMap.DrawItem += new System.Windows.Forms.DrawItemEventHandler(this.listBoxRoadMap_DrawItem);
            this.listBoxRoadMap.SelectedIndexChanged += new System.EventHandler(this.listBoxRoadMap_SelectedIndexChanged);
            // 
            // btnRoadMapSelectNone
            // 
            this.btnRoadMapSelectNone.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnRoadMapSelectNone.Location = new System.Drawing.Point(440, 186);
            this.btnRoadMapSelectNone.Name = "btnRoadMapSelectNone";
            this.btnRoadMapSelectNone.Size = new System.Drawing.Size(50, 27);
            this.btnRoadMapSelectNone.TabIndex = 10;
            this.btnRoadMapSelectNone.Text = "全清";
            this.btnRoadMapSelectNone.Click += new System.EventHandler(this.btnRoadMapSelectNone_Click);
            // 
            // btnRoadMapAdd
            // 
            this.btnRoadMapAdd.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnRoadMapAdd.Location = new System.Drawing.Point(440, 102);
            this.btnRoadMapAdd.Name = "btnRoadMapAdd";
            this.btnRoadMapAdd.Size = new System.Drawing.Size(50, 27);
            this.btnRoadMapAdd.TabIndex = 7;
            this.btnRoadMapAdd.Text = "添加";
            this.btnRoadMapAdd.Click += new System.EventHandler(this.btnRoadMapAdd_Click);
            // 
            // cbxRoadNameColumn
            // 
            this.cbxRoadNameColumn.Location = new System.Drawing.Point(102, 65);
            this.cbxRoadNameColumn.Name = "cbxRoadNameColumn";
            this.cbxRoadNameColumn.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxRoadNameColumn.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxRoadNameColumn.Size = new System.Drawing.Size(130, 21);
            this.cbxRoadNameColumn.TabIndex = 3;
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(14, 68);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(84, 14);
            this.labelControl2.TabIndex = 1;
            this.labelControl2.Text = "道路名称列名：";
            // 
            // edtRoadMap
            // 
            this.edtRoadMap.Location = new System.Drawing.Point(102, 32);
            this.edtRoadMap.Name = "edtRoadMap";
            this.edtRoadMap.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.edtRoadMap.Size = new System.Drawing.Size(388, 21);
            this.edtRoadMap.TabIndex = 2;
            this.edtRoadMap.ButtonClick += new DevExpress.XtraEditors.Controls.ButtonPressedEventHandler(this.edtRoadMap_ButtonClick);
            // 
            // btnRoadMapDelete
            // 
            this.btnRoadMapDelete.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnRoadMapDelete.Enabled = false;
            this.btnRoadMapDelete.Location = new System.Drawing.Point(440, 144);
            this.btnRoadMapDelete.Name = "btnRoadMapDelete";
            this.btnRoadMapDelete.Size = new System.Drawing.Size(50, 27);
            this.btnRoadMapDelete.TabIndex = 8;
            this.btnRoadMapDelete.Text = "删除";
            this.btnRoadMapDelete.Click += new System.EventHandler(this.btnRoadMapDelete_Click);
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(14, 35);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(84, 14);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "选取道路图层：";
            // 
            // CityRoadMapSetDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.ClientSize = new System.Drawing.Size(720, 446);
            this.Controls.Add(this.splitContainerControl1);
            this.Name = "CityRoadMapSetDlg";
            this.Text = "地市图层设置";
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl4)).EndInit();
            this.groupControl4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupCtrGrid)).EndInit();
            this.groupCtrGrid.ResumeLayout(false);
            this.groupCtrGrid.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxGridColumn.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtGridMap.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.groupControl2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtMapsName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupCtrRoad)).EndInit();
            this.groupCtrRoad.ResumeLayout(false);
            this.groupCtrRoad.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxRoadIdColumn.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxRoadNameColumn.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.edtRoadMap.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.GroupControl groupControl4;
        private System.Windows.Forms.ListBox listBoxAllSettings;
        private DevExpress.XtraEditors.SimpleButton btnSaveSetting;
        private DevExpress.XtraEditors.SimpleButton btnRemoveSetting;
        private DevExpress.XtraEditors.SimpleButton btnNewSetting;
        private DevExpress.XtraEditors.GroupControl groupCtrGrid;
        private DevExpress.XtraEditors.ComboBoxEdit cbxGridColumn;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.ButtonEdit edtGridMap;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.GroupControl groupCtrRoad;
        private DevExpress.XtraEditors.SimpleButton btnRoadMapSelectNone;
        private DevExpress.XtraEditors.SimpleButton btnRoadMapAdd;
        private DevExpress.XtraEditors.ComboBoxEdit cbxRoadNameColumn;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.ButtonEdit edtRoadMap;
        private DevExpress.XtraEditors.SimpleButton btnRoadMapDelete;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.TextEdit txtMapsName;
        private System.Windows.Forms.ListBox listBoxRoadMap;
        private DevExpress.XtraEditors.ComboBoxEdit cbxRoadIdColumn;
        private DevExpress.XtraEditors.LabelControl labelControl6;
    }
}