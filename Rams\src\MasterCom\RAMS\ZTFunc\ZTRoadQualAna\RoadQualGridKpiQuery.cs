﻿using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class RoadPartLteKpiQuery : DIYSQLBase
    {
        protected string dbName;
        protected TimePeriod timePeriod;
        protected string kpiTimeType;
        protected Dictionary<int, RoadPartAreaBaseInfo> roadPartAreaBaseInfoDic;
        protected Dictionary<int, RoadPartAnaInfo> roadPartAnaInfoDic;
        public RoadPartLteKpiQuery(Dictionary<int, RoadPartAreaBaseInfo> roadPartAreaBaseInfoDic
            , Dictionary<int, RoadPartAnaInfo> roadPartAnaInfoDic)
            : base(MainModel.GetInstance())
        {
            this.roadPartAreaBaseInfoDic = roadPartAreaBaseInfoDic;
            this.roadPartAnaInfoDic = roadPartAnaInfoDic;
            this.dbName = RoadQualAnaCfgManager.GetInstance().RoadAnaBaseDBName;

            RoadQualAnaCond roadAnaCond = RoadQualAnaCfgManager.GetInstance().RoadQualAnaSetting;
            this.kpiTimeType = roadAnaCond.KpiTimeType;
            this.timePeriod = roadAnaCond.TimePeriod_Kpi;
        }
        public override string Name
        {
            get { return "RoadPartLteKpiQuery"; }
        }
        protected override string getSqlTextString()
        {
            return string.Format(@"EXEC {0}.dbo.proc_道路分析_客户端_统计栅格小区 '{1}','4G','{2}','{3}'"
                , dbName, kpiTimeType, timePeriod.BeginTime, timePeriod.EndTime);
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            E_VType[] vType = new E_VType[10];
            vType[i++] = E_VType.E_String;
            vType[i++] = E_VType.E_Int;
            vType[i++] = E_VType.E_Int;
            vType[i++] = E_VType.E_Int;
            vType[i++] = E_VType.E_Float;
            vType[i++] = E_VType.E_Int;
            vType[i++] = E_VType.E_Float;
            vType[i++] = E_VType.E_Int;
            vType[i++] = E_VType.E_Int;
            vType[i] = E_VType.E_Int;
            return vType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            int index = 0;
            int progress = 0;
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }

                setProgressPercent(ref index, ref progress);
            }
        }

        private void fillData(Package package)
        {
            DateTime.Parse(package.Content.GetParamString());//testTime
            int areaId = package.Content.GetParamInt();
            int eci = package.Content.GetParamInt();
            ICell iCell = getSrcCell(DateTime.Now, eci);
            if (iCell != null)
            {
                RoadPartCellKpiInfo roadPartCellData = new RoadPartCellKpiInfo(areaId, iCell);

                int sampleNum_Lte = package.Content.GetParamInt();
                float rsrpSum = package.Content.GetParamFloat();
                int sampleNum_Rsrp = package.Content.GetParamInt();
                float sinrSum = package.Content.GetParamFloat();
                int sampleNum_Sinr = package.Content.GetParamInt();
                int sampleNum_LteCoverValid = package.Content.GetParamInt();
                int sampleNum_LteCoverTotal = package.Content.GetParamInt();

                roadPartCellData.AddLteKpi(sampleNum_Lte, sampleNum_Rsrp, rsrpSum, sampleNum_Sinr, sinrSum
                    , sampleNum_LteCoverValid, sampleNum_LteCoverTotal);

                addToRoadPartInfoDic(roadPartCellData);
            }
        }

        protected void addToRoadPartInfoDic(RoadPartCellKpiInfo roadPartCellData)
        {
            RoadPartAnaInfo roadPartAnaItem;
            if (!roadPartAnaInfoDic.TryGetValue(roadPartCellData.AreaId, out roadPartAnaItem))
            {
                RoadPartAreaBaseInfo roadPartBaseInfo;
                if (!roadPartAreaBaseInfoDic.TryGetValue(roadPartCellData.AreaId, out roadPartBaseInfo))
                {
                    return;
                }
                roadPartAnaItem = new RoadPartAnaInfo(roadPartBaseInfo);
                roadPartAnaInfoDic.Add(roadPartCellData.AreaId, roadPartAnaItem);
            }
            roadPartAnaItem.AddRoadPartCellKpi(roadPartCellData);
        }
        private ICell getSrcCell(DateTime time, int eci)
        {
            ICell iCell = null;
            if (eci > 0)
            {
                iCell = CellManager.GetInstance().GetLTECellByECI(time, eci);

                if (iCell == null)
                {
                    return new UnknowCell(0, eci);
                }
            }
            return iCell;
        }
    }

    public class RoadPartGsmKpiQuery : RoadPartLteKpiQuery
    {
        public RoadPartGsmKpiQuery(Dictionary<int, RoadPartAreaBaseInfo> roadPartAreaBaseInfoDic
            , Dictionary<int, RoadPartAnaInfo> roadPartAnaInfoDic)
            : base(roadPartAreaBaseInfoDic, roadPartAnaInfoDic)
        {
        }

        public override string Name
        {
            get { return "RoadPartGsmKpiQuery"; }
        }
        protected override string getSqlTextString()
        {
            return string.Format(@"EXEC {0}.dbo.proc_道路分析_客户端_统计栅格小区 '{1}','2G','{2}','{3}'"
                , dbName, kpiTimeType, timePeriod.BeginTime, timePeriod.EndTime);
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            E_VType[] vType = new E_VType[7];
            vType[i++] = E_VType.E_String;
            vType[i++] = E_VType.E_Int;
            vType[i++] = E_VType.E_Int;
            vType[i++] = E_VType.E_Int;
            vType[i++] = E_VType.E_Int;
            vType[i++] = E_VType.E_Float;
            vType[i] = E_VType.E_Int;
            return vType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            int index = 0;
            int progress = 0;
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }

                setProgressPercent(ref index, ref progress);
            }
        }

        private void fillData(Package package)
        {
            package.Content.GetParamString();//testTimeStr
            int areaID = package.Content.GetParamInt();
            int lac = package.Content.GetParamInt();
            int ci = package.Content.GetParamInt();
            ICell iCell = getSrcCell(lac, ci);
            if (iCell != null)
            {
                RoadPartCellKpiInfo gridCellData = new RoadPartCellKpiInfo(areaID, iCell);

                int sampleNum_Gsm = package.Content.GetParamInt();
                float rxlevSum = package.Content.GetParamFloat();
                int sampleNum_Rxlev = package.Content.GetParamInt();

                gridCellData.AddGsmKpi(sampleNum_Gsm, sampleNum_Rxlev, rxlevSum);

                addToRoadPartInfoDic(gridCellData);
            }
        }

        private ICell getSrcCell(int lac, int ci)
        {
            ICell cell = null;
            if (lac > 0 && ci > 0)
            {
                cell = CellManager.GetInstance().GetICellByLACCI(lac, ci);
            }
            return cell;
        }

    }
}
