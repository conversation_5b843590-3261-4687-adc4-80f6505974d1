﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTLTENCellLevelHigherDlg : BaseDialog
    {
        public ZTLTENCellLevelHigherDlg()
        {
            InitializeComponent();
        }

        public void GetCondition(out int numRSRP)
        {
            numRSRP = (int)numLevelDB.Value;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
