﻿using System;
using System.Collections.Generic;
using System.Text;
using GMap.NET.WindowsForms;
using System.Drawing;
using GMap.NET;
using MasterCom.Util;
using System.Xml;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.MapControlTool;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.ZTFunc.LteSignalImsi;

namespace MasterCom.RAMS.ExMap
{
    public partial class MTExGMap : GMapControl
    {
        private ExMapFromPanel exMapFormPanel = null;
        readonly List<ExMapDrawBaseLayer> layersList = new List<ExMapDrawBaseLayer>();

        private List<LayerBase> customLayerBases = new List<LayerBase>();

        private ExMapRsvRegionLayer rsvRegionLayer = null;
        private ExMapRsvStreetLayer rsvStreetLayer = null;
        private ExMapScanMod3IndexLayer scanMod3IndexLayer = null;

        public ExMapRsvRegionLayer RsvRegionLayer
        {
            get { return rsvRegionLayer; }
        }
        public ExMapRsvStreetLayer RsvStreetLayer
        {
            get { return rsvStreetLayer; }
        }
        public ExMapScanMod3IndexLayer ExMapScanMod3IndexLayer
        {
            get { return scanMod3IndexLayer; }
        }
#if CellEmulateCov
        private ExMapCellEmulateCovLayer layerCellEmulateCov = null;
#endif
#if PCIOpt
        private ExMapPCIOptimizeLayer layerPCIOptimize = null;
#endif
        private PointLatLng mousePoint;
        private ContextMenuStrip ctxMenu;
        private System.ComponentModel.IContainer components;
        private ToolStripMenuItem miSaveAsRsvRegion;
        private ToolStripMenuItem exShowFlyLines;
        private ToolStripMenuItem miReplayTestPoint;
        private ToolStripSeparator toolStripMenuItem1;
        private ToolStripMenuItem miReplayEvent;
        public ENM_GMapOpStatus curOpMode { get; set; } = ENM_GMapOpStatus.E_SELECT;
        private ToolStripMenuItem tsmiOutputHighResolutionMap;

        public MTExGMap()
        {
            InitializeComponent();
            MainModel.GetInstance().MainForm.GetMapForm().TempLayerRemoved += ParentMapForm_TempLayerRemoved;
            initCustomLayers();
            setServerCachePrefer();
            this.Manager.VersionGoogleMapChina = "m@299000000";
            this.Manager.VersionGoogleSatelliteChina = "s@199";

            tsmiOutputHighResolutionMap = new ToolStripMenuItem("高清截图");
            tsmiOutputHighResolutionMap.Click += new EventHandler(tsmiOutputHighResolutionMap_Click);
            ctxMenu.Items.Add(tsmiOutputHighResolutionMap);
        }

        void tsmiOutputHighResolutionMap_Click(object sender, EventArgs e)
        {
            if (this.Visible)
            {
                var exMapPanel = MainModel.GetInstance().MainForm.GetMapForm().ExMapPanel;
                double zoom = exMapPanel.getMainMap().Zoom;
                NumberInputBox box = new NumberInputBox("选择比例", "请选择要截图的比例", (int)(zoom + 1), 19, 7, 1);
                if (box.ShowDialog() == DialogResult.OK)
                {
                    ScreenMapCapture.Instance.OutputHighResolutionMap(exMapPanel, (float)box.Value);
                    //OutputHighResolutionMap((float)box.Value, true);
                }
            }
            else
            {
                MessageBox.Show("地图窗口不可见，不能截图！");
            }
        }

        private readonly List<ExMapDrawBaseLayer> tempLayers = new List<ExMapDrawBaseLayer>();
        private void ParentMapForm_TempLayerRemoved(object sender, EventArgs e)
        {
            for (int i = 0; i < tempLayers.Count; i++)
            {
                CustomDrawLayer mwgLayer = ParentMapForm.TempLayers.Find(x => 
                {
                    bool isFind = x.GetGMapLayerType() == tempLayers[i].GetType();
                    return isFind;
                });
                if (mwgLayer == null)
                {
                    tempLayers.RemoveAt(i);
                    i--;
                }
            }
            needReDrawBufferImage = true;
        }

        //private List<LayerBase> tempNewLayers = new List<LayerBase>();
        //private void ParentMapForm_TempNewLayerRemoved(object sender, EventArgs e)
        //{
        //    for (int i = 0; i < tempNewLayers.Count; i++)
        //    {
        //        tempNewLayers.RemoveAt(i);
        //        i--;
        //    }
        //    needReDrawBufferImage = true;
        //}

        public void UpdateMap()
        {
            needReDrawBufferImage = true;
            this.Invalidate();
            this.Refresh();
        }

        private void setServerCachePrefer()
        {
            if (GMaps.IsOffLineMode)
            {
                this.MapType = GMap.NET.MapType.MT_2DMap;
                this.Manager.Mode = AccessMode.ServerAndCache;
            }
            else
            {
                try
                {
                    //判断是否能连外网
                    //System.Net.Dns.GetHostEntry("www.bing.com");
                    //this.Manager.Mode = AccessMode.ServerAndCache;
                }
                catch
                {
                    //不能连外网会解析DNS报错,此时采用CacheOnly
                    this.Manager.Mode = AccessMode.CacheOnly;
                }
                this.Manager.Mode = AccessMode.CacheOnly;
                string dbConStr = getDbConStr();
                if (!string.IsNullOrEmpty(dbConStr))
                {
                    var ch = new GMap.NET.CacheProviders.MsSQLPureImageCache();
                    ch.ConnectionString = dbConStr;
                    this.Manager.ImageCacheLocal = null;
                    this.Manager.ImageCacheSecond = ch;
                }
            }
        }

        private string getDbConStr()
        {
            return MainModel.GetInstance().GetGMapConString();
        }
        public ExMapFromPanel ParentExMapFormPanel
        {
            set
            {
                this.exMapFormPanel = value;
                if (this.exMapFormPanel != null)
                {
                    gisAdapter = new GMapGisAdapter(exMapFormPanel.getMainMap());
                }
            }
            get
            {
                return exMapFormPanel;
            }
        }
        public MapForm ParentMapForm
        {
            get { return exMapFormPanel.getMapFormInstance(); }
        }
        public MainModel ParentMainModel
        {
            get { return exMapFormPanel.getMapFormInstance().MainModel; }
        }
      
        private void initCustomLayers()
        {
            layersList.Clear();

            MapForm mapForm = MainModel.GetInstance().MainForm.GetMapForm();
            customLayerBases = mapForm.CustomLayerBases;

#if CellEmulateCov
            layerCellEmulateCov = new ExMapCellEmulateCovLayer(this);
            layersList.Add(layerCellEmulateCov);
#endif
#if PCIOpt
            layerPCIOptimize = new ExMapPCIOptimizeLayer(this);
            layersList.Add(layerPCIOptimize);
#endif
            this.rsvRegionLayer = new ExMapRsvRegionLayer(this);
            layersList.Add(rsvRegionLayer);
            this.rsvStreetLayer = new ExMapRsvStreetLayer(this);
            layersList.Add(rsvStreetLayer);
            this.scanMod3IndexLayer = new ExMapScanMod3IndexLayer(this);
            layersList.Add(scanMod3IndexLayer);
        }

        private Bitmap bufferImage = null;
        private bool  needReDrawBufferImage=true;
        public bool NeedReDrawBufferImage
        {
            get
            {
                return needReDrawBufferImage;
            }
        }
        private PointLatLng bufferImagePosition = PointLatLng.Zero;
        GMapGisAdapter gisAdapter;
        /// <summary>
        /// any custom drawing here
        /// </summary>
        /// <param name="drawingContext"></param>
        protected override void OnPaintEtc(System.Drawing.Graphics g)
        {
            base.OnPaintEtc(g);
            if (DesignMode)
            {
                return;
            }
            if (needReDrawBufferImage || bufferImage == null)
            {
                syncTempLayer();//先同步图层
                if (bufferImage != null)
                {
                    bufferImage.Dispose();
                }
                drawLayer();
            }
            GPoint pnt = this.FromLatLngToLocalAdaptered(bufferImagePosition);
            g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
            g.DrawImage(bufferImage, pnt.X, pnt.Y);
        }

        private void drawLayer()
        {
            bufferImage = new Bitmap(this.Width, this.Height);
            Graphics bufferG = Graphics.FromImage(bufferImage);
            try
            {
                //====transparent
                if (transValueA > 0)
                {
                    bufferG.FillRectangle(new SolidBrush(Color.FromArgb(transValueA, Color.White)), this.ClientRectangle);
                }

                ///====
                bufferG.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                Rectangle rect = this.ClientRectangle;
                PointLatLng ptLatLngLT = this.FromLocalToLatLngAdaptered(rect.Left, rect.Top);
                bufferImagePosition = ptLatLngLT;
                PointLatLng ptLatLngBR = this.FromLocalToLatLngAdaptered(rect.Right, rect.Bottom);
                foreach (LayerBase layer in ParentMapForm.TempLayerBaseVec)
                {
                    if (layer.IsVisible)
                    {
                        layer.BindingGisAdapter(gisAdapter);
                        layer.Draw(ClientRectangle, ClientRectangle, bufferG);
                    }
                }
                foreach (ExMapDrawBaseLayer layer in layersList)
                {
                    if (layer.Visible)
                    {
                        layer.Draw(bufferG, ptLatLngLT, ptLatLngBR);
                    }
                }
                foreach (ExMapDrawBaseLayer tempLayer in tempLayers)
                {
                    if (tempLayer.Visible)
                    {
                        tempLayer.Draw(bufferG, ptLatLngLT, ptLatLngBR);
                    }

                }
                foreach (LayerBase layer in customLayerBases)
                {
                    if (layer.IsVisible)
                    {
                        layer.BindingGisAdapter(gisAdapter);
                        layer.Draw(ClientRectangle, ClientRectangle, bufferG);
                    }
                }

                //ParentMapForm.PolygonLayer.BindingGisAdapter(GMapGisAdapter.Instance);
                //ParentMapForm.PolygonLayer.Draw(ClientRectangle, ClientRectangle, bufferG);
                drawAdditionalOverlayInfo(bufferG);
                needReDrawBufferImage = false;
            }
            catch
            {
                //continue
            }
            finally
            {
                bufferG.Dispose();
            }
        }

        private void syncTempLayer()
        {
            foreach (CustomDrawLayer mwgLayer in ParentMapForm.TempLayers)
            {
                Type t = mwgLayer.GetGMapLayerType();
                if (t != null)
                {
                    ExMapDrawBaseLayer gLayer = tempLayers.Find(delegate(ExMapDrawBaseLayer x) { return x.GetType() == t; });
                    if (gLayer == null)
                    {
                        gLayer = Activator.CreateInstance(t, new object[] { this }) as ExMapDrawBaseLayer;
                        tempLayers.Add(gLayer);
                    }
                }
            }
        }

        public DbRect GetCurrentViewBounds()
        {
            Rectangle rect = this.ClientRectangle;
            PointLatLng ptLatLngLT = this.FromLocalToLatLngAdaptered(rect.Left, rect.Top);
            PointLatLng ptLatLngBR = this.FromLocalToLatLngAdaptered(rect.Right, rect.Bottom);
            return new DbRect(ptLatLngLT.Lng,ptLatLngLT.Lat,ptLatLngBR.Lng,ptLatLngBR.Lat);
        }
        
        public void FreshInvalidate()
        {
            this.Invalidate();
        }

        /// <summary>
        /// 纠偏
        /// </summary>
        /// <param name="point"></param>
        /// <returns></returns>
        public GPoint FromLatLngToLocalAdaptered(PointLatLng point)
        {
            return FromLatLngToLocalAdaptered(point.Lng, point.Lat);
        }

        /// <summary>
        /// 纠偏
        /// </summary>
        /// <param name="lng"></param>
        /// <param name="lat"></param>
        /// <returns></returns>
        public GPoint FromLatLngToLocalAdaptered(double lng, double lat)
        {
            double marsLng = lng;
            double marsLat = lat;
            if (MapType != MapType.MT_2DMap 
                && MapType != MapType.MT_Satellite)
            {
                EvilTransform.Wgs2Mars(lat, lng, out marsLat, out marsLng);
            }
            PointLatLng tmp = new PointLatLng(marsLat, marsLng);
            return this.FromLatLngToLocal(tmp);
        }

        public PointLatLng FromLocalToLatLngAdaptered(int x, int y)
        {
            PointLatLng tmp = this.FromLocalToLatLng(x, y);
            if (MapType != MapType.MT_2DMap 
                && MapType != MapType.MT_Satellite)
            {
                double lngO = 0;
                double latO = 0;

                //反推法
                EvilTransform.Mars2Wgs(tmp.Lat, tmp.Lng, out latO, out lngO);
                tmp.Lat = latO;
                tmp.Lng = lngO;
            }
            return tmp;
        }

        public PointLatLng FromLocalToLatLngAdaptered(GPoint point)
        {
            return FromLocalToLatLngAdaptered(point.X, point.Y);
        }

        public void ClearRegionLayer()
        {
            if (addRectTool!=null)
            {
                addRectTool.Clear();
            }
            if (addPolygonTool!=null)
            {
                addPolygonTool.Clear();
            }
            if (rsvRegionLayer != null)
            {
                rsvRegionLayer.ApplyResvRegion(null);
            }
            if (rsvStreetLayer != null)
            {
                rsvStreetLayer.ApplyStreets(null);
            }
            if (scanMod3IndexLayer != null)
            {
                scanMod3IndexLayer.ApplyResvRegion(null);
            }
            needReDrawBufferImage = true;
        }

        internal void SetLayerVisible(string alias, bool visible)
        {
            foreach (ExMapDrawBaseLayer baseLayer in layersList)
            {
                if (baseLayer.Alias.Equals(alias))
                {
                    baseLayer.Visible = visible;
                    break;
                }
            }

            foreach (LayerBase baseLayer in customLayerBases)
            {
                if (baseLayer.getName().Equals(alias))
                {
                    baseLayer.IsVisible = visible;
                    break;
                }
            }
            this.Invalidate();
        }

        ExMapToolAddRect addRectTool = null;
        ExMapToolMeasureDistance measureTool = null;
        internal void SetCurrentToolStatus(ENM_GMapOpStatus opStatus)
        {
            this.CanDragMap = false;
            if (addRectTool != null)
            {
                addRectTool.Deactivate();
            }
            if (measureTool != null)
            {
                measureTool.Deactivate();
            }
            if (addPolygonTool != null)
            {
                addPolygonTool.Deactivate();
            }

            dealToolMode(opStatus);
            this.UpdateMap();
        }

        private void dealToolMode(ENM_GMapOpStatus opStatus)
        {
            if (opStatus == ENM_GMapOpStatus.E_MOVE)
            {
                this.CanDragMap = true;
                curOpMode = ENM_GMapOpStatus.E_MOVE;
                this.Cursor = new Cursor(RAMS.Properties.Resources.cursor_hand.GetHicon());
            }
            else if (opStatus == ENM_GMapOpStatus.E_SELECT)
            {
                curOpMode = ENM_GMapOpStatus.E_SELECT;
                this.Cursor = System.Windows.Forms.Cursors.Arrow;
            }
            else if (opStatus == ENM_GMapOpStatus.E_AddPolygon)
            {
                curOpMode = ENM_GMapOpStatus.E_AddPolygon;
                this.Cursor = System.Windows.Forms.Cursors.Cross;
                if (addPolygonTool == null)
                {
                    addPolygonTool = new ExMapToolAddPolygon(this);
                    addPolygonTool.PolygonCreated += new EventHandler(addPolygonTool_PolygonCreated);
                }
                addPolygonTool.Activate();
            }
            else if (opStatus == ENM_GMapOpStatus.E_AddRectangle)
            {
                curOpMode = ENM_GMapOpStatus.E_AddRectangle;
                this.Cursor = System.Windows.Forms.Cursors.Cross;
                if (addRectTool == null)
                {
                    addRectTool = new ExMapToolAddRect(this);
                    addRectTool.RectangleChanged += new EventHandler(addRectTool_RectangleChanged);
                }
                addRectTool.Activate();
            }
            else if (opStatus == ENM_GMapOpStatus.E_ZoomIn)
            {
                curOpMode = ENM_GMapOpStatus.E_ZoomIn;
                this.Cursor = new Cursor(RAMS.Properties.Resources.cursor_zoom_in.GetHicon());
            }
            else if (opStatus == ENM_GMapOpStatus.E_ZoomOut)
            {
                curOpMode = ENM_GMapOpStatus.E_ZoomOut;
                this.Cursor = new Cursor(RAMS.Properties.Resources.cursor_zoom_out.GetHicon());
            }
            else if (opStatus == ENM_GMapOpStatus.E_Info)
            {
                curOpMode = ENM_GMapOpStatus.E_Info;
                this.Cursor = System.Windows.Forms.Cursors.Help;
            }
            else if (opStatus == ENM_GMapOpStatus.E_Measure)
            {
                curOpMode = ENM_GMapOpStatus.E_Measure;
                if (measureTool == null)
                {
                    measureTool = new ExMapToolMeasureDistance(this, ParentMapForm);
                }
                measureTool.Activate();
                this.Cursor = System.Windows.Forms.Cursors.Cross;
            }
            else if (opStatus == ENM_GMapOpStatus.E_RegionEdit_FastSelect)
            {
                curOpMode = ENM_GMapOpStatus.E_RegionEdit_FastSelect;
                this.Cursor = System.Windows.Forms.Cursors.Arrow;
            }
        }

        void addRectTool_RectangleChanged(object sender, EventArgs e)
        {
            if (sender==addRectTool)
            {
                fireMapFormRegionSelectedChanged();
            }
        }

        private void drawAdditionalOverlayInfo(Graphics g)
        {
            if (addRectTool!=null)
            {
                addRectTool.Draw(g);
            }
            if (measureTool!=null)
            {
                measureTool.Draw(g);
            }
            if (addPolygonTool!=null)
            {
                addPolygonTool.Draw(g);
            }
        }

        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MTExGMap));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplayEvent = new System.Windows.Forms.ToolStripMenuItem();
            this.miReplayTestPoint = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miSaveAsRsvRegion = new System.Windows.Forms.ToolStripMenuItem();
            this.exShowFlyLines = new System.Windows.Forms.ToolStripMenuItem();
            this.ctxMenu.SuspendLayout();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplayEvent,
            this.miReplayTestPoint,
            this.toolStripMenuItem1,
            this.miSaveAsRsvRegion,
            this.exShowFlyLines});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(170, 98);
            this.ctxMenu.Opening += new System.ComponentModel.CancelEventHandler(this.ctxMenu_Opening);
            // 
            // miReplayEvent
            // 
            this.miReplayEvent.Image = ((System.Drawing.Image)(resources.GetObject("miReplayEvent.Image")));
            this.miReplayEvent.Name = "miReplayEvent";
            this.miReplayEvent.Size = new System.Drawing.Size(169, 22);
            this.miReplayEvent.Text = "回放事件";
            this.miReplayEvent.Visible = false;
            this.miReplayEvent.Click += new System.EventHandler(this.miReplayEvent_Click);
            // 
            // miReplayTestPoint
            // 
            this.miReplayTestPoint.Image = ((System.Drawing.Image)(resources.GetObject("miReplayTestPoint.Image")));
            this.miReplayTestPoint.Name = "miReplayTestPoint";
            this.miReplayTestPoint.Size = new System.Drawing.Size(169, 22);
            this.miReplayTestPoint.Text = "回放采样点";
            this.miReplayTestPoint.Visible = false;
            this.miReplayTestPoint.Click += new System.EventHandler(this.miReplayTestPoint_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(166, 6);
            // 
            // miSaveAsRsvRegion
            // 
            this.miSaveAsRsvRegion.Name = "miSaveAsRsvRegion";
            this.miSaveAsRsvRegion.Size = new System.Drawing.Size(169, 22);
            this.miSaveAsRsvRegion.Text = "保存为预存区域...";
            this.miSaveAsRsvRegion.Click += new System.EventHandler(this.miSaveAsRsvRegion_Click);
            // 
            // exShowFlyLines
            // 
            this.exShowFlyLines.Name = "exShowFlyLines";
            this.exShowFlyLines.Size = new System.Drawing.Size(169, 22);
            this.exShowFlyLines.Text = "设置飞线图";
            this.exShowFlyLines.Click += new System.EventHandler(this.exShowFlyLines_Click);
            // 
            // MTExGMap
            // 
            this.ContextMenuStrip = this.ctxMenu;
            this.MaxZoom = 24;
            this.MinZoom = 1;
            this.Name = "MTExGMap";
            this.Size = new System.Drawing.Size(542, 270);
            this.OnCurrentPositionChanged += new GMap.NET.CurrentPositionChanged(this.MTExGMap_OnCurrentPositionChanged);
            this.OnMapDrag += new GMap.NET.MapDrag(this.ExMap_OnMapDrag);
            this.OnMapZoomChanged += new GMap.NET.MapZoomChanged(this.ExMap_OnMapZoomChanged);
            this.MouseClick += new System.Windows.Forms.MouseEventHandler(this.ExMap_MouseClick);
            this.MouseDown += new System.Windows.Forms.MouseEventHandler(this.MTExGMap_MouseDown);
            this.MouseMove += new System.Windows.Forms.MouseEventHandler(this.ExMap_MouseMove);
            this.MouseUp += new System.Windows.Forms.MouseEventHandler(this.ExMap_MouseUp);
            this.ctxMenu.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        private void ExMap_MouseMove(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            mousePoint = this.FromLocalToLatLngAdaptered(e.X, e.Y);
            this.ParentExMapFormPanel.MapForm.SetStatusLabelXY(string.Format("{0:F7}", mousePoint.Lng), string.Format("{0:F7}", mousePoint.Lat));
        }

        private void ExMap_MouseUp(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            if(e.Button!= System.Windows.Forms.MouseButtons.Left)
            {
                MainModel mainModel = MainModel.GetInstance();
                #region Replay Event

                if (mainModel.SelectedEvents.Count == 1)
                {
                    miReplayEvent.Visible = true;

                }
                else
                {
                    miReplayEvent.Visible = false;
                }
                #endregion

                #region Replay Point
                if (mainModel.SelectedTestPoints.Count == 1)
                {
                    miReplayTestPoint.Visible = true;
                }
                else
                {
                    miReplayTestPoint.Visible = false;
                }
                #endregion
                return;
            }
            needReDrawBufferImage = true;
        }

        private void ExMap_MouseClick(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            if (curOpMode == ENM_GMapOpStatus.E_ZoomIn)
            {
                int zm = (int)this.Zoom + 1;
                if (zm >= 1 && zm <= 22)
                {
                    this.Zoom = zm;
                }
            }
            else if (curOpMode == ENM_GMapOpStatus.E_ZoomOut)
            {
                int zm = (int)this.Zoom - 1;
                if (zm >= 1 && zm <= 22)
                {
                    this.Zoom = zm;
                }
            }
        }

        //将GMap中选择的区域更新到MapForm中做为选择区域
        private void fireMapFormRegionSelectedChanged()
        {
            UpdateMap();
            if (curOpMode == ENM_GMapOpStatus.E_AddPolygon)
            {
                exMapFormPanel.FireMapFormRegionChanged_Polygon(addPolygonTool.Points);
            }
            else if (curOpMode == ENM_GMapOpStatus.E_AddRectangle && addRectTool != null)
            {
                exMapFormPanel.FireMapFormRegionChanged_Rectangle(addRectTool.Rectangle);
            }
        }

        public void ApplyAddRectTool(DbRect rect)
        {
            if (addRectTool == null)
            {
                addRectTool = new ExMapToolAddRect(this);
                addRectTool.RectangleChanged += new EventHandler(addRectTool_RectangleChanged);
            }
            addRectTool.Rectangle = rect;
        }

        internal void ApplyMeasureToolData(List<DbPoint> points, bool measureStarted)
        {
            if (measureTool == null)
            {
                measureTool = new ExMapToolMeasureDistance(this, ParentMapForm);
            }
            if (measureStarted)
            {
                measureTool.Activate();
            }
            measureTool.MeasureStarted = measureStarted;
            measureTool.MeasurePoints = points;
        }
        ExMapToolAddPolygon addPolygonTool = null;
        public void ApplyAddPolygonTool(List<DbPoint> points)
        {
            if (addPolygonTool == null)
            {
                addPolygonTool = new ExMapToolAddPolygon(this);
                addPolygonTool.PolygonCreated += new EventHandler(addPolygonTool_PolygonCreated);
            }
            addPolygonTool.Points = points;
        }
        //====================================================

        void addPolygonTool_PolygonCreated(object sender, EventArgs e)
        {
            fireMapFormRegionSelectedChanged();
        }

        private double lngOffset = 0;
        private double latOffset = 0;
        internal void SetMapType(MapTypeAdapter mtype)
        {
            this.MapType = mtype.mapType;
            this.lngOffset = mtype.lngOffset;
            this.latOffset = mtype.latOffset;
        }

        private void ExMap_OnMapZoomChanged()
        {
            double scale = ExMapUtil.parseScaleFromZoomLevel(this.Zoom);
            Console.WriteLine("zoom changed");
            double d = this.Projection.GetGroundResolution((int)this.Zoom, mousePoint.Lat);
            string zoom = "缩放" + string.Format("{0:F2}", d) + "km";
            this.ParentExMapFormPanel.MapForm.DoSetCurrentScale(scale);
            this.ParentExMapFormPanel.MapForm.SetStatusLabelZoom(zoom);
            needReDrawBufferImage = true;
            scanMod3IndexLayer.setZoom(d);
        }

        private void ExMap_OnMapDrag()
        {
            //拖动地图不重画
            needReDrawBufferImage = false;
        }

        private void MTExGMap_OnCurrentPositionChanged(PointLatLng point)
        {
            //切换谷歌地图不重画,但初始位置不一定准确，需拖动地图重画时才能重新对准
            needReDrawBufferImage = false;
        }

        internal void GotoView(DbRect bounds)
        {
            double left = bounds.x1, top = bounds.y2, right = bounds.x2, bottom = bounds.y1;
            if (MapType != MapType.MT_2DMap && MapType != MapType.MT_Satellite)
            {
                EvilTransform.Wgs2Mars(bounds.y2, bounds.x1, out top, out left);
                EvilTransform.Wgs2Mars(bounds.y1, bounds.x2, out bottom, out right);
            }
            SetZoomToFitRect(new RectLatLng(top, left, right - left, top - bottom));
            Position = new PointLatLng(top - (top - bottom) / 2, left + (right - left) / 2);
            needReDrawBufferImage = true;
        }
        public DbRect CurBoundsAdaptered
        {
            get
            {
                DbRect bnds = new DbRect(CurrentViewArea.Left, CurrentViewArea.Bottom
                    , CurrentViewArea.Right, CurrentViewArea.Top);
                if (MapType != MapType.MT_2DMap && MapType != MapType.MT_Satellite)
                {
                    double left,top,right,bottom;
                    EvilTransform.Mars2Wgs(bnds.y2, bnds.x1, out top, out left);
                    EvilTransform.Mars2Wgs(bnds.y1, bnds.x2, out bottom, out right);
                    bnds.x1=left;
                    bnds.x2=right;
                    bnds.y1=bottom;
                    bnds.y2=top;
                }
                return bnds;
            }
        }

        internal void GotoView(DbPoint point)
        {
            PointLatLng pnt = new PointLatLng(point.y, point.x);
            if (MapType != MapType.MT_2DMap && MapType != MapType.MT_Satellite)
            {
                double marsLat, marsLnt;
                EvilTransform.Wgs2Mars(point.y, point.x, out marsLat, out marsLnt);
                pnt.Lat = marsLat;
                pnt.Lng = marsLnt;
            }
            Position = pnt;
            needReDrawBufferImage = true;
        }

        private void miSaveAsRsvRegion_Click(object sender, EventArgs e)
        {
            MainModel mainModel = MainModel.GetInstance();
            MapWinGIS.Shape shape = mainModel.SearchGeometrys.Region;
            if (shape == null)
            {
                MessageBox.Show("当前无可保存区域！");
                return;
            }
            if (!shape.IsValid)
            {
                MessageBox.Show("当前区域无效！\r\n原因：" + shape.IsValidReason);
                return;
            }
            CustomRegionDlg customRegionDlg = new CustomRegionDlg();
            List<string> regionTitleList = new List<string>();
            foreach (string s in mainModel.RegionTableNames)
            {
                if (s.Contains("预存"))
                {
                    regionTitleList.Add(s.Substring(s.LastIndexOf(@"\") + 1, s.IndexOf(".") - s.LastIndexOf(@"\") - 1));
                }
            }
            customRegionDlg.InitTable(regionTitleList);
            customRegionDlg.ShowDialog();
            if (customRegionDlg.DialogResult == DialogResult.OK)
            {
               mainModel.MainForm.SaveCustomRegion(shape, customRegionDlg.tableName, customRegionDlg.regionName);
            }
        }

        //触发MapForm的设置飞线右键菜单
        private void exShowFlyLines_Click(object sender, EventArgs e)
        {
            this.ParentExMapFormPanel.MapForm.miShowFlyLines_Click(sender, e);
        }

        private void ctxMenu_Opening(object sender, System.ComponentModel.CancelEventArgs e)
        {
            //
        }

        private void miReplayEvent_Click(object sender, EventArgs e)
        {
            MainModel mainModel = MainModel.GetInstance();
            if (mainModel.SelectedEvents.Count > 0)
            {
                FileReplayer.Replay(mainModel.SelectedEvents[0], true);
            }
        }

        private void miReplayTestPoint_Click(object sender, EventArgs e)
        {
            MainModel mainModel = MainModel.GetInstance();
            if (mainModel.SelectedTestPoints.Count > 0)
            {
                FileReplayer.Replay(mainModel.SelectedTestPoints[0], true);
            }
        }

        private void MTExGMap_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button != System.Windows.Forms.MouseButtons.Left || (curOpMode != ENM_GMapOpStatus.E_SELECT && curOpMode!= ENM_GMapOpStatus.E_Info && curOpMode!=ENM_GMapOpStatus.E_RegionEdit_FastSelect))
            {//不是鼠标左键按下
                return;
            }
            PointLatLng mPoint = this.FromLocalToLatLngAdaptered(e.X, e.Y);
            double xProjected = mPoint.Lng, yProjected = mPoint.Lat;
            double offsetDiff = getAllowedDiffForSelectExMap();
            
            DbRect regCheckRect = new DbRect(xProjected - offsetDiff, yProjected - offsetDiff, xProjected + offsetDiff, yProjected + offsetDiff);
            MapOperation2 mop2 = new MapOperation2();
            mop2.FillSelectPoint(xProjected, yProjected, offsetDiff);
            MapForm mapForm = MainModel.GetInstance().MainForm.GetMapForm();
            MapWinGIS.Extents extents = new MapWinGIS.Extents();
            extents.SetBounds(xProjected - offsetDiff, yProjected - offsetDiff, 0, xProjected + offsetDiff, yProjected + offsetDiff, 0);
            MasterCom.RAMS.Func.MapForm.MapEventArgs evtA = new MasterCom.RAMS.Func.MapForm.MapEventArgs();
            evtA.Longitude = xProjected;
            evtA.Latitude = yProjected;
            evtA.MapOp2 = mop2;
            mapForm.SelectedDataClear();
            if(curOpMode == ENM_GMapOpStatus.E_SELECT)
            {
                mapForm.FireMapFeatureSelecting(this, evtA);
                mapForm.doToolSelectedChecked(mop2, extents, regCheckRect,true);//Google图上，不在去选择道路了，慢
                needReDrawBufferImage = true;
            }
            else if(curOpMode == ENM_GMapOpStatus.E_Info)
            {
                mapForm.FireMapFeatureSelecting(this, evtA);
                mapForm.doToolInfoCheckedNewLayer(xProjected, yProjected, mop2);
                needReDrawBufferImage = true;
            }
        }

        private double getAllowedDiffForSelectExMap()
        {
            PointLatLng mPoint = this.FromLocalToLatLngAdaptered(50, 50);
            double projX1 = mPoint.Lng;
            //double projY1 = mPoint.Lat;
            mPoint = this.FromLocalToLatLngAdaptered(55, 55);
            double projX2 = mPoint.Lng;
            //double projY2 = mPoint.Lat;
            return Math.Abs(projX2 - projX1);
        }
        //=====================================================================================

        private int transValueA = 0;
        internal void SetBackgroundTransparentA(int v)
        {
            transValueA = v;
            needReDrawBufferImage = true;
            this.Refresh();
        }
    }
    public class MapTypeAdapter
    {
        public string name { get; set; }
        public MapType mapType { get; set; } = MapType.GoogleLabelsChina;
        public double lngOffset { get; set; } = 0;
        public double latOffset { get; set; } = 0;
        public MapTypeAdapter()
        {

        }
        public MapTypeAdapter(string name,MapType type, double lngoffset, double latoffset)
        {
            this.name = name;
            this.mapType = type;
            this.lngOffset = lngoffset;
            this.latOffset = latoffset;
        }
        public static object GetItemValue(XmlConfigFile configFile, XmlElement item, string typeName)
        {
            if (typeName.Equals(typeof(MapTypeAdapter).Name))
            {
                Dictionary<string, object> param = configFile.GetItemValue(item, "Setting") as Dictionary<string, object>;
                MapTypeAdapter condSaver = new MapTypeAdapter();
                condSaver.SetParam ( param);
                return condSaver;
            }
            return null;
        }
        public void SetParam(Dictionary<string, object> value)
        {
            if (value == null || value.Count == 0)
            {
                return;
            }
            this.name = (string)value["Name"];
            this.mapType = (MapType)(int)value["MapType"];
            this.lngOffset = (double)value["LngOffset"];
            this.latOffset = (double)value["LatOffset"];
        }
    }
    public enum ENM_GMapOpStatus
    {
        /// <summary>
        /// 拖动模式
        /// </summary>
        E_MOVE,
        /// <summary>
        /// 选择模式
        /// </summary>
        E_SELECT,
        /// <summary>
        /// 矩形区域选取
        /// </summary>
        E_AddRectangle,
        /// <summary>
        /// 多边形区域选取
        /// </summary>
        E_AddPolygon,
        /// <summary>
        /// ZoomIn
        /// </summary>
        E_ZoomIn,
        /// <summary>
        /// ZoomOut
        /// </summary>
        E_ZoomOut,
        /// <summary>
        /// 测距
        /// </summary>
        E_Measure,
        /// <summary>
        /// 信息
        /// </summary>
        E_Info,
        /// <summary>
        /// 区域图形编辑工具集--添加多边形区域
        /// </summary>
        E_RegionEdit_AddPolygon,
        /// <summary>
        /// 区域图形编辑工具集--修改多边形区域控制点
        /// </summary>
        E_RegionEdit_EditPolygon,
        /// <summary>
        /// 区域图形编辑工具集--点选按钮
        /// </summary>
        E_RegionEdit_FastSelect,
        /// <summary>
        /// 新增打点标注点
        /// </summary>
        E_PinEdit_AddPinPoint
    };
}
