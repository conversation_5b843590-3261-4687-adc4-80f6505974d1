﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc
{
    public class TwoEarfcnGetter
    {
        private Dictionary<string, List<string>> nameCellLst = null;
        public Dictionary<string, List<string>> NameCellLst
        {
            get
            {
                MainModel.GetInstance().DistrictChanged -= TwoEarfcnGetter_DistrictChanged;
                MainModel.GetInstance().DistrictChanged += TwoEarfcnGetter_DistrictChanged;
                if (nameCellLst == null)
                    WaitTextBox.Show("分析各小区多层网关系...", anaCells);
                return nameCellLst;
            }
        }

        void TwoEarfcnGetter_DistrictChanged(object sender, EventArgs e)
        {
            this.nameCellLst = null;
        }

        private void anaCells()
        {
            try
            {
                nameCellLst = new Dictionary<string, List<string>>();
                Dictionary<LTECell, List<LTECell>> cellDic = new Dictionary<LTECell, List<LTECell>>();
                List<LTECell> lteCellList = CellManager.GetInstance().GetCurrentLTECells();
                for (int idx = 0; idx < lteCellList.Count - 1; idx++)
                {
                    LTECell cellOne = lteCellList[idx];
                    if (cellOne.Type == LTEBTSType.Indoor)
                        continue;

                    anaLeftCell(cellDic, lteCellList, idx, cellOne);
                    anaNeighbors(cellOne, cellDic);
                }
                if (lteCellList.Count > 0)
                {
                    anaNeighbors(lteCellList[lteCellList.Count - 1], cellDic);
                }
            }
            finally
            {
                System.Threading.Thread.Sleep(100);
                WaitTextBox.Close();
            }
        }

        private void anaLeftCell (Dictionary<LTECell, List<LTECell>> cellDic, List<LTECell> lteCellList, int idx, LTECell cellOne)
        {
            for (int idxNext = idx + 1; idxNext < lteCellList.Count; idxNext++)
            {
                LTECell cellNext = lteCellList[idxNext];
                if (cellNext.Type == LTEBTSType.Indoor)
                    continue;
                double lngGap = Math.Abs(cellOne.Longitude - cellNext.Longitude);
                double latGap = Math.Abs(cellOne.Latitude - cellNext.Latitude);
                if (lngGap > 0.0006 || latGap > 0.0006)
                {
                    continue;
                }

                double distance = MathFuncs.GetDistance(cellOne.Longitude, cellOne.Latitude, cellNext.Longitude, cellNext.Latitude);
                if (distance < 50)
                {
                    addNeighborCell(cellOne, cellNext, cellDic);
                }
            }
        }

        private void addNeighborCell(LTECell cellOne, LTECell cellNext, Dictionary<LTECell, List<LTECell>> cellDic)
        {
            List<LTECell> neighbors;
            if (!cellDic.TryGetValue(cellOne, out neighbors))
            {
                neighbors = new List<LTECell>();
                cellDic[cellOne] = neighbors;
            }
            neighbors.Add(cellNext);

            if (!cellDic.TryGetValue(cellNext, out neighbors))
            {
                neighbors = new List<LTECell>();
                cellDic[cellNext] = neighbors;
            }
            neighbors.Add(cellOne);
        }

        private void anaNeighbors(LTECell curCell, Dictionary<LTECell, List<LTECell>> cellDic)
        {
            if (cellDic.ContainsKey(curCell))
            {
                List<LTECell> neighbors = cellDic[curCell];
                if (neighbors.Count > 4)
                {
                    Dictionary<int, int> earfcnDic = new Dictionary<int, int>();
                    List<string> neighborNames = new List<string>();
                    foreach (LTECell cell in neighbors)
                    {
                        neighborNames.Add(cell.Name);
                        earfcnDic[cell.EARFCN] = 0;
                    }
                    neighborNames.Add(earfcnDic.Count.ToString());
                    nameCellLst[curCell.Name] = neighborNames;
                }
                cellDic.Remove(curCell);
            }
        }
    }
}
