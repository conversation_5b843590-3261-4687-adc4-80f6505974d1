﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LTEScanNoCoverRoadSetDlg : BaseDialog
    {
        public LTEScanNoCoverRoadSetDlg()
        {
            InitializeComponent();
        }

        public void GetFilterCondition(out int rxLev, out int distance)
        {
            rxLev = (int)numRxLevThreshold.Value;
            distance = (int)numDistance.Value;
        }
    }
}
