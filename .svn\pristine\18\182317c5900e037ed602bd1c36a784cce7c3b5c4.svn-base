﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.MTGis;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class DIYIndexOfRoadStructureByGrid_GScan : DIYSampleByRegion
    {
        private int rxLevDValue = 12;
        private int totalBandCount900 = 95;
        private int totalBandCount1800 = 125;
        private readonly Dictionary<string, MapOperation2> regionMopDic = null;
        private readonly Dictionary<string, IndexOfRoadStructureGridInfo> gridIndexOfRoadStruDic;

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 15041, 15041, this.Name);//////
        }

        public DIYIndexOfRoadStructureByGrid_GScan(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
            regionMopDic = new Dictionary<string, MapOperation2>();
            gridIndexOfRoadStruDic = new Dictionary<string, IndexOfRoadStructureGridInfo>();
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_RxLev";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"GSM_SCAN_RxLev");
            tmpDic.Add("themeName", (object)"GSM_SCAN_RxLev");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override bool getConditionBeforeQuery()
        {
            regionMopDic.Clear();
            gridIndexOfRoadStruDic.Clear();

            IndexOfRoadStructureDlg conditionDlg = new IndexOfRoadStructureDlg();
            if (conditionDlg.ShowDialog() != DialogResult.OK) return false;
            conditionDlg.GetCondition(ref rxLevDValue, ref totalBandCount900, ref totalBandCount1800);

            InitRegionMop2();

            return true;
        }

        private void InitRegionMop2()
        {
            List<ResvRegion> resvRegions = MainModel.SearchGeometrys.SelectedResvRegions;
            MapWinGIS.Shape gmt = MainModel.SearchGeometrys.Region;
            
            if (resvRegions != null && resvRegions.Count > 0)  //预存区域
            {
                foreach (ResvRegion region in resvRegions)
                {
                    if (!regionMopDic.ContainsKey(region.RegionName))
                    {
                        MapOperation2 mapOp2 = new MapOperation2();
                        mapOp2.FillPolygon(region.Shape);
                        regionMopDic.Add(region.RegionName, mapOp2);
                        gridIndexOfRoadStruDic.Add(region.RegionName, new IndexOfRoadStructureGridInfo(region.RegionName));
                    }
                }
            }
            else if (gmt != null)//单个区域
            {
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(gmt);
                regionMopDic.Add("当前区域", mapOp2);
                gridIndexOfRoadStruDic.Add("当前区域", new IndexOfRoadStructureGridInfo("当前区域"));
            }
        }

        private string isContainPoint(DbPoint dPoint)
        {
            foreach (string strKey in regionMopDic.Keys)
            {
                if (regionMopDic[strKey].CheckPointInRegion(dPoint.x, dPoint.y))
                {
                    return strKey;
                }
            }
            return null;
        }

        protected override void doWithDTData(MasterCom.RAMS.Model.TestPoint tp)
        {
            string strRegionName = isContainPoint(new DbPoint(tp.Longitude, tp.Latitude));
            if (strRegionName == null) return;

            List<int> lst900;
            List<int> lst1800;
            GScanTestPointSplitter.Split(tp, out lst900, out lst1800);
            anaSpecificBand(strRegionName, tp, BTSBandType.GSM900, lst900);
            anaSpecificBand(strRegionName, tp, BTSBandType.DSC1800, lst1800);
        }

        private void anaSpecificBand(string regName,TestPoint tp, BTSBandType bandType, List<int> idxOfBand)
        {
            if (idxOfBand.Count == 0)
            {
                return;
            }
            int mainIdx = idxOfBand[0];

            float? rxLevMain = (float?)tp["GSCAN_RxLev", mainIdx];
            if (rxLevMain == null || rxLevMain < -120 || rxLevMain > -10)
            {
                return;
            }
            int sum = 0;
            for (int i = 1; i < idxOfBand.Count; i++)
            {
                int idx = idxOfBand[i];
                float? rxLev = (float?)tp["GSCAN_RxLev", idx];
                if (rxLev == null || rxLev < -120 || rxLev > -10)
                {
                    break;
                }
                Cell cell = tp.GetCell_GSMScan(idx);
                if (rxLevMain - rxLev <= rxLevDValue)
                {
                    if (cell != null)
                    {
                        sum += cell.TCH.Count;
                    }
                }
                else
                {
                    break;
                }
            }
            if (sum == 0)
            {
                return;
            }
            int totalBandCount = bandType == BTSBandType.GSM900 ? totalBandCount900 : totalBandCount1800;
            double indexStructure = Math.Round(1.0 * sum / totalBandCount, 4);
            gridIndexOfRoadStruDic[regName].IndexStruList.Add(indexStructure);
        }

        protected override void FireShowFormAfterQuery()
        {
            IndexOfRoadStructureGridForm form = MainModel.GetObjectFromBlackboard(typeof(IndexOfRoadStructureGridForm)) as IndexOfRoadStructureGridForm;
            if (form==null||form.IsDisposed)
            {
                form = new IndexOfRoadStructureGridForm(MainModel);
            }
            form.Owner = MainModel.MainForm;
            form.FillData(new List<IndexOfRoadStructureGridInfo>(gridIndexOfRoadStruDic.Values));
            form.Visible = true;
            form.BringToFront();
        }
    }

    public class IndexOfRoadStructureGridInfo
    {
        public string RegionName { get; set; }

        public List<double> IndexStruList { get; set; } = new List<double>();
        public int Index { get; set; }
        public string IndexOfStructure
        {
            get
            {
                if (IndexStruList.Count > 0)
                {
                    return Index >= 0 ? ((IndexStruList.Count - Index) * 1.0 / IndexStruList.Count).ToString("p2") : "0%";
                }
                return "-";
            }
        }

        public IndexOfRoadStructureGridInfo(string regionName)
        {
            this.RegionName = regionName;
            Index = -1;
        }
    }
}
