﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.Net
{
    public partial class ZTCellDateSettigDlg : BaseForm
    {
        //标准时段
        public TimePeriod TPStandard { get; set; } = new TimePeriod();
        //对比时段
        public TimePeriod TPCompare { get; set; } = new TimePeriod();

        public ZTCellDateSettigDlg()
            : base()
        {
            InitializeComponent();

            dtpBeginDateStandard.Value = DateTime.Today.AddDays(1 - DateTime.Today.Day).AddMonths(-1);
            dtpAfterDateStandard.Value = DateTime.Today.AddDays(1 - DateTime.Today.Day).AddSeconds(-1);
            dtpBeginDateCompare.Value = dtpBeginDateStandard.Value.AddMonths(1);
            dtpAfterDateCompare.Value = dtpAfterDateStandard.Value.AddMonths(1);
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;

            //修改时间条件              
            TPStandard.SetPeriod(dtpBeginDateStandard.Value, dtpAfterDateStandard.Value);
            TPCompare.SetPeriod(dtpBeginDateCompare.Value, dtpAfterDateCompare.Value);
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
