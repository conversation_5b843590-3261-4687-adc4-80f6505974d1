﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using Microsoft.CSharp;
using System.CodeDom.Compiler;
using MasterCom.RAMS.Model;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func.OwnSampleAnalyse
{
    public class CommonAnalyserCommander
    {
        public string funcName { get; set; }
        public string desc { get; set; }
        public string codeString { get; set; }
        public string descriptionNote { get; set; }
        public string codeParamFormula { get; set; }
        public override string ToString()
        {
            return desc;
        }
        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["funcName"] = funcName;
                param["desc"] = desc;
                param["codeString"] = codeString;
                param["descriptionNote"] = descriptionNote;
                param["codeParaFormula"] = codeParamFormula;
                return param;
            }
            set
            {
                funcName = (string)value["funcName"];
                desc = (string)value["desc"];
                codeString = (string)value["codeString"];
                descriptionNote = (string)value["descriptionNote"];
                if (value.ContainsKey("codeParaFormula"))
                {
                    codeParamFormula = (string)value["codeParaFormula"];
                }
            }
        }

        public bool _classReady { get; set; } = false;

        public bool _hasError { get; set; } = false;

        public object clzzInst { get; set; }
        internal string initFuncClass_TestPoint()
        {
            string strErrorMsg = "";
            CryptionData cdata = new CryptionData();
            string strSourceCode = cdata.DecryptionStringdata(codeString);
            string strSourceCode2 = "public class XClassTesting{ public int x; public string wwwstr;}";
            CSharpCodeProvider objCSharpCodePrivoder = new CSharpCodeProvider();
            CompilerParameters objCompilerParameters = new CompilerParameters();
            objCompilerParameters.ReferencedAssemblies.Add("RAMS.exe");
            objCompilerParameters.ReferencedAssemblies.Add("System.dll");
            objCompilerParameters.ReferencedAssemblies.Add("System.Windows.Forms.dll");
            objCompilerParameters.GenerateInMemory = true;
            CompilerResults cr = objCSharpCodePrivoder.CompileAssemblyFromSource(objCompilerParameters, strSourceCode,strSourceCode2);
            if (cr.Errors.HasErrors)
            {
                strErrorMsg = cr.Errors.Count.ToString() + " Errors:";
                StringBuilder sb = new StringBuilder(strErrorMsg);
                for (int x = 0; x < cr.Errors.Count; x++)
                {
                    sb.Append("\r\nLine: " + cr.Errors[x].Line.ToString() + " - " + cr.Errors[x].ErrorText);
                }
                strErrorMsg = sb.ToString();
                _hasError = true;
                _classReady = false;
            }
            else
            {
                System.Reflection.Assembly objAssembly = cr.CompiledAssembly;
                clzzInst = objAssembly.CreateInstance(funcName);
                if (clzzInst == null)
                {
                    strErrorMsg += "未能实例化类：" + funcName;
                    return strErrorMsg;
                }
                strErrorMsg = getCheckValidFuncErr(strErrorMsg);
                strErrorMsg = getPrepareResultSaverErr(strErrorMsg);
                strErrorMsg = getShowInListViewErr(strErrorMsg);
            }
            return strErrorMsg;
        }

        private string getCheckValidFuncErr(string strErrorMsg)
        {
            //检测校验函数==Start
            try
            {
                object[] paramObj = new object[3];
                paramObj[0] = new TestPoint();
                paramObj[1] = "";
                paramObj[2] = new object();
                clzzInst.GetType().InvokeMember("CheckValidFunc", System.Reflection.BindingFlags.InvokeMethod, null, clzzInst, paramObj);
                _hasError = false;
                _classReady = true;
            }
            catch (MissingMethodException exx)
            {
                strErrorMsg += "未找到函数bool CheckValidFunc(TestPoint tp,string str,object saverRet)或所制定的函数不满足要求！ " + exx.ToString();
                _hasError = true;
                _classReady = false;
            }
            catch (Exception ex)
            {
                strErrorMsg += ex.ToString();
                _hasError = true;
                _classReady = false;
            }//End

            return strErrorMsg;
        }

        private string getPrepareResultSaverErr(string strErrorMsg)
        {
            if (!_hasError && _classReady)
            {
                //检测准备结果变量函数==Start
                try
                {
                    object[] paramObj = new object[0];
                    clzzInst.GetType().InvokeMember("PrepareResultSaver", System.Reflection.BindingFlags.InvokeMethod, null, clzzInst, paramObj);
                    _hasError = false;
                    _classReady = true;
                }
                catch (MissingMethodException exx)
                {
                    strErrorMsg += "未找到函数object PrepareResultSaver()或所制定的函数不满足要求！ " + exx.ToString();
                    _hasError = true;
                    _classReady = false;
                }
                catch (Exception ex)
                {
                    strErrorMsg += ex.ToString();
                    _hasError = true;
                    _classReady = false;
                }//End
            }

            return strErrorMsg;
        }

        private string getShowInListViewErr(string strErrorMsg)
        {
            if (!_hasError && _classReady)
            {
                //检测输出结果函数==Start
                try
                {
                    object[] paramObj = new object[2];
                    paramObj[0] = new object();
                    paramObj[1] = new ListView();
                    clzzInst.GetType().InvokeMember("ShowInListView", System.Reflection.BindingFlags.InvokeMethod, null, clzzInst, paramObj);
                    _hasError = false;
                    _classReady = true;
                }
                catch (MissingMethodException exx)
                {
                    strErrorMsg += "未找到函数bool ShowInListView(object obj,ListView lv)或所制定的函数不满足要求！ " + exx.ToString();
                    _hasError = true;
                    _classReady = false;
                }
                catch (Exception ex)
                {
                    strErrorMsg += ex.ToString();
                    _hasError = true;
                    _classReady = false;
                }//End
            }

            return strErrorMsg;
        }
    };
}
