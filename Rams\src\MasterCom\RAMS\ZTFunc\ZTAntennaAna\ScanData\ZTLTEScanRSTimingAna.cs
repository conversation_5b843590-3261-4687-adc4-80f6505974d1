﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteScanRSTimingAna : ZTAntennaBase
    {
        public ZTLteScanRSTimingAna()
            : base(MainModel.GetInstance())
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_SCAN_TOPN);
#if LT
            carrierID = CarrierType.ChinaUnicom;
#else
            carrierID = CarrierType.ChinaMobile;
#endif
        }
        private static ZTLteScanRSTimingAna instance = null;
        protected static readonly object lockObj = new object();

        public static ZTLteScanRSTimingAna GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTLteScanRSTimingAna();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "天线分析_TDD-LTE时间侵占分析"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 23000, 23022, this.Name);
        }

        string strCityName = "";
        //状态库天线数据
        readonly Dictionary<string, AntCfgSub> antCfgParaDic = new Dictionary<string, AntCfgSub>();
        readonly Dictionary<LteCellPairKey, List<LteScanPairInfo>> pciTimingDic = new Dictionary<LteCellPairKey, List<LteScanPairInfo>>();
        readonly List<TddTimingResult> resultList = new List<TddTimingResult>();

        protected override void query()
        {
            pciTimingDic.Clear();
            if (!MainModel.IsBackground && !MainModel.QueryFromBackground)
            {
                setVarBeforQuery();
                InitRegionMop2();
            }
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                MainModel.ClearDTData();
                if (!MainModel.IsBackground)
                {
                    if (!MainModel.QueryFromBackground)
                    {
                        WaitBox.CanCancel = true;
                        WaitBox.Text = "正在查询...";
                        strCityName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                        WaitBox.Show("读取状态库天线信息...", doWithAntCfgData);
                        WaitBox.Show("读取数据分析...", queryInThread, clientProxy);
                        anaCellPciTiming();
                    }
                    else
                    {
                        getBackgroundData();
                        initBackgroundImageDesc();
                    }
                    MainModel.FireSetDefaultMapSerialTheme("LTE", "RSRP");
                }
                else
                {
                    strCityName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);
                    doBackgroundStatByPeriod(clientProxy);
                }
            }
            catch (Exception exp)
            {
                clientProxy.Close();
                log.Error(exp.Message);
            }
            finally
            {
                MainModel.ClearDTData();
            }
        }

        protected override void statData(ClientProxy clientProxy)
        {
            InitRegionMop2();
            setVarBeforQuery();
            queryInThread(clientProxy);
        }

        protected override void queryInThread(object o)
        {
            try
            {
                doSomethingBeforeQueryInThread();
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                int periodCount = Condition.Periods.Count;
                int index = 0;
                if (condition.IsByRound)
                {
                    queryPeriodInfo(clientProxy, package, null, true);
                }
                else
                {
                    foreach (TimePeriod period in Condition.Periods)
                    {
                        if (WaitBox.CancelRequest)
                        {
                            break;
                        }
                        WaitBox.Text = "开始统计时段[" + period.GetShortString() + "]内的数据...";
                        WaitBox.ProgressPercent = index / periodCount + 10;
                        queryPeriodInfo(clientProxy, package, period, false);
                    }
                }
                getResultAfterQuery();
            }
            catch (Exception ex)
            {
                log.Error(ex.Message + Environment.NewLine + ex.StackTrace);
            }
            finally
            {
                WaitBox.Close();
            }
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_CELL_Specific_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_CELL_Specific_RSSINR";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param["param_name"] = "LTESCAN_TopN_RS_RP_Rx1Tx1";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_RS_CINR_Rx1Tx1";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_RS_Timing_Rx1Tx1";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param["param_name"] = "LTESCAN_TopN_RS_RP_Rx1Tx2";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_RS_CINR_Rx1Tx2";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_RS_Timing_Rx1Tx2";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "LTESCAN_TopN_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"LTE_SCAN");
            tmpDic.Add("themeName", (object)"TopN_CELL_Specific_RSRP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void fillContentNeeded_Sample(Package package)
        {
            List<ColumnDefItem> colDefList = getNeededColumnDefList(curSelDIYSampleGroup);
            StringBuilder sbuilder = new StringBuilder();
            sbuilder.Append("0,1,51,0,2,51,0,4,51,0,5,51,");//isampleid
            for (int i = 0; i < colDefList.Count; i++)
            {
                ColumnDefItem sid = colDefList[i];
                sbuilder.Append(sid.GetTriIdStr());
                if (i < colDefList.Count - 1)
                {
                    sbuilder.Append(",");
                }
            }
            package.Content.AddParam(sbuilder.ToString());
        }

        protected override void doWithDTData(TestPoint tp)
        {
            try
            {
                if (mapOp2.CheckPointInRegion(tp.Longitude, tp.Latitude) && tp is ScanTestPoint_LTE)
                {
                    dealTP(tp);
                }
            }
            catch (Exception ee)
            {
                log.Error(ee.Message);
            }
        }

        private void dealTP(TestPoint tp)
        {
            Dictionary<int, List<LteScanCellInfo>> earfcnDic = new Dictionary<int, List<LteScanCellInfo>>();
            for (int i = 0; i < 50; i++)
            {
                #region 获取采样点数据
                try
                {
                    LteScanCellInfo sampleInfo;
                    int earfcn;
                    bool isValid = setSampleInfo(tp, i, out sampleInfo, out earfcn);
                    if (!isValid)
                    {
                        break;
                    }

                    LTECell lteCell = tp.GetCell_LTEScan(i);
                    if (lteCell == null)
                        continue;
                    addAntCfgDic(lteCell);

                    double cellDistance = lteCell.GetDistance(tp.Longitude, tp.Latitude);
                    if (cellDistance > CD.MAX_COV_DISTANCE_LTE)
                        return;
                    sampleInfo.strCellName = lteCell.Name;
                    sampleInfo.fDistance = (float)cellDistance;
                    sampleInfo.snk = i;

                    List<LteScanCellInfo> ptList;
                    if (!earfcnDic.TryGetValue(earfcn, out ptList))
                    {
                        ptList = new List<LteScanCellInfo>();
                    }
                    ptList.Add(sampleInfo);
                    earfcnDic[earfcn] = ptList;
                }
                catch (Exception exp)
                {
                    log.Error(exp.Message);
                }
                #endregion
            }
            addPciTimingDic(tp, earfcnDic);
        }

        private bool setSampleInfo(TestPoint tp, int i, out LteScanCellInfo sampleInfo, out int earfcn)
        {
            earfcn = 0;
            sampleInfo = new LteScanCellInfo();
            object value = tp["LTESCAN_TopN_CELL_Specific_RSRP", i];
            if (value == null)
                return false;
            float rsrp = float.Parse(value.ToString());
            if (rsrp < -141 || rsrp > 25)
                return false;
            sampleInfo.fRsrp = rsrp;

            value = tp["LTESCAN_TopN_CELL_Specific_RSSINR", i];
            if (value == null)
                return false;
            float sinr = float.Parse(value.ToString());
            if (sinr < -50 || sinr > 50)
                return false;
            sampleInfo.fSinr = sinr;

            object value0 = tp["LTESCAN_TopN_RS_Timing_Rx1Tx1", i];
            if (value0 == null)
                return false;
            int timing0 = (int)float.Parse(value0.ToString());
            if (timing0 <= 0)
                return false;
            sampleInfo.iTiming = timing0;

            value0 = tp["LTESCAN_TopN_EARFCN", i];
            if (value0 == null)
                return false;
            earfcn = int.Parse(value0.ToString());
            if (earfcn < 0)
                return false;
            sampleInfo.iEarfcn = earfcn;

            value0 = tp["LTESCAN_TopN_PCI", i];
            if (value0 == null)
                return false;
            int pci = int.Parse(value0.ToString());
            if (pci < 0)
                return false;
            sampleInfo.iPci = pci;
            return true;
        }

        private void addPciTimingDic(TestPoint tp, Dictionary<int, List<LteScanCellInfo>> earfcnDic)
        {
            foreach (int iEarfcn in earfcnDic.Keys)
            {
                Dictionary<LteCellPairKey, LteScanPairInfo> ltePciDic = calcPciTiming(earfcnDic[iEarfcn], tp);
                foreach (LteCellPairKey cpKey in ltePciDic.Keys)
                {
                    List<LteScanPairInfo> pairInfoList;
                    if (!pciTimingDic.TryGetValue(cpKey, out pairInfoList))
                    {
                        pairInfoList = new List<LteScanPairInfo>();
                    }
                    pairInfoList.Add(ltePciDic[cpKey]);
                    pciTimingDic[cpKey] = pairInfoList;
                }
            }
        }

        /// <summary>
        /// 若当前状态库无数据的小区，则补充
        /// </summary>
        private void addAntCfgDic(LTECell lteCell)
        {
            if (!antCfgParaDic.ContainsKey(lteCell.Name))
            {
                AntCfgSub antCfg = new AntCfgSub();
                antCfg.strCity = strCityName;
                antCfg.strCellName = lteCell.Name;
                antCfg.fLongitude = (float)lteCell.Longitude;
                antCfg.fLatitude = (float)lteCell.Latitude;
                antCfgParaDic.Add(lteCell.Name, antCfg);
            }
        }

        /// <summary>
        /// 查询状态库天线数据
        /// </summary>
        private void doWithAntCfgData()
        {
            WaitBox.CanCancel = true;
            DiyCfgPara cellPara = new DiyCfgPara(MainModel);
            cellPara.Query();
            Dictionary<int, AntCfgSub>  tmpDic = cellPara.antCfgParaDic;
            antCfgParaDic.Clear();
            foreach (int ieci in tmpDic.Keys)
            {
                if (!antCfgParaDic.ContainsKey(tmpDic[ieci].strCellName))
                {
                    antCfgParaDic.Add(tmpDic[ieci].strCellName, tmpDic[ieci]);
                }
            }
            WaitBox.Close();
        }

        /// <summary>
        /// PCI时延矩阵分析法
        /// </summary>
        private Dictionary<LteCellPairKey, LteScanPairInfo> calcPciTiming(List<LteScanCellInfo> ptList,TestPoint tp)
        {
            int iNum = ptList.Count;
            Dictionary<LteCellPairKey, LteScanPairInfo> ltePciDic = new Dictionary<LteCellPairKey, LteScanPairInfo>();
            for (int i = 0; i < iNum; i++)
            {
                try
                {
                    for (int j = 0; j < iNum; j++)
                    {
                        if (i == j)
                            continue;

                        LteCellPairKey pciKey = new LteCellPairKey(ptList[i],ptList[j]);
                        LteScanPairInfo pairInfo = new LteScanPairInfo(ptList[i], ptList[j], tp);

                        if (!ltePciDic.ContainsKey(pciKey))
                            ltePciDic.Add(pciKey, pairInfo);
                    }
                }
                catch (Exception ee)
                {
                    log.Error(ee.Message);
                }
            }
            return ltePciDic;
        }

        /// <summary>
        /// 分析每个小区的时间侵占情况
        /// </summary>
        private void anaCellPciTiming()
        {
            resultList.Clear();

            int idx = 1;
            foreach (LteCellPairKey cpKey in pciTimingDic.Keys)
            {
                List<LteScanPairInfo> pairInfoList = pciTimingDic[cpKey];
                AntCfgSub ant0 = new AntCfgSub();
                AntCfgSub ant1 = new AntCfgSub();
                if (antCfgParaDic.ContainsKey(cpKey.strCellName0))
                    ant0 = antCfgParaDic[cpKey.strCellName0];
                if (antCfgParaDic.ContainsKey(cpKey.strCellName1))
                    ant1 = antCfgParaDic[cpKey.strCellName1];

                TddTimingResult tResult = new TddTimingResult();
                tResult.FillData(idx, pairInfoList, ant0, ant1);
                resultList.Add(tResult);
                idx++;
            }
            ExportTddLTETimingData();
        }

        /// <summary>
        /// 按主服导出数据
        /// </summary>
        private void ExportTddLTETimingData()
        {
            List<NPOIRow> datas = new List<NPOIRow>();
            NPOIRow nr1 = new NPOIRow();
            List<object> cols = new List<object>();
            cols.Add("序号");
            cols.Add("城市");
            cols.Add("被侵占小区所属行政区");
            cols.Add("被侵占小区所属厂家");
            cols.Add("被侵占小区名称");
            cols.Add("被侵占小区频点");
            cols.Add("被侵占小区所属频段");
            cols.Add("被侵占小区PCI");
            cols.Add("被侵占小区平均覆盖距离");
            cols.Add("被侵占小区平均RSRP");
            cols.Add("被侵占小区平均SINR");

            cols.Add("侵占小区所属行政区");
            cols.Add("侵占小区所属厂家");
            cols.Add("侵占小区名称");
            cols.Add("侵占小区PCI");
            cols.Add("侵占小区平均覆盖距离");
            cols.Add("侵占小区平均RSRP");
            cols.Add("侵占小区平均SINR");

            cols.Add("两站点间距离");
            cols.Add("信号强度差值");
            cols.Add("模干扰");
            cols.Add("采样点数量");
            cols.Add("平均时延差");
            cols.Add("时延最小值");
            cols.Add("时延最大值");
            cols.Add("大于1ms时延差占比");
            cols.Add("大于2ms时延差占比");
            nr1.cellValues = cols;
            datas.Add(nr1);
            
            try
            {
                foreach (TddTimingResult tt in resultList)
                {
                    NPOIRow nr = new NPOIRow();
                    List<object> objs = new List<object>();
                    objs.Add(tt.idx);
                    objs.Add(tt.StrCity);
                    objs.Add(tt.StrMainCellRegion);
                    objs.Add(tt.StrMainCellVendor);
                    objs.Add(tt.StrMainCellName);
                    objs.Add(tt.IMainCellEarfcn);
                    objs.Add(tt.StrBandType);
                    objs.Add(tt.IMainCellPci);
                    objs.Add(Math.Round(tt.FAvgDistMainCell, 2));
                    objs.Add(Math.Round(tt.FMainCellRsrp, 2));
                    objs.Add(Math.Round(tt.FMainCellSinr, 2));

                    objs.Add(tt.StrNbCellRegion);
                    objs.Add(tt.StrNbCellVendor);
                    objs.Add(tt.StrNbCellName);
                    objs.Add(tt.INbCellPci);
                    objs.Add(Math.Round(tt.FAvgDistNbCell, 2));
                    objs.Add(Math.Round(tt.FNbCellRsrp, 2));
                    objs.Add(Math.Round(tt.FNbCellSinr, 2));

                    objs.Add(Math.Round(tt.FDistance, 2));
                    objs.Add(Math.Round(tt.FDiffRsrp, 2));
                    objs.Add(tt.StrModType);
                    objs.Add(tt.ISampleNum);
                    objs.Add(Math.Round(tt.FAvgTiming, 2));
                    objs.Add(tt.IMinTiming);
                    objs.Add(tt.IMaxTiming);
                    objs.Add(tt.Str1msRate);
                    objs.Add(tt.Str2msRate);
                    nr.cellValues = objs;
                    datas.Add(nr);
                }
            }
            catch (Exception ee)
            {
                log.Error(ee.Message);
            }

            List<List<NPOIRow>> nrDatasList = new List<List<NPOIRow>>();
            nrDatasList.Add(datas);

            List<string> sheetNames = new List<string>();
            sheetNames.Add("TDD-LTE时间侵占统计");
            FireShowResultForm(nrDatasList, sheetNames);
        }

        private void FireShowResultForm(List<List<NPOIRow>> nrDatasList, List<string> sheetNames)
        {
            if (resultList.Count == 0)
            {
                MessageBox.Show("没有符合条件的结果集！！！");
                return;
            }
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(LTEScanRSTimingForm).FullName);
            LTEScanRSTimingForm form = obj == null ? null : obj as LTEScanRSTimingForm;
            if (form == null || form.IsDisposed)
            {
                form = new LTEScanRSTimingForm(MainModel);
            }
            form.nrDatasList = nrDatasList;
            form.sheetNames = sheetNames;
            form.FillData(resultList);
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
        }

        public class LteScanCellInfo
        {
            public string strCellName { get; set; }
            public int iEarfcn { get; set; }
            public int iPci { get; set; }

            public int iTiming { get; set; }
            public float fRsrp { get; set; }
            public float fSinr { get; set; }
            public float fDistance { get; set; }
            public int snk { get; set; }
            public TestPoint testPoint { get; set; }

            public LteScanCellInfo()
            {
                strCellName = "";
                iEarfcn = 0;
                iPci = 0;
                iTiming = 0;
                fRsrp = 0;
                fSinr = 0;
                fDistance = 0;
                snk = 0;
                testPoint = null;
            }
        }

        public class LteScanPairInfo
        {
            public string strCellName0 { get; set; }
            public int iEarfcn0 { get; set; }
            public int iPci0 { get; set; }

            public int iTiming0 { get; set; }
            public float fRsrp0 { get; set; }
            public float fSinr0 { get; set; }
            public float fDistance0 { get; set; }
            public int snk0 { get; set; }

            public string strCellName1 { get; set; }
            public int iEarfcn1 { get; set; }
            public int iPci1 { get; set; }

            public int iTiming1 { get; set; }
            public float fRsrp1 { get; set; }
            public float fSinr1 { get; set; }
            public float fDistance1 { get; set; }
            public int snk1 { get; set; }

            public TestPoint testPoint { get; set; }

            public LteScanPairInfo()
            {
                strCellName0 = "";
                iEarfcn0 = 0;
                iPci0 = 0;
                iTiming0 = 0;
                fRsrp0 = 0;
                fSinr0 = 0;
                fDistance0 = 0;
                snk0 = 0;

                strCellName1 = "";
                iEarfcn1 = 0;
                iPci1 = 0;
                iTiming1 = 0;
                fRsrp1 = 0;
                fSinr1 = 0;
                fDistance1 = 0;
                snk1 = 0;
                testPoint = null;
            }

            public LteScanPairInfo(LteScanCellInfo cellInfo1, LteScanCellInfo cellInfo2, TestPoint testPoint)
            {
                if (cellInfo1.iPci < cellInfo2.iPci)
                {
                    this.strCellName0 = cellInfo1.strCellName;
                    this.iEarfcn0 = cellInfo1.iEarfcn;
                    this.iPci0 = cellInfo1.iPci;
                    this.iTiming0 = cellInfo1.iTiming;
                    this.fRsrp0 = cellInfo1.fRsrp;
                    this.fSinr0 = cellInfo1.fSinr;
                    this.fDistance0 = cellInfo1.fDistance;
                    this.snk0 = cellInfo1.snk;

                    this.strCellName1 = cellInfo2.strCellName;
                    this.iEarfcn1 = cellInfo2.iEarfcn;
                    this.iPci1 = cellInfo2.iPci;
                    this.iTiming1 = cellInfo2.iTiming;
                    this.fRsrp1 = cellInfo2.fRsrp;
                    this.fSinr1 = cellInfo2.fSinr;
                    this.fDistance1 = cellInfo2.fDistance;
                    this.snk1 = cellInfo2.snk;
                }
                else
                {
                    this.strCellName0 = cellInfo2.strCellName;
                    this.iEarfcn0 = cellInfo2.iEarfcn;
                    this.iPci0 = cellInfo2.iPci;
                    this.iTiming0 = cellInfo2.iTiming;
                    this.fRsrp0 = cellInfo2.fRsrp;
                    this.fSinr0 = cellInfo2.fSinr;
                    this.fDistance0 = cellInfo2.fDistance;
                    this.snk0 = cellInfo2.snk;

                    this.strCellName1 = cellInfo1.strCellName;
                    this.iEarfcn1 = cellInfo1.iEarfcn;
                    this.iPci1 = cellInfo1.iPci;
                    this.iTiming1 = cellInfo1.iTiming;
                    this.fRsrp1 = cellInfo1.fRsrp;
                    this.fSinr1 = cellInfo1.fSinr;
                    this.fDistance1 = cellInfo1.fDistance;
                    this.snk1 = cellInfo1.snk;
                }

                //仅当时延干扰时保留采样点
                if (ITiming > 30720)
                {
                    this.testPoint = testPoint;
                }
            }

            public int ITiming
            {
                get
                {
                    int diffTiming1 = ((iTiming0 - iTiming1) + 307200) % 307200;
                    int diffTiming2 = 307200 - diffTiming1;
                    if (diffTiming1 < diffTiming2)
                        return diffTiming1;
                    else
                        return diffTiming2;
                }
            }
        }

        public class LteCellKey 
        {
            public string strCellName { get; set; }
            public int iEarfcn { get; set; }
            public int iPCI { get; set; }

            public LteCellKey()
            {
                strCellName = "";
                iEarfcn = 0;
                iPCI = 0;
            }

            public LteCellKey(string strCellName, int iEarfcn, int iPci)
            {
                this.strCellName = strCellName;
                this.iEarfcn = iEarfcn;
                this.iPCI = iPci;
            }

            public override bool Equals(object obj)
            {
                LteCellKey other = obj as LteCellKey;
                if (other == null)
                    return false;

                if (!base.GetType().Equals(obj.GetType()))
                    return false;

                return (this.strCellName.Equals(other.strCellName) &&
                        this.iEarfcn.Equals(other.iEarfcn) &&
                        this.iPCI.Equals(other.iPCI));
            }

            public override int GetHashCode()
            {
                string strTmpKey = strCellName;
                return strTmpKey.GetHashCode();
            }
        }

        public class LteCellPairKey 
        {
            public string strCellName0 { get; set; }
            public int iEarfcn0 { get; set; }
            public int iPCI0 { get; set; }

            public string strCellName1 { get; set; }
            public int iEarfcn1 { get; set; }
            public int iPCI1 { get; set; }

            public LteCellPairKey()
            {
                strCellName0 = "";
                iEarfcn0 = 0;
                iPCI0 = 0;

                strCellName1 = "";
                iEarfcn1 = 0;
                iPCI1 = 0;
            }

            public LteCellPairKey(LteScanCellInfo cellInfo1, LteScanCellInfo cellInfo2)
            {
                if (cellInfo1.iPci < cellInfo2.iPci)
                {
                    this.strCellName0 = cellInfo1.strCellName;
                    this.iEarfcn0 = cellInfo1.iEarfcn;
                    this.iPCI0 = cellInfo1.iPci;

                    this.strCellName1 = cellInfo2.strCellName;
                    this.iEarfcn1 = cellInfo2.iEarfcn;
                    this.iPCI1 = cellInfo2.iPci;
                }
                else
                {
                    this.strCellName0 = cellInfo2.strCellName;
                    this.iEarfcn0 = cellInfo2.iEarfcn;
                    this.iPCI0 = cellInfo2.iPci;

                    this.strCellName1 = cellInfo1.strCellName;
                    this.iEarfcn1 = cellInfo1.iEarfcn;
                    this.iPCI1 = cellInfo1.iPci;
                }
            }

            public override bool Equals(object obj)
            {
                LteCellPairKey other = obj as LteCellPairKey;
                if (other == null)
                    return false;

                if (!base.GetType().Equals(obj.GetType()))
                    return false;

                return (this.strCellName0.Equals(other.strCellName0) &&
                        this.iEarfcn0.Equals(other.iEarfcn0) && 
                        this.iPCI0.Equals(other.iPCI0) &&
                        this.strCellName1.Equals(other.strCellName1) &&
                        this.iEarfcn1.Equals(other.iEarfcn1) && 
                        this.iPCI1.Equals(other.iPCI1));
            }

            public override int GetHashCode()
            {
                string strTmpKey = strCellName0 + '|' + strCellName1;
                return strTmpKey.GetHashCode();
            }
        }

        public class TddTimingResult
        {
            public int idx{ get; set; }
            public string StrCity{ get; set; }
            public string StrMainCellRegion{ get; set; }
            public string StrMainCellVendor{ get; set; }
            public string StrMainCellName{ get; set; }
            public int IMainCellEarfcn{ get; set; }
            public int IMainCellPci{ get; set; }
            public float FAvgDistMainCell{ get; set; }
            public float FMainCellRsrp{ get; set; }
            public float FMainCellSinr{ get; set; }

            public string StrNbCellRegion{ get; set; }
            public string StrNbCellVendor{ get; set; }
            public string StrNbCellName{ get; set; }
            public int INbCellPci{ get; set; }
            public float FAvgDistNbCell{ get; set; }
            public float FNbCellRsrp{ get; set; }
            public float FNbCellSinr{ get; set; }

            public float FDistance{ get; set; }
            public int ISampleNum{ get; set; }
            public float FAvgTiming{ get; set; }
            public int IMinTiming{ get; set; }
            public int IMaxTiming{ get; set; }
            public int i1msCount{ get; set; }
            public int i2msCount{ get; set; }

            public List<TestPoint> tpList { get; set; }

        public string StrBandType
            {
                get
                {
                    return LTECell.GetBandTypeByEarfcn_BJ(IMainCellEarfcn).ToString().Split('_')[0];
                }
            }

            public string Str1msRate
            {
                get
                {
                    float fRate = 0;
                    if (ISampleNum != 0)
                    {
                        fRate = Convert.ToSingle(i1msCount) / ISampleNum;
                    }
                    return string.Format("{0:00.00}%", fRate * 100);
                }
            }

            public string Str2msRate
            {
                get
                {
                    float fRate = 0;
                    if (ISampleNum != 0)
                    {
                        fRate = Convert.ToSingle(i2msCount) / ISampleNum;
                    }
                    return string.Format("{0:00.00}%", fRate * 100);
                }
            }

            public float FDiffRsrp
            {
                get
                {
                    return Math.Abs(FMainCellRsrp - FNbCellRsrp);
                }
            }

            public string StrModType
            {
                get
                {
                    if (IMainCellPci % 6 == INbCellPci % 6)
                    {
                        return "模6干扰";
                    }
                    else if (IMainCellPci % 3 == INbCellPci % 3)
                    {
                        return "模3干扰";
                    }
                    else
                    {
                        return "";
                    }
                }
            }

            public TddTimingResult()
            {
                idx = 0;
                StrCity = "";
                StrMainCellRegion = "";
                StrMainCellVendor = "";
                StrMainCellName = "";
                IMainCellEarfcn = 0;
                IMainCellPci = 0;
                FAvgDistMainCell = 0;
                FMainCellRsrp = 0;
                FMainCellSinr = 0;

                StrNbCellRegion = "";
                StrNbCellVendor = "";
                StrNbCellName = "";
                INbCellPci = 0;
                FAvgDistNbCell = 0;
                FNbCellRsrp = 0;
                FNbCellSinr = 0;

                FDistance = 0;
                ISampleNum = 0;
                FAvgTiming = 0;
                IMinTiming = -1;
                IMaxTiming = -1;
                i1msCount = 0;
                i2msCount = 0;
                tpList = new List<TestPoint>();
            }

            public void FillData(int idx, List<LteScanPairInfo> pairInfoList, AntCfgSub ant0, AntCfgSub ant1)
            {
                float fTotalTiming = 0;
                float fMainCellTotalRsrp = 0;
                float fMainCellTotalSinr = 0;
                float fMainCellTotalDist = 0;
                float fNbCellTotalRsrp = 0;
                float fNbCellTotalSinr = 0;
                float fNbCellTotalDist = 0;

                int iNum = 0;
                this.ISampleNum = pairInfoList.Count;
                foreach (LteScanPairInfo pairInfo in pairInfoList)
                {
                    if (iNum == 0)
                    {
                        this.StrMainCellName = pairInfo.strCellName0;
                        this.IMainCellEarfcn = pairInfo.iEarfcn0;
                        this.IMainCellPci = pairInfo.iPci0;
                        this.StrNbCellName = pairInfo.strCellName1;
                        this.INbCellPci = pairInfo.iPci1;
                    }

                    fTotalTiming += pairInfo.ITiming;
                    fMainCellTotalRsrp += pairInfo.fRsrp0;
                    fMainCellTotalSinr += pairInfo.fSinr0;
                    fMainCellTotalDist += pairInfo.fDistance0;
                    fNbCellTotalRsrp += pairInfo.fRsrp1;
                    fNbCellTotalSinr += pairInfo.fSinr1;
                    fNbCellTotalDist += pairInfo.fDistance1;

                    if (pairInfo.ITiming > 30720)
                        this.i1msCount++;
                    if (pairInfo.ITiming > 61440)
                        this.i2msCount++;

                    if (this.IMinTiming == -1 || this.IMinTiming > pairInfo.ITiming)
                        this.IMinTiming = pairInfo.ITiming;
                    if (this.IMaxTiming == -1 || this.IMaxTiming < pairInfo.ITiming)
                        this.IMaxTiming = pairInfo.ITiming;

                    if (pairInfo.testPoint != null)
                        tpList.Add(pairInfo.testPoint);

                    iNum++;
                }

                this.FAvgTiming = fTotalTiming / this.ISampleNum;
                this.FMainCellRsrp = fMainCellTotalRsrp / this.ISampleNum;
                this.FMainCellSinr = fMainCellTotalSinr / this.ISampleNum;
                this.FAvgDistMainCell = fMainCellTotalDist / this.ISampleNum;
                this.FNbCellRsrp = fNbCellTotalRsrp / this.ISampleNum;
                this.FNbCellSinr = fNbCellTotalSinr / this.ISampleNum;
                this.FAvgDistNbCell = fNbCellTotalDist / this.ISampleNum;

                this.idx = idx;
                this.StrCity = ant0.strCity;
                this.StrMainCellRegion = ant0.strRegion;
                this.StrMainCellVendor = ant0.strVender;
                this.StrNbCellRegion = ant1.strRegion;
                this.StrNbCellVendor = ant1.strVender;
                this.FDistance = (float)MathFuncs.GetDistance((double)ant0.fLongitude, (double)ant0.fLatitude, (double)ant1.fLongitude, (double)ant1.fLatitude);
            }
        }
    }
}
