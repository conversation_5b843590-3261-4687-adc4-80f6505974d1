﻿using System.Collections.Generic;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCellParamExportQuery : QueryBase
    {
        ExportSecurityCondition exportSecurityCond;
        public ZTCellParamExportQuery(MainModel mm)
            : base(mm)
        {
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 19000, 19019, this.Name);
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        /// <summary>
        /// 标题名称
        /// </summary>
        public override string Name
        {
            get { return "工参导出"; }
        }

        /// <summary>
        /// 图标名称
        /// </summary>
        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }

        /// <summary>
        /// 查询条件是否有效
        /// </summary>
        protected override bool isValidCondition()
        {
            if (setForm == null)
            {
                setForm = new ZTCellParamExportSetForm();
            }
            if (setForm.ShowDialog() != DialogResult.OK)
            {
                return false;
            }

            if (setForm.getSetCondition(out exportCond))
            {
                string savePath = "";
                if (!ExportResultSecurityHelper.GetExportPermit(this.getRecLogItem(), null,
                    ref savePath, out exportSecurityCond))
                {
                    return false;
                }
                exportCond.SavePath = savePath;
                return true;
            }
            return false;
        }

        /// <summary>
        /// 判断是否选择区域
        /// </summary>
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override void query()
        {
            List<CellParamExportBase> expers = CellParamExporter.Instance.GetExporters(exportCond);
            foreach (CellParamExportBase exp in expers)
            {
                exp.GetNBCellInfo().Invoke();
            }
            WaitBox.Show(ExportInThread, expers);
        }

        protected virtual void ExportInThread(object o)
        {
            try
            {
                WaitBox.CanCancel = true;
                List<CellParamExportBase> expers = o as List<CellParamExportBase>;
                foreach (CellParamExportBase exp in expers)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    exp.Export();
                }
                string savePath = "";
                ExportResultSecurityHelper.ExportSecurity(this.getRecLogItem(), ref savePath, exportSecurityCond);
                exportCond.SavePath = savePath;
                MessageBox.Show("导出完成！", "提示");
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private CellParamExportCondition exportCond;
        private ZTCellParamExportSetForm setForm;
    }
}
