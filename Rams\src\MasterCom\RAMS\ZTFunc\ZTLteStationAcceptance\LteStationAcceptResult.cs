﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteStationAcceptResult
    {
        public int BtsID
        {
            get;
            private set;
        }

        public string BtsName
        {
            get;
            private set;
        }

        public int CellCount
        {
            get { return cellDic.Count; }
        }
        public Dictionary<int, object[,]> CellDic
        {
            get { return cellDic; }
        }

        public List<int> CellIDs
        {
            get
            {
                List<int> retList = new List<int>(cellDic.Keys);
                retList.Sort();
                return retList;
            }
        }

        public LteStationAcceptResult(int btsID, string btsName,
            int rowCnt, int colCnt)
        {
            BtsID = btsID;
            BtsName = btsName;

            this.rowCnt = rowCnt;
            this.colCnt = colCnt;
            this.cellDic = new Dictionary<int, object[,]>();
        }

        public object GetValue(int cellID, int rowIdx, int colIdx)
        {
            if (!cellDic.ContainsKey(cellID))
            {
                return null;
            }

            object[,] table = cellDic[cellID];
            if (rowIdx >= table.GetLength(0) || colIdx >= table.GetLength(1))
            {
                return null;
            }

            return table[rowIdx, colIdx];
        }

        public void SetValue(int cellID, int rowIdx, int colIdx, object value)
        {
            if (!cellDic.ContainsKey(cellID))
            {
                cellDic.Add(cellID, new object[rowCnt, colCnt]);
            }
            cellDic[cellID][rowIdx, colIdx] = value;
        }


        private readonly Dictionary<int, object[,]> cellDic;

        private readonly int rowCnt;

        private readonly int colCnt;
    }
}
