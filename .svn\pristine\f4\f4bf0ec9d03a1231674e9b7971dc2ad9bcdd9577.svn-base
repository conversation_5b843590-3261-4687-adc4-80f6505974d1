using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.ZTHandoverAndReselection.File
{
    public partial class QueryHandoverInfoDlg : BaseDialog
    {
        public QueryHandoverInfoDlg()
        {
            InitializeComponent();
        }

        public int SecondsAna
        {
            get { return (int)numSecondAna.Value; }
        }

        public int SecondsBad
        {
            get { return (int)numSecondBad.Value; }
        }

        public int DistanceBad
        {
            get { return (int)numDistanceBad.Value; }
        }

        public int SecondsBack
        {
            get { return (int)numSecondsBack.Value; }
        }

        public int DistanceBack
        {
            get { return (int)numDistanceBack.Value; }
        }

        public int HandoverTimesBack
        {
            get { return (int)numHandoverTimesBack.Value; }
        }
    }
}

