﻿
namespace MasterCom.RAMS.ZTFunc
{
    partial class NRScanModRoadSettingDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.numDiffRxlev = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.numMaxRxlev = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.numRoadLength = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.numSampleInterval = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.cmbModType = new DevExpress.XtraEditors.ComboBoxEdit();
            ((System.ComponentModel.ISupportInitialize)(this.numDiffRxlev.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxRxlev.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRoadLength.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleInterval.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbModType.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl8.Appearance.Options.UseFont = true;
            this.labelControl8.Location = new System.Drawing.Point(224, 59);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(12, 12);
            this.labelControl8.TabIndex = 28;
            this.labelControl8.Text = "dB";
            // 
            // numDiffRxlev
            // 
            this.numDiffRxlev.EditValue = new decimal(new int[] {
            12,
            0,
            0,
            0});
            this.numDiffRxlev.Location = new System.Drawing.Point(112, 56);
            this.numDiffRxlev.Name = "numDiffRxlev";
            this.numDiffRxlev.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numDiffRxlev.Properties.Appearance.Options.UseFont = true;
            this.numDiffRxlev.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDiffRxlev.Properties.IsFloatValue = false;
            this.numDiffRxlev.Properties.Mask.EditMask = "N00";
            this.numDiffRxlev.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numDiffRxlev.Properties.MinValue = new decimal(new int[] {
            100,
            0,
            0,
            -2147483648});
            this.numDiffRxlev.Size = new System.Drawing.Size(105, 20);
            this.numDiffRxlev.TabIndex = 17;
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl7.Appearance.Options.UseFont = true;
            this.labelControl7.Location = new System.Drawing.Point(29, 62);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(60, 12);
            this.labelControl7.TabIndex = 27;
            this.labelControl7.Text = "相对覆盖≤";
            // 
            // numMaxRxlev
            // 
            this.numMaxRxlev.EditValue = new decimal(new int[] {
            95,
            0,
            0,
            -2147483648});
            this.numMaxRxlev.Location = new System.Drawing.Point(112, 22);
            this.numMaxRxlev.Name = "numMaxRxlev";
            this.numMaxRxlev.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numMaxRxlev.Properties.Appearance.Options.UseFont = true;
            this.numMaxRxlev.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMaxRxlev.Properties.IsFloatValue = false;
            this.numMaxRxlev.Properties.Mask.EditMask = "N00";
            this.numMaxRxlev.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numMaxRxlev.Properties.MinValue = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numMaxRxlev.Size = new System.Drawing.Size(105, 20);
            this.numMaxRxlev.TabIndex = 15;
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Location = new System.Drawing.Point(224, 27);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(18, 12);
            this.labelControl6.TabIndex = 26;
            this.labelControl6.Text = "dBm";
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(29, 29);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(60, 12);
            this.labelControl5.TabIndex = 25;
            this.labelControl5.Text = "最强信号≥";
            // 
            // btnOK
            // 
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.Location = new System.Drawing.Point(49, 209);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(87, 27);
            this.btnOK.TabIndex = 23;
            this.btnOK.Text = "确定";
            // 
            // btnCancel
            // 
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.Location = new System.Drawing.Point(157, 209);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(87, 27);
            this.btnCancel.TabIndex = 22;
            this.btnCancel.Text = "取消";
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(224, 131);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(12, 12);
            this.labelControl4.TabIndex = 24;
            this.labelControl4.Text = "米";
            // 
            // numRoadLength
            // 
            this.numRoadLength.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numRoadLength.Location = new System.Drawing.Point(112, 127);
            this.numRoadLength.Name = "numRoadLength";
            this.numRoadLength.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numRoadLength.Properties.Appearance.Options.UseFont = true;
            this.numRoadLength.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRoadLength.Properties.HideSelection = false;
            this.numRoadLength.Properties.IsFloatValue = false;
            this.numRoadLength.Properties.Mask.EditMask = "N00";
            this.numRoadLength.Properties.MaxValue = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numRoadLength.Size = new System.Drawing.Size(105, 20);
            this.numRoadLength.TabIndex = 20;
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(29, 133);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(60, 12);
            this.labelControl3.TabIndex = 21;
            this.labelControl3.Text = "道路长度≥";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(224, 96);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(12, 12);
            this.labelControl2.TabIndex = 18;
            this.labelControl2.Text = "米";
            // 
            // numSampleInterval
            // 
            this.numSampleInterval.EditValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numSampleInterval.Location = new System.Drawing.Point(112, 91);
            this.numSampleInterval.Name = "numSampleInterval";
            this.numSampleInterval.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSampleInterval.Properties.Appearance.Options.UseFont = true;
            this.numSampleInterval.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSampleInterval.Properties.IsFloatValue = false;
            this.numSampleInterval.Properties.Mask.EditMask = "N00";
            this.numSampleInterval.Properties.MaxValue = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numSampleInterval.Size = new System.Drawing.Size(105, 20);
            this.numSampleInterval.TabIndex = 19;
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(13, 98);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(72, 12);
            this.labelControl1.TabIndex = 16;
            this.labelControl1.Text = "采样点间隔≤";
            // 
            // labelControl9
            // 
            this.labelControl9.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl9.Appearance.Options.UseFont = true;
            this.labelControl9.Location = new System.Drawing.Point(224, 166);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(24, 12);
            this.labelControl9.TabIndex = 31;
            this.labelControl9.Text = "干扰";
            // 
            // labelControl10
            // 
            this.labelControl10.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl10.Appearance.Options.UseFont = true;
            this.labelControl10.Location = new System.Drawing.Point(41, 166);
            this.labelControl10.Name = "labelControl10";
            this.labelControl10.Size = new System.Drawing.Size(48, 12);
            this.labelControl10.TabIndex = 30;
            this.labelControl10.Text = "自定义模";
            // 
            // cmbModType
            // 
            this.cmbModType.EditValue = "3";
            this.cmbModType.Location = new System.Drawing.Point(112, 162);
            this.cmbModType.Name = "cmbModType";
            this.cmbModType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cmbModType.Properties.Items.AddRange(new object[] {
            "3",
            "4",
            "6"});
            this.cmbModType.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cmbModType.Size = new System.Drawing.Size(105, 21);
            this.cmbModType.TabIndex = 29;
            // 
            // NRScanModRoadSettingDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(275, 258);
            this.Controls.Add(this.labelControl9);
            this.Controls.Add(this.labelControl10);
            this.Controls.Add(this.cmbModType);
            this.Controls.Add(this.labelControl8);
            this.Controls.Add(this.numDiffRxlev);
            this.Controls.Add(this.labelControl7);
            this.Controls.Add(this.numMaxRxlev);
            this.Controls.Add(this.labelControl6);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.numRoadLength);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.numSampleInterval);
            this.Controls.Add(this.labelControl1);
            this.Name = "NRScanModRoadSettingDlg";
            this.Text = "NR道路模干扰条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numDiffRxlev.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxRxlev.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRoadLength.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleInterval.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbModType.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.SpinEdit numDiffRxlev;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.SpinEdit numMaxRxlev;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.SpinEdit numRoadLength;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SpinEdit numSampleInterval;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraEditors.ComboBoxEdit cmbModType;
    }
}