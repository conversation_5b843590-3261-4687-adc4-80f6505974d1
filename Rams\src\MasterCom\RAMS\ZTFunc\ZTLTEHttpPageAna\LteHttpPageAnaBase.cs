﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    class LteHttpPageAnaBase : DIYAnalyseFilesOneByOneByRegion
    {
        public LteHttpPageAnaBase(MainModel mainModel)
            : base(mainModel)
        {
            this.FilterEventByRegion = false;
            this.IncludeMessage = true;
            this.IncludeTestPoint = true;
            this.IncludeEvent = true;

            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
            ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);
            ServiceTypes.Add(ServiceType.LTE_TDD_IDLE);
            ServiceTypes.Add(ServiceType.LTE_TDD_MULTI);
            ServiceTypes.Add(ServiceType.LTE_TDD_UEP);

            ServiceTypes.Add(ServiceType.LTE_FDD_DATA);
            ServiceTypes.Add(ServiceType.LTE_FDD_IDLE);
            ServiceTypes.Add(ServiceType.LTE_FDD_MULTI);
            ServiceTypes.Add(ServiceType.LTE_FDD_VOICE);

        }

        protected string RsrpStr = "";
        protected string SinrStr = "";
        protected string NCellRsrpStr = "";
        protected string AppSpeedStr = "";
        protected string TacStr = "";

        protected List<ZTLTEHttpPageAnaItem> httpPageAnaList = new List<ZTLTEHttpPageAnaItem>();
        protected HttpPageCondion_LTE httpPageCondition = new HttpPageCondion_LTE();
        private readonly LteURLAnalyzer analyzer = new LteURLAnalyzer();

        protected override bool getCondition()
        {
            setParmAndServiceType();
            LteHttpPageSettingDlg dlg = new LteHttpPageSettingDlg(httpPageCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                httpPageCondition = dlg.GetCondition();
                return true;
            }
            return false;
        }

        /// <summary>
        /// 设置参数名和业务类型
        /// </summary>
        protected virtual void setParmAndServiceType()
        {
            this.RsrpStr = "lte_RSRP";
            this.SinrStr = "lte_SINR";
            this.NCellRsrpStr = "lte_NCell_RSRP";
            this.AppSpeedStr = "lte_APP_Speed_Mb";
            this.TacStr = "lte_TAC";

            this.Columns = new List<string>();
            Columns.Add(RsrpStr);
            Columns.Add(SinrStr);
            Columns.Add(TacStr);
            Columns.Add(NCellRsrpStr);
            Columns.Add(AppSpeedStr);
            Columns.Add("lte_ECI");
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_PCI");
            Columns.Add("lte_APP_Speed");
            Columns.Add("lte_APP_type");
            Columns.Add("lte_Transmission_Mode");
            Columns.Add("lte_PDSCH_BLER");
            Columns.Add("lte_Rank_Indicator");
            Columns.Add("lte_PDCCH_DL_Grant_Count");
            Columns.Add("lte_PDSCH_PRb_Num_slot");
            Columns.Add("lte_NCell_SINR");

            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
            ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);
            ServiceTypes.Add(ServiceType.LTE_TDD_IDLE);
            ServiceTypes.Add(ServiceType.LTE_TDD_MULTI);
            ServiceTypes.Add(ServiceType.LTE_TDD_UEP);
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            httpPageAnaList = new List<ZTLTEHttpPageAnaItem>();
        }
        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                List<Event> evtsList = fileMng.Events;
                ZTLTEHttpPageAnaItem curAnaItem = null;
                foreach (Event evt in evtsList)
                {
                    if (evt.ID == (int)ELteHttpPageEvt.HttpPageRequest ||
                        evt.ID == (int)ELteHttpPageEvt.HttpPageRequestFDD)     //Http浏览发起
                    {
                        setStartInfo(evt, fileMng.Messages, ref curAnaItem);
                    }
                    else if ((evt.ID == (int)ELteHttpPageEvt.HttpPageComplete ||
                        evt.ID == (int)ELteHttpPageEvt.HttpPageCompleteFDD ||
                         evt.ID == (int)ELteHttpPageEvt.HttpPageIncomplete ||
                         evt.ID == (int)ELteHttpPageEvt.HttpPageIncompleteFDD))  //Http浏览加载完成或未完成
                    {
                        curAnaItem = setZTLTEHttpPageAnaItem(fileMng, curAnaItem, evt);
                    }
                }
            }
        }

        private ZTLTEHttpPageAnaItem setZTLTEHttpPageAnaItem(DTFileDataManager fileMng, ZTLTEHttpPageAnaItem curAnaItem, Event evt)
        {
            if (curAnaItem != null)
            {
                curAnaItem.EndTime = evt.DateTime;
                curAnaItem.PagingTime = calcInterSeconds(curAnaItem.BeginTime, curAnaItem.EndTime);
                if (curAnaItem.PagingTime > httpPageCondition.LessPageTime
                    && (curAnaItem.PagingTime <= httpPageCondition.GreatPageTime || httpPageCondition.IsNoGreat))
                //达到浏览时长要求
                {
                    TimePeriod period = new TimePeriod(curAnaItem.BeginTime.AddSeconds(-httpPageCondition.PreLoadTime), curAnaItem.EndTime);
                    curAnaItem.TpsList = getTestPoinsByPeriod(period, fileMng.TestPoints);
                    setEndInfo(evt, ref curAnaItem);
                }
            }

            return curAnaItem;
        }

        /// <summary>
        /// 计算datetime的间隔秒数
        /// </summary>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        protected static double calcInterSeconds(DateTime beginTime, DateTime endTime)
        {
            TimeSpan ts = endTime.Subtract(beginTime);
            return ts.TotalSeconds;
        }

        /// <summary>
        /// 设置开始事件相关信息
        /// </summary>
        /// <param name="evt"></param>
        /// <param name="msgList"></param>
        /// <param name="anaItem"></param>
        private void setStartInfo(Event evt, List<Model.Message> msgList, ref ZTLTEHttpPageAnaItem anaItem)
        {
            anaItem = new ZTLTEHttpPageAnaItem(evt.FileName);
            anaItem.BeginTime = evt.DateTime;
            anaItem.BeginLatitude = evt.Latitude;
            anaItem.BeginLongitude = evt.Longitude;
            anaItem.URL = analyzer.GetURL(evt.SN, msgList);
        }

        /// <summary>
        /// 设置结束事件相关信息
        /// </summary>
        /// <param name="evt"></param>
        /// <param name="anaItem"></param>
        private void setEndInfo(Event evt, ref ZTLTEHttpPageAnaItem anaItem)
        {
            if (isValidPeriod(anaItem.TpsList))
            {
                anaItem.FillItem(RsrpStr, SinrStr, TacStr, AppSpeedStr, NCellRsrpStr);
                anaItem.SN = httpPageAnaList.Count + 1;
                anaItem.EndLatitude = evt.Latitude;
                anaItem.EndLongitude = evt.Longitude;
                checkIsFailed(ref anaItem, evt.ID);
                anaItem.StatTpsList = getVaildTestPoints(anaItem.TpsList, anaItem.MaxNBIndexList);
                httpPageAnaList.Add(anaItem);
            }
            anaItem = null;
        }

        protected override void fireShowForm()
        {
            if (httpPageAnaList.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }
            MainModel.FireSetDefaultMapSerialTheme(RsrpStr);
            LteHttpPageAnaForm frm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(LteHttpPageAnaForm)) as LteHttpPageAnaForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new LteHttpPageAnaForm(MainModel);
            }
            frm.FillData(httpPageAnaList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            httpPageAnaList = null;
        }

        /// <summary>
        /// 筛选在区域内的采样点
        /// </summary>
        /// <param name="tpList"></param>
        /// <returns></returns>
        protected List<KeyValuePair<TestPoint, int>> getVaildTestPoints(List<TestPoint> tpList, List<int> maxNBIndexList)
        {
            List<KeyValuePair<TestPoint, int>> ret = new List<KeyValuePair<TestPoint, int>>();
            if (tpList.Count == maxNBIndexList.Count)
            {
                for (int i = 0; i < tpList.Count; i++)
                {
                    if (isValidTestPoint(tpList[i]))
                    {
                        ret.Add(new KeyValuePair<TestPoint, int>(tpList[i], maxNBIndexList[i]));
                    }
                }
            }
            return ret;
        }

        /// <summary>
        /// 根据时间段获取采样点
        /// </summary>
        /// <param name="period"></param>
        /// <param name="tps"></param>
        /// <returns></returns>
        protected List<TestPoint> getTestPoinsByPeriod(TimePeriod period, List<TestPoint> tps)
        {
            List<TestPoint> ret = new List<TestPoint>();
            for (int index = 0; index < tps.Count; index++)
            {
                TestPoint tp = tps[index];
                if (tp.DateTime >= period.BeginTime && tp.DateTime <= period.EndTime)
                {
                    ret.Add(tp);
                }
                else if (tp.DateTime > period.EndTime)
                {
                    break;
                }
            }
            return ret;
        }

        /// <summary>
        /// 判断时间段的采样点存在区域内
        /// </summary>
        /// <param name="tpsList"></param>
        /// <returns></returns>
        protected virtual bool isValidPeriod(List<TestPoint> tpsList)
        {
            foreach (TestPoint tp in tpsList)
            {
                if (isValidTestPoint(tp))
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 查询HTTP浏览加载是否成功
        /// </summary>
        /// <param name="anaItem"></param>
        /// <param name="evtID"></param>
        protected void checkIsFailed(ref ZTLTEHttpPageAnaItem anaItem, int evtID)
        {
            if (evtID == (int)ELteHttpPageEvt.HttpPageComplete ||
                evtID == (int)ELteHttpPageEvt.HttpPageCompleteFDD)
            {
                anaItem.IsFailed = "成功";
            }
            else if (evtID == (int)ELteHttpPageEvt.HttpPageIncomplete ||
                evtID == (int)ELteHttpPageEvt.HttpPageIncompleteFDD)
            {
                anaItem.IsFailed = "失败";
            }
        }

    }
    /// <summary>
    /// Http浏览统计单元
    /// </summary>
    public class ZTLTEHttpPageAnaItem : LteHttpPageOrVideoPlay.LteCommonParm
    {
        public ZTLTEHttpPageAnaItem(string fileName)
        {
            FileName = fileName;
            TpsList = new List<TestPoint>();
        }
        public double PagingTime
        {
            get;
            set;
        }
        public string URL
        {
            get;
            set;
        }
        public string IsFailed
        {
            get;
            set;
        }
    }
    public class HttpPageCondion_LTE
    {
        /// <summary>
        /// Http浏览时间上限
        /// </summary>
        public int GreatPageTime { get; set; } = 5;
        /// <summary>
        /// Http浏览时间下限
        /// </summary>
        public int LessPageTime { get; set; }
        /// <summary>
        /// 不设上限
        /// </summary>
        public bool IsNoGreat { get; set; } = false;
        /// <summary>
        /// HTTP浏览发起前几秒
        /// </summary>
        public int PreLoadTime { get; set; }
    }
    enum ELteHttpPageEvt
    {
        HttpPageRequest = 1211,
        HttpPageComplete = 1214,
        HttpPageIncomplete = 1215,
        HttpPageFailID = 1269,
        HttpPageRequestFDD = 3211,
        HttpPageCompleteFDD = 3214,
        HttpPageIncompleteFDD = 3215,
    }

}
