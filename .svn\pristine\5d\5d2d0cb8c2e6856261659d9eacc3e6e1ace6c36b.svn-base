﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LteURLAnaForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode2 = new DevExpress.XtraGrid.GridLevelNode();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode3 = new DevExpress.XtraGrid.GridLevelNode();
            this.gvBroURL = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn75 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn61 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn62 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn63 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn64 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcBrowse = new DevExpress.XtraGrid.GridControl();
            this.gvBroFile = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn82 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn81 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn78 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView4 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView5 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gvDowURL = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn76 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn65 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn66 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn67 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcDownload = new DevExpress.XtraGrid.GridControl();
            this.gvDowFile = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn83 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn84 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn79 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView8 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView9 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView10 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gvVideoURL = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn77 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn44 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn45 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn68 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn69 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn70 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn71 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn72 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn73 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn74 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcVideo = new DevExpress.XtraGrid.GridControl();
            this.gvVideoFile = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn46 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn85 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn86 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn80 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn47 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn48 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView16 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView17 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridView18 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gcBroDetail = new DevExpress.XtraGrid.GridControl();
            this.ctxMenuReplay = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplayFile = new System.Windows.Forms.ToolStripMenuItem();
            this.gvBroDet = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn87 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.tabPageBrowse = new System.Windows.Forms.TabPage();
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPageDL = new System.Windows.Forms.TabPage();
            this.splitContainer2 = new System.Windows.Forms.SplitContainer();
            this.gcDowDet = new DevExpress.XtraGrid.GridControl();
            this.gvDowDet = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn88 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView12 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn39 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView13 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.tabPageVideo = new System.Windows.Forms.TabPage();
            this.splitContainer3 = new System.Windows.Forms.SplitContainer();
            this.gcVideoDet = new DevExpress.XtraGrid.GridControl();
            this.gvVideoDet = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn49 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn50 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn51 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn52 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn53 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn54 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn55 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn56 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn57 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn89 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView20 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn58 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn59 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn60 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView21 = new DevExpress.XtraGrid.Views.Grid.GridView();
            ((System.ComponentModel.ISupportInitialize)(this.gvBroURL)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcBrowse)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvBroFile)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvDowURL)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcDownload)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvDowFile)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvVideoURL)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcVideo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvVideoFile)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView16)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView17)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView18)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcBroDetail)).BeginInit();
            this.ctxMenuReplay.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gvBroDet)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            this.tabPageBrowse.SuspendLayout();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            this.tabControl1.SuspendLayout();
            this.tabPageDL.SuspendLayout();
            this.splitContainer2.Panel1.SuspendLayout();
            this.splitContainer2.Panel2.SuspendLayout();
            this.splitContainer2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcDowDet)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvDowDet)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView13)).BeginInit();
            this.tabPageVideo.SuspendLayout();
            this.splitContainer3.Panel1.SuspendLayout();
            this.splitContainer3.Panel2.SuspendLayout();
            this.splitContainer3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcVideoDet)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvVideoDet)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView20)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView21)).BeginInit();
            this.SuspendLayout();
            // 
            // gvBroURL
            // 
            this.gvBroURL.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn75,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn61,
            this.gridColumn62,
            this.gridColumn63,
            this.gridColumn64});
            this.gvBroURL.GridControl = this.gcBrowse;
            this.gvBroURL.Name = "gvBroURL";
            this.gvBroURL.OptionsBehavior.Editable = false;
            this.gvBroURL.OptionsBehavior.ReadOnly = true;
            this.gvBroURL.OptionsView.ColumnAutoWidth = false;
            this.gvBroURL.OptionsView.ShowGroupPanel = false;
            this.gvBroURL.OptionsView.ShowIndicator = false;
            // 
            // gridColumn75
            // 
            this.gridColumn75.Caption = "序号";
            this.gridColumn75.FieldName = "SN";
            this.gridColumn75.Name = "gridColumn75";
            this.gridColumn75.Visible = true;
            this.gridColumn75.VisibleIndex = 0;
            this.gridColumn75.Width = 53;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "URL";
            this.gridColumn4.FieldName = "URL";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 1;
            this.gridColumn4.Width = 142;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "HTTP登陆尝试次数";
            this.gridColumn5.FieldName = "DisCount";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 2;
            this.gridColumn5.Width = 127;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "HTTP登陆成功次数";
            this.gridColumn6.FieldName = "DisSuc";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 3;
            this.gridColumn6.Width = 122;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "HTTP完全加载次数";
            this.gridColumn17.FieldName = "Complete";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 4;
            this.gridColumn17.Width = 127;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "HTTP登陆成功率";
            this.gridColumn18.DisplayFormat.FormatString = "P2";
            this.gridColumn18.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn18.FieldName = "DisSucRate";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 5;
            this.gridColumn18.Width = 122;
            // 
            // gridColumn61
            // 
            this.gridColumn61.Caption = "HTTP登陆时延(s)";
            this.gridColumn61.FieldName = "DisDelay";
            this.gridColumn61.Name = "gridColumn61";
            this.gridColumn61.Visible = true;
            this.gridColumn61.VisibleIndex = 6;
            this.gridColumn61.Width = 122;
            // 
            // gridColumn62
            // 
            this.gridColumn62.Caption = "HTTP浏览成功率";
            this.gridColumn62.DisplayFormat.FormatString = "P2";
            this.gridColumn62.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn62.FieldName = "SucRate";
            this.gridColumn62.Name = "gridColumn62";
            this.gridColumn62.Visible = true;
            this.gridColumn62.VisibleIndex = 7;
            this.gridColumn62.Width = 122;
            // 
            // gridColumn63
            // 
            this.gridColumn63.Caption = "HTTP浏览时长(s)";
            this.gridColumn63.FieldName = "Time";
            this.gridColumn63.Name = "gridColumn63";
            this.gridColumn63.Visible = true;
            this.gridColumn63.VisibleIndex = 8;
            this.gridColumn63.Width = 122;
            // 
            // gridColumn64
            // 
            this.gridColumn64.Caption = "HTTP应用层下载速率(不含掉线)(kbps)";
            this.gridColumn64.FieldName = "Speed";
            this.gridColumn64.Name = "gridColumn64";
            this.gridColumn64.Visible = true;
            this.gridColumn64.VisibleIndex = 9;
            this.gridColumn64.Width = 230;
            // 
            // gcBrowse
            // 
            this.gcBrowse.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcBrowse.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gcBrowse.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gcBrowse.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gcBrowse.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gcBrowse.EmbeddedNavigator.Buttons.NextPage.Visible = false;
            this.gcBrowse.EmbeddedNavigator.Buttons.PrevPage.Visible = false;
            this.gcBrowse.EmbeddedNavigator.Buttons.Remove.Visible = false;
            gridLevelNode1.LevelTemplate = this.gvBroURL;
            gridLevelNode1.RelationName = "Bros";
            this.gcBrowse.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.gcBrowse.Location = new System.Drawing.Point(0, 0);
            this.gcBrowse.MainView = this.gvBroFile;
            this.gcBrowse.Name = "gcBrowse";
            this.gcBrowse.ShowOnlyPredefinedDetails = true;
            this.gcBrowse.Size = new System.Drawing.Size(996, 353);
            this.gcBrowse.TabIndex = 5;
            this.gcBrowse.UseEmbeddedNavigator = true;
            this.gcBrowse.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvBroFile,
            this.gridView2,
            this.gridView4,
            this.gridView5,
            this.gvBroURL});
            // 
            // gvBroFile
            // 
            this.gvBroFile.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn82,
            this.gridColumn81,
            this.gridColumn78,
            this.gridColumn2,
            this.gridColumn3});
            this.gvBroFile.GridControl = this.gcBrowse;
            this.gvBroFile.Name = "gvBroFile";
            this.gvBroFile.OptionsBehavior.Editable = false;
            this.gvBroFile.OptionsDetail.ShowDetailTabs = false;
            this.gvBroFile.OptionsView.ColumnAutoWidth = false;
            this.gvBroFile.OptionsView.ShowGroupPanel = false;
            this.gvBroFile.OptionsView.ShowIndicator = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "序号";
            this.gridColumn1.FieldName = "SN";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 100;
            // 
            // gridColumn82
            // 
            this.gridColumn82.Caption = "图层类型";
            this.gridColumn82.FieldName = "RegionName";
            this.gridColumn82.Name = "gridColumn82";
            this.gridColumn82.Visible = true;
            this.gridColumn82.VisibleIndex = 1;
            this.gridColumn82.Width = 125;
            // 
            // gridColumn81
            // 
            this.gridColumn81.Caption = "网格信息";
            this.gridColumn81.FieldName = "GridName";
            this.gridColumn81.Name = "gridColumn81";
            this.gridColumn81.Visible = true;
            this.gridColumn81.VisibleIndex = 2;
            this.gridColumn81.Width = 125;
            // 
            // gridColumn78
            // 
            this.gridColumn78.Caption = "地市";
            this.gridColumn78.FieldName = "DistrictName";
            this.gridColumn78.Name = "gridColumn78";
            this.gridColumn78.Visible = true;
            this.gridColumn78.VisibleIndex = 3;
            this.gridColumn78.Width = 125;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "文件名称";
            this.gridColumn2.FieldName = "FileName";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 4;
            this.gridColumn2.Width = 480;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "URL数";
            this.gridColumn3.FieldName = "BroCount";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 5;
            this.gridColumn3.Width = 103;
            // 
            // gridView2
            // 
            this.gridView2.GridControl = this.gcBrowse;
            this.gridView2.Name = "gridView2";
            // 
            // gridView4
            // 
            this.gridView4.GridControl = this.gcBrowse;
            this.gridView4.Name = "gridView4";
            // 
            // gridView5
            // 
            this.gridView5.GridControl = this.gcBrowse;
            this.gridView5.Name = "gridView5";
            // 
            // gvDowURL
            // 
            this.gvDowURL.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn76,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn24,
            this.gridColumn25,
            this.gridColumn65,
            this.gridColumn66,
            this.gridColumn67});
            this.gvDowURL.GridControl = this.gcDownload;
            this.gvDowURL.Name = "gvDowURL";
            this.gvDowURL.OptionsBehavior.Editable = false;
            this.gvDowURL.OptionsBehavior.ReadOnly = true;
            this.gvDowURL.OptionsView.ColumnAutoWidth = false;
            this.gvDowURL.OptionsView.ShowGroupPanel = false;
            this.gvDowURL.OptionsView.ShowIndicator = false;
            // 
            // gridColumn76
            // 
            this.gridColumn76.Caption = "序号";
            this.gridColumn76.FieldName = "SN";
            this.gridColumn76.Name = "gridColumn76";
            this.gridColumn76.Visible = true;
            this.gridColumn76.VisibleIndex = 0;
            this.gridColumn76.Width = 100;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "URL";
            this.gridColumn21.FieldName = "URL";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 1;
            this.gridColumn21.Width = 146;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "HTTP下载尝试次数";
            this.gridColumn22.FieldName = "Dowcount";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 2;
            this.gridColumn22.Width = 122;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "HTTP下载成功次数";
            this.gridColumn23.FieldName = "DowSuc";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 3;
            this.gridColumn23.Width = 122;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "HTTP下载成功率";
            this.gridColumn24.DisplayFormat.FormatString = "P2";
            this.gridColumn24.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn24.FieldName = "DowSucRate";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 4;
            this.gridColumn24.Width = 110;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "HTTP下载掉线次数";
            this.gridColumn25.FieldName = "DowFail";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 5;
            this.gridColumn25.Width = 122;
            // 
            // gridColumn65
            // 
            this.gridColumn65.Caption = "HTTP下载掉线率";
            this.gridColumn65.DisplayFormat.FormatString = "P2";
            this.gridColumn65.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn65.FieldName = "DowFaiRate";
            this.gridColumn65.Name = "gridColumn65";
            this.gridColumn65.Visible = true;
            this.gridColumn65.VisibleIndex = 6;
            this.gridColumn65.Width = 110;
            // 
            // gridColumn66
            // 
            this.gridColumn66.Caption = "HTTP应用层下载速率(不含掉线)(kbps）";
            this.gridColumn66.FieldName = "SucSpeed";
            this.gridColumn66.Name = "gridColumn66";
            this.gridColumn66.Visible = true;
            this.gridColumn66.VisibleIndex = 7;
            this.gridColumn66.Width = 235;
            // 
            // gridColumn67
            // 
            this.gridColumn67.Caption = "HTTP应用层下载速率(含掉线)(kbps)";
            this.gridColumn67.FieldName = "Speed";
            this.gridColumn67.Name = "gridColumn67";
            this.gridColumn67.Visible = true;
            this.gridColumn67.VisibleIndex = 8;
            this.gridColumn67.Width = 230;
            // 
            // gcDownload
            // 
            this.gcDownload.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcDownload.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gcDownload.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gcDownload.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gcDownload.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gcDownload.EmbeddedNavigator.Buttons.NextPage.Visible = false;
            this.gcDownload.EmbeddedNavigator.Buttons.PrevPage.Visible = false;
            this.gcDownload.EmbeddedNavigator.Buttons.Remove.Visible = false;
            gridLevelNode2.LevelTemplate = this.gvDowURL;
            gridLevelNode2.RelationName = "Downs";
            this.gcDownload.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode2});
            this.gcDownload.Location = new System.Drawing.Point(0, 0);
            this.gcDownload.MainView = this.gvDowFile;
            this.gcDownload.Name = "gcDownload";
            this.gcDownload.ShowOnlyPredefinedDetails = true;
            this.gcDownload.Size = new System.Drawing.Size(996, 353);
            this.gcDownload.TabIndex = 5;
            this.gcDownload.UseEmbeddedNavigator = true;
            this.gcDownload.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvDowFile,
            this.gridView8,
            this.gridView9,
            this.gridView10,
            this.gvDowURL});
            // 
            // gvDowFile
            // 
            this.gvDowFile.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn26,
            this.gridColumn83,
            this.gridColumn84,
            this.gridColumn79,
            this.gridColumn27,
            this.gridColumn28});
            this.gvDowFile.GridControl = this.gcDownload;
            this.gvDowFile.Name = "gvDowFile";
            this.gvDowFile.OptionsBehavior.Editable = false;
            this.gvDowFile.OptionsDetail.ShowDetailTabs = false;
            this.gvDowFile.OptionsView.ColumnAutoWidth = false;
            this.gvDowFile.OptionsView.ShowGroupPanel = false;
            this.gvDowFile.OptionsView.ShowIndicator = false;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "序号";
            this.gridColumn26.FieldName = "SN";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 0;
            this.gridColumn26.Width = 100;
            // 
            // gridColumn83
            // 
            this.gridColumn83.Caption = "图层类型";
            this.gridColumn83.FieldName = "RegionName";
            this.gridColumn83.Name = "gridColumn83";
            this.gridColumn83.Visible = true;
            this.gridColumn83.VisibleIndex = 1;
            this.gridColumn83.Width = 125;
            // 
            // gridColumn84
            // 
            this.gridColumn84.Caption = "网格信息";
            this.gridColumn84.FieldName = "GridName";
            this.gridColumn84.Name = "gridColumn84";
            this.gridColumn84.Visible = true;
            this.gridColumn84.VisibleIndex = 2;
            this.gridColumn84.Width = 125;
            // 
            // gridColumn79
            // 
            this.gridColumn79.Caption = "地市";
            this.gridColumn79.FieldName = "DistrictName";
            this.gridColumn79.Name = "gridColumn79";
            this.gridColumn79.Visible = true;
            this.gridColumn79.VisibleIndex = 3;
            this.gridColumn79.Width = 125;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "文件名称";
            this.gridColumn27.FieldName = "FileName";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 4;
            this.gridColumn27.Width = 480;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "URL数";
            this.gridColumn28.FieldName = "DownCount";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 5;
            this.gridColumn28.Width = 103;
            // 
            // gridView8
            // 
            this.gridView8.GridControl = this.gcDownload;
            this.gridView8.Name = "gridView8";
            // 
            // gridView9
            // 
            this.gridView9.GridControl = this.gcDownload;
            this.gridView9.Name = "gridView9";
            // 
            // gridView10
            // 
            this.gridView10.GridControl = this.gcDownload;
            this.gridView10.Name = "gridView10";
            // 
            // gvVideoURL
            // 
            this.gvVideoURL.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn77,
            this.gridColumn41,
            this.gridColumn42,
            this.gridColumn43,
            this.gridColumn44,
            this.gridColumn45,
            this.gridColumn68,
            this.gridColumn69,
            this.gridColumn70,
            this.gridColumn71,
            this.gridColumn72,
            this.gridColumn73,
            this.gridColumn74});
            this.gvVideoURL.GridControl = this.gcVideo;
            this.gvVideoURL.Name = "gvVideoURL";
            this.gvVideoURL.OptionsBehavior.Editable = false;
            this.gvVideoURL.OptionsBehavior.ReadOnly = true;
            this.gvVideoURL.OptionsView.ColumnAutoWidth = false;
            this.gvVideoURL.OptionsView.ShowGroupPanel = false;
            this.gvVideoURL.OptionsView.ShowIndicator = false;
            // 
            // gridColumn77
            // 
            this.gridColumn77.Caption = "序号";
            this.gridColumn77.FieldName = "SN";
            this.gridColumn77.Name = "gridColumn77";
            this.gridColumn77.Visible = true;
            this.gridColumn77.VisibleIndex = 0;
            this.gridColumn77.Width = 53;
            // 
            // gridColumn41
            // 
            this.gridColumn41.Caption = "URL";
            this.gridColumn41.FieldName = "URL";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.Visible = true;
            this.gridColumn41.VisibleIndex = 1;
            this.gridColumn41.Width = 146;
            // 
            // gridColumn42
            // 
            this.gridColumn42.Caption = "流媒体业务发起次数";
            this.gridColumn42.FieldName = "ReqCount";
            this.gridColumn42.Name = "gridColumn42";
            this.gridColumn42.Visible = true;
            this.gridColumn42.VisibleIndex = 2;
            this.gridColumn42.Width = 130;
            // 
            // gridColumn43
            // 
            this.gridColumn43.Caption = "流媒体业务成功次数";
            this.gridColumn43.FieldName = "SucCount";
            this.gridColumn43.Name = "gridColumn43";
            this.gridColumn43.Visible = true;
            this.gridColumn43.VisibleIndex = 3;
            this.gridColumn43.Width = 130;
            // 
            // gridColumn44
            // 
            this.gridColumn44.Caption = "流媒体业务成功率";
            this.gridColumn44.DisplayFormat.FormatString = "P2";
            this.gridColumn44.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn44.FieldName = "SucRate";
            this.gridColumn44.Name = "gridColumn44";
            this.gridColumn44.Visible = true;
            this.gridColumn44.VisibleIndex = 4;
            this.gridColumn44.Width = 120;
            // 
            // gridColumn45
            // 
            this.gridColumn45.Caption = "流媒体加载时延(s)";
            this.gridColumn45.FieldName = "Delay";
            this.gridColumn45.Name = "gridColumn45";
            this.gridColumn45.Visible = true;
            this.gridColumn45.VisibleIndex = 5;
            this.gridColumn45.Width = 122;
            // 
            // gridColumn68
            // 
            this.gridColumn68.Caption = "流媒体时长（s）";
            this.gridColumn68.FieldName = "Time";
            this.gridColumn68.Name = "gridColumn68";
            this.gridColumn68.Visible = true;
            this.gridColumn68.VisibleIndex = 6;
            this.gridColumn68.Width = 115;
            // 
            // gridColumn69
            // 
            this.gridColumn69.Caption = "流媒体卡顿时长(s)";
            this.gridColumn69.FieldName = "RebufferTime";
            this.gridColumn69.Name = "gridColumn69";
            this.gridColumn69.Visible = true;
            this.gridColumn69.VisibleIndex = 7;
            this.gridColumn69.Width = 130;
            // 
            // gridColumn70
            // 
            this.gridColumn70.Caption = "流媒体播放总时长（s）";
            this.gridColumn70.FieldName = "PlayTime";
            this.gridColumn70.Name = "gridColumn70";
            this.gridColumn70.Visible = true;
            this.gridColumn70.VisibleIndex = 8;
            this.gridColumn70.Width = 145;
            // 
            // gridColumn71
            // 
            this.gridColumn71.Caption = "流媒体播放卡顿次数";
            this.gridColumn71.FieldName = "RebufferCount";
            this.gridColumn71.Name = "gridColumn71";
            this.gridColumn71.Visible = true;
            this.gridColumn71.VisibleIndex = 9;
            this.gridColumn71.Width = 130;
            // 
            // gridColumn72
            // 
            this.gridColumn72.Caption = "流媒体播放超时比例";
            this.gridColumn72.DisplayFormat.FormatString = "P2";
            this.gridColumn72.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn72.FieldName = "TimeoutRate";
            this.gridColumn72.Name = "gridColumn72";
            this.gridColumn72.Visible = true;
            this.gridColumn72.VisibleIndex = 10;
            this.gridColumn72.Width = 130;
            // 
            // gridColumn73
            // 
            this.gridColumn73.Caption = "应用层下载速率(不含掉线)(kbps)";
            this.gridColumn73.FieldName = "DownSpeed";
            this.gridColumn73.Name = "gridColumn73";
            this.gridColumn73.Visible = true;
            this.gridColumn73.VisibleIndex = 11;
            this.gridColumn73.Width = 210;
            // 
            // gridColumn74
            // 
            this.gridColumn74.Caption = "流媒体加载速率(kbps)";
            this.gridColumn74.FieldName = "LoadSpeed";
            this.gridColumn74.Name = "gridColumn74";
            this.gridColumn74.Visible = true;
            this.gridColumn74.VisibleIndex = 12;
            this.gridColumn74.Width = 160;
            // 
            // gcVideo
            // 
            this.gcVideo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcVideo.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gcVideo.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gcVideo.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gcVideo.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gcVideo.EmbeddedNavigator.Buttons.NextPage.Visible = false;
            this.gcVideo.EmbeddedNavigator.Buttons.PrevPage.Visible = false;
            this.gcVideo.EmbeddedNavigator.Buttons.Remove.Visible = false;
            gridLevelNode3.LevelTemplate = this.gvVideoURL;
            gridLevelNode3.RelationName = "Videos";
            this.gcVideo.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode3});
            this.gcVideo.Location = new System.Drawing.Point(0, 0);
            this.gcVideo.MainView = this.gvVideoFile;
            this.gcVideo.Name = "gcVideo";
            this.gcVideo.ShowOnlyPredefinedDetails = true;
            this.gcVideo.Size = new System.Drawing.Size(996, 353);
            this.gcVideo.TabIndex = 5;
            this.gcVideo.UseEmbeddedNavigator = true;
            this.gcVideo.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvVideoFile,
            this.gridView16,
            this.gridView17,
            this.gridView18,
            this.gvVideoURL});
            // 
            // gvVideoFile
            // 
            this.gvVideoFile.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn46,
            this.gridColumn85,
            this.gridColumn86,
            this.gridColumn80,
            this.gridColumn47,
            this.gridColumn48});
            this.gvVideoFile.GridControl = this.gcVideo;
            this.gvVideoFile.Name = "gvVideoFile";
            this.gvVideoFile.OptionsBehavior.Editable = false;
            this.gvVideoFile.OptionsDetail.ShowDetailTabs = false;
            this.gvVideoFile.OptionsView.ColumnAutoWidth = false;
            this.gvVideoFile.OptionsView.ShowGroupPanel = false;
            this.gvVideoFile.OptionsView.ShowIndicator = false;
            // 
            // gridColumn46
            // 
            this.gridColumn46.Caption = "序号";
            this.gridColumn46.FieldName = "SN";
            this.gridColumn46.Name = "gridColumn46";
            this.gridColumn46.Visible = true;
            this.gridColumn46.VisibleIndex = 0;
            this.gridColumn46.Width = 100;
            // 
            // gridColumn85
            // 
            this.gridColumn85.Caption = "图层类型";
            this.gridColumn85.FieldName = "RegionName";
            this.gridColumn85.Name = "gridColumn85";
            this.gridColumn85.Visible = true;
            this.gridColumn85.VisibleIndex = 1;
            this.gridColumn85.Width = 125;
            // 
            // gridColumn86
            // 
            this.gridColumn86.Caption = "网格信息";
            this.gridColumn86.FieldName = "GridName";
            this.gridColumn86.Name = "gridColumn86";
            this.gridColumn86.Visible = true;
            this.gridColumn86.VisibleIndex = 2;
            this.gridColumn86.Width = 125;
            // 
            // gridColumn80
            // 
            this.gridColumn80.Caption = "地市";
            this.gridColumn80.FieldName = "DistrictName";
            this.gridColumn80.Name = "gridColumn80";
            this.gridColumn80.Visible = true;
            this.gridColumn80.VisibleIndex = 3;
            this.gridColumn80.Width = 125;
            // 
            // gridColumn47
            // 
            this.gridColumn47.Caption = "文件名称";
            this.gridColumn47.FieldName = "FileName";
            this.gridColumn47.Name = "gridColumn47";
            this.gridColumn47.Visible = true;
            this.gridColumn47.VisibleIndex = 4;
            this.gridColumn47.Width = 480;
            // 
            // gridColumn48
            // 
            this.gridColumn48.Caption = "URL数";
            this.gridColumn48.FieldName = "VideoCount";
            this.gridColumn48.Name = "gridColumn48";
            this.gridColumn48.Visible = true;
            this.gridColumn48.VisibleIndex = 5;
            this.gridColumn48.Width = 103;
            // 
            // gridView16
            // 
            this.gridView16.GridControl = this.gcVideo;
            this.gridView16.Name = "gridView16";
            // 
            // gridView17
            // 
            this.gridView17.GridControl = this.gcVideo;
            this.gridView17.Name = "gridView17";
            // 
            // gridView18
            // 
            this.gridView18.GridControl = this.gcVideo;
            this.gridView18.Name = "gridView18";
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(139, 26);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(138, 22);
            this.miExportExcel.Text = "导出Excel...";
            // 
            // gridView1
            // 
            this.gridView1.GridControl = this.gcBroDetail;
            this.gridView1.Name = "gridView1";
            // 
            // gcBroDetail
            // 
            this.gcBroDetail.ContextMenuStrip = this.ctxMenuReplay;
            this.gcBroDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcBroDetail.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gcBroDetail.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gcBroDetail.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gcBroDetail.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gcBroDetail.EmbeddedNavigator.Buttons.NextPage.Visible = false;
            this.gcBroDetail.EmbeddedNavigator.Buttons.PrevPage.Visible = false;
            this.gcBroDetail.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gcBroDetail.Location = new System.Drawing.Point(0, 0);
            this.gcBroDetail.MainView = this.gvBroDet;
            this.gcBroDetail.Name = "gcBroDetail";
            this.gcBroDetail.ShowOnlyPredefinedDetails = true;
            this.gcBroDetail.Size = new System.Drawing.Size(996, 165);
            this.gcBroDetail.TabIndex = 6;
            this.gcBroDetail.UseEmbeddedNavigator = true;
            this.gcBroDetail.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvBroDet,
            this.gridView3,
            this.gridView1});
            // 
            // ctxMenuReplay
            // 
            this.ctxMenuReplay.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplayFile});
            this.ctxMenuReplay.Name = "contextMenuStrip1";
            this.ctxMenuReplay.Size = new System.Drawing.Size(134, 26);
            // 
            // miReplayFile
            // 
            this.miReplayFile.Name = "miReplayFile";
            this.miReplayFile.Size = new System.Drawing.Size(133, 22);
            this.miReplayFile.Text = "回放文件...";
            this.miReplayFile.Click += new System.EventHandler(this.miReplayFile_Click);
            // 
            // gvBroDet
            // 
            this.gvBroDet.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn87,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn19,
            this.gridColumn20});
            this.gvBroDet.GridControl = this.gcBroDetail;
            this.gvBroDet.Name = "gvBroDet";
            this.gvBroDet.OptionsBehavior.Editable = false;
            this.gvBroDet.OptionsDetail.ShowDetailTabs = false;
            this.gvBroDet.OptionsView.ShowGroupPanel = false;
            this.gvBroDet.OptionsView.ShowIndicator = false;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "序号";
            this.gridColumn10.FieldName = "SN";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 0;
            this.gridColumn10.Width = 49;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "URL";
            this.gridColumn11.FieldName = "URL";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 1;
            this.gridColumn11.Width = 176;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "是否失败";
            this.gridColumn12.FieldName = "IsFail";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 2;
            this.gridColumn12.Width = 84;
            // 
            // gridColumn87
            // 
            this.gridColumn87.Caption = "浏览时长(S)";
            this.gridColumn87.FieldName = "TimeSpan";
            this.gridColumn87.Name = "gridColumn87";
            this.gridColumn87.Visible = true;
            this.gridColumn87.VisibleIndex = 3;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "开始时间";
            this.gridColumn13.FieldName = "StartTime";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 4;
            this.gridColumn13.Width = 104;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "结束时间";
            this.gridColumn14.FieldName = "EndTime";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 5;
            this.gridColumn14.Width = 104;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "开始事件名称";
            this.gridColumn15.FieldName = "StartName";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 6;
            this.gridColumn15.Width = 111;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "结束事件名称";
            this.gridColumn16.FieldName = "EvtEndName";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 7;
            this.gridColumn16.Width = 111;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "失败原因";
            this.gridColumn19.FieldName = "FailReason";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 8;
            this.gridColumn19.Width = 135;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "业务测试字节数";
            this.gridColumn20.FieldName = "Bytes";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 9;
            this.gridColumn20.Width = 158;
            // 
            // gridView3
            // 
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9});
            this.gridView3.GridControl = this.gcBroDetail;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsBehavior.Editable = false;
            this.gridView3.OptionsBehavior.ReadOnly = true;
            this.gridView3.OptionsView.ColumnAutoWidth = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            this.gridView3.OptionsView.ShowIndicator = false;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "切换序号";
            this.gridColumn7.FieldName = "SN";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 0;
            this.gridColumn7.Width = 112;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "切换小区";
            this.gridColumn8.FieldName = "Desc";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 1;
            this.gridColumn8.Width = 343;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "切换间隔(秒)";
            this.gridColumn9.FieldName = "Interval";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 2;
            this.gridColumn9.Width = 123;
            // 
            // tabPageBrowse
            // 
            this.tabPageBrowse.Controls.Add(this.splitContainer1);
            this.tabPageBrowse.Location = new System.Drawing.Point(4, 23);
            this.tabPageBrowse.Name = "tabPageBrowse";
            this.tabPageBrowse.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageBrowse.Size = new System.Drawing.Size(1002, 528);
            this.tabPageBrowse.TabIndex = 0;
            this.tabPageBrowse.Text = "Http浏览";
            this.tabPageBrowse.UseVisualStyleBackColor = true;
            // 
            // splitContainer1
            // 
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.Location = new System.Drawing.Point(3, 3);
            this.splitContainer1.Name = "splitContainer1";
            this.splitContainer1.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.gcBrowse);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.gcBroDetail);
            this.splitContainer1.Size = new System.Drawing.Size(996, 522);
            this.splitContainer1.SplitterDistance = 353;
            this.splitContainer1.TabIndex = 1;
            // 
            // tabControl1
            // 
            this.tabControl1.ContextMenuStrip = this.contextMenuStrip1;
            this.tabControl1.Controls.Add(this.tabPageBrowse);
            this.tabControl1.Controls.Add(this.tabPageDL);
            this.tabControl1.Controls.Add(this.tabPageVideo);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Location = new System.Drawing.Point(0, 0);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(1010, 555);
            this.tabControl1.TabIndex = 4;
            this.tabControl1.Tag = "";
            // 
            // tabPageDL
            // 
            this.tabPageDL.Controls.Add(this.splitContainer2);
            this.tabPageDL.Location = new System.Drawing.Point(4, 23);
            this.tabPageDL.Name = "tabPageDL";
            this.tabPageDL.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageDL.Size = new System.Drawing.Size(1002, 528);
            this.tabPageDL.TabIndex = 2;
            this.tabPageDL.Text = "Http下载";
            this.tabPageDL.UseVisualStyleBackColor = true;
            // 
            // splitContainer2
            // 
            this.splitContainer2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer2.Location = new System.Drawing.Point(3, 3);
            this.splitContainer2.Name = "splitContainer2";
            this.splitContainer2.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer2.Panel1
            // 
            this.splitContainer2.Panel1.Controls.Add(this.gcDownload);
            // 
            // splitContainer2.Panel2
            // 
            this.splitContainer2.Panel2.Controls.Add(this.gcDowDet);
            this.splitContainer2.Size = new System.Drawing.Size(996, 522);
            this.splitContainer2.SplitterDistance = 353;
            this.splitContainer2.TabIndex = 1;
            // 
            // gcDowDet
            // 
            this.gcDowDet.ContextMenuStrip = this.ctxMenuReplay;
            this.gcDowDet.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcDowDet.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gcDowDet.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gcDowDet.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gcDowDet.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gcDowDet.EmbeddedNavigator.Buttons.NextPage.Visible = false;
            this.gcDowDet.EmbeddedNavigator.Buttons.PrevPage.Visible = false;
            this.gcDowDet.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gcDowDet.Location = new System.Drawing.Point(0, 0);
            this.gcDowDet.MainView = this.gvDowDet;
            this.gcDowDet.Name = "gcDowDet";
            this.gcDowDet.ShowOnlyPredefinedDetails = true;
            this.gcDowDet.Size = new System.Drawing.Size(996, 165);
            this.gcDowDet.TabIndex = 6;
            this.gcDowDet.UseEmbeddedNavigator = true;
            this.gcDowDet.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvDowDet,
            this.gridView12,
            this.gridView13});
            // 
            // gvDowDet
            // 
            this.gvDowDet.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn29,
            this.gridColumn30,
            this.gridColumn31,
            this.gridColumn88,
            this.gridColumn32,
            this.gridColumn33,
            this.gridColumn34,
            this.gridColumn35,
            this.gridColumn36,
            this.gridColumn37});
            this.gvDowDet.GridControl = this.gcDowDet;
            this.gvDowDet.Name = "gvDowDet";
            this.gvDowDet.OptionsBehavior.Editable = false;
            this.gvDowDet.OptionsDetail.ShowDetailTabs = false;
            this.gvDowDet.OptionsView.ShowGroupPanel = false;
            this.gvDowDet.OptionsView.ShowIndicator = false;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "序号";
            this.gridColumn29.FieldName = "SN";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 0;
            this.gridColumn29.Width = 49;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "URL";
            this.gridColumn30.FieldName = "URL";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 1;
            this.gridColumn30.Width = 176;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "是否失败";
            this.gridColumn31.FieldName = "IsFail";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 2;
            this.gridColumn31.Width = 84;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "开始时间";
            this.gridColumn32.FieldName = "StartTime";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 4;
            this.gridColumn32.Width = 104;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "结束时间";
            this.gridColumn33.FieldName = "EndTime";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 5;
            this.gridColumn33.Width = 104;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "开始事件名称";
            this.gridColumn34.FieldName = "StartName";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 6;
            this.gridColumn34.Width = 111;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "结束事件名称";
            this.gridColumn35.FieldName = "EvtEndName";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 7;
            this.gridColumn35.Width = 111;
            // 
            // gridColumn36
            // 
            this.gridColumn36.Caption = "失败原因";
            this.gridColumn36.FieldName = "FailReason";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.Visible = true;
            this.gridColumn36.VisibleIndex = 8;
            this.gridColumn36.Width = 135;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "业务测试字节数";
            this.gridColumn37.FieldName = "Bytes";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.Visible = true;
            this.gridColumn37.VisibleIndex = 9;
            this.gridColumn37.Width = 112;
            // 
            // gridColumn88
            // 
            this.gridColumn88.Caption = "下载时长(S)";
            this.gridColumn88.FieldName = "TimeSpan";
            this.gridColumn88.Name = "gridColumn88";
            this.gridColumn88.Visible = true;
            this.gridColumn88.VisibleIndex = 3;
            // 
            // gridView12
            // 
            this.gridView12.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn38,
            this.gridColumn39,
            this.gridColumn40});
            this.gridView12.GridControl = this.gcDowDet;
            this.gridView12.Name = "gridView12";
            this.gridView12.OptionsBehavior.Editable = false;
            this.gridView12.OptionsBehavior.ReadOnly = true;
            this.gridView12.OptionsView.ColumnAutoWidth = false;
            this.gridView12.OptionsView.ShowGroupPanel = false;
            this.gridView12.OptionsView.ShowIndicator = false;
            // 
            // gridColumn38
            // 
            this.gridColumn38.Caption = "切换序号";
            this.gridColumn38.FieldName = "SN";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.Visible = true;
            this.gridColumn38.VisibleIndex = 0;
            this.gridColumn38.Width = 112;
            // 
            // gridColumn39
            // 
            this.gridColumn39.Caption = "切换小区";
            this.gridColumn39.FieldName = "Desc";
            this.gridColumn39.Name = "gridColumn39";
            this.gridColumn39.Visible = true;
            this.gridColumn39.VisibleIndex = 1;
            this.gridColumn39.Width = 343;
            // 
            // gridColumn40
            // 
            this.gridColumn40.Caption = "切换间隔(秒)";
            this.gridColumn40.FieldName = "Interval";
            this.gridColumn40.Name = "gridColumn40";
            this.gridColumn40.Visible = true;
            this.gridColumn40.VisibleIndex = 2;
            this.gridColumn40.Width = 123;
            // 
            // gridView13
            // 
            this.gridView13.GridControl = this.gcDowDet;
            this.gridView13.Name = "gridView13";
            // 
            // tabPageVideo
            // 
            this.tabPageVideo.Controls.Add(this.splitContainer3);
            this.tabPageVideo.Location = new System.Drawing.Point(4, 23);
            this.tabPageVideo.Name = "tabPageVideo";
            this.tabPageVideo.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageVideo.Size = new System.Drawing.Size(1002, 528);
            this.tabPageVideo.TabIndex = 3;
            this.tabPageVideo.Text = "流媒体";
            this.tabPageVideo.UseVisualStyleBackColor = true;
            // 
            // splitContainer3
            // 
            this.splitContainer3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer3.Location = new System.Drawing.Point(3, 3);
            this.splitContainer3.Name = "splitContainer3";
            this.splitContainer3.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer3.Panel1
            // 
            this.splitContainer3.Panel1.Controls.Add(this.gcVideo);
            // 
            // splitContainer3.Panel2
            // 
            this.splitContainer3.Panel2.Controls.Add(this.gcVideoDet);
            this.splitContainer3.Size = new System.Drawing.Size(996, 522);
            this.splitContainer3.SplitterDistance = 353;
            this.splitContainer3.TabIndex = 1;
            // 
            // gcVideoDet
            // 
            this.gcVideoDet.ContextMenuStrip = this.ctxMenuReplay;
            this.gcVideoDet.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcVideoDet.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gcVideoDet.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gcVideoDet.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gcVideoDet.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gcVideoDet.EmbeddedNavigator.Buttons.NextPage.Visible = false;
            this.gcVideoDet.EmbeddedNavigator.Buttons.PrevPage.Visible = false;
            this.gcVideoDet.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gcVideoDet.Location = new System.Drawing.Point(0, 0);
            this.gcVideoDet.MainView = this.gvVideoDet;
            this.gcVideoDet.Name = "gcVideoDet";
            this.gcVideoDet.ShowOnlyPredefinedDetails = true;
            this.gcVideoDet.Size = new System.Drawing.Size(996, 165);
            this.gcVideoDet.TabIndex = 6;
            this.gcVideoDet.UseEmbeddedNavigator = true;
            this.gcVideoDet.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvVideoDet,
            this.gridView20,
            this.gridView21});
            // 
            // gvVideoDet
            // 
            this.gvVideoDet.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn49,
            this.gridColumn50,
            this.gridColumn51,
            this.gridColumn89,
            this.gridColumn52,
            this.gridColumn53,
            this.gridColumn54,
            this.gridColumn55,
            this.gridColumn56,
            this.gridColumn57});
            this.gvVideoDet.GridControl = this.gcVideoDet;
            this.gvVideoDet.Name = "gvVideoDet";
            this.gvVideoDet.OptionsBehavior.Editable = false;
            this.gvVideoDet.OptionsDetail.ShowDetailTabs = false;
            this.gvVideoDet.OptionsView.ShowGroupPanel = false;
            this.gvVideoDet.OptionsView.ShowIndicator = false;
            // 
            // gridColumn49
            // 
            this.gridColumn49.Caption = "序号";
            this.gridColumn49.FieldName = "SN";
            this.gridColumn49.Name = "gridColumn49";
            this.gridColumn49.Visible = true;
            this.gridColumn49.VisibleIndex = 0;
            this.gridColumn49.Width = 49;
            // 
            // gridColumn50
            // 
            this.gridColumn50.Caption = "URL";
            this.gridColumn50.FieldName = "URL";
            this.gridColumn50.Name = "gridColumn50";
            this.gridColumn50.Visible = true;
            this.gridColumn50.VisibleIndex = 1;
            this.gridColumn50.Width = 176;
            // 
            // gridColumn51
            // 
            this.gridColumn51.Caption = "是否失败";
            this.gridColumn51.FieldName = "IsFail";
            this.gridColumn51.Name = "gridColumn51";
            this.gridColumn51.Visible = true;
            this.gridColumn51.VisibleIndex = 2;
            this.gridColumn51.Width = 84;
            // 
            // gridColumn52
            // 
            this.gridColumn52.Caption = "开始时间";
            this.gridColumn52.FieldName = "StartTime";
            this.gridColumn52.Name = "gridColumn52";
            this.gridColumn52.Visible = true;
            this.gridColumn52.VisibleIndex = 4;
            this.gridColumn52.Width = 104;
            // 
            // gridColumn53
            // 
            this.gridColumn53.Caption = "结束时间";
            this.gridColumn53.FieldName = "EndTime";
            this.gridColumn53.Name = "gridColumn53";
            this.gridColumn53.Visible = true;
            this.gridColumn53.VisibleIndex = 5;
            this.gridColumn53.Width = 104;
            // 
            // gridColumn54
            // 
            this.gridColumn54.Caption = "开始事件名称";
            this.gridColumn54.FieldName = "StartName";
            this.gridColumn54.Name = "gridColumn54";
            this.gridColumn54.Visible = true;
            this.gridColumn54.VisibleIndex = 6;
            this.gridColumn54.Width = 111;
            // 
            // gridColumn55
            // 
            this.gridColumn55.Caption = "结束事件名称";
            this.gridColumn55.FieldName = "EvtEndName";
            this.gridColumn55.Name = "gridColumn55";
            this.gridColumn55.Visible = true;
            this.gridColumn55.VisibleIndex = 7;
            this.gridColumn55.Width = 111;
            // 
            // gridColumn56
            // 
            this.gridColumn56.Caption = "失败原因";
            this.gridColumn56.FieldName = "FailReason";
            this.gridColumn56.Name = "gridColumn56";
            this.gridColumn56.Visible = true;
            this.gridColumn56.VisibleIndex = 8;
            this.gridColumn56.Width = 135;
            // 
            // gridColumn57
            // 
            this.gridColumn57.Caption = "业务测试字节数";
            this.gridColumn57.Name = "gridColumn57";
            this.gridColumn57.Visible = true;
            this.gridColumn57.VisibleIndex = 9;
            this.gridColumn57.Width = 112;
            // 
            // gridColumn89
            // 
            this.gridColumn89.Caption = "时长(S)";
            this.gridColumn89.FieldName = "TimeSpan";
            this.gridColumn89.Name = "gridColumn89";
            this.gridColumn89.Visible = true;
            this.gridColumn89.VisibleIndex = 3;
            // 
            // gridView20
            // 
            this.gridView20.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn58,
            this.gridColumn59,
            this.gridColumn60});
            this.gridView20.GridControl = this.gcVideoDet;
            this.gridView20.Name = "gridView20";
            this.gridView20.OptionsBehavior.Editable = false;
            this.gridView20.OptionsBehavior.ReadOnly = true;
            this.gridView20.OptionsView.ColumnAutoWidth = false;
            this.gridView20.OptionsView.ShowGroupPanel = false;
            this.gridView20.OptionsView.ShowIndicator = false;
            // 
            // gridColumn58
            // 
            this.gridColumn58.Caption = "切换序号";
            this.gridColumn58.FieldName = "SN";
            this.gridColumn58.Name = "gridColumn58";
            this.gridColumn58.Visible = true;
            this.gridColumn58.VisibleIndex = 0;
            this.gridColumn58.Width = 112;
            // 
            // gridColumn59
            // 
            this.gridColumn59.Caption = "切换小区";
            this.gridColumn59.FieldName = "Desc";
            this.gridColumn59.Name = "gridColumn59";
            this.gridColumn59.Visible = true;
            this.gridColumn59.VisibleIndex = 1;
            this.gridColumn59.Width = 343;
            // 
            // gridColumn60
            // 
            this.gridColumn60.Caption = "切换间隔(秒)";
            this.gridColumn60.FieldName = "Interval";
            this.gridColumn60.Name = "gridColumn60";
            this.gridColumn60.Visible = true;
            this.gridColumn60.VisibleIndex = 2;
            this.gridColumn60.Width = 123;
            // 
            // gridView21
            // 
            this.gridView21.GridControl = this.gcVideoDet;
            this.gridView21.Name = "gridView21";
            // 
            // LteURLAnaForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1010, 555);
            this.Controls.Add(this.tabControl1);
            this.Name = "LteURLAnaForm";
            this.Text = "URL统计分析";
            ((System.ComponentModel.ISupportInitialize)(this.gvBroURL)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcBrowse)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvBroFile)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvDowURL)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcDownload)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvDowFile)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvVideoURL)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcVideo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvVideoFile)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView16)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView17)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView18)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcBroDetail)).EndInit();
            this.ctxMenuReplay.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gvBroDet)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            this.tabPageBrowse.ResumeLayout(false);
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            this.splitContainer1.ResumeLayout(false);
            this.tabControl1.ResumeLayout(false);
            this.tabPageDL.ResumeLayout(false);
            this.splitContainer2.Panel1.ResumeLayout(false);
            this.splitContainer2.Panel2.ResumeLayout(false);
            this.splitContainer2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcDowDet)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvDowDet)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView13)).EndInit();
            this.tabPageVideo.ResumeLayout(false);
            this.splitContainer3.Panel1.ResumeLayout(false);
            this.splitContainer3.Panel2.ResumeLayout(false);
            this.splitContainer3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcVideoDet)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvVideoDet)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView20)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView21)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.GridControl gcBroDetail;
        private DevExpress.XtraGrid.Views.Grid.GridView gvBroDet;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.GridControl gcBrowse;
        private DevExpress.XtraGrid.Views.Grid.GridView gvBroURL;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Views.Grid.GridView gvBroFile;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView4;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView5;
        private System.Windows.Forms.TabPage tabPageBrowse;
        private System.Windows.Forms.SplitContainer splitContainer1;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tabPageDL;
        private System.Windows.Forms.SplitContainer splitContainer2;
        private DevExpress.XtraGrid.GridControl gcDownload;
        private DevExpress.XtraGrid.Views.Grid.GridView gvDowURL;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Views.Grid.GridView gvDowFile;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView8;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView9;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView10;
        private DevExpress.XtraGrid.GridControl gcDowDet;
        private DevExpress.XtraGrid.Views.Grid.GridView gvDowDet;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn39;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn40;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView13;
        private System.Windows.Forms.TabPage tabPageVideo;
        private System.Windows.Forms.SplitContainer splitContainer3;
        private DevExpress.XtraGrid.GridControl gcVideo;
        private DevExpress.XtraGrid.Views.Grid.GridView gvVideoURL;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn44;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn45;
        private DevExpress.XtraGrid.Views.Grid.GridView gvVideoFile;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn46;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn47;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn48;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView16;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView17;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView18;
        private DevExpress.XtraGrid.GridControl gcVideoDet;
        private DevExpress.XtraGrid.Views.Grid.GridView gvVideoDet;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn49;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn50;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn51;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn52;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn53;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn54;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn55;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn56;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn57;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn58;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn59;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn60;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn61;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn62;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn63;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn64;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn65;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn66;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn67;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn68;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn69;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn70;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn71;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn72;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn73;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn74;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn75;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn76;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn77;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn78;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn79;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn80;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn82;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn81;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn83;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn84;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn85;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn86;
        private System.Windows.Forms.ContextMenuStrip ctxMenuReplay;
        private System.Windows.Forms.ToolStripMenuItem miReplayFile;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn87;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn88;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn89;
    }
}