﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.Utils;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class XtraTDIPerformanceForm : DevExpress.XtraEditors.XtraForm
    {
        public XtraTDIPerformanceForm()
        {
            InitializeComponent();
        }

        public void setdata(DataTable datatable)
        {
            this.gridControl1.DataSource = datatable;

            for (int i = 0; i < 3; i++)
            {
                gridView1.VisibleColumns[i].Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            }

            gridView1.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;  //列头自动换行
            gridView1.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;  //dev的bug  必须设置trimming为None

            gridView1.ColumnPanelRowHeight = 50;       //必须设置列头高度 否则不会换行
            gridView1.Columns[0].Width = 150;
            gridView1.Columns[0].DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            gridView1.Columns[0].DisplayFormat.FormatType = FormatType.DateTime;

            gridView1.Columns[0].GroupFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            gridView1.Columns[0].GroupFormat.FormatType = FormatType.DateTime;
        }

        //判断颜色
        private void gridView1_RowCellStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs e)
        {
            if (e.Column.FieldName == "上行码资源利用率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["上行码资源利用率"]);
                if (Convert.ToDouble(aa) >0.55)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "下行码资源利用率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["下行码资源利用率"]);
                if (Convert.ToDouble(aa) > 0.6)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "RLC重传率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["RLC重传率"]);
                if (Convert.ToDouble(aa) > 0.1)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }


            if (e.Column.FieldName == "误块率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["误块率"]);
                if (Convert.ToDouble(aa) > 0.01)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "CS域误块率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["CS域误块率"]);
                if (Convert.ToDouble(aa) > 0.05)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "PS域误块率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["PS域误块率"]);
                if (Convert.ToDouble(aa) > 0.25)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "PS域RAB拥塞率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["PS域RAB拥塞率"]);
                if (Convert.ToDouble(aa) > 0.02)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "RRC拥塞率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["RRC拥塞率"]);
                if (Convert.ToDouble(aa) > 0.5)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "CS域RAB拥塞率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["CS域RAB拥塞率"]);
                if (Convert.ToDouble(aa) > 0.02)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "寻呼拥塞率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["寻呼拥塞率"]);
                if (Convert.ToDouble(aa) > 0.01)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "CS域RAB建立成功率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["CS域RAB建立成功率"]);
                if (Convert.ToDouble(aa) <0.98)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "CS域RRC连接建立成功率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["CS域RRC连接建立成功率"]);
                if (Convert.ToDouble(aa) <0.98)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "CS域无线接通率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["CS域无线接通率"]);
                if (Convert.ToDouble(aa) < 0.98)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "PS域RAB建立成功率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["PS域RAB建立成功率"]);
                if (Convert.ToDouble(aa) < 0.98)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "PS域RRC连接建立成功率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["PS域RRC连接建立成功率"]);
                if (Convert.ToDouble(aa) < 0.98)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "PS域无线接通率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns["PS域无线接通率"]);
                if (Convert.ToDouble(aa) < 0.98)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }//

            if (e.Column.FieldName == "HSDPA信道建立成功率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns[""]);
                if (Convert.ToDouble(aa) <0.97)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "语音业务无线掉话率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns[""]);
                if (Convert.ToDouble(aa) > 0.02)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "PS域无线掉线率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns[""]);
                if (Convert.ToDouble(aa) > 0.03)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "HSDPA信道异常释放率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns[""]);
                if (Convert.ToDouble(aa) > 0.3)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "接力切换成功率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns[""]);
                if (Convert.ToDouble(aa) <0.98)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "RNC内同频接力切换成功率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns[""]);
                if (Convert.ToDouble(aa) < 0.98)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "RNC内异频接力切换成功率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns[""]);
                if (Convert.ToDouble(aa) < 0.98)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }//

            if (e.Column.FieldName == "异频硬切换成功率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns[""]);
                if (Convert.ToDouble(aa) <0.95)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "同频硬切换成功率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns[""]);
                if (Convert.ToDouble(aa) < 0.95)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "系统间CS域切换成功率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns[""]);
                if (Convert.ToDouble(aa) < 0.95)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "系统间PS域切换出成功率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns[""]);
                if (Convert.ToDouble(aa) < 0.95)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "电路域系统间切换平均时长")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns[""]);
                if (Convert.ToDouble(aa) > 900)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "系统间切换占比")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns[""]);
                if (Convert.ToDouble(aa) > 0.6)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "小区间切换出成功率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns[""]);
                if (Convert.ToDouble(aa) < 0.95)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "小区间切换入成功率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns[""]);
                if (Convert.ToDouble(aa) < 0.95)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }

            if (e.Column.FieldName == "TD网内小区间切换成功率")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns[""]);
                if (Convert.ToDouble(aa) < 0.95)
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }


           
        }

    }
}