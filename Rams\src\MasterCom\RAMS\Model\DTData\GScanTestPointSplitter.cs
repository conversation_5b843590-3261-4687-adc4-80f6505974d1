﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model
{
    public static class GScanTestPointSplitter
    {
        public static List<int> GetSpecificBandIndex(TestPoint testPoint, GSMFreqBandType bandType)
        {
            List<int> idxSet = null;
            if (bandType == GSMFreqBandType.GSM900)
            {
                idxSet = Get900IndexOnly(testPoint);
            }
            else if (bandType == GSMFreqBandType.DSC1800)
            {
                idxSet = Get1800IndexOnly(testPoint);
            }
            else
            {
                idxSet = new List<int>();
                for (int i = 0; i < 50; i++)
                {
                    idxSet.Add(i);
                }
            }
            return idxSet;
        }

        public static List<int> Get900IndexOnly(TestPoint testPoint)
        {
            List<int> idx900;
            List<int> idx1800;
            Split(testPoint, out idx900, out idx1800);
            return idx900;
        }

        public static List<int> Get1800IndexOnly(TestPoint testPoint)
        {
            List<int> idx900;
            List<int> idx1800;
            Split(testPoint, out idx900, out idx1800);
            return idx1800;
        }

        public static void Split(TestPoint testPoint, out List<int> indexOf900, out List<int> indexOf1800)
        {
            indexOf900 = new List<int>();
            indexOf1800 = new List<int>();
            for (int i = 0; i < 60; i++)
            {
                int? bcch = (int?)testPoint["GSCAN_BCCH", i];
                if (bcch == null)
                {
                    break;
                }
                if (1 <= bcch && bcch <= 124)
                {
                    indexOf900.Add(i);
                }
                else if (512 <= bcch && bcch <= 1024)
                {
                    indexOf1800.Add(i);
                }
            }
        }
    }

    public enum GSMFreqBandType
    {
        All = 0,
        GSM900,
        DSC1800
    }

}
