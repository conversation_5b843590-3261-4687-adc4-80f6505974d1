﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public class TDCellSign : CellSign
    {
        private int lac = -1;
        public int LAC
        {
            get { return lac; }
        }

        private int ci = -1;
        public int CI
        {
            get { return ci; }
        }
        private int rncID = -1;
        public int RNCID
        {
            get { return rncID; }
        }
        private string rncName = string.Empty;
        public string RNCName
        {
            get { return rncName; }
        }
        /*
       [id]
      ,[begin_time]
      ,[end_time]
      ,[自维护ID]
      ,[小区号]
      ,[LAC]
      ,[CI]
      ,[RNCID]
      ,[RNCNAME]
         */
        public static TDCellSign Fill(SqlDataReader reader)
        {
            if (reader["id"] is DBNull || reader["begin_time"] is DBNull
             || reader["end_time"] is DBNull || reader["自维护ID"] is DBNull
             || reader["小区号"] is DBNull || reader["LAC"] is DBNull
             || reader["RNCID"] is DBNull || reader["RNCNAME"] is DBNull
             || reader["CI"] is DBNull)
            {
                return null;
            }
            TDCellSign sign = new TDCellSign();
            try
            {
                sign.ID = (int)reader["id"];
                sign.ValidPeriod = new TimePeriod(DateTime.Parse(reader["begin_time"].ToString()), DateTime.Parse(reader["end_time"].ToString()));
                sign.signID = (int)reader["自维护ID"];
                sign.code = reader["小区号"].ToString();
                sign.lac = (int)reader["LAC"];
                sign.ci = (int)reader["CI"];
                sign.rncID = (int)reader["RNCID"];
                sign.rncName = reader["RNCNAME"].ToString();
            }
            catch
            {
                return null;
            }
            return sign;
        }
    }
}
