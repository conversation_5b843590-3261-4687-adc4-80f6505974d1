﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTDIYEventsComparisonDlg : BaseDialog
    {
        private ItemSelectionPanel projPanelOld;
        private ItemSelectionPanel projPanelNow;
        private ItemSelectionPanel servPanelOld;
        private ItemSelectionPanel servPanelNow;
        List<Event> eventsOld;
        List<Event> eventsNow;
        List<Event> eventsOldCmcc;
        List<Event> eventsNowCmcc;
        int districtIDNow;
        bool isAllRegionQuery;
        
        public ZTDIYEventsComparisonDlg(MainModel mModel, bool isAllRegionQuery)
        {
            mainModel = mModel;
            MapFormItemSelection itemSelection = mainModel.MainForm.GetMapForm().ItemSelection;
            InitializeComponent();

            MasterCom.Util.TreeViewCheckHelper.AutoUpdateCheckState(treeViewAreaNow);
            MasterCom.Util.TreeViewCheckHelper.AutoUpdateCheckState(treeViewAreaOld);

            this.isAllRegionQuery = isAllRegionQuery;
            dateTimePickerBeginTimeNow.Value = DateTime.Now.AddDays(-DateTime.Now.Day + 1);
            dateTimePickerEndTimeNow.Value = DateTime.Today;
            dateTimePickerBeginTimeOld.Value = DateTime.Now.AddMonths(-1).AddDays(-DateTime.Now.Day + 1);
            dateTimePickerEndTimeOld.Value = DateTime.Now.AddMonths(-1);

            addProject(itemSelection);

            addServiceType(itemSelection);

            addAgent();

            addAreaType();

            addCarrier();

            addCity();
        }

        private void addProject(MapFormItemSelection itemSelection)
        {
            listViewProjectOld.Items.Clear();
            listViewProjectNow.Items.Clear();

            if (mainModel.CategoryManager["Project"] != null)
            {
                //CategoryEnumItem[] projItems = ((CategoryEnum)CategoryManager.GetInstance()["Project"]).Items;
                projPanelOld = new ItemSelectionPanel(toolStripDropDownProjectOld, listViewProjectOld, lbProjCountOld, itemSelection, "Project", true);
                projPanelNow = new ItemSelectionPanel(toolStripDropDownProjectNow, listViewProjectNow, lbProjCountNow, itemSelection, "Project", true);
                toolStripDropDownProjectOld.Items.Clear();
                toolStripDropDownProjectNow.Items.Clear();
                projPanelOld.FreshItems();
                projPanelNow.FreshItems();
                toolStripDropDownProjectOld.Items.Add(new ToolStripControlHost(projPanelOld));
                toolStripDropDownProjectNow.Items.Add(new ToolStripControlHost(projPanelNow));
            }
        }

        private void addServiceType(MapFormItemSelection itemSelection)
        {
            listViewServiceOld.Items.Clear();
            listViewServiceNow.Items.Clear();
            if (mainModel.CategoryManager["ServiceType"] != null)
            {
                //CategoryEnumItem[] svItems = ((CategoryEnum)CategoryManager.GetInstance()["ServiceType"]).Items;
                servPanelOld = new ItemSelectionPanel(toolStripDropDownServiceOld, listViewServiceOld, lbSvCountOld, itemSelection, "ServiceType", true);
                servPanelNow = new ItemSelectionPanel(toolStripDropDownServiceNow, listViewServiceNow, lbSvCountNow, itemSelection, "ServiceType", true);
                toolStripDropDownServiceOld.Items.Clear();
                toolStripDropDownServiceNow.Items.Clear();
                servPanelOld.FreshItems();
                servPanelNow.FreshItems();
                toolStripDropDownServiceOld.Items.Add(new ToolStripControlHost(servPanelOld));
                toolStripDropDownServiceNow.Items.Add(new ToolStripControlHost(servPanelNow));
            }
        }

        private void addAgent()
        {
            listViewAgentOld.Items.Clear();
            listViewAgentNow.Items.Clear();
            if (mainModel.CategoryManager["Agent"] != null)
            {
                foreach (CategoryEnumItem item in ((CategoryEnum)mainModel.CategoryManager["Agent"]).Items)
                {
                    ListViewItem listViewItemOld = new ListViewItem();
                    listViewItemOld.Text = item.Description;
                    listViewItemOld.Tag = item.ID;
                    listViewAgentOld.Items.Add(listViewItemOld);
                    listViewItemOld.Checked = true;

                    ListViewItem listViewItemNow = new ListViewItem();
                    listViewItemNow.Text = item.Description;
                    listViewItemNow.Tag = item.ID;
                    listViewAgentNow.Items.Add(listViewItemNow);
                    listViewItemNow.Checked = true;
                }
            }
        }

        private void addAreaType()
        {
            treeViewAreaOld.Nodes.Clear();
            treeViewAreaNow.Nodes.Clear();
            TreeNode rootOld = new TreeNode("所有类型");
            TreeNode rootNow = new TreeNode("所有类型");
            treeViewAreaOld.Nodes.Add(rootOld);
            treeViewAreaNow.Nodes.Add(rootNow);
            if (mainModel.CategoryManager["AreaType"] != null)
            {
                foreach (CategoryEnumItem item in ((CategoryEnum)mainModel.CategoryManager["AreaType"]).Items)
                {
                    TreeNode nodeOld = new TreeNode();
                    nodeOld.Text = item.Name;
                    nodeOld.Tag = item.ID;
                    rootOld.Nodes.Add(nodeOld);
                    nodeOld.Checked = true;

                    TreeNode nodeNow = new TreeNode();
                    nodeNow.Text = item.Name;
                    nodeNow.Tag = item.ID;
                    rootNow.Nodes.Add(nodeNow);
                    nodeNow.Checked = true;

                    if (mainModel.AreaManager[item.ID] != null)
                    {
                        foreach (CategoryEnumItem itemArea in mainModel.AreaManager[item.ID])
                        {
                            TreeNode nodeAreaOld = new TreeNode();
                            nodeAreaOld.Text = itemArea.Name;
                            nodeAreaOld.Tag = itemArea.ID;
                            nodeOld.Nodes.Add(nodeAreaOld);
                            nodeAreaOld.Checked = true;

                            TreeNode nodeAreaNow = new TreeNode();
                            nodeAreaNow.Text = itemArea.Name;
                            nodeAreaNow.Tag = itemArea.ID;
                            nodeNow.Nodes.Add(nodeAreaNow);
                            nodeAreaNow.Checked = true;
                        }
                    }
                }
            }
            rootOld.Expand();
            rootOld.Checked = true;
            rootNow.Expand();
            rootNow.Checked = true;
        }

        private void addCarrier()
        {
            listViewCarrier.Items.Clear();
            if (mainModel.CategoryManager["Carrier"] != null)
            {
#if LT
                ListViewItem listViewItem = new ListViewItem();
                listViewItem.Text = "中国联通";
                listViewItem.Tag = 2;
                listViewCarrier.Items.Add(listViewItem);
                listViewItem = new ListViewItem();
                listViewItem.Text = "中国移动";
                listViewItem.Tag = 1;
                listViewCarrier.Items.Add(listViewItem);
                listViewItem = new ListViewItem();
                listViewItem.Text = "中国电信";
                listViewItem.Tag = 3;
                listViewCarrier.Items.Add(listViewItem);
#elif DX
                ListViewItem listViewItem = new ListViewItem();
                listViewItem = new ListViewItem();
                listViewItem.Text = "中国电信";
                listViewItem.Tag = 3;
                listViewCarrier.Items.Add(listViewItem);
                listViewItem = new ListViewItem();
                listViewItem.Text = "中国移动";
                listViewItem.Tag = 1;
                listViewCarrier.Items.Add(listViewItem);
                listViewItem = new ListViewItem();
                listViewItem.Text = "中国联通";
                listViewItem.Tag = 2;
                listViewCarrier.Items.Add(listViewItem); 
#else
                foreach (CategoryEnumItem item in ((CategoryEnum)mainModel.CategoryManager["Carrier"]).Items)
                {
                    ListViewItem listViewItem = new ListViewItem();
                    listViewItem.Text = item.Description;
                    listViewItem.Tag = item.ID;
                    listViewCarrier.Items.Add(listViewItem);
                }
#endif
            }
            if (listViewCarrier.Items.Count > 0)
            {
                listViewCarrier.Items[0].Checked = true;
            }
        }

        private void addCity()
        {
            listViewCity.Items.Clear();
            if (MainModel.GetInstance().DistrictID == 1)
            {
                for (int districtId = 1; districtId <= DistrictManager.GetInstance().getDistrictCount(); districtId++)
                {
                    ListViewItem listViewItem = new ListViewItem();
                    listViewItem.Text = DistrictManager.GetInstance().getDistrictName(districtId);
                    listViewItem.Tag = districtId;
                    listViewCity.Items.Add(listViewItem);
                    listViewItem.Checked = false;
                }
            }
            else
            {
                ListViewItem listViewItem = new ListViewItem();
                listViewItem.Text = DistrictManager.GetInstance().getDistrictName(MainModel.GetInstance().DistrictID);
                listViewItem.Tag = MainModel.GetInstance().DistrictID;
                listViewCity.Items.Add(listViewItem);
                listViewItem.Checked = false;
            }
            if (listViewCity.Items.Count == 1)
            {
                checkAllCity.Visible = false;
                foreach (ListViewItem item in listViewCity.Items)
                {
                    item.Checked = true;
                }
            }
        }

        public static double GetDistance(double x1, double y1, double x2, double y2)
        {
            double longitudeDistance = (Math.Sin((90 - y1) * 2 * Math.PI / 360) + Math.Sin((90 - y2) * 2 * Math.PI / 360)) / 2 * (x1 - x2) / 360 * 40075360;
            double latitudeDistance = (y1 - y2) / 360 * 39940670;
            return Math.Sqrt(longitudeDistance * longitudeDistance + latitudeDistance * latitudeDistance);
        }

         /// <summary>
        /// 判断当前查询文件的地市是否符合事件的地市
         /// </summary>
         /// <param name="e"></param>
         /// <param name="districtID"></param>
         /// <param name="lacCityNameMap"></param>
         /// <returns></returns>
        private bool IsHighspeedDistrict(Event e, int districtID,Dictionary<int, string> lacCityNameMap)
        {
            string cityname;
            if (lacCityNameMap.TryGetValue((int)e["LAC"], out cityname))
            {
                int dId = -1;
                switch (cityname)
                {
                    case "广州":
                        dId = 2;
                        break;
                    case "深圳":
                        dId = 3;
                        break;
                    case "珠海":
                        dId = 4;
                        break;
                    case "汕头":
                        dId = 5;
                        break;
                    case "佛山":
                        dId = 6;
                        break;
                    case "韶关":
                        dId = 7;
                        break;
                    case "河源":
                        dId = 8;
                        break;
                    case "梅州":
                        dId = 9;
                        break;
                    case "惠州":
                        dId = 10;
                        break;
                    case "汕尾":
                        dId = 11;
                        break;
                    case "东莞":
                        dId = 12;
                        break;
                    case "中山":
                        dId = 13;
                        break;
                    case "江门":
                        dId = 14;
                        break;
                    case "阳江":
                        dId = 15;
                        break;
                    case "湛江":
                        dId = 16;
                        break;
                    case "茂名":
                        dId = 17;
                        break;
                    case "肇庆":
                        dId = 18;
                        break;
                    case "清远":
                        dId = 19;
                        break;
                    case "潮州":
                        dId = 20;
                        break;
                    case "揭阳":
                        dId = 21;
                        break;
                    case "云浮":
                        dId = 22;
                        break;
                    default:
                        break;
                }
                if (dId == districtID)
                    return true;
                else
                    return false;
            }
            else
                return false;
        }

        private void btnQueryReduplicateEvents_Click(object sender, EventArgs e)
        {
            mainModel.EventsResultForReduplicateEvents.eventNowHasReduplicateEvents.Clear();
            mainModel.EventsResultForReduplicateEvents.eventNowList.Clear();

            //每查询一个地市时,地市使用的事件选择内容------------------------------------------------------
            bool filterOffValue9Old = false;
            bool filterOffValue9Now = false;
            bool hasSelectedOldEvents = false;//是否第一次选择旧的事件，以后其它地市则沿用第一次选择的事件类型
            bool hasSelectedNowEvents = false;//是否第一次选择当前事件，以后其它地市则沿用第一次选择的事件类型
            List<int> selectedOldEvents = new List<int>();
            List<int> selectedNowEvents = new List<int>();
            //------------------------------------------------------------------------------------------------

            districtIDNow = mainModel.DistrictID;
            if (listViewCity.CheckedItems.Count == 0)
            {
                MessageBox.Show("请选择查询的地市。");
                return;
            }

            DiySqlQueryLacCityName diySQLQueryLacCityName = new DiySqlQueryLacCityName(mainModel, districtIDNow);
            diySQLQueryLacCityName.Query();
            Dictionary<int, string> lacCityNameMap = diySQLQueryLacCityName.LacCityNameMap;   //查询Lac与地市名关联的集合

            eventsOldCmcc = new List<Event>();
            eventsNowCmcc = new List<Event>();
            foreach (ListViewItem item in this.listViewCity.CheckedItems)
            {
                eventsOld = new List<Event>();
                eventsNow = new List<Event>();
                int districtID = (int)item.Tag;

                DiySqlQueryLacCityName querySelLacCityName = new DiySqlQueryLacCityName(mainModel, districtID);
                querySelLacCityName.Query();
                Dictionary<int, string> selLacCityNameMap = querySelLacCityName.LacCityNameMap;   //查询Lac与地市名关联的集合

                #region 按选择的区域模式查询历史事件集合
                bool isQueryHis = queryRegionEvent(ref filterOffValue9Old, ref hasSelectedOldEvents, ref selectedOldEvents, districtID, selLacCityNameMap, eventsOld, eventsOldCmcc);
                if (!isQueryHis)
                {
                    return;
                }
                #endregion

                #region 按选择的区域模式查询当前事件
                bool isQueryCur = queryRegionEvent(ref filterOffValue9Now, ref hasSelectedNowEvents, ref selectedNowEvents, districtID, selLacCityNameMap, eventsNow, eventsNowCmcc);
                if (!isQueryCur)
                {
                    return;
                }
                #endregion

                removeEvent();

                getReduplicateEvents(lacCityNameMap, districtID);

                setColumnVisible();
            }
            fireShowForm();
            mainModel.DistrictID = districtIDNow;
        }

        private bool queryRegionEvent(ref bool filterOffValue9Now, ref bool hasSelectedNowEvents, ref List<int> selectedNowEvents, int districtID, Dictionary<int, string> selLacCityNameMap, List<Event> events, List<Event> eventsCmcc)
        {
            if (isAllRegionQuery)
            {
                bool isQuery = setDiyEventForComparison(ref filterOffValue9Now, ref hasSelectedNowEvents, ref selectedNowEvents, districtID);
                if (!isQuery)
                {
                    return false;
                }
            }
            else
            {
                bool isQuery = setDiyEventForComparisonByRegion(ref filterOffValue9Now, ref hasSelectedNowEvents, ref selectedNowEvents, districtID);
                if (!isQuery)
                {
                    return false;
                }
            }

            addValidEvent(districtID, selLacCityNameMap, events, eventsCmcc);
            return true;
        }

        private bool setDiyEventForComparisonByRegion(ref bool filterOffValue9, ref bool hasSelectedEvents, ref List<int> selectedEvents, int districtID)
        {
            DiyEventForComparisonByRegion diyEvent = new DiyEventForComparisonByRegion(mainModel, filterOffValue9, hasSelectedEvents, selectedEvents);
            diyEvent.BeginDate = this.dateTimePickerBeginTimeNow.Value.Date;
            diyEvent.EndDate = this.dateTimePickerEndTimeNow.Value.Date;
            diyEvent.ListViewProject = this.listViewProjectNow;
            diyEvent.ListViewService = this.listViewServiceNow;
            diyEvent.ListViewCarrier = this.listViewCarrier;
            diyEvent.ListViewAgent = this.listViewAgentNow;
            diyEvent.TreeViewArea = this.treeViewAreaNow;
            diyEvent.checkboxFilename = this.cbxFilenameNow;
            diyEvent.textboxFilename = this.textBoxFilenameNow;
            diyEvent.IsForOldEvent = false;
            QueryCondition condition = diyEvent.getQueryCondition();
            if (condition == null)
            {
                mainModel.DistrictID = districtIDNow;
                return false;
            }
            mainModel.DistrictID = districtID;
            condition.DistrictID = districtID;
            diyEvent.SetQueryCondition(condition);
            diyEvent.Query();
            if (diyEvent.stopQuery)
            {
                return false;
            }
            filterOffValue9 = diyEvent.filterOffValue9;
            hasSelectedEvents = diyEvent.hasSelectedEvents;
            selectedEvents = diyEvent.selectedEvents;
            return true;
        }

        private bool setDiyEventForComparison(ref bool filterOffValue9, ref bool hasSelectedEvents, ref List<int> selectedEvents, int districtID)
        {
            DiyEventForComparison diyEvent = new DiyEventForComparison(mainModel, filterOffValue9, hasSelectedEvents, selectedEvents);
            diyEvent.BeginDate = this.dateTimePickerBeginTimeNow.Value.Date;
            diyEvent.EndDate = this.dateTimePickerEndTimeNow.Value.Date;
            diyEvent.ListViewProject = this.listViewProjectNow;
            diyEvent.ListViewService = this.listViewServiceNow;
            diyEvent.ListViewCarrier = this.listViewCarrier;
            diyEvent.ListViewAgent = this.listViewAgentNow;
            diyEvent.TreeViewArea = this.treeViewAreaNow;
            diyEvent.checkboxFilename = this.cbxFilenameNow;
            diyEvent.textboxFilename = this.textBoxFilenameNow;
            diyEvent.IsForOldEvent = false;
            QueryCondition condition = diyEvent.getQueryCondition();
            if (condition == null)
            {
                mainModel.DistrictID = districtIDNow;
                return false;
            }
            mainModel.DistrictID = districtID;
            condition.DistrictID = districtID;
            diyEvent.SetQueryCondition(condition);
            diyEvent.Query();
            if (diyEvent.stopQuery)
            {
                return false;
            }
            filterOffValue9 = diyEvent.filterOffValue9;
            hasSelectedEvents = diyEvent.hasSelectedEvents;
            selectedEvents = diyEvent.selectedEvents;
            return true;
        }

        private void addValidEvent(int districtID, Dictionary<int, string> selLacCityNameMap, List<Event> events, List<Event> eventsCmcc)
        {
            foreach (DTFileDataManager fileDataManager in mainModel.DTDataManager.FileDataManagers)
            {
                foreach (Event evt in fileDataManager.Events)
                {
                    //高速文件包含多个地市的事件，需要过滤非当前查询地市的事件
                    if (evt.AreaTypeID != 2 || IsHighspeedDistrict(evt, districtID, selLacCityNameMap))
                    {
                        addEvent(districtID, evt, events, eventsCmcc);
                    }
                }
            }
        }

        private void addEvent(int districtID, Event evt, List<Event> events,List<Event> eventsCmcc)
        {
            if (districtID != 1)
            {
                bool evtExist = false;
                foreach (Event e2 in eventsCmcc)
                {
                    if ((int)e2["LAC"] == (int)evt["LAC"])
                    {
                        evtExist = true;
                        break;
                    }
                }
                if (!evtExist)
                {
                    events.Add(evt);
                }
            }
            else
            {
                eventsCmcc.Add(evt);
                events.Add(evt); //储存新的事件集
            }
        }

        private void removeEvent()
        {
            #region 剔除位置更新事件
            List<int> projIds = new List<int>();
            string projIdStr; //剔除事件的项目id，包含历史和当前的
            StringBuilder sb = new StringBuilder();
            foreach (ListViewItem selItem in this.listViewProjectNow.Items)
            {
                projIds.Add((int)selItem.Tag);
            }
            foreach (ListViewItem selItem in this.listViewProjectOld.Items)
            {
                if (!projIds.Contains((int)selItem.Tag))
                    projIds.Add((int)selItem.Tag);
            }
            int idCount = projIds.Count;
            foreach (int id in projIds)
            {
                if (id == projIds[idCount - 1])
                    sb.Append(id);
                else
                    sb.Append(id.ToString() + ",");
            }
            projIdStr = sb.ToString();
            int begintime = dateTimePickerBeginTimeOld.Value.Year * 10000 + dateTimePickerBeginTimeOld.Value.Month * 100 + dateTimePickerBeginTimeOld.Value.Day; //剔除时间开始点为历史开始时间
            int endtime = dateTimePickerEndTimeNow.Value.Year * 10000 + dateTimePickerEndTimeNow.Value.Month * 100 + dateTimePickerEndTimeNow.Value.Day; //剔除时间结束点为当前结束时间
            DiySqlQueryCutEvent diySQLQueryCutEvent = new DiySqlQueryCutEvent(mainModel, begintime, endtime, projIdStr);//查询要剔除的事件

            diySQLQueryCutEvent.Query();
            List<CutEvent> cutEvents = diySQLQueryCutEvent.cutEvents;
            List<Event> cutEventstempNow = new List<Event>();
            List<Event> cutEventstempOld = new List<Event>();
            addNeedRemoveEvt(cutEvents, cutEventstempNow, cutEventstempOld);
            foreach (Event cutevt in cutEventstempNow)
            {
                eventsNow.Remove(cutevt);
            }
            foreach (Event cutevt in cutEventstempOld)
            {
                eventsOld.Remove(cutevt);
            }
            #endregion
        }

        private void addNeedRemoveEvt(List<CutEvent> cutEvents, List<Event> cutEventstempNow, List<Event> cutEventstempOld)
        {
            foreach (CutEvent cutevt in cutEvents)
            {
                foreach (Event evtNow in eventsNow)
                {
                    if (evtNow.FileID == cutevt.FileID
                        && evtNow.EventInfo.ID == cutevt.eventid
                        && evtNow.Time == cutevt.time)
                    {
                        cutEventstempNow.Add(evtNow);
                    }
                }

                foreach (Event evtOld in eventsOld)
                {
                    if (evtOld.FileID == cutevt.FileID
                        && evtOld.EventInfo.ID == cutevt.eventid
                        && evtOld.Time == cutevt.time)
                    {
                        cutEventstempOld.Add(evtOld);
                    }
                }
            }
        }

        private void getReduplicateEvents(Dictionary<int, string> lacCityNameMap, int districtID)
        {
            ZTDIYEventsComparisonForm.EventNowHasReduplicateEvents eventNowHasReduplicateEvents;    //含有历史重复问题点的当前的事件
            List<ZTDIYEventsComparisonForm.EventNowHasReduplicateEvents> eventNowHasReduplicateEventsList =
                new List<ZTDIYEventsComparisonForm.EventNowHasReduplicateEvents>();    //含有历史重复问题点的当前的事件集合
            List<ZTDIYEventsComparisonForm.ReduplicateEvent> reduplicateEventList = null;     //历史重复问题事件点集合
            List<ZTDIYEventsComparisonForm.EventNowHasReduplicateEvents> eventNowList =
                new List<ZTDIYEventsComparisonForm.EventNowHasReduplicateEvents>();  //当前事件集合
            foreach (Event evtNow in eventsNow)
            {
                eventNowHasReduplicateEvents = new ZTDIYEventsComparisonForm.EventNowHasReduplicateEvents();
                addEventNowList(lacCityNameMap, districtID, eventNowHasReduplicateEvents, eventNowList, evtNow);

                reduplicateEventList = new List<ZTDIYEventsComparisonForm.ReduplicateEvent>();
                foreach (Event evtOld in eventsOld)
                {
                    addReduplicateEventList(lacCityNameMap, districtID, eventNowHasReduplicateEvents, reduplicateEventList, evtNow, evtOld);
                }
                eventNowHasReduplicateEvents.reduplicateEvents = reduplicateEventList;
                if (eventNowHasReduplicateEvents.reduplicateEvents.Count > 0)   //只显示有重复事件的当前事件
                {
                    eventNowHasReduplicateEventsList.Add(eventNowHasReduplicateEvents);
                }

            }
            mainModel.EventsResultForReduplicateEvents.eventNowHasReduplicateEvents.AddRange(eventNowHasReduplicateEventsList);

            mainModel.EventsResultForReduplicateEvents.eventNowList.AddRange(eventNowList);
        }

        private void addReduplicateEventList(Dictionary<int, string> lacCityNameMap, int districtID, ZTDIYEventsComparisonForm.EventNowHasReduplicateEvents eventNowHasReduplicateEvents, List<ZTDIYEventsComparisonForm.ReduplicateEvent> reduplicateEventList, Event evtNow, Event evtOld)
        {
            double distance = GetDistance(evtNow.Longitude, evtNow.Latitude, evtOld.Longitude, evtOld.Latitude);
            if ((int)evtNow["LAC"] != 0 && (int)evtNow["CI"] != 0 && (int)evtOld["LAC"] != 0 && (int)evtOld["CI"] != 0
               && (int)evtNow["LAC"] == (int)evtOld["LAC"] && (int)evtNow["CI"] == (int)evtOld["CI"])       //两事件的LAC和CI相同，则为重复
            {
                //历史重复问题事件点
                ZTDIYEventsComparisonForm.ReduplicateEvent reduplicateEvent = new ZTDIYEventsComparisonForm.ReduplicateEvent();
                setReduplicateEvent(reduplicateEvent, evtOld);
                string cityName2 = null;
                if (districtIDNow == 1 && districtID == 1)
                {
                    lacCityNameMap.TryGetValue((int)evtNow["LAC"], out cityName2);
                    eventNowHasReduplicateEvents.City = cityName2;
                }
                else
                {
                    reduplicateEvent.City = DistrictManager.GetInstance().getDistrictName(districtID);
                }

                if (distance <= (double)(this.numericUpDownDistance.Value))
                {
                    reduplicateEvent.reduplicateReason = String.Format("距离在{0}米以内，LAC,CI相同", this.numericUpDownDistance.Value.ToString());
                }
                else
                {
                    reduplicateEvent.reduplicateReason = "LAC,CI相同";
                }

                reduplicateEventList.Add(reduplicateEvent);
            }
            else if (distance <= (double)(this.numericUpDownDistance.Value))   //判断在两个事件相距少于等于设定的临界距离，如：少于等于200米，则判为重复
            {
                ZTDIYEventsComparisonForm.ReduplicateEvent reduplicateEvent = new ZTDIYEventsComparisonForm.ReduplicateEvent();
                setReduplicateEvent(reduplicateEvent, evtOld);
                reduplicateEvent.reduplicateReason = String.Format("距离在{0}米以内", this.numericUpDownDistance.Value.ToString());
                string cityName3 = null;
                if (districtIDNow == 1 && districtID == 1)
                {
                    lacCityNameMap.TryGetValue((int)evtOld["LAC"], out cityName3);
                    reduplicateEvent.City = cityName3;
                }
                else
                {
                    reduplicateEvent.City = DistrictManager.GetInstance().getDistrictName(districtID);
                }

                reduplicateEventList.Add(reduplicateEvent);
            }
        }

        private void setReduplicateEvent(ZTDIYEventsComparisonForm.ReduplicateEvent reduplicateEvent, Event evtOld)
        {
            reduplicateEvent.ApplyHeader(evtOld.FileInfo);
            reduplicateEvent.ID = evtOld.ID;
            reduplicateEvent.Time = evtOld.Time;
            foreach (ListViewItem projItem in this.listViewProjectOld.Items)
            {
                if ((byte)(int)projItem.Tag == evtOld.ProjectType)
                {
                    reduplicateEvent.projDesc = projItem.Text;
                    break;
                }
            }
            if (mainModel.AreaManager[evtOld.AreaTypeID] != null)
            {
                foreach (CategoryEnumItem itemArea in mainModel.AreaManager[evtOld.AreaTypeID])
                {
                    if (itemArea.ID == evtOld.AreaID)
                        reduplicateEvent.RoadName = itemArea.Name;
                }
            }
            reduplicateEvent.reduplicateEventName = evtOld.EventInfo == null ? "事件ID：" + evtOld.ID : evtOld.EventInfo.Name;
            reduplicateEvent.Longitude = evtOld.Longitude;
            reduplicateEvent.Latitude = evtOld.Latitude;
            reduplicateEvent.reduplicateDate = evtOld.DateTime;
            reduplicateEvent.reduplicateLac = (int)evtOld["LAC"];
            reduplicateEvent.reduplicateCi = (int)evtOld["CI"];
        }

        private void addEventNowList(Dictionary<int, string> lacCityNameMap, int districtID, ZTDIYEventsComparisonForm.EventNowHasReduplicateEvents eventNowHasReduplicateEvents, List<ZTDIYEventsComparisonForm.EventNowHasReduplicateEvents> eventNowList, Event evtNow)
        {
            eventNowHasReduplicateEvents.ApplyHeader(evtNow.FileInfo);
            if (mainModel.AreaManager[evtNow.AreaTypeID] != null)
            {
                foreach (CategoryEnumItem itemArea in mainModel.AreaManager[evtNow.AreaTypeID])
                {
                    if (itemArea.ID == evtNow.AreaID)
                        eventNowHasReduplicateEvents.RoadName = itemArea.Name;
                }
            }
            eventNowHasReduplicateEvents.EventName = evtNow.EventInfo == null ? "事件ID：" + evtNow.ID : evtNow.EventInfo.Name;
            eventNowHasReduplicateEvents.Longitude = evtNow.Longitude;
            eventNowHasReduplicateEvents.Latitude = evtNow.Latitude;
            eventNowHasReduplicateEvents.date = evtNow.DateTime;
            eventNowHasReduplicateEvents.lac = (int)evtNow["LAC"];
            eventNowHasReduplicateEvents.ci = (int)evtNow["CI"];
            foreach (ListViewItem projItem in this.listViewProjectNow.Items)
            {
                if ((byte)(int)projItem.Tag == evtNow.ProjectType)
                {
                    eventNowHasReduplicateEvents.projDesc = projItem.Text;
                    break;
                }
            }

            //此四字段拿来计算工单号
            eventNowHasReduplicateEvents.FileID = evtNow.FileID;
            eventNowHasReduplicateEvents.ID = evtNow.ID;
            eventNowHasReduplicateEvents.Time = evtNow.Time;
            eventNowHasReduplicateEvents.dbid = districtID;

            string cityName1 = null;
            if (districtIDNow == 1 && districtID == 1)    //当当前查询的地市为广东，而同时系统加载广东地图时，按LAC取事件对应的地市名
            {
                lacCityNameMap.TryGetValue((int)evtNow["LAC"], out cityName1);
                eventNowHasReduplicateEvents.City = cityName1;
            }
            else
                eventNowHasReduplicateEvents.City = DistrictManager.GetInstance().getDistrictName(districtID);

            eventNowList.Add(eventNowHasReduplicateEvents);
        }

        private void setColumnVisible()
        {
            //是否显示重复事件的详细信息
            if (this.checkBoxShowReduplicateDetail.Checked)
                mainModel.EventsResultForReduplicateEvents.ShowReduplicateEventsDetail = true;
            else
                mainModel.EventsResultForReduplicateEvents.ShowReduplicateEventsDetail = false;

            //是否只显示在临界距离内以及LAC，CI相同的重复事件
            if (this.radioButtonInOrLacCi.Checked)
            {
                mainModel.EventsResultForReduplicateEvents.OnlyShowInLacCiReason = false;
            }
            if (this.radioButtonInAndLacCi.Checked)
            {
                mainModel.EventsResultForReduplicateEvents.OnlyShowInLacCiReason = true;
                mainModel.EventsResultForReduplicateEvents.ShowInLacCiReason = true;
            }

            //在重复原因上是否显示临界距离内LAC，CI相同的信息
            if (this.chkShowInLacCiReason.Checked)
                mainModel.EventsResultForReduplicateEvents.ShowInLacCiReason = true;
            else
                mainModel.EventsResultForReduplicateEvents.ShowInLacCiReason = false;

            //在当前事件上显示该工单号
            if (this.chkShowOrderId.Checked)
                mainModel.EventsResultForReduplicateEvents.ShowOrderId = true;
            else
                mainModel.EventsResultForReduplicateEvents.ShowOrderId = false;
        }

        protected void fireShowForm()
        {
            ZTDIYEventsComparisonForm frm = MainModel.GetInstance().CreateResultForm(typeof(ZTDIYEventsComparisonForm)) as ZTDIYEventsComparisonForm;
            frm.Owner = MainModel.MainForm;
            frm.showDuplicateEventForm();
            frm.Visible = true;
            frm.BringToFront();
        }

        private void btnPopupProjectOld_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(btnPopupProjectOld.Width, btnPopupProjectOld.Height);
            toolStripDropDownProjectOld.Show(btnPopupProjectOld, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void btnPopupServiceOld_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(btnPopupServiceOld.Width, btnPopupServiceOld.Height);
            toolStripDropDownServiceOld.Show(btnPopupServiceOld, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void btnPopupServiceNow_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(btnPopupServiceNow.Width, btnPopupServiceNow.Height);
            toolStripDropDownServiceNow.Show(btnPopupServiceNow, pt, ToolStripDropDownDirection.BelowLeft);
        }   
        
        private void btnPopupProjectNow_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(btnPopupProjectNow.Width, btnPopupProjectNow.Height);
            toolStripDropDownProjectNow.Show(btnPopupProjectNow, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void checkAllCity_CheckedChanged(object sender, EventArgs e)
        {
            if (checkAllCity.Checked)
            {
                for (int i = 0; i < listViewCity.Items.Count; i++)
                {
                    listViewCity.Items[i].Checked = true;
                }
            }
            else
            {
                for (int i = 0; i < listViewCity.Items.Count; i++)
                {
                    listViewCity.Items[i].Checked = false;
                }
            }
            listViewCity.Enabled = true;
        }

        private void checkBoxShowReduplicateDetail_CheckedChanged(object sender, EventArgs e)
        {
            if (checkBoxShowReduplicateDetail.Checked)    //当选择显示详细的重复事件选项时，以下重复原因的显示选项才有用
            {
                if (this.radioButtonInOrLacCi.Checked)
                    this.chkShowInLacCiReason.Enabled = true;
                else
                    this.chkShowInLacCiReason.Enabled = false;
            }
            else
            {
                this.chkShowInLacCiReason.Checked = false;
                this.chkShowInLacCiReason.Enabled = false;
            }
        }

        private void radioButtonInOrLacCi_CheckedChanged(object sender, EventArgs e)
        {
            if (radioButtonInOrLacCi.Checked)
                chkShowInLacCiReason.Enabled = true;
            else
            {
                chkShowInLacCiReason.Checked = true;
                chkShowInLacCiReason.Enabled = false;
            }
        }

        private void cbxFilenameOld_CheckedChanged(object sender, EventArgs e)
        {
            textBoxFilenameOld.Enabled = cbxFilenameOld.Checked;
        }

        private void cbxFilenameNow_CheckedChanged(object sender, EventArgs e)
        {
            textBoxFilenameNow.Enabled = cbxFilenameNow.Checked;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }

    public class DiyEventForComparisonByRegion : DIYEventByRegion
    {
        public DiyEventForComparisonByRegion(MainModel mainModel, bool FilterOffValue9, bool hasSelectedEvents, List<int> selectedEvents)
            : base(mainModel)
        {
            this.filterOffValue9 = FilterOffValue9;
            this.hasSelectedEvents = hasSelectedEvents;
            this.selectedEvents = selectedEvents;
        }

        public DateTime BeginDate { get; set; }
        public DateTime EndDate { get; set; }
        public ListView ListViewProject { get; set; }
        public ListView ListViewService { get; set; }
        public ListView ListViewCarrier { get; set; }
        public TreeView TreeViewArea { get; set; }
        public ListView ListViewAgent { get; set; }
        public CheckBox checkboxFilename { get; set; }
        public TextBox textboxFilename { get; set; }
        public bool IsForOldEvent { get; set; }
        public bool filterOffValue9 { get; set; }
        public bool hasSelectedEvents { get; set; }//是否第一次选择事件，以后其它地市则沿用第一次选择的事件类型
        public List<int> selectedEvents { get; set; }

        public QueryCondition getQueryCondition()
        {
            if (BeginDate > EndDate)
            {
                MessageBox.Show("开始时间不能大于结束时间。");
            }
            else if (ListViewProject.Items.Count <= 0)
            {
                MessageBox.Show("至少需要选择一个项目。");
            }
            else if (ListViewService.Items.Count <= 0)
            {
                MessageBox.Show("至少需要选择一个业务。");
            }
            else if (ListViewCarrier.CheckedItems.Count <= 0)
            {
                MessageBox.Show("至少需要选择一个运营商。");
            }
            else if (ListViewAgent.CheckedItems.Count <= 0)
            {
                MessageBox.Show("至少需要选择一个上传单元。");
            }
            else
            {
                QueryCondition condition = new QueryCondition();
                condition.Geometorys = MainModel.SearchGeometrys;
                condition.Periods.Add(new TimePeriod(BeginDate, EndDate + new TimeSpan(1, 0, 0, 0)));
                foreach (ListViewItem item in ListViewProject.Items)
                {
                    condition.Projects.Add((byte)(int)item.Tag);
                }
                foreach (ListViewItem item in ListViewService.Items)
                {
                    condition.ServiceTypes.Add((byte)(int)item.Tag);
                }
                foreach (ListViewItem item in ListViewCarrier.CheckedItems)
                {
                    condition.CarrierTypes.Add((byte)(int)item.Tag);
                }

                addConditionAgent(condition);

                addConditionArea(condition);

                addConditionFileName(condition);

                int queryTypeSelectedIndex = -1;
                condition.QueryType = (byte)queryTypeSelectedIndex;

                return condition;
            }
            return null;
        }

        private void addConditionAgent(QueryCondition condition)
        {
            condition.IsAllAgent = false;
            condition.AgentIds.Clear();
            foreach (ListViewItem item in ListViewAgent.Items)
            {
                if (item.Checked)
                {
                    condition.AgentIds.Add((int)item.Tag);
                }
            }
        }

        private void addConditionFileName(QueryCondition condition)
        {
            if (checkboxFilename.Checked)
            {
                int orNum = 1;
                condition.FileNameOrNum = orNum;
                condition.FileName = textboxFilename.Text;
            }
            else
            {
                condition.FileName = null;
            }
        }

        private void addConditionArea(QueryCondition condition)
        {
            if (TreeViewArea.Nodes[0].Checked)
            {
                bool allTypes = true;
                foreach (TreeNode typeNode in TreeViewArea.Nodes[0].Nodes)
                {
                    if (typeNode.Checked)
                    {
                        allTypes = addCheckedArea(condition, allTypes, typeNode);
                    }
                    else
                    {
                        allTypes = false;
                    }
                }
                if (allTypes)
                {
                    condition.Areas.Clear();
                }
            }
        }

        private bool addCheckedArea(QueryCondition condition, bool allTypes, TreeNode typeNode)
        {
            int type = (int)typeNode.Tag;
            List<int> areaIDs = new List<int>();
            bool allArea = true;
            foreach (TreeNode areaNode in typeNode.Nodes)
            {
                if (areaNode.Checked)
                {
                    int id = (int)areaNode.Tag;
                    areaIDs.Add(id);
                }
                else
                {
                    allArea = false;
                }
            }
            if (allArea)
            {
                condition.Areas[type] = null;
            }
            else
            {
                condition.Areas[type] = areaIDs;
                allTypes = false;
            }

            return allTypes;
        }

        public bool stopQuery { get; set; } = false;//在事件选择窗中取消查询
        /// <summary>
        /// 选择事件窗口，区分显示标题按历史问题点库和当前问题点
        /// </summary>
        /// <returns></returns>
        protected override bool prepareAskWhatEvent()
        {
            if (!hasSelectedEvents)  //第一个地市需要选择事件类型
            {
                EventChooserForm eventChooser = EventChooserForm.GetInstance(MainModel);
                if (IsForOldEvent)
                {
                    eventChooser.Text = "历史问题点库的事件选择";
                }
                else
                {
                    eventChooser.Text = "当前问题点的事件选择";
                }

                if (eventChooser.ShowDialog() == DialogResult.OK)
                {
                    showForm = eventChooser.ShowForm;
                    Condition.FilterOffValue9 = eventChooser.FilterOffValue9;
                    filterOffValue9 = eventChooser.FilterOffValue9;
                    if (eventChooser.SelectedEventIDs.Count > 0)
                    {
                        Condition.EventIDs = eventChooser.SelectedEventIDs;
                        selectedEvents = eventChooser.SelectedEventIDs;
                        hasSelectedEvents = true;
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
            }
            else   //随后的地市都沿用第一个地市所选择的事件类型
            {
                Condition.FilterOffValue9 = filterOffValue9;
                Condition.EventIDs = selectedEvents;
                return true;
            }
            stopQuery = true;
            return false;
        }

        protected override void query()
        {
            if (!prepareAskWhatEvent())
            {
                return;
            }
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.IsDrawEventResult = false;
                MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;
                MainModel.SelectedMessage = null;
                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }

        }
    }

    public class DiyEventForComparison : DIYEventByAllRegion
    {
        public DiyEventForComparison(MainModel mainModel, bool FilterOffValue9, bool hasSelectedEvents, List<int> selectedEvents)
            : base(mainModel)
        {
            this.filterOffValue9 = FilterOffValue9;
            this.hasSelectedEvents = hasSelectedEvents;
            this.selectedEvents = selectedEvents;
        }

        public DateTime BeginDate { get; set; }
        public DateTime EndDate { get; set; }
        public ListView ListViewProject { get; set; }
        public ListView ListViewService { get; set; }
        public ListView ListViewCarrier { get; set; }
        public TreeView TreeViewArea { get; set; }
        public ListView ListViewAgent { get; set; }
        public CheckBox checkboxFilename { get; set; }
        public TextBox textboxFilename { get; set; }

        public bool IsForOldEvent { get; set; }
        public bool filterOffValue9 { get; set; }
        public bool hasSelectedEvents { get; set; }//是否第一次选择事件，以后其它地市则沿用第一次选择的事件类型
        public List<int> selectedEvents { get; set; }

        public QueryCondition getQueryCondition()
        {
            if (BeginDate > EndDate)
            {
                MessageBox.Show("开始时间不能大于结束时间。");
            }
            else if (ListViewProject.Items.Count <= 0)
            {
                MessageBox.Show("至少需要选择一个项目。");
            }
            else if (ListViewService.Items.Count <= 0)
            {
                MessageBox.Show("至少需要选择一个业务。");
            }
            else if (ListViewCarrier.CheckedItems.Count <= 0)
            {
                MessageBox.Show("至少需要选择一个运营商。");
            }
            else if (ListViewAgent.CheckedItems.Count <= 0)
            {
                MessageBox.Show("至少需要选择一个上传单元。");
            }
            else
            {
                QueryCondition condition = new QueryCondition();
                //condition.Geometorys = MainModel.SearchGeometrys;
                condition.Periods.Add(new TimePeriod(BeginDate, EndDate + new TimeSpan(1, 0, 0, 0)));
                foreach (ListViewItem item in ListViewProject.Items)
                {
                    condition.Projects.Add((byte)(int)item.Tag);
                }
                foreach (ListViewItem item in ListViewService.Items)
                {
                    condition.ServiceTypes.Add((byte)(int)item.Tag);
                }
                foreach (ListViewItem item in ListViewCarrier.CheckedItems)
                {
                    condition.CarrierTypes.Add((byte)(int)item.Tag);
                }

                addConditionAgent(condition);

                addConditionArea(condition);

                addConditionFileName(condition);

                int queryTypeSelectedIndex = -1;
                condition.QueryType = (byte)queryTypeSelectedIndex;

                return condition;
            }
            return null;
        }

        private void addConditionAgent(QueryCondition condition)
        {
            condition.IsAllAgent = false;
            condition.AgentIds.Clear();
            foreach (ListViewItem item in ListViewAgent.Items)
            {
                if (item.Checked)
                {
                    condition.AgentIds.Add((int)item.Tag);
                }
            }
        }

        private void addConditionFileName(QueryCondition condition)
        {
            if (checkboxFilename.Checked)
            {
                int orNum = 1;
                condition.FileNameOrNum = orNum;
                condition.FileName = textboxFilename.Text;
            }
            else
            {
                condition.FileName = null;
            }
        }

        private void addConditionArea(QueryCondition condition)
        {
            if (TreeViewArea.Nodes[0].Checked)
            {
                bool allTypes = true;
                foreach (TreeNode typeNode in TreeViewArea.Nodes[0].Nodes)
                {
                    if (typeNode.Checked)
                    {
                        allTypes = addCheckedArea(condition, allTypes, typeNode);
                    }
                    else
                    {
                        allTypes = false;
                    }
                }
                if (allTypes)
                {
                    condition.Areas.Clear();
                }
            }
        }

        private bool addCheckedArea(QueryCondition condition, bool allTypes, TreeNode typeNode)
        {
            int type = (int)typeNode.Tag;
            List<int> areaIDs = new List<int>();
            bool allArea = true;
            foreach (TreeNode areaNode in typeNode.Nodes)
            {
                if (areaNode.Checked)
                {
                    int id = (int)areaNode.Tag;
                    areaIDs.Add(id);
                }
                else
                {
                    allArea = false;
                }
            }
            if (allArea)
            {
                condition.Areas[type] = null;
            }
            else
            {
                condition.Areas[type] = areaIDs;
                allTypes = false;
            }

            return allTypes;
        }

        public bool stopQuery { get; set; } = false;//在事件选择窗中取消查询
        /// <summary>
        /// 选择事件窗口，区分显示标题按历史问题点库和当前问题点
        /// </summary>
        /// <returns></returns>
        protected override bool prepareAskWhatEvent()
        {
            if (!hasSelectedEvents)  //第一个地市需要选择事件类型
            {
                EventChooserForm eventChooser = EventChooserForm.GetInstance(MainModel);
                if (IsForOldEvent)
                {
                    eventChooser.Text = "历史问题点库的事件选择";
                }
                else
                {
                    eventChooser.Text = "当前问题点的事件选择";
                }

                if (eventChooser.ShowDialog() == DialogResult.OK)
                {
                    showForm = eventChooser.ShowForm;
                    Condition.FilterOffValue9 = eventChooser.FilterOffValue9;
                    filterOffValue9 = eventChooser.FilterOffValue9;
                    if (eventChooser.SelectedEventIDs.Count > 0)
                    {
                        Condition.EventIDs = eventChooser.SelectedEventIDs;
                        selectedEvents = eventChooser.SelectedEventIDs;
                        hasSelectedEvents = true;
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
            }
            else   //随后的地市都沿用第一个地市所选择的事件类型
            {
                Condition.FilterOffValue9 = filterOffValue9;
                Condition.EventIDs = selectedEvents;
                return true;
            }
            stopQuery = true;
            return false;
        }

        protected override void query()
        {
            if (!prepareAskWhatEvent())
            {
                return;
            }
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.IsDrawEventResult = false;
                MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;
                MainModel.SelectedMessage = null;
                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }

        }
    }

    public class CutEvent : Event
    {
        public int eventid { get; set; }
        public int time { get; set; }
        public int iseqid { get; set; }
        public static CutEvent FillData(Content content)
        {
            CutEvent cutEvent = new CutEvent();
            cutEvent.FileID = content.GetParamInt();
            cutEvent.eventid = content.GetParamInt();
            cutEvent.time = content.GetParamInt();
            cutEvent.iseqid = content.GetParamInt();
            return cutEvent;
        }
    }

    public class DiySqlQueryCutEvent : DIYSQLBase
    {
        public DiySqlQueryCutEvent(MainModel mainModel, int starttime, int endtime, string projIdStr)
            : base(mainModel)
        {
            this.starttime = starttime;
            this.endtime = endtime;
            this.projIdStr = projIdStr;
        }

        public override string Name
        {
            get { return "DiySqlQueryCutEvent"; }
        }

        private int starttime { get; set; }
        private int endtime { get; set; }
        private string projIdStr { get; set; }
        /// <summary>
        /// 需要被剔除掉的事件
        /// </summary>
        public List<CutEvent> cutEvents { get; set; } = new List<CutEvent>();
        protected override string getSqlTextString()
        {
            string sql = string.Format("sp_gd_es_evnt_qry '{0}', '{1}','{2}','被叫位置更新' ",
                        starttime,
                        endtime,
                        projIdStr);
            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[5];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_String;
            return rType;
        }


        protected override void receiveRetData(ClientProxy clientProxy)
        {
            cutEvents.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    cutEvents.Add(CutEvent.FillData(package.Content));
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

    }

    public class LacCityName
    {
        public int lac { get; set; }
        public string cityName { get; set; }
        public static LacCityName FillData(Content content)
        {
            LacCityName lacCityName = new LacCityName();
            lacCityName.lac = content.GetParamInt();
            lacCityName.cityName = content.GetParamString();
            return lacCityName;
        }
    }

    public class DiySqlQueryLacCityName : DIYSQLBase
    {
        public DiySqlQueryLacCityName(MainModel mainModel,int districtID)
            : base(mainModel)
        {
            this.districtID = districtID;
        }

        public override string Name
        {
            get { return "DiySqlQueryLacCityName"; }
        }

        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password,districtID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                queryInThread(clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }

        }

        /// <summary>
        /// 查询LAC相关的地市名
        /// </summary>
        public List<CutEvent> cutEvents { get; set; } = new List<CutEvent>();
        public Dictionary<int, string> LacCityNameMap { get; set; } = new Dictionary<int, string>();
        int districtID { get; set; }
        protected override string getSqlTextString()
        {
            string sql = "SELECT DISTINCT lac,city FROM dbo.tb_gd_platform_lac";
            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[2];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_String;
            return rType;
        }


        protected override void receiveRetData(ClientProxy clientProxy)
        {
            LacCityNameMap.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    LacCityName lacCityName = LacCityName.FillData(package.Content);
                    if (!LacCityNameMap.ContainsKey(lacCityName.lac))
                        LacCityNameMap.Add(lacCityName.lac, lacCityName.cityName);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

    }
}
