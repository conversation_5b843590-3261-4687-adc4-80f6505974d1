﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc.StationAccept_Shanxi
{
    public class DiyQueryStationAcceptReport_SX : DIYSQLBase
    {
        public DiyQueryStationAcceptReport_SX(StationAcceptReportCondtion curCondtion, NetType type)
             : base()
        {
            MainDB = true;
            this.curCondtion = curCondtion;
            dbInfoList = new List<StationAcceptReportInfo>();
            tableName = ParamsHelper.GetRecordTableName(type);
        }

        public List<BtsAcceptRecordInfo_SX<string>> RecordList { get; set; }
        readonly List<StationAcceptReportInfo> dbInfoList;
        readonly StationAcceptReportCondtion curCondtion;
        readonly string tableName;

        protected override void queryInThread(object o)
        {
            base.queryInThread(o);
            doSomethingAfterQuery();
            WaitBox.Close();
        }

        protected override string getSqlTextString()
        {
            string sql = $"SELECT [DistrictName],[BtsName],[ENodeBID],[CoverTypeDes]," +
                $"[CellName],[CellID],[IsCellPassAccept] ,[CellErrorInfo],[IsBtsPassAccept]," +
                $"[BtsErrorInfo],[ExcelPath],[UpdateTime] FROM [{tableName}] " +
                $"where DistrictName in ({curCondtion.DistrictNames}) " +
                $"and UpdateTime between '{curCondtion.StartDate}' and '{curCondtion.EndDate}'";

            if (!string.IsNullOrEmpty(curCondtion.BtsNames))
            {
                sql += string.Format("and BtsName like '%{0}%'", curCondtion.BtsNames);
            }
            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[12];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_String;
            rType[5] = E_VType.E_Int;
            rType[6] = E_VType.E_Int;
            rType[7] = E_VType.E_String;
            rType[8] = E_VType.E_Int;
            rType[9] = E_VType.E_String;
            rType[10] = E_VType.E_String;
            rType[11] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    StationAcceptReportInfo info = new StationAcceptReportInfo();
                    info.FillDataByDB(package);
                    dbInfoList.Add(info);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        private void doSomethingAfterQuery()
        {
            var btsRecordDic = new Dictionary<int, BtsAcceptRecordInfo_SX<string>>();
            foreach (var info in dbInfoList)
            {
                if (!btsRecordDic.TryGetValue(info.ENodeBID, out var btsInfo))
                {
                    btsInfo = new BtsAcceptRecordInfo_SX<string>(info);
                    btsRecordDic.Add(info.ENodeBID, btsInfo);
                }
                if (!btsInfo.CellRecordInfoDic.TryGetValue(info.CellName, out var cellInfo))
                {
                    cellInfo = new CellAcceptRecordInfo_SX(info);
                    btsInfo.CellRecordInfoDic.Add(info.CellName, cellInfo);
                }
            }

            foreach (var item in btsRecordDic.Values)
            {
                item.CellRecordInfoList = new List<CellAcceptRecordInfo_SX>(item.CellRecordInfoDic.Values);
            }
            RecordList = new List<BtsAcceptRecordInfo_SX<string>>(btsRecordDic.Values);
        }
    }
}
