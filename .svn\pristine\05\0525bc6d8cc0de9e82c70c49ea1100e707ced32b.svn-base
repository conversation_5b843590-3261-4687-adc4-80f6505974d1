﻿using System;
using System.Collections.Generic;
using System.Text;
using DevExpress.XtraTreeList;
using System.Drawing;
using MasterCom.Util;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage.TestManager;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public abstract class AnaDealerBase
    {
        protected AnaDealerBase(AreaTestCondition anaCondition) { this.anaCondition = anaCondition; }

        public AreaTestCondition anaCondition { get; set; }

        public abstract void AnaArea(CAreaSummary village, AreaTestCondition condition);

        public abstract void MergeArea(CAreaSummary summary, CAreaSummary village);

        public abstract void CheckAchieve(CAreaSummary summary);

        public virtual int CreateColumn(TreeList treeList)
        {
            treeList.Columns.Clear();
            DevExpress.XtraTreeList.Columns.TreeListColumn colFixedArea = treeList.Columns.Add();
            colFixedArea.Tag = null;
            colFixedArea.Caption = "区域名称";
            colFixedArea.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
            colFixedArea.OptionsColumn.AllowEdit = false;
            colFixedArea.OptionsColumn.AllowMoveToCustomizationForm = false;
            colFixedArea.OptionsColumn.ReadOnly = true;
            colFixedArea.Width = 150;
            colFixedArea.Visible = true;
            colFixedArea.Fixed = DevExpress.XtraTreeList.Columns.FixedStyle.Left;

            DevExpress.XtraTreeList.Columns.TreeListColumn colFixedRank = treeList.Columns.Add();
            colFixedRank.Tag = null;
            colFixedRank.Caption = "行政级别";
            colFixedRank.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
            colFixedRank.OptionsColumn.AllowEdit = false;
            colFixedRank.OptionsColumn.AllowMoveToCustomizationForm = false;
            colFixedRank.OptionsColumn.ReadOnly = true;
            colFixedRank.Visible = true;
            colFixedRank.Fixed = DevExpress.XtraTreeList.Columns.FixedStyle.Left;

            return colFixedArea.Width + colFixedRank.Width;
        }

        public abstract object[] GetRowContext(CAreaSummary summary);

        public abstract CPermeate GetPermeate(CAreaSummary summary, object column);

        public abstract Color GetColor(CAreaSummary summary, object column);

        public abstract List<CAreaDetail> GetAreaDetail(CAreaSummary summary, Dictionary<AreaBase, CAreaSummary> areaSummaryMap);

        protected void appendPermeate(CAreaSummary summary, CAreaDetail detail, ECarrier car, CPermeate permeate)
        {
            detail.AppendPercent(string.Format("{0}%", permeate.DPermeate), car);
            int idxAchieve = 0, idxUnAchieve = 0;
            foreach (CAreaSummary village in summary.VillageVec)
            {
                Dictionary<ECarrier, CPermeate> villagePerDic = village.Permeate as Dictionary<ECarrier, CPermeate>;
                if (villagePerDic[car].BAchieve)
                {
                    idxAchieve++;
                }
                else
                {
                    idxUnAchieve++;
                }
            }
            detail.AppendAchieveArea(string.Format("{0}个", idxAchieve), car);
            detail.AppendUnAchieveArea(string.Format("{0}个", idxUnAchieve), car);
        }

        protected void appendSubArea(CAreaSummary summary, Dictionary<AreaBase, CAreaSummary> areaSummaryMap,
            CAreaDetail detail, ECarrier car, CPermeate permeate)
        {
            detail.AppendPercent(string.Format("{0}%", permeate.DPermeate), car);
            foreach (AreaBase sub in summary.Area.SubAreas)
            {
                if (areaSummaryMap.ContainsKey(sub))
                {
                    if (permeate.BAchieve)
                    {
                        detail.AppendAchieveArea(sub.Name, car);
                    }
                    else
                    {
                        detail.AppendUnAchieveArea(sub.Name, car);
                    }
                }
            }
        }
    }

    public class NoneAnaDealer : AnaDealerBase
    {
        public NoneAnaDealer(AreaTestCondition condition)
            : base(condition) { }

        public override void AnaArea(CAreaSummary village, AreaTestCondition condition)
        {
        }

        public override void MergeArea(CAreaSummary summary, CAreaSummary village)
        {
        }

        public override void CheckAchieve(CAreaSummary summary)
        {
        }

        public override object[] GetRowContext(CAreaSummary summary)
        {
            return new object[0];
        }

        public override CPermeate GetPermeate(CAreaSummary summary, object column)
        {
            return null;
        }

        public override Color GetColor(CAreaSummary summary, object column)
        {
            return Color.Empty;
        }

        public override List<CAreaDetail> GetAreaDetail(CAreaSummary summary, Dictionary<AreaBase, CAreaSummary> areaSummaryMap)
        {
            return new List<CAreaDetail>();
        }
    }

    public class IntegrityAnaDealer : AnaDealerBase
    {
        public IntegrityAnaDealer(IntegrityTestCondition condition)
            : base(condition) { }

        public override void AnaArea(CAreaSummary village, AreaTestCondition condition)
        {
            if (condition is IntegrityTestCondition)
            {
                village.CalcIntegrity(condition as IntegrityTestCondition);
            }
        }

        public override void MergeArea(CAreaSummary summary, CAreaSummary village)
        {
            if (summary.Permeate == null)
                summary.Permeate = new Dictionary<ECarrier, CPermeate>();
            Dictionary<ECarrier, CPermeate> carPerMap = summary.Permeate as Dictionary<ECarrier, CPermeate>;
            Dictionary<ECarrier, CPermeate> villagePerMap = village.Permeate as Dictionary<ECarrier, CPermeate>;
            foreach (ECarrier servCar in villagePerMap.Keys)
            {
                CPermeate per;
                if (!carPerMap.TryGetValue(servCar, out per))
                {
                    per = new CPermeate(servCar.ToString());
                    carPerMap[servCar] = per;
                }
                per.ITotalCnt++;
                per.IValidCnt += villagePerMap[servCar].IValidCnt;
            }
        }

        public override void CheckAchieve(CAreaSummary summary)
        {
            Dictionary<ECarrier, CPermeate> carPerMap = summary.Permeate as Dictionary<ECarrier, CPermeate>;

            foreach (CPermeate per in carPerMap.Values)
            {
                anaCondition.CheckAchieve(per, summary.Area.SubAreas == null);
            }
        }

        public override int CreateColumn(TreeList treeList)
        {
            int width = base.CreateColumn(treeList);

            int wPer = (treeList.Width - width) / 3;
            foreach (ECarrier car in Enum.GetValues(typeof(ECarrier)))
            {
                DevExpress.XtraTreeList.Columns.TreeListColumn col = treeList.Columns.Add();
                col.Tag = car;
                col.Caption = car.ToString();
                col.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
                col.OptionsColumn.AllowEdit = false;
                col.OptionsColumn.AllowMoveToCustomizationForm = false;
                col.OptionsColumn.ReadOnly = true;
                col.Visible = true;

                col.Width = wPer;
            }
            return treeList.Width;
        }

        public override object[] GetRowContext(CAreaSummary summary)
        {
            object[] rows = new object[2 + Enum.GetValues(typeof(ECarrier)).Length];
            int idx = 0;
            rows[idx++] = summary.Area;
            rows[idx++] = summary.Area.Rank;

            Dictionary<ECarrier, CPermeate> carPerDic = summary.Permeate as Dictionary<ECarrier, CPermeate>;
            foreach (ECarrier car in Enum.GetValues(typeof(ECarrier)))
            {
                if (summary.Area.Rank == ZTAreaManager.Instance.LowestRank)
                    rows[idx++] = carPerDic[car].StrDesc;
                else
                    rows[idx++] = carPerDic[car].DPermeate;
            }
            return rows;
        }

        public override CPermeate GetPermeate(CAreaSummary summary, object column)
        {
            ECarrier car = (ECarrier)column;
            Dictionary<ECarrier, CPermeate> carPerMap = summary.Permeate as Dictionary<ECarrier, CPermeate>;

            return carPerMap[car];
        }

        public override Color GetColor(CAreaSummary summary, object column)
        {
            return anaCondition.GetShowColor(GetPermeate(summary, column).BAchieve);
        }

        public override List<CAreaDetail> GetAreaDetail(CAreaSummary summary, Dictionary<AreaBase, CAreaSummary> areaSummaryMap)
        {
            bool isVallige = summary.Area.Rank == ZTAreaManager.Instance.LowestRank;

            List<CAreaDetail> rtDetailVec = new List<CAreaDetail>();

            Dictionary<ECarrier, CPermeate> carPerDic = summary.Permeate as Dictionary<ECarrier, CPermeate>;
            CAreaDetail detail = new CAreaDetail(summary);
            rtDetailVec.Add(detail);

            foreach (ECarrier car in carPerDic.Keys)
            {
                CPermeate permeate = carPerDic[car];
                if (isVallige)
                {
                    detail.AppendPercent(permeate.StrDesc, car);
                }
                else if (summary.Area.Rank == ZTAreaManager.Instance.LowestRank.ParentRank)
                {
                    appendPermeate(summary, detail, car, permeate);
                }
                else
                {
                    appendSubArea(summary, areaSummaryMap, detail, car, permeate);
                }
            }
            return rtDetailVec;
        }


    }

    public class AlarmTestAnaDealer : AnaDealerBase
    {
        public AlarmTestAnaDealer(AlarmTestCondition condition)
            : base(condition) { }

        public override void AnaArea(CAreaSummary village, AreaTestCondition condition)
        {
            if (condition is AlarmTestCondition)
            {
                village.CalcTestAlarm(condition as AlarmTestCondition);
            }
        }

        public override void MergeArea(CAreaSummary summary, CAreaSummary village)
        {
            if (summary.Permeate == null)
                summary.Permeate = new Dictionary<ECarrier, CPermeate>();
            Dictionary<ECarrier, CPermeate> servPerMap = summary.Permeate as Dictionary<ECarrier, CPermeate>;
            Dictionary<ECarrier, CPermeate> villagePerMap = village.Permeate as Dictionary<ECarrier, CPermeate>;
            foreach (ECarrier servCar in villagePerMap.Keys)
            {
                CPermeate per;
                if (!servPerMap.TryGetValue(servCar, out per))
                {
                    per = new CPermeate(servCar.ToString());
                    servPerMap[servCar] = per;
                }
                per.ITotalCnt++;
                per.IValidCnt += villagePerMap[servCar].IValidCnt;
            }
        }

        public override void CheckAchieve(CAreaSummary summary)
        {
            Dictionary<ECarrier, CPermeate> carPerMap = summary.Permeate as Dictionary<ECarrier, CPermeate>;

            foreach (CPermeate per in carPerMap.Values)
            {
                anaCondition.CheckAchieve(per, summary.Area.SubAreas == null);
            }
        }

        public override int CreateColumn(TreeList treeList)
        {
            int width = base.CreateColumn(treeList);

            int wPer = (treeList.Width - width) / 3;
            foreach (ECarrier car in Enum.GetValues(typeof(ECarrier)))
            {
                DevExpress.XtraTreeList.Columns.TreeListColumn col = treeList.Columns.Add();
                col.Tag = car;
                col.Caption = car.ToString();
                col.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
                col.OptionsColumn.AllowEdit = false;
                col.OptionsColumn.AllowMoveToCustomizationForm = false;
                col.OptionsColumn.ReadOnly = true;
                col.Visible = true;

                col.Width = wPer;
            }
            return treeList.Width;
        }

        public override object[] GetRowContext(CAreaSummary summary)
        {
            object[] rows = new object[2 + Enum.GetValues(typeof(ECarrier)).Length];
            int idx = 0;
            rows[idx++] = summary.Area;
            rows[idx++] = summary.Area.Rank;

            Dictionary<ECarrier, CPermeate> carPerDic = summary.Permeate as Dictionary<ECarrier, CPermeate>;
            foreach (ECarrier car in Enum.GetValues(typeof(ECarrier)))
            {
                if (summary.Area.Rank == ZTAreaManager.Instance.LowestRank)
                    rows[idx++] = carPerDic[car].StrDesc;
                else
                    rows[idx++] = carPerDic[car].DPermeate;
            }
            return rows;
        }

        public override CPermeate GetPermeate(CAreaSummary summary, object column)
        {
            ECarrier car = (ECarrier)column;
            Dictionary<ECarrier, CPermeate> carPerMap = summary.Permeate as Dictionary<ECarrier, CPermeate>;

            return carPerMap[car];
        }

        public override Color GetColor(CAreaSummary summary, object column)
        {
            return anaCondition.GetShowColor(GetPermeate(summary, column).BAchieve);
        }

        public override List<CAreaDetail> GetAreaDetail(CAreaSummary summary, Dictionary<AreaBase, CAreaSummary> areaSummaryMap)
        {
            bool isVallige = summary.Area.Rank == ZTAreaManager.Instance.LowestRank;

            List<CAreaDetail> rtDetailVec = new List<CAreaDetail>();

            Dictionary<ECarrier, CPermeate> carPerDic = summary.Permeate as Dictionary<ECarrier, CPermeate>;
            CAreaDetail detail = new CAreaDetail(summary);
            rtDetailVec.Add(detail);

            foreach (ECarrier car in carPerDic.Keys)
            {
                CPermeate permeate = carPerDic[car];
                if (isVallige)
                {
                    detail.AppendPercent(permeate.StrDesc, car);
                }
                else if (summary.Area.Rank == ZTAreaManager.Instance.LowestRank.ParentRank)
                {
                    appendPermeate(summary, detail, car, permeate);
                }
                else
                {
                    appendSubArea(summary, areaSummaryMap, detail, car, permeate);
                }
            }
            return rtDetailVec;
        }
    }

    public class WorkTestAnaDealer : AnaDealerBase
    {
        public WorkTestAnaDealer(WorkTestCondition condition)
            : base(condition) { }

        public override void AnaArea(CAreaSummary village, AreaTestCondition condition)
        {
            if (condition is WorkTestCondition)
            {
                village.CalcTestWork(condition as WorkTestCondition);
            }
        }

        public override void MergeArea(CAreaSummary summary, CAreaSummary village)
        {
            if (summary.Permeate == null)
                summary.Permeate = new Dictionary<TimePeriod, Dictionary<ECarrier, CPermeate>>();
            Dictionary<TimePeriod, Dictionary<ECarrier, CPermeate>> timeServCarPerMap = summary.Permeate as Dictionary<TimePeriod, Dictionary<ECarrier, CPermeate>>;
            Dictionary<TimePeriod, Dictionary<ECarrier, CPermeate>> villagePerMap = village.Permeate as Dictionary<TimePeriod, Dictionary<ECarrier, CPermeate>>;
            foreach (TimePeriod tPeriod in villagePerMap.Keys)
            {
                Dictionary<ECarrier, CPermeate> serPerMap;
                if (!timeServCarPerMap.TryGetValue(tPeriod, out serPerMap))
                {
                    serPerMap = new Dictionary<ECarrier, CPermeate>();
                    timeServCarPerMap[tPeriod] = serPerMap;
                }
                foreach (ECarrier serCar in villagePerMap[tPeriod].Keys)
                {
                    CPermeate per;
                    if (!serPerMap.TryGetValue(serCar, out per))
                    {
                        per = new CPermeate(serCar.ToString(), "有测", "未测");
                        serPerMap[serCar] = per;
                    }
                    per.ITotalCnt++;
                    per.IValidCnt += villagePerMap[tPeriod][serCar].IValidCnt;
                }
            }
        }

        public override void CheckAchieve(CAreaSummary summary)
        {
            Dictionary<TimePeriod, Dictionary<ECarrier, CPermeate>> timeServCarPerMap = summary.Permeate as Dictionary<TimePeriod, Dictionary<ECarrier, CPermeate>>;

            foreach (Dictionary<ECarrier, CPermeate> carPerDic in timeServCarPerMap.Values)
            {
                foreach (CPermeate per in carPerDic.Values)
                {
                    anaCondition.CheckAchieve(per, summary.Area.SubAreas == null);
                }
            }
        }

        public override int CreateColumn(TreeList treeList)
        {
            int width = base.CreateColumn(treeList);

            WorkTestCondition cond = anaCondition as WorkTestCondition;

            int wPer = (treeList.Width - width) / (cond.TimeVec.Count * 3);
            wPer = wPer > 100 ? wPer : 100;
            foreach (TimePeriod tp in cond.TimeVec)
            {
                foreach (ECarrier car in Enum.GetValues(typeof(ECarrier)))
                {
                    CtpCarBind bind = new CtpCarBind(tp, car);
                    DevExpress.XtraTreeList.Columns.TreeListColumn col = treeList.Columns.Add();
                    col.Width = wPer;
                    col.Tag = bind;
                    col.Caption = bind.ToString();
                    col.UnboundType = DevExpress.XtraTreeList.Data.UnboundColumnType.Object;
                    col.OptionsColumn.AllowEdit = false;
                    col.OptionsColumn.AllowMoveToCustomizationForm = false;
                    col.OptionsColumn.ReadOnly = true;
                    col.Visible = true;
                }
            }
            return treeList.Width;
        }

        public override object[] GetRowContext(CAreaSummary summary)
        {
            WorkTestCondition cond = anaCondition as WorkTestCondition;
            object[] rows = new object[2 + Enum.GetValues(typeof(ECarrier)).Length * cond.TimeVec.Count];
            int idx = 0;
            rows[idx++] = summary.Area;
            rows[idx++] = summary.Area.Rank;

            Dictionary<TimePeriod, Dictionary<ECarrier, CPermeate>> timeServCarPerMap = summary.Permeate as Dictionary<TimePeriod, Dictionary<ECarrier, CPermeate>>;
            
            foreach (TimePeriod tp in timeServCarPerMap.Keys)
            {
                foreach (ECarrier car in timeServCarPerMap[tp].Keys)
                {
                    if (summary.Area.Rank == ZTAreaManager.Instance.LowestRank)
                        rows[idx++] = timeServCarPerMap[tp][car].StrDesc;
                    else
                        rows[idx++] = timeServCarPerMap[tp][car].DPermeate;
                }
            }
            return rows;
        }

        public override CPermeate GetPermeate(CAreaSummary summary, object column)
        {
            CtpCarBind bind = column as CtpCarBind;

            TimePeriod tp = bind.Time;
            ECarrier car = bind.Car;
            Dictionary<TimePeriod, Dictionary<ECarrier, CPermeate>> timeServCarPerMap = summary.Permeate as Dictionary<TimePeriod, Dictionary<ECarrier, CPermeate>>;

            return timeServCarPerMap[tp][car];
        }

        public override Color GetColor(CAreaSummary summary, object column)
        {
            return anaCondition.GetShowColor(GetPermeate(summary, column).BAchieve);
        }

        public override List<CAreaDetail> GetAreaDetail(CAreaSummary summary, Dictionary<AreaBase, CAreaSummary> areaSummaryMap)
        {
            bool isVallige = summary.Area.Rank == ZTAreaManager.Instance.LowestRank;

            List<CAreaDetail> rtDetailVec = new List<CAreaDetail>();

            Dictionary<TimePeriod, Dictionary<ECarrier, CPermeate>> timeServCarPerMap = summary.Permeate as Dictionary<TimePeriod, Dictionary<ECarrier, CPermeate>>;
            foreach (TimePeriod period in timeServCarPerMap.Keys)
            {
                CAreaDetail detail = new CAreaDetail(summary);
                rtDetailVec.Add(detail);

                foreach (ECarrier car in timeServCarPerMap[period].Keys)
                {
                    CPermeate permeate = timeServCarPerMap[period][car];
                    if (isVallige)
                    {
                        detail.AppendPercent(permeate.StrDesc, car);
                    }
                    else if (summary.Area.Rank == ZTAreaManager.Instance.LowestRank.ParentRank)
                    {
                        appendPermeate(summary, period, detail, car, permeate);
                    }
                    else
                    {
                        appendSubArea(summary, areaSummaryMap, detail, car, permeate);
                    }
                }
            }
            return rtDetailVec;
        }

        private void appendPermeate(CAreaSummary summary, TimePeriod period, CAreaDetail detail, ECarrier car, CPermeate permeate)
        {
            detail.AppendPercent(string.Format("{0}%", permeate.DPermeate), car);
            int idxAchieve = 0, idxUnAchieve = 0;
            foreach (CAreaSummary village in summary.VillageVec)
            {
                Dictionary<TimePeriod, Dictionary<ECarrier, CPermeate>> villagePerDic = village.Permeate as Dictionary<TimePeriod, Dictionary<ECarrier, CPermeate>>;
                if (villagePerDic[period][car].BAchieve)
                {
                    idxAchieve++;
                }
                else
                {
                    idxUnAchieve++;
                }
            }
            detail.AppendAchieveArea(string.Format("{0}个", idxAchieve), car);
            detail.AppendUnAchieveArea(string.Format("{0}个", idxUnAchieve), car);
        }
    }
}
