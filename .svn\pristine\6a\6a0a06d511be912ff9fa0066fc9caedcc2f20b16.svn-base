﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRVideoPlayAnaBase : DIYAnalyseFilesOneByOneByRegion
    {
        public NRVideoPlayAnaBase(MainModel mainModel)
            : base(mainModel)
        {
            this.FilterEventByRegion = false;
            this.IncludeMessage = true;
            this.IncludeTestPoint = true;
            this.IncludeEvent = true;
        }

        //protected string RsrpStr = "";
        //protected string SinrStr = "";
        //protected string NCellRsrpStr = "";
        //protected string AppSpeedStr = "";
        //protected string TacStr = "";

        protected List<NRVideoPlayAnaItem> rebufferAnaList = new List<NRVideoPlayAnaItem>();
        protected List<NRVideoPlayAnaItem> loadAnaList = new List<NRVideoPlayAnaItem>();
        protected NRVideoPlayAnaCondition videoPlayCondition = new NRVideoPlayAnaCondition();
        protected string themeName = "";

        /// <summary>
        /// 设置参数和业务类型
        /// </summary>
        protected virtual void setParmAndServiceType()
        {
            Columns = NRTpHelper.InitBaseReplayParamBackground(false, true);
            Columns.Add("NR_APP_type");
            Columns.Add("NR_APP_Speed");
            Columns.Add("NR_APP_Status");

            themeName = "NR:SS_RSRP";

            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
        }

        protected override bool getCondition()
        {
            setParmAndServiceType();

            NRVideoPlayAnaDlg dlg = new NRVideoPlayAnaDlg(videoPlayCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                videoPlayCondition = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            rebufferAnaList = new List<NRVideoPlayAnaItem>();
            loadAnaList = new List<NRVideoPlayAnaItem>();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                List<Event> evtsList = fileMng.Events;
                bool noRebuffer = true;//是否是播放开始后的第一次卡顿
                string lastURL = "";    //最近一次流媒体播放URL
                DateTime lastLoadTime = new DateTime(); //最近一次加载完成时间
                DateTime lastRebufferEndTime = new DateTime(); //上一次卡顿结束时间
                NRVideoPlayAnaItem curRebufferItem = null;   //流媒体卡顿分析单元
                NRVideoPlayAnaItem curLoadItem = null;   //流媒体缓冲分析单元

                foreach (Event evt in evtsList)
                {
                    switch (evt.ID)
                    {
                        //卡顿开始事件
                        case (int)NREventManager.VideoRebufferStart:
                            dealRebufferStart(fileMng, ref noRebuffer, lastURL, lastLoadTime, lastRebufferEndTime, ref curRebufferItem, evt);
                            break;
                        //卡顿结束事件
                        case (int)NREventManager.VideoRebufferEnd:
                            lastRebufferEndTime = RebufferEnd(fileMng, ref curRebufferItem, evt);
                            break;
                        //流媒体请求事件（缓冲开始）
                        case (int)NREventManager.VideoRequest:
                            lastURL = dealRequest(fileMng, ref curLoadItem, evt);
                            break;
                        //流媒体加载成功（缓冲结束）
                        case (int)NREventManager.VideoReproductionStart:
                            lastLoadTime = dealReproductionStart(fileMng, ref curLoadItem, evt);
                            break;
                        //流媒体播放完成
                        case (int)NREventManager.VideoLastData:
                        case (int)NREventManager.VideoDrop:
                            lastURL = dealLastData(fileMng, ref noRebuffer, evt);
                            break;
                        //流媒体开始播放
                        case (int)NREventManager.VideoFirstData:
                            break;
                    }
                }
            }
        }

        private DateTime RebufferEnd(DTFileDataManager fileMng, ref NRVideoPlayAnaItem curRebufferItem, Event evt)
        {
            DateTime lastRebufferEndTime = evt.DateTime;
            if (curRebufferItem != null)
            {
                curRebufferItem.EndTime = evt.DateTime;
                curRebufferItem.InterTime = calcInterSeconds(curRebufferItem.BeginTime,
                    curRebufferItem.EndTime); //获取卡顿时长
                curRebufferItem.TpsList = getTestPoinsByTime(curRebufferItem.BeginTime.AddSeconds(-videoPlayCondition.PreRebufferTime),
                    curRebufferItem.EndTime, fileMng.TestPoints, false);          //获取卡顿开始前几秒到卡顿结束的采样点                          
                setEndInfo(evt, ref curRebufferItem, rebufferAnaList);
            }
            return lastRebufferEndTime;
        }

        private string dealRequest(DTFileDataManager fileMng, ref NRVideoPlayAnaItem curLoadItem, Event evt)
        {
            string lastURL;
            setStartInfo(evt, ref curLoadItem);
            curLoadItem.URL = ZTUrlAnalyzer.GetURL(evt.SN, fileMng.Messages);
            lastURL = curLoadItem.URL;
            return lastURL;
        }

        private DateTime dealReproductionStart(DTFileDataManager fileMng, ref NRVideoPlayAnaItem curLoadItem, Event evt)
        {
            DateTime lastLoadTime = evt.DateTime;
            if (curLoadItem != null)
            {
                curLoadItem.EndTime = evt.DateTime;
                curLoadItem.InterTime = calcInterSeconds(curLoadItem.BeginTime, curLoadItem.EndTime); //获取缓冲时长
                curLoadItem.TpsList = getTestPoinsByTime(curLoadItem.BeginTime.AddSeconds(-videoPlayCondition.PreLoadTime),
                    curLoadItem.EndTime, fileMng.TestPoints, false);//获取缓冲开始前几秒到缓冲结束的采样点
                setEndInfo(evt, ref curLoadItem, loadAnaList);
            }

            return lastLoadTime;
        }

        private void dealRebufferStart(DTFileDataManager fileMng, ref bool noRebuffer, string lastURL, DateTime lastLoadTime, DateTime lastRebufferEndTime, ref NRVideoPlayAnaItem curRebufferItem, Event evt)
        {
            setStartInfo(evt, ref curRebufferItem);
            curRebufferItem.URL = lastURL;
            if (noRebuffer)
            {
                List<TestPoint> tpList = getTestPoinsByTime(lastLoadTime, evt.DateTime,
                    fileMng.TestPoints, true);
                //获取加载完成到第一次卡顿的采样点
                if (tpList != null && tpList.Count != 0)
                {
                    curRebufferItem.OtherTpsList.AddRange(tpList);  //加入到非卡顿采样点
                    curRebufferItem.fillOtherMaxNBIndexList(tpList);
                }
            }
            else
            {
                List<TestPoint> tpList = getTestPoinsByTime(lastRebufferEndTime, evt.DateTime,
                    fileMng.TestPoints, true);
                //获取第N-1次卡顿结束到第N次卡顿开始的采样点 
                if (tpList != null && tpList.Count != 0)
                {
                    curRebufferItem.OtherTpsList.AddRange(tpList);  //加入到非卡顿采样点
                    curRebufferItem.fillOtherMaxNBIndexList(tpList);
                }
            }
            noRebuffer = false;
        }

        private string dealLastData(DTFileDataManager fileMng, ref bool noRebuffer, Event evt)
        {
            string lastURL;
            lastURL = "";   //流媒体播放完成重新获取URL
            if (rebufferAnaList.Count != 0 && !noRebuffer)
            {
                //如果这次播放周期里有卡顿事件
                int lastIndex = rebufferAnaList.Count - 1;
                List<TestPoint> tpList = getTestPoinsByTime(rebufferAnaList[lastIndex].EndTime, evt.DateTime,
                    fileMng.TestPoints, true);//截取最后一次卡顿结束到播放结束的采样点
                if (tpList != null && tpList.Count != 0)
                {
                    rebufferAnaList[lastIndex].OtherTpsList.AddRange(tpList); //加入非卡顿采样点
                    rebufferAnaList[lastIndex].fillOtherMaxNBIndexList(tpList);
                }
            }
            if (loadAnaList.Count != 0 && noRebuffer)
            {
                //如果这次播放周期里没卡顿事件则有非缓冲周期
                int lastIndex = loadAnaList.Count - 1;
                List<TestPoint> tpList = getTestPoinsByTime(loadAnaList[lastIndex].EndTime,
                    evt.DateTime, fileMng.TestPoints, true);//截取缓冲结束到播放开始的采样点
                if (tpList != null && tpList.Count != 0)
                {
                    loadAnaList[lastIndex].OtherTpsList.AddRange(tpList); //加入非缓冲采样点
                    loadAnaList[lastIndex].fillOtherMaxNBIndexList(tpList);
                }
            }
            noRebuffer = true;   //重新开始新的流媒体播放周期
            return lastURL;
        }

        /// <summary>
        /// 根据前后时间获取采样点
        /// </summary>
        /// <param name="period"></param>
        /// <param name="tps"></param>
        /// <param name="countInRegion">是否需要判断在区域内</param>
        /// <returns></returns>
        protected List<TestPoint> getTestPoinsByTime(DateTime beginTime, DateTime endTime, List<TestPoint> tps, bool countInRegion)
        {
            TimePeriod period = new TimePeriod();
            List<TestPoint> ret = new List<TestPoint>();
            if (!period.SetPeriod(beginTime, endTime))    //开始时间和结束时间不合逻辑
            {
                return ret;
            }
            for (int index = 0; index < tps.Count; index++)
            {
                TestPoint tp = tps[index];
                if (tp.DateTime >= period.BeginTime && tp.DateTime <= period.EndTime)
                {
                    if (countInRegion && !isValidTestPoint(tp))
                    {
                        continue;
                    }
                    ret.Add(tp);
                }
                else if (tp.DateTime > period.EndTime)
                {
                    break;
                }
            }
            return ret;
        }

        /// <summary>
        /// 设置开始信息
        /// </summary>
        /// <param name="evt">开始事件</param>
        /// <param name="anaItem">分析单元</param>
        private void setStartInfo(Event evt, ref NRVideoPlayAnaItem anaItem)
        {
            anaItem = new NRVideoPlayAnaItem(evt.FileName);
            anaItem.BeginTime = evt.DateTime;
            anaItem.BeginLongitude = evt.Longitude;
            anaItem.BeginLatitude = evt.Latitude;
        }

        protected static double calcInterSeconds(DateTime beginTime, DateTime endTime)
        {
            TimeSpan ts = endTime.Subtract(beginTime);
            return ts.TotalSeconds;
        }

        /// <summary>
        /// 设置结束信息
        /// </summary>
        /// <param name="evt">结束事件</param>
        /// <param name="anaItem">分析单元</param>
        /// <param name="list">结果列表</param>
        private void setEndInfo(Event evt, ref NRVideoPlayAnaItem anaItem, List<NRVideoPlayAnaItem> list)
        {
            if (isValidPeriod(anaItem.TpsList))
            {
                anaItem.FillItem();
                anaItem.SN = list.Count + 1;
                anaItem.EndLatitude = evt.Latitude;
                anaItem.EndLongitude = evt.Longitude;
                anaItem.StatTpsList = getVaildTestPoints(anaItem.TpsList, anaItem.MaxNBIndexList);
                list.Add(anaItem);
            }
            anaItem = null;
        }

        /// <summary>
        /// 判断时间段的采样点存在区域内
        /// </summary>
        /// <param name="tpsList"></param>
        /// <returns></returns>
        protected virtual bool isValidPeriod(List<TestPoint> tpsList)
        {
            foreach (TestPoint tp in tpsList)
            {
                if (isValidTestPoint(tp))
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 筛选在区域内的采样点
        /// </summary>
        /// <param name="tpList"></param>
        /// <returns></returns>
        protected List<KeyValuePair<TestPoint, int>> getVaildTestPoints(List<TestPoint> tpList, List<int> maxNBIndexList)
        {
            List<KeyValuePair<TestPoint, int>> ret = new List<KeyValuePair<TestPoint, int>>();
            if (tpList.Count == maxNBIndexList.Count)
            {
                for (int i = 0; i < tpList.Count; i++)
                {
                    if (isValidTestPoint(tpList[i]))
                    {
                        ret.Add(new KeyValuePair<TestPoint, int>(tpList[i], maxNBIndexList[i]));
                    }
                }
            }
            return ret;
        }

        protected override void fireShowForm()
        {
            if (rebufferAnaList.Count == 0 && loadAnaList.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }
            MainModel.FireSetDefaultMapSerialTheme(themeName);
            NRVideoPlayAnaForm frm = MainModel.CreateResultForm(typeof(NRVideoPlayAnaForm)) as NRVideoPlayAnaForm;
            frm.FillData(rebufferAnaList, loadAnaList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    class NRVideoPlayAnaByFile : NRVideoPlayAnaBase
    {
        public NRVideoPlayAnaByFile(MainModel mainModel)
            : base(mainModel)
        {

        }
        private static NRVideoPlayAnaByFile instance = null;
        protected static readonly object lockObj = new object();
        public static NRVideoPlayAnaByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRVideoPlayAnaByFile(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "流媒体卡顿缓冲分析(按文件)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22102, this.Name);
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidPeriod(List<TestPoint> tpsList)
        {
            return true;
        }
    }

    class NRVideoPlayAnaByRegion : NRVideoPlayAnaBase
    {
        public NRVideoPlayAnaByRegion(MainModel mm)
            : base(mm)
        {

        }
        private static NRVideoPlayAnaByRegion instance = null;
        protected static readonly object lockObj = new object();
        public static NRVideoPlayAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRVideoPlayAnaByRegion(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "流媒体卡顿缓冲分析(按区域)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35053, this.Name);
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }
    }
}
