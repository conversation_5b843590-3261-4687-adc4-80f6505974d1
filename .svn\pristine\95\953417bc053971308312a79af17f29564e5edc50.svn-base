﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.Data.Sql;
using System.Data.SqlTypes;
using System.Data.SqlClient;

using MasterCom.Util;
using MasterCom.RAMS.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using System.IO;

namespace MasterCom.RAMS.ZTFunc
{
    public class CellAssociationKPIQuery
    {
        public CellAssociationKPIResult QueryReport(CellAssociationKPIReport report, List<TimePeriod> timePeriods)
        {
            CellAssociationKPIResult result = new CellAssociationKPIResult(report.NetType);
            try
            {
                FillKpiDic();

                DataTable kpiTable = null;
                StringBuilder kpiSqlTextSb = new StringBuilder();
                foreach (TimePeriod tp in timePeriods)
                {
                    string kpiSqlText = GetKpiSql(report, tp);
                    kpiSqlTextSb.AppendLine(kpiSqlText);
                    DataTable tmpKpiTable = GetKpiTable(kpiSqlText);
                    if (kpiTable == null)
                    {
                        kpiTable = tmpKpiTable;
                    }
                    else
                    {
                        kpiTable.Merge(tmpKpiTable);
                    }
                }
#if DEBUG
                //DebugTable(kpiTable, "CellAssociationKPI.csv");
#endif
                DataTable cellTable = GetCellTable(report.NetType);
                DataTable resultTable = JoinTables(report, kpiTable, cellTable);

                result.KPISqlText = kpiSqlTextSb.ToString();
                result.ResultTable = resultTable;
                result.ColumnInfoMap = GetColumnInfo(report);

            }
            catch (Exception ex)
            {
                result.Error = ex;
            }

            return result;
        }

        public CellAssociationKPIResult QueryReport(CellAssociationKPIReport report, TimePeriod timePeriod)
        {
            CellAssociationKPIResult result = new CellAssociationKPIResult(report.NetType);
            try
            {
                FillKpiDic();

                string kpiSqlText = GetKpiSql(report, timePeriod);
                result.KPISqlText = kpiSqlText;

                DataTable kpiTable = GetKpiTable(kpiSqlText);
                DataTable cellTable = GetCellTable(report.NetType);
                DataTable resultTable = JoinTables(report, kpiTable, cellTable);

                result.ResultTable = resultTable;
                result.ColumnInfoMap = GetColumnInfo(report);

            }
            catch (Exception ex)
            {
                result.Error = ex;
            }

            return result;
        }

        protected string GetKpiSql(CellAssociationKPIReport report, TimePeriod timePeriod)
        {
            int timeId = (int)report.TimeType;
            int objectId = (int)report.NetType;

            StringBuilder sb = new StringBuilder();
            foreach (CellAssociationKPIField field in report.FieldList)
            {
                string[] kpiKeyParts = field.KpiKey.Split(':');
                string kpiId = kpiKeyParts[3];

                sb.Append(string.Format("{0},{1},{2};", kpiId, timeId, objectId));
            }

            string timeString = string.Format("{0}|{1}", timePeriod.BeginTime.ToString("yyyy-MM-dd HH:mm:ss"), 
                timePeriod.EndTime.ToString("yyyy-MM-dd HH:mm:ss"));

            string sqlText = string.Format("exec PROC_通用查询_ExecQuerySql {0}, {1}, '{2}', '{3}', '', '1, 1', '{4}', '', 1, ''",
                timeId, objectId, sb.ToString().TrimEnd(';'), timeString, objectId);
            return sqlText;
        }

        protected DataTable GetKpiTable(string sqlText)
        {
            DataSet ds = new DataSet();
            SqlHelper.FillDataset(CellAssociationKPIConfig.Instance.ConnectionString, CommandType.Text, sqlText, ds, new string[] { "KpiTable" });
            return ds.Tables[0];
        }

        protected DataTable GetCellTable(ECellAssociationKPINetType netType)
        {
            string sqlText = null;
            switch (netType)
            {
                case ECellAssociationKPINetType.GSM:
                    sqlText = "select * from [MTNOH_AAA_Resource2].[dbo].[TB_GSM_小区标识]";
                    break;
                case ECellAssociationKPINetType.TD:
                    sqlText = "select * from [MTNOH_AAA_Resource2].[dbo].[TB_TD_小区标识]";
                    break;
                case ECellAssociationKPINetType.LTE:
                    sqlText = "select * from [MTNOH_AAA_Resource2].[dbo].[TB_LTE_小区标识]";
                    break;
            }

            DataSet ds = new DataSet();
            SqlHelper.FillDataset(CellAssociationKPIConfig.Instance.ConnectionString, CommandType.Text, sqlText, ds, new string[] { "CellTable" });
            return ds.Tables[0];
        }

        protected DataTable JoinTables(CellAssociationKPIReport report, DataTable kpiTable, DataTable cellTable)
        {
            // build columns
            DataTable resultTable = new DataTable("ResultTable");
            resultTable.Columns.Add("时间", typeof(DateTime));
            for (int i = 0, j = 2; i < report.FieldList.Count && j < kpiTable.Columns.Count; ++i, ++j) // kpiTable从index = 2开始为自定义指标
            {
                DataColumn kpiColumn = kpiTable.Columns[j];
                CellAssociationKPIField field = report.FieldList[i];
                string columnName = field.ShowName == null ? field.KpiName : field.ShowName;
                resultTable.Columns.Add(columnName, kpiColumn.DataType);
            }
            resultTable.Columns.Add("Arg1", typeof(int)); // 小区检索参数
            resultTable.Columns.Add("Arg2", typeof(int));

            // cell index dic
            Dictionary<int, List<DataRow>> cellTableDic = new Dictionary<int, List<DataRow>>();
            foreach (DataRow dr in cellTable.Rows)
            {
                int cellId = (int)dr["自维护ID"];
                if (!cellTableDic.ContainsKey(cellId))
                {
                    cellTableDic.Add(cellId, new List<DataRow>());
                }
                cellTableDic[cellId].Add(dr);
            }

            // fill data
            foreach (DataRow kpiRow in kpiTable.Rows)
            {
                if (Convert.IsDBNull(kpiRow["时间"]) || Convert.IsDBNull(kpiRow["iCellID"]))
                {
                    continue;
                }
                DateTime time = (DateTime)kpiRow["时间"];
                int iCellID = (int)kpiRow["iCellID"];

                if (cellTableDic.ContainsKey(iCellID))
                {
                    addResultTableRow(report, resultTable, cellTableDic, kpiRow, time, iCellID);
                }
            }

            //DebugTable(resultTable, "AssociationResultTable.csv");
            //DebugTable(kpiTable, "AssociationKPITable.csv");
            //DebugTable(cellTable, "AssociationCellTable.csv");    
            return resultTable;
        }

        private void addResultTableRow(CellAssociationKPIReport report, DataTable resultTable, 
            Dictionary<int, List<DataRow>> cellTableDic, DataRow kpiRow, DateTime time, int iCellID)
        {
            DataRow cellRow = null;
            foreach (DataRow dr in cellTableDic[iCellID])
            {
                if ((DateTime)dr["begin_time"] <= time && (DateTime)dr["end_time"] >= time)
                {
                    cellRow = dr;
                    break;
                }
            }

            if (cellRow != null)
            {
                List<object> resultRow = getResultRow(report, kpiRow, cellRow);

                resultTable.Rows.Add(resultRow.ToArray());
            }
        }

        private List<object> getResultRow(CellAssociationKPIReport report, DataRow kpiRow, DataRow cellRow)
        {
            List<object> resultRow = new List<object>(kpiRow.ItemArray);
            resultRow.RemoveAt(1); // 去掉iCellID的值
            if (report.NetType == ECellAssociationKPINetType.LTE)
            {
                resultRow.Add(cellRow["ENBID"]);        // if cellRows.Length == 0, let error throw
                resultRow.Add(cellRow["扇区序号"]);
            }
            else
            {
                resultRow.Add(cellRow["LAC"]);
                resultRow.Add(cellRow["CI"]);
            }

            return resultRow;
        }

        protected void DebugTable(DataTable dt, string xlsFile)
        {
            if (System.IO.File.Exists(xlsFile))
            {
                System.IO.File.Delete(xlsFile);
            }
            using (System.IO.StreamWriter sw = new System.IO.StreamWriter(xlsFile, false, Encoding.Default))
            {
                foreach (DataColumn dc in dt.Columns)
                {
                    sw.Write(dc.Caption);
                    if (dc != dt.Columns[dt.Columns.Count - 1]) sw.Write(",");
                }
                sw.WriteLine();
                foreach (DataRow dr in dt.Rows)
                {
                    for (int i = 0; i < dt.Columns.Count; ++i)
                    {
                        sw.Write(dr[i]);
                        if (i != dt.Columns.Count - 1) sw.Write(",");
                    }
                    sw.WriteLine();
                }
            }
        }

        protected Dictionary<string, object> GetColumnInfo(CellAssociationKPIReport report)
        {
            Dictionary<string, object> retDic = new Dictionary<string, object>();
            foreach (CellAssociationKPIField field in report.FieldList)
            {
                CellAssociationKPIItem kpi = null;
                if (!kpiDic.TryGetValue(field.KpiKey, out kpi) || !kpi.HasExtra) // 未设置扩展属性（颜色区间)
                {
                    continue;
                }

                string showName = field.ShowName == null ? field.KpiName : field.ShowName;
                retDic[showName] = kpi.ColorRanges;
            }
            return retDic;
        }

        protected void FillKpiDic()
        {
            kpiDic.Clear();
            List<CellAssociationKPIItem> kpiList = CellAssociationKPIManager.Instance.KPIList;
            foreach (CellAssociationKPIItem kpiItem in kpiList)
            {
                kpiDic[kpiItem.Key] = kpiItem;
            }
        }

        protected Dictionary<string, CellAssociationKPIItem> kpiDic = new Dictionary<string, CellAssociationKPIItem>();
    }

    public class CellAssociationKPIQueryEx : CellAssociationKPIQuery
    {
        public CellAssociationKPIResult QueryReport(CellAssociationKPIReport report, List<TimePeriod> timePeriods, List<ICell> cellList)
        {
            CellAssociationKPIResult result = new CellAssociationKPIResult(report.NetType);
            StringBuilder kpiSqlTextSb = new StringBuilder();
            try
            {
                // 需要查询的指标
                FillKpiDic();

                // 小区过滤字符串(由于参数长度有限制所以拆分多个串)
                DataTable cellTable = GetCellTable(report.NetType);
                FillCellTableDic(report.NetType, cellTable);
                List<string> cellFilterStrings = GetFilterStrings(report.NetType, cellList);

                // 每个时间段，每个过滤参数，进行一次查询，然后将结果合并
                DataTable kpiTable = null;
                string showStringFormat = null;
                foreach (TimePeriod tp in timePeriods)
                {
                    foreach (string filterStr in cellFilterStrings)
                    {
                        string kpiSqlText = null;
                        SqlParameter[] sqlParams = GetKpiSqlParams(report, tp, filterStr, out kpiSqlText);
                        kpiSqlTextSb.AppendLine(kpiSqlText);
                        DataTable tmpKpiTable = GetKpiTable(sqlParams, out showStringFormat);
                        if (kpiTable == null)
                        {
                            kpiTable = tmpKpiTable;
                        }
                        else
                        {
                            kpiTable.Merge(tmpKpiTable);
                        }
                    }
                }
#if DEBUG
                //DebugTable(kpiTable, "CellAssociationKPI.csv");
#endif
                DataTable resultTable = JoinTables(report, kpiTable, cellTable);

                result.ResultTable = resultTable;
                result.ColumnFormatMap = GetColumnFormat(report, showStringFormat);
                result.ColumnInfoMap = GetColumnInfo(report);

            }
            catch (Exception ex)
            {
                result.Error = ex;
            }
            finally
            {
                result.KPISqlText = kpiSqlTextSb.ToString();
            }

            return result;
        }

        protected string GetKpiSql(CellAssociationKPIReport report, TimePeriod timePeriod, string cellFilter)
        {
            int timeId = (int)report.TimeType;
            int objectId = (int)report.NetType;

            StringBuilder sb = new StringBuilder();
            foreach (CellAssociationKPIField field in report.FieldList)
            {
                string[] kpiKeyParts = field.KpiKey.Split(':');
                string kpiId = kpiKeyParts[3];

                sb.Append(string.Format("{0},{1},{2};", kpiId, timeId, objectId));
            }

            string timeString = string.Format("{0}|{1}", timePeriod.BeginTime.ToString("yyyy-MM-dd HH:mm:ss"),
                timePeriod.EndTime.ToString("yyyy-MM-dd HH:mm:ss"));

            string sqlText = string.Format("exec PROC_通用查询_ExecQuerySql {0}, {1}, '{2}', '{3}', '{4}', '1, 1', '{5}', '', 1, ''",
                timeId, objectId, sb.ToString().TrimEnd(';'), timeString, cellFilter, objectId);
            return sqlText;
        }

        protected SqlParameter[] GetKpiSqlParams(CellAssociationKPIReport report, TimePeriod timePeriod, string cellFilter, out string sqlText)
        {
            int timeId = (int)report.TimeType;
            int objectId = (int)report.NetType;

            StringBuilder sb = new StringBuilder();
            foreach (CellAssociationKPIField field in report.FieldList)
            {
                string[] kpiKeyParts = field.KpiKey.Split(':');
                string kpiId = kpiKeyParts[3];

                sb.Append(string.Format("{0},{1},{2};", kpiId, timeId, objectId));
            }

            string timeString = string.Format("{0}|{1}", timePeriod.BeginTime.ToString("yyyy-MM-dd HH:mm:ss"),
                timePeriod.EndTime.ToString("yyyy-MM-dd HH:mm:ss"));

            SqlParameter[] sqlParams = new SqlParameter[9];
            sqlParams[0] = new SqlParameter("@时间类型", DbType.Int32);
            sqlParams[1] = new SqlParameter("@对象类型", DbType.Int32);
            sqlParams[2] = new SqlParameter("@指标串", SqlDbType.VarChar, 2000);
            sqlParams[3] = new SqlParameter("@时间过滤条件", SqlDbType.VarChar, 1000);
            sqlParams[4] = new SqlParameter("@对象过滤条件", SqlDbType.VarChar, 2000);
            sqlParams[5] = new SqlParameter("@时间显示方案", SqlDbType.VarChar, 100);
            sqlParams[6] = new SqlParameter("@对象显示方案", SqlDbType.VarChar, 1000);
            sqlParams[7] = new SqlParameter("@特殊过滤方案", SqlDbType.VarChar, 2000);
            sqlParams[8] = new SqlParameter("@格式化字符串", SqlDbType.VarChar, 5000);

            sqlParams[0].Value = timeId;
            sqlParams[1].Value = objectId;
            sqlParams[2].Value = sb.ToString().TrimEnd(';');
            sqlParams[3].Value = timeString;
            sqlParams[4].Value = cellFilter;
            sqlParams[5].Value = "1,1";
            sqlParams[6].Value = objectId.ToString();
            sqlParams[7].Value = "";
            //sqlParams[8].Value = "";
            sqlParams[8].Direction = ParameterDirection.Output;

            sqlText = string.Format("exec PROC_通用查询_ExecQuerySqlFormat {0}, {1}, '{2}', '{3}', '{4}', '1, 1', '{5}'",
                timeId, objectId, sb.ToString().TrimEnd(';'), timeString, cellFilter, objectId);
            return sqlParams;
        }

        protected DataTable GetKpiTable(SqlParameter[] sqlParams, out string fmtText)
        {
            DataSet ds = new DataSet();
            SqlHelper.FillDataset(
                new SqlConnection(CellAssociationKPIConfig.Instance.ConnectionString),
                CommandType.StoredProcedure,
                "PROC_通用查询_ExecQuerySqlFormat",
                ds,
                new string[] { "KpiTable"},
                sqlParams);
            fmtText = Convert.IsDBNull(sqlParams[sqlParams.Length - 1].Value) ? "" : sqlParams[sqlParams.Length - 1].Value.ToString();
            return ds.Tables[0];
        }

        protected void FillCellTableDic(ECellAssociationKPINetType netType, DataTable cellTable)
        {
            if (netType == ECellAssociationKPINetType.LTE)
            {
                string key = null;
                foreach (DataRow dr in cellTable.Rows)
                {
                    int eNodeBID = (int)dr["ENBID"];
                    int sectorID = (int)dr["扇区序号"];
                    int iCellID = (int)dr["自维护ID"];
                    key = eNodeBID + ":" + sectorID;
                    if (!cellTableDic.ContainsKey(key))
                    {
                        cellTableDic.Add(key, iCellID);
                    }
                }
            }
            else
            {
                string key = null;
                foreach (DataRow dr in cellTable.Rows)
                {
                    int lac = (int)dr["LAC"];
                    int ci = (int)dr["CI"];
                    int iCellID = (int)dr["自维护ID"];
                    key = lac + ":" + ci;
                    if (!cellTableDic.ContainsKey(key))
                    {
                        cellTableDic.Add(key, iCellID);
                    }
                }
            }
        }

        protected List<string> GetFilterStrings(ECellAssociationKPINetType netType, List<ICell> cellList)
        {
            int modValue = 50;
            List<string> retList = new List<string>();

            StringBuilder sb = null;
            for (int i = 0; i < cellList.Count; ++i)
            {
                if (i % modValue == 0)
                {
                    if (sb != null && sb.ToString().EndsWith(","))
                    {
                        retList.Add(sb.ToString(0, sb.Length - 1) + "}");
                    }
                    sb = new StringBuilder("{" + (int)netType + "|");
                }

                int iCellID;
                if (this.TryGetCellID(netType, cellList[i], out iCellID) && sb != null)
                {
                    sb.Append(iCellID + ",");
                }
            }
            if (sb != null && sb.ToString().EndsWith(","))
            {
                retList.Add(sb.ToString(0, sb.Length - 1) + "}");
            }

            return retList;
        }

        protected bool TryGetCellID(ECellAssociationKPINetType netType, ICell cell, out int iCellID)
        {
            iCellID = -1;
            int arg1 = -1, arg2 = -1;
            if (netType == ECellAssociationKPINetType.LTE)
            {
                LTECell lteCell = cell as LTECell;
                arg1 = lteCell.BelongBTS.BTSID;
                arg2 = lteCell.SectorID;
            }
            else if (netType == ECellAssociationKPINetType.TD)
            {
                TDCell tdCell = cell as TDCell;
                if (tdCell == null)
                {
                    return false;
                }
                arg1 = tdCell.LAC;
                arg2 = tdCell.CI;
            }
            else if (netType == ECellAssociationKPINetType.GSM)
            {
                Cell gsmCell = cell as Cell;
                if (gsmCell == null)
                {
                    return false;
                }
                arg1 = gsmCell.LAC;
                arg2 = gsmCell.CI;
            }

            string key = arg1 + ":" + arg2;
            return cellTableDic.TryGetValue(key, out iCellID);
        }

        protected Dictionary<string, string> GetColumnFormat(CellAssociationKPIReport report, string formatString)
        {
            Dictionary<string, string> retDic = new Dictionary<string, string>();
            string[] columnFormats = formatString.Split('@');
            foreach (string cFmt in columnFormats)
            {
                string cName = cFmt.Substring(0, cFmt.IndexOf('$'));
                foreach (CellAssociationKPIField field in report.FieldList)
                {
                    CellAssociationKPIItem kpi = null;
                    if (!kpiDic.TryGetValue(field.KpiKey, out kpi) || field.KpiName != cName) 
                    {
                        continue;
                    }

                    string showName = field.ShowName == null ? field.KpiName : field.ShowName;
                    retDic[showName] = cFmt;
                }
            }
            return retDic;
        }

        protected Dictionary<string, int> cellTableDic = new Dictionary<string, int>();
    }

    public class CellAssociationKPIResult
    {
        public CellAssociationKPIResult(ECellAssociationKPINetType netType)
        {
            this.netType = netType;

            List<LTECell> lteCells = null;
            if (MapCellLayer.DrawCurrent)
            {
                lteCells = CellManager.GetInstance().GetCurrentLTECells();
            }
            else
            {
                lteCells = CellManager.GetInstance().GetLTECells(MapCellLayer.CurShowTimeAt);
            }
            foreach (LTECell lteCell in lteCells)
            {
                lteCellIdDic[lteCell.SCellID] = lteCell;
            }
        }

        public string KPISqlText
        {
            get;
            set;
        }

        /// <summary>
        /// 时间，指标1，指标2，...，指标N，Arg1, Arg2
        /// </summary>
        public DataTable ResultTable
        {
            get;
            set;
        }

        public Exception Error
        {
            get;
            set;
        }

        public Dictionary<string, object> ColumnInfoMap
        {
            get;
            set;
        }

        public Dictionary<string, string> ColumnFormatMap
        {
            get;
            set;
        }

        public ICell GetCell(DataRow dr)
        {
            if (Convert.IsDBNull(dr["时间"]) || Convert.IsDBNull(dr["Arg1"]) || Convert.IsDBNull(dr["Arg2"]))
            {
                return null;
            }

            DateTime time = (DateTime)dr["时间"];
            int arg1 = (int)dr["Arg1"];
            int arg2 = (int)dr["Arg2"];
            if (netType == ECellAssociationKPINetType.LTE)
            {
                int cellId = arg1 * 10 + arg2; // arg1 = enobid, arg2 = sectorid
                LTECell lteCell = null;
                lteCellIdDic.TryGetValue(cellId, out lteCell);
                return lteCell;
            }
            else if (netType == ECellAssociationKPINetType.TD)
            {
                return CellManager.GetInstance().GetTDCell(time, arg1, arg2);
            }
            else
            {
                return CellManager.GetInstance().GetCell(time, (ushort)arg1, (ushort)arg2);
            }
        }

        public List<DataColumn> GetKPIColumns()
        {
            List<DataColumn> columns = new List<DataColumn>();
            for (int i = 0; i < ResultTable.Columns.Count - 2; ++i)
            {
                columns.Add(ResultTable.Columns[i]);
            }
            return columns;
        }

        public List<object> GetKPIValues(DataRow dr)
        {
            List<object> values = new List<object>();
            for (int i = 0; i < dr.ItemArray.Length - 2; ++i)
            {
                values.Add(dr.ItemArray[i]);
            }
            return values;
        }

        private readonly ECellAssociationKPINetType netType;
        private readonly Dictionary<int, LTECell> lteCellIdDic = new Dictionary<int, LTECell>();
    }
}
