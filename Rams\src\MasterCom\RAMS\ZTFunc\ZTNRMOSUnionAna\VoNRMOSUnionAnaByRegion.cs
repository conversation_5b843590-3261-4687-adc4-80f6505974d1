﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class VoNRMOSUnionAnaByRegion : VoNRMOSUnionAnaBase
    {
        public VoNRMOSUnionAnaByRegion(MainModel mm) : base(mm)
        {
            mainModel = mm;
        }

        public override string Name
        {
            get { return "VoNR-MOS关联分析(按区域)"; }
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        protected override void query()
        {
            if (!getCondition())
            {
                return;
            }

            DIYQueryFileInfoByRegion queryFileByReg = new DIYQueryFileInfoByRegion(MainModel);
            queryFileByReg.IsShowFileInfoForm = false;
            queryFileByReg.SetQueryCondition(condition);
            queryFileByReg.Query();

            Condition.FileInfos.Clear();
            Condition.FileInfos.AddRange(MainModel.FileInfos);
            SetQueryCondition(condition);
            NeedJudgeTestPointByRegion = true;
            base.query();
            MainModel.DTDataManager.Clear();
        }
    }
}
