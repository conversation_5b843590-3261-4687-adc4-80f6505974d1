﻿using MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage._45GHighReverseFlowCoverage;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTHighReverseFlowCoverage
{
    public static class HighReverseFlowCoverageParams
    {
        #region 读取工参
        public static Dictionary<int, List<CellParamInfo>> ReadNRParams()
        {
            WaitBox.Text = "正在读取5G工参...";
            DiyQueryNRCellInfo query = new DiyQueryNRCellInfo();
            query.Query();
            HighReverseFlowCoverageConfig.Instance.WriteLog($"读取到NR工参[{query.TotalCount}]条, 无效数据{query.ErrTotalCount}条", "info");
            return query.CityCellInfosDic;
        }

        public static Dictionary<int, List<CellParamInfo>> ReadLTEParams()
        {
            WaitBox.Text = "正在读取4G工参...";
            DiyQueryLTECellInfo query = new DiyQueryLTECellInfo();
            query.Query();
            HighReverseFlowCoverageConfig.Instance.WriteLog($"读取到LTE工参[{query.TotalCount}]条, 无效数据{query.ErrTotalCount}条", "info");
            return query.CityCellInfosDic;
        }
        #endregion
    }
}
