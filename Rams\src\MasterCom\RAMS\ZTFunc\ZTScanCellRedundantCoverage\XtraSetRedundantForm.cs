﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class XtraSetRedundantForm : BaseDialog
    {
        public XtraSetRedundantForm(string type)
        {
            InitializeComponent();
            cbxCoFreqType.Properties.Items.Clear();
            if (type.Equals("GSM"))
            {
                cbxCoFreqType.Properties.Items.Add("BCCH & TCH");
                cbxCoFreqType.Properties.Items.Add("BCCH Only");
                cbxCoFreqType.Properties.Items.Add("TCH Only");
                numRxLevDValue.Value = 12;
                numRxLevThreshold.Value = -80;
            }
            else if (type.Equals("TD"))
            {
                cbxCoFreqType.Properties.Items.Add("ARFCN & ARFCNList");
                cbxCoFreqType.Properties.Items.Add("ARFCN Only");
                cbxCoFreqType.Properties.Items.Add("ARFCNList Only");
                numRxLevDValue.Value = 6;
                numRxLevThreshold.Value = -80;
            }
            else if (type.Equals("WCDMA"))
            {
                cbxCoFreqType.Properties.Items.Add("UARFCN 或 UARFCNListOnly");
                cbxCoFreqType.Properties.Items.Add("UARFCN Only");
                cbxCoFreqType.Properties.Items.Add("UARFCNList Only");
                numRxLevDValue.Value = 6;
                numRxLevThreshold.Value = -80;
            }
            if (cbxCoFreqType.Properties.Items.Count > 1)
            {
                cbxCoFreqType.SelectedIndex = 1;
            }
            chkCoFreq_CheckedChanged(null, null);
        }


        public void GetSettingFilterRet(out int setRxlev, out int setRxlevDiff, out bool coFreq, out MapForm.DisplayInterferenceType interferenceType, out int invalidPointRxLev)
        {
            setRxlevDiff = (int)numRxLevDValue.Value;

            setRxlev = (int)numRxLevThreshold.Value;

            coFreq = chkCoFreq.Checked;

            interferenceType = (MapForm.DisplayInterferenceType)cbxCoFreqType.SelectedIndex;

            invalidPointRxLev = (int)spinEditInvalidThresold.Value;
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleButton2_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void chkCoFreq_CheckedChanged(object sender, EventArgs e)
        {
            cbxCoFreqType.Enabled = chkCoFreq.Checked;
        }
    }
}