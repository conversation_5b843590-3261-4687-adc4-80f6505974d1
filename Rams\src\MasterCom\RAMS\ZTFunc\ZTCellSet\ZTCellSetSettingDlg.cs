﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.Net
{
    public partial class ZTCellSetSettingDlg : BaseForm
    {
        public ZTCellSetSettingDlg()
        {
            InitializeComponent();
        }

        public int RxqualSubMin
        {
            get { return (int)spinEditRxqualMin.Value; }
        }

        public int RxqualSubMax
        {
            get { return (int)spinEditRxqualMax.Value; }
        }

        public void Fill(int rxqualMin, int rxqualMax)
        {
            spinEditRxqualMin.Value = rxqualMin;
            spinEditRxqualMax.Value = rxqualMax;
        }
        public void SetLableTxt(string str)
        {
            labelControl1.Text = str;
        }
        private void simpleButtonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleButtonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
