﻿namespace MasterCom.RAMS.NewBlackBlock
{
    partial class FusionDataForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.tabCtrl = new System.Windows.Forms.TabControl();
            this.pageAlarm = new System.Windows.Forms.TabPage();
            this.gridCtrlAlarm = new DevExpress.XtraGrid.GridControl();
            this.gvAlarm = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.pagePerf = new System.Windows.Forms.TabPage();
            this.gridCtrlPerf = new DevExpress.XtraGrid.GridControl();
            this.gvPerf = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.pageArg = new System.Windows.Forms.TabPage();
            this.gridCtrlArg = new DevExpress.XtraGrid.GridControl();
            this.gvArg = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn39 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.contextMenuExport = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItem_ExportAll = new System.Windows.Forms.ToolStripMenuItem();
            this.tabCtrl.SuspendLayout();
            this.pageAlarm.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlAlarm)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvAlarm)).BeginInit();
            this.pagePerf.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlPerf)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvPerf)).BeginInit();
            this.pageArg.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlArg)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvArg)).BeginInit();
            this.contextMenuExport.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabCtrl
            // 
            this.tabCtrl.ContextMenuStrip = this.contextMenuExport;
            this.tabCtrl.Controls.Add(this.pageAlarm);
            this.tabCtrl.Controls.Add(this.pagePerf);
            this.tabCtrl.Controls.Add(this.pageArg);
            this.tabCtrl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabCtrl.Location = new System.Drawing.Point(0, 0);
            this.tabCtrl.Name = "tabCtrl";
            this.tabCtrl.SelectedIndex = 0;
            this.tabCtrl.Size = new System.Drawing.Size(922, 430);
            this.tabCtrl.TabIndex = 0;
            // 
            // pageAlarm
            // 
            this.pageAlarm.Controls.Add(this.gridCtrlAlarm);
            this.pageAlarm.Location = new System.Drawing.Point(4, 23);
            this.pageAlarm.Name = "pageAlarm";
            this.pageAlarm.Padding = new System.Windows.Forms.Padding(3);
            this.pageAlarm.Size = new System.Drawing.Size(914, 403);
            this.pageAlarm.TabIndex = 0;
            this.pageAlarm.Text = "告警";
            this.pageAlarm.UseVisualStyleBackColor = true;
            // 
            // gridCtrlAlarm
            // 
            this.gridCtrlAlarm.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridCtrlAlarm.Location = new System.Drawing.Point(3, 3);
            this.gridCtrlAlarm.MainView = this.gvAlarm;
            this.gridCtrlAlarm.Name = "gridCtrlAlarm";
            this.gridCtrlAlarm.ShowOnlyPredefinedDetails = true;
            this.gridCtrlAlarm.Size = new System.Drawing.Size(908, 397);
            this.gridCtrlAlarm.TabIndex = 0;
            this.gridCtrlAlarm.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvAlarm});
            // 
            // gvAlarm
            // 
            this.gvAlarm.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6});
            this.gvAlarm.GridControl = this.gridCtrlAlarm;
            this.gvAlarm.Name = "gvAlarm";
            this.gvAlarm.OptionsBehavior.Editable = false;
            this.gvAlarm.OptionsView.ColumnAutoWidth = false;
            this.gvAlarm.OptionsView.ShowGroupPanel = false;
            this.gvAlarm.CustomDrawEmptyForeground += new DevExpress.XtraGrid.Views.Base.CustomDrawEventHandler(this.gvAlarm_CustomDrawEmptyForeground);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "小区";
            this.gridColumn1.FieldName = "CellName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "TAC";
            this.gridColumn2.FieldName = "TAC";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "ECI";
            this.gridColumn3.FieldName = "ECI";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "告警开始时间";
            this.gridColumn4.FieldName = "BeginTime";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "告警结束时间";
            this.gridColumn5.FieldName = "EndTime";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 4;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "告警描述";
            this.gridColumn6.FieldName = "Desc";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 5;
            // 
            // pagePerf
            // 
            this.pagePerf.Controls.Add(this.gridCtrlPerf);
            this.pagePerf.Location = new System.Drawing.Point(4, 23);
            this.pagePerf.Name = "pagePerf";
            this.pagePerf.Padding = new System.Windows.Forms.Padding(3);
            this.pagePerf.Size = new System.Drawing.Size(914, 403);
            this.pagePerf.TabIndex = 1;
            this.pagePerf.Text = "性能";
            this.pagePerf.UseVisualStyleBackColor = true;
            // 
            // gridCtrlPerf
            // 
            this.gridCtrlPerf.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridCtrlPerf.Location = new System.Drawing.Point(3, 3);
            this.gridCtrlPerf.MainView = this.gvPerf;
            this.gridCtrlPerf.Name = "gridCtrlPerf";
            this.gridCtrlPerf.ShowOnlyPredefinedDetails = true;
            this.gridCtrlPerf.Size = new System.Drawing.Size(908, 397);
            this.gridCtrlPerf.TabIndex = 1;
            this.gridCtrlPerf.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvPerf});
            // 
            // gvPerf
            // 
            this.gvPerf.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn43,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn24,
            this.gridColumn25,
            this.gridColumn26,
            this.gridColumn27,
            this.gridColumn28,
            this.gridColumn29,
            this.gridColumn30,
            this.gridColumn31,
            this.gridColumn32,
            this.gridColumn33,
            this.gridColumn34});
            this.gvPerf.GridControl = this.gridCtrlPerf;
            this.gvPerf.Name = "gvPerf";
            this.gvPerf.OptionsBehavior.Editable = false;
            this.gvPerf.OptionsView.ColumnAutoWidth = false;
            this.gvPerf.OptionsView.ShowGroupPanel = false;
            this.gvPerf.CustomDrawEmptyForeground += new DevExpress.XtraGrid.Views.Base.CustomDrawEventHandler(this.gvPerf_CustomDrawEmptyForeground);
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "小区";
            this.gridColumn7.FieldName = "CellName";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 0;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "TAC";
            this.gridColumn8.FieldName = "TAC";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 1;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "ECI";
            this.gridColumn9.FieldName = "ECI";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 2;
            // 
            // gridColumn43
            // 
            this.gridColumn43.Caption = "时间";
            this.gridColumn43.FieldName = "BeginTimeDes";
            this.gridColumn43.Name = "gridColumn43";
            this.gridColumn43.Visible = true;
            this.gridColumn43.VisibleIndex = 3;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "空口上行业务字节数";
            this.gridColumn10.FieldName = "空口上行业务字节数";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 4;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "空口下行业务字节数";
            this.gridColumn11.FieldName = "空口下行业务字节数";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 5;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "RRC连接平均数";
            this.gridColumn12.FieldName = "RRC连接平均数";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 6;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "RRC连接最大数";
            this.gridColumn19.FieldName = "RRC连接最大数";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 7;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "eNB接收干扰功率平均值";
            this.gridColumn20.FieldName = "eNB接收干扰功率平均值";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 8;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "上行PRB平均利用率";
            this.gridColumn21.FieldName = "上行PRB平均利用率";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 9;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "下行PRB平均利用率";
            this.gridColumn22.FieldName = "下行PRB平均利用率";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 10;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "无线利用率";
            this.gridColumn23.FieldName = "无线利用率";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 11;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "RRC连接建立请求次数";
            this.gridColumn24.FieldName = "RRC连接建立请求次数";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 12;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "RRC连接建立成功次数";
            this.gridColumn25.FieldName = "RRC连接建立成功次数";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 13;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "RRC连接建立成功率";
            this.gridColumn26.FieldName = "RRC连接建立成功率";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 14;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "E-RAB建立请求数";
            this.gridColumn27.FieldName = "E_RAB建立请求数";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 15;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "E-RAB建立成功数";
            this.gridColumn28.FieldName = "E_RAB建立成功数";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 16;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "E-RAB建立成功率";
            this.gridColumn29.FieldName = "E_RAB建立成功率";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 17;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "无线接通率";
            this.gridColumn30.FieldName = "无线接通率";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 18;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "eNB请求释放上下文数";
            this.gridColumn31.FieldName = "eNB请求释放上下文数";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 19;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "正常的eNB请求释放上下文数";
            this.gridColumn32.FieldName = "正常的eNB请求释放上下文数";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 20;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "初始上下文建立成功次数";
            this.gridColumn33.FieldName = "初始上下文建立成功次数";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 21;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "无线掉线率";
            this.gridColumn34.FieldName = "无线掉线率";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 22;
            // 
            // pageArg
            // 
            this.pageArg.Controls.Add(this.gridCtrlArg);
            this.pageArg.Location = new System.Drawing.Point(4, 23);
            this.pageArg.Name = "pageArg";
            this.pageArg.Padding = new System.Windows.Forms.Padding(3);
            this.pageArg.Size = new System.Drawing.Size(914, 403);
            this.pageArg.TabIndex = 2;
            this.pageArg.Text = "参数";
            this.pageArg.UseVisualStyleBackColor = true;
            // 
            // gridCtrlArg
            // 
            this.gridCtrlArg.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridCtrlArg.Location = new System.Drawing.Point(3, 3);
            this.gridCtrlArg.MainView = this.gvArg;
            this.gridCtrlArg.Name = "gridCtrlArg";
            this.gridCtrlArg.ShowOnlyPredefinedDetails = true;
            this.gridCtrlArg.Size = new System.Drawing.Size(908, 397);
            this.gridCtrlArg.TabIndex = 2;
            this.gridCtrlArg.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvArg});
            // 
            // gvArg
            // 
            this.gvArg.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn35,
            this.gridColumn36,
            this.gridColumn37,
            this.gridColumn38,
            this.gridColumn39,
            this.gridColumn40,
            this.gridColumn41,
            this.gridColumn42});
            this.gvArg.GridControl = this.gridCtrlArg;
            this.gvArg.Name = "gvArg";
            this.gvArg.OptionsBehavior.Editable = false;
            this.gvArg.OptionsView.ColumnAutoWidth = false;
            this.gvArg.OptionsView.ShowGroupPanel = false;
            this.gvArg.CustomDrawEmptyForeground += new DevExpress.XtraGrid.Views.Base.CustomDrawEventHandler(this.gvArg_CustomDrawEmptyForeground);
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "小区";
            this.gridColumn13.FieldName = "CellName";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 0;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "TAC";
            this.gridColumn14.FieldName = "TAC";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 1;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "ECI";
            this.gridColumn15.FieldName = "ECI";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 2;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "参考信号功率";
            this.gridColumn16.FieldName = "参考信号功率";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 3;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "PDSCH采用均匀功率分配时的PA值";
            this.gridColumn17.FieldName = "PDSCH采用均匀功率分配时的PA值";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 4;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "PB";
            this.gridColumn18.FieldName = "PB";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 5;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "小区偏移量";
            this.gridColumn35.FieldName = "小区偏移量";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 6;
            // 
            // gridColumn36
            // 
            this.gridColumn36.Caption = "小区偏置";
            this.gridColumn36.FieldName = "小区偏置";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.Visible = true;
            this.gridColumn36.VisibleIndex = 7;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "上下行业务子帧配置";
            this.gridColumn37.FieldName = "上下行业务子帧配置";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.Visible = true;
            this.gridColumn37.VisibleIndex = 8;
            // 
            // gridColumn38
            // 
            this.gridColumn38.Caption = "特殊子帧配置";
            this.gridColumn38.FieldName = "特殊子帧配置";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.Visible = true;
            this.gridColumn38.VisibleIndex = 9;
            // 
            // gridColumn39
            // 
            this.gridColumn39.Caption = "同频 A3 offset";
            this.gridColumn39.FieldName = "同频_A3_offset";
            this.gridColumn39.Name = "gridColumn39";
            this.gridColumn39.Visible = true;
            this.gridColumn39.VisibleIndex = 10;
            // 
            // gridColumn40
            // 
            this.gridColumn40.Caption = "同频 A3 Hysteresis";
            this.gridColumn40.FieldName = "同频_A3_Hysteresis";
            this.gridColumn40.Name = "gridColumn40";
            this.gridColumn40.Visible = true;
            this.gridColumn40.VisibleIndex = 11;
            // 
            // gridColumn41
            // 
            this.gridColumn41.Caption = "同频 A3 Time-to-trigger";
            this.gridColumn41.FieldName = "同频_A3_Time_to_trigger";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.Visible = true;
            this.gridColumn41.VisibleIndex = 12;
            // 
            // gridColumn42
            // 
            this.gridColumn42.Caption = "异频A2门限";
            this.gridColumn42.FieldName = "异频A2门限";
            this.gridColumn42.Name = "gridColumn42";
            this.gridColumn42.Visible = true;
            this.gridColumn42.VisibleIndex = 13;
            // 
            // contextMenuExport
            // 
            this.contextMenuExport.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItem_ExportAll});
            this.contextMenuExport.Name = "contextMenuExport";
            this.contextMenuExport.Size = new System.Drawing.Size(142, 26);
            // 
            // ToolStripMenuItem_ExportAll
            // 
            this.ToolStripMenuItem_ExportAll.Name = "ToolStripMenuItem_ExportAll";
            this.ToolStripMenuItem_ExportAll.Size = new System.Drawing.Size(141, 22);
            this.ToolStripMenuItem_ExportAll.Text = "导出到Excel";
            this.ToolStripMenuItem_ExportAll.Click += new System.EventHandler(this.ToolStripMenuItem_ExportAll_Click);
            // 
            // FusionDataForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(922, 430);
            this.Controls.Add(this.tabCtrl);
            this.Name = "FusionDataForm";
            this.Text = "关联数据";
            this.tabCtrl.ResumeLayout(false);
            this.pageAlarm.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlAlarm)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvAlarm)).EndInit();
            this.pagePerf.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlPerf)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvPerf)).EndInit();
            this.pageArg.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlArg)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvArg)).EndInit();
            this.contextMenuExport.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabControl tabCtrl;
        private System.Windows.Forms.TabPage pageAlarm;
        private DevExpress.XtraGrid.GridControl gridCtrlAlarm;
        private DevExpress.XtraGrid.Views.Grid.GridView gvAlarm;
        private System.Windows.Forms.TabPage pagePerf;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.GridControl gridCtrlPerf;
        private DevExpress.XtraGrid.Views.Grid.GridView gvPerf;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private System.Windows.Forms.TabPage pageArg;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.GridControl gridCtrlArg;
        private DevExpress.XtraGrid.Views.Grid.GridView gvArg;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn39;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn40;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private System.Windows.Forms.ContextMenuStrip contextMenuExport;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItem_ExportAll;

    }
}