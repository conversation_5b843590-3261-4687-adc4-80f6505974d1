﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.ZTFunc.ZTLteTestAcceptance;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Excel = Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.BackgroundFunc
{
    class ExportIndoorBtsReportHelper_XJ : ExportIndoorBtsReportBase
    {
        public static string ExportReports(InDoorBtsAcceptInfo btsInfo, BtsWorkParam_XJ btsWorkParamInfo
            , List<IndoorCoverPicInfo> coverPicInfoList)
        {
            string targetFile = "";
            if (MainModel.GetInstance().BackgroundStopRequest)
            {
                return targetFile;
            }

            Excel.Application xlApp = null;
            try
            {
                StationAcceptAutoSet_XJ funcSet = MultiStationExportReportAna.GetInstance().FuncSet;

                string folderPath = Path.Combine(funcSet.ReportSavePath, btsWorkParamInfo.DateDes);
                if (!Directory.Exists(folderPath))
                {
                    Directory.CreateDirectory(folderPath);
                }
                //string btsFileKey = string.Format("{0}_{1}_{2}", btsInfo.BtsName, btsWorkParamInfo.ENodeBID, btsWorkParamInfo.WorkOrderNum);
                targetFile = LteTestAcceptManager.GetTargetFile(btsInfo.BtsName, btsInfo.LteBts.Cells.Count, folderPath);
                reportInfo(string.Format("开始导出 {0} 站点的单验报告", btsInfo.BtsName));

                xlApp = new Excel.Application();
                xlApp.Visible = false;
                Excel.Workbook eBook = xlApp.Workbooks.Open(targetFile);

                fillHomePage(eBook, btsInfo, btsWorkParamInfo);
                fillKpiPage(eBook, btsInfo);
                fillLevelKpiPage(eBook, btsInfo);
                fillCoverPicPage(eBook, coverPicInfoList, funcSet.IndoorCoverPicFolderPath);

                BtsFusionInfo_XJ btsFusionInfo = btsInfo.FusionInfo as BtsFusionInfo_XJ;
                fillFusionKpiPage(eBook, btsFusionInfo);

                eBook.Save();
                eBook.Close(Type.Missing, Type.Missing, Type.Missing);

                reportInfo(string.Format("成功导出 {0} 站点的单验报告。", btsInfo.BtsName));
                return targetFile;
            }
            catch (Exception ex)
            {
                reportError(ex);
                return "";
            }
            finally
            {
                if (xlApp != null)
                {
                    xlApp.Quit();
                }
                //GC.Collect();
            }
        }

        //填充报告首页-室分记录单
        protected static void fillHomePage(Excel.Workbook eBook, InDoorBtsAcceptInfo btsInfo
            , BtsWorkParam_XJ btsWorkParamInfo)
        {
            LTEBTS srcLteBts = btsInfo.LteBts;
            Excel.Worksheet homePageSheet = (Excel.Worksheet)eBook.Sheets[1];
            DistrictManager.GetInstance().getDistrictName(MainModel.GetInstance().DistrictID);

            #region 基站描述及基站参数
            homePageSheet.get_Range("e3").set_Value(Type.Missing, srcLteBts.Name);
            homePageSheet.get_Range("e5").set_Value(Type.Missing, srcLteBts.BTSID);
            homePageSheet.get_Range("e7").set_Value(Type.Missing, btsWorkParamInfo.Address);
            homePageSheet.get_Range("z3").set_Value(Type.Missing, DateTime.Now.ToString("yyyy-MM-dd"));
            homePageSheet.get_Range("z5").set_Value(Type.Missing, btsWorkParamInfo.CoverScene);
            homePageSheet.get_Range("z9").set_Value(Type.Missing, btsInfo.CoveredFloors);
            homePageSheet.get_Range("h13").set_Value(Type.Missing, srcLteBts.Longitude);
            homePageSheet.get_Range("h14").set_Value(Type.Missing, srcLteBts.Latitude);
            homePageSheet.get_Range("n13").set_Value(Type.Missing, btsWorkParamInfo.TestLongitude);
            homePageSheet.get_Range("n14").set_Value(Type.Missing, btsWorkParamInfo.TestLatitude);
            #endregion

            int cellIndex = 0;
            int cellParamColIndex = 8;
            int cellParamRowIndex = 18;

            int cellKpiColIndex = 12;
            int cellKpiRowIndex = 39;
            foreach (LTECell icell in srcLteBts.Cells)
            {
                if (cellIndex == 3)
                {
                    cellParamColIndex = 8;
                    cellParamRowIndex = 27;

                    cellKpiColIndex = 12;
                    cellKpiRowIndex = 47;
                }
                else if (cellIndex == 6)
                {
                    cellKpiColIndex = 12;
                    cellKpiRowIndex = 55;
                }

                #region 小区参数
                homePageSheet.Cells[cellParamRowIndex, cellParamColIndex] = string.Format("Cell-{0}(PCI:{1})", cellIndex + 1, icell.PCI);
                homePageSheet.Cells[cellParamRowIndex + 2, cellParamColIndex] = icell.PCI;
                homePageSheet.Cells[cellParamRowIndex + 3, cellParamColIndex] = icell.EARFCN;
                //homePageSheet.Cells[cellParamRowIndex + 7, cellParamColIndex] = "";//合路方式
                InDoorCellAcceptInfo cellInfo;
                if (btsInfo.CellsAcceptDic.TryGetValue(icell.CellID, out cellInfo))
                {
                    homePageSheet.Cells[cellParamRowIndex + 8, cellParamColIndex + 3] = cellInfo.RoadTypeDes;//单双路
                }
                #endregion

                #region 小区指标
                if (cellInfo != null)
                {
                    homePageSheet.Cells[cellKpiRowIndex, cellKpiColIndex] = cellInfo.RrcInfo.IsAccordDes;
                    homePageSheet.Cells[cellKpiRowIndex + 1, cellKpiColIndex] = cellInfo.ErabInfo.IsAccordDes;
                    homePageSheet.Cells[cellKpiRowIndex + 2, cellKpiColIndex] = cellInfo.AccessInfo.IsAccordDes;
                    homePageSheet.Cells[cellKpiRowIndex + 3, cellKpiColIndex] = cellInfo.CsfbInfo.IsAccordDes;
                    homePageSheet.Cells[cellKpiRowIndex + 4, cellKpiColIndex] = cellInfo.IsFtpDlAccordDes;
                    homePageSheet.Cells[cellKpiRowIndex + 5, cellKpiColIndex] = cellInfo.IsFtpUlAccordDes;
                    homePageSheet.Cells[cellKpiRowIndex + 6, cellKpiColIndex] = cellInfo.DlCoverRate.IsAccordDes;
                }
                #endregion

                cellIndex++;
                cellParamColIndex += 8;
                cellKpiColIndex += 5;
            }

            homePageSheet.get_Range("n64").set_Value(Type.Missing, btsInfo.IsHandoverAccordDes);//系统内切换
            homePageSheet.get_Range("n65").set_Value(Type.Missing, btsInfo.IsLeakOutCheckAccordDes);//室分信号泄漏
            homePageSheet.get_Range("h82").set_Value(Type.Missing, btsInfo.IsAccordAcceptStr);//验收结论
            homePageSheet.get_Range("a85").set_Value(Type.Missing, btsInfo.NotAccordKpiDes);//未通过验收的原因
        }

        //填充报告第二页-性能验收测试表格
        protected static void fillKpiPage(Excel.Workbook eBook, InDoorBtsAcceptInfo btsInfo)
        {
            LTEBTS srcLteBts = btsInfo.LteBts;
            Excel.Worksheet kpiPageSheet = (Excel.Worksheet)eBook.Sheets[2];
            kpiPageSheet.get_Range("c2").set_Value(Type.Missing, srcLteBts.Name);
            kpiPageSheet.get_Range("o2").set_Value(Type.Missing, srcLteBts.BTSID);

            int cellIndex = 0;
            int firstColIndex = 16;//尝试次数或FTP相关指标所在列
            int secondColIndex = 23;//成功次数所在列
            foreach (LTECell icell in srcLteBts.Cells)
            {
                InDoorCellAcceptInfo cellInfo;
                if (btsInfo.CellsAcceptDic.TryGetValue(icell.CellID, out cellInfo))
                {
                    int rowIndex = 7 + (cellIndex * 16);//小区RRC指标所在行：首个小区所在行号为5，其他小区行号以16递增
                    kpiPageSheet.Cells[rowIndex, firstColIndex] = cellInfo.RrcInfo.TotalCount;
                    kpiPageSheet.Cells[rowIndex, secondColIndex] = cellInfo.RrcInfo.ValidCount;
                    kpiPageSheet.Cells[rowIndex + 1, firstColIndex] = cellInfo.ErabInfo.TotalCount;
                    kpiPageSheet.Cells[rowIndex + 1, secondColIndex] = cellInfo.ErabInfo.ValidCount;
                    kpiPageSheet.Cells[rowIndex + 2, firstColIndex] = cellInfo.AccessInfo.TotalCount;
                    kpiPageSheet.Cells[rowIndex + 2, secondColIndex] = cellInfo.AccessInfo.ValidCount;
                    kpiPageSheet.Cells[rowIndex + 4, firstColIndex] = cellInfo.CsfbInfo.TotalCount;
                    kpiPageSheet.Cells[rowIndex + 4, secondColIndex] = cellInfo.CsfbInfo.ValidCount;

                    setRateValue(kpiPageSheet, rowIndex + 6, firstColIndex, cellInfo.DlCoverRate.Rate);
                    kpiPageSheet.Cells[rowIndex + 7, firstColIndex] = cellInfo.FtpRsrpInfo.KpiAvgValueDes;
                    kpiPageSheet.Cells[rowIndex + 8, firstColIndex] = cellInfo.FtpSinrInfo.KpiAvgValueDes;
                    kpiPageSheet.Cells[rowIndex + 9, firstColIndex] = cellInfo.FtpDlSpeedInfo.KpiAvgValueDes;
                    kpiPageSheet.Cells[rowIndex + 10, firstColIndex] = "均值：" + cellInfo.FtpUlSpeedInfo.KpiAvgValueDes;
                    if (cellInfo.FtpUlSpeedMax != double.MinValue)
                    {
                        kpiPageSheet.Cells[rowIndex + 10, firstColIndex + 13] = "峰值：" + cellInfo.FtpUlSpeedMax;
                    }
                    setRateValue(kpiPageSheet, rowIndex + 12, firstColIndex, cellInfo.LeakoutRate_LockEarfcn);
                    setRateValue(kpiPageSheet, rowIndex + 12, firstColIndex + 13, cellInfo.LeakoutRate_Scan);

                    kpiPageSheet.Cells[rowIndex + 14, firstColIndex] = cellInfo.HandOverInfo.TotalCount;
                    kpiPageSheet.Cells[rowIndex + 14, secondColIndex] = cellInfo.HandOverInfo.ValidCount;
                }
                cellIndex++;
            }
        }

        //填充报告第三页-平层测试指标
        protected static void fillLevelKpiPage(Excel.Workbook eBook, InDoorBtsAcceptInfo btsInfo)
        {
            int rowIndex = 2;
            Excel.Worksheet kpiPageSheet = (Excel.Worksheet)eBook.Sheets[3];
            foreach (InDoorCellAcceptInfo cellAcceptInfo in btsInfo.CellsAcceptDic.Values)
            {
                fillCellLevelKpiPage(kpiPageSheet, cellAcceptInfo, ref rowIndex);
            }

            kpiPageSheet.Cells[rowIndex + 2, 1] = btsInfo.CoveredFloorsDes_Lack;
        }

        //填充报告第四页-性能覆盖效果图
        protected static void fillCoverPicPage(Excel.Workbook eBook, List<IndoorCoverPicInfo> coverPicInfoList
            , string picFolderPath)
        {
            if (string.IsNullOrEmpty(picFolderPath))
            {
                return;
            }

            Excel.Worksheet coverPicPageSheet = (Excel.Worksheet)eBook.Sheets[4];

            coverPicInfoList.Sort();
            int rowIndex = 17;
            foreach (IndoorCoverPicInfo coverInfo in coverPicInfoList)
            {
                string coverPicFolderPath = Path.Combine(picFolderPath, coverInfo.SerialNumber);
                if (Directory.Exists(coverPicFolderPath))
                {
                    insertCoverPicture(coverPicPageSheet, coverPicFolderPath, 1, rowIndex, "RSRP-" + coverInfo.CoverFloor + "F", coverInfo.RsrpPicPath);
                    insertLegendPicture(coverPicPageSheet, coverPicFolderPath, 8, rowIndex + 1, coverInfo.RsrpLegendPicPath);
                    insertCoverPicture(coverPicPageSheet, coverPicFolderPath, 10, rowIndex, "SINR-" + coverInfo.CoverFloor + "F", coverInfo.SinrPicPath);
                    insertLegendPicture(coverPicPageSheet, coverPicFolderPath, 17, rowIndex + 1, coverInfo.SinrLegendPicPath);
                    insertCoverPicture(coverPicPageSheet, coverPicFolderPath, 19, rowIndex, "FTP下载-" + coverInfo.CoverFloor + "F", coverInfo.FtpDlPicPath);
                    insertLegendPicture(coverPicPageSheet, coverPicFolderPath, 26, rowIndex + 1, coverInfo.FtpSpeedLegendPicPath);
                    insertCoverPicture(coverPicPageSheet, coverPicFolderPath, 28, rowIndex, "FTP上传-" + coverInfo.CoverFloor + "F", coverInfo.FtpUlPicPath);
                    insertLegendPicture(coverPicPageSheet, coverPicFolderPath, 35, rowIndex + 1, coverInfo.FtpSpeedLegendPicPath);

                    rowIndex += 22;
                }
            }
        }

        protected static void insertCoverPicture(Excel.Worksheet eSheet, string coverPicFolderPath
            , int colIndex, int rowIndex, string picDes, string picFileName)
        {
            string picPath = Path.Combine(coverPicFolderPath, picFileName);
            if (File.Exists(picPath))
            {
                Excel.Range rng = eSheet.get_Range(eSheet.Cells[rowIndex, colIndex], eSheet.Cells[rowIndex, colIndex]);
                rng.set_Value(Type.Missing, picDes);

                rowIndex++;
                rng = eSheet.get_Range(eSheet.Cells[rowIndex, colIndex], eSheet.Cells[rowIndex + 19, colIndex + 6]);
                eSheet.Shapes.AddPicture(picPath, Microsoft.Office.Core.MsoTriState.msoFalse
                    , Microsoft.Office.Core.MsoTriState.msoCTrue, (float)(double)rng.Left
                   , (float)(double)rng.Top, (float)(double)rng.Width, (float)(double)rng.Height);
            }
        }
        protected static void insertLegendPicture(Excel.Worksheet eSheet, string coverPicFolderPath
            , int colIndex, int rowIndex, string picFileName)
        {
            string picPath = Path.Combine(coverPicFolderPath, picFileName);
            if (File.Exists(picPath))
            {
                Excel.Range rng = eSheet.get_Range(eSheet.Cells[rowIndex, colIndex], eSheet.Cells[rowIndex + 19, colIndex + 1]);
                eSheet.Shapes.AddPicture(picPath, Microsoft.Office.Core.MsoTriState.msoFalse
                    , Microsoft.Office.Core.MsoTriState.msoCTrue, (float)(double)rng.Left
                   , (float)(double)rng.Top, (float)(double)rng.Width, (float)(double)rng.Height);
            }
        }

        //填充报告第五页-站点后台指标监控
        protected static void fillFusionKpiPage(Excel.Workbook eBook, BtsFusionInfo_XJ btsFusionInfo)
        {
            if (btsFusionInfo == null)
            {
                return;
            }
            Excel.Worksheet bgKpiPageSheet = (Excel.Worksheet)eBook.Sheets[5];

            DateTime curDate = btsFusionInfo.BeginTime.Date;
            int cellIndex = 0;
            foreach (CellWorkParamBase cellWorkParam in btsFusionInfo.BtsWorkParamInfo.CellWorkParams)
            {
                if (cellIndex >= 3)
                {
                    break;
                }
                int perfRowIndex = 4 + (7 * cellIndex);
                bgKpiPageSheet.Cells[perfRowIndex, 1] = btsFusionInfo.BtsWorkParamInfo.BtsName;
                bgKpiPageSheet.Cells[perfRowIndex, 2] = cellWorkParam.CellName;
                bgKpiPageSheet.Cells[perfRowIndex, 3] = cellWorkParam.ENodeBID;
                bgKpiPageSheet.Cells[perfRowIndex, 4] = cellWorkParam.SectorID;
                bgKpiPageSheet.Cells[perfRowIndex, 5] = cellWorkParam.CellID;

                Dictionary<string, CellPerfDataBase> date_cellPerfDataDic;
                btsFusionInfo.CellPerfInfoDic.TryGetValue(cellWorkParam.CGI, out date_cellPerfDataDic);
                while (curDate <= btsFusionInfo.EndTime.Date)
                {
                    string dateDes = BtsFusionInfo_XJ.GetDateKeyDes(curDate);
                    bgKpiPageSheet.Cells[perfRowIndex, 6] = dateDes;
                    bgKpiPageSheet.Cells[perfRowIndex, 7] = "";

                    #region 性能数据
                    setPerfData(bgKpiPageSheet, perfRowIndex, date_cellPerfDataDic, dateDes);
                    #endregion

                    #region MR数据
                    setMRData(btsFusionInfo, bgKpiPageSheet, cellWorkParam, perfRowIndex, dateDes);
                    #endregion

                    perfRowIndex++;
                    curDate = curDate.AddDays(1);
                }
                cellIndex++;
            }

            #region 告警数据
            int alarmRowIndex = 28;
            if (btsFusionInfo.BtsAlarmInfoDic != null && btsFusionInfo.BtsAlarmInfoDic.Count > 0)
            {
                bgKpiPageSheet.Cells[alarmRowIndex, 2] = "有";
                alarmRowIndex += 3; 
                foreach (BtsAlarmDataBase data in btsFusionInfo.BtsAlarmInfoDic.Values)
                {
                    BtsAlarmData_XJ alarmData = data as BtsAlarmData_XJ;
                    bgKpiPageSheet.Cells[alarmRowIndex, 1] = alarmData.AlarmTitle;
                    bgKpiPageSheet.Cells[alarmRowIndex, 2] = alarmData.BeginTimeStr;
                    bgKpiPageSheet.Cells[alarmRowIndex, 3] = alarmData.EndTimeStr;
                    bgKpiPageSheet.Cells[alarmRowIndex, 4] = alarmData.Desc;
                    alarmRowIndex++;
                }
            }
            else
            {
                bgKpiPageSheet.Cells[alarmRowIndex, 2] = "无";
            }
            #endregion
        }

        private static void setPerfData(Excel.Worksheet bgKpiPageSheet, int perfRowIndex, Dictionary<string, CellPerfDataBase> date_cellPerfDataDic, string dateDes)
        {
            CellPerfDataBase cellPerfInfo;
            if (date_cellPerfDataDic != null && date_cellPerfDataDic.TryGetValue(dateDes, out cellPerfInfo))
            {
                int perfColIndex = 8;//列序号
                setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.RrcConnectTryCount);
                setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.RrcSetupSuccessRate);
                setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.ErabConnectTryCount);
                setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.ErabSetupSuccessRate);
                setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.WirelessConnectRate);
                setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.WirelessDropRate);
                setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.ErabDropRate);
                setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.InnerHandoverSuccessRate);
                setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex++, cellPerfInfo.PdcpThroughput_UL);
                setFloatValue(bgKpiPageSheet, perfRowIndex, perfColIndex, cellPerfInfo.PdcpThroughput_DL);
            }
        }

        private static void setMRData(BtsFusionInfo_XJ btsFusionInfo, Excel.Worksheet bgKpiPageSheet, CellWorkParamBase cellWorkParam, int perfRowIndex, string dateDes)
        {
            Dictionary<string, CellMRDataBase> date_CellMRDataDic;
            if (btsFusionInfo.CellMRInfoDic.TryGetValue(cellWorkParam.CGI, out date_CellMRDataDic))
            {
                CellMRDataBase cellMrInfo;
                if (date_CellMRDataDic.TryGetValue(dateDes, out cellMrInfo))
                {
                    setFloatValue(bgKpiPageSheet, perfRowIndex, 18, cellMrInfo.MrCoverRate);
                }
            }
        }
    }

    abstract class ExportIndoorBtsReportBase
    {
        protected ExportIndoorBtsReportBase()
        {

        }

        protected static void fillCellLevelKpiPage(Excel.Worksheet kpiPageSheet, InDoorCellAcceptInfo cellAcceptInfo
            , ref int rowIndex)
        {
            foreach (LevelTestKpiInfo levelInfo in cellAcceptInfo.LevelTestInfoList)
            {
                int colIndex = 1;
                kpiPageSheet.Cells[rowIndex, colIndex++] = levelInfo.FileName;
                setRateValue(kpiPageSheet, rowIndex, colIndex++, levelInfo.BalanceRate);
                setRateValue(kpiPageSheet, rowIndex, colIndex++, levelInfo.HandoverSucceedRate);
                setDoubleValue(kpiPageSheet, rowIndex, colIndex++, levelInfo.AvgRsrp);
                setDoubleValue(kpiPageSheet, rowIndex, colIndex++, levelInfo.AvgSinr);
                setRateValue(kpiPageSheet, rowIndex, colIndex++, levelInfo.SinrRate_HigherThan6);
                setRateValue(kpiPageSheet, rowIndex, colIndex++, levelInfo.SinrRate_HigherThan9);
                setRateValue(kpiPageSheet, rowIndex, colIndex++, levelInfo.RsrpB105Sinr6Rate);
                setRateValue(kpiPageSheet, rowIndex, colIndex++, levelInfo.RsrpB95Sinr9Rate);
                setRateValue(kpiPageSheet, rowIndex, colIndex++, levelInfo.RsrpRate_HigerThanB85);
                setDoubleValue(kpiPageSheet, rowIndex, colIndex++, levelInfo.AvgULSpeed);
                setDoubleValue(kpiPageSheet, rowIndex, colIndex++, levelInfo.AvgDLSpeed);
                setDoubleValue(kpiPageSheet, rowIndex, colIndex++, levelInfo.MaxULSpeed);
                setDoubleValue(kpiPageSheet, rowIndex, colIndex, levelInfo.MaxDLSpeed);
                rowIndex++;
            }
        }

        protected static void setFloatValue(Excel.Worksheet workSheet, int rowIndex, int colIndex, float value)
        {
            if (float.IsNaN(value) || value == float.MinValue)
            {
                workSheet.Cells[rowIndex, colIndex] = "";
            }
            else
            {
                workSheet.Cells[rowIndex, colIndex] = value;
            }
        }
        protected static void setDoubleValue(Excel.Worksheet workSheet, int rowIndex, int colIndex, double value)
        {
            if (double.IsNaN(value) || value == double.MinValue)
            {
                workSheet.Cells[rowIndex, colIndex] = "";
            }
            else
            {
                workSheet.Cells[rowIndex, colIndex] = value;
            }
        }
        protected static void setRateValue(Excel.Worksheet workSheet, int rowIndex, int colIndex, double? value)
        {
            if (value == null || double.IsNaN((double)value) || value == double.MinValue)
            {
                workSheet.Cells[rowIndex, colIndex] = "";
            }
            else
            {
                workSheet.Cells[rowIndex, colIndex] = Math.Round((double)value, 2) + "%";
            }
        }
        protected static void reportInfo(string str)
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo(str);
        }
        protected static void reportError(Exception ex)
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundError(ex);
        }
    }

    class QueryIndoorCoverPic_XJ : DIYSQLBase
    {
        protected string btsName;
        protected DateTime beginTime;
        protected DateTime endTime;
        public Dictionary<string, IndoorCoverPicInfo> Floor_CoverPicDic = new Dictionary<string, IndoorCoverPicInfo>();
        public QueryIndoorCoverPic_XJ(string btsName, DateTime beginTime, DateTime endTime)
            : base()
        {
            MainDB = true;
            this.btsName = btsName;
            this.beginTime = beginTime;
            this.endTime = endTime;
        }
        protected override string getSqlTextString()
        {
            return string.Format(@"exec sp_xinjiang_indoorCoverPic_get '{0}','{1}','{2}'"
                , beginTime.ToString(), endTime.ToString(), btsName);
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            E_VType[] arr = new E_VType[11];
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i] = E_VType.E_String;
            return arr;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    IndoorCoverPicInfo info = new IndoorCoverPicInfo();
                    info.Fill(package.Content);
                    if (!Floor_CoverPicDic.ContainsKey(info.CoverFloor))
                    {
                        Floor_CoverPicDic.Add(info.CoverFloor, info);
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    break;
                }
            }
        }
    }

    public class IndoorCoverPicInfo : IComparable<IndoorCoverPicInfo>
    {
        public string CellName { get; set; }
        public string CoverFloor { get; set; }
        public string TestDateDes { get; set; }
        public string RsrpPicPath { get; set; }
        public string RsrpLegendPicPath { get; set; }
        public string SinrPicPath { get; set; }
        public string SinrLegendPicPath { get; set; }
        public string FtpUlPicPath { get; set; }
        public string FtpDlPicPath { get; set; }
        public string FtpSpeedLegendPicPath { get; set; }
        public string SerialNumber { get; set; }//工单流水号
        public void Fill(MasterCom.RAMS.Net.Content content)
        {
            this.CellName = content.GetParamString();
            this.CoverFloor = content.GetParamString();
            this.TestDateDes = content.GetParamString();
            this.RsrpPicPath = content.GetParamString();
            this.RsrpLegendPicPath = content.GetParamString();
            this.SinrPicPath = content.GetParamString();
            this.SinrLegendPicPath = content.GetParamString();
            this.FtpUlPicPath = content.GetParamString();
            this.FtpDlPicPath = content.GetParamString();
            this.FtpSpeedLegendPicPath = content.GetParamString();
            this.SerialNumber = content.GetParamString();
        }

        public int CompareTo(IndoorCoverPicInfo other)
        {
            return this.CoverFloor.CompareTo(other.CoverFloor);
        }
    }
}
