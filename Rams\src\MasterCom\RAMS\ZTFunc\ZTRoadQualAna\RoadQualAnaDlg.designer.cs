﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class RoadQualAnaDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.btnEditColor = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.cmbGridColorMode = new DevExpress.XtraEditors.ComboBoxEdit();
            this.timeExitCellEnd = new DevExpress.XtraEditors.DateEdit();
            this.timeExitCellBegin = new DevExpress.XtraEditors.DateEdit();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.numAbnormalGate = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.numWeakRsrpGate = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.chk4G = new DevExpress.XtraEditors.CheckEdit();
            this.chk2G = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.numWeakRxlevGate = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.timeKpiEnd = new DevExpress.XtraEditors.DateEdit();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            this.timeKpiBegin = new DevExpress.XtraEditors.DateEdit();
            this.labelControl13 = new DevExpress.XtraEditors.LabelControl();
            this.cmbKpiTimeType = new DevExpress.XtraEditors.ComboBoxEdit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbGridColorMode.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeExitCellEnd.Properties.VistaTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeExitCellEnd.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeExitCellBegin.Properties.VistaTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeExitCellBegin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numAbnormalGate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakRsrpGate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk4G.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk2G.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakRxlevGate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeKpiEnd.Properties.VistaTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeKpiEnd.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeKpiBegin.Properties.VistaTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeKpiBegin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbKpiTimeType.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Location = new System.Drawing.Point(265, 303);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(59, 27);
            this.btnOK.TabIndex = 5;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(350, 303);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(59, 27);
            this.btnCancel.TabIndex = 6;
            this.btnCancel.Text = "取消";
            // 
            // btnEditColor
            // 
            this.btnEditColor.Location = new System.Drawing.Point(88, 261);
            this.btnEditColor.Name = "btnEditColor";
            this.btnEditColor.Size = new System.Drawing.Size(59, 27);
            this.btnEditColor.TabIndex = 28;
            this.btnEditColor.Text = "编辑";
            this.btnEditColor.Click += new System.EventHandler(this.btnEditColor_Click);
            // 
            // labelControl11
            // 
            this.labelControl11.Location = new System.Drawing.Point(21, 267);
            this.labelControl11.Name = "labelControl11";
            this.labelControl11.Size = new System.Drawing.Size(52, 14);
            this.labelControl11.TabIndex = 17;
            this.labelControl11.Text = "渲染指标:";
            // 
            // cmbGridColorMode
            // 
            this.cmbGridColorMode.Location = new System.Drawing.Point(153, 264);
            this.cmbGridColorMode.Name = "cmbGridColorMode";
            this.cmbGridColorMode.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cmbGridColorMode.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cmbGridColorMode.Size = new System.Drawing.Size(130, 21);
            this.cmbGridColorMode.TabIndex = 16;
            this.cmbGridColorMode.SelectedIndexChanged += new System.EventHandler(this.cmbGridColorMode_SelectedIndexChanged);
            // 
            // timeExitCellEnd
            // 
            this.timeExitCellEnd.EditValue = new System.DateTime(2018, 8, 1, 0, 0, 0, 0);
            this.timeExitCellEnd.Location = new System.Drawing.Point(269, 114);
            this.timeExitCellEnd.Name = "timeExitCellEnd";
            this.timeExitCellEnd.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.timeExitCellEnd.Properties.DisplayFormat.FormatString = "G";
            this.timeExitCellEnd.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.timeExitCellEnd.Properties.EditFormat.FormatString = "G";
            this.timeExitCellEnd.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.timeExitCellEnd.Properties.Mask.EditMask = "G";
            this.timeExitCellEnd.Properties.VistaDisplayMode = DevExpress.Utils.DefaultBoolean.True;
            this.timeExitCellEnd.Properties.VistaEditTime = DevExpress.Utils.DefaultBoolean.True;
            this.timeExitCellEnd.Properties.VistaTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.timeExitCellEnd.Size = new System.Drawing.Size(130, 21);
            this.timeExitCellEnd.TabIndex = 14;
            // 
            // timeExitCellBegin
            // 
            this.timeExitCellBegin.EditValue = new System.DateTime(2018, 8, 1, 0, 0, 0, 0);
            this.timeExitCellBegin.Location = new System.Drawing.Point(109, 114);
            this.timeExitCellBegin.Name = "timeExitCellBegin";
            this.timeExitCellBegin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.timeExitCellBegin.Properties.DisplayFormat.FormatString = "G";
            this.timeExitCellBegin.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.timeExitCellBegin.Properties.EditFormat.FormatString = "G";
            this.timeExitCellBegin.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.timeExitCellBegin.Properties.Mask.EditMask = "G";
            this.timeExitCellBegin.Properties.VistaDisplayMode = DevExpress.Utils.DefaultBoolean.True;
            this.timeExitCellBegin.Properties.VistaEditTime = DevExpress.Utils.DefaultBoolean.True;
            this.timeExitCellBegin.Properties.VistaTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.timeExitCellBegin.Size = new System.Drawing.Size(130, 21);
            this.timeExitCellBegin.TabIndex = 13;
            // 
            // labelControl9
            // 
            this.labelControl9.Location = new System.Drawing.Point(262, 229);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(12, 14);
            this.labelControl9.TabIndex = 12;
            this.labelControl9.Text = "%";
            // 
            // numAbnormalGate
            // 
            this.numAbnormalGate.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numAbnormalGate.Location = new System.Drawing.Point(153, 226);
            this.numAbnormalGate.Name = "numAbnormalGate";
            this.numAbnormalGate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numAbnormalGate.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numAbnormalGate.Size = new System.Drawing.Size(100, 21);
            this.numAbnormalGate.TabIndex = 11;
            // 
            // labelControl10
            // 
            this.labelControl10.Location = new System.Drawing.Point(21, 229);
            this.labelControl10.Name = "labelControl10";
            this.labelControl10.Size = new System.Drawing.Size(125, 14);
            this.labelControl10.TabIndex = 10;
            this.labelControl10.Text = "问题路段:    异常概率≥";
            // 
            // labelControl8
            // 
            this.labelControl8.Location = new System.Drawing.Point(357, 187);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(24, 14);
            this.labelControl8.TabIndex = 9;
            this.labelControl8.Text = "dBm";
            // 
            // numWeakRsrpGate
            // 
            this.numWeakRsrpGate.EditValue = new decimal(new int[] {
            100,
            0,
            0,
            -2147483648});
            this.numWeakRsrpGate.Location = new System.Drawing.Point(251, 188);
            this.numWeakRsrpGate.Name = "numWeakRsrpGate";
            this.numWeakRsrpGate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numWeakRsrpGate.Properties.MaxValue = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numWeakRsrpGate.Properties.MinValue = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numWeakRsrpGate.Size = new System.Drawing.Size(100, 21);
            this.numWeakRsrpGate.TabIndex = 8;
            // 
            // labelControl7
            // 
            this.labelControl7.Location = new System.Drawing.Point(21, 191);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(219, 14);
            this.labelControl7.TabIndex = 7;
            this.labelControl7.Text = "4G问题栅格:    首强在服小区平均Rsrp < ";
            // 
            // labelControl5
            // 
            this.labelControl5.Location = new System.Drawing.Point(21, 12);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(52, 14);
            this.labelControl5.TabIndex = 6;
            this.labelControl5.Text = "网络类型:";
            // 
            // chk4G
            // 
            this.chk4G.Location = new System.Drawing.Point(167, 10);
            this.chk4G.Name = "chk4G";
            this.chk4G.Properties.Caption = "4G";
            this.chk4G.Size = new System.Drawing.Size(75, 19);
            this.chk4G.TabIndex = 5;
            this.chk4G.CheckedChanged += new System.EventHandler(this.chk4G_CheckedChanged);
            // 
            // chk2G
            // 
            this.chk2G.Location = new System.Drawing.Point(86, 10);
            this.chk2G.Name = "chk2G";
            this.chk2G.Properties.Caption = "2G";
            this.chk2G.Size = new System.Drawing.Size(75, 19);
            this.chk2G.TabIndex = 4;
            this.chk2G.CheckedChanged += new System.EventHandler(this.chk2G_CheckedChanged);
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(21, 117);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(76, 14);
            this.labelControl4.TabIndex = 3;
            this.labelControl4.Text = "退服信息时间:";
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(248, 117);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(12, 14);
            this.labelControl3.TabIndex = 2;
            this.labelControl3.Text = "至";
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(357, 151);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(24, 14);
            this.labelControl1.TabIndex = 31;
            this.labelControl1.Text = "dBm";
            // 
            // numWeakRxlevGate
            // 
            this.numWeakRxlevGate.EditValue = new decimal(new int[] {
            100,
            0,
            0,
            -2147483648});
            this.numWeakRxlevGate.Location = new System.Drawing.Point(251, 152);
            this.numWeakRxlevGate.Name = "numWeakRxlevGate";
            this.numWeakRxlevGate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numWeakRxlevGate.Properties.MaxValue = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numWeakRxlevGate.Properties.MinValue = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numWeakRxlevGate.Size = new System.Drawing.Size(100, 21);
            this.numWeakRxlevGate.TabIndex = 30;
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(21, 155);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(224, 14);
            this.labelControl2.TabIndex = 29;
            this.labelControl2.Text = "2G问题栅格:    首强在服小区平均Rxlev < ";
            // 
            // labelControl6
            // 
            this.labelControl6.Location = new System.Drawing.Point(248, 80);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(12, 14);
            this.labelControl6.TabIndex = 32;
            this.labelControl6.Text = "至";
            // 
            // timeKpiEnd
            // 
            this.timeKpiEnd.EditValue = new System.DateTime(2018, 8, 1, 0, 0, 0, 0);
            this.timeKpiEnd.Location = new System.Drawing.Point(269, 77);
            this.timeKpiEnd.Name = "timeKpiEnd";
            this.timeKpiEnd.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.timeKpiEnd.Properties.DisplayFormat.FormatString = "G";
            this.timeKpiEnd.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.timeKpiEnd.Properties.EditFormat.FormatString = "G";
            this.timeKpiEnd.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.timeKpiEnd.Properties.Mask.EditMask = "G";
            this.timeKpiEnd.Properties.VistaDisplayMode = DevExpress.Utils.DefaultBoolean.True;
            this.timeKpiEnd.Properties.VistaEditTime = DevExpress.Utils.DefaultBoolean.True;
            this.timeKpiEnd.Properties.VistaTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.timeKpiEnd.Size = new System.Drawing.Size(130, 21);
            this.timeKpiEnd.TabIndex = 35;
            // 
            // labelControl12
            // 
            this.labelControl12.Location = new System.Drawing.Point(21, 80);
            this.labelControl12.Name = "labelControl12";
            this.labelControl12.Size = new System.Drawing.Size(76, 14);
            this.labelControl12.TabIndex = 33;
            this.labelControl12.Text = "路测数据时间:";
            // 
            // timeKpiBegin
            // 
            this.timeKpiBegin.EditValue = new System.DateTime(2018, 8, 1, 0, 0, 0, 0);
            this.timeKpiBegin.Location = new System.Drawing.Point(109, 77);
            this.timeKpiBegin.Name = "timeKpiBegin";
            this.timeKpiBegin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.timeKpiBegin.Properties.DisplayFormat.FormatString = "G";
            this.timeKpiBegin.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.timeKpiBegin.Properties.EditFormat.FormatString = "G";
            this.timeKpiBegin.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.timeKpiBegin.Properties.Mask.EditMask = "G";
            this.timeKpiBegin.Properties.VistaDisplayMode = DevExpress.Utils.DefaultBoolean.True;
            this.timeKpiBegin.Properties.VistaEditTime = DevExpress.Utils.DefaultBoolean.True;
            this.timeKpiBegin.Properties.VistaTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.timeKpiBegin.Size = new System.Drawing.Size(130, 21);
            this.timeKpiBegin.TabIndex = 34;
            // 
            // labelControl13
            // 
            this.labelControl13.Location = new System.Drawing.Point(21, 45);
            this.labelControl13.Name = "labelControl13";
            this.labelControl13.Size = new System.Drawing.Size(76, 14);
            this.labelControl13.TabIndex = 36;
            this.labelControl13.Text = "路测数据粒度:";
            // 
            // cmbKpiTimeType
            // 
            this.cmbKpiTimeType.EditValue = "";
            this.cmbKpiTimeType.Location = new System.Drawing.Point(109, 42);
            this.cmbKpiTimeType.Name = "cmbKpiTimeType";
            this.cmbKpiTimeType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cmbKpiTimeType.Properties.Items.AddRange(new object[] {
            "最新",
            "月",
            "天"});
            this.cmbKpiTimeType.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cmbKpiTimeType.Size = new System.Drawing.Size(130, 21);
            this.cmbKpiTimeType.TabIndex = 37;
            this.cmbKpiTimeType.SelectedIndexChanged += new System.EventHandler(this.cmbKpiTimeType_SelectedIndexChanged);
            // 
            // RoadQualAnaDlg
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CancelButton = this.btnCancel;
            this.ClientSize = new System.Drawing.Size(421, 342);
            this.Controls.Add(this.cmbKpiTimeType);
            this.Controls.Add(this.labelControl13);
            this.Controls.Add(this.labelControl6);
            this.Controls.Add(this.timeKpiEnd);
            this.Controls.Add(this.labelControl12);
            this.Controls.Add(this.timeKpiBegin);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.numWeakRxlevGate);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.btnEditColor);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.labelControl11);
            this.Controls.Add(this.labelControl7);
            this.Controls.Add(this.cmbGridColorMode);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.timeExitCellEnd);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.timeExitCellBegin);
            this.Controls.Add(this.chk2G);
            this.Controls.Add(this.labelControl9);
            this.Controls.Add(this.chk4G);
            this.Controls.Add(this.numAbnormalGate);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.labelControl10);
            this.Controls.Add(this.numWeakRsrpGate);
            this.Controls.Add(this.labelControl8);
            this.Name = "RoadQualAnaDlg";
            this.Text = "道路质量分析条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.cmbGridColorMode.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeExitCellEnd.Properties.VistaTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeExitCellEnd.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeExitCellBegin.Properties.VistaTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeExitCellBegin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numAbnormalGate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakRsrpGate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk4G.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk2G.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakRxlevGate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeKpiEnd.Properties.VistaTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeKpiEnd.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeKpiBegin.Properties.VistaTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeKpiBegin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbKpiTimeType.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.CheckEdit chk4G;
        private DevExpress.XtraEditors.CheckEdit chk2G;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.SpinEdit numWeakRsrpGate;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.SpinEdit numAbnormalGate;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraEditors.DateEdit timeExitCellBegin;
        private DevExpress.XtraEditors.DateEdit timeExitCellEnd;
        private DevExpress.XtraEditors.ComboBoxEdit cmbGridColorMode;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraEditors.SimpleButton btnEditColor;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SpinEdit numWeakRxlevGate;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.DateEdit timeKpiEnd;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private DevExpress.XtraEditors.DateEdit timeKpiBegin;
        private DevExpress.XtraEditors.LabelControl labelControl13;
        private DevExpress.XtraEditors.ComboBoxEdit cmbKpiTimeType;
    }
}