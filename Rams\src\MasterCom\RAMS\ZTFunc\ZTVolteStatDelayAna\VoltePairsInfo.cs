﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTVolteStatDelayAna
{
    public class VoltePairsInfo
    {
        public VoltePairsInfo(VolteCallInfo moCall,VolteCallInfo mtCall)
        {
            this.moCall = moCall;
            this.mtCall = mtCall;
        }
        public int Sn { get; set; }
        public string GridName
        {
            get 
            {
                if (MoCall != null)
                {
                    return MoCall.GridName;
                }
                return "";
            }
        }

        private VolteCallInfo moCall;
        public VolteCallInfo MoCall
        {
            get
            {
                if (moCall == null)
                {
                    return new VolteCallInfo("", 1);
                }
                else
                {
                    return moCall;
                }
            }
            set
            {
                moCall = value;
            }
        }

        private VolteCallInfo mtCall;
        public VolteCallInfo MtCall 
        {
            get
            {
                if (mtCall == null)
                {
                    return new VolteCallInfo("", 2);
                }
                else
                {
                    return mtCall;
                }
            }
            set
            {
                mtCall = value;
            }
        }
    }
    public class VolteItemsInfo
    {
        public int SN { get; set; }
        public string GridName { get { return ListVoltePairsInfo[0].GridName; } }
        public List<VoltePairsInfo> ListVoltePairsInfo { get; set; } = new List<VoltePairsInfo>();

    }
}
