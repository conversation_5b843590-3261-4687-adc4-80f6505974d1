﻿namespace MasterCom.RAMS.NOP
{
    partial class GISPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(GISPanel));
            this.mapControl = new AxMapWinGIS.AxMap();
            this.lbxLegend = new System.Windows.Forms.ListBox();
            ((System.ComponentModel.ISupportInitialize)(this.mapControl)).BeginInit();
            this.SuspendLayout();
            // 
            // mapControl
            // 
            this.mapControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.mapControl.Enabled = true;
            this.mapControl.Location = new System.Drawing.Point(0, 0);
            this.mapControl.Name = "mapControl";
            this.mapControl.OcxState = ((System.Windows.Forms.AxHost.State)(resources.GetObject("mapControl.OcxState")));
            this.mapControl.Size = new System.Drawing.Size(503, 362);
            this.mapControl.TabIndex = 2;
            // 
            // lbxLegend
            // 
            this.lbxLegend.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbxLegend.DrawMode = System.Windows.Forms.DrawMode.OwnerDrawFixed;
            this.lbxLegend.FormattingEnabled = true;
            this.lbxLegend.IntegralHeight = false;
            this.lbxLegend.ItemHeight = 18;
            this.lbxLegend.Items.AddRange(new object[] {
            "图例"});
            this.lbxLegend.Location = new System.Drawing.Point(353, 3);
            this.lbxLegend.Name = "lbxLegend";
            this.lbxLegend.SelectionMode = System.Windows.Forms.SelectionMode.None;
            this.lbxLegend.Size = new System.Drawing.Size(147, 143);
            this.lbxLegend.TabIndex = 3;
            this.lbxLegend.DrawItem += new System.Windows.Forms.DrawItemEventHandler(this.lbxLegend_DrawItem);
            this.lbxLegend.MouseDown += new System.Windows.Forms.MouseEventHandler(this.lbxLegend_MouseDown);
            this.lbxLegend.MouseMove += new System.Windows.Forms.MouseEventHandler(this.lbxLegend_MouseMove);
            this.lbxLegend.MouseUp += new System.Windows.Forms.MouseEventHandler(this.lbxLegend_MouseUp);
            // 
            // GISPanel
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.lbxLegend);
            this.Controls.Add(this.mapControl);
            this.Name = "GISPanel";
            this.Size = new System.Drawing.Size(503, 362);
            this.SizeChanged += new System.EventHandler(this.GISPanel_SizeChanged);
            ((System.ComponentModel.ISupportInitialize)(this.mapControl)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private AxMapWinGIS.AxMap mapControl;
        private System.Windows.Forms.ListBox lbxLegend;
    }
}
