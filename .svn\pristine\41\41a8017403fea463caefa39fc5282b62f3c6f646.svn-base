﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.Func
{
    public partial class DIYCellSetBriefDataCompareDataForm : MinCloseForm
    {
        public DIYCellSetBriefDataCompareDataForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        Dictionary<string, CellSetStat> cellSetStatTheSameDic = new Dictionary<string, CellSetStat>();
        Dictionary<string, CellSetStat> cellSetStatTheOldDic = new Dictionary<string, CellSetStat>();
        Dictionary<string, CellSetStat> cellSetStatTheNewDic = new Dictionary<string, CellSetStat>();

        public void FillData(Dictionary<string, CellSetStat> cellSetStatTheSame
            , Dictionary<string, CellSetStat> cellSetStatTheOld, Dictionary<string, CellSetStat> cellSetStatTheNew)
        {
            this.cellSetStatTheSameDic = cellSetStatTheSame;
            this.cellSetStatTheOldDic = cellSetStatTheOld;
            this.cellSetStatTheNewDic = cellSetStatTheNew;

            int idxOne = 1;
            foreach (string strkey in cellSetStatTheSameDic.Keys)
            {
                ListViewItem listViewItem = new ListViewItem();
                listViewItem.Text = idxOne.ToString();
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].strCity);
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].strGridType);
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].strGrid);

                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iAllTdCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iAllYDGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iAllWCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iAllLTGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iAllEvodCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iAllCdmaCellNum.ToString());

                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iFTdCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iFWCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iFEvodoCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iFYDGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iFLTGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iFDXCdmaCellNum.ToString());   

                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iVTdCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iVTdGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iVWCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iVWGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iVCd2000CellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iVGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iVLtGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iVCdCellNum.ToString());

                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iDTdCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iDTdGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iDWCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iDWGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iDEvdoCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iDEvdoCdma1xCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iDGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iDLtGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheSameDic[strkey].iDCdCellNum.ToString());                           

                lstViewSame.Items.Add(listViewItem);
                idxOne++;
            }

            idxOne = 1;
            foreach (string strkey in cellSetStatTheOldDic.Keys)
            {
                ListViewItem listViewItem = new ListViewItem();
                listViewItem.Text = idxOne.ToString();
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].strCity);
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].strGridType);
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].strGrid);

                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iAllTdCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iAllYDGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iAllWCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iAllLTGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iAllEvodCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iAllCdmaCellNum.ToString());

                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iFTdCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iFWCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iFEvodoCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iFYDGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iFLTGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iFDXCdmaCellNum.ToString());

                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iVTdCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iVTdGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iVWCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iVWGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iVCd2000CellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iVGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iVLtGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iVCdCellNum.ToString());

                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iDTdCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iDTdGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iDWCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iDWGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iDEvdoCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iDEvdoCdma1xCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iDGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iDLtGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheOldDic[strkey].iDCdCellNum.ToString());
                            
                lstViewOld.Items.Add(listViewItem);
                idxOne++;
            }

            idxOne = 1;
            foreach (string strkey in cellSetStatTheNewDic.Keys)
            {
                ListViewItem listViewItem = new ListViewItem();
                listViewItem.Text = idxOne.ToString();
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].strCity);
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].strGridType);
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].strGrid);

                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iAllTdCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iAllYDGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iAllWCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iAllLTGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iAllEvodCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iAllCdmaCellNum.ToString());

                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iFTdCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iFWCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iFEvodoCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iFYDGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iFLTGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iFDXCdmaCellNum.ToString());

                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iVTdCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iVTdGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iVWCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iVWGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iVCd2000CellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iVGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iVLtGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iVCdCellNum.ToString());

                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iDTdCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iDTdGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iDWCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iDWGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iDEvdoCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iDEvdoCdma1xCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iDGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iDLtGsmCellNum.ToString());
                listViewItem.SubItems.Add(cellSetStatTheNewDic[strkey].iDCdCellNum.ToString());
                            
                lstViewNew.Items.Add(listViewItem);
                idxOne++;
            }
        }

        private void DateExcelToolStripMenuItem_Click(object sender, EventArgs e)
        {
            outPutCellSetByExcel();
        }
        /// <summary>
        /// 将小区集结果集导出EXCEL
        /// </summary>
        private void outPutCellSetByExcel()
        {
            List<NPOIRow> datas = new List<NPOIRow>();
            List<NPOIRow> datas2 = new List<NPOIRow>();
            List<NPOIRow> datas3 = new List<NPOIRow>();

            #region EXCEL-SHEET1列表构造
            NPOIRow nr1 = new NPOIRow();
            List<object> cols = new List<object>();
            cols.Add("序号");
            cols.Add("城市");
            cols.Add("图层类型");
            cols.Add("网格");

            cols.Add("所有TD小区");
            cols.Add("所有移动GSM小区");
            cols.Add("所有W小区");
            cols.Add("所有联通GSM小区");
            cols.Add("所有EVDO小区");
            cols.Add("所有CMDA小区");

            cols.Add("TD锁网小区");
            cols.Add("W锁网小区");
            cols.Add("EVDO锁网小区");
            cols.Add("移动GSM锁网小区");
            cols.Add("联通GSM锁网小区");
            cols.Add("电信CDMA锁网小区");    

            cols.Add("移动语音TD小区");
            cols.Add("移动语音TD-GSM小区");
            cols.Add("联通语音W小区");
            cols.Add("联通语音W-GSM小区");
            cols.Add("电信CDMA2000空闲小区");
            cols.Add("移动语音GSM小区");
            cols.Add("联通语音GSM小区");
            cols.Add("电信语音CDMA小区");

            cols.Add("移动数据TD小区");
            cols.Add("移动数据TD-GSM小区");
            cols.Add("联通数据W小区");
            cols.Add("联通数据W-GSM小区");
            cols.Add("电信数据CDMA2000小区");
            cols.Add("电信数据CDMA小区(2G)");
            cols.Add("移动数据GSM小区");
            cols.Add("联通数据GSM小区");
            cols.Add("电信数据CDMA小区");                 

            nr1.cellValues = cols;
            datas.Add(nr1);
            #endregion

            #region EXCEL-SHEET2列表构造
            NPOIRow nr2 = new NPOIRow();
            List<object> cols2 = new List<object>();
            cols2.Add("序号");
            cols2.Add("城市");
            cols2.Add("图层类型");
            cols2.Add("网格");

            cols2.Add("所有TD小区");
            cols2.Add("所有移动GSM小区");
            cols2.Add("所有W小区");
            cols2.Add("所有联通GSM小区");
            cols2.Add("所有EVDO小区");
            cols2.Add("所有CMDA小区");

            cols2.Add("TD锁网小区");
            cols2.Add("W锁网小区");
            cols2.Add("EVDO锁网小区");
            cols2.Add("移动GSM锁网小区");
            cols2.Add("联通GSM锁网小区");
            cols2.Add("电信CDMA锁网小区");

            cols2.Add("移动语音TD小区");
            cols2.Add("移动语音TD-GSM小区");
            cols2.Add("联通语音W小区");
            cols2.Add("联通语音W-GSM小区");
            cols2.Add("电信CDMA2000空闲小区");
            cols2.Add("移动语音GSM小区");
            cols2.Add("联通语音GSM小区");
            cols2.Add("电信语音CDMA小区");

            cols2.Add("移动数据TD小区");
            cols2.Add("移动数据TD-GSM小区");
            cols2.Add("联通数据W小区");
            cols2.Add("联通数据W-GSM小区");
            cols2.Add("电信数据CDMA2000小区");
            cols2.Add("电信数据CDMA小区(2G)");
            cols2.Add("移动数据GSM小区");
            cols2.Add("联通数据GSM小区");
            cols2.Add("电信数据CDMA小区");
          
            nr2.cellValues = cols2;
            datas2.Add(nr2);
            #endregion

            #region EXCEL-SHEET3列表构造
            NPOIRow nr3 = new NPOIRow();
            List<object> cols3 = new List<object>();
            cols3.Add("序号");
            cols3.Add("城市");
            cols3.Add("图层类型");
            cols3.Add("网格");

            cols3.Add("所有TD小区");
            cols3.Add("所有移动GSM小区");
            cols3.Add("所有W小区");
            cols3.Add("所有联通GSM小区");
            cols3.Add("所有EVDO小区");
            cols3.Add("所有CMDA小区");

            cols3.Add("TD锁网小区");
            cols3.Add("W锁网小区");
            cols3.Add("EVDO锁网小区");
            cols3.Add("移动GSM锁网小区");
            cols3.Add("联通GSM锁网小区");
            cols3.Add("电信CDMA锁网小区");

            cols3.Add("移动语音TD小区");
            cols3.Add("移动语音TD-GSM小区");
            cols3.Add("联通语音W小区");
            cols3.Add("联通语音W-GSM小区");
            cols3.Add("电信CDMA2000空闲小区");
            cols3.Add("移动语音GSM小区");
            cols3.Add("联通语音GSM小区");
            cols3.Add("电信语音CDMA小区");

            cols3.Add("移动数据TD小区");
            cols3.Add("移动数据TD-GSM小区");
            cols3.Add("联通数据W小区");
            cols3.Add("联通数据W-GSM小区");
            cols3.Add("电信数据CDMA2000小区");
            cols3.Add("电信数据CDMA小区(2G)");
            cols3.Add("移动数据GSM小区");
            cols3.Add("联通数据GSM小区");
            cols3.Add("电信数据CDMA小区");
                     
            nr3.cellValues = cols3;
            datas3.Add(nr3);
            #endregion

            int idx = 0;
            foreach (string strkey in cellSetStatTheSameDic.Keys)
            {
                NPOIRow nr = new NPOIRow();
                List<object> objs = new List<object>();
                idx++;
                objs.Add(idx.ToString());
                objs.Add(cellSetStatTheSameDic[strkey].strCity);
                objs.Add(cellSetStatTheSameDic[strkey].strGridType);
                objs.Add(cellSetStatTheSameDic[strkey].strGrid);

                objs.Add(cellSetStatTheSameDic[strkey].iVTdCellNum);
                objs.Add(cellSetStatTheSameDic[strkey].iVTdGsmCellNum);
                objs.Add(cellSetStatTheSameDic[strkey].iVWCellNum);
                objs.Add(cellSetStatTheSameDic[strkey].iVWGsmCellNum);
                objs.Add(cellSetStatTheSameDic[strkey].iVCd2000CellNum);
                objs.Add(cellSetStatTheSameDic[strkey].iVGsmCellNum);
                objs.Add(cellSetStatTheSameDic[strkey].iVLtGsmCellNum);
                objs.Add(cellSetStatTheSameDic[strkey].iVCdCellNum);

                objs.Add(cellSetStatTheSameDic[strkey].iDTdCellNum);
                objs.Add(cellSetStatTheSameDic[strkey].iDTdGsmCellNum);
                objs.Add(cellSetStatTheSameDic[strkey].iDWCellNum);
                objs.Add(cellSetStatTheSameDic[strkey].iDWGsmCellNum);
                objs.Add(cellSetStatTheSameDic[strkey].iDEvdoCellNum);
                objs.Add(cellSetStatTheSameDic[strkey].iDEvdoCdma1xCellNum);
                objs.Add(cellSetStatTheSameDic[strkey].iDGsmCellNum);
                objs.Add(cellSetStatTheSameDic[strkey].iDLtGsmCellNum);
                objs.Add(cellSetStatTheSameDic[strkey].iDCdCellNum);

                objs.Add(cellSetStatTheSameDic[strkey].iFTdCellNum);
                objs.Add(cellSetStatTheSameDic[strkey].iFWCellNum);
                objs.Add(cellSetStatTheSameDic[strkey].iFEvodoCellNum);
                objs.Add(cellSetStatTheSameDic[strkey].iFYDGsmCellNum);
                objs.Add(cellSetStatTheSameDic[strkey].iFLTGsmCellNum);
                objs.Add(cellSetStatTheSameDic[strkey].iFDXCdmaCellNum);

                objs.Add(cellSetStatTheSameDic[strkey].iAllTdCellNum);
                objs.Add(cellSetStatTheSameDic[strkey].iAllYDGsmCellNum);
                objs.Add(cellSetStatTheSameDic[strkey].iAllWCellNum);
                objs.Add(cellSetStatTheSameDic[strkey].iAllLTGsmCellNum);
                objs.Add(cellSetStatTheSameDic[strkey].iAllEvodCellNum);
                objs.Add(cellSetStatTheSameDic[strkey].iAllCdmaCellNum);

                nr.cellValues = objs;
                datas.Add(nr);
            }

            idx = 0;
            foreach (string strkey in cellSetStatTheOldDic.Keys)
            {
                NPOIRow nr = new NPOIRow();
                List<object> objs = new List<object>();
                idx++;
                objs.Add(idx.ToString());
                objs.Add(cellSetStatTheOldDic[strkey].strCity);
                objs.Add(cellSetStatTheOldDic[strkey].strGridType);
                objs.Add(cellSetStatTheOldDic[strkey].strGrid);

                objs.Add(cellSetStatTheOldDic[strkey].iVTdCellNum);
                objs.Add(cellSetStatTheOldDic[strkey].iVTdGsmCellNum);
                objs.Add(cellSetStatTheOldDic[strkey].iVWCellNum);
                objs.Add(cellSetStatTheOldDic[strkey].iVWGsmCellNum);
                objs.Add(cellSetStatTheOldDic[strkey].iVCd2000CellNum);
                objs.Add(cellSetStatTheOldDic[strkey].iVGsmCellNum);
                objs.Add(cellSetStatTheOldDic[strkey].iVLtGsmCellNum);
                objs.Add(cellSetStatTheOldDic[strkey].iVCdCellNum);

                objs.Add(cellSetStatTheOldDic[strkey].iDTdCellNum);
                objs.Add(cellSetStatTheOldDic[strkey].iDTdGsmCellNum);
                objs.Add(cellSetStatTheOldDic[strkey].iDWCellNum);
                objs.Add(cellSetStatTheOldDic[strkey].iDWGsmCellNum);
                objs.Add(cellSetStatTheOldDic[strkey].iDEvdoCellNum);
                objs.Add(cellSetStatTheOldDic[strkey].iDEvdoCdma1xCellNum);
                objs.Add(cellSetStatTheOldDic[strkey].iDGsmCellNum);
                objs.Add(cellSetStatTheOldDic[strkey].iDLtGsmCellNum);
                objs.Add(cellSetStatTheOldDic[strkey].iDCdCellNum);

                objs.Add(cellSetStatTheOldDic[strkey].iFTdCellNum);
                objs.Add(cellSetStatTheOldDic[strkey].iFWCellNum);
                objs.Add(cellSetStatTheOldDic[strkey].iFEvodoCellNum);
                objs.Add(cellSetStatTheOldDic[strkey].iFYDGsmCellNum);
                objs.Add(cellSetStatTheOldDic[strkey].iFLTGsmCellNum);
                objs.Add(cellSetStatTheOldDic[strkey].iFDXCdmaCellNum);

                objs.Add(cellSetStatTheOldDic[strkey].iAllTdCellNum);
                objs.Add(cellSetStatTheOldDic[strkey].iAllYDGsmCellNum);
                objs.Add(cellSetStatTheOldDic[strkey].iAllWCellNum);
                objs.Add(cellSetStatTheOldDic[strkey].iAllLTGsmCellNum);
                objs.Add(cellSetStatTheOldDic[strkey].iAllEvodCellNum);
                objs.Add(cellSetStatTheOldDic[strkey].iAllCdmaCellNum);

                nr.cellValues = objs;
                datas2.Add(nr);
            }

            idx = 0;
            foreach (string strkey in cellSetStatTheNewDic.Keys)
            {
                NPOIRow nr = new NPOIRow();
                List<object> objs = new List<object>();
                idx++;
                objs.Add(idx.ToString());
                objs.Add(cellSetStatTheNewDic[strkey].strCity);
                objs.Add(cellSetStatTheNewDic[strkey].strGridType);
                objs.Add(cellSetStatTheNewDic[strkey].strGrid);

                objs.Add(cellSetStatTheNewDic[strkey].iVTdCellNum);
                objs.Add(cellSetStatTheNewDic[strkey].iVTdGsmCellNum);
                objs.Add(cellSetStatTheNewDic[strkey].iVWCellNum);
                objs.Add(cellSetStatTheNewDic[strkey].iVWGsmCellNum);
                objs.Add(cellSetStatTheNewDic[strkey].iVCd2000CellNum);
                objs.Add(cellSetStatTheNewDic[strkey].iVGsmCellNum);
                objs.Add(cellSetStatTheNewDic[strkey].iVLtGsmCellNum);
                objs.Add(cellSetStatTheNewDic[strkey].iVCdCellNum);

                objs.Add(cellSetStatTheNewDic[strkey].iDTdCellNum);
                objs.Add(cellSetStatTheNewDic[strkey].iDTdGsmCellNum);
                objs.Add(cellSetStatTheNewDic[strkey].iDWCellNum);
                objs.Add(cellSetStatTheNewDic[strkey].iDWGsmCellNum);
                objs.Add(cellSetStatTheNewDic[strkey].iDEvdoCellNum);
                objs.Add(cellSetStatTheNewDic[strkey].iDEvdoCdma1xCellNum);
                objs.Add(cellSetStatTheNewDic[strkey].iDGsmCellNum);
                objs.Add(cellSetStatTheNewDic[strkey].iDLtGsmCellNum);
                objs.Add(cellSetStatTheNewDic[strkey].iDCdCellNum);

                objs.Add(cellSetStatTheNewDic[strkey].iFTdCellNum);
                objs.Add(cellSetStatTheNewDic[strkey].iFWCellNum);
                objs.Add(cellSetStatTheNewDic[strkey].iFEvodoCellNum);
                objs.Add(cellSetStatTheNewDic[strkey].iFYDGsmCellNum);
                objs.Add(cellSetStatTheNewDic[strkey].iFLTGsmCellNum);
                objs.Add(cellSetStatTheNewDic[strkey].iFDXCdmaCellNum);

                objs.Add(cellSetStatTheNewDic[strkey].iAllTdCellNum);
                objs.Add(cellSetStatTheNewDic[strkey].iAllYDGsmCellNum);
                objs.Add(cellSetStatTheNewDic[strkey].iAllWCellNum);
                objs.Add(cellSetStatTheNewDic[strkey].iAllLTGsmCellNum);
                objs.Add(cellSetStatTheNewDic[strkey].iAllEvodCellNum);
                objs.Add(cellSetStatTheNewDic[strkey].iAllCdmaCellNum);

                nr.cellValues = objs;
                datas3.Add(nr);
            }

            List<List<NPOIRow>> nrDatasList = new List<List<NPOIRow>>();
            nrDatasList.Add(datas);
            nrDatasList.Add(datas2);
            nrDatasList.Add(datas3);

            List<string> sheetNames = new List<string>();
            sheetNames.Add("两月共占有小区数");
            sheetNames.Add("前一个月独有小区数");
            sheetNames.Add("当前月独有小区数");

            ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames);

        }
    }
}
