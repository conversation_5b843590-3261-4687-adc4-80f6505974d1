using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public class WLANTestPoint : TestPoint
    {
        public override void Fill(MasterCom.RAMS.Net.Content content)
        {
            FileID = content.GetParamInt();
            SN = content.GetParamInt();
            Time = content.GetParamInt();
            Millisecond = (short)content.GetParamInt();
            MS = (byte)content.GetParamInt();
            Longitude = content.GetParamDouble();
            Latitude = content.GetParamDouble();
            this["ialtitude"] = content.GetParamInt();
            this["ispeed"] = content.GetParamInt();
            this["mode"] = content.GetParamShort();
            this["Associate_Status"] = content.GetParamByte();
            this["SSID"] = content.GetParamString();
            this["BSSID"] = content.GetParamString();
            this["IP_Address"] = content.GetParamInt();
            this["AP_RSSI"] = content.GetParamShort();
            this["AP_SNR"] = content.GetParamShort();
            this["AP_NFI"] = content.GetParamFloat();
            this["AP_SFI"] = content.GetParamFloat();
            this["AP_Channel"] = content.GetParamShort();
            this["AP_Freq"] = content.GetParamShort();
            this["APP_type"] = content.GetParamInt();
            this["APP_Status"] = content.GetParamInt();
            this["APP_DataStatus_DL"] = content.GetParamInt();
            this["APP_DataStatus_UL"] = content.GetParamInt();
            this["APP_TotalSize"] = content.GetParamInt();
            this["APP_TransferedSize"] = content.GetParamInt();
            this["APP_Speed"] = content.GetParamInt();
            this["APP_Average_Speed"] = content.GetParamInt();
            this["APP_TransferedTime"] = content.GetParamInt();
            this["APP_TotalTime"] = content.GetParamInt();
            for (int index = 0; index < 6; index++)
            {
                this["ARR_AP_BSSIDthis", index] = content.GetParamByte();
            }
            this["ARR_AP_connect"] = content.GetParamByte();
            this["ARR_AP_RSSI"] = content.GetParamShort();
            this["ARR_AP_SNR"] = content.GetParamShort();
            this["ARR_AP_Noise"] = content.GetParamShort();
            this["ARR_AP_Channel"] = content.GetParamShort();
            this["ARR_AP_Freqecncy"] = content.GetParamShort();
            this["ARR_AP_Security"] = content.GetParamInt();
            this["ARR_AP_susp_speed"] = content.GetParamInt();
            this["ARR_AP_NetType"] = content.GetParamShort();
            this["ARR_AP_Infrastructure"] = content.GetParamInt();
            this["ARR_AP_SFI"] = content.GetParamInt();
            this["ARR_AP_NFI"] = content.GetParamInt();
            this["ARR_AP_CDS_First_Seen"] = content.GetParamInt();
            this["ARR_AP_CDS_Last_Seen"] = content.GetParamInt();
            this["ARR_AP_CDS_Active_Time"] = content.GetParamInt();
            this["ARR_AP_CDS_RxThrput"] = content.GetParamInt();
            this["ARR_AP_CDS_TxThrput"] = content.GetParamInt();
            this["ARR_AP_CDS_Band"] = content.GetParamInt();
            this["ARR_AP_CDS_Encry"] = content.GetParamInt();
            this["ARR_AP_CDS_Bridge"] = content.GetParamInt();
            this["ARR_AP_CDS_PreambleLen"] = content.GetParamByte();
            this["ARR_AP_CDS_PCF_DCF"] = content.GetParamByte();
            this["ARR_AP_CDS_BeaconInterval"] = content.GetParamInt();
            this["ARR_AP_CDS_MaxRate"] = content.GetParamInt();
            this["ARR_AP_SSID"] = content.GetParamString();
            for (int index = 0; index < 6; index++)
            {
                this["ARR_DVS_BSSIDthis", index] = content.GetParamByte();
            }
            this["ARR_DVS_Channel"] = content.GetParamShort();
            this["ARR_DVS_RSSI"] = content.GetParamShort();
            this["ARR_DVS_Noise"] = content.GetParamShort();
            this["ARR_DVS_SNR"] = content.GetParamShort();
            this["ARR_DVS_Total_Frames"] = content.GetParamInt();
            this["ARR_DVS_Manage_Frames"] = content.GetParamInt();
            this["ARR_DVS_Ctrl_Frames"] = content.GetParamInt();
            this["ARR_DVS_Data_Frames"] = content.GetParamInt();
            this["ARR_DVS_Retry_Frames"] = content.GetParamInt();
            this["ARR_DVS_Broadcast_Frames"] = content.GetParamInt();
            this["ARR_DVS_Multi_Frames"] = content.GetParamInt();
            this["ARR_DVS_Single_Frames"] = content.GetParamInt();
            this["ARR_DVS_Total_Bytes"] = content.GetParamInt();
            this["ARR_DVS_Send_Bytes"] = content.GetParamInt();
            this["ARR_DVS_Receive_Bytes"] = content.GetParamInt();
            this["ARR_DVS_Send_Rate"] = content.GetParamInt();
            this["ARR_DVS_Receive_Rate"] = content.GetParamInt();
            this["ARR_DVS_TxFrames"] = content.GetParamInt();
            this["ARR_DVS_RxFrames"] = content.GetParamInt();
            this["ARR_DVS_TxFrames_Rate"] = content.GetParamInt();
            this["ARR_DVS_RxFrames_Rate"] = content.GetParamInt();
            this["ARR_DVS_TxRetrans"] = content.GetParamInt();
            this["ARR_DVS_RxRetrans"] = content.GetParamInt();
            this["ARR_DVS_TxFER"] = content.GetParamInt();
            this["ARR_DVS_RxFER"] = content.GetParamInt();
            this["ARR_DVS_HX_PreambleLen"] = content.GetParamByte();
            this["ARR_DVS_HX_PCF_DCF"] = content.GetParamByte();
            this["ARR_DVS_CDS_First_Seen"] = content.GetParamInt();
            this["ARR_DVS_CDS_Last_Seen"] = content.GetParamInt();
            this["ARR_DVS_CDS_Encry"] = content.GetParamInt();
            this["ARR_DVS_CDS_Band"] = content.GetParamInt();
            this["ARR_DVS_CDS_Data"] = content.GetParamInt();
            this["ARR_DVS_SSID"] = content.GetParamString();
            this["ARR_CHAN_Channel"] = content.GetParamShort();
            this["ARR_CHAN_Noise"] = content.GetParamShort();
            this["ARR_CHAN_RSSI"] = content.GetParamShort();
            this["ARR_CHAN_SNR"] = content.GetParamShort();
            this["ARR_CHAN_AP_RSSI"] = content.GetParamShort();
            this["ARR_CHAN_Active_APs"] = content.GetParamInt();
            this["ARR_CHAN_Active_Stations"] = content.GetParamInt();
            this["ARR_CHAN_Active_SSIDs"] = content.GetParamInt();
            this["ARR_CHAN_Total_Stations"] = content.GetParamInt();
            this["ARR_CHAN_Band_Usage"] = content.GetParamInt();
            this["ARR_CHAN_Physical_rate"] = content.GetParamInt();
            this["ARR_CHAN_AdjChannelBytes"] = content.GetParamInt();
            this["ARR_CHAN_ThroughPut"] = content.GetParamInt();
            this["ARR_CHAN_Total_Frames"] = content.GetParamInt();
            this["ARR_CHAN_Valid_Frames"] = content.GetParamInt();
            this["ARR_CHAN_CRC_Frames"] = content.GetParamInt();
            this["ARR_CHAN_Error_Frames"] = content.GetParamInt();
            this["ARR_CHAN_Retry_Frames"] = content.GetParamInt();
            this["ARR_CHAN_Manage_Frames"] = content.GetParamInt();
            this["ARR_CHAN_Ctrl_Frames"] = content.GetParamInt();
            this["ARR_CHAN_Data_Frames"] = content.GetParamInt();
            this["ARR_CHAN_Broadcast_Frames"] = content.GetParamInt();
            this["ARR_CHAN_Multi_Frames"] = content.GetParamInt();
            this["ARR_CHAN_Single_Frames"] = content.GetParamInt();
            this["ARR_CHAN_Total_Bytes"] = content.GetParamInt();
            this["ARR_CHAN_Valid_Bytes"] = content.GetParamInt();
            this["ARR_CHAN_CRC_Bytes"] = content.GetParamInt();
            this["ARR_CHAN_Error_Bytes"] = content.GetParamInt();
            this["ARR_CHAN_Retry_Bytes"] = content.GetParamInt();
            this["ARR_CHAN_Broadcast_Bytes"] = content.GetParamInt();
            this["ARR_CHAN_Multi_Bytes"] = content.GetParamInt();
            this["ARR_CHAN_Single_Bytes"] = content.GetParamInt();
            this["ARR_CHAN_Tx_Bytes"] = content.GetParamInt();
            this["ARR_CHAN_Rx_Bytes"] = content.GetParamInt();
            this["Card1_Card_Band_Usage"] = content.GetParamInt();
            this["Card1_Card_Rx_ThrPut"] = content.GetParamInt();
            this["Card1_Card_Rx_Retrans"] = content.GetParamInt();
            this["Card1_Card_Rx_Physical_Rate"] = content.GetParamInt();
            this["Card1_Card_Tx_ThrPut"] = content.GetParamInt();
            this["Card1_Card_Tx_Retrans"] = content.GetParamInt();
            this["Card1_Card_Tx_Physical_Rate"] = content.GetParamInt();
            this["Card1_BSS_Band_Usage"] = content.GetParamInt();
            this["Card1_BSS_ThrPut"] = content.GetParamInt();
            this["Card1_BSS_Retrans"] = content.GetParamInt();
            this["Card1_BSS_Physical_Rate"] = content.GetParamInt();
            this["Card1_BSS_Active_Users"] = content.GetParamInt();
            this["Card1_BSS_Total_Users"] = content.GetParamInt();
            this["Card1_Channel_Band_Usage"] = content.GetParamInt();
            this["Card1_Channel_ThrPut"] = content.GetParamInt();
            this["Card1_Channel_Retrans"] = content.GetParamInt();
            this["Card1_Channel_Physical_Rate"] = content.GetParamInt();
            this["Card1_Channel_FER"] = content.GetParamInt();
            this["Card1_Channel_Active_Users"] = content.GetParamInt();
            this["Card1_Channel_Total_Users"] = content.GetParamInt();
            this["Card1_Traffic_from_Adj_Channel"] = content.GetParamInt();
            this["Card1_AP_Total_Number"] = content.GetParamInt();
            this["Card2_current_lookahead"] = content.GetParamInt();
            this["Card2_maximum_lookahead"] = content.GetParamInt();
            this["Card2_max_frame_size"] = content.GetParamInt();
            this["Card2_link_speed"] = content.GetParamInt();
            this["Card2_transmit_buffer_space"] = content.GetParamInt();
            this["Card2_receive_buffer_space"] = content.GetParamInt();
            this["Card2_transmit_block_size"] = content.GetParamInt();
            this["Card2_receive_block_size"] = content.GetParamInt();
            this["Card2_max_total_size"] = content.GetParamInt();
            this["Card2_media_connect_status"] = content.GetParamByte();
            this["Card2_max_send_packets"] = content.GetParamInt();
            this["Card2_xmit_OK"] = content.GetParamInt();
            this["Card2_rcv_OK"] = content.GetParamInt();
            this["Card2_xmit_error"] = content.GetParamInt();
            this["Card2_rcv_error"] = content.GetParamInt();
            this["Card2_rcv_error_no_buffer"] = content.GetParamInt();
            this["Card2_beacon_pereod"] = content.GetParamInt();
            this["Card2_atim_window"] = content.GetParamInt();
            this["Card2_fragmentation_threshold"] = content.GetParamInt();
            this["Card2_Signal_level"] = content.GetParamShort();
            this["Card2_Signal_level_in"] = content.GetParamShort();
            this["Card2_Noise_level_in"] = content.GetParamShort();
            this["Card2_Rate"] = content.GetParamInt();
            this["Card2_Band"] = content.GetParamShort();
            this["Card2_Channel"] = content.GetParamShort();
            this["Card2_hardware_status"] = content.GetParamString();
            this["Card2_media_stream_mode"] = content.GetParamString();
            this["Card2_wep_status"] = content.GetParamString();
            this["Card2_authentication_mode"] = content.GetParamString();
            this["Card2_power_mode"] = content.GetParamString();
            this["reserved"] = content.GetParamByte();
        }

        //public override DateTime DateTime
        //{
        //    get { return JavaDate.GetDateTime(Time * 1000L + Millisecond); }
        //}

    }
}
