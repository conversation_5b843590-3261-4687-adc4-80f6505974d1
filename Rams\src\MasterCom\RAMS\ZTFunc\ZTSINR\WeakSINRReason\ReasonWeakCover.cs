﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    public class ReasonWeakCover : ReasonBase
    {
        public ReasonWeakCover()
        {
            this.Name = "弱覆盖";
        }
        public float MaxRSRP { get; set; } = -105;
        public override bool IsValid(Model.TestPoint tp, params object[] resvParams)
        {
            object value = GetRSRP(tp);
            if (value == null)
            {
                return false;
            }
            float rsrp = float.Parse(value.ToString());
            return rsrp <= MaxRSRP && rsrp >= -141;
        }
        protected object GetRSRP(Model.TestPoint tp)
        {
            if (tp is Model.LTEFddTestPoint)
            {
                return tp["lte_fdd_RSRP"];
            }
            else if (tp is Model.ScanTestPoint_NBIOT)
            {
                return tp["LTESCAN_TopN_CELL_Specific_RSRP"];
            }
            return tp["lte_RSRP"];
        }
        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Enable"] = this.Enable;
                param["MaxRSRP"] = this.MaxRSRP;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("Enable"))
                {
                    this.Enable = (bool)param["Enable"];
                }
                if (param.ContainsKey("MaxRSRP"))
                {
                    this.MaxRSRP = (float)param["MaxRSRP"];
                }
            }
        }
    }
}
