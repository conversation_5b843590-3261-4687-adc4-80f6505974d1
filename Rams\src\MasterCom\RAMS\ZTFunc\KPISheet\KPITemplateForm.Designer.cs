﻿namespace MasterCom.RAMS.ZTFunc.KPISheet
{
    partial class KPITemplateForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.listBox_template = new System.Windows.Forms.ListBox();
            this.groupControlTemplate = new DevExpress.XtraEditors.GroupControl();
            this.gridCtrlNetType = new DevExpress.XtraGrid.GridControl();
            this.gvTmpl = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.listBox_subTemplate = new System.Windows.Forms.ListBox();
            this.groupControlColInfo = new DevExpress.XtraEditors.GroupControl();
            this.chkBox_selectAll = new System.Windows.Forms.CheckBox();
            this.label1 = new System.Windows.Forms.Label();
            this.txtTemplateName = new DevExpress.XtraEditors.TextEdit();
            this.btn_RemoveTemplate = new DevExpress.XtraEditors.SimpleButton();
            this.chkList_columnItems = new DevExpress.XtraEditors.CheckedListBoxControl();
            this.btn_Save = new DevExpress.XtraEditors.SimpleButton();
            this.btn_NewTemplate = new DevExpress.XtraEditors.SimpleButton();
            this.btn_Up = new DevExpress.XtraEditors.SimpleButton();
            this.btn_Down = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControlTemplate)).BeginInit();
            this.groupControlTemplate.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlNetType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvTmpl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControlColInfo)).BeginInit();
            this.groupControlColInfo.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtTemplateName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkList_columnItems)).BeginInit();
            this.SuspendLayout();
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Default;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl1.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.groupControl1);
            this.splitContainerControl1.Panel1.Controls.Add(this.groupControlTemplate);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.AutoScroll = true;
            this.splitContainerControl1.Panel2.Controls.Add(this.groupControl2);
            this.splitContainerControl1.Panel2.Controls.Add(this.groupControlColInfo);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(884, 595);
            this.splitContainerControl1.SplitterPosition = 206;
            this.splitContainerControl1.TabIndex = 10;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // groupControl1
            // 
            this.groupControl1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupControl1.Controls.Add(this.listBox_template);
            this.groupControl1.Location = new System.Drawing.Point(0, 124);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(205, 464);
            this.groupControl1.TabIndex = 14;
            this.groupControl1.Text = "模板列表";
            // 
            // listBox_template
            // 
            this.listBox_template.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listBox_template.FormattingEnabled = true;
            this.listBox_template.ItemHeight = 14;
            this.listBox_template.Location = new System.Drawing.Point(2, 23);
            this.listBox_template.Name = "listBox_template";
            this.listBox_template.Size = new System.Drawing.Size(201, 439);
            this.listBox_template.TabIndex = 14;
            this.listBox_template.SelectedIndexChanged += new System.EventHandler(this.listBox_template_SelectedIndexChanged);
            // 
            // groupControlTemplate
            // 
            this.groupControlTemplate.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupControlTemplate.Controls.Add(this.gridCtrlNetType);
            this.groupControlTemplate.Location = new System.Drawing.Point(0, 0);
            this.groupControlTemplate.Name = "groupControlTemplate";
            this.groupControlTemplate.Size = new System.Drawing.Size(205, 118);
            this.groupControlTemplate.TabIndex = 0;
            this.groupControlTemplate.Text = "网络类型";
            // 
            // gridCtrlNetType
            // 
            this.gridCtrlNetType.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridCtrlNetType.Location = new System.Drawing.Point(2, 23);
            this.gridCtrlNetType.MainView = this.gvTmpl;
            this.gridCtrlNetType.Name = "gridCtrlNetType";
            this.gridCtrlNetType.ShowOnlyPredefinedDetails = true;
            this.gridCtrlNetType.Size = new System.Drawing.Size(201, 93);
            this.gridCtrlNetType.TabIndex = 1;
            this.gridCtrlNetType.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvTmpl});
            // 
            // gvTmpl
            // 
            this.gvTmpl.ActiveFilterEnabled = false;
            this.gvTmpl.Appearance.FocusedCell.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvTmpl.Appearance.FocusedCell.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvTmpl.Appearance.FocusedCell.BorderColor = System.Drawing.Color.Black;
            this.gvTmpl.Appearance.FocusedCell.Options.UseBackColor = true;
            this.gvTmpl.Appearance.FocusedCell.Options.UseBorderColor = true;
            this.gvTmpl.Appearance.FocusedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvTmpl.Appearance.FocusedRow.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.gvTmpl.Appearance.FocusedRow.BorderColor = System.Drawing.Color.Black;
            this.gvTmpl.Appearance.FocusedRow.Options.UseBackColor = true;
            this.gvTmpl.Appearance.FocusedRow.Options.UseBorderColor = true;
            this.gvTmpl.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1});
            this.gvTmpl.GridControl = this.gridCtrlNetType;
            this.gvTmpl.Name = "gvTmpl";
            this.gvTmpl.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gvTmpl.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gvTmpl.OptionsDetail.ShowDetailTabs = false;
            this.gvTmpl.OptionsFilter.AllowColumnMRUFilterList = false;
            this.gvTmpl.OptionsFilter.AllowFilterEditor = false;
            this.gvTmpl.OptionsFilter.AllowMRUFilterList = false;
            this.gvTmpl.OptionsView.EnableAppearanceEvenRow = true;
            this.gvTmpl.OptionsView.ShowDetailButtons = false;
            this.gvTmpl.OptionsView.ShowGroupPanel = false;
            this.gvTmpl.OptionsView.ShowIndicator = false;
            this.gvTmpl.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.gvTmpl_FocusedRowChanged);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "名称";
            this.gridColumn1.FieldName = "NetType";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.OptionsColumn.AllowEdit = false;
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // groupControl2
            // 
            this.groupControl2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupControl2.Controls.Add(this.btn_Down);
            this.groupControl2.Controls.Add(this.btn_Up);
            this.groupControl2.Controls.Add(this.listBox_subTemplate);
            this.groupControl2.Location = new System.Drawing.Point(1, 0);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(269, 591);
            this.groupControl2.TabIndex = 15;
            this.groupControl2.Text = "子模板列表";
            // 
            // listBox_subTemplate
            // 
            this.listBox_subTemplate.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.listBox_subTemplate.FormattingEnabled = true;
            this.listBox_subTemplate.ItemHeight = 14;
            this.listBox_subTemplate.Location = new System.Drawing.Point(5, 31);
            this.listBox_subTemplate.Name = "listBox_subTemplate";
            this.listBox_subTemplate.Size = new System.Drawing.Size(259, 522);
            this.listBox_subTemplate.TabIndex = 14;
            this.listBox_subTemplate.SelectedIndexChanged += new System.EventHandler(this.listBox_subTemplate_SelectedIndexChanged);
            // 
            // groupControlColInfo
            // 
            this.groupControlColInfo.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupControlColInfo.Controls.Add(this.chkBox_selectAll);
            this.groupControlColInfo.Controls.Add(this.label1);
            this.groupControlColInfo.Controls.Add(this.txtTemplateName);
            this.groupControlColInfo.Controls.Add(this.btn_RemoveTemplate);
            this.groupControlColInfo.Controls.Add(this.chkList_columnItems);
            this.groupControlColInfo.Controls.Add(this.btn_Save);
            this.groupControlColInfo.Controls.Add(this.btn_NewTemplate);
            this.groupControlColInfo.Location = new System.Drawing.Point(276, 0);
            this.groupControlColInfo.Name = "groupControlColInfo";
            this.groupControlColInfo.Size = new System.Drawing.Size(391, 591);
            this.groupControlColInfo.TabIndex = 0;
            this.groupControlColInfo.Text = "模版内容";
            // 
            // chkBox_selectAll
            // 
            this.chkBox_selectAll.AutoSize = true;
            this.chkBox_selectAll.Location = new System.Drawing.Point(8, 559);
            this.chkBox_selectAll.Name = "chkBox_selectAll";
            this.chkBox_selectAll.Size = new System.Drawing.Size(50, 18);
            this.chkBox_selectAll.TabIndex = 16;
            this.chkBox_selectAll.Text = "全选";
            this.chkBox_selectAll.UseVisualStyleBackColor = true;
            this.chkBox_selectAll.CheckedChanged += new System.EventHandler(this.chkBox_selectAll_CheckedChanged);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(5, 29);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(91, 14);
            this.label1.TabIndex = 15;
            this.label1.Text = "当前模版名称：";
            // 
            // txtTemplateName
            // 
            this.txtTemplateName.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtTemplateName.Location = new System.Drawing.Point(102, 26);
            this.txtTemplateName.Name = "txtTemplateName";
            this.txtTemplateName.Size = new System.Drawing.Size(284, 21);
            this.txtTemplateName.TabIndex = 14;
            // 
            // btn_RemoveTemplate
            // 
            this.btn_RemoveTemplate.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_RemoveTemplate.Location = new System.Drawing.Point(198, 559);
            this.btn_RemoveTemplate.Name = "btn_RemoveTemplate";
            this.btn_RemoveTemplate.Size = new System.Drawing.Size(90, 27);
            this.btn_RemoveTemplate.TabIndex = 3;
            this.btn_RemoveTemplate.Text = "删除";
            this.btn_RemoveTemplate.Click += new System.EventHandler(this.btnRemoveTemplate_Click);
            // 
            // chkList_columnItems
            // 
            this.chkList_columnItems.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.chkList_columnItems.CheckOnClick = true;
            this.chkList_columnItems.Location = new System.Drawing.Point(7, 59);
            this.chkList_columnItems.Name = "chkList_columnItems";
            this.chkList_columnItems.Size = new System.Drawing.Size(379, 494);
            this.chkList_columnItems.TabIndex = 13;
            // 
            // btn_Save
            // 
            this.btn_Save.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_Save.Location = new System.Drawing.Point(294, 559);
            this.btn_Save.Name = "btn_Save";
            this.btn_Save.Size = new System.Drawing.Size(90, 27);
            this.btn_Save.TabIndex = 0;
            this.btn_Save.Text = "保存";
            this.btn_Save.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // btn_NewTemplate
            // 
            this.btn_NewTemplate.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_NewTemplate.Location = new System.Drawing.Point(102, 559);
            this.btn_NewTemplate.Name = "btn_NewTemplate";
            this.btn_NewTemplate.Size = new System.Drawing.Size(90, 27);
            this.btn_NewTemplate.TabIndex = 2;
            this.btn_NewTemplate.Text = "新建";
            this.btn_NewTemplate.Click += new System.EventHandler(this.btnNewReport_Click);
            // 
            // btn_Up
            // 
            this.btn_Up.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_Up.Location = new System.Drawing.Point(5, 559);
            this.btn_Up.Name = "btn_Up";
            this.btn_Up.Size = new System.Drawing.Size(90, 27);
            this.btn_Up.TabIndex = 15;
            this.btn_Up.Text = "上移";
            this.btn_Up.Click += new System.EventHandler(this.btn_Up_Click);
            // 
            // btn_Down
            // 
            this.btn_Down.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_Down.Location = new System.Drawing.Point(174, 559);
            this.btn_Down.Name = "btn_Down";
            this.btn_Down.Size = new System.Drawing.Size(90, 27);
            this.btn_Down.TabIndex = 16;
            this.btn_Down.Text = "下移";
            this.btn_Down.Click += new System.EventHandler(this.btn_Down_Click);
            // 
            // KPITemplateForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(884, 595);
            this.Controls.Add(this.splitContainerControl1);
            this.MinimumSize = new System.Drawing.Size(900, 633);
            this.Name = "KPITemplateForm";
            this.Text = "指标模板设置";
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControlTemplate)).EndInit();
            this.groupControlTemplate.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlNetType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvTmpl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControlColInfo)).EndInit();
            this.groupControlColInfo.ResumeLayout(false);
            this.groupControlColInfo.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtTemplateName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkList_columnItems)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.GroupControl groupControlTemplate;
        private DevExpress.XtraEditors.SimpleButton btn_Save;
        private DevExpress.XtraEditors.SimpleButton btn_NewTemplate;
        private DevExpress.XtraEditors.GroupControl groupControlColInfo;
        private DevExpress.XtraEditors.SimpleButton btn_RemoveTemplate;
        private DevExpress.XtraGrid.GridControl gridCtrlNetType;
        private DevExpress.XtraGrid.Views.Grid.GridView gvTmpl;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraEditors.CheckedListBoxControl chkList_columnItems;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.TextEdit txtTemplateName;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.CheckBox chkBox_selectAll;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private System.Windows.Forms.ListBox listBox_subTemplate;
        private System.Windows.Forms.ListBox listBox_template;
        private DevExpress.XtraEditors.SimpleButton btn_Down;
        private DevExpress.XtraEditors.SimpleButton btn_Up;
    }
}