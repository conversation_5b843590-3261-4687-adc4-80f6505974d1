﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTScanInterfereForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.contextMenuStripListView = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem3 = new System.Windows.Forms.ToolStripSeparator();
            this.ToolStripMenuItemPushOutGridInfo = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemPushOut = new System.Windows.Forms.ToolStripMenuItem();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.listViewTotal = new BrightIdeasSoftware.TreeListView();
            this.olvColumnIndex = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCell1 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLAC1 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCI1 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBCCH1 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnTCH1 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSampleCount1 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRxlev1 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCell2 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLAC2 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCI2 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBCCH2 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnTCH2 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSampleCount2 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRxLev2 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnAngle = new BrightIdeasSoftware.OLVColumn();
            this.lblConditionVisible = new System.Windows.Forms.LinkLabel();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.cbxDoor = new System.Windows.Forms.ComboBox();
            this.label8 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.comboBoxAngleMax = new System.Windows.Forms.ComboBox();
            this.comboBoxAngleMin = new System.Windows.Forms.ComboBox();
            this.label5 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.cbxCoAdj = new System.Windows.Forms.ComboBox();
            this.label3 = new System.Windows.Forms.Label();
            this.cbxInterferenceType = new System.Windows.Forms.ComboBox();
            this.numBCCHDValue = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.numSampleCount = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.numRxLevDValue = new System.Windows.Forms.NumericUpDown();
            this.label11 = new System.Windows.Forms.Label();
            this.lblConditionDisappear = new System.Windows.Forms.LinkLabel();
            this.btnOK = new System.Windows.Forms.Button();
            this.pnlCondition = new System.Windows.Forms.Panel();
            this.contextMenuStripListView.SuspendLayout();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numBCCHDValue)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue)).BeginInit();
            this.pnlCondition.SuspendLayout();
            this.SuspendLayout();
            // 
            // contextMenuStripListView
            // 
            this.contextMenuStripListView.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll,
            this.miCollapseAll,
            this.toolStripMenuItem3,
            this.ToolStripMenuItemPushOutGridInfo,
            this.ToolStripMenuItemPushOut});
            this.contextMenuStripListView.Name = "contextMenuStripListView";
            this.contextMenuStripListView.Size = new System.Drawing.Size(149, 98);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(148, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCollapseAll
            // 
            this.miCollapseAll.Name = "miCollapseAll";
            this.miCollapseAll.Size = new System.Drawing.Size(148, 22);
            this.miCollapseAll.Text = "全部合并";
            this.miCollapseAll.Click += new System.EventHandler(this.miCollapseAll_Click);
            // 
            // toolStripMenuItem3
            // 
            this.toolStripMenuItem3.Name = "toolStripMenuItem3";
            this.toolStripMenuItem3.Size = new System.Drawing.Size(145, 6);
            // 
            // ToolStripMenuItemPushOutGridInfo
            // 
            this.ToolStripMenuItemPushOutGridInfo.Name = "ToolStripMenuItemPushOutGridInfo";
            this.ToolStripMenuItemPushOutGridInfo.Size = new System.Drawing.Size(148, 22);
            this.ToolStripMenuItemPushOutGridInfo.Text = "栅格信息列表";
            this.ToolStripMenuItemPushOutGridInfo.Visible = false;
            this.ToolStripMenuItemPushOutGridInfo.Click += new System.EventHandler(this.ToolStripMenuItemPushOutGridInfo_Click);
            // 
            // ToolStripMenuItemPushOut
            // 
            this.ToolStripMenuItemPushOut.Name = "ToolStripMenuItemPushOut";
            this.ToolStripMenuItemPushOut.Size = new System.Drawing.Size(148, 22);
            this.ToolStripMenuItemPushOut.Text = "导出Excel";
            this.ToolStripMenuItemPushOut.Click += new System.EventHandler(this.ToolStripMenuItemPushOut_Click);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.listViewTotal);
            this.groupBox2.Controls.Add(this.lblConditionVisible);
            this.groupBox2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox2.Location = new System.Drawing.Point(0, 0);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(865, 440);
            this.groupBox2.TabIndex = 13;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "筛选条件";
            // 
            // listViewTotal
            // 
            this.listViewTotal.AllColumns.Add(this.olvColumnIndex);
            this.listViewTotal.AllColumns.Add(this.olvColumnCell1);
            this.listViewTotal.AllColumns.Add(this.olvColumnLAC1);
            this.listViewTotal.AllColumns.Add(this.olvColumnCI1);
            this.listViewTotal.AllColumns.Add(this.olvColumnBCCH1);
            this.listViewTotal.AllColumns.Add(this.olvColumnTCH1);
            this.listViewTotal.AllColumns.Add(this.olvColumnSampleCount1);
            this.listViewTotal.AllColumns.Add(this.olvColumnRxlev1);
            this.listViewTotal.AllColumns.Add(this.olvColumnCell2);
            this.listViewTotal.AllColumns.Add(this.olvColumnLAC2);
            this.listViewTotal.AllColumns.Add(this.olvColumnCI2);
            this.listViewTotal.AllColumns.Add(this.olvColumnBCCH2);
            this.listViewTotal.AllColumns.Add(this.olvColumnTCH2);
            this.listViewTotal.AllColumns.Add(this.olvColumnSampleCount2);
            this.listViewTotal.AllColumns.Add(this.olvColumnRxLev2);
            this.listViewTotal.AllColumns.Add(this.olvColumnAngle);
            this.listViewTotal.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnIndex,
            this.olvColumnCell1,
            this.olvColumnLAC1,
            this.olvColumnCI1,
            this.olvColumnBCCH1,
            this.olvColumnTCH1,
            this.olvColumnSampleCount1,
            this.olvColumnRxlev1,
            this.olvColumnCell2,
            this.olvColumnLAC2,
            this.olvColumnCI2,
            this.olvColumnBCCH2,
            this.olvColumnTCH2,
            this.olvColumnSampleCount2,
            this.olvColumnRxLev2,
            this.olvColumnAngle});
            this.listViewTotal.ContextMenuStrip = this.contextMenuStripListView;
            this.listViewTotal.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewTotal.FullRowSelect = true;
            this.listViewTotal.GridLines = true;
            this.listViewTotal.HeaderWordWrap = true;
            this.listViewTotal.Location = new System.Drawing.Point(3, 17);
            this.listViewTotal.Name = "listViewTotal";
            this.listViewTotal.OwnerDraw = true;
            this.listViewTotal.ShowGroups = false;
            this.listViewTotal.Size = new System.Drawing.Size(859, 420);
            this.listViewTotal.TabIndex = 16;
            this.listViewTotal.UseCompatibleStateImageBehavior = false;
            this.listViewTotal.View = System.Windows.Forms.View.Details;
            this.listViewTotal.VirtualMode = true;
            this.listViewTotal.DoubleClick += new System.EventHandler(this.listViewTotal_DoubleClick);
            // 
            // olvColumnIndex
            // 
            this.olvColumnIndex.HeaderFont = null;
            this.olvColumnIndex.Text = "序号";
            // 
            // olvColumnCell1
            // 
            this.olvColumnCell1.HeaderFont = null;
            this.olvColumnCell1.Text = "小区一";
            // 
            // olvColumnLAC1
            // 
            this.olvColumnLAC1.HeaderFont = null;
            this.olvColumnLAC1.Text = "小区一LAC";
            // 
            // olvColumnCI1
            // 
            this.olvColumnCI1.HeaderFont = null;
            this.olvColumnCI1.Text = "小区一CI";
            // 
            // olvColumnBCCH1
            // 
            this.olvColumnBCCH1.HeaderFont = null;
            this.olvColumnBCCH1.Text = "小区一BCCH";
            // 
            // olvColumnTCH1
            // 
            this.olvColumnTCH1.HeaderFont = null;
            this.olvColumnTCH1.Text = "小区一TCH";
            // 
            // olvColumnSampleCount1
            // 
            this.olvColumnSampleCount1.HeaderFont = null;
            this.olvColumnSampleCount1.Text = "小区一采样点数";
            // 
            // olvColumnRxlev1
            // 
            this.olvColumnRxlev1.HeaderFont = null;
            this.olvColumnRxlev1.Text = "小区一RxLev";
            // 
            // olvColumnCell2
            // 
            this.olvColumnCell2.HeaderFont = null;
            this.olvColumnCell2.Text = "小区二";
            // 
            // olvColumnLAC2
            // 
            this.olvColumnLAC2.HeaderFont = null;
            this.olvColumnLAC2.Text = "小区二LAC";
            // 
            // olvColumnCI2
            // 
            this.olvColumnCI2.HeaderFont = null;
            this.olvColumnCI2.Text = "小区二CI";
            // 
            // olvColumnBCCH2
            // 
            this.olvColumnBCCH2.HeaderFont = null;
            this.olvColumnBCCH2.Text = "小区二BCCH";
            // 
            // olvColumnTCH2
            // 
            this.olvColumnTCH2.HeaderFont = null;
            this.olvColumnTCH2.Text = "小区二TCH";
            // 
            // olvColumnSampleCount2
            // 
            this.olvColumnSampleCount2.HeaderFont = null;
            this.olvColumnSampleCount2.Text = "小区二采样点数";
            // 
            // olvColumnRxLev2
            // 
            this.olvColumnRxLev2.HeaderFont = null;
            this.olvColumnRxLev2.Text = "小区二RxLev";
            // 
            // olvColumnAngle
            // 
            this.olvColumnAngle.HeaderFont = null;
            this.olvColumnAngle.Text = "夹角";
            // 
            // lblConditionVisible
            // 
            this.lblConditionVisible.AutoSize = true;
            this.lblConditionVisible.Location = new System.Drawing.Point(6, 0);
            this.lblConditionVisible.Name = "lblConditionVisible";
            this.lblConditionVisible.Size = new System.Drawing.Size(53, 12);
            this.lblConditionVisible.TabIndex = 14;
            this.lblConditionVisible.TabStop = true;
            this.lblConditionVisible.Text = "筛选条件";
            this.lblConditionVisible.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.lblConditionVisible_LinkClicked);
            // 
            // groupBox1
            // 
            this.groupBox1.BackColor = System.Drawing.SystemColors.Window;
            this.groupBox1.Controls.Add(this.cbxDoor);
            this.groupBox1.Controls.Add(this.label8);
            this.groupBox1.Controls.Add(this.label7);
            this.groupBox1.Controls.Add(this.label6);
            this.groupBox1.Controls.Add(this.comboBoxAngleMax);
            this.groupBox1.Controls.Add(this.comboBoxAngleMin);
            this.groupBox1.Controls.Add(this.label5);
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Controls.Add(this.cbxCoAdj);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.cbxInterferenceType);
            this.groupBox1.Controls.Add(this.numBCCHDValue);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.numSampleCount);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.numRxLevDValue);
            this.groupBox1.Controls.Add(this.label11);
            this.groupBox1.Controls.Add(this.lblConditionDisappear);
            this.groupBox1.Controls.Add(this.btnOK);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(865, 78);
            this.groupBox1.TabIndex = 11;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "筛选条件";
            // 
            // cbxDoor
            // 
            this.cbxDoor.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxDoor.FormattingEnabled = true;
            this.cbxDoor.Items.AddRange(new object[] {
            "全部",
            "室外",
            "室内"});
            this.cbxDoor.Location = new System.Drawing.Point(658, 46);
            this.cbxDoor.Name = "cbxDoor";
            this.cbxDoor.Size = new System.Drawing.Size(78, 20);
            this.cbxDoor.TabIndex = 45;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(599, 50);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(53, 12);
            this.label8.TabIndex = 44;
            this.label8.Text = "室内外：";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(515, 20);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(209, 12);
            this.label7.TabIndex = 43;
            this.label7.Text = "(此排条件修改，重新生成结果较慢！)";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(492, 50);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(17, 12);
            this.label6.TabIndex = 42;
            this.label6.Text = "至";
            // 
            // comboBoxAngleMax
            // 
            this.comboBoxAngleMax.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxAngleMax.FormattingEnabled = true;
            this.comboBoxAngleMax.Location = new System.Drawing.Point(517, 46);
            this.comboBoxAngleMax.Name = "comboBoxAngleMax";
            this.comboBoxAngleMax.Size = new System.Drawing.Size(64, 20);
            this.comboBoxAngleMax.TabIndex = 41;
            // 
            // comboBoxAngleMin
            // 
            this.comboBoxAngleMin.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxAngleMin.FormattingEnabled = true;
            this.comboBoxAngleMin.Location = new System.Drawing.Point(419, 46);
            this.comboBoxAngleMin.Name = "comboBoxAngleMin";
            this.comboBoxAngleMin.Size = new System.Drawing.Size(64, 20);
            this.comboBoxAngleMin.TabIndex = 40;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(324, 50);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(89, 12);
            this.label5.TabIndex = 39;
            this.label5.Text = "天线夹角范围：";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(33, 20);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(77, 12);
            this.label4.TabIndex = 38;
            this.label4.Text = "同邻频选择：";
            // 
            // cbxCoAdj
            // 
            this.cbxCoAdj.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxCoAdj.FormattingEnabled = true;
            this.cbxCoAdj.Items.AddRange(new object[] {
            "同频",
            "邻频"});
            this.cbxCoAdj.Location = new System.Drawing.Point(116, 16);
            this.cbxCoAdj.Name = "cbxCoAdj";
            this.cbxCoAdj.Size = new System.Drawing.Size(67, 20);
            this.cbxCoAdj.TabIndex = 37;
            this.cbxCoAdj.SelectedIndexChanged += new System.EventHandler(this.cbxCoAdj_SelectedIndexChanged);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(348, 20);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(65, 12);
            this.label3.TabIndex = 36;
            this.label3.Text = "干扰类型：";
            // 
            // cbxInterferenceType
            // 
            this.cbxInterferenceType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxInterferenceType.FormattingEnabled = true;
            this.cbxInterferenceType.Items.AddRange(new object[] {
            "BCCH&TCH",
            "BCCH Only",
            "TCH Only"});
            this.cbxInterferenceType.Location = new System.Drawing.Point(419, 16);
            this.cbxInterferenceType.Name = "cbxInterferenceType";
            this.cbxInterferenceType.Size = new System.Drawing.Size(81, 20);
            this.cbxInterferenceType.TabIndex = 35;
            // 
            // numBCCHDValue
            // 
            this.numBCCHDValue.Location = new System.Drawing.Point(268, 16);
            this.numBCCHDValue.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numBCCHDValue.Name = "numBCCHDValue";
            this.numBCCHDValue.Size = new System.Drawing.Size(50, 21);
            this.numBCCHDValue.TabIndex = 34;
            this.numBCCHDValue.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(197, 20);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 33;
            this.label2.Text = "BCCH差值≤";
            // 
            // numSampleCount
            // 
            this.numSampleCount.Location = new System.Drawing.Point(268, 46);
            this.numSampleCount.Name = "numSampleCount";
            this.numSampleCount.Size = new System.Drawing.Size(50, 21);
            this.numSampleCount.TabIndex = 27;
            this.numSampleCount.Value = new decimal(new int[] {
            2,
            0,
            0,
            0});
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(197, 50);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 26;
            this.label1.Text = "采样点数≥";
            // 
            // numRxLevDValue
            // 
            this.numRxLevDValue.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numRxLevDValue.Location = new System.Drawing.Point(116, 46);
            this.numRxLevDValue.Name = "numRxLevDValue";
            this.numRxLevDValue.Size = new System.Drawing.Size(67, 21);
            this.numRxLevDValue.TabIndex = 25;
            this.numRxLevDValue.Value = new decimal(new int[] {
            9,
            0,
            0,
            0});
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(9, 50);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(101, 12);
            this.label11.TabIndex = 24;
            this.label11.Text = "两小区电平差值≤";
            // 
            // lblConditionDisappear
            // 
            this.lblConditionDisappear.AutoSize = true;
            this.lblConditionDisappear.Location = new System.Drawing.Point(6, 0);
            this.lblConditionDisappear.Name = "lblConditionDisappear";
            this.lblConditionDisappear.Size = new System.Drawing.Size(53, 12);
            this.lblConditionDisappear.TabIndex = 23;
            this.lblConditionDisappear.TabStop = true;
            this.lblConditionDisappear.Text = "筛选条件";
            this.lblConditionDisappear.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.lblConditionDisappear_LinkClicked);
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(778, 45);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(81, 23);
            this.btnOK.TabIndex = 8;
            this.btnOK.Text = "立即筛选";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // pnlCondition
            // 
            this.pnlCondition.BackColor = System.Drawing.SystemColors.Control;
            this.pnlCondition.Controls.Add(this.groupBox1);
            this.pnlCondition.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlCondition.Location = new System.Drawing.Point(0, 0);
            this.pnlCondition.Name = "pnlCondition";
            this.pnlCondition.Size = new System.Drawing.Size(865, 78);
            this.pnlCondition.TabIndex = 14;
            this.pnlCondition.Visible = false;
            // 
            // ZTScanInterfereForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.SystemColors.Control;
            this.ClientSize = new System.Drawing.Size(865, 440);
            this.Controls.Add(this.pnlCondition);
            this.Controls.Add(this.groupBox2);
            this.MinimumSize = new System.Drawing.Size(787, 348);
            this.Name = "ZTScanInterfereForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "扫频同邻频干扰";
            this.contextMenuStripListView.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numBCCHDValue)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue)).EndInit();
            this.pnlCondition.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contextMenuStripListView;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemPushOut;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemPushOutGridInfo;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.LinkLabel lblConditionVisible;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem3;
        private BrightIdeasSoftware.TreeListView listViewTotal;
        private BrightIdeasSoftware.OLVColumn olvColumnIndex;
        private BrightIdeasSoftware.OLVColumn olvColumnCell1;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC1;
        private BrightIdeasSoftware.OLVColumn olvColumnCI1;
        private BrightIdeasSoftware.OLVColumn olvColumnBCCH1;
        private BrightIdeasSoftware.OLVColumn olvColumnTCH1;
        private BrightIdeasSoftware.OLVColumn olvColumnSampleCount1;
        private BrightIdeasSoftware.OLVColumn olvColumnRxlev1;
        private BrightIdeasSoftware.OLVColumn olvColumnCell2;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC2;
        private BrightIdeasSoftware.OLVColumn olvColumnCI2;
        private BrightIdeasSoftware.OLVColumn olvColumnBCCH2;
        private BrightIdeasSoftware.OLVColumn olvColumnTCH2;
        private BrightIdeasSoftware.OLVColumn olvColumnSampleCount2;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev2;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.NumericUpDown numBCCHDValue;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.NumericUpDown numSampleCount;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.NumericUpDown numRxLevDValue;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.LinkLabel lblConditionDisappear;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Panel pnlCondition;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.ComboBox cbxInterferenceType;
        private System.Windows.Forms.ComboBox cbxCoAdj;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.ComboBox comboBoxAngleMax;
        private System.Windows.Forms.ComboBox comboBoxAngleMin;
        private System.Windows.Forms.Label label5;
        private BrightIdeasSoftware.OLVColumn olvColumnAngle;
        private System.Windows.Forms.ComboBox cbxDoor;
        private System.Windows.Forms.Label label8;
    }
}