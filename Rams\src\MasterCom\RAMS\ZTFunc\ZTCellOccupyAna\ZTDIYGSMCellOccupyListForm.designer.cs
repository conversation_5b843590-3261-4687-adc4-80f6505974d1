﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTDIYGSMCellOccupyListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTDIYGSMCellOccupyListForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ListViewCellOccupy = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLastLac = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLastCi = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colNextLac = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colNextCi = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBeginTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnEndTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCellOccupy)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel,
            this.toolStripMenuItem1,
            this.miExpandAll,
            this.miCallapsAll});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(153, 98);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(152, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(149, 6);
            this.toolStripMenuItem1.Visible = false;
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(152, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Visible = false;
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCallapsAll
            // 
            this.miCallapsAll.Name = "miCallapsAll";
            this.miCallapsAll.Size = new System.Drawing.Size(152, 22);
            this.miCallapsAll.Text = "全部合并";
            this.miCallapsAll.Visible = false;
            this.miCallapsAll.Click += new System.EventHandler(this.miCallapsAll_Click);
            // 
            // ListViewCellOccupy
            // 
            this.ListViewCellOccupy.AllColumns.Add(this.olvColumnSN);
            this.ListViewCellOccupy.AllColumns.Add(this.colLastLac);
            this.ListViewCellOccupy.AllColumns.Add(this.colLastCi);
            this.ListViewCellOccupy.AllColumns.Add(this.olvColumnCellName);
            this.ListViewCellOccupy.AllColumns.Add(this.olvColumnLAC);
            this.ListViewCellOccupy.AllColumns.Add(this.olvColumnCI);
            this.ListViewCellOccupy.AllColumns.Add(this.colNextLac);
            this.ListViewCellOccupy.AllColumns.Add(this.colNextCi);
            this.ListViewCellOccupy.AllColumns.Add(this.olvColumnFileName);
            this.ListViewCellOccupy.AllColumns.Add(this.olvColumnBeginTime);
            this.ListViewCellOccupy.AllColumns.Add(this.olvColumnEndTime);
            this.ListViewCellOccupy.AllColumns.Add(this.olvColumnTime);
            this.ListViewCellOccupy.AllColumns.Add(this.olvColumnDistance);
            this.ListViewCellOccupy.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.colLastLac,
            this.colLastCi,
            this.olvColumnCellName,
            this.olvColumnLAC,
            this.olvColumnCI,
            this.colNextLac,
            this.colNextCi,
            this.olvColumnFileName,
            this.olvColumnBeginTime,
            this.olvColumnEndTime,
            this.olvColumnTime,
            this.olvColumnDistance});
            this.ListViewCellOccupy.ContextMenuStrip = this.ctxMenu;
            this.ListViewCellOccupy.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewCellOccupy.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ListViewCellOccupy.FullRowSelect = true;
            this.ListViewCellOccupy.GridLines = true;
            this.ListViewCellOccupy.HeaderWordWrap = true;
            this.ListViewCellOccupy.IsNeedShowOverlay = false;
            this.ListViewCellOccupy.Location = new System.Drawing.Point(0, 0);
            this.ListViewCellOccupy.Name = "ListViewCellOccupy";
            this.ListViewCellOccupy.OwnerDraw = true;
            this.ListViewCellOccupy.ShowGroups = false;
            this.ListViewCellOccupy.Size = new System.Drawing.Size(1026, 502);
            this.ListViewCellOccupy.TabIndex = 5;
            this.ListViewCellOccupy.UseCompatibleStateImageBehavior = false;
            this.ListViewCellOccupy.View = System.Windows.Forms.View.Details;
            this.ListViewCellOccupy.VirtualMode = true;
            this.ListViewCellOccupy.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewTotal_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 40;
            // 
            // colLastLac
            // 
            this.colLastLac.DisplayIndex = 9;
            this.colLastLac.HeaderFont = null;
            this.colLastLac.Text = "切入前LAC";
            // 
            // colLastCi
            // 
            this.colLastCi.DisplayIndex = 10;
            this.colLastCi.HeaderFont = null;
            this.colLastCi.Text = "切入前CI";
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.DisplayIndex = 1;
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "当前小区名称";
            this.olvColumnCellName.Width = 100;
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.DisplayIndex = 2;
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "当前小区LAC";
            this.olvColumnLAC.Width = 80;
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.DisplayIndex = 3;
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "当前小区CI";
            this.olvColumnCI.Width = 70;
            // 
            // colNextLac
            // 
            this.colNextLac.DisplayIndex = 11;
            this.colNextLac.HeaderFont = null;
            this.colNextLac.Text = "切出目标LAC";
            // 
            // colNextCi
            // 
            this.colNextCi.DisplayIndex = 12;
            this.colNextCi.HeaderFont = null;
            this.colNextCi.Text = "切出目标CI";
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.DisplayIndex = 4;
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名称";
            this.olvColumnFileName.Width = 200;
            // 
            // olvColumnBeginTime
            // 
            this.olvColumnBeginTime.DisplayIndex = 5;
            this.olvColumnBeginTime.HeaderFont = null;
            this.olvColumnBeginTime.Text = "开始时间";
            this.olvColumnBeginTime.Width = 150;
            // 
            // olvColumnEndTime
            // 
            this.olvColumnEndTime.DisplayIndex = 6;
            this.olvColumnEndTime.HeaderFont = null;
            this.olvColumnEndTime.Text = "结束时间";
            this.olvColumnEndTime.Width = 150;
            // 
            // olvColumnTime
            // 
            this.olvColumnTime.DisplayIndex = 7;
            this.olvColumnTime.HeaderFont = null;
            this.olvColumnTime.Text = "持续时长（秒）";
            this.olvColumnTime.Width = 100;
            // 
            // olvColumnDistance
            // 
            this.olvColumnDistance.DisplayIndex = 8;
            this.olvColumnDistance.HeaderFont = null;
            this.olvColumnDistance.Text = "覆盖距离（米）";
            this.olvColumnDistance.Width = 100;
            // 
            // ZTDIYGSMCellOccupyListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1026, 502);
            this.Controls.Add(this.ListViewCellOccupy);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTDIYGSMCellOccupyListForm";
            this.Text = "小区占用";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCellOccupy)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCallapsAll;
        private BrightIdeasSoftware.TreeListView ListViewCellOccupy;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnBeginTime;
        private BrightIdeasSoftware.OLVColumn olvColumnEndTime;
        private BrightIdeasSoftware.OLVColumn olvColumnTime;
        private BrightIdeasSoftware.OLVColumn olvColumnDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn colLastLac;
        private BrightIdeasSoftware.OLVColumn colLastCi;
        private BrightIdeasSoftware.OLVColumn colNextLac;
        private BrightIdeasSoftware.OLVColumn colNextCi;

    }
}