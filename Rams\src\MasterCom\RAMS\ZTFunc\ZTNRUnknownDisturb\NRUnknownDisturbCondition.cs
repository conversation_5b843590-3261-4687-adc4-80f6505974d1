﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRUnknownDisturbCondition
    {
        public double MinStaySecond
        {
            get;
            set;
        }
        public bool CheckTime
        {
            get;
            set;
        }
        public double MinStayDistance
        {
            get;
            set;
        }

        public float MaxSinr
        {
            get;
            set;
        }
        public float MinDiffer
        {
            get;
            set;
        }

        public bool IsMatchIndicator(float? rsrp, float? sinr, float? nMaxRsrp)
        {
            return rsrp <= 25 && sinr >= -50 && sinr < MaxSinr && rsrp - nMaxRsrp >= MinDiffer;
        }

        public bool CheckStayTime(double second)
        {
            if (CheckTime)
            {
                return second >= MinStaySecond;
            }
            return true;
        }

        public bool checkStayDistance(double dis)
        {
            return dis >= MinStayDistance;
        }
    }
}
