﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util;

namespace MasterCom.MTGis
{
    public partial class ImportFileForMapDlg : Form
    {
        public ImportFileForMapDlg()
        {
            InitializeComponent();
        }
        public DataTable GetGridDataTable()
        {
            return gridDataTable;
        }
        public string LngName()
        {
            return lngName;
        }
        public string LatName()
        {
            return latName;
        }
        public String GetLayerName()
        {
            return tbxLayerName.Text.Trim();
        }
        private DataTable gridDataTable = null;
        private string lngName = "经度";
        private string latName = "纬度";
        private void btnOpen_Click(object sender, EventArgs e)
        {
            gridDataTable = null;
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = MasterCom.Util.FilterHelper.Xlsx + "|" + MasterCom.Util.FilterHelper.Xls + "|" + MasterCom.Util.FilterHelper.Csv;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            txtFile.Text = dlg.FileName;
            DataTable dt = null;
            String errorText = "";
            dealFile(ref dt, ref errorText);

            if (tbxLayerName.Text.Trim() == "")
            {
                tbxLayerName.Text = System.IO.Path.GetFileNameWithoutExtension(dlg.FileName);
            }
            if (dt == null)
            {
                lbDataInfo.Text = errorText;
            }
            else
            {
                lbDataInfo.Text = "共" + dt.Rows.Count + "条记录。" + (dt.Rows.Count > 10000 ? "(预览10000)" : "");
            }
            DataTable dtForPreview = null;
            if (dt != null)
            {
                if (dt.Rows.Count > 10000)
                {
                    dtForPreview = getPreview(dt, 10000);
                }
                else
                {
                    dtForPreview = dt;
                }
            }
            this.dataGridAll.DataSource = dtForPreview;
            gridDataTable = dt;
        }

        private void dealFile(ref DataTable dt, ref string errorText)
        {
            if (txtFile.Text.ToUpper().EndsWith(".CSV"))
            {
                dealCSVFile(ref dt, ref errorText);
            }
            else
            {
                dealExcelFile(ref dt, ref errorText);
            }
        }

        private void dealCSVFile(ref DataTable dt, ref string errorText)
        {
            CSVReader reader = new CSVReader(txtFile.Text);
            List<string[]> listData = reader.GetAllData();
            if (listData != null && listData.Count > 0)
            {
                string[] cols = listData[0];
                listData.RemoveAt(0);
                int hasLongiAt = -1;
                int hasLatiAt = -1;
                for (int cat = 0; cat < cols.Length; cat++)
                {
                    String col = cols[cat];
                    if (col == "经度")
                    {
                        hasLongiAt = cat;
                    }
                    if (col == "纬度")
                    {
                        hasLatiAt = cat;
                    }
                }

                dt = getDataTable(listData, cols);

                if (hasLongiAt >= 0 && hasLatiAt >= 0)
                {
                    rdoLng.Text = "经度";
                    rdoLat.Text = "纬度";
                }
            }
            else
            {
                errorText = reader.lastError;
            }
        }

        private static DataTable getDataTable(List<string[]> listData, string[] cols)
        {
            DataTable dt;
            DataTable dtnew = new DataTable();
            foreach (String c in cols)
            {
                DataColumn dcol = new DataColumn(c, typeof(String));
                dtnew.Columns.Add(dcol);
            }
            foreach (String[] rowDataVec in listData)
            {
                DataRow drow = dtnew.NewRow();
                for (int colAt = 0; colAt < rowDataVec.Length; colAt++)
                {
                    drow[colAt] = rowDataVec[colAt].ToString().Trim();
                }
                dtnew.Rows.Add(drow);
            }
            dt = dtnew;
            return dt;
        }

        private void dealExcelFile(ref DataTable dt, ref string errorText)
        {
            DataSet ds = ExcelNPOIManager.ImportFromExcel(txtFile.Text);
            if (ds.Tables.Count > 0)
            {
                dt = ds.Tables[0];
                if (dt.Columns.Contains("经度") && dt.Columns.Contains("纬度"))
                {
                    rdoLng.Text = "经度";
                    rdoLat.Text = "纬度";
                }
            }

            if (dt == null)
            {
                errorText = "所选表格中必须要有‘经度’或‘纬度’列。";
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            lngName = rdoLng.Text.Trim();
            latName = rdoLat.Text.Trim();

            if(tbxLayerName.Text.Trim()=="")
            {
                MessageBox.Show("请输入图层名称!");
                return;
            }

            if (lngName == "")
            {
                MessageBox.Show("请选择经度列!");
                return;
            }
            if (latName == "")
            {
                MessageBox.Show("请选择纬度列!");
                return;
            }
            if (lngName == latName)
            {
                MessageBox.Show("请选择不同的经纬度列!");
                return;
            }

            if(gridDataTable!=null )
            {
                this.Cursor = Cursors.WaitCursor;

                gridDataTable = ChangeColumnName(gridDataTable);
                gridDataTable = GetRepeatStastic(gridDataTable);

                if (cbxDelRepeat.Checked)
                {
                    gridDataTable = removeRepeat(gridDataTable);
                }
                this.Cursor = Cursors.Default;

                this.DialogResult = System.Windows.Forms.DialogResult.OK;
            }
        }
        private DataTable ChangeColumnName(DataTable mytable) {

            DataColumn colLng = mytable.Columns[LngName()];
            if (colLng != null)
            {
                colLng.ColumnName = "经度";
            }

            DataColumn colLat = mytable.Columns[LatName()];
            if (colLat != null)
            {
                colLat.ColumnName = "纬度";
            }

            //mytable.Columns[LngName()].ColumnName = "经度";
            //mytable.Columns[LatName()].ColumnName = "纬度";
            return mytable;
        }
        private DataTable getPreview(DataTable dt,int count)
        {
            DataTable dtNew = new DataTable();
            foreach (DataColumn col in dt.Columns)
            {
                dtNew.Columns.Add(new DataColumn(col.ColumnName, col.DataType));
            }
            for (int i = 0; i < count;i++)
            {
                DataRow drow = dt.Rows[i];
                DataRow rowNew = dtNew.NewRow();
                foreach (DataColumn col in dt.Columns)
                {
                    rowNew[col.ColumnName] = drow[col.ColumnName];
                }
                dtNew.Rows.Add(rowNew);
            }
            return dtNew;
        }
        private DataTable removeRepeat(DataTable dt)
        {
            DataTable dtNew = new DataTable();
            foreach(DataColumn col in dt.Columns)
            {
                dtNew.Columns.Add(new DataColumn(col.ColumnName,col.DataType));
            }
            Dictionary<string, bool> dicRepeat = new Dictionary<string, bool>();
            foreach(DataRow drow in dt.Rows)
            {
                String str = drow["经度"].ToString() + "_" + drow["纬度"];
                if(!dicRepeat.ContainsKey(str))
                {
                    dicRepeat[str] = true;
                    DataRow rowNew = dtNew.NewRow();
                    foreach (DataColumn col in dt.Columns)
                    {
                        rowNew[col.ColumnName] = drow[col.ColumnName];
                    }
                    dtNew.Rows.Add(rowNew);
                }
            }
            return dtNew;
        }

        private DataTable GetRepeatStastic(DataTable dt)
        {
            DataTable dtNew = new DataTable();
            foreach (DataColumn col in dt.Columns)
            {
                dtNew.Columns.Add(new DataColumn(col.ColumnName, col.DataType));
            }
            dtNew.Columns.Add(new DataColumn("重复数量", Type.GetType("System.Int32")));
            
            Dictionary<string, int> dicRepeat = new Dictionary<string, int>();
            foreach (DataRow drow in dt.Rows)
            {
                String str = drow["经度"].ToString() + "_" + drow["纬度"];
                if (dicRepeat.ContainsKey(str))
                {
                    int repeatCount = dicRepeat[str];
                    repeatCount = repeatCount + 1;
                    dicRepeat[str] = repeatCount;
                }
                else
                {
                    dicRepeat[str] = 1;
                }
            }

            foreach (DataRow drow in dt.Rows)
            {
                String str = drow["经度"].ToString() + "_" + drow["纬度"];

                DataRow rowNew = getRowInfo(dt, dtNew, dicRepeat, drow, str);

                dtNew.Rows.Add(rowNew);

            }
            return dtNew;
        }

        private static DataRow getRowInfo(DataTable dt, DataTable dtNew, Dictionary<string, int> dicRepeat, DataRow drow, string str)
        {
            DataRow rowNew = dtNew.NewRow();
            foreach (DataColumn col in dt.Columns)
            {
                if (col.ColumnName == "经度" || col.ColumnName == "纬度")
                {
                    double myValue = double.Parse(drow[col.ColumnName].ToString().Trim());
                    if (Math.Abs(myValue) > 10000000)
                    {
                        rowNew[col.ColumnName] = myValue / 10000000;
                    }
                    else
                    {
                        rowNew[col.ColumnName] = myValue;
                    }
                }
                else
                {
                    rowNew[col.ColumnName] = drow[col.ColumnName];
                }
            }

            int repeatCount = 1;
            if (dicRepeat.TryGetValue(str, out repeatCount))
            {
                rowNew["重复数量"] = repeatCount;
            }
            else
            {
                rowNew["重复数量"] = 1;
            }

            return rowNew;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }

        private void dataGridAll_ColumnHeaderMouseClick(object sender, DataGridViewCellMouseEventArgs e)
        {
            string columnText = (sender as DataGridView).Columns[e.ColumnIndex].HeaderText;
            if (rdoLng.Checked)
            {
                rdoLng.Text = columnText;
            }
            if (rdoLat.Checked)
            {
                rdoLat.Text = columnText;
            }
        }
    }
}
