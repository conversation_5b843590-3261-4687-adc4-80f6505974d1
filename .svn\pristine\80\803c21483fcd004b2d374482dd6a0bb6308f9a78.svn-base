﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTNRLTECollaborativeAnaByGrid
{
    public class SerialResult
    {
        public int Index { get; set; }
        public string TypeDesc { get; set; } = "";
        public int GridCount { get { return ResultList.Count; } }
        public List<Result> ResultList { get; set; } = new List<Result>();
    }

    public class Result : GridItem
    {
        public string Token { get; set; }
        public ColorUnit Grid { get; set; }
        public ParamInfo NR { get; set; } = new ParamInfo();
        public ParamInfo LTE { get; set; } = new ParamInfo();
        public ZTNRLTECollaborativeAnaType Type { get; set; } = ZTNRLTECollaborativeAnaType.Unknown;
        public string TypeDesc { get; set; }
        public double CentLng { get; set; }
        public double CentLat { get; set; }

        public void Calculate()
        {
            CentLng = Grid.CenterLng;
            CentLat = Grid.CenterLat;

            NR.Calculate();
            LTE.Calculate();
        }

        public void SetTpeDesc()
        {
            switch (Type)
            {
                case ZTNRLTECollaborativeAnaType.WeakCover4G5G:
                    TypeDesc = "覆盖同差";
                    break;
                case ZTNRLTECollaborativeAnaType.Better4G:
                    TypeDesc = "4G优于5G";
                    break;
                case ZTNRLTECollaborativeAnaType.Better5G:
                    TypeDesc = "5G优于4G";
                    break;
            }
        }

        public class ParamInfo
        {
            public DataInfo RSRP { get; set; } = new DataInfo();
            public DataInfo SINR { get; set; } = new DataInfo();

            public void Calculate()
            {
                RSRP.Calculate();
                SINR.Calculate();
            }
        }

        public class DataInfo
        {
            public double Sum { get; set; } = 0;
            public double Count { get; set; } = 0;
            public double? Avg { get; private set; } = null;

            public void Calculate()
            {
                if (Count > 0)
                {
                    Avg = Math.Round(Sum / Count, 2);
                }
            }
        }
    }
}
