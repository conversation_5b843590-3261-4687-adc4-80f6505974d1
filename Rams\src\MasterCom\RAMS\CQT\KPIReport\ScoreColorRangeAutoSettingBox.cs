﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Frame;

namespace MasterCom.RAMS.CQT
{
    public partial class ScoreColorRangeAutoSettingBox : RangeColorAutoSettingBox
    {
        float min;
        float max;
        double scoreMin;
        double scoreMax;
        protected ScoreOrderType scoreType;
        public bool IsSmoothScore { get; set; } = true;
        public ScoreColorRangeAutoSettingBox(float min, float max, double scoreMin, double scoreMax, ScoreOrderType scoreType)
            : base(min, max)
        {
            InitializeComponent();
            this.min = min;
            this.max = max;
            this.scoreMin = scoreMin;
            this.scoreMax = scoreMax;
            this.scoreType = scoreType;
            buttonOK.Click += new EventHandler(buttonOK_Click);
        }

        void buttonOK_Click(object sender, EventArgs e)
        {
            RangeValues = new List<CQTKPIScoreColorRange>();
            for (int i = 0; i < RangeCount; i++)
            {
                CQTKPIScoreColorRange rg = new CQTKPIScoreColorRange();
                rg.IsSmoonthScore = IsSmoothScore;
                RangeValues.Add(rg);
            }
            MakeRange();
            DialogResult = DialogResult.OK;
        }
        public new List<CQTKPIScoreColorRange> RangeValues { get; set; }

        protected new virtual void MakeRange()
        {
            if (IsSmoothScore)
            {
                makeSmoothRange();
            }
            else
            {
                makeStepRange();
            }
        }

        protected virtual void makeStepRange()
        {
            double scoreRStep = (scoreMax - scoreMin + 1) / RangeCount;
            float range = max - min;
            for (int i = 0; i < RangeCount; i++)
            {
                CQTKPIScoreColorRange rangeColor = RangeValues[i];
                rangeColor.Min = (int)((min + range * i / RangeCount) * 10) / 10.0F;
                rangeColor.Max = (int)((min + range * (i + 1) / RangeCount) * 10) / 10.0F;
                if (rangeColor.Max == max)
                {
                    rangeColor.MaxIncluded = true;
                }
                float value = (float)(scoreMin + scoreRStep * i);
                rangeColor.ScoreRangeMin = value;
                rangeColor.ScoreRangeMax = value;

                float percent = RangeCount == 1 ? 0.5F : (float)i / (RangeCount - 1);
                Color beginColor;
                Color endColor;
                if (checkBoxColorVia.Checked)
                {
                    percent = percent * 2;
                    if (percent < 1)
                    {
                        beginColor = labelColorBegin.BackColor;
                        endColor = labelColorVia.BackColor;
                    }
                    else
                    {
                        percent -= 1;
                        beginColor = labelColorVia.BackColor;
                        endColor = labelColorEnd.BackColor;
                    }
                }
                else
                {
                    beginColor = labelColorBegin.BackColor;
                    endColor = labelColorEnd.BackColor;
                }
                rangeColor.Value = Color.FromArgb(
                  (int)(beginColor.R + (endColor.R - beginColor.R) * percent),
                  (int)(beginColor.G + (endColor.G - beginColor.G) * percent),
                  (int)(beginColor.B + (endColor.B - beginColor.B) * percent)
                  );
            }
        }

        protected virtual void makeSmoothRange()
        {
            float range = max - min;
            for (int i = 0; i < RangeCount; i++)
            {
                CQTKPIScoreColorRange rangeColor = RangeValues[i];
                double scoreR = scoreMax - scoreMin;
                rangeColor.Min = (int)((min + range * i / RangeCount) * 10) / 10.0F;
                rangeColor.Max = (int)((min + range * (i + 1) / RangeCount) * 10) / 10.0F;
                if (rangeColor.Max == max)
                {
                    rangeColor.MaxIncluded = true;
                }
                if (scoreType == ScoreOrderType.Positive)
                {
                    rangeColor.ScoreRangeMax = (int)((scoreMin + scoreR * (i + 1) / RangeCount) * 10) / 10.0F;
                    rangeColor.ScoreRangeMin = (int)((scoreMin + scoreR * i / RangeCount) * 10) / 10.0F;//得分
                }
                else
                {
                    rangeColor.ScoreRangeMax = (int)((scoreMax - scoreR * i / RangeCount) * 10) / 10.0F;//得分
                    rangeColor.ScoreRangeMin = (int)((scoreMax - scoreR * (i + 1) / RangeCount) * 10) / 10.0F;//得分
                }

                float percent = RangeCount == 1 ? 0.5F : (float)i / (RangeCount - 1);
                Color beginColor;
                Color endColor;
                if (checkBoxColorVia.Checked)
                {
                    percent = percent * 2;
                    if (percent < 1)
                    {
                        beginColor = labelColorBegin.BackColor;
                        endColor = labelColorVia.BackColor;
                    }
                    else
                    {
                        percent -= 1;
                        beginColor = labelColorVia.BackColor;
                        endColor = labelColorEnd.BackColor;
                    }
                }
                else
                {
                    beginColor = labelColorBegin.BackColor;
                    endColor = labelColorEnd.BackColor;
                }

                rangeColor.Value = Color.FromArgb(
                    (int)(beginColor.R + (endColor.R - beginColor.R) * percent),
                    (int)(beginColor.G + (endColor.G - beginColor.G) * percent),
                    (int)(beginColor.B + (endColor.B - beginColor.B) * percent)
                    );
            }
        }

    }
}
