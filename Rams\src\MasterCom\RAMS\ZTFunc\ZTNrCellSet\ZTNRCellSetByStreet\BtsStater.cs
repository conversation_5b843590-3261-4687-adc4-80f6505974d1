﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class BtsStater
    {
        public int SN { get; set; }
        public ISite BTS { get; set; }
        public string BTSName { get; set; }
        public int TPCount { get; set; }
        public long TPTotalCount { get; set; }
        public double TPProportion { get; set; }

        public CellSetDataSub RsrpDataSub { get; private set; } = new CellSetDataSub(-10, -120);
        public CellSetDataSub SinrDataSub { get; private set; } = new CellSetDataSub(50, -50);
        public CellSetDataSub RsrqDataSub { get; private set; } = new CellSetDataSub(40, -40);
        public CellSetDataSub DistanceDataSub { get; private set; } = new CellSetDataSub();

        public string AreaPlaceDesc { get; private set; }
        public string GridDesc { get; private set; }

        public virtual void Calculate()
        {
            RsrpDataSub.Calculate();
            SinrDataSub.Calculate();
            RsrqDataSub.Calculate();
            DistanceDataSub.Calculate();

            TPProportion = Math.Round(1d * TPCount / TPTotalCount, 2);
            //setDesc();
        }

        //private void setDesc()
        //{
        //    AreaPlaceDesc = GISManager.GetInstance().GetAreaPlaceDesc(BTS.Longitude, BTS.Latitude);
        //    GridDesc = GISManager.GetInstance().GetGridDesc(BTS.Longitude, BTS.Latitude);
        //}
    }

    public class NRBtsStater : BtsStater
    {
        public override void Calculate()
        {
            if (BTS is NRBTS)
            {
                NRBTS nrBTS = BTS as NRBTS;
                BTSName = nrBTS.Name;
            }
            base.Calculate();
        }
    }
}
