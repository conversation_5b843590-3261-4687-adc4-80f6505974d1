﻿using System;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRHandOverAnaSetForm : BaseDialog
    {
        public NRHandOverAnaSetForm()
        {
            InitializeComponent();
        }

        public NRHandOverAnaCondition GetCondition()
        {
            NRHandOverAnaCondition condition = new NRHandOverAnaCondition();
            condition.BeforeSecond = Math.Round((double)numBeforeSecond.Value,3);
            condition.AfterSecond = Math.Round((double)numAfterSecond.Value,3);
            condition.SiteDistance = (int)numSiteDistance.Value;
            //condition.IsBusiness = chkBusiness.Checked;
            condition.IsAnaLte = chkAnaLte.Checked;
            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
