﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTLteNBCellCheckAnaListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTLteNBCellCheckAnaListForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ListViewNBCheckStat = new BrightIdeasSoftware.TreeListView();
            this.olvColumnStatSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatNetType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatCellTAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatCellCellID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatCellEARFCN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatCellPCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatCellRSRP = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatCellRxQual = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatScore = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatStatusByMsg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatStatus = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnStatDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewNBCheckStat)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel,
            this.toolStripMenuItem1,
            this.miExpandAll,
            this.miCallapsAll});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(153, 98);
            this.ctxMenu.Opening += new System.ComponentModel.CancelEventHandler(this.ctxMenu_Opening);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(152, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(149, 6);
            this.toolStripMenuItem1.Visible = false;
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(152, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCallapsAll
            // 
            this.miCallapsAll.Name = "miCallapsAll";
            this.miCallapsAll.Size = new System.Drawing.Size(152, 22);
            this.miCallapsAll.Text = "全部合并";
            this.miCallapsAll.Click += new System.EventHandler(this.miCallapsAll_Click);
            // 
            // ListViewNBCheckStat
            // 
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatSN);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatCellName);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatNetType);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatCellTAC);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatCellCellID);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatCellEARFCN);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatCellPCI);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatSampleCount);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatCellRSRP);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatCellRxQual);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatScore);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatStatusByMsg);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatStatus);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnStatDistance);
            this.ListViewNBCheckStat.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.ListViewNBCheckStat.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnStatSN,
            this.olvColumnStatCellName,
            this.olvColumnStatNetType,
            this.olvColumnStatCellTAC,
            this.olvColumnStatCellCellID,
            this.olvColumnStatCellEARFCN,
            this.olvColumnStatCellPCI,
            this.olvColumnStatSampleCount,
            this.olvColumnStatCellRSRP,
            this.olvColumnStatCellRxQual,
            this.olvColumnStatScore,
            this.olvColumnStatStatusByMsg,
            this.olvColumnStatStatus,
            this.olvColumnStatDistance});
            this.ListViewNBCheckStat.ContextMenuStrip = this.ctxMenu;
            this.ListViewNBCheckStat.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewNBCheckStat.FullRowSelect = true;
            this.ListViewNBCheckStat.GridLines = true;
            this.ListViewNBCheckStat.HeaderWordWrap = true;
            this.ListViewNBCheckStat.IsNeedShowOverlay = false;
            this.ListViewNBCheckStat.Location = new System.Drawing.Point(1, 1);
            this.ListViewNBCheckStat.Name = "ListViewNBCheckStat";
            this.ListViewNBCheckStat.OwnerDraw = true;
            this.ListViewNBCheckStat.ShowGroups = false;
            this.ListViewNBCheckStat.Size = new System.Drawing.Size(1249, 501);
            this.ListViewNBCheckStat.TabIndex = 7;
            this.ListViewNBCheckStat.UseCompatibleStateImageBehavior = false;
            this.ListViewNBCheckStat.View = System.Windows.Forms.View.Details;
            this.ListViewNBCheckStat.VirtualMode = true;
            // 
            // olvColumnStatSN
            // 
            this.olvColumnStatSN.AspectName = "";
            this.olvColumnStatSN.HeaderFont = null;
            this.olvColumnStatSN.Text = "序号";
            // 
            // olvColumnStatCellName
            // 
            this.olvColumnStatCellName.HeaderFont = null;
            this.olvColumnStatCellName.Text = "小区名称";
            this.olvColumnStatCellName.Width = 160;
            // 
            // olvColumnStatNetType
            // 
            this.olvColumnStatNetType.HeaderFont = null;
            this.olvColumnStatNetType.Text = "网络类型";
            this.olvColumnStatNetType.Width = 80;
            // 
            // olvColumnStatCellTAC
            // 
            this.olvColumnStatCellTAC.HeaderFont = null;
            this.olvColumnStatCellTAC.Text = "TAC/LAC";
            this.olvColumnStatCellTAC.Width = 80;
            // 
            // olvColumnStatCellCellID
            // 
            this.olvColumnStatCellCellID.HeaderFont = null;
            this.olvColumnStatCellCellID.Text = "CellID/CI";
            this.olvColumnStatCellCellID.Width = 80;
            // 
            // olvColumnStatCellEARFCN
            // 
            this.olvColumnStatCellEARFCN.HeaderFont = null;
            this.olvColumnStatCellEARFCN.Text = "EARFCN/BCCH";
            this.olvColumnStatCellEARFCN.Width = 100;
            // 
            // olvColumnStatCellPCI
            // 
            this.olvColumnStatCellPCI.HeaderFont = null;
            this.olvColumnStatCellPCI.Text = "PCI/BSIC";
            this.olvColumnStatCellPCI.Width = 80;
            // 
            // olvColumnStatSampleCount
            // 
            this.olvColumnStatSampleCount.HeaderFont = null;
            this.olvColumnStatSampleCount.Text = "采样点数";
            this.olvColumnStatSampleCount.Width = 80;
            // 
            // olvColumnStatCellRSRP
            // 
            this.olvColumnStatCellRSRP.HeaderFont = null;
            this.olvColumnStatCellRSRP.Text = "平均信号强度";
            this.olvColumnStatCellRSRP.Width = 80;
            // 
            // olvColumnStatCellRxQual
            // 
            this.olvColumnStatCellRxQual.HeaderFont = null;
            this.olvColumnStatCellRxQual.Text = "平均RxQual";
            this.olvColumnStatCellRxQual.Width = 100;
            // 
            // olvColumnStatScore
            // 
            this.olvColumnStatScore.HeaderFont = null;
            this.olvColumnStatScore.Text = "总得分";
            this.olvColumnStatScore.Width = 80;
            // 
            // olvColumnStatStatusByMsg
            // 
            this.olvColumnStatStatusByMsg.DisplayIndex = 12;
            this.olvColumnStatStatusByMsg.HeaderFont = null;
            this.olvColumnStatStatusByMsg.Text = "检测结果(与信令对比)";
            this.olvColumnStatStatusByMsg.Width = 80;
            // 
            // olvColumnStatStatus
            // 
            this.olvColumnStatStatus.DisplayIndex = 11;
            this.olvColumnStatStatus.HeaderFont = null;
            this.olvColumnStatStatus.Text = "检测结果(与工参对比)";
            this.olvColumnStatStatus.Width = 80;
            // 
            // olvColumnStatDistance
            // 
            this.olvColumnStatDistance.HeaderFont = null;
            this.olvColumnStatDistance.Text = "距离(米)";
            this.olvColumnStatDistance.Width = 80;
            // 
            // ZTLteNBCellCheckAnaListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1251, 502);
            this.Controls.Add(this.ListViewNBCheckStat);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTLteNBCellCheckAnaListForm";
            this.Text = "LTE邻区检测分析结果";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewNBCheckStat)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCallapsAll;
        private BrightIdeasSoftware.TreeListView ListViewNBCheckStat;
        private BrightIdeasSoftware.OLVColumn olvColumnStatSN;
        private BrightIdeasSoftware.OLVColumn olvColumnStatCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnStatCellTAC;
        private BrightIdeasSoftware.OLVColumn olvColumnStatCellCellID;
        private BrightIdeasSoftware.OLVColumn olvColumnStatCellEARFCN;
        private BrightIdeasSoftware.OLVColumn olvColumnStatCellPCI;
        private BrightIdeasSoftware.OLVColumn olvColumnStatSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnStatCellRSRP;
        private BrightIdeasSoftware.OLVColumn olvColumnStatScore;
        private BrightIdeasSoftware.OLVColumn olvColumnStatDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnStatStatus;
        private BrightIdeasSoftware.OLVColumn olvColumnStatNetType;
        private BrightIdeasSoftware.OLVColumn olvColumnStatStatusByMsg;
        private BrightIdeasSoftware.OLVColumn olvColumnStatCellRxQual;

    }
}