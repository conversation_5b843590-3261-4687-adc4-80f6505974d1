﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTScanHighCoverageRoadSetForm : BaseDialog
    {
        public ZTScanHighCoverageRoadSetForm(ServiceType netType)
        {
            InitializeComponent();
            if (netType ==ServiceType.GSM_SCAN)    //GSM
            {
                this.spinEditMaxDiff.Value = 12;
                this.spinEditRxlevMin.Value = -80;
                this.spinEditCoverage.Value = 7;
            }
            else if (netType == ServiceType .TD_SCAN||
                netType == ServiceType.LTE_SCAN_TOPN) //TD、LTE
            {
                this.spinEditMaxDiff.Value = 6;
                this.spinEditRxlevMin.Value = -95;
                this.spinEditCoverage.Value = 4;
            }
        }

        /// <summary>
        /// 获取采样点重叠覆盖度分析条件的数据
        /// </summary>
        /// <returns></returns>
        public void setCondition(out ScanHighCoverageRoadCondition condhighCover)
        {
            condhighCover = new ScanHighCoverageRoadCondition();
            condhighCover.RxLevMaxDiff = (int)this.spinEditMaxDiff.Value;
            condhighCover.RxLevMin = (int)this.spinEditRxlevMin.Value;
            condhighCover.RelCoverate = (int)this.spinEditCoverage.Value;
            condhighCover.RoadDistance = (int)this.spinEditRoadDistance.Value;
            condhighCover.SampleDistance = (int)this.spinEditSampleDistance.Value;
       }

        public void FillCondition(ScanHighCoverageRoadCondition condhighCover)
        {
            this.spinEditMaxDiff.Value = condhighCover.RxLevMaxDiff;
            this.spinEditRxlevMin.Value = condhighCover.RxLevMin;
            this.spinEditCoverage.Value = condhighCover.RelCoverate;
            this.spinEditRoadDistance.Value = condhighCover.RoadDistance;
            this.spinEditSampleDistance.Value = condhighCover.SampleDistance;
        }

        /// <summary>
        /// 确定
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        /// <summary>
        /// 取消
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}