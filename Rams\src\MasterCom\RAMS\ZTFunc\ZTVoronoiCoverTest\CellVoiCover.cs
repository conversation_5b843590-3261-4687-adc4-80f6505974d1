﻿/** 小区方向切割基站泰森多边形接口
 * 
 * 用法：
        // GSMCellVoiCover voiCover = new GSMCellVoiCover();
        TDCellVoiCover voiCover = new TDCellVoiCover();
        Dictionary<TDCell, List<Vertex[]>> cellPolysDict = voiCover.Construct();
        if (cellPolysDict == null)
        {
            MessageBox.Show(voiCover.LastErrorText);
            return;
        }

        List<List<Vertex[]>> drawList = new List<List<Vertex[]>>();
        foreach (List<Vertex[]> lst in cellPolysDict.Values)
        {
            drawList.Add(lst);
        }
        VoronoiLayer.GetInstance().Draw(drawList);
 */

using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// GSM小区方向切割泰森多边形
    /// </summary>
    public class GSMCellVoiCover
    {
        public string LastErrorText { get; set; }

        /// <summary>
        /// GSM小区方向切割泰森多边形
        /// </summary>
        /// <returns>出错返回null</returns>
        public Dictionary<Cell, List<Vertex[]>> Construct()
        {
            Init();
            WaitBox.Show("正在建立GSM泰森多边形数据映射...", PrepareData);
            DoLongLatVoi();
            if (longlatPolysDict == null && LastErrorText != "")
            {
                cellPolysDict = null;
                return cellPolysDict;
            }
            WaitBox.Show("正在按GSM小区切割泰森多边形...", DoCellClip);
            return cellPolysDict;
        }

        private bool IsValidCell(Cell cell)
        {
            return cell.BelongBTS != null && cell.BelongBTS.Type == BTSType.Outdoor;
        }

        #region private member
        private void Init()
        {
            LastErrorText = "";
            longlatPolysDict = null;
            cellPolysDict = new Dictionary<Cell,List<Vertex[]>>();
        }

        private void PrepareData()
        {
            if (cellList != null)
            {
                System.Threading.Thread.Sleep(500);
                WaitBox.Close();
                return;
            }

            if (MapCellLayer.DrawCurrent)
            {
                cellList = MainModel.GetInstance().CellManager.GetCurrentCells();
            }
            else
            {
                cellList = MainModel.GetInstance().CellManager.GetCells(MapCellLayer.CurShowTimeAt);
            }

            int loop = 0, count = cellList.Count;
            foreach (Cell cell in cellList)
            {
                WaitBox.ProgressPercent = ++loop * 100 / count;
                if (!IsValidCell(cell))
                {
                    continue;
                }

                Vertex v = new Vertex(cell.Longitude, cell.Latitude);
                BTS bts = cell.BelongBTS;

                // 用于构建基站多边形的经纬坐标
                if (!longlatList.Contains(v))
                {
                    longlatList.Add(v);
                }

                // 一个经纬坐标点可能包含多个基站
                if (!longlatBtsDict.ContainsKey(v))
                {
                    longlatBtsDict.Add(v, new List<BTS>());
                }
                longlatBtsDict[v].Add(bts);

                // 一个基站包含多个小区
                if (!btsCellDict.ContainsKey(bts))
                {
                    btsCellDict.Add(bts, new List<Cell>());
                }
                btsCellDict[bts].Add(cell);
            }

            System.Threading.Thread.Sleep(500);
            WaitBox.Close();
        }

        /// <summary>
        /// 按基站构建泰森多边形
        /// </summary>
        private void DoLongLatVoi()
        {
            VoronoiManager<Vertex> manager = VoronoiManager<Vertex>.GetInstance();
            longlatPolysDict = manager.Construct(longlatList, Filter, true);
            LastErrorText = manager.LastErrorText;
        }

        /// <summary>
        /// 按小区方向切割多边形
        /// </summary>
        private void DoCellClip()
        {
            int loop = 0, count = cellList.Count;
            foreach (BTS bts in btsCellDict.Keys)
            {
                WaitBox.ProgressPercent = ++loop * 100 / count;

                // 基站不包含小区或者小区不在基站多边形返回结果中（被范围过滤）
                if (btsCellDict[bts].Count == 0)
                {
                    continue;
                }
                Vertex v = new Vertex(btsCellDict[bts][0].Longitude, btsCellDict[bts][0].Latitude);
                if (!longlatPolysDict.ContainsKey(v))
                {
                    continue;
                }

                // 提取小区方向并进行排序
                List<short> dirList = new List<short>();
                foreach (Cell cell in btsCellDict[bts])
                {
                    if (!dirList.Contains(cell.Direction))
                    {
                        dirList.Add(cell.Direction);
                    }
                }
                dirList.Sort();

                // 按小区方向切割
                Dictionary<short, List<Vertex[]>> dirPolysDict =
                    VoronoiCellClipper.DoClip(v, longlatPolysDict[v], dirList);
                foreach (Cell cell in btsCellDict[bts])
                {
                    if (!cellPolysDict.ContainsKey(cell))
                    {
                        cellPolysDict.Add(cell, dirPolysDict[cell.Direction]);
                    }
                }
            }

            System.Threading.Thread.Sleep(500);
            WaitBox.Close();
        }

        /// <summary>
        /// 使用切割边界过滤基站
        /// </summary>
        /// <param name="v"></param>
        /// <param name="mop2"></param>
        /// <returns></returns>
        private bool Filter(Vertex v, MapOperation2 mop2)
        {
            return mop2.CheckPointInRegion(v.X, v.Y);
        }

        // 小区列表
        private static List<Cell> cellList { get; set; }
        // 用于构建基站多边形的不重复经纬坐标点
        private static List<Vertex> longlatList = new List<Vertex>();
        // 同一经纬坐标点可能存在多个基站
        private static Dictionary<Vertex, List<BTS>> longlatBtsDict = new Dictionary<Vertex, List<BTS>>();
        // 一个基站有多个小区
        private static Dictionary<BTS, List<Cell>> btsCellDict = new Dictionary<BTS, List<Cell>>();
        // 基站泰森多边形的构建结果
        private Dictionary<Vertex, List<Vertex[]>> longlatPolysDict;
        // 按小区方向切割后的结果
        private Dictionary<Cell, List<Vertex[]>> cellPolysDict;
#endregion
    }

    public class TDCellVoiCover
    {
        public string LastErrorText { get; set; }

        public Dictionary<TDCell, List<Vertex[]>> Construct()
        {
            Init();
            WaitBox.Show("正在建立TD泰森多边形数据映射...", PrepareData);
            DoLongLatVoi();
            if (longlatPolysDict == null && LastErrorText != "")
            {
                cellPolysDict = null;
                return cellPolysDict;
            }
            WaitBox.Show("正在按TD小区切割泰森多边形...", DoCellClip);
            return cellPolysDict;
        }

        #region private member
        private bool IsValidCell(TDCell cell)
        {
            return cell.BelongBTS != null && cell.BelongBTS.Type == TDNodeBType.Outdoor;
        }

        private void Init()
        {
            LastErrorText = "";
            longlatPolysDict = null;
            cellPolysDict = new Dictionary<TDCell, List<Vertex[]>>();
        }

        private void PrepareData()
        {
            if (cellList != null)
            {
                System.Threading.Thread.Sleep(500);
                WaitBox.Close();
                return;
            }

            if (MapCellLayer.DrawCurrent)
            {
                cellList = MainModel.GetInstance().CellManager.GetCurrentTDCells();
            }
            else
            {
                cellList = MainModel.GetInstance().CellManager.GetTDCells(MapCellLayer.CurShowTimeAt);
            }

            int loop = 0, count = cellList.Count;
            foreach (TDCell cell in cellList)
            {
                WaitBox.ProgressPercent = ++loop * 100 / count;
                if (!IsValidCell(cell))
                {
                    continue;
                }

                Vertex v = new Vertex(cell.Longitude, cell.Latitude);
                TDNodeB bts = cell.BelongBTS;

                if (!longlatList.Contains(v))
                {
                    longlatList.Add(v);
                }

                if (!longlatBtsDict.ContainsKey(v))
                {
                    longlatBtsDict.Add(v, new List<TDNodeB>());
                }
                longlatBtsDict[v].Add(bts);

                if (!btsCellDict.ContainsKey(bts))
                {
                    btsCellDict.Add(bts, new List<TDCell>());
                }
                btsCellDict[bts].Add(cell);
            }

            System.Threading.Thread.Sleep(500);
            WaitBox.Close();
        }

        private void DoLongLatVoi()
        {
            VoronoiManager<Vertex> manager = VoronoiManager<Vertex>.GetInstance();
            longlatPolysDict = manager.Construct(longlatList, Filter, true);
            LastErrorText = manager.LastErrorText;
        }

        private void DoCellClip()
        {
            int loop = 0, count = cellList.Count;
            foreach (TDNodeB bts in btsCellDict.Keys)
            {
                WaitBox.ProgressPercent = ++loop * 100 / count;

                if (btsCellDict[bts].Count == 0)
                {
                    continue;
                }
                Vertex v = new Vertex(btsCellDict[bts][0].Longitude, btsCellDict[bts][0].Latitude);
                if (!longlatPolysDict.ContainsKey(v))
                {
                    continue;
                }

                List<short> dirList = new List<short>();
                foreach (TDCell cell in btsCellDict[bts])
                {
                    if (!dirList.Contains(cell.Direction))
                    {
                        dirList.Add(cell.Direction);
                    }
                }
                dirList.Sort();

                Dictionary<short, List<Vertex[]>> dirPolysDict =
                    VoronoiCellClipper.DoClip(v, longlatPolysDict[v], dirList);
                foreach (TDCell cell in btsCellDict[bts])
                {
                    if (!cellPolysDict.ContainsKey(cell))
                    {
                        cellPolysDict.Add(cell, dirPolysDict[cell.Direction]);
                    }
                }
            }

            System.Threading.Thread.Sleep(500);
            WaitBox.Close();
        }

        private bool Filter(Vertex v, MapOperation2 mop2)
        {
            return mop2.CheckPointInRegion(v.X, v.Y);
        }

        private static List<TDCell> cellList { get; set; }
        private static List<Vertex> longlatList = new List<Vertex>();
        private static Dictionary<Vertex, List<TDNodeB>> longlatBtsDict = new Dictionary<Vertex, List<TDNodeB>>();
        private static Dictionary<TDNodeB, List<TDCell>> btsCellDict = new Dictionary<TDNodeB, List<TDCell>>();
        private Dictionary<Vertex, List<Vertex[]>> longlatPolysDict;
        private Dictionary<TDCell, List<Vertex[]>> cellPolysDict;
        #endregion
    }

    public class LTECellVoiCover
    {
        public string LastErrorText { get; set; }

        public Dictionary<LTECell, List<Vertex[]>> Construct()
        {
            Init();
            WaitBox.Show("正在建立LTE泰森多边形数据映射...", PrepareData);
            DoLongLatVoi();
            if (longlatPolysDict == null && LastErrorText != "")
            {
                cellPolysDict = null;
                return cellPolysDict;
            }
            WaitBox.Show("正在按LTE小区切割泰森多边形...", DoCellClip);
            return cellPolysDict;
        }

        #region private member
        private bool IsValidCell(LTECell cell)
        {
            return cell.BelongBTS != null && cell.BelongBTS.Type == LTEBTSType.Outdoor;
        }

        private void Init()
        {
            LastErrorText = "";
            longlatPolysDict = null;
            cellPolysDict = new Dictionary<LTECell, List<Vertex[]>>();
        }

        private void PrepareData()
        {
            if (cellList != null)
            {
                System.Threading.Thread.Sleep(500);
                WaitBox.Close();
                return;
            }

            if (MapCellLayer.DrawCurrent)
            {
                cellList = MainModel.GetInstance().CellManager.GetCurrentLTECells();
            }
            else
            {
                cellList = MainModel.GetInstance().CellManager.GetLTECells(MapCellLayer.CurShowTimeAt);
            }

            int loop = 0, count = cellList.Count;
            foreach (LTECell cell in cellList)
            {
                WaitBox.ProgressPercent = ++loop * 100 / count;
                if (!IsValidCell(cell))
                {
                    continue;
                }

                Vertex v = new Vertex(cell.Longitude, cell.Latitude);
                LTEBTS bts = cell.BelongBTS;

                if (!longlatList.Contains(v))
                {
                    longlatList.Add(v);
                }

                if (!longlatBtsDict.ContainsKey(v))
                {
                    longlatBtsDict.Add(v, new List<LTEBTS>());
                }
                longlatBtsDict[v].Add(bts);

                if (!btsCellDict.ContainsKey(bts))
                {
                    btsCellDict.Add(bts, new List<LTECell>());
                }
                btsCellDict[bts].Add(cell);
            }

            System.Threading.Thread.Sleep(500);
            WaitBox.Close();
        }

        private void DoLongLatVoi()
        {
            VoronoiManager<Vertex> manager = VoronoiManager<Vertex>.GetInstance();
            longlatPolysDict = manager.Construct(longlatList, Filter, true);
            LastErrorText = manager.LastErrorText;
        }

        private void DoCellClip()
        {
            int loop = 0, count = cellList.Count;
            foreach (LTEBTS bts in btsCellDict.Keys)
            {
                WaitBox.ProgressPercent = ++loop * 100 / count;

                if (btsCellDict[bts].Count == 0)
                {
                    continue;
                }
                Vertex v = new Vertex(btsCellDict[bts][0].Longitude, btsCellDict[bts][0].Latitude);
                if (!longlatPolysDict.ContainsKey(v))
                {
                    continue;
                }

                List<short> dirList = new List<short>();
                foreach (LTECell cell in btsCellDict[bts])
                {
                    if (!dirList.Contains(cell.Direction))
                    {
                        dirList.Add(cell.Direction);
                    }
                }
                dirList.Sort();

                Dictionary<short, List<Vertex[]>> dirPolysDict =
                    VoronoiCellClipper.DoClip(v, longlatPolysDict[v], dirList);
                foreach (LTECell cell in btsCellDict[bts])
                {
                    if (!cellPolysDict.ContainsKey(cell))
                    {
                        cellPolysDict.Add(cell, dirPolysDict[cell.Direction]);
                    }
                }
            }

            System.Threading.Thread.Sleep(500);
            WaitBox.Close();
        }

        private bool Filter(Vertex v, MapOperation2 mop2)
        {
            return mop2.CheckPointInRegion(v.X, v.Y);
        }

        private static List<LTECell> cellList { get; set; }
        private static List<Vertex> longlatList = new List<Vertex>();
        private static Dictionary<Vertex, List<LTEBTS>> longlatBtsDict = new Dictionary<Vertex, List<LTEBTS>>();
        private static Dictionary<LTEBTS, List<LTECell>> btsCellDict = new Dictionary<LTEBTS, List<LTECell>>();
        private Dictionary<Vertex, List<Vertex[]>> longlatPolysDict;
        private Dictionary<LTECell, List<Vertex[]>> cellPolysDict;
        #endregion
    }
}
