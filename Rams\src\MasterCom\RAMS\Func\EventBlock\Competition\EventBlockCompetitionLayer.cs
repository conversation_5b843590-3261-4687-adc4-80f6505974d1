﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Runtime.Serialization;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func.EventBlock
{
    public class EventBlockCompetitionLayer : CustomDrawLayer
    {
        public EventBlockCompetitionLayer(MapOperation mp, string name)
            : base(mp, name)
        {

        }

        public double BlockRadius
        {
            get;
            set;
        }
        
        public bool DrawHost { get; set; } = true;
        public bool DrawGuest { get; set; } = true;
        public bool DrawOverlap { get; set; }
        private readonly SolidBrush hostBrush = new SolidBrush(Color.FromArgb(200, Color.Blue));
        public Color HostColor
        {
            get { return hostBrush.Color; }
            set { hostBrush.Color = Color.FromArgb(200, value); }
        }
        private readonly SolidBrush guestBrush = new SolidBrush(Color.FromArgb(200,Color.Lime));
        public Color GuestColor
        {
            get { return guestBrush.Color; }
            set { guestBrush.Color = Color.FromArgb(200, value); }
        }
        private readonly SolidBrush overlapBrush = new SolidBrush(Color.FromArgb(200, Color.Red));
        public Color OverlapColor
        {
            get { return overlapBrush.Color; }
            set { overlapBrush.Color = Color.FromArgb(100, value); }
        }

        public List<EventBlock> HostBlocks
        {
            get;
            set;
        }
        public List<EventBlock> GuestBlocks
        {
            get;
            set;
        }
        public List<EventBlock> OverlapBlocks
        {
            get;
            set;
        }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible)
            {
                return;
            }
            updateRect.Inflate((int)(40 * 10000 / Map.Scale), (int)(40 * 10000 / Map.Scale));
            DbRect dRect;
            Map.FromDisplay(updateRect, out dRect);
            double temp_long = Map.GetCenter().x;
            double temp_lati = Map.GetCenter().y;
            DbPoint ptDSel = new DbPoint(temp_long, temp_lati);
            PointF scrPointSel;
            Map.ToDisplay(ptDSel, out scrPointSel);
            //底层20米的精度跨度大小 0.0001951;
            double llGap = (0.0001951 / 20) * BlockRadius;
            DbPoint ptDSel2 = new DbPoint(temp_long + llGap, temp_lati);
            PointF scrPointSel2;
            Map.ToDisplay(ptDSel2, out scrPointSel2);
            int rGap = (int)(scrPointSel2.X - scrPointSel.X) + 1;
            if (DrawHost && HostBlocks != null && HostBlocks.Count > 0)
            {
                drawBlocks(graphics, dRect, hostBrush, HostBlocks, rGap);
            }
            if (DrawGuest && GuestBlocks != null && GuestBlocks.Count > 0)
            {
                drawBlocks(graphics, dRect, guestBrush, GuestBlocks, rGap);
            }
            if (DrawOverlap && OverlapBlocks != null && OverlapBlocks.Count > 0)
            {
                drawBlocks(graphics, dRect, overlapBrush, OverlapBlocks, rGap);
            }
        }

        private void drawBlocks(Graphics g, DbRect viewRect, SolidBrush brush, List<EventBlock> blocks, double radius)
        {
            if (blocks == null)
            {
                return;
            }

            float r = radius > 4 ? (float)radius : 4;
            foreach (EventBlock blk in blocks)
            {
                if (blk.Within(viewRect.x1, viewRect.y1, viewRect.x2, viewRect.y2))
                {
                    drawRegionBlock(g, viewRect, brush, r, blk);
                }
            }
        }

        private void drawRegionBlock(Graphics g, DbRect viewRect, SolidBrush brush, float r, EventBlock blk)
        {
            Region blockReg = null;
            foreach (Event evt in blk.AbnormalEvents)
            {
                if (evt.Within(viewRect.x1, viewRect.y1, viewRect.x2, viewRect.y2))
                {
                    DbPoint dPoint = new DbPoint(evt.Longitude, evt.Latitude);
                    PointF point;
                    this.Map.ToDisplay(dPoint, out point);
                    RectangleF rect = new RectangleF(point.X - r, point.Y - r, r * 2, r * 2);
                    GraphicsPath gp = new GraphicsPath();
                    gp.AddEllipse(rect);
                    if (blockReg == null)
                    {
                        blockReg = new Region(gp);
                    }
                    else
                    {
                        blockReg.Union(gp);
                    }
                }
            }
            g.FillRegion(brush, blockReg);
        }

    }



}
