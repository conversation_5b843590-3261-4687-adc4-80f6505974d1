﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTGSMAndTDScore.GSMScore
{
    public partial class GSMScoreSimpleFrm : Form
    {
        MainModel mainModel;
        public GSMScoreSimpleFrm(MainModel mainmodel)
        {
            InitializeComponent();
            mainModel = mainmodel;
            this.gridControl_result.DataSource = Source;
            this.gridControl_result.RefreshDataSource();
            this.labelControl2.Text = this.gridView_result.Columns[2].SummaryItem.SummaryValue.ToString();
        }
        public BindingSource Source { get; set; } = new BindingSource();

        /// <summary>
        /// 显示详细数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Button_detail_Click(object sender, EventArgs e)
        {
            //
        }
    }
}
