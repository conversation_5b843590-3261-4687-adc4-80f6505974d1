﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Xml;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class TestPointConvergedCondition
    {
        public string Description
        {
            get
            {
                StringBuilder str = new StringBuilder();
                str.AppendFormat("共{0}个条件{1}:\r\n", DetailConditionSet.Count, DetailConditionSet.Count <= 1 ? "" : "(关系为“" + LogicalType + "”)");
                foreach (TPConvergedDetailItem item in DetailConditionSet)
                {
                    str.Append(item.ToString() + "；" + Environment.NewLine);
                }
                return str.ToString();
            }
        }
        public ELogicalType LogicalType { get; set; }
        public List<TPConvergedDetailItem> DetailConditionSet { get; set; } = new List<TPConvergedDetailItem>();
        public bool Contains(string sysName,string paramName,int paramIdx)
        {
            bool contains = false;
            foreach (TPConvergedDetailItem item in DetailConditionSet)
            {
                if (item.SysName==sysName&&item.ParamName==paramName&&item.ParamArrayIndex==paramIdx)
                {
                    return true;
                }
            }
            return contains;
        }
        public bool IsMatched(TestPoint tp)
        {
            bool match = true;//最终逻辑判断结果，先假设为真
            foreach (TPConvergedDetailItem item in DetailConditionSet)
            {
                bool matchOne = item.IsMatch(tp);
                switch (LogicalType)
                {
                    case ELogicalType.与:
                        match = match && matchOne;
                        if (!match)
                        {//与 关系判断，其一条件不成立，即返回不符合：false
                            return false;
                        }
                        break;
                    case ELogicalType.或:
                        if (matchOne)
                        {//或 关系判断，其一条件成立，即返回符合：true
                            return true;
                        }
                        else
                        {
                            match = false;
                        }
                        break;
                    default:
                        break;
                }
            }
            return match;
        }

        internal System.Xml.XmlElement SaveConfig(MasterCom.Util.XmlConfigFile configFile, System.Xml.XmlElement config, string name, object value)
        {
            if (value is TestPointConvergedCondition)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                configFile.AddItem(item, "LogicalType", this.LogicalType.ToString());
                configFile.AddItem(item, "DetailConditionSet", this.DetailConditionSet, SaveConfig);
                return item;
            }
            else if (value is TPConvergedDetailItem)
            {
                return (value as TPConvergedDetailItem).SaveConfig(configFile, config, typeof(TPConvergedDetailItem).Name, value);
            }
            return null;
        }

        internal object LoadConfig(MasterCom.Util.XmlConfigFile configFile, XmlElement item, string itemName)
        {
            if (itemName==typeof(TestPointConvergedCondition).Name)
            {
                try
                {
                    LogicalType = (ELogicalType)Enum.Parse(typeof(ELogicalType), configFile.GetItemValue(item, "LogicalType").ToString());
                }
                catch
                {
                    //continue
                }
                List<object> list = configFile.GetItemValue(item, "DetailConditionSet", LoadConfig) as List<object>;
                if (list!=null)
                {
                    foreach (object obj in list)
                    {
                        if (obj is TPConvergedDetailItem)
                        {
                            DetailConditionSet.Add(obj as TPConvergedDetailItem);
                        }
                    }
                }
                return this;
            }
            else if (itemName==typeof(TPConvergedDetailItem).Name)
            {
                TPConvergedDetailItem dItem = new TPConvergedDetailItem();
                dItem.LoadConfig(configFile, item, itemName);
                return dItem;
            }
            return null;
        }
    }

    public class TPConvergedDetailItem : TestPointValueCondition
    {
        internal System.Xml.XmlElement SaveConfig(MasterCom.Util.XmlConfigFile configFile, System.Xml.XmlElement config, string name, object value)
        {
            if (value is TPConvergedDetailItem)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                configFile.AddItem(item, "SysName", this.SysName);
                configFile.AddItem(item, "ParamName", this.ParamName);
                configFile.AddItem(item, "ParamArrayIndex", this.ParamArrayIndex);
                configFile.AddItem(item, "MinValue", this.MinValue);
                configFile.AddItem(item, "MaxValue", this.MaxValue);
                return item;
            }
            return null;
        }

        internal object LoadConfig(MasterCom.Util.XmlConfigFile configFile, XmlElement item, string itemName)
        {
            if (itemName==typeof(TPConvergedDetailItem).Name)
            {
                this.SysName = configFile.GetItemValue(item, "SysName") as string;
                this.ParamName = configFile.GetItemValue(item, "ParamName") as string;
                this.ParamArrayIndex = (int)configFile.GetItemValue(item, "ParamArrayIndex");
                this.MinValue = (double)configFile.GetItemValue(item, "MinValue");
                this.MaxValue = (double)configFile.GetItemValue(item, "MaxValue");
                return this;
            }
            return null;
        }
    }

}
