﻿namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    partial class ImportAreaForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnEditPath = new DevExpress.XtraEditors.ButtonEdit();
            this.label1 = new System.Windows.Forms.Label();
            this.cbxDistrict = new System.Windows.Forms.ComboBox();
            this.label2 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.dataGridView = new System.Windows.Forms.DataGridView();
            this.colName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colFieldName = new System.Windows.Forms.DataGridViewComboBoxColumn();
            this.colShpFile = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.btnRemoveShpFile = new DevExpress.XtraEditors.SimpleButton();
            this.btnAddShpFile = new DevExpress.XtraEditors.SimpleButton();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.gridCtrlRankSetting = new DevExpress.XtraGrid.GridControl();
            this.gvRankSetting = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.cbxCol = new DevExpress.XtraEditors.Repository.RepositoryItemComboBox();
            this.cbxSnCol = new System.Windows.Forms.ComboBox();
            this.gridCtrlXls = new DevExpress.XtraGrid.GridControl();
            this.gvXls = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.label3 = new System.Windows.Forms.Label();
            this.btnImport = new DevExpress.XtraEditors.SimpleButton();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            ((System.ComponentModel.ISupportInitialize)(this.btnEditPath.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlRankSetting)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvRankSetting)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxCol)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlXls)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvXls)).BeginInit();
            this.SuspendLayout();
            // 
            // btnEditPath
            // 
            this.btnEditPath.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.btnEditPath.EditValue = "请选择文件";
            this.btnEditPath.Location = new System.Drawing.Point(168, 23);
            this.btnEditPath.Name = "btnEditPath";
            this.btnEditPath.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.btnEditPath.Properties.ButtonClick += new DevExpress.XtraEditors.Controls.ButtonPressedEventHandler(this.btnEditPath_Properties_ButtonClick);
            this.btnEditPath.Size = new System.Drawing.Size(686, 21);
            this.btnEditPath.TabIndex = 0;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(7, 28);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(155, 12);
            this.label1.TabIndex = 1;
            this.label1.Text = "区域层级关系文件(Excel)：";
            // 
            // cbxDistrict
            // 
            this.cbxDistrict.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxDistrict.FormattingEnabled = true;
            this.cbxDistrict.Location = new System.Drawing.Point(66, 12);
            this.cbxDistrict.Name = "cbxDistrict";
            this.cbxDistrict.Size = new System.Drawing.Size(121, 22);
            this.cbxDistrict.TabIndex = 2;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(19, 18);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(41, 12);
            this.label2.TabIndex = 1;
            this.label2.Text = "地市：";
            // 
            // groupBox1
            // 
            this.groupBox1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox1.Controls.Add(this.dataGridView);
            this.groupBox1.Controls.Add(this.btnRemoveShpFile);
            this.groupBox1.Controls.Add(this.btnAddShpFile);
            this.groupBox1.Location = new System.Drawing.Point(12, 50);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(863, 132);
            this.groupBox1.TabIndex = 4;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "区域图层文件(shp)";
            // 
            // dataGridView
            // 
            this.dataGridView.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dataGridView.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.colName,
            this.colFieldName,
            this.colShpFile});
            this.dataGridView.Location = new System.Drawing.Point(9, 20);
            this.dataGridView.Name = "dataGridView";
            this.dataGridView.RowHeadersVisible = false;
            this.dataGridView.RowTemplate.Height = 23;
            this.dataGridView.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridView.Size = new System.Drawing.Size(845, 77);
            this.dataGridView.TabIndex = 5;
            this.dataGridView.DataSourceChanged += new System.EventHandler(this.dataGridView_DataSourceChanged);
            this.dataGridView.CellDoubleClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dataGridView_CellDoubleClick);
            // 
            // colName
            // 
            this.colName.DataPropertyName = "Name";
            this.colName.HeaderText = "文件名";
            this.colName.Name = "colName";
            // 
            // colFieldName
            // 
            this.colFieldName.DataPropertyName = "ShapeSnFieldName";
            this.colFieldName.HeaderText = "区域序号字段";
            this.colFieldName.Name = "colFieldName";
            // 
            // colShpFile
            // 
            this.colShpFile.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
            this.colShpFile.DataPropertyName = "FilePath";
            this.colShpFile.HeaderText = "文件路径";
            this.colShpFile.Name = "colShpFile";
            this.colShpFile.ReadOnly = true;
            // 
            // btnRemoveShpFile
            // 
            this.btnRemoveShpFile.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRemoveShpFile.Location = new System.Drawing.Point(701, 103);
            this.btnRemoveShpFile.Name = "btnRemoveShpFile";
            this.btnRemoveShpFile.Size = new System.Drawing.Size(75, 23);
            this.btnRemoveShpFile.TabIndex = 4;
            this.btnRemoveShpFile.Text = "移除图层";
            this.btnRemoveShpFile.Click += new System.EventHandler(this.btnRemoveShpFile_Click);
            // 
            // btnAddShpFile
            // 
            this.btnAddShpFile.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnAddShpFile.Location = new System.Drawing.Point(782, 103);
            this.btnAddShpFile.Name = "btnAddShpFile";
            this.btnAddShpFile.Size = new System.Drawing.Size(75, 23);
            this.btnAddShpFile.TabIndex = 4;
            this.btnAddShpFile.Text = "添加图层";
            this.btnAddShpFile.Click += new System.EventHandler(this.btnAddShpFile_Click);
            // 
            // groupBox2
            // 
            this.groupBox2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox2.Controls.Add(this.groupBox3);
            this.groupBox2.Controls.Add(this.groupControl2);
            this.groupBox2.Controls.Add(this.cbxSnCol);
            this.groupBox2.Controls.Add(this.gridCtrlXls);
            this.groupBox2.Controls.Add(this.label3);
            this.groupBox2.Controls.Add(this.label1);
            this.groupBox2.Controls.Add(this.btnEditPath);
            this.groupBox2.Location = new System.Drawing.Point(12, 210);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(863, 353);
            this.groupBox2.TabIndex = 5;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "区域层级关系";
            // 
            // groupControl2
            // 
            this.groupControl2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.groupControl2.Controls.Add(this.gridCtrlRankSetting);
            this.groupControl2.Location = new System.Drawing.Point(451, 219);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(403, 125);
            this.groupControl2.TabIndex = 6;
            this.groupControl2.Text = "Excel列对应区域关系";
            // 
            // gridCtrlRankSetting
            // 
            this.gridCtrlRankSetting.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridCtrlRankSetting.Location = new System.Drawing.Point(2, 23);
            this.gridCtrlRankSetting.MainView = this.gvRankSetting;
            this.gridCtrlRankSetting.Name = "gridCtrlRankSetting";
            this.gridCtrlRankSetting.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.cbxCol});
            this.gridCtrlRankSetting.Size = new System.Drawing.Size(399, 100);
            this.gridCtrlRankSetting.TabIndex = 4;
            this.gridCtrlRankSetting.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvRankSetting});
            // 
            // gvRankSetting
            // 
            this.gvRankSetting.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn6,
            this.gridColumn7});
            this.gvRankSetting.GridControl = this.gridCtrlRankSetting;
            this.gvRankSetting.Name = "gvRankSetting";
            this.gvRankSetting.OptionsView.ShowGroupPanel = false;
            this.gvRankSetting.OptionsView.ShowIndicator = false;
            this.gvRankSetting.ShowButtonMode = DevExpress.XtraGrid.Views.Base.ShowButtonModeEnum.ShowAlways;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "区域级别";
            this.gridColumn6.FieldName = "Rank";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.OptionsColumn.AllowEdit = false;
            this.gridColumn6.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 0;
            this.gridColumn6.Width = 184;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "对应Excel列";
            this.gridColumn7.ColumnEdit = this.cbxCol;
            this.gridColumn7.FieldName = "Column";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 1;
            this.gridColumn7.Width = 211;
            // 
            // cbxCol
            // 
            this.cbxCol.AutoHeight = false;
            this.cbxCol.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxCol.Name = "cbxCol";
            this.cbxCol.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            // 
            // cbxSnCol
            // 
            this.cbxSnCol.DisplayMember = "ColumnName";
            this.cbxSnCol.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxSnCol.FormattingEnabled = true;
            this.cbxSnCol.Location = new System.Drawing.Point(97, 219);
            this.cbxSnCol.Name = "cbxSnCol";
            this.cbxSnCol.Size = new System.Drawing.Size(121, 22);
            this.cbxSnCol.TabIndex = 2;
            // 
            // gridCtrlXls
            // 
            this.gridCtrlXls.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.gridCtrlXls.Location = new System.Drawing.Point(9, 50);
            this.gridCtrlXls.MainView = this.gvXls;
            this.gridCtrlXls.Name = "gridCtrlXls";
            this.gridCtrlXls.Size = new System.Drawing.Size(845, 147);
            this.gridCtrlXls.TabIndex = 2;
            this.gridCtrlXls.UseEmbeddedNavigator = true;
            this.gridCtrlXls.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvXls});
            // 
            // gvXls
            // 
            this.gvXls.GridControl = this.gridCtrlXls;
            this.gvXls.Name = "gvXls";
            this.gvXls.OptionsBehavior.Editable = false;
            this.gvXls.OptionsView.ShowGroupPanel = false;
            this.gvXls.OptionsView.ShowIndicator = false;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(14, 224);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(77, 12);
            this.label3.TabIndex = 1;
            this.label3.Text = "区域编号列：";
            // 
            // btnImport
            // 
            this.btnImport.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnImport.Location = new System.Drawing.Point(795, 572);
            this.btnImport.Name = "btnImport";
            this.btnImport.Size = new System.Drawing.Size(75, 23);
            this.btnImport.TabIndex = 4;
            this.btnImport.Text = "导入";
            this.btnImport.Click += new System.EventHandler(this.btnImport_Click);
            // 
            // groupBox3
            // 
            this.groupBox3.Location = new System.Drawing.Point(9, 203);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(845, 10);
            this.groupBox3.TabIndex = 7;
            this.groupBox3.TabStop = false;
            // 
            // ImportAreaForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(882, 602);
            this.Controls.Add(this.btnImport);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.cbxDistrict);
            this.Controls.Add(this.label2);
            this.Name = "ImportAreaForm";
            this.Text = "导入区域图层信息";
            ((System.ComponentModel.ISupportInitialize)(this.btnEditPath.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlRankSetting)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvRankSetting)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxCol)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlXls)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvXls)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.ButtonEdit btnEditPath;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.ComboBox cbxDistrict;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraEditors.SimpleButton btnRemoveShpFile;
        private DevExpress.XtraEditors.SimpleButton btnAddShpFile;
        private System.Windows.Forms.GroupBox groupBox2;
        private DevExpress.XtraGrid.GridControl gridCtrlXls;
        private DevExpress.XtraGrid.Views.Grid.GridView gvXls;
        private DevExpress.XtraGrid.GridControl gridCtrlRankSetting;
        private DevExpress.XtraGrid.Views.Grid.GridView gvRankSetting;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraEditors.SimpleButton btnImport;
        private System.Windows.Forms.DataGridView dataGridView;
        private System.Windows.Forms.DataGridViewTextBoxColumn colName;
        private System.Windows.Forms.DataGridViewComboBoxColumn colFieldName;
        private System.Windows.Forms.DataGridViewTextBoxColumn colShpFile;
        private System.Windows.Forms.ComboBox cbxSnCol;
        private System.Windows.Forms.Label label3;
        private DevExpress.XtraEditors.Repository.RepositoryItemComboBox cbxCol;
        private System.Windows.Forms.GroupBox groupBox3;

    }
}