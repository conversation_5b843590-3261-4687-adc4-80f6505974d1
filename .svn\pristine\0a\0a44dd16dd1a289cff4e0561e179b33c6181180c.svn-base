﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteFddNBCellCheckAnaBase_WCDMA : ZTLteNBCellCheckAnaBase_GSM
    {
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26016, this.Name);
        }

        public LteFddNBCellCheckAnaBase_WCDMA(MainModel mainModel)
            : base(mainModel)
        {
            isLteFdd = true;
        }
        protected override void getReadyBeforeQuery()
        {
            this.Columns = new List<string>();
            this.Columns.Add("lte_fdd_TAC");
            this.Columns.Add("lte_fdd_ECI");
            this.Columns.Add("lte_fdd_EARFCN");
            this.Columns.Add("lte_fdd_PCI");
            this.Columns.Add("lte_fdd_RSRP");
            this.Columns.Add("lte_fdd_SINR");
            this.Columns.Add("lte_fdd_NCell_EARFCN");
            this.Columns.Add("lte_fdd_NCell_PCI");
            this.Columns.Add("lte_fdd_NCell_RSRP");
            this.Columns.Add("lte_fdd_NCell_SINR");
            this.Columns.Add("lte_fdd_wcdma_SysLAI");
            this.Columns.Add("lte_fdd_wcdma_SysCellID");
            this.Columns.Add("lte_fdd_wcdma_frequency");
            this.Columns.Add("lte_fdd_wcdma_Reference_PSC");
            this.Columns.Add("lte_fdd_wcdma_TotalRSCP");
            this.Columns.Add("lte_fdd_wcdma_TotalEc_Io");
            this.Columns.Add("lte_fdd_wcdma_SNeiFreq");
            this.Columns.Add("lte_fdd_wcdma_SNeiPSC");
            this.Columns.Add("lte_fdd_wcdma_SNeiRSCP");
            this.Columns.Add("lte_fdd_wcdma_SNeiEcIo");
        }
        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                List<Message> messageList = fileMng.Messages;
                List<TimePeriodNBPci> tnfLst = getTnfLst(messageList);

                foreach (TestPoint tp in fileMng.TestPoints)
                {
                    doStatWithTestPoint(tp, tnfLst);
                }
            }
        }

        private List<TimePeriodNBPci> getTnfLst(List<Message> messageList)
        {
            List<TimePeriodNBPci> tnfLst = new List<TimePeriodNBPci>();
            foreach (Message msg in messageList)
            {
                if (msg.ID == (int)EnumLteNBCheckMsg.RRCConnectionRelease)
                {
                    uint carrier = 0, group = 0;
                    MasterCom.RAMS.Model.MessageWithSource msgWithSoure = msg as MasterCom.RAMS.Model.MessageWithSource;
                    MessageDecodeHelper.StartDissect(msgWithSoure.Direction, msgWithSoure.Source, msgWithSoure.Length, msgWithSoure.ID);
                    if (!MessageDecodeHelper.GetSingleUInt("lte-rrc.redirectedCarrierInfo", ref carrier) ||
                        !MessageDecodeHelper.GetSingleUInt("lte-rrc.utra_FDD_r9", ref group))
                    {
                        continue;
                    }
                    if (carrier != (int)ECarrierInfo.utra_FDD || group <= 0) continue;
                    uint[] pciAry = new uint[group];
                    if (MessageDecodeHelper.GetMultiUInt("lte-rrc.physCellId_r9", ref pciAry, (int)group))
                    {
                        TimePeriodNBPci tnf = new TimePeriodNBPci(msg.DateTime, msg.DateTime.AddSeconds(nbCellCheckCondition.TimeSpan), pciAry);
                        tnfLst.Add(tnf);
                    }
                }
            }

            return tnfLst;
        }

        /// <summary>
        /// 对采样点进行处理
        /// </summary>
        private void doStatWithTestPoint(TestPoint tp, List<TimePeriodNBPci> tnfLst)
        {
            float? rsrp = (float?)tp["lte_fdd_RSRP"];

            if (rsrp != null && rsrp >= -141)   //有LTE下的信号强度，则认为是LTE的采样点
            {
                doTestPoint_LTE(tp, (float)rsrp);
            }
            else
            {
                doTestPoint_WCDMA(tp, tnfLst);
            }
        }

        private void doTestPoint_WCDMA(TestPoint tp, List<TimePeriodNBPci> tnfLst)
        {
            if (curLTECell == null)    //找不到当前LTE小区，不进行处理
            {
                return;
            }

            if (!resultDic.ContainsKey(curLTECell.Name))    //该LTE小区必然曾经出现过
            {
                return;
            }

            if ((tp.Time - curLTETime) > nbCellCheckCondition.TimeSpan)  //超出时间范围
            {
                return;
            }

            TimePeriodNBPci tnf;
            getTimePeriodNBPci(tnfLst, tp, out tnf);

            ZTLteNBCellCheckCellItem cellItem;
            cellItem = resultDic[curLTECell.Name];

            #region WCDMA主服小区
            addServerCellInfo(tp, tnf, cellItem);
            #endregion

            #region WCDMA邻区
            addNBCellInfo(tp, tnf, cellItem);
            #endregion
        }

        private void addServerCellInfo(TestPoint tp, TimePeriodNBPci tnf, ZTLteNBCellCheckCellItem cellItem)
        {
            WCell servCell = tp.GetMainCell_LTE_FDD_W();
            if (servCell != null)
            {
                float? rxlev = (float?)tp["lte_fdd_wcdma_TotalRSCP"];
                float? rxqual = null;

                object value = tp["lte_fdd_wcdma_TotalEc_Io"];
                if (value != null)
                {
                    float qual = float.Parse(value.ToString());
                    if (-40 <= qual && qual <= 0)
                    {
                        rxqual = qual;
                    }
                }

                if (rxlev != null && rxlev > -140 && rxlev >= nbCellCheckCondition.RSRP)  //是合法的rxlev值
                {
                    if (cellItem.NBDic.ContainsKey(servCell.Name))
                    {
                        cellItem.NBDic[servCell.Name].MergeData((float)rxlev, rxqual, 0);
                    }
                    else
                    {
                        ZTLteNBCellCheckNBItem nbItem = new ZTLteNBCellCheckNBItem(servCell, (float)rxlev, rxqual, 0, curLTECell);  //主服的index默认为1，排位第一
                        cellItem.NBDic.Add(servCell.Name, nbItem);
                    }
                    nbCellAddMsgPci(cellItem.NBDic[servCell.Name], tnf);
                }
            }
        }

        private void addNBCellInfo(TestPoint tp, TimePeriodNBPci tnf, ZTLteNBCellCheckCellItem cellItem)
        {
            for (int i = 0; i < 6; i++)
            {
                float? nRxlev = (float?)tp["lte_fdd_wcdma_SNeiRSCP", i];

                if (nRxlev == null || nRxlev < nbCellCheckCondition.RSRP)
                {
                    continue;
                }

                WCell nbCell = tp.GetNBCell_LTE_FDD_W(i);

                if (nbCell == null)
                {
                    continue;
                }

                if (cellItem.NBDic.ContainsKey(nbCell.Name))
                {
                    cellItem.NBDic[nbCell.Name].MergeData((float)nRxlev, -255, i + 1);     //i+1:邻区的index默认从1开始
                }
                else
                {
                    ZTLteNBCellCheckNBItem nbItem = new ZTLteNBCellCheckNBItem(nbCell, (float)nRxlev, -255, i + 1, curLTECell);
                    cellItem.NBDic.Add(nbCell.Name, nbItem);
                }
                nbCellAddMsgPci(cellItem.NBDic[nbCell.Name], tnf);
            }
        }

        protected void nbCellAddMsgPci(ZTLteNBCellCheckNBItem nbItem, TimePeriodNBPci tnf)
        {
            if (tnf != null)
            {
                nbItem.AddRangePci(tnf.PciLst);
            }
        }
        protected void getTimePeriodNBPci(List<TimePeriodNBPci> tnfLst, TestPoint tp, out TimePeriodNBPci tnf)
        {
            tnf = null;
            while (tnfLst.Count > 0)
            {
                if (tnfLst[0].IsInPeriod(tp.DateTime))
                {
                    tnf = tnfLst[0];
                    break;
                }
                else if (tnfLst[0].IsPreviously(tp.DateTime))
                {
                    break;
                }
                tnfLst.RemoveAt(0);
            }
        }

        /// <summary>
        /// 对最终结果按照条件进行过滤
        /// </summary>
        /// <returns></returns>
        protected override List<ZTLteNBCellCheckCellItem> filterResultByCondition()
        {
            if (resultDic.Count > 0)
            {
                MainModel.CellManager.GetLTENBCellInfo();
            }

            List<ZTLteNBCellCheckCellItem> resultList = new List<ZTLteNBCellCheckCellItem>();
            int seqNo = 1;  //序号

            foreach (string cellName in resultDic.Keys)
            {
                LTECell servCell = resultDic[cellName].ServCell;

                //按照条件进行过滤
                List<ZTLteNBCellCheckNBItem> nbList = getNbList(cellName, servCell);

                seqNo = addResult(resultList, seqNo, cellName, nbList);
            }

            return resultList;
        }

        private List<ZTLteNBCellCheckNBItem> getNbList(string cellName, LTECell servCell)
        {
            List<ZTLteNBCellCheckNBItem> nbList = new List<ZTLteNBCellCheckNBItem>();
            foreach (ZTLteNBCellCheckNBItem nbItem in resultDic[cellName].NBDic.Values)
            {
                if (nbItem.RSRPCount >= nbCellCheckCondition.SampleCount && nbItem.Distance < nbCellCheckCondition.Distance)
                {
                    nbItem.Status = "漏配";     //初始化为漏配

                    if (nbItem.NBCell_W != null)      //WCDMA邻区
                    {
                        nbItem.SetMsgPciStatus();
#if NBCellCheckByBcch
                        if (servCell.IsInGsmFreq((int)nbItem.NBCell_W.UARFCN))
                        {
                            nbItem.Status = "在配置中";
                        }
#else
                        setWNbStatus(servCell, nbItem);
#endif
                    }
                    else                             //TDD-LTE邻区
                    {
                        setLteNbStatus(servCell, nbItem);
                    }

                    nbList.Add(nbItem);
                }
            }

            return nbList;
        }

        private void setWNbStatus(LTECell servCell, ZTLteNBCellCheckNBItem nbItem)
        {
            foreach (WCell nbCell in servCell.NeighbourWCells)
            {
                if (nbCell.Name == nbItem.NBCell_W.Name)   //名称相同
                {
                    nbItem.Status = "在配置中";
                    break;
                }
            }
        }

        private void setLteNbStatus(LTECell servCell, ZTLteNBCellCheckNBItem nbItem)
        {
            foreach (LTECell nbCell in servCell.NeighbourCells)
            {
                if (nbCell.Name == nbItem.NBCell_LTE.Name)   //名称相同
                {
                    nbItem.Status = "在配置中";
                    break;
                }
            }
        }

        private int addResult(List<ZTLteNBCellCheckCellItem> resultList, int seqNo, string cellName, List<ZTLteNBCellCheckNBItem> nbList)
        {
            if (nbList.Count > 0)
            {
                nbList.Sort(ZTLteNBCellCheckNBItem.GetCompareByScore());    //按照score进行排序
                resultDic[cellName].NBDic = new Dictionary<string, ZTLteNBCellCheckNBItem>();

                foreach (ZTLteNBCellCheckNBItem nbItem in nbList)
                {
                    resultDic[cellName].NBDic.Add(nbItem.CellName, nbItem);
                }

                resultDic[cellName].SN = seqNo++;
                resultList.Add(resultDic[cellName]);
            }

            return seqNo;
        }

        protected override float? getNRsrp(TestPoint tp, int index)
        {
            return (float?)tp["lte_fdd_NCell_RSRP", index];
        }
    }
    public class TimePeriodNBPci : TimePeriodNBFreq
    {
        public List<int> PciLst { get; set; }

        public TimePeriodNBPci(DateTime dtStart, DateTime dtEnd, uint[] pciAry)
        {
            this.DtStart = dtStart;
            this.DtEnd = dtEnd;
            PciLst = new List<int>();
            for (int i = 0; i < pciAry.Length; i++)
            {
                PciLst.Add((int)pciAry[i]);
            }
        }
    }
}
