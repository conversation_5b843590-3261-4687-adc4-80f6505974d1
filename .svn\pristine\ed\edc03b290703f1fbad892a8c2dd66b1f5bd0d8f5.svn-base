﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTWeakCoverWForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.ListViewRoad = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRoadName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDitance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSample = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMaxRxlev = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMinRxlev = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgRxlev = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnC2IMax = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnC2IMin = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnC2IAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitudeMid = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitudeMid = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExport = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewRoad)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // ListViewRoad
            // 
            this.ListViewRoad.AllColumns.Add(this.olvColumnSN);
            this.ListViewRoad.AllColumns.Add(this.olvColumnFileName);
            this.ListViewRoad.AllColumns.Add(this.olvColumnRoadName);
            this.ListViewRoad.AllColumns.Add(this.olvColumnDitance);
            this.ListViewRoad.AllColumns.Add(this.olvColumnSample);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMaxRxlev);
            this.ListViewRoad.AllColumns.Add(this.olvColumnMinRxlev);
            this.ListViewRoad.AllColumns.Add(this.olvColumnAvgRxlev);
            this.ListViewRoad.AllColumns.Add(this.olvColumnC2IMax);
            this.ListViewRoad.AllColumns.Add(this.olvColumnC2IMin);
            this.ListViewRoad.AllColumns.Add(this.olvColumnC2IAvg);
            this.ListViewRoad.AllColumns.Add(this.olvColumnLongitudeMid);
            this.ListViewRoad.AllColumns.Add(this.olvColumnLatitudeMid);
            this.ListViewRoad.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnFileName,
            this.olvColumnRoadName,
            this.olvColumnDitance,
            this.olvColumnSample,
            this.olvColumnMaxRxlev,
            this.olvColumnMinRxlev,
            this.olvColumnAvgRxlev,
            this.olvColumnC2IMax,
            this.olvColumnC2IMin,
            this.olvColumnC2IAvg,
            this.olvColumnLongitudeMid,
            this.olvColumnLatitudeMid});
            this.ListViewRoad.ContextMenuStrip = this.contextMenuStrip1;
            this.ListViewRoad.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewRoad.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ListViewRoad.FullRowSelect = true;
            this.ListViewRoad.GridLines = true;
            this.ListViewRoad.HeaderWordWrap = true;
            this.ListViewRoad.IsNeedShowOverlay = false;
            this.ListViewRoad.Location = new System.Drawing.Point(0, 0);
            this.ListViewRoad.Name = "ListViewRoad";
            this.ListViewRoad.OwnerDraw = true;
            this.ListViewRoad.ShowGroups = false;
            this.ListViewRoad.Size = new System.Drawing.Size(980, 407);
            this.ListViewRoad.TabIndex = 6;
            this.ListViewRoad.UseCompatibleStateImageBehavior = false;
            this.ListViewRoad.View = System.Windows.Forms.View.Details;
            this.ListViewRoad.VirtualMode = true;
            this.ListViewRoad.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.ListViewRoad_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名称";
            this.olvColumnFileName.Width = 200;
            // 
            // olvColumnRoadName
            // 
            this.olvColumnRoadName.HeaderFont = null;
            this.olvColumnRoadName.Text = "道路名称";
            this.olvColumnRoadName.Width = 150;
            // 
            // olvColumnDitance
            // 
            this.olvColumnDitance.HeaderFont = null;
            this.olvColumnDitance.Text = "弱覆盖长度";
            this.olvColumnDitance.Width = 80;
            // 
            // olvColumnSample
            // 
            this.olvColumnSample.HeaderFont = null;
            this.olvColumnSample.Text = "采样点数";
            this.olvColumnSample.Width = 80;
            // 
            // olvColumnMaxRxlev
            // 
            this.olvColumnMaxRxlev.HeaderFont = null;
            this.olvColumnMaxRxlev.Text = "最大场强";
            this.olvColumnMaxRxlev.Width = 80;
            // 
            // olvColumnMinRxlev
            // 
            this.olvColumnMinRxlev.HeaderFont = null;
            this.olvColumnMinRxlev.Text = "最小场强";
            this.olvColumnMinRxlev.Width = 80;
            // 
            // olvColumnAvgRxlev
            // 
            this.olvColumnAvgRxlev.HeaderFont = null;
            this.olvColumnAvgRxlev.Text = "平均场强";
            this.olvColumnAvgRxlev.Width = 80;
            // 
            // olvColumnC2IMax
            // 
            this.olvColumnC2IMax.HeaderFont = null;
            this.olvColumnC2IMax.Text = "最大C/I";
            // 
            // olvColumnC2IMin
            // 
            this.olvColumnC2IMin.HeaderFont = null;
            this.olvColumnC2IMin.Text = "最小C/I";
            // 
            // olvColumnC2IAvg
            // 
            this.olvColumnC2IAvg.HeaderFont = null;
            this.olvColumnC2IAvg.Text = "平均C/I";
            // 
            // olvColumnLongitudeMid
            // 
            this.olvColumnLongitudeMid.HeaderFont = null;
            this.olvColumnLongitudeMid.Text = "中心经度";
            this.olvColumnLongitudeMid.Width = 80;
            // 
            // olvColumnLatitudeMid
            // 
            this.olvColumnLatitudeMid.HeaderFont = null;
            this.olvColumnLatitudeMid.Text = "中心纬度";
            this.olvColumnLatitudeMid.Width = 80;
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExport});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(125, 26);
            // 
            // ToolStripMenuItemExport
            // 
            this.ToolStripMenuItemExport.Name = "ToolStripMenuItemExport";
            this.ToolStripMenuItemExport.Size = new System.Drawing.Size(124, 22);
            this.ToolStripMenuItemExport.Text = "导出列表";
            this.ToolStripMenuItemExport.Click += new System.EventHandler(this.ToolStripMenuItemExport_Click);
            // 
            // ZTWeakCoverWForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(980, 407);
            this.Controls.Add(this.ListViewRoad);
            this.Name = "ZTWeakCoverWForm";
            this.Text = "弱覆盖分析";
            ((System.ComponentModel.ISupportInitialize)(this.ListViewRoad)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView ListViewRoad;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnRoadName;
        private BrightIdeasSoftware.OLVColumn olvColumnDitance;
        private BrightIdeasSoftware.OLVColumn olvColumnSample;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxRxlev;
        private BrightIdeasSoftware.OLVColumn olvColumnMinRxlev;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgRxlev;
        private BrightIdeasSoftware.OLVColumn olvColumnC2IMax;
        private BrightIdeasSoftware.OLVColumn olvColumnC2IMin;
        private BrightIdeasSoftware.OLVColumn olvColumnC2IAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitudeMid;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitudeMid;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExport;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;

    }
}