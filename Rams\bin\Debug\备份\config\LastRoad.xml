<?xml version="1.0"?>
<Configs>
  <Config name="LastRoadCfg">
    <Item name="Reports" typeName="IList">
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">LTE弱覆盖且质差</Item>
        <Item typeName="String" key="GISDisplaySerialName">LTE_TDD:RSRP</Item>
        <Item typeName="IDictionary" key="Condition">
          <Item typeName="Double" key="TwoTestPointMaxDis">50</Item>
          <Item typeName="Double" key="MinLastDistance">100</Item>
          <Item typeName="String" key="LogicalType">或</Item>
          <Item typeName="IList" key="Details">
            <Item typeName="IDictionary">
              <Item typeName="String" key="SysName">LTE_TDD</Item>
              <Item typeName="String" key="ParamName">RSRP</Item>
              <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
              <Item typeName="Double" key="MinValue">-141</Item>
              <Item typeName="Double" key="MaxValue">-110</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="SysName">LTE_TDD</Item>
              <Item typeName="String" key="ParamName">SINR</Item>
              <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
              <Item typeName="Double" key="MinValue">-50</Item>
              <Item typeName="Double" key="MaxValue">0</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IList" key="DisplayColumns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">RSRP _平均值</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="String" key="SummaryValueType">Average</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">SINR _平均值</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">SINR</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="String" key="SummaryValueType">Average</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">RSRP _最大值</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="String" key="SummaryValueType">Max</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">SINR _最大值</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">SINR</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="String" key="SummaryValueType">Max</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">RSRP _最小值</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="String" key="SummaryValueType">Min</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">SINR _最小值</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">SINR</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="String" key="SummaryValueType">Min</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">RSRQ _平均值</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">RSRQ</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="String" key="SummaryValueType">Average</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">联通3G弱覆盖路段</Item>
        <Item typeName="String" key="GISDisplaySerialName">WCDMA:TotalRSCP</Item>
        <Item typeName="IDictionary" key="Condition">
          <Item typeName="Double" key="TwoTestPointMaxDis">50</Item>
          <Item typeName="Double" key="MinLastDistance">50</Item>
          <Item typeName="String" key="LogicalType">与</Item>
          <Item typeName="IList" key="Details">
            <Item typeName="IDictionary">
              <Item typeName="String" key="SysName">WCDMA</Item>
              <Item typeName="String" key="ParamName">TotalRSCP</Item>
              <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
              <Item typeName="Double" key="MinValue">-140</Item>
              <Item typeName="Double" key="MaxValue">-85</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IList" key="DisplayColumns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">TotalRSCP _平均值</Item>
            <Item typeName="String" key="SysName">WCDMA</Item>
            <Item typeName="String" key="ParamName">TotalRSCP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="String" key="SummaryValueType">Average</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">TotalRSCP _最小值</Item>
            <Item typeName="String" key="SysName">WCDMA</Item>
            <Item typeName="String" key="ParamName">TotalRSCP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="String" key="SummaryValueType">Min</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">TotalRSCP _最大值</Item>
            <Item typeName="String" key="SysName">WCDMA</Item>
            <Item typeName="String" key="ParamName">TotalRSCP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="String" key="SummaryValueType">Max</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">联通4G弱覆盖路段</Item>
        <Item typeName="String" key="GISDisplaySerialName">LTE_TDD:RSRP</Item>
        <Item typeName="IDictionary" key="Condition">
          <Item typeName="Double" key="TwoTestPointMaxDis">50</Item>
          <Item typeName="Double" key="MinLastDistance">50</Item>
          <Item typeName="String" key="LogicalType">与</Item>
          <Item typeName="IList" key="Details">
            <Item typeName="IDictionary">
              <Item typeName="String" key="SysName">LTE_TDD</Item>
              <Item typeName="String" key="ParamName">RSRP</Item>
              <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
              <Item typeName="Double" key="MinValue">-141</Item>
              <Item typeName="Double" key="MaxValue">-110</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IList" key="DisplayColumns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">RSRP _平均值</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="String" key="SummaryValueType">Average</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">RSRP _最小值</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="String" key="SummaryValueType">Min</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">RSRP _最大值</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="String" key="SummaryValueType">Max</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">电信2G弱覆盖路段</Item>
        <Item typeName="String" key="GISDisplaySerialName">CDMA:RxAGC</Item>
        <Item typeName="IDictionary" key="Condition">
          <Item typeName="Double" key="TwoTestPointMaxDis">50</Item>
          <Item typeName="Double" key="MinLastDistance">50</Item>
          <Item typeName="String" key="LogicalType">与</Item>
          <Item typeName="IList" key="Details">
            <Item typeName="IDictionary">
              <Item typeName="String" key="SysName">CDMA</Item>
              <Item typeName="String" key="ParamName">RxAGC</Item>
              <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
              <Item typeName="Double" key="MinValue">-140</Item>
              <Item typeName="Double" key="MaxValue">-85</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IList" key="DisplayColumns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">RxAGC _最小值</Item>
            <Item typeName="String" key="SysName">CDMA</Item>
            <Item typeName="String" key="ParamName">RxAGC</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="String" key="SummaryValueType">Min</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">RxAGC _平均值</Item>
            <Item typeName="String" key="SysName">CDMA</Item>
            <Item typeName="String" key="ParamName">RxAGC</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="String" key="SummaryValueType">Average</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">RxAGC _最大值</Item>
            <Item typeName="String" key="SysName">CDMA</Item>
            <Item typeName="String" key="ParamName">RxAGC</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="String" key="SummaryValueType">Max</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">电信3G弱覆盖路段</Item>
        <Item typeName="String" key="GISDisplaySerialName">CDMA:evdo_RxAGC0</Item>
        <Item typeName="IDictionary" key="Condition">
          <Item typeName="Double" key="TwoTestPointMaxDis">50</Item>
          <Item typeName="Double" key="MinLastDistance">50</Item>
          <Item typeName="String" key="LogicalType">与</Item>
          <Item typeName="IList" key="Details">
            <Item typeName="IDictionary">
              <Item typeName="String" key="SysName">CDMA</Item>
              <Item typeName="String" key="ParamName">evdo_RxAGC0</Item>
              <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
              <Item typeName="Double" key="MinValue">-140</Item>
              <Item typeName="Double" key="MaxValue">-85</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IList" key="DisplayColumns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">evdo_RxAGC0 _平均值</Item>
            <Item typeName="String" key="SysName">CDMA</Item>
            <Item typeName="String" key="ParamName">evdo_RxAGC0</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="String" key="SummaryValueType">Average</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">evdo_RxAGC0 _最小值</Item>
            <Item typeName="String" key="SysName">CDMA</Item>
            <Item typeName="String" key="ParamName">evdo_RxAGC0</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="String" key="SummaryValueType">Min</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">evdo_RxAGC0 _最大值</Item>
            <Item typeName="String" key="SysName">CDMA</Item>
            <Item typeName="String" key="ParamName">evdo_RxAGC0</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="String" key="SummaryValueType">Max</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">电信4G弱覆盖路段</Item>
        <Item typeName="String" key="GISDisplaySerialName">LTE_TDD:RSRP</Item>
        <Item typeName="IDictionary" key="Condition">
          <Item typeName="Double" key="TwoTestPointMaxDis">50</Item>
          <Item typeName="Double" key="MinLastDistance">50</Item>
          <Item typeName="String" key="LogicalType">与</Item>
          <Item typeName="IList" key="Details">
            <Item typeName="IDictionary">
              <Item typeName="String" key="SysName">LTE_TDD</Item>
              <Item typeName="String" key="ParamName">RSRP</Item>
              <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
              <Item typeName="Double" key="MinValue">-141</Item>
              <Item typeName="Double" key="MaxValue">-110</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IList" key="DisplayColumns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">RSRP _平均值</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="String" key="SummaryValueType">Average</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">RSRP _最小值</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="String" key="SummaryValueType">Min</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">RSRP _最大值</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="String" key="SummaryValueType">Max</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">LTE扫频低SINR路段</Item>
        <Item typeName="String" key="GISDisplaySerialName">LTE_SCAN:TopN_CELL_Specific_RSSINR</Item>
        <Item typeName="IDictionary" key="Condition">
          <Item typeName="Double" key="TwoTestPointMaxDis">50</Item>
          <Item typeName="Double" key="MinLastDistance">20</Item>
          <Item typeName="String" key="LogicalType">或</Item>
          <Item typeName="IList" key="Details">
            <Item typeName="IDictionary">
              <Item typeName="String" key="SysName">LTE_SCAN</Item>
              <Item typeName="String" key="ParamName">TopN_CELL_Specific_RSSINR</Item>
              <Item typeName="Int32" key="ParamArrayIndex">0</Item>
              <Item typeName="Double" key="MinValue">-50</Item>
              <Item typeName="Double" key="MaxValue">50</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IList" key="DisplayColumns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">TopN_CELL_Specific_RSSINR[0] _平均值</Item>
            <Item typeName="String" key="SysName">LTE_SCAN</Item>
            <Item typeName="String" key="ParamName">TopN_CELL_Specific_RSSINR</Item>
            <Item typeName="Int32" key="ParamArrayIndex">0</Item>
            <Item typeName="String" key="SummaryValueType">Average</Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>