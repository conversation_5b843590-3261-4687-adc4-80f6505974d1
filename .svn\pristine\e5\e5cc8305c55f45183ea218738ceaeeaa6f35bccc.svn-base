﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using System.Windows.Forms;
using MasterCom.RAMS.ZTFunc.VoLTEBlockCallCause;
using Message = MasterCom.RAMS.Model.Message;

namespace MasterCom.RAMS.ZTFunc
{
    public class VoLTEBlockCallCauseByFile : VoLTEBlockCallCauseQuery
    {

        private static VoLTEBlockCallCauseByFile instance = null;
        public static new VoLTEBlockCallCauseByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VoLTEBlockCallCauseByFile();
                    }
                }
            }
            return instance;
        }

        protected VoLTEBlockCallCauseByFile()
            : base()
        {
        }

        public override string Name
        {
            get
            {
                return "VoLTE未接通原因分析(按文件)";
            }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }

    public class VoLTEBlockCallCauseByFile_FDD : VoLTEBlockCallCauseQuery_FDD
    {
        private static VoLTEBlockCallCauseByFile_FDD instance = null;
        public static new VoLTEBlockCallCauseByFile_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VoLTEBlockCallCauseByFile_FDD();
                    }
                }
            }
            return instance;
        }
        protected VoLTEBlockCallCauseByFile_FDD()
            : base()
        {

        }
        public override string Name
        {
            get
            {
                return "VoLTE_FDD未接通原因分析(按文件)";
            }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }
}
