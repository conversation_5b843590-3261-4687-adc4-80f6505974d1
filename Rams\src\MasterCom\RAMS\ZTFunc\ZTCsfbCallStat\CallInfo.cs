﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTCsfbCallStat
{
    public class CallInfo
    {
        public CallInfo(string fileName)
        {
            this.FileName = fileName;
            CallResultDesc = "Fail";
            MsgMtPagingList = new List<Message>();
        }
        public string FileName { get; set; }
        public List<Event> Events { get; set; } = new List<Event>();

        readonly List<int> successEvt = new List<int>() { 881, 889, 1024, 1025, 1044, 1045, 3078, 3079
               , 3024, 3025, 3054, 3055 };
        readonly List<int> failEvt = new List<int>() { 882, 890, 1026, 1027, 1028, 1029, 1046, 1047, 1048, 1049
               , 3080, 3081, 3026, 3027, 3028, 3029, 3046, 3047, 3048, 3049 };

        /// <summary>
        /// 添加事件
        /// </summary>
        /// <param name="evt"></param>
        /// <returns>通话是否已有结果（含成功、失败）</returns>
        public bool AddEvent(Event evt)
        {
            Events.Add(evt);
            if (evt.ID == 877 || evt.ID == 885 || evt.ID == 3070 || evt.ID == 3071)
            {//mo/mt csfb request
                EvtCsfbCallRequest = evt;
            }
            else if (evt.ID == 878 || evt.ID == 886 || evt.ID == 3072 || evt.ID == 3073)
            {
                EvtRelease = evt;
            }
            else if (evt.ID == 1001 || evt.ID == 1002 || evt.ID == 3001 || evt.ID == 3002)
            {//csfb call attempt
                EvtCallAttempt = evt;
                PreCallNetType = "CSFB";
            }
            else if (evt.ID == 1021 || evt.ID == 1022 || evt.ID == 3021 || evt.ID == 3022)
            {//mo/mt gsm mo attempt
                EvtCallAttempt = evt;
                PreCallNetType = "GSM";
            }
            else if (evt.ID == 1041 || evt.ID == 1042)
            {//mo/mt td call attempt
                EvtCallAttempt = evt;
                PreCallNetType = "TD";
            }
            else if (evt.ID == 3041 || evt.ID == 3042)
            {//mo/mt td call attempt
                EvtCallAttempt = evt;
                PreCallNetType = "WCDMA";
            }
            else if (successEvt.Contains(evt.ID))
            {/*MO/mt CSFB Success          
              *GSM MO/mt Call Established  
              *TD MO/mt Call Established   
              */
                CallResultDesc = "Success";
                return true;
            }
            else if (failEvt.Contains(evt.ID))
            {//MO CSFB Failure
                //GSM MO Drop Call    GSM MO Block Call
                //TD MO Drop Call     TD MO Block Call
                CallResultDesc = "Fail";
                return true;
            }

            return false;
        }

        public Event EvtCsfbCallRequest { get; set; }
        public Event EvtRelease { get; set; }
        public Event EvtCallAttempt { get; set; }

        public float? Rsrp { get; set; }
        public float? RxLev_Rscp { get; set; }
        public List<Message> Messages { get; set; } = new List<Message>();

        public Message MsgServiceNotification { get; set; }
        public Message MsgExtendedServicerequest { get; set; }
        public Message MsgAlerting { get; set; }
        public Message MsgRrcConnectionRelease { get; set; }
        public Message MsgFirstSysInfo { get; set; }
        public Message MsgCmServiceRequest { get; set; }
        public Message MsgCmServiceAccept { get; set; }
        public Message MsgAuthenticationRequest { get; set; }
        public Message MsgAuthenticationResponse { get; set; }
        public Message MsgSetup { get; set; }
        public Message MsgCallProceeding { get; set; }
        public Message MsgRrAssignmentCommand { get; set; }
        public Message MsgRrAssignmentComplete { get; set; }
        public Message MsgChannelModeModify { get; set; }
        public Message MsgChannelModeModifyAcknowledge { get; set; }

        public Message MsgMtPaging { get; set; }
        public List<Message> MsgMtPagingList { get; set; }
        public Message MsgMtPagingResponse { get; set; }
        public Message MsgMtCallConfirmed { get; set; }
        public Message MsgMtAuthenticationRequest { get; set; }
        public Message MsgMtAuthenticationResponse { get; set; }

        public string CallNetType { get; set; }
        public string CallResultDesc { get; set; }

        public int? Tac { get; set; }

        public int? Eci { get; set; }

        public int? Lac { get; set; }

        public int? Ci { get; set; }

        public string PreCallNetType { get; set; }

        public int? Enodebid
        {
            get
            {
                if (Tac != null && Eci != null && EvtCsfbCallRequest != null)
                {
                    LTECell lteCell = CellManager.GetInstance().GetLTECell(EvtCsfbCallRequest.DateTime, (int)Tac, (int)Eci);
                    if (lteCell != null)
                    {
                        return lteCell.BelongBTS.BTSID;
                    }
                }
                return null;
            }
        }
        
        
        public int? Cellid
        {
            get
            {
                if (Tac != null && Eci != null && EvtCsfbCallRequest != null)
                {
                    LTECell lteCell = CellManager.GetInstance().GetLTECell(EvtCsfbCallRequest.DateTime, (int)Tac, (int)Eci);
                    if (lteCell != null)
                    {
                        return lteCell.CellID;
                    }
                }
                return null;
            }
        }

        public int? ExRequest2Alerting
        {
            get
            {
                if (MsgExtendedServicerequest != null && MsgAlerting != null)
                {
                    return (int)(MsgAlerting.DateTime - MsgExtendedServicerequest.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }

        public int? ExRequest2Release
        {
            get
            {
                if (MsgExtendedServicerequest != null && MsgRrcConnectionRelease != null)
                {
                    return (int)(MsgRrcConnectionRelease.DateTime - MsgExtendedServicerequest.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }

        public int? Release2SysInfo
        {
            get
            {
                if (MsgRrcConnectionRelease != null && MsgFirstSysInfo != null)
                {
                    return (int)(MsgFirstSysInfo.DateTime - MsgRrcConnectionRelease.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }

        public int? SysInfo2CmRequest
        {
            get
            {
                if (MsgCmServiceRequest != null && MsgFirstSysInfo != null)
                {
                    return (int)(MsgCmServiceRequest.DateTime - MsgFirstSysInfo.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }

        public int? CmRequest2CmAccept
        {
            get
            {
                if (MsgCmServiceAccept != null && MsgCmServiceRequest != null)
                {
                    return (int)(MsgCmServiceAccept.DateTime - MsgCmServiceRequest.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }

        public int? CmRequest2AuRequest
        {
            get
            {
                if (MsgCmServiceRequest != null && MsgAuthenticationRequest != null)
                {
                    return (int)(MsgAuthenticationRequest.DateTime - MsgCmServiceRequest.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }

        public int? AuRequest2AuResponse
        {
            get
            {
                if (MsgAuthenticationRequest != null && MsgAuthenticationResponse != null)
                {
                    return (int)(MsgAuthenticationResponse.DateTime - MsgAuthenticationRequest.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }

        public int? AuResponse2CmAccept
        {
            get
            {
                if (MsgAuthenticationResponse != null && MsgCmServiceAccept != null)
                {
                    return (int)(MsgCmServiceAccept.DateTime - MsgAuthenticationResponse.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }

        public int? CmAccept2Setup
        {
            get
            {
                if (MsgSetup != null && MsgCmServiceAccept != null)
                {
                    return (int)(MsgSetup.DateTime - MsgCmServiceAccept.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }
        public int? CmRequest2Setup
        {
            get
            {
                if (MsgSetup != null && MsgCmServiceRequest != null)
                {
                    return (int)(MsgSetup.DateTime - MsgCmServiceRequest.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }

        public int? Setup2Proc
        {
            get
            {
                if (MsgSetup != null && MsgCallProceeding != null)
                {
                    return (int)(MsgCallProceeding.DateTime - MsgSetup.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }

        public int? Proc2AssignmentCmd
        {
            get
            {
                if (MsgRrAssignmentCommand != null && MsgCallProceeding != null)
                {
                    return (int)(MsgRrAssignmentCommand.DateTime - MsgCallProceeding.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }

        public int? AssigmentCmd2Complete
        {
            get
            {
                if (MsgRrAssignmentComplete != null && MsgRrAssignmentCommand != null)
                {
                    return (int)(MsgRrAssignmentComplete.DateTime - MsgRrAssignmentCommand.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }

        public int? Complete2Alerting
        {
            get
            {
                if (this.MsgAlerting != null && MsgRrAssignmentComplete != null)
                {
                    return (int)(MsgAlerting.DateTime - MsgRrAssignmentComplete.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }
        public int? CallProceed2ChannelModify
        {
            get
            {
                if (MsgChannelModeModify != null && this.MsgCallProceeding != null)
                {
                    return (int)(MsgChannelModeModify.DateTime - MsgCallProceeding.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }
        public int? ChannelModify2ChannelAcknowledge
        {
            get
            {
                if (this.MsgChannelModeModifyAcknowledge != null && MsgChannelModeModify != null)
                {
                    return (int)(MsgChannelModeModifyAcknowledge.DateTime - MsgChannelModeModify.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }
        public int? ChannelAcknowledge2Alerting
        {
            get
            {
                if (this.MsgAlerting != null && MsgChannelModeModifyAcknowledge != null)
                {
                    return (int)(MsgAlerting.DateTime - MsgChannelModeModifyAcknowledge.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }
        public int? CallProceeding2Alerting
        {
            get
            {
                if (this.MsgAlerting != null && MsgCallProceeding != null)
                {
                    return (int)(MsgAlerting.DateTime - MsgCallProceeding.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }
        public int? Paging2Alerting
        {
            get
            {
                if (MsgAlerting != null)
                {
                    if (MsgServiceNotification != null)
                    {
                        return (int)(MsgAlerting.DateTime - MsgServiceNotification.DateTime).TotalMilliseconds;
                    }
                    else if (MsgMtPaging != null)
                    {
                        return (int)(MsgAlerting.DateTime - MsgMtPaging.DateTime).TotalMilliseconds;
                    }
                }
                return null;
            }
        }

        public int? Paging2ExRequest
        {
            get
            {
                if (MsgExtendedServicerequest != null)
                {
                    if (MsgServiceNotification != null)
                    {
                        return (int)(MsgExtendedServicerequest.DateTime - MsgServiceNotification.DateTime).TotalMilliseconds;
                    }
                    else if (MsgMtPaging != null)
                    {
                        return (int)(MsgExtendedServicerequest.DateTime - MsgMtPaging.DateTime).TotalMilliseconds;
                    }
                }
                return null;
            }
        }

        public int? SysInfo2Response
        {
            get
            {
                if (MsgMtPagingResponse != null && MsgFirstSysInfo != null)
                {
                    return (int)(MsgMtPagingResponse.DateTime - MsgFirstSysInfo.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }

        public int? Response2Setup
        {
            get
            {
                if (MsgSetup != null && MsgMtPagingResponse != null)
                {
                    return (int)(MsgSetup.DateTime - MsgMtPagingResponse.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }

        public int? PResponse2AuRequest
        {
            get
            {
                if (MsgMtPagingResponse != null && MsgMtAuthenticationRequest != null)
                {
                    return (int)(MsgMtAuthenticationRequest.DateTime - MsgMtPagingResponse.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }

        public int? MtAuRequest2AuResponse
        {
            get
            {
                if (MsgMtAuthenticationRequest != null && MsgMtAuthenticationResponse != null)
                {
                    return (int)(MsgMtAuthenticationResponse.DateTime - MsgMtAuthenticationRequest.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }

        public int? AuResponse2Setup
        {
            get
            {
                if (MsgMtAuthenticationResponse != null && MsgSetup != null)
                {
                    return (int)(MsgSetup.DateTime - MsgMtAuthenticationResponse.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }

        public int? Setup2ClConfirmed
        {
            get
            {
                if (MsgSetup != null && MsgMtCallConfirmed != null)
                {
                    return (int)(MsgMtCallConfirmed.DateTime - MsgSetup.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }

        public int? ClConfirmed2AsCommand
        {
            get
            {
                if (MsgMtCallConfirmed != null && MsgRrAssignmentCommand != null)
                {
                    return (int)(MsgRrAssignmentCommand.DateTime - MsgMtCallConfirmed.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }

        public int? AsCommand2AsComplete
        {
            get
            {
                if (MsgRrAssignmentCommand != null && MsgRrAssignmentComplete != null)
                {
                    return (int)(MsgRrAssignmentComplete.DateTime - MsgRrAssignmentCommand.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }

        public int? Response2Confirmed
        {
            get
            {
                if (MsgMtCallConfirmed != null && MsgMtPagingResponse != null)
                {
                    return (int)(MsgMtCallConfirmed.DateTime - MsgMtPagingResponse.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }

        public int? Response2AssigmentCmd
        {
            get
            {
                if (MsgRrAssignmentCommand != null && MsgMtPagingResponse != null)
                {
                    return (int)(MsgRrAssignmentCommand.DateTime - MsgMtPagingResponse.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }

        public int? Response2Complete
        {
            get
            {
                if (MsgRrAssignmentComplete != null && MsgMtPagingResponse != null)
                {
                    return (int)(MsgRrAssignmentComplete.DateTime - MsgMtPagingResponse.DateTime).TotalMilliseconds;
                }
                return null;
            }
        }

        public string BackResult {
            get { return EvtCallAttempt == null ? "Fail" : "Success"; }
        }
    }
}
