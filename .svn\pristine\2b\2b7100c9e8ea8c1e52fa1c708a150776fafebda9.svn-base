﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTWeakMOSLastForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.objectListViewEvent = new BrightIdeasSoftware.ObjectListView();
            this.olvColumnSN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnEventName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnFirstSampleTime = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnFirstSampleLAC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnFirstSampleCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnFirstSampleLongitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnFirstSampleLatitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnFirstCell = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLastSampleTime = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLastSampleLAC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLastSampleCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLastSampleLongitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLastSampleLatitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLastCell = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSampleNum = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMOSMaxValue = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMOSMinValue = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMOSMeanValue = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripMenuItemExport2Excel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStrip = new System.Windows.Forms.ToolStrip();
            this.toolStripLabelSampleNum = new System.Windows.Forms.ToolStripLabel();
            this.toolStripLabel1 = new System.Windows.Forms.ToolStripLabel();
            this.olvColumnFileName = new BrightIdeasSoftware.OLVColumn();
            ((System.ComponentModel.ISupportInitialize)(this.objectListViewEvent)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.toolStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // objectListViewEvent
            // 
            this.objectListViewEvent.AllColumns.Add(this.olvColumnSN);
            this.objectListViewEvent.AllColumns.Add(this.olvColumnEventName);
            this.objectListViewEvent.AllColumns.Add(this.olvColumnFirstSampleTime);
            this.objectListViewEvent.AllColumns.Add(this.olvColumnFirstSampleLAC);
            this.objectListViewEvent.AllColumns.Add(this.olvColumnFirstSampleCI);
            this.objectListViewEvent.AllColumns.Add(this.olvColumnFirstSampleLongitude);
            this.objectListViewEvent.AllColumns.Add(this.olvColumnFirstSampleLatitude);
            this.objectListViewEvent.AllColumns.Add(this.olvColumnFirstCell);
            this.objectListViewEvent.AllColumns.Add(this.olvColumnLastSampleTime);
            this.objectListViewEvent.AllColumns.Add(this.olvColumnLastSampleLAC);
            this.objectListViewEvent.AllColumns.Add(this.olvColumnLastSampleCI);
            this.objectListViewEvent.AllColumns.Add(this.olvColumnLastSampleLongitude);
            this.objectListViewEvent.AllColumns.Add(this.olvColumnLastSampleLatitude);
            this.objectListViewEvent.AllColumns.Add(this.olvColumnLastCell);
            this.objectListViewEvent.AllColumns.Add(this.olvColumnSampleNum);
            this.objectListViewEvent.AllColumns.Add(this.olvColumnMOSMaxValue);
            this.objectListViewEvent.AllColumns.Add(this.olvColumnMOSMinValue);
            this.objectListViewEvent.AllColumns.Add(this.olvColumnMOSMeanValue);
            this.objectListViewEvent.AllColumns.Add(this.olvColumnFileName);
            this.objectListViewEvent.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.objectListViewEvent.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnEventName,
            this.olvColumnFirstSampleTime,
            this.olvColumnFirstSampleLAC,
            this.olvColumnFirstSampleCI,
            this.olvColumnFirstSampleLongitude,
            this.olvColumnFirstSampleLatitude,
            this.olvColumnFirstCell,
            this.olvColumnLastSampleTime,
            this.olvColumnLastSampleLAC,
            this.olvColumnLastSampleCI,
            this.olvColumnLastSampleLongitude,
            this.olvColumnLastSampleLatitude,
            this.olvColumnLastCell,
            this.olvColumnSampleNum,
            this.olvColumnMOSMaxValue,
            this.olvColumnMOSMinValue,
            this.olvColumnMOSMeanValue,
            this.olvColumnFileName});
            this.objectListViewEvent.ContextMenuStrip = this.contextMenuStrip;
            this.objectListViewEvent.Cursor = System.Windows.Forms.Cursors.Default;
            this.objectListViewEvent.FullRowSelect = true;
            this.objectListViewEvent.GridLines = true;
            this.objectListViewEvent.Location = new System.Drawing.Point(0, 28);
            this.objectListViewEvent.Name = "objectListViewEvent";
            this.objectListViewEvent.ShowGroups = false;
            this.objectListViewEvent.Size = new System.Drawing.Size(980, 370);
            this.objectListViewEvent.TabIndex = 0;
            this.objectListViewEvent.UseCompatibleStateImageBehavior = false;
            this.objectListViewEvent.View = System.Windows.Forms.View.Details;
            this.objectListViewEvent.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.objectListViewEvent_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 50;
            // 
            // olvColumnEventName
            // 
            this.olvColumnEventName.HeaderFont = null;
            this.olvColumnEventName.Text = "事件名称";
            this.olvColumnEventName.Width = 108;
            // 
            // olvColumnFirstSampleTime
            // 
            this.olvColumnFirstSampleTime.HeaderFont = null;
            this.olvColumnFirstSampleTime.Text = "第一个采样点时间";
            this.olvColumnFirstSampleTime.Width = 130;
            // 
            // olvColumnFirstSampleLAC
            // 
            this.olvColumnFirstSampleLAC.HeaderFont = null;
            this.olvColumnFirstSampleLAC.Text = "第一个采样点LAC";
            this.olvColumnFirstSampleLAC.Width = 123;
            // 
            // olvColumnFirstSampleCI
            // 
            this.olvColumnFirstSampleCI.HeaderFont = null;
            this.olvColumnFirstSampleCI.Text = "第一个采样点CI";
            this.olvColumnFirstSampleCI.Width = 108;
            // 
            // olvColumnFirstSampleLongitude
            // 
            this.olvColumnFirstSampleLongitude.HeaderFont = null;
            this.olvColumnFirstSampleLongitude.Text = "第一个采样点经度";
            this.olvColumnFirstSampleLongitude.Width = 116;
            // 
            // olvColumnFirstSampleLatitude
            // 
            this.olvColumnFirstSampleLatitude.HeaderFont = null;
            this.olvColumnFirstSampleLatitude.Text = "第一个采样点纬度";
            this.olvColumnFirstSampleLatitude.Width = 113;
            // 
            // olvColumnFirstCell
            // 
            this.olvColumnFirstCell.HeaderFont = null;
            this.olvColumnFirstCell.Text = "第一个采样点小区名称";
            this.olvColumnFirstCell.Width = 160;
            // 
            // olvColumnLastSampleTime
            // 
            this.olvColumnLastSampleTime.HeaderFont = null;
            this.olvColumnLastSampleTime.Text = "最后一个采样点时间";
            this.olvColumnLastSampleTime.Width = 132;
            // 
            // olvColumnLastSampleLAC
            // 
            this.olvColumnLastSampleLAC.HeaderFont = null;
            this.olvColumnLastSampleLAC.Text = "最后一个采样点LAC";
            this.olvColumnLastSampleLAC.Width = 130;
            // 
            // olvColumnLastSampleCI
            // 
            this.olvColumnLastSampleCI.HeaderFont = null;
            this.olvColumnLastSampleCI.Text = "最后一个采样点CI";
            this.olvColumnLastSampleCI.Width = 124;
            // 
            // olvColumnLastSampleLongitude
            // 
            this.olvColumnLastSampleLongitude.HeaderFont = null;
            this.olvColumnLastSampleLongitude.Text = "最后一个采样点经度";
            this.olvColumnLastSampleLongitude.Width = 131;
            // 
            // olvColumnLastSampleLatitude
            // 
            this.olvColumnLastSampleLatitude.HeaderFont = null;
            this.olvColumnLastSampleLatitude.Text = "最后一个采样点纬度";
            this.olvColumnLastSampleLatitude.Width = 133;
            // 
            // olvColumnLastCell
            // 
            this.olvColumnLastCell.HeaderFont = null;
            this.olvColumnLastCell.Text = "最后一个采样点小区名称";
            this.olvColumnLastCell.Width = 160;
            // 
            // olvColumnSampleNum
            // 
            this.olvColumnSampleNum.HeaderFont = null;
            this.olvColumnSampleNum.Text = "MOS差的采样点数";
            this.olvColumnSampleNum.Width = 143;
            // 
            // olvColumnMOSMaxValue
            // 
            this.olvColumnMOSMaxValue.HeaderFont = null;
            this.olvColumnMOSMaxValue.Text = "MOS差的MOS最大值";
            this.olvColumnMOSMaxValue.Width = 143;
            // 
            // olvColumnMOSMinValue
            // 
            this.olvColumnMOSMinValue.HeaderFont = null;
            this.olvColumnMOSMinValue.Text = "MOS差的MOS最小值";
            this.olvColumnMOSMinValue.Width = 136;
            // 
            // olvColumnMOSMeanValue
            // 
            this.olvColumnMOSMeanValue.HeaderFont = null;
            this.olvColumnMOSMeanValue.Text = "MOS差的MOS均值";
            this.olvColumnMOSMeanValue.Width = 136;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItemExport2Excel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(151, 26);
            // 
            // toolStripMenuItemExport2Excel
            // 
            this.toolStripMenuItemExport2Excel.Name = "toolStripMenuItemExport2Excel";
            this.toolStripMenuItemExport2Excel.Size = new System.Drawing.Size(150, 22);
            this.toolStripMenuItemExport2Excel.Text = "导出到Excel...";
            this.toolStripMenuItemExport2Excel.Click += new System.EventHandler(this.toolStripMenuItemExport2Excel_Click);
            // 
            // toolStrip
            // 
            this.toolStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripLabelSampleNum,
            this.toolStripLabel1});
            this.toolStrip.Location = new System.Drawing.Point(0, 0);
            this.toolStrip.Name = "toolStrip";
            this.toolStrip.Size = new System.Drawing.Size(981, 25);
            this.toolStrip.TabIndex = 1;
            this.toolStrip.Text = "toolStrip1";
            // 
            // toolStripLabelSampleNum
            // 
            this.toolStripLabelSampleNum.Name = "toolStripLabelSampleNum";
            this.toolStripLabelSampleNum.Size = new System.Drawing.Size(95, 22);
            this.toolStripLabelSampleNum.Text = "采样点数门限值:";
            // 
            // toolStripLabel1
            // 
            this.toolStripLabel1.Name = "toolStripLabel1";
            this.toolStripLabel1.Size = new System.Drawing.Size(92, 22);
            this.toolStripLabel1.Text = "<=采样点数<=";
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名";
            // 
            // ZTWeakMOSLastForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(981, 398);
            this.Controls.Add(this.toolStrip);
            this.Controls.Add(this.objectListViewEvent);
            this.Name = "ZTWeakMOSLastForm";
            this.ShowIcon = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "MOS差事件列表";
            ((System.ComponentModel.ISupportInitialize)(this.objectListViewEvent)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.toolStrip.ResumeLayout(false);
            this.toolStrip.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private BrightIdeasSoftware.ObjectListView objectListViewEvent;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnFirstSampleTime;
        private BrightIdeasSoftware.OLVColumn olvColumnFirstSampleLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnFirstSampleCI;
        private BrightIdeasSoftware.OLVColumn olvColumnFirstSampleLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnFirstSampleLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLastSampleTime;
        private BrightIdeasSoftware.OLVColumn olvColumnLastSampleLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnLastSampleCI;
        private BrightIdeasSoftware.OLVColumn olvColumnLastSampleLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLastSampleLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnSampleNum;
        private BrightIdeasSoftware.OLVColumn olvColumnMOSMaxValue;
        private BrightIdeasSoftware.OLVColumn olvColumnMOSMinValue;
        private BrightIdeasSoftware.OLVColumn olvColumnMOSMeanValue;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItemExport2Excel;
        private BrightIdeasSoftware.OLVColumn olvColumnEventName;
        private System.Windows.Forms.ToolStrip toolStrip;
        private System.Windows.Forms.ToolStripLabel toolStripLabelSampleNum;
        private System.Windows.Forms.ToolStripLabel toolStripLabel1;
        private BrightIdeasSoftware.OLVColumn olvColumnFirstCell;
        private BrightIdeasSoftware.OLVColumn olvColumnLastCell;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
    }
}