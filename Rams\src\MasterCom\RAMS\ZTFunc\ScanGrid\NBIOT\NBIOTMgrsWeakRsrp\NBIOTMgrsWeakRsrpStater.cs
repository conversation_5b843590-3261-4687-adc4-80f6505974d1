﻿using System;
using System.Collections.Generic;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NbIotMgrsWeakRsrpStater : NbIotMgrsStaterBase
    {
        protected NbIotMgrsFuncItem tmpFuncItem;

        public NbIotMgrsWeakRsrpStater()
        {
            //GridSize = NBIOTMgrsBaseSettingManager.Instance.GridSize
        }

        public double GridSize
        {
            get 
            { 
                return NbIotMgrsBaseSettingManager.Instance.GridSize; 
            }
        }
        public int SerialGridCount { get; set; }
        public bool CityType { get; set; }
        public double CurRsrpMax { get; set; }
        public double CurRsrpMin { get; set; }
        public double CurMaxSINR { get; set; }
        public bool CheckSINR { get; set; }
        public bool CheckSINRType { get; set; }

        public override void DoStat(NbIotMgrsFuncItem curFuncItem)
        {
            this.tmpFuncItem = curFuncItem;
        }

        public override void SetResultControl()
        {
            NbIotMgrsWeakRsrpResult resultControl = new NbIotMgrsWeakRsrpResult();
            staterName = resultControl.Desc;
            object[] values = tmpFuncItem.FuncCondtion as object[];
            SerialGridCount = (int)values[0];
            CurRsrpMax = (double)values[1];
            CurRsrpMin = (double)values[2];
            CheckSINR = (bool)values[3];
            CurMaxSINR = (double)values[4];
            CheckSINRType = (bool)values[5];
            resultControl.FillData(tmpFuncItem);
            resultControlList = new List<NbIotMgrsResultControlBase>() { resultControl };
        }

        public virtual List<CarrierData> GetViews()
        {
            resultList = new Dictionary<string, string>();
            List<CarrierData> carrierList = new List<CarrierData>();
            CarrierData cm = getSerialGridViews(CarrierType.ChinaMobile, cmCarrierAreaResult);
            carrierList.Add(cm);

            CarrierData cu = getSerialGridViews(CarrierType.ChinaUnicom, cuCarrierAreaResult);
            carrierList.Add(cu);

            CarrierData ct = getSerialGridViews(CarrierType.ChinaTelecom, ctCarrierAreaResult);
            carrierList.Add(ct);

            return carrierList;
        }

        protected CarrierData getSerialGridViews(CarrierType carierType, CarrierAreaResult areaResult)
        {
            CarrierData carrierData = new CarrierData();
            switch (carierType)
            {
                case CarrierType.ChinaMobile:
                    carrierData.Name = "中国移动";
                    break;
                case CarrierType.ChinaUnicom:
                    carrierData.Name = "中国联通";
                    break;
                case CarrierType.ChinaTelecom:
                    carrierData.Name = "中国电信";
                    break;
            }

            List<SerialWeakGrid> serialGridViews = dealWithData(areaResult.GridList, carierType);
            carrierData.AreaGridViews = getGridArea(serialGridViews);
            addResultInfo(carrierData.AreaGridViews, areaResult);
            return carrierData;
        }

        protected virtual void addResultInfo(List<AreaGridData> areaDataList, CarrierAreaResult carrierAreaResult)
        {
            foreach (var area in areaDataList)
            {
                foreach (var item in carrierAreaResult.carrierResult.AreaList)
                {
                    if (item.AreaName == area.AreaName)
                    {
                        item.SerialWeakRSRPGridCoverage = carrierAreaResult.getAreaResultData(area.AreaName, area.IssuesGridCount);
                        break;
                    }
                }
            }

            carrierAreaResult.carrierResult.SerialWeakRSRPGridCoverage = carrierAreaResult.getTotalResultData(serialWeakGridCount);
        }

        protected virtual List<SerialWeakGrid> dealWithData(Dictionary<string, List<ScanGridInfo>> gridList, CarrierType carrierType)
        {
            List<WeakGrid> weakGridList;

            totalGridCount = gridList.Count;
            weakGridList = GetWeakGridList(gridList);

            NbIotMgrsSerialGridFinder<WeakGrid> finder = new NbIotMgrsSerialGridFinder<WeakGrid>(GridSize);
            Dictionary<int, List<WeakGrid>> idItemsDic = finder.SerialGridFinder(weakGridList, SerialGridCount != 1);

            List<SerialWeakGrid> serialWeakGridList = new List<SerialWeakGrid>();
            //处理结果
            serialWeakGridCount = 0d;
            foreach (var item in idItemsDic.Values)
            {
                if (item.Count >= SerialGridCount)
                {
                    SerialWeakGrid serialWeakGrid = new SerialWeakGrid();
                    serialWeakGrid.GridViews = item;
                    serialWeakGrid.SetFileNameByFileID(MainModel.FileInfos);
                    serialWeakGridList.Add(serialWeakGrid);
                    serialWeakGridCount += item.Count;
                }
            }
            return serialWeakGridList;
        }

        /// <summary>
        /// 按网格区域获取栅格
        /// </summary>
        /// <param name="serialWeakGridList"></param>
        /// <returns></returns>
        public virtual List<AreaGridData> getGridArea(List<SerialWeakGrid> serialWeakGridList)
        {
            List<AreaGridData> areaGridDataList = new List<AreaGridData>();
            Dictionary<string, List<SerialWeakGrid>> gridList = new Dictionary<string, List<SerialWeakGrid>>();
            foreach (SerialWeakGrid serialWeakGrid in serialWeakGridList)
            {
                if (serialWeakGrid.GridViews.Count > 0)
                {
                    //连续栅格的第一个栅格属于那个区域,该结果就分给哪个区域
                    WeakGrid weakGrid = serialWeakGrid.GridViews[0];
                    addSerialWeakGrid(gridList, serialWeakGrid, weakGrid);
                }
            }

            foreach (var item in gridList)
            {
                 AreaGridData areaGridData = new AreaGridData();
                 areaGridData.AreaName = item.Key;
                 areaGridData.SerialGridViews = item.Value;
                 areaGridDataList.Add(areaGridData);
            }

            return areaGridDataList;
        }

        private void addSerialWeakGrid(Dictionary<string, List<SerialWeakGrid>> gridList, SerialWeakGrid serialWeakGrid, WeakGrid weakGrid)
        {
            foreach (MTPolygon polygon in selectPolygons)
            {
                if (polygon.CheckPointInRegion(weakGrid.CentLng, weakGrid.CentLat))
                {
                    if (!gridList.ContainsKey(polygon.Name))
                    {
                        gridList[polygon.Name] = new List<SerialWeakGrid>() { serialWeakGrid };
                        break;
                    }
                    else
                    {
                        gridList[polygon.Name].Add(serialWeakGrid);
                        break;
                    }
                }
            }
        }

        public override Dictionary<string, string> GetResultData()
        {
            return resultList;
        }

        protected virtual List<WeakGrid> GetWeakGridList(Dictionary<string, List<ScanGridInfo>> gridList)
        {
            List<WeakGrid> weakGridList = new List<WeakGrid>();
            //按栅格中最强rsrp小区判断弱覆盖栅格
            foreach (var item in gridList)
            {
                //将栅格中的小区按rsrp降序排序
                item.Value.Sort((x, y) => { return -x.R0_RP.CompareTo(y.R0_RP); });
                ScanGridInfo grid = item.Value[0];

                addWeakGridList(weakGridList, item, grid);
            }
            return weakGridList;
        }

        private void addWeakGridList(List<WeakGrid> weakGridList, KeyValuePair<string, List<ScanGridInfo>> item, ScanGridInfo grid)
        {
            if (CheckSINR && CheckSINRType)
            {
                if ((grid.R0_RP >= CurRsrpMin && grid.R0_RP < CurRsrpMax) && (grid.R0_CINR < CurMaxSINR))
                {
                    WeakGrid weakGrid = new WeakGrid();
                    weakGrid.CellGrid = item.Value;
                    weakGridList.Add(weakGrid);
                }
            }
            else if (CheckSINR && !CheckSINRType)
            {
                if ((grid.R0_RP >= CurRsrpMin && grid.R0_RP < CurRsrpMax) || (grid.R0_CINR < CurMaxSINR))
                {
                    WeakGrid weakGrid = new WeakGrid();
                    weakGrid.CellGrid = item.Value;
                    weakGridList.Add(weakGrid);
                }
            }
            else
            {
                if (grid.R0_RP >= CurRsrpMin && grid.R0_RP < CurRsrpMax)
                {
                    WeakGrid weakGrid = new WeakGrid();
                    weakGrid.CellGrid = item.Value;
                    weakGridList.Add(weakGrid);
                }
            }
        }

        public override void Clear()
        {
            this.tmpFuncItem = null;
        }
    }

    public class CarrierData
    {
        public string Name
        {
            get;
            set;
        }

        public int IssuesCount
        {
            get
            {
                int result = 0;
                foreach (AreaGridData item in AreaGridViews)
                {
                    result += item.SerialGridViews.Count;
                }
                return result;
            }
        }

        public List<AreaGridData> AreaGridViews
        {
            get;
            set;
        }
    }

    public class AreaGridData
    {
        public string AreaName
        {
            get;
            set;
        }

        public int IssuesCount
        {
            get
            {
                return SerialGridViews.Count;
            }
        }

        public int IssuesGridCount
        {
            get 
            {
                int res = 0;
                foreach (var grid in SerialGridViews)
                {
                    res += grid.WeakGridCount;
                }
                return res;
            }
        }

        public List<SerialWeakGrid> SerialGridViews
        {
            get;
            set;
        }
    }

    public class SerialWeakGrid
    {
        /// <summary>
        /// 连续弱覆盖栅格数
        /// </summary>
        public int WeakGridCount
        {
            get
            {
                return GridViews.Count;
            }
        }

        /// <summary>
        /// 连续弱覆盖栅格序号合集
        /// </summary>
        public string WeakGridDesc
        {
            get
            {
                StringBuilder sb = new StringBuilder();
                foreach (var item in GridViews)
                {
                    sb.Append(item.MGRTIndex);
                    sb.Append(",");
                }

                return sb.ToString().TrimEnd(',');
            }
        }

        public string MaxRsrp
        {
            get
            {
                double max = -200;
                foreach (var item in GridViews)
                {
                    max = Math.Max(max, item.CellGrid[0].R0_RP);
                }

                return max.ToString("f2");
            }
        }

        public string MinRsrp
        {
            get
            {
                double min = 200;
                foreach (var item in GridViews)
                {
                    min = Math.Min(min, item.CellGrid[0].R0_RP);
                }

                return min.ToString("f2");
            }
        }

        public string AvgRsrp
        {
            get
            {
                double sum = 0;
                foreach (var item in GridViews)
                {
                    sum += item.CellGrid[0].R0_RP;
                }

                return (sum / GridViews.Count).ToString("f2");
            }
        }

        public string MaxSinr
        {
            get
            {
                double max = -200;
                foreach (var item in GridViews)
                {
                    max = Math.Max(max, item.CellGrid[0].R0_CINR);
                }

                return max.ToString("f2");
            }
        }

        public string MinSinr
        {
            get
            {
                double min = 200;
                foreach (var item in GridViews)
                {
                    min = Math.Min(min, item.CellGrid[0].R0_CINR);
                }

                return min.ToString("f2");
            }
        }

        public string AvgSinr
        {
            get
            {
                double sum = 0;
                foreach (var item in GridViews)
                {
                    sum += item.CellGrid[0].R0_CINR;
                }

                return (sum / GridViews.Count).ToString("f2");
            }
        }

        private string fileNames;
        public string FileNames
        {
            get
            {
                return fileNames;
            }
        }

        public void SetFileNameByFileID(List<FileInfo> fileList)
        {
            fileNames = "";
            StringBuilder sb = new StringBuilder();
            List<int> fileIDList = new List<int>();
            foreach (var item in GridViews)
            {
                foreach (var file in item.CellGrid)
                {
                    if (!fileIDList.Contains(file.FileID))
                    {
                        fileIDList.Add(file.FileID);
                    }
                }
            }

            foreach (var item in fileIDList)
            {
                foreach (var file in fileList)
                {
                    if (item == file.ID)
                    {
                        sb.Append(file.Name);
                        sb.Append(",");
                    }
                }
            }
            fileNames = sb.ToString().TrimEnd(',');
        }

        /// <summary>
        /// 连续弱覆盖栅格
        /// </summary>
        public List<WeakGrid> GridViews
        {
            get;
            set;
        }
    }

    public class WeakGrid : ILteMgrsBlockItem
    {
        public string MGRTIndex
        {
            get
            {
                if (CellGrid.Count > 0)
                {
                    return CellGrid[0].MGRTIndex;
                }
                return "";
            }
        }
        public int CellCount
        {
            get
            {
                return CellGrid.Count;
            }
        }

        public double CentLng
        {
            get
            {
                if (CellGrid.Count > 0)
                {
                    return CellGrid[0].CentLng;
                }
                return 0;
            }
        }

        public double CentLat
        {
            get
            {
                if (CellGrid.Count > 0)
                {
                    return CellGrid[0].CentLat;
                }
                return 0;
            }
        }

        public string MaxRsrp
        {
            get
            {
                double max = -200;
                foreach (var item in CellGrid)
                {
                    max = Math.Max(max, item.R0_RP);
                }

                return max.ToString("f2");
            }
        }

        public string AvgRsrp
        {
            get
            {
                double sum = 0;
                int count = 0;
                foreach (var item in CellGrid)
                {
                    sum += item.R0_RP * item.SampleCount;
                    count += item.SampleCount;
                }

                return (sum / count).ToString("f2");
            }
        }

        public string MaxSinr
        {
            get
            {
                double max = -200;
                foreach (var item in CellGrid)
                {
                    max = Math.Max(max, item.R0_CINR);
                }

                return max.ToString("f2");
            }
        }

        public string AvgSinr
        {
            get
            {
                double sum = 0;
                int count = 0;
                foreach (var item in CellGrid)
                {
                    sum += item.R0_CINR * item.SampleCount;
                    count += item.SampleCount;
                }

                return (sum / count).ToString("f2");
            }
        }

        /// <summary>
        /// 小区栅格列表
        /// </summary>
        public List<ScanGridInfo> CellGrid
        {
            get;
            set;
        }
    }
}
