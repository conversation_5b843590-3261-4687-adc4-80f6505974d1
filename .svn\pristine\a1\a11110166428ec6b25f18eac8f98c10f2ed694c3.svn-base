﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Runtime.Serialization;
using System.Drawing;
using MasterCom.RAMS.Model;

namespace MasterCom.MTGis
{
    [Serializable]
    public abstract class CustomDrawLayer : ISerializable
    {
        public event EventHandler VisibleChanged;
        public void FireVisibleChanged()
        {
            if (VisibleChanged!=null)
            {
                VisibleChanged(this, EventArgs.Empty);
            }
        }
        public static float GetTestPointDisplayRatio(double scale)
        {
            float ratio = 50;
            if (scale > 200000)
            {
                ratio = 0.02F;
            }
            if (scale > 100000)
            {
                ratio = 0.025F;
            }
            else if (scale > 70000)
            {
                ratio = 0.03F;
            }
            else if (scale > 50000)
            {
                ratio = 0.035F;
            }
            else if (scale > 30000)
            {
                ratio = 0.04F;
            }
            else if (scale > 20000)
            {
                ratio = 0.045F;
            }
            else if (scale > 15000)
            {
                ratio = 0.05F;
            }
            else if (scale > 10000)
            {
                ratio = 0.0625F;
            }
            else if (scale > 1000)
            {
                ratio = 0.125F / (float)(Math.Log10(scale) - 2);
            }
            else if (scale > 100)
            {
                ratio = 0.25F / (float)(Math.Log10(scale) - 1);
            }
            else if (scale > 10)
            {
                ratio = 0.5F / (float)Math.Log10(scale);
            }
            else if (scale > 0.1)
            {
                ratio = 5 / (float)scale;
            }
            return ratio / 2;
        }
        protected CustomDrawLayer(MapOperation oper, String Name)
            : this(oper)
        {
            this.Name = Name;
            this.MainModel = oper.MainModel;
            MasterCom.RAMS.Func.MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.MapFeatureSelecting += new EventHandler(mapForm_MapFeatureSelecting);
            }
        }

        protected CustomDrawLayer(MapOperation oper)
        {
            this.Map = oper;
            this.MainModel = oper.MainModel;
            MasterCom.RAMS.Func.MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.MapFeatureSelecting += new EventHandler(mapForm_MapFeatureSelecting);
            }
        }
        protected virtual void mapForm_MapFeatureSelecting(object sender, EventArgs e)
        {

        }
        public VisibleScale VisibleScale { get; set; } = new VisibleScale(0, 9999999.0);
        public bool VisibleScaleEnabled { get; set; } = false;
        public bool IsVisible
        {
            get
            {
                return visible;
            }
            set
            {
                enabled = value;
                visible = value;
                FireVisibleChanged();
            }
        }
        public bool Enabled
        {
            get
            {
                return enabled;
            }
            set
            {
                enabled = value;
                visible = value;
            }
        }
        public string Name { get; set; } = "";
        virtual public MainModel MainModel
        {
            get
            {
                return mainModel;
            }
            set
            {
#if DEBUG
                //为了防止sonar提示而加的,部分子类会重写
                Console.Write(1);
#endif
                mainModel = value;
            }
        }
        public int Alpha { get; set; } = 255;//不透明255 透明0
        public abstract void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics);
        public virtual void GetObjectData(SerializationInfo info, StreamingContext context)
        {
        }
        public virtual void SetObjectData(SerializationInfo info, StreamingContext context)
        {
        }
        public virtual void Invalidate()
        {
            MasterCom.RAMS.Func.MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf!=null)
            {
                mf.updateMap();
            }
        }
        public virtual Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["VisibleScaleEnabled"] = VisibleScaleEnabled;
                param["minVisibleScan"] = VisibleScale.ScaleMin;
                param["maxVisibleScan"] = VisibleScale.ScaleMax;
                return param;
            }
            set
            {
                if (value.ContainsKey("VisibleScaleEnabled"))
                {
                    VisibleScaleEnabled = (bool)value["VisibleScaleEnabled"];
                }
                if (value.ContainsKey("minVisibleScan"))
                {
                    VisibleScale.ScaleMin = (double)value["minVisibleScan"];
                }
                if (value.ContainsKey("maxVisibleScan"))
                {
                    VisibleScale.ScaleMax = (double)value["maxVisibleScan"];
                }
            }
        }
        public MapOperation Map { get; set; }
        private bool visible = true;
        private bool enabled = true;
        protected MainModel mainModel = null;
        public virtual void LayerDispose()
        {
            MasterCom.RAMS.Func.MapForm mf = mainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.MapFeatureSelecting -= mapForm_MapFeatureSelecting;
            }
        }

        /// <summary>
        /// GreatMap对应的图层
        /// </summary>
        public virtual Type GetGMapLayerType()
        {
            return null;
        }
        public virtual int GetSupportedThemeOptions()
        {
            return 0;
        }
        public virtual double CalcThemeOption(int themeOption, object[] args)
        {
            return 0;
        }
        public override string ToString()
        {
            if (Name != null && Name != "")
            {
                return Name;
            }
            else
            {
                return "(未命名CustomDrawLayer图层)";
            }
        }
    }
}
