﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class XtraSetSampleMultiForm : DevExpress.XtraEditors.XtraForm
    {
        public XtraSetSampleMultiForm(string type)
        {
            InitializeComponent();
            cbxCoFreqType.Properties.Items.Clear();
            cbxBandType.SelectedIndex = 0;
            if (type.Equals("GSM"))
            {
                cbxCoFreqType.Properties.Items.Add("BCCH 或 TCH");
                cbxCoFreqType.Properties.Items.Add("BCCH Only");
                cbxCoFreqType.Properties.Items.Add("TCH Only");
                numRxLevDValue.Value = 12;
                numRxLevThreshold.Value = -80;
            }
            else if (type.Equals("TD"))
            {
                cbxCoFreqType.Properties.Items.Add("ARFCN & ARFCNList");
                cbxCoFreqType.Properties.Items.Add("ARFCN Only");
                cbxCoFreqType.Properties.Items.Add("ARFCNList Only");
                numRxLevDValue.Value = 6;
                numRxLevThreshold.Value = -80;
            }
            if (cbxCoFreqType.Properties.Items.Count > 1)
            {
                cbxCoFreqType.SelectedIndex = 1;
            }
        }


        public bool GetSettingFilterRet(out int bandType, out int setRxlev, out int setRxlevDiff, out bool coFreq, out int level, out MapForm.DisplayInterferenceType interferenceType,
                                        out int invalidPointRxLev)
        {
            setRxlevDiff = (int)numRxLevDValue.Value;
            setRxlev = (int)numRxLevThreshold.Value;
            coFreq = chkCoFreq.Checked;
            level = (int)spinEditLevel.Value;
            bandType = cbxBandType.SelectedIndex;
            interferenceType = (MapForm.DisplayInterferenceType)cbxCoFreqType.SelectedIndex;
            invalidPointRxLev = (int)spinEditInvalidThresold.Value;

            return true;
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleButton2_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}