﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.NOP;
using MasterCom.Util;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.NewBlackBlock
{
    public partial class BlackBlockESForm : MinCloseForm
    {
        public BlackBlockESForm()
        {
            InitializeComponent();
        }

        public void FillData(List<BlockEventItem> listData)
        {
            this.gridCtrl.DataSource = listData;
            this.gridCtrl.RefreshDataSource();
            this.gridCtrl.Refresh();
            gv.LayoutChanged();
        }

        private void miShowEsFlow_Click(object sender, EventArgs e)
        {
            int[] arr = gv.GetSelectedRows();
            if (arr.Length==0)
            {
                return;
            }
            BlockEventItem evtItem = gv.GetRow(arr[0]) as BlockEventItem;

            ProcRoutineManager.Instance.Init();
            TaskFlowDiagramForm frm = MainModel.CreateResultForm(typeof(TaskFlowDiagramForm)) as TaskFlowDiagramForm;
            TaskEventItem taskItem = new TaskEventItem(evtItem.EventDes, evtItem.districtID,
                evtItem.file_id, evtItem.ieventId, evtItem.sn, evtItem.DateTime);
            taskItem.ESResultInfo = evtItem.ESResult;
            frm.TaskEventItem = taskItem;
            frm.Visible = true;
            frm.BringToFront();
        }

        private void miExportXls_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gv);
        }

        private void miReplayEvent_Click(object sender, EventArgs e)
        {
            int[] arr = gv.GetSelectedRows();
            if (arr.Length == 0)
            {
                return;
            }
            BlockEventItem evtItem = gv.GetRow(arr[0]) as BlockEventItem;
            FileReplayer.Replay(evtItem.ConvertToEvent(), true);
        }

        private void gv_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gv.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }
            BlockEventItem evtItem = gv.GetRow(info.RowHandle) as BlockEventItem;
            MainModel.MainForm.GetMapForm().GoToView(evtItem.Longitude, evtItem.Latitude, 5000);

        }

        private void miReplayFusion_Click(object sender, EventArgs e)
        {
            int[] arr = gv.GetSelectedRows();
            if (arr.Length == 0)
            {
                return;
            }
            BlockEventItem evtItem = gv.GetRow(arr[0]) as BlockEventItem;
            FileReplayer.Replay(evtItem.ConvertToEvent(), true);
            Dictionary<string, LTECell> cellDic = new Dictionary<string, LTECell>();
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                foreach (TestPoint tp in file.TestPoints)
                {
                    LTECell cell = tp.GetMainCell_LTE();
                    if (cell == null)
                    {
                        continue;
                    }
                    cellDic[string.Format("{0}_{1}", cell.TAC, cell.ECI)] = cell;
                }
            }
            if (cellDic.Count == 0)
            {
                MessageBox.Show("未能关联到小区数据");
                return;
            }
            WaitTextBox.Show("", queryFusionData, new object[] { evtItem.ConvertToEvent(), cellDic });
            FusionDataForm frm = MainModel.CreateResultForm(typeof(FusionDataForm)) as FusionDataForm;
            frm.FillData(alarmDataSet, perfDataSet, argDataSet);
            frm.Visible = true;
            frm.BringToFront();
        }

        private void miReplayFile_Click(object sender, EventArgs e)
        {
            int[] arr = gv.GetSelectedRows();
            if (arr.Length == 0)
            {
                return;
            }
            BlockEventItem evtItem = gv.GetRow(arr[0]) as BlockEventItem;
            FileReplayer.Replay(evtItem.ConvertToEvent(), false);
        }

        List<CellAlarmData> alarmDataSet = new List<CellAlarmData>();
        List<CellPerfData> perfDataSet = new List<CellPerfData>();
        List<CellArgData> argDataSet = new List<CellArgData>();
        private void queryFusionData(object obj)
        {
            object[] arr = obj as object[];
            Event evt = arr[0] as Event;
            Dictionary<string, LTECell> cellDic = arr[1] as Dictionary<string, LTECell>;       
            StringBuilder sb = new StringBuilder();
            foreach (LTECell cell in cellDic.Values)
            {
                if (sb.Length > 0)
                {
                    sb.Append(" or ");
                }
                sb.AppendFormat(" (lac={0} and ci={1}) ", cell.TAC, cell.ECI);
            }
            string cellCond = sb.ToString();
            WaitTextBox.Text = "正在关联告警数据...";
            QueryFusionAlarmData qryAlarm = new QueryFusionAlarmData();
            qryAlarm.CellDic = cellDic;
            qryAlarm.CellCondition = cellCond;
            qryAlarm.Event = evt;
            qryAlarm.Query();
            alarmDataSet = qryAlarm.CellAlarmSet;
            WaitTextBox.Text = "正在关联性能数据...";
            QueryFusionPerfData qryPerf = new QueryFusionPerfData();
            qryPerf.CellDic = cellDic;
            qryPerf.CellCondition = cellCond;
            qryPerf.Event = evt;
            qryPerf.Query();
            perfDataSet = qryPerf.CellPerfSet;
            WaitTextBox.Text = "正在关联参数数据...";
            QueryFusionArgData qryArg = new QueryFusionArgData();
            qryArg.CellDic = cellDic;
            qryArg.CellCondition = cellCond;
            qryArg.Event = evt;
            qryArg.Query();
            argDataSet = qryArg.CellArgSet;
            WaitTextBox.Close();
        }
    }
}
