﻿using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteNBCellCheckDiffFreqAnaBase : DIYAnalyseFilesOneByOneByRegion
    {
        public List<ZTLteNBCellCheckDiffFreqFileItem> resultList { get; set; } = new List<ZTLteNBCellCheckDiffFreqFileItem>();

        public ZTLteNBCellCheckDiffFreqAnaBase(MainModel mainModel)
            : base(mainModel)
        {
            this.IncludeEvent = false;
            this.IncludeMessage = true;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            resultList = new List<ZTLteNBCellCheckDiffFreqFileItem>();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                List<DTData> dtDataList = new List<DTData>();

                foreach (TestPoint tp in fileMng.TestPoints)
                {
                    dtDataList.Add((DTData)tp);
                }

                foreach (MasterCom.RAMS.Model.Message msg in fileMng.Messages)
                {
                    if (msg.ID == (int)EnumLteNBCheckMsg.RRCConnectionReconfiguration ||
                         msg.ID == (int)EnumLteNBCheckMsg.SystemInformationBlockType1 ||
                         msg.ID == (int)EnumLteNBCheckMsg.MeasurementReport)
                    {
                        dtDataList.Add((DTData)msg);
                    }
                }

                dtDataList.Sort(comparer);

                ZTLteNBCellCheckDiffFreqFileItem fileItem = new ZTLteNBCellCheckDiffFreqFileItem();
                fileItem.SN = resultList.Count + 1;
                fileItem.FileName = fileMng.FileName;
                resultList.Add(fileItem);

                ZTLteNBCellCheckDiffFreqMsgItem curMsgItem = new ZTLteNBCellCheckDiffFreqMsgItem();

                for (int i = 0; i < dtDataList.Count; i++)
                {
                    dealDTData(dtDataList, ref fileItem, ref curMsgItem, i);
                }
            }
        }

        private void dealDTData(List<DTData> dtDataList, ref ZTLteNBCellCheckDiffFreqFileItem fileItem, 
            ref ZTLteNBCellCheckDiffFreqMsgItem curMsgItem, int i)
        {
            if (dtDataList[i] is TestPoint)
            {
                if (curMsgItem.IsGotHOMsg)
                {
                    curMsgItem.Tp = dtDataList[i] as TestPoint; //用最近的采样点经纬度来填充
                }
            }
            else if (dtDataList[i] is MasterCom.RAMS.Model.Message)
            {
                Model.Message msg = dtDataList[i] as Model.Message;
                if (msg.ID == (int)EnumLteNBCheckMsg.RRCConnectionReconfiguration)
                {
                    processRRCConnReconfigMsg(dtDataList[i], ref curMsgItem, ref fileItem);
                }
                else if (msg.ID == (int)EnumLteNBCheckMsg.SystemInformationBlockType1)
                {
                    processSIB1Msg(dtDataList[i], ref curMsgItem);
                }
                else if (msg.ID == (int)EnumLteNBCheckMsg.MeasurementReport)
                {
                    processMRMsg(dtDataList[i], ref curMsgItem);
                }
            }
        }

        /// <summary>
        /// 分析 RRCConnectionReconfig层三信令，如果包含目标EARFCN和PCI，就认为是切换信令
        /// 如果不是，则需进一步判断是否是下发邻区的信令
        /// </summary>
        /// <param name="cellItem"></param>
        /// <param name="dtData"></param>
        /// <param name="fileItem"></param>
        private void processRRCConnReconfigMsg(DTData dtData, ref ZTLteNBCellCheckDiffFreqMsgItem curMsgItem, ref ZTLteNBCellCheckDiffFreqFileItem curFileItem)
        {
            MessageWithSource msg = ((MessageWithSource)dtData);
            MessageDecodeHelper.StartDissect(msg.Direction,msg.Source, msg.Source.Length, msg.ID);

            // 如果RRC Connection Reconfiguration中包含mobilityControlInfo，那主要作用就是eNodeB发切换命令给UE执行切换
            // 如果RRC Connection Reconfiguration中包含measConfig，那其主要作用就是进行测量配置

            int earfcn = 0;
            int pci = 0;
            if (MessageDecodeHelper.GetSingleSInt("lte-rrc.targetPhysCellId", ref pci))
            {
                if (!MessageDecodeHelper.GetSingleSInt("lte-rrc.dl_CarrierFreq", ref earfcn))   //如果获取不到earfcn，沿用之前的
                {
                    earfcn = curMsgItem.EARFCN;
                }

                curMsgItem.EARFCN_HO = earfcn;
                curMsgItem.PCI_HO = pci;

                if (curMsgItem.NBCfgTime != 0)
                {
                    curMsgItem.SpanReConfig2HO = dtData.Time - curMsgItem.NBCfgTime;
                }
                if (curMsgItem.LastMRTime != 0)
                {
                    curMsgItem.SpanLastMR2HO = dtData.Time - curMsgItem.LastMRTime;
                }

                addToResult(ref curMsgItem, ref curFileItem);

                curMsgItem = new ZTLteNBCellCheckDiffFreqMsgItem();
                curMsgItem.EARFCN = earfcn;
                curMsgItem.PCI = pci;
                curMsgItem.IsGotHOMsg = true;
            }
            else
            {
                processRRCConnReconfigMsg_CfgNBList(dtData, ref curMsgItem);
            }
        }

 
        /// <summary>
        /// 解析出需要测量的邻区列表
        /// </summary>
        /// <param name="cellItem"></param>
        private void processRRCConnReconfigMsg_CfgNBList(DTData dtData, ref ZTLteNBCellCheckDiffFreqMsgItem curMsgItem)
        {
            if (!curMsgItem.IsGotHOMsg)  //对于其它信令，如果还没有头信令，不处理
            {
                return;
            }

            //找出异频事件所在的频点和measID
            setMeasID(ref curMsgItem);

            if (curMsgItem.NBCfgDic.Count > 0)
            {
                if (curMsgItem.NBCfgTime == 0)
                {
                    curMsgItem.NBCfgTime = dtData.Time;
                }
                curMsgItem.IsGotNBConfigMsg = true;
            }
            else
            {
                curMsgItem.IsGotNBConfigMsg = false;
            }
        }

        /// <summary>
        /// 找到与当前频点异频的setMeasID
        /// 对应方法如下：
        /// carrierFreq 与 measObjectID 对应，具体查看 measObjectToAddModList
        /// measObjectID 与 measID 对应，具体查看measIdToAddModList
        /// measObjectID在两个列表中都存在，因此数组的初始大小为 measObjectIDCount + measIDCount
        /// </summary>
        private void setMeasID(ref ZTLteNBCellCheckDiffFreqMsgItem curMsgItem)
        {
            int measObjectIDCount = 0;
            int measIDCount = 0;
            if (MessageDecodeHelper.GetSingleSInt("lte-rrc.measObjectToAddModList", ref measObjectIDCount)  //包含测量对象
                && MessageDecodeHelper.GetSingleSInt("lte-rrc.measIdToAddModList", ref measIDCount))
            {
                int[] arrCarrierFreqs = new int[measObjectIDCount];
                int[] arrMeasObjectIDs = new int[measObjectIDCount + measIDCount];  //两个List都可以获取这部分信息
                int[] arrMeasIDs = new int[measIDCount];

                if (MessageDecodeHelper.GetMultiSInt("lte-rrc.carrierFreq", ref arrCarrierFreqs, measObjectIDCount)  //存在有测量，无carrierFreq的情况
                    && MessageDecodeHelper.GetMultiSInt("lte-rrc.measObjectId", ref arrMeasObjectIDs, measObjectIDCount + measIDCount)
                    && MessageDecodeHelper.GetMultiSInt("lte-rrc.measId", ref arrMeasIDs, measIDCount))
                {
                    for (int i = 0; i < arrCarrierFreqs.Length; i++)    //用频点的length做判断，避免部分Item不下发频点信息，导致报错
                    {
                        int earfcn = arrCarrierFreqs[i];
                        int measObjectID = arrMeasObjectIDs[i];

                        for (int j = measObjectIDCount; j < measObjectIDCount + measIDCount; j++)   //向后看，找到同样的MeasObjectID
                        {
                            dealNBCfg(curMsgItem, measObjectIDCount, arrMeasObjectIDs, arrMeasIDs, earfcn, measObjectID, j);
                        }
                    }
                }
            }                 
        }

        private void dealNBCfg(ZTLteNBCellCheckDiffFreqMsgItem curMsgItem, int measObjectIDCount, int[] arrMeasObjectIDs, int[] arrMeasIDs, int earfcn, int measObjectID, int j)
        {
            if (measObjectID == arrMeasObjectIDs[j])
            {
                int measID = arrMeasIDs[j - measObjectIDCount];

                if (curMsgItem.NBCfgDic.ContainsKey(measID))  //measID已经占用
                {
                    if (earfcn != curMsgItem.EARFCN) //如果是异频
                    {
                        curMsgItem.NBCfgDic[measID] = earfcn; //更新
                    }
                    else
                    {
                        curMsgItem.NBCfgDic.Remove(measID);  //不是，则删除
                    }
                }
                else
                {
                    if (earfcn != curMsgItem.EARFCN) //如果是异频
                    {
                        curMsgItem.NBCfgDic.Add(measID, earfcn);
                    }
                }
            }
        }

        /// <summary>
        /// 解析SIB1,用户获取当前小区的TAC和ECI
        /// </summary>
        /// <param name="cellItem"></param>
        /// <param name="dtData"></param>
        private void processSIB1Msg(DTData dtData, ref ZTLteNBCellCheckDiffFreqMsgItem curMsgItem)
        {
            if (!curMsgItem.IsGotHOMsg)  //对于其它信令，如果还没有头信令，不处理
            {
                return;
            }

            if (curMsgItem.IsGotSIB1Msg)    //已经获取到一次，不再获取，避免出现重定向后更新小区频点，导致错误
            {
                return;
            }

            MessageWithSource msg = ((MessageWithSource)dtData);
            MessageDecodeHelper.StartDissect(msg.Direction,msg.Source, msg.Source.Length, msg.ID);

            uint uTac = 0;
            uint uEci = 0;
            if (MessageDecodeHelper.GetSingleUInt("lte-rrc.trackingAreaCode_TAC", ref uTac) 
                && MessageDecodeHelper.GetSingleUInt("lte-rrc.cellIdentity_ECI", ref uEci))
            {
                curMsgItem.TAC = (int)uTac;
                curMsgItem.ECI = (int)uEci;
                curMsgItem.IsGotSIB1Msg = true;
            }

            int freqBandIndicator = 0;
            if (MessageDecodeHelper.GetSingleSInt("lte-rrc.freqBandIndicator", ref freqBandIndicator))
            {
                if (freqBandIndicator == 39)
                {
                    curMsgItem.EARFCN = 38350;
                }
                else if (freqBandIndicator == 38)
                {
                    curMsgItem.EARFCN = 37900;
                }
                else if (freqBandIndicator == 40)
                {
                    curMsgItem.EARFCN = 39150;
                }
            }

            curMsgItem.IsGotSIB1Msg = true;
        }

        /// <summary>
        /// 解析MR测量报告，解析时需要判断MeasID是否是之前异频测量设置的ID
        /// </summary>
        /// <param name="cellItem"></param>
        /// <param name="dtData"></param>
        private void processMRMsg(DTData dtData, ref ZTLteNBCellCheckDiffFreqMsgItem curMsgItem)
        {
            if (!curMsgItem.IsGotHOMsg)  //对于其它信令，如果还没有头信令，不处理
            {
                return;
            }

            if (!curMsgItem.IsGotNBConfigMsg)    //如果没有下配置，不需要看MR
            {
                return;
            }

            curMsgItem.LastMRTime = dtData.Time;

            MessageWithSource msg = ((MessageWithSource)dtData);
            MessageDecodeHelper.StartDissect(msg.Direction, msg.Source, msg.Source.Length, msg.ID);

            int measID = 0;
            bool isFound = false;
            int earfcn = 0;
            if (MessageDecodeHelper.GetSingleSInt("lte-rrc.measId", ref measID))
            {
                foreach (int nbMeasID in curMsgItem.NBCfgDic.Keys)
                {
                    if (measID == nbMeasID)
                    {
                        isFound = true;
                        earfcn = curMsgItem.NBCfgDic[nbMeasID];
                        break;
                    }
                }
                if (!isFound)  //不是需要的报告
                {
                    return;
                }
            }

            setCurMsgItem(curMsgItem, earfcn);
        }

        private void setCurMsgItem(ZTLteNBCellCheckDiffFreqMsgItem curMsgItem, int earfcn)
        {
            int nbCellsCount = 0;
            if (MessageDecodeHelper.GetSingleSInt("lte-rrc.measResultListEUTRA", ref nbCellsCount))
            {
                int[] arrRSRPs = new int[nbCellsCount + 1]; //包括主服的，所以+1
                int[] arrPCIs = new int[nbCellsCount];
                if (MessageDecodeHelper.GetMultiSInt("lte-rrc.rsrpResult", ref arrRSRPs, nbCellsCount + 1) && MessageDecodeHelper.GetMultiSInt("lte-rrc.physCellId", ref arrPCIs, nbCellsCount))
                {
                    Dictionary<int, int> pciDic = new Dictionary<int, int>();
                    if (curMsgItem.NBRptDic.ContainsKey(earfcn))
                    {
                        pciDic = curMsgItem.NBRptDic[earfcn];
                    }
                    else
                    {
                        curMsgItem.NBRptDic.Add(earfcn, pciDic);
                    }

                    for (int i = 0; i < nbCellsCount; i++)
                    {
                        if (!pciDic.ContainsKey(arrPCIs[i]))
                        {
                            pciDic.Add(arrPCIs[i], 1);
                        }
                    }
                    curMsgItem.IsGotMRMsg = true; //信息收集完毕
                }
            }
        }

        /// <summary>
        /// 将结果添加到列表中
        /// </summary>
        /// <param name="curMsgItem"></param>
        private void addToResult(ref ZTLteNBCellCheckDiffFreqMsgItem curMsgItem, ref ZTLteNBCellCheckDiffFreqFileItem curFileItem)
        {
            if (!curMsgItem.IsGotHOMsg)     //没有头信息
            {
                return;
            }

            if (curMsgItem.Tp == null)  //没有采样点信息
            {
                return;
            }

            if (curMsgItem.EARFCN == 0)  //未获取到频点信息，常见于测试开始
            {
                return;
            }

            ZTLteNBCellCheckDiffFreqCellItem cellItem = new ZTLteNBCellCheckDiffFreqCellItem(curMsgItem);

            setServCellInfo(ref cellItem);

            setTypeAndStatus(ref cellItem);

            setHOCellInfo(ref cellItem);

            setRptCellInfo(ref cellItem);

            if (curMsgItem.SpanReConfig2HO >= 0)
            {
                cellItem.SpanReConfig2HO = curMsgItem.SpanReConfig2HO.ToString();
            }

            if (curMsgItem.SpanLastMR2HO >= 0)
            {
                cellItem.SpanLastMR2HO = curMsgItem.SpanLastMR2HO.ToString();
            }

            cellItem.SN = curFileItem.CellList.Count + 1;
            curFileItem.CellList.Add(cellItem);
        }

        /// <summary>
        /// 设置主服小区信息
        /// </summary>
        /// <param name="cellItem"></param>
        private void setServCellInfo(ref ZTLteNBCellCheckDiffFreqCellItem cellItem)
        {
            LTECell servCell = null;
            servCell = CellManager.GetInstance().GetNearestLTECell(cellItem.MsgItem.Tp.DateTime, cellItem.MsgItem.TAC, cellItem.MsgItem.ECI, cellItem.MsgItem.EARFCN, cellItem.MsgItem.PCI,
                                                                  cellItem.MsgItem.Tp.Longitude, cellItem.MsgItem.Tp.Latitude);

            if (servCell != null)    //没有匹配到主服工参
            {
                cellItem.ServCellName = servCell.Name + "(" + cellItem.MsgItem.EARFCN.ToString() + "_" + cellItem.MsgItem.PCI.ToString() + ")";
                cellItem.ServCell = servCell;
            }
            else
            {
                cellItem.ServCellName = cellItem.MsgItem.EARFCN.ToString() + "_" + cellItem.MsgItem.PCI.ToString();
            }
        }

        /// <summary>
        /// 设置类型和漏配检测结果
        /// </summary>
        /// <param name="cellItem"></param>
        private void setTypeAndStatus(ref ZTLteNBCellCheckDiffFreqCellItem cellItem)
        {
            if (cellItem.MsgItem.NBCfgDic.Count == 0)
            {
                cellItem.NBCfgType = "没有下发异频测量";
            }
            else
            {
                if (cellItem.MsgItem.NBRptDic.Count == 0)
                {
                    cellItem.NBCfgType = "下发异频测量，没有收到MR报告";
                }
                else
                {
                    cellItem.NBCfgType = "下发异频测量，收到MR报告";

                    cellItem.Status = "漏配"; //初始化为漏配

                    //看拟切换的小区，是否在上报的小区中，如果是：没有漏配，如果不是：漏配
                    if (cellItem.MsgItem.NBRptDic.ContainsKey(cellItem.MsgItem.EARFCN_HO)
                        && cellItem.MsgItem.NBRptDic[cellItem.MsgItem.EARFCN_HO].ContainsKey(cellItem.MsgItem.PCI_HO))
                    {
                            cellItem.Status = "切换小区在上报中";
                    }
                }
            }
        }

        /// <summary>
        /// 匹配拟切换的小区名称
        /// </summary>
        /// <param name="cellItem"></param>
        private void setHOCellInfo(ref ZTLteNBCellCheckDiffFreqCellItem cellItem)
        {
            LTECell cellHO = null;
            cellHO = CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(cellItem.MsgItem.Tp.DateTime, cellItem.MsgItem.EARFCN_HO, cellItem.MsgItem.PCI_HO,
                                                                            cellItem.MsgItem.Tp.Longitude, cellItem.MsgItem.Tp.Latitude);
            if (cellHO != null)
            {
                cellItem.NBHOInfo = cellHO.Name + "(" + cellItem.MsgItem.EARFCN_HO + "_" + cellItem.MsgItem.PCI_HO + ")";
            }
            else
            {
                cellItem.NBHOInfo = cellItem.MsgItem.EARFCN_HO + "_" + cellItem.MsgItem.PCI_HO;
            }
        }

        /// <summary>
        /// 匹配上报的小区名称
        /// </summary>
        /// <param name="cellItem"></param>
        private void setRptCellInfo(ref ZTLteNBCellCheckDiffFreqCellItem cellItem)
        {
            if (cellItem.MsgItem.NBRptDic.Count == 0)   //没有上报的小区
            {
                return;
            }

            StringBuilder sb = new StringBuilder(cellItem.NBRptInfo);
            foreach (int earfcn in cellItem.MsgItem.NBRptDic.Keys)
            {
                foreach (int pci in cellItem.MsgItem.NBRptDic[earfcn].Keys)
                {
                    LTECell cellRpt = null;
                    cellRpt = CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(cellItem.MsgItem.Tp.DateTime, earfcn, pci,
                                                                                    cellItem.MsgItem.Tp.Longitude, cellItem.MsgItem.Tp.Latitude);
               
                    if (cellRpt != null)
                    {
                        sb.Append(cellRpt.Name + "(" + earfcn + "_" + pci + ")" + " | ");
                    }
                    else
                    {
                        sb.Append(earfcn + "_" + pci + " | ");
                    }
                }
            }
            cellItem.NBRptInfo = sb.ToString();
        }        

        /// <summary>
        /// 显示结果
        /// </summary>
        protected override void fireShowForm()
        {
            if (resultList.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }

            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTLteNBCellCheckDiffFreqAnaListForm).FullName);
            ZTLteNBCellCheckDiffFreqAnaListForm lteNBCellCheckListForm = obj == null ? null : obj as ZTLteNBCellCheckDiffFreqAnaListForm;
            if (lteNBCellCheckListForm == null || lteNBCellCheckListForm.IsDisposed)
            {
                lteNBCellCheckListForm = new ZTLteNBCellCheckDiffFreqAnaListForm(MainModel);
            }

            lteNBCellCheckListForm.FillData(resultList);
            if (!lteNBCellCheckListForm.Visible)
            {
                lteNBCellCheckListForm.Show(MainModel.MainForm);
            }
        }

        protected override void releaseSource()
        {
            resultList = null;
        }

        private Comparer comparer { get; set; } = new Comparer();
        private class Comparer : IComparer<DTData>
        {
            public int Compare(DTData x, DTData y)
            {
                return x.SN - y.SN;
            }
        }
    }

    //用来记录一次完整的信令过程，
    public class ZTLteNBCellCheckDiffFreqMsgItem
    {
        public int TAC { get; set; }
        public int ECI { get; set; }
        public int EARFCN { get; set; }
        public int PCI { get; set; }

        //通过信令下发的MeasI和异频频点的对应关系
        public Dictionary<int,int> NBCfgDic { get; set; }          //Dictionary<MeasID, EARFCN>
        public int NBCfgTime { get; set; }                         //下发异频测量的时间 
        public int LastMRTime { get; set; }                        //最后一个MR消息，用于计算最后一个MR与HO之间的时间差
        public int SpanReConfig2HO { get; set; }                   //下发异频测量到切换之间的时长
        public int SpanLastMR2HO { get; set; }                     //最近MR到切换之间的时长

        //MR报告中上报的PCI
        public Dictionary<int, Dictionary<int, int>> NBRptDic { get; set; }         //Dictionary<EARFCN, Dictionary<PCI, int>>

        //本次完整信令过程后的第一个切换小区
        public int EARFCN_HO { get; set; }
        public int PCI_HO { get; set; }

        public bool IsGotHOMsg { get; set; }
        public bool IsGotNBConfigMsg { get; set; }
        public bool IsGotSIB1Msg { get; set; }
        public bool IsGotMRMsg { get; set; }

        //用于获取经纬度信息
        public TestPoint Tp { get; set; }

        public ZTLteNBCellCheckDiffFreqMsgItem()
        {
            TAC = 0;
            ECI = 0;
            EARFCN = 0;
            PCI = 0;

            NBCfgTime = 0;
            SpanReConfig2HO = -999;
            SpanLastMR2HO = -999;

            NBCfgDic = new Dictionary<int, int>();
            NBRptDic = new Dictionary<int, Dictionary<int, int>>();

            IsGotHOMsg = false;
            IsGotNBConfigMsg = false;
            IsGotSIB1Msg = false;
            IsGotMRMsg = false;

            EARFCN_HO = 0;
            PCI_HO = 0;

            Tp = null;
        }
    }

    public class ZTLteNBCellCheckDiffFreqFileItem
    {
        public int SN { get; set; }
        public string FileName { get; set; }
        public List<ZTLteNBCellCheckDiffFreqCellItem> CellList { get; set; }

        public ZTLteNBCellCheckDiffFreqFileItem()
        {
            SN = 0;
            FileName = "";
            CellList = new List<ZTLteNBCellCheckDiffFreqCellItem>();
        }
    }

    public class ZTLteNBCellCheckDiffFreqCellItem
    {
        public int SN { get; set; }
        public string ServCellName { get; set; }
        public LTECell ServCell { get; set; }
        public ZTLteNBCellCheckDiffFreqMsgItem MsgItem { get; set; }
        public string NBCfgType { get; set; }   
        public string Status { get; set; }
        public string SpanReConfig2HO { get; set; }      //下发异频测量到切换之间的时长
        public string SpanLastMR2HO { get; set; }      //下发异频测量到切换之间的时长

        public string NBRptInfo { get; set; }
        public string NBHOInfo { get; set; }

        public ZTLteNBCellCheckDiffFreqCellItem(ZTLteNBCellCheckDiffFreqMsgItem msgItem)
        {
            SN = 0;
            ServCellName = "";
            ServCell = null;
            MsgItem = msgItem;
            NBCfgType = "";
            Status = "";
            SpanReConfig2HO = "";
            SpanLastMR2HO = ""; 
            NBRptInfo = "";
            NBHOInfo = "";
        }
    }
}


 
