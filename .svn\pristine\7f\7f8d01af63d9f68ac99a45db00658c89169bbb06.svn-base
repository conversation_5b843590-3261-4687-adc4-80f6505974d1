﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRModRoadItem : ModRoadItemBase
    {
        public NRModRoadItem(TestPoint firstPoint, string fileName, NRModRoadCondition cond)
            : base(firstPoint, fileName, cond)
        {
            this.roadCond = cond;
        }

        private NRModRoadCondition roadCond { get; set; }
        private readonly List<NRModRoadTestPoint> mrTestPoints = new List<NRModRoadTestPoint>();

        // 道路序号
        public int SN
        {
            get;
            set;
        }

        // 路段采样点能匹配到的小区个数
        public int CellCount
        {
            get;
            private set;
        }

        // 路段采样点能匹配到的主服个数
        public int MainCellCount
        {
            get;
            private set;
        }

        // 路段采样点能匹配到的邻区个数
        public int NbCellCount
        {
            get;
            private set;
        }

        // 被干扰的主服个数
        public int InterMainCellCount
        {
            get;
            private set;
        }

        // 干扰主服的邻区个数
        public int InterNbCellCount
        {
            get;
            private set;
        }

        // 发生干扰的采样点个数
        public int InterSampleCount
        {
            get;
            private set;
        }

        // 发生干扰的采样点比例
        public double InterSampleRate
        {
            get { return SampleCount == 0 ? 0 : 1d * InterSampleCount / SampleCount; }
        }

        public List<NRModCellItem> InterCellList
        {
            get;
            private set;
        }





        protected override void ProcessInfo()
        {
            mrTestPoints.Clear();
            Dictionary<NRCell, int[]> cellSampleCountDic = new Dictionary<NRCell, int[]>();

            foreach (TestPoint tp in TestPoints)
            {
                NRModRoadTestPoint mrPt = new NRModRoadTestPoint(tp);
                mrTestPoints.Add(mrPt);
                if (mrPt.MainCell == null)
                {
                    continue;
                }

                NRCell mainCell = mrPt.MainCell;
                if (!cellSampleCountDic.ContainsKey(mainCell))
                {
                    cellSampleCountDic.Add(mainCell, new int[2] { 0, 0 });
                }
                ++cellSampleCountDic[mainCell][0]; // 主服采样点数加1

                foreach (NRCell nbCell in mrPt.NbCellList)
                {
                    if (!cellSampleCountDic.ContainsKey(nbCell))
                    {
                        cellSampleCountDic.Add(nbCell, new int[2] { 0, 0 });
                    }
                    ++cellSampleCountDic[nbCell][1]; // 邻区采样点数加1
                }
            }

            this.CellCount = cellSampleCountDic.Count;
            this.MainCellCount = this.NbCellCount = 0;
            foreach (int[] cnts in cellSampleCountDic.Values)
            {
                this.MainCellCount += cnts[0] > 0 ? 1 : 0;
                this.NbCellCount += cnts[1] > 0 ? 1 : 0;
            }

            cellSampleCountDic.Clear();
        }

        public override void Clear()
        {
            if (InterCellList != null)
            {
                InterCellList.Clear();
            }
        }

        public void FilterInterfere(NRModInterfereCondition interfereCond)
        {
            this.InterSampleCount = this.InterMainCellCount = this.InterNbCellCount = 0;
            Dictionary<NRCell, int[]> cellSampleCountDic = new Dictionary<NRCell, int[]>();
            Dictionary<NRCell, NRModCellItem> cellItemDic = new Dictionary<NRCell, NRModCellItem>();
            Dictionary<NRCell, double> interNbCellDic = new Dictionary<NRCell, double>();

            foreach (NRModRoadTestPoint mrPt in mrTestPoints)
            {
                List<NRModInterfereCell> interCells = null;
                if (mrPt.MainCell == null)
                {
                    continue;
                }

                // 干扰邻区列表
                interNbCellDic.Clear();
                bool isInterPoint = IsInterferePoint(mrPt, interfereCond, out interCells);
                if (isInterPoint)
                {
                    foreach (NRModInterfereCell interCell in interCells)
                    {
                        //interNbCellDic.Add(interCell.LteCell, interCell.Distance);
                        interNbCellDic[interCell.Cell] = interCell.Distance; // 同一采样点有可能出现两个频点完全一样的情况
                    }
                }

                // 主服
                NRModCellItem mainCellItem = dealMainCell(cellItemDic, mrPt, isInterPoint);
                if (mrPt.NbCellCount == 0)
                {
                    continue;
                }

                // 所有邻区
                dealNBCell(cellItemDic, interNbCellDic, mrPt, mainCellItem);
                if (!isInterPoint)
                {
                    continue; // 该点没有发生干扰
                }

                // 干扰统计
                caculateInterfereInfo(cellSampleCountDic, mrPt, interCells);
            }
            dealResultCount(cellSampleCountDic);

            cellSampleCountDic.Clear();
            CalcResult(cellItemDic);
            cellItemDic.Clear();
        }

        private void dealResultCount(Dictionary<NRCell, int[]> cellSampleCountDic)
        {
            foreach (int[] cnts in cellSampleCountDic.Values)
            {
                this.InterMainCellCount += cnts[0] > 0 ? 1 : 0;
                this.InterNbCellCount += cnts[1] > 0 ? 1 : 0;
            }
        }

        private static NRModCellItem dealMainCell(Dictionary<NRCell, NRModCellItem> cellItemDic, NRModRoadTestPoint mrPt, bool isInterPoint)
        {
            NRModCellItem mainCellItem = null;
            if (!cellItemDic.TryGetValue(mrPt.MainCell, out mainCellItem))
            {
                mainCellItem = new NRModCellItem(mrPt.MainCell);
                cellItemDic.Add(mrPt.MainCell, mainCellItem);
            }
            mainCellItem.AddSample(mrPt.Rsrp, mrPt.Sinr, mrPt.PdcpDl, mrPt.PdcpUl, false, isInterPoint);
            return mainCellItem;
        }

        private static void dealNBCell(Dictionary<NRCell, NRModCellItem> cellItemDic, Dictionary<NRCell, double> interNbCellDic, NRModRoadTestPoint mrPt, NRModCellItem mainCellItem)
        {
            for (int i = 0; i < mrPt.NbCellCount; ++i)
            {
                NRCell nbCell = mrPt.NbCellList[i];
                NRModCellItem nbCellItem = null;
                if (!cellItemDic.TryGetValue(nbCell, out nbCellItem))
                {
                    nbCellItem = new NRModCellItem(nbCell);
                    cellItemDic.Add(nbCell, nbCellItem);
                }

                bool isInterNb = interNbCellDic.ContainsKey(nbCell);

                nbCellItem.AddSample(mrPt.NbRsrpList[i], mrPt.NbSinrList[i], null, null, isInterNb, false);
                mainCellItem.AddNbCell(nbCellItem, isInterNb);
            }
        }

        private void caculateInterfereInfo(Dictionary<NRCell, int[]> cellSampleCountDic, NRModRoadTestPoint mrPt, List<NRModInterfereCell> interCells)
        {
            ++InterSampleCount;
            if (!cellSampleCountDic.ContainsKey(mrPt.MainCell))
            {
                cellSampleCountDic.Add(mrPt.MainCell, new int[] { 0, 0 });
            }
            ++cellSampleCountDic[mrPt.MainCell][0]; // 主服被干扰次数加1，即干扰主服采样点数加1
            foreach (NRModInterfereCell interCell in interCells)
            {
                if (!cellSampleCountDic.ContainsKey(interCell.Cell))
                {
                    cellSampleCountDic.Add(interCell.Cell, new int[] { 0, 0 });
                }
                ++cellSampleCountDic[interCell.Cell][1]; // 邻区中干扰主服的采样点数加1
            }
        }

        private bool IsInterferePoint(NRModRoadTestPoint mrPt, NRModInterfereCondition interfereCond, out List<NRModInterfereCell> interCells)
        {
            interCells = null;
            if (mrPt.NbCellCount == 0)
            {
                return false;
            }

            List<NRCell> nbCells = new List<NRCell>();
            for (int i = 0; i < mrPt.NbCellList.Count; ++i)
            {
                if (mrPt.Rsrp - mrPt.NbRsrpList[i] <= interfereCond.RxlevDiff)
                {
                    nbCells.Add(mrPt.NbCellList[i]);
                }
            }

            interCells = NRModInterferer.Instance.Stat(mrPt.MainCell, nbCells, interfereCond);
            return interCells.Count > 0;
        }

        private void CalcResult(Dictionary<NRCell, NRModCellItem> cellItemDic)
        {
            InterCellList = new List<NRModCellItem>();
            foreach (NRModCellItem cellItem in cellItemDic.Values)
            {
                cellItem.CalcResult();
                if (cellItem.InterNbCellCount > 0)
                {
                    InterCellList.Add(cellItem);
                }
            }
        }
    }

    public class NRModCellItem
    {
        public NRModCellItem(NRCell cell)
        {
            Cell = cell;
        }

        public NRCell Cell { get; private set; }

        public string CellName
        {
            get { return Cell.Name; }
        }

        public int Earfcn
        {
            get { return Cell.SSBARFCN; }
        }

        public int Pci
        {
            get { return Cell.PCI; }
        }

        public int Tac
        {
            get { return Cell.TAC; }
        }

        public long Nci
        {
            get { return Cell.NCI; }
        }

        public int CellID
        {
            get { return Cell.CellID; }
        }

        // 作为干扰邻区时与主服的区里
        public double Distance
        {
            get;
            set;
        }

        public string RsrpAvgStr
        {
            get;
            private set;
        }

        public string SinrAvgStr
        {
            get;
            private set;
        }

        public string PdcpDlAvgStr
        {
            get;
            private set;
        }

        public string PdcpUlAvgStr
        {
            get;
            private set;
        }

        // 邻区个数
        public int NbCellCount
        {
            get;
            private set;
        }

        // 产生干扰的邻区个数
        public int InterNbCellCount
        {
            get;
            private set;
        }

        // 产生干扰的邻区占比
        public double InterNbCellRate
        {
            get;
            private set;
        }

        // 采样点总数
        public int SampleCount
        {
            get;
            private set;
        }

        // 发生干扰的采样点数
        public int InterSampleCount
        {
            get;
            private set;
        }

        // 干扰其他主服的采样点个数
        public int InterOtherSampleCount
        {
            get;
            private set;
        }

        // 被邻区干扰的采样点个数
        public int OtherInterSampleCount
        {
            get;
            private set;
        }

        // 发生干扰的采样点个数
        public double InterSampleRate
        {
            get;
            private set;
        }

        // 受干扰的邻区列表
        public List<NRModCellItem> InterNbCellList
        {
            get;
            private set;
        }

        private double rsrpSum;
        private double sinrSum;
        private double pdcpDlSum;
        private double pdcpUlSum;
        private readonly Dictionary<NRModCellItem, bool> nbCellItemDic = new Dictionary<NRModCellItem, bool>();

        public void AddNbCell(NRModCellItem nbCellItem, bool isInterfere)
        {
            if (!nbCellItemDic.ContainsKey(nbCellItem))
            {
                nbCellItemDic.Add(nbCellItem, isInterfere);
            }
            else if (isInterfere) // 发生了干扰，更新状态
            {
                nbCellItemDic[nbCellItem] = isInterfere;
            }
        }

        public void AddSample(float? rsrp, float? sinr, double? pdcpDl, double? pdcpUl, bool isInterOther, bool isOtherInter)
        {
            ++SampleCount;
            rsrpSum += rsrp == null ? 0 : (float)rsrp;
            if (sinr > -50 && sinr < 50)
            {
                sinrSum += (float)sinr;
            }
            pdcpDlSum += pdcpDl == null ? 0 : (int)pdcpDl;
            pdcpUlSum += pdcpUl == null ? 0 : (int)pdcpUl;

            if (isInterOther)
            {
                ++InterOtherSampleCount;
                ++InterSampleCount;
            }
            if (isOtherInter)
            {
                ++OtherInterSampleCount;
                ++InterSampleCount;
            }
        }

        public void CalcResult()
        {
            NbCellCount = nbCellItemDic.Count;
            InterNbCellCount = 0;
            InterNbCellList = new List<NRModCellItem>();
            foreach (NRModCellItem nbCellItem in nbCellItemDic.Keys)
            {
                if (nbCellItemDic[nbCellItem])
                {
                    InterNbCellList.Add(nbCellItem);
                    nbCellItem.Distance = MathFuncs.GetDistance(Cell.Longitude, Cell.Latitude, nbCellItem.Cell.Longitude, nbCellItem.Cell.Latitude);
                    ++InterNbCellCount;
                }
            }
            InterNbCellRate = NbCellCount == 0 ? 0 : 1d * InterNbCellCount / NbCellCount;
            InterSampleRate = SampleCount == 0 ? 0 : 1d * InterSampleCount / SampleCount;

            RsrpAvgStr = SampleCount == 0 ? "" : string.Format("{0:F2}", rsrpSum / SampleCount);
            SinrAvgStr = SampleCount == 0 ? "" : string.Format("{0:F2}", sinrSum / SampleCount);
            PdcpDlAvgStr = SampleCount == 0 ? "" : string.Format("{0:F2}", pdcpDlSum / SampleCount);
            PdcpUlAvgStr = SampleCount == 0 ? "" : string.Format("{0:F2}", pdcpUlSum / SampleCount);

            nbCellItemDic.Clear();
        }
    }

    public class NRModRoadTestPoint
    {
        public NRModRoadTestPoint(TestPoint tp)
        {
            this.TestPoint = tp;
            Dictionary<NRCell, bool> nbCellDic = new Dictionary<NRCell, bool>();
            Rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
            Sinr = NRTpHelper.NrTpManager.GetSCellSinr(tp);
            PdcpDl = NRTpHelper.NrTpManager.GetPdcpDLMb(tp);
            PdcpUl = NRTpHelper.NrTpManager.GetPdcpULMb(tp);
            MainCell = tp.GetMainCell_NR();
            if (MainCell == null)
            {
                Tac = (int?)NRTpHelper.NrTpManager.GetTAC(tp);
                Nci = (long?)NRTpHelper.NrTpManager.GetNCI(tp);
                Earfcn = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
                Pci = (int?)NRTpHelper.NrTpManager.GetPCI(tp);
            }
            else
            {
                Tac = MainCell.TAC;
                Nci = MainCell.NCI;
                Earfcn = MainCell.SSBARFCN;
                Pci = MainCell.PCI;
                addNCellInfo(tp, nbCellDic);
            }
        }

        private void addNCellInfo(TestPoint tp, Dictionary<NRCell, bool> nbCellDic)
        {
            for (int i = 0; i < 16; ++i)
            {
                NRTpHelper.NRNCellType type = NRTpHelper.NrTpManager.GetNCellType(tp, i);
                if (type != NRTpHelper.NRNCellType.NCELL)
                {
                    continue;
                }

                float? nbRsrp = NRTpHelper.NrTpManager.GetNCellRsrp(tp, i);
                float? nbSinr = NRTpHelper.NrTpManager.GetNCellSinr(tp, i);
                int? nbEarfcn = (int?)NRTpHelper.NrTpManager.GetNEARFCN(tp, i);
                int? nbPci = (int?)NRTpHelper.NrTpManager.GetNPCI(tp, i);
                if (nbRsrp == null || nbEarfcn == null || nbPci == null)
                {
                    break;
                }

                NRCell nbCell = CellManager.GetInstance().GetNearestNRCellByARFCNPCI(tp.DateTime, nbEarfcn, nbPci, tp.Longitude, tp.Latitude);
                if (nbCell == null)
                {
                    break;
                }
                if (nbCellDic.ContainsKey(nbCell))
                {
                    continue;
                }

                NbCellList.Add(nbCell);
                NbRsrpList.Add(nbRsrp);
                NbSinrList.Add(nbSinr);
                NbEarfcnList.Add(nbEarfcn);
                NbPciList.Add(nbPci);
                nbCellDic.Add(nbCell, true);
                ++NbCellCount;
            }
        }

        public TestPoint TestPoint { get; set; }

        public NRCell MainCell { get; set; }
        public int? Tac { get; set; }
        public long? Nci { get; set; }
        public int? Earfcn { get; set; }
        public int? Pci { get; set; }
        public float? Rsrp { get; set; }
        public float? Sinr { get; set; }
        public double? PdcpDl { get; set; }
        public double? PdcpUl { get; set; }

        public int NbCellCount { get; set; } = 0;
        public List<NRCell> NbCellList { get; set; } = new List<NRCell>();
        public List<float?> NbRsrpList { get; set; } = new List<float?>();
        public List<float?> NbSinrList { get; set; } = new List<float?>();
        public List<int?> NbEarfcnList { get; set; } = new List<int?>();
        public List<int?> NbPciList { get; set; } = new List<int?>();
    }

    /// <summary>
    /// 道路查询条件
    /// </summary>
    public class NRModRoadCondition : ModRoadConditionBase
    {
        //Base中为基础条件,这里需根据不同业务添加拓展条件

        public NRModInterfereCondition FilterCond { get; set; } = new NRModInterfereCondition();
    }

    public enum ModType
    {
        Mod3 = 3,
        Mod4 = 4,
        Mod6 = 6
    }


    /// <summary>
    /// 结果过滤条件
    /// </summary>
    public class NRModInterfereCondition : NRModInterfereCond
    {
        public double InterfereRate { get; set; } = 0.1;
        public double RxlevDiff { get; set; } = 6;

        public int GetModeType(string type)
        {
            ModType modType;
            switch (type)
            {
                case "3":
                    modType = ModType.Mod3;
                    break;
                case "4":
                    modType = ModType.Mod4;
                    break;
                case "6":
                    modType = ModType.Mod6;
                    break;
                default:
                    modType = ModType.Mod3;
                    break;
            }
            return (int)modType;
        }
    }
}
