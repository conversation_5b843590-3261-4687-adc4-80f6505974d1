﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.Util;
using MasterCom.MControls;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public partial class GISMultiParamColorForm : BaseFormStyle
    {
        private TreeNode preSelectedNode = null;
        private MultiParamColorTranslator translator;

        public GISMultiParamColorForm()
        {
            InitializeComponent();

            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
            btnColor.Click += BtnColor_Click;

            btnAddCond.Click += BtnAddCond_Click;
            btnAddDefault.Click += BtnAddDefault_Click;
            btnAddInvalid.Click += BtnAddInvalid_Click;

            btnAddParam.Click += BtnAddParam_Click;
            btnSaveParam.Click += BtnSaveParam_Click;

            btnDelete.Click += BtnDelete_Click;
            btnUp.Click += BtnUp_Click;
            btnDown.Click += BtnDown_Click;

            cbxSystem.SelectedIndexChanged += CbxSystem_SelectedChanged;
            treeView.BeforeCollapse += TreeView_BeforeCollapse;
            treeView.AfterSelect += TreeView_AfterSelect;
            this.Load += Form_Load;

            LoadParameters();
        }

        // OwnFuncParam在按下确定后设置
        public string OwnFuncParam { get; set; } = "";

        public List<ColorRange> ColorRanges { get; set; }

        private void Form_Load(object sender, EventArgs e)
        {
            translator = new MultiParamColorTranslator(OwnFuncParam);
            FillTreeView();
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            OwnFuncParam = translator.ParamString;
            if (OwnFuncParam != null)
            {
                DialogResult = DialogResult.OK;
            }
            else
            {
                MessageBox.Show("部分着色条件未设置参数", "错误");
                DialogResult = DialogResult.Retry;
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void BtnColor_Click(object sender, EventArgs e)
        {
            if (ColorRanges == null)
            {
                ColorRanges = new List<ColorRange>();
            }

            MasterCom.Grid.ColorRangeMngDlg mngDlg = new MasterCom.Grid.ColorRangeMngDlg();
            mngDlg.MakeRangeModeOnly();
            mngDlg.FixMinMax(0, 100);
            mngDlg.RangeMode = true;
            mngDlg.FillColorRanges(ColorRanges);
            if (DialogResult.OK == mngDlg.ShowDialog(this))
            {
                ColorRanges = mngDlg.ColorRanges;
            }
        }

        /// <summary>
        /// 添加参数条件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BtnAddCond_Click(object sender, EventArgs e)
        {
            int idx = translator.AddArgumentCondition();
            treeView.Nodes.Add(GetTopLevelName(idx));
            treeView.SelectedNode = treeView.Nodes[idx];
        }

        private void BtnAddDefault_Click(object sender, EventArgs e)
        {
            if (translator.DefaultCondIndex != -1)
            {
                MessageBox.Show("已经存在缺省设置", "提示");
                return;
            }

            int idx = translator.AddDefaultCondition();
            treeView.Nodes.Add(GetTopLevelName(idx));
            MultiParamColorItem item = translator.ItemList[idx][0];
            treeView.Nodes[idx].Nodes.Add(item.ShowString);
            treeView.SelectedNode = treeView.Nodes[idx];
            treeView.SelectedNode.Expand();
        }

        private void BtnAddInvalid_Click(object sender, EventArgs e)
        {
            if (translator.InvalidCondIndex != -1)
            {
                MessageBox.Show("已经存在参数无效点设置", "提示");
                return;
            }

            int idx = translator.AddInvalidCondition();
            treeView.Nodes.Add(GetTopLevelName(idx));
            MultiParamColorItem item = translator.ItemList[idx][0];
            treeView.Nodes[idx].Nodes.Add(item.ShowString);
            treeView.SelectedNode = treeView.Nodes[idx];
            treeView.SelectedNode.Expand();
        }

        private void BtnAddParam_Click(object sender, EventArgs e)
        {
            if (numMinValue.Value >= numMaxValue.Value)
            {
                MessageBox.Show("参数取值范围设置不正确", "错误");
                return;
            }

            int selectedIndex = GetSelectedCondIndex();
            if (selectedIndex == -1)
            {
                MessageBox.Show("请选择参数条件", "提示");
                return;
            }

            MultiParamColorItem item = translator.AddArgumentItem(selectedIndex,
                cbxSystem.SelectedItem as string,
                cbxParam.SelectedItem as string,
                cbxIndex.Enabled ? Convert.ToInt32(cbxIndex.SelectedItem) : 0,
                (float)numMinValue.Value,
                (float)numMaxValue.Value);
            if (item == null)
            {
                MessageBox.Show("添加参数失败", "提示");
                return;
            }

            TreeNode selectedNode = treeView.Nodes[selectedIndex];
            selectedNode.Nodes.Add(item.ShowString);
            treeView.SelectedNode.Expand();
        }

        private void BtnSaveParam_Click(object sender, EventArgs e)
        {
            if (numMinValue.Value >= numMaxValue.Value)
            {
                MessageBox.Show("参数取值范围设置不正确", "错误");
                return;
            }

            int condIndex = 0;
            int itemIndex = 0;
            GetSelectedItemIndex(out condIndex, out itemIndex);
            if (condIndex == -1 || itemIndex == -1)
            {
                MessageBox.Show("请选择参数条件子项", "提示");
                return;
            }

            MultiParamColorItem item = translator.UpdateArgumentItem(
                condIndex, itemIndex,
                cbxSystem.SelectedItem as string,
                cbxParam.SelectedItem as string,
                cbxIndex.Enabled ? Convert.ToInt32(cbxIndex.SelectedItem) : 0,
                (float)numMinValue.Value,
                (float)numMaxValue.Value);
            if (item == null)
            {
                MessageBox.Show("更新参数失败", "提示");
                return;
            }
            treeView.SelectedNode.Text = item.ShowString;
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (treeView.SelectedNode.Level == 0)
            {
                if (MessageBox.Show("确定删除着色条件吗", "提示", MessageBoxButtons.OKCancel) != DialogResult.OK)
                {
                    return;
                }

                int selectedIndex = GetSelectedCondIndex();
                translator.RemoveCondition(selectedIndex);
                FillTreeView();
            }
            else
            {
                int condIndex = 0, itemIndex = 0;
                GetSelectedItemIndex(out condIndex, out itemIndex);
                if (!translator.RemoveArgumentItem(condIndex, itemIndex))
                {
                    MessageBox.Show("删除失败", "错误");
                    return;
                }
                treeView.SelectedNode.Remove();
                treeView.SelectedNode = treeView.Nodes[condIndex];
            }
        }

        private void BtnUp_Click(object sender, EventArgs e)
        {
            int selectedIndex = GetSelectedCondIndex();
            if (!translator.MoveConditionUp(selectedIndex))
            {
                return;
            }

            TreeNode node = treeView.Nodes[selectedIndex];
            treeView.Nodes.Remove(node);
            treeView.Nodes.Insert(selectedIndex - 1, node);

            string text = node.Text;
            node.Text = node.NextNode.Text;
            node.NextNode.Text = text;

            treeView.SelectedNode = node;
        }

        private void BtnDown_Click(object sender, EventArgs e)
        {
            int selectedIndex = GetSelectedCondIndex();
            if (!translator.MoveConditionDown(selectedIndex))
            {
                return;
            }

            TreeNode node = treeView.Nodes[selectedIndex];
            treeView.Nodes.Remove(node);
            treeView.Nodes.Insert(selectedIndex + 1, node);

            string text = node.Text;
            node.Text = node.PrevNode.Text;
            node.PrevNode.Text = text;

            treeView.SelectedNode = node;
        }

        private void CbxSystem_SelectedChanged(object sender, EventArgs e)
        {
            string sysName = cbxSystem.SelectedItem as string;
            if (sysName == null)
            {
                return;
            }

            cbxParam.BeginUpdate();
            cbxParam.SelectedIndexChanged -= CbxParam_SelectedChanged;
            cbxParam.Items.Clear();
            foreach (DTDisplayParameterInfo displayInfo in DTDisplayParameterManager.GetInstance()[sysName].DisplayParamInfos)
            {
                if ((displayInfo.Type & (int)DTDisplayParameterInfoType.Range) != 0)
                {
                    cbxParam.Items.Add(displayInfo.Name);
                }
            }
            cbxParam.SelectedIndexChanged += CbxParam_SelectedChanged;
            cbxParam.EndUpdate();

            if (cbxParam.Items.Count > 0)
            {
                cbxParam.SelectedIndex = 0;
            }
        }

        private void CbxParam_SelectedChanged(object sender, EventArgs e)
        {
            string sysName = cbxSystem.SelectedItem as string;
            string paramName = cbxParam.SelectedItem as string;
            if (sysName == null || paramName == null)
            {
                return;
            }

            cbxIndex.BeginUpdate();
            cbxIndex.Items.Clear();
            DTDisplayParameterInfo displayInfo = DTDisplayParameterManager.GetInstance()[sysName][paramName];
            if (displayInfo.ArrayBounds > 1)
            {
                for (int i = 0; i < displayInfo.ArrayBounds; i++)
                {
                    cbxIndex.Items.Add(i.ToString());
                }
            }
            cbxIndex.EndUpdate();

            if (cbxIndex.Items.Count > 0)
            {
                cbxIndex.Enabled = true;
                cbxIndex.SelectedIndex = 0;
            }
            else
            {
                cbxIndex.Enabled = false;
            }
        }

        private void TreeView_AfterSelect(object sender, TreeViewEventArgs e)
        {
            if (e.Node.Level != 0)
            {
                int condIndex, itemIndex;
                GetSelectedItemIndex(out condIndex, out itemIndex);
                MultiParamColorItem item = translator.ItemList[condIndex][itemIndex];

                if (item.ItemType == MultiParamColorType.Argument)
                {
                    cbxSystem.SelectedItem = item.SystemName;
                    cbxParam.SelectedItem = item.ParamName;
                    if (cbxIndex.Enabled)
                    {
                        cbxIndex.SelectedItem = item.ArrayIndex.ToString();
                    }
                    numMinValue.Value = (decimal)item.MinValue;
                    numMaxValue.Value = (decimal)item.MaxValue;
                }
            }
            FireSlectedNodeChanged();
        }

        private void TreeView_BeforeCollapse(object sender, TreeViewCancelEventArgs e)
        {
            e.Cancel = true;
        }

        private void LoadParameters()
        {
            foreach (DTDisplayParameterSystem sys in DTDisplayParameterManager.GetInstance().Systems)
            {
                cbxSystem.Items.Add(sys.Name);
            }
            if (cbxSystem.Items.Count > 0)
            {
                cbxSystem.SelectedIndex = 0;
            }
        }

        private void FillTreeView()
        {
            preSelectedNode = null;
            treeView.Nodes.Clear();
            for (int i = 0; i < translator.ItemList.Count; ++i)
            {
                TreeNode node = new TreeNode(GetTopLevelName(i));
                treeView.Nodes.Add(node);
                foreach (MultiParamColorItem item in translator.ItemList[i])
                {
                    node.Nodes.Add(item.ShowString);
                }
            }

            if (treeView.Nodes.Count > 0)
            {
                treeView.SelectedNode = treeView.Nodes[0];
                treeView.ExpandAll();
            }
            else
            {
                FireSlectedNodeChanged();
            }
        }

        /// <summary>
        /// 设置选中项的颜色及按钮的状态
        /// </summary>
        private void FireSlectedNodeChanged()
        {
            if (preSelectedNode != null)
            {
                preSelectedNode.BackColor = Color.White;
            }
            preSelectedNode = treeView.SelectedNode;

            if (treeView.SelectedNode == null)       // 没有节点选中
            {
                btnSaveParam.Enabled = false;
                btnDelete.Enabled = false;
                btnUp.Enabled = false;
                btnDown.Enabled = false;
                btnAddParam.Enabled = false;
                return;
            }
            treeView.SelectedNode.BackColor = Color.LightBlue;

            btnSaveParam.Enabled = true;
            btnDelete.Enabled = true;
            btnUp.Enabled = true;
            btnDown.Enabled = true;
            btnAddParam.Enabled = true;
            if (treeView.SelectedNode.Level == 0)
            {
                btnSaveParam.Enabled = false;
                int selectedIndex = GetSelectedCondIndex();
                if (selectedIndex == translator.DefaultCondIndex || selectedIndex == translator.InvalidCondIndex)
                {
                    btnAddParam.Enabled = false;
                }
            }
            else
            {
                btnUp.Enabled = false;
                btnDown.Enabled = false;
                btnAddParam.Enabled = false;
                int condIndex, itemIndex;
                GetSelectedItemIndex(out condIndex, out itemIndex);
                if (condIndex == translator.DefaultCondIndex || condIndex == translator.InvalidCondIndex)
                {
                    btnSaveParam.Enabled = false;
                    btnDelete.Enabled = false;
                }
            }
        }

        private int GetSelectedCondIndex()
        {
            return treeView.Nodes.IndexOf(treeView.SelectedNode);
        }

        private void GetSelectedItemIndex(out int condIndex, out int itemIndex)
        {
            TreeNode parent = treeView.SelectedNode.Parent;
            condIndex = treeView.Nodes.IndexOf(parent);
            itemIndex = parent.Nodes.IndexOf(treeView.SelectedNode);
        }

        private string GetTopLevelName(int nodeIndex)
        {
            return "着色返回值" + (nodeIndex + 1);
        }
    }
}
