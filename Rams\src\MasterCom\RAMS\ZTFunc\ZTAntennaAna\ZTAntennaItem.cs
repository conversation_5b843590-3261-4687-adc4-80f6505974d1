﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 天线水平(垂直)信息
    /// </summary>
    public class ZTAntAngleItem
    {
        /// <summary>
        /// 原值RSRP
        /// </summary>
        public int IRsrp { get; set; }
        /// <summary>
        /// 平滑RSRP
        /// </summary>
        public double DRsrpNew { get; set; }
        /// <summary>
        /// SINR
        /// </summary>
        public int ISinr { get; set; }
        /// <summary>
        /// 覆盖指数
        /// </summary>
        public double DCover { get; set; }
        /// <summary>
        /// 通信距离
        /// </summary>
        public double DSampDist { get; set; }
        /// <summary>
        /// 采样点数目
        /// </summary>
        public int ISampNum { get; set; }
        /// <summary>
        /// SINR3
        /// </summary>
        public int ISinr3 { get; set; }
        /// <summary>
        /// RSSI
        /// </summary>
        public int IRssi { get; set; }
        /// <summary>
        /// RSRQ
        /// </summary>
        public int IRsrq { get; set; }
        /// <summary>
        /// RSRP0天线
        /// </summary>
        public int IRsrp0 { get; set; }
        /// <summary>
        /// RSRP1天线
        /// </summary>
        public int IRsrp1 { get; set; }
        /// <summary>
        /// 路损
        /// </summary>
        public int IPathlsos { get; set; }

        public ZTAntAngleItem()
        {
            IRsrp = 0;
            DRsrpNew = 0;
            ISinr = 0;

            DSampDist = 0;
            ISampNum = 0;

            ISinr3 = 0;
            IRssi = 0;
            IRsrq = 0;
            IRsrp0 = 0;
            IRsrp1 = 0;
            IPathlsos = 0;
        }
    }

    public class ZTAntMRBaseItem
    {
        public int iEnodebId { get; set; }
        public int iCellId { get; set; }
        public int iECI
        {
            get
            {
                return iEnodebId * 256 + iCellId;
            }
        }
        public int iSECI
        {
            get
            {
                int iSubCellId = iCellId % 10;
                return iEnodebId * 256 + iSubCellId;
            }
        }
        public int[] dataValue { get; set; }
        public int iSampleNum { get; set; }

        public ZTAntMRBaseItem()
        {
            iEnodebId = 0;
            iCellId = 0;
            dataValue = new int[150];
            iSampleNum = 0;
        }

        public int[] getSubValue(int iNum)
        {
            int[] tmpValue = new int[iNum];
            for (int i = 0; i < iNum; i++)
            {
                tmpValue[i] = dataValue[i];
            }
            return tmpValue;
        }
    }

    public class ZTAntMRPowerHeadRoom
    {
        public int iEnodebId { get; set; }
        public int iCellId { get; set; }
        public int iECI
        {
            get
            {
                return iEnodebId * 256 + iCellId;
            }
        }
        public int iSECI
        {
            get
            {
                int iSubCellId = iCellId % 10;
                return iEnodebId * 256 + iSubCellId;
            }
        }
        public int[] powerValue { get; set; }
        public int iSampleNum { get; set; }

        public ZTAntMRPowerHeadRoom()
        {
            iEnodebId = 0;
            iCellId = 0;
            powerValue = new int[64];
            iSampleNum = 0;
        }
    }

    public class ZTAntMRRsrp
    {
        public int iEnodebId { get; set; }
        public int iCellId { get; set; }
        public int iECI
        {
            get
            {
                return iEnodebId * 256 + iCellId;
            }
        }
        public int iSECI
        {
            get
            {
                int iSubCellId = iCellId % 10;
                return iEnodebId * 256 + iSubCellId;
            }
        }
        public int[] rsrpValue { get; set; }
        public int iSampleNum { get; set; }

        public ZTAntMRRsrp()
        {
            iEnodebId = 0;
            iCellId = 0;
            rsrpValue = new int[48];
            iSampleNum = 0;
        }
    }

    public class ZTAntMRAoa
    {
        public int iEnodebId { get; set; }
        public int iCellId { get; set; }
        public int iECI
        {
            get
            {
                return iEnodebId * 256 + iCellId;
            }
        }
        public int iSECI
        {
            get
            {
                int iSubCellId = iCellId % 10;
                return iEnodebId * 256 + iSubCellId;
            }
        }
        public int[] aoaValue { get; set; }
        public int iSampleNum { get; set; }

        public ZTAntMRAoa()
        {
            iEnodebId = 0;
            iCellId = 0;
            aoaValue = new int[72];
            iSampleNum = 0;
        }
    }

    public class ZTAntMRSinrUl
    {
        public int iEnodebId { get; set; }
        public int iCellId { get; set; }
        public int iECI
        {
            get
            {
                return iEnodebId * 256 + iCellId;
            }
        }
        public int iSECI
        {
            get
            {
                int iSubCellId = iCellId % 10;
                return iEnodebId * 256 + iSubCellId;
            }
        }
        public int[] sinrValue { get; set; }
        public int iSampleNum { get; set; }

        public ZTAntMRSinrUl()
        {
            iEnodebId = 0;
            iCellId = 0;
            sinrValue = new int[37];
            iSampleNum = 0;
        }
    }

    public class ZTAntMRRttdAoa
    {
        public int iEnodebId { get; set; }
        public int iCellId { get; set; }
        public int iECI
        {
            get
            {
                return iEnodebId * 256 + iCellId;
            }
        }
        public int iSECI
        {
            get
            {
                int iSubCellId = iCellId % 10;
                return iEnodebId * 256 + iSubCellId;
            }
        }
        public int[] rttdAoaValue { get; set; }
        public int iSampleNum { get; set; }

        public ZTAntMRRttdAoa()
        {
            iEnodebId = 0;
            iCellId = 0;
            rttdAoaValue = new int[142];
            iSampleNum = 0;
        }
    }

    public class ZTAntGsmTdCellInfo
    {
        public string 基站BTS名称 { get; set; }
        public double 基站经度 { get; set; }
        public double 基站纬度 { get; set; }
        public string 基站室分类型 { get; set; }
        public string 小区名称 { get; set; }
        public int 小区LAC { get; set; }
        public int 小区CI { get; set; }
        public int 小区频点 { get; set; }
        public int 小区扰码 { get; set; }
        public double 天线经度 { get; set; }
        public double 天线纬度 { get; set; }
        public int 天线方向角 { get; set; }
        public int 天线下倾角 { get; set; }
        public int 天线挂高 { get; set; }
    }

    public class GsmAntParaItem
    {
        public int iFunc { get; set; }
        public AntTimeCfg timeCfg { get; set; }
        public int 序号 { get; set; }
        public string 地市 { get; set; }
        public string 基站名称 { get; set; }
        public string 小区名称 { get; set; }
        public int 小区LAC { get; set; }
        public int 小区CI { get; set; }
        public string 覆盖类型 { get; set; }
        public string 场景类型 { get; set; }
        public int 小区主频 { get; set; }
        public double 天线经度 { get; set; }
        public double 天线纬度 { get; set; }
        public int 方位角 { get; set; }
        public int 下倾角 { get; set; }
        public int 挂高 { get; set; }
        public string 分析结果 { get; set; }
        public string 告警状态 { get; set; }

        public string 采样点总数_路测 { get; set; }
        public string 覆盖率Rxlev90_路测 { get; set; }
        public string 覆盖率Rxlev94_路测 { get; set; }
        public string Rxlev均值_路测 { get; set; }
        public string RxQual5_7级占比_路测 { get; set; }
        public string RxQual平均_路测 { get; set; }
        public string C_I均值_路测 { get; set; }
        public string 小区平均通信距离_路测 { get; set; }
        public string 范围内采样点比例_60_路测 { get; set; }
        public string 范围内平均覆盖距离_60_路测 { get; set; }
        public string 范围内覆盖率Rxlev90_60_路测 { get; set; }
        public string 范围内平均Rxlev_60_路测 { get; set; }
        public string 范围内采样点比例_150_路测 { get; set; }
        public string 范围内平均覆盖距离_150_路测 { get; set; }
        public string 范围内覆盖率Rxlev90_150_路测 { get; set; }
        public string 范围内平均Rxlev_150_路测 { get; set; }
        public string 范围内采样点比例_180_路测 { get; set; }
        public string 范围内平均覆盖距离_180_路测 { get; set; }
        public string 范围内覆盖率Rxlev90_180_路测 { get; set; }
        public string 范围内平均Rxlev_180_路测 { get; set; }
        public string 前后比_路测 { get; set; }

        public string 采样点总数_扫频 { get; set; }
        public string 覆盖率Rxlev90_扫频 { get; set; }
        public string 覆盖率Rxlev94_扫频 { get; set; }
        public string Rxlev均值_扫频 { get; set; }
        public string C_I均值_扫频 { get; set; }
        public string 小区平均通信距离_扫频 { get; set; }
        public string 范围内采样点比例_60_扫频 { get; set; }
        public string 范围内平均覆盖距离_60_扫频 { get; set; }
        public string 范围内覆盖率Rxlev90_60_扫频 { get; set; }
        public string 范围内平均Rxlev_60_扫频 { get; set; }
        public string 范围内采样点比例_150_扫频 { get; set; }
        public string 范围内平均覆盖距离_150_扫频 { get; set; }
        public string 范围内覆盖率Rxlev90_150_扫频 { get; set; }
        public string 范围内平均Rxlev_150_扫频 { get; set; }
        public string 范围内采样点比例_180_扫频 { get; set; }
        public string 范围内平均覆盖距离_180_扫频 { get; set; }
        public string 范围内覆盖率Rxlev90_180_扫频 { get; set; }
        public string 范围内平均Rxlev_180_扫频 { get; set; }
        public string 前后比_扫频 { get; set; }

        public ZTGsmAntenna.CellAngleData dtCellAngleData { get; set; }
        public ZTGsmAntenna.CellAngleData scanCellAngleData { get; set; }
        public ZTGsmAntenna.CellInfoItem dtCellInfoItem { get; set; }
        public ZTGsmAntenna.CellInfoItem scanCellInfoItem { get; set; }
        public CellGsmPara paraCellInfoIem { get; set; }
        public ZTAntGsmMrCover antGsmMrData { get; set; }
    }

    public class TdAntParaItem
    {
        public int iFunc { get; set; }
        public AntTimeCfg timeCfg { get; set; }
        public int 序号 { get; set; }
        public string 地市 { get; set; }
        public string 基站名称 { get; set; }
        public string 小区名称 { get; set; }
        public int 小区LAC { get; set; }
        public int 小区CI { get; set; }
        public string 覆盖类型 { get; set; }
        public string 场景类型 { get; set; }
        public int 小区主频 { get; set; }
        public double 天线经度 { get; set; }
        public double 天线纬度 { get; set; }
        public int 方位角 { get; set; }
        public int 下倾角 { get; set; }
        public int 挂高 { get; set; }
        public string 分析结果 { get; set; }
        public string 告警状态 { get; set; }

        public string 采样点总数_路测 { get; set; }
        public string 覆盖率PCCPCHRSCP90_C_I_3_路测 { get; set; }
        public string 覆盖率PCCPCHRSCP95_C_I_3_路测 { get; set; }
        public string 覆盖率DPCHRSCP90_C_I_3_路测 { get; set; }
        public string 覆盖率DPCHRSCP95_C_I_3_路测 { get; set; }
        public string 小区PCCPCHRSCP均值_路测 { get; set; }
        public string 小区PCCPCH_C2I_3占比_路测 { get; set; }
        public string 小区PCCPCH_C_I平均_路测 { get; set; }
        public string 小区平均通信距离_路测 { get; set; }

        public string 范围内覆盖率PCCPCHRSCPC2I_3_150_路测 { get; set; }
        public string 范围内覆盖率PCCPCHRSCPC2I_3_150_扫频 { get; set; }
        public string 范围内采样点比例_60_路测 { get; set; }
        public string 范围内平均覆盖距离_60_路测 { get; set; }
        public string 范围内覆盖率PCCPCHRSCP90_C_I_3_60_路测 { get; set; }
        public string 范围内平均PCCPCHRSCP_60_路测 { get; set; }
        public string 范围内采样点比例_150_路测 { get; set; }
        public string 范围内平均覆盖距离_150_路测 { get; set; }
        public string 范围内覆盖率PCCPCHRSCP90_C_I_3_150_路测 { get; set; }
        public string 范围内平均PCCPCHRSCP_150_路测 { get; set; }
        public string 范围内采样点比例_180_路测 { get; set; }
        public string 范围内平均覆盖距离_180_路测 { get; set; }
        public string 范围内覆盖率PCCPCHRSCP90_C_I_3_180_路测 { get; set; }
        public string 范围内平均PCCPCHRSCP_180_路测 { get; set; }
        public string 前后比_路测 { get; set; }

        public string 采样点总数_扫频 { get; set; }
        public string 覆盖率PCCPCHRSCP90_C_I_3_扫频 { get; set; }
        public string 覆盖率PCCPCHRSCP95_C_I_3_扫频 { get; set; }
        public string 小区PCCPCHRSCP均值_扫频 { get; set; }
        public string 小区PCCPCH_C2I_3占比_扫频 { get; set; }
        public string 小区PCCPCH_C_I平均_扫频 { get; set; }
        public string 小区平均通信距离_扫频 { get; set; }
        public string 范围内采样点比例_60_扫频 { get; set; }
        public string 范围内平均覆盖距离_60_扫频 { get; set; }
        public string 范围内覆盖率PCCPCHRSCP90_C_I_3_60_扫频 { get; set; }
        public string 范围内平均PCCPCHRSCP_60_扫频 { get; set; }
        public string 范围内采样点比例_150_扫频 { get; set; }
        public string 范围内平均覆盖距离_150_扫频 { get; set; }
        public string 范围内覆盖率PCCPCHRSCP90_C_I_3_150_扫频 { get; set; }
        public string 范围内平均PCCPCHRSCP_150_扫频 { get; set; }
        public string 范围内采样点比例_180_扫频 { get; set; }
        public string 范围内平均覆盖距离_180_扫频 { get; set; }
        public string 范围内覆盖率PCCPCHRSCP90_C_I_3_180_扫频 { get; set; }
        public string 范围内平均PCCPCHRSCP_180_扫频 { get; set; }
        public string 前后比_扫频 { get; set; }

        public ZTTdAntenna.CellAngleData dtCellAngleData { get; set; }
        public ZTTdAntenna.CellAngleData scanCellAngleData { get; set; }
        public ZTTdAntenna.CellInfoItem dtCellInfoItem { get; set; }
        public ZTTdAntenna.CellInfoItem scanCellInfoItem { get; set; }
        public CellTdPara paraCellInfoIem { get; set; }
        public CellTdMRKpi mrCellInfoIem { get; set; }
        public GTCellMrData mrData { get; set; }
        public ZTTDAntMRAna.TdCellMRData tdMrData { get; set; } = new ZTTDAntMRAna.TdCellMRData();
    }

    public class ZTAntGsmMrCover
    {
        public int iLac { get; set; }
        public int iCi { get; set; }
        public double 上行100覆盖率 { get; set; }
        public string rate上行100覆盖率
        {
            get
            {
                return Math.Round(上行100覆盖率 * 100, 2) + "%";
            }
        }
        public double 上行95覆盖率 { get; set; }
        public string rate上行95覆盖率
        {
            get
            {
                return Math.Round(上行95覆盖率 * 100, 2) + "%";
            }
        }
        public double 上行90覆盖率 { get; set; }
        public string rate上行90覆盖率
        {
            get
            {
                return Math.Round(上行90覆盖率 * 100, 2) + "%";
            }
        }
        public double 上行85覆盖率 { get; set; }
        public string rate上行85覆盖率
        {
            get
            {
                return Math.Round(上行85覆盖率 * 100, 2) + "%";
            }
        }
        public double 下行100覆盖率 { get; set; }
        public string rate下行100覆盖率
        {
            get
            {
                return Math.Round(下行100覆盖率 * 100, 2) + "%";
            }
        }
        public double 下行95覆盖率 { get; set; }
        public string rate下行95覆盖率
        {
            get
            {
                return Math.Round(下行95覆盖率 * 100, 2) + "%";
            }
        }
        public double 下行90覆盖率 { get; set; }
        public string rate下行90覆盖率
        {
            get
            {
                return Math.Round(下行90覆盖率 * 100, 2) + "%";
            }
        }
        public double 下行85覆盖率 { get; set; }
        public string rate下行85覆盖率
        {
            get
            {
                return Math.Round(下行85覆盖率 * 100, 2) + "%";
            }
        }
        public double 上行567质量占比 { get; set; }
        public string rate上行567质量占比
        {
            get
            {
                return Math.Round(上行567质量占比 * 100, 2) + "%";
            }
        }
        public double 下行567质量占比 { get; set; }
        public string rate下行567质量占比
        {
            get
            {
                return Math.Round(下行567质量占比 * 100, 2) + "%";
            }
        }
    }
    /// <summary>
    /// 小区主键
    /// </summary>
    public class LaiKey
    {
        public int ILac { get; set; }
        public int ICi { get; set; }

        public LaiKey()
        { }

        public LaiKey(int iLac, int iCi)
        {
            this.ILac = iLac;
            this.ICi = iCi;
        }

        public override bool Equals(object obj)
        {
            LaiKey other = obj as LaiKey;
            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.ILac.Equals(other.ILac) && this.ICi.Equals(other.ICi));
        }

        public override int GetHashCode()
        {
            return this.ICi.GetHashCode();
        }
    }


}
