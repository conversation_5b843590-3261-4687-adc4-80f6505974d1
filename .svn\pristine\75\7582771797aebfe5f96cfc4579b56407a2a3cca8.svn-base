﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTWeakCoverByEventSetForm : BaseDialog
    {
        public ZTWeakCoverByEventSetForm()
        {
            InitializeComponent();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
        public void GetSetCond(ref int iTimeSpan, ref int iDistanceSpan)
        {
            iTimeSpan = Convert.ToInt32(numTimeSpan.Value.ToString());
            iDistanceSpan = Convert.ToInt32(numDistanceSpan.Value.ToString());
        }
    }
}
