﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    class PingPangFile
    {
        public PingPangFile(DTFileDataManager fileManager)
        {
            this.Cells = new List<PingPangICell>();
            this.Pairs = new List<PingPangPair>();
            this.FileName = fileManager.FileName;
        }

        public int SN
        {
            get;
            set;
        }

        public string FileName
        {
            get;
            private set;
        }

        public int PairCount
        {
            get { return Pairs.Count; }
        }

        public int CellCount
        {
            get { return Cells.Count; }
        }

        public List<PingPangPair> Pairs
        {
            get;
            private set;
        }

        public List<PingPangICell> Cells
        {
            get;
            private set;
        }

        public void CalcResult()
        {
            Dictionary<string, PingPangICell> tokenDic = new Dictionary<string, PingPangICell>();
            foreach (PingPangPair pair in Pairs)
            {
                foreach (PingPangEvent evt in pair.Events)
                {
                    ICell iSrcCell = evt.SrcCell;
                    if (!tokenDic.ContainsKey(iSrcCell.Token))
                    {
                        tokenDic.Add(iSrcCell.Token, new PingPangICell(iSrcCell));
                    }
                    ++tokenDic[iSrcCell.Token].Count;

                    ICell iTarCell = evt.TarCell;
                    if (!tokenDic.ContainsKey(iTarCell.Token))
                    {
                        tokenDic.Add(iTarCell.Token, new PingPangICell(iTarCell));
                    }
                    ++tokenDic[iTarCell.Token].Count;
                }
            }

            this.Cells.Clear();
            this.Cells.AddRange(tokenDic.Values);
        }

        public List<BackgroundResult> CellsConvertToBgResultList(FileInfo file, int funcId)
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            int maxCellCount = 10;//每10条转换成一个BackgroundResult，防止image过大
            while (Cells.Count > maxCellCount)
            {
                bgResultList.Add(cellsConvertToBackgroundResult(Cells.GetRange(0, maxCellCount), file, funcId));

                Cells.RemoveRange(0, maxCellCount);
            }
            if (Cells.Count > 0)
            {
                bgResultList.Add(cellsConvertToBackgroundResult(Cells, file, funcId));
            }
            return bgResultList;
        }

        private BackgroundResult cellsConvertToBackgroundResult(List<PingPangICell> pList, FileInfo file, int funcId)
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.SubFuncID = funcId;
            bgResult.ProjectString = MasterCom.RAMS.BackgroundFunc.BackgroundFuncBaseSetting.GetInstance().projectType;
            bgResult.StrDesc = "切换小区";
            bgResult.CellType = BackgroundCellType.LTE;

            if (file != null)
            {
                bgResult.FileID = file.ID;
                bgResult.FileName = file.Name;
                bgResult.ISTime = file.BeginTime;
                bgResult.IETime = file.BeginTime;
            }

            bgResult.AddImageValue(pList.Count);
            foreach (PingPangICell ppCell in pList)
            {
                bgResult.AddImageValue(ppCell.Name);
                bgResult.AddImageValue(ppCell.ILac);
                bgResult.AddImageValue(ppCell.ICi);
                bgResult.AddImageValue(ppCell.Count);
            }

            return bgResult;
        }
    }

    class PingPangPair
    {
        public PingPangPair(PingPangEvent evtA, PingPangEvent evtB)
        {
            this.Events = new List<PingPangEvent>() { evtA, evtB };
            this.Desc = string.Format("[{0}] -> [{1}] -> [{0}]", evtA.SrcCell.Name, evtA.TarCell.Name);
            this.Interval = Math.Round((evtB.Time - evtA.Time).TotalSeconds, 2).ToString();
        }

        public int SN
        {
            get;
            set;
        }

        public string Desc
        {
            get;
            private set;
        }

        public string Interval
        {
            get;
            private set;
        }

        public List<PingPangEvent> Events
        {
            get;
            private set;
        }
        public BackgroundResult ConvertToBackgroundResult(FileInfo file, int pairCount)
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.StrDesc = "切换序列";
            bgResult.CellType = BackgroundCellType.LTE;
            if (Events.Count > 0)
            {
                bgResult.ISTime = Events[0].Evt.Time;
                bgResult.IETime = Events[Events.Count - 1].Evt.Time;
                bgResult.LongitudeMid = Events[Events.Count / 2].Evt.Longitude;
                bgResult.LatitudeMid = Events[Events.Count / 2].Evt.Latitude;
            }

            if (file != null)
            {
                bgResult.FileID = file.ID;
                bgResult.FileName = file.Name;
            }

            bgResult.AddImageValue(pairCount);//乒乓切换组数
            bgResult.AddImageValue(Desc);//切换小区
            bgResult.AddImageValue(Interval);//切换间隔
            bgResult.AddImageValue(Events.Count);//切换间隔

            foreach (PingPangEvent ppEvt in Events)
            {
                bgResult.AddImageValue(ppEvt.TimeString);
                bgResult.AddImageValue(ppEvt.SrcCellName);
                bgResult.AddImageValue(ppEvt.TarCellName);
                bgResult.AddImageValue(ppEvt.BeforeRsrp == null ? float.NaN : (float)ppEvt.BeforeRsrp);
                bgResult.AddImageValue(ppEvt.AfterRsrp == null ? float.NaN : (float)ppEvt.AfterRsrp);
                bgResult.AddImageValue(ppEvt.BeforeSinr == null ? float.NaN : (float)ppEvt.BeforeSinr);
                bgResult.AddImageValue(ppEvt.AfterSinr == null ? float.NaN : (float)ppEvt.AfterSinr);
            }

            return bgResult;
        }
    }

    class PingPangEvent
    {
        public PingPangEvent(Event evt, DTFileDataManager fileManager)
        {
            this.Evt = evt;
            this.Time = evt.DateTime;
            this.TimeString = evt.DateTime.ToString("HH:mm:ss.fff");
            this.SetCells(evt);
            this.SetKpis(evt, fileManager);
        }

        public int SN
        {
            get;
            set;
        }

        public ICell SrcCell
        {
            get;
            private set;
        }

        public ICell TarCell
        {
            get;
            private set;
        }

        public string SrcCellName
        {
            get { return SrcCell.Name; }
        }

        public string TarCellName
        {
            get { return TarCell.Name; }
        }

        public Event Evt
        {
            get;
            private set;
        }

        public DateTime Time
        {
            get;
            private set;
        }

        public string TimeString
        {
            get;
            private set;
        }

        public double? BeforeRsrp
        {
            get;
            private set;
        }

        public double? AfterRsrp
        {
            get;
            private set;
        }

        public double? BeforeSinr
        {
            get;
            private set;
        }

        public double? AfterSinr
        {
            get;
            private set;
        }

        protected virtual double? GetRsrp(TestPoint tp)
        {
            float? rsrp = (float?)tp["lte_RSRP"];
            if (tp is LTEFddTestPoint)
            {
                rsrp = (float?)tp["lte_fdd_RSRP"];
            }
            if (rsrp != null)
            {
                return (float)rsrp;
            }
            return null;
        }

        protected virtual double? GetSinr(TestPoint tp)
        {
            float? sinr = (float?)tp["lte_SINR"];
            if (tp is LTEFddTestPoint)
            {
                sinr = (float?)tp["lte_fdd_SINR"];
            }
            if (sinr != null)
            {
                return (float)sinr;
            }
            return null;
        }

        private void SetCells(Event evt)
        {
            SrcCell = evt.GetSrcCell();
            if (SrcCell == null)
            {
                SrcCell = new UnknowCell((int)evt["LAC"], (int)evt["CI"]);
            }

            TarCell = evt.GetTargetCell();
            if (TarCell == null)
            {
                TarCell = new UnknowCell((int)evt["TargetLAC"], (int)evt["TargetCI"]);
            }
        }

        private void SetKpis(Event evt, DTFileDataManager fileManager)
        {
            int tpCnt = 1;
            int preIdx = BinSearchPreSN(fileManager.TestPoints, evt.SN);

            double? rsrp = null, sinr = null;
            if (preIdx >= 0) // before exists
            {
                int startIdx = preIdx - tpCnt + 1;
                if (startIdx < 0)
                {
                    startIdx = 0;
                }

                GetKpis(fileManager.TestPoints.GetRange(startIdx , tpCnt), out rsrp, out sinr);
                this.BeforeRsrp = rsrp;
                this.BeforeSinr = sinr;
            }
            
            if (preIdx + 1 <= fileManager.TestPoints.Count - 1) // after exists
            {
                GetKpis(fileManager.TestPoints.GetRange(preIdx + 1, tpCnt), out rsrp, out sinr);
                this.AfterRsrp = rsrp;
                this.AfterSinr = sinr;
            }
        }

        private void GetKpis(List<TestPoint> tpList, out double? rsrp, out double? sinr)
        {
            int rsrpCnt = 0, sinrCnt = 0;
            double rsrpSum = 0, sinrSum = 0;
            foreach (TestPoint tp in tpList)
            {
                double? tmpRsrp = GetRsrp(tp);
                if (tmpRsrp != null)
                {
                    rsrpSum += (double)tmpRsrp;
                    ++rsrpCnt;
                }

                double? tmpSinr = GetSinr(tp);
                if (tmpSinr != null)
                {
                    sinrSum += (double)tmpSinr;
                    ++sinrCnt;
                }
            }

            rsrp = sinr = null;
            if (rsrpCnt != 0)
            {
                rsrp = Math.Round(rsrpSum / rsrpCnt, 2);
            }
            if (sinrCnt != 0)
            {
                sinr = Math.Round(sinrSum / sinrCnt, 2);
            }
        }

        /// <summary>
        /// 查找事件前一个采样点的索引下标
        /// 当采样点不存在时返回小于0的值
        /// </summary>
        /// <param name="tpList"></param>
        /// <param name="evtSn"></param>
        /// <returns></returns>
        private int BinSearchPreSN(List<TestPoint> tpList, int evtSn)
        {
            int left = 0, right = tpList.Count - 1;
            while (left < right)
            {
                int mid = (left + right) / 2;
                if (tpList[mid].SN > evtSn)
                {
                    right = mid - 1;
                }
                else if (tpList[mid].SN < evtSn)
                {
                    left = mid + 1;
                }
                else // ==
                {
                    throw (new Exception("发现采样点SN跟事件SN相等！"));
                }
            }

            // left == right
            // when tpList.Count == 0, left > right, then let exception throw
            return tpList[left].SN > evtSn ? left - 1 : left;
        }
    }

    class PingPangICell
    {
        public PingPangICell(ICell iCell)
        {
            int lac = 0, ci = 0;
            if (iCell == null)
            {
                throw (new Exception("小区为空"));
            }
            else if (iCell is LTECell)
            {
                lac = (iCell as LTECell).TAC;
                ci = (iCell as LTECell).ECI;
            }
            else if (iCell is TDCell)
            {
                lac = (iCell as TDCell).LAC;
                ci = (iCell as TDCell).CI;
            }
            else if (iCell is Cell)
            {
                lac = (iCell as Cell).LAC;
                ci = (iCell as Cell).CI;
            }
            else if (iCell is UnknowCell)
            {
                lac = (iCell as UnknowCell).LAC;
                ci = (int)(iCell as UnknowCell).CI;
            }

            SetValues(lac, ci, iCell.Name, iCell);
        }

        public int SN
        {
            get;
            set;
        }

        public int Count
        {
            get;
            set;
        }

        public ICell ICell
        {
            get;
            private set;
        }

        public string Name
        {
            get;
            private set;
        }

        public int ILac
        {
            get;
            private set;
        }

        public int ICi
        {
            get;
            private set;
        }

        private void SetValues(int lac, int ci, string name, ICell iCell)
        {
            this.ILac = lac;
            this.ICi = ci;
            this.Name = name;
            this.ICell = iCell;
        }
    }
}
