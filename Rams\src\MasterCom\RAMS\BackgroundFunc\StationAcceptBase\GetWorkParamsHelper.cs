﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    /// <summary>
    /// 获取单验工参
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public abstract class GetWorkParamsHelper<T> where T : new()
    {
        private static T instance { get; set; }
        public static T GetInstance()
        {
            if (instance == null)
            {
                instance = new T();
            }
            return instance;
        }

        public virtual StationAcceptWorkParams GetWorkParams(StationAcceptCondition condition)
        {
            StationAcceptWorkParams workParamSum;
            try
            {
                bool needUpLoad;
                List<string> readSuccessFileList;
                //1.从Excel读取工参
                StationAcceptWorkParams workParams = readParamsFromExcel(condition.ExcelPath, out readSuccessFileList, out needUpLoad);

                //2.工参入库
                upLoadWorkParams(workParams, condition, readSuccessFileList, needUpLoad);

                //3.查询待单验的工参
                workParamSum = readWorkParams(condition);
            }
            catch (Exception ex)
            {
                reportInfo("读取工参Excel异常" + ex.Message + Environment.NewLine + ex.Source + Environment.NewLine + ex.StackTrace);
                return null;
            }
            return workParamSum;
        }

        #region 从Excel读取工参
        protected virtual StationAcceptWorkParams readParamsFromExcel(string excelPath, out List<string> readSuccessFileList, out bool needUpLoad)
        {
            needUpLoad = false;
            reportInfo("正在读取单验工参Excel...");
            StationAcceptWorkParams workParams = getWorkParams();
            List<string> fileNameList = getFileNameList(excelPath);
            readSuccessFileList = new List<string>();
            if (fileNameList.Count > 0)
            {
                foreach (string excelFileName in fileNameList)
                {
                    bool isValid = getWorkParamFormExcel(excelPath, excelFileName, workParams);
                    if (isValid)
                    {
                        readSuccessFileList.Add(excelFileName);
                    }
                }
                needUpLoad = true;
            }

            return workParams;
        }

        protected abstract StationAcceptWorkParams getWorkParams();

        protected List<string> getFileNameList(string excelPath)
        {
            List<string> fileNameList = new List<string>();

            if (System.IO.Directory.Exists(excelPath))
            {
                //读取文件夹下的所有excel文件
                fileNameList = getAllExcelFromDir(excelPath);
            }
            else
            {
                reportInfo("未找到指定工参Excel路径");
            }

            return fileNameList;
        }

        protected List<string> getAllExcelFromDir(string dirName)
        {
            List<string> fileNameList = new List<string>();
            if (System.IO.Directory.Exists(dirName))
            {
                System.IO.DirectoryInfo dinfo = new System.IO.DirectoryInfo(dirName);
                System.IO.FileInfo[] fileLst = dinfo.GetFiles();
                foreach (System.IO.FileInfo file in fileLst)
                {
                    if (FilterHelper.Excel.Contains(file.Extension))
                    {
                        fileNameList.Add(file.FullName);
                    }
                }
            }

            return fileNameList;
        }

        protected bool getWorkParamFormExcel(string dirName, string fileName, StationAcceptWorkParams workParams)
        {
            reportInfo("正在读取" + fileName);
   
            try
            {
                System.Data.DataTable tb = getValidDataTable(dirName, fileName);
                if (tb == null)
                {
                    return false;
                }

                StringBuilder strbErrorInfo = new StringBuilder();
                int index = 0;
                foreach (System.Data.DataRow row in tb.Rows)
                {
                    index++;
                    try
                    {
                        setWorkParams(row, workParams);
                    }
                    catch(Exception e)
                    {
                        strbErrorInfo.AppendLine("第" + index + "行工参信息配置错误!   " + e.Message);
                        break;
                    }
                }
                if (strbErrorInfo.Length > 0)
                {
                    reportInfo(fileName + strbErrorInfo.ToString());
                    BcpFiles(dirName, fileName, true);
                    return false;
                }
                reportInfo("共成功读取" + index + "行数据");
            }
            catch (Exception ex)
            {
                reportInfo(ex.Message + Environment.NewLine + ex.Source + Environment.NewLine + ex.StackTrace);
                BcpFiles(dirName, fileName, true);
                return false;
            }
            return true;
        }

        private System.Data.DataTable getValidDataTable(string dirName, string fileName)
        {
            int reReadCount = 0;
            while (reReadCount < 6 && FileStatus.FileIsOpen(fileName) == 1)
            {
                System.Threading.Thread.Sleep(10000);
                reReadCount++;
            }

            System.Data.DataTable tb;
            using (System.Data.DataSet dataSet = ExcelNPOIManager.ImportFromExcel(fileName))
            {
                if (dataSet == null || dataSet.Tables.Count <= 0)
                {
                    BcpFiles(dirName, fileName, true);
                    reportInfo("Excel中无数据");
                    return null;
                }
                tb = dataSet.Tables[0];
                if (tb == null || tb.Rows.Count <= 0)
                {
                    BcpFiles(dirName, fileName, true);
                    reportInfo("表中无数据");
                    return null;
                }

                bool iaValid = judgeOtherInfo(tb);
                if (!iaValid)
                {
                    BcpFiles(dirName, fileName, true);
                    return null;
                }

                removeEmpty(tb);
            }

            return tb;
        }

        protected virtual bool judgeOtherInfo(System.Data.DataTable tb)
        {
            return true;
        }

        protected abstract void setWorkParams(System.Data.DataRow row, StationAcceptWorkParams workParams);

        protected void removeEmpty(System.Data.DataTable dt)
        {
            List<System.Data.DataRow> removelist = new List<System.Data.DataRow>();
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                bool rowdataisnull = true;
                for (int j = 0; j < dt.Columns.Count; j++)
                {
                    if (!string.IsNullOrEmpty(dt.Rows[i][j].ToString().Trim()))
                    {
                        rowdataisnull = false;
                    }
                }
                if (rowdataisnull)
                {
                    removelist.Add(dt.Rows[i]);
                }
            }
            for (int i = 0; i < removelist.Count; i++)
            {
                dt.Rows.Remove(removelist[i]);
            }
        }
        #endregion

        #region 上传读取的工参
        protected void upLoadWorkParams(StationAcceptWorkParams workParams, StationAcceptCondition condition, List<string> readSuccessFileList, bool needUpLoad)
        {
            if (!needUpLoad)
            {
                return;
            }

            reportInfo("正在上传工参数据...");
            bool saveInDBSuccess;
            try
            {
                saveInDBSuccess = upLoadWorkParams(workParams, condition);
            }
            catch(Exception ex)
            {
                reportInfo($"{ex.Message}{ex.StackTrace}");
                saveInDBSuccess = false;
            }
            if (saveInDBSuccess)
            {
                reportInfo("上传成功...");
                BcpFiles(condition.ExcelPath, readSuccessFileList);
            }
            else
            {
                reportInfo("上传失败...");
                BcpFiles(condition.ExcelPath, readSuccessFileList, true);
            }
        }

        protected abstract bool upLoadWorkParams(StationAcceptWorkParams workParams, StationAcceptCondition condition);
        #endregion

        #region 从数据库读取待验证工参
        protected StationAcceptWorkParams readWorkParams(StationAcceptCondition condition)
        {
            reportInfo("正在读取需要单验的工参...");
            return readWorkParamsFromDB(condition);
        }

        protected abstract StationAcceptWorkParams readWorkParamsFromDB(StationAcceptCondition condition);
        #endregion

        #region 备份工参文件
        public void BcpFiles(string srcFolder, string fileName, bool isError = false)
        {
            string bcpFolderPath = srcFolder + "\\工参备份\\" + DateTime.Now.ToString("yyMMdd");
            if (isError)
            {
                bcpFolderPath = srcFolder + "\\异常工参\\" + DateTime.Now.ToString("yyMMdd");
            }

            if (!Directory.Exists(bcpFolderPath))
            {
                Directory.CreateDirectory(bcpFolderPath);
            }

            string filePathNew = fileName.Replace(srcFolder, bcpFolderPath);
            string fileExtension = Path.GetExtension(fileName);

            while (File.Exists(filePathNew))
            {
                System.Threading.Thread.Sleep(1000);
                string fileTitle = Path.GetFileNameWithoutExtension(filePathNew);
                filePathNew = filePathNew.Replace(string.Format("{0}{1}", fileTitle, fileExtension)
                    , string.Format("{0}_{1}{2}", fileTitle, DateTime.Now.ToString("HHmmss"), fileExtension));
            }
            File.Copy(fileName, filePathNew, true);
            File.Delete(fileName);
        }

        public void BcpFiles(string srcFolder, List<string> fileNameList, bool isError = false)
        {
            foreach (string fileName in fileNameList)
            {
                BcpFiles(srcFolder, fileName, isError);
            }
        }
        #endregion

        protected void reportInfo(string str)
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo(str);
        }

        protected void reportError(Exception ex)
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundError(ex);
        }
    }

    /// <summary>
    /// 单验条件
    /// </summary>
    public class StationAcceptCondition
    {
        public string ExcelPath { get; set; }
        public string FilePath { get; set; }
        public virtual Dictionary<string, object> Params
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>
                {
                    ["ExcelPath"] = ExcelPath,
                    ["FilePath"] = FilePath
                };
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                setParam(param);
            }
        }

        protected virtual void setParam(Dictionary<string, object> param)
        {
            ExcelPath = getValidValue(param, "ExcelPath", "");
            FilePath = getValidValue(param, "FilePath", "");
        }

        protected T getValidValue<T>(Dictionary<string, object> param, string name, T defaultValue)
        {
            if (param.ContainsKey(name))
            {
                object obj = param[name];
                if (obj != null && obj is T)
                {
                    return (T)obj;
                }
            }
            return defaultValue;
        }
    }

    /// <summary>
    /// 单验工参
    /// </summary>
    public class StationAcceptWorkParams
    {
    }

    public abstract class BtsAcceptWorkParamBase<U>
    {
        protected BtsAcceptWorkParamBase(CellAcceptWorkParamBase cellParam)
        {
            ENodeBID = cellParam.ENodeBID;
            BtsNameFull = cellParam.BtsNameFull;
            DistrictName = cellParam.DistrictName;
            CoverScene = cellParam.CoverTypeDes;
        }
        
        public string BtsNameFull { get; set; }
        public int ENodeBID { get; set; }
        public string DistrictName { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public string CoverScene { get; set; }

        public bool IsOutDoor
        {
            get
            {
                if (string.IsNullOrEmpty(CoverScene) 
                    || !CoverScene.Trim().Equals("室外")
                    && !CoverScene.Trim().Equals("宏站"))
                {
                    return false;
                }
                return true;
            }
        }

        public Dictionary<U, CellAcceptWorkParamBase> CellWorkParamDic { get; set; } = new Dictionary<U, CellAcceptWorkParamBase>();
        public List<CellAcceptWorkParamBase> CellWorkParams
        {
            get { return new List<CellAcceptWorkParamBase>(CellWorkParamDic.Values); }
        }

        public abstract void LoadCurBtsWorkParam(CellManager cellManager);
        public abstract void RemoveCurBtsWorkParam(CellManager cellManager);
    }

    [Serializable]
    public class CellAcceptWorkParamBase
    {
        public string CellNameFull { get; set; } = "";
        public string BtsNameFull { get; set; } = "";
        public string DistrictName { get; set; } = "";
        public int ENodeBID { get; set; }
        public int CellID { get; set; }
        public int SectorID { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public int Tac { get; set; }
        public int Eci { get; set; }
        public int Earfcn { get; set; }
        public int Pci { get; set; }
        public string CoverTypeDes { get; set; }
    }
}
