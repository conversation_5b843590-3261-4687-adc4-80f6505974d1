﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTDIYQueryScanAnalysisForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.evtTP = new DevExpress.XtraTab.XtraTabPage();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.evtGridControl = new DevExpress.XtraGrid.GridControl();
            this.contextMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.sampleMenu = new System.Windows.Forms.ToolStripMenuItem();
            this.miCustomFly = new System.Windows.Forms.ToolStripMenuItem();
            this.miSpFly = new System.Windows.Forms.ToolStripMenuItem();
            this.replayScanSample = new System.Windows.Forms.ToolStripMenuItem();
            this.replayEvent = new System.Windows.Forms.ToolStripMenuItem();
            this.outPutExcelMenu = new System.Windows.Forms.ToolStripMenuItem();
            this.outShapFile = new System.Windows.Forms.ToolStripMenuItem();
            this.outPutEventShpFile = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gdCellInfo = new DevExpress.XtraGrid.GridControl();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.cellTP = new DevExpress.XtraTab.XtraTabPage();
            this.cellGridControl = new DevExpress.XtraGrid.GridControl();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.pageSetupDialog1 = new System.Windows.Forms.PageSetupDialog();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.evtTP.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.evtGridControl)).BeginInit();
            this.contextMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gdCellInfo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            this.cellTP.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cellGridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            this.SuspendLayout();
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.evtTP;
            this.xtraTabControl1.Size = new System.Drawing.Size(1098, 520);
            this.xtraTabControl1.TabIndex = 0;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.evtTP,
            this.cellTP});
            this.xtraTabControl1.SelectedPageChanged += new DevExpress.XtraTab.TabPageChangedEventHandler(this.xtraTabControl1_SelectedPageChanged);
            // 
            // evtTP
            // 
            this.evtTP.Controls.Add(this.splitContainerControl1);
            this.evtTP.Name = "evtTP";
            this.evtTP.Size = new System.Drawing.Size(1091, 490);
            this.evtTP.Text = "事件维度结果";
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.evtGridControl);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.gdCellInfo);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1091, 490);
            this.splitContainerControl1.SplitterPosition = 363;
            this.splitContainerControl1.TabIndex = 9;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // evtGridControl
            // 
            this.evtGridControl.ContextMenuStrip = this.contextMenu;
            this.evtGridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.evtGridControl.Location = new System.Drawing.Point(0, 0);
            this.evtGridControl.MainView = this.gridView1;
            this.evtGridControl.Name = "evtGridControl";
            this.evtGridControl.Size = new System.Drawing.Size(1091, 363);
            this.evtGridControl.TabIndex = 1;
            this.evtGridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            this.evtGridControl.Click += new System.EventHandler(this.evtGridControl_Click);
            // 
            // contextMenu
            // 
            this.contextMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.sampleMenu,
            this.miCustomFly,
            this.miSpFly,
            this.replayScanSample,
            this.replayEvent,
            this.outPutExcelMenu,
            this.outShapFile,
            this.outPutEventShpFile});
            this.contextMenu.Name = "contextMenu";
            this.contextMenu.Size = new System.Drawing.Size(188, 202);
            // 
            // sampleMenu
            // 
            this.sampleMenu.Name = "sampleMenu";
            this.sampleMenu.Size = new System.Drawing.Size(187, 22);
            this.sampleMenu.Text = "隐藏/显示全部采样点";
            this.sampleMenu.Click += new System.EventHandler(this.sampleMenu_Click);
            // 
            // miCustomFly
            // 
            this.miCustomFly.Name = "miCustomFly";
            this.miCustomFly.Size = new System.Drawing.Size(187, 22);
            this.miCustomFly.Text = "隐藏常用飞线";
            this.miCustomFly.Click += new System.EventHandler(this.miCustomFly_Click);
            // 
            // miSpFly
            // 
            this.miSpFly.Name = "miSpFly";
            this.miSpFly.Size = new System.Drawing.Size(187, 22);
            this.miSpFly.Text = "隐藏飞线(含邻区)";
            this.miSpFly.Visible = false;
            this.miSpFly.Click += new System.EventHandler(this.miSpFly_Click);
            // 
            // replayScanSample
            // 
            this.replayScanSample.Name = "replayScanSample";
            this.replayScanSample.Size = new System.Drawing.Size(187, 22);
            this.replayScanSample.Text = "回放扫频采样点";
            this.replayScanSample.Click += new System.EventHandler(this.replayScanSample_Click);
            // 
            // replayEvent
            // 
            this.replayEvent.Name = "replayEvent";
            this.replayEvent.Size = new System.Drawing.Size(187, 22);
            this.replayEvent.Text = "回放路测事件";
            this.replayEvent.Click += new System.EventHandler(this.replayEvent_Click);
            // 
            // outPutExcelMenu
            // 
            this.outPutExcelMenu.Name = "outPutExcelMenu";
            this.outPutExcelMenu.Size = new System.Drawing.Size(187, 22);
            this.outPutExcelMenu.Text = "导出EXCEL";
            this.outPutExcelMenu.Click += new System.EventHandler(this.outPutExcelMenu_Click);
            // 
            // outShapFile
            // 
            this.outShapFile.Name = "outShapFile";
            this.outShapFile.Size = new System.Drawing.Size(187, 22);
            this.outShapFile.Text = "导出扫频采样点图层";
            this.outShapFile.Click += new System.EventHandler(this.outShapFile_Click);
            // 
            // outPutEventShpFile
            // 
            this.outPutEventShpFile.Name = "outPutEventShpFile";
            this.outPutEventShpFile.Size = new System.Drawing.Size(187, 22);
            this.outPutEventShpFile.Text = "导出路测事件图层";
            this.outPutEventShpFile.Click += new System.EventHandler(this.outPutEventShpFile_Click);
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn33,
            this.gridColumn1,
            this.gridColumn32,
            this.gridColumn25,
            this.gridColumn2,
            this.gridColumn34,
            this.gridColumn35,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn16,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12});
            this.gridView1.GridControl = this.evtGridControl;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsView.ColumnAutoWidth = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "序号";
            this.gridColumn33.FieldName = "IndexID";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 0;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "事件名称";
            this.gridColumn1.FieldName = "StrEventName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 1;
            this.gridColumn1.Width = 83;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "网格类型";
            this.gridColumn32.FieldName = "StrGridType";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 2;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "网格名称";
            this.gridColumn25.FieldName = "StrGridName";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 3;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "道路名称";
            this.gridColumn2.FieldName = "StrEventRoadName";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 4;
            this.gridColumn2.Width = 83;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "LAC";
            this.gridColumn34.FieldName = "StrLAC";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 5;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "CI";
            this.gridColumn35.FieldName = "StrCI";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 6;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "经度";
            this.gridColumn3.FieldName = "DEventLongitude";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 7;
            this.gridColumn3.Width = 65;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "纬度";
            this.gridColumn4.FieldName = "DEventLatitude";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 8;
            this.gridColumn4.Width = 63;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "时间";
            this.gridColumn5.FieldName = "StrTime";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 9;
            this.gridColumn5.Width = 84;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "扫频采样点数";
            this.gridColumn6.FieldName = "ItpCount";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 10;
            this.gridColumn6.Width = 97;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "覆盖率";
            this.gridColumn7.FieldName = "StrCoverRate";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 11;
            this.gridColumn7.Width = 58;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "覆盖时长(s)";
            this.gridColumn8.FieldName = "DCoverTime";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 12;
            this.gridColumn8.Width = 85;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "连续覆盖最大时长(s)";
            this.gridColumn16.FieldName = "DCoverTimeMax";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 13;
            this.gridColumn16.Width = 133;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "覆盖小区数";
            this.gridColumn9.FieldName = "ICoverCellCount";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 14;
            this.gridColumn9.Width = 76;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "平均场强(dBm)";
            this.gridColumn10.FieldName = "IR0_RPMean";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 15;
            this.gridColumn10.Width = 109;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "最大场强(dBm)";
            this.gridColumn11.FieldName = "IRO_RPMax";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 16;
            this.gridColumn11.Width = 98;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "最小场强(dBm)";
            this.gridColumn12.FieldName = "IRO_RPMin";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 17;
            this.gridColumn12.Width = 100;
            // 
            // gdCellInfo
            // 
            this.gdCellInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gdCellInfo.Location = new System.Drawing.Point(0, 0);
            this.gdCellInfo.MainView = this.gridView3;
            this.gdCellInfo.Name = "gdCellInfo";
            this.gdCellInfo.Size = new System.Drawing.Size(1091, 121);
            this.gdCellInfo.TabIndex = 7;
            this.gdCellInfo.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView3});
            // 
            // gridView3
            // 
            this.gridView3.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView3.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView3.Appearance.Row.Options.UseTextOptions = true;
            this.gridView3.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn31,
            this.gridColumn29,
            this.gridColumn30,
            this.gridColumn24,
            this.gridColumn20,
            this.gridColumn28,
            this.gridColumn26,
            this.gridColumn27});
            this.gridView3.GridControl = this.gdCellInfo;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsBehavior.Editable = false;
            this.gridView3.OptionsView.ColumnAutoWidth = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            this.gridView3.OptionsView.ShowIndicator = false;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "小区名称";
            this.gridColumn31.FieldName = "StrCellName";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 0;
            this.gridColumn31.Width = 95;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "TAC";
            this.gridColumn29.FieldName = "ITAC";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 1;
            this.gridColumn29.Width = 88;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "ECI";
            this.gridColumn30.FieldName = "IECI";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 2;
            this.gridColumn30.Width = 117;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "采样点数";
            this.gridColumn24.FieldName = "ICellCountRO_RP";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 3;
            this.gridColumn24.Width = 120;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "覆盖时长(s)";
            this.gridColumn20.FieldName = "DCellCoverTime";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 4;
            this.gridColumn20.Width = 114;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "平均场强(dBm)";
            this.gridColumn28.FieldName = "ICellMeanRO_RP";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 5;
            this.gridColumn28.Width = 140;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "最大场强(dBm)";
            this.gridColumn26.FieldName = "ICellMaxRO_RP";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 6;
            this.gridColumn26.Width = 140;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "最小场强(dBm)";
            this.gridColumn27.FieldName = "ICellMinRO_RP";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 7;
            this.gridColumn27.Width = 140;
            // 
            // cellTP
            // 
            this.cellTP.Controls.Add(this.cellGridControl);
            this.cellTP.Name = "cellTP";
            this.cellTP.Size = new System.Drawing.Size(1091, 490);
            this.cellTP.Text = "小区维度结果";
            // 
            // cellGridControl
            // 
            this.cellGridControl.ContextMenuStrip = this.contextMenu;
            this.cellGridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.cellGridControl.Location = new System.Drawing.Point(0, 0);
            this.cellGridControl.MainView = this.gridView2;
            this.cellGridControl.Name = "cellGridControl";
            this.cellGridControl.Size = new System.Drawing.Size(1091, 490);
            this.cellGridControl.TabIndex = 1;
            this.cellGridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView2});
            // 
            // gridView2
            // 
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23});
            this.gridView2.GridControl = this.cellGridControl;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsBehavior.Editable = false;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "小区名称";
            this.gridColumn13.FieldName = "StrCellName";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 0;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "频点";
            this.gridColumn14.FieldName = "IEARFCN";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 1;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "PCI";
            this.gridColumn15.FieldName = "IPCI";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 2;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "扫频采样点数";
            this.gridColumn17.FieldName = "ItpCount";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 3;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "覆盖率";
            this.gridColumn18.FieldName = "StrCoverRate";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 4;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "覆盖时长(s)";
            this.gridColumn19.FieldName = "StrCoverTime";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 5;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "平均场强(dBm)";
            this.gridColumn21.FieldName = "IR0_RPMean";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 6;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "最大场强(dBm)";
            this.gridColumn22.FieldName = "IRO_RPMax";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 7;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "最小场强(dBm)";
            this.gridColumn23.FieldName = "IRO_RPMin";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 8;
            // 
            // ZTDIYQueryScanAnalysisForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1098, 520);
            this.Controls.Add(this.xtraTabControl1);
            this.Name = "ZTDIYQueryScanAnalysisForm";
            this.Text = "LTE关联扫频分析结果";
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.evtTP.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.evtGridControl)).EndInit();
            this.contextMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gdCellInfo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            this.cellTP.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.cellGridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage evtTP;
        private DevExpress.XtraTab.XtraTabPage cellTP;
        private DevExpress.XtraGrid.GridControl cellGridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private System.Windows.Forms.ContextMenuStrip contextMenu;
        private System.Windows.Forms.ToolStripMenuItem outPutExcelMenu;
        private System.Windows.Forms.ToolStripMenuItem replayScanSample;
        private DevExpress.XtraGrid.GridControl gdCellInfo;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.GridControl evtGridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private System.Windows.Forms.PageSetupDialog pageSetupDialog1;
        private System.Windows.Forms.ToolStripMenuItem sampleMenu;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private System.Windows.Forms.ToolStripMenuItem replayEvent;
        private System.Windows.Forms.ToolStripMenuItem outShapFile;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private System.Windows.Forms.ToolStripMenuItem outPutEventShpFile;
        private System.Windows.Forms.ToolStripMenuItem miSpFly;
        private System.Windows.Forms.ToolStripMenuItem miCustomFly;
    }
}