﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTNRScanModCell
{
    public class NRScanModCellCondition
    {
        public double MinRsrp { get; set; }
        public double MaxSinr { get; set; }
        public double DiffRsrp { get; set; }
        public int MinSampleCount { get; set; }
        public NRModInterfereCondition FilterCond { get; set; } = new NRModInterfereCondition();
    }

    public class NRScanModCellItem
    {
        public NRCell NrCell
        {
            get;
            protected set;
        }

        public string CellName
        {
            get { return NrCell == null ? string.Format("{0}_{1}", Earfcn, Pci) : NrCell.Name; }
        }

        public virtual double AvgRsrp
        {
            get { return SampleCount == 0 ? 0 : rsrpSum / SampleCount; }
        }

        public virtual double AvgSinr
        {
            get { return SampleCount == 0 ? 0 : sinrSum / SampleCount; }
        }

        public string Tac
        {
            get { return NrCell == null ? "" : NrCell.TAC.ToString(); }
        }

        public string Nci
        {
            get { return NrCell == null ? "" : NrCell.NCI.ToString(); }
        }

        public int Earfcn
        {
            get;
            protected set;
        }

        public int Pci
        {
            get;
            protected set;
        }

        public int SampleCount
        {
            get;
            protected set;
        }

        public List<TestPoint> TestPoints
        {
            get;
            protected set;
        }

        public void AddTestPoint(TestPoint tp, double rsrp, double sinr)
        {
            TestPoints.Add(tp);
            rsrpSum += rsrp;
            sinrSum += sinr;
            SampleCount += 1;
        }

        public NRScanModCellItem(NRCell cell, int earfcn, int pci)
        {
            NrCell = cell;
            Earfcn = earfcn;
            Pci = pci;
            TestPoints = new List<TestPoint>();
        }

        protected double rsrpSum;
        protected double sinrSum;
    }

    public class NRScanModSourceCell : NRScanModCellItem
    {
        public double InterferTargetRate
        {
            get;
            private set;
        }

        public double InterfereTargetTotalRate
        {
            get;
            private set;
        }

        public NRScanModTargetCell TargetCell
        {
            get;
            private set;
        }

        public void GetResult(NRScanModTargetCell tarCell)
        {
            InterferTargetRate = tarCell.InterferedSampleCount == 0 ? 0 : 1d * SampleCount / tarCell.InterferedSampleCount;
            InterfereTargetTotalRate = tarCell.SampleCount == 0 ? 0 : 1d * SampleCount / tarCell.SampleCount;
            TargetCell = tarCell;
        }

        public NRScanModSourceCell(NRCell cell, int earfcn, int pci) : base(cell, earfcn, pci)
        {
        }
    }

    public class NRScanModTargetCell : NRScanModCellItem
    {
        public override double AvgRsrp
        {
            get { return InterferedSampleCount == 0 ? 0 : 1d * base.rsrpSum / InterferedSampleCount; }
        }

        public override double AvgSinr
        {
            get { return InterferedSampleCount == 0 ? 0 : 1d * base.sinrSum / InterferedSampleCount; }
        }

        public int InterferedSampleCount //  受干扰点数
        {
            get;
            private set;
        }

        public List<NRScanModSourceCell> SourceCells
        {
            get;
            private set;
        }

        public Dictionary<NRCell, NRScanModSourceCell> SrcCellDic
        {
            get { return srcCellDic; }
        }

        public void AddTestPoint(TestPoint tp, double rsrp, double sinr, bool isInterfered)
        {
            if (isInterfered)
            {
                base.AddTestPoint(tp, rsrp, sinr);
                ++InterferedSampleCount;
            }
            else
            {
                ++SampleCount; // 只对总点数加1
                TestPoints.Add(tp);
            }
        }

        public void GetResult()
        {
            SourceCells = new List<NRScanModSourceCell>(srcCellDic.Values);
            foreach (NRScanModSourceCell srcCell in SourceCells)
            {
                srcCell.GetResult(this);
            }
        }

        public NRScanModTargetCell(NRCell cell, int earfcn, int pci) : base(cell, earfcn, pci)
        {
        }

        private readonly Dictionary<NRCell, NRScanModSourceCell> srcCellDic = new Dictionary<NRCell, NRScanModSourceCell>();
    }
}
