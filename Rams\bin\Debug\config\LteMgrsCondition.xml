<?xml version="1.0"?>
<Configs>
  <Config name="BaseSetting">
    <Item name="GridSize" typeName="Int32">50</Item>
  </Config>
  <Config name="武汉">
    <Item name="ShapeFile" typeName="String">E:\Project\Rams\bin\Debug\GEOGRAPHIC\武汉\城区网格_region.shp</Item>
    <Item name="FieldName" typeName="String">NAME</Item>
    <Item name="Enable" typeName="Boolean">True</Item>
  </Config>
  <Config name="SampleRate">
    <Item name="RSRP" typeName="Double">-105</Item>
  </Config>
  <Config name="DualFreq">
    <Item name="RSRP" typeName="Double">-105</Item>
  </Config>
  <Config name="RSRPRange">
    <Item name="FreqType" typeName="String">最强信号</Item>
  </Config>
  <Config name="CoverageRange">
    <Item name="RSRPMin" typeName="Double">-105</Item>
    <Item name="RSRPDiff" typeName="Double">6</Item>
    <Item name="EnableOptional" typeName="Boolean">True</Item>
    <Item name="OptionalRsrp" typeName="Double">-100</Item>
    <Item name="CheckTwoEarfcn" typeName="Boolean">False</Item>
    <Item name="FBandType" typeName="Boolean">False</Item>
    <Item name="FilterF2" typeName="Boolean">False</Item>
    <Item name="EditSampleData" typeName="Boolean">False</Item>
    <Item name="CsvPath" typeName="String" />
    <Item name="FreqType" typeName="String">不分段</Item>
  </Config>
  <Config name="WeakRsrp">
    <Item name="RsrpMax" typeName="Double">-95</Item>
    <Item name="GridCount" typeName="Int32">3</Item>
    <Item name="FreqType" typeName="String">最强信号</Item>
  </Config>
  <Config name="HighCoverage">
    <Item name="Coverage" typeName="Double">4</Item>
    <Item name="CheckTwoEarfcn" typeName="Boolean">False</Item>
    <Item name="FreqType" typeName="String">不分段</Item>
    <Item name="MinRsrp" typeName="Double">-105</Item>
    <Item name="DiffRsrp" typeName="Double">6</Item>
    <Item name="GridCount" typeName="Int32">3</Item>
    <Item name="EnableOptional" typeName="Boolean">True</Item>
    <Item name="OptionalRsrp" typeName="Double">-100</Item>
    <Item name="EnableOutputSample" typeName="Boolean">False</Item>
    <Item name="EnableGridNumDist" typeName="Boolean">False</Item>
    <Item name="GridNumDist" typeName="Int32">2</Item>
    <Item name="CsvPath" typeName="String" />
    <Item name="EnableFBandType" typeName="Boolean">False</Item>
  </Config>
  <Config name="RsrpColorRange">
    <Item name="(-∞,-120)" typeName="String">-1000,-120,-65536</Item>
    <Item name="[-120,-105)" typeName="String">-120,-105,-47872</Item>
    <Item name="[-105,-100)" typeName="String">-105,-100,-40121</Item>
    <Item name="[-100,-95)" typeName="String">-100,-95,-23296</Item>
    <Item name="[-95,-90)" typeName="String">-95,-90,-256</Item>
    <Item name="[-90,-85)" typeName="String">-90,-85,-5374161</Item>
    <Item name="[-85,-80)" typeName="String">-85,-80,-7278960</Item>
    <Item name="[-80,-75)" typeName="String">-80,-75,-8586240</Item>
    <Item name="[-75,-70)" typeName="String">-75,-70,-13447886</Item>
    <Item name="[-70,+∞)" typeName="String">-70,1000,-16776961</Item>
  </Config>
  <Config name="CoverageColorRange">
    <Item name="（-∞,1)" typeName="String">-1000,1,-16776961</Item>
    <Item name="[1,2)" typeName="String">1,2,-12042869</Item>
    <Item name="[2,3)" typeName="String">2,3,-16751616</Item>
    <Item name="[3,4" typeName="String">3,4,-12799119</Item>
    <Item name="[4,5)" typeName="String">4,5,-16711936</Item>
    <Item name="[5,6)" typeName="String">5,6,-8586240</Item>
    <Item name="[6,7)" typeName="String">6,7,-5374161</Item>
    <Item name="[7,8)" typeName="String">7,8,-256</Item>
    <Item name="[8,9)" typeName="String">8,9,-23296</Item>
    <Item name="[9,10)" typeName="String">9,10,-29696</Item>
    <Item name="[10,+∞)" typeName="String">10,1000,-65536</Item>
  </Config>
</Configs>