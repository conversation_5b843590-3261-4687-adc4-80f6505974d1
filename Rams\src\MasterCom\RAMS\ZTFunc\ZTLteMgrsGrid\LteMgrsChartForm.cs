﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

using DevExpress.XtraCharts;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteMgrsChartForm : MinCloseForm
    {
        public LteMgrsChartForm(MainModel mainModel) : base(mainModel)
        {
            InitializeComponent();
        }

        public void FillChart(ChartControl chart, string title)
        {
            chart.Dock = DockStyle.Fill;
            this.Controls.Clear();
            this.Controls.Add(chart);
            if (title != null)
            {
                this.Text = title;
            }
            this.Chart = chart;
        }

        public ChartControl Chart
        {
            get;
            private set;
        }
    }
}
