﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.ES.Core;
using MasterCom.Util;
using System.Xml;
using System.IO;
using MasterCom.ES.Data;
using DevExpress.XtraEditors;

namespace MasterCom.ES.UI
{
    public partial class ESProcGraphForm : BaseFormStyle
    {
        private Font fontText = new Font(new FontFamily("宋体"), 10, FontStyle.Bold);
        private Pen penConnLine = new Pen(Brushes.Blue, 1);
        private Pen penBorderNormal = new Pen(Brushes.Blue, 1);
        private Pen penBorderSelected = new Pen(Brushes.Red, 1);
        private Pen penHoverLine = new Pen(Color.Red, 1);
        //
        ESEngine es = null;
        //
        private ProcRoutine curSelRoutine = null;
        private static ESProcGraphForm instance = null;
        private ESProcGraphForm()
        {
            InitializeComponent();
            penHoverLine.DashStyle = System.Drawing.Drawing2D.DashStyle.Custom;
            penHoverLine.DashPattern = new float[] { 5, 5 };
        }
        public static ESProcGraphForm GetInstance()
        {
            if(instance==null)
            {
                instance = new ESProcGraphForm();
            }
            return instance;
        }
        public ProcRoutine CurSelRoutine
        {
            get
            {
                return curSelRoutine;
            }
        }
        public static Dictionary<string, bool> PathRenderedDic { get; set; } = new Dictionary<string, bool>();
        private void mainGraphPanel_Paint(object sender, PaintEventArgs e)
        {
            if(curSelRoutine!=null)
            {
                PathRenderedDic.Clear();
                CurSelRoutine.RootNode.ClearIdx();
                int retInt = CurSelRoutine.RootNode.ResetIdx(1);
                Graphics g = e.Graphics;
                g.ScaleTransform(zoomFactor, zoomFactor);
                drawEntryNode(g, mainGraphPanel.AutoScrollPosition.X,mainGraphPanel.AutoScrollPosition.Y, curSelRoutine.RootNode);
                if (curSelNode != null && connTargetHoverPoint != Point.Empty)
                {
                    g.DrawLine(penHoverLine, mainGraphPanel.AutoScrollPosition.X + curSelNode.XPos, mainGraphPanel.AutoScrollPosition.Y + curSelNode.YPos,
                       connTargetHoverPoint.X, connTargetHoverPoint.Y);
                }
                //hover nodes
                foreach(NodeEntry hover in curSelRoutine._HoverNodes)
                {
                    hover.ClearIdx();
                    retInt = hover.ResetIdx(retInt+1);
                    drawEntryNode(g, mainGraphPanel.AutoScrollPosition.X, mainGraphPanel.AutoScrollPosition.Y, hover);
                }
            }
        }
        private void updateEnvelope()
        {
            if(curSelRoutine!=null)
            {
                Point pt = curSelRoutine.RootNode.GetMostLeftTopPoint();
                int mostx = pt.X < 0 ? -pt.X : 0;
                int mosty = pt.Y < 0 ? -pt.Y : 0;
                if (mostx >0 || mosty>0)
                {
                    curSelRoutine.RootNode.ClearIdx();
                    curSelRoutine.RootNode.moveOffset(mostx, mosty);
                }

                mainGraphPanel.AutoScrollMinSize = new Size((int)(curSelRoutine.RootNode.GetMaxSize().Width*zoomFactor) +20, (int)(curSelRoutine.RootNode.GetMaxSize().Height *zoomFactor)+20);
            }
            else
            {
                mainGraphPanel.AutoScrollMinSize = new Size(200, 200);
            }
        }

        private void drawEntryNode(Graphics g, int offsetx,int offsety,NodeEntry nd)
        {
            if(nd!=null)
            {
                if(nd.Type == NodeType.Condition)
                {
                    SizeF size = g.MeasureString(nd.ExpString, fontText);
                    nd.Width = (int)size.Width + 30;
                    nd.Height = (int)size.Height + 30;
                    Point[] pts = new Point[4];
                    pts[0] = new Point((int)(nd.XPos - 0.5 * nd.Width) +offsetx, nd.YPos+offsety);
                    pts[1] = new Point(nd.XPos + offsetx, (int)(nd.YPos - 0.5 * nd.Height) + offsety);
                    pts[2] = new Point((int)(nd.XPos + 0.5 * nd.Width) + offsetx, nd.YPos + offsety);
                    pts[3] = new Point(nd.XPos + offsetx, (int)(nd.YPos + 0.5 * nd.Height) + offsety);
                    g.FillPolygon(Brushes.LightCyan, pts);
                    g.DrawPolygon(curSelNode == nd ? penBorderSelected : penBorderNormal, pts);
                    g.DrawString(nd.ExpString, fontText, Brushes.DarkBlue, nd.XPos - 0.5f * size.Width+offsetx, nd.YPos - 0.5f * size.Height+offsety);
                    if (ESEngine.GetInstance().CurBrkNode == nd)
                    {
                        g.FillRectangle(Brushes.Yellow, nd.XPos - 20 + offsetx, nd.YPos - 20 + offsety, 30, 20);
                    }
                    if(nd._breakPoint)
                    {
                        g.DrawString("Brk", fontText, Brushes.Red, nd.XPos - 20 + offsetx, nd.YPos - 20 + offsety);
                        
                    }
                }
                else if(nd.Type == NodeType.Operate)
                {
                    SizeF size = g.MeasureString(nd.ExpString, fontText);
                    nd.Width = (int)size.Width + 2;
                    nd.Height = (int)size.Height + 2;
                    g.FillRectangle(Brushes.Cyan, (int)(nd.XPos - 0.5 * nd.Width)+offsetx, (int)(nd.YPos - 0.5 * nd.Height)+offsety, nd.Width, nd.Height);
                    g.DrawRectangle(curSelNode == nd ? penBorderSelected : penBorderNormal, (int)(nd.XPos - 0.5 * nd.Width)+offsetx, (int)(nd.YPos - 0.5 * nd.Height)+offsety, nd.Width, nd.Height);
                    g.DrawString(nd.ExpString, fontText, Brushes.DarkBlue, nd.XPos - 0.5f * size.Width+offsetx, nd.YPos - 0.5f*size.Height+offsety);
                    if (ESEngine.GetInstance().CurBrkNode == nd)
                    {
                        g.FillRectangle(Brushes.Yellow, nd.XPos - 20 + offsetx, nd.YPos - 20 + offsety, 30, 20);
                    }
                    if (nd._breakPoint)
                    {
                        
                        g.DrawString("Brk", fontText, Brushes.Red, nd.XPos - 20 + offsetx, nd.YPos - 20 + offsety);
                    }
                }
                else if(nd.Type == NodeType.PreProblemType)
                {
                    SizeF size = g.MeasureString(nd.ExpString, fontText);
                    nd.Width = (int)size.Width + 10;
                    nd.Height = (int)size.Height + 5;
                    g.FillEllipse(Brushes.Yellow, (int)(nd.XPos - 0.5 * nd.Width)+offsetx, (int)(nd.YPos - 0.5 * nd.Height)+offsety, nd.Width, nd.Height);
                    g.DrawEllipse(curSelNode == nd ? penBorderSelected : penBorderNormal, (int)(nd.XPos - 0.5 * nd.Width)+offsetx, (int)(nd.YPos - 0.5 * nd.Height)+offsety, nd.Width, nd.Height);
                    g.DrawString(nd.ExpString, fontText, Brushes.DarkBlue, nd.XPos - 0.5f * size.Width+offsetx, nd.YPos - 0.5f * size.Height+offsety);
                    if (ESEngine.GetInstance().CurBrkNode == nd)
                    {
                        g.FillRectangle(Brushes.Yellow, nd.XPos - 20 + offsetx, nd.YPos - 20 + offsety, 30, 20);
                    }
                    if (nd._breakPoint)
                    {
                        g.DrawString("Brk", fontText, Brushes.Red, nd.XPos - 20 + offsetx, nd.YPos - 20 + offsety);
                    }
                }
                else if(nd.Type == NodeType.ReasonDesc)
                {
                    SizeF size = g.MeasureString(nd.ExpString, fontText);
                    nd.Width = (int)size.Width + 10;
                    nd.Height = (int)size.Height + 5;
                    g.FillRectangle(Brushes.LightPink, (int)(nd.XPos - 0.5 * nd.Width) + offsetx, (int)(nd.YPos - 0.5 * nd.Height) + offsety, nd.Width, nd.Height);
                    g.DrawRectangle(curSelNode == nd ? penBorderSelected : penBorderNormal, (int)(nd.XPos - 0.5 * nd.Width) + offsetx, (int)(nd.YPos - 0.5 * nd.Height) + offsety, nd.Width, nd.Height);
                    g.DrawString(nd.ExpString, fontText, Brushes.DarkBlue, nd.XPos - 0.5f * size.Width + offsetx, nd.YPos - 0.5f * size.Height + offsety);
                    if (ESEngine.GetInstance().CurBrkNode == nd)
                    {
                        g.FillRectangle(Brushes.Yellow, nd.XPos - 20 + offsetx, nd.YPos - 20 + offsety, 30, 20);
                    }
                    if (nd._breakPoint)
                    {
                        g.DrawString("Brk", fontText, Brushes.Red, nd.XPos - 20 + offsetx, nd.YPos - 20 + offsety);
                    }
                }
                else if(nd.Type == NodeType.SolveDesc)
                {
                    SizeF size = g.MeasureString(nd.ExpString, fontText);
                    nd.Width = (int)size.Width + 10;
                    nd.Height = (int)size.Height + 5;
                    g.FillRectangle(Brushes.LightGreen, (int)(nd.XPos - 0.5 * nd.Width) + offsetx, (int)(nd.YPos - 0.5 * nd.Height) + offsety, nd.Width, nd.Height);
                    g.DrawRectangle(curSelNode == nd ? penBorderSelected : penBorderNormal, (int)(nd.XPos - 0.5 * nd.Width) + offsetx, (int)(nd.YPos - 0.5 * nd.Height) + offsety, nd.Width, nd.Height);
                    g.DrawString(nd.ExpString, fontText, Brushes.DarkBlue, nd.XPos - 0.5f * size.Width + offsetx, nd.YPos - 0.5f * size.Height + offsety);
                    if (ESEngine.GetInstance().CurBrkNode == nd)
                    {
                        g.FillRectangle(Brushes.Yellow, nd.XPos - 20 + offsetx, nd.YPos - 20 + offsety, 30, 20);
                    }
                    if (nd._breakPoint)
                    {
                        g.DrawString("Brk", fontText, Brushes.Red, nd.XPos - 20 + offsetx, nd.YPos - 20 + offsety);
                    }
                }
                else if (nd.Type == NodeType.OtherProc)
                {
                    SizeF size = g.MeasureString(nd.ExpString, fontText);
                    nd.Width = (int)size.Width + 10;
                    nd.Height = (int)size.Height + 5;
                    g.FillRectangle(Brushes.Red, (int)(nd.XPos - 0.5 * nd.Width) + offsetx, (int)(nd.YPos - 0.5 * nd.Height) + offsety, nd.Width, nd.Height);
                    g.DrawRectangle(curSelNode == nd ? penBorderSelected : penBorderNormal, (int)(nd.XPos - 0.5 * nd.Width) + offsetx, (int)(nd.YPos - 0.5 * nd.Height) + offsety, nd.Width, nd.Height);
                    g.DrawString(nd.ExpString, fontText, Brushes.DarkBlue, nd.XPos - 0.5f * size.Width + offsetx, nd.YPos - 0.5f * size.Height + offsety);
                    if (ESEngine.GetInstance().CurBrkNode == nd)
                    {
                        g.FillRectangle(Brushes.Yellow, nd.XPos - 20 + offsetx, nd.YPos - 20 + offsety, 30, 20);
                    }
                    if (nd._breakPoint)
                    {
                        g.DrawString("Brk", fontText, Brushes.Red, nd.XPos - 20 + offsetx, nd.YPos - 20 + offsety);
                    }
                }
                //
                if(nd.YesNode!=null)
                {
                    int ysIdx = nd.YesNode._Idx;
                    if (!checkPathDrawed(nd._Idx, ysIdx))
                    {
                    drawEntryNode(g, offsetx,offsety,nd.YesNode);
                    string lineTag = "";
                    if(nd.Type== NodeType.Condition)
                    {
                        lineTag = "是";
                    }
                    drawConnLine(g, offsetx, offsety, nd, nd.YesNode, lineTag);
                }
                    
                }
                if(nd.NoNode!=null)
                {
                    int noIdx = nd.NoNode._Idx;
                    if (!checkPathDrawed(nd._Idx, noIdx))
                    {
                    drawEntryNode(g, offsetx, offsety, nd.NoNode);
                    string lineTag = "";
                    if (nd.Type == NodeType.Condition)
                    {
                        lineTag = "否";
                    }
                    else
                    {
                        lineTag = "b";
                    }
                    drawConnLine(g, offsetx, offsety,nd, nd.NoNode, lineTag);
                }
            }
        }
        }

        private bool checkPathDrawed(int fromId, int toId)
        {
            string key = fromId + "->" + toId;
            if (ESProcGraphForm.PathRenderedDic.ContainsKey(key))
            {
                return true;
            }
            else
            {
                ESProcGraphForm.PathRenderedDic[key] = true;
                return false;
            }
        }

        private void drawConnLine(Graphics g, int xoffpos, int yoffpos, NodeEntry fromNode, NodeEntry toNode, string tagStr)
        {
            if (fromNode == null || toNode == null)
            {
                return;
            }
            bool downwardfirst = false;
            int fromX = fromNode.XPos;
            int fromY = fromNode.YPos;

            if (fromNode.Type == NodeType.Condition)
            {
                if(toNode.XPos<fromNode.XPos)//左侧
                {
                    fromX = fromNode.XPos - fromNode.Width / 2;
                    g.DrawString(tagStr, fontText, Brushes.Black, xoffpos + fromX-15, yoffpos + fromY-10);
                }
                else//右侧
                {
                    fromX = fromNode.XPos + fromNode.Width / 2;
                    g.DrawString(tagStr, fontText, Brushes.Black, xoffpos + fromX, yoffpos + fromY-10);
                }
                downwardfirst = false;
            }
            else if(fromNode.Type == NodeType.Operate||fromNode.Type== NodeType.PreProblemType
                ||fromNode.Type == NodeType.ReasonDesc||fromNode.Type == NodeType.SolveDesc ||fromNode.Type == NodeType.OtherProc)
            {
                fromY = fromNode.YPos + fromNode.Height / 2;
                downwardfirst = true;
            }
            int toX = toNode.XPos;
            int toY = toNode.YPos - toNode.Height / 2;
            
            if(downwardfirst)
            {
                g.DrawLine(penConnLine, fromX + xoffpos, fromY + yoffpos, fromX + xoffpos, (fromY + toY) / 2 + yoffpos);
                g.DrawLine(penConnLine, fromX + xoffpos, (fromY + toY) / 2 + yoffpos, toX + xoffpos, (fromY + toY) / 2 + yoffpos);
                g.DrawLine(penConnLine, toX + xoffpos, (fromY + toY) / 2 + yoffpos, toX + xoffpos, toY + yoffpos);
                drawArrow(g, toX + xoffpos, (fromY + toY) / 2 + yoffpos, toX + xoffpos, toY + yoffpos);
            }
            else
            {
                g.DrawLine(penConnLine, fromX + xoffpos, fromY + yoffpos, toX + xoffpos, fromY + yoffpos);
                g.DrawLine(penConnLine, toX + xoffpos, fromY + yoffpos, toX + xoffpos, toY + yoffpos);
                drawArrow(g, toX + xoffpos, fromY + yoffpos, toX + xoffpos, toY + yoffpos);
            }
            
            
        }

        private void drawArrow(Graphics g, int x1, int y1, int x2, int y2)
        {
            int len = 10;
            int gap = 5;
            if(x1 == x2)//上下方向的箭头
            {
                Point[] pts = new Point[3];
                pts[0] = new Point(x2, y2);
                pts[1] = new Point(x2 + gap, (y2-(len*(y2>y1?1:(-1)))));
                pts[2] = new Point(x2 - gap, (y2-(len*(y2>y1?1:(-1)))));
                g.FillPolygon(Brushes.Blue, pts);
            }
            else //(y1==y2)左右方向的箭头
            {
                Point[] pts = new Point[3];
                pts[0] = new Point(x2, y2);
                pts[1] = new Point(x2 - len * (x2 > x1 ? 1 : (-1)), (y2 + gap));
                pts[2] = new Point(x2 - len * (x2 > x1 ? 1 : (-1)), (y2 - gap));
                g.FillPolygon(Brushes.Blue, pts);
            }
        }

        private void ESProcGraphForm_Shown(object sender, EventArgs e)
        {
            es = ESEngine.GetInstance();
            initTreeRoutineSetting();
            intPubResvTreeShow();
        }
        public void FreshShowBreaked()
        {
            intPubResvTreeShow();
            updateInprocResvValues();
            AutoShowRunningProc();
            this.mainGraphPanel.Invalidate();
        }
        private void intPubResvTreeShow()
        {
            treeViewRsvValues.Nodes.Clear();
            TreeNode pubNode = new TreeNode();
            treeViewRsvValues.Nodes.Add(pubNode);
            pubNode.Tag = es.pubResvStore;
            pubNode.Text = "公共预存值";
            fillRsvItemsToNode(es.pubResvStore, pubNode);
            foreach (string inProcKey in es.inProcModuleDic.Keys)
            {
                ResvStore store = es.inProcModuleDic[inProcKey].ReservStore;
                TreeNode provNode = new TreeNode();
                provNode.Tag = store;
                provNode.Text = inProcKey;
                treeViewRsvValues.Nodes.Add(provNode);
                fillRsvItemsToNode(store, provNode);
            }
        }

        private void freshAddResvItemNode(string name,int type,TreeNode tnode)
        {
            if(tnode.Tag is ResvStore)
            {
                ResvStore store = tnode.Tag as ResvStore;
                if(store.ContainName(name)!=0)
                {
                    XtraMessageBox.Show("预存值 "+name+" 在当前预存池中已经存在，请换个名称！");
                    return;
                }
                TreeNode nnode = new TreeNode();
                nnode.Tag = name;
                if(type==0)
                {
                    store.SetResvValue(name, -999);
                    nnode.Text = name;
                }
                else
                {
                    store.SetResvValue(name, "");
                    nnode.Text = name + "(字符型)";
                }
                tnode.Nodes.Add(nnode);
                tnode.Expand();
            }
            
        }
        private void fillRsvItemsToNode(ResvStore store, TreeNode tnode)
        {
            foreach (string str in store.ResvValueDic.Keys)
            {
                TreeNode nd = new TreeNode();
                nd.Text = str;
                nd.Tag = str;
                tnode.Nodes.Add(nd);
                nd.ToolTipText = store.ResvValueDic[str]+"";
            }
            foreach (string str in store.ResvStringDic.Keys)
            {
                TreeNode nd = new TreeNode();
                nd.Tag = str;
                nd.Text = str+"(字符型)";
                tnode.Nodes.Add(nd);
                nd.ToolTipText = store.ResvStringDic[str];
            }
        }

        private void initTreeRoutineSetting()
        {
            foreach(RoutineGroup rg in es.groups)
            {
                TreeNode rgNode = new TreeNode();
                rgNode.Text = rg.Name;
                rgNode.Tag = rg;
                foreach(ProcRoutine routine in rg.Routines)
                {
                    TreeNode routineNode = new TreeNode();
                    routineNode.Text = routine.Name;
                    routineNode.Tag = routine;
                    rgNode.Nodes.Add(routineNode);
                    if(routine.InProcModule)
                    {
                        routineNode.BackColor = Color.Cyan;
                    }
                }
                treeViewRoutines.Nodes.Add(rgNode);
            }
        }
        private void saveSettings()
        {
            es.SaveSettings();
            updateDirtyPanel();
        }
        private void fireShowSelectedRoutine(ProcRoutine routine)
        {
            if(routine!=null)
            {
                this.curSelRoutine = routine;
                this.Text = "自动分析逻辑设置 - " + routine.Name;
            }
            else
            {
                this.curSelRoutine = null;
                this.Text = "自动分析逻辑设置";
            }
            updateInprocResvValues();
            updateEnvelope();
            mainGraphPanel.Invalidate();
        }

        private void updateInprocResvValues()
        {
            treeViewInProcResv.Nodes.Clear();
            if (this.curSelRoutine != null)
            {
                TreeNode allNode = new TreeNode();
                allNode.Text = "本流程的所有预存值";
                allNode.Tag = this.curSelRoutine.ReservStore;
                treeViewInProcResv.Nodes.Add(allNode);
                fillRsvItemsToNode(this.curSelRoutine.ReservStore, allNode);
                allNode.ExpandAll();
            }
            
        }
        private void treeView_NodeMouseClick(object sender, TreeNodeMouseClickEventArgs e)
        {
            if (e.Node.Tag is ProcRoutine)
            {
                ProcRoutine routine = e.Node.Tag as ProcRoutine;
                fireShowSelectedRoutine(routine);
            }
            else
            {
                fireShowSelectedRoutine(null);
            }
            updateDirtyPanel();
            
            if(e.Button == MouseButtons.Right)
            {
                treeViewRoutines.SelectedNode = e.Node;
                if (e.Node == null)
                {
                    return;
                }
                if(e.Node.Tag is ProcRoutine)
                {
                    miAddGroup.Enabled = false;
                    miDelGroup.Enabled = false;
                    miAddRoutine.Enabled = false;
                    miDelRoutine.Enabled = true;
                }
                else if(e.Node.Tag is RoutineGroup)
                {
                    miAddGroup.Enabled = true;
                    miDelGroup.Enabled = true;
                    miAddRoutine.Enabled = true;
                    miDelRoutine.Enabled = false;
                }
                else
                {
                    return;
                }
                ctxMenuRoutine.Show(treeViewRoutines, e.X, e.Y);
            }
        }
        Point clickLocation = Point.Empty;
        Point lastLocation = Point.Empty;
        Point curMouseHover = Point.Empty;
        /// <summary>
        /// 剪切板中的节点
        /// </summary>
        NodeEntry clipboardNode = null;
        int clipParentOrigX = 0;
        int clipParentOrigY = 0;
        NodeEntry curSelNode = null;
        Point connTargetHoverPoint = Point.Empty;
        Point fromScrollPosition = Point.Empty;
        bool dragging = false;
        bool ctrlDrag = false;
        private void mainGraphPanel_MouseDown(object sender, MouseEventArgs e)
        {
            int xx = (int)(e.X / zoomFactor);
            int yy = (int)(e.Y / zoomFactor);
            if (e.Button == MouseButtons.Left)
            {
                dragging = true;
                ctrlDrag = false;
                clickLocation = new Point(xx, yy);
                curSelNode = hitGetNodeEntry(e);
                doShowInfo(curSelNode);
                if (curSelNode != null)
                {
                    CurSelRoutine.IsDirty = true;
                    updateDirtyPanel();
                    lastLocation = new Point(curSelNode.XPos, curSelNode.YPos);
                    if ((Control.ModifierKeys & Keys.Control) == Keys.Control)
                    {
                        ctrlDrag = true;
                        curSelNode.UpdateChildrenOffsets(curSelNode.XPos, curSelNode.YPos);
                    }
                }
                else
                {
                    fromScrollPosition = mainGraphPanel.AutoScrollPosition;
                }
            }
        }

        private void updateDirtyPanel()
        {
            if(CurSelRoutine!=null)
            {
                if(CurSelRoutine.IsDirty)
                {
                    panelDirty.Left = 0;
                    panelDirty.Top = 0;
                    panelDirty.Visible = true;
                }
                else
                {
                    panelDirty.Visible = false;
                }
            }
            else
            {
                panelDirty.Visible = false;
            }
        }
        private void btnMakeNotDirty_Click(object sender, EventArgs e)
        {
            if(CurSelRoutine!=null)
            {
                curSelRoutine.IsDirty = false;
            }
            panelDirty.Visible = false;
        }

        private void doShowInfo(NodeEntry curSelNode)
        {
            propGrid.SelectedObject = curSelNode;
        }

        private NodeEntry hitGetNodeEntry(MouseEventArgs e)
        {
            int xx = (int) (e.X / zoomFactor);
            int yy = (int) (e.Y / zoomFactor);
            if(curSelRoutine!=null)
            {
                NodeEntry node =  hitTestNodeEntry(curSelRoutine.RootNode, xx-mainGraphPanel.AutoScrollPosition.X, yy-mainGraphPanel.AutoScrollPosition.Y);
                if(node!=null)
                {
                    return node;
                }
                foreach (NodeEntry hover in curSelRoutine._HoverNodes)
                {
                    NodeEntry hnode = hitTestNodeEntry(hover, xx - mainGraphPanel.AutoScrollPosition.X, yy - mainGraphPanel.AutoScrollPosition.Y);
                    if (hnode != null)
                    {
                        return hnode;
                    }
                }
            }
            return null;
        }

        private NodeEntry hitTestNodeEntry(NodeEntry nodeEntry, int xpos, int ypos)
        {
            if(xpos>nodeEntry.XPos-nodeEntry.Width/2 && xpos<nodeEntry.XPos +nodeEntry.Width/2
                && ypos>nodeEntry.YPos - nodeEntry.Height/2 && ypos<nodeEntry.YPos + nodeEntry.Height/2)
            {
                return nodeEntry;
            }
            else 
            {
                if(nodeEntry.YesNode!=null)
                {
                    NodeEntry ret = hitTestNodeEntry(nodeEntry.YesNode, xpos, ypos);
                    if(ret!=null)
                    {
                        return ret;
                    }
                }
                if (nodeEntry.NoNode != null)
                {
                    NodeEntry ret = hitTestNodeEntry(nodeEntry.NoNode, xpos, ypos);
                    if (ret != null)
                    {
                        return ret;
                    }
                }
            }
            return null;
        }

        private void mainGraphPanel_MouseMove(object sender, MouseEventArgs e)
        {
            int xx = (int)(e.X / zoomFactor);
            int yy = (int)(e.Y / zoomFactor);
            if (dragging && curSelNode != null)
            {
                if (!btnModeAddConn.Checked)
                {
                    curSelNode.XPos = lastLocation.X + xx - clickLocation.X;
                    curSelNode.YPos = lastLocation.Y + yy - clickLocation.Y;
                    if ((Control.ModifierKeys & Keys.Control) == Keys.Control && ctrlDrag)
                    {
                        curSelNode.UpdateChildrenByOffsets(curSelNode.XPos, curSelNode.YPos);
                    }
                    mainGraphPanel.Invalidate();
                }
                else//连接操作模式
                {
                    connTargetHoverPoint = new Point(xx, yy);
                    mainGraphPanel.Invalidate();
                }
            }
            else if(dragging)
            {
                int xoffset = clickLocation.X - e.X;
                int yoffset = clickLocation.Y - e.Y;
                mainGraphPanel.AutoScrollPosition = new Point(xoffset - fromScrollPosition.X, yoffset - fromScrollPosition.Y);
                updateDirtyPanel();
            }
        }

        private void mainGraphPanel_MouseUp(object sender, MouseEventArgs e)
        {
            dragging = false;
            ctrlDrag = false;
            if (!btnModeAddConn.Checked)
            {
                updateEnvelope();
                
                curMouseHover = Point.Empty;
                mainGraphPanel.Invalidate();
            }
            else//连接模式
            {
                if(curSelNode!=null)
                {
                    NodeEntry tarNode = hitGetNodeEntry(e);
                    if(tarNode!=null)
                    {
                        bool succ = connect2Nodes(curSelNode, tarNode);
                        if(succ)
                        {
                            curSelRoutine._HoverNodes.Remove(tarNode);
                        }
                    }
                    else
                    {
                        connTargetHoverPoint = Point.Empty;

                    }
                    mainGraphPanel.Invalidate();
                }
            }
        }

        private bool connect2Nodes(NodeEntry fromNode, NodeEntry toNode)
        {
            connTargetHoverPoint = Point.Empty;
            if(checkCanConnect(fromNode,toNode))
            {
                if(fromNode.Type== NodeType.Condition)
                {
                    return addConnectNode(fromNode, toNode);
                }
                else
                {
                    if(fromNode.YesNode != null)
                    {
                        XtraMessageBox.Show(this, "源节点的输出分支已经占用，不能再添加其他的输出分支！");
                        return false;
                    }
                    else
                    {
                        fromNode.YesNode = toNode;
                        toNode._ParentNodes.Add(fromNode);
                        return true;
                    }
                }
            }
           
            return false;
        }

        private bool addConnectNode(NodeEntry fromNode, NodeEntry toNode)
        {
            if (fromNode.YesNode != null && fromNode.NoNode != null)
            {
                XtraMessageBox.Show(this, "源节点的输出分支已经占用，不能再添加其他的判定分支！");
                return false;
            }
            else if (fromNode.YesNode == null && fromNode.NoNode == null)
            {
                DialogResult dr = XtraMessageBox.Show(this, "请选择需要添加的分支类别：'是'或'否'", "条件分支选项", MessageBoxButtons.YesNoCancel);
                if (dr == DialogResult.Cancel)
                {
                    return false;
                }
                else
                {
                    if (dr == DialogResult.Yes)
                    {
                        fromNode.YesNode = toNode;
                        toNode._ParentNodes.Add(fromNode);
                        return true;
                    }
                    else
                    {
                        fromNode.NoNode = toNode;
                        toNode._ParentNodes.Add(fromNode);
                        return true;
                    }
                }
            }
            else if (fromNode.YesNode == null)
            {
                fromNode.YesNode = toNode;
                toNode._ParentNodes.Add(fromNode);
                return true;
            }
            else if (fromNode.NoNode == null)
            {
                fromNode.NoNode = toNode;
                toNode._ParentNodes.Add(fromNode);
                return true;
            }
            return false;
        }

        private bool checkCanConnect(NodeEntry fromNode, NodeEntry toNode)
        {
            if(fromNode==toNode)
            {
                XtraMessageBox.Show(this, "不能连接 " + fromNode.ExpString+" 到 "+toNode.ExpString, "连接错误");
                return false;
            }
            if(checkCanThrough(toNode,fromNode))
            {
                XtraMessageBox.Show(this, "不能连接 " + fromNode.ExpString + " 到 " + toNode.ExpString+",改连接会导致回路循环判断问题！", "连接错误");
                return false;
            }
            return true;
        }
        /// <summary>
        /// 是否能从一个节点到达另一个节点
        /// </summary>
        /// <param name="from"></param>
        /// <param name="to"></param>
        /// <returns></returns>
        private bool checkCanThrough(NodeEntry from, NodeEntry to)
        {
            bool left = false;
            bool right = false;
            if(from.YesNode!=null)
            {
                if(from.YesNode==to)
                {
                    return true;
                }
                else
                {
                    left = checkCanThrough(from.YesNode, to);
                }
            }
            if (from.NoNode != null)
            {
                if (from.NoNode == to)
                {
                    return true;
                }
                else
                {
                    right = checkCanThrough(from.NoNode, to);
                }
            }
            return left || right;
        }

        private void mainGraphPanel_MouseClick(object sender, MouseEventArgs e)
        {
            int xx = (int)(e.X / zoomFactor);
            int yy = (int)(e.Y / zoomFactor);
            if (e.Button == MouseButtons.Right)
            {
                if (curSelNode != null)
                {
                    miNodePaste.Enabled = (clipboardNode!=null);
                    ctxMenuNode.Show(mainGraphPanel, e.X, e.Y);
                }
                else
                {
                    lastLocation = new Point(xx, yy);
                }
            }
            mainGraphPanel.Focus();
        }
        private void fireInsertAfterNode(NodeEntry node,NodeType type)
        {
            if(node.Type == NodeType.Condition)
            {
                DialogResult dr = XtraMessageBox.Show(this, "请选择需要插入到的分支类别：'是'或'否'", "条件分支选项", MessageBoxButtons.YesNoCancel);
                if (dr == DialogResult.Cancel)
                {
                    return;
                }
                NodeEntry newNode = getNewNode(node, type);
                if (dr == DialogResult.Yes)
                {
                    insertYesNode(node, newNode);
                }
                else
                {
                    insertNoNode(node, newNode);
                }
            }
            else
            {
                NodeEntry newNode = getNewNode(node, type);
                insertYesNode(node, newNode);
            }
        }

        private static void insertNoNode(NodeEntry node, NodeEntry newNode)
        {
            if (node.NoNode != null)
            {
                node.NoNode._ParentNodes.Remove(node);
                node.NoNode._ParentNodes.Add(newNode);
                newNode.YesNode = node.NoNode;
                node.NoNode = newNode;
                newNode._ParentNodes.Add(node);
            }
            else
            {
                node.NoNode = newNode;
                newNode._ParentNodes.Add(node);
            }
        }

        private void insertYesNode(NodeEntry node, NodeEntry newNode)
        {
            if (node.YesNode != null)
            {
                node.YesNode._ParentNodes.Remove(node);
                node.YesNode._ParentNodes.Add(newNode);
                newNode.YesNode = node.YesNode;
                node.YesNode = newNode;
                newNode._ParentNodes.Add(node);
            }
            else
            {
                node.YesNode = newNode;
                newNode._ParentNodes.Add(node);
            }
        }

        private void fireAddToNode(NodeEntry node,NodeType type)
        {
            if(node.Type == NodeType.Condition)
            {
                setNodeBranch(node, type);
            }
            else
            {
                if(node.YesNode!=null)
                {
                    XtraMessageBox.Show(this, "该节点的输出分支已经占用，不能再添加其他的分支！");
                }
                else
                {
                    node.YesNode = getNewNode(node, type);
                    node.YesNode._ParentNodes.Add(node);
                }
            }
        }

        private void setNodeBranch(NodeEntry node, NodeType type)
        {
            if (node.YesNode != null && node.NoNode != null)
            {
                XtraMessageBox.Show(this, "该节点的输出分支已经占用，不能再添加其他的判定分支！");
            }
            else if (node.YesNode == null && node.NoNode == null)
            {
                DialogResult dr = XtraMessageBox.Show(this, "请选择需要添加的分支类别：'是'或'否'", "条件分支选项", MessageBoxButtons.YesNoCancel);
                if (dr != DialogResult.Cancel)
                {
                    NodeEntry newNode = getNewNode(node, type);
                    if (dr == DialogResult.Yes)
                    {
                        node.YesNode = newNode;
                        newNode._ParentNodes.Add(node);
                    }
                    else
                    {
                        node.NoNode = newNode;
                        newNode._ParentNodes.Add(node);
                    }
                }
            }
            else if (node.YesNode == null)
            {
                node.YesNode = getNewNode(node, type);
                node.YesNode._ParentNodes.Add(node);
            }
            else if (node.NoNode == null)
            {
                node.NoNode = getNewNode(node, type);
                node.NoNode._ParentNodes.Add(node);
            }
        }

        private NodeEntry getNewNode(NodeEntry node, NodeType type)
        {
            NodeEntry newNode = new NodeEntry();
            newNode.XPos = node.XPos + 20;
            newNode.YPos = node.YPos + 100;
            newNode.ExpString = "新流程节点";
            newNode.Type = type;
            return newNode;
        }

        private void miTypeCondition_Click(object sender, EventArgs e)
        {
            fireAddToNode(curSelNode, NodeType.Condition);
            mainGraphPanel.Invalidate();
        }

        private void miTypeOperation_Click(object sender, EventArgs e)
        {
            fireAddToNode(curSelNode, NodeType.Operate);
            mainGraphPanel.Invalidate();
        }

        private void miTypePreType_Click(object sender, EventArgs e)
        {
            fireAddToNode(curSelNode, NodeType.PreProblemType);
            mainGraphPanel.Invalidate();
        }

        private void miTypeReason_Click(object sender, EventArgs e)
        {
            fireAddToNode(curSelNode, NodeType.ReasonDesc);
            mainGraphPanel.Invalidate();
        }

        private void miTypeSolved_Click(object sender, EventArgs e)
        {
            fireAddToNode(curSelNode, NodeType.SolveDesc);
            mainGraphPanel.Invalidate();
        }

        private void miTypeOtherProc_Click(object sender, EventArgs e)
        {
            fireAddToNode(curSelNode, NodeType.OtherProc);
            mainGraphPanel.Invalidate();
        }

        private void treeViewRsvValues_NodeMouseClick(object sender, TreeNodeMouseClickEventArgs e)
        {
            inProcRsvFired = false;
            if(e.Button == MouseButtons.Right)
            {
                if (e.Node == null)
                {
                    return;
                }
                treeViewRsvValues.SelectedNode = e.Node;
                miDelResvItem.Enabled = e.Node.Level == 1;
                miAddResvItem.Enabled = e.Node.Level == 0;

                ctxMenuResv.Show(treeViewRsvValues, e.X, e.Y);
            }
            
        }

        private void miAddResvItem_Click(object sender, EventArgs e)
        {
            AddResvItemDlg addDlg = new AddResvItemDlg();
            if(DialogResult.OK == addDlg.ShowDialog(this))
            {
                string name = addDlg.InputName;
                int type = addDlg.InputType;
                TreeNode node = inProcRsvFired ? treeViewInProcResv.SelectedNode : treeViewRsvValues.SelectedNode;
                freshAddResvItemNode(name, type, node);
            }
        }

        private void miDelResvItem_Click(object sender, EventArgs e)
        {
            TreeNode node = inProcRsvFired ? treeViewInProcResv.SelectedNode : treeViewRsvValues.SelectedNode;
            TreeNode parentNode = node.Parent;
            string rsvName = node.Tag as string;

            ResvStore store = parentNode.Tag as ResvStore;
            store.ResvValueDic.Remove(rsvName);
            store.ResvStringDic.Remove(rsvName);
            parentNode.Nodes.Remove(node);
        }



        private void btnSave_Click(object sender, EventArgs e)
        {
            if(DialogResult.OK == XtraMessageBox.Show(this,"确认要保存覆盖？","保存",MessageBoxButtons.OKCancel))
            {
                saveSettings();
            }
        }
        private string saveToTargetDir = Application.StartupPath;
        private void btnSaveDirtyTo_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog dlg = new FolderBrowserDialog();
            dlg.SelectedPath = saveToTargetDir;
            dlg.Description = "请选择保存到的目录";
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                string destination = dlg.SelectedPath;
                if(destination== Application.StartupPath)
                {
                    XtraMessageBox.Show(this, "所选路径与当前路径重复，请选择另一个路径！");
                }
                else
                {
                    saveToTargetDir = destination;
                    saveDirtySettingsTo(destination);
                }
                
            }
        }

        private void saveDirtySettingsTo(string destination)
        {
            es.SaveDirtySettingsTo(destination);
        }

        private void miAddRoutine_Click(object sender, EventArgs e)
        {
            TreeNode groupNode = treeViewRoutines.SelectedNode;
            RoutineGroup group = groupNode.Tag as RoutineGroup;
            if(group==null)
            {
                return;
            }
            AddRoutineItemDlg rtDlg = new AddRoutineItemDlg();
            if(DialogResult.OK == rtDlg.ShowDialog(this))
            {
                string name = rtDlg.InputName.Trim();
                if(group.isProcNameExist(name))
                {
                    XtraMessageBox.Show(this, "选定组中已经存在名称为+"+name+"的判断流程，请换一个名称！", "发现重名");
                    return;
                }
                ProcRoutine routine = new ProcRoutine();
                routine.Name = name;
                routine.InProcModule = rtDlg.IsInProc;
                NodeEntry node = new NodeEntry();
                node.Type = NodeType.Condition;
                node.XPos = 200;
                node.YPos = 50;
                node.ExpString = "新的流程起始";
                routine.RootNode = node;
                group.Routines.Add(routine);
                //
                if(routine.InProcModule)
                {
                    ESEngine.GetInstance().inProcModuleDic[routine.Name] = routine;
                }
                //
                TreeNode routineNode = new TreeNode();
                routineNode.Text = routine.Name;
                routineNode.Tag = routine;
                groupNode.Nodes.Add(routineNode);
                if (routine.InProcModule)
                {
                    routineNode.BackColor = Color.Cyan;
                }
                groupNode.Expand();
                treeViewRoutines.SelectedNode = routineNode;
                fireShowSelectedRoutine(routine);
            }
        }

        private void miDelRoutine_Click(object sender, EventArgs e)
        {
            TreeNode routineNode = treeViewRoutines.SelectedNode;
            ProcRoutine proc = routineNode.Tag as ProcRoutine;
            if(proc==null)
            {
                return;
            }
            if (DialogResult.OK == XtraMessageBox.Show(this, "确定要永久删除判断流程 " + proc.Name + " ?", "删除", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning))
            {
                RoutineGroup group = routineNode.Parent.Tag as RoutineGroup;
                string saveRootDir = Application.StartupPath + @"\esconfig";
                string fileFullName = saveRootDir + @"\[" + group.Name + @"]\[" + proc.Name + "].xml";
                if(File.Exists(fileFullName))
                {
                    File.Delete(fileFullName);
                }
                group.Routines.Remove(proc);
                routineNode.Parent.Nodes.Remove(routineNode);
                fireShowSelectedRoutine(null);
            }
        }

        private void miAddGroup_Click(object sender, EventArgs e)
        {
            AddGroupItemDlg grpDlg = new AddGroupItemDlg();
            if(DialogResult.OK == grpDlg.ShowDialog(this))
            {
                string name = grpDlg.InputName.Trim();
                if(hasGroupName(name))
                {
                    XtraMessageBox.Show(this, "已经存在名称为+" + name + "的组，请换一个名称！", "发现重名");
                    return;
                }
                RoutineGroup group = new RoutineGroup();
                group.Name = name;
                es.groups.Add(group);

                TreeNode rgNode = new TreeNode();
                rgNode.Text = group.Name;
                rgNode.Tag = group;
                treeViewRoutines.Nodes.Add(rgNode);
                treeViewRoutines.SelectedNode = rgNode;
            }
        }

        private bool hasGroupName(string name)
        {
            foreach (RoutineGroup group in es.groups)
            {
                if(name.ToUpper().Equals(group.Name.ToUpper()))
                {
                    return true;
                }
            }
            return false;
        }

        private void miDelGroup_Click(object sender, EventArgs e)
        {
            TreeNode groupNode = treeViewRoutines.SelectedNode;
            RoutineGroup group = groupNode.Tag as RoutineGroup;
            if (group == null)
            {
                return;
            }
            if(group.Routines.Count>0)
            {
                XtraMessageBox.Show(this,"所选流程组不为空，不能删除！","删除组");
                return;
            }
            if (DialogResult.OK == XtraMessageBox.Show(this, "确定要删除流程组" + group.Name +" ?", "删除组", MessageBoxButtons.OKCancel))
            {
                string saveRootDir = Application.StartupPath + @"\esconfig";
                string groupFullName = saveRootDir + @"\[" + group.Name + "]";
                if(Directory.Exists(groupFullName))
                {
                    Directory.Delete(groupFullName);
                }
                treeViewRoutines.Nodes.Remove(groupNode);
                es.groups.Remove(group);
            }
        }

        private void miNodeDelete_Click(object sender, EventArgs e)
        {
            if(curSelNode._ParentNodes.Count>0)
            {
                if (DialogResult.OK == XtraMessageBox.Show(this, "确定要删除节点 " + curSelNode.ExpString + " ?", "删除节点", MessageBoxButtons.OKCancel))
                {
                    foreach(NodeEntry _pNode in curSelNode._ParentNodes)
                    {
                        if (_pNode.YesNode == curSelNode)
                        {
                            _pNode.YesNode = null;
                        }
                        else if (_pNode.NoNode == curSelNode)
                        {
                            _pNode.NoNode = null;
                        }
                    }
                    curSelNode = null;
                    mainGraphPanel.Invalidate();
                }
            }
            else
            {
                XtraMessageBox.Show(this, "所选节点为根节点，不能删除！", "删除节点");
            }
        }

        private void miNodeCut_Click(object sender, EventArgs e)
        {
            if(curSelNode!=null)
            {
                if (curSelNode._ParentNodes.Count==0)
                {
                    XtraMessageBox.Show(this, "所选节点为根节点，不能剪切！", "剪切");
                    return;
                }
                foreach(NodeEntry _pNode in curSelNode._ParentNodes)
                {
                    if (_pNode.YesNode == curSelNode)
                    {
                        _pNode.YesNode = null;
                    }
                    else if (_pNode.NoNode == curSelNode)
                    {
                        _pNode.NoNode = null;
                    }
                }
                clipboardNode = curSelNode;
                clipParentOrigX = curSelNode._ParentNodes[0].XPos;
                clipParentOrigY = curSelNode._ParentNodes[0].YPos;
                curSelNode = null;
                mainGraphPanel.Invalidate();
            }
        }

        private void miNodeCopy_Click(object sender, EventArgs e)
        {
            if (curSelNode != null)
            {
                Dictionary<int, NodeEntry> cloneDic = new Dictionary<int, NodeEntry>();
                curSelNode.ClearIdx();
                curSelNode.ResetIdx(1);
                NodeEntry newNode = curSelNode.Clone(cloneDic);
                clipboardNode = newNode;
                if(curSelNode._ParentNodes.Count>0)
                {
                    clipParentOrigX = curSelNode._ParentNodes[0].XPos;
                    clipParentOrigY = curSelNode._ParentNodes[0].YPos;
                }
                else
                {
                    clipParentOrigX = curSelNode.XPos - 50;
                    clipParentOrigY = curSelNode.YPos - 50;
                }
            }
        }

        private void miNodePaste_Click(object sender, EventArgs e)
        {
            if(curSelNode!=null && clipboardNode!=null)
            {
                if (curSelNode.Type == NodeType.Condition)
                {
                    bool isAdded = addConditionNode();
                    if (!isAdded)
                    {
                        return;
                    }
                }
                else
                {
                    if (curSelNode.YesNode != null)
                    {
                        XtraMessageBox.Show(this, "该节点的输出分支已经占用，不能再添加其他的分支！");
                        return;
                    }
                    else
                    {
                        doOffsetClip(clipboardNode, curSelNode.XPos - clipParentOrigX, curSelNode.YPos - clipParentOrigY);
                        curSelNode.YesNode = clipboardNode;
                        clipboardNode._ParentNodes.Add(curSelNode);
                        clipboardNode = null;
                    }
                }
                mainGraphPanel.Invalidate();
            }
        }

        private bool addConditionNode()
        {
            if (curSelNode.YesNode != null && curSelNode.NoNode != null)
            {
                XtraMessageBox.Show(this, "该节点的输出分支已经占用，不能再添加其他的判定分支！");
                return false;
            }
            else if (curSelNode.YesNode == null && curSelNode.NoNode == null)
            {
                bool isCancel = judgeDialogResult();
                if (isCancel)
                {
                    return false;
                }
            }
            else if (curSelNode.YesNode == null)
            {
                doOffsetClip(clipboardNode, curSelNode.XPos - clipParentOrigX, curSelNode.YPos - clipParentOrigY);
                curSelNode.YesNode = clipboardNode;
                clipboardNode._ParentNodes.Add(curSelNode);
                clipboardNode = null;
            }
            else if (curSelNode.NoNode == null)
            {
                doOffsetClip(clipboardNode, curSelNode.XPos - clipParentOrigX, curSelNode.YPos - clipParentOrigY);
                curSelNode.NoNode = clipboardNode;
                clipboardNode._ParentNodes.Add(curSelNode);
                clipboardNode = null;
            }
            return true;
        }

        private bool judgeDialogResult()
        {
            DialogResult dr = XtraMessageBox.Show(this, "请选择需要添加的分支类别：'是'或'否'", "条件分支选项", MessageBoxButtons.YesNoCancel);
            if (dr == DialogResult.Cancel)
            {
                return true;
            }
            else
            {
                if (dr == DialogResult.Yes)
                {
                    doOffsetClip(clipboardNode, curSelNode.XPos - clipParentOrigX, curSelNode.YPos - clipParentOrigY);
                    curSelNode.YesNode = clipboardNode;
                    clipboardNode._ParentNodes.Add(curSelNode);
                    clipboardNode = null;
                }
                else
                {
                    doOffsetClip(clipboardNode, curSelNode.XPos - clipParentOrigX, curSelNode.YPos - clipParentOrigY);
                    curSelNode.NoNode = clipboardNode;
                    clipboardNode._ParentNodes.Add(curSelNode);
                    clipboardNode = null;
                }
            }
            return false;
        }

        private void doOffsetClip(NodeEntry node, int xoffset, int yoffset)
        {
            node.moveOffset(xoffset, yoffset);
        }

        public void SetStandAlone(bool value)
        {
            standalone = value;
        }
        private bool standalone = true;
        private void ESProcGraphForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if(e.CloseReason== CloseReason.UserClosing && !standalone)
            {
                e.Cancel = true;
                this.Visible = false;
            }
        }

        private void miInsCondition_Click(object sender, EventArgs e)
        {
            fireInsertAfterNode(curSelNode, NodeType.Condition);
            mainGraphPanel.Invalidate();
        }

        private void miInsOperation_Click(object sender, EventArgs e)
        {
            fireInsertAfterNode(curSelNode, NodeType.Operate);
            mainGraphPanel.Invalidate();
        }

        private void miInsPreType_Click(object sender, EventArgs e)
        {
            fireInsertAfterNode(curSelNode, NodeType.PreProblemType);
            mainGraphPanel.Invalidate();
        }

        private void miInsReason_Click(object sender, EventArgs e)
        {
            fireInsertAfterNode(curSelNode, NodeType.ReasonDesc);
            mainGraphPanel.Invalidate();
        }

        private void miInsSolved_Click(object sender, EventArgs e)
        {
            fireInsertAfterNode(curSelNode, NodeType.SolveDesc);
            mainGraphPanel.Invalidate();
        }
        private void miInsOtherProc_Click(object sender, EventArgs e)
        {
            fireInsertAfterNode(curSelNode, NodeType.OtherProc);
            mainGraphPanel.Invalidate();
        }
        private void btnModeAddConn_Click(object sender, EventArgs e)
        {
            btnModeAddConn.Checked = !btnModeAddConn.Checked;
        }

        private void miDelPath2Child_Click(object sender, EventArgs e)
        {
            if (curSelNode != null)
            {
                if (curSelNode.YesNode == null && curSelNode.NoNode == null)//没有子流程
                {
                    XtraMessageBox.Show(this, "所选节点没有子节点，无法删除连接关系！", "删除连接");
                    return;
                }
                bool isRemmove = setCurSelNode();
                if (isRemmove)
                {
                    mainGraphPanel.Invalidate();
                }
            }
        }

        private bool setCurSelNode()
        {
            if (curSelNode.YesNode != null && curSelNode.NoNode != null)
            {
                DialogResult dr = XtraMessageBox.Show(this, "请选择要删除的分支：'是'或'否'", "条件分支选项", MessageBoxButtons.YesNoCancel);
                if (dr == DialogResult.Cancel)
                {
                    return false;
                }
                else
                {
                    if (dr == DialogResult.Yes)
                    {
                        curSelNode.YesNode = removeNode(curSelNode.YesNode);
                    }
                    else
                    {
                        curSelNode.NoNode = removeNode(curSelNode.NoNode);
                    }
                }
            }
            else if (curSelNode.YesNode != null)
            {
                curSelNode.YesNode = removeNode(curSelNode.YesNode);
            }
            else if (curSelNode.NoNode != null)
            {
                curSelNode.NoNode = removeNode(curSelNode.NoNode);
            }
            return true;
        }

        private NodeEntry removeNode(NodeEntry node)
        {
            if (node._ParentNodes.Count == 1)
            {
                NodeEntry hoverNode = node;
                curSelRoutine._HoverNodes.Add(hoverNode);

                node._ParentNodes.Remove(curSelNode);
                node = null;
            }
            else
            {
                node._ParentNodes.Remove(curSelNode);
                node = null;
            }
            return node;
        }

        ParamSettingDlg paramSettingDlg = null;
        private void btnParamSetting_Click(object sender, EventArgs e)
        {
            if (paramSettingDlg==null)
            {
                paramSettingDlg = new ParamSettingDlg();
            }
            paramSettingDlg.FillParams(es.dtParamDic);
            if(DialogResult.OK == paramSettingDlg.ShowDialog(this))
            {
                Dictionary<string, DTParameter> paramDic = paramSettingDlg.GetResultParaDic();
                es.dtParamDic = paramDic;
            }
        }
        L3MsgParamDlg l3MsgParamDlg = null;
        private void btnL3ParamSetting_Click(object sender, EventArgs e)
        {
            if(l3MsgParamDlg==null)
            {
                l3MsgParamDlg = new L3MsgParamDlg();
            }
            l3MsgParamDlg.FillParams(es.dtL3ParamListDic);
            if (DialogResult.OK == l3MsgParamDlg.ShowDialog(this))
            {
                Dictionary<int, List<L3MsgParam>> l3Dic = l3MsgParamDlg.GetResultL3ParaDic();
                es.dtL3ParamListDic = l3Dic;
            }
        }
        private bool inProcRsvFired = true;
        private void treeViewInProcResv_NodeMouseClick(object sender, TreeNodeMouseClickEventArgs e)
        {
            inProcRsvFired = true;
            if (e.Button == MouseButtons.Right)
            {
                if (e.Node == null)
                {
                    return;
                }
                treeViewInProcResv.SelectedNode = e.Node;
                miDelResvItem.Enabled = e.Node.Level == 1;
                miAddResvItem.Enabled = e.Node.Level == 0;
                
                ctxMenuResv.Show(treeViewInProcResv, e.X, e.Y);
            }
        }

        private void btnHideRight_Click(object sender, EventArgs e)
        {
            splitMain.Panel2Collapsed = false;
            btnHideRight.Visible = false;
        }

        private void splitMain_MouseClick(object sender, MouseEventArgs e)
        {
            splitMain.Panel2Collapsed = true;
            btnHideRight.Visible = true;
        }
        private float zoomFactor = 1.0f;
        private void btnLarge_Click(object sender, EventArgs e)
        {
            zoomFactor = 1.1f * zoomFactor;
            mainGraphPanel.Invalidate();
        }

        private void btnSmall_Click(object sender, EventArgs e)
        {
            zoomFactor = zoomFactor/1.1f;
            mainGraphPanel.Invalidate();
        }

        private void miBreakAt_Click(object sender, EventArgs e)
        {
            if (curSelNode != null)
            {
                curSelNode._breakPoint = !curSelNode._breakPoint;
                mainGraphPanel.Invalidate();
            }
        }

        private void btnContinueRun_Click(object sender, EventArgs e)
        {
            //
        }
        public void AutoShowRunningProc()
        {
            if (ESEngine.GetInstance().CurRunningProc != null
                && ESEngine.GetInstance().CurRunningProc != curSelRoutine)
            {
                fireShowSelectedRoutine(ESEngine.GetInstance().CurRunningProc);
            }
        }
        private void miNextCursor_Click(object sender, EventArgs e)
        {
            if (ESEngine.GetInstance().CurRunningProc!=null)
            {
                fireShowSelectedRoutine(ESEngine.GetInstance().CurRunningProc);
            }
        }

        private void btnStepOver_Click(object sender, EventArgs e)
        {
            //
        }

        private void bbtnStepInto_Click(object sender, EventArgs e)
        {
            //
        }

        private void btnClearBrk_Click(object sender, EventArgs e)
        {
            ESEngine.GetInstance().ClearAllBreakPoint();
            mainGraphPanel.Invalidate();
        }

        private void btnFindText_Click(object sender, EventArgs e)
        {
            FindDlg findDlg = new FindDlg();
            if(DialogResult.OK == findDlg.ShowDialog())
            {
                Cursor.Current = Cursors.WaitCursor;
                List<FindResultItem> findResultList = new List<FindResultItem>();
                string stext = findDlg.getInputText();
                List<RoutineGroup> groups = ESEngine.GetInstance().groups;
                foreach(RoutineGroup group in groups)
                {
                    List<ProcRoutine> routines = group.Routines;
                    foreach(ProcRoutine routine in routines)
                    {
                        if(routine.RootNode!=null)
                        {
                            Dictionary<NodeEntry, FindResultType> findMatchNodes = new Dictionary<NodeEntry, FindResultType>();
                            routine.RootNode.FindTextWith(findMatchNodes, stext);
                            foreach(NodeEntry mtNode in findMatchNodes.Keys)
                            {
                                FindResultType ftype = findMatchNodes[mtNode];
                                FindResultItem fri = new FindResultItem();
                                fri.group = group;
                                fri.routine = routine;
                                fri.node = mtNode;
                                fri.findType = ftype;
                                findResultList.Add(fri);
                            }
                        }
                    }
                }
                FindResultForm findForm = new FindResultForm(this);
                findForm.FillFindResult(findResultList);
                findForm.Owner = this;
                findForm.Visible = true;
                Cursor.Current = Cursors.Default;
            }
        }

        internal void FireGoToShowFind(FindResultItem result)
        {
            curSelNode = result.node;
            int xscrolTo = curSelNode.XPos - mainGraphPanel.Width / 2;
            int yscrolTo = curSelNode.YPos - mainGraphPanel.Height / 2;
            mainGraphPanel.AutoScrollPosition  = new Point(xscrolTo,yscrolTo);

            fireShowSelectedRoutine(result.routine);
        }

        private void btnPackToZip_Click(object sender, EventArgs e)
        {
            try
            {
                string folderDir = Application.StartupPath + @"\esconfig\";
                string targetZipFile = Application.StartupPath + @"\esconfig.dlz";
                ZipClass.ZipFileMain(folderDir, targetZipFile);
                XtraMessageBox.Show("完成发布压缩！" + targetZipFile);
            }
            catch(Exception ex)
            {
                XtraMessageBox.Show("压缩失败！"+ex.ToString());
            }
        }
    }
}