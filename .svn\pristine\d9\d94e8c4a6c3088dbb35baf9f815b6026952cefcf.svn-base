﻿namespace MasterCom.RAMS.Func
{
    partial class MapNRCellLayerCellProperties
    {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.grpFactor = new System.Windows.Forms.GroupBox();
            this.label1 = new System.Windows.Forms.Label();
            this.colorFrame = new DevExpress.XtraEditors.ColorEdit();
            this.labelColor = new System.Windows.Forms.Label();
            this.TrackBarOpacity = new System.Windows.Forms.TrackBar();
            this.colorCell = new DevExpress.XtraEditors.ColorEdit();
            this.label100 = new System.Windows.Forms.Label();
            this.LabelOpacity = new System.Windows.Forms.Label();
            this.label0 = new System.Windows.Forms.Label();
            this.checkBoxDisplay = new System.Windows.Forms.CheckBox();
            this.btnFont = new DevExpress.XtraEditors.SimpleButton();
            this.cbxDrawCellLabel = new System.Windows.Forms.CheckBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.cbxCellPCI = new System.Windows.Forms.CheckBox();
            this.cbxCellDes = new System.Windows.Forms.CheckBox();
            this.cbxCellFreqList = new System.Windows.Forms.CheckBox();
            this.cbxCellFreq = new System.Windows.Forms.CheckBox();
            this.cbxCellNCI = new System.Windows.Forms.CheckBox();
            this.cbxCellLAC = new System.Windows.Forms.CheckBox();
            this.cbxCellCode = new System.Windows.Forms.CheckBox();
            this.cbxCellName = new System.Windows.Forms.CheckBox();
            this.grpFactor.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.colorFrame.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.TrackBarOpacity)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorCell.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // grpFactor
            // 
            this.grpFactor.Controls.Add(this.label1);
            this.grpFactor.Controls.Add(this.colorFrame);
            this.grpFactor.Controls.Add(this.labelColor);
            this.grpFactor.Controls.Add(this.TrackBarOpacity);
            this.grpFactor.Controls.Add(this.colorCell);
            this.grpFactor.Controls.Add(this.label100);
            this.grpFactor.Controls.Add(this.LabelOpacity);
            this.grpFactor.Controls.Add(this.label0);
            this.grpFactor.Controls.Add(this.checkBoxDisplay);
            this.grpFactor.Location = new System.Drawing.Point(24, 3);
            this.grpFactor.Name = "grpFactor";
            this.grpFactor.Size = new System.Drawing.Size(489, 129);
            this.grpFactor.TabIndex = 87;
            this.grpFactor.TabStop = false;
            // 
            // label1
            // 
            this.label1.Location = new System.Drawing.Point(21, 59);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(62, 20);
            this.label1.TabIndex = 88;
            this.label1.Text = "边框颜色:";
            this.label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // colorFrame
            // 
            this.colorFrame.EditValue = System.Drawing.Color.Aqua;
            this.colorFrame.Location = new System.Drawing.Point(96, 58);
            this.colorFrame.Name = "colorFrame";
            this.colorFrame.Properties.Appearance.ForeColor = System.Drawing.Color.Black;
            this.colorFrame.Properties.Appearance.Options.UseForeColor = true;
            this.colorFrame.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorFrame.Properties.ShowWebColors = false;
            this.colorFrame.Size = new System.Drawing.Size(120, 21);
            this.colorFrame.TabIndex = 87;
            // 
            // labelColor
            // 
            this.labelColor.Location = new System.Drawing.Point(46, 27);
            this.labelColor.Name = "labelColor";
            this.labelColor.Size = new System.Drawing.Size(37, 20);
            this.labelColor.TabIndex = 86;
            this.labelColor.Text = "颜色:";
            this.labelColor.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // TrackBarOpacity
            // 
            this.TrackBarOpacity.AutoSize = false;
            this.TrackBarOpacity.LargeChange = 32;
            this.TrackBarOpacity.Location = new System.Drawing.Point(143, 86);
            this.TrackBarOpacity.Maximum = 255;
            this.TrackBarOpacity.Name = "TrackBarOpacity";
            this.TrackBarOpacity.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.TrackBarOpacity.Size = new System.Drawing.Size(187, 28);
            this.TrackBarOpacity.TabIndex = 74;
            this.TrackBarOpacity.TickFrequency = 32;
            this.TrackBarOpacity.Value = 255;
            // 
            // colorCell
            // 
            this.colorCell.EditValue = System.Drawing.Color.Gold;
            this.colorCell.Location = new System.Drawing.Point(96, 24);
            this.colorCell.Name = "colorCell";
            this.colorCell.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorCell.Properties.ShowWebColors = false;
            this.colorCell.Size = new System.Drawing.Size(120, 21);
            this.colorCell.TabIndex = 84;
            // 
            // label100
            // 
            this.label100.AutoSize = true;
            this.label100.Location = new System.Drawing.Point(336, 92);
            this.label100.Name = "label100";
            this.label100.Size = new System.Drawing.Size(53, 12);
            this.label100.TabIndex = 77;
            this.label100.Text = "100%透明";
            this.label100.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // LabelOpacity
            // 
            this.LabelOpacity.AutoSize = true;
            this.LabelOpacity.Location = new System.Drawing.Point(36, 92);
            this.LabelOpacity.Name = "LabelOpacity";
            this.LabelOpacity.Size = new System.Drawing.Size(47, 12);
            this.LabelOpacity.TabIndex = 73;
            this.LabelOpacity.Text = "透明度:";
            // 
            // label0
            // 
            this.label0.AutoSize = true;
            this.label0.Location = new System.Drawing.Point(96, 92);
            this.label0.Name = "label0";
            this.label0.Size = new System.Drawing.Size(41, 12);
            this.label0.TabIndex = 76;
            this.label0.Text = "不透明";
            this.label0.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // checkBoxDisplay
            // 
            this.checkBoxDisplay.AutoSize = true;
            this.checkBoxDisplay.Location = new System.Drawing.Point(13, 0);
            this.checkBoxDisplay.Name = "checkBoxDisplay";
            this.checkBoxDisplay.Size = new System.Drawing.Size(72, 16);
            this.checkBoxDisplay.TabIndex = 78;
            this.checkBoxDisplay.Text = "显示图元";
            this.checkBoxDisplay.UseVisualStyleBackColor = true;
            // 
            // btnFont
            // 
            this.btnFont.Location = new System.Drawing.Point(399, 45);
            this.btnFont.Name = "btnFont";
            this.btnFont.Size = new System.Drawing.Size(75, 23);
            this.btnFont.TabIndex = 77;
            this.btnFont.Text = "字体...";
            // 
            // cbxDrawCellLabel
            // 
            this.cbxDrawCellLabel.AutoSize = true;
            this.cbxDrawCellLabel.Location = new System.Drawing.Point(13, 0);
            this.cbxDrawCellLabel.Name = "cbxDrawCellLabel";
            this.cbxDrawCellLabel.Size = new System.Drawing.Size(72, 16);
            this.cbxDrawCellLabel.TabIndex = 79;
            this.cbxDrawCellLabel.Text = "显示标签";
            this.cbxDrawCellLabel.UseVisualStyleBackColor = true;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.cbxDrawCellLabel);
            this.groupBox1.Controls.Add(this.cbxCellPCI);
            this.groupBox1.Controls.Add(this.btnFont);
            this.groupBox1.Controls.Add(this.cbxCellDes);
            this.groupBox1.Controls.Add(this.cbxCellFreqList);
            this.groupBox1.Controls.Add(this.cbxCellFreq);
            this.groupBox1.Controls.Add(this.cbxCellNCI);
            this.groupBox1.Controls.Add(this.cbxCellLAC);
            this.groupBox1.Controls.Add(this.cbxCellCode);
            this.groupBox1.Controls.Add(this.cbxCellName);
            this.groupBox1.Location = new System.Drawing.Point(24, 138);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(489, 74);
            this.groupBox1.TabIndex = 78;
            this.groupBox1.TabStop = false;
            // 
            // cbxCellCPI
            // 
            this.cbxCellPCI.AutoSize = true;
            this.cbxCellPCI.Location = new System.Drawing.Point(378, 24);
            this.cbxCellPCI.Name = "cbxCellCPI";
            this.cbxCellPCI.Size = new System.Drawing.Size(42, 16);
            this.cbxCellPCI.TabIndex = 8;
            this.cbxCellPCI.Text = "PCI";
            this.cbxCellPCI.UseVisualStyleBackColor = true;
            // 
            // cbxCellDes
            // 
            this.cbxCellDes.AutoSize = true;
            this.cbxCellDes.Location = new System.Drawing.Point(430, 24);
            this.cbxCellDes.Name = "cbxCellDes";
            this.cbxCellDes.Size = new System.Drawing.Size(48, 16);
            this.cbxCellDes.TabIndex = 7;
            this.cbxCellDes.Text = "描述";
            this.cbxCellDes.UseVisualStyleBackColor = true;
            // 
            // cbxCellFreqList
            // 
            this.cbxCellFreqList.AutoSize = true;
            this.cbxCellFreqList.Location = new System.Drawing.Point(299, 24);
            this.cbxCellFreqList.Name = "cbxCellFreqList";
            this.cbxCellFreqList.Size = new System.Drawing.Size(72, 16);
            this.cbxCellFreqList.TabIndex = 6;
            this.cbxCellFreqList.Text = "频点列表";
            this.cbxCellFreqList.UseVisualStyleBackColor = true;
            // 
            // cbxCellFreq
            // 
            this.cbxCellFreq.AutoSize = true;
            this.cbxCellFreq.Location = new System.Drawing.Point(233, 24);
            this.cbxCellFreq.Name = "cbxCellFreq";
            this.cbxCellFreq.Size = new System.Drawing.Size(54, 16);
            this.cbxCellFreq.TabIndex = 4;
            this.cbxCellFreq.Text = "ARFCN";
            this.cbxCellFreq.UseVisualStyleBackColor = true;
            // 
            // cbxCellCI
            // 
            this.cbxCellNCI.AutoSize = true;
            this.cbxCellNCI.Location = new System.Drawing.Point(187, 24);
            this.cbxCellNCI.Name = "cbxCellCI";
            this.cbxCellNCI.Size = new System.Drawing.Size(42, 16);
            this.cbxCellNCI.TabIndex = 3;
            this.cbxCellNCI.Text = "NCI";
            this.cbxCellNCI.UseVisualStyleBackColor = true;
            // 
            // cbxCellLAC
            // 
            this.cbxCellLAC.AutoSize = true;
            this.cbxCellLAC.Location = new System.Drawing.Point(133, 24);
            this.cbxCellLAC.Name = "cbxCellLAC";
            this.cbxCellLAC.Size = new System.Drawing.Size(42, 16);
            this.cbxCellLAC.TabIndex = 2;
            this.cbxCellLAC.Text = "TAC";
            this.cbxCellLAC.UseVisualStyleBackColor = true;
            // 
            // cbxCellCode
            // 
            this.cbxCellCode.AutoSize = true;
            this.cbxCellCode.Location = new System.Drawing.Point(79, 24);
            this.cbxCellCode.Name = "cbxCellCode";
            this.cbxCellCode.Size = new System.Drawing.Size(48, 16);
            this.cbxCellCode.TabIndex = 1;
            this.cbxCellCode.Text = "编码";
            this.cbxCellCode.UseVisualStyleBackColor = true;
            // 
            // cbxCellName
            // 
            this.cbxCellName.AutoSize = true;
            this.cbxCellName.Checked = true;
            this.cbxCellName.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbxCellName.Location = new System.Drawing.Point(25, 24);
            this.cbxCellName.Name = "cbxCellName";
            this.cbxCellName.Size = new System.Drawing.Size(48, 16);
            this.cbxCellName.TabIndex = 0;
            this.cbxCellName.Text = "名称";
            this.cbxCellName.UseVisualStyleBackColor = true;
            // 
            // MapNRCellLayerCellProperties
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.grpFactor);
            this.Name = "MapNRCellLayerCellProperties";
            this.Size = new System.Drawing.Size(537, 337);
            this.grpFactor.ResumeLayout(false);
            this.grpFactor.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.colorFrame.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.TrackBarOpacity)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorCell.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox grpFactor;
        private System.Windows.Forms.TrackBar TrackBarOpacity;
        private DevExpress.XtraEditors.ColorEdit colorCell;
        private System.Windows.Forms.Label label100;
        private System.Windows.Forms.Label LabelOpacity;
        private System.Windows.Forms.Label label0;
        private System.Windows.Forms.CheckBox checkBoxDisplay;
        private DevExpress.XtraEditors.SimpleButton btnFont;
        private System.Windows.Forms.CheckBox cbxDrawCellLabel;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.CheckBox cbxCellPCI;
        private System.Windows.Forms.CheckBox cbxCellDes;
        private System.Windows.Forms.CheckBox cbxCellFreqList;
        private System.Windows.Forms.CheckBox cbxCellFreq;
        private System.Windows.Forms.CheckBox cbxCellNCI;
        private System.Windows.Forms.CheckBox cbxCellLAC;
        private System.Windows.Forms.CheckBox cbxCellCode;
        private System.Windows.Forms.CheckBox cbxCellName;
        private System.Windows.Forms.Label labelColor;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.ColorEdit colorFrame;
    }
}
