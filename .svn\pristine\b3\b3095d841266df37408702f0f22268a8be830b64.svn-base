﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTGSMInterferenceQueryForm : DevExpress.XtraEditors.XtraForm
    {
        public CQTGSMInterferenceQueryForm(string netWoker)
        {
            InitializeComponent();
            this.Text = netWoker + this.Text;
        }

        public void getSelect(out int numci, out  int numall, out double numerror)
        {
            numci = Convert.ToInt32(spinEdit1.Value);
            numall = Convert.ToInt32(spinEdit2.Value.ToString());
            numerror = double.Parse(spinEdit3.Value.ToString());
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleButton2_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

       
    }
}