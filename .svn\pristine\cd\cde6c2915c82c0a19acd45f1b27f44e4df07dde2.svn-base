﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Func.EventBlock
{
    public class EventBlock
    {
        public EventBlock()
        {

        }
        public EventBlock(int idx, Event item)
        {
            this.ID = idx;
            AddAbnormalEvent(item);
        }

        public int ID { get; set; }
        //黑点ID
        public int BbId { get; set; }
        //权重序号
        public int WeightSn { get; set; }
        /// <summary>
        /// 用时两个时间段黑点对比功能中指示改黑点是否重现
        /// </summary>
        public bool Repeat { get; set; } = false;

        private readonly List<int> repeatIdList = new List<int>();
        /// <summary>
        /// 包含的EventBlockIds
        /// </summary>
        public List<int> RepeatIdList
        {
            get { return repeatIdList; }
        }
        private readonly List<Event> abnormalEvents = new List<Event>();
        public List<Event> AbnormalEvents
        {
            get { return abnormalEvents; }
        }
        public int AbnormalEventCount
        {
            get { return abnormalEvents.Count; }
        }
        public string AbnormalEventsDescription
        {
            get
            {
                StringBuilder sb = new StringBuilder();
                Dictionary<int, int> eventIDCountDic = new Dictionary<int, int>();
                foreach (Event evt in abnormalEvents)
                {
                    if (eventIDCountDic.ContainsKey(evt.ID))
                    {
                        eventIDCountDic[evt.ID]++;
                    }
                    else
                    {
                        eventIDCountDic[evt.ID] = 1;
                    }
                }
                foreach (int eventID in eventIDCountDic.Keys)
                {
                    EventInfo evt = EventInfoManager.GetInstance()[eventID];
                    string name = evt == null ? "事件ID:" + eventID : evt.Name;
                    sb.Append(name + ":" + eventIDCountDic[eventID].ToString() + "个;");
                }
                string desc = sb.ToString();
                if (desc.EndsWith(";"))
                {
                    desc = desc.Substring(0, desc.Length - 1);
                }
                return desc;
            }
        }
        public string AbnormalEventDetails
        {
            get
            {
                StringBuilder desc = new StringBuilder();
                foreach (Event et in this.abnormalEvents)
                {
                    if (et.EventInfo == null)
                    {
                        desc .Append( "事件ID:" + et.ID + " ");
                    }
                    else
                    {
                        desc.Append(et.EventInfo.Name + " ");
                    }
                    desc.Append(et.DateTime.ToString("yyyy-MM-dd HH:mm:ss"));
                    desc .Append(" ");
                    desc .Append(et.FileName);
                    desc .Append("$");
                }
                return desc.ToString();
            }
        }

        private readonly List<Event> abnormalDominanceEvents = new List<Event>();
        public double weight { get; set; } = 0;
        /// <summary>
        /// 显性异常事件
        /// </summary>
        public List<Event> AbnormalDominanceEvents
        {
            get { return abnormalDominanceEvents; }
        }
        public int AbnormalDominanceEventCount
        {
            get { return abnormalDominanceEvents.Count; }
        }

        private readonly List<Event> abnormalRecessivenessEvents = new List<Event>();
        /// <summary>
        /// 隐性异常事件
        /// </summary>
        public List<Event> AbnormalRecessivenessEvents
        {
            get { return abnormalRecessivenessEvents; }
        }
        public int AbnormalRecessivenessEventCount
        {
            get { return abnormalRecessivenessEvents.Count; }
        }
        
        public int FileCount { get; set; }

        internal void Join(EventBlock eblock)
        {
            foreach (Event e in eblock.AbnormalEvents)
            {
                AddAbnormalEvent(e);
            }
            if (!repeatIdList.Contains(eblock.ID))
            {
                repeatIdList.Add(eblock.ID);
            }
            foreach (int id in eblock.RepeatIdList)
            {
                if (!repeatIdList.Contains(id))
                {
                    repeatIdList.Add(id);
                }
            }
        }
        internal bool Within(double x1, double y1, double x2, double y2)
        {
            foreach (Event evt in abnormalEvents)
            {
                if (evt.Longitude >= x1 && evt.Longitude <= x2 && evt.Latitude >= y1 && evt.Latitude <= y2)
                {
                    return true;
                }
            }
            return false;
        }

        static int[] eventIDs = new int[] { 8, 10, 82, 88, 7, 6, 907, 908, 105, 111, 189, 201, 106, 112, 118, 124, 199, 200, 59 };
        private readonly List<int> DominanceEventIDs = new List<int>(eventIDs);
        public void AddAbnormalEvent(Event e)
        {
            if (!eventContained(e))
            {
                abnormalEvents.Add(e);

                if (DominanceEventIDs.Contains(e.ID))
                {
                    abnormalDominanceEvents.Add(e);   //统计显性事件数量
                    weight += 1.5; //显性权重累计加1.5
                }
                else
                {
                    abnormalRecessivenessEvents.Add(e);   //统计隐性事件数量
                    weight += 0.5; //隐性权重累计加0.5
                }
            }
        }
        /// <summary>
        /// 在距离范围内并且同LACCI
        /// </summary>
        /// <param name="evt"></param>
        /// <param name="radius"></param>
        /// <param name="sameLacCi"></param>
        /// <returns></returns>
        public bool Intersect(Event evt, int radius, bool sameLacCi)
        {
            if (sameLacCi)
            {
                return IntersectLACCIFirst(evt, radius);
            }
            else
            {
                return Intersect(evt.Longitude, evt.Latitude, radius);
            }
        }
        /// <summary>
        /// 同LACCI或者距离范围内
        /// </summary>
        /// <param name="longitude"></param>
        /// <param name="latitude"></param>
        /// <param name="radius"></param>
        /// <param name="sameLACCI"></param>
        /// <returns></returns>
        public bool IntersectLACCIFirst(Event evt, int radius)
        {
            foreach (Event e in abnormalEvents)
            {
                if (evt["LAC"] != null && evt["CI"] != null && e["LAC"] != null && e["CI"] != null &&
                    (int)evt["LAC"] != 0 && (int)evt["CI"] != 0 && (int)e["LAC"] != 0 && (int)e["CI"] != 0 &&
                    (int)evt["LAC"] == (int)e["LAC"] && (int)evt["CI"] == (int)e["CI"])
                {
                    return true;
                }
                if (MathFuncs.GetDistance(evt.Longitude, evt.Latitude, e.Longitude, e.Latitude) <= 2 * radius)
                {
                    return true;
                }
            }
            return false;
        }
        public bool Intersect(double longitude, double latitude, int radius)
        {
            foreach (Event e in abnormalEvents)
            {
                if (MathFuncs.GetDistance(longitude, latitude, e.Longitude, e.Latitude) <= 2 * radius)
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 以longitude，latitude为圆点，作半径为radius的圆，分别与已有事件的同半径圆判断是否相交
        /// </summary>
        /// <param name="longitude"></param>
        /// <param name="latitude"></param>
        /// <param name="radius"></param>
        /// <returns></returns>
        public bool Intersect(double longitude, double latitude, double radius)
        {
            double d = 2 * radius;
            double lngSpan = d * 0.00001;//直径对应的经度跨度
            double latSpan = d * 0.000009;//直径对应的纬度跨度
            foreach (Event e in abnormalEvents)
            {//距离判断优化，先进行判断经纬度差值判断（快），在转化成米单位的距离判断。
                if (Math.Abs(e.Longitude - longitude) <= lngSpan && Math.Abs(e.Latitude - latitude) <= latSpan)
                {
                    return true;
                }
                if (MathFuncs.GetDistance(longitude, latitude, e.Longitude, e.Latitude) <= d)
                {
                    return true;
                }
            }
            return false;
        }

        internal bool CoveredBy(List<GridBase> gridPoints, int radius)
        {
            int coveredEventCount = 0;

            foreach (Event e in abnormalEvents)
            {
                foreach (GridBase gd in gridPoints)
                {
                    if (MathFuncs.GetDistance(gd.midLong, gd.midLat, e.Longitude, e.Latitude) <= radius)
                    {
                        coveredEventCount++;
                        break;
                    }
                }
            }
            if (abnormalEvents.Count > 0)
            {
                if (((float)coveredEventCount) / (abnormalEvents.Count) >= 0.6)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            return true;

        }
      
        private bool eventContained(Event e)
        {
            return abnormalEvents.Contains(e);
        }

        internal DbPoint GetCenterPoint()
        {
            DbRect bounds = new DbRect();
            bool first = true;
            foreach (Event item in AbnormalEvents)
            {
                if (first)
                {
                    bounds.x1 = item.Longitude;
                    bounds.x2 = item.Longitude;
                    bounds.y1 = item.Latitude;
                    bounds.y2 = item.Latitude;
                    first = false;
                }
                else
                {
                    setOtherBounds(bounds, item);
                }
            }
            return bounds.Center();
        }

        private void setOtherBounds(DbRect bounds, Event item)
        {
            if (bounds.x1 > item.Longitude)
            {
                bounds.x1 = item.Longitude;
            }
            if (bounds.x2 < item.Longitude)
            {
                bounds.x2 = item.Longitude;
            }
            if (bounds.y1 > item.Latitude)
            {
                bounds.y1 = item.Latitude;
            }
            if (bounds.y2 < item.Latitude)
            {
                bounds.y2 = item.Latitude;
            }
        }

        public string EventBlockDesc
        {
            get
            {
                StringBuilder sb = new StringBuilder();
                sb.Append("汇聚点ID：" + this.WeightSn.ToString());
                sb.Append("$中心经度：" + this.GetCenterPoint().x.ToString());
                sb.Append("$中心纬度：" + this.GetCenterPoint().y.ToString());
                sb.Append("$权重：" + this.weight.ToString());
                sb.Append("$显性事件数目：" + this.AbnormalDominanceEventCount.ToString());
                sb.Append("$隐性事件数目：" + this.AbnormalRecessivenessEventCount.ToString());
                sb.Append("$汇聚点事件描述：" + this.AbnormalEventsDescription);
                sb.Append("$汇聚点事件详情：$");
                sb.Append(this.AbnormalEventDetails);
                return sb.ToString();
            }
        }
        internal string GetHasBlackBlockDesc(MapForm mform)
        {
            foreach (Event evt in AbnormalEvents)
            {
                if (mform.HasIntersectedBlackBlock(evt.Longitude, evt.Latitude))
                {
                    return "是";
                }
            }
            return "否";
        }
        internal string GetHasBlackBlockID(MapForm mform)
        {
            foreach (Event evt in AbnormalEvents)
            {
                if (mform.HasIntersectedBlackBlock(evt.Longitude, evt.Latitude,this))
                {
                    return "是";
                }
            }
            return "否";
        }

        private string roadPlaceDesc = null;
        public string RoadPlaceDesc
        {
            get
            {
                if (roadPlaceDesc == null)
                {
                    roadPlaceDesc = GISManager.GetInstance().GetRoadPlaceDesc(GetCenterPoint().x, GetCenterPoint().y);
                }
                return roadPlaceDesc;
            }
        }

        private string areaPlaceDesc = null;
        public string AreaPlaceDesc
        {
            get
            {
                if (areaPlaceDesc == null)
                {
                    areaPlaceDesc = GISManager.GetInstance().GetGridDesc(GetCenterPoint().x, GetCenterPoint().y);
                }
                return areaPlaceDesc;
            }
        }

        /// <summary>
        /// 按权重大小排序
        /// </summary>
        /// <returns></returns>
        public static IComparer<EventBlock> GetComparebyWeight()
        {
            if (comparebyWeight == null)
            {
                comparebyWeight = new ComparebyWeight();
            }
            return comparebyWeight;
        }
        private static IComparer<EventBlock> comparebyWeight;

        public class ComparebyWeight : IComparer<EventBlock>
        {
            public int Compare(EventBlock x, EventBlock y)
            {
                return x.weight.CompareTo(y.weight);
            }
        }
    }
}
