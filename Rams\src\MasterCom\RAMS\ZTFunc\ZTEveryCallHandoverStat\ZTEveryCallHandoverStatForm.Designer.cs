﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTEveryCallHandoverStatForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTEveryCallHandoverStatForm));
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.objectListView = new BrightIdeasSoftware.ObjectListView();
            this.olvColumnXH = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnFileName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnStartTime = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnEndTime = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDistance = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHandoverRequestCount = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHandoverSuccessCount = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHandoverFailureCount = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHandoverTimeIntervalMean = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHandoverTimeIntervalMin = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHandoverTimeIntervalMax = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).BeginInit();
            this.SuspendLayout();
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExport2Xls});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(166, 26);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(165, 22);
            this.miExport2Xls.Text = "导出数据到Excel";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // objectListView
            // 
            this.objectListView.AllColumns.Add(this.olvColumnXH);
            this.objectListView.AllColumns.Add(this.olvColumnFileName);
            this.objectListView.AllColumns.Add(this.olvColumnStartTime);
            this.objectListView.AllColumns.Add(this.olvColumnEndTime);
            this.objectListView.AllColumns.Add(this.olvColumnDistance);
            this.objectListView.AllColumns.Add(this.olvColumnHandoverRequestCount);
            this.objectListView.AllColumns.Add(this.olvColumnHandoverSuccessCount);
            this.objectListView.AllColumns.Add(this.olvColumnHandoverFailureCount);
            this.objectListView.AllColumns.Add(this.olvColumnHandoverTimeIntervalMean);
            this.objectListView.AllColumns.Add(this.olvColumnHandoverTimeIntervalMin);
            this.objectListView.AllColumns.Add(this.olvColumnHandoverTimeIntervalMax);
            this.objectListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnXH,
            this.olvColumnFileName,
            this.olvColumnStartTime,
            this.olvColumnEndTime,
            this.olvColumnDistance,
            this.olvColumnHandoverRequestCount,
            this.olvColumnHandoverSuccessCount,
            this.olvColumnHandoverFailureCount,
            this.olvColumnHandoverTimeIntervalMean,
            this.olvColumnHandoverTimeIntervalMin,
            this.olvColumnHandoverTimeIntervalMax});
            this.objectListView.ContextMenuStrip = this.contextMenuStrip;
            this.objectListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.objectListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.objectListView.FullRowSelect = true;
            this.objectListView.GridLines = true;
            this.objectListView.HeaderWordWrap = true;
            this.objectListView.Location = new System.Drawing.Point(0, 0);
            this.objectListView.Name = "objectListView";
            this.objectListView.ShowGroups = false;
            this.objectListView.Size = new System.Drawing.Size(959, 311);
            this.objectListView.TabIndex = 1;
            this.objectListView.UseCompatibleStateImageBehavior = false;
            this.objectListView.View = System.Windows.Forms.View.Details;
            this.objectListView.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.objectListView_MouseDoubleClick);
            // 
            // olvColumnXH
            // 
            this.olvColumnXH.HeaderFont = null;
            this.olvColumnXH.Text = "序号";
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.AspectName = "FileName";
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名";
            this.olvColumnFileName.Width = 150;
            // 
            // olvColumnStartTime
            // 
            this.olvColumnStartTime.AspectName = "StartDateTime";
            this.olvColumnStartTime.HeaderFont = null;
            this.olvColumnStartTime.Text = "起呼时间";
            // 
            // olvColumnEndTime
            // 
            this.olvColumnEndTime.AspectName = "EndDateTime";
            this.olvColumnEndTime.HeaderFont = null;
            this.olvColumnEndTime.Text = "结束时间";
            // 
            // olvColumnDistance
            // 
            this.olvColumnDistance.AspectName = "Distance";
            this.olvColumnDistance.HeaderFont = null;
            this.olvColumnDistance.Text = "持续距离(米)";
            this.olvColumnDistance.Width = 80;
            // 
            // olvColumnHandoverRequestCount
            // 
            this.olvColumnHandoverRequestCount.AspectName = "HandoverRequestCount";
            this.olvColumnHandoverRequestCount.HeaderFont = null;
            this.olvColumnHandoverRequestCount.Text = "切换请求次数";
            this.olvColumnHandoverRequestCount.Width = 80;
            // 
            // olvColumnHandoverSuccessCount
            // 
            this.olvColumnHandoverSuccessCount.AspectName = "HandoverSuccessCount";
            this.olvColumnHandoverSuccessCount.HeaderFont = null;
            this.olvColumnHandoverSuccessCount.Text = "切换成功次数";
            this.olvColumnHandoverSuccessCount.Width = 80;
            // 
            // olvColumnHandoverFailureCount
            // 
            this.olvColumnHandoverFailureCount.AspectName = "HandoverFailureCount";
            this.olvColumnHandoverFailureCount.HeaderFont = null;
            this.olvColumnHandoverFailureCount.Text = "切换失败次数";
            this.olvColumnHandoverFailureCount.Width = 80;
            // 
            // olvColumnHandoverTimeIntervalMean
            // 
            this.olvColumnHandoverTimeIntervalMean.AspectName = "HandoverTimeIntervalMean";
            this.olvColumnHandoverTimeIntervalMean.HeaderFont = null;
            this.olvColumnHandoverTimeIntervalMean.Text = "切换间隔平均值(秒)";
            this.olvColumnHandoverTimeIntervalMean.Width = 80;
            // 
            // olvColumnHandoverTimeIntervalMin
            // 
            this.olvColumnHandoverTimeIntervalMin.AspectName = "HandoverTimeIntervalMin";
            this.olvColumnHandoverTimeIntervalMin.HeaderFont = null;
            this.olvColumnHandoverTimeIntervalMin.Text = "切换间隔最小值(秒)";
            this.olvColumnHandoverTimeIntervalMin.Width = 80;
            // 
            // olvColumnHandoverTimeIntervalMax
            // 
            this.olvColumnHandoverTimeIntervalMax.AspectName = "HandoverTimeIntervalMax";
            this.olvColumnHandoverTimeIntervalMax.HeaderFont = null;
            this.olvColumnHandoverTimeIntervalMax.Text = "切换间隔最大值(秒)";
            this.olvColumnHandoverTimeIntervalMax.Width = 80;
            // 
            // ZTEveryCallHandoverStatForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(959, 311);
            this.Controls.Add(this.objectListView);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTEveryCallHandoverStatForm";
            this.Text = "每呼叫切换统计";
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
        private BrightIdeasSoftware.ObjectListView objectListView;
        private BrightIdeasSoftware.OLVColumn olvColumnXH;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnStartTime;
        private BrightIdeasSoftware.OLVColumn olvColumnEndTime;
        private BrightIdeasSoftware.OLVColumn olvColumnDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnHandoverRequestCount;
        private BrightIdeasSoftware.OLVColumn olvColumnHandoverSuccessCount;
        private BrightIdeasSoftware.OLVColumn olvColumnHandoverFailureCount;
        private BrightIdeasSoftware.OLVColumn olvColumnHandoverTimeIntervalMin;
        private BrightIdeasSoftware.OLVColumn olvColumnHandoverTimeIntervalMax;
        private BrightIdeasSoftware.OLVColumn olvColumnHandoverTimeIntervalMean;
    }
}