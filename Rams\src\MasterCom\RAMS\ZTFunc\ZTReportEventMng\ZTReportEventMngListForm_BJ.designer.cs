﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTReportEventMngListForm_BJ
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTReportEventMngListForm_BJ));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplayEvent = new System.Windows.Forms.ToolStripMenuItem();
            this.miCompareReplayEvent = new System.Windows.Forms.ToolStripMenuItem();
            this.miToOtherArea = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridControlReportEvent = new DevExpress.XtraGrid.GridControl();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumnSN = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnNetType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnEventType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnEventName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnAreaName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnProjName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTestDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnTestTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnFileName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnGridName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnGridGroup = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCellName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLAC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLongitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnLatitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnRemark = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCauseDeal = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCauseDetailDeal = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnMethodDeal = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnSolution = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnIsRectify = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnIsReTest = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnIsKeyProb = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnOptEffect = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDwUserName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDwTime = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnDeadLine = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCauseProcess = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnCauseDetailProcess = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnMethodProcess = new DevExpress.XtraGrid.Columns.GridColumn();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlReportEvent)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplayEvent,
            this.miCompareReplayEvent,
            this.miToOtherArea,
            this.miExportExcel});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(149, 92);
            // 
            // miReplayEvent
            // 
            this.miReplayEvent.Name = "miReplayEvent";
            this.miReplayEvent.Size = new System.Drawing.Size(148, 22);
            this.miReplayEvent.Text = "回放事件";
            this.miReplayEvent.Click += new System.EventHandler(this.miReplayEvent_Click);
            // 
            // miCompareReplayEvent
            // 
            this.miCompareReplayEvent.Name = "miCompareReplayEvent";
            this.miCompareReplayEvent.Size = new System.Drawing.Size(148, 22);
            this.miCompareReplayEvent.Text = "对比回放事件";
            this.miCompareReplayEvent.Click += new System.EventHandler(this.miCompareReplayEvent_Click);
            // 
            // miToOtherArea
            // 
            this.miToOtherArea.Name = "miToOtherArea";
            this.miToOtherArea.Size = new System.Drawing.Size(148, 22);
            this.miToOtherArea.Text = "转至其它区域";
            this.miToOtherArea.Click += new System.EventHandler(this.miToOtherArea_Click);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(148, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // gridControlReportEvent
            // 
            this.gridControlReportEvent.ContextMenuStrip = this.ctxMenu;
            this.gridControlReportEvent.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlReportEvent.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlReportEvent.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlReportEvent.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlReportEvent.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlReportEvent.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlReportEvent.Location = new System.Drawing.Point(0, 0);
            this.gridControlReportEvent.MainView = this.gridView;
            this.gridControlReportEvent.Name = "gridControlReportEvent";
            this.gridControlReportEvent.Size = new System.Drawing.Size(1288, 581);
            this.gridControlReportEvent.TabIndex = 5;
            this.gridControlReportEvent.UseEmbeddedNavigator = true;
            this.gridControlReportEvent.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            // 
            // gridView
            // 
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumnSN,
            this.gridColumnNetType,
            this.gridColumnEventType,
            this.gridColumnEventName,
            this.gridColumnAreaName,
            this.gridColumnProjName,
            this.gridColumnTestDate,
            this.gridColumnTestTime,
            this.gridColumnFileName,
            this.gridColumnGridName,
            this.gridColumnGridGroup,
            this.gridColumnCellName,
            this.gridColumnLAC,
            this.gridColumnCI,
            this.gridColumnLongitude,
            this.gridColumnLatitude,
            this.gridColumnRemark,
            this.gridColumnCauseDeal,
            this.gridColumnCauseDetailDeal,
            this.gridColumnMethodDeal,
            this.gridColumnSolution,
            this.gridColumnIsRectify,
            this.gridColumnIsReTest,
            this.gridColumnIsKeyProb,
            this.gridColumnOptEffect,
            this.gridColumnDwUserName,
            this.gridColumnDwTime,
            this.gridColumnDeadLine,
            this.gridColumnCauseProcess,
            this.gridColumnCauseDetailProcess,
            this.gridColumnMethodProcess});
            this.gridView.GridControl = this.gridControlReportEvent;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsView.ColumnAutoWidth = false;
            this.gridView.OptionsView.ShowDetailButtons = false;
            this.gridView.OptionsView.ShowGroupPanel = false;
            this.gridView.DoubleClick += new System.EventHandler(this.gridView_DoubleClick);
            // 
            // gridColumnSN
            // 
            this.gridColumnSN.Caption = "序号";
            this.gridColumnSN.FieldName = "SN";
            this.gridColumnSN.Name = "gridColumnSN";
            this.gridColumnSN.Visible = true;
            this.gridColumnSN.VisibleIndex = 0;
            this.gridColumnSN.Width = 47;
            // 
            // gridColumnNetType
            // 
            this.gridColumnNetType.Caption = "网络制式";
            this.gridColumnNetType.FieldName = "NetType";
            this.gridColumnNetType.Name = "gridColumnNetType";
            this.gridColumnNetType.Visible = true;
            this.gridColumnNetType.VisibleIndex = 1;
            this.gridColumnNetType.Width = 60;
            // 
            // gridColumnEventType
            // 
            this.gridColumnEventType.Caption = "事件类别";
            this.gridColumnEventType.FieldName = "EventType";
            this.gridColumnEventType.Name = "gridColumnEventType";
            this.gridColumnEventType.Visible = true;
            this.gridColumnEventType.VisibleIndex = 2;
            this.gridColumnEventType.Width = 60;
            // 
            // gridColumnEventName
            // 
            this.gridColumnEventName.Caption = "事件名称";
            this.gridColumnEventName.FieldName = "EventName";
            this.gridColumnEventName.Name = "gridColumnEventName";
            this.gridColumnEventName.Visible = true;
            this.gridColumnEventName.VisibleIndex = 3;
            this.gridColumnEventName.Width = 80;
            // 
            // gridColumnAreaName
            // 
            this.gridColumnAreaName.Caption = "区域名称";
            this.gridColumnAreaName.FieldName = "AreaName";
            this.gridColumnAreaName.Name = "gridColumnAreaName";
            this.gridColumnAreaName.Visible = true;
            this.gridColumnAreaName.VisibleIndex = 4;
            // 
            // gridColumnProjName
            // 
            this.gridColumnProjName.Caption = "测试类别";
            this.gridColumnProjName.FieldName = "ProjName";
            this.gridColumnProjName.Name = "gridColumnProjName";
            this.gridColumnProjName.Visible = true;
            this.gridColumnProjName.VisibleIndex = 5;
            // 
            // gridColumnTestDate
            // 
            this.gridColumnTestDate.Caption = "测试日期";
            this.gridColumnTestDate.FieldName = "TestDate";
            this.gridColumnTestDate.Name = "gridColumnTestDate";
            this.gridColumnTestDate.Visible = true;
            this.gridColumnTestDate.VisibleIndex = 6;
            // 
            // gridColumnTestTime
            // 
            this.gridColumnTestTime.Caption = "事件时间";
            this.gridColumnTestTime.FieldName = "TestTime";
            this.gridColumnTestTime.Name = "gridColumnTestTime";
            this.gridColumnTestTime.Visible = true;
            this.gridColumnTestTime.VisibleIndex = 7;
            // 
            // gridColumnFileName
            // 
            this.gridColumnFileName.Caption = "文件名称";
            this.gridColumnFileName.FieldName = "FileName";
            this.gridColumnFileName.Name = "gridColumnFileName";
            this.gridColumnFileName.Visible = true;
            this.gridColumnFileName.VisibleIndex = 8;
            // 
            // gridColumnGridName
            // 
            this.gridColumnGridName.Caption = "网格名称";
            this.gridColumnGridName.FieldName = "GridName";
            this.gridColumnGridName.Name = "gridColumnGridName";
            this.gridColumnGridName.Visible = true;
            this.gridColumnGridName.VisibleIndex = 10;
            // 
            // gridColumnGridGroup
            // 
            this.gridColumnGridGroup.Caption = "所属网格组";
            this.gridColumnGridGroup.FieldName = "GridGroup";
            this.gridColumnGridGroup.Name = "gridColumnGridGroup";
            this.gridColumnGridGroup.Visible = true;
            this.gridColumnGridGroup.VisibleIndex = 9;
            // 
            // gridColumnCellName
            // 
            this.gridColumnCellName.Caption = "小区名称";
            this.gridColumnCellName.FieldName = "CellName";
            this.gridColumnCellName.Name = "gridColumnCellName";
            this.gridColumnCellName.Visible = true;
            this.gridColumnCellName.VisibleIndex = 11;
            // 
            // gridColumnLAC
            // 
            this.gridColumnLAC.Caption = "LAC";
            this.gridColumnLAC.FieldName = "LAC";
            this.gridColumnLAC.Name = "gridColumnLAC";
            this.gridColumnLAC.Visible = true;
            this.gridColumnLAC.VisibleIndex = 12;
            // 
            // gridColumnCI
            // 
            this.gridColumnCI.Caption = "CI";
            this.gridColumnCI.FieldName = "CI";
            this.gridColumnCI.Name = "gridColumnCI";
            this.gridColumnCI.Visible = true;
            this.gridColumnCI.VisibleIndex = 13;
            // 
            // gridColumnLongitude
            // 
            this.gridColumnLongitude.Caption = "经度";
            this.gridColumnLongitude.FieldName = "Longitude";
            this.gridColumnLongitude.Name = "gridColumnLongitude";
            this.gridColumnLongitude.Visible = true;
            this.gridColumnLongitude.VisibleIndex = 14;
            // 
            // gridColumnLatitude
            // 
            this.gridColumnLatitude.Caption = "纬度";
            this.gridColumnLatitude.FieldName = "Latitude";
            this.gridColumnLatitude.Name = "gridColumnLatitude";
            this.gridColumnLatitude.Visible = true;
            this.gridColumnLatitude.VisibleIndex = 15;
            // 
            // gridColumnRemark
            // 
            this.gridColumnRemark.Caption = "备注";
            this.gridColumnRemark.FieldName = "Remark";
            this.gridColumnRemark.Name = "gridColumnRemark";
            this.gridColumnRemark.Visible = true;
            this.gridColumnRemark.VisibleIndex = 16;
            // 
            // gridColumnCauseDeal
            // 
            this.gridColumnCauseDeal.Caption = "原因";
            this.gridColumnCauseDeal.FieldName = "CauseDeal";
            this.gridColumnCauseDeal.Name = "gridColumnCauseDeal";
            this.gridColumnCauseDeal.Visible = true;
            this.gridColumnCauseDeal.VisibleIndex = 17;
            // 
            // gridColumnCauseDetailDeal
            // 
            this.gridColumnCauseDetailDeal.Caption = "原因分析";
            this.gridColumnCauseDetailDeal.FieldName = "CauseDetailDeal";
            this.gridColumnCauseDetailDeal.Name = "gridColumnCauseDetailDeal";
            this.gridColumnCauseDetailDeal.Visible = true;
            this.gridColumnCauseDetailDeal.VisibleIndex = 18;
            // 
            // gridColumnMethodDeal
            // 
            this.gridColumnMethodDeal.Caption = "问题分析";
            this.gridColumnMethodDeal.FieldName = "MethodDeal";
            this.gridColumnMethodDeal.Name = "gridColumnMethodDeal";
            this.gridColumnMethodDeal.Visible = true;
            this.gridColumnMethodDeal.VisibleIndex = 19;
            // 
            // gridColumnSolution
            // 
            this.gridColumnSolution.Caption = "解决办法";
            this.gridColumnSolution.FieldName = "Solution";
            this.gridColumnSolution.Name = "gridColumnSolution";
            this.gridColumnSolution.Visible = true;
            this.gridColumnSolution.VisibleIndex = 20;
            // 
            // gridColumnIsRectify
            // 
            this.gridColumnIsRectify.Caption = "已整改";
            this.gridColumnIsRectify.FieldName = "IsRectify";
            this.gridColumnIsRectify.Name = "gridColumnIsRectify";
            this.gridColumnIsRectify.Visible = true;
            this.gridColumnIsRectify.VisibleIndex = 21;
            // 
            // gridColumnIsReTest
            // 
            this.gridColumnIsReTest.Caption = "已复测";
            this.gridColumnIsReTest.FieldName = "IsReTest";
            this.gridColumnIsReTest.Name = "gridColumnIsReTest";
            this.gridColumnIsReTest.Visible = true;
            this.gridColumnIsReTest.VisibleIndex = 22;
            // 
            // gridColumnIsKeyProb
            // 
            this.gridColumnIsKeyProb.Caption = "是否重点问题";
            this.gridColumnIsKeyProb.FieldName = "IsKeyProb";
            this.gridColumnIsKeyProb.Name = "gridColumnIsKeyProb";
            this.gridColumnIsKeyProb.Visible = true;
            this.gridColumnIsKeyProb.VisibleIndex = 23;
            // 
            // gridColumnOptEffect
            // 
            this.gridColumnOptEffect.Caption = "复测结果";
            this.gridColumnOptEffect.FieldName = "OptEffect";
            this.gridColumnOptEffect.Name = "gridColumnOptEffect";
            this.gridColumnOptEffect.Visible = true;
            this.gridColumnOptEffect.VisibleIndex = 24;
            // 
            // gridColumnDwUserName
            // 
            this.gridColumnDwUserName.Caption = "处理人";
            this.gridColumnDwUserName.FieldName = "DwUserName";
            this.gridColumnDwUserName.Name = "gridColumnDwUserName";
            this.gridColumnDwUserName.Visible = true;
            this.gridColumnDwUserName.VisibleIndex = 25;
            // 
            // gridColumnDwTime
            // 
            this.gridColumnDwTime.Caption = "处理时间";
            this.gridColumnDwTime.FieldName = "DwTime";
            this.gridColumnDwTime.Name = "gridColumnDwTime";
            this.gridColumnDwTime.Visible = true;
            this.gridColumnDwTime.VisibleIndex = 26;
            // 
            // gridColumnDeadLine
            // 
            this.gridColumnDeadLine.Caption = "最迟反馈日期";
            this.gridColumnDeadLine.FieldName = "DealLine";
            this.gridColumnDeadLine.Name = "gridColumnDeadLine";
            this.gridColumnDeadLine.Visible = true;
            this.gridColumnDeadLine.VisibleIndex = 27;
            // 
            // gridColumnCauseProcess
            // 
            this.gridColumnCauseProcess.Caption = "预处理原因";
            this.gridColumnCauseProcess.FieldName = "CauseProcess";
            this.gridColumnCauseProcess.Name = "gridColumnCauseProcess";
            this.gridColumnCauseProcess.Visible = true;
            this.gridColumnCauseProcess.VisibleIndex = 28;
            // 
            // gridColumnCauseDetailProcess
            // 
            this.gridColumnCauseDetailProcess.Caption = "预处理原因分析";
            this.gridColumnCauseDetailProcess.FieldName = "CauseDetailProcess";
            this.gridColumnCauseDetailProcess.Name = "gridColumnCauseDetailProcess";
            this.gridColumnCauseDetailProcess.Visible = true;
            this.gridColumnCauseDetailProcess.VisibleIndex = 29;
            // 
            // gridColumnMethodProcess
            // 
            this.gridColumnMethodProcess.Caption = "预处理问题分析";
            this.gridColumnMethodProcess.FieldName = "MethodProcess";
            this.gridColumnMethodProcess.Name = "gridColumnMethodProcess";
            this.gridColumnMethodProcess.Visible = true;
            this.gridColumnMethodProcess.VisibleIndex = 30;
            // 
            // ZTReportEventMngListForm_BJ
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1288, 581);
            this.Controls.Add(this.gridControlReportEvent);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTReportEventMngListForm_BJ";
            this.Text = "异常事件跟踪";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlReportEvent)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripMenuItem miReplayEvent;
        private DevExpress.XtraGrid.GridControl gridControlReportEvent;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnSN;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnNetType;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnEventType;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnEventName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnAreaName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnProjName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTestDate;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnTestTime;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnFileName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnGridName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCellName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLAC;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCI;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLongitude;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnLatitude;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnRemark;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCauseDeal;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCauseDetailDeal;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnMethodDeal;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnSolution;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnIsRectify;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnIsReTest;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnOptEffect;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDwUserName;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDwTime;
        private System.Windows.Forms.ToolStripMenuItem miCompareReplayEvent;
        private System.Windows.Forms.ToolStripMenuItem miToOtherArea;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnGridGroup;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnDeadLine;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnIsKeyProb;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCauseProcess;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnCauseDetailProcess;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnMethodProcess;

    }
}