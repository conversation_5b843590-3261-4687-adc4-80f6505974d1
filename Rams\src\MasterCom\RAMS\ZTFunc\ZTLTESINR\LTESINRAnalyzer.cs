﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;


namespace MasterCom.RAMS.ZTFunc.ZTLTESINR
{
    public class LTESINRAnalyzer
    {
        public SaveCondition Condition { get; set; }
        //保存所有结果
        public List<ResultModel> Results { get; set; }

        public LTESINRAnalyzer()
        {
            Condition = new SaveCondition();
            Results = new List<ResultModel>();
            
        }

        public void Refreshs()
        {
            if (Results == null)
            {
                Results = new List<ResultModel>();
            }
            else
            {
                Results.Clear();
            }
            SINRCondition sinr = Condition.SINRCond;
            for (int i = 1; i < 4; i++)
            {
                ResultModel result = new ResultModel(sinr.SINRType, sinr.SINR, sinr.KPIName, i);
                List<ItemModel> items = createModelList(sinr);
                List<ItemModel> totalItems = createModelList(sinr);
                result.sinrModel.Items = items;
                result.totalModel.Items = totalItems;

                Results.Add(result);
            }

        }

        public List<ResultModel> GetResult()
        {
            return Results;
        }

        public void ClearResult()
        {
            Results = new List<ResultModel>();
        }
        public void Analyze(List<DTFileDataManager> fileManagers)
        {
            int count = fileManagers.Count;
            for (int i = 0; i < count; i++)
            {
                DTFileDataManager dtFile = fileManagers[0];
                Analyze(dtFile, dtFile.CarrierType);
                fileManagers.Remove(dtFile);
                
            }
        }
        private void Analyze(DTFileDataManager fileManager, int carrierType)
        {
           foreach(TestPoint tp in fileManager.TestPoints)
           {
               analyzeSINR(tp, carrierType);
           }
        }
        private void analyzeSINR(TestPoint tp, int carrierType)
        {
            SINRCondition sinr = Condition.SINRCond;


            float sinrValue;

            if (carrierType == 1)
            {
                sinrValue = (float)tp["lte_SINR"];
            }
            else
            {
                sinrValue = (float)tp["lte_fdd_SINR"];
            }
            if (sinr.IsEligibility(sinrValue))
            {
                analyzeItem(tp, Results[carrierType - 1].sinrModel, carrierType);
            }
            analyzeItem(tp, Results[carrierType - 1].totalModel, carrierType);

        }

    
        private void analyzeItem(TestPoint tp, SINRModel sinrModel,int carrierType)
        {
            foreach (ItemModel item in sinrModel.Items)
            {
                float? rsrp;
                if (carrierType == 1)
                {
                    rsrp = (float?)tp["lte_RSRP"];
                }
                else
                {
                    rsrp = (float?)tp["lte_fdd_RSRP"];
                }
                if (rsrp == null)
                {
                    return;
                }
                addValidPointCount(item, rsrp);
            }
        }

        private static void addValidPointCount(ItemModel item, float? rsrp)
        {
            if (item.OperType == (int)EnumOperatorsType.GreaterThan)
            {
                if (rsrp > item.ExtremumFirst)
                {
                    item.PointCount++;
                }
            }
            else if (item.OperType == (int)EnumOperatorsType.GTAndLT)
            {
                if (rsrp > item.ExtremumFirst && rsrp <= item.ExtremumSecond)
                {
                    item.PointCount++;
                }
            }
            else if (item.OperType == (int)EnumOperatorsType.EqualOrLessThan)
            {
                if (rsrp <= item.ExtremumFirst)
                {
                    item.PointCount++;
                }
            }
            else
            {
                //
            }
        }

        private List<ItemModel> createModelList( SINRCondition sinr)
        {
            List<ItemModel> itemModels = new List<ItemModel>();
            ItemModel temp = null;
            foreach(SINRConditionItem item in sinr.Conditions)
            {
                temp = new ItemModel(item.OperType, item.ExtremumFirst, item.ExtremumSecond, sinr.KPIName);
                itemModels.Add(temp);
            }
            return itemModels;
            
        }
    }
}
