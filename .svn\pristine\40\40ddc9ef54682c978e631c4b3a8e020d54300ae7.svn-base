﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
namespace MasterCom.RAMS.NOP
{
    public class OrderAnalyzer
    {
        private Dictionary<string, OrderAnalyzerResult> results = null;

        public List<OrderAnalyzerResult> GetResults()
        {
            List<OrderAnalyzerResult> resultList = new List<OrderAnalyzerResult>(results.Values);
            return resultList;
        }
        public void Analyze(Dictionary<int, List<GroupStatModel>> models, Dictionary<int, List<TimerModel>> timers)
        {
            results = creatResults();
            foreach (var item in models)
            {
                if (item.Value.Count == 0)
                {
                    continue;
                }
                string discrict = item.Value[0].District;
                int countIndex = validDistrict(item.Value[0].District);
                if (countIndex == 6)
                {
                    if (item.Value[0].AreaName.Contains("天门市"))
                    {
                        discrict = "天门";
                    }
                    else if (item.Value[0].AreaName.Contains("潜江市"))
                    {
                        discrict = "潜江";
                    }
                }

                List<TimerModel> timerList =null;
                if (timers.ContainsKey(item.Key))
                {
                    timerList = timers[item.Key];
                }
                else
                {
                    timerList = new List<TimerModel>();
                }

                dealOneOrder(results[discrict], results["总计"], item.Value, timerList);

            }
        }

        private void dealOneOrder(OrderAnalyzerResult result, OrderAnalyzerResult totalResult, List<GroupStatModel> models, List<TimerModel> timers)
        {
            result.Param["生成工单总数"]++;
            totalResult.Param["生成工单总数"]++;

            //预关处理工单判断0为工单没有预处理,1为有预处理且为未驳回工单,大于1为有预处理且为驳回工单
            int proDealOrder = 0;

            //T1及时性
            bool T1ProUnTimeout = true;

            //T2方案准确性的两个指标
            int checkFailed = 0;
            int T2ToT1 = 0;

            //方案可行性的两个指标
            int executableOrder = 0;
            int ToT0 = 0;
            bool valid = true; //如果包含工程督办,则不需要统计到可执行的工单数里,包含为false

            //T2方案制定及时性的两个指标
            bool timelinessOrder = true;
            bool T2Order = false;

            //分公司落实及时性
            bool T0UnTimeout = true;
            bool ToT0Order = false;

            //T0落实准确性
            int T0VeracityOrder = 0;
            bool hadEvent = true;
            bool T0reply = false;

            //质检及时性 
            bool T1CheckOrder = false;
            bool T2CheckOrder = false;
            bool T1CheckTimely = true;
            bool T2CheckTimely = true;

            //督办挂起解决率
            bool DuBan = false;

            foreach (GroupStatModel item in models)
            {
                //判断预处理工单
                if (item.Description.Contains("T1_分析支撑组保存了工单,工单状态为[预处理]"))
                {
                    proDealOrder++;
                }

                //T2方案准确性判断
                if (item.Description.Contains("[预处理]变为[已派单]"))
                {
                    T2ToT1++;
                }
                else if (item.Description.Contains("指标是否达标:不达标"))
                {
                    checkFailed++;
                }

                //方案可行性判断
                addExecutable(ref executableOrder, ref ToT0, ref valid, ref DuBan, item);

                //T2方案制定及时性判断
                if (item.Description.Contains("T1_分析支撑组保存了工单,工单状态为[预处理]。") && item.Description.Contains("用户组别:T2"))
                {
                    T2Order = true;
                }

                //T0落实及时性判断
                if (item.Description.Contains("[预处理]变为[已派单]"))
                {
                    ToT0Order = true;
                }

                //质检及时性判断
                addCheckTimely(ref T1CheckOrder, ref T2CheckOrder, item);

                //T0落实准确性判断
                addT0Veracity(ref T0VeracityOrder, ref hadEvent, ref T0reply, item);
            }

            judgeStatus(timers, ref T1ProUnTimeout, ref timelinessOrder, ref T0UnTimeout, ref T1CheckTimely, ref T2CheckTimely);

            //预处理工单,处理派单准确性和预处理完整性
            proDeal(result, totalResult, proDealOrder, T1ProUnTimeout);

            //T2方案准确性
            t2VeracityDeal(result, totalResult, T2ToT1, checkFailed);

            //方案可行性
            executableDeal(result, totalResult, executableOrder, ToT0, valid);

            //T2方案制定及时性
            T2TimelyDeal(result, totalResult, T2Order, timelinessOrder);

            //T0落实及时性
            T0TimelyDeal(result, totalResult, ToT0Order, T0UnTimeout);

            //T0落实准确性
            t0VeracityDeal(result, totalResult, executableOrder, valid, T0VeracityOrder, hadEvent, T0reply);

            //质检及时性判断
            CheckTimelyDeal(result, totalResult, T1CheckOrder, T2CheckOrder, T1CheckTimely, T2CheckTimely);

            //督办挂起解决工单
            DuBanDeal(result, totalResult, valid, DuBan);
        }

        private void addExecutable(ref int executableOrder, ref int ToT0, ref bool valid, ref bool DuBan, GroupStatModel item)
        {
            if (item.Description.Contains("地市是否执行派单方案:执行"))
            {
                executableOrder++;
            }
            if (item.Description.Contains("问题点已关闭，验证成功"))
            {
                DuBan = true;
            }
            string status = getStatus(item.Description);
            int groupIndex = getGroupIndex(item.GroupName, status);
            if (groupIndex == 7) //地市
            {
                ToT0++;
            }
            else if (groupIndex == 8) //督办
            {
                valid = false;
            }
        }

        private static void addCheckTimely(ref bool T1CheckOrder, ref bool T2CheckOrder, GroupStatModel item)
        {
            if (item.Description.Contains("[已接单]变为[已回单]") && item.Description.Contains("[用户组别:T1"))
            {
                T1CheckOrder = true;
            }
            else if (item.Description.Contains("[已接单]变为[已回单]") && item.Description.Contains("[用户组别:T2"))
            {
                T2CheckOrder = true;
            }
        }

        private static void addT0Veracity(ref int T0VeracityOrder, ref bool hadEvent, ref bool T0reply, GroupStatModel item)
        {
            if (item.Description.Contains("[已回单]变为[验证失败]"))
            {
                T0VeracityOrder++;
            }
            if (item.Description.Contains("问题点没有关闭，“预关闭”后发生异常事件"))
            {
                hadEvent = false;
            }
            if (item.Description.Contains("[已接单]变为[已回单]"))
            {
                T0reply = true;
            }
        }

        private static void judgeStatus(List<TimerModel> timers, ref bool T1ProUnTimeout, ref bool timelinessOrder, ref bool T0UnTimeout, ref bool T1CheckTimely, ref bool T2CheckTimely)
        {
            if (timers == null || timers.Count == 0)
            {
                return;
            }

            foreach (var item in timers)
            {
                //T1预处理及时性判断
                dealT2Timely(ref T1ProUnTimeout, ref timelinessOrder, ref T0UnTimeout, ref T1CheckTimely, ref T2CheckTimely, item);
            }
        }

        private static void dealT2Timely(ref bool T1ProUnTimeout, ref bool timelinessOrder, ref bool T0UnTimeout, ref bool T1CheckTimely, ref bool T2CheckTimely, TimerModel item)
        {
            if (item.StatusType.Contains("T1预处理") && item.Status.Contains("已超时"))
            {
                T1ProUnTimeout = false;
            }
            if (item.StatusType.Contains("T2质检") && item.Status.Contains("已超时"))
            {
                T2CheckTimely = false;
            }
            if (item.StatusType.Contains("T1质检") && item.Status.Contains("已超时"))
            {
                T1CheckTimely = false;
            }
            if (item.CurUser.Contains("T2") && item.Status.Contains("已超时"))
            {
                timelinessOrder = false;
            }
            if (item.CurUser == item.CityName && item.Status.Contains("已超时"))
            {
                T0UnTimeout = false;
            }
        }

        private void proDeal(OrderAnalyzerResult result,OrderAnalyzerResult totalResult,int proDealOrder, bool T1ProUnTimeout)
        {
            if (proDealOrder == 1)
            {
                result.Param["T1预处理工单数"]++;
                result.Param["T1准确派单数"]++;

                totalResult.Param["T1预处理工单数"]++;
                totalResult.Param["T1准确派单数"]++;
            }
            else if (proDealOrder > 1)
            {
                result.Param["T1预处理工单数"]++;
                totalResult.Param["T1预处理工单数"]++;
            }

            if (T1ProUnTimeout && proDealOrder > 0)
            {
                result.Param["T1未超时工单数"]++;
                totalResult.Param["T1未超时工单数"]++;
            }
        }

        private void t2VeracityDeal(OrderAnalyzerResult result, OrderAnalyzerResult totalResult, int T2ToT1, int checkFailed)
        {
            if (T2ToT1 >0)
            {
                result.Param["T2派单T0工单数"]++;

                totalResult.Param["T2派单T0工单数"]++;
            }
            if (checkFailed > 0)
            {
                result.Param["T2指标验证失败数"]++;

                totalResult.Param["T2指标验证失败数"]++;
            }
        }

        private void executableDeal(OrderAnalyzerResult result, OrderAnalyzerResult totalResult, int executableOrder, int ToT0, bool valid)
        {
            if (executableOrder >= 1 && valid)
            {
                result.Param["可执行工单数"]++;

                totalResult.Param["可执行工单数"]++;
            }
            if (ToT0 >= 1)
            {
                result.Param["ToT0工单"]++;

                totalResult.Param["ToT0工单"]++;
            }
        }

        private void T2TimelyDeal(OrderAnalyzerResult result, OrderAnalyzerResult totalResult, bool T2Order, bool timelinessOrder)
        {
            if (T2Order)
            {
                result.Param["T2分析工单数"]++;

                totalResult.Param["T2分析工单数"]++;
            }
            if (timelinessOrder && T2Order)
            {
                result.Param["T2未超时工单数"]++;

                totalResult.Param["T2未超时工单数"]++;
            }
        }

        private void T0TimelyDeal(OrderAnalyzerResult result, OrderAnalyzerResult totalResult, bool ToT0Order, bool T0UnTimeout)
        {
            if (ToT0Order)
            {
                result.Param["派发T0工单数"]++;

                totalResult.Param["派发T0工单数"]++;
            }
            if (T0UnTimeout && ToT0Order)
            {
                result.Param["T0未超时工单数"]++;

                totalResult.Param["T0未超时工单数"]++;
            }
        }

        private void t0VeracityDeal(OrderAnalyzerResult result, OrderAnalyzerResult totalResult, int executableOrder
            , bool valid,int T0VeracityOrder, bool hadEvent,bool T0Reply)
        {
            if (executableOrder < 1 && valid && T0VeracityOrder >=1 && hadEvent)
            {
                result.Param["T0验证失败工单数"]++;

                totalResult.Param["T0验证失败工单数"]++;
            }
            if (T0Reply)
            {
                result.Param["T0回复工单数"]++;

                totalResult.Param["T0回复工单数"]++;
            }

        }

        private void CheckTimelyDeal(OrderAnalyzerResult result, OrderAnalyzerResult totalResult, bool T1CheckOrder, bool T2CheckOrder
            ,bool T1UnTimeout, bool T2UnTimeout)
        {
            if (T1CheckOrder)
            {
                result.Param["T1质检总工单"]++;

                totalResult.Param["T1质检总工单"]++;
            }
            if (T2CheckOrder)
            {
                result.Param["T2质检总工单"]++;

                totalResult.Param["T2质检总工单"]++;
            }
            if (T1UnTimeout && T1CheckOrder)
            {
                result.Param["T1质检未超时工单数"]++;

                totalResult.Param["T1质检未超时工单数"]++;
            }
            if (T2UnTimeout && T2CheckOrder)
            {
                result.Param["T2质检未超时工单数"]++;

                totalResult.Param["T2质检未超时工单数"]++;
            }
        }

        private void DuBanDeal(OrderAnalyzerResult result, OrderAnalyzerResult totalResult, bool valid, bool DuBan)
        {
            if (!valid)
            {
                result.Param["工程督办工单数"]++;

                totalResult.Param["工程督办工单数"]++;
            }
            if (DuBan && !valid)
            {
                result.Param["工程督办解决工单数"]++;

                totalResult.Param["工程督办解决工单数"]++;
            }

        }

        private Dictionary<string,OrderAnalyzerResult> creatResults()
        {
            Dictionary<string, OrderAnalyzerResult> resultDic = new Dictionary<string, OrderAnalyzerResult>();

            resultDic.Add("武汉", new OrderAnalyzerResult("武汉"));
            resultDic.Add("黄石", new OrderAnalyzerResult("黄石"));
            resultDic.Add("襄阳", new OrderAnalyzerResult("襄阳"));
            resultDic.Add("十堰", new OrderAnalyzerResult("十堰"));
            resultDic.Add("荆州", new OrderAnalyzerResult("荆州"));
            resultDic.Add("宜昌", new OrderAnalyzerResult("宜昌"));
            resultDic.Add("荆门", new OrderAnalyzerResult("荆门"));
            resultDic.Add("鄂州", new OrderAnalyzerResult("鄂州"));
            resultDic.Add("孝感", new OrderAnalyzerResult("孝感"));
            resultDic.Add("黄冈", new OrderAnalyzerResult("黄冈"));
            resultDic.Add("咸宁", new OrderAnalyzerResult("咸宁"));
            resultDic.Add("随州", new OrderAnalyzerResult("随州"));
            resultDic.Add("恩施", new OrderAnalyzerResult("恩施"));
            resultDic.Add("江汉", new OrderAnalyzerResult("江汉"));
            resultDic.Add("潜江", new OrderAnalyzerResult("潜江"));
            resultDic.Add("天门", new OrderAnalyzerResult("天门"));
            resultDic.Add("总计", new OrderAnalyzerResult("总计"));
            return resultDic;
        }

        private string getStatus(string str)
        {
            string status = "";

            if (str.Contains("为[预处理]"))
            {
                return "预处理";
            }
            else if (str.Contains("为[已派单]"))
            {
                return "已派单";
            }
            else if (str.Contains("为[已接单]"))
            {
                return "已接单";
            }
            else if (str.Contains("为[已回单]"))
            {
                return "已回单";
            }
            else if (str.Contains("为[预关闭]"))
            {
                return "预关闭";
            }
            else if (str.Contains("为[工程督办]"))
            {
                return "工程督办";
            }
            return status;
        }

        private int getGroupIndex(string str, string status)
        {
            if (status.Contains("工程督办"))
            {
                return 8;
            }
            int index = -1;
            List<TaskName> groupList = new List<TaskName>();
            groupList.Add(new TaskName("T1", "测试数据管理", ++index));
            groupList.Add(new TaskName("T2_1", "片区优化组", ++index));
            groupList.Add(new TaskName("T2_2", "参数维护组", ++index));
            groupList.Add(new TaskName("T2_3", "室分维护组", ++index));
            groupList.Add(new TaskName("T2_4", "无线规划组", ++index));
            groupList.Add(new TaskName("T2_5", "干线优化组", ++index));
            groupList.Add(new TaskName("T3", "疑难问题处理组", ++index));

            index = -1;
            foreach (var group in groupList)
            {
                if (str.Contains(group.Name) || str.Contains(group.NameCH))
                {
                    index = group.Index;
                    return index;
                }
            }
            if (validDistrict(str) >= 0)
            {
                index = 7;
            }
            return index;
        }

        private int validDistrict(string str)
        {
            int index = -1;
            List<TaskName> districtList = new List<TaskName>();
            districtList.Add(new TaskName("ezhou", "鄂州", ++index));
            districtList.Add(new TaskName("enshi", "恩施", ++index));
            districtList.Add(new TaskName("huangshi", "黄石", ++index));
            districtList.Add(new TaskName("huanggang", "黄冈", ++index));
            districtList.Add(new TaskName("jingmen", "荆门", ++index));
            districtList.Add(new TaskName("jingzhou", "荆州", ++index));
            districtList.Add(new TaskName("jianghan", "江汉", ++index));
            districtList.Add(new TaskName("tianmen", "天门", ++index));
            districtList.Add(new TaskName("qianjiang", "潜江", ++index));
            districtList.Add(new TaskName("shiyan", "十堰", ++index));
            districtList.Add(new TaskName("suizhou", "随州", ++index));
            districtList.Add(new TaskName("wuhan", "武汉", ++index));
            districtList.Add(new TaskName("xiangyang", "襄阳", ++index));
            districtList.Add(new TaskName("xianning", "咸宁", ++index));
            districtList.Add(new TaskName("xiaogan", "孝感", ++index));
            districtList.Add(new TaskName("yichang", "宜昌", ++index));

            index = -1;
            foreach (var district in districtList)
            {
                if (str.Contains(district.Name) || str.Contains(district.NameCH))
                {
                    index = district.Index;
                }
            }
            return index;
        }

        class TaskName
        {
            public TaskName(string name, string nameCH, int index)
            {
                Index = index;
                Name = name;
                NameCH = nameCH;
            }
            public int Index { get; set; }
            public string Name { get; set; }
            public string NameCH { get; set; }
        }
    }
}
