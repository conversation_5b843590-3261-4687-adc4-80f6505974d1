﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.CQT;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.Stat
{
    public class QueryCqtAreaList: DIYSQLBase
    { 
        readonly int areaTypeId = 0;
        public QueryCqtAreaList(int ditrictID, int areaTypeId)
            : base(MainModel.GetInstance())
        {
            this.dbid = ditrictID;
            this.areaTypeId = areaTypeId;
        }
     
        public override string Name
        {
            get { return "查询CQT地点属性"; }
        }
        protected override string getSqlTextString()
        {
            string str= @"select a.iareatypeid,a.strareatypename,a.iareaid,a.strareaname,b.pointType from tb_cfg_static_arealist a,tb_cqt_point b";
            str += @" where a.iareatypeid={0} and a.strareaname COLLATE DATABASE_DEFAULT=b.pointName COLLATE DATABASE_DEFAULT";
            str = string.Format(str, areaTypeId);
            return str;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[5];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_Int;
            return rType;
        }
        public Dictionary<int, CQTPointTem> CQTPointDic { get; set; } = new Dictionary<int, CQTPointTem>();
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            CQTPointDic.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CQTPointTem cqtTem = new CQTPointTem();
                    cqtTem.Iareatypeid = package.Content.GetParamInt();
                    cqtTem.Strareatypename = package.Content.GetParamString();
                    cqtTem.Iareaid = package.Content.GetParamInt();
                    cqtTem.Strareaname = package.Content.GetParamString();
                    int pointType = package.Content.GetParamInt();
                    cqtTem.Strcomment = CQTCfgManager.GetInstance().GetCQTPointType(pointType).Name;
                    CQTPointDic[cqtTem.Iareaid] = cqtTem;
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }     
    }
}
