﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Stat;

namespace MasterCom.RAMS.NewBlackBlock
{
    public partial class BlackBlockEventFilterDlg : Form
    {
        public BlackBlockEventFilterDlg()
        {
            InitializeComponent();
        }

        internal void FreshSetSystem(int bySystem)
        {
            if(bySystem == 1)
            {
                cbxList.Items.Clear();
                cbxList.Items.Add(new IDNamePair(6, "MO Drop Call"));
                cbxList.Items.Add(new IDNamePair(7, "MT Drop Call"));
                cbxList.Items.Add(new IDNamePair(8, "MO Call Fail"));
                cbxList.Items.Add(new IDNamePair(9, "MT Call Fail"));
                cbxList.Items.Add(new IDNamePair(10, "MO Block Call"));
                cbxList.Items.Add(new IDNamePair(11, "MT Block Call"));
                cbxList.Items.Add(new IDNamePair(18, "Handover Failure"));
                cbxList.Items.Add(new IDNamePair(41, "Weak Coverage"));
                cbxList.Items.Add(new IDNamePair(43, "Weak Quality"));
            }
        }

        internal Dictionary<int, bool> GetDicSetting()
        {
            Dictionary<int, bool>  ret = new Dictionary<int,bool>();
            if(cbxAll.Checked)
            {
                return ret;
            }
            foreach(IDNamePair pair in cbxList.CheckedItems)
            {
                ret[pair.id] = true;
            }
            return ret;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void cbxAll_CheckedChanged(object sender, EventArgs e)
        {
            cbxList.Enabled = !cbxAll.Checked;
        }
    }
}