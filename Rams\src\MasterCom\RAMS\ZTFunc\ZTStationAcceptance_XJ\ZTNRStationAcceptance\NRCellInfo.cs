﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class NRCellInfo : CellInfoBase
    {
        public NRCellServiceInfo SAInfo { get; private set; }
        public NRCellServiceInfo NSAInfo { get; private set; }

        public NRCellInfo(ICell cell)
            : base(cell)
        {
        }

        public void Init(ICell cell, NRServiceName serviceType)
        {
            if (SAInfo == null && serviceType == NRServiceName.SA)
            {
                SAInfo = new NRCellServiceInfo(cell as NRCell);
            }
            else if (NSAInfo == null && serviceType == NRServiceName.NSA)
            {
                NSAInfo = new NRCellServiceInfo(cell as NRCell);
            }
        }
    }

    public class NRCellServiceInfo
    {
        public NRCellServiceInfo(NRCell cell)
        {
            Cell = cell;
        }

        public NRCell Cell { get; set; }

        public SuccessRateKpiInfo AccessInfo { get; set; } = new SuccessRateKpiInfo();
        public SuccessRateKpiInfo EPSFBInfo { get; set; } = new SuccessRateKpiInfo();
        public DataKpiInfo EPSFBDelay { get; set; } = new DataKpiInfo();
        public SuccessRateKpiInfo VONRInfo { get; set; } = new SuccessRateKpiInfo();

        public FtpPointInfo GoodSampleDL { get; set; } = new FtpPointInfo();
        public FtpPointInfo MiddleSampleDL { get; set; } = new FtpPointInfo();
        public FtpPointInfo BadSampleDL { get; set; } = new FtpPointInfo();

        public FtpPointInfo GoodSampleUL { get; set; } = new FtpPointInfo();
        public FtpPointInfo MiddleSampleUL { get; set; } = new FtpPointInfo();
        public FtpPointInfo BadSampleUL { get; set; } = new FtpPointInfo();

        public DataKpiInfo BigPackageDelay { get; set; } = new DataKpiInfo();
        public DataKpiInfo SmallPackageDelay { get; set; } = new DataKpiInfo();

        public PicKpiInfo NRRsrpPic { get; set; } = new PicKpiInfo();
        public PicKpiInfo LteRsrpPic { get; set; } = new PicKpiInfo();
        public PicKpiInfo NRSinrPic { get; set; } = new PicKpiInfo();
        public PicKpiInfo LteSinrPic { get; set; } = new PicKpiInfo();
        public PicKpiInfo NRDLPic { get; set; } = new PicKpiInfo();
        public PicKpiInfo NRULPic { get; set; } = new PicKpiInfo();
    }

    public class FtpPointInfo
    {
        public bool IsValid { get; set; } = false;
        public DataKpiInfo Rsrp { get; set; } = new DataKpiInfo();
        public DataKpiInfo Sinr { get; set; } = new DataKpiInfo();
        public DataKpiInfo Throughput { get; set; } = new DataKpiInfo();

        public void Add(TestPoint tp, bool isDL)
        {
            float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
            Rsrp.Add(rsrp);
            float? sinr = NRTpHelper.NrTpManager.GetSCellSinr(tp);
            Sinr.Add(sinr);
            if (isDL)
            {
                double? throughput = NRTpHelper.NrTpManager.GetMacDLMb(tp);
                Throughput.Add(throughput, true);
            }
            else
            {
                double? throughput = NRTpHelper.NrTpManager.GetMacULMb(tp);
                Throughput.Add(throughput, true);
            }
        }

        public void Judge()
        {
            IsValid = Rsrp.IsValid && Sinr.IsValid && Throughput.IsValid;
        }

        public void Calculate()
        {
            Rsrp.Calculate();
            Sinr.Calculate();
            Throughput.Calculate();
        }
    }

    public class NRCellParameters : CellParameters
    {
        #region 小区天线参数验证
        public string CellName { get; set; }
        /// <summary>
        /// 经度
        /// </summary>
        public ParamInfo<double?> Longitude { get; set; } = new ParamInfo<double?>();
        /// <summary>
        /// 纬度
        /// </summary>
        public ParamInfo<double?> Latitude { get; set; } = new ParamInfo<double?>();
        /// <summary>
        /// 天线挂高
        /// </summary>
        public ParamInfo<double?> Altitude { get; set; } = new ParamInfo<double?>();
        /// <summary>
        /// 方位角
        /// </summary>
        public ParamInfo<double?> Direction { get; set; } = new ParamInfo<double?>();
        /// <summary>
        /// 电气下倾角
        /// </summary>
        public ParamInfo<double?> Downtilt { get; set; } = new ParamInfo<double?>();
        /// <summary>
        /// 机械下倾角
        /// </summary>
        public ParamInfo<double?> MechanicalTilt { get; set; } = new ParamInfo<double?>();
        /// <summary>
        /// 总下倾角
        /// </summary>
        public ParamInfo<double?> Downward { get; set; } = new ParamInfo<double?>();
        /// <summary>
        /// 收发通道数
        /// </summary>
        public ParamInfo<int?> Channels { get; set; } = new ParamInfo<int?>();
        #endregion

        #region 小区工程参数验证
        public ParamInfo<int?> CellID { get; set; } = new ParamInfo<int?>();
        public ParamInfo<int?> PCI { get; set; } = new ParamInfo<int?>();
        /// <summary>
        /// 频段
        /// </summary>
        public ParamInfo<string> FreqBand { get; set; } = new ParamInfo<string>();
        /// <summary>
        /// 频点
        /// </summary>
        public ParamInfo<int?> Freq { get; set; } = new ParamInfo<int?>();
        /// <summary>
        /// SSB频点
        /// </summary>
        public ParamInfo<int?> SSBFreq { get; set; } = new ParamInfo<int?>();
        /// <summary>
        /// 小区带宽（兆）
        /// </summary>
        public ParamInfo<string> Bandwidth { get; set; } = new ParamInfo<string>();
        /// <summary>
        /// 根序列(PRACH)
        /// </summary>
        public ParamInfo<string> PRACH { get; set; } = new ParamInfo<string>();
        /// <summary>
        /// 子帧配比
        /// </summary>
        public ParamInfo<string> SubFrameRatio { get; set; } = new ParamInfo<string>();
        /// <summary>
        /// AAU数量
        /// </summary>
        public ParamInfo<string> AAUCount { get; set; } = new ParamInfo<string>();
        /// <summary>
        /// 核心网接入模式
        /// </summary>
        public ParamInfo<string> CoreMode { get; set; } = new ParamInfo<string>();
        #endregion

        public void Caluculate()
        {
            Longitude.JudgeValidLongitude(50);
            Latitude.JudgeValidLatitude(50);
            Altitude.JudgeValidPercent(10);
            Direction.JudgeValid(10);
            Downtilt.JudgeValid(3);
            MechanicalTilt.JudgeValid(3);
            Downward.JudgeValid(3);
            Channels.JudgeValid();

            CellID.JudgeValid();
            PCI.JudgeValid();
            FreqBand.JudgeValid();
            Freq.JudgeValid();
            SSBFreq.JudgeValid();
            Bandwidth.JudgeValid();
            PRACH.JudgeValid();
            SubFrameRatio.JudgeValid();
            AAUCount.JudgeValid();
            CoreMode.JudgeValid();
        }
    }
}
