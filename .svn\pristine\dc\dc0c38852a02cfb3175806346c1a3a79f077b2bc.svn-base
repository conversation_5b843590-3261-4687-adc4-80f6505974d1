using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.ES.ColorManager
{
    public class EsColorConfig:MasterCom.Util.AConfig_Ext
    {
        [MasterCom.Util.NonSaveConfig]
        public static EsColorConfig OneInstance { get; set; } = new EsColorConfig();

        private EsColorConfig()
        {
            Load();
        }

        public Dictionary<string, EventCategory> EventColorItems { get; set; } = new Dictionary<string,EventCategory>();

        public List<string> Pretypes { get; set; } = new List<string>();

        public string LastCategoryName { get; set; } = "";

        public EventCategory LastCategory
        {
            get
            {
                if (!string.IsNullOrEmpty(LastCategoryName)&&EventColorItems.ContainsKey(LastCategoryName))
                    return EventColorItems[LastCategoryName];
                return null;
           }
        }

        public List<EventColorItem> SelectedEventColorItems
        {
            get
            {
                List<EventColorItem> ecis = new List<EventColorItem>();
                if (LastCategory != null)
                    foreach (EventColorItem eci in LastCategory.ColorItems)
                    {
                        if (eci.IsChecked)
                            ecis.Add(eci);
                    }
                return ecis;
            }
        }
    }
}
