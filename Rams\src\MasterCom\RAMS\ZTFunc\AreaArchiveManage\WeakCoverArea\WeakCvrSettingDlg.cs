﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.WeakCoverArea
{
    public partial class WeakCvrSettingDlg : BaseDialog
    {
        public WeakCvrSettingDlg()
            : base()
        {
            InitializeComponent();
            this.cmbArea.SelectedIndex = 0;
            this.cmbCell.SelectedIndex = 2;
            this.cmbRxLev.SelectedIndex = 0;
            this.cmbWeakAreaNum.SelectedIndex = 3;
        }

        public WeakCvrAreaCondition Condition
        {
            get
            {
                WeakCvrAreaCondition cond = new WeakCvrAreaCondition();
                cond.AreaDisOp = (RelationalOperator)cmbArea.SelectedIndex;
                cond.AreaDistance = (double)numAreaDis.Value;
                cond.CellDisOp = (RelationalOperator)cmbCell.SelectedIndex;
                cond.CellDistance = (double)numCellDis.Value;
                cond.RxLev = (double)numRxLev.Value;
                cond.WeakAreaNum = (int)numWeakAreaNum.Value;
                cond.WeakAreaNumOp = (RelationalOperator)cmbWeakAreaNum.SelectedIndex;
                cond.WeakRxLevOp = (RelationalOperator)cmbRxLev.SelectedIndex;
                return cond;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                cmbArea.SelectedIndex = (int)value.AreaDisOp;
                numAreaDis.Value = (decimal)value.AreaDistance;
                cmbCell.SelectedIndex = (int)value.CellDisOp;
                numCellDis.Value = (decimal)value.CellDistance;
                numRxLev.Value = (decimal)value.RxLev;
                numWeakAreaNum.Value = (decimal)value.WeakAreaNum;
                cmbWeakAreaNum.SelectedIndex = (int)value.WeakAreaNumOp;
                cmbRxLev.SelectedIndex = (int)value.WeakRxLevOp;
            }
        }

        private void WeakCvrSettingDlg_Load(object sender, EventArgs e)
        {
            //
        }
    }
}
