﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.MTGis;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class DIYNoCoverRoadByRegion_GScan : DIYAnalyseByFileBackgroundBase
    {
        private GSMFreqBandType bandType = GSMFreqBandType.All;
        public int rxLevThreshold { get; set; } = -95;
        public int distanceLast { get; set; } = 100;

        readonly List<NoCoverRoad> noCoverRoadList = new List<NoCoverRoad>();
        private static DIYNoCoverRoadByRegion_GScan intance = null;
        protected static readonly object lockObj = new object();
        public static DIYNoCoverRoadByRegion_GScan GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new DIYNoCoverRoadByRegion_GScan();
                    }
                }
            }
            return intance;
        }

        protected DIYNoCoverRoadByRegion_GScan()
            : base(MainModel.GetInstance())
        {
            FilterSampleByRegion = false;
            IncludeEvent = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.GSM_SCAN);
            carrierID = CarrierType.ChinaMobile;
        }

        public override string Name
        {
            get { return "弱覆盖路段分析_GSM扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 15000, 15001, this.Name);//////
        }

        protected override void getReadyBeforeQuery()
        {
            Columns = new List<string>();
            Columns.Add("GSCAN_RxLev");
            Columns.Add("LTESCAN_TopN_CELL_Specific_RSRP");
            Columns.Add("WS_CPICHTotalRSCP");
            Columns.Add("TDS_PCCPCH_RSCP");
        }

        protected override void fireShowForm()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(NoCoverRoadForm).FullName);
            NoCoverRoadForm noCoverRoadForm = obj == null ? null : obj as NoCoverRoadForm;
            if (noCoverRoadForm == null || noCoverRoadForm.IsDisposed)
            {
                noCoverRoadForm = new NoCoverRoadForm(MainModel);
            }
            noCoverRoadForm.FillData(noCoverRoadList, distanceLast);
            if (!noCoverRoadForm.Visible)
            {
                noCoverRoadForm.Show(MainModel.MainForm);
            }
        }

        protected override void fireSetDefaultMapSerialTheme()
        {
            MainModel.FireSetDefaultMapSerialTheme("GSM_SCAN_RxLev");
        }

        NoCoverRoadSetDlg conditionDlg = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                bandType = GSMFreqBandType.All;
                return true;
            }
            if (conditionDlg == null)
            {
                conditionDlg = new NoCoverRoadSetDlg();
            }
            if (conditionDlg.ShowDialog() == DialogResult.OK)
            {
                int band = 0;
                int rxLev;
                int distance;
                conditionDlg.GetFilterCondition(out band, out rxLev, out distance);
                bandType = (GSMFreqBandType)band;
                rxLevThreshold = rxLev;
                distanceLast = distance;
                return true;
            }
            return false;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            noCoverRoadList.Clear();
        }

        protected override void analyseFiles()
        {
            try
            {
                List<FileInfo> files = new List<FileInfo>();

                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    if (ServiceTypes.Count > 0 && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                    {
                        continue;
                    }
                    files.Add(fileInfo);
                }
                clearDataBeforeAnalyseFiles();
                int iloop = 0;
                foreach (FileInfo fileInfo in files)
                {
                    Condition.FileInfos.Clear();
                    Condition.FileInfos.Add(fileInfo);

                    MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;
                    iloop++;
                    WaitBox.Text = "正在分析文件(" + (iloop) + "/" + files.Count + ")...";
                    replay();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
                MainModel.ClearDTData();
                getResultsAfterQuery();
            }
            finally
            {
                System.Threading.Thread.Sleep(20);
                WaitBox.Close();
            }
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    List<TestPoint> testPointList = fileDataManager.TestPoints;
                    NoCoverRoad curNoCoverRoad = null;
                    for (int i = 0; i < testPointList.Count; i++)
                    {
                        TestPoint testPoint = testPointList[i];
                        if (!isValidTestPoint(testPoint))
                        {
                            saveResult(curNoCoverRoad);
                            curNoCoverRoad = null;
                        }
                        else
                        {
                            if (curNoCoverRoad == null)
                            {
                                curNoCoverRoad = init(testPoint);
                            }
                            else
                            {
                                curNoCoverRoad.AddTestPoint(testPoint, bandType);
                            }
                        }
                    }
                    saveResult(curNoCoverRoad); //最后一段
                }
            }
            catch
            {
                //continue
            }
        }

        protected virtual NoCoverRoad init(TestPoint testPoint)
        {
            return new NoCoverRoad(testPoint, bandType);
        }

        private void saveResult(NoCoverRoad curNoCoverRoad)
        {
            if (curNoCoverRoad != null && curNoCoverRoad.Distance >= distanceLast)
            {
                noCoverRoadList.Add(curNoCoverRoad);
            }
        }

        protected override void getResultsAfterQuery()
        {
            foreach (NoCoverRoad curNoCoverRoad in noCoverRoadList)
            {
                curNoCoverRoad.GetResult();
            }
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            try
            {
                if (validTestPoint(testPoint))
                {
                    return base.isValidTestPoint(testPoint);
                }
                return false;
            }
            catch(Exception)
            {
                return false;
            }
        }

        protected virtual bool validTestPoint(TestPoint tp)
        {
            List<int> indexLst = tp.GetGSMScanIdxSpecify(bandType);
            if (tp is ScanTestPoint_G && indexLst.Count > 0)
            {
                float? rxLev = (float?)tp["GSCAN_RxLev", indexLst[0]];
                if (rxLev <= rxLevThreshold)
                {
                    return true;
                }
            }
            return false;
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.GSM扫频; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.覆盖; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["RxLevThreshold"] = rxLevThreshold;
                param["DistanceLast"] = distanceLast;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("RxLevThreshold"))
                {
                    rxLevThreshold = int.Parse(param["RxLevThreshold"].ToString());
                }
                if (param.ContainsKey("DistanceLast"))
                {
                    distanceLast = int.Parse(param["DistanceLast"].ToString());
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new NoCoverRoadProperties_GSCAN(this);
            }
        }

        protected override void saveBackgroundData()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (NoCoverRoad block in noCoverRoadList)
            {
                block.GetResult();
                BackgroundResult result = block.ConvertToBackgroundResult();
                result.SubFuncID = GetSubFuncID();
                bgResultList.Add(result);
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Road(GetSubFuncID(), curAnaFileInfo, bgResultList);
            noCoverRoadList.Clear();
        }
        #endregion

    }

    /// <summary>
    /// 无覆盖路段
    /// </summary>
    public class NoCoverRoad
    {
        protected NoCoverRoad()
        {
        }

        public NoCoverRoad(TestPoint tp, GSMFreqBandType bandtype)
        {
            AddTestPoint(tp, bandtype);
        }

        public string FileName
        {
            get { return testPointList[0].FileName; }
        }

        protected double distance = 0;
        public double Distance
        {
            get { return Math.Round(distance, 4); }
            set { distance = value; }
        }

        public int Second
        {
            get { return LastTestPoint.Time - testPointList[0].Time; }
        }

        protected double rxLevMin;
        public double RxLevMin
        {
            get { return Math.Round(rxLevMin, 4); }
        }

        protected double rxLevMax;
        public double RxLevMax
        {
            get { return Math.Round(rxLevMax, 4); }
        }

        protected double rxLevSum;
        public double RxLevMean
        {
            get { return Math.Round(rxLevSum / testPointList.Count, 4); }
        }
        
        public List<TestPoint> testPointList { get; set; } = new List<TestPoint>();

        public TestPoint LastTestPoint
        {
            get { return testPointList[testPointList.Count - 1]; }
        }

        public int TestPointCount
        {
            get { return testPointList.Count; }
        }
        
        public string RoadDesc { get; set; } = "";

        public double LongitudeMid
        {
            get
            {
                int index = testPointList.Count / 2;
                return testPointList[index].Longitude;
            }
        }

        public double LatitudeMid
        {
            get
            {
                int index = testPointList.Count / 2;
                return testPointList[index].Latitude;
            }
        }

        public virtual void AddTestPoint(TestPoint tp, GSMFreqBandType bandtype)
        {
            float rxLev = 0;
            if (tp is ScanTestPoint_G)
            {
                List<int> indexLst = tp.GetGSMScanIdxSpecify(bandtype);
                rxLev = (float)(float?)tp["GSCAN_RxLev", indexLst[0]];
            }
            else if (tp is ScanTestPoint_TD)
            {
                rxLev = (float)(float?)tp["TDS_PCCPCH_RSCP", 0];
            }
            else if (tp is ScanTestPoint_W)
            {
                rxLev = (float)(float?)tp["WS_CPICHTotalRSCP", 0];
            }
            else if (tp is ScanTestPoint_LTE || tp is ScanTestPoint_NBIOT)
            {
                rxLev = (float)(float?)tp["LTESCAN_TopN_CELL_Specific_RSRP", 0];
            }
            else
            {
                rxLev = (float)(float?)tp["GSCAN_RxLev", 0];    //针对合并后的情况
            }

            addValidData(tp, rxLev);
        }

        protected void addValidData(TestPoint tp, float rxLev)
        {
            if (testPointList.Count == 0)
            {
                rxLevMin = rxLev;
                rxLevMax = rxLev;
                rxLevSum = rxLev;
            }
            else
            {
                if (rxLevMin > rxLev)
                {
                    rxLevMin = rxLev;
                }
                if (rxLevMax < rxLev)
                {
                    rxLevMax = rxLev;
                }
                rxLevSum += rxLev;
                TestPoint tpPrior = testPointList[testPointList.Count - 1];
                distance += MathFuncs.GetDistance(tpPrior.Longitude, tpPrior.Latitude, tp.Longitude, tp.Latitude);
            }
            testPointList.Add(tp);
        }

        public void GetResult()
        {
            RoadDesc = GISManager.GetInstance().GetRoadPlaceDesc(LongitudeMid, LatitudeMid);
        }

        public BackgroundResult ConvertToBackgroundResult()
        {
            BackgroundResult bgResult = new BackgroundResult();
            TestPoint tpFirst = testPointList[0];
            TestPoint tpLast = testPointList[testPointList.Count - 1];
            bgResult.FileID = tpFirst.FileID;
            bgResult.FileName = FileName;
            bgResult.DistanceLast = Distance;
            bgResult.ISTime = tpFirst.Time;
            bgResult.IETime = tpLast.Time;
            bgResult.SampleCount = TestPointCount;
            bgResult.RxLevMean = (float)RxLevMean;
            bgResult.RxLevMin = (float)rxLevMin;
            bgResult.RxLevMax = (float)rxLevMax;
            bgResult.LongitudeStart = tpFirst.Longitude;
            bgResult.LatitudeStart = tpFirst.Latitude;
            bgResult.LongitudeMid = LongitudeMid;
            bgResult.LatitudeMid = LatitudeMid;
            bgResult.LongitudeEnd = tpLast.Longitude;
            bgResult.LatitudeEnd = tpLast.Latitude;
            return bgResult;
        }
    }
}
