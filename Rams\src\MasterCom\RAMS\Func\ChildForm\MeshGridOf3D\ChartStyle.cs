using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Text;
using System.Runtime.Serialization;

namespace MasterCom.RAMS.Func.MeshGridOf3D
{
    /// <summary>
    /// Class that handles the properties of the charting area for 3D(where the curves are
    /// actually drawn).
    /// </summary>
    /// 
    [Serializable]
    public class ChartStyle : ICloneable, ISerializable
    {
        #region Variables
        private MeshGridOf3DForm form1;
        #endregion

        #region Constructors

        public ChartStyle(MeshGridOf3DForm fm1)
        {
            form1 = fm1;
            GridStyle = new LineStyle();
            AxisStyle = new LineStyle();
        }
        /// <summary>
        /// Copy constructor
        /// </summary>
        /// 
        public ChartStyle(ChartStyle rhs)
        {
            XMax = rhs.XMax;
            XMin = rhs.XMin;
            YMax = rhs.YMax;
            YMin = rhs.YMin;
            ZMax = rhs.ZMax;
            ZMin = rhs.ZMin;
            XTick = rhs.XTick;
            YTick = rhs.YTick;
            ZTick = rhs.ZTick;
            TickFont = rhs.TickFont;
            TickColor = rhs.TickColor;
            Title = rhs.Title;
            TitleFont = rhs.TitleFont;
            TitleColor = rhs.TitleColor;
            XLabel = rhs.XLabel;
            YLabel = rhs.YLabel;
            ZLabel = rhs.ZLabel;
            LabelFont = rhs.LabelFont;
            LabelColor = rhs.LabelColor;
            Elevation = rhs.Elevation;
            Azimuth = rhs.Azimuth;
            IsXGrid = rhs.IsXGrid;
            IsYGrid = rhs.IsYGrid;
            IsZGrid = rhs.IsZGrid;
            GridStyle = rhs.GridStyle;
            AxisStyle = rhs.AxisStyle;
            IsColorBar = rhs.IsColorBar;
        }

        /// <summary>
        /// Implement the <see cref="ICloneable" /> interface in a typesafe manner by just
        /// calling the typed version of <see cref="Clone" />
        /// </summary>
        /// <returns>A deep copy of this object</returns>
        object ICloneable.Clone()
        {
            return new ChartStyle(this);
        }
        #endregion

        #region Properties
        public float XMax { get; set; } = 5f;
        public float XMin { get; set; } = -5f;
        public float YMax { get; set; } = 3f;
        public float YMin { get; set; } = -3f;
        public float ZMax { get; set; } = 6f;
        public float ZMin { get; set; } = -6f;
        public float XTick { get; set; } = 1f;
        public float YTick { get; set; } = 1f;
        public float ZTick { get; set; } = 3f;
        public Font TickFont { get; set; } = new Font("Arial Narrow", 8, FontStyle.Regular);
        public Color TickColor { get; set; } = Color.Black;
        public string Title { get; set; } = "My 3D Chart";
        public Font TitleFont { get; set; } = new Font("Arial Narrow", 14, FontStyle.Regular);
        public Color TitleColor { get; set; } = Color.Black;
        public string XLabel { get; set; } = "X Axis";
        public string YLabel { get; set; } = "Y Axis";
        public string ZLabel { get; set; } = "Z Axis";
        public Font LabelFont { get; set; } = new Font("Arial Narrow", 10, FontStyle.Regular);
        public Color LabelColor { get; set; } = Color.Black;
        public float Elevation { get; set; } = 30;
        public float Azimuth { get; set; } = -37.5f;
        public bool IsXGrid { get; set; } = true;
        public bool IsYGrid { get; set; } = true;
        public bool IsZGrid { get; set; } = true;
        public LineStyle GridStyle { get; set; }
        public LineStyle AxisStyle { get; set; }
        public bool IsColorBar { get; set; } = false;
        #endregion

        #region Serialization

        /// <summary>
        /// Constructor for deserializing objects
        /// </summary>
        /// <param name="info">A <see cref="SerializationInfo"/> instance that defines the serialized data
        /// </param>
        /// <param name="context">A <see cref="StreamingContext"/> instance that contains the serialized data
        /// </param>
        /// 
        protected ChartStyle(SerializationInfo info, StreamingContext context)
        {
            XMax = info.GetSingle("xMax");
            XMin = info.GetSingle("xMin");
            YMax = info.GetSingle("yMax");
            YMin = info.GetSingle("yMin");
            ZMax = info.GetSingle("zMax");
            ZMin = info.GetSingle("zMin");
            XTick = info.GetSingle("Tick");
            YTick = info.GetSingle("yTick");
            ZTick = info.GetSingle("zTick");
            TickFont = (Font)info.GetValue("tickFont", typeof(Font));
            TickColor = (Color)info.GetValue("tickColor", typeof(Color));
            Title = info.GetString("title");
            TitleFont = (Font)info.GetValue("titleFont", typeof(Font));
            TitleColor = (Color)info.GetValue("titleColor", typeof(Color));
            XLabel = info.GetString("xLabel");
            YLabel = info.GetString("yLabel");
            ZLabel = info.GetString("zLabel");
            LabelFont = (Font)info.GetValue("labelFont", typeof(Font));
            LabelColor = (Color)info.GetValue("labelColor", typeof(Color));
            Elevation = info.GetSingle("elevation");
            Azimuth = info.GetSingle("azimuth");
            IsXGrid = info.GetBoolean("isXGrid");
            IsYGrid = info.GetBoolean("isYGrid");
            IsZGrid = info.GetBoolean("isZGrid");
            GridStyle = (LineStyle)info.GetValue("gridStyle", typeof(LineStyle));
            AxisStyle = (LineStyle)info.GetValue("axisStyle", typeof(LineStyle));
            IsColorBar = info.GetBoolean("isColorBar");
        }
        /// <summary>
        /// Populates a <see cref="SerializationInfo"/> instance with the data needed to serialize the target object
        /// </summary>
        /// <param name="info">A <see cref="SerializationInfo"/> instance that defines the serialized data</param>
        /// <param name="context">A <see cref="StreamingContext"/> instance that contains the serialized data</param>
        public virtual void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            info.AddValue("xMax", XMax);
            info.AddValue("xMin", XMin);
            info.AddValue("yMax ", YMax);
            info.AddValue("yMin", YMin);
            info.AddValue("zMax", ZMax);
            info.AddValue("zMin", ZMin);
            info.AddValue("xTick", XTick);
            info.AddValue("yTick", YTick);
            info.AddValue("zTick", ZTick);
            info.AddValue("tickFont", TickFont);
            info.AddValue("tickColor", TickColor);
            info.AddValue("title", Title);
            info.AddValue("titleFont", TitleFont);
            info.AddValue("titleColor", TitleColor);
            info.AddValue("xLabel", XLabel);
            info.AddValue("yLabel", YLabel);
            info.AddValue("zLabel", ZLabel);
            info.AddValue("labelFont", LabelFont);
            info.AddValue("labelColor", LabelColor);
            info.AddValue("elevation", Elevation);
            info.AddValue("azimuth", Azimuth);
            info.AddValue("isXGrid", IsXGrid);
            info.AddValue("isYGrid", IsYGrid);
            info.AddValue("isZGrid", IsZGrid);
            info.AddValue("gridStyle", GridStyle);
            info.AddValue("axisStyle", AxisStyle);
            info.AddValue("isColorBar", IsColorBar);
        }
        #endregion

        #region Methods

        private Point3[] CoordinatesOfChartBox()
        {
            // Create coordinate of the axes:
            Point3[] pta = new Point3[8];
            pta[0] = new Point3(XMax, YMin, ZMin, 1);
            pta[1] = new Point3(XMin, YMin, ZMin, 1);
            pta[2] = new Point3(XMin, YMax, ZMin, 1);
            pta[3] = new Point3(XMin, YMax, ZMax, 1);
            pta[4] = new Point3(XMin, YMin, ZMax, 1);
            pta[5] = new Point3(XMax, YMin, ZMax, 1);
            pta[6] = new Point3(XMax, YMax, ZMax, 1);
            pta[7] = new Point3(XMax, YMax, ZMin, 1);

            Point3[] pts = new Point3[4];
            int[] npts = new int[4] { 0, 1, 2, 3 };
            if (Elevation >= 0)
            {
                npts = setNpts(npts);
            }
            else if (Elevation < 0)
            {
                npts = setNegativeNpts(npts);
            }

            for (int i = 0; i < 4; i++)
            {
                pts[i] = pta[npts[i]];
            }
            return pts;
        }

        private int[] setNpts(int[] npts)
        {
            if (Azimuth >= -180 && Azimuth < -90)
            {
                npts = new int[4] { 1, 2, 7, 6 };
            }
            else if (Azimuth >= -90 && Azimuth < 0)
            {
                npts = new int[4] { 0, 1, 2, 3 };
            }
            else if (Azimuth >= 0 && Azimuth < 90)
            {
                npts = new int[4] { 7, 0, 1, 4 };
            }
            else if (Azimuth >= 90 && Azimuth <= 180)
            {
                npts = new int[4] { 2, 7, 0, 5 };
            }

            return npts;
        }

        private int[] setNegativeNpts(int[] npts)
        {
            if (Azimuth >= -180 && Azimuth < -90)
            {
                npts = new int[4] { 1, 0, 7, 6 };
            }
            else if (Azimuth >= -90 && Azimuth < 0)
            {
                npts = new int[4] { 0, 7, 2, 3 };
            }
            else if (Azimuth >= 0 && Azimuth < 90)
            {
                npts = new int[4] { 7, 2, 1, 4 };
            }
            else if (Azimuth >= 90 && Azimuth <= 180)
            {
                npts = new int[4] { 2, 1, 0, 5 };
            }

            return npts;
        }

        public void AddChartStyle(Graphics g)
        {
            AddTicks(g);
            AddGrids(g);
            AddAxes(g);
            AddLabels(g);
        }

        private void AddAxes(Graphics g)
        {
            Matrix3 m = Matrix3.AzimuthElevation(Elevation, Azimuth);
            Point3[] pts = CoordinatesOfChartBox();
            Pen aPen = new Pen(AxisStyle.LineColor, AxisStyle.Thickness);
            aPen.DashStyle = AxisStyle.Pattern;
            for (int i = 0; i < pts.Length; i++)
            {
                pts[i].Transform(m, form1, this);
            }
            g.DrawLine(aPen, pts[0].X, pts[0].Y, pts[1].X, pts[1].Y);
            g.DrawLine(aPen, pts[1].X, pts[1].Y, pts[2].X, pts[2].Y);
            g.DrawLine(aPen, pts[2].X, pts[2].Y, pts[3].X, pts[3].Y);
            aPen.Dispose();
        }
        
        #region Ticks
        private void AddTicks(Graphics g)
        {
            Matrix3 m = Matrix3.AzimuthElevation(Elevation, Azimuth);
           
            Pen aPen = new Pen(AxisStyle.LineColor, AxisStyle.Thickness);
            aPen.DashStyle = AxisStyle.Pattern;

            // Add x ticks:
            addXTicks(g, m, aPen);

            // Add y ticks:
            addYTicks(g, m, aPen);

            addZTicks(g, m, aPen);
            aPen.Dispose();
        }

        private void addXTicks(Graphics g, Matrix3 m, Pen aPen)
        {
            Point3[] pta = new Point3[2];
            Point3[] pts = CoordinatesOfChartBox();
            float offset = (YMax - YMin) / 30.0f;
            float ticklength = offset;
            for (float x = XMin; x <= XMax; x = x + XTick)
            {
                if (Elevation >= 0)
                {
                    if (Azimuth >= -90 && Azimuth < 90)
                        ticklength = -offset;
                }
                else if (Elevation < 0 && ((Azimuth >= -180 && Azimuth < -90) || Azimuth >= 90 && Azimuth <= 180))
                {
                    ticklength = -(YMax - YMin) / 30;
                }
                pta[0] = new Point3(x, pts[1].Y + ticklength, pts[1].Z, pts[1].W);
                pta[1] = new Point3(x, pts[1].Y, pts[1].Z, pts[1].W);
                for (int i = 0; i < pta.Length; i++)
                {
                    pta[i].Transform(m, form1, this);
                }
                g.DrawLine(aPen, pta[0].X, pta[0].Y, pta[1].X, pta[1].Y);
            }
        }

        private void addYTicks(Graphics g, Matrix3 m, Pen aPen)
        {
            Point3[] pta = new Point3[2];
            Point3[] pts;
            float offset = (XMax - XMin) / 30.0f;
            float ticklength = offset;
            for (float y = YMin; y <= YMax; y = y + YTick)
            {
                pts = CoordinatesOfChartBox();
                if (Elevation >= 0)
                {
                    if (Azimuth >= -180 && Azimuth < 0)
                        ticklength = -offset;
                }
                else if (Elevation < 0 && Azimuth >= 0 && Azimuth < 180)
                {
                    ticklength = -offset;
                }
                pta[0] = new Point3(pts[1].X + ticklength, y, pts[1].Z, pts[1].W);
                pta[1] = new Point3(pts[1].X, y, pts[1].Z, pts[1].W);
                for (int i = 0; i < pta.Length; i++)
                {
                    pta[i].Transform(m, form1, this);
                }
                g.DrawLine(aPen, pta[0].X, pta[0].Y, pta[1].X, pta[1].Y);
            }
        }

        private void addZTicks(Graphics g, Matrix3 m, Pen aPen)
        {
            Point3[] pta = new Point3[2];
            Point3[] pts = CoordinatesOfChartBox();
            float xoffset = (XMax - XMin) / 45.0f;
            float yoffset = (YMax - YMin) / 20.0f;
            float xticklength = xoffset;
            float yticklength = yoffset;
            if (ZMin != ZMax)
            {
                for (float z = ZMin; z <= ZMax; z = z + ZTick)
                {
                    if (Elevation >= 0)
                    {
                        setTickLength(xoffset, yoffset, ref xticklength, ref yticklength);
                    }
                    else if (Elevation < 0)
                    {
                        setNegativeTickLength(xoffset, yoffset, ref xticklength, ref yticklength);
                    }
                    pta[0] = new Point3(pts[2].X, pts[2].Y, z, pts[2].W);
                    pta[1] = new Point3(pts[2].X + yticklength,
                        pts[2].Y + xticklength, z, pts[2].W);
                    for (int i = 0; i < pta.Length; i++)
                    {
                        pta[i].Transform(m, form1, this);
                    }
                    pta[1].X = pta[0].X - 3.996049f;             //////////////////////
                    pta[1].Y = pta[0].Y - 1.5813f;               //////////////////////
                    g.DrawLine(aPen, pta[0].X, pta[0].Y, pta[1].X, pta[1].Y);
                }
            }
        }

        private void setTickLength(float xoffset, float yoffset, ref float xticklength, ref float yticklength)
        {
            if (Azimuth >= -180 && Azimuth < -90)
            {
                xticklength = 0;
                yticklength = yoffset;
            }
            else if (Azimuth >= -90 && Azimuth < 0)
            {
                xticklength = xoffset;
                yticklength = 0;
            }
            else if (Azimuth >= 0 && Azimuth < 90)
            {
                xticklength = 0;
                yticklength = -yoffset;
            }
            else if (Azimuth >= 90 && Azimuth <= 180)
            {
                xticklength = -xoffset;
                yticklength = 0;
            }
        }

        private void setNegativeTickLength(float xoffset, float yoffset, ref float xticklength, ref float yticklength)
        {
            if (Azimuth >= -180 && Azimuth < -90)
            {
                yticklength = 0;
                xticklength = xoffset;
            }
            else if (Azimuth >= -90 && Azimuth < 0)
            {
                yticklength = -yoffset;
                xticklength = 0;
            }
            else if (Azimuth >= 0 && Azimuth < 90)
            {
                yticklength = 0;
                xticklength = -xoffset;
            }
            else if (Azimuth >= 90 && Azimuth <= 180)
            {
                yticklength = yoffset;
                xticklength = 0;
            }
        }
        #endregion

        #region Grids
        private void AddGrids(Graphics g)
        {
            Matrix3 m = Matrix3.AzimuthElevation(Elevation, Azimuth);
            Pen aPen = new Pen(GridStyle.LineColor, GridStyle.Thickness);
            aPen.DashStyle = GridStyle.Pattern;

            // Draw x gridlines:
            if (IsXGrid)
            {
                for (float x = XMin; x <= XMax; x = x + XTick)
                {
                    drawXGridlines(g, m, aPen, x);
                }

                // Draw y gridlines:
                if (IsYGrid)
                {
                    for (float y = YMin; y <= YMax; y = y + YTick)
                    {
                        drawYGridlines(g, m, aPen, y);
                    }
                }

                // Draw Z gridlines:
                if (IsZGrid && ZMin != ZMax)
                {
                    for (float z = ZMin; z <= ZMax; z = z + ZTick)
                    {
                        drawZGridlines(g, m, aPen, z);
                    }
                }
            }
        }

        private void drawXGridlines(Graphics g, Matrix3 m, Pen aPen, float x)
        {
            Point3[] pta = new Point3[3];
            Point3[] pts = CoordinatesOfChartBox();
            pta[0] = new Point3(x, pts[1].Y, pts[1].Z, pts[1].W);
            if (Elevation >= 0)
            {
                if ((Azimuth >= -180 && Azimuth < -90) ||
                    (Azimuth >= 0 && Azimuth < 90))
                {
                    pta[1] = new Point3(x, pts[0].Y, pts[1].Z, pts[1].W);
                    pta[2] = new Point3(x, pts[0].Y, pts[3].Z, pts[1].W);
                }
                else
                {
                    pta[1] = new Point3(x, pts[2].Y, pts[1].Z, pts[1].W);
                    pta[2] = new Point3(x, pts[2].Y, pts[3].Z, pts[1].W);

                }
            }
            else if (Elevation < 0)
            {
                if ((Azimuth >= -180 && Azimuth < -90) ||
                    (Azimuth >= 0 && Azimuth < 90))
                {
                    pta[1] = new Point3(x, pts[2].Y, pts[1].Z, pts[1].W);
                    pta[2] = new Point3(x, pts[2].Y, pts[3].Z, pts[1].W);

                }
                else
                {
                    pta[1] = new Point3(x, pts[0].Y, pts[1].Z, pts[1].W);
                    pta[2] = new Point3(x, pts[0].Y, pts[3].Z, pts[1].W);
                }
            }
            for (int i = 0; i < pta.Length; i++)
            {
                pta[i].Transform(m, form1, this);
            }
            g.DrawLine(aPen, pta[0].X, pta[0].Y, pta[1].X, pta[1].Y);
            g.DrawLine(aPen, pta[1].X, pta[1].Y, pta[2].X, pta[2].Y);
        }

        private void drawYGridlines(Graphics g, Matrix3 m, Pen aPen, float y)
        {
            Point3[] pta = new Point3[3];
            Point3[] pts = CoordinatesOfChartBox();
            pta[0] = new Point3(pts[1].X, y, pts[1].Z, pts[1].W);
            if (Elevation >= 0)
            {
                if ((Azimuth >= -180 && Azimuth < -90) ||
                    (Azimuth >= 0 && Azimuth < 90))
                {
                    pta[1] = new Point3(pts[2].X, y, pts[1].Z, pts[1].W);
                    pta[2] = new Point3(pts[2].X, y, pts[3].Z, pts[1].W);
                }
                else
                {
                    pta[1] = new Point3(pts[0].X, y, pts[1].Z, pts[1].W);
                    pta[2] = new Point3(pts[0].X, y, pts[3].Z, pts[1].W);
                }
            }
            if (Elevation < 0)
            {
                if ((Azimuth >= -180 && Azimuth < -90) ||
                    (Azimuth >= 0 && Azimuth < 90))
                {
                    pta[1] = new Point3(pts[0].X, y, pts[1].Z, pts[1].W);
                    pta[2] = new Point3(pts[0].X, y, pts[3].Z, pts[1].W);

                }
                else
                {
                    pta[1] = new Point3(pts[2].X, y, pts[1].Z, pts[1].W);
                    pta[2] = new Point3(pts[2].X, y, pts[3].Z, pts[1].W);
                }
            }
            for (int i = 0; i < pta.Length; i++)
            {
                pta[i].Transform(m, form1, this);
            }
            g.DrawLine(aPen, pta[0].X, pta[0].Y, pta[1].X, pta[1].Y);
            g.DrawLine(aPen, pta[1].X, pta[1].Y, pta[2].X, pta[2].Y);
        }

        private void drawZGridlines(Graphics g, Matrix3 m, Pen aPen, float z)
        {
            Point3[] pta = new Point3[3];
            Point3[] pts = CoordinatesOfChartBox();
            pta[0] = new Point3(pts[2].X, pts[2].Y, z, pts[2].W);
            if (Elevation >= 0)
            {
                if ((Azimuth >= -180 && Azimuth < -90) ||
                    (Azimuth >= 0 && Azimuth < 90))
                {
                    pta[1] = new Point3(pts[2].X, pts[0].Y, z, pts[1].W);
                    pta[2] = new Point3(pts[0].X, pts[0].Y, z, pts[1].W);
                }
                else
                {
                    pta[1] = new Point3(pts[0].X, pts[2].Y, z, pts[1].W);
                    pta[2] = new Point3(pts[0].X, pts[1].Y, z, pts[1].W);
                }
            }
            if (Elevation < 0)
            {
                if ((Azimuth >= -180 && Azimuth < -90) ||
                    (Azimuth >= 0 && Azimuth < 90))
                {
                    pta[1] = new Point3(pts[0].X, pts[2].Y, z, pts[1].W);
                    pta[2] = new Point3(pts[0].X, pts[0].Y, z, pts[1].W);

                }
                else
                {
                    pta[1] = new Point3(pts[2].X, pts[0].Y, z, pts[1].W);
                    pta[2] = new Point3(pts[0].X, pts[0].Y, z, pts[1].W);
                }
            }
            for (int i = 0; i < pta.Length; i++)
            {
                pta[i].Transform(m, form1, this);
            }
            g.DrawLine(aPen, pta[0].X, pta[0].Y, pta[1].X, pta[1].Y);
            g.DrawLine(aPen, pta[1].X, pta[1].Y, pta[2].X, pta[2].Y);
        }
        #endregion

        #region Lables
        private void AddLabels(Graphics g)
        {
            Matrix3 m = Matrix3.AzimuthElevation(Elevation, Azimuth);
            Point3[] pts = CoordinatesOfChartBox();
            SolidBrush aBrush = new SolidBrush(LabelColor);
            StringFormat sf = new StringFormat();
            sf.Alignment = StringAlignment.Center;

            // Add x tick labels:
            float offset = (YMax - YMin) / (MeshGridOf3DForm.SegCount - 1);
            float labelSpace = offset;
            getXTickLabels(g, m, pts, aBrush, sf, offset, labelSpace);

            // Add y tick labels:
            pts = getYTickLabels(g, m, pts, aBrush, sf, out offset, out labelSpace);

            // Add z tick labels:
            pts = getZTickLabels(g, m, pts, aBrush, sf, labelSpace);

            // Add Title:
            sf.Alignment = StringAlignment.Center;
            aBrush = new SolidBrush(TitleColor);
            if (Title != "No Title")
            {
                g.DrawString(Title, TitleFont, aBrush,
                    new PointF(form1.PlotPanel.Width / 2, form1.Height / 30), sf);
            }
            aBrush.Dispose();

            // Add x axis label:
            float offset1, xc;
            getXAxisLabelInfo(out aBrush, sf, out offset, out labelSpace, out xc);
            Point3[] pta = new Point3[2];
            pta[0] = new Point3(XMin, pts[1].Y + labelSpace, pts[1].Z, pts[1].W);
            pta[1] = new Point3((XMin + XMax) / 2 - xc, pts[1].Y + labelSpace,
                pts[1].Z, pts[1].W);
            pta[0].Transform(m, form1, this);
            pta[1].Transform(m, form1, this);
            if (pta[1].X - pta[0].X == 0f)
            {
                return;
            }
            float theta = (float)Math.Atan((pta[1].Y - pta[0].Y) / (pta[1].X - pta[0].X));
            theta = theta * 180 / (float)Math.PI;
            GraphicsState gs = g.Save();
            g.TranslateTransform(pta[1].X, pta[1].Y);
            g.RotateTransform(theta);
            g.DrawString(XLabel, LabelFont, aBrush,
                new PointF(0, 0), sf);
            g.Restore(gs);

            // Add y axis label:
            float yc;
            getYAxisLabelInfo(out offset, out labelSpace, out offset1, out yc);
            pta[0] = new Point3(pts[1].X + labelSpace, YMin, pts[1].Z, pts[1].W);
            pta[1] = new Point3(pts[1].X + labelSpace, (YMin + YMax) / 2 + yc, pts[1].Z, pts[1].W);
            pta[0].Transform(m, form1, this);
            pta[1].Transform(m, form1, this);
            theta = (float)Math.Atan((pta[1].Y - pta[0].Y) / (pta[1].X - pta[0].X));
            theta = theta * 180 / (float)Math.PI;
            gs = g.Save();
            g.TranslateTransform(pta[1].X, pta[1].Y);
            g.RotateTransform(theta);
            g.DrawString(YLabel, LabelFont, aBrush,
                new PointF(0, 0), sf);
            g.Restore(gs);

            // Add z axis labels:
            float zc, zlength;
            getZAxisLabelInfo(g, offset, out labelSpace, out offset1, out zc, out zlength);
            pta[0] = new Point3(pts[2].X - labelSpace, pts[2].Y,
                (ZMin + ZMax) / 2 + zc, pts[2].W);
            pta[0].Transform(m, form1, this);
            gs = g.Save();
            g.TranslateTransform(pta[0].X - zlength, pta[0].Y);
            g.RotateTransform(270);
            g.DrawString(ZLabel, LabelFont, aBrush,
                new PointF(0, 0), sf);
            g.Restore(gs);
        }

        private void getXTickLabels(Graphics g, Matrix3 m, Point3[] pts, SolidBrush aBrush, StringFormat sf, float offset, float labelSpace)
        {
            for (float x = XMin + XTick; x < XMax; x = x + XTick)
            {
                if (Elevation >= 0)
                {
                    if (Azimuth >= -90 && Azimuth < 90)
                        labelSpace = -offset;
                }
                else if (Elevation < 0 && ((Azimuth >= -180 && Azimuth < -90) || Azimuth >= 90 && Azimuth <= 180))
                {
                    labelSpace = -offset;
                }
                Point3 pt = new Point3(x, pts[1].Y + labelSpace, pts[1].Z, pts[1].W);
                pt.Transform(m, form1, this);
                g.DrawString(x.ToString(), TickFont, aBrush,
                    new PointF(pt.X, pt.Y), sf);
            }
        }

        private Point3[] getYTickLabels(Graphics g, Matrix3 m, Point3[] pts, SolidBrush aBrush, StringFormat sf, out float offset, out float labelSpace)
        {
            offset = (XMax - XMin) / (MeshGridOf3DForm.SegCount - 1);
            labelSpace = offset;
            for (float y = YMin + YTick; y < YMax; y = y + YTick)
            {
                pts = CoordinatesOfChartBox();
                if (Elevation >= 0)
                {
                    if (Azimuth >= -180 && Azimuth < 0)
                        labelSpace = -offset;
                }
                else if (Elevation < 0 && Azimuth >= 0 && Azimuth < 180)
                {
                    labelSpace = -offset;
                }
                Point3 pt = new Point3(pts[1].X + labelSpace, y, pts[1].Z, pts[1].W);
                pt.Transform(m, form1, this);
                g.DrawString(y.ToString(), TickFont, aBrush,
                    new PointF(pt.X, pt.Y), sf);
            }

            return pts;
        }

        private Point3[] getZTickLabels(Graphics g, Matrix3 m, Point3[] pts, SolidBrush aBrush, StringFormat sf, float labelSpace)
        {
            SizeF s = g.MeasureString("A", TickFont);
            if (ZMin != ZMax)
            {
                for (float z = ZMin; z <= ZMax; z = z + ZTick)
                {
                    sf.Alignment = StringAlignment.Far;
                    pts = CoordinatesOfChartBox();
                    Point3 pt = new Point3(pts[2].X - 4.0f, pts[2].Y + 0.5f, z, pts[2].W);
                    pt.Transform(m, form1, this);
                    g.DrawString(z.ToString(), TickFont, aBrush,
                        new PointF(pt.X - labelSpace, pt.Y - s.Height / 2), sf);
                }
            }

            return pts;
        }

        private void getXAxisLabelInfo(out SolidBrush aBrush, StringFormat sf, out float offset, out float labelSpace, out float xc)
        {
            offset = (YMax - YMin) / 3;
            labelSpace = offset;
            sf.Alignment = StringAlignment.Center;
            aBrush = new SolidBrush(LabelColor);
            float offset1 = (XMax - XMin) / 10;
            xc = offset1;
            if (Elevation >= 0)
            {
                setXAxis(offset, ref labelSpace, ref xc, offset1);
            }
            else if (Elevation < 0)
            {
                setNegativeXAxis(offset, ref labelSpace, ref xc, offset1);
            }
        }

        private void setXAxis(float offset, ref float labelSpace, ref float xc, float offset1)
        {
            if (Azimuth >= -90 && Azimuth < 90)
                labelSpace = -offset;
            if (Azimuth >= 0 && Azimuth <= 180)
                xc = -offset1;
        }

        private void setNegativeXAxis(float offset, ref float labelSpace, ref float xc, float offset1)
        {
            if ((Azimuth >= -180 && Azimuth < -90) || Azimuth >= 90 && Azimuth <= 180)
                labelSpace = -offset;
            if (Azimuth >= -180 && Azimuth <= 0)
                xc = -offset1;
        }

        private void getYAxisLabelInfo(out float offset, out float labelSpace, out float offset1, out float yc)
        {
            offset = (XMax - XMin) / 3;
            offset1 = (YMax - YMin) / 5;
            labelSpace = offset;
            yc = YTick;
            if (Elevation >= 0)
            {
                setYAxis(offset, ref labelSpace, offset1, ref yc);
            }
            else if (Elevation < 0)
            {
                yc = setNegativeYAxis(offset, ref labelSpace, offset1);
            }
        }

        private void setYAxis(float offset, ref float labelSpace, float offset1, ref float yc)
        {
            if (Azimuth >= -180 && Azimuth < 0)
                labelSpace = -offset;
            if (Azimuth >= -90 && Azimuth <= 90)
                yc = -offset1;
        }

        private float setNegativeYAxis(float offset, ref float labelSpace, float offset1)
        {
            float yc = -offset1;
            if (Azimuth >= 0 && Azimuth < 180)
                labelSpace = -offset;
            if (Azimuth >= -90 && Azimuth <= 90)
                yc = offset1;
            return yc;
        }

        private void getZAxisLabelInfo(Graphics g, float offset, out float labelSpace, out float offset1, out float zc, out float zlength)
        {
            float zticklength = 10;
            labelSpace = -1.3f * offset;
            offset1 = (ZMax - ZMin) / 8;
            zc = -offset1;
            if (ZMin != ZMax)
            {
                for (float z = ZMin; z < ZMax; z = z + ZTick)
                {
                    SizeF size = g.MeasureString(z.ToString(), TickFont);
                    if (zticklength < size.Width)
                        zticklength = size.Width;
                }
            }

            zlength = -zticklength;
            if (Azimuth >= -180 && Azimuth < -90)
            {
                zlength = -zticklength;
                labelSpace = -1.3f * offset;
                zc = -offset1;
            }
            else if (Azimuth >= -90 && Azimuth < 0)
            {
                zlength = zticklength;
                labelSpace = 2 * offset / 3;
                zc = offset1;
            }
            else if (Azimuth >= 0 && Azimuth < 90)
            {
                zlength = zticklength;
                labelSpace = 2 * offset / 3;
                zc = -offset1;
            }
            else if (Azimuth >= 90 && Azimuth <= 180)
            {
                zlength = -zticklength;
                labelSpace = -1.3f * offset;
                zc = offset1;
            }
            if (Elevation < 0)
            {
                zc = -zc;
            }
        }
        #endregion
        #endregion
    }
}