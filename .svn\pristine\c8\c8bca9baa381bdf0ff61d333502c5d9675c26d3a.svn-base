﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCellCoverLapByRegion_LTE : ZTCellCoverLapByRegion
   {

        private static ZTCellCoverLapByRegion_LTE instance = null;
        public new static ZTCellCoverLapByRegion_LTE GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTCellCoverLapByRegion_LTE();
                    }
                }
            }
            return instance;
        }

        protected ZTCellCoverLapByRegion_LTE()
            : base()
        {
        }

        public ZTCellCoverLapByRegion_LTE(ServiceName serviceName)
            : this()
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        }

        public override string Name
        {
            get { return "过覆盖分析_LTE"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22010, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            DIYSampleGroup leakGroup = new DIYSampleGroup();
            leakGroup.ThemeName = "---";

            DTParameter parameter = DTParameterManager.GetInstance().GetParameter("lte_TAC");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_ECI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_EARFCN");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_PCI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_RSRP");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_RSRQ");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }

            return leakGroup;
        }

        protected virtual float? getRsrp(TestPoint tp)
        {
            return (float?)tp["lte_RSRP"];
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            try
            {
                if (this.isBand)
                {
                    int? earfcn = (int?)tp["lte_EARFCN"];
                    if (earfcn == null)
                    {
                        return false;
                    }
                    if (!LTECell.GetBandTypeByEarfcn((int)earfcn).ToString().Equals(this.bandType))
                    {
                        return false;
                    }
                }

                bool inRegion = Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
                if (inRegion && (tp is LTETestPointDetail || tp is LTEUepTestPoint))//进行过覆盖算法运算
                {
                    return judgeCoverLap(tp);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        private bool judgeCoverLap(TestPoint tp)
        {
            float? pccpchRSCP = getRsrp(tp);
            if (pccpchRSCP == null || pccpchRSCP < curFilterRxlev)
            {
                return false;
            }
            LTECell cell = tp.GetMainCell_LTE();
            if (cell == null || cell.Type == LTEBTSType.Indoor)
            {
                return false;
            }

            CellCoverLap_LTE covLap = getCovLap(cell);

            if (fileIDNameDic.ContainsKey(tp.FileID) && !covLap.strFileID.Contains(fileIDNameDic[tp.FileID] + ""))
            {
                covLap.strFileID += fileIDNameDic[tp.FileID] + ",";
            }

            double distanceToCell = MathFuncs.GetDistance(tp.Longitude, tp.Latitude, cell.Longitude, cell.Latitude);
            bool isBadCheck = distanceToCell > covLap.rationalDistance;
            if (isBadCheck)
            {
                covLap.AddBadSample(tp, distanceToCell, (float)pccpchRSCP, (int?)(float?)tp["lte_RSRQ"]);
                return true;
            }
            else
            {
                covLap.goodSampleCount++;
                return false;
            }
        }

        private CellCoverLap_LTE getCovLap(LTECell cell)
        {
            CellCoverLap_LTE covLap = null;
            CellCoverLap clTmp = null;
            if (!cellLapRetDic.TryGetValue(cell.Name, out clTmp))
            {
                double radiusOfCell = MasterCom.ES.Data.CfgDataProvider.CalculateRadius(cell, nearestCellCount);
                covLap = new CellCoverLap_LTE(cell, radiusOfCell);
                covLap.rationalDistance = radiusOfCell * disFactor;
                covLap.nearestBTSs = MasterCom.ES.Data.CfgDataProvider.GetNearestBTSs(cell, nearestCellCount);
                covLap.mnger = new DTDataManager(MainModel.GetInstance());
                cellLapRetDic[cell.Name] = covLap;
            }
            else
            {
                covLap = (CellCoverLap_LTE)clTmp;
            }

            return covLap;
        }

        protected override void getResultAfterQuery()
        {
            curSelDIYSampleGroup.ThemeName = "TD_LTE_RSRP";
            FilterCellCoverLap();
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new CoverLapProperties_LTE(this, false);
            }
        }
        #endregion
    }

    //过覆盖分析************************************************
    public class ZTCellCoverLapByRegion_LteFdd : ZTCellCoverLapByRegion_LTE
    {
        private static ZTCellCoverLapByRegion_LteFdd instance = null;
        public new static ZTCellCoverLapByRegion_LteFdd GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTCellCoverLapByRegion_LteFdd();
                    }
                }
            }
            return instance;
        }

        protected ZTCellCoverLapByRegion_LteFdd()
            : base()
        {
            ServiceTypes.Clear();
            carrierID = CarrierType.ChinaMobile;
        }

        public override string Name
        {
            get { return "过覆盖分析_LTE_FDD"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26004, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            DIYSampleGroup leakGroup = new DIYSampleGroup();
            leakGroup.ThemeName = "---";

            DTParameter parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_TAC");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_ECI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_EARFCN");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_PCI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_RSRP");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_RSRQ");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                leakGroup.ColumnsDefSet.Add(pDef);
            }

            return leakGroup;
        }

        protected override float? getRsrp(TestPoint tp)
        {
            return (float?)tp["lte_fdd_RSRP"];
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            try
            {
                bool inRegion = Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
                //进行过覆盖算法运算

                if (inRegion && tp is LTEFddTestPoint)
                {
                    float? pccpchRSCP = getRsrp(tp);
                    if (pccpchRSCP == null)
                    {
                        return false;
                    }
                    if (pccpchRSCP < curFilterRxlev)
                    {
                        return false;
                    }
                    LTECell cell = tp.GetMainCell_LTE_FDD();
                    if (cell != null)
                    {
                        return judgeCoverLap(tp, pccpchRSCP, cell);
                    }
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        private bool judgeCoverLap(TestPoint tp, float? pccpchRSCP, LTECell cell)
        {
            if (cell.Type == LTEBTSType.Indoor)
            {
                return false;
            }
            CellCoverLap_LTE covLap = null;
            CellCoverLap clTmp = null;
            if (!cellLapRetDic.TryGetValue(cell.Name, out clTmp))
            {
                double radiusOfCell = MasterCom.ES.Data.CfgDataProvider.CalculateRadius(cell, nearestCellCount);
                covLap = new CellCoverLap_LTE(cell, radiusOfCell);
                covLap.rationalDistance = radiusOfCell * disFactor;
                covLap.nearestBTSs = MasterCom.ES.Data.CfgDataProvider.GetNearestBTSs(cell, nearestCellCount);
                covLap.mnger = new DTDataManager(MainModel.GetInstance());
                cellLapRetDic[cell.Name] = covLap;
            }
            else
            {
                covLap = (CellCoverLap_LTE)clTmp;
            }
            double distanceToCell = MathFuncs.GetDistance(tp.Longitude, tp.Latitude, cell.Longitude, cell.Latitude);
            bool isBadCheck = distanceToCell > covLap.rationalDistance;
            if (isBadCheck)
            {
                covLap.AddBadSample(tp, distanceToCell, (float)pccpchRSCP, (int?)(float?)tp["lte_fdd_RSRQ"]);
                return true;
            }
            else
            {
                covLap.goodSampleCount++;
                return false;
            }
        }

        protected override void getResultAfterQuery()
        {
            curSelDIYSampleGroup.ThemeName = "LTE_FDD:RSRP";
            FilterCellCoverLap();
        }
    }

    public class ZTCellCoverLapByRegion_LteFdd_VOLTE : ZTCellCoverLapByRegion_LteFdd
    {
        private static ZTCellCoverLapByRegion_LteFdd_VOLTE instance = null;
        public static new ZTCellCoverLapByRegion_LteFdd_VOLTE GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTCellCoverLapByRegion_LteFdd_VOLTE();
                    }
                }
            }
            return instance;
        }
        protected ZTCellCoverLapByRegion_LteFdd_VOLTE()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }
        public override string Name
        {
            get { return "过覆盖分析_LTE_FDD"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30038, this.Name);
        }
        protected override bool isValidTestPoint(TestPoint tp)
        {
            if (ServiceTypes.Count > 0 && !ServiceTypes.Contains((ServiceType)tp.ServiceType)) return false;
            return true;
        }
    }
}
