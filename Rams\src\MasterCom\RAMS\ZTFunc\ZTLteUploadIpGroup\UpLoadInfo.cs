﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTLteUploadIpGroup
{
    public class UpLoadInfo
    {
        public UpLoadInfo(string fileName, string ip)
        {
            this.FileName = fileName;
            this.IP = ip;
        }
        public string FileName
        {
            get;
            private set;
        }
        public string IP
        {
            get;
            private set;
        }
        public DateTime BeginTime
        {
            get;
            set;
        }
        public DateTime EndTime
        {
            get
            {
                DateTime t = DateTime.MinValue;
                if (UploadResultEvents.Count > 0)
                {
                    t = UploadResultEvents[UploadResultEvents.Count - 1].DateTime;
                }
                return t;
            }
        }
        public int ULDuration
        {
            get;
            set;
        }
        public List<TestPoint> TestPoints { get; set; } = new List<TestPoint>();
        public List<Event> UploadResultEvents { get; set; } = new List<Event>();

        public double AvgRsrp
        {
            get;
            private set;
        }
        public double AvgSinr
        {
            get;
            private set;
        }
        public double AvgSpeedM
        {
            get;
            private set;
        }
        public double FailRate
        {
            get;
            private set;
        }
        public double DoubleRankTimeRate
        {
            get;
            private set;
        }
        public double AvgGrantCount
        {
            get;
            private set;
        }
        public double AvgPrbPers
        {
            get;
            private set;
        }
        public double ULQam64Rate
        {
            get;
            private set;
        }
        public double Cqi0 { get; set; }

        public double Cqi1 { get; set; }
        public double Bler { get; set; }
        public double? Bler0 { get; set; }
        public double? Bler1 { get; set; }
        public double AckSum { get; set; }
        public double NackSum { get; set; }
        public double AckRate { get; set; }
        public double NackRate { get; set; }
        public double MscAvg { get; set; }

        class DataInfo
        {
            public double mscSum = 0;
            public int mscNum = 0;
            public int time1Sum = 0;
            public int time2Sum = 0;
            public int lastRank = 0;
            public int lastIdx = -1;
        }

        public void MakeSummary()
        {
            AvgRsrp = this["RSRP", SummaryType.Avg];
            AvgSinr = this["SINR", SummaryType.Avg];
            AvgSpeedM = this["APP_Speed_Mb", SummaryType.Avg];
            int failNum = 0;
            foreach (Event evt in UploadResultEvents)
            {
                if (evt.ID == 62)
                {
                    failNum++;
                }
            }
            FailRate = Math.Round(100.0 * failNum / UploadResultEvents.Count, 2);

            AvgGrantCount = this["PDCCH_UL_Grant_Count", SummaryType.Avg];

            AvgPrbPers = this["PDSCH_PRb_Num_s", SummaryType.Avg];

            double cQam64Sum = this["Times_QAM64_UL", SummaryType.Sum];
            double cQpskSum = this["Times_QPSK_UL", SummaryType.Sum];
            double cQam16Sum = this["Times_QAM16_UL", SummaryType.Sum];

            ULQam64Rate = Math.Round(100.0 * cQam64Sum / (cQam64Sum + cQpskSum + cQam16Sum), 2);

            Cqi0 = this["Wideband_CQI_for_CW0", SummaryType.Avg];
            Cqi1 = this["Wideband_CQI_for_CW1", SummaryType.Avg];
            Bler = this["PDSCH_BLER", SummaryType.Avg];
            Bler0 = this["PDSCH_Code0_BLER", SummaryType.Avg];
            Bler0 = double.IsNaN((double)Bler0) ? null : Bler0;
            Bler1 = this["PDSCH_Code1_BLER", SummaryType.Avg];
            Bler1 = double.IsNaN((double)Bler1) ? null : Bler1;

            AckSum = this["Count_UL_HARQ_ACK", SummaryType.Sum];
            NackSum = this["Count_UL_HARQ_NACK", SummaryType.Sum];
            AckRate = Math.Round(100.0 * AckSum / (AckSum + NackSum), 2);
            NackRate = Math.Round(100 - AckRate, 2);

            DataInfo info = new DataInfo();
            for (int i = 0; i < TestPoints.Count; i++)
            {
                TestPoint tp = TestPoints[i];
                double val = GetLteMCSCode_UL_Avg(tp, "MCS_UL");
                if (!double.IsNaN(val))
                {
                    info.mscSum += val;
                    info.mscNum++;
                }

                dealTPTime(info, i, tp);
            }
            MscAvg = Math.Round(info.mscSum / info.mscNum, 2);
            DoubleRankTimeRate = Math.Round(info.time2Sum * 100.0 / (info.time1Sum + info.time2Sum), 2);
        }

        private void dealTPTime(DataInfo info, int i, TestPoint tp)
        {
            bool added = false;
            int rank = getRank(tp);

            if (info.lastRank == 0 && (rank == 1 || rank == 2))
            {
                info.lastIdx = i;
                info.lastRank = rank;
            }

            if (testpointBandIdx.Contains(i))
            {
                if (info.lastRank == 1)
                {
                    info.time1Sum += TestPoints[i - 1].Time - TestPoints[info.lastIdx].Time;
                    added = true;
                }
                else if (info.lastRank == 2)
                {
                    info.time2Sum += TestPoints[i - 1].Time - TestPoints[info.lastIdx].Time;
                }
                info.lastIdx = i;
                info.lastRank = rank;
            }

            if (rank != info.lastRank)
            {
                added = addTime(info, tp, added);
                info.lastIdx = i;
            }
            info.lastRank = rank;

            if (i == TestPoints.Count - 1 && !added)
            {//避免漏掉最后一段
                if (info.lastRank == 1)
                {
                    info.time1Sum += tp.Time - TestPoints[info.lastIdx].Time;
                }
                else if (info.lastRank == 2)
                {
                    info.time2Sum += tp.Time - TestPoints[info.lastIdx].Time;
                }
            }
        }

        private static int getRank(TestPoint tp)
        {
            int rank = 0;
            object obj = null;
            if (tp is LTEFddTestPoint)
            {
                obj = tp["lte_fdd_Rank_Indicator"];
            }
            else
            {
                obj = tp["lte_Rank_Indicator"];
            }
            if (obj != null)
            {
                int.TryParse(obj.ToString(), out rank);
            }

            return rank;
        }

        private bool addTime(DataInfo info, TestPoint tp, bool added)
        {
            if (info.lastRank == 1)
            {
                info.time1Sum += tp.Time - TestPoints[info.lastIdx].Time;
                added = true;
            }
            else if (info.lastRank == 2)
            {
                info.time2Sum += tp.Time - TestPoints[info.lastIdx].Time;
                added = true;
            }

            return added;
        }

        public double GetLteMCSCode_UL_Avg(TestPoint tp, string key)
        {
            if (tp is LTEFddTestPoint)
            {
                key = "lte_fdd_" + key;
            }
            else
            {
                key = "lte_" + key;
            }
            double d = 0;
            double p = 0;
            for (int i = 0; i < 32; i++)
            {
                object x = tp[key, i];
                if (x != null)
                {
                    double val;
                    if (double.TryParse(x.ToString(), out val))
                    {
                        p += val * i;
                        d += val;
                    }
                }
            }
            return Math.Round(p / d, 2);
        }

        public enum SummaryType
        {
            Num,
            Sum,
            Avg
        }

        public double this[string key, SummaryType type]
        {
            get
            {
                double sum = 0;
                int num = 0;
                foreach (TestPoint tp in TestPoints)
                {
                    if (tp is LTEFddTestPoint)
                    {
                        key = "lte_fdd_" + key;
                    }
                    else
                    {
                        key = "lte_" + key;
                    }
                    object objV = tp[key];
                    if (objV == null)
                    {
                        continue;
                    }
                    double val;
                    if (double.TryParse(objV.ToString(), out val))
                    {
                        num++;
                        sum += val;
                    }
                }
                switch (type)
                {
                    case SummaryType.Num:
                        return num;
                    case SummaryType.Sum:
                        return Math.Round(sum, 2);
                    case SummaryType.Avg:
                        return Math.Round(sum / num, 2);
                    default:
                        break;
                }
                return double.NaN;
            }
        }

        private readonly List<int> testpointBandIdx = new List<int>();
        internal void AddTestPoints(List<TestPoint> dlingTpSet)
        {
            if (TestPoints.Count > 0 && dlingTpSet.Count > 0)
            {
                testpointBandIdx.Add(TestPoints.Count);
            }
            if (dlingTpSet.Count > 0)
            {
                ULDuration += dlingTpSet[dlingTpSet.Count - 1].Time - dlingTpSet[0].Time;
            }
            TestPoints.AddRange(dlingTpSet);
        }
    }
}
