﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using MasterCom.MControls;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Stat
{
    public class RptCell
    {
        public readonly static string GridAvg = "GridAvg";
        public int rowAt { get; set; }
        public int colAt { get; set; }
        public Color bkColor { get; set; } = Color.White;
        public Color foreColor { get; set; } = Color.Black;
        public string exp { get; set; } = string.Empty;
        public byte carrierID { get; set; } = 1;//默认运营商是中国移动
        public byte momt { get; set; } = 0;
        public string cellValue { get; set; }//单元格value
        public string ExtraExp { get; set; }
        public int deciNum { get; set; } = 4;//取值小数点位数
        /// <summary>
        /// 动态设置背景颜色时的参数。
        /// </summary>
        public List<object> DynamicBKColorParams { get; set; }
        public bool IsDynamicBKColor { get; set; } = false;
        private float valueRamgeMin = 0;
        public float ValueRangeMin
        {
            get { return valueRamgeMin; }
            set
            {
                if (value <= ValueRangeMax)
                {
                    valueRamgeMin = value;
                }
            }
        }
        private float valueRangeMax = 100;
        public float ValueRangeMax
        {
            get { return valueRangeMax; }
            set
            {
                if (value >= valueRamgeMin)
                {
                    valueRangeMax = value;
                }
            }
        }
        public List<DTParameterRangeColor> DynamicBKColorRanges { get; set; }
        public Color GetBKColorByValue(double value)
        {
            Color ret = bkColor;
            if (IsDynamicBKColor && DynamicBKColorRanges != null)
            {
                foreach (DTParameterRangeColor item in DynamicBKColorRanges)
                {
                    if (item.Within((float)value))
                    {
                        ret = item.Value;
                        break;
                    }
                }
            }
            return ret;
        }

        internal RptCell CopyInstance()
        {
            RptCell cell = new RptCell();
            cell.Param = this.Param;
            return cell;
        }
        internal void applyChangeStyle(RptCell rpt)
        {
            this.bkColor = rpt.bkColor;
            this.foreColor = rpt.foreColor;
        }
        internal void applyChangeContent(RptCell rpt)
        {
            this.exp = rpt.exp;
            this.carrierID = rpt.carrierID;
            this.momt = rpt.momt;
        }
        internal string CellValue
        {
            get { return cellValue; }
            set { cellValue = value; }
        }
        
        public List<int> ServiceIDSet { get; set; } = new List<int>();
        public string FileNameFilter { get; set; } = "";
        public List<string> FileNameKeyValueList
        {
            get
            {
                List<string> list = new List<string>();
                if (!string.IsNullOrEmpty(FileNameFilter))
                {
                    string uppStr = FileNameFilter.ToUpper();
                    uppStr = uppStr.Replace(" OR ", " or ");
                    string[] split = uppStr.Replace(" or ", QueryCondition.Splitor).Split(new string[]{ QueryCondition.Splitor },
                        StringSplitOptions.RemoveEmptyEntries);

                    list = new List<string>(split);
                }
                return list;
            }
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Exp"] = exp;
                param["RowAt"] = rowAt;
                param["ColAt"] = colAt;
                if (DynamicBKColorRanges != null && DynamicBKColorRanges.Count > 0)
                {
                    List<object> list_R = new List<object>();
                    foreach (DTParameterRangeColor range in DynamicBKColorRanges)
                    {
                        List<object> list_P = new List<object>();
                        list_P.Add(range.Min);
                        list_P.Add(range.Max);
                        list_P.Add(range.Value.ToArgb());
                        list_R.Add(list_P);
                    }
                    param["BKColorRanges"] = list_R;
                }
                param["Rcells"] = (object)DynamicBKColorParams;
                param["BkColorR"] = (int)(bkColor.R);
                param["BkColorG"] = (int)(bkColor.G);
                param["BkColorB"] = (int)(bkColor.B);
                param["ForeColorR"] = (int)(foreColor.R);
                param["ForeColorG"] = (int)(foreColor.G);
                param["ForeColorB"] = (int)(foreColor.B);
                param["CarrierID"] = (int)(carrierID);
                param["momt"] = (int)(momt);
                param["IsDynamicBKClr"] = IsDynamicBKColor;
                if (ExtraExp != null)
                {
                    param["ExtendExp"] = ExtraExp;
                }
                param["ServiceIDSet"] = ServiceIDSet;
                param["DeciNum"] = this.deciNum;
                param["FileNameFilter"] = FileNameFilter;
                return param;
            }
            set
            {
                setParm(value);
            }
        }

        private void setParm(Dictionary<string, object> value)
        {
            exp = (string)value["Exp"];
            rowAt = (int)value["RowAt"];
            colAt = (int)value["ColAt"];
            int r = (int)value["BkColorR"];
            int g = (int)value["BkColorG"];
            int b = (int)value["BkColorB"];
            bkColor = Color.FromArgb(r, g, b);
            r = (int)value["ForeColorR"];
            g = (int)value["ForeColorG"];
            b = (int)value["ForeColorB"];
            if (value.ContainsKey("IsDynamicBKClr"))
            {
                IsDynamicBKColor = (bool)value["IsDynamicBKClr"];
            }
            if (value.ContainsKey("Rcells"))
            {
                DynamicBKColorParams = (List<object>)value["Rcells"];
            }
            setBKColorRanges(value);
            foreColor = Color.FromArgb(r, g, b);

            if (value.ContainsKey("CarrierID"))
            {
                carrierID = (byte)(int)value["CarrierID"];
            }

            if (value.ContainsKey("momt"))
            {
                momt = (byte)(int)value["momt"];
            }
            else
            {
                momt = 0;
            }
            if (value.ContainsKey("ExtendExp"))
            {
                ExtraExp = value["ExtendExp"] as string;
            }
            setServiceIDSet(value);
            if (value.ContainsKey("DeciNum"))
            {
                this.deciNum = (int)value["DeciNum"];
            }
            else
            {
                this.deciNum = 4;
            }

            if (value.ContainsKey("FileNameFilter"))
            {
                this.FileNameFilter = (string)value["FileNameFilter"];
            }
            else
            {
                this.FileNameFilter = "";
            }
        }

        private void setBKColorRanges(Dictionary<string, object> value)
        {
            if (value.ContainsKey("BKColorRanges"))
            {
                List<object> list = (List<object>)value["BKColorRanges"];
                DynamicBKColorRanges = new List<DTParameterRangeColor>();
                foreach (List<object> list_P in list)
                {
                    DTParameterRangeColor range = new DTParameterRangeColor();
                    for (int i = 0; i < list_P.Count; i++)
                    {
                        if (i == 0)
                            range.Min = (float)list_P[i];
                        else if (i == 1)
                            range.Max = (float)list_P[i];
                        else range.Value = Color.FromArgb((int)list_P[i]);
                    }
                    DynamicBKColorRanges.Add(range);
                }
            }
        }

        private void setServiceIDSet(Dictionary<string, object> value)
        {
            if (value.ContainsKey("ServiceIDSet"))
            {
                object obj = value["ServiceIDSet"];
                if (obj is List<object>)
                {
                    ServiceIDSet.Clear();
                    foreach (int id in obj as List<object>)
                    {
                        ServiceIDSet.Add(id);
                    }
                }
                else if (obj is List<int>)
                {
                    ServiceIDSet = value["ServiceIDSet"] as List<int>;
                }
            }
        }
    }
}
