﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTWirelessNetTest
{
    /// <summary>
    /// 按地市汇聚后的最终结果,每个级别的数据分别保存在一个list中
    /// </summary>
    public class WirelessNetTestResult
    {
        public WirelessNetTestResult()
        {
            DistrictResList = new List<WirelessNetTestKpiDistrict>();
            CountyResList = new List<WirelessNetTestKpiCounty>();
            SceneResList = new List<WirelessNetTestKpiScene>();
            SubSceneResList = new List<WirelessNetTestKpiSubScene>();
        }

        //结果最终分为4个合集,分别保存到4个tabpage中显示
        public List<WirelessNetTestKpiDistrict> DistrictResList { get; set; }
        public List<WirelessNetTestKpiCounty> CountyResList { get; set; }
        public List<WirelessNetTestKpiScene> SceneResList { get; set; }
        public List<WirelessNetTestKpiSubScene> SubSceneResList { get; set; }

        public void Adddata(WirelessNetTestResult data)
        {
            DistrictResList.AddRange(data.DistrictResList);
            CountyResList.AddRange(data.CountyResList);
            SceneResList.AddRange(data.SceneResList);
            SubSceneResList.AddRange(data.SubSceneResList);
        }

        public void Sort()
        {
            //按地市名排序
            DistrictResList.Sort((a,b)=>a.DistrictName.FormulaData.CompareTo(b.DistrictName.FormulaData));
            CountyResList.Sort((a, b) => a.DistrictName.FormulaData.CompareTo(b.DistrictName.FormulaData));
            SceneResList.Sort((a, b) => a.DistrictName.FormulaData.CompareTo(b.DistrictName.FormulaData));
            SubSceneResList.Sort((a, b) => a.DistrictName.FormulaData.CompareTo(b.DistrictName.FormulaData));
        }
    }

    #region 每个级别的结果数据
    public class WirelessNetTestKpi
    {
        public WirelessNetTestKpi()
        {
            FormulaDataDic = new Dictionary<CarrierStatType, Dictionary<ReportExcelStyle, double>>();
        }

        public Dictionary<CarrierStatType, Dictionary<ReportExcelStyle, double>> FormulaDataDic { get; set; }

        public class OtherFormula
        {
            public OtherFormula(string formulaDesc)
            {
                FormulaDesc = formulaDesc;
            }

            public string FormulaDesc { get; }
            public string FormulaData { get; set; } = "";

            public override string ToString()
            {
                return FormulaData;
            }
        }

        #region 添加列字段
        public virtual DataTable GetTableColumns()
        {
            DataTable dt = new DataTable();
            addStartColumns(dt);
            dt.Columns.Add("运营商");
            WirelessNetTestReport.Instance.AddFormulaTableColumns(dt);
            addEndColumns(dt);
            return dt;
        }

        protected virtual void addStartColumns(DataTable dt)
        {
            //添加公式前面的字段
        }

        protected virtual void addEndColumns(DataTable dt)
        {
            //添加公式后面的字段
        }
        #endregion

        #region 添加行数据
        public virtual void AddTableRows<T>(DataTable dt, List<T> resList)
            where T : WirelessNetTestKpi
        {
            foreach (var res in resList)
            {
                List<object> rowDatas = new List<object>();
                addStartDatas(rowDatas, res);
                foreach (var carrierFormulaDic in res.FormulaDataDic)
                {
                    rowDatas.Add(carrierFormulaDic.Key.ToString());
                    foreach (var data in carrierFormulaDic.Value.Values)
                    {
                        if (double.IsNaN(data))
                        {
                            rowDatas.Add("-");
                        }
                        else
                        {
                            rowDatas.Add(data);
                        }
                    }
                }
                addEndDatas(rowDatas, res);
                dt.Rows.Add(rowDatas.ToArray());
            }
        }

        protected virtual void addStartDatas<T>(List<object> rowDatas, T data)
            where T : WirelessNetTestKpi
        {
        }

        protected virtual void addEndDatas<T>(List<object> rowDatas, T data)
            where T : WirelessNetTestKpi
        {
        }
        #endregion
    }

    public class WirelessNetTestKpiDistrict : WirelessNetTestKpi
    {
        public OtherFormula DistrictName { get; } = new OtherFormula("地市");

        protected override void addStartColumns(DataTable dt)
        {
            dt.Columns.Add(DistrictName.FormulaDesc);
        }

        protected override void addStartDatas<T>(List<object> rowDatas, T data)
        {
            var curData = data as WirelessNetTestKpiDistrict;
            rowDatas.Add(curData.DistrictName.FormulaData);
        }
    }

    public class WirelessNetTestKpiCounty : WirelessNetTestKpi
    {
        public OtherFormula DistrictName { get; } = new OtherFormula("地市");
        public OtherFormula CountyName { get; } = new OtherFormula("区县");

        protected override void addStartColumns(DataTable dt)
        {
            dt.Columns.Add(DistrictName.FormulaDesc);
            dt.Columns.Add(CountyName.FormulaDesc);
        }

        protected override void addStartDatas<T>(List<object> rowDatas, T data)
        {
            var curData = data as WirelessNetTestKpiCounty;
            rowDatas.Add(curData.DistrictName.FormulaData);
            rowDatas.Add(curData.CountyName.FormulaData);
        }
    }

    public class WirelessNetTestKpiScene : WirelessNetTestKpi
    {
        public OtherFormula DistrictName { get; } = new OtherFormula("地市");
        public OtherFormula Scene { get; } = new OtherFormula("场景");

        public OtherFormula Injection { get; } = new OtherFormula("渗透率");
        public OtherFormula CarSpeed { get; } = new OtherFormula("总车速");
        public OtherFormula LteCellOccupyRate { get; } = new OtherFormula("4G主服小区占用比例");
        public OtherFormula LteBtsOccupyRate { get; } = new OtherFormula("4G基站占用比例");
        public OtherFormula NrCellOccupyRate { get; } = new OtherFormula("5G主服小区占用比例");
        public OtherFormula NrBtsOccupyRate { get; } = new OtherFormula("5G基站占用比例");

        protected override void addStartColumns(DataTable dt)
        {
            dt.Columns.Add(DistrictName.FormulaDesc);
            dt.Columns.Add(Scene.FormulaDesc);

            dt.Columns.Add(Injection.FormulaDesc);
            dt.Columns.Add(CarSpeed.FormulaDesc);
            dt.Columns.Add(LteCellOccupyRate.FormulaDesc);
            dt.Columns.Add(LteBtsOccupyRate.FormulaDesc);
            dt.Columns.Add(NrCellOccupyRate.FormulaDesc);
            dt.Columns.Add(NrBtsOccupyRate.FormulaDesc);
        }

        protected override void addStartDatas<T>(List<object> rowDatas, T data)
        {
            var curData = data as WirelessNetTestKpiScene;
            rowDatas.Add(curData.DistrictName.FormulaData);
            rowDatas.Add(curData.Scene.FormulaData);

            rowDatas.Add(curData.Injection.FormulaData);
            rowDatas.Add(curData.CarSpeed.FormulaData);
            rowDatas.Add(curData.LteCellOccupyRate.FormulaData);
            rowDatas.Add(curData.LteBtsOccupyRate.FormulaData);
            rowDatas.Add(curData.NrCellOccupyRate.FormulaData);
            rowDatas.Add(curData.NrBtsOccupyRate.FormulaData);
        }
    }

    public class WirelessNetTestKpiSubScene : WirelessNetTestKpi
    {
        public OtherFormula DistrictName { get; } = new OtherFormula("地市");
        public OtherFormula SubScene { get; } = new OtherFormula("子场景");

        //渗透率暂不输出
        public OtherFormula Injection { get; } = new OtherFormula("渗透率");
        public OtherFormula CarSpeed { get; } = new OtherFormula("总车速");
        public OtherFormula LteCellOccupyRate { get; } = new OtherFormula("4G主服小区占用比例");
        public OtherFormula LteBtsOccupyRate { get; } = new OtherFormula("4G基站占用比例");
        public OtherFormula NrCellOccupyRate { get; } = new OtherFormula("5G主服小区占用比例");
        public OtherFormula NrBtsOccupyRate { get; } = new OtherFormula("5G基站占用比例");

        protected override void addStartColumns(DataTable dt)
        {
            dt.Columns.Add(DistrictName.FormulaDesc);
            dt.Columns.Add(SubScene.FormulaDesc);

            dt.Columns.Add(Injection.FormulaDesc);
            dt.Columns.Add(CarSpeed.FormulaDesc);
            dt.Columns.Add(LteCellOccupyRate.FormulaDesc);
            dt.Columns.Add(LteBtsOccupyRate.FormulaDesc);
            dt.Columns.Add(NrCellOccupyRate.FormulaDesc);
            dt.Columns.Add(NrBtsOccupyRate.FormulaDesc);
        }

        protected override void addStartDatas<T>(List<object> rowDatas, T data)
        {
            var curData = data as WirelessNetTestKpiSubScene;
            rowDatas.Add(curData.DistrictName.FormulaData);
            rowDatas.Add(curData.SubScene.FormulaData);

            rowDatas.Add(curData.Injection.FormulaData);
            rowDatas.Add(curData.CarSpeed.FormulaData);
            rowDatas.Add(curData.LteCellOccupyRate.FormulaData);
            rowDatas.Add(curData.LteBtsOccupyRate.FormulaData);
            rowDatas.Add(curData.NrCellOccupyRate.FormulaData);
            rowDatas.Add(curData.NrBtsOccupyRate.FormulaData);
        }
    }
    #endregion

    //#region 报表公式
    //public class WirelessNetTestReportInfo
    //{
    //    public WirelessNetTestReportInfo()
    //    {
    //        FormulaInfoDic = new Dictionary<string, WirelessNetTestFormula>();
    //        ImgCodeTotalDic = new Dictionary<string, bool>();
    //        EventIDTotalDic = new Dictionary<int, bool>();
    //    }

    //    //报表包含的所有公式
    //    public Dictionary<string, WirelessNetTestFormula> FormulaInfoDic { get; set; } 
    //    //报表公式包含的所有指标
    //    public Dictionary<string, bool> ImgCodeTotalDic { get; set; } 
    //    public Dictionary<int, bool> EventIDTotalDic { get; set; } 
    //}

    ///// <summary>
    ///// 读取公式Excel格式
    ///// </summary>
    //public class WirelessNetTestFormula
    //{
    //    /// <summary>
    //    /// 指标顺序
    //    /// </summary>
    //    public string FormulaIndex { get; set; }
    //    /// <summary>
    //    /// 指标公式
    //    /// </summary>
    //    public string Formula { get; set; }
    //    /// <summary>
    //    /// 指标名
    //    /// </summary>
    //    public string FormulaName { get; set; }
    //    /// <summary>
    //    /// 移动有效
    //    /// </summary>
    //    public bool IsCMValid { get; set; }
    //    /// <summary>
    //    /// 电信/联通有效
    //    /// </summary>
    //    public bool IsOherValid { get; set; }

    //    //每个公式有哪些指标
    //    public Dictionary<string, bool> ImgCodeTotalDic { get; set; } = new Dictionary<string, bool>();
    //    public Dictionary<int, bool> EventIDTotalDic { get; set; } = new Dictionary<int, bool>();
    //}
    //#endregion
}
