using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using CQTLibrary.PublicItem;
using CQTLibrary.RAMS.NET;
using DevExpress.XtraCharts;
using DevExpress.XtraGrid;
using DevExpress.XtraTab;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;

namespace MasterCom.RAMS
{
    public partial class CQTAdrePropertyAssessGSMAlarmForm : DevExpress.XtraEditors.XtraForm
    {
        private List<AlarmResult> alarmResults;
        private DataTable alarmDataForShow;
        private Dictionary<DateTime, List<ParaResult>> alarmDetailInfForAdress;
        public GetAlarmDetailInfoByCqtFunc GetAlarmDetailInfoByCqtDo { get; set; }
        public GetAlarmDetailInfoByCqtFuncCell GetAlarmDetailInfoByCqtCellDo { get; set; }
        private string focuedCQTAdress;
        private DataSet dsForAdressDetailDraw;
        private DataSet dsForAdressDetailShow;
        private Dictionary<string, List<object[]>> dicDataForAdressDraw;
        private Dictionary<string, List<string[]>> dicDataForAdressShow;
        private Dictionary<LaiKey, Dictionary<DateTime, List<ParaResult>>> dicDataForAdressCell;
        private bool IsTD;
        private int simplecount = 0;
        private int cellsimplecount = 0;
        bool isNewCreate = true;
        bool cellIsNewCreate = true;
        private Dictionary<string, Dictionary<string, Dictionary<DateTime, float>>> alarmDataSecDic;
        private Dictionary<string, Dictionary<string, Dictionary<DateTime, float>>> cellAlarmDataSecDic;

        public CQTAdrePropertyAssessGSMAlarmForm(List<AlarmResult> alarmResults, bool IsTD)
        {
            InitializeComponent();
            this.IsTD = IsTD;
            this.alarmResults = alarmResults;
            IniFormHideShow();

        }

        private void IniFormHideShow()
        {
            chartControlDraw.Series.Clear();
            xtraTabPage2.PageVisible = false;
            xtraTabPage3.PageVisible = false;
        }

        private void CQTAdrePropertyAssessGSMAlarmForm_Load(object sender, EventArgs e)
        {
            if (alarmResults == null || alarmResults.Count == 0)
            {
                XtraMessageBox.Show("未能获得查询数据");
                this.Close();
                return;
            }
            IniDataTableForShow();
            for (int i = 0; i < alarmResults.Count; i++)
            {
                AlarmResult alarRes = alarmResults[i];
                object[] rowInf = new object[8];
                rowInf[0] = alarRes.StrNo;
                rowInf[1] = alarRes.StrCqtName;
                rowInf[2] = alarRes.StrProblem;
                if (IsTD)
                {
                    rowInf[3] = alarRes.StrTdSetupResult;
                    rowInf[4] = alarRes.StrTdRealseResult;
                    rowInf[5] = alarRes.StrTdInterSystemResult;
                    rowInf[6] = alarRes.StrTdIntraSystemResult;
                }
                else
                {
                    rowInf[3] = alarRes.StrGsmVolumeResult;
                    rowInf[4] = alarRes.StrGsmAccessResult;
                    rowInf[5] = alarRes.StrGsmHoldResult;
                    rowInf[6] = alarRes.StrGsmDataResutl;
                }
                rowInf[7] = i;
                alarmDataForShow.Rows.Add(rowInf);
            }
            this.gridRes.DataSource = alarmDataForShow;
        }

        private void IniDataTableForShow()
        {
            alarmDataForShow = new DataTable();
            alarmDataForShow.Columns.Add("c1");
            alarmDataForShow.Columns.Add("c2");
            alarmDataForShow.Columns.Add("c3");
            alarmDataForShow.Columns.Add("c4");
            alarmDataForShow.Columns.Add("c5");
            alarmDataForShow.Columns.Add("c6");
            alarmDataForShow.Columns.Add("c7");
            alarmDataForShow.Columns.Add("c8");

            if (IsTD)
            {
                gridColumn4.Caption = "业务建立相关";
                gridColumn5.Caption = "业务释放相关";
                gridColumn6.Caption = "异系统切换";
                gridColumn7.Caption = "本系统切换";
            }
        }

        private void grdViewAdressDetail_CustomDrawEmptyForeground(object sender, DevExpress.XtraGrid.Views.Base.CustomDrawEventArgs e)
        {
            if (simplecount == 0)
            {
                string explainTxt = "没有地点详细数据";
                Font f = new Font("宋体", 10, FontStyle.Bold);
                Rectangle r = new Rectangle(e.Bounds.Left + 5, e.Bounds.Top + 5, e.Bounds.Width - 5, e.Bounds.Height - 25);
                e.Graphics.DrawString(explainTxt, f, Brushes.Black, r);
            }
        }

        private void 导出数据ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (grdViewAll.RowCount != 0 && saveFileDialog1.ShowDialog() == DialogResult.OK)
            {
                grdViewAll.ExportToXls(saveFileDialog1.FileName);
            }
        }

        private void 查看地点测试明细ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                if (grdViewAll.RowCount != 0)
                {
                    DataRow rowInfTemp = grdViewAll.GetFocusedDataRow();
                    this.lblCtrlAdress1.Text = rowInfTemp["c2"].ToString();
                    this.lblCtrlAdress2.Text = rowInfTemp["c3"].ToString();
                    this.lblCtrlAdress3.Text = rowInfTemp["c4"].ToString();
                    this.lblCtrlAdress4.Text = rowInfTemp["c5"].ToString();
                    this.lblCtrlAdress5.Text = rowInfTemp["c6"].ToString();
                    this.lblCtrlAdress6.Text = rowInfTemp["c7"].ToString();
                    if (GetAlarmDetailInfoByCqtDo != null)
                    {
                        focuedCQTAdress = rowInfTemp["c2"].ToString();
                        WaitBox.Show(GetAlarmDetailInfoForAdressWaitBoxDo);
                        if (!xtraTabPage2.PageVisible)
                        {
                            xtraTabPage2.PageVisible = true;
                        }
                        xtraTabControl1.SelectedTabPage = xtraTabPage2;
                        backBtn.Visible = true;
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        private void GetAlarmDetailInfoForAdressWaitBoxDo()
        {
            WaitBox.ProgressPercent = 35;
            alarmDetailInfForAdress = GetAlarmDetailInfoByCqtDo(focuedCQTAdress);
            WaitBox.ProgressPercent = 85;
            IniAlarmDetailInfoForAdress();
            WaitBox.ProgressPercent = 100;
            WaitBox.Close();
        }

        private void IniAlarmDetailInfoForAdress()
        {
            if (alarmDetailInfForAdress != null && alarmDetailInfForAdress.Count != 0)
            {
                //将原始数据解析
                GetAnalysisDataFromDataGet();
                //将解析后的数据转换成可以绘制和显示的数据
                GetDrawAndShowDataSetForAdress();
                //初始化绘图显示用的Series
                IniChartShow();
                //初始化显示数据
                IniDataShow();
                //让第一条曲线可见
                if (chartControlDraw.Series != null && chartControlDraw.Series.Count != 0)
                {
                    checkEdit1.Checked = true;
                    chartControlDraw.Series[0].Visible = true;
                    chartControlDraw.Series[0].Label.Visible = false;
                    chartControlDraw.Series[1].Label.Visible = false;
                    chartControlDraw.Series[2].Label.Visible = false;
                    chartControlDraw.Series[3].Label.Visible = false;
                    chartControlDraw.Series[4].Label.Visible = false;
                    chartControlDraw.Series[5].Label.Visible = false;
                    chartControlDraw.Series[6].Label.Visible = false;
                }
            }
        }

        private void IniDataShow()
        {
            List<string> projectName = new List<string>();
            Dictionary<string, Dictionary<DateTime, float>> kpiName;
            Dictionary<DateTime, float> dateTime;
            List<string> kpiNameList = new List<string>();
            List<DateTime> dtime = new List<DateTime>();
            projectName.AddRange(alarmDataSecDic.Keys);

            for (int i = 0; i < projectName.Count; i++)
            {
                dtime.Clear();
                kpiName = alarmDataSecDic[projectName[i]];
                kpiNameList.AddRange(kpiName.Keys);
                dateTime = kpiName[kpiNameList[0]];
                dtime.AddRange(dateTime.Keys);
                DataTable dt = initDataTable(kpiName, kpiNameList, dtime);
                simplecount = dt.Rows.Count;
                if (isNewCreate)
                {
                    initPageControl(projectName, kpiNameList, i, dt);
                }
                else
                {
                    initGridControl(i, dt);
                }
                kpiNameList.Clear();
            }
            isNewCreate = false;
        }

        private static DataTable initDataTable(Dictionary<string, Dictionary<DateTime, float>> kpiName, List<string> kpiNameList, List<DateTime> dtime)
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("时间");
            for (int m = 0; m < kpiNameList.Count; m++)
            {
                dt.Columns.Add(kpiNameList[m]);
            }
            foreach (DateTime dtTime in dtime)
            {
                DataRow dr = dt.NewRow();
                dr["时间"] = dtTime.ToString("yyyy-MM-dd");
                for (int n = 0; n < kpiNameList.Count; n++)
                {
                    if (kpiName[kpiNameList[n]].ContainsKey(dtTime))
                    {
                        dr[kpiNameList[n]] = Math.Round(kpiName[kpiNameList[n]][dtTime], 2).ToString() + "%";
                    }
                }
                dt.Rows.Add(dr);
            }

            return dt;
        }

        private void initPageControl(List<string> projectName, List<string> kpiNameList, int i, DataTable dt)
        {
            XtraTabPage page = new XtraTabPage();
            page.Name = (i + 1).ToString();
            page.Text = projectName[i];
            xTabCtrlPages.TabPages.Add(page);
            if (i == 0)
                xTabCtrlPages.SelectedTabPage = page;
            GridControl gridControl = new GridControl();
            GridView gridView = new GridView();

            GridColumn Colt = new GridColumn();
            Colt.FieldName = "时间";
            Colt.Caption = "时间";
            Colt.VisibleIndex = gridView.Columns.Count;
            gridView.Columns.Add(Colt);
            for (int m = 0; m < kpiNameList.Count; m++)
            {
                GridColumn Col1 = new GridColumn();
                Col1.FieldName = kpiNameList[m];
                Col1.Caption = kpiNameList[m];
                Col1.VisibleIndex = gridView.Columns.Count;
                gridView.Columns.Add(Col1);
            }

            gridControl.DataSource = dt;
            gridControl.MainView = gridView;
            gridView.Name = "gridView" + i.ToString();
            gridView.OptionsBehavior.Editable = false;
            gridView.OptionsView.ShowGroupPanel = false;
            gridView.OptionsView.ColumnAutoWidth = false;
            for (int co = 0; co < gridView.Columns.Count; co++)
            {
                int nameCount = gridView.Columns[co].Caption.Length;
                if (co == 0)
                {
                    gridView.Columns[co].Width = 90;
                    gridView.Columns[co].AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
                }
                else
                {
                    gridView.Columns[co].Width = 110;
                    if (nameCount > 8)
                        gridView.Columns[co].Caption = gridView.Columns[co].Caption.Substring(0, nameCount / 2) + " " + gridView.Columns[co].Caption.Substring(nameCount / 2, nameCount - nameCount / 2);
                    gridView.Columns[co].AppearanceHeader.Options.UseTextOptions = true;
                    gridView.Columns[co].AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
                    gridView.Columns[co].AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
                }
            }
            gridView.ColumnPanelRowHeight = 35;
            gridView.VisibleColumns[0].Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            gridControl.ContextMenuStrip = contextMenuStrip2;
            gridControl.Name = "gc" + i.ToString();
            gridControl.Dock = DockStyle.Fill;
            page.Controls.Add(gridControl);
        }

        private void initGridControl(int i, DataTable dt)
        {
            try
            {
                GridControl gtrl = null;
                gtrl = (GridControl)xTabCtrlPages.TabPages[i].Controls[0];
                gtrl.DataSource = dt;
                gtrl.RefreshDataSource();
            }
            catch
            {
                //continue
            }
        }

        private void IniChartShow()
        {
            checkEdit1.Checked = false;
            checkEdit2.Checked = false;
            checkEdit3.Checked = false;
            checkEdit4.Checked = false;
            checkEdit5.Checked = false;
            checkEdit6.Checked = false;
            checkEdit7.Checked = false;
            chartControlDraw.Series.Clear();
            foreach (DataTable dt in dsForAdressDetailDraw.Tables)
            {
                chartControlDraw.Series.Add(IniChartControlSeries(dt.TableName, dt));
                chartControlDraw.Series[chartControlDraw.Series.Count - 1].Visible = false;
            }
            for (int i = 0; i < 7; i++)
            {
                if (i < dsForAdressDetailShow.Tables.Count)
                {
                    switch (i)
                    {
                        case 0:
                            checkEdit1.Text = dsForAdressDetailShow.Tables[i].TableName;
                            checkEdit1.Show();
                            break;
                        case 1:
                            checkEdit2.Text = dsForAdressDetailShow.Tables[i].TableName;
                            checkEdit2.Show();
                            break;
                        case 2:
                            checkEdit3.Text = dsForAdressDetailShow.Tables[i].TableName;
                            checkEdit3.Show();
                            break;
                        case 3:
                            checkEdit4.Text = dsForAdressDetailShow.Tables[i].TableName;
                            checkEdit4.Show();
                            break;
                        case 4:
                            checkEdit5.Text = dsForAdressDetailShow.Tables[i].TableName;
                            checkEdit5.Show();
                            break;
                        case 5:
                            checkEdit6.Text = dsForAdressDetailShow.Tables[i].TableName;
                            checkEdit6.Show();
                            break;
                        case 6:
                            checkEdit7.Text = dsForAdressDetailShow.Tables[i].TableName;
                            checkEdit7.Show();
                            break;
                        default:
                            break;
                    }
                }
            }
        }

        private Series IniChartControlSeries(string seriesName, DataTable dtForDraw)
        {
            Series seriesTemp = new Series(seriesName, ViewType.Spline);
            seriesTemp.DataSource = dtForDraw;
            seriesTemp.ArgumentScaleType = ScaleType.Qualitative;

            // 以哪个字段进行显示 
            seriesTemp.ArgumentDataMember = "c1";
            seriesTemp.ValueScaleType = ScaleType.Numerical;
            seriesTemp.PointOptions.ValueNumericOptions.Format = NumericFormat.Percent;
            //就是这的series

            // 柱状图里的柱的取值字段 
            seriesTemp.ValueDataMembers[0] = "c2";

            return seriesTemp;
        }

        private void GetAnalysisDataFromDataGet()
        {
            #region 从原始数据中获得解析后的地点数据
            dicDataForAdressDraw = new Dictionary<string, List<object[]>>();
            dicDataForAdressShow = new Dictionary<string, List<string[]>>();
            alarmDataSecDic = new Dictionary<string, Dictionary<string, Dictionary<DateTime, float>>>();

            foreach (DateTime dTime in alarmDetailInfForAdress.Keys)
            {
                List<ParaResult> prList = alarmDetailInfForAdress[dTime];
                foreach (ParaResult prItem in prList)
                {
                    addAlarmDataSecDic(dTime, prItem);
                }
            }

            foreach (DateTime dtKey in alarmDetailInfForAdress.Keys)
            {
                foreach (ParaResult para in alarmDetailInfForAdress[dtKey])
                {
                    addDicDataForAdressDraw(dtKey, para);

                    addDicDataForAdressShow(dtKey, para);
                }
            }
            #endregion
        }

        private void addDicDataForAdressDraw(DateTime dtKey, ParaResult para)
        {
            object[] tempRowForDraw = new object[2];
            tempRowForDraw[0] = dtKey.ToString("yyyy/MM/dd");
            tempRowForDraw[1] = Math.Round(para.FValue / 100.0, 4);
            if (dicDataForAdressDraw.ContainsKey(para.StrKpiName))
            {
                dicDataForAdressDraw[para.StrKpiName].Add(tempRowForDraw);
            }
            else
            {
                List<object[]> lTemp = new List<object[]>();
                lTemp.Add(tempRowForDraw);
                dicDataForAdressDraw.Add(para.StrKpiName, lTemp);
            }
        }

        private void addDicDataForAdressShow(DateTime dtKey, ParaResult para)
        {
            string[] tempRowForShow = new string[4];
            tempRowForShow[0] = dtKey.ToString("yyyy-MM-dd HH:mm:ss");
            tempRowForShow[1] = para.StrAlarmType.ToString();
            tempRowForShow[2] = (para.FValue / 100.0).ToString();
            tempRowForShow[3] = para.StrValue.ToString();
            if (dicDataForAdressShow.ContainsKey(para.StrKpiName))
            {
                dicDataForAdressShow[para.StrKpiName].Add(tempRowForShow);
            }
            else
            {
                List<string[]> lStrTemp = new List<string[]>();
                lStrTemp.Add(tempRowForShow);
                dicDataForAdressShow.Add(para.StrKpiName, lStrTemp);
            }
        }

        private void addAlarmDataSecDic(DateTime dTime, ParaResult prItem)
        {
            if (alarmDataSecDic.ContainsKey(prItem.StrAlarmType))
            {
                Dictionary<string, Dictionary<DateTime, float>> kpiNameDic = alarmDataSecDic[prItem.StrAlarmType];
                if (kpiNameDic.ContainsKey(prItem.StrKpiName))
                {
                    Dictionary<DateTime, float> dTimeDic = kpiNameDic[prItem.StrKpiName];
                    if (!dTimeDic.ContainsKey(dTime))
                    {
                        dTimeDic.Add(dTime, prItem.FValue);
                    }
                }
                else
                {
                    Dictionary<DateTime, float> dTimeDic = new Dictionary<DateTime, float>();
                    dTimeDic.Add(dTime, prItem.FValue);
                    kpiNameDic.Add(prItem.StrKpiName, dTimeDic);
                }
            }
            else
            {
                Dictionary<string, Dictionary<DateTime, float>> kpiNameDic = new Dictionary<string, Dictionary<DateTime, float>>();
                Dictionary<DateTime, float> dTimeDic = new Dictionary<DateTime, float>();
                dTimeDic.Add(dTime, prItem.FValue);
                kpiNameDic.Add(prItem.StrKpiName, dTimeDic);
                alarmDataSecDic.Add(prItem.StrAlarmType, kpiNameDic);
            }
        }

        private void GetDrawAndShowDataSetForAdress()
        {
            if (dicDataForAdressDraw != null && dicDataForAdressDraw.Count != 0)
            {
                dsForAdressDetailDraw = new DataSet();
                foreach (string strName in dicDataForAdressDraw.Keys)
                {
                    DataTable dtTemp = new DataTable(strName);
                    dtTemp.Columns.Add("c1", typeof(string));
                    dtTemp.Columns.Add("c2", typeof(double));
                    for (int i = 0; i < dicDataForAdressDraw[strName].Count; i++)
                    {
                        object[] rowData = new object[2];
                        rowData[0] = dicDataForAdressDraw[strName][i][0];
                        rowData[1] = double.Parse(dicDataForAdressDraw[strName][i][1].ToString());
                        dtTemp.Rows.Add(rowData);

                    }
                    dsForAdressDetailDraw.Tables.Add(dtTemp);
                }
            }

            if (dicDataForAdressShow != null && dicDataForAdressShow.Count != 0)
            {
                dsForAdressDetailShow = new DataSet();
                foreach (string strName in dicDataForAdressShow.Keys)
                {
                    DataTable dtTemp = new DataTable(strName);
                    dtTemp.Columns.Add("c1", typeof(string));
                    dtTemp.Columns.Add("c2", typeof(string));
                    dtTemp.Columns.Add("c3", typeof(string));
                    dtTemp.Columns.Add("c4", typeof(string));
                    dtTemp.Columns.Add("c5", typeof(string));
                    for (int i = 0; i < dicDataForAdressShow[strName].Count; i++)
                    {
                        DataRow row = dtTemp.NewRow();
                        row["c1"] = dicDataForAdressShow[strName][i][0].ToString();
                        row["c2"] = strName.ToString();
                        row["c3"] = dicDataForAdressShow[strName][i][1].ToString();
                        row["c4"] = dicDataForAdressShow[strName][i][2].ToString();
                        row["c5"] = dicDataForAdressShow[strName][i][3].ToString();
                        dtTemp.Rows.Add(row);
                    }
                    dsForAdressDetailShow.Tables.Add(dtTemp);
                    dtTemp.Dispose();
                }
            }
        }

        private void checkEdit_CheckedChanged(object sender, EventArgs e)
        {
            SeriesChangeDo((sender as CheckEdit).Name, (sender as CheckEdit).Checked);
        }

        private void SeriesChangeDo(string checkEditName, bool IsChecked)
        {
            if (checkEditName.Contains("1"))
            {
                chartControlDraw.Series[0].Visible = IsChecked;
                return;
            }

            if (checkEditName.Contains("2"))
            {
                chartControlDraw.Series[1].Visible = IsChecked;
                return;
            }

            if (checkEditName.Contains("3"))
            {
                chartControlDraw.Series[2].Visible = IsChecked;
                return;
            }

            if (checkEditName.Contains("4"))
            {
                chartControlDraw.Series[3].Visible = IsChecked;
                return;
            }

            if (checkEditName.Contains("5"))
            {
                chartControlDraw.Series[4].Visible = IsChecked;
                return;
            }

            if (checkEditName.Contains("6"))
            {
                chartControlDraw.Series[5].Visible = IsChecked;
                return;
            }

            if (checkEditName.Contains("7"))
            {
                chartControlDraw.Series[6].Visible = IsChecked;
                return;
            }

            if (checkEditName.Contains("8"))
            {
                chartControlDraw.Series[7].Visible = IsChecked;
            }
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            if (GetAlarmDetailInfoByCqtCellDo != null && grdViewAll.RowCount != 0)
            {
                DataRow rowInfTemp = grdViewAll.GetFocusedDataRow();
                this.l1.Text = rowInfTemp["c2"].ToString();
                this.l2.Text = rowInfTemp["c3"].ToString();
                this.l3.Text = rowInfTemp["c4"].ToString();
                this.l4.Text = rowInfTemp["c5"].ToString();
                this.l5.Text = rowInfTemp["c6"].ToString();
                this.l6.Text = rowInfTemp["c7"].ToString();
                if (GetAlarmDetailInfoByCqtDo != null)
                {
                    focuedCQTAdress = rowInfTemp["c2"].ToString();
                    WaitBox.Show(GetAlarmDetailInfoForAdressCellWaitBoxDo);

                    if (!xtraTabPage3.PageVisible)
                    {
                        xtraTabPage3.PageVisible = true;
                    }
                    xtraTabControl1.SelectedTabPage = xtraTabPage3;
                    backBtn.Visible = true;
                }
            }
        }

        private void GetAlarmDetailInfoForAdressCellWaitBoxDo()
        {
            WaitBox.ProgressPercent = 30;
            if (GetAlarmDetailInfoByCqtCellDo != null)
            {
                dicDataForAdressCell = GetAlarmDetailInfoByCqtCellDo(focuedCQTAdress);
                WaitBox.ProgressPercent = 75;

                CellGetAnalysisDataFromDataGet();
                CellDataShow();
            }

            WaitBox.ProgressPercent = 100;
            WaitBox.Close();
        }

        /// <summary>
        /// 转化获取到的小区点信息
        /// </summary>
        private void CellGetAnalysisDataFromDataGet()
        {
            cellAlarmDataSecDic = new Dictionary<string, Dictionary<string, Dictionary<DateTime, float>>>();
            foreach (LaiKey laiKey in dicDataForAdressCell.Keys)
            {
                foreach (DateTime dTime in dicDataForAdressCell[laiKey].Keys)
                {
                    List<ParaResult> prListCell = dicDataForAdressCell[laiKey][dTime];
                    foreach (ParaResult prItem in prListCell)
                    {
                        addCellAlarmDataSecDic(dTime, prItem);
                    }
                }
            }
        }

        private void addCellAlarmDataSecDic(DateTime dTime, ParaResult prItem)
        {
            if (cellAlarmDataSecDic.ContainsKey(prItem.StrAlarmType))
            {
                Dictionary<string, Dictionary<DateTime, float>> cellKpiNameDic = cellAlarmDataSecDic[prItem.StrAlarmType];
                if (cellKpiNameDic.ContainsKey(prItem.StrKpiName))
                {
                    Dictionary<DateTime, float> cellDTimeDic = cellKpiNameDic[prItem.StrKpiName];
                    if (!cellDTimeDic.ContainsKey(dTime))
                    {
                        cellDTimeDic.Add(dTime, prItem.FValue);
                    }
                }
                else
                {
                    Dictionary<DateTime, float> cellDTimeDic = new Dictionary<DateTime, float>();
                    cellDTimeDic.Add(dTime, prItem.FValue);
                    cellKpiNameDic.Add(prItem.StrKpiName, cellDTimeDic);
                }
            }
            else
            {
                Dictionary<string, Dictionary<DateTime, float>> cellKpiNameDic = new Dictionary<string, Dictionary<DateTime, float>>();
                Dictionary<DateTime, float> cellDTimeDic = new Dictionary<DateTime, float>();
                cellDTimeDic.Add(dTime, prItem.FValue);
                cellKpiNameDic.Add(prItem.StrKpiName, cellDTimeDic);
                cellAlarmDataSecDic.Add(prItem.StrAlarmType, cellKpiNameDic);
            }
        }

        /// <summary>
        /// 显示小区数据
        /// </summary>
        private void CellDataShow()
        {
            List<string> cellProjectName = new List<string>();
            Dictionary<string, Dictionary<DateTime, float>> cellKpiName;
            Dictionary<DateTime, float> cellDateTime;
            List<string> cellKpiNameList = new List<string>();
            List<DateTime> cellDtime = new List<DateTime>();
            List<LaiKey> laiKeyList = new List<LaiKey>();
            int lkID = 0;
            cellProjectName.AddRange(cellAlarmDataSecDic.Keys);           
            laiKeyList.AddRange(dicDataForAdressCell.Keys);
            for (int i = 0; i < cellProjectName.Count; i++)
            {
                cellDtime.Clear();
                lkID = 0;
                cellKpiName = alarmDataSecDic[cellProjectName[i]];
                cellKpiNameList.AddRange(cellKpiName.Keys);
                cellDateTime = cellKpiName[cellKpiNameList[0]];
                cellDtime.AddRange(cellDateTime.Keys);
                DataTable dt = getDataTable(cellKpiName, cellKpiNameList, cellDtime, laiKeyList, lkID);
                cellsimplecount = dt.Rows.Count;
                if (cellIsNewCreate)
                {
                    addCellpageControl(cellProjectName, cellKpiNameList, i, dt);
                }
                else
                {
                    setGridControl(i, dt);
                }
                cellKpiNameList.Clear();
            }
            cellIsNewCreate = false;         
        }

        private DataTable getDataTable(Dictionary<string, Dictionary<DateTime, float>> cellKpiName, List<string> cellKpiNameList, List<DateTime> cellDtime, List<LaiKey> laiKeyList, int lkID)
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("时间");
            dt.Columns.Add("LAC");
            dt.Columns.Add("CI");
            for (int m = 0; m < cellKpiNameList.Count; m++)
            {
                dt.Columns.Add(cellKpiNameList[m]);
            }
            foreach (DateTime dtTime in cellDtime)
            {
                DataRow dr = dt.NewRow();
                dr["时间"] = dtTime.ToString("yyyy-MM-dd");
                dr["LAC"] = laiKeyList[lkID].Ilac.ToString();
                dr["CI"] = laiKeyList[lkID].Ici.ToString();
                for (int n = 0; n < cellKpiNameList.Count; n++)
                {
                    if (cellKpiName[cellKpiNameList[n]].ContainsKey(dtTime))
                    {
                        dr[cellKpiNameList[n]] = Math.Round(cellKpiName[cellKpiNameList[n]][dtTime], 2).ToString() + "%";
                    }
                }
                dt.Rows.Add(dr);
            }

            return dt;
        }

        private void addCellpageControl(List<string> cellProjectName, List<string> cellKpiNameList, int i, DataTable dt)
        {
            XtraTabPage cellpage = new XtraTabPage();
            cellpage.Name = "cell" + (i + 1).ToString();
            cellpage.Text = cellProjectName[i];
            xTabPageCell.TabPages.Add(cellpage);
            if (i == 0)
                xTabPageCell.SelectedTabPage = cellpage;
            GridControl cellgridControl = new GridControl();
            GridView cellgridView = new GridView();

            GridColumn cellColt = new GridColumn();
            cellColt.FieldName = "时间";
            cellColt.Caption = "时间";
            cellColt.VisibleIndex = cellgridView.Columns.Count;
            cellgridView.Columns.Add(cellColt);
            GridColumn cellColl = new GridColumn();
            cellColl.FieldName = "LAC";
            cellColl.Caption = "LAC";
            cellColl.VisibleIndex = cellgridView.Columns.Count;
            cellgridView.Columns.Add(cellColl);
            GridColumn cellColc = new GridColumn();
            cellColc.FieldName = "CI";
            cellColc.Caption = "CI";
            cellColc.VisibleIndex = cellgridView.Columns.Count;
            cellgridView.Columns.Add(cellColc);
            for (int m = 0; m < cellKpiNameList.Count; m++)
            {
                GridColumn cellCol1 = new GridColumn();
                cellCol1.FieldName = cellKpiNameList[m];
                cellCol1.Caption = cellKpiNameList[m];
                cellCol1.VisibleIndex = cellgridView.Columns.Count;
                cellgridView.Columns.Add(cellCol1);
            }

            cellgridControl.DataSource = dt;
            cellgridControl.MainView = cellgridView;
            cellgridView.Name = "cellgridView" + i.ToString();
            cellgridView.OptionsBehavior.Editable = false;
            cellgridView.OptionsView.ShowGroupPanel = false;
            cellgridView.OptionsView.ColumnAutoWidth = false;

            for (int cellCo = 0; cellCo < cellgridView.Columns.Count; cellCo++)
            {
                int cellNameCount = cellgridView.Columns[cellCo].Caption.Length;
                if (cellCo == 0 || cellCo == 1 || cellCo == 2)
                {
                    cellgridView.Columns[cellCo].Width = 80;
                    cellgridView.Columns[cellCo].AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
                }
                else
                {
                    cellgridView.Columns[cellCo].Width = 110;
                    if (cellNameCount > 8)
                        cellgridView.Columns[cellCo].Caption = cellgridView.Columns[cellCo].Caption.Substring(0, cellNameCount / 2) + " " + cellgridView.Columns[cellCo].Caption.Substring(cellNameCount / 2, cellNameCount - cellNameCount / 2);
                    cellgridView.Columns[cellCo].AppearanceHeader.Options.UseTextOptions = true;
                    cellgridView.Columns[cellCo].AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
                    cellgridView.Columns[cellCo].AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
                }
            }
            cellgridView.ColumnPanelRowHeight = 35;
            cellgridView.VisibleColumns[0].Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            cellgridControl.ContextMenuStrip = contextMenuStrip2;
            cellgridControl.Name = "cellgc" + i.ToString();
            cellgridControl.Dock = DockStyle.Fill;
            cellpage.Controls.Add(cellgridControl);
        }

        private void setGridControl(int i, DataTable dt)
        {
            try
            {
                GridControl gtrl = (GridControl)xTabPageCell.TabPages[i].Controls[0];
                gtrl.DataSource = dt;
                gtrl.RefreshDataSource();
            }
            catch
            {
                //continue
            }
        }

        private void toolStripMenuItem1_Click(object sender, EventArgs e)
        {
            if (saveFileDialog1.ShowDialog() == DialogResult.OK)
            {
                GridControl gtrl = null;
                gtrl = (GridControl)xTabCtrlPages.SelectedTabPage.Controls[0];
                gtrl.ExportToXls(saveFileDialog1.FileName);
            }
        }

        private void toolStripMenuItem2_Click_1(object sender, EventArgs e)
        {
            if (saveFileDialog1.ShowDialog() == DialogResult.OK)
            {
                GridControl gtrl = null;
                gtrl = (GridControl)xTabPageCell.SelectedTabPage.Controls[0];
                gtrl.ExportToXls(saveFileDialog1.FileName);
            }
        }

        private void labelControl13_Click(object sender, EventArgs e)
        {
            if (labelControl13.Text.Equals("显示标签"))
            {
                for (int i = 0; i < 7; i++)
                {
                    chartControlDraw.Series[i].Label.Visible = true;
                }
                labelControl13.Text = "隐藏标签";
            }
            else if (labelControl13.Text.Equals("隐藏标签"))
            {
                for (int i = 0; i < 7; i++)
                {
                    chartControlDraw.Series[i].Label.Visible = false;
                }
                labelControl13.Text = "显示标签";
            }
        }

        private void backBtn_Click(object sender, EventArgs e)
        {
            if (xtraTabControl1.SelectedTabPage == xtraTabPage3)
            {
                xtraTabControl1.SelectedTabPage = xtraTabPage2;
                xtraTabPage3.PageVisible = false;
            }
            else if (xtraTabControl1.SelectedTabPage == xtraTabPage2)
            {
                xtraTabControl1.SelectedTabPage = xtraTabPage1;
                xtraTabPage2.PageVisible = false;
                xtraTabPage3.PageVisible = false;
                backBtn.Visible = false;
            }
        }

        private void gridRes_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                if (grdViewAll.RowCount != 0)
                {
                    DataRow rowInfTemp = grdViewAll.GetFocusedDataRow();
                    this.lblCtrlAdress1.Text = rowInfTemp["c2"].ToString();
                    this.lblCtrlAdress2.Text = rowInfTemp["c3"].ToString();
                    this.lblCtrlAdress3.Text = rowInfTemp["c4"].ToString();
                    this.lblCtrlAdress4.Text = rowInfTemp["c5"].ToString();
                    this.lblCtrlAdress5.Text = rowInfTemp["c6"].ToString();
                    this.lblCtrlAdress6.Text = rowInfTemp["c7"].ToString();
                    if (GetAlarmDetailInfoByCqtDo != null)
                    {
                        focuedCQTAdress = rowInfTemp["c2"].ToString();
                        WaitBox.Show(GetAlarmDetailInfoForAdressWaitBoxDo);
                        if (!xtraTabPage2.PageVisible)
                        {
                            xtraTabPage2.PageVisible = true;
                        }
                        xtraTabControl1.SelectedTabPage = xtraTabPage2;
                        backBtn.Visible = true;
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        private void 导出ExcelToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (grdViewAll.RowCount != 0 && saveFileDialog1.ShowDialog() == DialogResult.OK)
            {
                grdViewAll.ExportToXls(saveFileDialog1.FileName);
            }
        }

        /// <summary>
        /// 缩放功能
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void chartControlDraw_MouseWheel(object sender, MouseEventArgs e)
        {
            ((ChartControl)sender).BeginInit();
            XYDiagram diagram = ((ChartControl)sender).Diagram as XYDiagram;
            diagram.EnableAxisXScrolling = true;
            double minValueX = diagram.AxisX.Range.MinValueInternal;
            double maxValueX = diagram.AxisX.Range.MaxValueInternal;
            double scrollMinValueX = diagram.AxisX.Range.ScrollingRange.MinValueInternal;
            double scrollMaxValueX = diagram.AxisX.Range.ScrollingRange.MaxValueInternal;
            DiagramCoordinates coord = diagram.PointToDiagram(e.Location);
            double x = coord.NumericalArgument;
            if (e.Delta > 0 && maxValueX - minValueX > 0.1)
            {
                diagram.AxisX.Range.SetInternalMinMaxValues(0.2 * x + 0.8 * minValueX, 0.2 * x + 0.8 * maxValueX);
            }
            if (e.Delta < 0 && (minValueX > scrollMinValueX || maxValueX < scrollMinValueX))
            {
                double minValueInternalX = (1.2 * minValueX - 0.2 * x >= scrollMinValueX) ? 1.2 * minValueX - 0.2 * x : scrollMinValueX;
                double maxValueInternalX = (1.2 * maxValueX - 0.2 * x <= scrollMaxValueX) ? 1.2 * maxValueX - 0.2 * x : scrollMaxValueX;
                diagram.AxisX.Range.SetInternalMinMaxValues(minValueInternalX, maxValueInternalX);
            }
            ((ChartControl)sender).EndInit();
        }
    }
}