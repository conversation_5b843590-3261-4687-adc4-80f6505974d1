﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.Windows.Forms;

namespace MasterCom.RAMS.NewBlackBlock
{
    class NewBlackBlockReappearQuery : QueryNewBlackBlock
    {
        public NewBlackBlockReappearQuery(MainModel mm) : base(mm)
        {
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18024, this.Name);
        }

        protected override void query()
        {
            FillCondition(false, reappCond.QueryCond);
            base.query();
        }

        protected override void fireShowResultForm()
        {
            List<ReappearBlockItem> statedList = StatData();
            FireLayerChanged(statedList);

            ReappearResultForm resultForm = MainModel.CreateResultForm(typeof(ReappearResultForm)) as ReappearResultForm;
            resultForm.FillData(statedList);
            resultForm.Visible = true;
            resultForm.BringToFront();
        }

        protected override bool isValidCondition()
        {
            if (condDlg == null)
            {
                condDlg = new ReappearCondDlg();
            }
            if (reappCond == null)
            {
                reappCond = new ReappearCondition();
            }
            if (condDlg.ShowDialog() == DialogResult.OK)
            {
                condDlg.GetCondition(ref reappCond);
                return true;
            }
            return false;
        }

        /// <summary>
        /// 对查询的结果集进行分析，得到用于呈现的结果列表
        /// </summary>
        /// <returns></returns>
        private List<ReappearBlockItem> StatData()
        {
            List<ReappearBlockItem> retList = new List<ReappearBlockItem>();
            List<BlackBlockItem> bbList = new List<BlackBlockItem>(MainModel.CurBlackBlockList);
            bbList.Sort(new BlockItemComparer());

            // 按关闭时间由前到后遍历
            for (int i = 0; i < bbList.Count; ++i)
            {
                BlackBlockItem bbItem = bbList[i];

                // 不是关闭状态或者关闭时间迟于设定时间段
                if (bbItem.status != 4 || bbItem.closedDate > reappCond.EndDate)
                {
                    break;
                }
                // 已关闭状态，但无关闭时间或者关闭时间早于设定时间段
                if (bbItem.closedDate <= 10000 || bbItem.closedDate < reappCond.StartDate)
                {
                    continue;
                }
                // 经纬度异常
                if (bbItem.Longitude == 0 || bbItem.Latitude == 0)
                {
                    continue;
                }

                ReappearBlockItem curReItem = getReappearBlockItem(bbList, i, bbItem);

                retList.Add(curReItem);
            }

            // 按黑点ID排序
            retList.Sort();
            foreach (ReappearBlockItem item in retList)
            {
                item.SubItems.Sort();
            }

            return retList;
        }

        private ReappearBlockItem getReappearBlockItem(List<BlackBlockItem> bbList, int i, BlackBlockItem bbItem)
        {
            ReappearBlockItem curReItem = new ReappearBlockItem(bbItem, -1);
            for (int j = i + 1; j < bbList.Count; ++j)
            {
                BlackBlockItem next = bbList[j];

                // 创建时间迟于关闭时间，或者经纬度异常
                if (next.createDate < bbItem.closedDate || next.Longitude == 0 || next.Latitude == 0)
                {
                    continue;
                }
                // 距离条件判断
                double distance = MathFuncs.GetDistance(bbItem.Longitude, bbItem.Latitude, next.Longitude, next.Latitude);
                if (distance > reappCond.Radius)
                {
                    continue;
                }

                curReItem.AddSubItem(next, distance);
            }

            return curReItem;
        }

        /// <summary>
        /// 在地图显示经过处理的黑点
        /// </summary>
        /// <param name="statedList"></param>
        private void FireLayerChanged(List<ReappearBlockItem> statedList)
        {
            MainModel.CurBlackBlockList.Clear();
            Dictionary<int, BlackBlockItem> idItemDic = new Dictionary<int, BlackBlockItem>();
            foreach (ReappearBlockItem item in statedList)
            {
                idItemDic[item.BlackBlockItem.blockId] = item.BlackBlockItem;
                foreach (ReappearBlockItem subItem in item.SubItems)
                {
                    idItemDic[subItem.BlackBlockItem.blockId] = subItem.BlackBlockItem;
                }
            }
            MainModel.CurBlackBlockList.AddRange(idItemDic.Values);
            MainModel.MainForm.GetMapForm().GetLayerBase(typeof(MasterCom.RAMS.Func.BlackBlockOpLayer_GDI));
        }

        private ReappearCondDlg condDlg;
        private ReappearCondition reappCond; 
    }

    /// <summary>
    /// 黑点重现查询条件
    /// </summary>
    public class ReappearCondition
    {
        public NewBlockQueryCond QueryCond { get; set; }
        public double Radius { get; set; }
        public int StartDate { get; set; }
        public int EndDate { get; set; }

        public ReappearCondition()
        {
            QueryCond = new NewBlockQueryCond();
            Radius = 0;
            StartDate = 0;
            EndDate = 0;
        }
    }

    /// <summary>
    /// 黑点重现结果项
    /// </summary>
    public class ReappearBlockItem : IComparable<ReappearBlockItem>
    {
        public BlackBlockItem BlackBlockItem { get; set; }

        public ReappearBlockItem(BlackBlockItem bbItem, double distance)
        {
            BlackBlockItem = bbItem;
            Distance = distance;
            SubItems = new List<ReappearBlockItem>();
        }

        public List<ReappearBlockItem> SubItems
        {
            get;
            private set;
        }

        public double Distance
        {
            get;
            private set;
        }

        public void AddSubItem(BlackBlockItem bbItem, double distance)
        {
            SubItems.Add(new ReappearBlockItem(bbItem, distance));
        }

        public int CompareTo(ReappearBlockItem other)
        {
            if (BlackBlockItem.blockId == other.BlackBlockItem.blockId)
            {
                return 0;
            }
            return BlackBlockItem.blockId < other.BlackBlockItem.blockId ? -1 : 1;
        }
    }

    /// <summary>
    /// 按BlackBlockItem.closeDate从小到大排序
    /// 未有关闭时间则排至最后
    /// </summary>
    public class BlockItemComparer : IComparer<BlackBlockItem>
    {
        public int Compare(BlackBlockItem x, BlackBlockItem y)
        {
            if (x.status != 4)
            {
                return 1;
            }
            if (y.status != 4)
            {
                return -1;
            }
            if (x.closedDate == y.closedDate)
            {
                return 0;
            }
            return x.closedDate < y.closedDate ? -1 : 1;
        }
    }
}
