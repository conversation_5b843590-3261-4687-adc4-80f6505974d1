﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.LteSignalImsi;

namespace MasterCom.RAMS.ZTFunc
{
    public class KpiRoadRenderByRegion : KpiGridRenderByRegion
    {
        public override string Name
        {
            get { return "区域内道路渲染"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11019, this.Name);
        }

        protected override bool getConditionBeforeQuery()
        {
            KpiRoadRenderSetForm setForm = new KpiRoadRenderSetForm();
            if (setForm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            this.shpFiles = setForm.GetShpFiles();

            return base.getConditionBeforeQuery();
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            /*
            KpiRoadRenderPreProcessor preProcessor = new KpiRoadRenderPreProcessor();
            preProcessor.Run(gridMatrix, selectedColorMode, shpFiles);
            List<KpiRoadRenderGridInfo> roadGridsNotMatrix = preProcessor.NotInMatrixGrids;

            KpiRoadRenderLayer layer = MainModel.MainForm.GetMapForm().GetLayerBase(typeof(KpiRoadRenderLayer)) as KpiRoadRenderLayer;
            if (layer == null)
            {
                layer = new KpiRoadRenderLayer();
                MainModel.MainForm.GetMapForm().AddLayerBase(layer);
            }
            layer.SetMatrixAndColor(gridMatrix, roadGridsNotMatrix, selectedColorMode);
             */

            KpiRoadRenderLayer layer = MainModel.MainForm.GetMapForm().GetLayerBase(typeof(KpiRoadRenderLayer)) as KpiRoadRenderLayer;
            if (layer == null)
            {
                layer = new KpiRoadRenderLayer();
                MainModel.MainForm.GetMapForm().AddLayerBase(layer);
            }
            layer.SetMatrixAndColor(gridMatrix, shpFiles, selectedColorMode);

            MainModel.MainForm.GetMapForm().GetGridShowLayer().CurUsingColorMode = this.selectedColorMode;
            MainModel.FireDTDataChanged(MainModel.MainForm);
            MainModel.MainForm.LegendPanel.RefreshLegend();

            this.gridMatrix = null;
        }

        private List<KpiRoadRenderShpFile> shpFiles;
    }
}
