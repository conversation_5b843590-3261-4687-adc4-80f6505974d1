﻿using DevExpress.XtraGrid.Views.Base;
using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Net
{
    public partial class ZTCQTRenderingForm : MinCloseForm
    {
        ZTCqtRenderingLayer layer = null;
        List<ZTCQTRenderingInfo> ztCQTTempInfo = new List<ZTCQTRenderingInfo>();
        MapForm mf = null;
        protected LayerDataInfo lastSelectedData = new LayerDataInfo();
        protected List<LayerDataInfo> infos;

        public ZTCQTRenderingForm()
        {
            InitializeComponent();
        }

        public void FillData(List<ZTCQTRenderingInfo> ztCQTTempInfo)
        {
            mf = MainModel.MainForm.GetMapForm();
            this.ztCQTTempInfo = ztCQTTempInfo;
            infos = new List<LayerDataInfo>();
            foreach (ZTCQTRenderingInfo data in ztCQTTempInfo)
            {
                LayerDataInfo info = new LayerDataInfo();
                info.Longitude = data.Longitude;
                info.Latitude = data.Latitude;
                info.RenderingIndex = data.RSSI;
                info.Selected = data.Selected;
                infos.Add(info);
            }
            //设置图层
            MasterCom.MTGis.LayerBase clayer = mf.GetTempLayerBase(typeof(ZTCqtRenderingLayer));
            layer = clayer as ZTCqtRenderingLayer;
            layer.SerialInfoName = "LTE_TDD:RSRP";
            layer.DataInfos = infos;
            MainModel.FireSetDefaultMapSerialTheme("LTE_TDD:RSRP");
            //填充数据
            gridControlInfo.DataSource = ztCQTTempInfo;
            gridControlInfo.RefreshDataSource();
            
            GotoSelectedView(5000);
        }

        /// <summary>
        /// 图层跳转到数据所在位置
        /// </summary>
        protected virtual void GotoSelectedView(float zoom)
        {
            object imlongitude = gridViewInfo.GetFocusedRowCellValue("Longitude");
            object imlatitude = gridViewInfo.GetFocusedRowCellValue("Latitude");
            if (imlongitude != null && imlatitude != null && imlongitude.ToString() != "" && imlatitude.ToString() != "")
            {
                double fLong = (double)imlongitude;
                double fLat = (double)imlatitude;
                MainModel.MainForm.GetMapForm().GoToView(fLong, fLat, zoom);
            }
        }

        private void gridViewInfo_SelectionChanged(object sender, DevExpress.Data.SelectionChangedEventArgs e)
        {
            //设置选中行
            int focusedhandle = gridViewInfo.FocusedRowHandle;
            GotoSelectedPoint(focusedhandle);
        }

        protected void GotoSelectedPoint(int focusedhandle)
        {
            LayerDataInfo selectedData = infos[focusedhandle];
            lastSelectedData.Selected = false;
            selectedData.Selected = true;
            lastSelectedData = selectedData;
            GotoSelectedView(1000);
        }
    }
}
