﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.NewBlackBlock;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class NBIotCellAcceptFileInfo
    {
        public NBIotCellAcceptFileInfo(FileInfo fileInfo, LTECell cell)
        {
            File = fileInfo;
            NBIOTCell = cell;
            AcceptKpiDic = new Dictionary<uint, object>();
        }
        public int SN { get; set; }

        public int FileId
        {
            get
            {
                if (File != null)
                {
                    return File.ID;
                }
                return 0;
            }
        }

        public string FileName
        {
            get
            {
                if (File != null)
                {
                    return File.Name;
                }
                return "";
            }
        }
        public int CellId
        {
            get
            {
                if (NBIOTCell != null)
                {
                    return NBIOTCell.CellID;
                }
                return 0;
            }
        }
        public string CellName
        {
            get
            {
                if (NBIOTCell != null)
                {
                    return NBIOTCell.Name;
                }
                return "";
            }
        }
        public string BtsName
        {
            get
            {
                if (NBIOTCell != null)
                {
                    return NBIOTCell.BTSName;
                }
                return "";
            }
        }
        public int BtsId
        {
            get
            {
                if (NBIOTCell != null)
                {
                    return NBIOTCell.BelongBTS.BTSID;
                }
                return 0;
            }
        }

        public int TAC
        {
            get
            {
                if (NBIOTCell != null)
                {
                    return NBIOTCell.TAC;
                }
                return 0;
            }
        }
        public int ECI 
        {
            get 
            {
                if (NBIOTCell != null)
                {
                    return NBIOTCell.ECI;
                }
                return 0;
            } 
        }

        public LTECell NBIOTCell { get; set; }

        public FileInfo File { get; set; }

        public int PointCount { get; set; }

        public Dictionary<uint, object> AcceptKpiDic { get; set; }

        public BackgroundResult ConvertToBackgroundResult(int funcId,string strProject)
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.SubFuncID = funcId;
            bgResult.ProjectString = strProject;
            bgResult.FileID = FileId;
            bgResult.SampleCount = PointCount;
            bgResult.FileName = FileName;
            if (File != null)
            {
                bgResult.ISTime = File.BeginTime;
                bgResult.IETime = File.EndTime;
            }
            if (NBIOTCell != null)
            {
                bgResult.StrDesc = NBIOTCell.Type == LTEBTSType.Outdoor ? "室外" : "室内";
                bgResult.BCCH = NBIOTCell.EARFCN;
                bgResult.BSIC = NBIOTCell.PCI;
                bgResult.LongitudeMid = NBIOTCell.Longitude;
                bgResult.LatitudeMid = NBIOTCell.Latitude;
            }
            bgResult.CellType = BackgroundCellType.LTE;
            bgResult.LAC = TAC;
            bgResult.CI = ECI;

            byte[] kpiBytes = KeyValueImageParser.ToImage(AcceptKpiDic);
            bgResult.AddImageValue(kpiBytes);

            return bgResult;
        }
    }
}
