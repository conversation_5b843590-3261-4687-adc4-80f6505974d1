﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.AreaArchiveManage;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class TestAlarmAnaQuery : AreaKpiQueryBase
    {
        public TestAlarmAnaQuery(MainModel mainModel)
            : base(mainModel)
        {
            isQueryEvents = false;
            areaCondition = new AlarmTestCondition();
            anaDealer = new AlarmTestAnaDealer(areaCondition as AlarmTestCondition);
        }

        public override string Name
        {
            get { return "测试预警"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 31000, 31010, this.Name);
        }

        protected override bool setConditionDlg()
        {
            TestAlarmSettingDlg dlg = new TestAlarmSettingDlg();
            dlg.SetCondition((AlarmTestCondition)areaCondition);

            if (dlg.ShowDialog() == DialogResult.OK)
            {
                areaCondition = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override List<string> getFormulas()
        {
            return new List<string> { "Mx_0801", "Tx_0801", "Wx_0801", "Cx_0801", "Ex_0801" };
        }

        protected override void fireShowForm()
        {
            TestAlarmForm form = MainModel.CreateResultForm(typeof(TestAlarmForm)) as TestAlarmForm;
            form.FillData(anaDealer, areaSummaryMap);
            form.Show(MainModel.MainForm);
        }
    }
}
