﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRWeakCoverAnaByFile : NRWeakCoverAnaByRegion
    {
        private static ZTWeakSINRRoadNRQueryByFile instance = null;
        public new static ZTWeakSINRRoadNRQueryByFile GetInstance()
        {
            if (instance == null)
            {
                instance = new ZTWeakSINRRoadNRQueryByFile();
            }
            return instance;
        }

        public override string Name
        {
            get { return "NR弱覆盖分析(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override void InitRegionMop2()
        {
            regionMapDic.Add("当前区域", null);
        }

        protected override string isContainPoint(MTGis.DbPoint dPoint)
        {
            return "当前区域";
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }
    }
}
