﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTGSMCellEmulateCoverAna : QueryBase
    {
        public ZTGSMCellEmulateCoverAna(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override string Name
        {
            get { return "GSM小区覆盖仿真"; }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 19000, 19021, this.Name);
        }
        
        List<List<LongLat>> cellEmulationPointsList = null;
        bool drawEmulationPoints = false;
        //int iPt;
        //int iPr;

        protected override void query()
        {
            cellEmulationPointsList = new List<List<LongLat>>();
            MapOperation2 mapOp2 = new MapOperation2();

            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }

                WaitBox.CanCancel = true;
                WaitBox.Text = "正在查询...";

                MainModel.ClearDTData();
                MapWinGIS.Shape curRegion = MainModel.SearchGeometrys.Region;
                mapOp2.FillPolygon(curRegion);
                WaitBox.ProgressPercent = 70;
                WaitBox.Show("开始绘制小区仿真点...", queryInThread, clientProxy);

                MainModel.CellEmulationPointsList = cellEmulationPointsList;
                MainModel.DrawEmulationPoints = drawEmulationPoints;
                MainModel.FireCellDrawInfoChanged(this);
            }
            catch
            {
                clientProxy.Close();
            }
        }

        private  void queryInThread(object o)
        {
            try
            {
                List<Cell> allGsmCell = CellManager.GetInstance().GetCurrentCells();
                EmulateCovSettingForm settingForm = new EmulateCovSettingForm();

                if (settingForm.ShowDialog() == DialogResult.OK)
                {
                    double covAddDistance = settingForm.getCovDistance();   //最远覆盖距离：用于判断区域以外参与计算的小区
                    covAddDistance = covAddDistance * 0.00001;

                    double xMin = MainModel.SearchGeometrys.Region.Extents.xMin - covAddDistance;
                    double xMax = MainModel.SearchGeometrys.Region.Extents.xMax + covAddDistance;
                    double yMin = MainModel.SearchGeometrys.Region.Extents.yMin - covAddDistance;
                    double yMax = MainModel.SearchGeometrys.Region.Extents.yMax + covAddDistance;

                    drawEmulationPoints = settingForm.getIsDrawPoints();//是否画仿真点
                    //iPr = settingForm.getPr();
                    //iPt = settingForm.getPt();
                    int iTotalNum = allGsmCell.Count;
                    int iCurNum = 0;

                    foreach (Cell cell in allGsmCell)
                    {
                        iCurNum++;
                        WaitBox.ProgressPercent = (int)( 100 * ((float)(iCurNum) / iTotalNum));
                        if (cell.Type != BTSType.Outdoor)
                        {
                            continue;
                        }
                        if (cell.Longitude > xMin && cell.Longitude < xMax && cell.Latitude > yMin && cell.Latitude < yMax)
                        {
                            if (settingForm.getIsIdealCellCov())//ture为理想覆盖模型，false为仿真模型
                            {
                                List<LongLat> longLatList = new List<LongLat>();
                                LongLat ll = new LongLat();
                                ll.fLongitude = (float)(cell.Longitude);
                                ll.fLatitude = (float)(cell.Latitude);
                                longLatList.Add(ll);

                                //覆盖仿真算法一，以打点集画图
                                longLatList.AddRange(getCellEmulateCover(ll, (int)(cell.Direction), cell.Altitude, (int)(cell.Downword)));
                                cellEmulationPointsList.Add(longLatList);
                            }
                            else
                            {
                                //覆盖仿真算法二，以椭圆画图
                                List<LongLat> longLatList = calcCellCoverDistance(cell);
                                cellEmulationPointsList.Add(longLatList);
                            }
                        }
                    }
                }
            }
            finally
            {
                WaitBox.Close();
            }
        }

        /// <summary>
        /// 小区简单覆盖仿真(工参)
        /// </summary>
        private List<LongLat> getCellEmulateCover(LongLat btsLongLat, int iangle_dir, int ialtitude, int iangle_ob)
        {
            List<LongLat> cellEmulateList = new List<LongLat>();
            float coverDistance = 0;
            if (iangle_ob > 0 && iangle_ob < 90)
            {
                coverDistance = (float)(ialtitude / Math.Tan(iangle_ob * Math.PI / 180));
            }
            if (coverDistance < 600)
                coverDistance = 600;
            else if (coverDistance > 3000)
                coverDistance = 3000;

            int sDir = iangle_dir - 80;
            int eDir = iangle_dir + 80;
            for (int i = sDir; i <= eDir; i += 5)
            {
                LongLat tLongLat = calcPointX(i, coverDistance, btsLongLat);
                cellEmulateList.Add(tLongLat);
            }
            return cellEmulateList;
        }

        /// <summary>
        /// 小区传播覆盖仿真
        /// </summary>
        private List<LongLat> calcCellCoverDistance(Cell cell)
        {
            List<LongLat> ellipsoidList = new List<LongLat>();
            List<LongLat> ellipsoidTmpList = new List<LongLat>();
            float fDistance = 0;
            float fCellDist = (float)(MasterCom.ES.Data.CfgDataProvider.CalculateRadius(cell, 3, true));
            //float fFasthestDist = 1000 * calcCellFasthestCoverDistance(iFreq, iPt, iPr, cell.Altitude, cell.Downword, fCellDist);//公式有误，暂时屏蔽该算法
            fDistance = fCellDist;

            LongLat llOrg = new LongLat();
            llOrg.fLongitude = (float)cell.Longitude;
            llOrg.fLatitude = (float)cell.Latitude;
            float bTmpDist = (float)(fDistance * 0.00001);

            float aTmpDist = bTmpDist / 10;
            for (int i = 2; i <= 9; i++)
            {
                LongLat tmpll = getEllipsoidLeft(llOrg, bTmpDist / 2, bTmpDist / 2, aTmpDist * i);
                ellipsoidTmpList.Add(tmpll);
            }

            LongLat llOther = new LongLat();
            llOther.fLongitude = llOrg.fLongitude;
            llOther.fLatitude = llOrg.fLatitude + bTmpDist;
            ellipsoidTmpList.Add(llOther);

            for (int i = 9; i >= 2; i--)
            {
                LongLat tmpll = getEllipsoidRight(llOrg, bTmpDist / 2, bTmpDist / 2, aTmpDist * i);
                ellipsoidTmpList.Add(tmpll);
            }

            foreach (LongLat ll in ellipsoidTmpList)
            {
                float fDist = (float)(MathFuncs.GetDistance(llOrg.fLongitude, llOrg.fLatitude, ll.fLongitude, ll.fLatitude));
                int iAngle = calcSampleAngle(cell, ll.fLongitude, ll.fLatitude, 0);
                LongLat newLl = calcPointX(iAngle, fDist, llOrg);
                ellipsoidList.Add(newLl);
            }
            ellipsoidList.Add(llOrg);

            return ellipsoidList;
        }

        /// <summary>
        /// 以基站经纬度正北方向为正，绘制椭圆
        /// </summary>
        /// <param name="llOrg">基站经纬度</param>
        /// <param name="aDist">椭圆长A</param>
        /// <param name="bDist">椭圆宽B</param>
        /// <param name="yDist">椭圆长y</param>
        private LongLat getEllipsoidLeft(LongLat llOrg, float aDist, float bDist, float yDist)
        {
            float xTmpDist = (float)(aDist * Math.Sqrt(1 - Math.Pow((yDist - bDist) / bDist, 2)));//椭圆宽x
            LongLat ll = new LongLat();
            ll.fLongitude = llOrg.fLongitude - xTmpDist;
            ll.fLatitude = llOrg.fLatitude + yDist;
            return ll;
        }

        /// <summary>
        /// 以基站经纬度正北方向为正，绘制椭圆
        /// </summary>
        /// <param name="llOrg">基站经纬度</param>
        /// <param name="aDist">椭圆长A</param>
        /// <param name="bDist">椭圆宽B</param>
        /// <param name="yDist">椭圆长y</param>
        private LongLat getEllipsoidRight(LongLat llOrg, float aDist, float bDist, float yDist)
        {
            float xTmpDist = (float)(aDist * Math.Sqrt(1 - Math.Pow((yDist - bDist) / bDist, 2)));//椭圆宽x
            LongLat ll = new LongLat();
            ll.fLongitude = llOrg.fLongitude + xTmpDist;
            ll.fLatitude = llOrg.fLatitude + yDist;
            return ll;
        }

        /**
        /// <summary>
        /// 计算小区Cost231-Hata模型覆盖半径
        /// </summary>
        /// <param name="iFreq">小区频点</param>
        /// <param name="iPt">发射信号功率</param>
        /// <param name="iPr">接收信号功率</param>
        /// <param name="ialtitude">挂高</param>
        /// <param name="iangle_ob">下倾角</param>
        /// <param name="fSiteDistance">小区理想覆盖半径</param>
        private float calcCellFasthestCoverDistance(int iFreq, int iPt, int iPr, int ialtitude, int iangle_ob, float fSiteDistance)
        {
            float cValue = 0;
            if (fSiteDistance < 1000)
                cValue = 3;

            if (ialtitude < 10)
                ialtitude = 10;

            float fFreq = 0;
            if (iFreq < 2000)
                fFreq = getGsmDlFreq(iFreq);
            else
                fFreq = getTdDlFreq(iFreq);

            double fDiffW = iPt - iPr;
            float fValue = (float)Math.Pow(10, ((fDiffW - 46.33 - 33.9 * Math.Log(fFreq,10) + 13.82 * Math.Log(ialtitude,10) - cValue) / (44.9 - 6.55 * Math.Log(ialtitude,10))));//公式有误，暂时屏蔽该算法
            fValue = (float)(fValue * Math.Cos(iangle_ob * Math.PI / 180));
            return fValue;
        }

        /// <summary>
        /// GSM频点换算成下行波长
        /// </summary>
        private float getGsmDlFreq(int iFreq)
        {
            float fFreq = 0;
            if (iFreq < 500)
                fFreq = 935 + (float)0.2 * iFreq;
            else
                fFreq = 1805 + (float)0.2 * (iFreq - 511);
            return fFreq;
        }

        /// <summary>
        /// TD频点换算成下行波长
        /// </summary>
        private float getTdDlFreq(int iFreq)
        {
            return ((float)iFreq) / 5;
        }
        */

        /// <summary>
        /// 粗略定位另一点信息
        /// </summary>
        /// <param name="iangle">夹角</param>
        public LongLat calcPointX(int iangle, float idis, LongLat s)
        {
            LongLat e = new LongLat();
            double a = Math.Cos((90 - iangle) * 2 * Math.PI / 360);
            double b = Math.Sin((90 - iangle) * 2 * Math.PI / 360);
            e.fLongitude = s.fLongitude + (float)(a * (idis / 40075360) * 360);//32507969.15
            e.fLatitude = s.fLatitude + (float)(b * (idis / 39940670) * 360); //40172187.93
            return e;
        }

        /// <summary>
        /// 获取采样点与小区的夹角
        /// </summary>
        /// <param name="itype">0为相对夹角，1为相对夹角(绝对值)</param>
        private int calcSampleAngle(Cell cell, double longitude, double latitude, int itype)
        {
            //所有角度按正北方向算起始，顺时针算夹角，正北为0度
            double angleDiff = 0;
            double distance = cell.GetDistance(longitude, latitude);

            double angle;
            double ygap = cell.GetDistance(cell.Longitude, latitude);
            double angleV = Math.Acos(ygap / distance);
            if (longitude >= cell.Longitude && latitude >= cell.Latitude)//1象限
            {
                angle = angleV * 180 / Math.PI;
            }
            else if (longitude <= cell.Longitude && latitude >= cell.Latitude)//2象限
            {
                angle = 360 - angleV * 180 / Math.PI;
            }
            else if (longitude <= cell.Longitude && latitude <= cell.Latitude)//3象限
            {
                angle = 180 + angleV * 180 / Math.PI;
            }
            else//4象限
            {
                angle = 180 - angleV * 180 / Math.PI;
            }

            if (itype == 0)
            {
                angleDiff = angle - cell.Direction;
                if (angleDiff < 0)
                {
                    angleDiff = 360 + angleDiff;
                }
            }
            else
            {
                angleDiff = Math.Abs(angle - cell.Direction);
                if (angleDiff > 180)
                {
                    angleDiff = 360 - angleDiff;
                }
            }

            return (int)angleDiff;
        }
    }

    public class LongLat
    {
        public float fLongitude { get; set; }
        public float fLatitude { get; set; }

        public LongLat()
        {
            fLongitude = 0;
            fLatitude = 0;
        }

        public LongLat(float longitude,float latitude)
        {
            fLongitude = longitude;
            fLatitude = latitude;
        }
    }

    public class CellLobe
    {
        public CellLobe()
        {
            basePoint = new LongLat();
            backPoint = new LongLat();
            aPoint = new LongLat();
            bPoint = new LongLat();
        }

        /// <summary>
        /// 椭圆长度
        /// </summary>
        public float fLength { get; set; }
        /// <summary>
        /// 椭圆宽度
        /// </summary>
        public float fWidth { get; set; }
        /// <summary>
        /// 椭圆原点
        /// </summary>
        public LongLat basePoint { get; set; }
        /// <summary>
        /// 椭圆方向角，以椭圆的长指向方向定为0度，角度与小区方向角一致
        /// </summary>
        public float direction { get; set; }
        /// <summary>
        /// 椭圆对端顶点
        /// </summary>
        public LongLat backPoint { get; set; }
        /// <summary>
        /// 三角形A点
        /// </summary>
        public LongLat aPoint { get; set; }
        /// <summary>
        /// 三角形B点
        /// </summary>
        public LongLat bPoint { get; set; }
    }
}
