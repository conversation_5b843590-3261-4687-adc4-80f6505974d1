﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class CellWorkParam
    {
        public string DistrictName { get; set; }
        public string CellName { get; set; }
        public int CellId { get; set; }
        public int SectorID { get; set; }
        public int Direction { get; set; }
        public string BtsName { get; set; }
        public bool IsOutDoor { get; set; }
        public string CoverType
        {
            get
            {
                return IsOutDoor ? "室外" : "室内";
            }
        }
        public int ENodeBID { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public string Address { get; set; }
        public int Tac { get; set; }
        public int Eci
        {
            get
            {
                return ENodeBID * 256 + CellId;
            }
        }
        public int ARFCN { get; set; }
        public int PCI { get; set; }
        public DateTime AcceptDateTime { get; set; }
    }
}
