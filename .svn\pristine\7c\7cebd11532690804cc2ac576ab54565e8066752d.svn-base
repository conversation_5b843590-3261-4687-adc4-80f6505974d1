﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Data.SqlClient;
using GMap.NET.Projections;
using GMap.NET;
using System.IO;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Util;
using MasterCom.RAMS.Grid;
using MasterCom.MTGis;
using MasterCom.RAMS.src.MasterCom.RAMS.ZTFunc.Save4Use;
using MasterCom.MControls;
using MasterCom.Util;

namespace MasterCom.RAMS.Func
{
    public partial class Save4UseMainForm : Form
    {
        static readonly object padlock = new object();
        bool started = false;
        SqlCommand cmdUpdate;
        int curCityID = -1;
        MainModel mainModel;
        private String ConnectionString = "";//@"server=192.168.1.21;User Id=dtauser;Persist Security Info=True;database=MTNOH_APP_ANA;password=dtauser;"
        public Save4UseMainForm(MainModel mainModel)
        {
            InitializeComponent();
            this.mainModel = mainModel;
        }
        public void freshCurrentInfo()
        {
            String filePath = Application.StartupPath + @"\config\save4use.config";
            try
            {
                String conStr = File.ReadAllText(filePath);
                ConnectionString = conStr;
            }
            catch {
                tbxLog.Text += @"Open failed: config\save4use.config";
                return;
            }
            MapGridLayer gridLayer = mainModel.MainForm.GetMapForm().GetGridShowLayer();
            String name = gridLayer.CurUsingColorMode.Name;
            tbxName.Text = name;
        }
       
       
        private void bgWorker_DoWork(object sender, DoWorkEventArgs e)
        {
            lock (padlock)
            {
                if (started)
                {
                    return;
                }
                else
                {
                    started = true;
                }
                reportInfo("run start");
                if (bgWorker.CancellationPending)
                {
                    started = true;
                }
                if (colorMatrix == null || colorMatrix.Grids.Count==0)
                {
                    reportInfo("matrix为空，尚未查询栅格结果！");
                    return;
                }
                if (curCityID <= 0)
                {
                    reportInfo("地市未知！");
                    return;
                }
                SqlConnection connection = new SqlConnection(ConnectionString);
                try
                {
                    if (connection.State == ConnectionState.Closed)
                    {
                        connection.Open();
                    }
                    //=====
                    int xid = -1;
                    string sql = @"select id from [MTNOH_APP_ANA].[dbo].[TB_DT_Grid_Index] where type='" + curTypeStr + "' and cityid=" + curCityID + " and name='" + curTaskname + "'";
                    object rObj = SqlHelper.ExecuteScalar(connection, CommandType.Text, sql);
                    if (rObj != null)
                    {
                        xid = (int)rObj;
                        reportInfo("发现重复，弹出对话框询问..");
                        if (MessageBox.Show("已经存在，是否替换？", "发现重复", MessageBoxButtons.OKCancel) != DialogResult.OK)
                        {
                            reportInfo("选择终止。");
                            return;
                        }
                        sql = @"delete from [MTNOH_APP_ANA].[dbo].TB_DT_Grid_TileLayer where id=" + xid;
                        SqlHelper.ExecuteNonQuery(connection, CommandType.Text, sql);
                    }
                    else
                    {
                        sql = @"insert into [MTNOH_APP_ANA].[dbo].[TB_DT_Grid_Index] select isnull(max(id),0)+1,'" + curTypeStr + "'," + curCityID + ",'" + curTaskname + "','' from [MTNOH_APP_ANA].[dbo].[TB_DT_Grid_Index]";
                        SqlHelper.ExecuteNonQuery(connection, CommandType.Text, sql);
                        sql = @"select id from [MTNOH_APP_ANA].[dbo].[TB_DT_Grid_Index] where type='" + curTypeStr + "' and cityid=" + curCityID + " and name='" + curTaskname + "'";
                        rObj = SqlHelper.ExecuteScalar(connection, CommandType.Text, sql);
                        xid = (int)rObj;
                    }
                    //更新图例
                    sql = @"delete from  [MTNOH_APP_ANA].[dbo].[TB_DT_Grid_Legend] where id =" + xid;
                    try
                    {
                        SqlHelper.ExecuteNonQuery(connection, CommandType.Text, sql);
                    }
                    catch
                    {
                        reportInfo("ERROR：无[TB_DT_Grid_Legend]表？");
                        return;
                    }
                    insertColorRanges(connection, xid);
                    //==
                    //=======
                    dealLevelData(connection, xid);
                }
                catch (Exception eexx)
                {
                    reportInfo("错误：" + eexx.Message);
                    return;
                }
                finally
                {
                    if (connection != null)
                    {
                        connection.Close();
                    }
                }
                reportInfo("成功！");
            }
        }

        private void insertColorRanges(SqlConnection connection, int xid)
        {
            List<ColorRange> colorRanges = curUsingColorMode.colorRanges;
            for (int r = 0; r < colorRanges.Count; r++)
            {
                ColorRange cr = colorRanges[r];
                String caption = "[" + cr.minValue + "," + cr.maxValue + ((r < colorRanges.Count - 1) ? ")" : "]");
                String desc = "";
                if (cr.desInfo != null)
                {
                    desc = cr.desInfo;
                }
                string sql = @"insert into [MTNOH_APP_ANA].[dbo].[TB_DT_Grid_Legend] values(" + xid + ",'" + caption + "'," + cr.color.ToArgb() + ",'" + desc + "')";
                SqlHelper.ExecuteNonQuery(connection, CommandType.Text, sql);
            }
        }

        private void dealLevelData(SqlConnection connection, int xid)
        {
            for (int level = 17; level >= 4; level--)
            {
                if (level >= 14 && level <= 16)//14-17层，用17级的
                {
                    continue;
                }
                int powFact = level == 17 ? 1 : (int)Math.Pow(2, 14 - level);
                GridMatrix<ColorUnit> nxColorMatrix = GridMatrix<ColorUnit>.RefactorMatrixBySize(powFact, colorMatrix);
                foreach (ColorUnit cu in nxColorMatrix)
                {
                    curUsingColorMode.CalcColor(cu);
                }
                ColorUnit grid = nxColorMatrix.Grids[0];
                double lngSpan = grid.LngSpan;
                double latSpan = grid.LatSpan;

                int eachGridN = 20;
                double eachGapX = lngSpan * eachGridN;
                double eachGapY = latSpan * eachGridN;
                DbRect rect = nxColorMatrix.GetBounds();

                double longiMin = (int)(Math.Floor(rect.x1 / eachGapX)) * eachGapX;
                double longiMax = (int)(Math.Ceiling(rect.x2 / eachGapX)) * eachGapX;
                double latiMin = (int)(Math.Floor(rect.y1 / eachGapY)) * eachGapY;
                double latiMax = (int)(Math.Ceiling(rect.y2 / eachGapY)) * eachGapY;

                this.cmdUpdate = new SqlCommand("insert into MTNOH_APP_ANA.dbo.[TB_DT_Grid_TileLayer] values(" + xid + ",@ltLong,@ltLat,@brLong,@brLat,@zoom,@tile)", connection);
                this.cmdUpdate.Parameters.Add("@ltLong", System.Data.SqlDbType.Int);
                this.cmdUpdate.Parameters.Add("@ltLat", System.Data.SqlDbType.Int);
                this.cmdUpdate.Parameters.Add("@brLong", System.Data.SqlDbType.Int);
                this.cmdUpdate.Parameters.Add("@brLat", System.Data.SqlDbType.Int);
                this.cmdUpdate.Parameters.Add("@zoom", System.Data.SqlDbType.Int);
                this.cmdUpdate.Parameters.Add("@tile", System.Data.SqlDbType.Image);

                for (double x = longiMin; x <= longiMax; x += eachGapX)
                {
                    for (double y = latiMin; y <= latiMax; y += eachGapY)
                    {
                        insertValidLevelData(level, nxColorMatrix, lngSpan, latSpan, eachGridN, x, y);
                    }
                }
            }
        }

        private void insertValidLevelData(int level, GridMatrix<ColorUnit> nxColorMatrix, double lngSpan, double latSpan, int eachGridN, double x, double y)
        {
            int gridSize = 4;
            int columnCount = eachGridN * gridSize;
            int rowCount = eachGridN * gridSize;
            Bitmap img = new Bitmap(columnCount, rowCount, System.Drawing.Imaging.PixelFormat.Format32bppArgb);
            Graphics graphics = Graphics.FromImage(img);

            int innerRoundStartX = (int)Math.Round(x / lngSpan);
            int innerRoundStartY = (int)Math.Round(y / latSpan);
            bool hasValidData = false;
            for (int vx = 0; vx < eachGridN; vx++)
            {
                for (int vy = 0; vy < eachGridN; vy++)
                {
                    ColorUnit cu = nxColorMatrix[innerRoundStartY + vy, innerRoundStartX + vx];
                    if (cu != null)
                    {
                        int cy = vx * 4;
                        int rx = (eachGridN - vy - 1) * 4;
                        Color clr = cu.color;
                        graphics.FillRectangle(new SolidBrush(clr), cy, rx, gridSize, gridSize);
                        hasValidData = true;
                    }
                }
            }
            //==one tile page
            if (hasValidData)
            {
                cmdUpdate.Parameters["@ltLong"].Value = x * 10000000;
                cmdUpdate.Parameters["@ltLat"].Value = (y + eachGridN * latSpan) * 10000000;
                cmdUpdate.Parameters["@brLong"].Value = (x + eachGridN * lngSpan) * 10000000;
                cmdUpdate.Parameters["@brLat"].Value = y * 10000000;
                cmdUpdate.Parameters["@zoom"].Value = level;
                MemoryStream tempStream = new MemoryStream();
                img.Save(tempStream, System.Drawing.Imaging.ImageFormat.Png);
                cmdUpdate.Parameters["@tile"].Value = tempStream.GetBuffer();
                cmdUpdate.ExecuteNonQuery();
            }
        }

        public void reportInfo(string s)
        {
            bgWorker.ReportProgress(30, s);
        }
        private void bgWorker_ProgressChanged(object sender, ProgressChangedEventArgs e)
        {
            if (e.ProgressPercentage == 99)
            {
                //lbCurCount.Text = e.UserState.ToString()
            }
            if (e.ProgressPercentage == 30 && e.UserState.ToString().Length > 0)
            {
                if (tbxLog.Text.Length > 10240)
                {
                    tbxLog.Text = "";//清空
                }
                else
                {
                    tbxLog.AppendText("\r\n");
                    tbxLog.AppendText(e.UserState.ToString());
                }
            }
        }

        private void bgWorker_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            if (started)
            {
                btnStartDo.Enabled = true;
                tbxLog.AppendText("\r\nok!");
                started = false;
            }
        }

        private void btnStartDo_Click(object sender, EventArgs e)
        {
            doFireStartWork();
        }
        private GridColorModeItem curUsingColorMode = null;
        private GridMatrix<ColorUnit> colorMatrix = null;
        private String curTaskname = "";
        private String curTypeStr = "";
        private void doFireStartWork()
        {
            lock (padlock)
            {
                if (!started)
                {
                    btnStartDo.Enabled = false;
                    if (!bgWorker.IsBusy)
                    {
                        MapGridLayer gridLayer = mainModel.MainForm.GetMapForm().GetGridShowLayer();
                        colorMatrix = gridLayer.ColorMatrix;
                        curUsingColorMode = gridLayer.CurUsingColorMode;
                        curCityID = mainModel.DistrictID;
                        bool isvalid = judgeValid();
                        if (!isvalid)
                        {
                            return;
                        }
                        if (DialogResult.OK == MessageBox.Show(this, "保存到Tile...名称：" + curTaskname + "，网络类型：" + curTypeStr + "？", "保存", MessageBoxButtons.OKCancel))
                        {
                            bgWorker.RunWorkerAsync();
                        }
                        else
                        {
                            doNothing("取消。");
                        }
                    }
                }
            }
        }

        private bool judgeValid()
        {
            if (curCityID <= 0)
            {
                doNothing("地市未知！");
                return false;
            }
            if (curUsingColorMode == null)
            {
                doNothing("未找到着色模式！");
                return false;
            }
            if (cbxType.SelectedIndex == -1)
            {
                doNothing("请选择网络类型！");
                return false;
            }
            curTypeStr = (String)cbxType.SelectedItem;
            curTaskname = tbxName.Text.Trim();
            if (curTaskname == "" || curTaskname.IndexOf("'") != -1)
            {
                doNothing("输入名称非法：" + curTaskname);
                return false;
            }
            return true;
        }

        private void doNothing(string p)
        {
            btnStartDo.Enabled = true;
            tbxLog.AppendText("\r\n" + p);
            started = false;
        }

        private void Save4UseMainForm_Load(object sender, EventArgs e)
        {
            //
        }

        private void btnSetting_Click(object sender, EventArgs e)
        {
            Save4UseMngForm mng = new Save4UseMngForm(this.ConnectionString);
            mng.ShowDialog();
        }

        private void Save4UseMainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                e.Cancel = true;
                this.Visible = false;
            }
        }
       
     
    }
    public class ToDoTask
    {
        public int zoom { get; set; }
        public int x { get; set; }
        public int y { get; set; }
    }
    public class GatherUnit
    {
        public List<Double> dataList { get; set; } = new List<double>();
        public double getMeanValue()
        {
            double sum = 0;
            foreach (double db in dataList)
            {
                sum += db;
            }
            return sum / dataList.Count;
        }
    }
}
