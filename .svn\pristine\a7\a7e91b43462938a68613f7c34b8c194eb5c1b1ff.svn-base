<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="buttonEditVillage.Properties.Buttons" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        R0lGODlhEAAQAIcAAAEAAAAAAACAAICAAAAAgIAAgACAgMDAwMDcwKbK8D4AAF0AAHwAAJsAALoAANkA
        APAAAP8kJP9ISP9sbP+QkP+0tD4UAF0eAHwoAJsyALo8ANlGAPBVAP9tJP+FSP+dbP+1kP/NtD4qAF0/
        AHxUAJtpALp+ANmTAPCqAP+2JP/CSP/ObP/akP/mtD4+AF1dAHx8AJubALq6ANnZAPDwAP//JP//SP//
        bP//kP//tCo+AD9dAFR8AGmbAH66AJPZAKrwALb/JML/SM7/bNr/kOb/tBQ+AB5dACh8ADKbADy6AEbZ
        AFXwAG3/JIX/SJ3/bLX/kM3/tAA+AABdAAB8AACbAAC6AADZAADwACT/JEj/SGz/bJD/kLT/tAA+FABd
        HgB8KACbMgC6PADZRgDwVST/bUj/hWz/nZD/tbT/zQA+KgBdPwB8VACbaQC6fgDZkwDwqiT/tkj/wmz/
        zpD/2rT/5gA+PgBdXQB8fACbmwC6ugDZ2QDw8CT//0j//2z//5D//7T//wAqPgA/XQBUfABpmwB+ugCT
        2QCq8CS2/0jC/2zO/5Da/7Tm/wAUPgAeXQAofAAymwA8ugBG2QBV8CRt/0iF/2yd/5C1/7TN/wAAPgAA
        XQAAfAAAmwAAugAA2QAA8CQk/0hI/2xs/5CQ/7S0/xQAPh4AXSgAfDIAmzwAukYA2VUA8G0k/4VI/51s
        /7WQ/820/yoAPj8AXVQAfGkAm34AupMA2aoA8LYk/8JI/85s/9qQ/+a0/z4APl0AXXwAfJsAm7oAutkA
        2fAA8P8k//9I//9s//+Q//+0/z4AKl0AP3wAVJsAaboAftkAk/AAqv8ktv9Iwv9szv+Q2v+05j4AFF0A
        HnwAKJsAMroAPNkARvAAVf8kbf9Ihf9snf+Qtf+0zQYGBhISEh8fHywsLDk5OUVFRVJSUl9fX2xsbHh4
        eIWFhZKSkp+fn6urq7i4uMXFxdLS0t7e3uvr6/j4+P/78KCgpICAgP8AAAD/AP//AAAA//8A/wD/////
        /yH/C05FVFNDQVBFMi4wAwEBAAAh+QQBAAAAACwAAAAAEAAQAAAIiQABCBxIsKDBgwjVmQtgTh1Cgesa
        1aPXqNG6hBL/1avIyKFBc/XmSUh3YB0jcwcD/JNwQ4JERgFSsrzR8t/Jg+hY6mzULt3BfOta0mxHjsQI
        g/n2pVtoLp2IEyxIEEy6bx/VfedImIg68GrVqvnQaV0hVWC+s2jzCRRrYsXRh2tJlHgLVyCJcwEBADs=
</value>
  </data>
  <metadata name="contextMenuStripTreeList.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>224, 17</value>
  </metadata>
  <metadata name="toolStripDropDownVillage.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
</root>