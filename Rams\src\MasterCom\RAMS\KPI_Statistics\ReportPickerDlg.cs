﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat;
using DevExpress.XtraEditors.Controls;

namespace MasterCom.RAMS.KPI_Statistics
{
    public partial class ReportPickerDlg : BaseDialog
    {
        public ReportPickerDlg()
        {
            InitializeComponent();
            DisplayCellFilter(false);  //不显示筛选小区相关控件
            reportFilter.initReportPicker();
        }

        private void lnkReloadRpt_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            KPIReportManager.Instance.Init();
            reportFilter.initReportPicker();
        }

        public ReportStyle Report
        {
            get
            {
                ReportStyle rpt = reportFilter.SelectedReport;
                return rpt;
            }
        }

        public bool DisplayRounds
        {
            get { return pnlRound.Visible; }
            set {
                pnlRound.Visible = value;
                if (value && chkCbxRound.Properties.Items.Count == 0)
                {
                    for (int i = 1; i < 31; i++)
                    {
                        chkCbxRound.Properties.Items.Add(i, string.Format("第{0}轮", i));
                    }
                }
            }
        }
        public void DisplayCellFilter(bool displayCellFilter)
        {
            gpbCellFilter.Visible = displayCellFilter;
            chkSelectFilter.Visible = displayCellFilter;
            if (displayCellFilter)
            {
                gpbCellFilter.Location = new Point(9, 87);
                btnOK.Location = new System.Drawing.Point(382, 113);
                btnCancel.Location = new System.Drawing.Point(475, 113);
                ClientSize = new System.Drawing.Size(574, 210);
            }
            else
            {
                btnOK.Location = new System.Drawing.Point(382, 227);
                btnCancel.Location = new System.Drawing.Point(475, 227);
                ClientSize = new System.Drawing.Size(574, 150);
            }
        }

        public List<int> Rounds
        {
            get
            {
                List<int> rounds = new List<int>();
                foreach (CheckedListBoxItem item in chkCbxRound.Properties.Items)
                {
                    if (item.CheckState == CheckState.Checked)
                    {
                        rounds.Add((int)item.Value);
                    }
                }
                return rounds;
            }
        }

        public bool IsQueryAllParams
        {
            get { return chkAllParam.Checked; }
        }
        public void SetIsQueryByTimePeriod()
        {
            chkTime.Visible=true;
        }
        public bool IsQueryByTimePeriod
        {
            get
            {
                return chkTime.Checked;
            }
        }
        public bool IsSelectFile
        {
            get { return chkSelectFilter.Checked; }
        }
        private void chkSelectFilter_Click(object sender, EventArgs e)
        {
            if (chkSelectFilter.CheckState == CheckState.Checked)
            {
                txtFileNames.Enabled = true;
                btnFileSelect.Enabled = true;
            }
            if (chkSelectFilter.CheckState == CheckState.Unchecked)
            {
                txtFileNames.Enabled = false;
                btnFileSelect.Enabled = false;
            }
        }
        public string[] FilePath { get; set; }
        public string[] ExcelFilePath { get; set; }
        public List<string> Excellist { get; set; }
        //选择文件
        private void btnFileSelect_Click(object sender, EventArgs e)
        {
            Excellist = new List<string>();
            try
            {
                OpenFileDialog ofd = new OpenFileDialog();
                ofd.Multiselect = true;
                ofd.Filter = "Excel Files(*.xlsx)|*.xlsx|Excel Files(*.xls)|*.xls|AllFiles(*.*)|*.*";
                if (ofd.ShowDialog() == DialogResult.OK)
                {
                    FilePath = ofd.FileNames;
                }
                else
                {
                    return;
                }
                StringBuilder strtext = new StringBuilder();
                if (FilePath == null) { return; }
                foreach (var path in FilePath)
                {
                    string strpath = path.ToString();
                    if (strpath.Contains("xlsx")||strpath.Contains("xls"))
                    { Excellist.Add(strpath); }
                    strtext.Append(strpath);
                }
                txtFileNames.Text = strtext.ToString();
                if (Excellist != null)
                {
                    ExcelFilePath = Excellist.ToArray(); 
                }
            }
            catch
            {
                //continue
            }
        }
        private void btnOK_Click(object sender, EventArgs e)
        {
            ReportStyle rpt = reportFilter.SelectedReport;
            if (rpt == null)
            {
                MessageBox.Show("请选择报表！");
                return;
            }
            object item = chkCbxRound.Properties.GetCheckedItems();
            if (DisplayRounds && (item == null || item.ToString() == string.Empty))
            {
                MessageBox.Show("请至少选择一轮测试");
                return;
            }
            DialogResult = DialogResult.OK;
        }



    }
}
