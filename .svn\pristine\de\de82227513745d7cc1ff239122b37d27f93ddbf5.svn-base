﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRCellCoverTypeAnaDlg : BaseDialog
    {
        public NRCellCoverTypeAnaDlg() : base()
        {
            InitializeComponent();
        }

        public void GetCondition(NRCellCoverTypeAnaCond cond)
        {
            cond.MaxDirection = (int)numDirection.Value;
        }

        public void SetCondition(NRCellCoverTypeAnaCond cond)
        {
            numDirection.Value = cond.MaxDirection;
        }
    }

    public class NRCellCoverTypeAnaCond
    {
        public int MaxDirection { get; set; } = 30;
    }
}
