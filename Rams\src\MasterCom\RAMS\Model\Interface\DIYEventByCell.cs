﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;

namespace MasterCom.RAMS.Net
{
    public class DIYEventByCell : DIYEventQuery
    {
        public DIYEventByCell(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get { return "事件查询(按小区)"; }
        }
        public override string IconName
        {
            get { return "Images/cellevt.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11007, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Cell;
        }

        protected override void prepareOtherEventFilter(Package package)
        {
            AddDIYRegion_Sample(package);
        }

        protected override void queryPeriodInfo(ClientProxy clientProxy, Package package, TimePeriod period, bool byround)
        {
            List<CellQueryCondUnit> cellLacCiChangedPeriods = parseChangeLACCIPeriodsOfCell(period);
            foreach (CellQueryCondUnit cond in cellLacCiChangedPeriods)
            {
                prepareStatPackage_Event_FileFilter_OnlyCell(package, cond, byround);
                prepareStatPackage_Event_EventFilter_OnlyCell(package,cond);
                fillContentNeeded_Event(package);
                clientProxy.Send();
                recieveInfo_Event(clientProxy);
            }
            
        }

        private void prepareStatPackage_Event_FileFilter_OnlyCell(Package package, CellQueryCondUnit cond, bool byround)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_DIY_AREA_EVENT;
            package.Content.PrepareAddParam();
            if (byround)
            {
                AddDIYRound(package, condition.ByRoundYear, condition.ByRoundRound);
            }
            else
            {
                AddDIYPeriod(package, cond.period);
            }
            AddDIYRegionOfCell(package,cond);
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYCarrierType(package, condition.CarrierTypes);
            AddDIYFileFilter(package, condition);

            AddDIYStatStatus(package);
            AddDIYAreaTypeAndID(package, condition.Areas);
            AddDIYMomt(package, condition.Momt);
            //
            AddDIYEndOpFlag(package);
        }
        protected void AddDIYRegionOfCell(Package package, CellQueryCondUnit cond)
        {
            package.Content.AddParam((byte)OpOptionDef.AreaSelectIntersect);
            package.Content.AddParam(cond.ltLongitude);
            package.Content.AddParam(cond.ltLatitude);
            package.Content.AddParam(cond.brLongitude);
            package.Content.AddParam(cond.brLatitude);
        }
        private void prepareStatPackage_Event_EventFilter_OnlyCell(Package package, CellQueryCondUnit cond)
        {
            //指定事件
            if (Condition.EventIDs.Count > 0 && Condition.EventIDs[0] != -1)
            {
                package.Content.AddParam((byte)OpOptionDef.InSelect);
                package.Content.AddParam("0,7,43");
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < Condition.EventIDs.Count; i++)
                {
                    int id = Condition.EventIDs[i];
                    sb.Append(id);
                    if (i < Condition.EventIDs.Count - 1)
                    {
                        sb.Append(",");
                    }
                }
                package.Content.AddParam(sb.ToString());
            }
            //指定经纬度范围
            package.Content.AddParam((byte)OpOptionDef.AreaSelectSample);
            package.Content.AddParam(cond.ltLongitude);
            package.Content.AddParam(cond.ltLatitude);
            package.Content.AddParam(cond.brLongitude);
            package.Content.AddParam(cond.brLatitude);
            //指定LAC,CI
            package.Content.AddParam((byte)OpOptionDef.CellSelect);
            package.Content.AddParam(cond.LAC);
            package.Content.AddParam(cond.CI);

            AddDIYEndOpFlag(package);
        }
        private List<CellQueryCondUnit> parseChangeLACCIPeriodsOfCell(TimePeriod period)
        {
            List<CellQueryCondUnit> condPeriodList = new List<CellQueryCondUnit>();
            int meter = 5000;
            double ltlong = 0, ltlat = 0, brlong = 0, brlat = 0;
            if (condition.Geometorys.SelectedCell != null)
            {
                parseRectByLongLatDistance(condition.Geometorys.SelectedCell.Longitude, condition.Geometorys.SelectedCell.Latitude, meter, out ltlong, out ltlat, out brlong, out brlat);
                setCellSnaps(period, condPeriodList, ltlong, ltlat, brlong, brlat);
            }
            else if (condition.Geometorys.SelectedTDCell != null)
            {
                parseRectByLongLatDistance(condition.Geometorys.SelectedTDCell.Longitude, condition.Geometorys.SelectedTDCell.Latitude, meter, out ltlong, out ltlat, out brlong, out brlat);
                setTDCellSnaps(period, condPeriodList, ltlong, ltlat, brlong, brlat);
            }
            else if (condition.Geometorys.SelectedWCell != null)
            {
                parseRectByLongLatDistance(condition.Geometorys.SelectedWCell.Longitude, condition.Geometorys.SelectedWCell.Latitude, meter, out ltlong, out ltlat, out brlong, out brlat);
                setWCellSnaps(period, condPeriodList, ltlong, ltlat, brlong, brlat);
            }
            return condPeriodList;
        }

        private void setCellSnaps(TimePeriod period, List<CellQueryCondUnit> condPeriodList, double ltlong, double ltlat, double brlong, double brlat)
        {
            List<Cell> cellsnaps = condition.Geometorys.SelectedCell.GetAll(period);
            int formarLac = 0;
            int formarCi = 0;
            foreach (Cell snap in cellsnaps)
            {
                int lac = snap.LAC;
                int ci = snap.CI;
                if (lac != 0 && lac != formarLac && ci != 0 && ci != formarCi)
                {
                    CellQueryCondUnit cond = new CellQueryCondUnit();
                    cond.LAC = lac;
                    cond.CI = ci;
                    TimePeriod tpseg = new TimePeriod();
                    DateTime fromTime = snap.ValidPeriod.BeginTime;
                    DateTime toTime = snap.ValidPeriod.EndTime;
                    if (fromTime < period.BeginTime)
                    {
                        fromTime = period.BeginTime;
                    }
                    if (toTime > period.EndTime)
                    {
                        toTime = period.EndTime;
                    }
                    tpseg.SetPeriod(fromTime, toTime);
                    cond.period = tpseg;
                    cond.ltLongitude = ltlong;
                    cond.ltLatitude = ltlat;
                    cond.brLongitude = brlong;
                    cond.brLatitude = brlat;
                    condPeriodList.Add(cond);
                    formarLac = lac;
                    formarCi = ci;
                }
            }
        }

        private void setTDCellSnaps(TimePeriod period, List<CellQueryCondUnit> condPeriodList, double ltlong, double ltlat, double brlong, double brlat)
        {
            List<TDCell> cellsnaps = condition.Geometorys.SelectedTDCell.GetAll(period);
            int formarLac = 0;
            int formarCi = 0;
            foreach (TDCell snap in cellsnaps)
            {
                int lac = snap.LAC;
                int ci = snap.CI;
                if (lac != 0 && lac != formarLac && ci != 0 && ci != formarCi)
                {
                    CellQueryCondUnit cond = new CellQueryCondUnit();
                    cond.LAC = lac;
                    cond.CI = ci;
                    cond.period = snap.ValidPeriod;
                    cond.ltLongitude = ltlong;
                    cond.ltLatitude = ltlat;
                    cond.brLongitude = brlong;
                    cond.brLatitude = brlat;
                    condPeriodList.Add(cond);
                    formarLac = lac;
                    formarCi = ci;
                }
            }
        }

        private void setWCellSnaps(TimePeriod period, List<CellQueryCondUnit> condPeriodList, double ltlong, double ltlat, double brlong, double brlat)
        {
            List<WCell> cellsnaps = condition.Geometorys.SelectedWCell.GetAll(period);
            int formarLac = 0;
            int formarCi = 0;
            foreach (WCell snap in cellsnaps)
            {
                int lac = snap.LAC;
                int ci = snap.CI;
                if (lac != 0 && lac != formarLac && ci != 0 && ci != formarCi)
                {
                    CellQueryCondUnit cond = new CellQueryCondUnit();
                    cond.LAC = lac;
                    cond.CI = ci;
                    cond.period = snap.ValidPeriod;
                    cond.ltLongitude = ltlong;
                    cond.ltLatitude = ltlat;
                    cond.brLongitude = brlong;
                    cond.brLatitude = brlat;
                    condPeriodList.Add(cond);
                    formarLac = lac;
                    formarCi = ci;
                }
            }
        }

        private void parseRectByLongLatDistance(double longitude, double latitude, int meter, out double ltlong, out double ltlat, out double brlong, out double brlat)//通过中心经纬度及其到边框的距离求经纬度（模糊值）
        {
            double longPerMeter = 0.00001;
            double latPerMeter = 0.000009;
            ltlong = longitude - meter * longPerMeter;
            ltlat = latitude + meter * latPerMeter;
            brlong = longitude + meter * longPerMeter;
            brlat = latitude - meter * latPerMeter;
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.SelectedCell == null && searchGeometrys.SelectedTDCell == null && searchGeometrys.SelectedWCell == null)
            {
                return false;
            }
            return true;
        }
        protected override bool isValidPoint(double ltX,double ltY,double brX,double brY)
        {
            return true;
           
        }
        protected override bool isValidPoint(double jd, double wd)
        {
            return true;
            
        }

    }
}
