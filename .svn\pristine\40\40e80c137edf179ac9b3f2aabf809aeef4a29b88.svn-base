﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.GeneralFuncDef
{
    public class BaseSubAnalyser
    {
        public bool debugMode { get; set; } = false;
        public string name { get; set; }
        public virtual string GetDescShow(){return "";}

        public virtual Dictionary<string, object> Param
        {
            get
            {
                throw new InvalidOperationException("Param未实现！");
            }
            set
            {
                Console.Write(value);
            }
        }
    }
}
