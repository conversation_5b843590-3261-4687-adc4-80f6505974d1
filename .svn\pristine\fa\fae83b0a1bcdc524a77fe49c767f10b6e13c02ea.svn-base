﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;


using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Model.Interface;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Net
{
    public class DIYStatByRegionWholeProvince : DIYStatQuery
    {
        public DIYStatByRegionWholeProvince(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get { return "KPI统计(全省按区域)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11034, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        protected override StatTbToken getBranchToken()
        {
            return StatTbToken.grid;
        }
        protected override void prepareStatPackage_ImgGrid_FileFilter(Package package, TimePeriod period,byte carrierID,bool byRound)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_DIY_AREA_COVER_GRID;
            package.Content.PrepareAddParam();
            if(byRound)
            {
                AddDIYRound(package, condition.ByRoundYear, condition.ByRoundRound);
            }
            else
            {
                AddDIYPeriod(package, period);
            }
            AddDIYRegion_Intersect(package);
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYCarrierType(package, carrierID);
            AddDIYFileFilter(package, condition);

            AddDIYStatStatus(package);
            AddDIYAreaTypeAndID(package, condition.Areas);
            AddDIYMomt(package, condition.Momt);
            //
            AddDIYEndOpFlag(package);
            
        }
        protected override void prepareStatPackage_Event_FileFilter(Package package, TimePeriod period, byte carrierID,bool byRound)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_DIY_AREA_EVENT;
            package.Content.PrepareAddParam();
            if (byRound)
            {
                AddDIYRound(package, condition.ByRoundYear, condition.ByRoundRound);
            }
            else
            {
                AddDIYPeriod(package, period);
            }
            AddDIYRegion_Intersect(package);
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYCarrierType(package, carrierID);
            AddDIYFileFilter(package, condition);
            AddDIYStatStatus(package);
            AddDIYAreaTypeAndID(package, condition.Areas);
            AddDIYMomt(package, condition.Momt);
            //
            AddDIYEndOpFlag(package);
        }
        protected override void prepareStatPackage_Event_EventFilter(Package package, TimePeriod period)
        {
       //     AddDIYPeriod_Between(package, period); 因为可能要按轮查询，所以不加时间过滤
            AddDIYRegion_Sample(package);
            AddDIYEndOpFlag(package);

        }

        protected override void query()
        {
            loadReportFromFile();
            SelectReportDlg dlg = new SelectReportDlg();
            dlg.FillCurrentReports(ref rptStyleList);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            bool curSelStyleContentAllTmp;
            curSelStyle = dlg.GetSelectedReport(out curSelStyleContentAllTmp);
            curSelStyleContentAll = curSelStyleContentAllTmp;
            curEventStatFilter = dlg.GetEventStatFilter();
            momtFlag = dlg.GetMomtFlag();

            MainModel.KPICaleGridPercent = dlg.GetCaleGridPercent();
            //==
            MainModel.CurChinaMobileStatReportData = null;
            MainModel.CurChinaUnicomStatReportData = null;
            MainModel.CurChinaTelecomStatReportData = null;
            MainModel.CurChinaMobileStatReportDataList.Clear();
            MainModel.CurChinaUnicomStatReportDataList.Clear();
            MainModel.CurChinaTelecomStatReportDataList.Clear();

            MainModel.CurChinaMobileStatReportDataMo = null;
            MainModel.CurChinaUnicomStatReportDataMo = null;
            MainModel.CurChinaTelecomStatReportDataMo = null;
            MainModel.CurChinaMobileStatReportDataListMo.Clear();
            MainModel.CurChinaUnicomStatReportDataListMo.Clear();
            MainModel.CurChinaTelecomStatReportDataListMo.Clear();

            MainModel.CurChinaMobileStatReportDataMt = null;
            MainModel.CurChinaUnicomStatReportDataMt = null;
            MainModel.CurChinaTelecomStatReportDataMt = null;
            MainModel.CurChinaMobileStatReportDataListMt.Clear();
            MainModel.CurChinaUnicomStatReportDataListMt.Clear();
            MainModel.CurChinaTelecomStatReportDataListMt.Clear();

            cmDataUnitAreaKPIQueryDic = null;
            cuDataUnitAreaKPIQueryDic = null;
            ctDataUnitAreaKPIQueryDic = null;

            cmDataUnitAreaKPIQueryDicMo = null;
            cuDataUnitAreaKPIQueryDicMo = null;
            ctDataUnitAreaKPIQueryDicMo = null;

            cmDataUnitAreaKPIQueryDicMt = null;
            cuDataUnitAreaKPIQueryDicMt = null;
            ctDataUnitAreaKPIQueryDicMt = null;

            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            multiGeometrys = false;
            stopQuery = false;
            regionDic.Clear();
            if (multiGeometrys || MainModel.KPICaleGridPercent || MainModel.KPIBlanceCaleGrid)
            {
                gridDic = new Dictionary<string, StatGridUnit>();
            }

            int curDistrictID = MainModel.DistrictID;
            string[] districtNames = DistrictManager.GetInstance().DistrictNames;
            for (int i = 1; i < districtNames.Length; i++)
            {
                MainModel.DistrictID = i;
                regionDic[districtNames[i]] = null;
                doQuery();
            }
            MainModel.DistrictID = curDistrictID;

            MainModel.FireMultiRegionStatQueried(this, curSelStyle, regionDic, false);
        }

        private void doQuery()
        {
            queryByCarrier(1, queryChinaMobileInThread);
            queryByCarrier(2, queryChinaUnicomInThread);
            queryByCarrier(3, queryChinaTelecomInThread);
        }

        private void queryByCarrier(int carrierType, CallBackMethodWithParams queryInThread)
        {
            if (Condition.CarrierTypes.Contains(carrierType) && !WaitBox.CancelRequest)
            {
                Condition.Momt = 0;
                queryByCarrier(queryInThread);

                if (momtFlag)
                {
                    Condition.Momt = (int)MoMtFile.MoFlag;
                    queryByCarrier(queryInThread);

                    Condition.Momt = (int)MoMtFile.MtFlag;
                    queryByCarrier(queryInThread);
                }
            }
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }
        protected override bool isEventInRegion(double lng, double lat)
        {
            return Condition.Geometorys.GeoOp.Contains(lng, lat);
        }
        protected override bool isValidPoint(double jd, double wd)
        {
            try
            {
                GridPartParam data = new GridPartParam();
                data.LTLng = jd;
                data.LTLat = wd;
                return Condition.Geometorys.GeoOp.ContainsRectCenter(data.Bounds);
            }
            catch
            {
                return false;
            }
            
        }

    }
}
