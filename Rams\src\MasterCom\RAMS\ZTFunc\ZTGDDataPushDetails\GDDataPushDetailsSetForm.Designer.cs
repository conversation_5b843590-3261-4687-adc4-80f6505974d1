﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class GDDataPushDetailsSetForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.groupSet = new DevExpress.XtraEditors.GroupControl();
            this.chk下载状态 = new System.Windows.Forms.CheckBox();
            this.com推送状态 = new System.Windows.Forms.ComboBox();
            this.num数据编号 = new System.Windows.Forms.NumericUpDown();
            this.date加入记录R = new System.Windows.Forms.DateTimePicker();
            this.label3 = new System.Windows.Forms.Label();
            this.chk数据加入记录 = new System.Windows.Forms.CheckBox();
            this.date加入记录L = new System.Windows.Forms.DateTimePicker();
            this.chk推送状态 = new System.Windows.Forms.CheckBox();
            this.date推送开始R = new System.Windows.Forms.DateTimePicker();
            this.label2 = new System.Windows.Forms.Label();
            this.chk推送开始时间 = new System.Windows.Forms.CheckBox();
            this.date推送开始L = new System.Windows.Forms.DateTimePicker();
            this.date推送结束R = new System.Windows.Forms.DateTimePicker();
            this.label1 = new System.Windows.Forms.Label();
            this.chk推送结束时间 = new System.Windows.Forms.CheckBox();
            this.date推送结束L = new System.Windows.Forms.DateTimePicker();
            this.chk设备ID = new System.Windows.Forms.CheckBox();
            this.txt设备ID = new DevExpress.XtraEditors.TextEdit();
            this.chk数据名称 = new System.Windows.Forms.CheckBox();
            this.txt数据名称 = new DevExpress.XtraEditors.TextEdit();
            this.chk数据编号 = new System.Windows.Forms.CheckBox();
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            ((System.ComponentModel.ISupportInitialize)(this.groupSet)).BeginInit();
            this.groupSet.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.num数据编号)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt设备ID.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt数据名称.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // groupSet
            // 
            this.groupSet.Controls.Add(this.chk下载状态);
            this.groupSet.Controls.Add(this.com推送状态);
            this.groupSet.Controls.Add(this.num数据编号);
            this.groupSet.Controls.Add(this.date加入记录R);
            this.groupSet.Controls.Add(this.label3);
            this.groupSet.Controls.Add(this.chk数据加入记录);
            this.groupSet.Controls.Add(this.date加入记录L);
            this.groupSet.Controls.Add(this.chk推送状态);
            this.groupSet.Controls.Add(this.date推送开始R);
            this.groupSet.Controls.Add(this.label2);
            this.groupSet.Controls.Add(this.chk推送开始时间);
            this.groupSet.Controls.Add(this.date推送开始L);
            this.groupSet.Controls.Add(this.date推送结束R);
            this.groupSet.Controls.Add(this.label1);
            this.groupSet.Controls.Add(this.chk推送结束时间);
            this.groupSet.Controls.Add(this.date推送结束L);
            this.groupSet.Controls.Add(this.chk设备ID);
            this.groupSet.Controls.Add(this.txt设备ID);
            this.groupSet.Controls.Add(this.chk数据名称);
            this.groupSet.Controls.Add(this.txt数据名称);
            this.groupSet.Controls.Add(this.chk数据编号);
            this.groupSet.Location = new System.Drawing.Point(0, 1);
            this.groupSet.Name = "groupSet";
            this.groupSet.Size = new System.Drawing.Size(507, 262);
            this.groupSet.TabIndex = 0;
            this.groupSet.Text = "查询条件设置";
            // 
            // chk下载状态
            // 
            this.chk下载状态.AutoSize = true;
            this.chk下载状态.ForeColor = System.Drawing.Color.Red;
            this.chk下载状态.Location = new System.Drawing.Point(12, 231);
            this.chk下载状态.Name = "chk下载状态";
            this.chk下载状态.Size = new System.Drawing.Size(288, 18);
            this.chk下载状态.TabIndex = 27;
            this.chk下载状态.Text = "勾上关联下载状态(存在一对多关系，且比较耗时)";
            this.chk下载状态.UseVisualStyleBackColor = true;
            // 
            // com推送状态
            // 
            this.com推送状态.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.com推送状态.Enabled = false;
            this.com推送状态.FormattingEnabled = true;
            this.com推送状态.Items.AddRange(new object[] {
            "改名完成，等待上传",
            "上传中",
            "上传完成",
            "上传失败"});
            this.com推送状态.Location = new System.Drawing.Point(325, 31);
            this.com推送状态.Name = "com推送状态";
            this.com推送状态.Size = new System.Drawing.Size(175, 22);
            this.com推送状态.TabIndex = 26;
            // 
            // num数据编号
            // 
            this.num数据编号.Enabled = false;
            this.num数据编号.Location = new System.Drawing.Point(116, 30);
            this.num数据编号.Maximum = new decimal(new int[] {
            999999999,
            0,
            0,
            0});
            this.num数据编号.Name = "num数据编号";
            this.num数据编号.Size = new System.Drawing.Size(120, 22);
            this.num数据编号.TabIndex = 25;
            this.num数据编号.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // date加入记录R
            // 
            this.date加入记录R.Enabled = false;
            this.date加入记录R.Location = new System.Drawing.Point(315, 132);
            this.date加入记录R.Name = "date加入记录R";
            this.date加入记录R.Size = new System.Drawing.Size(181, 22);
            this.date加入记录R.TabIndex = 24;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(277, 136);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(35, 14);
            this.label3.TabIndex = 23;
            this.label3.Text = "  至  ";
            // 
            // chk数据加入记录
            // 
            this.chk数据加入记录.AutoSize = true;
            this.chk数据加入记录.Location = new System.Drawing.Point(12, 136);
            this.chk数据加入记录.Name = "chk数据加入记录";
            this.chk数据加入记录.Size = new System.Drawing.Size(98, 18);
            this.chk数据加入记录.TabIndex = 22;
            this.chk数据加入记录.Text = "数据加入记录";
            this.chk数据加入记录.UseVisualStyleBackColor = true;
            this.chk数据加入记录.CheckedChanged += new System.EventHandler(this.chk数据加入记录_CheckedChanged);
            // 
            // date加入记录L
            // 
            this.date加入记录L.Enabled = false;
            this.date加入记录L.Location = new System.Drawing.Point(116, 132);
            this.date加入记录L.Name = "date加入记录L";
            this.date加入记录L.Size = new System.Drawing.Size(152, 22);
            this.date加入记录L.TabIndex = 21;
            // 
            // chk推送状态
            // 
            this.chk推送状态.AutoSize = true;
            this.chk推送状态.Location = new System.Drawing.Point(242, 32);
            this.chk推送状态.Name = "chk推送状态";
            this.chk推送状态.Size = new System.Drawing.Size(74, 18);
            this.chk推送状态.TabIndex = 20;
            this.chk推送状态.Text = "推送状态";
            this.chk推送状态.UseVisualStyleBackColor = true;
            this.chk推送状态.CheckedChanged += new System.EventHandler(this.chk推送状态_CheckedChanged);
            // 
            // date推送开始R
            // 
            this.date推送开始R.Enabled = false;
            this.date推送开始R.Location = new System.Drawing.Point(315, 166);
            this.date推送开始R.Name = "date推送开始R";
            this.date推送开始R.Size = new System.Drawing.Size(181, 22);
            this.date推送开始R.TabIndex = 14;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(274, 171);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(35, 14);
            this.label2.TabIndex = 13;
            this.label2.Text = "  至  ";
            // 
            // chk推送开始时间
            // 
            this.chk推送开始时间.AutoSize = true;
            this.chk推送开始时间.Location = new System.Drawing.Point(12, 171);
            this.chk推送开始时间.Name = "chk推送开始时间";
            this.chk推送开始时间.Size = new System.Drawing.Size(98, 18);
            this.chk推送开始时间.TabIndex = 12;
            this.chk推送开始时间.Text = "推送开始时间";
            this.chk推送开始时间.UseVisualStyleBackColor = true;
            this.chk推送开始时间.CheckedChanged += new System.EventHandler(this.chk推送开始时间_CheckedChanged);
            // 
            // date推送开始L
            // 
            this.date推送开始L.Enabled = false;
            this.date推送开始L.Location = new System.Drawing.Point(116, 165);
            this.date推送开始L.Name = "date推送开始L";
            this.date推送开始L.Size = new System.Drawing.Size(152, 22);
            this.date推送开始L.TabIndex = 11;
            // 
            // date推送结束R
            // 
            this.date推送结束R.Location = new System.Drawing.Point(315, 201);
            this.date推送结束R.Name = "date推送结束R";
            this.date推送结束R.Size = new System.Drawing.Size(181, 22);
            this.date推送结束R.TabIndex = 10;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(274, 207);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(35, 14);
            this.label1.TabIndex = 9;
            this.label1.Text = "  至  ";
            // 
            // chk推送结束时间
            // 
            this.chk推送结束时间.AutoSize = true;
            this.chk推送结束时间.Checked = true;
            this.chk推送结束时间.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chk推送结束时间.Location = new System.Drawing.Point(12, 207);
            this.chk推送结束时间.Name = "chk推送结束时间";
            this.chk推送结束时间.Size = new System.Drawing.Size(98, 18);
            this.chk推送结束时间.TabIndex = 8;
            this.chk推送结束时间.Text = "推送结束时间";
            this.chk推送结束时间.UseVisualStyleBackColor = true;
            this.chk推送结束时间.CheckedChanged += new System.EventHandler(this.chk推送结束时间_CheckedChanged);
            // 
            // date推送结束L
            // 
            this.date推送结束L.Location = new System.Drawing.Point(116, 202);
            this.date推送结束L.Name = "date推送结束L";
            this.date推送结束L.Size = new System.Drawing.Size(152, 22);
            this.date推送结束L.TabIndex = 7;
            // 
            // chk设备ID
            // 
            this.chk设备ID.AutoSize = true;
            this.chk设备ID.Location = new System.Drawing.Point(12, 69);
            this.chk设备ID.Name = "chk设备ID";
            this.chk设备ID.Size = new System.Drawing.Size(62, 18);
            this.chk设备ID.TabIndex = 6;
            this.chk设备ID.Text = "设备ID";
            this.chk设备ID.UseVisualStyleBackColor = true;
            this.chk设备ID.CheckedChanged += new System.EventHandler(this.chk设备ID_CheckedChanged);
            // 
            // txt设备ID
            // 
            this.txt设备ID.Enabled = false;
            this.txt设备ID.Location = new System.Drawing.Point(116, 67);
            this.txt设备ID.Name = "txt设备ID";
            this.txt设备ID.Size = new System.Drawing.Size(384, 21);
            this.txt设备ID.TabIndex = 5;
            // 
            // chk数据名称
            // 
            this.chk数据名称.AutoSize = true;
            this.chk数据名称.Location = new System.Drawing.Point(12, 102);
            this.chk数据名称.Name = "chk数据名称";
            this.chk数据名称.Size = new System.Drawing.Size(74, 18);
            this.chk数据名称.TabIndex = 4;
            this.chk数据名称.Text = "数据名称";
            this.chk数据名称.UseVisualStyleBackColor = true;
            this.chk数据名称.CheckedChanged += new System.EventHandler(this.chk数据名称_CheckedChanged);
            // 
            // txt数据名称
            // 
            this.txt数据名称.Enabled = false;
            this.txt数据名称.Location = new System.Drawing.Point(116, 99);
            this.txt数据名称.Name = "txt数据名称";
            this.txt数据名称.Size = new System.Drawing.Size(384, 21);
            this.txt数据名称.TabIndex = 3;
            // 
            // chk数据编号
            // 
            this.chk数据编号.AutoSize = true;
            this.chk数据编号.Location = new System.Drawing.Point(12, 34);
            this.chk数据编号.Name = "chk数据编号";
            this.chk数据编号.Size = new System.Drawing.Size(78, 18);
            this.chk数据编号.TabIndex = 2;
            this.chk数据编号.Text = "数据编号 ";
            this.chk数据编号.UseVisualStyleBackColor = true;
            this.chk数据编号.CheckedChanged += new System.EventHandler(this.chk数据编号_CheckedChanged);
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(290, 276);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 1;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(403, 276);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 2;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // GDDataPushDetailsSetForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(510, 311);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.groupSet);
            this.MaximizeBox = false;
            this.Name = "GDDataPushDetailsSetForm";
            this.Text = "数据推送查询设置";
            ((System.ComponentModel.ISupportInitialize)(this.groupSet)).EndInit();
            this.groupSet.ResumeLayout(false);
            this.groupSet.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.num数据编号)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt设备ID.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt数据名称.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.GroupControl groupSet;
        private System.Windows.Forms.CheckBox chk数据名称;
        private DevExpress.XtraEditors.TextEdit txt数据名称;
        private System.Windows.Forms.CheckBox chk数据编号;
        private System.Windows.Forms.CheckBox chk设备ID;
        private DevExpress.XtraEditors.TextEdit txt设备ID;
        private System.Windows.Forms.DateTimePicker date推送结束R;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.CheckBox chk推送结束时间;
        private System.Windows.Forms.DateTimePicker date推送结束L;
        private System.Windows.Forms.DateTimePicker date推送开始R;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.CheckBox chk推送开始时间;
        private System.Windows.Forms.DateTimePicker date推送开始L;
        private System.Windows.Forms.CheckBox chk推送状态;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.DateTimePicker date加入记录R;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.CheckBox chk数据加入记录;
        private System.Windows.Forms.DateTimePicker date加入记录L;
        private System.Windows.Forms.NumericUpDown num数据编号;
        private System.Windows.Forms.ComboBox com推送状态;
        private System.Windows.Forms.CheckBox chk下载状态;
    }
}