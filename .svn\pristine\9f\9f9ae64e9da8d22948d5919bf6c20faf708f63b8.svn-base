﻿namespace MasterCom.RAMS.Func.PlanningInfo
{
    partial class PlanningInfoImportForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.label1 = new System.Windows.Forms.Label();
            this.tbxExcelPath = new System.Windows.Forms.TextBox();
            this.btnBrowse = new System.Windows.Forms.Button();
            this.btnImport = new System.Windows.Forms.Button();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.rbtnAppend = new System.Windows.Forms.RadioButton();
            this.rbtnFull = new System.Windows.Forms.RadioButton();
            this.label3 = new System.Windows.Forms.Label();
            this.btnExportProblems = new System.Windows.Forms.Button();
            this.tbxTip = new System.Windows.Forms.TextBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.grpBoxInfo = new System.Windows.Forms.GroupBox();
            this.groupBox2.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.grpBoxInfo.SuspendLayout();
            this.SuspendLayout();
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(25, 25);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(77, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "规划文档路径";
            // 
            // tbxExcelPath
            // 
            this.tbxExcelPath.BackColor = System.Drawing.SystemColors.Window;
            this.tbxExcelPath.Location = new System.Drawing.Point(103, 22);
            this.tbxExcelPath.Name = "tbxExcelPath";
            this.tbxExcelPath.ReadOnly = true;
            this.tbxExcelPath.Size = new System.Drawing.Size(360, 21);
            this.tbxExcelPath.TabIndex = 1;
            // 
            // btnBrowse
            // 
            this.btnBrowse.Location = new System.Drawing.Point(469, 20);
            this.btnBrowse.Name = "btnBrowse";
            this.btnBrowse.Size = new System.Drawing.Size(75, 23);
            this.btnBrowse.TabIndex = 2;
            this.btnBrowse.Text = "浏览";
            this.btnBrowse.UseVisualStyleBackColor = true;
            this.btnBrowse.Click += new System.EventHandler(this.btnBrowse_Click);
            // 
            // btnImport
            // 
            this.btnImport.Location = new System.Drawing.Point(469, 55);
            this.btnImport.Name = "btnImport";
            this.btnImport.Size = new System.Drawing.Size(75, 23);
            this.btnImport.TabIndex = 3;
            this.btnImport.Text = "导入";
            this.btnImport.UseVisualStyleBackColor = true;
            this.btnImport.Click += new System.EventHandler(this.btnImport_Click);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.rbtnAppend);
            this.groupBox2.Controls.Add(this.rbtnFull);
            this.groupBox2.Controls.Add(this.label3);
            this.groupBox2.Controls.Add(this.label1);
            this.groupBox2.Controls.Add(this.btnImport);
            this.groupBox2.Controls.Add(this.btnBrowse);
            this.groupBox2.Controls.Add(this.tbxExcelPath);
            this.groupBox2.Location = new System.Drawing.Point(12, 12);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(553, 91);
            this.groupBox2.TabIndex = 5;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "导入信息";
            // 
            // rbtnAppend
            // 
            this.rbtnAppend.AutoSize = true;
            this.rbtnAppend.Location = new System.Drawing.Point(241, 63);
            this.rbtnAppend.Name = "rbtnAppend";
            this.rbtnAppend.Size = new System.Drawing.Size(71, 16);
            this.rbtnAppend.TabIndex = 6;
            this.rbtnAppend.Text = "追加导入";
            this.rbtnAppend.UseVisualStyleBackColor = true;
            // 
            // rbtnFull
            // 
            this.rbtnFull.AutoSize = true;
            this.rbtnFull.Checked = true;
            this.rbtnFull.Location = new System.Drawing.Point(129, 63);
            this.rbtnFull.Name = "rbtnFull";
            this.rbtnFull.Size = new System.Drawing.Size(71, 16);
            this.rbtnFull.TabIndex = 5;
            this.rbtnFull.TabStop = true;
            this.rbtnFull.Text = "完全导入";
            this.rbtnFull.UseVisualStyleBackColor = true;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(49, 63);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(53, 12);
            this.label3.TabIndex = 4;
            this.label3.Text = "导入方式";
            // 
            // btnExportProblems
            // 
            this.btnExportProblems.Location = new System.Drawing.Point(27, 30);
            this.btnExportProblems.Name = "btnExportProblems";
            this.btnExportProblems.Size = new System.Drawing.Size(120, 23);
            this.btnExportProblems.TabIndex = 6;
            this.btnExportProblems.Text = "导出问题记录";
            this.btnExportProblems.UseVisualStyleBackColor = true;
            this.btnExportProblems.Click += new System.EventHandler(this.btnExportProblems_Click);
            // 
            // tbxTip
            // 
            this.tbxTip.BackColor = System.Drawing.SystemColors.Window;
            this.tbxTip.Location = new System.Drawing.Point(27, 29);
            this.tbxTip.Multiline = true;
            this.tbxTip.Name = "tbxTip";
            this.tbxTip.ReadOnly = true;
            this.tbxTip.ScrollBars = System.Windows.Forms.ScrollBars.Both;
            this.tbxTip.Size = new System.Drawing.Size(517, 229);
            this.tbxTip.TabIndex = 4;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.btnExportProblems);
            this.groupBox1.Location = new System.Drawing.Point(12, 115);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(553, 69);
            this.groupBox1.TabIndex = 7;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "导出信息";
            // 
            // grpBoxInfo
            // 
            this.grpBoxInfo.Controls.Add(this.tbxTip);
            this.grpBoxInfo.Location = new System.Drawing.Point(12, 203);
            this.grpBoxInfo.Name = "grpBoxInfo";
            this.grpBoxInfo.Size = new System.Drawing.Size(553, 277);
            this.grpBoxInfo.TabIndex = 8;
            this.grpBoxInfo.TabStop = false;
            this.grpBoxInfo.Text = "提示信息";
            // 
            // PlanningInfoImportForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(577, 492);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.grpBoxInfo);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "PlanningInfoImportForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "规划信息";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.PlanningInfoImportForm_FormClosing);
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.grpBoxInfo.ResumeLayout(false);
            this.grpBoxInfo.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox tbxExcelPath;
        private System.Windows.Forms.Button btnBrowse;
        private System.Windows.Forms.Button btnImport;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.TextBox tbxTip;
        private System.Windows.Forms.Button btnExportProblems;
        private System.Windows.Forms.RadioButton rbtnAppend;
        private System.Windows.Forms.RadioButton rbtnFull;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox grpBoxInfo;
    }
}