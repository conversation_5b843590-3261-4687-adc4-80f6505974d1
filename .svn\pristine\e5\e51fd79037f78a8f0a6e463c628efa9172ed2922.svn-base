﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    class PingPangAnalyzer
    {
        public PingPangAnalyzer()
        {
            statEvtIDs = new List<int>();
            statEvtIDs.Add(851); // Intra Handover Success
            statEvtIDs.Add(899); // Inter Handover Success
            statEvtIDs.Add(3156);  //fdd下的
            statEvtIDs.Add(3159);  //fdd下的
        }
        
        public PingPangCondition PingPangCond { get; set; } = new PingPangCondition();

        public void Analyze(List<DTFileDataManager> fileManagers)
        {
            Results.Clear();
            foreach (DTFileDataManager dtFile in fileManagers)
            {
                Analyze(dtFile);
            }
            SetSN();
        }

        public void Init()
        {
            Results.Clear();
        }
        public void Analyze(DTFileDataManager dtFile)
        {
            PingPangFile ppFile = new PingPangFile(dtFile);
            Event lastEvt = null;
            foreach (Event evt in dtFile.Events)
            {
                if (!statEvtIDs.Contains(evt.ID))
                {
                    continue;
                }

                if (lastEvt == null)
                {
                    lastEvt = evt;
                    continue;
                }

                addPingPangPairs(dtFile, ppFile, lastEvt, evt);

                lastEvt = evt;
            } // end foreach event

            if (ppFile.PairCount > 0)
            {
                ppFile.CalcResult();
                Results.Add(ppFile);
            }
        }

        private void addPingPangPairs(DTFileDataManager dtFile, PingPangFile ppFile, Event lastEvt, Event evt)
        {
            if (((int)lastEvt["TargetLAC"] == (int)evt["LAC"]
                && (int)lastEvt["TargetCI"] == (int)evt["CI"]
                && (int)evt["TargetLAC"] == (int)lastEvt["LAC"]
                && (int)evt["TargetCI"] == (int)lastEvt["CI"]))  //出现乒乓切换
            {
                TimeSpan timeSpan = evt.DateTime - lastEvt.DateTime;
                if (timeSpan.TotalSeconds <= PingPangCond.TimeLimit)
                {
                    bool isSpeedValid = true;
                    if (PingPangCond.IsLimitSpeed)
                    {
                        double curSpeed = MathFuncs.GetDistance(evt.Longitude, evt.Latitude,
                            lastEvt.Longitude, lastEvt.Latitude) / Math.Abs(evt.Time - lastEvt.Time) * 3.6;
                        isSpeedValid = curSpeed >= PingPangCond.SpeedLimitMin && curSpeed <= PingPangCond.SpeedLimitMax;
                    }

                    if (isSpeedValid)
                    {
                        PingPangEvent evtA = new PingPangEvent(lastEvt, dtFile);
                        PingPangEvent evtB = new PingPangEvent(evt, dtFile);
                        PingPangPair pair = new PingPangPair(evtA, evtB);
                        ppFile.Pairs.Add(pair);
                    }
                }
            } // end if 出现乒乓切换
        }

        public void SetSN()
        {
            int fileSN = 0;
            foreach (PingPangFile ppFile in Results)
            {
                ppFile.SN = ++fileSN;

                int pairSN = 0;
                foreach (PingPangPair ppPair in ppFile.Pairs)
                {
                    ppPair.SN = ++pairSN;

                    int evtSN = 0;
                    foreach (PingPangEvent ppEvt in ppPair.Events)
                    {
                        ppEvt.SN = ++evtSN;
                    }
                }

                int cellSN = 0;
                foreach (PingPangICell ppCell in ppFile.Cells)
                {
                    ppCell.SN = ++cellSN;
                }
            }
        }

        protected List<int> statEvtIDs = null;

        public List<PingPangFile> Results = new List<PingPangFile>();
    }
}
