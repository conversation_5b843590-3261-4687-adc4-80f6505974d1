﻿using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.Linq;
using MasterCom.Util.UiEx;


namespace MasterCom.RAMS.ZTFunc
{

    public class XJLTEBtsCheck : QueryBase
    {
        protected int day = 7;
        DateTime bTime;
        DateTime eTime;
        readonly List<XJBaseSitesInfo> resultList = new List<XJBaseSitesInfo>();
        readonly List<XJBaseSitesInfo> resultListNew = new List<XJBaseSitesInfo>();
        readonly Dictionary<string, List<XJBaseSitesInfo>> resultDic = new Dictionary<string, List<XJBaseSitesInfo>>();

        public XJLTEBtsCheck()
            : base(MainModel.GetInstance())
        {
        }
        public override string Name
        {
            get { return "新疆LTE站点核查"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11067, this.Name);
        }
        public override bool IsNeedSetQueryCondition
        {
            get { return true; }
        }
        XJLTEBtsCheckConditionDlg dlg;
        protected override bool isValidCondition()
        {
            MainModel.MainForm.GetConditionTime(out bTime, out eTime);
            if (dlg == null)
            {
                dlg = new XJLTEBtsCheckConditionDlg();
            }
            dlg.SetCondition(day);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            dlg.GetCondition(out day);
            TimeSpan ts = eTime - bTime;
            if (day > Convert.ToInt32(ts.TotalDays))//如果设置时间大于 所选时间 则错误
            {
                System.Windows.Forms.MessageBox.Show("所选时间大于客户端设置的时间！");
                return false;
            }
            return true;
        }

        XJLTEBtsCheckBase xjBase;
        XJLTEBtsCheckOld xjOld;
        XJLTEBtsCheckAsset xjAsset;

        protected override void query()//获取三种类型的表的数据
        {
            clearData();
            xjBase = new XJLTEBtsCheckBase();
            xjBase.SetBeginTime(bTime);//传入时间
            xjBase.SetEndTime(eTime);
            xjBase.Query();

            xjOld = new XJLTEBtsCheckOld();
            xjOld.Query();

            xjAsset = new XJLTEBtsCheckAsset();
            xjAsset.Query();
            WaitTextBox.Show("正在处理数据",dealData);
            showResultForm();
        }
        private void clearData()
        {
            resultList.Clear();
            resultListNew.Clear();
            resultDic.Clear();
        }
    
        private void dealData()
        {
            try
            {
                XJBaseSitesInfo infoBase;
                int total = xjBase.BaseList.Count;
                for (int i = 0; i < total; i++)
                {
                    infoBase = xjBase.BaseList[i];
                    bool flag = getFlag(infoBase);
                    if (!flag)
                    {
                        flag = resetFlag(infoBase, flag);
                        if (!flag)
                        {
                            addResultDic(infoBase);
                        }
                    }
                    //WaitTextBox.Text = $"当前进度 : {i+1}/{total}";
                }

                addresultListNew();
            }
            catch (Exception e)
            {
                MessageBox.Show(e.ToString());
            }
            finally
            {
                System.Threading.Thread.Sleep(200);
                WaitTextBox.Close();
            }
        }


        private bool getFlag(XJBaseSitesInfo infoBase)
        {
            bool flag = false;
            List<XJAssetManageInfo> infoAssetList = null;
            if (xjAsset.ENodeBElementNameAssetDic.ContainsKey(infoBase.Belong))
            {
                infoAssetList = xjAsset.ENodeBElementNameAssetDic[infoBase.Belong];
                flag = judgeAsset(infoBase, infoAssetList);
                if (flag)
                {
                    return flag;
                }
            }

            if (xjAsset.ENodeBIDAssetDic.ContainsKey(infoBase.ENODEBID))
            {
                infoAssetList = xjAsset.ENodeBIDAssetDic[infoBase.ENODEBID];
                flag = judgeAsset(infoBase, infoAssetList);
            }

            return flag;
        }

        private bool judgeAsset(XJBaseSitesInfo infoBase, List<XJAssetManageInfo> infoAssetList)
        {
            foreach (var infoAsset in infoAssetList)
            {
                if (infoAsset.Status == "现网" || infoAsset.Status == "维护" || infoAsset.Status == "工程")
                {
                    if (xjAsset.ENodeBIDAssetDic.ContainsKey(infoBase.ENODEBID)
                        || xjAsset.ENodeBNameAssetDic.ContainsKey(infoBase.ChineseName)
                        || xjAsset.ENodeBNameAssetDic.ContainsKey(infoBase.Belong)
                        || xjAsset.ENodeBElementNameAssetDic.ContainsKey(infoBase.ChineseName)
                        || xjAsset.ENodeBElementNameAssetDic.ContainsKey(infoBase.Belong))
                    {
                        return true;
                    }
                }
                else
                {
                    infoBase.Status = infoAsset.Status;//在非（现网，维护，工程）的时候，出现相同id则赋值状态
                }
            }

            return false;
        }

        private bool resetFlag(XJBaseSitesInfo infoBase, bool flag)
        {
            if (xjOld.ENodeBIDOldSiteDic.ContainsKey(infoBase.ENODEBID)
                || xjOld.ENodeBNameOldSiteDic.ContainsKey(infoBase.ChineseName))
            {
                flag = true;
            }

            return flag;
        }

        private void addResultDic(XJBaseSitesInfo infoBase)
        {
            string key = infoBase.ENODEBID + infoBase.NetType;
            if (!resultDic.ContainsKey(key))
            {
                resultDic.Add(key, new List<XJBaseSitesInfo>());
            }
            resultDic[key].Add(infoBase);
        }

        private void addresultListNew()
        {
            foreach (List<XJBaseSitesInfo> temp in resultDic.Values)
            {
                if (temp.Count < day) continue;
                temp.Sort((x, y) =>
                {
                    return x.DateTime.CompareTo(y.DateTime);
                });

                int count = 1;
                //int index1 = 0;//下标指向起点
                //int index2 = 1;
                //for (int i = 0; i < temp.Count - 1; i++)
                //{
                //    getCountDays(temp, ref count, ref index1, ref index2, i);
                //}
                if (temp.Count > 1)
                {
                    Dictionary<DateTime, int> dayDic = new Dictionary<DateTime, int>();
                    for (int i = 0; i < temp.Count; i++)
                    {
                        if (!dayDic.ContainsKey(temp[i].DateTime.Date))
                        {
                            dayDic.Add(temp[i].DateTime.Date, i);
                        }
                    }
                    count = dayDic.Count;
                }

                if (count >= day)
                {
                    XJBaseSitesInfo infonew = temp[0];
                    infonew.CountDays = count;
                    infonew.SN = resultListNew.Count + 1;
                    resultListNew.Add(infonew);
                }
            }
        }

        //private void getCountDays(List<XJBaseSitesInfo> temp, ref int count, ref int index1, ref int index2, int i)
        //{
        //    TimeSpan ts = temp[i + 1].DateTime - temp[i].DateTime;
        //    if (ts.Days == 0)
        //    {
        //        return;
        //    }

        //    if (ts.Days != 1)//如果时间间隔不为1
        //    {
        //        if (index2 - index1 >= day)//如果此时时间间隔大于day
        //        {
        //            if (index2 - index1 > count)
        //                count = index2 - index1;//count取值为最大的数
        //        }
        //        else
        //        {
        //            count = 1;//如果时间间隔小于day则直接命为1
        //        }
        //        index1 = i;//重新指向不相同时间的起点
        //    }
        //    else if (ts.Days == 1)
        //    {
        //        count++;//相隔天数加一
        //    }
        //    index2++;
        //}

        private void clear()
        {
            xjBase.ClearData();
            xjAsset.ClearData();
            xjOld.ClearData();
        }

        protected void showResultForm()
        {
            clear();
            if (resultListNew.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            XJLTEBtsCheckInfoForm frm = MainModel.CreateResultForm(typeof(XJLTEBtsCheckInfoForm)) as XJLTEBtsCheckInfoForm;
            frm.FillData(resultListNew);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public  class XJBaseSitesInfo
    {
        public int SN { get; set; }
        public DateTime  DateTime { get; set; }
        public string City { get; set; }
        public string Company { get; set; }
        public int ENODEBID { get; set; }
        public string Belong { get; set; }
        public string ChineseName { get; set; }
        public string SetTime { get; set; }
        public int CountDays { get; set; }
        public string Status { get; set; } = "资管数据中未出现";
        public string NetType { get; set; }
    }

    public  class XJOldSitesInfo
    {
        public int SN { get; set; }
        public string Belong { get; set; }
        public int ENODEBID { get; set; }
    }

    public  class XJAssetManageInfo
    {
        public int SN { get; set; }
        public string Name { get; set; }
        public string Code { get; set; }
        public string ElementName { get; set; }
        public string ManageArea { get; set; }
        public int ENODEBID { get; set; }
        public string Company { get; set; }
        public string Status { get; set; }
    }
}
