﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.NOP.BatchImport
{
    public partial class BatchImportResultForm : MinCloseForm
    {
        public BatchImportResultForm()
        {
            InitializeComponent();
            base.DisposeWhenClose = true;
        }

        public void FillData(List<BatchImportItem> itemList)
        {
            BatchImportItemView view = new BatchImportItemView();
            view.FillData(new List<BatchImportItem>(itemList));
            view.Dock = DockStyle.Fill;

            this.Controls.Clear();
            this.Controls.Add(view);
        }
    }
}
