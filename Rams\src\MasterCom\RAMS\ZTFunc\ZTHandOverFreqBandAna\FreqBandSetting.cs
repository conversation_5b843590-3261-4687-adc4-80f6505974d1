﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraPrinting.Export.Pdf;
using DevExpress.XtraGrid.Views.Grid;
using System.Resources;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class FreqBandSetting : UserControl
    {
        public FreqBandList FreqBands { get; private set; } = new FreqBandList();
        int curSelectedFreqBandIdx = -1;
        //int curSelectedFreqIdx = -1;
        object curSelectedFreqRow = null;

        public FreqBandSetting()
        {
            InitializeComponent();
        }

        public void FillData(FreqBandAnaClassType type)
        {
            FreqBandManager.Instance.LoadConfig();
            Dictionary<FreqBandAnaClassType, FreqBandList> freqBandAnaDic = FreqBandManager.Instance.FreqBandAnaDic;
         
            if (freqBandAnaDic.ContainsKey(type))
            {
                FreqBands = freqBandAnaDic[type];
                //chbAllowRangeIntersect.Checked = FreqBands.AllowRangeIntersect;
                gridControl1.DataSource = FreqBands.FreqBandInfoList;
                gridControl1.RefreshDataSource();
            }
            else
            {
                FreqBands = freqBandAnaDic[FreqBandAnaClassType.BaseType];
                //chbAllowRangeIntersect.Checked = FreqBands.AllowRangeIntersect;
                gridControl1.DataSource = FreqBands.FreqBandInfoList;
                gridControl1.RefreshDataSource();
            }
        }

        private void btnAddFreqBand_Click(object sender, EventArgs e)
        {
            FreqValueSettingBox box = new FreqValueSettingBox();
            List<string> parentFreqBandList = getParentFreqBandList();
            box.AddFreqBand(parentFreqBandList);
            if (box.ShowDialog() == DialogResult.OK)
            {
                bool isExsist = FreqBands.JudgeExsistFreqBand(box.FreqBandName);
                if (isExsist)
                {
                    MessageBox.Show("添加失败,已存在该频段");
                }
                else
                {
                    string errMsg = FreqBands.AddFreqBand(box.FreqBandName, box.ParentFreqBandName, box.FreqValue);
                    if (!string.IsNullOrEmpty(errMsg))
                    {
                        MessageBox.Show("添加失败," + errMsg);
                    }
                    gridControl1.RefreshDataSource();
                }
            }
        }

        private List<string> getParentFreqBandList()
        {
            List<string> parentFreqBandList = new List<string>();
            parentFreqBandList.Add("");
            foreach (var item in FreqBands.FreqBandInfoDic)
            {
                if (string.IsNullOrEmpty(item.Value.ParentFreqBandName))
                {
                    parentFreqBandList.Add(item.Key);
                }
            }

            return parentFreqBandList;
        }

        private void btnAddFreq_Click(object sender, EventArgs e)
        {
            //int rowHandle = gvFreqBand.GetSelectedRows()[0];
            object row = gvFreqBand.GetRow(curSelectedFreqBandIdx);
            FreqBandInfo value = row as FreqBandInfo;
            if (value != null)
            {
                FreqValueSettingBox box = new FreqValueSettingBox();
                box.AddFreq(value.FreqBandName, value.ParentFreqBandName);
                if (box.ShowDialog() == DialogResult.OK)
                {
                    gvFreqBand.CollapseMasterRow(curSelectedFreqBandIdx);
                    string errMsg = FreqBands.AddFreq(box.FreqBandName, box.FreqValue);
                    if (!string.IsNullOrEmpty(errMsg))
                    {
                        MessageBox.Show("添加失败," + errMsg);
                    }
                    gridControl1.RefreshDataSource();
                    gvFreqBand.ExpandMasterRow(curSelectedFreqBandIdx);
                }
            }
        }

        private void btnModifyFreqBand_Click(object sender, EventArgs e)
        {
            object row = gvFreqBand.GetRow(curSelectedFreqBandIdx);
            FreqBandInfo value = row as FreqBandInfo;
            if (value != null)
            {
                string curBandName = value.FreqBandName;
                FreqValueSettingBox box = new FreqValueSettingBox();
                List<string> parentFreqBandList = getParentFreqBandList();
                box.EditFreqBand(value.FreqBandName, value.ParentFreqBandName, parentFreqBandList);
                if (box.ShowDialog() == DialogResult.OK)
                {
                    bool isExsist = FreqBands.JudgeExsistFreqBand(box.FreqBandName, curBandName);
                    if (isExsist)
                    {
                        MessageBox.Show("修改失败,已存在该频段");
                    }
                    else
                    {
                        FreqBands.EditFreqBand(value.FreqBandName, box.FreqBandName, box.ParentFreqBandName);
                        gridControl1.RefreshDataSource();
                    }
                }
            }
        }

        private void btnModifyFreq_Click(object sender, EventArgs e)
        {
            FreqInfo value = curSelectedFreqRow as FreqInfo;
            if (value != null)
            {
                object row = gvFreqBand.GetRow(curSelectedFreqBandIdx);
                FreqBandInfo freqBand = row as FreqBandInfo;

                FreqInfo beforeModifyInfo = new FreqInfo()
                {
                    Type = value.Type,
                    Value = value.Value,
                    MinRangeValue = value.MinRangeValue,
                    MaxRangeValue = value.MaxRangeValue
                };

                FreqValueSettingBox box = new FreqValueSettingBox();
                box.EditFreq(value.FreqBandName, freqBand.ParentFreqBandName, beforeModifyInfo);
                if (box.ShowDialog() == DialogResult.OK)
                {
                    //修改频点时判断修改后的频点是否与除父频段之外的其他点重合,重合则修改失败
                    string errMsg = freqBand.JudgeRangeIntersect(value.FreqDesc, beforeModifyInfo, FreqBands.FreqBandInfoDic);
                    if (!string.IsNullOrEmpty(errMsg))
                    {
                        MessageBox.Show("修改失败," + errMsg);
                    }
                    else
                    {
                        gvFreqBand.CollapseMasterRow(curSelectedFreqBandIdx);
                        freqBand.EditFreq(beforeModifyInfo, value);
                        gridControl1.RefreshDataSource();
                        gvFreqBand.ExpandMasterRow(curSelectedFreqBandIdx);
                    }
                }
            }
        }

        private void btnRemove_Click(object sender, EventArgs e)
        {
            if (curSelectedFreqRow != null && curSelectedFreqBandIdx != -1)
            {
                FreqInfo freq = curSelectedFreqRow as FreqInfo;
                FreqBands.RemoveFreq(freq.FreqBandName, freq);
                gridControl1.RefreshDataSource();
            }
            else if (curSelectedFreqRow == null && curSelectedFreqBandIdx != -1)
            {
                object row = gvFreqBand.GetRow(curSelectedFreqBandIdx);
                FreqBandInfo value = row as FreqBandInfo;
                FreqBands.RemoveFreqBand(value.FreqBandName);
                gridControl1.RefreshDataSource();
            }
        }

        public void SaveConfig()
        {
            FreqBandManager.Instance.SaveConfig();
        }


        private void gvFreq_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            GridView gv = sender as GridView;
            int rowHandle = e.RowHandle;
            object row = gv.GetRow(rowHandle);
            FreqInfo freq = row as FreqInfo;

            curSelectedFreqBandIdx = getFreqBandIdxByFreq(freq);
            curSelectedFreqRow = row;

            gvFreqBand.SelectRow(curSelectedFreqBandIdx);
            gvFreqBand.FocusedRowHandle = curSelectedFreqBandIdx;
        }

        private int getFreqBandIdxByFreq(FreqInfo freq)
        {
            int idx = -1;
            for (int i = 0; i < gvFreqBand.RowCount; i++)
            {
                object freqBand = gvFreqBand.GetRowCellValue(i, "FreqBandName");
                if (freqBand != null && freqBand.ToString() == freq.FreqBandName)
                {
                    idx = i;
                    break;
                }
            }
            return idx;
        }

        private void gvFreqBand_RowClick(object sender, RowClickEventArgs e)
        {
            curSelectedFreqBandIdx = e.RowHandle;
            curSelectedFreqRow = null;
        }

        private void chbAllowRangeIntersect_CheckedChanged(object sender, EventArgs e)
        {
            //FreqBands.AllowRangeIntersect = chbAllowRangeIntersect.Checked;
        }

        private void gvFreqBand_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            object row = gvFreqBand.GetRow(curSelectedFreqBandIdx);
            FreqBandInfo value = row as FreqBandInfo;
            if (value != null)
            {
                value.Enable = (bool)e.Value;
            }
        }

        private void gvFreq_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            FreqInfo value = curSelectedFreqRow as FreqInfo;
            if (value != null)
            {
                value.Enable = (bool)e.Value;
            }
        }
    }
}
