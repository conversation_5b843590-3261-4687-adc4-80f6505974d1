﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;

namespace MasterCom.RAMS.BackgroundFunc
{
    public enum NbIotKpiKeyXJ
    {
        AttachRate = 0x04020000,
        InStationReselection = 0x04020001,
        BetweenStationReselection = 0x04020002,
        PingRate = 0x04020003,
        PingDelay = 0x04020004,
        ULAvgRSRP = 0x04020005,
        ULAvgSINR = 0x04020006,
        ULThroughputRate = 0x04020007,
        DLThroughputRate = 0x04020008,

        CoverRsrpPath = 0x04020009,
        CoverSinrPath = 0x0402000A,
        CoverULPath = 0x0402000B,
        CoverAvgRSRP = 0x0402000C,
        CoverAvgSINR = 0x0402000D,
        CoverAvgSpeed = 0x0402000E,
        CoverSpeedUpTen = 0x04020010,
        CoverCoverRate = 0x04020011,

        AttachRateDis = 0x04020012,
        ReselectionDis = 0x04020013,
        PingRateDis = 0x04020014,
        PingDelayDis = 0x04020015,
        ULThroughputRateDis = 0x04020016,
        DLThroughputRateDis = 0x04020017,
    }

    public abstract class NbIotCellAcceptKpiAnaXJ
    {
        protected static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        public abstract Dictionary<NbIotKpiKeyXJ, object> GetFileKpiInfos(FileInfo fileInfo
            , DTFileDataManager fileManager, LTECell targetCell);

        public abstract bool IsValidFile(FileInfo fileInfo);

        protected LTECell getTpSrcCell(TestPoint tp)
        {
            return NbIotStationAcceptManagerXJ.GetTpSrcCell(tp);
        }

        protected void reportInfo(string strInfo)
        {
            if (MainModel.GetInstance().BackgroundStarted)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo(strInfo);
            }
            else
            {
                log.Info(strInfo);
            }
        }

        protected void reportInfo(Exception ex)
        {
            if (MainModel.GetInstance().BackgroundStarted)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundError(ex);
            }
            else
            {
                System.Windows.Forms.MessageBox.Show(ex.Message + Environment.NewLine + ex.Source + Environment.NewLine + ex.StackTrace);
            }
        }
    }

    #region 室外站
    /// <summary>
    /// Attach
    /// </summary>
    class NbIotAcpAutoAttachRateXJ : NbIotCellAcceptKpiAnaXJ
    {
        protected List<int> evtSuccList;
        protected List<int> evtFailList;
        protected List<int> evtRequList;

        public NbIotAcpAutoAttachRateXJ()
        {
            evtRequList = new List<int> { 5001 };
            evtSuccList = new List<int> { 5002 };
            evtFailList = new List<int> { 5003 };
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.ToUpper().Contains("ATTACH");
        }

        /// <summary>
        /// 获取文件分析出的结果
        /// </summary>
        /// <param name="fileInfo"></param>
        /// <param name="fileManager"></param>
        /// <param name="targetCell"></param>
        /// <returns></returns>
        public override Dictionary<NbIotKpiKeyXJ, object> GetFileKpiInfos(FileInfo fileInfo
            , DTFileDataManager fileManager, LTECell targetCell)
        {
            NbIotCellKpi kpiCell = anaFile(fileInfo, fileManager, targetCell);
            if (kpiCell == null)
            {
                reportInfo(string.Format("文件{0}获取有效的事件或采样点信息失败", fileInfo.Name));
                return new Dictionary<NbIotKpiKeyXJ, object>();
            }
            getDistance(fileManager, targetCell, kpiCell);

            if (filterDataINfo(kpiCell, fileInfo))
            {
                return getKpiInfos(kpiCell);
            }
            else
            {
                return new Dictionary<NbIotKpiKeyXJ, object>();
            }
        }

        /// <summary>
        /// 对文件分析
        /// </summary>
        /// <param name="fileInfo"></param>
        /// <param name="fileManager"></param>
        /// <param name="targetCell"></param>
        /// <returns></returns>
        protected virtual NbIotCellKpi anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            NbIotCellKpi targetKpiCell = new NbIotCellKpi();

            if (fileManager.Events.Count == 0)
            {
                reportInfo("没有事件");
                return null;
            }

            //判断事件成功率
            foreach (Event evt in fileManager.Events)
            {
                if (evtRequList.Contains(evt.ID))
                {
                    ++targetKpiCell.RequestCnt;
                }
                else if (evtSuccList.Contains(evt.ID))
                {
                    ++targetKpiCell.SucceedCnt;
                }
                else if (evtFailList.Contains(evt.ID))
                {
                    ++targetKpiCell.FailedCnt;
                }
            }
            return targetKpiCell;
        }

        protected virtual void getDistance(DTFileDataManager fileManager, LTECell targetCell, NbIotCellKpi targetKpiCell)
        {
            foreach (var tp in fileManager.TestPoints)
            {
                if (tp.Longitude > 10 && tp.Latitude > 10)
                {
                    double dis = tp.Distance2(targetCell.BelongBTS.Longitude, targetCell.BelongBTS.Latitude);
                    targetKpiCell.Distance = Math.Round(dis / 1000, 2);
                    break;
                }
            }
        }

        /// <summary>
        /// 过滤测试标准
        /// </summary>
        /// <param name="kpiCell"></param>
        /// <param name="fileInfo"></param>
        /// <returns></returns>
        protected virtual bool filterDataINfo(NbIotCellKpi kpiCell, FileInfo fileInfo)
        {
            if (kpiCell.RequestCnt < 5)
            {
                reportInfo(string.Format("文件{0}测试次数不足5次", fileInfo.Name));
                return false;
            }
            return true;
        }

        /// <summary>
        /// 添加用于导出的结果
        /// </summary>
        /// <param name="kpiCell"></param>
        /// <returns></returns>
        protected virtual Dictionary<NbIotKpiKeyXJ, object> getKpiInfos(NbIotCellKpi kpiCell)
        {
            Dictionary<NbIotKpiKeyXJ, object> kpiInfos = new Dictionary<NbIotKpiKeyXJ, object>();
            kpiInfos.Add(NbIotKpiKeyXJ.AttachRate, kpiCell.SuccessRate);
            kpiInfos.Add(NbIotKpiKeyXJ.AttachRateDis, kpiCell.Distance);
            return kpiInfos;
        }

        public class NbIotCellKpi
        {
            public NbIotCellKpi()
            {
            }

            public double Distance { get; set; }

            //事件请求数
            public int RequestCnt { get; set; }
            //事件成功数
            public int SucceedCnt { get; set; }
            //事件失败数
            public int FailedCnt { get; set; }
            //成功率
            public double SuccessRate
            {
                get
                {
                    if (RequestCnt != 0)
                    {
                        return (SucceedCnt * 100d / RequestCnt);
                    }
                    else if (SucceedCnt + FailedCnt != 0)
                    {
                        return (SucceedCnt * 100d / (SucceedCnt + FailedCnt));
                    }
                    else
                    {
                        return 0;
                    }
                }
            }
        }
    }

    /// <summary>
    /// 站内重选
    /// </summary>
    class NbIotAcpAutoInStationReSelectXJ : NbIotAcpAutoAttachRateXJ
    {
        public NbIotAcpAutoInStationReSelectXJ()
        {
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("站内重选");
        }

        protected override NbIotCellKpi anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            if (fileManager.Events.Count == 0)
            {
                reportInfo("没有站内重选事件");
                return null;
            }

            NbIotCellKpiReSelectRate targetKpiCell = new NbIotCellKpiReSelectRate();
            foreach (Event evt in fileManager.Events)
            {
                if (evtSuccList.Contains(evt.ID))
                {
                    ++targetKpiCell.SucceedCnt;
                }
                else if (evtFailList.Contains(evt.ID))
                {
                    ++targetKpiCell.FailedCnt;
                }
            }

            return targetKpiCell;
        }

        protected override bool filterDataINfo(NbIotCellKpi kpiCell, FileInfo fileInfo)
        {
            if (((NbIotCellKpiReSelectRate)kpiCell).TestCount < 5)
            {
                reportInfo(string.Format("文件{0}测试次数不足5次", fileInfo.Name));
                return false;
            }
            return true;
        }

        protected override Dictionary<NbIotKpiKeyXJ, object> getKpiInfos(NbIotCellKpi kpiCell)
        {
            Dictionary<NbIotKpiKeyXJ, object> kpiInfos = new Dictionary<NbIotKpiKeyXJ, object>();
            kpiInfos.Add(NbIotKpiKeyXJ.InStationReselection, ((NbIotCellKpiReSelectRate)kpiCell).SuccessRate);
            kpiInfos.Add(NbIotKpiKeyXJ.ReselectionDis, ((NbIotCellKpiReSelectRate)kpiCell).Distance);
            return kpiInfos;
        }

        protected class NbIotCellKpiReSelectRate : NbIotCellKpi
        {
            public NbIotCellKpiReSelectRate()
                : base()
            {
            }

            /// <summary>
            /// 有效的测试次数
            /// </summary>
            public int TestCount
            {
                get
                {
                    if (RequestCnt != 0)
                    {
                        return RequestCnt;
                    }
                    else if (SucceedCnt + FailedCnt != 0)
                    {
                        return SucceedCnt + FailedCnt;
                    }
                    else
                    {
                        return 0;
                    }
                }
            }
        }
    }

    /// <summary>
    /// 站间重选
    /// </summary>
    class NbIotAcpAutoBetweenStationReSelectXJ : NbIotAcpAutoInStationReSelectXJ
    {
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("站间重选");
        }

        protected override Dictionary<NbIotKpiKeyXJ, object> getKpiInfos(NbIotCellKpi kpiCell)
        {
            Dictionary<NbIotKpiKeyXJ, object> kpiInfos = new Dictionary<NbIotKpiKeyXJ, object>();
            kpiInfos.Add(NbIotKpiKeyXJ.BetweenStationReselection, ((NbIotCellKpiReSelectRate)kpiCell).SuccessRate);
            kpiInfos.Add(NbIotKpiKeyXJ.ReselectionDis, ((NbIotCellKpiReSelectRate)kpiCell).Distance);
            return kpiInfos;
        }
    }

    /// <summary>
    /// Ping成功率
    /// </summary>
    class NbIotAcpAutoPingRateXJ : NbIotAcpAutoAttachRateXJ
    {
        public NbIotAcpAutoPingRateXJ()
        {
            evtSuccList = new List<int> { 5006 };
            evtFailList = new List<int> { 5007 };
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.ToUpper().Contains("PING成功率");
        }

        protected override NbIotCellKpi anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            NbIotCellKpiPingRate targetKpiCell = new NbIotCellKpiPingRate();
            if (fileManager.Events.Count == 0)
            {
                reportInfo("没有Ping事件");
                return null;
            }

            //Event prevEvt = null;
            foreach (Event evt in fileManager.Events)
            {
                if (evtSuccList.Contains(evt.ID))
                {
                    //前后2个ping事件需要相隔10s
                    //if (prevEvt == null || evt.DateTime.Subtract(prevEvt.DateTime).TotalSeconds >= 10)
                    //{
                        ++targetKpiCell.SucceedCnt;
                    //}
                    //prevEvt = evt;
                }
                else if (evtFailList.Contains(evt.ID))
                {
                    //if (prevEvt == null || evt.DateTime.Subtract(prevEvt.DateTime).TotalSeconds >= 10)
                    //{
                        ++targetKpiCell.FailedCnt;
                    //}
                    //prevEvt = evt;
                }
            }

            return targetKpiCell;
        }

        protected override bool filterDataINfo(NbIotCellKpi kpiCell, FileInfo fileInfo)
        {
            if (((NbIotCellKpiPingRate)kpiCell).TestCount < 10)
            {
                reportInfo(string.Format("文件{0}测试次数不足10次", fileInfo.Name));
                return false;
            }
            return true;
        }

        protected override Dictionary<NbIotKpiKeyXJ, object> getKpiInfos(NbIotCellKpi kpiCell)
        {
            Dictionary<NbIotKpiKeyXJ, object> kpiInfos = new Dictionary<NbIotKpiKeyXJ, object>();
            kpiInfos.Add(NbIotKpiKeyXJ.PingRate, ((NbIotCellKpiPingRate)kpiCell).SuccessRate);
            kpiInfos.Add(NbIotKpiKeyXJ.PingRateDis, ((NbIotCellKpiPingRate)kpiCell).Distance);
            return kpiInfos;
        }

        protected class NbIotCellKpiPingRate : NbIotCellKpi
        {
            public NbIotCellKpiPingRate()
                : base()
            {
            }

            /// <summary>
            /// 有效的测试次数
            /// </summary>
            public int TestCount
            {
                get
                {
                    return SucceedCnt + FailedCnt;
                }
            }
        }
    }

    /// <summary>
    /// Ping时延
    /// </summary>
    class NbIotAcpAutoPingDelayXJ : NbIotAcpAutoAttachRateXJ
    {
        public NbIotAcpAutoPingDelayXJ()
        {
            evtSuccList = new List<int> { 5006 };
            evtFailList = new List<int> { 5007 };
        }
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.ToUpper().Contains("PING时延");
        }

        protected override NbIotCellKpi anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            NbIotCellKpiPingDelay targetKpiCell = new NbIotCellKpiPingDelay();
            if (fileManager.Events.Count == 0)
            {
                reportInfo("没有Ping事件");
                return null;
            }

            foreach (Event evt in fileManager.Events)
            {
                if (evtSuccList.Contains(evt.ID))
                {
                    targetKpiCell.TotalEvtList.Add(evt);
                }
                else if (evtFailList.Contains(evt.ID))
                {
                    targetKpiCell.TotalEvtList.Add(evt);
                }
            }
            targetKpiCell.CalculatePingDelay();

            return targetKpiCell;
        }

        protected override bool filterDataINfo(NbIotCellKpi kpiCell, FileInfo fileInfo)
        {
            //if (((NbIotCellKpiPingDelay)kpiCell).TestCount < 10)
            //{
            //    reportInfo(string.Format("文件{0}测试次数不足10次", fileInfo.Name));
            //    return false;
            //}
            return true;
        }

        protected override Dictionary<NbIotKpiKeyXJ, object> getKpiInfos(NbIotCellKpi kpiCell)
        {
            Dictionary<NbIotKpiKeyXJ, object> kpiInfos = new Dictionary<NbIotKpiKeyXJ, object>();
            kpiInfos.Add(NbIotKpiKeyXJ.PingDelay, ((NbIotCellKpiPingDelay)kpiCell).AvgDelay);
            kpiInfos.Add(NbIotKpiKeyXJ.PingDelayDis, ((NbIotCellKpiPingDelay)kpiCell).Distance);
            return kpiInfos;
        }

        protected class NbIotCellKpiPingDelay : NbIotCellKpi
        {
            public NbIotCellKpiPingDelay()
                : base()
            {
            }

            public List<Event> TotalEvtList = new List<Event>();

            /// <summary>
            /// 有效的测试次数
            /// </summary>
            public int TestCount { get; private set; }

            /// <summary>
            /// 平均时延(秒)
            /// </summary>
            public double AvgDelay { get; private set; }

            protected List<int> evtSuccList = new List<int> { 5006 };
            public void CalculatePingDelay()
            {
                double sumDelay = 0;
                int validEvtNum = 0;
                Event prevEvt = null;
                foreach (Event evt in TotalEvtList)
                {
                    //2次ping事件间隔大于2秒
                    if (prevEvt != null && evt.DateTime.Subtract(prevEvt.DateTime).TotalSeconds < 2)
                    {
                        prevEvt = evt;
                    }
                    else if (evtSuccList.Contains(evt.ID))
                    {
                        sumDelay += int.Parse(evt["Value1"].ToString());
                        prevEvt = evt;
                        validEvtNum++;
                    }
                }
                if (validEvtNum > 0)
                {
                    TestCount = validEvtNum;
                    AvgDelay = Math.Round(sumDelay / (validEvtNum * 1000), 3);
                }
                else
                {
                    AvgDelay = double.MinValue;
                }
            }
        }
    }

    /// <summary>
    /// 上传速率
    /// </summary>
    class NbIotAcpAutoULSpeedXJ : NbIotAcpAutoAttachRateXJ
    {
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("上传") && !fileInfo.Name.Contains("DT上传");
        }

        protected override NbIotCellKpi anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            NbIotCellKpiUL targetKpiCell = new NbIotCellKpiUL();
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                LTECell cell = getTpSrcCell(tp);
                if (cell != null && cell.Token == targetCell.Token)
                {
                    targetKpiCell.AddPoint(tp);
                }
            }

            return targetKpiCell;
        }

        protected override bool filterDataINfo(NbIotCellKpi kpiCell, FileInfo fileInfo)
        {
            return true;
        }

        protected override Dictionary<NbIotKpiKeyXJ, object> getKpiInfos(NbIotCellKpi kpiCell)
        {
            Dictionary<NbIotKpiKeyXJ, object> kpiInfos = new Dictionary<NbIotKpiKeyXJ, object>();
            kpiInfos.Add(NbIotKpiKeyXJ.ULAvgRSRP, ((NbIotCellKpiUL)kpiCell).AvgRsrp);
            kpiInfos.Add(NbIotKpiKeyXJ.ULAvgSINR, ((NbIotCellKpiUL)kpiCell).AvgSinr);
            kpiInfos.Add(NbIotKpiKeyXJ.ULThroughputRate, ((NbIotCellKpiUL)kpiCell).AvgMacUL);
            kpiInfos.Add(NbIotKpiKeyXJ.ULThroughputRateDis, ((NbIotCellKpiUL)kpiCell).Distance);
            return kpiInfos;
        }

        public class NbIotCellKpiUL : NbIotCellKpi
        {
            public NbIotCellKpiUL()
                : base()
            {
            }

            public int PointCount { get; protected set; }

            protected double sumSinr;
            protected int cntSinr;

            protected double sumRsrp;
            protected int cntRsrp;

            public double AvgSinr
            {
                get { return cntSinr == 0 ? double.MinValue : Math.Round(sumSinr / cntSinr, 2); }
            }
            public double AvgRsrp
            {
                get { return cntRsrp == 0 ? double.MinValue : Math.Round(sumRsrp / cntRsrp, 2); }
            }

            protected double sumMacUL;
            protected int cntMacUL;
            /// <summary>
            /// 物理层平均速率(KB)
            /// </summary>
            public double AvgMacUL
            {
                get { return cntMacUL == 0 ? double.MinValue : Math.Round(sumMacUL / (cntMacUL * 1000), 2); }
            }

            /// <summary>
            /// 是否找到速率计算的起始点(第一个速率不为0的点)
            /// </summary>
            protected bool started = false;

            public void AddPoint(TestPoint tp)
            {
                ++PointCount;

                float? sinr = (float?)tp["lte_SINR"];
                if (sinr != null && sinr != -10000000)
                {
                    ++cntSinr;
                    sumSinr += (float)sinr;
                }

                float? rsrp = (float?)tp["lte_RSRP"];
                if (rsrp != null && rsrp != -10000000)
                {
                    ++cntRsrp;
                    sumRsrp += (float)rsrp;
                }

                int? macUL = (int?)tp["lte_MAC_UL"];
                if (macUL != null)
                {
                    if (started)
                    {
                        ++cntMacUL;
                        sumMacUL += (float)macUL;
                    }
                    else if ((float)macUL != 0)
                    {
                        started = true;
                        ++cntMacUL;
                        sumMacUL += (float)macUL;
                    }
                }
            }
        }
    }

    /// <summary>
    /// 下载速率
    /// </summary>
    class NbIotAcpAutoDLSpeedXJ : NbIotAcpAutoULSpeedXJ
    {
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("下载");
        }

        protected override NbIotCellKpi anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            NbIotCellKpiDL targetKpiCell = new NbIotCellKpiDL();
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                LTECell cell = getTpSrcCell(tp);
                if (cell != null && cell.Token == targetCell.Token)
                {
                    targetKpiCell.AddPoint(tp);
                }
            }

            return targetKpiCell;
        }

        protected override Dictionary<NbIotKpiKeyXJ, object> getKpiInfos(NbIotCellKpi kpiCell)
        {
            Dictionary<NbIotKpiKeyXJ, object> kpiInfos = new Dictionary<NbIotKpiKeyXJ, object>();
            kpiInfos.Add(NbIotKpiKeyXJ.DLThroughputRate, ((NbIotCellKpiDL)kpiCell).AvgMacDL);
            kpiInfos.Add(NbIotKpiKeyXJ.DLThroughputRateDis, ((NbIotCellKpiDL)kpiCell).Distance);
            return kpiInfos;
        }

        protected override bool filterDataINfo(NbIotCellKpi kpiCell, FileInfo fileInfo)
        {
            return true;
        }

        public class NbIotCellKpiDL : NbIotCellKpiUL
        {
            public NbIotCellKpiDL()
                : base()
            {
            }

            protected double sumMacDL;
            protected int cntMacDL;
            /// <summary>
            /// 物理层平均速率(KB)
            /// </summary>
            public double AvgMacDL
            {
                get { return cntMacDL == 0 ? double.MinValue : Math.Round(sumMacDL / (cntMacDL * 1000), 2); }
            }

            public new void AddPoint(TestPoint tp)
            {
                int? macDL = (int?)tp["lte_MAC_DL"];
                if (macDL != null)
                {
                    if (started)
                    {
                        ++cntMacDL;
                        sumMacDL += (float)macDL;
                    }
                    else if ((float)macDL != 0)
                    {
                        started = true;
                        ++cntMacDL;
                        sumMacDL += (float)macDL;
                    }
                }
            }
        }
    }

    /// <summary>
    /// DT上传
    /// </summary>
    class NbIotAcpAutoCoverPictureXJ : NbIotAcpAutoULSpeedXJ
    {
        protected string picFolderPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory,
            "userdata\\NBIOTStationAcceptance_XJ");

        protected static readonly object lockObj = new object();
        private static NbIotAcpAutoCoverPictureXJ intance = null;
        public static NbIotAcpAutoCoverPictureXJ Instance
        {
            get
            {
                if (intance == null)
                {
                    lock (lockObj)
                    {
                        if (intance == null)
                        {
                            intance = new NbIotAcpAutoCoverPictureXJ();
                        }
                    }
                }
                return intance;
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("DT上传");
        }

        public override Dictionary<NbIotKpiKeyXJ, object> GetFileKpiInfos(FileInfo fileInfo
            , DTFileDataManager fileManager, LTECell targetCell)
        {
            NbIotCellKpi kpiCell = anaFile(fileInfo, fileManager, targetCell);
            if (kpiCell == null)
            {
                reportInfo(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return new Dictionary<NbIotKpiKeyXJ, object>();
            }

            try
            {
                MasterCom.MTGis.DbRect bounds = GetCoverBounds(fileManager, targetCell);
                double nearestDistance;
                TestPoint nearestTp = GetNearestTp(fileManager.TestPoints, targetCell, out nearestDistance);
                string rsrpPathr = FireMapAndTakePic("RSRP", bounds, nearestTp, targetCell);
                string sinrPathr = FireMapAndTakePic("SINR", bounds, nearestTp, targetCell);
                string macULPathr = FireMapAndTakePic("MAC_UL", bounds, nearestTp, targetCell);

                Dictionary<NbIotKpiKeyXJ, object> kpiInfos = new Dictionary<NbIotKpiKeyXJ, object>();
                kpiInfos.Add(NbIotKpiKeyXJ.CoverRsrpPath, rsrpPathr);
                kpiInfos.Add(NbIotKpiKeyXJ.CoverSinrPath, sinrPathr);
                kpiInfos.Add(NbIotKpiKeyXJ.CoverULPath, macULPathr);
                kpiInfos.Add(NbIotKpiKeyXJ.CoverAvgRSRP, ((NbIotCellKpiUL)kpiCell).AvgRsrp);
                kpiInfos.Add(NbIotKpiKeyXJ.CoverAvgSINR, ((NbIotCellKpiUL)kpiCell).AvgSinr);
                kpiInfos.Add(NbIotKpiKeyXJ.CoverAvgSpeed, ((NbIotCellKpiULPic)kpiCell).AvgULSpeed);
                kpiInfos.Add(NbIotKpiKeyXJ.CoverSpeedUpTen, ((NbIotCellKpiULPic)kpiCell).ULSpeedUpTenRate);
                kpiInfos.Add(NbIotKpiKeyXJ.CoverCoverRate, ((NbIotCellKpiULPic)kpiCell).CoverRate);
                return kpiInfos;
            }
            catch (Exception ex)
            {
                reportInfo(ex);
            }
            return new Dictionary<NbIotKpiKeyXJ, object>();
        }

        protected override NbIotCellKpi anaFile(FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            NbIotCellKpiULPic targetKpiCell = new NbIotCellKpiULPic(targetCell);

            //获取平均RSRP,SINR,判断RSRP,SINR是否满足条件
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                LTECell cell = getTpSrcCell(tp);
                if (cell != null && cell.Token == targetCell.Token)
                {
                    targetKpiCell.AddPoint(tp);
                }
            }

            return targetKpiCell;
        }

        /// <summary>
        /// 获取小区及所有采样点占用的最大范围
        /// </summary>
        /// <param name="fileManager"></param>
        /// <param name="LTECell"></param>
        /// <returns></returns>
        public MasterCom.MTGis.DbRect GetCoverBounds(DTFileDataManager fileManager, LTECell LTECell)
        {
            double lngMin = LTECell.Longitude;
            double lngMax = LTECell.Longitude;
            double latMin = LTECell.Latitude;
            double latMax = LTECell.Latitude;

            foreach (TestPoint tp in fileManager.TestPoints)
            {
                if (tp.Longitude > 70 && tp.Latitude > 3
                    && tp.GetMainCell() == LTECell)
                {
                    lngMin = Math.Min(lngMin, tp.Longitude);
                    lngMax = Math.Max(lngMax, tp.Longitude);
                    latMin = Math.Min(latMin, tp.Latitude);
                    latMax = Math.Max(latMax, tp.Latitude);
                }
            }
            MTGis.DbRect bounds = new MTGis.DbRect(lngMin - 0.001, latMin - 0.001
                , lngMax + 0.001, latMax + 0.001);

            return bounds;
        }
        public string FireMapAndTakePic(string paramName, MTGis.DbRect bounds, TestPoint nearestTp
            , LTECell srcLTECell)
        {
            if (srcLTECell == null)
            {
                return "";
            }

            MainModel mModel = MainModel.GetInstance();
            mModel.FireSetDefaultMapSerialTheme("LTE_TDD", paramName);
            mModel.DrawFlyLines = false;

            var mf = mModel.MainForm.GetMapForm();

            foreach (MapSerialInfo serialInfo in mModel.MainForm.GetMapForm().GetDTLayer().SerialInfos)
            {
                if (serialInfo.Name.Equals(paramName))
                {
                    mf.GetDTLayer().CurFlyLinesSerialInfo = serialInfo;
                    break;
                }
            }

            mModel.DrawLinesPntToCells = true;
            mModel.PntToCellsDic.Clear();
            List<ZTFunc.LongLat> longlatList = new List<ZTFunc.LongLat>();
            mModel.PntToCellsDic.Add(nearestTp, longlatList);
            longlatList.Add(new ZTFunc.LongLat((float)srcLTECell.EndPointLongitude
                , (float)srcLTECell.EndPointLatitude));

            mModel.FireDTDataChanged(mModel.MainForm);

            MapLTECellLayer lteLayer = mf.GetLTECellLayer();
            mf.MakeSureCustomLayerVisible(lteLayer, true);
            mf.GoToView(bounds);

            return takePicture(srcLTECell.BTSName, srcLTECell.Name, paramName, mf);
        }

        #region 覆盖截图
        private string takePicture(string btsName, string cellName, string paramName, MapForm mf)
        {
            string filePath = GetCoverPicPath(btsName, cellName, paramName);
            if (System.IO.Directory.Exists(filePath))//删除本站之前的覆盖截图
            {
                System.IO.Directory.Delete(filePath, true);
            }

            Bitmap bitMap = mf.DrawToBitmapDIY();
            bitMap.Save(filePath, ImageFormat.Png);
            bitMap.Dispose();
            return filePath;
        }

        /// <summary>
        /// 获取小区某种覆盖截图的保存路径
        /// </summary>
        /// <param name="btsName"></param>
        /// <param name="cellName"></param>
        /// <param name="postfix"></param>
        /// <returns></returns>
        public string GetCoverPicPath(string btsName, string cellName, string paramName)
        {
            string folderPath = GetBtsPicFolder(btsName);
            if (!System.IO.Directory.Exists(folderPath))
            {
                System.IO.Directory.CreateDirectory(folderPath);
            }
            return System.IO.Path.Combine(folderPath, cellName + "_" + paramName + ".png");
        }
        /// <summary>
        /// 获取站点覆盖截图保存文件夹地址
        /// </summary>
        /// <param name="btsName"></param>
        /// <returns></returns>
        public string GetBtsPicFolder(string btsName)
        {
            return System.IO.Path.Combine(picFolderPath, btsName.Trim());
        }
        #endregion

        /// <summary>
        /// 最近的采样点
        /// </summary>
        /// <param name="testPoints"></param>
        /// <param name="nbiotCell"></param>
        /// <param name="nearestDistance"></param>
        /// <returns></returns>
        public TestPoint GetNearestTp(List<TestPoint> testPoints, LTECell nbiotCell, out double nearestDistance)
        {
            nearestDistance = double.MaxValue;
            TestPoint nearestTp = null;
            foreach (TestPoint tp in testPoints)
            {
                if (tp["lte_RSRP"] != null && tp["lte_SINR"] != null && tp["lte_MAC_UL"] != null)
                {
                    double curDistance = tp.Distance2(nbiotCell.Longitude, nbiotCell.Latitude);
                    if (curDistance < nearestDistance)
                    {
                        nearestDistance = curDistance;
                        nearestTp = tp;
                    }
                }
            }
            return nearestTp;
        }

        public static void InsertExcelPicture(Workbook eBook, string startCell, string picPath)
        {
            Worksheet eSheet = (Worksheet)eBook.Sheets[7];
            Range rng = eSheet.get_Range(startCell, Type.Missing);

            double width = eBook.Application.CentimetersToPoints(12.75);
            double height = eBook.Application.CentimetersToPoints(6.62);
            eSheet.Shapes.AddPicture(picPath,
                Microsoft.Office.Core.MsoTriState.msoFalse,
                Microsoft.Office.Core.MsoTriState.msoCTrue,
                (float)(double)rng.Left, (float)(double)rng.Top,
                (float)width, (float)height);
        }


        public class NbIotCellKpiULPic : NbIotCellKpiUL
        {
            public NbIotCellKpiULPic(LTECell cell)
                : base()
            {
                switch (cell.DESC)
                {
                    case "主城区高":
                        rsrpLimit = -84;
                        break;
                    case "主城区低":
                    case "一般城区":
                        rsrpLimit = -87;
                        break;
                    case "县城及乡镇":
                        rsrpLimit = -89;
                        break;
                    default:
                        rsrpLimit = -84;
                        break;
                }
            }
            /// <summary>
            /// 覆盖率判断阈值
            /// </summary>
            protected int rsrpLimit;
            protected double sumULSpeed;
            protected int cntULSpeed;
            protected int cntULSpeedUpTen;

            /// <summary>
            /// 物理层平均速率(KB)
            /// </summary>
            public double AvgULSpeed
            {
                get { return cntULSpeed == 0 ? double.MinValue : Math.Round(sumULSpeed / (cntULSpeed * 1000), 2); }
            }

            /// <summary>
            /// 速率大于10k的占比
            /// </summary>
            public double ULSpeedUpTenRate
            {
                get { return cntULSpeed == 0 ? double.MinValue : Math.Round(cntULSpeedUpTen * 100d / cntULSpeed, 2); }
            }

            /// <summary>
            /// 覆盖率
            /// </summary>
            public double CoverRate
            {
                get { return cntRsrp == 0 ? double.MinValue : Math.Round(cntRsrpReachLimit * 100d / cntRsrp, 2); }
            }


            /// <summary>
            /// 达到覆盖率阈值的采样点数
            /// </summary>
            protected int cntRsrpReachLimit;

            public new void AddPoint(TestPoint tp)
            {
                AddTestPointSinr(tp);

                AddTestPointRsrp(tp);

                AddTestPointSpeed(tp);
            }

            private void AddTestPointSinr(TestPoint tp)
            {
                float? sinr = (float?)tp["lte_SINR"];
                if (sinr != null && sinr != -10000000)
                {
                    ++cntSinr;
                    sumSinr += (float)sinr;
                }
            }

            private void AddTestPointRsrp(TestPoint tp)
            {
                float? rsrp = (float?)tp["lte_RSRP"];
                if (rsrp != null && rsrp != -10000000)
                {
                    ++cntRsrp;
                    sumRsrp += (float)rsrp;
                    //统计达到覆盖率阈值的采样点数
                    if (rsrp >= rsrpLimit)
                    {
                        cntRsrpReachLimit++;
                    }
                }
            }

            private void AddTestPointSpeed(TestPoint tp)
            {
                int? macUL = (int?)tp["lte_MAC_UL"];
                if (macUL != null)
                {
                    if (started)
                    {
                        ++cntULSpeed;
                        sumULSpeed += (float)macUL;
                        //速率大于10k
                        if (macUL >= 10000)
                        {
                            cntULSpeedUpTen++;
                        }
                    }
                    else if ((float)macUL != 0)
                    {
                        started = true;
                        ++cntULSpeed;
                        sumULSpeed += (float)macUL;
                        //速率大于10k
                        if (macUL >= 10000)
                        {
                            cntULSpeedUpTen++;
                        }
                    }
                }
            }
        }
    }
    #endregion
}
