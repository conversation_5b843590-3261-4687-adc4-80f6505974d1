﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid.ViewInfo;
using DevExpress.XtraGrid.Views.Base;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class EleRadioAndLACCI : DevExpress.XtraEditors.XtraForm
    {
        private string mainSelectedEleName;
        public string MainSelectedEleName
        {
            get
            {
                return mainSelectedEleName;
            }
            set
            {
                this.Text = value;
                mainSelectedEleName = value;
            }
        }

        public DateTime TimeStart { get; set; }
        public DateTime TimeEnd { get; set; }

        private MainModel mainModel;
        private bool IsTDInf;
        public EleRadioAndLACCI(MainModel mainModel,bool IsTDInf)
        {
            InitializeComponent();
            this.mainModel = mainModel;
            this.IsTDInf = IsTDInf;
            IsFirstFocusedRowChanged = true;
        }
        
        public List<EleDetailInf> DetailInf { get; set; }
        DataTable eleRadioDT;
        private void EleRadioAndLACCI_Load(object sender, EventArgs e)
        {
            if (this.DetailInf != null && this.DetailInf.Count != 0)
            {
                if (eleRadioDT == null)
                {
                    eleRadioDT = new DataTable();
                    eleRadioDT.Columns.Add("LAC");
                    eleRadioDT.Columns.Add("CI");
                    eleRadioDT.Columns.Add("Radio");
                }
                eleRadioDT.Rows.Clear();
                for (int i = 0; i < DetailInf.Count; i++)
                {
                    eleRadioDT.Rows.Add(new object[] { DetailInf[i].LAC,DetailInf[i].CI,(DetailInf[i].Radio*100).ToString("0.00")+"%"});
                }
                this.gridControlRes.DataSource = eleRadioDT;
            }
        }
        //性能数据详细
        private PerCellDetailShowGSM cellDetailShowGSM;
        private PerCellDetailShowTD cellDetailShowTD;
        private int selectedLAC;
        private int selectedCI;
        private List<GSMPerDetailEleInf> deailInfGSM;
        private List<TDPerDetailEleInf> detailInfTD;
        private void GetDetailInf()
        {
            WaitBox.ProgressPercent = 25;
            PerDetailEleInfQuery detailQuery = new PerDetailEleInfQuery(mainModel, IsTDInf);
            detailQuery.TimeStart = TimeStart;
            detailQuery.TimeEnd = TimeEnd;
            detailQuery.LAC = selectedLAC;
            detailQuery.CI = selectedCI;
            detailQuery.Query();
            deailInfGSM = detailQuery.DetailInfGSM;
            detailInfTD = detailQuery.DetailInfTD;
            WaitBox.ProgressPercent = 100;
            WaitBox.Close();
        }

        private void EleRadioAndLACCI_LocationChanged(object sender, EventArgs e)
        {
            if (cellDetailShowGSM != null)
            {
                cellDetailShowGSM.Location = new Point(this.Location.X + this.Width - 5, this.Location.Y);
            }
            if (cellDetailShowTD != null)
            {
                cellDetailShowTD.Location = new Point(this.Location.X + this.Width - 5, this.Location.Y);
            }
        }


        private bool IsFirstFocusedRowChanged;
        private void gridViewForCellRadio_FocusedRowChanged(object sender, FocusedRowChangedEventArgs e)
        {
            if (IsFirstFocusedRowChanged)
            {
                IsFirstFocusedRowChanged = false;
                return;
            }
            if (eleRadioDT == null || eleRadioDT.Rows.Count == 0)
            {
                return;
            }

            int rowIndex = gridViewForCellRadio.FocusedRowHandle;
            if (rowIndex > eleRadioDT.Rows.Count)
            {
                return;
            }
            selectedLAC = Convert.ToInt32(eleRadioDT.Rows[rowIndex]["LAC"]);
            selectedCI = Convert.ToInt32(eleRadioDT.Rows[rowIndex]["CI"]);
            WaitBox.Show(GetDetailInf);
            if (IsTDInf)
            {
                if (detailInfTD != null)
                {
                    cellDetailShowTD = new PerCellDetailShowTD();
                    cellDetailShowTD.DeailInfTD = detailInfTD;
                    cellDetailShowTD.MainSelectedEleName = MainSelectedEleName;
                    cellDetailShowTD.Location = new Point(this.Location.X +this.Width-5, this.Location.Y);
                    cellDetailShowTD.Show();
                }
            }
            else
            {
                if (deailInfGSM != null)
                {
                    cellDetailShowGSM = new PerCellDetailShowGSM();
                    cellDetailShowGSM.DeailInfGSM = deailInfGSM;
                    cellDetailShowGSM.MainSelectedEleName = MainSelectedEleName;
                    cellDetailShowGSM.Location = new Point(this.Location.X + this.Width - 5, this.Location.Y);
                    cellDetailShowGSM.Show();
                }
            }
        }      
    }
}