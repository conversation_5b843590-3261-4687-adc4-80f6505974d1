﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc;
using GMap.NET;
using System.Drawing;

namespace MasterCom.RAMS.ExMap
{
    public class ExMapPCIOptimizeLayer : ExMapDrawBaseLayer
    {
        MainModel mModel { get; set; }
        MapForm mapForm = null;
        PCIOptimizeLayer pciLayer = null;
        public ExMapPCIOptimizeLayer(MTExGMap exMap)
            : base(exMap)
        {
            mModel = MainModel.GetInstance();
        }

        public override string Alias
        {
            get { return "PCI优化"; }
        }

        public override void Draw(System.Drawing.Graphics g, GMap.NET.PointLatLng ltPt, GMap.NET.PointLatLng brPt)
        {
            if (mapForm == null)
            {
                mapForm = exMap.ParentExMapFormPanel.getMapFormInstance();
            }
            if (mapForm == null) return;
            pciLayer = mapForm.GetCustomLayer(typeof(PCIOptimizeLayer)) as PCIOptimizeLayer;
            if (pciLayer == null || !pciLayer.IsVisible)
            {
                return;
            }

            if (pciLayer.Obj != null)
            {
                bool isValid = drawResultPntWithLine(g);
                if (!isValid)
                {
                    return;
                }
            }
            foreach (SINRToPCI sinr in pciLayer.AllPntList)
            {
                drawPnt(g, sinr);
            }
        }

        private bool drawResultPntWithLine(Graphics g)
        {
            if (pciLayer.Obj is ArrangeResult)
            {
                ArrangeResult aResult = pciLayer.Obj as ArrangeResult;
                if (aResult == null || aResult.mainCell == null)
                {
                    return false;
                }

                foreach (SINRToPCI sinr in aResult.SinrList)
                {
                    if (sinr.GetNBCell() != null)
                    {
                        drawPntWithLine(g, sinr);
                    }
                }
            }
            else if (pciLayer.Obj is SINRToPCI)
            {
                SINRToPCI sinr = pciLayer.Obj as SINRToPCI;
                drawPntWithLine(g, sinr);
            }
            else if (pciLayer.Obj is List<SINRToPCI>)
            {
                List<SINRToPCI> sinrList = pciLayer.Obj as List<SINRToPCI>;
                if (sinrList == null)
                {
                    return false;
                }
                foreach (SINRToPCI sinr in sinrList)
                {
                    drawPntWithLine(g, sinr);
                }
            }
            return true;
        }

        private void drawPnt(System.Drawing.Graphics graphics, SINRToPCI sinr)
        {
            PointLatLng ptLLPoint = new PointLatLng(sinr.latitude, sinr.longitude);
            GPoint point = exMap.FromLatLngToLocalAdaptered(ptLLPoint);
            PointF pointF = new PointF(point.X, point.Y);
            graphics.FillEllipse(sinr.GetBrush(), pointF.X - 6, pointF.Y - 6, 12, 12);
        }

        private void drawPntWithLine(System.Drawing.Graphics graphics, SINRToPCI sinr)
        {
            if (sinr.mainCell == null || sinr.GetNBCell() == null) return;

            MasterCom.MTGis.DbPoint pntMCell = sinr.GetLTEAntennaEndPoint(sinr.mainCell);
            PointLatLng ptLLPointMCell = new PointLatLng(pntMCell.y, pntMCell.x);
            GPoint pointMCell = exMap.FromLatLngToLocalAdaptered(ptLLPointMCell);
            PointF pntFMCell = new PointF(pointMCell.X, pointMCell.Y);

            MasterCom.MTGis.DbPoint pntNBCell = sinr.GetLTEAntennaEndPoint(sinr.GetNBCell());
            PointLatLng ptLLPointNCell = new PointLatLng(pntNBCell.y, pntNBCell.x);
            GPoint pointNCell = exMap.FromLatLngToLocalAdaptered(ptLLPointNCell);
            PointF pntFNCell = new PointF(pointNCell.X, pointNCell.Y);

            PointLatLng ptLLPointSinr = new PointLatLng(sinr.latitude, sinr.longitude);
            GPoint pointSinr = exMap.FromLatLngToLocalAdaptered(ptLLPointSinr);
            PointF pntFSinr = new PointF(pointSinr.X, pointSinr.Y);

            graphics.DrawLine(new Pen(Color.Blue, 1), pntFSinr, pntFMCell);
            graphics.DrawLine(new Pen(Color.Yellow, 1), pntFSinr, pntFNCell);

            graphics.FillEllipse(sinr.GetBrush(), pntFSinr.X - 6, pntFSinr.Y - 6, 12, 12);
        }
    }
}
