using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYScanRxlevAndBcchQueryByRegion : DIYSampleByRegion
    {
        public int RxlevThreshold { get; set; }
        public int BcchStart { get; set; }
        public int BcchEnd { get; set; }
        public List<RxlevBcchInfo> rxlevBcchInfoList { get; set; } = new List<RxlevBcchInfo>();
        public ZTDIYScanRxlevAndBcchQueryByRegion(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 15000, 15032, this.Name);
        }

        protected override bool getConditionBeforeQuery()
        {
            ScanRxlevAndBcchSetForm rxlevBcchDlg = new ScanRxlevAndBcchSetForm();
            if (rxlevBcchDlg.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            rxlevBcchInfoList.Clear();
            MainModel.DTDataManager.Clear();
            RxlevThreshold = rxlevBcchDlg.RxlevThreShold;
            BcchStart = rxlevBcchDlg.BcchStart;
            BcchEnd = rxlevBcchDlg.BcchEnd;
            return true;
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_RxLev";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_BCCH";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "GSCAN_BSIC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "SCAN_FreqSp_Frequency";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "SCAN_FreqSp_Pwr";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"GSM_SCAN_RxLev");
            tmpDic.Add("themeName", (object)"GSM_SCAN_RxLev");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            if (tp is ScanTestPoint_G || tp is ScanTestPoint_FreqSpecturm)
            {
                return base.isValidTestPoint(tp);
            }
            return false;
        }

        protected override void doWithDTData(TestPoint tp)
        {
            try
            {
                bool isAdd = false;
                isAdd = addTPInfo(tp, isAdd);
                if (isAdd)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            catch
            {
                //continue
            }
        }

        private bool addTPInfo(TestPoint tp, bool isAdd)
        {
            for (int i = 0; i < 50; i++)
            {
                if (tp is ScanTestPoint_G)
                {
                    float? rxlev = (float?)tp["GSCAN_RxLev", i];
                    int? bcch = (int?)tp["GSCAN_BCCH", i];
                    int? bsic = (int?)tp["GSCAN_BSIC", i];

                    if (rxlev == null || rxlev < -140 || rxlev > -10 || bcch == null)
                    {
                        break;
                    }
                    isAdd = addRxlevBcchInfo(tp, isAdd, rxlev, bcch, bsic);
                }
                else if (tp is ScanTestPoint_FreqSpecturm)
                {
                    float? pwr = (float?)tp["SCAN_FreqSp_Pwr", i];
                    int? freq = (int?)tp["SCAN_FreqSp_Frequency", i];
                    if (pwr == null || pwr < -150 || pwr > -25 || freq == null)
                    {
                        break;
                    }
                    isAdd = addRxlevBcchInfo(tp, isAdd, pwr, freq);
                }
            }

            return isAdd;
        }

        private bool addRxlevBcchInfo(TestPoint tp, bool isAdd, float? pwr, int? freq)
        {
            if (pwr >= RxlevThreshold && freq >= BcchStart && freq <= BcchEnd)
            {
                isAdd = true;
                RxlevBcchInfo rbInfo = new RxlevBcchInfo(null, (int)freq, null, (float)pwr, tp);
                rxlevBcchInfoList.Add(rbInfo);
                rbInfo.sn = rxlevBcchInfoList.Count;
            }

            return isAdd;
        }

        private bool addRxlevBcchInfo(TestPoint tp, bool isAdd, float? rxlev, int? bcch, int? bsic)
        {
            if (rxlev >= RxlevThreshold && bcch >= BcchStart && bcch <= BcchEnd)
            {
                isAdd = true;
                Cell cell = CellManager.GetInstance().GetNearestCell(tp.DateTime, (short?)bcch, (byte?)bsic, tp.Longitude, tp.Latitude, null, null, null, null);
                RxlevBcchInfo rbInfo = new RxlevBcchInfo(cell, (int)bcch, bsic, (float)rxlev, tp);
                rxlevBcchInfoList.Add(rbInfo);
                rbInfo.sn = rxlevBcchInfoList.Count;
            }

            return isAdd;
        }

        protected override void FireShowFormAfterQuery()
        {
            fireShowForm(rxlevBcchInfoList);
            if (rxlevBcchInfoList.Count > 0)
            {
                if (rxlevBcchInfoList[0].testPoint is ScanTestPoint_G)
                    MainModel.FireSetDefaultMapSerialTheme("GSM_SCAN_RxLev");
                else if (rxlevBcchInfoList[0].testPoint is ScanTestPoint_FreqSpecturm)
                    MainModel.FireSetDefaultMapSerialTheme("SCAN_FreqSp_Pwr");
            }
        }

        protected void fireShowForm(List<RxlevBcchInfo> rxlevBcchInfoList)
        {
            ScanRxlevAndBcchInfoForm frm = MainModel.GetInstance().CreateResultForm(typeof(ScanRxlevAndBcchInfoForm)) as ScanRxlevAndBcchInfoForm;
            frm.Owner = MainModel.MainForm;
            frm.FillData(rxlevBcchInfoList);
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class RxlevBcchInfo
    {
        public int sn { get; set; }
        public Cell cell { get; set; }
        public int bcch { get; set; }
        public string bsic { get; set; }
        public float rxlev { get; set; }
        public TestPoint testPoint { get; set; }

        public RxlevBcchInfo(Cell cell, int bcch, int? bsic, float rxlev, TestPoint testPoint)
        {
            this.cell = cell;
            this.bcch = bcch;
            this.bsic = bsic == null ? "" : bsic.ToString();
            this.rxlev = rxlev;
            this.testPoint = testPoint;
        }
    }
}
