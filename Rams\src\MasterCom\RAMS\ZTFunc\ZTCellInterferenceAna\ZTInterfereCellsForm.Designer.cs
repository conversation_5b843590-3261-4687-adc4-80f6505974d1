﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTInterfereCellsForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.panel1 = new System.Windows.Forms.Panel();
            this.label8 = new System.Windows.Forms.Label();
            this.numAngleMin = new System.Windows.Forms.NumericUpDown();
            this.label7 = new System.Windows.Forms.Label();
            this.numBeam = new System.Windows.Forms.NumericUpDown();
            this.label6 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.numAngleMax = new System.Windows.Forms.NumericUpDown();
            this.label5 = new System.Windows.Forms.Label();
            this.chkCoBSIC = new System.Windows.Forms.CheckBox();
            this.label3 = new System.Windows.Forms.Label();
            this.btnOK = new System.Windows.Forms.Button();
            this.numDistance = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.chkAdjFreq = new System.Windows.Forms.CheckBox();
            this.chkCoFreq = new System.Windows.Forms.CheckBox();
            this.cbxInterfereType = new System.Windows.Forms.ComboBox();
            this.label1 = new System.Windows.Forms.Label();
            this.treeListView = new BrightIdeasSoftware.TreeListView();
            this.olvColumnCellName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLAC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBCCH = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBSIC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnTCH = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDistance = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnInterAngle = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnIsInBeam = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportToExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numAngleMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numBeam)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numAngleMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.label8);
            this.panel1.Controls.Add(this.numAngleMin);
            this.panel1.Controls.Add(this.label7);
            this.panel1.Controls.Add(this.numBeam);
            this.panel1.Controls.Add(this.label6);
            this.panel1.Controls.Add(this.label4);
            this.panel1.Controls.Add(this.numAngleMax);
            this.panel1.Controls.Add(this.label5);
            this.panel1.Controls.Add(this.chkCoBSIC);
            this.panel1.Controls.Add(this.label3);
            this.panel1.Controls.Add(this.btnOK);
            this.panel1.Controls.Add(this.numDistance);
            this.panel1.Controls.Add(this.label2);
            this.panel1.Controls.Add(this.chkAdjFreq);
            this.panel1.Controls.Add(this.chkCoFreq);
            this.panel1.Controls.Add(this.cbxInterfereType);
            this.panel1.Controls.Add(this.label1);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(1026, 45);
            this.panel1.TabIndex = 0;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(603, 16);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(17, 12);
            this.label8.TabIndex = 14;
            this.label8.Text = "度";
            // 
            // numAngleMin
            // 
            this.numAngleMin.Increment = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numAngleMin.Location = new System.Drawing.Point(547, 11);
            this.numAngleMin.Maximum = new decimal(new int[] {
            180,
            0,
            0,
            0});
            this.numAngleMin.Name = "numAngleMin";
            this.numAngleMin.Size = new System.Drawing.Size(52, 21);
            this.numAngleMin.TabIndex = 13;
            this.numAngleMin.Value = new decimal(new int[] {
            90,
            0,
            0,
            0});
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(893, 16);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(29, 12);
            this.label7.TabIndex = 12;
            this.label7.Text = "度内";
            // 
            // numBeam
            // 
            this.numBeam.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numBeam.Location = new System.Drawing.Point(837, 11);
            this.numBeam.Maximum = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.numBeam.Name = "numBeam";
            this.numBeam.Size = new System.Drawing.Size(52, 21);
            this.numBeam.TabIndex = 11;
            this.numBeam.Value = new decimal(new int[] {
            45,
            0,
            0,
            0});
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(759, 16);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(77, 12);
            this.label6.TabIndex = 10;
            this.label6.Text = "主瓣为小区±";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(728, 16);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(17, 12);
            this.label4.TabIndex = 9;
            this.label4.Text = "度";
            // 
            // numAngleMax
            // 
            this.numAngleMax.Increment = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numAngleMax.Location = new System.Drawing.Point(672, 11);
            this.numAngleMax.Maximum = new decimal(new int[] {
            180,
            0,
            0,
            0});
            this.numAngleMax.Name = "numAngleMax";
            this.numAngleMax.Size = new System.Drawing.Size(52, 21);
            this.numAngleMax.TabIndex = 7;
            this.numAngleMax.Value = new decimal(new int[] {
            180,
            0,
            0,
            0});
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(620, 16);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(53, 12);
            this.label5.TabIndex = 8;
            this.label5.Text = "≤夹角≤";
            // 
            // chkCoBSIC
            // 
            this.chkCoBSIC.AutoSize = true;
            this.chkCoBSIC.Location = new System.Drawing.Point(324, 14);
            this.chkCoBSIC.Name = "chkCoBSIC";
            this.chkCoBSIC.Size = new System.Drawing.Size(60, 16);
            this.chkCoBSIC.TabIndex = 6;
            this.chkCoBSIC.Text = "同BSIC";
            this.chkCoBSIC.UseVisualStyleBackColor = true;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(504, 16);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(17, 12);
            this.label3.TabIndex = 5;
            this.label3.Text = "米";
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(934, 6);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(87, 27);
            this.btnOK.TabIndex = 4;
            this.btnOK.Text = "查询";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // numDistance
            // 
            this.numDistance.Increment = new decimal(new int[] {
            500,
            0,
            0,
            0});
            this.numDistance.Location = new System.Drawing.Point(446, 11);
            this.numDistance.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numDistance.Name = "numDistance";
            this.numDistance.Size = new System.Drawing.Size(52, 21);
            this.numDistance.TabIndex = 3;
            this.numDistance.Value = new decimal(new int[] {
            2000,
            0,
            0,
            0});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(404, 16);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(41, 12);
            this.label2.TabIndex = 4;
            this.label2.Text = "距离≤";
            // 
            // chkAdjFreq
            // 
            this.chkAdjFreq.AutoSize = true;
            this.chkAdjFreq.Location = new System.Drawing.Point(268, 14);
            this.chkAdjFreq.Name = "chkAdjFreq";
            this.chkAdjFreq.Size = new System.Drawing.Size(48, 16);
            this.chkAdjFreq.TabIndex = 2;
            this.chkAdjFreq.Text = "邻频";
            this.chkAdjFreq.UseVisualStyleBackColor = true;
            // 
            // chkCoFreq
            // 
            this.chkCoFreq.AutoSize = true;
            this.chkCoFreq.Location = new System.Drawing.Point(214, 14);
            this.chkCoFreq.Name = "chkCoFreq";
            this.chkCoFreq.Size = new System.Drawing.Size(48, 16);
            this.chkCoFreq.TabIndex = 1;
            this.chkCoFreq.Text = "同频";
            this.chkCoFreq.UseVisualStyleBackColor = true;
            // 
            // cbxInterfereType
            // 
            this.cbxInterfereType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxInterfereType.FormattingEnabled = true;
            this.cbxInterfereType.Items.AddRange(new object[] {
            "BCCH & TCH",
            "BCCH Only",
            "TCH Only"});
            this.cbxInterfereType.Location = new System.Drawing.Point(64, 12);
            this.cbxInterfereType.Name = "cbxInterfereType";
            this.cbxInterfereType.Size = new System.Drawing.Size(140, 20);
            this.cbxInterfereType.TabIndex = 0;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(5, 16);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(53, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "干扰类型";
            // 
            // treeListView
            // 
            this.treeListView.AllColumns.Add(this.olvColumnCellName);
            this.treeListView.AllColumns.Add(this.olvColumnLAC);
            this.treeListView.AllColumns.Add(this.olvColumnCI);
            this.treeListView.AllColumns.Add(this.olvColumnBCCH);
            this.treeListView.AllColumns.Add(this.olvColumnBSIC);
            this.treeListView.AllColumns.Add(this.olvColumnTCH);
            this.treeListView.AllColumns.Add(this.olvColumnDistance);
            this.treeListView.AllColumns.Add(this.olvColumnInterAngle);
            this.treeListView.AllColumns.Add(this.olvColumnIsInBeam);
            this.treeListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnCellName,
            this.olvColumnLAC,
            this.olvColumnCI,
            this.olvColumnBCCH,
            this.olvColumnBSIC,
            this.olvColumnTCH,
            this.olvColumnDistance,
            this.olvColumnInterAngle,
            this.olvColumnIsInBeam});
            this.treeListView.ContextMenuStrip = this.contextMenuStrip;
            this.treeListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListView.FullRowSelect = true;
            this.treeListView.GridLines = true;
            this.treeListView.Location = new System.Drawing.Point(0, 45);
            this.treeListView.MultiSelect = false;
            this.treeListView.Name = "treeListView";
            this.treeListView.OwnerDraw = true;
            this.treeListView.ShowGroups = false;
            this.treeListView.Size = new System.Drawing.Size(1026, 388);
            this.treeListView.TabIndex = 1;
            this.treeListView.UseCompatibleStateImageBehavior = false;
            this.treeListView.View = System.Windows.Forms.View.Details;
            this.treeListView.VirtualMode = true;
            this.treeListView.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.treeListView_MouseDoubleClick);
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名";
            this.olvColumnCellName.Width = 150;
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "LAC";
            this.olvColumnLAC.Width = 80;
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI";
            this.olvColumnCI.Width = 80;
            // 
            // olvColumnBCCH
            // 
            this.olvColumnBCCH.HeaderFont = null;
            this.olvColumnBCCH.Text = "BCCH";
            this.olvColumnBCCH.Width = 80;
            // 
            // olvColumnBSIC
            // 
            this.olvColumnBSIC.HeaderFont = null;
            this.olvColumnBSIC.Text = "BSIC";
            this.olvColumnBSIC.Width = 80;
            // 
            // olvColumnTCH
            // 
            this.olvColumnTCH.HeaderFont = null;
            this.olvColumnTCH.Text = "TCH";
            this.olvColumnTCH.Width = 300;
            // 
            // olvColumnDistance
            // 
            this.olvColumnDistance.HeaderFont = null;
            this.olvColumnDistance.Text = "距离";
            this.olvColumnDistance.Width = 80;
            // 
            // olvColumnInterAngle
            // 
            this.olvColumnInterAngle.HeaderFont = null;
            this.olvColumnInterAngle.Text = "夹角";
            this.olvColumnInterAngle.Width = 80;
            // 
            // olvColumnIsInBeam
            // 
            this.olvColumnIsInBeam.HeaderFont = null;
            this.olvColumnIsInBeam.Text = "在主瓣内";
            this.olvColumnIsInBeam.Width = 80;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll,
            this.miCollapseAll,
            this.miExportToExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 70);
            this.contextMenuStrip.Opening += new System.ComponentModel.CancelEventHandler(this.contextMenuStrip_Opening);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(129, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCollapseAll
            // 
            this.miCollapseAll.Name = "miCollapseAll";
            this.miCollapseAll.Size = new System.Drawing.Size(129, 22);
            this.miCollapseAll.Text = "全部折叠";
            this.miCollapseAll.Click += new System.EventHandler(this.miCollapseAll_Click);
            // 
            // miExportToExcel
            // 
            this.miExportToExcel.Name = "miExportToExcel";
            this.miExportToExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportToExcel.Text = "导出Excel";
            this.miExportToExcel.Click += new System.EventHandler(this.miExportToExcel_Click);
            // 
            // ZTInterfereCellsForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1026, 433);
            this.Controls.Add(this.treeListView);
            this.Controls.Add(this.panel1);
            this.Name = "ZTInterfereCellsForm";
            this.Text = "同邻频干扰";
            this.Load += new System.EventHandler(this.ZTInterfereCellsForm_Load);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numAngleMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numBeam)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numAngleMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel1;
        private BrightIdeasSoftware.TreeListView treeListView;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnBCCH;
        private BrightIdeasSoftware.OLVColumn olvColumnTCH;
        private System.Windows.Forms.NumericUpDown numDistance;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.CheckBox chkAdjFreq;
        private System.Windows.Forms.CheckBox chkCoFreq;
        private System.Windows.Forms.ComboBox cbxInterfereType;
        private System.Windows.Forms.Label label1;
        private BrightIdeasSoftware.OLVColumn olvColumnDistance;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcel;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll;
        private System.Windows.Forms.CheckBox chkCoBSIC;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC;
        private BrightIdeasSoftware.OLVColumn olvColumnInterAngle;
        private BrightIdeasSoftware.OLVColumn olvColumnIsInBeam;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.NumericUpDown numBeam;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown numAngleMax;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.NumericUpDown numAngleMin;
    }
}