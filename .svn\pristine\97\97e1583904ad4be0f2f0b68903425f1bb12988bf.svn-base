﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Xml.Serialization;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// GSM测试指标门限
    /// </summary>
    [XmlRoot("GSM测试指标门限")]
    public class GSMTestThreadholdInf
    {
        [XmlArray("items"), XmlArrayItem("item")]
        public IndicatorThresholdInf[] ThresholdInf { get; set; }
        public GSMTestThreadholdInf()
        {
            ThresholdInf = new IndicatorThresholdInf[14];
            ThresholdInf[0] = new IndicatorThresholdInf("平均车速");
            ThresholdInf[1] = new IndicatorThresholdInf("接通率");
            ThresholdInf[2] = new IndicatorThresholdInf("掉话率");

            ThresholdInf[3] = new IndicatorThresholdInf("切换成功率");
            ThresholdInf[4] = new IndicatorThresholdInf("90覆盖率");
            ThresholdInf[5] = new IndicatorThresholdInf("质量0-4级占比");

            ThresholdInf[6] = new IndicatorThresholdInf("MOS2.8以上占比");
            ThresholdInf[7] = new IndicatorThresholdInf("半速率占比");
            ThresholdInf[8] = new IndicatorThresholdInf("GSM语音质量");

            ThresholdInf[9] = new IndicatorThresholdInf("质差路段占比");
            ThresholdInf[10] = new IndicatorThresholdInf("每呼切换次数");
            ThresholdInf[11] = new IndicatorThresholdInf("FTP下载速率");

            ThresholdInf[12] = new IndicatorThresholdInf("高编码占比");
            ThresholdInf[13] = new IndicatorThresholdInf("BLER占比");
        }
    }
}
