﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func.CoverageCheck;
using MasterCom.RAMS.Grid;
using MasterCom.MTGis;
using MasterCom.Util.UiEx;
using MapWinGIS;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Compare_Foreground;
using BrightIdeasSoftware;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS
{
    public partial class CPUnitShowForm : MinCloseForm
    {
        private QueryCondition condition;
        private QueryCondition conditionTPHost;
        private QueryCondition condtionTPGuest;
        private MainModel mainModel;
        protected MapFormItemSelection ItemSelection;

        public CPUnitShowForm(MainModel mainModel, MapFormItemSelection ItemSelection, QueryCondition condition, 
            QueryCondition conditionTPHost, QueryCondition conditionTPGuest) : base(mainModel)
        {
            InitializeComponent();
            listView.ListViewItemSorter = new ListViewSorter(listView);
            this.mainModel = mainModel;
            this.ItemSelection = ItemSelection;
            this.condition = condition;
            this.conditionTPHost = conditionTPHost;
            this.condtionTPGuest = conditionTPGuest;

            if (curCompareTemplate != null)
            {
                fillDisplayItem(curCompareTemplate);
            }

            numCombineMinCnt.ValueChanged += numCombineMinCnt_ValueChanged;
            listView.MouseDoubleClick += listView_MouseDoubleClick;
            lvDetails.MouseDoubleClick += lvDetails_DoubleClick;

            setOldDisCols();
            initUI();
        }

        private void initUI()
        {
            colItemName.AspectGetter += delegate (object row)
            {
                if (row is CompareResult)
                {
                    return "全部";
                }
                else if (row is CompareGridLevel)
                {
                    return (row as CompareGridLevel).LevelName;
                }
                return null;
            };

            colRoadsCenterPntDesc.AspectGetter += delegate (object row)
            {
                if (row is CompareGridBlock)
                {
                    return (row as CompareGridBlock).RoadsCenterPntDesc;
                }
                return null;
            };

            colLng.AspectGetter += delegate (object row)
            {
                if (row is CompareResultGrid)
                {
                    return (row as CompareResultGrid).CenterLng;
                }
                return null;
            };

            colLat.AspectGetter += delegate (object row)
            {
                if (row is CompareResultGrid)
                {
                    return (row as CompareResultGrid).CenterLat;
                }
                return null;
            };

            colGridCnt.AspectGetter += delegate (object row)
            {
                if (row is CompareGridLevel)
                {
                    return (row as CompareGridLevel).GridCnt;
                }
                else if (row is CompareGridBlock)
                {
                    return (row as CompareGridBlock).GridCnt;
                }
                return null;
            };

            setGusetValue();

            setHostValue();

            olvColHostGuestDiff.AspectGetter += delegate (object row)
            {
                if (row is CompareResultGrid)
                {
                    return (row as CompareResultGrid).HMGValue;
                }
                return null;
            };

            setcolHostDistance();

            setcolGuestDistance();

            lvDetails.CanExpandGetter += delegate (object row)
            {
                if (row is CompareResult || row is CompareGridLevel || row is CompareGridBlock)
                {
                    return true;
                }
                return false;
            };

            setlvDetails();
        }

        private void setGusetValue()
        {
            colGuestValueAvg.AspectGetter += delegate (object row)
            {
                if (row is CompareGridBlock)
                {
                    double v = (row as CompareGridBlock).GuestValueAvg;
                    return getValidData(v);
                }
                else if (row is CompareResultGrid)
                {
                    return (row as CompareResultGrid).ValueGuest;
                }
                return null;
            };

            colGuestValueMax.AspectGetter += delegate (object row)
            {
                if (row is CompareGridBlock)
                {
                    double v = (row as CompareGridBlock).GuestValueMax;
                    return getValidData(v);
                }
                else if (row is CompareResultGrid)
                {
                    return (row as CompareResultGrid).ValueGuest;
                }
                return null;
            };

            colGuestValueMin.AspectGetter += delegate (object row)
            {
                if (row is CompareGridBlock)
                {
                    double v = (row as CompareGridBlock).GuestValueMin;
                    return getValidData(v);
                }
                else if (row is CompareResultGrid)
                {
                    return (row as CompareResultGrid).ValueGuest;
                }
                return null;
            };
        }

        private void setHostValue()
        {
            colHostValueAvg.AspectGetter += delegate (object row)
            {
                if (row is CompareGridBlock)
                {
                    double v = (row as CompareGridBlock).HostValueAvg;
                    return getValidData(v);
                }
                else if (row is CompareResultGrid)
                {
                    return (row as CompareResultGrid).ValueHost;
                }
                return null;
            };

            colHostValueMax.AspectGetter += delegate (object row)
            {
                if (row is CompareGridBlock)
                {
                    double v = (row as CompareGridBlock).HostValueMax;
                    return getValidData(v);
                }
                else if (row is CompareResultGrid)
                {
                    return (row as CompareResultGrid).ValueHost;
                }
                return null;
            };

            colHostValueMin.AspectGetter += delegate (object row)
            {
                if (row is CompareGridBlock)
                {
                    double v = (row as CompareGridBlock).HostValueMin;
                    return getValidData(v);
                }
                else if (row is CompareResultGrid)
                {
                    return (row as CompareResultGrid).ValueHost;
                }
                return null;
            };
        }

        private void setcolHostDistance()
        {
            colHostDistance.AspectGetter += delegate (object row)
            {
                if (row is CompareGridLevel)
                {
                    double v = (row as CompareGridLevel).HostDistance;
                    return getValidData(v);
                }
                else if (row is CompareGridBlock)
                {
                    double v = (row as CompareGridBlock).HostDistance;
                    return getValidData(v);
                }
                else if (row is CompareResultGrid)
                {
                    double v = (row as CompareResultGrid).HostDistance;
                    return getValidData(v);
                }
                return null;
            };
        }

        private void setcolGuestDistance()
        {
            colGuestDistance.AspectGetter += delegate (object row)
            {
                if (row is CompareGridLevel)
                {
                    double v = (row as CompareGridLevel).GuestDistance;
                    return getValidData(v);
                }
                else if (row is CompareGridBlock)
                {
                    double v = (row as CompareGridBlock).GuestDistance;
                    return getValidData(v);
                }
                else if (row is CompareResultGrid)
                {
                    double v = (row as CompareResultGrid).GuestDistance;
                    return getValidData(v);
                }
                return null;
            };
        }

        private void setlvDetails()
        {
            lvDetails.ChildrenGetter = delegate (object row)
            {
                if (row is CompareResult)
                {
                    return (row as CompareResult).FilteredLevelBlocks;
                }
                else if (row is CompareGridLevel)
                {
                    return (row as CompareGridLevel).FilteredBlocks;
                }
                else if (row is CompareGridBlock)
                {
                    return (row as CompareGridBlock).Grids;
                }
                return null;
            };
        }

        private static object getValidData(double v)
        {
            if (double.IsNaN(v))
            {
                return "-";
            }
            else
            {
                return v;
            }
        }

        List<OLVColumn> oldDisCols = new List<OLVColumn>();
        private void reSetViewOLVColumns()
        {
            lvDetails.AllColumns.Clear();
            foreach (OLVColumn olv in oldDisCols)
            {
                lvDetails.AllColumns.Add(olv);
            }
        }
        private void setOldDisCols()
        {
            foreach (OLVColumn olv in lvDetails.AllColumns)
            {
                this.oldDisCols.Add(olv);
            }
        }
        private void addCol2ListView(List<CompareDisplayColumn> cols ,string str)
        {
            foreach (CompareDisplayColumn colInfo in cols)
            {
                addColumn(str, colInfo);
            }
            lvDetails.RebuildColumns();
        }

        private void addColumn(string str, CompareDisplayColumn colInfo)
        {
            OLVColumn col = new OLVColumn();
            col.Text = str + colInfo.Caption;
            col.Tag = colInfo;
            lvDetails.AllColumns.Add(col);
            lvDetails.Columns.AddRange(new ColumnHeader[] { col });
            col.AspectGetter += delegate (object row)
            {
                CompareResultGrid resultGrid = row as CompareResultGrid;
                if (resultGrid != null)
                {
                    return getResultGridShowValue(col, resultGrid);
                }
                else
                {
                    CompareGridBlock gridBlock = row as CompareGridBlock;
                    if (gridBlock != null)
                    {
                        return getGridBlockShowValue(col, gridBlock);
                    }

                    return null;
                }
            };
        }

        private object getGridBlockShowValue(OLVColumn col, CompareGridBlock gridBlock)
        {
            CompareDisplayColumn disColInfo = col.Tag as CompareDisplayColumn;
            double d = double.NaN;
            if (gridBlock.DisColAvgDic.ContainsKey(disColInfo.ParamKey))
            {
                d = gridBlock.DisColAvgDic[disColInfo.ParamKey];
            }
            if (double.IsNaN(d))
            {
                return "-";
            }
            else
            {
                return Math.Round(d, 2);
            }
        }

        private object getResultGridShowValue(OLVColumn col, CompareResultGrid resultGrid)
        {
            CompareDisplayColumn disColInfo = col.Tag as CompareDisplayColumn;
            double d = double.NaN;
            if (resultGrid.DisColDic.ContainsKey(disColInfo.ParamKey))
            {
                d = resultGrid.DisColDic[disColInfo.ParamKey];
            }
            if (double.IsNaN(d))
            {
                return "-";
            }
            else
            {
                return Math.Round(d, 2);
            }
        }

        private CompareResult compareResult = null;
        CompareParam curCompareTemplate = null;
        public void StartAnalysisData(CompareParam compareTemplate, bool isChkRoad)
        {
            curCompareTemplate = compareTemplate;
            clear();
            doSearch(curCompareTemplate);
            WaitTextBox.Show(doCompare, curCompareTemplate);

            listView.Items.Clear();
            if (regionCPResultDic.Count <= 0)
            {
                numCombineMinCnt.Enabled = false;
                MessageBox.Show("主队客队均未发现栅格数据", "提示");
                return;
            }
            numCombineMinCnt.Enabled = true;
            foreach (CompareResultArea cpRegion in regionCPResultDic.Values)
            {
                cpRegion.Combine((int)numCombineMinCnt.Value, curCompareTemplate, isChkRoad, cbxDisplayMode.SelectedItem);
            }

            fillLayer();
            fillComboxRegion();
            reSetViewOLVColumns();
            addCol2ListView(curCompareTemplate.displayColumnList_A, "主队");
            addCol2ListView(curCompareTemplate.displayColumnList_B, "客队");
            mainModel.RefreshLegend();

            fillDisplayItem(curCompareTemplate);
        }

        private void fillComboxRegion()
        {
            cbxEditRegion.Properties.Items.Clear();
            foreach (CompareResultArea cpRegion in regionCPResultDic.Values)
            {
                if (cpRegion.AreaRegion.Equals("全部汇总"))
                    cbxEditRegion.Properties.Items.Insert(0, cpRegion);
                else
                    cbxEditRegion.Properties.Items.Add(cpRegion);
            }
            if (cbxEditRegion.Properties.Items.Count > 0)
            {
                cbxEditRegion.SelectedIndex = 0;
            }
        }

        private void fillLayer()
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                MasterCom.MTGis.CustomDrawLayer cLayer = mf.GetCustomLayer(typeof(CPGridUnitLayer));
                if (cLayer == null)
                {
                    layer = new CPGridUnitLayer(mf.GetMapOperation(), "栅格对比图层");
                    mf.AddTempCustomLayer(layer);
                }
                else
                {
                    layer = cLayer as CPGridUnitLayer;
                }
            }
            layer.Invalidate();
            layer.FillData(compareResult);
        }
        CPGridUnitLayer layer = null;

        private void fillLvDetails()
        {
            lvDetails.Items.Clear();
            if (compareResult == null) return;
            List<CompareResult> dataSrc = new List<CompareResult>();
            dataSrc.Add(compareResult);
            lvDetails.BeginUpdate();
            try
            {
                lvDetails.ClearObjects();
            }
            catch
            {
                //continue
            }
            lvDetails.SetObjects(dataSrc);
            lvDetails.RefreshObjects(dataSrc);
            foreach (CompareGridLevel level in compareResult.FilteredLevelBlocks)
            {
                lvDetails.RefreshObject(level);
                lvDetails.RefreshObjects(level.FilteredBlocks);
                foreach (CompareGridBlock blk in level.FilteredBlocks)
                {
                    lvDetails.RefreshObject(blk);
                    lvDetails.RefreshObjects(blk.Grids);
                }
            }
            lvDetails.ExpandAll();
            lvDetails.EndUpdate();
        }

        private void ToolStripMenuItemCellHost_Click(object sender, EventArgs e)
        {
            List<CompareResultGrid> grids = getGrids();
            GridMatrix<GridCellsUnit> gridCells = searchGridCells(true, grids);
            showGridCellsForm(gridCells, true);
        }

        private void ToolStripMenuItemCellGuest_Click(object sender, EventArgs e)
        {
            List<CompareResultGrid> grids = getGrids();
            GridMatrix<GridCellsUnit> gridCells = searchGridCells(false, grids);
            showGridCellsForm(gridCells, false);
        }

        private List<CompareResultGrid> getGrids()
        {
            List<CompareResultGrid> grids = new List<CompareResultGrid>();
            if (lvDetails.SelectedItem == null)
                return grids;

            BrightIdeasSoftware.OLVListItem item = lvDetails.SelectedItem as BrightIdeasSoftware.OLVListItem;
            if (item == null) return grids;

            if (item.RowObject is CompareResult)
            {
                CompareResult cResult = item.RowObject as CompareResult;
                foreach (CompareGridLevel level in cResult.FilteredLevelBlocks)
                {
                    foreach (CompareGridBlock block in level.FilteredBlocks)
                    {
                        grids.AddRange(block.Grids);
                    }
                }
            }
            else if (item.RowObject is CompareGridLevel)
            {
                CompareGridLevel level = item.RowObject as CompareGridLevel;
                foreach (CompareGridBlock block in level.FilteredBlocks)
                {
                    grids.AddRange(block.Grids);
                }
            }
            else if (item.RowObject is CompareGridBlock)
            {
                CompareGridBlock block = item.RowObject as CompareGridBlock;
                grids.AddRange(block.Grids);
            }
            else if (item.RowObject is CompareResultGrid)
            {
                CompareResultGrid grid = item.RowObject as CompareResultGrid;
                grids.Add(grid);
            }
            return grids;
        }

        private GridMatrix<GridCellsUnit> searchGridCells(bool bHost, List<CompareResultGrid> grids)
        {
            QueryCondition condHost;
            QueryCondition condGuest;

            getCondition(curCompareTemplate, out condHost, out condGuest);
            QueryCondition searchCond = bHost ? condHost : condGuest;
            QueryKPIStatCellGridByGrids query = new QueryKPIStatCellGridByGrids();
            query.SetQueryCondition(searchCond);
            query.IsShowResultForm = false;
            query.FormulaSet = new string[] { bHost ? curCompareTemplate.formula_A : curCompareTemplate.formula_B };

            foreach (CompareResultGrid grid in grids)
            {
                query.AddGrid(grid);
            }
            query.Query();
            return query.RtGridCells;
        }

        private void showGridCellsForm(GridMatrix<GridCellsUnit> gridCells, bool bHost)
        {
            GridCellsForm form = mainModel.GetObjectFromBlackboard(typeof(GridCellsForm)) as GridCellsForm;
            if (form == null || form.IsDisposed)
            {
                form = new GridCellsForm();
            }
            form.FillData(gridCells, curCompareTemplate, bHost);
            form.Visible = true;
            form.Owner = this;
            form.BringToFront();
        }

        private void clear()
        {
            cbxEditRegion.Properties.Items.Clear();
            cbxEditRegion.ResetText();
            compareResult = null;
            regionCPResultDic.Clear();
            listView.Items.Clear();
            lvDetails.Items.Clear();
        }

        private void doSearch(CompareParam cpParam)
        {
            QueryCondition conditionHost;
            QueryCondition conditionGuest;
            getCondition(cpParam, out conditionHost, out conditionGuest);
            searchHostFormula(cpParam, conditionHost);
            searchGuestFormula(cpParam, conditionGuest);
        }

        Dictionary<string, CompareResultArea> regionCPResultDic = new Dictionary<string, CompareResultArea>();
        private void doCompare(object o)
        {
            CompareParam cpParam = o as CompareParam;

            Dictionary<string, MapOperation2> resvMopDic = RegionMop.GetResvRegionMop();
            
            dealGridMatrix(cpParam, resvMopDic, hostGridMatrix, guestGridMatrix, true);
            dealGridMatrix(cpParam, resvMopDic, guestGridMatrix, hostGridMatrix, false);
            System.Threading.Thread.Sleep(100);
            WaitTextBox.Close();
        }

        private void dealGridMatrix(CompareParam cpParam, Dictionary<string, MapOperation2> resvMopDic, 
            GridMatrix<GridFormula> gridMatrix, GridMatrix<GridFormula> otherGridMatrix, bool isHost)
        {
            foreach (GridFormula grid in gridMatrix)
            {
                string regionName = RegionMop.GetResvRegionName(resvMopDic, new DbPoint(grid.CenterLng, grid.CenterLat));
                if (regionName == null) continue;

                GridFormula gridOther = otherGridMatrix[grid.RowIdx, grid.ColIdx];

                double dHost = double.NaN;
                double dGuest = double.NaN;
                TextColorRange rtColor = cpParam.GetTextColorRange(grid, gridOther, ref dHost, ref dGuest);
                if (rtColor != null)
                {
                    bool isValid = judgeValidDesc(isHost, rtColor);
                    if (isValid)
                    {
                        CompareResultGrid info = getResultGridInfo(cpParam, grid, gridOther, dHost, dGuest, rtColor);
                        addCPResult(regionName, rtColor.description, info);
                        addCPResult("全部汇总", rtColor.description, info);
                    }
                }
            }
        }

        private CompareResultGrid getResultGridInfo(CompareParam cpParam, GridFormula grid, GridFormula gridOther, double dHost, double dGuest, TextColorRange rtColor)
        {
            CompareResultGrid info = new CompareResultGrid();
            setDisColDic(cpParam.displayColumnList_A, grid, info);
            setDisColDic(cpParam.displayColumnList_B, gridOther, info);
            if (dHost.Equals(double.NaN))
            {
                info.HostDistance = double.NaN;
            }
            else
            {
                info.HostDistance = Math.Round(grid.formulaValueDic["0806"], 2);
            }
            
            if (dGuest.Equals(double.NaN))
            {
                info.GuestDistance = double.NaN;
            }
            else
            {
                info.GuestDistance = Math.Round(gridOther.formulaValueDic["0806"], 2);
            }
            
            info.FillData(grid, rtColor, getValidValue(dHost, dGuest, "-", Convert.ToString(dHost - dGuest)),
                 getValidValue(dHost, "-", Convert.ToString(dHost)), getValidValue(dGuest, "-", Convert.ToString(dGuest)));
            return info;
        }

        private bool judgeValidDesc(bool isHost, TextColorRange rtColor)
        {
            bool isValid = false;
            if (isHost)
            {
                if (rtColor.description == CPModeEditForm.GUESTNULL)
                {
                    isValid = true;
                }
            }
            else
            {
                if (rtColor.description != CPModeEditForm.GUESTNULL)
                {
                    isValid = true;
                }
            }

            return isValid;
        }

        private void setDisColDic(List<CompareDisplayColumn> displayColumnList, GridFormula grid, CompareResultGrid info)
        {
            foreach (CompareDisplayColumn col in displayColumnList)
            {
                if (grid == null)
                {
                    info.DisColDic[col.ParamKey] = double.NaN;
                }
                else
                {
                    info.DisColDic[col.ParamKey] = grid.GetValue(col.ParamKey);
                }
            }
        }

        private string getValidValue(double value, string result1, string result2)
        {
            if (double.IsNaN(value))
            {
                return result1;
            }
            else
            {
                return result2;
            }
        }

        private string getValidValue(double value1, double value2, string result1, string result2)
        {
            if (double.IsNaN(value1) || double.IsNaN(value2))
            {
                return result1;
            }
            else
            {
                return result2;
            }
        }

        private void addCPResult(string regionName, string level, CompareResultGrid cpResult)
        {
            if (!regionCPResultDic.ContainsKey(regionName))
            {
                regionCPResultDic.Add(regionName, new CompareResultArea(regionName));
            }
            regionCPResultDic[regionName].AddCompareResultGrid(level, cpResult);
        }

        private void fireListView(CompareResultArea cpRegion)
        {
            listView.Items.Clear();
            foreach (string level in cpRegion.CompareResultGridDic.Keys)
            {
                string[] array = new string[3];
                array[0] = level;
                array[1] = cpRegion.CompareResultGridDic[level].Count.ToString();
                array[2] = Math.Round(100.0 * cpRegion.CompareResultGridDic[level].Count / cpRegion.CpResultGridCount, 2).ToString();
                ListViewItem lvi = new ListViewItem(array);
                lvi.Tag = cpRegion.CompareResultGridDic[level];
                listView.Items.Add(lvi);
            }
        }

        private void getCondition(CompareParam param, out QueryCondition conditionHost, out QueryCondition conditionGuest)
        {
            initHostCondition(param, out conditionHost);
            initGuestCondition(param, out conditionGuest);
        }

        private void initHostCondition(CompareParam param, out QueryCondition conditionHost)
        {
            conditionHost = new QueryCondition();
            conditionHost.AgentIds = condition.AgentIds;
            conditionHost.Areas = condition.Areas;
            conditionHost.DistrictID = condition.DistrictID;
            conditionHost.DistrictIDs = condition.DistrictIDs;
            conditionHost.EventIDs = condition.EventIDs;
            conditionHost.FileInfos = condition.FileInfos;
            conditionHost.FileName = conditionTPHost.FileName;
            conditionHost.FileNameOrNum = conditionTPHost.FileNameOrNum;
            conditionHost.NameFilterType = condition.NameFilterType;
            conditionHost.Geometorys = condition.Geometorys;
            conditionHost.IsAllAgent = condition.IsAllAgent;
            conditionHost.QueryType = condition.QueryType;

            conditionHost.Periods.Clear();
            conditionHost.Periods = conditionTPHost.Periods;
            conditionHost.Projects.Clear();
            foreach (int projID in conditionTPHost.Projects)
            {
                conditionHost.Projects.Add(projID);
            }
            conditionHost.ServiceTypes.Clear();
            foreach (int servID in param.serviceList_A)
            {
                conditionHost.ServiceTypes.Add(servID);
            }
            conditionHost.CarrierTypes.Clear();
            conditionHost.CarrierTypes.Add(param.carrier_A);
        }

        private void initGuestCondition(CompareParam param, out QueryCondition conditionGuest)
        {
            conditionGuest = new QueryCondition();
            conditionGuest.AgentIds = condition.AgentIds;
            conditionGuest.Areas = condition.Areas;
            conditionGuest.DistrictID = condition.DistrictID;
            conditionGuest.DistrictIDs = condition.DistrictIDs;
            conditionGuest.EventIDs = condition.EventIDs;
            conditionGuest.FileInfos = condition.FileInfos;
            conditionGuest.FileName = condtionTPGuest.FileName;
            conditionGuest.FileNameOrNum = condtionTPGuest.FileNameOrNum;
            conditionGuest.NameFilterType = condition.NameFilterType;
            conditionGuest.Geometorys = condition.Geometorys;
            conditionGuest.IsAllAgent = condition.IsAllAgent;
            conditionGuest.QueryType = condition.QueryType;

            conditionGuest.Periods.Clear();
            conditionGuest.Periods = condtionTPGuest.Periods;
            conditionGuest.Projects.Clear();
            foreach (int projID in condtionTPGuest.Projects)
            {
                conditionGuest.Projects.Add(projID);
            }
            conditionGuest.ServiceTypes.Clear();
            foreach (int servID in param.serviceList_B)
            {
                conditionGuest.ServiceTypes.Add(servID);
            }
            conditionGuest.CarrierTypes.Clear();
            conditionGuest.CarrierTypes.Add(param.carrier_B);
        }

        GridMatrix<GridFormula> hostGridMatrix;
        private void searchHostFormula(CompareParam param, QueryCondition conditionHost)
        {
            DIYQueryFormulaInGridByRegion queryHost = new DIYQueryFormulaInGridByRegion(mainModel);
            queryHost.setQueryFormulas(param.displayColumnList_A, param.formula_A);
            queryHost.SetQueryCondition(conditionHost);
            queryHost.Query();
            hostGridMatrix = queryHost.formulaGridMatrix;
        }

        GridMatrix<GridFormula> guestGridMatrix;
        private void searchGuestFormula(CompareParam param, QueryCondition conditionGuest)
        {
            DIYQueryFormulaInGridByRegion queryGuest = new DIYQueryFormulaInGridByRegion(mainModel);
            queryGuest.setQueryFormulas(param.displayColumnList_B, param.formula_B);
            queryGuest.SetQueryCondition(conditionGuest);
            queryGuest.Query();
            guestGridMatrix = queryGuest.formulaGridMatrix;
        }

        private void listView_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (listView.SelectedItems.Count <= 0)
            {
                return;
            }
            List<CompareResultGrid> gcrInfoList = listView.SelectedItems[0].Tag as List<CompareResultGrid>;
            mainModel.MainForm.GetMapForm().GoToView(gcrInfoList[0].LTLng, gcrInfoList[0].LTLat, 1800);
        }

        private void numCombineMinCnt_ValueChanged(object sender, EventArgs e)
        {
            if (compareResult == null)
            {
                return;
            }
            compareResult.Filter((int)numCombineMinCnt.Value, curCompareTemplate);
            fillLvDetails();
            layer.Invalidate();
        }

        private void btnExport_Click(object sender, EventArgs e)
        {
            lvDetails.ExpandAll();
            if (lvDetails.Items.Count > 65535)
            {
                TxtExporter.Export(lvDetails, true);
            }
            else
            {
                ExcelNPOIManager.ExportToExcel(lvDetails);
            }
        }

        private void lvDetails_DoubleClick(object sender, EventArgs e)
        {
            if (layer==null)
            {
                return;
            }
            object row = lvDetails.SelectedObject;
            if (row is CompareGridBlock)
            {
                mainModel.MainForm.GetMapForm().GoToView(((row as CompareGridBlock).GetBounds()));
            }
            else if (row is CompareResultGrid)
            {
                CompareResultGrid g = row as CompareResultGrid;
                mainModel.MainForm.GetMapForm().GoToView(g.CenterLng, g.CenterLat, 6000);
            }
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            lvDetails.ExpandAll();
        }

        private void miCollapseAll_Click(object sender, EventArgs e)
        {
            lvDetails.CollapseAll();
        }

        private void btnExportShp_Click(object sender, EventArgs e)
        {
            if (compareResult==null)
            {
                return;
            }
            SaveFileDialog saveFileDlg = new SaveFileDialog();
            saveFileDlg.Filter = FilterHelper.Shp;
            saveFileDlg.FilterIndex = 1;
            saveFileDlg.RestoreDirectory = true;
            saveFileDlg.Title = "保存shp图层文件";
            if (saveFileDlg.ShowDialog()==DialogResult.OK)
            {
                exportShpFile(saveFileDlg.FileName);
            }
        }

        public void exportShpFile(string filename)
        {
            Shapefile shpFile = new Shapefile();
            try
            {
                bool result = shpFile.CreateNewWithShapeID("", MapWinGIS.ShpfileType.SHP_POLYGON);
                if (!result)
                {
                    System.Windows.Forms.MessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                    return;
                }
                int idIdx = 0;
                int fiColor = idIdx++;
                int fiLongitude = idIdx++;
                int fiLatitude = idIdx++;
                int fiValue = idIdx++;
                int fiHost = idIdx++;
                int fiGuest = idIdx++;
                int fiDes = idIdx++;
                int fiDisHost = idIdx++;
                int fiDisGuest = idIdx;
                ShapeHelper.InsertNewField(shpFile, "Color", FieldType.INTEGER_FIELD, 10, 30, ref fiColor);
                ShapeHelper.InsertNewField(shpFile, "中心经度", FieldType.DOUBLE_FIELD, 10, 30, ref fiLongitude);
                ShapeHelper.InsertNewField(shpFile, "中心纬度", FieldType.DOUBLE_FIELD, 10, 30, ref fiLatitude);
                ShapeHelper.InsertNewField(shpFile, "Value", FieldType.STRING_FIELD, 10, 30, ref fiValue);
                ShapeHelper.InsertNewField(shpFile, "ValueHost", FieldType.STRING_FIELD, 10, 30, ref fiHost);
                ShapeHelper.InsertNewField(shpFile, "ValueGuest", FieldType.STRING_FIELD, 10, 30, ref fiGuest);
                ShapeHelper.InsertNewField(shpFile, "描述", FieldType.STRING_FIELD, 10, 30, ref fiDes);
                ShapeHelper.InsertNewField(shpFile, "DistanceHost", FieldType.DOUBLE_FIELD, 10, 30, ref fiDisHost);
                ShapeHelper.InsertNewField(shpFile, "DistanceGuest", FieldType.DOUBLE_FIELD, 10, 30, ref fiDisGuest);
                int numShp = 0;
                foreach (CompareGridLevel levelBlk in compareResult.FilteredLevelBlocks)
                {
                    foreach (CompareGridBlock blk in levelBlk.FilteredBlocks)
                    {
                        foreach (CompareResultGrid grid in blk.Grids)
                        {
                            shpFile.EditInsertShape(ShapeHelper.CreateRectShape(grid.LTLng, grid.LTLat, grid.BRLng, grid.BRLat), ref numShp);
                            shpFile.EditCellValue(fiColor, numShp, ColorTranslator.ToOle(levelBlk.TextColorRange.color));
                            shpFile.EditCellValue(fiLongitude, numShp,grid.CenterLng);
                            shpFile.EditCellValue(fiLatitude, numShp, grid.CenterLat);
                            shpFile.EditCellValue(fiValue, numShp, grid.HMGValue);
                            shpFile.EditCellValue(fiHost, numShp, grid.ValueHost);
                            shpFile.EditCellValue(fiGuest, numShp, grid.ValueGuest);
                            shpFile.EditCellValue(fiDes, numShp, levelBlk.TextColorRange.description);
                            shpFile.EditCellValue(fiDisHost, numShp, grid.HostDistance);
                            shpFile.EditCellValue(fiDisGuest, numShp, grid.GuestDistance);
                        }
                    }
                }
                ShapeHelper.DeleteShpFile(filename);
                if (!shpFile.SaveAs(filename, null))
                {
                    MessageBox.Show("保存文件失败！" + shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                }
            }
            catch (System.Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
            finally
            {
                shpFile.Close();
            }
        }

        private void btnExportBrief_Click(object sender, EventArgs e)
        {
            if (compareResult != null)
            {
                lvDetails.Expand(compareResult);
                foreach (CompareGridLevel level in compareResult.FilteredLevelBlocks)
                {
                    lvDetails.Expand(level);
                    foreach (CompareGridBlock blk in level.FilteredBlocks)
                    {
                        lvDetails.Collapse(blk);
                    }
                }
            }
            if (lvDetails.Items.Count > 65535)
            {
                TxtExporter.Export(lvDetails, true);
            }
            else
            {
                ExcelNPOIManager.ExportToExcel(lvDetails);
            }
        }

        private void btnExportBriefTxt_Click(object sender, EventArgs e)
        {
            if (compareResult != null)
            {
                lvDetails.Expand(compareResult);
                foreach (CompareGridLevel level in compareResult.FilteredLevelBlocks)
                {
                    lvDetails.Expand(level);
                    foreach (CompareGridBlock blk in level.FilteredBlocks)
                    {
                        lvDetails.Collapse(blk);
                    }
                }
            }
            TxtExporter.Export(lvDetails, true);
        }

        private void btnExport2Txt_Click(object sender, EventArgs e)
        {
            lvDetails.ExpandAll();
            TxtExporter.Export(lvDetails, true);
        }

        private void fillDisplayItem(CompareParam mode)
        {
            cbxDisplayMode.Items.Clear();
            cbxDisplayMode.Items.Add("全部");
            foreach (TextColorRange range in mode.AlgorithmCfg.specialColorList)
            {
                cbxDisplayMode.Items.Add(range);
            }
            foreach (CPModeColorItem item in mode.AlgorithmCfg.colorItemList)
            {
                cbxDisplayMode.Items.Add(item.colorRange);
            }
            foreach (CPModeColorItem item in mode.AlgorithmCfg.bothStandardList)
            {
                cbxDisplayMode.Items.Add(item.colorRange);
            }
            if (cbxDisplayMode.Items.Count > 0)
            {
                cbxDisplayMode.SelectedIndex = 0;
            }
        }

        private void cbxDisplayMode_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (compareResult == null)
            {
                return;
            }
            compareResult.Filter((int)numCombineMinCnt.Value, cbxDisplayMode.SelectedItem);
            fillLvDetails();
            layer.FillData(compareResult);
            layer.Invalidate();
        }

        private void cbxEditRegion_SelectedIndexChanged(object sender, EventArgs e)
        {
            CompareResultArea cpRegion = cbxEditRegion.SelectedItem as CompareResultArea;
            if (cpRegion == null) return;
            fireListView(cpRegion);
            compareResult = cpRegion.CompareResultCombinded;
            cbxDisplayMode_SelectedIndexChanged(null, null);
        }

        private void ToolStripMenuItemExportCurrent_Click(object sender, EventArgs e)
        {
            try
            {
                ExcelNPOIManager.ExportToExcel(listView);
            }
            catch
            {
                MessageBox.Show("导出到Excel失败...", "提示");
            }
        }

        private void ToolStripMenuItemExportAll_Click(object sender, EventArgs e)
        {
            try
            {
                List<NPOIRow> rowList = new List<NPOIRow>();
                NPOIRow row = new NPOIRow();
                for (int idx = 0; idx < listView.Columns.Count; idx++ )
                {
                    row.AddCellValue(listView.Columns[idx].Text);
                }
                rowList.Add(row);
                int num = 1;
                foreach (string region in regionCPResultDic.Keys)
                {
                    if (region.Equals("全部汇总")) continue;
                    if(num != 1)
                        rowList.Add(new NPOIRow());
                    num++;
                    row = new NPOIRow();
                    row.AddCellValue(region);
                    rowList.Add(row);
                    foreach (string level in regionCPResultDic[region].CompareResultGridDic.Keys)
                    {
                        row = new NPOIRow();
                        row.AddCellValue(level);
                        row.AddCellValue(regionCPResultDic[region].CompareResultGridDic[level].Count);
                        row.AddCellValue(Math.Round(100.0 * regionCPResultDic[region].CompareResultGridDic[level].Count / regionCPResultDic[region].CpResultGridCount, 2));
                        rowList.Add(row);
                    }
                }
                if (regionCPResultDic.ContainsKey("全部汇总"))
                {
                    string region = "全部汇总";
                    rowList.Add(new NPOIRow());
                    row = new NPOIRow();
                    row.AddCellValue(region);
                    rowList.Add(row);
                    foreach (string level in regionCPResultDic[region].CompareResultGridDic.Keys)
                    {
                        row = new NPOIRow();
                        row.AddCellValue(level);
                        row.AddCellValue(regionCPResultDic[region].CompareResultGridDic[level].Count);
                        row.AddCellValue(Math.Round(100.0 * regionCPResultDic[region].CompareResultGridDic[level].Count / regionCPResultDic[region].CpResultGridCount, 2));
                        rowList.Add(row);
                    }
                }
                ExcelNPOIManager.ExportToExcel(rowList);
            }
            catch
            {
                MessageBox.Show("导出到Excel失败...", "提示");
            }
        }

        private void contextMenuStripWhole_Opening(object sender, CancelEventArgs e)
        {
            ToolStripMenuItemExportCurrent.Enabled = listView.Items.Count > 0;
            ToolStripMenuItemExportAll.Enabled = regionCPResultDic.Count > 0;
        }
    }

    public class CompareResult
    {
        private readonly int blockMinGridCnt;
        public CompareResult(Dictionary<string, List<CompareResultGrid>> levelGridDic, int blockMinGridCnt,CompareParam compareCfg,bool isShowRoadname,object filterLevel)
        {
            this.blockMinGridCnt = blockMinGridCnt;
            object[] paramArr=new object[4];
            paramArr[0] = levelGridDic;
            paramArr[1] = compareCfg;
            paramArr[2] = isShowRoadname;
            paramArr[3] = filterLevel;
            if (levelGridDic.Count > 200)
                WaitTextBox.Show(combineBlock, paramArr);
            else
                combineBlock(paramArr);
        }
        private void combineBlock(object o)
        {
             object[] paramArr=o as  object[];
            Dictionary<string, List<CompareResultGrid>> levelGridDic = paramArr[0] as  Dictionary<string, List<CompareResultGrid>>;
            CompareParam compareCfg = paramArr[1] as CompareParam;
            bool isShowRoadname = (bool)paramArr[2];
            object filterLevel = paramArr[3];
            foreach (KeyValuePair<string, List<CompareResultGrid>> kvp in levelGridDic)
            {
                WaitTextBox.Text = "正在汇聚【" + kvp.Key + "】的栅格...";
                TextColorRange rangeInfo = null;
                setRangeInfo(compareCfg, kvp.Key, ref rangeInfo);
                List<CompareGridBlock> blocks = new List<CompareGridBlock>();
                List<CompareGridBlock> canMergeBlks = new List<CompareGridBlock>();
                mergeGridBlock(kvp, blocks, canMergeBlks);
                foreach (CompareGridBlock blk in blocks)
                {//汇聚块进行信息汇总
                    blk.MakeSummary(isShowRoadname);
                }
                CompareGridLevel levelItem = new CompareGridLevel(blocks, rangeInfo);
                levelItem.FilterBlock(blockMinGridCnt);
                totalLevelBlocks.Add(levelItem);
            }
            Filter(blockMinGridCnt, filterLevel);
            WaitTextBox.Close();
        }

        private static void setRangeInfo(CompareParam compareCfg, string desc, ref TextColorRange rangeInfo)
        {
            foreach (CPModeColorItem cItem in compareCfg.AlgorithmCfg.colorItemList)
            {
                if (cItem.colorRange.description.Equals(desc))
                {
                    rangeInfo = cItem.colorRange;
                    break;
                }
            }
            foreach (CPModeColorItem cItem in compareCfg.AlgorithmCfg.bothStandardList)
            {
                if (cItem.colorRange.description.Equals(desc))
                {
                    rangeInfo = cItem.colorRange;
                    break;
                }
            }
            if (rangeInfo == null)
            {
                foreach (TextColorRange tcRange in compareCfg.AlgorithmCfg.specialColorList)
                {
                    if (tcRange.description.Equals(desc))
                    {
                        rangeInfo = tcRange;
                        break;
                    }
                }
            }
        }

        private void mergeGridBlock(KeyValuePair<string, List<CompareResultGrid>> kvp, List<CompareGridBlock> blocks, List<CompareGridBlock> canMergeBlks)
        {
            foreach (CompareResultGrid grid in kvp.Value)
            {
                canMergeBlks.Clear();
                foreach (CompareGridBlock blk in blocks)
                {
                    if (blk.CanCollect(grid))
                    {//这里不能跳出循环！需要遍历所有已汇聚的block,因为该grid可能把多个block都连起来
                        canMergeBlks.Add(blk);
                    }
                }
                if (canMergeBlks.Count == 0)
                {//栅格无法与其它栅格汇聚，成为新的block
                    blocks.Add(new CompareGridBlock(kvp.Key, grid));
                }
                else
                {//可以汇聚，甚至可以跟多个block汇聚
                    CompareGridBlock firstBlk = canMergeBlks[0];
                    firstBlk.Collects(grid);//把该栅格跟已有的栅格汇聚
                                            //如果不止一个可以汇聚的block,则这些block都是能合并起来的。
                    for (int i = 1; i < canMergeBlks.Count; i++)
                    {
                        CompareGridBlock blk2Merge = canMergeBlks[i];
                        firstBlk.Merge(blk2Merge);
                        blocks.Remove(blk2Merge);//变成了一个bigger block后，移除some small block~
                    }
                }
            }
        }

        private readonly List<CompareGridLevel> totalLevelBlocks = new List<CompareGridLevel>();
        private readonly List<CompareGridLevel> filteredLevelBlocks = new List<CompareGridLevel>();
        public List<CompareGridLevel> FilteredLevelBlocks
        {
            get { return filteredLevelBlocks; }
        }

        public void Filter(int blockMinGridCnt,object showCompareItem)
        {
            filteredLevelBlocks.Clear();
            foreach (CompareGridLevel level in totalLevelBlocks)
            {
                if (!(showCompareItem is TextColorRange) || level.TextColorRange.Equals(showCompareItem))
                {
                    level.FilterBlock(blockMinGridCnt);
                    filteredLevelBlocks.Add(level);
                }
            }
        }
    }

    public class CompareGridLevel
    {
        readonly TextColorRange txtClrRange;
        public TextColorRange TextColorRange
        {
            get { return txtClrRange; }
        }
        public CompareGridLevel(List<CompareGridBlock> blocks,TextColorRange rangeInfo)
        {
            totalBlocks = blocks;
            txtClrRange = rangeInfo;
        }
        public string LevelName
        {
            get { return txtClrRange.description; }
        }
        private readonly List<CompareGridBlock> totalBlocks;
        private readonly List<CompareGridBlock> filteredBlocks = new List<CompareGridBlock>();
        public List<CompareGridBlock> FilteredBlocks
        {
            get { return filteredBlocks; }
        }
        public void FilterBlock(int blockMinGridCnt)
        {
            filteredBlocks.Clear();
            foreach (CompareGridBlock blk in totalBlocks)
            {
                if (blk.GridCnt>=blockMinGridCnt)
                {
                    filteredBlocks.Add(blk); 
                }
            }
        }

        public int GridCnt
        {
            get {
                int cnt = 0;
                foreach (CompareGridBlock blk in filteredBlocks)
                {
                    cnt += blk.GridCnt;
                }
                return cnt;
            }
        }

        public int TotalBlockCnt
        {
            get { return totalBlocks.Count; }
        }
        public int FilteredBlockCnt
        {
            get { return filteredBlocks.Count; }
        }

        public double HostDistance
        {
            get
            {
                double dis = 0;
                foreach (CompareGridBlock blk in filteredBlocks)
                {
                    dis += blk.HostDistance;
                }
                return dis;
            }
        }

        public double GuestDistance
        {
            get
            {
                double dis = 0;
                foreach (CompareGridBlock blk in filteredBlocks)
                {
                    dis += blk.GuestDistance;
                }
                return dis;
            }
        }

    }

    public class CompareGridBlock
    {
        public CompareGridBlock(string levelName, CompareResultGrid grid)
        {
            this.levelName = levelName;
            Collects(grid);
        }
        readonly string levelName;
        public string LevelName
        {
            get { return levelName; }
        }
        public int GridCnt
        {
            get { return matrix.Length; }
        }
        public List<CompareResultGrid> Grids
        {
            get { return matrix.Grids; }
        }

        public DbRect GetBounds()
        {
            return matrix.GetBounds();
        }

        /// <summary>
        /// 如果相邻，返回true，否则返回false
        /// </summary>
        /// <param name="grid">待判定的Grid</param>
        /// <returns>能汇聚：true;否则：false</returns>
        public bool CanCollect(CompareResultGrid grid)
        {
            bool canCombine = false;
            int rowIdx = grid.RowIdx;
            int colIdx = grid.ColIdx;
            //先与扩大一圈的block的矩形边界判断，只有在这区域内的栅格才有可能汇聚
            if (rowIdx >= (matrix.MinRowIdx - 1) && rowIdx <= (matrix.MaxRowIdx + 1)
             && colIdx >= (matrix.MinColIdx - 1) && colIdx <= (matrix.MaxColIdx + 1))
            {
                bool isNear = juedgeNear(rowIdx, colIdx);
                if (isNear)
                {
                    return true;
                }
            }
            return canCombine;
        }

        private bool juedgeNear(int rowIdx, int colIdx)
        {
            for (int r = -1; r < 2; r++)
            {
                int newRowIdx = rowIdx + r;
                for (int c = -1; c < 2; c++)
                {
                    bool isNear = juedgeNearGrid(colIdx, r, newRowIdx, c);
                    if (isNear)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        private bool juedgeNearGrid(int colIdx, int r, int newRowIdx, int c)
        {
            if (r != 0 || c != 0)
            {
                int newColIdx = colIdx + c;
                if (matrix[newRowIdx, newColIdx] != null)
                {
                    return true;
                }
            }
            return false;
        }

        readonly GridMatrix<CompareResultGrid> matrix = new GridMatrix<CompareResultGrid>();
        public void Collects(CompareResultGrid grid)
        {
            matrix[grid.RowIdx, grid.ColIdx] = grid;
        }

        public void Merge(CompareGridBlock block)
        {
            matrix.Merge(block.matrix);
        }

        private double hostValueSum = double.NaN;
        public double HostValueAvg
        {
            get { return Math.Round(hostValueSum / GridCnt, 2); }
        }
        private double hostValueMin = double.MaxValue;
        public double HostValueMin
        {
            get
            {
                if (double.MaxValue == hostValueMin)
                {
                    return double.NaN;
                }
                return Math.Round(hostValueMin, 2);
            }
        }
        private double hostValueMax = double.MinValue;
        public double HostValueMax
        {
            get
            {
                if (double.MinValue==hostValueMax)
                {
                    return double.NaN;
                }
                return Math.Round(hostValueMax,2); }
        }
        private double guestValueSum = double.NaN;
        public double GuestValueAvg
        {
            get { return Math.Round(guestValueSum / GridCnt, 2); }
        }
        private double guestValueMin = double.MaxValue;
        public double GuestValueMin
        {
            get
            {
                if (double.MaxValue==guestValueMin)
                {
                    return double.NaN;
                }
                return Math.Round(guestValueMin,2); }
        }
        private double guestValueMax = double.MinValue;
        public double GuestValueMax
        {
            get
            {
                if (double.MinValue==guestValueMax)
                {
                    return double.NaN;
                }
                return Math.Round(guestValueMax,2); }
        }
        private double guestDis = 0;
        public double GuestDistance
        {
            get { return Math.Round(guestDis,2); }
        }
        private double hostDis = 0;
        public double HostDistance
        {
            get { return Math.Round(hostDis, 2); }
        }
        public string RoadsCenterPntDesc
        {
            get;
            set;
        }

        public Dictionary<string, double> DisColAvgDic { get; set; } = new Dictionary<string, double>();

        public void MakeSummary(bool isShowRoadName)
        {
            List<double> lngSet = new List<double>();
            List<double> latSet = new List<double>();
            Dictionary<string, double> DisColSumDic = new Dictionary<string, double>();
            foreach (CompareResultGrid grid in matrix)
            {
                if (DisColSumDic.Count == 0)
                {
                    foreach (string str in grid.DisColDic.Keys)
                    {
                        DisColSumDic[str] = 0;
                    }
                }
                foreach (string str in grid.DisColDic.Keys)
                {
                    DisColSumDic[str] += grid.DisColDic[str];
                }
                addDataByGrid(lngSet, latSet, grid);
            }
            foreach (string str in DisColSumDic.Keys)
            {
                DisColAvgDic[str] = DisColSumDic[str] / matrix.Length;
            }

            if (isShowRoadName)
            {
                this.RoadsCenterPntDesc = GISManager.GetInstance().GetRoadPlaceDesc(lngSet, latSet);
            }
        }

        private void addDataByGrid(List<double> lngSet, List<double> latSet, CompareResultGrid grid)
        {
            lngSet.Add(grid.CenterLng);
            latSet.Add(grid.CenterLat);
            hostDis += grid.HostDistance;
            guestDis += grid.GuestDistance;
            double hostValue;
            if (double.TryParse(grid.ValueHost, out hostValue))
            {
                if (double.IsNaN(hostValueSum))
                {
                    hostValueSum = 0;
                }
                hostValueSum += hostValue;
                hostValueMax = Math.Max(hostValueMax, hostValue);
                hostValueMin = Math.Min(hostValueMin, hostValue);
            }
            double guestValue;
            if (double.TryParse(grid.ValueGuest, out guestValue))
            {
                if (double.IsNaN(guestValueSum))
                {
                    guestValueSum = 0;
                }
                guestValueSum += guestValue;
                guestValueMax = Math.Max(guestValueMax, guestValue);
                guestValueMin = Math.Min(guestValueMin, guestValue);
            }
        }
    }

    public class CompareResultArea
    {
        public int CpResultGridCount { get; set; }
        public string AreaRegion { get; set; }
        public CompareResult CompareResultCombinded { get; set; }
        public Dictionary<string, List<CompareResultGrid>> CompareResultGridDic { get; set; }

        public CompareResultArea(string region)
        {
            CpResultGridCount = 0;
            this.AreaRegion = region;
            CompareResultGridDic = new Dictionary<string, List<CompareResultGrid>>();
        }

        public void AddCompareResultGrid(string level, CompareResultGrid cpResult)
        {
            List<CompareResultGrid> cpResultGridList;
            if (!CompareResultGridDic.TryGetValue(level, out cpResultGridList))
            {
                cpResultGridList = new List<CompareResultGrid>();
                CompareResultGridDic.Add(level, cpResultGridList);
            }
            cpResultGridList.Add(cpResult);
            CpResultGridCount++;
        }

        public void Combine(int combineMinCnt, CompareParam cpParam, bool bChkRoadName, object filterLevel)
        {
            CompareResultCombinded = new CompareResult(CompareResultGridDic, combineMinCnt,
                cpParam, bChkRoadName, filterLevel);
        }

        public override string ToString()
        {
            return AreaRegion;
        }
    }

    public class CompareResultGrid:GridUnitBase
    {
        public string HMGValue
        {
            get;
            set;
        }
        public string ValueHost
        {
            get;
            set;
        }
        public string ValueGuest
        {
            get;
            set;
        }

        public CompareResultGrid()
        {
        }

        public Dictionary<string, double> DisColDic { get; set; } = new Dictionary<string, double>();
        
        public double HostDistance { get; set; } = double.NaN;
        public double GuestDistance { get; set; } = double.NaN;

        public TextColorRange TCRange { get; set; }

        public void FillData(GridFormula gridFormula, TextColorRange tcrange, string hmgValue, string valueHost, string valueGuest)
        {
            LTLng = gridFormula.LTLng;
            LTLat = gridFormula.LTLat;
            this.TCRange = tcrange;
            HMGValue = hmgValue;
            this.ValueHost = valueHost;
            this.ValueGuest = valueGuest;
        }
    }

}
