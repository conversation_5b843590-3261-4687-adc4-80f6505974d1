﻿using MasterCom.RAMS.BackgroundFunc.StationAccept_Shanxi;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    /// <summary>
    /// 每个文件的单验结果,每个文件只测试一个小区,一种业务
    /// </summary>
    public class CellAcceptFileInfo_SX<T> where T : ICell
    {
        public CellAcceptFileInfo_SX(FileInfo fileInfo)
        {
            File = fileInfo;
            FileId = File.ID;
            FileName = File.Name;
        }
        #region 文件信息
        public FileInfo File { get; set; }
        public int FileId { get; protected set; }
        public string FileName { get; protected set; }
        public string FileNameKey { get; protected set; }
        #endregion

        #region 工参信息
        public T Cell { get; set; }
        public string BtsName { get; set; }
        public int CellId { get; protected set; }
        public string CellName { get; set; }
        //去除中文的小区Key
        public string CellNameKey { get; protected set; }
        #endregion

        #region 指标信息
        public int PointCount { get; set; }
        public DataInfo Rsrp { get; set; } = new DataInfo();
        public DataInfo Sinr { get; set; } = new DataInfo();
        public DataInfo Speed { get; set; } = new DataInfo();

        public DataInfo PingBig { get; set; } = new DataInfo();
        public DataInfo PingSmall { get; set; } = new DataInfo();
        #endregion
    }

    public class CellAcceptFileInfo_SX_LTE : CellAcceptFileInfo_SX<LTECell>
    {
        public CellAcceptFileInfo_SX_LTE(FileInfo fileInfo, LTECell cell)
            : base(fileInfo)
        {
            Cell = cell;
            bool isOutDoor = cell.Type == LTEBTSType.Outdoor;
            FileNameKey = FileNameRuleHelper_SX.GetNameKey(FileName, isOutDoor);

             CellId = cell.CellID;
            CellNameKey = cell.Name;
            BtsName = cell.BTSName;
        }
    }

    public class CellAcceptFileInfo_SX_NR : CellAcceptFileInfo_SX<NRCell>
    {
        public CellAcceptFileInfo_SX_NR(FileInfo fileInfo, NRCell cell)
            : base(fileInfo)
        {
            Cell = cell;
            bool isOutDoor = cell.Type == NRBTSType.Outdoor;
            FileNameKey = FileNameRuleHelper_SX.GetNameKey(FileName, isOutDoor);

            CellId = cell.CellID;
            CellNameKey = cell.Name;
            BtsName = cell.BTSName;
        }
    }
}
