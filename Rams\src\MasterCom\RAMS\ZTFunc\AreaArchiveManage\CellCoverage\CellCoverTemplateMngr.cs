﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.CellCoverage
{
    public class CellCoverTemplateMngr
    {
        public static readonly string PATH = Application.StartupPath + "\\config\\AreaArchive\\CellCoverageTemplate\\";
        private static CellCoverTemplateMngr instance = null;
        private CellCoverTemplateMngr()
        {
            Init();
        }

        public void Init()
        {
            ReportTemplates = new List<CellCoverRptTemplate>();
            if (!System.IO.Directory.Exists(PATH))
            {
                return;
            }
            try
            {
                System.IO.DirectoryInfo directory = new System.IO.DirectoryInfo(PATH);
                System.IO.FileInfo[] files = directory.GetFiles("*.xml", System.IO.SearchOption.TopDirectoryOnly);
                if (files.Length > 0)
                {
                    foreach (System.IO.FileInfo file in files)
                    {
                        XmlConfigFile configFile = new XmlConfigFile(file.FullName);
                        Dictionary<string, object> dic = configFile.GetItemValue("Template", "Options") as Dictionary<string, object>;
                        if (dic == null)
                        {
                            continue;
                        }
                        CellCoverRptTemplate rptstyle = new CellCoverRptTemplate();
                        rptstyle.Param = dic;
                        ReportTemplates.Add(rptstyle);
                    }
                }
            }
            catch (Exception e)
            {
                System.Windows.Forms.MessageBox.Show("加载档案模板异常。" + e.Message);
            }
        }

        public static CellCoverTemplateMngr Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new CellCoverTemplateMngr();
                }
                return instance;
            }
        }

        public List<CellCoverRptTemplate> ReportTemplates
        {
            get;
            set;
        }
    }

    public class CellCoverRptTemplate
    {
        public override string ToString()
        {
            return Name;
        }
        public CellCoverRptTemplate()
        {
            Columns = new List<TemplateColumn>();
        }
        public string Name
        {
            get;
            set;
        }

        public List<TemplateColumn> Columns
        {
            get;
            set;
        }

        public void Save()
        {
            XmlConfigFile configFile = new XmlConfigFile();
            System.Xml.XmlElement cfg = configFile.AddConfig("Template");
            configFile.AddItem(cfg, "Options", this.Param);
            try
            {
                configFile.Save(CellCoverTemplateMngr.PATH + Name + ".xml");
            }
            catch
            {
                //continue
            }
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic["Name"] = Name;
                List<object> list = new List<object>();
                foreach (TemplateColumn col in this.Columns)
                {
                    list.Add(col.Param);
                }
                dic["Columns"] = list;
                return dic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                Name = value["Name"].ToString();
                Columns = new List<TemplateColumn>();
                List<object> list = value["Columns"] as List<object>;
                foreach (Dictionary<string, object> item in list)
                {
                    TemplateColumn col = new TemplateColumn();
                    col.Param = item;
                    Columns.Add(col);
                }
            }
        }

    }

    public class TemplateColumn
    {
        public TemplateColumn()
        {
            DynamicBKColorRanges = new List<DTParameterRangeColor>();
            StaticColor = Color.Lime;
        }

        public override string ToString()
        {
            return this.Caption;
        }

        public string ExpProportion
        {
            get
            {
                return string.Format("{0}_占比", Expression);
            }
        }

        public string CaptionProportion
        {
            get {
                return string.Format("{0}占比(%)", Caption);
            }
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Caption"] = Caption;
                param["Expression"] = Expression;
                param["CarrierID"] = (int)(CarrierID);
                param["MoMtFlag"] = (int)(MoMtFlag);
                param["CalcProportion"] = IsCalcProportion;
                param["IsDynamicBKClr"] = IsDynamicBKColor;
                param["StaticColor"] = StaticColor.ToArgb();
                List<object> colorParam = new List<object>();
                if (DynamicBKColorRanges != null)
                {
                    foreach (DTParameterRangeColor item in DynamicBKColorRanges)
                    {
                        colorParam.Add(item.Params);
                    }
                }
                param["ColorParam"] = colorParam;
                return param;
            }
            set
            {
                setParam(value);
            }
        }

        private void setParam(Dictionary<string, object> value)
        {
            Caption = (string)value["Caption"];
            Expression = (string)value["Expression"];
            IsCalcProportion = (bool)value["CalcProportion"];
            if (value.ContainsKey("IsDynamicBKClr"))
            {
                IsDynamicBKColor = (bool)value["IsDynamicBKClr"];
            }
            if (value.ContainsKey("CarrierID"))
            {
                CarrierID = (byte)(int)value["CarrierID"];
            }

            if (value.ContainsKey("MoMtFlag"))
            {
                MoMtFlag = (byte)(int)value["MoMtFlag"];
            }
            if (value.ContainsKey("StaticColor"))
            {
                StaticColor = Color.FromArgb((int)value["StaticColor"]);
            }

            setColorParam(value);
        }

        private void setColorParam(Dictionary<string, object> value)
        {
            List<object> colorParam = value["ColorParam"] as List<object>;
            if (colorParam != null)
            {
                float fMin = float.MaxValue, fMax = float.MinValue;
                this.DynamicBKColorRanges = new List<DTParameterRangeColor>();
                foreach (object item in colorParam)
                {
                    DTParameterRangeColor color = new DTParameterRangeColor();
                    color.Params = (Dictionary<string, object>)item;
                    this.DynamicBKColorRanges.Add(color);

                    if (fMin > color.Min)
                    {
                        fMin = color.Min;
                    }
                    if (fMax < color.Max)
                    {
                        fMax = color.Max;
                    }
                }
                if (fMin != float.MaxValue && fMax != float.MinValue)
                {
                    valueRamgeMin = fMin;
                    valueRangeMax = fMax;
                }
            }
        }

        public string Caption { get; set; }
        public string Expression { get; set; } = string.Empty;
        public byte CarrierID { get; set; } = 1;//默认运营商是中国移动
        public byte MoMtFlag { get; set; }
        public bool IsCalcProportion
        { get; set; }
        public bool IsDynamicBKColor
        {
            get;
            set;
        }
        public Color StaticColor
        {
            get;
            set;
        }
        private float valueRamgeMin = 0;
        public float ValueRangeMin
        {
            get { return valueRamgeMin; }
            set
            {
                if (value <= valueRangeMax)
                {
                    valueRamgeMin = value;
                }
            }
        }
        private float valueRangeMax = 100;
        public float ValueRangeMax
        {
            get { return valueRangeMax; }
            set
            {
                if (value >= valueRamgeMin)
                {
                    valueRangeMax = value;
                }
            }
        }
        public List<DTParameterRangeColor> DynamicBKColorRanges { get; set; }

        public Color GetBKColorByValue(double value)
        {
            Color ret = StaticColor;
            if (IsDynamicBKColor && DynamicBKColorRanges != null)
            {
                foreach (DTParameterRangeColor item in DynamicBKColorRanges)
                {
                    if (item.Within((float)value))
                    {
                        ret = item.Value;
                        break;
                    }
                }
            }
            return ret;
        }
    }

}
