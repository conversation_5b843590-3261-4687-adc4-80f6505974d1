﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.NOP
{
    public class QueryTaskESResultInfo : DIYSQLBase
    {
        private readonly TaskEventItem taskItem = null;
        public QueryTaskESResultInfo(TaskEventItem taskItem, bool include2017 = false)
            : base(MainModel.GetInstance())
        {
            dbid = taskItem.DistrictID;
            this.taskItem = taskItem;
            this.include2017 = include2017;
        }

        bool isVer2017 = false;
        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                queryInThread(clientProxy);
                if (include2017)
                {
                    isVer2017 = true;
                    queryInThread(clientProxy);
                }
            }
            finally
            {
                clientProxy.Close();
            }

        }

        private readonly bool include2017 = false;

        protected override string getSqlTextString()
        {
            string cond = string.Format("fileid={0} and evtid={1} and seqid={2}"
            , taskItem.FileID, taskItem.EventID, taskItem.EventSN);
            return @"select fileid,evtid,seqid,rid,suggest,detail,msg,ctime from "
                + (isVer2017 ? taskItem.ESResultTbNameV2017 : taskItem.ESResultTbName) + " where " + cond;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] arr = new E_VType[8];
            int i = 0;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_VARYBIN;
            arr[i] = E_VType.E_String;
            return arr;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    ESResultInfo resultInfo = new ESResultInfo(isVer2017);
                    resultInfo.FileID = package.Content.GetParamInt();
                    resultInfo.EventID = package.Content.GetParamInt();
                    resultInfo.SeqID = package.Content.GetParamInt();
                    resultInfo.RelationID = package.Content.GetParamInt();
                    resultInfo.Suggest = package.Content.GetParamString();
                    resultInfo.Detail = package.Content.GetParamString();
                    resultInfo.FillMsg(package.Content.GetParamBytes());
                    resultInfo.CTime = package.Content.GetParamString();
                    if (isVer2017)
                    {
                        taskItem.ESResultInfoV2017 = resultInfo;
                    }
                    else
                    {
                        taskItem.ESResultInfo = resultInfo;
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    break;
                }
            }
        }

    }
}
