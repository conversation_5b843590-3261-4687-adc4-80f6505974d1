﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryEvtMsgInfoByRegion : DIYAnalyseByFileBackgroundBase
    {
        public bool IsAnaMsgCellInfo { get; set; } = false;
        public List<Event> ProblemEvtList { get; set; } = new List<Event>();
        public Dictionary<string, CelllMessageInfo> UnfitCellMsgInfoDic { get; set; } = new Dictionary<string, CelllMessageInfo>();
        public int UnfitParamCount { get; set; } = 0;
        List<int> selectEvtIdList = new List<int>();
        List<MsgParamSetting> selectParamList { get; set; }
        public QueryEvtMsgInfoByRegion(List<MsgParamSetting> paramList)
            : base(MainModel.GetInstance())
        {
            this.IncludeTestPoint = false;
            this.IncludeMessage = true;
            ServiceTypes.Clear();

            selectParamList = new List<MsgParamSetting>();
            foreach (MsgParamSetting paramSet in paramList)
            {
                if (paramSet.IsChecked)
                {
                    this.selectParamList.Add(paramSet);
                }
            }
        }
        public override string Name
        {
            get
            {
                return "小区参数导出(按区域)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 20000, 20043, "分析");
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            // Method intentionally left empty.
        }
        protected override void getReadyBeforeQuery()
        {
            selectEvtIdList = condition.EventIDs;
            UnfitCellMsgInfoDic = new Dictionary<string, CelllMessageInfo>();
            ProblemEvtList = new List<Event>();
            UnfitParamCount = 0;
        }
        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    List<DTData> dtDataList = new List<DTData>();
                    foreach (Event evt in file.Events)
                    {
                        dtDataList.Add(evt);
                        if (selectEvtIdList.Contains(evt.ID))
                        {
                            ProblemEvtList.Add(evt);
                        }
                    }

                    if (IsAnaMsgCellInfo)
                    {
                        WaitBox.Text = "正在分析层三信令小区参数信息......";
                        foreach (Message msg in file.Messages)
                        {
                            dtDataList.Add((DTData)msg);
                        }
                        dtDataList.Sort(comparer);

                        Event lastEvt = null;
                        int index = 0;
                        int progress = 0;
                        bool isCancel = dealDTData(dtDataList, ref lastEvt, ref index, ref progress);
                        if (isCancel)
                        {
                            return;
                        }
                    }
                }
            }
            catch (Exception ee)
            {
                System.Windows.Forms.MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }

        private bool dealDTData(List<DTData> dtDataList, ref Event lastEvt, ref int index, ref int progress)
        {
            foreach (DTData data in dtDataList)
            {
                if (WaitBox.CancelRequest)
                {
                    return true;
                }
                if (Math.Log(index++) * (10) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }

                if (data is Event)
                {
                    Event evt = data as Event;
                    lastEvt = evt;
                }
                else
                {

                    Message msg = data as Message;
                    doWithMessage(lastEvt, msg);
                }
            }
            return false;
        }

        private void doWithMessage(Event lastEvt, Message msg)
        {
            LTECell curCell = getCellSrc(lastEvt);
            if (curCell == null || curCell.Name == "0_0" || curCell.Name == "-1_-1")
            {
                return;
            }
            foreach (MsgParamSetting paramSet in selectParamList)
            {
                if (paramSet.RelevantMsgId == msg.ID)
                {
                    if (!string.IsNullOrEmpty(paramSet.ParamQueryName))
                    {
                        addParamInfoSingleIntValue(curCell, msg, paramSet);
                    }
                    else
                    {
                        addParamInfoMultiIntValue(curCell, msg, paramSet);
                    }
                }
            }
        }
        private LTECell getCellSrc(Event evt)
        {
            if (evt == null)
            {
                return null;
            }
            LTECell cell = new LTECell();
            int? lac = (int?)evt["LAC"];
            int? ci = (int?)evt["CI"];
            if (lac != null && ci != null)
            {
                cell = CellManager.GetInstance().GetLTECell(evt.DateTime, (int)lac, (int)ci);
                if (cell == null)
                {
                    cell = new LTECell();
                    cell.Name = lac + "_" + ci;
                    cell.TAC = (int)lac;
                    cell.ECI = (int)ci;
                    cell.BelongBTS = new LTEBTS();
                }
            }
            return cell;
        }
        private void addParamInfoSingleIntValue(LTECell curCell, Message msg, MsgParamSetting paramSet)
        {
            int msgValue = MessageDecodeHelper.GetMsgSingleInt(msg, paramSet.ParamQueryName);
            if (msgValue != int.MaxValue && msgValue != int.MinValue)
            {
                addParamInfoBase(curCell, paramSet, msgValue);
            }
        }
        private void addParamInfoMultiIntValue(LTECell curCell, Message msg, MsgParamSetting paramSet)
        {
            if (paramSet.ParamQueryNameList == null || paramSet.ParamQueryNameList.Count == 0)
            {
                return;
            }
            int msgValue = 0;
            bool isContinue = false;
            foreach (string curParamQueryName in paramSet.ParamQueryNameList)
            {
                addMsgValue(msg, ref msgValue, ref isContinue, curParamQueryName);
            }
            if (isContinue)
            {
                addParamInfoBase(curCell, paramSet, msgValue);
            }
        }

        private static void addMsgValue(Message msg, ref int msgValue, ref bool isContinue, string curParamQueryName)
        {
            int value = MessageDecodeHelper.GetMsgSingleInt(msg, curParamQueryName);
            int intVlaue = 0;
            if (value != int.MaxValue && msgValue != int.MinValue)
            {
                int[] intValueArray = null;
                switch (curParamQueryName)
                {
                    case "lte-rrc.q_OffsetCell":
                        intValueArray = new int[] { -24, -22, -20, -18, -16, -14, -12, -10, -8, -6, -5, -4, -3, -2, -1, 0, 1, 2, 3
                                , 4, 5, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24 };
                        if (value >= 0 && value < intValueArray.Length)
                        {
                            intVlaue = intValueArray[value];
                        }
                        break;
                    case "lte-rrc.q_Hyst":
                        intValueArray = new int[] { 0, 1, 2, 3, 4, 5, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24 };
                        if (value >= 0 && value < intValueArray.Length)
                        {
                            intVlaue = intValueArray[value];
                        }
                        break;
                    case "lte-rrc.a3_Offset":
                        intVlaue = value / 2;
                        break;
                    case "lte-rrc.hysteresis":
                        intVlaue = value / 2;
                        break;
                    default:
                        intVlaue = 0;
                        break;
                }
                msgValue += intVlaue;
                isContinue = true;
            }
        }

        private void addParamInfoBase(LTECell curCell, MsgParamSetting paramSet, object objValue)
        {
            try
            {
                paramSet.BtsType = curCell.Type;
                string strValue = "";
                if (!paramSet.IsFitParamValue(objValue,ref strValue))
                {
                    CelllMessageInfo info;
                    if (!UnfitCellMsgInfoDic.TryGetValue(curCell.Name, out info))
                    {
                        info = new CelllMessageInfo();
                        info.LteCellSrc = curCell;
                        UnfitCellMsgInfoDic.Add(curCell.Name, info);
                    }

                    if (!info.ParamInfoDic.ContainsKey(paramSet.ParamNameDes))
                    {
                        UnfitParamCount++;
                        MessageParamInfo paramInfo = new MessageParamInfo(paramSet.ParamNameDes, strValue);
                        paramInfo.ParamRightValueDes = paramSet.ParamRightValueDes;
                        info.ParamInfoDic.Add(paramSet.ParamNameDes, paramInfo);
                    }
                }
            }
            catch
            {
                //continue
            }
        }
        protected override void fireShowForm()
        {
            // Method intentionally left empty.
        }

        private readonly Comparer comparer = new Comparer();
        private class Comparer : IComparer<DTData>
        {
            public int Compare(DTData x, DTData y)
            {
                return x.SN - y.SN;
            }
        }
    }

    public class CelllMessageInfo
    {
        public CelllMessageInfo()
        {
            LteCellSrc = new LTECell();
            ParamInfoDic = new Dictionary<string, MessageParamInfo>();
        }
        public LTECell LteCellSrc { get; set; }

        public Dictionary<string, MessageParamInfo> ParamInfoDic { get; set; }
    }

    public class MessageParamInfo
    {
        public MessageParamInfo(string paraName, string paramCurrentValue)
        {
            this.ParamName = paraName;
            this.ParamCurrentValue = paramCurrentValue;
        }
        public string ParamName { get; set; }
        public string ParamCurrentValue { get; set; }
        public string ParamRightValueDes { get; set; }
        
    }

    public class MsgParamSetting
    {
        public MsgParamSetting(bool isAutoSetValue, string paraName)
        {
            setValue(isAutoSetValue, paraName);
        }

        public bool IsChecked { get; set; }
        public override string ToString()
        {
            return ParamNameDes ?? "";
        }

        public int RelevantMsgId { get; set; }
        
        public LTEBTSType BtsType { get; set; } = LTEBTSType.Outdoor;
        public string ParamNameDes { get; private set; }
        public string ParamQueryName { get; private set; }
        public List<string> ParamQueryNameList { get; private set; }

        /// <summary>
        /// 要求值在信令里对应的值
        /// </summary>
        public string ParamRightValue { get; set; }

        /// <summary>
        /// 要求值
        /// </summary>
        public string ParamRightValueDes { get; set; }

        private void setValue(bool isAutoSetValue, string paraName)
        {
            this.ParamNameDes = paraName;
            if (paraName == ParamName_LongDrxCycle)
            {
                this.RelevantMsgId = (int)EnumLteCellInfoMsgId.RRCConnectionReconfiguration;
                this.ParamQueryName = "lte-rrc.longDRX_CycleStartOffset";
                if (isAutoSetValue)
                {
                    this.ParamRightValueDes = "SF160";
                    this.ParamRightValue = "7";
                }
            }
            else if (paraName == ParamName_OnDurationTimer)
            {
                this.RelevantMsgId = (int)EnumLteCellInfoMsgId.RRCConnectionReconfiguration;
                this.ParamQueryName = "lte-rrc.onDurationTimer";
                if (isAutoSetValue)
                {
                    this.ParamRightValueDes = "PSF8";
                    this.ParamRightValue = "6";
                }
            }
            else if (paraName == ParamName_DrxInactivityTimer)
            {
                this.RelevantMsgId = (int)EnumLteCellInfoMsgId.RRCConnectionReconfiguration;
                this.ParamQueryName = "lte-rrc.drx_InactivityTimer";
                if (isAutoSetValue)
                {
                    this.ParamRightValueDes = "PSF60";
                    this.ParamRightValue = "12";
                }
            }
            else if (paraName == ParamName_DrxRetransmissionTimer)
            {
                this.RelevantMsgId = (int)EnumLteCellInfoMsgId.RRCConnectionReconfiguration;
                this.ParamQueryName = "lte-rrc.drx_RetransmissionTimer";
                if (isAutoSetValue)
                {
                    this.ParamRightValueDes = "PSF4";
                    this.ParamRightValue = "2";
                }
            }
            else if (paraName == ParamName_ShortDrxCycle)
            {
                this.RelevantMsgId = (int)EnumLteCellInfoMsgId.RRCConnectionReconfiguration;
                this.ParamQueryName = "lte-rrc.shortDRX_Cycle";
                if (isAutoSetValue)
                {
                    this.ParamRightValueDes = "SF20";
                    this.ParamRightValue = "5";
                }
            }
            else if (paraName == ParamName_DrxShortCycleTimer)
            {
                this.RelevantMsgId = (int)EnumLteCellInfoMsgId.RRCConnectionReconfiguration;
                this.ParamQueryName = "lte-rrc.drxShortCycleTimer";
                if (isAutoSetValue)
                {
                    this.ParamRightValueDes = "2";
                    this.ParamRightValue = "2";
                }
            }
            else if (paraName == ParamName_DefaultPagingCycle)
            {
                this.RelevantMsgId = (int)EnumLteCellInfoMsgId.SystemInformationBlocks;
                this.ParamQueryName = "lte-rrc.defaultPagingCycle";
                if (isAutoSetValue)
                {
                    this.ParamRightValueDes = "rf128";
                    this.ParamRightValue = "2";
                }
            }
            else if (paraName == ParamName_ReferenceSignalPower)
            {
                this.RelevantMsgId = (int)EnumLteCellInfoMsgId.SystemInformationBlocks;
                this.ParamQueryName = "lte-rrc.referenceSignalPower";
                if (isAutoSetValue)
                {
                    this.ParamRightValueDes = "15dBm";
                    this.ParamRightValue = "15";
                }
            }
            else if (paraName == ParamName_Pb)
            {
                this.RelevantMsgId = (int)EnumLteCellInfoMsgId.SystemInformationBlocks;
                this.ParamQueryName = "lte-rrc.p_b";
                if (isAutoSetValue)
                {
                    this.ParamRightValueDes = "1（室分单流0）";
                    this.ParamRightValue = "1；0";
                }
            }
            else if (paraName == ParamName_Pa)
            {
                this.RelevantMsgId = (int)EnumLteCellInfoMsgId.RRCConnectionSetup;
                this.ParamQueryName = "lte-rrc.p_a";
                if (isAutoSetValue)
                {
                    this.ParamRightValueDes = "-3（室分单流0）";
                    this.ParamRightValue = "2；4)";
                }
            }
            else if (paraName == ParamName_PreambleIRTargetPower)
            {
                this.RelevantMsgId = (int)EnumLteCellInfoMsgId.SystemInformationBlocks;
                this.ParamQueryName = "lte-rrc.preambleInitialReceivedTargetPower";
                if (isAutoSetValue)
                {
                    this.ParamRightValueDes = "-100dBm～-104dBm";
                    this.ParamRightValue = "8～12";
                }
            }
            else if (paraName == ParamName_PreambleTransMax)
            {
                this.RelevantMsgId = (int)EnumLteCellInfoMsgId.SystemInformationBlocks;
                this.ParamQueryName = "lte-rrc.preambleTransMax";
                if (isAutoSetValue)
                {
                    this.ParamRightValueDes = "n8，n10";
                    this.ParamRightValue = "5,6";
                }
            }
            else if (paraName == ParamName_PowerRampingStep)
            {
                this.RelevantMsgId = (int)EnumLteCellInfoMsgId.SystemInformationBlocks;
                this.ParamQueryName = "lte-rrc.powerRampingStep";
                if (isAutoSetValue)
                {
                    this.ParamRightValueDes = "dB2，dB4";
                    this.ParamRightValue = "1,2";
                }
            }
            else if (paraName == ParamName_P_max)
            {
                this.RelevantMsgId = (int)EnumLteCellInfoMsgId.SystemInformationBlocks;
                this.ParamQueryName = "lte-rrc.p_Max";
                if (isAutoSetValue)
                {
                    this.ParamRightValueDes = "23dBm";
                    this.ParamRightValue = "23";
                }
            }
            else if (paraName == ParamName_P0_NominalPUCCH)
            {
                this.RelevantMsgId = (int)EnumLteCellInfoMsgId.SystemInformationBlocks;
                this.ParamQueryName = "lte-rrc.p0_NominalPUCCH";
                if (isAutoSetValue)
                {
                    this.ParamRightValueDes = " -100dBm～-105dBm";
                    this.ParamRightValue = "-100～-105";
                }
            }
            else if (paraName == ParamName_Alpha)
            {
                this.RelevantMsgId = (int)EnumLteCellInfoMsgId.SystemInformationBlocks;
                this.ParamQueryName = "lte-rrc.alpha";
                if (isAutoSetValue)
                {
                    this.ParamRightValueDes = "0.8";
                    this.ParamRightValue = "5";
                }
            }
            else if (paraName == ParamName_P0NominalPusch)
            {
                this.RelevantMsgId = (int)EnumLteCellInfoMsgId.SystemInformationBlocks;
                this.ParamQueryName = "lte-rrc.p0_NominalPUSCH";
                if (isAutoSetValue)
                {
                    this.ParamRightValueDes = "-87dBm";
                    this.ParamRightValue = "-87";
                }
            }
            else if (paraName == ParamName_QRxLevMin)
            {
                this.RelevantMsgId = (int)EnumLteCellInfoMsgId.SystemInformationBlocks;
                this.ParamQueryName = "lte-rrc.q_RxLevMin";
                if (isAutoSetValue)
                {
                    this.ParamRightValueDes = "-126dBm";
                    this.ParamRightValue = "-63";
                }
            }
            else if (paraName == ParamName_S_IntraSearch)
            {
                this.RelevantMsgId = (int)EnumLteCellInfoMsgId.SystemInformationBlocks;
                this.ParamQueryName = "lte-rrc.s_IntraSearch";
                if (isAutoSetValue)
                {
                    this.ParamRightValueDes = "-70dBm";
                    this.ParamRightValue = "-35";
                }
            }
            else if (paraName == ParamName_Q_OffsetCellPlusQ_Hyst)
            {
                this.RelevantMsgId = (int)EnumLteCellInfoMsgId.SystemInformationBlocks;
                this.ParamQueryNameList = new List<string> { "lte-rrc.q_OffsetCell", "lte-rrc.q_Hyst" };
                if (isAutoSetValue)
                {
                    this.ParamRightValueDes = "2～4dB";
                    this.ParamRightValue = "2～4";
                }
            }
            else if (paraName == ParamName_A3offsetPlusHysteresis)
            {
                this.RelevantMsgId = (int)EnumLteCellInfoMsgId.RRCConnectionReconfiguration;
                this.ParamQueryNameList = new List<string> { "lte-rrc.a3_Offset", "lte-rrc.hysteresis" };
                if (isAutoSetValue)
                {
                    this.ParamRightValueDes = "2～4dB";
                    this.ParamRightValue = "2～4";
                }
            }
            else if (paraName == ParamName_A3Time_to_trigger)
            {
                this.RelevantMsgId = (int)EnumLteCellInfoMsgId.RRCConnectionReconfiguration;
                this.ParamQueryName = "lte-rrc.timeToTrigger";
                if (isAutoSetValue)
                {
                    this.ParamRightValueDes = "320ms";
                    this.ParamRightValue = "8";
                }
            }
            else if (paraName == ParamName_DifSysA2Gate)
            {
                this.RelevantMsgId = (int)EnumLteCellInfoMsgId.RRCConnectionReconfiguration;
                this.ParamQueryName = "lte-rrc.threshold_RSRP";
                if (isAutoSetValue)
                {
                    this.ParamRightValueDes = "-118dBm～-126dBm";
                    this.ParamRightValue = "14～22";
                }
            }
        }

        public bool IsFitParamValue(object obj, ref string strVlaue)
        {
            int intValue;
            bool isFit = true;
            strVlaue = obj.ToString();
            if (int.TryParse(obj.ToString(), out intValue))
            {
                intValue = (int)obj;
            }
            else
            {
                return false;
            }

            if (!GetParamSingleValue(this.ParamNameDes, obj.ToString(), ref strVlaue))
            {
                strVlaue = obj.ToString();
            }

            string[] strArray = ParamRightValue.Split('；');
            if (strArray.Length == 1)
            {
                strArray = ParamRightValue.Split(';');
            }

            if (strArray.Length > 1)
            {
                if (BtsType == LTEBTSType.Indoor)
                {
                    isFit = isValueFit(strArray[1], intValue);
                }
                else
                {
                    isFit = isValueFit(strArray[0], intValue);
                }
            }
            else if (strArray.Length == 1)
            {
                isFit = isValueFit(strArray[0], intValue);
            }

            return isFit;
        }

        private bool isValueFit(string strRightValueRange,int curValue)
        {
            StringBuilder strbSingleRightValue = new StringBuilder();
            foreach (char chr in strRightValueRange)
            {
                if (chr == ',' || chr == '，')
                {
                    if (isSingleFit(strbSingleRightValue.ToString(), curValue))
                    {
                        return true;
                    }
                    strbSingleRightValue = new StringBuilder();
                }
                else if (chr == '~' || chr == '～')
                {
                    strbSingleRightValue.Append(chr);
                }
                else
                {
                    strbSingleRightValue.Append(chr);
                }
            }

            if (isSingleFit(strbSingleRightValue.ToString(), curValue))
            {
                return true;
            }
            return false;
        }

        private bool isSingleFit(string strSingleRightValue, int curValue)
        {
            int intRightValue;
            if (strSingleRightValue.Contains("~") || strSingleRightValue.Contains("～"))
            {
                string[] strArray = strSingleRightValue.Split('~');
                if (strArray.Length == 1)
                {
                    strArray = strSingleRightValue.Split('～');
                }

                int intRightValue1;
                if (strArray.Length > 1 && int.TryParse(strArray[0], out intRightValue) && int.TryParse(strArray[1], out intRightValue1))
                {
                    bool isValid = curValue >= intRightValue && curValue <= intRightValue1;
                    if (isValid)
                    {
                        return true;
                    }
                }
            }
            else if (int.TryParse(strSingleRightValue, out intRightValue) && intRightValue == curValue)
            {
                return true;
            }
            return false;
        }

        public static bool GetParamSingleValue(string paramName, string strVlaue, ref string strVlaueDes)
        {
            int intValue;
            strVlaueDes = "";
            if (!int.TryParse(strVlaue, out intValue))
            {
                return false;
            }

            if (paramName == MsgParamSetting.ParamName_LongDrxCycle)
            {
                string[] strValueArray = { "SF10", "SF20", "SF32", "SF40", "SF64", "SF80", "SF128", "SF160", "SF256", "SF320"
                                                 , "SF512", "SF640", "SF1024", "SF1280", "SF2048", "SF2560" };
                if (intValue >= 0 && intValue < strValueArray.Length)
                {
                    strVlaueDes = strValueArray[intValue];
                }
            }
            else if (paramName == MsgParamSetting.ParamName_OnDurationTimer)
            {
                string[] strValueArray = { "PSF1","PSF2", "PSF3", "PSF4", "PSF5", "PSF6", "PSF8", "PSF10", "PSF20", "PSF30"
                                                 , "PSF40", "PSF50", "PSF60", "PSF80", "PSF100", "PSF200" };
                if (intValue >= 0 && intValue < strValueArray.Length)
                {
                    strVlaueDes = strValueArray[intValue];
                }
            }
            else if (paramName == MsgParamSetting.ParamName_DrxInactivityTimer)
            {
                string[] strValueArray = { "PSF1", "PSF2", "PSF3", "PSF4", "PSF5", "PSF6", "PSF8", "PSF10", "PSF20", "PSF30"
                                                 , "PSF40", "PSF50", "PSF60", "PSF80", "PSF100", "PSF200", "PSF300", "PSF500", "PSF750"
                                                 , "PSF1280", "PSF1920", "PSF2560", "PSF0-v1020", "spare9", "spare8", "spare7", "spare6"
                                                 , "spare5", "spare4", "spare3", "spare2", "spare1" };
                if (intValue >= 0 && intValue < strValueArray.Length)
                {
                    strVlaueDes = strValueArray[intValue];
                }
            }
            else if (paramName == MsgParamSetting.ParamName_DrxRetransmissionTimer)
            {
                string[] strValueArray = { "PSF1", "PSF2", "PSF4", "PSF6", "PSF8", "PSF16", "PSF24", "PSF33" };
                if (intValue >= 0 && intValue < strValueArray.Length)
                {
                    strVlaueDes = strValueArray[intValue];
                }
            }
            else if (paramName == MsgParamSetting.ParamName_ShortDrxCycle)
            {
                string[] strValueArray = { "SF2", "SF5", "SF8", "SF10", "SF16", "SF20", "SF32", "SF40", "SF64", "SF80"
                                                 , "SF128", "SF160", "SF256", "SF320", "SF512", "SF640" };
                if (intValue >= 0 && intValue < strValueArray.Length)
                {
                    strVlaueDes = strValueArray[intValue];
                }
            }
            else if (paramName == MsgParamSetting.ParamName_DrxShortCycleTimer)
            {
                if (intValue >= 1 && intValue <= 16)//INTEGER （1..16）
                {
                    strVlaueDes = intValue.ToString();
                }
            }
            else if (paramName == MsgParamSetting.ParamName_DefaultPagingCycle)
            {
                string[] strValueArray = { "rf32", "rf64", "rf128", "rf256" };
                if (intValue >= 0 && intValue < strValueArray.Length)
                {
                    strVlaueDes = strValueArray[intValue];
                }
            }
            else if (paramName == MsgParamSetting.ParamName_ReferenceSignalPower)
            {
                if (intValue >= -60 && intValue <= 50)//INTEGER (-60..50)
                {
                    strVlaueDes = intValue.ToString() + "dBm";
                }
            }
            else if (paramName == MsgParamSetting.ParamName_Pb)
            {
                if (intValue >= 0 && intValue <= 3)//INTEGER (0..3)
                {
                    strVlaueDes = intValue.ToString();
                }
            }
            else if (paramName == MsgParamSetting.ParamName_Pa)
            {
                string[] strValueArray = { "-6", "-4.77", "-3", "-1.77", "0", "1", "2", "3" };
                if (intValue >= 0 && intValue < strValueArray.Length)
                {
                    strVlaueDes = strValueArray[intValue];
                }
            }
            else if (paramName == MsgParamSetting.ParamName_PreambleIRTargetPower)
            {
                string[] strValueArray = { "-120dBm", "-118dBm", "-116dBm", "-114dBm", "-112dBm", "-110dBm", "-108dBm", "-106dBm"
                                             , "-104dBm", "-102dBm", "-100dBm", "-98dBm", "-96dBm", "-94dBm", "-92dBm", "-90dBm" };
                if (intValue >= 0 && intValue < strValueArray.Length)
                {
                    strVlaueDes = strValueArray[intValue];
                }
            }
            else if (paramName == MsgParamSetting.ParamName_PreambleTransMax)
            {
                string[] strValueArray = { "n3", "n4", "n5", "n6", "n7", "n8", "n10", "n20", "n50", "n100", "n200" };
                if (intValue >= 0 && intValue < strValueArray.Length)
                {
                    strVlaueDes = strValueArray[intValue];
                }
            }
            else if (paramName == MsgParamSetting.ParamName_PowerRampingStep)
            {
                string[] strValueArray = { "dB0", "dB2", "dB4", "dB6" };
                if (intValue >= 0 && intValue < strValueArray.Length)
                {
                    strVlaueDes = strValueArray[intValue];
                }
            }
            else if (paramName == MsgParamSetting.ParamName_P_max)
            {
                if (intValue >= -30 && intValue <= 33)//INTEGER (-30..33)
                {
                    strVlaueDes = intValue.ToString() + "dBm";
                }
            }
            else if (paramName == MsgParamSetting.ParamName_P0_NominalPUCCH)
            {
                if (intValue >= -127 && intValue <= -96)//INTEGER (-127..-96)
                {
                    strVlaueDes = intValue.ToString() + "dBm";
                }
            }
            else if (paramName == MsgParamSetting.ParamName_Alpha)
            {
                string[] strValueArray = { "0", "0.4", "0.5", "0.6", "0.7", "0.8", "0.9", "1" };
                if (intValue >= 0 && intValue < strValueArray.Length)
                {
                    strVlaueDes = strValueArray[intValue];
                }
            }
            else if (paramName == MsgParamSetting.ParamName_P0NominalPusch)
            {
                if (intValue >= -126 && intValue <= 24)//INTEGER (-126..24)
                {
                    strVlaueDes = intValue.ToString() + "dBm";
                }
            }
            else if (paramName == MsgParamSetting.ParamName_QRxLevMin)
            {
                strVlaueDes = (intValue * 2).ToString() + "dBm";
            }
            else if (paramName == MsgParamSetting.ParamName_S_IntraSearch)
            {
                strVlaueDes = (intValue * 2).ToString() + "dBm";
            }
            else if (paramName == MsgParamSetting.ParamName_Q_OffsetCellPlusQ_Hyst)
            {
                strVlaueDes = intValue.ToString() + "dB";
            }
            else if (paramName == MsgParamSetting.ParamName_A3offsetPlusHysteresis)
            {
                strVlaueDes = intValue.ToString() + "dB";
            }
            else if (paramName == MsgParamSetting.ParamName_A3Time_to_trigger)
            {
                string[] strValueArray = { "0ms", "40ms", "64ms", "80ms", "100ms", "128ms", "160ms", "256ms", "320ms", "480ms"
                                                 , "512ms", "640ms", "1024ms", "1280ms", "2560ms", "5120ms" };
                if (intValue >= 0 && intValue < strValueArray.Length)
                {
                    strVlaueDes = strValueArray[intValue];
                }
            }
            else if (paramName == MsgParamSetting.ParamName_DifSysA2Gate)
            {
                strVlaueDes = (intValue - 140).ToString() + "dBm";
            }

            if (!string.IsNullOrEmpty(strVlaueDes))
            {
                return true;
            }
            return false;
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["ParamNameDes"] = ParamNameDes;
                param["ParamRightValue"] = ParamRightValue;
                param["ParamRightValueDes"] = ParamRightValueDes;
                param["IsChecked"] = IsChecked;
                return param;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                if (value.ContainsKey("ParamNameDes") && value["ParamNameDes"] != null)
                {
                    this.ParamNameDes = value["ParamNameDes"].ToString();
                }
                if (value.ContainsKey("ParamRightValue") && value["ParamRightValue"] != null)
                {
                    this.ParamRightValue = value["ParamRightValue"].ToString();
                }
                if (value.ContainsKey("ParamRightValueDes") && value["ParamRightValueDes"] != null)
                {
                    this.ParamRightValueDes = value["ParamRightValueDes"].ToString();
                }
                if (value.ContainsKey("IsChecked") && value["IsChecked"] != null)
                {
                    this.IsChecked = (bool)value["IsChecked"];
                }
            }
        }

        public static readonly string ParamName_LongDrxCycle = "LongDrxCycle";
        public static readonly string ParamName_OnDurationTimer = "OnDurationTimer";
        public static readonly string ParamName_DrxInactivityTimer = "DrxInactivityTimer";
        public static readonly string ParamName_DrxRetransmissionTimer = "DrxRetransmissionTimer";
        public static readonly string ParamName_ShortDrxCycle = "ShortDrxCycle";
        public static readonly string ParamName_DrxShortCycleTimer = "DrxShortCycleTimer";
        public static readonly string ParamName_DefaultPagingCycle = "DefaultPagingCycle";
        public static readonly string ParamName_ReferenceSignalPower = "Reference Signal Power";
        public static readonly string ParamName_Pb = "Pb";
        public static readonly string ParamName_Pa = "Pa";
        public static readonly string ParamName_PreambleIRTargetPower = "PreambleInitial ReceivedTargetPower";
        public static readonly string ParamName_PreambleTransMax = "PreambleTransMax";
        public static readonly string ParamName_PowerRampingStep = "PowerRampingStep";
        public static readonly string ParamName_P_max = "P-max";
        public static readonly string ParamName_P0_NominalPUCCH = "p0-NominalPUCCH";
        public static readonly string ParamName_Alpha = "Alpha";
        public static readonly string ParamName_P0NominalPusch = "P0NominalPusch";
        public static readonly string ParamName_QRxLevMin = "QRxLevMin";
        public static readonly string ParamName_S_IntraSearch = "s-IntraSearch";
        public static readonly string ParamName_Q_OffsetCellPlusQ_Hyst = "q-OffsetCell + q-Hyst";
        public static readonly string ParamName_A3offsetPlusHysteresis = "同频 A3 offset + Hysteresis";
        public static readonly string ParamName_A3Time_to_trigger = "同频 A3 Time-to-trigger";
        public static readonly string ParamName_DifSysA2Gate = "异系统A2门限(盲重定向）";
    }

    public enum EnumLteCellInfoMsgId
    {
        RRCConnectionReconfiguration = 1093625860,
        SystemInformationBlocks = 1093625088,
        RRCConnectionSetup = 1093625603
    }
}
