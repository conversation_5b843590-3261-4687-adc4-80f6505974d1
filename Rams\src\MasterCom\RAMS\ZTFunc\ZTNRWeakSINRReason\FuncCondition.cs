﻿using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRFuncCondition
    {
        public NRFuncCondition()
        {
            Reasons = new List<NRReasonBase>();
            Reasons.Add(new NRReasonWeakCover());
            Reasons.Add(new NRReasonMultiCover());
            Reasons.Add(new NRReasonMod3());
            Reasons.Add(new NRReasonChangeFreq());
            Reasons.Add(new NRReasonHandOverProblem());
            Reasons.Add(new NRReasonHandOverUnTimely());
            Reasons.Add(new NRReasonsOverCover());
            Reasons.Add(new NRReasonsBackCover());
            Reasons.Add(new NRReasonsIndoorOutCover());
            Reasons.Add(new NRReasonSuddenWeak());
            Reasons.Add(NRReasonUnknow.Instance);
        }
        
        public float MaxSINR { get; set; } = 3;

        public List<NRReasonBase> Reasons
        {
            get;
            set;
        }

        public bool IsAnaLte { get; set; }

        /// <summary>
        /// 判断质差原因。应先调用IsValid（）判断是否为质差点，然后才判断原因。
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        public NRReasonBase JudgeReason(TestPoint tp, bool isAnaLte)
        {
            foreach (NRReasonBase item in Reasons)
            {
                if (item.IsValid(tp, NRTpHelper.NrTpManager))
                {
                    return item;
                }
                else if (isAnaLte && item.IsValid(tp, NRTpHelper.NrLteTpManager))
                {
                    return item;
                }
            }
            return NRReasonUnknow.Instance;
        }

        /// <summary>
        /// 是否为质差点
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        internal bool IsValid(TestPoint tp, bool isAnaLte)
        {
            object obj = NRTpHelper.NrTpManager.GetSCellSinr(tp);
            if (isAnaLte && obj == null)
            {
                obj = NRTpHelper.NrLteTpManager.GetSCellSinr(tp);
            }
            if (obj == null)
            {
                return false;
            }
            float sinr = float.Parse(obj.ToString());
            return sinr <= MaxSINR && -50 <= sinr;
        }

        public Dictionary<string, object> ReasonsParam
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                foreach (NRReasonBase reason in this.Reasons)
                {
                    param[reason.Name] = reason.Param;
                }
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                List<NRReasonBase> reasonList = new List<NRReasonBase>();
                foreach (var keyValuePair in param)
                {
                    foreach (NRReasonBase reason in this.Reasons)
                    {
                        if (keyValuePair.Key == reason.Name)
                        {
                            reason.Param = (Dictionary<string, object>)param[keyValuePair.Key];
                            reasonList.Add(reason);
                            this.Reasons.Remove(reason);
                            break;
                        }
                    }
                }
                this.Reasons = reasonList;
            }
        }
    }
}
