﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.CQT
{
    public class CQTCfgManager
    {
        private static CQTCfgManager instance = null;
        private CQTCfgManager()
        {
            CoverTypeList = new List<CQTCoverType>();
            DensityTypeList = new List<CQTDensityType>();
            NetworkypeList = new List<CQTNetworkType>();
            PointTypeList = new List<CQTPointType>();
            SpaceTypeList = new List<CQTSpaceType>();
            MasterCom.Util.UiEx.WaitTextBox.Show("正在获取CQT配置...",initBaseInfo);
        }

        public static CQTCfgManager GetInstance()
        {
            if (instance == null)
            {
                instance = new CQTCfgManager();
            }
            return instance;
        }

        private void initBaseInfo()
        {
            try
            {
                DIYQueryCQTCfg query = new DIYQueryCQTCfg(MainModel.GetInstance());
                string sqlTxt = "select densityType,densityTypeName,comment from tb_cqt_cfg_densityType";
                query.SetSQLTxt(sqlTxt);
                query.Query();
                foreach (CQTCfgTypeBase infoBase in query.GetResults())
                {
                    DensityTypeList.Add(new CQTDensityType(infoBase));
                }
                sqlTxt = "select coverType,coverTypeName,comment from tb_cqt_cfg_coverType";
                query.SetSQLTxt(sqlTxt);
                query.Query();
                foreach (CQTCfgTypeBase infoBase in query.GetResults())
                {
                    CoverTypeList.Add(new CQTCoverType(infoBase));
                }
                sqlTxt = "select networkType,networkTypeName,comment from tb_cqt_cfg_networkType";
                query.SetSQLTxt(sqlTxt);
                query.Query();
                foreach (CQTCfgTypeBase infoBase in query.GetResults())
                {
                    NetworkypeList.Add(new CQTNetworkType(infoBase));
                }
                sqlTxt = "select pointType,pointTypeName,comment from tb_cqt_cfg_pointType";
                query.SetSQLTxt(sqlTxt);
                query.Query();
                foreach (CQTCfgTypeBase infoBase in query.GetResults())
                {
                    PointTypeList.Add(new CQTPointType(infoBase));
                }
                sqlTxt = "select spaceType,spaceTypeName,comment from tb_cqt_cfg_spaceType";
                query.SetSQLTxt(sqlTxt);
                query.Query();
                foreach (CQTCfgTypeBase infoBase in query.GetResults())
                {
                    SpaceTypeList.Add(new CQTSpaceType(infoBase));
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                MasterCom.Util.UiEx.WaitTextBox.Close();
            }
        }

        public CQTDensityType GetCQTDensityType(int typeID)
        {
            CQTDensityType ret = null;
            foreach (CQTDensityType typeInfo in DensityTypeList)
            {
                if (typeInfo.ID == typeID)
                {
                    ret = typeInfo;
                    break;
                }
            }
            return ret;
        }
        public CQTDensityType GetCQTDensityType(string typeName)
        {
            CQTDensityType ret = null;
            foreach (CQTDensityType typeInfo in DensityTypeList)
            {
                if (typeInfo.Name == typeName)
                {
                    ret = typeInfo;
                    break;
                }
            }
            return ret;
        }
        public CQTCoverType GetCQTCoverType(int typeID)
        {
            CQTCoverType ret = null;
            foreach (CQTCoverType typeInfo in CoverTypeList)
            {
                if (typeInfo.ID == typeID)
                {
                    ret = typeInfo;
                    break;
                }
            }
            return ret;
        }
        public CQTCoverType GetCQTCoverType(string typeName)
        {
            CQTCoverType ret = null;
            foreach (CQTCoverType typeInfo in CoverTypeList)
            {
                if (typeInfo.Name == typeName)
                {
                    ret = typeInfo;
                    break;
                }
            }
            return ret;
        }
        public List<CQTNetworkType> GetCQTNetworkTypes(List<int> typeIDs)
        {
            List<CQTNetworkType> ret = new List<CQTNetworkType>();
            foreach (CQTNetworkType item in NetworkypeList)
            {
                if (typeIDs.Contains(item.ID))
                {
                    ret.Add(item);
                }
            }
            return ret;
        }
        public CQTNetworkType GetCQTNetworkType(int typeID)
        {
            CQTNetworkType ret = null;
            foreach (CQTNetworkType typeInfo in NetworkypeList)
            {
                if (typeInfo.ID == typeID)
                {
                    ret = typeInfo;
                    break;
                }
            }
            return ret;
        }
        public CQTNetworkType GetCQTNetworkType(string typeName)
        {
            CQTNetworkType ret = null;
            foreach (CQTNetworkType typeInfo in NetworkypeList)
            {
                if (typeInfo.Name == typeName)
                {
                    ret = typeInfo;
                    break;
                }
            }
            return ret;
        }
        public CQTPointType GetCQTPointType(int typeID)
        {
            CQTPointType ret = null;
            foreach (CQTPointType typeInfo in PointTypeList)
            {
                if (typeInfo.ID == typeID)
                {
                    ret = typeInfo;
                    break;
                }
            }
            return ret;
        }
        public CQTPointType GetCQTPointType(string typeName)
        {
            CQTPointType ret = null;
            foreach (CQTPointType typeInfo in PointTypeList)
            {
                if (typeInfo.Name == typeName)
                {
                    ret = typeInfo;
                    break;
                }
            }
            return ret;
        }
        public CQTSpaceType GetCQTSpaceType(int typeID)
        {
            CQTSpaceType ret = null;
            foreach (CQTSpaceType typeInfo in SpaceTypeList)
            {
                if (typeInfo.ID == typeID)
                {
                    ret = typeInfo;
                    break;
                }
            }
            return ret;
        }
        public CQTSpaceType GetCQTSpaceType(string typeName)
        {
            CQTSpaceType ret = null;
            foreach (CQTSpaceType typeInfo in SpaceTypeList)
            {
                if (typeInfo.Name == typeName)
                {
                    ret = typeInfo;
                    break;
                }
            }
            return ret;
        }
        public List<CQTDensityType> DensityTypeList { get; set; }
        public List<CQTCoverType> CoverTypeList { get; set; }
        public List<CQTNetworkType> NetworkypeList { get; set; }
        public List<CQTPointType> PointTypeList { get; set; }
        public List<CQTSpaceType> SpaceTypeList { get; set; }
    }

#region CQT基础信息
    public class CQTCfgTypeBase
    {
        public int ID { get; set; }
        public string Name { get; set; }
        public string Comment { get; set; }
        public CQTCfgTypeBase()
        { }
        public CQTCfgTypeBase(CQTCfgTypeBase baseType)
        {
            ID = baseType.ID;
            Name = baseType.Name;
            Comment = baseType.Comment;
        }
        public override string ToString()
        {
            return Name;
        }
        public static CQTCfgTypeBase FillFrom(MasterCom.RAMS.Net.Content content)
        {
            CQTCfgTypeBase info = new CQTCfgTypeBase();
            info.ID = content.GetParamInt();
            info.Name = content.GetParamString();
            info.Comment = content.GetParamString();
            return info;
        }
    }

    /// <summary>
    /// CQT覆盖类型
    /// </summary>
    public class CQTCoverType : CQTCfgTypeBase
    {
        public CQTCoverType(CQTCfgTypeBase baseType)
            : base(baseType)
        {
        }

        public static bool checkIllegal(string coverType)
        {
            List<string> cqtCoverTypeNameList = new List<string>();
            foreach (CQTCoverType cqtCoverType in CQTCfgManager.GetInstance().CoverTypeList)
            {
                cqtCoverTypeNameList.Add(cqtCoverType.Name);
            }
            if (cqtCoverTypeNameList.Contains(coverType))
            {
                return false;
            }
            return true;
        }
    }

    /// <summary>
    /// CQT密度类型
    /// </summary>
    public class CQTDensityType : CQTCfgTypeBase
    {
        public CQTDensityType(CQTCfgTypeBase baseType)
            : base(baseType)
        {
        }
        
        public static bool checkIllegal(string density)
        {
            List<string> cqtDensityTypeNameList = new List<string>();
            foreach (CQTDensityType cqtDensityType in CQTCfgManager.GetInstance().DensityTypeList)
            {
                cqtDensityTypeNameList.Add(cqtDensityType.Name);
            }
            if (cqtDensityTypeNameList.Contains(density))
            {
                return false;
            }
            return true;
        }
    }
    /// <summary>
    /// CQT网络类型
    /// </summary>
    public class CQTNetworkType : CQTCfgTypeBase
    {
        public CQTNetworkType(CQTCfgTypeBase baseType)
            : base(baseType)
        {
        }
        
        public static bool checkIllegal(string netType, char cSplit)
        {
            List<string> cqtNetNameList = new List<string>();
            foreach (CQTNetworkType cqtNetWork in CQTCfgManager.GetInstance().NetworkypeList)
            {
                cqtNetNameList.Add(cqtNetWork.Name);
            }
            string[] netArrary = netType.Split(cSplit);
            foreach (string type in netArrary)
            {
                if (!cqtNetNameList.Contains(type))
                {
                    return true;
                }
            }
            return false;
        }
    }

    /// <summary>
    /// CQT地点类型
    /// </summary>
    public class CQTPointType : CQTCfgTypeBase
    {
        public CQTPointType(CQTCfgTypeBase baseType)
            : base(baseType)
        {
        }
        
        public static bool checkIllegal(string pointType)
        {
            List<string> cqtPointTypeNameList = new List<string>();
            foreach (CQTPointType cqtPointType in CQTCfgManager.GetInstance().PointTypeList)
            {
                cqtPointTypeNameList.Add(cqtPointType.Name);
            }
            if (cqtPointTypeNameList.Contains(pointType))
            {
                return false;
            }
            return true;
        }
    }

    /// <summary>
    /// CQT地点建筑类型
    /// </summary>
    public class CQTSpaceType : CQTCfgTypeBase
    {
        public CQTSpaceType(CQTCfgTypeBase baseType)
            : base(baseType)
        {
        }
        
        public static bool checkIllegal(string spaceType)
        {
            List<string> cqtSpaceTypeNameList = new List<string>();
            foreach (CQTSpaceType cqtSpaceType in CQTCfgManager.GetInstance().SpaceTypeList)
            {
                cqtSpaceTypeNameList.Add(cqtSpaceType.Name);
            }
            if (cqtSpaceTypeNameList.Contains(spaceType))
            {
                return false;
            }
            return true;
        }
    }
#endregion

}
