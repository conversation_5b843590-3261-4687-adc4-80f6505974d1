using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.IO;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.Grid;
using MasterCom.MControls;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using MasterCom.RAMS.Net;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTScanLTEInterferenceListForm : MinCloseForm
    {
        List<ScanLTEInterType> resultList = new List<ScanLTEInterType>();
        int sampleTotal = 0;

        public ZTScanLTEInterferenceListForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            mapForm = mainModel.MainForm.GetMapForm();
            init();
        }
        private MapForm mapForm = null;

        private void init()
        {
            this.olvColumnType.AspectGetter = delegate(object row)
            {
                if (row is ScanLTEInterType)
                {
                    ScanLTEInterType item = row as ScanLTEInterType;
                    return item.interType;
                }
                else if (row is ScanLTEInterference)
                {
                    ScanLTEInterference item = row as ScanLTEInterference;
                    return item.SN;
                }
                else if (row is ScanLTEFreq)
                {
                    ScanLTEFreq item = row as ScanLTEFreq;
                    return item.SN;
                }
                return null;
            };

            this.olvColumnSample.AspectGetter = delegate(object row)
            {
                if (row is ScanLTEInterType)
                {
                    ScanLTEInterType item = row as ScanLTEInterType;
                    return item.typeList.Count;
                }
                return "";
            };

            this.olvColumnRate.AspectGetter = delegate(object row)
            {
                if (row is ScanLTEInterType)
                {
                    ScanLTEInterType item = row as ScanLTEInterType;
                    return sampleTotal == 0 ? "-" : Math.Round(100*item.typeList.Count / (double)sampleTotal, 2).ToString();
                }
                return "";
            };

            this.olvColumnARFCN.AspectGetter = delegate(object row)
            {
                if (row is ScanLTEFreq)
                {
                    ScanLTEFreq item = row as ScanLTEFreq;
                    return item.freq;
                }
                return "";
            };

            this.olvColumnRxlev.AspectGetter = delegate(object row)
            {
                if (row is ScanLTEFreq)
                {
                    ScanLTEFreq item = row as ScanLTEFreq;
                    return item.rxlev;
                }
                return "";
            };

            this.olvColumnLongitude.AspectGetter = delegate(object row)
            {
                if (row is ScanLTEInterference)
                {
                    ScanLTEInterference item = row as ScanLTEInterference;
                    return item.tp.Longitude;
                }
                return "";
            };

            this.olvColumnLatitude.AspectGetter = delegate(object row)
            {
                if (row is ScanLTEInterference)
                {
                    ScanLTEInterference item = row as ScanLTEInterference;
                    return item.tp.Latitude;
                }
                return "";
            };

            this.ListViewInter.CanExpandGetter = delegate(object x)
            {
                return x is ScanLTEInterType || x is ScanLTEInterference;
            };
            this.ListViewInter.ChildrenGetter = delegate(object x)
            {
                if (x is ScanLTEInterType)
                {
                    ScanLTEInterType typeInfo = x as ScanLTEInterType;
                    return typeInfo.typeList;
                }
                else if (x is ScanLTEInterference)
                {
                    ScanLTEInterference interInfo = x as ScanLTEInterference;
                    return interInfo.freqList;
                }
                else
                {
                    return "";
                }
            };
        }

        public void FillData(Dictionary<string, ScanLTEInterType> dicInterAll)
        {
            ListViewInter.RebuildColumns();
            ListViewInter.ClearObjects();

            resultList = new List<ScanLTEInterType>();
            sampleTotal = 0;

            foreach(string type in dicInterAll.Keys)
            {
                resultList.Add(dicInterAll[type]);
                sampleTotal += dicInterAll[type].typeList.Count;
            }

            ListViewInter.SetObjects(resultList);//(MainModel.TdPoorBlerRoadCovLst);
            MainModel.RefreshLegend();
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GetCellLayer().Invalidate();
            }
        }

        private void listViewTotal_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (ListViewInter.SelectedObject is ScanLTEInterType)
            {
                ScanLTEInterType interType = ListViewInter.SelectedObject as ScanLTEInterType;

                MainModel.DTDataManager.Clear();
                foreach (ScanLTEInterference interInfo in interType.typeList)
                {
                    mModel.DTDataManager.Add(interInfo.tp);
                }
                mModel.FireDTDataChanged(this);
            }
            else if (ListViewInter.SelectedObject is ScanLTEInterference)
            {
                MainModel.DTDataManager.Clear();
                ScanLTEInterference interInfo = ListViewInter.SelectedObject as ScanLTEInterference;
                MainModel.DTDataManager.Add(interInfo.tp);
                mModel.FireDTDataChanged(this);
            }
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            ListViewInter.ExpandAll();
        }

        private void miCallapsAll_Click(object sender, EventArgs e)
        {
            ListViewInter.CollapseAll();
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(ListViewInter);
        }
    }
}