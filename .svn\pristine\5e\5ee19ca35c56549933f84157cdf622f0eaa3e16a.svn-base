﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class NRSmallStationAcceptQuery : NRStationAcceptQuery
    {
        public NRSmallStationAcceptQuery(MainModel mainModel)
           : base(mainModel)
        {
        }

        public override string Name
        {
            get
            {
                return "NR小站验收";
            }
        }

        protected override bool isValidCondition()
        {
            List<FileInfo> nrFiles = new List<FileInfo>();
            foreach (var file in Condition.FileInfos)
            {
                ServiceName name = ServiceTypeManager.getServiceNameFromTypeID(file.ServiceType);
                if (name == ServiceName.NR)
                {
                    nrFiles.Add(file);
                }
            }
            if (nrFiles.Count == 0)
            {
                MessageBox.Show("请选择5G文件进行单验");
                return false;
            }
            Condition.FileInfos = nrFiles;

            NRSmallStationAcceptCondition cond = new NRSmallStationAcceptCondition();
            cond.Init();
            FolderBrowserDialog fbd = new FolderBrowserDialog();
            if (fbd.ShowDialog() == DialogResult.OK)
            {
                cond.SaveFolder = fbd.SelectedPath;
                initManager(cond);
                return true;
            }
            return false;
        }

        protected override void initManager(StationAcceptConditionBase cond)
        {
            manager = new NRSmallStationAcceptManager();
            manager.SetAcceptCond(cond);
        }
    }

    public class NRSmallStationAcceptCondition : NRStationAcceptCondition
    {
        public override void Init()
        {
            Standard = new NRSmallThroughputStandard();
            Standard.Init();
        }
    }

    public class NRSmallThroughputStandard : NRThroughputStandard
    {
        public override void Init()
        {
            ThroughputStandardList = new List<ThroughputStandard>()
            {
                new ThroughputStandard(NRServiceName.SA, 60, 1,
                  new ThroughputStandard.DataInfo( 120, 0, 72),
                  new ThroughputStandard.DataInfo(12, 0, 3)),
                new ThroughputStandard(NRServiceName.SA, 60, 2,
                  new ThroughputStandard.DataInfo( 180, 0, 120),
                  new ThroughputStandard.DataInfo(18, 0, 3)),
                new ThroughputStandard(NRServiceName.SA, 60, 4,
                  new ThroughputStandard.DataInfo( 360, 0, 210),
                  new ThroughputStandard.DataInfo(42, 0, 3)),

                new ThroughputStandard(NRServiceName.SA, 100, 1,
                  new ThroughputStandard.DataInfo( 200, 0, 120),
                  new ThroughputStandard.DataInfo(20, 0, 5)),
                new ThroughputStandard(NRServiceName.SA, 100, 2,
                  new ThroughputStandard.DataInfo( 300, 0, 200),
                  new ThroughputStandard.DataInfo(30, 0, 5)),
                new ThroughputStandard(NRServiceName.SA, 100, 4,
                  new ThroughputStandard.DataInfo( 600, 0, 350),
                  new ThroughputStandard.DataInfo(70, 0, 5)),

                new ThroughputStandard(NRServiceName.NSA, 60, 1,
                  new ThroughputStandard.DataInfo( 120, 0, 72),
                  new ThroughputStandard.DataInfo(12, 0, 1.2)),
                new ThroughputStandard(NRServiceName.NSA, 60, 2,
                  new ThroughputStandard.DataInfo( 180, 0, 120),
                  new ThroughputStandard.DataInfo(12, 0, 1.2)),
                new ThroughputStandard(NRServiceName.NSA, 60, 4,
                  new ThroughputStandard.DataInfo( 360, 0, 210),
                  new ThroughputStandard.DataInfo(12, 0, 1.2)),

                new ThroughputStandard(NRServiceName.NSA, 100, 1,
                  new ThroughputStandard.DataInfo( 200, 0, 120),
                  new ThroughputStandard.DataInfo(20, 0, 2)),
                new ThroughputStandard(NRServiceName.NSA, 100, 2,
                  new ThroughputStandard.DataInfo( 300, 0, 200),
                  new ThroughputStandard.DataInfo(20, 0, 2)),
                new ThroughputStandard(NRServiceName.NSA, 100, 4,
                  new ThroughputStandard.DataInfo( 600, 0, 350),
                  new ThroughputStandard.DataInfo(20, 0, 2)),
            };
        }
    }
}
