﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTWirelessNetTest
{
    /// <summary>
    /// 统计类型
    /// 目前分为4种级别进行统计
    /// </summary>
    public enum WirelessNetTestStatType
    {
        地市级,
        区县级,
        场景级,
        子场景级
    }

    /// <summary>
    /// 项目类型
    /// 各个场景使用什么项目的文件
    /// </summary>
    public enum WirelessNetTestProjectType
    {
        UNKNOWN,
        城区,
        区县,
        高铁,
        地铁,
        高速,
        机场
    }

    /// <summary>
    /// 网络类型
    /// </summary>
    public enum NetType
    {
        UNKNOWN,
        //GSM,
        //CDMA,
        //WCDMA,
        LTE,
        NR
    }

    /// <summary>
    /// 公式类型
    /// 部分公式只用数据业务文件的指标出
    /// 部分公式只用语音业务文件的指标出
    /// </summary>
    public enum FormulaType
    {
        全部,
        数据,
        语音
    }

    /// <summary>
    /// 运营商类型
    /// 因为本功能只区分了移动/电信,故结果如果包含联通就合并到电信中
    /// </summary>
    public enum CarrierStatType
    { 
        移动 = 1,
        电信
    }
}
