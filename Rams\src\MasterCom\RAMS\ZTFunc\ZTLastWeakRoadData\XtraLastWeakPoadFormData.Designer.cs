﻿namespace MasterCom.RAMS.ZTFunc.ZTLastWeakRoadData
{
    partial class XtraLastWeakPoadFormData
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraCharts.XYDiagram xyDiagram1 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series1 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel1 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.Series series2 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel2 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel3 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.tableLayoutPanel2 = new System.Windows.Forms.TableLayoutPanel();
            this.chartControl1 = new DevExpress.XtraCharts.ChartControl();
            this.gridControl5 = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemDIYReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemClearFly = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemSHowFly = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemToExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemLable = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView9 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn129 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn130 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn131 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn135 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn132 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn136 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn133 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn134 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.bandedGridColumn22 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn23 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn24 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn25 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn26 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn27 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn28 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn29 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn30 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn31 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn32 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn5 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn6 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn7 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn8 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn9 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn10 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn11 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn12 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn13 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn14 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn15 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn16 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn17 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn18 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn19 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn20 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn21 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn39 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn44 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn45 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn47 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn48 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn49 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn50 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn51 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn109 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn110 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn111 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn112 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn113 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn195 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.xtraTabPage3 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControl2 = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn46 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn52 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn53 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn54 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn55 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn56 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn57 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn196 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView4 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.xtraTabPage4 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControl3 = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridView5 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn58 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn59 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn60 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn61 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn62 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn63 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn64 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn65 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn66 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn67 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn68 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn69 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn70 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn71 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn72 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn73 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn74 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn75 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn76 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn77 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn78 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn79 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn80 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn81 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn82 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn83 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn84 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn85 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn86 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn87 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn88 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn197 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView6 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.xtraTabPage5 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControl4 = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridView7 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn89 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn90 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn91 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn92 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn93 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn94 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn95 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn96 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn97 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn98 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn99 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn100 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn101 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn102 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn103 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn104 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn105 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn106 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn107 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn108 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn114 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn115 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn116 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn117 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn118 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn119 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn120 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn121 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn122 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn123 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn124 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn198 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView8 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.xtraTabPage6 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControl6 = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView5 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridView10 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn125 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn126 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn127 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn128 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn137 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn138 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn139 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn140 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn141 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn142 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn143 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn144 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn145 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn146 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn147 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn148 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn149 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn150 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn151 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn152 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn153 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn154 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn155 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn156 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn157 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn158 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn159 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn160 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn161 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn162 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn163 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn199 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView11 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.xtraTabPage7 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControl7 = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView6 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridView12 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn164 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn165 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn166 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn167 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn168 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn169 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn170 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn171 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn172 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn173 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn174 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn175 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn176 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn177 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn178 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn179 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn180 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn181 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn182 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn183 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn184 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn185 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn186 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn187 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn188 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn189 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn190 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn191 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn192 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn193 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn194 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn200 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView13 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.btnClearFly = new System.Windows.Forms.Button();
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.panel1 = new System.Windows.Forms.Panel();
            this.label1 = new System.Windows.Forms.Label();
            this.btnNext1 = new System.Windows.Forms.Button();
            this.gridBand12 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand1 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand7 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand8 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand9 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand10 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand11 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn33 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn34 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn35 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn36 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn37 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn38 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn39 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn40 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn41 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn42 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn43 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn44 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn45 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn46 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn47 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn48 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn49 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn50 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn51 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn52 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn53 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn54 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn55 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn56 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn57 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn58 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn59 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn60 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn61 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn62 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn63 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn64 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand2 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand13 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand14 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand15 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand16 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand17 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand18 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn65 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn66 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn67 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn68 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn69 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn70 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn71 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn72 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn73 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn74 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn75 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn76 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn77 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn78 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn79 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn80 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn81 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn82 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn83 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn84 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn85 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn86 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn87 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn88 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn89 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn90 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn91 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn92 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn93 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn94 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn95 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn96 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand3 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand19 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand20 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand21 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand22 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand23 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand24 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn97 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn98 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn99 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn100 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn101 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn102 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn103 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn104 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn105 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn106 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn107 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn108 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn109 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn110 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn111 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn112 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn113 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn114 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn115 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn116 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn117 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn118 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn119 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn120 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn121 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn122 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn123 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn124 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn125 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn126 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn127 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn128 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand4 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand25 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand26 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand27 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand28 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand29 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand30 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn129 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn130 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn131 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn132 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn133 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn134 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn135 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn136 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn137 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn138 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn139 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn140 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn141 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn142 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn143 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn144 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn145 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn146 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn147 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn148 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn149 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn150 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn151 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn152 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn153 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn154 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn155 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn156 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn157 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn158 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn159 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn160 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand5 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand31 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand32 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand33 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand34 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand35 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand36 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn161 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn162 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn163 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn164 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn165 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn166 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn167 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn168 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn169 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn170 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn171 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn172 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn173 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn174 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn175 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn176 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn177 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn178 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn179 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn180 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn181 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn182 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn183 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn184 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn185 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn186 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn187 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn188 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn189 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn190 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn191 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn192 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand6 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand37 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand38 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand39 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand40 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand41 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand42 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            this.tableLayoutPanel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl5)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView9)).BeginInit();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            this.xtraTabPage3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).BeginInit();
            this.xtraTabPage4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).BeginInit();
            this.xtraTabPage5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView8)).BeginInit();
            this.xtraTabPage6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView11)).BeginInit();
            this.xtraTabPage7.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView13)).BeginInit();
            this.tableLayoutPanel1.SuspendLayout();
            this.panel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(3, 3);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(843, 411);
            this.xtraTabControl1.TabIndex = 0;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2,
            this.xtraTabPage3,
            this.xtraTabPage4,
            this.xtraTabPage5,
            this.xtraTabPage6,
            this.xtraTabPage7});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.tableLayoutPanel2);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(836, 381);
            this.xtraTabPage1.Text = "概况";
            // 
            // tableLayoutPanel2
            // 
            this.tableLayoutPanel2.ColumnCount = 1;
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel2.Controls.Add(this.chartControl1, 0, 1);
            this.tableLayoutPanel2.Controls.Add(this.gridControl5, 0, 0);
            this.tableLayoutPanel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel2.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel2.Name = "tableLayoutPanel2";
            this.tableLayoutPanel2.RowCount = 2;
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 150F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel2.Size = new System.Drawing.Size(836, 381);
            this.tableLayoutPanel2.TabIndex = 2;
            // 
            // chartControl1
            // 
            xyDiagram1.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram1.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControl1.Diagram = xyDiagram1;
            this.chartControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControl1.Location = new System.Drawing.Point(3, 153);
            this.chartControl1.Name = "chartControl1";
            sideBySideBarSeriesLabel1.LineVisible = true;
            series1.Label = sideBySideBarSeriesLabel1;
            series1.Name = "Series 1";
            sideBySideBarSeriesLabel2.LineVisible = true;
            series2.Label = sideBySideBarSeriesLabel2;
            series2.Name = "Series 2";
            this.chartControl1.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series1,
        series2};
            sideBySideBarSeriesLabel3.LineVisible = true;
            this.chartControl1.SeriesTemplate.Label = sideBySideBarSeriesLabel3;
            this.chartControl1.Size = new System.Drawing.Size(830, 225);
            this.chartControl1.TabIndex = 0;
            // 
            // gridControl5
            // 
            this.gridControl5.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl5.Location = new System.Drawing.Point(3, 3);
            this.gridControl5.MainView = this.gridView9;
            this.gridControl5.Name = "gridControl5";
            this.gridControl5.Size = new System.Drawing.Size(830, 144);
            this.gridControl5.TabIndex = 0;
            this.gridControl5.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView9});
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemDIYReplay,
            this.ToolStripMenuItemClearFly,
            this.ToolStripMenuItemSHowFly,
            this.ToolStripMenuItemToExcel,
            this.ToolStripMenuItemLable});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(135, 114);
            // 
            // ToolStripMenuItemDIYReplay
            // 
            this.ToolStripMenuItemDIYReplay.Name = "ToolStripMenuItemDIYReplay";
            this.ToolStripMenuItemDIYReplay.Size = new System.Drawing.Size(134, 22);
            this.ToolStripMenuItemDIYReplay.Text = "回放";
            this.ToolStripMenuItemDIYReplay.Click += new System.EventHandler(this.ToolStripMenuItemDIYReplay_Click);
            // 
            // ToolStripMenuItemClearFly
            // 
            this.ToolStripMenuItemClearFly.Name = "ToolStripMenuItemClearFly";
            this.ToolStripMenuItemClearFly.Size = new System.Drawing.Size(134, 22);
            this.ToolStripMenuItemClearFly.Text = "清除飞线";
            this.ToolStripMenuItemClearFly.Click += new System.EventHandler(this.ToolStripMenuItemClearFly_Click);
            // 
            // ToolStripMenuItemSHowFly
            // 
            this.ToolStripMenuItemSHowFly.Name = "ToolStripMenuItemSHowFly";
            this.ToolStripMenuItemSHowFly.Size = new System.Drawing.Size(134, 22);
            this.ToolStripMenuItemSHowFly.Text = "显示飞线";
            this.ToolStripMenuItemSHowFly.Click += new System.EventHandler(this.ToolStripMenuItemSHowFly_Click);
            // 
            // ToolStripMenuItemToExcel
            // 
            this.ToolStripMenuItemToExcel.Name = "ToolStripMenuItemToExcel";
            this.ToolStripMenuItemToExcel.Size = new System.Drawing.Size(134, 22);
            this.ToolStripMenuItemToExcel.Text = "导出EXCEL";
            this.ToolStripMenuItemToExcel.Click += new System.EventHandler(this.ToolStripMenuItemToExcel_Click);
            // 
            // ToolStripMenuItemLable
            // 
            this.ToolStripMenuItemLable.Name = "ToolStripMenuItemLable";
            this.ToolStripMenuItemLable.Size = new System.Drawing.Size(134, 22);
            this.ToolStripMenuItemLable.Text = "显示标签";
            this.ToolStripMenuItemLable.Click += new System.EventHandler(this.ToolStripMenuItemLable_Click);
            // 
            // gridView9
            // 
            this.gridView9.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn129,
            this.gridColumn130,
            this.gridColumn131,
            this.gridColumn135,
            this.gridColumn132,
            this.gridColumn136,
            this.gridColumn133,
            this.gridColumn134});
            this.gridView9.GridControl = this.gridControl5;
            this.gridView9.Name = "gridView9";
            this.gridView9.OptionsBehavior.Editable = false;
            this.gridView9.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView9.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gridView9.OptionsView.ShowGroupPanel = false;
            this.gridView9.EndSorting += new System.EventHandler(this.gridView9_EndSorting);
            this.gridView9.RowCellClick += new DevExpress.XtraGrid.Views.Grid.RowCellClickEventHandler(this.gridView9_RowCellClick);
            // 
            // gridColumn129
            // 
            this.gridColumn129.Caption = "类型";
            this.gridColumn129.FieldName = "StrType";
            this.gridColumn129.Name = "gridColumn129";
            this.gridColumn129.Visible = true;
            this.gridColumn129.VisibleIndex = 0;
            this.gridColumn129.Width = 62;
            // 
            // gridColumn130
            // 
            this.gridColumn130.Caption = "数量";
            this.gridColumn130.FieldName = "INum";
            this.gridColumn130.Name = "gridColumn130";
            this.gridColumn130.Visible = true;
            this.gridColumn130.VisibleIndex = 1;
            this.gridColumn130.Width = 44;
            // 
            // gridColumn131
            // 
            this.gridColumn131.Caption = "时间(s)";
            this.gridColumn131.FieldName = "IDuration";
            this.gridColumn131.Name = "gridColumn131";
            this.gridColumn131.Visible = true;
            this.gridColumn131.VisibleIndex = 2;
            this.gridColumn131.Width = 57;
            // 
            // gridColumn135
            // 
            this.gridColumn135.Caption = "总里程(m)";
            this.gridColumn135.FieldName = "IAllDistance";
            this.gridColumn135.Name = "gridColumn135";
            this.gridColumn135.Visible = true;
            this.gridColumn135.VisibleIndex = 3;
            this.gridColumn135.Width = 66;
            // 
            // gridColumn132
            // 
            this.gridColumn132.Caption = "问题里程(m)";
            this.gridColumn132.FieldName = "IDistance";
            this.gridColumn132.Name = "gridColumn132";
            this.gridColumn132.Visible = true;
            this.gridColumn132.VisibleIndex = 4;
            this.gridColumn132.Width = 60;
            // 
            // gridColumn136
            // 
            this.gridColumn136.Caption = "差道路占比(%)";
            this.gridColumn136.DisplayFormat.FormatString = "00.00%";
            this.gridColumn136.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn136.FieldName = "FRate";
            this.gridColumn136.GroupFormat.FormatString = "00.00%";
            this.gridColumn136.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn136.Name = "gridColumn136";
            this.gridColumn136.Visible = true;
            this.gridColumn136.VisibleIndex = 5;
            this.gridColumn136.Width = 97;
            // 
            // gridColumn133
            // 
            this.gridColumn133.Caption = "小区数量";
            this.gridColumn133.FieldName = "ICellNum";
            this.gridColumn133.Name = "gridColumn133";
            this.gridColumn133.Visible = true;
            this.gridColumn133.VisibleIndex = 6;
            this.gridColumn133.Width = 59;
            // 
            // gridColumn134
            // 
            this.gridColumn134.Caption = "采样点";
            this.gridColumn134.FieldName = "ISampleId";
            this.gridColumn134.Name = "gridColumn134";
            this.gridColumn134.Visible = true;
            this.gridColumn134.VisibleIndex = 7;
            this.gridColumn134.Width = 60;
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.gridControl1);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(836, 381);
            this.xtraTabPage2.Text = "下载速率低";
            // 
            // gridControl1
            // 
            this.gridControl1.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl1.Location = new System.Drawing.Point(0, 0);
            this.gridControl1.MainView = this.bandedGridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.Size = new System.Drawing.Size(836, 381);
            this.gridControl1.TabIndex = 0;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView1,
            this.gridView1,
            this.gridView2});
            this.gridControl1.DoubleClick += new System.EventHandler(this.gridControl1_DoubleClick);
            // 
            // bandedGridView1
            // 
            this.bandedGridView1.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand12,
            this.gridBand1,
            this.gridBand7,
            this.gridBand8,
            this.gridBand9,
            this.gridBand10,
            this.gridBand11});
            this.bandedGridView1.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn1,
            this.bandedGridColumn2,
            this.bandedGridColumn3,
            this.bandedGridColumn4,
            this.bandedGridColumn5,
            this.bandedGridColumn6,
            this.bandedGridColumn7,
            this.bandedGridColumn8,
            this.bandedGridColumn9,
            this.bandedGridColumn10,
            this.bandedGridColumn11,
            this.bandedGridColumn12,
            this.bandedGridColumn13,
            this.bandedGridColumn14,
            this.bandedGridColumn15,
            this.bandedGridColumn16,
            this.bandedGridColumn17,
            this.bandedGridColumn18,
            this.bandedGridColumn19,
            this.bandedGridColumn20,
            this.bandedGridColumn21,
            this.bandedGridColumn22,
            this.bandedGridColumn23,
            this.bandedGridColumn24,
            this.bandedGridColumn25,
            this.bandedGridColumn26,
            this.bandedGridColumn27,
            this.bandedGridColumn28,
            this.bandedGridColumn29,
            this.bandedGridColumn30,
            this.bandedGridColumn31,
            this.bandedGridColumn32});
            this.bandedGridView1.GridControl = this.gridControl1;
            this.bandedGridView1.Name = "bandedGridView1";
            this.bandedGridView1.OptionsBehavior.Editable = false;
            this.bandedGridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.bandedGridView1.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView1.OptionsView.ShowGroupPanel = false;
            // 
            // bandedGridColumn22
            // 
            this.bandedGridColumn22.Caption = "TOP1小区";
            this.bandedGridColumn22.CustomizationCaption = "TIO1小区";
            this.bandedGridColumn22.FieldName = "Cell1";
            this.bandedGridColumn22.Name = "bandedGridColumn22";
            this.bandedGridColumn22.Visible = true;
            this.bandedGridColumn22.Width = 85;
            // 
            // bandedGridColumn23
            // 
            this.bandedGridColumn23.Caption = "TOP2小区";
            this.bandedGridColumn23.CustomizationCaption = "TOP2小区";
            this.bandedGridColumn23.FieldName = "Cell2";
            this.bandedGridColumn23.Name = "bandedGridColumn23";
            this.bandedGridColumn23.Visible = true;
            this.bandedGridColumn23.Width = 85;
            // 
            // bandedGridColumn24
            // 
            this.bandedGridColumn24.Caption = "TOP3小区";
            this.bandedGridColumn24.CustomizationCaption = "TOP3小区";
            this.bandedGridColumn24.FieldName = "Cell3";
            this.bandedGridColumn24.Name = "bandedGridColumn24";
            this.bandedGridColumn24.Visible = true;
            this.bandedGridColumn24.Width = 85;
            // 
            // bandedGridColumn25
            // 
            this.bandedGridColumn25.Caption = "所属网格";
            this.bandedGridColumn25.CustomizationCaption = "所属网格";
            this.bandedGridColumn25.FieldName = "Strgrid";
            this.bandedGridColumn25.Name = "bandedGridColumn25";
            this.bandedGridColumn25.Visible = true;
            // 
            // bandedGridColumn26
            // 
            this.bandedGridColumn26.Caption = "所属道路";
            this.bandedGridColumn26.CustomizationCaption = "所属道路";
            this.bandedGridColumn26.FieldName = "Strroad";
            this.bandedGridColumn26.Name = "bandedGridColumn26";
            this.bandedGridColumn26.Visible = true;
            // 
            // bandedGridColumn27
            // 
            this.bandedGridColumn27.Caption = "经度";
            this.bandedGridColumn27.CustomizationCaption = "经度";
            this.bandedGridColumn27.FieldName = "Imlongitude";
            this.bandedGridColumn27.Name = "bandedGridColumn27";
            this.bandedGridColumn27.Visible = true;
            // 
            // bandedGridColumn28
            // 
            this.bandedGridColumn28.Caption = "纬度";
            this.bandedGridColumn28.CustomizationCaption = "纬度";
            this.bandedGridColumn28.FieldName = "Imlatitude";
            this.bandedGridColumn28.Name = "bandedGridColumn28";
            this.bandedGridColumn28.Visible = true;
            // 
            // bandedGridColumn1
            // 
            this.bandedGridColumn1.Caption = "距离(米)";
            this.bandedGridColumn1.CustomizationCaption = "距离(米)";
            this.bandedGridColumn1.FieldName = "Idistance";
            this.bandedGridColumn1.Name = "bandedGridColumn1";
            this.bandedGridColumn1.Visible = true;
            // 
            // bandedGridColumn2
            // 
            this.bandedGridColumn2.Caption = "时长(秒)";
            this.bandedGridColumn2.CustomizationCaption = "时长(秒)";
            this.bandedGridColumn2.FieldName = "Iduration";
            this.bandedGridColumn2.Name = "bandedGridColumn2";
            this.bandedGridColumn2.Visible = true;
            // 
            // bandedGridColumn29
            // 
            this.bandedGridColumn29.Caption = "ifileid";
            this.bandedGridColumn29.FieldName = "Ifileid";
            this.bandedGridColumn29.Name = "bandedGridColumn29";
            // 
            // bandedGridColumn30
            // 
            this.bandedGridColumn30.Caption = "istime";
            this.bandedGridColumn30.FieldName = "Istime";
            this.bandedGridColumn30.Name = "bandedGridColumn30";
            // 
            // bandedGridColumn31
            // 
            this.bandedGridColumn31.Caption = "ietime";
            this.bandedGridColumn31.FieldName = "Ietime";
            this.bandedGridColumn31.Name = "bandedGridColumn31";
            // 
            // bandedGridColumn32
            // 
            this.bandedGridColumn32.Caption = "gridColumn195";
            this.bandedGridColumn32.FieldName = "Iid";
            this.bandedGridColumn32.Name = "bandedGridColumn32";
            // 
            // bandedGridColumn3
            // 
            this.bandedGridColumn3.Caption = "0";
            this.bandedGridColumn3.CustomizationCaption = "速率0";
            this.bandedGridColumn3.FieldName = "APP_Speed0";
            this.bandedGridColumn3.Name = "bandedGridColumn3";
            this.bandedGridColumn3.Visible = true;
            // 
            // bandedGridColumn4
            // 
            this.bandedGridColumn4.Caption = "[0,40]";
            this.bandedGridColumn4.CustomizationCaption = "速率[0,40]";
            this.bandedGridColumn4.FieldName = "APP_Speed0_40";
            this.bandedGridColumn4.Name = "bandedGridColumn4";
            this.bandedGridColumn4.Visible = true;
            // 
            // bandedGridColumn5
            // 
            this.bandedGridColumn5.Caption = "[40,70]";
            this.bandedGridColumn5.CustomizationCaption = "速率[40,70]";
            this.bandedGridColumn5.FieldName = "APP_Speed40_70";
            this.bandedGridColumn5.Name = "bandedGridColumn5";
            this.bandedGridColumn5.Visible = true;
            // 
            // bandedGridColumn6
            // 
            this.bandedGridColumn6.Caption = "[70,90]";
            this.bandedGridColumn6.CustomizationCaption = "速率[70,90]";
            this.bandedGridColumn6.FieldName = "APP_Speed70_90";
            this.bandedGridColumn6.Name = "bandedGridColumn6";
            this.bandedGridColumn6.Visible = true;
            // 
            // bandedGridColumn7
            // 
            this.bandedGridColumn7.Caption = "[90,110]";
            this.bandedGridColumn7.CustomizationCaption = "速率[90,110]";
            this.bandedGridColumn7.FieldName = "APP_Speed90_110";
            this.bandedGridColumn7.Name = "bandedGridColumn7";
            this.bandedGridColumn7.Visible = true;
            // 
            // bandedGridColumn8
            // 
            this.bandedGridColumn8.Caption = "110";
            this.bandedGridColumn8.CustomizationCaption = "速率110";
            this.bandedGridColumn8.FieldName = "APP_Speed110";
            this.bandedGridColumn8.Name = "bandedGridColumn8";
            this.bandedGridColumn8.Visible = true;
            // 
            // bandedGridColumn9
            // 
            this.bandedGridColumn9.Caption = "[0,20]";
            this.bandedGridColumn9.CustomizationCaption = "误码[0,20]";
            this.bandedGridColumn9.FieldName = "BLER0_20";
            this.bandedGridColumn9.Name = "bandedGridColumn9";
            this.bandedGridColumn9.Visible = true;
            // 
            // bandedGridColumn10
            // 
            this.bandedGridColumn10.Caption = "[20,50]";
            this.bandedGridColumn10.CustomizationCaption = "误码[20,50]";
            this.bandedGridColumn10.FieldName = "BLER20_50";
            this.bandedGridColumn10.Name = "bandedGridColumn10";
            this.bandedGridColumn10.Visible = true;
            // 
            // bandedGridColumn11
            // 
            this.bandedGridColumn11.Caption = "[50,100]";
            this.bandedGridColumn11.CustomizationCaption = "误码[50,100]";
            this.bandedGridColumn11.FieldName = "BLER50_100";
            this.bandedGridColumn11.Name = "bandedGridColumn11";
            this.bandedGridColumn11.Visible = true;
            // 
            // bandedGridColumn12
            // 
            this.bandedGridColumn12.Caption = "[1,10]";
            this.bandedGridColumn12.CustomizationCaption = "信号质量[1,10]";
            this.bandedGridColumn12.FieldName = "BEP_Mean1_10";
            this.bandedGridColumn12.Name = "bandedGridColumn12";
            this.bandedGridColumn12.Visible = true;
            // 
            // bandedGridColumn13
            // 
            this.bandedGridColumn13.Caption = "[11,17]";
            this.bandedGridColumn13.CustomizationCaption = "信号质量[11,17]";
            this.bandedGridColumn13.FieldName = "BEP_Mean11_17";
            this.bandedGridColumn13.Name = "bandedGridColumn13";
            this.bandedGridColumn13.Visible = true;
            // 
            // bandedGridColumn14
            // 
            this.bandedGridColumn14.Caption = "[18,27]";
            this.bandedGridColumn14.CustomizationCaption = "信号质量[18,27]";
            this.bandedGridColumn14.FieldName = "BEP_Mean18_27";
            this.bandedGridColumn14.Name = "bandedGridColumn14";
            this.bandedGridColumn14.Visible = true;
            // 
            // bandedGridColumn15
            // 
            this.bandedGridColumn15.Caption = "[28,31]";
            this.bandedGridColumn15.CustomizationCaption = "信号质量[28,31]";
            this.bandedGridColumn15.FieldName = "BEP_Mean28_31";
            this.bandedGridColumn15.Name = "bandedGridColumn15";
            this.bandedGridColumn15.Visible = true;
            // 
            // bandedGridColumn16
            // 
            this.bandedGridColumn16.Caption = "打开状态";
            this.bandedGridColumn16.CustomizationCaption = "TBF打开状态";
            this.bandedGridColumn16.FieldName = "TBF_OPEN";
            this.bandedGridColumn16.Name = "bandedGridColumn16";
            this.bandedGridColumn16.Visible = true;
            // 
            // bandedGridColumn17
            // 
            this.bandedGridColumn17.Caption = "关闭状态";
            this.bandedGridColumn17.CustomizationCaption = "TBF关闭状态";
            this.bandedGridColumn17.FieldName = "TBF_CLOSE";
            this.bandedGridColumn17.Name = "bandedGridColumn17";
            this.bandedGridColumn17.Visible = true;
            // 
            // bandedGridColumn18
            // 
            this.bandedGridColumn18.Caption = "时隙1";
            this.bandedGridColumn18.CustomizationCaption = "时隙1";
            this.bandedGridColumn18.FieldName = "TimeSlot1";
            this.bandedGridColumn18.Name = "bandedGridColumn18";
            this.bandedGridColumn18.Visible = true;
            // 
            // bandedGridColumn19
            // 
            this.bandedGridColumn19.Caption = "时隙2";
            this.bandedGridColumn19.CustomizationCaption = "时隙2";
            this.bandedGridColumn19.FieldName = "TimeSlot2";
            this.bandedGridColumn19.Name = "bandedGridColumn19";
            this.bandedGridColumn19.Visible = true;
            // 
            // bandedGridColumn20
            // 
            this.bandedGridColumn20.Caption = "时隙3";
            this.bandedGridColumn20.CustomizationCaption = "时隙3";
            this.bandedGridColumn20.FieldName = "TimeSlot3";
            this.bandedGridColumn20.Name = "bandedGridColumn20";
            this.bandedGridColumn20.Visible = true;
            // 
            // bandedGridColumn21
            // 
            this.bandedGridColumn21.Caption = "时隙4";
            this.bandedGridColumn21.CustomizationCaption = "时隙4";
            this.bandedGridColumn21.FieldName = "TimeSlot4";
            this.bandedGridColumn21.Name = "bandedGridColumn21";
            this.bandedGridColumn21.Visible = true;
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn33,
            this.gridColumn34,
            this.gridColumn35,
            this.gridColumn36,
            this.gridColumn37,
            this.gridColumn38,
            this.gridColumn39,
            this.gridColumn40,
            this.gridColumn41,
            this.gridColumn42,
            this.gridColumn43,
            this.gridColumn44,
            this.gridColumn45,
            this.gridColumn47,
            this.gridColumn48,
            this.gridColumn49,
            this.gridColumn50,
            this.gridColumn51,
            this.gridColumn109,
            this.gridColumn110,
            this.gridColumn111,
            this.gridColumn112,
            this.gridColumn113,
            this.gridColumn195});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView1.OptionsView.ColumnAutoWidth = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "距离(米)";
            this.gridColumn1.FieldName = "Idistance";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "时长(秒)";
            this.gridColumn2.FieldName = "Iduration";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "速率0";
            this.gridColumn3.FieldName = "APP_Speed0";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "速率[0,40]";
            this.gridColumn4.FieldName = "APP_Speed0_40";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "速率[40,70]";
            this.gridColumn5.FieldName = "APP_Speed40_70";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 4;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "速率[70,90]";
            this.gridColumn6.FieldName = "APP_Speed70_90";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 5;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "速率[90,110]";
            this.gridColumn7.FieldName = "APP_Speed90_110";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 6;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "速率110";
            this.gridColumn8.FieldName = "APP_Speed110";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 7;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "误码[0,20]";
            this.gridColumn33.FieldName = "BLER0_20";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 8;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "误码[20,50]";
            this.gridColumn34.FieldName = "BLER20_50";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 9;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "误码[50,100]";
            this.gridColumn35.FieldName = "BLER50_100";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 10;
            // 
            // gridColumn36
            // 
            this.gridColumn36.Caption = "信号质量[1,10]";
            this.gridColumn36.FieldName = "BEP_Mean1_10";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.Visible = true;
            this.gridColumn36.VisibleIndex = 11;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "信号质量[11,17]";
            this.gridColumn37.FieldName = "BEP_Mean11_17";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.Visible = true;
            this.gridColumn37.VisibleIndex = 12;
            // 
            // gridColumn38
            // 
            this.gridColumn38.Caption = "信号质量[18,27]";
            this.gridColumn38.FieldName = "BEP_Mean18_27";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.Visible = true;
            this.gridColumn38.VisibleIndex = 13;
            // 
            // gridColumn39
            // 
            this.gridColumn39.Caption = "信号质量[28,31]";
            this.gridColumn39.FieldName = "BEP_Mean28_31";
            this.gridColumn39.Name = "gridColumn39";
            this.gridColumn39.Visible = true;
            this.gridColumn39.VisibleIndex = 14;
            // 
            // gridColumn40
            // 
            this.gridColumn40.Caption = "TBF打开状态";
            this.gridColumn40.FieldName = "TBF_OPEN";
            this.gridColumn40.Name = "gridColumn40";
            this.gridColumn40.Visible = true;
            this.gridColumn40.VisibleIndex = 15;
            // 
            // gridColumn41
            // 
            this.gridColumn41.Caption = "TBF关闭状态";
            this.gridColumn41.FieldName = "TBF_CLOSE";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.Visible = true;
            this.gridColumn41.VisibleIndex = 16;
            // 
            // gridColumn42
            // 
            this.gridColumn42.Caption = "时隙1";
            this.gridColumn42.FieldName = "TimeSlot1";
            this.gridColumn42.Name = "gridColumn42";
            this.gridColumn42.Visible = true;
            this.gridColumn42.VisibleIndex = 17;
            // 
            // gridColumn43
            // 
            this.gridColumn43.Caption = "时隙2";
            this.gridColumn43.FieldName = "TimeSlot2";
            this.gridColumn43.Name = "gridColumn43";
            this.gridColumn43.Visible = true;
            this.gridColumn43.VisibleIndex = 18;
            // 
            // gridColumn44
            // 
            this.gridColumn44.Caption = "时隙3";
            this.gridColumn44.FieldName = "TimeSlot3";
            this.gridColumn44.Name = "gridColumn44";
            this.gridColumn44.Visible = true;
            this.gridColumn44.VisibleIndex = 19;
            // 
            // gridColumn45
            // 
            this.gridColumn45.Caption = "时隙4";
            this.gridColumn45.FieldName = "TimeSlot4";
            this.gridColumn45.Name = "gridColumn45";
            this.gridColumn45.Visible = true;
            this.gridColumn45.VisibleIndex = 20;
            // 
            // gridColumn47
            // 
            this.gridColumn47.Caption = "小区1";
            this.gridColumn47.FieldName = "Cell1";
            this.gridColumn47.Name = "gridColumn47";
            this.gridColumn47.Visible = true;
            this.gridColumn47.VisibleIndex = 21;
            // 
            // gridColumn48
            // 
            this.gridColumn48.Caption = "小区2";
            this.gridColumn48.FieldName = "Cell2";
            this.gridColumn48.Name = "gridColumn48";
            this.gridColumn48.Visible = true;
            this.gridColumn48.VisibleIndex = 22;
            // 
            // gridColumn49
            // 
            this.gridColumn49.Caption = "小区3";
            this.gridColumn49.FieldName = "Cell3";
            this.gridColumn49.Name = "gridColumn49";
            this.gridColumn49.Visible = true;
            this.gridColumn49.VisibleIndex = 23;
            // 
            // gridColumn50
            // 
            this.gridColumn50.Caption = "所属网格";
            this.gridColumn50.FieldName = "Strgrid";
            this.gridColumn50.Name = "gridColumn50";
            this.gridColumn50.Visible = true;
            this.gridColumn50.VisibleIndex = 24;
            // 
            // gridColumn51
            // 
            this.gridColumn51.Caption = "所属道路";
            this.gridColumn51.FieldName = "Strroad";
            this.gridColumn51.Name = "gridColumn51";
            this.gridColumn51.Visible = true;
            this.gridColumn51.VisibleIndex = 25;
            // 
            // gridColumn109
            // 
            this.gridColumn109.Caption = "经度";
            this.gridColumn109.FieldName = "Imlongitude";
            this.gridColumn109.Name = "gridColumn109";
            // 
            // gridColumn110
            // 
            this.gridColumn110.Caption = "纬度";
            this.gridColumn110.FieldName = "Imlatitude";
            this.gridColumn110.Name = "gridColumn110";
            // 
            // gridColumn111
            // 
            this.gridColumn111.Caption = "ifileid";
            this.gridColumn111.FieldName = "Ifileid";
            this.gridColumn111.Name = "gridColumn111";
            // 
            // gridColumn112
            // 
            this.gridColumn112.Caption = "istime";
            this.gridColumn112.FieldName = "Istime";
            this.gridColumn112.Name = "gridColumn112";
            // 
            // gridColumn113
            // 
            this.gridColumn113.Caption = "ietime";
            this.gridColumn113.FieldName = "Ietime";
            this.gridColumn113.Name = "gridColumn113";
            // 
            // gridColumn195
            // 
            this.gridColumn195.Caption = "gridColumn195";
            this.gridColumn195.FieldName = "Iid";
            this.gridColumn195.Name = "gridColumn195";
            // 
            // gridView2
            // 
            this.gridView2.GridControl = this.gridControl1;
            this.gridView2.Name = "gridView2";
            // 
            // xtraTabPage3
            // 
            this.xtraTabPage3.Controls.Add(this.gridControl2);
            this.xtraTabPage3.Name = "xtraTabPage3";
            this.xtraTabPage3.Size = new System.Drawing.Size(836, 381);
            this.xtraTabPage3.Text = "弱覆盖";
            // 
            // gridControl2
            // 
            this.gridControl2.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl2.Location = new System.Drawing.Point(0, 0);
            this.gridControl2.MainView = this.bandedGridView2;
            this.gridControl2.Name = "gridControl2";
            this.gridControl2.Size = new System.Drawing.Size(836, 381);
            this.gridControl2.TabIndex = 1;
            this.gridControl2.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView2,
            this.gridView3,
            this.gridView4});
            this.gridControl2.DoubleClick += new System.EventHandler(this.gridControl2_DoubleClick);
            // 
            // bandedGridView2
            // 
            this.bandedGridView2.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand2,
            this.gridBand13,
            this.gridBand14,
            this.gridBand15,
            this.gridBand16,
            this.gridBand17,
            this.gridBand18});
            this.bandedGridView2.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn33,
            this.bandedGridColumn34,
            this.bandedGridColumn35,
            this.bandedGridColumn36,
            this.bandedGridColumn37,
            this.bandedGridColumn38,
            this.bandedGridColumn39,
            this.bandedGridColumn40,
            this.bandedGridColumn41,
            this.bandedGridColumn42,
            this.bandedGridColumn43,
            this.bandedGridColumn44,
            this.bandedGridColumn45,
            this.bandedGridColumn46,
            this.bandedGridColumn47,
            this.bandedGridColumn48,
            this.bandedGridColumn49,
            this.bandedGridColumn50,
            this.bandedGridColumn51,
            this.bandedGridColumn52,
            this.bandedGridColumn53,
            this.bandedGridColumn54,
            this.bandedGridColumn55,
            this.bandedGridColumn56,
            this.bandedGridColumn57,
            this.bandedGridColumn58,
            this.bandedGridColumn59,
            this.bandedGridColumn60,
            this.bandedGridColumn61,
            this.bandedGridColumn62,
            this.bandedGridColumn63,
            this.bandedGridColumn64});
            this.bandedGridView2.GridControl = this.gridControl2;
            this.bandedGridView2.Name = "bandedGridView2";
            this.bandedGridView2.OptionsBehavior.Editable = false;
            this.bandedGridView2.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.bandedGridView2.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView2.OptionsView.ShowGroupPanel = false;
            // 
            // gridView3
            // 
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn24,
            this.gridColumn25,
            this.gridColumn26,
            this.gridColumn27,
            this.gridColumn28,
            this.gridColumn29,
            this.gridColumn30,
            this.gridColumn31,
            this.gridColumn32,
            this.gridColumn46,
            this.gridColumn52,
            this.gridColumn53,
            this.gridColumn54,
            this.gridColumn55,
            this.gridColumn56,
            this.gridColumn57,
            this.gridColumn196});
            this.gridView3.GridControl = this.gridControl2;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsBehavior.Editable = false;
            this.gridView3.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView3.OptionsView.ColumnAutoWidth = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            this.gridView3.DoubleClick += new System.EventHandler(this.gridControl2_DoubleClick);
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "距离(米)";
            this.gridColumn9.FieldName = "Idistance";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 0;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "时长(秒)";
            this.gridColumn10.FieldName = "Iduration";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 1;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "速率0";
            this.gridColumn11.FieldName = "APP_Speed0";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 2;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "速率[0,40]";
            this.gridColumn12.FieldName = "APP_Speed0_40";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 3;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "速率[40,70]";
            this.gridColumn13.FieldName = "APP_Speed40_70";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 4;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "速率[70,90]";
            this.gridColumn14.FieldName = "APP_Speed70_90";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 5;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "速率[90,110]";
            this.gridColumn15.FieldName = "APP_Speed90_110";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 6;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "速率110";
            this.gridColumn16.FieldName = "APP_Speed110";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 7;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "误码[0,20]";
            this.gridColumn17.FieldName = "BLER0_20";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 8;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "误码[20,50]";
            this.gridColumn18.FieldName = "BLER20_50";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 9;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "误码[50,100]";
            this.gridColumn19.FieldName = "BLER50_100";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 10;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "信号质量[1,10]";
            this.gridColumn20.FieldName = "BEP_Mean1_10";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 11;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "信号质量[11,17]";
            this.gridColumn21.FieldName = "BEP_Mean11_17";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 12;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "信号质量[18,27]";
            this.gridColumn22.FieldName = "BEP_Mean18_27";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 13;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "信号质量[28,31]";
            this.gridColumn23.FieldName = "BEP_Mean28_31";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 14;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "TBF打开状态";
            this.gridColumn24.FieldName = "TBF_OPEN";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 15;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "TBF关闭状态";
            this.gridColumn25.FieldName = "TBF_CLOSE";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 16;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "时隙1";
            this.gridColumn26.FieldName = "TimeSlot1";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 17;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "时隙2";
            this.gridColumn27.FieldName = "TimeSlot2";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 18;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "时隙3";
            this.gridColumn28.FieldName = "TimeSlot3";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 19;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "时隙4";
            this.gridColumn29.FieldName = "TimeSlot4";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 20;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "小区1";
            this.gridColumn30.FieldName = "Cell1";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 21;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "小区2";
            this.gridColumn31.FieldName = "Cell2";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 22;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "小区3";
            this.gridColumn32.FieldName = "Cell3";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 23;
            // 
            // gridColumn46
            // 
            this.gridColumn46.Caption = "所属网格";
            this.gridColumn46.FieldName = "Strgrid";
            this.gridColumn46.Name = "gridColumn46";
            this.gridColumn46.Visible = true;
            this.gridColumn46.VisibleIndex = 24;
            // 
            // gridColumn52
            // 
            this.gridColumn52.Caption = "所属道路";
            this.gridColumn52.FieldName = "Strroad";
            this.gridColumn52.Name = "gridColumn52";
            this.gridColumn52.Visible = true;
            this.gridColumn52.VisibleIndex = 25;
            // 
            // gridColumn53
            // 
            this.gridColumn53.Caption = "经度";
            this.gridColumn53.FieldName = "Imlongitude";
            this.gridColumn53.Name = "gridColumn53";
            // 
            // gridColumn54
            // 
            this.gridColumn54.Caption = "纬度";
            this.gridColumn54.FieldName = "Imlatitude";
            this.gridColumn54.Name = "gridColumn54";
            // 
            // gridColumn55
            // 
            this.gridColumn55.Caption = "ifileid";
            this.gridColumn55.FieldName = "Ifileid";
            this.gridColumn55.Name = "gridColumn55";
            // 
            // gridColumn56
            // 
            this.gridColumn56.Caption = "istime";
            this.gridColumn56.FieldName = "Istime";
            this.gridColumn56.Name = "gridColumn56";
            // 
            // gridColumn57
            // 
            this.gridColumn57.Caption = "ietime";
            this.gridColumn57.FieldName = "Ietime";
            this.gridColumn57.Name = "gridColumn57";
            // 
            // gridColumn196
            // 
            this.gridColumn196.Caption = "gridColumn196";
            this.gridColumn196.FieldName = "Iid";
            this.gridColumn196.Name = "gridColumn196";
            // 
            // gridView4
            // 
            this.gridView4.GridControl = this.gridControl2;
            this.gridView4.Name = "gridView4";
            // 
            // xtraTabPage4
            // 
            this.xtraTabPage4.Controls.Add(this.gridControl3);
            this.xtraTabPage4.Name = "xtraTabPage4";
            this.xtraTabPage4.Size = new System.Drawing.Size(836, 381);
            this.xtraTabPage4.Text = "无线环境差";
            // 
            // gridControl3
            // 
            this.gridControl3.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl3.Location = new System.Drawing.Point(0, 0);
            this.gridControl3.MainView = this.bandedGridView3;
            this.gridControl3.Name = "gridControl3";
            this.gridControl3.Size = new System.Drawing.Size(836, 381);
            this.gridControl3.TabIndex = 1;
            this.gridControl3.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView3,
            this.gridView5,
            this.gridView6});
            this.gridControl3.DoubleClick += new System.EventHandler(this.gridControl3_DoubleClick);
            // 
            // bandedGridView3
            // 
            this.bandedGridView3.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand3,
            this.gridBand19,
            this.gridBand20,
            this.gridBand21,
            this.gridBand22,
            this.gridBand23,
            this.gridBand24});
            this.bandedGridView3.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn65,
            this.bandedGridColumn66,
            this.bandedGridColumn67,
            this.bandedGridColumn68,
            this.bandedGridColumn69,
            this.bandedGridColumn70,
            this.bandedGridColumn71,
            this.bandedGridColumn72,
            this.bandedGridColumn73,
            this.bandedGridColumn74,
            this.bandedGridColumn75,
            this.bandedGridColumn76,
            this.bandedGridColumn77,
            this.bandedGridColumn78,
            this.bandedGridColumn79,
            this.bandedGridColumn80,
            this.bandedGridColumn81,
            this.bandedGridColumn82,
            this.bandedGridColumn83,
            this.bandedGridColumn84,
            this.bandedGridColumn85,
            this.bandedGridColumn86,
            this.bandedGridColumn87,
            this.bandedGridColumn88,
            this.bandedGridColumn89,
            this.bandedGridColumn90,
            this.bandedGridColumn91,
            this.bandedGridColumn92,
            this.bandedGridColumn93,
            this.bandedGridColumn94,
            this.bandedGridColumn95,
            this.bandedGridColumn96});
            this.bandedGridView3.GridControl = this.gridControl3;
            this.bandedGridView3.Name = "bandedGridView3";
            this.bandedGridView3.OptionsBehavior.Editable = false;
            this.bandedGridView3.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.bandedGridView3.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView3.OptionsView.ShowGroupPanel = false;
            // 
            // gridView5
            // 
            this.gridView5.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn58,
            this.gridColumn59,
            this.gridColumn60,
            this.gridColumn61,
            this.gridColumn62,
            this.gridColumn63,
            this.gridColumn64,
            this.gridColumn65,
            this.gridColumn66,
            this.gridColumn67,
            this.gridColumn68,
            this.gridColumn69,
            this.gridColumn70,
            this.gridColumn71,
            this.gridColumn72,
            this.gridColumn73,
            this.gridColumn74,
            this.gridColumn75,
            this.gridColumn76,
            this.gridColumn77,
            this.gridColumn78,
            this.gridColumn79,
            this.gridColumn80,
            this.gridColumn81,
            this.gridColumn82,
            this.gridColumn83,
            this.gridColumn84,
            this.gridColumn85,
            this.gridColumn86,
            this.gridColumn87,
            this.gridColumn88,
            this.gridColumn197});
            this.gridView5.GridControl = this.gridControl3;
            this.gridView5.Name = "gridView5";
            this.gridView5.OptionsBehavior.Editable = false;
            this.gridView5.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView5.OptionsView.ColumnAutoWidth = false;
            this.gridView5.OptionsView.ShowGroupPanel = false;
            this.gridView5.DoubleClick += new System.EventHandler(this.gridControl3_DoubleClick);
            // 
            // gridColumn58
            // 
            this.gridColumn58.Caption = "距离(米)";
            this.gridColumn58.FieldName = "Idistance";
            this.gridColumn58.Name = "gridColumn58";
            this.gridColumn58.Visible = true;
            this.gridColumn58.VisibleIndex = 0;
            // 
            // gridColumn59
            // 
            this.gridColumn59.Caption = "时长(秒)";
            this.gridColumn59.FieldName = "Iduration";
            this.gridColumn59.Name = "gridColumn59";
            this.gridColumn59.Visible = true;
            this.gridColumn59.VisibleIndex = 1;
            // 
            // gridColumn60
            // 
            this.gridColumn60.Caption = "速率0";
            this.gridColumn60.FieldName = "APP_Speed0";
            this.gridColumn60.Name = "gridColumn60";
            this.gridColumn60.Visible = true;
            this.gridColumn60.VisibleIndex = 2;
            // 
            // gridColumn61
            // 
            this.gridColumn61.Caption = "速率[0,40]";
            this.gridColumn61.FieldName = "APP_Speed0_40";
            this.gridColumn61.Name = "gridColumn61";
            this.gridColumn61.Visible = true;
            this.gridColumn61.VisibleIndex = 3;
            // 
            // gridColumn62
            // 
            this.gridColumn62.Caption = "速率[40,70]";
            this.gridColumn62.FieldName = "APP_Speed40_70";
            this.gridColumn62.Name = "gridColumn62";
            this.gridColumn62.Visible = true;
            this.gridColumn62.VisibleIndex = 4;
            // 
            // gridColumn63
            // 
            this.gridColumn63.Caption = "速率[70,90]";
            this.gridColumn63.FieldName = "APP_Speed70_90";
            this.gridColumn63.Name = "gridColumn63";
            this.gridColumn63.Visible = true;
            this.gridColumn63.VisibleIndex = 5;
            // 
            // gridColumn64
            // 
            this.gridColumn64.Caption = "速率[90,110]";
            this.gridColumn64.FieldName = "APP_Speed90_110";
            this.gridColumn64.Name = "gridColumn64";
            this.gridColumn64.Visible = true;
            this.gridColumn64.VisibleIndex = 6;
            // 
            // gridColumn65
            // 
            this.gridColumn65.Caption = "速率110";
            this.gridColumn65.FieldName = "APP_Speed110";
            this.gridColumn65.Name = "gridColumn65";
            this.gridColumn65.Visible = true;
            this.gridColumn65.VisibleIndex = 7;
            // 
            // gridColumn66
            // 
            this.gridColumn66.Caption = "误码[0,20]";
            this.gridColumn66.FieldName = "BLER0_20";
            this.gridColumn66.Name = "gridColumn66";
            this.gridColumn66.Visible = true;
            this.gridColumn66.VisibleIndex = 8;
            // 
            // gridColumn67
            // 
            this.gridColumn67.Caption = "误码[20,50]";
            this.gridColumn67.FieldName = "BLER20_50";
            this.gridColumn67.Name = "gridColumn67";
            this.gridColumn67.Visible = true;
            this.gridColumn67.VisibleIndex = 9;
            // 
            // gridColumn68
            // 
            this.gridColumn68.Caption = "误码[50,100]";
            this.gridColumn68.FieldName = "BLER50_100";
            this.gridColumn68.Name = "gridColumn68";
            this.gridColumn68.Visible = true;
            this.gridColumn68.VisibleIndex = 10;
            // 
            // gridColumn69
            // 
            this.gridColumn69.Caption = "信号质量[1,10]";
            this.gridColumn69.FieldName = "BEP_Mean1_10";
            this.gridColumn69.Name = "gridColumn69";
            this.gridColumn69.Visible = true;
            this.gridColumn69.VisibleIndex = 11;
            // 
            // gridColumn70
            // 
            this.gridColumn70.Caption = "信号质量[11,17]";
            this.gridColumn70.FieldName = "BEP_Mean11_17";
            this.gridColumn70.Name = "gridColumn70";
            this.gridColumn70.Visible = true;
            this.gridColumn70.VisibleIndex = 12;
            // 
            // gridColumn71
            // 
            this.gridColumn71.Caption = "信号质量[18,27]";
            this.gridColumn71.FieldName = "BEP_Mean18_27";
            this.gridColumn71.Name = "gridColumn71";
            this.gridColumn71.Visible = true;
            this.gridColumn71.VisibleIndex = 13;
            // 
            // gridColumn72
            // 
            this.gridColumn72.Caption = "信号质量[28,31]";
            this.gridColumn72.FieldName = "BEP_Mean28_31";
            this.gridColumn72.Name = "gridColumn72";
            this.gridColumn72.Visible = true;
            this.gridColumn72.VisibleIndex = 14;
            // 
            // gridColumn73
            // 
            this.gridColumn73.Caption = "TBF打开状态";
            this.gridColumn73.FieldName = "TBF_OPEN";
            this.gridColumn73.Name = "gridColumn73";
            this.gridColumn73.Visible = true;
            this.gridColumn73.VisibleIndex = 15;
            // 
            // gridColumn74
            // 
            this.gridColumn74.Caption = "TBF关闭状态";
            this.gridColumn74.FieldName = "TBF_CLOSE";
            this.gridColumn74.Name = "gridColumn74";
            this.gridColumn74.Visible = true;
            this.gridColumn74.VisibleIndex = 16;
            // 
            // gridColumn75
            // 
            this.gridColumn75.Caption = "时隙1";
            this.gridColumn75.FieldName = "TimeSlot1";
            this.gridColumn75.Name = "gridColumn75";
            this.gridColumn75.Visible = true;
            this.gridColumn75.VisibleIndex = 17;
            // 
            // gridColumn76
            // 
            this.gridColumn76.Caption = "时隙2";
            this.gridColumn76.FieldName = "TimeSlot2";
            this.gridColumn76.Name = "gridColumn76";
            this.gridColumn76.Visible = true;
            this.gridColumn76.VisibleIndex = 18;
            // 
            // gridColumn77
            // 
            this.gridColumn77.Caption = "时隙3";
            this.gridColumn77.FieldName = "TimeSlot3";
            this.gridColumn77.Name = "gridColumn77";
            this.gridColumn77.Visible = true;
            this.gridColumn77.VisibleIndex = 19;
            // 
            // gridColumn78
            // 
            this.gridColumn78.Caption = "时隙4";
            this.gridColumn78.FieldName = "TimeSlot4";
            this.gridColumn78.Name = "gridColumn78";
            this.gridColumn78.Visible = true;
            this.gridColumn78.VisibleIndex = 20;
            // 
            // gridColumn79
            // 
            this.gridColumn79.Caption = "小区1";
            this.gridColumn79.FieldName = "Cell1";
            this.gridColumn79.Name = "gridColumn79";
            this.gridColumn79.Visible = true;
            this.gridColumn79.VisibleIndex = 21;
            // 
            // gridColumn80
            // 
            this.gridColumn80.Caption = "小区2";
            this.gridColumn80.FieldName = "Cell2";
            this.gridColumn80.Name = "gridColumn80";
            this.gridColumn80.Visible = true;
            this.gridColumn80.VisibleIndex = 22;
            // 
            // gridColumn81
            // 
            this.gridColumn81.Caption = "小区3";
            this.gridColumn81.FieldName = "Cell3";
            this.gridColumn81.Name = "gridColumn81";
            this.gridColumn81.Visible = true;
            this.gridColumn81.VisibleIndex = 23;
            // 
            // gridColumn82
            // 
            this.gridColumn82.Caption = "所属网格";
            this.gridColumn82.FieldName = "Strgrid";
            this.gridColumn82.Name = "gridColumn82";
            this.gridColumn82.Visible = true;
            this.gridColumn82.VisibleIndex = 24;
            // 
            // gridColumn83
            // 
            this.gridColumn83.Caption = "所属道路";
            this.gridColumn83.FieldName = "Strroad";
            this.gridColumn83.Name = "gridColumn83";
            this.gridColumn83.Visible = true;
            this.gridColumn83.VisibleIndex = 25;
            // 
            // gridColumn84
            // 
            this.gridColumn84.Caption = "经度";
            this.gridColumn84.FieldName = "Imlongitude";
            this.gridColumn84.Name = "gridColumn84";
            // 
            // gridColumn85
            // 
            this.gridColumn85.Caption = "纬度";
            this.gridColumn85.FieldName = "Imlatitude";
            this.gridColumn85.Name = "gridColumn85";
            // 
            // gridColumn86
            // 
            this.gridColumn86.Caption = "ifileid";
            this.gridColumn86.FieldName = "Ifileid";
            this.gridColumn86.Name = "gridColumn86";
            // 
            // gridColumn87
            // 
            this.gridColumn87.Caption = "istime";
            this.gridColumn87.FieldName = "Istime";
            this.gridColumn87.Name = "gridColumn87";
            // 
            // gridColumn88
            // 
            this.gridColumn88.Caption = "ietime";
            this.gridColumn88.FieldName = "Ietime";
            this.gridColumn88.Name = "gridColumn88";
            // 
            // gridColumn197
            // 
            this.gridColumn197.Caption = "gridColumn197";
            this.gridColumn197.FieldName = "Iid";
            this.gridColumn197.Name = "gridColumn197";
            // 
            // gridView6
            // 
            this.gridView6.GridControl = this.gridControl3;
            this.gridView6.Name = "gridView6";
            // 
            // xtraTabPage5
            // 
            this.xtraTabPage5.Controls.Add(this.gridControl4);
            this.xtraTabPage5.Name = "xtraTabPage5";
            this.xtraTabPage5.Size = new System.Drawing.Size(836, 381);
            this.xtraTabPage5.Text = "高BLER";
            // 
            // gridControl4
            // 
            this.gridControl4.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl4.Location = new System.Drawing.Point(0, 0);
            this.gridControl4.MainView = this.bandedGridView4;
            this.gridControl4.Name = "gridControl4";
            this.gridControl4.Size = new System.Drawing.Size(836, 381);
            this.gridControl4.TabIndex = 1;
            this.gridControl4.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView4,
            this.gridView7,
            this.gridView8});
            this.gridControl4.DoubleClick += new System.EventHandler(this.gridControl4_DoubleClick);
            // 
            // bandedGridView4
            // 
            this.bandedGridView4.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand4,
            this.gridBand25,
            this.gridBand26,
            this.gridBand27,
            this.gridBand28,
            this.gridBand29,
            this.gridBand30});
            this.bandedGridView4.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn97,
            this.bandedGridColumn98,
            this.bandedGridColumn99,
            this.bandedGridColumn100,
            this.bandedGridColumn101,
            this.bandedGridColumn102,
            this.bandedGridColumn103,
            this.bandedGridColumn104,
            this.bandedGridColumn105,
            this.bandedGridColumn106,
            this.bandedGridColumn107,
            this.bandedGridColumn108,
            this.bandedGridColumn109,
            this.bandedGridColumn110,
            this.bandedGridColumn111,
            this.bandedGridColumn112,
            this.bandedGridColumn113,
            this.bandedGridColumn114,
            this.bandedGridColumn115,
            this.bandedGridColumn116,
            this.bandedGridColumn117,
            this.bandedGridColumn118,
            this.bandedGridColumn119,
            this.bandedGridColumn120,
            this.bandedGridColumn121,
            this.bandedGridColumn122,
            this.bandedGridColumn123,
            this.bandedGridColumn124,
            this.bandedGridColumn125,
            this.bandedGridColumn126,
            this.bandedGridColumn127,
            this.bandedGridColumn128});
            this.bandedGridView4.GridControl = this.gridControl4;
            this.bandedGridView4.Name = "bandedGridView4";
            this.bandedGridView4.OptionsBehavior.Editable = false;
            this.bandedGridView4.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.bandedGridView4.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView4.OptionsView.ShowGroupPanel = false;
            // 
            // gridView7
            // 
            this.gridView7.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn89,
            this.gridColumn90,
            this.gridColumn91,
            this.gridColumn92,
            this.gridColumn93,
            this.gridColumn94,
            this.gridColumn95,
            this.gridColumn96,
            this.gridColumn97,
            this.gridColumn98,
            this.gridColumn99,
            this.gridColumn100,
            this.gridColumn101,
            this.gridColumn102,
            this.gridColumn103,
            this.gridColumn104,
            this.gridColumn105,
            this.gridColumn106,
            this.gridColumn107,
            this.gridColumn108,
            this.gridColumn114,
            this.gridColumn115,
            this.gridColumn116,
            this.gridColumn117,
            this.gridColumn118,
            this.gridColumn119,
            this.gridColumn120,
            this.gridColumn121,
            this.gridColumn122,
            this.gridColumn123,
            this.gridColumn124,
            this.gridColumn198});
            this.gridView7.GridControl = this.gridControl4;
            this.gridView7.Name = "gridView7";
            this.gridView7.OptionsBehavior.Editable = false;
            this.gridView7.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView7.OptionsView.ColumnAutoWidth = false;
            this.gridView7.OptionsView.ShowGroupPanel = false;
            this.gridView7.DoubleClick += new System.EventHandler(this.gridControl4_DoubleClick);
            // 
            // gridColumn89
            // 
            this.gridColumn89.Caption = "距离(米)";
            this.gridColumn89.FieldName = "Idistance";
            this.gridColumn89.Name = "gridColumn89";
            this.gridColumn89.Visible = true;
            this.gridColumn89.VisibleIndex = 0;
            // 
            // gridColumn90
            // 
            this.gridColumn90.Caption = "时长(秒)";
            this.gridColumn90.FieldName = "Iduration";
            this.gridColumn90.Name = "gridColumn90";
            this.gridColumn90.Visible = true;
            this.gridColumn90.VisibleIndex = 1;
            // 
            // gridColumn91
            // 
            this.gridColumn91.Caption = "速率0";
            this.gridColumn91.FieldName = "APP_Speed0";
            this.gridColumn91.Name = "gridColumn91";
            this.gridColumn91.Visible = true;
            this.gridColumn91.VisibleIndex = 2;
            // 
            // gridColumn92
            // 
            this.gridColumn92.Caption = "速率[0,40]";
            this.gridColumn92.FieldName = "APP_Speed0_40";
            this.gridColumn92.Name = "gridColumn92";
            this.gridColumn92.Visible = true;
            this.gridColumn92.VisibleIndex = 3;
            // 
            // gridColumn93
            // 
            this.gridColumn93.Caption = "速率[40,70]";
            this.gridColumn93.FieldName = "APP_Speed40_70";
            this.gridColumn93.Name = "gridColumn93";
            this.gridColumn93.Visible = true;
            this.gridColumn93.VisibleIndex = 4;
            // 
            // gridColumn94
            // 
            this.gridColumn94.Caption = "速率[70,90]";
            this.gridColumn94.FieldName = "APP_Speed70_90";
            this.gridColumn94.Name = "gridColumn94";
            this.gridColumn94.Visible = true;
            this.gridColumn94.VisibleIndex = 5;
            // 
            // gridColumn95
            // 
            this.gridColumn95.Caption = "速率[90,110]";
            this.gridColumn95.FieldName = "APP_Speed90_110";
            this.gridColumn95.Name = "gridColumn95";
            this.gridColumn95.Visible = true;
            this.gridColumn95.VisibleIndex = 6;
            // 
            // gridColumn96
            // 
            this.gridColumn96.Caption = "速率110";
            this.gridColumn96.FieldName = "APP_Speed110";
            this.gridColumn96.Name = "gridColumn96";
            this.gridColumn96.Visible = true;
            this.gridColumn96.VisibleIndex = 7;
            // 
            // gridColumn97
            // 
            this.gridColumn97.Caption = "误码[0,20]";
            this.gridColumn97.FieldName = "BLER0_20";
            this.gridColumn97.Name = "gridColumn97";
            this.gridColumn97.Visible = true;
            this.gridColumn97.VisibleIndex = 8;
            // 
            // gridColumn98
            // 
            this.gridColumn98.Caption = "误码[20,50]";
            this.gridColumn98.FieldName = "BLER20_50";
            this.gridColumn98.Name = "gridColumn98";
            this.gridColumn98.Visible = true;
            this.gridColumn98.VisibleIndex = 9;
            // 
            // gridColumn99
            // 
            this.gridColumn99.Caption = "误码[50,100]";
            this.gridColumn99.FieldName = "BLER50_100";
            this.gridColumn99.Name = "gridColumn99";
            this.gridColumn99.Visible = true;
            this.gridColumn99.VisibleIndex = 10;
            // 
            // gridColumn100
            // 
            this.gridColumn100.Caption = "信号质量[1,10]";
            this.gridColumn100.FieldName = "BEP_Mean1_10";
            this.gridColumn100.Name = "gridColumn100";
            this.gridColumn100.Visible = true;
            this.gridColumn100.VisibleIndex = 11;
            // 
            // gridColumn101
            // 
            this.gridColumn101.Caption = "信号质量[11,17]";
            this.gridColumn101.FieldName = "BEP_Mean11_17";
            this.gridColumn101.Name = "gridColumn101";
            this.gridColumn101.Visible = true;
            this.gridColumn101.VisibleIndex = 12;
            // 
            // gridColumn102
            // 
            this.gridColumn102.Caption = "信号质量[18,27]";
            this.gridColumn102.FieldName = "BEP_Mean18_27";
            this.gridColumn102.Name = "gridColumn102";
            this.gridColumn102.Visible = true;
            this.gridColumn102.VisibleIndex = 13;
            // 
            // gridColumn103
            // 
            this.gridColumn103.Caption = "信号质量[28,31]";
            this.gridColumn103.FieldName = "BEP_Mean28_31";
            this.gridColumn103.Name = "gridColumn103";
            this.gridColumn103.Visible = true;
            this.gridColumn103.VisibleIndex = 14;
            // 
            // gridColumn104
            // 
            this.gridColumn104.Caption = "TBF打开状态";
            this.gridColumn104.FieldName = "TBF_OPEN";
            this.gridColumn104.Name = "gridColumn104";
            this.gridColumn104.Visible = true;
            this.gridColumn104.VisibleIndex = 15;
            // 
            // gridColumn105
            // 
            this.gridColumn105.Caption = "TBF关闭状态";
            this.gridColumn105.FieldName = "TBF_CLOSE";
            this.gridColumn105.Name = "gridColumn105";
            this.gridColumn105.Visible = true;
            this.gridColumn105.VisibleIndex = 16;
            // 
            // gridColumn106
            // 
            this.gridColumn106.Caption = "时隙1";
            this.gridColumn106.FieldName = "TimeSlot1";
            this.gridColumn106.Name = "gridColumn106";
            this.gridColumn106.Visible = true;
            this.gridColumn106.VisibleIndex = 17;
            // 
            // gridColumn107
            // 
            this.gridColumn107.Caption = "时隙2";
            this.gridColumn107.FieldName = "TimeSlot2";
            this.gridColumn107.Name = "gridColumn107";
            this.gridColumn107.Visible = true;
            this.gridColumn107.VisibleIndex = 18;
            // 
            // gridColumn108
            // 
            this.gridColumn108.Caption = "时隙3";
            this.gridColumn108.FieldName = "TimeSlot3";
            this.gridColumn108.Name = "gridColumn108";
            this.gridColumn108.Visible = true;
            this.gridColumn108.VisibleIndex = 19;
            // 
            // gridColumn114
            // 
            this.gridColumn114.Caption = "时隙4";
            this.gridColumn114.FieldName = "TimeSlot4";
            this.gridColumn114.Name = "gridColumn114";
            this.gridColumn114.Visible = true;
            this.gridColumn114.VisibleIndex = 20;
            // 
            // gridColumn115
            // 
            this.gridColumn115.Caption = "小区1";
            this.gridColumn115.FieldName = "Cell1";
            this.gridColumn115.Name = "gridColumn115";
            this.gridColumn115.Visible = true;
            this.gridColumn115.VisibleIndex = 21;
            // 
            // gridColumn116
            // 
            this.gridColumn116.Caption = "小区2";
            this.gridColumn116.FieldName = "Cell2";
            this.gridColumn116.Name = "gridColumn116";
            this.gridColumn116.Visible = true;
            this.gridColumn116.VisibleIndex = 22;
            // 
            // gridColumn117
            // 
            this.gridColumn117.Caption = "小区3";
            this.gridColumn117.FieldName = "Cell3";
            this.gridColumn117.Name = "gridColumn117";
            this.gridColumn117.Visible = true;
            this.gridColumn117.VisibleIndex = 23;
            // 
            // gridColumn118
            // 
            this.gridColumn118.Caption = "所属网格";
            this.gridColumn118.FieldName = "Strgrid";
            this.gridColumn118.Name = "gridColumn118";
            this.gridColumn118.Visible = true;
            this.gridColumn118.VisibleIndex = 24;
            // 
            // gridColumn119
            // 
            this.gridColumn119.Caption = "所属道路";
            this.gridColumn119.FieldName = "Strroad";
            this.gridColumn119.Name = "gridColumn119";
            this.gridColumn119.Visible = true;
            this.gridColumn119.VisibleIndex = 25;
            // 
            // gridColumn120
            // 
            this.gridColumn120.Caption = "经度";
            this.gridColumn120.FieldName = "Imlongitude";
            this.gridColumn120.Name = "gridColumn120";
            // 
            // gridColumn121
            // 
            this.gridColumn121.Caption = "纬度";
            this.gridColumn121.FieldName = "Imlatitude";
            this.gridColumn121.Name = "gridColumn121";
            // 
            // gridColumn122
            // 
            this.gridColumn122.Caption = "ifileid";
            this.gridColumn122.FieldName = "Ifileid";
            this.gridColumn122.Name = "gridColumn122";
            // 
            // gridColumn123
            // 
            this.gridColumn123.Caption = "istime";
            this.gridColumn123.FieldName = "Istime";
            this.gridColumn123.Name = "gridColumn123";
            // 
            // gridColumn124
            // 
            this.gridColumn124.Caption = "ietime";
            this.gridColumn124.FieldName = "Ietime";
            this.gridColumn124.Name = "gridColumn124";
            // 
            // gridColumn198
            // 
            this.gridColumn198.Caption = "gridColumn198";
            this.gridColumn198.FieldName = "Iid";
            this.gridColumn198.Name = "gridColumn198";
            // 
            // gridView8
            // 
            this.gridView8.GridControl = this.gridControl4;
            this.gridView8.Name = "gridView8";
            // 
            // xtraTabPage6
            // 
            this.xtraTabPage6.Controls.Add(this.gridControl6);
            this.xtraTabPage6.Name = "xtraTabPage6";
            this.xtraTabPage6.Size = new System.Drawing.Size(836, 381);
            this.xtraTabPage6.Text = "0速率";
            // 
            // gridControl6
            // 
            this.gridControl6.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl6.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl6.Location = new System.Drawing.Point(0, 0);
            this.gridControl6.MainView = this.bandedGridView5;
            this.gridControl6.Name = "gridControl6";
            this.gridControl6.Size = new System.Drawing.Size(836, 381);
            this.gridControl6.TabIndex = 1;
            this.gridControl6.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView5,
            this.gridView10,
            this.gridView11});
            this.gridControl6.DoubleClick += new System.EventHandler(this.gridControl6_DoubleClick);
            // 
            // bandedGridView5
            // 
            this.bandedGridView5.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand5,
            this.gridBand31,
            this.gridBand32,
            this.gridBand33,
            this.gridBand34,
            this.gridBand35,
            this.gridBand36});
            this.bandedGridView5.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn129,
            this.bandedGridColumn130,
            this.bandedGridColumn131,
            this.bandedGridColumn132,
            this.bandedGridColumn133,
            this.bandedGridColumn134,
            this.bandedGridColumn135,
            this.bandedGridColumn136,
            this.bandedGridColumn137,
            this.bandedGridColumn138,
            this.bandedGridColumn139,
            this.bandedGridColumn140,
            this.bandedGridColumn141,
            this.bandedGridColumn142,
            this.bandedGridColumn143,
            this.bandedGridColumn144,
            this.bandedGridColumn145,
            this.bandedGridColumn146,
            this.bandedGridColumn147,
            this.bandedGridColumn148,
            this.bandedGridColumn149,
            this.bandedGridColumn150,
            this.bandedGridColumn151,
            this.bandedGridColumn152,
            this.bandedGridColumn153,
            this.bandedGridColumn154,
            this.bandedGridColumn155,
            this.bandedGridColumn156,
            this.bandedGridColumn157,
            this.bandedGridColumn158,
            this.bandedGridColumn159,
            this.bandedGridColumn160});
            this.bandedGridView5.GridControl = this.gridControl6;
            this.bandedGridView5.Name = "bandedGridView5";
            this.bandedGridView5.OptionsBehavior.Editable = false;
            this.bandedGridView5.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.bandedGridView5.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView5.OptionsView.ShowGroupPanel = false;
            // 
            // gridView10
            // 
            this.gridView10.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn125,
            this.gridColumn126,
            this.gridColumn127,
            this.gridColumn128,
            this.gridColumn137,
            this.gridColumn138,
            this.gridColumn139,
            this.gridColumn140,
            this.gridColumn141,
            this.gridColumn142,
            this.gridColumn143,
            this.gridColumn144,
            this.gridColumn145,
            this.gridColumn146,
            this.gridColumn147,
            this.gridColumn148,
            this.gridColumn149,
            this.gridColumn150,
            this.gridColumn151,
            this.gridColumn152,
            this.gridColumn153,
            this.gridColumn154,
            this.gridColumn155,
            this.gridColumn156,
            this.gridColumn157,
            this.gridColumn158,
            this.gridColumn159,
            this.gridColumn160,
            this.gridColumn161,
            this.gridColumn162,
            this.gridColumn163,
            this.gridColumn199});
            this.gridView10.GridControl = this.gridControl6;
            this.gridView10.Name = "gridView10";
            this.gridView10.OptionsBehavior.Editable = false;
            this.gridView10.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView10.OptionsView.ColumnAutoWidth = false;
            this.gridView10.OptionsView.ShowGroupPanel = false;
            this.gridView10.DoubleClick += new System.EventHandler(this.gridView10_DoubleClick);
            // 
            // gridColumn125
            // 
            this.gridColumn125.Caption = "距离(米)";
            this.gridColumn125.FieldName = "Idistance";
            this.gridColumn125.Name = "gridColumn125";
            this.gridColumn125.Visible = true;
            this.gridColumn125.VisibleIndex = 0;
            // 
            // gridColumn126
            // 
            this.gridColumn126.Caption = "时长(秒)";
            this.gridColumn126.FieldName = "Iduration";
            this.gridColumn126.Name = "gridColumn126";
            this.gridColumn126.Visible = true;
            this.gridColumn126.VisibleIndex = 1;
            // 
            // gridColumn127
            // 
            this.gridColumn127.Caption = "速率0";
            this.gridColumn127.FieldName = "APP_Speed0";
            this.gridColumn127.Name = "gridColumn127";
            this.gridColumn127.Visible = true;
            this.gridColumn127.VisibleIndex = 2;
            // 
            // gridColumn128
            // 
            this.gridColumn128.Caption = "速率[0,40]";
            this.gridColumn128.FieldName = "APP_Speed0_40";
            this.gridColumn128.Name = "gridColumn128";
            this.gridColumn128.Visible = true;
            this.gridColumn128.VisibleIndex = 3;
            // 
            // gridColumn137
            // 
            this.gridColumn137.Caption = "速率[40,70]";
            this.gridColumn137.FieldName = "APP_Speed40_70";
            this.gridColumn137.Name = "gridColumn137";
            this.gridColumn137.Visible = true;
            this.gridColumn137.VisibleIndex = 4;
            // 
            // gridColumn138
            // 
            this.gridColumn138.Caption = "速率[70,90]";
            this.gridColumn138.FieldName = "APP_Speed70_90";
            this.gridColumn138.Name = "gridColumn138";
            this.gridColumn138.Visible = true;
            this.gridColumn138.VisibleIndex = 5;
            // 
            // gridColumn139
            // 
            this.gridColumn139.Caption = "速率[90,110]";
            this.gridColumn139.FieldName = "APP_Speed90_110";
            this.gridColumn139.Name = "gridColumn139";
            this.gridColumn139.Visible = true;
            this.gridColumn139.VisibleIndex = 6;
            // 
            // gridColumn140
            // 
            this.gridColumn140.Caption = "速率110";
            this.gridColumn140.FieldName = "APP_Speed110";
            this.gridColumn140.Name = "gridColumn140";
            this.gridColumn140.Visible = true;
            this.gridColumn140.VisibleIndex = 7;
            // 
            // gridColumn141
            // 
            this.gridColumn141.Caption = "误码[0,20]";
            this.gridColumn141.FieldName = "BLER0_20";
            this.gridColumn141.Name = "gridColumn141";
            this.gridColumn141.Visible = true;
            this.gridColumn141.VisibleIndex = 8;
            // 
            // gridColumn142
            // 
            this.gridColumn142.Caption = "误码[20,50]";
            this.gridColumn142.FieldName = "BLER20_50";
            this.gridColumn142.Name = "gridColumn142";
            this.gridColumn142.Visible = true;
            this.gridColumn142.VisibleIndex = 9;
            // 
            // gridColumn143
            // 
            this.gridColumn143.Caption = "误码[50,100]";
            this.gridColumn143.FieldName = "BLER50_100";
            this.gridColumn143.Name = "gridColumn143";
            this.gridColumn143.Visible = true;
            this.gridColumn143.VisibleIndex = 10;
            // 
            // gridColumn144
            // 
            this.gridColumn144.Caption = "信号质量[1,10]";
            this.gridColumn144.FieldName = "BEP_Mean1_10";
            this.gridColumn144.Name = "gridColumn144";
            this.gridColumn144.Visible = true;
            this.gridColumn144.VisibleIndex = 11;
            // 
            // gridColumn145
            // 
            this.gridColumn145.Caption = "信号质量[11,17]";
            this.gridColumn145.FieldName = "BEP_Mean11_17";
            this.gridColumn145.Name = "gridColumn145";
            this.gridColumn145.Visible = true;
            this.gridColumn145.VisibleIndex = 12;
            // 
            // gridColumn146
            // 
            this.gridColumn146.Caption = "信号质量[18,27]";
            this.gridColumn146.FieldName = "BEP_Mean18_27";
            this.gridColumn146.Name = "gridColumn146";
            this.gridColumn146.Visible = true;
            this.gridColumn146.VisibleIndex = 13;
            // 
            // gridColumn147
            // 
            this.gridColumn147.Caption = "信号质量[28,31]";
            this.gridColumn147.FieldName = "BEP_Mean28_31";
            this.gridColumn147.Name = "gridColumn147";
            this.gridColumn147.Visible = true;
            this.gridColumn147.VisibleIndex = 14;
            // 
            // gridColumn148
            // 
            this.gridColumn148.Caption = "TBF打开状态";
            this.gridColumn148.FieldName = "TBF_OPEN";
            this.gridColumn148.Name = "gridColumn148";
            this.gridColumn148.Visible = true;
            this.gridColumn148.VisibleIndex = 15;
            // 
            // gridColumn149
            // 
            this.gridColumn149.Caption = "TBF关闭状态";
            this.gridColumn149.FieldName = "TBF_CLOSE";
            this.gridColumn149.Name = "gridColumn149";
            this.gridColumn149.Visible = true;
            this.gridColumn149.VisibleIndex = 16;
            // 
            // gridColumn150
            // 
            this.gridColumn150.Caption = "时隙1";
            this.gridColumn150.FieldName = "TimeSlot1";
            this.gridColumn150.Name = "gridColumn150";
            this.gridColumn150.Visible = true;
            this.gridColumn150.VisibleIndex = 17;
            // 
            // gridColumn151
            // 
            this.gridColumn151.Caption = "时隙2";
            this.gridColumn151.FieldName = "TimeSlot2";
            this.gridColumn151.Name = "gridColumn151";
            this.gridColumn151.Visible = true;
            this.gridColumn151.VisibleIndex = 18;
            // 
            // gridColumn152
            // 
            this.gridColumn152.Caption = "时隙3";
            this.gridColumn152.FieldName = "TimeSlot3";
            this.gridColumn152.Name = "gridColumn152";
            this.gridColumn152.Visible = true;
            this.gridColumn152.VisibleIndex = 19;
            // 
            // gridColumn153
            // 
            this.gridColumn153.Caption = "时隙4";
            this.gridColumn153.FieldName = "TimeSlot4";
            this.gridColumn153.Name = "gridColumn153";
            this.gridColumn153.Visible = true;
            this.gridColumn153.VisibleIndex = 20;
            // 
            // gridColumn154
            // 
            this.gridColumn154.Caption = "小区1";
            this.gridColumn154.FieldName = "Cell1";
            this.gridColumn154.Name = "gridColumn154";
            this.gridColumn154.Visible = true;
            this.gridColumn154.VisibleIndex = 21;
            // 
            // gridColumn155
            // 
            this.gridColumn155.Caption = "小区2";
            this.gridColumn155.FieldName = "Cell2";
            this.gridColumn155.Name = "gridColumn155";
            this.gridColumn155.Visible = true;
            this.gridColumn155.VisibleIndex = 22;
            // 
            // gridColumn156
            // 
            this.gridColumn156.Caption = "小区3";
            this.gridColumn156.FieldName = "Cell3";
            this.gridColumn156.Name = "gridColumn156";
            this.gridColumn156.Visible = true;
            this.gridColumn156.VisibleIndex = 23;
            // 
            // gridColumn157
            // 
            this.gridColumn157.Caption = "所属网格";
            this.gridColumn157.FieldName = "Strgrid";
            this.gridColumn157.Name = "gridColumn157";
            this.gridColumn157.Visible = true;
            this.gridColumn157.VisibleIndex = 24;
            // 
            // gridColumn158
            // 
            this.gridColumn158.Caption = "所属道路";
            this.gridColumn158.FieldName = "Strroad";
            this.gridColumn158.Name = "gridColumn158";
            this.gridColumn158.Visible = true;
            this.gridColumn158.VisibleIndex = 25;
            // 
            // gridColumn159
            // 
            this.gridColumn159.Caption = "经度";
            this.gridColumn159.FieldName = "Imlongitude";
            this.gridColumn159.Name = "gridColumn159";
            // 
            // gridColumn160
            // 
            this.gridColumn160.Caption = "纬度";
            this.gridColumn160.FieldName = "Imlatitude";
            this.gridColumn160.Name = "gridColumn160";
            // 
            // gridColumn161
            // 
            this.gridColumn161.Caption = "ifileid";
            this.gridColumn161.FieldName = "Ifileid";
            this.gridColumn161.Name = "gridColumn161";
            // 
            // gridColumn162
            // 
            this.gridColumn162.Caption = "istime";
            this.gridColumn162.FieldName = "Istime";
            this.gridColumn162.Name = "gridColumn162";
            // 
            // gridColumn163
            // 
            this.gridColumn163.Caption = "ietime";
            this.gridColumn163.FieldName = "Ietime";
            this.gridColumn163.Name = "gridColumn163";
            // 
            // gridColumn199
            // 
            this.gridColumn199.Caption = "gridColumn199";
            this.gridColumn199.FieldName = "Iid";
            this.gridColumn199.Name = "gridColumn199";
            // 
            // gridView11
            // 
            this.gridView11.GridControl = this.gridControl6;
            this.gridView11.Name = "gridView11";
            // 
            // xtraTabPage7
            // 
            this.xtraTabPage7.Controls.Add(this.gridControl7);
            this.xtraTabPage7.Name = "xtraTabPage7";
            this.xtraTabPage7.Size = new System.Drawing.Size(836, 381);
            this.xtraTabPage7.Text = "TBF Close状态";
            // 
            // gridControl7
            // 
            this.gridControl7.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl7.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl7.Location = new System.Drawing.Point(0, 0);
            this.gridControl7.MainView = this.bandedGridView6;
            this.gridControl7.Name = "gridControl7";
            this.gridControl7.Size = new System.Drawing.Size(836, 381);
            this.gridControl7.TabIndex = 1;
            this.gridControl7.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView6,
            this.gridView12,
            this.gridView13});
            this.gridControl7.DoubleClick += new System.EventHandler(this.gridControl7_DoubleClick);
            // 
            // bandedGridView6
            // 
            this.bandedGridView6.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand6,
            this.gridBand37,
            this.gridBand38,
            this.gridBand39,
            this.gridBand40,
            this.gridBand41,
            this.gridBand42});
            this.bandedGridView6.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn161,
            this.bandedGridColumn162,
            this.bandedGridColumn163,
            this.bandedGridColumn164,
            this.bandedGridColumn165,
            this.bandedGridColumn166,
            this.bandedGridColumn167,
            this.bandedGridColumn168,
            this.bandedGridColumn169,
            this.bandedGridColumn170,
            this.bandedGridColumn171,
            this.bandedGridColumn172,
            this.bandedGridColumn173,
            this.bandedGridColumn174,
            this.bandedGridColumn175,
            this.bandedGridColumn176,
            this.bandedGridColumn177,
            this.bandedGridColumn178,
            this.bandedGridColumn179,
            this.bandedGridColumn180,
            this.bandedGridColumn181,
            this.bandedGridColumn182,
            this.bandedGridColumn183,
            this.bandedGridColumn184,
            this.bandedGridColumn185,
            this.bandedGridColumn186,
            this.bandedGridColumn187,
            this.bandedGridColumn188,
            this.bandedGridColumn189,
            this.bandedGridColumn190,
            this.bandedGridColumn191,
            this.bandedGridColumn192});
            this.bandedGridView6.GridControl = this.gridControl7;
            this.bandedGridView6.Name = "bandedGridView6";
            this.bandedGridView6.OptionsBehavior.Editable = false;
            this.bandedGridView6.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.bandedGridView6.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView6.OptionsView.ShowGroupPanel = false;
            // 
            // gridView12
            // 
            this.gridView12.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn164,
            this.gridColumn165,
            this.gridColumn166,
            this.gridColumn167,
            this.gridColumn168,
            this.gridColumn169,
            this.gridColumn170,
            this.gridColumn171,
            this.gridColumn172,
            this.gridColumn173,
            this.gridColumn174,
            this.gridColumn175,
            this.gridColumn176,
            this.gridColumn177,
            this.gridColumn178,
            this.gridColumn179,
            this.gridColumn180,
            this.gridColumn181,
            this.gridColumn182,
            this.gridColumn183,
            this.gridColumn184,
            this.gridColumn185,
            this.gridColumn186,
            this.gridColumn187,
            this.gridColumn188,
            this.gridColumn189,
            this.gridColumn190,
            this.gridColumn191,
            this.gridColumn192,
            this.gridColumn193,
            this.gridColumn194,
            this.gridColumn200});
            this.gridView12.GridControl = this.gridControl7;
            this.gridView12.Name = "gridView12";
            this.gridView12.OptionsBehavior.Editable = false;
            this.gridView12.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView12.OptionsView.ColumnAutoWidth = false;
            this.gridView12.OptionsView.ShowGroupPanel = false;
            this.gridView12.DoubleClick += new System.EventHandler(this.gridView12_DoubleClick);
            // 
            // gridColumn164
            // 
            this.gridColumn164.Caption = "距离(米)";
            this.gridColumn164.FieldName = "Idistance";
            this.gridColumn164.Name = "gridColumn164";
            this.gridColumn164.Visible = true;
            this.gridColumn164.VisibleIndex = 0;
            // 
            // gridColumn165
            // 
            this.gridColumn165.Caption = "时长(秒)";
            this.gridColumn165.FieldName = "Iduration";
            this.gridColumn165.Name = "gridColumn165";
            this.gridColumn165.Visible = true;
            this.gridColumn165.VisibleIndex = 1;
            // 
            // gridColumn166
            // 
            this.gridColumn166.Caption = "速率0";
            this.gridColumn166.FieldName = "APP_Speed0";
            this.gridColumn166.Name = "gridColumn166";
            this.gridColumn166.Visible = true;
            this.gridColumn166.VisibleIndex = 2;
            // 
            // gridColumn167
            // 
            this.gridColumn167.Caption = "速率[0,40]";
            this.gridColumn167.FieldName = "APP_Speed0_40";
            this.gridColumn167.Name = "gridColumn167";
            this.gridColumn167.Visible = true;
            this.gridColumn167.VisibleIndex = 3;
            // 
            // gridColumn168
            // 
            this.gridColumn168.Caption = "速率[40,70]";
            this.gridColumn168.FieldName = "APP_Speed40_70";
            this.gridColumn168.Name = "gridColumn168";
            this.gridColumn168.Visible = true;
            this.gridColumn168.VisibleIndex = 4;
            // 
            // gridColumn169
            // 
            this.gridColumn169.Caption = "速率[70,90]";
            this.gridColumn169.FieldName = "APP_Speed70_90";
            this.gridColumn169.Name = "gridColumn169";
            this.gridColumn169.Visible = true;
            this.gridColumn169.VisibleIndex = 5;
            // 
            // gridColumn170
            // 
            this.gridColumn170.Caption = "速率[90,110]";
            this.gridColumn170.FieldName = "APP_Speed90_110";
            this.gridColumn170.Name = "gridColumn170";
            this.gridColumn170.Visible = true;
            this.gridColumn170.VisibleIndex = 6;
            // 
            // gridColumn171
            // 
            this.gridColumn171.Caption = "速率110";
            this.gridColumn171.FieldName = "APP_Speed110";
            this.gridColumn171.Name = "gridColumn171";
            this.gridColumn171.Visible = true;
            this.gridColumn171.VisibleIndex = 7;
            // 
            // gridColumn172
            // 
            this.gridColumn172.Caption = "误码[0,20]";
            this.gridColumn172.FieldName = "BLER0_20";
            this.gridColumn172.Name = "gridColumn172";
            this.gridColumn172.Visible = true;
            this.gridColumn172.VisibleIndex = 8;
            // 
            // gridColumn173
            // 
            this.gridColumn173.Caption = "误码[20,50]";
            this.gridColumn173.FieldName = "BLER20_50";
            this.gridColumn173.Name = "gridColumn173";
            this.gridColumn173.Visible = true;
            this.gridColumn173.VisibleIndex = 9;
            // 
            // gridColumn174
            // 
            this.gridColumn174.Caption = "误码[50,100]";
            this.gridColumn174.FieldName = "BLER50_100";
            this.gridColumn174.Name = "gridColumn174";
            this.gridColumn174.Visible = true;
            this.gridColumn174.VisibleIndex = 10;
            // 
            // gridColumn175
            // 
            this.gridColumn175.Caption = "信号质量[1,10]";
            this.gridColumn175.FieldName = "BEP_Mean1_10";
            this.gridColumn175.Name = "gridColumn175";
            this.gridColumn175.Visible = true;
            this.gridColumn175.VisibleIndex = 11;
            // 
            // gridColumn176
            // 
            this.gridColumn176.Caption = "信号质量[11,17]";
            this.gridColumn176.FieldName = "BEP_Mean11_17";
            this.gridColumn176.Name = "gridColumn176";
            this.gridColumn176.Visible = true;
            this.gridColumn176.VisibleIndex = 12;
            // 
            // gridColumn177
            // 
            this.gridColumn177.Caption = "信号质量[18,27]";
            this.gridColumn177.FieldName = "BEP_Mean18_27";
            this.gridColumn177.Name = "gridColumn177";
            this.gridColumn177.Visible = true;
            this.gridColumn177.VisibleIndex = 13;
            // 
            // gridColumn178
            // 
            this.gridColumn178.Caption = "信号质量[28,31]";
            this.gridColumn178.FieldName = "BEP_Mean28_31";
            this.gridColumn178.Name = "gridColumn178";
            this.gridColumn178.Visible = true;
            this.gridColumn178.VisibleIndex = 14;
            // 
            // gridColumn179
            // 
            this.gridColumn179.Caption = "TBF打开状态";
            this.gridColumn179.FieldName = "TBF_OPEN";
            this.gridColumn179.Name = "gridColumn179";
            this.gridColumn179.Visible = true;
            this.gridColumn179.VisibleIndex = 15;
            // 
            // gridColumn180
            // 
            this.gridColumn180.Caption = "TBF关闭状态";
            this.gridColumn180.FieldName = "TBF_CLOSE";
            this.gridColumn180.Name = "gridColumn180";
            this.gridColumn180.Visible = true;
            this.gridColumn180.VisibleIndex = 16;
            // 
            // gridColumn181
            // 
            this.gridColumn181.Caption = "时隙1";
            this.gridColumn181.FieldName = "TimeSlot1";
            this.gridColumn181.Name = "gridColumn181";
            this.gridColumn181.Visible = true;
            this.gridColumn181.VisibleIndex = 17;
            // 
            // gridColumn182
            // 
            this.gridColumn182.Caption = "时隙2";
            this.gridColumn182.FieldName = "TimeSlot2";
            this.gridColumn182.Name = "gridColumn182";
            this.gridColumn182.Visible = true;
            this.gridColumn182.VisibleIndex = 18;
            // 
            // gridColumn183
            // 
            this.gridColumn183.Caption = "时隙3";
            this.gridColumn183.FieldName = "TimeSlot3";
            this.gridColumn183.Name = "gridColumn183";
            this.gridColumn183.Visible = true;
            this.gridColumn183.VisibleIndex = 19;
            // 
            // gridColumn184
            // 
            this.gridColumn184.Caption = "时隙4";
            this.gridColumn184.FieldName = "TimeSlot4";
            this.gridColumn184.Name = "gridColumn184";
            this.gridColumn184.Visible = true;
            this.gridColumn184.VisibleIndex = 20;
            // 
            // gridColumn185
            // 
            this.gridColumn185.Caption = "小区1";
            this.gridColumn185.FieldName = "Cell1";
            this.gridColumn185.Name = "gridColumn185";
            this.gridColumn185.Visible = true;
            this.gridColumn185.VisibleIndex = 21;
            // 
            // gridColumn186
            // 
            this.gridColumn186.Caption = "小区2";
            this.gridColumn186.FieldName = "Cell2";
            this.gridColumn186.Name = "gridColumn186";
            this.gridColumn186.Visible = true;
            this.gridColumn186.VisibleIndex = 22;
            // 
            // gridColumn187
            // 
            this.gridColumn187.Caption = "小区3";
            this.gridColumn187.FieldName = "Cell3";
            this.gridColumn187.Name = "gridColumn187";
            this.gridColumn187.Visible = true;
            this.gridColumn187.VisibleIndex = 23;
            // 
            // gridColumn188
            // 
            this.gridColumn188.Caption = "所属网格";
            this.gridColumn188.FieldName = "Strgrid";
            this.gridColumn188.Name = "gridColumn188";
            this.gridColumn188.Visible = true;
            this.gridColumn188.VisibleIndex = 24;
            // 
            // gridColumn189
            // 
            this.gridColumn189.Caption = "所属道路";
            this.gridColumn189.FieldName = "Strroad";
            this.gridColumn189.Name = "gridColumn189";
            this.gridColumn189.Visible = true;
            this.gridColumn189.VisibleIndex = 25;
            // 
            // gridColumn190
            // 
            this.gridColumn190.Caption = "经度";
            this.gridColumn190.FieldName = "Imlongitude";
            this.gridColumn190.Name = "gridColumn190";
            // 
            // gridColumn191
            // 
            this.gridColumn191.Caption = "纬度";
            this.gridColumn191.FieldName = "Imlatitude";
            this.gridColumn191.Name = "gridColumn191";
            // 
            // gridColumn192
            // 
            this.gridColumn192.Caption = "ifileid";
            this.gridColumn192.FieldName = "Ifileid";
            this.gridColumn192.Name = "gridColumn192";
            // 
            // gridColumn193
            // 
            this.gridColumn193.Caption = "istime";
            this.gridColumn193.FieldName = "Istime";
            this.gridColumn193.Name = "gridColumn193";
            // 
            // gridColumn194
            // 
            this.gridColumn194.Caption = "ietime";
            this.gridColumn194.FieldName = "Ietime";
            this.gridColumn194.Name = "gridColumn194";
            // 
            // gridColumn200
            // 
            this.gridColumn200.Caption = "gridColumn200";
            this.gridColumn200.FieldName = "Iid";
            this.gridColumn200.Name = "gridColumn200";
            // 
            // gridView13
            // 
            this.gridView13.GridControl = this.gridControl7;
            this.gridView13.Name = "gridView13";
            // 
            // btnClearFly
            // 
            this.btnClearFly.Location = new System.Drawing.Point(0, 0);
            this.btnClearFly.Name = "btnClearFly";
            this.btnClearFly.Size = new System.Drawing.Size(75, 23);
            this.btnClearFly.TabIndex = 0;
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.ColumnCount = 1;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.Controls.Add(this.panel1, 0, 0);
            this.tableLayoutPanel1.Controls.Add(this.xtraTabControl1, 0, 1);
            this.tableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 2;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 0F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(849, 417);
            this.tableLayoutPanel1.TabIndex = 1;
            // 
            // panel1
            // 
            this.panel1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.panel1.Controls.Add(this.label1);
            this.panel1.Controls.Add(this.btnNext1);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel1.Location = new System.Drawing.Point(3, 3);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(843, 1);
            this.panel1.TabIndex = 2;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(26, 20);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(66, 14);
            this.label1.TabIndex = 1;
            this.label1.Text = "1.道路汇聚";
            // 
            // btnNext1
            // 
            this.btnNext1.Enabled = false;
            this.btnNext1.Location = new System.Drawing.Point(101, 16);
            this.btnNext1.Name = "btnNext1";
            this.btnNext1.Size = new System.Drawing.Size(75, 23);
            this.btnNext1.TabIndex = 0;
            this.btnNext1.Text = "下一步";
            this.btnNext1.UseVisualStyleBackColor = true;
            this.btnNext1.Click += new System.EventHandler(this.btnNext1_Click);
            // 
            // gridBand12
            // 
            this.gridBand12.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand12.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand12.Caption = "基础信息";
            this.gridBand12.Columns.Add(this.bandedGridColumn25);
            this.gridBand12.Columns.Add(this.bandedGridColumn26);
            this.gridBand12.Columns.Add(this.bandedGridColumn22);
            this.gridBand12.Columns.Add(this.bandedGridColumn23);
            this.gridBand12.Columns.Add(this.bandedGridColumn24);
            this.gridBand12.Columns.Add(this.bandedGridColumn27);
            this.gridBand12.Columns.Add(this.bandedGridColumn28);
            this.gridBand12.Name = "gridBand12";
            this.gridBand12.Width = 555;
            // 
            // gridBand1
            // 
            this.gridBand1.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand1.Caption = "测试信息";
            this.gridBand1.Columns.Add(this.bandedGridColumn1);
            this.gridBand1.Columns.Add(this.bandedGridColumn2);
            this.gridBand1.Columns.Add(this.bandedGridColumn29);
            this.gridBand1.Columns.Add(this.bandedGridColumn30);
            this.gridBand1.Columns.Add(this.bandedGridColumn31);
            this.gridBand1.Columns.Add(this.bandedGridColumn32);
            this.gridBand1.Name = "gridBand1";
            this.gridBand1.Width = 150;
            // 
            // gridBand7
            // 
            this.gridBand7.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand7.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand7.Caption = "速率";
            this.gridBand7.Columns.Add(this.bandedGridColumn3);
            this.gridBand7.Columns.Add(this.bandedGridColumn4);
            this.gridBand7.Columns.Add(this.bandedGridColumn5);
            this.gridBand7.Columns.Add(this.bandedGridColumn6);
            this.gridBand7.Columns.Add(this.bandedGridColumn7);
            this.gridBand7.Columns.Add(this.bandedGridColumn8);
            this.gridBand7.Name = "gridBand7";
            this.gridBand7.Width = 450;
            // 
            // gridBand8
            // 
            this.gridBand8.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand8.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand8.Caption = "误码";
            this.gridBand8.Columns.Add(this.bandedGridColumn9);
            this.gridBand8.Columns.Add(this.bandedGridColumn10);
            this.gridBand8.Columns.Add(this.bandedGridColumn11);
            this.gridBand8.Name = "gridBand8";
            this.gridBand8.Width = 225;
            // 
            // gridBand9
            // 
            this.gridBand9.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand9.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand9.Caption = "信号质量";
            this.gridBand9.Columns.Add(this.bandedGridColumn12);
            this.gridBand9.Columns.Add(this.bandedGridColumn13);
            this.gridBand9.Columns.Add(this.bandedGridColumn14);
            this.gridBand9.Columns.Add(this.bandedGridColumn15);
            this.gridBand9.Name = "gridBand9";
            this.gridBand9.Width = 300;
            // 
            // gridBand10
            // 
            this.gridBand10.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand10.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand10.Caption = "TBF";
            this.gridBand10.Columns.Add(this.bandedGridColumn16);
            this.gridBand10.Columns.Add(this.bandedGridColumn17);
            this.gridBand10.Name = "gridBand10";
            this.gridBand10.Width = 150;
            // 
            // gridBand11
            // 
            this.gridBand11.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand11.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand11.Caption = "时隙";
            this.gridBand11.Columns.Add(this.bandedGridColumn18);
            this.gridBand11.Columns.Add(this.bandedGridColumn19);
            this.gridBand11.Columns.Add(this.bandedGridColumn20);
            this.gridBand11.Columns.Add(this.bandedGridColumn21);
            this.gridBand11.Name = "gridBand11";
            this.gridBand11.Width = 300;
            // 
            // bandedGridColumn33
            // 
            this.bandedGridColumn33.Caption = "距离(米)";
            this.bandedGridColumn33.CustomizationCaption = "距离(米)";
            this.bandedGridColumn33.FieldName = "Idistance";
            this.bandedGridColumn33.Name = "bandedGridColumn33";
            this.bandedGridColumn33.Visible = true;
            // 
            // bandedGridColumn34
            // 
            this.bandedGridColumn34.Caption = "时长(秒)";
            this.bandedGridColumn34.CustomizationCaption = "时长(秒)";
            this.bandedGridColumn34.FieldName = "Iduration";
            this.bandedGridColumn34.Name = "bandedGridColumn34";
            this.bandedGridColumn34.Visible = true;
            // 
            // bandedGridColumn35
            // 
            this.bandedGridColumn35.Caption = "0";
            this.bandedGridColumn35.CustomizationCaption = "速率0";
            this.bandedGridColumn35.FieldName = "APP_Speed0";
            this.bandedGridColumn35.Name = "bandedGridColumn35";
            this.bandedGridColumn35.Visible = true;
            // 
            // bandedGridColumn36
            // 
            this.bandedGridColumn36.Caption = "[0,40]";
            this.bandedGridColumn36.CustomizationCaption = "速率[0,40]";
            this.bandedGridColumn36.FieldName = "APP_Speed0_40";
            this.bandedGridColumn36.Name = "bandedGridColumn36";
            this.bandedGridColumn36.Visible = true;
            // 
            // bandedGridColumn37
            // 
            this.bandedGridColumn37.Caption = "[40,70]";
            this.bandedGridColumn37.CustomizationCaption = "速率[40,70]";
            this.bandedGridColumn37.FieldName = "APP_Speed40_70";
            this.bandedGridColumn37.Name = "bandedGridColumn37";
            this.bandedGridColumn37.Visible = true;
            // 
            // bandedGridColumn38
            // 
            this.bandedGridColumn38.Caption = "[70,90]";
            this.bandedGridColumn38.CustomizationCaption = "速率[70,90]";
            this.bandedGridColumn38.FieldName = "APP_Speed70_90";
            this.bandedGridColumn38.Name = "bandedGridColumn38";
            this.bandedGridColumn38.Visible = true;
            // 
            // bandedGridColumn39
            // 
            this.bandedGridColumn39.Caption = "[90,110]";
            this.bandedGridColumn39.CustomizationCaption = "速率[90,110]";
            this.bandedGridColumn39.FieldName = "APP_Speed90_110";
            this.bandedGridColumn39.Name = "bandedGridColumn39";
            this.bandedGridColumn39.Visible = true;
            // 
            // bandedGridColumn40
            // 
            this.bandedGridColumn40.Caption = "110";
            this.bandedGridColumn40.CustomizationCaption = "速率110";
            this.bandedGridColumn40.FieldName = "APP_Speed110";
            this.bandedGridColumn40.Name = "bandedGridColumn40";
            this.bandedGridColumn40.Visible = true;
            // 
            // bandedGridColumn41
            // 
            this.bandedGridColumn41.Caption = "[0,20]";
            this.bandedGridColumn41.CustomizationCaption = "误码[0,20]";
            this.bandedGridColumn41.FieldName = "BLER0_20";
            this.bandedGridColumn41.Name = "bandedGridColumn41";
            this.bandedGridColumn41.Visible = true;
            // 
            // bandedGridColumn42
            // 
            this.bandedGridColumn42.Caption = "[20,50]";
            this.bandedGridColumn42.CustomizationCaption = "误码[20,50]";
            this.bandedGridColumn42.FieldName = "BLER20_50";
            this.bandedGridColumn42.Name = "bandedGridColumn42";
            this.bandedGridColumn42.Visible = true;
            // 
            // bandedGridColumn43
            // 
            this.bandedGridColumn43.Caption = "[50,100]";
            this.bandedGridColumn43.CustomizationCaption = "误码[50,100]";
            this.bandedGridColumn43.FieldName = "BLER50_100";
            this.bandedGridColumn43.Name = "bandedGridColumn43";
            this.bandedGridColumn43.Visible = true;
            // 
            // bandedGridColumn44
            // 
            this.bandedGridColumn44.Caption = "[1,10]";
            this.bandedGridColumn44.CustomizationCaption = "信号质量[1,10]";
            this.bandedGridColumn44.FieldName = "BEP_Mean1_10";
            this.bandedGridColumn44.Name = "bandedGridColumn44";
            this.bandedGridColumn44.Visible = true;
            // 
            // bandedGridColumn45
            // 
            this.bandedGridColumn45.Caption = "[11,17]";
            this.bandedGridColumn45.CustomizationCaption = "信号质量[11,17]";
            this.bandedGridColumn45.FieldName = "BEP_Mean11_17";
            this.bandedGridColumn45.Name = "bandedGridColumn45";
            this.bandedGridColumn45.Visible = true;
            // 
            // bandedGridColumn46
            // 
            this.bandedGridColumn46.Caption = "[18,27]";
            this.bandedGridColumn46.CustomizationCaption = "信号质量[18,27]";
            this.bandedGridColumn46.FieldName = "BEP_Mean18_27";
            this.bandedGridColumn46.Name = "bandedGridColumn46";
            this.bandedGridColumn46.Visible = true;
            // 
            // bandedGridColumn47
            // 
            this.bandedGridColumn47.Caption = "[28,31]";
            this.bandedGridColumn47.CustomizationCaption = "信号质量[28,31]";
            this.bandedGridColumn47.FieldName = "BEP_Mean28_31";
            this.bandedGridColumn47.Name = "bandedGridColumn47";
            this.bandedGridColumn47.Visible = true;
            // 
            // bandedGridColumn48
            // 
            this.bandedGridColumn48.Caption = "打开状态";
            this.bandedGridColumn48.CustomizationCaption = "TBF打开状态";
            this.bandedGridColumn48.FieldName = "TBF_OPEN";
            this.bandedGridColumn48.Name = "bandedGridColumn48";
            this.bandedGridColumn48.Visible = true;
            // 
            // bandedGridColumn49
            // 
            this.bandedGridColumn49.Caption = "关闭状态";
            this.bandedGridColumn49.CustomizationCaption = "TBF关闭状态";
            this.bandedGridColumn49.FieldName = "TBF_CLOSE";
            this.bandedGridColumn49.Name = "bandedGridColumn49";
            this.bandedGridColumn49.Visible = true;
            // 
            // bandedGridColumn50
            // 
            this.bandedGridColumn50.Caption = "时隙1";
            this.bandedGridColumn50.CustomizationCaption = "时隙1";
            this.bandedGridColumn50.FieldName = "TimeSlot1";
            this.bandedGridColumn50.Name = "bandedGridColumn50";
            this.bandedGridColumn50.Visible = true;
            // 
            // bandedGridColumn51
            // 
            this.bandedGridColumn51.Caption = "时隙2";
            this.bandedGridColumn51.CustomizationCaption = "时隙2";
            this.bandedGridColumn51.FieldName = "TimeSlot2";
            this.bandedGridColumn51.Name = "bandedGridColumn51";
            this.bandedGridColumn51.Visible = true;
            // 
            // bandedGridColumn52
            // 
            this.bandedGridColumn52.Caption = "时隙3";
            this.bandedGridColumn52.CustomizationCaption = "时隙3";
            this.bandedGridColumn52.FieldName = "TimeSlot3";
            this.bandedGridColumn52.Name = "bandedGridColumn52";
            this.bandedGridColumn52.Visible = true;
            // 
            // bandedGridColumn53
            // 
            this.bandedGridColumn53.Caption = "时隙4";
            this.bandedGridColumn53.CustomizationCaption = "时隙4";
            this.bandedGridColumn53.FieldName = "TimeSlot4";
            this.bandedGridColumn53.Name = "bandedGridColumn53";
            this.bandedGridColumn53.Visible = true;
            // 
            // bandedGridColumn54
            // 
            this.bandedGridColumn54.Caption = "TOP1小区";
            this.bandedGridColumn54.CustomizationCaption = "TIO1小区";
            this.bandedGridColumn54.FieldName = "Cell1";
            this.bandedGridColumn54.Name = "bandedGridColumn54";
            this.bandedGridColumn54.Visible = true;
            this.bandedGridColumn54.Width = 85;
            // 
            // bandedGridColumn55
            // 
            this.bandedGridColumn55.Caption = "TOP2小区";
            this.bandedGridColumn55.CustomizationCaption = "TOP2小区";
            this.bandedGridColumn55.FieldName = "Cell2";
            this.bandedGridColumn55.Name = "bandedGridColumn55";
            this.bandedGridColumn55.Visible = true;
            this.bandedGridColumn55.Width = 85;
            // 
            // bandedGridColumn56
            // 
            this.bandedGridColumn56.Caption = "TOP3小区";
            this.bandedGridColumn56.CustomizationCaption = "TOP3小区";
            this.bandedGridColumn56.FieldName = "Cell3";
            this.bandedGridColumn56.Name = "bandedGridColumn56";
            this.bandedGridColumn56.Visible = true;
            this.bandedGridColumn56.Width = 85;
            // 
            // bandedGridColumn57
            // 
            this.bandedGridColumn57.Caption = "所属网格";
            this.bandedGridColumn57.CustomizationCaption = "所属网格";
            this.bandedGridColumn57.FieldName = "Strgrid";
            this.bandedGridColumn57.Name = "bandedGridColumn57";
            this.bandedGridColumn57.Visible = true;
            // 
            // bandedGridColumn58
            // 
            this.bandedGridColumn58.Caption = "所属道路";
            this.bandedGridColumn58.CustomizationCaption = "所属道路";
            this.bandedGridColumn58.FieldName = "Strroad";
            this.bandedGridColumn58.Name = "bandedGridColumn58";
            this.bandedGridColumn58.Visible = true;
            // 
            // bandedGridColumn59
            // 
            this.bandedGridColumn59.Caption = "经度";
            this.bandedGridColumn59.CustomizationCaption = "经度";
            this.bandedGridColumn59.FieldName = "Imlongitude";
            this.bandedGridColumn59.Name = "bandedGridColumn59";
            this.bandedGridColumn59.Visible = true;
            // 
            // bandedGridColumn60
            // 
            this.bandedGridColumn60.Caption = "纬度";
            this.bandedGridColumn60.CustomizationCaption = "纬度";
            this.bandedGridColumn60.FieldName = "Imlatitude";
            this.bandedGridColumn60.Name = "bandedGridColumn60";
            this.bandedGridColumn60.Visible = true;
            // 
            // bandedGridColumn61
            // 
            this.bandedGridColumn61.Caption = "ifileid";
            this.bandedGridColumn61.FieldName = "Ifileid";
            this.bandedGridColumn61.Name = "bandedGridColumn61";
            // 
            // bandedGridColumn62
            // 
            this.bandedGridColumn62.Caption = "istime";
            this.bandedGridColumn62.FieldName = "Istime";
            this.bandedGridColumn62.Name = "bandedGridColumn62";
            // 
            // bandedGridColumn63
            // 
            this.bandedGridColumn63.Caption = "ietime";
            this.bandedGridColumn63.FieldName = "Ietime";
            this.bandedGridColumn63.Name = "bandedGridColumn63";
            // 
            // bandedGridColumn64
            // 
            this.bandedGridColumn64.Caption = "gridColumn195";
            this.bandedGridColumn64.FieldName = "Iid";
            this.bandedGridColumn64.Name = "bandedGridColumn64";
            // 
            // gridBand2
            // 
            this.gridBand2.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand2.Caption = "基础信息";
            this.gridBand2.Columns.Add(this.bandedGridColumn57);
            this.gridBand2.Columns.Add(this.bandedGridColumn58);
            this.gridBand2.Columns.Add(this.bandedGridColumn54);
            this.gridBand2.Columns.Add(this.bandedGridColumn55);
            this.gridBand2.Columns.Add(this.bandedGridColumn56);
            this.gridBand2.Columns.Add(this.bandedGridColumn59);
            this.gridBand2.Columns.Add(this.bandedGridColumn60);
            this.gridBand2.MinWidth = 20;
            this.gridBand2.Name = "gridBand2";
            this.gridBand2.Width = 555;
            // 
            // gridBand13
            // 
            this.gridBand13.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand13.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand13.Caption = "测试信息";
            this.gridBand13.Columns.Add(this.bandedGridColumn33);
            this.gridBand13.Columns.Add(this.bandedGridColumn34);
            this.gridBand13.Columns.Add(this.bandedGridColumn61);
            this.gridBand13.Columns.Add(this.bandedGridColumn62);
            this.gridBand13.Columns.Add(this.bandedGridColumn63);
            this.gridBand13.Columns.Add(this.bandedGridColumn64);
            this.gridBand13.MinWidth = 20;
            this.gridBand13.Name = "gridBand13";
            this.gridBand13.Width = 150;
            // 
            // gridBand14
            // 
            this.gridBand14.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand14.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand14.Caption = "速率";
            this.gridBand14.Columns.Add(this.bandedGridColumn35);
            this.gridBand14.Columns.Add(this.bandedGridColumn36);
            this.gridBand14.Columns.Add(this.bandedGridColumn37);
            this.gridBand14.Columns.Add(this.bandedGridColumn38);
            this.gridBand14.Columns.Add(this.bandedGridColumn39);
            this.gridBand14.Columns.Add(this.bandedGridColumn40);
            this.gridBand14.MinWidth = 20;
            this.gridBand14.Name = "gridBand14";
            this.gridBand14.Width = 450;
            // 
            // gridBand15
            // 
            this.gridBand15.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand15.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand15.Caption = "误码";
            this.gridBand15.Columns.Add(this.bandedGridColumn41);
            this.gridBand15.Columns.Add(this.bandedGridColumn42);
            this.gridBand15.Columns.Add(this.bandedGridColumn43);
            this.gridBand15.MinWidth = 20;
            this.gridBand15.Name = "gridBand15";
            this.gridBand15.Width = 225;
            // 
            // gridBand16
            // 
            this.gridBand16.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand16.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand16.Caption = "信号质量";
            this.gridBand16.Columns.Add(this.bandedGridColumn44);
            this.gridBand16.Columns.Add(this.bandedGridColumn45);
            this.gridBand16.Columns.Add(this.bandedGridColumn46);
            this.gridBand16.Columns.Add(this.bandedGridColumn47);
            this.gridBand16.MinWidth = 20;
            this.gridBand16.Name = "gridBand16";
            this.gridBand16.Width = 300;
            // 
            // gridBand17
            // 
            this.gridBand17.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand17.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand17.Caption = "TBF";
            this.gridBand17.Columns.Add(this.bandedGridColumn48);
            this.gridBand17.Columns.Add(this.bandedGridColumn49);
            this.gridBand17.MinWidth = 20;
            this.gridBand17.Name = "gridBand17";
            this.gridBand17.Width = 150;
            // 
            // gridBand18
            // 
            this.gridBand18.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand18.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand18.Caption = "时隙";
            this.gridBand18.Columns.Add(this.bandedGridColumn50);
            this.gridBand18.Columns.Add(this.bandedGridColumn51);
            this.gridBand18.Columns.Add(this.bandedGridColumn52);
            this.gridBand18.Columns.Add(this.bandedGridColumn53);
            this.gridBand18.MinWidth = 20;
            this.gridBand18.Name = "gridBand18";
            this.gridBand18.Width = 300;
            // 
            // bandedGridColumn65
            // 
            this.bandedGridColumn65.Caption = "距离(米)";
            this.bandedGridColumn65.CustomizationCaption = "距离(米)";
            this.bandedGridColumn65.FieldName = "Idistance";
            this.bandedGridColumn65.Name = "bandedGridColumn65";
            this.bandedGridColumn65.Visible = true;
            // 
            // bandedGridColumn66
            // 
            this.bandedGridColumn66.Caption = "时长(秒)";
            this.bandedGridColumn66.CustomizationCaption = "时长(秒)";
            this.bandedGridColumn66.FieldName = "Iduration";
            this.bandedGridColumn66.Name = "bandedGridColumn66";
            this.bandedGridColumn66.Visible = true;
            // 
            // bandedGridColumn67
            // 
            this.bandedGridColumn67.Caption = "0";
            this.bandedGridColumn67.CustomizationCaption = "速率0";
            this.bandedGridColumn67.FieldName = "APP_Speed0";
            this.bandedGridColumn67.Name = "bandedGridColumn67";
            this.bandedGridColumn67.Visible = true;
            // 
            // bandedGridColumn68
            // 
            this.bandedGridColumn68.Caption = "[0,40]";
            this.bandedGridColumn68.CustomizationCaption = "速率[0,40]";
            this.bandedGridColumn68.FieldName = "APP_Speed0_40";
            this.bandedGridColumn68.Name = "bandedGridColumn68";
            this.bandedGridColumn68.Visible = true;
            // 
            // bandedGridColumn69
            // 
            this.bandedGridColumn69.Caption = "[40,70]";
            this.bandedGridColumn69.CustomizationCaption = "速率[40,70]";
            this.bandedGridColumn69.FieldName = "APP_Speed40_70";
            this.bandedGridColumn69.Name = "bandedGridColumn69";
            this.bandedGridColumn69.Visible = true;
            // 
            // bandedGridColumn70
            // 
            this.bandedGridColumn70.Caption = "[70,90]";
            this.bandedGridColumn70.CustomizationCaption = "速率[70,90]";
            this.bandedGridColumn70.FieldName = "APP_Speed70_90";
            this.bandedGridColumn70.Name = "bandedGridColumn70";
            this.bandedGridColumn70.Visible = true;
            // 
            // bandedGridColumn71
            // 
            this.bandedGridColumn71.Caption = "[90,110]";
            this.bandedGridColumn71.CustomizationCaption = "速率[90,110]";
            this.bandedGridColumn71.FieldName = "APP_Speed90_110";
            this.bandedGridColumn71.Name = "bandedGridColumn71";
            this.bandedGridColumn71.Visible = true;
            // 
            // bandedGridColumn72
            // 
            this.bandedGridColumn72.Caption = "110";
            this.bandedGridColumn72.CustomizationCaption = "速率110";
            this.bandedGridColumn72.FieldName = "APP_Speed110";
            this.bandedGridColumn72.Name = "bandedGridColumn72";
            this.bandedGridColumn72.Visible = true;
            // 
            // bandedGridColumn73
            // 
            this.bandedGridColumn73.Caption = "[0,20]";
            this.bandedGridColumn73.CustomizationCaption = "误码[0,20]";
            this.bandedGridColumn73.FieldName = "BLER0_20";
            this.bandedGridColumn73.Name = "bandedGridColumn73";
            this.bandedGridColumn73.Visible = true;
            // 
            // bandedGridColumn74
            // 
            this.bandedGridColumn74.Caption = "[20,50]";
            this.bandedGridColumn74.CustomizationCaption = "误码[20,50]";
            this.bandedGridColumn74.FieldName = "BLER20_50";
            this.bandedGridColumn74.Name = "bandedGridColumn74";
            this.bandedGridColumn74.Visible = true;
            // 
            // bandedGridColumn75
            // 
            this.bandedGridColumn75.Caption = "[50,100]";
            this.bandedGridColumn75.CustomizationCaption = "误码[50,100]";
            this.bandedGridColumn75.FieldName = "BLER50_100";
            this.bandedGridColumn75.Name = "bandedGridColumn75";
            this.bandedGridColumn75.Visible = true;
            // 
            // bandedGridColumn76
            // 
            this.bandedGridColumn76.Caption = "[1,10]";
            this.bandedGridColumn76.CustomizationCaption = "信号质量[1,10]";
            this.bandedGridColumn76.FieldName = "BEP_Mean1_10";
            this.bandedGridColumn76.Name = "bandedGridColumn76";
            this.bandedGridColumn76.Visible = true;
            // 
            // bandedGridColumn77
            // 
            this.bandedGridColumn77.Caption = "[11,17]";
            this.bandedGridColumn77.CustomizationCaption = "信号质量[11,17]";
            this.bandedGridColumn77.FieldName = "BEP_Mean11_17";
            this.bandedGridColumn77.Name = "bandedGridColumn77";
            this.bandedGridColumn77.Visible = true;
            // 
            // bandedGridColumn78
            // 
            this.bandedGridColumn78.Caption = "[18,27]";
            this.bandedGridColumn78.CustomizationCaption = "信号质量[18,27]";
            this.bandedGridColumn78.FieldName = "BEP_Mean18_27";
            this.bandedGridColumn78.Name = "bandedGridColumn78";
            this.bandedGridColumn78.Visible = true;
            // 
            // bandedGridColumn79
            // 
            this.bandedGridColumn79.Caption = "[28,31]";
            this.bandedGridColumn79.CustomizationCaption = "信号质量[28,31]";
            this.bandedGridColumn79.FieldName = "BEP_Mean28_31";
            this.bandedGridColumn79.Name = "bandedGridColumn79";
            this.bandedGridColumn79.Visible = true;
            // 
            // bandedGridColumn80
            // 
            this.bandedGridColumn80.Caption = "打开状态";
            this.bandedGridColumn80.CustomizationCaption = "TBF打开状态";
            this.bandedGridColumn80.FieldName = "TBF_OPEN";
            this.bandedGridColumn80.Name = "bandedGridColumn80";
            this.bandedGridColumn80.Visible = true;
            // 
            // bandedGridColumn81
            // 
            this.bandedGridColumn81.Caption = "关闭状态";
            this.bandedGridColumn81.CustomizationCaption = "TBF关闭状态";
            this.bandedGridColumn81.FieldName = "TBF_CLOSE";
            this.bandedGridColumn81.Name = "bandedGridColumn81";
            this.bandedGridColumn81.Visible = true;
            // 
            // bandedGridColumn82
            // 
            this.bandedGridColumn82.Caption = "时隙1";
            this.bandedGridColumn82.CustomizationCaption = "时隙1";
            this.bandedGridColumn82.FieldName = "TimeSlot1";
            this.bandedGridColumn82.Name = "bandedGridColumn82";
            this.bandedGridColumn82.Visible = true;
            // 
            // bandedGridColumn83
            // 
            this.bandedGridColumn83.Caption = "时隙2";
            this.bandedGridColumn83.CustomizationCaption = "时隙2";
            this.bandedGridColumn83.FieldName = "TimeSlot2";
            this.bandedGridColumn83.Name = "bandedGridColumn83";
            this.bandedGridColumn83.Visible = true;
            // 
            // bandedGridColumn84
            // 
            this.bandedGridColumn84.Caption = "时隙3";
            this.bandedGridColumn84.CustomizationCaption = "时隙3";
            this.bandedGridColumn84.FieldName = "TimeSlot3";
            this.bandedGridColumn84.Name = "bandedGridColumn84";
            this.bandedGridColumn84.Visible = true;
            // 
            // bandedGridColumn85
            // 
            this.bandedGridColumn85.Caption = "时隙4";
            this.bandedGridColumn85.CustomizationCaption = "时隙4";
            this.bandedGridColumn85.FieldName = "TimeSlot4";
            this.bandedGridColumn85.Name = "bandedGridColumn85";
            this.bandedGridColumn85.Visible = true;
            // 
            // bandedGridColumn86
            // 
            this.bandedGridColumn86.Caption = "TOP1小区";
            this.bandedGridColumn86.CustomizationCaption = "TIO1小区";
            this.bandedGridColumn86.FieldName = "Cell1";
            this.bandedGridColumn86.Name = "bandedGridColumn86";
            this.bandedGridColumn86.Visible = true;
            this.bandedGridColumn86.Width = 85;
            // 
            // bandedGridColumn87
            // 
            this.bandedGridColumn87.Caption = "TOP2小区";
            this.bandedGridColumn87.CustomizationCaption = "TOP2小区";
            this.bandedGridColumn87.FieldName = "Cell2";
            this.bandedGridColumn87.Name = "bandedGridColumn87";
            this.bandedGridColumn87.Visible = true;
            this.bandedGridColumn87.Width = 85;
            // 
            // bandedGridColumn88
            // 
            this.bandedGridColumn88.Caption = "TOP3小区";
            this.bandedGridColumn88.CustomizationCaption = "TOP3小区";
            this.bandedGridColumn88.FieldName = "Cell3";
            this.bandedGridColumn88.Name = "bandedGridColumn88";
            this.bandedGridColumn88.Visible = true;
            this.bandedGridColumn88.Width = 85;
            // 
            // bandedGridColumn89
            // 
            this.bandedGridColumn89.Caption = "所属网格";
            this.bandedGridColumn89.CustomizationCaption = "所属网格";
            this.bandedGridColumn89.FieldName = "Strgrid";
            this.bandedGridColumn89.Name = "bandedGridColumn89";
            this.bandedGridColumn89.Visible = true;
            // 
            // bandedGridColumn90
            // 
            this.bandedGridColumn90.Caption = "所属道路";
            this.bandedGridColumn90.CustomizationCaption = "所属道路";
            this.bandedGridColumn90.FieldName = "Strroad";
            this.bandedGridColumn90.Name = "bandedGridColumn90";
            this.bandedGridColumn90.Visible = true;
            // 
            // bandedGridColumn91
            // 
            this.bandedGridColumn91.Caption = "经度";
            this.bandedGridColumn91.CustomizationCaption = "经度";
            this.bandedGridColumn91.FieldName = "Imlongitude";
            this.bandedGridColumn91.Name = "bandedGridColumn91";
            this.bandedGridColumn91.Visible = true;
            // 
            // bandedGridColumn92
            // 
            this.bandedGridColumn92.Caption = "纬度";
            this.bandedGridColumn92.CustomizationCaption = "纬度";
            this.bandedGridColumn92.FieldName = "Imlatitude";
            this.bandedGridColumn92.Name = "bandedGridColumn92";
            this.bandedGridColumn92.Visible = true;
            // 
            // bandedGridColumn93
            // 
            this.bandedGridColumn93.Caption = "ifileid";
            this.bandedGridColumn93.FieldName = "Ifileid";
            this.bandedGridColumn93.Name = "bandedGridColumn93";
            // 
            // bandedGridColumn94
            // 
            this.bandedGridColumn94.Caption = "istime";
            this.bandedGridColumn94.FieldName = "Istime";
            this.bandedGridColumn94.Name = "bandedGridColumn94";
            // 
            // bandedGridColumn95
            // 
            this.bandedGridColumn95.Caption = "ietime";
            this.bandedGridColumn95.FieldName = "Ietime";
            this.bandedGridColumn95.Name = "bandedGridColumn95";
            // 
            // bandedGridColumn96
            // 
            this.bandedGridColumn96.Caption = "gridColumn195";
            this.bandedGridColumn96.FieldName = "Iid";
            this.bandedGridColumn96.Name = "bandedGridColumn96";
            // 
            // gridBand3
            // 
            this.gridBand3.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand3.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand3.Caption = "基础信息";
            this.gridBand3.Columns.Add(this.bandedGridColumn89);
            this.gridBand3.Columns.Add(this.bandedGridColumn90);
            this.gridBand3.Columns.Add(this.bandedGridColumn86);
            this.gridBand3.Columns.Add(this.bandedGridColumn87);
            this.gridBand3.Columns.Add(this.bandedGridColumn88);
            this.gridBand3.Columns.Add(this.bandedGridColumn91);
            this.gridBand3.Columns.Add(this.bandedGridColumn92);
            this.gridBand3.MinWidth = 20;
            this.gridBand3.Name = "gridBand3";
            this.gridBand3.Width = 555;
            // 
            // gridBand19
            // 
            this.gridBand19.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand19.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand19.Caption = "测试信息";
            this.gridBand19.Columns.Add(this.bandedGridColumn65);
            this.gridBand19.Columns.Add(this.bandedGridColumn66);
            this.gridBand19.Columns.Add(this.bandedGridColumn93);
            this.gridBand19.Columns.Add(this.bandedGridColumn94);
            this.gridBand19.Columns.Add(this.bandedGridColumn95);
            this.gridBand19.Columns.Add(this.bandedGridColumn96);
            this.gridBand19.MinWidth = 20;
            this.gridBand19.Name = "gridBand19";
            this.gridBand19.Width = 150;
            // 
            // gridBand20
            // 
            this.gridBand20.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand20.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand20.Caption = "速率";
            this.gridBand20.Columns.Add(this.bandedGridColumn67);
            this.gridBand20.Columns.Add(this.bandedGridColumn68);
            this.gridBand20.Columns.Add(this.bandedGridColumn69);
            this.gridBand20.Columns.Add(this.bandedGridColumn70);
            this.gridBand20.Columns.Add(this.bandedGridColumn71);
            this.gridBand20.Columns.Add(this.bandedGridColumn72);
            this.gridBand20.MinWidth = 20;
            this.gridBand20.Name = "gridBand20";
            this.gridBand20.Width = 450;
            // 
            // gridBand21
            // 
            this.gridBand21.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand21.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand21.Caption = "误码";
            this.gridBand21.Columns.Add(this.bandedGridColumn73);
            this.gridBand21.Columns.Add(this.bandedGridColumn74);
            this.gridBand21.Columns.Add(this.bandedGridColumn75);
            this.gridBand21.MinWidth = 20;
            this.gridBand21.Name = "gridBand21";
            this.gridBand21.Width = 225;
            // 
            // gridBand22
            // 
            this.gridBand22.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand22.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand22.Caption = "信号质量";
            this.gridBand22.Columns.Add(this.bandedGridColumn76);
            this.gridBand22.Columns.Add(this.bandedGridColumn77);
            this.gridBand22.Columns.Add(this.bandedGridColumn78);
            this.gridBand22.Columns.Add(this.bandedGridColumn79);
            this.gridBand22.MinWidth = 20;
            this.gridBand22.Name = "gridBand22";
            this.gridBand22.Width = 300;
            // 
            // gridBand23
            // 
            this.gridBand23.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand23.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand23.Caption = "TBF";
            this.gridBand23.Columns.Add(this.bandedGridColumn80);
            this.gridBand23.Columns.Add(this.bandedGridColumn81);
            this.gridBand23.MinWidth = 20;
            this.gridBand23.Name = "gridBand23";
            this.gridBand23.Width = 150;
            // 
            // gridBand24
            // 
            this.gridBand24.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand24.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand24.Caption = "时隙";
            this.gridBand24.Columns.Add(this.bandedGridColumn82);
            this.gridBand24.Columns.Add(this.bandedGridColumn83);
            this.gridBand24.Columns.Add(this.bandedGridColumn84);
            this.gridBand24.Columns.Add(this.bandedGridColumn85);
            this.gridBand24.MinWidth = 20;
            this.gridBand24.Name = "gridBand24";
            this.gridBand24.Width = 300;
            // 
            // bandedGridColumn97
            // 
            this.bandedGridColumn97.Caption = "距离(米)";
            this.bandedGridColumn97.CustomizationCaption = "距离(米)";
            this.bandedGridColumn97.FieldName = "Idistance";
            this.bandedGridColumn97.Name = "bandedGridColumn97";
            this.bandedGridColumn97.Visible = true;
            // 
            // bandedGridColumn98
            // 
            this.bandedGridColumn98.Caption = "时长(秒)";
            this.bandedGridColumn98.CustomizationCaption = "时长(秒)";
            this.bandedGridColumn98.FieldName = "Iduration";
            this.bandedGridColumn98.Name = "bandedGridColumn98";
            this.bandedGridColumn98.Visible = true;
            // 
            // bandedGridColumn99
            // 
            this.bandedGridColumn99.Caption = "0";
            this.bandedGridColumn99.CustomizationCaption = "速率0";
            this.bandedGridColumn99.FieldName = "APP_Speed0";
            this.bandedGridColumn99.Name = "bandedGridColumn99";
            this.bandedGridColumn99.Visible = true;
            // 
            // bandedGridColumn100
            // 
            this.bandedGridColumn100.Caption = "[0,40]";
            this.bandedGridColumn100.CustomizationCaption = "速率[0,40]";
            this.bandedGridColumn100.FieldName = "APP_Speed0_40";
            this.bandedGridColumn100.Name = "bandedGridColumn100";
            this.bandedGridColumn100.Visible = true;
            // 
            // bandedGridColumn101
            // 
            this.bandedGridColumn101.Caption = "[40,70]";
            this.bandedGridColumn101.CustomizationCaption = "速率[40,70]";
            this.bandedGridColumn101.FieldName = "APP_Speed40_70";
            this.bandedGridColumn101.Name = "bandedGridColumn101";
            this.bandedGridColumn101.Visible = true;
            // 
            // bandedGridColumn102
            // 
            this.bandedGridColumn102.Caption = "[70,90]";
            this.bandedGridColumn102.CustomizationCaption = "速率[70,90]";
            this.bandedGridColumn102.FieldName = "APP_Speed70_90";
            this.bandedGridColumn102.Name = "bandedGridColumn102";
            this.bandedGridColumn102.Visible = true;
            // 
            // bandedGridColumn103
            // 
            this.bandedGridColumn103.Caption = "[90,110]";
            this.bandedGridColumn103.CustomizationCaption = "速率[90,110]";
            this.bandedGridColumn103.FieldName = "APP_Speed90_110";
            this.bandedGridColumn103.Name = "bandedGridColumn103";
            this.bandedGridColumn103.Visible = true;
            // 
            // bandedGridColumn104
            // 
            this.bandedGridColumn104.Caption = "110";
            this.bandedGridColumn104.CustomizationCaption = "速率110";
            this.bandedGridColumn104.FieldName = "APP_Speed110";
            this.bandedGridColumn104.Name = "bandedGridColumn104";
            this.bandedGridColumn104.Visible = true;
            // 
            // bandedGridColumn105
            // 
            this.bandedGridColumn105.Caption = "[0,20]";
            this.bandedGridColumn105.CustomizationCaption = "误码[0,20]";
            this.bandedGridColumn105.FieldName = "BLER0_20";
            this.bandedGridColumn105.Name = "bandedGridColumn105";
            this.bandedGridColumn105.Visible = true;
            // 
            // bandedGridColumn106
            // 
            this.bandedGridColumn106.Caption = "[20,50]";
            this.bandedGridColumn106.CustomizationCaption = "误码[20,50]";
            this.bandedGridColumn106.FieldName = "BLER20_50";
            this.bandedGridColumn106.Name = "bandedGridColumn106";
            this.bandedGridColumn106.Visible = true;
            // 
            // bandedGridColumn107
            // 
            this.bandedGridColumn107.Caption = "[50,100]";
            this.bandedGridColumn107.CustomizationCaption = "误码[50,100]";
            this.bandedGridColumn107.FieldName = "BLER50_100";
            this.bandedGridColumn107.Name = "bandedGridColumn107";
            this.bandedGridColumn107.Visible = true;
            // 
            // bandedGridColumn108
            // 
            this.bandedGridColumn108.Caption = "[1,10]";
            this.bandedGridColumn108.CustomizationCaption = "信号质量[1,10]";
            this.bandedGridColumn108.FieldName = "BEP_Mean1_10";
            this.bandedGridColumn108.Name = "bandedGridColumn108";
            this.bandedGridColumn108.Visible = true;
            // 
            // bandedGridColumn109
            // 
            this.bandedGridColumn109.Caption = "[11,17]";
            this.bandedGridColumn109.CustomizationCaption = "信号质量[11,17]";
            this.bandedGridColumn109.FieldName = "BEP_Mean11_17";
            this.bandedGridColumn109.Name = "bandedGridColumn109";
            this.bandedGridColumn109.Visible = true;
            // 
            // bandedGridColumn110
            // 
            this.bandedGridColumn110.Caption = "[18,27]";
            this.bandedGridColumn110.CustomizationCaption = "信号质量[18,27]";
            this.bandedGridColumn110.FieldName = "BEP_Mean18_27";
            this.bandedGridColumn110.Name = "bandedGridColumn110";
            this.bandedGridColumn110.Visible = true;
            // 
            // bandedGridColumn111
            // 
            this.bandedGridColumn111.Caption = "[28,31]";
            this.bandedGridColumn111.CustomizationCaption = "信号质量[28,31]";
            this.bandedGridColumn111.FieldName = "BEP_Mean28_31";
            this.bandedGridColumn111.Name = "bandedGridColumn111";
            this.bandedGridColumn111.Visible = true;
            // 
            // bandedGridColumn112
            // 
            this.bandedGridColumn112.Caption = "打开状态";
            this.bandedGridColumn112.CustomizationCaption = "TBF打开状态";
            this.bandedGridColumn112.FieldName = "TBF_OPEN";
            this.bandedGridColumn112.Name = "bandedGridColumn112";
            this.bandedGridColumn112.Visible = true;
            // 
            // bandedGridColumn113
            // 
            this.bandedGridColumn113.Caption = "关闭状态";
            this.bandedGridColumn113.CustomizationCaption = "TBF关闭状态";
            this.bandedGridColumn113.FieldName = "TBF_CLOSE";
            this.bandedGridColumn113.Name = "bandedGridColumn113";
            this.bandedGridColumn113.Visible = true;
            // 
            // bandedGridColumn114
            // 
            this.bandedGridColumn114.Caption = "时隙1";
            this.bandedGridColumn114.CustomizationCaption = "时隙1";
            this.bandedGridColumn114.FieldName = "TimeSlot1";
            this.bandedGridColumn114.Name = "bandedGridColumn114";
            this.bandedGridColumn114.Visible = true;
            // 
            // bandedGridColumn115
            // 
            this.bandedGridColumn115.Caption = "时隙2";
            this.bandedGridColumn115.CustomizationCaption = "时隙2";
            this.bandedGridColumn115.FieldName = "TimeSlot2";
            this.bandedGridColumn115.Name = "bandedGridColumn115";
            this.bandedGridColumn115.Visible = true;
            // 
            // bandedGridColumn116
            // 
            this.bandedGridColumn116.Caption = "时隙3";
            this.bandedGridColumn116.CustomizationCaption = "时隙3";
            this.bandedGridColumn116.FieldName = "TimeSlot3";
            this.bandedGridColumn116.Name = "bandedGridColumn116";
            this.bandedGridColumn116.Visible = true;
            // 
            // bandedGridColumn117
            // 
            this.bandedGridColumn117.Caption = "时隙4";
            this.bandedGridColumn117.CustomizationCaption = "时隙4";
            this.bandedGridColumn117.FieldName = "TimeSlot4";
            this.bandedGridColumn117.Name = "bandedGridColumn117";
            this.bandedGridColumn117.Visible = true;
            // 
            // bandedGridColumn118
            // 
            this.bandedGridColumn118.Caption = "TOP1小区";
            this.bandedGridColumn118.CustomizationCaption = "TIO1小区";
            this.bandedGridColumn118.FieldName = "Cell1";
            this.bandedGridColumn118.Name = "bandedGridColumn118";
            this.bandedGridColumn118.Visible = true;
            this.bandedGridColumn118.Width = 85;
            // 
            // bandedGridColumn119
            // 
            this.bandedGridColumn119.Caption = "TOP2小区";
            this.bandedGridColumn119.CustomizationCaption = "TOP2小区";
            this.bandedGridColumn119.FieldName = "Cell2";
            this.bandedGridColumn119.Name = "bandedGridColumn119";
            this.bandedGridColumn119.Visible = true;
            this.bandedGridColumn119.Width = 85;
            // 
            // bandedGridColumn120
            // 
            this.bandedGridColumn120.Caption = "TOP3小区";
            this.bandedGridColumn120.CustomizationCaption = "TOP3小区";
            this.bandedGridColumn120.FieldName = "Cell3";
            this.bandedGridColumn120.Name = "bandedGridColumn120";
            this.bandedGridColumn120.Visible = true;
            this.bandedGridColumn120.Width = 85;
            // 
            // bandedGridColumn121
            // 
            this.bandedGridColumn121.Caption = "所属网格";
            this.bandedGridColumn121.CustomizationCaption = "所属网格";
            this.bandedGridColumn121.FieldName = "Strgrid";
            this.bandedGridColumn121.Name = "bandedGridColumn121";
            this.bandedGridColumn121.Visible = true;
            // 
            // bandedGridColumn122
            // 
            this.bandedGridColumn122.Caption = "所属道路";
            this.bandedGridColumn122.CustomizationCaption = "所属道路";
            this.bandedGridColumn122.FieldName = "Strroad";
            this.bandedGridColumn122.Name = "bandedGridColumn122";
            this.bandedGridColumn122.Visible = true;
            // 
            // bandedGridColumn123
            // 
            this.bandedGridColumn123.Caption = "经度";
            this.bandedGridColumn123.CustomizationCaption = "经度";
            this.bandedGridColumn123.FieldName = "Imlongitude";
            this.bandedGridColumn123.Name = "bandedGridColumn123";
            this.bandedGridColumn123.Visible = true;
            // 
            // bandedGridColumn124
            // 
            this.bandedGridColumn124.Caption = "纬度";
            this.bandedGridColumn124.CustomizationCaption = "纬度";
            this.bandedGridColumn124.FieldName = "Imlatitude";
            this.bandedGridColumn124.Name = "bandedGridColumn124";
            this.bandedGridColumn124.Visible = true;
            // 
            // bandedGridColumn125
            // 
            this.bandedGridColumn125.Caption = "ifileid";
            this.bandedGridColumn125.FieldName = "Ifileid";
            this.bandedGridColumn125.Name = "bandedGridColumn125";
            // 
            // bandedGridColumn126
            // 
            this.bandedGridColumn126.Caption = "istime";
            this.bandedGridColumn126.FieldName = "Istime";
            this.bandedGridColumn126.Name = "bandedGridColumn126";
            // 
            // bandedGridColumn127
            // 
            this.bandedGridColumn127.Caption = "ietime";
            this.bandedGridColumn127.FieldName = "Ietime";
            this.bandedGridColumn127.Name = "bandedGridColumn127";
            // 
            // bandedGridColumn128
            // 
            this.bandedGridColumn128.Caption = "gridColumn195";
            this.bandedGridColumn128.FieldName = "Iid";
            this.bandedGridColumn128.Name = "bandedGridColumn128";
            // 
            // gridBand4
            // 
            this.gridBand4.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand4.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand4.Caption = "基础信息";
            this.gridBand4.Columns.Add(this.bandedGridColumn121);
            this.gridBand4.Columns.Add(this.bandedGridColumn122);
            this.gridBand4.Columns.Add(this.bandedGridColumn118);
            this.gridBand4.Columns.Add(this.bandedGridColumn119);
            this.gridBand4.Columns.Add(this.bandedGridColumn120);
            this.gridBand4.Columns.Add(this.bandedGridColumn123);
            this.gridBand4.Columns.Add(this.bandedGridColumn124);
            this.gridBand4.MinWidth = 20;
            this.gridBand4.Name = "gridBand4";
            this.gridBand4.Width = 555;
            // 
            // gridBand25
            // 
            this.gridBand25.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand25.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand25.Caption = "测试信息";
            this.gridBand25.Columns.Add(this.bandedGridColumn97);
            this.gridBand25.Columns.Add(this.bandedGridColumn98);
            this.gridBand25.Columns.Add(this.bandedGridColumn125);
            this.gridBand25.Columns.Add(this.bandedGridColumn126);
            this.gridBand25.Columns.Add(this.bandedGridColumn127);
            this.gridBand25.Columns.Add(this.bandedGridColumn128);
            this.gridBand25.MinWidth = 20;
            this.gridBand25.Name = "gridBand25";
            this.gridBand25.Width = 150;
            // 
            // gridBand26
            // 
            this.gridBand26.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand26.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand26.Caption = "速率";
            this.gridBand26.Columns.Add(this.bandedGridColumn99);
            this.gridBand26.Columns.Add(this.bandedGridColumn100);
            this.gridBand26.Columns.Add(this.bandedGridColumn101);
            this.gridBand26.Columns.Add(this.bandedGridColumn102);
            this.gridBand26.Columns.Add(this.bandedGridColumn103);
            this.gridBand26.Columns.Add(this.bandedGridColumn104);
            this.gridBand26.MinWidth = 20;
            this.gridBand26.Name = "gridBand26";
            this.gridBand26.Width = 450;
            // 
            // gridBand27
            // 
            this.gridBand27.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand27.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand27.Caption = "误码";
            this.gridBand27.Columns.Add(this.bandedGridColumn105);
            this.gridBand27.Columns.Add(this.bandedGridColumn106);
            this.gridBand27.Columns.Add(this.bandedGridColumn107);
            this.gridBand27.MinWidth = 20;
            this.gridBand27.Name = "gridBand27";
            this.gridBand27.Width = 225;
            // 
            // gridBand28
            // 
            this.gridBand28.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand28.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand28.Caption = "信号质量";
            this.gridBand28.Columns.Add(this.bandedGridColumn108);
            this.gridBand28.Columns.Add(this.bandedGridColumn109);
            this.gridBand28.Columns.Add(this.bandedGridColumn110);
            this.gridBand28.Columns.Add(this.bandedGridColumn111);
            this.gridBand28.MinWidth = 20;
            this.gridBand28.Name = "gridBand28";
            this.gridBand28.Width = 300;
            // 
            // gridBand29
            // 
            this.gridBand29.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand29.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand29.Caption = "TBF";
            this.gridBand29.Columns.Add(this.bandedGridColumn112);
            this.gridBand29.Columns.Add(this.bandedGridColumn113);
            this.gridBand29.MinWidth = 20;
            this.gridBand29.Name = "gridBand29";
            this.gridBand29.Width = 150;
            // 
            // gridBand30
            // 
            this.gridBand30.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand30.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand30.Caption = "时隙";
            this.gridBand30.Columns.Add(this.bandedGridColumn114);
            this.gridBand30.Columns.Add(this.bandedGridColumn115);
            this.gridBand30.Columns.Add(this.bandedGridColumn116);
            this.gridBand30.Columns.Add(this.bandedGridColumn117);
            this.gridBand30.MinWidth = 20;
            this.gridBand30.Name = "gridBand30";
            this.gridBand30.Width = 300;
            // 
            // bandedGridColumn129
            // 
            this.bandedGridColumn129.Caption = "距离(米)";
            this.bandedGridColumn129.CustomizationCaption = "距离(米)";
            this.bandedGridColumn129.FieldName = "Idistance";
            this.bandedGridColumn129.Name = "bandedGridColumn129";
            this.bandedGridColumn129.Visible = true;
            // 
            // bandedGridColumn130
            // 
            this.bandedGridColumn130.Caption = "时长(秒)";
            this.bandedGridColumn130.CustomizationCaption = "时长(秒)";
            this.bandedGridColumn130.FieldName = "Iduration";
            this.bandedGridColumn130.Name = "bandedGridColumn130";
            this.bandedGridColumn130.Visible = true;
            // 
            // bandedGridColumn131
            // 
            this.bandedGridColumn131.Caption = "0";
            this.bandedGridColumn131.CustomizationCaption = "速率0";
            this.bandedGridColumn131.FieldName = "APP_Speed0";
            this.bandedGridColumn131.Name = "bandedGridColumn131";
            this.bandedGridColumn131.Visible = true;
            // 
            // bandedGridColumn132
            // 
            this.bandedGridColumn132.Caption = "[0,40]";
            this.bandedGridColumn132.CustomizationCaption = "速率[0,40]";
            this.bandedGridColumn132.FieldName = "APP_Speed0_40";
            this.bandedGridColumn132.Name = "bandedGridColumn132";
            this.bandedGridColumn132.Visible = true;
            // 
            // bandedGridColumn133
            // 
            this.bandedGridColumn133.Caption = "[40,70]";
            this.bandedGridColumn133.CustomizationCaption = "速率[40,70]";
            this.bandedGridColumn133.FieldName = "APP_Speed40_70";
            this.bandedGridColumn133.Name = "bandedGridColumn133";
            this.bandedGridColumn133.Visible = true;
            // 
            // bandedGridColumn134
            // 
            this.bandedGridColumn134.Caption = "[70,90]";
            this.bandedGridColumn134.CustomizationCaption = "速率[70,90]";
            this.bandedGridColumn134.FieldName = "APP_Speed70_90";
            this.bandedGridColumn134.Name = "bandedGridColumn134";
            this.bandedGridColumn134.Visible = true;
            // 
            // bandedGridColumn135
            // 
            this.bandedGridColumn135.Caption = "[90,110]";
            this.bandedGridColumn135.CustomizationCaption = "速率[90,110]";
            this.bandedGridColumn135.FieldName = "APP_Speed90_110";
            this.bandedGridColumn135.Name = "bandedGridColumn135";
            this.bandedGridColumn135.Visible = true;
            // 
            // bandedGridColumn136
            // 
            this.bandedGridColumn136.Caption = "110";
            this.bandedGridColumn136.CustomizationCaption = "速率110";
            this.bandedGridColumn136.FieldName = "APP_Speed110";
            this.bandedGridColumn136.Name = "bandedGridColumn136";
            this.bandedGridColumn136.Visible = true;
            // 
            // bandedGridColumn137
            // 
            this.bandedGridColumn137.Caption = "[0,20]";
            this.bandedGridColumn137.CustomizationCaption = "误码[0,20]";
            this.bandedGridColumn137.FieldName = "BLER0_20";
            this.bandedGridColumn137.Name = "bandedGridColumn137";
            this.bandedGridColumn137.Visible = true;
            // 
            // bandedGridColumn138
            // 
            this.bandedGridColumn138.Caption = "[20,50]";
            this.bandedGridColumn138.CustomizationCaption = "误码[20,50]";
            this.bandedGridColumn138.FieldName = "BLER20_50";
            this.bandedGridColumn138.Name = "bandedGridColumn138";
            this.bandedGridColumn138.Visible = true;
            // 
            // bandedGridColumn139
            // 
            this.bandedGridColumn139.Caption = "[50,100]";
            this.bandedGridColumn139.CustomizationCaption = "误码[50,100]";
            this.bandedGridColumn139.FieldName = "BLER50_100";
            this.bandedGridColumn139.Name = "bandedGridColumn139";
            this.bandedGridColumn139.Visible = true;
            // 
            // bandedGridColumn140
            // 
            this.bandedGridColumn140.Caption = "[1,10]";
            this.bandedGridColumn140.CustomizationCaption = "信号质量[1,10]";
            this.bandedGridColumn140.FieldName = "BEP_Mean1_10";
            this.bandedGridColumn140.Name = "bandedGridColumn140";
            this.bandedGridColumn140.Visible = true;
            // 
            // bandedGridColumn141
            // 
            this.bandedGridColumn141.Caption = "[11,17]";
            this.bandedGridColumn141.CustomizationCaption = "信号质量[11,17]";
            this.bandedGridColumn141.FieldName = "BEP_Mean11_17";
            this.bandedGridColumn141.Name = "bandedGridColumn141";
            this.bandedGridColumn141.Visible = true;
            // 
            // bandedGridColumn142
            // 
            this.bandedGridColumn142.Caption = "[18,27]";
            this.bandedGridColumn142.CustomizationCaption = "信号质量[18,27]";
            this.bandedGridColumn142.FieldName = "BEP_Mean18_27";
            this.bandedGridColumn142.Name = "bandedGridColumn142";
            this.bandedGridColumn142.Visible = true;
            // 
            // bandedGridColumn143
            // 
            this.bandedGridColumn143.Caption = "[28,31]";
            this.bandedGridColumn143.CustomizationCaption = "信号质量[28,31]";
            this.bandedGridColumn143.FieldName = "BEP_Mean28_31";
            this.bandedGridColumn143.Name = "bandedGridColumn143";
            this.bandedGridColumn143.Visible = true;
            // 
            // bandedGridColumn144
            // 
            this.bandedGridColumn144.Caption = "打开状态";
            this.bandedGridColumn144.CustomizationCaption = "TBF打开状态";
            this.bandedGridColumn144.FieldName = "TBF_OPEN";
            this.bandedGridColumn144.Name = "bandedGridColumn144";
            this.bandedGridColumn144.Visible = true;
            // 
            // bandedGridColumn145
            // 
            this.bandedGridColumn145.Caption = "关闭状态";
            this.bandedGridColumn145.CustomizationCaption = "TBF关闭状态";
            this.bandedGridColumn145.FieldName = "TBF_CLOSE";
            this.bandedGridColumn145.Name = "bandedGridColumn145";
            this.bandedGridColumn145.Visible = true;
            // 
            // bandedGridColumn146
            // 
            this.bandedGridColumn146.Caption = "时隙1";
            this.bandedGridColumn146.CustomizationCaption = "时隙1";
            this.bandedGridColumn146.FieldName = "TimeSlot1";
            this.bandedGridColumn146.Name = "bandedGridColumn146";
            this.bandedGridColumn146.Visible = true;
            // 
            // bandedGridColumn147
            // 
            this.bandedGridColumn147.Caption = "时隙2";
            this.bandedGridColumn147.CustomizationCaption = "时隙2";
            this.bandedGridColumn147.FieldName = "TimeSlot2";
            this.bandedGridColumn147.Name = "bandedGridColumn147";
            this.bandedGridColumn147.Visible = true;
            // 
            // bandedGridColumn148
            // 
            this.bandedGridColumn148.Caption = "时隙3";
            this.bandedGridColumn148.CustomizationCaption = "时隙3";
            this.bandedGridColumn148.FieldName = "TimeSlot3";
            this.bandedGridColumn148.Name = "bandedGridColumn148";
            this.bandedGridColumn148.Visible = true;
            // 
            // bandedGridColumn149
            // 
            this.bandedGridColumn149.Caption = "时隙4";
            this.bandedGridColumn149.CustomizationCaption = "时隙4";
            this.bandedGridColumn149.FieldName = "TimeSlot4";
            this.bandedGridColumn149.Name = "bandedGridColumn149";
            this.bandedGridColumn149.Visible = true;
            // 
            // bandedGridColumn150
            // 
            this.bandedGridColumn150.Caption = "TOP1小区";
            this.bandedGridColumn150.CustomizationCaption = "TIO1小区";
            this.bandedGridColumn150.FieldName = "Cell1";
            this.bandedGridColumn150.Name = "bandedGridColumn150";
            this.bandedGridColumn150.Visible = true;
            this.bandedGridColumn150.Width = 85;
            // 
            // bandedGridColumn151
            // 
            this.bandedGridColumn151.Caption = "TOP2小区";
            this.bandedGridColumn151.CustomizationCaption = "TOP2小区";
            this.bandedGridColumn151.FieldName = "Cell2";
            this.bandedGridColumn151.Name = "bandedGridColumn151";
            this.bandedGridColumn151.Visible = true;
            this.bandedGridColumn151.Width = 85;
            // 
            // bandedGridColumn152
            // 
            this.bandedGridColumn152.Caption = "TOP3小区";
            this.bandedGridColumn152.CustomizationCaption = "TOP3小区";
            this.bandedGridColumn152.FieldName = "Cell3";
            this.bandedGridColumn152.Name = "bandedGridColumn152";
            this.bandedGridColumn152.Visible = true;
            this.bandedGridColumn152.Width = 85;
            // 
            // bandedGridColumn153
            // 
            this.bandedGridColumn153.Caption = "所属网格";
            this.bandedGridColumn153.CustomizationCaption = "所属网格";
            this.bandedGridColumn153.FieldName = "Strgrid";
            this.bandedGridColumn153.Name = "bandedGridColumn153";
            this.bandedGridColumn153.Visible = true;
            // 
            // bandedGridColumn154
            // 
            this.bandedGridColumn154.Caption = "所属道路";
            this.bandedGridColumn154.CustomizationCaption = "所属道路";
            this.bandedGridColumn154.FieldName = "Strroad";
            this.bandedGridColumn154.Name = "bandedGridColumn154";
            this.bandedGridColumn154.Visible = true;
            // 
            // bandedGridColumn155
            // 
            this.bandedGridColumn155.Caption = "经度";
            this.bandedGridColumn155.CustomizationCaption = "经度";
            this.bandedGridColumn155.FieldName = "Imlongitude";
            this.bandedGridColumn155.Name = "bandedGridColumn155";
            this.bandedGridColumn155.Visible = true;
            // 
            // bandedGridColumn156
            // 
            this.bandedGridColumn156.Caption = "纬度";
            this.bandedGridColumn156.CustomizationCaption = "纬度";
            this.bandedGridColumn156.FieldName = "Imlatitude";
            this.bandedGridColumn156.Name = "bandedGridColumn156";
            this.bandedGridColumn156.Visible = true;
            // 
            // bandedGridColumn157
            // 
            this.bandedGridColumn157.Caption = "ifileid";
            this.bandedGridColumn157.FieldName = "Ifileid";
            this.bandedGridColumn157.Name = "bandedGridColumn157";
            // 
            // bandedGridColumn158
            // 
            this.bandedGridColumn158.Caption = "istime";
            this.bandedGridColumn158.FieldName = "Istime";
            this.bandedGridColumn158.Name = "bandedGridColumn158";
            // 
            // bandedGridColumn159
            // 
            this.bandedGridColumn159.Caption = "ietime";
            this.bandedGridColumn159.FieldName = "Ietime";
            this.bandedGridColumn159.Name = "bandedGridColumn159";
            // 
            // bandedGridColumn160
            // 
            this.bandedGridColumn160.Caption = "gridColumn195";
            this.bandedGridColumn160.FieldName = "Iid";
            this.bandedGridColumn160.Name = "bandedGridColumn160";
            // 
            // gridBand5
            // 
            this.gridBand5.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand5.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand5.Caption = "基础信息";
            this.gridBand5.Columns.Add(this.bandedGridColumn153);
            this.gridBand5.Columns.Add(this.bandedGridColumn154);
            this.gridBand5.Columns.Add(this.bandedGridColumn150);
            this.gridBand5.Columns.Add(this.bandedGridColumn151);
            this.gridBand5.Columns.Add(this.bandedGridColumn152);
            this.gridBand5.Columns.Add(this.bandedGridColumn155);
            this.gridBand5.Columns.Add(this.bandedGridColumn156);
            this.gridBand5.MinWidth = 20;
            this.gridBand5.Name = "gridBand5";
            this.gridBand5.Width = 555;
            // 
            // gridBand31
            // 
            this.gridBand31.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand31.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand31.Caption = "测试信息";
            this.gridBand31.Columns.Add(this.bandedGridColumn129);
            this.gridBand31.Columns.Add(this.bandedGridColumn130);
            this.gridBand31.Columns.Add(this.bandedGridColumn157);
            this.gridBand31.Columns.Add(this.bandedGridColumn158);
            this.gridBand31.Columns.Add(this.bandedGridColumn159);
            this.gridBand31.Columns.Add(this.bandedGridColumn160);
            this.gridBand31.MinWidth = 20;
            this.gridBand31.Name = "gridBand31";
            this.gridBand31.Width = 150;
            // 
            // gridBand32
            // 
            this.gridBand32.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand32.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand32.Caption = "速率";
            this.gridBand32.Columns.Add(this.bandedGridColumn131);
            this.gridBand32.Columns.Add(this.bandedGridColumn132);
            this.gridBand32.Columns.Add(this.bandedGridColumn133);
            this.gridBand32.Columns.Add(this.bandedGridColumn134);
            this.gridBand32.Columns.Add(this.bandedGridColumn135);
            this.gridBand32.Columns.Add(this.bandedGridColumn136);
            this.gridBand32.MinWidth = 20;
            this.gridBand32.Name = "gridBand32";
            this.gridBand32.Width = 450;
            // 
            // gridBand33
            // 
            this.gridBand33.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand33.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand33.Caption = "误码";
            this.gridBand33.Columns.Add(this.bandedGridColumn137);
            this.gridBand33.Columns.Add(this.bandedGridColumn138);
            this.gridBand33.Columns.Add(this.bandedGridColumn139);
            this.gridBand33.MinWidth = 20;
            this.gridBand33.Name = "gridBand33";
            this.gridBand33.Width = 225;
            // 
            // gridBand34
            // 
            this.gridBand34.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand34.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand34.Caption = "信号质量";
            this.gridBand34.Columns.Add(this.bandedGridColumn140);
            this.gridBand34.Columns.Add(this.bandedGridColumn141);
            this.gridBand34.Columns.Add(this.bandedGridColumn142);
            this.gridBand34.Columns.Add(this.bandedGridColumn143);
            this.gridBand34.MinWidth = 20;
            this.gridBand34.Name = "gridBand34";
            this.gridBand34.Width = 300;
            // 
            // gridBand35
            // 
            this.gridBand35.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand35.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand35.Caption = "TBF";
            this.gridBand35.Columns.Add(this.bandedGridColumn144);
            this.gridBand35.Columns.Add(this.bandedGridColumn145);
            this.gridBand35.MinWidth = 20;
            this.gridBand35.Name = "gridBand35";
            this.gridBand35.Width = 150;
            // 
            // gridBand36
            // 
            this.gridBand36.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand36.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand36.Caption = "时隙";
            this.gridBand36.Columns.Add(this.bandedGridColumn146);
            this.gridBand36.Columns.Add(this.bandedGridColumn147);
            this.gridBand36.Columns.Add(this.bandedGridColumn148);
            this.gridBand36.Columns.Add(this.bandedGridColumn149);
            this.gridBand36.MinWidth = 20;
            this.gridBand36.Name = "gridBand36";
            this.gridBand36.Width = 300;
            // 
            // bandedGridColumn161
            // 
            this.bandedGridColumn161.Caption = "距离(米)";
            this.bandedGridColumn161.CustomizationCaption = "距离(米)";
            this.bandedGridColumn161.FieldName = "Idistance";
            this.bandedGridColumn161.Name = "bandedGridColumn161";
            this.bandedGridColumn161.Visible = true;
            // 
            // bandedGridColumn162
            // 
            this.bandedGridColumn162.Caption = "时长(秒)";
            this.bandedGridColumn162.CustomizationCaption = "时长(秒)";
            this.bandedGridColumn162.FieldName = "Iduration";
            this.bandedGridColumn162.Name = "bandedGridColumn162";
            this.bandedGridColumn162.Visible = true;
            // 
            // bandedGridColumn163
            // 
            this.bandedGridColumn163.Caption = "0";
            this.bandedGridColumn163.CustomizationCaption = "速率0";
            this.bandedGridColumn163.FieldName = "APP_Speed0";
            this.bandedGridColumn163.Name = "bandedGridColumn163";
            this.bandedGridColumn163.Visible = true;
            // 
            // bandedGridColumn164
            // 
            this.bandedGridColumn164.Caption = "[0,40]";
            this.bandedGridColumn164.CustomizationCaption = "速率[0,40]";
            this.bandedGridColumn164.FieldName = "APP_Speed0_40";
            this.bandedGridColumn164.Name = "bandedGridColumn164";
            this.bandedGridColumn164.Visible = true;
            // 
            // bandedGridColumn165
            // 
            this.bandedGridColumn165.Caption = "[40,70]";
            this.bandedGridColumn165.CustomizationCaption = "速率[40,70]";
            this.bandedGridColumn165.FieldName = "APP_Speed40_70";
            this.bandedGridColumn165.Name = "bandedGridColumn165";
            this.bandedGridColumn165.Visible = true;
            // 
            // bandedGridColumn166
            // 
            this.bandedGridColumn166.Caption = "[70,90]";
            this.bandedGridColumn166.CustomizationCaption = "速率[70,90]";
            this.bandedGridColumn166.FieldName = "APP_Speed70_90";
            this.bandedGridColumn166.Name = "bandedGridColumn166";
            this.bandedGridColumn166.Visible = true;
            // 
            // bandedGridColumn167
            // 
            this.bandedGridColumn167.Caption = "[90,110]";
            this.bandedGridColumn167.CustomizationCaption = "速率[90,110]";
            this.bandedGridColumn167.FieldName = "APP_Speed90_110";
            this.bandedGridColumn167.Name = "bandedGridColumn167";
            this.bandedGridColumn167.Visible = true;
            // 
            // bandedGridColumn168
            // 
            this.bandedGridColumn168.Caption = "110";
            this.bandedGridColumn168.CustomizationCaption = "速率110";
            this.bandedGridColumn168.FieldName = "APP_Speed110";
            this.bandedGridColumn168.Name = "bandedGridColumn168";
            this.bandedGridColumn168.Visible = true;
            // 
            // bandedGridColumn169
            // 
            this.bandedGridColumn169.Caption = "[0,20]";
            this.bandedGridColumn169.CustomizationCaption = "误码[0,20]";
            this.bandedGridColumn169.FieldName = "BLER0_20";
            this.bandedGridColumn169.Name = "bandedGridColumn169";
            this.bandedGridColumn169.Visible = true;
            // 
            // bandedGridColumn170
            // 
            this.bandedGridColumn170.Caption = "[20,50]";
            this.bandedGridColumn170.CustomizationCaption = "误码[20,50]";
            this.bandedGridColumn170.FieldName = "BLER20_50";
            this.bandedGridColumn170.Name = "bandedGridColumn170";
            this.bandedGridColumn170.Visible = true;
            // 
            // bandedGridColumn171
            // 
            this.bandedGridColumn171.Caption = "[50,100]";
            this.bandedGridColumn171.CustomizationCaption = "误码[50,100]";
            this.bandedGridColumn171.FieldName = "BLER50_100";
            this.bandedGridColumn171.Name = "bandedGridColumn171";
            this.bandedGridColumn171.Visible = true;
            // 
            // bandedGridColumn172
            // 
            this.bandedGridColumn172.Caption = "[1,10]";
            this.bandedGridColumn172.CustomizationCaption = "信号质量[1,10]";
            this.bandedGridColumn172.FieldName = "BEP_Mean1_10";
            this.bandedGridColumn172.Name = "bandedGridColumn172";
            this.bandedGridColumn172.Visible = true;
            // 
            // bandedGridColumn173
            // 
            this.bandedGridColumn173.Caption = "[11,17]";
            this.bandedGridColumn173.CustomizationCaption = "信号质量[11,17]";
            this.bandedGridColumn173.FieldName = "BEP_Mean11_17";
            this.bandedGridColumn173.Name = "bandedGridColumn173";
            this.bandedGridColumn173.Visible = true;
            // 
            // bandedGridColumn174
            // 
            this.bandedGridColumn174.Caption = "[18,27]";
            this.bandedGridColumn174.CustomizationCaption = "信号质量[18,27]";
            this.bandedGridColumn174.FieldName = "BEP_Mean18_27";
            this.bandedGridColumn174.Name = "bandedGridColumn174";
            this.bandedGridColumn174.Visible = true;
            // 
            // bandedGridColumn175
            // 
            this.bandedGridColumn175.Caption = "[28,31]";
            this.bandedGridColumn175.CustomizationCaption = "信号质量[28,31]";
            this.bandedGridColumn175.FieldName = "BEP_Mean28_31";
            this.bandedGridColumn175.Name = "bandedGridColumn175";
            this.bandedGridColumn175.Visible = true;
            // 
            // bandedGridColumn176
            // 
            this.bandedGridColumn176.Caption = "打开状态";
            this.bandedGridColumn176.CustomizationCaption = "TBF打开状态";
            this.bandedGridColumn176.FieldName = "TBF_OPEN";
            this.bandedGridColumn176.Name = "bandedGridColumn176";
            this.bandedGridColumn176.Visible = true;
            // 
            // bandedGridColumn177
            // 
            this.bandedGridColumn177.Caption = "关闭状态";
            this.bandedGridColumn177.CustomizationCaption = "TBF关闭状态";
            this.bandedGridColumn177.FieldName = "TBF_CLOSE";
            this.bandedGridColumn177.Name = "bandedGridColumn177";
            this.bandedGridColumn177.Visible = true;
            // 
            // bandedGridColumn178
            // 
            this.bandedGridColumn178.Caption = "时隙1";
            this.bandedGridColumn178.CustomizationCaption = "时隙1";
            this.bandedGridColumn178.FieldName = "TimeSlot1";
            this.bandedGridColumn178.Name = "bandedGridColumn178";
            this.bandedGridColumn178.Visible = true;
            // 
            // bandedGridColumn179
            // 
            this.bandedGridColumn179.Caption = "时隙2";
            this.bandedGridColumn179.CustomizationCaption = "时隙2";
            this.bandedGridColumn179.FieldName = "TimeSlot2";
            this.bandedGridColumn179.Name = "bandedGridColumn179";
            this.bandedGridColumn179.Visible = true;
            // 
            // bandedGridColumn180
            // 
            this.bandedGridColumn180.Caption = "时隙3";
            this.bandedGridColumn180.CustomizationCaption = "时隙3";
            this.bandedGridColumn180.FieldName = "TimeSlot3";
            this.bandedGridColumn180.Name = "bandedGridColumn180";
            this.bandedGridColumn180.Visible = true;
            // 
            // bandedGridColumn181
            // 
            this.bandedGridColumn181.Caption = "时隙4";
            this.bandedGridColumn181.CustomizationCaption = "时隙4";
            this.bandedGridColumn181.FieldName = "TimeSlot4";
            this.bandedGridColumn181.Name = "bandedGridColumn181";
            this.bandedGridColumn181.Visible = true;
            // 
            // bandedGridColumn182
            // 
            this.bandedGridColumn182.Caption = "TOP1小区";
            this.bandedGridColumn182.CustomizationCaption = "TIO1小区";
            this.bandedGridColumn182.FieldName = "Cell1";
            this.bandedGridColumn182.Name = "bandedGridColumn182";
            this.bandedGridColumn182.Visible = true;
            this.bandedGridColumn182.Width = 85;
            // 
            // bandedGridColumn183
            // 
            this.bandedGridColumn183.Caption = "TOP2小区";
            this.bandedGridColumn183.CustomizationCaption = "TOP2小区";
            this.bandedGridColumn183.FieldName = "Cell2";
            this.bandedGridColumn183.Name = "bandedGridColumn183";
            this.bandedGridColumn183.Visible = true;
            this.bandedGridColumn183.Width = 85;
            // 
            // bandedGridColumn184
            // 
            this.bandedGridColumn184.Caption = "TOP3小区";
            this.bandedGridColumn184.CustomizationCaption = "TOP3小区";
            this.bandedGridColumn184.FieldName = "Cell3";
            this.bandedGridColumn184.Name = "bandedGridColumn184";
            this.bandedGridColumn184.Visible = true;
            this.bandedGridColumn184.Width = 85;
            // 
            // bandedGridColumn185
            // 
            this.bandedGridColumn185.Caption = "所属网格";
            this.bandedGridColumn185.CustomizationCaption = "所属网格";
            this.bandedGridColumn185.FieldName = "Strgrid";
            this.bandedGridColumn185.Name = "bandedGridColumn185";
            this.bandedGridColumn185.Visible = true;
            // 
            // bandedGridColumn186
            // 
            this.bandedGridColumn186.Caption = "所属道路";
            this.bandedGridColumn186.CustomizationCaption = "所属道路";
            this.bandedGridColumn186.FieldName = "Strroad";
            this.bandedGridColumn186.Name = "bandedGridColumn186";
            this.bandedGridColumn186.Visible = true;
            // 
            // bandedGridColumn187
            // 
            this.bandedGridColumn187.Caption = "经度";
            this.bandedGridColumn187.CustomizationCaption = "经度";
            this.bandedGridColumn187.FieldName = "Imlongitude";
            this.bandedGridColumn187.Name = "bandedGridColumn187";
            this.bandedGridColumn187.Visible = true;
            // 
            // bandedGridColumn188
            // 
            this.bandedGridColumn188.Caption = "纬度";
            this.bandedGridColumn188.CustomizationCaption = "纬度";
            this.bandedGridColumn188.FieldName = "Imlatitude";
            this.bandedGridColumn188.Name = "bandedGridColumn188";
            this.bandedGridColumn188.Visible = true;
            // 
            // bandedGridColumn189
            // 
            this.bandedGridColumn189.Caption = "ifileid";
            this.bandedGridColumn189.FieldName = "Ifileid";
            this.bandedGridColumn189.Name = "bandedGridColumn189";
            // 
            // bandedGridColumn190
            // 
            this.bandedGridColumn190.Caption = "istime";
            this.bandedGridColumn190.FieldName = "Istime";
            this.bandedGridColumn190.Name = "bandedGridColumn190";
            // 
            // bandedGridColumn191
            // 
            this.bandedGridColumn191.Caption = "ietime";
            this.bandedGridColumn191.FieldName = "Ietime";
            this.bandedGridColumn191.Name = "bandedGridColumn191";
            // 
            // bandedGridColumn192
            // 
            this.bandedGridColumn192.Caption = "gridColumn195";
            this.bandedGridColumn192.FieldName = "Iid";
            this.bandedGridColumn192.Name = "bandedGridColumn192";
            // 
            // gridBand6
            // 
            this.gridBand6.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand6.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand6.Caption = "基础信息";
            this.gridBand6.Columns.Add(this.bandedGridColumn185);
            this.gridBand6.Columns.Add(this.bandedGridColumn186);
            this.gridBand6.Columns.Add(this.bandedGridColumn182);
            this.gridBand6.Columns.Add(this.bandedGridColumn183);
            this.gridBand6.Columns.Add(this.bandedGridColumn184);
            this.gridBand6.Columns.Add(this.bandedGridColumn187);
            this.gridBand6.Columns.Add(this.bandedGridColumn188);
            this.gridBand6.MinWidth = 20;
            this.gridBand6.Name = "gridBand6";
            this.gridBand6.Width = 555;
            // 
            // gridBand37
            // 
            this.gridBand37.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand37.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand37.Caption = "测试信息";
            this.gridBand37.Columns.Add(this.bandedGridColumn161);
            this.gridBand37.Columns.Add(this.bandedGridColumn162);
            this.gridBand37.Columns.Add(this.bandedGridColumn189);
            this.gridBand37.Columns.Add(this.bandedGridColumn190);
            this.gridBand37.Columns.Add(this.bandedGridColumn191);
            this.gridBand37.Columns.Add(this.bandedGridColumn192);
            this.gridBand37.MinWidth = 20;
            this.gridBand37.Name = "gridBand37";
            this.gridBand37.Width = 150;
            // 
            // gridBand38
            // 
            this.gridBand38.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand38.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand38.Caption = "速率";
            this.gridBand38.Columns.Add(this.bandedGridColumn163);
            this.gridBand38.Columns.Add(this.bandedGridColumn164);
            this.gridBand38.Columns.Add(this.bandedGridColumn165);
            this.gridBand38.Columns.Add(this.bandedGridColumn166);
            this.gridBand38.Columns.Add(this.bandedGridColumn167);
            this.gridBand38.Columns.Add(this.bandedGridColumn168);
            this.gridBand38.MinWidth = 20;
            this.gridBand38.Name = "gridBand38";
            this.gridBand38.Width = 450;
            // 
            // gridBand39
            // 
            this.gridBand39.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand39.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand39.Caption = "误码";
            this.gridBand39.Columns.Add(this.bandedGridColumn169);
            this.gridBand39.Columns.Add(this.bandedGridColumn170);
            this.gridBand39.Columns.Add(this.bandedGridColumn171);
            this.gridBand39.MinWidth = 20;
            this.gridBand39.Name = "gridBand39";
            this.gridBand39.Width = 225;
            // 
            // gridBand40
            // 
            this.gridBand40.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand40.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand40.Caption = "信号质量";
            this.gridBand40.Columns.Add(this.bandedGridColumn172);
            this.gridBand40.Columns.Add(this.bandedGridColumn173);
            this.gridBand40.Columns.Add(this.bandedGridColumn174);
            this.gridBand40.Columns.Add(this.bandedGridColumn175);
            this.gridBand40.MinWidth = 20;
            this.gridBand40.Name = "gridBand40";
            this.gridBand40.Width = 300;
            // 
            // gridBand41
            // 
            this.gridBand41.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand41.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand41.Caption = "TBF";
            this.gridBand41.Columns.Add(this.bandedGridColumn176);
            this.gridBand41.Columns.Add(this.bandedGridColumn177);
            this.gridBand41.MinWidth = 20;
            this.gridBand41.Name = "gridBand41";
            this.gridBand41.Width = 150;
            // 
            // gridBand42
            // 
            this.gridBand42.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand42.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand42.Caption = "时隙";
            this.gridBand42.Columns.Add(this.bandedGridColumn178);
            this.gridBand42.Columns.Add(this.bandedGridColumn179);
            this.gridBand42.Columns.Add(this.bandedGridColumn180);
            this.gridBand42.Columns.Add(this.bandedGridColumn181);
            this.gridBand42.MinWidth = 20;
            this.gridBand42.Name = "gridBand42";
            this.gridBand42.Width = 300;
            // 
            // XtraLastWeakPoadFormData
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(849, 417);
            this.Controls.Add(this.tableLayoutPanel1);
            this.MaximizeBox = false;
            this.Name = "XtraLastWeakPoadFormData";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "EDGE持续差道路详细信息";
            this.Deactivate += new System.EventHandler(this.XtraLastWeakPoadForm_Deactivate);
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            this.tableLayoutPanel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl5)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView9)).EndInit();
            this.xtraTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            this.xtraTabPage3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).EndInit();
            this.xtraTabPage4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).EndInit();
            this.xtraTabPage5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView8)).EndInit();
            this.xtraTabPage6.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView11)).EndInit();
            this.xtraTabPage7.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView13)).EndInit();
            this.tableLayoutPanel1.ResumeLayout(false);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage3;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage4;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn39;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn40;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn44;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn45;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn47;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn48;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn49;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn50;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn51;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn109;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn110;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemDIYReplay;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn111;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn112;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn113;
        private DevExpress.XtraGrid.GridControl gridControl5;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView9;
        private DevExpress.XtraCharts.ChartControl chartControl1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn129;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn130;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn131;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn132;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn133;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn134;
        private System.Windows.Forms.Button btnClearFly;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemClearFly;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn135;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn136;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemSHowFly;
        private DevExpress.XtraGrid.GridControl gridControl2;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn46;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn52;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn53;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn54;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn55;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn56;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn57;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView4;
        private DevExpress.XtraGrid.GridControl gridControl3;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn58;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn59;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn60;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn61;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn62;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn63;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn64;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn65;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn66;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn67;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn68;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn69;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn70;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn71;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn72;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn73;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn74;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn75;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn76;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn77;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn78;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn79;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn80;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn81;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn82;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn83;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn84;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn85;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn86;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn87;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn88;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView6;
        private DevExpress.XtraGrid.GridControl gridControl4;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn89;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn90;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn91;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn92;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn93;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn94;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn95;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn96;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn97;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn98;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn99;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn100;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn101;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn102;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn103;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn104;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn105;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn106;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn107;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn108;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn114;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn115;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn116;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn117;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn118;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn119;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn120;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn121;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn122;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn123;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn124;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView8;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage6;
        private DevExpress.XtraGrid.GridControl gridControl6;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn125;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn126;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn127;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn128;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn137;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn138;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn139;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn140;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn141;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn142;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn143;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn144;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn145;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn146;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn147;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn148;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn149;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn150;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn151;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn152;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn153;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn154;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn155;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn156;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn157;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn158;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn159;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn160;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn161;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn162;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn163;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView11;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage7;
        private DevExpress.XtraGrid.GridControl gridControl7;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn164;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn165;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn166;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn167;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn168;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn169;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn170;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn171;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn172;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn173;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn174;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn175;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn176;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn177;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn178;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn179;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn180;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn181;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn182;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn183;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn184;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn185;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn186;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn187;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn188;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn189;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn190;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn191;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn192;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn193;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn194;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView13;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemToExcel;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btnNext1;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemLable;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn195;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn196;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn197;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn198;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn199;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn200;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn5;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn6;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn7;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn8;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn9;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn10;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn11;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn12;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn13;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn14;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn15;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn16;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn17;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn18;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn19;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn20;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn21;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn22;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn23;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn24;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn25;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn26;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn27;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn28;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn29;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn30;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn31;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn32;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView5;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView6;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand12;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand1;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand7;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand8;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand9;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand10;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand11;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn57;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn58;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn54;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn55;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn56;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn59;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn60;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand13;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn33;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn34;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn61;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn62;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn63;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn64;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand14;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn35;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn36;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn37;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn38;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn39;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn40;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand15;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn41;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn42;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn43;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand16;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn44;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn45;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn46;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn47;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand17;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn48;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn49;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand18;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn50;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn51;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn52;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn53;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn89;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn90;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn86;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn87;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn88;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn91;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn92;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand19;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn65;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn66;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn93;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn94;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn95;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn96;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand20;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn67;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn68;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn69;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn70;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn71;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn72;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand21;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn73;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn74;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn75;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand22;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn76;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn77;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn78;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn79;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand23;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn80;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn81;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand24;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn82;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn83;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn84;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn85;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn121;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn122;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn118;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn119;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn120;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn123;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn124;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand25;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn97;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn98;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn125;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn126;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn127;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn128;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand26;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn99;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn100;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn101;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn102;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn103;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn104;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand27;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn105;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn106;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn107;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand28;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn108;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn109;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn110;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn111;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand29;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn112;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn113;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand30;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn114;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn115;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn116;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn117;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand5;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn153;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn154;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn150;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn151;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn152;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn155;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn156;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand31;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn129;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn130;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn157;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn158;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn159;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn160;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand32;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn131;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn132;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn133;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn134;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn135;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn136;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand33;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn137;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn138;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn139;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand34;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn140;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn141;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn142;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn143;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand35;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn144;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn145;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand36;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn146;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn147;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn148;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn149;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand6;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn185;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn186;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn182;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn183;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn184;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn187;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn188;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand37;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn161;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn162;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn189;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn190;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn191;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn192;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand38;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn163;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn164;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn165;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn166;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn167;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn168;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand39;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn169;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn170;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn171;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand40;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn172;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn173;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn174;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn175;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand41;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn176;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn177;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand42;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn178;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn179;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn180;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn181;
    }
}