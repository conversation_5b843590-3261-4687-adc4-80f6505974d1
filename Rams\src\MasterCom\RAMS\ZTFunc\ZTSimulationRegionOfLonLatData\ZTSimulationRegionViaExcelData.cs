﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.OleDb;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTSimulationRegionViaExcelData : QueryBase
    {
        public List<LongLatRadius> pointRadiusInfoList { get; set; }
        public ZTSimulationRegionViaExcelData(MainModel mainModel)
            : base(mainModel)
        {

        }

        public override string Name
        {
            get { return "xls生成图层"; }
        }

        public override bool IsNeedSetQueryCondition
        {
            get { return false; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 20000, 20028, this.Name);
        }

        public override string IconName
        {
            get { return "images/其它专题/簇优化.png"; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
            simulationRegionInfoList = new List<SimulationRegionInfo>();
            pointRadiusInfoList = new List<LongLatRadius>();

            importMapData();

            drawSimulationRegion();
        }

        string filename = "";
        public List<SimulationRegionInfo> simulationRegionInfoList { get; set; }

        private void importMapData()
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = FilterHelper.Excel;
            dlg.RestoreDirectory = true;
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                filename = dlg.FileName;
            }
            else
                return;

            DataSet dataSet = ExcelNPOIManager.ImportFromExcel(filename);
            importHorizontalSheet(dataSet);
            importVerticalSheet(dataSet);
            importPointSheet(dataSet);
        }

        private void importVerticalSheet(DataSet dataSet)
        {
            if (!dataSet.Tables.Contains("纵表"))
            {
                return;
            }
            System.Data.DataTable tb = dataSet.Tables["纵表"];
            if (tb == null)
            {
                return;
            }

            SimulationRegionInfo srInfo = null;
            foreach (DataRow row in tb.Rows)
            {
                try
                {
                    string regionName = row["区域名称"].ToString();
                    if (regionName == null)
                    {
                        break;
                    }
                    srInfo = getSrInfo(srInfo, row, regionName);
                }
                catch
                {
                    //errorMsg = "Excel缺少信息列！"
                }
            }
        }

        private SimulationRegionInfo getSrInfo(SimulationRegionInfo srInfo, DataRow row, string regionName)
        {
            if (srInfo == null || srInfo.regionName != regionName)
            {
                srInfo = new SimulationRegionInfo();
                srInfo.regionName = regionName;
                simulationRegionInfoList.Add(srInfo);
            }

            srInfo.scene = row["场点类型"].ToString();
            srInfo.remarks = row["备注"].ToString();
            double lng, lat;
            if (double.TryParse(row["经度"].ToString(), out lng) && double.TryParse(row["纬度"].ToString(), out lat))
            {
                srInfo.LongLatList.Add(new DbPoint(lng, lat));
            }

            return srInfo;
        }

        private void importHorizontalSheet(DataSet dataSet)
        {
            if (!dataSet.Tables.Contains("横表"))
            {
                return;
            }
            System.Data.DataTable tb = dataSet.Tables["横表"];
            if (tb == null)
            {
                return;
            }

            foreach (DataRow row in tb.Rows)
            {
                SimulationRegionInfo srInfo = new SimulationRegionInfo();
                try
                {
                    string regionName = row["区域名称"].ToString();
                    srInfo.regionName = regionName;
                    srInfo.scene = row["场点类型"].ToString();
                    srInfo.remarks = row["备注"].ToString();
                }
                catch
                {
                    //continue
                }
                addLongLatList(tb, row, srInfo);
                if (srInfo.LongLatList.Count > 0)
                {
                    this.simulationRegionInfoList.Add(srInfo);
                }
            }
        }

        private void addLongLatList(DataTable tb, DataRow row, SimulationRegionInfo srInfo)
        {
            int index = 1;
            while (index > 0)
            {
                string lngName = "经度" + index.ToString();
                string latName = "纬度" + index.ToString();
                if (!tb.Columns.Contains(lngName)
                    || !tb.Columns.Contains(latName))
                {
                    break;
                }
                object lngValue = row[lngName];
                object latValue = row[latName];
                if (lngValue == null
                    || latValue == null)
                {
                    break;
                }
                double lng, lat;
                if (double.TryParse(lngValue.ToString(), out lng)
                    && double.TryParse(latValue.ToString(), out lat))
                {
                    srInfo.LongLatList.Add(new DbPoint(lng, lat));
                }
                index++;
            }
        }

        private void importPointSheet(DataSet ds)
        {
            if (!ds.Tables.Contains("点表"))
            {
                return;
            }
            System.Data.DataTable tb = ds.Tables["点表"];

            foreach (DataRow row in tb.Rows)
            {
                try
                {
                    double longitude;
                    double latitude;
                    int radius;
                    if (!double.TryParse(row["经度"].ToString(), out longitude) ||
                        !double.TryParse(row["纬度"].ToString(), out latitude) ||
                        !int.TryParse(row["半径"].ToString(), out radius))
                    {
                        //该条数据不合法被抛弃
                        continue;
                    }
                    LongLatRadius llr = new LongLatRadius();
                    llr.Fill(longitude, latitude, radius);
                    pointRadiusInfoList.Add(llr);
                }
                catch
                {
                    //continue
                }
            }
        }

        private void drawSimulationRegion()
        {
            MapForm mapForm = MainModel.MainForm.GetMapForm();
            if (mapForm == null)
            {
                XtraMessageBox.Show("地图窗口不可见！");
                return;
            }
            mapForm.clearRegionLayer();
            DbRect bounds = new DbRect();
            MainModel.SearchGeometrys.SelectedResvRegions = new List<ResvRegion>();
            foreach (SimulationRegionInfo sRInfo in simulationRegionInfoList)
            {
                MapWinGIS.Shape shape = new MapWinGIS.Shape();
                shape.Create(MapWinGIS.ShpfileType.SHP_POLYGON);
                int index = 0;
                foreach (DbPoint ll in sRInfo.LongLatList)
                {
                    MapWinGIS.Point pt = new MapWinGIS.Point();
                    pt.x = ll.x;
                    pt.y = ll.y;
                    shape.InsertPoint(pt, ref index);
                }
                MapWinGIS.Point endPt = new MapWinGIS.Point();
                endPt.x = sRInfo.LongLatList[0].x;
                endPt.y = sRInfo.LongLatList[0].y;
                shape.InsertPoint(endPt, ref index);

                sRInfo.shape = shape;

                ResvRegion region = new ResvRegion();
                region.RegionName = sRInfo.regionName;
                region.Shape = sRInfo.shape;
                MainModel.SearchGeometrys.SelectedResvRegions.Add(region);
                if (bounds.IsEmpty)
                {
                    bounds = region.GeoOp.Bounds.Clone();
                }
                else
                {
                    bounds.MergeRects(region.GeoOp.Bounds);
                }
            }
            foreach (LongLatRadius llr in pointRadiusInfoList)
            {
                MapWinGIS.Shape circle = MasterCom.MTGis.ShapeHelper.CreateCircleShape(
                    llr.DLongitude, llr.DLatitude, llr.IRadius * 0.00001);

                ResvRegion region = new ResvRegion();
                region.RegionName = string.Format("{0}_{1}", llr.DLongitude, llr.DLatitude);
                region.Shape = circle;
                MainModel.SearchGeometrys.SelectedResvRegions.Add(region);
            }
            MainModel.SimulationRegionInfoList = simulationRegionInfoList;
            mapForm.searchGeometrysChanged();
            if (!bounds.IsEmpty)
            {
                mapForm.RefreshResvLayer();
                mapForm.GoToView(bounds);
            }

        }

    }

    /// <summary>
    ///模拟经纬度生成区域信息
    /// </summary>
    public class SimulationRegionInfo
    {
        public string regionName { get; set; }
        public string scene { get; set; }
        public string remarks { get; set; }
        public List<DbPoint> LongLatList { get; set; } = new List<DbPoint>();
        public MapWinGIS.Shape shape { get; set; }
    }

    public class LongLatRadius
    {
        public double DLongitude { get; set; }
        public double DLatitude { get; set; }
        public int IRadius { get; set; }

        public void Fill(double longitude, double latitude, int radius)
        {
            this.DLongitude = longitude;
            this.DLatitude = latitude;
            this.IRadius = radius;
        }
    }
}
