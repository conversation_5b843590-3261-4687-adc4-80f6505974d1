﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LowSpeedRoadInfoForm_NR
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.contextMenuStripNoFlow = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExport = new System.Windows.Forms.ToolStripMenuItem();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn39 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn46 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn45 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn44 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn47 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.contextMenuStripNoFlow.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            this.SuspendLayout();
            // 
            // contextMenuStripNoFlow
            // 
            this.contextMenuStripNoFlow.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExport});
            this.contextMenuStripNoFlow.Name = "contextMenuStripNoFlow";
            this.contextMenuStripNoFlow.Size = new System.Drawing.Size(125, 26);
            // 
            // ToolStripMenuItemExport
            // 
            this.ToolStripMenuItemExport.Name = "ToolStripMenuItemExport";
            this.ToolStripMenuItemExport.Size = new System.Drawing.Size(124, 22);
            this.ToolStripMenuItemExport.Text = "导出xls...";
            this.ToolStripMenuItemExport.Click += new System.EventHandler(this.ToolStripMenuItemExport_Click);
            // 
            // gridControl
            // 
            this.gridControl.ContextMenuStrip = this.contextMenuStripNoFlow;
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControl.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControl.Location = new System.Drawing.Point(0, 0);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(1008, 482);
            this.gridControl.TabIndex = 1;
            this.gridControl.UseEmbeddedNavigator = true;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            this.gridControl.DoubleClick += new System.EventHandler(this.gridControl_DoubleClick);
            // 
            // gridView
            // 
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn10,
            this.gridColumn33,
            this.gridColumn9,
            this.gridColumn11,
            this.gridColumn13,
            this.gridColumn12,
            this.gridColumn15,
            this.gridColumn35,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn24,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn27,
            this.gridColumn25,
            this.gridColumn26,
            this.gridColumn38,
            this.gridColumn40,
            this.gridColumn39,
            this.gridColumn46,
            this.gridColumn45,
            this.gridColumn44,
            this.gridColumn43,
            this.gridColumn42,
            this.gridColumn28,
            this.gridColumn19,
            this.gridColumn16,
            this.gridColumn14,
            this.gridColumn8,
            this.gridColumn34,
            this.gridColumn37,
            this.gridColumn36,
            this.gridColumn41,
            this.gridColumn29,
            this.gridColumn47,
            this.gridColumn30,
            this.gridColumn31,
            this.gridColumn32,
            this.gridColumn2});
            this.gridView.GridControl = this.gridControl;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsSelection.MultiSelect = true;
            this.gridView.OptionsView.ColumnAutoWidth = false;
            this.gridView.OptionsView.ShowDetailButtons = false;
            this.gridView.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "序号";
            this.gridColumn1.FieldName = "SN";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 60;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "开始时间";
            this.gridColumn3.DisplayFormat.FormatString = "G";
            this.gridColumn3.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn3.FieldName = "BeginTime";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 1;
            this.gridColumn3.Width = 130;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "结束时间";
            this.gridColumn4.DisplayFormat.FormatString = "G";
            this.gridColumn4.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn4.FieldName = "EndTime";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 2;
            this.gridColumn4.Width = 130;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "中心经度";
            this.gridColumn5.FieldName = "Longitude";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 3;
            this.gridColumn5.Width = 80;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "中心纬度";
            this.gridColumn6.FieldName = "Latitude";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 4;
            this.gridColumn6.Width = 80;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "道路";
            this.gridColumn7.FieldName = "RoadDesc";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 5;
            this.gridColumn7.Width = 80;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "网络类型";
            this.gridColumn10.FieldName = "NetType";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 6;
            this.gridColumn10.Width = 80;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "速率类型";
            this.gridColumn33.FieldName = "RoadAppType";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 7;
            this.gridColumn33.Width = 80;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "持续距离(米)";
            this.gridColumn9.FieldName = "Distance";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 8;
            this.gridColumn9.Width = 85;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "持续时间(秒)";
            this.gridColumn11.FieldName = "Duration";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 9;
            this.gridColumn11.Width = 85;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "采样点总数";
            this.gridColumn13.FieldName = "TpCount";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 10;
            this.gridColumn13.Width = 100;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "低速率采样点个数";
            this.gridColumn12.FieldName = "TpCount_LowSpeed";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 11;
            this.gridColumn12.Width = 125;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "低速率采样点占比(%)";
            this.gridColumn15.FieldName = "TpRate_LowSpeed";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 12;
            this.gridColumn15.Width = 135;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "0速率采样点个数";
            this.gridColumn35.FieldName = "TpCount_0Speed";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 13;
            this.gridColumn35.Width = 125;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "0速率采样点占比(%)";
            this.gridColumn20.FieldName = "TpRate_0Speed";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 14;
            this.gridColumn20.Width = 135;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "平均速率(M)";
            this.gridColumn21.FieldName = "SpeedAvg";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 15;
            this.gridColumn21.Width = 88;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "最高速率(M)";
            this.gridColumn17.FieldName = "SpeedMax";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 16;
            this.gridColumn17.Width = 85;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "最低速率(M)";
            this.gridColumn18.FieldName = "SpeedMin";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 17;
            this.gridColumn18.Width = 89;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "SS-RSRP平均值";
            this.gridColumn24.FieldName = "RsrpAvg";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 18;
            this.gridColumn24.Width = 100;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "SS-RSRP最大值";
            this.gridColumn22.FieldName = "RsrpMax";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 19;
            this.gridColumn22.Width = 100;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "SS-RSRP最大值";
            this.gridColumn23.FieldName = "RsrpMin";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 20;
            this.gridColumn23.Width = 100;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "SS-SINR平均值";
            this.gridColumn27.FieldName = "SinrAvg";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 21;
            this.gridColumn27.Width = 100;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "SS-SINR最大值";
            this.gridColumn25.FieldName = "SinrMax";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 22;
            this.gridColumn25.Width = 100;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "SS-SINR最小值";
            this.gridColumn26.FieldName = "SinrMin";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 23;
            this.gridColumn26.Width = 100;
            // 
            // gridColumn38
            // 
            this.gridColumn38.Caption = "邻区SS-RSRP平均值";
            this.gridColumn38.FieldName = "RsrpAvg_NCell";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.Visible = true;
            this.gridColumn38.VisibleIndex = 24;
            this.gridColumn38.Width = 120;
            // 
            // gridColumn40
            // 
            this.gridColumn40.Caption = "邻区SS-RSRP最大值";
            this.gridColumn40.FieldName = "RsrpMax_NCell";
            this.gridColumn40.Name = "gridColumn40";
            this.gridColumn40.Visible = true;
            this.gridColumn40.VisibleIndex = 25;
            this.gridColumn40.Width = 120;
            // 
            // gridColumn39
            // 
            this.gridColumn39.Caption = "邻区SS-RSRP最小值";
            this.gridColumn39.FieldName = "RsrpMin_NCell";
            this.gridColumn39.Name = "gridColumn39";
            this.gridColumn39.Visible = true;
            this.gridColumn39.VisibleIndex = 26;
            this.gridColumn39.Width = 120;
            // 
            // gridColumn46
            // 
            this.gridColumn46.Caption = "LTE-RSRP平均值";
            this.gridColumn46.FieldName = "RsrpAvg_LTE";
            this.gridColumn46.Name = "gridColumn46";
            this.gridColumn46.Visible = true;
            this.gridColumn46.VisibleIndex = 27;
            // 
            // gridColumn45
            // 
            this.gridColumn45.Caption = "LTE-RSRP最大值";
            this.gridColumn45.FieldName = "RsrpMax_LTE";
            this.gridColumn45.Name = "gridColumn45";
            this.gridColumn45.Visible = true;
            this.gridColumn45.VisibleIndex = 28;
            // 
            // gridColumn44
            // 
            this.gridColumn44.Caption = "LTE-RSRP最小值";
            this.gridColumn44.FieldName = "RsrpMin_LTE";
            this.gridColumn44.Name = "gridColumn44";
            this.gridColumn44.Visible = true;
            this.gridColumn44.VisibleIndex = 29;
            // 
            // gridColumn43
            // 
            this.gridColumn43.Caption = "LTE-SINR平均值";
            this.gridColumn43.FieldName = "SinrAvg_LTE";
            this.gridColumn43.Name = "gridColumn43";
            this.gridColumn43.Visible = true;
            this.gridColumn43.VisibleIndex = 30;
            // 
            // gridColumn42
            // 
            this.gridColumn42.Caption = "LTE-SINR最大值";
            this.gridColumn42.FieldName = "SinrMax_LTE";
            this.gridColumn42.Name = "gridColumn42";
            this.gridColumn42.Visible = true;
            this.gridColumn42.VisibleIndex = 31;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "LTE-SINR最小值";
            this.gridColumn28.FieldName = "SinrMin_LTE";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 32;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "下行16QAM占比(%)";
            this.gridColumn19.FieldName = "Rate_16QAM_DL";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 33;
            this.gridColumn19.Width = 125;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "下行64QAM占比(%)";
            this.gridColumn16.FieldName = "Rate_64QAM_DL";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 34;
            this.gridColumn16.Width = 125;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "下行256QAM占比(%)";
            this.gridColumn14.FieldName = "Rate_256QAM_DL";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 35;
            this.gridColumn14.Width = 130;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "下行BPSK占比(%)";
            this.gridColumn8.FieldName = "Rate_BPSK_DL";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 36;
            this.gridColumn8.Width = 110;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "下行QPSK占比(%)";
            this.gridColumn34.FieldName = "Rate_QPSK_DL";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 37;
            this.gridColumn34.Width = 120;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "PDSCH误块率(%)";
            this.gridColumn37.FieldName = "PDSCH_BLER";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.Visible = true;
            this.gridColumn37.VisibleIndex = 38;
            this.gridColumn37.Width = 115;
            // 
            // gridColumn36
            // 
            this.gridColumn36.Caption = "PUSCH误块率(%)";
            this.gridColumn36.FieldName = "PUSCH_BLER";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.Visible = true;
            this.gridColumn36.VisibleIndex = 39;
            this.gridColumn36.Width = 115;
            // 
            // gridColumn41
            // 
            this.gridColumn41.Caption = "CQI平均值";
            this.gridColumn41.FieldName = "CqiAvg";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.Visible = true;
            this.gridColumn41.VisibleIndex = 40;
            this.gridColumn41.Width = 80;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "关联NR小区名称";
            this.gridColumn29.FieldName = "CellNames_NR";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 42;
            this.gridColumn29.Width = 170;
            // 
            // gridColumn47
            // 
            this.gridColumn47.Caption = "关联LTE小区名称";
            this.gridColumn47.FieldName = "CellNames_LTE";
            this.gridColumn47.Name = "gridColumn47";
            this.gridColumn47.Visible = true;
            this.gridColumn47.VisibleIndex = 43;
            this.gridColumn47.Width = 170;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "NR频点";
            this.gridColumn30.FieldName = "Arfcns";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 41;
            this.gridColumn30.Width = 80;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "网格名称";
            this.gridColumn31.FieldName = "GridName";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 44;
            this.gridColumn31.Width = 80;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "片区名称";
            this.gridColumn32.FieldName = "AreaName";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 45;
            this.gridColumn32.Width = 80;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "文件名";
            this.gridColumn2.FieldName = "FileName";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 46;
            this.gridColumn2.Width = 150;
            // 
            // LowSpeedRoadInfoForm_NR
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1008, 482);
            this.Controls.Add(this.gridControl);
            this.Name = "LowSpeedRoadInfoForm_NR";
            this.Text = "NR低速率路段";
            this.contextMenuStripNoFlow.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contextMenuStripNoFlow;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExport;
        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn40;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn39;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn46;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn45;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn44;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn47;
    }
}