﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLTEMobileServiceAnaBase : DIYAnalyseFilesOneByOneByRegion
    {
        public MobileServiceEvent mEvents { get; set; }
        public List<ZTLTEMobileServiceFileItem> resultList { get; set; } = new List<ZTLTEMobileServiceFileItem>();    //保存结果

        public ZTLTEMobileServiceAnaBase(MainModel mainModel)
            : base(mainModel)
        {
            this.IncludeEvent = true;
            this.IncludeMessage = true;
        } 

        protected override void clearDataBeforeAnalyseFiles()
        {
            resultList = new List<ZTLTEMobileServiceFileItem>();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                ZTLTEMobileServiceFileItem fileItem = new ZTLTEMobileServiceFileItem(fileMng.FileName);

                List<DTData> dtDataList = new List<DTData>();

                foreach (TestPoint tp in fileMng.TestPoints)
                {
                    dtDataList.Add((DTData)tp);
                }

                addEvtToDtDataList(fileMng, dtDataList);
                dtDataList.Sort(comparer);

                //开始分析事件
                ZTLTEMobileServiceAnaItem anaItem = new ZTLTEMobileServiceAnaItem(mEvents);

                for (int i = 0; i < dtDataList.Count; i++)
                {
                    anaItem = analyseDtDataList(fileMng, fileItem, dtDataList, anaItem, i);
                }

                addToResultList(anaItem, fileItem); //最后一个

                if (fileItem.anaList.Count > 1)
                {
                    fileItem.SN = resultList.Count + 1;
                    resultList.Add(fileItem);
                }
            }
        }

        private ZTLTEMobileServiceAnaItem analyseDtDataList(DTFileDataManager fileMng, ZTLTEMobileServiceFileItem fileItem, List<DTData> dtDataList, ZTLTEMobileServiceAnaItem anaItem, int i)
        {
            if (dtDataList[i] is TestPoint)
            {
                if (anaItem.IsGotBegin && !anaItem.IsGotEnd)  //有开始，尚未结束，记录中间的采样点信息
                {
                    anaItem.AddTpInfo(dtDataList[i] as TestPoint);
                }
            }
            else if (dtDataList[i] is Event)
            {
                Event evt = dtDataList[i] as Event;
                if (evt.ID == mEvents.structEvents.FTPDownloadBegan           //开始事件
                    || evt.ID == mEvents.structEvents.HTTPPageRequest
                    || evt.ID == mEvents.structEvents.HTTPDownloadBegan
                    || evt.ID == mEvents.structEvents.VIDEOPlayRequest)
                {
                    if (anaItem.IsGotBegin)         //前一个结果，加入列表中
                    {
                        addToResultList(anaItem, fileItem);
                    }

                    anaItem = new ZTLTEMobileServiceAnaItem(mEvents);  //重新初始化
                    string URL = GetURL(evt.SN, fileMng.Messages);
                    anaItem.AddFirstEvtInfo(evt, URL);
                }
                else  //其它事件
                {
                    anaItem.AddOtherEvtInfo(evt);
                }
            }

            return anaItem;
        }

        private void addEvtToDtDataList(DTFileDataManager fileMng, List<DTData> dtDataList)
        {
            foreach (Event evt in fileMng.Events)
            {
                if (evt.ID == mEvents.structEvents.FTPDownloadBegan || evt.ID == mEvents.structEvents.FTPDownloadFirstData
                    || evt.ID == mEvents.structEvents.FTPDownloadSuccess || evt.ID == mEvents.structEvents.FTPDownloadDrop
                    || evt.ID == mEvents.structEvents.FTPDownloadUnFinished

                    || evt.ID == mEvents.structEvents.HTTPPageRequest || evt.ID == mEvents.structEvents.HTTPPageDisplaySuccess
                    || evt.ID == mEvents.structEvents.HTTPPageDisplayFailure || evt.ID == mEvents.structEvents.HTTPPageComplete
                    || evt.ID == mEvents.structEvents.HTTPPageIncomplete || evt.ID == mEvents.structEvents.HTTPPageFail

                    || evt.ID == mEvents.structEvents.VIDEOPlayRequest || evt.ID == mEvents.structEvents.VIDEOPlayFirstData
                    || evt.ID == mEvents.structEvents.VIDEOPlayRebufferStart || evt.ID == mEvents.structEvents.VIDEOPlayRebufferEnd
                    || evt.ID == mEvents.structEvents.VIDEOPlayLastData || evt.ID == mEvents.structEvents.VIDEOPlayDrop
                    || evt.ID == mEvents.structEvents.VIDEOPlayReproductionStart || evt.ID == mEvents.structEvents.VIDEOPlayReproductionStartFailure

                    || evt.ID == mEvents.structEvents.HTTPDownloadBegan || evt.ID == mEvents.structEvents.HTTPDownloadSuccess
                    || evt.ID == mEvents.structEvents.HTTPDownloadDrop || evt.ID == mEvents.structEvents.HTTPDownloadFail)
                {
                    dtDataList.Add(evt);
                }
            }
        }

        protected String GetURL(int sn, List<Message> messages)
        {
            string url = "";
            for (int i = sn - 1; i >= 0; i--)
            {
                foreach (Message msg in messages)
                {
                    if (msg.SN == i)
                    {
                        byte[] source = ((MessageWithSource)msg).Source;
                        url = DisplaySrcCode(source);
                        return url;
                    }
                }
            }
            return url;
        }


        #region 解析URL
        private String ToAssicString(int bgnPos, byte[] srcCode)
        {
            byte byt = srcCode[bgnPos];
            if (byt < 0 || byt > 0xa0)
                return ".";
            if (byt >= '!' && byt <= '~')
            {
                return Encoding.Default.GetString(srcCode, bgnPos, 1);
            }
            return ".";
        }

        private String SrcToString(int bgnPos, int len, byte[] srcCode)
        {
            StringBuilder dstStr = new StringBuilder();
            for (int pos = bgnPos; pos < (bgnPos + len); pos++)
            {
                String tmp = ToAssicString(pos, srcCode);
                dstStr.Append(tmp);
            }
            return dstStr.ToString();
        }

        private int FindFistChar(byte[] srcCode)
        {
            byte byt;
            for (int i = 0; i < srcCode.Length; i++)
            {
                byt = srcCode[i];
                if (Char.IsLetter((char)byt))
                {
                    switch (Encoding.Default.GetString(srcCode, i, 1))
                    {
                        case "B":
                            if (Encoding.Default.GetString(srcCode, i + 7, 4) == "http")
                            {
                                return i + 14;
                            }
                            return i + 7;
                        case "S":
                            return i + 7;
                        case "D":
                            return i + 9;
                    }

                }
            }
            return srcCode.Length;
        }

        public string DisplaySrcCode(byte[] srcCode)
        {
            string URLtext = "";
            if (srcCode != null)
            {
                int pos = FindFistChar(srcCode);
                if (pos < srcCode.Length)
                {
                    int lastLen = (srcCode.Length - pos);
                    URLtext = SrcToString(pos, lastLen, srcCode);
                }
            }
            return URLtext;
        }
        #endregion

        private void addToResultList(ZTLTEMobileServiceAnaItem anaItem, ZTLTEMobileServiceFileItem fileItem)
        {
            //没有开始，不处理
            if (!anaItem.IsGotBegin)
            {
                return;
            }

            if (!anaItem.IsGotEnd)
            {
                anaItem.Result = "无结论";  //存在没有结束事件的情况
            }

            anaItem.SN = fileItem.anaList.Count + 1;
            fileItem.anaList.Add(anaItem);
        }

         /// <summary>
        /// 显示结果
        /// </summary>
        protected override void fireShowForm()
        {
            if (resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }

            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTLTEMobileServiceAnaListForm).FullName);
            ZTLTEMobileServiceAnaListForm mobileServiceForm = obj == null ? null : obj as ZTLTEMobileServiceAnaListForm;
            if (mobileServiceForm == null || mobileServiceForm.IsDisposed)
            {
                mobileServiceForm = new ZTLTEMobileServiceAnaListForm(MainModel);
            }

            mobileServiceForm.FillData(resultList);
            if (!mobileServiceForm.Visible)
            {
                mobileServiceForm.Show(MainModel.MainForm);
            }
        }

        protected override void releaseSource()
        {
            resultList = null;
        }

        private Comparer comparer { get; set; } = new Comparer();
        private class Comparer : IComparer<DTData>
        {
            public int Compare(DTData x, DTData y)
            {
                return x.SN - y.SN;
            }
        }
    }

    public class ZTLTEMobileServiceAnaBase_FDD : ZTLTEMobileServiceAnaBase
    {
        public ZTLTEMobileServiceAnaBase_FDD(MainModel mainModel)
            : base(mainModel)
        {
            this.IncludeEvent = true;
            this.IncludeMessage = true;
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26051, this.Name);//////
        }
    }

    public class ZTLTEMobileServiceFileItem
    {
        public int SN { get; set; }
        public string FileName { get; set; }
        public List<ZTLTEMobileServiceAnaItem> anaList { get; set; }

        public ZTLTEMobileServiceFileItem(string fileName)
        {
            FileName = fileName;
            anaList = new List<ZTLTEMobileServiceAnaItem>();
        }
    }

    public class ZTLTEMobileServiceAnaItem
    {
        public int SN { get; set; }
        public string TypeName{ get; set; }
        public string URL { get; set; }
        public string Result { get; set; }
        public string FailReason { get; set; }      //失败原因

        public string SignalFirstName { get; set; }
        public string SignalFirstTime { get; set; }
        public string SignalSecondName { get; set; }
        public string SignalSecondTime { get; set; }
        public string SignalLastName { get; set; }
        public string SignalLastTime { get; set; }
        public string SignalSpanFirst2Second { get; set; }     //第一和第二信令间的时间差
        public string SignalSpanSecond2Last { get; set; }      //第二和最后信令间的时间差

        public float VideoReBufferCount { get; set; }    //视频卡顿次数
        public float VideoReBufferTime { get; set; }     //视频卡顿时长
        public float VideoLoadByte { get; set; }         //视频加载字节数
        public float VideoLoadTime { get; set; }         //视频加载时长，用于计算加载速率

        public string AppTotalByte { get; set; }      //传输字节
        public string AppTotalTime { get; set; }      //传输时间
        
        public float Rsrp { get; set; }
        public float RsrpCount { get; set; }
        public float Sinr { get; set; }
        public float SinrCount { get; set; }

        private double? appSpeedMax = null;
        public string AppSpeedMax 
        {
            get
            {
                if (Result != "无结论" && appSpeedMax != null)
                {
                    return Math.Round((double)appSpeedMax, 2).ToString();
                }
                else
                {
                    return "";
                }
            }
        }
        public float AppSpeed { get; set; }
        public float AppSpeedCount { get; set; }

        public Event RequestEvt { get; set; }
        public Event SecondEvt { get; set; }     //记录，用于计算最后事件与第二事件间的时间差


        public bool IsGotBegin { get; set; }
        public bool IsGotEnd { get; set; }

        public Dictionary<string, int> cellDic { get; set; }
        public MobileServiceEvent mEvents { get; set; }

        public ZTLTEMobileServiceAnaItem(MobileServiceEvent mEvents)
        {
            IsGotBegin = false;
            IsGotEnd = false;
            cellDic = new Dictionary<string, int>();
            this.mEvents = mEvents;
        }

        public void AddFirstEvtInfo(Event requestEvt,string url)
        {
            RequestEvt = requestEvt;
            IsGotBegin = true;

            if (requestEvt.ID == mEvents.structEvents.FTPDownloadBegan)
            {
                TypeName = "FTP";
                SignalFirstName = "FTPDownloadBegan";
            }
            else if (requestEvt.ID == mEvents.structEvents.HTTPPageRequest)
            {
                TypeName = "HTTP浏览";
                SignalFirstName = "HTTPPageRequest";
            }
            else if (requestEvt.ID == mEvents.structEvents.HTTPDownloadBegan)
            {
                TypeName = "HTTP下载";
                SignalFirstName = "HTTPDownloadBegan";

                //由于HTTP下载没有firstData，使用Began填充FirstData，重要！
                SignalSecondName = "HTTPDownloadFirstData";
                SignalSecondTime = requestEvt.DateTimeStringWithMillisecond;
                SignalSpanFirst2Second = "0";
                SecondEvt = requestEvt;
            }
            else if (requestEvt.ID == mEvents.structEvents.VIDEOPlayRequest)
            {
                TypeName = "VIDEO";
                SignalFirstName = "VIDEOPlayRequest";
            }
            URL = url;
            SignalFirstTime = requestEvt.DateTimeStringWithMillisecond;
        }

        public void AddOtherEvtInfo(Event otherEvt)
        {
            if (!IsGotBegin)
            {
                return;
            }

            if (RequestEvt.ID == mEvents.structEvents.FTPDownloadBegan)
            {
                DealWithFTP(otherEvt);
            }
            else if (RequestEvt.ID == mEvents.structEvents.HTTPPageRequest)
            {
                DealWithHTTPPage(otherEvt);
            }
            else if (RequestEvt.ID == mEvents.structEvents.HTTPDownloadBegan)
            {
                DealWithHTTPDownLoad(otherEvt);
            }
            else if (RequestEvt.ID == mEvents.structEvents.VIDEOPlayRequest)
            {
                DealWithVIDEO(otherEvt);
            }
        }

        public void DealWithFTP(Event otherEvt)
        {
            if (otherEvt.ID == mEvents.structEvents.FTPDownloadSuccess)
            {
                Result = "成功";
                SignalLastName = "FTPDownloadSuccess";
                SignalLastTime = otherEvt.DateTimeStringWithMillisecond;
                AppTotalTime = otherEvt["Value4"].ToString();
                AppTotalByte = otherEvt["Value10"].ToString();
                SignalSpanSecond2Last = GetTwoEvtIntervalDes(otherEvt, SecondEvt);
                IsGotEnd = true;
            }
            else if (otherEvt.ID == mEvents.structEvents.FTPDownloadDrop)
            {
                Result = "失败";
                SignalLastName = "FTPDownloadDrop";
                SignalLastTime = otherEvt.DateTimeStringWithMillisecond;
                AppTotalTime = otherEvt["Value4"].ToString();
                AppTotalByte = otherEvt["Value10"].ToString();
                SignalSpanSecond2Last = GetTwoEvtIntervalDes(otherEvt, SecondEvt);
                IsGotEnd = true;
            }
            else if (otherEvt.ID == mEvents.structEvents.FTPDownloadUnFinished)  //一般为文件末尾
            {
                Result = "未完成";
                SignalLastName = "FTPDownloadUnFinished";
                SignalLastTime = otherEvt.DateTimeStringWithMillisecond;
                AppTotalTime = otherEvt["Value4"].ToString();
                AppTotalByte = otherEvt["Value10"].ToString();
                SignalSpanSecond2Last = GetTwoEvtIntervalDes(otherEvt, SecondEvt);
                IsGotEnd = true;
            }
            else if (otherEvt.ID == mEvents.structEvents.FTPDownloadFirstData)
            {
                SignalSecondName = "FTPDownloadFirstData";
                SignalSecondTime = otherEvt.DateTimeStringWithMillisecond;
                SecondEvt = otherEvt;
                SignalSpanFirst2Second = GetTwoEvtIntervalDes(otherEvt, RequestEvt);
            }
        }

        public void DealWithHTTPPage(Event otherEvt)
        {
            if (otherEvt.ID == mEvents.structEvents.HTTPPageComplete)
            {
                Result = "成功";
                SignalLastName = "HTTPPageComplete";
                SignalLastTime = otherEvt.DateTimeStringWithMillisecond;
                AppTotalTime = otherEvt["Value1"].ToString();
                AppTotalByte = otherEvt["Value2"].ToString();
                SignalSpanSecond2Last = GetTwoEvtIntervalDes(otherEvt, SecondEvt);
                IsGotEnd = true;
            }
            else if (otherEvt.ID == mEvents.structEvents.HTTPPageIncomplete)
            {
                Result = "失败";
                SignalLastName = "HTTPPageIncomplete";
                SignalLastTime = otherEvt.DateTimeStringWithMillisecond;
                AppTotalTime = otherEvt["Value1"].ToString();
                AppTotalByte = otherEvt["Value2"].ToString();
                SignalSpanSecond2Last = GetTwoEvtIntervalDes(otherEvt, SecondEvt);
                IsGotEnd = true;
            }
            else if (otherEvt.ID == mEvents.structEvents.HTTPPageFail)
            {
                if (IsGotEnd)   //之前出现 HTTPPageDisplayFailure 
                {
                    AppTotalTime = otherEvt["Value1"].ToString();
                    AppTotalByte = otherEvt["Value2"].ToString();
                    FailReason = getHttpPageFailReason((long)otherEvt["Value3"]);
                }
                else
                {
                    Result = "失败";
                    SignalLastName = "HTTPPageFail";
                    SignalLastTime = otherEvt.DateTimeStringWithMillisecond;
                    SignalSpanSecond2Last = GetTwoEvtIntervalDes(otherEvt, SecondEvt);
                    IsGotEnd = true;
                }
            }
            else if (otherEvt.ID == mEvents.structEvents.HTTPPageDisplaySuccess)
            {
                SignalSecondName = "HTTPPageDisplaySuccess";
                SignalSecondTime = otherEvt.DateTimeStringWithMillisecond;
                SecondEvt = otherEvt;
                SignalSpanFirst2Second = GetTwoEvtIntervalDes(otherEvt, RequestEvt);
            }
            else if (otherEvt.ID == mEvents.structEvents.HTTPPageDisplayFailure)
            {
                Result = "失败";
                SignalSecondName = "HTTPPageDisplayFailure";
                SignalSecondTime = otherEvt.DateTimeStringWithMillisecond;
                SecondEvt = otherEvt;
                SignalSpanFirst2Second = GetTwoEvtIntervalDes(otherEvt, RequestEvt);
                IsGotEnd = true;
            }
        }

        private string getHttpPageFailReason(long evtValue)
        {
            switch (evtValue)
            {
                case 0:
                    return "APPNotDefined";
                case 5:
                    return "APPServiceReject";
                case 6:
                    return "APPServiceTimeout";
                case 7:
                    return "APPServiceFailure";
                case 8:
                    return "APPServiceDrop";
                default:
                    return evtValue.ToString();
            }
        }

        public void DealWithHTTPDownLoad(Event otherEvt)
        {
            if (otherEvt.ID == mEvents.structEvents.HTTPDownloadSuccess)
            {
                Result = "成功";
                SignalLastName = "HTTPDownloadSuccess";
                SignalLastTime = otherEvt.DateTimeStringWithMillisecond;
                AppTotalTime = otherEvt["Value1"].ToString();
                AppTotalByte = otherEvt["Value2"].ToString();
                SignalSpanSecond2Last = GetTwoEvtIntervalDes(otherEvt, SecondEvt);
                IsGotEnd = true;
            }
            else if (otherEvt.ID == mEvents.structEvents.HTTPDownloadDrop)
            {
                Result = "失败";
                SignalLastName = "FTPDownloadDrop";
                SignalLastTime = otherEvt.DateTimeStringWithMillisecond;
                AppTotalTime = otherEvt["Value1"].ToString();
                AppTotalByte = otherEvt["Value2"].ToString();
                SignalSpanSecond2Last = GetTwoEvtIntervalDes(otherEvt, SecondEvt);
                IsGotEnd = true;
            }
            else if (otherEvt.ID == mEvents.structEvents.HTTPDownloadFail)
            {

                Result = "失败";
                SignalLastName = "HTTPDownloadFail";
                SignalLastTime = otherEvt.DateTimeStringWithMillisecond;
                AppTotalTime = otherEvt["Value1"].ToString();
                AppTotalByte = otherEvt["Value2"].ToString();
                SignalSpanSecond2Last = GetTwoEvtIntervalDes(otherEvt, SecondEvt);
                IsGotEnd = true;
            }

        }

        public void DealWithVIDEO(Event otherEvt)
        {
            if (otherEvt.ID == mEvents.structEvents.VIDEOPlayLastData)
            {
                Result = "成功";
                SignalLastName = "VIDEOPlayLastData";
                SignalLastTime = otherEvt.DateTimeStringWithMillisecond;
                AppTotalTime = otherEvt["Value1"].ToString();
                AppTotalByte = otherEvt["Value2"].ToString();
                VideoLoadByte = (float)(long)otherEvt["Value7"];
                SignalSpanSecond2Last = GetTwoEvtIntervalDes(otherEvt, SecondEvt);
                IsGotEnd = true;
            }
            else if (otherEvt.ID == mEvents.structEvents.VIDEOPlayDrop)
            {
                Result = "失败";
                SignalLastName = "VIDEOPlayDrop";
                SignalLastTime = otherEvt.DateTimeStringWithMillisecond;
                AppTotalTime = otherEvt["Value1"].ToString();
                AppTotalByte = otherEvt["Value2"].ToString();
                SignalSpanSecond2Last = GetTwoEvtIntervalDes(otherEvt, SecondEvt);
                IsGotEnd = true;
            }
            else if (otherEvt.ID == mEvents.structEvents.VIDEOPlayReproductionStartFailure)
            {
                Result = "失败";
                SignalLastName = "VIDEOPlayReproductionStartFailure";
                SignalLastTime = otherEvt.DateTimeStringWithMillisecond;
                AppTotalTime = otherEvt["Value1"].ToString();
                AppTotalByte = otherEvt["Value2"].ToString();
                SignalSpanSecond2Last = GetTwoEvtIntervalDes(otherEvt, SecondEvt);
                IsGotEnd = true;
            }               
            else if (otherEvt.ID == mEvents.structEvents.VIDEOPlayFirstData)
            {
                SignalSecondName = "VIDEOPlayFirstData";
                SignalSecondTime = otherEvt.DateTimeStringWithMillisecond;
                SecondEvt = otherEvt;
                SignalSpanFirst2Second = GetTwoEvtIntervalDes(otherEvt, RequestEvt);
            }
            else if(otherEvt.ID == mEvents.structEvents.VIDEOPlayRebufferStart)
            {
                VideoReBufferCount ++;
            }
            else if (otherEvt.ID == mEvents.structEvents.VIDEOPlayRebufferEnd)
            {
                VideoReBufferTime += (float)(long)otherEvt["Value2"];
            }
            else if(otherEvt.ID == mEvents.structEvents.VIDEOPlayReproductionStart)
            {
                VideoLoadTime = (float)(long)otherEvt["Value1"];
            }
        }
        public static string GetTwoEvtIntervalDes(Event curEvt,Event lastEvt)
        {
            if (curEvt != null && lastEvt != null)
            {
                return (curEvt.lTimeWithMillsecond - lastEvt.lTimeWithMillsecond).ToString();
            }
            return "";
        }
        public void AddTpInfo(TestPoint tp)
        {
            float? rsrp = GetRSRP(tp);
            if (rsrp != null && rsrp >= -140 && rsrp <= -10)
            {
                Rsrp += (float)rsrp;
                RsrpCount++;
            }

            float? sinr = GetSINR(tp);
            if (sinr != null && sinr >= -50 && sinr <= 50)
            {
                Sinr += (float)sinr;
                SinrCount++;
            }

            float? appSpeed = GetAppSpeed(tp);
            if (appSpeed != null && appSpeed >= 0)
            {
                float appSpeedMb = (float)appSpeed / 1000 / 1000;
                appSpeedMax = appSpeedMax > appSpeedMb ? appSpeedMax : appSpeedMb;
                AppSpeed += appSpeedMb;
                AppSpeedCount++;
            }

            //分析小区信息
            string cellName = getCellName(tp);

            if (cellDic.ContainsKey(cellName))
            {
                cellDic[cellName]++;
            }
            else
            {
                cellDic.Add(cellName, 1);
            }
        }

        private string getCellName(TestPoint tp)
        {
            LTECell lteCell = tp.GetMainLTECell_TdOrFdd();
            string cellName = "";
            if (lteCell != null)
            {
                cellName = lteCell.Name;
            }
            else
            {
                if (GetTAC(tp) != null && GetECI(tp) != null)
                {
                    int tac = (int)(ushort?)GetTAC(tp);
                    int eci = (int)GetECI(tp);
                    cellName = tac.ToString() + "_" + eci.ToString();
                }
                else
                {
                    cellName = "未知小区";
                }
            }

            return cellName;
        }

        protected float? GetRSRP(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_RSRP"];
            }
            return (float?)tp["lte_RSRP"];
        }
        protected float? GetSINR(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)tp["lte_fdd_SINR"];
            }
            return (float?)tp["lte_SINR"]; 
        }
        protected float? GetAppSpeed(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return (float?)(int?)tp["lte_fdd_APP_Speed"];
            }
            return (float?)(int?)tp["lte_APP_Speed"];
        }
        protected object GetTAC(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_TAC"];
            }
            return tp["lte_TAC"];
        }
        protected object GetECI(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_ECI"];
            }
            return tp["lte_ECI"];
        }

        #region 预处理
        public string AppSpeedAvg
        {
            get
            {
                if (Result == "无结论")
                {
                    return "";
                }
                else
                {
                    if (AppSpeedCount > 0)
                    {
                        return Math.Round(AppSpeed / AppSpeedCount, 2).ToString();
                    }
                    else
                    {
                        return "";
                    }
                }
            }
        }
        public string RsrpAvg
        {
            get
            {
                if (Result == "无结论")
                {
                    return "";
                }
                else
                {
                    if (RsrpCount > 0)
                    {
                        return Math.Round(Rsrp / RsrpCount, 2).ToString();
                    }
                    else
                    {
                        return "";
                    }
                }
            }
        }

        public string SinrAvg
        {
            get
            {
                if (Result == "无结论")
                {
                    return "";
                }
                else
                {
                    if (SinrCount > 0)
                    {
                        return Math.Round(Sinr / SinrCount, 2).ToString();
                    }
                    else
                    {
                        return "";
                    }
                }
            }
        }

        public string SampleCount
        {
            get
            {
                if (Result == "无结论")
                {
                    return "";
                }
                else
                {
                    return RsrpCount.ToString();
                }
            }
        }


        public string CellInfos
        {
            get
            {
                if (Result == "无结论")
                {
                    return "";
                }
                else
                {
                    if (cellDic.Count > 0)
                    {
                        StringBuilder cellInfo = new StringBuilder();
                        foreach (string cellName in cellDic.Keys)
                        {
                            cellInfo.Append(cellName + "(" + cellDic[cellName].ToString() + ")" + " | ");  //小区A(5) | 小区B(11)
                        }

                        return cellInfo.ToString();
                    }
                    else
                    {
                        return "";
                    }
                }
            }
        }

        public string ReBufferCount
        {
            get
            {
                if (VideoReBufferCount == 0)
                {
                    return "";
                }
                else
                {
                    return VideoReBufferCount.ToString();
                }
            }
        }

        public string ReBufferTime
        {
            get
            {
                if (VideoReBufferTime == 0)
                {
                    return "";
                }
                else
                {
                    return VideoReBufferTime.ToString();
                }
            }
        }

        public string LoadSpeed
        {
            get
            {
                if (VideoLoadTime == 0)
                {
                    return "";
                }
                else
                {
                    return Math.Round((VideoLoadByte * 8 * 1000) / (VideoLoadTime * 1024 * 1024),2).ToString();
                }
            }
        }  
        #endregion        
    }

    public class MobileServiceEvent
    {
        public StructMobileServiceEvent structEvents { get; set; } = new StructMobileServiceEvent();
        public MobileServiceEvent(bool isFdd)
        {
            if (isFdd)
            {
                structEvents.FTPDownloadBegan = 3557;
                structEvents.FTPDownloadFirstData = 3573;
                structEvents.FTPDownloadSuccess = 3558;
                structEvents.FTPDownloadDrop = 3559;
                structEvents.FTPDownloadUnFinished = 3592;

                structEvents.HTTPDownloadBegan = 3216;
                structEvents.HTTPDownloadSuccess = 3217;
                structEvents.HTTPDownloadDrop = 3218;
                structEvents.HTTPDownloadFail = 1270;

                structEvents.HTTPPageRequest = 3211;
                structEvents.HTTPPageDisplaySuccess = 3212;
                structEvents.HTTPPageDisplayFailure = 3213;
                structEvents.HTTPPageComplete = 3214;
                structEvents.HTTPPageIncomplete = 3215;
                structEvents.HTTPPageFail = 3269;

                structEvents.VIDEOPlayRequest = 3231;
                structEvents.VIDEOPlayFirstData = 3232;
                structEvents.VIDEOPlayRebufferStart = 3233;
                structEvents.VIDEOPlayRebufferEnd = 3234;
                structEvents.VIDEOPlayLastData = 3235;  //相当于success
                structEvents.VIDEOPlayDrop = 3237;
                structEvents.VIDEOPlayReproductionStart = 3238;
                structEvents.VIDEOPlayReproductionStartFailure = 3239;
            }
            else
            {
                structEvents.FTPDownloadBegan = 57;
                structEvents.FTPDownloadFirstData = 73;
                structEvents.FTPDownloadSuccess = 58;
                structEvents.FTPDownloadDrop = 59;
                structEvents.FTPDownloadUnFinished = 92;

                structEvents.HTTPDownloadBegan = 1216;
                structEvents.HTTPDownloadSuccess = 1217;
                structEvents.HTTPDownloadDrop = 1218;
                structEvents.HTTPDownloadFail = 1270;

                structEvents.HTTPPageRequest = 1211;
                structEvents.HTTPPageDisplaySuccess = 1212;
                structEvents.HTTPPageDisplayFailure = 1213;
                structEvents.HTTPPageComplete = 1214;
                structEvents.HTTPPageIncomplete = 1215;
                structEvents.HTTPPageFail = 1269;

                structEvents.VIDEOPlayRequest = 1231;
                structEvents.VIDEOPlayFirstData = 1232;
                structEvents.VIDEOPlayRebufferStart = 1233;
                structEvents.VIDEOPlayRebufferEnd = 1234;
                structEvents.VIDEOPlayLastData = 1235;  //相当于success
                structEvents.VIDEOPlayDrop = 1237;
                structEvents.VIDEOPlayReproductionStart = 1238;
                structEvents.VIDEOPlayReproductionStartFailure = 1239;
            }
        }

        public class StructMobileServiceEvent
        {
            public int FTPDownloadBegan { get; set; } 
            public int FTPDownloadFirstData { get; set; } 
            public int FTPDownloadSuccess { get; set; } 
            public int FTPDownloadDrop { get; set; } 
            public int FTPDownloadUnFinished { get; set; } 

            public int HTTPDownloadBegan { get; set; } 
            public int HTTPDownloadSuccess { get; set; } 
            public int HTTPDownloadDrop { get; set; } 
            public int HTTPDownloadFail { get; set; } 


            public int HTTPPageRequest { get; set; } 
            public int HTTPPageDisplaySuccess { get; set; } 
            public int HTTPPageDisplayFailure { get; set; } 
            public int HTTPPageComplete { get; set; } 
            public int HTTPPageIncomplete { get; set; } 
            public int HTTPPageFail { get; set; } 

            public int VIDEOPlayRequest { get; set; } 
            public int VIDEOPlayFirstData { get; set; } 
            public int VIDEOPlayRebufferStart { get; set; } 
            public int VIDEOPlayRebufferEnd { get; set; } 
            public int VIDEOPlayLastData { get; set; }   //相当于success
            public int VIDEOPlayDrop { get; set; } 
            public int VIDEOPlayReproductionStart { get; set; } 
            public int VIDEOPlayReproductionStartFailure { get; set; } 
        }
    }
    enum EnumMobileServiceEvent
    {
        FTPDownloadBegan = 57,
        FTPDownloadFirstData = 73,
        FTPDownloadSuccess = 58,
        FTPDownloadDrop = 59,
        FTPDownloadUnFinished = 92,

        HTTPDownloadBegan = 1216,
        HTTPDownloadSuccess = 1217,
        HTTPDownloadDrop = 1218,
        HTTPDownloadFail = 1270,
        

        HTTPPageRequest = 1211,
        HTTPPageDisplaySuccess = 1212,
        HTTPPageDisplayFailure = 1213,
        HTTPPageComplete = 1214,
        HTTPPageIncomplete = 1215,
        HTTPPageFail = 1269,

        VIDEOPlayRequest = 1231,
        VIDEOPlayFirstData = 1232,
        VIDEOPlayRebufferStart = 1233,
        VIDEOPlayRebufferEnd = 1234,
        VIDEOPlayLastData = 1235,  //相当于success
        VIDEOPlayDrop = 1237,
        VIDEOPlayReproductionStart = 1238,
        VIDEOPlayReproductionStartFailure = 1239,
    }
}
