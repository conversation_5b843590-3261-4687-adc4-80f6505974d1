﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class LtePRBAnaByRegion : LtePRBAnaQueryBase
    {
        private LtePRBAnaByRegion()
            : base()
        {
            FilterSampleByRegion = true;
        }

        private static LtePRBAnaByRegion intance = null;
        public static new LtePRBAnaByRegion GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new LtePRBAnaByRegion();
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "PRB占比分析(按区域)"; }
        }

    }

    public class LtePRBAnaByRegion_FDD : LtePRBAnaQueryBase_FDD
    {
        private static LtePRBAnaByRegion_FDD instance = null;
        public static new LtePRBAnaByRegion_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LtePRBAnaByRegion_FDD();
                    }
                }
            }
            return instance;
        }

        private LtePRBAnaByRegion_FDD()
            : base()
        {
            FilterSampleByRegion = true;
        }

        public override string Name
        {
            get { return "PRB占比分析LTE_FDD(按区域)"; }
        }
    }
}
