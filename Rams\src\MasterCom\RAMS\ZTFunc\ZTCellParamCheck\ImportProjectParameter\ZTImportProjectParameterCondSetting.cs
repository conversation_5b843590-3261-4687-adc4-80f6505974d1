﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ImportProjectParameterCond
{
    public partial class ZTImportProjectParameterCondSetting : MinCloseForm
    {
        private CondSet cond = null;
        public ZTImportProjectParameterCondSetting()
        {
            InitializeComponent();
        }

        public void SetCond(CondSet con)
        {
            this.cond = con;
            this.textBoxFileName.Text = this.cond.fileName;
        }

        public CondSet GetCond()
        {
            return this.cond;
        }

        private void buttonSelectFile_Click(object sender, EventArgs e)
        {
            OpenFileDialog opd = new OpenFileDialog();
            opd.Filter = "表格文件|*.csv;*.xls;*.xlsx";
            if (opd.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return;
            }
            this.textBoxFileName.Text = opd.FileName;
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            if (this.textBoxFileName.Text == "")
            {
                MessageBox.Show("请选择要导入的文件!");
                return;
            }
            this.cond.fileName = this.textBoxFileName.Text;
            this.DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }
    }

    public class CondSet
    {
        public string fileName { get; set; } = "";
    }
}
