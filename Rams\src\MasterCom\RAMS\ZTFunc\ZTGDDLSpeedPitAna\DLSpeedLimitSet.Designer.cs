﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class DLSpeedLimitSet
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.dVarianceMax = new System.Windows.Forms.NumericUpDown();
            this.label7 = new System.Windows.Forms.Label();
            this.dVarianceMin = new System.Windows.Forms.NumericUpDown();
            this.label5 = new System.Windows.Forms.Label();
            this.dSpeedAvgMax = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.dSpeedAvgMin = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.dLastTime = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.chkRegion = new System.Windows.Forms.CheckBox();
            ((System.ComponentModel.ISupportInitialize)(this.dVarianceMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dVarianceMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dSpeedAvgMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dSpeedAvgMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dLastTime)).BeginInit();
            this.SuspendLayout();
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(393, 94);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 30;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(303, 94);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 29;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // dVarianceMax
            // 
            this.dVarianceMax.DecimalPlaces = 2;
            this.dVarianceMax.Location = new System.Drawing.Point(250, 55);
            this.dVarianceMax.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.dVarianceMax.Name = "dVarianceMax";
            this.dVarianceMax.Size = new System.Drawing.Size(63, 21);
            this.dVarianceMax.TabIndex = 28;
            this.dVarianceMax.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.dVarianceMax.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(177, 61);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(65, 12);
            this.label7.TabIndex = 27;
            this.label7.Text = "速率方差≤";
            // 
            // dVarianceMin
            // 
            this.dVarianceMin.DecimalPlaces = 2;
            this.dVarianceMin.Location = new System.Drawing.Point(85, 57);
            this.dVarianceMin.Name = "dVarianceMin";
            this.dVarianceMin.Size = new System.Drawing.Size(66, 21);
            this.dVarianceMin.TabIndex = 26;
            this.dVarianceMin.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(15, 61);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(65, 12);
            this.label5.TabIndex = 23;
            this.label5.Text = "速率方差≥";
            // 
            // dSpeedAvgMax
            // 
            this.dSpeedAvgMax.DecimalPlaces = 2;
            this.dSpeedAvgMax.Location = new System.Drawing.Point(398, 20);
            this.dSpeedAvgMax.Name = "dSpeedAvgMax";
            this.dSpeedAvgMax.Size = new System.Drawing.Size(65, 21);
            this.dSpeedAvgMax.TabIndex = 22;
            this.dSpeedAvgMax.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.dSpeedAvgMax.Value = new decimal(new int[] {
            42,
            0,
            0,
            0});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(311, 24);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(83, 12);
            this.label2.TabIndex = 21;
            this.label2.Text = "M  平均速率≤";
            // 
            // dSpeedAvgMin
            // 
            this.dSpeedAvgMin.DecimalPlaces = 2;
            this.dSpeedAvgMin.Location = new System.Drawing.Point(245, 19);
            this.dSpeedAvgMin.Name = "dSpeedAvgMin";
            this.dSpeedAvgMin.Size = new System.Drawing.Size(65, 21);
            this.dSpeedAvgMin.TabIndex = 20;
            this.dSpeedAvgMin.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.dSpeedAvgMin.Value = new decimal(new int[] {
            36,
            0,
            0,
            0});
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(156, 24);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(83, 12);
            this.label3.TabIndex = 19;
            this.label3.Text = "S  平均速率≥";
            // 
            // dLastTime
            // 
            this.dLastTime.DecimalPlaces = 2;
            this.dLastTime.Location = new System.Drawing.Point(85, 19);
            this.dLastTime.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.dLastTime.Name = "dLastTime";
            this.dLastTime.Size = new System.Drawing.Size(66, 21);
            this.dLastTime.TabIndex = 18;
            this.dLastTime.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.dLastTime.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(16, 23);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 17;
            this.label1.Text = "持续时间≥";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(467, 26);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(11, 12);
            this.label4.TabIndex = 31;
            this.label4.Text = "M";
            // 
            // chkRegion
            // 
            this.chkRegion.AutoSize = true;
            this.chkRegion.Location = new System.Drawing.Point(330, 56);
            this.chkRegion.Name = "chkRegion";
            this.chkRegion.Size = new System.Drawing.Size(156, 16);
            this.chkRegion.TabIndex = 32;
            this.chkRegion.Text = "是否判断采样点区域归属";
            this.chkRegion.UseVisualStyleBackColor = true;
            // 
            // DLSpeedLimitSet
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(486, 133);
            this.Controls.Add(this.chkRegion);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.dVarianceMax);
            this.Controls.Add(this.label7);
            this.Controls.Add(this.dVarianceMin);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.dSpeedAvgMax);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.dSpeedAvgMin);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.dLastTime);
            this.Controls.Add(this.label1);
            this.Name = "DLSpeedLimitSet";
            this.Text = "下载速率限速分析设置";
            ((System.ComponentModel.ISupportInitialize)(this.dVarianceMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dVarianceMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dSpeedAvgMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dSpeedAvgMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dLastTime)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.NumericUpDown dVarianceMax;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.NumericUpDown dVarianceMin;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.NumericUpDown dSpeedAvgMax;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.NumericUpDown dSpeedAvgMin;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.NumericUpDown dLastTime;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.CheckBox chkRegion;
    }
}