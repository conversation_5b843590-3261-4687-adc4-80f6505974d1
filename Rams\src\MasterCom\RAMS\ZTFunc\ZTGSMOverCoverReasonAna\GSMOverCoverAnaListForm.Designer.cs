﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class GSMOverCoverAnaListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            this.gridControlRegion = new DevExpress.XtraGrid.GridControl();
            this.ctxMenuRegion = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripRegionExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridViewRegion = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlCell = new DevExpress.XtraGrid.GridControl();
            this.ctxMenuCell = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripCellExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridViewCell = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand1 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn6 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand3 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn7 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand4 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn8 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand5 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumnBlerRate = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.girdColumnTotalBler = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand2 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn18 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.comboBoxEditFile = new DevExpress.XtraEditors.ComboBoxEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.gridControlTP = new DevExpress.XtraGrid.GridControl();
            this.ctxMenuTP = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripTPReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripTPReplayCompare = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripTPLineChartLocate = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripTPExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridViewTP = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRegion)).BeginInit();
            this.ctxMenuRegion.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewRegion)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCell)).BeginInit();
            this.ctxMenuCell.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewCell)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEditFile.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTP)).BeginInit();
            this.ctxMenuTP.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTP)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControlRegion
            // 
            this.gridControlRegion.ContextMenuStrip = this.ctxMenuRegion;
            this.gridControlRegion.Dock = System.Windows.Forms.DockStyle.Fill;
            gridLevelNode1.RelationName = "Level1";
            this.gridControlRegion.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.gridControlRegion.Location = new System.Drawing.Point(0, 0);
            this.gridControlRegion.MainView = this.gridViewRegion;
            this.gridControlRegion.Name = "gridControlRegion";
            this.gridControlRegion.Size = new System.Drawing.Size(791, 382);
            this.gridControlRegion.TabIndex = 0;
            this.gridControlRegion.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewRegion});
            // 
            // ctxMenuRegion
            // 
            this.ctxMenuRegion.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripRegionExportExcel});
            this.ctxMenuRegion.Name = "contextMenuStrip";
            this.ctxMenuRegion.Size = new System.Drawing.Size(151, 26);
            // 
            // toolStripRegionExportExcel
            // 
            this.toolStripRegionExportExcel.Name = "toolStripRegionExportExcel";
            this.toolStripRegionExportExcel.Size = new System.Drawing.Size(150, 22);
            this.toolStripRegionExportExcel.Text = "导出到Excel...";
            this.toolStripRegionExportExcel.Click += new System.EventHandler(this.ToolStripMenuRegionExportExcel_Click);
            // 
            // gridViewRegion
            // 
            this.gridViewRegion.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridViewRegion.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewRegion.Appearance.Row.Options.UseTextOptions = true;
            this.gridViewRegion.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewRegion.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn37,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5});
            this.gridViewRegion.GridControl = this.gridControlRegion;
            this.gridViewRegion.Name = "gridViewRegion";
            this.gridViewRegion.OptionsBehavior.Editable = false;
            this.gridViewRegion.OptionsView.AllowCellMerge = true;
            this.gridViewRegion.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "区域名称";
            this.gridColumn1.FieldName = "regionName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "总采样点";
            this.gridColumn37.FieldName = "tpTotal";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn37.Visible = true;
            this.gridColumn37.VisibleIndex = 1;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "过覆盖总数";
            this.gridColumn2.FieldName = "rxQualTotal";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 2;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "原因分类";
            this.gridColumn3.FieldName = "reason";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 3;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "过覆盖采样点数";
            this.gridColumn4.FieldName = "reasonRxQualTotal";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 4;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "百分比(%)";
            this.gridColumn5.FieldName = "reasonRxQualRate";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 5;
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(798, 412);
            this.xtraTabControl1.TabIndex = 1;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.gridControlRegion);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(791, 382);
            this.xtraTabPage1.Text = "原因分析概况";
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.splitContainerControl1);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(791, 382);
            this.xtraTabPage2.Text = "问题详情";
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.gridControlCell);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.labelControl2);
            this.splitContainerControl1.Panel2.Controls.Add(this.comboBoxEditFile);
            this.splitContainerControl1.Panel2.Controls.Add(this.labelControl1);
            this.splitContainerControl1.Panel2.Controls.Add(this.gridControlTP);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(791, 382);
            this.splitContainerControl1.SplitterPosition = 191;
            this.splitContainerControl1.TabIndex = 1;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // gridControlCell
            // 
            this.gridControlCell.ContextMenuStrip = this.ctxMenuCell;
            this.gridControlCell.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlCell.Location = new System.Drawing.Point(0, 0);
            this.gridControlCell.MainView = this.gridViewCell;
            this.gridControlCell.Name = "gridControlCell";
            this.gridControlCell.Size = new System.Drawing.Size(791, 191);
            this.gridControlCell.TabIndex = 0;
            this.gridControlCell.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewCell});
            // 
            // ctxMenuCell
            // 
            this.ctxMenuCell.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripCellExportExcel});
            this.ctxMenuCell.Name = "contextMenuStripProblem";
            this.ctxMenuCell.Size = new System.Drawing.Size(151, 26);
            // 
            // toolStripCellExportExcel
            // 
            this.toolStripCellExportExcel.Name = "toolStripCellExportExcel";
            this.toolStripCellExportExcel.Size = new System.Drawing.Size(150, 22);
            this.toolStripCellExportExcel.Text = "导出到Excel...";
            this.toolStripCellExportExcel.Click += new System.EventHandler(this.ToolStripMenuCellExportExcel_Click);
            // 
            // gridViewCell
            // 
            this.gridViewCell.Appearance.BandPanel.Options.UseTextOptions = true;
            this.gridViewCell.Appearance.BandPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewCell.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridViewCell.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewCell.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridViewCell.Appearance.Row.Options.UseTextOptions = true;
            this.gridViewCell.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewCell.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand1,
            this.gridBand3,
            this.gridBand4,
            this.gridBand5,
            this.gridBand2});
            this.gridViewCell.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumnBlerRate,
            this.gridColumn14,
            this.girdColumnTotalBler,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn24,
            this.gridColumn22,
            this.bandedGridColumn3,
            this.bandedGridColumn2,
            this.bandedGridColumn1,
            this.gridColumn32});
            this.gridViewCell.GridControl = this.gridControlCell;
            this.gridViewCell.Name = "gridViewCell";
            this.gridViewCell.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridViewCell.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridViewCell.OptionsBehavior.Editable = false;
            this.gridViewCell.OptionsCustomization.AllowBandMoving = false;
            this.gridViewCell.OptionsCustomization.AllowBandResizing = false;
            this.gridViewCell.OptionsCustomization.AllowGroup = false;
            this.gridViewCell.OptionsFilter.AllowColumnMRUFilterList = false;
            this.gridViewCell.OptionsFilter.AllowFilterEditor = false;
            this.gridViewCell.OptionsFilter.AllowMRUFilterList = false;
            this.gridViewCell.OptionsFilter.ShowAllTableValuesInFilterPopup = true;
            this.gridViewCell.OptionsFilter.UseNewCustomFilterDialog = true;
            this.gridViewCell.OptionsSelection.MultiSelect = true;
            this.gridViewCell.OptionsView.EnableAppearanceEvenRow = true;
            this.gridViewCell.OptionsView.EnableAppearanceOddRow = true;
            this.gridViewCell.OptionsView.ShowGroupPanel = false;
            this.gridViewCell.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.gridViewCell_FocusedRowChanged);
            this.gridViewCell.DoubleClick += new System.EventHandler(this.gridViewCell_DoubleClick);
            // 
            // gridBand1
            // 
            this.gridBand1.Columns.Add(this.gridColumn6);
            this.gridBand1.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridBand1.Name = "gridBand1";
            this.gridBand1.Width = 44;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "序号";
            this.gridColumn6.FieldName = "sn";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.Width = 44;
            // 
            // gridBand3
            // 
            this.gridBand3.Caption = "小区信息";
            this.gridBand3.Columns.Add(this.gridColumn7);
            this.gridBand3.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridBand3.Name = "gridBand3";
            this.gridBand3.Width = 100;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "小区名";
            this.gridColumn7.FieldName = "cellName";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.Width = 100;
            // 
            // gridBand4
            // 
            this.gridBand4.Caption = "基本信息";
            this.gridBand4.Columns.Add(this.gridColumn8);
            this.gridBand4.Columns.Add(this.gridColumn9);
            this.gridBand4.Columns.Add(this.gridColumn10);
            this.gridBand4.Columns.Add(this.gridColumn11);
            this.gridBand4.Columns.Add(this.gridColumn12);
            this.gridBand4.Columns.Add(this.gridColumn13);
            this.gridBand4.MinWidth = 20;
            this.gridBand4.Name = "gridBand4";
            this.gridBand4.Width = 440;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "LAC";
            this.gridColumn8.FieldName = "LAC";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.Width = 60;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "CI";
            this.gridColumn9.FieldName = "CI";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.Width = 60;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "经度";
            this.gridColumn10.FieldName = "longitude";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.Width = 80;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "纬度";
            this.gridColumn11.FieldName = "latitude";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.Width = 80;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "所属网格";
            this.gridColumn12.FieldName = "areaName";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.Width = 80;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "所属路段";
            this.gridColumn13.FieldName = "roadName";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.Width = 80;
            // 
            // gridBand5
            // 
            this.gridBand5.Caption = "采样点信息";
            this.gridBand5.Columns.Add(this.gridColumnBlerRate);
            this.gridBand5.Columns.Add(this.gridColumn14);
            this.gridBand5.Columns.Add(this.girdColumnTotalBler);
            this.gridBand5.Name = "gridBand5";
            this.gridBand5.Width = 260;
            // 
            // gridColumnBlerRate
            // 
            this.gridColumnBlerRate.Caption = "过覆盖比例";
            this.gridColumnBlerRate.FieldName = "rxLevRate";
            this.gridColumnBlerRate.Name = "gridColumnBlerRate";
            this.gridColumnBlerRate.Visible = true;
            this.gridColumnBlerRate.Width = 100;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "总采样点数";
            this.gridColumn14.FieldName = "tpTotal";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.Width = 80;
            // 
            // girdColumnTotalBler
            // 
            this.girdColumnTotalBler.Caption = "过覆盖总数";
            this.girdColumnTotalBler.FieldName = "rxLevTotal";
            this.girdColumnTotalBler.Name = "girdColumnTotalBler";
            this.girdColumnTotalBler.Visible = true;
            this.girdColumnTotalBler.Width = 80;
            // 
            // gridBand2
            // 
            this.gridBand2.Caption = "原因分类";
            this.gridBand2.Columns.Add(this.gridColumn18);
            this.gridBand2.Columns.Add(this.gridColumn19);
            this.gridBand2.Columns.Add(this.gridColumn22);
            this.gridBand2.Columns.Add(this.gridColumn24);
            this.gridBand2.Columns.Add(this.bandedGridColumn3);
            this.gridBand2.Columns.Add(this.bandedGridColumn2);
            this.gridBand2.Columns.Add(this.bandedGridColumn1);
            this.gridBand2.Columns.Add(this.gridColumn32);
            this.gridBand2.Name = "gridBand2";
            this.gridBand2.Width = 625;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "缺少基站";
            this.gridColumn18.FieldName = "poorBtsCount";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.Width = 80;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "邻区漏配";
            this.gridColumn19.FieldName = "lackNCellCount";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.Width = 80;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "室分泄漏";
            this.gridColumn22.FieldName = "inDoorCount";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.Width = 80;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "切换不合理";
            this.gridColumn24.FieldName = "hoAbnormalCount";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.Width = 80;
            // 
            // bandedGridColumn3
            // 
            this.bandedGridColumn3.Caption = "主服小区覆盖不足";
            this.bandedGridColumn3.FieldName = "mainCellCoverWeakCount";
            this.bandedGridColumn3.Name = "bandedGridColumn3";
            this.bandedGridColumn3.Visible = true;
            // 
            // bandedGridColumn2
            // 
            this.bandedGridColumn2.Caption = "邻小区覆盖不足";
            this.bandedGridColumn2.FieldName = "nCellCoverWeakCount";
            this.bandedGridColumn2.Name = "bandedGridColumn2";
            this.bandedGridColumn2.Visible = true;
            // 
            // bandedGridColumn1
            // 
            this.bandedGridColumn1.Caption = "覆盖不稳定";
            this.bandedGridColumn1.FieldName = "unstabitilyCoverCount";
            this.bandedGridColumn1.Name = "bandedGridColumn1";
            this.bandedGridColumn1.Visible = true;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "其它";
            this.gridColumn32.FieldName = "otherCount";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.Width = 80;
            // 
            // labelControl2
            // 
            this.labelControl2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.labelControl2.Location = new System.Drawing.Point(605, 4);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(164, 14);
            this.labelControl2.TabIndex = 3;
            this.labelControl2.Text = "文件共 个，当前文件有 条记录";
            // 
            // comboBoxEditFile
            // 
            this.comboBoxEditFile.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.comboBoxEditFile.Location = new System.Drawing.Point(61, 1);
            this.comboBoxEditFile.Name = "comboBoxEditFile";
            this.comboBoxEditFile.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.comboBoxEditFile.Size = new System.Drawing.Size(509, 21);
            this.comboBoxEditFile.TabIndex = 2;
            this.comboBoxEditFile.SelectedIndexChanged += new System.EventHandler(this.comboBoxEditFile_SelectedIndexChanged);
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(3, 4);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(52, 14);
            this.labelControl1.TabIndex = 1;
            this.labelControl1.Text = "文件列表:";
            // 
            // gridControlTP
            // 
            this.gridControlTP.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.gridControlTP.ContextMenuStrip = this.ctxMenuTP;
            this.gridControlTP.Location = new System.Drawing.Point(0, 28);
            this.gridControlTP.MainView = this.gridViewTP;
            this.gridControlTP.Name = "gridControlTP";
            this.gridControlTP.Size = new System.Drawing.Size(791, 157);
            this.gridControlTP.TabIndex = 0;
            this.gridControlTP.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewTP});
            // 
            // ctxMenuTP
            // 
            this.ctxMenuTP.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripTPReplay,
            this.toolStripTPReplayCompare,
            this.toolStripTPLineChartLocate,
            this.toolStripTPExportExcel});
            this.ctxMenuTP.Name = "contextMenuStripTp";
            this.ctxMenuTP.Size = new System.Drawing.Size(151, 92);
            // 
            // toolStripTPReplay
            // 
            this.toolStripTPReplay.Image = global::MasterCom.RAMS.Properties.Resources.replay;
            this.toolStripTPReplay.Name = "toolStripTPReplay";
            this.toolStripTPReplay.Size = new System.Drawing.Size(150, 22);
            this.toolStripTPReplay.Text = "回放采样点";
            this.toolStripTPReplay.Click += new System.EventHandler(this.miReplayTestpoint_Click);
            // 
            // toolStripTPReplayCompare
            // 
            this.toolStripTPReplayCompare.Name = "toolStripTPReplayCompare";
            this.toolStripTPReplayCompare.Size = new System.Drawing.Size(150, 22);
            this.toolStripTPReplayCompare.Text = "对比回放文件";
            this.toolStripTPReplayCompare.Click += new System.EventHandler(this.miReplayTestpointCompare_Click);
            // 
            // toolStripTPLineChartLocate
            // 
            this.toolStripTPLineChartLocate.Name = "toolStripTPLineChartLocate";
            this.toolStripTPLineChartLocate.Size = new System.Drawing.Size(150, 22);
            this.toolStripTPLineChartLocate.Text = "定位时序图";
            this.toolStripTPLineChartLocate.Click += new System.EventHandler(this.miLocateLineChart_Click);
            // 
            // toolStripTPExportExcel
            // 
            this.toolStripTPExportExcel.Name = "toolStripTPExportExcel";
            this.toolStripTPExportExcel.Size = new System.Drawing.Size(150, 22);
            this.toolStripTPExportExcel.Text = "导出到Excel...";
            this.toolStripTPExportExcel.Click += new System.EventHandler(this.miExportTp_Click);
            // 
            // gridViewTP
            // 
            this.gridViewTP.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridViewTP.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewTP.Appearance.Row.Options.UseTextOptions = true;
            this.gridViewTP.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewTP.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn33,
            this.gridColumn34,
            this.gridColumn35});
            this.gridViewTP.GridControl = this.gridControlTP;
            this.gridViewTP.Name = "gridViewTP";
            this.gridViewTP.OptionsBehavior.Editable = false;
            this.gridViewTP.OptionsView.ShowGroupPanel = false;
            this.gridViewTP.DoubleClick += new System.EventHandler(this.gridViewTP_DoubleClick);
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "序号";
            this.gridColumn15.FieldName = "SN";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 0;
            this.gridColumn15.Width = 61;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "原因";
            this.gridColumn16.FieldName = "reason";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 1;
            this.gridColumn16.Width = 100;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "采样点时间";
            this.gridColumn33.FieldName = "tpTime";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 2;
            this.gridColumn33.Width = 200;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "信号强度";
            this.gridColumn34.FieldName = "rxlev";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 3;
            this.gridColumn34.Width = 134;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "C/I";
            this.gridColumn35.FieldName = "c2i";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 4;
            this.gridColumn35.Width = 134;
            // 
            // GSMOverCoverAnaListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(798, 412);
            this.ContextMenuStrip = this.ctxMenuTP;
            this.Controls.Add(this.xtraTabControl1);
            this.Name = "GSMOverCoverAnaListForm";
            this.Text = "过覆盖分类列表";
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRegion)).EndInit();
            this.ctxMenuRegion.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewRegion)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            this.xtraTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCell)).EndInit();
            this.ctxMenuCell.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewCell)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.comboBoxEditFile.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlTP)).EndInit();
            this.ctxMenuTP.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewTP)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControlRegion;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewRegion;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private System.Windows.Forms.ContextMenuStrip ctxMenuRegion;
        private System.Windows.Forms.ToolStripMenuItem toolStripRegionExportExcel;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraGrid.GridControl gridControlCell;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView gridViewCell;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn6;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn7;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn8;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn9;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn10;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn11;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn12;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn13;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn14;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn18;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn19;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn22;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn24;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn32;
        private System.Windows.Forms.ContextMenuStrip ctxMenuCell;
        private System.Windows.Forms.ToolStripMenuItem toolStripCellExportExcel;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraGrid.GridControl gridControlTP;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewTP;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.ComboBoxEdit comboBoxEditFile;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn girdColumnTotalBler;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumnBlerRate;
        private System.Windows.Forms.ContextMenuStrip ctxMenuTP;
        private System.Windows.Forms.ToolStripMenuItem toolStripTPReplay;
        private System.Windows.Forms.ToolStripMenuItem toolStripTPReplayCompare;
        private System.Windows.Forms.ToolStripMenuItem toolStripTPLineChartLocate;
        private System.Windows.Forms.ToolStripMenuItem toolStripTPExportExcel;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn1;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand1;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand3;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand4;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand5;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand2;
    }
}