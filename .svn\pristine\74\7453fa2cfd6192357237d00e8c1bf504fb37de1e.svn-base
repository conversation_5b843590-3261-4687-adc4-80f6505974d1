﻿using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteSmallStationSettingFormConfig_XJ : ConfigHelper<LteSmallStationSettingFormConfigModel_XJ>
    {
        private static LteSmallStationSettingFormConfig_XJ instance = null;
        public static LteSmallStationSettingFormConfig_XJ Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new LteSmallStationSettingFormConfig_XJ();
                }
                return instance;
            }
        }

        public override string ConfigPath { get; } = $@"{AppDomain.CurrentDomain.BaseDirectory}\config\StationDlgConfig\LteSmallStationSettingDlg.xml";

        public override string LogPath { get; } = @"\BackGroundLog\StationDlgConfig\";
        public override string LogName { get; } = "-LTE小站单验门限设置.txt";

        protected override void loadConfig(XmlConfigFile xcfg, LteSmallStationSettingFormConfigModel_XJ configInfo)
        {
            try
            {
                XmlElement config = xcfg.GetConfig("Configs");
                configInfo.AccessSuccessRate = getValidData(xcfg, config, "AccessSuccessRate", "");
                configInfo.AccessTestCount = getValidData(xcfg, config, "AccessTestCount", 0);
                configInfo.VOLTETestCount = getValidData(xcfg, config, "VOLTETestCount", 0);
                configInfo.VOLTESuccessRate = getValidData(xcfg, config, "VOLTESuccessRate", "");
                configInfo.FTPDownloadThroughput = getValidData(xcfg, config, "FTPDownloadThroughput", "");
                configInfo.FTPUploadThroughput = getValidData(xcfg, config, "FTPUploadThroughput", "");
                configInfo.InDoorAvgRSRP = getValidData(xcfg, config, "InDoorAvgRSRP", "");
                configInfo.InDoorAvgSINR = getValidData(xcfg, config, "InDoorAvgSINR", "");
                configInfo.InDoorDownThroughput = getValidData(xcfg, config, "InDoorDownThroughput", "");
                configInfo.InDoorUpThroughput = getValidData(xcfg, config, "InDoorUpThroughput", "");
                configInfo.AntAvgRSRP = getValidData(xcfg, config, "AntAvgRSRP", "");
                configInfo.AntAvgSINR = getValidData(xcfg, config, "AntAvgSINR", "");
                configInfo.SystemInSwitch = getValidData(xcfg, config, "SystemInSwitch", "");
                configInfo.WeakCoverCheckStandard = getValidData(xcfg, config, "WeakCoverCheckStandard", "");
            }
            catch (Exception ex)
            {
                ErrMsg = $"加载配置出错:{ex.Message}";
            }
        }

        public override void SaveConfig(LteSmallStationSettingFormConfigModel_XJ configInfo)
        {
            try
            {
                var newConfig = new XmlConfigFile();
                XmlElement cfg = newConfig.AddConfig("Configs");
                newConfig.AddItem(cfg, "AccessSuccessRate", configInfo.AccessSuccessRate);
                newConfig.AddItem(cfg, "AccessTestCount", configInfo.AccessTestCount);
                newConfig.AddItem(cfg, "VOLTETestCount", configInfo.VOLTETestCount);
                newConfig.AddItem(cfg, "VOLTESuccessRate", configInfo.VOLTESuccessRate);
                newConfig.AddItem(cfg, "FTPDownloadThroughput", configInfo.FTPDownloadThroughput);
                newConfig.AddItem(cfg, "FTPUploadThroughput", configInfo.FTPUploadThroughput);
                newConfig.AddItem(cfg, "InDoorAvgRSRP", configInfo.InDoorAvgRSRP);
                newConfig.AddItem(cfg, "InDoorAvgSINR", configInfo.InDoorAvgSINR);
                newConfig.AddItem(cfg, "InDoorDownThroughput", configInfo.InDoorDownThroughput);
                newConfig.AddItem(cfg, "InDoorUpThroughput", configInfo.InDoorUpThroughput);
                newConfig.AddItem(cfg, "AntAvgRSRP", configInfo.AntAvgRSRP);
                newConfig.AddItem(cfg, "AntAvgSINR", configInfo.AntAvgSINR);
                newConfig.AddItem(cfg, "SystemInSwitch", configInfo.SystemInSwitch);
                newConfig.AddItem(cfg, "WeakCoverCheckStandard", configInfo.WeakCoverCheckStandard);
                newConfig.Save(ConfigPath);
            }
            catch (Exception ex)
            {
                ErrMsg = $"保存配置出错:{ex.Message}";
            }
        }
    }

    public class LteSmallStationSettingFormConfigModel_XJ : ConfigDataInfo
    {
        public string AccessSuccessRate { get; set; }

        public int AccessTestCount { get; set; }

        public int VOLTETestCount { get; set; }

        public string VOLTESuccessRate { get; set; }

        public string FTPDownloadThroughput { get; set; }

        public string FTPUploadThroughput { get; set; }

        public string InDoorAvgRSRP { get; set; }

        public string InDoorAvgSINR { get; set; }

        public string InDoorDownThroughput { get; set; }

        public string InDoorUpThroughput { get; set; }

        public string AntAvgRSRP { get; set; }

        public string AntAvgSINR { get; set; }

        public string SystemInSwitch { get; set; }

        public string WeakCoverCheckStandard { get; set; }
    }
}
