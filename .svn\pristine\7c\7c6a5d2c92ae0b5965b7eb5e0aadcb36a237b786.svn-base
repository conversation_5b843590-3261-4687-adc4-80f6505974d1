﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTNRScanModCell
{
    public partial class NRScanModCellSettingDlg : BaseDialog
    {
        public NRScanModCellSettingDlg()
        {
            InitializeComponent();
            this.btnOK.Click += BtnOK_Click;
            this.btnCancel.Click += BtnCancel_Click;
        }

        public NRScanModCellCondition GetCondition()
        {
            NRScanModCellCondition cond = new NRScanModCellCondition();
            cond.MinRsrp = (double)numMinRxlev.Value;
            cond.MaxSinr = (double)numMaxSinr.Value;
            cond.DiffRsrp = (double)numDiffRxlev.Value;
            cond.MinSampleCount = (int)numSampleCount.Value;
            string type = cmbModType.SelectedItem.ToString();
            cond.FilterCond.ModX = cond.FilterCond.GetModeType(type);
            return cond;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }
}
