﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.JiLinFocusSet
{
    public partial class GridOrderPeriodPicker : BaseDialog
    {
        public GridOrderPeriodPicker()
        {
            InitializeComponent();
        }

        public DateTime DateFrom
        {
            get { return dtFrom.Value.Date; }
            set { dtFrom.Value = value; }
        }

        public DateTime DateTo
        {
            get { return dtTo.Value.Date.AddDays(1).AddMilliseconds(-1); }
            set { dtTo.Value = value; }
        }

    }
}
