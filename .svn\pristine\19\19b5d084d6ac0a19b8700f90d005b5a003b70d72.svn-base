﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class XtraSetSampleMultiForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.labelControlBand = new DevExpress.XtraEditors.LabelControl();
            this.numRxLevDValue = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.label4 = new System.Windows.Forms.Label();
            this.numRxLevThreshold = new DevExpress.XtraEditors.SpinEdit();
            this.simpleButton1 = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButton2 = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.cbxCoFreqType = new DevExpress.XtraEditors.ComboBoxEdit();
            this.chkCoFreq = new DevExpress.XtraEditors.CheckEdit();
            this.label1 = new System.Windows.Forms.Label();
            this.spinEditLevel = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditInvalidThresold = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.grpMultiCoverage = new System.Windows.Forms.GroupBox();
            this.labelInValidPoint = new DevExpress.XtraEditors.LabelControl();
            this.labelAbsolute = new DevExpress.XtraEditors.LabelControl();
            this.labelRelative = new DevExpress.XtraEditors.LabelControl();
            this.grpFilter = new System.Windows.Forms.GroupBox();
            this.cbxBandType = new DevExpress.XtraEditors.ComboBoxEdit();
            this.label3 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevThreshold.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxCoFreqType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCoFreq.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditLevel.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditInvalidThresold.Properties)).BeginInit();
            this.grpMultiCoverage.SuspendLayout();
            this.grpFilter.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxBandType.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // labelControlBand
            // 
            this.labelControlBand.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControlBand.Appearance.Options.UseFont = true;
            this.labelControlBand.Location = new System.Drawing.Point(120, 26);
            this.labelControlBand.Name = "labelControlBand";
            this.labelControlBand.Size = new System.Drawing.Size(102, 12);
            this.labelControlBand.TabIndex = 3;
            this.labelControlBand.Text = "与最强信号差异 ≤";
            // 
            // numRxLevDValue
            // 
            this.numRxLevDValue.EditValue = new decimal(new int[] {
            6,
            0,
            0,
            0});
            this.numRxLevDValue.Location = new System.Drawing.Point(229, 22);
            this.numRxLevDValue.Name = "numRxLevDValue";
            this.numRxLevDValue.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numRxLevDValue.Properties.Appearance.Options.UseFont = true;
            this.numRxLevDValue.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRxLevDValue.Properties.IsFloatValue = false;
            this.numRxLevDValue.Properties.Mask.AutoComplete = DevExpress.XtraEditors.Mask.AutoCompleteType.None;
            this.numRxLevDValue.Properties.Mask.EditMask = "N00";
            this.numRxLevDValue.Size = new System.Drawing.Size(61, 20);
            this.numRxLevDValue.TabIndex = 4;
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(299, 25);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(12, 12);
            this.labelControl1.TabIndex = 5;
            this.labelControl1.Text = "dB";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.Location = new System.Drawing.Point(151, 53);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(71, 12);
            this.label4.TabIndex = 8;
            this.label4.Text = "信号强度 ≥";
            // 
            // numRxLevThreshold
            // 
            this.numRxLevThreshold.EditValue = new decimal(new int[] {
            80,
            0,
            0,
            -2147483648});
            this.numRxLevThreshold.Location = new System.Drawing.Point(229, 50);
            this.numRxLevThreshold.Name = "numRxLevThreshold";
            this.numRxLevThreshold.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numRxLevThreshold.Properties.Appearance.Options.UseFont = true;
            this.numRxLevThreshold.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRxLevThreshold.Properties.IsFloatValue = false;
            this.numRxLevThreshold.Properties.Mask.EditMask = "N00";
            this.numRxLevThreshold.Size = new System.Drawing.Size(61, 20);
            this.numRxLevThreshold.TabIndex = 9;
            // 
            // simpleButton1
            // 
            this.simpleButton1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButton1.Appearance.Options.UseFont = true;
            this.simpleButton1.Location = new System.Drawing.Point(188, 297);
            this.simpleButton1.Name = "simpleButton1";
            this.simpleButton1.Size = new System.Drawing.Size(75, 23);
            this.simpleButton1.TabIndex = 10;
            this.simpleButton1.Text = "确定";
            this.simpleButton1.Click += new System.EventHandler(this.simpleButton1_Click);
            // 
            // simpleButton2
            // 
            this.simpleButton2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButton2.Appearance.Options.UseFont = true;
            this.simpleButton2.Location = new System.Drawing.Point(283, 297);
            this.simpleButton2.Name = "simpleButton2";
            this.simpleButton2.Size = new System.Drawing.Size(75, 23);
            this.simpleButton2.TabIndex = 11;
            this.simpleButton2.Text = "取消";
            this.simpleButton2.Click += new System.EventHandler(this.simpleButton2_Click);
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(299, 53);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(18, 12);
            this.labelControl2.TabIndex = 12;
            this.labelControl2.Text = "dBm";
            // 
            // cbxCoFreqType
            // 
            this.cbxCoFreqType.Location = new System.Drawing.Point(105, 85);
            this.cbxCoFreqType.Name = "cbxCoFreqType";
            this.cbxCoFreqType.Properties.Appearance.BackColor = System.Drawing.Color.White;
            this.cbxCoFreqType.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbxCoFreqType.Properties.Appearance.Options.UseBackColor = true;
            this.cbxCoFreqType.Properties.Appearance.Options.UseFont = true;
            this.cbxCoFreqType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxCoFreqType.Properties.Items.AddRange(new object[] {
            "BCCH&TCH",
            "BCCH Only",
            "TCH Only"});
            this.cbxCoFreqType.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxCoFreqType.Size = new System.Drawing.Size(116, 20);
            this.cbxCoFreqType.TabIndex = 16;
            // 
            // chkCoFreq
            // 
            this.chkCoFreq.Location = new System.Drawing.Point(39, 85);
            this.chkCoFreq.Name = "chkCoFreq";
            this.chkCoFreq.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkCoFreq.Properties.Appearance.Options.UseFont = true;
            this.chkCoFreq.Properties.Caption = "同频";
            this.chkCoFreq.Size = new System.Drawing.Size(57, 19);
            this.chkCoFreq.TabIndex = 15;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label1.Location = new System.Drawing.Point(39, 57);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(59, 12);
            this.label1.TabIndex = 8;
            this.label1.Text = "覆盖度 ≥";
            // 
            // spinEditLevel
            // 
            this.spinEditLevel.EditValue = new decimal(new int[] {
            7,
            0,
            0,
            0});
            this.spinEditLevel.Location = new System.Drawing.Point(105, 54);
            this.spinEditLevel.Name = "spinEditLevel";
            this.spinEditLevel.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditLevel.Properties.Appearance.Options.UseFont = true;
            this.spinEditLevel.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditLevel.Properties.IsFloatValue = false;
            this.spinEditLevel.Properties.Mask.EditMask = "N00";
            this.spinEditLevel.Size = new System.Drawing.Size(61, 20);
            this.spinEditLevel.TabIndex = 9;
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(299, 81);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(18, 12);
            this.labelControl5.TabIndex = 19;
            this.labelControl5.Text = "dBm";
            // 
            // spinEditInvalidThresold
            // 
            this.spinEditInvalidThresold.EditValue = new decimal(new int[] {
            95,
            0,
            0,
            -2147483648});
            this.spinEditInvalidThresold.Location = new System.Drawing.Point(229, 78);
            this.spinEditInvalidThresold.Name = "spinEditInvalidThresold";
            this.spinEditInvalidThresold.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditInvalidThresold.Properties.Appearance.Options.UseFont = true;
            this.spinEditInvalidThresold.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditInvalidThresold.Properties.IsFloatValue = false;
            this.spinEditInvalidThresold.Properties.Mask.EditMask = "N00";
            this.spinEditInvalidThresold.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.spinEditInvalidThresold.Properties.MinValue = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.spinEditInvalidThresold.Size = new System.Drawing.Size(61, 20);
            this.spinEditInvalidThresold.TabIndex = 17;
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(162, 80);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(60, 12);
            this.labelControl4.TabIndex = 18;
            this.labelControl4.Text = "最强信号 <";
            // 
            // grpMultiCoverage
            // 
            this.grpMultiCoverage.Controls.Add(this.labelInValidPoint);
            this.grpMultiCoverage.Controls.Add(this.labelAbsolute);
            this.grpMultiCoverage.Controls.Add(this.labelControl5);
            this.grpMultiCoverage.Controls.Add(this.labelRelative);
            this.grpMultiCoverage.Controls.Add(this.spinEditInvalidThresold);
            this.grpMultiCoverage.Controls.Add(this.numRxLevDValue);
            this.grpMultiCoverage.Controls.Add(this.labelControl4);
            this.grpMultiCoverage.Controls.Add(this.labelControlBand);
            this.grpMultiCoverage.Controls.Add(this.labelControl1);
            this.grpMultiCoverage.Controls.Add(this.label4);
            this.grpMultiCoverage.Controls.Add(this.numRxLevThreshold);
            this.grpMultiCoverage.Controls.Add(this.labelControl2);
            this.grpMultiCoverage.Location = new System.Drawing.Point(12, 12);
            this.grpMultiCoverage.Name = "grpMultiCoverage";
            this.grpMultiCoverage.Size = new System.Drawing.Size(346, 117);
            this.grpMultiCoverage.TabIndex = 24;
            this.grpMultiCoverage.TabStop = false;
            this.grpMultiCoverage.Text = "重叠覆盖度设置";
            // 
            // labelInValidPoint
            // 
            this.labelInValidPoint.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelInValidPoint.Appearance.Options.UseFont = true;
            this.labelInValidPoint.Location = new System.Drawing.Point(42, 80);
            this.labelInValidPoint.Name = "labelInValidPoint";
            this.labelInValidPoint.Size = new System.Drawing.Size(72, 12);
            this.labelInValidPoint.TabIndex = 13;
            this.labelInValidPoint.Text = "无效点定义：";
            // 
            // labelAbsolute
            // 
            this.labelAbsolute.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelAbsolute.Appearance.Options.UseFont = true;
            this.labelAbsolute.Location = new System.Drawing.Point(42, 53);
            this.labelAbsolute.Name = "labelAbsolute";
            this.labelAbsolute.Size = new System.Drawing.Size(72, 12);
            this.labelAbsolute.TabIndex = 7;
            this.labelAbsolute.Text = "绝对覆盖带：";
            // 
            // labelRelative
            // 
            this.labelRelative.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelRelative.Appearance.Options.UseFont = true;
            this.labelRelative.Location = new System.Drawing.Point(42, 26);
            this.labelRelative.Name = "labelRelative";
            this.labelRelative.Size = new System.Drawing.Size(72, 12);
            this.labelRelative.TabIndex = 6;
            this.labelRelative.Text = "相对覆盖带：";
            // 
            // grpFilter
            // 
            this.grpFilter.Controls.Add(this.cbxBandType);
            this.grpFilter.Controls.Add(this.label3);
            this.grpFilter.Controls.Add(this.label1);
            this.grpFilter.Controls.Add(this.spinEditLevel);
            this.grpFilter.Controls.Add(this.chkCoFreq);
            this.grpFilter.Controls.Add(this.cbxCoFreqType);
            this.grpFilter.Location = new System.Drawing.Point(13, 143);
            this.grpFilter.Name = "grpFilter";
            this.grpFilter.Size = new System.Drawing.Size(345, 124);
            this.grpFilter.TabIndex = 25;
            this.grpFilter.TabStop = false;
            this.grpFilter.Text = "筛选条件";
            // 
            // cbxBandType
            // 
            this.cbxBandType.Location = new System.Drawing.Point(105, 21);
            this.cbxBandType.Name = "cbxBandType";
            this.cbxBandType.Properties.Appearance.BackColor = System.Drawing.Color.White;
            this.cbxBandType.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbxBandType.Properties.Appearance.Options.UseBackColor = true;
            this.cbxBandType.Properties.Appearance.Options.UseFont = true;
            this.cbxBandType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxBandType.Properties.Items.AddRange(new object[] {
            "总体",
            "900",
            "1800"});
            this.cbxBandType.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxBandType.Size = new System.Drawing.Size(116, 20);
            this.cbxBandType.TabIndex = 19;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label3.Location = new System.Drawing.Point(39, 26);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(29, 12);
            this.label3.TabIndex = 18;
            this.label3.Text = "频段";
            // 
            // XtraSetSampleMultiForm
            // 
            this.Appearance.BackColor = System.Drawing.SystemColors.Control;
            this.Appearance.Options.UseBackColor = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(373, 342);
            this.Controls.Add(this.grpFilter);
            this.Controls.Add(this.grpMultiCoverage);
            this.Controls.Add(this.simpleButton2);
            this.Controls.Add(this.simpleButton1);
            this.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "XtraSetSampleMultiForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "采样点重叠覆盖度分析条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevDValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevThreshold.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxCoFreqType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCoFreq.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditLevel.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditInvalidThresold.Properties)).EndInit();
            this.grpMultiCoverage.ResumeLayout(false);
            this.grpMultiCoverage.PerformLayout();
            this.grpFilter.ResumeLayout(false);
            this.grpFilter.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxBandType.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControlBand;
        private DevExpress.XtraEditors.SpinEdit numRxLevDValue;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private System.Windows.Forms.Label label4;
        private DevExpress.XtraEditors.SpinEdit numRxLevThreshold;
        private DevExpress.XtraEditors.SimpleButton simpleButton1;
        private DevExpress.XtraEditors.SimpleButton simpleButton2;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.ComboBoxEdit cbxCoFreqType;
        private DevExpress.XtraEditors.CheckEdit chkCoFreq;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.SpinEdit spinEditLevel;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.SpinEdit spinEditInvalidThresold;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private System.Windows.Forms.GroupBox grpMultiCoverage;
        private DevExpress.XtraEditors.LabelControl labelInValidPoint;
        private DevExpress.XtraEditors.LabelControl labelAbsolute;
        private DevExpress.XtraEditors.LabelControl labelRelative;
        private System.Windows.Forms.GroupBox grpFilter;
        private System.Windows.Forms.Label label3;
        private DevExpress.XtraEditors.ComboBoxEdit cbxBandType;
    }
}