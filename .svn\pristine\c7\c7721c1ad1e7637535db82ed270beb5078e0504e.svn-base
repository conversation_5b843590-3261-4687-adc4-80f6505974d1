﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRHandOverAnaListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(NRHandOverAnaListForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gcDetail = new DevExpress.XtraGrid.GridControl();
            this.gv = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand1 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand2 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn10 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand4 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn19 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand3 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn26 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn36 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand5 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn31 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gcDetail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplay,
            this.miExportExcel});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(130, 48);
            // 
            // miReplay
            // 
            this.miReplay.Name = "miReplay";
            this.miReplay.Size = new System.Drawing.Size(129, 22);
            this.miReplay.Text = "回放";
            this.miReplay.Click += new System.EventHandler(this.miReplay_Click);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // gcDetail
            // 
            this.gcDetail.ContextMenuStrip = this.ctxMenu;
            this.gcDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcDetail.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gcDetail.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gcDetail.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gcDetail.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gcDetail.EmbeddedNavigator.Buttons.NextPage.Visible = false;
            this.gcDetail.EmbeddedNavigator.Buttons.PrevPage.Visible = false;
            this.gcDetail.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gcDetail.Location = new System.Drawing.Point(0, 0);
            this.gcDetail.MainView = this.gv;
            this.gcDetail.Name = "gcDetail";
            this.gcDetail.ShowOnlyPredefinedDetails = true;
            this.gcDetail.Size = new System.Drawing.Size(1354, 671);
            this.gcDetail.TabIndex = 8;
            this.gcDetail.UseEmbeddedNavigator = true;
            this.gcDetail.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gv});
            // 
            // gv
            // 
            this.gv.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand1,
            this.gridBand2,
            this.gridBand4,
            this.gridBand3,
            this.gridBand5});
            this.gv.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn28,
            this.gridColumn29,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn26,
            this.gridColumn27,
            this.gridColumn17,
            this.gridColumn30,
            this.gridColumn34,
            this.gridColumn14,
            this.gridColumn36,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn37,
            this.gridColumn31,
            this.gridColumn32,
            this.gridColumn18,
            this.gridColumn33,
            this.gridColumn35,
            this.gridColumn24,
            this.gridColumn25});
            this.gv.GridControl = this.gcDetail;
            this.gv.Name = "gv";
            this.gv.OptionsBehavior.Editable = false;
            this.gv.OptionsDetail.ShowDetailTabs = false;
            this.gv.OptionsView.ColumnAutoWidth = false;
            this.gv.OptionsView.ShowGroupPanel = false;
            this.gv.OptionsView.ShowIndicator = false;
            // 
            // gridBand1
            // 
            this.gridBand1.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand1.Caption = "基础指标";
            this.gridBand1.Columns.Add(this.gridColumn1);
            this.gridBand1.Columns.Add(this.gridColumn2);
            this.gridBand1.Columns.Add(this.gridColumn3);
            this.gridBand1.Columns.Add(this.gridColumn4);
            this.gridBand1.Columns.Add(this.gridColumn5);
            this.gridBand1.Columns.Add(this.gridColumn6);
            this.gridBand1.Columns.Add(this.gridColumn28);
            this.gridBand1.Columns.Add(this.gridColumn29);
            this.gridBand1.MinWidth = 20;
            this.gridBand1.Name = "gridBand1";
            this.gridBand1.Width = 657;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "序号";
            this.gridColumn1.FieldName = "SN";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "文件名称";
            this.gridColumn2.FieldName = "FileName";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.Width = 121;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "网格名称";
            this.gridColumn3.FieldName = "GridName";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "时间";
            this.gridColumn4.FieldName = "DateTime";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.Width = 112;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "经度";
            this.gridColumn5.FieldName = "Longitude";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.Width = 97;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "纬度";
            this.gridColumn6.FieldName = "Latitude";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.Width = 102;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "切换类型";
            this.gridColumn28.FieldName = "HandOverType";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "切换合理性分析";
            this.gridColumn29.FieldName = "HandOverReasonable";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.Width = 150;
            // 
            // gridBand2
            // 
            this.gridBand2.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand2.Caption = "切换前NR指标";
            this.gridBand2.Columns.Add(this.gridColumn10);
            this.gridBand2.Columns.Add(this.gridColumn11);
            this.gridBand2.Columns.Add(this.gridColumn12);
            this.gridBand2.Columns.Add(this.gridColumn13);
            this.gridBand2.Columns.Add(this.gridColumn15);
            this.gridBand2.Columns.Add(this.gridColumn16);
            this.gridBand2.MinWidth = 20;
            this.gridBand2.Name = "gridBand2";
            this.gridBand2.Width = 582;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "源NR小区";
            this.gridColumn10.FieldName = "NrSrcCellItem.CellName";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.Width = 118;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "源NR ARFCN";
            this.gridColumn11.FieldName = "NrSrcCellItem.EARFCN";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.Width = 93;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "源NR PCI";
            this.gridColumn12.FieldName = "NrSrcCellItem.PCI";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "源NR小区与事件距离";
            this.gridColumn13.FieldName = "NrSrcCellItem.Distance";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.Width = 138;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "前NR RSRP";
            this.gridColumn15.FieldName = "NrSrcCellItem.RsrpInfo.AvgDesc";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.Width = 79;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "前NR SINR";
            this.gridColumn16.FieldName = "NrSrcCellItem.SinrInfo.AvgDesc";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.Width = 79;
            // 
            // gridBand4
            // 
            this.gridBand4.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand4.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand4.Caption = "切换后NR指标";
            this.gridBand4.Columns.Add(this.gridColumn19);
            this.gridBand4.Columns.Add(this.gridColumn20);
            this.gridBand4.Columns.Add(this.gridColumn21);
            this.gridBand4.Columns.Add(this.gridColumn22);
            this.gridBand4.Columns.Add(this.gridColumn23);
            this.gridBand4.Columns.Add(this.gridColumn37);
            this.gridBand4.MinWidth = 20;
            this.gridBand4.Name = "gridBand4";
            this.gridBand4.Width = 644;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "目的NR小区";
            this.gridColumn19.FieldName = "NrDestCellItem.CellName";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.Width = 127;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "目的NR ARFCN";
            this.gridColumn20.FieldName = "NrDestCellItem.EARFCN";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.Width = 103;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "目的NR PCI";
            this.gridColumn21.FieldName = "NrDestCellItem.PCI";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.Width = 95;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "目的NR小区与事件距离";
            this.gridColumn22.FieldName = "NrDestCellItem.Distance";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.Width = 156;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "后NR RSRP";
            this.gridColumn23.FieldName = "NrDestCellItem.RsrpInfo.AvgDesc";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.Width = 88;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "后NR SINR";
            this.gridColumn37.FieldName = "NrDestCellItem.SinrInfo.AvgDesc";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.Visible = true;
            // 
            // gridBand3
            // 
            this.gridBand3.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand3.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand3.Caption = "切换前LTE指标";
            this.gridBand3.Columns.Add(this.gridColumn26);
            this.gridBand3.Columns.Add(this.gridColumn27);
            this.gridBand3.Columns.Add(this.gridColumn17);
            this.gridBand3.Columns.Add(this.gridColumn30);
            this.gridBand3.Columns.Add(this.gridColumn34);
            this.gridBand3.Columns.Add(this.gridColumn14);
            this.gridBand3.Columns.Add(this.gridColumn36);
            this.gridBand3.MinWidth = 20;
            this.gridBand3.Name = "gridBand3";
            this.gridBand3.Width = 707;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "源LTE小区";
            this.gridColumn26.FieldName = "LteSrcCellItem.CellName";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.Width = 123;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "源LTE EARFCN";
            this.gridColumn27.FieldName = "LteSrcCellItem.EARFCN";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.Width = 103;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "源LTE频段";
            this.gridColumn17.FieldName = "LteSrcCellItem.FreqBand";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.Width = 81;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "源LTE PCI";
            this.gridColumn30.FieldName = "LteSrcCellItem.PCI";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.Width = 83;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "源LTE小区与事件距离";
            this.gridColumn34.FieldName = "LteSrcCellItem.Distance";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.Width = 144;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "前LTE RSRP";
            this.gridColumn14.FieldName = "LteSrcCellItem.RsrpInfo.AvgDesc";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.Width = 86;
            // 
            // gridColumn36
            // 
            this.gridColumn36.Caption = "前LTE SINR";
            this.gridColumn36.FieldName = "LteSrcCellItem.SinrInfo.AvgDesc";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.Visible = true;
            this.gridColumn36.Width = 87;
            // 
            // gridBand5
            // 
            this.gridBand5.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand5.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand5.Caption = "切换后LTE指标";
            this.gridBand5.Columns.Add(this.gridColumn31);
            this.gridBand5.Columns.Add(this.gridColumn32);
            this.gridBand5.Columns.Add(this.gridColumn18);
            this.gridBand5.Columns.Add(this.gridColumn33);
            this.gridBand5.Columns.Add(this.gridColumn35);
            this.gridBand5.Columns.Add(this.gridColumn24);
            this.gridBand5.Columns.Add(this.gridColumn25);
            this.gridBand5.MinWidth = 20;
            this.gridBand5.Name = "gridBand5";
            this.gridBand5.Width = 775;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "目的LTE小区";
            this.gridColumn31.FieldName = "LteDestCellItem.CellName";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.Width = 123;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "目的LTE EARFCN";
            this.gridColumn32.FieldName = "LteDestCellItem.EARFCN";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.Width = 124;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "目的LTE频段";
            this.gridColumn18.FieldName = "LteDestCellItem.FreqBand";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.Width = 96;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "目的LTE PCI";
            this.gridColumn33.FieldName = "LteDestCellItem.PCI";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.Width = 100;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "目的LTE小区与事件距离";
            this.gridColumn35.FieldName = "LteDestCellItem.Distance";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.Width = 158;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "后LTE RSRP";
            this.gridColumn24.FieldName = "LteDestCellItem.RsrpInfo.AvgDesc";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.Width = 91;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "后LTE SINR";
            this.gridColumn25.FieldName = "LteDestCellItem.SinrInfo.AvgDesc";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.Width = 83;
            // 
            // NRHandOverAnaListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1354, 671);
            this.Controls.Add(this.gcDetail);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "NRHandOverAnaListForm";
            this.Text = "切换前后指标分析结果";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gcDetail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripMenuItem miReplay;
        private DevExpress.XtraGrid.GridControl gcDetail;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView gv;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn5;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn6;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn28;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn29;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn10;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn11;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn12;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn13;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn15;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn16;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn19;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn21;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn20;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn22;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn23;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn37;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn26;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn27;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn17;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn30;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn34;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn14;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn36;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand5;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn31;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn32;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn18;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn33;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn35;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn24;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn25;
    }
}