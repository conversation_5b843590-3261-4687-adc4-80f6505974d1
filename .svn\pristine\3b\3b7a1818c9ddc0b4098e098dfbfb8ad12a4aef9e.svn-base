﻿namespace MasterCom.MapSpaceManager
{
    partial class MainForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MainForm));
            System.Windows.Forms.TreeNode treeNode1 = new System.Windows.Forms.TreeNode("地图");
            this.toolStripMap = new System.Windows.Forms.ToolStrip();
            this.btnFullExtent = new System.Windows.Forms.ToolStripButton();
            this.menuStrip = new System.Windows.Forms.MenuStrip();
            this.miFile = new System.Windows.Forms.ToolStripMenuItem();
            this.miOpenMWS = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miSaveMWS = new System.Windows.Forms.ToolStripMenuItem();
            this.miSaveAsMWS = new System.Windows.Forms.ToolStripMenuItem();
            this.splitContainer = new System.Windows.Forms.SplitContainer();
            this.splitContainerSetting = new System.Windows.Forms.SplitContainer();
            this.treeViewMapLayer = new System.Windows.Forms.TreeView();
            this.toolStripSetting = new System.Windows.Forms.ToolStrip();
            this.tsBtnNewSpace = new System.Windows.Forms.ToolStripButton();
            this.btnSaveIt = new System.Windows.Forms.ToolStripButton();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.tsBtnAddLayer = new System.Windows.Forms.ToolStripButton();
            this.tsBtnRemoveLayer = new System.Windows.Forms.ToolStripButton();
            this.miBtnUp = new System.Windows.Forms.ToolStripButton();
            this.miBtnDown = new System.Windows.Forms.ToolStripButton();
            this.layerInfoPanel = new MasterCom.MapSpaceManager.MapSpaceManager.LayerInfoPanel();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.txtCurMinLat = new System.Windows.Forms.TextBox();
            this.txtCurMinLong = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.txtCurMaxLat = new System.Windows.Forms.TextBox();
            this.label7 = new System.Windows.Forms.Label();
            this.txtCurMaxLong = new System.Windows.Forms.TextBox();
            this.label8 = new System.Windows.Forms.Label();
            this.mapControl = new AxMapWinGIS.AxMap();
            this.statusStrip = new System.Windows.Forms.StatusStrip();
            this.toolStripMap.SuspendLayout();
            this.menuStrip.SuspendLayout();
            this.splitContainer.Panel1.SuspendLayout();
            this.splitContainer.Panel2.SuspendLayout();
            this.splitContainer.SuspendLayout();
            this.splitContainerSetting.Panel1.SuspendLayout();
            this.splitContainerSetting.Panel2.SuspendLayout();
            this.splitContainerSetting.SuspendLayout();
            this.toolStripSetting.SuspendLayout();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.mapControl)).BeginInit();
            this.SuspendLayout();
            // 
            // toolStripMap
            // 
            this.toolStripMap.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.btnFullExtent});
            this.toolStripMap.Location = new System.Drawing.Point(0, 0);
            this.toolStripMap.Name = "toolStripMap";
            this.toolStripMap.Size = new System.Drawing.Size(617, 25);
            this.toolStripMap.TabIndex = 0;
            this.toolStripMap.Text = "toolStrip1";
            // 
            // btnFullExtent
            // 
            this.btnFullExtent.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnFullExtent.Image = ((System.Drawing.Image)(resources.GetObject("btnFullExtent.Image")));
            this.btnFullExtent.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnFullExtent.Name = "btnFullExtent";
            this.btnFullExtent.Size = new System.Drawing.Size(23, 22);
            this.btnFullExtent.Text = "全图";
            this.btnFullExtent.Click += new System.EventHandler(this.btnFullExtent_Click);
            // 
            // menuStrip
            // 
            this.menuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miFile});
            this.menuStrip.Location = new System.Drawing.Point(0, 0);
            this.menuStrip.Name = "menuStrip";
            this.menuStrip.Size = new System.Drawing.Size(1008, 24);
            this.menuStrip.TabIndex = 1;
            this.menuStrip.Text = "菜单栏";
            // 
            // miFile
            // 
            this.miFile.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miOpenMWS,
            this.toolStripMenuItem1,
            this.miSaveMWS,
            this.miSaveAsMWS});
            this.miFile.Name = "miFile";
            this.miFile.Size = new System.Drawing.Size(43, 20);
            this.miFile.Text = "文件";
            // 
            // miOpenMWS
            // 
            this.miOpenMWS.Name = "miOpenMWS";
            this.miOpenMWS.Size = new System.Drawing.Size(167, 22);
            this.miOpenMWS.Text = "打开地图空间...";
            this.miOpenMWS.Click += new System.EventHandler(this.miOpenMWS_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(164, 6);
            // 
            // miSaveMWS
            // 
            this.miSaveMWS.Name = "miSaveMWS";
            this.miSaveMWS.Size = new System.Drawing.Size(167, 22);
            this.miSaveMWS.Text = "保存地图空间";
            this.miSaveMWS.Click += new System.EventHandler(this.miSaveMWS_Click);
            // 
            // miSaveAsMWS
            // 
            this.miSaveAsMWS.Name = "miSaveAsMWS";
            this.miSaveAsMWS.Size = new System.Drawing.Size(167, 22);
            this.miSaveAsMWS.Text = "地图空间另存为...";
            this.miSaveAsMWS.Click += new System.EventHandler(this.miSaveAsMWS_Click);
            // 
            // splitContainer
            // 
            this.splitContainer.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.splitContainer.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer.FixedPanel = System.Windows.Forms.FixedPanel.Panel1;
            this.splitContainer.Location = new System.Drawing.Point(0, 24);
            this.splitContainer.Name = "splitContainer";
            // 
            // splitContainer.Panel1
            // 
            this.splitContainer.Panel1.AutoScroll = true;
            this.splitContainer.Panel1.Controls.Add(this.splitContainerSetting);
            this.splitContainer.Panel1MinSize = 385;
            // 
            // splitContainer.Panel2
            // 
            this.splitContainer.Panel2.Controls.Add(this.toolStripMap);
            this.splitContainer.Panel2.Controls.Add(this.mapControl);
            this.splitContainer.Size = new System.Drawing.Size(1008, 700);
            this.splitContainer.SplitterDistance = 385;
            this.splitContainer.TabIndex = 2;
            // 
            // splitContainerSetting
            // 
            this.splitContainerSetting.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.splitContainerSetting.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerSetting.Location = new System.Drawing.Point(0, 0);
            this.splitContainerSetting.Name = "splitContainerSetting";
            this.splitContainerSetting.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainerSetting.Panel1
            // 
            this.splitContainerSetting.Panel1.AutoScroll = true;
            this.splitContainerSetting.Panel1.Controls.Add(this.treeViewMapLayer);
            this.splitContainerSetting.Panel1.Controls.Add(this.toolStripSetting);
            // 
            // splitContainerSetting.Panel2
            // 
            this.splitContainerSetting.Panel2.Controls.Add(this.layerInfoPanel);
            this.splitContainerSetting.Panel2.Controls.Add(this.groupBox2);
            this.splitContainerSetting.Size = new System.Drawing.Size(385, 700);
            this.splitContainerSetting.SplitterDistance = 433;
            this.splitContainerSetting.TabIndex = 0;
            // 
            // treeViewMapLayer
            // 
            this.treeViewMapLayer.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeViewMapLayer.HideSelection = false;
            this.treeViewMapLayer.ItemHeight = 20;
            this.treeViewMapLayer.Location = new System.Drawing.Point(0, 25);
            this.treeViewMapLayer.Name = "treeViewMapLayer";
            treeNode1.Name = "mapRootNode";
            treeNode1.Text = "地图";
            this.treeViewMapLayer.Nodes.AddRange(new System.Windows.Forms.TreeNode[] {
            treeNode1});
            this.treeViewMapLayer.Size = new System.Drawing.Size(383, 406);
            this.treeViewMapLayer.TabIndex = 0;
            this.treeViewMapLayer.AfterSelect += new System.Windows.Forms.TreeViewEventHandler(this.treeViewMapLayer_AfterSelect);
            // 
            // toolStripSetting
            // 
            this.toolStripSetting.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsBtnNewSpace,
            this.btnSaveIt,
            this.toolStripSeparator1,
            this.tsBtnAddLayer,
            this.tsBtnRemoveLayer,
            this.miBtnUp,
            this.miBtnDown});
            this.toolStripSetting.Location = new System.Drawing.Point(0, 0);
            this.toolStripSetting.Name = "toolStripSetting";
            this.toolStripSetting.Size = new System.Drawing.Size(383, 25);
            this.toolStripSetting.TabIndex = 1;
            this.toolStripSetting.Text = "toolStrip1";
            // 
            // tsBtnNewSpace
            // 
            this.tsBtnNewSpace.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsBtnNewSpace.Image = ((System.Drawing.Image)(resources.GetObject("tsBtnNewSpace.Image")));
            this.tsBtnNewSpace.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsBtnNewSpace.Name = "tsBtnNewSpace";
            this.tsBtnNewSpace.Size = new System.Drawing.Size(23, 22);
            this.tsBtnNewSpace.Text = "新建地图空间";
            this.tsBtnNewSpace.Click += new System.EventHandler(this.tsBtnNewSpace_Click);
            // 
            // btnSaveIt
            // 
            this.btnSaveIt.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.btnSaveIt.Image = ((System.Drawing.Image)(resources.GetObject("btnSaveIt.Image")));
            this.btnSaveIt.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnSaveIt.Name = "btnSaveIt";
            this.btnSaveIt.Size = new System.Drawing.Size(23, 22);
            this.btnSaveIt.Text = "保存";
            this.btnSaveIt.Click += new System.EventHandler(this.btnSaveIt_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(6, 25);
            // 
            // tsBtnAddLayer
            // 
            this.tsBtnAddLayer.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsBtnAddLayer.Image = ((System.Drawing.Image)(resources.GetObject("tsBtnAddLayer.Image")));
            this.tsBtnAddLayer.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsBtnAddLayer.Name = "tsBtnAddLayer";
            this.tsBtnAddLayer.Size = new System.Drawing.Size(23, 22);
            this.tsBtnAddLayer.Text = "添加图层";
            this.tsBtnAddLayer.Click += new System.EventHandler(this.tsBtnAddLayer_Click);
            // 
            // tsBtnRemoveLayer
            // 
            this.tsBtnRemoveLayer.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.tsBtnRemoveLayer.Enabled = false;
            this.tsBtnRemoveLayer.Image = ((System.Drawing.Image)(resources.GetObject("tsBtnRemoveLayer.Image")));
            this.tsBtnRemoveLayer.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.tsBtnRemoveLayer.Name = "tsBtnRemoveLayer";
            this.tsBtnRemoveLayer.Size = new System.Drawing.Size(23, 22);
            this.tsBtnRemoveLayer.Text = "移除图层";
            this.tsBtnRemoveLayer.Click += new System.EventHandler(this.tsBtnRemoveLayer_Click);
            // 
            // miBtnUp
            // 
            this.miBtnUp.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.miBtnUp.Enabled = false;
            this.miBtnUp.Image = ((System.Drawing.Image)(resources.GetObject("miBtnUp.Image")));
            this.miBtnUp.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.miBtnUp.Name = "miBtnUp";
            this.miBtnUp.Size = new System.Drawing.Size(23, 22);
            this.miBtnUp.Text = "上一层";
            this.miBtnUp.Click += new System.EventHandler(this.miBtnUp_Click);
            // 
            // miBtnDown
            // 
            this.miBtnDown.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
            this.miBtnDown.Enabled = false;
            this.miBtnDown.Image = ((System.Drawing.Image)(resources.GetObject("miBtnDown.Image")));
            this.miBtnDown.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.miBtnDown.Name = "miBtnDown";
            this.miBtnDown.Size = new System.Drawing.Size(23, 22);
            this.miBtnDown.Text = "下一层";
            this.miBtnDown.Click += new System.EventHandler(this.miBtnDown_Click);
            // 
            // layerInfoPanel
            // 
            this.layerInfoPanel.AutoScroll = true;
            this.layerInfoPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.layerInfoPanel.Location = new System.Drawing.Point(0, 0);
            this.layerInfoPanel.Name = "layerInfoPanel";
            this.layerInfoPanel.Size = new System.Drawing.Size(383, 261);
            this.layerInfoPanel.TabIndex = 0;
            this.layerInfoPanel.Visible = false;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.txtCurMinLat);
            this.groupBox2.Controls.Add(this.txtCurMinLong);
            this.groupBox2.Controls.Add(this.label3);
            this.groupBox2.Controls.Add(this.label6);
            this.groupBox2.Controls.Add(this.txtCurMaxLat);
            this.groupBox2.Controls.Add(this.label7);
            this.groupBox2.Controls.Add(this.txtCurMaxLong);
            this.groupBox2.Controls.Add(this.label8);
            this.groupBox2.Location = new System.Drawing.Point(3, 3);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(375, 100);
            this.groupBox2.TabIndex = 2;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "当前显示区域";
            // 
            // txtCurMinLat
            // 
            this.txtCurMinLat.Location = new System.Drawing.Point(69, 65);
            this.txtCurMinLat.Name = "txtCurMinLat";
            this.txtCurMinLat.ReadOnly = true;
            this.txtCurMinLat.Size = new System.Drawing.Size(100, 21);
            this.txtCurMinLat.TabIndex = 1;
            // 
            // txtCurMinLong
            // 
            this.txtCurMinLong.Location = new System.Drawing.Point(69, 20);
            this.txtCurMinLong.Name = "txtCurMinLong";
            this.txtCurMinLong.ReadOnly = true;
            this.txtCurMinLong.Size = new System.Drawing.Size(100, 21);
            this.txtCurMinLong.TabIndex = 1;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(205, 68);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(59, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "最大纬度:";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(11, 68);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(59, 12);
            this.label6.TabIndex = 0;
            this.label6.Text = "最小纬度:";
            // 
            // txtCurMaxLat
            // 
            this.txtCurMaxLat.Location = new System.Drawing.Point(269, 65);
            this.txtCurMaxLat.Name = "txtCurMaxLat";
            this.txtCurMaxLat.ReadOnly = true;
            this.txtCurMaxLat.Size = new System.Drawing.Size(100, 21);
            this.txtCurMaxLat.TabIndex = 1;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(11, 24);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(59, 12);
            this.label7.TabIndex = 0;
            this.label7.Text = "最小经度:";
            // 
            // txtCurMaxLong
            // 
            this.txtCurMaxLong.Location = new System.Drawing.Point(269, 20);
            this.txtCurMaxLong.Name = "txtCurMaxLong";
            this.txtCurMaxLong.ReadOnly = true;
            this.txtCurMaxLong.Size = new System.Drawing.Size(100, 21);
            this.txtCurMaxLong.TabIndex = 1;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(205, 24);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(59, 12);
            this.label8.TabIndex = 0;
            this.label8.Text = "最大经度:";
            // 
            // mapControl
            // 
            this.mapControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.mapControl.Enabled = true;
            this.mapControl.Location = new System.Drawing.Point(0, 0);
            this.mapControl.Name = "mapControl";
            this.mapControl.OcxState = ((System.Windows.Forms.AxHost.State)(resources.GetObject("mapControl.OcxState")));
            this.mapControl.Size = new System.Drawing.Size(617, 698);
            this.mapControl.TabIndex = 0;
            this.mapControl.MouseMoveEvent += new AxMapWinGIS._DMapEvents_MouseMoveEventHandler(this.mapControl_MouseMoveEvent);
            this.mapControl.ExtentsChanged += new System.EventHandler(this.mapControl_ExtentsChanged);
            this.mapControl.OnDrawBackBuffer += new AxMapWinGIS._DMapEvents_OnDrawBackBufferEventHandler(this.mapControl_OnDrawBackBuffer);
            // 
            // statusStrip
            // 
            this.statusStrip.Location = new System.Drawing.Point(0, 724);
            this.statusStrip.Name = "statusStrip";
            this.statusStrip.Size = new System.Drawing.Size(1008, 22);
            this.statusStrip.TabIndex = 1;
            this.statusStrip.Text = "statusStrip1";
            // 
            // MainForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1008, 746);
            this.Controls.Add(this.splitContainer);
            this.Controls.Add(this.statusStrip);
            this.Controls.Add(this.menuStrip);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MainMenuStrip = this.menuStrip;
            this.Name = "MainForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "名通GIS地图空间编辑器";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.toolStripMap.ResumeLayout(false);
            this.toolStripMap.PerformLayout();
            this.menuStrip.ResumeLayout(false);
            this.menuStrip.PerformLayout();
            this.splitContainer.Panel1.ResumeLayout(false);
            this.splitContainer.Panel2.ResumeLayout(false);
            this.splitContainer.Panel2.PerformLayout();
            this.splitContainer.ResumeLayout(false);
            this.splitContainerSetting.Panel1.ResumeLayout(false);
            this.splitContainerSetting.Panel1.PerformLayout();
            this.splitContainerSetting.Panel2.ResumeLayout(false);
            this.splitContainerSetting.ResumeLayout(false);
            this.toolStripSetting.ResumeLayout(false);
            this.toolStripSetting.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.mapControl)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.ToolStrip toolStripMap;
        private System.Windows.Forms.ToolStripButton btnFullExtent;
        private System.Windows.Forms.MenuStrip menuStrip;
        private System.Windows.Forms.ToolStripMenuItem miFile;
        private System.Windows.Forms.SplitContainer splitContainer;
        private System.Windows.Forms.SplitContainer splitContainerSetting;
        private System.Windows.Forms.TreeView treeViewMapLayer;
        private AxMapWinGIS.AxMap mapControl;
        private System.Windows.Forms.StatusStrip statusStrip;
        private System.Windows.Forms.ToolStrip toolStripSetting;
        private System.Windows.Forms.ToolStripButton tsBtnAddLayer;
        private MasterCom.MapSpaceManager.MapSpaceManager.LayerInfoPanel layerInfoPanel;
        private System.Windows.Forms.ToolStripButton tsBtnRemoveLayer;
        private System.Windows.Forms.ToolStripMenuItem miSaveMWS;
        private System.Windows.Forms.ToolStripButton miBtnUp;
        private System.Windows.Forms.ToolStripButton miBtnDown;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.TextBox txtCurMinLat;
        private System.Windows.Forms.TextBox txtCurMinLong;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.TextBox txtCurMaxLat;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.TextBox txtCurMaxLong;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.ToolStripMenuItem miOpenMWS;
        private System.Windows.Forms.ToolStripButton tsBtnNewSpace;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miSaveAsMWS;
        private System.Windows.Forms.ToolStripButton btnSaveIt;
    }
}

