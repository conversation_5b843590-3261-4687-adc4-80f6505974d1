﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRWeakCoverPnl : UserControl
    {
        public NRWeakCoverPnl()
        {
            InitializeComponent();
        }

        NRWeakCoverCause mainReason = null;
        public void LinkCondition(NRWeakCoverCause reason)
        {
            this.mainReason = reason;
            numRSRPMax.Value = (decimal)mainReason.RSRPMax;
            numRSRPMax.ValueChanged += numRSRPMax_ValueChanged;
        }

        void numRSRPMax_ValueChanged(object sender, EventArgs e)
        {
            mainReason.RSRPMax = (float)numRSRPMax.Value;
        }
    }
}
