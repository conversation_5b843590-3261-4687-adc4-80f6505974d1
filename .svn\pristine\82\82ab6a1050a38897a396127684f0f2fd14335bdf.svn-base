﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.Func.ExportTestPoint
{
    public class ExportTestPointBase : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        private static ExportTestPointBase instance = null;
        public static ExportTestPointBase GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ExportTestPointBase();
                    }
                }
            }
            return instance;
        }

        protected ExportTestPointBase()
            : base(MainModel.GetInstance())
        {
            this.IncludeMessage = false;
            this.IncludeEvent = false;
        }

        public override string Name
        {
            get
            {
                return "导出采样点信息(按区域)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11035, this.Name);
        }

        protected override void fireShowForm()
        {
            if (streamWriter != null)
            {
                streamWriter.Close();
            }
            if (fileStream != null)
            {
                fileStream.Close();
            }

            ExportResultSecurityHelper.ExportSecurity(this.getRecLogItem(), ref fileName, exportCond, true);
        }

        ExportTemplate template = null;
        protected System.IO.FileStream fileStream = null;
        protected System.IO.StreamWriter streamWriter = null;
        ExportSecurityCondition exportCond;
        string fileName;
        protected override bool getCondition()
        {
            TemplateSelector dlg = new TemplateSelector(template);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            template = dlg.Template;

            if (!ExportResultSecurityHelper.GetExportPermit(this.getRecLogItem(), FileSimpleTypeHelper.Csv
                , ref fileName, out exportCond))
            {
                return false;
            }

            if (System.IO.File.Exists(fileName))
            {
                System.IO.File.Delete(fileName);
            }
            fileStream = new System.IO.FileStream(fileName, System.IO.FileMode.CreateNew, System.IO.FileAccess.Write, System.IO.FileShare.Read);
            streamWriter = new System.IO.StreamWriter(fileStream, Encoding.UTF8);

            StringBuilder title = new StringBuilder();
            this.Columns = new List<string>();
            foreach (ColumnOptions col in template.Columns)
            {
                title.Append(col.Caption + ",");
                Columns.Add(col.DisplayParam.ParamInfo.Name);
            }
            Columns.Add("wtimems");
            streamWriter.WriteLine(title.ToString());
            return true;
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                foreach (TestPoint tp in file.TestPoints)
                {
                    if (!isValidTestPoint(tp))
                    {
                        continue;
                    }
                    StringBuilder sb = new StringBuilder();
                    bool filter = dealTemplateColumns(tp, sb);
                    if (filter)
                    {
                        continue;
                    }
                    streamWriter.WriteLine(sb.ToString());
                }
            }
        }

        private bool dealTemplateColumns(TestPoint tp, StringBuilder sb)
        {
            bool filter = false;
            foreach (ColumnOptions col in template.Columns)
            {
                object value = getValue(tp, col);

                if (!col.CheckValue)
                {
                    if (value == null)
                    {
                        sb.Append(",");
                    }
                    else
                    {
                        sb.Append(value.ToString() + ",");
                    }
                    continue;
                }

                if (value == null)
                {
                    if (col.IsIgnoreWhenNotInRange)
                    {
                        filter = true;
                        break;
                    }
                    else
                    {
                        sb.Append(",");
                    }
                    continue;
                }

                filter = addValidDoubleValue(sb, filter, col, value);
                if (filter)
                {
                    break;
                }
            }

            return filter;
        }

        private object getValue(TestPoint tp, ColumnOptions col)
        {
            object value = tp[col.DisplayParam.ParamInfo.Name, col.ParamArrayIndex];
            if (col.DisplayParam.Name == "Time")
            {
                value = tp.DateTimeStringWithMillisecond;
            }
            else if (col.DisplayParam.Name == "FileName")
            {
                value = MasterCom.Util.CsvFieldFormatter.Format(value.ToString());
            }

            return value;
        }

        private bool addValidDoubleValue(StringBuilder sb, bool filter, ColumnOptions col, object value)
        {
            double dVal;
            if (double.TryParse(value.ToString(), out dVal))
            {
                if (dVal >= col.MinValue && dVal <= col.MaxValue)
                {
                    sb.Append(dVal + ",");
                }
                else if (col.IsIgnoreWhenNotInRange)
                {
                    filter = true;
                }
                else
                {
                    sb.Append(",");
                }
            }

            return filter;
        }
    }
}
