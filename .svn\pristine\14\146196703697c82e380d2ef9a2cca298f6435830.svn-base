﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class VolteWeakMosReasonHelper : WeakMosReasonHelperBase
    {
        public VolteWeakMosReasonHelper(MainModel mainModel, List<ServiceType> serviceTypes)
            : base(mainModel, serviceTypes)
        {

        }

        public List<int> CallBeginEvtIdList { get; } = new List<int>() 
        {
            1004,1005,1024,1025,1044,1045,1054,1055,1074,1075,1082,1083,1374,1375,1382,1383
        };

        public List<int> CallEndEvtIdList { get; } = new List<int>
        {
            1084,1085,1086,1087,1384,1385,1386,1387
        };

        public int P2CHandoverSuccess { get; } = (int)EnumESRVCCEvent.P2CHandoverSuccess;

        public List<int> HandOverEvtIdList { get; } = new List<int>
        {
            851,899
        };

        public List<int> ReestablishmentRequestMsg { get; } = new List<int>
        { 1093626112 };

        public int? GetRtpPacketsLostNum(TestPoint tp)
        {
            return (int?)tp["lte_volte_RTP_Packets_Lost_Num"];
        }
    }
}
