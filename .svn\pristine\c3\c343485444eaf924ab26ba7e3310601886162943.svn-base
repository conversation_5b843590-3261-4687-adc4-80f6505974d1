﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MapWinGIS;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class RoadCompareDlg : BaseDialog
    {
        CityRoadCompareSetting comPareCond;
        public RoadCompareDlg(CityRoadCompareSetting cond)
        {
            InitializeComponent();
            mainModel = MainModel.GetInstance();
            this.comPareCond = cond;
            init();
        }

        private void init()
        {
            if (comPareCond == null)
            {
                comPareCond = new CityRoadCompareSetting();
            }
            chkMergeSameRoad.Visible = comPareCond.IsShowMergeSameRoad;
            chkMergeSameRoad.Checked = comPareCond.IsMergeSameRoad;
            listBoxPeriod.Items.Clear();
            foreach (TimePeriod item in comPareCond.PeriodList)
            {
                listBoxPeriod.Items.Add(item);
            }
            fillCbx(comPareCond.CityRoadMapCond);
        }

        private void fillCbx(CityRoadMapCfg selMapCfg)
        {
            cbxReports.Items.Clear();
            foreach (CityRoadMapCfg mapCfg in CityRoadMapManager.GetInstance().CityRoadMapList)
            {
                cbxReports.Items.Add(mapCfg);
            }
            if (cbxReports.Items.Count > 0)
            {
                if (selMapCfg != null)
                {
                    cbxReports.SelectedItem = selMapCfg;
                }
                else
                {
                    cbxReports.SelectedIndex = 0;
                }
            }
        }

        private void btnMapSetting_Click(object sender, EventArgs e)
        {
            CityRoadMapSetDlg frm = new CityRoadMapSetDlg(comPareCond.CityRoadMapCond);
            frm.ShowDialog();
            fillCbx(frm.SelectMapCfg);
        }

        #region 时间条件设置

        private void buttonPeriodAdd_Click(object sender, EventArgs e)
        {
            DateTime beginDateTime = new DateTime(DateTime.Now.AddMonths(-1).Year, DateTime.Now.AddMonths(-1).Month, 1);
            DateTime endDateTime = beginDateTime.AddMonths(1).AddMilliseconds(-1);
            TimePeriod period = new TimePeriod(beginDateTime, endDateTime);

            TimePeriodChooser timePeriodChooser = new TimePeriodChooser(period);
            if (timePeriodChooser.ShowDialog() == DialogResult.OK)
            {
                period = timePeriodChooser.Value;
                int index = 0;
                for (; index < listBoxPeriod.Items.Count; index++)
                {
                    TimePeriod periodTemp = (TimePeriod)listBoxPeriod.Items[index];
                    if (period.EndTime < periodTemp.BeginTime)
                    {
                        listBoxPeriod.Items.Insert(index, period);
                        break;
                    }
                    if (period.Combine(periodTemp))
                    {
                        listBoxPeriod.Items.RemoveAt(index);
                        index--;
                    }
                }
                if (index >= listBoxPeriod.Items.Count)
                {
                    listBoxPeriod.Items.Add(period);
                }
                if (listBoxPeriod.SelectedIndex < 0)
                {
                    listBoxPeriod.SelectedIndex = 0;
                }
                buttonPeriodRemove.Enabled = true;
                buttonPeriodClear.Enabled = true;
            }
        }

        private void buttonPeriodRemove_Click(object sender, EventArgs e)
        {
            int index = listBoxPeriod.SelectedIndex;
            if (index >= 0)
            {
                listBoxPeriod.Items.RemoveAt(index);
                if (index >= listBoxPeriod.Items.Count)
                {
                    index = listBoxPeriod.Items.Count - 1;
                }
                listBoxPeriod.SelectedIndex = index;
                if (listBoxPeriod.Items.Count <= 0)
                {
                    buttonPeriodRemove.Enabled = false;
                    buttonPeriodClear.Enabled = false;
                }
            }
        }

        private void buttonPeriodClear_Click(object sender, EventArgs e)
        {
            listBoxPeriod.Items.Clear();
            buttonPeriodRemove.Enabled = false;
            buttonPeriodClear.Enabled = false;
        }

        #endregion

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (listBoxPeriod.Items.Count <= 0)
            {
                MessageBox.Show("请设置对比时段", "设置错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            comPareCond.CityRoadMapCond = cbxReports.SelectedItem as CityRoadMapCfg;
            if (comPareCond.CityRoadMapCond == null)
            {
                MessageBox.Show("请先设置图层集", "设置错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (string.IsNullOrEmpty(comPareCond.CityRoadMapCond.Name))
            {
                MessageBox.Show("请设置图层集名称", "设置错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (string.IsNullOrEmpty(comPareCond.CityRoadMapCond.GridMapPath)
                || string.IsNullOrEmpty(comPareCond.CityRoadMapCond.GridNameColumn))
            {
                MessageBox.Show("请正确设置地市网格图层", "设置错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (comPareCond.CityRoadMapCond.StreetInjectTablesList.Count <= 0)
            {
                MessageBox.Show("请设置道路图层", "设置错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (!applayMapSetting(comPareCond))
            {
                return;
            }

            comPareCond.IsMergeSameRoad = chkMergeSameRoad.Checked;
            comPareCond.PeriodList.Clear();
            foreach (object item in listBoxPeriod.Items)
            {
                comPareCond.PeriodList.Add((TimePeriod)item);
            }
            DialogResult = DialogResult.OK;
        }

        protected static bool applayMapSetting(CityRoadCompareSetting cond)
        {
            try
            {
                return setGridMapInfo(cond.CityRoadMapCond)
                    && setStreetMapInfo(cond.CityRoadMapCond.StreetInjectTablesList);
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
                return false;
            }
        }
        private static bool setGridMapInfo(CityRoadMapCfg cityRoadMapCond)
        {
            Shapefile table = new Shapefile();
            if (!table.Open(cityRoadMapCond.GridMapPath, null))
            {
                return false;
            }
            if (table.ShapefileType != ShpfileType.SHP_POLYGON
             && table.ShapefileType != ShpfileType.SHP_POLYGONZ)
            {
                MessageBox.Show(string.Format("文件:{0}{1}不是有多边形图层！"
                    , table.Filename, Environment.NewLine));
                table.Close();
                return false;
            }

            bool isOk = false;
            try
            {
                int nmFldIndex = MapOperation.GetColumnFieldIndex(table, cityRoadMapCond.GridNameColumn);

                string regionKeyName = "多时段道路信息对比_网格图层";
                List<ResvRegion> subRegionList = new List<ResvRegion>();
                for (int i = 0; i < table.NumShapes; i++)
                {
                    MapWinGIS.Shape geome = table.get_Shape(i);
                    if (geome == null)
                    {
                        continue;
                    }
                    string namestr = table.get_CellValue(nmFldIndex, i).ToString();

                    ResvRegion region = new ResvRegion();
                    region.RootNodeName = regionKeyName;
                    region.RegionName = namestr;
                    region.Shape = geome.Clone();
                    subRegionList.Add(region);
                }

                MainModel model = MainModel.GetInstance();
                model.MainForm.GetMapForm().clearRegionLayer();
                model.SearchGeometrys.SelectedResvRegions = new List<ResvRegion>();
                model.SearchGeometrys.SelectedResvRegions.AddRange(subRegionList);
                model.SearchGeometrys.SelectedResvRegionDic = new Dictionary<string, List<ResvRegion>>();
                model.SearchGeometrys.SelectedResvRegionDic.Add(regionKeyName, subRegionList);

                model.MainForm.GetMapForm().RefreshResvLayer();
                model.MainForm.GetMapForm().searchGeometrysChanged();
                model.MainForm.GetMapForm().GoToView(model.SearchGeometrys.RegionBounds); 
                isOk = true;
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
            finally
            {
                table.Close();
            }
            return isOk;
        }
        private static bool setStreetMapInfo(List<StreetInjectTableInfo> streetInjectTablesList)
        {
            MainModel model = MainModel.GetInstance();
            if (model.StreetInjectTablesDic.ContainsKey(model.DistrictID))
            {
                model.StreetInjectTablesDic[model.DistrictID].Clear();
            }
            else
            {
                List<StreetInjectTableInfo> streetList = new List<StreetInjectTableInfo>();
                model.StreetInjectTablesDic[model.DistrictID] = streetList;
            }

            model.StreetInjectTablesList.Clear();
            foreach (StreetInjectTableInfo item in streetInjectTablesList)
            {
                model.StreetInjectTablesDic[model.DistrictID].Add(item);
                model.StreetInjectTablesList.Add(item);
            }
            return true;
        }
    }

    public class CityRoadCompareSetting
    {
        public CityRoadCompareSetting()
        { 
        }
        public CityRoadCompareSetting(bool isShowMergeSameRoad)
        {
            this.IsShowMergeSameRoad = isShowMergeSameRoad;
        }
        public List<TimePeriod> PeriodList { get; set; } = new List<TimePeriod>();

        public CityRoadMapCfg CityRoadMapCond { get; set; }

        public bool IsShowMergeSameRoad { get; set; }
        public bool IsMergeSameRoad { get; set; }
    }
}
