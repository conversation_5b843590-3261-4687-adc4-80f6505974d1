<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="toolStripMap.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 1</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnFullExtent.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACASURBVDhPY6A16IJikgEzEM8H4v9QDGKDxIgCnEC8EYhh
        mmF4CxCD5GgHDKA0MQBDLUjgLYSJAmDORwcgtXBDYJrRFcI0YzMExIcbUgEVwKYIGSMDmBhILxg0AjG6
        IhDAphkEQGIgPSgAQwAPIEUt6YAqCYmipIwMyM5MRAIGBgD/hS9H+VU9dAAAAABJRU5ErkJggg==
</value>
  </data>
  <metadata name="menuStrip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>113, 1</value>
  </metadata>
  <metadata name="toolStripSetting.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>338, 2</value>
  </metadata>
  <data name="tsBtnNewSpace.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAJFSURBVDhPpY5dSFNhAIYHY6CgdTG7cDcOCstESsgiSQRh
        dDFXmNsqEg2UIazQi1JEGrShjslo6UzPGLM510bgmmtrlprNqWU1rYtkc/40vWjd9eONbu3tnLM1PUQR
        9MDLd3G+5zkf6xc3mpqOt7e0ECqFgtB0dBB6rZYw6vWExWgk7BYL4bTbCc/ICDHudstSyi71tbUnO1Uq
        /AuroRDGXC5dSk1yqbp6kPrY/SiIbmcQ2tEg7rhC0D1extLmV0T9frxXq+kzkUjA43Bsp9QkIqHQSgW0
        o0mp1xNGn3cF/U9XsR37QctvWlvpk8JhsyGlJhFUVNCBu+5l9D0JgyBF48QaTJPrCH/6zngBhc1sZgbO
        lJbSAeqvhmdJ0Tz1ERZfBGOLUaxGt7BDvmTt8xYduG8wMAMniovpgGGclJ+vY+hFBMPTG3jgJzezAdvM
        ZnoUAz09zEBhQQEdqDXdw8DEB1j3iPZZ5ih0Gg0zcJDPt36LfcHRLikO3Zbg+mIDtPMPMTgdJEOR3wJd
        SiUzwOPx6BdQMrXTxgsoH65CoUaM884a3HqrhunlQjqgaGtjBg7k5DACe3ekU4yr74TofzWVDtxsbmYG
        9mdl/TFATeQSY2huKR241tjIDGRmZlp34jHyogQ1rytxZb4SEp+I3kVfFRpmZWgPKGGdW6ED9XV1zACH
        w5HJ5XLE43H6wt9YCARwWSr1ptRd2Gx2b0ZGRnxfdja4XC54ubng5+XhcH4+jhUV4VRJCcrLynBWIPCe
        I0lp/wuL9RNm6yN8FL7AtwAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="btnSaveIt.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAABQSURBVDhP3YxJCgAgDAP7dH+uhJIggkvVgzgQLGaxW+RN
        iZySf85gBi+KJDRQSYQGmEWR/DJQGV01OSFjVeh41WmNobYGCO43B4DCAZ1iVgBP6Iyo8QiMcwAAAABJ
        RU5ErkJggg==
</value>
  </data>
  <data name="tsBtnAddLayer.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAIFSURBVEhLYxgZoGdeeP7x6zPPnQDjuWA8eXF6PlSacjB3
        fWbX2j3N/5Dxkq0VXVBpysHM9aldr/7s/Q/CK3fX/wPRizaXUM+C6WsTup792vIfhJfurP8HoudtLKCe
        BZNXRHXN31b/DxnPBgYbVJpy0LcsuOvWl/n/QXjm5vp/IHrq6gTqWdCxyLfr0vvJ/0F44rr6fyB6AtBX
        UGniQE67mVP9HHe/+jkuSNjRr2aao9+kjcHrOlfW/0PGU7dErquZBlLj7tcExt5A7AfGxd2OTlBjEWD2
        jqibBx5X/Qfh+kVV/7Cxkfm41IDYc/ck3IQaiwATNgbfrJ+d+48aeOq2YEwL8uaK3WzYJ/QfhOvrhf5h
        YyPzcakBsXPnCmBakDiN62b+Jsb/IJxXz/APGxuZj0sNiB0/jRnTAr9y/kWRnTz7Izo594eCcAcHELPu
        D2ll3R85heVh+krG/2AMNABER05heBjYyrA/sJURBYPUe5SwLYIaSxxwKWLtige5DAk7FTFQLx845bF3
        Rcxi/A/C4UDDQbRdHiP1LLDL5uwKnMz4H4yBFoBoy0wqWmCdzt3lBTQYGZunMlPPAstkvi7Xdsb/IOwC
        NBxEGydQ0QLzBIEu+ybG/2AMtABEG8axUs8Cs2jRZucSrs+OQOwAxKYlnJ/1wjmbodJUAUxAzIyGQWID
        DRgYAMjKwxgp0okJAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="tsBtnRemoveLayer.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADRSURBVEhLYxgFo2AUDBBYnpRUcqqtbdqpxsZpp+rrERjE
        B4l3dk471d2Ngtfl59dAtRMGR2trj/9cs+Y/CO+or/8HYyPzkcVB7DOtrVeg2gmDHbGxx1dVVv4jBR/J
        yLgA1U4YzBUROX6IgeE/CDcyMPyDsZH5yOIg9kpp6XNQ7YRBP9CCNUCNIFwP1AxjI/ORxUHsmeLixFuQ
        LSNTVy8ltQiGayQlF9WIiCyq4uNbVM7GtqiEkXFRCQMDmAbxq/j5FxVKSzdBtY+CUTAK6AsYGAAUmK26
        Dre4LAAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="miBtnUp.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAJvSURBVEhLYyAF2Nvbs4AwlEtdEBoaynbwxNT9B45O3R8a
        qsUGFaYeWLS6oufgidn/QHjByooeqDB1QNeEuMD9R/f/3X9s6r+PP47/B7FbeuMCodKUgZJqD+Wbz9a8
        e/f94P89R/v/gWgQBonllngoQ5WRB+Lj7Tn2nOo+veNQxz9s+MCpCadBaqDKSQfzVmdOe/Zp0/8tBxr+
        IdMwvOVA47/5KwqmQZWTBuo6fSPX7q7/9/D9qv/r9qLSMAzir91T/6+6LSASqo04kFNpr3HmbsfHlTvr
        /xGDz92d8jGtyF4Dqh0/iInR4153uPDioq31/648n/YfhGFsZDF0/qbDVRddgXqhxuAGvfPCZp591PN/
        9ob6f2cfdf8HYRgbWQyVD1HfOy96JtQY3CAmx0A/tdDCPrXQxD6lzNR97dGSL5NW1f87cqfhP4yG4bUX
        S7+kFFm6p5YC1Zfa2MfkmOlDjSEaMK08HP+6a0nJP2x4xaH41yA1EKXkAab5O6JfN8zL+LfpQsZ/GA3D
        83cEUW7BpB2Br+unR//Dhidt86XcguZVOq/rOy3+Tdlr97++x+nf9P3O/6ftc/w/ZY/tf5AcSA1EKXmA
        qWS+9OuKeuF/lauF/8NoGM6ZI0S5BTlzhF8X1fP/w4YzJ/FTbkH2JOHXOfU8/3IW8fyH01AcP4mNcgvS
        gRak1XP8S5/L8R9Gw3D8RCpYEA+0ILGe9R82HNNPJQti65n/xU5j/g+noTiolXILGBN75G+l14v/SasX
        /ZNaL/wnuV7wT1I9/5+Eet4/oS1ct0BqIErJBPL28hz69vwCMCwPw/ogmoFAbcbAAAAMWwDNqWvWSQAA
        AABJRU5ErkJggg==
</value>
  </data>
  <data name="miBtnDown.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAJ1SURBVEhLYyAE5O0ZOOT1+QXk7SFYHwnL28tzQJWRDRhD
        W7huJdTz/kmq5/+TXC/4J7Ve+E9aveif9HrxP4k98rdAaiBKyQNMQa1sr2OnMf+H43rmfzA6fpLwa5Aa
        iFLyAFNMP9vrxHrWf9gwVSyIn8j2On0ux38YTqvn+AenqWLBJLbXOYt4/sNxPc8/GJ1NDQsyJ/G/Lqrn
        /4cN58yhggU5c4ReV64W/g/DFfXC/2B0yXxpyi1oXqXzesoe2//T9jn+n77f+X99j9O/KXvt/td3WvwD
        yYHUQJSSB5gmbfN9XT89+h82PGlHIOUWzN8R9HrThYz/MNwwL+MfjJ6/I5pyC1Ycin/dtaTkHza88nA8
        6RbE5Jjpp5ba2KeWWtinFFm6r71Y+uXInYb/MDxpVf0/GL32aMmXlDJT99RCE/vUQgv7mBwDfagxuEHv
        vOiZszfU/zv7qOf/2Ufd/yHsbjAbmY8sDlPfOy9sJtQY3MA1Ro970+Gqi1eeT/sPwou21v+DsZH5yOIg
        9rrDhRdjgHqhxuAHaUX2GufuTvm4cmf9P2LwmbsdH3Mq7TWg2okD1W0BkWv3AF22t/7fw/er/sMwjA+j
        1+6u/1fX6RsJ1UYamL+iYNqWA43/nn3a9B+GtxxoAPNh9LzVmdOgykkH8fH2HAdOTTi941DHP2x4z6nu
        0yA1UOXkgdwSD+Wbz9a8e/f94H8Q3nO0/x+IBomVVHsoQ5VRBlp64wL3H93/9+OP4//3H5v6D8TumhAX
        CJWmDliwsqLn4InZ/0B40eqKHqgw9UBoqBbbgaNT9x88MXV/aGgoG1SYusDe3p4FhKFcIgADAwDZAQDN
        mdqsngAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="mapControl.OcxState" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        ********************************************************************************
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACFTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5BeEhvc3QrU3RhdGUBAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAZQAAAAIB
        AAAAAQAAAAAAAAAAAAAAAFAAAAAIAAQAxT8AACRIAAAAAAAA////AHsUrkfhepQ/AgAzMzMzMzPTPwEB
        AAAAFAAAAAEAAQAAAAAAAADgPwAAAQAAAAAAAAAAAAADAAAAAAAAAAs=
</value>
  </data>
  <metadata name="statusStrip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>224, 2</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>49</value>
  </metadata>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAGBgAAAEAIACICQAAFgAAACgAAAAYAAAAMAAAAAEAIAAAAAAAAAkAABILAAASCwAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADAAAHFQYAKj8g
        Am9hMgOsHwsAWwAAAAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAMAAAQJAgAaNx8IWmtHH56Qejzdf5Y1/7ByIv+tcCP+m5pl/UhAG6wEAAAoAAAAAQAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAgMAAA8oFARDYD4WiZlnKsrMj0D61JxP/9as
        aP48kCv/IpQd/7KIOv+nxKb9SNDA/4OgWv6MTQvoJBAAaAAAAA4AAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AABULgpTiGIhuMKLM/DbpEP/36dL/tylUP6ks1z+Nq5B/TWhKf5ckTX9x55W/cijYf1espL+KJFc/Q1+
        Hf5Yih/+nmoW/lwuALYFAQAwAAAAAgAAAAAAAAAAAAAAAAAAAABtOw0KlmUiec2aPu3isVL/5rRa/+uz
        Yv2rqEr/rLJZ/+W0c/3JpVr/zKde/2Oydf8chDv9C14M/wlfAv0QeAf/io0w/59eAP6RUQPuLhYAdAAA
        ABEAAAAAAAAAAAAAAAAAAAAAAAAAAGpAFg2lczKA2q9b8U6pKv8ijAv/CYMA/5KuYv1Cv4b/MZlQ/1GG
        OP9udyD9I1YL/wdUAv0KYgH/bIEf/6RlAf2mZgT/qmgL/24+CMAJAwA5AAAABQAAAAAAAAAAAAAAAAAA
        AAAAAAAAdE8nD4GAPIVhpjv0RIYu/mt+Pf6onFv9uqlV/befQv2kizL+tZQv/WJuGf4HUwH9moIp/qpu
        Av6tbwf+sHIO/7Z1E/+mYRLwIw8BUAAAAAAAAAAAAAAAAKh2ZAnKmYBI27KUjN++ns/KtZ3958+x/+bL
        pP3gv43/4LV5/7CMVP+2kEr9lno0/5Z4KP2FbB//c24W/YlVBs9wQwOgXTUEbUUiAzomEQEMHA0BAQAA
        AAC7hXJQ2ryhw9/Iqvr+5b///+e9/828oP704cP/zsGu/8i5o/3mzKT/zrGD/5N8WP+vj2D9ong9/7B+
        Ov3CgjL/pWgg/qFeH6h5QxgTAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAuJJ/Vte7oNm6q5b/yrme/+/b
        uv3Mvab/saeW/66ll/2flor/s6eY/7+zpP/BtKH9p5qH/6OEV/25hjz/2pk5/+CVK/70nTLt0oA3XgAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAKmViwm1nI+cuq6e/qOdlP6wqqL9z8m+/dDJv/7FvrT9qaKt/XNv
        tf2IgaD+vK6a/bCjkv6Tg239s4hG/e2lNf7/tD3+/7RD/+KZR7vJgEgfAAAAAAAAAACEhIQtqKinyb++
        vvzJycj/2djW/9rW0/3Vz8n/0MrA/9HKwP3Iwbb/mJO2/1JPxf9tZ6z9sqeY/7Cjkf2xoYz/t6OH/6iL
        YP3EkT7/+7ZG//y5U//jolbbtm9JLwAAAAAAAAAAmpmZE7Ozsn/Kycjr3drZ/+Df3P59fdP9UVDM/bq1
        yP7TzMH9zMK0/ci8q/3CtqP+tKeb/YyCm/6ml479uKSL/bKfiv6pmor+nIVu24ZtWUAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAACWlpUGs7KxZ83MytvW1eP/xMLj/+fk5P3h3Nj/2tLJ/9TLv//OxLj9qaG9/05O
        1P1xbrT/saym/6qkn/2Oi6L/b2uo/6umofajoJ6CioiHCwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AACzsrFPz87Lxuro5v/m5OL/4t/d/9zZ2P/Sz9H91dPP/9LPyf3Oy8b/ysXB/8fCvf6qp83/m5jM/765
        tPq3sq/Pn5yYhXRzcgUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALGwrznIx8Ww393a/paV
        1/5iYtT+08/P/9PPy//Ewb78uLSx0LCsqZymoZ9nmJWSM4iGhAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAp6alI8G/vJbDwb2dtLKwaaGfnTWOjIoFAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///8A////AP+B/wD4AH8AgAA/AAAADwAAAAcAwAABAPAA
        AQDAAAEAAAAfAIAADwDAAAMAAAABAIAABwDgAAEA/AAAAP8ABwD/wP8A////AP///wD///8A////AP//
        /wA=
</value>
  </data>
</root>