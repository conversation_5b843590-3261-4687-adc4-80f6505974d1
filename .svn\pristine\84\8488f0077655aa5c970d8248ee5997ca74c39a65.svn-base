﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using System.Windows.Forms;
using MasterCom.Util;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public class ArchiveSettingManager
    {
        private static string path = Application.StartupPath + "\\config\\AreaArchive\\archiveConfig.xml";

        private ArchiveCondition condition = null;
        public ArchiveCondition Condition
        {
            get
            {
                if (condition == null || condition.BaseCondition.DistrictID != model.DistrictID)
                {
                    init();
                }
                return condition;
            }
        }

        private readonly MainModel model;

        private static ArchiveSettingManager instance;

        private static object olock = new object();

        private ArchiveSettingManager(MainModel model)
        {
            this.model = model;
        }

        public static ArchiveSettingManager GetInstance()
        {
            if (instance == null)
            {
                lock (olock)
                {
                    if (instance == null)
                    {
                        instance = new ArchiveSettingManager(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }

        private void init()
        {
            if (condition == null || condition.BaseCondition.DistrictID != model.DistrictID)
            {
                condition = new ArchiveCondition();
                condition.LoadConfig(path);
            }
        }

        public void SetCondition()
        {
            ArchiveBaseSettingForm form = model.GetObjectFromBlackboard(typeof(ArchiveBaseSettingForm)) as ArchiveBaseSettingForm;

            if (form == null || form.IsDisposed)
            {
                form = new ArchiveBaseSettingForm();
            }
            init();
            form.SetCondition(condition);
            if (DialogResult.OK == form.ShowDialog(model.MainForm))
            {
                condition = form.GetCondition();
                condition.SetDistrictID();
                condition.SaveConfig(path);
            }
        }
    }

    public class ArchiveCondition
    {
        public QueryCondition BaseCondition { get; set; }

        public AreaCondition VillageCondition { get; set; }

        public ArchiveCondition()
        {
            VillageCondition = new AreaCondition();
            initBaseCond();
        }

        private void initBaseCond()
        {
            BaseCondition = new QueryCondition();
            SetDistrictID();
        }

        public void SetDistrictID()
        {
            QueryCondition cond = MainModel.GetInstance().QueryCondition;
            BaseCondition.DistrictID = cond.DistrictID;
            BaseCondition.DistrictIDs = cond.DistrictIDs;
        }

        public QueryCondition GetBaseConditionBackUp()
        {
            QueryCondition cond = new QueryCondition();
            cond.DistrictID = BaseCondition.DistrictID;
            cond.CarrierTypes.AddRange(BaseCondition.CarrierTypes);
            cond.DistrictIDs = BaseCondition.DistrictIDs;
            cond.FileName = BaseCondition.FileName;
            cond.FileNameOrNum = BaseCondition.FileNameOrNum;
            cond.FilterOffValue9 = BaseCondition.FilterOffValue9;
            cond.Geometorys = BaseCondition.Geometorys;
            cond.IsAllAgent = BaseCondition.IsAllAgent;
            cond.Momt = BaseCondition.Momt;
            cond.NameFilterType = BaseCondition.NameFilterType;
            cond.Periods = BaseCondition.Periods;
            cond.Projects = BaseCondition.Projects;
            cond.QueryType = BaseCondition.QueryType;
            cond.ServiceTypes = BaseCondition.ServiceTypes;
            cond.AgentIds = BaseCondition.AgentIds;

            return cond;
        }

        public MasterCom.MTGis.DbRect GetBound()
        {
            return VillageCondition.GetBound();
        }

        public bool LoadConfig(string path)
        {
            try
            {
                XmlConfigFile file = new XmlConfigFile(path);
                Dictionary<string, object> baseDic = file.GetItemValue("Configs", "BaseConfig") as Dictionary<string, object>;
                Dictionary<string, object> areaDic = file.GetItemValue("Configs", "AreaConfig") as Dictionary<string, object>;

                parseBaseCondition(baseDic);
                VillageCondition.ParseAreaDic(areaDic);

                return true;
            }
            catch
            {
                return false;
            }
        }

        public bool SaveConfig(string path)
        {
            try
            {
                XmlConfigFile file = new XmlConfigFile();
                XmlElement cfg = file.AddConfig("Configs");
                file.AddItem(cfg, "BaseConfig", getBaseCondDic());
                file.AddItem(cfg, "AreaConfig", VillageCondition.GetAreaCondDic());
                file.Save(path);

                return true;
            }
            catch
            {
                return false;
            }
        }

        private Dictionary<string, object> getBaseCondDic()
        {
            Dictionary<string, object> baseDic = new Dictionary<string, object>();

            if (BaseCondition.Periods.Count > 0)
            {
                baseDic[CommonWord.StartTime] = BaseCondition.Periods[0].BeginTime.ToString();
                baseDic[CommonWord.EndTime] = BaseCondition.Periods[0].EndTime.ToString();
            }
            baseDic[CommonWord.Projs] = BaseCondition.Projects;
            baseDic[CommonWord.Servs] = BaseCondition.ServiceTypes;
            baseDic[CommonWord.Carris] = BaseCondition.CarrierTypes;
            baseDic[CommonWord.FileFilterType] = BaseCondition.NameFilterType.ToString();
            baseDic[CommonWord.FileName] = BaseCondition.FileName;

            return baseDic;
        }

        private void parseBaseCondition(Dictionary<string, object> baseDic)
        {
            DateTime dtStart, dtEnd;
            if (baseDic.ContainsKey(CommonWord.StartTime) &&
                baseDic.ContainsKey(CommonWord.EndTime) &&
                DateTime.TryParse(baseDic[CommonWord.StartTime].ToString(), out dtStart) &&
                DateTime.TryParse(baseDic[CommonWord.EndTime].ToString(), out dtEnd))
            {
                BaseCondition.Periods.Clear();
                BaseCondition.Periods.Add(new TimePeriod(dtStart, dtEnd));
            }

            if (baseDic.ContainsKey(CommonWord.Projs))
            {
                BaseCondition.Projects.Clear();
                List<object> projs = baseDic[CommonWord.Projs] as List<object>;
                foreach (object o in projs)
                {
                    BaseCondition.Projects.Add((int)o);
                }
            }

            if (baseDic.ContainsKey(CommonWord.Servs))
            {
                BaseCondition.ServiceTypes.Clear();
                List<object> servs = baseDic[CommonWord.Servs] as List<object>;
                foreach (object o in servs)
                {
                    BaseCondition.ServiceTypes.Add((int)o);
                }
            }

            if (baseDic.ContainsKey(CommonWord.Carris))
            {
                BaseCondition.CarrierTypes.Clear();
                List<object> cars = baseDic[CommonWord.Carris] as List<object>;
                foreach (object o in cars)
                {
                    BaseCondition.CarrierTypes.Add((int)o);
                }
            }

            if (baseDic.ContainsKey(CommonWord.FileFilterType))
            {
                BaseCondition.NameFilterType = (FileFilterType)Enum.Parse(typeof(FileFilterType), baseDic[CommonWord.FileFilterType].ToString());
            }

            if (baseDic.ContainsKey(CommonWord.FileName))
            {
                BaseCondition.FileName = Convert.ToString(baseDic[CommonWord.FileName]);
            }
        }
    }

    public static class CommonWord
    {
        public static string StartTime { get; set; } = "StartTime";

        public static string EndTime { get; set; } = "EndTime";

        public static string Projs { get; set; } = "Projs";

        public static string Servs { get; set; } = "Servs";

        public static string Carris { get; set; } = "Carris";

        public static string FileFilterType { get; set; } = "FileFilterType";

        public static string FileName { get; set; } = "FileName";

        public static string City { get; set; } = "City";

        public static string Village { get; set; } = "Village";
    }
}