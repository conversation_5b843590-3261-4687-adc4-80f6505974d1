﻿namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.WeakCoverArea
{
    partial class WeakCvrSettingDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.label4 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.numRxLev = new System.Windows.Forms.NumericUpDown();
            this.numCellDis = new System.Windows.Forms.NumericUpDown();
            this.numAreaDis = new System.Windows.Forms.NumericUpDown();
            this.cmbRxLev = new System.Windows.Forms.ComboBox();
            this.cmbCell = new System.Windows.Forms.ComboBox();
            this.cmbArea = new System.Windows.Forms.ComboBox();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.label7 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.numWeakAreaNum = new System.Windows.Forms.NumericUpDown();
            this.cmbWeakAreaNum = new System.Windows.Forms.ComboBox();
            this.label10 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLev)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCellDis)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numAreaDis)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakAreaNum)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.SuspendLayout();
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(72, 37);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(83, 12);
            this.label4.TabIndex = 0;
            this.label4.Text = "村庄平均RxLev";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(78, 32);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(77, 12);
            this.label5.TabIndex = 0;
            this.label5.Text = "村庄最近基站";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(66, 23);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(89, 12);
            this.label6.TabIndex = 0;
            this.label6.Text = "两村庄中心距离";
            // 
            // numRxLev
            // 
            this.numRxLev.Location = new System.Drawing.Point(201, 34);
            this.numRxLev.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numRxLev.Minimum = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.numRxLev.Name = "numRxLev";
            this.numRxLev.Size = new System.Drawing.Size(75, 21);
            this.numRxLev.TabIndex = 1;
            this.numRxLev.Value = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            // 
            // numCellDis
            // 
            this.numCellDis.Location = new System.Drawing.Point(201, 30);
            this.numCellDis.Maximum = new decimal(new int[] {
            100000000,
            0,
            0,
            0});
            this.numCellDis.Name = "numCellDis";
            this.numCellDis.Size = new System.Drawing.Size(75, 21);
            this.numCellDis.TabIndex = 3;
            this.numCellDis.Value = new decimal(new int[] {
            1500,
            0,
            0,
            0});
            // 
            // numAreaDis
            // 
            this.numAreaDis.Location = new System.Drawing.Point(201, 20);
            this.numAreaDis.Maximum = new decimal(new int[] {
            10000000,
            0,
            0,
            0});
            this.numAreaDis.Name = "numAreaDis";
            this.numAreaDis.Size = new System.Drawing.Size(75, 21);
            this.numAreaDis.TabIndex = 5;
            this.numAreaDis.Value = new decimal(new int[] {
            1500,
            0,
            0,
            0});
            // 
            // cmbRxLev
            // 
            this.cmbRxLev.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbRxLev.FormattingEnabled = true;
            this.cmbRxLev.Items.AddRange(new object[] {
            "<",
            "≤",
            ">",
            "≥",
            "="});
            this.cmbRxLev.Location = new System.Drawing.Point(161, 34);
            this.cmbRxLev.Name = "cmbRxLev";
            this.cmbRxLev.Size = new System.Drawing.Size(34, 22);
            this.cmbRxLev.TabIndex = 0;
            // 
            // cmbCell
            // 
            this.cmbCell.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbCell.FormattingEnabled = true;
            this.cmbCell.Items.AddRange(new object[] {
            "<",
            "≤",
            ">",
            "≥",
            "="});
            this.cmbCell.Location = new System.Drawing.Point(161, 29);
            this.cmbCell.Name = "cmbCell";
            this.cmbCell.Size = new System.Drawing.Size(34, 22);
            this.cmbCell.TabIndex = 2;
            // 
            // cmbArea
            // 
            this.cmbArea.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbArea.FormattingEnabled = true;
            this.cmbArea.Items.AddRange(new object[] {
            "<",
            "≤",
            ">",
            "≥",
            "="});
            this.cmbArea.Location = new System.Drawing.Point(161, 19);
            this.cmbArea.Name = "cmbArea";
            this.cmbArea.Size = new System.Drawing.Size(34, 22);
            this.cmbArea.TabIndex = 4;
            // 
            // btnOK
            // 
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(213, 253);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 8;
            this.btnOK.Text = "确定";
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(309, 253);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 9;
            this.btnCancel.Text = "取消";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(281, 37);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(23, 12);
            this.label7.TabIndex = 0;
            this.label7.Text = "dBm";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(282, 32);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(17, 12);
            this.label8.TabIndex = 0;
            this.label8.Text = "米";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(282, 23);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(17, 12);
            this.label9.TabIndex = 0;
            this.label9.Text = "米";
            // 
            // numWeakAreaNum
            // 
            this.numWeakAreaNum.Location = new System.Drawing.Point(201, 47);
            this.numWeakAreaNum.Maximum = new decimal(new int[] {
            10000000,
            0,
            0,
            0});
            this.numWeakAreaNum.Name = "numWeakAreaNum";
            this.numWeakAreaNum.Size = new System.Drawing.Size(75, 21);
            this.numWeakAreaNum.TabIndex = 7;
            this.numWeakAreaNum.Value = new decimal(new int[] {
            2,
            0,
            0,
            0});
            // 
            // cmbWeakAreaNum
            // 
            this.cmbWeakAreaNum.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbWeakAreaNum.FormattingEnabled = true;
            this.cmbWeakAreaNum.Items.AddRange(new object[] {
            "<",
            "≤",
            ">",
            "≥",
            "="});
            this.cmbWeakAreaNum.Location = new System.Drawing.Point(161, 46);
            this.cmbWeakAreaNum.Name = "cmbWeakAreaNum";
            this.cmbWeakAreaNum.Size = new System.Drawing.Size(34, 22);
            this.cmbWeakAreaNum.TabIndex = 6;
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(42, 49);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(113, 12);
            this.label10.TabIndex = 0;
            this.label10.Text = "相邻弱覆盖村庄个数";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.numRxLev);
            this.groupBox1.Controls.Add(this.label7);
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Controls.Add(this.cmbRxLev);
            this.groupBox1.Location = new System.Drawing.Point(12, 12);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(372, 66);
            this.groupBox1.TabIndex = 10;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "弱覆盖";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.numCellDis);
            this.groupBox2.Controls.Add(this.label8);
            this.groupBox2.Controls.Add(this.label5);
            this.groupBox2.Controls.Add(this.cmbCell);
            this.groupBox2.Location = new System.Drawing.Point(12, 84);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(372, 66);
            this.groupBox2.TabIndex = 10;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "缺站";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.numAreaDis);
            this.groupBox3.Controls.Add(this.label9);
            this.groupBox3.Controls.Add(this.label6);
            this.groupBox3.Controls.Add(this.label10);
            this.groupBox3.Controls.Add(this.numWeakAreaNum);
            this.groupBox3.Controls.Add(this.cmbWeakAreaNum);
            this.groupBox3.Controls.Add(this.cmbArea);
            this.groupBox3.Location = new System.Drawing.Point(12, 156);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(372, 81);
            this.groupBox3.TabIndex = 10;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "相邻村庄";
            // 
            // WeakCvrSettingDlg
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(398, 288);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Name = "WeakCvrSettingDlg";
            this.Text = "缺站弱覆盖";
            this.Load += new System.EventHandler(this.WeakCvrSettingDlg_Load);
            ((System.ComponentModel.ISupportInitialize)(this.numRxLev)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCellDis)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numAreaDis)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakAreaNum)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.NumericUpDown numRxLev;
        private System.Windows.Forms.NumericUpDown numCellDis;
        private System.Windows.Forms.NumericUpDown numAreaDis;
        private System.Windows.Forms.ComboBox cmbRxLev;
        private System.Windows.Forms.ComboBox cmbCell;
        private System.Windows.Forms.ComboBox cmbArea;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.NumericUpDown numWeakAreaNum;
        private System.Windows.Forms.ComboBox cmbWeakAreaNum;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.GroupBox groupBox3;
    }
}