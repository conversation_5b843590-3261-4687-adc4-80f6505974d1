﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.Util
{
    /// <summary>
    /// 通过后缀表达式实现的简单的四则运算
    /// </summary>
    public static class SimpleCalculator
    {
        // 判断字符串是否为数值
        private static bool isNumberExp(string str)
        {
            double d = 0;
            return double.TryParse(str, out d);
        }

        // 基本一目计算
        private static double account(double n1, double n2, string num_op)
        {
            double aresult = 0;
            switch (num_op)
            {
                case "+":
                    aresult = n1 + n2;
                    break;
                case "-":
                    aresult = n1 - n2;
                    break;
                case "*":
                    aresult = n1 * n2;
                    break;
                case "/":
                    aresult = n1 / n2;
                    break;
            }
            return aresult;
        }

        /// <summary>
        /// 将String类型表达式转为由操作数和运算符组成的ArrayList类型表达式
        /// </summary>
        /// <param name="exp"></param>
        /// <param name="elementList"></param>
        /// <returns>true:表达式正确;false:表达式错误</returns>
        public static bool ToExpElementList(string exp, out ArrayList elementList)
        {
            StringBuilder element = new StringBuilder();
            string lastCh = string.Empty;
            elementList = new ArrayList();
            for (int i = 0; i < exp.Length; i++)
            {
                char ch = exp[i];
                //如果该字符为数字,小数字或者负号(非运算符的减号）
                if (char.IsNumber(ch) || ch == '.'
                    || ((i == 0 || lastCh == "(") && ch == '-'))//负数情况
                {
                    element.Append(ch.ToString());//存为操作数
                }
                else if (ch == '+' || ch == '-' || ch == '*' || ch == '/'
                    || ch == '(' || ch == ')')
                {
                    //将操作数添加到ArrayList类型表达式
                    if (!string.IsNullOrEmpty(element.ToString()))
                    {
                        elementList.Add(element.ToString());
                    }
                    //将运算符添加到ArrayList类型表达式
                    elementList.Add(ch.ToString());
                    element = new StringBuilder();
                }
                else
                {//表达式若出现非数字字符，非四则运算符，视为无效表达式
                    return false;
                }
                lastCh = ch.ToString();
            }
            //如果还有操作数未添加到ArrayList类型表达式,则执行添加操作
            if (!string.IsNullOrEmpty(element.ToString()))
            {
                elementList.Add(element.ToString());
            }
            return true;
        }

        //返回运算符的优先级
        private static int operatorOrder(string op)
        {
            switch (op)
            {
                case "*":
                    return 3;
                case "/":
                    return 4;
                case "+":
                    return 1;
                case "-":
                    return 2;
                default:
                    return 0;
            }
        }

        private static bool isPop(string op, Stack operators)
        {
            if (operators.Count == 0)
            {
                return false;
            }
            else
            {
                if (operators.Peek().ToString() == "("
                    || operatorOrder(op) > operatorOrder(operators.Peek().ToString()))
                {
                    return false;
                }
                else
                {
                    return true;
                }
            }
        }

        //将ArrayList类型的中缀表达式转为ArrayList类型的后缀表达式
        private static ArrayList toExpBackList(ArrayList exp)
        {
            ArrayList backList = new ArrayList();
            Stack operators = new Stack();
            //遍历ArrayList类型的中缀表达式
            foreach (string s in exp)
            {
                //若为数字则添加到ArrayList类型的后缀表达式
                if (isNumberExp(s))
                {
                    backList.Add(s);
                }
                else
                {
                    setOperators(backList, operators, s);
                }
            }
            while (operators.Count != 0)
            {
                backList.Add(operators.Pop().ToString());
            }
            return backList;
        }

        private static void setOperators(ArrayList backList, Stack operators, string s)
        {
            switch (s)
            {
                case "+":
                case "-":
                case "*":
                case "/":
                    while (isPop(s, operators))
                    {
                        backList.Add(operators.Pop().ToString());
                    }
                    operators.Push(s);
                    break;
                case "(":
                    operators.Push(s);
                    break;
                case ")":
                    while (operators.Count != 0)
                    {
                        string op = operators.Pop().ToString();
                        if (op != "(")
                        {
                            backList.Add(op);
                        }
                        else
                        {
                            break;
                        }
                    }
                    break;
            }
        }

        /// <summary>
        /// 计算一个后缀表达式List的值，若该后缀表达式错误，返回double.NaN
        /// </summary>
        /// <param name="expBack"></param>
        /// <returns></returns>
        public static double Compute(ArrayList expBack)
        {
            Stack num = new Stack();
            try
            {
                foreach (string n in expBack)
                {
                    if (isNumberExp(n))
                    {
                        num.Push(n);
                    }
                    else
                    {
                        double num2 = Convert.ToDouble(num.Pop());
                        double num1 = Convert.ToDouble(num.Pop());
                        num.Push(account(num1, num2, n));
                    }
                }
            }
            catch (Exception)
            {
                return double.NaN;
            }
            if (num.Count == 1)
            {
                double result;
                if (!double.TryParse(num.Pop().ToString(), out result))
                {
                    result = double.NaN;
                }
                return result;
            }
            else
            {
                return double.NaN;
            }
        }

        /// <summary>
        /// 计算表达式（只支持简单的四则运算）
        /// </summary>
        /// <param name="expresstion"></param>
        /// <returns>表达式不正确返回double.NaN</returns>
        public static double Compute(string expresstion)
        {
            string str = expresstion.Replace(" ", string.Empty);
            ArrayList list = null;
            if (ToExpElementList(str, out list))
            {
                return Compute(toExpBackList(list));
            }
            return double.NaN;
        }

    }
}
