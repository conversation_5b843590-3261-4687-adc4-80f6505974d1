﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc.GridOrderCommon
{
    public class GridOrderLayer : LayerBase
   {
        public GridOrderLayer()
            : base("栅格工单图层")
        {
        }

        readonly SolidBrush brush = new SolidBrush(Color.FromArgb(200, Color.Orange));
        public List<GridOrder> Orders { get; set; }
        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible || Orders == null || Orders.Count == 0)
            {
                return;
            }

            Rectangle inflatedRect = new Rectangle(updateRect.X, updateRect.Y, updateRect.Width, updateRect.Height);
            inflatedRect.Inflate(50, 50);
            DbRect dRect;
            this.gisAdapter.FromDisplay(inflatedRect, out dRect);

            foreach (GridOrder item in Orders)
            {
                if (item.Grids == null)
                {
                    continue;
                }
                foreach (OrderGridItem grid in item.Grids)
                {
                    if (dRect.Within(grid.Bounds))
                    {
                        RectangleF rect;
                        this.gisAdapter.ToDisplay(grid.Bounds, out rect);
                        graphics.FillRectangle(brush,rect);
                    }
                }
            }

        }

    }
}
