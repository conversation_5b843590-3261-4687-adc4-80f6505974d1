﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteWeakCellInfo
    {
        public LteWeakCellInfo(int districtId)
        {
            this.districtId = districtId;
        }

        public LteWeakCellInfo(int districtId, LTECell lteCell)
        {
            this.districtId = districtId;
            this.LteCell = lteCell;
        }
        public int SN { get; set; }

        protected int districtId { get; set; }
        public string DistrictName
        {
            get { return DistrictManager.GetInstance().getDistrictName(districtId); }
        }

        protected string gridDesc = null;
        public string GridDesc
        {
            get
            {
                if (this.gridDesc == null)
                {
                    this.GetGridDesc();
                }
                return this.gridDesc;
            }
        }
        public void GetGridDesc()
        {
            this.gridDesc = GISManager.GetInstance().GetGridDesc(this.Longitude, this.Latitude);
        }


        protected readonly List<string> fileNameList = new List<string>();
        public string FileNameDes
        {
            get
            {
                StringBuilder strb = new StringBuilder();
                foreach (string strFileName in fileNameList)
                {
                    strb.Append(strFileName + ";");
                }
                if (strb.Length > 0)
                {
                    strb.Remove(strb.Length - 1, 1);
                }
                return strb.ToString();
            }
        }

        public LTECell LteCell { get; set; }
        public virtual string CellName { get { return LteCell.Name; } }
        public virtual int TAC { get { return LteCell.TAC; } }
        public virtual int ECI { get { return LteCell.ECI; } }
        public virtual double Longitude { get { return LteCell.Longitude; } }
        public virtual double Latitude { get { return LteCell.Latitude; } }

        public List<TestPoint> TestPoints { get; set; } = new List<TestPoint>();

        public int TestPointCount
        {
            get { return TestPoints.Count; }
        }

        protected int rsrpCount = 0;
        protected float rsrpSum = 0;
        public float? RsrpAvg
        {
            get
            {
                return rsrpCount > 0 ? (float?)(Math.Round(rsrpSum / rsrpCount, 2)) : null;
            }
        }
        public float? RsrpMax { get; set; }
        public float? RsrpMin { get; set; }

        protected int sinrCount = 0;
        protected float sinrSum = 0;
        public float? SinrAvg
        {
            get
            {
                return sinrCount > 0 ? (float?)(Math.Round(sinrSum / sinrCount, 2)) : null;
            }
        }
        public float? SinrMax { get; set; }
        public float? SinrMin { get; set; }

        protected int rssiCount = 0;
        protected float rssiSum = 0;
        public float? RssiAvg
        {
            get
            {
                return rssiCount > 0 ? (float?)(Math.Round(rssiSum / rssiCount, 2)) : null;
            }
        }

        protected int pathlossCount = 0;
        protected float pathlossSum = 0;
        public float? PathlossAvg
        {
            get
            {
                return pathlossCount > 0 ? (float?)(Math.Round(pathlossSum / pathlossCount, 2)) : null;
            }
        }

        public virtual void AddTestPoint(TestPoint tp)
        {
            this.TestPoints.Add(tp);
            if (!this.fileNameList.Contains(tp.FileName))
            {
                this.fileNameList.Add(tp.FileName);
            }

            float? rsrp = (float?)tp["lte_RSRP"];
            if (rsrp != null)
            {
                rsrpCount++;
                rsrpSum += (float)rsrp;
                RsrpMax = RsrpMax == null ? rsrp : Math.Max((float)rsrp, (float)RsrpMax);
                RsrpMin = RsrpMin == null ? rsrp : Math.Min((float)rsrp, (float)RsrpMin);
            }

            float? sinr = (float?)tp["lte_SINR"];
            if (sinr != null)
            {
                sinrCount++;
                sinrSum += (float)sinr;
                SinrMax = SinrMax == null ? sinr : Math.Max((float)sinr, (float)SinrMax);
                SinrMin = SinrMin == null ? sinr : Math.Min((float)sinr, (float)SinrMin);
            }

            float? rssi = (float?)tp["lte_RSSI"];
            if (rssi != null)
            {
                rssiCount++;
                rssiSum += (float)rssi;
            }

            short? pathloss = (short?)tp["lte_Pathloss"];
            if (pathloss != null)
            {
                pathlossCount++;
                pathlossSum += (short)pathloss;
            }
        }
    }
}
