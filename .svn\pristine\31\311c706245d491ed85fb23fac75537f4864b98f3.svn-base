﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using DevExpress.XtraGrid.Views.Base;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class DIYMRDataResultForm : MinCloseForm
    {
        private MainModel mainModel;
        private List<MRDataResult> resultList;
        private MRDataCondition condition;

        public DIYMRDataResultForm(MainModel mainModel, MRDataCondition condition)
            : base(mainModel)
        {
            this.mainModel = mainModel;
            this.condition = condition;
            this.resultList = new List<MRDataResult>();
            InitializeComponent();
            DisposeWhenClose = true;
        }

        public void FillData(List<MRDataResult> resultList)
        {
            this.resultList.Clear();
            this.resultList.AddRange(resultList);
            this.gridControl1.DataSource = this.resultList;
            this.gridControl1.RefreshDataSource();
        }

        private void gridView_CustomDrawCell(object sender, RowCellCustomDrawEventArgs e)
        {
            bool isInvalid = false;
            double value = Convert.ToDouble(e.CellValue);
            if (double.IsNaN(value))
            {
                e.DisplayText = "-";
                return;
            }

            switch (e.Column.FieldName)
            {
                case "UL90Cover" :
                    isInvalid = value <= condition.UL90Cover * 100;
                    break;
                case "DL90Cover" :
                    isInvalid = value <= condition.DL90Cover * 100;
                    break;
                case "CoverImbalance":
                    isInvalid = value >= condition.CoverImbalance;
                    break;
                case "ULQual":
                    isInvalid = value <= condition.ULQual * 100;
                    break;
                case "DLQual":
                    isInvalid = value <= condition.DLQual * 100;
                    break;
                case "TA":
                    isInvalid = value <= condition.TA * 100;
                    break;
                case "PathLossImbalance":
                    isInvalid = value <= condition.PathLossImbalance * 100;
                    break;
            }
            if (isInvalid)
            {
                e.Appearance.BackColor = Color.Red;
            }
        }

        private void miExportExcel_Clicked(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(this.gridView1);
        }

        private MRDataCloudPictureSettingForm cloudForm;
        private void miCloudPicture_Clicked(object sender, EventArgs e)
        {
            List<string> columns = new List<string>();
            foreach (DevExpress.XtraGrid.Columns.GridColumn col in gridView1.Columns)
            {
                columns.Add(col.Caption);
            }
            if (cloudForm == null)
            {
                cloudForm = new MRDataCloudPictureSettingForm(columns);
            }
            if (cloudForm.ShowDialog() == DialogResult.OK)
            {
                WaitBox.Show(DrawCloudPicture);
            }
        }

        private List<Cell> cellList;
        private void ColloctCells()
        {
            if (cellList == null)
            {
                cellList = new List<Cell>();
                for (int i = 0; i < resultList.Count; ++i)
                {
                    Cell cell = CellManager.GetInstance().GetCurrentCell(resultList[i].Lac, resultList[i].Ci);
                    cellList.Add(cell);
                }
            }
        }

        private void CalcCoverRadius()
        {
            int loop = 0;
            WaitBox.Text = "正在计算理想覆盖半径...";
            foreach (Cell cell in cellList)
            {
                WaitBox.ProgressPercent = ++loop * 100 / cellList.Count;
                if (cell == null)
                {
                    continue;
                }
                CellRadiusManager.Set(cell);
            }
        }

        private void DrawCloudPicture()
        {
            // 收集需要计算的小区
            ColloctCells();

            // 小区理想覆盖半径
            CalcCoverRadius();

            // 收集列数据
            DevExpress.XtraGrid.Columns.GridColumn selCol = gridView1.Columns[cloudForm.SelectedIndex];
            List<CellCloudPictureData> cloudDataList = new List<CellCloudPictureData>();
            WaitBox.Text = "正在收集云图数据...";
            for (int i = 0; i < cellList.Count && i < gridView1.RowCount; ++i)
            {
                WaitBox.ProgressPercent = (i + 1) * 100 / cellList.Count;

                Cell cell = cellList[i];
                if (cell == null)
                {
                    continue;
                }

                double radius = CellRadiusManager.Get(cell);
                if (radius == -1)
                {
                    continue;
                }
                
                object obj = gridView1.GetRowCellValue(i, selCol);
                if (obj == null)
                {
                    continue;
                }

                double value = 0;
                try
                {
                    value = Convert.ToDouble(obj);
                }
                catch
                {
                    continue;
                }

                CellCloudPictureData data = new CellCloudPictureData();
                data.Weight = value;
                data.Direction = cell.Direction;
                data.Longitude = cell.Longitude;
                data.Latitude = cell.Latitude;
                data.Radius = radius;
                cloudDataList.Add(data);
            }

            CellCloudPictureLayer layer = CellCloudPictureLayer.GetInstance();
            layer.Config = cloudForm.Config;
            layer.Datas = cloudDataList;
            layer.ClearCache();
            System.Threading.Thread.Sleep(500);
            WaitBox.Close();
            mainModel.MainForm.GetMapForm().FireCellCloudPictureShow();
        }
    }
}
