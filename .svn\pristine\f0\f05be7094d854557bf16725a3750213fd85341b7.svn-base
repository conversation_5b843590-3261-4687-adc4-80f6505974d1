﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRModInterferer
    {
        private static readonly NRModInterferer instance = new NRModInterferer();
        public static NRModInterferer Instance
        {
            get { return instance; }
        }

        private NRModInterferer()
        {
        }

        /// <summary>
        /// 从srcCells分析对tarCell造成干扰的小区
        /// </summary>
        /// <param name="tarCell">目标小区</param>
        /// <param name="srcCells">可能存在干扰的小区</param>
        /// <param name="cond"></param>
        /// <returns></returns>
        public List<NRModInterfereCell> Stat(NRCell tarCell, List<NRCell> srcCells, NRModInterfereCond cond)
        {
            List<NRCell>[] arrayList = ClassifyByModX(srcCells, cond.ModX);
            int tarMod = tarCell.PCI % cond.ModX;
            return statInSameMod(tarCell, arrayList[tarMod], tarMod, cond);
        }

        /// <summary>
        /// 按模值归类小区
        /// </summary>
        /// <param name="cellList"></param>
        /// <param name="modX"></param>
        /// <returns></returns>
        private List<NRCell>[] ClassifyByModX(List<NRCell> cellList, int modX)
        {
            List<NRCell>[] retArray = new List<NRCell>[modX];
            for (int i = 0; i < retArray.Length; ++i)
            {
                retArray[i] = new List<NRCell>();
            }
            foreach (NRCell cell in cellList)
            {
                int mod = cell.PCI % modX;
                retArray[mod].Add(cell);
            }
            return retArray;
        }

        /// <summary>
        /// 分析EARFCN,PCI,DISTANCE,ANGLE四个因素
        /// </summary>
        /// <param name="tarCell"></param>
        /// <param name="srcCells"></param>
        /// <param name="cond"></param>
        /// <returns></returns>
        private List<NRModInterfereCell> statInSameMod(NRCell tarCell, List<NRCell> srcCells, int tarMod
            , NRModInterfereCond cond)
        {
            List<NRModInterfereCell> retList = new List<NRModInterfereCell>();
            if (!cond.Sids.Contains(tarMod))
            {
                return retList;
            }

            foreach (NRCell cell in srcCells)
            {
                if (tarCell.ID == cell.ID)
                {
                    continue;
                }

                if (tarCell.SSBARFCN != cell.SSBARFCN)
                {
                    continue;
                }

                double distance = getDistance(tarCell, cell);
                if (distance > cond.Distance)
                {
                    continue;
                }

                bool inRange = judgeAngle(tarCell, cell, cond.Angle);
                if (!inRange)
                {
                    continue;
                }

                NRModInterfereCell interCell = new NRModInterfereCell(cell, distance);
                retList.Add(interCell);
            }
            return retList;
        }

        private double getDistance(NRCell tarCell, NRCell srcCell)
        {
            return MathFuncs.GetDistance(tarCell.Longitude, tarCell.Latitude, srcCell.Longitude, srcCell.Latitude);
        }

        private bool judgeAngle(NRCell tarCell, NRCell srcCell, double angleRange)
        {
            return MathFuncs.JudgePoint(tarCell.Longitude, tarCell.Latitude, srcCell.Longitude, srcCell.Latitude, tarCell.Direction, (int)angleRange);
        }
    }

    /// <summary>
    /// 小区模干扰分析条件
    /// </summary>
    public class NRModInterfereCond
    {
        public double Angle { get; set; } = 180;
        public double Distance { get; set; } = 2000;
        public List<int> Sids { get; set; }  // 模值
        public int ModX { get; set; } = 3;//默认模3干扰,nr需要对3/4/6进行分析

        public NRModInterfereCond()
        {
            Sids = new List<int>();
        }
    }

    /// <summary>
    /// 模干扰来源小区
    /// </summary>
    public class NRModInterfereCell
    {
        public NRCell Cell { get; set; }
        public double Distance { get; set; } // 与干扰目标的距离
        public NRModInterfereCell()
        {
        }
        public NRModInterfereCell(NRCell cell, double distance)
        {
            Cell = cell;
            Distance = distance;
        }
    }
}
