﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Xml;
using MasterCom.RAMS.CQT;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Stat.Data;
using MasterCom.Util;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public class RegionCellDataManager
    {
        private RegionCellDataManager()
        {
            MasterCom.RAMS.Model.MainModel.GetInstance().ClearDataEvent += RegionCellDataManager_ClearDataEvent;
        }

        void RegionCellDataManager_ClearDataEvent(object sender, EventArgs e)
        {
            MasterCom.RAMS.Model.MainModel.GetInstance().ClearDataEvent -= RegionCellDataManager_ClearDataEvent;
            instance = null;
        }

        private static RegionCellDataManager instance { get; set; }
        public static RegionCellDataManager GetInstance()
        {
            if (instance == null)
            {
                instance = new RegionCellDataManager();
            }
            return instance;
        }


    }
    /// <summary>
    /// 终端满意度统计(裴福平  2013-10-16)
    /// </summary>
    public class TerminalStatData
    {
        /// <summary>
        /// 月份
        /// </summary>
        public string Month { get; set; }
        /// <summary>
        /// 地市
        /// </summary>
        public string City { get; set; }
        /// <summary>
        /// 终端类型
        /// </summary>
        public string TerminalType { get; set; }
        public double networkCover { get; set; }
        /// <summary>
        /// 网络覆盖良好占比
        /// </summary>
        public string NetworkCover
        {
            get { return networkCover.ToString("0.00") + "%"; }
        }
        public double networkQuality { get; set; }
        /// <summary>
        /// 网络质量良好占比
        /// </summary>
        public string NetworkQuality
        {
            get { return networkQuality.ToString("0.00") + "%"; }
        }
        public double mobileInternet { get; set; }
        /// <summary>
        /// 手机上网良好占比
        /// </summary>
        public string MobileInternet
        {
            get { return mobileInternet.ToString("0.00") + "%"; }
        }
        public double voiceCall { get; set; }
        /// <summary>
        /// 语音通话良好占比
        /// </summary>
        public string VoiceCall
        {
            get { return voiceCall.ToString("0.00") + "%"; }
        }
    }
    /// <summary>
    /// 网格投诉量统计数据对象(裴福平 2013-10-21)
    /// </summary>
    public class RegionComplainItem
    {
        public string ID { get; set; }
        public string GridName { get; set; }
        public string Region { get; set; }
        public string Town { get; set; }
        public string GridScene { get; set; }
        public int ItemCount { get; set; }

        private double gsmAllScore;
        public string GSMAllScore
        {
            get { return gsmAllScore == 0 ? "无" : gsmAllScore.ToString("0.00"); }
            set { gsmAllScore = (value == null || value.Trim() == "" ? 0 : double.Parse(value)); }
        }

        private double tdAllScore;
        public string TDAllScore
        {
            get { return tdAllScore == 0 ? "无" : tdAllScore.ToString("0.00"); }
            set { tdAllScore = (value == null || value.Trim() == "" ? 0 : double.Parse(value)); }
        }
        
        public string LinkMan { get; set; }
        public string LinkPhone { get; set; }
    }
    /// <summary>
    /// 网格KQI(裴福平 2013-10-21)
    /// </summary>
    public class RegionKQIItem
    {
        public string IndicatorName { get; set; }
        public double score { get; set; }
        public string Score
        {
            get { return score == 0 ? "无" : score.ToString("0.00"); }
            set { score = (value == null || value.Trim() == "" ? 0 : double.Parse(value)); }
        }
    }
    /// <summary>
    /// 网格KPI(裴福平 2013-10-21)
    /// </summary>
    public class RegionKPIItem : RegionKQIItem
    {
        private double indicatorValue;
        public string IndicatorValue
        {
            get { return indicatorValue.ToString("0.00"); }
            set { indicatorValue = (value == null || value.Trim() == "" ? 0 : double.Parse(value)); }
        }
    }
    /// <summary>
    /// 用户满意度分析类(裴福平 2013-10-21)
    /// </summary>
    public class UserSatisfactionItem
    {
        public UserSatisfactionItem() { }
        public UserSatisfactionItem(string userphone, string usersex, string terminaltype, int networkcover, int networkquality, int mobileinternet, int voicecall)
        {
            this.UserPhone = userphone;
            this.UserSex = usersex;
            this.TerminalType = terminaltype;
            this.NetworkCover = networkcover;
            this.NetworkQuality = networkquality;
            this.MobileInternet = mobileinternet;
            this.VoiceCall = voicecall;
        }
        /// <summary>
        /// 用户号码
        /// </summary>
        public string UserPhone { get; set; }
        /// <summary>
        /// 用户性别
        /// </summary>
        public string UserSex { get; set; }
        /// <summary>
        /// 终端类型
        /// </summary>
        public string TerminalType { get; set; }
        /// <summary>
        /// 网络覆盖
        /// </summary>
        public int NetworkCover { get; set; }
        /// <summary>
        /// 网络质量
        /// </summary>
        public int NetworkQuality { get; set; }
        /// <summary>
        /// 手机上网
        /// </summary>
        public int MobileInternet { get; set; }
        /// <summary>
        /// 语音通话
        /// </summary>
        public int VoiceCall { get; set; }
        private readonly Dictionary<string, CellScore> cellScoreMap = new Dictionary<string, CellScore>();
        public void AddCellScore(CellScore cellscore)
        {
            if (!cellScoreMap.ContainsKey(cellscore.LAC + "-" + cellscore.CI))
            {
                cellScoreList.Add(cellscore);
                cellScoreMap[cellscore.LAC + "-" + cellscore.CI] = cellscore;
            }
        }
        private readonly List<CellScore> cellScoreList = new List<CellScore>();
        /// <summary>
        /// 话单占用小区列表
        /// </summary>
        public List<CellScore> CellScoreList
        {
            get { return cellScoreList; }
        }
        private readonly Dictionary<string, GridScore> gridScoreMap = new Dictionary<string, GridScore>();
        public void AddGridScore(GridScore gridscore)
        {
            if (!gridScoreMap.ContainsKey(gridscore.GridName))
            {
                gridScoreMap[gridscore.GridName] = gridscore;
                gridScoreList.Add(gridscore);
            }
        }
        private readonly List<GridScore> gridScoreList = new List<GridScore>();
        /// <summary>
        /// 话单占用网格列表
        /// </summary>
        public List<GridScore> GridScoreList
        {
            get { return gridScoreList; }
        }
    }
    /// <summary>
    /// 用户占用小区打分信息(裴福平 2013-10-21)
    /// </summary>
    public class CellScore
    {
        public CellScore() { }
        public CellScore(string cellname, string lac, string ci, string network, double gsmallscore, double tdallscore)
        {
            CellName = cellname;
            LAC = lac;
            CI = ci;
            Network = network;
            gsmAllScore = gsmallscore;
            tdAllScore = tdallscore;
        }
        public string CellName { get; set; }
        public string LAC { get; set; }
        public string CI { get; set; }
        public string Network { get; set; }
        private double gsmAllScore;
        public string GSMAllScore
        {
            get { return gsmAllScore.ToString("0.00"); }
            set { gsmAllScore = double.Parse(value); }
        }
        private double tdAllScore;
        public string TDAllScore
        {
            get { return tdAllScore.ToString("0.00"); }
            set { tdAllScore = double.Parse(value); }
        }
        
        public List<Vertex[]> Vertexs { get; set; } = new List<Vertex[]>();
    }
    /// <summary>
    /// 用户占用网格打分信息(裴福平 2013-10-21)
    /// </summary>
    public class GridScore
    {
        public GridScore() { }
        public GridScore(string gridname, string gridschene, int complaincount, double gsmallscore, double tdallscore)
        {
            this.GridName = gridname;
            this.GridSchene = gridschene;
            this.ComplaintCount = complaincount;
            this.gsmAllScore = gsmallscore;
            this.tdAllScore = tdallscore;
        }
        public string GridName { get; set; }
        public string GridSchene { get; set; }
        public int ComplaintCount { get; set; }

        private double gsmAllScore;
        public string GSMAllScore
        {
            get { return gsmAllScore.ToString("0.00"); }
            set { gsmAllScore = double.Parse(value); }
        }
        private double tdAllScore;
        public string TDAllScore
        {
            get { return tdAllScore.ToString("0.00"); }
            set { tdAllScore = double.Parse(value); }
        }
    }
}
