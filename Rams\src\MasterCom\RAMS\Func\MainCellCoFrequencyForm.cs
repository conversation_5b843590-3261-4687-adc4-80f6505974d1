﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.Utils.Paint;

namespace MasterCom.RAMS.Func
{
    public partial class MainCellCoFrequencyForm : BaseFormStyle
    {
        public MainCellCoFrequencyForm(MainModel mm)
        {
            InitializeComponent();
            mainModel = mm;
            mainModel.SelectedTestPointsChanged += new EventHandler(SelectedTestPointsChanged);
            SelectedTestPointsChanged(this, EventArgs.Empty);
        }

        private int cellCnt
        {
            get { return (int)numCellCnt.Value; }
        }
        public void SelectedTestPointsChanged(object sender, EventArgs e)
        {
            if (mainModel.SelectedTestPoints.Count == 0)
            {
                clearGridView();
                return;
            }

            TestPoint testPoint = mainModel.SelectedTestPoints[0];
            ICell cell = testPoint.GetMainCell();
            if (cell is Cell)
            {
                mainModel.MainForm.GetMapForm().SetWholeMapInterference(cell as Cell, true, false);
                fillTopNNearestCoFreqCell(cell as Cell, testPoint, cellCnt);
            }
            else if (cell is TDCell)
            {
                mainModel.MainForm.GetMapForm().SetWholeMapInterference(cell as TDCell, true, false);
                fillTopNNearestCoFreqCell(cell as TDCell, testPoint, cellCnt);
            }
            else
            {
                clearGridView();
            }
        }

        private List<int> coFreq = new List<int>();
        private void fillTopNNearestCoFreqCell(Cell svrCell, TestPoint testPoint, int topN)
        {
            coFreq = new List<int>();
            List<Cell> nearestCells;
            List<double> nearestDis;
            getNearestDataInfo(svrCell, testPoint, topN, out nearestCells, out nearestDis);

            addLocalCoBcchTch(svrCell);

            List<CellTestPointInfo> cells = new List<CellTestPointInfo>();
            PointF oPoint = new PointF((float)svrCell.Longitude, (float)svrCell.Latitude);
            PointF aPoint = new PointF((float)svrCell.EndPointLongitude, (float)svrCell.EndPointLatitude);
            PointF bPoint = new PointF((float)testPoint.Longitude, (float)testPoint.Latitude);
            int angle = (int)MathFuncs.CalAngle(oPoint, aPoint, bPoint);
            CellTestPointInfo svrCellInfo = new CellTestPointInfo(svrCell, true, svrCell.GetDistance(testPoint.Longitude, testPoint.Latitude), angle);
            cells.Add(svrCellInfo);
            List<ICell> coCells = new List<ICell>();
            for (int i = 0; i < nearestCells.Count && i < topN; i++)
            {
                Cell nCell = nearestCells[i];
                coCells.Add(nCell);
                oPoint = new PointF((float)nCell.Longitude, (float)nCell.Latitude);
                aPoint = new PointF((float)nCell.EndPointLongitude, (float)nCell.EndPointLatitude);
                angle = (int)MathFuncs.CalAngle(oPoint, aPoint, bPoint);
                CellTestPointInfo cellInfo = new CellTestPointInfo(nCell, false, nearestDis[i], angle);
                cells.Add(cellInfo);
            }
            if (coCells.Count > 0)
            {
                mainModel.CoCellTestPointDic = new Dictionary<TestPoint, List<ICell>>();
                mainModel.CoCellTestPointDic[testPoint] = coCells;
            }
            fillGridView(cells);
            colBSIC.Visible = true;
        }

        private void getNearestDataInfo(Cell svrCell, TestPoint testPoint, int topN, out List<Cell> nearestCells, out List<double> nearestDis)
        {
            nearestCells = new List<Cell>(topN);
            nearestDis = new List<double>(topN);
            List<Cell> coFreqCells2Remove = new List<Cell>();
            foreach (Cell cell in mainModel.LocalCoBcchTch.Keys)
            {
                if (cell.Name == svrCell.Name)
                {
                    coFreqCells2Remove.Add(cell);
                }
                else
                {
                    addValidNearestData(testPoint, topN, nearestCells, nearestDis, coFreqCells2Remove, cell);
                }
            }
            foreach (Cell item in coFreqCells2Remove)
            {
                if (item.Equals(svrCell))
                {
                    continue;
                }
                mainModel.LocalCoBcchTch.Remove(item);
            }
        }

        private static void addValidNearestData(TestPoint testPoint, int topN, List<Cell> nearestCells, 
            List<double> nearestDis, List<Cell> coFreqCells2Remove, Cell cell)
        {
            double dis = cell.GetDistance(testPoint.Longitude, testPoint.Latitude);
            if (nearestDis.Count == 0)
            {
                nearestDis.Add(dis);
                nearestCells.Add(cell);
            }
            else
            {
                int insertIdx = -1;
                for (int i = 0; i < nearestDis.Count; i++)//List由最近到最远
                {
                    double nDis = nearestDis[i];
                    if (dis < nDis)//最近距离，往前插入
                    {
                        insertIdx = i;
                        break;
                    }
                }
                if (insertIdx != -1)
                {
                    nearestDis.Insert(insertIdx, dis);
                    nearestCells.Insert(insertIdx, cell);
                }
                else
                {
                    coFreqCells2Remove.Add(cell);
                }
                if (nearestDis.Count > topN)
                {
                    if (!coFreqCells2Remove.Contains(nearestCells[topN]))
                    {
                        coFreqCells2Remove.Add(nearestCells[topN]);
                    }
                    nearestDis.RemoveAt(topN);
                    nearestCells.RemoveAt(topN);
                }
            }
        }

        private void addLocalCoBcchTch(Cell svrCell)
        {
            List<short> tempList = new List<short>();
            foreach (KeyValuePair<Cell, List<short>> kvp in mainModel.LocalCoBcchTch)
            {
                foreach (short item in kvp.Value)
                {
                    if (!coFreq.Contains((int)item))
                    {
                        coFreq.Add((int)item);
                        tempList.Add(item);
                    }
                }
            }
            Cell tmpCell = CellManager.GetInstance().GetCellByName(svrCell.Name);
            if (!mainModel.LocalCoBcchTch.ContainsKey(tmpCell))
            {
                mainModel.LocalCoBcchTch.Add(tmpCell, tempList);
            }
        }

        private void fillTopNNearestCoFreqCell(TDCell svrCell, TestPoint testPoint, int topN)
        {
            coFreq = new List<int>();
            List<TDCell> nearestCells;
            List<double> nearestDis;
            getNearestDataInfo(svrCell, testPoint, topN, out nearestCells, out nearestDis);

            addCoCpiFreq(svrCell);

            List<CellTestPointInfo> cells = new List<CellTestPointInfo>();
            PointF oPoint = new PointF((float)svrCell.Longitude, (float)svrCell.Latitude);
            PointF aPoint = new PointF((float)svrCell.EndPointLongitude, (float)svrCell.EndPointLatitude);
            PointF bPoint = new PointF((float)testPoint.Longitude, (float)testPoint.Latitude);
            int angle = (int)MathFuncs.CalAngle(oPoint, aPoint, bPoint);
            CellTestPointInfo svrCellInfo = new CellTestPointInfo(svrCell, true, svrCell.GetDistance(testPoint.Longitude, testPoint.Latitude), angle);
            cells.Add(svrCellInfo);
            List<ICell> coCells = new List<ICell>();
            for (int i = 0; i < nearestCells.Count && i < topN; i++)
            {
                TDCell nCell = nearestCells[i];
                coCells.Add(nCell);
                oPoint = new PointF((float)nCell.Longitude, (float)nCell.Latitude);
                aPoint = new PointF((float)nCell.EndPointLongitude, (float)nCell.EndPointLatitude);
                angle = (int)MathFuncs.CalAngle(oPoint, aPoint, bPoint);
                CellTestPointInfo cellInfo = new CellTestPointInfo(nCell, false, nearestDis[i], angle);
                cells.Add(cellInfo);
            }
            if (coCells.Count > 0)
            {
                mainModel.CoCellTestPointDic = new Dictionary<TestPoint, List<ICell>>();
                mainModel.CoCellTestPointDic[testPoint] = coCells;
            }
            fillGridView(cells);
            colBSIC.Visible = false;
        }

        private void getNearestDataInfo(TDCell svrCell, TestPoint testPoint, int topN, out List<TDCell> nearestCells, out List<double> nearestDis)
        {
            nearestCells = new List<TDCell>(topN);
            nearestDis = new List<double>(topN);
            List<TDCell> coFreqCells2Remove = new List<TDCell>();
            foreach (TDCell cell in mainModel.CoCpiFreq.Keys)
            {
                if (cell.Name == svrCell.Name)
                {
                    coFreqCells2Remove.Add(cell);
                }
                else
                {
                    addValidNearestData(testPoint, topN, nearestCells, nearestDis, coFreqCells2Remove, cell);
                }
            }
            foreach (TDCell item in coFreqCells2Remove)
            {
                if (!item.Equals(svrCell))
                {
                    mainModel.CoCpiFreq.Remove(item);
                }
            }
        }

        private void addValidNearestData(TestPoint testPoint, int topN, List<TDCell> nearestCells,
            List<double> nearestDis, List<TDCell> coFreqCells2Remove, TDCell cell)
        {
            double dis = cell.GetDistance(testPoint.Longitude, testPoint.Latitude);
            if (nearestDis.Count == 0)
            {
                nearestDis.Add(dis);
                nearestCells.Add(cell);
            }
            else
            {
                int insertIdx = getInsertIdx(nearestDis, dis);
                if (insertIdx != -1)
                {
                    nearestDis.Insert(insertIdx, dis);
                    nearestCells.Insert(insertIdx, cell);
                }
                else
                {
                    if (nearestDis.Count < topN)
                    {
                        nearestDis.Add(dis);
                        nearestCells.Add(cell);
                    }
                    else
                    {
                        coFreqCells2Remove.Add(cell);
                    }
                }
                if (nearestDis.Count > topN)
                {
                    if (!coFreqCells2Remove.Contains(nearestCells[topN]))
                    {
                        coFreqCells2Remove.Add(nearestCells[topN]);
                    }
                    nearestDis.RemoveAt(topN);
                    nearestCells.RemoveAt(topN);
                }
            }
        }

        private static int getInsertIdx(List<double> nearestDis, double dis)
        {
            int insertIdx = -1;
            for (int i = 0; i < nearestDis.Count; i++)//List由最近到最远
            {
                double nDis = nearestDis[i];
                if (dis < nDis)//最近距离，往前插入
                {
                    insertIdx = i;
                    break;
                }
            }

            return insertIdx;
        }

        private void addCoCpiFreq(TDCell svrCell)
        {
            List<int> tempList = new List<int>();
            foreach (KeyValuePair<TDCell, List<int>> kvp in mainModel.CoCpiFreq)
            {
                foreach (int item in kvp.Value)
                {
                    if (!coFreq.Contains(item))
                    {
                        coFreq.Add(item);
                        tempList.Add(item);
                    }
                }
            }
            TDCell tmpCell = CellManager.GetInstance().GetTDCellByName(svrCell.Name);
            if (!mainModel.CoCpiFreq.ContainsKey(tmpCell))
            {
                mainModel.CoCpiFreq.Add(tmpCell, tempList);
            }
        }

        private void clearGridView()
        {
            gridControl.DataSource = null;
            gridControl.RefreshDataSource();
        }

        private void fillGridView(List<CellTestPointInfo> dataSource)
        {
            gridControl.DataSource = dataSource;
            gridControl.RefreshDataSource();
        }

        protected override void Dispose(bool disposing)
        {
            mainModel.SelectedTestPointsChanged -= new EventHandler(SelectedTestPointsChanged);
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void MainCellCoFrequencyForm_VisibleChanged(object sender, EventArgs e)
        {
            mainModel.SelectedTestPointsChanged -= new EventHandler(SelectedTestPointsChanged);
            if (Visible)
            {
                mainModel.SelectedTestPointsChanged += new EventHandler(SelectedTestPointsChanged);
            }
        }

        private void MainCellCoFrequencyForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                clearGridView();
                e.Cancel = true;
                this.Visible = false;
            }
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gridView.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }
            CellTestPointInfo cellInfo = gridView.GetRow(info.RowHandle) as CellTestPointInfo;
            if (cellInfo != null)
            {
                mainModel.MainForm.GetMapForm().GoToView(cellInfo.CellLongitude, cellInfo.CellLatitude, 2000);
            }
        }

        XPaint paint = new XPaint();
        private void gridView_CustomDrawCell(object sender, DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e)
        {
            if (e.Column == colBCCH)
            {
                int bcch = 0;
                if (int.TryParse(e.DisplayText, out bcch) && coFreq.Contains(bcch))
                {
                    paint.DrawMultiColorString(e.Cache, e.Bounds, e.DisplayText, e.DisplayText, e.Appearance, Color.Red, e.Appearance.GetBackColor(), false, 0);
                    e.Handled = true;
                }
            }
            else if (e.Column == colTCH)
            {
                string[] tchArr = e.DisplayText.Split(',');
                string matchedText, notMacthedText;
                getText(tchArr, out matchedText, out notMacthedText);
                if (!string.IsNullOrEmpty(matchedText))
                {
                    paint.DrawMultiColorString(e.Cache, e.Bounds, matchedText + notMacthedText, matchedText, e.Appearance, Color.Red, e.Appearance.GetBackColor(), false, 0);
                    e.Handled = true;
                }
            }

        }

        private void getText(string[] tchArr, out string matchedText, out string notMacthedText)
        {
            matchedText = "";
            notMacthedText = "";
            StringBuilder matchedSb = new StringBuilder();
            StringBuilder notMatchedSb = new StringBuilder();
            for (int i = 0; i < tchArr.Length; i++)
            {
                int tch = 0;
                if (int.TryParse(tchArr[i], out tch))
                {
                    if (coFreq.Contains(tch))
                    {
                        matchedSb.Append("," + tchArr[i]);
                    }
                    else
                    {
                        notMatchedSb.Append("," + tchArr[i]);
                    }
                }
            }
            matchedText = matchedSb.ToString();
            notMacthedText = notMacthedText.ToString();
            if (matchedText.Length > 0)
            {
                matchedText = matchedText.Remove(0, 1);
            }
            else if (notMacthedText.Length > 0)
            {
                notMacthedText = notMacthedText.Remove(0, 1);
            }
        }

        private void numCellCnt_EditValueChanged(object sender, EventArgs e)
        {
            SelectedTestPointsChanged(this, EventArgs.Empty);
        }

        private void chkShowLine_CheckedChanged(object sender, EventArgs e)
        {
            mainModel.IsShowCoCellLine = chkShowLine.Checked;
            mainModel.MainForm.GetMapForm().updateMap();
        }
    }

    class CellTestPointInfo
    {
        public CellTestPointInfo(ICell cell, bool isSvrCell, double distance2Testpoint, int angle2Testpoint)
        {
            this.cell = cell;
            this.distance2Testpoint = Math.Round(distance2Testpoint, 2);
            this.angle2Testpoint = angle2Testpoint;
            this.isSvrCell = isSvrCell;
        }
        private readonly bool isSvrCell = false;
        public string CellType
        {
            get
            {
                if (isSvrCell)
                {
                    return "MainCell";
                }
                else
                {
                    return "Co-Cell";
                }
            }
        }
        private readonly ICell cell;
        private readonly double distance2Testpoint = 0;
        private readonly int angle2Testpoint = 0;
        public string Name
        {
            get
            {
                return cell.Name;
            }
        }

        public double CellLongitude
        {
            get
            {
                return cell.Longitude;
            }
        }
        public double CellLatitude
        {
            get
            {
                return cell.Latitude;
            }
        }

        public int LAC
        {
            get
            {
                int lac = 0;
                if (cell is Cell)
                {
                    lac = (cell as Cell).LAC;
                }
                else if (cell is TDCell)
                {
                    lac = (cell as TDCell).LAC;
                }
                return lac;
            }
        }
        public int CI
        {
            get
            {
                int ci = 0;
                if (cell is Cell)
                {
                    ci = (cell as Cell).CI;
                }
                else if (cell is TDCell)
                {
                    ci = (cell as TDCell).CI;
                }
                return ci;
            }
        }
        public int BSIC
        {
            get
            {
                int bsic = 0;
                if (cell is Cell)
                {
                    bsic = (cell as Cell).BSIC;
                }
                else if (cell is TDCell)
                {
                    bsic = (cell as TDCell).FREQ;
                }
                return bsic;
            }
        }
        public int BCCH
        {
            get
            {
                int bcch = 0;
                if (cell is Cell)
                {
                    bcch = (cell as Cell).BCCH;
                }
                else if (cell is TDCell)
                {
                    bcch = (cell as TDCell).CPI;
                }
                return bcch;
            }
        }
        public string TCH
        {
            get
            {
                string tchStr = string.Empty;
                if (cell is Cell)
                {
                    StringBuilder sb = new StringBuilder();
                    foreach (short tch in (cell as Cell).TCH)
                    {
                        sb.Append("," + tch.ToString());
                    }
                    tchStr += sb.ToString();
                    if (tchStr.Length > 0)
                    {
                        tchStr = tchStr.Remove(0, 1);
                    }
                }
                else if (cell is TDCell)
                {
                    tchStr = (cell as TDCell).FREQ.ToString();
                }
                return tchStr;
            }
        }

        public double Distance
        {
            get { return distance2Testpoint; }
        }

        public int Angle
        {
            get { return angle2Testpoint; }
        }

    }

}
