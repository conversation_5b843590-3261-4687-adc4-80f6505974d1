﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class VoNRStatDelayInfoForm_Divided
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.treeListViewResult = new BrightIdeasSoftware.TreeListView();
            this.col_sn = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.col_beginTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.col_endTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.col_totalTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.col_invite_100 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.col_183_prack = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.col_prack_200 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.col_200_update = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.col_update_200 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.col_200_180 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.col_fileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.col_100_183 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            ((System.ComponentModel.ISupportInitialize)(this.treeListViewResult)).BeginInit();
            this.SuspendLayout();
            // 
            // treeListViewResult
            // 
            this.treeListViewResult.AllColumns.Add(this.col_sn);
            this.treeListViewResult.AllColumns.Add(this.col_beginTime);
            this.treeListViewResult.AllColumns.Add(this.col_endTime);
            this.treeListViewResult.AllColumns.Add(this.col_totalTime);
            this.treeListViewResult.AllColumns.Add(this.col_invite_100);
            this.treeListViewResult.AllColumns.Add(this.col_100_183);
            this.treeListViewResult.AllColumns.Add(this.col_183_prack);
            this.treeListViewResult.AllColumns.Add(this.col_prack_200);
            this.treeListViewResult.AllColumns.Add(this.col_200_update);
            this.treeListViewResult.AllColumns.Add(this.col_update_200);
            this.treeListViewResult.AllColumns.Add(this.col_200_180);
            this.treeListViewResult.AllColumns.Add(this.col_fileName);
            this.treeListViewResult.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.col_sn,
            this.col_beginTime,
            this.col_endTime,
            this.col_totalTime,
            this.col_invite_100,
            this.col_100_183,
            this.col_183_prack,
            this.col_prack_200,
            this.col_200_update,
            this.col_update_200,
            this.col_200_180,
            this.col_fileName});
            this.treeListViewResult.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListViewResult.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListViewResult.Location = new System.Drawing.Point(0, 0);
            this.treeListViewResult.Name = "treeListViewResult";
            this.treeListViewResult.OwnerDraw = true;
            this.treeListViewResult.ShowGroups = false;
            this.treeListViewResult.Size = new System.Drawing.Size(1191, 426);
            this.treeListViewResult.TabIndex = 0;
            this.treeListViewResult.UseCompatibleStateImageBehavior = false;
            this.treeListViewResult.View = System.Windows.Forms.View.Details;
            this.treeListViewResult.VirtualMode = true;
            // 
            // col_sn
            // 
            this.col_sn.HeaderFont = null;
            this.col_sn.Text = "序号";
            this.col_sn.Width = 50;
            // 
            // col_beginTime
            // 
            this.col_beginTime.HeaderFont = null;
            this.col_beginTime.Text = "开始时间";
            this.col_beginTime.Width = 155;
            // 
            // col_endTime
            // 
            this.col_endTime.HeaderFont = null;
            this.col_endTime.Text = "结束时间";
            this.col_endTime.Width = 155;
            // 
            // col_totalTime
            // 
            this.col_totalTime.HeaderFont = null;
            this.col_totalTime.Text = "总时长(s)";
            this.col_totalTime.Width = 90;
            // 
            // col_invite_100
            // 
            this.col_invite_100.HeaderFont = null;
            this.col_invite_100.Text = "INVITE-100(s)";
            this.col_invite_100.Width = 100;
            // 
            // col_183_prack
            // 
            this.col_183_prack.HeaderFont = null;
            this.col_183_prack.Text = "183-PRACK(s)";
            this.col_183_prack.Width = 100;
            // 
            // col_prack_200
            // 
            this.col_prack_200.HeaderFont = null;
            this.col_prack_200.Text = "PRACK-200(s)";
            this.col_prack_200.Width = 100;
            // 
            // col_200_update
            // 
            this.col_200_update.HeaderFont = null;
            this.col_200_update.Text = "200-UPDATE(s)";
            this.col_200_update.Width = 100;
            // 
            // col_update_200
            // 
            this.col_update_200.HeaderFont = null;
            this.col_update_200.Text = "UPDATE-200(s)";
            this.col_update_200.Width = 100;
            // 
            // col_200_180
            // 
            this.col_200_180.HeaderFont = null;
            this.col_200_180.Text = "200-180(s)";
            this.col_200_180.Width = 100;
            // 
            // col_fileName
            // 
            this.col_fileName.HeaderFont = null;
            this.col_fileName.Text = "文件名";
            this.col_fileName.Width = 300;
            // 
            // col_100_183
            // 
            this.col_100_183.HeaderFont = null;
            this.col_100_183.Text = "100_183(s)";
            this.col_100_183.Width = 100;
            // 
            // VoNRStatDelayInfoForm_Divided
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1191, 426);
            this.Controls.Add(this.treeListViewResult);
            this.Name = "VoNRStatDelayInfoForm_Divided";
            this.Text = "分段时延分析统计结果";
            ((System.ComponentModel.ISupportInitialize)(this.treeListViewResult)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView treeListViewResult;
        private BrightIdeasSoftware.OLVColumn col_sn;
        private BrightIdeasSoftware.OLVColumn col_beginTime;
        private BrightIdeasSoftware.OLVColumn col_endTime;
        private BrightIdeasSoftware.OLVColumn col_invite_100;
        private BrightIdeasSoftware.OLVColumn col_183_prack;
        private BrightIdeasSoftware.OLVColumn col_prack_200;
        private BrightIdeasSoftware.OLVColumn col_200_update;
        private BrightIdeasSoftware.OLVColumn col_update_200;
        private BrightIdeasSoftware.OLVColumn col_200_180;
        private BrightIdeasSoftware.OLVColumn col_totalTime;
        private BrightIdeasSoftware.OLVColumn col_fileName;
        private BrightIdeasSoftware.OLVColumn col_100_183;
    }
}