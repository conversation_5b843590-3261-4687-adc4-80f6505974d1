using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Chris.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class TDPoorBlerCellSetForm : Form
    {
        public TDPoorBlerCellSetForm()
        {
            InitializeComponent();
            //rangeSettingBLER.NumericUpDownMin.Increment = 0.1M;
            //rangeSettingBLER.NumericUpDownMin.DecimalPlaces = 1;
            //rangeSettingBLER.NumericUpDownMax.Increment = 0.1M;
            //rangeSettingBLER.NumericUpDownMax.DecimalPlaces = 1;

            rangeSettingBLER.RangeAll = new Range(0, true, 100, true);
            rangeSettingPccpchRscp.RangeAll = new Range(-140, true, -10, true);
            rangeSettingPCCPCHC2I.RangeAll = new Range(-30, true, 30, true);
            rangeSettingDPCHC2I.RangeAll = new Range(-30, true, 30, true);
        }
        public Range BLERRange
        {
            get { return rangeSettingBLER.Range; }
        }
        public Range PccpchRscpRange
        {
            get { return rangeSettingPccpchRscp.Range; }
        }        
        public Range PCCPCHC2IRange
        {
            get { return rangeSettingPCCPCHC2I.Range; }
        }
        public Range DPCHC2IRange
        {
            get { return rangeSettingDPCHC2I.Range; }
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void TDPoorBlerCellSetForm_Load(object sender, EventArgs e)
        {
            //
        }
    }
}