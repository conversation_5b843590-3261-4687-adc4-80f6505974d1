﻿namespace MasterCom.RAMS.Func
{
    partial class CellLengthSettingDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(CellLengthSettingDlg));
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.checkEditAll = new DevExpress.XtraEditors.CheckEdit();
            this.spinEditGSMCell = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditOtherCell = new DevExpress.XtraEditors.SpinEdit();
            this.labelGSM = new DevExpress.XtraEditors.LabelControl();
            this.labelTD = new DevExpress.XtraEditors.LabelControl();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.labelW = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditAll.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditGSMCell.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditOtherCell.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            this.SuspendLayout();
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(40, 107);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 0;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(121, 107);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 0;
            this.btnCancel.Text = "取消";
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // checkEditAll
            // 
            this.checkEditAll.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.checkEditAll.EditValue = true;
            this.checkEditAll.Location = new System.Drawing.Point(154, 3);
            this.checkEditAll.Name = "checkEditAll";
            this.checkEditAll.Properties.Caption = "统一比例";
            this.checkEditAll.Size = new System.Drawing.Size(78, 19);
            this.checkEditAll.TabIndex = 1;
            // 
            // spinEditGSMCell
            // 
            this.spinEditGSMCell.EditValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.spinEditGSMCell.Location = new System.Drawing.Point(72, 29);
            this.spinEditGSMCell.Name = "spinEditGSMCell";
            this.spinEditGSMCell.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditGSMCell.Properties.Increment = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.spinEditGSMCell.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.spinEditGSMCell.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.spinEditGSMCell.Size = new System.Drawing.Size(100, 21);
            this.spinEditGSMCell.TabIndex = 1;
            this.spinEditGSMCell.EditValueChanged += new System.EventHandler(this.spinEditGSMCell_EditValueChanged);
            // 
            // spinEditOtherCell
            // 
            this.spinEditOtherCell.EditValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.spinEditOtherCell.Location = new System.Drawing.Point(72, 62);
            this.spinEditOtherCell.Name = "spinEditOtherCell";
            this.spinEditOtherCell.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditOtherCell.Properties.Increment = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.spinEditOtherCell.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.spinEditOtherCell.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.spinEditOtherCell.Size = new System.Drawing.Size(100, 21);
            this.spinEditOtherCell.TabIndex = 2;
            this.spinEditOtherCell.EditValueChanged += new System.EventHandler(this.spinEditOtherCell_EditValueChanged);
            // 
            // labelGSM
            // 
            this.labelGSM.Location = new System.Drawing.Point(38, 32);
            this.labelGSM.Name = "labelGSM";
            this.labelGSM.Size = new System.Drawing.Size(28, 14);
            this.labelGSM.TabIndex = 3;
            this.labelGSM.Text = "GSM:";
            // 
            // labelTD
            // 
            this.labelTD.Location = new System.Drawing.Point(47, 65);
            this.labelTD.Name = "labelTD";
            this.labelTD.Size = new System.Drawing.Size(19, 14);
            this.labelTD.TabIndex = 3;
            this.labelTD.Text = "TD:";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.checkEditAll);
            this.groupControl1.Controls.Add(this.spinEditOtherCell);
            this.groupControl1.Controls.Add(this.labelW);
            this.groupControl1.Controls.Add(this.labelTD);
            this.groupControl1.Controls.Add(this.labelGSM);
            this.groupControl1.Controls.Add(this.spinEditGSMCell);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(236, 92);
            this.groupControl1.TabIndex = 4;
            this.groupControl1.Text = "比例";
            // 
            // labelW
            // 
            this.labelW.Location = new System.Drawing.Point(18, 65);
            this.labelW.Name = "labelW";
            this.labelW.Size = new System.Drawing.Size(48, 14);
            this.labelW.TabIndex = 3;
            this.labelW.Text = "WCDMA:";
            this.labelW.Visible = false;
            // 
            // CellLengthSettingDlg
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("SimSun", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("CellLengthSettingDlg.Appearance.Image")));
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(236, 140);
            this.Controls.Add(this.groupControl1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "CellLengthSettingDlg";
            this.ShowIcon = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "小区天线长度显示比例设置";
            ((System.ComponentModel.ISupportInitialize)(this.checkEditAll.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditGSMCell.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditOtherCell.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.CheckEdit checkEditAll;
        private DevExpress.XtraEditors.SpinEdit spinEditGSMCell;
        private DevExpress.XtraEditors.SpinEdit spinEditOtherCell;
        private DevExpress.XtraEditors.LabelControl labelGSM;
        private DevExpress.XtraEditors.LabelControl labelTD;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.LabelControl labelW;
    }
}