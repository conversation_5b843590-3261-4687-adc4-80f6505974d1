﻿using System;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTTDCellReselectAnaSetForm : BaseDialog
    {
        public ZTTDCellReselectAnaSetForm()
        {
            InitializeComponent();
        }

        public ZTTDCellReselectAnaCondition GetCondition()
        {
            ZTTDCellReselectAnaCondition condition = new ZTTDCellReselectAnaCondition();
            condition.BeforeSecond = (int)numBeforeSecond.Value;
            condition.AfterSecond = (int)numAfterSecond.Value;
            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void ZTTDCellReselectAnaSetForm_Load(object sender, EventArgs e)
        {
            //
        }
    }
}
