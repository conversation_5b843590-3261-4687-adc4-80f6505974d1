﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace  MasterCom.RAMS.ZTFunc.VoLTEBlockCallCause
{
    public partial class CauseSettingForm : BaseDialog
    {
        public CauseSettingForm()
            : base()
        {
            InitializeComponent();
        }

        public BlockCallCondition Condition
        {
            get
            {
                BlockCallCondition cond = new BlockCallCondition();
                cond.HoNum = (int)this.numHoNum.Value;
                cond.HoSec = (int)this.numHoSec.Value;
                cond.PoorSinr = (int)this.numSinr.Value;
                cond.PoorSinrSec = (int)this.numSinrSec.Value;
                cond.WeakRsrp = (float)this.numRsrp.Value;
                cond.WeakRsrpSec = (int)this.numRsrpSec.Value;
                cond.MultiSec = (int)this.numMultiSec.Value;
                cond.MultiBand = (int)this.numMultiBand.Value;
                cond.MultiPer = (float)this.numMultiPer.Value;
                cond.MultiValue = (int)this.numMultiValue.Value;
                return cond;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                this.numHoNum.Value = value.HoNum;
                this.numHoSec.Value = value.HoSec;
                this.numRsrp.Value = (decimal)value.WeakRsrp;
                this.numRsrpSec.Value = value.WeakRsrpSec;
                this.numSinr.Value = value.PoorSinr;
                this.numSinrSec.Value = value.PoorSinrSec;

                this.numMultiSec.Value = value.MultiSec;
                this.numMultiBand.Value = value.MultiBand;
                this.numMultiPer.Value = (decimal)value.MultiPer;
                this.numMultiValue.Value = value.MultiValue;
            }

        }

    }
}
