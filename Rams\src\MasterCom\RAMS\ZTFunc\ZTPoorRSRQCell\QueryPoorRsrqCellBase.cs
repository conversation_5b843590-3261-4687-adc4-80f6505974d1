﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTPoorRSRQCell;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public abstract class QueryPoorRsrqCellBase : DIYAnalyseByFileBackgroundBase
    {
        protected abstract string themeName { get; }
        protected abstract string rsrpName { get; }
        protected abstract string sinrName { get; }
        protected abstract string rsrqName { get; }

        protected PoorRsrqCellCondition poorCondition = null;
        protected Dictionary<string, PoorRsrqCell> poorCellDic;

        protected QueryPoorRsrqCellBase()
         : base(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
        }

        public override string Name
        {
            get { return "低RSRQ小区"; }
        }

        protected override void fireShowForm()
        {
            if (poorCellDic.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }
            ResultForm frm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ResultForm)) as ResultForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new ResultForm();
                frm.Owner = MainModel.MainForm;
            }

            frm.FillData(new List<PoorRsrqCell>(poorCellDic.Values));
            frm.Visible = true;
            frm.BringToFront();
            poorCellDic = null;
        }

        protected virtual void resetTheme()
        {
            MainModel.FireSetDefaultMapSerialTheme(themeName);
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            SettingForm dlg = new SettingForm(poorCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                poorCondition = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            poorCellDic = new Dictionary<string, PoorRsrqCell>();
        }

        protected override void doStatWithQuery()
        {
            if (MainModel.DTDataManager.FileDataManagers.Count == 0)
            {
                return;
            }
            List<TestPoint> testPointList = MainModel.DTDataManager.FileDataManagers[0].TestPoints;
            for (int i = 0; i < testPointList.Count; i++)
            {
                TestPoint testPoint = testPointList[i];
                if (!isValidTestPoint(testPoint))
                {//区域外的采样点
                    continue;
                }
                addPoorCell(testPoint);
            }
        }

        protected virtual void addPoorCell(TestPoint testPoint)
        {
            LTECell cell;
            if (testPoint is ScanTestPoint_NBIOT)
            {
                cell = testPoint.GetCell_LTEScan(0);
            }
            else
            {
                cell = testPoint.GetMainLTECell_TdOrFdd();
            }
            if (cell != null)
            {
                float? rsrq = getRsrq(testPoint);
                if (rsrq != null)
                {
                    bool poorPt = poorCondition.IsPoorRsrq((float)rsrq);
                    PoorRsrqCell poorCell;
                    if (!poorCellDic.TryGetValue(cell.Token, out poorCell))
                    {
                        poorCell = new PoorRsrqCell(cell);
                        poorCellDic[cell.Token] = poorCell;
                    }
                    poorCell.AddTestPoint(testPoint, (float)rsrq, poorPt);
                    poorCell.AddOtherTPInfo(testPoint);
                }
            }
        }

        protected virtual float? getRsrq(TestPoint tp)
        {
            return (float?)tp[rsrqName];
        }
    }
}
