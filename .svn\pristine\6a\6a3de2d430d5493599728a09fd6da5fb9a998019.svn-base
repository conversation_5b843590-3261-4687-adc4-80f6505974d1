﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.ZTAtuLogKPIGroup
{
    public partial class TemplateSelector : BaseDialog
    {
        public TemplateSelector(GroupTemplate selRpt)
        {
            InitializeComponent();
            fillCbx(selRpt);
        }

        private void fillCbx(GroupTemplate selRpt)
        {
            cbxTemplates.Items.Clear();
            foreach (GroupTemplate rpt in TemplateManager.Instance.Templates)
            {
                cbxTemplates.Items.Add(rpt);
            }

            if (cbxTemplates.Items.Count > 0)
            {
                if (selRpt != null)
                {
                    cbxTemplates.SelectedItem = selRpt;
                }
                else
                {
                    cbxTemplates.SelectedIndex = 0;
                }
            }
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            TemplateSettingDlg form = new TemplateSettingDlg(this.cbxTemplates.SelectedItem as GroupTemplate);
            form.ShowDialog();
            fillCbx(form.SelectedTemplate);
        }

        public GroupTemplate Template
        {
            get;
            private set;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            Template = this.cbxTemplates.SelectedItem as GroupTemplate;
            if (Template==null)
            {
                MessageBox.Show("请选择模板！");
                return;
            }
            DialogResult = DialogResult.OK;
        }

        private void linkCfg_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            if (!System.IO.File.Exists(TemplateManager.CfgFileName))
            {
                MessageBox.Show("模板配置不存在，请先点击【定制】按钮进行模板定制");
                return; 
            }
            System.Diagnostics.Process.Start("Explorer", "/select," 
                + System.IO.Path.GetFullPath(TemplateManager.CfgFileName));
        }
    }
}
