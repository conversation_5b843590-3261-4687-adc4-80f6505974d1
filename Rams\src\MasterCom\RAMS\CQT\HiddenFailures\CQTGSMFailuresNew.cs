﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTLastWeakRoadAna;
using MasterCom.Util;
using Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTGSMFailuresNew : DevExpress.XtraEditors.XtraForm
    {
        public CQTGSMFailuresNew(MainModel Mainmodel, QueryCondition Condition)
        {
            InitializeComponent();

            mainmodel = Mainmodel;
            conditioncqt = Condition;
        }
        MainModel mainmodel = null;
        QueryCondition conditioncqt = null;

        List<CQTQualityItem> touresult = new List<CQTQualityItem>();
        List<CQTEventItem> weiresult = new List<CQTEventItem>();

        public void setData(List<CQTQualityItem> result)
        {
            touresult.Clear();
            touresult = result;
            this.gridControl1.DataSource = result;


        }

        //点击查看详情
        private void gridView1_Click(object sender, EventArgs e)
        {
            int[] rows = gridView1.GetSelectedRows();
            if (rows.Length == 0)
                return;
            object o = gridView1.GetRow(rows[0]);
            CQTQualityItem evaluate = o as CQTQualityItem;

            if (evaluate != null)
            {
                foreach (CQTEventItem item in evaluate.cqtEventList)
                {
                    Cell cell = null;
                    cell = mainmodel.CellManager.GetCurrentCell(item.Ilac, item.Ici);
                    if (cell != null)
                        item.Strcellname = cell.Name;
                }

                this.gridControl2.DataSource = evaluate.cqtEventList;
            }
        }

        private void EXCELToolStripMenuItem_Click(object sender, EventArgs e)
        {
            weiresult.Clear();
            for (int i = 0; i < touresult.Count; i++)
            {
                object o = gridView1.GetRow(i);
                CQTQualityItem evaluate = o as CQTQualityItem;

                if (evaluate != null)
                {
                    foreach (CQTEventItem item in evaluate.cqtEventList)
                    {
                        Cell cell = null;
                        cell = mainmodel.CellManager.GetCurrentCell(item.Ilac, item.Ici);
                        if (cell != null)
                            item.Strcellname = cell.Name;
                    }
                    weiresult.AddRange(evaluate.cqtEventList);
                }
            }
            ExportExcel();
        }

        DateTime dtime;

        private void DIYReplayFile(ReplaySampleItem replaysample)
        {
            DTData dtData = null;
            QueryCondition condition = new QueryCondition();
            FileInfo fileInfo = new FileInfo();
            condition.DistrictID = conditioncqt.DistrictID;
            fileInfo.Name = replaysample.StrFileName;
            fileInfo.ProjectID = replaysample.IProjectType;
            fileInfo.ID = replaysample.IFileid;
            fileInfo.LogTable = replaysample.StrLogFileName;
            fileInfo.ServiceType = replaysample.IServicetType;
            fileInfo.SampleTbName = replaysample.StrSampleName;
            
            dtData = replaysample.ConvertToTestPoint();
            
            condition.FileInfos.Add(fileInfo);
            if (dtData != null)
            {
                FileReplayer.Replay(dtData, true);
            }
        }

        private void ExportExcel()
        {
            Microsoft.Office.Interop.Excel.Application app = null;
            try
            {
                app = new Microsoft.Office.Interop.Excel.Application();
                app.Visible = false;

                app.UserControl = false;
                Workbooks workbooks = app.Workbooks;
                _Workbook workbook = workbooks.Add(XlWBATemplate.xlWBATWorksheet);
                Sheets sheets = workbook.Worksheets;

                _Worksheet worksheet = (_Worksheet)sheets.get_Item(1);
                if (worksheet == null)
                {
                    throw (new Exception("ERROR: worksheet == null"));
                }
                worksheet.Name = "GSM隐性故障表";
                //==== 列标题
                int idx = 1;
                makeTitle(worksheet, 1, idx++, "CQT地点名称", 12, false);
                makeTitle(worksheet, 1, idx++, "RxqualSub0-4级占比", 18, false);
                makeTitle(worksheet, 1, idx++, "感知质差数目", 8, false);
                makeTitle(worksheet, 1, idx++, "持续质差数目", 12, false);
                makeTitle(worksheet, 1, idx++, "事件类型", 12, false);
                makeTitle(worksheet, 1, idx++, "所属文件", 10, false);
                makeTitle(worksheet, 1, idx++, "事件时间", 18, false);
                makeTitle(worksheet, 1, idx++, "小区名称", 12, false);
                makeTitle(worksheet, 1, idx++, "LAC", 7, false);
                makeTitle(worksheet, 1, idx, "CI", 7, false);

                int rowAt = 2;
                foreach (CQTQualityItem ts in touresult)
                {

                    for (int i = 0; i < ts.cqtEventList.Count; i++)
                    {
                        int xx = 1;
                        makeItemRow(worksheet, rowAt, xx++, ts.Strcqtname);
                        makeItemRow(worksheet, rowAt, xx++, ts.StrQual0_4Rate);
                        makeItemRow(worksheet, rowAt, xx++, ts.IWeakQualNum.ToString());
                        makeItemRow(worksheet, rowAt, xx, ts.ILastWeakQualNum.ToString());
                        rowAt++;
                    }

                }
                rowAt = 2;
                foreach (CQTEventItem ws in weiresult)
                {
                    int xx = 5;
                    makeItemRow(worksheet, rowAt, xx++, ws.Streventtype);
                    makeItemRow(worksheet, rowAt, xx++, ws.Strfilename);
                    makeItemRow(worksheet, rowAt, xx++, ws.Dtime.ToString("yyyy/MM/dd HH:mm:ss"));
                    makeItemRow(worksheet, rowAt, xx++, ws.Strcellname);
                    makeItemRow(worksheet, rowAt, xx++, ws.Ilac.ToString());
                    makeItemRow(worksheet, rowAt, xx, ws.Ici.ToString());
                    rowAt++;
                }

                app.Visible = true;
                app.UserControl = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show(this, "导出 Excel 出错：" + ex.Message);
            }
            finally
            {
                if (app != null)
                {
                    app.Quit();
                }
            }

        }

        public void makeTitle(_Worksheet worksheet, int row, int col, string title, int width, bool wraptext)
        {
            Range range = worksheet.Cells[row, col] as Range;
            range.Value2 = title;
            range.ColumnWidth = width;
            range.WrapText = wraptext;
        }

        public void makeItemRow(_Worksheet worksheet, int row, int column, string str)
        {
            Range range = worksheet.Cells[row, column] as Range;
            range.Value2 = str;
        }

        private void ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            int[] row = gridView2.GetSelectedRows();
            if (row.Length == 0)
                return;
            object ev = gridView2.GetRow(row[0]);
            CQTEventItem eve = ev as CQTEventItem;
            dtime = eve.Dtime;

            if (eve.Ifileid != 0)
            {
                QueryItem queryitem = new QueryItem();
                queryitem.IFileid = eve.Ifileid;
                queryitem.Istime = Convert.ToInt32(eve.Itime);
                queryitem.Ietime = Convert.ToInt32(eve.Itime);

                DiySqlQueryReplaySampleInfoCQT diyreplay = new DiySqlQueryReplaySampleInfoCQT(mainmodel);
                DiySqlQueryReplaySampleInfoCQT.QItem = queryitem;
                DiySqlQueryReplaySampleInfoCQT.mergeSql();
                diyreplay.Query();
                ReplaySampleItem replaysample = diyreplay.RsItem;


                mainmodel.MainForm.NeedChangeWorkSpace(false);
                DIYReplayFile(replaysample);

            }
        }
    }
}