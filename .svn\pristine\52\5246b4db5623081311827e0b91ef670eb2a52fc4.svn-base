﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.Data.Sql;
using System.Data.SqlClient;
using MasterCom.Util;


namespace MasterCom.RAMS.ZTFunc
{
    public class AlarmRecordManager
    {
        public static AlarmRecordManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new AlarmRecordManager();
                }
                return instance;
            }
        }

        public string SqlConnectionString
        {
            get;
            private set;
        }

        public void FireLoadAlarmRecords(int cityID, bool isForce)
        {
            if (cityID == curCityID && !isForce)
            {
                return;
            }
            curCityID = cityID;

            cellRecordDic.Clear();
            btsRecordDic.Clear();
            SqlConnection sqlConn = null;
            SqlCommand cmd = null;
            SqlDataReader reader = null;
            try
            {
                sqlConn = new SqlConnection(this.SqlConnectionString);
                sqlConn.Open();

                string sql = "SELECT [地市ID],[发生时间],[结束时间],[网络类型],[基站名称],[小区名称],[告警ID],[告警名称],[告警级别] FROM [tb_association_alarm] where [地市ID] = " + curCityID;
                cmd = new SqlCommand(sql, sqlConn);
                reader = cmd.ExecuteReader();
                while (reader.Read())
                {
                    AlarmRecord record = new AlarmRecord();
                    record.FillData(reader);

                    addRecord(record);
                }
                this.logger.Info(string.Format("Load {0} Cell Alarm Records and {1} Site Alarm Records.", cellRecordDic.Count, btsRecordDic.Count));
            }
            catch (System.Exception ex)
            {
                this.logger.Error("Load Alarm Record Error", ex);
            }
            finally
            {
                if (sqlConn != null) sqlConn.Dispose();
                if (cmd != null) cmd.Dispose();
                else if (reader != null) cmd.Dispose();
            }
        }

        private void addRecord(AlarmRecord record)
        {
            if (record.IsCellAlarm)
            {
                List<AlarmRecord> recordList = null;
                if (!cellRecordDic.TryGetValue(record.CellName, out recordList))
                {
                    recordList = new List<AlarmRecord>();
                    cellRecordDic.Add(record.CellName, recordList);
                }
                cellRecordDic[record.CellName].Add(record);
            }
            else
            {
                List<AlarmRecord> recordList = null;
                if (!btsRecordDic.TryGetValue(record.BtsName, out recordList))
                {
                    recordList = new List<AlarmRecord>();
                    btsRecordDic.Add(record.BtsName, recordList);
                }
                btsRecordDic[record.BtsName].Add(record);
            }
        }

        public void FireLoadAlarmRecords(int cityID)
        {
            FireLoadAlarmRecords(cityID, false);
        }

        public List<AlarmRecord> GetAlarmRecords(string name, DateTime time)
        {
            List<AlarmRecord> retList = new List<AlarmRecord>();

            List<AlarmRecord> cellRecords = null;
            if (cellRecordDic.TryGetValue(name, out cellRecords))
            {
                foreach (AlarmRecord r in cellRecords)
                {
                    if (r.Period.Contains(time))
                    {
                        retList.Add(r);
                    }
                }
            }

            List<AlarmRecord> btsRecords = null;
            if (btsRecordDic.TryGetValue(name, out btsRecords))
            {
                foreach (AlarmRecord r in btsRecords)
                {
                    if (r.Period.Contains(time))
                    {
                        retList.Add(r);
                    }
                }
            }

            return retList;
        }

        public List<AlarmRecord> GetAllRecords(string name)
        {
            List<AlarmRecord> retList = new List<AlarmRecord>();
            if (cellRecordDic.ContainsKey(name))
            {
                retList.AddRange(cellRecordDic[name]);
            }
            if (btsRecordDic.ContainsKey(name))
            {
                retList.AddRange(btsRecordDic[name]);
            }
            return retList;
        }

        private AlarmRecordManager()
        {
            AlarmDbSetting dbSetting = new AlarmDbSetting();
            this.SqlConnectionString = dbSetting.ConnectionString;
        }

        private int curCityID = -1;

        private readonly Dictionary<string, List<AlarmRecord>> cellRecordDic = new Dictionary<string, List<AlarmRecord>>();

        private readonly Dictionary<string, List<AlarmRecord>> btsRecordDic = new Dictionary<string, List<AlarmRecord>>();

        private readonly log4net.ILog logger = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        private static AlarmRecordManager instance;
    }

    public class AlarmRecord
    {
        public bool IsCellAlarm
        {
            get { return CellName != null; }
        }

        public int CityID
        {
            get;
            set;
        }

        public int NetType
        {
            get;
            set;
        }

        public string CellName
        {
            get;
            set;
        }

        public string BtsName
        {
            get;
            set;
        }

        public TimePeriod Period
        {
            get;
            set;
        }

        public int AlarmID
        {
            get;
            set;
        }

        public string AlarmName
        {
            get;
            set;
        }

        public string AlarmLevel
        {
            get;
            set;
        }

        public void FillData(SqlDataReader reader)
        {
            CityID = (int)reader["地市ID"];
            NetType = (int)reader["网络类型"];

            DateTime sTime = (DateTime)reader["发生时间"];
            DateTime eTime = (DateTime)reader["结束时间"];
            Period = new TimePeriod(sTime, eTime);

            object cellName = reader["小区名称"];
            CellName = Convert.IsDBNull(cellName) ? null : cellName.ToString();

            object btsName = reader["基站名称"];
            BtsName = Convert.IsDBNull(btsName) ? null : btsName.ToString();

            object alarmId = reader["告警ID"];
            if (!Convert.IsDBNull(alarmId))
            {
                AlarmID = (int)alarmId;
            }

            object alarmName = reader["告警名称"];
            if (!Convert.IsDBNull(alarmName))
            {
                AlarmName = alarmName.ToString();
            }

            object alarmLevel = reader["告警级别"];
            if (!Convert.IsDBNull(alarmLevel))
            {
                AlarmLevel = alarmLevel.ToString();
            }
        }

        public string GetDesc()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendLine(IsCellAlarm ? "小区名称: " + CellName : "基站名称: " + BtsName);
            sb.AppendLine("告警ID: " + AlarmID);
            sb.AppendLine("告警名称: " + AlarmName);
            sb.AppendLine("告警级别: " + AlarmLevel);
            sb.AppendLine("开始时间: " + Period.BeginTime.ToString("yyyy-MM-dd HH:mm:ss"));
            sb.AppendLine("结束时间: " + Period.EndTime.ToString("yyyy-MM-dd HH:mm:ss"));
            return sb.ToString();
        }
    }

    public class AlarmDbSetting
    {
        public string DataSource
        {
            get;
            set;
        }

        public string InitialCatalog
        {
            get;
            set;
        }

        public string Username
        {
            get;
            set;
        }

        public string Password
        {
            get;
            set;
        }

        public string ConnectionString
        {
            get;
            private set;
        }

        private string getPwd()
        {
            string def = "dtauser";
            return def;
        }

        public AlarmDbSetting()
        {
            DataSource = "127.0.0.1";
            InitialCatalog = "RAMSSERVER";
            Username = "dtauser";
            Password = getPwd();

            Load();

            SqlConnectionStringBuilder sb = new SqlConnectionStringBuilder();
            sb.DataSource = DataSource;
            sb.InitialCatalog = InitialCatalog;
            sb.UserID = Username;
            sb.Password = Password;
            this.ConnectionString = sb.ConnectionString;
        }

        protected void Load()
        {
            if (!System.IO.File.Exists(xmlFile))
            {
                return;
            }

            XmlConfigFile cfgFile = new XmlConfigFile(xmlFile);
            Dictionary<string, object> param = cfgFile.GetItemValue("Config", "Connection") as Dictionary<string, object>;
            if (param.ContainsKey("DataSource") && param["DataSource"] != null)
            {
                this.DataSource = param["DataSource"].ToString();
            }
            if (param.ContainsKey("InitialCatalog") && param["InitialCatalog"] != null)
            {
                this.InitialCatalog = param["InitialCatalog"].ToString();
            }
            if (param.ContainsKey("Username") && param["Username"] != null)
            {
                this.Username= param["Username"].ToString();
            }
            if (param.ContainsKey("Password") && param["Password"] != null)
            {
                this.Password = param["Password"].ToString();
            }
        }

        protected string xmlFile = "config/AssociationAlarm.xml";
    }
}
