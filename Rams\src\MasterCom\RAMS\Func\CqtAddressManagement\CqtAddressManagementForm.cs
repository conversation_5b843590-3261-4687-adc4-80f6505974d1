﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System.Data.OleDb;
using DevExpress.XtraEditors;
using NPOI.OpenXmlFormats.Dml;

namespace MasterCom.RAMS.Func.CqtAddressManagement
{
    /// <summary>
    /// Written by WuJunHong 2012.9.21
    /// </summary>
    public partial class CqtAddressManagementForm : XtraForm
    {
        MainModel mModel;
        string searchText;
        int areaType;
        List<CQTAddressItem> cqtAddressItemsInNewData = null;   //从导入Excel表中的地点
        List<CQTAddressItem> cqtAddressItemsNeedToAdd = null;    //比较现有地点，需要新增导入的地点
        List<CQTAddressItem> cqtAddressItemsNeedToUpdate = null;   //比较现有地点，需要更新内容的地点
        public CqtAddressManagementForm(MainModel mainModel, string searchText, int areaType)
        {
            InitializeComponent();
            groupControlBatImp.Dock = DockStyle.Top;
            groupControlCQTAddrs.Dock = DockStyle.Fill;
            nudRatio.Maximum = nudOpImgRatio.Maximum = decimal.MaxValue;
         listView.ListViewItemSorter = new ListViewSorter(listView);

            mModel = mainModel;
            this.searchText = searchText;
            this.areaType = areaType;

            queryCQTAddrInfo();

            ShowAllCqtPoints();
        }

        private void queryCQTAddrInfo()
        {
            QueryCQTAddrInfo queryInfo = new QueryCQTAddrInfo(mModel, searchText, areaType);
            queryInfo.Query();
        }

        private void ShowAllCqtPoints()
        {
            this.FillData(mModel.CurCQTPointList);
        }

        internal void FillData(List<CQTAddressItem> list)
        {
            listView.Items.Clear();
            if (list == null)
            {
                return;
            }
            List<CQTTypeInfoItem> typeList = mModel.CQTTypeInfoList;
            Dictionary<int, string> typeDescDic = new Dictionary<int, string>();
            foreach (CQTTypeInfoItem type in typeList)
            {
                typeDescDic[type.areaTypeId] = type.areaTypeName;
            }
            int icount = 1;
            foreach (CQTAddressItem addr in list)
            {
                ListViewItem listViewItem = new System.Windows.Forms.ListViewItem();
                listViewItem.Tag = addr;
                listViewItem.Text = icount.ToString();
                listViewItem.SubItems[0].Tag = icount;
                listViewItem.SubItems.Add(addr.name);
                listViewItem.SubItems.Add(addr.desc);
                string typedes = "";
                typeDescDic.TryGetValue(addr.cqtTypeId, out typedes);
                listViewItem.SubItems.Add(typedes);
                listViewItem.SubItems.Add(string.Format("{0:F7}", addr.jd));
                listViewItem.SubItems.Add(string.Format("{0:F7}", addr.wd));
                listViewItem.SubItems.Add(addr.ImageCount.ToString());
                icount++;
                listView.Items.Add(listViewItem);
            }
            bool sel = false;
            if (curSelAddrItem!=null)
            {
                foreach (ListViewItem item in listView.Items)
                {
                    if (item.Tag.Equals(curSelAddrItem))
                    {
                        item.Selected = sel=true;
                        break;
                    }
                }
            }
            if (!sel)
            {
                listView_SelectedIndexChanged(null, null);
            }
        }

        internal void AddData(List<CQTAddressItem> addList)
        {
            if (addList == null)
            {
                return;
            }
            List<CQTTypeInfoItem> typeList = mModel.CQTTypeInfoList;
            Dictionary<int, string> typeDescDic = new Dictionary<int, string>();
            foreach (CQTTypeInfoItem type in typeList)
            {
                typeDescDic[type.areaTypeId] = type.areaTypeName;
            }
            int icount = this.listView.Items.Count + 1;
            foreach (CQTAddressItem addr in addList)
            {
                ListViewItem listViewItem = new System.Windows.Forms.ListViewItem();
                listViewItem.Tag = addr;
                listViewItem.Text = icount.ToString();
                listViewItem.SubItems[0].Tag = icount;
                listViewItem.SubItems.Add(addr.name);
                listViewItem.SubItems.Add(addr.desc);
                string typedes = "";
                typeDescDic.TryGetValue(addr.cqtTypeId, out typedes);
                listViewItem.SubItems.Add(typedes);
                listViewItem.SubItems.Add(string.Format("{0:F7}", addr.jd));
                listViewItem.SubItems.Add(string.Format("{0:F7}", addr.wd));
                icount++;
                listView.Items.Add(listViewItem);

                highlightSnList.Add(icount); //高亮新改动的行
            }
        }

        internal void updateData(List<CQTAddressItem> updateList)
        {
            if (updateList == null)
            {
                return;
            }
            List<CQTTypeInfoItem> typeList = mModel.CQTTypeInfoList;
            Dictionary<int, string> typeDescDic = new Dictionary<int, string>();
            foreach (CQTTypeInfoItem type in typeList)
            {
                typeDescDic[type.areaTypeId] = type.areaTypeName;
            }
            foreach (CQTAddressItem addr in updateList)
            {
                foreach (ListViewItem item in this.listView.Items)
                {
                    if (addr.name == ((CQTAddressItem)item.Tag).name)
                    {
                        item.Tag = addr;
                        item.SubItems[2].Text = addr.desc;
                        string typedes = "";
                        typeDescDic.TryGetValue(addr.cqtTypeId, out typedes);
                        item.SubItems[3].Text = typedes;
                        item.SubItems[4].Text = string.Format("{0:F7}", addr.jd);
                        item.SubItems[5].Text = string.Format("{0:F7}", addr.wd);

                        highlightSnList.Add((int)item.SubItems[0].Tag);
                    }
                }
            }
        }

        List<int> highlightSnList = null;
        bool isFirstTimeImport = true;
        private void btnImport_Click(object sender, EventArgs e)
        {
            if (!isFirstTimeImport)
            {
                reset();
                isFirstTimeImport = false;
            }
            isFirstTimeImport = false;
            cqtAddressItemsInNewData = new List<CQTAddressItem>();
            cqtAddressItemsNeedToAdd = new List<CQTAddressItem>();
            cqtAddressItemsNeedToUpdate = new List<CQTAddressItem>();

            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = "Excel2003文件(*.xls)|*.xls|Excel2007文件(*.xlsx)|*.xlsx";
            dlg.RestoreDirectory = true;
            if (dlg.ShowDialog(this) == DialogResult.OK)
            {
                string filename = dlg.FileName;
                List<CQTAddressItem> cqtAddressItems = GetCqtAddressItems(filename);
                if (cqtAddressItems.Count == 0)
                {
                    showResult("在Excel表内检测到非法的数据，请检查数据！");
                    return;
                }
                cqtAddressItemsInNewData = cqtAddressItems;

                CalculateDataNeedAddAndUpdate(); //计算新增的地点和需要更新内容的地点

                showResult("需要新增CQT测试点" + cqtAddressItemsNeedToAdd.Count + "个 \r\n"
                    + "需要更新CQT测试点" + cqtAddressItemsNeedToUpdate.Count + "个 \r\n");

                highlightSnList = new List<int>();
                AddData(cqtAddressItemsNeedToAdd);
                updateData(cqtAddressItemsNeedToUpdate);
                highlightChangedItems(true);
                if (cqtAddressItemsNeedToAdd.Count > 0 || cqtAddressItemsNeedToUpdate.Count > 0)
                {
                    btnImportOK.Enabled = true;
                    MessageBox.Show("如测试点数据无误，请点击按钮 ”确认入库“进行数据入库。", "检测对比发现新CQT测试点", MessageBoxButtons.OK, MessageBoxIcon.Question);
                }
                else
                {
                    MessageBox.Show("检测没有发现新CQT测试点。");
                }
            }
        }

        private List<CQTAddressItem> GetCqtAddressItems(string filename)
        {
            try
            {
                int count = 0;
                string strConn = null;
                int index = filename.LastIndexOf(".");
                int lenFilename = filename.Length;
                if (filename.Substring(index + 1, lenFilename - index - 1) == "xls") //Excel2003版本引擎
                {
                    strConn = "Provider=Microsoft.Jet.OLEDB.4.0;" + "Data Source=" + filename + ";" + "Extended Properties=Excel 8.0;";
                }
                else //Excel2007版本引擎
                {
                    strConn = "Provider=Microsoft.ACE.OLEDB.12.0;" + "Data Source=" + filename + ";" + "Extended Properties=Excel 8.0;";
                }
                OleDbConnection con = new OleDbConnection(strConn);
                string importWorkSheetName = GetFirstSheetNameFromExcelFileName(filename);
                string cmdText = "select * from [" + importWorkSheetName.Replace('.', '#') + "$]";
                OleDbCommand command = new OleDbCommand(cmdText, con);
                con.Open();
                OleDbDataReader reader = command.ExecuteReader();

                List<CQTTypeInfoItem> typeList = mModel.CQTTypeInfoList;
                Dictionary<string, int> descTypeDic = new Dictionary<string, int>();
                foreach (CQTTypeInfoItem type in typeList)
                {
                    if (!descTypeDic.ContainsKey(type.areaTypeName))
                    {
                        descTypeDic.Add(type.areaTypeName, type.areaTypeId);
                    }
                }

                StringBuilder problemstr = new StringBuilder();
                if (reader.HasRows)
                {
                    List<CQTAddressItem> cqtAddressItems = new List<CQTAddressItem>();
                    while (reader.Read())
                    {
                        CQTAddressItem item = new CQTAddressItem();
                        count++;
                        addProblemstr(count, reader, descTypeDic, problemstr, item);

                        cqtAddressItems.Add(item);
                    }
                    reader.Close();
                    con.Close();
                    if (problemstr.Length > 0)
                    {
                        showResult(problemstr.ToString());
                        cqtAddressItems.Clear();
                    }
                    return cqtAddressItems;
                }
                else
                {
                    reader.Close();
                    con.Close();
                    return new List<CQTAddressItem>();
                }
            }
            catch (Exception ex)
            {
                showResult(ex.Message);
                return new List<CQTAddressItem>();
            }
        }

        private void addProblemstr(int count, OleDbDataReader reader, Dictionary<string, int> descTypeDic, StringBuilder problemstr, CQTAddressItem item)
        {
            item.name = Convert.ToString(reader["CQT点名称"]).Trim();
            if (item.name == "")
            {
                problemstr.Append("Excel表中,第" + count + "行的【CQT点名称】字段不能为空！\r\n");
            }

            item.desc = Convert.ToString(reader["地点描述"]).Trim();
            if (item.desc == "")
            {
                problemstr.Append("Excel表中,第" + count + "行的【地点描述】字段不能为空！\r\n");
            }

            string type = Convert.ToString(reader["CQT类别"]).Trim();
            if (type == "")
            {
                problemstr.Append("Excel表中,第" + count + "行的【CQT类别】字段不能为空！\r\n");
            }
            if (descTypeDic.ContainsKey(type))
            {
                item.cqtTypeId = descTypeDic[type];
            }
            else
            {
                problemstr.Append("Excel表中,第" + count + "行的【CQT类别】不存在该字段所输入的类型！" + type + "\r\n");
            }

            if (!double.TryParse(reader["经度"].ToString().Trim(), out item.jd))
            {
                problemstr.Append("Excel表中,第" + count + "行的【经度】字段有误！\r\n");
            }

            if (!double.TryParse(reader["纬度"].ToString().Trim(), out item.wd))
            {
                problemstr.Append("Excel表中,第" + count + "行的【纬度】字段有误！\r\n");
            }
        }

        /// <summary>
        /// 获取Excel中第一个Sheet名称
        /// </summary>
        /// <param name="filepath"></param>
        /// <param name="numberSheetID"></param>
        /// <returns></returns>
        /// <example>
        /// string sheetName = GetFirstSheetNameFromExcelFileName(strFileUpLoadPath;
        /// </example>
        public static string GetFirstSheetNameFromExcelFileName(string filepath)
        {
            try
            {
                string strFirstSheetName = null;
                Microsoft.Office.Interop.Excel.Application _excelApp = new Microsoft.Office.Interop.Excel.Application();
                Microsoft.Office.Interop.Excel.Workbook objWB = _excelApp.Workbooks.Open(filepath, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing);

                strFirstSheetName = ((Microsoft.Office.Interop.Excel.Worksheet)objWB.Worksheets[1]).Name;

                objWB.Close(Type.Missing, Type.Missing, Type.Missing);
                _excelApp.Quit();
                ExcelHelper.KillCurrentExcel(_excelApp);

                return strFirstSheetName;
            }
            catch (Exception Err)
            {
                return Err.Message;
            }
        }

        private void CalculateDataNeedAddAndUpdate()
        {
            List<CQTAddressItem> commonItems = new List<CQTAddressItem>();
            foreach (CQTAddressItem itemNew in cqtAddressItemsInNewData)
            {
                foreach (CQTAddressItem itemNow in mModel.CurCQTPointList)
                {
                    if (itemNew.name == itemNow.name)
                    {
                        commonItems.Add(itemNew);  //现有地点与新导入地点共有的地点
                    }

                    if ((itemNew.name == itemNow.name) &&
                        ((itemNew.desc != itemNow.desc) || (itemNew.cqtTypeId != itemNow.cqtTypeId)
                        || (itemNew.jd != itemNow.jd) || (itemNew.wd != itemNow.wd)))
                    {
                        itemNew.id = itemNow.id;
                        cqtAddressItemsNeedToUpdate.Add(itemNew);    //有内容更新的新导入的地点
                    }
                }
            }
            List<CQTAddressItem> tempItems = new List<CQTAddressItem>();
            tempItems.AddRange(cqtAddressItemsInNewData);
            foreach (CQTAddressItem im in commonItems)
            {
                tempItems.Remove(im);
            }
            cqtAddressItemsNeedToAdd = tempItems;
        }

        private void showResult(string result)
        {
            textBoxResult.AppendText(result + "\r\n");
        }

        private void highlightChangedItems(bool setHighlight)
        {
            if (setHighlight)
            {
                foreach (int i in highlightSnList)
                {
                    foreach (ListViewItem item in listView.Items)
                    {
                        if ((int)item.SubItems[0].Tag == i)
                        {
                            item.BackColor = Color.SkyBlue;
                        }
                    }
                }
            }
            else
            {
                foreach (ListViewItem item in listView.Items)
                    item.BackColor = Color.White;
            }
            this.listView.Invalidate();
        }

        /// <summary>
        /// 数据恢复到读取Excel表前的状态
        /// </summary>
        private void reset()
        {
            this.textBoxResult.Clear();
            btnImportOK.Enabled = false;
            FillData(mModel.CurCQTPointList);
            highlightChangedItems(false);
            this.progressBar.Value = 0;
            this.lbPercent.Text = "%";
        }

        private void btnStopImport_Click(object sender, EventArgs e)
        {
            backgroundWorker.CancelAsync();
        }

        private void btnClearResult_Click(object sender, EventArgs e)
        {
            this.textBoxResult.Clear();
            this.lbPercent.Text = "%";
            this.progressBar.Value = 0;
        }

        private void backgroundWorker_DoWork(object sender, DoWorkEventArgs e)
        {
            bool success = true;
            /////添加CQT测试点
            success = dealCQTAddress(e, success, cqtAddressItemsNeedToAdd, new Func(addCQTAddress));
            /////

            /////更新CQT测试点
            success = dealCQTAddress(e, success, cqtAddressItemsNeedToUpdate, new Func(updateCQTAddress));
            /////

            //导入结果
            if (success)
            {
                showResult("……\r\n 成功导入所有的CQT测试点！");
            }
            else
            {
                showResult("……\r\n 导入过程中发生异常！");
                progressBar.Value = 0;
                lbPercent.Text = "%";
            }
        }

        private delegate bool Func(bool success, int i);
        private bool dealCQTAddress(DoWorkEventArgs e, bool success, List<CQTAddressItem> cqtAddressItems, Func func)
        {
            if (cqtAddressItems.Count > 0)
            {
                for (int i = 0; i < cqtAddressItems.Count; i++)
                {
                    if (backgroundWorker.CancellationPending)
                    {
                        e.Cancel = true;
                        break;
                    }
                    else
                    {
                        success = func(success, i);
                    }
                }
            }

            return success;
        }

        private bool addCQTAddress(bool success, int i)
        {
            backgroundWorker.ReportProgress((i + 1) * 100 / (cqtAddressItemsNeedToAdd.Count + cqtAddressItemsNeedToUpdate.Count));
            EasyOpHelper hp = new EasyOpHelper(mModel);
            int ret = hp.DoCreateNewCQTAddress(cqtAddressItemsNeedToAdd[i]);
            if (ret >= 0)
            {
                cqtAddressItemsNeedToAdd[i].id = ret;
                mModel.CurCQTPointList.Add(cqtAddressItemsNeedToAdd[i]);
                showResult("成功添加CQT地点：" + cqtAddressItemsNeedToAdd[i].name.ToString());
            }
            else
            {
                showResult(cqtAddressItemsNeedToAdd[i].name.ToString() + "CQT地点添加失败!");
                success = false;
            }

            return success;
        }

        private bool updateCQTAddress(bool success, int i)
        {
            backgroundWorker.ReportProgress((i + 1 + cqtAddressItemsNeedToAdd.Count) * 100 / (cqtAddressItemsNeedToAdd.Count + cqtAddressItemsNeedToUpdate.Count));
            EasyOpHelper hp = new EasyOpHelper(mModel);
            CQTAddressItem importAddressItem = cqtAddressItemsNeedToUpdate[i];
            int ret = hp.DoEditCQTAddress(importAddressItem);
            if (ret > 0)
            {
                showResult("成功更新CQT地点：" + importAddressItem.name.ToString());
                foreach (CQTAddressItem addr in mModel.CurCQTPointList)
                {
                    if (importAddressItem.name == addr.name)
                    {
                        addr.copyInfo(importAddressItem);
                    }
                }
            }
            else
            {
                showResult("更新CQT地点" + importAddressItem.name.ToString() + "发生错误!");
                success = false;
            }

            return success;
        }

        private void backgroundWorker_ProgressChanged(object sender, ProgressChangedEventArgs e)
        {
            if (e.UserState is string)
            {
                textBoxResult.AppendText(e.UserState.ToString() + "\r\n");
            }
            else
            {
                progressBar.Value = e.ProgressPercentage;
                lbPercent.Text = e.ProgressPercentage.ToString() + "%";
            }
        }

        private void backgroundWorker_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            btnStopImport.Enabled = false;
            btnImportOK.Enabled = false;
        }

        private void btnImportOK_Click(object sender, EventArgs e)
        {
            btnStopImport.Enabled = true;
            backgroundWorker.RunWorkerAsync();
        }

        private void CqtAddressManagementForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (!backgroundWorker.IsBusy)
            {
                e.Cancel = false;
            }
            else
            {
                e.Cancel = true;
                MessageBox.Show("关闭本窗口请先停止导入！", "提示");
            }

        }

        private void miRefresh_Click(object sender, EventArgs e)
        {
            highlightChangedItems(false);
            this.FillData(mModel.CurCQTPointList);
            this.listView_SelectedIndexChanged(null, null);
        }

        private void miEditCqtAddr_Click(object sender, EventArgs e)
        {
            foreach (ListViewItem selItem in listView.Items)//高亮选中的项
            {
                if (selItem.Selected)
                    selItem.BackColor = Color.Yellow;
            }
            foreach (ListViewItem selItem in listView.Items)
            {
                if (selItem.Selected)
                {
                    editSelectedCQTAddress(selItem);
                }
            }
        }

        private void editSelectedCQTAddress(ListViewItem selItem)
        {
            CQTAddressItem selAddrItem = selItem.Tag as CQTAddressItem;
            CQTAddrAddDlg dlg = new CQTAddrAddDlg();
            dlg.SetFormProperty(false, "修改CQT测试点");
            dlg.FillCQTType(mModel.CQTTypeInfoList);
            dlg.FillAddrItem(selAddrItem);
            if (DialogResult.OK == dlg.ShowDialog(this))
            {
                CQTAddressItem item = dlg.GetResultItem();
                if (item != null)
                {
                    item.id = selAddrItem.id;
                    editCQTAddress(selAddrItem, item);
                }
            }
            else
            {
                foreach (ListViewItem cleanItem in listView.Items)
                {
                    if (cleanItem.Selected)
                        cleanItem.BackColor = Color.White;
                }
            }
        }

        private void editCQTAddress(CQTAddressItem selAddrItem, CQTAddressItem item)
        {
            EasyOpHelper hp = new EasyOpHelper(mModel);
            int ret = hp.DoEditCQTAddress(item);
            if (ret >= 0)
            {
                selAddrItem.copyInfo(item);
                MessageBox.Show(this, "CQT地点修改成功!");
                foreach (CQTAddressItem addr in mModel.CurCQTPointList)
                {
                    if (addr.name == item.name)
                    {
                        addr.copyInfo(item);
                    }
                }
            }
            else
            {
                MessageBox.Show(this, "CQT地点修改失败，名称重复!");
            }
        }


        private void CqtAddressManagementForm_Load(object sender, EventArgs e)
        {
            this.helpProvider.SetHelpString(btnImport, "从Excel表导入CQT测试点");
            this.helpProvider.SetHelpString(btnImportOK, "预览的导入结果无误，点击将新数据入库！");
        }

        private void miBatchEditDesc_Click(object sender, EventArgs e)
        {
            try
            {
                List<CQTAddressItem> selAddrItemList = new List<CQTAddressItem>();
                bool isFirstSelAddrItem = true;
                string resultDesc = "";
                CQTAddrAddDlg dlg = new CQTAddrAddDlg();
                dlg.SetFormPropertys(false, false, false, true, false, "批量修改所选项的地点描述");
                dlg.FillCQTType(mModel.CQTTypeInfoList);

                foreach (ListViewItem selItem in listView.Items)//高亮选中的项
                {
                    if (selItem.Selected)
                        selItem.BackColor = Color.Yellow;
                }
                foreach (ListViewItem selItem in listView.Items)
                {
                    if (selItem.Selected)
                    {
                        CQTAddressItem selAddrItem = selItem.Tag as CQTAddressItem;
                        if (selAddrItem != null)
                        {
                            bool isEnd = dealResDesc(selAddrItemList, ref isFirstSelAddrItem, ref resultDesc, dlg, selAddrItem);
                            if (isEnd)
                            {
                                return;
                            }

                            editCQT(selAddrItem, "CQT地点描述");
                        }
                    }
                }
            }
            catch
            {
                return;
            }
            MessageBox.Show(this, "CQT描述批量修改成功!");
        }

        private bool dealResDesc(List<CQTAddressItem> selAddrItemList, ref bool isFirstSelAddrItem, ref string resultDesc, CQTAddrAddDlg dlg, CQTAddressItem selAddrItem)
        {
            selAddrItemList.Add(selAddrItem);
            if (isFirstSelAddrItem)
            {
                if (DialogResult.OK == dlg.ShowDialog(this))
                {
                    resultDesc = dlg.GetResultItemsDesc();
                    selAddrItem.desc = resultDesc;
                }
                else
                {
                    foreach (ListViewItem cleanItem in listView.Items)
                    {
                        if (cleanItem.Selected)
                            cleanItem.BackColor = Color.White;
                    }
                    return true;
                }
                isFirstSelAddrItem = false;
            }
            else
            {
                selAddrItem.desc = resultDesc;
            }
            return false;
        }

        private void editCQT(CQTAddressItem selAddrItem, string desc)
        {
            EasyOpHelper hp = new EasyOpHelper(mModel);
            int ret = hp.DoEditCQTAddress(selAddrItem);
            if (ret >= 0)
            {
                selAddrItem.copyInfo(selAddrItem);
                foreach (CQTAddressItem addr in mModel.CurCQTPointList)
                {
                    if (addr.name == selAddrItem.name)
                    {
                        addr.copyInfo(selAddrItem);
                    }
                }
            }
            else
            {
                MessageBox.Show(this, desc + "修改失败!");
            }
        }

        private void miBatchEditType_Click(object sender, EventArgs e)
        {
            try
            {
                List<CQTAddressItem> selAddrItemList = new List<CQTAddressItem>();
                bool isFirstSelAddrItem = true;
                int resultTypeId = -1;
                CQTAddrAddDlg dlg = new CQTAddrAddDlg();
                dlg.SetFormPropertys(false, false, false, false, true, "批量修改所选项的类别描述");
                dlg.FillCQTType(mModel.CQTTypeInfoList);

                foreach (ListViewItem selItem in listView.Items)//高亮选中的项
                {
                    if (selItem.Selected)
                        selItem.BackColor = Color.Yellow;
                }
                foreach (ListViewItem selItem in listView.Items)
                {
                    if (selItem.Selected)
                    {
                        CQTAddressItem selAddrItem = selItem.Tag as CQTAddressItem;
                        if (selAddrItem != null)
                        {
                            bool isEnd = dealResultType(selAddrItemList, ref isFirstSelAddrItem, ref resultTypeId, dlg, selAddrItem);
                            if (isEnd)
                            {
                                return;
                            }

                            editCQT(selAddrItem, "CQT类别");
                        }
                    }
                }
            }
            catch
            {
                return;
            }
            MessageBox.Show(this, "CQT类别批量修改成功!");
        }

        private bool dealResultType(List<CQTAddressItem> selAddrItemList, ref bool isFirstSelAddrItem, ref int resultTypeId, CQTAddrAddDlg dlg, CQTAddressItem selAddrItem)
        {
            selAddrItemList.Add(selAddrItem);
            if (isFirstSelAddrItem)
            {
                if (DialogResult.OK == dlg.ShowDialog(this))
                {
                    resultTypeId = dlg.GetResultItemsType();
                    if (resultTypeId == -1)
                    {
                        return true;
                    }
                    selAddrItem.cqtTypeId = resultTypeId;
                }
                else
                {
                    foreach (ListViewItem cleanItem in listView.Items)
                    {
                        if (cleanItem.Selected)
                            cleanItem.BackColor = Color.White;
                    }
                    return true;
                }
                isFirstSelAddrItem = false;
            }
            else
            {
                selAddrItem.cqtTypeId = resultTypeId;
            }
            return false;
        }

        private void miModify_Click(object sender, EventArgs e)
        {
            if (listView.SelectedItems.Count > 0)
            {
                CQTAddressItem item = listView.SelectedItems[0].Tag as CQTAddressItem;
                CqtAddrItemMngForm frm = new CqtAddrItemMngForm(item, mModel.CQTTypeInfoList);
                frm.ShowDialog();
            }
        }

        #region AddrList
        private void btnAddAddr_Click(object sender, EventArgs e)
        {
            curAddrOpType = AddrItemOpType.Add;
            CQTAddressItem item = new CQTAddressItem();
            fillGrpOp(item);
            setGrpOpLayout(true);
            curAddrOpType = AddrItemOpType.Add;
        }

        private void btnModifyAddr_Click(object sender, EventArgs e)
        {
            if (listView.SelectedItems.Count > 0)
            {
                curAddrOpType = AddrItemOpType.Modify;
                setGrpOpLayout(true);
                CQTAddressItem item = listView.SelectedItems[0].Tag as CQTAddressItem;
                fillGrpOp(item);
            }
        }

        private void btnAddrDelete_Click(object sender, EventArgs e)
        {
            if (listView.SelectedItems.Count > 0)
            {
                CQTAddressItem item = listView.SelectedItems[0].Tag as CQTAddressItem;
                if (XtraMessageBox.Show("确定要删除测试点”" + item.name + "“？", "确认", MessageBoxButtons.YesNo) == DialogResult.Yes)
                {
                    EasyOpHelper hp = new EasyOpHelper(mModel);
                    int ret = hp.DoDeleteCQTAddress(item);
                    if (ret >= 0)
                    {
                        mModel.CurCQTPointList.Remove(item);
                        mModel.SelectedCQTAddrItem = null;
                        XtraMessageBox.Show(this, "CQT地点删除成功!");
                        FillData(mModel.CurCQTPointList);
                        mModel.MainForm.GetMapForm().GetCqtAddrLayer().Invalidate();
                    }
                    else
                    {
                        XtraMessageBox.Show(this, "CQT地点删除失败!");
                    }
                }
            }
        }

        private void listView_SelectedIndexChanged(object sender, EventArgs e)
        {
            //bool hasSelItem = false;
            //foreach (ListViewItem selItem in listView.Items)
            //{
            //    if (selItem.Selected)
            //    {
            //        hasSelItem = true;
            //        break;
            //    }
            //}
            btnModifyAddr.Enabled = btnAddrDelete.Enabled = listView.SelectedItems.Count > 0;
            //if (hasSelItem)
            //{
            //    this.miEditCqtAddr.Enabled = true;
            //    this.miBatchEditDesc.Enabled = true;
            //    this.miBatchEditType.Enabled = true;
            //}
            //else
            //{
            //    this.miEditCqtAddr.Enabled = false;
            //    this.miBatchEditDesc.Enabled = false;
            //    this.miBatchEditType.Enabled = false;
            //}
        }

        private void listView_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (listView.SelectedItems.Count > 0)
            {
                CQTAddressItem item = listView.SelectedItems[0].Tag as CQTAddressItem;
                mModel.SelectedCQTAddrItem = item;
                mModel.MainForm.GetMapForm().GoToView(item.jd, item.wd);
            }
        }
        #endregion

        private CQTAddressItem curSelAddrItem;

        private void setGrpOpLayout(bool visible)
        {
            if (visible)
            {
                groupControlCQTAddrs.Visible = false;
                groupControlBatImp.Visible = false;
                groupControlOP.Visible = true;
                groupControlOP.Dock = DockStyle.Fill;
                groupControlOP.Text = curAddrOpType == AddrItemOpType.Add ? "新增" : "修改";
                grpImg.Visible = true;
                grpImg.Dock = DockStyle.Fill;
            }
            else
            {
                groupControlOP.Visible = false;
                groupControlBatImp.Visible = true;
                //groupControlBatImp.Dock = DockStyle.Top;
                groupControlCQTAddrs.Visible = true;
                //groupControlCQTAddrs.Dock = DockStyle.Fill;
            }
        }

        #region Op Grp
        private void fillGrpOp(CQTAddressItem addrItem)
        {
            curSelAddrItem = addrItem;

            tbxAddrName.Text = addrItem.name;
            tbxAddrDesc.Text = addrItem.desc;
            nudJD.Value = (decimal)addrItem.jd;
            nudWD.Value = (decimal)addrItem.wd;
            tbxImgCount.Text = addrItem.ImageCount.ToString();

            cbxAreaType.Items.Clear();
            foreach (CQTTypeInfoItem cqtType in mModel.CQTTypeInfoList)
            {
                cbxAreaType.Items.Add(cqtType);
                if (addrItem.cqtTypeId == cqtType.areaTypeId)
                {
                    cbxAreaType.SelectedItem = cqtType;
                }
            }

            fillImgLV(addrItem);
        }

        private void fillImgLV(CQTAddressItem addrItem)
        {
            listViewImg.BeginUpdate();
            listViewImg.Items.Clear();
            imageList.Images.Clear();
            foreach (CQTAddrItemImage img in addrItem.Images)
            {
                imageList.Images.Add(img.Name, img.Img);
                ListViewItem item = new ListViewItem(img.Name, img.Name);
                listViewImg.Items.Add(item);
            }
            if (listViewImg.Items.Count > 0)
            {
                listViewImg.Items[0].Selected = true;
            }
            else
            {
                listViewImg_SelectedIndexChanged(null, null);
            }
            listViewImg.EndUpdate();
        }

        private void btnCloseOpGrp_Click(object sender, EventArgs e)
        {
            setImgOpGrpLayout(false);
            setGrpOpLayout(false);
            FillData(mModel.CurCQTPointList);
        }

        private void btnCommitAddr_Click(object sender, EventArgs e)
        {
            string name = tbxAddrName.Text;
            double jd = (double)nudJD.Value;
            double wd = (double)nudWD.Value;
            CQTTypeInfoItem typeInfoItem = cbxAreaType.SelectedItem as CQTTypeInfoItem;
            bool isValid = judgeValid(name, jd, wd, typeInfoItem);
            if (!isValid)
            {
                return;
            }

            string retStr = "";
            if (curAddrOpType == AddrItemOpType.Add)
            {
                curSelAddrItem.cqtTypeId = typeInfoItem.areaTypeId;
                curSelAddrItem.name = name;
                curSelAddrItem.jd = jd;
                curSelAddrItem.wd = wd;
                curSelAddrItem.desc = tbxAddrDesc.Text;
                EasyOpHelper hp = new EasyOpHelper(mModel);
                if (hp.DoCreateNewCQTAddress(curSelAddrItem) >= 0)
                {
                    retStr = "新增改CQT测试点成功";
                    mModel.CurCQTPointList.Add(curSelAddrItem);
                }
                else
                {
                    retStr = "新增CQT测试点失败";
                }
            }
            else if (curAddrOpType == AddrItemOpType.Modify)
            {
                if (curSelAddrItem.cqtTypeId != typeInfoItem.areaTypeId)
                {
                    DialogResult res = XtraMessageBox.Show("修改测试地点的类型可能会造成已入库文件关联测试地点错误！确定要修改？", "提醒", MessageBoxButtons.YesNo);
                    if (res != DialogResult.Yes)
                        return;
                }
                EasyOpHelper hp = new EasyOpHelper(mModel);
                curSelAddrItem.cqtTypeId = typeInfoItem.areaTypeId;
                curSelAddrItem.name = name;
                curSelAddrItem.jd = jd;
                curSelAddrItem.wd = wd;
                curSelAddrItem.desc = tbxAddrDesc.Text;
                if (hp.DoEditCQTAddress(curSelAddrItem) >= 0)
                {
                    retStr = "修改CQT测试点成功";
                }
                else
                {
                    retStr = "修改CQT测试点失败";
                }
            }
            XtraMessageBox.Show(retStr);
        }

        private bool judgeValid(string name, double jd, double wd, CQTTypeInfoItem typeInfoItem)
        {
            if (name.Trim().Length == 0)
            {
                XtraMessageBox.Show("请填写测试地点名称！");
                return false;
            }
            if (typeInfoItem == null)
            {
                XtraMessageBox.Show("请选择测试地点类型！");
                return false;
            }
            if (jd == 0)
            {
                XtraMessageBox.Show("测试地点的经度不能为0！");
                return false;
            }
            if (wd == 0)
            {
                XtraMessageBox.Show("测试地点的纬度不能为0！");
                return false;
            }
            return true;
        }
        #endregion

        #region IMG Grp
        private void listViewImg_SelectedIndexChanged(object sender, EventArgs e)
        {
            bool isSel = listViewImg.SelectedItems.Count > 0;
            btnDeleteImg.Enabled = btnModifyImg.Enabled = isSel;
            if (isSel)
            {
                curSelImg = curSelAddrItem.Images[listViewImg.SelectedIndices[0]];
                tbxImgName.Text = curSelImg.Name;
                tbxImgDesc.Text = curSelImg.Desc;
                nudRatio.Value = (decimal)curSelImg.Ratio;
            }
            else
            {
                curSelImg = null;
            }
        }

        private void btnDeleteImg_Click(object sender, EventArgs e)
        {
            if (XtraMessageBox.Show("确定要删除该图片信息？", "确认", MessageBoxButtons.YesNo) == DialogResult.Yes)
            {
                DiyDeleteCqtImage deleteImg = new DiyDeleteCqtImage(MainModel.GetInstance(), curSelImg);
                deleteImg.Query();
                curSelAddrItem.Images.Remove(curSelImg);
                fillGrpOp(curSelAddrItem);
            }
        }

        private void btnModifyImg_Click(object sender, EventArgs e)
        {
            showImgInfo();
            setImgOpGrpLayout(true);
            curImgOpType = CQTImgOPType.Modify;
        }

        private void btnAddImg_Click(object sender, EventArgs e)
        {
            //CQTAddrItemImage img = new CQTAddrItemImage(curSelAddrItem.cqtTypeId, curSelAddrItem.id, "", "", 9000, null);
            clearImgAddGrp();
            setImgOpGrpLayout(true);
            curImgOpType = CQTImgOPType.Add;
        }
        #endregion

        private void clearImgAddGrp()
        {
            pictureEdit.Image = null;
            tbxOpImgDesc.Text = "";
            tbxOpImgName.Text = "";
            nudOpImgRatio.Value = 9000;
        }

        private void showImgInfo()
        {
            if (curSelImg.Img != null)
            {
                pictureEdit.Image = curSelImg.Img.Clone() as Image;
            }
            else
            {
                pictureEdit.Image = null;
            }
            tbxOpImgDesc.Text = curSelImg.Desc;
            tbxOpImgName.Text = curSelImg.Name;
            nudOpImgRatio.Value = (decimal)curSelImg.Ratio;
        }

        private CQTAddrItemImage curSelImg = null;

        private void setImgOpGrpLayout(bool visible)
        {
            if (visible)
            {
                grpImg.Visible = false;
                grpOpImg.Visible = true;
                grpOpImg.Dock = DockStyle.Fill;
                grpOpImg.Text = curImgOpType == CQTImgOPType.Add ? "新增" : "修改";
            }
            else
            {
                grpOpImg.Visible = false;
                grpImg.Visible = true;
                grpImg.Dock = DockStyle.Fill;
            }
        }

        #region IMG OP Grp
        private void btnCloseImgOpGrp_Click(object sender, EventArgs e)
        {
            cancel();
        }

        private void btnImgCancel_Click(object sender, EventArgs e)
        {
            cancel();
        }

        private void cancel()
        {
            setImgOpGrpLayout(false);
            fillImgLV(curSelAddrItem);
        }

        private void btnLoadImg_Click(object sender, EventArgs e)
        {
            Image imgTmp = null;
            string imgName = null;
            openImgFile(out imgTmp, out imgName);
            if (imgTmp != null)
            {
                pictureEdit.Image = imgTmp;
                tbxOpImgName.Text = imgName;
            }
        }

        private void openImgFile(out Image img, out string imgName)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.CheckFileExists = true;
            dlg.CheckPathExists = true;
            dlg.Filter = "图片文件|*.jpg;*.png;*.bmp;*.gif";
            dlg.Multiselect = false;
            dlg.ValidateNames = true;
            dlg.RestoreDirectory = true;
            img = null;
            imgName = "";
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    Image imgTmp = Image.FromFile(dlg.FileName);
                    img = new Bitmap(imgTmp);
                    imgTmp.Dispose();
                    imgName = System.IO.Path.GetFileNameWithoutExtension(dlg.FileName);
                }
                catch (System.Exception ex)
                {
                    XtraMessageBox.Show(ex.Message);
                }
            }
        }

        private void btnImgOK_Click(object sender, EventArgs e)
        {
            Image imgTmp = null;
            if (pictureEdit.Image == null)
            {
                XtraMessageBox.Show("请选择图片！");
                return;
            }
            try
            {
                imgTmp = pictureEdit.Image.Clone() as Image;
            }
            catch (System.Exception ex)
            {
                XtraMessageBox.Show(ex.ToString());
                return;
            }

            string name = tbxOpImgName.Text;
            if (name.Trim().Length == 0)
            {
                XtraMessageBox.Show("请填写图片名！");
                return;
            }

            foreach (CQTAddrItemImage img in curSelAddrItem.Images)
            {
                if (img.Equals(curSelImg))
                {
                    continue;
                }
                if (img.Name == name)
                {
                    XtraMessageBox.Show("不能与现有的图片名相同！");
                    return;
                }
            }

            double ratio = (double)nudOpImgRatio.Value;
            if (ratio <= 0)
            {
                XtraMessageBox.Show("图片的比例尺必须大于0！");
                return;
            }

            string imgDesc = tbxOpImgDesc.Text;
            CQTAddrItemImage newImg = new CQTAddrItemImage(curSelAddrItem.cqtTypeId, curSelAddrItem.id, name, imgDesc, ratio, imgTmp);
            if (curImgOpType == CQTImgOPType.Add)
            {
                UpLoadCQTImage2DB upload = new UpLoadCQTImage2DB(MainModel.GetInstance(), newImg);
                upload.Query();
                curSelAddrItem.Images.Add(newImg);
            }
            else if (curImgOpType == CQTImgOPType.Modify)
            {
                DiyUpdateCqtImage update = new DiyUpdateCqtImage(MainModel.GetInstance(), curSelImg, newImg);
                update.Query();
                int idx = curSelAddrItem.Images.IndexOf(curSelImg);
                curSelAddrItem.Images[idx] = newImg;
            }
            setImgOpGrpLayout(false);
            fillGrpOp(curSelAddrItem);
        }

        #endregion

        CQTImgOPType curImgOpType;
        AddrItemOpType curAddrOpType;
        private enum AddrItemOpType
        {
            Modify = 1,
            Add = 2
        }
    }
  
}
