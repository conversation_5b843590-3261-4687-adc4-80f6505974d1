﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CSFBCauseInfoForm : MinCloseForm
    {
        List<CScene> sceneList;
        Dictionary<string, CSFBBlockCallSummaryInfo> nameSumaryDic;

        List<string> reasonNameLst;

        public CSFBCauseInfoForm()
        {
            InitializeComponent();
            reasonNameLst = new List<string>();
        }

        public void FillData(Dictionary<string, CSFBBlockCallSummaryInfo> nameSumaryDic, List<CScene> sceneList)
        {
            this.nameSumaryDic = nameSumaryDic;
            this.sceneList = sceneList;
            getReason();
            refreshSummary();
            refreshDetail();
        }

        private void getReason()
        {
            reasonNameLst.Clear();
            foreach (CScene scene in sceneList)
            {
                CCauseBase cb = scene.SceneCause;

                while (cb != null)
                {
                    if (cb.Cond.BChecked && !reasonNameLst.Contains(cb.GetCauseName()))
                    {
                        reasonNameLst.Add(cb.GetCauseName());
                    }
                    cb = cb.NextCause;
                }
            }
            COtherCause other = new COtherCause();
            if(!reasonNameLst.Contains(other.GetCauseName()))
            {
                reasonNameLst.Add(other.GetCauseName());
            }
        }

        private void refreshSummary()
        {
            DataTable table = new DataTable();
            table.Columns.Add("原因", typeof(string));
            table.Columns.Add("个数", typeof(int));
            table.Columns.Add("占比(%)", typeof(double));

            DevExpress.XtraCharts.Series sReason = chartControlReason.Series[0];
            //sReason.PointOptions.ValueNumericOptions.Format = DevExpress.XtraCharts.NumericFormat.Percent;
            sReason.SeriesPointsSorting = DevExpress.XtraCharts.SortingMode.Descending;
            sReason.SeriesPointsSortingKey = DevExpress.XtraCharts.SeriesPointKey.Value_1;
            sReason.Points.Clear();

            foreach (string reason in reasonNameLst)
            {
                DataRow row = table.NewRow();
                row["原因"] = reason;
                if (nameSumaryDic.ContainsKey(reason))
                {
                    row["个数"] = nameSumaryDic[reason].Num;
                    row["占比(%)"] = Math.Round(nameSumaryDic[reason].Percent, 2);
                    DevExpress.XtraCharts.SeriesPoint sp = 
                        new DevExpress.XtraCharts.SeriesPoint(reason, Math.Round(nameSumaryDic[reason].Percent, 2));
                    sReason.Points.Add(sp);
                }
                else
                {
                    row["个数"] = 0;
                    row["占比(%)"] = 0;
                    DevExpress.XtraCharts.SeriesPoint sp = new DevExpress.XtraCharts.SeriesPoint(reason, 0);
                    sReason.Points.Add(sp);
                }
                table.Rows.Add(row);
            }
            gridSummary.DataSource = table;
            gridSummary.RefreshDataSource();
            viewSummary.PopulateColumns();
        }

        private void refreshDetail()
        {
            List<CSFBBlockCallSummaryInfo> summaryInfoLst = new List<CSFBBlockCallSummaryInfo>(nameSumaryDic.Values);
            List<CSFBBlockCallInfo> bcInfoLst = new List<CSFBBlockCallInfo>();
            foreach (CSFBBlockCallSummaryInfo summary in summaryInfoLst)
            {
                bcInfoLst.AddRange(summary.BlockCallInfoLst);
            }
            gridDetail.DataSource = bcInfoLst;
            gridDetail.RefreshDataSource();
        }

        private void toolStripMenuItemReplay_Click(object sender, EventArgs e)
        {
            int[] hRow = viewDetail.GetSelectedRows();

            if (hRow.Length <= 0) return;

            CSFBBlockCallInfo info = viewDetail.GetRow(hRow[0]) as CSFBBlockCallInfo;
            if (info == null) return;


            if (info.Eblockcall != null)
            {
                FileReplayer.Replay(info.Eblockcall, true);
            }
        }

        private void ToolStripMenuItemExport_Click(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv;
            if (xtraTabControl.SelectedTabPage == xtraTabPageSummary)
            {
                gv = viewSummary;
            }
            else
            {
                gv = viewDetail;
            }

            try
            {
                ExcelNPOIManager.ExportToExcel(gv);
            }
            catch
            {
                MessageBox.Show("导出到Excel失败...", "提示");
            }
        }

        private void contextMenuStrip_Opening(object sender, CancelEventArgs e)
        {
            toolStripMenuItemReplay.Visible = xtraTabControl.SelectedTabPage == xtraTabPageDetail;
        }

        private void viewDetail_DoubleClick(object sender, EventArgs e)
        {
            CSFBBlockCallInfo info = viewDetail.GetRow(viewDetail.GetSelectedRows()[0]) as CSFBBlockCallInfo;
            if (info != null)
            {
                MainModel.ClearDTData();
                foreach (MasterCom.RAMS.Model.Event evt in info.EvtList)
                {
                    MainModel.DTDataManager.Add(evt);
                }
                MainModel.MainForm.GetMapForm().GoToView(info.CenterLongitude, info.CenterLatitude, 6000);
                MainModel.FireDTDataChanged(this);
            }
        }
    }
}
