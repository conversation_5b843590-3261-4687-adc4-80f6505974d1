﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteDriveSpeedWithKpiAnaByFile : LteDriveSpeedWithKpiAnaBase
    {
        protected LteDriveSpeedWithKpiAnaByFile()
            : base()
        {
        }

        private static LteDriveSpeedWithKpiAnaByFile instance = null;
        public static LteDriveSpeedWithKpiAnaByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteDriveSpeedWithKpiAnaByFile();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "指标与车速分析(按文件)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22097, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

    }

    public class LteDriveSpeedWithKpiAnaByFile_FDD : LteDriveSpeedWithKpiAnaByFile
    {
        private static LteDriveSpeedWithKpiAnaByFile_FDD instance = null;
        public static new LteDriveSpeedWithKpiAnaByFile_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteDriveSpeedWithKpiAnaByFile_FDD();
                    }
                }
            }
            return instance;
        }
        protected LteDriveSpeedWithKpiAnaByFile_FDD()
            : base()
        {

        }
        public override string Name
        {
            get { return "指标与车速分析LTE_FDD(按文件)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26064, this.Name);
        }
    }
}
