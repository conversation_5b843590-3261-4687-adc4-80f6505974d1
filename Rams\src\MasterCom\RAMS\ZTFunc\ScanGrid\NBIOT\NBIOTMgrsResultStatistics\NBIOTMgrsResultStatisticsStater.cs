﻿using MasterCom.MTGis;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NbIotMgrsResultStatisticsStater : NbIotMgrsStaterBase
    {
        private DbRect selectRect { get; set; }

        public void SetSelectRegion(DbRect selectedRect)
        {
            selectRect = selectedRect;
        }

        private List<NbIotMgrsResultInfo> allResultList = null;

        public void SetResultData(Dictionary<string, string> resultData)
        {
            this.resultList = resultData;
        }

        public override void DoStat(NbIotMgrsFuncItem curFuncItem)
        {
            allResultList = new List<NbIotMgrsResultInfo>();

            allResultList.Add(cmCarrierAreaResult.carrierResult);
            allResultList.Add(cuCarrierAreaResult.carrierResult);
            allResultList.Add(ctCarrierAreaResult.carrierResult);
        }

        #region 由于无法用存储过程对网格区域进行计算,暂屏蔽
        //private void dealWithData(int carrierType)
        //{
        //    NBIOTMgrsResultInfo carrierResult = new NBIOTMgrsResultInfo();
        //    string tableNames = "";
        //    string fileIDs = "";
        //    foreach (var item in MainModel.FileInfos)
        //    {
        //        if (item.CarrierType == carrierType)
        //        {
        //            tableNames += NBIOTMgrsQueryFuncBase.ModelTableprefix + item.StrWeek + ",";
        //            fileIDs += item.ID.ToString() + ",";
        //        }
        //    }
        //    if (!string.IsNullOrEmpty(tableNames) && !string.IsNullOrEmpty(fileIDs))
        //    {
        //        NBIOTMgrsResultStatisticsQuery query = new NBIOTMgrsResultStatisticsQuery(MainModel);
        //        query.SetQueryCondition(tableNames.TrimEnd(','), fileIDs.TrimEnd(','), selectRect.x1, selectRect.y2, selectRect.x2, selectRect.y1);
        //        query.Query();
        //        carrierResult = query.GetResult();
        //    }
        //    if (carrierType == 1)
        //    {
        //        carrierResult.Name = "中国移动";
        //        carrierResult.SerialWeakRSRPGridCoverage = resultList["连续弱覆盖ChinaTelecom"];
        //        carrierResult.SerialNoRSRPGridCoverage = resultList["连续无覆盖ChinaTelecom"];
        //        carrierResult.SerialWeakSINRGridCoverage = resultList["连续质差ChinaTelecom"];
        //        carrierResult.SerialOverlapCoverage = resultList["连续重叠覆盖ChinaTelecom"];
        //        //carrierResult.areaList = ;
        //    }
        //    else if (carrierType == 2)
        //    {
        //        carrierResult.Name = "中国联通";
        //        carrierResult.SerialWeakRSRPGridCoverage = resultList["连续弱覆盖ChinaUnicom"];
        //        carrierResult.SerialNoRSRPGridCoverage = resultList["连续无覆盖ChinaUnicom"];
        //        carrierResult.SerialWeakSINRGridCoverage = resultList["连续质差ChinaUnicom"];
        //        carrierResult.SerialOverlapCoverage = resultList["连续重叠覆盖ChinaUnicom"];
        //    }
        //    else if (carrierType == 3)
        //    {
        //        carrierResult.Name = "中国电信";
        //        carrierResult.SerialWeakRSRPGridCoverage = resultList["连续弱覆盖ChinaMobile"];
        //        carrierResult.SerialNoRSRPGridCoverage = resultList["连续无覆盖ChinaMobile"];
        //        carrierResult.SerialWeakSINRGridCoverage = resultList["连续质差ChinaMobile"];
        //        carrierResult.SerialOverlapCoverage = resultList["连续重叠覆盖ChinaMobile"];
        //    }

        //    allResultList.Add(carrierResult);
        //}
        #endregion

        public override void SetResultControl()
        {
            NbIotMgrsResultStatisticsResult resultControl = new NbIotMgrsResultStatisticsResult();
            resultControl.FillData(allResultList);
            resultControlList = new List<NbIotMgrsResultControlBase>() { resultControl };
        }

        public override void Clear()
        {
            allResultList = null;
            selectRect = null;
        }
    }

    public class NbIotMgrsResultInfo
    {
        public NbIotMgrsResultInfo()
        {
            AreaList = new List<NbIotMgrsAreaResultInfo>();
        }

        public string Name
        {
            get;
            set;
        }

        private float coreCityComprehensiveCoverage;
        /// <summary>
        /// 核心城区综合覆盖率
        /// </summary>
        public string CoreCityComprehensiveCoverage
        {
            get { return coreCityComprehensiveCoverage * 100 + "%"; }
        }
        private float normalCityComprehensiveCoverage;
        /// <summary>
        /// 一般城区综合覆盖率
        /// </summary>
        public string NormalCityComprehensiveCoverage
        {
            get { return normalCityComprehensiveCoverage * 100 + "%"; }
        }
        private float coreCityCoverage;
        /// <summary>
        /// 核心城区覆盖率
        /// </summary>
        public string CoreCityCoverage
        {
            get { return coreCityCoverage * 100 + "%"; }
        }
        private float normalCityCoverage;
        /// <summary>
        /// 一般城区覆盖率
        /// </summary>
        public string NormalCityCoverage
        {
            get { return normalCityCoverage * 100 + "%"; }
        }
        private float highCoverage;
        /// <summary>
        /// 高度重叠覆盖率
        /// </summary>
        public string HighCoverage
        {
            get { return highCoverage * 100 + "%"; }
        }
        private float edgeRSRP;
        /// <summary>
        /// 边缘RSRP
        /// </summary>
        public float EdgeRSRP
        {
            get { return edgeRSRP; }
        }
        private float avgRSRP;
        /// <summary>
        /// 平均RSRP
        /// </summary>
        public float AvgRSRP
        {
            get { return avgRSRP; }
        }
        private float edgeSINR;
        /// <summary>
        /// 边缘SINR
        /// </summary>
        public float EdgeSINR
        {
            get { return edgeSINR; }
        }
        private float avgSINR;
        /// <summary>
        /// 平均SINR
        /// </summary>
        public float AvgSINR
        {
            get { return avgSINR; }
        }
        public string SerialWeakRSRPGridCoverage
        { get; set; }
        public string SerialNoRSRPGridCoverage
        { get; set; }
        public string SerialWeakSINRGridCoverage
        { get; set; }
        public string SerialOverlapCoverage
        { get; set; }
        
        public List<NbIotMgrsAreaResultInfo> AreaList { get; set; }

        public static NbIotMgrsResultInfo Fill(Content content)
        {
            NbIotMgrsResultInfo info = new NbIotMgrsResultInfo();
            info.coreCityComprehensiveCoverage = content.GetParamFloat();
            info.normalCityComprehensiveCoverage = content.GetParamFloat();
            info.coreCityCoverage = content.GetParamFloat();
            info.normalCityCoverage = content.GetParamFloat();
            info.highCoverage = content.GetParamFloat();
            info.avgRSRP = content.GetParamFloat();
            info.edgeRSRP = content.GetParamFloat();
            info.avgSINR = content.GetParamFloat();
            info.edgeSINR = content.GetParamFloat();
            return info;
        }
    }

    public class NbIotMgrsAreaResultInfo
    {
        public NbIotMgrsAreaResultInfo(string name)
        {
            AreaName = name;
            SerialWeakRSRPGridCoverage = "0.00%";
            SerialNoRSRPGridCoverage = "0.00%";
            SerialWeakSINRGridCoverage = "0.00%";
            SerialOverlapCoverage = "0.00%";
        }
        
        public string AreaName { get; set; }
        public string SerialWeakRSRPGridCoverage { get; set; }
        public string SerialNoRSRPGridCoverage { get; set; }
        public string SerialWeakSINRGridCoverage { get; set; }
        public string SerialOverlapCoverage { get; set; }

        public object this[string name]
        {
            get { return AreaName; }
        }
    }
}
