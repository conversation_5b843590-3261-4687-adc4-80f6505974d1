﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class PoorRsrqCell
    {
        public LTECell Cell
        {
            get;
            set;
        }

        public virtual string Name
        {
            get { return this.Cell.Name; }
        }

        public virtual int Tac
        {
            get { return this.Cell.TAC; }
        }

        public virtual int Eci
        {
            get { return this.Cell.ECI; }
        }

        public virtual int CellID
        {
            get { return this.Cell.SCellID; }
        }

        public PoorRsrqCell(LTECell cell)
        {
            this.Cell = cell;
        }

        public PoorRsrqCell()
        { 
        
        }

        public float totalRsrq { get; set; } = 0;
        public float totalSINR { get; set; } = 0;
        public float totalRSRP { get; set; } = 0;

        protected readonly float minRsrq = float.MaxValue;
        public float MinRsrq
        {
            get { return minRsrq; }
        }
        protected readonly float maxRsrq = float.MinValue;
        public float MaxRsrq
        {
            get { return maxRsrq; }
        }
        public float AvgRsrq
        {
            get;
            protected set;
        }

        public int sinrNum { get; set; }
        protected readonly float minSINR = float.MaxValue;
        public float MinSINR
        {
            get { return minSINR; }
        }
        protected readonly float minRSPR = float.MaxValue;
        public float MinRSRP
        {
            get { return minRSPR; }
        }
        public float MaxSINR
        {
            get { return maxSINR; }
        }
        protected readonly float maxSINR = float.MinValue;
        public float MaxRSRP
        {
            get { return maxRSRP; }
        }
        protected readonly float maxRSRP = float.MinValue;
        public float AvgSINR
        {
            get { return (float)Math.Round(totalSINR / sinrNum, 2); }
        }
        public int rsrpNum { get; set; }
        public float AvgRSRP
        {
            get { return (float)Math.Round(totalRSRP / rsrpNum, 2); }
        }

        protected readonly List<TestPoint> goodPoints = new List<TestPoint>();
        public List<TestPoint> GoodPoints
        {
            get { return goodPoints; }
        }
        protected readonly List<TestPoint> poorPoints = new List<TestPoint>();
        public List<TestPoint> PoorPoints
        {
            get { return poorPoints; }
        }

        protected float totalPoorRsrq;
        protected float? minPoorRsrq = null;
        public float? MinPoorRsrq
        {
            get { return minPoorRsrq; }
        }
        protected float? maxPoorRsrq = null;
        public float? MaxPoorRsrq
        {
            get { return maxPoorRsrq; }
        }
        public float? AvgPoorRsrq
        {
            get
            {
                if (PoorTestPointCount != 0)
                {
                    return (float)Math.Round(totalPoorRsrq / PoorTestPointCount, 2);
                }
                return null;
            }
        }

        protected float totalGoodRsrq;
        protected float? minGoodRsrq = null;
        public float? MinGoodRsrq
        {
            get { return minGoodRsrq; }
        }
        protected float? maxGoodRsrq = null;
        public float? MaxGoodRsrq
        {
            get { return maxGoodRsrq; }
        }
        public float? AvgGoodRsrq
        {
            get
            {
                if (GoodTestPointCount!=0)
                {
                    return (float)Math.Round(totalGoodRsrq / GoodTestPointCount, 2);
                }
                return null;
            }
        }
        internal void AddTestPoint(TestPoint tp, float rsrq, bool poorPt)
        {
            if (poorPt)
            {
                poorPoints.Add(tp);
                totalPoorRsrq += rsrq;
                if (minPoorRsrq == null)
                {
                    minPoorRsrq = rsrq;
                }
                else
                {
                    minPoorRsrq = Math.Min(rsrq, (float)minPoorRsrq);
                }

                if (maxPoorRsrq == null)
                {
                    maxPoorRsrq = rsrq;
                }
                else
                {
                    maxPoorRsrq = Math.Max(rsrq, (float)maxPoorRsrq);
                }
            }
            else
            {
                goodPoints.Add(tp);
                totalGoodRsrq += rsrq;
                if (minGoodRsrq == null)
                {
                    minGoodRsrq = rsrq;
                }
                else
                {
                    minGoodRsrq = Math.Min(rsrq, (float)minGoodRsrq);
                }

                if (maxGoodRsrq == null)
                {
                    maxGoodRsrq = rsrq;
                }
                else
                {
                    maxGoodRsrq = Math.Max(rsrq, (float)maxGoodRsrq);
                }
            }
        }

        public virtual void AddOtherTPInfo(TestPoint testPoint)
        { 
        
        }

        public double PoorPercent
        {
            get { return Math.Round(100.0 * PoorTestPointCount / TotalTestPointCount, 2); }
        }
        public double PoorTestPointCount
        {
            get { return poorPoints.Count; }
        }
        public double GoodTestPointCount
        {
            get { return goodPoints.Count; }
        }
        public double TotalTestPointCount
        {
            get { return poorPoints.Count + goodPoints.Count; }
        }
    }
}
