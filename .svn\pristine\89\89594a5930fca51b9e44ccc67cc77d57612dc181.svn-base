﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;

namespace MasterCom.RAMS.CQT
{
    public class DIYQueryCQTTestTask : DIYSQLBase
    {
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 21000, 21006, this.Name);
        }
        public DIYQueryCQTTestTask(MainModel mm)
            : base(mm)
        { }
        public override bool IsNeedSetQueryCondition
        {
            get { return true; }
        }
        public override string Name
        {
            get { return "查询CQT测试计划"; }
        }

        public override string IconName
        {
            get { throw new NotImplementedException(); }
        }

        protected override bool isValidCondition()
        {
            return true;
        }
        
        protected override void query()
        {
            CQTPointManager.GetInstance();
            testTaskList.Clear();
            base.query();
            showResultForm();
        }

        protected virtual void showResultForm()
        {
            CQTTestTaskListForm taskForm = null;
            object o = MainModel.GetObjectFromBlackboard(typeof(CQTTestTaskListForm).Name);
            if (o is CQTTestTaskListForm)
            {
                taskForm = o as CQTTestTaskListForm;
            }
            if (taskForm==null||taskForm.IsDisposed)
            {
                taskForm = new CQTTestTaskListForm(MainModel);
            }
            taskForm.FillTestTask(testTaskList);
            if (!taskForm.Visible)
            {
                taskForm.Show(MainModel.MainForm);
            }
        }

        protected override string getSqlTextString()
        {
            StringBuilder sb = new StringBuilder();
            
            sb.Append("select b.[taskID],b.[taskName],b.[sTime],b.[eTime],b.[creatorID],b.[careerID],b.[serviceID],b.[agentID],d.[pointID],d.[planWorkLoad],d.[comment]");
            sb.Append(" from [tb_cqt_testtask] b join [tb_cqt_testtask_detail] d on  b.[taskID]=d.[taskID]");
            sb.Append(" where b.[creatorID]=" + MainModel.User.ID);
            sb.Append(" and b.[sTime]>=" + (int)(JavaDate.GetMilliseconds(condition.Periods[0].BeginTime) / 1000));
            sb.Append(" and b.[eTime]<=" + (int)(JavaDate.GetMilliseconds(condition.Periods[0].EndTime) / 1000));
            if (!MainModel.User.HasFunctionRight(406))//创建CQT测试任务
            {
                sb.Append(" and b.[agentID]=1");//user 和agentid 暂无匹配
            }
            return sb.ToString();
        }

        protected override MasterCom.RAMS.Model.Interface.E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[11];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_Int;
            rType[5] = E_VType.E_Int;
            rType[6] = E_VType.E_Int;
            rType[7] = E_VType.E_Int;
            rType[8] = E_VType.E_Int;
            rType[9] = E_VType.E_Int;
            rType[10] = E_VType.E_String;
            return rType;
        }

        private readonly List<CQTTestTask> testTaskList = new List<CQTTestTask>();
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CQTTestTask testTask = new CQTTestTask();
                    testTask.ID = new Guid(package.Content.GetParamString());
                    testTask.Name = package.Content.GetParamString();
                    testTask.BeginTime = package.Content.GetParameDateTimeByInt();
                    testTask.EndTime = package.Content.GetParameDateTimeByInt();
                    testTask.CreatorID = package.Content.GetParamInt();
                    testTask.CareerID = package.Content.GetParamInt();
                    testTask.ServiceID = package.Content.GetParamInt();
                    testTask.AgentID = package.Content.GetParamInt();
                    int pointID = package.Content.GetParamInt();
                    testTask.CQTPoint = CQTPointManager.GetInstance().GetPoint(pointID);
                    testTask.Target = TimeSpan.FromSeconds(package.Content.GetParamInt());//秒
                    testTask.Comment = package.Content.GetParamString();
                    testTaskList.Add(testTask);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

    }
}
