﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.FewAreaKPI
{
    public partial class FewAreaSettingDlg : BaseDialog
    {
        public FewAreaSettingDlg()
        {
            InitializeComponent();
            this.gridCtrlOptionalArea.DataSource = optionalAeas;
            this.gridCtrlSelected.DataSource = selAreas;
        }

         public FewAreaSettingDlg(TemplateMngr templateMngr)
            : this()
        {
            this.templateMngr = templateMngr;
            fillTemplate(null);
        }

        private List<AreaBase> optionalAeas = new List<AreaBase>();
        private List<AreaBase> selAreas = new List<AreaBase>();
        public List<AreaBase> SelectedAreas
        {
            get
            {
                return selAreas;
            }
            set
            {
                selAreas.Clear();
                if (value == null)
                {
                    return;
                }
                selAreas.AddRange(value);
                gridCtrlSelected.RefreshDataSource();
            }
        }

        TemplateMngr templateMngr;
        public AreaReportTemplate SelTemplate
        {
            get
            {
                return cbxTemplate.SelectedItem as AreaReportTemplate;
            }
        }

        private void fillTemplate(AreaReportTemplate selTemplate)
        {
            cbxTemplate.Properties.Items.Clear();
            foreach (AreaReportTemplate rpt in templateMngr.ReportTemplates)
            {
                cbxTemplate.Properties.Items.Add(rpt);
            }
            if (selTemplate != null)
            {
                cbxTemplate.SelectedItem = selTemplate;
            }
            else if (cbxTemplate.Properties.Items.Count > 0)
            {
                cbxTemplate.SelectedIndex = 0;
            }
        }

        private void btnTemplate_Click(object sender, EventArgs e)
        {
            AreaRptTemplateOptionDlg dlg = new AreaRptTemplateOptionDlg();
            dlg.SetTemplate(cbxTemplate.SelectedItem as AreaReportTemplate);
            dlg.ShowDialog();
            fillTemplate(dlg.CurTemplate);
        }

        private void btnFind_Click(object sender, EventArgs e)
        {
            filterArea();
        }

        private void txtBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                filterArea();
            }
        }

        private void filterArea()
        {
            string name = txtName.Text.Trim().ToUpper();
            string sn = txtSn.Text.Trim().ToUpper();
            if (name.Length == 0 && sn.Length == 0)
            {
                return;
            }
            optionalAeas.Clear();
            foreach (AreaBase area in ZTAreaManager.Instance.GetLowestAreas())
            {
                if ((name.Length != 0 && area.Name.ToUpper().IndexOf(name) == -1)
                    || (sn.Length != 0 && area.SN.ToUpper().IndexOf(sn) == -1))
                {
                    continue;
                }
                optionalAeas.Add(area);
            }
            gridCtrlOptionalArea.RefreshDataSource();
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (this.optionalAeas == null)
            {
                return;
            }
            foreach (AreaBase cell in optionalAeas)
            {
                if (!selAreas.Contains(cell))
                {
                    selAreas.Add(cell);
                }
            }
            optionalAeas.Clear();
            gridCtrlOptionalArea.RefreshDataSource();
            gridCtrlSelected.RefreshDataSource();
        }

        private void btnRemove_Click(object sender, EventArgs e)
        {
            foreach (AreaBase area in selAreas)
            {
                if (!optionalAeas.Contains(area))
                {
                    optionalAeas.Add(area);
                }
            }
            selAreas.Clear();
            gridCtrlOptionalArea.RefreshDataSource();
            gridCtrlSelected.RefreshDataSource();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (SelTemplate==null)
            {
                MessageBox.Show("报表模板不能为空！");
                return;
            }
            if (selAreas.Count==0)
            {
                MessageBox.Show("请至少选择一个村庄！");
                return;
            }
            DialogResult = DialogResult.OK;
        }

        private void gvOptionalArea_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gvOptionalArea.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }
            int idx = gvOptionalArea.GetDataSourceRowIndex(info.RowHandle);
            if (idx >= 0)
            {
                selAreas.Add(optionalAeas[idx]);
                optionalAeas.RemoveAt(idx);
                gridCtrlOptionalArea.RefreshDataSource();
                gridCtrlSelected.RefreshDataSource();
            }
        }

        private void gvSelected_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gvSelected.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }
            int idx = gvSelected.GetDataSourceRowIndex(info.RowHandle);
            if (idx >= 0)
            {
                optionalAeas.Add(this.selAreas[idx]);
                selAreas.RemoveAt(idx);
                gridCtrlOptionalArea.RefreshDataSource();
                gridCtrlSelected.RefreshDataSource();
            }
        }

        private void btnClearSel_Click(object sender, EventArgs e)
        {
            this.selAreas.Clear();
            gridCtrlSelected.RefreshDataSource();
        }

    }
}
