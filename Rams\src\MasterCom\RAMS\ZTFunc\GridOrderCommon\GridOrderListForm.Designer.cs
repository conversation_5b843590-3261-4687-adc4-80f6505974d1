﻿namespace MasterCom.RAMS.ZTFunc.GridOrderCommon
{
    partial class GridOrderListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.lv = new BrightIdeasSoftware.TreeListView();
            this.colDistrictName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colOrderTypeName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colTaskID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colStatusDesc = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colItemCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colAreaNames = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colRoadNames = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colGridSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLng = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLat = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLac = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colCi = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colIsProbCell = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colPrimaryCause = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colSpecificCuase = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colDetail = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colSuggest = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miShowAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.miReplayFile = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExportXls = new System.Windows.Forms.ToolStripMenuItem();
            this.tabCtrl = new System.Windows.Forms.TabControl();
            ((System.ComponentModel.ISupportInitialize)(this.lv)).BeginInit();
            this.ctxMenu.SuspendLayout();
            this.SuspendLayout();
            // 
            // lv
            // 
            this.lv.Activation = System.Windows.Forms.ItemActivation.OneClick;
            this.lv.AllColumns.Add(this.colDistrictName);
            this.lv.AllColumns.Add(this.colOrderTypeName);
            this.lv.AllColumns.Add(this.colTaskID);
            this.lv.AllColumns.Add(this.colStatusDesc);
            this.lv.AllColumns.Add(this.colItemCount);
            this.lv.AllColumns.Add(this.colAreaNames);
            this.lv.AllColumns.Add(this.colRoadNames);
            this.lv.AllColumns.Add(this.colFileName);
            this.lv.AllColumns.Add(this.colGridSN);
            this.lv.AllColumns.Add(this.colLng);
            this.lv.AllColumns.Add(this.colLat);
            this.lv.AllColumns.Add(this.colLac);
            this.lv.AllColumns.Add(this.colCi);
            this.lv.AllColumns.Add(this.colCellName);
            this.lv.AllColumns.Add(this.colIsProbCell);
            this.lv.AllColumns.Add(this.colPrimaryCause);
            this.lv.AllColumns.Add(this.colSpecificCuase);
            this.lv.AllColumns.Add(this.colDetail);
            this.lv.AllColumns.Add(this.colSuggest);
            this.lv.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.colDistrictName,
            this.colOrderTypeName,
            this.colTaskID,
            this.colStatusDesc,
            this.colItemCount,
            this.colAreaNames,
            this.colRoadNames,
            this.colFileName,
            this.colGridSN,
            this.colLng,
            this.colLat,
            this.colLac,
            this.colCi,
            this.colCellName,
            this.colIsProbCell,
            this.colPrimaryCause,
            this.colSpecificCuase,
            this.colDetail,
            this.colSuggest});
            this.lv.ContextMenuStrip = this.ctxMenu;
            this.lv.Cursor = System.Windows.Forms.Cursors.Default;
            this.lv.FullRowSelect = true;
            this.lv.GridLines = true;
            this.lv.HeaderWordWrap = true;
            this.lv.IsNeedShowOverlay = false;
            this.lv.Location = new System.Drawing.Point(118, 12);
            this.lv.Name = "lv";
            this.lv.OwnerDraw = true;
            this.lv.ShowGroups = false;
            this.lv.ShowItemToolTips = true;
            this.lv.Size = new System.Drawing.Size(949, 413);
            this.lv.TabIndex = 6;
            this.lv.UseCompatibleStateImageBehavior = false;
            this.lv.View = System.Windows.Forms.View.Details;
            this.lv.VirtualMode = true;
            this.lv.Visible = false;
            // 
            // colDistrictName
            // 
            this.colDistrictName.HeaderFont = null;
            this.colDistrictName.Text = "地市";
            this.colDistrictName.Width = 43;
            // 
            // colOrderTypeName
            // 
            this.colOrderTypeName.HeaderFont = null;
            this.colOrderTypeName.Text = "工单类型";
            this.colOrderTypeName.Width = 124;
            // 
            // colTaskID
            // 
            this.colTaskID.HeaderFont = null;
            this.colTaskID.MinimumWidth = 60;
            this.colTaskID.Text = "工单ID";
            // 
            // colStatusDesc
            // 
            this.colStatusDesc.HeaderFont = null;
            this.colStatusDesc.MinimumWidth = 60;
            this.colStatusDesc.Text = "状态";
            // 
            // colItemCount
            // 
            this.colItemCount.HeaderFont = null;
            this.colItemCount.Text = "栅格个数";
            this.colItemCount.Width = 57;
            // 
            // colAreaNames
            // 
            this.colAreaNames.HeaderFont = null;
            this.colAreaNames.MinimumWidth = 100;
            this.colAreaNames.Text = "区域";
            this.colAreaNames.Width = 200;
            // 
            // colRoadNames
            // 
            this.colRoadNames.HeaderFont = null;
            this.colRoadNames.MinimumWidth = 100;
            this.colRoadNames.Text = "道路";
            this.colRoadNames.Width = 200;
            // 
            // colFileName
            // 
            this.colFileName.HeaderFont = null;
            this.colFileName.Text = "文件名";
            // 
            // colGridSN
            // 
            this.colGridSN.HeaderFont = null;
            this.colGridSN.Text = "栅格编号";
            // 
            // colLng
            // 
            this.colLng.HeaderFont = null;
            this.colLng.MinimumWidth = 60;
            this.colLng.Text = "栅格左上经度";
            this.colLng.Width = 120;
            // 
            // colLat
            // 
            this.colLat.HeaderFont = null;
            this.colLat.MinimumWidth = 60;
            this.colLat.Text = "栅格左上纬度";
            this.colLat.Width = 120;
            // 
            // colLac
            // 
            this.colLac.HeaderFont = null;
            this.colLac.Text = "LAC/TAC";
            // 
            // colCi
            // 
            this.colCi.HeaderFont = null;
            this.colCi.Text = "CI/ECI";
            // 
            // colCellName
            // 
            this.colCellName.HeaderFont = null;
            this.colCellName.Text = "小区名称";
            // 
            // colIsProbCell
            // 
            this.colIsProbCell.HeaderFont = null;
            this.colIsProbCell.Text = "是否问题小区";
            // 
            // colPrimaryCause
            // 
            this.colPrimaryCause.HeaderFont = null;
            this.colPrimaryCause.Text = "预判主因";
            // 
            // colSpecificCuase
            // 
            this.colSpecificCuase.HeaderFont = null;
            this.colSpecificCuase.Text = "预判具因";
            // 
            // colDetail
            // 
            this.colDetail.HeaderFont = null;
            this.colDetail.Text = "预判详情";
            // 
            // colSuggest
            // 
            this.colSuggest.HeaderFont = null;
            this.colSuggest.Text = "优化建议";
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miShowAll,
            this.miExpandAll,
            this.miCollapseAll,
            this.toolStripSeparator2,
            this.miReplayFile,
            this.toolStripSeparator1,
            this.miExportXls});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(173, 126);
            this.ctxMenu.Opening += new System.ComponentModel.CancelEventHandler(this.ctxMenu_Opening);
            // 
            // miShowAll
            // 
            this.miShowAll.Name = "miShowAll";
            this.miShowAll.Size = new System.Drawing.Size(172, 22);
            this.miShowAll.Text = "显示所有问题栅格";
            this.miShowAll.Click += new System.EventHandler(this.miShowAll_Click);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(172, 22);
            this.miExpandAll.Text = "展开所有节点";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCollapseAll
            // 
            this.miCollapseAll.Name = "miCollapseAll";
            this.miCollapseAll.Size = new System.Drawing.Size(172, 22);
            this.miCollapseAll.Text = "收缩所有节点";
            this.miCollapseAll.Click += new System.EventHandler(this.miCollapseAll_Click);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(169, 6);
            // 
            // miReplayFile
            // 
            this.miReplayFile.Name = "miReplayFile";
            this.miReplayFile.Size = new System.Drawing.Size(172, 22);
            this.miReplayFile.Text = "回放文件";
            this.miReplayFile.Click += new System.EventHandler(this.miReplayFile_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(169, 6);
            // 
            // miExportXls
            // 
            this.miExportXls.Name = "miExportXls";
            this.miExportXls.Size = new System.Drawing.Size(172, 22);
            this.miExportXls.Text = "导出Excel...";
            this.miExportXls.Click += new System.EventHandler(this.miExportXls_Click);
            // 
            // tabCtrl
            // 
            this.tabCtrl.Location = new System.Drawing.Point(46, 96);
            this.tabCtrl.Name = "tabCtrl";
            this.tabCtrl.SelectedIndex = 0;
            this.tabCtrl.Size = new System.Drawing.Size(513, 315);
            this.tabCtrl.TabIndex = 7;
            // 
            // GridOrderListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(949, 413);
            this.Controls.Add(this.tabCtrl);
            this.Controls.Add(this.lv);
            this.Name = "GridOrderListForm";
            this.Text = "栅格工单列表";
            ((System.ComponentModel.ISupportInitialize)(this.lv)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView lv;
        private BrightIdeasSoftware.OLVColumn colDistrictName;
        private BrightIdeasSoftware.OLVColumn colOrderTypeName;
        private BrightIdeasSoftware.OLVColumn colTaskID;
        private BrightIdeasSoftware.OLVColumn colStatusDesc;
        private BrightIdeasSoftware.OLVColumn colItemCount;
        private BrightIdeasSoftware.OLVColumn colAreaNames;
        private BrightIdeasSoftware.OLVColumn colRoadNames;
        private BrightIdeasSoftware.OLVColumn colLng;
        private BrightIdeasSoftware.OLVColumn colLat;
        private BrightIdeasSoftware.OLVColumn colLac;
        private BrightIdeasSoftware.OLVColumn colCi;
        private BrightIdeasSoftware.OLVColumn colCellName;
        private BrightIdeasSoftware.OLVColumn colIsProbCell;
        private BrightIdeasSoftware.OLVColumn colPrimaryCause;
        private BrightIdeasSoftware.OLVColumn colSpecificCuase;
        private BrightIdeasSoftware.OLVColumn colDetail;
        private BrightIdeasSoftware.OLVColumn colSuggest;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miShowAll;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem miExportXls;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll;
        private System.Windows.Forms.TabControl tabCtrl;
        private BrightIdeasSoftware.OLVColumn colFileName;
        private BrightIdeasSoftware.OLVColumn colGridSN;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripMenuItem miReplayFile;
    }
}