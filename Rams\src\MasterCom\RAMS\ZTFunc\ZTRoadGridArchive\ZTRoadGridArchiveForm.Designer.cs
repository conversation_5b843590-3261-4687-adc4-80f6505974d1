﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTRoadGridArchiveForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.gridControlDetail = new DevExpress.XtraGrid.GridControl();
            this.bandedGvRoad = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand1 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand2 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand3 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn7 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand4 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn8 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand5 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn19 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand6 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn36 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn39 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn40 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn42 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn44 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.panel1 = new System.Windows.Forms.Panel();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.btnRangeSetting = new DevExpress.XtraEditors.SimpleButton();
            this.cmbRendingIndex = new DevExpress.XtraEditors.ComboBoxEdit();
            this.splitContainerControl2 = new DevExpress.XtraEditors.SplitContainerControl();
            this.groupCur = new DevExpress.XtraEditors.GroupControl();
            this.gridControlCur = new DevExpress.XtraGrid.GridControl();
            this.gvCurRound = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.groupHis = new DevExpress.XtraEditors.GroupControl();
            this.gridControlHis = new DevExpress.XtraGrid.GridControl();
            this.gvHisRound = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlSum = new DevExpress.XtraGrid.GridControl();
            this.gvSum = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridColumn15 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumnSTime = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumnETime = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumnRound = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumnRoundCount = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn5 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn6 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn7 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn8 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn9 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripExport = new System.Windows.Forms.ToolStripMenuItem();
            this.gridBand7 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand8 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand9 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand10 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand11 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlDetail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGvRoad)).BeginInit();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbRendingIndex.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).BeginInit();
            this.splitContainerControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupCur)).BeginInit();
            this.groupCur.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCur)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvCurRound)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupHis)).BeginInit();
            this.groupHis.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlHis)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvHisRound)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlSum)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvSum)).BeginInit();
            this.xtraTabPage2.SuspendLayout();
            this.contextMenuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.gridControlDetail);
            this.splitContainerControl1.Panel1.Controls.Add(this.panel1);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.splitContainerControl2);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1186, 523);
            this.splitContainerControl1.SplitterPosition = 310;
            this.splitContainerControl1.TabIndex = 10;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // gridControlDetail
            // 
            this.gridControlDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlDetail.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlDetail.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlDetail.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlDetail.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlDetail.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlDetail.Location = new System.Drawing.Point(0, 41);
            this.gridControlDetail.MainView = this.bandedGvRoad;
            this.gridControlDetail.Name = "gridControlDetail";
            this.gridControlDetail.ShowOnlyPredefinedDetails = true;
            this.gridControlDetail.Size = new System.Drawing.Size(1186, 269);
            this.gridControlDetail.TabIndex = 9;
            this.gridControlDetail.UseEmbeddedNavigator = true;
            this.gridControlDetail.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGvRoad});
            // 
            // bandedGvRoad
            // 
            this.bandedGvRoad.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand1,
            this.gridBand2,
            this.gridBand3,
            this.gridBand4,
            this.gridBand5,
            this.gridBand6});
            this.bandedGvRoad.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn24,
            this.gridColumn25,
            this.gridColumn26,
            this.gridColumn36,
            this.gridColumn37,
            this.gridColumn38,
            this.gridColumn39,
            this.gridColumn40,
            this.gridColumn41,
            this.gridColumn42,
            this.gridColumn43,
            this.gridColumn44});
            this.bandedGvRoad.GridControl = this.gridControlDetail;
            this.bandedGvRoad.IndicatorWidth = 40;
            this.bandedGvRoad.Name = "bandedGvRoad";
            this.bandedGvRoad.OptionsBehavior.Editable = false;
            this.bandedGvRoad.OptionsDetail.ShowDetailTabs = false;
            this.bandedGvRoad.OptionsView.ColumnAutoWidth = false;
            this.bandedGvRoad.OptionsView.ShowGroupPanel = false;
            this.bandedGvRoad.OptionsView.ShowIndicator = false;
            this.bandedGvRoad.Click += new System.EventHandler(this.bandedGvRoad_Click);
            // 
            // gridBand1
            // 
            this.gridBand1.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand1.Caption = "道路";
            this.gridBand1.Columns.Add(this.gridColumn1);
            this.gridBand1.Name = "gridBand1";
            this.gridBand1.Width = 80;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "道路名";
            this.gridColumn1.FieldName = "RoadName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.Width = 80;
            // 
            // gridBand2
            // 
            this.gridBand2.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand2.Caption = "100米路段";
            this.gridBand2.Columns.Add(this.gridColumn2);
            this.gridBand2.Columns.Add(this.gridColumn3);
            this.gridBand2.Columns.Add(this.gridColumn4);
            this.gridBand2.Columns.Add(this.gridColumn5);
            this.gridBand2.Name = "gridBand2";
            this.gridBand2.Width = 335;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "路段标识";
            this.gridColumn2.FieldName = "AreaID";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "路段名";
            this.gridColumn3.FieldName = "AreaName";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.Width = 100;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "经度";
            this.gridColumn4.FieldName = "CenterLongitude";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.Width = 80;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "纬度";
            this.gridColumn5.FieldName = "CenterLatitude";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.Width = 80;
            // 
            // gridBand3
            // 
            this.gridBand3.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand3.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand3.Caption = "区域";
            this.gridBand3.Columns.Add(this.gridColumn7);
            this.gridBand3.Columns.Add(this.gridColumn6);
            this.gridBand3.Name = "gridBand3";
            this.gridBand3.Width = 150;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "类型";
            this.gridColumn7.FieldName = "AreaTypeName";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "分公司";
            this.gridColumn6.FieldName = "CompanyName";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            // 
            // gridBand4
            // 
            this.gridBand4.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand4.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand4.Caption = "占用小区数";
            this.gridBand4.Columns.Add(this.gridColumn8);
            this.gridBand4.Name = "gridBand4";
            this.gridBand4.Width = 85;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "占用小区数";
            this.gridColumn8.FieldName = "CellCount";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.Width = 85;
            // 
            // gridBand5
            // 
            this.gridBand5.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand5.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand5.Caption = "异常事件";
            this.gridBand5.Columns.Add(this.gridColumn19);
            this.gridBand5.Columns.Add(this.gridColumn20);
            this.gridBand5.Columns.Add(this.gridColumn21);
            this.gridBand5.Columns.Add(this.gridColumn22);
            this.gridBand5.Columns.Add(this.gridColumn23);
            this.gridBand5.Columns.Add(this.gridColumn24);
            this.gridBand5.Columns.Add(this.gridColumn25);
            this.gridBand5.Columns.Add(this.gridColumn26);
            this.gridBand5.Name = "gridBand5";
            this.gridBand5.Width = 540;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "异常事件总次数";
            this.gridColumn19.FieldName = "CurEventRoadInfo.TotalEventNum";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.Width = 105;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "未接通";
            this.gridColumn20.FieldName = "CurEventRoadInfo.NotConnected";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.Width = 60;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "掉话";
            this.gridColumn21.FieldName = "CurEventRoadInfo.DroppedCalls";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.Width = 50;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "MOS3.0差";
            this.gridColumn22.FieldName = "CurEventRoadInfo.WeakMos";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "RSRP差";
            this.gridColumn23.FieldName = "CurEventRoadInfo.WeakRsrp";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.Width = 60;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "SINR差";
            this.gridColumn24.FieldName = "CurEventRoadInfo.WeakSinr";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.Width = 60;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "RRC重建";
            this.gridColumn25.FieldName = "CurEventRoadInfo.RRCRebuild";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.Width = 70;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "速率低";
            this.gridColumn26.FieldName = "CurEventRoadInfo.Downless";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.Width = 60;
            // 
            // gridBand6
            // 
            this.gridBand6.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand6.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand6.Caption = "网格根因";
            this.gridBand6.Columns.Add(this.gridColumn36);
            this.gridBand6.Columns.Add(this.gridColumn37);
            this.gridBand6.Columns.Add(this.gridColumn38);
            this.gridBand6.Columns.Add(this.gridColumn39);
            this.gridBand6.Columns.Add(this.gridColumn40);
            this.gridBand6.Columns.Add(this.gridColumn41);
            this.gridBand6.Columns.Add(this.gridColumn42);
            this.gridBand6.Columns.Add(this.gridColumn43);
            this.gridBand6.Columns.Add(this.gridColumn44);
            this.gridBand6.Name = "gridBand6";
            this.gridBand6.Width = 675;
            // 
            // gridColumn36
            // 
            this.gridColumn36.Caption = "弱覆盖";
            this.gridColumn36.FieldName = "CurRoadReasonInfo.WeakCover";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.Visible = true;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "模三干扰";
            this.gridColumn37.FieldName = "CurRoadReasonInfo.ModRoad";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.Visible = true;
            // 
            // gridColumn38
            // 
            this.gridColumn38.Caption = "同频干扰";
            this.gridColumn38.FieldName = "CurRoadReasonInfo.CoFreq";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.Visible = true;
            // 
            // gridColumn39
            // 
            this.gridColumn39.Caption = "重叠覆盖";
            this.gridColumn39.FieldName = "CurRoadReasonInfo.MultiCoverage";
            this.gridColumn39.Name = "gridColumn39";
            this.gridColumn39.Visible = true;
            // 
            // gridColumn40
            // 
            this.gridColumn40.Caption = "过覆盖";
            this.gridColumn40.FieldName = "CurRoadReasonInfo.CoverLap";
            this.gridColumn40.Name = "gridColumn40";
            this.gridColumn40.Visible = true;
            // 
            // gridColumn41
            // 
            this.gridColumn41.Caption = "小区故障";
            this.gridColumn41.FieldName = "CurRoadReasonInfo.CellFailure";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.Visible = true;
            // 
            // gridColumn42
            // 
            this.gridColumn42.Caption = "断站";
            this.gridColumn42.FieldName = "CurRoadReasonInfo.BtsInterruption";
            this.gridColumn42.Name = "gridColumn42";
            this.gridColumn42.Visible = true;
            // 
            // gridColumn43
            // 
            this.gridColumn43.Caption = "拆站";
            this.gridColumn43.FieldName = "CurRoadReasonInfo.BtsTearDown";
            this.gridColumn43.Name = "gridColumn43";
            this.gridColumn43.Visible = true;
            // 
            // gridColumn44
            // 
            this.gridColumn44.Caption = "容量";
            this.gridColumn44.FieldName = "CurRoadReasonInfo.LoadProblem";
            this.gridColumn44.Name = "gridColumn44";
            this.gridColumn44.Visible = true;
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.labelControl1);
            this.panel1.Controls.Add(this.btnRangeSetting);
            this.panel1.Controls.Add(this.cmbRendingIndex);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(1186, 41);
            this.panel1.TabIndex = 13;
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(16, 13);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(76, 14);
            this.labelControl1.TabIndex = 11;
            this.labelControl1.Text = "渲染指标选择:";
            // 
            // btnRangeSetting
            // 
            this.btnRangeSetting.Location = new System.Drawing.Point(263, 8);
            this.btnRangeSetting.Name = "btnRangeSetting";
            this.btnRangeSetting.Size = new System.Drawing.Size(85, 23);
            this.btnRangeSetting.TabIndex = 12;
            this.btnRangeSetting.Text = "渲染指标设置";
            this.btnRangeSetting.Click += new System.EventHandler(this.btnRangeSetting_Click);
            // 
            // cmbRendingIndex
            // 
            this.cmbRendingIndex.Location = new System.Drawing.Point(98, 10);
            this.cmbRendingIndex.Name = "cmbRendingIndex";
            this.cmbRendingIndex.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cmbRendingIndex.Size = new System.Drawing.Size(152, 21);
            this.cmbRendingIndex.TabIndex = 10;
            // 
            // splitContainerControl2
            // 
            this.splitContainerControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl2.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl2.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl2.Name = "splitContainerControl2";
            this.splitContainerControl2.Panel1.Controls.Add(this.groupCur);
            this.splitContainerControl2.Panel1.Text = "Panel1";
            this.splitContainerControl2.Panel2.Controls.Add(this.groupHis);
            this.splitContainerControl2.Panel2.Text = "Panel2";
            this.splitContainerControl2.Size = new System.Drawing.Size(1186, 207);
            this.splitContainerControl2.SplitterPosition = 590;
            this.splitContainerControl2.TabIndex = 0;
            this.splitContainerControl2.Text = "splitContainerControl2";
            // 
            // groupCur
            // 
            this.groupCur.Controls.Add(this.gridControlCur);
            this.groupCur.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupCur.Location = new System.Drawing.Point(0, 0);
            this.groupCur.Name = "groupCur";
            this.groupCur.Size = new System.Drawing.Size(590, 207);
            this.groupCur.TabIndex = 11;
            this.groupCur.Text = "本次占用小区";
            // 
            // gridControlCur
            // 
            this.gridControlCur.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlCur.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlCur.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlCur.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlCur.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlCur.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlCur.Location = new System.Drawing.Point(2, 23);
            this.gridControlCur.MainView = this.gvCurRound;
            this.gridControlCur.Name = "gridControlCur";
            this.gridControlCur.ShowOnlyPredefinedDetails = true;
            this.gridControlCur.Size = new System.Drawing.Size(586, 182);
            this.gridControlCur.TabIndex = 12;
            this.gridControlCur.UseEmbeddedNavigator = true;
            this.gridControlCur.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvCurRound});
            // 
            // gvCurRound
            // 
            this.gvCurRound.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11});
            this.gvCurRound.GridControl = this.gridControlCur;
            this.gvCurRound.Name = "gvCurRound";
            this.gvCurRound.OptionsBehavior.Editable = false;
            this.gvCurRound.OptionsDetail.ShowDetailTabs = false;
            this.gvCurRound.OptionsView.ShowGroupPanel = false;
            this.gvCurRound.OptionsView.ShowIndicator = false;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "小区序号";
            this.gridColumn9.FieldName = "SN";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 0;
            this.gridColumn9.Width = 100;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "占用小区";
            this.gridColumn10.FieldName = "CellName";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 1;
            this.gridColumn10.Width = 269;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "占用次数";
            this.gridColumn11.FieldName = "Times";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 2;
            this.gridColumn11.Width = 100;
            // 
            // groupHis
            // 
            this.groupHis.Controls.Add(this.gridControlHis);
            this.groupHis.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupHis.Location = new System.Drawing.Point(0, 0);
            this.groupHis.Name = "groupHis";
            this.groupHis.Size = new System.Drawing.Size(590, 207);
            this.groupHis.TabIndex = 12;
            this.groupHis.Text = "往次占用小区";
            // 
            // gridControlHis
            // 
            this.gridControlHis.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlHis.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlHis.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlHis.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlHis.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlHis.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlHis.Location = new System.Drawing.Point(2, 23);
            this.gridControlHis.MainView = this.gvHisRound;
            this.gridControlHis.Name = "gridControlHis";
            this.gridControlHis.ShowOnlyPredefinedDetails = true;
            this.gridControlHis.Size = new System.Drawing.Size(586, 182);
            this.gridControlHis.TabIndex = 10;
            this.gridControlHis.UseEmbeddedNavigator = true;
            this.gridControlHis.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvHisRound});
            // 
            // gvHisRound
            // 
            this.gvHisRound.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14});
            this.gvHisRound.GridControl = this.gridControlHis;
            this.gvHisRound.Name = "gvHisRound";
            this.gvHisRound.OptionsBehavior.Editable = false;
            this.gvHisRound.OptionsDetail.ShowDetailTabs = false;
            this.gvHisRound.OptionsView.ShowGroupPanel = false;
            this.gvHisRound.OptionsView.ShowIndicator = false;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "小区序号";
            this.gridColumn12.FieldName = "SN";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 0;
            this.gridColumn12.Width = 100;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "占用小区";
            this.gridColumn13.FieldName = "CellName";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 1;
            this.gridColumn13.Width = 211;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "占用次数";
            this.gridColumn14.FieldName = "Times";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 2;
            this.gridColumn14.Width = 100;
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(1193, 553);
            this.xtraTabControl1.TabIndex = 11;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.gridControlSum);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(1186, 523);
            this.xtraTabPage1.Text = "汇总";
            // 
            // gridControlSum
            // 
            this.gridControlSum.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlSum.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gridControlSum.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gridControlSum.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gridControlSum.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gridControlSum.EmbeddedNavigator.Buttons.Remove.Visible = false;
            this.gridControlSum.Location = new System.Drawing.Point(0, 0);
            this.gridControlSum.MainView = this.gvSum;
            this.gridControlSum.Name = "gridControlSum";
            this.gridControlSum.ShowOnlyPredefinedDetails = true;
            this.gridControlSum.Size = new System.Drawing.Size(1186, 523);
            this.gridControlSum.TabIndex = 10;
            this.gridControlSum.UseEmbeddedNavigator = true;
            this.gridControlSum.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvSum});
            // 
            // gvSum
            // 
            this.gvSum.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand7,
            this.gridBand8,
            this.gridBand9,
            this.gridBand10,
            this.gridBand11});
            this.gvSum.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumnSTime,
            this.gridColumnETime,
            this.gridColumnRound,
            this.gridColumnRoundCount,
            this.gridColumn27,
            this.gridColumn28,
            this.gridColumn29,
            this.gridColumn30,
            this.gridColumn31,
            this.gridColumn32,
            this.gridColumn33,
            this.gridColumn34,
            this.gridColumn35,
            this.bandedGridColumn1,
            this.bandedGridColumn2,
            this.bandedGridColumn3,
            this.bandedGridColumn4,
            this.bandedGridColumn5,
            this.bandedGridColumn6,
            this.bandedGridColumn7,
            this.bandedGridColumn8,
            this.bandedGridColumn9});
            this.gvSum.GridControl = this.gridControlSum;
            this.gvSum.IndicatorWidth = 40;
            this.gvSum.Name = "gvSum";
            this.gvSum.OptionsBehavior.Editable = false;
            this.gvSum.OptionsDetail.ShowDetailTabs = false;
            this.gvSum.OptionsView.ColumnAutoWidth = false;
            this.gvSum.OptionsView.ShowGroupPanel = false;
            this.gvSum.OptionsView.ShowIndicator = false;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "道路名";
            this.gridColumn15.FieldName = "RoadName";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.Width = 66;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "道路长度(KM)";
            this.gridColumn16.FieldName = "RoadLength";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.Width = 93;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "分公司";
            this.gridColumn17.FieldName = "CompanyName";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "类型";
            this.gridColumn18.FieldName = "AreaTypeName";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.Width = 62;
            // 
            // gridColumnSTime
            // 
            this.gridColumnSTime.Caption = "起始时间";
            this.gridColumnSTime.FieldName = "StartDate";
            this.gridColumnSTime.Name = "gridColumnSTime";
            this.gridColumnSTime.Visible = true;
            this.gridColumnSTime.Width = 102;
            // 
            // gridColumnETime
            // 
            this.gridColumnETime.Caption = "结束时间";
            this.gridColumnETime.FieldName = "EndDate";
            this.gridColumnETime.Name = "gridColumnETime";
            this.gridColumnETime.Visible = true;
            this.gridColumnETime.Width = 102;
            // 
            // gridColumnRound
            // 
            this.gridColumnRound.Caption = "测试轮次";
            this.gridColumnRound.FieldName = "Round";
            this.gridColumnRound.Name = "gridColumnRound";
            this.gridColumnRound.Visible = true;
            this.gridColumnRound.Width = 72;
            // 
            // gridColumnRoundCount
            // 
            this.gridColumnRoundCount.Caption = "轮次数";
            this.gridColumnRoundCount.FieldName = "RoundCount";
            this.gridColumnRoundCount.Name = "gridColumnRoundCount";
            this.gridColumnRoundCount.Visible = true;
            this.gridColumnRoundCount.Width = 55;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "占用小区数量";
            this.gridColumn27.FieldName = "CellCount";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.Width = 93;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "异常事件总次数";
            this.gridColumn28.FieldName = "EventRoadInfo.TotalEventNum";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.Width = 105;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "未接通";
            this.gridColumn29.FieldName = "EventRoadInfo.NotConnected";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.Width = 60;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "掉话";
            this.gridColumn30.FieldName = "EventRoadInfo.DroppedCalls";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.Width = 46;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "MOS3.0差";
            this.gridColumn31.FieldName = "EventRoadInfo.WeakMos";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.Width = 82;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "RSRP差";
            this.gridColumn32.FieldName = "EventRoadInfo.WeakRsrp";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.Width = 72;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "SINR差";
            this.gridColumn33.FieldName = "EventRoadInfo.WeakSinr";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.Width = 67;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "RRC重建";
            this.gridColumn34.FieldName = "EventRoadInfo.RRCRebuild";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.Width = 71;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "速率低";
            this.gridColumn35.FieldName = "EventRoadInfo.Downless";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.Width = 63;
            // 
            // bandedGridColumn1
            // 
            this.bandedGridColumn1.Caption = "弱覆盖";
            this.bandedGridColumn1.FieldName = "RoadReasonInfo.WeakCover";
            this.bandedGridColumn1.Name = "bandedGridColumn1";
            this.bandedGridColumn1.Visible = true;
            // 
            // bandedGridColumn2
            // 
            this.bandedGridColumn2.Caption = "模三干扰";
            this.bandedGridColumn2.FieldName = "RoadReasonInfo.ModRoad";
            this.bandedGridColumn2.Name = "bandedGridColumn2";
            this.bandedGridColumn2.Visible = true;
            // 
            // bandedGridColumn3
            // 
            this.bandedGridColumn3.Caption = "同频干扰";
            this.bandedGridColumn3.FieldName = "RoadReasonInfo.CoFreq";
            this.bandedGridColumn3.Name = "bandedGridColumn3";
            this.bandedGridColumn3.Visible = true;
            // 
            // bandedGridColumn4
            // 
            this.bandedGridColumn4.Caption = "重叠覆盖";
            this.bandedGridColumn4.FieldName = "RoadReasonInfo.MultiCoverage";
            this.bandedGridColumn4.Name = "bandedGridColumn4";
            this.bandedGridColumn4.Visible = true;
            // 
            // bandedGridColumn5
            // 
            this.bandedGridColumn5.Caption = "过覆盖";
            this.bandedGridColumn5.FieldName = "RoadReasonInfo.CoverLap";
            this.bandedGridColumn5.Name = "bandedGridColumn5";
            this.bandedGridColumn5.Visible = true;
            // 
            // bandedGridColumn6
            // 
            this.bandedGridColumn6.Caption = "小区故障";
            this.bandedGridColumn6.FieldName = "RoadReasonInfo.CellFailure";
            this.bandedGridColumn6.Name = "bandedGridColumn6";
            this.bandedGridColumn6.Visible = true;
            // 
            // bandedGridColumn7
            // 
            this.bandedGridColumn7.Caption = "断站";
            this.bandedGridColumn7.FieldName = "RoadReasonInfo.BtsInterruption";
            this.bandedGridColumn7.Name = "bandedGridColumn7";
            this.bandedGridColumn7.Visible = true;
            // 
            // bandedGridColumn8
            // 
            this.bandedGridColumn8.Caption = "拆站";
            this.bandedGridColumn8.FieldName = "RoadReasonInfo.BtsTearDown";
            this.bandedGridColumn8.Name = "bandedGridColumn8";
            this.bandedGridColumn8.Visible = true;
            // 
            // bandedGridColumn9
            // 
            this.bandedGridColumn9.Caption = "容量";
            this.bandedGridColumn9.FieldName = "RoadReasonInfo.LoadProblem";
            this.bandedGridColumn9.Name = "bandedGridColumn9";
            this.bandedGridColumn9.Visible = true;
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.splitContainerControl1);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(1186, 523);
            this.xtraTabPage2.Text = "详情";
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripExport});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(130, 26);
            // 
            // ToolStripExport
            // 
            this.ToolStripExport.Name = "ToolStripExport";
            this.ToolStripExport.Size = new System.Drawing.Size(129, 22);
            this.ToolStripExport.Text = "导出Excel";
            this.ToolStripExport.Click += new System.EventHandler(this.ToolStripExport_Click);
            // 
            // gridBand7
            // 
            this.gridBand7.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand7.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand7.Caption = "道路信息";
            this.gridBand7.Columns.Add(this.gridColumn15);
            this.gridBand7.Columns.Add(this.gridColumn16);
            this.gridBand7.Columns.Add(this.gridColumn17);
            this.gridBand7.Columns.Add(this.gridColumn18);
            this.gridBand7.Name = "gridBand7";
            this.gridBand7.Width = 296;
            // 
            // gridBand8
            // 
            this.gridBand8.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand8.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand8.Caption = "轮次信息";
            this.gridBand8.Columns.Add(this.gridColumnSTime);
            this.gridBand8.Columns.Add(this.gridColumnETime);
            this.gridBand8.Columns.Add(this.gridColumnRound);
            this.gridBand8.Columns.Add(this.gridColumnRoundCount);
            this.gridBand8.Name = "gridBand8";
            this.gridBand8.Width = 331;
            // 
            // gridBand9
            // 
            this.gridBand9.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand9.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand9.Caption = "占用小区信息";
            this.gridBand9.Columns.Add(this.gridColumn27);
            this.gridBand9.Name = "gridBand9";
            this.gridBand9.Width = 93;
            // 
            // gridBand10
            // 
            this.gridBand10.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand10.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand10.Caption = "异常事件";
            this.gridBand10.Columns.Add(this.gridColumn28);
            this.gridBand10.Columns.Add(this.gridColumn29);
            this.gridBand10.Columns.Add(this.gridColumn30);
            this.gridBand10.Columns.Add(this.gridColumn31);
            this.gridBand10.Columns.Add(this.gridColumn32);
            this.gridBand10.Columns.Add(this.gridColumn33);
            this.gridBand10.Columns.Add(this.gridColumn34);
            this.gridBand10.Columns.Add(this.gridColumn35);
            this.gridBand10.Name = "gridBand10";
            this.gridBand10.Width = 566;
            // 
            // gridBand11
            // 
            this.gridBand11.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand11.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand11.Caption = "网络根因";
            this.gridBand11.Columns.Add(this.bandedGridColumn1);
            this.gridBand11.Columns.Add(this.bandedGridColumn2);
            this.gridBand11.Columns.Add(this.bandedGridColumn3);
            this.gridBand11.Columns.Add(this.bandedGridColumn4);
            this.gridBand11.Columns.Add(this.bandedGridColumn5);
            this.gridBand11.Columns.Add(this.bandedGridColumn6);
            this.gridBand11.Columns.Add(this.bandedGridColumn7);
            this.gridBand11.Columns.Add(this.bandedGridColumn8);
            this.gridBand11.Columns.Add(this.bandedGridColumn9);
            this.gridBand11.Name = "gridBand11";
            this.gridBand11.Width = 675;
            // 
            // ZTRoadGridArchiveForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1193, 553);
            this.ContextMenuStrip = this.contextMenuStrip1;
            this.Controls.Add(this.xtraTabControl1);
            this.Name = "ZTRoadGridArchiveForm";
            this.Text = "道路栅格档案库结果";
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlDetail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGvRoad)).EndInit();
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbRendingIndex.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl2)).EndInit();
            this.splitContainerControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupCur)).EndInit();
            this.groupCur.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlCur)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvCurRound)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupHis)).EndInit();
            this.groupHis.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlHis)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvHisRound)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlSum)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvSum)).EndInit();
            this.xtraTabPage2.ResumeLayout(false);
            this.contextMenuStrip1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl2;
        private DevExpress.XtraEditors.GroupControl groupCur;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraEditors.GroupControl groupHis;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.GridControl gridControlHis;
        private DevExpress.XtraGrid.Views.Grid.GridView gvHisRound;
        private DevExpress.XtraGrid.GridControl gridControlCur;
        private DevExpress.XtraGrid.Views.Grid.GridView gvCurRound;
        private DevExpress.XtraGrid.GridControl gridControlDetail;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraGrid.GridControl gridControlSum;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem ToolStripExport;
        private DevExpress.XtraEditors.ComboBoxEdit cmbRendingIndex;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SimpleButton btnRangeSetting;
        private System.Windows.Forms.Panel panel1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGvRoad;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn1;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn5;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn7;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn6;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn8;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand5;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn19;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn20;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn21;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn22;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn23;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn24;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn25;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn26;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand6;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn36;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn37;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn38;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn39;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn40;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn41;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn42;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn43;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn44;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView gvSum;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn15;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn16;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn17;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn18;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumnSTime;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumnETime;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumnRound;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumnRoundCount;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn27;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn28;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn29;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn30;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn31;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn32;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn33;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn34;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn35;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn5;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn6;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn7;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn8;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn9;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand7;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand8;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand9;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand10;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand11;
    }
}