﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTReportEventMng
{
    public class InsertRecordsIntoDB : DIYSQLBase
    {
        protected List<EventLogGSM> gsmRecords;
        protected List<EventLogTD> tdRecords;
        public InsertRecordsIntoDB(List<EventLogGSM> gsmRecords, List<EventLogTD> tdRecords)
            : base(MasterCom.RAMS.Model.MainModel.GetInstance())
        {
            this.gsmRecords = gsmRecords;
            this.tdRecords = tdRecords;
        }

        protected override void query()
        {
            if (gsmRecords != null && gsmRecords.Count > 0)
            {
                List<int> idSet = new List<int>(gsmRecords.Count);
                foreach (EventLogGSM log in gsmRecords)
                {
                    idSet.Add(log.SN);
                }
                idSet.Sort();
                string info = containsSN(idSet, 1);
                if (!string.IsNullOrEmpty(info))
                {
                    MessageBox.Show(info);
                    return;
                }
            }
            if (tdRecords != null && tdRecords.Count > 0)
            {
                List<int> idSet = new List<int>(tdRecords.Count);
                foreach (EventLogTD log in tdRecords)
                {
                    idSet.Add(log.SN);
                }
                idSet.Sort();
                string info = containsSN(idSet, 2);
                if (!string.IsNullOrEmpty(info))
                {
                    MessageBox.Show(info);
                    return;
                }
            }
            base.query();
        }
        /// <summary>
        /// 检查是否已存在相同的事件编号
        /// </summary>
        /// <param name="snSet">待插入编号</param>
        /// <param name="type">GSM or TD</param>
        /// <returns>如果存在，返回对应的信息，否则返回string.Empty</returns>
        private string containsSN(List<int> idSet, byte type)
        {
            idSet.Sort();
            QueryEventLogSN queryGSMSn = new QueryEventLogSN(type);
            queryGSMSn.Query();
            List<int> snSet = queryGSMSn.LogSNs;
            List<int> sameSnSet = new List<int>();
            foreach (int id in idSet)
            {
                if (snSet.Contains(id))
                {
                    sameSnSet.Add(id);
                }
            }
            StringBuilder sb = new StringBuilder();
            if (sameSnSet.Count > 0)
            {
                string token = type == 1 ? "VVIP-GSM" : "VVIP-TD";
                sb.Append(token);
                sb.Append(" 数据库里已存在相同的【事件编号】：");
                foreach (int sn in sameSnSet)
                {
                    sb.Append(sn);
                    sb.Append(",");
                }
                sb.Remove(sb.Length - 1, 1);
            }
            return sb.ToString();
        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                E_VType[] retArrDef = getSqlRetTypeArr();
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                if (MainDB)
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
                }
                else
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
                }
                List<string> sqls = getSQLTextSet();
                StringBuilder sqlSb = new StringBuilder();
                bool firstPack = true;
                for (int i = 0; i < sqls.Count; i++)
                {
                    string sql = sqls[i];
                    if (sqlSb.Length + sql.Length < 3000)
                    {
                        sqlSb.Append(sql);
                    }
                    else
                    {//发送完之前的sql语句，再append当前sql
                        if (!firstPack)
                        {
                            System.Threading.Thread.Sleep(1000);
                        }
                        firstPack = false;
                        sendPack(clientProxy, sqlSb, retArrDef);
                        sqlSb.Remove(0, sqlSb.Length);
                        sqlSb.Append(sql);
                    }
                    if (i == sqls.Count - 1)
                    {//last
                        if (!firstPack)
                        {
                            System.Threading.Thread.Sleep(1000);
                        }
                        sendPack(clientProxy, sqlSb, retArrDef);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
        }

        private void sendPack(ClientProxy clientProxy, StringBuilder sqlSb, E_VType[] retArrDef)
        {
            clientProxy.Package.Content.PrepareAddParam();
            clientProxy.Package.Content.AddParam(sqlSb.ToString());
            StringBuilder sb = new StringBuilder();
            if (retArrDef != null)
            {
                for (int i = 0; i < retArrDef.Length; i++)
                {
                    sb.Append((int)retArrDef[i]);
                    if (i < retArrDef.Length - 1)
                    {
                        sb.Append(",");
                    }
                }
            }
            clientProxy.Package.Content.AddParam(sb.ToString());
            clientProxy.Send();
        }

        protected virtual List<string> getSQLTextSet()
        {
            List<string> sqls = new List<string>();
            if (gsmRecords != null)
            {
                foreach (EventLogGSM log in gsmRecords)
                {
                    sqls.Add(createOneInsertText(log));
                }
            }
            if (tdRecords != null)
            {
                foreach (EventLogTD log in tdRecords)
                {
                    sqls.Add(createOneInsertText(log));
                }
            }
            return sqls;
        }

        private string createOneInsertText(EventLog log)
        {
            bool gsmLog = log is EventLogGSM;
            string table = gsmLog ? "tb_gsm_vvip_log" : "tb_td_vvip_log";
            StringBuilder sb = new StringBuilder("insert into  ");
            sb.Append(table);
            sb.Append(" values(");
            sb.Append(log.SN);
            sb.Append(",'");
            sb.Append(log.BTSType);
            sb.Append("',");
            sb.Append(JavaDate.GetMilliseconds(log.Date) / 1000);
            sb.Append(",'");
            if (!gsmLog)
            {
                sb.Append(((EventLogTD)log).PhoneModelNumber);
                sb.Append("','");
            }
            sb.Append(log.LogNumber);
            sb.Append("','");
            sb.Append(log.CI);
            sb.Append("','");
            sb.Append(log.MoMt);
            sb.Append("','");
            sb.Append(log.EventName);
            sb.Append("',");
            sb.Append(log.Time.Ticks / 1000);
            sb.Append(",'");
            sb.Append(log.Place);
            sb.Append("',");
            sb.Append(log.Lng);
            sb.Append(",");
            sb.Append(log.Lat);
            sb.Append(",'");
            sb.Append(log.Analytics);
            sb.Append("','");
            sb.Append(log.Suggestion);
            sb.Append("','");
            sb.Append(log.ErrorType);
            sb.Append("','");
            sb.Append(log.OwnRegion);
            sb.Append("','");
            sb.Append(log.IsCloseLoop);
            sb.Append("','");
            sb.Append(log.HasTrafficLog);
            sb.Append("','");
            sb.Append(log.PhoneNumber);
            sb.Append("');");
            return sb.ToString();
        }

        protected override Model.Interface.E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_Int;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {

        }

        protected override string getSqlTextString()
        {
            throw new NotImplementedException();
        }
    }
}
