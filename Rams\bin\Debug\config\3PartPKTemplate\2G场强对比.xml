<?xml version="1.0"?>
<Configs>
  <Config name="Template">
    <Item name="Options" typeName="IDictionary">
      <Item typeName="String" key="Name">2G场强对比</Item>
      <Item typeName="IDictionary" key="HubContext">
        <Item typeName="IDictionary" key="移动">
          <Item typeName="String" key="Name">移动</Item>
          <Item typeName="IList" key="ServVec">
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="String" key="FormulaName" />
          <Item typeName="String" key="FormulaExp">{Mx_5A010202}</Item>
          <Item typeName="IDictionary" key="ValueRange">
            <Item typeName="Double" key="Min">-140</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Double" key="Max">-10</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="联通">
          <Item typeName="String" key="Name">联通</Item>
          <Item typeName="IList" key="ServVec">
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="String" key="FormulaName" />
          <Item typeName="String" key="FormulaExp">{Mx_5A010202}</Item>
          <Item typeName="IDictionary" key="ValueRange">
            <Item typeName="Double" key="Min">-140</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Double" key="Max">-10</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="电信">
          <Item typeName="String" key="Name">电信</Item>
          <Item typeName="IList" key="ServVec">
            <Item typeName="Int32">6</Item>
          </Item>
          <Item typeName="String" key="FormulaName" />
          <Item typeName="String" key="FormulaExp">{Cx_5B060526}</Item>
          <Item typeName="IDictionary" key="ValueRange">
            <Item typeName="Double" key="Min">-140</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Double" key="Max">-10</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IList" key="AlghirithmVec">
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">移动比联通电信都好</Item>
          <Item typeName="Boolean" key="IsSpecial">False</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-16760832</Item>
          <Item typeName="String" key="ColorRGB">0,64,0</Item>
          <Item typeName="IList" key="ValueRangeVec">
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">移动</Item>
              <Item typeName="String" key="GuestCarrier">联通</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">5</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">100</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">移动</Item>
              <Item typeName="String" key="GuestCarrier">电信</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">5</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">100</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">移动与联通电信相当</Item>
          <Item typeName="Boolean" key="IsSpecial">False</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-256</Item>
          <Item typeName="String" key="ColorRGB">255,255,0</Item>
          <Item typeName="IList" key="ValueRangeVec">
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">移动</Item>
              <Item typeName="String" key="GuestCarrier">联通</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">-5</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">5</Item>
                <Item typeName="Boolean" key="MaxIncluded">False</Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">移动</Item>
              <Item typeName="String" key="GuestCarrier">电信</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">-5</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">5</Item>
                <Item typeName="Boolean" key="MaxIncluded">False</Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">移动比联通电信都差</Item>
          <Item typeName="Boolean" key="IsSpecial">False</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-65536</Item>
          <Item typeName="String" key="ColorRGB">255,0,0</Item>
          <Item typeName="IList" key="ValueRangeVec">
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">移动</Item>
              <Item typeName="String" key="GuestCarrier">联通</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">-100</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">-5</Item>
                <Item typeName="Boolean" key="MaxIncluded">False</Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">移动</Item>
              <Item typeName="String" key="GuestCarrier">电信</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">-100</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">-5</Item>
                <Item typeName="Boolean" key="MaxIncluded">False</Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">联通比移动电信都好</Item>
          <Item typeName="Boolean" key="IsSpecial">False</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-16744320</Item>
          <Item typeName="String" key="ColorRGB">0,128,128</Item>
          <Item typeName="IList" key="ValueRangeVec">
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">联通</Item>
              <Item typeName="String" key="GuestCarrier">移动</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">5</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">100</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">联通</Item>
              <Item typeName="String" key="GuestCarrier">电信</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">5</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">100</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">联通与移动电信相当</Item>
          <Item typeName="Boolean" key="IsSpecial">False</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-128</Item>
          <Item typeName="String" key="ColorRGB">255,255,128</Item>
          <Item typeName="IList" key="ValueRangeVec">
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">联通</Item>
              <Item typeName="String" key="GuestCarrier">移动</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">-5</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">5</Item>
                <Item typeName="Boolean" key="MaxIncluded">False</Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">联通</Item>
              <Item typeName="String" key="GuestCarrier">电信</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">-5</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">5</Item>
                <Item typeName="Boolean" key="MaxIncluded">False</Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">联通比移动电信都差</Item>
          <Item typeName="Boolean" key="IsSpecial">False</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-32640</Item>
          <Item typeName="String" key="ColorRGB">255,128,128</Item>
          <Item typeName="IList" key="ValueRangeVec">
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">联通</Item>
              <Item typeName="String" key="GuestCarrier">移动</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">-100</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">-5</Item>
                <Item typeName="Boolean" key="MaxIncluded">False</Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">联通</Item>
              <Item typeName="String" key="GuestCarrier">电信</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">-100</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">-5</Item>
                <Item typeName="Boolean" key="MaxIncluded">False</Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">电信比移动电信都好</Item>
          <Item typeName="Boolean" key="IsSpecial">False</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-16777152</Item>
          <Item typeName="String" key="ColorRGB">0,0,64</Item>
          <Item typeName="IList" key="ValueRangeVec">
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">电信</Item>
              <Item typeName="String" key="GuestCarrier">移动</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">5</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">100</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">电信</Item>
              <Item typeName="String" key="GuestCarrier">联通</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">5</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">100</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">电信与移动联通相当</Item>
          <Item typeName="Boolean" key="IsSpecial">False</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-8323200</Item>
          <Item typeName="String" key="ColorRGB">128,255,128</Item>
          <Item typeName="IList" key="ValueRangeVec">
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">电信</Item>
              <Item typeName="String" key="GuestCarrier">移动</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">-5</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">5</Item>
                <Item typeName="Boolean" key="MaxIncluded">False</Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">电信</Item>
              <Item typeName="String" key="GuestCarrier">联通</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">-5</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">5</Item>
                <Item typeName="Boolean" key="MaxIncluded">False</Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">电信比移动电信都差</Item>
          <Item typeName="Boolean" key="IsSpecial">False</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-4144897</Item>
          <Item typeName="String" key="ColorRGB">192,192,255</Item>
          <Item typeName="IList" key="ValueRangeVec">
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">电信</Item>
              <Item typeName="String" key="GuestCarrier">移动</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">-100</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">-5</Item>
                <Item typeName="Boolean" key="MaxIncluded">False</Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">电信</Item>
              <Item typeName="String" key="GuestCarrier">联通</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">-100</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">-5</Item>
                <Item typeName="Boolean" key="MaxIncluded">False</Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">只有移动未测</Item>
          <Item typeName="Boolean" key="IsSpecial">True</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-2461482</Item>
          <Item typeName="String" key="ColorRGB">218,112,214</Item>
          <Item typeName="IList" key="ValueRangeVec" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">只有联通未测</Item>
          <Item typeName="Boolean" key="IsSpecial">True</Item>
          <Item typeName="String" key="ENaNCM">有数据</Item>
          <Item typeName="String" key="ENaNCU">无数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-60269</Item>
          <Item typeName="String" key="ColorRGB">255,20,147</Item>
          <Item typeName="IList" key="ValueRangeVec" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">只有电信未测</Item>
          <Item typeName="Boolean" key="IsSpecial">True</Item>
          <Item typeName="String" key="ENaNCM">有数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">无数据</Item>
          <Item typeName="Int32" key="Color">-2354116</Item>
          <Item typeName="String" key="ColorRGB">220,20,60</Item>
          <Item typeName="IList" key="ValueRangeVec" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">只有移动有测</Item>
          <Item typeName="Boolean" key="IsSpecial">True</Item>
          <Item typeName="String" key="ENaNCM">有数据</Item>
          <Item typeName="String" key="ENaNCU">无数据</Item>
          <Item typeName="String" key="ENaNCT">无数据</Item>
          <Item typeName="Int32" key="Color">-9868951</Item>
          <Item typeName="String" key="ColorRGB">105,105,105</Item>
          <Item typeName="IList" key="ValueRangeVec" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">只有联通有测</Item>
          <Item typeName="Boolean" key="IsSpecial">True</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">无数据</Item>
          <Item typeName="Int32" key="Color">-5658199</Item>
          <Item typeName="String" key="ColorRGB">169,169,169</Item>
          <Item typeName="IList" key="ValueRangeVec" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">只有电信有测</Item>
          <Item typeName="Boolean" key="IsSpecial">True</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">无数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-4419697</Item>
          <Item typeName="String" key="ColorRGB">188,143,143</Item>
          <Item typeName="IList" key="ValueRangeVec" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">三网都没测</Item>
          <Item typeName="Boolean" key="IsSpecial">True</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">无数据</Item>
          <Item typeName="String" key="ENaNCT">无数据</Item>
          <Item typeName="Int32" key="Color">-47872</Item>
          <Item typeName="String" key="ColorRGB">255,69,0</Item>
          <Item typeName="IList" key="ValueRangeVec" />
        </Item>
      </Item>
      <Item typeName="IDictionary" key="OtherAlghirithm">
        <Item typeName="String" key="Name">其他</Item>
        <Item typeName="Boolean" key="IsSpecial">False</Item>
        <Item typeName="String" key="ENaNCM">有数据</Item>
        <Item typeName="String" key="ENaNCU">有数据</Item>
        <Item typeName="String" key="ENaNCT">有数据</Item>
        <Item typeName="Int32" key="Color">-16711681</Item>
        <Item typeName="String" key="ColorRGB">0,255,255</Item>
        <Item typeName="IList" key="ValueRangeVec" />
      </Item>
    </Item>
  </Config>
</Configs>