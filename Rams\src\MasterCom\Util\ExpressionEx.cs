﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.Util
{
    class ExpressionEx
    {
        delegate double UnaryCalculator(double baseNum);
        delegate double BinaryCalculator(double first, double second);

        public double lastAnswer;
        private double angleFactor;

        private const int HighPriority = 1;
        private readonly Dictionary<string, UnaryCalculator> dicUnaryCal;
        private readonly Dictionary<string, BinaryCalculator> dicBinaryCal;
        private readonly Dictionary<string, int> dicBinary;

        public ExpressionEx()
        {
            dicUnaryCal = new Dictionary<string, UnaryCalculator>()
            {
                {"cos", cos},{"sin", sin},{"tan",tan},{"acos",acos},{"asin",asin},{"atan",atan},{"log",log},
                {"ln",ln},{"floor",floor},{"ceil",ceil},{"sqrt",sqrt},{"cosh",cosh},{"sinh",sinh},{"tanh",tanh},
                {"round",round},{"abs",abs},{"!", factorial}
            };

            dicBinaryCal = new Dictionary<string, BinaryCalculator>()
            {
                {"^", power}, 
                {"*", multiply}, {"/", divide}, {"\\", divide}, {"%", mod}, 
                {">", greater}, {"<", less}, 
                {"+", addition}, {"-", subtraction}
            };

            dicBinary = new Dictionary<string, int>()
            {
                {"(", 0}, {")", 0},
                {"^", 2}, 
                {"*", 3}, {"/", 3}, {"\\", 3}, {"%", 3}, 
                {">", 4}, {"<", 4}, 
                {"+", 5}, {"-", 5}
            };

            useDegrees();
        }

        public void useRadians()
        {
            angleFactor = 1;
        }

        public void useDegrees()
        {
            angleFactor = Math.PI / 180;
        }

        public string ParseCommand(string command)//公式有异常时，考虑效率问题，不再抛出Exception
        {
            string returnValue;
            command = command.Trim();
            try
            {
                List<string> lstResult;
                if (!InfixToPostfix(command, out lstResult))
                {
                    return "解析公式失败，无效数字！";
                }
                lastAnswer = ParseExpr(lstResult);
                returnValue = lastAnswer.ToString();
            }
            catch
            {
                return "解析公式失败，无效数字！";
            }
            return returnValue;
        }

        private double ParseExpr(List<string> lstResult)
        {
            double dv;
            Stack<double> answers = new Stack<double>();
            foreach (string result in lstResult)
            {
                if (dicUnaryCal.ContainsKey(result))
                {
                    if (answers.Count <= 0)
                        return double.NaN;

                    dv = answers.Pop();
                    dv = dicUnaryCal[result](dv);
                    answers.Push(dv);
                }
                else if (dicBinaryCal.ContainsKey(result))
                {
                    if (answers.Count <= 1)
                        return double.NaN;

                    double second = answers.Pop();
                    double first = answers.Pop();
                    dv = dicBinaryCal[result](first, second);
                    answers.Push(dv);
                }
                else if(double.TryParse(result, out dv))
                {
                    answers.Push(dv);
                }
                else
                {
                    return double.NaN;
                }
            }

            return answers.Peek();
        }

        public bool InfixToPostfix(string expression, out List<string> lstResults)
        {
            bool rt = true;
            if (expression.StartsWith("-"))
                expression = "0" + expression;
            if (expression.Contains("(-"))
                expression = expression.Replace("(-", "(0-");

            Stack<string> operators = new Stack<string>();
            lstResults = new List<string>();

            while (expression != string.Empty)
            {
                if (!getItem(ref expression, ref operators, ref lstResults))
                {
                    rt = false;
                    break;
                }
            }
            while (rt && operators.Count > 0)
            {
                lstResults.Add(operators.Pop()); //pop All Operator
            }
            return rt;
        }

        private bool getItem(ref string command, ref Stack<string> operators, ref List<string> lstResults)
        {
            bool rt = false;
            char c = command[0];
            if (char.IsWhiteSpace(c))
            {
                command = command.Substring(1);
                return true;
            }
            else if (char.IsDigit(c))
            {
                rt = ParseNumber(ref command, ref lstResults);
            }
            else if (char.IsLetter(c))
            {
                rt = ParseWord(ref command, ref operators, ref lstResults);
            }
            else if (c == '(' || c == ')' || dicBinary.ContainsKey(c.ToString()))
            {
                command = command.Substring(1);
                string strop = c.ToString();
                rt = pushOperator(strop, dicBinary[strop], ref operators, ref lstResults);
            }
            else
            {
                Console.WriteLine("UnDefine Operator [{0}] !", c);
            }

            return rt;
        }

        private bool ParseNumber(ref string command, ref List<string> lstResults)
        {
            bool foundDecimal = false;
            StringBuilder sbItem = new StringBuilder();
            int i = 0;

            // build a string with the number
            while (i != command.Length && (char.IsDigit(command[i]) || command[i] == '.'))
            {
                if (command[i] == '.')
                {
                    if (!foundDecimal)
                        foundDecimal = true;
                    else
                    {
                        Console.WriteLine("FormatException : two point");
                        return false;
                    }
                }
                sbItem.Append(command[i++]);
            }
            if (sbItem[0] == '.' || sbItem[sbItem.Length - 1] == '.')
            {
                Console.WriteLine("FormatException : point in each end");
                return false;
            }
            //throw new System.FormatException(); // don't allow the first last char to be a decimal point

            command = command.Substring(i, command.Length - i);

            double dv;
            bool rt = double.TryParse(sbItem.ToString(), out dv);
            if(rt)
            {
                lstResults.Add(dv.ToString());
            }
            return rt;
        }

        private bool ParseWord(ref string command, 
            ref Stack<string> operators, ref List<string> lstResults)
        {
            bool rt = true;
            string item = "";
            int i = 0;
            string funcName = "";
            StringBuilder sb = new StringBuilder();
            while (i != command.Length && char.IsLetter(command[i]))    // build of string of letters
            {
                sb.Append(command[i++]);
            }
            funcName = sb.ToString();

            command = command.Substring(funcName.Length, command.Length - funcName.Length);
            switch (funcName)
            {
                case "cos":
                case "sin":
                case "tan":
                case "acos":
                case "asin":
                case "atan":
                case "log":
                case "ln":
                case "floor":
                case "ceil":
                case "sqrt":
                case "cosh":
                case "sinh":
                case "tanh":
                case "round":
                case "abs":
                    item = funcName;
                    pushOperator(item, HighPriority, ref operators, ref lstResults);
                    break;
                // Constants
                case "e": // exponent		
                    item = Math.E.ToString();
                    lstResults.Add(item);
                    break;
                case "PI": // pi
                    item = Math.PI.ToString();
                    lstResults.Add(item);
                    break;
                case "g":	// acceleration of gravity on earth (m/s)
                    item = "9.80665";
                    lstResults.Add(item);
                    break;
                case "R":   // Gas Las constant - J / (mol * k)
#if DEBUG
                    Console.Write("gas");
#endif
                    item = "9.80665";
                    lstResults.Add(item);
                    break;
                case "c":	// Speed of Light in vacuum (m/s)
                    item = "2.9979";
                    lstResults.Add(item);
                    break;
                case "ANS":
                    item = lastAnswer.ToString();
                    lstResults.Add(item);
                    break;
                default:
                    Console.WriteLine("FormatException : UnDefine Unary Operator");
                    rt = false;
                    break;
            }
            return rt;
        }

        private bool pushOperator(string strop, int priority,
            ref Stack<string> operators, ref List<string> lstResults)
        {
            bool rt = true;
            if(strop == "(")
            {
                operators.Push(strop);
            }
            else if (strop == ")")
            {
                while (operators.Count > 0)
                {
                    string popOp = operators.Pop();
                    if (popOp == "(")
                        break;

                    lstResults.Add(popOp);
                }
            }
            else
            {
                rt = dealOperators(strop, priority, operators, lstResults, rt);

                operators.Push(strop);
            }

            return rt;
        }

        private bool dealOperators(string strop, int priority, Stack<string> operators, List<string> lstResults, bool rt)
        {
            while (operators.Count > 0)
            {
                string popOp = operators.Pop();
                if (popOp == "(")
                {
                    operators.Push(popOp);
                    break;
                }
                else if (dicUnaryCal.ContainsKey(popOp))
                {
                    if (HighPriority <= priority)
                    {
                        lstResults.Add(popOp);
                    }
                    else
                    {
                        operators.Push(popOp);
                        break;
                    }
                }
                else if (dicBinary.ContainsKey(popOp))
                {
                    int popPrio = dicBinary[popOp];
                    if (popPrio <= priority)
                    {
                        lstResults.Add(popOp);
                    }
                    else
                    {
                        operators.Push(popOp);
                        break;
                    }
                }
                else
                {
                    rt = false;
                    Console.WriteLine("Undefine Operator {0}", strop);
                    break;
                }
            }

            return rt;
        }

        private double cos(double baseNum)
        {
            return Math.Cos(baseNum * angleFactor);
        }

        private double sin(double baseNum)
        {
            return Math.Sin(baseNum * angleFactor);
        }

        private double tan(double baseNum)
        {
            return Math.Tan(baseNum * angleFactor);
        }

        private double acos(double baseNum)
        {
            return Math.Acos(baseNum * angleFactor);
        }

        private double asin(double baseNum)
        {
            return Math.Asin(baseNum * angleFactor);
        }

        private double atan(double baseNum)
        {
            return Math.Atan(baseNum * angleFactor);
        }

        private double cosh(double baseNum)
        {
            return Math.Cosh(baseNum * angleFactor);
        }

        private double sinh(double baseNum)
        {
            return Math.Sinh(baseNum * angleFactor);
        }

        private double tanh(double baseNum)
        {
            return Math.Tanh(baseNum * angleFactor);
        }

        private double log(double baseNum)
        {
            return Math.Log10(baseNum);
        }

        private double ln(double baseNum)
        {
            return Math.Log(baseNum);
        }

        private double floor(double baseNum)
        {
            return Math.Floor(baseNum);
        }

        private double ceil(double baseNum)
        {
            return Math.Ceiling(baseNum);
        }

        private double sqrt(double baseNum)
        {
            return Math.Sqrt(baseNum);
        }

        private double round(double baseNum)
        {
            return Math.Round(baseNum);
        }

        private double abs(double baseNum)
        {
            return Math.Abs(baseNum);
        }

        private double factorial(double baseNum)
        {
            double product = 1;
            for (long i = 1; i <= baseNum; ++i)
                product = product * i;
            return product;
        }

        private double power(double first, double second)
        {
            return Math.Pow(first, second);
        }

        private double multiply(double first, double second)
        {
            return first * second;
        }

        private double divide(double first, double second)
        {
            return first / second;
        }

        private double mod(double first, double second)
        {
            return first % second;
        }

        private double greater(double first, double second)
        {
            if (first > second)
                return 1;

            return 0;
        }

        private double less(double first, double second)
        {
            if (first < second)
                return 1;

            return 0;
        }

        private double addition(double first, double second)
        {
            return first + second;
        }

        private double subtraction(double first, double second)
        {
            return first - second;
        }
    }
}
