﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCompareHandoverSequenceAnaByFiles_GSM : ZTCompareHandoverSequenceAna
    {
        public ZTCompareHandoverSequenceAnaByFiles_GSM(MainModel mm)
            : base(mm)
        {
            eventIDLst.Clear();
            foreach (EEventGSMID id in Enum.GetValues(typeof(EEventGSMID)))
            {
                eventIDLst.Add((int)id);
            }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }
    }
}
