﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraTreeList.Nodes;
using DevExpress.Utils.Paint;
using DevExpress.XtraTreeList;
using DevExpress.Utils.Drawing;
using MasterCom.Util.DevControlManager;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public partial class AreaListPnl : UserControl
    {
        public AreaListPnl()
        {
            InitializeComponent();
            TreeListHelper.ThreeStateControl(treeList);
        }

        public void FillData(ZTAreaManager areaMngr)
        {
            this.areaMngr = areaMngr;
            treeList.CustomDrawNodeCell -= treeList_CustomDrawNodeCell;
            treeList.CustomDrawNodeCell += treeList_CustomDrawNodeCell;
            treeList.FocusedNodeChanged -= treeList_FocusedNodeChanged;
            treeList.FocusedNodeChanged += treeList_FocusedNodeChanged;

            layoutRank.Controls.Clear();
            this.layoutRank.ColumnCount = areaMngr.Ranks.Count;
            for (int i = 0; i < areaMngr.Ranks.Count; i++)
            {
                AreaRank rank = areaMngr.Ranks[i];
                CheckBox chkBox = new CheckBox();
                chkBox.Tag = rank;
                chkBox.Text = rank.Name;
                chkBox.CheckedChanged += chkBox_CheckedChanged;
                this.layoutRank.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 25F));
                this.layoutRank.Controls.Add(chkBox, i, 0);
            }
            treeList.Nodes.Clear();
            List<AreaBase> roots = ZTAreaManager.Instance.GetArea(ZTAreaManager.Instance.Ranks[0]);
            if (roots == null)
            {
                return;
            }
            foreach (AreaBase area in roots)
            {
                if (area.SubAreas == null)
                {
                    continue;
                }
                appendTreeNode(area, treeList, null);
            }

        }

        private void appendTreeNode(AreaBase area, TreeList treeList, TreeListNode parentNode)
        {
            TreeListNode node = treeList.AppendNode(new object[] { area.Name }, parentNode);
            node.Tag = area;
            if (area.SubAreas != null)
            {
                foreach (AreaBase subArea in area.SubAreas)
                {
                    appendTreeNode(subArea, treeList, node);
                }
            }
        }

        private void treeList_CustomDrawNodeIndicator(object sender, CustomDrawNodeIndicatorEventArgs e)
        {
            if (e.Node != null)
            {
                AreaBase area = e.Node.Tag as AreaBase;
                e.Graphics.DrawString(area.Rank.Name, this.Font, Brushes.Black, e.Bounds);
                IndicatorObjectInfoArgs args = e.ObjectArgs as IndicatorObjectInfoArgs;
                args.DisplayText = area.Rank.Name;
                e.ImageIndex = -1;
            }
        }

        ZTAreaManager areaMngr = null;

        void chkBox_CheckedChanged(object sender, EventArgs e)
        {
            CheckBox chk = sender as CheckBox;
            AreaRank area = chk.Tag as AreaRank;
            if (chk.Checked)
            {
                rankCheckDic[area] = true;
            }
            else
            {
                rankCheckDic.Remove(area);
            }
        }

        private Dictionary<AreaRank, bool> rankCheckDic = new Dictionary<AreaRank, bool>();

        private void btnSearch_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            searchText = btnSearch.Text;
            if (searchText.Length>0)
            {
                PerformSearch(e.Button.Index == 1);
                treeList.FocusedNode = CurrentNode;
            }
        }

        void treeList_FocusedNodeChanged(object sender, FocusedNodeChangedEventArgs e)
        {
            CurrentNode = e.Node;
        }

        XPaint paint = new XPaint();
        void treeList_CustomDrawNodeCell(object sender, CustomDrawNodeCellEventArgs e)
        {
            if (searchText == null || searchText.Length == 0)
            {
                return;
            }
            int index = e.CellText.IndexOf(Text, 0);
            if (index >= 0)
            {
                e.Handled = true;
                if (e.Node.Focused)
                {
                    paint.DrawMultiColorString(e.Cache, e.Bounds, e.CellText, Text, e.Appearance, Color.Red, Color.Yellow, false, index);
                }
                else
                    paint.DrawMultiColorString(e.Cache, e.Bounds, e.CellText, Text, e.Appearance, Color.Blue, e.Appearance.GetBackColor(), false, index);
            }
        }

        private TreeListNode currentNode;
        public TreeListNode CurrentNode
        {
            get
            {
                if (currentNode == null)
                    return treeList.FocusedNode;
                else
                    return currentNode;
            }
            set { currentNode = value; }
        }

        private string searchText;

        public void FindNext()
        {
            PerformSearch(true);
        }

        public void FindPrev()
        {
            PerformSearch(false);
        }

        public void PerformSearch(bool forward)
        {
            CurrentNode = FindNode(forward);
        }

        private TreeListNode FindNode(bool forward)
        {
            if (CurrentNode == null)
                return CurrentNode;
            TreeListNode node = GetNextNode(CurrentNode, forward);
            if (node == null)
                return node;
            while (!MatchCondition(node))
            {
                node = GetNextNode(node, forward);
            }
            return node;
        }
        private TreeListNode GetNextNode(TreeListNode node, bool forward)
        {
            node.ExpandAll();
            return forward ? node.NextVisibleNode : node.PrevVisibleNode;
        }
        private bool MatchCondition(TreeListNode node)
        {
            if (node == null)
                return true;

            AreaBase area = node.Tag as AreaBase;
            if (!rankCheckDic.ContainsKey(area.Rank))
            {
                return false;
            }
           
            if (node.GetDisplayText(treeListColumn1).Contains(searchText))
                return true;
            return false;
        }

        public Dictionary<AreaBase, List<AreaBase>> GetSelAreaDic()
        {
            Dictionary<AreaBase, List<AreaBase>> rootLeafDic = new Dictionary<AreaBase, List<AreaBase>>();
            foreach (TreeListNode node in treeList.Nodes)
            {
                AreaBase area = node.Tag as AreaBase;
                if (area == null) continue;

                List<AreaBase> leafs = new List<AreaBase>();
                getLeafIds(node, ref leafs);

                if (leafs.Count > 0)
                {
                    rootLeafDic[area] = leafs;
                }
            }
            return rootLeafDic;
        }

        //public List<AreaBase> CurSelBaseAreas
        //{
        //    get
        //    {
        //        List<AreaBase> list = new List<AreaBase>();
        //        foreach (TreeListNode node in treeList.Nodes)
        //        {
        //            getLeafIds(node, ref list);
        //        }
        //        return list;
        //    }
        //}


        private void getLeafIds(TreeListNode node, ref List<AreaBase> leafs)
        {
            if (!node.HasChildren)
            {
                if(node.Checked)
                    leafs.Add((AreaBase)node.Tag);
            }
            else
            {
                foreach (TreeListNode sub in node.Nodes)
                {
                    getLeafIds(sub, ref leafs);
                }
            }
        }
    }
}
