﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class WeakSINRRoadSettingDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.numMaxValue = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.numMinDistance = new System.Windows.Forms.NumericUpDown();
            this.label7 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.numMaxTPDistance = new System.Windows.Forms.NumericUpDown();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.label5 = new System.Windows.Forms.Label();
            this.numWeakSINRPercent = new System.Windows.Forms.NumericUpDown();
            this.label8 = new System.Windows.Forms.Label();
            this.chkMinDuration = new System.Windows.Forms.CheckBox();
            this.chkMinDistance = new System.Windows.Forms.CheckBox();
            this.label18 = new System.Windows.Forms.Label();
            this.numMinDuration = new System.Windows.Forms.NumericUpDown();
            this.groupDisDur = new DevExpress.XtraEditors.GroupControl();
            this.groupBase = new DevExpress.XtraEditors.GroupControl();
            this.chkRSRP = new System.Windows.Forms.CheckBox();
            this.numMinValue = new System.Windows.Forms.NumericUpDown();
            this.label9 = new System.Windows.Forms.Label();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.chkShowFDDPoint = new DevExpress.XtraEditors.CheckEdit();
            this.btnRemove = new DevExpress.XtraEditors.SimpleButton();
            this.btnAddFreBand = new DevExpress.XtraEditors.SimpleButton();
            this.spinEditEnd = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditStart = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.listBoxControlFDDFreq = new DevExpress.XtraEditors.ListBoxControl();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxValue)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxTPDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakSINRPercent)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDuration)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupDisDur)).BeginInit();
            this.groupDisDur.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupBase)).BeginInit();
            this.groupBase.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMinValue)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkShowFDDPoint.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditEnd.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditStart.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.listBoxControlFDDFreq)).BeginInit();
            this.SuspendLayout();
            // 
            // numMaxValue
            // 
            this.numMaxValue.Location = new System.Drawing.Point(111, 28);
            this.numMaxValue.Maximum = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numMaxValue.Minimum = new decimal(new int[] {
            50,
            0,
            0,
            -2147483648});
            this.numMaxValue.Name = "numMaxValue";
            this.numMaxValue.Size = new System.Drawing.Size(80, 21);
            this.numMaxValue.TabIndex = 0;
            this.numMaxValue.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(65, 33);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(41, 12);
            this.label1.TabIndex = 43;
            this.label1.Text = "SINR≤";
            // 
            // numMinDistance
            // 
            this.numMinDistance.Location = new System.Drawing.Point(116, 38);
            this.numMinDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numMinDistance.Name = "numMinDistance";
            this.numMinDistance.Size = new System.Drawing.Size(80, 21);
            this.numMinDistance.TabIndex = 1;
            this.numMinDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMinDistance.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(196, 128);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(17, 12);
            this.label7.TabIndex = 55;
            this.label7.Text = "米";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(196, 33);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(17, 12);
            this.label3.TabIndex = 47;
            this.label3.Text = "dB";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(5, 127);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(101, 12);
            this.label6.TabIndex = 53;
            this.label6.Text = "相邻采样点距离≤";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(201, 45);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(17, 12);
            this.label4.TabIndex = 48;
            this.label4.Text = "米";
            // 
            // numMaxTPDistance
            // 
            this.numMaxTPDistance.Location = new System.Drawing.Point(111, 122);
            this.numMaxTPDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numMaxTPDistance.Name = "numMaxTPDistance";
            this.numMaxTPDistance.Size = new System.Drawing.Size(80, 21);
            this.numMaxTPDistance.TabIndex = 2;
            this.numMaxTPDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMaxTPDistance.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // btnOK
            // 
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(438, 307);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 3;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(558, 307);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 56;
            this.btnCancel.Text = "取消";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(196, 97);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(17, 12);
            this.label5.TabIndex = 48;
            this.label5.Text = " %";
            // 
            // numWeakSINRPercent
            // 
            this.numWeakSINRPercent.Location = new System.Drawing.Point(111, 90);
            this.numWeakSINRPercent.Name = "numWeakSINRPercent";
            this.numWeakSINRPercent.Size = new System.Drawing.Size(80, 21);
            this.numWeakSINRPercent.TabIndex = 1;
            this.numWeakSINRPercent.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numWeakSINRPercent.Value = new decimal(new int[] {
            80,
            0,
            0,
            0});
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(29, 97);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(77, 12);
            this.label8.TabIndex = 44;
            this.label8.Text = "质差点占比≥";
            // 
            // chkMinDuration
            // 
            this.chkMinDuration.AutoSize = true;
            this.chkMinDuration.Location = new System.Drawing.Point(51, 79);
            this.chkMinDuration.Name = "chkMinDuration";
            this.chkMinDuration.Size = new System.Drawing.Size(60, 16);
            this.chkMinDuration.TabIndex = 71;
            this.chkMinDuration.Text = "时长≥";
            this.chkMinDuration.UseVisualStyleBackColor = true;
            this.chkMinDuration.CheckedChanged += new System.EventHandler(this.chkMinDuration_CheckedChanged);
            // 
            // chkMinDistance
            // 
            this.chkMinDistance.AutoSize = true;
            this.chkMinDistance.Checked = true;
            this.chkMinDistance.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkMinDistance.Location = new System.Drawing.Point(27, 45);
            this.chkMinDistance.Name = "chkMinDistance";
            this.chkMinDistance.Size = new System.Drawing.Size(84, 16);
            this.chkMinDistance.TabIndex = 70;
            this.chkMinDistance.Text = "持续距离≥";
            this.chkMinDistance.UseVisualStyleBackColor = true;
            this.chkMinDistance.CheckedChanged += new System.EventHandler(this.chkMinDistance_CheckedChanged);
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(203, 79);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(17, 12);
            this.label18.TabIndex = 69;
            this.label18.Text = "秒";
            // 
            // numMinDuration
            // 
            this.numMinDuration.Enabled = false;
            this.numMinDuration.Location = new System.Drawing.Point(116, 74);
            this.numMinDuration.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numMinDuration.Name = "numMinDuration";
            this.numMinDuration.Size = new System.Drawing.Size(80, 21);
            this.numMinDuration.TabIndex = 68;
            this.numMinDuration.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMinDuration.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // groupDisDur
            // 
            this.groupDisDur.Controls.Add(this.chkMinDistance);
            this.groupDisDur.Controls.Add(this.chkMinDuration);
            this.groupDisDur.Controls.Add(this.label4);
            this.groupDisDur.Controls.Add(this.numMinDistance);
            this.groupDisDur.Controls.Add(this.label18);
            this.groupDisDur.Controls.Add(this.numMinDuration);
            this.groupDisDur.Location = new System.Drawing.Point(30, 182);
            this.groupDisDur.Name = "groupDisDur";
            this.groupDisDur.Size = new System.Drawing.Size(236, 113);
            this.groupDisDur.TabIndex = 72;
            this.groupDisDur.Text = "持续条件设置(至少选其一,且关系)";
            // 
            // groupBase
            // 
            this.groupBase.Controls.Add(this.chkRSRP);
            this.groupBase.Controls.Add(this.numMinValue);
            this.groupBase.Controls.Add(this.label9);
            this.groupBase.Controls.Add(this.label6);
            this.groupBase.Controls.Add(this.numMaxTPDistance);
            this.groupBase.Controls.Add(this.label7);
            this.groupBase.Controls.Add(this.label1);
            this.groupBase.Controls.Add(this.numMaxValue);
            this.groupBase.Controls.Add(this.label5);
            this.groupBase.Controls.Add(this.label3);
            this.groupBase.Controls.Add(this.label8);
            this.groupBase.Controls.Add(this.numWeakSINRPercent);
            this.groupBase.Location = new System.Drawing.Point(30, 12);
            this.groupBase.Name = "groupBase";
            this.groupBase.Size = new System.Drawing.Size(236, 153);
            this.groupBase.TabIndex = 73;
            this.groupBase.Text = "基础条件设置";
            // 
            // chkRSRP
            // 
            this.chkRSRP.AutoSize = true;
            this.chkRSRP.Location = new System.Drawing.Point(46, 61);
            this.chkRSRP.Name = "chkRSRP";
            this.chkRSRP.Size = new System.Drawing.Size(60, 16);
            this.chkRSRP.TabIndex = 72;
            this.chkRSRP.Text = "RSRP≥";
            this.chkRSRP.UseVisualStyleBackColor = true;
            // 
            // numMinValue
            // 
            this.numMinValue.Location = new System.Drawing.Point(111, 60);
            this.numMinValue.Maximum = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numMinValue.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numMinValue.Name = "numMinValue";
            this.numMinValue.Size = new System.Drawing.Size(80, 21);
            this.numMinValue.TabIndex = 56;
            this.numMinValue.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMinValue.Value = new decimal(new int[] {
            110,
            0,
            0,
            -2147483648});
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(196, 65);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(23, 12);
            this.label9.TabIndex = 58;
            this.label9.Text = "dBm";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.chkShowFDDPoint);
            this.groupControl1.Controls.Add(this.btnRemove);
            this.groupControl1.Controls.Add(this.btnAddFreBand);
            this.groupControl1.Controls.Add(this.spinEditEnd);
            this.groupControl1.Controls.Add(this.spinEditStart);
            this.groupControl1.Controls.Add(this.labelControl3);
            this.groupControl1.Controls.Add(this.labelControl2);
            this.groupControl1.Controls.Add(this.listBoxControlFDDFreq);
            this.groupControl1.Location = new System.Drawing.Point(276, 12);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(375, 283);
            this.groupControl1.TabIndex = 74;
            this.groupControl1.Text = "设置FDD频段";
            // 
            // chkShowFDDPoint
            // 
            this.chkShowFDDPoint.EditValue = true;
            this.chkShowFDDPoint.Location = new System.Drawing.Point(9, 29);
            this.chkShowFDDPoint.Name = "chkShowFDDPoint";
            this.chkShowFDDPoint.Properties.Caption = "显示FDD采样点占比";
            this.chkShowFDDPoint.Size = new System.Drawing.Size(143, 19);
            this.chkShowFDDPoint.TabIndex = 4;
            this.chkShowFDDPoint.CheckedChanged += new System.EventHandler(this.chkShowFDDPoint_CheckedChanged);
            // 
            // btnRemove
            // 
            this.btnRemove.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnRemove.Appearance.Options.UseFont = true;
            this.btnRemove.Location = new System.Drawing.Point(282, 80);
            this.btnRemove.Name = "btnRemove";
            this.btnRemove.Size = new System.Drawing.Size(75, 23);
            this.btnRemove.TabIndex = 3;
            this.btnRemove.Text = "删除";
            this.btnRemove.Click += new System.EventHandler(this.btnRemove_Click);
            // 
            // btnAddFreBand
            // 
            this.btnAddFreBand.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnAddFreBand.Appearance.Options.UseFont = true;
            this.btnAddFreBand.Location = new System.Drawing.Point(282, 51);
            this.btnAddFreBand.Name = "btnAddFreBand";
            this.btnAddFreBand.Size = new System.Drawing.Size(75, 23);
            this.btnAddFreBand.TabIndex = 3;
            this.btnAddFreBand.Text = "增加";
            this.btnAddFreBand.Click += new System.EventHandler(this.btnAddFreBand_Click);
            // 
            // spinEditEnd
            // 
            this.spinEditEnd.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spinEditEnd.Location = new System.Drawing.Point(189, 54);
            this.spinEditEnd.Name = "spinEditEnd";
            this.spinEditEnd.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditEnd.Properties.Appearance.Options.UseFont = true;
            this.spinEditEnd.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditEnd.Properties.Mask.EditMask = "f0";
            this.spinEditEnd.Size = new System.Drawing.Size(86, 20);
            this.spinEditEnd.TabIndex = 2;
            // 
            // spinEditStart
            // 
            this.spinEditStart.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spinEditStart.Location = new System.Drawing.Point(66, 54);
            this.spinEditStart.Name = "spinEditStart";
            this.spinEditStart.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditStart.Properties.Appearance.Options.UseFont = true;
            this.spinEditStart.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditStart.Properties.Mask.EditMask = "f0";
            this.spinEditStart.Size = new System.Drawing.Size(86, 20);
            this.spinEditStart.TabIndex = 2;
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(162, 58);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(18, 12);
            this.labelControl3.TabIndex = 1;
            this.labelControl3.Text = "---";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(11, 58);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(54, 12);
            this.labelControl2.TabIndex = 1;
            this.labelControl2.Text = "FDD频段：";
            // 
            // listBoxControlFDDFreq
            // 
            this.listBoxControlFDDFreq.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.listBoxControlFDDFreq.Appearance.Options.UseFont = true;
            this.listBoxControlFDDFreq.Location = new System.Drawing.Point(66, 80);
            this.listBoxControlFDDFreq.Name = "listBoxControlFDDFreq";
            this.listBoxControlFDDFreq.Size = new System.Drawing.Size(209, 190);
            this.listBoxControlFDDFreq.TabIndex = 0;
            this.listBoxControlFDDFreq.SelectedIndexChanged += new System.EventHandler(this.listBoxControlFDDFreq_SelectedIndexChanged);
            // 
            // WeakSINRRoadSettingDlg
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(670, 342);
            this.Controls.Add(this.groupControl1);
            this.Controls.Add(this.groupBase);
            this.Controls.Add(this.groupDisDur);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "WeakSINRRoadSettingDlg";
            this.Text = "SINR质差路段条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numMaxValue)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxTPDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakSINRPercent)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinDuration)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupDisDur)).EndInit();
            this.groupDisDur.ResumeLayout(false);
            this.groupDisDur.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupBase)).EndInit();
            this.groupBase.ResumeLayout(false);
            this.groupBase.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMinValue)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkShowFDDPoint.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditEnd.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditStart.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.listBoxControlFDDFreq)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.NumericUpDown numMaxValue;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.NumericUpDown numMinDistance;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown numMaxTPDistance;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.NumericUpDown numWeakSINRPercent;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.CheckBox chkMinDuration;
        private System.Windows.Forms.CheckBox chkMinDistance;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.NumericUpDown numMinDuration;
        private DevExpress.XtraEditors.GroupControl groupDisDur;
        private DevExpress.XtraEditors.GroupControl groupBase;
        private System.Windows.Forms.NumericUpDown numMinValue;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.CheckBox chkRSRP;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.SimpleButton btnRemove;
        private DevExpress.XtraEditors.SimpleButton btnAddFreBand;
        private DevExpress.XtraEditors.SpinEdit spinEditEnd;
        private DevExpress.XtraEditors.SpinEdit spinEditStart;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.ListBoxControl listBoxControlFDDFreq;
        private DevExpress.XtraEditors.CheckEdit chkShowFDDPoint;
    }
}