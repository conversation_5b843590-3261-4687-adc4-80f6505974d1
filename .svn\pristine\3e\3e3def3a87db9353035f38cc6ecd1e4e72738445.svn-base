using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    partial class FindInterferenceForm : Form
    {
        public FindInterferenceForm(MainModel mainModel)
        {
            InitializeComponent();
            this.mainModel = mainModel;
        }

        private void numericUpDownBCCH_TextChanged(object sender, EventArgs e)
        {
            int value = (int)numericUpDownBCCH.Value;
            if (value == 125 || (value > 318 && value < 511))
            {
                numericUpDownBCCH.Value = 512;
            }
            else if ((value > 125 && value <= 318) || value == 511)
            {
                numericUpDownBCCH.Value = 124;
            }
        }

        private void buttonFind_Click(object sender, EventArgs e)
        {
            new InterferenceForm(mainModel, (short)numericUpDownBCCH.Value).Show(Owner);
        }

        private void buttonClose_Click(object sender, EventArgs e)
        {
            Close();
        }

        private void InitializeComponent()
        {
            System.Windows.Forms.Label labelBTS;
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FindInterferenceForm));
            this.buttonClose = new System.Windows.Forms.Button();
            this.numericUpDownBCCH = new System.Windows.Forms.NumericUpDown();
            this.buttonFind = new System.Windows.Forms.Button();
            labelBTS = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownBCCH)).BeginInit();
            this.SuspendLayout();
            // 
            // labelBTS
            // 
            labelBTS.AutoSize = true;
            labelBTS.Location = new System.Drawing.Point(12, 14);
            labelBTS.Name = "labelBTS";
            labelBTS.Size = new System.Drawing.Size(41, 12);
            labelBTS.TabIndex = 0;
            labelBTS.Text = "ARFCN:";
            // 
            // buttonClose
            // 
            this.buttonClose.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.buttonClose.Location = new System.Drawing.Point(93, 49);
            this.buttonClose.Name = "buttonClose";
            this.buttonClose.Size = new System.Drawing.Size(75, 23);
            this.buttonClose.TabIndex = 3;
            this.buttonClose.Text = "Close";
            this.buttonClose.UseVisualStyleBackColor = true;
            this.buttonClose.Click += new System.EventHandler(this.buttonClose_Click);
            // 
            // numericUpDownBCCH
            // 
            this.numericUpDownBCCH.Location = new System.Drawing.Point(64, 12);
            this.numericUpDownBCCH.Maximum = new decimal(new int[] {
            9999,
            0,
            0,
            0});
            this.numericUpDownBCCH.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numericUpDownBCCH.Name = "numericUpDownBCCH";
            this.numericUpDownBCCH.Size = new System.Drawing.Size(104, 21);
            this.numericUpDownBCCH.TabIndex = 1;
            this.numericUpDownBCCH.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numericUpDownBCCH.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // buttonFind
            // 
            this.buttonFind.Anchor = (System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left);
            this.buttonFind.Location = new System.Drawing.Point(12, 49);
            this.buttonFind.Name = "buttonFind";
            this.buttonFind.Size = new System.Drawing.Size(75, 23);
            this.buttonFind.TabIndex = 2;
            this.buttonFind.Text = "&Find";
            this.buttonFind.UseVisualStyleBackColor = true;
            this.buttonFind.Click += new System.EventHandler(this.buttonFind_Click);
            // 
            // FindInterferenceForm
            // 
            this.AcceptButton = this.buttonFind;
            this.CancelButton = this.buttonClose;
            this.ClientSize = new System.Drawing.Size(180, 84);
            this.Controls.Add(this.buttonFind);
            this.Controls.Add(this.numericUpDownBCCH);
            this.Controls.Add(this.buttonClose);
            this.Controls.Add(labelBTS);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FindInterferenceForm";
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Interference";
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownBCCH)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        private readonly MainModel mainModel;

        private NumericUpDown numericUpDownBCCH;

        private Button buttonFind;
        
        private Button buttonClose;
    }
}
