﻿using MasterCom.RAMS.Func;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{ 
    public partial class NRScanMultiCoverageGridForm : MinCloseForm
    {
        public NRScanMultiCoverageGridForm()
        {
            InitializeComponent();
            mapForm = MainModel.MainForm.GetMapForm();
        }

        MapForm mapForm;
        Dictionary<string, NRScanMultiCoverageGridInfo> dicResult;
        NRScanMultiCoverageGridCond condition;
        public void FillData(Dictionary<string, NRScanMultiCoverageGridInfo> dicResult
            , NRScanMultiCoverageGridCond condition)
        {
            this.dicResult = dicResult;
            this.condition = condition;

            var dicCov = new Dictionary<string, Dictionary<int, int>>();
            DataTable dt = new DataTable("重叠覆盖栅格信息");
            DataTable dtCov = new DataTable("重叠覆盖度");
            addColumns(dt, 1);
            addColumns(dtCov, 2);
            int i = 0;
            foreach (var info in dicResult.Values)
            {
                List<object> secValue1 = new List<object>();
                secValue1.Add(++i);
                secValue1.Add(info.MgrsString);
                secValue1.Add(info.CellList.Count);
                secValue1.Add(info.Coverage);
                secValue1.Add(info.CoverageCell);
                secValue1.Add(info.CentLng);
                secValue1.Add(info.CentLat);
                secValue1.Add(info.GridAvgSINR);
                var ListCell = info.CoverageCellList;
                for (int index = 0; index < 5; index++)
                {
                    if (ListCell != null && ListCell.Count - 1 >= index)
                    {
                        secValue1.Add(ListCell[index].CellName);
                        secValue1.Add(Math.Round(ListCell[index].Rsrp.Avg, 2));
                    }
                    else
                    {
                        addDefault(secValue1, 2);
                    }
                }
                dt.Rows.Add(secValue1.ToArray());
                setDicCov(info, dicCov);
            }
            addRows(dicCov, dtCov);
            fillData(dt, dtCov);
        }

        private void addColumns(DataTable tblDatas, int iflage)
        {
            if (iflage == 1)
            {
                tblDatas.Columns.Add("序号", typeof(int));
                tblDatas.Columns.Add("栅格号");
                tblDatas.Columns.Add("小区数", typeof(int));
                tblDatas.Columns.Add("重叠度", typeof(int));
                tblDatas.Columns.Add("重叠小区");
                tblDatas.Columns.Add("中心经度");
                tblDatas.Columns.Add("中心纬度");
                tblDatas.Columns.Add("栅格平均SINR");
                //前5强
                for (int i = 1; i <= 5; i++)
                {
                    tblDatas.Columns.Add(i.ToString() + "强小区名");
                    tblDatas.Columns.Add(i.ToString() + "强小区RSRP");
                }
            }
            if (iflage == 2)
            {
                tblDatas.Columns.Add("网格");
                //tblDatas.Columns.Add("频段");
                tblDatas.Columns.Add(string.Format("(-∞,{0})", 1));
                for (int i = 1; i < 10; i++)
                {
                    tblDatas.Columns.Add(string.Format("[{0},{1})", i, i + 1));
                }
                tblDatas.Columns.Add(string.Format("[{0},+∞)", 10));
                tblDatas.Columns.Add("无效栅格");
                tblDatas.Columns.Add("总栅格数");
                tblDatas.Columns.Add("重叠覆盖度");
            }
        }

        private void addDefault(List<object> secValue1, int count)
        {
            for (int i = 0; i < count; i++)
            {
                secValue1.Add(" ");
            }
        }

        private void setDicCov(NRScanMultiCoverageGridInfo info, Dictionary<string, Dictionary<int, int>> dicCov)
        {
            string strGrid = info.StrGrid;
            int iCoverage = info.Coverage > 10 ? 10 : info.Coverage;
            if (!dicCov.ContainsKey(strGrid))
            {
                dicCov[strGrid] = new Dictionary<int, int>();
            }
            if (!dicCov[strGrid].ContainsKey(iCoverage))
            {
                Dictionary<int, int> dicTmp = new Dictionary<int, int>();
                for (int idx = 0; idx <= 10; idx++)
                {
                    dicTmp.Add(idx, 0);
                }
                dicCov[strGrid] = dicTmp;
            }
            dicCov[strGrid][iCoverage]++;
        }

        private void addRows(Dictionary<string, Dictionary<int, int>> dicCov, DataTable dtCov)
        {
            foreach (string strGrid in dicCov.Keys)
            {
                List<object> secValue1 = new List<object>();
                secValue1.Add(strGrid);
                //secValue1.Add(getFreqBandType());
                secValue1.Add(0);
                int iGridSum = 0;
                int iCovCount = 0;
                for (int idx = 1; idx <= 10; idx++)
                {
                    secValue1.Add(dicCov[strGrid][idx]);
                    iGridSum += dicCov[strGrid][idx];
                    if (idx >= 4)
                    {
                        iCovCount += dicCov[strGrid][idx];
                    }
                }
                secValue1.Add(dicCov[strGrid][0]);
                secValue1.Add(iGridSum);
                secValue1.Add(string.Format("{0}%", Math.Round(iCovCount * 1.0 / iGridSum * 100, 2)));
                dtCov.Rows.Add(secValue1.ToArray());
            }
        }

        private void fillData(DataTable dt, DataTable dtCov)
        {
            if (dt != null)
            {
                gridControl1.DataSource = dt;
                gridControl1.RefreshDataSource();
            }
            if (dtCov != null)
            {
                gridControl2.DataSource = dtCov;
                gridControl2.RefreshDataSource();
            }
            RefreshResult();
        }

        private void RefreshResult()
        {
            var gisDataList = getGisData();
            MgrsCoveLayerBase gLayer = mapForm.GetLayerBase(typeof(MgrsCoveLayerBase)) as MgrsCoveLayerBase;
            if (gLayer != null)
            {
                gLayer.iniData(gisDataList);
            }
            MainModel.FireDTDataChanged(MainModel.MainForm);
        }

        private List<ScanMultiCoverageGridInfo> getGisData()
        {
            var gisDataList = new List<ScanMultiCoverageGridInfo>();

            for (int r = 0; r < gridViewDetail.RowCount; r++)
            {
                string strName = gridViewDetail.GetRowCellValue(r, gridViewDetail.Columns["栅格号"]).ToString();
                int iCoverage = Convert.ToInt32(gridViewDetail.GetRowCellValue(r, gridViewDetail.Columns["重叠度"]).ToString());
                Color c = NRScanMultiCoverageGridColorRanges.Instance.GetColor(iCoverage);
                var data = dicResult[strName];
                data.GridColor = c;
                gisDataList.Add(data);
            }
            return gisDataList;
        }

        private void GisShowItem_Click(object sender, EventArgs e)
        {
            RefreshResult();
        }

        private void ColorSettingItem_Click(object sender, EventArgs e)
        {
            LteMgrsColorRangeSettingDlg dlg = new LteMgrsColorRangeSettingDlg();
            dlg.FixMinMax(NRScanMultiCoverageGridColorRanges.Instance.Minimum, NRScanMultiCoverageGridColorRanges.Instance.Maximum);
            dlg.MakeRangeModeOnly();
            dlg.FillColorRanges(NRScanMultiCoverageGridColorRanges.Instance.ColorRanges);
            dlg.InvalidatePointColor = NRScanMultiCoverageGridColorRanges.Instance.InvalidColor;

            if (DialogResult.OK != dlg.ShowDialog(this))
            {
                return;
            }

            NRScanMultiCoverageGridColorRanges.Instance.ColorRanges = new List<MControls.ColorRange>(dlg.ColorRanges);
            NRScanMultiCoverageGridColorRanges.Instance.InvalidColor = dlg.InvalidatePointColor;

            RefreshResult();
        }

        private void ExportExcelItem_Click(object sender, EventArgs e)
        {
            SaveFileDialog saveDialog = new SaveFileDialog();
            saveDialog.Filter = FilterHelper.Csv;
            saveDialog.RestoreDirectory = true;
            if (saveDialog.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            WaitBox.Show("正在导出表格...", OutputCsvFile, saveDialog.FileName);
            string strFile = saveDialog.FileName.Replace(".csv", "");
            string strFileName = string.Format(@"{0}_{1}_{2:yyyyMMddHHmmss}.xlsx", strFile, "重叠覆盖度", DateTime.Now);
            ExcelNPOIManager.ExportToExcel(gridView2, strFileName, "重叠覆盖度");
            MessageBox.Show("导出完成");
        }

        private void OutputCsvFile(object filePath)
        {
            try
            {
                string strFileName = filePath as string;
                StreamWriter comSr = new StreamWriter(strFileName, true, Encoding.Default);
                StringBuilder colName = new StringBuilder();
                List<NRScanMultiCoverageGridInfo> listTmp = new List<NRScanMultiCoverageGridInfo>();
                listTmp.AddRange(dicResult.Values);
                listTmp.Sort(new Comparison<NRScanMultiCoverageGridInfo>((x, y) => { return y.Coverage.CompareTo(x.Coverage); }));
                int iMaxCoverage = listTmp[0].Coverage;
                for (int c = 0; c < gridViewDetail.Columns.Count; c++)
                {
                    if (c > 0)
                        colName.Append(",");
                    colName.Append(gridViewDetail.Columns[c].Caption == "" ? gridViewDetail.Columns[c].FieldName : gridViewDetail.Columns[c].Caption);
                }
                if (iMaxCoverage > 5)
                {
                    for (int c = 6; c <= iMaxCoverage; c++)
                    {
                        colName.Append(",");
                        colName.Append(c.ToString() + "强小区名");
                        colName.Append(",");
                        colName.Append(c.ToString() + "强小区RSRP");
                    }
                }
                bool exportlongLat = colName.ToString().Contains("经度");
                comSr.WriteLine(colName.ToString());
                int idx = 1;
                foreach (NRScanMultiCoverageGridInfo mgrs in listTmp)
                {
                    StringBuilder rows = getRows(iMaxCoverage, exportlongLat, ref idx, mgrs);
                    comSr.WriteLine(rows.ToString());
                }

                comSr.Flush();
                comSr.Close();
                WaitBox.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show("导出失败，error：" + ex.Message);
                WaitBox.Close();
            }
        }

        private static StringBuilder getRows(int iMaxCoverage, bool exportlongLat, ref int idx, NRScanMultiCoverageGridInfo mgrs)
        {
            StringBuilder rows = new StringBuilder();
            rows.Append(idx++ + ",");
            rows.Append(mgrs.MgrsString + ",");
            rows.Append(mgrs.CellList.Count + ",");
            rows.Append(mgrs.Coverage + ",");
            rows.Append(mgrs.CoverageCell + ",");
            if (exportlongLat)
            {
                rows.Append(mgrs.CentLng + ",");
                rows.Append(mgrs.CentLat + ",");
            }
            rows.Append(mgrs.GridAvgSINR + ",");
            List<MgrsCell> listCell = mgrs.CoverageCellList;
            for (int index = 0; index < iMaxCoverage; index++)
            {
                if (index > 0)
                    rows.Append(",");
                if (listCell.Count - 1 >= index)
                {
                    rows.Append(listCell[index].CellName + ",");
                    rows.Append(Math.Round(listCell[index].Rsrp.Avg, 2));
                    continue;
                }
                rows.Append(" " + ",");
                rows.Append(" ");
            }

            return rows;
        }

        private void gridViewDetail_DoubleClick(object sender, EventArgs e)
        {
            DataRowView dtView = gridViewDetail.GetFocusedRow() as DataRowView;
            if (dtView != null)
            {
                double longitude = Convert.ToDouble(dtView["中心经度"]);
                double latitude = Convert.ToDouble(dtView["中心纬度"]);

                mapForm.GoToView(longitude, latitude, 2000);
            }
        }
    }
}
