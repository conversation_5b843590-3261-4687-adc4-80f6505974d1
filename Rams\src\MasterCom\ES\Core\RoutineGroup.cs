﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.ES.Core
{
    public class RoutineGroup
    {
        public string Name { get; set; }
        public List<ProcRoutine> Routines { get; set; } = new List<ProcRoutine>();

        internal bool isProcNameExist(string name)
        {
            foreach(ProcRoutine proc in Routines)
            {
                if(name.ToUpper().Equals(proc.Name.ToUpper()))
                {
                    return true;
                }
            }
            return false;
        }
    }
}
