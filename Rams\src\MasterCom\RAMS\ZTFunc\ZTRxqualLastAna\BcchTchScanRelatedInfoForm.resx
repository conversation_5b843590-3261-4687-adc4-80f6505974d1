<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="columnHeaderSN.Text" xml:space="preserve">
    <value>序号</value>
  </data>
  <data name="columnHeaderMainCellName.Text" xml:space="preserve">
    <value>主服小区名称</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="columnHeaderMainCellName.Width" type="System.Int32, mscorlib">
    <value>98</value>
  </data>
  <data name="columnHeaderLAC.Text" xml:space="preserve">
    <value>LAC</value>
  </data>
  <data name="columnHeaderLAC.Width" type="System.Int32, mscorlib">
    <value>51</value>
  </data>
  <data name="columnHeaderCI.Text" xml:space="preserve">
    <value>CI</value>
  </data>
  <data name="columnHeaderCI.Width" type="System.Int32, mscorlib">
    <value>47</value>
  </data>
  <data name="columnHeadermainRxlevMean.Text" xml:space="preserve">
    <value>平均Rxlev</value>
  </data>
  <data name="columnHeadermainRxlevMean.Width" type="System.Int32, mscorlib">
    <value>67</value>
  </data>
  <data name="columnHeaderBCCH.Text" xml:space="preserve">
    <value>BCCH</value>
  </data>
  <data name="columnHeaderBCCH.Width" type="System.Int32, mscorlib">
    <value>47</value>
  </data>
  <data name="columnHeaderTCH.Text" xml:space="preserve">
    <value>TCH</value>
  </data>
  <data name="columnHeaderTCH.Width" type="System.Int32, mscorlib">
    <value>44</value>
  </data>
  <data name="columnHeaderScanCellName.Text" xml:space="preserve">
    <value>同邻频扫频小区名称</value>
  </data>
  <data name="columnHeaderScanCellName.Width" type="System.Int32, mscorlib">
    <value>123</value>
  </data>
  <data name="columnHeaderScanCellBCCH.Text" xml:space="preserve">
    <value>同邻频小区BCCH</value>
  </data>
  <data name="columnHeaderScanCellBCCH.Width" type="System.Int32, mscorlib">
    <value>105</value>
  </data>
  <data name="columnHeaderScanCellTCH.Text" xml:space="preserve">
    <value>同邻频小区TCH</value>
  </data>
  <data name="columnHeaderScanCellTCH.Width" type="System.Int32, mscorlib">
    <value>98</value>
  </data>
  <data name="columnHeaderScanCellRxlevMean.Text" xml:space="preserve">
    <value>同邻频扫频小区平均Rxlev</value>
  </data>
  <data name="columnHeaderScanCellRxlevMean.Width" type="System.Int32, mscorlib">
    <value>154</value>
  </data>
  <data name="columnHeaderGridNum.Text" xml:space="preserve">
    <value>栅格数</value>
  </data>
  <data name="columnHeaderDistance.Text" xml:space="preserve">
    <value>距离</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="listView.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="listView.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="listView.Size" type="System.Drawing.Size, System.Drawing">
    <value>965, 385</value>
  </data>
  <data name="listView.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;listView.Name" xml:space="preserve">
    <value>listView</value>
  </data>
  <data name="&gt;&gt;listView.Type" xml:space="preserve">
    <value>System.Windows.Forms.ListView, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;listView.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;listView.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <metadata name="contextMenuStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="ToolStripMenuItemExport.Size" type="System.Drawing.Size, System.Drawing">
    <value>142, 22</value>
  </data>
  <data name="ToolStripMenuItemExport.Text" xml:space="preserve">
    <value>导出到xls...</value>
  </data>
  <data name="contextMenuStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>143, 26</value>
  </data>
  <data name="&gt;&gt;contextMenuStrip1.Name" xml:space="preserve">
    <value>contextMenuStrip1</value>
  </data>
  <data name="&gt;&gt;contextMenuStrip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ContextMenuStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>7, 14</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>965, 385</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>扫频关联分析结果</value>
  </data>
  <data name="&gt;&gt;columnHeaderSN.Name" xml:space="preserve">
    <value>columnHeaderSN</value>
  </data>
  <data name="&gt;&gt;columnHeaderSN.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeaderMainCellName.Name" xml:space="preserve">
    <value>columnHeaderMainCellName</value>
  </data>
  <data name="&gt;&gt;columnHeaderMainCellName.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeaderLAC.Name" xml:space="preserve">
    <value>columnHeaderLAC</value>
  </data>
  <data name="&gt;&gt;columnHeaderLAC.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeaderCI.Name" xml:space="preserve">
    <value>columnHeaderCI</value>
  </data>
  <data name="&gt;&gt;columnHeaderCI.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeadermainRxlevMean.Name" xml:space="preserve">
    <value>columnHeadermainRxlevMean</value>
  </data>
  <data name="&gt;&gt;columnHeadermainRxlevMean.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeaderBCCH.Name" xml:space="preserve">
    <value>columnHeaderBCCH</value>
  </data>
  <data name="&gt;&gt;columnHeaderBCCH.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeaderTCH.Name" xml:space="preserve">
    <value>columnHeaderTCH</value>
  </data>
  <data name="&gt;&gt;columnHeaderTCH.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeaderScanCellName.Name" xml:space="preserve">
    <value>columnHeaderScanCellName</value>
  </data>
  <data name="&gt;&gt;columnHeaderScanCellName.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeaderScanCellBCCH.Name" xml:space="preserve">
    <value>columnHeaderScanCellBCCH</value>
  </data>
  <data name="&gt;&gt;columnHeaderScanCellBCCH.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeaderScanCellTCH.Name" xml:space="preserve">
    <value>columnHeaderScanCellTCH</value>
  </data>
  <data name="&gt;&gt;columnHeaderScanCellTCH.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeaderScanCellRxlevMean.Name" xml:space="preserve">
    <value>columnHeaderScanCellRxlevMean</value>
  </data>
  <data name="&gt;&gt;columnHeaderScanCellRxlevMean.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeaderGridNum.Name" xml:space="preserve">
    <value>columnHeaderGridNum</value>
  </data>
  <data name="&gt;&gt;columnHeaderGridNum.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;ToolStripMenuItemExport.Name" xml:space="preserve">
    <value>ToolStripMenuItemExport</value>
  </data>
  <data name="&gt;&gt;ToolStripMenuItemExport.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeaderDistance.Name" xml:space="preserve">
    <value>columnHeaderDistance</value>
  </data>
  <data name="&gt;&gt;columnHeaderDistance.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>BcchTchScanRelatedInfoForm</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>MasterCom.RAMS.Func.MinCloseForm, RAMS, Version=*******, Culture=neutral, PublicKeyToken=null</value>
  </data>
</root>