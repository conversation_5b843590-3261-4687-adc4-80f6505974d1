﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Xml;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public class CellParamTable
    {
        public CellParamTable(CellParamDataBase db)
        { this.DataBase = db; }
        public CellParamTable(string name)
        {
            this.name = name;
            this.Alias = name;
        }
        public CellParamTable(string name,string alias,string desc)
        {
            this.name = name;
            this.Alias = alias;
            this.Description = desc;
        }
        public override string ToString()
        {
            return name;
        }
        public CellParamDataBase DataBase
        {
            get;
            set;
        }
        private string name = string.Empty;
        public string Name
        {
            get { return name; }
        }
        public string FullName
        {
            get { return DataBase.Name + ".." + Name; }
        }
        
        public string CellIDForeignKey { get; set; } = "iCeLLID";
        public string CheckDateFieldName { get; set; } = "Checkdate";
        public List<CellParamColumn> Columns { get; set; } = new List<CellParamColumn>();
        public string Description { get; set; } = string.Empty;
        public string Alias { get; set; } = string.Empty;

        public Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> cfgDic = new Dictionary<string, object>();
                cfgDic["Name"] = name;
                cfgDic["Alias"] = Alias;
                cfgDic["Description"] = Description;
                cfgDic["CellIDForeignKey"] = CellIDForeignKey;
                cfgDic["CheckDateFieldName"] = CheckDateFieldName;
                List<object> colCfgs = new List<object>();
                foreach (CellParamColumn col in Columns)
                {
                    colCfgs.Add(col.CfgParam);
                }
                cfgDic["Colunms"] = colCfgs;
                return cfgDic;
            }
            set
            {
                if (value==null)
                {
                    return;
                }
                name=value["Name"].ToString();
                Alias = value["Alias"] as string;
                Description = value["Description"] as string;
                CellIDForeignKey = value["CellIDForeignKey"].ToString();
                List<object> colCfgs = value["Colunms"] as List<object>;
                if (value.ContainsKey("CheckDateFieldName"))
                {
                    CheckDateFieldName = value["CheckDateFieldName"] as string;
                }
                foreach (object cfg in colCfgs)
                {
                    CellParamColumn col = new CellParamColumn(this);
                    col.CfgParam = cfg as Dictionary<string, object>;
                    Columns.Add(col);
                }
            }
        }


        internal bool TryGetColunmByFullName(string fullColName,out CellParamColumn col)
        {
            col = null;
            if (fullColName.IndexOf(name) >= 0)
            {
                col = Columns.Find(delegate(CellParamColumn c)
                              { return c.FullName.Equals(fullColName); });
                return col != null;
            }
            else
            {
                return false;
            }
        }
    }
}
