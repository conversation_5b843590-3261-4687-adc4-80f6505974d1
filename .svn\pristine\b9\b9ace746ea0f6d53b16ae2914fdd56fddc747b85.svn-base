﻿namespace MasterCom.RAMS.Func
{
    partial class MapLTECellLayerCellProperties
    {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.grpFactor = new System.Windows.Forms.GroupBox();
            this.radioBtnMod6 = new System.Windows.Forms.RadioButton();
            this.radioBtnMod3 = new System.Windows.Forms.RadioButton();
            this.radioBtnDefault = new System.Windows.Forms.RadioButton();
            this.TrackBarOpacity = new System.Windows.Forms.TrackBar();
            this.colorCellMod3_2 = new DevExpress.XtraEditors.ColorEdit();
            this.colorCellMod3_1 = new DevExpress.XtraEditors.ColorEdit();
            this.colorCellMod6_5 = new DevExpress.XtraEditors.ColorEdit();
            this.colorCellMod6_4 = new DevExpress.XtraEditors.ColorEdit();
            this.colorCellMod6_3 = new DevExpress.XtraEditors.ColorEdit();
            this.colorCellMod6_2 = new DevExpress.XtraEditors.ColorEdit();
            this.colorCellMod6_1 = new DevExpress.XtraEditors.ColorEdit();
            this.colorCellMod6_0 = new DevExpress.XtraEditors.ColorEdit();
            this.colorCellMod3_0 = new DevExpress.XtraEditors.ColorEdit();
            this.colorCell = new DevExpress.XtraEditors.ColorEdit();
            this.label100 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.LabelOpacity = new System.Windows.Forms.Label();
            this.label0 = new System.Windows.Forms.Label();
            this.checkBoxDisplay = new System.Windows.Forms.CheckBox();
            this.btnFont = new DevExpress.XtraEditors.SimpleButton();
            this.cbxDrawCellLabel = new System.Windows.Forms.CheckBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.cbxCellCPI = new System.Windows.Forms.CheckBox();
            this.cbxCellDes = new System.Windows.Forms.CheckBox();
            this.cbxCellFreqList = new System.Windows.Forms.CheckBox();
            this.cbxCellFreq = new System.Windows.Forms.CheckBox();
            this.cbxCellCI = new System.Windows.Forms.CheckBox();
            this.cbxCellLAC = new System.Windows.Forms.CheckBox();
            this.cbxCellCode = new System.Windows.Forms.CheckBox();
            this.cbxCellName = new System.Windows.Forms.CheckBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.numBandF = new System.Windows.Forms.NumericUpDown();
            this.numBandE = new System.Windows.Forms.NumericUpDown();
            this.numBandD = new System.Windows.Forms.NumericUpDown();
            this.numBandA = new System.Windows.Forms.NumericUpDown();
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.colorF = new DevExpress.XtraEditors.ColorEdit();
            this.colorD = new DevExpress.XtraEditors.ColorEdit();
            this.colorA = new DevExpress.XtraEditors.ColorEdit();
            this.colorE = new DevExpress.XtraEditors.ColorEdit();
            this.Label = new System.Windows.Forms.Label();
            this.colorLable = new DevExpress.XtraEditors.ColorEdit();
            this.grpFactor.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.TrackBarOpacity)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorCellMod3_2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorCellMod3_1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorCellMod6_5.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorCellMod6_4.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorCellMod6_3.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorCellMod6_2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorCellMod6_1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorCellMod6_0.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorCellMod3_0.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorCell.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numBandF)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numBandE)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numBandD)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numBandA)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorF.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorD.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorA.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorE.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorLable.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // grpFactor
            // 
            this.grpFactor.Controls.Add(this.radioBtnMod6);
            this.grpFactor.Controls.Add(this.radioBtnMod3);
            this.grpFactor.Controls.Add(this.radioBtnDefault);
            this.grpFactor.Controls.Add(this.TrackBarOpacity);
            this.grpFactor.Controls.Add(this.colorCellMod3_2);
            this.grpFactor.Controls.Add(this.colorCellMod3_1);
            this.grpFactor.Controls.Add(this.colorCellMod6_5);
            this.grpFactor.Controls.Add(this.colorCellMod6_4);
            this.grpFactor.Controls.Add(this.colorCellMod6_3);
            this.grpFactor.Controls.Add(this.colorCellMod6_2);
            this.grpFactor.Controls.Add(this.colorCellMod6_1);
            this.grpFactor.Controls.Add(this.colorCellMod6_0);
            this.grpFactor.Controls.Add(this.colorCellMod3_0);
            this.grpFactor.Controls.Add(this.colorCell);
            this.grpFactor.Controls.Add(this.label100);
            this.grpFactor.Controls.Add(this.label8);
            this.grpFactor.Controls.Add(this.LabelOpacity);
            this.grpFactor.Controls.Add(this.label0);
            this.grpFactor.Controls.Add(this.checkBoxDisplay);
            this.grpFactor.Location = new System.Drawing.Point(24, 3);
            this.grpFactor.Name = "grpFactor";
            this.grpFactor.Size = new System.Drawing.Size(489, 162);
            this.grpFactor.TabIndex = 87;
            this.grpFactor.TabStop = false;
            // 
            // radioBtnMod6
            // 
            this.radioBtnMod6.AutoSize = true;
            this.radioBtnMod6.Location = new System.Drawing.Point(28, 92);
            this.radioBtnMod6.Name = "radioBtnMod6";
            this.radioBtnMod6.Size = new System.Drawing.Size(59, 16);
            this.radioBtnMod6.TabIndex = 85;
            this.radioBtnMod6.TabStop = true;
            this.radioBtnMod6.Text = "PCI模6";
            this.radioBtnMod6.UseVisualStyleBackColor = true;
            this.radioBtnMod6.CheckedChanged += new System.EventHandler(this.radioBtnMod6_CheckedChanged);
            // 
            // radioBtnMod3
            // 
            this.radioBtnMod3.AutoSize = true;
            this.radioBtnMod3.Location = new System.Drawing.Point(28, 57);
            this.radioBtnMod3.Name = "radioBtnMod3";
            this.radioBtnMod3.Size = new System.Drawing.Size(59, 16);
            this.radioBtnMod3.TabIndex = 85;
            this.radioBtnMod3.TabStop = true;
            this.radioBtnMod3.Text = "PCI模3";
            this.radioBtnMod3.UseVisualStyleBackColor = true;
            this.radioBtnMod3.CheckedChanged += new System.EventHandler(this.radioBtnMod3_CheckedChanged);
            // 
            // radioBtnDefault
            // 
            this.radioBtnDefault.AutoSize = true;
            this.radioBtnDefault.Location = new System.Drawing.Point(28, 22);
            this.radioBtnDefault.Name = "radioBtnDefault";
            this.radioBtnDefault.Size = new System.Drawing.Size(71, 16);
            this.radioBtnDefault.TabIndex = 85;
            this.radioBtnDefault.TabStop = true;
            this.radioBtnDefault.Text = "统一着色";
            this.radioBtnDefault.UseVisualStyleBackColor = true;
            this.radioBtnDefault.CheckedChanged += new System.EventHandler(this.radioBtnDefault_CheckedChanged);
            // 
            // TrackBarOpacity
            // 
            this.TrackBarOpacity.AutoSize = false;
            this.TrackBarOpacity.LargeChange = 32;
            this.TrackBarOpacity.Location = new System.Drawing.Point(139, 125);
            this.TrackBarOpacity.Maximum = 255;
            this.TrackBarOpacity.Name = "TrackBarOpacity";
            this.TrackBarOpacity.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.TrackBarOpacity.Size = new System.Drawing.Size(187, 28);
            this.TrackBarOpacity.TabIndex = 74;
            this.TrackBarOpacity.TickFrequency = 32;
            this.TrackBarOpacity.Value = 255;
            // 
            // colorCellMod3_2
            // 
            this.colorCellMod3_2.EditValue = System.Drawing.Color.Black;
            this.colorCellMod3_2.Location = new System.Drawing.Point(224, 52);
            this.colorCellMod3_2.Name = "colorCellMod3_2";
            this.colorCellMod3_2.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorCellMod3_2.Properties.ShowWebColors = false;
            this.colorCellMod3_2.Size = new System.Drawing.Size(40, 21);
            this.colorCellMod3_2.TabIndex = 84;
            // 
            // colorCellMod3_1
            // 
            this.colorCellMod3_1.EditValue = System.Drawing.Color.Black;
            this.colorCellMod3_1.Location = new System.Drawing.Point(164, 52);
            this.colorCellMod3_1.Name = "colorCellMod3_1";
            this.colorCellMod3_1.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorCellMod3_1.Properties.ShowWebColors = false;
            this.colorCellMod3_1.Size = new System.Drawing.Size(40, 21);
            this.colorCellMod3_1.TabIndex = 84;
            // 
            // colorCellMod6_5
            // 
            this.colorCellMod6_5.EditValue = System.Drawing.Color.Black;
            this.colorCellMod6_5.Location = new System.Drawing.Point(408, 92);
            this.colorCellMod6_5.Name = "colorCellMod6_5";
            this.colorCellMod6_5.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorCellMod6_5.Properties.ShowWebColors = false;
            this.colorCellMod6_5.Size = new System.Drawing.Size(40, 21);
            this.colorCellMod6_5.TabIndex = 84;
            // 
            // colorCellMod6_4
            // 
            this.colorCellMod6_4.EditValue = System.Drawing.Color.Black;
            this.colorCellMod6_4.Location = new System.Drawing.Point(345, 92);
            this.colorCellMod6_4.Name = "colorCellMod6_4";
            this.colorCellMod6_4.Properties.Appearance.BackColor = System.Drawing.Color.White;
            this.colorCellMod6_4.Properties.Appearance.Options.UseBackColor = true;
            this.colorCellMod6_4.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorCellMod6_4.Properties.ShowWebColors = false;
            this.colorCellMod6_4.Size = new System.Drawing.Size(40, 21);
            this.colorCellMod6_4.TabIndex = 84;
            // 
            // colorCellMod6_3
            // 
            this.colorCellMod6_3.EditValue = System.Drawing.Color.Black;
            this.colorCellMod6_3.Location = new System.Drawing.Point(286, 92);
            this.colorCellMod6_3.Name = "colorCellMod6_3";
            this.colorCellMod6_3.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorCellMod6_3.Properties.ShowWebColors = false;
            this.colorCellMod6_3.Size = new System.Drawing.Size(40, 21);
            this.colorCellMod6_3.TabIndex = 84;
            // 
            // colorCellMod6_2
            // 
            this.colorCellMod6_2.EditValue = System.Drawing.Color.Black;
            this.colorCellMod6_2.Location = new System.Drawing.Point(224, 92);
            this.colorCellMod6_2.Name = "colorCellMod6_2";
            this.colorCellMod6_2.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorCellMod6_2.Properties.ShowWebColors = false;
            this.colorCellMod6_2.Size = new System.Drawing.Size(40, 21);
            this.colorCellMod6_2.TabIndex = 84;
            // 
            // colorCellMod6_1
            // 
            this.colorCellMod6_1.EditValue = System.Drawing.Color.Black;
            this.colorCellMod6_1.Location = new System.Drawing.Point(164, 92);
            this.colorCellMod6_1.Name = "colorCellMod6_1";
            this.colorCellMod6_1.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorCellMod6_1.Properties.ShowWebColors = false;
            this.colorCellMod6_1.Size = new System.Drawing.Size(40, 21);
            this.colorCellMod6_1.TabIndex = 84;
            // 
            // colorCellMod6_0
            // 
            this.colorCellMod6_0.EditValue = System.Drawing.Color.Black;
            this.colorCellMod6_0.Location = new System.Drawing.Point(106, 92);
            this.colorCellMod6_0.Name = "colorCellMod6_0";
            this.colorCellMod6_0.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorCellMod6_0.Properties.ShowWebColors = false;
            this.colorCellMod6_0.Size = new System.Drawing.Size(40, 21);
            this.colorCellMod6_0.TabIndex = 84;
            // 
            // colorCellMod3_0
            // 
            this.colorCellMod3_0.EditValue = System.Drawing.Color.Black;
            this.colorCellMod3_0.Location = new System.Drawing.Point(106, 52);
            this.colorCellMod3_0.Name = "colorCellMod3_0";
            this.colorCellMod3_0.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorCellMod3_0.Properties.ShowWebColors = false;
            this.colorCellMod3_0.Size = new System.Drawing.Size(40, 21);
            this.colorCellMod3_0.TabIndex = 84;
            // 
            // colorCell
            // 
            this.colorCell.EditValue = System.Drawing.Color.Black;
            this.colorCell.Location = new System.Drawing.Point(106, 20);
            this.colorCell.Name = "colorCell";
            this.colorCell.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorCell.Properties.ShowWebColors = false;
            this.colorCell.Size = new System.Drawing.Size(40, 21);
            this.colorCell.TabIndex = 84;
            // 
            // label100
            // 
            this.label100.AutoSize = true;
            this.label100.Location = new System.Drawing.Point(332, 131);
            this.label100.Name = "label100";
            this.label100.Size = new System.Drawing.Size(53, 12);
            this.label100.TabIndex = 77;
            this.label100.Text = "100%透明";
            this.label100.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(118, 97);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(0, 12);
            this.label8.TabIndex = 76;
            this.label8.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // LabelOpacity
            // 
            this.LabelOpacity.AutoSize = true;
            this.LabelOpacity.Location = new System.Drawing.Point(26, 131);
            this.LabelOpacity.Name = "LabelOpacity";
            this.LabelOpacity.Size = new System.Drawing.Size(47, 12);
            this.LabelOpacity.TabIndex = 73;
            this.LabelOpacity.Text = "透明度:";
            // 
            // label0
            // 
            this.label0.AutoSize = true;
            this.label0.Location = new System.Drawing.Point(92, 131);
            this.label0.Name = "label0";
            this.label0.Size = new System.Drawing.Size(41, 12);
            this.label0.TabIndex = 76;
            this.label0.Text = "不透明";
            this.label0.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // checkBoxDisplay
            // 
            this.checkBoxDisplay.AutoSize = true;
            this.checkBoxDisplay.Location = new System.Drawing.Point(13, 0);
            this.checkBoxDisplay.Name = "checkBoxDisplay";
            this.checkBoxDisplay.Size = new System.Drawing.Size(72, 16);
            this.checkBoxDisplay.TabIndex = 78;
            this.checkBoxDisplay.Text = "显示图元";
            this.checkBoxDisplay.UseVisualStyleBackColor = true;
            // 
            // btnFont
            // 
            this.btnFont.Location = new System.Drawing.Point(399, 45);
            this.btnFont.Name = "btnFont";
            this.btnFont.Size = new System.Drawing.Size(75, 23);
            this.btnFont.TabIndex = 77;
            this.btnFont.Text = "字体...";
            // 
            // cbxDrawCellLabel
            // 
            this.cbxDrawCellLabel.AutoSize = true;
            this.cbxDrawCellLabel.Location = new System.Drawing.Point(13, 0);
            this.cbxDrawCellLabel.Name = "cbxDrawCellLabel";
            this.cbxDrawCellLabel.Size = new System.Drawing.Size(72, 16);
            this.cbxDrawCellLabel.TabIndex = 79;
            this.cbxDrawCellLabel.Text = "显示标签";
            this.cbxDrawCellLabel.UseVisualStyleBackColor = true;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.Label);
            this.groupBox1.Controls.Add(this.colorLable);
            this.groupBox1.Controls.Add(this.cbxDrawCellLabel);
            this.groupBox1.Controls.Add(this.cbxCellCPI);
            this.groupBox1.Controls.Add(this.btnFont);
            this.groupBox1.Controls.Add(this.cbxCellDes);
            this.groupBox1.Controls.Add(this.cbxCellFreqList);
            this.groupBox1.Controls.Add(this.cbxCellFreq);
            this.groupBox1.Controls.Add(this.cbxCellCI);
            this.groupBox1.Controls.Add(this.cbxCellLAC);
            this.groupBox1.Controls.Add(this.cbxCellCode);
            this.groupBox1.Controls.Add(this.cbxCellName);
            this.groupBox1.Location = new System.Drawing.Point(24, 171);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(489, 74);
            this.groupBox1.TabIndex = 78;
            this.groupBox1.TabStop = false;
            // 
            // cbxCellCPI
            // 
            this.cbxCellCPI.AutoSize = true;
            this.cbxCellCPI.Location = new System.Drawing.Point(378, 24);
            this.cbxCellCPI.Name = "cbxCellCPI";
            this.cbxCellCPI.Size = new System.Drawing.Size(42, 16);
            this.cbxCellCPI.TabIndex = 8;
            this.cbxCellCPI.Text = "PCI";
            this.cbxCellCPI.UseVisualStyleBackColor = true;
            // 
            // cbxCellDes
            // 
            this.cbxCellDes.AutoSize = true;
            this.cbxCellDes.Location = new System.Drawing.Point(430, 24);
            this.cbxCellDes.Name = "cbxCellDes";
            this.cbxCellDes.Size = new System.Drawing.Size(48, 16);
            this.cbxCellDes.TabIndex = 7;
            this.cbxCellDes.Text = "描述";
            this.cbxCellDes.UseVisualStyleBackColor = true;
            // 
            // cbxCellFreqList
            // 
            this.cbxCellFreqList.AutoSize = true;
            this.cbxCellFreqList.Location = new System.Drawing.Point(299, 24);
            this.cbxCellFreqList.Name = "cbxCellFreqList";
            this.cbxCellFreqList.Size = new System.Drawing.Size(72, 16);
            this.cbxCellFreqList.TabIndex = 6;
            this.cbxCellFreqList.Text = "频点列表";
            this.cbxCellFreqList.UseVisualStyleBackColor = true;
            // 
            // cbxCellFreq
            // 
            this.cbxCellFreq.AutoSize = true;
            this.cbxCellFreq.Location = new System.Drawing.Point(233, 24);
            this.cbxCellFreq.Name = "cbxCellFreq";
            this.cbxCellFreq.Size = new System.Drawing.Size(60, 16);
            this.cbxCellFreq.TabIndex = 4;
            this.cbxCellFreq.Text = "EARFCN";
            this.cbxCellFreq.UseVisualStyleBackColor = true;
            // 
            // cbxCellCI
            // 
            this.cbxCellCI.AutoSize = true;
            this.cbxCellCI.Location = new System.Drawing.Point(187, 24);
            this.cbxCellCI.Name = "cbxCellCI";
            this.cbxCellCI.Size = new System.Drawing.Size(42, 16);
            this.cbxCellCI.TabIndex = 3;
            this.cbxCellCI.Text = "ECI";
            this.cbxCellCI.UseVisualStyleBackColor = true;
            // 
            // cbxCellLAC
            // 
            this.cbxCellLAC.AutoSize = true;
            this.cbxCellLAC.Location = new System.Drawing.Point(133, 24);
            this.cbxCellLAC.Name = "cbxCellLAC";
            this.cbxCellLAC.Size = new System.Drawing.Size(42, 16);
            this.cbxCellLAC.TabIndex = 2;
            this.cbxCellLAC.Text = "TAC";
            this.cbxCellLAC.UseVisualStyleBackColor = true;
            // 
            // cbxCellCode
            // 
            this.cbxCellCode.AutoSize = true;
            this.cbxCellCode.Location = new System.Drawing.Point(79, 24);
            this.cbxCellCode.Name = "cbxCellCode";
            this.cbxCellCode.Size = new System.Drawing.Size(48, 16);
            this.cbxCellCode.TabIndex = 1;
            this.cbxCellCode.Text = "编码";
            this.cbxCellCode.UseVisualStyleBackColor = true;
            // 
            // cbxCellName
            // 
            this.cbxCellName.AutoSize = true;
            this.cbxCellName.Checked = true;
            this.cbxCellName.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbxCellName.Location = new System.Drawing.Point(25, 24);
            this.cbxCellName.Name = "cbxCellName";
            this.cbxCellName.Size = new System.Drawing.Size(48, 16);
            this.cbxCellName.TabIndex = 0;
            this.cbxCellName.Text = "名称";
            this.cbxCellName.UseVisualStyleBackColor = true;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.numBandF);
            this.groupBox2.Controls.Add(this.numBandE);
            this.groupBox2.Controls.Add(this.numBandD);
            this.groupBox2.Controls.Add(this.numBandA);
            this.groupBox2.Controls.Add(this.label4);
            this.groupBox2.Controls.Add(this.label3);
            this.groupBox2.Controls.Add(this.label2);
            this.groupBox2.Controls.Add(this.label9);
            this.groupBox2.Controls.Add(this.label7);
            this.groupBox2.Controls.Add(this.label6);
            this.groupBox2.Controls.Add(this.label5);
            this.groupBox2.Controls.Add(this.label1);
            this.groupBox2.Controls.Add(this.colorF);
            this.groupBox2.Controls.Add(this.colorD);
            this.groupBox2.Controls.Add(this.colorA);
            this.groupBox2.Controls.Add(this.colorE);
            this.groupBox2.Location = new System.Drawing.Point(24, 251);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(489, 55);
            this.groupBox2.TabIndex = 88;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "频点渲染设置";
            // 
            // numBandF
            // 
            this.numBandF.DecimalPlaces = 1;
            this.numBandF.Location = new System.Drawing.Point(430, 49);
            this.numBandF.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numBandF.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numBandF.Name = "numBandF";
            this.numBandF.Size = new System.Drawing.Size(44, 21);
            this.numBandF.TabIndex = 90;
            this.numBandF.Value = new decimal(new int[] {
            16,
            0,
            0,
            65536});
            this.numBandF.Visible = false;
            // 
            // numBandE
            // 
            this.numBandE.DecimalPlaces = 1;
            this.numBandE.Increment = new decimal(new int[] {
            2,
            0,
            0,
            65536});
            this.numBandE.Location = new System.Drawing.Point(310, 49);
            this.numBandE.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numBandE.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numBandE.Name = "numBandE";
            this.numBandE.Size = new System.Drawing.Size(44, 21);
            this.numBandE.TabIndex = 90;
            this.numBandE.Value = new decimal(new int[] {
            14,
            0,
            0,
            65536});
            this.numBandE.Visible = false;
            // 
            // numBandD
            // 
            this.numBandD.DecimalPlaces = 1;
            this.numBandD.Increment = new decimal(new int[] {
            2,
            0,
            0,
            65536});
            this.numBandD.Location = new System.Drawing.Point(191, 49);
            this.numBandD.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numBandD.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numBandD.Name = "numBandD";
            this.numBandD.Size = new System.Drawing.Size(44, 21);
            this.numBandD.TabIndex = 90;
            this.numBandD.Value = new decimal(new int[] {
            12,
            0,
            0,
            65536});
            this.numBandD.Visible = false;
            // 
            // numBandA
            // 
            this.numBandA.DecimalPlaces = 1;
            this.numBandA.Increment = new decimal(new int[] {
            2,
            0,
            0,
            65536});
            this.numBandA.Location = new System.Drawing.Point(66, 49);
            this.numBandA.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numBandA.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numBandA.Name = "numBandA";
            this.numBandA.Size = new System.Drawing.Size(44, 21);
            this.numBandA.TabIndex = 90;
            this.numBandA.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numBandA.Visible = false;
            // 
            // label4
            // 
            this.label4.Location = new System.Drawing.Point(383, 24);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(37, 20);
            this.label4.TabIndex = 89;
            this.label4.Text = "F频段";
            this.label4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label3
            // 
            this.label3.Location = new System.Drawing.Point(144, 23);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(37, 20);
            this.label3.TabIndex = 88;
            this.label3.Text = "D频段";
            this.label3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label2
            // 
            this.label2.Location = new System.Drawing.Point(262, 24);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(37, 20);
            this.label2.TabIndex = 87;
            this.label2.Text = "E频段";
            this.label2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label9
            // 
            this.label9.Location = new System.Drawing.Point(387, 49);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(37, 20);
            this.label9.TabIndex = 86;
            this.label9.Text = "长度";
            this.label9.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.label9.Visible = false;
            // 
            // label7
            // 
            this.label7.Location = new System.Drawing.Point(267, 49);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(37, 20);
            this.label7.TabIndex = 86;
            this.label7.Text = "长度";
            this.label7.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.label7.Visible = false;
            // 
            // label6
            // 
            this.label6.Location = new System.Drawing.Point(148, 49);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(37, 20);
            this.label6.TabIndex = 86;
            this.label6.Text = "长度";
            this.label6.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.label6.Visible = false;
            // 
            // label5
            // 
            this.label5.Location = new System.Drawing.Point(26, 49);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(37, 20);
            this.label5.TabIndex = 86;
            this.label5.Text = "长度";
            this.label5.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.label5.Visible = false;
            // 
            // label1
            // 
            this.label1.Location = new System.Drawing.Point(23, 23);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(37, 20);
            this.label1.TabIndex = 86;
            this.label1.Text = "A频段";
            this.label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // colorF
            // 
            this.colorF.EditValue = System.Drawing.Color.Pink;
            this.colorF.Location = new System.Drawing.Point(430, 23);
            this.colorF.Name = "colorF";
            this.colorF.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorF.Properties.ShowWebColors = false;
            this.colorF.Size = new System.Drawing.Size(44, 21);
            this.colorF.TabIndex = 85;
            // 
            // colorD
            // 
            this.colorD.EditValue = System.Drawing.Color.Gray;
            this.colorD.Location = new System.Drawing.Point(191, 23);
            this.colorD.Name = "colorD";
            this.colorD.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorD.Properties.ShowWebColors = false;
            this.colorD.Size = new System.Drawing.Size(44, 21);
            this.colorD.TabIndex = 85;
            // 
            // colorA
            // 
            this.colorA.EditValue = System.Drawing.Color.Gold;
            this.colorA.Location = new System.Drawing.Point(66, 22);
            this.colorA.Name = "colorA";
            this.colorA.Properties.Appearance.ForeColor = System.Drawing.Color.Black;
            this.colorA.Properties.Appearance.Options.UseForeColor = true;
            this.colorA.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorA.Properties.ShowWebColors = false;
            this.colorA.Size = new System.Drawing.Size(44, 21);
            this.colorA.TabIndex = 85;
            // 
            // colorE
            // 
            this.colorE.EditValue = System.Drawing.Color.Green;
            this.colorE.Location = new System.Drawing.Point(310, 24);
            this.colorE.Name = "colorE";
            this.colorE.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorE.Properties.ShowWebColors = false;
            this.colorE.Size = new System.Drawing.Size(44, 21);
            this.colorE.TabIndex = 85;
            // 
            // Label
            // 
            this.Label.Location = new System.Drawing.Point(279, 46);
            this.Label.Name = "Label";
            this.Label.Size = new System.Drawing.Size(56, 20);
            this.Label.TabIndex = 95;
            this.Label.Text = "标签颜色";
            this.Label.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // colorLable
            // 
            this.colorLable.EditValue = System.Drawing.Color.Black;
            this.colorLable.Location = new System.Drawing.Point(341, 46);
            this.colorLable.Name = "colorLable";
            this.colorLable.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorLable.Properties.ShowWebColors = false;
            this.colorLable.Size = new System.Drawing.Size(44, 21);
            this.colorLable.TabIndex = 94;
            // 
            // MapLTECellLayerCellProperties
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.grpFactor);
            this.Name = "MapLTECellLayerCellProperties";
            this.Size = new System.Drawing.Size(537, 337);
            this.grpFactor.ResumeLayout(false);
            this.grpFactor.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.TrackBarOpacity)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorCellMod3_2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorCellMod3_1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorCellMod6_5.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorCellMod6_4.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorCellMod6_3.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorCellMod6_2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorCellMod6_1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorCellMod6_0.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorCellMod3_0.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorCell.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.numBandF)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numBandE)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numBandD)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numBandA)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorF.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorD.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorA.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorE.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorLable.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox grpFactor;
        private System.Windows.Forms.TrackBar TrackBarOpacity;
        private DevExpress.XtraEditors.ColorEdit colorCell;
        private System.Windows.Forms.Label label100;
        private System.Windows.Forms.Label LabelOpacity;
        private System.Windows.Forms.Label label0;
        private System.Windows.Forms.CheckBox checkBoxDisplay;
        private DevExpress.XtraEditors.SimpleButton btnFont;
        private System.Windows.Forms.CheckBox cbxDrawCellLabel;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.CheckBox cbxCellCPI;
        private System.Windows.Forms.CheckBox cbxCellDes;
        private System.Windows.Forms.CheckBox cbxCellFreqList;
        private System.Windows.Forms.CheckBox cbxCellFreq;
        private System.Windows.Forms.CheckBox cbxCellCI;
        private System.Windows.Forms.CheckBox cbxCellLAC;
        private System.Windows.Forms.CheckBox cbxCellCode;
        private System.Windows.Forms.CheckBox cbxCellName;
        private System.Windows.Forms.GroupBox groupBox2;
        private DevExpress.XtraEditors.ColorEdit colorF;
        private DevExpress.XtraEditors.ColorEdit colorD;
        private DevExpress.XtraEditors.ColorEdit colorA;
        private DevExpress.XtraEditors.ColorEdit colorE;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.RadioButton radioBtnMod6;
        private System.Windows.Forms.RadioButton radioBtnMod3;
        private System.Windows.Forms.RadioButton radioBtnDefault;
        private DevExpress.XtraEditors.ColorEdit colorCellMod3_2;
        private DevExpress.XtraEditors.ColorEdit colorCellMod3_1;
        private DevExpress.XtraEditors.ColorEdit colorCellMod6_5;
        private DevExpress.XtraEditors.ColorEdit colorCellMod6_3;
        private DevExpress.XtraEditors.ColorEdit colorCellMod6_2;
        private DevExpress.XtraEditors.ColorEdit colorCellMod6_1;
        private DevExpress.XtraEditors.ColorEdit colorCellMod6_0;
        private DevExpress.XtraEditors.ColorEdit colorCellMod3_0;
        private System.Windows.Forms.Label label8;
        private DevExpress.XtraEditors.ColorEdit colorCellMod6_4;
        private System.Windows.Forms.NumericUpDown numBandF;
        private System.Windows.Forms.NumericUpDown numBandE;
        private System.Windows.Forms.NumericUpDown numBandD;
        private System.Windows.Forms.NumericUpDown numBandA;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label Label;
        private DevExpress.XtraEditors.ColorEdit colorLable;
    }
}
