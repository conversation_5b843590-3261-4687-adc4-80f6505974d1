﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.CQT;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTCsfbCallStat;
using MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause;
using MasterCom.Util;
using NPOI.HSSF.Record;
using Message = MasterCom.RAMS.Model.Message;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteFddCsfbCallStatByRegion : CsfbCallStatQuery
    {
        private static LteFddCsfbCallStatByRegion instance = null;
        public static new LteFddCsfbCallStatByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteFddCsfbCallStatByRegion();
                    }
                }
            }
            return instance;
        }

        protected LteFddCsfbCallStatByRegion()
        {
            this.IncludeMessage = true;
            this.Columns = new List<string>();
            Columns.Add("lte_fdd_RSRP");
            Columns.Add("lte_fdd_TAC");
            Columns.Add("lte_fdd_ECI");
            Columns.Add("lte_fdd_gsm_SC_LAC");
            Columns.Add("lte_fdd_gsm_SC_CI");
            Columns.Add("lte_fdd_gsm_DM_RxLevBCCH");
            Columns.Add("lte_fdd_gsm_DM_RxLevSub");
            Columns.Add("lte_fdd_wcdma_SysLAI");
            Columns.Add("lte_fdd_wcdma_SysCellID");
            Columns.Add("lte_fdd_wcdma_Reference_PSC");
            Columns.Add("lte_fdd_wcdma_TotalRSCP");

            MoCallAttemptEvtIdList = new List<int> { 3070, 3021, 3041 };
            MtCallAttemptEvtIdList = new List<int> { 3071, 3022, 3042 };
        }
        public override string Name
        {
            get
            {
                return "LTEFDD-CSFB呼叫时延统计(区域文件)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26015, this.Name);
        }

        protected override void getCellInfoBeforeCSFB(CallInfo call, DTFileDataManager file, ref int lastTpIdx)
        {
            for (int i = lastTpIdx; i < file.TestPoints.Count; i++)
            {
                TestPoint tp = file.TestPoints[i];
                searchCsfbPrevInfo(call, file, i, tp);

                bool isAttempt = searchCsfbAttemptInfo(call, ref lastTpIdx, i, tp);
                if (isAttempt)
                {
                    break;
                }
            }
        }

        private void searchCsfbPrevInfo(CallInfo call, DTFileDataManager file, int i, TestPoint tp)
        {
            if (call.EvtCsfbCallRequest != null
                && (call.Rsrp == null || call.Tac == null || call.Eci == null)
                && tp.SN > call.EvtCsfbCallRequest.SN)
            {//回落情况
                setCsfbPrevInfo(call, file, i);
            }
        }

        private void setCsfbPrevInfo(CallInfo call, DTFileDataManager file, int i)
        {
            for (int preIdx = i - 1; preIdx >= 0; preIdx--)
            {//往前找
                TestPoint prePoint = file.TestPoints[preIdx];
                if (call.Rsrp == null)
                {
                    float? rsrp = (float?)prePoint["lte_fdd_RSRP"];
                    if (rsrp != null && rsrp >= -141 && rsrp <= 25)
                    {
                        call.Rsrp = rsrp;
                    }
                }
                int? tac = (int?)(ushort?)prePoint["lte_fdd_TAC"];
                int? eci = (int?)prePoint["lte_fdd_ECI"];
                if (tac != null || eci != null)
                {
                    call.Tac = tac;
                    call.Eci = eci;
                    break;
                }
            }
        }

        private bool searchCsfbAttemptInfo(CallInfo call, ref int lastTpIdx, int i, TestPoint tp)
        {
            if (call.EvtCallAttempt != null
                && (call.RxLev_Rscp == null || call.Lac == null || call.Ci == null)
                && tp.SN > call.EvtCallAttempt.SN)
            {
                return setCsfbAttemptInfo(call, ref lastTpIdx, i, tp);
            }

            return false;
        }

        private bool setCsfbAttemptInfo(CallInfo call, ref int lastTpIdx, int i, TestPoint tp)
        {
            float? lev = (float?)tp["lte_fdd_wcdma_TotalRSCP"];
            int? lac = (int?)tp["lte_fdd_wcdma_SysLAI"];
            int? ci = (int?)tp["lte_fdd_wcdma_SysCellID"];
            if (lev != null && lev >= -140 && lev <= -10)
            {
                call.RxLev_Rscp = lev;
                call.CallNetType = "WCDMA";
                call.Lac = lac;
                call.Ci = ci;
                if (call.Lac != null || call.Ci != null)
                {
                    lastTpIdx = i;
                    return true;
                }
            }
            else
            {
                lev = (short?)tp["lte_fdd_gsm_DM_RxLevSub"];
                lac = (int?)tp["lte_fdd_gsm_SC_LAC"];
                ci = (int?)tp["lte_fdd_gsm_SC_CI"];
                if (lev != null && lev >= -120 && lev <= -10)
                {
                    call.RxLev_Rscp = lev;
                    call.CallNetType = "GSM";
                    call.Lac = lac;
                    call.Ci = ci;
                }
                if (call.Lac != null || call.Ci != null)
                {
                    lastTpIdx = i;
                    return true;
                }
            }

            return false;
        }
    }
}
