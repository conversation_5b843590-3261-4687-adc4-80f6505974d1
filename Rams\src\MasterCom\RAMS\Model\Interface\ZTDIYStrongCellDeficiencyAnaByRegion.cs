﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.Model.Interface
{
    /// <summary>
    /// Written by Wu<PERSON>un<PERSON>ong 2012.8.16
    /// </summary>
    public class ZTDIYStrongCellDeficiencyAnaByRegion : QueryBase
    {
        public ZTDIYStrongCellDeficiencyAnaByRegion(MainModel mainModel)
            :base(mainModel)
        {
        }

        public override string Name
        {
            get { return "强信号小区缺失度分析(按区域)"; }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12070, this.Name);
        }

        protected MapFormItemSelection ItemSelection;
        protected override void query()
        {
            SetQueryFormStrongCellDeficiency setQueryFormStrongCellDeficiency = new SetQueryFormStrongCellDeficiency(MainModel, ItemSelection, Condition);
            setQueryFormStrongCellDeficiency.Show(MainModel.MainForm);
        }
    }
}
