﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class WeakSinrSampleRoadQuery_LteFdd : ZTWeakSINRRoadQuery_LteFdd
    {
        public static WeakSinrSampleRoadQuery_LteFdd Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new WeakSinrSampleRoadQuery_LteFdd();
                }
                return instance;
            }
        }

        protected WeakSinrSampleRoadQuery_LteFdd()
            : base()
        {
            Columns = new List<string>();
            Columns.Add("");
            Columns.Add("lte_fdd_EARFCN");
            Columns.Add("lte_fdd_PCI");
            Columns.Add("lte_fdd_TAC");
            Columns.Add("lte_fdd_ECI");
            Columns.Add("lte_fdd_RSRQ");
            Columns.Add("lte_fdd_RSRP");
            Columns.Add("lte_fdd_SINR");
            Columns.Add("lte_fdd_Pathloss");
            Columns.Add("lte_fdd_NCell_RSRP");
            Columns.Add("lte_fdd_NCell_EARFCN");
            Columns.Add("lte_fdd_NCell_PCI");
            Columns.Add("lte_fdd_Times_QPSK_DLCode0");
            Columns.Add("lte_fdd_Times_QAM16_DLCode0");
            Columns.Add("lte_fdd_Times_QAM64_DLCode0");
            Columns.Add("lte_fdd_Times_QPSK_DLCode1");
            Columns.Add("lte_fdd_Times_QAM16_DLCode1");
            Columns.Add("lte_fdd_Times_QAM64_DLCode1");
            Columns.Add("lte_fdd_PHY_DL");
            Columns.Add("lte_fdd_MAC_DL");
            Columns.Add("lte_fdd_PDCP_DL");
            Columns.Add("lte_fdd_FTP_Download_Rate");
            Columns.Add("lte_fdd_Wideband_CQI");
            Columns.Add("lte_fdd_Transmission_Mode");
            Columns.Add("lte_fdd_PDCCH_DL_Grant_Count");
            Columns.Add("lte_fdd_PDSCH_PRb_Num_slot");
        }

        public override string Name
        {
            get { return "LTEFDD质差采样点统计"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26011, this.Name);//////
        }

        protected override void fireShowForm()
        {
            List<WeakSinrSampleRoad_LteFdd> roadList = new List<WeakSinrSampleRoad_LteFdd>();
            foreach (WeakSINRRoad_LteFdd road in weakCoverList)
            {
                WeakSinrSampleRoad_LteFdd sampleRoad = new WeakSinrSampleRoad_LteFdd(road);
                sampleRoad.GetResult();
                roadList.Add(sampleRoad);
            }

            WeakSinrSampleRoadForm frm = MainModel.CreateResultForm(typeof(WeakSinrSampleRoadForm)) as WeakSinrSampleRoadForm;
            frm.FillData(roadList);
            frm.Visible = true;
            frm.BringToFront();
            weakCoverList = null;
        }

        private static WeakSinrSampleRoadQuery_LteFdd instance;
    }

    public class WeakSinrSampleRoad_LteFdd : WeakSINRRoad_LteFdd
    {
        public WeakSinrSampleRoad_LteFdd(WeakSINRRoad_LteFdd baseRoad)
            : base(baseRoad)
        {
        }

        public List<WeakSinrSampleInfo_LteFdd> SampleInfos
        {
            get;
            private set;
        }

        public void GetResult()
        {
            SampleInfos = new List<WeakSinrSampleInfo_LteFdd>();
            int sn = 0;
            foreach (TestPoint tp in TestPoints)
            {
                WeakSinrSampleInfo_LteFdd sample = new WeakSinrSampleInfo_LteFdd(tp);
                sample.SN = ++sn;
                SampleInfos.Add(sample);
            }
        }

    }

    public class WeakSinrSampleRoadQuery_LteFdd_VOLTE : WeakSinrSampleRoadQuery_LteFdd
    {
        private static WeakSinrSampleRoadQuery_LteFdd_VOLTE instance = null;
        public static new WeakSinrSampleRoadQuery_LteFdd_VOLTE Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new WeakSinrSampleRoadQuery_LteFdd_VOLTE();
                }
                return instance;
            }
        }
        protected WeakSinrSampleRoadQuery_LteFdd_VOLTE()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }
        public override string Name
        {
            get { return "VOLTE_FDD质差采样点统计"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30016, this.Name);//////
        }
    }

    public class WeakSinrSampleInfo_LteFdd
    {
        public TestPoint TestPoint
        {
            get;
            protected set;
        }

        public int SN
        {
            get;
            set;
        }

        public string TimeString
        {
            get;
            protected set;
        }

        public string CellID
        {
            get;
            protected set;
        }

        public string CellName
        {
            get;
            protected set;
        }

        public int? Tac
        {
            get;
            protected set;
        }

        public int? Eci
        {
            get;
            protected set;
        }

        public int? Earfcn
        {
            get;
            protected set;
        }

        public int? Pci
        {
            get;
            protected set;
        }

        public double Longitude
        {
            get;
            protected set;
        }

        public double Latitude
        {
            get;
            protected set;
        }

        public float? Rsrp
        {
            get;
            protected set;
        }

        public float? Sinr
        {
            get;
            protected set;
        }

        public float? Rsrq
        {
            get;
            protected set;
        }

        public float? Pathloss
        {
            get;
            protected set;
        }

        public float? PhyThroughput
        {
            get;
            protected set;
        }

        public float? MacThroughput
        {
            get;
            protected set;
        }

        public float? PdcpThroughput
        {
            get;
            protected set;
        }

        public float? FtpDlRate
        {
            get;
            protected set;
        }

        public float? Cqi
        {
            get;
            protected set;
        }

        public string AntennaPattern
        {
            get;
            protected set;
        }

        public string CodingSchemeDesc
        {
            get;
            protected set;
        }

        public int? PrbPerFrame
        {
            get;
            protected set;
        }

        public int? FramePerSecond
        {
            get;
            protected set;
        }

        public string NEarfcnDesc
        {
            get;
            protected set;
        }

        public string NPciDesc
        {
            get;
            protected set;
        }

        public string NRsrpDesc
        {
            get;
            protected set;
        }

        public List<int?> NEarfcnList
        {
            get;
            protected set;
        }

        public List<int?> NPciList
        {
            get;
            protected set;
        }

        public List<float?> NRsrpList
        {
            get;
            protected set;
        }

        public WeakSinrSampleInfo_LteFdd(TestPoint tp)
        {
            TestPoint = tp;

            TimeString = tp.DateTimeStringWithMillisecond;
            Longitude = tp.Longitude;
            Latitude = tp.Latitude;

            Tac = (int?)(ushort?)tp["lte_fdd_TAC"];
            Eci = (int?)tp["lte_fdd_ECI"];
            Earfcn = (int?)tp["lte_fdd_EARFCN"];
            Pci = (int?)(short?)tp["lte_fdd_PCI"];

            LTECell lteCell = CellManager.GetInstance().GetNearestLTECell(tp.DateTime, Tac, Eci, Earfcn, Pci, Longitude, Latitude);
            if (lteCell == null)
            {
                CellID = CellName = "";
            }
            else
            {
                CellID = lteCell.ID.ToString();
                CellName = lteCell.Name;
            }

            Rsrp = (float?)tp["lte_fdd_RSRP"];
            Sinr = (float?)tp["lte_fdd_SINR"];
            Rsrq = (float?)tp["lte_fdd_RSRQ"];
            Pathloss = (float?)(short?)tp["lte_fdd_Pathloss"];
            PhyThroughput = (float?)((int?)tp["lte_fdd_PHY_DL"] / 1000d / 1000);
            MacThroughput = (float?)((int?)tp["lte_fdd_MAC_DL"] / 1000d / 1000);
            PdcpThroughput = (float?)((int?)tp["lte_fdd_PDCP_DL"] / 1000d / 1000);
            FtpDlRate = (float?)((int?)tp["lte_fdd_FTP_Download_Rate"] / 1000d / 1000);

            Cqi = (float?)(short?)tp["lte_fdd_Wideband_CQI"];
            short? antennaPat = (short?)tp["lte_fdd_Transmission_Mode"];
            if (antennaPat == null)
            {
                AntennaPattern = "";
            }
            else
            {
                AntennaPattern = "TM" + antennaPat.ToString();
            }

            int? qpsk = (int?)tp["lte_fdd_Times_QPSK_DLCode0"] + (int?)tp["lte_fdd_Times_QPSK_DLCode1"];
            int? qam16 = (int?)tp["lte_fdd_Times_QAM16_DLCode0"] + (int?)tp["lte_fdd_Times_QAM16_DLCode1"];
            int? qam64 = (int?)tp["lte_fdd_Times_QAM64_DLCode0"] + (int?)tp["lte_fdd_Times_QAM64_DLCode1"];
            List<CodingScheme> lst = new List<CodingScheme>();
            lst.Add(new CodingScheme(qpsk, "QPSK"));
            lst.Add(new CodingScheme(qam16, "QAM16"));
            lst.Add(new CodingScheme(qam64, "QAM64"));
            lst.Sort();
            CodingSchemeDesc = lst[2].Value == null ? "" : lst[2].Desc;

            PrbPerFrame = (int?)tp["lte_fdd_PDSCH_PRb_Num_slot"];
            FramePerSecond = (int?)(short?)tp["lte_fdd_PDCCH_DL_Grant_Count"];

            NEarfcnList = new List<int?>();
            NPciList = new List<int?>();
            NRsrpList = new List<float?>();
            StringBuilder sbEarfcn = new StringBuilder();
            StringBuilder sbPci = new StringBuilder();
            StringBuilder sbRsrp = new StringBuilder();
            for (int i = 0; i < 50; ++i)
            {
                int? nEarfcn = (int?)tp["lte_fdd_NCell_EARFCN", i];
                int? nPci = (int?)(short?)tp["lte_fdd_NCell_PCI", i];
                float? nRsrp = (float?)tp["lte_fdd_NCell_RSRP", i];
                if (nEarfcn == null || nPci == null || nRsrp == null)
                {
                    break;
                }
                NEarfcnList.Add(nEarfcn);
                NPciList.Add(nPci);
                NRsrpList.Add(nRsrp);
                sbEarfcn.Append(nEarfcn.ToString() + ",");
                sbPci.Append(nPci.ToString() + ",");
                sbRsrp.Append(nRsrp.ToString() + ",");
            }
            NEarfcnDesc = sbEarfcn.Length == 0 ? "" : sbEarfcn.Remove(sbEarfcn.Length - 1, 1).ToString();
            NPciDesc = sbPci.Length == 0 ? "" : sbPci.Remove(sbPci.Length - 1, 1).ToString();
            NRsrpDesc = sbRsrp.Length == 0 ? "" : sbRsrp.Remove(sbRsrp.Length - 1, 1).ToString();
        }

        protected class CodingScheme : IComparable<CodingScheme>
        {
            public int? Value { get; set; }

            public string Desc { get; set; }

            public CodingScheme(int? value, string desc)
            {
                Value = value;
                Desc = desc;
            }

            public int CompareTo(CodingScheme other)
            {
                if (this == other) return 0;
                if (Value == null) return -1;
                if (other.Value == null) return 1;
                return Value > other.Value ? 1 : -1;
            }
        }
    }
}
