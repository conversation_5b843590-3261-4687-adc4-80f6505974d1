﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class DLSpeedPitCond
    {
        public double DPitPreLastTime { get; set; } = 10;
        public double DPitPreSpeedAvg { get; set; } = 30;
        public double DPitDropSpeed { get; set; } = 20;
        public double DPitLastTime { get; set; } = 6;
        public double DPitLastSpeedAvg { get; set; } = 10;
        public double DPitAfterLastTime { get; set; } = 5;
    }

    public class DLSpeedPitInfo
    {
        public int ISN { get; set; }
        public FileInfo FileInfoMsg { get; set; }
        public string StrFileName
        {
            get { return TpPitStart.FileName; }
        }
        public bool IsFindStartPoint { get; set; } = false;
        public TestPoint TpPitStart { get; set; }
        public string StrTpPitStartTime
        {
            get
            {
                return "20" + TpPitStart.DateTimeStringWithMillisecond;
            }
        }
        public double DPreSpeedAvg { get; set; }
        public double DPitStartSpeed { get; set; }
        public List<int> LPreTimeList { get; set; } = new List<int>();
        public List<double> DPreSpeedList { get; set; } = new List<double>();
        public double DCurSpeedAvg { get; set; }
        public List<int> LCurTimeList { get; set; } = new List<int>();
        public List<double> DCurSpeedList { get; set; } = new List<double>();
        public bool IsFindEndPoint { get; set; } = false;
        public TestPoint TpPitEnd { get; set; }
        public TestPoint TpOutPitEnd { get; set; }
        public double DOutPitSpeed { get; set; }
        public string StrOutPitTime
        {
            get
            {
                return "20" + TpPitStart.DateTimeStringWithMillisecond;
            }
        }
        public List<int> LAftTimeList { get; set; } = new List<int>();
        public List<double> DAftSpeedList { get; set; } = new List<double>();
        public double DOutPitSpeedAvg { get; set; }
        public bool IsOutPitLast { get; set; } = false;
        public bool IsVailInfo { get; set; }

        public void AddPoint(TestPoint tp, double dSpeed, DLSpeedPitCond pitCond)
        {
            if (!IsFindStartPoint)
            {
                setIsFindStartPointTP(tp, dSpeed, pitCond);
            }
            else if (!IsFindEndPoint)
            {
                setIsFindEndPointTP(tp, dSpeed, pitCond);
            }
            else if (!IsOutPitLast)
            {
                setIsOutPitLastTP(tp, dSpeed, pitCond);
            }
        }

        private void setIsFindStartPointTP(TestPoint tp, double dSpeed, DLSpeedPitCond pitCond)
        {
            if (LPreTimeList.Count == 0 || (tp.Time - LPreTimeList[0] < pitCond.DPitPreLastTime))
            {
                LPreTimeList.Add(tp.Time);
                DPreSpeedList.Add(dSpeed);
                return;
            }
            double dSpeedAvg = calSpeedAvg(DPreSpeedList);
            if (dSpeedAvg > pitCond.DPitPreSpeedAvg && (DPreSpeedList[DPreSpeedList.Count - 1] - dSpeed) > pitCond.DPitDropSpeed)
            {
                IsFindStartPoint = true;
                TpPitStart = tp;
                DPreSpeedAvg = Math.Round(dSpeedAvg, 3);
                DPitStartSpeed = Math.Round(dSpeed, 3);
                LCurTimeList.Add(tp.Time);
                DCurSpeedList.Add(dSpeed);
                return;
            }
            int iRemoveIndex = -1;
            for (int i = 0; i < LPreTimeList.Count; i++)
            {
                if (tp.Time - LPreTimeList[i] >= pitCond.DPitPreLastTime)
                    iRemoveIndex = i;
                else
                    break;
            }
            if (iRemoveIndex > -1)
            {
                LPreTimeList.RemoveRange(0, iRemoveIndex + 1);
                DPreSpeedList.RemoveRange(0, iRemoveIndex + 1);
            }
            LPreTimeList.Add(tp.Time);
            DPreSpeedList.Add(dSpeed);
        }

        private void setIsFindEndPointTP(TestPoint tp, double dSpeed, DLSpeedPitCond pitCond)
        {
            if (tp.Time - LCurTimeList[0] < pitCond.DPitLastTime)
            {
                LCurTimeList.Add(tp.Time);
                DCurSpeedList.Add(dSpeed);
                TpPitEnd = tp;
                return;
            }
            double dSpeedAvg = calSpeedAvg(DCurSpeedList);
            if (dSpeedAvg < pitCond.DPitLastSpeedAvg && dSpeed > pitCond.DPitPreSpeedAvg)
            {
                IsFindEndPoint = true;
                DCurSpeedAvg = Math.Round(dSpeedAvg, 3);
                DOutPitSpeed = Math.Round(dSpeed, 3);
                TpOutPitEnd = tp;
                LAftTimeList.Add(tp.Time);
                DAftSpeedList.Add(dSpeed);
            }
            else
            {
                IsVailInfo = false;
            }
        }

        private void setIsOutPitLastTP(TestPoint tp, double dSpeed, DLSpeedPitCond pitCond)
        {
            if (tp.Time - LAftTimeList[0] < pitCond.DPitAfterLastTime)
            {
                LAftTimeList.Add(tp.Time);
                DAftSpeedList.Add(dSpeed);
                return;
            }
            double dSpeedAvg = calSpeedAvg(DAftSpeedList);
            if (dSpeedAvg > pitCond.DPitPreSpeedAvg)
            {
                IsOutPitLast = true;
                DOutPitSpeedAvg = Math.Round(dSpeedAvg, 3);
            }
            else
            {
                IsVailInfo = false;
            }
        }

        private double calSpeedAvg(List<double> dSpeedList)
        {
            double dSpeedSum = 0;
            foreach (double speed in dSpeedList)
            {
                dSpeedSum += speed;
            }
            return dSpeedSum / dSpeedList.Count;
        }

        public void Clear()
        {
            LPreTimeList.Clear();
            LCurTimeList.Clear();
            LAftTimeList.Clear();
            DPreSpeedList.Clear();
            DCurSpeedList.Clear();
            DAftSpeedList.Clear();
        }
    }

    public class DLSeepLimitCond
    {
        public double DLastTime { get; set; } = 10;
        public double DSeepAvgMin { get; set; } = 36;
        public double DSeepAvgMax { get; set; } = 42;
        public double DVarianceMin { get; set; } = 0;
        public double DVarianceMax { get; set; } = 36;
        public bool IsTpRegion { get; set; } = false;
    }

    public class DLSpeedLimitInfo
    {
        public int ISN { get; set; }
        public FileInfo FileInfoMsg { get; set; }
        public string StrFileName
        {
            get { return FileInfoMsg.Name; }
        }
        public DateTime DateTime { get; set; }
        public TestPoint TpStart
        {
            get
            {
                return TpList.Count == 0 ? null : TpList[0];
            }
        }
        public string StrLimitStartTime { get; set; } = "";
        public TestPoint TpEnd
        {
            get
            {
                return TpList.Count == 0 ? null : TpList[TpList.Count - 1];
            }
        }
        public string StrLimitEndTime { get; set; } = "";
        public int ITimeSpan
        {
            get
            {
                return TpEnd.Time - TpStart.Time;
            }
        }
        public string StrTimeSpan { get; set; } = "";
        public double DSpeedAvg
        {
            get 
            {
                double seepAvgSum = 0;
                foreach (double seep in dSeepList)
                {
                    seepAvgSum += seep;
                }
                return dSeepList.Count == 0 ? 0 : seepAvgSum / dSeepList.Count;
            }
        }
        public string StrSpeedAvg { get; set; } = "";
        public double DVariance
        {
            get
            {
                double varianceSum = 0;
                foreach (double seep in dSeepList)
                {
                    varianceSum += Math.Pow(seep - DSpeedAvg, 2);
                }
                return dSeepList.Count == 0 ? 0 : varianceSum / dSeepList.Count;
            }
        }
        public string StrVariance { get; set; } = "";
        public List<TestPoint> TpList { get; set; } = new List<TestPoint>();

        private readonly List<double> dSeepList = new List<double>();

        public int TpCount
        {
            get
            {
                return dSeepList.Count;
            }
        }
        public double DVariancePre { get; set; }
        public double DVariancePer { get; set; }

        public void Add(TestPoint tp, double dSeep)
        {
            this.dSeepList.Add(dSeep);
            this.TpList.Add(tp);
        }

        public void ReMove()
        {
            this.dSeepList.RemoveAt(this.dSeepList.Count - 1);
            this.TpList.RemoveAt(this.TpList.Count - 1);
        }

        public void ConvertInfo(DLSeepLimitCond limitCond)
        {
            StrLimitStartTime = "20" + TpStart.DateTimeStringWithMillisecond;
            StrLimitEndTime = "20" + TpEnd.DateTimeStringWithMillisecond;
            StrSpeedAvg = Math.Round(DSpeedAvg, 2).ToString();
            StrVariance = Math.Round(DVariance, 2).ToString();
            StrTimeSpan = ITimeSpan.ToString();
            DateTime = TpStart.DateTime;
            int iVariance = 0;
            foreach (double dValue in dSeepList)
            {
                if (dValue >= limitCond.DSeepAvgMin && dValue <= limitCond.DSeepAvgMax)
                {
                    iVariance++;
                }
            }
            DVariancePer = Math.Round(100.0 * iVariance / dSeepList.Count, 2);

            dSeepList.Clear();
            TpList.Clear();
        }
    }

    public class WeakRoadCond
    {
        public bool IsWeakCover { get; set; }
        public int ISampleNum { get; set; } = 10;
        public double DWeakPercent { get; set; } = 70;
        public double DThreshold { get; set; } = -110;
    }

    public class WeakRoadInfo
    {
        public int ISN { get; set; }
        public FileInfo FileInfoMsg { get; set; }
        public string StrFileName
        {
            get { return FileInfoMsg.Name; }
        }
        public DateTime DStartTime { get; set; }
        public string StrStartTime { get; set; }
        public DateTime DEndeTime { get; set; }
        public string StrEndTime { get; set; }
        public int ISampleNum { get; set; }
        public int IWeakSampleNum { get; set; }
        public double DWeakPercent { get; set; }

        public string StrWeakPercent
        {
            get
            {
                return Math.Round(DWeakPercent, 2).ToString() + "%";
            }
        }
        
        public double DStartLng { get; set; }
        public double DStartLat { get; set; }
        public double DEndLng { get; set; }
        public double DEndLat { get; set; }
        public double DRsrpAvg { get; set; }
        public float FRsrpMax { get; set; }
        public float FRsrpMin { get; set; }
        public List<float> TpParamList { get; set; } = new List<float>();
        public List<TestPoint> TpList { get; set; } = new List<TestPoint>();

        public void AddPoint(TestPoint tp, float fParamValue)
        {
            TpParamList.Add(fParamValue);
            TpList.Add(tp);
        }

        public void CalWeakPercent(WeakRoadCond weakRoadCond)
        {
            float fRsrpSum = 0;
            foreach (float fValue in TpParamList)
            {
                fRsrpSum += fValue;
                if (fValue < weakRoadCond.DThreshold)
                    IWeakSampleNum++;
            }
            DWeakPercent = Math.Round(100.0 * IWeakSampleNum / TpParamList.Count, 2);
            FileInfoMsg = TpList[0].FileInfo;
            DStartTime = TpList[0].DateTime;
            DEndeTime = TpList[TpList.Count - 1].DateTime;
            StrStartTime = "20" + TpList[0].DateTimeStringWithMillisecond;
            DStartLat = TpList[0].Latitude;
            DStartLng = TpList[0].Longitude;
            StrEndTime = "20" + TpList[TpList.Count - 1].DateTimeStringWithMillisecond;
            DEndLat = TpList[TpList.Count - 1].Latitude;
            DEndLng = TpList[TpList.Count - 1].Longitude;
            TpParamList.Sort();
            DRsrpAvg = Math.Round(1.0 * fRsrpSum / TpList.Count, 3);
            FRsrpMax = TpParamList[TpParamList.Count - 1];
            FRsrpMin = TpParamList[0];
        }

        public void ClearList()
        {
            TpParamList.Clear();
            TpList.Clear();
        }
    }
}
