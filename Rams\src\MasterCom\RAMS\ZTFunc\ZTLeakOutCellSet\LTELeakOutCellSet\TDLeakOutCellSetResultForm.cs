﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using DevExpress.XtraGrid.Columns;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class TDLeakOutCellSetResultForm : MinCloseForm
    {
        public TDLeakOutCellSetResultForm()
            : base()
        {
            InitializeComponent();
            miExportSimpleExcel.Click += MiExportSimpleExcel_Click;
            gridView1.DoubleClick += GridView_DoubleClick;
            gridView2.DoubleClick += GridView_DoubleClick;

        }

        public void FillData(List<LeakOutCell> indoorCells)
        {
            gridControl1.DataSource = indoorCells;
            gridControl1.RefreshDataSource();
        }

        public void GridView_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            double Longitude = 0;
            double Latitude = 0;
            ICell icell = null;
            if (gv.GetRow(gv.GetSelectedRows()[0]) is LeakOutOutDoorCellInfo)
            {
                LeakOutOutDoorCellInfo leakOutCell = gv.GetRow(gv.GetSelectedRows()[0]) as LeakOutOutDoorCellInfo;

                MainModel.DTDataManager.Clear();
                //foreach (TestPoint tp in leakOutCell.TestPoints)
                //{
                //    MainModel.DTDataManager.Add(tp);
                //}
                icell = leakOutCell.Cell;
                Longitude = leakOutCell.Cell.Longitude;
                Latitude = leakOutCell.Cell.Latitude;
            }
            else if (gv.GetRow(gv.GetSelectedRows()[0]) is LeakOutCell)
            {
                LeakOutCell leakOutCell = gv.GetRow(gv.GetSelectedRows()[0]) as LeakOutCell;

                MainModel.DTDataManager.Clear();
                foreach (TestPoint tp in leakOutCell.testPointList)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                icell = leakOutCell.Cell;
                Longitude = leakOutCell.Cell.Longitude;
                Latitude = leakOutCell.Cell.Latitude;
            }
            if (icell == null)
            {
                return;
            }
            if (icell is Cell && (icell as Cell).Antennas != null)
            {
                MainModel.SelectedCell = icell as Cell;
            }
            else if (icell is TDCell && (icell as TDCell).Antenna != null)
            {
                MainModel.SelectedTDCell = icell as TDCell;
            }
            MainModel.FireSetDefaultMapSerialTheme("TD_PCCPCH_RSCP");
            MainModel.FireDTDataChanged(this);
            MainModel.FireSelectedCellChanged(this);
            MainModel.MainForm.GetMapForm().GoToView(Longitude, Latitude);
        }

        public void MiExportSimpleExcel_Click(object sender, EventArgs e)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            foreach (GridColumn col in this.gridView1.Columns)
            {
                row.AddCellValue(col.Caption);
            }
            foreach (GridColumn col in this.gridView2.Columns)
            {
                row.AddCellValue(col.Caption);
            }
            rows.Add(row);
            for (int i = 0; i < gridView1.RowCount; i++)
            {
                row = new NPOIRow();
                rows.Add(row);
                foreach (GridColumn col in gridView1.Columns)
                {
                    row.AddCellValue(gridView1.GetRowCellDisplayText(i, col));
                }
                gridView1.ExpandMasterRow(i);
                DevExpress.XtraGrid.Views.Grid.GridView view = gridView1.GetDetailView(i, 0) as DevExpress.XtraGrid.Views.Grid.GridView;
                if (view!=null)
                {
                    for (int j = 0; j < view.RowCount; j++)
                    {
                        NPOIRow subRow=new NPOIRow();
                        row.AddSubRow(subRow);
                        foreach (GridColumn subCol in view.Columns)
                        {
                            subRow.AddCellValue(view.GetRowCellDisplayText(j, subCol));
                        }
                    }
                }
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }

    }
}
