﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.MTGis
{
    public static class RegionMop
    {
        public static Dictionary<string, MapOperation2> GetResvRegionMop()
        {
            Dictionary<string, MapOperation2> regionMopDic = new Dictionary<string, MapOperation2>();
            List<ResvRegion> resvRegions = MainModel.GetInstance().SearchGeometrys.SelectedResvRegions;
            MapWinGIS.Shape gmt = MainModel.GetInstance().SearchGeometrys.Region;

            if (resvRegions != null && resvRegions.Count > 0)
            {
                foreach (ResvRegion region in resvRegions)
                {
                    if (!regionMopDic.ContainsKey(region.RegionName))
                    {
                        MapOperation2 mapOp2 = new MapOperation2();
                        mapOp2.FillPolygon(region.Shape);
                        regionMopDic.Add(region.RegionName, mapOp2);
                    }
                }
            }
            else if (gmt != null)
            {
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(gmt);
                regionMopDic.Add("当前区域", mapOp2);
            }
            return regionMopDic;
        }

        public static string GetResvRegionName(Dictionary<string, MapOperation2> regionMopDic, DbPoint dPoint)
        {
            foreach (string strKey in regionMopDic.Keys)
            {
                if (regionMopDic[strKey].CheckPointInRegion(dPoint.x, dPoint.y))
                {
                    return strKey;
                }
            }
            return null;
        }
    }
}
