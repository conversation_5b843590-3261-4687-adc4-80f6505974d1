﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class TDScanHighCoverageRoadInfo
    {
        public int SN { get; set; }
        public int FileID { get; set; }
        public string FileName { get; set; } = "";
        public string RoadName { get; set; } = "";
        public float PccpchRscpMax { get; set; } = 255;
        public float PccpchRscpMin { get; set; } = 255;
        public float PccpchRscpAvg { get; set; } = 255;
        public float PccpchRscpSample { get; set; }
        public List<TDScanHighCoveragePointInfo> SampleLst { get; set; } = new List<TDScanHighCoveragePointInfo>();

        private double distance = 0;
        public double Distance
        {
            get { return Math.Round(distance, 2); }
            set { distance = value; }
        }

        public double LongitudeMid
        {
            get
            {
                int index = SampleLst.Count / 2;
                return SampleLst[index].Tp.Longitude;
            }
        }
        public double LatitudeMid
        {
            get
            {
                int index = SampleLst.Count / 2;
                return SampleLst[index].Tp.Latitude;
            }
        }

        public void SetPccpchRscp(float rscp)
        {
            PccpchRscpSample++;

            if (PccpchRscpSample == 1)     //first
            {
                PccpchRscpMax = rscp;
                PccpchRscpMin = rscp;
                PccpchRscpAvg = rscp;
            }
            else
            {
                if (PccpchRscpMax < rscp)
                {
                    PccpchRscpMax = rscp;
                }
                if (PccpchRscpMin > rscp)
                {
                    PccpchRscpMin = rscp;
                }

                PccpchRscpAvg += rscp;
            }
        }

        public void GetResult()
        {
            FileID = SampleLst[0].Tp.FileID;
            FileName = SampleLst[0].Tp.FileName;
            if (PccpchRscpSample > 0)
            {
                PccpchRscpAvg = (float)Math.Round((double)PccpchRscpAvg / (double)PccpchRscpSample, 2);
            }
            RoadName = GISManager.GetInstance().GetRoadPlaceDesc(LongitudeMid, LatitudeMid);
        }

        public string GetFirstTime()
        {
            if (SampleLst.Count > 0)
            {
                return SampleLst[0].Tp.TimeStringWithMillisecond;
            }
            return "";
        }
        public string GetLasttime()
        {
            if (SampleLst.Count > 0)
            {
                return SampleLst[SampleLst.Count - 1].Tp.TimeStringWithMillisecond;
            }
            return "";
        }

        public BackgroundResult ConvertToBackgroundResult()
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.FileID = FileID;
            bgResult.FileName = FileName;
            bgResult.ISTime = SampleLst[0].Tp.Time;
            bgResult.IETime = SampleLst[0].Tp.Time;
            bgResult.LongitudeStart = SampleLst[0].Tp.Longitude;
            bgResult.LatitudeStart = SampleLst[0].Tp.Latitude;
            bgResult.LongitudeMid = LongitudeMid;
            bgResult.LatitudeMid = LatitudeMid;
            bgResult.LongitudeEnd = SampleLst[SampleLst.Count - 1].Tp.Longitude;
            bgResult.LatitudeEnd = SampleLst[SampleLst.Count - 1].Tp.Latitude;
            bgResult.SampleCount = SampleLst.Count;
            bgResult.DistanceLast = distance;
            bgResult.RxLevMean = PccpchRscpAvg;
            bgResult.RxLevMin = PccpchRscpMin;
            bgResult.RxLevMax = PccpchRscpMax;
            return bgResult;
        }
    }

    public class TDScanHighCoveragePointInfo
    {
        public int SN { get; set; }
        public TestPoint Tp { get; set; }
        public List<TDScanHighCoverageCellInfo> CellList { get; set; }

        public TDScanHighCoveragePointInfo(int SN, TestPoint tp, List<TDScanHighCoverageCellInfo> cellList)
        {
            this.SN = SN;
            this.Tp = tp;
            this.CellList = cellList;
        }
    }

    public class TDScanHighCoverageCellInfo
    {
        public int SN { get; set; }
        public TDCell TdCell { get; set; }
        public string CellName { get; set; } = "";
        public string LAC { get; set; }
        public string CI { get; set; }
        public int ARFCN { get; set; }
        public int CPI { get; set; }
        public string Longitude { get; set; }
        public string Latitude { get; set; }
        public float PccpchRscp { get; set; }
        public string Distance { get; set; }

        public TDScanHighCoverageCellInfo(TestPoint tp, int index)
        {
            this.SN = index + 1;

            float? rscp = (float?)tp["TDS_PCCPCH_RSCP", index];
            short? arfcn = (short?)(int?)tp["TDS_PCCPCH_Channel", index];
            short? cpi = (short?)(int?)tp["TDS_PCCPCH_CPI", index];

            if (rscp != null && arfcn != null && cpi != null)
            {
                TDCell curCell = CellManager.GetInstance().GetNearestTDCell(tp.DateTime, (short)arfcn, (short)cpi, tp.Longitude, tp.Latitude);

                if (curCell != null)
                {
                    this.TdCell = curCell;
                    this.CellName = curCell.Name;
                    this.LAC = curCell.LAC.ToString();
                    this.CI = curCell.CI.ToString();
                    this.Longitude = curCell.Longitude.ToString();
                    this.Latitude = curCell.Latitude.ToString();
                    this.Distance = Math.Round(curCell.GetDistance(tp.Longitude, tp.Latitude), 2).ToString();
                }
                else
                {
                    this.TdCell = null;
                    this.CellName = arfcn.ToString() + "_" + cpi.ToString();
                    this.LAC = "-";
                    this.CI = "-";
                    this.Longitude = "-";
                    this.Latitude = "-";
                    this.Distance = "-";
                }
                this.ARFCN = (int)arfcn;
                this.CPI = (int)cpi;
                this.PccpchRscp = (float)rscp;
            }
        }
    }
}
