﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTPointQuerySettingDlg : BaseFormStyle
    {
        public CQTPointQuerySettingDlg()
        {
            InitializeComponent();
        }
        //private const string pointID = "pointID"
        //private const string pointName = "pointName"
        //private const string pointAddr = "pointAddr"
        //private const string pointDesc = "pointDesc"
        //private const string tlLongitude = "tlLongitude"
        //private const string tlLatitude = "tlLatitude"
        //private const string brLongitude = "brLongitude"
        //private const string brLatitude = "brLatitude"
        //private const string altitude = "altitude"
        private const string pointType = "pointType";
        private const string densityType = "densityType";
        //private const string aliasName = "aliasName"
        private const string spaceType = "spaceType";
        private const string coverType = "coverType";
        private const string networkType = "networkType";
        //private const string belongArea = "belongArea"
        //private const string belongArea2 = "belongArea2"
        private Dictionary<GroupControl, List<CheckEdit>> gcCheckDic = new Dictionary<GroupControl, List<CheckEdit>>();
        public void InitFilters(CQTCfgManager cfgMng)
        {
            gcCheckDic.Clear();
            List<CQTCfgTypeBase> cfgList = new List<CQTCfgTypeBase>();
            foreach (CQTCfgTypeBase baseCfg in cfgMng.CoverTypeList)
            {
                cfgList.Add(baseCfg);
            }
            fillGroupControl(gcCvrType, cfgList);

            cfgList = new List<CQTCfgTypeBase>();
            foreach (CQTCfgTypeBase baseCfg in cfgMng.PointTypeList)
            {
                cfgList.Add(baseCfg);
            }
            fillGroupControl(gcPntType, cfgList);

            cfgList = new List<CQTCfgTypeBase>();
            foreach (CQTCfgTypeBase baseCfg in cfgMng.NetworkypeList)
            {
                cfgList.Add(baseCfg);
            }
            fillGroupControl(gcNetType, cfgList);

            cfgList = new List<CQTCfgTypeBase>();
            foreach (CQTCfgTypeBase baseCfg in cfgMng.DensityTypeList)
            {
                cfgList.Add(baseCfg);
            }
            fillGroupControl(gcDensityType, cfgList);


            cfgList = new List<CQTCfgTypeBase>();
            foreach (CQTCfgTypeBase baseCfg in cfgMng.SpaceTypeList)
            {
                cfgList.Add(baseCfg);
            }
            fillGroupControl(gcSpaceType, cfgList);

        }

        private void fillGroupControl(GroupControl gc, List<CQTCfgTypeBase> cfgList)
        {
            Control checkAllCtrl = null;
            foreach (Control ctrl in gc.Controls)
            {
                if (ctrl.Location.Y == 0)
                {
                    checkAllCtrl = ctrl;
                    break;
                }
            }
            List<CheckEdit> list = new List<CheckEdit>();
            gc.Controls.Clear();
            gc.Controls.Add(checkAllCtrl);
            int xGap = 5;
            int yGap = 3;
            int rowHeight = 25;
            int containerWidth = gc.DisplayRectangle.Width;
            int lastXPos = gc.DisplayRectangle.X + 20;
            int lastYPos = gc.DisplayRectangle.Y + yGap;
            foreach (CQTCfgTypeBase item in cfgList)
            {
                CheckEdit ce = new CheckEdit();
                ce.CheckedChanged += new EventHandler(ce_CheckedChanged);
                ce.Checked = true;
                ce.Parent = gc;
                ce.Properties.AutoWidth = true;
                ce.Text = item.Name;
                ce.Tag = item;
                int xPos = lastXPos + xGap;
                int xEndPos = xPos + ce.Width;
                if (xEndPos > containerWidth)//需换行
                {
                    ce.Left = gc.DisplayRectangle.Left + xGap;
                    ce.Top = lastYPos + rowHeight;
                }
                else
                {
                    ce.Left = xPos;
                    ce.Top = lastYPos;
                }
                lastXPos = ce.Left + ce.Width;
                lastYPos = ce.Top;
                list.Add(ce);
            }
            gc.Height = gc.DisplayRectangle.Y + lastYPos + 3 * yGap;
            gcCheckDic.Add(gc, list);
        }

        void ce_CheckedChanged(object sender, EventArgs e)
        {
            CheckEdit ce = sender as CheckEdit;
            if (ce == null || ce.Checked)//只对不勾选进行判断
            {
                return;
            }
            bool otherSel = false;
            foreach (CheckEdit ck in gcCheckDic[(GroupControl)(ce.Parent)])
            {
                if (ck != ce && ck.Checked)
                {
                    otherSel = true;
                    break;
                }
            }
            if (!otherSel)
            {
                XtraMessageBox.Show("请至少勾选一个类型，否则无法进行筛选！");
                ce.Checked = true;
            }
        }

        private void checkAll_CheckedChanged(object sender, EventArgs e)
        {
            CheckEdit ce = sender as CheckEdit;
            GroupControl gc = ce.Parent as GroupControl;
            foreach (Control ctrl in gc.Controls)
            {
                if (ctrl!=ce)
                {
                    ctrl.Enabled = !ce.Checked;
                }
            }
        }

        public string GetFilterSqlTxt()
        {
            StringBuilder sb = new StringBuilder();
            if (!cePointAll.Checked)
            {
                sb.Append(pointType + " in (" + getCheckedTypeIDStr(gcCheckDic[gcPntType]) + ")");
            }
            if (!ceDensityAll.Checked)
            {
                if (sb.Length > 0)
                {
                    sb.Append(" and ");
                }
                sb.Append(densityType + " in (" + getCheckedTypeIDStr(gcCheckDic[gcDensityType]) + ")");
            }
            if (!ceCvrAll.Checked)
            {
                if (sb.Length > 0)
                {
                    sb.Append(" and ");
                }
                sb.Append(coverType + " in (" + getCheckedTypeIDStr(gcCheckDic[gcCvrType]) + ")");
            }
            if (!ceSpaceAll.Checked)
            {
                if (sb.Length > 0)
                {
                    sb.Append(" and ");
                }
                sb.Append(spaceType + " in (" + getCheckedTypeIDStr(gcCheckDic[gcSpaceType]) + ")");
            }
            if (!ceNetAll.Checked)
            {
                if (sb.Length > 0)
                {
                    sb.Append(" and ");
                }
                sb.Append(networkType + " like '%[" + getCheckedTypeIDStr(gcCheckDic[gcNetType]) + "]%'");
            }
            return sb.ToString();
        }

        /// <summary>
        /// 获取选择的typeID,逗号分隔
        /// </summary>
        /// <param name="list">Items</param>
        /// <returns>字符串形式为 1,2,3,4</returns>
        private string getCheckedTypeIDStr(List<CheckEdit> items)
        {
            string str = "";
            StringBuilder sb = new StringBuilder();
            foreach (CheckEdit item in items)
            {
                if (item.Checked)
                {
                    CQTCfgTypeBase cfg = item.Tag as CQTCfgTypeBase;
                    if (cfg != null)
                    {
                        sb.Append(cfg.ID.ToString() + ",");
                    }
                }
            }
            str = sb.ToString();
            if (str.Length > 0)
            {//删掉最后一个逗号
                str = str.Remove(str.Length - 1, 1);
            }
            return str;
        }

    }
}
