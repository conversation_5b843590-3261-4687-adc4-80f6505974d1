﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class EleShowForScoreQueryControl : DevExpress.XtraEditors.XtraUserControl
    {
        private DataTable saveTable;
        private int luX;
        private int luY;
        private int xDistance;
        private int yDistance;
        public EleShowForScoreQueryControl()
        {
            InitializeComponent();
            IniSaveTable();
        }
        private void IniSaveTable()
        {
            saveTable = new DataTable();
            saveTable.Columns.Add("指标项");
            saveTable.Columns.Add("KPI");
            saveTable.Columns.Add("达标门限");
            saveTable.Columns.Add("挑战门限");
            saveTable.Columns.Add("得分");
            saveTable.Columns.Add("满分得分");
        }
        /// <summary>
        /// 利用指标项初始化控件显示
        /// </summary>
        /// <param name="indicatorOfShow"></param>
        public void IniData(List<Indicator> indicatorOfShow)
        {
            saveTable.Rows.Clear();
            panel1.Controls.Clear();
            //panel1.SuspendLayout();
            for (int i = 0; i < indicatorOfShow.Count; i++)
            {
                List<object> rowInf;

                //位置变化，从15, 10开始，横每次加100，纵每次加15
                LabelControl labelName = new LabelControl();
                LabelControl labelKPI = new LabelControl();
                LabelControl labelStandardThreshold = new LabelControl();
                LabelControl labelChallengeThreshold = new LabelControl();
                LabelControl labelScore = new LabelControl();
                LabelControl labelFullScore = new LabelControl();
                labelName.Text = indicatorOfShow[i].Name;
                labelFullScore.Text = indicatorOfShow[i].TotalScore.ToString();
                if (indicatorOfShow[i].KPI < -9999)
                {
                    labelKPI.Text = "未知";
                    if (!noPencentageEleNames.Contains(indicatorOfShow[i].Name))
                    {
                        labelStandardThreshold.Text = (indicatorOfShow[i].StandardThreshold*100).ToString("0.00")+"%";
                        labelChallengeThreshold.Text = (indicatorOfShow[i].ChallengeThreshold*100).ToString("0.00")+"%";
                    }
                    else
                    {
                        labelStandardThreshold.Text = indicatorOfShow[i].StandardThreshold.ToString("0.00");
                        labelChallengeThreshold.Text = indicatorOfShow[i].ChallengeThreshold.ToString("0.00");
                    }
                    labelScore.Text = "未知";
                }
                else
                {
                    if (!noPencentageEleNames.Contains(indicatorOfShow[i].Name))
                    {
                        labelKPI.Text = (indicatorOfShow[i].KPI*100).ToString("0.00")+"%";
                        labelStandardThreshold.Text = (indicatorOfShow[i].StandardThreshold * 100).ToString("0.00") + "%";
                        labelChallengeThreshold.Text = (indicatorOfShow[i].ChallengeThreshold * 100).ToString("0.00") + "%";
                        labelScore.Text = indicatorOfShow[i].Score.ToString("0.00");
                    }
                    else
                    {
                        labelKPI.Text = indicatorOfShow[i].KPI.ToString("0.00");
                        labelStandardThreshold.Text = (indicatorOfShow[i].StandardThreshold).ToString("0.00");
                        labelChallengeThreshold.Text = (indicatorOfShow[i].ChallengeThreshold).ToString("0.00");
                        labelScore.Text = indicatorOfShow[i].Score.ToString("0.00");
                    }
                }
                rowInf = new List<object>();
                rowInf.Add(labelName.Text);
                rowInf.Add(labelKPI.Text);
                rowInf.Add(labelStandardThreshold.Text);
                rowInf.Add(labelChallengeThreshold.Text);
                rowInf.Add(labelScore.Text);
                rowInf.Add(labelFullScore.Text);
                saveTable.Rows.Add(rowInf.ToArray());

                labelName.Location = new Point(luX - (int)(xDistance / 2.0)+15, (int)(luY / 2.0) + i * yDistance);
                labelKPI.Location = new Point(luX + xDistance+(int)(xDistance/4.0), (int)(luY / 2.0) + i * yDistance);
                labelStandardThreshold.Location = new Point(luX + xDistance * 2 + (int)(xDistance / 4.0), (int)(luY / 2.0) + i * yDistance);
                labelChallengeThreshold.Location = new Point(luX + xDistance * 3 + (int)(xDistance / 4.0), (int)(luY / 2.0) + i * yDistance);
                labelScore.Location = new Point(luX + xDistance * 4 + (int)(xDistance / 4.0), (int)(luY / 2.0) + i * yDistance);
                labelFullScore.Location = new Point(luX + (int)(xDistance * 4.75) + (int)(xDistance / 4.0), (int)(luY / 2.0) + i * yDistance);

                panel1.Controls.Add(labelName);
                panel1.Controls.Add(labelKPI);
                panel1.Controls.Add(labelStandardThreshold);
                panel1.Controls.Add(labelChallengeThreshold);
                panel1.Controls.Add(labelScore);
                panel1.Controls.Add(labelFullScore);
            }
            //panel1.ResumeLayout();
        }

        private void EleShowForScoreQueryControl_Load(object sender, EventArgs e)
        {
            luX = (int)(this.Size.Width / 14.0 + 0.5);
            luY = 15;
            xDistance = (int)(this.Size.Width / 6.0 + 0.5);
            yDistance = 20;
            this.labelControl1.Location = new Point(luX - (int)(xDistance / 3.0), luY);
            this.labelControl2.Location = new Point(luX + xDistance + (int)(xDistance / 3.0) - 5, luY);
            this.labelControl3.Location = new Point(luX + xDistance * 2 + (int)(xDistance / 3.0) - 5, luY);
            this.labelControl4.Location = new Point(luX + xDistance * 3 + (int)(xDistance / 3.0) - 5, luY);
            this.labelControl5.Location = new Point(luX + xDistance * 4 + (int)(xDistance / 3.0) - 5, luY);
            this.labelControl6.Location = new Point(luX + (int)(xDistance * 4.75) + (int)(xDistance / 3.0) - 5, luY);
        }

        public void ClearShow()
        {
            this.panel1.Controls.Clear();
        }
        private List<string> noPencentageEleNames = new List<string>(new string[] { 
            "平均车速", "每呼切换次数", "FTP下载速率", "里程互操作比", "每呼切换数", "FTP平均速率" });

        private void 导出ExcelToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (saveTable != null && saveTable.Rows.Count != 0)
            {
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(saveTable);
            }
        }
    }
}
