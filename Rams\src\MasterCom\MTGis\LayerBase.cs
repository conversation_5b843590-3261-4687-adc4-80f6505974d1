﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Runtime.Serialization;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.MTGis
{
    [Serializable]
    public abstract class LayerBase : ISerializable
    {
        protected string name;

        public VisibleScale VisibleScale { get; set; } = new VisibleScale(0, 9999999.0);

        public bool VisibleScaleEnabled { get; set; } = false;

        protected bool visible = true;

        protected bool enabled = true;

        protected int alpha = 255;//不透明255 透明0
        public int Alpha 
        {
            get 
            { 
                return alpha; 
            }
            set 
            { 
                alpha = value; 
                SetOpacity(value);
            }
        }

        /// <summary>
        /// 需要将所有颜色在此设置透明度,否则不能统一控制透明
        /// </summary>
        /// <param name="curAlpha"></param>
        public virtual void SetOpacity(int curAlpha)
        {
            
        }

        protected MainModel mainModel;
        public virtual MainModel MainModel
        {
            get
            {
                return mainModel;
            }
            set
            {
#if DEBUG 
                //为了防止sonar提示而加的
                Console.Write(1);
#endif
                mainModel = value;
            }
        }

        protected IGisAdapter gisAdapter;
        public IGisAdapter GisAdapter
        {
            get { return gisAdapter; }
        }

        public event EventHandler VisibleChanged;
        
        public bool Fix { get; set; } = false;//若为true，则该图层不会被橡皮擦功能清除，默认为false，会被清除

        public void setName(string v)
        {
            this.name = v;
        }
        public string getName()
        {
            return name;
        }
        public override string ToString()
        {
            if(name!=null && name!="")
            {
                return name;
            }
            else
            {
                return "(未命名LayerBase图层)";
            }
        }
        protected LayerBase(string name)
        {
            this.name = name;
            this.MainModel = MainModel.GetInstance();
            MasterCom.RAMS.Func.MapForm mf = mainModel.MainForm.GetMapForm();
            if (mf != null)
                mf.MapFeatureSelecting += new EventHandler(mapForm_MapFeatureSelecting);
            Fix = false;
            gisAdapterChanged += new EventHandler(gisAdapter_Changed);
        }

        /// <summary>
        /// 绑定gisAdapter并触发事件
        /// </summary>
        /// <param name="adapter"></param>
        public void BindingGisAdapter(IGisAdapter adapter)
        {
            this.gisAdapter = adapter;
            gisAdapterChanged(this, EventArgs.Empty);
        }

        public void FireVisibleChanged()
        {
            if (VisibleChanged != null)
            {
                VisibleChanged(this, EventArgs.Empty);
            }
        }

        public virtual void Invalidate()
        {
            MasterCom.RAMS.Func.MapForm mf = MainModel.GetInstance().MainForm.GetMapForm();
            if (mf != null)
            {
                mf.updateMap();
            }
        }

        public abstract void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics);

        public virtual void mapForm_MapFeatureSelecting(object sender, EventArgs e) { }

        public virtual void GetObjectData(SerializationInfo info, StreamingContext context) { }

        public virtual void LayerDispose()
        {
            MasterCom.RAMS.Func.MapForm mf = mainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.MapFeatureSelecting -= mapForm_MapFeatureSelecting;
            }
        }
        
        public bool IsVisible
        {
            get
            {
                return visible;
            }
            set
            {
                enabled = value;
                visible = value;
                FireVisibleChanged();
            }
        }
        public bool Enabled
        {
            get
            {
                return enabled;
            }
            set
            {
                enabled = value;
                visible = value;
            }
        }

        //================
        public const int ThemeOption_NearestDistance = 1;
        public virtual int GetSupportedThemeOptions()
        {
            return 0;
        }
        public virtual double CalcThemeOption(int themeOption, object[] args)
        {
            return 0;
        }

        private event EventHandler gisAdapterChanged;
        /// <summary>
        /// 重新绑定gisAdapter后根据类型重新赋值当前类型和地图比例
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        public virtual void gisAdapter_Changed(object sender, EventArgs e)
        {
            if (gisAdapter.GetType() == typeof(MtGisAdapter))
            {
                curMapType = LayerMapType.MTGis;
                mapScale = gisAdapter.GetScale();
            }
            else if (gisAdapter.GetType() == typeof(GMapGisAdapter))
            {
                curMapType = LayerMapType.Google;
                mapScale = gisAdapter.GetScale();
            }
        }

        protected double mapScale = 0;
        /// <summary>
        /// 地图比例
        /// </summary>
        public double MapScale
        {
            get { return mapScale; }
        }

        protected LayerMapType curMapType = LayerMapType.Other;
        public LayerMapType CurMapType
        {
            get { return curMapType; }
        }
        
        public double FeatrueMaxZoomScale { get; set; } = 1000;

        protected double getDisplayScale(double scale)
        {
            double disScale = FeatrueMaxZoomScale > scale ? FeatrueMaxZoomScale : scale;
            return disScale;
        }

        /// <summary>
        /// 图层类型
        /// </summary>
        public enum LayerMapType
        {
            Other = 0,
            MTGis = 1,
            Google = 2
        }

    }
}
