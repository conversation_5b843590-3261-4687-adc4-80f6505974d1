﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTHandOverAndCellReselFileInfo
    {
        public int SN { get; set; }
        public string FileName { get; set; }
        public List<ZTHandOverAndCellReselHOInfo> HoInfoList { get; set; }
        public Dictionary<string, ZTHandOverAndCellReselCellInfo> CellDic { get; set; }

        public ZTHandOverAndCellReselFileInfo(string fileName)
        {
            this.FileName = fileName;
            this.HoInfoList = new List<ZTHandOverAndCellReselHOInfo>();
            this.CellDic = new Dictionary<string, ZTHandOverAndCellReselCellInfo>();
        }
    }

    public class ZTHandOverAndCellReselHOInfo
    {
        public int SN { get; set; }
        public List<ZTHandOverAndCellReselEventInfo> EventList { get; set; }
        public string SeqCells { get; set; }

        public ZTHandOverAndCellReselHOInfo()
        {
            this.EventList = new List<ZTHandOverAndCellReselEventInfo>();
            this.SeqCells = string.Empty;
        }
    }

    public class ZTHandOverAndCellReselEventInfo
    {
        public string EventDesc { get; set; }
        public Event Evt { get; set; }

        public ZTHandOverAndCellReselEventInfo(string eventDesc, Event evt)
        {
            this.EventDesc = eventDesc;
            this.Evt = evt;
        }
    }

    public class ZTHandOverAndCellReselCellInfo
    {
        public int SN { get; set; }
        public string CellName { get; set; }
        public int LAC { get; set; }
        public int CI { get; set; }
        public int HoCount { get; set; }
        public Cell GsmCell { get; set; }
        public TDCell TdCell { get; set; }
        public WCell WcdmaCell { get; set; }

        public ZTHandOverAndCellReselCellInfo(Cell gCell)
        {
            this.CellName = gCell.Name;
            this.LAC = gCell.LAC;
            this.CI = gCell.CI;
            this.GsmCell = gCell;
            this.HoCount = 1;
        }

        public ZTHandOverAndCellReselCellInfo(TDCell tdCell)
        {
            this.CellName = tdCell.Name;
            this.LAC = tdCell.LAC;
            this.CI = tdCell.CI;
            this.TdCell = tdCell;
            this.HoCount = 1;
        }

        public ZTHandOverAndCellReselCellInfo(WCell wCell)
        {
            this.CellName = wCell.Name;
            this.LAC = wCell.LAC;
            this.CI = wCell.CI;
            this.WcdmaCell = wCell;
            this.HoCount = 1;
        }

        public ZTHandOverAndCellReselCellInfo(int lac, int ci)
        {
            this.CellName = lac.ToString() + "_" + ci.ToString();
            this.LAC = lac;
            this.CI = ci;
            this.HoCount = 1;
        }

        public static string AddGsmCell(Dictionary<string, ZTHandOverAndCellReselCellInfo> cellDic, Cell gsmCell, int lac, int ci)
        {
            if (gsmCell != null)
            {
                if (!cellDic.ContainsKey(gsmCell.Name))
                {
                    ZTHandOverAndCellReselCellInfo cellInfo = new ZTHandOverAndCellReselCellInfo(gsmCell);
                    cellInfo.SN = cellDic.Count + 1;
                    cellDic.Add(gsmCell.Name, cellInfo);
                }
                else
                {
                    cellDic[gsmCell.Name].HoCount++;
                }

                StringBuilder sb = new StringBuilder();
                sb.Append(gsmCell.Name);
                sb.Append("<");
                sb.Append(gsmCell.LAC.ToString());
                sb.Append("_");
                sb.Append(gsmCell.CI.ToString());
                sb.Append(">");
                return sb.ToString();
            }
            else
            {
                return AddCellWithoutName(cellDic, lac, ci);
            }
        }

        public static string AddTdCell(Dictionary<string, ZTHandOverAndCellReselCellInfo> cellDic, TDCell tdCell, int lac, int ci)
        {
            if (tdCell != null)
            {
                if (!cellDic.ContainsKey(tdCell.Name))
                {
                    ZTHandOverAndCellReselCellInfo cellInfo = new ZTHandOverAndCellReselCellInfo(tdCell);
                    cellInfo.SN = cellDic.Count + 1;
                    cellDic.Add(tdCell.Name, cellInfo);
                }
                else
                {
                    cellDic[tdCell.Name].HoCount++;
                }

                StringBuilder sb = new StringBuilder();
                sb.Append(tdCell.Name);
                sb.Append("<");
                sb.Append(tdCell.LAC.ToString());
                sb.Append("_");
                sb.Append(tdCell.CI.ToString());
                sb.Append(">");
                return sb.ToString();
            }
            else
            {
                return AddCellWithoutName(cellDic, lac, ci);
            }
        }

        public static string AddWCell(Dictionary<string, ZTHandOverAndCellReselCellInfo> cellDic, WCell wCell, int lac, int ci)
        {
            if (wCell != null)
            {
                if (!cellDic.ContainsKey(wCell.Name))
                {
                    ZTHandOverAndCellReselCellInfo cellInfo = new ZTHandOverAndCellReselCellInfo(wCell);
                    cellInfo.SN = cellDic.Count + 1;
                    cellDic.Add(wCell.Name, cellInfo);
                }
                else
                {
                    cellDic[wCell.Name].HoCount++;
                }

                StringBuilder sb = new StringBuilder();
                sb.Append(wCell.Name);
                sb.Append("<");
                sb.Append(wCell.LAC.ToString());
                sb.Append("_");
                sb.Append(wCell.CI.ToString());
                sb.Append(">");
                return sb.ToString();
            }
            else
            {
                return AddCellWithoutName(cellDic, lac, ci);
            }
        }

        public static string AddCellWithoutName(Dictionary<string, ZTHandOverAndCellReselCellInfo> cellDic, int lac, int ci)
        {
            string cellName = lac.ToString() + "_" + ci.ToString();

            if (!cellDic.ContainsKey(cellName))
            {
                ZTHandOverAndCellReselCellInfo cellInfo = new ZTHandOverAndCellReselCellInfo(lac, ci);
                cellInfo.SN = cellDic.Count + 1;
                cellDic.Add(cellName, cellInfo);
            }
            else
            {
                cellDic[cellName].HoCount++;
            }

            StringBuilder sb = new StringBuilder();
            sb.Append(cellName);
            sb.Append("<");
            sb.Append(lac.ToString());
            sb.Append("_");
            sb.Append(ci.ToString());
            sb.Append(">");
            return sb.ToString();
        }
    }

    public class ZTHandOverAndCellReselCondition
    {
        public int TimeLimit { get; set; }
        public bool IsLimitSpeed { get; set; }
        public int SpeedLimitMax { get; set; }
        public int SpeedLimitMin { get; set; }

        public ZTHandOverAndCellReselCondition()
        {
            this.TimeLimit = 10;
            this.IsLimitSpeed = false;
            this.SpeedLimitMax = 20;
            this.SpeedLimitMin = 5;
        }
    }
}
