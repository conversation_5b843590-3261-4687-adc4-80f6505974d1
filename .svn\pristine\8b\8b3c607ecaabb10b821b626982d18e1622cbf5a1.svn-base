﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using System.Drawing.Drawing2D;

namespace MasterCom.RAMS.Func
{
    public partial class MapNRCellLayerBaseProperties : MTLayerPropUserControl
    {
        public MapNRCellLayerBaseProperties()
        {
            InitializeComponent();
        }

        private MapNRCellLayer layer = null;
        public override void Setup(object obj)
        {
            layer = obj as MapNRCellLayer;
            if (layer == null)
            {
                return;
            }
            Text = "通用设置";
            this.numMaxScale.Value = (decimal)layer.FeatrueMaxZoomScale;
            this.checkBoxDrawServer.Checked=layer.DrawServer;
            this.checkBoxDrawIndoor.Checked = layer.DrawIndoor;
            this.checkBoxDrawOutdoor.Checked = layer.DrawOutdoor;

            this.colorSelected.Color = layer.ColorSelected;
            this.colorSvrCell.Color = layer.ColorServerCell;

            this.radioButtonDrawCurrent.Checked = MapNRCellLayer.DrawCurrent;
            this.radioButtonDrawSnapshotTime.Checked = !MapNRCellLayer.DrawCurrent;

            this.numMaxScale.ValueChanged += new EventHandler(numMaxScale_ValueChanged);
            this.checkBoxDrawServer.CheckedChanged += new EventHandler(checkBoxDrawServer_CheckedChanged);
            this.checkBoxDrawIndoor.CheckedChanged += new EventHandler(checkBoxDrawIndoor_CheckedChanged);
            this.checkBoxDrawOutdoor.CheckedChanged += new EventHandler(checkBoxDrawOutdoor_CheckedChanged);
            this.colorSelected.ColorChanged += new EventHandler(colorSelected_ColorChanged);
            this.colorSvrCell.ColorChanged += new EventHandler(colorSvrCell_ColorChanged);
            radioButtonDrawCurrent.CheckedChanged += new EventHandler(radioButtonDrawCurrent_CheckedChanged);
            radioButtonDrawSnapshotTime.CheckedChanged += new EventHandler(radioButtonDrawSnapshotTime_CheckedChanged);
            dateTimePickerTime.ValueChanged += new EventHandler(dateTimePickerTime_ValueChanged);
        }

        void dateTimePickerTime_ValueChanged(object sender, EventArgs e)
        {
            MapNRCellLayer.DrawCurrent = !radioButtonDrawSnapshotTime.Checked;
            MapNRCellLayer.CurShowSnapshotTime = dateTimePickerTime.Value.Date;
        }

        void radioButtonDrawSnapshotTime_CheckedChanged(object sender, EventArgs e)
        {
            MapNRCellLayer.DrawCurrent = !radioButtonDrawSnapshotTime.Checked;
            dateTimePickerTime.Enabled = radioButtonDrawSnapshotTime.Checked;
            MapNRCellLayer.CurShowSnapshotTime = dateTimePickerTime.Value.Date;
        }

        void radioButtonDrawCurrent_CheckedChanged(object sender, EventArgs e)
        {
            MapNRCellLayer.DrawCurrent = radioButtonDrawCurrent.Checked;
        }

        void colorSvrCell_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorServerCell = colorSvrCell.Color;
        }

        void colorSelected_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorSelected = colorSelected.Color;
        }

        void checkBoxDrawOutdoor_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawOutdoor = checkBoxDrawOutdoor.Checked;
        }

        void checkBoxDrawIndoor_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawIndoor = checkBoxDrawIndoor.Checked;
        }

        void checkBoxDrawServer_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawServer = checkBoxDrawServer.Checked;
        }

        void numMaxScale_ValueChanged(object sender, EventArgs e)
        {
            layer.FeatrueMaxZoomScale = (double)numMaxScale.Value;
        }
    }
}
