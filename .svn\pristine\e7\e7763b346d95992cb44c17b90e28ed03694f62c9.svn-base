﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRIndoorStationSettingDlg_XJ : BaseDialog
    {
        public NRIndoorStationSettingDlg_XJ()
        {
            InitializeComponent();
        }
        public NRIndoorStationSettingDlg_XJ(NRIndoorStationSettingDlgConfigModel_XJ condition)
        {
            InitializeComponent();
            setCondition(condition);
        }

        public void setCondition(NRIndoorStationSettingDlgConfigModel_XJ condition)
        {
            if (condition == null)
            {
                return;
            }

            txtAccessRate.Value = Convert.ToDecimal(condition.AccessSuccessRate);
            txtPing.Value = Convert.ToDecimal(condition.PingTimeDelay);
            txtDownRsrp.Value = Convert.ToDecimal(condition.DownTestRSRP);
            txtDownAvgSINR.Value = Convert.ToDecimal(condition.DownTestAvgSINR);
            txtDownThrought.Value = Convert.ToDecimal(condition.DownThroughput);
            txtUploadRSRP.Value = Convert.ToDecimal(condition.UploadTestRSRP);
            txtUploadAvgSINR.Value = Convert.ToDecimal(condition.UploadAvgSINR);
            txtUploadThrought.Value = Convert.ToDecimal(condition.UploadThroughput);
            txtCallDelay.Value = Convert.ToDecimal(condition.CallTimeDelay);
            txtCallRate.Value = Convert.ToDecimal(condition.CallSuccessRate);
            txtConnSuccessRate.Value = Convert.ToDecimal(condition.ConnectionSuccessRate);
            txtSwitchSuccessRate.Value = Convert.ToDecimal(condition.SwitchSuccessRate);
        }

        public NRIndoorStationSettingDlgConfigModel_XJ GetCondition()
        {
            NRIndoorStationSettingDlgConfigModel_XJ condition = new NRIndoorStationSettingDlgConfigModel_XJ();
            condition.AccessSuccessRate = txtAccessRate.Value.ToString();
            condition.PingTimeDelay = txtPing.Value.ToString();
            condition.DownTestRSRP = txtDownAvgSINR.Value.ToString();
            condition.DownTestAvgSINR = txtDownAvgSINR.Value.ToString();
            condition.DownThroughput = txtDownThrought.Value.ToString();
            condition.UploadTestRSRP = txtUploadRSRP.Value.ToString();
            condition.UploadAvgSINR = txtAccessRate.Value.ToString();
            condition.UploadThroughput = txtUploadThrought.Value.ToString();
            condition.CallTimeDelay = txtCallDelay.Value.ToString();
            condition.CallSuccessRate = txtCallRate.Value.ToString();
            condition.ConnectionSuccessRate = txtConnSuccessRate.Value.ToString();
            condition.SwitchSuccessRate = txtSwitchSuccessRate.Value.ToString();
            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            var cond = GetCondition();
            if (cond != null)
            {
                NRIndoorStationSettingDlgConfig_XJ.Instance.SaveConfig(cond);
                DialogResult = DialogResult.OK;
            }
            else
            {
                MessageBox.Show("主库连接设置不能为空");
            }
        }

        private void groupBox3_Enter(object sender, EventArgs e)
        {

        }
    }
}
