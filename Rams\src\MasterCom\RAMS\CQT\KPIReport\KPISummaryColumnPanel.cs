﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.CQT
{
    public partial class KPISummaryColumnPanel : UserControl
    {
        public KPISummaryColumnPanel()
        {
            InitializeComponent();
            scoreRangeColorSettingPanel1.SetScoreColumnCaption("总分范围");
        }
        CQTKPISummaryColumn column;
        List<SummaryItem> summaryItems;
        double minScore = 0;
        double maxScore = 0;
        public void SetColumn(CQTKPISummaryColumn column) 
        {
            gridView.CellValueChanged += gridView_CellValueChanged;
            this.column = column;
            summaryItems = column.SummaryColumns;
            gridControl.DataSource = summaryItems;
            gridControl.RefreshDataSource();
            calScoreRange();
            setScoreRangeColors();
            // gridView.CellValueChanged -= gridView_CellValueChanged;
        }

        private void setScoreRangeColors()
        {
            scoreRangeColorSettingPanel1.SetScoreColorRanges(column.ScoreRangeColors, minScore, maxScore);
        }
        
        private void gridView_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            if (e.Column.Equals(this.gcWeight))
            {
                calScoreRange();
                checkRanges();
                setScoreRangeColors();
            }
        }

        private void calScoreRange()
        {
            minScore = 0;
            maxScore = 0;
            foreach (SummaryItem item in summaryItems)
            {
                if (item.Weight>=0)
                {
                    minScore += item.MinScore * item.Weight;
                    maxScore += item.MaxScore * item.Weight;
                }
                else
                {
                    minScore += item.MaxScore * item.Weight;
                    maxScore += item.MinScore * item.Weight;
                }
             
            }
            txtMinScore.Text = minScore.ToString();
            txtMaxScore.Text = maxScore.ToString();
        }

        private void checkRanges()
        {
            foreach (DTParameterRangeColor item in column.ScoreRangeColors)
            {
                if (item.Min<minScore)
                {
                    item.Min = (float)minScore;
                }
                if (item.Min>maxScore)
                {
                    item.Min = (float)maxScore;
                }
                if (item.Max>maxScore)
                {
                    item.Max = (float)maxScore;
                }
            }
        }

    }
}
