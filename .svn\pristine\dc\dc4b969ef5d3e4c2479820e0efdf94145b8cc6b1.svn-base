﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;

using DevExpress.XtraGrid.Columns;

using MasterCom.NOP.WF.Core;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.NOP
{
    public class ReasonStatRow
    {
        public ReasonStatRow(string cityName)
        {
            this.cityName = cityName;
            this.reasonCnts = new int[sIntegerColumnNames.Length];
        }

        public virtual void ProcessOrder(Task task)
        {
            string reason = task.GetValue("预处理主要原因") as string;
            if (string.IsNullOrEmpty(reason))
            {
                reason = "其他";
            }

            int index = 0;
            for (; index < sIntegerColumnNames.Length; ++index)
            {
                if (sIntegerColumnNames[index] == reason)
                {
                    break;
                }
            }

            if (index >= sIntegerColumnNames.Length)
            {
                index = sIntegerColumnNames.Length - 1; // 未找到任何原因，默认为最后一个，即"其他"
            }
            ++reasonCnts[index];
        }

        public object[] GetResult()
        {
            List<object> retObjs = new List<object>();
            retObjs.Add(this.cityName);
            foreach (int cnt in reasonCnts)
            {
                retObjs.Add(cnt);
            }
            return retObjs.ToArray();
        }

        protected string cityName;

        protected int[] reasonCnts = null;

        public static DataColumn[] DataColumns
        {
            get
            {
                List<DataColumn> gridColumns = new List<DataColumn>();

                DataColumn column = new DataColumn();
                column.ColumnName = "地市名称";
                column.DataType = typeof(string);
                gridColumns.Add(column);

                foreach (string columnName in sIntegerColumnNames)
                {
                    AddIntegerDataColumn(gridColumns, columnName);
                }

                return gridColumns.ToArray();
            }
        }

        protected static void AddIntegerDataColumn(List<DataColumn> gridColumns, string name)
        {
            DataColumn column = new DataColumn();
            column.ColumnName = name;
            column.DataType = typeof(int);
            gridColumns.Add(column);
        }

        private static string[] sIntegerColumnNames = new string[] {
                "故障",
                "弱覆盖",
                "质差",
                "高干扰问题",
                "资源问题",
                "射频优化问题",
                "其他" };
    }

    public class ReasonStater
    {
        public ReasonStater()
        {
            cityRowDic = new Dictionary<string, ReasonStatRow>();
            string[] cityNames = DistrictManager.GetInstance().DistrictNames;
            foreach (string city in cityNames)
            {
                if (!string.IsNullOrEmpty(city))
                {
                    if (summaryRow == null)
                    {
                        summaryRow = GetStatRow("汇总");
                    }
                    else
                    {
                        string key = city.TrimEnd('市');
                        cityRowDic[key] = GetStatRow(key);
                    }
                }
            }
        }

        public virtual object GetResult(DateTime sTime, DateTime eTime)
        {
            // 工单查询
            TaskOrderQuerier querier = new TaskOrderQuerier(MainModel.GetInstance());
            ICollection<Task> tasks = querier.Query(sTime, eTime);
            if (tasks == null)
            {
                throw (new Exception("工单查询失败"));
            }

            // 工单处理
            foreach (Task task in tasks)
            {
                string city = task.GetValue("地市") as string;
                if (string.IsNullOrEmpty(city))
                {
                    continue;
                }

                if (summaryRow != null)
                {
                    summaryRow.ProcessOrder(task);
                }

                string key = city.TrimEnd('市');
                if (cityRowDic.ContainsKey(key))
                {
                    cityRowDic[key].ProcessOrder(task);
                }
            }

            // 地市排序
            System.Globalization.CultureInfo cultureInfo = new System.Globalization.CultureInfo("zh-CN");
            List<string> citys = new List<string>(cityRowDic.Keys);
            citys.Sort(delegate(string t1, string t2) {
                return string.Compare(t1, t2, false, cultureInfo);
            });

            // 构造DataTable返回
            DataTable dtTable = new DataTable();
            dtTable.Columns.AddRange(GetColumns());
            foreach (string c in citys)
            {
                AddTableRow(dtTable, cityRowDic[c]);
            }
            AddTableRow(dtTable, summaryRow);

            return dtTable;
        }

        protected ReasonStatRow GetStatRow(string key)
        {
            return new ReasonStatRow(key);
        }

        protected virtual DataColumn[] GetColumns()
        {
            return ReasonStatRow.DataColumns;
        }

        private void AddTableRow(DataTable dtTable, ReasonStatRow row)
        {
            DataRow dr = dtTable.NewRow();
            object[] rowValues = row.GetResult();
            dr.ItemArray = rowValues;
            dtTable.Rows.Add(dr);
        }

        private readonly Dictionary<string, ReasonStatRow> cityRowDic;

        private readonly ReasonStatRow summaryRow;
    }
}
