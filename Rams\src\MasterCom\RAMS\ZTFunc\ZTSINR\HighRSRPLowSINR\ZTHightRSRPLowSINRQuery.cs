﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTHightRSRPLowSINRQuery : ZTHightRSRPLowSINRBaseQuery<ZTHightRSRPLowSINRQuery>
    {
        protected override string themeName { get { return "TD_LTE_SINR"; } }
        protected override string rsrpName { get { return "lte_RSRP"; } }
        protected override string sinrName { get { return "lte_SINR"; } }

        public ZTHightRSRPLowSINRQuery()
            : base()
        {
            init();
        }

        public ZTHightRSRPLowSINRQuery(ServiceName serviceName)
            : this()
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        }

        protected void init()
        {
            this.IsCanExportResultMapToWord = true;
            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetLteAllServiceTypes());
#if LT
            carrierID = CarrierType.ChinaUnicom;
#else
            carrierID = CarrierType.ChinaMobile;
#endif
        }

        public override string Name
        {
            get { return "强信号弱质量_LTE"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22014, this.Name);//////
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.质量; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["MaxSinr"] = WeakCond.MaxSinr;
                param["MinRsrp"] = WeakCond.MinRsrp;
                param["Percentage"] = WeakCond.Percentage;
                param["Check2TPDistance"] = WeakCond.Check2TPDistance;
                param["Max2TPDistance"] = WeakCond.Max2TPDistance;
                param["CheckDistance"] = WeakCond.CheckDistance;
                param["MinStayDistance"] = WeakCond.MinStayDistance;
                param["CheckTime"] = WeakCond.CheckTime;
                param["MinStaySecond"] = WeakCond.MinStaySecond;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("MaxSinr"))
                {
                    WeakCond.MaxSinr = (float)param["MaxSinr"];
                }
                if (param.ContainsKey("MinRsrp"))
                {
                    WeakCond.MinRsrp = (float)param["MinRsrp"];
                }
                if (param.ContainsKey("Percentage"))
                {
                    WeakCond.Percentage = (double)param["Percentage"];
                }
                if (param.ContainsKey("Check2TPDistance"))
                {
                    WeakCond.Check2TPDistance = (bool)param["Check2TPDistance"];
                }
                if (param.ContainsKey("Max2TPDistance"))
                {
                    WeakCond.Max2TPDistance = (double)param["Max2TPDistance"];
                }
                if (param.ContainsKey("CheckDistance"))
                {
                    WeakCond.CheckDistance = (bool)param["CheckDistance"];
                }
                if (param.ContainsKey("MinStayDistance"))
                {
                    WeakCond.MinStayDistance = (double)param["MinStayDistance"];
                }
                if (param.ContainsKey("CheckTime"))
                {
                    WeakCond.CheckTime = (bool)param["CheckTime"];
                }
                if (param.ContainsKey("MinStaySecond"))
                {
                    WeakCond.MinStaySecond = (double)param["MinStaySecond"];
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new WeakSinrHighRsrpRoadProperties_LTE(this);
            }
        }

        protected override void saveBackgroundData()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (HighRSRPLowSINR item in resultList)
            {
                BackgroundResult result = item.ConvertToBackgroundResult();
                result.SubFuncID = GetSubFuncID();
                result.ProjectString = BackgroundFuncBaseSetting.GetInstance().projectType;
                bgResultList.Add(result);
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Road(GetSubFuncID(), curAnaFileInfo, bgResultList);
            resultList.Clear();
        }
        protected override void initBackgroundImageDesc()
        {
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                float percent = bgResult.GetImageValueFloat();
                bgResult.ImageDesc = "采样点占比(%)：" + percent;
            }
        }
        #endregion
    }

    public class HighRSRPLowSINRCondition
    {
        public HighRSRPLowSINRCondition()
        {
            MinRsrp = -85;
            MaxSinr = 5;
            CheckTime = true;
            MinStaySecond = 10;
            MinStayDistance = 50;
            Max2TPDistance = 20;
            Percentage = 70;

        }
        public double MinStaySecond
        {
            get;
            set;
        }
        public bool CheckTime
        {
            get;
            set;
        }
        public double MinStayDistance
        {
            get;
            set;
        }
        public bool CheckDistance
        {
            get;
            set;
        }
        public double Max2TPDistance
        {
            get;
            set;
        }
        public bool Check2TPDistance
        {
            get;
            set;
        }

        public float MinRsrp
        {
            get;
            set;
        }

        public float MaxSinr
        {
            get;
            set;
        }

        public bool IsMatchIndicator(float? sinr)
        {
            return sinr >= -50 && sinr <= MaxSinr;
        }

        public bool CheckStayTime(double second)
        {
            if (CheckTime)
            {
                return second >= MinStaySecond;
            }
            return true;
        }

        public bool CheckStayDistance(double dis)
        {
            if (CheckDistance)
            {
                return dis >= MinStayDistance;
            }
            return true;
        }

        public double Percentage { get; set; }
    }

    public class ZTHightRSRPLowSINRQuery_FDD : ZTHightRSRPLowSINRBaseQuery<ZTHightRSRPLowSINRQuery_FDD>
    {
        protected override string themeName { get { return "LTE_FDD:SINR"; } }
        protected override string rsrpName { get { return "lte_fdd_RSRP"; } }
        protected override string sinrName { get { return "lte_fdd_SINR"; } }

        public ZTHightRSRPLowSINRQuery_FDD()
            : base()
        {
            ServiceTypes.Clear();
            carrierID = CarrierType.ChinaUnicom;
        }

        public override string Name
        {
            get
            {
                return "强信号弱质量_LTE_FDD";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26022, this.Name);//////
        }
    }

    public class ZTHightRSRPLowSINRQuery_FDD_VOLTE : ZTHightRSRPLowSINRBaseQuery<ZTHightRSRPLowSINRQuery_FDD_VOLTE>
    {
        protected override string themeName { get { return "LTE_FDD:SINR"; } }
        protected override string rsrpName { get { return "lte_fdd_RSRP"; } }
        protected override string sinrName { get { return "lte_fdd_SINR"; } }

        public ZTHightRSRPLowSINRQuery_FDD_VOLTE()
            : base()
        {
            carrierID = CarrierType.ChinaUnicom;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }

        public override string Name
        {
            get
            {
                return "VOLTE_FDD强信号弱质量";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30014, this.Name);//////
        }
    }
}
