﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Collections;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Util;
using MasterCom.Util.UiEx;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public class ReselectionManager_LTE
    {
        protected ReselectionManager_LTE()
        {

        }

        #region 乒乓切换分析

        /// <summary>
        /// 获取切换重新事件小区变更序列描述
        /// </summary>
        /// <param name="list">切换重新事件列表</param>
        /// <returns></returns>
        public static string MakeCellUpdateStr(List<List<Event>> list)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < list.Count; i++)
            {
                sb.Append(MakeNetworkSelectCellUpdateStr(list[i]));
                sb.Append("；\r\n");
            }
            return sb.ToString();
        }
        /// <summary>
        /// 选网小区变换序列
        /// </summary>
        /// <param name="eList"></param>
        /// <returns></returns>
        public static string MakeNetworkSelectCellUpdateStr(List<Event> eList)
        {
            string lastLacCi = "";
            StringBuilder sb = new StringBuilder();
            for (int j = 0; j < eList.Count; j++)
            {
                Event e = eList[j];
                string cellName = "";

                int eLac1 = (int)e["LAC"];
                int eCi1 = (int)e["CI"];
                ICell cellSrc = e.GetSrcCell();

                if (lastLacCi != (eLac1 + "_" + eCi1))
                {
                    addUpdateSign2StringBuilder(ref sb, e);
                    cellName = getCellNameDes(cellSrc, eLac1, eCi1);
                    sb.Append(cellName);
                }

                int eLac2 = (int)e["TargetLAC"];
                int eCi2 = (int)e["TargetCI"];
                ICell cellTar = e.GetTargetCell();
                addUpdateSign2StringBuilder(ref sb, e);
                cellName = getCellNameDes(cellTar, eLac2, eCi2);
                sb.Append(cellName);

                lastLacCi = eLac2 + "_" + eCi2;
            }
            return sb.ToString();
        }
        protected static string getCellNameDes(ICell cell, int lac, int ci)
        {
            string cellName = "";
            if (cell != null)
            {
                cellName = cell.Name + "(" + lac + "," + ci + ")";
            }
            else
            {
                cellName = "(LAC:" + lac + ",CI:" + ci + ")";
            }
            return cellName;
        }

        private static void addUpdateSign2StringBuilder(ref StringBuilder sb, Event e)
        {
            if (e.ID == 40 || e.ID == 137 || e.ID == 139 || e.ID == 179 || e.ID == 537
                    || e.ID == 539 || e.ID == 579)//小区重选
            {
                sb.Append("=>");
            }
            else
            {
                sb.Append("->");
            }
        }

        #endregion

        #region 过频繁分析
        /// <summary>
        /// 获取切换(重选)过频繁结果
        /// </summary>
        /// <param name="dataManageBak">文件列表</param>
        /// <param name="timeLimit">时间限制(多少秒内发生的)</param>
        /// <param name="distanceLimit">距离限制</param>
        /// <param name="handoverCount">切换次数</param>
        /// <param name="handoverEvents">输出的问题点涉及到的事件列表</param>
        /// <returns>形成过频繁问题的文件列表</returns>
        public static List<HandoverFileDataManager> GetHandoverTooMuchResult(List<DTFileDataManager> dataManageBak, int timeLimit, int distanceLimit,
            int handoverCount, List<Event> handoverEvents)
        {
            handoverEvents.Clear();
            List<HandoverFileDataManager> list = new List<HandoverFileDataManager>();
            for (int index = 0; index < dataManageBak.Count; index++)
            {
                DTFileDataManager dtFileDataManager = dataManageBak[index];
                List<Event> events = new List<Event>();
                HandoverFileDataManager handoverFile = GetHandoverTooMuchResult(dtFileDataManager, timeLimit,
                    distanceLimit, handoverCount, events);
                if (handoverFile.HandoverTimes > 0)
                {
                    handoverEvents.AddRange(events);
                    handoverFile.Index = list.Count + 1;
                    list.Add(handoverFile);
                }
            }
            return list;
        }

        public static HandoverFileDataManager GetHandoverTooMuchResult(DTFileDataManager dtFileDataManager, int timeLimit, int distanceLimit,
            int handoverCount, List<Event> handoverEvents)
        {
            handoverEvents.Clear();
            DTFileDataManager dtFileDataManagerNew = new DTFileDataManager(dtFileDataManager.FileID, dtFileDataManager.FileName,
                dtFileDataManager.ProjectType, dtFileDataManager.TestType, dtFileDataManager.CarrierType, dtFileDataManager.LogTable,
                dtFileDataManager.SampleTableName, dtFileDataManager.ServiceType, dtFileDataManager.MoMtFlag);
            HandoverFileDataManager item = new HandoverFileDataManager(dtFileDataManagerNew);
            List<Event> events;
            List<List<Event>> eventsList;
            if (isTooFrequent(dtFileDataManager.Events, timeLimit, distanceLimit, handoverCount, out events, out eventsList))
            {
                handoverEvents.AddRange(events);

                foreach (Event e in events)
                {
                    dtFileDataManagerNew.Add(e);
                }

                item.Des = MakeCellUpdateStr(eventsList);
                item.Events.AddRange(events);
                item.EventsList = eventsList;
                item.HandoverTimes = eventsList.Count;
            }
            return item;
        }

        private static bool isTooFrequent(List<Event> events, int secondLimit, int distanceLimit, int timesLimit, out List<Event> resultEvents,
            out List<List<Event>> eventsList)
        {
            List<Event> timeEventQueue = new List<Event>();
            int indexBegin = 0;
            int indexEnd = -1;
            double distance = 0;
            List<int> indexBegins = new List<int>();
            List<int> indexEnds = new List<int>();
            foreach (Event e in events)
            {
                setTimeEventQueue(secondLimit, distanceLimit, timeEventQueue, ref indexBegin, ref distance, e);
                indexEnd++;
                if (timeEventQueue.Count >= timesLimit)
                {
                    int idx = indexBegins.IndexOf(indexBegin);
                    if (idx >= 0)
                    {
                        indexEnds[idx] = indexEnd;
                    }
                    else
                    {
                        indexBegins.Add(indexBegin);
                        indexEnds.Add(indexEnd);
                    }
                }
            }

            eventsList = new List<List<Event>>();
            List<List<Event>> eventsListTem = new List<List<Event>>();
            List<List<Event>> eventsListTemReject = new List<List<Event>>();
            List<Event> rejectEvents = new List<Event>();
            addEventsListTem(events, timesLimit, indexBegins, indexEnds, eventsListTem, rejectEvents);

            resultEvents = getResultEvents(events, indexBegins, indexEnds, rejectEvents);

            addEventsListTemReject(timesLimit, eventsListTem, eventsListTemReject, rejectEvents);

            List<List<int>> iInIdList = getInIdList(eventsListTemReject);

            addEventsList(eventsList, eventsListTemReject, iInIdList);
            return resultEvents.Count > 0;
        }

        private static void setTimeEventQueue(int secondLimit, int distanceLimit, List<Event> timeEventQueue, ref int indexBegin, ref double distance, Event e)
        {
            int timeLimit = e.Time - secondLimit;
            while (timeEventQueue.Count > 0 && (timeEventQueue[0].Time < timeLimit ||
                (timeEventQueue[0].Time == timeLimit && timeEventQueue[0].Millisecond <= e.Millisecond) ||
                distance + MathFuncs.GetDistance(timeEventQueue[timeEventQueue.Count - 1].Longitude,
                timeEventQueue[timeEventQueue.Count - 1].Latitude, e.Longitude, e.Latitude) >= distanceLimit))
            {
                if (distance > 0.01)
                {
                    distance -= MathFuncs.GetDistance(timeEventQueue[0].Longitude, timeEventQueue[0].Latitude,
                        timeEventQueue[1].Longitude, timeEventQueue[1].Latitude);
                }
                timeEventQueue.RemoveAt(0);
                indexBegin++;
            }
            if (timeEventQueue.Count > 0)
            {
                distance += MathFuncs.GetDistance(timeEventQueue[timeEventQueue.Count - 1].Longitude,
                timeEventQueue[timeEventQueue.Count - 1].Latitude, e.Longitude, e.Latitude);
            }
            timeEventQueue.Add(e);
        }

        private static void addEventsListTem(List<Event> events, int timesLimit, List<int> indexBegins, List<int> indexEnds, List<List<Event>> eventsListTem, List<Event> rejectEvents)
        {
            for (int i = 0; i < indexBegins.Count; i++)
            {
                List<Event> eList = new List<Event>();
                int iBegin = indexBegins[i];
                int iEnd = indexEnds[i];
                List<string> strReselectTypeList = new List<string>();
                for (int j = iBegin; j <= iEnd; j++)
                {
                    HandoverItem hi = new HandoverItem(events[j], true);
                    if (strReselectTypeList.Count == 0
                        || strReselectTypeList[strReselectTypeList.Count - 1] != hi.OverType)
                    {
                        strReselectTypeList.Add(hi.OverType);
                        eList.Add(events[j]);
                    }
                    else
                    {
                        rejectEvents.Add(events[j]);
                    }
                }
                if (eList.Count >= timesLimit)
                    eventsListTem.Add(eList);
            }
        }

        private static List<Event> getResultEvents(List<Event> events, List<int> indexBegins, List<int> indexEnds, List<Event> rejectEvents)
        {
            List<Event> resultEvents = new List<Event>();
            for (int index = 0; index < events.Count; index++)
            {
                for (int index2 = 0; index2 < indexBegins.Count; index2++)
                {
                    if (indexBegins[index2] <= index && index <= indexEnds[index2]
                        && !rejectEvents.Contains(events[index]))
                    {
                        resultEvents.Add(events[index]);
                        break;
                    }
                }
            }

            return resultEvents;
        }

        private static void addEventsListTemReject(int timesLimit, List<List<Event>> eventsListTem, List<List<Event>> eventsListTemReject, List<Event> rejectEvents)
        {
            for (int k = 0; k < eventsListTem.Count; k++)
            {
                List<Event> eList = new List<Event>();
                foreach (Event e in eventsListTem[k])
                {
                    if (!rejectEvents.Contains(e))
                        eList.Add(e);
                }
                if (eList.Count >= timesLimit)
                    eventsListTemReject.Add(eList);
            }
        }

        private static List<List<int>> getInIdList(List<List<Event>> eventsListTemReject)
        {
            List<List<int>> iInIdList = new List<List<int>>();
            for (int m = 0; m < eventsListTemReject.Count - 1; m++)
            {
                List<int> iInId = new List<int>();
                for (int n = m + 1; n < eventsListTemReject.Count; n++)
                {
                    if (eventsListTemReject[m][eventsListTemReject[m].Count - 1].Time
                        < eventsListTemReject[n][0].Time)
                    {
                        break;
                    }
                    iInId.Add(n);
                }
                iInIdList.Add(iInId);
            }

            return iInIdList;
        }

        private static void addEventsList(List<List<Event>> eventsList, List<List<Event>> eventsListTemReject, List<List<int>> iInIdList)
        {
            if (iInIdList.Count == 0 && eventsListTemReject.Count != 0)
            {
                eventsList.AddRange(eventsListTemReject);
            }
            else
            {
                addEventsListByInIdList(eventsList, eventsListTemReject, iInIdList);
                if (iInIdList.Count > 0 && iInIdList[iInIdList.Count - 1].Count == 0
                    && eventsListTemReject.Count != 0)
                {
                    eventsList.Add(eventsListTemReject[eventsListTemReject.Count - 1]);
                }
            }
        }

        private static void addEventsListByInIdList(List<List<Event>> eventsList, List<List<Event>> eventsListTemReject, List<List<int>> iInIdList)
        {
            List<int> iDealID = new List<int>();
            for (int iR = 0; iR < iInIdList.Count; iR++)
            {
                if (!iDealID.Contains(iR))
                {
                    iDealID.Add(iR);
                    if (iR == iInIdList.Count - 1 && iInIdList[iR].Count != 0)
                        iDealID.Add(iInIdList[iR][0]);
                    if (iInIdList[iR].Count == 0 || (iR == iInIdList.Count - 1))
                    {
                        List<Event> eList = getEList(eventsListTemReject, iDealID);
                        eventsList.Add(eList);
                        iDealID.Clear();
                    }
                }
            }
        }

        private static List<Event> getEList(List<List<Event>> eventsListTemReject, List<int> iDealID)
        {
            List<Event> eList = new List<Event>();
            foreach (int index in iDealID)
            {
                foreach (Event ev in eventsListTemReject[index])
                {
                    if (!eList.Contains(ev))
                        eList.Add(ev);
                }
            }

            return eList;
        }
        #endregion

        #region 获取问题事件组(如乒乓切换，切换过频繁)的详细指标
        public static void GetHandoverDetails(List<HandoverFileDataManager> handoverFileList)
        {
            try
            {
                foreach (HandoverFileDataManager handoverFile in handoverFileList)
                {
                    GetHandoverDetails(handoverFile);
                }
            }
            catch
            {
                //continue
            }
        }

        public static void GetHandoverDetails(HandoverFileDataManager handoverFile)
        {
            ReplayFileInPeriod(handoverFile);
            List<TestPoint> tpList = new List<TestPoint>();
            foreach (DTFileDataManager dtfdm in MainModel.GetInstance().DTDataManager.FileDataManagers)
            {
                if (dtfdm.IsEqual(handoverFile.Tag as DTFileDataManager))
                {
                    tpList.AddRange(dtfdm.TestPoints);
                    addHandoverEventDic(handoverFile, dtfdm);
                    dtfdm.TestPoints.Clear();
                    break;
                }
            }

            getHandoverItems(handoverFile, tpList);
        }

        private static void addHandoverEventDic(HandoverFileDataManager handoverFile, DTFileDataManager dtfdm)
        {
            for (int i = 0; i < handoverFile.Events.Count; i++)
            {
                Event ev = handoverFile.Events[i];
                List<HandoverCellItem> hoCellList = new List<HandoverCellItem>();
                hoCellList.AddRange(statNetworkSelectCells(dtfdm, ev));
                HandoverItem handoverItem = new HandoverItem(ev, true);
                foreach (HandoverCellItem cellItem in hoCellList)
                {
                    if (cellItem == null) continue;
                    cellItem.Ev = ev;
                    if (cellItem.Type == HandoverCellType.BeforeHandover)
                    {
                        handoverItem.CellItemBefore = cellItem;
                    }
                    else
                    {
                        handoverItem.CellItemAfter = cellItem;
                    }
                }
                handoverFile.HandoverEventDic[ev] = handoverItem;
            }
        }

        private static void ReplayFileInPeriod(HandoverFileDataManager dtfdmi)
        {
            DIYReplayFileWithNoWaitBox qb = new DIYReplayFileWithNoWaitBox(MainModel.GetInstance());
            qb.FilterByPeriod = true;
            QueryCondition condition = new QueryCondition();
            condition.QueryType = 2;

            DTFileDataManager dtfdm = dtfdmi.Tag as DTFileDataManager;
            FileInfo fileInfo = new FileInfo();
            fileInfo.Name = dtfdm.FileName;
            fileInfo.ProjectID = dtfdm.ProjectType;
            fileInfo.ID = dtfdm.FileID;
            fileInfo.LogTable = dtfdm.LogTable;
            fileInfo.ServiceType = dtfdm.ServiceType;
            fileInfo.SampleTbName = dtfdm.SampleTableName;
            condition.FileInfos.Add(fileInfo);
            condition.Periods.Add(GetTimePeriod(dtfdmi.Events));

            qb.SetQueryCondition(condition);
            qb.Query();
        }

        private static TimePeriod GetTimePeriod(List<Event> events)
        {
            return new TimePeriod(events[0].DateTime.AddSeconds(-10), events[events.Count - 1].DateTime.AddSeconds(5));
        }
        /// <summary>
        /// 计算事件选网前后小区指标信息
        /// </summary>
        /// <param name="dtfdm"></param>
        /// <param name="e"></param>
        /// <returns></returns>
        private static List<HandoverCellItem> statNetworkSelectCells(DTFileDataManager dtfdm, Event e)
        {
            List<HandoverCellItem> hoCellList = new List<HandoverCellItem>();
            HandoverCellItem tpsBefore = null;
            HandoverCellItem tpsAfter = null;
            tpsBefore = new HandoverCellItem(e, HandoverCellType.BeforeHandover);  //选网前
            tpsAfter = new HandoverCellItem(e, HandoverCellType.AfterHandover);  //选网后 
            hoCellList.Add(tpsBefore);
            hoCellList.Add(tpsAfter);
            dtfdm.TestPoints.Sort(TestPointSortByMillsecondeTime.GetCompareByTime());
            int index = getChangeTestpointIndex(dtfdm.TestPoints, e);
            if (index == -1)
            {
                return hoCellList;
            }
            TestPoint tp = new TestPoint();

            for (int i = index; i >= 0; i--)
            {
                tp = dtfdm.TestPoints[i];
                long timeTestpoint = tp.Time * 1000L + tp.Millisecond;
                long tiemEvent = e.Time * 1000L + e.Millisecond;
                if ((timeTestpoint + 5 * 1000) < tiemEvent)
                {
                    break;
                }
                AddTpCellItemInfo(tpsBefore, tp, e);
            }

            for (int i = index + 1; i < dtfdm.TestPoints.Count; i++)
            {
                long timeTestpoint = tp.Time * 1000L + tp.Millisecond;
                long tiemEvent = e.Time * 1000L + e.Millisecond;
                tp = dtfdm.TestPoints[i];
                if ((tiemEvent + 5 * 1000) < timeTestpoint)
                {
                    break;
                }
                AddTpCellItemInfo(tpsAfter, tp, e);
            }

            return hoCellList;
        }
        /// <summary>
        /// 采样点算入相应小区信息(频繁选网)
        /// </summary>
        /// <param name="cellItem">小区信息对象</param>
        /// <param name="tp">TestPoint</param>
        private static void AddTpCellItemInfo(HandoverCellItem cellItem, TestPoint tp, Event e)
        {
            if (cellItem.Type == HandoverCellType.BeforeHandover)
            {
                setBeforeHandover(cellItem, tp, e);
            }
            else if (cellItem.Type == HandoverCellType.AfterHandover)
            {
                setAfterHandOver(cellItem, tp, e);
            }
        }

        private static void setBeforeHandover(HandoverCellItem cellItem, TestPoint tp, Event e)
        {
            if (e.ID == 1117 || e.ID == 1300 || e.ID == 1302)
            {
                setLteValue(cellItem, tp);
            }
            else if (e.ID == 1120 || e.ID == 1304 || e.ID == 1308)
            {
                setLteTdValue(cellItem, tp);
            }
            else if (e.ID == 1306 || e.ID == 1310)
            {
                setGsmValue(cellItem, tp);
            }
        }

        private static void setAfterHandOver(HandoverCellItem cellItem, TestPoint tp, Event e)
        {
            if (e.ID == 1120 || e.ID == 1306 || e.ID == 1308)
            {
                setLteValue(cellItem, tp);
            }
            else if (e.ID == 1117 || e.ID == 1302 || e.ID == 1310)
            {
                setLteTdValue(cellItem, tp);
            }
            else if (e.ID == 1306 || e.ID == 1310)
            {
                setGsmValue(cellItem, tp);
            }
        }

        private static void setLteValue(HandoverCellItem cellItem, TestPoint tp)
        {
            float? rxlev = (float?)tp["lte_RSRP"];
            float? rxqual = (float?)tp["lte_SINR"];
            if (-141 <= rxlev && rxlev <= 25)
            {
                cellItem.Rxlev += (int)(float)rxlev;
                cellItem.RxlevCount++;
            }
            if (-50 <= rxqual && rxqual <= 50)
            {
                cellItem.Rxqual += (int)(float)rxqual;
                cellItem.RxqualCount++;
            }
        }

        private static void setLteTdValue(HandoverCellItem cellItem, TestPoint tp)
        {
            int? rxlev = (int?)tp["lte_td_DM_PCCPCH_RSCP"];
            int? rxqual = (int?)tp["lte_td_DM_PCCPCH_C2I"];
            if (-141 <= rxlev && rxlev <= 25)
            {
                cellItem.Rxlev += (int)rxlev;
                cellItem.RxlevCount++;
            }
            if (-50 <= rxqual && rxqual <= 50)
            {
                cellItem.Rxqual += (int)rxqual;
                cellItem.RxqualCount++;
            }
        }

        private static void setGsmValue(HandoverCellItem cellItem, TestPoint tp)
        {
            object obj = tp["lte_gsm_DM_TA"];
            if (obj != null)
            {
                int ta = int.Parse(obj.ToString());
                cellItem.Ta += ta;
                cellItem.TaCount++;
            }
            obj = tp["lte_gsm_DM_RxLevSub"];
            if (obj != null)
            {
                int rxlev = int.Parse(obj.ToString());
                cellItem.Rxlev += rxlev;
                cellItem.RxlevCount++;
            }
            obj = tp["lte_gsm_DM_RxQualSub"];
            if (obj != null)
            {
                int rxqual = int.Parse(obj.ToString());
                cellItem.Rxqual += rxqual;
                cellItem.RxqualCount++;
            }
        }

        /// <summary>
        /// 查找切换点的索引
        /// </summary>
        /// <param name="cellItemAfter">TestPoint 集合</param>
        /// <param name="events">Event  集合</param>
        /// <returns></returns>
        private static int getChangeTestpointIndex(List<TestPoint> tpList, Event e)
        {
            int index = -1;
            for (int i = 0; i < tpList.Count; i++)
            {
                if (tpList[i].SN > e.SN)
                {
                    index = i - 1;
                    break;
                }
                if (tpList[i].SN == e.SN)
                {
                    index = i;
                    break;
                }
            }

            return index;
        }

        /**
        /// <summary>
        /// 检测是否属于有效范围内的切换点
        /// 1、切换前后 3 秒
        /// 2、切换前后 LAC、CI比较
        /// </summary>
        /// <param name="tp">切换所在的点</param>
        /// <param name="e">切换事件</param>
        /// <returns>true or false</returns>
        private static bool checkTestPoint(TestPoint tp, Event e, HandoverCellType cellType)
        {
            bool isFlag = false;
            int? lac = tp.GetLAC();
            int? ci = tp.GetCI();
            if (lac == null || ci == null)
            {
                return isFlag;
            }

            int? eLac1 = (int?)e["LAC"];
            int? eCi1 = (int?)e["CI"];
            int? eLac2 = (int?)e["TargetLAC"];
            int? eCi2 = (int?)e["TargetCI"];

            long timeTestpoint = tp.Time * 1000L + tp.Millisecond;
            long tiemEvent = e.Time * 1000L + e.Millisecond;

            if (((cellType == HandoverCellType.BeforeHandover)
                    && ((timeTestpoint + 3 * 1000) >= tiemEvent))//比较时间范围(小于它的3秒内）
                && (lac == eLac1 && ci == eCi1))
            {
                isFlag = true;
            }
            else if (((cellType == HandoverCellType.AfterHandover)
                        && ((timeTestpoint - 3 * 1000) <= tiemEvent))) //比较时间范围(大于它的3秒内）
            //&& (lac == eLac2 && ci == eCi2)) //shielded by wj 切换后LAC,CI获取太慢，导致匹配不到，因此屏蔽
            {
                isFlag = true;
            }

            return isFlag;
        }
        */

        private static void getHandoverItems(HandoverFileDataManager dtfdmi,List<TestPoint> tpList)
        {
            if (dtfdmi.EventsList.Count <= 0)
            {
                return;
            }
            foreach (List<Event> eList in dtfdmi.EventsList)
            {
                if (eList.Count > 1)
                {
                    HandoverProblemItem item = new HandoverProblemItem();
                    item.Index = dtfdmi.HandoverItems.Count + 1;
                    dtfdmi.HandoverItems.Add(item);
                    for (int i = 0; i < eList.Count; i++)
                    {
                        Event e = eList[i];
                        HandoverItem hoItem = dtfdmi.HandoverEventDic[e];
                        hoItem.Index = i + 1;
                        item.handoverItemList.Add(hoItem);
                    }
                    fillHandoverItem(item, tpList);
                }
            }
        }

        private static void fillHandoverItem(HandoverProblemItem item,List<TestPoint> tpList)
        {
            List<Event> eList = new List<Event>();
            StringBuilder sb = new StringBuilder();
            foreach (HandoverItem cellItem in item.handoverItemList)
            {
                sb.Append(cellItem.Des + ";\r\n");
                if (!eList.Contains(cellItem.Event))
                {
                    eList.Add(cellItem.Event);
                }
            }
            item.Des += sb.ToString();
            item.Name = ReselectionManager_LTE.MakeNetworkSelectCellUpdateStr(eList);
            item.overPool = isOverPool(eList);
            item.eventList = eList;

            //获取采样点，用于打点
            GetSampleForLayer(eList[0], tpList);
        }

        public static void GetSampleForLayer(Event e,List<TestPoint> tpList)
        {

            if (ZTDIYQueryScanAnalysis_LTE.ScanTestPointList != null)
            {
                foreach (TestPoint tp in tpList)
                {
                    if (e.DateTime.AddSeconds(ZTDIYQueryReselectionTooMuchSetForm.ISecond)
                        > tp.DateTime && tp.DateTime > e.DateTime)
                    {
                        ZTDIYQueryScanAnalysis_LTE.ScanTestPointList.Add(tp);
                    }
                }
                
            }
        }
        /// <summary>
        /// 是否跨pool
        /// </summary>
        /// <param name="eList"></param>
        /// <returns></returns>
        private static bool isOverPool(List<Event> eList)
        {
            for (int i = 0; i < eList.Count; i++)
            {
                Event e = eList[i];

                Cell cellSrc = e.GetSrcCell() as Cell;
                Cell cellTar = e.GetTargetCell() as Cell;
                if (cellSrc != null && cellTar != null
                    && cellSrc.PoolID != -1 && cellTar.PoolID != -1
                    && cellSrc.PoolID != cellTar.PoolID)
                {
                    return true;
                }
            }
            return false;
        }
        #endregion

    }
}
