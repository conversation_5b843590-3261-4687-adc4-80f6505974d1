﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class BlackBlock_PointForm : MinCloseForm
    {
        MapForm mapForm;
        public BlackBlock_PointForm(MapForm mapForm)
            : base(mapForm == null ? null : mapForm.MainModel)
        {
            InitializeComponent();
            this.mapForm = mapForm;
        }

        public void FillData()
        {
            BindingSource source = new BindingSource();
            source.DataSource = MainModel.BlackBlockPointsDic.Values;
            gridControl.DataSource = source;
            gridControl.RefreshDataSource();
        }

        private void gridControl_DoubleClick(object sender, EventArgs e)
        {
            if (gridView.SelectedRowsCount > 0)
            {
                BlackBlock_Point bb = gridView.GetRow(gridView.GetSelectedRows()[0]) as BlackBlock_Point;
                MainModel.ClearDTData();
                mapForm.doFillEvents(bb.Events);
                mapForm.GoToView(bb.Longitude, bb.Latitude);
            }
        }

        BlackBlock_PointDetailForm detailForm = null;
        private void miDetail_Click(object sender, EventArgs e)
        {
            if (gridView.SelectedRowsCount > 0)
            {
                BlackBlock_Point bb = gridView.GetRow(gridView.GetSelectedRows()[0]) as BlackBlock_Point;
                if (detailForm == null || detailForm.IsDisposed)
                {
                    detailForm = new BlackBlock_PointDetailForm();
                }
                detailForm.FillData(bb);
                detailForm.ShowDialog();
            }
        }

        private void miExportToExcel_Click(object sender, EventArgs e)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.AddCellValue("问题点编号");
            row.AddCellValue("名称");
            row.AddCellValue("权重");
            row.AddCellValue("状态");
            row.AddCellValue("创建日期");
            row.AddCellValue("最后异常日期");
            row.AddCellValue("关闭日期");
            row.AddCellValue("验证正常次数");
            row.AddCellValue("ATU验证次数");
            row.AddCellValue("最后验证日期");
            row.AddCellValue("验证状态");
            row.AddCellValue("LAC");
            row.AddCellValue("CI");
            row.AddCellValue("经度");
            row.AddCellValue("纬度");
            row.AddCellValue("片区");
            row.AddCellValue("代维片区");
            row.AddCellValue("网格");
            row.AddCellValue("道路");
            row.AddCellValue("小区");
            row.AddCellValue("事件时间");
            row.AddCellValue("文件");
            row.AddCellValue("事件类型");
            row.AddCellValue("小区");
            row.AddCellValue("经度");
            row.AddCellValue("纬度");
            row.AddCellValue("LAC");
            row.AddCellValue("CI");
            row.AddCellValue("项目");
            row.AddCellValue("重复类型");
            row.AddCellValue("是否圆心");
            rows.Add(row);
            foreach (BlackBlock_Point bb in MainModel.BlackBlockPointsDic.Values)
            {
                row = new NPOIRow();
                row.AddCellValue(bb.ID);
                row.AddCellValue(bb.Name);
                row.AddCellValue(bb.Weight);
                row.AddCellValue(bb.StatusString);
                row.AddCellValue(bb.CreatedDateString);
                row.AddCellValue(bb.LastAbnormalDateString);
                row.AddCellValue(bb.ClosedDateString);
                row.AddCellValue(bb.GoodDaysCount);
                row.AddCellValue(bb.ATUValidateCount);
                row.AddCellValue(bb.LastValidateDateString);
                row.AddCellValue(bb.ValidateStatusString);
                row.AddCellValue(bb.LAC);
                row.AddCellValue(bb.CI);
                row.AddCellValue(bb.Longitude);
                row.AddCellValue(bb.Latitude);
                row.AddCellValue(bb.AreaNames);
                row.AddCellValue(bb.DWAreaNames);
                row.AddCellValue(bb.GridNames);
                row.AddCellValue(bb.RoadNames);
                row.AddCellValue(bb.CellNames);
                foreach (BlackBlock_Point_Event be in bb.Events)
                {
                    NPOIRow subRow = new NPOIRow();
                    subRow.AddCellValue(be.DateTimeString);
                    subRow.AddCellValue(be.FileName);
                    subRow.AddCellValue(be.EventDesc);
                    subRow.AddCellValue(be.CellName);
                    subRow.AddCellValue(be.Longitude);
                    subRow.AddCellValue(be.Latitude);
                    subRow.AddCellValue(be.LAC);
                    subRow.AddCellValue(be.CI);
                    subRow.AddCellValue(be.ProjectName);
                    subRow.AddCellValue(be.TypeString);
                    subRow.AddCellValue(be.CenterPointString);
                    row.AddSubRow(subRow);
                }
                rows.Add(row);
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }
    }
}
