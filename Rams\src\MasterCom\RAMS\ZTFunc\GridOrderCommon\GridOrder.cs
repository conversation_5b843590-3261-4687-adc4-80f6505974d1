﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.GridOrderCommon
{
    public class GridOrder
    {
        public int CityID { get; set; }

        public int SetTokenID { get; set; }

        public int SetID { get; set; }

        public int GridCount { get; set; }

        public string AreaNames { get; set; }

        public string RoadNames { get; set; }

        public List<OrderGridItem> Grids { get; set; }

        public void AddGrid(OrderGridItem grid)
        {
            if (this.Grids == null)
            {
                this.Grids = new List<OrderGridItem>();
            }
            grid.Order = this;
            Grids.Add(grid);
        }

        public void AddCell(OrderCellItem cell)
        {
            if (Grids==null)
            {
                return;
            }
            foreach (OrderGridItem item in Grids)
            {
                if (item.ItemID == cell.ItemID)
                {
                    item.AddCell(cell);
                    return;
                }
            }
            System.Windows.Forms.MessageBox.Show(cell.OrderKey.ToString());
        }

        public string Key
        {
            get
            {
                return string.Format("{0}-{1}-{2}", CityID, SetTokenID, SetID);
            }
        }

        public string CityName { get { return DistrictManager.GetInstance().getDistrictName(CityID); } }

        public string TokenName { get; set; }

        public int OrderStatus
        {
            get;
            set;
        }
        public string StatusDesc
        {
            get
            {
                if (OrderStatus == 1 || OrderStatus == 0)
                {
                    return "已创建";
                }
                else if (OrderStatus == 2)
                {
                    return "验证通过";
                }
                else if (OrderStatus == 3)
                {
                    return "验证失败";
                }
                return OrderStatus.ToString();
            }
        }

        internal void AddGridKPI(int itemID, string kpiName, double kpiValue)
        {
            if (Grids == null)
            {
                return;
            }
            foreach (OrderGridItem item in Grids)
            {
                if (item.ItemID == itemID)
                {
                    item.KPIDic[kpiName] = kpiValue;
                }
            }
        }

        internal void AddGridCellKPI(int itemID, int cellID, string kpiName, double kpiValue)
        {
            if (Grids == null)
            {
                return;
            }
            foreach (OrderGridItem item in Grids)
            {
                if (item.ItemID == itemID)
                {
                    setGridCellKPI(cellID, kpiName, kpiValue, item);
                    break;
                }
            }
        }

        private void setGridCellKPI(int cellID, string kpiName, double kpiValue, OrderGridItem item)
        {
            if (item.Cells != null)
            {
                foreach (OrderCellItem cell in item.Cells)
                {
                    if (cell.CellID == cellID)
                    {
                        cell.KPIDic[kpiName] = kpiValue;
                        break;
                    }
                }
            }
        }

        public string TaskID { get; set; }
    }
}
