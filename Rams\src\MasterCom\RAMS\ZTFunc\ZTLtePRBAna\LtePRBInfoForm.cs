﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraEditors;
using System.Windows.Forms;
using DevExpress.XtraCharts;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LtePRBInfoForm : MinCloseForm
    {
        List<PrbInfo> ListPrbInfos;
        public LtePRBInfoForm()
            : base()
        {
            InitializeComponent();
            miExportSimpleExcel.Click += MiExportSimpleExcel_Click;

        }

        public void FillData(List<PrbInfo> PrbInfoList)
        {
            ListPrbInfos = PrbInfoList;
            gridControl1.DataSource = ListPrbInfos;
            gridControl1.RefreshDataSource();
            updateSeries(PrbInfoList[PrbInfoList.Count-1]);
        }

        private void updateSeries(PrbInfo prbInfo)
        {
            Series ser = chartControl1.Series[0];
            ser.Points.Clear();
            foreach (PrbRangeSet prbSet in prbInfo.PrbSetList)
            {
                ser.Points.Add(new SeriesPoint(prbSet.PrbRange,prbSet.Per/100));
            }
        }
        private void MiExportSimpleExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(getNPOIRowsByGridView());
        }

        private List<NPOIRow> getNPOIRowsByGridView()
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            foreach (GridColumn col in this.gridView1.Columns)
            {
                row.AddCellValue(col.Caption);
            }
            foreach (GridColumn col in this.gridView2.Columns)
            {
                row.AddCellValue(col.Caption);
            }
            rows.Add(row);
            for (int i = 0; i < ListPrbInfos.Count; i++)
            {
                row = new NPOIRow();
                rows.Add(row);
                row.AddCellValue(ListPrbInfos[i].SN);
                row.AddCellValue(ListPrbInfos[i].FileName);
                row.AddCellValue(ListPrbInfos[i].Count);
                for (int j = 0; j < ListPrbInfos[i].PrbSetList.Count; j++)
                {
                    NPOIRow subRow = new NPOIRow();
                    row.AddSubRow(subRow);
                    subRow.AddCellValue(ListPrbInfos[i].PrbSetList[j].PrbRange);
                    subRow.AddCellValue(ListPrbInfos[i].PrbSetList[j].Per);
                    subRow.AddCellValue(ListPrbInfos[i].PrbSetList[j].Count);
                }
            }
            return rows;
        }

        private void gridView_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            if (gv.GetRow(gv.GetSelectedRows()[0]) is PrbInfo)
            {
                updateSeries(gv.GetRow(gv.GetSelectedRows()[0]) as PrbInfo);
            }
        }
    }
}
