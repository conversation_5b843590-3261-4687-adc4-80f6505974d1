using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func.MeshGridOf3D
{
    public partial class SettingForm : Form
    {
        public SettingForm(string sys,string xtarget,string ytarget)
        {
            this.Sys = sys;
            this.Xtarget = xtarget;
            this.Ytarget = ytarget;
            InitializeComponent();
        }
        
        public string Sys { get; set; }
        public string Xtarget { get; set; }
        public string Ytarget { get; set; }
        private void initComponent()
        {
            comboBoxSys.Text = this.Sys;
            comboBoxXTarget.Text = this.Xtarget;
            comboBoxYTarget.Text = this.Ytarget;
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
            this.Xtarget = comboBoxXTarget.Text;
            this.Ytarget = comboBoxYTarget.Text;
        }

        private void SettingForm_Load(object sender, EventArgs e)
        {
            init();
        }

        private void init()
        {
            loadSystem();
            loadTargetXY();
            initComponent();
        }

        private void loadSystem()
        {
            foreach (DTDisplayParameterSystem system in DTDisplayParameterManager.GetInstance().Systems)
            {
                comboBoxSys.Items.Add(system.Name);
            }
            if (comboBoxSys.Items.Count > 0)
            {
                comboBoxSys.SelectedIndex = 0;
            }
        }

        private void loadTargetXY()
        {
            comboBoxXTarget.Items.Clear();
            comboBoxYTarget.Items.Clear();
            string systemName = (string)comboBoxSys.SelectedItem;
            foreach (DTDisplayParameterInfo paramInfo in DTDisplayParameterManager.GetInstance()[systemName].DisplayParamInfos)
            {
                if ((paramInfo.Type & (int)DTDisplayParameterInfoType.Range) != 0)
                {
                    comboBoxXTarget.Items.Add(paramInfo.Name);
                    comboBoxYTarget.Items.Add(paramInfo.Name);
                }
            }
           // comboBoxXTarget.SelectedIndex = 0;
           // comboBoxYTarget.SelectedIndex = 0;
        }

        private void comboBoxSys_SelectedIndexChanged(object sender, EventArgs e)
        {
            this.Sys = comboBoxSys.Text;
            loadTargetXY();
            comboBoxXTarget.SelectedIndex = 0;
            comboBoxYTarget.SelectedIndex = 0;
        }

        private void comboBoxXTarget_SelectedIndexChanged(object sender, EventArgs e)
        {
           // this.Xtarget = comboBoxXTarget.Text;
        }

        private void comboBoxYTarget_SelectedIndexChanged(object sender, EventArgs e)
        {
           // this.Ytarget = comboBoxYTarget.Text;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.Dispose();
        }
    }
}