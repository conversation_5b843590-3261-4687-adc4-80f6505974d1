﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.Util.UiEx;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    /*以文件维度呈现每通话的切换情况
     * 1、按区域回放文件事件，若起呼事件在所选区域内，则记录下该次通话的所有事件
     * 2、回放文件采样点，记录下一次通话内的采样点
    */
    public class ZTEveryCallHandoverAnaByRegion_TD:QueryBase
    {
        public ZTEveryCallHandoverAnaByRegion_TD(MainModel mm)
            : base(mm)
        { }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }
        public override string Description
        {
            get
            {
                return "区域回放文件，以测试文件的每个通话维度分析切换情况";
            }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 13028, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        public override string Name
        {
            get { return "按区域分析文件每通话的切换情况"; }
        }

        public override string IconName
        {
            get { return ""; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
            if (!isValidCondition())
            {
                return;
            }
            MainModel.ClearDTData();
            fileCallList = new List<FileEveryCallInfoOfTD>();
            DIYQueryFileInfoByRegion queryFileInfo = new DIYQueryFileInfoByRegion(MainModel);
            queryFileInfo.IsShowFileInfoForm = false;
            queryFileInfo.SetQueryCondition(condition);
            queryFileInfo.Query();

            WaitTextBox.Show(doQueryAndStat);

            if (fileCallList.Count > 0)
            {
                EveryCallHandoverInfoForm frm = MainModel.GetObjectFromBlackboard(typeof(EveryCallHandoverInfoForm).FullName) as EveryCallHandoverInfoForm;
                if (frm==null||frm.IsDisposed)
                {
                    frm = new EveryCallHandoverInfoForm(MainModel);
                }
                frm.FillData(fileCallList);
                if (!frm.Visible)
                {
                    frm.Show(MainModel.MainForm);
                }
                frm.BringToFront();
            }
            else
            {
                DevExpress.XtraEditors.XtraMessageBox.Show("没有符合条件的数据！");
            }
            MainModel.ClearDTData();
            MainModel.FileInfos.Clear();
            fileCallList = new List<FileEveryCallInfoOfTD>();
        }
        List<FileEveryCallInfoOfTD> fileCallList = new List<FileEveryCallInfoOfTD>();

        private void doQueryAndStat()
        {
            try
            {
                foreach (FileInfo fi in MainModel.FileInfos)
                {
                    Condition.FileInfos.Clear();
                    Condition.FileInfos.Add(fi);
                    List<string> columns = new List<string>();
                    columns.Add("TD_PCCPCH_RSCP");
                    columns.Add(MainModel.TD_SCell_LAC);
                    columns.Add(MainModel.TD_SCell_CI);
                    columns.Add(MainModel.TD_GSM_SCell_LAC);
                    columns.Add(MainModel.TD_GSM_SCell_CI);
                    columns.Add(MainModel.TD_SCell_UARFCN);
                    columns.Add(MainModel.TD_SCell_CPI);
                    DIYReplayFileWithNoWaitBox replay = new DIYReplayFileWithNoWaitBox(MainModel);
                    replay.Columns = columns;
                    replay.FilterEventByRegion = false;
                    replay.SetQueryCondition(Condition);
                    replay.Query();
                    foreach (DTFileDataManager fMng in MainModel.DTDataManager.FileDataManagers)
                    {
                        List<OneCallInfoOfTD> calls = new List<OneCallInfoOfTD>();
                        bool callStarted = false;
                        OneCallInfoOfTD oneCall = null;
                        dealEvts(fMng, calls, ref callStarted, ref oneCall);
                        int callCount = calls.Count;
                        if (callCount > 0)
                        {
                            FileEveryCallInfoOfTD fileCalls = new FileEveryCallInfoOfTD();
                            fileCalls.SN = fileCallList.Count + 1;
                            fileCalls.FileName = fi.Name;
                            fileCalls.AllCalls = calls;
                            fileCallList.Add(fileCalls);
                            int curCallIdx = 0;
                            OneCallInfoOfTD curCall = calls[curCallIdx];
                            WaitTextBox.Text = "正在统计[" + fi.Name + "]...";
                            dealTPs(fMng, calls, callCount, ref curCallIdx, ref curCall);
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
            finally
            {
                WaitTextBox.Close();
            }
        }

        private void dealEvts(DTFileDataManager fMng, List<OneCallInfoOfTD> calls, ref bool callStarted, ref OneCallInfoOfTD oneCall)
        {
            foreach (Event evt in fMng.Events)
            {
                if (evt.ID == 101 || evt.ID == 107 || evt.ID == 113 || evt.ID == 119 || evt.ID == 155 || evt.ID == 160)//Call Attempt
                {
                    if (condition.Geometorys.GeoOp.Contains(evt.Longitude, evt.Latitude))
                    {
                        oneCall = new OneCallInfoOfTD(evt);
                        callStarted = true;
                    }
                    else
                    {
                        callStarted = false;
                    }
                }
                else if (callStarted)
                {
                    oneCall.AddEvent(evt);
                    if (evt.ID == 104 || evt.ID == 110 || evt.ID == 116 || evt.ID == 122 || evt.ID == 158 || evt.ID == 163)//Disconnect
                    {
                        callStarted = false;
                        calls.Add(oneCall);
                        oneCall = null;
                    }
                }
            }
        }

        private void dealTPs(DTFileDataManager fMng, List<OneCallInfoOfTD> calls, int callCount, ref int curCallIdx, ref OneCallInfoOfTD curCall)
        {
            foreach (TestPoint tp in fMng.TestPoints)
            {
                if (tp.Time >= curCall.AttempTime && tp.Time <= curCall.DisconnectTime)
                {
                    curCall.AddTestPoint(tp);
                }
                else if (tp.Time > curCall.DisconnectTime)
                {
                    if (curCallIdx < callCount - 1)
                    {
                        curCallIdx++;
                        curCall = calls[curCallIdx];
                    }
                    else
                    {
                        break;
                    }
                }
            }
        }
    }

    public class FileEveryCallInfoOfTD
    {
        public string FileName { get; set; }
        public string Name
        {
            get { return FileName; }
        }
        public int SN { get; set; }
        public List<OneCallInfoOfTD> AllCalls { get; set; }
        private List<OneCallInfoOfTD> matchCalls = new List<OneCallInfoOfTD>();
        public List<OneCallInfoOfTD> MatchCalls
        {
            get { return matchCalls; }
        }
        public void FilterCall(int handoverCount, int cellOccupation)
        {
            matchCalls = new List<OneCallInfoOfTD>();
            foreach (OneCallInfoOfTD call in AllCalls)
            {
                if (call.HandoverSuccessCount >= handoverCount)
                {
                    call.FilterCellByOccupation(cellOccupation);
                    if (call.MatchCellInfo.Count>0)
                    {
                        matchCalls.Add(call);
                    }
                }
            }
        }
        public int CallCount
        {
            get
            {
                if (AllCalls != null)
                {
                    return AllCalls.Count;
                }
                return 0;
            }
        }
    }

    public class OneCallInfoOfTD
    {
        private readonly int beginTime = 0;
        public OneCallInfoOfTD(Event callAttemptEvent)
        {
            Events = new List<Event>();
            Events.Add(callAttemptEvent);
            beginTime = AttempTime = callAttemptEvent.Time;
            lastHandoverSuccessTime = callAttemptEvent.DateTime;
        }
        public DateTime BeginTime {
            get { return JavaDate.GetDateTimeFromMilliseconds(beginTime * 1000L); }
        }
        public DateTime EndTime
        {
            get { return JavaDate.GetDateTimeFromMilliseconds(DisconnectTime * 1000L); }
        }
        private DateTime lastHandoverSuccessTime;
        public void AddEvent(Event evtInThisCall)
        {
            Object cell = evtInThisCall.GetSrcCell();
            if (cell == null)
            {
                cell = evtInThisCall["LAC"].ToString() + "_" + evtInThisCall["CI"].ToString();
            }
            if (evtInThisCall.ID == 142 || evtInThisCall.ID == 145 || evtInThisCall.ID == 148 || evtInThisCall.ID == 151)
            {
                lastHandoverSuccessTime = evtInThisCall.DateTime;
                HandoverSuccessCount++;
            }
            else if (evtInThisCall.ID == 104 || evtInThisCall.ID == 110 || evtInThisCall.ID == 116 || evtInThisCall.ID == 122 || evtInThisCall.ID == 158 || evtInThisCall.ID == 163)//Disconnect
            {
                DisconnectTime = evtInThisCall.Time;
            }
            CellInfoOfOneCallTD cellInfo = new CellInfoOfOneCallTD(cell);
            cellInfo.BeginTime = lastHandoverSuccessTime;
            cellInfo.EndTime = evtInThisCall.DateTime;
            AllCellInfoInThisCall.Add(cellInfo);

            Events.Add(evtInThisCall);
        }
        public List<TestPoint> TestPoints { get; set; } = new List<TestPoint>();
        public void AddTestPoint(TestPoint testPoint)
        {
            float? rscp = (float?)testPoint["TD_PCCPCH_RSCP"];
            int? rxlev = (int?)testPoint["TD_GSM_RxlevSub"];
            if (rscp == null && rxlev == null)
            {
                return;
            }
            if ((rscp >= -120 && rscp <= -10) || (rxlev >= -120 && rxlev <= -10))
            {
                Object cell = null;
                int? lac = null;
                int? ci = null;
                cell = getCellInfo(testPoint, rscp, cell, out lac, out ci);
                if (cell == null && lac != null && ci != null)
                {
                    cell = lac.ToString() + "_" + ci.ToString();
                }
                bool isValid = judgeValid(testPoint, cell, lac, ci);
                if (isValid)
                {
                    TestPoints.Add(testPoint);
                }
            }
        }

        private static object getCellInfo(TestPoint testPoint, float? rscp, object cell, out int? lac, out int? ci)
        {
            if (rscp >= -120 && rscp <= -10)
            {
                lac = (int?)testPoint[MainModel.TD_SCell_LAC];
                ci = (int?)testPoint[MainModel.TD_SCell_CI];
                if (lac != null && ci != null && lac != -255 && ci != -255)
                {
                    cell = testPoint.GetMainCell_TD_TDCell();
                }
            }
            else
            {
                lac = (int?)testPoint[MainModel.TD_GSM_SCell_LAC];
                ci = (int?)testPoint[MainModel.TD_GSM_SCell_CI];
                if (lac != null && ci != null && lac != -255 && ci != -255)
                {
                    cell = testPoint.GetMainCell_TD_GSMCell();
                }
            }

            return cell;
        }

        private bool judgeValid(TestPoint testPoint, object cell, int? lac, int? ci)
        {
            bool isValid = false;
            for (int i = 0; i < AllCellInfoInThisCall.Count; i++)
            {
                CellInfoOfOneCallTD cellInfo = AllCellInfoInThisCall[i];
                if (cellInfo.BeginTime <= testPoint.DateTime && testPoint.DateTime <= cellInfo.EndTime)
                {
                    if (cellInfo.Cell == cell)
                    {
                        isValid = cellInfo.AddTestPoint(testPoint);
                    }
                    else if (cellInfo.Cell is string)
                    {
                        string[] strArr = cellInfo.Cell.ToString().Split('_');
                        int lacEvt = int.Parse(strArr[0]);
                        int ciEvt = int.Parse(strArr[1]);
                        if (lacEvt == lac || ciEvt == ci)
                        {
                            cellInfo.Cell = cell;
                            isValid = cellInfo.AddTestPoint(testPoint);
                        }
                    }
                    break;
                }
            }

            return isValid;
        }

        public int AttempTime { get; set; }
        public int DisconnectTime { get; set; } = -1;
        public int HandoverSuccessCount { get; set; } = 0;
        public List<Event> Events { get; set; }
        public List<CellInfoOfOneCallTD> AllCellInfoInThisCall { get; set; } = new List<CellInfoOfOneCallTD>();
        private List<CellInfoOfOneCallTD> matchCellInfo = new List<CellInfoOfOneCallTD>();
        public List<CellInfoOfOneCallTD> MatchCellInfo
        {
            get { return matchCellInfo; }
        }
        public void FilterCellByOccupation(int second)
        {
            matchCellInfo = new List<CellInfoOfOneCallTD>();
            foreach (CellInfoOfOneCallTD cellInfo in AllCellInfoInThisCall)
            {
                if (cellInfo.OccupaSec <= second)
                {
                    matchCellInfo.Add(cellInfo);
                }
            }
        }
        public string Desc
        {
            get
            {
                bool firstCell=true;
                StringBuilder sb = new StringBuilder();
                sb.Append("本次通话共发生" + (AllCellInfoInThisCall.Count - 1).ToString() + "次切换：");
                foreach (CellInfoOfOneCallTD cellInfo in AllCellInfoInThisCall)
                {
                    if (firstCell)
                    {
                        sb.Append(cellInfo.Name + "(" + cellInfo.OccupaSec + "s)");
                        firstCell = false;
                    }
                    else
                    {
                        sb.Append("->" + cellInfo.Name + "(" + cellInfo.OccupaSec + "s)");
                    }
                }
                return sb.ToString();
            }
        }
    }

    public class CellInfoOfOneCallTD
    {
        public CellInfoOfOneCallTD(Object cell)
        {
            this.Cell = cell;
        }
        public Object Cell { get; set; }
        public string Name
        {
            get
            {
                if (Cell == null)
                {
                    return string.Empty;
                }
                string ret = Cell.ToString();
                if (Cell is TDCell)
                {
                    ret = (Cell as TDCell).Name;
                }
                else if (Cell is Cell)
                {
                    ret = (Cell as Cell).Name;
                }
                return ret;
            }
        }
        public int LAC
        {
            get
            {
                if (Cell is TDCell)
                {
                    return (Cell as TDCell).LAC;
                }
                else if (Cell is Cell)
                {
                    return (Cell as Cell).LAC;
                }
                else
                {
                    String[] strArr = Cell.ToString().Split('_');
                    return int.Parse(strArr[0]);
                }
            }
        }
        public int CI
        {
            get
            {
                if (Cell is TDCell)
                {
                    return (Cell as TDCell).CI;
                }
                else if (Cell is Cell)
                {
                    return (Cell as Cell).CI;
                }
                else
                {
                    String[] strArr = Cell.ToString().Split('_');
                    return int.Parse(strArr[1]);
                }
            }
        }
        public DateTime BeginTime { get; set; }
        public DateTime EndTime { get; set; }
        public int OccupaSec
        {
            get { return (int)(EndTime - BeginTime).TotalSeconds; }
        }
        public double Speed
        {
            get { return Math.Round(curMilage / OccupaSec, 2); } 
        }
        private int maxRxLev = int.MinValue;
        public int MaxRxLev
        {
            get { return maxRxLev; }
        }
        private int minRxLev = int.MaxValue;
        public int MinRxLev
        {
            get { return minRxLev; }
        }
        private double rxlevSum = 0;
        public bool AddTestPoint(TestPoint testPoint)
        {
            bool ret = false;
            float? rscp = (float?)testPoint["TD_PCCPCH_RSCP"];
            int? rxlev = (int?)testPoint["TD_GSM_RxlevSub"];
            if (rscp != null && rscp <= -10 && rscp >= -120)//TD
            {
                rxlevSum += (float)rscp;
                maxRxLev = Math.Max((int)rscp, maxRxLev);
                minRxLev = Math.Min((int)rscp, minRxLev);
                int count = TestPoints.Count;
                if (count > 0)
                {
                    curMilage += MathFuncs.GetDistance(TestPoints[count - 1].Longitude, TestPoints[count - 1].Latitude, testPoint.Longitude, testPoint.Latitude);
                }
                double dis = 0;
                if (Cell is TDCell)
                {
                    TDCell tdCell = Cell as TDCell;
                    dis = MathFuncs.GetDistance(tdCell.Longitude, tdCell.Latitude, testPoint.Longitude, testPoint.Latitude);
                }
                else if (Cell is Cell)
                {
                    Cell c = Cell as Cell;
                    dis = MathFuncs.GetDistance(c.Longitude, c.Latitude, testPoint.Longitude, testPoint.Latitude);
                }
                minDistance = Math.Min(minDistance, dis);
                maxDistance = Math.Max(maxDistance, dis);
                sumDistance += dis;
                TestPoints.Add(testPoint);
                ret = true;
            }
            else if (rxlev != null && rxlev <= -10 && rxlev >= -120)//GSM
            {
                rxlevSum += (int)rxlev;
                maxRxLev = Math.Max((int)rxlev, maxRxLev);
                minRxLev = Math.Min((int)rxlev, minRxLev);
                int count = TestPoints.Count;
                if (count > 0)
                {
                    curMilage += MathFuncs.GetDistance(TestPoints[count - 1].Longitude, TestPoints[count - 1].Latitude, testPoint.Longitude, testPoint.Latitude);
                }
                double dis = 0;
                if (Cell is TDCell)
                {
                    TDCell tdCell = Cell as TDCell;
                    dis = MathFuncs.GetDistance(tdCell.Longitude, tdCell.Latitude, testPoint.Longitude, testPoint.Latitude);
                }
                else if (Cell is Cell)
                {
                    Cell c = Cell as Cell;
                    dis = MathFuncs.GetDistance(c.Longitude, c.Latitude, testPoint.Longitude, testPoint.Latitude);
                }
                minDistance = Math.Min(minDistance, dis);
                maxDistance = Math.Max(maxDistance, dis);
                sumDistance += dis;
                TestPoints.Add(testPoint);
                ret = true;
            }
            return ret;
        }
        public double RxLevAvg
        {
            get
            {
                double ret = double.MaxValue;
                if (TestPoints.Count != 0)
                {
                    ret = Math.Round(rxlevSum / TestPoints.Count, 2);
                }
                return ret;
            }
        }
        public List<TestPoint> TestPoints { get; set; } = new List<TestPoint>();
        private double maxDistance = 0;
        public double MaxDistance
        {
            get { return Math.Round(maxDistance,2); }
        }
        private double minDistance = double.MaxValue;
        public double MinDistance
        {
            get { return Math.Round(minDistance,2); }
        }
        private double sumDistance = 0;
        public double AvgDistance
        {
            get
            {
                double ret = 0;
                if (TestPoints.Count > 0)
                {
                    ret = Math.Round(sumDistance / TestPoints.Count, 2);
                }
                return ret;
            }
        }
        private double curMilage = 0;
        public double Milage
        {
            get
            {
                double ret = 0;
                if (TestPoints.Count > 1)
                {
                    for (int i = 1; i < TestPoints.Count; i++)
                    {
                        ret += MathFuncs.GetDistance(TestPoints[i].Longitude, TestPoints[i].Latitude, TestPoints[i - 1].Longitude, TestPoints[i - 1].Latitude);
                    }
                }
                ret = Math.Round(ret, 2);
                return ret;
            }
        }
    }
}
