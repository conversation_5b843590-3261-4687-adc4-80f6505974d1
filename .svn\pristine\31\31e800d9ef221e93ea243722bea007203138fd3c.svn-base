﻿namespace MasterCom.RAMS.Func
{
    partial class GISMultiParamColorForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.cbxSystem = new System.Windows.Forms.ComboBox();
            this.cbxParam = new System.Windows.Forms.ComboBox();
            this.cbxIndex = new System.Windows.Forms.ComboBox();
            this.numMinValue = new System.Windows.Forms.NumericUpDown();
            this.numMaxValue = new System.Windows.Forms.NumericUpDown();
            this.label5 = new System.Windows.Forms.Label();
            this.treeView = new System.Windows.Forms.TreeView();
            this.btnAddCond = new System.Windows.Forms.Button();
            this.btnSaveParam = new System.Windows.Forms.Button();
            this.btnDelete = new System.Windows.Forms.Button();
            this.btnUp = new System.Windows.Forms.Button();
            this.btnDown = new System.Windows.Forms.Button();
            this.btnColor = new System.Windows.Forms.Button();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.btnAddParam = new System.Windows.Forms.Button();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.btnAddDefault = new System.Windows.Forms.Button();
            this.btnAddInvalid = new System.Windows.Forms.Button();
            ((System.ComponentModel.ISupportInitialize)(this.numMinValue)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxValue)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.SuspendLayout();
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(262, 449);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 0;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(181, 449);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 1;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(27, 106);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(53, 12);
            this.label4.TabIndex = 7;
            this.label4.Text = "参数范围";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(27, 78);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(53, 12);
            this.label3.TabIndex = 6;
            this.label3.Text = "参数序号";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(27, 51);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(53, 12);
            this.label2.TabIndex = 5;
            this.label2.Text = "参数名称";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(27, 25);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(53, 12);
            this.label1.TabIndex = 4;
            this.label1.Text = "参数类别";
            // 
            // cbxSystem
            // 
            this.cbxSystem.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxSystem.FormattingEnabled = true;
            this.cbxSystem.Location = new System.Drawing.Point(94, 20);
            this.cbxSystem.Name = "cbxSystem";
            this.cbxSystem.Size = new System.Drawing.Size(202, 20);
            this.cbxSystem.TabIndex = 8;
            // 
            // cbxParam
            // 
            this.cbxParam.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxParam.FormattingEnabled = true;
            this.cbxParam.Location = new System.Drawing.Point(94, 46);
            this.cbxParam.Name = "cbxParam";
            this.cbxParam.Size = new System.Drawing.Size(202, 20);
            this.cbxParam.TabIndex = 9;
            // 
            // cbxIndex
            // 
            this.cbxIndex.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxIndex.FormattingEnabled = true;
            this.cbxIndex.Location = new System.Drawing.Point(94, 72);
            this.cbxIndex.Name = "cbxIndex";
            this.cbxIndex.Size = new System.Drawing.Size(202, 20);
            this.cbxIndex.TabIndex = 10;
            // 
            // numMinValue
            // 
            this.numMinValue.Cursor = System.Windows.Forms.Cursors.Default;
            this.numMinValue.DecimalPlaces = 1;
            this.numMinValue.Location = new System.Drawing.Point(94, 102);
            this.numMinValue.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.numMinValue.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.numMinValue.Name = "numMinValue";
            this.numMinValue.Size = new System.Drawing.Size(67, 21);
            this.numMinValue.TabIndex = 11;
            this.numMinValue.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // numMaxValue
            // 
            this.numMaxValue.DecimalPlaces = 1;
            this.numMaxValue.Location = new System.Drawing.Point(229, 104);
            this.numMaxValue.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.numMaxValue.Minimum = new decimal(new int[] {
            1000000,
            0,
            0,
            -2147483648});
            this.numMaxValue.Name = "numMaxValue";
            this.numMaxValue.Size = new System.Drawing.Size(67, 21);
            this.numMaxValue.TabIndex = 12;
            this.numMaxValue.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(174, 106);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(41, 12);
            this.label5.TabIndex = 13;
            this.label5.Text = "≤ X <";
            // 
            // treeView
            // 
            this.treeView.Location = new System.Drawing.Point(112, 182);
            this.treeView.Name = "treeView";
            this.treeView.Size = new System.Drawing.Size(225, 252);
            this.treeView.TabIndex = 15;
            // 
            // btnAddCond
            // 
            this.btnAddCond.Location = new System.Drawing.Point(8, 18);
            this.btnAddCond.Name = "btnAddCond";
            this.btnAddCond.Size = new System.Drawing.Size(75, 23);
            this.btnAddCond.TabIndex = 16;
            this.btnAddCond.Text = "参数条件";
            this.btnAddCond.UseVisualStyleBackColor = true;
            // 
            // btnSaveParam
            // 
            this.btnSaveParam.Location = new System.Drawing.Point(8, 119);
            this.btnSaveParam.Name = "btnSaveParam";
            this.btnSaveParam.Size = new System.Drawing.Size(75, 23);
            this.btnSaveParam.TabIndex = 17;
            this.btnSaveParam.Text = "更新";
            this.btnSaveParam.UseVisualStyleBackColor = true;
            // 
            // btnDelete
            // 
            this.btnDelete.Location = new System.Drawing.Point(8, 148);
            this.btnDelete.Name = "btnDelete";
            this.btnDelete.Size = new System.Drawing.Size(75, 23);
            this.btnDelete.TabIndex = 18;
            this.btnDelete.Text = "删除";
            this.btnDelete.UseVisualStyleBackColor = true;
            // 
            // btnUp
            // 
            this.btnUp.Location = new System.Drawing.Point(8, 194);
            this.btnUp.Name = "btnUp";
            this.btnUp.Size = new System.Drawing.Size(75, 23);
            this.btnUp.TabIndex = 19;
            this.btnUp.Text = "向上";
            this.btnUp.UseVisualStyleBackColor = true;
            // 
            // btnDown
            // 
            this.btnDown.Location = new System.Drawing.Point(8, 223);
            this.btnDown.Name = "btnDown";
            this.btnDown.Size = new System.Drawing.Size(75, 23);
            this.btnDown.TabIndex = 20;
            this.btnDown.Text = "向下";
            this.btnDown.UseVisualStyleBackColor = true;
            // 
            // btnColor
            // 
            this.btnColor.Location = new System.Drawing.Point(22, 449);
            this.btnColor.Name = "btnColor";
            this.btnColor.Size = new System.Drawing.Size(75, 23);
            this.btnColor.TabIndex = 21;
            this.btnColor.Text = "颜色设置";
            this.btnColor.UseVisualStyleBackColor = true;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.btnAddParam);
            this.groupBox1.Controls.Add(this.cbxSystem);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Controls.Add(this.cbxParam);
            this.groupBox1.Controls.Add(this.cbxIndex);
            this.groupBox1.Controls.Add(this.numMinValue);
            this.groupBox1.Controls.Add(this.label5);
            this.groupBox1.Controls.Add(this.numMaxValue);
            this.groupBox1.Location = new System.Drawing.Point(14, 5);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(323, 163);
            this.groupBox1.TabIndex = 22;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "参数选择";
            // 
            // btnAddParam
            // 
            this.btnAddParam.Location = new System.Drawing.Point(221, 133);
            this.btnAddParam.Name = "btnAddParam";
            this.btnAddParam.Size = new System.Drawing.Size(75, 23);
            this.btnAddParam.TabIndex = 18;
            this.btnAddParam.Text = "添加参数";
            this.btnAddParam.UseVisualStyleBackColor = true;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.btnAddDefault);
            this.groupBox2.Controls.Add(this.btnAddInvalid);
            this.groupBox2.Controls.Add(this.btnUp);
            this.groupBox2.Controls.Add(this.btnSaveParam);
            this.groupBox2.Controls.Add(this.btnDelete);
            this.groupBox2.Controls.Add(this.btnDown);
            this.groupBox2.Controls.Add(this.btnAddCond);
            this.groupBox2.Location = new System.Drawing.Point(14, 182);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(92, 252);
            this.groupBox2.TabIndex = 23;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "着色操作";
            // 
            // btnAddDefault
            // 
            this.btnAddDefault.Location = new System.Drawing.Point(8, 47);
            this.btnAddDefault.Name = "btnAddDefault";
            this.btnAddDefault.Size = new System.Drawing.Size(75, 23);
            this.btnAddDefault.TabIndex = 14;
            this.btnAddDefault.Text = "缺省条件";
            this.btnAddDefault.UseVisualStyleBackColor = true;
            // 
            // btnAddInvalid
            // 
            this.btnAddInvalid.Location = new System.Drawing.Point(8, 76);
            this.btnAddInvalid.Name = "btnAddInvalid";
            this.btnAddInvalid.Size = new System.Drawing.Size(75, 23);
            this.btnAddInvalid.TabIndex = 25;
            this.btnAddInvalid.Text = "无效条件";
            this.btnAddInvalid.UseVisualStyleBackColor = true;
            // 
            // GISMultiParamColorForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(349, 484);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.treeView);
            this.Controls.Add(this.btnColor);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.btnCancel);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "GISMultiParamColorForm";
            this.Text = "GIS多指标渲染设置";
            ((System.ComponentModel.ISupportInitialize)(this.numMinValue)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxValue)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.ComboBox cbxSystem;
        private System.Windows.Forms.ComboBox cbxParam;
        private System.Windows.Forms.ComboBox cbxIndex;
        private System.Windows.Forms.NumericUpDown numMinValue;
        private System.Windows.Forms.NumericUpDown numMaxValue;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TreeView treeView;
        private System.Windows.Forms.Button btnAddCond;
        private System.Windows.Forms.Button btnSaveParam;
        private System.Windows.Forms.Button btnDelete;
        private System.Windows.Forms.Button btnUp;
        private System.Windows.Forms.Button btnDown;
        private System.Windows.Forms.Button btnColor;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Button btnAddDefault;
        private System.Windows.Forms.Button btnAddParam;
        private System.Windows.Forms.Button btnAddInvalid;
    }
}