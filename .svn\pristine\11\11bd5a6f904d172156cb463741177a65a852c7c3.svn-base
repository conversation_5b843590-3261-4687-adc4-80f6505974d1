﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class MultiStationAutoAcceptManager
    {
        public CellAcceptFileInfo AcceptFileInfo { get; set; }

        protected virtual List<AcpAutoKpiBase> getAcceptAnaList(LTEBTSType btsType)
        {
            List<AcpAutoKpiBase> acceptAnaList = null;
            if (btsType == LTEBTSType.Outdoor)
            {
                acceptAnaList = new List<AcpAutoKpiBase>()
                {
                    new AcpAutoFtpDownload(),
                    new AcpAutoFtpUpload(),
                    new AcpAutoRrcRate(),
                    new AcpAutoAccRate(),
                    new AcpAutoErabRate(),
                    new AcpAuto34ReselectRate(),
                    new AcpAuto24ReselectRate(),
                    new AcpAutoCsfbRate()
                };

                if (MultiStationExportReportAna.GetInstance().IsStarted)//导出站点报告时，需截取覆盖图
                {
                    acceptAnaList.Add(new AcpAutoCoverPicture());
                }
            }
            else
            {
                acceptAnaList = new List<AcpAutoKpiBase>() 
                {
                    //new AcpAutoIndoorFtpDownload(),
                    //new AcpAutoIndoorFtpUpload(),
                    new AcpAutoIndoorRrcRate(),
                    new AcpAutoIndoorAccRate(),
                    new AcpAutoIndoorErabRate(),
                    new AcpAutoIndoorCsfbRate(),
                    new AcpAutoIndoorLeakOut_LockEarfcn(),
                    //new AcpAutoIndoorLeakOut_Scan(),
                    new AcpAutoIndoorLevelTestKpi(),
                    new AcpAutoIndoorInnerHandover(),
                    new AcpAutoIndoorCoverFloor()
                };
            }
            return acceptAnaList;
        }

        public void AnalyzeFile(MasterCom.RAMS.Model.FileInfo fileInfo, DTFileDataManager fileManager)
        {
            try
            {
                fileInfo.Momt = fileManager.MoMtFlag;
                if (fileManager.DTDatas.Count > 0)
                {
                    fileInfo = fileManager.GetFileInfo();//网络体检查到的FileInfo信息不全，回放文件得到的FileInfo较全
                }

                if (fileInfo.Name.Contains("系统内切换"))
                {
                    anaWithHandoverFile(fileInfo, fileManager);
                }
                else if (fileInfo.Name.Contains("扫频"))//文件名包含“扫频”的文件，并非扫频测试，而是非锁频路测文件
                {
                    anaWithLeakOut_ScanFile(fileInfo, fileManager);
                }
                else
                {
                    anaWithOtherFile(fileInfo, fileManager);
                }
            }
            catch(Exception ex)
            {
                reportInfo(ex);
            }
        }

        protected virtual void anaWithHandoverFile(MasterCom.RAMS.Model.FileInfo fileInfo, DTFileDataManager fileManager)
        {
            LTEBTS bts = getFileTestBts(fileManager);
            if (bts != null && bts.Type == LTEBTSType.Indoor)//新疆室分系统内切测试文件为一个小区一个文件
            {
                anaWithOtherFile(fileInfo, fileManager);
            }
            else//宏站系统内切换特殊处理，一个站一个系统内切换文件
            {
                if (bts != null && bts.Cells.Count > 0)
                {
                    AcpAutoInnerHandover handOverAna = new AcpAutoInnerHandover();
                    doStatWithData(handOverAna, fileInfo, fileManager, bts.Cells[0]);
                }
                else
                {
                    reportInfo(string.Format("文件{0}未找到目标基站", fileInfo.Name));
                }
            }
        }

        #region 室分扫频外泄文件特殊处理（该类文件采样点主服一般是室外小区，邻服才有室分小区）
        protected virtual void anaWithLeakOut_ScanFile(MasterCom.RAMS.Model.FileInfo fileInfo, DTFileDataManager fileManager)
        {
            AcpAutoIndoorLeakOut_Scan acp = new AcpAutoIndoorLeakOut_Scan();
            LTECell targetCell = GetLeakOutScanTestCell(fileManager);
            if (targetCell == null)
            {
                reportInfo(string.Format("文件{0}未找到目标小区", fileInfo.Name));
                return;
            }

            doStatWithData(acp, fileInfo, fileManager, targetCell);
        }
        public LTECell GetLeakOutScanTestCell(DTFileDataManager fileManager)
        {
            //根据文件名中的频点PCI获取宏目标室分小区
            string[] strArray = fileManager.FileName.Split('_');
            if (strArray.Length == 6)
            {
                string cellName = strArray[2];
                int? pci = getValidData(strArray[3], "PCI");
                int? earfcn = getValidData(strArray[4], "频点");

                DateTime time = DateTime.Now;
                if (fileManager.TestPoints.Count > 0)
                {
                    time = fileManager.TestPoints[0].DateTime;
                }
                List<LTECell> cells = CellManager.GetInstance().GetLTECellListByEARFCNPCI(time, earfcn, pci);
                foreach (LTECell cell in cells)
                {
                    if (cell.Name.Contains(cellName))
                    {
                        return cell;
                    }
                }
            }
            return null;
        }

        private int? getValidData(string str, string type)
        {
            int? data = null;
            if (str.Contains(type))
            {
                string strData = str.Replace(type, "");
                int iData;
                if (int.TryParse(strData, out iData))
                {
                    data = iData;
                }
            }
            return data;
        }

        public static LTECell GetLeakOutScanTpNCell(TestPoint tp, int index)
        {
            LTECell cell = null;
            if (tp is LTETestPointDetail)
            {
                int? earfcn = (int?)tp["lte_NCell_EARFCN", index];
                int? pci = (int?)(short?)tp["lte_NCell_PCI", index];
                cell = StationAcceptCellHelper_XJ.Instance.GetLTECellByEarfcnPci(tp, earfcn, pci);
            }
            return cell;
        }
        #endregion
        protected virtual void anaWithOtherFile(MasterCom.RAMS.Model.FileInfo fileInfo, DTFileDataManager fileManager)
        {
            LTECell targetCell = GetFileTestCell(fileManager);
            if (targetCell == null)
            {
                reportInfo(string.Format("文件{0}未找到目标小区", fileInfo.Name));
                return;
            }

            List<AcpAutoKpiBase> acceptAnaList = getAcceptAnaList(targetCell.Type);//根据小区类型获取业务分析类
            foreach (AcpAutoKpiBase acp in acceptAnaList)
            {
                if (acp.IsValidFile(fileInfo))
                {
                    doStatWithData(acp, fileInfo, fileManager, targetCell);
                }
            }
        }
        protected void doStatWithData(AcpAutoKpiBase acp, FileInfo fileInfo, DTFileDataManager fileManager, LTECell targetCell)
        {
            reportInfo(string.Format("文件{0}开始处理：{1}", fileInfo.Name, acp.GetType().ToString()));
            if (AcceptFileInfo == null)
            {
                AcceptFileInfo = new CellAcceptFileInfo(fileInfo, targetCell);
            }
            Dictionary<KpiKey, object> kpiInfoDic = acp.GetFileKpiInfos(fileInfo, fileManager, targetCell);
            if (kpiInfoDic != null)
            {
                foreach (KpiKey key in kpiInfoDic.Keys)
                {
                    object valueObj = kpiInfoDic[key];
                    if (valueObj is double)
                    {
                        double valueDouble = (double)valueObj;
                        if (valueDouble == double.MinValue)
                        {
                            valueObj = double.NaN;
                        }
                        else
                        {
                            valueObj = Math.Round(valueDouble, 4);
                        }
                    }

                    AcceptFileInfo.AcceptKpiDic.Add((uint)key, valueObj);
                }
            }
        }

        public static LTECell GetFileTestCell(DTFileDataManager fileManager)
        {
            LTECell targeCell = null;
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                LTECell cell = GetTpSrcCell(tp);
                if (isSrcTestCell(cell, fileManager.FileName))
                {
                    targeCell = cell;
                    break;
                }
            }
            return targeCell;
        }

        protected LTEBTS getFileTestBts(DTFileDataManager fileManager)
        {
            LTECell targeCell = null;
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                LTECell cell = GetTpSrcCell(tp);
                if (cell != null && fileManager.FileName.Contains(cell.BTSName))
                {
                    targeCell = cell;
                    break;
                }
            }
            if (targeCell != null)
            {
                return targeCell.BelongBTS;
            }
            return null;
        }

        public static LTECell GetTpSrcCell(TestPoint tp)
        {
            LTECell cell = null;
            if (tp is LTETestPointDetail)
            {
                cell = StationAcceptCellHelper_XJ.Instance.GetLTECellByTacEci(tp);

                int? earfcn = (int?)tp["lte_EARFCN"];
                int? pci = (int?)(short?)tp["lte_PCI"];

                if (cell == null)
                {
                    cell = StationAcceptCellHelper_XJ.Instance.GetLTECellByEarfcnPci(tp, earfcn, pci);
                }
                else if (earfcn != null && pci != null 
                    && (cell.EARFCN != (int)earfcn || cell.PCI != (int)pci))
                {
                    cell = StationAcceptCellHelper_XJ.Instance.GetLTECellByEarfcnPci(tp, earfcn, pci);
                }
            }
            return cell;
        }
        public static LTECell GetLTECellByEARFCNPCI(DateTime time, int? earfcn, int? pci, double longitude
            , double latitude, string fileName)
        {
            if (earfcn == null || pci == null)
            {
                return null;
            }

            List<LTECell> cells = CellManager.GetInstance().GetLTECellListByEARFCNPCI(time, earfcn, pci);
            if (cells != null && cells.Count > 0)
            {
                #region 先根据文件名匹配小区，匹配不到再取最近的一个小区
                if (!string.IsNullOrEmpty(fileName))
                {
                    LTECell cell = getCell(time, longitude, latitude, fileName, cells);
                    if (cell != null)
                    {
                        return cell;
                    }
                }
                #endregion

                return CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(time, earfcn, pci, longitude, latitude);
            }
            return null;
        }

        private static LTECell getCell(DateTime time, double longitude, double latitude, string fileName, List<LTECell> cells)
        {
            foreach (LTECell cell in cells)
            {
                if (cell.ValidPeriod.Contains(time))
                {
                    if (longitude > 0 && latitude > 0 && CellManager.GetInstance().SystemConfigInfo.distLimit
                        && cell.GetDistance(longitude, latitude) > CD.MAX_COV_DISTANCE_LTE)//距离限制设置
                    {
                        continue;
                    }
                    if (isSrcTestCell(cell, fileName))
                    {
                        return cell;
                    }
                }
            }
            return null;
        }

        private static bool isSrcTestCell(LTECell cell, string fileName)
        {
            if (cell == null)
            {
                return false;
            }
            fileName = fileName.Trim().ToUpper();
            string cellName = cell.Name.Trim().ToUpper();
            string btsName = cell.BTSName.Trim().ToUpper();
            if (fileName.Contains(cellName))
            {
                return true;
            }

            if (fileName.Contains(btsName) && fileName.Contains("PCI" + cell.PCI))
            {
                return true;
            }
            else if (cell.Type == LTEBTSType.Indoor)
            {
                string[] names = fileName.Split('_');
                if (names.Length > 3)
                {
                    string targeCellName = names[2];
                    if (cellName.Contains(targeCellName.Trim().ToUpper()))
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        protected static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);      
        protected void reportInfo(string strInfo)
        {
            if (MainModel.GetInstance().BackgroundStarted)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo(strInfo);
            }
            else
            {
                log.Info(strInfo);
            }
        }
        protected void reportInfo(Exception ex)
        {
            if (MainModel.GetInstance().BackgroundStarted)
            {
                BackgroundFuncManager.GetInstance().ReportBackgroundError(ex);
            }
            else
            {
                System.Windows.Forms.MessageBox.Show(ex.Message + Environment.NewLine + ex.Source + Environment.NewLine + ex.StackTrace);
            }
        }
    }
    public class StationAcceptAutoSet_XJ : StationAcceptAutoSetBase
    {
    }
    public class StationAcceptAutoSetBase
    {
        public StationAcceptAutoSetBase()
        {
            WorkParamBeforeDays_Begin = 2;
            WorkParamBeforeDays_End = 0;
            FusionRecentDays = 7;
            PerfDataAccordLastDays = 3;
            IsAnaFusionByRecentDays = true;
        }

        public int WorkParamBeforeDays_Begin { get; set; }
        public int WorkParamBeforeDays_End { get; set; }
        public DateTime WorkParamTime_Begin
        {
            get
            {
                return DateTime.Now.Date.AddDays(-1 * (WorkParamBeforeDays_Begin));
            }
        }
        public DateTime WorkParamTime_End
        {
            get
            {
                return DateTime.Now.Date.AddDays(-1 * (WorkParamBeforeDays_End - 1));
            }
        }
        /// <summary>
        /// 工参选择从excel导入时的地址
        /// </summary>
        public string CellParamFolderPath { get; set; }
        public string AntennaInfoFolderPath { get; set; }//天资实测数据
        public string OutdoorBtsPicFolderPath { get; set; }
        public string IndoorCoverPicFolderPath { get; set; }
        public string ReportSavePath { get; set; }
        public string ResultWebservicePath { get; set; }
        public bool IsCheckWebservice { get; set; }

        public bool IsAnaFusionDatas { get; set; }

        public bool IsAnaFusionByRecentDays { get; set; }
        /// <summary>
        /// 关联性能MR告警数据时，查询最近N天内的关联数据
        /// </summary>
        public int FusionRecentDays { get; set; }

        public string FusionByPeriodBeginDate { get; set; }
        public string FusionByPeriodEndDate { get; set; }

        public DateTime FusionBeginTime
        {
            get
            {
                if (!IsAnaFusionByRecentDays)
                {
                    DateTime fusionBeginTime;
                    if (DateTime.TryParse(FusionByPeriodBeginDate, out fusionBeginTime))
                    {
                        return fusionBeginTime;
                    }
                }
                return DateTime.Now.Date.AddDays(-1 * (FusionRecentDays - 1));
            }
        }

        public DateTime FusionEndTime
        {
            get
            {
                if (!IsAnaFusionByRecentDays)
                {
                    DateTime fusionEndTime;
                    if (DateTime.TryParse(FusionByPeriodEndDate, out fusionEndTime))
                    {
                        return fusionEndTime.Date.AddDays(1).AddMilliseconds(-1);
                    }
                }
                return DateTime.Now.Date.AddDays(1).AddMilliseconds(-1);
            }
        }
        /// <summary>
        /// 性能数据要求至少连续N天指标都要达到门限值才能通过验收
        /// </summary>
        public int PerfDataAccordLastDays { get; set; }

        private string perfDataCheckDistictNames; //要判断性能数据是否连续若干天合格的地市
        public string PerfDataCheckDistictNames
        {
            get { return string.IsNullOrEmpty(perfDataCheckDistictNames) ? "" : perfDataCheckDistictNames.Trim().Replace("市", ""); }
            set { perfDataCheckDistictNames = value; }
        }

        public Dictionary<string, object> Params
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["ReportSavePath"] = this.ReportSavePath;
                param["IsCheckWebservice"] = this.IsCheckWebservice;
                param["ResultWebservicePath"] = this.ResultWebservicePath;
                param["WorkParamBeforeDays_Begin"] = this.WorkParamBeforeDays_Begin;
                param["WorkParamBeforeDays_End"] = this.WorkParamBeforeDays_End;
                param["CellParamFolderPath"] = this.CellParamFolderPath;
                param["AntennaInfoFolderPath"] = this.AntennaInfoFolderPath;
                param["OutdoorBtsPicFolderPath"] = this.OutdoorBtsPicFolderPath;
                param["IndoorCoverPicFolderPath"] = this.IndoorCoverPicFolderPath;
                param["IsAnaFusionDatas"] = this.IsAnaFusionDatas;
                param["IsAnaFusionByRecentDays"] = this.IsAnaFusionByRecentDays;
                param["FusionRecentDays"] = this.FusionRecentDays;
                param["FusionByPeriodBeginDate"] = this.FusionByPeriodBeginDate;
                param["FusionByPeriodEndDate"] = this.FusionByPeriodEndDate;
                param["FusionAccordLastDays"] = this.PerfDataAccordLastDays;
                param["FusionCheckDistictNames"] = this.PerfDataCheckDistictNames;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                setParam(param);
            }
        }

        private void setParam(Dictionary<string, object> param)
        {
            setPath(param);
            if (param.ContainsKey("WorkParamBeforeDays_Begin"))
            {
                this.WorkParamBeforeDays_Begin = (int)param["WorkParamBeforeDays_Begin"];
            }
            if (param.ContainsKey("WorkParamBeforeDays_End"))
            {
                this.WorkParamBeforeDays_End = (int)param["WorkParamBeforeDays_End"];
            }
            if (param.ContainsKey("IsAnaFusionDatas"))
            {
                this.IsAnaFusionDatas = (bool)param["IsAnaFusionDatas"];
            }
            if (param.ContainsKey("IsAnaFusionByRecentDays"))
            {
                this.IsAnaFusionByRecentDays = (bool)param["IsAnaFusionByRecentDays"];
            }
            if (param.ContainsKey("FusionRecentDays"))
            {
                this.FusionRecentDays = (int)param["FusionRecentDays"];
            }
            if (param.ContainsKey("FusionByPeriodBeginDate"))
            {
                this.FusionByPeriodBeginDate = (string)param["FusionByPeriodBeginDate"];
            }
            if (param.ContainsKey("FusionByPeriodEndDate"))
            {
                this.FusionByPeriodEndDate = (string)param["FusionByPeriodEndDate"];
            }
            if (param.ContainsKey("FusionAccordLastDays"))
            {
                this.PerfDataAccordLastDays = (int)param["FusionAccordLastDays"];
            }
            if (param.ContainsKey("FusionCheckDistictNames"))
            {
                this.PerfDataCheckDistictNames = (string)param["FusionCheckDistictNames"];
            }
        }

        private void setPath(Dictionary<string, object> param)
        {
            if (param.ContainsKey("ReportSavePath"))
            {
                this.ReportSavePath = (string)param["ReportSavePath"];
            }
            if (param.ContainsKey("IsCheckWebservice"))
            {
                this.IsCheckWebservice = (bool)param["IsCheckWebservice"];
            }
            if (param.ContainsKey("ResultWebservicePath"))
            {
                this.ResultWebservicePath = (string)param["ResultWebservicePath"];
            }
            if (param.ContainsKey("CellParamFolderPath"))
            {
                this.CellParamFolderPath = (string)param["CellParamFolderPath"];
            }
            if (param.ContainsKey("AntennaInfoFolderPath"))
            {
                this.AntennaInfoFolderPath = (string)param["AntennaInfoFolderPath"];
            }
            if (param.ContainsKey("OutdoorBtsPicFolderPath"))
            {
                this.OutdoorBtsPicFolderPath = (string)param["OutdoorBtsPicFolderPath"];
            }
            if (param.ContainsKey("IndoorCoverPicFolderPath"))
            {
                this.IndoorCoverPicFolderPath = (string)param["IndoorCoverPicFolderPath"];
            }
        }
    }
}
