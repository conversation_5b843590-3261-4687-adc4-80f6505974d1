using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;

namespace MasterCom.ES.ColorManager
{
    public class EventColorItem:MasterCom.Util.AConfig_Ext
    {
        public EventColorItem()
        {   
        }

        public EventColorItem(string name,string exp, Color col, string des)
        {
            this.Name = name;
            this.Expression = exp;
            this.Color = col;
            this.Des = des;
        }

        public int Size { get; set; } = 8;
        public string Expression { get; set; } = "";
        public Color Color { get; set; } = Color.White;
        public string Des { get; set; } = "";
        public bool IsChecked { get; set; } = false;
        public string Name { get; set; } = "";
    }

    public class EventCategory : MasterCom.Util.AConfig_Ext
    {
        public string Name { get; set; } = "";
        public string Des { get; set; } = "";
        public List<EventColorItem> ColorItems { get; set; } = new List<EventColorItem>();

        public List<EventColorItem> CheckedColorItems
        {
            get
            {
                List<EventColorItem> ecis = new List<EventColorItem>();
                foreach (EventColorItem eci in ColorItems)
                {
                    if (eci.IsChecked)
                        ecis.Add(eci);
                }
                return ecis;
            }
        }
    }
}
