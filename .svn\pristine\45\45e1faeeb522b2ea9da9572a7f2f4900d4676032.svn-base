﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTRtpPacketsLostListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCloseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.objLv = new BrightIdeasSoftware.TreeListView();
            this.colSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colSSRC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colEARFCN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colPCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLostReason = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colSINRBefore = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colRSRPBefore = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colMaxSINRBefore = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colMaxRSRPBefore = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colSINRAfter = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colRSRPAfter = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colMaxSINRAfter = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colMaxRSRPAfter = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colDirection = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLostNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colRate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.objLv)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel,
            this.miExpandAll,
            this.miCloseAll});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(130, 70);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(129, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCloseAll
            // 
            this.miCloseAll.Name = "miCloseAll";
            this.miCloseAll.Size = new System.Drawing.Size(129, 22);
            this.miCloseAll.Text = "全部折叠";
            this.miCloseAll.Click += new System.EventHandler(this.miCloseAll_Click);
            // 
            // objLv
            // 
            this.objLv.AllColumns.Add(this.colSN);
            this.objLv.AllColumns.Add(this.colFileName);
            this.objLv.AllColumns.Add(this.colSSRC);
            this.objLv.AllColumns.Add(this.colLongitude);
            this.objLv.AllColumns.Add(this.colLatitude);
            this.objLv.AllColumns.Add(this.colEARFCN);
            this.objLv.AllColumns.Add(this.colPCI);
            this.objLv.AllColumns.Add(this.colTime);
            this.objLv.AllColumns.Add(this.colLostReason);
            this.objLv.AllColumns.Add(this.colSINRBefore);
            this.objLv.AllColumns.Add(this.colRSRPBefore);
            this.objLv.AllColumns.Add(this.colMaxSINRBefore);
            this.objLv.AllColumns.Add(this.colMaxRSRPBefore);
            this.objLv.AllColumns.Add(this.colSINRAfter);
            this.objLv.AllColumns.Add(this.colRSRPAfter);
            this.objLv.AllColumns.Add(this.colMaxSINRAfter);
            this.objLv.AllColumns.Add(this.colMaxRSRPAfter);
            this.objLv.AllColumns.Add(this.colDirection);
            this.objLv.AllColumns.Add(this.colLostNum);
            this.objLv.AllColumns.Add(this.colNum);
            this.objLv.AllColumns.Add(this.colRate);
            this.objLv.AllColumns.Add(this.colType);
            this.objLv.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.colSN,
            this.colFileName,
            this.colSSRC,
            this.colLongitude,
            this.colLatitude,
            this.colEARFCN,
            this.colPCI,
            this.colTime,
            this.colLostReason,
            this.colSINRBefore,
            this.colRSRPBefore,
            this.colMaxSINRBefore,
            this.colMaxRSRPBefore,
            this.colSINRAfter,
            this.colRSRPAfter,
            this.colMaxSINRAfter,
            this.colMaxRSRPAfter,
            this.colDirection,
            this.colLostNum,
            this.colNum,
            this.colRate,
            this.colType});
            this.objLv.ContextMenuStrip = this.ctxMenu;
            this.objLv.Cursor = System.Windows.Forms.Cursors.Default;
            this.objLv.Dock = System.Windows.Forms.DockStyle.Fill;
            this.objLv.FullRowSelect = true;
            this.objLv.GridLines = true;
            this.objLv.HeaderWordWrap = true;
            this.objLv.Location = new System.Drawing.Point(0, 0);
            this.objLv.Name = "objLv";
            this.objLv.OwnerDraw = true;
            this.objLv.ShowGroups = false;
            this.objLv.Size = new System.Drawing.Size(805, 456);
            this.objLv.TabIndex = 5;
            this.objLv.UseCompatibleStateImageBehavior = false;
            this.objLv.View = System.Windows.Forms.View.Details;
            this.objLv.VirtualMode = true;
            this.objLv.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.objLv_MouseDoubleClick);
            // 
            // colSN
            // 
            this.colSN.HeaderFont = null;
            this.colSN.Text = "序号";
            // 
            // colFileName
            // 
            this.colFileName.HeaderFont = null;
            this.colFileName.Text = "文件名";
            this.colFileName.Width = 120;
            // 
            // colSSRC
            // 
            this.colSSRC.HeaderFont = null;
            this.colSSRC.Text = "Source_SSRC";
            this.colSSRC.Width = 80;
            // 
            // colLongitude
            // 
            this.colLongitude.HeaderFont = null;
            this.colLongitude.Text = "经度";
            // 
            // colLatitude
            // 
            this.colLatitude.HeaderFont = null;
            this.colLatitude.Text = "纬度";
            // 
            // colEARFCN
            // 
            this.colEARFCN.HeaderFont = null;
            this.colEARFCN.Text = "EARFCN";
            // 
            // colPCI
            // 
            this.colPCI.HeaderFont = null;
            this.colPCI.Text = "PCI";
            //
            // colLostReason
            //
            this.colLostReason.HeaderFont = null;
            this.colLostReason.Text = "丢包原因分析";
            // 
            // colSINRBefore
            // 
            this.colSINRBefore.HeaderFont = null;
            this.colSINRBefore.Text = "丢包前平均SINR";
            // 
            // colRSRPBefore
            // 
            this.colRSRPBefore.HeaderFont = null;
            this.colRSRPBefore.Text = "丢包前平均RSRP";
            // 
            // colMaxSINRBefore
            // 
            this.colMaxSINRBefore.HeaderFont = null;
            this.colMaxSINRBefore.Text = "丢包前MaxSINR";
            // 
            // colMaxRSRPBefore
            // 
            this.colMaxRSRPBefore.HeaderFont = null;
            this.colMaxRSRPBefore.Text = "丢包前MaxRSRP";
            // 
            // colSINRAfter
            // 
            this.colSINRAfter.HeaderFont = null;
            this.colSINRAfter.Text = "丢包后平均SINR";
            // 
            // colRSRPAfter
            // 
            this.colRSRPAfter.HeaderFont = null;
            this.colRSRPAfter.Text = "丢包后平均RSRP";
            // 
            // colMaxSINRAfter
            // 
            this.colMaxSINRAfter.HeaderFont = null;
            this.colMaxSINRAfter.Text = "丢包后MaxSINR";
            // 
            // colMaxRSRPAfter
            // 
            this.colMaxRSRPAfter.HeaderFont = null;
            this.colMaxRSRPAfter.Text = "丢包后MaxRSRP";
            // 
            // colDirection
            // 
            this.colDirection.HeaderFont = null;
            this.colDirection.Text = "RTP_Direction";
            this.colDirection.Width = 90;
            // 
            // colLostNum
            // 
            this.colLostNum.HeaderFont = null;
            this.colLostNum.Text = "RTP_Packets_Lost_Num";
            this.colLostNum.Width = 140;
            // 
            // colNum
            // 
            this.colNum.HeaderFont = null;
            this.colNum.Text = "RTP_Packets_Num";
            this.colNum.Width = 110;
            // 
            // colRate
            // 
            this.colRate.HeaderFont = null;
            this.colRate.Text = "RTP_Loss_Rate";
            this.colRate.Width = 90;
            // 
            // colType
            // 
            this.colType.HeaderFont = null;
            this.colType.Text = "RTP_Media_Type";
            this.colType.Width = 110;
            // 
            // colTime
            // 
            this.colTime.HeaderFont = null;
            this.colTime.Text = "时间";
            // 
            // ZTRtpPacketsLostListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(805, 456);
            this.Controls.Add(this.objLv);
            this.Name = "ZTRtpPacketsLostListForm";
            this.Text = "RTP丢包率分析";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.objLv)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private BrightIdeasSoftware.TreeListView objLv;
        private BrightIdeasSoftware.OLVColumn colSN;
        private BrightIdeasSoftware.OLVColumn colEARFCN;
        private BrightIdeasSoftware.OLVColumn colPCI;
        private BrightIdeasSoftware.OLVColumn colDirection;
        private BrightIdeasSoftware.OLVColumn colLostNum;
        private BrightIdeasSoftware.OLVColumn colNum;
        private BrightIdeasSoftware.OLVColumn colRate;
        private BrightIdeasSoftware.OLVColumn colType;
        private BrightIdeasSoftware.OLVColumn colSSRC;
        private BrightIdeasSoftware.OLVColumn colFileName;
        private BrightIdeasSoftware.OLVColumn colLongitude;
        private BrightIdeasSoftware.OLVColumn colLatitude;
        private BrightIdeasSoftware.OLVColumn colTime;
        private BrightIdeasSoftware.OLVColumn colLostReason;
        private BrightIdeasSoftware.OLVColumn colSINRBefore;
        private BrightIdeasSoftware.OLVColumn colRSRPBefore;
        private BrightIdeasSoftware.OLVColumn colSINRAfter;
        private BrightIdeasSoftware.OLVColumn colRSRPAfter;
        private BrightIdeasSoftware.OLVColumn colMaxSINRBefore;
        private BrightIdeasSoftware.OLVColumn colMaxRSRPBefore;
        private BrightIdeasSoftware.OLVColumn colMaxSINRAfter;
        private BrightIdeasSoftware.OLVColumn colMaxRSRPAfter;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCloseAll;
    }
}