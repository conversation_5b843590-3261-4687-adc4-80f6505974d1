﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.MTGis;
using MapWinGIS;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LtePlanningRoadResultForm : MinCloseForm
    {
        public LtePlanningRoadResultForm(MainModel mainModel) : base(mainModel)
        {
            InitializeComponent();
            miExportCurXls.Click += MiExportCurXls_Click;
            miExportAllXls.Click += MiExportAllXls_Click;
            miExportCurShp.Click += MiExportCurShp_Click;
            miExportAllShp.Click += MiExportAllShp_Click;
            gvWeakCoverage.DoubleClick += GridView_DoubleClick;
            gvMultiCoverage.DoubleClick += GridView_DoubleClick;
        }

        public void FillData(List<object> data)
        {
            gcWeakCoverage.DataSource = null;
            gcMultiCoverage.DataSource = null;

            foreach (object o in data)
            {
                if (o is List<WeakCoverageRoadView>)
                {
                    gcWeakCoverage.DataSource = o;
                }
                else if (o is List<MultiCoverageRoadView>)
                {
                    gcMultiCoverage.DataSource = o;
                }
            }

            FireDTDataChanged();
        }

        private void MiExportCurXls_Click(object sender, EventArgs e)
        {
            TabPage tp = tabControl.SelectedTab;
            GridControl gc = tp.Controls[0] as GridControl;
            GridView gv = gc.MainView as GridView;

            List<GridView> gvs = new List<GridView>();
            gvs.Add(gv);
            List<string> sheetNames = new List<string>();
            sheetNames.Add(tp.Text);
            ExcelNPOIManager.ExportToExcel(gvs, sheetNames);
        }

        private void MiExportAllXls_Click(object sender, EventArgs e)
        {
            List<GridView> gvs = new List<GridView>();
            List<string> sheetNames = new List<string>();
            foreach (TabPage tp in tabControl.TabPages)
            {
                GridControl gc = tp.Controls[0] as GridControl;
                GridView gv = gc.MainView as GridView;

                gvs.Add(gv);
                sheetNames.Add(tp.Text);
            }
            ExcelNPOIManager.ExportToExcel(gvs, sheetNames);
        }

        private void GridView_DoubleClick(object sender, EventArgs e)
        {
            GridView gv = sender as GridView;
            int[] selectedRows = gv.GetSelectedRows();
            if (selectedRows == null || selectedRows.Length == 0)
            {
                return;
            }

            LtePlanningRoadView roadView = gv.GetRow(selectedRows[0]) as LtePlanningRoadView;
            if (roadView == null)
            {
                return;
            }

            OutlineOfRoad outRoad = new OutlineOfRoad();
            outRoad.SetPoints(roadView.DbPoints);
            TempLayer.Instance.Draw(outRoad.Drawer);

            MainModel.DTDataManager.Clear();
            foreach (TestPoint tp in roadView.TestPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }
            MainModel.FireDTDataChanged(MainModel.MainForm);
            MainModel.MainForm.GetMapForm().GoToView(roadView.CentLng, roadView.CentLat);
        }

        private void MiExportCurShp_Click(object sender, EventArgs e)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.Filter = FilterHelper.Shp;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            TabPage tp = tabControl.SelectedTab;
            GridControl gc = tp.Controls[0] as GridControl;
            GridView gv = gc.MainView as GridView;

            ExportGridViewShp(dlg.FileName, new List<GridView>() { gv });
        }

        private void MiExportAllShp_Click(object sender, EventArgs e)
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.Filter = FilterHelper.Shp;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            List<GridView> gvs = new List<GridView>();
            foreach (TabPage tp in tabControl.TabPages)
            {
                GridControl gc = tp.Controls[0] as GridControl;
                GridView gv = gc.MainView as GridView;
                gvs.Add(gv);
            }

            ExportGridViewShp(dlg.FileName, gvs);
        }

        private void FireDTDataChanged()
        {
            MainModel.DTDataManager.Clear();
            foreach (TabPage tp in tabControl.TabPages)
            {
                GridControl gc = tp.Controls[0] as GridControl;
                GridView gv = gc.MainView as GridView;
                for (int i = 0; i < gv.RowCount; ++i)
                {
                    LtePlanningRoadView roadView = gv.GetRow(i) as LtePlanningRoadView;
                    foreach (TestPoint tPoint in roadView.TestPoints)
                    {
                        MainModel.DTDataManager.Add(tPoint);
                    }
                }
            }
            MainModel.FireSetDefaultMapSerialTheme("LTE:RSRP");
        }

        private List<MapWinGIS.Shape> GetRoadShapes(LtePlanningRoadView road)
        {
            List<MapWinGIS.Shape> shapeList = new List<MapWinGIS.Shape>();
            foreach (DbPoint dPoint in road.DbPoints)
            {
                MapWinGIS.Shape shape = new MapWinGIS.Shape();
                shape.ShapeType = ShpfileType.SHP_POINT;
                MapWinGIS.Point pt = new MapWinGIS.Point();
                pt.x = dPoint.x;
                pt.y = dPoint.y;
                int ptIndex = 0;
                shape.InsertPoint(pt, ref ptIndex);
                shapeList.Add(shape);
            }
            return shapeList;
        }

        private void ExportGridViewShp(string shpFileName, List<GridView> gvs)
        {
            Shapefile shp = new Shapefile();
            shp.CreateNew("", ShpfileType.SHP_POINT);

            int fieldIndex = 0;
            Field field = new Field();
            field.Name = "RoadDesc";
            field.Type = FieldType.STRING_FIELD;
            shp.EditInsertField(field, ref fieldIndex, null);

            ++fieldIndex;
            field = new Field();
            field.Name = "RoadIndex";
            field.Type = FieldType.INTEGER_FIELD;
            shp.EditInsertField(field, ref fieldIndex, null);

            int roadIndex = 0;
            int shapeIndex = 0;
            foreach (GridView gv in gvs)
            {
                string roadDesc = "";
                if (gv == gvWeakCoverage)
                {
                    roadDesc = "WeakCoverage";
                }
                else if (gv == gvMultiCoverage)
                {
                    roadDesc = "MultiCoverage";
                }

                for (int i = 0; i < gv.RowCount; ++i)
                {
                    LtePlanningRoadView roadView = gv.GetRow(i) as LtePlanningRoadView;
                    List<MapWinGIS.Shape> shapeList = GetRoadShapes(roadView);
                    ++roadIndex;
                    foreach (MapWinGIS.Shape shape in shapeList)
                    {
                        shp.EditInsertShape(shape, ref shapeIndex);
                        shp.EditCellValue(0, shapeIndex, roadDesc);
                        shp.EditCellValue(1, shapeIndex, roadIndex);
                        ++shapeIndex;
                    }
                }
            }

            if (shp.SaveAs(shpFileName, null))
            {
                MessageBox.Show("保存图层生成!", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("保存图层失败!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            shp.Close();
        }
    }
}
