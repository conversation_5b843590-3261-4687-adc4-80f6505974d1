﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTCsfbCellJudgeListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.ListViewCsfbCellJudge = new BrightIdeasSoftware.TreeListView();
            this.olvColumnStatSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMoFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMtFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLTECellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLTEECI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnENBID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLTETAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLTELongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLTELatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLTEDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnGSMLongitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnGSMLatitude = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnGSMDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnGSMRxLev = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMaxGSMDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMaxGSMRxLev = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnIsInList = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC1 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC2 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell4 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType4 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID4 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev4 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI4 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC4 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell5 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType5 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID5 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev5 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI5 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC5 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell6 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType6 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID6 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev6 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI6 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC6 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell7 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType7 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID7 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev7 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI7 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC7 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell8 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType8 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID8 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev8 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI8 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC8 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell9 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType9 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID9 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev9 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI9 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC9 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell10 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType10 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID10 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev10 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI10 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC10 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell11 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType11 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID11 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev11 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI11 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC11 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell12 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType12 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID12 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev12 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI12 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC12 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell13 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType13 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID13 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev13 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI13 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC13 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell14 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType14 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID14 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev14 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI14 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC14 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell15 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType15 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID15 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev15 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI15 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC15 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell16 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType16 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID16 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev16 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI16 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC16 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell17 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType17 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID17 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev17 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI17 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC17 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell18 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType18 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID18 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev18 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI18 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC18 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell19 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType19 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID19 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev19 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI19 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC19 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell20 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType20 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID20 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev20 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI20 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC20 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell21 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType21 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID21 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev21 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI21 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC21 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell22 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType22 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID22 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev22 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI22 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC22 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell23 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType23 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID23 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev23 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI23 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC23 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell24 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType24 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID24 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev24 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI24 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC24 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell25 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType25 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID25 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev25 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI25 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC25 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell26 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType26 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID26 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev26 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI26 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC26 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell27 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType27 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID27 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev27 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI27 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC27 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell28 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType28 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID28 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev28 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI28 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC28 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell29 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType29 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID29 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev29 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI29 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC29 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell30 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType30 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID30 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev30 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI30 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC30 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell31 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType31 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID31 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev31 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI31 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC31 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCell32 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType32 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellID32 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxLev32 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI32 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSIC32 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCsfbCellJudge)).BeginInit();
            this.ctxMenu.SuspendLayout();
            this.SuspendLayout();
            // 
            // ListViewCsfbCellJudge
            // 
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnStatSN);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnMoFileName);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnMtFileName);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnTime);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnLongitude);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnLatitude);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnLTECellName);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnLTEECI);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnENBID);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnLTETAC);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnLTELongitude);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnLTELatitude);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnLTEDistance);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellName);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnLAC);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnGSMLongitude);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnGSMLatitude);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnGSMDistance);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnGSMRxLev);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnMaxGSMDistance);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnMaxGSMRxLev);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnIsInList);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell1);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType1);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID1);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev1);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI1);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC1);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell2);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType2);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID2);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev2);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI2);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC2);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell3);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType3);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID3);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev3);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI3);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC3);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell4);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType4);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID4);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev4);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI4);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC4);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell5);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType5);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID5);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev5);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI5);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC5);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell6);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType6);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID6);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev6);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI6);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC6);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell7);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType7);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID7);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev7);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI7);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC7);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell8);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType8);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID8);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev8);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI8);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC8);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell9);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType9);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID9);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev9);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI9);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC9);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell10);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType10);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID10);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev10);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI10);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC10);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell11);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType11);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID11);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev11);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI11);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC11);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell12);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType12);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID12);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev12);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI12);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC12);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell13);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType13);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID13);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev13);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI13);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC13);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell14);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType14);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID14);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev14);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI14);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC14);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell15);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType15);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID15);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev15);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI15);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC15);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell16);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType16);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID16);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev16);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI16);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC16);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell17);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType17);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID17);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev17);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI17);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC17);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell18);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType18);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID18);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev18);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI18);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC18);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell19);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType19);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID19);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev19);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI19);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC19);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell20);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType20);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID20);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev20);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI20);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC20);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell21);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType21);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID21);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev21);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI21);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC21);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell22);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType22);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID22);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev22);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI22);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC22);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell23);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType23);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID23);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev23);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI23);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC23);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell24);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType24);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID24);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev24);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI24);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC24);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell25);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType25);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID25);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev25);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI25);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC25);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell26);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType26);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID26);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev26);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI26);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC26);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell27);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType27);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID27);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev27);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI27);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC27);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell28);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType28);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID28);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev28);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI28);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC28);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell29);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType29);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID29);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev29);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI29);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC29);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell30);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType30);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID30);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev30);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI30);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC30);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell31);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType31);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID31);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev31);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI31);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC31);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCell32);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellType32);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnCellID32);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnRxLev32);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnPCI32);
            this.ListViewCsfbCellJudge.AllColumns.Add(this.olvColumnBSIC32);
            this.ListViewCsfbCellJudge.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnStatSN,
            this.olvColumnMoFileName,
            this.olvColumnMtFileName,
            this.olvColumnTime,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnLTECellName,
            this.olvColumnLTEECI,
            this.olvColumnENBID,
            this.olvColumnLTETAC,
            this.olvColumnLTELongitude,
            this.olvColumnLTELatitude,
            this.olvColumnLTEDistance,
            this.olvColumnCellName,
            this.olvColumnCellID,
            this.olvColumnLAC,
            this.olvColumnGSMLongitude,
            this.olvColumnGSMLatitude,
            this.olvColumnGSMDistance,
            this.olvColumnGSMRxLev,
            this.olvColumnMaxGSMDistance,
            this.olvColumnMaxGSMRxLev,
            this.olvColumnIsInList,
            this.olvColumnCell1,
            this.olvColumnCellType1,
            this.olvColumnCellID1,
            this.olvColumnRxLev1,
            this.olvColumnPCI1,
            this.olvColumnBSIC1,
            this.olvColumnCell2,
            this.olvColumnCellType2,
            this.olvColumnCellID2,
            this.olvColumnRxLev2,
            this.olvColumnPCI2,
            this.olvColumnBSIC2,
            this.olvColumnCell3,
            this.olvColumnCellType3,
            this.olvColumnCellID3,
            this.olvColumnRxLev3,
            this.olvColumnPCI3,
            this.olvColumnBSIC3,
            this.olvColumnCell4,
            this.olvColumnCellType4,
            this.olvColumnCellID4,
            this.olvColumnRxLev4,
            this.olvColumnPCI4,
            this.olvColumnBSIC4,
            this.olvColumnCell5,
            this.olvColumnCellType5,
            this.olvColumnCellID5,
            this.olvColumnRxLev5,
            this.olvColumnPCI5,
            this.olvColumnBSIC5,
            this.olvColumnCell6,
            this.olvColumnCellType6,
            this.olvColumnCellID6,
            this.olvColumnRxLev6,
            this.olvColumnPCI6,
            this.olvColumnBSIC6,
            this.olvColumnCell7,
            this.olvColumnCellType7,
            this.olvColumnCellID7,
            this.olvColumnRxLev7,
            this.olvColumnPCI7,
            this.olvColumnBSIC7,
            this.olvColumnCell8,
            this.olvColumnCellType8,
            this.olvColumnCellID8,
            this.olvColumnRxLev8,
            this.olvColumnPCI8,
            this.olvColumnBSIC8,
            this.olvColumnCell9,
            this.olvColumnCellType9,
            this.olvColumnCellID9,
            this.olvColumnRxLev9,
            this.olvColumnPCI9,
            this.olvColumnBSIC9,
            this.olvColumnCell10,
            this.olvColumnCellType10,
            this.olvColumnCellID10,
            this.olvColumnRxLev10,
            this.olvColumnPCI10,
            this.olvColumnBSIC10,
            this.olvColumnCell11,
            this.olvColumnCellType11,
            this.olvColumnCellID11,
            this.olvColumnRxLev11,
            this.olvColumnPCI11,
            this.olvColumnBSIC11,
            this.olvColumnCell12,
            this.olvColumnCellType12,
            this.olvColumnCellID12,
            this.olvColumnRxLev12,
            this.olvColumnPCI12,
            this.olvColumnBSIC12,
            this.olvColumnCell13,
            this.olvColumnCellType13,
            this.olvColumnCellID13,
            this.olvColumnRxLev13,
            this.olvColumnPCI13,
            this.olvColumnBSIC13,
            this.olvColumnCell14,
            this.olvColumnCellType14,
            this.olvColumnCellID14,
            this.olvColumnRxLev14,
            this.olvColumnPCI14,
            this.olvColumnBSIC14,
            this.olvColumnCell15,
            this.olvColumnCellType15,
            this.olvColumnCellID15,
            this.olvColumnRxLev15,
            this.olvColumnPCI15,
            this.olvColumnBSIC15,
            this.olvColumnCell16,
            this.olvColumnCellType16,
            this.olvColumnCellID16,
            this.olvColumnRxLev16,
            this.olvColumnPCI16,
            this.olvColumnBSIC16,
            this.olvColumnCell17,
            this.olvColumnCellType17,
            this.olvColumnCellID17,
            this.olvColumnRxLev17,
            this.olvColumnPCI17,
            this.olvColumnBSIC17,
            this.olvColumnCell18,
            this.olvColumnCellType18,
            this.olvColumnCellID18,
            this.olvColumnRxLev18,
            this.olvColumnPCI18,
            this.olvColumnBSIC18,
            this.olvColumnCell19,
            this.olvColumnCellType19,
            this.olvColumnCellID19,
            this.olvColumnRxLev19,
            this.olvColumnPCI19,
            this.olvColumnBSIC19,
            this.olvColumnCell20,
            this.olvColumnCellType20,
            this.olvColumnCellID20,
            this.olvColumnRxLev20,
            this.olvColumnPCI20,
            this.olvColumnBSIC20,
            this.olvColumnCell21,
            this.olvColumnCellType21,
            this.olvColumnCellID21,
            this.olvColumnRxLev21,
            this.olvColumnPCI21,
            this.olvColumnBSIC21,
            this.olvColumnCell22,
            this.olvColumnCellType22,
            this.olvColumnCellID22,
            this.olvColumnRxLev22,
            this.olvColumnPCI22,
            this.olvColumnBSIC22,
            this.olvColumnCell23,
            this.olvColumnCellType23,
            this.olvColumnCellID23,
            this.olvColumnRxLev23,
            this.olvColumnPCI23,
            this.olvColumnBSIC23,
            this.olvColumnCell24,
            this.olvColumnCellType24,
            this.olvColumnCellID24,
            this.olvColumnRxLev24,
            this.olvColumnPCI24,
            this.olvColumnBSIC24,
            this.olvColumnCell25,
            this.olvColumnCellType25,
            this.olvColumnCellID25,
            this.olvColumnRxLev25,
            this.olvColumnPCI25,
            this.olvColumnBSIC25,
            this.olvColumnCell26,
            this.olvColumnCellType26,
            this.olvColumnCellID26,
            this.olvColumnRxLev26,
            this.olvColumnPCI26,
            this.olvColumnBSIC26,
            this.olvColumnCell27,
            this.olvColumnCellType27,
            this.olvColumnCellID27,
            this.olvColumnRxLev27,
            this.olvColumnPCI27,
            this.olvColumnBSIC27,
            this.olvColumnCell28,
            this.olvColumnCellType28,
            this.olvColumnCellID28,
            this.olvColumnRxLev28,
            this.olvColumnPCI28,
            this.olvColumnBSIC28,
            this.olvColumnCell29,
            this.olvColumnCellType29,
            this.olvColumnCellID29,
            this.olvColumnRxLev29,
            this.olvColumnPCI29,
            this.olvColumnBSIC29,
            this.olvColumnCell30,
            this.olvColumnCellType30,
            this.olvColumnCellID30,
            this.olvColumnRxLev30,
            this.olvColumnPCI30,
            this.olvColumnBSIC30,
            this.olvColumnCell31,
            this.olvColumnCellType31,
            this.olvColumnCellID31,
            this.olvColumnRxLev31,
            this.olvColumnPCI31,
            this.olvColumnBSIC31,
            this.olvColumnCell32,
            this.olvColumnCellType32,
            this.olvColumnCellID32,
            this.olvColumnRxLev32,
            this.olvColumnPCI32,
            this.olvColumnBSIC32});
            this.ListViewCsfbCellJudge.ContextMenuStrip = this.ctxMenu;
            this.ListViewCsfbCellJudge.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewCsfbCellJudge.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ListViewCsfbCellJudge.FullRowSelect = true;
            this.ListViewCsfbCellJudge.GridLines = true;
            this.ListViewCsfbCellJudge.HeaderWordWrap = true;
            this.ListViewCsfbCellJudge.IsNeedShowOverlay = false;
            this.ListViewCsfbCellJudge.Location = new System.Drawing.Point(0, 0);
            this.ListViewCsfbCellJudge.Name = "ListViewCsfbCellJudge";
            this.ListViewCsfbCellJudge.OwnerDraw = true;
            this.ListViewCsfbCellJudge.ShowGroups = false;
            this.ListViewCsfbCellJudge.Size = new System.Drawing.Size(1197, 509);
            this.ListViewCsfbCellJudge.TabIndex = 8;
            this.ListViewCsfbCellJudge.UseCompatibleStateImageBehavior = false;
            this.ListViewCsfbCellJudge.View = System.Windows.Forms.View.Details;
            this.ListViewCsfbCellJudge.VirtualMode = true;
            this.ListViewCsfbCellJudge.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.ListViewCsfbCellJudge_MouseDoubleClick);
            // 
            // olvColumnStatSN
            // 
            this.olvColumnStatSN.AspectName = "";
            this.olvColumnStatSN.HeaderFont = null;
            this.olvColumnStatSN.Text = "序号";
            this.olvColumnStatSN.Width = 40;
            // 
            // olvColumnMoFileName
            // 
            this.olvColumnMoFileName.HeaderFont = null;
            this.olvColumnMoFileName.Text = "CSFB文件";
            this.olvColumnMoFileName.Width = 80;
            // 
            // olvColumnMtFileName
            // 
            this.olvColumnMtFileName.HeaderFont = null;
            this.olvColumnMtFileName.Text = "GSM文件";
            this.olvColumnMtFileName.Width = 80;
            // 
            // olvColumnTime
            // 
            this.olvColumnTime.HeaderFont = null;
            this.olvColumnTime.Text = "回落时间";
            this.olvColumnTime.Width = 120;
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            this.olvColumnLongitude.Width = 80;
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            this.olvColumnLatitude.Width = 80;
            // 
            // olvColumnLTECellName
            // 
            this.olvColumnLTECellName.HeaderFont = null;
            this.olvColumnLTECellName.Text = "回落前4G小区";
            this.olvColumnLTECellName.Width = 80;
            // 
            // olvColumnLTEECI
            // 
            this.olvColumnLTEECI.HeaderFont = null;
            this.olvColumnLTEECI.Text = "回落前4G小区ECI";
            // 
            // olvColumnENBID
            // 
            this.olvColumnENBID.HeaderFont = null;
            this.olvColumnENBID.Text = "ENBID-LOCALCELLID";
            // 
            // olvColumnLTETAC
            // 
            this.olvColumnLTETAC.HeaderFont = null;
            this.olvColumnLTETAC.Text = "回落前4G小区TAC";
            // 
            // olvColumnLTELongitude
            // 
            this.olvColumnLTELongitude.HeaderFont = null;
            this.olvColumnLTELongitude.Text = "回落前4G小区经度";
            // 
            // olvColumnLTELatitude
            // 
            this.olvColumnLTELatitude.HeaderFont = null;
            this.olvColumnLTELatitude.Text = "回落前4G小区纬度";
            // 
            // olvColumnLTEDistance
            // 
            this.olvColumnLTEDistance.HeaderFont = null;
            this.olvColumnLTEDistance.Text = "回落点与LTE基站距离";
            this.olvColumnLTEDistance.Width = 80;
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "回落小区";
            this.olvColumnCellName.Width = 120;
            // 
            // olvColumnCellID
            // 
            this.olvColumnCellID.HeaderFont = null;
            this.olvColumnCellID.Text = "回落小区CellID";
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "LAC";
            // 
            // olvColumnGSMLongitude
            // 
            this.olvColumnGSMLongitude.HeaderFont = null;
            this.olvColumnGSMLongitude.Text = "回落后GSM小区经度";
            this.olvColumnGSMLongitude.Width = 80;
            // 
            // olvColumnGSMLatitude
            // 
            this.olvColumnGSMLatitude.HeaderFont = null;
            this.olvColumnGSMLatitude.Text = "回落后GMS小区纬度";
            this.olvColumnGSMLatitude.Width = 80;
            // 
            // olvColumnGSMDistance
            // 
            this.olvColumnGSMDistance.HeaderFont = null;
            this.olvColumnGSMDistance.Text = "回落距离";
            this.olvColumnGSMDistance.Width = 80;
            // 
            // olvColumnGSMRxLev
            // 
            this.olvColumnGSMRxLev.HeaderFont = null;
            this.olvColumnGSMRxLev.Text = "回落后GSM小区电平";
            this.olvColumnGSMRxLev.Width = 80;
            // 
            // olvColumnMaxGSMDistance
            // 
            this.olvColumnMaxGSMDistance.HeaderFont = null;
            this.olvColumnMaxGSMDistance.Text = "最强GSM小区距离";
            this.olvColumnMaxGSMDistance.Width = 80;
            // 
            // olvColumnMaxGSMRxLev
            // 
            this.olvColumnMaxGSMRxLev.HeaderFont = null;
            this.olvColumnMaxGSMRxLev.Text = "最强GSM小区电平";
            this.olvColumnMaxGSMRxLev.Width = 80;
            // 
            // olvColumnIsInList
            // 
            this.olvColumnIsInList.HeaderFont = null;
            this.olvColumnIsInList.Text = "是否在前六强小区中";
            // 
            // olvColumnCell1
            // 
            this.olvColumnCell1.HeaderFont = null;
            this.olvColumnCell1.Text = "小区1";
            // 
            // olvColumnCellType1
            // 
            this.olvColumnCellType1.HeaderFont = null;
            this.olvColumnCellType1.Text = "小区1类别";
            // 
            // olvColumnCellID1
            // 
            this.olvColumnCellID1.HeaderFont = null;
            this.olvColumnCellID1.Text = "小区1CellID";
            // 
            // olvColumnRxLev1
            // 
            this.olvColumnRxLev1.HeaderFont = null;
            this.olvColumnRxLev1.Text = "小区1场强";
            // 
            // olvColumnPCI1
            // 
            this.olvColumnPCI1.HeaderFont = null;
            this.olvColumnPCI1.Text = "小区1频点";
            // 
            // olvColumnBSIC1
            // 
            this.olvColumnBSIC1.HeaderFont = null;
            this.olvColumnBSIC1.Text = "小区1BSIC";
            // 
            // olvColumnCell2
            // 
            this.olvColumnCell2.HeaderFont = null;
            this.olvColumnCell2.Text = "小区2";
            // 
            // olvColumnCellType2
            // 
            this.olvColumnCellType2.HeaderFont = null;
            this.olvColumnCellType2.Text = "小区2类别";
            // 
            // olvColumnCellID2
            // 
            this.olvColumnCellID2.HeaderFont = null;
            this.olvColumnCellID2.Text = "小区2CellID";
            // 
            // olvColumnRxLev2
            // 
            this.olvColumnRxLev2.HeaderFont = null;
            this.olvColumnRxLev2.Text = "小区2场强";
            // 
            // olvColumnPCI2
            // 
            this.olvColumnPCI2.HeaderFont = null;
            this.olvColumnPCI2.Text = "小区2频点";
            // 
            // olvColumnBSIC2
            // 
            this.olvColumnBSIC2.HeaderFont = null;
            this.olvColumnBSIC2.Text = "小区2BSIC";
            // 
            // olvColumnCell3
            // 
            this.olvColumnCell3.HeaderFont = null;
            this.olvColumnCell3.Text = "小区3";
            // 
            // olvColumnCellType3
            // 
            this.olvColumnCellType3.HeaderFont = null;
            this.olvColumnCellType3.Text = "小区3类别";
            // 
            // olvColumnCellID3
            // 
            this.olvColumnCellID3.HeaderFont = null;
            this.olvColumnCellID3.Text = "小区3CellID";
            // 
            // olvColumnRxLev3
            // 
            this.olvColumnRxLev3.HeaderFont = null;
            this.olvColumnRxLev3.Text = "小区3场强";
            // 
            // olvColumnPCI3
            // 
            this.olvColumnPCI3.HeaderFont = null;
            this.olvColumnPCI3.Text = "小区3频点";
            // 
            // olvColumnBSIC3
            // 
            this.olvColumnBSIC3.HeaderFont = null;
            this.olvColumnBSIC3.Text = "小区3BSIC";
            // 
            // olvColumnCell4
            // 
            this.olvColumnCell4.HeaderFont = null;
            this.olvColumnCell4.Text = "小区4";
            // 
            // olvColumnCellType4
            // 
            this.olvColumnCellType4.HeaderFont = null;
            this.olvColumnCellType4.Text = "小区4类别";
            // 
            // olvColumnCellID4
            // 
            this.olvColumnCellID4.HeaderFont = null;
            this.olvColumnCellID4.Text = "小区4CellID";
            // 
            // olvColumnRxLev4
            // 
            this.olvColumnRxLev4.HeaderFont = null;
            this.olvColumnRxLev4.Text = "小区4场强";
            // 
            // olvColumnPCI4
            // 
            this.olvColumnPCI4.HeaderFont = null;
            this.olvColumnPCI4.Text = "小区4频点";
            // 
            // olvColumnBSIC4
            // 
            this.olvColumnBSIC4.HeaderFont = null;
            this.olvColumnBSIC4.Text = "小区4BSIC";
            // 
            // olvColumnCell5
            // 
            this.olvColumnCell5.HeaderFont = null;
            this.olvColumnCell5.Text = "小区5";
            // 
            // olvColumnCellType5
            // 
            this.olvColumnCellType5.HeaderFont = null;
            this.olvColumnCellType5.Text = "小区5类别";
            // 
            // olvColumnCellID5
            // 
            this.olvColumnCellID5.HeaderFont = null;
            this.olvColumnCellID5.Text = "小区5CellID";
            // 
            // olvColumnRxLev5
            // 
            this.olvColumnRxLev5.HeaderFont = null;
            this.olvColumnRxLev5.Text = "小区5场强";
            // 
            // olvColumnPCI5
            // 
            this.olvColumnPCI5.HeaderFont = null;
            this.olvColumnPCI5.Text = "小区5频点";
            // 
            // olvColumnBSIC5
            // 
            this.olvColumnBSIC5.HeaderFont = null;
            this.olvColumnBSIC5.Text = "小区5BSIC";
            // 
            // olvColumnCell6
            // 
            this.olvColumnCell6.HeaderFont = null;
            this.olvColumnCell6.Text = "小区6";
            // 
            // olvColumnCellType6
            // 
            this.olvColumnCellType6.HeaderFont = null;
            this.olvColumnCellType6.Text = "小区6类别";
            // 
            // olvColumnCellID6
            // 
            this.olvColumnCellID6.HeaderFont = null;
            this.olvColumnCellID6.Text = "小区6CellID";
            // 
            // olvColumnRxLev6
            // 
            this.olvColumnRxLev6.HeaderFont = null;
            this.olvColumnRxLev6.Text = "小区6场强";
            // 
            // olvColumnPCI6
            // 
            this.olvColumnPCI6.HeaderFont = null;
            this.olvColumnPCI6.Text = "小区6频点";
            // 
            // olvColumnBSIC6
            // 
            this.olvColumnBSIC6.HeaderFont = null;
            this.olvColumnBSIC6.Text = "小区6BSIC";
            // 
            // olvColumnCell7
            // 
            this.olvColumnCell7.HeaderFont = null;
            this.olvColumnCell7.Text = "小区7";
            // 
            // olvColumnCellType7
            // 
            this.olvColumnCellType7.HeaderFont = null;
            this.olvColumnCellType7.Text = "小区7类别";
            // 
            // olvColumnCellID7
            // 
            this.olvColumnCellID7.HeaderFont = null;
            this.olvColumnCellID7.Text = "小区7CellID";
            // 
            // olvColumnRxLev7
            // 
            this.olvColumnRxLev7.HeaderFont = null;
            this.olvColumnRxLev7.Text = "小区7场强";
            // 
            // olvColumnPCI7
            // 
            this.olvColumnPCI7.HeaderFont = null;
            this.olvColumnPCI7.Text = "小区7频点";
            // 
            // olvColumnBSIC7
            // 
            this.olvColumnBSIC7.HeaderFont = null;
            this.olvColumnBSIC7.Text = "小区7BSIC";
            // 
            // olvColumnCell8
            // 
            this.olvColumnCell8.HeaderFont = null;
            this.olvColumnCell8.Text = "小区8";
            // 
            // olvColumnCellType8
            // 
            this.olvColumnCellType8.HeaderFont = null;
            this.olvColumnCellType8.Text = "小区8类别";
            // 
            // olvColumnCellID8
            // 
            this.olvColumnCellID8.HeaderFont = null;
            this.olvColumnCellID8.Text = "小区8CellID";
            // 
            // olvColumnRxLev8
            // 
            this.olvColumnRxLev8.HeaderFont = null;
            this.olvColumnRxLev8.Text = "小区8场强";
            // 
            // olvColumnPCI8
            // 
            this.olvColumnPCI8.HeaderFont = null;
            this.olvColumnPCI8.Text = "小区8频点";
            // 
            // olvColumnBSIC8
            // 
            this.olvColumnBSIC8.HeaderFont = null;
            this.olvColumnBSIC8.Text = "小区8BSIC";
            // 
            // olvColumnCell9
            // 
            this.olvColumnCell9.HeaderFont = null;
            this.olvColumnCell9.Text = "小区9";
            // 
            // olvColumnCellType9
            // 
            this.olvColumnCellType9.HeaderFont = null;
            this.olvColumnCellType9.Text = "小区9类别";
            // 
            // olvColumnCellID9
            // 
            this.olvColumnCellID9.HeaderFont = null;
            this.olvColumnCellID9.Text = "小区9CellID";
            // 
            // olvColumnRxLev9
            // 
            this.olvColumnRxLev9.HeaderFont = null;
            this.olvColumnRxLev9.Text = "小区9场强";
            // 
            // olvColumnPCI9
            // 
            this.olvColumnPCI9.HeaderFont = null;
            this.olvColumnPCI9.Text = "小区9频点";
            // 
            // olvColumnBSIC9
            // 
            this.olvColumnBSIC9.HeaderFont = null;
            this.olvColumnBSIC9.Text = "小区9BSIC";
            // 
            // olvColumnCell10
            // 
            this.olvColumnCell10.HeaderFont = null;
            this.olvColumnCell10.Text = "小区10";
            // 
            // olvColumnCellType10
            // 
            this.olvColumnCellType10.HeaderFont = null;
            this.olvColumnCellType10.Text = "小区10类别";
            // 
            // olvColumnCellID10
            // 
            this.olvColumnCellID10.HeaderFont = null;
            this.olvColumnCellID10.Text = "小区10CellID";
            // 
            // olvColumnRxLev10
            // 
            this.olvColumnRxLev10.HeaderFont = null;
            this.olvColumnRxLev10.Text = "小区10场强";
            // 
            // olvColumnPCI10
            // 
            this.olvColumnPCI10.HeaderFont = null;
            this.olvColumnPCI10.Text = "小区10频点";
            // 
            // olvColumnBSIC10
            // 
            this.olvColumnBSIC10.HeaderFont = null;
            this.olvColumnBSIC10.Text = "小区10BSIC";
            // 
            // olvColumnCell11
            // 
            this.olvColumnCell11.HeaderFont = null;
            this.olvColumnCell11.Text = "小区11";
            // 
            // olvColumnCellType11
            // 
            this.olvColumnCellType11.HeaderFont = null;
            this.olvColumnCellType11.Text = "小区11类别";
            // 
            // olvColumnCellID11
            // 
            this.olvColumnCellID11.HeaderFont = null;
            this.olvColumnCellID11.Text = "小区11CellID";
            // 
            // olvColumnRxLev11
            // 
            this.olvColumnRxLev11.HeaderFont = null;
            this.olvColumnRxLev11.Text = "小区11场强";
            // 
            // olvColumnPCI11
            // 
            this.olvColumnPCI11.HeaderFont = null;
            this.olvColumnPCI11.Text = "小区11频点";
            // 
            // olvColumnBSIC11
            // 
            this.olvColumnBSIC11.HeaderFont = null;
            this.olvColumnBSIC11.Text = "小区11BSIC";
            // 
            // olvColumnCell12
            // 
            this.olvColumnCell12.HeaderFont = null;
            this.olvColumnCell12.Text = "小区12";
            // 
            // olvColumnCellType12
            // 
            this.olvColumnCellType12.HeaderFont = null;
            this.olvColumnCellType12.Text = "小区12类别";
            // 
            // olvColumnCellID12
            // 
            this.olvColumnCellID12.HeaderFont = null;
            this.olvColumnCellID12.Text = "小区12CellID";
            // 
            // olvColumnRxLev12
            // 
            this.olvColumnRxLev12.HeaderFont = null;
            this.olvColumnRxLev12.Text = "小区12场强";
            // 
            // olvColumnPCI12
            // 
            this.olvColumnPCI12.HeaderFont = null;
            this.olvColumnPCI12.Text = "小区12频点";
            // 
            // olvColumnBSIC12
            // 
            this.olvColumnBSIC12.HeaderFont = null;
            this.olvColumnBSIC12.Text = "小区12BSIC";
            // 
            // olvColumnCell13
            // 
            this.olvColumnCell13.HeaderFont = null;
            this.olvColumnCell13.Text = "小区13";
            // 
            // olvColumnCellType13
            // 
            this.olvColumnCellType13.HeaderFont = null;
            this.olvColumnCellType13.Text = "小区13类别";
            // 
            // olvColumnCellID13
            // 
            this.olvColumnCellID13.HeaderFont = null;
            this.olvColumnCellID13.Text = "小区13CellID";
            // 
            // olvColumnRxLev13
            // 
            this.olvColumnRxLev13.HeaderFont = null;
            this.olvColumnRxLev13.Text = "小区13场强";
            // 
            // olvColumnPCI13
            // 
            this.olvColumnPCI13.HeaderFont = null;
            this.olvColumnPCI13.Text = "小区13频点";
            // 
            // olvColumnBSIC13
            // 
            this.olvColumnBSIC13.HeaderFont = null;
            this.olvColumnBSIC13.Text = "小区13BSIC";
            // 
            // olvColumnCell14
            // 
            this.olvColumnCell14.HeaderFont = null;
            this.olvColumnCell14.Text = "小区14";
            // 
            // olvColumnCellType14
            // 
            this.olvColumnCellType14.HeaderFont = null;
            this.olvColumnCellType14.Text = "小区14类别";
            // 
            // olvColumnCellID14
            // 
            this.olvColumnCellID14.HeaderFont = null;
            this.olvColumnCellID14.Text = "小区14CellID";
            // 
            // olvColumnRxLev14
            // 
            this.olvColumnRxLev14.HeaderFont = null;
            this.olvColumnRxLev14.Text = "小区14场强";
            // 
            // olvColumnPCI14
            // 
            this.olvColumnPCI14.HeaderFont = null;
            this.olvColumnPCI14.Text = "小区14频点";
            // 
            // olvColumnBSIC14
            // 
            this.olvColumnBSIC14.HeaderFont = null;
            this.olvColumnBSIC14.Text = "小区14BSIC";
            // 
            // olvColumnCell15
            // 
            this.olvColumnCell15.HeaderFont = null;
            this.olvColumnCell15.Text = "小区15";
            // 
            // olvColumnCellType15
            // 
            this.olvColumnCellType15.HeaderFont = null;
            this.olvColumnCellType15.Text = "小区15类别";
            // 
            // olvColumnCellID15
            // 
            this.olvColumnCellID15.HeaderFont = null;
            this.olvColumnCellID15.Text = "小区15CellID";
            // 
            // olvColumnRxLev15
            // 
            this.olvColumnRxLev15.HeaderFont = null;
            this.olvColumnRxLev15.Text = "小区15场强";
            // 
            // olvColumnPCI15
            // 
            this.olvColumnPCI15.HeaderFont = null;
            this.olvColumnPCI15.Text = "小区15频点";
            // 
            // olvColumnBSIC15
            // 
            this.olvColumnBSIC15.HeaderFont = null;
            this.olvColumnBSIC15.Text = "小区15BSIC";
            // 
            // olvColumnCell16
            // 
            this.olvColumnCell16.HeaderFont = null;
            this.olvColumnCell16.Text = "小区16";
            // 
            // olvColumnCellType16
            // 
            this.olvColumnCellType16.HeaderFont = null;
            this.olvColumnCellType16.Text = "小区16类别";
            // 
            // olvColumnCellID16
            // 
            this.olvColumnCellID16.HeaderFont = null;
            this.olvColumnCellID16.Text = "小区16CellID";
            // 
            // olvColumnRxLev16
            // 
            this.olvColumnRxLev16.HeaderFont = null;
            this.olvColumnRxLev16.Text = "小区16场强";
            // 
            // olvColumnPCI16
            // 
            this.olvColumnPCI16.HeaderFont = null;
            this.olvColumnPCI16.Text = "小区16频点";
            // 
            // olvColumnBSIC16
            // 
            this.olvColumnBSIC16.HeaderFont = null;
            this.olvColumnBSIC16.Text = "小区16BSIC";
            // 
            // olvColumnCell17
            // 
            this.olvColumnCell17.HeaderFont = null;
            this.olvColumnCell17.Text = "小区17";
            // 
            // olvColumnCellType17
            // 
            this.olvColumnCellType17.HeaderFont = null;
            this.olvColumnCellType17.Text = "小区17类别";
            // 
            // olvColumnCellID17
            // 
            this.olvColumnCellID17.HeaderFont = null;
            this.olvColumnCellID17.Text = "小区17CellID";
            // 
            // olvColumnRxLev17
            // 
            this.olvColumnRxLev17.HeaderFont = null;
            this.olvColumnRxLev17.Text = "小区17场强";
            // 
            // olvColumnPCI17
            // 
            this.olvColumnPCI17.HeaderFont = null;
            this.olvColumnPCI17.Text = "小区17频点";
            // 
            // olvColumnBSIC17
            // 
            this.olvColumnBSIC17.HeaderFont = null;
            this.olvColumnBSIC17.Text = "小区17BSIC";
            // 
            // olvColumnCell18
            // 
            this.olvColumnCell18.HeaderFont = null;
            this.olvColumnCell18.Text = "小区18";
            // 
            // olvColumnCellType18
            // 
            this.olvColumnCellType18.HeaderFont = null;
            this.olvColumnCellType18.Text = "小区18类别";
            // 
            // olvColumnCellID18
            // 
            this.olvColumnCellID18.HeaderFont = null;
            this.olvColumnCellID18.Text = "小区18CellID";
            // 
            // olvColumnRxLev18
            // 
            this.olvColumnRxLev18.HeaderFont = null;
            this.olvColumnRxLev18.Text = "小区18场强";
            // 
            // olvColumnPCI18
            // 
            this.olvColumnPCI18.HeaderFont = null;
            this.olvColumnPCI18.Text = "小区18频点";
            // 
            // olvColumnBSIC18
            // 
            this.olvColumnBSIC18.HeaderFont = null;
            this.olvColumnBSIC18.Text = "小区18BSIC";
            // 
            // olvColumnCell19
            // 
            this.olvColumnCell19.HeaderFont = null;
            this.olvColumnCell19.Text = "小区19";
            // 
            // olvColumnCellType19
            // 
            this.olvColumnCellType19.HeaderFont = null;
            this.olvColumnCellType19.Text = "小区19类别";
            // 
            // olvColumnCellID19
            // 
            this.olvColumnCellID19.HeaderFont = null;
            this.olvColumnCellID19.Text = "小区19CellID";
            // 
            // olvColumnRxLev19
            // 
            this.olvColumnRxLev19.HeaderFont = null;
            this.olvColumnRxLev19.Text = "小区19场强";
            // 
            // olvColumnPCI19
            // 
            this.olvColumnPCI19.HeaderFont = null;
            this.olvColumnPCI19.Text = "小区19频点";
            // 
            // olvColumnBSIC19
            // 
            this.olvColumnBSIC19.HeaderFont = null;
            this.olvColumnBSIC19.Text = "小区19BSIC";
            // 
            // olvColumnCell20
            // 
            this.olvColumnCell20.HeaderFont = null;
            this.olvColumnCell20.Text = "小区20";
            // 
            // olvColumnCellType20
            // 
            this.olvColumnCellType20.HeaderFont = null;
            this.olvColumnCellType20.Text = "小区20类别";
            // 
            // olvColumnCellID20
            // 
            this.olvColumnCellID20.HeaderFont = null;
            this.olvColumnCellID20.Text = "小区20CellID";
            // 
            // olvColumnRxLev20
            // 
            this.olvColumnRxLev20.HeaderFont = null;
            this.olvColumnRxLev20.Text = "小区20场强";
            // 
            // olvColumnPCI20
            // 
            this.olvColumnPCI20.HeaderFont = null;
            this.olvColumnPCI20.Text = "小区20PCI";
            // 
            // olvColumnBSIC20
            // 
            this.olvColumnBSIC20.HeaderFont = null;
            this.olvColumnBSIC20.Text = "小区20BSIC";
            // 
            // olvColumnCell21
            // 
            this.olvColumnCell21.HeaderFont = null;
            this.olvColumnCell21.Text = "小区21";
            // 
            // olvColumnCellType21
            // 
            this.olvColumnCellType21.HeaderFont = null;
            this.olvColumnCellType21.Text = "小区21类别";
            // 
            // olvColumnCellID21
            // 
            this.olvColumnCellID21.HeaderFont = null;
            this.olvColumnCellID21.Text = "小区21CellID";
            // 
            // olvColumnRxLev21
            // 
            this.olvColumnRxLev21.HeaderFont = null;
            this.olvColumnRxLev21.Text = "小区21场强";
            // 
            // olvColumnPCI21
            // 
            this.olvColumnPCI21.HeaderFont = null;
            this.olvColumnPCI21.Text = "小区21频点";
            // 
            // olvColumnBSIC21
            // 
            this.olvColumnBSIC21.HeaderFont = null;
            this.olvColumnBSIC21.Text = "小区21BSIC";
            // 
            // olvColumnCell22
            // 
            this.olvColumnCell22.HeaderFont = null;
            this.olvColumnCell22.Text = "小区22";
            // 
            // olvColumnCellType22
            // 
            this.olvColumnCellType22.HeaderFont = null;
            this.olvColumnCellType22.Text = "小区22类别";
            // 
            // olvColumnCellID22
            // 
            this.olvColumnCellID22.HeaderFont = null;
            this.olvColumnCellID22.Text = "小区22CellID";
            // 
            // olvColumnRxLev22
            // 
            this.olvColumnRxLev22.HeaderFont = null;
            this.olvColumnRxLev22.Text = "小区22场强";
            // 
            // olvColumnPCI22
            // 
            this.olvColumnPCI22.HeaderFont = null;
            this.olvColumnPCI22.Text = "小区22频点";
            // 
            // olvColumnBSIC22
            // 
            this.olvColumnBSIC22.HeaderFont = null;
            this.olvColumnBSIC22.Text = "小区22BSIC";
            // 
            // olvColumnCell23
            // 
            this.olvColumnCell23.HeaderFont = null;
            this.olvColumnCell23.Text = "小区23";
            // 
            // olvColumnCellType23
            // 
            this.olvColumnCellType23.HeaderFont = null;
            this.olvColumnCellType23.Text = "小区23类别";
            // 
            // olvColumnCellID23
            // 
            this.olvColumnCellID23.HeaderFont = null;
            this.olvColumnCellID23.Text = "小区23CellID";
            // 
            // olvColumnRxLev23
            // 
            this.olvColumnRxLev23.HeaderFont = null;
            this.olvColumnRxLev23.Text = "小区23场强";
            // 
            // olvColumnPCI23
            // 
            this.olvColumnPCI23.HeaderFont = null;
            this.olvColumnPCI23.Text = "小区23频点";
            // 
            // olvColumnBSIC23
            // 
            this.olvColumnBSIC23.HeaderFont = null;
            this.olvColumnBSIC23.Text = "小区23BSIC";
            // 
            // olvColumnCell24
            // 
            this.olvColumnCell24.HeaderFont = null;
            this.olvColumnCell24.Text = "小区24";
            // 
            // olvColumnCellType24
            // 
            this.olvColumnCellType24.HeaderFont = null;
            this.olvColumnCellType24.Text = "小区24类别";
            // 
            // olvColumnCellID24
            // 
            this.olvColumnCellID24.HeaderFont = null;
            this.olvColumnCellID24.Text = "小区24CellID";
            // 
            // olvColumnRxLev24
            // 
            this.olvColumnRxLev24.HeaderFont = null;
            this.olvColumnRxLev24.Text = "小区24场强";
            // 
            // olvColumnPCI24
            // 
            this.olvColumnPCI24.HeaderFont = null;
            this.olvColumnPCI24.Text = "小区24频点";
            // 
            // olvColumnBSIC24
            // 
            this.olvColumnBSIC24.HeaderFont = null;
            this.olvColumnBSIC24.Text = "小区24BSIC";
            // 
            // olvColumnCell25
            // 
            this.olvColumnCell25.HeaderFont = null;
            this.olvColumnCell25.Text = "小区25";
            // 
            // olvColumnCellType25
            // 
            this.olvColumnCellType25.HeaderFont = null;
            this.olvColumnCellType25.Text = "小区25类别";
            // 
            // olvColumnCellID25
            // 
            this.olvColumnCellID25.HeaderFont = null;
            this.olvColumnCellID25.Text = "小区25CellID";
            // 
            // olvColumnRxLev25
            // 
            this.olvColumnRxLev25.HeaderFont = null;
            this.olvColumnRxLev25.Text = "小区25场强";
            // 
            // olvColumnPCI25
            // 
            this.olvColumnPCI25.HeaderFont = null;
            this.olvColumnPCI25.Text = "小区25频点";
            // 
            // olvColumnBSIC25
            // 
            this.olvColumnBSIC25.HeaderFont = null;
            this.olvColumnBSIC25.Text = "小区25BSIC";
            // 
            // olvColumnCell26
            // 
            this.olvColumnCell26.HeaderFont = null;
            this.olvColumnCell26.Text = "小区26";
            // 
            // olvColumnCellType26
            // 
            this.olvColumnCellType26.HeaderFont = null;
            this.olvColumnCellType26.Text = "小区26类别";
            // 
            // olvColumnCellID26
            // 
            this.olvColumnCellID26.HeaderFont = null;
            this.olvColumnCellID26.Text = "小区26CellID";
            // 
            // olvColumnRxLev26
            // 
            this.olvColumnRxLev26.HeaderFont = null;
            this.olvColumnRxLev26.Text = "小区26场强";
            // 
            // olvColumnPCI26
            // 
            this.olvColumnPCI26.HeaderFont = null;
            this.olvColumnPCI26.Text = "小区26PCI";
            // 
            // olvColumnBSIC26
            // 
            this.olvColumnBSIC26.HeaderFont = null;
            this.olvColumnBSIC26.Text = "小区26BSIC";
            // 
            // olvColumnCell27
            // 
            this.olvColumnCell27.HeaderFont = null;
            this.olvColumnCell27.Text = "小区27";
            // 
            // olvColumnCellType27
            // 
            this.olvColumnCellType27.HeaderFont = null;
            this.olvColumnCellType27.Text = "小区27类别";
            // 
            // olvColumnCellID27
            // 
            this.olvColumnCellID27.HeaderFont = null;
            this.olvColumnCellID27.Text = "小区27CellID";
            // 
            // olvColumnRxLev27
            // 
            this.olvColumnRxLev27.HeaderFont = null;
            this.olvColumnRxLev27.Text = "小区27场强";
            // 
            // olvColumnPCI27
            // 
            this.olvColumnPCI27.HeaderFont = null;
            this.olvColumnPCI27.Text = "小区27频点";
            // 
            // olvColumnBSIC27
            // 
            this.olvColumnBSIC27.HeaderFont = null;
            this.olvColumnBSIC27.Text = "小区27BSIC";
            // 
            // olvColumnCell28
            // 
            this.olvColumnCell28.HeaderFont = null;
            this.olvColumnCell28.Text = "小区28";
            // 
            // olvColumnCellType28
            // 
            this.olvColumnCellType28.HeaderFont = null;
            this.olvColumnCellType28.Text = "小区28类别";
            // 
            // olvColumnCellID28
            // 
            this.olvColumnCellID28.HeaderFont = null;
            this.olvColumnCellID28.Text = "小区28CellID";
            // 
            // olvColumnRxLev28
            // 
            this.olvColumnRxLev28.HeaderFont = null;
            this.olvColumnRxLev28.Text = "小区28场强";
            // 
            // olvColumnPCI28
            // 
            this.olvColumnPCI28.HeaderFont = null;
            this.olvColumnPCI28.Text = "小区28频点";
            // 
            // olvColumnBSIC28
            // 
            this.olvColumnBSIC28.HeaderFont = null;
            this.olvColumnBSIC28.Text = "小区28BSIC";
            // 
            // olvColumnCell29
            // 
            this.olvColumnCell29.HeaderFont = null;
            this.olvColumnCell29.Text = "小区29";
            // 
            // olvColumnCellType29
            // 
            this.olvColumnCellType29.HeaderFont = null;
            this.olvColumnCellType29.Text = "小区29类别";
            // 
            // olvColumnCellID29
            // 
            this.olvColumnCellID29.HeaderFont = null;
            this.olvColumnCellID29.Text = "小区29CellID";
            // 
            // olvColumnRxLev29
            // 
            this.olvColumnRxLev29.HeaderFont = null;
            this.olvColumnRxLev29.Text = "小区29场强";
            // 
            // olvColumnPCI29
            // 
            this.olvColumnPCI29.HeaderFont = null;
            this.olvColumnPCI29.Text = "小区29PCI";
            // 
            // olvColumnBSIC29
            // 
            this.olvColumnBSIC29.HeaderFont = null;
            this.olvColumnBSIC29.Text = "小区29BSIC";
            // 
            // olvColumnCell30
            // 
            this.olvColumnCell30.HeaderFont = null;
            this.olvColumnCell30.Text = "小区30";
            // 
            // olvColumnCellType30
            // 
            this.olvColumnCellType30.HeaderFont = null;
            this.olvColumnCellType30.Text = "小区30类别";
            // 
            // olvColumnCellID30
            // 
            this.olvColumnCellID30.HeaderFont = null;
            this.olvColumnCellID30.Text = "小区30CellID";
            // 
            // olvColumnRxLev30
            // 
            this.olvColumnRxLev30.HeaderFont = null;
            this.olvColumnRxLev30.Text = "小区30场强";
            // 
            // olvColumnPCI30
            // 
            this.olvColumnPCI30.HeaderFont = null;
            this.olvColumnPCI30.Text = "小区30频点";
            // 
            // olvColumnBSIC30
            // 
            this.olvColumnBSIC30.HeaderFont = null;
            this.olvColumnBSIC30.Text = "小区30BSIC";
            // 
            // olvColumnCell31
            // 
            this.olvColumnCell31.HeaderFont = null;
            this.olvColumnCell31.Text = "小区31";
            // 
            // olvColumnCellType31
            // 
            this.olvColumnCellType31.HeaderFont = null;
            this.olvColumnCellType31.Text = "小区31类别";
            // 
            // olvColumnCellID31
            // 
            this.olvColumnCellID31.HeaderFont = null;
            this.olvColumnCellID31.Text = "小区31CellID";
            // 
            // olvColumnRxLev31
            // 
            this.olvColumnRxLev31.HeaderFont = null;
            this.olvColumnRxLev31.Text = "小区31场强";
            // 
            // olvColumnPCI31
            // 
            this.olvColumnPCI31.HeaderFont = null;
            this.olvColumnPCI31.Text = "小区31频点";
            // 
            // olvColumnBSIC31
            // 
            this.olvColumnBSIC31.HeaderFont = null;
            this.olvColumnBSIC31.Text = "小区31BSIC";
            // 
            // olvColumnCell32
            // 
            this.olvColumnCell32.HeaderFont = null;
            this.olvColumnCell32.Text = "小区32";
            // 
            // olvColumnCellType32
            // 
            this.olvColumnCellType32.HeaderFont = null;
            this.olvColumnCellType32.Text = "小区32类别";
            // 
            // olvColumnCellID32
            // 
            this.olvColumnCellID32.HeaderFont = null;
            this.olvColumnCellID32.Text = "小区32CellID";
            // 
            // olvColumnRxLev32
            // 
            this.olvColumnRxLev32.HeaderFont = null;
            this.olvColumnRxLev32.Text = "小区32场强";
            // 
            // olvColumnPCI32
            // 
            this.olvColumnPCI32.HeaderFont = null;
            this.olvColumnPCI32.Text = "小区32频点";
            // 
            // olvColumnBSIC32
            // 
            this.olvColumnBSIC32.HeaderFont = null;
            this.olvColumnBSIC32.Text = "小区32BSIC";
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(130, 26);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // ZTCsfbCellJudgeListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1197, 509);
            this.Controls.Add(this.ListViewCsfbCellJudge);
            this.Name = "ZTCsfbCellJudgeListForm";
            this.Text = "CSFB回落非最佳小区";
            ((System.ComponentModel.ISupportInitialize)(this.ListViewCsfbCellJudge)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView ListViewCsfbCellJudge;
        private BrightIdeasSoftware.OLVColumn olvColumnStatSN;
        private BrightIdeasSoftware.OLVColumn olvColumnTime;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnGSMLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnGSMLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnGSMDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnLTEDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxGSMDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnMaxGSMRxLev;
        private BrightIdeasSoftware.OLVColumn olvColumnGSMRxLev;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnMoFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnMtFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnCell1;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev1;
        private BrightIdeasSoftware.OLVColumn olvColumnLTECellName;
        private BrightIdeasSoftware.OLVColumn olvColumnCell2;
        private BrightIdeasSoftware.OLVColumn olvColumnCell3;
        private BrightIdeasSoftware.OLVColumn olvColumnCell4;
        private BrightIdeasSoftware.OLVColumn olvColumnCell5;
        private BrightIdeasSoftware.OLVColumn olvColumnCell6;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev2;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev3;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev4;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev5;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev6;
        private BrightIdeasSoftware.OLVColumn olvColumnIsInList;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI1;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI2;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI3;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI4;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI5;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI6;
        private BrightIdeasSoftware.OLVColumn olvColumnLTEECI;
        private BrightIdeasSoftware.OLVColumn olvColumnENBID;
        private BrightIdeasSoftware.OLVColumn olvColumnLTETAC;
        private BrightIdeasSoftware.OLVColumn olvColumnLTELongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLTELatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC1;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC2;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC3;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC4;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC5;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC6;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID1;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID2;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID3;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID4;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID5;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID6;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType1;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType2;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType3;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType4;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType5;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType6;
        private BrightIdeasSoftware.OLVColumn olvColumnCell7;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType7;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID7;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev7;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI7;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC7;
        private BrightIdeasSoftware.OLVColumn olvColumnCell8;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID8;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev8;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI8;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC8;
        private BrightIdeasSoftware.OLVColumn olvColumnCell9;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType9;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID9;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev9;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI9;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC9;
        private BrightIdeasSoftware.OLVColumn olvColumnCell10;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType10;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID10;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev10;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI10;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC10;
        private BrightIdeasSoftware.OLVColumn olvColumnCell11;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType11;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID11;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev11;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI11;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC11;
        private BrightIdeasSoftware.OLVColumn olvColumnCell12;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType12;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID12;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev12;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI12;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC12;
        private BrightIdeasSoftware.OLVColumn olvColumnCell13;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType13;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID13;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev13;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI13;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC13;
        private BrightIdeasSoftware.OLVColumn olvColumnCell14;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType14;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID14;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev14;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI14;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC14;
        private BrightIdeasSoftware.OLVColumn olvColumnCell15;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType15;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID15;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev15;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI15;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC15;
        private BrightIdeasSoftware.OLVColumn olvColumnCell16;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType16;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID16;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev16;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI16;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC16;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType8;
        private BrightIdeasSoftware.OLVColumn olvColumnCell17;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType17;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID17;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev17;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI17;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC17;
        private BrightIdeasSoftware.OLVColumn olvColumnCell18;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType18;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID18;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev18;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI18;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC18;
        private BrightIdeasSoftware.OLVColumn olvColumnCell19;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType19;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID19;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev19;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI19;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC19;
        private BrightIdeasSoftware.OLVColumn olvColumnCell20;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType20;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID20;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev20;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI20;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC20;
        private BrightIdeasSoftware.OLVColumn olvColumnCell21;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType21;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID21;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev21;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI21;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC21;
        private BrightIdeasSoftware.OLVColumn olvColumnCell22;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType22;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID22;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev22;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI22;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC22;
        private BrightIdeasSoftware.OLVColumn olvColumnCell23;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType23;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID23;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev23;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI23;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC23;
        private BrightIdeasSoftware.OLVColumn olvColumnCell24;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType24;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID24;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev24;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI24;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC24;
        private BrightIdeasSoftware.OLVColumn olvColumnCell25;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType25;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID25;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev25;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI25;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC25;
        private BrightIdeasSoftware.OLVColumn olvColumnCell26;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType26;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID26;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev26;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI26;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC26;
        private BrightIdeasSoftware.OLVColumn olvColumnCell27;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType27;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID27;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev27;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI27;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC27;
        private BrightIdeasSoftware.OLVColumn olvColumnCell28;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType28;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID28;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev28;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI28;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC28;
        private BrightIdeasSoftware.OLVColumn olvColumnCell29;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType29;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID29;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev29;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI29;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC29;
        private BrightIdeasSoftware.OLVColumn olvColumnCell30;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType30;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID30;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev30;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI30;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC30;
        private BrightIdeasSoftware.OLVColumn olvColumnCell31;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType31;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID31;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev31;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI31;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC31;
        private BrightIdeasSoftware.OLVColumn olvColumnCell32;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType32;
        private BrightIdeasSoftware.OLVColumn olvColumnCellID32;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev32;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI32;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC32;
    }
}