﻿namespace MasterCom.RAMS.Func.EventSearchForm
{
    partial class SearchInfoForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.searchText_tb = new System.Windows.Forms.TextBox();
            this.peText_b = new System.Windows.Forms.Button();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.curCol_cb = new System.Windows.Forms.CheckBox();
            this.relation_cb = new System.Windows.Forms.CheckBox();
            this.matchPE_cb = new System.Windows.Forms.CheckBox();
            this.matchAll_cb = new System.Windows.Forms.CheckBox();
            this.matchUp_cb = new System.Windows.Forms.CheckBox();
            this.matchCase_cb = new System.Windows.Forms.CheckBox();
            this.serchNext_b = new System.Windows.Forms.Button();
            this.exit_b = new System.Windows.Forms.Button();
            this.peText_cms = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.panel1 = new System.Windows.Forms.Panel();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.panel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.searchText_tb);
            this.groupBox1.Controls.Add(this.peText_b);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(328, 42);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "查找内容";
            // 
            // searchText_tb
            // 
            this.searchText_tb.Dock = System.Windows.Forms.DockStyle.Fill;
            this.searchText_tb.Location = new System.Drawing.Point(3, 17);
            this.searchText_tb.Name = "searchText_tb";
            this.searchText_tb.Size = new System.Drawing.Size(297, 21);
            this.searchText_tb.TabIndex = 0;
            this.searchText_tb.TextChanged += new System.EventHandler(this.searchText_tb_TextChanged);
            // 
            // peText_b
            // 
            this.peText_b.Dock = System.Windows.Forms.DockStyle.Right;
            this.peText_b.Location = new System.Drawing.Point(300, 17);
            this.peText_b.Margin = new System.Windows.Forms.Padding(0);
            this.peText_b.Name = "peText_b";
            this.peText_b.Size = new System.Drawing.Size(25, 22);
            this.peText_b.TabIndex = 1;
            this.peText_b.Text = ">";
            this.peText_b.UseVisualStyleBackColor = true;
            this.peText_b.Click += new System.EventHandler(this.peText_b_Click);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.curCol_cb);
            this.groupBox2.Controls.Add(this.relation_cb);
            this.groupBox2.Controls.Add(this.matchPE_cb);
            this.groupBox2.Controls.Add(this.matchAll_cb);
            this.groupBox2.Controls.Add(this.matchUp_cb);
            this.groupBox2.Controls.Add(this.matchCase_cb);
            this.groupBox2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox2.Location = new System.Drawing.Point(0, 42);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(328, 66);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "查找选项";
            // 
            // curCol_cb
            // 
            this.curCol_cb.AutoSize = true;
            this.curCol_cb.Location = new System.Drawing.Point(244, 44);
            this.curCol_cb.Name = "curCol_cb";
            this.curCol_cb.Size = new System.Drawing.Size(60, 16);
            this.curCol_cb.TabIndex = 5;
            this.curCol_cb.Text = "当前列";
            this.curCol_cb.UseVisualStyleBackColor = true;
            this.curCol_cb.Visible = false;
            // 
            // relation_cb
            // 
            this.relation_cb.AutoSize = true;
            this.relation_cb.Location = new System.Drawing.Point(126, 44);
            this.relation_cb.Name = "relation_cb";
            this.relation_cb.Size = new System.Drawing.Size(108, 16);
            this.relation_cb.TabIndex = 4;
            this.relation_cb.Text = "关系表达式匹配";
            this.relation_cb.UseVisualStyleBackColor = true;
            this.relation_cb.Visible = false;
            // 
            // matchPE_cb
            // 
            this.matchPE_cb.AutoSize = true;
            this.matchPE_cb.Location = new System.Drawing.Point(12, 44);
            this.matchPE_cb.Name = "matchPE_cb";
            this.matchPE_cb.Size = new System.Drawing.Size(108, 16);
            this.matchPE_cb.TabIndex = 3;
            this.matchPE_cb.Text = "正则表达式匹配";
            this.matchPE_cb.UseVisualStyleBackColor = true;
            // 
            // matchAll_cb
            // 
            this.matchAll_cb.AutoSize = true;
            this.matchAll_cb.Location = new System.Drawing.Point(244, 20);
            this.matchAll_cb.Name = "matchAll_cb";
            this.matchAll_cb.Size = new System.Drawing.Size(72, 16);
            this.matchAll_cb.TabIndex = 2;
            this.matchAll_cb.Text = "全字匹配";
            this.matchAll_cb.UseVisualStyleBackColor = true;
            // 
            // matchUp_cb
            // 
            this.matchUp_cb.AutoSize = true;
            this.matchUp_cb.Location = new System.Drawing.Point(126, 20);
            this.matchUp_cb.Name = "matchUp_cb";
            this.matchUp_cb.Size = new System.Drawing.Size(72, 16);
            this.matchUp_cb.TabIndex = 1;
            this.matchUp_cb.Text = "向上匹配";
            this.matchUp_cb.UseVisualStyleBackColor = true;
            // 
            // matchCase_cb
            // 
            this.matchCase_cb.AutoSize = true;
            this.matchCase_cb.Location = new System.Drawing.Point(12, 20);
            this.matchCase_cb.Name = "matchCase_cb";
            this.matchCase_cb.Size = new System.Drawing.Size(84, 16);
            this.matchCase_cb.TabIndex = 0;
            this.matchCase_cb.Text = "大小写匹配";
            this.matchCase_cb.UseVisualStyleBackColor = true;
            // 
            // serchNext_b
            // 
            this.serchNext_b.Dock = System.Windows.Forms.DockStyle.Right;
            this.serchNext_b.Enabled = false;
            this.serchNext_b.Location = new System.Drawing.Point(178, 0);
            this.serchNext_b.Name = "serchNext_b";
            this.serchNext_b.Size = new System.Drawing.Size(75, 23);
            this.serchNext_b.TabIndex = 2;
            this.serchNext_b.Text = "查找下一个";
            this.serchNext_b.UseVisualStyleBackColor = true;
            this.serchNext_b.Click += new System.EventHandler(this.serchNext_b_Click);
            // 
            // exit_b
            // 
            this.exit_b.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.exit_b.Dock = System.Windows.Forms.DockStyle.Right;
            this.exit_b.Location = new System.Drawing.Point(253, 0);
            this.exit_b.Name = "exit_b";
            this.exit_b.Size = new System.Drawing.Size(75, 23);
            this.exit_b.TabIndex = 3;
            this.exit_b.Text = "退出";
            this.exit_b.UseVisualStyleBackColor = true;
            this.exit_b.Click += new System.EventHandler(this.exit_b_Click);
            // 
            // peText_cms
            // 
            this.peText_cms.Name = "peText_cms";
            this.peText_cms.Size = new System.Drawing.Size(61, 4);
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.serchNext_b);
            this.panel1.Controls.Add(this.exit_b);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panel1.Location = new System.Drawing.Point(0, 108);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(328, 23);
            this.panel1.TabIndex = 4;
            // 
            // SearchInfoForm
            // 
            this.AcceptButton = this.serchNext_b;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.CancelButton = this.exit_b;
            this.ClientSize = new System.Drawing.Size(328, 131);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.panel1);
            this.Controls.Add(this.groupBox1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.MaximizeBox = false;
            this.Name = "SearchInfoForm";
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "查找";
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.panel1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Button serchNext_b;
        private System.Windows.Forms.Button exit_b;
        private System.Windows.Forms.Button peText_b;
        private System.Windows.Forms.TextBox searchText_tb;
        private System.Windows.Forms.CheckBox matchPE_cb;
        private System.Windows.Forms.CheckBox matchAll_cb;
        private System.Windows.Forms.CheckBox matchUp_cb;
        private System.Windows.Forms.CheckBox matchCase_cb;
        private System.Windows.Forms.ContextMenuStrip peText_cms;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.CheckBox relation_cb;
        private System.Windows.Forms.CheckBox curCol_cb;
    }
}