﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraCharts;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LTEMIMOSetForm : BaseDialog
    {
        public LTEMIMOSetForm()
        {
            InitializeComponent();
        }

        public MiMoCfgInfo GetScanMIMOCfg(MiMoCfgInfo mimoCfgInfo)
        {
            MiMoCfgInfo res = mimoCfgInfo;
            res.bChanelAna = chkChanel.Checked;
            res.bChanelD = chkD.Checked;
            res.bChanelE = chkE.Checked;
            res.bChanelF = chkF.Checked;
            res.iChanelNum = int.Parse(numChanel.Value.ToString());
            res.bSampleN = chkSampleN.Checked;
            res.iSampleN = int.Parse(numSampleN.Value.ToString());
            res.bRSRP0 = chkRsrp0.Checked;
            res.fRsrp0Min = float.Parse(numRsrp0Min.Value.ToString());
            res.fRsrp0Max = float.Parse(numRsrp0Max.Value.ToString());
            res.bRSRP1 = chkRsrp1.Checked;
            res.fRsrp1Min = float.Parse(numRsrp1Min.Value.ToString());
            res.fRsrp1Max = float.Parse(numRsrp1Max.Value.ToString());
            
            res.bValue = res.bChanelAna || res.bSampleN || res.bRSRP0 || res.bRSRP1;
            return res;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void chkChanel_CheckedChanged(object sender, EventArgs e)
        {
            chkD.Enabled = chkE.Enabled = chkF.Enabled = numChanel.Enabled = chkChanel.Checked;
        }

        private void chkSampleN_CheckedChanged(object sender, EventArgs e)
        {
            numSampleN.Enabled = chkSampleN.Checked;
        }

        private void chkRsrp0_CheckedChanged(object sender, EventArgs e)
        {
            numRsrp0Min.Enabled = numRsrp0Max.Enabled = chkRsrp0.Checked;
        }

        private void chkRsrp1_CheckedChanged(object sender, EventArgs e)
        {
            numRsrp1Min.Enabled = numRsrp1Max.Enabled = chkRsrp1.Checked;
        }

        private void rbScan_CheckedChanged(object sender, EventArgs e)
        {
            bool isEnable = true;
            if (rbScan.Checked)
                isEnable = true;
            else
                isEnable = false;

            chkChanel.Enabled = isEnable;
            chkD.Enabled = isEnable;
            chkF.Enabled = isEnable;
            chkE.Enabled = isEnable;
            numChanel.Enabled = isEnable;

            chkSampleN.Enabled = isEnable;
            numSampleN.Enabled = isEnable;
        }
    }
}
