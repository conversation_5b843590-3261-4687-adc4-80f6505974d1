﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTNRLTECollaborativeAnaForm : MinCloseForm
    {
        MapForm mf = null;
        NRLTECollaborativeAnaLayer layer = null;
        List<ZTNRLTECollaborativeAnaInfo> resList = new List<ZTNRLTECollaborativeAnaInfo>();
        List<TestPoint> allValidTPs;

        public ZTNRLTECollaborativeAnaForm()
        {
            InitializeComponent();
        }

        public void FillData(List<ZTNRLTECollaborativeAnaInfo> resList, List<TestPoint> allValidTPs)
        {
            foreach (var data in MainModel.DTDataManager.FileDataManagers)
            {
                data.ClearTestPoints();
            }
            this.allValidTPs = allValidTPs;
            this.resList = resList;
            curResList = resList;
            gcDetail.DataSource = resList;
            gcDetail.RefreshDataSource();

            mf = MainModel.MainForm.GetMapForm();
            //设置图层
            MasterCom.MTGis.LayerBase clayer = mf.GetTempLayerBase(typeof(NRLTECollaborativeAnaLayer));
            layer = clayer as NRLTECollaborativeAnaLayer;
            layer.ResList = resList;
            layer.SelectedPointsChanged += selectedTestPointsChanged;
            Disposed += disposed;

            MainModel.FireSetDefaultMapSerialThemes("NR:SS_RSRP", "NR:lte_RSRP");
        }

        private void selectedTestPointsChanged(object sender, EventArgs e)
        {
            int index = -1;
            for (int i = 0; i < curResList.Count; i++)
            {
                ZTNRLTECollaborativeAnaInfo res = curResList[i];
                foreach (var tp in res.TestPoints)
                {
                    index = getselectedIndex(index, i, tp);
                    if (index >= 0)
                    {
                        break;
                    }
                }
                if (index >= 0)
                {
                    break;
                }
            }

            if (index >= 0)
            {
                gv.ClearSelection();
                gv.FocusedRowHandle = index;
                gv.SelectRow(index);
            }
        }

        private int getselectedIndex(int index, int i, TestPoint tp)
        {
            foreach (var selectedTP in layer.SelectedTPs)
            {
                if (tp == selectedTP)
                {
                    index = i;
                    break;
                }
            }

            return index;
        }

        private void disposed(object sender, EventArgs e)
        {
            layer.SelectedPointsChanged -= selectedTestPointsChanged;
        }

        private void gv_DoubleClick(object sender, EventArgs e)
        {
            int focusedhandle = gv.FocusedRowHandle;
            ZTNRLTECollaborativeAnaInfo selectedData = curResList[focusedhandle];
            foreach (var tp in layer.SelectedTPs)
            {
                tp.Selected = false;
            }
            layer.SelectedTPs.Clear();
            foreach (var tp in selectedData.TestPoints)
            {
                tp.Selected = true;
                layer.SelectedTPs.Add(tp);
            }
            layer.ResList = new List<ZTNRLTECollaborativeAnaInfo>() { selectedData };

            MasterCom.MTGis.DbRect bounds = getCoverBounds(selectedData.TestPoints);
            mModel.MainForm.GetMapForm().GoToView(bounds);
        }

        private MasterCom.MTGis.DbRect getCoverBounds(List<TestPoint> tps)
        {
            double lngMax = 0;
            double latMax = 0;
            double lngMin = 180;
            double latMin = 90;

            foreach (TestPoint tp in tps)
            {
                if (tp.Longitude > 70 && tp.Latitude > 3)
                {
                    lngMin = Math.Min(lngMin, tp.Longitude);
                    lngMax = Math.Max(lngMax, tp.Longitude);
                    latMin = Math.Min(latMin, tp.Latitude);
                    latMax = Math.Max(latMax, tp.Latitude);
                }
            }
            MasterCom.MTGis.DbRect bounds = new MTGis.DbRect(lngMin - 0.001, latMin - 0.001
                , lngMax + 0.001, latMax + 0.001);

            return bounds;
        }

        protected override void MinCloseForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            MainModel.MainForm.GetMapForm().RemoveTempBaseLayer(typeof(NRLTECollaborativeAnaLayer));
            MainModel.MainForm.GetMapForm().updateMap();

            base.MinCloseForm_FormClosing(sender, e);
        }

        private void miExportXlsSum_Click(object sender, EventArgs e)
        {
            try
            {
                ExcelNPOIManager.ExportToExcel(gv);
            }
            catch
            {
                MessageBox.Show("导出导Excel...失败！");
            }
        }

        private void miShowAll_Click(object sender, EventArgs e)
        {
            layer.ResList = curResList;
            MasterCom.MTGis.DbRect bounds = getCoverBounds(allValidTPs);
            mModel.MainForm.GetMapForm().GoToView(bounds);
        }

        List<ZTNRLTECollaborativeAnaInfo> curResList;
        private void ckbType_EditValueChanged(object sender, EventArgs e)
        {
            curResList = new List<ZTNRLTECollaborativeAnaInfo>();
            var typeDic = new Dictionary<ZTNRLTECollaborativeAnaType, int>();
            var ckbTypeText = ckbType.Text.Trim();
            string[] types = ckbTypeText.Split(',');
            foreach (var name in types)
            {
                if (name.Contains("覆盖同差"))
                {
                    typeDic.Add(ZTNRLTECollaborativeAnaType.WeakCover4G5G, 1);
                }
                else if (name.Contains("4G优于5G"))
                {
                    typeDic.Add(ZTNRLTECollaborativeAnaType.Better4G, 2);
                }
                else if (name.Contains("5G优于4G"))
                {
                    typeDic.Add(ZTNRLTECollaborativeAnaType.Better5G, 3);
                }
            }
        
            foreach (var res in resList)
            {
                if (typeDic.TryGetValue(res.Type, out _))
                {
                    curResList.Add(res);
                }
            }

            gcDetail.DataSource = curResList;
            gcDetail.RefreshDataSource();

            layer.ResList = curResList;
            //layer.TypeDic = typeDic;

            MasterCom.MTGis.DbRect bounds = getCoverBounds(allValidTPs);
            mModel.MainForm.GetMapForm().GoToView(bounds);
        }
    }
}
