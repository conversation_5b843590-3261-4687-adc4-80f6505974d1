﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteVoiceAnaByFreqBandHelper : VoiceAnaByFreqBandHelperBase
    {
        protected override VoiceAnaByFreqBandResult initRes(string str)
        {
            return new LteVoiceAnaByFreqBandResult(str);
        }

        protected override void intEvtList()
        {
            MoCallRequestEvtList.Add(1070);
            MoCallEstablishedEvtList.Add(1074);
            MoDropCallEvtList.Add(1078);

            MtCallRequestEvtList.Add(1071);
            MtCallEstablishedEvtList.Add(1075);
            MtDropCallEvtList.Add(1079);
        }

        protected override int getEarfcn(TestPoint tp)
        {
            int earfcn = 0;
            int? nrEarfcn = (int?)tp["lte_EARFCN"];
            if (nrEarfcn != null)
            {
                earfcn = (int)nrEarfcn;
            }
            return earfcn;
        }

        protected override float? getRsrp(TestPoint tp)
        {
            float? rsrp = (float?)tp["lte_RSRP"];
            if (rsrp != null && rsrp > -150 && rsrp < 50)
            {
                return rsrp;
            }
            return null;
        }

        protected override float? getSinr(TestPoint tp)
        {
            float? sinr = (float?)tp["lte_SINR"];
            if (sinr != null && sinr > -50 && sinr < 50)
            {
                return sinr;
            }
            return null;
        }

        public LteVoiceAnaByFreqBandFileResult GetResult(string fileName)
        {
            if (resDic == null || resDic.Values.Count <= 1)
            {
                return null;
            }

            LteVoiceAnaByFreqBandFileResult fileRes = new LteVoiceAnaByFreqBandFileResult();
            fileRes.FileName = fileName;
            foreach (var res in resDic.Values)
            {
                if (res is LteVoiceAnaByFreqBandResult)
                {
                    fileRes.ResList.Add(res as LteVoiceAnaByFreqBandResult);
                }
            }
            return fileRes;
        }

        public LteVoiceAnaByFreqBandFileResult StatTotalResult(List<LteVoiceAnaByFreqBandFileResult> fileResList)
        {
            LteVoiceAnaByFreqBandFileResult totalFileRes = new LteVoiceAnaByFreqBandFileResult();
            totalFileRes.FileName = "总体";

            Dictionary<string, LteVoiceAnaByFreqBandResult> resDic = new Dictionary<string, LteVoiceAnaByFreqBandResult>
            {
                { "A", new LteVoiceAnaByFreqBandResult("A") },
                { "D", new LteVoiceAnaByFreqBandResult("D") },
                { "E", new LteVoiceAnaByFreqBandResult("E") },
                { "F", new LteVoiceAnaByFreqBandResult("F") },
                { "FDD900", new LteVoiceAnaByFreqBandResult("FDD900") },
                { "FDD1800", new LteVoiceAnaByFreqBandResult("FDD1800") },
                { "总体", new LteVoiceAnaByFreqBandResult("总体") }
            };

            foreach (var fileRes in fileResList)
            {
                foreach (var res in fileRes.ResList)
                {
                    LteVoiceAnaByFreqBandResult curRes;
                    if (resDic.TryGetValue(res.FreqBand, out curRes))
                    {
                        curRes.SampleRate.TotalCount += res.SampleRate.TotalCount;
                        curRes.Add(res);
                    }
                }
            }

            foreach (var res in resDic.Values)
            {
                res.Calculate();
            }

            totalFileRes.ResList = new List<LteVoiceAnaByFreqBandResult>(resDic.Values);
            return totalFileRes;
        }
    }
}
