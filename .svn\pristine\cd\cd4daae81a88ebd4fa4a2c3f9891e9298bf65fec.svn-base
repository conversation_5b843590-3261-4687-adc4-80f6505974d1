﻿using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model
{
    public class ServiceTypeManager //业务类型
    {
        private static ServiceTypeManager instance = null;
        public static ServiceTypeManager getInstance()
        {
            if (instance == null)
            {
                instance = new ServiceTypeManager();
            }
            return instance;
        }
        public static List<ServiceType> GetServiceTypesByServiceName(ServiceName serviceName)
        {
            List<ServiceType> serviceTypes = new List<ServiceType>();
            switch (serviceName)
            {
                case ServiceName.GSM:
                    serviceTypes.Add(ServiceType.GSM_VOICE);
                    serviceTypes.Add(ServiceType.GPRS_DATA);
                    serviceTypes.Add(ServiceType.EDGE_DATA);
                    serviceTypes.Add(ServiceType.GSM_IDLE);
                    serviceTypes.Add(ServiceType.GSM_MOS);
                    serviceTypes.Add(ServiceType.GSM_SCAN);
                    return serviceTypes;

                case ServiceName.TD:
                    serviceTypes.Add(ServiceType.TDSCDMA_VOICE);
                    serviceTypes.Add(ServiceType.TDSCDMA_DATA);
                    serviceTypes.Add(ServiceType.TDSCDMA_VIDEO);
                    serviceTypes.Add(ServiceType.TDSCDMA_IDLE);
                    serviceTypes.Add(ServiceType.TDSCDMA_HSDPA);
                    serviceTypes.Add(ServiceType.TD_SCAN);
                    return serviceTypes;

                case ServiceName.WCDMA:
                    serviceTypes.Add(ServiceType.WCDMA_VOICE);
                    serviceTypes.Add(ServiceType.WCDMA_DATA);
                    serviceTypes.Add(ServiceType.WCDMA_VIDEO);
                    serviceTypes.Add(ServiceType.WCDMA_HSDPA);
                    serviceTypes.Add(ServiceType.WCDMA_SCAN);
                    return serviceTypes;

                case ServiceName.CDMA:
                    serviceTypes.Add(ServiceType.CDMA_VOICE);
                    serviceTypes.Add(ServiceType.CDMA1X_DATA);
                    serviceTypes.Add(ServiceType.CDMA_SCAN);
                    return serviceTypes;

                case ServiceName.EVDO:
                    serviceTypes.Add(ServiceType.CDMA2000_VOICE);
                    serviceTypes.Add(ServiceType.CDMA2000_DATA);
                    serviceTypes.Add(ServiceType.CDMA2000_VIDEO);
                    return serviceTypes;

                case ServiceName.LTE:
                    serviceTypes.Add(ServiceType.LTE_TDD_VOICE);
                    serviceTypes.Add(ServiceType.LTE_TDD_DATA);
                    serviceTypes.Add(ServiceType.LTE_TDD_IDLE);
                    serviceTypes.Add(ServiceType.LTE_TDD_MULTI);
                    serviceTypes.Add(ServiceType.LTE_TDD_UEP);
                    serviceTypes.Add(ServiceType.LTE_SCAN_TOPN);
                    serviceTypes.Add(ServiceType.LTE_SCAN_CW);
                    serviceTypes.Add(ServiceType.LTE扫频_频谱分析);
                    serviceTypes.Add(ServiceType.LTE_SIGNAL);
                    return serviceTypes;

                case ServiceName.LTEFDD:
                    serviceTypes.Add(ServiceType.LTE_FDD_VOICE);
                    serviceTypes.Add(ServiceType.LTE_FDD_DATA);
                    serviceTypes.Add(ServiceType.LTE_FDD_IDLE);
                    serviceTypes.Add(ServiceType.LTE_FDD_MULTI);
                    return serviceTypes;

                case ServiceName.VoLTE:
                    serviceTypes.Add(ServiceType.LTE_TDD_VOLTE);
                    serviceTypes.Add(ServiceType.SER_LTE_TDD_VIDEO_VOLTE);
                    return serviceTypes;

                case ServiceName.VoLTEFDD:
                    serviceTypes.Add(ServiceType.LTE_FDD_VOLTE);
                    serviceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
                    return serviceTypes;

                case ServiceName.NBIOT:
                    serviceTypes.Add(ServiceType.SER_NBIOT_DATA);
                    return serviceTypes;

                case ServiceName.NBIOTSCAN:
                    serviceTypes.Add(ServiceType.SCAN_NBIOT_TOPN);
                    return serviceTypes;

                case ServiceName.VoLTETDDAndFDD:
                    serviceTypes.Add(ServiceType.LTE_TDD_VOLTE);
                    serviceTypes.Add(ServiceType.SER_LTE_TDD_VIDEO_VOLTE);
                    serviceTypes.Add(ServiceType.LTE_FDD_VOLTE);
                    serviceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
                    return serviceTypes;

                case ServiceName.LTETDDAndFDD:
                    serviceTypes.Add(ServiceType.LTE_TDD_DATA);
                    serviceTypes.Add(ServiceType.LTE_TDD_IDLE);
                    serviceTypes.Add(ServiceType.LTE_TDD_MULTI);
                    serviceTypes.Add(ServiceType.LTE_TDD_VOICE);
                    serviceTypes.Add(ServiceType.LTE_FDD_DATA);
                    serviceTypes.Add(ServiceType.LTE_FDD_IDLE);
                    serviceTypes.Add(ServiceType.LTE_FDD_MULTI);
                    serviceTypes.Add(ServiceType.LTE_FDD_VOICE);
                    return serviceTypes;

                case ServiceName.NR:
                    serviceTypes.Add(ServiceType.NR_NSA_TDD_IDLE);
                    serviceTypes.Add(ServiceType.NR_NSA_TDD_DATA);
                    serviceTypes.Add(ServiceType.NR_NSA_TDD_VOLTE);
                    serviceTypes.Add(ServiceType.NR_SA_TDD_IDLE);
                    serviceTypes.Add(ServiceType.NR_SA_TDD_DATA);
                    serviceTypes.Add(ServiceType.NR_SA_TDD_VOLTE);
                    serviceTypes.Add(ServiceType.NR_DM_TDD_IDLE);
                    serviceTypes.Add(ServiceType.NR_DM_TDD_DATA);
                    serviceTypes.Add(ServiceType.NR_DM_TDD_VOLTE);
                    serviceTypes.Add(ServiceType.NR_SA_TDD_VONR);

                    serviceTypes.Add(ServiceType.SER_NR_SA_TDD_EPSFB);
                    serviceTypes.Add(ServiceType.SER_NR_DM_TDD_EPSFB);

                    serviceTypes.Add(ServiceType.SER_NR_NSA_TDD_MULTI);
                    serviceTypes.Add(ServiceType.SER_NR_SA_TDD_MULTI);
                    serviceTypes.Add(ServiceType.SER_NR_DM_TDD_MULTI);
                    return serviceTypes;

                case ServiceName.NRVoice:
                    serviceTypes.Add(ServiceType.NR_NSA_TDD_VOLTE);
                    serviceTypes.Add(ServiceType.NR_SA_TDD_VOLTE);
                    serviceTypes.Add(ServiceType.NR_DM_TDD_VOLTE);
                    serviceTypes.Add(ServiceType.NR_SA_TDD_VONR);

                    serviceTypes.Add(ServiceType.SER_NR_SA_TDD_EPSFB);
                    serviceTypes.Add(ServiceType.SER_NR_DM_TDD_EPSFB);
                    
                    serviceTypes.Add(ServiceType.SER_NR_NSA_TDD_MULTI);
                    serviceTypes.Add(ServiceType.SER_NR_SA_TDD_MULTI);
                    serviceTypes.Add(ServiceType.SER_NR_DM_TDD_MULTI);
                    return serviceTypes;

                case ServiceName.NRScan:
                    serviceTypes.Add(ServiceType.NR_Scan);
                    return serviceTypes;

                default:
                    return serviceTypes;
            }
        }

        public static List<ServiceType> GetLteAllServiceTypes()
        {
            List<ServiceType> serviceTypes = new List<ServiceType>();

            serviceTypes.Add(ServiceType.LTE_TDD_VOICE);
            serviceTypes.Add(ServiceType.LTE_TDD_DATA);
            serviceTypes.Add(ServiceType.LTE_TDD_IDLE);
            serviceTypes.Add(ServiceType.LTE_TDD_MULTI);
            serviceTypes.Add(ServiceType.LTE_TDD_UEP);
            serviceTypes.Add(ServiceType.LTE_SCAN_TOPN);
            serviceTypes.Add(ServiceType.LTE_SCAN_CW);
            serviceTypes.Add(ServiceType.LTE扫频_频谱分析);
            serviceTypes.Add(ServiceType.LTE_SIGNAL);

            serviceTypes.Add(ServiceType.LTE_FDD_VOICE);
            serviceTypes.Add(ServiceType.LTE_FDD_DATA);
            serviceTypes.Add(ServiceType.LTE_FDD_IDLE);
            serviceTypes.Add(ServiceType.LTE_FDD_MULTI);

            serviceTypes.Add(ServiceType.LTE_TDD_VOLTE);
            serviceTypes.Add(ServiceType.SER_LTE_TDD_VIDEO_VOLTE);

            serviceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            serviceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);

            return serviceTypes;
        }
        public static ServiceName getServiceNameFromTypeID(int serviceType)
        {
            ServiceType typeEnum = (ServiceType)serviceType;
            switch (typeEnum)
            {
                case ServiceType.GSM_VOICE:
                case ServiceType.GPRS_DATA:
                case ServiceType.EDGE_DATA:
                case ServiceType.GSM_IDLE:
                case ServiceType.GSM_MOS:
                case ServiceType.GSM_SCAN:
            	    return ServiceName.GSM;
                case ServiceType.TDSCDMA_VOICE:
                case ServiceType.TDSCDMA_DATA:
                case ServiceType.TDSCDMA_VIDEO:
                case ServiceType.TDSCDMA_IDLE:
                case ServiceType.TDSCDMA_HSDPA:
                case ServiceType.TD_SCAN:
                    return ServiceName.TD;
                case ServiceType.WCDMA_VOICE:
                case ServiceType.WCDMA_DATA:
                case ServiceType.WCDMA_VIDEO:
                case ServiceType.WCDMA_HSDPA:
                case ServiceType.WCDMA_SCAN:
                    return ServiceName.WCDMA;
                case ServiceType.CDMA_VOICE:
                case ServiceType.CDMA1X_DATA:
                case ServiceType.CDMA_SCAN:
                    return ServiceName.CDMA;
                case ServiceType.CDMA2000_VOICE:
                case ServiceType.CDMA2000_DATA:
                case ServiceType.CDMA2000_VIDEO:
                    return ServiceName.EVDO;

                case ServiceType.LTE_TDD_VOICE:
                case ServiceType.LTE_TDD_DATA:
                case ServiceType.LTE_TDD_IDLE:
                case ServiceType.LTE_TDD_MULTI:
                case ServiceType.LTE_TDD_UEP:
                case ServiceType.LTE_SCAN_TOPN:
                case ServiceType.LTE_SCAN_CW:
                case ServiceType.LTE扫频_频谱分析:
                case ServiceType.LTE_SIGNAL:
                    return ServiceName.LTE;

                case ServiceType.LTE_FDD_VOICE:
                case ServiceType.LTE_FDD_DATA:
                case ServiceType.LTE_FDD_IDLE:
                case ServiceType.LTE_FDD_MULTI:
                    return ServiceName.LTEFDD;

                case ServiceType.LTE_TDD_VOLTE:
                case ServiceType.SER_LTE_TDD_VIDEO_VOLTE:
                    return ServiceName.VoLTE;

                case ServiceType.LTE_FDD_VOLTE:
                case ServiceType.SER_LTE_FDD_VIDEO_VOLTE:
                    return ServiceName.VoLTEFDD;

                case ServiceType.SCAN_NBIOT_TOPN:
                case ServiceType.SER_NBIOT_DATA:
                    return ServiceName.NBIOT;

                case ServiceType.NR_NSA_TDD_IDLE:
                case ServiceType.NR_NSA_TDD_DATA:
                case ServiceType.NR_NSA_TDD_VOLTE:
                case ServiceType.NR_SA_TDD_IDLE:
                case ServiceType.NR_SA_TDD_DATA:
                case ServiceType.NR_SA_TDD_VOLTE:
                case ServiceType.NR_DM_TDD_IDLE:
                case ServiceType.NR_DM_TDD_DATA:
                case ServiceType.NR_DM_TDD_VOLTE:
                case ServiceType.SER_NR_SA_TDD_EPSFB:
                case ServiceType.SER_NR_DM_TDD_EPSFB:
                case ServiceType.SER_NR_NSA_TDD_MULTI:
                case ServiceType.SER_NR_SA_TDD_MULTI:
                case ServiceType.SER_NR_DM_TDD_MULTI:
                case ServiceType.NR_SA_TDD_VONR:
                    return ServiceName.NR;

                default:
                    return ServiceName.NULL;

            }
        }

        private List<int> getNeedCarrierIDsFrom(List<int> selServiceTypes,List<int> selCarrierIDs) //获取需要增加的运营商ID（不包含已选运营商ID）
        {
            List<int> retList = new List<int>();
            foreach (int servID in selServiceTypes)
            {
                if (ServCarrierDic.ContainsKey(servID))
                {
                    List<int> cList = ServCarrierDic[servID];
                    bool found = judgeIsFound(selCarrierIDs, cList);
                    addRetList(retList, cList, found);
                }
            }
            return retList;
        }

        private bool judgeIsFound(List<int> selCarrierIDs, List<int> cList)
        {
            bool found = false;
            foreach (int selCID in selCarrierIDs)
            {
                if (cList.Contains(selCID))
                {
                    found = true;
                    break;
                }
            }

            return found;
        }

        private void addRetList(List<int> retList, List<int> cList, bool found)
        {
            if (!found)
            {
                foreach (int cid in cList)
                {
                    if (!retList.Contains(cid))
                    {
                        retList.Add(cid);
                    }
                }
            }
        }

        public List<int> getAllNeedCarrierIDsFrom(List<int> selServiceTypes, List<int> selCarrierIDs) //获取所有需要用到的运营商ID(包含已选运营商ID)
        {
            List<int> retList = getNeedCarrierIDsFrom(selServiceTypes, selCarrierIDs);
            foreach (int cid in selCarrierIDs)
            {
                if (!retList.Contains(cid))
                {
                    retList.Add(cid);
                }
            }
            return retList;
        }

        public bool needAddCarrierIDs(List<int> selServiceTypes, List<int> selCarrierIDs) //是否需要增加运营商
        {
            List<int> ids = getNeedCarrierIDsFrom(selServiceTypes, selCarrierIDs);
#if PermissionControl_DataSrc
            for (int i = 0; i < ids.Count; i++)
            {
                int id = ids[i];
                if (!MainModel.GetInstance().User.HasCarrierRight(MainModel.GetInstance().DistrictID, id))
                {
                    ids.RemoveAt(i);
                    i--;
                }
            }
#endif
            return ids.Count > 0;
        }

        private Dictionary<int, List<int>> servCarrierDic = null;
        public Dictionary<int, List<int>> ServCarrierDic
        {
            get
            {
                if (servCarrierDic == null)
                {
                    servCarrierDic = new Dictionary<int, List<int>>();
                    initServCarrierDic(servCarrierDic);
                }
                return servCarrierDic;
            }
        }

        private void initServCarrierDic(Dictionary<int, List<int>> dict) //初始化业务类型、运营商ID哈希表
        {
            dict.Add(1, new List<int>());//GSM语音业务
            dict[1].Add(1);
            dict[1].Add(2);

            dict.Add(2,new List<int>());//GPRS数据业务
            dict[2].Add(1);
            dict[2].Add(2);

            dict.Add(3, new List<int>());//EDGE数据业务
            dict[3].Add(1);
            dict[3].Add(2);

            dict.Add(4, new List<int>());//TD语音业务
            dict[4].Add(1);

            dict.Add(5, new List<int>());//TD数据业务
            dict[5].Add(1);

            dict.Add(6, new List<int>());//CDMA语音业务
            dict[6].Add(3);

            dict.Add(7, new List<int>());//CDMA1X数据业务
            dict[7].Add(3);

            dict.Add(8, new List<int>());//CDMA2000语音业务
            dict[8].Add(3);

            dict.Add(9, new List<int>());//CDMA2000数据业务
            dict[9].Add(3);

            dict.Add(10, new List<int>());//WCDMA语音业务
            dict[10].Add(2);

            dict.Add(11, new List<int>());//WCDMA数据业务
            dict[11].Add(2);

            dict.Add(12, new List<int>());//扫频业务
            dict[12].Add(1);
            dict[12].Add(2);
            dict[12].Add(3);

            dict.Add(13, new List<int>());//TD视频业务
            dict[13].Add(1);

            dict.Add(14, new List<int>());//WCDMA_VIDEO
            dict[14].Add(2);

            dict.Add(15, new List<int>());//WCDMA_HSDPA
            dict[15].Add(2);

            dict.Add(16, new List<int>());//CDMA2000_VIDEO
            dict[16].Add(3);

            dict.Add(17, new List<int>());//TDSCDMA_IDLE
            dict[17].Add(1);

            dict.Add(18, new List<int>());//TDSCDMA_HSDPA
            dict[18].Add(1);

            dict.Add(19, new List<int>());//SCAN_TD
            dict[19].Add(1);

            dict.Add(20, new List<int>());//SCAN_CDMA
            dict[20].Add(3);

            dict.Add(21, new List<int>());//SCAN_WCDMA
            dict[21].Add(2);

            dict.Add(22, new List<int>());//GSM_IDLE
            dict[22].Add(1);
            dict[22].Add(2);

            dict.Add(23, new List<int>());//GSM_MOS
            dict[23].Add(1);
            dict[23].Add(2);

            dict.Add(24, new List<int>());//GSM_UPLINK
            dict[24].Add(1);
            dict[24].Add(2);

            dict.Add(25, new List<int>());//WCDMA_IDLE
            dict[25].Add(2);

            dict.Add(26, new List<int>());//CDMA_IDLE
            dict[26].Add(3);

            dict.Add(27, new List<int>());//TDSCDMA_HSUPA
            dict[27].Add(1);

            dict.Add(28, new List<int>());//WCDMA_HSUPA
            dict[28].Add(2);

            dict.Add(29, new List<int>());//GSM_CALLTRACE
            dict[29].Add(1);
            dict[29].Add(2);

            dict.Add(30, new List<int>());//TD_CALLTRACE
            dict[30].Add(1);

            dict.Add(40, new List<int>());//CDMA2000空闲
            dict[40].Add(3);

            dict.Add(45, new List<int>());//LTE_FDD_VOICE
            dict[45].Add(2);
            dict[45].Add(3);

            dict.Add(46, new List<int>());//LTE_FDD_DATA
            dict[46].Add(2);
            dict[46].Add(3);

            dict.Add(47, new List<int>());//LTE_FDD_IDLE
            dict[47].Add(2);
            dict[47].Add(3);

            dict.Add(48, new List<int>());//LTE_FDD_MULTI
            dict[48].Add(2);
            dict[48].Add(3);

            dict.Add(49, new List<int>());//LTE_FDD_VIDEO
            dict[49].Add(2);
            dict[49].Add(3);
        }

        public string GetStrServiceName(int iServiceId)
        {
            string strServiceName = "";
            CategoryEnumItem[] svItems = ((CategoryEnum)CategoryManager.GetInstance()["ServiceType"]).Items;
            foreach (CategoryEnumItem cei in svItems)
            {
                if (cei.ID == iServiceId)
                {
                    strServiceName = cei.Description;
                    break;
                }
            }
            return strServiceName;
        }
    }

    /// <summary>
    /// 运营商，既定顺序，不可调整
    /// </summary>
    public enum CarrierType
    {
        ChinaMobile = 1,
        ChinaUnicom,
        ChinaTelecom
    }

    public enum NetworkType
    {
        GSM=1,
        TDSCDMA,
        WCDMA,
        CDMA,
        LTE_TDD,
        NR
    }

    /// <summary>
    /// 业务类型，既定顺序，对应数据库，不可调整
    /// </summary>
    public enum ServiceType
    {
        GSM_VOICE = 1,
        GPRS_DATA=2,
        EDGE_DATA=3,
        TDSCDMA_VOICE=4,
        TDSCDMA_DATA=5,
        CDMA_VOICE=6,
        CDMA1X_DATA=7,
        CDMA2000_VOICE=8,
        CDMA2000_DATA=9,
        WCDMA_VOICE=10,
        WCDMA_DATA=11,
        GSM_SCAN=12,
        TDSCDMA_VIDEO=13,
        WCDMA_VIDEO=14,
        WCDMA_HSDPA=15,
        CDMA2000_VIDEO=16,
        TDSCDMA_IDLE=17,
        TDSCDMA_HSDPA=18,
        TD_SCAN=19,
        CDMA_SCAN=20,
        WCDMA_SCAN=21,
        GSM_IDLE=22,
        GSM_MOS=23,
        GSM_MTR=24,
        WCDMA_IDLE=25,
        CDMA_IDLE=26,
        TDSCDMA_HSUPA=27,
        WCDMA_HSUPA=28,
        GSM_CALLTRACE=29,
        TD_CALLTRACE=30,
        WLAN=31,
        CDMA_MOS=32,
        LTE_TDD_VOICE=33,
        LTE_TDD_DATA=34,
        LTE_SCAN_TOPN=35,
        LTE_SCAN_CW=36,
        LTE扫频_频谱分析=37
        ,TD扫频_频谱分析=38
        ,GSM扫频_频谱分析=39
        ,CDMA2000_IDLE=40
        ,LTE_TDD_IDLE=41
        ,LTE_TDD_MULTI=42
        ,LTE_TDD_VOLTE=43
        ,LTE_TDD_UEP=44
        ,LTE_FDD_VOICE=45
        ,LTE_FDD_DATA=46
        ,LTE_FDD_IDLE=47
        ,LTE_FDD_MULTI=48
        ,LTE_FDD_VOLTE=49
        ,LTE_SIGNAL=50
        ,SER_LTE_TDD_VIDEO_VOLTE = 51
        ,SER_LTE_FDD_VIDEO_VOLTE = 52
        ,SCAN_NBIOT_TOPN = 55
        ,SER_NBIOT_DATA = 56
        ,NR_NSA_TDD_IDLE = 57
        ,NR_NSA_TDD_DATA = 58
        ,NR_NSA_TDD_VOLTE = 59
        ,NR_SA_TDD_IDLE = 60
        ,NR_SA_TDD_DATA = 61
        ,NR_SA_TDD_VOLTE = 62
        ,NR_DM_TDD_IDLE = 63
        ,NR_DM_TDD_DATA = 64
        ,NR_DM_TDD_VOLTE = 65
        ,SER_NR_SA_TDD_EPSFB = 66
        ,SER_NR_DM_TDD_EPSFB = 67
        ,SER_NR_NSA_TDD_MULTI = 68
        ,SER_NR_SA_TDD_MULTI = 69
        ,SER_NR_DM_TDD_MULTI = 70
        ,NR_Scan = 71
        ,NR扫频_频谱分析 = 72
        ,NR_SA_TDD_VONR = 73
    }

    public enum ServiceName //业务类型名称
    {
        GSM,
        TD,
        WCDMA,
        CDMA,
        EVDO,
        LTE,
        LTEFDD,
        VoLTE,
        VoLTEFDD,
        NBIOT,
        NBIOTSCAN,
        VoLTETDDAndFDD,
        LTETDDAndFDD,
        NR,
        NRVoice,
        NRScan,
        NULL
    }
    public enum AppType
    {
        PPP_Dial = 1,
        FTP_Download,
        FTP_Upload,
        WAP_Logon,
        WAP_Page,//5
        WAP_Download,
        WAP_Upload,
        WAP_Kjava,
        PING,
        SMS_P2P,//10
        MMS_P2P,
        Http_Download,
        Http_Upload,
        Email_POP3,
        Email_SMTP,//15
        Email_IMAP,
        PDP_Active,
        PDP_deactivated,
        PS_Attach,
        PS_Detached,//20
        Device,
        Web_Authentication,
        DNS_Lookup,
        Service_State,
        Http_Video,//25 流媒体
        Http,//Http浏览
        TestAction,
        Event_Call,
        FTP_Logon,
        PESQ_Mos,//30
        RCU, //LTE Detach等
        Flash_Video,
        Video_Stream,
        LTE_Connect,
        IMS_RTP,//35
        Cell_EARFCN,
        NR_RACH//NR请求连接(入网)
    }

    public enum AppDataStatus
    {
        PS384 = 1,
        PS128 = 2,
        PS64 = 3,
        GPRS = 4,
        HSPA = 5,
        CDMA1X = 6,
        EVDO = 7,
        EDGE = 8,
        LTE = 9,
    }

    public enum EAppStatus
    {
        //----------------<common>-------------------//
        eAPPSTATUS_Test_Begin = 0x00, //暂ATU用来标记测试开始
        eAPPSTATUS_Test_End = 0xFF, //暂ATU用来标记测试结束
        STATUS_Success = 23,
        STATUS_Failure = 24,
        
        //-------------<FTP_Logon>---------------//
        FTPSTATUS_FTP_LOGON = 69,
        FTPSTATUS_FTP_LOGON_SUCCESS = 70,
        FTPSTATUS_FTP_LOGON_FAIL = 71,

        //------------<FTP_Download>-------------//
        FTPSTATUS_DOWNLOAD = 5,
        FTPSTATUS_DOWNLOAD_CONNECT = 6,
        FTPSTATUS_DOWNLOAD_SEND_RETR = 7,
        FTPSTATUS_DOWNLOAD_FIRST_DATA = 8,
        FTPSTATUS_DOWNLOAD_LAST_DATA = 9,
        FTPSTATUS_DOWNLOAD_STOP = 10,
        FTPSTATUS_DOWNLOAD_DISCONNECT = 11,
        FTPSTATUS_DOWNLOAD_CONTINUE = 12,
        FTPSTATUS_DOWNLOAD_Error = 13,
        FTPSTATUS_DOWNLOAD_Connect_Request = 31, // Multi Ftp
        FTPSTATUS_DOWNLOAD_Connect_Success = 32, // Multi Ftp
        FTPSTATUS_DOWNLOAD_File_Size = 33,       // Multi Ftp
        FTPSTATUS_DOWNLOAD_Resend_Retr_Cmd = 34, // Multi Ftp
        FTPSTATUS_DOWNLOAD_ReSend_RETR = 35,     // Multi Ftp
        FTPSTATUS_DOWNLOAD_Qos = 36,             // Multi Ftp
        FTPSTATUS_DOWNLOAD_Count_Down_Request = 37, // Multi Ftp
        FTPSTATUS_DOWNLOAD_DROP = 42,
        FTPSTATUS_DOWNLOAD_Failure = 77, // ATU用了
        FTPSTATUS_DOWNLOAD_DROP_ATU = 78,

        //-------------<FTP_Upload>--------------//
        FTPSTATUS_UPLOAD = 14,
        FTPSTATUS_UPLOAD_CONNECT = 15,
        FTPSTATUS_UPLOAD_SEND_STOR = 16,
        FTPSTATUS_UPLOAD_FIRST_DATA = 17,
        FTPSTATUS_UPLOAD_LAST_DATA = 18,
        FTPSTATUS_UPLOAD_STOP = 19,
        FTPSTATUS_UPLOAD_DISCONNECT = 20,
        FTPSTATUS_UPLOAD_CONTINUE = 21,
        FTPSTATUS_UPLOAD_Error = 22,
        FTPSTATUS_UPLOAD_Failure = 23, // ATU用了
        FTPSTATUS_UPLOAD_FileSize = 24, // Multi Ftp
        FTPSTATUS_UPLOAD_Support_Reset = 25, //Multi Ftp
        FTPSTATUS_UPLOAD_Count_Down_Request = 26, // //Multi Ftp
        FTPSTATUS_UPLOAD_Connect_Request = 27, // Multi Ftp
        FTPSTATUS_UPLOAD_Connect_Success = 28, // Multi Ftp
        FTPSTATUS_UPLOAD_Qos = 29, // Multi Ftp
        FTPSTATUS_UPLOAD_ReSend_STOR = 30,        // Multi Ftp

        FTPSTATUS_UPLOAD_DROP = 43,
    }

}
