﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTLteLastRoadScene
{
    public class RoadSceneCondition
    {
        public double FastFadeDistance { get; set; }
        public float FastFadeRsrp { get; set; }
        public float FastFadeRsrpDiff { get; set; }
        public int MultiBand { get; set; }
        public double MultiCellDistance { get; set; }
        public int MultiCellNum { get; set; }
        public double MultiDistance { get; set; }
        public int MultiLev { get; set; }
        public float MultiRsrp { get; set; }
        public int SiteAltitude { get; set; }
        public double WeakCvrCellDis { get; set; }
        public double WeakCvrDistance { get; set; }
        public float WeakCvrRsrp { get; set; }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["FastFadeDistance"] = FastFadeDistance;
                param["FastFadeRsrp"] = FastFadeRsrp;
                param["FastFadeRsrpDiff"] = FastFadeRsrpDiff;
                param["MultiBand"] = MultiBand;
                param["MultiCellDistance"] = MultiCellDistance;
                param["MultiCellNum"] = MultiCellNum;
                param["MultiDistance"] = MultiDistance;
                param["MultiLev"] = MultiLev;
                param["MultiRsrp"] = MultiRsrp;
                param["SiteAltitude"] = SiteAltitude;
                param["WeakCvrCellDis"] = WeakCvrCellDis;
                param["WeakCvrDistance"] = WeakCvrDistance;
                param["WeakCvrRsrp"] = WeakCvrRsrp;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("FastFadeDistance"))
                {
                    FastFadeDistance = (double)param["FastFadeDistance"];
                }
                if (param.ContainsKey("FastFadeRsrp"))
                {
                    FastFadeRsrp = (float)param["FastFadeRsrp"];
                }
                if (param.ContainsKey("FastFadeRsrpDiff"))
                {
                    FastFadeRsrpDiff = (float)param["FastFadeRsrpDiff"];
                }
                if (param.ContainsKey("MultiBand"))
                {
                    MultiBand = (int)param["MultiBand"];
                }
                if (param.ContainsKey("MultiCellDistance"))
                {
                    MultiCellDistance = (double)param["MultiCellDistance"];
                }
                if (param.ContainsKey("MultiCellNum"))
                {
                    MultiCellNum = (int)param["MultiCellNum"];
                }
                if (param.ContainsKey("MultiDistance"))
                {
                    MultiDistance = (double)param["MultiDistance"];
                }
                if (param.ContainsKey("MultiLev"))
                {
                    MultiLev = (int)param["MultiLev"];
                }
                if (param.ContainsKey("MultiRsrp"))
                {
                    MultiRsrp = (float)param["MultiRsrp"];
                }
                if (param.ContainsKey("SiteAltitude"))
                {
                    SiteAltitude = (int)param["SiteAltitude"];
                }
                if (param.ContainsKey("WeakCvrCellDis"))
                {
                    WeakCvrCellDis = (double)param["WeakCvrCellDis"];
                }
                if (param.ContainsKey("WeakCvrDistance"))
                {
                    WeakCvrDistance = (double)param["WeakCvrDistance"];
                }
                if (param.ContainsKey("WeakCvrRsrp"))
                {
                    WeakCvrRsrp = (float)param["WeakCvrRsrp"];
                }
            }
        }
    }
}
