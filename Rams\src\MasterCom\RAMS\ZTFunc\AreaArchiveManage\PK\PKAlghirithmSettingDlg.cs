﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public enum ESpecialDesc
    {
        无数据,
        有数据,
        忽略,
    }

    public partial class PKAlghirithmSettingDlg : MasterCom.RAMS.Func.BaseForm
    {
        private Alghirithm alghirithm;

        public PKAlghirithmSettingDlg()
        {
            InitializeComponent();
            listBoxAlghirithmVec.SelectedIndexChanged += new EventHandler(listBoxAlghirithmVec_SelectedIndexChanged);
            radioGroupCompeteType.SelectedIndex = 0;
            alghirithm = new Alghirithm();
            init();
            radioGroupCompeteType.SelectedIndexChanged += new EventHandler(radioGroupCompeteType_SelectedIndexChanged);
            radioGroupCompeteType_SelectedIndexChanged(null, null);
            listBoxAlghirithmVec_SelectedIndexChanged(null, null);
        }

        private void init()
        {
            comboBoxEditCM.Properties.Items.Clear();
            comboBoxEditCU.Properties.Items.Clear();
            comboBoxEditCT.Properties.Items.Clear();

            foreach (ESpecialDesc e in Enum.GetValues(typeof(ESpecialDesc)))
            {
                comboBoxEditCM.Properties.Items.Add(e);
                comboBoxEditCU.Properties.Items.Add(e);
                comboBoxEditCT.Properties.Items.Add(e);
            }

            comboBoxEditCM.SelectedIndex = comboBoxEditCU.SelectedIndex = comboBoxEditCT.SelectedIndex = 0;
        }

        private void radioGroupCompeteType_SelectedIndexChanged(object sender, EventArgs e)
        {
            int sIdx = radioGroupCompeteType.SelectedIndex;
            groupBoxCommon.Visible = sIdx == 0;
            groupBoxSpecial.Visible = sIdx == 1;
        }

        private void listBoxAlghirithmVec_SelectedIndexChanged(object sender, EventArgs e)
        {
            simpleButtonModify.Enabled = simpleButtonRemove.Enabled = listBoxAlghirithmVec.SelectedItem != null;
        }

        public void FillData(Alghirithm thm)
        {
            this.alghirithm = thm;

            refreshData();
        }

        public Alghirithm GetAttribute()
        {
            alghirithm.IsSpecial = radioGroupCompeteType.SelectedIndex == 1;
            alghirithm.Name = textBoxAlghirithmName.Text;
            alghirithm.Color = (Color)colorEidt.EditValue;
            alghirithm.ValueRangeVec.Clear();
            foreach (object o in listBoxAlghirithmVec.Items)
            {
                alghirithm.ValueRangeVec.Add(o as ValueRange);
            }
            alghirithm.ENaNCM = (ESpecialDesc)comboBoxEditCM.SelectedItem;
            alghirithm.ENaNCU = (ESpecialDesc)comboBoxEditCU.SelectedItem;
            alghirithm.ENaNCT = (ESpecialDesc)comboBoxEditCT.SelectedItem;

            return alghirithm;
        }

        private void refreshData()
        {
            if (alghirithm == null) return;
            radioGroupCompeteType.SelectedIndex = alghirithm.IsSpecial ? 1 : 0;
            textBoxAlghirithmName.Text = alghirithm.Name;
            refreshAlghirithm();

            comboBoxEditCM.SelectedItem = alghirithm.ENaNCM;
            comboBoxEditCU.SelectedItem = alghirithm.ENaNCU;
            comboBoxEditCT.SelectedItem = alghirithm.ENaNCT;
        }

        private void refreshAlghirithm()
        {
            listBoxAlghirithmVec.Items.Clear();
            foreach (ValueRange vr in alghirithm.ValueRangeVec)
            {
                listBoxAlghirithmVec.Items.Add(vr);
            }
            colorEidt.EditValue = alghirithm.Color;

            simpleButtonModify.Enabled = simpleButtonRemove.Enabled = listBoxAlghirithmVec.Items.Count > 0;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (textBoxAlghirithmName.Text.Trim() == string.Empty)
            {
                MessageBox.Show("竞比算法名称不能为空...", "提示");
                return;
            }
            if (radioGroupCompeteType.SelectedIndex == 0 && listBoxAlghirithmVec.Items.Count == 0)
            {
                MessageBox.Show("尚未添加竞比规则...", "提示");
                return;
            }
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void simpleButtonAdd_Click(object sender, EventArgs e)
        {
            PKAlghirithmOption dlg = new PKAlghirithmOption();
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                ValueRange vr = dlg.GetAttribute();

                alghirithm.ValueRangeVec.Add(vr);
                refreshAlghirithm();
                listBoxAlghirithmVec.SelectedItem = vr;
            }
        }

        private void simpleButtonModify_Click(object sender, EventArgs e)
        {
            ValueRange vr = listBoxAlghirithmVec.SelectedItem as ValueRange;
            if (vr == null) return;

            PKAlghirithmOption dlg = new PKAlghirithmOption();
            dlg.SetAttribute(vr);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                vr = dlg.GetAttribute();
                refreshAlghirithm();
                listBoxAlghirithmVec.SelectedItem = vr;
            }
        }

        private void simpleButtonRemove_Click(object sender, EventArgs e)
        {
            ValueRange vr = listBoxAlghirithmVec.SelectedItem as ValueRange;
            if (vr == null) return;

            alghirithm.ValueRangeVec.Remove(vr);
            refreshAlghirithm();
        }
    }
}
