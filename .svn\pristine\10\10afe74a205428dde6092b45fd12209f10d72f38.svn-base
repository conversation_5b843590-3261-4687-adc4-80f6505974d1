using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.ES.Data;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.Net
{
    public class ZTDIYCoverDistanceByRegion_W : ZTDIYCoverDistanceByRegion
    {
        public ZTDIYCoverDistanceByRegion_W(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
        }
        
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 14000, 14024, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "W_TotalRSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SysLAI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_SysCellID";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_frequency";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "W_Reference_PSC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"CoverDistance");
            tmpDic.Add("themeName", (object)"W_TotalRSCP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void clearDataBeforeQuery()
        {
            cellCoverDistanceDic.Clear();
            wCellCoverDistanceDic.Clear();
        }

        protected override void doWithDTData(TestPoint tp)
        {
            try
            {
                Cell mainCell = null;
                WCell mainWCell = tp.GetMainCell_W();
                if (mainWCell == null)
                {
                    int? rxlevSub = (int?)tp["W_NGsmRXLev"];
                    if (rxlevSub == null || rxlevSub < -120 || rxlevSub > -10)
                    {
                        return;
                    }
                    mainCell = tp.GetMainCell_GSM();
                    if (mainCell != null)
                    {
                        saveTestPoint(tp, mainCell, (float)rxlevSub);
                    }
                }
                else
                {
                    float? rscp = (float?)tp["W_TotalRSCP"];
                    if (rscp == null || rscp < -120 || rscp > -10)
                    {
                            return;
                    }
                    saveTestPoint(tp, mainWCell, (float)rscp);
                }
            }
            catch
            {
                //continue
            }
        }

        protected void saveTestPoint(TestPoint tp, WCell mainWCell, float rscp)
        {
            double distance = MathFuncs.GetDistance(tp.Longitude, tp.Latitude, mainWCell.Longitude, mainWCell.Latitude);
            if (!wCellCoverDistanceDic.ContainsKey(mainWCell))
            {
                wCellCoverDistanceDic[mainWCell] = new CellCoverDistance(mainWCell);
            }
            wCellCoverDistanceDic[mainWCell].distanceList.Add(distance);
            wCellCoverDistanceDic[mainWCell].rxLevList.Add(rscp);
        }

        protected override void getResultAfterQuery()
        {
            MainModel.CellCoverDistanceList = new List<CellCoverDistance>(wCellCoverDistanceDic.Values);
            if (cellCoverDistanceDic.Count > 0)
            {
                MainModel.CellCoverDistanceList.AddRange(cellCoverDistanceDic.Values);
            }
        }

        protected Dictionary<WCell, CellCoverDistance> wCellCoverDistanceDic = new Dictionary<WCell, CellCoverDistance>();
    }
}
