using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Frame;

namespace MasterCom.RAMS.Func
{
    public partial class WlanApInfoDetailForm : ChildForm
    {
        public WlanApInfoDetailForm()
        {
            InitializeComponent();
        }

        public override void Init()
        {
            base.Init();
            MainModel.wlanApInfoDetailShow += wlanApInfoDetailShow;
            Disposed += disposed;
            initializeComponent();
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                return param;
            }
        }

        private void initializeComponent()
        {
            //
        }

        private void disposed(object sender, EventArgs e)
        {
            MainModel.wlanApInfoDetailShow -= wlanApInfoDetailShow;
        }

        private void wlanApInfoDetailShow(object sender, EventArgs e)
        {
            WlanApInfo apInfo = sender as WlanApInfo;
            FillForm(apInfo);
        }

        private void FillForm(WlanApInfo apInfo)
        {
            DataTable dataTable = new DataTable();
            dataTable.Columns.Add("Param1", typeof(string));
            dataTable.Columns.Add("Value1", typeof(string));
            dataTable.Columns.Add("Param2", typeof(string));
            dataTable.Columns.Add("Value2", typeof(string));

            DataRow dataRow = dataTable.NewRow();
            dataRow["Param1"] = "Freqecncy";
            dataRow["Value1"] = apInfo.freqecncy;
            dataRow["Param2"] = "Channel";
            dataRow["Value2"] = apInfo.channel;
            dataTable.Rows.Add(dataRow);

            dataRow = dataTable.NewRow();
            dataRow["Param1"] = "RSSI";
            dataRow["Value1"] = apInfo.rssi;
            dataRow["Param2"] = "SSID";
            dataRow["Value2"] = apInfo.ssid;
            dataTable.Rows.Add(dataRow);

            dataRow = dataTable.NewRow();
            dataRow["Param1"] = "BSSID";
            dataRow["Value1"] = apInfo.bssid;
            dataRow["Param2"] = "SFI";
            dataRow["Value2"] = apInfo.sfi;
            dataTable.Rows.Add(dataRow);

            dataRow = dataTable.NewRow();
            dataRow["Param1"] = "C/I";
            dataRow["Value1"] = apInfo.ci;
            dataRow["Param2"] = "NFI";
            dataRow["Value2"] = apInfo.nfi;
            dataTable.Rows.Add(dataRow);

            dataRow = dataTable.NewRow();
            dataRow["Param1"] = "First Seen";
            dataRow["Value1"] = apInfo.firstSeen;
            dataRow["Param2"] = "Last Seen";
            dataRow["Value2"] = apInfo.lastSeen;
            dataTable.Rows.Add(dataRow);

            dataRow = dataTable.NewRow();
            dataRow["Param1"] = "Active Times(s)";
            dataRow["Value1"] = apInfo.activeTime;
            dataRow["Param2"] = "Rx(kb/s)";
            dataRow["Value2"] = apInfo.rx;
            dataTable.Rows.Add(dataRow);

            dataRow = dataTable.NewRow();
            dataRow["Param1"] = "Tx(kb/s)";
            dataRow["Value1"] = apInfo.tx;
            dataRow["Param2"] = "Band(%)";
            dataRow["Value2"] = apInfo.band;
            dataTable.Rows.Add(dataRow);

            dataRow = dataTable.NewRow();
            dataRow["Param1"] = @"A\B\G\N";
            dataRow["Value1"] = apInfo.abgn;
            dataRow["Param2"] = "Type";
            dataRow["Value2"] = apInfo.type;
            dataTable.Rows.Add(dataRow);

            dataRow = dataTable.NewRow();
            dataRow["Param1"] = "Encrytion";
            dataRow["Value1"] = apInfo.encrytion;
            dataRow["Param2"] = "Security";
            dataRow["Value2"] = apInfo.security;
            dataTable.Rows.Add(dataRow);

            dataRow = dataTable.NewRow();
            dataRow["Param1"] = "Bridge";
            dataRow["Value1"] = apInfo.bridge;
            dataRow["Param2"] = @"DCF\PCF";
            dataRow["Value2"] = apInfo.dcf_pcf;
            dataTable.Rows.Add(dataRow);

            dataRow = dataTable.NewRow();
            dataRow["Param1"] = "Beacon Interval(ms)";
            dataRow["Value1"] = apInfo.beaconInterval;
            dataRow["Param2"] = "Support Rate";
            dataRow["Value2"] = apInfo.supportRate;
            dataTable.Rows.Add(dataRow);

            dataRow = dataTable.NewRow();
            dataRow["Param1"] = "Max Rate(Mb)";
            dataRow["Value1"] = apInfo.maxRate;
            dataRow["Param2"] = "Preamble";
            dataRow["Value2"] = apInfo.preamble;
            dataTable.Rows.Add(dataRow);

            dataRow = dataTable.NewRow();
            dataRow["Param1"] = "Noise";
            dataRow["Value1"] = apInfo.noise;
            dataRow["Param2"] = "";
            dataRow["Value2"] = "";
            dataTable.Rows.Add(dataRow);

            gridControlDetail.DataSource = dataTable;
        }
    }
}