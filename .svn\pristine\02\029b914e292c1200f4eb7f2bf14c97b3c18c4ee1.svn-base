﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class SiteQuery : DIYStatQuery
    {
        private SiteQueryCond siteCond = null;

        public SiteQuery(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected override StatTbToken getBranchToken()
        {
            return StatTbToken.area;
        }

        public override string Name
        {
            get { return "地点路测情况分析"; }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 1300, 1301, this.Name);
        }

        protected override void query()
        {
            if (siteCond == null)
            {
                siteCond = new SiteQueryCond(this.Condition);
            }
            siteCond.SetQueryCond(this.Condition);

            SiteForm form = MainModel.GetObjectFromBlackboard(typeof(SiteForm)) as SiteForm;
            if (form == null || form.IsDisposed)
            {
                form = new SiteForm(siteCond);
            }
            form.Show(MainModel.MainForm);
        }
    }
}
