﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTPoorRSRQRoad;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public abstract class QueryPoorRsrqRoadBase : DIYAnalyseByFileBackgroundBase
    {
        protected abstract string themeName { get; }
        protected abstract string rsrpName { get; }
        protected abstract string sinrName { get; }
        protected abstract string rsrqName { get; }

        protected PoorRsrqRoadCondition poorCondition = new PoorRsrqRoadCondition();

        protected QueryPoorRsrqRoadBase()
            : base(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
        }

        public override string Name
        {
            get { return "低RSRQ路段"; }
        }

        protected override void fireShowForm()
        {
            if (poorRoadList.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }
            ResultForm frm = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ResultForm)) as ResultForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new ResultForm();
                frm.Owner = MainModel.MainForm;
            }

            resetTheme();
            frm.FillData(poorRoadList);
            frm.Visible = true;
            frm.BringToFront();
            poorRoadList = null;
        }

        protected virtual void resetTheme()
        {
            MainModel.FireSetDefaultMapSerialTheme(themeName);
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            SettingForm dlg = new SettingForm(poorCondition);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                poorCondition = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            poorRoadList = new List<PoorRsrqRoad>();
        }

        protected override void doStatWithQuery()
        {
            if (MainModel.DTDataManager.FileDataManagers.Count == 0)
            {
                return;
            }
            List<TestPoint> testPointList = MainModel.DTDataManager.FileDataManagers[0].TestPoints;
            PoorRsrqRoad poorRoad = null;
            TestPoint prePoint = null;//前一点
            for (int i = 0; i < testPointList.Count; i++)
            {
                TestPoint testPoint = testPointList[i];
                if (!isValidTestPoint(testPoint))
                {//区域外的采样点
                    saveWeakCoverInfo(ref poorRoad);
                }
                else
                {
                    poorRoad = addPoorRsrqRoad(poorRoad, prePoint, testPoint);
                    prePoint = testPoint;
                }
            }
            saveWeakCoverInfo(ref poorRoad);
        }

        private PoorRsrqRoad addPoorRsrqRoad(PoorRsrqRoad poorRoad, TestPoint prePoint, TestPoint testPoint)
        {
            float? rsrq = getRsrq(testPoint);
            float? rsrp = getRsrp(testPoint);
            float? sinr = getSinr(testPoint);
            if (!poorCondition.IsPoorRsrq(rsrq)) //先作指标的判断，后作距离的判断
            {//不符合设置的弱覆盖条件
                saveWeakCoverInfo(ref poorRoad);
            }
            else
            {
                //指标符合弱覆盖，还需要进行距离条件判断
                if (poorRoad == null) //弱覆盖开始
                {
                    poorRoad = initPoorRsrqRoad();
                    poorRoad.Add((float)rsrq, rsrp, sinr, 0, testPoint);
                }
                else
                {//上一点为弱覆盖点
                    double dis = prePoint.Distance2(testPoint);
                    if (poorCondition.Match2TestpointsMaxDistance(dis))
                    {//符合两采样点之间的距离门限
                        poorRoad.Add((float)rsrq, rsrp, sinr, dis, testPoint);
                    }
                    else
                    {//两采样点距离不符合，该点开始新的弱覆盖
                        saveWeakCoverInfo(ref poorRoad);
                        poorRoad = initPoorRsrqRoad();
                        poorRoad.Add((float)rsrq, rsrp, sinr, 0, testPoint);
                    }
                }
            }

            return poorRoad;
        }

        protected virtual PoorRsrqRoad initPoorRsrqRoad()
        { 
            return new PoorRsrqRoad();
        }

        protected virtual float? getSinr(TestPoint tp)
        {
            return (float?)tp[sinrName];
        }

        protected virtual float? getRsrp(TestPoint tp)
        {
            return (float?)tp[rsrpName];
        }

        protected virtual float? getRsrq(TestPoint tp)
        {
            return (float?)tp[rsrqName];
        }

        protected List<PoorRsrqRoad> poorRoadList = null;

        private void saveWeakCoverInfo(ref PoorRsrqRoad info)
        {
            if (info == null || !poorCondition.MatchMinPoorRsrqDistance(info.Distance))
            {//不符合最小持续距离
                info = null;
                return;
            }
            //save 2 list
            if (poorRoadList == null)
            {
                poorRoadList = new List<PoorRsrqRoad>();
            }
            if (!poorRoadList.Contains(info))
            {
                info.SN = poorRoadList.Count + 1;
                info.MakeSummary();
                poorRoadList.Add(info);
            }

            info = null;
        }
    }
}
