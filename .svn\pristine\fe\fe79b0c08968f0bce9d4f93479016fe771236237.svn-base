﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model.CellParam;
using MasterCom.RAMS.Model.CellParam.QueryByRegion;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public class QueryCellSignParamByRegion_TD : QueryCellSignParamByRegion
    {
        public QueryCellSignParamByRegion_TD(MainModel mm)
            : base(mm)
        {
        }
        public override string Name
        {
            get { return "按区域查询"; }
        }

        public override string IconName
        {
            get { return null; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 20000, 20030, this.Name);
        }

        protected new Dictionary<CellSign, object> cellSignCellDic = new Dictionary<CellSign, object>();
        protected override void getCellSigns()
        {
            cellSignCellDic.Clear();
            List<TDCell> cells = MainModel.MainForm.GetMapForm().GetTDCellLayer().CellsInCurrentView;
            if (cells == null||cells.Count==0)
            {
                cells = MainModel.MainForm.GetMapForm().GetTDCellLayer().GetCellsByRegion(MainModel.SearchGeometrys.GeoOp);
            }
            else
            {
                List<TDCell> regionCells = new List<TDCell>();
                foreach (TDCell cell in cells)
                {
                    if (MainModel.SearchGeometrys.GeoOp.Contains(cell.Longitude,cell.Latitude))
                    {
                        regionCells.Add(cell);
                    }
                }
                cells = regionCells;
            }
            if (cells != null)
            {
                addCellSignCellDic(cells);
            }
        }

        private void addCellSignCellDic(List<TDCell> cells)
        {
            TimePeriod period = new TimePeriod(dtBegin, dtEnd);
            foreach (TDCell cell in cells)
            {
                List<TDCellSign> signs = CellSignManager.GetInstance().GetCellSign(period, cell);
                if (signs != null && signs.Count > 0)
                {
                    foreach (CellSign sign in signs)
                    {
                        cellSignCellDic.Add(sign, cell);
                    }
                }
            }
        }
    }
}
