﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Func.SystemSetting;

namespace MasterCom.RAMS.ZTFunc
{
    public class CellSetByBackgroundQuery : DIYAnalyseByCellBackgroundBaseByPeriod
    {
        public BTSBandType bandType { get; set; } = BTSBandType.GSM900;
        public double RxlevMin { get; set; } = -95;
        private readonly Dictionary<Cell, BackgroundResult> cellResultDic = new Dictionary<Cell, BackgroundResult>();

        public static CellSetByBackgroundQuery GetInstance()
        {
            if (instance == null)
            {
                instance = new CellSetByBackgroundQuery();
            }
            return instance;
        }

        public override string Name
        {
            get { return "小区频点信息"; }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12082, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            DIYSampleGroup sampleGrp = new DIYSampleGroup();
            sampleGrp.ThemeName = "---";
            DTParameter parameter = DTParameterManager.GetInstance().GetParameter("LAC");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                sampleGrp.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("CI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                sampleGrp.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("BCCH");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                sampleGrp.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("BSIC");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                sampleGrp.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("RxLevSub");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                sampleGrp.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("RxQualSub");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                sampleGrp.ColumnsDefSet.Add(pDef);
            }

            return sampleGrp;
        }

        protected override void doSomethingBeforeQueryInThread()
        {
            cellResultDic.Clear();
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            if (!(tp is TestPointDetail) || !Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude))
            {
                return false;
            }

            short? rxlevSub = (short?)tp["RxLevSub"];
            Cell cell = tp.GetMainCell() as Cell;
            if (rxlevSub == null || (short)rxlevSub < this.RxlevMin || cell == null || cell.BandType != this.bandType)
            {
                return false;
            }

            BackgroundResult bResult = null;
            if (!cellResultDic.TryGetValue(cell, out bResult))
            {
                bResult = new BackgroundResult();
                bResult.SubFuncID = GetSubFuncID();
                bResult.SubFuncName = GetSubFuncIDString();
                bResult.CellType = BackgroundCellType.GSM;
                bResult.LAC = cell.LAC;
                bResult.CI = cell.CI;
                bResult.BCCH = cell.BCCH;
                bResult.BSIC = cell.BSIC;
                bResult.RxLevMin = bResult.RxLevMax = bResult.RxLevMean = (short)rxlevSub;
                bResult.SampleCount = 1;
                bResult.LongitudeMid = cell.Longitude;
                bResult.LatitudeMid = cell.Latitude;
                cellResultDic.Add(cell, bResult);
            }
            else
            {
                bResult.RxLevMax = Math.Max(bResult.RxLevMax, (short)rxlevSub);
                bResult.RxLevMin = Math.Min(bResult.RxLevMin, (short)rxlevSub);
                bResult.RxLevMean = (bResult.RxLevMean * bResult.SampleCount + (short)rxlevSub) / (bResult.SampleCount + 1);
                bResult.SampleCount += 1;
            }

            return true;
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.GSM业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.频点; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["RxlevMin"] = RxlevMin.ToString();
                param["BandType"] = bandType.ToString();
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("RxlevMin"))
                {
                    RxlevMin = double.Parse(param["RxlevMin"] as string);
                }
                if (param.ContainsKey("BandType"))
                {
                    bandType= (BTSBandType)Enum.Parse(typeof(BTSBandType), param["BandType"] as string);
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new CellSetByBackgroundProperties(this);
            }
        }

        protected override void saveBackgroundData()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>(cellResultDic.Values);
            foreach (BackgroundResult bResult in bgResultList)
            {
                bResult.ISTime = Condition.Periods[0].IBeginTime;
                bResult.IETime = Condition.Periods[0].IEndTime;
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(GetSubFuncID(), Condition.Periods[0].IBeginTime,
                Condition.Periods[0].IEndTime, bgResultList);
            cellResultDic.Clear();
        }
        #endregion

        private CellSetByBackgroundQuery()
            : base(MainModel.GetInstance())
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.GSM_VOICE);
        }

        private static CellSetByBackgroundQuery instance = null;
    }
}
