﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteHandOverFreqBandAnaDlg : MinCloseForm
    {
        LteHandOverFreqBandAnaCond condotion;

        public LteHandOverFreqBandAnaDlg()
        {
            InitializeComponent();

            freqBandSetting.FillData(FreqBandAnaClassType.BaseType);
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            freqBandSetting.SaveConfig();
            condotion = new LteHandOverFreqBandAnaCond();
            condotion.FreqBands = freqBandSetting.FreqBands;
            condotion.BeforeHandOverSec = (int)numBeforeSec.Value;
            condotion.AfterHandOverSec = (int)numAfterSec.Value;
            DialogResult = DialogResult.OK;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        public void SetCondiotion(LteHandOverFreqBandAnaCond condotion)
        {
            if (condotion != null)
            {
                numBeforeSec.Value = condotion.BeforeHandOverSec;
                numAfterSec.Value = condotion.AfterHandOverSec;
            }
        }

        public LteHandOverFreqBandAnaCond GetCondition()
        {
            return condotion;
        }
    }

    public class LteHandOverFreqBandAnaCond
    {
        public FreqBandList FreqBands { get; set; }
        public int BeforeHandOverSec { get; set; }
        public int AfterHandOverSec { get; set; }
    }
}
