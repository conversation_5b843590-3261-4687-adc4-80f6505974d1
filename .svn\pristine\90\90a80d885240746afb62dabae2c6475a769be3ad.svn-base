﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class ZTHightRSRPLowSINRQuery_NB : ZTHightRSRPLowSINRBaseQuery<ZTHightRSRPLowSINRQuery_NB>
    {
        protected override string themeName { get { return "TD_LTE_SINR"; } }
        protected override string rsrpName { get { return "lte_RSRP"; } }
        protected override string sinrName { get { return "lte_SINR"; } }

        public ZTHightRSRPLowSINRQuery_NB()
            : base()
        {
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NBIOT);
        }

        public override string Name
        {
            get { return "强信号弱质量_NB"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 34000, 34009, this.Name);
        }
    }
}
