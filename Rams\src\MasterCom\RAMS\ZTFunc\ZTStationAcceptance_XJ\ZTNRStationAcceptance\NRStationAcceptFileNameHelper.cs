﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public static class NRStationAcceptFileNameHelper
    {
        /// <summary>
        /// 判断文件业务类型
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public static NRServiceName GetServiceName(string fileName)
        {
            string[] strs = fileName.Split('_');
            if (strs.Length > 0)
            {
                string type = strs[0];
                if (type == "SA")
                {
                    return NRServiceName.SA;
                }
                if (type == "NSA")
                {
                    return NRServiceName.NSA;
                }
            }
            return NRServiceName.NULL;
        }

        /// <summary>
        /// 根据命名规则获取文件名中的基站名
        /// </summary>
        public static string GetFileBtsName(NRCell cell, string fileName, bool isHandoverFile, out string errMsg)
        {
            string btsName = "";
            string[] strs = fileName.Split('_');
            errMsg = "文件名格式不对";
            if (strs.Length < 4)
            {
                return "";
            }
            if (isHandoverFile)
            {
                errMsg = "";
                btsName = strs[3];
            }
            else
            {
                int idx = strs[3].LastIndexOf('-');
                if (idx > 0)
                {
                    errMsg = "";
                    btsName = strs[3].Substring(0, idx);
                }
                else
                {
                    errMsg = "文件名中对应小区格式不对";
                }
            }

            if (!cell.Name.Contains(btsName))
            {
                errMsg = "工参小区名不包含文件名";
                btsName = "";
            }

            return btsName;
        }

        /// <summary>
        /// 根据命名规则token = 业务 + 带宽 + 通道
        /// 例 : NSA_100_1
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public static string GetThroughputFileToken(string fileName, int lengthLimit)
        {
            string[] strs = fileName.Split('_');
            if (strs.Length >= lengthLimit)
            {
                string band = strs[strs.Length - 2];
                string num = strs[strs.Length - 1];

                //后缀可能存在多个[.],因此[.]后面的都不要
                int suffixIdx = num.IndexOf('.');
                if (suffixIdx > 0)
                {
                    num = num.Remove(suffixIdx);
                }

                string token = $"{strs[0]}_{band}_{num}";
                return token;
            }
            return "";
        }
    }
}
