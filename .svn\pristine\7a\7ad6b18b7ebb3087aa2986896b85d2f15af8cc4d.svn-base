﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRQualPnl
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.nrPoorSINRPnl1 = new MasterCom.RAMS.ZTFunc.NRPoorSINRPnl();
            this.SuspendLayout();
            // 
            // nrPoorSINRPnl1
            // 
            this.nrPoorSINRPnl1.Location = new System.Drawing.Point(3, 0);
            this.nrPoorSINRPnl1.Name = "nrPoorSINRPnl1";
            this.nrPoorSINRPnl1.Size = new System.Drawing.Size(416, 375);
            this.nrPoorSINRPnl1.TabIndex = 0;
            // 
            // NRQualPnl
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.nrPoorSINRPnl1);
            this.Name = "NRQualPnl";
            this.Size = new System.Drawing.Size(837, 380);
            this.ResumeLayout(false);

        }

        #endregion

        private NRPoorSINRPnl nrPoorSINRPnl1;
    }
}
