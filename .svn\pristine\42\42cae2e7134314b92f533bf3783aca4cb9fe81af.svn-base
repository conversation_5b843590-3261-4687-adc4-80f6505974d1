﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraCharts;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTDataCfgForm : BaseDialog
    {
        public ZTDataCfgForm(bool isShowCompare, string strNet)
        {
            InitializeComponent();
            this.strNet = strNet;
            intiData();
            cbkCompareStat.Enabled = cbkCompareStat.Checked = isShowCompare;
        }
        Dictionary<string, AntTimeCfg> timeCfgDic = new Dictionary<string, AntTimeCfg>();
        private string strNet;
        private void intiData()
        {
            DiyAntTimeCfg diyAntTimeCfg = new DiyAntTimeCfg(MainModel);
            diyAntTimeCfg.strNet = strNet;
            diyAntTimeCfg.Query();
            timeCfgDic = diyAntTimeCfg.timeCfgDic;

            string strMaxTime = "";
            int indexComparea = 1;
            int indexAll = timeCfgDic.Keys.Count;
            foreach (string strTime in timeCfgDic.Keys)
            {
                strMaxTime = strTime;
                cbTimeCfg.Items.Add(strTime);
                cbCompareTimeCfg.Items.Add(strTime);
                if (indexAll > 2 && indexComparea++ == indexAll - 1)
                {
                    cbCompareTimeCfg.Text = strMaxTime;
                }
            }
            cbTimeCfg.Text = strMaxTime;
        }

        public int iFunc { get; set; } = 1;
        public List<AntTimeCfg> timeCfgList { get; set; } = new List<AntTimeCfg>();
        public bool isCompareStat { get; set; } = true;
        private void btnOK_Click(object sender, EventArgs e)
        {
            AntTimeCfg timeCfg;
            string strDate = cbTimeCfg.Text;
            if (!timeCfgDic.TryGetValue(strDate,out timeCfg))
            {
                this.DialogResult = DialogResult.Cancel;
                return;
            }
            else
            {
                 timeCfgList.Add(timeCfg);
                 strDate = cbCompareTimeCfg.Text;
                 if (timeCfgDic.TryGetValue(strDate, out timeCfg))
                 {
                     timeCfgList.Add(timeCfg);
                 }
            }
            if (rbEasy.Checked)
                iFunc = 2;
            else if (rbDetail.Checked)
                iFunc = 3;
            else
                iFunc = 1;
            isCompareStat = cbkCompareStat.Checked;
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void rbAna_Click(object sender, EventArgs e)
        {
            rbAna.Checked = true;
            rbEasy.Checked = false;
            rbDetail.Checked = false;
        }

        private void rbEasy_Click(object sender, EventArgs e)
        {
            rbEasy.Checked = true;
            rbAna.Checked = false;
            rbDetail.Checked = false;
        }

        private void rbDetail_Click(object sender, EventArgs e)
        {
            rbDetail.Checked = true;
            rbEasy.Checked = false;
            rbAna.Checked = false;
        }

        private void cbkCompareStat_CheckedChanged(object sender, EventArgs e)
        {
            cbCompareTimeCfg.Enabled = cbkCompareStat.Checked;
        }
    }
}
