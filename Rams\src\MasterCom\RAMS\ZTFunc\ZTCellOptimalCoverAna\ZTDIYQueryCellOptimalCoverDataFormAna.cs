﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTDIYQueryCellOptimalCoverDataFormAna : MinCloseForm
    {
        public ZTDIYQueryCellOptimalCoverDataFormAna(MainModel mainModel)
            :base(mainModel)
        {
            InitializeComponent();
        }
        public void setData(List<OptimalCoverDataInfo> result)
        {
            this.gridOptimalCover.DataSource = result;
            this.gridOptimalCover.RefreshDataSource();
        }

        private void excelStripMenuItem_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(this.gridView1);
        }
    }
}
