﻿namespace MasterCom.RAMS.ZTFunc.ZTUltraSite
{
    partial class UltraSiteCellForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.tvList = new BrightIdeasSoftware.TreeListView();
            this.colMainType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colAreaName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colSite = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colCell = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colDisByDelaunay = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colDisByDir = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colProbInfo = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLng = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLat = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colOtherSite = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colOtherCell = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportXlsx = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.tvList)).BeginInit();
            this.ctxMenu.SuspendLayout();
            this.SuspendLayout();
            // 
            // tvList
            // 
            this.tvList.AllColumns.Add(this.colMainType);
            this.tvList.AllColumns.Add(this.colAreaName);
            this.tvList.AllColumns.Add(this.colSite);
            this.tvList.AllColumns.Add(this.colCell);
            this.tvList.AllColumns.Add(this.colDisByDelaunay);
            this.tvList.AllColumns.Add(this.colDisByDir);
            this.tvList.AllColumns.Add(this.colProbInfo);
            this.tvList.AllColumns.Add(this.colLng);
            this.tvList.AllColumns.Add(this.colLat);
            this.tvList.AllColumns.Add(this.colOtherSite);
            this.tvList.AllColumns.Add(this.colOtherCell);
            this.tvList.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.colMainType,
            this.colAreaName,
            this.colSite,
            this.colCell,
            this.colDisByDelaunay,
            this.colDisByDir,
            this.colProbInfo,
            this.colLng,
            this.colLat,
            this.colOtherSite,
            this.colOtherCell});
            this.tvList.ContextMenuStrip = this.ctxMenu;
            this.tvList.Cursor = System.Windows.Forms.Cursors.Default;
            this.tvList.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tvList.FullRowSelect = true;
            this.tvList.GridLines = true;
            this.tvList.HeaderWordWrap = true;
            this.tvList.IsNeedShowOverlay = false;
            this.tvList.Location = new System.Drawing.Point(0, 0);
            this.tvList.Name = "tvList";
            this.tvList.OwnerDraw = true;
            this.tvList.ShowGroups = false;
            this.tvList.Size = new System.Drawing.Size(1013, 365);
            this.tvList.TabIndex = 13;
            this.tvList.UseCompatibleStateImageBehavior = false;
            this.tvList.View = System.Windows.Forms.View.Details;
            this.tvList.VirtualMode = true;
            this.tvList.DoubleClick += new System.EventHandler(this.tvList_DoubleClick);
            // 
            // colMainType
            // 
            this.colMainType.HeaderFont = null;
            this.colMainType.Text = "类别";
            this.colMainType.Width = 102;
            // 
            // colAreaName
            // 
            this.colAreaName.HeaderFont = null;
            this.colAreaName.Text = "所属区域";
            // 
            // colSite
            // 
            this.colSite.HeaderFont = null;
            this.colSite.Text = "站点";
            this.colSite.Width = 120;
            // 
            // colCell
            // 
            this.colCell.HeaderFont = null;
            this.colCell.Text = "小区";
            this.colCell.Width = 120;
            // 
            // colDisByDelaunay
            // 
            this.colDisByDelaunay.HeaderFont = null;
            this.colDisByDelaunay.Text = "Delaunay站间距";
            // 
            // colDisByDir
            // 
            this.colDisByDir.HeaderFont = null;
            this.colDisByDir.Text = "站间距(方向角)";
            // 
            // colProbInfo
            // 
            this.colProbInfo.HeaderFont = null;
            this.colProbInfo.Text = "问题信息";
            this.colProbInfo.Width = 80;
            // 
            // colLng
            // 
            this.colLng.HeaderFont = null;
            this.colLng.Text = "经度";
            this.colLng.Width = 80;
            // 
            // colLat
            // 
            this.colLat.HeaderFont = null;
            this.colLat.Text = "纬度";
            this.colLat.Width = 80;
            // 
            // colOtherSite
            // 
            this.colOtherSite.HeaderFont = null;
            this.colOtherSite.Text = "相关站点";
            this.colOtherSite.Width = 200;
            // 
            // colOtherCell
            // 
            this.colOtherCell.HeaderFont = null;
            this.colOtherCell.Text = "相关小区";
            this.colOtherCell.Width = 200;
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportXlsx});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(139, 26);
            // 
            // miExportXlsx
            // 
            this.miExportXlsx.Name = "miExportXlsx";
            this.miExportXlsx.Size = new System.Drawing.Size(138, 22);
            this.miExportXlsx.Text = "导出Excel...";
            this.miExportXlsx.Click += new System.EventHandler(this.miExportXlsx_Click);
            // 
            // UltraSiteCellForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1013, 365);
            this.Controls.Add(this.tvList);
            this.Name = "UltraSiteCellForm";
            this.Text = "三超站点";
            ((System.ComponentModel.ISupportInitialize)(this.tvList)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView tvList;
        private BrightIdeasSoftware.OLVColumn colMainType;
        private BrightIdeasSoftware.OLVColumn colSite;
        private BrightIdeasSoftware.OLVColumn colCell;
        private BrightIdeasSoftware.OLVColumn colLng;
        private BrightIdeasSoftware.OLVColumn colLat;
        private BrightIdeasSoftware.OLVColumn colOtherSite;
        private BrightIdeasSoftware.OLVColumn colOtherCell;
        private BrightIdeasSoftware.OLVColumn colProbInfo;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportXlsx;
        private BrightIdeasSoftware.OLVColumn colDisByDelaunay;
        private BrightIdeasSoftware.OLVColumn colAreaName;
        private BrightIdeasSoftware.OLVColumn colDisByDir;
    }
}