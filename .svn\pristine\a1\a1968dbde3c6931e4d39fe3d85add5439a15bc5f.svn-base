﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.Func.NebulaForm
{
    public partial class SettingScaleForm : MinCloseForm
    {
        private List<ScaleItem> listItem;
        private float min, max;
        public SettingScaleForm(List<ScaleItem> listItem, float min, float max) 
        {
            InitializeComponent();
            this.listBoxItem.SelectionMode = SelectionMode.One;
            this.spinEditDevide.Properties.IsFloatValue = false;
            this.spinEditDevide.Properties.Increment = 1;
            this.spinEditDevide.Properties.MinValue = 1;
            this.spinEditDevide.Properties.MaxValue = 10000;

            this.listItem = listItem;
            this.min = min;
            this.max = max;
            XYData.AdjustScaleItem(this.listItem, this.min, this.max);
            this.refreshItem();
        }

        private void refreshItem()
        {
            this.listBoxItem.Items.Clear();
            this.listItem.Sort(this.compare);
            foreach (ScaleItem item in this.listItem)
            {
                this.listBoxItem.Items.Add(item);
            }
        }
        private int compare(ScaleItem x, ScaleItem y)
        {
            float re = x.Val - y.Val;
            if (re < 0) return -1;
            if (re == 0) return 0;
            else return 1;
        }

        private void buttonAdd_Click(object sender, EventArgs e)
        { 
            SettingAddItemForm adder = new SettingAddItemForm(this.min, this.max);
            if (adder.ShowDialog() != System.Windows.Forms.DialogResult.OK) return;

            ScaleItem item = adder.GetResult();
            for (int i = 0; i < this.listItem.Count; i++)
            {
                ScaleItem si = this.listItem[i];
                if (si.Val < item.Val) continue;
                if (si.Val == item.Val) return;
                this.listItem.Insert(i,item);
                this.refreshItem();
                return;
            }
        }

        private void buttonDel_Click(object sender, EventArgs e)
        {
            int index = this.listBoxItem.SelectedIndex;
            if (index <= 0
                || index >= this.listBoxItem.Items.Count - 1)
            {
                return;
            }
            this.listItem.RemoveAt(index);
            this.listBoxItem.Items.Clear();
            this.refreshItem();
            this.listBoxItem.SelectedIndex = index;
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }

        private void button1_Click(object sender, EventArgs e)
        {
            int devNum = (int)this.spinEditDevide.Value;
            if (devNum < 1) return;
            this.listItem.Clear();
            float step = (this.max - this.min) / devNum;
            for (int i = 0; i < devNum; i++)
            {
                this.listItem.Add(new ScaleItem(this.min + step * i));
            }
            this.listItem.Add(new ScaleItem(this.max));
            this.refreshItem();
        } 
    }

    public class ScaleItem
    {
        public float Val { get; set; }

        public ScaleItem(float val)
        {
            this.Val = val;
        }
        public override string ToString()
        {
            return Math.Round(this.Val,2).ToString();
        }
    }
}
