﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using System.Reflection;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using Excel = Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.ZTFunc.ZTLTEClusterAna
{
    public partial class ResultForm : MinCloseForm
    {
        public ResultForm(MainModel mModel)
            : base(mModel)
        {
            InitializeComponent();
        }

        private List<ClusterResult> Results;
        public void FillData(List<ClusterResult> results)
        {
            if(results == null)
            {
                return;
            }
            this.gridControl1.DataSource = results;
            Results = results;
            this.gridControl1.RefreshDataSource();
            MyCellMergeHelper helper = new MyCellMergeHelper(this.gridView1);
            helper.AddMergedCell(2, 1, 2, "网络覆盖指标");
            helper.AddMergedCell(2, 2, 3, "网络覆盖指标");
            helper.AddMergedCell(2, 3, 4, "网络覆盖指标");
            helper.AddMergedCell(2, 4, 5, "网络覆盖指标");

            helper.AddMergedCell(9, 1, 2, "网络性能指标");
            helper.AddMergedCell(9, 2, 3, "网络性能指标");
            helper.AddMergedCell(9, 3, 4, "网络性能指标");
            helper.AddMergedCell(9, 4, 5, "网络性能指标");

        }

        private void 导出ExcelToolStripMenuItem_Click(object sender, EventArgs e)
        {
            #region 
            //List<NPOIRow> table = new List<NPOIRow>();
            //foreach (ClusterResult result in Results)
            //{
            //    NPOIRow row = new NPOIRow();
            //    row.AddCellValue(result.ColumnA);
            //    row.AddCellValue(result.ColumnB);
            //    row.AddCellValue(result.ColumnC);
            //    row.AddCellValue(result.ColumnD);
            //    row.AddCellValue(result.ColumnE);
            //    row.AddCellValue(result.ColumnF);
            //    table.Add(row);
            //}
            //doExport(table, "sheet");
            #endregion 

            #region Dv自带导出excel
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = true;
            dlg.Filter = "Excel文件(*.xlsx)|*.xlsx";
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                string filename = dlg.FileName;
                this.gridControl1.ExportToXlsx(filename);

                Microsoft.Office.Interop.Excel.Application excel = new Microsoft.Office.Interop.Excel.Application();  //Execl的操作类
                Microsoft.Office.Interop.Excel.Workbook bookDest = excel.Workbooks.Open(filename,
            Type.Missing, Type.Missing, Type.Missing, Type.Missing,
            Type.Missing, Type.Missing, Type.Missing, Type.Missing,
            Type.Missing, Type.Missing, Type.Missing, Type.Missing,
            Type.Missing, Type.Missing);
                try
                {

                   
                    Microsoft.Office.Interop.Excel.Worksheet sheetDest = bookDest.ActiveSheet as Microsoft.Office.Interop.Excel.Worksheet;


                    

                    Microsoft.Office.Interop.Excel.Range ran1 = sheetDest.get_Range(sheetDest.Cells[4, 2], sheetDest.Cells[4, 6]);
                    ran1.ClearContents();
                    ran1.MergeCells = true;
                    ran1.Merge(ran1.MergeCells);//合并单元格,网络覆盖指标
                    ran1.HorizontalAlignment = Microsoft.Office.Interop.Excel.XlHAlign.xlHAlignCenter;
                    ran1.Value = "网络覆盖指标";
                    Microsoft.Office.Interop.Excel.Range ran2 = sheetDest.get_Range(sheetDest.Cells[11, 2], sheetDest.Cells[11, 6]);
                    ran2.ClearContents();
                    ran2.Merge(ran2.MergeCells);//合并单元格,网络性能指标
                    ran2.HorizontalAlignment = Microsoft.Office.Interop.Excel.XlHAlign.xlHAlignCenter;
                    ran2.Value = "网络性能指标";

                    //设置禁止弹出保存和覆盖的询问提示框
                    excel.DisplayAlerts = false;

                    bookDest.SaveAs(filename);//保存

                }
                catch
                {
                    //异常时，处理代码
                }
                finally
                {
                    if (excel != null)
                        excel.Quit();//退出Excel
                    //excel = null;
                }
                DevExpress.XtraEditors.XtraMessageBox.Show(" 导出成功！ ", " 提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            #endregion
        }

        public void doExport(List<NPOIRow> nrDatasList, string strSheetName)
        {
            SaveFileDialog saveDialog = new SaveFileDialog();
            saveDialog.Filter = "xlsx文件(*.xls)|*.xls";
            saveDialog.RestoreDirectory = true;
            if (saveDialog.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            string fileName = saveDialog.FileName;
            Microsoft.Office.Interop.Excel.Application excel = new Microsoft.Office.Interop.Excel.Application();  //Execl的操作类
            Microsoft.Office.Interop.Excel.Workbook bookDest = excel.Workbooks.Add(Missing.Value);
            Microsoft.Office.Interop.Excel.Worksheet sheetDest = bookDest.Worksheets.Add(Missing.Value, Missing.Value, Missing.Value, Missing.Value) as Microsoft.Office.Interop.Excel.Worksheet;//给工作薄添加一个Sheet   
            sheetDest.Name = strSheetName;
            for (int i = bookDest.Worksheets.Count; i > 1; i--)
            {
                Microsoft.Office.Interop.Excel.Worksheet wt = (Microsoft.Office.Interop.Excel.Worksheet)bookDest.Worksheets[i];
                if (wt.Name != strSheetName)
                {
                    wt.Delete();
                }
            }
            try
            {
                excel.Application.Workbooks.Add(true);
                int row = 1;
                foreach (NPOIRow npoiRow in nrDatasList)
                {
                    Microsoft.Office.Interop.Excel.Range cell1ran = sheetDest.get_Range(sheetDest.Cells[row, 1], sheetDest.Cells[row++, 6]);
                    cell1ran.Value2 = npoiRow.cellValues.ToArray();
                }

                Microsoft.Office.Interop.Excel.Range ran1 = sheetDest.get_Range(sheetDest.Cells[3, 2], sheetDest.Cells[3, 6]);
                ran1.ClearContents();
                ran1.Merge(ran1.MergeCells);//合并单元格,网络覆盖指标
                ran1.HorizontalAlignment = Microsoft.Office.Interop.Excel.XlHAlign.xlHAlignCenter;
                ran1.Value = "网络覆盖指标";
                Microsoft.Office.Interop.Excel.Range ran2 = sheetDest.get_Range(sheetDest.Cells[10, 2], sheetDest.Cells[10, 6]);
                ran2.ClearContents();
                ran2.Merge(ran2.MergeCells);//合并单元格,网络性能指标
                ran2.HorizontalAlignment = Microsoft.Office.Interop.Excel.XlHAlign.xlHAlignCenter;
                ran2.Value = "网络性能指标";

                Microsoft.Office.Interop.Excel.Range ran3 = sheetDest.get_Range(sheetDest.Cells[3, 1], sheetDest.Cells[22, 1]);
                ran3.ClearContents();
                ran3.Merge(ran3.MergeCells);//合并单元格,场景
                ran3.HorizontalAlignment = Microsoft.Office.Interop.Excel.XlHAlign.xlHAlignCenter;
                ran3.Value =Results[3].ColumnA;

                Microsoft.Office.Interop.Excel.Range ran4 = sheetDest.get_Range(sheetDest.Cells[4, 2], sheetDest.Cells[6, 2]);
                ran4.ClearContents();
                ran4.Merge(ran4.MergeCells);//合并单元格,场景
                ran4.HorizontalAlignment = Microsoft.Office.Interop.Excel.XlHAlign.xlHAlignCenter;
                ran4.Value = Results[3].ColumnB;

                Microsoft.Office.Interop.Excel.Range ran5 = sheetDest.get_Range(sheetDest.Cells[7, 2], sheetDest.Cells[9, 2]);
                ran5.ClearContents();
                ran5.Merge(ran5.MergeCells);//合并单元格,场景
                ran5.HorizontalAlignment = Microsoft.Office.Interop.Excel.XlHAlign.xlHAlignCenter;
                ran5.Value = Results[6].ColumnB;

                Microsoft.Office.Interop.Excel.Range ran6 = sheetDest.get_Range(sheetDest.Cells[11, 2], sheetDest.Cells[13, 2]);
                ran6.ClearContents();
                ran6.Merge(ran6.MergeCells);//合并单元格,场景
                ran6.HorizontalAlignment = Microsoft.Office.Interop.Excel.XlHAlign.xlHAlignCenter;
                ran6.Value = Results[10].ColumnB;

                Microsoft.Office.Interop.Excel.Range ran7 = sheetDest.get_Range(sheetDest.Cells[14, 2], sheetDest.Cells[18, 2]);
                ran7.ClearContents();
                ran7.Merge(ran7.MergeCells);//合并单元格,场景
                ran7.HorizontalAlignment = Microsoft.Office.Interop.Excel.XlHAlign.xlHAlignCenter;
                ran7.Value = Results[13].ColumnB;

                Microsoft.Office.Interop.Excel.Range ran8 = sheetDest.get_Range(sheetDest.Cells[19, 2], sheetDest.Cells[20, 2]);
                ran8.ClearContents();
                ran8.Merge(ran8.MergeCells);//合并单元格,场景
                ran8.HorizontalAlignment = Microsoft.Office.Interop.Excel.XlHAlign.xlHAlignCenter;
                ran8.Value = Results[18].ColumnB;

                bookDest.Saved = true;
                bookDest.SaveCopyAs(fileName);//保存
                MessageBox.Show("导出成功！");
            }
            catch (Exception w)
            {
                MessageBox.Show("导出异常:" + w.Message);
            }
            finally
            {
                excel.Quit();
                //excel = null;
                //GC.Collect();//垃圾回收   
            }
        }


    }
}
