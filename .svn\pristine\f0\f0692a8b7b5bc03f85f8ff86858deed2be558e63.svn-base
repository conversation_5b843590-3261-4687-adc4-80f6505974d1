﻿using System;
using System.Collections.Generic;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTSQLReportEventInfoEdit_QH : DIYSQLBase
    {
        public ZTSQLReportEventInfoEdit_QH(MainModel mainModel, ZTReportEventInfo_QH reportEventInfo)
            : base(mainModel)
        {
            this.reportEventInfo = reportEventInfo;
        }

        private readonly ZTReportEventInfo_QH reportEventInfo;

        public ZTReportEventInfo_QH GetReportEventInfo()
        {
            return reportEventInfo;
        }

        protected override string getSqlTextString()
        {
            int rectify = reportEventInfo.IsRectify ? 1 : 0;
            int retest = reportEventInfo.IsReTest ? 1 : 0;
            int isKeyProb = reportEventInfo.IsKeyProb ? 1 : 0;
            return string.Format("exec mc_sp_beijing_report_event_edit {0},{1},'{2}','{3}','{4}','{5}',{6},{7},{8},'{9}','{10}';"
                  , reportEventInfo.FileID, reportEventInfo.SeqID, reportEventInfo.Cause, reportEventInfo.CauseDetail
                  , reportEventInfo.Method, reportEventInfo.Solution, rectify, retest, isKeyProb, reportEventInfo.OptEffect
                  , reportEventInfo.DwUserName);
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int index = 0;
            E_VType[] rType = new E_VType[1];
            rType[index] = E_VType.E_Int;

            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    int result = 0;
                    result = package.Content.GetParamInt();
                    if (result == 1)
                    {
                        //更新成功
                    }
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        public override string Name
        {
            get { return "ZTSQLReportEventInfoEdit_QH"; }
        }
    }
}
