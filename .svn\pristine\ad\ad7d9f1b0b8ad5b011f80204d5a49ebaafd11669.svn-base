﻿namespace MasterCom.RAMS.CQT
{
    partial class CQTKPIColumnPanel_PK
    {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.grpPK = new DevExpress.XtraEditors.GroupControl();
            this.navBarControl1 = new DevExpress.XtraNavBar.NavBarControl();
            this.navBarGroup1 = new DevExpress.XtraNavBar.NavBarGroup();
            this.navBarGroupControlContainer1 = new DevExpress.XtraNavBar.NavBarGroupControlContainer();
            this.btnEditF1 = new DevExpress.XtraEditors.SimpleButton();
            this.txtFormula1 = new System.Windows.Forms.TextBox();
            this.scoreColorRangeSettingPanel1 = new MasterCom.RAMS.Util.ScoreColorRangeSettingPanel();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.radioGroup1 = new DevExpress.XtraEditors.RadioGroup();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.seKPIValueMax1 = new DevExpress.XtraEditors.SpinEdit();
            this.seKPIVlaueMin1 = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.navBarGroupControlContainer2 = new DevExpress.XtraNavBar.NavBarGroupControlContainer();
            this.btnEidtF2 = new DevExpress.XtraEditors.SimpleButton();
            this.scoreColorRangeSettingPanel2 = new MasterCom.RAMS.Util.ScoreColorRangeSettingPanel();
            this.txtFormula2 = new System.Windows.Forms.TextBox();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            this.radioGroup2 = new DevExpress.XtraEditors.RadioGroup();
            this.seKPIValueMax2 = new DevExpress.XtraEditors.SpinEdit();
            this.seKPIValueMin2 = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl14 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl16 = new DevExpress.XtraEditors.LabelControl();
            this.navBarGroup2 = new DevExpress.XtraNavBar.NavBarGroup();
            this.panel1 = new System.Windows.Forms.Panel();
            this.pkScoreColorRangeSettingPanel = new MasterCom.RAMS.CQT.ScoreRangeColorSettingPanel();
            this.textEditColunmName = new DevExpress.XtraEditors.TextEdit();
            this.labelControl17 = new DevExpress.XtraEditors.LabelControl();
            this.seScoreRangeMax = new DevExpress.XtraEditors.SpinEdit();
            this.seScoreRangeMin = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.grpPK)).BeginInit();
            this.grpPK.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.navBarControl1)).BeginInit();
            this.navBarControl1.SuspendLayout();
            this.navBarGroupControlContainer1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroup1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.seKPIValueMax1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.seKPIVlaueMin1.Properties)).BeginInit();
            this.navBarGroupControlContainer2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroup2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.seKPIValueMax2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.seKPIValueMin2.Properties)).BeginInit();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.textEditColunmName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.seScoreRangeMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.seScoreRangeMin.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // grpPK
            // 
            this.grpPK.Controls.Add(this.navBarControl1);
            this.grpPK.Controls.Add(this.panel1);
            this.grpPK.Dock = System.Windows.Forms.DockStyle.Top;
            this.grpPK.Location = new System.Drawing.Point(0, 0);
            this.grpPK.MinimumSize = new System.Drawing.Size(654, 176);
            this.grpPK.Name = "grpPK";
            this.grpPK.Size = new System.Drawing.Size(654, 655);
            this.grpPK.TabIndex = 5;
            this.grpPK.Text = "显示与打分";
            // 
            // navBarControl1
            // 
            this.navBarControl1.ActiveGroup = this.navBarGroup1;
            this.navBarControl1.Controls.Add(this.navBarGroupControlContainer1);
            this.navBarControl1.Controls.Add(this.navBarGroupControlContainer2);
            this.navBarControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.navBarControl1.Groups.AddRange(new DevExpress.XtraNavBar.NavBarGroup[] {
            this.navBarGroup1,
            this.navBarGroup2});
            this.navBarControl1.Location = new System.Drawing.Point(2, 174);
            this.navBarControl1.Name = "navBarControl1";
            this.navBarControl1.OptionsNavPane.ExpandedWidth = 650;
            this.navBarControl1.Size = new System.Drawing.Size(650, 479);
            this.navBarControl1.TabIndex = 6;
            this.navBarControl1.Text = "navBarControl1";
            // 
            // navBarGroup1
            // 
            this.navBarGroup1.Caption = "对比指标1";
            this.navBarGroup1.ControlContainer = this.navBarGroupControlContainer1;
            this.navBarGroup1.Expanded = true;
            this.navBarGroup1.GroupClientHeight = 212;
            this.navBarGroup1.GroupStyle = DevExpress.XtraNavBar.NavBarGroupStyle.ControlContainer;
            this.navBarGroup1.Name = "navBarGroup1";
            // 
            // navBarGroupControlContainer1
            // 
            this.navBarGroupControlContainer1.Controls.Add(this.btnEditF1);
            this.navBarGroupControlContainer1.Controls.Add(this.txtFormula1);
            this.navBarGroupControlContainer1.Controls.Add(this.scoreColorRangeSettingPanel1);
            this.navBarGroupControlContainer1.Controls.Add(this.labelControl11);
            this.navBarGroupControlContainer1.Controls.Add(this.radioGroup1);
            this.navBarGroupControlContainer1.Controls.Add(this.labelControl1);
            this.navBarGroupControlContainer1.Controls.Add(this.labelControl9);
            this.navBarGroupControlContainer1.Controls.Add(this.seKPIValueMax1);
            this.navBarGroupControlContainer1.Controls.Add(this.seKPIVlaueMin1);
            this.navBarGroupControlContainer1.Controls.Add(this.labelControl7);
            this.navBarGroupControlContainer1.Name = "navBarGroupControlContainer1";
            this.navBarGroupControlContainer1.Size = new System.Drawing.Size(646, 210);
            this.navBarGroupControlContainer1.TabIndex = 0;
            // 
            // btnEditF1
            // 
            this.btnEditF1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnEditF1.Location = new System.Drawing.Point(585, 13);
            this.btnEditF1.Name = "btnEditF1";
            this.btnEditF1.Size = new System.Drawing.Size(48, 23);
            this.btnEditF1.TabIndex = 6;
            this.btnEditF1.Text = "编辑器";
            this.btnEditF1.Click += new System.EventHandler(this.btnEditF1_Click);
            // 
            // txtFormula1
            // 
            this.txtFormula1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtFormula1.Location = new System.Drawing.Point(63, 12);
            this.txtFormula1.Multiline = true;
            this.txtFormula1.Name = "txtFormula1";
            this.txtFormula1.Size = new System.Drawing.Size(516, 45);
            this.txtFormula1.TabIndex = 5;
            // 
            // scoreColorRangeSettingPanel1
            // 
            this.scoreColorRangeSettingPanel1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.scoreColorRangeSettingPanel1.IsSmoothScore = true;
            this.scoreColorRangeSettingPanel1.Location = new System.Drawing.Point(263, 63);
            this.scoreColorRangeSettingPanel1.Name = "scoreColorRangeSettingPanel1";
            this.scoreColorRangeSettingPanel1.Size = new System.Drawing.Size(370, 146);
            this.scoreColorRangeSettingPanel1.TabIndex = 2;
            // 
            // labelControl11
            // 
            this.labelControl11.Location = new System.Drawing.Point(8, 116);
            this.labelControl11.Name = "labelControl11";
            this.labelControl11.Size = new System.Drawing.Size(60, 14);
            this.labelControl11.TabIndex = 0;
            this.labelControl11.Text = "评分规则：";
            // 
            // radioGroup1
            // 
            this.radioGroup1.Location = new System.Drawing.Point(74, 100);
            this.radioGroup1.Name = "radioGroup1";
            this.radioGroup1.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "正向打分（指标值大分数高）"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "反向打分（指标值大分数低）")});
            this.radioGroup1.Size = new System.Drawing.Size(177, 50);
            this.radioGroup1.TabIndex = 4;
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(21, 13);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(36, 14);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "公式：";
            // 
            // labelControl9
            // 
            this.labelControl9.Location = new System.Drawing.Point(8, 71);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(60, 14);
            this.labelControl9.TabIndex = 0;
            this.labelControl9.Text = "指标值域：";
            // 
            // seKPIValueMax1
            // 
            this.seKPIValueMax1.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.seKPIValueMax1.Location = new System.Drawing.Point(185, 68);
            this.seKPIValueMax1.Name = "seKPIValueMax1";
            this.seKPIValueMax1.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.seKPIValueMax1.Size = new System.Drawing.Size(66, 21);
            this.seKPIValueMax1.TabIndex = 3;
            // 
            // seKPIVlaueMin1
            // 
            this.seKPIVlaueMin1.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.seKPIVlaueMin1.Location = new System.Drawing.Point(74, 68);
            this.seKPIVlaueMin1.Name = "seKPIVlaueMin1";
            this.seKPIVlaueMin1.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.seKPIVlaueMin1.Size = new System.Drawing.Size(66, 21);
            this.seKPIVlaueMin1.TabIndex = 3;
            // 
            // labelControl7
            // 
            this.labelControl7.Location = new System.Drawing.Point(149, 71);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(24, 14);
            this.labelControl7.TabIndex = 0;
            this.labelControl7.Text = "≤x≤";
            // 
            // navBarGroupControlContainer2
            // 
            this.navBarGroupControlContainer2.Controls.Add(this.btnEidtF2);
            this.navBarGroupControlContainer2.Controls.Add(this.scoreColorRangeSettingPanel2);
            this.navBarGroupControlContainer2.Controls.Add(this.txtFormula2);
            this.navBarGroupControlContainer2.Controls.Add(this.labelControl12);
            this.navBarGroupControlContainer2.Controls.Add(this.radioGroup2);
            this.navBarGroupControlContainer2.Controls.Add(this.seKPIValueMax2);
            this.navBarGroupControlContainer2.Controls.Add(this.seKPIValueMin2);
            this.navBarGroupControlContainer2.Controls.Add(this.labelControl2);
            this.navBarGroupControlContainer2.Controls.Add(this.labelControl14);
            this.navBarGroupControlContainer2.Controls.Add(this.labelControl16);
            this.navBarGroupControlContainer2.Name = "navBarGroupControlContainer2";
            this.navBarGroupControlContainer2.Size = new System.Drawing.Size(646, 210);
            this.navBarGroupControlContainer2.TabIndex = 1;
            // 
            // btnEidtF2
            // 
            this.btnEidtF2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnEidtF2.Location = new System.Drawing.Point(585, 3);
            this.btnEidtF2.Name = "btnEidtF2";
            this.btnEidtF2.Size = new System.Drawing.Size(48, 23);
            this.btnEidtF2.TabIndex = 6;
            this.btnEidtF2.Text = "编辑器";
            this.btnEidtF2.Click += new System.EventHandler(this.btnEidtF2_Click);
            // 
            // scoreColorRangeSettingPanel2
            // 
            this.scoreColorRangeSettingPanel2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.scoreColorRangeSettingPanel2.IsSmoothScore = true;
            this.scoreColorRangeSettingPanel2.Location = new System.Drawing.Point(263, 61);
            this.scoreColorRangeSettingPanel2.Name = "scoreColorRangeSettingPanel2";
            this.scoreColorRangeSettingPanel2.Size = new System.Drawing.Size(370, 146);
            this.scoreColorRangeSettingPanel2.TabIndex = 2;
            // 
            // txtFormula2
            // 
            this.txtFormula2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtFormula2.Location = new System.Drawing.Point(63, 3);
            this.txtFormula2.Multiline = true;
            this.txtFormula2.Name = "txtFormula2";
            this.txtFormula2.Size = new System.Drawing.Size(516, 45);
            this.txtFormula2.TabIndex = 5;
            // 
            // labelControl12
            // 
            this.labelControl12.Location = new System.Drawing.Point(8, 151);
            this.labelControl12.Name = "labelControl12";
            this.labelControl12.Size = new System.Drawing.Size(60, 14);
            this.labelControl12.TabIndex = 0;
            this.labelControl12.Text = "评分规则：";
            // 
            // radioGroup2
            // 
            this.radioGroup2.Location = new System.Drawing.Point(74, 135);
            this.radioGroup2.Name = "radioGroup2";
            this.radioGroup2.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "正向打分（指标值大分数高）"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "反向打分（指标值大分数低）")});
            this.radioGroup2.Size = new System.Drawing.Size(177, 50);
            this.radioGroup2.TabIndex = 4;
            // 
            // seKPIValueMax2
            // 
            this.seKPIValueMax2.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.seKPIValueMax2.Location = new System.Drawing.Point(185, 84);
            this.seKPIValueMax2.Name = "seKPIValueMax2";
            this.seKPIValueMax2.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.seKPIValueMax2.Size = new System.Drawing.Size(66, 21);
            this.seKPIValueMax2.TabIndex = 3;
            // 
            // seKPIValueMin2
            // 
            this.seKPIValueMin2.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.seKPIValueMin2.Location = new System.Drawing.Point(74, 84);
            this.seKPIValueMin2.Name = "seKPIValueMin2";
            this.seKPIValueMin2.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.seKPIValueMin2.Size = new System.Drawing.Size(66, 21);
            this.seKPIValueMin2.TabIndex = 3;
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(14, 3);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(36, 14);
            this.labelControl2.TabIndex = 0;
            this.labelControl2.Text = "公式：";
            // 
            // labelControl14
            // 
            this.labelControl14.Location = new System.Drawing.Point(8, 87);
            this.labelControl14.Name = "labelControl14";
            this.labelControl14.Size = new System.Drawing.Size(60, 14);
            this.labelControl14.TabIndex = 0;
            this.labelControl14.Text = "指标值域：";
            // 
            // labelControl16
            // 
            this.labelControl16.Location = new System.Drawing.Point(149, 87);
            this.labelControl16.Name = "labelControl16";
            this.labelControl16.Size = new System.Drawing.Size(24, 14);
            this.labelControl16.TabIndex = 0;
            this.labelControl16.Text = "≤x≤";
            // 
            // navBarGroup2
            // 
            this.navBarGroup2.Caption = "对比指标2";
            this.navBarGroup2.ControlContainer = this.navBarGroupControlContainer2;
            this.navBarGroup2.Expanded = true;
            this.navBarGroup2.GroupClientHeight = 212;
            this.navBarGroup2.GroupStyle = DevExpress.XtraNavBar.NavBarGroupStyle.ControlContainer;
            this.navBarGroup2.Name = "navBarGroup2";
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.pkScoreColorRangeSettingPanel);
            this.panel1.Controls.Add(this.textEditColunmName);
            this.panel1.Controls.Add(this.labelControl17);
            this.panel1.Controls.Add(this.seScoreRangeMax);
            this.panel1.Controls.Add(this.seScoreRangeMin);
            this.panel1.Controls.Add(this.labelControl5);
            this.panel1.Controls.Add(this.labelControl3);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Location = new System.Drawing.Point(2, 23);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(650, 151);
            this.panel1.TabIndex = 7;
            // 
            // pkScoreColorRangeSettingPanel
            // 
            this.pkScoreColorRangeSettingPanel.DescColumnsVisible = true;
            this.pkScoreColorRangeSettingPanel.Dock = System.Windows.Forms.DockStyle.Right;
            this.pkScoreColorRangeSettingPanel.Location = new System.Drawing.Point(280, 0);
            this.pkScoreColorRangeSettingPanel.Name = "pkScoreColorRangeSettingPanel";
            this.pkScoreColorRangeSettingPanel.Size = new System.Drawing.Size(370, 151);
            this.pkScoreColorRangeSettingPanel.TabIndex = 6;
            // 
            // textEditColunmName
            // 
            this.textEditColunmName.Location = new System.Drawing.Point(93, 12);
            this.textEditColunmName.Name = "textEditColunmName";
            this.textEditColunmName.Size = new System.Drawing.Size(160, 21);
            this.textEditColunmName.TabIndex = 1;
            // 
            // labelControl17
            // 
            this.labelControl17.Location = new System.Drawing.Point(51, 15);
            this.labelControl17.Name = "labelControl17";
            this.labelControl17.Size = new System.Drawing.Size(36, 14);
            this.labelControl17.TabIndex = 0;
            this.labelControl17.Text = "列名：";
            // 
            // seScoreRangeMax
            // 
            this.seScoreRangeMax.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.seScoreRangeMax.Location = new System.Drawing.Point(187, 39);
            this.seScoreRangeMax.Name = "seScoreRangeMax";
            this.seScoreRangeMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.seScoreRangeMax.Size = new System.Drawing.Size(66, 21);
            this.seScoreRangeMax.TabIndex = 3;
            // 
            // seScoreRangeMin
            // 
            this.seScoreRangeMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.seScoreRangeMin.Location = new System.Drawing.Point(93, 39);
            this.seScoreRangeMin.Name = "seScoreRangeMin";
            this.seScoreRangeMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.seScoreRangeMin.Size = new System.Drawing.Size(66, 21);
            this.seScoreRangeMin.TabIndex = 3;
            // 
            // labelControl5
            // 
            this.labelControl5.Location = new System.Drawing.Point(161, 42);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(24, 14);
            this.labelControl5.TabIndex = 0;
            this.labelControl5.Text = "≤x≤";
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(15, 42);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(72, 14);
            this.labelControl3.TabIndex = 0;
            this.labelControl3.Text = "标准分值域：";
            // 
            // CQTKPIColumnPanel_PK
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.grpPK);
            this.MinimumSize = new System.Drawing.Size(652, 657);
            this.Name = "CQTKPIColumnPanel_PK";
            this.Size = new System.Drawing.Size(652, 657);
            ((System.ComponentModel.ISupportInitialize)(this.grpPK)).EndInit();
            this.grpPK.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.navBarControl1)).EndInit();
            this.navBarControl1.ResumeLayout(false);
            this.navBarGroupControlContainer1.ResumeLayout(false);
            this.navBarGroupControlContainer1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroup1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.seKPIValueMax1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.seKPIVlaueMin1.Properties)).EndInit();
            this.navBarGroupControlContainer2.ResumeLayout(false);
            this.navBarGroupControlContainer2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroup2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.seKPIValueMax2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.seKPIValueMin2.Properties)).EndInit();
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.textEditColunmName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.seScoreRangeMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.seScoreRangeMin.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.GroupControl grpPK;
        private DevExpress.XtraNavBar.NavBarControl navBarControl1;
        private DevExpress.XtraNavBar.NavBarGroup navBarGroup1;
        private DevExpress.XtraNavBar.NavBarGroupControlContainer navBarGroupControlContainer1;
        private global::MasterCom.RAMS.Util.ScoreColorRangeSettingPanel scoreColorRangeSettingPanel1;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraEditors.RadioGroup radioGroup1;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.SpinEdit seKPIVlaueMin1;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraNavBar.NavBarGroupControlContainer navBarGroupControlContainer2;
        private global::MasterCom.RAMS.Util.ScoreColorRangeSettingPanel scoreColorRangeSettingPanel2;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private DevExpress.XtraEditors.RadioGroup radioGroup2;
        private DevExpress.XtraEditors.LabelControl labelControl14;
        private DevExpress.XtraEditors.LabelControl labelControl16;
        private DevExpress.XtraNavBar.NavBarGroup navBarGroup2;
        private System.Windows.Forms.Panel panel1;
        private global::MasterCom.RAMS.CQT.ScoreRangeColorSettingPanel pkScoreColorRangeSettingPanel;
        private DevExpress.XtraEditors.TextEdit textEditColunmName;
        private DevExpress.XtraEditors.LabelControl labelControl17;
        private DevExpress.XtraEditors.SpinEdit seScoreRangeMax;
        private DevExpress.XtraEditors.SpinEdit seScoreRangeMin;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SpinEdit seKPIValueMax1;
        private DevExpress.XtraEditors.SpinEdit seKPIValueMax2;
        private DevExpress.XtraEditors.SpinEdit seKPIValueMin2;
        private DevExpress.XtraEditors.SimpleButton btnEditF1;
        private System.Windows.Forms.TextBox txtFormula1;
        private DevExpress.XtraEditors.SimpleButton btnEidtF2;
        private System.Windows.Forms.TextBox txtFormula2;
        private DevExpress.XtraEditors.LabelControl labelControl2;
    }
}
