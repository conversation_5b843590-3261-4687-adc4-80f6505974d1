﻿namespace MasterCom.RAMS.Func
{
    partial class ZTBaStatForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.objectListView = new BrightIdeasSoftware.ObjectListView();
            this.olvColumnTimeRange = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHandoverTimes = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHandoverPercent = new BrightIdeasSoftware.OLVColumn();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miOutputToExcel = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // objectListView
            // 
            this.objectListView.AllColumns.Add(this.olvColumnTimeRange);
            this.objectListView.AllColumns.Add(this.olvColumnHandoverTimes);
            this.objectListView.AllColumns.Add(this.olvColumnHandoverPercent);
            this.objectListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnTimeRange,
            this.olvColumnHandoverTimes,
            this.olvColumnHandoverPercent});
            this.objectListView.ContextMenuStrip = this.contextMenuStrip;
            this.objectListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.objectListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.objectListView.FullRowSelect = true;
            this.objectListView.GridLines = true;
            this.objectListView.Location = new System.Drawing.Point(0, 0);
            this.objectListView.MultiSelect = false;
            this.objectListView.Name = "objectListView";
            this.objectListView.ShowGroups = false;
            this.objectListView.Size = new System.Drawing.Size(480, 287);
            this.objectListView.TabIndex = 7;
            this.objectListView.UseCompatibleStateImageBehavior = false;
            this.objectListView.View = System.Windows.Forms.View.Details;
            // 
            // olvColumnTimeRange
            // 
            this.olvColumnTimeRange.AspectName = "timeRange";
            this.olvColumnTimeRange.HeaderFont = null;
            this.olvColumnTimeRange.Text = "频点个数";
            this.olvColumnTimeRange.Width = 90;
            // 
            // olvColumnHandoverTimes
            // 
            this.olvColumnHandoverTimes.AspectName = "freqTimes";
            this.olvColumnHandoverTimes.HeaderFont = null;
            this.olvColumnHandoverTimes.Text = "出现次数";
            this.olvColumnHandoverTimes.Width = 90;
            // 
            // olvColumnHandoverPercent
            // 
            this.olvColumnHandoverPercent.AspectName = "Percent";
            this.olvColumnHandoverPercent.HeaderFont = null;
            this.olvColumnHandoverPercent.Text = "占比";
            this.olvColumnHandoverPercent.Width = 90;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miOutputToExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 26);
            // 
            // miOutputToExcel
            // 
            this.miOutputToExcel.Name = "miOutputToExcel";
            this.miOutputToExcel.Size = new System.Drawing.Size(129, 22);
            this.miOutputToExcel.Text = "导出Excel";
            this.miOutputToExcel.Click += new System.EventHandler(this.miOutputToExcel_Click);
            // 
            // ZTBaStatForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(480, 287);
            this.Controls.Add(this.objectListView);
            this.Name = "ZTBaStatForm";
            this.Text = "BA表统计";
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.ObjectListView objectListView;
        private BrightIdeasSoftware.OLVColumn olvColumnHandoverTimes;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miOutputToExcel;
        private BrightIdeasSoftware.OLVColumn olvColumnTimeRange;
        private BrightIdeasSoftware.OLVColumn olvColumnHandoverPercent;
    }
}