﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System.Windows.Forms;
using System.Linq;
using MasterCom.RAMS.BackgroundFunc;
using EvtEngineLib;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// According to time interval between two rtp messages, get the Loss Messages
    /// </summary>
    /// 

    public class ZTRtpPacketsLostByMessage_NR : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        private static ZTRtpPacketsLostByMessage_NR instance = null;
        public static ZTRtpPacketsLostByMessage_NR GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTRtpPacketsLostByMessage_NR(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get
            {
                return "单通问题点统计";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22111, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

        protected ZTRtpPacketsLostMessageConditon hoCondition = new ZTRtpPacketsLostMessageConditon();

        public ZTRtpPacketsLostByMessage_NR(MainModel mainModel)
            : base(mainModel)
        {
            this.Columns = new List<string>();
            this.IncludeMessage = true;
            this.Columns.Add("NR_VONR_RTP_Source_SSRC");
            this.Columns.Add("NR_TAC");
            this.Columns.Add("NR_NCI");
            this.Columns.Add("NR_PCI");
            this.Columns.Add("NR_SS_RSRP");
            this.Columns.Add("NR_SS_SINR");

            this.Columns.Add("lte_PUSCH_Power");
            this.Columns.Add("lte_gsm_SC_BCCH");
            this.Columns.Add("lte_gsm_SC_LAC");
            this.Columns.Add("lte_gsm_SC_CI");
            this.Columns.Add("isampleid");
            this.Columns.Add("itime");
            this.Columns.Add("ilongitude");
            this.Columns.Add("ilatitude");

            this.Columns.Add("lte_Pathloss");
            this.Columns.Add("NR_PDSCH_BLER");
            this.Columns.Add("NR_PUSCH_BLER");
            this.Columns.Add("lte_PDSCH_RB_Number");
            this.Columns.Add("NR_VONR_RTP_Sequence_Number");

            this.Columns.Add("NR_VONR_RTP_Source_SSRC");
            this.Columns.Add("NR_PESQMos");
            this.Columns.Add("NR_POLQA_Score_SWB");

            this.Columns.Add("NR_SSB_ARFCN");
            this.Columns.Add("NR_PDCCH_UL_GrantCount");
            this.Columns.Add("NR_PDCCH_DL_GrantCount");
            this.Columns.Add("NR_DL_TB0_Avg_MCS");
            this.Columns.Add("NR_DL_TB1_Avg_MCS");
            this.Columns.Add("NR_MCS_UL_Avg");
            this.Columns.Add("NR_PUCCH_PathLoss");
            this.Columns.Add("NR_PUSCH_TxPower");
            this.Columns.Add("lte_PDCCH_CCE_Start");
            this.Columns.Add("lte_PDCCH_CCEs_Number");
            this.Columns.Add("NR_QPSK_Ratio_UL_s");
            this.Columns.Add("NR_16QAM_Ratio_UL_s");
            this.Columns.Add("NR_64QAM_Ratio_UL_s");
            this.Columns.Add("NR_256QAM_Ratio_UL_s");
            this.Columns.Add("lte_PUSCH_PRb_Num_slot");
            this.Columns.Add("NR_PUSCH_Initial_BLER");
            this.Columns.Add("lte_Transmission_Mode");
            this.Columns.Add("lte_Rank_Indicator");
            this.Columns.Add("NR_BPSK_Ratio_DL_s");
            this.Columns.Add("NR_QPSK_Ratio_DL_s");
            this.Columns.Add("NR_16QAM_Ratio_DL_s");
            this.Columns.Add("NR_64QAM_Ratio_DL_s");
            this.Columns.Add("NR_256QAM_Ratio_DL_s");
            this.Columns.Add("NR_PRB_Num_DL_slot");
            this.Columns.Add("NR_PDSCH_Initial_BLER");
            this.Columns.Add("NR_PDSCH_Initial_BLER");
            this.Columns.Add("NR_PDSCH_Initial_BLER");
        }

        private readonly int signDL = 1;
        private readonly int signUL = 2;

        protected override void analyseFiles()
        {
            try
            {
                List<FileInfo> files = new List<FileInfo>();
                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    if (ServiceTypes.Count > 0 && fileInfo.ServiceType > 60 && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                    {
                        continue;
                    }
                    files.Add(fileInfo);
                }
                clearDataBeforeAnalyseFiles();

                if (MainModel.IsBackground)
                {
                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo("共读取待分析文件" + files.Count + "个...");
                }
                replayFiles(files);
                MainModel.ClearDTData();
                doSomethingAfterAnalyseFiles();
            }
            finally
            {
                System.Threading.Thread.Sleep(20);
                WaitBox.Close();
            }
        }

        private void replayFiles(List<FileInfo> files)
        {
            int iloop = 0;
            foreach (FileInfo fileInfo in files)
            {
                if (MainModel.IsBackground)
                {
                    if (MainModel.BackgroundStopRequest)
                    {
                        break;
                    }
                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo("正在分析 " + FuncType.ToString() +
                        SubFuncType.ToString() + " 类 " + Name + "，当前文件 " + (++iloop) + "/" + files.Count +
                        "个...文件名：" + fileInfo.Name);
                }
                else
                {
                    WaitBox.Text = "正在分析文件( " + (++iloop) + "/" + files.Count + " )...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / files.Count);
                }

                if (filterFile(fileInfo))
                {
                    continue;
                }
                curAnaFileInfo = fileInfo;
                Condition.FileInfos.Clear();
                Condition.FileInfos.Add(fileInfo);
                MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;

                //Replay all rtp messages.
                this.IncludeMessage = true;
                this.IncludeAllRtpMessage = true;
                replay();
                if (WaitBox.CancelRequest)
                {
                    break;
                }
            }
        }

        ZTRtpPacketsLostMessageSetConditionForm setForm = null;

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }

            if (setForm == null)
            {
                setForm = new ZTRtpPacketsLostMessageSetConditionForm();
            }

            if (setForm.ShowDialog() == DialogResult.OK)
            {
                setForm.GetCondition(out hoCondition);
                listFileInfo = new List<ZTRtpPacketsLostFileInfo>();
                return true;
            }

            return false;
        }

        Dictionary<string, MsgTime> dicCallingTime = null;
        List<DateTime> listMsgLostTime = null;
        List<ZTRtpPacketsLostMessageInfo> listDLMessageInfo = null;
        List<ZTRtpPacketsLostMessageInfo> listULMessageInfo = null;
        List<ZTRtpPacketsLostFileInfo> listFileInfo = null;
        List<MosInfo> listMosInfo = null;

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                if (file == null)
                    continue;

                dicCallingTime = new Dictionary<string, MsgTime>();
                listDLMessageInfo = new List<ZTRtpPacketsLostMessageInfo>();
                listULMessageInfo = new List<ZTRtpPacketsLostMessageInfo>();

                #region Analysis PacketLoss by messages

                //Get Calling time by messages and events
                fillDicCallingTime(file.Messages, file.Events);

                //DownLoad
                getLossByMessages(file.Messages, signDL);

                //UpLoad
                getLossByMessages(file.Messages, signUL);

                #endregion

                #region Analysis PacketLoss by TestPoint

                getAllMosByTestPoint(file.TestPoints);

                //DownLoad
                getLossTpByLossMessage(signDL, file);

                //UpLoad
                getLossTpByLossMessage(signUL, file);

                //FillInfos
                if (listDLMessageInfo.Count > 0 || listULMessageInfo.Count > 0)
                    listFileInfo.Add(new ZTRtpPacketsLostFileInfo(listDLMessageInfo, listULMessageInfo, file.FileName));

                #endregion
            }
        }

        const int ack = (int)MessageManager.Msg_IMS_SIP_ACK;
        const int byeOk = (int)MessageManager.Msg_IMS_SIP_BYE_OK;
        const int mobility = (int)MessageManager.NR_RRC_MobilityFromNRCommand;
        const int invite = (int)MessageManager.Msg_IMS_SIP_INVITE_OK;
        const int eventMsgLost = 9050;

        /// <summary>
        /// Beacuse pockets are different in different callings and with different direction,           
        /// so it is necessary initially to classify pockets into certain types.
        /// </summary>
        private void fillDicCallingTime(List<Model.Message> msgs, List<Event> evts)
        {
            if (msgs == null)
                return;

            Dictionary<string, MsgTime> dicTime = new Dictionary<string, MsgTime>();
            listMsgLostTime = new List<DateTime>();
            //保存所有的IMS_SIP_INVITE信令时间
            List<DateTime> listInvite = new List<DateTime>();

            #region Find ACK->Bye and ACK->Mobility

            for (int i = 0; i < msgs.Count; i++)
            {
                if (msgs[i].ID == invite)
                {
                    listInvite.Add(msgs[i].HandsetTime);
                }
                else if (msgs[i].ID == ack && (i + 1) < msgs.Count)
                {
                    i = addCall(msgs, dicTime, listInvite, i);
                }
            }
            #endregion

            findTimeOfMessageLost(evts, eventMsgLost);

            checkCallingTime(dicTime, listInvite);
        }

        private int addCall(List<Model.Message> msgs, Dictionary<string, MsgTime> dicTime, List<DateTime> listInvite, int i)
        {
            for (int j = i + 1; j < msgs.Count; j++)
            {
                if (msgs[j].ID == ack)
                {
                    i = j;
                    continue;
                }

                if (msgs[j].ID == byeOk)
                {
                    //ACK -> BYEOK 正常通话
                    if (!dicTime.ContainsKey("Bye_" + msgs[i].Time))
                    {
                        dicTime.Add("Bye_" + msgs[i].Time,
                            new MsgTime(new TimePeriod(msgs[i].HandsetTime, msgs[j].HandsetTime),
                                new TimePeriod(msgs[i].DateTime, msgs[j].DateTime)));
                    }
                    i = j;
                    break;
                }
                else if (msgs[j].ID == mobility)
                {
                    //ACK -> Mobility 发生eSRVCC切换通话
                    if (!dicTime.ContainsKey("Mobility_" + msgs[i].Time))
                    {
                        dicTime.Add("Mobility_" + msgs[i].Time,
                            new MsgTime(new TimePeriod(msgs[i].HandsetTime, msgs[j].HandsetTime),
                                new TimePeriod(msgs[i].DateTime, msgs[j].DateTime)));
                    }
                    i = j;
                    break;
                }
                else if (msgs[j].ID == invite)
                {
                    listInvite.Add(msgs[j].HandsetTime);
                }
            }

            return i;
        }

        private void findTimeOfMessageLost(List<Event> evts, int msgLost)
        {
            if (evts != null)
            {
                evts.ForEach(evt =>
                {
                    if (evt.ID == msgLost)
                    {
                        listMsgLostTime.Add(evt.DateTime);
                    }
                });
            }
        }

        private void checkCallingTime(Dictionary<string, MsgTime> dicTime, List<DateTime> listInvite)
        {
            List<string> listKey = new List<string>(dicTime.Keys);
            for (int i = 0; i < listKey.Count; i++)
            {
                if (listKey[i].Contains("Bye_"))
                {
                    //添加通话时段
                    addNewCallingTime(listKey[i], dicTime[listKey[i]]);
                }
                else
                {
                    if (i + 1 < listKey.Count && listKey[i + 1].Contains("Bye_"))
                    {
                        i = dealInvite(dicTime, listInvite, listKey, i);
                    }
                    else
                    {
                        addNewCallingTime(listKey[i], dicTime[listKey[i]]);
                    }
                }
            }
        }

        private int dealInvite(Dictionary<string, MsgTime> dicTime, List<DateTime> listInvite, List<string> listKey, int i)
        {
            //ACK -> Mobility信令时间段
            TimePeriod tpStart = dicTime[listKey[i]].HandsetsTime;
            //ACK -> ByeOK信令时间段
            TimePeriod tpEnd = dicTime[listKey[i + 1]].HandsetsTime;
            //Mobility -> ACK信令时间段
            TimePeriod tpCheck = new TimePeriod(tpStart.EndTime, tpEnd.BeginTime);
            //切换eSRVCC是否成功,true为成功

            bool isConnect = false;
            //判断 Mobility -> ACK 期间有无Invite信令,且Invite信令在Mobility信令发生的10秒内
            foreach (DateTime dt in listInvite)
            {
                if (tpCheck.Contains(dt) && (dt - tpCheck.BeginTime).TotalSeconds <= 10)
                {
                    isConnect = true;
                    break;
                }
            }

            if (isConnect)
            {
                addNewCallingTime("Invite_" + tpStart.BeginTime.ToShortTimeString(), new MsgTime(
                    new TimePeriod(tpStart.BeginTime, tpEnd.EndTime), new TimePeriod(
                        dicTime[listKey[i]].CompleteTime.BeginTime, dicTime[listKey[i + 1]].CompleteTime.EndTime)));
                i++;
            }
            else
            {
                addNewCallingTime(listKey[i], dicTime[listKey[i]]);
                addNewCallingTime(listKey[i + 1], dicTime[listKey[i + 1]]);
                i++;
            }

            return i;
        }

        private void addNewCallingTime(string key, MsgTime val)
        {
            if (!isMsgLost(val.HandsetsTime) && !dicCallingTime.ContainsKey(key))
                dicCallingTime.Add(key, val);
        }

        private bool isMsgLost(TimePeriod timePeriod)
        {
            bool result = false;
            listMsgLostTime.ForEach(t =>
            {
                if (timePeriod.Contains(t))
                    result = true;
            });
            return result;
        }

        /// <summary>
        /// To get LossMessages through time interval between two rtp messages.
        /// </summary>
        /// <param name="Messages">The messages in a file</param>
        /// <param name="direction">The sign of direction</param>
        private void getLossByMessages(List<Model.Message> Messages, int direction)
        {
            int indexTp = 0;
            Model.Message msgTol = null;
            Model.Message msgNext = null;
            List<ZTRtpPacketsLostMessageInfo> listLoss = direction == signDL ? listDLMessageInfo : listULMessageInfo;
            float lossTime = hoCondition.LossTime * 1000;
            //循环所有通话时段
            foreach (MsgTime msgTime in dicCallingTime.Values)
            {
                //满足在本次通话时段内的IMS_RTP_SN_And_Payload信令合集
                List<Model.Message> listMessage = getMsgList(Messages, direction, ref indexTp, ref msgTol, msgTime);

                addPacketsLostMsg(ref msgTol, ref msgNext, listLoss, lossTime, msgTime, listMessage);
            }
        }

        private static List<Model.Message> getMsgList(List<Model.Message> Messages, int direction, ref int indexTp, ref Model.Message msgTol, MsgTime msgTime)
        {
            List<Model.Message> listMessage = new List<Model.Message>();

            for (int i = indexTp; i < Messages.Count; i++)
            {
                msgTol = Messages[i];
                //msg 为 IMS_RTP_SN_And_Payload 信令且上下行方向相同
                if (msgTol != null && msgTol.ID == 2147426825 && msgTol.Direction == direction)
                {
                    //IMS_RTP_SN_And_Payload信令在本次循环的通话时段内
                    if (msgTol.HandsetTime >= msgTime.HandsetsTime.BeginTime &&
                        msgTol.HandsetTime <= msgTime.HandsetsTime.EndTime)
                    {
                        listMessage.Add(msgTol);
                    }
                    else if (msgTol.HandsetTime > msgTime.HandsetsTime.EndTime)
                    {
                        indexTp = i;
                        break;
                    }
                }
            }

            return listMessage;
        }

        private static void addPacketsLostMsg(ref Model.Message msgTol, ref Model.Message msgNext, List<ZTRtpPacketsLostMessageInfo> listLoss, float lossTime, MsgTime msgTime, List<Model.Message> listMessage)
        {
            for (int i = 0; i < listMessage.Count - 1; i++)
            {
                msgTol = listMessage[i];
                msgNext = listMessage[i + 1];

                //前后2个 IMS_RTP_SN_And_Payload 信令 时间大于设定条件则为丢包
                if ((msgNext.HandsetTime - msgTol.HandsetTime).TotalMilliseconds > lossTime)
                {
                    var msgInfo = new ZTRtpPacketsLostMessageInfo(msgTol, msgNext);
                    msgInfo.SCallTime = msgTime.HandsetsTime.BeginTime;
                    msgInfo.SCallCompleteTime = msgTime.CompleteTime.BeginTime;
                    listLoss.Add(msgInfo);
                }
            }
        }

        /// <summary>
        /// According to Loss Messages, to find Loss Testpoints.
        /// </summary>
        /// <param name="direction">The messages in a file</param>
        /// <param name="file">The information of file</param>
        private void getLossTpByLossMessage(int direction, DTFileDataManager file)
        {
            List<ZTRtpPacketsLostMessageInfo> listLoss = direction == signDL ? listDLMessageInfo : listULMessageInfo;
            string ssrc = direction == signDL ? "NR_VONR_RTP_Source_SSRC" : "NR_VONR_RTP_Source_SSRC";    // "NR_VONR_RTP_Source_SSRC"指的是下行
            List<TestPoint> tps = file.TestPoints;
            FileInfo fileInfo = file.GetFileInfo();
            string AreaString = string.Empty;

            if (fileInfo != null)
            {
                AreaString = fileInfo.AreaString;
            }

            if (listLoss != null && tps != null
                && listLoss.Count > 0)
            {
                int index = 0;

                dealNearestMsg(direction, file, listLoss, ssrc, tps, AreaString, index);
            }
        }

        private void dealNearestMsg(int direction, DTFileDataManager file, List<ZTRtpPacketsLostMessageInfo> listLoss,
            string ssrc, List<TestPoint> tps, string AreaString, int index)
        {
            listLoss.ForEach(msg =>
            {
                for (int i = index; i < tps.Count; i++)
                {
                    if (tps[i] != null && tps[i][ssrc] != null
                        && tps[i].DateTime >= msg.StartMsg.DateTime)
                    {
                        TestPoint tpTol = tps[i];
                        msg.TestPoints.Add(tpTol);
                        msg.Area = AreaString;
                        msg.Grid = getGrid(tpTol.Longitude, tpTol.Latitude);  //更改为 预存区域
                        msg.RoadName = GISManager.GetInstance().GetRoadPlaceDesc(tpTol.Longitude, tpTol.Latitude);
                        msg.FileName = file.FileName;
                        msg.Direction = direction == signDL ? "下行" : "上行";
                        msg.SLossTime = msg.StartMsg.HandsetTime;
                        msg.SLossCompleteTime = msg.StartMsg.DateTime;

                        msg.FillData_NR(tpTol);

                        getMosInfo(msg);
                        break;
                    }
                }
            });
        }

        private string getGrid(double longitude, double latitude)
        {
            foreach (string strKey in regionMopDic.Keys)
            {
                if (regionMopDic[strKey].CheckPointInRegion(longitude, latitude))
                {
                    return strKey;
                }
            }
            return null;
        }
        protected override void getReadyBeforeQuery()
        {
            //实例化区域，并初始化选择区域
            regionMopDic = new Dictionary<string, MapOperation2>();
            InitRegionMop2();
        }

        Dictionary<string, MapOperation2> regionMopDic = null;
        private void InitRegionMop2()
        {
            List<ResvRegion> resvRegions = MainModel.SearchGeometrys.SelectedResvRegions;
            MapWinGIS.Shape gmt = MainModel.SearchGeometrys.Region;

            if (resvRegions != null && resvRegions.Count > 0)    //预存区域
            {
                foreach (ResvRegion region in resvRegions)
                {
                    if (!regionMopDic.ContainsKey(region.RegionName))
                    {
                        MapOperation2 mapOp2 = new MapOperation2();
                        mapOp2.FillPolygon(region.Shape);
                        regionMopDic.Add(region.RegionName, mapOp2);
                    }
                }
            }
            else if (gmt != null)//单个区域
            {
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(gmt);
                regionMopDic.Add("当前区域", mapOp2);
            }
        }

        private void getAllMosByTestPoint(List<TestPoint> listTp)
        {
            string MOSParamName = string.Empty;
            int mosGate = 5;
            listMosInfo = new List<MosInfo>();

            foreach (TestPoint tp in listTp)    //通过采样点mos值，确定回放的时间段
            {
                if (MOSParamName == string.Empty)
                {
                    float? pesq = (float?)tp["NR_PESQMos"];
                    float? polqa = (float?)tp["NR_POLQA_Score_SWB"];
                    if (pesq != null && pesq > 0 && pesq <= 5)
                    {
                        MOSParamName = "NR_PESQMos";
                    }
                    else if (polqa != null && polqa > 0 && polqa <= 5)
                    {
                        MOSParamName = "NR_POLQA_Score_SWB";
                    }
                    else
                    {
                        continue;
                    }
                }

                float? mos = (float?)tp[MOSParamName];
                if (mos != null && mos > 0 && mos < mosGate)
                {
                    listMosInfo.Add(new MosInfo(
                        new TimePeriod(tp.DateTime.AddSeconds(-10), tp.DateTime.AddSeconds(-2)),
                        tp.DateTime, (float)mos));
                }
            }
        }

        private void getMosInfo(ZTRtpPacketsLostMessageInfo lostInfo)
        {
            if (listMosInfo.Count == 0)
                return;

            //记录在RTP丢包时间段内存在的MOS周期
            List<MosInfo> listMosInPeriod = new List<MosInfo>();
            string timeFormat = "yy-MM-dd HH:mm:ss.fff";

            string initMostTime = "";
            float? initMosVal = null;

            foreach (var mosInfo in listMosInfo)
            {
                initMostTime = mosInfo.PointTime.ToString(timeFormat);
                initMosVal = mosInfo.MosVal;

                //Mos值是从采样点中获取,采样点只有DateTime,因此要和对应的RTP丢包时间段的DateTime去比较
                if (mosInfo.MosPeriod.BeginTime > lostInfo.EndMsg.DateTime)
                {
                    break;
                }
                if (mosInfo.MosPeriod.EndTime < lostInfo.StartMsg.DateTime)
                {
                    continue;
                }

                listMosInPeriod.Add(mosInfo);
            }

            if (listMosInPeriod.Count == 0)
            {
                lostInfo.MosTime = initMostTime;
                lostInfo.MosVal = initMosVal;
                lostInfo.MosAveVal = initMosVal;
            }
            else if (listMosInPeriod.Count == 1)
            {
                lostInfo.MosTime = listMosInPeriod[0].PointTime.ToString(timeFormat);
                lostInfo.MosVal = listMosInPeriod[0].MosVal;
                lostInfo.MosAveVal = listMosInPeriod[0].MosVal;
            }
            else
            {
                float val = 0;
                listMosInPeriod.ForEach(m =>
                {
                    val += m.MosVal;
                    lostInfo.MosTime += m.PointTime.ToString(timeFormat) + ",";
                });
                lostInfo.MosTime = lostInfo.MosTime.TrimEnd(',');
                lostInfo.MosVal = listMosInPeriod[0].MosVal;
                lostInfo.MosAveVal = val / listMosInPeriod.Count;
            }
        }


        protected override void fireShowForm()
        {
            if (listFileInfo.Count == 0)
            {
                MessageBox.Show("没有符合条件的RTP信令！");
                return;
            }

            var frm = MainModel.GetInstance().CreateResultForm(typeof(ZTRtpPacketsLostShowForm_NR)) as ZTRtpPacketsLostShowForm_NR;
            frm.FillData(listFileInfo);
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class ZTRtpPacketsLostByMessage : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        private static ZTRtpPacketsLostByMessage instance = null;
        public static ZTRtpPacketsLostByMessage GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTRtpPacketsLostByMessage(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get
            {
                return "单通问题点统计";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22111, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

        protected ZTRtpPacketsLostMessageConditon hoCondition = new ZTRtpPacketsLostMessageConditon();

        public ZTRtpPacketsLostByMessage(MainModel mainModel)
            : base(mainModel)
        {
            this.Columns = new List<string>();
            this.IncludeMessage = true;
            this.Columns.Add("lte_volte_Source_SSRC");
            this.Columns.Add("lte_TAC");
            this.Columns.Add("lte_ECI");
            this.Columns.Add("lte_PCI");
            this.Columns.Add("lte_RSRP");
            this.Columns.Add("lte_SINR");

            this.Columns.Add("lte_PUSCH_Power");
            this.Columns.Add("lte_gsm_SC_BCCH");
            this.Columns.Add("lte_gsm_SC_LAC");
            this.Columns.Add("lte_gsm_SC_CI");
            this.Columns.Add("isampleid");
            this.Columns.Add("itime");
            this.Columns.Add("ilongitude");
            this.Columns.Add("ilatitude");

            this.Columns.Add("lte_Pathloss");
            this.Columns.Add("lte_PDSCH_BLER");
            this.Columns.Add("lte_PUSCH_BLER");
            this.Columns.Add("lte_PDSCH_RB_Number");
            this.Columns.Add("lte_volte_RTP_Sequence_Number");

            this.Columns.Add("lte_volte_UL_Source_SSRC");
            this.Columns.Add("lte_PESQMos");
            this.Columns.Add("lte_POLQA_Score_SWB");

            this.Columns.Add("lte_EARFCN");
            this.Columns.Add("lte_PDCCH_UL_Grant_Count");
            this.Columns.Add("lte_PDCCH_DL_Grant_Count");
            this.Columns.Add("lte_MCSCode0_DL");
            this.Columns.Add("lte_MCSCode1_DL");
            this.Columns.Add("lte_MCS_UL");
            this.Columns.Add("lte_PDSCH_Code0_BLER");
            this.Columns.Add("lte_PDSCH_Code1_BLER");
            this.Columns.Add("lte_PDCCH_CCE_Start");
            this.Columns.Add("lte_PDCCH_CCEs_Number");
            this.Columns.Add("lte_Times_QPSK_UL");
            this.Columns.Add("lte_Times_QAM16_UL");
            this.Columns.Add("lte_Times_QAM64_UL");
            this.Columns.Add("lte_PUSCH_PRb_Num_slot");
            this.Columns.Add("lte_PUSCH_Initial_BLER");
            this.Columns.Add("lte_Transmission_Mode");
            this.Columns.Add("lte_Rank_Indicator");
            this.Columns.Add("lte_Times_QPSK_DLCode0");
            this.Columns.Add("lte_Times_QPSK_DLCode1");
            this.Columns.Add("lte_Times_QAM16_DLCode0");
            this.Columns.Add("lte_Times_QAM16_DLCode1");
            this.Columns.Add("lte_Times_QAM64_DLCode0");
            this.Columns.Add("lte_Times_QAM64_DLCode1");
            this.Columns.Add("lte_PDSCH_PRb_Num_slot");
            this.Columns.Add("lte_PDSCH_Init_BLER");
            this.Columns.Add("lte_PDSCH_Init_BLER_Code0");
            this.Columns.Add("lte_PDSCH_Init_BLER_Code1");
        }

        private readonly int signDL = 1;
        private readonly int signUL = 2;

        protected override void analyseFiles()
        {
            try
            {
                List<FileInfo> files = new List<FileInfo>();
                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    if (ServiceTypes.Count > 0 && fileInfo.ServiceType > 0 && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                    {
                        continue;
                    }
                    files.Add(fileInfo);
                }
                clearDataBeforeAnalyseFiles();
                if (MainModel.IsBackground)
                {
                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo("共读取待分析文件" + files.Count + "个...");
                }
                replayFiles(files);
                MainModel.ClearDTData();
                doSomethingAfterAnalyseFiles();
            }
            finally
            {
                System.Threading.Thread.Sleep(20);
                WaitBox.Close();
            }

        }

        private void replayFiles(List<FileInfo> files)
        {
            int iloop = 0;
            foreach (FileInfo fileInfo in files)
            {
                if (MainModel.IsBackground)
                {
                    if (MainModel.BackgroundStopRequest)
                    {
                        break;
                    }
                    BackgroundFuncManager.GetInstance().ReportBackgroundInfo("正在分析 " + FuncType.ToString() +
                        SubFuncType.ToString() + " 类 " + Name + "，当前文件 " + (++iloop) + "/" + files.Count +
                        "个...文件名：" + fileInfo.Name);
                }
                else
                {
                    WaitBox.Text = "正在分析文件( " + (++iloop) + "/" + files.Count + " )...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / files.Count);
                }
                if (filterFile(fileInfo))
                {
                    continue;
                }
                curAnaFileInfo = fileInfo;
                Condition.FileInfos.Clear();
                Condition.FileInfos.Add(fileInfo);
                MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;

                //Replay all rtp messages.
                this.IncludeMessage = true;
                this.IncludeAllRtpMessage = true;
                replay();
                if (WaitBox.CancelRequest)
                {
                    break;
                }
            }
        }

        ZTRtpPacketsLostMessageSetConditionForm setForm = null;

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new ZTRtpPacketsLostMessageSetConditionForm();
            }
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                setForm.GetCondition(out hoCondition);
                listFileInfo = new List<ZTRtpPacketsLostFileInfo>();
                return true;
            }
            return false;
        }

        Dictionary<string, MsgTime> dicCallingTime = null;
        List<DateTime> listMsgLostTime = null;
        List<ZTRtpPacketsLostMessageInfo> listDLMessageInfo = null;
        List<ZTRtpPacketsLostMessageInfo> listULMessageInfo = null;
        List<ZTRtpPacketsLostFileInfo> listFileInfo = null;
        List<MosInfo> listMosInfo = null;

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                if (file == null)
                    continue;

                dicCallingTime = new Dictionary<string, MsgTime>();
                listDLMessageInfo = new List<ZTRtpPacketsLostMessageInfo>();
                listULMessageInfo = new List<ZTRtpPacketsLostMessageInfo>();

                #region Analysis PacketLoss by messages

                //Get Calling time by messages and events
                fillDicCallingTime(file.Messages, file.Events);

                //DownLoad
                getLossByMessages(file.Messages, signDL);

                //UpLoad
                getLossByMessages(file.Messages, signUL);

                #endregion

                #region Analysis PacketLoss by TestPoint

                getAllMosByTestPoint(file.TestPoints);

                //DownLoad
                getLossTpByLossMessage(signDL, file);

                //UpLoad
                getLossTpByLossMessage(signUL, file);

                //FillInfos
                if (listDLMessageInfo.Count > 0 || listULMessageInfo.Count > 0)
                    listFileInfo.Add(new ZTRtpPacketsLostFileInfo(listDLMessageInfo, listULMessageInfo, file.FileName));

                #endregion
            }

        }

        const int ack = (int)MessageManager.Msg_IMS_SIP_ACK;
        const int byeOk = (int)MessageManager.Msg_IMS_SIP_BYE_OK;
        const int mobility = (int)MessageManager.LTE_RRC_Mobility_From_EUTRA_Command;
        const int invite = (int)MessageManager.Msg_IMS_SIP_INVITE_OK;
        const int msgLost = 89;

        /// <summary>
        /// Beacuse pockets are different in different callings and with different direction,           
        /// so it is necessary initially to classify pockets into certain types.
        /// </summary>
        private void fillDicCallingTime(List<Model.Message> msgs, List<Event> evts)
        {
            if (msgs == null)
                return;

            Dictionary<string, MsgTime> dicTime = new Dictionary<string, MsgTime>();
            listMsgLostTime = new List<DateTime>();
            //保存所有的IMS_SIP_INVITE信令时间
            List<DateTime> listInvite = new List<DateTime>();

            #region Find ACK->Bye and ACK->Mobility

            for (int i = 0; i < msgs.Count; i++)
            {
                if (msgs[i].ID == invite)
                {
                    listInvite.Add(msgs[i].HandsetTime);
                }
                else if (msgs[i].ID == ack && (i + 1) < msgs.Count)
                {
                    i = addCall(msgs, dicTime, listInvite, i);
                }
            }
            #endregion

            findTimeOfMessageLost(evts, msgLost);

            checkCallingTime(dicTime, listInvite);
        }

        private int addCall(List<Model.Message> msgs, Dictionary<string, MsgTime> dicTime, List<DateTime> listInvite, int i)
        {
            for (int j = i + 1; j < msgs.Count; j++)
            {
                if (msgs[j].ID == ack)
                {
                    i = j;
                    continue;
                }

                if (msgs[j].ID == byeOk)
                {
                    //ACK -> BYEOK 正常通话
                    if (!dicTime.ContainsKey("Bye_" + msgs[i].Time))
                    {
                        dicTime.Add("Bye_" + msgs[i].Time,
                            new MsgTime(new TimePeriod(msgs[i].HandsetTime, msgs[j].HandsetTime),
                                new TimePeriod(msgs[i].DateTime, msgs[j].DateTime)));
                    }
                    i = j;
                    break;
                }
                else if (msgs[j].ID == mobility)
                {
                    //ACK -> Mobility 发生eSRVCC切换通话
                    if (!dicTime.ContainsKey("Mobility_" + msgs[i].Time))
                    {
                        dicTime.Add("Mobility_" + msgs[i].Time,
                            new MsgTime(new TimePeriod(msgs[i].HandsetTime, msgs[j].HandsetTime),
                                new TimePeriod(msgs[i].DateTime, msgs[j].DateTime)));
                    }
                    i = j;
                    break;
                }
                else if (msgs[j].ID == invite)
                {
                    listInvite.Add(msgs[j].HandsetTime);
                }
            }

            return i;
        }

        private void findTimeOfMessageLost(List<Event> evts, int msgLost)
        {
            if (evts != null)
            {
                evts.ForEach(evt =>
                {
                    if (evt.ID == msgLost)
                    {
                        listMsgLostTime.Add(evt.DateTime);
                    }
                });
            }
        }

        private void checkCallingTime(Dictionary<string, MsgTime> dicTime, List<DateTime> listInvite)
        {
            List<string> listKey = new List<string>(dicTime.Keys);
            for (int i = 0; i < listKey.Count; i++)
            {
                if (listKey[i].Contains("Bye_"))
                {
                    //添加通话时段
                    addNewCallingTime(listKey[i], dicTime[listKey[i]]);
                }
                else
                {
                    if (i + 1 < listKey.Count && listKey[i + 1].Contains("Bye_"))
                    {
                        i = dealInvite(dicTime, listInvite, listKey, i);
                    }
                    else
                    {
                        addNewCallingTime(listKey[i], dicTime[listKey[i]]);
                    }
                }
            }
        }

        private int dealInvite(Dictionary<string, MsgTime> dicTime, List<DateTime> listInvite, List<string> listKey, int i)
        {
            //ACK -> Mobility信令时间段
            TimePeriod tpStart = dicTime[listKey[i]].HandsetsTime;
            //ACK -> ByeOK信令时间段
            TimePeriod tpEnd = dicTime[listKey[i + 1]].HandsetsTime;
            //Mobility -> ACK信令时间段
            TimePeriod tpCheck = new TimePeriod(tpStart.EndTime, tpEnd.BeginTime);
            //切换eSRVCC是否成功,true为成功
            bool isConnect = false;
            //判断 Mobility -> ACK 期间有无Invite信令,且Invite信令在Mobility信令发生的10秒内
            foreach (DateTime dt in listInvite)
            {
                if (tpCheck.Contains(dt) && (dt - tpCheck.BeginTime).TotalSeconds <= 10)
                {
                    isConnect = true;
                    break;
                }
            }

            if (isConnect)
            {
                addNewCallingTime("Invite_" + tpStart.BeginTime.ToShortTimeString(), new MsgTime(
                    new TimePeriod(tpStart.BeginTime, tpEnd.EndTime), new TimePeriod(
                        dicTime[listKey[i]].CompleteTime.BeginTime, dicTime[listKey[i + 1]].CompleteTime.EndTime)));
                i++;
            }
            else
            {
                addNewCallingTime(listKey[i], dicTime[listKey[i]]);
                addNewCallingTime(listKey[i + 1], dicTime[listKey[i + 1]]);
                i++;
            }

            return i;
        }

        private void addNewCallingTime(string key, MsgTime val)
        {
            if (!isMsgLost(val.HandsetsTime) && !dicCallingTime.ContainsKey(key))
                dicCallingTime.Add(key, val);
        }

        private bool isMsgLost(TimePeriod timePeriod)
        {
            bool result = false;
            listMsgLostTime.ForEach(t =>
            {
                if (timePeriod.Contains(t))
                    result = true;
            });
            return result;
        }

        /// <summary>
        /// To get LossMessages through time interval between two rtp messages.
        /// </summary>
        /// <param name="Messages">The messages in a file</param>
        /// <param name="direction">The sign of direction</param>
        private void getLossByMessages(List<Model.Message> Messages, int direction)
        {
            int indexTp = 0;
            Model.Message msgTol = null;
            Model.Message msgNext = null;
            List<ZTRtpPacketsLostMessageInfo> listLoss = direction == signDL ? listDLMessageInfo : listULMessageInfo;
            float lossTime = hoCondition.LossTime * 1000;
            //循环所有通话时段
            foreach (MsgTime msgTime in dicCallingTime.Values)
            {
                //满足在本次通话时段内的IMS_RTP_SN_And_Payload信令合集
                List<Model.Message> listMessage = getMsgList(Messages, direction, ref indexTp, ref msgTol, msgTime);

                addPacketsLostMsg(ref msgTol, ref msgNext, listLoss, lossTime, msgTime, listMessage);
            }

        }

        private static List<Model.Message> getMsgList(List<Model.Message> Messages, int direction, ref int indexTp, ref Model.Message msgTol, MsgTime msgTime)
        {
            List<Model.Message> listMessage = new List<Model.Message>();

            for (int i = indexTp; i < Messages.Count; i++)
            {
                msgTol = Messages[i];
                //msg 为 IMS_RTP_SN_And_Payload信令且上下行方向相同
                if (msgTol != null && msgTol.ID == 2147426825 && msgTol.Direction == direction)
                {
                    //IMS_RTP_SN_And_Payload信令在本次循环的通话时段内
                    if (msgTol.HandsetTime >= msgTime.HandsetsTime.BeginTime &&
                        msgTol.HandsetTime <= msgTime.HandsetsTime.EndTime)
                    {
                        listMessage.Add(msgTol);
                    }
                    else if (msgTol.HandsetTime > msgTime.HandsetsTime.EndTime)
                    {
                        indexTp = i;
                        break;
                    }
                }
            }

            return listMessage;
        }

        private static void addPacketsLostMsg(ref Model.Message msgTol, ref Model.Message msgNext, List<ZTRtpPacketsLostMessageInfo> listLoss, float lossTime, MsgTime msgTime, List<Model.Message> listMessage)
        {
            for (int i = 0; i < listMessage.Count - 1; i++)
            {
                msgTol = listMessage[i];
                msgNext = listMessage[i + 1];
                //前后2个IMS_RTP_SN_And_Payload信令时间大于设定条件则为丢包
                if ((msgNext.HandsetTime - msgTol.HandsetTime).TotalMilliseconds > lossTime)
                {
                    ZTRtpPacketsLostMessageInfo msgInfo = new ZTRtpPacketsLostMessageInfo(
                        msgTol, msgNext);
                    msgInfo.SCallTime = msgTime.HandsetsTime.BeginTime;
                    msgInfo.SCallCompleteTime = msgTime.CompleteTime.BeginTime;
                    listLoss.Add(msgInfo);
                }
            }
        }

        /// <summary>
        /// According to Loss Messages, to find Loss Testpoints.
        /// </summary>
        /// <param name="direction">The messages in a file</param>
        /// <param name="file">The information of file</param>
        private void getLossTpByLossMessage(int direction, DTFileDataManager file)
        {
            List<ZTRtpPacketsLostMessageInfo> listLoss = direction == signDL ? listDLMessageInfo : listULMessageInfo;
            string ssrc = direction == signDL ? "lte_volte_Source_SSRC" : "lte_volte_UL_Source_SSRC";
            List<TestPoint> tps = file.TestPoints;
            FileInfo fileInfo = file.GetFileInfo();
            string AreaString = string.Empty;

            if (fileInfo != null)
            {
                AreaString = fileInfo.AreaString;
            }

            if (listLoss != null && tps != null
                && listLoss.Count > 0)
            {
                int index = 0;

                dealNearestMsg(direction, file, listLoss, ssrc, tps, AreaString, index);
            }
        }

        private void dealNearestMsg(int direction, DTFileDataManager file, List<ZTRtpPacketsLostMessageInfo> listLoss, 
            string ssrc, List<TestPoint> tps, string AreaString, int index)
        {
            listLoss.ForEach(msg =>
            {
                for (int i = index; i < tps.Count; i++)
                {
                    if (tps[i] != null && tps[i][ssrc] != null
                        && tps[i].DateTime >= msg.StartMsg.DateTime)
                    {
                        TestPoint tpTol = tps[i];
                        msg.TestPoints.Add(tpTol);
                        msg.Area = AreaString;
                        msg.Grid = getGrid(tpTol.Longitude, tpTol.Latitude);//更改为 预存区域
                        msg.RoadName = GISManager.GetInstance().GetRoadPlaceDesc(tpTol.Longitude, tpTol.Latitude);
                        msg.FileName = file.FileName;
                        msg.Direction = direction == signDL ? "下行" : "上行";
                        msg.SLossTime = msg.StartMsg.HandsetTime;
                        msg.SLossCompleteTime = msg.StartMsg.DateTime;

                        msg.FillData(tpTol);

                        getMosInfo(msg);
                        break;
                    }
                }
            });
        }

        private string getGrid(double longitude, double latitude)
        {
            foreach (string strKey in regionMopDic.Keys)
            {
                if (regionMopDic[strKey].CheckPointInRegion(longitude, latitude))
                {
                    return strKey;
                }
            }
            return null;
        }
        protected override void getReadyBeforeQuery()
        {
            //实例化区域，并初始化选择区域
            regionMopDic = new Dictionary<string, MapOperation2>();
            InitRegionMop2();
        }

        Dictionary<string, MapOperation2> regionMopDic = null;
        private void InitRegionMop2()
        {
            List<ResvRegion> resvRegions = MainModel.SearchGeometrys.SelectedResvRegions;
            MapWinGIS.Shape gmt = MainModel.SearchGeometrys.Region;

            if (resvRegions != null && resvRegions.Count > 0)  //预存区域
            {
                foreach (ResvRegion region in resvRegions)
                {
                    if (!regionMopDic.ContainsKey(region.RegionName))
                    {
                        MapOperation2 mapOp2 = new MapOperation2();
                        mapOp2.FillPolygon(region.Shape);
                        regionMopDic.Add(region.RegionName, mapOp2);
                    }
                }
            }
            else if (gmt != null)//单个区域
            {
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(gmt);
                regionMopDic.Add("当前区域", mapOp2);
            }
        }

        private void getAllMosByTestPoint(List<TestPoint> listTp)
        {
            string MOSParamName = string.Empty;
            int mosGate = 5;
            listMosInfo = new List<MosInfo>();

            foreach (TestPoint tp in listTp)//通过采样点mos值，确定回放的时间段
            {
                if (MOSParamName == string.Empty)
                {
                    float? pesq = (float?)tp["lte_PESQMos"];
                    float? polqa = (float?)tp["lte_POLQA_Score_SWB"];
                    if (pesq != null && pesq > 0 && pesq <= 5)
                    {
                        MOSParamName = "lte_PESQMos";
                    }
                    else if (polqa != null && polqa > 0 && polqa <= 5)
                    {
                        MOSParamName = "lte_POLQA_Score_SWB";
                    }
                    else
                    {
                        continue;
                    }
                }
                float? mos = (float?)tp[MOSParamName];
                if (mos != null && mos > 0 && mos < mosGate)
                {
                    listMosInfo.Add(new MosInfo(
                        new TimePeriod(tp.DateTime.AddSeconds(-10), tp.DateTime.AddSeconds(-2)),
                        tp.DateTime, (float)mos));
                }
            }
        }

        private void getMosInfo(ZTRtpPacketsLostMessageInfo lostInfo)
        {
            if (listMosInfo.Count == 0)
                return;
            //记录在RTP丢包时间段内存在的MOS周期
            List<MosInfo> listMosInPeriod = new List<MosInfo>();
            string timeFormat = "yy-MM-dd HH:mm:ss.fff";

            foreach (var mosInfo in listMosInfo)
            {
                //Mos值是从采样点中获取,采样点只有DateTime,因此要和对应的RTP丢包时间段的DateTime去比较
                if (mosInfo.MosPeriod.BeginTime > lostInfo.EndMsg.DateTime)
                {
                    break;
                }
                if (mosInfo.MosPeriod.EndTime < lostInfo.StartMsg.DateTime)
                {
                    continue;
                }

                listMosInPeriod.Add(mosInfo);
            }

            if (listMosInPeriod.Count == 0)
            {
                lostInfo.MosTime = string.Empty;
                lostInfo.MosVal = null;
                lostInfo.MosAveVal = null;
            }
            else if (listMosInPeriod.Count == 1)
            {
                lostInfo.MosTime = listMosInPeriod[0].PointTime.ToString(timeFormat);
                lostInfo.MosVal = listMosInPeriod[0].MosVal;
                lostInfo.MosAveVal = listMosInPeriod[0].MosVal;
            }
            else
            {
                float val = 0;
                listMosInPeriod.ForEach(m =>
                {
                    val += m.MosVal;
                    lostInfo.MosTime += m.PointTime.ToString(timeFormat) + ",";
                });
                lostInfo.MosTime = lostInfo.MosTime.TrimEnd(',');
                lostInfo.MosVal = listMosInPeriod[0].MosVal;
                lostInfo.MosAveVal = val / listMosInPeriod.Count;
            }
        }


        protected override void fireShowForm()
        {
            if (listFileInfo.Count == 0)
            {
                MessageBox.Show("没有符合条件的信息！");
                return;
            }

            ZTRtpPacketsLostShowForm frm = MainModel.GetInstance().CreateResultForm(typeof(ZTRtpPacketsLostShowForm)) as ZTRtpPacketsLostShowForm;
            frm.FillData(listFileInfo);
            frm.Visible = true;
            frm.BringToFront();

        }

    }

    public class ZTRtpPacketsLostMessageInfo
    {
        public ZTRtpPacketsLostMessageInfo(Model.Message startMsg, Model.Message endtMsg)
        {
            this.StartMsg = startMsg;
            this.EndMsg = endtMsg;
            TestPoints = new List<TestPoint>();
        }

        public List<TestPoint> TestPoints { get; set; }
        public Model.Message StartMsg { get; set; }
        public Model.Message EndMsg { get; set; }
        public int SN { get; set; }
        public string Area { get; set; }
        public string Grid { get; set; }
        public string RoadName { get; set; }
        public string FileName { get; set; }
        public DateTime? SCallTime { private get; set; }
        public string SCallTimeStr
        {
            get
            {
                return stringGetStringDate(SCallTime);
            }
        }
        public DateTime? SCallCompleteTime { private get; set; }
        public string SCallCompleteTimeStr
        {
            get
            {
                return stringGetStringDate(SCallCompleteTime);
            }
        }
        public string Direction { get; set; }
        public DateTime? SLossTime { private get; set; }
        public string SLossTimeStr
        {
            get
            {
                return stringGetStringDate(SLossTime);
            }
        }
        public DateTime? SLossCompleteTime { private get; set; }
        public string SLossCompleteTimeStr
        {
            get
            {
                return stringGetStringDate(SLossCompleteTime);
            }
        }
        private double longitude;
        public double Longitude
        {
            get { return longitude; }
        }

        private double latitude;
        public double Latitude
        {
            get { return latitude; }
        }

        private double? lossTime;
        public double? LossTime
        {
            get { return lossTime; }
        }

        private long lossNumber;
        public long LossNumber
        {
            get { return lossNumber; }
        }

        private string lossReason;
        public string LossReason
        {
            get { return lossReason; }
        }


        public string MosTime { get; set; }
        public float? MosVal { get; set; }
        public float? MosAveVal { get; set; }

        private string lossCellName;
        public string LossCellName
        {
            get { return lossCellName; }
        }

        private double? rsrp;
        public double? RSRP
        {
            get { return rsrp; }
        }

        private double? sinr;
        public double? SINR
        {
            get { return sinr; }
        }

        private double? pathLoss;
        public double? PathLoss
        {
            get
            {
                return pathLoss;
            }
        }

        private double? pdsch_bler;
        public double? Pdsch_bler
        {
            get
            {
                return pdsch_bler;
            }
        }

        private double? pusch_bler;
        public double? Pusch_bler
        {
            get
            {
                return pusch_bler;
            }
        }

        private double? uetxpower;
        public double? Uetxpower
        {
            get
            {
                return uetxpower;
            }
        }

        private double? rbCount;
        public double? RBCount
        {
            get
            {
                return rbCount;
            }
        }

        private int? earfcn;
        public int? EARFCN
        {
            get
            {
                return earfcn;
            }
        }

        private int? pdcch_UL_Grant_Count;
        public int? PDCCH_UL_Grant_Count
        {
            get
            {
                return pdcch_UL_Grant_Count;
            }
        }

        private int? pdcch_DL_Grant_Count;
        public int? PDCCH_DL_Grant_Count
        {
            get
            {
                return pdcch_DL_Grant_Count;
            }
        }

        private int? mcsCode0_DL;
        public int? MCSCode0_DL
        {
            get
            {
                return mcsCode0_DL;
            }
        }

        private int? mcsCode1_DL;
        public int? MCSCode1_DL
        {
            get
            {
                return mcsCode1_DL;
            }
        }

        private int? mcs_UL;
        public int? MCS_UL
        {
            get
            {
                return mcs_UL;
            }
        }

        private double? pdsch_Code0_BLER;
        public double? PDSCH_Code0_BLER
        {
            get
            {
                return pdsch_Code0_BLER;
            }
        }

        private double? pdsch_Code1_BLER;
        public double? PDSCH_Code1_BLER
        {
            get
            {
                return pdsch_Code1_BLER;
            }
        }

        private int? pdsch_CCE_Start;
        public int? PDCCH_CCE_Start
        {
            get
            {
                return pdsch_CCE_Start;
            }
        }

        private int? pdsch_CCEs_Number;
        public int? PDCCH_CCEs_Number
        {
            get
            {
                return pdsch_CCEs_Number;
            }
        }

        private double? times_QPSK_UL;
        public double? Times_QPSK_UL
        {
            get
            {
                return times_QPSK_UL;
            }
        }

        private double? times_QAM16_UL;
        public double? Times_QAM16_UL
        {
            get
            {
                return times_QAM16_UL;
            }
        }

        private double? times_QAM64_UL;
        public double? Times_QAM64_UL
        {
            get
            {
                return times_QAM64_UL;
            }
        }

        private int? pusch_PRb_Num_slot;
        public int? PUSCH_PRb_Num_slot
        {
            get
            {
                return pusch_PRb_Num_slot;
            }
        }

        private double? pusch_Initial_BLER;
        public double? PUSCH_Initial_BLER
        {
            get
            {
                return pusch_Initial_BLER;
            }
        }

        private int? transmission_Mode;
        public int? Transmission_Mode
        {
            get
            {
                return transmission_Mode;
            }
        }

        private int? rank;
        public int? Rank
        {
            get
            {
                return rank;
            }
        }

        private double? times_QPSK_DLCode0;
        public double? Times_QPSK_DLCode0
        {
            get
            {
                return times_QPSK_DLCode0;
            }
        }

        private double? times_QPSK_DLCode1;
        public double? Times_QPSK_DLCode1
        {
            get
            {
                return times_QPSK_DLCode1;
            }
        }

        private double? times_QAM16_DLCode0;
        public double? Times_QAM16_DLCode0
        {
            get
            {
                return times_QAM16_DLCode0;
            }
        }

        private double? times_QAM16_DLCode1;
        public double? Times_QAM16_DLCode1
        {
            get
            {
                return times_QAM16_DLCode1;
            }
        }

        private double? times_QAM64_DLCode0;
        public double? Times_QAM64_DLCode0
        {
            get
            {
                return times_QAM64_DLCode0;
            }
        }

        private double? times_QAM64_DLCode1;
        public double? Times_QAM64_DLCode1
        {
            get
            {
                return times_QAM64_DLCode1;
            }
        }

        private int? pdsch_PRb_Num_slot;
        public int? PDSCH_PRb_Num_slot
        {
            get
            {
                return pdsch_PRb_Num_slot;
            }
        }

        private double? pdsch_Init_BLER;
        public double? PDSCH_Init_BLER
        {
            get
            {
                return pdsch_Init_BLER;
            }
        }

        private double? pdsch_Init_BLERCode0;
        public double? PDSCH_Init_BLERCode0
        {
            get
            {
                return pdsch_Init_BLERCode0;
            }
        }

        private double? pdsch_Init_BLERCode1;
        public double? PDSCH_Init_BLERCode1
        {
            get
            {
                return pdsch_Init_BLERCode1;
            }
        }

        public void FillData(TestPoint tpTol)
        {
            longitude = tpTol.Longitude;
            latitude = tpTol.Latitude;

            TimeSpan? ts = EndMsg.HandsetTime - StartMsg.HandsetTime;
            if (ts != null)
            {
                lossTime = Math.Round(((TimeSpan)ts).TotalSeconds, 3);
            }

            LTECell lteCell = tpTol.GetMainCell_LTE();
            if (lteCell != null)
            {
                lossCellName = lteCell.Name;
            }
            lossNumber = getLossNum(StartMsg, EndMsg);

            rsrp = getDoubleValue(tpTol["lte_RSRP"]);
            sinr = getDoubleValue(tpTol["lte_SINR"]);
            pathLoss = getDoubleValue(tpTol["lte_Pathloss"]);
            pdsch_bler = getDoubleValue(tpTol["lte_PDSCH_BLER"]);
            pusch_bler = getDoubleValue(tpTol["lte_PUSCH_BLER"]);
            uetxpower = getIntValue(tpTol["lte_PUSCH_Power"]);
            rbCount = getIntValue(tpTol["lte_PDSCH_RB_Number"]);

            earfcn = getIntValue(tpTol["lte_EARFCN"]);
            pdcch_UL_Grant_Count = getIntValue(tpTol["lte_PDCCH_UL_Grant_Count"]);
            pdcch_DL_Grant_Count = getIntValue(tpTol["lte_PDCCH_DL_Grant_Count"]);
            mcsCode0_DL = getIntValue(tpTol["lte_MCSCode0_DL"]);
            mcsCode1_DL = getIntValue(tpTol["lte_MCSCode1_DL"]);
            mcs_UL = getIntValue(tpTol["lte_MCS_UL"]);
            pdsch_Code0_BLER = getDoubleValue(tpTol["lte_PDSCH_Code0_BLER"]);
            pdsch_Code1_BLER = getDoubleValue(tpTol["lte_PDSCH_Code1_BLER"]);
            pdsch_CCE_Start = getIntValue(tpTol["lte_PDCCH_CCE_Start"]);
            pdsch_CCEs_Number = getIntValue(tpTol["lte_PDCCH_CCEs_Number"]);
            times_QPSK_UL = getIntValue(tpTol["lte_Times_QPSK_UL"]);
            times_QAM16_UL = getIntValue(tpTol["lte_Times_QAM16_UL"]);
            times_QAM64_UL = getIntValue(tpTol["lte_Times_QAM64_UL"]);
            pusch_PRb_Num_slot = getIntValue(tpTol["lte_PUSCH_PRb_Num_slot"]);
            pusch_Initial_BLER = getDoubleValue(tpTol["lte_PUSCH_Initial_BLER"]);
            transmission_Mode = getIntValue(tpTol["lte_Transmission_Mode"]);
            rank = getIntValue(tpTol["lte_Rank_Indicator"]);
            times_QPSK_DLCode0 = getIntValue(tpTol["lte_Times_QPSK_DLCode0"]);
            times_QPSK_DLCode1 = getIntValue(tpTol["lte_Times_QPSK_DLCode1"]);
            times_QAM16_DLCode0 = getIntValue(tpTol["lte_Times_QAM16_DLCode0"]);
            times_QAM16_DLCode1 = getIntValue(tpTol["lte_Times_QAM16_DLCode1"]);
            times_QAM64_DLCode0 = getIntValue(tpTol["lte_Times_QAM64_DLCode0"]);
            times_QAM64_DLCode1 = getIntValue(tpTol["lte_Times_QAM64_DLCode1"]);
            pdsch_PRb_Num_slot = getIntValue(tpTol["lte_PDSCH_PRb_Num_slot"]);
            pdsch_Init_BLER = getDoubleValue(tpTol["lte_PDSCH_Init_BLER"]);
            pdsch_Init_BLERCode0 = getDoubleValue(tpTol["lte_PDSCH_Init_BLER_Code0"]);
            pdsch_Init_BLERCode1 = getDoubleValue(tpTol["lte_PDSCH_Init_BLER_Code1"]);
        }

        public void FillData_NR(TestPoint tpTol)
        {
            longitude = tpTol.Longitude;
            latitude = tpTol.Latitude;

            TimeSpan? ts = EndMsg.HandsetTime - StartMsg.HandsetTime;
            if (ts != null)
            {
                lossTime = Math.Round(((TimeSpan)ts).TotalSeconds, 3);
            }

            var nrCell = tpTol.GetMainCell_NR();
            if (nrCell != null)
            {
                lossCellName = nrCell.Name;
            }
            lossNumber = getLossNum(StartMsg, EndMsg);

            rsrp = getDoubleValue(tpTol["NR_SS_RSRP"]);
            sinr = getDoubleValue(tpTol["NR_SS_SINR"]);
            lossReason = getLossReason();

            pathLoss = getIntValue(tpTol["NR_PUCCH_PathLoss"]);
            pdsch_bler = getDoubleValue(tpTol["NR_PDSCH_BLER"]);
            pusch_bler = getDoubleValue(tpTol["NR_PUSCH_BLER"]);
            uetxpower = getDoubleValue(tpTol["NR_PUSCH_TxPower"]);
            rbCount = getDoubleValue(tpTol["NR_PDSCH_BLER"]);

            earfcn = getIntValue(tpTol["NR_SSB_ARFCN"]);
            pdcch_UL_Grant_Count = getByteValue(tpTol["NR_PDCCH_UL_GrantCount"]);
            pdcch_DL_Grant_Count = getByteValue(tpTol["NR_PDCCH_DL_GrantCount"]);
            mcsCode0_DL = getIntValue(tpTol["NR_DL_TB0_Avg_MCS"]);
            mcsCode1_DL = getIntValue(tpTol["NR_DL_TB1_Avg_MCS"]);
            mcs_UL = getIntValue(tpTol["NR_MCS_UL_Avg"]);
            pdsch_Code0_BLER = getDoubleValue(tpTol["NR_PDSCH_BLER"]);
            pdsch_Code1_BLER = getDoubleValue(tpTol["NR_PDSCH_BLER"]);
            pdsch_CCE_Start = getIntValue(tpTol["lte_PDCCH_CCE_Start"]);
            pdsch_CCEs_Number = getIntValue(tpTol["lte_PDCCH_CCEs_Number"]);
            times_QPSK_UL = getDoubleValue(tpTol["NR_QPSK_Ratio_UL_s"]);
            times_QAM16_UL = getDoubleValue(tpTol["NR_16QAM_Ratio_UL_s"]);
            times_QAM64_UL = getDoubleValue(tpTol["NR_64QAM_Ratio_UL_s"]);
            times_QAM64_DLCode1 = getDoubleValue(tpTol["NR_256QAM_Ratio_UL_s"]);
            pusch_PRb_Num_slot = getIntValue(tpTol["lte_PUSCH_PRb_Num_slot"]);
            pusch_Initial_BLER = getDoubleValue(tpTol["NR_PUSCH_Initial_BLER"]);
            transmission_Mode = getIntValue(tpTol["lte_Transmission_Mode"]);
            rank = getIntValue(tpTol["NR_Total_Rank_Count_DL_s"]);
            times_QPSK_DLCode0 = getDoubleValue(tpTol["NR_BPSK_Ratio_DL_s"]);
            times_QPSK_DLCode1 = getDoubleValue(tpTol["NR_QPSK_Ratio_DL_s"]);
            times_QAM16_DLCode0 = getDoubleValue(tpTol["NR_16QAM_Ratio_DL_s"]);
            times_QAM16_DLCode1 = getDoubleValue(tpTol["NR_64QAM_Ratio_DL_s"]);
            times_QAM64_DLCode0 = getDoubleValue(tpTol["NR_256QAM_Ratio_DL_s"]);
            pdsch_PRb_Num_slot = getIntValue(tpTol["NR_PRB_Num_DL_slot"]);
            pdsch_Init_BLER = getDoubleValue(tpTol["NR_PDSCH_Initial_BLER"]);
            pdsch_Init_BLERCode0 = getDoubleValue(tpTol["NR_PDSCH_Initial_BLER"]);
            pdsch_Init_BLERCode1 = getDoubleValue(tpTol["NR_PDSCH_Initial_BLER"]);
        }

        private string stringGetStringDate(DateTime? dt)
        {
            if (dt != null)
            {
                return ((DateTime)dt).ToString("yy-MM-dd HH:mm:ss.fff");
            }
            return "";
        }

        private double? getDoubleValue(object dbValue)
        {
            if (dbValue != null)
            {
                double value = Convert.ToSingle(dbValue);
                return Math.Round(value, 3);
            }
            return 0;
        }

        private int? getIntValue(object dbValue)
        {
            if (dbValue != null)
            {
                return Convert.ToInt32(dbValue);
            }
            return 0;
        }

        private byte? getByteValue(object dbValue)
        {
            if (dbValue != null)
            {
                return Convert.ToByte(dbValue);
            }
            return 0;
        }

        private long getLossNum(Model.Message msgStart, Model.Message msgEnd)
        {
            string signRTPNumber = "eSam_VOLTE_RTP_Sequence_Number";
            long result;
            long lossNumStart = 0;
            long lossNumEnd = 0;
            MessageWithSource msgStartWithSource = msgStart as MessageWithSource;
            MessageWithSource msgEndWithSource = msgEnd as MessageWithSource;

            OwnMsgDecode.StartDissect(msgStartWithSource.Source, msgStartWithSource.ID);
            OwnMsgDecode.GetIntValue(signRTPNumber, ref lossNumStart);
            OwnMsgDecode.StartDissect(msgEndWithSource.Source, msgEndWithSource.ID);
            OwnMsgDecode.GetIntValue(signRTPNumber, ref lossNumEnd);

            result = lossNumEnd - lossNumStart;
            return result;
        }

        private string getLossReason()
        {
            if (rsrp != null && rsrp < -88)
            {
                return "切换不合理";
            }
            else if (sinr != null && sinr < 3)
            {
                return "过覆盖";
            }

            return "覆盖不稳定";
        }
    }

    public class MosInfo
    {
        public MosInfo(TimePeriod mosPeriod, DateTime pointTime, float mosVal)
        {
            this.MosPeriod = mosPeriod;
            this.PointTime = pointTime;
            this.MosVal = mosVal;
        }

        public TimePeriod MosPeriod { get; set; }
        public DateTime PointTime { get; set; }
        public float MosVal { get; set; }
    }

    public class MsgTime
    {
        public MsgTime(TimePeriod handsetsTime, TimePeriod completeTime)
        {
            this.HandsetsTime = handsetsTime;
            this.CompleteTime = completeTime;
        }

        public TimePeriod HandsetsTime { get; set; }
        public TimePeriod CompleteTime { get; set; }
    }

    public class ZTRtpPacketsLostFileInfo
    {
        public List<ZTRtpPacketsLostMessageInfo> MessageInfos { get; set; }

        public string FileName { get; set; }

        public ZTRtpPacketsLostFileInfo(List<ZTRtpPacketsLostMessageInfo> dlMessageInfos, List<ZTRtpPacketsLostMessageInfo> ulMessageInfos, string fileName)
        {
            this.MessageInfos = dlMessageInfos;
            this.MessageInfos.AddRange(ulMessageInfos);
            this.FileName = fileName;
            GetSN();
        }

        private void GetSN()
        {
            int index = 1;

            if (MessageInfos != null)
                MessageInfos.ForEach(msg =>
                {
                    msg.SN = index++;
                });
        }
    }

    public class ZTRtpPacketsLostMessageConditon
    {
        public double RtpLossRate { get; set; }
        public float LossTime { get; set; }

        public ZTRtpPacketsLostMessageConditon()
        {
            RtpLossRate = 0.5;
            LossTime = 1;
        }
    }
}
