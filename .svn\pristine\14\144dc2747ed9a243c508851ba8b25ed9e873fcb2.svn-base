﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MapWinGIS;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.Injection
{
    public partial class RegionRoadLayerSettingDlg : BaseDialog
    {
        public RegionRoadLayerSettingDlg()
        {
            InitializeComponent();
            dataGridView.AutoGenerateColumns = false;
            foreach (CategoryEnumItem item in AreaManager.GetInstance().AreaTypes.Items)
            {
                cmbAreaType.Properties.Items.Add(item);
            }
        }

        private void btnRegionPath_Properties_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                Shapefile file = new Shapefile();
                if (!file.Open(dlg.FileName, null))
                {
                    MessageBox.Show("打开图层文件异常！");
                    return;
                }
                if (file.ShapefileType != ShpfileType.SHP_POLYGON)
                {
                    MessageBox.Show("该图层不是有效的多边形图层！");
                    return;
                }
                btnRegionPath.Text = dlg.FileName;
                cmbFieldIdx.Properties.Items.Clear();
                for (int i = 0; i < file.NumFields; i++)
                {
                    cmbFieldIdx.Properties.Items.Add(file.get_Field(i).Name);
                }
                file.Close();
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            RegionPath = btnRegionPath.Text;
            if (string.IsNullOrEmpty(RegionPath))
            {
                MessageBox.Show("请添加区域图层！");
                return;
            }
            if (cmbFieldIdx.SelectedIndex < 0)
            {
                MessageBox.Show("请选择区域ID列！");
                return;
            }
            RegionIDIdx = cmbFieldIdx.SelectedIndex;

            if (cmbAreaType.SelectedItem == null)
            {
                MessageBox.Show("请选择AreaType！");
                return;
            }
            CategoryEnumItem cateItem = cmbAreaType.SelectedItem as CategoryEnumItem;
            AreaType = cateItem.ID;

            if (roadLayerSet.Count == 0)
            {
                MessageBox.Show("请添加道路图层！");
                return;
            }
            DialogResult = DialogResult.OK;
        }

        public string RegionPath { get; set; }

        public int RegionIDIdx { get; set; }

        public int AreaType { get; set; }

        private void btnRemoveShpFile_Click(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count > 0)
            {
                foreach (DataGridViewRow row in dataGridView.SelectedRows)
                {
                    StreetInjectTableInfo layer = row.DataBoundItem as StreetInjectTableInfo;
                    roadLayerSet.Remove(layer);
                }
                dataGridView.DataSource = null;
                if (this.roadLayerSet.Count > 0)
                {
                    dataGridView.DataSource = this.roadLayerSet;
                    dataGridView.CurrentCell = dataGridView.Rows[0].Cells[1];
                }
                dataGridView.Invalidate();
            }
            else
            {
                MessageBox.Show("请选择要移除的图层文件！");
            }
        }

        private void btnAddShpFile_Click(object sender, EventArgs e)
        {
            addShpFile(null);
        }

        List<StreetInjectTableInfo> roadLayerSet = new List<StreetInjectTableInfo>();
        public List<StreetInjectTableInfo> RoadLayers
        {
            get { return roadLayerSet; }
        }
        private void addShpFile(string filePath)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = FilterHelper.Shp;
            dlg.Multiselect = filePath == null;
            dlg.FileName = filePath;
            List<StreetInjectTableInfo> layers = new List<StreetInjectTableInfo>();
            StringBuilder strbInvalidFiles = new StringBuilder();
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                foreach (string path in dlg.FileNames)
                {
                    removeExistStreetTable(path);

                    List<string> fields;
                    Shapefile shpFile = getShpFileProp(path, out fields);
                    if (shpFile != null && shpFile.ShapefileType != ShpfileType.SHP_POLYLINE)
                    {
                        StreetInjectTableInfo info = new StreetInjectTableInfo();
                        info.FilePath = path;
                        info.Fields = fields;
                        layers.Add(info);
                    }
                    else
                    {
                        strbInvalidFiles.AppendLine(path);
                    }
                }
            }

            if (strbInvalidFiles.Length > 0)
            {
                MessageBox.Show("非多边形图层文件：" + Environment.NewLine + strbInvalidFiles.ToString());
            }
            if (layers.Count > 0)
            {
                this.roadLayerSet.AddRange(layers);
            }
            if (this.roadLayerSet.Count > 0)
            {
                dataGridView.DataSource = this.roadLayerSet;
                dataGridView.CurrentCell = dataGridView.Rows[0].Cells[1];
            }
            dataGridView.Invalidate();
        }

        private void removeExistStreetTable(string path)
        {
            StreetInjectTableInfo old = this.roadLayerSet.Find(delegate (StreetInjectTableInfo x)
            { return x.FilePath == path; });
            if (old != null)
            {
                roadLayerSet.Remove(old);
            }
        }

        private void dataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex != colShpFile.Index)
            {
                return;
            }

            string fileName = null;
            DataGridViewRow row = dataGridView.Rows[e.RowIndex];
            if (row != null)
            {
                StreetInjectTableInfo layer = row.DataBoundItem as StreetInjectTableInfo;
                if (layer != null)
                {
                    fileName = layer.FilePath;
                }
            }
            addShpFile(fileName);
        }

        private Shapefile getShpFileProp(string fileName, out List<string> fields)
        {
            fields = new List<string>();
            Shapefile shp = new Shapefile();
            if (!shp.Open(fileName, null))
            {
                return null;
            }

            for (int i = 0; i < shp.NumFields; ++i)
            {
                Field field = shp.get_Field(i);
                fields.Add(field.Name);
            }
            shp.Close();
            return shp;
        }

        private void dataGridView_DataSourceChanged(object sender, EventArgs e)
        {
            foreach (DataGridViewRow row in dataGridView.Rows)
            {
                StreetInjectTableInfo layer = row.DataBoundItem as StreetInjectTableInfo;
                if (layer != null)
                {
                    DataGridViewComboBoxCell cbxCell = ((DataGridViewComboBoxCell)row.Cells["colFieldName"]);
                    cbxCell.Items.Clear();
                    foreach (string fName in layer.Fields)
                    {
                        cbxCell.Items.Add(fName);
                    }
                    if (cbxCell.Items.Count > 0)
                    {
                        cbxCell.Value = cbxCell.Items[0];
                    }
                }
            }
        }

        private void dataGridView_DataError(object sender, DataGridViewDataErrorEventArgs e)
        {
            e.ThrowException = false;
        }
    }
}
