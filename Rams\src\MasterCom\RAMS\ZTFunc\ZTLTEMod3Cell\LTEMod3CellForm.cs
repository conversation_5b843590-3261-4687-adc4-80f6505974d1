﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LTEMod3CellForm : MinCloseForm
    {
        public LTEMod3CellForm(MainModel mm) : base(mm)
        {
            InitializeComponent();

            chkValue0.Checked = true;
            chkValue1.Checked = true;
            chkValue2.Checked = true;
            numDistance.Value = 2000;
            numAngle.Value = 50;
            btnFind.TabIndex = 0;
        }

        private void BtnFind_Click(object sender, EventArgs e)
        {
            this.Cursor = Cursors.WaitCursor;
            LTEModXCondition cond = new LTEModXCondition();
            cond.Angle = (double)numAngle.Value;
            cond.Distance = (double)numDistance.Value;
            if (chkValue0.Checked)
            {
                cond.SIDs.Add(0);
            }
            if (chkValue1.Checked)
            {
                cond.SIDs.Add(1);
            }
            if (chkValue2.Checked)
            {
                cond.SIDs.Add(2);
            }

            LTEModXCellStater stater = new LTEModXCellStater(MainModel, cond);
            FillData(stater.GetModXCells());
            this.Cursor = Cursors.Default;
        }

        private void GridViewRow_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            LTEModXCell mxCell = gv.GetRow(gv.GetSelectedRows()[0]) as LTEModXCell;
            MainModel.SelectedLTECells = new List<LTECell>();
            if(mxCell.ModXCells!=null)
            {
                foreach (LTEModXCell modXcell in mxCell.ModXCells)
                {
                    modXcell.Cell.CellType = MainOrNBCell.Other;
                    MainModel.SelectedLTECells.Add(modXcell.Cell);
                }
            }
            mxCell.Cell.CellType = MainOrNBCell.MainCell;
            MainModel.SelectedLTECell = mxCell.Cell;
            MainModel.FireSelectedCellChanged(MainModel.MainForm);
            MainModel.MainForm.GetMapForm().GoToView(mxCell.Longitude, mxCell.Latitude);
        }

        private void ExportExcel_Click(object sender, EventArgs e)
        {
            if (isNeedHideCellKeyInfo)
            {
                List<List<object>> exportList = MasterCom.Util.GridViewTransfer.Transfer(gridControl1);
                MasterCom.Util.ExcelNPOIManager.ExportToExcel(exportList);
                return;
            }

            List<List<object>> content = new List<List<object>>();

            List<object> title = new List<object>();
            foreach (DevExpress.XtraGrid.Columns.GridColumn col in gridView1.VisibleColumns)
            {
                title.Add(col.Caption);
            }
            foreach (DevExpress.XtraGrid.Columns.GridColumn col in gridView2.VisibleColumns)
            {
                title.Add(col.Caption);
            }
            content.Add(title);

            #region Process Row in GridView
            /*
            ExpandAllRows(gridView1, true);
            this.Cursor = Cursors.WaitCursor;
            for (int i = 0; i < gridView1.RowCount; ++i)
            {
                List<object> curRow = new List<object>();
                foreach (DevExpress.XtraGrid.Columns.GridColumn col in gridView1.VisibleColumns)
                {
                    curRow.Add(gridView1.GetRowCellDisplayText(i, col));
                }

                DevExpress.XtraGrid.Views.Grid.GridView gv = gridView1.GetVisibleDetailView(i) as DevExpress.XtraGrid.Views.Grid.GridView;
                for (int j = 0; j < gv.RowCount; ++j)
                {
                    List<object> curSubRow = new List<object>(curRow);
                    foreach (DevExpress.XtraGrid.Columns.GridColumn col in gv.Columns)
                    {
                        curSubRow.Add(gv.GetRowCellDisplayText(j, col));
                    }
                    content.Add(curSubRow);
                }
            }
             */
            #endregion

            #region Process Row in DataSource
            List<LTEModXCell> cellList = gridControl1.DataSource as List<LTEModXCell>;
            foreach (LTEModXCell cell in cellList)
            {
                List<object> curRow = new List<object>();
                curRow.Add(cell.CellName);
                curRow.Add(cell.CellID);
                curRow.Add(cell.EARFCN);
                curRow.Add(cell.PCI);
                curRow.Add(cell.SID);
                curRow.Add(cell.Direction);
                curRow.Add(cell.Longitude);
                curRow.Add(cell.Latitude);
                curRow.Add(cell.MXCellCount);

                foreach (LTEModXCell sub in cell.ModXCells)
                {
                    List<object> curSubRow = new List<object>(curRow);
                    curSubRow.Add(sub.CellName);
                    curSubRow.Add(sub.CellID);
                    curSubRow.Add(sub.EARFCN);
                    curSubRow.Add(sub.PCI);
                    curSubRow.Add(sub.Longitude);
                    curSubRow.Add(sub.Latitude);
                    curSubRow.Add(sub.Distance);
                    content.Add(curSubRow);
                }
            }
            #endregion

            this.Cursor = Cursors.Default;
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(content);
            ExpandAllRows(gridView1, false);
        }

        private void FillData(object data)
        {
            gridControl1.DataSource = data;
            gridControl1.RefreshDataSource();
        }

        public void ExpandAllRows(DevExpress.XtraGrid.Views.Grid.GridView View, bool expand)
        {
            View.BeginUpdate();
            try
            {
                int dataRowCount = View.DataRowCount;
                for (int rHandle = 0; rHandle < dataRowCount; rHandle++)
                    View.SetMasterRowExpanded(rHandle, expand);
            }
            finally
            {
                View.EndUpdate();
            }
        }
    }
}
