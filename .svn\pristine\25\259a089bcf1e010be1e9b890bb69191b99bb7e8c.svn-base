﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MapWinGIS;

using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.FuncLayer;
using MasterCom.Util;

namespace MasterCom.RAMS.Func
{
    /// <summary>
    /// 扫频栅格重叠度查询类
    /// </summary>
    public class ScanGridAnaQueryControler : QueryBase
    {
        private ScanGridAnaSettingCondition cond;
        private ScanGridAnaCondition anaCond;
        private ScanGridAnaCondition cmpCond;
        private MapWinGIS.Shapefile shapeFile { get; set; }
        private ScanGridAnaSettingForm setForm;

        public ScanGridAnaQueryControler(MainModel mm) : base(mm)
        {
        }

        public override string Name
        {
            get { return "扫频栅格重叠度分析"; }
        }

        public override string IconName
        {
            get { return string.Empty; }
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 15000, 15040, this.Name);
        }

        protected override bool isValidCondition()
        {
            if (setForm == null)
            {
                setForm = new ScanGridAnaSettingForm(MainModel);
            }
            if (setForm.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            cond = ScanGridAnaSettingCondition.Instance;

            shapeFile = new MapWinGIS.Shapefile();
            if (!shapeFile.Open(cond.Shpfile, null))
            {
                MessageBox.Show("打开网格图层文件错误", "错误");
                return false;
            }
            if (shapeFile.ShapefileType != MapWinGIS.ShpfileType.SHP_POLYGON)
            {
                MessageBox.Show("网格图层文件类型错误", "错误");
                return false;
            }
            if (shapeFile.NumShapes == 0)
            {
                MessageBox.Show("网格图层文件不存在网格", "错误");
                return false;
            }
            Dictionary<MapWinGIS.Shape, string> shapeNameDic = GetShapeNameDic(shapeFile, cond.NameField);
            if (shapeNameDic == null || shapeNameDic.Count <= 0)
            {
                MessageBox.Show("从图层文件获取网格失败", "错误");
                return false;
            }
            shapeNameDic = UnionShapeNameDic(shapeNameDic);
            if (shapeNameDic == null || shapeNameDic.Count <= 0)
            {
                MessageBox.Show("网格合并失败", "错误");
                return false;
            }
            SetRsvRegionShowLayer(shapeNameDic);

            anaCond = new ScanGridAnaCondition();
            anaCond.TimePeriod = new MasterCom.Util.TimePeriod(cond.AnaStartTime, cond.AnaEndTime);
            anaCond.ServiceType = cond.ServiceType;
            anaCond.ProjectTypes = cond.AnaProjectTypes;
            anaCond.GridSize = cond.GridSize;
            anaCond.ShapeNameDic = shapeNameDic;
            anaCond.FileName = cond.AnaFileName;
            anaCond.FileNameOrNum = cond.AnaFileNameOrNum;
            anaCond.FilterType = cond.AnaFilterType;
            anaCond.Argument = new double[] { cond.RelativeValue, cond.AbsoluteValue, cond.ValidValue, cond.WeakRxlev, cond.HighCoverage, cond.ConsecutiveCount, cond.RelativeMin, };
            anaCond.ServiceTypes_GSM = cond.AnaServiceType_GSM;
            anaCond.ServiceTypes_TD = cond.AnaServiceType_TD;
            anaCond.Carriers = cond.Carriers;
            if (!anaCond.IsValid())
            {
                MessageBox.Show("分析数据源条件设置不正确", "错误");
                return false;
            }

            if (cond.IsCmpUsed)
            {
                cmpCond = new ScanGridAnaCondition();
                cmpCond.TimePeriod = new MasterCom.Util.TimePeriod(cond.CmpStartTime, cond.CmpEndTime);
                cmpCond.ServiceType = cond.ServiceType;
                cmpCond.ProjectTypes = cond.CmpProjectTypes;
                cmpCond.GridSize = cond.GridSize;
                cmpCond.ShapeNameDic = shapeNameDic;
                cmpCond.FileName = cond.CmpFileName;
                cmpCond.FileNameOrNum = cond.CmpFileNameOrNum;
                cmpCond.FilterType = cond.CmpFilterType;
                cmpCond.Argument = new double[] { cond.RelativeValue, cond.AbsoluteValue, cond.ValidValue, cond.WeakRxlev, cond.HighCoverage, cond.ConsecutiveCount, cond.RelativeMin};
                cmpCond.ServiceTypes_GSM = cond.CmpServiceType_GSM;
                cmpCond.ServiceTypes_TD = cond.CmpServiceType_TD;
                cmpCond.Carriers = cond.Carriers;
                if (!cmpCond.IsValid())
                {
                    MessageBox.Show("对比数据源条件设置不正确", "错误");
                    return false;
                }
            }
            else
            {
                cmpCond = null;
            }

            return true;
        }

        protected override void query()
        {
            ScanGridAnaQuery query = new ScanGridAnaQuery(MainModel);
            query.AnaCondition = anaCond;
            query.Query();
            ScanGridAnaResult anaResult = query.AnaResult;

            ScanGridAnaResult cmpResult = null;
            if (cond.IsCmpUsed)
            {
                query.AnaCondition = cmpCond;
                query.Query();
                cmpResult = query.AnaResult;
            }

            DoStatResult(anaResult, cmpResult);
        }

        private void DoStatResult(ScanGridAnaResult anaResult, ScanGridAnaResult cmpResult)
        {
            ScanGridMultiCoverageResultForm form = MainModel.GetObjectFromBlackboard(typeof(ScanGridMultiCoverageResultForm).FullName) as ScanGridMultiCoverageResultForm;
            if (form == null || form.IsDisposed)
            {
                form = new ScanGridMultiCoverageResultForm(MainModel);
            }
            List<TimePeriod> timePeriods = new List<TimePeriod>();
            timePeriods.Add(anaCond.TimePeriod);
            if (cmpCond != null)
            {
                timePeriods.Add(cmpCond.TimePeriod);
            }
            form.FillData(anaResult, cmpResult, cond.ServiceType, timePeriods);
            form.Show(MainModel.MainForm);
        }

        private Dictionary<MapWinGIS.Shape, string> GetShapeNameDic(Shapefile shpFile, string nameField)
        {
            Dictionary<MapWinGIS.Shape, string> retDic = new Dictionary<MapWinGIS.Shape, string>();

            int fieldIndex = -1;
            for (int i = 0; i < shpFile.NumFields; ++i)
            {
                Field field = shpFile.get_Field(i);
                if (field.Name == nameField)
                {
                    fieldIndex = i;
                    break;
                }
            }
            if (fieldIndex == -1)
            {
                return new Dictionary<MapWinGIS.Shape, string>();
            }

            int shapeCount = shpFile.NumShapes;
            for (int i = 0; i < shapeCount; ++i)
            {
                MapWinGIS.Shape shape = shpFile.get_Shape(i);
                string value = shpFile.get_CellValue(fieldIndex, i).ToString();
                retDic.Add(shape, value);
            }

            return retDic;
        }

        private Dictionary<MapWinGIS.Shape, string> UnionShapeNameDic(Dictionary<MapWinGIS.Shape, string> shapeNameDic)
        {
            Dictionary<string, MapWinGIS.Shape> tmpNameShapeDic = new Dictionary<string, MapWinGIS.Shape>();
            foreach (MapWinGIS.Shape key in shapeNameDic.Keys)
            {
                string value = shapeNameDic[key];
                if (!tmpNameShapeDic.ContainsKey(value))
                {
                    tmpNameShapeDic.Add(value, key);
                    continue;
                }

                MapWinGIS.Shape shape = tmpNameShapeDic[value];
                MapWinGIS.Shape unionResult = shape.Clip(key, tkClipOperation.clUnion);
                if (unionResult == null)
                {
                    return new Dictionary<MapWinGIS.Shape, string>();
                }
                tmpNameShapeDic[value] = unionResult;
            }

            Dictionary<MapWinGIS.Shape, string> retDic = new Dictionary<MapWinGIS.Shape, string>();
            foreach (KeyValuePair<string, MapWinGIS.Shape> kvp in tmpNameShapeDic)
            {
                string key = kvp.Key;
                if (key == "")
                {
                    key = "未命名网格";
                }
                retDic.Add(kvp.Value, key);
            }

            return retDic;
        }

        public void SetRsvRegionShowLayer(Dictionary<MapWinGIS.Shape, string> shapeNameDic)
        {
            RsvRegionShowLayer regionLayer = MainModel.MainForm.GetMapForm().RsvRegionShowLayer;
            List<ResvRegion> resvRegionList = new List<ResvRegion>();
            foreach (KeyValuePair<MapWinGIS.Shape, string> kvp in shapeNameDic)
            {
                ResvRegion reg = new ResvRegion();
                reg.RegionName = kvp.Value;
                reg.Shape = kvp.Key;
                resvRegionList.Add(reg);
            }
            regionLayer.ApplyRegions(resvRegionList);
        }
    }
}
