﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTLteNBCellOmitAnaSetForm : BaseDialog
    {
        public ZTLteNBCellOmitAnaSetForm()
        {
            InitializeComponent();
        }

        public ZTLteNBCellOmitCondition GetCondition()
        {
            ZTLteNBCellOmitCondition condition = new ZTLteNBCellOmitCondition();
            condition.MRCount = (int)numMRCount.Value;
            condition.RSRP = (int)numRSRP.Value;
            condition.Distance = (int)numDistance.Value;
            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
