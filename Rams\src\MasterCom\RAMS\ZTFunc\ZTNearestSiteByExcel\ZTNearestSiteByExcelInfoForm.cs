﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraEditors;
using System.Windows.Forms;
using MasterCom.MTGis;
using AxMapWinGIS;


namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTNearestSiteByExcelInfoForm : MinCloseForm
    {
        List<SiteBaseInfo> siteInfoNew;
        private List<Line> lines = new List<Line>();
        private Font fontMeasure;
        private SiteBaseInfo showSite;
        private AxMap mapControl;
        public ZTNearestSiteByExcelInfoForm()
        {
            InitializeComponent();
            gridView1.DoubleClick += GridView_DoubleClick;
            gridView2.DoubleClick += GridView_DoubleClick;
            fontMeasure = new Font("宋体", 11, FontStyle.Bold);
            mapControl = MainModel.GetInstance().MainForm.GetMapForm().GetMapFormControl();
        }
        public void FillData(List<SiteBaseInfo> resultList)
        {
            siteInfoNew = resultList;
            gridControl1.DataSource = resultList;
            gridControl1.RefreshDataSource();
            MainModel.DTDataManager.Clear();
            MainModel.FireDTDataChanged(this);
        }
        public void GridView_DoubleClick(object sender, EventArgs e)
        {
            MainModel.DTDataManager.Clear();
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            if (gv.GetRow(gv.GetSelectedRows()[0]) is SiteBaseInfo)
            {
                lines.Clear();
                SiteBaseInfo handoverInfo = gv.GetRow(gv.GetSelectedRows()[0]) as SiteBaseInfo;
                showSite = handoverInfo;
                draw(handoverInfo);
                TempLayer.Instance.Draw(DrawLine);
                DbRect bounds = GetBounds(showSite);
                MainModel.MainForm.GetMapForm().GoToViewCellsBounds(bounds);
            }
            else if (gv.GetRow(gv.GetSelectedRows()[0]) is SiteNearbyInfo)
            {
                SiteNearbyInfo ncell = gv.GetRow(gv.GetSelectedRows()[0]) as SiteNearbyInfo;
                MainModel.MainForm.GetMapForm().GoToView(ncell.Longitude, ncell.Latitude);
            }
            MainModel.FireDTDataChanged(this);
            MainModel.FireSelectedCellChanged(this);
        }
        private void draw(SiteBaseInfo item)
        {
            for (int i = 0; i < item.NearSiteCounts; i++)
            {
                    Line line = new Line();
                    line.P1 = new DbPoint(item.Longitude, item.Latitude);
                    line.P2 = new DbPoint(item.NearbyInfo[i].Longitude, item.NearbyInfo[i].Latitude);
                    line.LineColor = Color.Red;
                    lines.Add(line);
            }
        }
        private DbRect GetBounds(SiteBaseInfo info)
        {
            double minjd = info.Longitude;
            double maxjd = info.Longitude;
            double minwd = info.Latitude;
            double maxwd = info.Latitude;
            foreach (SiteNearbyInfo nearInfo in info.NearbyInfo)
            {
                minjd = Math.Min(minjd, nearInfo.Longitude);
                maxjd = Math.Max(maxjd, nearInfo.Longitude);
                minwd = Math.Min(minwd, nearInfo.Latitude);
                maxwd = Math.Max(maxwd, nearInfo.Latitude);
            }
            DbRect drect = new DbRect(minjd, minwd, maxjd, maxwd);
            if (drect.x1 == drect.x2 && drect.y1 == drect.y2)
            {
                drect.x1 -= 0.01;
                drect.x2 += 0.01;
                drect.y1 -= 0.01;
                drect.y2 += 0.01;
            }
            return drect;
        }
        private void DrawLine(Rectangle clientRect, Rectangle updateRect, Graphics graphics, MapOperation mop)
        {
            if (lines == null || lines.Count == 0)
            {
                return;
            }
            string str = showSite.CellName;
            double X = 0;
            double Y = 0;
            mapControl.ProjToPixel(showSite.Longitude, showSite.Latitude, ref X, ref Y);
            graphics.DrawString(str, fontMeasure,Brushes.OrangeRed,(float) X,(float)Y);
            foreach (SiteNearbyInfo temp in showSite.NearbyInfo)
            {
                string strnew = temp.Identifier + " 距离为：" + temp.Distance.ToString() + "米";
                double x = 0;
                double y = 0;
                mapControl.ProjToPixel(temp.Longitude, temp.Latitude, ref x, ref y);
                graphics.DrawString(strnew, fontMeasure, Brushes.OrangeRed,(float)x, (float)y);
            }
            foreach (Line line in lines)
            {
                PointF p1, p2;
                mop.ToDisplay(line.P1, out p1);
                mop.ToDisplay(line.P2, out p2);
                Pen pen = new Pen(line.LineColor, 1);
                pen.DashStyle = System.Drawing.Drawing2D.DashStyle.Dash;

                graphics.DrawLine(pen, p1, p2);
            }
        }
        private void ExcelToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(getNPOIRowsByGridView());
        }
        private List<NPOIRow> getNPOIRowsByGridView()
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            foreach (GridColumn col in this.gridView1.Columns)
            {
                row.AddCellValue(col.Caption);
            }
            foreach (GridColumn col in this.gridView2.Columns)
            {
                row.AddCellValue(col.Caption);
            }
            rows.Add(row);
            for (int i = 0; i < siteInfoNew.Count; i++)
            {
                row = new NPOIRow();
                rows.Add(row);
                row.AddCellValue(siteInfoNew[i].SN);
                row.AddCellValue(siteInfoNew[i].CellName);
                row.AddCellValue(siteInfoNew[i].Longitude);
                row.AddCellValue(siteInfoNew[i].Latitude);
                row.AddCellValue(siteInfoNew[i].NearSiteCounts);
                for (int j = 0; j < siteInfoNew[i].NearbyInfo.Count; j++)
                {
                    NPOIRow subRow = new NPOIRow();
                    row.AddSubRow(subRow);
                    subRow.AddCellValue(siteInfoNew[i].NearbyInfo[j].SN);
                    subRow.AddCellValue(siteInfoNew[i].NearbyInfo[j].Type);
                    subRow.AddCellValue(siteInfoNew[i].NearbyInfo[j].Identifier);
                    subRow.AddCellValue(siteInfoNew[i].NearbyInfo[j].Longitude);
                    subRow.AddCellValue(siteInfoNew[i].NearbyInfo[j].Latitude);
                    subRow.AddCellValue(siteInfoNew[i].NearbyInfo[j].DNumber);
                    subRow.AddCellValue(siteInfoNew[i].NearbyInfo[j].FNumber);
                    subRow.AddCellValue(siteInfoNew[i].NearbyInfo[j].Distance);
                }
            }
            return rows;
        }

        private class Line
        {
            public DbPoint P1;
            public DbPoint P2;
            public Color LineColor;
        }
    }
}
