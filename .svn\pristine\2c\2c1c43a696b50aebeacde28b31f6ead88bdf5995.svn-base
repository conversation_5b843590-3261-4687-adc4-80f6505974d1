﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.CQT.KPIReport
{
    public partial class FloorImgPanel : UserControl
    {
        public FloorImgPanel()
        {
            InitializeComponent();
        }

        internal void ShowImages(List<DTFileDataManager> files, Func.MapSerialInfo serial)
        {
            layoutPnl.Controls.Clear();
            for (int i = 0; i < files.Count; i++)
            {
                CQTCoverImg img = new CQTCoverImg();
                img.Draw(files[i], serial);
                img.Location = new System.Drawing.Point(3, 0);
                layoutPnl.Controls.Add(img);
                img.Img_DoubleClick += img_Img_DoubleClick;
            }
        }

        CoverImgFullForm fullImgForm;
        void img_Img_DoubleClick(object sender, EventArgs e)
        {
            CQTCoverImg img = sender as CQTCoverImg;
            if (img==null)
            {
                return;
            }
            if (fullImgForm==null||fullImgForm.IsDisposed)
            {
                fullImgForm = new CoverImgFullForm();
            }
            fullImgForm.SetFloorImg(img.Title, img.Image);
            fullImgForm.Owner = MainModel.GetInstance().MainForm;
            fullImgForm.Visible = true;
            fullImgForm.BringToFront();
        }

    }
}
