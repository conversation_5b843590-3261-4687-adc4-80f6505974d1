﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class TauHandoverConflictQuery : DIYAnalyseByFileBackgroundBase
    {
        List<TauHandoverInfo> resultList = null;

        protected static readonly object lockObj = new object();
        private static TauHandoverConflictQuery instance=null;
        public static TauHandoverConflictQuery GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new TauHandoverConflictQuery();
                    }
                }
            }
            return instance;
        }

        protected TauHandoverConflictQuery()
            : base(MainModel.GetInstance())
        {
            this.IncludeMessage = true;
        }

        public override string Name
        {
            get
            {
                return "VoLTE_TAU与切换冲突分析(按区域)";
            }
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 27000, 27013, this.Name);
        }

        protected override bool getCondition()
        {
            resultList = new List<TauHandoverInfo>();
            return true;
        }

        protected override void analyseFiles()
        {
            List<FileInfo> fileList = new List<FileInfo>();
            foreach (FileInfo file in MainModel.FileInfos)
            {
                if (ServiceTypes.Count > 0 && !ServiceTypes.Contains((ServiceType)file.ServiceType))
                {
                    continue;
                }
                fileList.Add(file);
            }
            try
            {
                clearDataBeforeAnalyseFiles();
                int iloop=0;
                foreach (FileInfo file in fileList)
                {
                    WaitBox.Text = "正在分析(" + (++iloop) + "/" + MainModel.FileInfos.Count + ")...";
                    WaitBox.ProgressPercent = (int)(iloop * 100.0 / MainModel.FileInfos.Count);

                    condition.FileInfos.Clear();
                    if (file != null)
                    {
                        condition.FileInfos.Add(file);
                    }

                    replay();
                    condition.FileInfos.Clear();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
                MainModel.ClearDTData();
                doSomethingAfterAnalyseFiles();
            }
            finally
            {
                System.Threading.Thread.Sleep(10);
                WaitBox.Close();
            }
        }

        TauHandoverInfo info = null;
        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager dt in MainModel.DTDataManager.FileDataManagers)
            {
                foreach (DTData data in dt.DTDatas)
                {
                    dealDTDatas(dt, data);
                }
            }
        }

        private void dealDTDatas(DTFileDataManager dt, DTData data)
        {
            if (data is TestPoint)
            {
                if (info != null)
                {
                    info.AddTestPoint(data as TestPoint);
                }
            }
            else if (data is Event)
            {
                if (info != null)
                {
                    info.AddEvent(data as Event);
                }
            }
            else if (data is Message)
            {
                setMsgInfo(dt, data);
            }
        }

        private void setMsgInfo(DTFileDataManager dt, DTData data)
        {
            Message msg = data as Message;
            if (msg.ID == 1097533256)
            {
                if (info == null)
                {
                    info = new TauHandoverInfo();
                    info.AddMessage(msg);
                    info.num = 1;
                }
                else
                {
                    if (info.num == 1)
                    {
                        info.AddMessage(msg);
                        info.num++;
                    }
                }
            }
            if (msg.ID == 1097533259 && info != null)
            {
                if (info.num > 1)
                {
                    info.AddMessage(msg);
                    info.BeginTime = msg.DateTime;
                    info.file = dt.GetFileInfo();
                    info.SN = resultList.Count + 1;
                    resultList.Add(info);
                    info = null;
                    return;
                }
                else if (info.num == 1)
                {
                    info = null;
                }
            }
            if (msg.ID == 1097533257)
            {
                info = null;
            }
        }

        protected override void fireShowForm()
        {
            if (resultList == null || resultList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的数据");
                return;
            }

            TauHandoverConflictResultListForm frm = MainModel.GetInstance().CreateResultForm(typeof(TauHandoverConflictResultListForm)) as TauHandoverConflictResultListForm;
            frm.FillData(resultList);
            frm.Visible = true;
            frm.BringToFront();
            resultList = null;
        }
    }

    public class TauHandoverConflictQuery_FDD : TauHandoverConflictQuery
    {
        private static TauHandoverConflictQuery_FDD instance = null;
        public static new TauHandoverConflictQuery_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new TauHandoverConflictQuery_FDD();
                    }
                }
            }
            return instance;
        }
        protected TauHandoverConflictQuery_FDD()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }
        public override string Name
        {
            get
            {
                return "VoLTE_FDD TAU与切换冲突分析(按区域)";
            }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 30000, 30036, this.Name);
        }
    }

    public class TauHandoverInfo
    {
        public int SN { get; set; }
        public FileInfo file { get; set; }
        public int num { get; set; }       //记录request信令出现的次数
        public DateTime BeginTime { get; set; }

        private readonly List<Event> evtList = new List<Event>();
        public void AddEvent(Event evt)
        {
            evtList.Add(evt);
        }
        public List<Event> Events
        {
            get
            {
                return evtList;
            }
        }

        private readonly List<TestPoint> tpList = new List<TestPoint>();
        public void AddTestPoint(TestPoint tp)
        {
            tpList.Add(tp);
        }
        public List<TestPoint> TestPoints
        {
            get
            {
                return tpList;
            }
        }

        private readonly List<Message> msgList = new List<Message>();
        public void AddMessage(Message msg)
        {
            msgList.Add(msg);
        }
        public List<Message> Messages
        {
            get
            {
                return msgList;
            }
        }
    }
}
