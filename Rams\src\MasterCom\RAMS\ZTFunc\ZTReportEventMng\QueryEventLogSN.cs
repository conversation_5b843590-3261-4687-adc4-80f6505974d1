﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc.ZTReportEventMng
{
    public class QueryEventLogSN : DIYSQLBase
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="queryType">1:GSM 2:TD</param>
        public QueryEventLogSN(byte queryType)
            : base(MainModel.GetInstance())
        {
            this.queryType = queryType;
        }
        private readonly byte queryType;

        protected override string getSqlTextString()
        {
            StringBuilder sb = new StringBuilder("select 事件编号 from ");
            if (queryType == 1)
            {
                sb.Append("tb_gsm_vvip_log");
            }
            else if (queryType == 2)
            {
                sb.Append("tb_td_vvip_log");
            }
            else
            {
                return string.Empty;
            }
            sb.Append(" order by 事件编号");
            return sb.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] arr=new E_VType[1];
            arr[0] = E_VType.E_Int;
            return arr;
        }

        public List<int> LogSNs
        {
            get;
            set;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            LogSNs = new List<int>();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    LogSNs.Add(package.Content.GetParamInt());
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

    }
}
