﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Net
{
    public class DownloadStationAcceptFile : QueryBase
    {
        private readonly List<string> filesPath;
        public DownloadStationAcceptFile(MainModel mainModel, List<string> filesPath)
            : base(mainModel)
        {
            this.filesPath = filesPath;
        }

        public override string Name
        {
            get { return "下载单站验收文件"; }
        }

        public override string IconName
        {
            get { return "Images/download.gif"; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected virtual void prepareSearchPackage(Package package, string filePath)
        {
            package.Command = Command.DataManage;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_FILEDOWNLOADByPath;
            package.Content.PrepareAddParam();
            package.Content.AddParam(filePath);
            filesDesc.Add(string.Format("下载单站验收文件路径:{0}", filePath));
        }

        protected List<string> filesDesc;
        bool isCancel = false;
        Dictionary<string, bool> fileNameDownLoaded = new Dictionary<string, bool>();
        protected override void query()
        {
            failMsg = "";
            isCancel = false;
            fileNameDownLoaded = new Dictionary<string, bool>();
            WaitBox.CanCancel = true;
            filesDesc = new List<string>();
            if (filesPath.Count == 1)
            {
                bool isSuccess = downloadFile();
                if (!isSuccess)
                {
                    return;
                }
            }
            else
            {
                bool isSuccess = downloadFolder();
                if (!isSuccess)
                {
                    return;
                }
            }
            if (failMsg != "")
            {
                MessageBox.Show(failMsg + "文件下载失败");
                return;
            }
            else if (isCancel)
            {
                MessageBox.Show("文件下载中断！");
            }
            MessageBox.Show("文件下载完成！");
        }

        private bool downloadFolder()
        {
            FolderBrowserDialog dlg = new FolderBrowserDialog();
            dlg.Description = "请选择保存目录";
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            string savePath = dlg.SelectedPath;

            ClientProxy clientProxy = null;
            for (int i = 0; i < filesPath.Count; i++)
            {
                string filePath = filesPath[i];
                string fileName = filePath.Substring(filePath.LastIndexOf('\\') + 1);
                if (!fileNameDownLoaded.ContainsKey(filePath))
                {
                    fileNameDownLoaded[filePath] = true;
                    string saveFileName = savePath + Path.DirectorySeparatorChar + fileName;

                    clientProxy = new ClientProxy();
                    clientProxy.setTimeout(30000, 30000);
                    if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port
                        , MainModel.User.LoginName, MainModel.User.Password
                        , MainModel.DistrictID) != ConnectResult.Success)
                    {
                        ErrorInfo = "连接服务器失败!";
                        return false;
                    }

                    WaitBox.Show(string.Format("[{0}/{1}]{2}", i + 1, filesPath.Count, fileName)
                    , queryInThread, new object[] { clientProxy, fileName, filePath, saveFileName });
                    if (isCancel)
                    {
                        break;
                    }
                    clientProxy.Close();
                }
            }
            return true;
        }

        private bool downloadFile()
        {
            string savePath;
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = true;
            string filePath = filesPath[0];
            string fileName = filePath.Substring(filePath.LastIndexOf('\\') + 1);
            dlg.FileName = fileName;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            savePath = dlg.FileName;
            ClientProxy clientProxy = new ClientProxy();
            clientProxy.setTimeout(30000, 30000);
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return false;
            }
            WaitBox.Show("正在下载数据文件...", queryInThread, new object[] { clientProxy, fileName, filePath, savePath });
            clientProxy.Close();
            return true;
        }

        string failMsg = "";
        private void queryInThread(object o)
        {
            try
            {
                object[] param = o as object[];
                ClientProxy clientProxy = (ClientProxy)param[0];
                string fileName = param[1] as string;
                string filePath = param[2] as string;
                string saveName = param[3] as string;

                Package package = clientProxy.Package;
                prepareSearchPackage(package, filePath);
                clientProxy.Send();
                failMsg = failMsg + receiveOneFile(clientProxy, saveName, fileName);
            }
            catch (Exception e)
            {
                ErrorInfo += e.Message;
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private string receiveOneFile(ClientProxy clientProxy, string saveName, string filePath)
        {
            string result = "";
            FileStream fileStream = null;
            Package package = clientProxy.Package;
            decimal gotLength = 0;
            try
            {
                receiveData(clientProxy, saveName, filePath, ref result, ref fileStream, package, ref gotLength);
            }
            catch (Exception ex)
            {
                result = "[" + filePath + "]";
                MessageBox.Show("文件下载异常！\r\n" + ex.Message);
            }
            finally
            {
                if (fileStream != null)
                {
                    fileStream.Close();
                }
            }
            return result;
        }

        private void receiveData(ClientProxy clientProxy, string saveName, string filePath, ref string result, ref FileStream fileStream, Package package, ref decimal gotLength)
        {
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    isCancel = true;
                    break;
                }

                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.FileBegin || package.Content.Type == ResponseType.FileContinue || package.Content.Type == ResponseType.FileEnd)
                {
                    package.Content.GetParamString();//realFileName
                    package.Content.GetParamInt();//pNum
                    int length = package.Content.GetParamInt();
                    if (fileStream == null)
                    {
                        fileStream = new FileStream(saveName, FileMode.Create);
                    }
                    fileStream.Write(package.Content.Buff, package.Content.CurOffset
                        , package.Content.Buff.Length - package.Content.CurOffset);
                    gotLength += length;
                    if (package.Content.Type == ResponseType.FileEnd)
                    {
                        break;
                    }
                }
                else if (package.Content.Type == ResponseType.FileNotFound)
                {
                    result = "[" + filePath + "]";
                    break;
                }
            }
        }
    }
}
