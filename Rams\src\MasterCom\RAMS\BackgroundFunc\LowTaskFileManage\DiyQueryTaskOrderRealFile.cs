﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class DiyQueryTaskOrderRealFile : DIYSQLBase
    {
        public DiyQueryTaskOrderRealFile(MainModel mainModel)
            : base(mainModel)
        {
            MainDB = false;
        }

        #region 基础数据重写
        public override string Name
        {
            get { return "查询工单文件对应的解析文件"; }
        }

        public override string IconName
        {
            get { return string.Empty; }
        }
        #endregion

        private string files;
        private string splitStr;
        private List<TaskOrderRealFile> resultList;

        public void SetQueryCondition(string files, string splitStr)
        {
            this.files = files;
            this.splitStr = splitStr;
            resultList = new List<TaskOrderRealFile>();
        }

        #region 查询流程
        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                queryInThread(clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
        }
        
        private string curFiles;
        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                int bufferLength = Encoding.Default.GetBytes(files).Length;
                if (bufferLength > 7000)
                {
                    string[] strArr = files.Split(new string[] { splitStr }, StringSplitOptions.RemoveEmptyEntries);
                    int curIdx = 0;
                    while (curIdx < strArr.Length)//分批次发送包
                    {
                        StringBuilder strb = getCurFiles(strArr, ref curIdx);
                        curFiles = strb.ToString().Substring(splitStr.Length);
                        getDataBySql(clientProxy, package);
                    }
                }
                else
                {
                    curFiles = files;
                    getDataBySql(clientProxy, package);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.ToString());
            }
        }

        private StringBuilder getCurFiles(string[] strArr, ref int curIdx)
        {
            StringBuilder strb = new StringBuilder();
            for (; curIdx < strArr.Length; curIdx++)
            {
                string curStr = strArr[curIdx];
                int bufferLength = Encoding.Default.GetBytes(strb + curStr).Length;
                if (bufferLength > 7000)
                {
                    break;
                }
                strb.Append(splitStr + curStr);
            }

            return strb;
        }

        private void getDataBySql(ClientProxy clientProxy, Package package)
        {
            string strsql = getSqlTextString();
            E_VType[] retArrDef = getSqlRetTypeArr();//获得枚举类型数组
            package.Command = Command.DIYSearch;//枚举类型：DIY接口
            package.SubCommand = SubCommand.Request;//枚举类型：请求
            if (MainDB)
            {
                package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
            }
            else
            {
                package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
            }
            package.Content.PrepareAddParam();
            package.Content.AddParam(strsql);
            StringBuilder sb = new StringBuilder();
            if (retArrDef != null)
            {
                for (int i = 0; i < retArrDef.Length; i++)
                {
                    sb.Append((int)retArrDef[i]);
                    sb.Append(",");
                }
            }
            package.Content.AddParam(sb.ToString().TrimEnd(','));
            clientProxy.Send();
            System.Threading.Thread.Sleep(300);
            receiveRetData(clientProxy);
        }

        protected override string getSqlTextString()
        {
            string strSQL = string.Format(@"EXEC PROC_路测文件_查询_解析信息 '{0}','{1}'", curFiles, splitStr);
            return strSQL;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[4];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_String;
            rType[3] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    TaskOrderRealFile curOrderFile = new TaskOrderRealFile();
                    curOrderFile.Fill(package);
                    resultList.Add(curOrderFile);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }
        #endregion

        /// <summary>
        /// 去除无解析信息的文件
        /// </summary>
        /// <returns></returns>
        public List<TaskOrderRealFile> GetValidFiles()
        {
            List<TaskOrderRealFile> realFileList = new List<TaskOrderRealFile>();
            foreach (TaskOrderRealFile result in resultList)
            {
                if (!string.IsNullOrEmpty(result.TableName) && !string.IsNullOrEmpty(result.AnalyzeFileName))
                {
                    realFileList.Add(result);
                }
            }
            return realFileList;
        }
    }

    public class TaskOrderRealFile
    {
        public string FileName { get; private set; }
        public string AnalyzeFileName { get; private set; }
        public string TableName { get; private set; }
        public string Remark { get; set; }

        public void Fill(Package package)
        {
            FileName = package.Content.GetParamString();
            AnalyzeFileName = package.Content.GetParamString();
            TableName = package.Content.GetParamString();
            Remark = package.Content.GetParamString();
        }
    }

}
