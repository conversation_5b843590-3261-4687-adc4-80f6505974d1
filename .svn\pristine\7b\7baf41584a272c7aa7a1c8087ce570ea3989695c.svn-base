using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Text;
using System.Runtime.Serialization;

namespace MasterCom.RAMS.Func.MeshGridOf3D
{
    /// <summary>
    /// A class representing all the characteristics of the Line
    /// segments that make up a curve on the graph.
    /// </summary>
    /// 
    [Serializable]
    public class LineStyle : ICloneable, ISerializable
    {
        #region Constructors

        public LineStyle()
        {

        }
        /// <summary>
        /// Copy constructor
        /// </summary>
        /// 
        public LineStyle(LineStyle rhs)
        {
            Pattern = rhs.Pattern;
            LineColor = rhs.LineColor;
            Thickness = rhs.Thickness;
            PlotMethod = rhs.PlotMethod;
            IsVisible = rhs.IsVisible;
        }
        /// <summary>
        /// Implement the <see cref="ICloneable" /> interface in a typesafe manner by just
        /// calling the typed version of <see cref="Clone" />
        /// </summary>
        /// <returns>A deep copy of this object</returns>
        object ICloneable.Clone()
        {
            return new LineStyle(this);
        }
        #endregion

        #region Properties

        public bool IsVisible { get; set; } = true;

        public PlotLinesMethod PlotMethod { get; set; } = PlotLinesMethod.Lines;

        virtual public DashStyle Pattern { get; set; } = DashStyle.Solid;

        public float Thickness { get; set; } = 1.0f;

        virtual public Color LineColor { get; set; } = Color.Black;
        #endregion

        #region Serialization

        /// <summary>
        /// Constructor for deserializing objects
        /// </summary>
        /// <param name="info">A <see cref="SerializationInfo"/> instance that defines the serialized data
        /// </param>
        /// <param name="context">A <see cref="StreamingContext"/> instance that contains the serialized data
        /// </param>
        /// 
        protected LineStyle(SerializationInfo info, StreamingContext context)
        {
            Pattern = (DashStyle)info.GetValue("linePattern", typeof(DashStyle));
            LineColor = (Color)info.GetValue("lineColor", typeof(Color));
            Thickness = info.GetSingle("LineThickness");
            IsVisible = info.GetBoolean("isVisible");
        }
        /// <summary>
        /// Populates a <see cref="SerializationInfo"/> instance with the data needed to serialize the target object
        /// </summary>
        /// <param name="info">A <see cref="SerializationInfo"/> instance that defines the serialized data</param>
        /// <param name="context">A <see cref="StreamingContext"/> instance that contains the serialized data</param>
        public virtual void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            info.AddValue("linePattern", Pattern);
            info.AddValue("lineColor", LineColor);
            info.AddValue("LineThickness", Thickness);
            info.AddValue("isVisible", IsVisible);
        }
        #endregion

        #region Enum

        public enum PlotLinesMethod
        {
            Lines = 0,
            Splines = 1
        }
        #endregion
    }
}


