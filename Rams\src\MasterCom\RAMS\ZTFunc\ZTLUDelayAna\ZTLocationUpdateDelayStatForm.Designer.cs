﻿namespace MasterCom.RAMS.Func
{
    partial class ZTLocationUpdateDelayStatForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTLocationUpdateDelayStatForm));
            this.dataGridView = new System.Windows.Forms.DataGridView();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.tsmOutputExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.tsmRangeSetting = new System.Windows.Forms.ToolStripMenuItem();
            this.ColumnRange = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnTimes = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnRatio = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnAVG = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnMax = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnMin = new System.Windows.Forms.DataGridViewTextBoxColumn();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // dataGridView
            // 
            this.dataGridView.AllowUserToAddRows = false;
            this.dataGridView.AllowUserToDeleteRows = false;
            this.dataGridView.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dataGridView.BackgroundColor = System.Drawing.SystemColors.Window;
            this.dataGridView.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.ColumnRange,
            this.ColumnTimes,
            this.ColumnRatio,
            this.ColumnAVG,
            this.ColumnMax,
            this.ColumnMin});
            this.dataGridView.ContextMenuStrip = this.contextMenuStrip1;
            this.dataGridView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridView.Location = new System.Drawing.Point(0, 0);
            this.dataGridView.MultiSelect = false;
            this.dataGridView.Name = "dataGridView";
            this.dataGridView.ReadOnly = true;
            this.dataGridView.RowHeadersVisible = false;
            this.dataGridView.RowTemplate.Height = 23;
            this.dataGridView.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridView.Size = new System.Drawing.Size(831, 310);
            this.dataGridView.TabIndex = 6;
            this.dataGridView.SelectionChanged += new System.EventHandler(this.dataGridView_SelectionChanged);
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmOutputExcel,
            this.toolStripMenuItem1,
            this.tsmRangeSetting});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(158, 54);
            // 
            // tsmOutputExcel
            // 
            this.tsmOutputExcel.Name = "tsmOutputExcel";
            this.tsmOutputExcel.Size = new System.Drawing.Size(157, 22);
            this.tsmOutputExcel.Text = "导出到Excel";
            this.tsmOutputExcel.Click += new System.EventHandler(this.tsmOutputExcel_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(154, 6);
            // 
            // tsmRangeSetting
            // 
            this.tsmRangeSetting.Name = "tsmRangeSetting";
            this.tsmRangeSetting.Size = new System.Drawing.Size(157, 22);
            this.tsmRangeSetting.Text = "设置统计区间...";
            this.tsmRangeSetting.Click += new System.EventHandler(this.tsmRangeSetting_Click);
            // 
            // ColumnRange
            // 
            this.ColumnRange.HeaderText = "统计区间(毫秒)";
            this.ColumnRange.Name = "ColumnRange";
            this.ColumnRange.ReadOnly = true;
            this.ColumnRange.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.NotSortable;
            // 
            // ColumnTimes
            // 
            this.ColumnTimes.HeaderText = "次数";
            this.ColumnTimes.Name = "ColumnTimes";
            this.ColumnTimes.ReadOnly = true;
            // 
            // ColumnRatio
            // 
            this.ColumnRatio.HeaderText = "占比(%)";
            this.ColumnRatio.Name = "ColumnRatio";
            this.ColumnRatio.ReadOnly = true;
            // 
            // ColumnAVG
            // 
            this.ColumnAVG.HeaderText = "平均时延(毫秒)";
            this.ColumnAVG.Name = "ColumnAVG";
            this.ColumnAVG.ReadOnly = true;
            // 
            // ColumnMax
            // 
            this.ColumnMax.HeaderText = "最低时延(毫秒)";
            this.ColumnMax.Name = "ColumnMax";
            this.ColumnMax.ReadOnly = true;
            // 
            // ColumnMin
            // 
            this.ColumnMin.HeaderText = "最高时延(毫秒)";
            this.ColumnMin.Name = "ColumnMin";
            this.ColumnMin.ReadOnly = true;
            // 
            // ZTLocationUpdateDelayStatForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(831, 310);
            this.Controls.Add(this.dataGridView);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTLocationUpdateDelayStatForm";
            this.Text = "位置更新时延分析";
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.DataGridView dataGridView;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem tsmOutputExcel;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem tsmRangeSetting;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnRange;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnTimes;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnRatio;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnAVG;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnMax;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnMin;
    }
}