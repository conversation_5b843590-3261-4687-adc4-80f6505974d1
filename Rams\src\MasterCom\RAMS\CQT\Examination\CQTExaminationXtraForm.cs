﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTExaminationXtraForm : DevExpress.XtraEditors.XtraForm
    {
        public CQTExaminationXtraForm(MainModel main)
        {
            InitializeComponent();
            mainmodel = main;
        }
        MainModel mainmodel = null;
        DateTime stime = new DateTime();
        DateTime etime = new DateTime();
        List<CQTLibrary.PublicItem.ParaColumnItem> paraitem = new List<CQTLibrary.PublicItem.ParaColumnItem>();
        Dictionary<string, CQTLibrary.PublicItem.AreaDetail> initDic2 = new Dictionary<string, CQTLibrary.PublicItem.AreaDetail>();

        public void setData(List<CQTLibrary.PublicItem.ExamItem> result, DateTime sstime, DateTime eetime, List<CQTLibrary.PublicItem.ParaColumnItem> pparalist,Dictionary<string, CQTLibrary.PublicItem.AreaDetail> iinitDic2)
        {
            this.gridControl1.DataSource = result;
            stime = sstime;
            etime = eetime;
            paraitem = pparalist;
            initDic2 = iinitDic2;
        }

        private void gridView1_RowCellStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs e)
        {
            //if (e.Column.Caption == "网络覆盖" || e.Column.Caption == "网络质量" || e.Column.Caption == "网络容量" || e.Column.Caption == "网络感知" || e.Column.Caption == "网络满意度")
            //{
            //    string aa = gridView1.GetRowCellDisplayText(e.RowHandle, e.Column);
            //    if (aa == "健康")
            //    {
            //        e.Appearance.BackColor = Color.Green;
            //        //e.Appearance.BackColor2 = Color.LightCyan;
            //    }

            //    if (aa == "合格")
            //    {
            //        e.Appearance.BackColor = Color.Orange;
            //        //e.Appearance.BackColor2 = Color.LightCyan;
            //    }

            //    if (aa == "不合格")
            //    {
            //        e.Appearance.BackColor = Color.Red;
            //        //e.Appearance.BackColor2 = Color.LightCyan;
            //    }

            //    if (aa == "满意")
            //    {
            //        e.Appearance.BackColor = Color.Green;
            //        //e.Appearance.BackColor2 = Color.LightCyan;
            //    }
            //}
        }

        //双击查看详情
        private void gridView1_DoubleClick(object sender, EventArgs e)
        {

            string cqtname = gridView1.GetFocusedRowCellValue("Strcqtname").ToString();

            int[] rows = gridView1.GetSelectedRows();
            if (rows.Length == 0)
                return;
            object o = gridView1.GetRow(rows[0]);
            CQTLibrary.PublicItem.ExamItem eitem = o as CQTLibrary.PublicItem.ExamItem;


            if (cqtname.Trim() != "")
            {
                CQTReportXtraForm form = new CQTReportXtraForm(mainmodel);
                form.Show();
                form.setData(eitem, stime, etime, paraitem, initDic2[cqtname]);
            }
        }

        private void 导出ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(this.gridView1);
        }
    }
}