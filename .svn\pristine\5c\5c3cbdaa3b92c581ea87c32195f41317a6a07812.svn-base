﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.ES.Data;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTPilotFreqPolluteByRegion_LTE : DIYAnalyseByPeriodBackgroundBase_Sample
   {
        public int cellCountThreshold { get; set; } = 4;
        public int rxLevDValueThreshold { get; set; } = 6;
        public int filterPCCPCH_RSCPMin { get; set; } = -85;
        public int filterPCCPCH_RSCPMax { get; set; } = -10;
        public static int PilotFrequencyPolluteBlockRadius { get; set; } = 50;
        public int sampleCountLimit { get; set; } = 10;
        protected Dictionary<string, int> curPilotFrequencyPolluteGridSampleCountDic = null;

        public string themeName { get; set; }

        private static ZTPilotFreqPolluteByRegion_LTE intance = null;
        protected static readonly object lockObj = new object();
        public static ZTPilotFreqPolluteByRegion_LTE GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTPilotFreqPolluteByRegion_LTE();
                    }
                }
            }
            return intance;
        }

        protected ZTPilotFreqPolluteByRegion_LTE()
            : base(MainModel.GetInstance())
        {
            themeName = "TD_LTE_RSRP";
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_DATA);
            carrierID = CarrierType.ChinaMobile;
            this.isAddSampleToDTDataManager = false;
        }

        public ZTPilotFreqPolluteByRegion_LTE(ServiceName serviceName)
            : this()
        {
            ServiceTypes.Clear();
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(serviceName);
        }

        public override string Name
        {
            get { return "导频污染分析_LTE"; }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22011, this.Name);
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            try
            {
                bool inRegion = Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
                if (!inRegion)
                {
                    return false;
                }
                return validTestPoint(tp);
            }
            catch
            {
                return false;
            }
        }

        protected virtual bool validTestPoint(TestPoint tp)
        {
            if (!(tp is LTETestPointDetail))
            {
                return false;
            }
            Dictionary<LTECell, float> cellRscpDic = new Dictionary<LTECell, float>();
            Dictionary<LTECell, float?> cellSinrDic = new Dictionary<LTECell, float?>();
            setCellInfo(tp, cellRscpDic, cellSinrDic);

            setNCellInfo(tp, cellRscpDic, cellSinrDic);

            float maxRscp = float.MinValue;
            LTECell maxCell = null;
            List<LTECell> cells = new List<LTECell>();
            bool isvalid = getMaxCell(cellRscpDic, ref maxRscp, ref maxCell, cells);
            if (!isvalid)
            {
                return false;
            }

            if (cells.Count < cellCountThreshold)
            {
                saveGoodSample(tp);
                return false;
            }
            else
            {
                List<LTECellOfPilotFrequencyPolluteBlock> cellList = new List<LTECellOfPilotFrequencyPolluteBlock>();
                foreach (LTECell c in cells)
                {
                    float value = cellRscpDic[c];
                    cellList.Add(new LTECellOfPilotFrequencyPolluteBlock(cellList.Count + 1, c, value, cellSinrDic[c]));
                }
                gatherBlock(cellList, tp, maxCell.EARFCN);
            }
            return true;
        }

        protected bool getMaxCell(Dictionary<LTECell, float> cellRscpDic, ref float maxRscp, ref LTECell maxCell, List<LTECell> cells)
        {
            //排序后统计与最强信号6dB内的小区数量
            if (cellRscpDic.Count >= cellCountThreshold)
            {
                foreach (LTECell c in cellRscpDic.Keys)
                {
                    if (cellRscpDic[c] > maxRscp)
                    {
                        maxRscp = cellRscpDic[c];
                        maxCell = c;
                    }
                }
                if (maxCell == null)
                {
                    return false;
                }
                foreach (LTECell c in cellRscpDic.Keys)
                {
                    if ((!coFreqOnly || c.EARFCN == maxCell.EARFCN) && (maxRscp - cellRscpDic[c]) <= rxLevDValueThreshold)
                    {
                        cells.Add(c);
                    }
                }
            }
            return true;
        }

        private void setNCellInfo(TestPoint tp, Dictionary<LTECell, float> cellRscpDic, Dictionary<LTECell, float?> cellSinrDic)
        {
            //邻区场强
            for (int i = 0; i < 6; i++)
            {
                LTECell cell = tp.GetNBCell_LTE(i);//
                if (cell == null)
                {
                    continue;
                }
                float? nRscp = (float?)tp["lte_NCell_RSRP", i];
                if (nRscp != null && nRscp >= filterPCCPCH_RSCPMin && nRscp <= filterPCCPCH_RSCPMax)
                {
                    cellRscpDic[cell] = (float)nRscp;
                    cellSinrDic[cell] = (float?)tp["lte_NCell_SINR", i];
                }
            }
        }

        private void setCellInfo(TestPoint tp, Dictionary<LTECell, float> cellRscpDic, Dictionary<LTECell, float?> cellSinrDic)
        {
            //主服场强
            float? rsrp = (float?)tp["lte_RSRP"];
            LTECell cell = tp.GetMainCell_LTE();//
            if (cell != null && rsrp != null && rsrp >= filterPCCPCH_RSCPMin && rsrp <= filterPCCPCH_RSCPMax)
            {
                cellRscpDic[cell] = (float)rsrp;
                cellSinrDic[cell] = (float?)tp["lte_SINR"];
            }
        }

        protected void saveGoodSample(TestPoint testPoint)
        {
            string key = testPoint.Longitude + "|" + testPoint.Latitude;
            if (curPilotFrequencyPolluteGridSampleCountDic.ContainsKey(key))
            {
                curPilotFrequencyPolluteGridSampleCountDic[key]++;
            }
            else
            {
                curPilotFrequencyPolluteGridSampleCountDic[key] = 1;
            }
        }

        protected bool coFreqOnly = false;
        protected override bool getConditionBeforeQuery()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            SetHavePilotFrequencyPolluteFilterDlg fDlg = new SetHavePilotFrequencyPolluteFilterDlg();
            if (fDlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            filterPCCPCH_RSCPMin = fDlg.RxlevMin;
            filterPCCPCH_RSCPMax = fDlg.RxlevMax;
            PilotFrequencyPolluteBlockRadius = fDlg.Radius;
            sampleCountLimit = fDlg.SampleCountLimit;
            cellCountThreshold = fDlg.CellCountThreshold;
            rxLevDValueThreshold = fDlg.RxLevDValueThreshold;
            coFreqOnly = fDlg.CoFreqOnly;
            return true;
        }

        protected override void doSomethingBeforeQueryInThread()
        {
            curPilotFrequencyPolluteGridSampleCountDic = new Dictionary<string, int>();
            pilotFrequencyPolluteBlockList = new List<PilotFrequencyPolluteBlock>();
            MainModel.CurPilotFrequencyPolluteBlockList.Clear();
        }

        protected override void FireShowFormAfterQuery()
        {
            PilotFrequencyPolluteSampleForm frm =
                MainModel.GetObjectFromBlackboard(typeof(PilotFrequencyPolluteSampleForm))
                as PilotFrequencyPolluteSampleForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new PilotFrequencyPolluteSampleForm();
            }
            frm.FillData();
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            curPilotFrequencyPolluteGridSampleCountDic = null;
            pilotFrequencyPolluteBlockList = null;
            MainModel.FireSetDefaultMapSerialTheme(themeName);
        }

        protected override void getResultAfterQuery()
        {
            getPilotFreqPolluteResult(pilotFrequencyPolluteBlockList);
            getGoodSample();
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "lte_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_TAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_ECI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_SINR";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            for (int i = 0; i < 6; i++)
            {
                param = new Dictionary<string, object>();
                param["param_name"] = "lte_NCell_EARFCN";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "lte_NCell_PCI";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "lte_NCell_RSRP";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "lte_NCell_SINR";
                param["param_arg"] = i;
                columnsDef.Add((object)param);
            }

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        List<PilotFrequencyPolluteBlock> pilotFrequencyPolluteBlockList = new List<PilotFrequencyPolluteBlock>();
      
        /// <summary>
        /// 采样点加到导频污染黑点
        /// </summary>
        /// <param name="pilotFrequencyPolluteBlockList"></param>
        /// <param name="nextId"></param>
        /// <param name="testPoint"></param>
        /// <param name="cellList"></param>
        protected void gatherBlock(List<LTECellOfPilotFrequencyPolluteBlock> cellList, TestPoint testPoint,int freq)
        {
            List<PilotFrequencyPolluteBlock> tmpList = getTmpList(testPoint, freq);

            if (tmpList.Count == 0)
            {
                PilotFrequencyPolluteBlock item = new PilotFrequencyPolluteBlock(0);
                item.AddTestPoint(testPoint, cellList);
                pilotFrequencyPolluteBlockList.Add(item);
            }
            else
            {
                PilotFrequencyPolluteBlock blockTmp = null;
                for (int i = 0; i < tmpList.Count; i++)
                {
                    PilotFrequencyPolluteBlock block = tmpList[i];
                    if (blockTmp == null)
                    {
                        blockTmp = block;
                        block.AddTestPoint(testPoint, cellList);
                    }
                    else
                    {
                        blockTmp.Join(block);
                        pilotFrequencyPolluteBlockList.Remove(block);
                    }
                }
            }
        }

        private List<PilotFrequencyPolluteBlock> getTmpList(TestPoint testPoint, int freq)
        {
            List<PilotFrequencyPolluteBlock> tmpList = new List<PilotFrequencyPolluteBlock>();
            foreach (PilotFrequencyPolluteBlock block in pilotFrequencyPolluteBlockList)
            {
                if (coFreqOnly)
                {
                    bool canJoin = judgeCanJoin(freq, block);
                    if (!canJoin)
                    {
                        continue;
                    }
                }
                if (block.Intersect(testPoint.Longitude, testPoint.Latitude, PilotFrequencyPolluteBlockRadius))
                {
                    tmpList.Add(block);
                }
            }

            return tmpList;
        }

        private bool judgeCanJoin(int freq, PilotFrequencyPolluteBlock block)
        {
            bool canJoin = true;
            foreach (string cellName in block.CellDic.Keys)
            {
                LTECellOfPilotFrequencyPolluteBlock lteBlk = block.CellDic[cellName] as LTECellOfPilotFrequencyPolluteBlock;
                if (lteBlk.LTECell.EARFCN != freq)
                {
                    canJoin = false;
                    break;
                }
            }

            return canJoin;
        }

        protected void getPilotFreqPolluteResult(List<PilotFrequencyPolluteBlock> pilotFrequencyPolluteBlockList)
        {
            MainModel.CurPilotFrequencyPolluteBlockList.Clear();
            int nextID = 1;
            foreach (PilotFrequencyPolluteBlock block in pilotFrequencyPolluteBlockList)
            {
                block.GetResult();
                if (block.TestPointCount >= sampleCountLimit)
                {
                    block.ID = nextID++;
                    int nextSN = 1;                   
                    foreach (CellOfPilotFrequencyPolluteBlock cell in block.CellDic.Values)
                    {
                        LTECellOfPilotFrequencyPolluteBlock lteCell = cell as LTECellOfPilotFrequencyPolluteBlock;
                        lteCell.SN = nextSN++;
                    }
                    foreach (TestPoint testPoint in block.TestPoints)
                    {
                        MainModel.DTDataManager.Add(testPoint);
                    }
                    MainModel.CurPilotFrequencyPolluteBlockList.Add(block);
                }
            }
        }

        protected virtual void getGoodSample()
        {
            int iloop = 0;
            WaitBox.Text = "开始获取正常采样点...";
            foreach (KeyValuePair<string, int> keyValue in curPilotFrequencyPolluteGridSampleCountDic)
            {
                string[] s = keyValue.Key.Split('|');
                double longitude = double.Parse(s[0]);
                double latitude = double.Parse(s[1]);
                foreach (PilotFrequencyPolluteBlock block in MainModel.CurPilotFrequencyPolluteBlockList)
                {
                    if (block.Intersect(longitude, latitude, PilotFrequencyPolluteBlockRadius))
                    {
                        block.GoodTestPointCount += keyValue.Value;
                    }
                }

                WaitBox.ProgressPercent = (int)(100.0 * ++iloop / curPilotFrequencyPolluteGridSampleCountDic.Count);
            }
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.覆盖; }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["FilterPCCPCH_RSCPMin"] = filterPCCPCH_RSCPMin;
                param["FilterPCCPCH_RSCPMax"] = filterPCCPCH_RSCPMax;
                param["PilotFrequencyPolluteBlockRadius"] = PilotFrequencyPolluteBlockRadius;
                param["SampleCountLimit"] = sampleCountLimit;
                param["CellCountThreshold"] = cellCountThreshold;
                param["RxLevDValueThreshold"] = rxLevDValueThreshold;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("FilterPCCPCH_RSCPMin"))
                {
                    filterPCCPCH_RSCPMin = int.Parse(param["FilterPCCPCH_RSCPMin"].ToString());
                }
                if (param.ContainsKey("FilterPCCPCH_RSCPMax"))
                {
                    filterPCCPCH_RSCPMax = int.Parse(param["FilterPCCPCH_RSCPMax"].ToString());
                }
                if (param.ContainsKey("PilotFrequencyPolluteBlockRadius"))
                {
                    PilotFrequencyPolluteBlockRadius = int.Parse(param["PilotFrequencyPolluteBlockRadius"].ToString());
                }
                if (param.ContainsKey("SampleCountLimit"))
                {
                    sampleCountLimit = int.Parse(param["SampleCountLimit"].ToString());
                }
                if (param.ContainsKey("CellCountThreshold"))
                {
                    cellCountThreshold = int.Parse(param["CellCountThreshold"].ToString());
                }
                if (param.ContainsKey("RxLevDValueThreshold"))
                {
                    rxLevDValueThreshold = int.Parse(param["RxLevDValueThreshold"].ToString());
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new PilotFrequencyPolluteBlockProperties_LTE(this);
            }
        }

        protected override void saveBackgroundData()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            foreach (PilotFrequencyPolluteBlock block in MainModel.CurPilotFrequencyPolluteBlockList)
            {
                //BackgroundResult result = block.ConvertToBackgroundResult();
                List<BackgroundResult> resultList = block.ConvertToBackgroundResultList(block);
                foreach (BackgroundResult result in resultList)
                {
                    result.SubFuncID = GetSubFuncID();
                    bgResultList.Add(result);
                }
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Region(GetSubFuncID(), Condition.Periods[0].IBeginTime,
                Condition.Periods[0].IEndTime, bgResultList);
        }

        protected override void initBackgroundImageDesc()
        {
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                int badTestPointCount = bgResult.GetImageValueInt();
                int goodTestPointCount = bgResult.GetImageValueInt();
                string cellName = bgResult.GetImageValueString();
                string cellCode = bgResult.GetImageValueString();
                string tac = bgResult.GetImageValueString();
                string eci = bgResult.GetImageValueString();
                string earfun = bgResult.GetImageValueString();
                string pci = bgResult.GetImageValueString();
                string altitude = bgResult.GetImageValueString();
                string downward = bgResult.GetImageValueString();
                string direction = bgResult.GetImageValueString();
                string longitude = bgResult.GetImageValueString();
                string latitude = bgResult.GetImageValueString();
                string PCCPCH_RSCPAvg = bgResult.GetImageValueString();
                string SampleCount = bgResult.GetImageValueString();
                string PCCPCH_C2IString = bgResult.GetImageValueString();
                string DPCH_C2IString = bgResult.GetImageValueString();
                string strProject = bgResult.GetImageValueString();

                StringBuilder sb = new StringBuilder();
                sb.Append("总采样点数：");
                sb.Append(badTestPointCount + goodTestPointCount);
                sb.Append("\r\n");
                sb.Append("异常采样点数：");
                sb.Append(badTestPointCount);
                sb.Append("\r\n");
                sb.Append("异常采样点比例：");
                sb.Append(Math.Round(100.0 * badTestPointCount / (badTestPointCount + goodTestPointCount), 2));
                sb.Append("%");
                sb.Append("\r\n");
                sb.Append("小区名：");
                sb.Append(cellName);
                sb.Append("\r\n");
                sb.Append("小区Code：");
                sb.Append(cellCode);
                sb.Append("\r\n");
                sb.Append("小区TAC：");
                sb.Append(tac);
                sb.Append("\r\n");
                sb.Append("小区ECI：");
                sb.Append(eci);
                sb.Append("\r\n");
                sb.Append("小区EARFCN：");
                sb.Append(earfun);
                sb.Append("\r\n");
                sb.Append("小区PCI：");
                sb.Append(pci);
                sb.Append("\r\n");
                sb.Append("小区Altitude：");
                sb.Append(altitude);
                sb.Append("\r\n");
                sb.Append("小区Downward：");
                sb.Append(downward);
                sb.Append("\r\n");
                sb.Append("小区Direction：");
                sb.Append(direction);
                sb.Append("\r\n");
                sb.Append("小区Longitude：");
                sb.Append(longitude);
                sb.Append("\r\n");
                sb.Append("小区Latitude：");
                sb.Append(latitude);
                sb.Append("\r\n");
               
                sb.Append("\r\n");
                sb.Append("小区PCCPCH_RSCPAvg：");
                sb.Append(PCCPCH_RSCPAvg);
                sb.Append("\r\n");
                sb.Append("小区SampleCount：");
                sb.Append(SampleCount);
                sb.Append("\r\n");
                sb.Append("小区PCCPCH_C2IString：");
                sb.Append(PCCPCH_C2IString);
                sb.Append("\r\n");
                sb.Append("小区DPCH_C2IString：");
                sb.Append(DPCH_C2IString);
                sb.Append("\r\n");
                sb.Append("项目ID：");
                sb.Append(strProject);

                bgResult.ImageDesc = sb.ToString();
            }
        }
        #endregion
    }

    //导频污染***************************************************************************
    public class ZTPilotFreqPolluteByRegion_LteFdd : ZTPilotFreqPolluteByRegion_LTE
    {
        
        private static ZTPilotFreqPolluteByRegion_LteFdd intance = null;
        public new static ZTPilotFreqPolluteByRegion_LteFdd GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTPilotFreqPolluteByRegion_LteFdd();
                    }
                }
            }
            return intance;
        }

        protected ZTPilotFreqPolluteByRegion_LteFdd()
            : base()
        {
            themeName = "LTE_FDD:RSRP";
            ServiceTypes.Clear();
            carrierID = CarrierType.ChinaUnicom;
            this.isAddSampleToDTDataManager = false;
        }

        public override string Name
        {
            get { return "导频污染分析_LTE_FDD"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26005, this.Name);
        }

        protected virtual bool IsInType(TestPoint tp)
        {
            return true;
        }

        protected override bool validTestPoint(TestPoint tp)
        {
            if (!(tp is LTEFddTestPoint) || !IsInType(tp))
            {
                return false;
            }
            Dictionary<LTECell, float> cellRscpDic = new Dictionary<LTECell, float>();
            Dictionary<LTECell, float?> cellSinrDic = new Dictionary<LTECell, float?>();
            setCellInfo(tp, cellRscpDic, cellSinrDic);

            setNCellInfo(tp, cellRscpDic, cellSinrDic);

            float maxRscp = float.MinValue;
            LTECell maxCell = null;
            List<LTECell> cells = new List<LTECell>();
            bool isvalid = getMaxCell(cellRscpDic, ref maxRscp, ref maxCell, cells);
            if (!isvalid)
            {
                return false;
            }

            if (cells.Count < cellCountThreshold)
            {
                saveGoodSample(tp);
                return false;
            }
            else
            {
                List<LTECellOfPilotFrequencyPolluteBlock> cellList = new List<LTECellOfPilotFrequencyPolluteBlock>();
                foreach (LTECell c in cells)
                {
                    float value = cellRscpDic[c];
                    cellList.Add(new LTECellOfPilotFrequencyPolluteBlock(cellList.Count + 1, c, value, cellSinrDic[c]));
                }
                gatherBlock(cellList, tp, maxCell.EARFCN);
            }
            return true;
        }

        private void setNCellInfo(TestPoint tp, Dictionary<LTECell, float> cellRscpDic, Dictionary<LTECell, float?> cellSinrDic)
        {
            //邻区场强
            for (int i = 0; i < 6; i++)
            {
                LTECell cell = tp.GetNBCell_LTE_FDD(i);//
                if (cell == null)
                {
                    continue;
                }
                float? nRscp = (float?)tp["lte_fdd_NCell_RSRP", i];
                if (nRscp != null && nRscp >= filterPCCPCH_RSCPMin && nRscp <= filterPCCPCH_RSCPMax)
                {
                    cellRscpDic[cell] = (float)nRscp;
                    cellSinrDic[cell] = (float?)tp["lte_fdd_NCell_SINR", i];
                }
            }
        }

        private void setCellInfo(TestPoint tp, Dictionary<LTECell, float> cellRscpDic, Dictionary<LTECell, float?> cellSinrDic)
        {
            //主服场强
            float? rsrp = (float?)tp["lte_fdd_RSRP"];
            LTECell cell = tp.GetMainCell_LTE_FDD();//
            if (cell != null && rsrp != null && rsrp >= filterPCCPCH_RSCPMin && rsrp <= filterPCCPCH_RSCPMax)
            {
                cellRscpDic[cell] = (float)rsrp;
                cellSinrDic[cell] = (float?)tp["lte_fdd_SINR"];
            }
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_RSRP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_TAC";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_ECI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_EARFCN";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_PCI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "lte_fdd_SINR";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            for (int i = 0; i < 6; i++)
            {
                param = new Dictionary<string, object>();
                param["param_name"] = "lte_fdd_NCell_EARFCN";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "lte_fdd_NCell_PCI";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "lte_fdd_NCell_RSRP";
                param["param_arg"] = i;
                columnsDef.Add((object)param);

                param = new Dictionary<string, object>();
                param["param_name"] = "lte_fdd_NCell_SINR";
                param["param_arg"] = i;
                columnsDef.Add((object)param);
            }

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }
    }

    public class ZTPilotFreqPolluteByRegion_LteFdd_VOLTE : ZTPilotFreqPolluteByRegion_LteFdd
    {
        private static ZTPilotFreqPolluteByRegion_LteFdd_VOLTE instance = null;
        public static new ZTPilotFreqPolluteByRegion_LteFdd_VOLTE GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTPilotFreqPolluteByRegion_LteFdd_VOLTE();
                    }
                }
            }
            return instance;
        }
        public ZTPilotFreqPolluteByRegion_LteFdd_VOLTE()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }
        public override string Name
        {
            get { return "VOLTE_FDD导频污染分析"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30007, this.Name);
        }

        protected override bool IsInType(TestPoint tp)
        {
            if (ServiceTypes.Count > 0 && !ServiceTypes.Contains((ServiceType)tp.ServiceType)) return false;
            return true;
        }
    }
}
