﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    public partial class ReasonPnlMod3 : ReasonPanelBase
    {
        public ReasonPnlMod3()
        {
            InitializeComponent();
        }

        public override void AttachReason(ReasonBase reason)
        {
            base.AttachReason(reason);
            numDiffMax.ValueChanged -= numDiffMax_ValueChanged;
            numDiffMax.Value = (decimal)((ReasonMod3)reason).RSRPDiffMax;
            numDiffMax.ValueChanged += numDiffMax_ValueChanged;
        }


        void numDiffMax_ValueChanged(object sender, EventArgs e)
        {
            ((ReasonMod3)reason).RSRPDiffMax = (float)numDiffMax.Value;
        }
    }
}
