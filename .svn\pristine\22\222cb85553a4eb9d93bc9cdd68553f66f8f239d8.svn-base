﻿using MasterCom.RAMS.KPI_Statistics;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTWirelessNetTest
{
    public class WirelessNetTestStatInfo
    {
        public TimePeriod TestPeriod { get; set; }
        public int DistrictID { get; set; }
        public string DistrictName { get; set; }
        public CarrierStatType Carrier { get; set; }
        public List<KPIStatDataBase> DataList { get; set; }
        public List<KPIStatDataBase> EventList { get; set; }
        public WirelessNetTestProjectType Type { get; set; }
        //public FormulaType FormulaType { get; set; }
        //public List<FileInfo> Files { get; set; }
    }

    public class StatData : StatDataHubBase
    {
        public List<int> GetFileIDList()
        {
            return new List<int>(fileIDDic.Keys);
        }

        public string GetFileID(int fileID)
        {
            return fileID.ToString();
        }

        public void AddStatData(KPIStatDataBase statData)
        {
            fileIDDic[statData.FileID] = null;
            base.AddStatData(statData, false);
        }
    }
}
