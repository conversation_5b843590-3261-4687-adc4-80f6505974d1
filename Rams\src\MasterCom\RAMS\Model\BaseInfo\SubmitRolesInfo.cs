﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.UserMng;

namespace MasterCom.RAMS.Model.BaseInfo
{
    public class SubmitRolesInfo : DiySqlMultiNonQuery
    {
        protected List<FunctionRole> roles2Update;
        public SubmitRolesInfo(MainModel mainModel, List<FunctionRole> roles2Update)
            : base()
        {
            this.roles2Update = roles2Update;
            MainDB = true;
        }
        protected override string getSqlTextString()
        {
            StringBuilder strbDelete = new StringBuilder();
            StringBuilder strbInsert = new StringBuilder();
            foreach (FunctionRole role in roles2Update)
            {
                strbInsert.AppendFormat(@"if not exists (select 1 from tb_cfg_static_role where iid = {0})
    insert into tb_cfg_static_role(iid, strname,strcomment) values ({0},'{1}','{2}');"
                    , role.ID, role.Name, role.Description);
                strbInsert.AppendLine();

                string strDeleteSubFuncIds = "";
                foreach (int subFuncId in role.PermissionModifyDic.Keys)
                {
                    //先记录下该角色需要删除的subFuncId，之后再一并删除（新增的需要先删除后添加，以防有重复数据）
                    strDeleteSubFuncIds = string.Format("{0},{1}", strDeleteSubFuncIds, subFuncId);
                    if (strDeleteSubFuncIds.Length > 6000)
                    {
                        addDeleteSql(strbDelete, role.ID, ref strDeleteSubFuncIds);
                    }

                    bool hasFunc = role.PermissionModifyDic[subFuncId];
                    if (hasFunc)//新增的
                    {
                        strbInsert.AppendFormat(@"insert into tb_cfg_static_role_func(role_id, subfunc_id) values ({0},{1});"
                            , role.ID, subFuncId);
                        strbInsert.AppendLine();
                    }
                }

                if (strDeleteSubFuncIds.Length > 1)
                {
                    addDeleteSql(strbDelete, role.ID, ref strDeleteSubFuncIds);
                }

                role.PermissionModifyDic.Clear();
            }
            return strbDelete.ToString() + strbInsert.ToString();//要先删除后添加
        }
        private void addDeleteSql(StringBuilder strbDelete, int roleId, ref string strDeleteSubFuncIds)
        {
            strbDelete.AppendFormat("delete from tb_cfg_static_role_func where role_id = {0} and subfunc_id in ({1});"
                , roleId, strDeleteSubFuncIds.Remove(0, 1));//一并删除该角色需要删除的功能
            strbDelete.AppendLine();

            strDeleteSubFuncIds = "";
        }
        public override string Name
        {
            get { return "更新权限组"; }
        }
    }

    public class SubmitRolesExportPermit : SubmitRolesInfo
    {
        public SubmitRolesExportPermit(MainModel mainModel, List<FunctionRole> roles2Update)
            : base(mainModel, roles2Update)
        {
        }
        public override string Name
        {
            get { return "更新导出权限组"; }
        }
        protected override string getSqlTextString()
        {
            StringBuilder strbDelete = new StringBuilder();
            StringBuilder strbInsert = new StringBuilder();
            foreach (FunctionRole role in roles2Update)
            {
                string strDeleteSubFuncIds = "";
                foreach (int subFuncId in role.FuncExportPermitModifyDic.Keys)
                {
                    FuncExportPermit ep = role.FuncExportPermitModifyDic[subFuncId];

                    //先记录下该角色需要删除的subFuncId，之后再一并删除（新增或修改的需要先删除后添加，以防有重复数据）
                    strDeleteSubFuncIds = string.Format("{0},{1}", strDeleteSubFuncIds, subFuncId);
                    if (strDeleteSubFuncIds.Length > 6000)
                    {
                        addDeleteSql(strbDelete, role.ID, ref strDeleteSubFuncIds);
                    }

                    if (ep != null)//新增或修改
                    {
                        strbInsert.AppendFormat(@"insert into tb_cfg_static_role_funcExport (role_id, subfunc_id, export_permit
, export_zip, export_writeLog, export_writeCause, isHideKeyInfo) values ({0},{1},{2},{3},{4},{5},{6});"
                            , role.ID, ep.SubFuncID, getPermit(ep.IsCanExportResult), getPermit(ep.IsExportToZip)
                            , getPermit(ep.IsNeedExportLog), getPermit(ep.IsNeedExportCause), getPermit(ep.IsHideKeyInfo));
                        strbInsert.AppendLine();
                    }
                }

                if (strDeleteSubFuncIds.Length > 1)
                {
                    addDeleteSql(strbDelete, role.ID, ref strDeleteSubFuncIds);
                }

                role.FuncExportPermitModifyDic.Clear();
            }
            return strbDelete.ToString() + strbInsert.ToString();//要先删除后添加
        }
        private void addDeleteSql(StringBuilder strbDelete, int roleId, ref string strDeleteSubFuncIds)
        {
            strbDelete.AppendFormat("delete from tb_cfg_static_role_funcExport where role_id = {0} and subfunc_id in ({1});"
                , roleId, strDeleteSubFuncIds.Remove(0, 1));//一并删除该角色需要删除的功能权限信息
            strbDelete.AppendLine();

            strDeleteSubFuncIds = "";
        }
        private int getPermit(bool isCan)
        {
            return isCan ? 1 : 0;
        }
    }
}
