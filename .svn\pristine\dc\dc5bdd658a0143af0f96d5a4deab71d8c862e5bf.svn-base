﻿using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLTECauseValueAnaByRegion : ZTLTECauseValueAnaBase
    {
        public ZTLTECauseValueAnaByRegion(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected static readonly object lockObj = new object();
        private static ZTLTECauseValueAnaByRegion intance = null;
        public static ZTLTECauseValueAnaByRegion GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTLTECauseValueAnaByRegion(MainModel.GetInstance());
                    }
                }
            }
            return intance;
        }

        public ZTLTECauseValueAnaByRegion(bool isVoLTE)
            : base(MainModel.GetInstance())
        {
            this.isVoLTE = isVoLTE;
        }

        public override string Name
        {
            get { return "RRC重建分析(按区域)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22052, this.Name);//////
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="testPoint"></param>
        /// <returns></returns>
        /// <summary>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            try
            {
                if (testPoint is LTETestPointDetail)
                {
                    return Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }

    public class ZTLTECauseValueAnaByRegion_FDD : ZTLTECauseValueAnaByRegion
    {
        private static ZTLTECauseValueAnaByRegion_FDD instance = null;
        public static new ZTLTECauseValueAnaByRegion_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTLTECauseValueAnaByRegion_FDD(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }

        public ZTLTECauseValueAnaByRegion_FDD(MainModel mainModel)
            : base(mainModel)
        {

        }
        public override string Name
        {
            get { return "Cause值分析LTE_FDD(按区域)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26049, this.Name);//////
        }
        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        /// <summary>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            try
            {
                if (testPoint is LTEFddTestPoint)
                {
                    return Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }
}