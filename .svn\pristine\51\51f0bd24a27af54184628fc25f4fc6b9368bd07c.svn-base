﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class MainScanCoverCellChangeTableForm_W
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MainScanCoverCellChangeTableForm));
            this.dataGridViewChangeTable = new System.Windows.Forms.DataGridView();
            this.ColumnSn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnCellId = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnCellname = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnUnfreq = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnCpi = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnRoadStartLongitude = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnRoadStartLatitude = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnSampleCount = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnRoadLength = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn3 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn4 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn5 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn6 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn7 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn8 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.tsmiExplandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.tsmiShowLine = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripSeparator();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.listViewTotal = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellId = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnArfcn = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCpi = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnPreRxLev = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnNextRxLev = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLongitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLatitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSampleCount = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCvrDis = new BrightIdeasSoftware.OLVColumn();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewChangeTable)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal)).BeginInit();
            this.SuspendLayout();
            // 
            // dataGridViewChangeTable
            // 
            this.dataGridViewChangeTable.AllowUserToAddRows = false;
            this.dataGridViewChangeTable.AllowUserToDeleteRows = false;
            this.dataGridViewChangeTable.AllowUserToOrderColumns = true;
            this.dataGridViewChangeTable.AllowUserToResizeRows = false;
            this.dataGridViewChangeTable.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dataGridViewChangeTable.BackgroundColor = System.Drawing.SystemColors.Window;
            this.dataGridViewChangeTable.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.ColumnSn,
            this.ColumnCellId,
            this.ColumnCellname,
            this.ColumnUnfreq,
            this.ColumnCpi,
            this.ColumnRoadStartLongitude,
            this.ColumnRoadStartLatitude,
            this.ColumnSampleCount,
            this.ColumnRoadLength});
            this.dataGridViewChangeTable.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridViewChangeTable.EditMode = System.Windows.Forms.DataGridViewEditMode.EditProgrammatically;
            this.dataGridViewChangeTable.GridColor = System.Drawing.SystemColors.HighlightText;
            this.dataGridViewChangeTable.Location = new System.Drawing.Point(0, 0);
            this.dataGridViewChangeTable.MultiSelect = false;
            this.dataGridViewChangeTable.Name = "dataGridViewChangeTable";
            this.dataGridViewChangeTable.ReadOnly = true;
            this.dataGridViewChangeTable.RowHeadersVisible = false;
            this.dataGridViewChangeTable.RowTemplate.Height = 23;
            this.dataGridViewChangeTable.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewChangeTable.Size = new System.Drawing.Size(1062, 437);
            this.dataGridViewChangeTable.TabIndex = 0;
            // 
            // ColumnSn
            // 
            this.ColumnSn.HeaderText = "序号";
            this.ColumnSn.MinimumWidth = 10;
            this.ColumnSn.Name = "ColumnSn";
            this.ColumnSn.ReadOnly = true;
            // 
            // ColumnCellId
            // 
            this.ColumnCellId.HeaderText = "小区ID";
            this.ColumnCellId.Name = "ColumnCellId";
            this.ColumnCellId.ReadOnly = true;
            // 
            // ColumnCellname
            // 
            this.ColumnCellname.HeaderText = "小区名";
            this.ColumnCellname.Name = "ColumnCellname";
            this.ColumnCellname.ReadOnly = true;
            // 
            // ColumnUnfreq
            // 
            this.ColumnUnfreq.HeaderText = "频点(ARFCN)";
            this.ColumnUnfreq.Name = "ColumnUnfreq";
            this.ColumnUnfreq.ReadOnly = true;
            // 
            // ColumnCpi
            // 
            this.ColumnCpi.HeaderText = "扰码(CPI)";
            this.ColumnCpi.Name = "ColumnCpi";
            this.ColumnCpi.ReadOnly = true;
            // 
            // ColumnRoadStartLongitude
            // 
            this.ColumnRoadStartLongitude.HeaderText = "变更起点经度";
            this.ColumnRoadStartLongitude.MinimumWidth = 30;
            this.ColumnRoadStartLongitude.Name = "ColumnRoadStartLongitude";
            this.ColumnRoadStartLongitude.ReadOnly = true;
            // 
            // ColumnRoadStartLatitude
            // 
            this.ColumnRoadStartLatitude.HeaderText = "变更起点纬度";
            this.ColumnRoadStartLatitude.MinimumWidth = 30;
            this.ColumnRoadStartLatitude.Name = "ColumnRoadStartLatitude";
            this.ColumnRoadStartLatitude.ReadOnly = true;
            // 
            // ColumnSampleCount
            // 
            this.ColumnSampleCount.HeaderText = "采样点数";
            this.ColumnSampleCount.Name = "ColumnSampleCount";
            this.ColumnSampleCount.ReadOnly = true;
            // 
            // ColumnRoadLength
            // 
            this.ColumnRoadLength.HeaderText = "覆盖长度(米)";
            this.ColumnRoadLength.MinimumWidth = 30;
            this.ColumnRoadLength.Name = "ColumnRoadLength";
            this.ColumnRoadLength.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn1
            // 
            this.dataGridViewTextBoxColumn1.HeaderText = "路段序列号";
            this.dataGridViewTextBoxColumn1.MinimumWidth = 10;
            this.dataGridViewTextBoxColumn1.Name = "dataGridViewTextBoxColumn1";
            this.dataGridViewTextBoxColumn1.Width = 119;
            // 
            // dataGridViewTextBoxColumn2
            // 
            this.dataGridViewTextBoxColumn2.HeaderText = "小区ID";
            this.dataGridViewTextBoxColumn2.Name = "dataGridViewTextBoxColumn2";
            this.dataGridViewTextBoxColumn2.Width = 119;
            // 
            // dataGridViewTextBoxColumn3
            // 
            this.dataGridViewTextBoxColumn3.HeaderText = "频点(bcch)";
            this.dataGridViewTextBoxColumn3.Name = "dataGridViewTextBoxColumn3";
            this.dataGridViewTextBoxColumn3.Width = 120;
            // 
            // dataGridViewTextBoxColumn4
            // 
            this.dataGridViewTextBoxColumn4.HeaderText = "扰码(cpi)";
            this.dataGridViewTextBoxColumn4.Name = "dataGridViewTextBoxColumn4";
            this.dataGridViewTextBoxColumn4.Width = 120;
            // 
            // dataGridViewTextBoxColumn5
            // 
            this.dataGridViewTextBoxColumn5.HeaderText = "变更路段起始位置经度";
            this.dataGridViewTextBoxColumn5.MinimumWidth = 30;
            this.dataGridViewTextBoxColumn5.Name = "dataGridViewTextBoxColumn5";
            this.dataGridViewTextBoxColumn5.Width = 119;
            // 
            // dataGridViewTextBoxColumn6
            // 
            this.dataGridViewTextBoxColumn6.HeaderText = "变更路段起始位置纬度";
            this.dataGridViewTextBoxColumn6.MinimumWidth = 30;
            this.dataGridViewTextBoxColumn6.Name = "dataGridViewTextBoxColumn6";
            this.dataGridViewTextBoxColumn6.Width = 120;
            // 
            // dataGridViewTextBoxColumn7
            // 
            this.dataGridViewTextBoxColumn7.HeaderText = "采样点数";
            this.dataGridViewTextBoxColumn7.Name = "dataGridViewTextBoxColumn7";
            this.dataGridViewTextBoxColumn7.Width = 119;
            // 
            // dataGridViewTextBoxColumn8
            // 
            this.dataGridViewTextBoxColumn8.HeaderText = "连续覆盖路段长度（m）";
            this.dataGridViewTextBoxColumn8.MinimumWidth = 30;
            this.dataGridViewTextBoxColumn8.Name = "dataGridViewTextBoxColumn8";
            this.dataGridViewTextBoxColumn8.Width = 120;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmiExplandAll,
            this.tsmiCallapsAll,
            this.toolStripMenuItem1,
            this.tsmiShowLine,
            this.toolStripMenuItem2,
            this.miExportExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(153, 126);
            this.contextMenuStrip.Opening += new System.ComponentModel.CancelEventHandler(this.contextMenuStrip_Opening);
            // 
            // tsmiExplandAll
            // 
            this.tsmiExplandAll.Name = "tsmiExplandAll";
            this.tsmiExplandAll.Size = new System.Drawing.Size(152, 22);
            this.tsmiExplandAll.Text = "全部展开";
            this.tsmiExplandAll.Click += new System.EventHandler(this.tsmiExplandAll_Click);
            // 
            // tsmiCallapsAll
            // 
            this.tsmiCallapsAll.Name = "tsmiCallapsAll";
            this.tsmiCallapsAll.Size = new System.Drawing.Size(152, 22);
            this.tsmiCallapsAll.Text = "全部合并";
            this.tsmiCallapsAll.Click += new System.EventHandler(this.tsmiCallapsAll_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(149, 6);
            // 
            // tsmiShowLine
            // 
            this.tsmiShowLine.Name = "tsmiShowLine";
            this.tsmiShowLine.Size = new System.Drawing.Size(152, 22);
            this.tsmiShowLine.Text = "显示拉线";
            this.tsmiShowLine.Click += new System.EventHandler(this.tsmiShowLine_Click);
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            this.toolStripMenuItem2.Size = new System.Drawing.Size(149, 6);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(152, 22);
            this.miExportExcel.Text = "导出Excel(&E)";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // listViewTotal
            // 
            this.listViewTotal.AllColumns.Add(this.olvColumnSN);
            this.listViewTotal.AllColumns.Add(this.olvColumnName);
            this.listViewTotal.AllColumns.Add(this.olvColumnCellId);
            this.listViewTotal.AllColumns.Add(this.olvColumnArfcn);
            this.listViewTotal.AllColumns.Add(this.olvColumnCpi);
            this.listViewTotal.AllColumns.Add(this.olvColumnPreRxLev);
            this.listViewTotal.AllColumns.Add(this.olvColumnNextRxLev);
            this.listViewTotal.AllColumns.Add(this.olvColumnLongitude);
            this.listViewTotal.AllColumns.Add(this.olvColumnLatitude);
            this.listViewTotal.AllColumns.Add(this.olvColumnSampleCount);
            this.listViewTotal.AllColumns.Add(this.olvColumnCvrDis);
            this.listViewTotal.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnName,
            this.olvColumnCellId,
            this.olvColumnArfcn,
            this.olvColumnCpi,
            this.olvColumnPreRxLev,
            this.olvColumnNextRxLev,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnSampleCount,
            this.olvColumnCvrDis});
            this.listViewTotal.Cursor = System.Windows.Forms.Cursors.Default;
            this.listViewTotal.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewTotal.FullRowSelect = true;
            this.listViewTotal.GridLines = true;
            this.listViewTotal.HeaderWordWrap = true;
            this.listViewTotal.IsNeedShowOverlay = false;
            this.listViewTotal.Location = new System.Drawing.Point(0, 0);
            this.listViewTotal.Name = "listViewTotal";
            this.listViewTotal.OwnerDraw = true;
            this.listViewTotal.ShowGroups = false;
            this.listViewTotal.Size = new System.Drawing.Size(1062, 437);
            this.listViewTotal.TabIndex = 4;
            this.listViewTotal.UseCompatibleStateImageBehavior = false;
            this.listViewTotal.View = System.Windows.Forms.View.Details;
            this.listViewTotal.VirtualMode = true;
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            // 
            // olvColumnName
            // 
            this.olvColumnName.HeaderFont = null;
            this.olvColumnName.Text = "文件名/小区名";
            this.olvColumnName.Width = 150;
            // 
            // olvColumnCellId
            // 
            this.olvColumnCellId.HeaderFont = null;
            this.olvColumnCellId.Text = "小区ID";
            // 
            // olvColumnArfcn
            // 
            this.olvColumnArfcn.HeaderFont = null;
            this.olvColumnArfcn.Text = " 频点(ARFCN)";
            this.olvColumnArfcn.Width = 90;
            // 
            // olvColumnCpi
            // 
            this.olvColumnCpi.HeaderFont = null;
            this.olvColumnCpi.Text = "扰码(CPI)";
            this.olvColumnCpi.Width = 70;
            // 
            // olvColumnPreRxLev
            // 
            this.olvColumnPreRxLev.HeaderFont = null;
            this.olvColumnPreRxLev.Text = "变更前场强";
            this.olvColumnPreRxLev.Width = 80;
            // 
            // olvColumnNextRxLev
            // 
            this.olvColumnNextRxLev.HeaderFont = null;
            this.olvColumnNextRxLev.Text = "变更后场强";
            this.olvColumnNextRxLev.Width = 80;
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "变更起点经度";
            this.olvColumnLongitude.Width = 128;
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "变更起点纬度";
            this.olvColumnLatitude.Width = 123;
            // 
            // olvColumnSampleCount
            // 
            this.olvColumnSampleCount.HeaderFont = null;
            this.olvColumnSampleCount.Text = "采样点个数";
            this.olvColumnSampleCount.Width = 80;
            // 
            // olvColumnCvrDis
            // 
            this.olvColumnCvrDis.HeaderFont = null;
            this.olvColumnCvrDis.Text = "覆盖里程(米)";
            this.olvColumnCvrDis.Width = 100;
            // 
            // MainScanCoverCellChangeTableForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.SystemColors.Window;
            this.ClientSize = new System.Drawing.Size(1062, 437);
            this.ContextMenuStrip = this.contextMenuStrip;
            this.Controls.Add(this.listViewTotal);
            this.Controls.Add(this.dataGridViewChangeTable);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "MainScanCoverCellChangeTableForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "主服小区变更表";
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewChangeTable)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.DataGridView dataGridViewChangeTable;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn1;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn2;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn3;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn4;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn5;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn6;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn7;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn8;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnSn;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnCellId;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnCellname;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnUnfreq;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnCpi;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnRoadStartLongitude;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnRoadStartLatitude;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnSampleCount;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnRoadLength;
        private BrightIdeasSoftware.TreeListView listViewTotal;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnCellId;
        private BrightIdeasSoftware.OLVColumn olvColumnName;
        private BrightIdeasSoftware.OLVColumn olvColumnArfcn;
        private BrightIdeasSoftware.OLVColumn olvColumnCpi;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnCvrDis;
        private BrightIdeasSoftware.OLVColumn olvColumnPreRxLev;
        private BrightIdeasSoftware.OLVColumn olvColumnNextRxLev;
        private System.Windows.Forms.ToolStripMenuItem tsmiExplandAll;
        private System.Windows.Forms.ToolStripMenuItem tsmiCallapsAll;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem tsmiShowLine;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem2;

    }
}