using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class PilotFrequencyPolluteBlock_W
    {
        public PilotFrequencyPolluteBlock_W()
        {

        }
        public PilotFrequencyPolluteBlock_W(int idx, TestPoint item)
        {
            this.ID = idx;
            AddTestPoint(item);
        }
        public int ID { get; set; }

        public List<CellOfPilotFrequencyPolluteBlock_W> CellList { get; set; } = new List<CellOfPilotFrequencyPolluteBlock_W>();

        private List<WCell> cells { get; set; } = new List<WCell>();
        public List<WCell> Cells
        {
            get { return cells; }
        }
        private List<TestPoint> testPoints { get; set; } = new List<TestPoint>();
        public List<TestPoint> TestPoints
        {
            get { return testPoints; }
        }
        public int TestPointCount
        {
            get { return testPoints.Count; }
        }
        
        public int GoodTestPointCount { get; set; }

        public int TotalTestPointCount
        {
            get { return testPoints.Count + GoodTestPointCount; }
        }

        public double BadSampleScale
        {
            get
            {
                return Math.Round(100.0 * TestPointCount / (TestPointCount + GoodTestPointCount), 2);
            }
        }

        internal void Join(PilotFrequencyPolluteBlock_W tpBlock)
        {
            foreach (TestPoint tp in tpBlock.TestPoints)
            {
                AddTestPoint(tp);
            }
            this.CellList.AddRange(tpBlock.CellList);
        }

        internal bool Within(double x1, double y1, double x2, double y2)
        {
            foreach (TestPoint tp in testPoints)
            {
                if (tp.Longitude >= x1 && tp.Longitude <= x2 && tp.Latitude >= y1 || tp.Latitude <= y2)
                {
                    return true;
                }
            }
            return false;
        }
        public void AddTestPoint(TestPoint tp)
        {
            if (!testPointContained(tp))
            {
                testPoints.Add(tp);
            }
        }
        public bool Intersect(double longitude, double latitude, int radius)
        {
            foreach (TestPoint tp in testPoints)
            {
                if (MathFuncs.GetDistance(longitude, latitude, tp.Longitude, tp.Latitude) <= radius)
                {
                    return true;
                }
            }
            return false;
        }

        private bool testPointContained(TestPoint tp)
        {
            return testPoints.Contains(tp);
        }

        internal DbPoint GetCenterPoint()
        {
            DbRect bounds = new DbRect();
            bool first = true;
            foreach (TestPoint item in TestPoints)
            {
                if (first)
                {
                    bounds.x1 = item.Longitude;
                    bounds.x2 = item.Longitude;
                    bounds.y1 = item.Latitude;
                    bounds.y2 = item.Latitude;
                    first = false;
                }
                else
                {
                    setBounds(bounds, item);
                }
            }
            return bounds.Center();
        }

        private void setBounds(DbRect bounds, TestPoint item)
        {
            if (bounds.x1 > item.Longitude)
            {
                bounds.x1 = item.Longitude;
            }
            if (bounds.x2 < item.Longitude)
            {
                bounds.x2 = item.Longitude;
            }
            if (bounds.y1 > item.Latitude)
            {
                bounds.y1 = item.Latitude;
            }
            if (bounds.y2 < item.Latitude)
            {
                bounds.y2 = item.Latitude;
            }
        }

        private string roadPlaceDesc = null;
        public string RoadPlaceDesc
        {
            get
            {
                if (roadPlaceDesc == null)
                {
                    roadPlaceDesc = GISManager.GetInstance().GetRoadPlaceDesc(GetCenterPoint().x, GetCenterPoint().y);
                }
                return roadPlaceDesc;
            }
        }

        private string areaPlaceDesc = null;
        public string AreaPlaceDesc
        {
            get
            {
                if (areaPlaceDesc == null)
                {
                    areaPlaceDesc = GISManager.GetInstance().GetAreaPlaceDesc(GetCenterPoint().x, GetCenterPoint().y);
                }
                return areaPlaceDesc;
            }
        }
    }

    public class PilotFrequencyPolluteSample_W
    {
        public int index { get; set; }
        public TestPoint testPoint { get; set; }
        public WCell mainCell { get; set; }
        public List<NBCellOfPilotFrequencyPolluteSample_W> nbCellList { get; set; } = new List<NBCellOfPilotFrequencyPolluteSample_W>();
    }

    public class NBCellOfPilotFrequencyPolluteSample_W
    {
        public WCell cell { get; set; }
        public int pccpch_rscp { get; set; }
    }

    public class CellOfPilotFrequencyPolluteBlock_W
    {
        public CellOfPilotFrequencyPolluteBlock_W()
        {

        }

        public CellOfPilotFrequencyPolluteBlock_W(int sn, WCell cell, double pccpch_rscp)
        {
            this.SN = sn;
            this.Cell = cell;
            this.PCCPCH_RSCP = pccpch_rscp;
        }
        
        public int SN { get; set; }
        public WCell Cell { get; set; }
        public double PCCPCH_RSCP { get; set; }

        public double PCCPCH_RSCP_Show
        {
            get { return Math.Round(PCCPCH_RSCP, 2); }
        }
        
        public int SampleCount { get; set; }
    }
}
