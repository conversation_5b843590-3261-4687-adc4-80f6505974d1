﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ScanLTEMod3CellResultForm : MinCloseForm
    {
        public ScanLTEMod3CellResultForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            miExportExcel.Click += MiExportExcel_Click;
            gridView1.DoubleClick += GridView_DoubleClick;
            gridView2.DoubleClick += GridView_DoubleClick;
        }

        protected override void MinCloseForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            TempLayer.Instance.Clear();
            lines.Clear();
            MainModel.MainForm.GetMapForm().updateMap();
            base.MinCloseForm_FormClosing(sender, e);
        }

        public void FillData(object data)
        {
            gridControl1.DataSource = data;
            gridControl1.RefreshDataSource();
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            List<ScanLTEMod3TargetCell> targetCellList = gridControl1.DataSource as List<ScanLTEMod3TargetCell>;
            if (targetCellList == null)
            {
                return;
            }

            List<List<object>> content = new List<List<object>>();
            List<object> title = new List<object>();
            title.Add("小区名1");
            addCellInfo(title);

            title.Add("小区名2");
            addCellInfo(title);

            title.Add("模三干扰采样数");
            title.Add("受干扰采样总数");
            title.Add("受干扰比例");
            title.Add("总模三干扰比例");
            title.Add("总采样数");
            content.Add(title);

            foreach (ScanLTEMod3TargetCell tarCell in targetCellList)
            {
                List<object> tarPart = new List<object>();
                tarPart.Add(tarCell.CellName);
                tarPart.Add(tarCell.Tac);
                tarPart.Add(tarCell.Eci);
                tarPart.Add(tarCell.Earfcn);
                tarPart.Add(tarCell.Pci);
                tarPart.Add(tarCell.AvgRsrp);
                tarPart.Add(tarCell.AvgSinr);
                foreach (ScanLTEMod3SourceCell srcCell in tarCell.SourceCells)
                {
                    List<object> row = new List<object>(tarPart);
                    row.Add(srcCell.CellName);
                    row.Add(srcCell.Tac);
                    row.Add(srcCell.Eci);
                    row.Add(srcCell.Earfcn);
                    row.Add(srcCell.Pci);
                    row.Add(srcCell.AvgRsrp);
                    row.Add(srcCell.AvgSinr);

                    row.Add(srcCell.SampleCount);
                    row.Add(tarCell.InterferedSampleCount);
                    row.Add(srcCell.InterferTargetRate);
                    row.Add(srcCell.InterfereTargetTotalRate);
                    row.Add(tarCell.SampleCount);

                    content.Add(row);
                }
            }

            ExcelNPOIManager.ExportToExcel(content);
        }

        private void addCellInfo(List<object> title)
        {
            title.Add("TAC");
            title.Add("ECI");
            title.Add("EARFCN");
            title.Add("PCI");
            title.Add("平均RSRP");
            title.Add("平均SINR");
        }

        private void GridView_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gridView = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            object o = gridView.GetRow(gridView.GetSelectedRows()[0]);
            if (o == null)
            {
                return;
            }

            MainModel.DTDataManager.Clear();
            lines.Clear();
            if (o is ScanLTEMod3TargetCell)
            {
                ScanLTEMod3TargetCell tarCell = o as ScanLTEMod3TargetCell;

                Dictionary<TestPoint, int> tmpDic = new Dictionary<TestPoint, int>();
                foreach (TestPoint tp in tarCell.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                    tmpDic[tp] = 1;
                }
                foreach (TestPoint tp in tmpDic.Keys)
                {
                    Line line = new Line();
                    line.P1 = new DbPoint(tarCell.LteCell.EndPointLongitude, tarCell.LteCell.EndPointLatitude);
                    line.P2 = new DbPoint(tp.Longitude, tp.Latitude);
                    line.LineColor = Color.Red;
                    lines.Add(line);
                }

                TempLayer.Instance.Draw(DrawLine);
                MainModel.FireDTDataChanged(MainModel.MainForm);
                MainModel.MainForm.GetMapForm().GoToView(tarCell.LteCell.Longitude, tarCell.LteCell.Latitude);
            }
            else if (o is ScanLTEMod3SourceCell)
            {
                ScanLTEMod3SourceCell srcCell = o as ScanLTEMod3SourceCell;

                Dictionary<TestPoint, int> tmpDic = new Dictionary<TestPoint, int>();
                foreach (TestPoint tp in srcCell.TestPoints) 
                {
                    MainModel.DTDataManager.Add(tp);
                    tmpDic[tp] = 1;
                }
                foreach (TestPoint tp in tmpDic.Keys)
                {
                    Line line = new Line();
                    line.P1 = new DbPoint(srcCell.LteCell.EndPointLongitude, srcCell.LteCell.EndPointLatitude);
                    line.P2 = new DbPoint(tp.Longitude, tp.Latitude);
                    line.LineColor = Color.Blue;
                    lines.Add(line);
                }

                ScanLTEMod3TargetCell tarCell = srcCell.TargetCell;
                tmpDic.Clear();
                foreach (TestPoint tp in tarCell.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                    tmpDic[tp] = 1;
                }
                foreach (TestPoint tp in tmpDic.Keys)
                {
                    Line line = new Line();
                    line.P1 = new DbPoint(tarCell.LteCell.EndPointLongitude, tarCell.LteCell.EndPointLatitude);
                    line.P2 = new DbPoint(tp.Longitude, tp.Latitude);
                    line.LineColor = Color.Red;
                    lines.Add(line);
                }

                MainModel.FireDTDataChanged(MainModel.MainForm);
                TempLayer.Instance.Draw(DrawLine);
                MainModel.MainForm.GetMapForm().GoToView(tarCell.LteCell.Longitude, tarCell.LteCell.Latitude);
            }
        }

        private void DrawLine(Rectangle clientRect, Rectangle updateRect, Graphics graphics, MapOperation mop)
        {
            if (lines == null || lines.Count == 0)
            {
                return;
            }

            foreach (Line line in lines)
            {
                PointF p1, p2;
                mop.ToDisplay(line.P1, out p1);
                mop.ToDisplay(line.P2, out p2);
                Pen pen = new Pen(line.LineColor, 1);
                pen.DashStyle = System.Drawing.Drawing2D.DashStyle.Dash;

                graphics.DrawLine(pen, p1, p2);
            }
        }

        private List<Line> lines = new List<Line>();

        private class Line
        {
            public DbPoint P1;
            public DbPoint P2;
            public Color LineColor;
        }
    }
}
