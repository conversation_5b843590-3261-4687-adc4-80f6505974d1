﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NBScanTraverseRateForm : MinCloseForm
    {
        MapForm mapForm;
        public NBScanTraverseRateForm()
        {
            InitializeComponent();
            mapForm = MainModel.MainForm.GetMapForm();
        }

        public void FillData(Dictionary<string, AreaNetCellInfo> dicResult)
        {
            DataTable dt = new DataTable("NB-IOT扫频统计");
            addColumns(dt);

            foreach (KeyValuePair<string, AreaNetCellInfo> dic in dicResult)
            {
                string areaCellName = "";
                string suspectCellName = "";
                int suspectCount = 0;
                string netName = dic.Key;
                int areaNetCount = dic.Value.AreaNetCellList.Count;

                foreach (var item in dic.Value.AreaNetCellList)
                {
                    areaCellName += item + ",";
                }

                foreach (var item in dic.Value.SuspectCellList)
                {
                    if (dic.Value.AreaNetCellList.Contains(item))
                    {
                        suspectCount++;
                        suspectCellName += item + ",";
                    }
                }

                //计算遍历率
                double rate = 0;
                if (areaNetCount > 0)
                {
                    rate = Math.Round(((double)suspectCount / areaNetCount) * 100, 2);
                }

                DataRow dr = dt.NewRow();
                dr["网格"] = netName;
                dr["网格内非可疑信号的小区"] = suspectCellName.TrimEnd(',');
                dr["网格内非可疑信号的小区数"] = suspectCount;
                dr["网格范围内小区"] = areaCellName.TrimEnd(',');
                dr["网格范围内小区数"] = areaNetCount;
                dr["遍历率"] = rate + "%";
                dt.Rows.Add(dr);
            }

            NBgridControl.DataSource = dt;
            NBgridControl.RefreshDataSource();
        }

        private void addColumns(DataTable tblDatas)
        {
            tblDatas.Columns.Add("网格");
            tblDatas.Columns.Add("网格内非可疑信号的小区");
            tblDatas.Columns.Add("网格内非可疑信号的小区数");
            tblDatas.Columns.Add("网格范围内小区");
            tblDatas.Columns.Add("网格范围内小区数");
            tblDatas.Columns.Add("遍历率");
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(NBgridView);
        }
    }
}
