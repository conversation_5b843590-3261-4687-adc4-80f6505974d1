﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTCsfbCellJudgeResultForm : MinCloseForm
    {
        private List<ZTCsfbCellJudgeNew> resultList = new List<ZTCsfbCellJudgeNew>();
        private bool isFDD = false;

        public ZTCsfbCellJudgeResultForm()
        {
            InitializeComponent();
        }

        public void FillData(List<ZTCsfbCellJudgeNew> resultList, bool isFDD)
        {
            this.resultList = new List<ZTCsfbCellJudgeNew>();
            this.resultList = resultList;
            this.isFDD = isFDD;

            if (isFDD)
            {
                gridColFallLatitude.Caption = "回落后WCDMA小区纬度";
                gridColFallLongitude.Caption = "回落后WCDMA小区经度";
                gridColFallRxLev.Caption = "回落后WCDMA小区电平";
                gridColMaxFallDistance.Caption = "最强WCDMA小区距离";
                gridColMaxFallRxLev.Caption = "最强WCDMA小区电平";
                gridColMtFileName.Caption = "WCDMA文件";
            }

            //填充数据
            gridControlCsfbCellJudge.DataSource = resultList;
            gridControlCsfbCellJudge.RefreshDataSource();

            MainModel.RefreshLegend();
            MapForm mapForm = MainModel.MainForm.GetMapForm();
            if (mapForm != null)
            {
                mapForm.GetCellLayer().Invalidate();
            }
        }

        private void gridViewCsfbCellJudge_DoubleClick(object sender, EventArgs e)
        {
            object obj = gridViewCsfbCellJudge.GetFocusedRow();
            if (obj is ZTCsfbCellJudgeNew)
            {
                MainModel.ClearDTData();
                addDTDataManager(obj);
                this.MainModel.IsFileReplayByCompareMode = false;
                this.MainModel.FireDTDataChanged(this);
                if (isFDD)
                {
                    this.MainModel.FireSetDefaultMapSerialTheme("WCDMA", "TotalRSCP");
                }
                else
                {
                    this.MainModel.FireSetDefaultMapSerialTheme("GSM", "RxLevSub");
                }
            }
        }

        private void addDTDataManager(object obj)
        {
            ZTCsfbCellJudgeNew judge = obj as ZTCsfbCellJudgeNew;
            foreach (TestPoint tp in judge.TestPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }
            foreach (Event evt in judge.Events)
            {
                MainModel.DTDataManager.Add(evt);
            }
            foreach (MasterCom.RAMS.Model.Message msg in judge.Messages)
            {
                MainModel.DTDataManager.Add(msg);
            }
            foreach (ZTCsfbCellJudgeNew cj in judge.AllList)
            {
                foreach (TestPoint tp in cj.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                foreach (Event evt in cj.Events)
                {
                    MainModel.DTDataManager.Add(evt);
                }
                foreach (MasterCom.RAMS.Model.Message msg in cj.Messages)
                {
                    MainModel.DTDataManager.Add(msg);
                }
            }
        }

        private void miResultExport_Click(object sender, EventArgs e)
        {
            export2Excel();

            /**
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = true;
            dlg.Filter = FilterHelper.ExcelX;
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                this.Cursor = System.Windows.Forms.Cursors.WaitCursor;
                DevExpress.XtraPrinting.XlsxExportOptions options = new DevExpress.XtraPrinting.XlsxExportOptions();
                options.TextExportMode = DevExpress.XtraPrinting.TextExportMode.Text;
                options.SheetName = "回落非最佳判断";
                gridControlCsfbCellJudge.ExportToXlsx(dlg.FileName, options);
                this.Cursor = System.Windows.Forms.Cursors.Default;
            }
            */
        }

        private void export2Excel()
        {
            List<NPOIRow> rowList = new List<NPOIRow>(resultList.Count + 1);
            //row为标题
            NPOIRow titleRow = new NPOIRow();
            titleRow.AddCellValue("序号");
            titleRow.AddCellValue("CSFB文件");
            titleRow.AddCellValue(gridColMtFileName.Caption);
            titleRow.AddCellValue("回落时间");
            titleRow.AddCellValue("经度");
            titleRow.AddCellValue("纬度");
            titleRow.AddCellValue("回落前4G小区");
            titleRow.AddCellValue("回落前4G小区ECI");
            titleRow.AddCellValue("ENBID-LOCALCELLID");
            titleRow.AddCellValue("回落前4G小区TAC");
            titleRow.AddCellValue("回落前4G小区经度");
            titleRow.AddCellValue("回落前4G小区纬度");
            titleRow.AddCellValue("回落点与LTE基站距离");
            titleRow.AddCellValue("回落小区");
            titleRow.AddCellValue("回落小区CellID");
            titleRow.AddCellValue("LAC");
            titleRow.AddCellValue(gridColFallLongitude.Caption);
            titleRow.AddCellValue(gridColFallLatitude.Caption);
            titleRow.AddCellValue("回落距离");
            titleRow.AddCellValue(gridColFallRxLev.Caption);
            titleRow.AddCellValue(gridColMaxFallDistance.Caption);
            titleRow.AddCellValue(gridColMaxFallRxLev.Caption);
            titleRow.AddCellValue("是否在前六强小区中");

            titleRow.AddCellValue("小区名");
            titleRow.AddCellValue("小区类别");
            titleRow.AddCellValue("小区ID");
            titleRow.AddCellValue("频点");
            titleRow.AddCellValue("扰码");
            titleRow.AddCellValue("场强");

            rowList.Add(titleRow);

            foreach (ZTCsfbCellJudgeNew item in resultList)
            {
                NPOIRow row = new NPOIRow();
                fillRow(ref row, item);
                rowList.Add(row);
            }

            ExcelNPOIManager.ExportToExcel(rowList);
        }

        private void fillRow(ref NPOIRow row, ZTCsfbCellJudgeNew item)
        {
            if (row == null || item == null)
                return;

            //添加一级数据
            row.AddCellValue(item.SN);
            row.AddCellValue(item.MoFileName);
            row.AddCellValue(item.MtFileName);
            row.AddCellValue(item.BeginTime);
            row.AddCellValue(item.Longitude);
            row.AddCellValue(item.Latitude);
            row.AddCellValue(item.LteCellName);
            row.AddCellValue(item.LteECI);
            row.AddCellValue(item.ENBID);
            row.AddCellValue(item.LteTAC);
            row.AddCellValue(item.LteLongitude);
            row.AddCellValue(item.LteLatitude);
            row.AddCellValue(item.LTEDistance);
            row.AddCellValue(item.CellName);
            row.AddCellValue(item.CellID);
            row.AddCellValue(item.LAC);
            row.AddCellValue(item.FallLongitude);
            row.AddCellValue(item.FallLatitude);
            row.AddCellValue(item.FallDistance);
            row.AddCellValue(item.FallRxLev);
            row.AddCellValue(item.MaxFallDistance);
            row.AddCellValue(item.MaxFallRxLev);
            row.AddCellValue(item.IsInListDisplay);
            //添加二级数据
            foreach (SubZTCsfbCellJudgeNew subItem in item.SubInfos)
            {
                NPOIRow subRow = new NPOIRow();
                subRow.AddCellValue(subItem.CellName);
                subRow.AddCellValue(subItem.CellType);
                subRow.AddCellValue(subItem.CellID);
                subRow.AddCellValue(subItem.PCI);
                subRow.AddCellValue(subItem.BSIC);
                subRow.AddCellValue(subItem.RxLev);

                row.AddSubRow(subRow);
            }
        }

    }
}
