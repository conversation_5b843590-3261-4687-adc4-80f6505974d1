﻿using MasterCom.RAMS.Net;
using MasterCom.RAMS.Stat;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class BtsResultInfo
    {
        public BtsResultInfo(BtsWorkParam_GZ btsParam)
        {
            this.BtsName = btsParam.BtsName;
            foreach (CellWorkParam_GZ cellParam in btsParam.CellWorkParamDic.Values)
            {
                CellResultInfoDic[cellParam.Token] = new CellResultInfo(cellParam.BtsName, cellParam.CellName);
            }
        }
        public string BtsName { get; set; }

        public Dictionary<string, CellResultInfo> CellResultInfoDic { get; set; } = new Dictionary<string, CellResultInfo>();
        public void AddResult(BackgroundResult bgResult)
        {
            CellResultInfo cellInfo;
            string cellToken = string.Format("{0}_{1}", bgResult.LAC, bgResult.CI);
            if (!this.CellResultInfoDic.TryGetValue(cellToken, out cellInfo))
            {
                return;
            }

            string fileName = bgResult.FileName.ToUpper();
            if (fileName.Contains("CQT"))
            {
                if (fileName.Contains("定点上传"))
                {
                    cellInfo.KpiInfo_CQT.AddInfo_Ul(bgResult);
                }
                else if (fileName.Contains("定点下载"))
                {
                    cellInfo.KpiInfo_CQT.AddInfo_Dl(bgResult);
                }
                else if (fileName.Contains("CSFB"))
                {
                    cellInfo.KpiInfo_CQT.AddInfo_Csfb(bgResult);
                } 
            }
            else if (fileName.Contains("_DT"))
            {
                cellInfo.KpiInfo_DT.AddInfo(bgResult);
            }
        }
    }

    public class CellResultInfo
    {
        public CellResultInfo(string btsName, string cellName)
        {
            this.BtsName = btsName;
            this.CellName = cellName;
            this.KpiInfo_VOLTE = new CellKpiInfo_VOLTE(btsName, cellName);
        }
        public string BtsName { get; set; }
        public string CellName { get; set; }

        public CellKpiInfo_CqtSum KpiInfo_CQT { get; set; } = new CellKpiInfo_CqtSum();

        public CellKpiInfo_DT KpiInfo_DT { get; set; } = new CellKpiInfo_DT();

        public CellKpiInfo_VOLTE KpiInfo_VOLTE { get; set; }

        public NPOIRow GetNPOIRow_CQT()
        {
            NPOIRow cqtRow = new NPOIRow();
            cqtRow.AddCellValue(this.BtsName);
            cqtRow.AddCellValue(this.CellName);
            cqtRow.AddCellValue(this.KpiInfo_CQT.CsfbCallSuccessRate);
            cqtRow.AddCellValue(this.KpiInfo_CQT.RsrpInfo.KpiAvgValueDes);
            cqtRow.AddCellValue(this.KpiInfo_CQT.SinrInfo.KpiAvgValueDes);
            cqtRow.AddCellValue(this.KpiInfo_CQT.UlSpeedAvg);
            cqtRow.AddCellValue(this.KpiInfo_CQT.DlSpeedAvg);
            cqtRow.AddCellValue(this.KpiInfo_CQT.UlSpeedMax);
            cqtRow.AddCellValue(this.KpiInfo_CQT.DlSpeedMax);

            return cqtRow;
        }
        public NPOIRow GetNPOIRow_DT()
        {
            NPOIRow dtRow = new NPOIRow();
            dtRow.AddCellValue(this.BtsName);
            dtRow.AddCellValue(this.CellName);
            dtRow.AddCellValue(this.KpiInfo_CQT.RsrpInfo.KpiAvgValueDes);
            dtRow.AddCellValue(this.KpiInfo_CQT.SinrInfo.KpiAvgValueDes);

            return dtRow;
        }
    }

    public class CellKpiInfo_CqtSum : CellKpiInfoBase
    {
        public CellKpiInfo_CqtSum()
        {
            this.RsrpInfo = new AvgKpiInfo();
            this.SinrInfo = new AvgKpiInfo();
        }

        private bool hasGotUlInfo = false;
        private bool hasGotDlInfo = false;
        private bool hasGotCsfbInfo = false;

        public AvgKpiInfo RsrpInfo { get; set; }
        public AvgKpiInfo SinrInfo { get; set; }
        public double UlSpeedMax { get; set; }
        public double UlSpeedAvg { get; set; }
        public double DlSpeedMax { get; set; }
        public double DlSpeedAvg { get; set; }
        public double CsfbCallSuccessRate { get; set; }

        public void AddInfo_Ul(BackgroundResult bgResult)
        {
            if (hasGotUlInfo)
            {
                return;
            }
            hasGotUlInfo = true;

            Dictionary<uint, object> kpiDic = getBgResultImgDic(bgResult);
            this.UlSpeedMax = getKpiDoubleValue(KpiKey.FtpUlSpeedMax, kpiDic);
            this.UlSpeedAvg = getKpiDoubleValue(KpiKey.FtpUlSpeedAvg, kpiDic);
            int rsrpCount = getKpiIntValue(KpiKey.FtpUlPntCount_Rsrp, kpiDic);
            int sinrCount = getKpiIntValue(KpiKey.FtpUlPntCount_Sinr, kpiDic);

            this.RsrpInfo.AddKpi(rsrpCount, bgResult.RxLevMean);
            this.SinrInfo.AddKpi(sinrCount, bgResult.RxQualMean);
        }
        public void AddInfo_Dl(BackgroundResult bgResult)
        {
            if (hasGotDlInfo)
            {
                return;
            }
            hasGotDlInfo = true;

            Dictionary<uint, object> kpiDic = getBgResultImgDic(bgResult);
            this.DlSpeedMax = getKpiDoubleValue(KpiKey.FtpDlSpeedMax, kpiDic);
            this.DlSpeedAvg = getKpiDoubleValue(KpiKey.FtpDlSpeedAvg, kpiDic);
            int rsrpCount = getKpiIntValue(KpiKey.FtpDlPntCount_Rsrp, kpiDic);
            int sinrCount = getKpiIntValue(KpiKey.FtpDlPntCount_Sinr, kpiDic);

            this.RsrpInfo.AddKpi(rsrpCount, bgResult.RxLevMean);
            this.SinrInfo.AddKpi(sinrCount, bgResult.RxQualMean);
        }
        public void AddInfo_Csfb(BackgroundResult bgResult)
        {
            if (hasGotCsfbInfo)
            {
                return;
            }
            hasGotCsfbInfo = true;

            Dictionary<uint, object> kpiDic = getBgResultImgDic(bgResult);
            this.CsfbCallSuccessRate = getKpiDoubleValue(KpiKey.CsfbSucessRatioAllCall, kpiDic);

        }
    }
    public class CellKpiInfo_DT : CellKpiInfoBase
    {
        public CellKpiInfo_DT()
        {
            this.RsrpInfo = new AvgKpiInfo();
            this.SinrInfo = new AvgKpiInfo();
        }
        private bool hasGotInfo = false;
        public AvgKpiInfo RsrpInfo { get; set; }
        public AvgKpiInfo SinrInfo { get; set; }
        public void AddInfo(BackgroundResult bgResult)
        {
            if (hasGotInfo)
            {
                return;
            }
            hasGotInfo = true;

            Dictionary<uint, object> kpiDic = getBgResultImgDic(bgResult);
            int rsrpCount = getKpiIntValue(KpiKey.FtpDlPntCount_Rsrp, kpiDic);
            int sinrCount = getKpiIntValue(KpiKey.FtpDlPntCount_Sinr, kpiDic);

            this.RsrpInfo.AddKpi(rsrpCount, bgResult.RxLevMean);
            this.SinrInfo.AddKpi(sinrCount, bgResult.RxQualMean);
        }
    }
    public class CellKpiInfo_VOLTE
    {
        public CellKpiInfo_VOLTE(string btsName, string cellName)
        {
            this.BtsName = btsName;
            this.CellName = cellName;
        }
        public string BtsName { get; set; }
        public string CellName { get; set; }
        public StatInfoBase KpiInfo { get; set; }
    }

    public class CellKpiInfoBase
    {
        protected Dictionary<uint, object> getBgResultImgDic(BackgroundResult bgResult)
        {
            byte[] bytes = bgResult.GetImageValueBytes();
            Dictionary<uint, object> kpiDic = MasterCom.RAMS.NewBlackBlock.KeyValueImageParser.FromImage(bytes);
            return kpiDic;
        }
        protected int getKpiIntValue(KpiKey key, Dictionary<uint, object> kpiDic)
        {
            object valueObj;
            if (kpiDic.TryGetValue((uint)key, out valueObj))
            {
                kpiDic.Remove((uint)key);//获取值后移除该键值
                int iValue = (int)valueObj;
                if (iValue == int.MinValue)
                {
                    return 0;
                }
                return iValue;
            }
            return 0;
        }
        protected double getKpiDoubleValue(KpiKey key, Dictionary<uint, object> kpiDic)
        {
            object valueObj;
            if (kpiDic.TryGetValue((uint)key, out valueObj))
            {
                kpiDic.Remove((uint)key);//获取值后移除该键值
                double dValue = (double)valueObj;
                if (dValue == double.MinValue)
                {
                    return 0;
                }
                return dValue;
            }
            return 0.0;
        }
    }
}
