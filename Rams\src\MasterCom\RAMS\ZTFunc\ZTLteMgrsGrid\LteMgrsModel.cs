﻿using System;
using System.Collections.Generic;
using System.Text;

using MapWinGIS;

using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteMgrsTestPoint
    {
        public LteMgrsTestPoint(TestPoint tp, string mgrsString)
        {
            MgrsString = mgrsString;
            Longitude = tp.Longitude;
            Latitude = tp.Latitude;
            Time = tp.DateTime;
            RsrpList = new List<decimal>();
            EarfcnList = new List<int>();
            PciList = new List<int>();
            SinrList = new List<float?>();
            if (tp is ScanTestPoint_LTE || tp is ScanTestPoint_NBIOT)//扫频
            {
                if (LteMgrsGrid.FileType == "路测")
                {
                    return;
                }
                addLTEScanInfo(tp);
            }
            else if (tp is LTETestPointDetail)//路测
            {
                if (LteMgrsGrid.FileType == "扫频")
                {
                    return;
                }
                addLTEInfo(tp);
            }
        }

        private void addLTEScanInfo(TestPoint tp)
        {
            LteMgrsGrid.FileType = "扫频";
            for (int i = 0; i < 50; ++i)
            {
                float? rsrp = (float?)tp["LTESCAN_TopN_CELL_Specific_RSRP", i];
                float? sinr = (float?)tp["LTESCAN_TopN_CELL_Specific_RSSINR", i];
                int? earfcn = (int?)tp["LTESCAN_TopN_EARFCN", i];
                int? pci = (int?)(short?)tp["LTESCAN_TopN_PCI", i];
                if (isUnValidRsrp(rsrp) || earfcn == null || pci == null)
                {
                    break;
                }
                RsrpList.Add((decimal)rsrp);
                EarfcnList.Add((int)earfcn);
                PciList.Add((int)pci);
                SinrList.Add(sinr);
            }
        }

        private void addLTEInfo(TestPoint tp)
        {
            LteMgrsGrid.FileType = "路测";
            //主服
            float? rsrp = (float?)tp["lte_RSRP"];
            float? sinr = (float?)tp["lte_SINR"];
            int? earfcn = (int?)tp["lte_EARFCN"];
            int? pci = (int?)(short?)tp["lte_PCI"];
            if (!isUnValidRsrp(rsrp) && earfcn != null && pci != null)
            {
                RsrpList.Add((decimal)(float)rsrp);
                SinrList.Add(sinr);
                EarfcnList.Add((int)earfcn);
                PciList.Add((int)pci);
            }
            //邻区
            for (int i = 0; i < 6; ++i)
            {
                float? nrsrp = (float?)tp["lte_NCell_RSRP", i];
                float? nsinr = (float?)tp["lte_NCell_SINR", i];
                int? nearfcn = (int?)tp["lte_NCell_EARFCN", i];
                int? npci = (int?)(short?)tp["lte_NCell_PCI", i];
                if (isUnValidRsrp(rsrp) || nearfcn == null || npci == null)
                {
                    break;
                }
                RsrpList.Add((decimal)(float)nrsrp);
                SinrList.Add(nsinr);
                EarfcnList.Add((int)nearfcn);
                PciList.Add((int)npci);
            }
        }

        private bool isUnValidRsrp(float? rsrp)
        {
            return rsrp == null || rsrp < -141 || rsrp > 25;
        }
        public string MgrsString
        {
            get;
            private set;
        }

        public double Longitude
        {
            get;
            private set;
        }

        public double Latitude
        {
            get;
            private set;
        }

        public DateTime Time
        {
            get;
            private set;
        }

        public List<decimal> RsrpList
        {
            get;
            private set;
        }

        public List<float?> SinrList
        {
            get;
            private set;
        }

        public List<int> EarfcnList
        {
            get;
            private set;
        }

        public List<int> PciList
        {
            get;
            private set;
        }
    }

    public class LteMgrsFreq : IComparable<LteMgrsFreq>
    {
        public LteMgrsFreq(int earfcn, int pci)
        {
            Earfcn = earfcn;
            Pci = pci;
            Key = MakeFreqKey(earfcn, pci);
            RsrpMax = float.MinValue;
            RsrpMin = float.MaxValue;
        }

        public string Key
        {
            get;
            private set;
        }

        public int Earfcn
        {
            get;
            private set;
        }

        public int Pci
        {
            get;
            private set;
        }

        public float RsrpMax
        {
            get;
            private set;
        }

        public float RsrpMin
        {
            get;
            private set;
        }

        public float RsrpAvg
        {
            get;
            private set;
        }

        public float RsrpSum
        {
            get;
            private set;
        }

        public int RsrpCnt
        {
            get;
            private set;
        }

        public void AddRsrp(float rsrp)
        {
            RsrpMax = Math.Max(RsrpMax, rsrp);
            RsrpMin = Math.Min(RsrpMin, rsrp);
            RsrpSum += rsrp;
            RsrpCnt += 1;
            RsrpAvg = RsrpSum / RsrpCnt;
        }

        public int CompareTo(LteMgrsFreq other)
        {
            if (RsrpAvg == other.RsrpAvg)
            {
                return 0;
            }

            return RsrpAvg > other.RsrpAvg ? 1 : -1;
        }

        public static string MakeFreqKey(int earfcn, int pci)
        {
            return earfcn + ":" + pci;
        }
    }

    public class LteMgrsCell : IComparable<LteMgrsCell>
    {
        public LteMgrsCell(ICell cell)
        {
            this.Cell = cell;
            SetInfos();
            initBandJT();
        }

        public LteMgrsCell(int earfcn, int pci)
        {
            this.Cell = null;
            CellName = earfcn + "-" + pci;
            Earfcn = earfcn;
            Pci = pci;
            initBandJT();
        }

        private void initBandJT()
        {
            this.BandJT = LTECell.GetBandTypeByJT(this.Earfcn);
        }

        /// <summary>
        /// 由构造方法进行初始化，避免后续多次频繁计算
        /// </summary>
        public LTEBandTypeJT BandJT
        {
            get;
            private set;
        }

        public ICell Cell
        {
            get;
            protected set;
        }

        //最近测试点时间，用于关联当前时间点的工参（剔除多层网）
        public DateTime Time
        {
            get;
            protected set;
        }

        public string CellName
        {
            get;
            protected set;
        }

        public int? Tac
        {
            get;
            protected set;
        }

        public int? Eci
        {
            get;
            protected set;
        }

        public int Earfcn
        {
            get;
            protected set;
        }

        public int Pci
        {
            get;
            protected set;
        }

        public double? Longitude
        {
            get;
            protected set;
        }

        public double? Latitude
        {
            get;
            protected set;
        }

        public decimal AvgRsrp
        {
            get;
            protected set;
        }

        public float AvgSinr
        {
            get;
            protected set;
        }

        public int SampleCount
        {
            get;
            protected set;
        }
        public int SinrSampleCount
        {
            get;
            protected set;
        }
        public void SetTime(DateTime time)
        {
            if (time > Time)
                Time = time;
        }

        public virtual void AddPoint(decimal rsrp, float? sinr)
        {
            rsrpSum += rsrp;
            ++SampleCount;
            AvgRsrp = rsrpSum / SampleCount;

            if (sinr != null && sinr >= -50 && sinr <= 50)
            {
                ++SinrSampleCount;
                sinrSum += (float)sinr;
                AvgSinr = sinrSum / SinrSampleCount;
            }
        }

        public int CompareTo(LteMgrsCell other)
        {
            return this.AvgRsrp.CompareTo(other.AvgRsrp);
        }

        protected void SetInfos()
        {
            LTECell lteCell = Cell as LTECell;
            Tac = lteCell.TAC;
            Eci = lteCell.ECI;
            Earfcn = lteCell.EARFCN;
            Pci = lteCell.PCI;
            CellName = lteCell.Name;
            Longitude = lteCell.Longitude;
            Latitude = lteCell.Latitude;
            SampleCount = 0;
            SinrSampleCount = 0;
        }

        private decimal rsrpSum = 0;
        private float sinrSum = 0;
    }

    public class LteMgrsGrid : ILteMgrsBlockItem
    {
        public LteMgrsGrid(string mgrsString)
        {
            MgrsString = mgrsString;
            double tlLng, tlLat, brLng, brLat;
            MgrsGridConverter.GetGridLngLat(mgrsString, out tlLng, out tlLat, out brLng, out brLat);
            TLLng = tlLng;
            TLLat = tlLat;
            BRLng = brLng;
            BRLat = brLat;
            CentLng = (TLLng + BRLng) / 2;
            CentLat = (TLLat + BRLat) / 2;
            FreqDic = new Dictionary<string, LteMgrsFreq>();
            SampleList = new List<LteMgrsTestPoint>();
        }

        public string MgrsString
        {
            get;
            private set;
        }

        public string DetailInfo
        {
            get
            {
                return string.Format("栅格编号: {0}\r\n采样点总数: {1}\r\n无效点数: {2}\r\n频点个数: {3}\r\n", 
                    MgrsString, TestPointCount, InvalidPointCount, FreqDic.Count);
            }
        }

        public double TLLng
        {
            get;
            private set;
        }

        public double TLLat
        {
            get;
            private set;
        }

        public double BRLng
        {
            get;
            private set;
        }

        public double BRLat
        {
            get;
            private set;
        }

        public double CentLng
        {
            get;
            private set;
        }

        public double CentLat
        {
            get;
            private set;
        }

        public int TestPointCount
        {
            get;
            private set;
        }

        public int InvalidPointCount
        {
            get;
            private set;
        }

        public Dictionary<string, LteMgrsFreq> FreqDic
        {
            get;
            private set;
        }

        public List<LteMgrsTestPoint> SampleList
        {
            get;
            private set;
        }

        public List<LteMgrsFreq> FreqList
        {
            get
            {
                if (freqList == null)
                {
                    freqList = new List<LteMgrsFreq>(FreqDic.Values);
                    freqList.Sort();
                    freqList.Reverse();
                }
                return freqList;
            }
        }

        /// <summary>
        /// 返回按平均RSRP由大到小排序的List
        /// </summary>
        public List<LteMgrsCell> CellList
        {
            get
            {
                if (cellList == null)
                {
                    cellList = new List<LteMgrsCell>();
                    LteMgrsCellMatcher matcher = new LteMgrsCellMatcher();
                    cellList = matcher.GetCells(SampleList);
                    cellList.Sort();
                    cellList.Reverse();
                }
                return cellList;
            }
        }
        public static string FileType { get; set; } = "";//只应该有三种取值：空字符串、扫频、路测
        public void DoWithTestPoint(TestPoint tp)
        {
            bool isValid = false;
            if (tp is ScanTestPoint_LTE || tp is ScanTestPoint_NBIOT) //扫频
            {
                if (LteMgrsGrid.FileType == "路测")
                {
                    return;
                }
                isValid = addLteScanTPInfo(tp, isValid);
            }
            else if (tp is LTETestPointDetail) //路测
            {
                if (LteMgrsGrid.FileType == "扫频")
                {
                    return;
                }
                isValid = addLteTPInfo(tp, isValid);
            }
            else
            {
                return;
            }

            TestPointCount += 1;
            InvalidPointCount += (isValid ? 0 : 1);
            SampleList.Add(new LteMgrsTestPoint(tp, MgrsString));
        }

        private bool addLteScanTPInfo(TestPoint tp, bool isValid)
        {
            LteMgrsGrid.FileType = "扫频";
            for (int i = 0; i < 50; ++i)
            {
                float? rsrp = (float?)tp["LTESCAN_TopN_CELL_Specific_RSRP", i];
                int? earfcn = (int?)tp["LTESCAN_TopN_EARFCN", i];
                int? pci = (int?)(short?)tp["LTESCAN_TopN_PCI", i];
                if (isUnValidRsrp(rsrp) || earfcn == null || pci == null)
                {
                    break;
                }
                isValid = true;

                string key = LteMgrsFreq.MakeFreqKey((int)earfcn, (int)pci);
                if (!FreqDic.ContainsKey(key))
                {
                    FreqDic.Add(key, new LteMgrsFreq((int)earfcn, (int)pci));
                }
                FreqDic[key].AddRsrp((float)rsrp);
            }

            return isValid;
        }

        private bool addLteTPInfo(TestPoint tp, bool isValid)
        {
            LteMgrsGrid.FileType = "路测";
            //主服
            float? rsrp = (float?)tp["lte_RSRP"];
            int? earfcn = (int?)tp["lte_EARFCN"];
            int? pci = (int?)(short?)tp["lte_PCI"];
            if (!isUnValidRsrp(rsrp) && earfcn != null && pci != null)
            {
                isValid = true;
                string key = LteMgrsFreq.MakeFreqKey((int)earfcn, (int)pci);
                if (!FreqDic.ContainsKey(key))
                {
                    FreqDic.Add(key, new LteMgrsFreq((int)earfcn, (int)pci));
                }
                FreqDic[key].AddRsrp((float)rsrp);
            }
            //邻区
            for (int i = 0; i < 6; ++i)
            {
                float? nrsrp = (float?)tp["lte_NCell_RSRP", i];
                int? nearfcn = (int?)tp["lte_NCell_EARFCN", i];
                int? npci = (int?)(short?)tp["lte_NCell_PCI", i];
                if (isUnValidRsrp(rsrp) || nearfcn == null || npci == null)
                {
                    continue;
                }
                isValid = true;
                string key = LteMgrsFreq.MakeFreqKey((int)nearfcn, (int)npci);
                if (!FreqDic.ContainsKey(key))
                {
                    FreqDic.Add(key, new LteMgrsFreq((int)nearfcn, (int)npci));
                }
                FreqDic[key].AddRsrp((float)nrsrp);
            }

            return isValid;
        }

        private bool isUnValidRsrp(float? rsrp)
        {
            return rsrp == null || rsrp < -141 || rsrp > 25;
        }

        #region static members
        public static int SGridSize { get; set; } = 50;
        #endregion

        private List<LteMgrsFreq> freqList = null;
        private List<LteMgrsCell> cellList = null;
    }

    public class LteMgrsRegion
    {
        public LteMgrsRegion(string regionName, Shape shape)
        {
            RegionName = regionName;
            if (shape != null)
            {
                Mop2 = new MapOperation2();
                Mop2.FillPolygon(shape);
            }
            GridDic = new Dictionary<string, LteMgrsGrid>();
        }

        public string RegionName
        {
            get;
            private set;
        }

        public MapOperation2 Mop2
        {
            get;
            private set;
        }

        public Dictionary<string, LteMgrsGrid> GridDic
        {
            get;
            private set;
        }

        public List<LteMgrsTestPoint> SampleList
        {
            get
            {
                List<LteMgrsTestPoint> retList = new List<LteMgrsTestPoint>();
                foreach (LteMgrsGrid grid in GridDic.Values)
                {
                    retList.AddRange(grid.SampleList);
                }
                return retList;
            }
        }

        public void DoWithTestPoint(string mgrsString, TestPoint tp)
        {
            if (!GridDic.ContainsKey(mgrsString))
            {
                LteMgrsGrid grid = new LteMgrsGrid(mgrsString);
                if (grid.CentLng > 180 || grid.CentLat > 90) // 转换库有问题
                {
                    return;
                }
                GridDic.Add(mgrsString, grid);
            }
            GridDic[mgrsString].DoWithTestPoint(tp);
        }

        public bool AddGrid(LteMgrsGrid grid)
        {
            if (GridDic.ContainsKey(grid.MgrsString))
            {
                return false;
            }
            GridDic.Add(grid.MgrsString, grid);
            return true;
        }
    }

    public class LteMgrsCity
    {
        public LteMgrsCity(string cityName, Dictionary<string, Shape> regionShapeDic)
        {
            CityName = cityName;
            MgrsRegionMap = new Dictionary<string, LteMgrsRegion>();
            RegionShapeDic = regionShapeDic;
            RegionDic = new Dictionary<string, LteMgrsRegion>();
            RegionDic.Add(SOutsideRegion, new LteMgrsRegion(SOutsideRegion, null));
            foreach (KeyValuePair<string, Shape> kvp in regionShapeDic)
            {
                RegionDic.Add(kvp.Key, new LteMgrsRegion(kvp.Key, kvp.Value));
            }
        }

        public string CityName
        {
            get;
            private set;
        }

        // Key是网格名称
        public Dictionary<string, LteMgrsRegion> RegionDic
        {
            get;
            private set;
        }

        // Key是网格名称
        public Dictionary<string, Shape> RegionShapeDic
        {
            get;
            private set;
        }

        // Key是栅格编号
        public Dictionary<string, LteMgrsRegion> MgrsRegionMap
        {
            get;
            private set;
        }

        public List<LteMgrsTestPoint> SampleList
        {
            get
            {
                List<LteMgrsTestPoint> retList = new List<LteMgrsTestPoint>();
                foreach (LteMgrsRegion region in RegionDic.Values)
                {
                    retList.AddRange(region.SampleList);
                }
                return retList;
            }
        }

        public void DoWithTestPoint(TestPoint tp)
        {
            if (tp.Longitude == 0 || tp.Latitude == 0)
            {
                return;
            }

            string mgrsString = MgrsGridConverter.GetMgrsString(tp.Longitude, tp.Latitude, LteMgrsGrid.SGridSize);
            if (MgrsRegionMap.ContainsKey(mgrsString))
            {
                MgrsRegionMap[mgrsString].DoWithTestPoint(mgrsString, tp);
                return;
            }

            bool foundIt = false;
            LteMgrsGrid grid = new LteMgrsGrid(mgrsString);
            foreach (LteMgrsRegion reg in RegionDic.Values)
            {
                if (reg.Mop2 != null //&& reg.Mop2.CheckRectIntersectWithRegion(gRect))
                    && reg.Mop2.CheckPointInRegion(grid.CentLng, grid.CentLat))//集团算法应该为栅格的中心经纬度
                        //(reg.Mop2.CheckPointInRegion(grid.TLLng, grid.TLLat)
                        //|| reg.Mop2.CheckPointInRegion(grid.BRLng, grid.BRLat)
                        //|| reg.Mop2.CheckPointInRegion(grid.TLLng, grid.BRLat)
                        //|| reg.Mop2.CheckPointInRegion(grid.BRLng, grid.TLLat)
                        //)
                {
                    foundIt = true;
                    MgrsRegionMap.Add(mgrsString, reg);
                    reg.DoWithTestPoint(mgrsString, tp);
                    break;
                }
            }

            if (!foundIt)
            {
                MgrsRegionMap.Add(mgrsString, RegionDic[SOutsideRegion]);
                RegionDic[SOutsideRegion].DoWithTestPoint(mgrsString, tp);
            }
        }

        public bool AddGrid(LteMgrsGrid grid, string regionName)
        {
            if (!RegionDic.ContainsKey(regionName) || MgrsRegionMap.ContainsKey(grid.MgrsString))
            {
                return false;
            }

            LteMgrsRegion curRegion = RegionDic[regionName];
            MgrsRegionMap.Add(grid.MgrsString, curRegion);
            return curRegion.AddGrid(grid);
        }

        public static string SOutsideRegion { get; set; } = "网格外";
    }

    public enum LteMgrsRsrpBandType
    {
        [EnumDescriptionAttribute("D频段")]
        SingleD,

        [EnumDescriptionAttribute("F频段")]
        SingleF,

        [EnumDescriptionAttribute("最强信号")]
        Top,
    }

    public enum LteMgrsCoverageBandType
    {
        [EnumDescriptionAttribute("D频段")]
        SingleD,

        [EnumDescriptionAttribute("D1频点")]
        SingleD1,

        [EnumDescriptionAttribute("D2频点")]
        SingleD2,

        [EnumDescriptionAttribute("D频点38098")]
        SingleD_38098,//内蒙所谓的D3，实为集团标准的D2

        [EnumDescriptionAttribute("F频段")]
        SingleF,

        [EnumDescriptionAttribute("F1频点")]
        SingleF1,

        [EnumDescriptionAttribute("F2频点")]
        SingleF2,

        [EnumDescriptionAttribute("最强归属段")]
        Top,

        [EnumDescriptionAttribute("最强频点")]
        TopEarfcn,

        [EnumDescriptionAttribute("不分段")]
        All,

        [EnumDescriptionAttribute("D频点40936")]
        SingleD_40936,

        [EnumDescriptionAttribute("D频点40940")]
        SingleD_40940,

        [EnumDescriptionAttribute("E频点38950")]
        SingleE_38950,

        [EnumDescriptionAttribute("E频点39148")]
        SingleE_39148,

        [EnumDescriptionAttribute("FDD1_3683")]
        FDD1_3683,
        [EnumDescriptionAttribute("FDD1_3692")]
        FDD1_3692,
        [EnumDescriptionAttribute("FDD2_1300")]
        FDD2_1300,
        [EnumDescriptionAttribute("FDD2_1309")]
        FDD2_1309,
        [EnumDescriptionAttribute("FDD2_1259")]
        FDD2_1259,
        [EnumDescriptionAttribute("FDD2_1359")]
        FDD2_1359
    }
}
