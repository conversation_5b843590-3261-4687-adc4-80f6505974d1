﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.Stat
{
    public class SingleCellDiyKPIQuery : ReportStatQueryBase
    {
        readonly List<int> handOverList = new List<int> { 850, 851, 870, 898, 899, 1038, 1039, 1040, 1100, 1145, 1146, 1147 };
        public Dictionary<string, SingleCellStatInfo> CellFileKeyDataDic { get; set; }
        readonly string strENodeBID_CellId = "";
        int cellId { get; set; }
        int tac { get; set; }
        int eci { get; set; }
        public SingleCellDiyKPIQuery(ReporterTemplate template)
            : base()
        {
            this.rptTemplate = template;
            this.IsShowResultForm = false;
            CellFileKeyDataDic = new Dictionary<string, SingleCellStatInfo>();
        }
        public SingleCellDiyKPIQuery(ReporterTemplate template, string strENodeBID, int eNodeBID, int cellId, int tac)
            : base()
        {
            this.rptTemplate = template;
            this.IsShowResultForm = false;
            CellFileKeyDataDic = new Dictionary<string, SingleCellStatInfo>();
            this.strENodeBID_CellId = strENodeBID + "_" + cellId;
            this.eci = eNodeBID * 256 + cellId;
            this.cellId = cellId;
            this.tac = tac;
        }
        protected override Model.Interface.StatTbToken getTableNameToken()
        {
            return Model.Interface.StatTbToken.log;
        }

        public override string Name
        {
            get { return "单站验收自定义统计"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22070, "查询");
        }

        protected override void preparePackageCommand(Package package)
        {
            if (isQueringEvent)
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_DIY_AREA_EVENT;
                package.Content.PrepareAddParam();
            }
            else
            {
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;
                package.Content.Type = RequestType.REQTYPE_DIY_LOG_KPI;
                package.Content.PrepareAddParam();
            }
        }
        protected override bool getConditionBeforeQuery()
        {
            return true;
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            //
        }
        protected override void recieveAndHandleSpecificStatData(Package package
            , List<StatImgDefItem> curImgColumnDef
            , KPIStatDataBase singleStatData)
        {
            fillStatData(package, curImgColumnDef, singleStatData);
            DTDataHeader fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(singleStatData.FileID);

            if (fi != null && !fi.Name.Contains(strENodeBID_CellId))//环测文件时只取切换类指标
            {
                return;
            }

            SingleCellStatInfo tmp = new SingleCellStatInfo();
            tmp.DistrictID = condition.DistrictID;
            tmp.FileHeader = fi;
            tmp.KPIData.AddStatData(fi, singleStatData, false);
            string flieKey = GetKeyUnionString(this.rptTemplate, tmp);
            SingleCellStatInfo fsi = null;
            if (this.CellFileKeyDataDic.TryGetValue(flieKey, out fsi))
            {
                fsi.KPIData.AddStatData(fi, singleStatData, false);
            }
            else
            {
                fsi = tmp;
                this.CellFileKeyDataDic[flieKey] = fsi;
            }
        }

        protected override void handleStatEvent(Event evt)
        {
            StatDataEvent singleStatData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
            DTDataHeader fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);

            if (fi != null && !fi.Name.Contains(strENodeBID_CellId) && !handOverList.Contains(evt.ID))//环测文件时只取切换类指标
            {
                return;
            }

            SingleCellStatInfo tmp = new SingleCellStatInfo();
            tmp.DistrictID = condition.DistrictID;
            tmp.FileHeader = fi;
            tmp.KPIData.AddStatData(fi, singleStatData, false);
            string flieKey = GetKeyUnionString(this.rptTemplate, tmp);
            SingleCellStatInfo fsi = null;
            if (this.CellFileKeyDataDic.TryGetValue(flieKey, out fsi))
            {
                fsi.KPIData.AddStatData(fi, singleStatData, false);
            }
            else
            {
                fsi = tmp;
                this.CellFileKeyDataDic[flieKey] = fsi;
            }
        }

        protected string GetKeyUnionString(ReporterTemplate tpl, SingleCellStatInfo statInfo)
        {
            return GetKeyValue("kFileId", tpl, statInfo);
        }
        protected override string GetKeyValue(string keyFieldRet, ReporterTemplate tpl, StatInfoBase statInfo)
        {
            string val = "";
            if (statInfo == null || statInfo.FileHeader == null)
            {
                val = "-";
            }
            else
            {
                val = GetKeyValueBase(keyFieldRet, tpl
                    , statInfo.FileHeader.AreaTypeID, statInfo.FileHeader.AreaID, statInfo.FileHeader.DistrictID, statInfo);
            }

            return val;
        }

        public List<NPOIRow> CreateReport(ReporterTemplate tpl, List<CellAcceptInfo_SXJin> retdatas)
        {
            if (retdatas == null)
                return new List<NPOIRow>();

            List<NPOIRow> outputRows = new List<NPOIRow>();
            try
            {
                NPOIRow row;
                int staticTitleCount = 13;

                for (int r = 0; r < retdatas.Count; r++)
                {
                    CellAcceptInfo_SXJin cellInfo = retdatas[r];
                    row = new NPOIRow();
                    outputRows.Add(row);
                    row.cellValues = new List<object>(tpl.Columns.Count + staticTitleCount);
                    row.cellValues.Add(r + 1);
                    row.cellValues.Add(cellInfo.CellParamInfo.DistrictName);
                    row.cellValues.Add(cellInfo.CellParamInfo.BtsName);
                    row.cellValues.Add(cellInfo.CellParamInfo.ENodeBID);
                    row.cellValues.Add(cellInfo.CellParamInfo.Longitude);
                    row.cellValues.Add(cellInfo.CellParamInfo.Latitude);
                    row.cellValues.Add(cellInfo.CellParamInfo.Address);
                    row.cellValues.Add(cellInfo.CellParamInfo.CellName);
                    row.cellValues.Add(cellInfo.CellParamInfo.Tac);
                    row.cellValues.Add(cellInfo.CellParamInfo.CellId);
                    row.cellValues.Add(cellInfo.CellParamInfo.ARFCN);
                    row.cellValues.Add(cellInfo.CellParamInfo.PCI);
                    row.cellValues.Add(cellInfo.HasFoundFile ? "是" : "否");
                    for (int i = staticTitleCount; i < tpl.Columns.Count + staticTitleCount; i++)
                    {
                        row.cellValues.Add(null);
                        row.cellValues[i] = getColumnSetValue(cellInfo.StatInfo, tpl, i - staticTitleCount);
                    }
                }
            }
            catch
            {
                //continue
            }
            return outputRows;
        }
    }

    public class SingleCellStatInfo : StatInfoBase, IComparable<SingleCellStatInfo>
    {
        #region IComparable<SingleCellStatInfo> 成员

        public int CompareTo(SingleCellStatInfo other)
        {
            if (this.FileHeader != null && other.FileHeader != null)
            {
                return other.FileHeader.BeginTime.CompareTo(this.FileHeader.BeginTime);
            }
            else
            {
                return other.DistrictID.CompareTo(this.DistrictID);
            }
        }

        #endregion
    }
}
