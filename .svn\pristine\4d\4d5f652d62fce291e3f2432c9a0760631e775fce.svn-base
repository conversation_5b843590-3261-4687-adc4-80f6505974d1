﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTRailWayResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.dataGv = new System.Windows.Forms.DataGridView();
            this.cMStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.exportExcel = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.dataGv)).BeginInit();
            this.cMStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // dataGv
            // 
            this.dataGv.BackgroundColor = System.Drawing.SystemColors.Window;
            this.dataGv.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGv.ContextMenuStrip = this.cMStrip;
            this.dataGv.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGv.Location = new System.Drawing.Point(0, 0);
            this.dataGv.Name = "dataGv";
            this.dataGv.RowTemplate.Height = 23;
            this.dataGv.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGv.Size = new System.Drawing.Size(872, 453);
            this.dataGv.TabIndex = 0;
            // 
            // cMStrip
            // 
            this.cMStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.exportExcel});
            this.cMStrip.Name = "cMStrip";
            this.cMStrip.Size = new System.Drawing.Size(130, 26);
            // 
            // exportExcel
            // 
            this.exportExcel.Name = "exportExcel";
            this.exportExcel.Size = new System.Drawing.Size(129, 22);
            this.exportExcel.Text = "导出Excel";
            this.exportExcel.Click += new System.EventHandler(this.exportExcel_Click);
            // 
            // ZTRailWayResultForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(872, 453);
            this.Controls.Add(this.dataGv);
            this.Name = "ZTRailWayResultForm";
            this.Text = "高铁专网小区占网统计";
            ((System.ComponentModel.ISupportInitialize)(this.dataGv)).EndInit();
            this.cMStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.DataGridView dataGv;
        private System.Windows.Forms.ContextMenuStrip cMStrip;
        private System.Windows.Forms.ToolStripMenuItem exportExcel;
    }
}