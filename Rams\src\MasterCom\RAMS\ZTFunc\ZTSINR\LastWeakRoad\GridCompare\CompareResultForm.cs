﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid;
using DevExpress.XtraEditors;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.LastWeakRoad.GridCompare
{
    public partial class CompareResultForm : MinCloseForm
    {
        public CompareResultForm()
            : base(Model.MainModel.GetInstance())
        {
            InitializeComponent();
            DisposeWhenClose = true;
        }

        Layer layer = null;
        public void FillData(List<WeakSinrRoadGrid> period1Grids, List<WeakSinrRoadGrid> period2Grids, List<WeakSinrRoadGrid> repeatGrids)
        {
            gridControlP1Grid.DataSource = period1Grids;
            gridControlP1Grid.RefreshDataSource();
            gridControlRepeatGrid.DataSource = repeatGrids;
            gridControlRepeatGrid.RefreshDataSource();
            gridControlP2Grid.DataSource = period2Grids;
            gridControlP2Grid.RefreshDataSource();

            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf == null)
            {
                return;
            }
            MainModel.MainForm.RefreshLegend();
            MasterCom.MTGis.CustomDrawLayer cLayer = mf.GetCustomLayer(typeof(Layer));
            if (cLayer == null)
            {
                layer = new Layer(mf.GetMapOperation(), "弱覆盖路段栅格图层");
                mf.AddTempCustomLayer(layer);
            }
            else
            {
                layer = cLayer as Layer;
            }
            layer.Peroid1WeakGrids = period1Grids;
            layer.Peroid2Grids = period2Grids;
            layer.Invalidate();
        }

        private void gridView_DoubleClick(object sender , EventArgs e)
        {
            GridView gv = sender as GridView;
            if(gv == null)
            {
                return;
            }
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if(dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = gv.CalcHitInfo(dxEvt.Location);
            if(info == null || !info.InRow)
            {
                return;
            }
            MapForm mf = MainModel.GetInstance().MainForm.GetMapForm();
            if(mf == null)
            {
                return;
            }
            WeakSinrRoadGrid grid = gv.GetRow(info.RowHandle) as WeakSinrRoadGrid;
            if(grid == null)
            {
                return;
            }
            MainModel.GetInstance().ClearDTData();
            foreach(TestPoint tp in grid.TestPoints)
            {
                MainModel.GetInstance().DTDataManager.Add(tp);
            }
            if(grid.IntersectSegs != null)
            {
                foreach(WeakSinrRoadGrid interGrid in grid.IntersectSegs)
                {
                    foreach(TestPoint testPoint in interGrid.TestPoints)
                    {
                        MainModel.GetInstance().DTDataManager.Add(testPoint);
                    }
                }
            }
            MainModel.GetInstance().FireDTDataChanged(this);
            MainModel.FireSetDefaultMapSerialTheme("TD_LTE_SINR");
        }

        private void checkPeriod1_CheckedChanged(object sender, EventArgs e)
        {
            if (layer!=null)
            {
                layer.ShowPeriod1Grid = checkPeriod1.Checked;
                layer.Invalidate();
            }
        }

        private void checkNew_CheckedChanged(object sender, EventArgs e)
        {
            if (layer != null)
            {
                layer.ShowPeriod2NewGrid = checkNew.Checked;
                layer.Invalidate();
            }
        }

        private void checkRepeat_CheckedChanged(object sender, EventArgs e)
        {
            if (layer != null)
            {
                layer.ShowPeriod2RepeatGrid = checkRepeat.Checked;
                layer.Invalidate();
            }
        }

        private void colorPeriod1_EditValueChanged(object sender, EventArgs e)
        {
            if (layer != null)
            {
                layer.Period1GridColor = colorPeriod1.Color;
                layer.Invalidate();
            }
        }

        private void colorNew_EditValueChanged(object sender, EventArgs e)
        {
            if (layer != null)
            {
                layer.Period2NewGridColor = colorNew.Color;
                layer.Invalidate();
            }
        }

        private void colorRepeat_EditValueChanged(object sender, EventArgs e)
        {
            if (layer != null)
            {
                layer.Period2RepeatGridColor = colorRepeat.Color;
                layer.Invalidate();
            }
        }

        private void miExp2Xls_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView1);
        }

        private void toolStripMenuItem2_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView2);
        }

        private void toolStripMenuItem1_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView3);
        }
    }
}
