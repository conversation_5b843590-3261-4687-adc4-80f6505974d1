﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class MustTestRoadManager
    {
        public string Iroad { get; set; }
        public List<MTGridRoadInfo> MtGridRoadInfoList { get; set; } = new List<MTGridRoadInfo>();
    }

    /// <summary>
    /// 测试道路采样点
    /// </summary>
    public class MustTestRoadSample
    {
        public int Iroad { get; set; }
        public int GridRoaditype { get; set; }
        public float Ilongitude { get; set; }
        public float Ilatitude { get; set; }
        public string Strroad { get; set; }
        public int Ifileid { get; set; }
        public int Iservicetype { get; set; }
        public int Isampleid { get; set; }
        public string Strlogname { get; set; }
        public int Itype { get; set; }
        public double Fdistance { get; set; }
    }

    /// <summary>
    /// 道路栅格信息表
    /// </summary>
    public class MTGridRoadInfo
    {
        public int Iroad { get; set; }
        public double Fdistance { get; set; }
        public int Itype { get; set; }
        //覆盖采样点集合
        public List<MustTestRoadSample> MustTestList { get; set; } = new List<MustTestRoadSample>();
        public int Order { get; set; }
        public int Isampleid { get; set; }
        public string Strgrid { get; set; }
        public string Strroad { get; set; }
        public float Ilongitude { get; set; }
        public float Ilatitude { get; set; }
        public string Strdesc1 { get; set; }
        public string Strdesc2 { get; set; }
        public string Strdesc3 { get; set; }
    }


    /// <summary>
    /// 道路详情信息
    /// </summary>
    public class MustTestRoadDesc
    {
        public int GridRoaditype { get; set; }
        public bool Check { get; set; }
        public string RoadName { get; set; }
        public int EventNum { get; set; }
        public double LastError { get; set; }
        public double Permeate { get; set; }
        public double Mileage { get; set; }
    }
}
