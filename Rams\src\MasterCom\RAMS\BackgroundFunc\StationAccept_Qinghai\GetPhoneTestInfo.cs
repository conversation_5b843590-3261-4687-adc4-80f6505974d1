﻿using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class GetPhoneTestInfoHelper
    {
        protected GetPhoneTestInfoHelper()
        {

        }

        public static void GetOutBtsPhoneTestInfo(OutDoorBtsAcceptInfo_QH btsInfo)
        {
            reportBgInfo(string.Format("开始关联站点{0}的手机实测信息...", btsInfo.BtsName));
            QueryOutBtsPhoneTestInfo queryBtsInfo = new QueryOutBtsPhoneTestInfo(btsInfo);
            queryBtsInfo.Query();

            QueryOutCellPhoneTestInfo queryCellInfo = new QueryOutCellPhoneTestInfo(btsInfo);
            queryCellInfo.Query();
        }
        public static void GetInBtsPhoneTestInfo(InDoorBtsAcceptInfo_QH btsInfo)
        {
            reportBgInfo(string.Format("开始关联站点{0}的手机实测信息...", btsInfo.BtsName));
            QueryInBtsPhoneTestInfo queryBtsInfo = new QueryInBtsPhoneTestInfo(btsInfo);
            queryBtsInfo.Query();

            QueryInBtsFloorCoverInfo queryFloorCoverInfo = new QueryInBtsFloorCoverInfo(btsInfo);
            queryFloorCoverInfo.Query();
            reportBgInfo(string.Format("获取到{0}个楼层的室分覆盖图信息...", btsInfo.FloorCoverPicInfoDic.Count));

            QueryInCellPhoneTestInfo queryCellInfo = new QueryInCellPhoneTestInfo(btsInfo);
            queryCellInfo.Query();
        }
        protected static void reportBgInfo(string str)
        {
            BackgroundFuncManager.GetInstance().ReportBackgroundInfo(str);
        }
    }

    //室外站点实测信息
    public class OutBtsPhoneTestInfo
    {
        public int ENodeBID { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public string AllViewPic_FolderName { get; set; }//站点全貌图_文件夹名
        public string AllViewPic_Buiding { get; set; }
        public string AllViewPic_Entry { get; set; }
        public string AllViewPic_Housetop { get; set; }
        public string AroundPic_FolderName { get; set; }//站点周边区域图_文件夹名
        public string AroundPic_0 { get; set; }
        public string AroundPic_45 { get; set; }
        public string AroundPic_90 { get; set; }
        public string AroundPic_135 { get; set; }
        public string AroundPic_180 { get; set; }
        public string AroundPic_225 { get; set; }
        public string AroundPic_270 { get; set; }
        public string AroundPic_315 { get; set; }
        public void Fill(Content content)
        {
            this.ENodeBID = content.GetParamInt();
            this.Longitude = content.GetParamFloat();
            this.Latitude = content.GetParamFloat();
            this.AllViewPic_FolderName = content.GetParamString();
            this.AllViewPic_Buiding = content.GetParamString();
            this.AllViewPic_Entry = content.GetParamString();
            this.AllViewPic_Housetop = content.GetParamString();
            this.AroundPic_FolderName = content.GetParamString();
            this.AroundPic_0 = content.GetParamString();
            this.AroundPic_45 = content.GetParamString();
            this.AroundPic_90 = content.GetParamString();
            this.AroundPic_135 = content.GetParamString();
            this.AroundPic_180 = content.GetParamString();
            this.AroundPic_225 = content.GetParamString();
            this.AroundPic_270 = content.GetParamString();
            this.AroundPic_315 = content.GetParamString();
        }
    }
    public class QueryOutBtsPhoneTestInfo : DIYSQLBase
    {
        readonly OutDoorBtsAcceptInfo_QH btsInfo;
        public QueryOutBtsPhoneTestInfo(OutDoorBtsAcceptInfo_QH btsInfo)
            : base()
        {
            MainDB = true;
            this.btsInfo = btsInfo;
        }
        protected override string getSqlTextString()
        {
            return string.Format(@"select top 1 enodebid, 经度, 纬度, 站点全貌图_文件夹名
,站点全貌图_建筑全景图, 站点全貌图_站点入口图, 站点全貌图_屋顶天面全景图
,站点周边区域图_文件夹名, 站点周边区域图_方向图_0, 站点周边区域图_方向图_45, 站点周边区域图_方向图_90
,站点周边区域图_方向图_135, 站点周边区域图_方向图_180, 站点周边区域图_方向图_225
,站点周边区域图_方向图_270, 站点周边区域图_方向图_315
from {0}.[dbo].[TB_自动单验_流程_基站信息]
where enodebid = {1} order by 工单流水号 desc", StationAcceptAna_QH.GetInstance().FuncSet.PhoneTestDbName, btsInfo.BtsId);
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            E_VType[] arr = new E_VType[16];
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Float;
            arr[i++] = E_VType.E_Float;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i] = E_VType.E_String;
            return arr;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    btsInfo.PhoneTestInfo = new OutBtsPhoneTestInfo();
                    btsInfo.PhoneTestInfo.Fill(package.Content);
                    break;
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    break;
                }
            }
        }
    }

    //室外小区实测信息
    public class OutCellPhoneTestInfo
    {
        public string CGI { get; set; }
        public string CellName { get; set; }
        public float Altitude { get; set; }//挂高
        public float Direction { get; set; }//方向角
        public float Downward { get; set; }//下倾角
        public string TopPanel_FolderName { get; set; }//小区原天面图_文件夹名
        public string TopPanel_PicName { get; set; }//小区原天面图_小区天面图
        public void Fill(Content content)
        {
            this.CGI = content.GetParamString();
            this.CellName = content.GetParamString();
            this.Altitude = content.GetParamFloat();
            this.Direction = content.GetParamFloat();
            this.Downward = content.GetParamFloat();
            this.TopPanel_FolderName = content.GetParamString();
            this.TopPanel_PicName = content.GetParamString();
        }
    }
    public class QueryOutCellPhoneTestInfo : DIYSQLBase
    {
        readonly OutDoorBtsAcceptInfo_QH btsInfo;
        public QueryOutCellPhoneTestInfo(OutDoorBtsAcceptInfo_QH btsInfo)
            : base()
        {
            MainDB = true;
            this.btsInfo = btsInfo;
        }
        protected override string getSqlTextString()
        {
            return string.Format(@"select cgi,小区名称, 天线挂高,方位角,下倾角, 小区原天面图_文件夹名
,小区原天面图_小区天面图
from {0}.[dbo].[TB_自动单验_流程_小区信息]
where enodebid = {1} order by 工单流水号 desc", StationAcceptAna_QH.GetInstance().FuncSet.PhoneTestDbName, btsInfo.BtsId);
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            E_VType[] arr = new E_VType[7];
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Float;
            arr[i++] = E_VType.E_Float;
            arr[i++] = E_VType.E_Float;
            arr[i++] = E_VType.E_String;
            arr[i] = E_VType.E_String;
            return arr;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    OutCellPhoneTestInfo cellTestInfo = new OutCellPhoneTestInfo();
                    cellTestInfo.Fill(package.Content);
                    foreach (OutDoorCellAcceptInfo_QH cellInfo in btsInfo.CellsAcceptDic.Values)
                    {
                        if (cellInfo.CellName.Trim() == cellTestInfo.CellName.Trim()
                            && cellInfo.PhoneTestInfo == null)
                        {
                            cellInfo.PhoneTestInfo = cellTestInfo;
                            break;
                        }
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    break;
                }
            }
        }
    }

    //室内站点实测信息
    public class InBtsPhoneTestInfo
    {
        public int ENodeBID { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public void Fill(Content content)
        {
            this.ENodeBID = content.GetParamInt();
            this.Longitude = content.GetParamFloat();
            this.Latitude = content.GetParamFloat();
        }
    }
    public class QueryInBtsPhoneTestInfo : DIYSQLBase
    {
        readonly InDoorBtsAcceptInfo_QH btsInfo;
        public QueryInBtsPhoneTestInfo(InDoorBtsAcceptInfo_QH btsInfo)
            : base()
        {
            MainDB = true;
            this.btsInfo = btsInfo;
        }
        protected override string getSqlTextString()
        {
            return string.Format(@"select top 1 enodebid, 经度, 纬度
from {0}.[dbo].[TB_自动单验_流程_基站信息] 
where enodebid = {1} order by 工单流水号 desc", StationAcceptAna_QH.GetInstance().FuncSet.PhoneTestDbName, btsInfo.BtsId);
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            E_VType[] arr = new E_VType[3];
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Float;
            arr[i] = E_VType.E_Float;
            return arr;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    btsInfo.PhoneTestInfo = new InBtsPhoneTestInfo();
                    btsInfo.PhoneTestInfo.Fill(package.Content);
                    break;
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    break;
                }
            }
        }
    }
    
    //室内站点楼层覆盖图信息
    public class InBtsFloorCoverPicInfo : IComparable<InBtsFloorCoverPicInfo>
    {
        public int ENodeBID { get; set; }
        public string FolderName { get; set; }
        public string FloorName { get; set; }
        public string CoverPic_Rsrp { get; set; }
        public string CoverPic_Sinr { get; set; }
        public string CoverPic_FtpDl { get; set; }
        public string CoverPic_FtpUl { get; set; }
        public string CoverPic_Pci { get; set; }
        public void Fill(Content content)
        {
            this.ENodeBID = content.GetParamInt();
            this.FolderName = content.GetParamString();
            this.FloorName = content.GetParamString();
            this.CoverPic_Rsrp = getFirstPicPath(content.GetParamString());
            this.CoverPic_Sinr = getFirstPicPath(content.GetParamString());
            this.CoverPic_FtpDl = content.GetParamString();
            this.CoverPic_FtpUl = content.GetParamString();
            this.CoverPic_Pci = getFirstPicPath(content.GetParamString());
        }

        private string getFirstPicPath(string fullPath)
        {
            if (!string.IsNullOrEmpty(fullPath))
            {
                return fullPath.Split(',')[0];
            }
            return "";
        }
        #region IComparable<InBtsFloorCoverPicInfo> 成员
        public int CompareTo(InBtsFloorCoverPicInfo other)
        {
            return this.FloorName.CompareTo(other.FloorName);
        }
        #endregion
    }
    public class QueryInBtsFloorCoverInfo : DIYSQLBase
    {
        readonly InDoorBtsAcceptInfo_QH btsInfo;
        public QueryInBtsFloorCoverInfo(InDoorBtsAcceptInfo_QH btsInfo)
            : base()
        {
            MainDB = true;
            this.btsInfo = btsInfo;
        }
        protected override string getSqlTextString()
        {
            return string.Format(@"select enodebid,文件夹名,楼层,RSRP轨迹图,SINR轨迹图,FTP下载轨迹图
,FTP上传轨迹图,切换PCI轨迹图
from {0}.[dbo].[TB_自动单验_流程_性能覆盖效果图] 
where enodebid = {1} order by 工单流水号 desc", StationAcceptAna_QH.GetInstance().FuncSet.PhoneTestDbName, btsInfo.BtsId);
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            E_VType[] arr = new E_VType[8];
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i] = E_VType.E_String;
            return arr;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    InBtsFloorCoverPicInfo floorCoverInfo = new InBtsFloorCoverPicInfo();
                    floorCoverInfo.Fill(package.Content);
                    if (!btsInfo.FloorCoverPicInfoDic.ContainsKey(floorCoverInfo.FloorName))
                    {
                        btsInfo.FloorCoverPicInfoDic.Add(floorCoverInfo.FloorName, floorCoverInfo);
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    break;
                }
            }
        }
    }

    //室内小区实测信息
    public class InCellPhoneTestInfo
    {
        public string CellName { get; set; }
        public string CGI { get; set; }
        public float PCI { get; set; }
        public float EARFCN { get; set; }
        public void Fill(Content content)
        {
            this.CellName = content.GetParamString();
            this.CGI = content.GetParamString();
            this.PCI = content.GetParamFloat();
            this.EARFCN = content.GetParamFloat();
        }
    }
    public class QueryInCellPhoneTestInfo : DIYSQLBase
    {
        readonly InDoorBtsAcceptInfo_QH btsInfo;
        public QueryInCellPhoneTestInfo(InDoorBtsAcceptInfo_QH btsInfo)
            : base()
        {
            MainDB = true;
            this.btsInfo = btsInfo;
        }
        protected override string getSqlTextString()
        {
            return string.Format(@"select 小区名称, cgi, PCI,频点
from {0}.[dbo].[TB_自动单验_流程_小区信息]
where enodebid = {1} order by 工单流水号 desc", StationAcceptAna_QH.GetInstance().FuncSet.PhoneTestDbName, btsInfo.BtsId);
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            E_VType[] arr = new E_VType[4];
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Float;
            arr[i] = E_VType.E_Float;
            return arr;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    InCellPhoneTestInfo cellTestInfo = new InCellPhoneTestInfo();
                    cellTestInfo.Fill(package.Content);
                    foreach (InDoorCellAcceptInfo_QH cellInfo in btsInfo.CellsAcceptDic.Values)
                    {
                        if (cellInfo.CellName.Trim() == cellTestInfo.CellName.Trim()
                            && cellInfo.PhoneTestInfo == null)
                        {
                            cellInfo.PhoneTestInfo = cellTestInfo;
                            break;
                        }
                    }
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    break;
                }
            }
        }
    }

    public class QueryPhoneTestDbInfo : DIYSQLBase
    { 
        public QueryPhoneTestDbInfo()
        {
            MainDB = true;
        }
        protected override string getSqlTextString()
        {
            return "select serverIp, dbName from tb_fusionDbSetting_qinghai where tableType = '手机实测'";
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            E_VType[] arr = new E_VType[2];
            arr[i++] = E_VType.E_String;
            arr[i] = E_VType.E_String;
            return arr;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    string serverIp = package.Content.GetParamString();
                    string dbName = package.Content.GetParamString();
                    StationAcceptAna_QH.GetInstance().FuncSet.PhoneTestDbName = string.Format("{0}.{1}", serverIp, dbName);
                    break;
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    break;
                }
            }
        }
    }
}
