using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.Frame
{
    public partial class LeakOutCellSetConditionDlg : BaseDialog
    {
        public LeakOutCellSetConditionDlg()
        {
            InitializeComponent();
        }

        private static LeakOutCellSetConditionDlg dlg = new LeakOutCellSetConditionDlg();
        public static LeakOutCellSetConditionDlg GetDlg()
        {
            return dlg;
        }

        private void simpleButtonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleButtonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        public int RscpLow
        {
            get { return int.Parse(spinEditLow.Value.ToString()); }
        }

        public int RscpLimit
        {
            get { return int.Parse(spinEditLimit.Value.ToString()); }
        }

        public int MaxDistance
        {
            get { return int.Parse(edtMaxDistance.Value.ToString()); }
        }

        public int ValidDistance
        {
            get { return int.Parse(edtValidDistance.Value.ToString()); }
        }
    }
}