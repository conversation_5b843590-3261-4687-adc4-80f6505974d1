﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.MControls;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Stat;
using MasterCom.Util;

namespace MasterCom.RAMS.KPI_Statistics.ReportForm
{
    public partial class CellOptionForm : BaseForm
    {
        private CellOptionForm()
            : base()
        {
            InitializeComponent();
            rngColorPnl.SetScoreColumnCaption("值范围");
            rngColorPnl.DescColumnsVisible = false;
            this.kpiExpEditor.SubmitFormula += kpiExpEditor_SubmitFormula;
            mainModel.DistrictChanged += mainModel_DistrictChanged;
            servPanel = new ItemSelectionPanel(toolStripDropDownService, listViewService, lbSvCount, new MapFormItemSelection(), "ServiceType", false);
            servPanel.FreshItems();
            toolStripDropDownService.Items.Add(new ToolStripControlHost(servPanel));
            this.toolTip.Popup += toolTip_Popup;
            cbxfileNumType.SelectedIndex = 0;
        }

        protected override void OnClosed(EventArgs e)
        {
            mainModel.DistrictChanged -= mainModel_DistrictChanged;
            base.OnClosed(e);
        }

        void toolTip_Popup(object sender, PopupEventArgs e)
        {
            if (e.AssociatedControl==this.listViewService)
            {
                StringBuilder text = new StringBuilder();
                foreach (ListViewItem item in this.listViewService.Items)
                {
                    text.Append(item.Text + Environment.NewLine);
                }
                toolTip.SetToolTip(this.listViewService, text.ToString());
            }
        }

        void mainModel_DistrictChanged(object sender, EventArgs e)
        {
            servPanel.FreshItems();
        }

        ItemSelectionPanel servPanel;
        void kpiExpEditor_SubmitFormula(object sender, EventArgs e)
        {
            MasterCom.RAMS.Util.KPIFormulaEditor.SubmitFormulaEventArgs ee = e as MasterCom.RAMS.Util.KPIFormulaEditor.SubmitFormulaEventArgs;
            if (!string.IsNullOrEmpty(ee.Desc))
            {
                this.tbxTitle.Text = ee.Desc;
            }
        }

        private static CellOptionForm instance = null;
        public static CellOptionForm Instance
        {
            get
            {
                if (instance == null || instance.IsDisposed)
                {
                    instance = new CellOptionForm();
                }
                return instance;
            }
        }

        private int rowIdx = -1;
        private int colIdx = -1;

        /// <summary>
        /// 传入待设置单元格
        /// </summary>
        /// <param name="cell">原单元格，不能为null</param>
        public void SetReportCells(RptCell orgCell, int rowIdx, int colIdx)
        {
            rBtnTop.Enabled = rowIdx > 0;
            rBtnLeft.Enabled = colIdx > 0;
            rBtnNoTitle.Checked = true;
            this.titleCell = null;
            this.expCell = orgCell;
            this.rowIdx = rowIdx;
            this.colIdx = colIdx;
            if (orgCell == null)
            {
                this.Text = "单元格设置（新单元格）";
                return;
            }
            else
            {
                this.Text = "单元格设置";
            }

            this.listViewService.Items.Clear();
            CategoryEnumItem[] svItems = ((CategoryEnum)CategoryManager.GetInstance()["ServiceType"]).Items;
            foreach (int id in expCell.ServiceIDSet)
            {
                foreach (CategoryEnumItem item in svItems)
                {
                    if (item.ID==id)
                    {
                        ListViewItem lvItem = new ListViewItem(item.Description);
                        lvItem.Tag = item.ID;
                        this.listViewService.Items.Add(lvItem);
                        break;
                    }
                }
            }
            this.lbSvCount.Text = string.Format("[{0}]", this.listViewService.Items.Count);
            carrierID = expCell.carrierID;
            momtFlag = expCell.momt;
            this.kpiExpEditor.Formula = expCell.exp;
            clrCellFore.Color = expCell.foreColor;
            clrCellBkStatic.Color = expCell.bkColor;
            rBtnDynamic.Checked = expCell.IsDynamicBKColor;
            chkGridAvg.Checked = expCell.ExtraExp == RptCell.GridAvg;
            numRngMin.ValueChanged -= numRngMin_ValueChanged;
            numRngMax.ValueChanged -= numRngMax_ValueChanged;
            numRngMin.Value = (decimal)expCell.ValueRangeMin;
            numRngMax.Value = (decimal)expCell.ValueRangeMax;
            this.numericUpDownDeciNum.Value = (decimal)expCell.deciNum;

            if (!string.IsNullOrEmpty(expCell.FileNameFilter))
            {
                checkBoxFileName.Checked = true;
            }
            else
            {
                checkBoxFileName.Checked = false;
            }
            textBoxFileName.Text = expCell.FileNameFilter;

            rngColorPnl.SetScoreColorRanges(expCell.DynamicBKColorRanges
                , expCell.ValueRangeMin, expCell.ValueRangeMax);
            numRngMin.ValueChanged += numRngMin_ValueChanged;
            numRngMax.ValueChanged += numRngMax_ValueChanged;
        }

        private void updateExpCell()
        {
            if (expCell == null)
            {
                expCell = new RptCell();
            }
            expCell.rowAt = this.rowIdx;
            expCell.colAt = this.colIdx;
            expCell.carrierID = carrierID;
            expCell.momt = momtFlag;
            expCell.exp = this.kpiExpEditor.Formula;
            expCell.foreColor = clrCellFore.Color;
            expCell.bkColor = clrCellBkStatic.Color;
            expCell.IsDynamicBKColor = rBtnDynamic.Checked;
            expCell.DynamicBKColorRanges = rngColorPnl.Ranges;
            expCell.ValueRangeMin = (float)numRngMin.Value;
            expCell.ValueRangeMax = (float)numRngMax.Value;
            expCell.ExtraExp = null;

            expCell.FileNameFilter = "";
            if (checkBoxFileName.Checked)
            {
                expCell.FileNameFilter = textBoxFileName.Text;
            }
            if (chkGridAvg.Checked)
            {
                expCell.ExtraExp = RptCell.GridAvg;
            }
            List<int> idSet = new List<int>();
            foreach (ListViewItem item in this.listViewService.Items)
            {
                idSet.Add((int)item.Tag);
            }
            expCell.ServiceIDSet = idSet;
            expCell.deciNum = (int)this.numericUpDownDeciNum.Value;
        }

        void numRngMax_ValueChanged(object sender, EventArgs e)
        {
            rngColorPnl.UpdateRange((double)numRngMin.Value, (double)numRngMax.Value);
        }

        void numRngMin_ValueChanged(object sender, EventArgs e)
        {
            rngColorPnl.UpdateRange((double)numRngMin.Value, (double)numRngMax.Value);
        }

        private void rBtnNoTitle_CheckedChanged(object sender, EventArgs e)
        {
            clrTitle.Enabled = clrTitleBk.Enabled = tbxTitle.Enabled = !rBtnNoTitle.Checked;
        }

        RptCell expCell = null;
        /// <summary>
        /// 表达式单元格
        /// </summary>
        public RptCell ExpCell
        {
            get { return expCell; }
        }

        RptCell titleCell = null;
        /// <summary>
        /// 附加的标题单元格
        /// </summary>
        public RptCell TitleCell
        {
            get { return titleCell; }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (!rBtnNoTitle.Checked && tbxTitle.Text.Trim().Length == 0)
            {//标题为空
                MessageBox.Show("标题不能为空！");
                return;
            }
            updateExpCell();
            updateTitleCell();
            DialogResult = DialogResult.OK;
        }

        private void updateTitleCell()
        {
            if (rBtnNoTitle.Checked)
            {
                this.titleCell = null;
                return;
            }
            titleCell = new RptCell();
            titleCell.rowAt = rBtnLeft.Checked ? expCell.rowAt : expCell.rowAt - 1;
            titleCell.colAt = rBtnLeft.Checked ? expCell.colAt - 1 : expCell.colAt;
            titleCell.exp = tbxTitle.Text;
            titleCell.foreColor = clrTitle.Color;
            titleCell.bkColor = clrTitleBk.Color;
        }

        private byte carrierID
        {
            get
            {
                if (rbtnChinaMobile.Checked)
                {
                    return (byte)Model.CarrierType.ChinaMobile;
                }
                else if (rbtnChinaUnicom.Checked)
                {
                    return (byte)Model.CarrierType.ChinaUnicom;
                }
                else
                {
                    return (byte)Model.CarrierType.ChinaTelecom;
                }
            }
            set {
                CarrierType type = (CarrierType)value;
                switch (type)
                {
                    case CarrierType.ChinaMobile:
                        rbtnChinaMobile.Checked = true;
                        break;
                    case CarrierType.ChinaUnicom:
                        rbtnChinaUnicom.Checked = true;
                        break;
                    case CarrierType.ChinaTelecom:
                        rbtnChinaTelecom.Checked = true;
                        break;
                    default:
                        break;
                }
            }
        }
        private byte momtFlag
        {
            get
            {
                if (rbtnMo.Checked)
                {
                    return 1;
                }
                else if (rbtnMt.Checked)
                {
                    return 2;
                }
                else
                {
                    return 0;
                }
            }
            set
            {
                if (value == 1)
                {
                    rbtnMo.Checked = true;
                }
                else if (value == 2)
                {
                    rbtnMt.Checked = true;
                }
                else if (value == 0)
                {
                    rbtnAll.Checked = true;
                }
            }
        }

        private void numRngMin_Validating(object sender, CancelEventArgs e)
        {
            e.Cancel = numRngMin.Value > numRngMax.Value;
        }

        private void numRngMax_Validating(object sender, CancelEventArgs e)
        {
            e.Cancel = numRngMin.Value > numRngMax.Value;
        }

        private void rBtnStaticBk_CheckedChanged(object sender, EventArgs e)
        {
            numRngMin.Enabled = numRngMax.Enabled = rngColorPnl.Enabled = rBtnDynamic.Checked;
        }

        private void CellOptionForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            Hide();
        }

        private void btnPopupService_Click(object sender, EventArgs e)
        {
            List<int> idSet = new List<int>();
            foreach (ListViewItem item in this.listViewService.Items)
            {
                idSet.Add((int)item.Tag);
            }
            this.servPanel.UpdateNodeState(idSet);
            System.Drawing.Point pt = new System.Drawing.Point(btnPopupService.Width, btnPopupService.Height);
            toolStripDropDownService.Show(btnPopupService, pt, ToolStripDropDownDirection.AboveLeft);
        }

        private void checkBoxFileName_CheckedChanged(object sender, EventArgs e)
        {
            textBoxFileName.Enabled = checkBoxFileName.Checked;
            ccbePort.Enabled = checkBoxFileName.Checked;
            btnClearPort.Enabled = checkBoxFileName.Checked;
        }

        private void btnClearPort_Click(object sender, EventArgs e)
        {
            ccbePort.Reset();
            ccbePort.Text = "";
            ccbePort.RefreshEditValue();
            textBoxFileName.Text = "";
        }

        private void ccbePort_EditValueChanged(object sender, EventArgs e)
        {
            textBoxFileName.Text = "";
            string[] attr = ccbePort.Text.Split(',');
            if (attr.Length == 1 && attr[0] == "")
            {
                return;
            }
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < attr.Length; i++)
            {
                if (i > 0 || sb.Length > 0)
                {
                    sb.Append(" or ");
                }
                if (cbxfileNumType.SelectedIndex == 0)
                {
                    sb.Append("(" + attr[i].Trim() + ")");
                }
                else if (cbxfileNumType.SelectedIndex == 1)
                {
                    sb.Append("ms" + attr[i].Trim());
                }
            }
            textBoxFileName.Text = sb.ToString();
        }
    }
}
