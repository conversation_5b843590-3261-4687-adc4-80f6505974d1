﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRReasonPnlHandoverProblem : NRReasonPanelBase
    {
        public NRReasonPnlHandoverProblem()
        {
            InitializeComponent();
        }

        public override void AttachReason(NRReasonBase reason)
        {
            base.AttachReason(reason);
            timePersistMax.ValueChanged -= timePersistMax_ValueChanged;
            timePersistMax.Value = (decimal)ZTWeakSINRReason.timePersist;
            timePersistMax.ValueChanged += timePersistMax_ValueChanged;
            timeLimitMax.ValueChanged -= timeLimitMax_ValueChanged;
            timeLimitMax.Value = (decimal)ZTWeakSINRReason.timeBeforeWeakSinr;
            timeLimitMax.ValueChanged += timeLimitMax_ValueChanged;
        }

        void timeLimitMax_ValueChanged(object sender, EventArgs e)
        {
            NRWeakSINRReason.timeBeforeWeakSinr = (int)timeLimitMax.Value;
        }
        void timePersistMax_ValueChanged(object sender, EventArgs e)
        {
            NRWeakSINRReason.timePersist = (int)timePersistMax.Value;
        }
    }
}
