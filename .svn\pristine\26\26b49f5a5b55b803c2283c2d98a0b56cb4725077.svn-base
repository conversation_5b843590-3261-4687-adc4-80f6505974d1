﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDiyLteFddCellSetQueryByRegion : DIYSampleByRegion
    {
        Dictionary<LTECell, LteFddCellInfo> lteCellInfoDic;
        Dictionary<string, LteFddCellInfo> notFindCellInfoDic;

        public ZTDiyLteFddCellSetQueryByRegion(MainModel mainModel)
            : base(mainModel)
        {
            isAddSampleToDTDataManager = false;
        }

        public override string Name
        {
            get { return "LTEFDD区域小区集"; }
        }

        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26001, this.Name);
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            DIYSampleGroup cellSetGroup = new DIYSampleGroup();
            cellSetGroup.ThemeName = "---";

            DTParameter parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_TAC");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_ECI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_EARFCN");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_PCI");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_RSRQ");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_RSRP");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_SINR");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }
            parameter = DTParameterManager.GetInstance().GetParameter("lte_fdd_MNC");
            if (parameter != null)
            {
                DIYSampleParamDef pDef = new DIYSampleParamDef();
                pDef.parameter = parameter;
                cellSetGroup.ColumnsDefSet.Add(pDef);
            }

            return cellSetGroup;
        }

        protected override void doSomethingBeforeQueryInThread()
        {
            lteCellInfoDic = new Dictionary<LTECell, LteFddCellInfo>();
            notFindCellInfoDic = new Dictionary<string, LteFddCellInfo>();
        }

        protected override void query()
        {
            curSelDIYSampleGroup = getCurSelDIYSampleGroupPrepare();
            if (curSelDIYSampleGroup == null)
            {
                return;
            }
            regionMopDic = new Dictionary<string, MapOperation2>();
            InitRegionMop2();
            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                WaitBox.CanCancel = true;
                clientProxy = new ClientProxy();
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);

            ShowResultForm();
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private void ShowResultForm()
        {
            LTECellSetForm lteCellSetForm = MainModel.CreateResultForm(typeof(LTECellSetForm)) as LTECellSetForm;
            lteCellSetForm.FillData(lteCellInfoDic, notFindCellInfoDic);
            lteCellSetForm.Visible = true;
            lteCellSetForm.BringToFront();
        }

        Dictionary<string, MapOperation2> regionMopDic = null;
        private void InitRegionMop2()
        {
            List<ResvRegion> resvRegions = MainModel.SearchGeometrys.SelectedResvRegions;
            MapWinGIS.Shape gmt = MainModel.SearchGeometrys.Region;

            if (resvRegions != null && resvRegions.Count > 0)  //预存区域
            {
                foreach (ResvRegion region in resvRegions)
                {
                    if (!regionMopDic.ContainsKey(region.RegionName))
                    {
                        MapOperation2 mapOp2 = new MapOperation2();
                        mapOp2.FillPolygon(region.Shape);
                        regionMopDic.Add(region.RegionName, mapOp2);
                    }
                }
            }
            else if (gmt != null)//单个区域
            {
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(gmt);
                regionMopDic.Add("当前区域", mapOp2);
            }
        }


        /// <summary>
        /// 定位所在网格
        /// </summary>
        /// <param name="iType"></param>
        /// <returns>返回区域名称</returns>
        private string getGrid(double longitude, double latitude)
        {
            foreach (string strKey in regionMopDic.Keys)
            {
                if (regionMopDic[strKey].CheckPointInRegion(longitude, latitude))
                {
                    return strKey;
                }
            }
            return null;
        }

        protected override void doWithDTData(TestPoint tp)
        {
            if (!isValidLTEData(tp))
                return;

            LTECell cell = tp.GetMainCell_LTE_FDD();
            if (cell == null)
            {
                addNotFindCellInfo(tp);
                return;
            }
            if (lteCellInfoDic.ContainsKey(cell))
            {
                LteFddCellInfo lcInfo = lteCellInfoDic[cell];
                lcInfo.calInfo(cell, tp);
            }
            else
            {
                LteFddCellInfo lcInfo = new LteFddCellInfo();
                lcInfo.Cellname = cell.Name;
                lcInfo.CellCode = cell.Code;
                lcInfo.Tac = cell.TAC;
                lcInfo.Eci = cell.ECI;
                lcInfo.Earfcn = cell.EARFCN;
                lcInfo.Pci = cell.PCI;
                lcInfo.CellID = cell.SCellID;
                lcInfo.IndoorOrOutdoor = cell.Type == LTEBTSType.Indoor ? "室内" : "室外";
                lcInfo.SampleCount = 1;
                lcInfo.Longitude=cell.Longitude;
                lcInfo.Latitude = cell.Latitude;
                lcInfo.MinRsrp = lcInfo.MaxRsrp = (float)tp["lte_fdd_RSRP"];
                lcInfo.AvgRsrp = (double)(float)tp["lte_fdd_RSRP"];
                lcInfo.MinRsrq = lcInfo.MaxRsrq = (float)tp["lte_fdd_RSRQ"];
                lcInfo.AvgRsrq = (double)(float)tp["lte_fdd_RSRQ"];
                lcInfo.MinDistance = lcInfo.MaxDistance = lcInfo.AvgDistance = Math.Round(MathFuncs.GetDistance(tp.Longitude, tp.Latitude, cell.Longitude, cell.Latitude), 2);
                lcInfo.MinSinr = lcInfo.MaxSinr = (float)tp["lte_fdd_SINR"];
                lcInfo.AvgSinr = (double)(float)tp["lte_fdd_SINR"];
                lcInfo.Grid = getGrid(cell.Longitude, cell.Latitude);

                lteCellInfoDic.Add(cell, lcInfo);
            }
        }

        private bool isValidLTEData(TestPoint tp)
        {
            float? rsrp = (float?)tp["lte_fdd_RSRP"];
            float? rsrq = (float?)tp["lte_fdd_RSRQ"];
            float? sinr = (float?)tp["lte_fdd_SINR"];
            byte? mnc = (byte?)tp["lte_fdd_MNC"];

            if (tp["lte_fdd_TAC"] == null || tp["lte_fdd_ECI"] == null
                || tp["lte_fdd_EARFCN"] == null || tp["lte_fdd_PCI"] == null
                || mnc == null || rsrp == null || rsrq == null || sinr == null)
            {
                return false;
            }
            else if (rsrp < -140 || rsrp > -10 || rsrq < -100 || rsrq > 100 || sinr < -100 || sinr > 100)
            {
                return false;
            }
            
            if ((tp.CarrierType == 2 && mnc != 1)
                || (tp.CarrierType == 3 && (mnc != 3 && mnc != 11)))
            {
                return false;
            }
            return true;
        }

        private void addNotFindCellInfo(TestPoint tp)
        {
            int iTAC = (int)(ushort)tp["lte_fdd_TAC"];
            int iEci = (int)tp["lte_fdd_ECI"];
            string strTAC_ECI = "" + iTAC + "_" + iEci;
            if (notFindCellInfoDic.ContainsKey(strTAC_ECI))
            {

                if (notFindCellInfoDic[strTAC_ECI].MaxRsrp < (float)tp["lte_fdd_RSRP"])
                {
                    notFindCellInfoDic[strTAC_ECI].MaxRsrp = (float)tp["lte_fdd_RSRP"];
                }
                if (notFindCellInfoDic[strTAC_ECI].MaxRsrq < (float)tp["lte_fdd_RSRQ"])
                {
                    notFindCellInfoDic[strTAC_ECI].MaxRsrq = (float)tp["lte_fdd_RSRQ"];
                }
                if (notFindCellInfoDic[strTAC_ECI].MaxSinr < (float)tp["lte_fdd_SINR"])
                {
                    notFindCellInfoDic[strTAC_ECI].MaxSinr = (float)tp["lte_fdd_SINR"];
                }

                if (notFindCellInfoDic[strTAC_ECI].MinRsrp > (float)tp["lte_fdd_RSRP"])
                {
                    notFindCellInfoDic[strTAC_ECI].MinRsrp = (float)tp["lte_fdd_RSRP"];
                }
                if (notFindCellInfoDic[strTAC_ECI].MinRsrq > (float)tp["lte_fdd_RSRQ"])
                {
                    notFindCellInfoDic[strTAC_ECI].MinRsrq = (float)tp["lte_fdd_RSRQ"];
                }
                if (notFindCellInfoDic[strTAC_ECI].MinSinr > (float)tp["lte_fdd_SINR"])
                {
                    notFindCellInfoDic[strTAC_ECI].MinSinr = (float)tp["lte_fdd_SINR"];
                }

                notFindCellInfoDic[strTAC_ECI].AvgRsrp = Math.Round((notFindCellInfoDic[strTAC_ECI].AvgRsrp * notFindCellInfoDic[strTAC_ECI].SampleCount
                    + (float)tp["lte_fdd_RSRP"]) / (notFindCellInfoDic[strTAC_ECI].SampleCount + 1), 2);
                notFindCellInfoDic[strTAC_ECI].AvgRsrq = Math.Round((notFindCellInfoDic[strTAC_ECI].AvgRsrq * notFindCellInfoDic[strTAC_ECI].SampleCount
                    + (float)tp["lte_fdd_RSRQ"]) / (notFindCellInfoDic[strTAC_ECI].SampleCount + 1), 2);
                notFindCellInfoDic[strTAC_ECI].AvgSinr = Math.Round((notFindCellInfoDic[strTAC_ECI].AvgSinr * notFindCellInfoDic[strTAC_ECI].SampleCount
                    + (float)tp["lte_fdd_SINR"]) / (notFindCellInfoDic[strTAC_ECI].SampleCount + 1), 2);

                notFindCellInfoDic[strTAC_ECI].SampleCount += 1;
            } 
            else
            {
                LteFddCellInfo lcInfo = new LteFddCellInfo();
                lcInfo.Cellname = strTAC_ECI;
                lcInfo.Tac = iTAC;
                lcInfo.Eci = iEci;
                lcInfo.Earfcn = (int)tp["lte_fdd_EARFCN"];
                lcInfo.Pci = (int)(short)tp["lte_fdd_PCI"];
                lcInfo.MinRsrp = lcInfo.MaxRsrp = (float)tp["lte_fdd_RSRP"];
                lcInfo.AvgRsrp = (double)(float)tp["lte_fdd_RSRP"];
                lcInfo.MinRsrq = lcInfo.MaxRsrq = (float)tp["lte_fdd_RSRQ"];
                lcInfo.AvgRsrq = (double)(float)tp["lte_fdd_RSRQ"];
                lcInfo.MinSinr = lcInfo.MaxSinr = (float)tp["lte_fdd_SINR"];
                lcInfo.AvgSinr = (double)(float)tp["lte_fdd_SINR"];
                lcInfo.SampleCount = 1;

                notFindCellInfoDic.Add(strTAC_ECI, lcInfo);
            }
        }
    }

    public class ZTDiyLteFddCellSetQueryByRegion_Volte : ZTDiyLteFddCellSetQueryByRegion
    {
        public ZTDiyLteFddCellSetQueryByRegion_Volte(MainModel mainModel)
            : base(mainModel)
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }
        public override string Name
        {
            get { return "VoLTE_FDD区域小区集"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30001, this.Name);
        }
        protected override bool isValidTestPoint(TestPoint tp)
        {
            if (ServiceTypes.Count > 0 && !ServiceTypes.Contains((ServiceType)tp.ServiceType)) return false;
            return true;
        }
    }

    public class LteFddCellInfo : LteCellInfo
    {
        /// <summary>
        /// 计算小区各参数值
        /// </summary>
        /// <param name="lteCell"></param>
        /// <param name="tp"></param>
        public override void calInfo(LTECell lteCell,TestPoint tp)
        {
            SampleCount++;
            if (MinRsrp > (float)tp["lte_fdd_RSRP"])
                MinRsrp = (float)tp["lte_fdd_RSRP"];
            else if (MaxRsrp < (float)tp["lte_fdd_RSRP"])
                MaxRsrp = (float)tp["lte_fdd_RSRP"];
            AvgRsrp = Math.Round((AvgRsrp * (SampleCount - 1) + (float)tp["lte_fdd_RSRP"]) / SampleCount, 2);

            if (MinRsrq > (float)tp["lte_fdd_RSRQ"])
                MinRsrq = (float)tp["lte_fdd_RSRQ"];
            else if (MaxRsrq < (float)tp["lte_fdd_RSRQ"])
                MaxRsrq = (float)tp["lte_fdd_RSRQ"];
            AvgRsrq = Math.Round((AvgRsrq * (SampleCount - 1) + (float)tp["lte_fdd_RSRQ"]) / SampleCount, 2);

            if (MinSinr > (float)tp["lte_fdd_SINR"])
                MinSinr = (float)tp["lte_fdd_SINR"];
            else if (MaxSinr < (float)tp["lte_fdd_SINR"])
                MaxSinr = (float)tp["lte_fdd_SINR"];
            AvgSinr = Math.Round((AvgSinr * (SampleCount - 1) + (float)tp["lte_fdd_SINR"]) / SampleCount, 2);

            double distance = Math.Round(MathFuncs.GetDistance(tp.Longitude, tp.Latitude, lteCell.Longitude, lteCell.Latitude), 2);
            if (MinDistance > distance)
                MinDistance = distance;
            else if (MaxDistance < distance)
                MaxDistance = distance;
            AvgDistance = Math.Round((AvgDistance * (SampleCount - 1) + distance) / SampleCount, 2);
        }
    }
}
