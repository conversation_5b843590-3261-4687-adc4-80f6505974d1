﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.Model.BaseInfo
{
    /// <summary>
    /// 统一以先delete再insert的方式更新数据库
    /// </summary>
    public class SubmitDataSourceRolesInfo : DiySqlMultiNonQuery
    {
        private readonly List<DataSourceRole> roles2Update;
        public SubmitDataSourceRolesInfo(MainModel mainModel, List<DataSourceRole> roles2Update)
            : base()
        {
            this.roles2Update = new List<DataSourceRole>();
            this.roles2Update.AddRange(roles2Update);
        }
        protected override void query()
        {
            Dictionary<int, List<DataSourceRole>> districtDic = new Dictionary<int, List<DataSourceRole>>();
            foreach (DataSourceRole role in roles2Update)
            {
                List<DataSourceRole> list;
                if (!districtDic.TryGetValue(role.DistrictID,out list))
                {
                    list = new List<DataSourceRole>();
                    districtDic[role.DistrictID] = list;
                }
                list.Add(role);
            }
            foreach (int id in districtDic.Keys)
            {
                this.roles2Update.Clear();
                this.roles2Update.AddRange(districtDic[id]);
                this.dbid = id;
                base.query();
            }
        }
        protected override string getSqlTextString()
        {
            StringBuilder strbDelete = new StringBuilder();
            StringBuilder strbInsert = new StringBuilder();
            foreach (DataSourceRole role in roles2Update)
            {
                strbInsert.AppendFormat(@"if not exists (select 1 from tb_cfg_static_dataSourceRole where iid = {0})
    insert into tb_cfg_static_dataSourceRole(iid, strname,strcomment) values ({0},'{1}','{2}');"
                    , role.ID, role.Name, role.Description);
                strbInsert.AppendLine();

                addCategoryModifySql(role, strbDelete, strbInsert);
                addAreaModifySql(role, strbDelete, strbInsert);
            }
            return strbDelete.ToString() + strbInsert.ToString();//要先删除后添加
        }
        private void addCategoryModifySql(DataSourceRole role, StringBuilder strbDelete, StringBuilder strbInsert)
        {
            foreach (KeyValuePair<string, Dictionary<int, bool>> kvp in role.CategoryNameModifyDic)
            {
                string strCategory = kvp.Key;
                string strDeleteSubIds = "";
                foreach (KeyValuePair<int, bool> subIdKvp in kvp.Value)
                {
                    int iCategorySubId = subIdKvp.Key;
                    //先记录下该角色该类型需要删除的subId，之后再一并删除（新增或修改的需要先删除后添加，以防有重复数据）
                    strDeleteSubIds = string.Format("{0},{1}", strDeleteSubIds, iCategorySubId);
                    if (strDeleteSubIds.Length > 6000)
                    {
                        addCategoryDeleteSql(strbDelete, role.ID, strCategory, ref strDeleteSubIds);
                    }

                    if (subIdKvp.Value)//新增或修改
                    {
                        strbInsert.AppendFormat(@"insert into tb_cfg_static_dataSourceRole_category (iRoleID, strCategoryName, iCategorySubItemID) values ({0},'{1}',{2});"
                            , role.ID, strCategory, iCategorySubId);
                        strbInsert.AppendLine();
                    }
                }

                if (strDeleteSubIds.Length > 1)
                {
                    addCategoryDeleteSql(strbDelete, role.ID, strCategory, ref strDeleteSubIds);
                }
            }
            role.CategoryNameModifyDic.Clear();
        }
        //一并删除该角色该类型需要删除的subId
        private void addCategoryDeleteSql(StringBuilder strbDelete, int roleId, string strCategory
            , ref string strDeleteSubIds)
        {
            strbDelete.AppendFormat(@"delete from tb_cfg_static_dataSourceRole_category 
where iRoleID = {0} and strCategoryName = '{1}' and iCategorySubItemID in ({2});"
                , roleId, strCategory, strDeleteSubIds.Remove(0, 1));
            strbDelete.AppendLine();

            strDeleteSubIds = "";
        }

        private void addAreaModifySql(DataSourceRole role, StringBuilder strbDelete, StringBuilder strbInsert)
        {
            if (role.HasAllAreaRightIncludeNew)
            {
                strbDelete.AppendFormat("delete from tb_cfg_static_dataSourceRole_area where iRoleID = {0};", role.ID);
                strbDelete.AppendLine();
                strbInsert.AppendFormat("insert into tb_cfg_static_dataSourceRole_area (iRoleID, iAreaTypeID, iAreaID) values ({0},{1},{2});"
                    , role.ID, -1, -1);
                strbInsert.AppendLine();
            }
            else
            {
                strbDelete.AppendFormat(@"delete from tb_cfg_static_dataSourceRole_area 
where iRoleID = {0} and iAreaTypeID = {1} and iAreaID = {2};", role.ID, -1, -1);
                strbDelete.AppendLine();

                dealAreaModifySql(role, strbDelete, strbInsert);
            }

            role.AreaModifyDic.Clear();
        }

        private void dealAreaModifySql(DataSourceRole role, StringBuilder strbDelete, StringBuilder strbInsert)
        {
            foreach (KeyValuePair<int, Dictionary<int, bool>> kvp in role.AreaModifyDic)
            {
                int iAreaTypeId = kvp.Key;
                string strDeleteAreaIds = "";
                foreach (KeyValuePair<int, bool> areaIdKvp in kvp.Value)
                {
                    int iareaId = areaIdKvp.Key;
                    //先记录下该角色该AreaType需要删除的AreaId，之后再一并删除（新增或修改的需要先删除后添加，以防有重复数据）
                    strDeleteAreaIds = string.Format("{0},{1}", strDeleteAreaIds, iareaId);
                    if (strDeleteAreaIds.Length > 6000)
                    {
                        addAreaDeleteSql(strbDelete, role.ID, iAreaTypeId, ref strDeleteAreaIds);
                    }

                    if (areaIdKvp.Value)//新增或修改
                    {
                        strbInsert.AppendFormat(@"insert into tb_cfg_static_dataSourceRole_area (iRoleID, iAreaTypeID, iAreaID) values ({0},{1},{2});"
                            , role.ID, iAreaTypeId, iareaId);
                        strbInsert.AppendLine();
                    }
                }

                if (strDeleteAreaIds.Length > 1)
                {
                    addAreaDeleteSql(strbDelete, role.ID, iAreaTypeId, ref strDeleteAreaIds);
                }
            }
        }

        //一并删除该角色该AreaType需要删除的AreaId
        private void addAreaDeleteSql(StringBuilder strbDelete, int roleId, int iAreaTypeId
            , ref string strDeleteAreaIds)
        {
            strbDelete.AppendFormat(@"delete from tb_cfg_static_dataSourceRole_area 
where iRoleID = {0} and iAreaTypeID = {1} and iAreaID in ({2});"
                , roleId, iAreaTypeId, strDeleteAreaIds.Remove(0, 1));
            strbDelete.AppendLine();

            strDeleteAreaIds = "";
        }

        public override string Name
        {
            get { return "更新数据源权限组"; }
        }
    }
}
