<?xml version="1.0"?>
<Configs>
  <Config name="Template">
    <Item name="Options" typeName="IDictionary">
      <Item typeName="String" key="WeakCvrNameCM">90覆盖率</Item>
      <Item typeName="String" key="WeakCvrExpCM">{100*(Mx_640117+Mx_64010A+Mx_640109+Mx_640108)/Mx_640101}</Item>
      <Item typeName="String" key="WeakCvrNameCU">90覆盖率</Item>
      <Item typeName="String" key="WeakCvrExpCU">{100*(Mx_640117+Mx_64010A+Mx_640109+Mx_640108)/Mx_640101}</Item>
      <Item typeName="String" key="WeakCvrNameCT">90覆盖率</Item>
      <Item typeName="String" key="WeakCvrExpCT">{100*Cx_6E061F/Cx_6E0621}</Item>
      <Item typeName="Double" key="WeakCvrValue">98</Item>
      <Item typeName="String" key="WeakCvrOperator">Less</Item>
      <Item typeName="String" key="PoorQualName">RxQual0-4级占比(%)</Item>
      <Item typeName="String" key="PoorQualExp">{(value10[57])*(1000*8)/((value4[57])*(1024*1024))}</Item>
      <Item typeName="Double" key="PoorQualValue">98</Item>
      <Item typeName="String" key="PoorQualOperator">Less</Item>
      <Item typeName="String" key="Events">6,7,8,10,82,907,908</Item>
    </Item>
  </Config>
</Configs>