﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Func
{
    class DiyMapRenderBaseQuery : DIYSQLBase
    {
        readonly MapRenderBaseForm frm;
        readonly CqtSettingConditionInfos cqtCondition;

        public DiyMapRenderBaseQuery(MainModel mainModel, CqtSettingConditionInfos condition, MapRenderBaseForm frm)
            : base(mainModel)
        {
            this.frm = frm;
            this.cqtCondition = condition;
            MainDB = true;
        }

        public override string Name
        {
            get { return "自定义"; }
        }

        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }

        readonly List<CqtRenderingBaseInfo> cqtRenderingInfos = new List<CqtRenderingBaseInfo>();
        public List<CqtRenderingBaseInfo> CQTRenderingInfos
        {
            get { return cqtRenderingInfos; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询自定义...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Show("正在查询自定义...", queryInThread, clientProxy);
                fireShowForm();
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override string getSqlTextString()
        {
            StringBuilder sb = new StringBuilder(string.Format(@"select a.time,a.mac,a.rssi,a.mac_x,a.mac_y,a.floorName,a.point_x,a.point_y,
a.loc_x,a.loc_y,a.tac,a.eci,a.rsrp,a.sinr,a.folderName from tb_bt_sample_all a where a.time between '{0}' and '{1}' ",
            cqtCondition.Period.BeginTime, cqtCondition.Period.EndTime));

            bool isFirst = true;
            foreach (string filter in cqtCondition.RenderingFilter)
            {
                if (isFirst)
                {
                    sb.Append("and a.floorName = '");
                    isFirst = false;
                }
                else
                {
                    sb.Append("or a.floorName = '");
                }
                sb.Append(filter);
                sb.Append("'");
            }

            return sb.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[15];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_Float;
            rType[3] = E_VType.E_Float;
            rType[4] = E_VType.E_Float;
            rType[5] = E_VType.E_String;
            rType[6] = E_VType.E_Float;
            rType[7] = E_VType.E_Float;
            rType[8] = E_VType.E_Float;
            rType[9] = E_VType.E_Float;
            rType[10] = E_VType.E_Int;
            rType[11] = E_VType.E_Int;
            rType[12] = E_VType.E_Float;
            rType[13] = E_VType.E_Float;
            rType[14] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CqtRenderingBaseInfo info = CqtRenderingBaseInfo.Fill(package.Content);
                    cqtRenderingInfos.Add(info);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        protected override void queryInThread(object o)
        {
            cqtRenderingInfos.Clear();
            base.queryInThread(o);
            WaitBox.Close();
        }

        private void fireShowForm()
        {
            frm.DealDataAfterQuery(cqtRenderingInfos);
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class CqtRenderingBaseInfo 
    {
        public string Time { get; set; } = "";
        public string MAC { get; set; } = "";
        public float RSSI { get; set; }
        public double Mac_x { get; set; }
        public double Mac_y { get; set; }
        public string FloorName { get; set; } = "";
        public double Point_x { get; set; }
        public double Point_y { get; set; }
        public double Loc_x { get; set; }
        public double Loc_y { get; set; }
        public int TAC { get; set; }
        public int ECI { get; set; }
        public float RSRP { get; set; }
        public float SINR { get; set; }
        public string FolderName { get; set; } = "";
        public double RealLongitude { get; set; }
        public double RealLatitude { get; set; }
        public bool Selected { get; set; } = false;

        public static CqtRenderingBaseInfo Fill(Content content)
        {
            CqtRenderingBaseInfo info = new CqtRenderingBaseInfo();
            info.Time = content.GetParamString();
            info.MAC = content.GetParamString();
            info.RSSI = content.GetParamFloat();
            info.Mac_x = content.GetParamFloat();
            info.Mac_y = content.GetParamFloat();
            info.FloorName = content.GetParamString();
            info.Point_x = content.GetParamFloat();
            info.Point_y = content.GetParamFloat();
            info.Loc_x = content.GetParamFloat();
            info.Loc_y = content.GetParamFloat();
            info.TAC = content.GetParamInt();
            info.ECI = content.GetParamInt();
            info.RSRP = content.GetParamFloat();
            info.SINR = content.GetParamFloat();
            info.FolderName = content.GetParamString();
            return info;
        }
    }
}
