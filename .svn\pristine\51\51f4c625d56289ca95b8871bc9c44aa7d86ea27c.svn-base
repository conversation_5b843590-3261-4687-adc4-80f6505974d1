using System;
using System.Collections;
using System.Drawing;
using System.Runtime.Serialization;

namespace MasterCom.RAMS.Func.MeshGridOf3D
{
    /// <summary>
    /// A class provide Arraylist to handler the data pint,
    /// and calculate the max and min value of data points
    /// </summary>
    /// 
    [Serializable]
    public class DataSeries : ISerializable
    {
        #region Constructors

        public DataSeries()
        {
            LineStyle = new LineStyle();
            PointList = new ArrayList();
        }
        #endregion

        #region Properties

        public Point4[,,] Point4Array { get; set; }

        public Point3[,] PointArray { get; set; }

        public int XNumber { get; set; } = 10;

        public int YNumber { get; set; } = 10;

        public int ZNumber { get; set; } = 10;

        public float XSpacing { get; set; } = 1;

        public float YSpacing { get; set; } = 1;

        public float ZSpacing { get; set; } = 1;

        public float XDataMin { get; set; } = -5;

        public float YDataMin { get; set; } = -5;

        public float ZZDataMin { get; set; } = -5;

        public LineStyle LineStyle { get; set; }

        public ArrayList PointList { get; set; }
        #endregion

        #region Serialization

        /// <summary>
        /// Constructor for deserializing objects
        /// </summary>
        /// <param name="info">A <see cref="SerializationInfo"/> instance that defines the serialized data
        /// </param>
        /// <param name="context">A <see cref="StreamingContext"/> instance that contains the serialized data
        /// </param>
        /// 
        protected DataSeries(SerializationInfo info, StreamingContext context)
        {
            PointList = (ArrayList)info.GetValue("pointList", typeof(ArrayList));
            LineStyle = (LineStyle)info.GetValue("lineStyle", typeof(LineStyle));
            XDataMin = info.GetSingle("xdataMin");
            YDataMin = info.GetSingle("ydataMin");
            ZZDataMin = info.GetSingle("zzdataMin");
            XSpacing = info.GetSingle("xSpacing");
            YSpacing = info.GetSingle("ySpacing");
            ZSpacing = info.GetSingle("zSpacing");
            XNumber = info.GetInt32("xNumber");
            YNumber = info.GetInt32("yNumber");
            ZNumber = info.GetInt32("zNumber");
        }
        /// <summary>
        /// Populates a <see cref="SerializationInfo"/> instance with the data needed to serialize the target object
        /// </summary>
        /// <param name="info">A <see cref="SerializationInfo"/> instance that defines the serialized data</param>
        /// <param name="context">A <see cref="StreamingContext"/> instance that contains the serialized data</param>
        public virtual void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            info.AddValue("pointList", PointList);
            info.AddValue("lineStyle", LineStyle);
            info.AddValue("xdataMin", XDataMin);
            info.AddValue("ydataMin", YDataMin);
            info.AddValue("zzdataMin", ZZDataMin);
            info.AddValue("xSpacing", XSpacing);
            info.AddValue("ySpacing", YSpacing);
            info.AddValue("zSpacing", ZSpacing);
            info.AddValue("xNumber", XNumber);
            info.AddValue("yNumber", YNumber);
            info.AddValue("zNumber", ZNumber);
        }
        #endregion

        #region Methods

        public void AddPoint(Point3 pt)
        {
            PointList.Add(pt);
        }

        public float ZDataMin()
        {
            float zmin = 0;
            for (int i = 0; i < PointArray.GetLength(0); i++)
            {
                for (int j = 0; j < PointArray.GetLength(1); j++)
                {
                    zmin = Math.Min(zmin, PointArray[i, j].Z);
                }
            }
            return zmin;
        }

        public float ZDataMax()
        {
            float zmax = 0;
            for (int i = 0; i < PointArray.GetLength(0); i++)
            {
                for (int j = 0; j < PointArray.GetLength(1); j++)
                {
                    zmax = Math.Max(zmax, PointArray[i, j].Z);
                }
            }
            return zmax;
        }

        public float VDataMin()
        {
            float vmin = 0;
            for (int i = 0; i < Point4Array.GetLength(0); i++)
            {
                for (int j = 0; j < Point4Array.GetLength(1); j++)
                {
                    for (int k = 0; k < Point4Array.GetLength(2); k++)
                    {
                        vmin = Math.Min(vmin, Point4Array[i, j, k]._mV);
                    }
                }
            }
            return vmin;
        }

        public float VDataMax()
        {
            float vmax = 0;
            for (int i = 0; i < Point4Array.GetLength(0); i++)
            {
                for (int j = 0; j < Point4Array.GetLength(1); j++)
                {
                    for (int k = 0; k < Point4Array.GetLength(2); k++)
                    {
                        vmax = Math.Max(vmax, Point4Array[i, j, k]._mV);
                    }
                }
            }
            return vmax;
        }
        #endregion
    }
}

