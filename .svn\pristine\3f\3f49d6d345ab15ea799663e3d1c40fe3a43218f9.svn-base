﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    class NRPingPangAnalyzer
    {
        public NRPingPangAnalyzer()
        {
            statLteEvtIDs = NREventHelper.HandoverHelper.GetIncludeLTEHandover();
            statNREvtIDs = NREventHelper.HandoverHelper.GetIncludeNRHandover();
        }

        protected List<int> statNREvtIDs = null;
        protected List<int> statLteEvtIDs = null;
        public NRPingPangCondition PingPangCond { get; set; } = new NRPingPangCondition();
        public List<NRPingPangFile> Results = new List<NRPingPangFile>();

        public void Analyze(List<DTFileDataManager> fileManagers)
        {
            Results.Clear();
            foreach (DTFileDataManager dtFile in fileManagers)
            {
                Analyze(dtFile);
            }
            SetSN();
        }

        public void Init()
        {
            Results.Clear();
        }

        public void Analyze(DTFileDataManager dtFile)
        {
            NRPingPangFile ppFile = new NRPingPangFile(dtFile);
            
            dealEvts(dtFile, ppFile, statNREvtIDs, true);
            if (PingPangCond.IsAnaLte)
            {
                NREventHelper.HandoverHelper.FilterHandoverEvents(dtFile.Events);
                dealEvts(dtFile, ppFile, statLteEvtIDs, false);
            }

            if (ppFile.PairCount > 0)
            {
                ppFile.CalcResult();
                Results.Add(ppFile);
            }
        }

        private void dealEvts(DTFileDataManager dtFile, NRPingPangFile ppFile, List<int> statEvtIDs, bool isNREvt)
        {
            Event lastEvt = null;
            foreach (Event evt in dtFile.Events)
            {
                if (!statEvtIDs.Contains(evt.ID))
                {
                    continue;
                }

                if (lastEvt == null)
                {
                    lastEvt = evt;
                    continue;
                }


                EventCellInfo lastEvtCellInfo = new EventCellInfo(lastEvt);
                EventCellInfo evtCellInfo = new EventCellInfo(evt);
                bool isPingPang = JudagePingPangHandover(lastEvtCellInfo, evtCellInfo, isNREvt);
                if (isPingPang)
                {
                    addNRPingPangPairs(dtFile, ppFile, lastEvtCellInfo, evtCellInfo);
                }

                lastEvt = evt;
            }
        }

        private bool JudagePingPangHandover(EventCellInfo lastEvtCellInfo, EventCellInfo evtCellInfo, bool isNREvt)
        {
            bool isPingPang = false;
            if (isNREvt)
            {
                if (lastEvtCellInfo.JudgePingPangHandover(evtCellInfo))
                {
                    isPingPang = true;
                }
            }
            else
            {
                if (lastEvtCellInfo.JudgeLTEPingPangHandover(evtCellInfo))
                {
                    isPingPang = true;
                }
            }
            return isPingPang;
        }

        public class EventCellInfo
        {
            public EventCellInfo(Event evt)
            {
                Evt = evt;
                CellInfo = NREventHelper.HandoverHelper.GetHandOverCellInfo(evt);
                Type = NREventHelper.HandoverHelper.GetHandoverType(evt.ID, true);
            }

            public NRHandOverType Type { get; set; }
            public Event Evt { get; set; }
            public NRHandoverEventHelper.HandOverCellInfo CellInfo { get; set; }

            public bool JudgePingPangHandover(EventCellInfo otherEvtCellInfo)
            {
                bool isPingPang = false;
                if (CellInfo.NRSrcCell.ARFCN == otherEvtCellInfo.CellInfo.NRTarCell.ARFCN
                  && CellInfo.NRSrcCell.PCI == otherEvtCellInfo.CellInfo.NRTarCell.PCI
                  && CellInfo.NRTarCell.ARFCN == otherEvtCellInfo.CellInfo.NRSrcCell.ARFCN
                  && CellInfo.NRTarCell.PCI == otherEvtCellInfo.CellInfo.NRSrcCell.PCI
                  && (CellInfo.NRSrcCell.ARFCN != CellInfo.NRTarCell.ARFCN
                        || CellInfo.NRSrcCell.PCI != CellInfo.NRTarCell.PCI))
                {
                    isPingPang = true;
                }
                return isPingPang;
            }

            public bool JudgeLTEPingPangHandover(EventCellInfo otherEvtCellInfo)
            {
                bool isPingPang = false;
                if (CellInfo.LTESrcCell.ARFCN == otherEvtCellInfo.CellInfo.LTETarCell.ARFCN
                    && CellInfo.LTESrcCell.PCI == otherEvtCellInfo.CellInfo.LTETarCell.PCI
                    && CellInfo.LTETarCell.ARFCN == otherEvtCellInfo.CellInfo.LTESrcCell.ARFCN
                    && CellInfo.LTETarCell.PCI == otherEvtCellInfo.CellInfo.LTESrcCell.PCI
                    && (CellInfo.LTESrcCell.ARFCN != CellInfo.LTETarCell.ARFCN
                        || CellInfo.LTESrcCell.PCI != CellInfo.LTETarCell.PCI))
                {
                    isPingPang = true;
                }
                return isPingPang;
            }
        }

        private void addNRPingPangPairs(DTFileDataManager dtFile, NRPingPangFile ppFile, EventCellInfo lastEvtCellInfo, EventCellInfo evtCellInfo)
        {
            TimeSpan timeSpan = evtCellInfo.Evt.DateTime - lastEvtCellInfo.Evt.DateTime;
            if (timeSpan.TotalSeconds <= PingPangCond.TimeLimit)
            {
                bool isSpeedValid = true;
                if (PingPangCond.IsLimitSpeed)
                {
                    //车也不是直线行驶,这样判断车速应该有问题
                    double curSpeed = MathFuncs.GetDistance(evtCellInfo.Evt.Longitude, evtCellInfo.Evt.Latitude,
                        lastEvtCellInfo.Evt.Longitude, lastEvtCellInfo.Evt.Latitude) / Math.Abs(evtCellInfo.Evt.Time - lastEvtCellInfo.Evt.Time) * 3.6;
                    isSpeedValid = curSpeed >= PingPangCond.SpeedLimitMin && curSpeed <= PingPangCond.SpeedLimitMax;
                }

                if (isSpeedValid)
                {
                    NRPingPangEvent evtA = new NRPingPangEvent(dtFile, lastEvtCellInfo);
                    NRPingPangEvent evtB = new NRPingPangEvent(dtFile, evtCellInfo);
                    NRPingPangPair pair = new NRPingPangPair(evtA, evtB);
                    ppFile.Pairs.Add(pair);
                }
            }
        }

        public void SetSN()
        {
            int fileSN = 0;
            foreach (NRPingPangFile ppFile in Results)
            {
                ppFile.SN = ++fileSN;

                int pairSN = 0;
                foreach (NRPingPangPair ppPair in ppFile.Pairs)
                {
                    ppPair.SN = ++pairSN;

                    int evtSN = 0;
                    foreach (NRPingPangEvent ppEvt in ppPair.Events)
                    {
                        ppEvt.SN = ++evtSN;
                    }
                }

                int cellSN = 0;
                foreach (NRPingPangICell ppCell in ppFile.Cells)
                {
                    ppCell.SN = ++cellSN;
                }
            }
        }
    }
}
