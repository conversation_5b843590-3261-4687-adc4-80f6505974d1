﻿using MapWinGIS;
using MasterCom.MControls;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class RoadQualAnaLayer : LayerBase
    {
        public List<RoadPartAnaInfo> SelRoadParts { get; set; } = new List<RoadPartAnaInfo>();

        private readonly List<RoadPartAnaInfo> roadPartInfoList_CurRegion = new List<RoadPartAnaInfo>();
        private Dictionary<int, RoadPartAnaInfo> roadPartAnaInfoDic;
        private Dictionary<string, RoadLabelQualAnaInfo> roadInfoDic;
        private bool drawRoadLabel;
        public event EventHandler SelectRoadPartsChanged;

        public RoadQualAnaLayer()
            : base("道路质量分析")
        {
        }

        public void SetMatrixAndColor(Dictionary<int, RoadPartAnaInfo> roadPartAnaInfoDic, RoadQualAnaColorModeItem colorMode
            , Dictionary<string, RoadLabelQualAnaInfo> roadInfoDic, bool drawRoadLabel)
        {
            this.roadPartAnaInfoDic = roadPartAnaInfoDic;
            this.roadInfoDic = roadInfoDic;
            this.drawRoadLabel = drawRoadLabel;

            ResetLayerColor(colorMode);
        }

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            drawRoadBitMap(clientRect, graphics);
            drawSelRoadPart(graphics);
        }
        private void drawRoadBitMap(Rectangle clientRect, Graphics graphics)
        {
            if (roadInfoDic == null || roadInfoDic.Count <= 0)
            {
                return;
            }

            DbRect viewRect;
            gisAdapter.FromDisplay(clientRect, out viewRect);

            roadPartInfoList_CurRegion.Clear();
            foreach (RoadLabelQualAnaInfo roadInfo in roadInfoDic.Values)
            {
                if (roadInfo.RoadPartAnaInfoDic.Count > 0)
                {
                    DbRect roadLabelDbRect = roadInfo.RoadLabelPolygon.Bounds;
                    if (roadLabelDbRect.Within(viewRect))
                    {
                        roadPartInfoList_CurRegion.AddRange(roadInfo.RoadPartAnaInfoDic.Values);

                        drawRoadName(clientRect, graphics, roadInfo, roadLabelDbRect);

                        drawRoadPart(graphics, roadInfo);
                    }
                }
            }
        }

        private void drawRoadName(Rectangle clientRect, Graphics graphics, RoadLabelQualAnaInfo roadInfo, DbRect roadLabelDbRect)
        {
            //画路段名
            if (drawRoadLabel)
            {
                Font fontStyle = new System.Drawing.Font(new FontFamily("宋体"), 10, FontStyle.Regular);
                PointF centerPt;
                gisAdapter.ToDisplay(new DbPoint(roadLabelDbRect.x2, roadLabelDbRect.y2), out centerPt);

                if (clientRect.Contains((int)centerPt.X, (int)centerPt.Y) && mapScale <= 30000)
                {
                    graphics.DrawString(roadInfo.RoadLabel, fontStyle, Brushes.Black, centerPt);
                }
            }
        }

        private void drawRoadPart(Graphics graphics, RoadLabelQualAnaInfo roadInfo)
        {
            //根据指标渲染小路段
            foreach (RoadPartAnaInfo roadPartInfo in roadInfo.RoadPartAnaInfoDic.Values)
            {
                if (roadPartInfo.RoadPartBaseInfo.DbPointList != null)
                {
                    DbPoint[] roadDps = roadPartInfo.RoadPartBaseInfo.DbPointList.ToArray();
                    PointF[] points;
                    gisAdapter.ToDisplay(roadDps, out points);
                    Brush brush = new SolidBrush(roadPartInfo.RoadPartCurColor);
                    graphics.FillPolygon(brush, points);
                }
            }
        }

        internal int Select(MapOperation2 mop2)
        {
            List<RoadPartAnaInfo> curSelRoadParts = null;
            Select(mop2, ref curSelRoadParts);
            SelRoadParts = curSelRoadParts;
            if (curSelRoadParts != null && curSelRoadParts.Count > 0)
            {
                if (SelectRoadPartsChanged != null)
                {
                    SelectRoadPartsChanged(this, EventArgs.Empty);
                }
                return 1;
            }
            return 0;
        }
        public void Select(MapOperation2 mop2, ref List<RoadPartAnaInfo> roadPartInfos)
        {
            roadPartInfos = null;
            if (IsVisible)
            {
                if (roadPartAnaInfoDic == null)
                {
                    roadPartInfos = null;
                    return;
                }
                roadPartInfos = new List<RoadPartAnaInfo>();
                DbPoint clickCenter = mop2.GetRegion().Bounds.Center();
                foreach (RoadPartAnaInfo item in roadPartInfoList_CurRegion)
                {
                    if (item.RoadPartPolygon.Contains(clickCenter.x, clickCenter.y))
                    {
                        roadPartInfos.Add(item);
                    }
                }
            }
        }
        private void drawSelRoadPart(Graphics graphics)
        {
            if (SelRoadParts == null)
            {
                return;
            }

            mainModel.SelectedCells.Clear();
            mainModel.SelectedLTECells.Clear();
            foreach (RoadPartAnaInfo selectItem in SelRoadParts)
            {
                RoadPartAreaBaseInfo selRoadBaseInfo = selectItem.RoadPartBaseInfo;

                DbPoint[] roadDps = selRoadBaseInfo.DbPointList.ToArray();
                PointF[] points;
                gisAdapter.ToDisplay(roadDps, out points);
                Pen pen = new Pen(Color.Red);
                graphics.DrawPolygon(pen, points);

                if (selectItem.RoadPartCellList != null && selectItem.RoadPartCellList.Count > 0)
                {
                    PointF roadCenterPntF;
                    gisAdapter.ToDisplay(new DbPoint(selRoadBaseInfo.CenterLongitude, selRoadBaseInfo.CenterLatitude), out roadCenterPntF);
                    addSelectedCells(graphics, selectItem, roadCenterPntF);
                }
            }
        }

        private void addSelectedCells(Graphics graphics, RoadPartAnaInfo selectItem, PointF roadCenterPntF)
        {
            foreach (RoadPartCellKpiInfo roadCellInfo in selectItem.RoadPartCellList)
            {
                if (!(roadCellInfo.SrcCell is UnknowCell))
                {
                    PointF cellPntF;
                    gisAdapter.ToDisplay(new DbPoint(roadCellInfo.SrcCell.EndPointLongitude, roadCellInfo.SrcCell.EndPointLatitude), out cellPntF);
                    graphics.DrawLine(Pens.Blue, roadCenterPntF.X, roadCenterPntF.Y, cellPntF.X, cellPntF.Y);

                    if (roadCellInfo.SrcCell is LTECell)
                    {
                        mainModel.SelectedLTECells.Add(roadCellInfo.SrcCell as LTECell);
                    }
                    else if (roadCellInfo.SrcCell is Cell)
                    {
                        mainModel.SelectedCells.Add(roadCellInfo.SrcCell as Cell);
                    }
                }
            }
        }

        public void ResetLayerColor(RoadQualAnaColorModeItem colormode)
        {
            if (colormode == null)
            {
                return;
            }

            RoadQualAnaCfgManager roadAnaCfg = RoadQualAnaCfgManager.GetInstance();

            foreach (RoadLabelQualAnaInfo roadLabelInfo in roadInfoDic.Values)
            {
                if (colormode.Formula == roadAnaCfg.Formula_AbnomalPer)
                {
                    roadLabelInfo.RoadAbnormalColor = colormode.GetColor((float?)roadLabelInfo.RoadAbnormalPer);
                }
                foreach (RoadPartAnaInfo roadPartInfo in roadLabelInfo.RoadPartAnaInfoDic.Values)
                {
                    roadPartInfo.RoadAbnormalColor = roadLabelInfo.RoadAbnormalColor;
                    if (colormode.Formula == roadAnaCfg.Formula_AbnomalPer)
                    {
                        roadPartInfo.RoadPartCurColor = roadLabelInfo.RoadAbnormalColor;
                    }
                    else
                    {
                        roadPartInfo.RoadPartCurColor = colormode.GetColor(roadAnaCfg, roadPartInfo.KpiData);
                    }
                }
            }
        }

        public void ClearData()
        {
            if (SelRoadParts != null)
            {
                SelRoadParts.Clear();
            }
            if (roadPartInfoList_CurRegion != null)
            {
                roadPartInfoList_CurRegion.Clear();
            }
            if (roadPartAnaInfoDic != null)
            {
                roadPartAnaInfoDic.Clear();
            }
            if (roadInfoDic != null)
            {
                roadInfoDic.Clear();
            }
        }
        public override void LayerDispose()
        {
            this.ClearData();
            base.LayerDispose();
        }
    }

    public class RoadQualAnaColorModeItem
    {
        private readonly Color EmptyColor = Color.Empty;
        public RoadQualAnaColorModeItem()
        { 
        }
        public RoadQualAnaColorModeItem(string name, string formula, float minValue, float maxValue)
        {
            this.Name = name;
            this.Formula = formula;
            this.MinR = minValue;
            this.MaxR = maxValue;
        }
        
        public string Name { get; set; }
        public string Formula { get; set; }
        public float MinR { get; set; }
        public float MaxR { get; set; }

        public List<ColorRange> ColorRanges { get; set; } = new List<ColorRange>();//Range着色模式

        public override string ToString()
        {
            return Name;
        }
        public Color GetColor(RoadQualAnaCfgManager roadAnaCfg, RoadQualKpiDataHub kpiInfo)
        {
            try
            {
                float? fValue = null;
                if (this.Formula == roadAnaCfg.Formula_LteRsrp)
                {
                    fValue = (float?)kpiInfo.RsrpInfo.KpiAvgValue;
                }
                else if (this.Formula == roadAnaCfg.Formula_LteSinr)
                {
                    fValue = (float?)kpiInfo.SinrInfo.KpiAvgValue;
                }
                else if (this.Formula == roadAnaCfg.Formula_LteCoverRate)
                {
                    fValue = (float?)kpiInfo.LteCoverRateInfo.Rate;
                }
                else if (this.Formula == roadAnaCfg.Formula_GsmRxlev)
                {
                    fValue = (float?)kpiInfo.RxlevInfo.KpiAvgValue;
                }
                return GetColor(fValue);
            }
            catch
            {
                return Color.Black;
            }
        }
        public Color GetColor(float? value)
        {
            int count = ColorRanges.Count;
            if (count == 0)
            {
                return EmptyColor;
            }
            if (value == null || float.IsNaN((float)value))
            {
                return Color.Gray;
            }

            for (int i = 0; i < count; i++)
            {
                ColorRange cr = ColorRanges[i];
                if (value >= cr.minValue && value < cr.maxValue)
                {
                    if (cr.visible)
                    {
                        return cr.color;
                    }
                    else
                    {
                        return EmptyColor;
                    }

                }
            }
            if (value == ColorRanges[count - 1].maxValue)
            {
                return ColorRanges[count - 1].color;
            }
            return EmptyColor;
        }
        
        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Name"] = Name;
                param["MinR"] = MinR;
                param["MaxR"] = MaxR;
                param["Formula"] = Formula;
                List<object> colorParams = new List<object>();
                param["ColorRanges"] = colorParams;
                foreach (ColorRange cr in ColorRanges)
                {
                    colorParams.Add(cr.Param);
                }
                return param;
            }
            set
            {
                Name = (String)value["Name"];
                MinR = (float)value["MinR"];
                MaxR = (float)value["MaxR"];
                Formula = (String)value["Formula"];
                ColorRanges.Clear();
                List<object> colorParams = (List<object>)value["ColorRanges"];
                foreach (object o in colorParams)
                {
                    Dictionary<string, object> colorParam = (Dictionary<string, object>)o;
                    ColorRange cr = new ColorRange();
                    cr.Param = colorParam;
                    ColorRanges.Add(cr);
                }
            }
        }
    }
}
