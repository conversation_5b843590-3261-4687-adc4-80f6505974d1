﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class TDSInterfereCpiConditionDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.lblShadow = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditfShadowThr = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditRelativeThr = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditfInterfireCoefficienThr = new DevExpress.XtraEditors.SpinEdit();
            this.simpleButton1 = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButton2 = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.cbxThresholdType = new System.Windows.Forms.ComboBox();
            this.lblAbsThr = new DevExpress.XtraEditors.LabelControl();
            this.spinEditAbsThr = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.cbxDefinedCell = new DevExpress.XtraEditors.CheckEdit();
            this.btnImport = new DevExpress.XtraEditors.SimpleButton();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.cbxBasicGroup = new System.Windows.Forms.CheckBox();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditfShadowThr.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRelativeThr.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditfInterfireCoefficienThr.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditAbsThr.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxDefinedCell.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.SuspendLayout();
            // 
            // lblShadow
            // 
            this.lblShadow.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblShadow.Appearance.Options.UseFont = true;
            this.lblShadow.Location = new System.Drawing.Point(36, 30);
            this.lblShadow.Name = "lblShadow";
            this.lblShadow.Size = new System.Drawing.Size(84, 12);
            this.lblShadow.TabIndex = 0;
            this.lblShadow.Text = "阴影余量衰落≤";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(205, 30);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(12, 12);
            this.labelControl2.TabIndex = 1;
            this.labelControl2.Text = "dB";
            // 
            // spinEditfShadowThr
            // 
            this.spinEditfShadowThr.EditValue = new decimal(new int[] {
            67,
            0,
            0,
            65536});
            this.spinEditfShadowThr.Location = new System.Drawing.Point(129, 27);
            this.spinEditfShadowThr.Name = "spinEditfShadowThr";
            this.spinEditfShadowThr.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditfShadowThr.Properties.Appearance.Options.UseFont = true;
            this.spinEditfShadowThr.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditfShadowThr.Size = new System.Drawing.Size(70, 20);
            this.spinEditfShadowThr.TabIndex = 2;
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(24, 66);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(108, 12);
            this.labelControl3.TabIndex = 3;
            this.labelControl3.Text = "干扰小区RSCP差值≤";
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(24, 60);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(96, 12);
            this.labelControl4.TabIndex = 4;
            this.labelControl4.Text = "扰码相关性系数≥";
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(214, 68);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(12, 12);
            this.labelControl5.TabIndex = 5;
            this.labelControl5.Text = "dB";
            // 
            // spinEditRelativeThr
            // 
            this.spinEditRelativeThr.EditValue = new decimal(new int[] {
            6,
            0,
            0,
            0});
            this.spinEditRelativeThr.Location = new System.Drawing.Point(138, 63);
            this.spinEditRelativeThr.Name = "spinEditRelativeThr";
            this.spinEditRelativeThr.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditRelativeThr.Properties.Appearance.Options.UseFont = true;
            this.spinEditRelativeThr.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditRelativeThr.Size = new System.Drawing.Size(70, 20);
            this.spinEditRelativeThr.TabIndex = 7;
            // 
            // spinEditfInterfireCoefficienThr
            // 
            this.spinEditfInterfireCoefficienThr.EditValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.spinEditfInterfireCoefficienThr.Location = new System.Drawing.Point(129, 57);
            this.spinEditfInterfireCoefficienThr.Name = "spinEditfInterfireCoefficienThr";
            this.spinEditfInterfireCoefficienThr.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditfInterfireCoefficienThr.Properties.Appearance.Options.UseFont = true;
            this.spinEditfInterfireCoefficienThr.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditfInterfireCoefficienThr.Size = new System.Drawing.Size(70, 20);
            this.spinEditfInterfireCoefficienThr.TabIndex = 8;
            // 
            // simpleButton1
            // 
            this.simpleButton1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButton1.Appearance.Options.UseFont = true;
            this.simpleButton1.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.simpleButton1.Location = new System.Drawing.Point(136, 406);
            this.simpleButton1.Name = "simpleButton1";
            this.simpleButton1.Size = new System.Drawing.Size(75, 23);
            this.simpleButton1.TabIndex = 9;
            this.simpleButton1.Text = "确定";
            // 
            // simpleButton2
            // 
            this.simpleButton2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButton2.Appearance.Options.UseFont = true;
            this.simpleButton2.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.simpleButton2.Location = new System.Drawing.Point(221, 406);
            this.simpleButton2.Name = "simpleButton2";
            this.simpleButton2.Size = new System.Drawing.Size(75, 23);
            this.simpleButton2.TabIndex = 10;
            this.simpleButton2.Text = "取消";
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Location = new System.Drawing.Point(24, 31);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(66, 12);
            this.labelControl6.TabIndex = 11;
            this.labelControl6.Text = "覆盖带类型 ";
            // 
            // cbxThresholdType
            // 
            this.cbxThresholdType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxThresholdType.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbxThresholdType.FormattingEnabled = true;
            this.cbxThresholdType.Location = new System.Drawing.Point(98, 26);
            this.cbxThresholdType.Name = "cbxThresholdType";
            this.cbxThresholdType.Size = new System.Drawing.Size(130, 20);
            this.cbxThresholdType.TabIndex = 12;
            this.cbxThresholdType.SelectedIndexChanged += new System.EventHandler(this.cbxThresholdType_SelectedIndexChanged);
            // 
            // lblAbsThr
            // 
            this.lblAbsThr.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblAbsThr.Appearance.Options.UseFont = true;
            this.lblAbsThr.Location = new System.Drawing.Point(36, 96);
            this.lblAbsThr.Name = "lblAbsThr";
            this.lblAbsThr.Size = new System.Drawing.Size(96, 12);
            this.lblAbsThr.TabIndex = 13;
            this.lblAbsThr.Text = "RSCP绝对门限值≥";
            // 
            // spinEditAbsThr
            // 
            this.spinEditAbsThr.EditValue = new decimal(new int[] {
            80,
            0,
            0,
            -2147483648});
            this.spinEditAbsThr.Location = new System.Drawing.Point(138, 92);
            this.spinEditAbsThr.Name = "spinEditAbsThr";
            this.spinEditAbsThr.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditAbsThr.Properties.Appearance.Options.UseFont = true;
            this.spinEditAbsThr.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditAbsThr.Properties.MinValue = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.spinEditAbsThr.Size = new System.Drawing.Size(70, 20);
            this.spinEditAbsThr.TabIndex = 14;
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl8.Appearance.Options.UseFont = true;
            this.labelControl8.Location = new System.Drawing.Point(214, 96);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(18, 12);
            this.labelControl8.TabIndex = 15;
            this.labelControl8.Text = "dBm";
            // 
            // cbxDefinedCell
            // 
            this.cbxDefinedCell.Location = new System.Drawing.Point(27, 30);
            this.cbxDefinedCell.Name = "cbxDefinedCell";
            this.cbxDefinedCell.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbxDefinedCell.Properties.Appearance.Options.UseFont = true;
            this.cbxDefinedCell.Properties.Caption = "使用特定小区工参";
            this.cbxDefinedCell.Size = new System.Drawing.Size(118, 19);
            this.cbxDefinedCell.TabIndex = 21;
            this.cbxDefinedCell.CheckedChanged += new System.EventHandler(this.checkEdit1_CheckedChanged);
            // 
            // btnImport
            // 
            this.btnImport.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnImport.Appearance.Options.UseFont = true;
            this.btnImport.Enabled = false;
            this.btnImport.Image = global::MasterCom.RAMS.Properties.Resources.xls;
            this.btnImport.Location = new System.Drawing.Point(162, 27);
            this.btnImport.Name = "btnImport";
            this.btnImport.Size = new System.Drawing.Size(75, 23);
            this.btnImport.TabIndex = 20;
            this.btnImport.Text = "导入";
            this.btnImport.Click += new System.EventHandler(this.btnImport_Click);
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.cbxDefinedCell);
            this.groupBox1.Controls.Add(this.btnImport);
            this.groupBox1.Location = new System.Drawing.Point(30, 308);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(266, 70);
            this.groupBox1.TabIndex = 22;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "导入工参";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.labelControl6);
            this.groupBox2.Controls.Add(this.cbxThresholdType);
            this.groupBox2.Controls.Add(this.labelControl8);
            this.groupBox2.Controls.Add(this.lblAbsThr);
            this.groupBox2.Controls.Add(this.spinEditAbsThr);
            this.groupBox2.Controls.Add(this.labelControl3);
            this.groupBox2.Controls.Add(this.labelControl5);
            this.groupBox2.Controls.Add(this.spinEditRelativeThr);
            this.groupBox2.Location = new System.Drawing.Point(30, 12);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(266, 129);
            this.groupBox2.TabIndex = 23;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "覆盖带设置";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.cbxBasicGroup);
            this.groupBox3.Controls.Add(this.lblShadow);
            this.groupBox3.Controls.Add(this.labelControl2);
            this.groupBox3.Controls.Add(this.spinEditfShadowThr);
            this.groupBox3.Controls.Add(this.labelControl4);
            this.groupBox3.Controls.Add(this.spinEditfInterfireCoefficienThr);
            this.groupBox3.Location = new System.Drawing.Point(30, 160);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(266, 128);
            this.groupBox3.TabIndex = 24;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "其它设置";
            // 
            // cbxBasicGroup
            // 
            this.cbxBasicGroup.AutoSize = true;
            this.cbxBasicGroup.Location = new System.Drawing.Point(29, 94);
            this.cbxBasicGroup.Name = "cbxBasicGroup";
            this.cbxBasicGroup.Size = new System.Drawing.Size(108, 16);
            this.cbxBasicGroup.TabIndex = 9;
            this.cbxBasicGroup.Text = "在同一基本组中";
            this.cbxBasicGroup.UseVisualStyleBackColor = true;
            // 
            // TDSInterfereCpiConditionDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(331, 448);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.simpleButton2);
            this.Controls.Add(this.simpleButton1);
            this.Name = "TDSInterfereCpiConditionDlg";
            this.Text = "扰码相关性条件";
            ((System.ComponentModel.ISupportInitialize)(this.spinEditfShadowThr.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditRelativeThr.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditfInterfireCoefficienThr.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditAbsThr.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxDefinedCell.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl lblShadow;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SpinEdit spinEditfShadowThr;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.SpinEdit spinEditRelativeThr;
        private DevExpress.XtraEditors.SpinEdit spinEditfInterfireCoefficienThr;
        private DevExpress.XtraEditors.SimpleButton simpleButton1;
        private DevExpress.XtraEditors.SimpleButton simpleButton2;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private System.Windows.Forms.ComboBox cbxThresholdType;
        private DevExpress.XtraEditors.LabelControl lblAbsThr;
        private DevExpress.XtraEditors.SpinEdit spinEditAbsThr;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.CheckEdit cbxDefinedCell;
        private DevExpress.XtraEditors.SimpleButton btnImport;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.CheckBox cbxBasicGroup;
    }
}