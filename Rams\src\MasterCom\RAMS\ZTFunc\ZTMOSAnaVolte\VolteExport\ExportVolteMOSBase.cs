﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Chris.Util;
using System.Windows.Forms;
using MasterCom.Util.UiEx;
using MasterCom.RAMS.UserMng;

namespace MasterCom.RAMS.ZTFunc
{
    public class ExportVolteMosBase : VolteMOSQueryAnaBase
    {
        public ExportVolteMosBase(MainModel mm)
            : base(mm)
        {
            mainModel = mm;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 27000, 27002, this.Name);
        }

        public override string IconName
        {
            get { return "Images/replay.gif"; }
        }

        public bool NeedJudgeTestPointByRegion { get; set;} = false;

        protected override void prepareSampleQryColumnNames(out List<string> colNames)
        {
            colNames = new List<string>();
            colNames.Add("lte_PESQMos");
            colNames.Add("lte_POLQA_Score_SWB");
            colNames.Add("lte_PESQLQ");
            colNames.Add("itime");
            colNames.Add("ilongitude");
            colNames.Add("ilatitude");
            colNames.Add("ifileid");
            colNames.Add("isampleid");

            colNames.Add("lte_PDCCH_UL_Grant_Count");
            colNames.Add("lte_PDCCH_DL_Grant_Count");
            colNames.Add("lte_DL_MCSCode0_AVG");
            colNames.Add("lte_DL_MCSCode1_AVG");
            colNames.Add("lte_UL_MCS_AVG");
            colNames.Add("lte_PDSCH_Code0_BLER");
            colNames.Add("lte_PDSCH_Code1_BLER");
            colNames.Add("lte_PUSCH_BLER");
            colNames.Add("lte_PDCCH_CCE_Start");
            colNames.Add("lte_PDCCH_CCEs_Number");
        }
       
#if AllRtpMsg
        double lossPackageSecondCondition;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            ExportVolteMOSSettingDlg setForm = new ExportVolteMOSSettingDlg();
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                setForm.GetCondition(out lossPackageSecondCondition);
                return true;
            }
            return false;
        }
#endif

        protected override void queryMOSFiles()
        {
            List<string> colNames = null;
            prepareSampleQryColumnNames(out colNames);
            DIYReplayFileQueryByCustom repFile = new DIYReplayFileQueryByCustom(MainModel);
            if (!NeedJudgeTestPointByRegion)
            {
                condition.Geometorys = null;
            }
            repFile.SetQueryCondition(condition);
            repFile.SetReplayContent(colNames, false, false);
            repFile.Query();
        }
        protected override void FireShowForm(List<VolteMosParam> mosParamList)
        {
            if (mosParamList.Count != 0)
            {
                List<List<object>> lstRecord = new List<List<object>>();
                lstRecord.Add(getTitleRow());
                foreach (VolteMosParam mosParam in mosParamList)
                {
                    lstRecord.Add(getContentRow(mosParam));
                }
                exportToFiles(lstRecord);
            }
            else
            {
                XtraMessageBox.Show("未查询到结果。");
            }
        }
        protected void exportToFiles(List<List<object>> lstRecord)
        {
            ExportFuncResultManager.GetInstance().SetCurLogItem(this.getRecLogItem());
            ExcelNPOIManager.ExportToExcel(lstRecord);
            ExportFuncResultManager.GetInstance().SetCurLogItem(null);
        }

        protected override void statOneFile(DTFileDataManager fileMng, out List<TimePeriod> periods, out List<PESQInfo> pesqLst, out List<double> longitudeLst, out List<double> latitudeLst)
        {
            periods = new List<TimePeriod>();
            pesqLst = new List<PESQInfo>();
            longitudeLst = new List<double>();
            latitudeLst = new List<double>();

            string MOSParamName = "";
            string PESQMOSParamName = "";
            string PESQLQParamName = "";
            foreach (TestPoint tp in fileMng.TestPoints)//通过采样点mos值，确定回放的时间段
            {
                /*
                根据测试文件里不同的mos类型取变量名：
                1）如果PESQMOS有值，则MOS值列取PESQMOS，PESQLQ列取PESQLQ
                2）如果POLQA_Score_SWB有值，则MOS值和PESQLQ列均取POLQA_Score_SWB
                */
                bool isValid = getMOSAndPESQ(ref MOSParamName, ref PESQMOSParamName, ref PESQLQParamName, tp);
                if (!isValid)
                {
                    continue;
                }

                float? mos = (float?)tp[MOSParamName];
                if (mos != null && mos > 0 && mos < mosGate)
                {
                    //设x为MOS值时间点，采样点分析的时间段为【x-10,x-2】，因为事件分析要包括切换事件所以事件的分析时间段为【x-10,x-1】，取2者最大值
                    //后面分析采样点时，需注意
                    DateTime endTime = tp.DateTime.AddSeconds(-1);
                    DateTime beginTime = tp.DateTime.AddSeconds(-10);
                    TimePeriod period = new TimePeriod(beginTime, endTime);
                    periods.Add(period);
                    pesqLst.Add(new PESQInfo((float)mos, tp[PESQLQParamName], tp[PESQMOSParamName],
                        tp["lte_PDCCH_UL_Grant_Count"], tp["lte_PDCCH_DL_Grant_Count"], tp["lte_DL_MCSCode0_AVG"],
                        tp["lte_DL_MCSCode1_AVG"], tp["lte_UL_MCS_AVG"], tp["lte_PDSCH_Code0_BLER"],
                        tp["lte_PDSCH_Code1_BLER"], tp["lte_PUSCH_BLER"], tp["lte_PDCCH_CCE_Start"],
                        tp["lte_PDCCH_CCEs_Number"]));
                    longitudeLst.Add(tp.Longitude);
                    latitudeLst.Add(tp.Latitude);
                }
            }
        }

        private bool getMOSAndPESQ(ref string MOSParamName, ref string PESQMOSParamName, ref string PESQLQParamName, TestPoint tp)
        {
            if (MOSParamName == "" || PESQMOSParamName == "" || PESQLQParamName == "")
            {
                if (tp is LTEFddTestPoint)
                {
                    return getLteFddMOSAndPESQ(ref MOSParamName, out PESQMOSParamName, ref PESQLQParamName, tp);
                }
                else
                {
                    return getLteMOSAndPESQ(ref MOSParamName, out PESQMOSParamName, ref PESQLQParamName, tp);
                }
            }
            return true;
        }

        private static bool getLteFddMOSAndPESQ(ref string MOSParamName, out string PESQMOSParamName, ref string PESQLQParamName, TestPoint tp)
        {
            PESQMOSParamName = "lte_fdd_PESQMos";
            float? pesq = (float?)tp["lte_fdd_PESQMos"];
            float? polqa = (float?)tp["lte_fdd_POLQA_Score_SWB"];
            if (pesq != null && pesq > 0 && pesq <= 5)
            {
                MOSParamName = "lte_fdd_PESQMos";
                PESQLQParamName = "lte_fdd_PESQLQ";
                return true;
            }
            else if (polqa != null && polqa > 0 && polqa <= 5)
            {
                MOSParamName = "lte_fdd_POLQA_Score_SWB";
                PESQLQParamName = "lte_fdd_POLQA_Score_SWB";
                return true;
            }
            else
            {
                return false;
            }
        }

        private static bool getLteMOSAndPESQ(ref string MOSParamName, out string PESQMOSParamName, ref string PESQLQParamName, TestPoint tp)
        {
            PESQMOSParamName = "lte_PESQMos";
            float? pesq = (float?)tp["lte_PESQMos"];
            float? polqa = (float?)tp["lte_POLQA_Score_SWB"];
            if (pesq != null && pesq > 0 && pesq <= 5)
            {
                MOSParamName = "lte_PESQMos";
                PESQLQParamName = "lte_PESQLQ";
                return true;
            }
            else if (polqa != null && polqa > 0 && polqa <= 5)
            {
                MOSParamName = "lte_POLQA_Score_SWB";
                PESQLQParamName = "lte_POLQA_Score_SWB";
                return true;
            }
            else
            {
                return false;
            }
        }

        protected override void statInThread(object fileDataManagers)
        {
            List<DTFileDataManager> files = (List<DTFileDataManager>)fileDataManagers;
            DTFileDataManager moFile = files[0];
            DTFileDataManager mtFile = files[1];
            try
            {
                fileStated.Add(moFile.FileID);

                int tpIdx1 = 0;
                int tpIdx2 = 0;
                int evtIdx1 = 0;
                int evtIdx2 = 0;
                int msgIdx1 = 0;
                int msgIdx2 = 0;
                for (int i = 0; i < prds.Count; i++)
                {
                    TimePeriod period = prds[i];

                    List<Event> evts1 = MOSAnaManager.getEventsByPeriod(period, moFile.Events, ref evtIdx1);
                    List<Event> evts2 = MOSAnaManager.getEventsByPeriod(period, mtFile.Events, ref evtIdx2);

                    //采样点的时间段为【x-10,x-2】,x为MOS值时间点
                    TimePeriod tpPeriod = new TimePeriod(period.BeginTime, period.EndTime.AddSeconds(-1));
                    List<TestPoint> tps1 = MOSAnaManager.getTestPoinsByPeriod(tpPeriod, moFile.TestPoints, ref tpIdx1);
                    List<TestPoint> tps2 = MOSAnaManager.getTestPoinsByPeriod(tpPeriod, mtFile.TestPoints, ref tpIdx2);

                    List<MasterCom.RAMS.Model.Message> msgs1 = MOSAnaManager.getMessagesByPeriod(tpPeriod, moFile.Messages, ref msgIdx1);
                    List<MasterCom.RAMS.Model.Message> msgs2 = MOSAnaManager.getMessagesByPeriod(tpPeriod, mtFile.Messages, ref msgIdx2);

                    VolteMosParam mosParam = new VolteMosParam(tpPeriod.BeginTime);
                    mosParam.SN = MosParamLst.Count + 1;
                    mosParam.strFileName = moFile.FileName;
                    mosParam.dtStartTime = tpPeriod.BeginTime;
                    mosParam.dtEndTime = tpPeriod.EndTime;
                    if (msgs1.Count > 0)
                    {
                        mosParam.dtStartHandSetTime = msgs1[0].HandsetTime;
                        mosParam.dtEndHandSetTime = msgs1[msgs1.Count - 1 >= 0 ? msgs1.Count - 1 : 0].HandsetTime;
                    }
                    mosParam.MOS = pesqLst[i].Mos;
                    mosParam.PESQLQ = pesqLst[i].PESQLQ;
                    mosParam.PESQMOS = pesqLst[i].PESQMOS;

                    mosParam.PDCCH_UL_Grant_Count = pesqLst[i].PDCCH_UL_Grant_Count;
                    mosParam.PDCCH_DL_Grant_Count = pesqLst[i].PDCCH_DL_Grant_Count;
                    mosParam.MCSCode0_DL = pesqLst[i].MCSCode0_DL;
                    mosParam.MCSCode1_DL = pesqLst[i].MCSCode1_DL;
                    mosParam.MCS_UL = pesqLst[i].MCS_UL;
                    mosParam.PDSCH_Code0_BLER = pesqLst[i].PDSCH_Code0_BLER;
                    mosParam.PDSCH_Code1_BLER = pesqLst[i].PDSCH_Code1_BLER;
                    mosParam.PUSCH_BLER = pesqLst[i].PUSCH_BLER;
                    mosParam.PDCCH_CCE_Start = pesqLst[i].PDCCH_CCE_Start;
                    mosParam.PDCCH_CCEs_Number = pesqLst[i].PDCCH_CCEs_Number;

                    mosParam.longitude = longitudeLst[i];
                    mosParam.latitude = latitudeLst[i];
#if AllRtpMsg
                    mosParam.ownParam.SetCondition(lossPackageSecondCondition);
                    //mosParam.otherParam.SetCondition(lossPackageSecondCondition)
#endif
                    mosParam.ownParam.Fill(tps1, tps2, evts1, msgs1, tpPeriod.EndTime);
                    mosParam.otherParam.Fill(tps2, evts2, msgs2, tpPeriod.EndTime);
                    mosParam.FillRTPInfo(tps1);
                    MosParamLst.Add(mosParam);
                }
                pesqLst.Clear();
                prds.Clear();
                longitudeLst.Clear();
                latitudeLst.Clear();
            }
            catch (System.Exception ex)
            {
                XtraMessageBox.Show(ex.ToString());
            }
            finally
            {
                System.Threading.Thread.Sleep(500);
                WaitTextBox.Close();
            }
        }

        protected List<object> getContentRow(VolteMosParam mosParam)
        {
            List<object> row = new List<object>();
            row.Add(mosParam.strFileName);
            row.Add(mosParam.SN);
            row.Add(mosParam.dtStartTime.ToString("yyyy-MM-dd HH:mm:ss.fff"));
            row.Add(mosParam.dtEndTime.ToString("yyyy-MM-dd HH:mm:ss.fff"));
#if AllRtpMsg
            row.Add(mosParam.dtStartHandSetTime.ToString("yyyy-MM-dd HH:mm:ss.fff"));
            row.Add(mosParam.dtEndHandSetTime.ToString("yyyy-MM-dd HH:mm:ss.fff"));
#endif
            row.Add(mosParam.MOS);
            row.Add(mosParam.PESQLQ);
            row.Add(mosParam.PESQMOS);
#if AllRtpMsg
            row.Add(mosParam.MOSContinuousDistance);
            row.Add(mosParam.ownParam.LossPackageSeconds);
            row.Add(mosParam.ownParam.LossMultiPackageSecond);
            row.Add(mosParam.ownParam.LossPackageTime);
            row.Add(mosParam.ownParam.MoLastSINRTestPoint);
            row.Add(mosParam.ownParam.MtLastSINRTestPoint);
#endif

            row.Add(mosParam.PDCCH_UL_Grant_Count);
            row.Add(mosParam.PDCCH_DL_Grant_Count);
            row.Add(mosParam.MCSCode0_DL);
            row.Add(mosParam.MCSCode1_DL);
            row.Add(mosParam.MCS_UL);
            row.Add(mosParam.PDSCH_Code0_BLER);
            row.Add(mosParam.PDSCH_Code1_BLER);
            row.Add(mosParam.PUSCH_BLER);
            row.Add(mosParam.PDCCH_CCE_Start);
            row.Add(mosParam.PDCCH_CCEs_Number);

            row.Add(mosParam.longitude);
            row.Add(mosParam.latitude);
            row.Add(mosParam.GridDesc); //网格

            row.Add(mosParam.ownParam.TpCount);
            row.Add(mosParam.ownParam.StrRsrp);
            row.Add(mosParam.ownParam.StrRsrpAvg);
            row.Add(mosParam.ownParam.RsrpDIF);
            row.Add(mosParam.ownParam.StrSinr);
            row.Add(mosParam.ownParam.StrSinrAvg);

            row.Add(mosParam.ownParam.RTPUETransferCount);
            row.Add(mosParam.ownParam.RTPUETransfer);
            row.Add(mosParam.ownParam.RTPIntermittentlyCount);
            row.Add(mosParam.ownParam.RTPIntermittently);
            row.Add(mosParam.ownParam.RTPPacketLostCount);
            row.Add(mosParam.ownParam.RTPPacketLost);

            row.Add(mosParam.ownParam.HORequest);
            row.Add(mosParam.ownParam.HOSucc);
            row.Add(mosParam.ownParam.HOFail);
            row.Add(mosParam.ownParam.HO1TimeSpan);
            row.Add(mosParam.ownParam.HO1Delay);
            row.Add(mosParam.ownParam.HO2TimeSpan);
            row.Add(mosParam.ownParam.HO2Delay);//切换2时延

#if AllRtpMsg
            row.Add(mosParam.ownParam.HO1DelayMedia);
            row.Add(mosParam.ownParam.HO2DelayMedia);
            row.Add(mosParam.ownParam.DelayMediaAverage);
#endif

            row.Add(mosParam.ownParam.Handover1Earfcns);
            row.Add(mosParam.ownParam.Handover2Earfcns);
            row.Add(mosParam.ownParam.Handover3Earfcns);
            row.Add(mosParam.ownParam.HOCellCGI);
            row.Add(mosParam.ownParam.TestPointCGI);
            row.Add(mosParam.ownParam.LacCiCount);//小区采样点数
            row.Add(mosParam.ownParam.BSC);
            row.Add(mosParam.ownParam.txPowerACount);
            row.Add(mosParam.ownParam.txPowerDCount);
            row.Add(mosParam.ownParam.txPowerECount);
            row.Add(mosParam.ownParam.txPowerFCount);
            row.Add(mosParam.ownParam.BandType);
            //row.Add(mosParam.ownParam.TxPowerAvg)//PUSCH_Power平均值
            //row.Add(mosParam.ownParam.TxPower1800Avg)
            //row.Add(mosParam.ownParam.TxPower900Avg)
            row.Add(mosParam.ownParam.NetWorkTypeDes);

            row.Add(mosParam.ownParam.QAM16UlPerAvg);
            row.Add(mosParam.ownParam.QAM16UlTimesAvg);
            row.Add(mosParam.ownParam.QAM64UlPerAvg);
            row.Add(mosParam.ownParam.QAM64UlTimesAvg);
            row.Add(mosParam.ownParam.QPSKUlPerAvg);
            row.Add(mosParam.ownParam.QPSKUlTimesAvg);
            row.Add(mosParam.ownParam.StrUlPathLoss);
            row.Add(mosParam.ownParam.UlPathLossAvg);
            row.Add(mosParam.ownParam.StrTxPower);
            row.Add(mosParam.ownParam.TxPowerAvg);
            row.Add(mosParam.ownParam.TxPowerMoreThan15);
            row.Add(mosParam.ownParam.StrMCSUl);
            row.Add(mosParam.ownParam.MCSUlAvg);
            row.Add(mosParam.ownParam.StrUlPRbNums);
            row.Add(mosParam.ownParam.UlPRbNumsAvg);
            row.Add(mosParam.ownParam.usInitBlerAvg);
            //row.Add(mosParam.ownParam.usBlerAvg)

            row.Add(mosParam.ownParam.DlFrequency);
            row.Add(mosParam.ownParam.TransMode);
            row.Add(mosParam.ownParam.StrRank);
            row.Add(mosParam.ownParam.QAM16DlPerAvg);
            row.Add(mosParam.ownParam.QAM16DlTimesAvg);
            row.Add(mosParam.ownParam.QAM64DlPerAvg);
            row.Add(mosParam.ownParam.QAM64DlTimesAvg);
            row.Add(mosParam.ownParam.QPSKDlPerAvg);
            row.Add(mosParam.ownParam.QPSKDlTimesAvg);
            row.Add(mosParam.ownParam.StrMCSDlCode0);
            row.Add(mosParam.ownParam.StrMCSDlCode1);
            row.Add(mosParam.ownParam.MCSDlAvg);
            row.Add(mosParam.ownParam.StrDlPRbNums);
            row.Add(mosParam.ownParam.DlPRbNumsAvg);
            row.Add(mosParam.ownParam.RRCReEstablish);
            row.Add(mosParam.ownParam.StrDtx);
            row.Add(mosParam.ownParam.dsInitBlerAvg);//下行初传BLER平均值
            row.Add(mosParam.ownParam.JitterAvg);
            //row.Add(mosParam.ownParam.dsBlerAvg)
            //row.Add(mosParam.ownParam.QAM16DlCode0PerAvg)
            //row.Add(mosParam.ownParam.QAM16DlCode0TimesAvg)
            //row.Add(mosParam.ownParam.QAM64DlCode0PerAvg)
            //row.Add(mosParam.ownParam.QAM64DlCode0TimesAvg)
            //row.Add(mosParam.ownParam.QPSKDlCode0PerAvg)
            //row.Add(mosParam.ownParam.QPSKDlCode0TimesAvg)

            //row.Add(mosParam.ownParam.QAM16DlCode1PerAvg)
            //row.Add(mosParam.ownParam.QAM16DlCode1TimesAvg)
            //row.Add(mosParam.ownParam.QAM64DlCode1PerAvg)
            //row.Add(mosParam.ownParam.QAM64DlCode1TimesAvg)
            //row.Add(mosParam.ownParam.QPSKDlCode1PerAvg)
            //row.Add(mosParam.ownParam.QPSKDlCode1TimesAvg)

            row.Add(mosParam.otherParam.TpCount);
            row.Add(mosParam.otherParam.StrRsrp);
            row.Add(mosParam.otherParam.StrRsrpAvg);
            row.Add(mosParam.otherParam.RsrpDIF);
            row.Add(mosParam.otherParam.StrSinr);
            row.Add(mosParam.otherParam.StrSinrAvg);

            row.Add(mosParam.otherParam.RTPUETransferCount);
            row.Add(mosParam.otherParam.RTPUETransfer);
            row.Add(mosParam.otherParam.RTPIntermittentlyCount);
            row.Add(mosParam.otherParam.RTPIntermittently);
            row.Add(mosParam.otherParam.RTPPacketLostCount);
            row.Add(mosParam.otherParam.RTPPacketLost);

            row.Add(mosParam.otherParam.HORequest);
            row.Add(mosParam.otherParam.HOSucc);
            row.Add(mosParam.otherParam.HOFail);
            row.Add(mosParam.otherParam.HO1TimeSpan);
            row.Add(mosParam.otherParam.HO1Delay);
            row.Add(mosParam.otherParam.HO2TimeSpan);
            row.Add(mosParam.otherParam.HO2Delay);//对端切换2时延

#if AllRtpMsg
            row.Add(mosParam.otherParam.HO1DelayMedia);
            row.Add(mosParam.otherParam.HO2DelayMedia);
            row.Add(mosParam.otherParam.DelayMediaAverage);
#endif

            row.Add(mosParam.otherParam.Handover1Earfcns);
            row.Add(mosParam.otherParam.Handover2Earfcns);
            row.Add(mosParam.otherParam.Handover3Earfcns);
            row.Add(mosParam.otherParam.HOCellCGI);
            row.Add(mosParam.otherParam.TestPointCGI);
            row.Add(mosParam.otherParam.LacCiCount);//小区采样点数
            row.Add(mosParam.otherParam.BSC);
            row.Add(mosParam.otherParam.txPowerACount);
            row.Add(mosParam.otherParam.txPowerDCount);
            row.Add(mosParam.otherParam.txPowerECount);
            row.Add(mosParam.otherParam.txPowerFCount);
            row.Add(mosParam.otherParam.BandType);
            //row.Add(mosParam.otherParam.TxPower1800Avg)
            //row.Add(mosParam.otherParam.TxPower900Avg)
            row.Add(mosParam.otherParam.NetWorkTypeDes);


            row.Add(mosParam.otherParam.QAM16UlPerAvg);
            row.Add(mosParam.otherParam.QAM16UlTimesAvg);
            row.Add(mosParam.otherParam.QAM64UlPerAvg);
            row.Add(mosParam.otherParam.QAM64UlTimesAvg);
            row.Add(mosParam.otherParam.QPSKUlPerAvg);
            row.Add(mosParam.otherParam.QPSKUlTimesAvg);
            row.Add(mosParam.otherParam.StrUlPathLoss);
            row.Add(mosParam.otherParam.UlPathLossAvg);
            row.Add(mosParam.otherParam.StrTxPower);
            row.Add(mosParam.otherParam.TxPowerAvg);
            row.Add(mosParam.otherParam.TxPowerMoreThan15);
            row.Add(mosParam.otherParam.StrMCSUl);
            row.Add(mosParam.otherParam.MCSUlAvg);
            row.Add(mosParam.otherParam.StrUlPRbNums);
            row.Add(mosParam.otherParam.UlPRbNumsAvg);
            row.Add(mosParam.otherParam.usInitBlerAvg);

            row.Add(mosParam.otherParam.DlFrequency);//频点
            row.Add(mosParam.otherParam.TransMode);
            row.Add(mosParam.otherParam.StrRank);
            row.Add(mosParam.otherParam.QAM16DlPerAvg);
            row.Add(mosParam.otherParam.QAM16DlTimesAvg);
            row.Add(mosParam.otherParam.QAM64DlPerAvg);
            row.Add(mosParam.otherParam.QAM64DlTimesAvg);
            row.Add(mosParam.otherParam.QPSKDlPerAvg);
            row.Add(mosParam.otherParam.QPSKDlTimesAvg);
            row.Add(mosParam.otherParam.StrMCSDlCode0);
            row.Add(mosParam.otherParam.StrMCSDlCode1);
            row.Add(mosParam.otherParam.MCSDlAvg);
            row.Add(mosParam.otherParam.StrDlPRbNums);
            row.Add(mosParam.otherParam.DlPRbNumsAvg);
            row.Add(mosParam.otherParam.RRCReEstablish);
            row.Add(mosParam.otherParam.StrDtx);
            row.Add(mosParam.otherParam.dsInitBlerAvg);//对端下行初传BLER平均值
            row.Add(mosParam.otherParam.JitterAvg);
            //row.Add(mosParam.otherParam.dsBlerAvg)
            //row.Add(mosParam.otherParam.QAM16DlCode0PerAvg)
            //row.Add(mosParam.otherParam.QAM16DlCode0TimesAvg)
            //row.Add(mosParam.otherParam.QAM64DlCode0PerAvg)
            //row.Add(mosParam.otherParam.QAM64DlCode0TimesAvg)
            //row.Add(mosParam.otherParam.QPSKDlCode0PerAvg)
            //row.Add(mosParam.otherParam.QPSKDlCode0TimesAvg)

            //row.Add(mosParam.otherParam.QAM16DlCode1PerAvg)
            //row.Add(mosParam.otherParam.QAM16DlCode1TimesAvg)
            //row.Add(mosParam.otherParam.QAM64DlCode1PerAvg)
            //row.Add(mosParam.otherParam.QAM64DlCode1TimesAvg)
            //row.Add(mosParam.otherParam.QPSKDlCode1PerAvg)
            //row.Add(mosParam.otherParam.QPSKDlCode1TimesAvg)
            row.Add(mosParam.RTPLossRate);
            row.Add(mosParam.PackageLostNum);
            row.Add(mosParam.PackageNum);
            row.Add(mosParam.AllHORequestCount);
            row.Add(mosParam.SameBtsSameEarfcnHOCount);
            row.Add(mosParam.SameBtsDiffEarfcnHOCount);
            row.Add(mosParam.DiffBtsSameEarfcnHOCount);
            row.Add(mosParam.DiffBtsDiffEarfcnHOCount);
            row.Add(mosParam.AllJitterAvg);

            return row;
        }

        protected List<object> getTitleRow()
        {
            List<object> row = new List<object>();
            row.Add("文件名");
            row.Add("序号");
            row.Add("起始时间");
            row.Add("结束时间");
#if AllRtpMsg
            row.Add("起始时间(handsettime)");
            row.Add("结束时间(handsettime)");
#endif
            row.Add("MOS值");
            row.Add("PESQLQ");
            row.Add("PESQMOS");
#if AllRtpMsg
            row.Add("MOS持续距离");
            row.Add("累计单通持续时间(handsettime)");
            row.Add("单次单通持续时间(handsettime)"); 
            row.Add("丢包时间点(handsettime)");
            row.Add("丢包前本端占用的小区SINR");
            row.Add("丢包前对端占用的小区SINR");
#endif

            row.Add("PDCCH_UL_Grant_Count");
            row.Add("PDCCH_DL_Grant_Count");
            row.Add("MCSCode0_DL");
            row.Add("MCSCode1_DL");
            row.Add("MCS_UL");
            row.Add("PDSCH_Code0_BLER");
            row.Add("PDSCH_Code1_BLER");
            row.Add("PUSCH_BLER");
            row.Add("PDCCH_CCE_Start");
            row.Add("PDCCH_CCEs_Number");

            row.Add("经度");
            row.Add("纬度");
            row.Add("网格");

            row.Add("采样点数");
            row.Add("采样点电平");
            row.Add("平均电平");
            row.Add("电平差（与邻区最大电平的差）");
            row.Add("采样点质差");
            row.Add("平均质差");

            row.Add("RTP单通个数");
            row.Add("RTP单通值");
            row.Add("RTP断续个数");
            row.Add("RTP断续值");
            row.Add("RTP吞字个数");
            row.Add("RTP吞字值");

            row.Add("切换请求次数");
            row.Add("切换成功次数");
            row.Add("切换失败次数");
            //切换时差是 切换事件的时间与起始时间的差值
            row.Add("切换1时差");
            //切换时延是 切换事件的时延的指标
            row.Add("切换1时延");
            row.Add("切换2时差");
            row.Add("切换2时延");

#if AllRtpMsg
            //切换时延（媒体面）是 切换后第一个下行RTP包的时间 减去 切换前最后一下行RTP的时间，精确到毫秒级别(需回放所有RTP信令)
            row.Add("切换1时延(媒体面)(ms)(handsettime)");
            row.Add("切换2时延(媒体面)(ms)(handsettime)");
            //当前周期内平均切换时延(媒体面)是 当前8秒MOS周期内发生的所有切换的媒体面时延的平均值
            row.Add("当前周期内平均切换时延(媒体面)(ms)(handsettime)");
#endif

            row.Add("切换1时频点");
            row.Add("切换2时频点");
            row.Add("切换3时频点");
            row.Add("切换小区CGI");
            row.Add("采样点小区CGI");
            row.Add("小区采样点数");
            row.Add("BSC");
            row.Add("A频段采样点次数");
            row.Add("D频段采样点次数");
            row.Add("E频段采样点次数");

            row.Add("F频段采样点次数");
            row.Add("频段");
            //row.Add("1800MSTX-Power平均值(dBm)")
            //row.Add("900MSTX-Power平均值(dBm)")
            row.Add("本端网络类型");
            row.Add("上行QAM16平均占比");
            row.Add("上行QAM16平均次数");
            row.Add("上行QAM64平均占比");
            row.Add("上行QAM64平均次数");
            row.Add("上行QPSK平均占比");
            row.Add("上行QPSK平均次数");
            row.Add("上行Path_Loss");

            row.Add("上行Path_Loss均值");
            row.Add("PUSCH_TxPower");
            row.Add("PUSCH_TxPower均值");
            row.Add("PUSCH_TxPower>=15占比");
            row.Add("MOS周期上行MCS的采样点值出现次数");
            row.Add("MOS周期上行MCS的采样点值出现次数平均值");
            row.Add("UL PRB");
            row.Add("UL PRB平均值");
            row.Add("上行初传BLER平均值");
            row.Add("频点");

            row.Add("传输模式");
            row.Add("rank");
            row.Add("下行QAM16平均占比");
            row.Add("下行QAM16平均次数");
            row.Add("下行QAM64平均占比");
            row.Add("下行QAM64平均次数");
            row.Add("下行QPSK平均占比");
            row.Add("下行QPSK平均次数");
            row.Add("下行MCSCode 0");
            row.Add("下行MCSCode 1");

            row.Add("MOS周期下行MCS的采样点值出现次数平均值");
            row.Add("DL PRB");
            row.Add("DL PRB平均值");
            row.Add("无线链路失败次数");
            row.Add("DTX");
            row.Add("下行初传BLER平均值");
            row.Add("抖动率平均值");
            //row.Add("下行QAM16码0平均占比")
            //row.Add("下行QAM16码0平均次数")
            //row.Add("下行QAM64码0平均占比")
            //row.Add("下行QAM64码0平均次数")
            //row.Add("下行QPSK码0平均占比")
            //row.Add("下行QPSK码0平均次数")

            //row.Add("下行QAM16码1平均占比")
            //row.Add("下行QAM16码1平均次数")
            //row.Add("下行QAM64码1平均占比")
            //row.Add("下行QAM64码1平均次数")
            //row.Add("下行QPSK码1平均占比")
            //row.Add("下行QPSK码1平均次数")

            row.Add("对端采样点数");
            row.Add("对端采样点电平");
            row.Add("对端平均电平");
            row.Add("对端电平差");
            row.Add("对端采样点质差");
            row.Add("对端平均质差");

            row.Add("对端RTP单通个数");
            row.Add("对端RTP单通值");
            row.Add("对端RTP断续个数");
            row.Add("对端RTP断续值");
            row.Add("对端RTP吞字个数");
            row.Add("对端RTP吞字值");

            row.Add("对端切换请求次数");
            row.Add("对端切换成功次数");
            row.Add("对端切换失败次数");
            row.Add("对端切换1时差");
            row.Add("对端切换1时延");
            row.Add("对端切换2时差");
            row.Add("对端切换2时延");

#if AllRtpMsg
            row.Add("对端切换1时延(媒体面)(ms)(handsettime)");
            row.Add("对端切换2时延(媒体面)(ms)(handsettime)");
            row.Add("对端当前周期内平均切换时延(媒体面)(ms)(handsettime)");
#endif

            row.Add("对端切换1时频点");
            row.Add("对端切换2时频点");
            row.Add("对端切换3时频点");
            row.Add("对端切换小区CGI");
            row.Add("对端采样点小区CGI");
            row.Add("对端小区采样点数");
            row.Add("对端BSC");
            row.Add("对端A频段采样点次数");
            row.Add("对端D频段采样点次数");
            row.Add("对端E频段采样点次数");
            row.Add("对端F频段采样点次数");
            row.Add("对端频段");
            //row.Add("对端1800MSTX-Power平均值(dBm)")
            //row.Add("对端900MSTX-Power平均值(dBm)")
            row.Add("对端网络类型");

            row.Add("对端上行QAM16平均占比");
            row.Add("对端上行QAM16平均次数");
            row.Add("对端上行QAM64平均占比");
            row.Add("对端上行QAM64平均次数");
            row.Add("对端上行QPSK平均占比");
            row.Add("对端上行QPSK平均次数");
            row.Add("对端上行Path_Loss");
            row.Add("对端上行Path_Loss均值");
            row.Add("对端PUSCH_TxPower");
            row.Add("对端PUSCH_TxPower均值");
            row.Add("对端PUSCH_TxPower>=15占比");
            row.Add("对端MOS周期上行MCS的采样点值出现次数");
            row.Add("对端MOS周期上行MCS的采样点值出现次数平均值");
            row.Add("对端UL PRB");
            row.Add("对端UL PRB平均值");
            row.Add("对端上行初传BLER平均值");

            row.Add("对端频点");
            row.Add("对端传输模式");
            row.Add("对端rank");
            row.Add("对端下行QAM16平均占比");
            row.Add("对端下行QAM16平均次数");
            row.Add("对端下行QAM64平均占比");
            row.Add("对端下行QAM64平均次数");
            row.Add("对端下行QPSK平均占比");
            row.Add("对端下行QPSK平均次数");
            //row.Add("对端下行平均误块率")
            row.Add("对端下行MCSCode 0");
            row.Add("对端下行MCSCode 1");
            row.Add("对端MOS周期下行MCS的采样点值出现次数平均值");
            row.Add("对端DL PRB");
            row.Add("对端DL PRB平均值");
            row.Add("对端无线链路失败次数");
            row.Add("对端DTX");
            row.Add("对端下行初传BLER平均值");
            row.Add("对端抖动率平均值");
            //row.Add("对端下行QAM16码0平均占比")
            //row.Add("对端下行QAM16码0平均次数")
            //row.Add("对端下行QAM64码0平均占比")
            //row.Add("对端下行QAM64码0平均次数")
            //row.Add("对端下行QPSK码0平均占比")
            //row.Add("对端下行QPSK码0平均次数")
             
            //row.Add("对端下行QAM16码1平均占比")
            //row.Add("对端下行QAM16码1平均次数")
            //row.Add("对端下行QAM64码1平均占比")
            //row.Add("对端下行QAM64码1平均次数")
            //row.Add("对端下行QPSK码1平均占比")
            //row.Add("对端下行QPSK码1平均次数")

            row.Add("端到端RTP丢包率（%）");
            row.Add("当前周期丢包数");
            row.Add("当前周期发包总数");
            row.Add("总切换次数");
            row.Add("同站同频切换次数");
            row.Add("同站异频切换次数");
            row.Add("异站同频切换次数");
            row.Add("异站异频切换次数");
            row.Add("主端+对端抖动率平均值");
            return row;
        }
    }

    public class ExportVolteMosBase_FDD : ExportVolteMosBase
    {
        public ExportVolteMosBase_FDD(MainModel mainModel)
            : base(mainModel)
        {

        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30025, this.Name);
        }

        public override string IconName
        {
            get { return "Images/replay.gif"; }
        }
        protected override void prepareSampleQryColumnNames(out List<string> colNames)
        {
            colNames = new List<string>();
            colNames.Add("lte_fdd_PESQMos");
            colNames.Add("lte_fdd_POLQA_Score_SWB");
            colNames.Add("itime");
            colNames.Add("ilongitude");
            colNames.Add("ilatitude");
            colNames.Add("ifileid");
            colNames.Add("isampleid");
        }

        protected override List<string> getFileReplaySampleColNames()
        {
            if (fileRepSampleColNames == null)
            {
                fileRepSampleColNames = new List<string>();
                fileRepSampleColNames.Add("isampleid");
                fileRepSampleColNames.Add("itime");
                fileRepSampleColNames.Add("ilongitude");
                fileRepSampleColNames.Add("ilatitude");
                fileRepSampleColNames.Add("lte_fdd_TAC");
                fileRepSampleColNames.Add("lte_fdd_ECI");
                fileRepSampleColNames.Add("lte_fdd_RSRP");
                fileRepSampleColNames.Add("lte_fdd_SINR");
                fileRepSampleColNames.Add("lte_fdd_PCI");
                fileRepSampleColNames.Add("lte_fdd_RSRQ");
                fileRepSampleColNames.Add("lte_fdd_EARFCN");
                fileRepSampleColNames.Add("lte_fdd_PESQMos");
                fileRepSampleColNames.Add("lte_fdd_POLQA_Score_SWB");
                fileRepSampleColNames.Add("lte_fdd_NCell_RSRP");
                fileRepSampleColNames.Add("lte_fdd_PUSCH_Power");
                fileRepSampleColNames.Add("lte_fdd_Times_QAM16_UL");
                fileRepSampleColNames.Add("lte_fdd_Times_QAM64_UL");
                fileRepSampleColNames.Add("lte_fdd_Times_QPSK_UL");
                fileRepSampleColNames.Add("lte_fdd_Times_QAM16_DLCode0");
                fileRepSampleColNames.Add("lte_fdd_Times_QAM16_DLCode1");
                fileRepSampleColNames.Add("lte_fdd_Times_QAM64_DLCode0");
                fileRepSampleColNames.Add("lte_fdd_Times_QAM64_DLCode1");
                fileRepSampleColNames.Add("lte_fdd_Times_QPSK_DLCode0");
                fileRepSampleColNames.Add("lte_fdd_Times_QPSK_DLCode1");
                
                fileRepSampleColNames.Add("lte_fdd_gsm_DM_RxLevSub");
                fileRepSampleColNames.Add("lte_fdd_gsm_DM_RxQualSub");
                fileRepSampleColNames.Add("mode");
                fileRepSampleColNames.Add("lte_fdd_gsm_SC_LAC");
                fileRepSampleColNames.Add("lte_fdd_gsm_SC_CI");
                fileRepSampleColNames.Add("lte_fdd_gsm_SC_BCCH");
                fileRepSampleColNames.Add("lte_fdd_gsm_SC_BSIC");
                fileRepSampleColNames.Add("lte_fdd_gsm_NC_RxLev");
            }
            return fileRepSampleColNames;
        }
    }
}
