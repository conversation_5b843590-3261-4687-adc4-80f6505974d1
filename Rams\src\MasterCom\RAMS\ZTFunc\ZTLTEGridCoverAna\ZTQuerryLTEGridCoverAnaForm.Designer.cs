﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTQuerryLTEGridCoverAnaForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.dataGrid = new DevExpress.XtraGrid.GridControl();
            this.outPutExcel = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.outPutData = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn39 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.dataGrid)).BeginInit();
            this.outPutExcel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            this.SuspendLayout();
            // 
            // dataGrid
            // 
            this.dataGrid.ContextMenuStrip = this.outPutExcel;
            this.dataGrid.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGrid.Location = new System.Drawing.Point(0, 0);
            this.dataGrid.MainView = this.gridView1;
            this.dataGrid.Name = "dataGrid";
            this.dataGrid.Size = new System.Drawing.Size(1164, 427);
            this.dataGrid.TabIndex = 1;
            this.dataGrid.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // outPutExcel
            // 
            this.outPutExcel.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.outPutData});
            this.outPutExcel.Name = "outPutExcel";
            this.outPutExcel.Size = new System.Drawing.Size(125, 26);
            // 
            // outPutData
            // 
            this.outPutData.Name = "outPutData";
            this.outPutData.Size = new System.Drawing.Size(124, 22);
            this.outPutData.Text = "导出Excel";
            this.outPutData.Click += new System.EventHandler(this.outPutData_Click);
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn8,
            this.gridColumn26,
            this.gridColumn7,
            this.gridColumn2,
            this.gridColumn27,
            this.gridColumn11,
            this.gridColumn28,
            this.gridColumn12,
            this.gridColumn3,
            this.gridColumn29,
            this.gridColumn14,
            this.gridColumn30,
            this.gridColumn15,
            this.gridColumn4,
            this.gridColumn31,
            this.gridColumn16,
            this.gridColumn32,
            this.gridColumn17,
            this.gridColumn5,
            this.gridColumn33,
            this.gridColumn18,
            this.gridColumn34,
            this.gridColumn19,
            this.gridColumn6,
            this.gridColumn35,
            this.gridColumn20,
            this.gridColumn36,
            this.gridColumn21,
            this.gridColumn9,
            this.gridColumn37,
            this.gridColumn22,
            this.gridColumn38,
            this.gridColumn23,
            this.gridColumn10,
            this.gridColumn39,
            this.gridColumn24,
            this.gridColumn40,
            this.gridColumn25,
            this.gridColumn13,
            this.gridColumn41});
            this.gridView1.GridControl = this.dataGrid;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsView.ColumnAutoWidth = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "地市名称";
            this.gridColumn1.FieldName = "StrCityName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "移动空闲有效栅格数(-100Rsrp,-3Sinr)";
            this.gridColumn8.FieldName = "IYDIDLECoverCount";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 1;
            this.gridColumn8.Width = 134;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "移动空闲有效栅格数(-110Rsrp,-3Sinr)";
            this.gridColumn26.FieldName = "IYDIDLECoverCount110";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 2;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "移动空闲总栅格数";
            this.gridColumn7.FieldName = "IYDIDLEAllCount";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 3;
            this.gridColumn7.Width = 110;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "移动空闲覆盖率(-100Rsrp,-3Sinr)";
            this.gridColumn2.FieldName = "StrYDIDLERate";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 4;
            this.gridColumn2.Width = 105;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "移动空闲覆盖率(-110Rsrp,-3Sinr)";
            this.gridColumn27.FieldName = "StrYDIDLERate110";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 5;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "移动上传有效栅格数(-100Rsrp,-3Sinr)";
            this.gridColumn11.FieldName = "IYDUploadCoverCount";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 6;
            this.gridColumn11.Width = 130;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "移动上传有效栅格数(-110Rsrp,-3Sinr)";
            this.gridColumn28.FieldName = "IYDUploadCoverCount110";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 7;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "移动上传总栅格数";
            this.gridColumn12.FieldName = "IYDUploadAllCount";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 8;
            this.gridColumn12.Width = 110;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "移动上传覆盖率(-100Rsrp,-3Sinr)";
            this.gridColumn3.FieldName = "StrYDUploadRate";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 9;
            this.gridColumn3.Width = 105;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "移动上传覆盖率(-110Rsrp,-3Sinr)";
            this.gridColumn29.FieldName = "StrYDUploadRate110";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 10;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "移动下载有效栅格数(-100Rsrp,-3Sinr)";
            this.gridColumn14.FieldName = "IYDDownloaCoverCount";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 11;
            this.gridColumn14.Width = 130;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "移动下载有效栅格数(-110Rsrp,-3Sinr)";
            this.gridColumn30.FieldName = "IYDDownloaCoverCount110";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 12;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "移动下载总栅格数";
            this.gridColumn15.FieldName = "IYDDownloaAllCount";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 13;
            this.gridColumn15.Width = 110;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "移动下载覆盖率(-100Rsrp,-3Sinr)";
            this.gridColumn4.FieldName = "StrYDDownloadRate";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 14;
            this.gridColumn4.Width = 105;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "移动下载覆盖率(-110Rsrp,-3Sinr)";
            this.gridColumn31.FieldName = "StrYDDownloadRate110";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 15;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "移动数据有效栅格数(-100Rsrp,-3Sinr)";
            this.gridColumn16.FieldName = "IYdUploadDownloadCover";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 16;
            this.gridColumn16.Width = 150;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "移动数据有效栅格数(-110Rsrp,-3Sinr)";
            this.gridColumn32.FieldName = "IYdUploadDownloadCover110";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 17;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "移动数据总栅格数";
            this.gridColumn17.FieldName = "IYdUploadDownloadAll";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 18;
            this.gridColumn17.Width = 150;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "移动数据覆盖率(-100Rsrp,-3Sinr)";
            this.gridColumn5.FieldName = "StrYDUpDownCoverRate";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 19;
            this.gridColumn5.Width = 150;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "移动数据覆盖率(-110Rsrp,-3Sinr)";
            this.gridColumn33.FieldName = "StrYDUpDownCoverRate110";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 20;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "联通空闲有效栅格数(-100Rsrp,-3Sinr)";
            this.gridColumn18.FieldName = "ILTIDLECoverCount";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 21;
            this.gridColumn18.Width = 130;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "联通空闲有效栅格数(-110Rsrp,-3Sinr)";
            this.gridColumn34.FieldName = "ILTIDLECoverCount110";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 22;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "联通空闲总栅格数";
            this.gridColumn19.FieldName = "ILTIDLEAllCount";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 23;
            this.gridColumn19.Width = 110;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "联通空闲覆盖率(-100Rsrp,-3Sinr)";
            this.gridColumn6.FieldName = "StrLTIDLERate";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 24;
            this.gridColumn6.Width = 105;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "联通空闲覆盖率(-110Rsrp,-3Sinr)";
            this.gridColumn35.FieldName = "StrLTIDLERate110";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 25;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "联通数据有效栅格数(-100Rsrp,-3Sinr)";
            this.gridColumn20.FieldName = "ILtUploadDownloadCover";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 26;
            this.gridColumn20.Width = 130;
            // 
            // gridColumn36
            // 
            this.gridColumn36.Caption = "联通数据有效栅格数(-110Rsrp,-3Sinr)";
            this.gridColumn36.FieldName = "ILtUploadDownloadCover110";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.Visible = true;
            this.gridColumn36.VisibleIndex = 27;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "联通数据总栅格数";
            this.gridColumn21.FieldName = "ILtUploadDownloadAll";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 28;
            this.gridColumn21.Width = 110;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "联通数据覆盖率(-100Rsrp,-3Sinr)";
            this.gridColumn9.FieldName = "StrLTUpDownCoverRate";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 29;
            this.gridColumn9.Width = 105;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "联通数据覆盖率(-110Rsrp,-3Sinr)";
            this.gridColumn37.FieldName = "StrLTUpDownCoverRate110";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.Visible = true;
            this.gridColumn37.VisibleIndex = 30;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "电信空闲有效栅格数(-100Rsrp,-3Sinr)";
            this.gridColumn22.FieldName = "IDXIDLECoverCount";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 31;
            this.gridColumn22.Width = 130;
            // 
            // gridColumn38
            // 
            this.gridColumn38.Caption = "电信空闲有效栅格数(-110Rsrp,-3Sinr)";
            this.gridColumn38.FieldName = "IDXIDLECoverCount110";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.Visible = true;
            this.gridColumn38.VisibleIndex = 32;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "电信空闲总栅格数";
            this.gridColumn23.FieldName = "IDXIDLEAllCount";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 33;
            this.gridColumn23.Width = 110;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "电信空闲覆盖率(-100Rsrp,-3Sinr)";
            this.gridColumn10.FieldName = "StrDXIDLERate";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 34;
            this.gridColumn10.Width = 105;
            // 
            // gridColumn39
            // 
            this.gridColumn39.Caption = "电信空闲覆盖率(-110Rsrp,-3Sinr)";
            this.gridColumn39.FieldName = "StrDXIDLERate110";
            this.gridColumn39.Name = "gridColumn39";
            this.gridColumn39.Visible = true;
            this.gridColumn39.VisibleIndex = 35;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "电信数据有效栅格数(-100Rsrp,-3Sinr)";
            this.gridColumn24.FieldName = "IDxUploadDownloadCover";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 36;
            this.gridColumn24.Width = 130;
            // 
            // gridColumn40
            // 
            this.gridColumn40.Caption = "电信数据有效栅格数(-110Rsrp,-3Sinr)";
            this.gridColumn40.FieldName = "IDxUploadDownloadCover110";
            this.gridColumn40.Name = "gridColumn40";
            this.gridColumn40.Visible = true;
            this.gridColumn40.VisibleIndex = 37;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "电信数据总栅格数";
            this.gridColumn25.FieldName = "IDxUploadDownloadAll";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 38;
            this.gridColumn25.Width = 110;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "电信数据覆盖率(-100Rsrp,-3Sinr)";
            this.gridColumn13.FieldName = "StrDXUpDownCoverRate";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 39;
            this.gridColumn13.Width = 105;
            // 
            // gridColumn41
            // 
            this.gridColumn41.Caption = "电信数据覆盖率(-110Rsrp,-3Sinr)";
            this.gridColumn41.FieldName = "StrDXUpDownCoverRate110";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.Visible = true;
            this.gridColumn41.VisibleIndex = 40;
            // 
            // ZTQuerryLTEGridCoverAnaForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1164, 427);
            this.Controls.Add(this.dataGrid);
            this.Name = "ZTQuerryLTEGridCoverAnaForm";
            this.Text = "LTE三网栅格覆盖率统计列表";
            ((System.ComponentModel.ISupportInitialize)(this.dataGrid)).EndInit();
            this.outPutExcel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl dataGrid;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private System.Windows.Forms.ContextMenuStrip outPutExcel;
        private System.Windows.Forms.ToolStripMenuItem outPutData;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn39;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn40;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
    }
}