﻿namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    partial class WeakCoverPnl
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label6 = new System.Windows.Forms.Label();
            this.numDisMax2 = new DevExpress.XtraEditors.SpinEdit();
            this.lblNCWeak = new System.Windows.Forms.Label();
            this.label20 = new System.Windows.Forms.Label();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.label9 = new System.Windows.Forms.Label();
            this.numDisMax = new DevExpress.XtraEditors.SpinEdit();
            this.lblSCWeak = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.label15 = new System.Windows.Forms.Label();
            this.numDisMin2 = new DevExpress.XtraEditors.SpinEdit();
            this.lblLNC = new System.Windows.Forms.Label();
            this.label18 = new System.Windows.Forms.Label();
            this.grpNoSite = new System.Windows.Forms.GroupBox();
            this.label3 = new System.Windows.Forms.Label();
            this.numDisMin = new DevExpress.XtraEditors.SpinEdit();
            this.lblNoSite = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.numRSRPMax = new DevExpress.XtraEditors.SpinEdit();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDisMax2.Properties)).BeginInit();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDisMax.Properties)).BeginInit();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDisMin2.Properties)).BeginInit();
            this.grpNoSite.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDisMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRSRPMax.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.groupBox2);
            this.groupBox1.Controls.Add(this.groupBox3);
            this.groupBox1.Controls.Add(this.groupBox4);
            this.groupBox1.Controls.Add(this.grpNoSite);
            this.groupBox1.Controls.Add(this.numRSRPMax);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(399, 378);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "弱覆盖";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.label6);
            this.groupBox2.Controls.Add(this.numDisMax2);
            this.groupBox2.Controls.Add(this.lblNCWeak);
            this.groupBox2.Controls.Add(this.label20);
            this.groupBox2.Location = new System.Drawing.Point(33, 295);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(355, 74);
            this.groupBox2.TabIndex = 4;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "邻区覆盖不足";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(16, 21);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(161, 12);
            this.label6.TabIndex = 0;
            this.label6.Text = "主服和邻区与采样点距离都＜";
            // 
            // numDisMax2
            // 
            this.numDisMax2.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numDisMax2.Location = new System.Drawing.Point(183, 16);
            this.numDisMax2.Name = "numDisMax2";
            this.numDisMax2.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDisMax2.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numDisMax2.Size = new System.Drawing.Size(60, 21);
            this.numDisMax2.TabIndex = 0;
            // 
            // lblNCWeak
            // 
            this.lblNCWeak.AutoSize = true;
            this.lblNCWeak.Location = new System.Drawing.Point(16, 48);
            this.lblNCWeak.Name = "lblNCWeak";
            this.lblNCWeak.Size = new System.Drawing.Size(149, 12);
            this.lblNCWeak.TabIndex = 0;
            this.lblNCWeak.Text = "邻区比主服距离采样点更近";
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(249, 21);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(29, 12);
            this.label20.TabIndex = 0;
            this.label20.Text = "米，";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.label9);
            this.groupBox3.Controls.Add(this.numDisMax);
            this.groupBox3.Controls.Add(this.lblSCWeak);
            this.groupBox3.Controls.Add(this.label10);
            this.groupBox3.Location = new System.Drawing.Point(33, 213);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(355, 74);
            this.groupBox3.TabIndex = 3;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "主服覆盖不足";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(16, 21);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(161, 12);
            this.label9.TabIndex = 0;
            this.label9.Text = "主服和邻区与采样点距离都＜";
            // 
            // numDisMax
            // 
            this.numDisMax.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numDisMax.Location = new System.Drawing.Point(183, 16);
            this.numDisMax.Name = "numDisMax";
            this.numDisMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDisMax.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numDisMax.Size = new System.Drawing.Size(60, 21);
            this.numDisMax.TabIndex = 0;
            // 
            // lblSCWeak
            // 
            this.lblSCWeak.AutoSize = true;
            this.lblSCWeak.Location = new System.Drawing.Point(16, 48);
            this.lblSCWeak.Name = "lblSCWeak";
            this.lblSCWeak.Size = new System.Drawing.Size(149, 12);
            this.lblSCWeak.TabIndex = 0;
            this.lblSCWeak.Text = "主服比邻区距离采样点更近";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(249, 17);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(29, 12);
            this.label10.TabIndex = 0;
            this.label10.Text = "米，";
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.label15);
            this.groupBox4.Controls.Add(this.numDisMin2);
            this.groupBox4.Controls.Add(this.lblLNC);
            this.groupBox4.Controls.Add(this.label18);
            this.groupBox4.Location = new System.Drawing.Point(33, 132);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(355, 74);
            this.groupBox4.TabIndex = 2;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "漏配邻区关系";
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(16, 21);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(161, 12);
            this.label15.TabIndex = 0;
            this.label15.Text = "主服和邻区与采样点距离都≥";
            // 
            // numDisMin2
            // 
            this.numDisMin2.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numDisMin2.Location = new System.Drawing.Point(183, 16);
            this.numDisMin2.Name = "numDisMin2";
            this.numDisMin2.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDisMin2.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numDisMin2.Size = new System.Drawing.Size(60, 21);
            this.numDisMin2.TabIndex = 0;
            // 
            // lblLNC
            // 
            this.lblLNC.AutoSize = true;
            this.lblLNC.Location = new System.Drawing.Point(16, 48);
            this.lblLNC.Name = "lblLNC";
            this.lblLNC.Size = new System.Drawing.Size(161, 12);
            this.lblLNC.TabIndex = 0;
            this.lblLNC.Text = "周围{0}米内，有其它LTE基站";
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(249, 21);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(29, 12);
            this.label18.TabIndex = 0;
            this.label18.Text = "米，";
            // 
            // grpNoSite
            // 
            this.grpNoSite.Controls.Add(this.label3);
            this.grpNoSite.Controls.Add(this.numDisMin);
            this.grpNoSite.Controls.Add(this.lblNoSite);
            this.grpNoSite.Controls.Add(this.label4);
            this.grpNoSite.Location = new System.Drawing.Point(33, 51);
            this.grpNoSite.Name = "grpNoSite";
            this.grpNoSite.Size = new System.Drawing.Size(355, 74);
            this.grpNoSite.TabIndex = 1;
            this.grpNoSite.TabStop = false;
            this.grpNoSite.Text = "缺站";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(16, 21);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(161, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "主服和邻区与采样点距离都≥";
            // 
            // numDisMin
            // 
            this.numDisMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numDisMin.Location = new System.Drawing.Point(183, 16);
            this.numDisMin.Name = "numDisMin";
            this.numDisMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDisMin.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numDisMin.Size = new System.Drawing.Size(60, 21);
            this.numDisMin.TabIndex = 0;
            // 
            // lblNoSite
            // 
            this.lblNoSite.AutoSize = true;
            this.lblNoSite.Location = new System.Drawing.Point(16, 48);
            this.lblNoSite.Name = "lblNoSite";
            this.lblNoSite.Size = new System.Drawing.Size(173, 12);
            this.lblNoSite.TabIndex = 0;
            this.lblNoSite.Text = "周围{0}米内，没有其它LTE基站";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(249, 21);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(29, 12);
            this.label4.TabIndex = 0;
            this.label4.Text = "米，";
            // 
            // numRSRPMax
            // 
            this.numRSRPMax.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numRSRPMax.Location = new System.Drawing.Point(174, 24);
            this.numRSRPMax.Name = "numRSRPMax";
            this.numRSRPMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRSRPMax.Properties.MaxValue = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numRSRPMax.Properties.MinValue = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numRSRPMax.Size = new System.Drawing.Size(60, 21);
            this.numRSRPMax.TabIndex = 0;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(240, 29);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(23, 12);
            this.label2.TabIndex = 0;
            this.label2.Text = "dBm";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(31, 29);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(137, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "主服和邻区信号强度都＜";
            // 
            // WeakCoverPnl
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.Controls.Add(this.groupBox1);
            this.Name = "WeakCoverPnl";
            this.Size = new System.Drawing.Size(399, 378);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDisMax2.Properties)).EndInit();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDisMax.Properties)).EndInit();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDisMin2.Properties)).EndInit();
            this.grpNoSite.ResumeLayout(false);
            this.grpNoSite.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numDisMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRSRPMax.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraEditors.SpinEdit numRSRPMax;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private DevExpress.XtraEditors.SpinEdit numDisMin;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label lblNoSite;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.Label label9;
        private DevExpress.XtraEditors.SpinEdit numDisMax;
        private System.Windows.Forms.Label lblSCWeak;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.GroupBox grpNoSite;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.Label label15;
        private DevExpress.XtraEditors.SpinEdit numDisMin2;
        private System.Windows.Forms.Label lblLNC;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label label6;
        private DevExpress.XtraEditors.SpinEdit numDisMax2;
        private System.Windows.Forms.Label lblNCWeak;
        private System.Windows.Forms.Label label20;
    }
}
