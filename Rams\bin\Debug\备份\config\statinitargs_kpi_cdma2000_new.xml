<?xml version="1.0"?>
<Configs>
  <Config name="StatParamCfg">
    <Item name="configs" typeName="IList">
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">CDMA2000参数</Item>
        <Item key="FDesc" />
        <Item typeName="String" key="FName" />
        <Item typeName="IList" key="children">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">基本参数</Item>
            <Item key="FDesc" />
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Fileid</Item>
                <Item key="FDesc" />
                <Item typeName="String" key="FName">Ex_0801</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TestType</Item>
                <Item key="FDesc" />
                <Item typeName="String" key="FName">Ex_0802</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">MS</Item>
                <Item key="FDesc" />
                <Item typeName="String" key="FName">Ex_0803</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">FileTime</Item>
                <Item key="FDesc" />
                <Item typeName="String" key="FName">Ex_0804</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration</Item>
                <Item typeName="String" key="FDesc">总时长</Item>
                <Item typeName="String" key="FName">Ex_0805</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance</Item>
                <Item typeName="String" key="FDesc">总里程</Item>
                <Item typeName="String" key="FName">Ex_0806</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">SampleNum</Item>
                <Item key="FDesc" />
                <Item typeName="String" key="FName">Ex_0807</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_Gis(毫秒)</Item>
                <Item typeName="String" key="FDesc">有经纬度时长</Item>
                <Item typeName="String" key="FName">Ex_084C</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_NoGis(毫秒)</Item>
                <Item typeName="String" key="FDesc">没经纬度时长</Item>
                <Item typeName="String" key="FName">Ex_084D</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">EVDO数据业务参数</Item>
            <Item key="FDesc" />
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">里程、时长</Item>
                <Item key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Duration_CDMA2000_EVDO(毫秒)</Item>
                    <Item typeName="String" key="FDesc">总时长(EVDO业务)</Item>
                    <Item typeName="String" key="FName">Ex_086E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Distance_CDMA2000_EVDO(米)</Item>
                    <Item typeName="String" key="FDesc">总里程(EVDO业务)</Item>
                    <Item typeName="String" key="FName">Ex_086F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Duration_DL_CDMA2000_EVDO(毫秒)</Item>
                    <Item typeName="String" key="FDesc">EVDO-3G下占用时长</Item>
                    <Item typeName="String" key="FName">Ex_0839</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Distance_DL_CDMA2000_EVDO(米)</Item>
                    <Item typeName="String" key="FDesc">EVDO-3G下占用里程</Item>
                    <Item typeName="String" key="FName">Ex_083A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">RxAGC</Item>
                <Item typeName="String" key="FDesc">统计指标CDMA_RxAGC</Item>
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC_(,F100)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090301</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC_[F100_F90)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090302</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC_[F90_F80)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090303</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC_[F85_F80)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090323</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC_[F80_F70)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090304</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC_[F70_F60)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090305</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC_[F60_F50)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090306</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC_[F50,)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090307</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC_(-85,)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09030C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC_(-90,)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090320</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC_[-95,)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09031D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC_SampleNum</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090308</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC_MaxValue</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090309</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC_MinValue</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09030A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC_MeanValue</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09030B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC_(,F100)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09030D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC_[F100_F90)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09030E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC_[F90_F80)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09030F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC_[F85_F80)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090324</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC_[F80_F70)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090310</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC_[F70_F60)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090311</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC_[F60_F50)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090312</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC_[F50,)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090313</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC_(-85,)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090314</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC_(-90,)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090321</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC_[-95,)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09031E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC_(,F100)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090315</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC_[F100_F90)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090316</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC_[F90_F80)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090317</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC_[F85_F80)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090325</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC_[F80_F70)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090318</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC_[F70_F60)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090319</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC_[F60_F50)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09031A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC_[F50,)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09031B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC_(-85,)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09031C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC_(-90,)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090322</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC_[-95,)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09031F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">RxAGC0</Item>
                <Item typeName="String" key="FDesc">统计指标CDMA_RxAGC0</Item>
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC0_(,F100)</Item>
                    <Item typeName="String" key="FDesc">统计场强小于-100的采样点的个数</Item>
                    <Item typeName="String" key="FName">Ex_5E090101</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC0_[F100_F90)</Item>
                    <Item typeName="String" key="FDesc">统计场强在[-100,-90)内采样点的个数</Item>
                    <Item typeName="String" key="FName">Ex_5E090102</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC0_[F90_F80)</Item>
                    <Item typeName="String" key="FDesc">统计场强在[-90,-80)内采样点的个数</Item>
                    <Item typeName="String" key="FName">Ex_5E090103</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC0_[F85_F80)</Item>
                    <Item typeName="String" key="FDesc">统计场强在[-85,-80)内采样点的个数</Item>
                    <Item typeName="String" key="FName">Ex_5E090123</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC0_[F80_F70)</Item>
                    <Item typeName="String" key="FDesc">统计场强在[-80,-70)内采样点的个数</Item>
                    <Item typeName="String" key="FName">Ex_5E090104</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC0_[F70_F60)</Item>
                    <Item typeName="String" key="FDesc">统计场强在[-70,-60)内采样点的个数</Item>
                    <Item typeName="String" key="FName">Ex_5E090105</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC0_[F60_F50)</Item>
                    <Item typeName="String" key="FDesc">统计场强在[-60,-50)内采样点的个数</Item>
                    <Item typeName="String" key="FName">Ex_5E090106</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC0_[F50,)</Item>
                    <Item typeName="String" key="FDesc">统计场强大于等于-50的采样点的个数</Item>
                    <Item typeName="String" key="FName">Ex_5E090107</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC0_(-85,)</Item>
                    <Item typeName="String" key="FDesc">统计场强大于-85的采样点的个数</Item>
                    <Item typeName="String" key="FName">Ex_5E09010C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC0_(-90,)</Item>
                    <Item typeName="String" key="FDesc">统计场强大于-95的采样点的个数</Item>
                    <Item typeName="String" key="FName">Ex_5E090120</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC0_[-95,)</Item>
                    <Item typeName="String" key="FDesc">统计场强大于等于-95的采样点的个数</Item>
                    <Item typeName="String" key="FName">Ex_5E09011D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC0_[-98,)</Item>
                    <Item typeName="String" key="FDesc">统计场强大于等于-98的采样点的个数</Item>
                    <Item typeName="String" key="FName">Ex_5E090156</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC0_SampleNum</Item>
                    <Item typeName="String" key="FDesc">统计所有采样点的个数</Item>
                    <Item typeName="String" key="FName">Ex_5E090108</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC0_MaxValue</Item>
                    <Item typeName="String" key="FDesc">记录场强的最大值</Item>
                    <Item typeName="String" key="FName">Ex_5E090109</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC0_MinValue</Item>
                    <Item typeName="String" key="FDesc">记录场强最小值</Item>
                    <Item typeName="String" key="FName">Ex_5E09010A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC0_MeanValue</Item>
                    <Item typeName="String" key="FDesc">记录场强平均值</Item>
                    <Item typeName="String" key="FName">Ex_5E09010B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC0_(,F100)</Item>
                    <Item typeName="String" key="FDesc">Current_Type标识为覆盖,场强小于-100的覆盖里程</Item>
                    <Item typeName="String" key="FName">Ex_5E09010D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC0_[F100_F90)</Item>
                    <Item typeName="String" key="FDesc">Current_Type标识为覆盖,场强[-100,-90)的覆盖里程</Item>
                    <Item typeName="String" key="FName">Ex_5E09010E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC0_[F90_F80)</Item>
                    <Item typeName="String" key="FDesc">Current_Type标识为覆盖,场强[-90,-80)的覆盖里程</Item>
                    <Item typeName="String" key="FName">Ex_5E09010F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC0_[F85_F80)</Item>
                    <Item typeName="String" key="FDesc">Current_Type标识为覆盖,场强[-85,-80)的覆盖里程</Item>
                    <Item typeName="String" key="FName">Ex_5E090124</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC0_[F80_F70)</Item>
                    <Item typeName="String" key="FDesc">Current_Type标识为覆盖,场强[-80,-70)的覆盖里程</Item>
                    <Item typeName="String" key="FName">Ex_5E090110</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC0_[F70_F60)</Item>
                    <Item typeName="String" key="FDesc">Current_Type标识为覆盖,场强[-70,-60)的覆盖里程</Item>
                    <Item typeName="String" key="FName">Ex_5E090111</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC0_[F60_F50)</Item>
                    <Item typeName="String" key="FDesc">Current_Type标识为覆盖,场强[-60,-50)的覆盖里程</Item>
                    <Item typeName="String" key="FName">Ex_5E090112</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC0_[F50,)</Item>
                    <Item typeName="String" key="FDesc">Current_Type标识为覆盖,场强大于-50的覆盖里程</Item>
                    <Item typeName="String" key="FName">Ex_5E090113</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC0_(-85,)</Item>
                    <Item typeName="String" key="FDesc">Current_Type标识为覆盖,场强大于-85的覆盖里程</Item>
                    <Item typeName="String" key="FName">Ex_5E090114</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC0_(-90,)</Item>
                    <Item typeName="String" key="FDesc">Current_Type标识为覆盖,场强大于-90的覆盖里程</Item>
                    <Item typeName="String" key="FName">Ex_5E090121</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC0_[-95,)</Item>
                    <Item typeName="String" key="FDesc">Current_Type标识为覆盖,场强大于等于-95的覆盖里程</Item>
                    <Item typeName="String" key="FName">Ex_5E09011E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC0_[-98,)</Item>
                    <Item typeName="String" key="FDesc">Current_Type标识为覆盖,场强大于等于-98的覆盖里程</Item>
                    <Item typeName="String" key="FName">Ex_5E090157</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC0_(,F100)</Item>
                    <Item typeName="String" key="FDesc">Current_Type标识为覆盖,场强小于-100的覆盖时长</Item>
                    <Item typeName="String" key="FName">Ex_5E090115</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC0_[F100_F90)</Item>
                    <Item typeName="String" key="FDesc">Current_Type标识为覆盖,场强[-100,-90)的覆盖时长</Item>
                    <Item typeName="String" key="FName">Ex_5E090116</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC0_[F90_F80)</Item>
                    <Item typeName="String" key="FDesc">Current_Type标识为覆盖,场强[-90,-80)的覆盖时长</Item>
                    <Item typeName="String" key="FName">Ex_5E090117</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC0_[F85_F80)</Item>
                    <Item typeName="String" key="FDesc">Current_Type标识为覆盖,场强[-85,-80)的覆盖时长</Item>
                    <Item typeName="String" key="FName">Ex_5E090125</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC0_[F80_F70)</Item>
                    <Item typeName="String" key="FDesc">Current_Type标识为覆盖,场强[-80,-70)的覆盖时长</Item>
                    <Item typeName="String" key="FName">Ex_5E090118</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC0_[F70_F60)</Item>
                    <Item typeName="String" key="FDesc">Current_Type标识为覆盖,场强[-70,-60)的覆盖时长</Item>
                    <Item typeName="String" key="FName">Ex_5E090119</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC0_[F60_F50)</Item>
                    <Item typeName="String" key="FDesc">Current_Type标识为覆盖,场强[-60,-50)的覆盖时长</Item>
                    <Item typeName="String" key="FName">Ex_5E09011A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC0_[F50,)</Item>
                    <Item typeName="String" key="FDesc">Current_Type标识为覆盖,场强大于等于-50的覆盖时长</Item>
                    <Item typeName="String" key="FName">Ex_5E09011B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC0_(-85,)</Item>
                    <Item typeName="String" key="FDesc">Current_Type标识为覆盖,场强大于-85的覆盖时长</Item>
                    <Item typeName="String" key="FName">Ex_5E09011C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC0_(-90,)</Item>
                    <Item typeName="String" key="FDesc">Current_Type标识为覆盖,场强大于-90的覆盖时长</Item>
                    <Item typeName="String" key="FName">Ex_5E090122</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC0_[-95,)</Item>
                    <Item typeName="String" key="FDesc">Current_Type标识为覆盖,场强大于等于-95的覆盖时长</Item>
                    <Item typeName="String" key="FName">Ex_5E09011F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC0_[-98,)</Item>
                    <Item typeName="String" key="FDesc">Current_Type标识为覆盖,场强大于等于-98的覆盖时长</Item>
                    <Item typeName="String" key="FName">Ex_5E090158</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_DMO(脱网里程)</Item>
                    <Item typeName="String" key="FDesc">Current_Type标识为脱网的脱网总里程</Item>
                    <Item typeName="String" key="FName">Ex_5E09013C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_DMO(脱网时长)</Item>
                    <Item typeName="String" key="FDesc">Current_Type标识为脱网的脱网总时长</Item>
                    <Item typeName="String" key="FName">Ex_5E09013D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CFG_LIMIT_Distance_RxAGC0_(,F100)</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值的覆盖里程,场强小于-100</Item>
                    <Item typeName="String" key="FName">Ex_5E090126</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CFG_LIMIT_Distance_RxAGC0_[F100_F90)</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值的覆盖里程,场强[-100,-90)</Item>
                    <Item typeName="String" key="FName">Ex_5E090127</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CFG_LIMIT_Distance_RxAGC0_[F90_F80)</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值的覆盖里程,场强[-90,-80)</Item>
                    <Item typeName="String" key="FName">Ex_5E090128</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CFG_LIMIT_Distance_RxAGC0_[F85_F80)</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值的覆盖里程,场强[-85,-80)</Item>
                    <Item typeName="String" key="FName">Ex_5E090130</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CFG_LIMIT_Distance_RxAGC0_[F80_F70)</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值的覆盖里程,场强[-80,-70)</Item>
                    <Item typeName="String" key="FName">Ex_5E090129</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CFG_LIMIT_Distance_RxAGC0_[F70_F60)</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值的覆盖里程,场强[-70,-60)</Item>
                    <Item typeName="String" key="FName">Ex_5E09012A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CFG_LIMIT_Distance_RxAGC0_[F60_F50)</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值的覆盖里程,场强[-60,-50)</Item>
                    <Item typeName="String" key="FName">Ex_5E09012B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CFG_LIMIT_Distance_RxAGC0_[F50,)</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值的覆盖里程,场强大于等于-50</Item>
                    <Item typeName="String" key="FName">Ex_5E09012C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CFG_LIMIT_Distance_RxAGC0_(-85,)</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值的覆盖里程,场强大于-85</Item>
                    <Item typeName="String" key="FName">Ex_5E09012D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CFG_LIMIT_Distance_RxAGC0_(-90,)</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值的覆盖里程,场强大于-90</Item>
                    <Item typeName="String" key="FName">Ex_5E09012E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CFG_LIMIT_Distance_RxAGC0_[-95,)</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值的覆盖里程,场强大于等于-95</Item>
                    <Item typeName="String" key="FName">Ex_5E09012F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CFG_LIMIT_Distance_RxAGC0_[-98,)</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值的覆盖里程,场强大于等于-98</Item>
                    <Item typeName="String" key="FName">Ex_5E090159</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CFG_LIMIT_Duration_RxAGC0_(,F100)</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值的覆盖时长,场强小于-100</Item>
                    <Item typeName="String" key="FName">Ex_5E090131</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CFG_LIMIT_Duration_RxAGC0_[F100_F90)</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值的覆盖时长,场强[-100,-90)</Item>
                    <Item typeName="String" key="FName">Ex_5E090132</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CFG_LIMIT_Duration_RxAGC0_[F90_F80)</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值的覆盖时长,场强[-90,-80)</Item>
                    <Item typeName="String" key="FName">Ex_5E090133</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CFG_LIMIT_Duration_RxAGC0_[F85_F80)</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值的覆盖时长,场强[-85,-80)</Item>
                    <Item typeName="String" key="FName">Ex_5E09013B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CFG_LIMIT_Duration_RxAGC0_[F80_F70)</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值的覆盖时长,场强[-80,-70)</Item>
                    <Item typeName="String" key="FName">Ex_5E090134</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CFG_LIMIT_Duration_RxAGC0_[F70_F60)</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值的覆盖时长,场强[-70,-60)</Item>
                    <Item typeName="String" key="FName">Ex_5E090135</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CFG_LIMIT_Duration_RxAGC0_[F60_F50)</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值的覆盖时长,场强[-60,-50)</Item>
                    <Item typeName="String" key="FName">Ex_5E090136</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CFG_LIMIT_Duration_RxAGC0_[F50,)</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值的覆盖时长,场强大于等于-50</Item>
                    <Item typeName="String" key="FName">Ex_5E090137</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CFG_LIMIT_Duration_RxAGC0_(-85,)</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值的覆盖时长,场强大于-85</Item>
                    <Item typeName="String" key="FName">Ex_5E090138</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CFG_LIMIT_Duration_RxAGC0_(-90,)</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值的覆盖时长,场强大于-90</Item>
                    <Item typeName="String" key="FName">Ex_5E090139</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CFG_LIMIT_Duration_RxAGC0_[-95,)</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值的覆盖时长,场强大于等于-95</Item>
                    <Item typeName="String" key="FName">Ex_5E09013A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CFG_LIMIT_Duration_RxAGC0_[-98,)</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值的覆盖时长,场强大于等于-98</Item>
                    <Item typeName="String" key="FName">Ex_5E09015A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CFG_LIMIT_Distance_DMO(脱网里程)</Item>
                    <Item typeName="String" key="FDesc">场强或时长均违背经验值,脱网总里程</Item>
                    <Item typeName="String" key="FName">Ex_5E09013E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CFG_LIMIT_Duration_DMO(脱网时长)</Item>
                    <Item typeName="String" key="FDesc">场强或时长均违背经验值,脱网总时长</Item>
                    <Item typeName="String" key="FName">Ex_5E09013F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_VOICE_CFG_LIMIT_RXAGC0_[SINR_F6,)_[RXAGC0_F95,)_(,TxAGC_15]_DISTANCE</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值,以RXAGC0为基准,沿用TxAGC和SINR,RXAGC0大于等于-95，TxAGC小于等于15且SINR大于等于-6的里程</Item>
                    <Item typeName="String" key="FName">Ex_5E090168</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_VOICE_CFG_LIMIT_RXAGC0_[SINR_F6,)_[RXAGC0_F95,)_(,TxAGC_15]_DURATION</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值,以RXAGC0为基准,沿用TxAGC和SINR,RXAGC0大于等于-95，TxAGC小于等于15且SINR大于等于-6的时长</Item>
                    <Item typeName="String" key="FName">Ex_5E090167</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_VOICE_CFG_LIMIT_RXAGC0_[SINR_F6,)_[RXAGC0_F95,)_(,TxAGC_15]_SAMPLE</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值,以RXAGC0为基准,沿用TxAGC和SINR,RXAGC0大于等于-95，TxAGC小于等于15且SINR大于等于-6的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_5E090166</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_VOICE_CFG_LIMIT_RXAGC0_[SINR_F6,)_[RXAGC0_F90,)_(,TxAGC_15]_DISTANCE</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值,以RXAGC0为基准,沿用TxAGC和SINR,RXAGC0大于等于-90，TxAGC小于等于15且SINR大于等于-6的里程</Item>
                    <Item typeName="String" key="FName">Ex_5E090165</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_VOICE_CFG_LIMIT_RXAGC0_[SINR_F6,)_[RXAGC0_F90,)_(,TxAGC_15]_DURATION</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值,以RXAGC0为基准,沿用TxAGC和SINR,RXAGC0大于等于-90，TxAGC小于等于15且SINR大于等于-6的时长</Item>
                    <Item typeName="String" key="FName">Ex_5E090164</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_VOICE_CFG_LIMIT_RXAGC0_[SINR_F6,)_[RXAGC0_F90,)_(,TxAGC_15]_SAMPLE</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值,以RXAGC0为基准,沿用TxAGC和SINR,RXAGC0大于等于-90，TxAGC小于等于15且SINR大于等于-6的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_5E090163</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_VOICE_CFG_LIMIT_RXAGC0_[SINR_F6,)_[RXAGC0_F85,)_(,TxAGC_15]_DISTANCE</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值,以RXAGC0为基准,沿用TxAGC和SINR,RXAGC0大于等于-85，TxAGC小于等于15且SINR大于等于-6的里程</Item>
                    <Item typeName="String" key="FName">Ex_5E090162</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_VOICE_CFG_LIMIT_RXAGC0_[SINR_F6,)_[RXAGC0_F85,)_(,TxAGC_15]_DURATION</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值,以RXAGC0为基准,沿用TxAGC和SINR,RXAGC0大于等于-85，TxAGC小于等于15且SINR大于等于-6的时长</Item>
                    <Item typeName="String" key="FName">Ex_5E090161</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_VOICE_CFG_LIMIT_RXAGC0_[SINR_F6,)_[RXAGC0_F85,)_(,TxAGC_15]_SAMPLE</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值,以RXAGC0为基准,沿用TxAGC和SINR,RXAGC0大于等于-85，TxAGC小于等于15且SINR大于等于-6的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_5E090160</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_VOICE_CFG_LIMIT_RXAGC0_[SINR_F6,)_[RXAGC0_F80,)_(,TxAGC_15]_DISTANCE</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值,以RXAGC0为基准,沿用TxAGC和SINR,RXAGC0大于等于-80，TxAGC小于等于15且SINR大于等于-6的里程</Item>
                    <Item typeName="String" key="FName">Ex_5E09015F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_VOICE_CFG_LIMIT_RXAGC0_[SINR_F6,)_[RXAGC0_F80,)_(,TxAGC_15]_DURATION</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值,以RXAGC0为基准,沿用TxAGC和SINR,RXAGC0大于等于-80，TxAGC小于等于15且SINR大于等于-6的时长</Item>
                    <Item typeName="String" key="FName">Ex_5E09015E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_VOICE_CFG_LIMIT_RXAGC0_[SINR_F6,)_[RXAGC0_F80,)_(,TxAGC_15]_SAMPLE</Item>
                    <Item typeName="String" key="FDesc">场强,时长均符合经验值,以RXAGC0为基准,沿用TxAGC和SINR,RXAGC0大于等于-80，TxAGC小于等于15且SINR大于等于-6的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_5E09015D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_PREV_Distance_RxAGC0_(,F100)</Item>
                    <Item typeName="String" key="FDesc">不剔除脱网路段的覆盖里程,场强小于-100</Item>
                    <Item typeName="String" key="FName">Ex_5E090140</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_PREV_Distance_RxAGC0_[F100_F90)</Item>
                    <Item typeName="String" key="FDesc">不剔除脱网路段的覆盖里程,场强[-100,-90)</Item>
                    <Item typeName="String" key="FName">Ex_5E090141</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_PREV_Distance_RxAGC0_[F90_F80)</Item>
                    <Item typeName="String" key="FDesc">不剔除脱网路段的覆盖里程,场强[-90,-80)</Item>
                    <Item typeName="String" key="FName">Ex_5E090142</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_PREV_Distance_RxAGC0_[F85_F80)</Item>
                    <Item typeName="String" key="FDesc">不剔除脱网路段的覆盖里程,场强[-85,-80)</Item>
                    <Item typeName="String" key="FName">Ex_5E090143</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_PREV_Distance_RxAGC0_[F80_F70)</Item>
                    <Item typeName="String" key="FDesc">不剔除脱网路段的覆盖里程,场强[-80,-70)</Item>
                    <Item typeName="String" key="FName">Ex_5E090144</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_PREV_Distance_RxAGC0_[F70_F60)</Item>
                    <Item typeName="String" key="FDesc">不剔除脱网路段的覆盖里程,场强[-70,-60)</Item>
                    <Item typeName="String" key="FName">Ex_5E090145</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_PREV_Distance_RxAGC0_[F60_F50)</Item>
                    <Item typeName="String" key="FDesc">不剔除脱网路段的覆盖里程,场强[-60,-50)</Item>
                    <Item typeName="String" key="FName">Ex_5E090146</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_PREV_Distance_RxAGC0_[F50,)</Item>
                    <Item typeName="String" key="FDesc">不剔除脱网路段的覆盖里程,场强大于等于-50</Item>
                    <Item typeName="String" key="FName">Ex_5E090147</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_PREV_Distance_RxAGC0_(F85,)</Item>
                    <Item typeName="String" key="FDesc">不剔除脱网路段的覆盖里程,场强大于-85</Item>
                    <Item typeName="String" key="FName">Ex_5E090150</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_PREV_Distance_RxAGC0_(F90,)</Item>
                    <Item typeName="String" key="FDesc">不剔除脱网路段的覆盖里程,场强大于-90</Item>
                    <Item typeName="String" key="FName">Ex_5E090152</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_PREV_Distance_RxAGC0_[F95,)</Item>
                    <Item typeName="String" key="FDesc">不剔除脱网路段的覆盖里程,场强大于等于-95</Item>
                    <Item typeName="String" key="FName">Ex_5E090154</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_PREV_Distance_RxAGC0_[F98,)</Item>
                    <Item typeName="String" key="FDesc">不剔除脱网路段的覆盖里程,场强大于等于-98</Item>
                    <Item typeName="String" key="FName">Ex_5E09015B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_PREV_Duration_RxAGC0_(,F100)</Item>
                    <Item typeName="String" key="FDesc">不剔除脱网路段的覆盖时长,场强小于-100</Item>
                    <Item typeName="String" key="FName">Ex_5E090148</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_PREV_Duration_RxAGC0_[F100_F90)</Item>
                    <Item typeName="String" key="FDesc">不剔除脱网路段的覆盖时长,场强[-100,-90)</Item>
                    <Item typeName="String" key="FName">Ex_5E090149</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_PREV_Duration_RxAGC0_[F90_F80)</Item>
                    <Item typeName="String" key="FDesc">不剔除脱网路段的覆盖时长,场强[-90,-80)</Item>
                    <Item typeName="String" key="FName">Ex_5E09014A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_PREV_Duration_RxAGC0_[F85_F80)</Item>
                    <Item typeName="String" key="FDesc">不剔除脱网路段的覆盖时长,场强[-85,-80)</Item>
                    <Item typeName="String" key="FName">Ex_5E09014B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_PREV_Duration_RxAGC0_[F80_F70)</Item>
                    <Item typeName="String" key="FDesc">不剔除脱网路段的覆盖时长,场强[-80,-70)</Item>
                    <Item typeName="String" key="FName">Ex_5E09014C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_PREV_Duration_RxAGC0_[F70_F60)</Item>
                    <Item typeName="String" key="FDesc">不剔除脱网路段的覆盖时长,场强[-70,-60)</Item>
                    <Item typeName="String" key="FName">Ex_5E09014D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_PREV_Duration_RxAGC0_[F60_F50)</Item>
                    <Item typeName="String" key="FDesc">不剔除脱网路段的覆盖时长,场强[-60,-50)</Item>
                    <Item typeName="String" key="FName">Ex_5E09014E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_PREV_Duration_RxAGC0_[F50,)</Item>
                    <Item typeName="String" key="FDesc">不剔除脱网路段的覆盖时长,场强大于等于-50</Item>
                    <Item typeName="String" key="FName">Ex_5E09014F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_PREV_Duration_RxAGC0_(F85,)</Item>
                    <Item typeName="String" key="FDesc">不剔除脱网路段的覆盖时长,场强大于-85</Item>
                    <Item typeName="String" key="FName">Ex_5E090151</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_PREV_Duration_RxAGC0_(F90,)</Item>
                    <Item typeName="String" key="FDesc">不剔除脱网路段的覆盖时长,场强大于-90</Item>
                    <Item typeName="String" key="FName">Ex_5E090153</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_PREV_Duration_RxAGC0_[F95,)</Item>
                    <Item typeName="String" key="FDesc">不剔除脱网路段的覆盖时长,场强大于等于-95</Item>
                    <Item typeName="String" key="FName">Ex_5E090155</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_PREV_Duration_RxAGC0_[F98,)</Item>
                    <Item typeName="String" key="FDesc">不剔除脱网路段的覆盖时长,场强大于等于-98</Item>
                    <Item typeName="String" key="FName">Ex_5E09015C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">RxAGC1</Item>
                <Item typeName="String" key="FDesc">统计指标CDMA_RxAGC1</Item>
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC1_(,F100)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090201</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC1_[F100_F90)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090202</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC1_[F90_F80)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090203</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC1_[F85_F80)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090223</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC1_[F80_F70)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090204</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC1_[F70_F60)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090205</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC1_[F60_F50)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090206</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC1_[F50,)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090207</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC1_(-85,)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09020C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC1_(-90,)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090220</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC1_[-95,)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09021D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC1_SampleNum</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090208</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC1_MaxValue</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090209</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC1_MinValue</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09020A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxAGC1_MeanValue</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09020B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC1_(,F100)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09020D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC1_[F100_F90)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09020E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC1_[F90_F80)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09020F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC1_[F85_F80)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090224</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC1_[F80_F70)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090210</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC1_[F70_F60)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090211</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC1_[F60_F50)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090212</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC1_[F50,)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090213</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC1_(-85,)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090214</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC1_(-90,)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090221</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Distance_RxAGC1_[-95,)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09021E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC1_(,F100)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090215</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC1_[F100_F90)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090216</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC1_[F90_F80)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090217</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC1_[F85_F80)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090225</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC1_[F80_F70)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090218</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC1_[F70_F60)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090219</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC1_[F60_F50)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09021A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC1_[F50,)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09021B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC1_(-85,)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09021C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC1_(-90,)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090222</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Duration_RxAGC1_[-95,)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09021F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TxAGC</Item>
                <Item typeName="String" key="FDesc">未统计,若涉及需要请联系研发人员,若不需要可考虑删除</Item>
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxAGC_(,F20)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090501</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxAGC_[F20_F15)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090502</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxAGC_[F15_F10)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090503</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxAGC_[F10_F5)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090504</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxAGC_[F5_0)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090505</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxAGC_[0_5)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090506</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxAGC_[5_10)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090507</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxAGC_[10_15)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090508</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxAGC_[15_20)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090509</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxAGC_[20_30)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09050A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxAGC_[30,)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09050B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxAGC_SampleNum</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09050C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxAGC_MaxValue</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09050D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxAGC_MinValue</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09050E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxAGC_MeanValue</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09050F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">APP</Item>
                <Item typeName="String" key="FDesc">上传下载速率指标：根据APP_type确定上传下载类型,APP_DataStatus_DL所处网段,对APP_TransferedSize、APP_TransferedTime、APP_Speed进行统计</Item>
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CDMA1X_FTP_DOWNLOAD_APP_BYTE</Item>
                    <Item typeName="String" key="FDesc">EVDO切在CDMA1X下的下载总字节数(单位Byte)</Item>
                    <Item typeName="String" key="FName">Ex_050906020101</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CDMA1X_FTP_DOWNLOAD_APP_TIME</Item>
                    <Item typeName="String" key="FDesc">EVDO切在CDMA1X下的下载总时长(单位ms)</Item>
                    <Item typeName="String" key="FName">Ex_050906020102</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CDMA1X_FTP_DOWNLOAD_APP_SAMPLE</Item>
                    <Item typeName="String" key="FDesc">EVDO切在CDMA1X下的下载总采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050906020103</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CDMA1X_FTP_DOWNLOAD_APP_SPEED_[0_50)kbps的采样点数</Item>
                    <Item typeName="String" key="FDesc">EVDO切在CDMA1X下,下载速率[0,50kbps)</Item>
                    <Item typeName="String" key="FName">Ex_050906020104</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CDMA1X_FTP_DOWNLOAD_APP_SPEED_[50_150)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在CDMA1X下,下载速率[50kbps,150kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050906020105</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CDMA1X_FTP_DOWNLOAD_APP_SPEED_(,200)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在CDMA1X下,下载速率小于200kbps的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_05090602011B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CDMA1X_FTP_DOWNLOAD_APP_SPEED_[150_300)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在CDMA1X下,下载速率[150kbps,300kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050906020106</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CDMA1X_FTP_DOWNLOAD_APP_SPEED_[300_500)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在CDMA1X下,下载速率[300kbps,500kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050906020107</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CDMA1X_FTP_DOWNLOAD_APP_SPEED_[500,)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在CDMA1X下,下载速率大于等于500kbps的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050906020108</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CDMA1X_FTP_DOWNLOAD_APP_SPEED_[700,)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在CDMA1X下,下载速率大于等于700kbps的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050906020110</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CDMA1X_FTP_UPLOAD_APP_BYTE</Item>
                    <Item typeName="String" key="FDesc">EVDO切在CDMA1X下的上传总字节数(单位Byte)</Item>
                    <Item typeName="String" key="FName">Ex_050906030201</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CDMA1X_FTP_UPLOAD_APP_TIME</Item>
                    <Item typeName="String" key="FDesc">EVDO切在CDMA1X下的上传总时长(单位ms)</Item>
                    <Item typeName="String" key="FName">Ex_050906030202</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CDMA1X_FTP_UPLOAD_APP_SAMPLE</Item>
                    <Item typeName="String" key="FDesc">EVDO切在CDMA1X下的上传总采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050906030203</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CDMA1X_FTP_UPLOAD_APP_SPEED_[0_50)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在CDMA1X下,上传速率[0,50kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050906030204</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CDMA1X_FTP_UPLOAD_APP_SPEED_[50_150)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在CDMA1X下,上传速率[50kbps,150kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050906030205</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CDMA1X_FTP_UPLOAD_APP_SPEED_(,200)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在CDMA1X下,上传速率小于200kbps的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_05090603021B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CDMA1X_FTP_UPLOAD_APP_SPEED_[150_300)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在CDMA1X下,上传速率[150kbps,300kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050906030206</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CDMA1X_FTP_UPLOAD_APP_SPEED_[300_500)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在CDMA1X下,上传速率[300kbps,500kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050906030207</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CDMA1X_FTP_UPLOAD_APP_SPEED_[500,)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在CDMA1X下,上传速率大于等于500kbps的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050906030208</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CDMA1X_FTP_UPLOAD_APP_SPEED_[700,)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在CDMA1X下,上传速率大于等于700kbps的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050906030210</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_EVDO_FTP_DOWNLOAD_APP_BYTE</Item>
                    <Item typeName="String" key="FDesc">EVDO切在EVDO下的下载总字节数(单位Byte)</Item>
                    <Item typeName="String" key="FName">Ex_050907020101</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_EVDO_FTP_DOWNLOAD_APP_TIME</Item>
                    <Item typeName="String" key="FDesc">EVDO切在EVDO下的下载总时长(单位ms)</Item>
                    <Item typeName="String" key="FName">Ex_050907020102</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_EVDO_FTP_DOWNLOAD_APP_SAMPLE</Item>
                    <Item typeName="String" key="FDesc">EVDO切在EVDO下的下载总采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050907020103</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_EVDO_FTP_DOWNLOAD_APP_SPEED_[0_50)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在EVDO下,下载速率[0,50kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050907020104</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_EVDO_FTP_DOWNLOAD_APP_SPEED_[50_150)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在EVDO下,下载速率[50kbps,150kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050907020105</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_EVDO_FTP_DOWNLOAD_APP_SPEED_(,200)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在EVDO下,下载速率小于200kbps的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_05090702011B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_EVDO_FTP_DOWNLOAD_APP_SPEED_[150_300)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在EVDO下,下载速率[150kbps,300kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050907020106</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_EVDO_FTP_DOWNLOAD_APP_SPEED_[300_500)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在EVDO下,下载速率[300kbps,500kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050907020107</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_EVDO_FTP_DOWNLOAD_APP_SPEED_[500,)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在EVDO下,下载速率大于等于500kbps的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050907020108</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_EVDO_FTP_DOWNLOAD_APP_SPEED_[700,)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在EVDO下,下载速率大于等于700kbps的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050907020110</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_EVDO_FTP_UPLOAD_APP_BYTE</Item>
                    <Item typeName="String" key="FDesc">EVDO切在EVDO下的上传总字节数(Byte)</Item>
                    <Item typeName="String" key="FName">Ex_050907030201</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_EVDO_FTP_UPLOAD_APP_TIME</Item>
                    <Item typeName="String" key="FDesc">EVDO切在EVDO下的上传总时长（单位ms）</Item>
                    <Item typeName="String" key="FName">Ex_050907030202</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_EVDO_FTP_UPLOAD_APP_SAMPLE</Item>
                    <Item typeName="String" key="FDesc">EVDO切在EVDO下的上传总采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050907030203</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_EVDO_FTP_UPLOAD_APP_SPEED_[0_50)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在EVDO下,上传速率[0,50kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050907030204</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_EVDO_FTP_UPLOAD_APP_SPEED_[50_150)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在EVDO下,上传速率[50kbps,150kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050907030205</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_EVDO_FTP_UPLOAD_APP_SPEED_(,200)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在EVDO下,上传速率小于200kbps的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_05090703021B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_EVDO_FTP_UPLOAD_APP_SPEED_[150_300)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在EVDO下,上传速率[150kbps,300kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050907030206</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_EVDO_FTP_UPLOAD_APP_SPEED_[300_500)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在EVDO下,上传速率[300kbps,500kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050907030207</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_EVDO_FTP_UPLOAD_APP_SPEED_[500,)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在EVDO下,上传速率大于等于500kbps的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050907030208</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_EVDO_FTP_UPLOAD_APP_SPEED_[700,)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO切在EVDO下,上传速率大于等于700kbps的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050907030210</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_BYTE</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下载总字节数（单位Byte）</Item>
                    <Item typeName="String" key="FName">Ex_050964020101</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_TIME</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下载总时长（单位ms）</Item>
                    <Item typeName="String" key="FName">Ex_050964020102</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_SAMPLE</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下载总采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964020103</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_SPEED_[0_50)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,下载速率[0,50kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964020104</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_SPEED_[50_150)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,下载速率[50kbps,150kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964020105</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_SPEED_(,200)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,下载速率小于200kbps的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_05096402011B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_SPEED_[150_300)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,下载速率[150kbps,300kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964020106</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_SPEED_[300_500)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,下载速率[300kbps,500kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964020107</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_SPEED_[500,)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,下载速率大于等于500kbps的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964020108</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_SPEED_[0_50)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,下载速率[0,50kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964020104</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_SPEED_[0_70)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,下载速率[0,70kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964020109</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_SPEED_[70_90)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,下载速率[70kbps,90kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_05096402010A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_SPEED_[80_90)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,下载速率[80kbps,90kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964020111</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_SPEED_[90_110)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,下载速率[90kbps,110kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_05096402010B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_SPEED_[110_150)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,下载速率[110kbps,150kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_05096402010C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_SPEED_[60_70)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,下载速率[60,70kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_05096402011C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_SPEED_[0,0d5M)</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,下载速率[0,0.5M)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964020112</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_SPEED_[0d5M,1M)</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,下载速率[0.5M,1M)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964020113</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_SPEED_[1M,1d5M)</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,下载速率[1M,1.5M)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964020114</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_SPEED_[1d5M,2M)</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,下载速率[1.5M,2M)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964020115</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_SPEED_[2M,2d5M)</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,下载速率[2M,2.5M)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964020116</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_SPEED_[2d5M,3M)</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,下载速率[2.5M,3M)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964020117</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_SPEED_[3M,)</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,下载速率大于3M的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964020118</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_SPEED_[100,)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,下载速率大于等于100kbps的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964020119</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_SPEED_[200,)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,下载速率大于等于200kbps的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_05096402011A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_SPEED_[700,)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,下载速率大于等于700kbps的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964020110</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_APP_SPEED_(,300)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,下载速率小于300kbps的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_05096402010D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_Distance_APP_SPEED_(,300)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,下载速率小于300kbps的里程</Item>
                    <Item typeName="String" key="FName">Ex_05096402010E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_DOWNLOAD_Distance_APP_SPEED_[500,)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,下载速率大于等于500kbps的里程</Item>
                    <Item typeName="String" key="FName">Ex_05096402010F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_APP_BYTE</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下的上传总字节数(单位Byte)</Item>
                    <Item typeName="String" key="FName">Ex_050964030201</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_APP_TIME</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下的上传总时长（单位ms）</Item>
                    <Item typeName="String" key="FName">Ex_050964030202</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_APP_SAMPLE</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下的上传总采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964030203</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_APP_SPEED_[0_50)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,上传速率[0,50kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964030204</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_APP_SPEED_[50_150)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,上传速率[50kbps,150kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964030205</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_APP_SPEED_(,200)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,上传速率小于200kbps的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_05096403021B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_APP_SPEED_[150_300)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,上传速率[150kbps,300kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964030206</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_APP_SPEED_[300_500)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,上传速率[300kbps,500kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964030207</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_APP_SPEED_[500,)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,上传速率大于等于500kbps的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964030208</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_APP_SPEED_[0_70)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,上传速率[0,70kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964030209</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_APP_SPEED_[70_90)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,上传速率[70kbps,90kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_05096403020A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_APP_SPEED_[80_90)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,上传速率[80kbps,90kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964030211</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_APP_SPEED_[90_110)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,上传速率[90kbps,110kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_05096403020B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_APP_SPEED_[110_150)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,上传速率[110kbps,150kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_05096403020C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_APP_SPEED_[60_70)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,上传速率[60,70kbps)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_05096403021C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_APP_SPEED_[0,0d5M)</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,上传速率[0,0.5M)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964030212</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_APP_SPEED_[0d5M,1M)</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,上传速率[0.5M,1M)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964030213</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_APP_SPEED_[1M,1d5M)</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,上传速率[1M,1.5M)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964030214</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_APP_SPEED_[1d5M,2M)</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,上传速率[1.5M,2M)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964030215</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_APP_SPEED_[2M,2d5M)</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,上传速率[2M,2.5M)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964030216</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_APP_SPEED_[2d5M,3M)</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,上传速率[2.5M,3M)的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964030217</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_UPLOAD_APP_SPEED_[3M,)</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,上传速率大于等于3M的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964030218</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_APP_SPEED_[100,)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,上传速率大于等于100kbps的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964030219</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_APP_SPEED_[200,)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,上传速率大于等于200kbps的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_05096403021A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_APP_SPEED_[700,)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,上传速率大于等于700kbps的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_050964030210</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_APP_SPEED_(,300)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,上传速率小于300kbps的采样点数</Item>
                    <Item typeName="String" key="FName">Ex_05096403020D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_Distance_APP_SPEED_(,300)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,上传速率小于300kbps的里程</Item>
                    <Item typeName="String" key="FName">Ex_05096403020E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TOTAL_FTP_UPLOAD_Distance_APP_SPEED_[500,)kbps</Item>
                    <Item typeName="String" key="FDesc">EVDO全网下,上传速率大于等于500kbps的里程</Item>
                    <Item typeName="String" key="FName">Ex_05096403020F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">SINR</Item>
                <Item typeName="String" key="FDesc">统计指标fEVDO_Total_SiNR,统计在GridStatIamge_StatConfig.xls</Item>
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Total_SiNR_[F70_F10)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090401</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Total_SiNR_[F10_F5)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090402</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Total_SiNR_[F5_0)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090403</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Total_SiNR_[0_3)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090404</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Total_SiNR_[3_6)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090405</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Total_SiNR_[6_8)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090406</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Total_SiNR_[8_10)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090407</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Total_SiNR_[10_30]</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090408</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Total_SiNR_SampleNum</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09040C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Total_SiNR_MaxValue</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09040D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Total_SiNR_MinValue</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09040E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_Total_SiNR_MeanValue</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09040F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">RxPER</Item>
                <Item typeName="String" key="FDesc">统计指标EVDO_RxPER,统计在GridStatIamge_StatConfig.xls</Item>
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxPER_[0_1)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090601</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxPER_[1_2)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090602</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxPER_[2_3)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090603</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxPER_[3_4)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090604</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxPER_[4_5)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090605</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxPER_[5_6)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090606</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxPER_[6_7)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090607</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxPER_[7_8)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090608</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxPER_[8_9)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090609</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxPER_9_10</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09060A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxPER_[10_100]</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09060B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxPER_SampleNum</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09060C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxPER_MaxValue</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09060D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxPER_MinValue</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09060E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxPER_MeanValue</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09060F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TxPER</Item>
                <Item typeName="String" key="FDesc">统计指标EVDO_TxPER,统计在GridStatIamge_StatConfig.xls</Item>
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxPER_[0_1)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090701</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxPER_[1_2)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090702</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxPER_[2_3)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090703</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxPER_[3_4)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090704</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxPER_[4_5)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090705</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxPER_[5_6)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090706</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxPER_[6_7)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090707</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxPER_[7_8)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090708</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxPER_[8_9)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090709</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxPER_[9_10)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09070A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxPER_[10_100]</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09070B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxPER_SampleNum</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09070C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxPER_MaxValue</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09070D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxPER_MinValue</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09070E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxPER_MeanValue</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09070F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">RxRLP</Item>
                <Item typeName="String" key="FDesc">统计指标EVDO_EV_RXRLPThr</Item>
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA1X_RxRLP_THR_SAMPLE</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_09090601</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA1X_RxRLP_THR_SUM(Kbyte)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_09090602</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA1X_RxRLP_THR_[0_50)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_09090605</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA1X_RxRLP_THR_[50_150)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_09090606</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA1X_RxRLP_THR_[150_300)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_09090607</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA1X_RxRLP_THR_[300_500)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_09090608</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA1X_RxRLP_THR_[500,)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_09090609</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxRLP_THR_SAMPLE</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_09090701</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxRLP_THR_SUM(Kbyte)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_09090702</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxRLP_THR_[0_50)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_09090705</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxRLP_THR_[50_150)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_09090706</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxRLP_THR_[150_300)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_09090707</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxRLP_THR_[300_500)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_09090708</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_RxRLP_THR_[500,)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_09090709</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">TxRLP</Item>
                <Item typeName="String" key="FDesc">统计指标EVDO_EV_TXRLPThr</Item>
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA1X_TxRLP_THR_SAMPLE</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_09090603</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA1X_TxRLP_THR_SUM(Kbyte)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_09090604</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA1X_TxRLP_THR_[0_50)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_0909060A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA1X_TxRLP_THR_[50_150)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_0909060B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA1X_TxRLP_THR_[150_300)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_0909060C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA1X_TxRLP_THR_[300_500)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_0909060D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA1X_TxRLP_THR_[500,)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_0909060E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxRLP_THR_SAMPLE</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_09090703</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxRLP_THR_SUM(Kbyte)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_09090704</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxRLP_THR_[0_50)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_0909070A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxRLP_THR_[50_150)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_0909070B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxRLP_THR_[150_300)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_0909070C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxRLP_THR_[300_500)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_0909070D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_TxRLP_THR_[500,)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_0909070E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">DRC</Item>
                <Item typeName="String" key="FDesc">统计指标EVDO_DRC_Rate</Item>
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA1X_DRC_RATE_SAMPLE</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_0A090601</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA1X_DRC_RATE_SUM(Kbyte)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_0A090602</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA1X_DRC_RATE_[0_50)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_0A090603</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA1X_DRC_RATE_[50_150)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_0A090604</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA1X_DRC_RATE_[150_300)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_0A090605</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA1X_DRC_RATE_[300_500)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_0A090606</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA1X_DRC_RATE_[500,)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_0A090607</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_DRC_RATE_SAMPLE</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_0A090701</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_DRC_RATE_SUM(Kbyte)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_0A090702</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_DRC_RATE_[0_50)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_0A090703</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_DRC_RATE_[50_150)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_0A090704</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_DRC_RATE_[150_300)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_0A090705</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_DRC_RATE_[300_500)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_0A090706</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_DRC_RATE_[500,)kbps</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_0A090707</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">CoverRate</Item>
                <Item typeName="String" key="FDesc">wmode为1,2时,以CDMA_TotalEcIo、CDMA_TxPower为基准,沿用fCDMA_RxAGC；wmode为3,4时,以EVDO_RxAGC0、EVDO_TxAGC为基准,沿用fEVDO_Total_SiNR</Item>
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CoverRate_SampleNum</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090801</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CoverRate_(SINR_F6,)_(RxAGC_F90,)_(,TxAGC_15)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090802</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CoverRate_(SINR_F6,)_(RxAGC_F95,)_(,TxAGC_15)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090805</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CoverRate_(SINR_F12,)_(RxAGC_F90,)_(,TxAGC_15)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090810</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CoverRate_Duration_(SINR_F6,)_(RxAGC_F90,)_(,TxAGC_15)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090803</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CoverRate_Duration_(SINR_F6,)_(RxAGC_F95,)_(,TxAGC_15)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090806</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CoverRate_(SINR_F6,)_(RxAGC_F80,)_(,TxAGC_15)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090813</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CoverRate_Duration_(SINR_F6,)_(RxAGC_F80,)_(,TxAGC_15)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090815</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CoverRate_Distance_(SINR_F6,)_(RxAGC_F80,)_(,TxAGC_15)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090814</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CoverRate_(SINR_F6,)_(RxAGC_F85,)_(,TxAGC_15)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090816</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CoverRate_Duration_(SINR_F6,)_(RxAGC_F85,)_(,TxAGC_15)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090818</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CoverRate_Distance_(SINR_F6,)_(RxAGC_F85,)_(,TxAGC_15)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090817</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CoverRate_Duration_(SINR_F12,)_(RxAGC_F90,)_(,TxAGC_15)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090811</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CoverRate_Distance_(SINR_F6,)_(RxAGC_F90,)_(,TxAGC_15)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090804</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CoverRate_Distance_(SINR_F6,)_(RxAGC_F95,)_(,TxAGC_15)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090807</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">EVDO_CoverRate_Distance_(SINR_F12,)_(RxAGC_F90,)_(,TxAGC_15)</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090812</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA_CoverRate_SampleNum</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090808</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA_CoverRate_[ECIO_F12,)_[RxAGC_F85,)_(,TxPower_15]</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09080F</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA_CoverRate_[ECIO_F12,)_[RxAGC_F90,)_(,TxPower_15]</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E090809</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA_CoverRate_[ECIO_F12,)_[RxAGC_F95,)_(,TxPower_20]</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09080C</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA_CoverRate_Duration_[ECIO_F12,)_[RxAGC_F90,)_(,TxPower_15]</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09080A</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA_CoverRate_Duration_[ECIO_F12,)_[RxAGC_F95,)_(,TxPower_20]</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09080D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA_CoverRate_Distance_[ECIO_F12,)_[RxAGC_F90,)_(,TxPower_15]</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09080B</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">CDMA_CoverRate_Distance_[ECIO_F12,)_[RxAGC_F95,)_(,TxPower_20]</Item>
                    <Item key="FDesc" />
                    <Item typeName="String" key="FName">Ex_5E09080E</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">CDMA业务参数</Item>
            <Item key="FDesc" />
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">里程、时长</Item>
                <Item key="FDesc" />
                <Item typeName="String" key="FName" />
                <Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Duration_DL_CDMA1X</Item>
                    <Item typeName="String" key="FDesc">EVDO在CDMA1X网络下的时长</Item>
                    <Item typeName="String" key="FName">Ex_081D</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">Distance_DL_CDMA1X</Item>
                    <Item typeName="String" key="FDesc">EVDO在CDMA1X网络下的里程</Item>
                    <Item typeName="String" key="FName">Ex_0821</Item>
                    <Item typeName="Int32" key="FTag">-1</Item>
                    <Item typeName="IList" key="children" />
                  </Item>
                </Item>
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Name">CDMA2000脱网参数</Item>
            <Item key="FDesc" />
            <Item typeName="String" key="FName" />
            <Item typeName="IList" key="children">
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Duration_DMO(毫秒,脱网)</Item>
                <Item key="FDesc" />
                <Item typeName="String" key="FName">Ex_087C</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="String" key="Name">Distance_DMO(米,脱网)</Item>
                <Item key="FDesc" />
                <Item typeName="String" key="FName">Ex_087D</Item>
                <Item typeName="Int32" key="FTag">-1</Item>
                <Item typeName="IList" key="children" />
              </Item>
            </Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>