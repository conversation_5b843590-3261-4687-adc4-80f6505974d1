﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTGSMMcNcRxLevAnaByFiles : DIYReplayFileQueryByCustom
    {
        public ZTGSMMcNcRxLevAnaByFiles()
            : base(MainModel.GetInstance())
        {
            List<string> tpParams = new List<string>();
            tpParams.Add("LAC");
            tpParams.Add("CI");
            tpParams.Add("BCCH");
            tpParams.Add("BSIC");
            tpParams.Add("RxLevSub");
            tpParams.Add("N_CellName");
            tpParams.Add("N_RxLev");
            tpParams.Add("N_BCCH");
            tpParams.Add("N_BSIC");
            tpParams.Add("itime");
            tpParams.Add("ilongitude");
            tpParams.Add("ilatitude");
            tpParams.Add("ifileid");
            SetReplayContent(tpParams, false, false);
            IsAddSampleToDTDataManager = false;
        }

        public override bool CanEnabled(Model.SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12082, this.Name);
        }

        public override Model.MainModel.NeedSearchType needSearchType()
        {
            return Model.MainModel.NeedSearchType.File;
        }

        public override string Name
        {
            get { return "主邻小区场强切换分析(按文件)"; }
        }

        protected override void doPostReplayAction()
        {
            List<MainCellRxLevInfo> rstLst = new List<MainCellRxLevInfo>(rstDic.Values);
            foreach (MainCellRxLevInfo mainInfo in rstLst)
            {
                mainInfo.DoCalculate();
            }
            MainCellNCellRxLevStatForm frm = MainModel.GetObjectFromBlackboard(typeof(MainCellNCellRxLevStatForm)) as MainCellNCellRxLevStatForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new MainCellNCellRxLevStatForm(MainModel);
            }
            frm.FillData(rstLst);
            frm.Owner = this.MainModel.MainForm;
            frm.Visible = true;
            rstDic = new Dictionary<string, MainCellRxLevInfo>();
            lastFileID = -1;
            lastCellKey = "";
        }

        private Dictionary<string, MainCellRxLevInfo> rstDic = new Dictionary<string, MainCellRxLevInfo>();
        private string lastCellKey = "";
        private int lastFileID = -1;
        protected override void doWithDTData(TestPoint tp)
        {
            if (lastFileID != tp.FileID)
            {
                lastCellKey = string.Empty;
            }
            lastFileID = tp.FileID;
            float? mainRxLev = (float?)(short?)tp["RxLevSub"];
            int? lac = (int?)tp["LAC"];
            int? ci = (int?)tp["CI"];
            Cell cell = tp.GetMainCell_GSM();
            string cellKey = "";
            string cellName = "";
            if (cell == null)
            {
                if (lac == null || ci == null || lac == -255 || ci == -255)
                {
                    return;
                }
                cellName = cellKey = lac.ToString() + "_" + ci.ToString();
            }
            else
            {
                cellKey = cell.LAC.ToString() + "_" + cell.CI.ToString();
                cellName = cell.Name;
            }

            if (lastCellKey != "" && lastCellKey != cellKey)
            {
                rstDic[lastCellKey].AddHandoverCell(cellKey);
            }

            addMainCellRxLevInfo(tp, mainRxLev, cell, cellKey, cellName);
        }

        private void addMainCellRxLevInfo(TestPoint tp, float? mainRxLev, Cell cell, string cellKey, string cellName)
        {
            if (mainRxLev != null && cellKey != "")
            {
                float mRxLev = (float)mainRxLev;
                MainCellRxLevInfo mainInfo;
                if (!rstDic.TryGetValue(cellKey, out mainInfo))
                {
                    mainInfo = new MainCellRxLevInfo(cell);

                    mainInfo.CellName = cellName;
                    mainInfo.Add(tp, mRxLev);
                    rstDic.Add(cellKey, mainInfo);
                }
                else
                {
                    mainInfo.Add(tp, mRxLev);
                }

                for (int i = 0; i < 20; i++)
                {
                    float? nCellRxLev = (float?)(short?)tp["N_RxLev", i];
                    Cell nCell = tp.GetNBCell_GSM(i);
                    if (nCellRxLev != null && nCell != null)
                    {
                        float nRxLev = (float)nCellRxLev;
                        mainInfo.Add(nCell, nRxLev);
                    }
                }
                lastCellKey = cellKey;
            }
        }
    }
}
