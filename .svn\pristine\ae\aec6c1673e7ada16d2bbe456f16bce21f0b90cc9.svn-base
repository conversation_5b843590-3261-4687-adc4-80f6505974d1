﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using System.Windows.Forms;

namespace MasterCom.RAMS.CQT
{
    public class DIYQueryTDSwitchNew : DIYStatQuery
    {
        public DIYQueryTDSwitchNew(MainModel mainModel, string netWoker)
            : base(mainModel)
        {
            this.mainModel = mainModel;
            this.netType = netWoker;
        }
        private readonly string netType;
        /// <summary>
        /// 标题名称
        /// </summary>
        public override string Name
        {
            get { return ""; }
        }
        /// <summary>
        /// 图标名称
        /// </summary>
        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }
        /// <summary>
        /// 查询条件是否有效
        /// </summary>
        protected override bool isValidCondition()
        {
            return true;
        }
        protected override StatTbToken getBranchToken()
        {
            return StatTbToken.log;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 21000, 21026, this.Name);//临时
        }

        #region 全局变量
        private readonly List<FileInfo> fileList = new List<FileInfo>();
        private readonly List<string> fileValueNameList = new List<string>();
        readonly Dictionary<string, List<FileInfo>> fileValueList = new Dictionary<string, List<FileInfo>>();
        Dictionary<int, List<Event>> condEventsDic = new Dictionary<int, List<Event>>();
        readonly List<CQTHandOverItem> cqtResultList = new List<CQTHandOverItem>();
        #endregion
        /// <summary>
        /// 查询数据条件
        /// </summary>
        protected override void query()
        {
            fileList.Clear();
            fileValueNameList.Clear();
            fileValueList.Clear();
            cqtResultList.Clear();
            // 查找全区域文件
            DIYFileInfoData diyFileInfoData = new DIYFileInfoData(mainModel);
            diyFileInfoData.SetQueryCondition(this.Condition);
            diyFileInfoData.Query();
            fileList.AddRange(diyFileInfoData.FlieInfoData);
            //找出所有文件中包含的地点名称
            foreach (FileInfo fileIn in fileList)
            {
                string[] name = fileIn.Name.Split('_');
                if (name.Length < 3)
                    continue;
                if (!fileValueNameList.Contains(name[2]))
                {
                    fileValueNameList.Add(name[2]);
                }
            }
            //处理事件
            PrepareEvents();
            List<int> eventId = new List<int>();
            eventId.AddRange(condEventsDic.Keys);
            //每个地点所涉及的文件
            addFileValueList(eventId);
            //各测试地点事件
            if (netType.Equals("GSM"))
            {
                WaitBox.Show("开始分析各个测试地点的切换事件", cqtHandOverEventAnaGSM);
            }
            else if (netType.Equals("TD"))
            {
                WaitBox.Show("开始分析各个测试地点的切换事件", cqtHandOverEventAnaTD);
            }

            CQTSwitchXtraFormNew xtraformstatus = new CQTSwitchXtraFormNew(MainModel, condition, netType);
            xtraformstatus.setData(cqtResultList);
            xtraformstatus.Show();
        }

        private void addFileValueList(List<int> eventId)
        {
            foreach (string nameL in fileValueNameList)
            {
                List<FileInfo> subFileList = new List<FileInfo>();
                foreach (FileInfo fileIn in fileList)
                {
                    string[] name = fileIn.Name.Split('_');
                    if (name.Length < 3)
                        continue;
                    if (nameL.Equals(name[2]) && eventId.Contains(fileIn.ID))
                    {
                        subFileList.Add(fileIn);
                    }
                }
                fileValueList.Add(nameL, subFileList);
            }
        }

        /// <summary>
        /// 查询全区域切换事件
        /// </summary>
        private void PrepareEvents()
        {
            DIYEventByAllRegion queryEvent = new DIYEventByAllRegion(MainModel);
            queryEvent.SetIsAddEventToDTDataManager(false);
            queryEvent.SetSaveAsFileEventsDic(true);
            queryEvent.showEventChooser = false;
            queryEvent.IsQueryAllEvents = false;
            List<int> eventIds = new List<int>();
            if (netType.Equals("GSM"))
            {
                eventIds.Add(16);
                eventIds.Add(17);
                eventIds.Add(18);
            }
            else if (netType.Equals("TD"))
            {
                for (int eId = 141; eId <= 152; eId++)
                {
                    eventIds.Add(eId);
                }
            }
            condition.EventIDs = eventIds;
            queryEvent.SetQueryCondition(condition);
            queryEvent.Query();
            condEventsDic = queryEvent.fileEventsDic;
        }
        /// <summary>
        /// 按文件分析切换事件（TD）
        /// </summary>
        private void cqtHandOverEventAnaTD()
        {
            WaitBox.CanCancel = true;
            DateTime sTimeTem = DateTime.Now;
            DateTime eTimeTem = DateTime.Now;
            int idx = 1;
            CQTHandOverItem cqtResultTem = new CQTHandOverItem();
            foreach (string cpn in fileValueNameList)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                WaitBox.Text = "正在分析 " + idx++ + "/" + fileValueNameList.Count + " " + cpn + " 的切换事件...";
                WaitBox.ProgressPercent = 30;
                Dictionary<int, List<TestPoint>> fileTestPoint = replayEventFile(fileValueList[cpn]);
                foreach (FileInfo fileinfo in fileValueList[cpn])
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    dealCondEvents(ref sTimeTem, ref eTimeTem, ref cqtResultTem, cpn, fileTestPoint, fileinfo);
                }
                WaitBox.ProgressPercent = 90;
            }
            WaitBox.Close();
        }

        private void dealCondEvents(ref DateTime sTimeTem, ref DateTime eTimeTem, ref CQTHandOverItem cqtResultTem, string cpn, Dictionary<int, List<TestPoint>> fileTestPoint, FileInfo fileinfo)
        {
            foreach (Event eve in condEventsDic[fileinfo.ID])
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                if (eve.ID % 3 == 0)
                {
                    cqtResultTem = new CQTHandOverItem();
                    sTimeTem = eve.DateTime;
                }
                else if (eve.ID % 3 == 1)
                {
                    eTimeTem = eve.DateTime;
                    addCqtResultList(sTimeTem, eTimeTem, cqtResultTem, cpn, fileTestPoint, eve);
                }
            }
        }

        private void addCqtResultList(DateTime sTimeTem, DateTime eTimeTem, CQTHandOverItem cqtResultTem, string cpn, Dictionary<int, List<TestPoint>> fileTestPoint, Event eve)
        {
            if (eve.ID == 142)
                cqtResultTem.HandOverType = "TD->GSM切换成功";
            else if (eve.ID == 145 || eve.ID == 148)
                cqtResultTem.HandOverType = "TD->TD切换成功";
            else if (eve.ID == 151)
                cqtResultTem.HandOverType = "GSM->GSM切换成功";
            TimeSpan timeSpan = eTimeTem.Subtract(sTimeTem);
            cqtResultTem.Hotime = (timeSpan.Hours * 3600 + timeSpan.Minutes * 60 + timeSpan.Seconds) * 1000 + timeSpan.Milliseconds;
            cqtResultTem.Strcqtname = cpn;
            cqtResultTem.Strfilename = eve.FileName;
            cqtResultTem.Dtime = eve.DateTime;
            if (fileTestPoint.ContainsKey(eve.FileID))
            {
                string[] strSubResult = TestPointAnaTD(fileTestPoint[eve.FileID], eve.DateTime).Split('|');
                if (strSubResult[0] != "" && strSubResult[1] != "" && strSubResult[2] != "" &&
                    strSubResult[6] != "" && strSubResult[7] != "")
                {
                    cqtResultTem.Hotype = strSubResult[0];
                    cqtResultTem.Strcellname = strSubResult[1];
                    cqtResultTem.Ilac = int.Parse(strSubResult[2]);
                    cqtResultTem.Ici = int.Parse(strSubResult[3]);
                    cqtResultTem.IUARFCN = int.Parse(strSubResult[4]);
                    cqtResultTem.ICPI = int.Parse(strSubResult[5]);
                    cqtResultTem.Strtargetcellname = strSubResult[6];
                    cqtResultTem.Itargetlac = int.Parse(strSubResult[7]);
                    cqtResultTem.Itargetci = int.Parse(strSubResult[8]);
                    cqtResultTem.IUARFCNTarget = int.Parse(strSubResult[9]);
                    cqtResultTem.ICPITarget = int.Parse(strSubResult[10]);
                    cqtResultTem.Beforerelev = int.Parse(strSubResult[11]);
                    cqtResultTem.Afterrelev = int.Parse(strSubResult[12]);
                    cqtResultTem.Ifileid = eve.FileID;
                    cqtResultList.Add(cqtResultTem);
                }
            }
        }



        /// <summary>
        /// 按文件分析切换事件（GSM）
        /// </summary>
        private void cqtHandOverEventAnaGSM()
        {
            WaitBox.CanCancel = true;
            DateTime sTimeTem = DateTime.Now;
            DateTime eTimeTem = DateTime.Now;
            int idx = 1;
            CQTHandOverItem cqtResultTem = new CQTHandOverItem();
            foreach (string cpn in fileValueNameList)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                WaitBox.Text = "正在分析 " + idx++ + "/" + fileValueNameList.Count + " " + cpn + " 的切换事件...";
                WaitBox.ProgressPercent = 30;
                Dictionary<int, List<TestPoint>> fileTestPoint = replayEventFile(fileValueList[cpn]);
                foreach (FileInfo fileinfo in fileValueList[cpn])
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    dealCondEventsByEvtID(ref sTimeTem, ref eTimeTem, ref cqtResultTem, cpn, fileTestPoint, fileinfo);
                }
                WaitBox.ProgressPercent = 90;
            }
            WaitBox.Close();
        }

        private void dealCondEventsByEvtID(ref DateTime sTimeTem, ref DateTime eTimeTem, ref CQTHandOverItem cqtResultTem, string cpn, Dictionary<int, List<TestPoint>> fileTestPoint, FileInfo fileinfo)
        {
            foreach (Event eve in condEventsDic[fileinfo.ID])
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                addGsmCqtResultList(ref sTimeTem, ref eTimeTem, ref cqtResultTem, cpn, fileTestPoint, eve);
            }
        }

        private void addGsmCqtResultList(ref DateTime sTimeTem, ref DateTime eTimeTem, ref CQTHandOverItem cqtResultTem, string cpn, Dictionary<int, List<TestPoint>> fileTestPoint, Event eve)
        {
            if (eve.ID == 16)
            {
                cqtResultTem = new CQTHandOverItem();
                sTimeTem = eve.DateTime;
            }
            else if (eve.ID == 17)
            {
                eTimeTem = eve.DateTime;
                cqtResultTem.HandOverType = "GSM切换成功";
                TimeSpan timeSpan = eTimeTem.Subtract(sTimeTem);
                cqtResultTem.Hotime = (timeSpan.Hours * 3600 + timeSpan.Minutes * 60 + timeSpan.Seconds) * 1000 + timeSpan.Milliseconds;
                cqtResultTem.Strcqtname = cpn;
                cqtResultTem.Strfilename = eve.FileName;
                cqtResultTem.Dtime = eve.DateTime;
                if (fileTestPoint.ContainsKey(eve.FileID))
                {
                    string[] strSubResult = TestPointAnaGSM(fileTestPoint[eve.FileID], eve.DateTime).Split('|');
                    if (strSubResult[0] != "" && strSubResult[1] != "" && strSubResult[2] != "" &&
                        strSubResult[6] != "" && strSubResult[7] != "")
                    {
                        cqtResultTem.Hotype = strSubResult[0];
                        cqtResultTem.Strcellname = strSubResult[1];
                        cqtResultTem.Ilac = int.Parse(strSubResult[2]);
                        cqtResultTem.Ici = int.Parse(strSubResult[3]);
                        cqtResultTem.IUARFCN = int.Parse(strSubResult[4]);
                        cqtResultTem.ICPI = int.Parse(strSubResult[5]);
                        cqtResultTem.Strtargetcellname = strSubResult[6];
                        cqtResultTem.Itargetlac = int.Parse(strSubResult[7]);
                        cqtResultTem.Itargetci = int.Parse(strSubResult[8]);
                        cqtResultTem.IUARFCNTarget = int.Parse(strSubResult[9]);
                        cqtResultTem.ICPITarget = int.Parse(strSubResult[10]);
                        cqtResultTem.Beforerelev = int.Parse(strSubResult[11]);
                        cqtResultTem.Afterrelev = int.Parse(strSubResult[12]);
                        cqtResultTem.Ifileid = eve.FileID;
                        cqtResultList.Add(cqtResultTem);
                    }
                }
            }
        }

        /// <summary>
        /// 回放事件所在文件获取采样点
        /// </summary>
        private Dictionary<int, List<TestPoint>> replayEventFile(List<FileInfo> filelist)
        {
            Dictionary<int, List<TestPoint>> fileTestPointTem
                = new Dictionary<int, List<TestPoint>>();
            QueryCondition condition = new QueryCondition();
            condition.FileInfos.AddRange(filelist);
            ReplayFileCQT query = new ReplayFileCQT(mainModel);
            query.SetQueryCondition(condition);
            query.Query();
            foreach (TestPoint tp in query.testPointList)
            {
                bool isValid = true;
                if (netType.Equals("GSM"))
                {
                    isValid = judgeGSMTPValid(tp);
                }
                else if (netType.Equals("TD"))
                {
                    isValid = judgeTDTPValid(tp);
                }

                if (!isValid)
                {
                    continue;
                }

                if (!fileTestPointTem.ContainsKey(tp.FileID))
                {
                    List<TestPoint> test = new List<TestPoint>();
                    test.Add(tp);
                    fileTestPointTem.Add(tp.FileID, test);
                }
                else
                {
                    fileTestPointTem[tp.FileID].Add(tp);
                }
            }
            return fileTestPointTem;
        }

        private bool judgeGSMTPValid(TestPoint tp)
        {
            int? lac = (int?)tp["LAC"];
            int? ci = (int?)tp["CI"];
            short? bcch = (short?)tp["BCCH"];
            byte? bsic = (byte?)tp["BSIC"];
            if (lac == null || ci == null || bcch == null || bsic == null)
            {
                return false;
            }
            if ((int?)(short?)tp["RxLevSub"] == null || (int)ci <= 0 || (int)(short?)tp["RxLevSub"] > -10 || (int)(short?)tp["RxLevSub"] < -140
                || tp["RxQualSub"] == null || int.Parse(tp["RxQualSub"].ToString()) > 7 || int.Parse(tp["RxQualSub"].ToString()) < 0)
            {
                return false;
            }
            return true;
        }

        private bool judgeTDTPValid(TestPoint tp)
        {
            if ((int?)tp["TD_SCell_UARFCN"] > 11000 || (int?)tp["TD_SCell_UARFCN"] < 9000
                || (int?)tp["TD_SCell_CPI"] > 124 || (int?)tp["TD_SCell_CPI"] < 0 || tp["TD_PCCPCH_RSCP"] == null
                || (int)(float?)tp["TD_PCCPCH_RSCP"] > -10 || (int)(float?)tp["TD_PCCPCH_RSCP"] < -140)
            {
                return false;
            }
            int? lac = (int?)tp["TD_SCell_LAC"];
            int? ci = (int?)tp["TD_SCell_CI"];
            if (lac == null || ci == null)
            {
                return false;
            }
            return true;
        }

        private class TestPointAnaInfo
        {
            public int IdTem { get; set; } = 0;
            public int AvgRSCP { get; set; } = 0;
            public int SumRSCP { get; set; } = 0;
            public int AvgRSCPTarget { get; set; } = 0;
            public int SumRSCPTarget { get; set; } = 0;
            public string HotType { get; set; } = "";
            public string CellName { get; set; } = "";
            public string CellTargetName { get; set; } = "";
            public string Lac { get; set; } = "";
            public string Ci { get; set; } = "";
            public string LacTarget { get; set; } = "";
            public string CiTarget { get; set; } = "";
            public string UARFCN { get; set; } = "";
            public string UARFCNTarget { get; set; } = "";
            public string CPI { get; set; } = "";
            public string CPITaret { get; set; } = "";
        }

        /// <summary>
        /// 分析切换前后6个采样点（TD）
        /// </summary>
        private string TestPointAnaTD(List<TestPoint> testPointL, DateTime dTime)
        {
            TestPointAnaInfo tpInfo = new TestPointAnaInfo();
            testPointL.Sort(TestPointSortByTime.GetCompareByTime());
            for (int tpId = 0; tpId < testPointL.Count - 1; tpId++)
            {
                if (testPointL[tpId].DateTime <= dTime && testPointL[tpId + 1].DateTime >= dTime)
                {
                    tpInfo.IdTem = tpId;
                    TDCell tdCell = null;
                    TDCell tdCellTarget = null;
                    Cell cell = null;
                    Cell cellTarget = null;
                    getTDCell(testPointL, tpInfo, ref tdCell, ref tdCellTarget);
                    getCell(testPointL, tpInfo, tdCell, tdCellTarget, ref cell, ref cellTarget);
                    tpInfo.AvgRSCP = tpInfo.SumRSCP / 6;
                    tpInfo.AvgRSCPTarget = tpInfo.SumRSCPTarget / 6;

                    setTDCellInfo(tpInfo, tdCell);
                    setCellInfo(tpInfo, tdCell, cell);
                    setTargetTDCellInfo(tpInfo, tdCellTarget);
                    setTargetCellInfo(tpInfo, tdCellTarget, cellTarget);
                    break;
                }
            }
            return tpInfo.HotType + "|" + tpInfo.CellName + "|" + tpInfo.Lac + "|" + tpInfo.Ci  + "|" + tpInfo.UARFCN + "|" 
                + tpInfo.CPI + "|" + tpInfo.CellTargetName + "|" + tpInfo.LacTarget + "|" + tpInfo.CiTarget + "|" 
                + tpInfo.UARFCNTarget + "|" + tpInfo.CPITaret + "|" + tpInfo.AvgRSCP.ToString() + "|" + tpInfo.AvgRSCPTarget.ToString();
        }

        private void getTDCell(List<TestPoint> testPointL, TestPointAnaInfo tpInfo, ref TDCell tdCell, ref TDCell tdCellTarget)
        {
            for (int i = tpInfo.IdTem; i > tpInfo.IdTem - 6; i--)
            {
                if (i < 0 || (2 * tpInfo.IdTem + 1 - i) > (testPointL.Count - 1))
                    break;
                getTDCellInfo(testPointL, tpInfo, ref tdCell, i);
                getTDTargetCellInfo(testPointL, tpInfo.IdTem, tpInfo, ref tdCellTarget, i);
                tpInfo.SumRSCP += (int)(float?)testPointL[i]["TD_PCCPCH_RSCP"];
                tpInfo.SumRSCPTarget += (int)(float?)testPointL[2 * tpInfo.IdTem + 1 - i]["TD_PCCPCH_RSCP"];
            }
        }

        private void getCell(List<TestPoint> testPointL, TestPointAnaInfo tpInfo, TDCell tdCell, TDCell tdCellTarget, ref Cell cell, ref Cell cellTarget)
        {
            if (tdCell == null || tdCellTarget == null)
            {
                for (int i = tpInfo.IdTem; i > tpInfo.IdTem - 6; i--)
                {
                    if (i < 0 || (2 * tpInfo.IdTem + 1 - i) > (testPointL.Count - 1))
                        break;
                    getCellInfo(testPointL, tpInfo, ref cell, i);
                    getTargetCEllInfo(testPointL, tpInfo.IdTem, tpInfo, ref cellTarget, i);
                }
            }
        }

        private void getTDCellInfo(List<TestPoint> testPointL, TestPointAnaInfo tpInfo, ref TDCell tdCell, int i)
        {
            if (tdCell == null)
            {
                tdCell = CellManager.GetInstance().GetTDCell(testPointL[i].DateTime, (ushort)(int?)testPointL[i]["TD_SCell_LAC"],
                    (ushort)(int?)testPointL[i]["TD_SCell_CI"]);
                tpInfo.UARFCN = testPointL[i]["TD_SCell_UARFCN"].ToString();
                tpInfo.CPI = testPointL[i]["TD_SCell_CPI"].ToString();
            }
        }

        private void getTDTargetCellInfo(List<TestPoint> testPointL, int idTem, TestPointAnaInfo tpInfo, ref TDCell tdCellTarget, int i)
        {
            if (tdCellTarget == null)
            {
                tdCellTarget = CellManager.GetInstance().GetTDCell(testPointL[2 * idTem - i].DateTime,
                    (ushort)(int?)testPointL[2 * idTem + 1 - i]["TD_SCell_LAC"], (ushort)(int?)testPointL[2 * idTem + 1 - i]["TD_SCell_CI"]);
                tpInfo.UARFCNTarget = testPointL[2 * idTem + 1 - i]["TD_SCell_UARFCN"].ToString();
                tpInfo.CPITaret = testPointL[2 * idTem + 1 - i]["TD_SCell_CPI"].ToString();
            }
        }

        private void getCellInfo(List<TestPoint> testPointL, TestPointAnaInfo tpInfo, ref Cell cell, int i)
        {
            if (cell == null && testPointL[i]["BCCH"] != null && testPointL[i]["BSIC"] != null)
            {
                cell = CellManager.GetInstance().GetCell(testPointL[i].DateTime, (ushort)(int?)testPointL[i]["LAC"],
                    (ushort)(int?)testPointL[i]["CI"]);
                tpInfo.UARFCN = testPointL[i]["BCCH"].ToString();
                tpInfo.CPI = testPointL[i]["BSIC"].ToString();
            }
        }

        private void getTargetCEllInfo(List<TestPoint> testPointL, int idTem, TestPointAnaInfo tpInfo, ref Cell cellTarget, int i)
        {
            if (cellTarget == null
                && testPointL[2 * idTem + 1 - i]["BCCH"] != null && testPointL[2 * idTem + 1 - i]["BSIC"] != null)
            {
                cellTarget = CellManager.GetInstance().GetCell(testPointL[2 * idTem - i].DateTime,
                    (ushort)(int?)testPointL[2 * idTem + 1 - i]["LAC"], (ushort)(int?)testPointL[2 * idTem + 1 - i]["CI"]);
                tpInfo.UARFCNTarget = testPointL[2 * idTem + 1 - i]["BCCH"].ToString();
                tpInfo.CPITaret = testPointL[2 * idTem + 1 - i]["BSIC"].ToString();
            }
        }

        private void setTDCellInfo(TestPointAnaInfo tpInfo, TDCell tdCell)
        {
            if (tdCell != null)
            {
                tpInfo.CellName = tdCell.Name;
                tpInfo.Lac = tdCell.LAC.ToString();
                tpInfo.Ci = tdCell.CI.ToString();
                if (tdCell.Type == TDNodeBType.Indoor)
                    tpInfo.HotType = "室内->";
                else if (tdCell.Type == TDNodeBType.Outdoor)
                    tpInfo.HotType = "室外->";
            }
        }

        private void setCellInfo(TestPointAnaInfo tpInfo, TDCell tdCell, Cell cell)
        {
            if (tdCell == null && cell != null)
            {
                tpInfo.CellName = cell.Name;
                tpInfo.Lac = cell.LAC.ToString();
                tpInfo.Ci = cell.CI.ToString();
                if (cell.Type == BTSType.Indoor)
                    tpInfo.HotType = "室内->";
                else if (cell.Type == BTSType.Outdoor)
                    tpInfo.HotType = "室外->";
            }
        }

        private void setTargetTDCellInfo(TestPointAnaInfo tpInfo, TDCell tdCellTarget)
        {
            if (tdCellTarget != null)
            {
                tpInfo.CellTargetName = tdCellTarget.Name;
                tpInfo.LacTarget = tdCellTarget.LAC.ToString();
                tpInfo.CiTarget = tdCellTarget.CI.ToString();
                if (tdCellTarget.Type == TDNodeBType.Indoor)
                    tpInfo.HotType = tpInfo.HotType + "室内";
                else if (tdCellTarget.Type == TDNodeBType.Outdoor)
                    tpInfo.HotType = tpInfo.HotType + "室外";
            }
        }

        private void setTargetCellInfo(TestPointAnaInfo tpInfo, TDCell tdCellTarget, Cell cellTarget)
        {
            if (tdCellTarget == null && cellTarget != null)
            {
                tpInfo.CellTargetName = cellTarget.Name;
                tpInfo.LacTarget = cellTarget.LAC.ToString();
                tpInfo.CiTarget = cellTarget.CI.ToString();
                if (cellTarget.Type == BTSType.Indoor)
                    tpInfo.HotType = tpInfo.HotType + "室内";
                else if (cellTarget.Type == BTSType.Outdoor)
                    tpInfo.HotType = tpInfo.HotType + "室外";
            }
        }

        /// <summary>
        /// 分析切换前后6个采样点（GSM）
        /// </summary>
        private string TestPointAnaGSM(List<TestPoint> testPointL, DateTime dTime)
        {
            TestPointAnaInfo tpInfo = new TestPointAnaInfo();
            testPointL.Sort(TestPointSortByTime.GetCompareByTime());
            for (int tpId = 0; tpId < testPointL.Count - 1; tpId++)
            {
                if (testPointL[tpId].DateTime <= dTime && testPointL[tpId + 1].DateTime >= dTime)
                {
                    tpInfo.IdTem = tpId;
                    Cell cell = null;
                    Cell cellTarget = null;
                    getCell(testPointL, tpInfo, ref cell, ref cellTarget);
                    tpInfo.AvgRSCP = tpInfo.SumRSCP / 6;
                    tpInfo.AvgRSCPTarget = tpInfo.SumRSCPTarget / 6;
                    getCellInfo(tpInfo, cell);
                    getCellInfo(tpInfo, cellTarget);
                    break;
                }
            }
            return tpInfo.HotType + "|" + tpInfo.CellName + "|" + tpInfo.Lac + "|" + tpInfo.Ci + "|" + tpInfo.UARFCN + "|"
                + tpInfo.CPI + "|" + tpInfo.CellTargetName + "|" + tpInfo.LacTarget + "|" + tpInfo.CiTarget + "|"
                + tpInfo.UARFCNTarget + "|" + tpInfo.CPITaret + "|" + tpInfo.AvgRSCP.ToString() + "|" + tpInfo.AvgRSCPTarget.ToString();
        }

        private void getCell(List<TestPoint> testPointL, TestPointAnaInfo tpInfo, ref Cell cell, ref Cell cellTarget)
        {
            for (int i = tpInfo.IdTem; i > tpInfo.IdTem - 6; i--)
            {
                if (i < 0 || (2 * tpInfo.IdTem + 1 - i) > (testPointL.Count - 1))
                    break;
                if (cell == null)
                {
                    cell = CellManager.GetInstance().GetCell(testPointL[i].DateTime, (ushort)(int?)testPointL[i]["LAC"],
                        (ushort)(int?)testPointL[i]["CI"]);
                }
                if (cellTarget == null)
                {
                    cellTarget = CellManager.GetInstance().GetCell(testPointL[2 * tpInfo.IdTem - i].DateTime,
                        (ushort)(int?)testPointL[2 * tpInfo.IdTem + 1 - i]["LAC"], (ushort)(int?)testPointL[2 * tpInfo.IdTem + 1 - i]["CI"]);
                }
                tpInfo.SumRSCP += (int)(short?)testPointL[i]["RxLevSub"];
                tpInfo.SumRSCPTarget += (int)(short?)testPointL[2 * tpInfo.IdTem + 1 - i]["RxLevSub"];
            }
        }

        private void getCellInfo(TestPointAnaInfo tpInfo, Cell cell)
        {
            if (cell != null)
            {
                tpInfo.CellName = cell.Name;
                tpInfo.Lac = cell.LAC.ToString();
                tpInfo.Ci = cell.CI.ToString();
                tpInfo.UARFCN = cell.BCCH.ToString();
                tpInfo.CPI = cell.BSIC.ToString();
                if (cell.Type == BTSType.Indoor)
                    tpInfo.HotType = "室内->";
                else if (cell.Type == BTSType.Outdoor)
                    tpInfo.HotType = "室外->";
            }
        }
    }
    /// <summary>
    /// 实现对采样点时间的排序类
    /// </summary>
    public class TestPointSortByTime
    {
        //实现排序的接口
        public static IComparer<TestPoint> GetCompareByTime()
        {
            if (comparerByTime == null)
            {
                comparerByTime = new CompareBySampleTime();
            }
            return comparerByTime;
        }
        public class CompareBySampleTime : IComparer<TestPoint>
        {
            public int Compare(TestPoint x, TestPoint y)
            {
                return x.DateTime.CompareTo(y.DateTime);
            }
        }
        private static IComparer<TestPoint> comparerByTime;
    }
    public class CQTHandOverItem
    {
        public int Afterrelev { get; set; }
        public int Beforerelev { get; set; }
        public DateTime Dtime { get; set; }
        public string HandOverType { get; set; }
        public int Hotime { get; set; }
        public string Hotype { get; set; }
        public int Iareaid { get; set; }
        public int Iareatype { get; set; }
        public int Ici { get; set; }
        public int Ieventid { get; set; }
        public int Ifileid { get; set; }
        public int Ilac { get; set; }
        public int IBCCH { get; set; }
        public int IBSIC { get; set; }
        public int IRxLevSub { get; set; }
        public int Itargetci { get; set; }
        public int Itargetlac { get; set; }
        public int ItargetPCCH { get; set; }
        public int ItargetDPCH { get; set; }
        public int ItargetRSCP { get; set; }
        public int Itime { get; set; }
        public string Strcellname { get; set; }
        public string Strcqtname { get; set; }
        public string Strfilename { get; set; }
        public string Strtargetcellname { get; set; }
        public int Wtimems { get; set; }
        public int IUARFCN { get; set; }
        public int ICPI { get; set; }
        public int IUARFCNTarget { get; set; }
        public int ICPITarget { get; set; }
    }
}