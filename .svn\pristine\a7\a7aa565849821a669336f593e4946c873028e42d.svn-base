﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.UserMng;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.ZTFunc;
using System.Drawing;

namespace MasterCom.RAMS.Net
{
    public class ZTGridDownloadSpeedAna : DIYGridQuery
    {
        public ZTGridDownloadSpeedAna(MainModel mainModel)
            : base(mainModel)
        {
            this.mainModel = mainModel;
        }
        public override string Name
        {
            get { return "平均下载速率(按栅格)"; }
        }
        public override string IconName
        {
            get { return null; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22073, "查询");
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        protected override void AddGeographicFilter(Package package)
        {
            this.AddDIYRegion_Intersect(package);
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override bool isValidStatImg(double lng, double lat)
        {
            MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(lng, lat);
            return isContainDbRect(grid.Bounds);
        }
        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            List<string> formulaSet = new List<string>();
            formulaSet.Add("(Mx_050204020102+Mx_050208020102)*1024");
            formulaSet.Add("(Mx_050204020101+Mx_050208020101)*(1000*8)");
            formulaSet.Add("(Cx_050706020102)*1024");
            formulaSet.Add("Cx_050706020101*(1000*8)");
            formulaSet.Add("(Tx_050564020102+Tx_051264020102)*1024");
            formulaSet.Add("(Tx_050564020101+Tx_051264020101)*(1000*8)");
            formulaSet.Add("(Wx_050B01640202+Wx_050F01640202)*1024");
            formulaSet.Add("(Wx_050B01640201+Wx_050F01640201)*(1000*8)");
            formulaSet.Add("Ex_050964020102*1024");
            formulaSet.Add("Ex_050964020101*(1000*8)");
            formulaSet.Add("Lte_052164020102/1000");
            formulaSet.Add("Lte_052164020101*8/(1024*1024)");
            formulaSet.Add("Lf_052D64020102/1000");
            formulaSet.Add("Lf_052D64020101*8/(1024*1024)");
            return getTriadIDIgnoreServiceType(formulaSet);
        }

        #region 全局变量
        List<int> iCarrList = null;
        string strCarrName = "";
        Dictionary<string, GridDownloadSpeedAvgInfo> cityDOwnloadSeepInfoDic = null;
        public List<GridDownloadSpeedAvgInfo> dOwnloadSeeInfoList { get; set; }
        Dictionary<string, Dictionary<string, MapOperation2>> mutRegionMopDic = null;
        string strCityName = "";
        string strStartTime = "";
        #endregion

        protected override void query()
        {
            if (!getConditionBeforeQuery())
            {
                return;
            }
            iCarrList = new List<int>();
            iCarrList.AddRange(condition.CarrierTypes);
            strStartTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            dOwnloadSeeInfoList = new List<GridDownloadSpeedAvgInfo>();
            cityDOwnloadSeepInfoDic = new Dictionary<string, GridDownloadSpeedAvgInfo>();
            mutRegionMopDic = new Dictionary<string, Dictionary<string, MapOperation2>>();
            InitRegionMop2();
            foreach (int districtID in condition.DistrictIDs)
            {
                strCityName = DistrictManager.GetInstance().getDistrictName(districtID);
                queryDistrictData(districtID);
                dOwnloadSeeInfoList.Add(cityDOwnloadSeepInfoDic[strCityName]);
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
            }
            afterRecieveAllData();
            fireShowResult();
        }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;

                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                MainModel.CurGridCoverData = null;
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                WaitBox.Text = strCityName + " 开始统计查询栅格数据...";
                WaitBox.CanCancel = true;
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();

                string statImgIDSet = getStatImgNeededTriadID();
                foreach (int iCarr in iCarrList)
                {
                    if (iCarr == 1)
                    {
                        strCarrName = " 移动 ";
                    }
                    else if (iCarr == 2)
                    {
                        strCarrName = " 联通 ";
                    }
                    else
                    {
                        strCarrName = " 电信 ";
                    }
                    condition.CarrierTypes.Clear();
                    condition.CarrierTypes.Add(iCarr);
                    foreach (TimePeriod period in condition.Periods)
                    {
                        queryPeriodInfo(period, clientProxy, statImgIDSet, MainModel.CurGridColorUnitMatrix);
                    }

                    changGridDataFormula();

                    MainModel.DTDataManager.Clear();
                    MainModel.SelectedTestPoints.Clear();
                    MainModel.SelectedEvents.Clear();
                    MainModel.SelectedMessage = null;
                    MainModel.CurGridCoverData = null;
                    MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                }
                WaitBox.Text = strCityName + " 数据获取完毕，进行显示预处理...";
                if (MainModel.CurGridCoverData != null)
                {
                    MainModel.CurGridCoverData.Clear();
                }
            }
            catch (Exception e)
            {
                MessageBox.Show("Error" + e.StackTrace + e.ToString());
            }
            finally
            {
                WaitBox.Close();
            }
            MapGridLayer.NeedFreshFullImg = true;
        }

        protected override void recieveInfo_ImgGrid(ClientProxy clientProxy, params object[] reservedParams)
        {
            Package package = clientProxy.Package;
            int counter = 0;
            bool recved = false;
            int curPercent = 11;
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (!recved)
                {
                    WaitBox.Text = strCityName + " 正在从服务器接收" + strCarrName + "数据...";
                }
                recved = true;
                KPIStatDataBase singleStatData = null;
                if (isFileHeaderContentType(package.Content.Type))
                {
                    //
                }
                else if (isImgColDefContent(package, curImgColumnDef))
                {
                    //
                }
                else if (isKPIDataContent(package, out singleStatData))
                {
                    fillData(package, curImgColumnDef, singleStatData);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    MessageBox.Show("Unexpected type: " + package.Content.Type);
                    break;
                }
                int tmp = (int)(Math.Log(counter++) * 10);
                if (tmp < 95 && tmp > 0 && curPercent != tmp)
                {
                    WaitBox.ProgressPercent = tmp;
                }
                else if (tmp > 95)
                {
                    curPercent = 5;
                    counter = 0;
                }
            }
        }

        private void fillData(Package package, List<StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            fillStatData(package, curImgColumnDef, singleStatData);
            ColorUnit cu = new ColorUnit();
            cu.LTLng = lng;
            cu.LTLat = lat;
            int rAt, cAt;
            GridHelper.GetIndexOfDefaultSizeGrid(cu.CenterLng, cu.CenterLat, out rAt, out cAt);

            cu = MainModel.CurGridColorUnitMatrix[rAt, cAt];
            if (cu == null)
            {
                if (isValidStatImg(lng, lat))
                {
                    cu = new ColorUnit();
                    cu.LTLng = lng;
                    cu.LTLat = lat;
                    MainModel.CurGridColorUnitMatrix[rAt, cAt] = cu;
                    cu.Status = 1;
                    cu.DataHub.AddStatData(singleStatData, false);
                }
            }
            else
            {
                cu.Status = 1;
                cu.DataHub.AddStatData(singleStatData, false);
            }
        }

        /// <summary>
        /// 获取预存或圈选的区域
        /// </summary>
        private void InitRegionMop2()
        {
            Dictionary<string, List<ResvRegion>> resvRegionsDic = MainModel.SearchGeometrys.SelectedResvRegionDic;
            MapWinGIS.Shape gmt = MainModel.SearchGeometrys.Region;
            if (resvRegionsDic != null && resvRegionsDic.Count > 0)
            {
                foreach (string strGridType in resvRegionsDic.Keys)
                {
                    addRegionMap(resvRegionsDic, strGridType);
                }
            }
            else if (gmt != null)//单个区域
            {
                Dictionary<string, MapOperation2> regionMopDic =
                    new Dictionary<string, MapOperation2>();
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(gmt);
                regionMopDic.Add("当前区域", mapOp2);
                mutRegionMopDic.Add("无网格类型", regionMopDic);
            }
        }

        private void addRegionMap(Dictionary<string, List<ResvRegion>> resvRegionsDic, string strGridType)
        {
            if (!mutRegionMopDic.ContainsKey(strGridType))
            {
                Dictionary<string, MapOperation2> regionMop = new Dictionary<string, MapOperation2>();
                foreach (ResvRegion region in resvRegionsDic[strGridType])
                {
                    if (!regionMop.ContainsKey(region.RegionName))
                    {
                        MapOperation2 mapOp2 = new MapOperation2();
                        mapOp2.FillPolygon(region.Shape);
                        regionMop.Add(region.RegionName, mapOp2);
                    }
                }
                mutRegionMopDic.Add(strGridType, regionMop);
            }
        }

        /// <summary>
        /// 定位所在网格
        /// </summary>
        private bool isContainDbRect(DbRect dRect)
        {
            foreach (string gridType in mutRegionMopDic.Keys)
            {
                foreach (string grid in mutRegionMopDic[gridType].Keys)
                {
                    if (!gridType.Contains("当前区域") && !(gridType + grid).Contains(strCityName))
                    {
                        continue;
                    }
                    if (mutRegionMopDic[gridType][grid].CheckRectIntersectWithRegion(dRect))
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        private void changGridDataFormula()
        {
            if (!cityDOwnloadSeepInfoDic.ContainsKey(strCityName))
            {
                GridDownloadSpeedAvgInfo downloadSpeedInfo = new GridDownloadSpeedAvgInfo();
                downloadSpeedInfo.StrCityName = strCityName;
                cityDOwnloadSeepInfoDic.Add(strCityName, downloadSpeedInfo);
            }

            WaitBox.Text = strCityName + " 正在统计" + strCarrName + "栅格数据......";
            WaitBox.ProgressPercent = 35;

            foreach (ColorUnit cu in MainModel.CurGridColorUnitMatrix)
            {
                StatDataGSM dataStatGSM = cu.DataHub.GetStatData(typeof(StatDataGSM)) as StatDataGSM;
                StatDataTD dataStatTD = cu.DataHub.GetStatData(typeof(StatDataTD)) as StatDataTD;
                StatDataWCDMA dataStatWCDMA = cu.DataHub.GetStatData(typeof(StatDataWCDMA)) as StatDataWCDMA;
                StatDataCDMA_Voice dataStatCDMA = cu.DataHub.GetStatData(typeof(StatDataCDMA_Voice)) as StatDataCDMA_Voice;
                StatDataCDMA_EVDO dataStatEVDO = cu.DataHub.GetStatData(typeof(StatDataCDMA_EVDO)) as StatDataCDMA_EVDO;
                StatDataLTE dataStatTDD = cu.DataHub.GetStatData(typeof(StatDataLTE)) as StatDataLTE;
                StatDataLTE_FDD dataStatFDD = cu.DataHub.GetStatData(typeof(StatDataLTE_FDD)) as StatDataLTE_FDD;
                if (dataStatGSM == null && dataStatTD == null && dataStatWCDMA == null&& dataStatCDMA == null
                    && dataStatEVDO == null && dataStatTDD == null && dataStatFDD == null)
                {
                    continue;
                }

                doStatGSM(dataStatGSM,cu);
                doStatCDMA(dataStatCDMA, cu);
                doStatTD(dataStatTD, cu);
                doStatWCDMA(dataStatWCDMA, cu);
                doStatCDMA2000(dataStatEVDO, cu);
                doStatTDD(dataStatTDD, cu);
                doStatFDD(dataStatFDD, cu);

                cu.DataHub = new StatDataHubBase();
            }
        }

        private void doStatGSM(StatDataGSM dataStatGSM, ColorUnit cu)
        {
            if (dataStatGSM != null)
            {
                double dDownTime = cu.DataHub.CalcValueByFormula("(Mx_050204020102+Mx_050208020102)*1024");
                double dDownSize = cu.DataHub.CalcValueByFormula("(Mx_050204020101+Mx_050208020101)*(1000*8)");
       
                if (condition.CarrierTypes.Contains(1))
                {
                    setYDCityDownloadSeepInfo(dDownTime, dDownSize);
                }
                else if (condition.CarrierTypes.Contains(2))
                {
                    setLTCityDownloadSeepInfo(dDownTime, dDownSize);
                }
            }
        }

        private void setYDCityDownloadSeepInfo(double dDownTime, double dDownSize)
        {
            double dDownSpeed = 0;
            if (dDownTime > 0)
            {
                cityDOwnloadSeepInfoDic[strCityName].ISumYDGSMGridCount++;
                if (dDownSize >= 0)
                {
                    dDownSpeed = dDownSize / dDownTime;
                }
            }
            cityDOwnloadSeepInfoDic[strCityName].DSumYDGSMGridSpeed += dDownSpeed;
        }

        private void setLTCityDownloadSeepInfo(double dDownTime, double dDownSize)
        {
            double dDownSpeed = 0;
            if (dDownTime > 0)
            {
                cityDOwnloadSeepInfoDic[strCityName].ISumLTGSMGridCount++;
                if (dDownSize >= 0)
                {
                    dDownSpeed = dDownSize / dDownTime;
                }
            }
            cityDOwnloadSeepInfoDic[strCityName].DSumLTGSMGridSpeed += dDownSpeed;
        }

        private void doStatCDMA(StatDataCDMA_Voice dataStatCDMA, ColorUnit cu)
        {
            if (dataStatCDMA != null)
            {
                double dDownTime = cu.DataHub.CalcValueByFormula("(Cx_050706020102)*1024");
                double dDownSize = cu.DataHub.CalcValueByFormula("Cx_050706020101*(1000*8)");
                double dDownSpeed = 0;
                if (dDownTime > 0)
                {
                    cityDOwnloadSeepInfoDic[strCityName].ISumDXCDMAGridCount++;
                    if (dDownSize >= 0)
                    {
                        dDownSpeed = dDownSize / dDownTime;
                    }
                }
                cityDOwnloadSeepInfoDic[strCityName].DSumDXCDMAGridSpeed += dDownSpeed;
            }
        }

        private void doStatTD(StatDataTD dataStatTD, ColorUnit cu)
        {
            if (dataStatTD != null)
            {
                double dDownTime = cu.DataHub.CalcValueByFormula("(Tx_050564020102+Tx_051264020102)*1024");
                double dDownSize = cu.DataHub.CalcValueByFormula("(Tx_050564020101+Tx_051264020101)*(1000*8)");
                double dDownSpeed = 0;
                if (dDownTime > 0)
                {
                    cityDOwnloadSeepInfoDic[strCityName].ISumYDTDGridCount++;
                    if (dDownSize >= 0)
                    {
                        dDownSpeed = dDownSize / dDownTime;
                    }
                }
                cityDOwnloadSeepInfoDic[strCityName].DSumYDTDGridSpeed += dDownSpeed;
            }
        }

        private void doStatWCDMA(StatDataWCDMA dataStatWCDMA, ColorUnit cu)
        {
            if (dataStatWCDMA != null)
            {
                double dDownTime = cu.DataHub.CalcValueByFormula("(Wx_050B01640202+Wx_050F01640202)*1024");
                double dDownSize = cu.DataHub.CalcValueByFormula("(Wx_050B01640201+Wx_050F01640201)*(1000*8)");
                double dDownSpeed = 0;
                if (dDownTime > 0)
                {
                    cityDOwnloadSeepInfoDic[strCityName].ISumLTWCDMAGridCount++;
                    if (dDownSize >= 0)
                    {
                        dDownSpeed = dDownSize / dDownTime;
                    }
                }
                cityDOwnloadSeepInfoDic[strCityName].DSumLTWCDMAGridSpeed += dDownSpeed;
            }
        }

        private void doStatCDMA2000(StatDataCDMA_EVDO dataStatEVDO, ColorUnit cu)
        {
            if (dataStatEVDO != null)
            {
                double dDownTime = cu.DataHub.CalcValueByFormula("Ex_050964020102*1024");
                double dDownSize = cu.DataHub.CalcValueByFormula("Ex_050964020101*(1000*8)");
                double dDownSpeed = 0;
                if (dDownTime > 0)
                {
                    cityDOwnloadSeepInfoDic[strCityName].ISumDXCDMA2000GridCount++;
                    if (dDownSize >= 0)
                    {
                        dDownSpeed = dDownSize / dDownTime;
                    }
                }
                cityDOwnloadSeepInfoDic[strCityName].DSumDXCDMA2000GridSpeed += dDownSpeed;
            }
        }

        private void doStatTDD(StatDataLTE dataStatTDD, ColorUnit cu)
        {
            if (dataStatTDD != null)
            {
                double dDownTime = cu.DataHub.CalcValueByFormula("Lte_052164020102/1000");
                double dDownSize = cu.DataHub.CalcValueByFormula("Lte_052164020101*8/(1024*1024)");
                double dDownSpeed = 0;
                if (dDownTime > 0)
                {
                    cityDOwnloadSeepInfoDic[strCityName].ISumYDTDDGridCount++;
                    if (dDownSize >= 0)
                    {
                        dDownSpeed = dDownSize / dDownTime;
                    }
                }
                cityDOwnloadSeepInfoDic[strCityName].DSumYDTDDGridSpeed += dDownSpeed;
            }
        }

        private void doStatFDD(StatDataLTE_FDD dataStatFDD, ColorUnit cu)
        {
            if (dataStatFDD != null)
            {
                double dDownTime = cu.DataHub.CalcValueByFormula("Lf_052D64020102/1000");
                double dDownSize = cu.DataHub.CalcValueByFormula("Lf_052D64020101*8/(1024*1024)");
                if (condition.CarrierTypes.Contains(2))
                {
                    setLTCityDOwnloadSeepInfo(dDownTime, dDownSize);
                }
                else if (condition.CarrierTypes.Contains(3))
                {
                    setDXCityDOwnloadSeepInfo(dDownTime, dDownSize);
                }
            }
        }

        private void setLTCityDOwnloadSeepInfo(double dDownTime, double dDownSize)
        {
            double dDownSpeed = 0;
            if (dDownTime > 0)
            {
                cityDOwnloadSeepInfoDic[strCityName].ISumLTFDDGridCount++;
                if (dDownSize >= 0)
                {
                    dDownSpeed = dDownSize / dDownTime;
                }
            }
            cityDOwnloadSeepInfoDic[strCityName].DSumLTFDDGridSpeed += dDownSpeed;
        }

        private void setDXCityDOwnloadSeepInfo(double dDownTime, double dDownSize)
        {
            double dDownSpeed = 0;
            if (dDownTime > 0)
            {
                cityDOwnloadSeepInfoDic[strCityName].ISumDXFDDGridCount++;
                if (dDownSize >= 0)
                {
                    dDownSpeed = dDownSize / dDownTime;
                }
            }
            cityDOwnloadSeepInfoDic[strCityName].DSumDXFDDGridSpeed += dDownSpeed;
        }

        protected override void fireShowResult()
        {
            ZTGridDownloadSpeedForm showForm = null;
            object obj = MainModel.GetObjectFromBlackboard(typeof(ZTGridDownloadSpeedForm).FullName);
            showForm = obj == null ? null : obj as ZTGridDownloadSpeedForm;
            if (showForm == null || showForm.IsDisposed)
            {
                showForm = new ZTGridDownloadSpeedForm(MainModel, strStartTime);
            }
            showForm.FillData(dOwnloadSeeInfoList);
            showForm.Show(MainModel.MainForm);
        }
    }

    public class GridDownloadSpeedAvgInfo
    {
        public string StrCityName { get; set; }
        public double DSumYDGSMGridSpeed { get; set; }
        public int ISumYDGSMGridCount { get; set; }
        public double DYDGSMGridSpeedAvg
        {
            get
            {
                double dSpeedAvg = 0;
                if (ISumYDGSMGridCount > 0)
                {
                    dSpeedAvg = DSumYDGSMGridSpeed / ISumYDGSMGridCount;
                }
                return Math.Round(dSpeedAvg,4);
            }
        }
        public double DSumYDTDGridSpeed { get; set; }
        public int ISumYDTDGridCount { get; set; }
        public double DYDTDGridSpeedAvg
        {
            get
            {
                double dSpeedAvg = 0;
                if (ISumYDTDGridCount > 0)
                {
                    dSpeedAvg = DSumYDTDGridSpeed / ISumYDTDGridCount;
                }
                return Math.Round(dSpeedAvg, 4);
            }
        }
        public double DSumYDTDDGridSpeed { get; set; }
        public int ISumYDTDDGridCount { get; set; }
        public double DYDTDDGridSpeedAvg
        {
            get
            {
                double dSpeedAvg = 0;
                if (ISumYDTDDGridCount > 0)
                {
                    dSpeedAvg = DSumYDTDDGridSpeed / ISumYDTDDGridCount;
                }
                return Math.Round(dSpeedAvg, 4);
            }
        }
        public double DSumLTGSMGridSpeed { get; set; }
        public int ISumLTGSMGridCount { get; set; }
        public double DLTGSMGridSpeedAvg
        {
            get
            {
                double dSpeedAvg = 0;
                if (ISumLTGSMGridCount > 0)
                {
                    dSpeedAvg = DSumLTGSMGridSpeed / ISumLTGSMGridCount;
                }
                return Math.Round(dSpeedAvg, 4);
            }
        }
        public double DSumLTWCDMAGridSpeed { get; set; }
        public int ISumLTWCDMAGridCount { get; set; }
        public double DLTWCDMAGridSpeedAvg
        {
            get
            {
                double dSpeedAvg = 0;
                if (ISumLTWCDMAGridCount > 0)
                {
                    dSpeedAvg = DSumLTWCDMAGridSpeed / ISumLTWCDMAGridCount;
                }
                return Math.Round(dSpeedAvg, 4);
            }
        }
        public double DSumLTFDDGridSpeed { get; set; }
        public int ISumLTFDDGridCount { get; set; }
        public double DLTFDDGridSpeedAvg
        {
            get
            {
                double dSpeedAvg = 0;
                if (ISumLTFDDGridCount > 0)
                {
                    dSpeedAvg = DSumLTFDDGridSpeed / ISumLTFDDGridCount;
                }
                return Math.Round(dSpeedAvg, 4);
            }
        }
        public double DSumDXCDMAGridSpeed { get; set; }
        public int ISumDXCDMAGridCount { get; set; }
        public double DDXCDMAGridSpeedAvg
        {
            get
            {
                double dSpeedAvg = 0;
                if (ISumDXCDMAGridCount > 0)
                {
                    dSpeedAvg = DSumDXCDMAGridSpeed / ISumDXCDMAGridCount;
                }
                return Math.Round(dSpeedAvg, 4);
            }
        }
        public double DSumDXCDMA2000GridSpeed { get; set; }
        public int ISumDXCDMA2000GridCount { get; set; }
        public double DDXCDMA2000GridSpeedAvg
        {
            get
            {
                double dSpeedAvg = 0;
                if (ISumDXCDMA2000GridCount > 0)
                {
                    dSpeedAvg = DSumDXCDMA2000GridSpeed / ISumDXCDMA2000GridCount;
                }
                return Math.Round(dSpeedAvg, 4);
            }
        }
        public double DSumDXFDDGridSpeed { get; set; }
        public int ISumDXFDDGridCount { get; set; }
        public double DDXFDDGridSpeedAvg
        {
            get
            {
                double dSpeedAvg = 0;
                if (ISumDXFDDGridCount > 0)
                {
                    dSpeedAvg = DSumDXFDDGridSpeed / ISumDXFDDGridCount;
                }
                return Math.Round(dSpeedAvg, 4);
            }
        }
    }
}
