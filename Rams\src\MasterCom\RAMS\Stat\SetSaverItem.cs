﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Stat;
using System.Xml;
using MasterCom.Util;

namespace MasterCom.RAMS.Stat
{
    /// <summary>
    /// 保存用户常用的查询条件设置
    /// </summary>
    public class SetSaverItem
    {
        /// <summary>
        /// 区域名
        /// </summary>
        public List<string> AreaList { get; set; } = new List<string>();

        /// <summary>
        /// 数据来源/项目类型
        /// </summary>
        public List<int> ProjList { get; set; } = new List<int>();

        /// <summary>
        /// 业务类型
        /// </summary>
        public List<int> ServiceList { get; set; } = new List<int>();

        /// <summary>
        /// 上传单元/代维公司 
        /// </summary>
        public List<int> AgentIds { get; set; } = new List<int>();

        /// <summary>
        /// 地市
        /// </summary>
        public List<int> CityList { get; set; } = new List<int>();

        /// <summary>
        /// 运营商
        /// </summary>
        public List<int> CarrierTypes { get; set; } = new List<int>();

        public int FormAreaType { get; set; } = -1;  //报表的区域类型

        public string FileName { get; set; }     //查询文件名

        public string ReporterTemplateName { get; set; }         //报告模板名

        public string AuthorName { get; set; }         //创建人

        public string SavePath { get; set; }         //保存路径

        public string dateTimeElapse { get; set; }           //第一个推移

        public int numElapseTo { get; set; }           //第二个推移

        public override string ToString()
        {
            return AuthorName;
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["AuthorName"] = AuthorName;
                param["ReporterTemplateName"] = ReporterTemplateName;
                param["FileName"] = FileName;
                param["FormAreaType"] = FormAreaType;
                if (SavePath != null)
                {
                    param["SavePath"] = SavePath;
                    param["dateTimeElapse"] = dateTimeElapse;
                    param["numElapseTo"] = numElapseTo;
                }

                List<object> agentParams = new List<object>();
                param["AgentIds"] = agentParams;
                foreach (int agentId in AgentIds)
                {
                    agentParams.Add(agentId);
                }

                List<object> areaParams = new List<object>();
                param["AreaList"] = areaParams;
                foreach (string area in AreaList)
                {
                    areaParams.Add(area);
                }

                List<object> projParams=new List<object>();
                param["ProjList"] = projParams;
                foreach (int projId in ProjList)
                {
                    projParams.Add(projId);
                }

                List<object> serviceParams = new List<object>();
                param["ServiceList"] = serviceParams;
                foreach (int serviceId in ServiceList)
                {
                    serviceParams.Add(serviceId);
                }

                List<object> carrierTypeParams = new List<object>();
                param["CarrierTypes"] = carrierTypeParams;
                foreach (int carrierType in CarrierTypes)
                {
                    carrierTypeParams.Add(carrierType);
                }

                List<object> cityParams = new List<object>();
                param["CityList"] = cityParams;
                foreach (int cityId in CityList)
                {
                    cityParams.Add(cityId);
                }

                return param;
            }
            set
            {
                if (value==null || value.Count==0)
                {
                    return;
                }
                this.AuthorName = (string)value["AuthorName"];
                this.FileName = (string)value["FileName"];
                if (value.ContainsKey("SavePath"))
                {
                    this.SavePath = (string)value["SavePath"];
                    this.dateTimeElapse = (string)value["dateTimeElapse"];
                    this.numElapseTo = (int)value["numElapseTo"];
                }
                this.ReporterTemplateName = (string)value["ReporterTemplateName"];
                this.FormAreaType = value.ContainsKey("FormAreaType") ? (int)value["FormAreaType"] : -1;  //后来加的字段，以防读取之前保存的条件产生空值报错，先判断是否有值

                List<object> objList = value["AgentIds"] as List<object>;
                this.AgentIds.Clear();
                foreach (object obj in objList)
                {
                    this.AgentIds.Add((int)obj);
                }

                objList = value["ProjList"] as List<object>;
                this.ProjList.Clear();
                foreach (object obj in objList)
                {
                    this.ProjList.Add((int)obj);
                }

                objList = value["ServiceList"] as List<object>;
                this.ServiceList.Clear();
                foreach (object obj in objList)
                {
                    this.ServiceList.Add((int)obj);
                }

                objList = value["AreaList"] as List<object>;
                this.AreaList.Clear();
                foreach (object obj in objList)
                {
                    this.AreaList.Add((string)obj);
                }

                objList = value["CarrierTypes"] as List<object>;
                this.CarrierTypes.Clear();
                foreach (object obj in objList)
                {
                    this.CarrierTypes.Add((int)obj);
                }

                objList = value["CityList"] as List<object>;
                this.CityList.Clear();
                foreach (object obj in objList)
                {
                    this.CityList.Add((int)obj);
                }
            }
        }

        public static XmlElement AddItem(XmlConfigFile configFile, XmlElement config, string name, object value)
        {
            if (value is SetSaverItem)
            {
                XmlElement item = configFile.AddItem(config, name, value.GetType());
                configFile.AddItem(item, "Saver", (value as SetSaverItem).Param);
                return item;
            }
            return null;
        }
        public static object GetItemValue(XmlConfigFile configFile, XmlElement item, string typeName)
        {
            if (typeName.Equals(typeof(SetSaverItem).Name))
            {
                Dictionary<string, object> param = configFile.GetItemValue(item, "Saver") as Dictionary<string, object>;
                SetSaverItem setSaver = new SetSaverItem();
                setSaver.Param = param;
                return setSaver;
            }
            return null;
        }
    }
}
