﻿using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class StationAccept_TianJin : StationAcceptBaseWithWorkParams<int, int>
    {
        /// <summary>
        /// 功能ID
        /// </summary>
        protected int subFuncId { get; set; }
        protected StationAcceptManager_TianJin manager { get; set; }
        /// <summary>
        /// 后台配置的条件参数
        /// </summary>
        public StationAcceptCondition_TJ StationCondition { get; set; } = new StationAcceptCondition_TJ();

        #region instance
        protected static readonly object lockObj = new object();
        private static StationAccept_TianJin instance = null;
        public static StationAccept_TianJin GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new StationAccept_TianJin();
                    }
                }
            }
            return instance;
        }
        #endregion

        protected StationAccept_TianJin()
            : base(MainModel.GetInstance())
        {
            
        }

        public override string Name
        {
            get { return "天津单站验收"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 22000, 22999, "天津单站验收");
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new StationAcceptProperties_TianJin(this);
            }
        }

        protected override void init()
        {
            this.isIgnoreExport = true;
            FilterSampleByRegion = false;
            FilterEventByRegion = false;
            IncludeMessage = true;

            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.LTEFDD));
            ServiceTypes.AddRange(ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.VoLTEFDD));

            Columns = new List<string>
            {
                "lte_fdd_TAC",
                "lte_fdd_ECI",
                "lte_fdd_EARFCN",
                "lte_fdd_PCI",
                "lte_fdd_RSRP",
                "lte_fdd_SINR",
                "lte_fdd_MAC_UL",
                "lte_fdd_MAC_DL",
                "lte_fdd_NCell_EARFCN",
                "lte_fdd_NCell_PCI",
                "lte_fdd_NCell_RSRP",
                "lte_fdd_NCell_SINR"
            };

            workParamSumDic = new Dictionary<string, Dictionary<int, BtsAcceptWorkParamBase<int>>>();
        }

        protected override void loadWorkParams()
        {
            StationAcceptWorkParams_TianJin workParams = GetWorkParamsHelper_TianJin.GetInstance().GetWorkParams(StationCondition) as StationAcceptWorkParams_TianJin;
            if (workParams != null)
            {
                workParams.setWorkParam(workParamSumDic);
            }
        }

        protected override bool judgeWorkParams()
        {
            if (workParamSumDic == null || workParamSumDic.Count <= 0
                || !workParamSumDic.ContainsKey(curDistrictName))
            {
                reportBackgroundInfo("未读取到" + curDistrictName + "的待单验的基站信息");
                return false;
            }
            MainModel.MainForm.GetMapForm().updateMap();
            return true;
        }

        //当前基站文件分析结果
        BtsAcceptFileInfo_TianJin curBtsRes;
        //当前基站小区文件分析结果
        List<CellAcceptFileInfo_TianJin> curBtsCellsResList;
        protected override void initCurBtsAcceptInfo()
        {
            curBtsRes = null;
            curBtsCellsResList = new List<CellAcceptFileInfo_TianJin>();
            cellsResTmp = new Dictionary<string, List<CellAcceptKpiAna_TianJin>>();
            manager = new StationAcceptManager_TianJin();
        }

        //#region 工参处理
        ///// <summary>
        ///// 是否是从库里读取的待单验工参
        ///// </summary>
        //private bool isNewAdd;
        ///// <summary>
        ///// 待单验站点对应缓存的工参小区
        ///// </summary>
        //private List<LTECell> oldCells;
        //protected override void loadCurBtsWorkParam(BtsAcceptWorkParamBase btsInfo)
        //{
        //    BtsWorkParam_TianJin tjBtsInfo = btsInfo as BtsWorkParam_TianJin;

        //    //加载单验工参
        //    CellWorkParam_TianJin cellParam = tjBtsInfo.CellWorkParams[0] as CellWorkParam_TianJin;
        //    int eci = cellParam.ENodeBID * 256 + cellParam.CellID;
        //    oldCells = MainModel.CellManager.GetLTECellsByECI(eci);
        //    removeCell(oldCells);
        //    LTEBTS bts = null;
        //    isNewAdd = addCellInfoToCellManager(tjBtsInfo, ref bts);
        //    tjBtsInfo.Bts = bts;
        //}

        //protected override void removeCurBtsWorkParam(BtsAcceptWorkParamBase btsInfo)
        //{
        //    BtsWorkParam_TianJin tjBtsInfo = btsInfo as BtsWorkParam_TianJin;

        //    //移除单验工参
        //    removeNedAddedCell(tjBtsInfo.Bts, isNewAdd);
        //    reCoverCells(oldCells);

        //    MainModel.ClearDTData();
        //    mainModel.FireDTDataChanged(this);
        //}

        //private void removeNedAddedCell(LTEBTS bts, bool isNewAdd)
        //{
        //    if (isNewAdd)//将动态添加的工参移除
        //    {
        //        foreach (LTECell cell in bts.Cells)
        //        {
        //            MainModel.CellManager.Remove(cell);
        //            foreach (LTEAntenna ant in cell.Antennas)
        //            {
        //                MainModel.CellManager.Remove(ant);
        //            }
        //        }
        //        MainModel.CellManager.Remove(bts);
        //    }
        //}

        ///// <summary>
        ///// 添加上传的工参
        ///// </summary>
        ///// <param name="btsParam"></param>
        ///// <param name="bts"></param>
        ///// <returns></returns>
        //private bool addCellInfoToCellManager(BtsWorkParam_TianJin btsParam, ref LTEBTS bts)
        //{
        //    foreach (CellAcceptWorkParamBase info in btsParam.CellWorkParams)
        //    {
        //        CellWorkParam_TianJin cellInfo = (CellWorkParam_TianJin)info;
        //        LTECell nbiotCell = CellManager.GetInstance().GetLTECellByECI(DateTime.Now, cellInfo.Eci);
        //        if (nbiotCell != null)
        //        {
        //            bts = nbiotCell.BelongBTS;
        //            return false;
        //        }
        //    }

        //    bts = new LTEBTS();
        //    int snapShotId = -1;

        //    #region 暂时动态添加工参到CellManager，稍后移除
        //    bts.Fill(snapShotId, 0, 2147483647);
        //    bts.Name = btsParam.BtsName;
        //    bts.BTSID = btsParam.ENodeBID;
        //    bts.Type = btsParam.IsOutDoor ? LTEBTSType.Outdoor : LTEBTSType.Indoor;

        //    foreach (CellAcceptWorkParamBase info in btsParam.CellWorkParams)
        //    {
        //        CellWorkParam_TianJin cellParamInfo = (CellWorkParam_TianJin)info;
        //        bts.Longitude = cellParamInfo.Longitude;
        //        bts.Latitude = cellParamInfo.Latitude;

        //        snapShotId--;
        //        LTECell cell = new LTECell();
        //        cell.Fill(snapShotId, 0, 2147483647);
        //        cell.BelongBTS = bts;
        //        cell.Name = cellParamInfo.CellName;
        //        cell.TAC = cellParamInfo.Tac;
        //        cell.ECI = cellParamInfo.ENodeBID * 256 + cellParamInfo.CellID;
        //        cell.CellID = cellParamInfo.CellID;
        //        cell.PCI = cellParamInfo.Pci;
        //        cell.EARFCN = cellParamInfo.Earfcn;
        //        bts.AddCell(cell);
        //        MainModel.CellManager.Add(cell);

        //        LTEAntenna antenna = new LTEAntenna();
        //        snapShotId--;
        //        antenna.Fill(snapShotId, 0, 2147483647);
        //        antenna.Cell = cell;
        //        antenna.Longitude = cellParamInfo.Longitude;
        //        antenna.Latitude = cellParamInfo.Latitude;
        //        antenna.Direction = (short)cellParamInfo.Direction;
        //        antenna.Downward = (short)cellParamInfo.Downward;
        //        antenna.Altitude = cellParamInfo.Altitude;
        //    }
        //    MainModel.CellManager.Add(bts);
        //    #endregion

        //    return true;
        //}

        ///// <summary>
        ///// 移除该小区的旧工参
        ///// </summary>
        ///// <param name="cells"></param>
        //private void removeCell(List<LTECell> cells)
        //{
        //    if (cells == null)
        //    {
        //        return;
        //    }
        //    cells = new List<LTECell>(cells);
        //    foreach (LTECell oldCell in cells)
        //    {
        //        MainModel.CellManager.Remove(oldCell.BelongBTS);
        //        foreach (LTECell lteCell in oldCell.BelongBTS.Cells)
        //        {
        //            MainModel.CellManager.Remove(lteCell);
        //            foreach (LTEAntenna antenna in lteCell.Antennas)
        //            {
        //                MainModel.CellManager.Remove(antenna);
        //            }
        //        }
        //    }
        //}

        ///// <summary>
        ///// 恢复旧工参
        ///// </summary>
        ///// <param name="cells"></param>
        //private void reCoverCells(List<LTECell> cells)
        //{
        //    if (cells != null)
        //    {
        //        List<LTEBTS> lteBtsList = MainModel.CellManager.GetCurrentLTEBTSs();
        //        cells = new List<LTECell>(cells);
        //        foreach (LTECell oldCell in cells)
        //        {
        //            foreach (LTECell lteCell in oldCell.BelongBTS.Cells)
        //            {
        //                MainModel.CellManager.Add(lteCell);
        //                foreach (LTEAntenna antenna in lteCell.Antennas)
        //                {
        //                    MainModel.CellManager.Add(antenna);
        //                }
        //            }

        //            if (!lteBtsList.Contains(oldCell.BelongBTS))
        //            {
        //                MainModel.CellManager.Add(oldCell.BelongBTS);
        //            }
        //        }
        //    }
        //}
        //#endregion

        /// <summary>
        /// 获取待分析的文件信息
        /// </summary>
        protected override void getFilesForAnalyse()
        {
            carrierID = CarrierType.ChinaUnicom;
            BackgroundFuncQueryManager.GetInstance().GetFilterFile_CellAccept(subFuncId, ServiceTypeString
                            , ((int)carrierID).ToString(), "strfilename", FileNameKeyStr);
        }

        /// <summary>
        /// 文件回放后,将其数据按文件名进行不同的单站验收功能
        /// </summary>
        protected override void doStatWithQuery()
        {
            if (curAnaFileInfo == null || MainModel.DTDataManager.FileDataManagers.Count == 0)
            {
                return;
            }
            manager = new StationAcceptManager_TianJin();
            manager.AnalyzeFile(curAnaFileInfo, MainModel.DTDataManager.FileDataManagers[0], (BtsWorkParam_TianJin)curBtsInfo);
            MainModel.DTDataManager.Clear();
        }

        //多文件临时结果属性,
        Dictionary<string, List<CellAcceptKpiAna_TianJin>> cellsResTmp;
        protected override void saveBackgroundData()
        {
            foreach (var acceptFileInfo in manager.AcceptCellFileInfoList.Values)
            {
                curBtsCellsResList.Add(acceptFileInfo);
            }

            if (manager.AcceptBtsFileInfo != null)
            {
                addBtsAcceptRes();
            }

            //添加临时结果
            foreach (var cellRes in manager.CellsResListTmp)
            {
                List<CellAcceptKpiAna_TianJin> acpList;
                if (!cellsResTmp.TryGetValue(cellRes.Key, out acpList))
                {
                    acpList = new List<CellAcceptKpiAna_TianJin>();
                    cellsResTmp.Add(cellRes.Key, acpList);
                }
                acpList.AddRange(cellRes.Value);
            }
        }

        private void addBtsAcceptRes()
        {
            if (curBtsRes == null)
            {
                curBtsRes = manager.AcceptBtsFileInfo;
            }
            else if (curBtsRes.BtsId == manager.AcceptBtsFileInfo.BtsId)
            {
                foreach (var kpi in manager.AcceptBtsFileInfo.AcceptKpiDic)
                {
                    if (!curBtsRes.AcceptKpiDic.ContainsKey(kpi.Key))
                    {
                        curBtsRes.AcceptKpiDic.Add(kpi.Key, kpi.Value);
                    }
                }
            }
        }

        protected override void doSomethingAfterAnalyseFiles()
        {
            if (MainModel.BackgroundStopRequest)
            {
                return;
            }

            Dictionary<uint, object> kpiInfos = manager.DoAfterAnalyseAllFiles(cellsResTmp);
            foreach (var kpi in kpiInfos)
            {
                if (!curBtsRes.AcceptKpiDic.ContainsKey(kpi.Key))
                {
                    curBtsRes.AcceptKpiDic.Add(kpi.Key, kpi.Value);
                }
            }

            //有可能截图后转到了地图界面,返回后台信息界面
            BackToBackgroundWorkSheet();

            //分析完所有文件后导出
            reportBackgroundInfo(string.Format("开始处理基站 {0} 的结果信息...", curBtsInfo.BtsNameFull));
            if (curBtsInfo.IsOutDoor)
            {
                exportOutdoorBtsReport();
            }
        }

        /// <summary>
        /// 导出室外站
        /// </summary>
        /// <param name="bgResultList"></param>
        /// <param name="btsWorkParamInfo"></param>
        protected virtual void exportOutdoorBtsReport()
        {
            BtsWorkParam_TianJin bts = curBtsInfo as BtsWorkParam_TianJin;
            OutDoorBtsAcceptInfo_TianJin curBtsAcceptInfo = StaionAcceptResultHelper_TJ.GetOutDoorBtsResultByBgData(bts, curBtsCellsResList, curBtsRes);

            if (curBtsAcceptInfo != null)
            {
                string savePath;
                bool hasExportReport = ExportBtsReportHelper_TianJin.ExportReports(curBtsAcceptInfo, bts, out savePath);

                if (hasExportReport)
                {
                    updateBtsAcceptDes(curBtsAcceptInfo, hasExportReport, bts, savePath);
                }
            }
            else
            {
                reportBackgroundInfo("没有分析出对应基站信息");
            }
        }

        /// <summary>
        /// 修改上传的工参描述
        /// </summary>
        /// <param name="hasPassedAccept"></param>
        /// <param name="hasExportReport"></param>
        /// <param name="btsParam"></param>
        protected void updateBtsAcceptDes(OutDoorBtsAcceptInfo_TianJin acceptInfo, bool hasExportReport, BtsWorkParam_TianJin curBtsInfo, string savePath)
        {
            if (hasExportReport)
            {
                //更新工参表
                UpdateWorkParamDes_TianJin upParam = new UpdateWorkParamDes_TianJin(acceptInfo);
                upParam.Query();

                //新增结果
                InsertAcceptResult_TianJin insertRes = new InsertAcceptResult_TianJin(acceptInfo, curBtsInfo, savePath);
                insertRes.Query();
            }
        }

        public void BackToBackgroundWorkSheet()
        {
            MainModel model = MainModel.GetInstance();
            foreach (WorkSheet ws in model.WorkSpace.WorkSheets)
            {
                foreach (ChildFormConfig cfc in ws.ChildFormConfigs)
                {
                    if (cfc.Text == "后台专题运行信息")
                    {
                        model.MainForm.activeChildForm(ws, cfc);
                    }
                }
            }
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                ignoreParamKeys.Clear();
                ignoreParamKeys.Add("ExportReportSet");

                Dictionary<string, object> param = new Dictionary<string, object>
                {
                    ["BackgroundStat"] = BackgroundStat,
                    ["ExportReportSet"] = StationCondition.Params
                };
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("ExportReportSet"))
                {
                    StationCondition.Params = param["ExportReportSet"] as Dictionary<string, object>;
                }
            }
        }

        public override void DealAfterBackgroundQueryByCity()
        {
            //上传单验报告,暂未添加
        }

        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Cell_Region; }
        }

        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.单站验收; }
        }
    }
}
