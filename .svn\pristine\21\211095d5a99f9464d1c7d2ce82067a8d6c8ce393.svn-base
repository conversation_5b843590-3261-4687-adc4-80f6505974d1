﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTReportEventMng;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTInportTestEventLog : QueryBase
    {
        public ZTInportTestEventLog(MainModel mm)
            : base(mm)
        {
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 18000, 18022, this.Name);
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }
        public override string Name
        {
            get {return "导入VVIP测试记录"; }
        }

        public override string IconName
        {
            get { return null; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
            InportForm frm=new InportForm();
            frm.ShowDialog(MainModel.MainForm);
        }
    }
}
