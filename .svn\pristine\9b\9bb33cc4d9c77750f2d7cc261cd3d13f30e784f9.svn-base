﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NREdgeSpeedAnaDlg : BaseDialog
    {
        public NREdgeSpeedAnaDlg()
        {
            InitializeComponent();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        public void SetCondition(NREdgeSpeedCondtion condition)
        {
            if (condition == null)
            {
                return;
            }
            numericProbability.Value = (decimal)condition.EdgeSpeedThreshold;
        }

        public NREdgeSpeedCondtion GetCondition()
        {
            NREdgeSpeedCondtion condition = new NREdgeSpeedCondtion();
            condition.EdgeSpeedThreshold = (float)numericProbability.Value;
            return condition;
        }
    }
}
