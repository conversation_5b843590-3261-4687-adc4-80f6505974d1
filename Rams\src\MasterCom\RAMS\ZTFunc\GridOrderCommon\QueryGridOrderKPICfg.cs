﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc.GridOrderCommon
{
    public class QueryGridOrderKPICfg : DIYSQLBase
    {
        public QueryGridOrderKPICfg()
        {
            MainDB = true;
        }

        protected override string getSqlTextString()
        {
            return string.Format(@"SELECT [setTypeID],[kpiID],[kpiName],[keykpi] FROM {0}.[dbo].[tb_cfg_grid_set_kpi];", QueryGridOrder.DBCatName);
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] arr = new E_VType[4];
            arr[0] = E_VType.E_Int;
            arr[1] = E_VType.E_Int;
            arr[2] = E_VType.E_String;
            arr[3] = E_VType.E_Int;
            return arr;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            KPICfgSet = new List<GridOrderKPICfg>();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    GridOrderKPICfg cfg = new GridOrderKPICfg();
                    cfg.SetTypeID = package.Content.GetParamInt();
                    cfg.ID = package.Content.GetParamInt();
                    cfg.Name = package.Content.GetParamString();
                    cfg.IsKeyKPI = package.Content.GetParamInt() == 1;
                    KPICfgSet.Add(cfg);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        public List<GridOrderKPICfg> KPICfgSet { get; set; }
    }
}
