﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class NRBtsInfo : BtsInfoBase
    {
        public NRBtsServiceInfo NSABtsInfo { get; set; }
        public NRBtsServiceInfo SABtsInfo { get; set; }

        public Dictionary<string, NRBtsOutdoorScenePicInfo> OutdoorScenePicInfoDic { get; set; } = new Dictionary<string, NRBtsOutdoorScenePicInfo>();

        public List<NRBtsOutdoorScenePicInfo> OutdoorScenePicInfoList { get; set; } = new List<NRBtsOutdoorScenePicInfo>();

        public List<NRAlarmInfo> BtsAlarmList { get; set; }

        /// <summary>
        /// 由于存在拉远站,使用拉远小区名中的部分作为基站名
        /// </summary>
        public string CurFileBtsName { get; set; }

        public NRBtsInfo(NRBTS bts)
            : base(bts)
        {
            BtsName = bts.Name;
            BtsID = bts.BTSID;
        }

        public void Init(NRServiceName serviceType)
        {
            if (SABtsInfo == null && serviceType == NRServiceName.SA)
            {
                SABtsInfo = new NRBtsServiceInfo();
            }
            else if (NSABtsInfo == null && serviceType == NRServiceName.NSA)
            {
                NSABtsInfo = new NRBtsServiceInfo();
            }
        }

        public override void Calculate()
        {
            base.Calculate();
            NSABtsInfo?.Calculate(BtsName);
            SABtsInfo?.Calculate(BtsName);

            foreach (var info in OutdoorScenePicInfoDic)
            {
                if (info.Key == BtsName)
                {
                    //相同名字作为主站,放第一个
                    OutdoorScenePicInfoList.Insert(0, info.Value);
                }
                else
                {
                    //不同名字为拉远站
                    OutdoorScenePicInfoList.Add(info.Value);
                }
            }
        }
    }

    public class NRBtsServiceInfo
    {
        //由于存在拉远站,会存在多个切换文件,每个文件分开出结果
        public Dictionary<string, BtsHandoverInfo> FileBtsHandOverInfoDic { get; set; } = new Dictionary<string, BtsHandoverInfo>();

        public List<BtsHandoverInfo> FileBtsHandOverInfoList { get; set; } = new List<BtsHandoverInfo>();

        public void Calculate(string btsName)
        {
            foreach (var handOverInfo in FileBtsHandOverInfoDic.Values)
            {
                if (handOverInfo.FileBtsName == btsName)
                {
                    //相同名字作为主站,放第一个
                    FileBtsHandOverInfoList.Insert(0, handOverInfo);
                }
                else
                {
                    //不同名字为拉远站
                    FileBtsHandOverInfoList.Add(handOverInfo);
                }
            }
        }
    }

    public class BtsHandoverInfo
    {
        public BtsHandoverInfo(string fileBtsName)
        {
            FileBtsName = fileBtsName;
        }

        public string FileBtsName { get; set; }
        public PicKpiInfo PCIPicInfo { get; set; } = new PicKpiInfo();
        public SuccessRateKpiInfo HandoverRate { get; set; } = new SuccessRateKpiInfo();

        public void Calculate()
        {
            HandoverRate.Calculate();
        }
    }

    public class NRBtsOutdoorScenePicInfo
    {
        public NRBtsOutdoorScenePicInfo(string fileBtsName)
        {
            FileBtsName = fileBtsName;
        }
        public string FileBtsName { get; set; }

        public PicKpiInfo PanoramicPicInfo { get; set; } = new PicKpiInfo();
        public PicKpiInfo EntrancePicInfo { get; set; } = new PicKpiInfo();
        public PicKpiInfo RoofPicInfo { get; set; } = new PicKpiInfo();

        public PicKpiInfo Cell1PicInfo { get; set; } = new PicKpiInfo();
        public PicKpiInfo Cell2PicInfo { get; set; } = new PicKpiInfo();
        public PicKpiInfo Cell3PicInfo { get; set; } = new PicKpiInfo();
        public PicKpiInfo Cell4PicInfo { get; set; } = new PicKpiInfo();
        public PicKpiInfo Cell5PicInfo { get; set; } = new PicKpiInfo();
        public PicKpiInfo Cell6PicInfo { get; set; } = new PicKpiInfo();
        public PicKpiInfo Cell7PicInfo { get; set; } = new PicKpiInfo();
        public PicKpiInfo Cell8PicInfo { get; set; } = new PicKpiInfo();
        public PicKpiInfo Cell9PicInfo { get; set; } = new PicKpiInfo();

        public PicKpiInfo Dir0PicInfo { get; set; } = new PicKpiInfo();
        public PicKpiInfo Dir45PicInfo { get; set; } = new PicKpiInfo();
        public PicKpiInfo Dir90PicInfo { get; set; } = new PicKpiInfo();
        public PicKpiInfo Dir135PicInfo { get; set; } = new PicKpiInfo();
        public PicKpiInfo Dir180PicInfo { get; set; } = new PicKpiInfo();
        public PicKpiInfo Dir225PicInfo { get; set; } = new PicKpiInfo();
        public PicKpiInfo Dir270PicInfo { get; set; } = new PicKpiInfo();
        public PicKpiInfo Dir315PicInfo { get; set; } = new PicKpiInfo();

        public PicKpiInfo Other1PicInfo { get; set; } = new PicKpiInfo();
        public PicKpiInfo Other2PicInfo { get; set; } = new PicKpiInfo();
    }

    public class NRBtsParameters : BtsParameters
    {
        /// <summary>
        /// 站名
        /// </summary>
        public string BtsName { get; set; }
        /// <summary>
        /// 站号
        /// </summary>
        public int ENodeBID { get; set; }
        /// <summary>
        /// 地址
        /// </summary>
        public string Address { get; set; }
        /// <summary>
        /// 区县
        /// </summary>
        public string Country { get; set; }

        public ParamInfo<double?> BtsLongutide { get; set; } = new ParamInfo<double?>();
        public ParamInfo<double?> BtsLatitude { get; set; } = new ParamInfo<double?>();
        public ParamInfo<int?> CellCount { get; set; } = new ParamInfo<int?>();
        public ParamInfo<int?> TAC { get; set; } = new ParamInfo<int?>();

        public Dictionary<string, NRCellParameters> CellDic { get; set; } = new Dictionary<string, NRCellParameters>();

        public void Caluculate()
        {
            BtsLongutide.JudgeValidLongitude(50);
            BtsLatitude.JudgeValidLatitude(50);
            CellCount.JudgeValid();
            TAC.JudgeValid();

            foreach (var cell in CellDic.Values)
            {
                cell.Caluculate();
            }
        }
    }
}
