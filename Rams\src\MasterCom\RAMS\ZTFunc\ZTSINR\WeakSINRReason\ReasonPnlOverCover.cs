﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    public partial class ReasonPnlOverCover : ReasonPanelBase
    {
        public ReasonPnlOverCover()
        {
            InitializeComponent();
        }

        public override void AttachReason(ReasonBase reason)
        {
            base.AttachReason(reason);
            numMainRsrp.ValueChanged -= numMainRsrp_ValueChanged;
            numMainRsrp.Value = (decimal)((ReasonsOverCover)reason).OverRsrpMin;
            numMainRsrp.ValueChanged += numMainRsrp_ValueChanged;
            numOverCoverFactor.ValueChanged -= numOverCoverFactor_ValueChanged;
            numOverCoverFactor.Value = (decimal)((ReasonsOverCover)reason).CoverFactor;
            numOverCoverFactor.ValueChanged += numOverCoverFactor_ValueChanged;
        }

        void numMainRsrp_ValueChanged(object sender, EventArgs e)
        {
            ((ReasonsOverCover)reason).OverRsrpMin = (double)numMainRsrp.Value;
        }
        void numOverCoverFactor_ValueChanged(object sender, EventArgs e)
        {
            ((ReasonsOverCover)reason).CoverFactor = (double)numOverCoverFactor.Value;
        }
    }
}
