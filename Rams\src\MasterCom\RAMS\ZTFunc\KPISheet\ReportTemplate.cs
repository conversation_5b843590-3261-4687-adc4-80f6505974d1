﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.KPISheet
{
    public class ReportTemplate
    {
        public ReportTemplate()
        { }
        public ReportTemplate(string name)
        {
            this.Name = name;
            Columns = new List<ReportColunm>();
        }
        public string Name { get; set; }
        public override string ToString()
        {
            if (Name == null)
            {
                return string.Empty;
            }
            return Name;
        }

        public Dictionary<TableTemplate, List<ReportColunm>> TableColDic
        {
            get
            {
                Dictionary<TableTemplate, List<ReportColunm>> dic = new Dictionary<TableTemplate, List<ReportColunm>>();
                foreach (ReportColunm col in Columns)
                {
                    List<ReportColunm> colSet = null;
                    if (!dic.TryGetValue(col.TableCol.Table, out colSet))
                    {
                        colSet = new List<ReportColunm>();
                        dic[col.TableCol.Table] = colSet;
                    }
                    colSet.Add(col);
                }
                return dic;
            }
        }

        public List<ReportColunm> Columns
        {
            get;
            set;
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic["Name"] = this.Name;
                List<object> cols=new List<object>();
                dic["Columns"] = cols;
                foreach (ReportColunm col in Columns)
                {
                    cols.Add(col.Param);
                }
                return dic;
            }
            set
            {
                this.Name = value["Name"] as string;
                this.Columns = new List<ReportColunm>();
                List<object> obj = value["Columns"] as List<object>;
                foreach (Dictionary<string,object> dic in obj)
                {
                    ReportColunm col = new ReportColunm();
                    col.Param = dic;
                    this.Columns.Add(col);
                }
            }
        }

    }
}
