﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.ZTReportEventMng
{
    public partial class VVipConditionDlg : BaseForm
    {
        public VVipConditionDlg(DateTime beginTime, DateTime endTime, int netType)
        {
            InitializeComponent();
            dtBegin.Value = beginTime.Date;
            dtEnd.Value = endTime.Date;
            if (-1 < netType && netType < 3)
            {
                cmbNetType.SelectedIndex = netType;
            }
        }

        public void GetSetting(out DateTime beginTime, out DateTime endTime, out int netType)
        {
            beginTime = dtBegin.Value.Date;
            endTime = dtEnd.Value.Date;
            netType = cmbNetType.SelectedIndex;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (dtEnd.Value.Date<dtBegin.Value.Date)
            {
                MessageBox.Show("开始时间不能大于结束时间！");
                return;
            }
            if (cmbNetType.SelectedIndex==-1)
            {
                MessageBox.Show("请选择查询的网络类型！");
                return;
            }
            DialogResult = DialogResult.OK;
        }

    }
}
