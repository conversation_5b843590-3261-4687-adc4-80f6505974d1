﻿using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRPilotFreqPolluteByRegion : DIYAnalyseByPeriodBackgroundBase_Sample
    {
        public int cellCountThreshold { get; set; } = 4;
        public int RsrpThreshold { get; set; } = 6;
        public int filterRsrpMin { get; set; } = -85;
        public int filterRsrpMax { get; set; } = -10;
        public static int PilotFrequencyPolluteBlockRadius { get; set; } = 50;
        public int sampleCountLimit { get; set; } = 10;
        protected Dictionary<string, int> curPilotFrequencyPolluteGridSampleCountDic = null;

        public string themeName { get; set; }

        private static NRPilotFreqPolluteByRegion intance = null;
        protected static readonly object lockObj = new object();
        public static NRPilotFreqPolluteByRegion GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new NRPilotFreqPolluteByRegion();
                    }
                }
            }
            return intance;
        }

        protected NRPilotFreqPolluteByRegion()
            : base(MainModel.GetInstance())
        {
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
            this.isAddSampleToDTDataManager = false;
            themeName = "NR:SS_RSRP";
        }

        public override string Name
        {
            //宁夏要求修改
            get { return "mod30干扰"; }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35030, this.Name);
        }

        protected override bool isValidTestPoint(TestPoint tp)
        {
            try
            {
                bool inRegion = Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
                if (!inRegion)
                {
                    return false;
                }
                return validTestPoint(tp);
            }
            catch
            {
                return false;
            }
        }

        protected virtual bool validTestPoint(TestPoint tp)
        {
            if (!(tp is TestPoint_NR))
            {
                return false;
            }
            Dictionary<NRCell, float> cellRsrpDic = new Dictionary<NRCell, float>();
            Dictionary<NRCell, float?> cellSinrDic = new Dictionary<NRCell, float?>();
            setCellInfo(tp, cellRsrpDic, cellSinrDic);

            setNCellInfo(tp, cellRsrpDic, cellSinrDic);

            float maxRsrp = float.MinValue;
            NRCell maxCell = null;
            List<NRCell> cells = new List<NRCell>();
            bool isvalid = getMaxCell(cellRsrpDic, ref maxRsrp, ref maxCell, cells);
            if (!isvalid)
            {
                return false;
            }

            if (cells.Count < cellCountThreshold)
            {
                saveGoodSample(tp);
                return false;
            }
            else
            {
                List<NRCellOfPilotFrequencyPolluteBlock> cellList = new List<NRCellOfPilotFrequencyPolluteBlock>();
                foreach (NRCell c in cells)
                {
                    float value = cellRsrpDic[c];
                    cellList.Add(new NRCellOfPilotFrequencyPolluteBlock(cellList.Count + 1, c, value, cellSinrDic[c]));
                }
                gatherBlock(cellList, tp, maxCell.SSBARFCN);
            }
            return true;
        }

        protected bool getMaxCell(Dictionary<NRCell, float> cellRsrpDic, ref float maxRsrp, ref NRCell maxCell, List<NRCell> cells)
        {
            //排序后统计与最强信号6dB内的小区数量
            if (cellRsrpDic.Count >= cellCountThreshold)
            {
                foreach (NRCell c in cellRsrpDic.Keys)
                {
                    if (cellRsrpDic[c] > maxRsrp)
                    {
                        maxRsrp = cellRsrpDic[c];
                        maxCell = c;
                    }
                }
                if (maxCell == null)
                {
                    return false;
                }
                foreach (NRCell c in cellRsrpDic.Keys)
                {
                    if ((!coFreqOnly || c.SSBARFCN == maxCell.SSBARFCN) && (maxRsrp - cellRsrpDic[c]) <= RsrpThreshold)
                    {
                        cells.Add(c);
                    }
                }
            }
            return true;
        }

        private void setNCellInfo(TestPoint tp, Dictionary<NRCell, float> cellRsrpDic, Dictionary<NRCell, float?> cellSinrDic)
        {
            //邻区场强
            for (int i = 0; i < 16; i++)
            {
                NRTpHelper.NRNCellType type = NRTpHelper.NrTpManager.GetNCellType(tp, i);
                if (type == NRTpHelper.NRNCellType.NCELL)
                {
                    NRCell cell = tp.GetNBCell_NR(i);
                    if (cell != null)
                    {
                        setValidCellInfo(tp, cellRsrpDic, cellSinrDic, i, cell);
                    }
                }
            }
        }

        private void setValidCellInfo(TestPoint tp, Dictionary<NRCell, float> cellRsrpDic, Dictionary<NRCell, float?> cellSinrDic, int i, NRCell cell)
        {
            float? nRsrp = NRTpHelper.NrTpManager.GetNCellRsrp(tp, i);
            if (nRsrp != null && nRsrp >= filterRsrpMin && nRsrp <= filterRsrpMax)
            {
                cellRsrpDic[cell] = (float)nRsrp;
                cellSinrDic[cell] = NRTpHelper.NrTpManager.GetNCellSinr(tp, i);
            }
        }

        private void setCellInfo(TestPoint tp, Dictionary<NRCell, float> cellRsrpDic, Dictionary<NRCell, float?> cellSinrDic)
        {
            //主服场强
            float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
            NRCell cell = tp.GetMainCell_NR();//
            if (cell != null && rsrp != null && rsrp >= filterRsrpMin && rsrp <= filterRsrpMax)
            {
                cellRsrpDic[cell] = (float)rsrp;
                cellSinrDic[cell] = NRTpHelper.NrTpManager.GetSCellSinr(tp);
            }
        }

        protected void saveGoodSample(TestPoint testPoint)
        {
            string key = testPoint.Longitude + "|" + testPoint.Latitude;
            if (curPilotFrequencyPolluteGridSampleCountDic.ContainsKey(key))
            {
                curPilotFrequencyPolluteGridSampleCountDic[key]++;
            }
            else
            {
                curPilotFrequencyPolluteGridSampleCountDic[key] = 1;
            }
        }

        protected bool coFreqOnly = false;
        protected override bool getConditionBeforeQuery()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            NRPilotFrequencyPolluteDlg fDlg = new NRPilotFrequencyPolluteDlg();
            if (fDlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            filterRsrpMin = fDlg.RxlevMin;
            filterRsrpMax = fDlg.RxlevMax;
            PilotFrequencyPolluteBlockRadius = fDlg.Radius;
            sampleCountLimit = fDlg.SampleCountLimit;
            cellCountThreshold = fDlg.CellCountThreshold;
            RsrpThreshold = fDlg.RxLevDValueThreshold;
            coFreqOnly = fDlg.CoFreqOnly;
            return true;
        }

        protected override void doSomethingBeforeQueryInThread()
        {
            curPilotFrequencyPolluteGridSampleCountDic = new Dictionary<string, int>();
            pilotFrequencyPolluteBlockList = new List<NRPilotFrequencyPolluteBlock>();
            MainModel.CurPilotFrequencyPolluteBlockList_NR.Clear();
        }

        protected override void FireShowFormAfterQuery()
        {
            NRPilotFrequencyPolluteForm frm = MainModel.GetObjectFromBlackboard(typeof(NRPilotFrequencyPolluteForm)) as NRPilotFrequencyPolluteForm;
            if (frm == null || frm.IsDisposed)
            {
                frm = new NRPilotFrequencyPolluteForm();
            }
            frm.FillData();
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            curPilotFrequencyPolluteGridSampleCountDic = null;
            pilotFrequencyPolluteBlockList = null;
            MainModel.FireSetDefaultMapSerialTheme(themeName);
        }

        protected override void getResultAfterQuery()
        {
            getPilotFreqPolluteResult(pilotFrequencyPolluteBlockList);
            getGoodSample();
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = NRTpHelper.InitBaseReplayParamSample(false, true);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        List<NRPilotFrequencyPolluteBlock> pilotFrequencyPolluteBlockList = new List<NRPilotFrequencyPolluteBlock>();

        /// <summary>
        /// 采样点加到导频污染黑点
        /// </summary>
        /// <param name="pilotFrequencyPolluteBlockList"></param>
        /// <param name="nextId"></param>
        /// <param name="testPoint"></param>
        /// <param name="cellList"></param>
        protected void gatherBlock(List<NRCellOfPilotFrequencyPolluteBlock> cellList, TestPoint testPoint, int freq)
        {
            List<NRPilotFrequencyPolluteBlock> tmpList = getTmpList(testPoint, freq);

            if (tmpList.Count == 0)
            {
                NRPilotFrequencyPolluteBlock item = new NRPilotFrequencyPolluteBlock(0);
                item.AddTestPoint(testPoint, cellList);
                pilotFrequencyPolluteBlockList.Add(item);
            }
            else
            {
                NRPilotFrequencyPolluteBlock blockTmp = null;
                for (int i = 0; i < tmpList.Count; i++)
                {
                    NRPilotFrequencyPolluteBlock block = tmpList[i];
                    if (blockTmp == null)
                    {
                        blockTmp = block;
                        block.AddTestPoint(testPoint, cellList);
                    }
                    else
                    {
                        blockTmp.Join(block);
                        pilotFrequencyPolluteBlockList.Remove(block);
                    }
                }
            }
        }

        private List<NRPilotFrequencyPolluteBlock> getTmpList(TestPoint testPoint, int freq)
        {
            List<NRPilotFrequencyPolluteBlock> tmpList = new List<NRPilotFrequencyPolluteBlock>();
            foreach (NRPilotFrequencyPolluteBlock block in pilotFrequencyPolluteBlockList)
            {
                if (coFreqOnly)
                {
                    bool canJoin = judgeCanJoin(freq, block);
                    if (!canJoin)
                    {
                        continue;
                    }
                }
                if (block.Intersect(testPoint.Longitude, testPoint.Latitude, PilotFrequencyPolluteBlockRadius))
                {
                    tmpList.Add(block);
                }
            }

            return tmpList;
        }

        private bool judgeCanJoin(int freq, NRPilotFrequencyPolluteBlock block)
        {
            bool canJoin = true;
            foreach (string cellName in block.CellDic.Keys)
            {
                NRCellOfPilotFrequencyPolluteBlock lteBlk = block.CellDic[cellName];
                if (lteBlk.NRCell.SSBARFCN != freq)
                {
                    canJoin = false;
                    break;
                }
            }

            return canJoin;
        }

        protected void getPilotFreqPolluteResult(List<NRPilotFrequencyPolluteBlock> pilotFrequencyPolluteBlockList)
        {
            MainModel.CurPilotFrequencyPolluteBlockList_NR.Clear();
            int nextID = 1;
            foreach (NRPilotFrequencyPolluteBlock block in pilotFrequencyPolluteBlockList)
            {
                block.GetResult();
                if (block.TestPointCount >= sampleCountLimit)
                {
                    block.ID = nextID++;
                    int nextSN = 1;
                    foreach (CellOfPilotFrequencyPolluteBlock cell in block.CellDic.Values)
                    {
                        NRCellOfPilotFrequencyPolluteBlock NRCell = cell as NRCellOfPilotFrequencyPolluteBlock;
                        NRCell.SN = nextSN++;
                    }
                    foreach (TestPoint testPoint in block.TestPoints)
                    {
                        MainModel.DTDataManager.Add(testPoint);
                    }
                    MainModel.CurPilotFrequencyPolluteBlockList_NR.Add(block);
                }
            }
        }

        protected virtual void getGoodSample()
        {
            int iloop = 0;
            WaitBox.Text = "开始获取正常采样点...";
            foreach (KeyValuePair<string, int> keyValue in curPilotFrequencyPolluteGridSampleCountDic)
            {
                string[] s = keyValue.Key.Split('|');
                double longitude = double.Parse(s[0]);
                double latitude = double.Parse(s[1]);
                foreach (NRPilotFrequencyPolluteBlock block in MainModel.CurPilotFrequencyPolluteBlockList_NR)
                {
                    if (block.Intersect(longitude, latitude, PilotFrequencyPolluteBlockRadius))
                    {
                        block.GoodTestPointCount += keyValue.Value;
                    }
                }

                WaitBox.ProgressPercent = (int)(100.0 * ++iloop / curPilotFrequencyPolluteGridSampleCountDic.Count);
            }
        }
    }
}
