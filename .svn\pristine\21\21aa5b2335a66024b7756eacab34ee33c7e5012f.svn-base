﻿namespace MasterCom.RAMS.Func
{
    partial class MapFormCellLayerPlanBTSProperties
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.Label label1;
            System.Windows.Forms.Label label100;
            System.Windows.Forms.Label label0;
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.cbxNO = new System.Windows.Forms.CheckBox();
            this.cbxComment = new System.Windows.Forms.CheckBox();
            this.cbxAddress = new System.Windows.Forms.CheckBox();
            this.cbxProgress = new System.Windows.Forms.CheckBox();
            this.cbxReason = new System.Windows.Forms.CheckBox();
            this.cbxType = new System.Windows.Forms.CheckBox();
            this.cbxLatitude = new System.Windows.Forms.CheckBox();
            this.cbxLongitude = new System.Windows.Forms.CheckBox();
            this.cbxName = new System.Windows.Forms.CheckBox();
            this.cbxDisplay = new System.Windows.Forms.CheckBox();
            this.cbxDrawBTSLabel = new System.Windows.Forms.CheckBox();
            this.buttonFont = new System.Windows.Forms.Button();
            this.labelDisplayColor = new System.Windows.Forms.Label();
            this.trackBarOpacity = new System.Windows.Forms.TrackBar();
            this.LabelOpacity = new System.Windows.Forms.Label();
            this.labelColor = new System.Windows.Forms.Label();
            this.numericUpDownSize = new System.Windows.Forms.NumericUpDown();
            label1 = new System.Windows.Forms.Label();
            label100 = new System.Windows.Forms.Label();
            label0 = new System.Windows.Forms.Label();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.trackBarOpacity)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownSize)).BeginInit();
            this.SuspendLayout();
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new System.Drawing.Point(19, 75);
            label1.Name = "label1";
            label1.Size = new System.Drawing.Size(29, 12);
            label1.TabIndex = 73;
            label1.Text = "尺寸";
            // 
            // label100
            // 
            label100.AutoSize = true;
            label100.Location = new System.Drawing.Point(168, 227);
            label100.Name = "label100";
            label100.Size = new System.Drawing.Size(29, 12);
            label100.TabIndex = 76;
            label100.Text = "100%";
            label100.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label0
            // 
            label0.AutoSize = true;
            label0.Location = new System.Drawing.Point(84, 227);
            label0.Name = "label0";
            label0.Size = new System.Drawing.Size(17, 12);
            label0.TabIndex = 75;
            label0.Text = "0%";
            label0.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.cbxNO);
            this.groupBox1.Controls.Add(this.cbxComment);
            this.groupBox1.Controls.Add(this.cbxAddress);
            this.groupBox1.Controls.Add(this.cbxProgress);
            this.groupBox1.Controls.Add(this.cbxReason);
            this.groupBox1.Controls.Add(this.cbxType);
            this.groupBox1.Controls.Add(this.cbxLatitude);
            this.groupBox1.Controls.Add(this.cbxLongitude);
            this.groupBox1.Controls.Add(this.cbxName);
            this.groupBox1.Location = new System.Drawing.Point(202, 32);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(104, 224);
            this.groupBox1.TabIndex = 1;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "标签";
            // 
            // cbxNO
            // 
            this.cbxNO.AutoSize = true;
            this.cbxNO.Location = new System.Drawing.Point(24, 21);
            this.cbxNO.Name = "cbxNO";
            this.cbxNO.Size = new System.Drawing.Size(42, 16);
            this.cbxNO.TabIndex = 0;
            this.cbxNO.Text = "NO.";
            this.cbxNO.UseVisualStyleBackColor = true;
            this.cbxNO.CheckedChanged += new System.EventHandler(this.cbxNO_CheckedChanged);
            // 
            // cbxComment
            // 
            this.cbxComment.AutoSize = true;
            this.cbxComment.Location = new System.Drawing.Point(24, 198);
            this.cbxComment.Name = "cbxComment";
            this.cbxComment.Size = new System.Drawing.Size(48, 16);
            this.cbxComment.TabIndex = 0;
            this.cbxComment.Text = "描述";
            this.cbxComment.UseVisualStyleBackColor = true;
            this.cbxComment.CheckedChanged += new System.EventHandler(this.cbxComment_CheckedChanged);
            // 
            // cbxAddress
            // 
            this.cbxAddress.AutoSize = true;
            this.cbxAddress.Location = new System.Drawing.Point(24, 176);
            this.cbxAddress.Name = "cbxAddress";
            this.cbxAddress.Size = new System.Drawing.Size(48, 16);
            this.cbxAddress.TabIndex = 0;
            this.cbxAddress.Text = "地址";
            this.cbxAddress.UseVisualStyleBackColor = true;
            this.cbxAddress.CheckedChanged += new System.EventHandler(this.cbxAddress_CheckedChanged);
            // 
            // cbxProgress
            // 
            this.cbxProgress.AutoSize = true;
            this.cbxProgress.Location = new System.Drawing.Point(24, 154);
            this.cbxProgress.Name = "cbxProgress";
            this.cbxProgress.Size = new System.Drawing.Size(48, 16);
            this.cbxProgress.TabIndex = 0;
            this.cbxProgress.Text = "进度";
            this.cbxProgress.UseVisualStyleBackColor = true;
            this.cbxProgress.CheckedChanged += new System.EventHandler(this.cbxProgress_CheckedChanged);
            // 
            // cbxReason
            // 
            this.cbxReason.AutoSize = true;
            this.cbxReason.Location = new System.Drawing.Point(24, 132);
            this.cbxReason.Name = "cbxReason";
            this.cbxReason.Size = new System.Drawing.Size(48, 16);
            this.cbxReason.TabIndex = 0;
            this.cbxReason.Text = "原因";
            this.cbxReason.UseVisualStyleBackColor = true;
            this.cbxReason.CheckedChanged += new System.EventHandler(this.checkReason_CheckedChanged);
            // 
            // cbxType
            // 
            this.cbxType.AutoSize = true;
            this.cbxType.Location = new System.Drawing.Point(24, 110);
            this.cbxType.Name = "cbxType";
            this.cbxType.Size = new System.Drawing.Size(48, 16);
            this.cbxType.TabIndex = 0;
            this.cbxType.Text = "类型";
            this.cbxType.UseVisualStyleBackColor = true;
            this.cbxType.CheckedChanged += new System.EventHandler(this.cbxType_CheckedChanged);
            // 
            // cbxLatitude
            // 
            this.cbxLatitude.AutoSize = true;
            this.cbxLatitude.Location = new System.Drawing.Point(24, 87);
            this.cbxLatitude.Name = "cbxLatitude";
            this.cbxLatitude.Size = new System.Drawing.Size(48, 16);
            this.cbxLatitude.TabIndex = 0;
            this.cbxLatitude.Text = "纬度";
            this.cbxLatitude.UseVisualStyleBackColor = true;
            this.cbxLatitude.CheckedChanged += new System.EventHandler(this.cbxLatitude_CheckedChanged);
            // 
            // cbxLongitude
            // 
            this.cbxLongitude.AutoSize = true;
            this.cbxLongitude.Location = new System.Drawing.Point(24, 65);
            this.cbxLongitude.Name = "cbxLongitude";
            this.cbxLongitude.Size = new System.Drawing.Size(48, 16);
            this.cbxLongitude.TabIndex = 0;
            this.cbxLongitude.Text = "经度";
            this.cbxLongitude.UseVisualStyleBackColor = true;
            this.cbxLongitude.CheckedChanged += new System.EventHandler(this.cbxLongitude_CheckedChanged);
            // 
            // cbxName
            // 
            this.cbxName.AutoSize = true;
            this.cbxName.Location = new System.Drawing.Point(24, 43);
            this.cbxName.Name = "cbxName";
            this.cbxName.Size = new System.Drawing.Size(48, 16);
            this.cbxName.TabIndex = 0;
            this.cbxName.Text = "名称";
            this.cbxName.UseVisualStyleBackColor = true;
            this.cbxName.CheckedChanged += new System.EventHandler(this.cbxName_CheckedChanged);
            // 
            // cbxDisplay
            // 
            this.cbxDisplay.AutoSize = true;
            this.cbxDisplay.Location = new System.Drawing.Point(14, 10);
            this.cbxDisplay.Name = "cbxDisplay";
            this.cbxDisplay.Size = new System.Drawing.Size(48, 16);
            this.cbxDisplay.TabIndex = 0;
            this.cbxDisplay.Text = "显示";
            this.cbxDisplay.UseVisualStyleBackColor = true;
            this.cbxDisplay.CheckedChanged += new System.EventHandler(this.cbxDisplay_CheckedChanged);
            // 
            // cbxDrawBTSLabel
            // 
            this.cbxDrawBTSLabel.AutoSize = true;
            this.cbxDrawBTSLabel.Location = new System.Drawing.Point(204, 10);
            this.cbxDrawBTSLabel.Name = "cbxDrawBTSLabel";
            this.cbxDrawBTSLabel.Size = new System.Drawing.Size(72, 16);
            this.cbxDrawBTSLabel.TabIndex = 51;
            this.cbxDrawBTSLabel.Text = "显示标签";
            this.cbxDrawBTSLabel.UseVisualStyleBackColor = true;
            this.cbxDrawBTSLabel.CheckedChanged += new System.EventHandler(this.checkBoxDrawBTSLabel_CheckedChanged);
            // 
            // buttonFont
            // 
            this.buttonFont.Location = new System.Drawing.Point(312, 37);
            this.buttonFont.Name = "buttonFont";
            this.buttonFont.Size = new System.Drawing.Size(75, 23);
            this.buttonFont.TabIndex = 72;
            this.buttonFont.Text = "字体...";
            this.buttonFont.UseVisualStyleBackColor = true;
            this.buttonFont.Click += new System.EventHandler(this.buttonFont_Click);
            // 
            // labelDisplayColor
            // 
            this.labelDisplayColor.BackColor = System.Drawing.Color.Orange;
            this.labelDisplayColor.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.labelDisplayColor.Location = new System.Drawing.Point(54, 32);
            this.labelDisplayColor.Name = "labelDisplayColor";
            this.labelDisplayColor.Size = new System.Drawing.Size(25, 25);
            this.labelDisplayColor.TabIndex = 71;
            this.labelDisplayColor.Click += new System.EventHandler(this.labelDisplayColor_Click);
            // 
            // trackBarOpacity
            // 
            this.trackBarOpacity.LargeChange = 32;
            this.trackBarOpacity.Location = new System.Drawing.Point(72, 194);
            this.trackBarOpacity.Maximum = 255;
            this.trackBarOpacity.Name = "trackBarOpacity";
            this.trackBarOpacity.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.trackBarOpacity.Size = new System.Drawing.Size(131, 45);
            this.trackBarOpacity.TabIndex = 70;
            this.trackBarOpacity.TickFrequency = 32;
            this.trackBarOpacity.Value = 255;
            this.trackBarOpacity.Scroll += new System.EventHandler(this.TrackBarOpacity_Scroll);
            // 
            // LabelOpacity
            // 
            this.LabelOpacity.Location = new System.Drawing.Point(19, 198);
            this.LabelOpacity.Name = "LabelOpacity";
            this.LabelOpacity.Size = new System.Drawing.Size(56, 16);
            this.LabelOpacity.TabIndex = 69;
            this.LabelOpacity.Text = "透明度";
            // 
            // labelColor
            // 
            this.labelColor.Location = new System.Drawing.Point(19, 40);
            this.labelColor.Name = "labelColor";
            this.labelColor.Size = new System.Drawing.Size(47, 20);
            this.labelColor.TabIndex = 68;
            this.labelColor.Text = "颜色";
            this.labelColor.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // numericUpDownSize
            // 
            this.numericUpDownSize.Location = new System.Drawing.Point(54, 70);
            this.numericUpDownSize.Maximum = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numericUpDownSize.Minimum = new decimal(new int[] {
            4,
            0,
            0,
            0});
            this.numericUpDownSize.Name = "numericUpDownSize";
            this.numericUpDownSize.Size = new System.Drawing.Size(34, 21);
            this.numericUpDownSize.TabIndex = 74;
            this.numericUpDownSize.Value = new decimal(new int[] {
            4,
            0,
            0,
            0});
            this.numericUpDownSize.ValueChanged += new System.EventHandler(this.numericUpDownSize_ValueChanged);
            // 
            // MapFormCellLayerPlanBTSProperties
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(label100);
            this.Controls.Add(label0);
            this.Controls.Add(this.numericUpDownSize);
            this.Controls.Add(label1);
            this.Controls.Add(this.buttonFont);
            this.Controls.Add(this.labelDisplayColor);
            this.Controls.Add(this.trackBarOpacity);
            this.Controls.Add(this.LabelOpacity);
            this.Controls.Add(this.labelColor);
            this.Controls.Add(this.cbxDrawBTSLabel);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.cbxDisplay);
            this.Name = "MapFormCellLayerPlanBTSProperties";
            this.Size = new System.Drawing.Size(430, 268);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.trackBarOpacity)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownSize)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.CheckBox cbxReason;
        private System.Windows.Forms.CheckBox cbxType;
        private System.Windows.Forms.CheckBox cbxLatitude;
        private System.Windows.Forms.CheckBox cbxLongitude;
        private System.Windows.Forms.CheckBox cbxName;
        private System.Windows.Forms.CheckBox cbxDisplay;
        private System.Windows.Forms.CheckBox cbxDrawBTSLabel;
        private System.Windows.Forms.Button buttonFont;
        private System.Windows.Forms.Label labelDisplayColor;
        private System.Windows.Forms.TrackBar trackBarOpacity;
        private System.Windows.Forms.Label LabelOpacity;
        private System.Windows.Forms.Label labelColor;
        private System.Windows.Forms.CheckBox cbxNO;
        private System.Windows.Forms.CheckBox cbxComment;
        private System.Windows.Forms.CheckBox cbxAddress;
        private System.Windows.Forms.CheckBox cbxProgress;
        private System.Windows.Forms.NumericUpDown numericUpDownSize;
    }
}
