﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc.KPISheet
{
    public class TableTemplate
    {
        public TableTemplate(string typeName,string tableName,AreaType areaType)
        {
            this.TypeName = typeName;
            this.Name = tableName;
            this.AreaType = areaType;
            Columns = new List<TableColumn>();
        }

        public AreaType AreaType
        {
            get;
            private set;
        }

        public void AddColumn(string colName,E_VType valueType)
        {
            TableColumn col = new TableColumn(this, colName, valueType);
            Columns.Add(col);
        }

        public string Name
        {
            get;
            set;
        }

        public List<TableColumn> Columns
        {
            get;
            set;
        }

        public string TypeName
        {
            get;
            set;
        }

    }
}
