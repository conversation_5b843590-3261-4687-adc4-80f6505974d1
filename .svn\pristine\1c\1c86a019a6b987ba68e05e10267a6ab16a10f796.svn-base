﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class CellMultiCoverageForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(CellMultiCoverageForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportToExcelSimple = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.btnCancelCloud = new DevExpress.XtraEditors.SimpleButton();
            this.btnDrawCloud = new DevExpress.XtraEditors.SimpleButton();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.cellRadioType = new DevExpress.XtraEditors.RadioGroup();
            this.btnColorSetting = new DevExpress.XtraEditors.SimpleButton();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.radioGroupType = new DevExpress.XtraEditors.RadioGroup();
            this.listViewTotal = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLacCi = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDistanceCov = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgMultiCovLev = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRelConditionSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAbsLevel = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAbsConditionSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellsInRegion = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSubCellLacCi = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFrequency = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAvgRxlev = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cellRadioType.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroupType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel,
            this.miExportToExcelSimple,
            this.toolStripMenuItem1,
            this.miExpandAll,
            this.miCallapsAll});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(190, 120);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(189, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // miExportToExcelSimple
            // 
            this.miExportToExcelSimple.Name = "miExportToExcelSimple";
            this.miExportToExcelSimple.Size = new System.Drawing.Size(189, 22);
            this.miExportToExcelSimple.Text = "导出简单信息到Excel";
            this.miExportToExcelSimple.Click += new System.EventHandler(this.miExportToExcelSimple_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(186, 6);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(189, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCallapsAll
            // 
            this.miCallapsAll.Name = "miCallapsAll";
            this.miCallapsAll.Size = new System.Drawing.Size(189, 22);
            this.miCallapsAll.Text = "全部合并";
            this.miCallapsAll.Click += new System.EventHandler(this.miCallapsAll_Click);
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.btnCancelCloud);
            this.groupControl1.Controls.Add(this.btnDrawCloud);
            this.groupControl1.Controls.Add(this.groupBox2);
            this.groupControl1.Controls.Add(this.btnColorSetting);
            this.groupControl1.Controls.Add(this.groupBox1);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(1163, 87);
            this.groupControl1.TabIndex = 1;
            this.groupControl1.Text = "GIS显示设置";
            // 
            // btnCancelCloud
            // 
            this.btnCancelCloud.Enabled = false;
            this.btnCancelCloud.Location = new System.Drawing.Point(857, 46);
            this.btnCancelCloud.Name = "btnCancelCloud";
            this.btnCancelCloud.Size = new System.Drawing.Size(75, 23);
            this.btnCancelCloud.TabIndex = 8;
            this.btnCancelCloud.Text = "撤销云图";
            this.btnCancelCloud.Click += new System.EventHandler(this.btnCancelCloud_Click);
            // 
            // btnDrawCloud
            // 
            this.btnDrawCloud.Location = new System.Drawing.Point(763, 46);
            this.btnDrawCloud.Name = "btnDrawCloud";
            this.btnDrawCloud.Size = new System.Drawing.Size(75, 23);
            this.btnDrawCloud.TabIndex = 7;
            this.btnDrawCloud.Text = "显示云图";
            this.btnDrawCloud.Click += new System.EventHandler(this.btnDrawCloud_Click);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.cellRadioType);
            this.groupBox2.Location = new System.Drawing.Point(420, 28);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(324, 51);
            this.groupBox2.TabIndex = 6;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "云图设置";
            // 
            // cellRadioType
            // 
            this.cellRadioType.EditValue = true;
            this.cellRadioType.Location = new System.Drawing.Point(15, 18);
            this.cellRadioType.Name = "cellRadioType";
            this.cellRadioType.Properties.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.cellRadioType.Properties.Appearance.Options.UseBackColor = true;
            this.cellRadioType.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.cellRadioType.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(true, "GSM900"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "DCS1800"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "全部")});
            this.cellRadioType.Size = new System.Drawing.Size(294, 27);
            this.cellRadioType.TabIndex = 0;
            this.cellRadioType.SelectedIndexChanged += new System.EventHandler(this.cellRadioType_SelectedIndexChanged);
            // 
            // btnColorSetting
            // 
            this.btnColorSetting.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnColorSetting.Location = new System.Drawing.Point(1076, 46);
            this.btnColorSetting.Name = "btnColorSetting";
            this.btnColorSetting.Size = new System.Drawing.Size(75, 23);
            this.btnColorSetting.TabIndex = 1;
            this.btnColorSetting.Text = "着色设置...";
            this.btnColorSetting.Click += new System.EventHandler(this.btnColorSetting_Click);
            // 
            // groupBox1
            // 
            this.groupBox1.BackColor = System.Drawing.Color.Transparent;
            this.groupBox1.Controls.Add(this.radioGroupType);
            this.groupBox1.Location = new System.Drawing.Point(12, 28);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(381, 51);
            this.groupBox1.TabIndex = 5;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "覆盖度类别";
            // 
            // radioGroupType
            // 
            this.radioGroupType.EditValue = true;
            this.radioGroupType.Location = new System.Drawing.Point(36, 16);
            this.radioGroupType.Name = "radioGroupType";
            this.radioGroupType.Properties.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.radioGroupType.Properties.Appearance.Options.UseBackColor = true;
            this.radioGroupType.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.radioGroupType.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(true, "相对覆盖度(-12dBm内)"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "绝对覆盖度( > -80dBm)")});
            this.radioGroupType.Size = new System.Drawing.Size(333, 33);
            this.radioGroupType.TabIndex = 0;
            this.radioGroupType.SelectedIndexChanged += new System.EventHandler(this.radioGroupType_SelectedIndexChanged);
            // 
            // listViewTotal
            // 
            this.listViewTotal.AllColumns.Add(this.olvColumnSN);
            this.listViewTotal.AllColumns.Add(this.olvColumnCellName);
            this.listViewTotal.AllColumns.Add(this.olvColumnLacCi);
            this.listViewTotal.AllColumns.Add(this.olvColumnSampleCount);
            this.listViewTotal.AllColumns.Add(this.olvColumnDistanceCov);
            this.listViewTotal.AllColumns.Add(this.olvColumnAvgMultiCovLev);
            this.listViewTotal.AllColumns.Add(this.olvColumnRelConditionSampleCount);
            this.listViewTotal.AllColumns.Add(this.olvColumnAbsLevel);
            this.listViewTotal.AllColumns.Add(this.olvColumnAbsConditionSampleCount);
            this.listViewTotal.AllColumns.Add(this.olvColumnCellsInRegion);
            this.listViewTotal.AllColumns.Add(this.olvColumnSubCellLacCi);
            this.listViewTotal.AllColumns.Add(this.olvColumnDistance);
            this.listViewTotal.AllColumns.Add(this.olvColumnFrequency);
            this.listViewTotal.AllColumns.Add(this.olvColumnAvgRxlev);
            this.listViewTotal.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.listViewTotal.AutoArrange = false;
            this.listViewTotal.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnCellName,
            this.olvColumnLacCi,
            this.olvColumnSampleCount,
            this.olvColumnDistanceCov,
            this.olvColumnAvgMultiCovLev,
            this.olvColumnRelConditionSampleCount,
            this.olvColumnAbsLevel,
            this.olvColumnAbsConditionSampleCount,
            this.olvColumnCellsInRegion,
            this.olvColumnSubCellLacCi,
            this.olvColumnDistance,
            this.olvColumnFrequency,
            this.olvColumnAvgRxlev});
            this.listViewTotal.ContextMenuStrip = this.ctxMenu;
            this.listViewTotal.Cursor = System.Windows.Forms.Cursors.Default;
            this.listViewTotal.FullRowSelect = true;
            this.listViewTotal.GridLines = true;
            this.listViewTotal.HeaderWordWrap = true;
            this.listViewTotal.IsNeedShowOverlay = false;
            this.listViewTotal.Location = new System.Drawing.Point(0, 90);
            this.listViewTotal.Name = "listViewTotal";
            this.listViewTotal.OwnerDraw = true;
            this.listViewTotal.ShowGroups = false;
            this.listViewTotal.Size = new System.Drawing.Size(1163, 397);
            this.listViewTotal.TabIndex = 5;
            this.listViewTotal.UseCompatibleStateImageBehavior = false;
            this.listViewTotal.View = System.Windows.Forms.View.Details;
            this.listViewTotal.VirtualMode = true;
            this.listViewTotal.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewTotal_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 70;
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名称";
            this.olvColumnCellName.Width = 100;
            // 
            // olvColumnLacCi
            // 
            this.olvColumnLacCi.HeaderFont = null;
            this.olvColumnLacCi.Text = "小区LAC-CI";
            this.olvColumnLacCi.Width = 100;
            // 
            // olvColumnSampleCount
            // 
            this.olvColumnSampleCount.HeaderFont = null;
            this.olvColumnSampleCount.Text = "采样点个数";
            this.olvColumnSampleCount.Width = 70;
            // 
            // olvColumnDistanceCov
            // 
            this.olvColumnDistanceCov.HeaderFont = null;
            this.olvColumnDistanceCov.Text = "覆盖距离(米)";
            this.olvColumnDistanceCov.Width = 80;
            // 
            // olvColumnAvgMultiCovLev
            // 
            this.olvColumnAvgMultiCovLev.HeaderFont = null;
            this.olvColumnAvgMultiCovLev.Text = "相对重叠覆盖度";
            this.olvColumnAvgMultiCovLev.Width = 100;
            // 
            // olvColumnRelConditionSampleCount
            // 
            this.olvColumnRelConditionSampleCount.HeaderFont = null;
            this.olvColumnRelConditionSampleCount.Text = "相对条件样本点数";
            // 
            // olvColumnAbsLevel
            // 
            this.olvColumnAbsLevel.HeaderFont = null;
            this.olvColumnAbsLevel.Text = "绝对重叠覆盖度";
            this.olvColumnAbsLevel.Width = 100;
            // 
            // olvColumnAbsConditionSampleCount
            // 
            this.olvColumnAbsConditionSampleCount.HeaderFont = null;
            this.olvColumnAbsConditionSampleCount.Text = "绝对条件样本点数";
            // 
            // olvColumnCellsInRegion
            // 
            this.olvColumnCellsInRegion.HeaderFont = null;
            this.olvColumnCellsInRegion.Text = "覆盖带内小区名称";
            this.olvColumnCellsInRegion.Width = 110;
            // 
            // olvColumnSubCellLacCi
            // 
            this.olvColumnSubCellLacCi.HeaderFont = null;
            this.olvColumnSubCellLacCi.Text = "小区LAC-CI";
            this.olvColumnSubCellLacCi.Width = 100;
            // 
            // olvColumnDistance
            // 
            this.olvColumnDistance.HeaderFont = null;
            this.olvColumnDistance.Text = "两小区距离(米)";
            this.olvColumnDistance.Width = 100;
            // 
            // olvColumnFrequency
            // 
            this.olvColumnFrequency.HeaderFont = null;
            this.olvColumnFrequency.Text = "涉及采样点数";
            this.olvColumnFrequency.Width = 70;
            // 
            // olvColumnAvgRxlev
            // 
            this.olvColumnAvgRxlev.HeaderFont = null;
            this.olvColumnAvgRxlev.Text = "平均场强";
            this.olvColumnAvgRxlev.Width = 70;
            // 
            // CellMultiCoverageForm
            // 
            this.Appearance.BackColor = System.Drawing.Color.White;
            this.Appearance.Options.UseBackColor = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.AutoSize = true;
            this.ClientSize = new System.Drawing.Size(1163, 488);
            this.Controls.Add(this.groupControl1);
            this.Controls.Add(this.listViewTotal);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "CellMultiCoverageForm";
            this.Text = "GSM小区重叠覆盖";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.cellRadioType.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.radioGroupType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCallapsAll;
        private BrightIdeasSoftware.TreeListView listViewTotal;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnLacCi;
        private BrightIdeasSoftware.OLVColumn olvColumnSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnDistanceCov;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgMultiCovLev;
        private BrightIdeasSoftware.OLVColumn olvColumnAbsLevel;
        private BrightIdeasSoftware.OLVColumn olvColumnCellsInRegion;
        private BrightIdeasSoftware.OLVColumn olvColumnSubCellLacCi;
        private BrightIdeasSoftware.OLVColumn olvColumnDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnFrequency;
        private BrightIdeasSoftware.OLVColumn olvColumnAvgRxlev;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.SimpleButton btnColorSetting;
        private DevExpress.XtraEditors.RadioGroup radioGroupType;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcelSimple;
        private BrightIdeasSoftware.OLVColumn olvColumnRelConditionSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnAbsConditionSampleCount;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private DevExpress.XtraEditors.RadioGroup cellRadioType;
        private DevExpress.XtraEditors.SimpleButton btnCancelCloud;
        private DevExpress.XtraEditors.SimpleButton btnDrawCloud;

    }
}