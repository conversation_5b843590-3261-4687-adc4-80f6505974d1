﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class PoorRsrqRoad_NR: PoorRsrqRoad
    {
        protected int lteRsrpCount = 0;
        protected float lteRsrpSum = 0;
        public float? AvgLteRSRP { get { return caculateAvg(lteRsrpCount, lteRsrpSum); } }

        protected int lteSinrCount = 0;
        protected float lteSinrSum = 0;
        public float? AvgLteSINR { get { return caculateAvg(lteSinrCount, lteSinrSum); } }

        public string AreaName { get; set; }
        readonly List<string> areaList = new List<string>();

        public List<string> ArfcnPciList { get; set; } = new List<string>();
        public string ArfcnPcis
        {
            get
            {
                StringBuilder arfcnPcis = new StringBuilder();
                foreach (string arfcnPci in ArfcnPciList)
                {
                    if (arfcnPcis.Length > 0)
                    {
                        arfcnPcis.Append(" | ");
                    }
                    arfcnPcis.Append(arfcnPci);
                }
                return arfcnPcis.ToString();
            }
        }
        protected override void addOtherTPInfo(TestPoint testPoint)
        {
            float? lteRsrp = NRTpHelper.NrLteTpManager.GetSCellRsrp(testPoint, true);
            if (lteRsrp != null)
            {
                lteRsrpCount++;
                lteRsrpSum += (float)lteRsrp;
            }

            float? lteSinr = NRTpHelper.NrLteTpManager.GetSCellSinr(testPoint, true);
            if (lteSinr != null)
            {
                lteSinrCount++;
                lteSinrSum += (float)lteSinr;
            }

            string area = GISManager.GetInstance().GetAreaPlaceDesc(testPoint.Longitude, testPoint.Latitude);
            if (!string.IsNullOrEmpty(area) && !areaList.Contains(area))
            {
                areaList.Add(area);
            }
        }

        protected float? caculateAvg(int count, float sum)
        {
            float? res = null;
            if (count > 0)
            {
                res = (float?)Math.Round(sum / count, 2);
            }
            return res;
        }

        protected override void addCellList(TestPoint testPoint)
        {
            NRCell nrCell = testPoint.GetMainCell_NR();
            if (nrCell != null)
            {
                //string lacci = nrCell.TAC.ToString() + "_" + nrCell.CellID.ToString();
                //if (!lacciList.Contains(lacci))
                //{
                //    lacciList.Add(lacci);
                //}
                string arfcnPci = nrCell.SSBARFCN.ToString() + "_" + nrCell.PCI.ToString();
                if (!ArfcnPciList.Contains(arfcnPci))
                {
                    ArfcnPciList.Add(arfcnPci);
                }

                if (!cellNames.Contains(nrCell.Name))
                {
                    cellNames.Add(nrCell.Name);
                }
            }
        }

        public override void MakeSummary()
        {
            base.MakeSummary();
            AreaName = getStrDesc(areaList);
        }

        private string getStrDesc(List<string> strList)
        {
            char charSplit = '；';
            StringBuilder strb = new StringBuilder();
            foreach (string str in strList)
            {
                strb.Append(str + charSplit);
            }

            return strb.ToString().TrimEnd(charSplit);
        }
    }
}
