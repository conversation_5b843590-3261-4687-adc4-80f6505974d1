﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc.StationAccept_Shanxi
{
    public class DiyQueryStationAcceptParam_SX : DIYSQLBase
    {
        public DiyQueryStationAcceptParam_SX(WorkParamsImportCondtion curCondtion)
             : base()
        {
            MainDB = true;
            this.curCondtion = curCondtion;
        }

        public List<CellAcceptWorkParam_SX> ResList { get; set; }
        readonly WorkParamsImportCondtion curCondtion;

        protected override void queryInThread(object o)
        {
            ResList = new List<CellAcceptWorkParam_SX>();
            base.queryInThread(o);
            WaitBox.Close();
        }

        protected override string getSqlTextString()
        {
            string sql = string.Format(@"SELECT [DistrictName],[BtsName],[ENodeBID],[CellName],[CellID],[SectorID],[Tac],[Eci],[Earfcn],[Pci],[Longitude],[Latitude],[CoverTypeDes],[Altitude],[Direction],[Downward],[AnalysedType],[UpdateTime],[Remark],[ImportTime] FROM [tb_btscheck_SX_cfg_cell] where AnalysedType in (-1,0,1) and ImportTime between '{0}' and '{1}'",
            curCondtion.StartDate, curCondtion.EndDate);
            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            E_VType[] arr = new E_VType[20];
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i] = E_VType.E_String;
            return arr;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CellAcceptWorkParam_SX cellInfo = new CellAcceptWorkParam_SX();
                    cellInfo.FillDataByDB(package);
                    cellInfo.AnalysedType = package.Content.GetParamInt();
                    cellInfo.AnalysedTypeString = EnumDescriptionAttribute.GetText((AnalyseType)cellInfo.AnalysedType);
                    cellInfo.UpdateTimeDesc = package.Content.GetParamString();
                    if (!string.IsNullOrEmpty(cellInfo.UpdateTimeDesc))
                    {
                        cellInfo.UpdateTime = Convert.ToDateTime(cellInfo.UpdateTimeDesc);
                    }
                    cellInfo.Remark = package.Content.GetParamString();
                    cellInfo.ImportTimeDesc = package.Content.GetParamString();
                    if (!string.IsNullOrEmpty(cellInfo.ImportTimeDesc))
                    {
                        cellInfo.ImportTime = Convert.ToDateTime(cellInfo.ImportTimeDesc);
                    }
                    ResList.Add(cellInfo);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }
    }
}
