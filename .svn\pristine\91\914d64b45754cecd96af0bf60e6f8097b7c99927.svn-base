﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class DIYFrequencyShortageOnTheMoveByRegion_GSCAN : DIYFrequencyShortageByRegion_GSCAN
    {
        protected int secondFading = 5;
        protected int rxLevDValueFading = 10;
        public DIYFrequencyShortageOnTheMoveByRegion_GSCAN(MainModel mainModel)
            : base(mainModel)
        {
            FilterSampleByRegion = false;
            IncludeEvent = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.GSM_SCAN);
        }

        public override string Name
        {
            get { return "频率资源不足(移动导致)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 15000, 15028, this.Name);//////
        }

        readonly FrequencyShortageOnTheMoveDlg_GSCAN conditionDlg = new FrequencyShortageOnTheMoveDlg_GSCAN();
        protected override bool getCondition()
        {
            if (conditionDlg.ShowDialog() == DialogResult.OK)
            {
                int band = 0;
                conditionDlg.GetFilterCondition(out band, out rxLevDValue, out secondLast, out rxLevDValueOther, out freqCountRateThreshold,
                    out secondFading, out rxLevDValueFading);
                bandType = (BandTypeFilter)band;
                return true;
            }
            return false;
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    List<TestPoint> testPointList = fileDataManager.TestPoints;
                    clearIntermediateVariable();
                    for (int i = 0; i < testPointList.Count; i++)
                    {
                        TestPoint testPoint = testPointList[i];
                        List<int> idxList = GetIdxList(testPoint, this.bandType);

                        if (idxList.Count > 0 && isValidTestPoint(testPoint))
                        {
                            Dictionary<Cell, float> cellRxLevDic = getCellRxLevDic(testPointList, i, testPoint, idxList);

                            judgeTestPoint(testPointList, i, cellRxLevDic);
                        }
                        else
                        {
                            clearIntermediateVariable();
                        }
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        private Dictionary<Cell, float> getCellRxLevDic(List<TestPoint> testPointList, int i, TestPoint testPoint, List<int> idxList)
        {
            Dictionary<Cell, float> cellRxLevDic = new Dictionary<Cell, float>();
            float? rxLevMax = (float?)testPoint["GSCAN_RxLev", idxList[0]];
            foreach (int idx in idxList)
            {
                int j = idx;
                float? rxLev = (float?)testPoint["GSCAN_RxLev", j];
                if (rxLev == null || rxLev > -10 || rxLev < -120)
                {
                    break;
                }
                int? bcch = (int?)testPoint["GSCAN_BCCH", j];
                int? bsic = (int?)testPoint["GSCAN_BSIC", j];
                Cell cell = CellManager.GetInstance().GetNearestCell(testPoint.DateTime, (short)bcch, (byte)bsic, testPoint.Longitude, testPoint.Latitude);
                if (cell != null)
                {
                    cellRxLevDic[cell] = (float)rxLev;
                    judgeCell(rxLevMax, rxLev, cell, testPointList, i);
                }
            }

            return cellRxLevDic;
        }

        /// <summary>
        /// 确定是否对象小区（条件一：与一强相差8db内并持续10秒；条件二：在之前10秒，信号强度大于等于对象小区-12dBm的同频段小区载波数（含对象小区）小于该频段可用频点总数的50%），做四件事：
        /// 1.如果与一强相差8db，放入小区持续队列；
        /// 2.如果与一强相差8db，小区持续满10秒，判断条件二，如果满足放入对象小区队列，从小区持续队列中移除，不满足直接移除；
        /// 3.如果与一强相差8db，在对象小区队列中存在前一个采样点，并且前一个采样点(前一个才有持续性)的对象小区中有该小区，更新该小区到当前采样点的对象小区，并从前一个采样点的对象小区中移除。
        /// 4.如果与一强相差超过8db，从小区持续队列移除。
        /// </summary>
        /// <param name="rxLevMax">一强小区电平</param>
        /// <param name="rxLev">当前小区电平</param>
        /// <param name="cell">当前小区</param>
        /// <param name="curIndex">当前采样点序号</param>
        /// <param name="testPoint">当前采样点</param>
        /// <param name="cellLastDic">小区持续队列</param>
        /// <param name="tpCellValidDic">对象小区队列</param>
        protected override void judgeCell(float? rxLevMax, float? rxLev, Cell cell, List<TestPoint> testPointList, int curIndex)
        {
            try
            {
                TestPoint testPoint = testPointList[curIndex];
                if (rxLevMax - rxLev <= rxLevDValue)    //与一强相差8db内
                {
                    if (!cellLastDic.ContainsKey(cell))
                    {
                        CellLast cellLast = new CellLast();
                        cellLastDic[cell] = cellLast;
                    }
                    cellLastDic[cell].Add(testPoint, (float)rxLev);

                    setTpCellValidDic(cell, testPointList, curIndex);

                    judgeValidCell(rxLev, cell, testPointList, curIndex, testPoint);
                }
                else
                {
                    //未满10秒移除
                    if (cellLastDic.ContainsKey(cell))
                    {
                        cellLastDic.Remove(cell);
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        private void setTpCellValidDic(Cell cell, List<TestPoint> testPointList, int curIndex)
        {
            if (cellLastDic[cell].SecondLast >= secondLast) //持续10秒，满足前提条件
            {
                //判断是否满足条件二，满足，移至对象小区队列，并移除
                int k = curIndex - 1;
                for (; k >= 0; k--)
                {
                    if (testPointList[curIndex].Time - testPointList[k].Time >= 5)
                    {
                        break;
                    }
                }
                Dictionary<Cell, float> cellDic = tpCellDic[k];
                double freqCountRate = getFreqCountRate(cell, cellDic);
                if (100.0 * freqCountRate < freqCountRateThreshold) //同频段小区频点数占可用频点数小于50%
                {
                    if (!tpCellValidDic.ContainsKey(curIndex))
                    {
                        Dictionary<Cell, CellLast> cellValidDic = new Dictionary<Cell, CellLast>();
                        tpCellValidDic[curIndex] = cellValidDic;
                    }
                    tpCellValidDic[curIndex][cell] = cellLastDic[cell];
                }
                cellLastDic.Remove(cell);
            }
        }

        private void judgeValidCell(float? rxLev, Cell cell, List<TestPoint> testPointList, int curIndex, TestPoint testPoint)
        {
            //更新已经满10秒并当前点5秒前的占比还是满足对象小区条件的，未形成频率资源不足的点，延续到下个点判断(只判断当前点的前一个点才有延续性)    
            List<int> tpList = new List<int>(tpCellValidDic.Keys);
            foreach (int index in tpList)
            {
                if (index == curIndex - 1)
                {
                    List<Cell> cellList = new List<Cell>(tpCellValidDic[index].Keys);
                    foreach (Cell cellTP in cellList)
                    {
                        dealwithCell(rxLev, cell, testPointList, curIndex, testPoint, index, cellTP);
                    }
                }
            }
        }

        private void dealwithCell(float? rxLev, Cell cell, List<TestPoint> testPointList, int curIndex, TestPoint testPoint, int index, Cell cellTP)
        {
            if (cell == cellTP)
            {
                int k = curIndex - 1;
                for (; k >= 0; k--)
                {
                    if (testPointList[curIndex].Time - testPointList[k].Time >= 5)
                    {
                        break;
                    }
                }
                Dictionary<Cell, float> cellDic = tpCellDic[k];
                addValidCurCell(rxLev, cell, curIndex, testPoint, index, cellDic);
            }
        }

        private void addValidCurCell(float? rxLev, Cell cell, int curIndex, TestPoint testPoint, int index, Dictionary<Cell, float> cellDic)
        {
            double freqCountRate = getFreqCountRate(cell, cellDic);
            if (100.0 * freqCountRate < freqCountRateThreshold) //同频段小区频点数占可用频点数小于50%
            {
                //对象小区加入当前采样点
                if (!tpCellValidDic.ContainsKey(curIndex))
                {
                    Dictionary<Cell, CellLast> cellValidDic = new Dictionary<Cell, CellLast>();
                    tpCellValidDic[curIndex] = cellValidDic;
                }
                if (!tpCellValidDic[curIndex].ContainsKey(cell))
                {
                    tpCellValidDic[curIndex][cell] = tpCellValidDic[index][cell];
                }
                tpCellValidDic[curIndex][cell].Add(testPoint, (float)rxLev);

                //对象小区从前一个采样点移除，如果前一个采样点没有对象小区，移除
                tpCellValidDic[index].Remove(cell);
                if (tpCellValidDic[index].Count == 0)
                {
                    tpCellValidDic.Remove(index);
                }
            }
        }

        /// <summary>
        /// 在每个采样点判断，做三件事：
        /// 1.小区持续列表中移除不连续的小区，即之前连续，在当前采样点中未出现；
        /// 2.采样点对象小区列表中，未形成频率资源不足的移除(如果不是当前采样点，说明在之前判断未形成频率资源不足，如果形成，已经移除，有延续性的小区也已经移到当前采样点)；
        /// 3.判断采样点对象小区列表中对象小区是否形成频率资源不足（列表中只有当前采样点，不是当前采样点的第二步已经移除）。
        /// </summary>
        /// <param name="testPointList">采样点列表</param>
        /// <param name="i">当前采样点序号</param>
        /// <param name="cellRxLevDic">当前采样点小区列表</param>
        protected override void judgeTestPoint(List<TestPoint> testPointList, int i, Dictionary<Cell, float> cellRxLevDic)
        {
            try
            {
                tpCellDic[i] = cellRxLevDic;
                //不连续的小区移除
                List<Cell> removeCellList = new List<Cell>(cellLastDic.Keys);
                foreach (Cell cell in removeCellList)
                {
                    if (!cellRxLevDic.ContainsKey(cell))
                    {
                        cellLastDic.Remove(cell);
                    }
                }

                //满足前提条件小区，中断或者5秒内衰减超过10db或者未形成频率资源不足的移除
                List<int> removeTPList = new List<int>(tpCellValidDic.Keys);
                foreach (int index in removeTPList)
                {
                    removeCellValidDic(testPointList, i, cellRxLevDic, index);
                }

                //判断是否形成频率资源不足
                removeTPList = new List<int>(tpCellValidDic.Keys);
                removeCellValidDic(testPointList, i, removeTPList);
            }
            catch
            {
                //continue
            }
        }

        private void removeCellValidDic(List<TestPoint> testPointList, int i, Dictionary<Cell, float> cellRxLevDic, int index)
        {
            //5秒内未形成频率资源不足
            if (testPointList[i].Time - testPointList[index].Time > secondFading)
            {
                tpCellValidDic.Remove(index);
            }
            else
            {
                List<Cell> cellList = new List<Cell>(tpCellValidDic[index].Keys);
                foreach (Cell cell in cellList)
                {
                    //中断或者超过10db
                    if (!cellRxLevDic.ContainsKey(cell) || tpCellValidDic[index][cell].RxLevLastAvg - cellRxLevDic[cell] > rxLevDValueFading)
                    {
                        tpCellValidDic[index].Remove(cell);
                    }
                }
                if (tpCellValidDic[index].Count == 0)
                {
                    tpCellValidDic.Remove(index);
                }
            }
        }

        private void removeCellValidDic(List<TestPoint> testPointList, int i, List<int> removeTPList)
        {
            foreach (int index in removeTPList)
            {
                if (i != index)
                {
                    Dictionary<Cell, CellLast> cellValidDic = tpCellValidDic[index];
                    Dictionary<Cell, float> cellDic = tpCellDic[i];
                    FrequencyShortage freqShortage = null;
                    foreach (Cell cell in cellValidDic.Keys)    //对每个前提对象进行判断，有一个小区满足条件就满足
                    {
                        freqShortage = getFreqShortage(testPointList, i, cellDic, freqShortage, cell);
                    }
                    if (freqShortage != null)
                    {
                        tpCellValidDic.Remove(index);
                    }
                }
            }
        }

        private FrequencyShortage getFreqShortage(List<TestPoint> testPointList, int i, Dictionary<Cell, float> cellDic, FrequencyShortage freqShortage, Cell cell)
        {
            if (cellDic.ContainsKey(cell))    //小区信号中断，忽略
            {
                //同频段相差12db内小区的频点数占频段可用频点数百分比达到门限形成频率资源不足
                double freqCountRate = getFreqCountRate(cell, cellDic);
                if (100.0 * freqCountRate >= freqCountRateThreshold)
                {
                    if (freqShortage == null)
                    {
                        freqShortage = new FrequencyShortage(testPointList[i]);
                        MainModel.FrequencyShortageList.Add(freqShortage);
                    }
                    freqShortage.AddCell(cell.Name, freqCountRate);
                }
            }

            return freqShortage;
        }
    }
}
