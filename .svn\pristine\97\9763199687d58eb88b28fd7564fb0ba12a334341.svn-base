﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.AnaZT
{
    public partial class SetIdleScanLostDlg : Form
    {
        public SetIdleScanLostDlg()
        {
            InitializeComponent();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
        internal void GetSettingFilterRet(out int gridSize,out int sacnDBOffset)
        {
            gridSize = (int)numGridSize.Value;
            sacnDBOffset = (int)numScanDBOffset.Value;
        }
    }
}