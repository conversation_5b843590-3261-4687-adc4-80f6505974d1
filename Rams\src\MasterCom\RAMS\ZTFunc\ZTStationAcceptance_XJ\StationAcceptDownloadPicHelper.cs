﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public static class StationAcceptDownloadPicHelper
    {
        public static void DownloadPicFile(StationAcceptConfigHelper configHelper, string btsName, List<string> picNameList)
        {
            //1.获取当前测试站路径
            configHelper.ConfigInfo.BtsServerCoverPicPath = configHelper.ConfigInfo.ServerCoverPicPath + System.IO.Path.DirectorySeparatorChar + btsName;
            configHelper.ConfigInfo.BtsLocalCoverPicPath = configHelper.ConfigInfo.LocalCoverPicPath + System.IO.Path.DirectorySeparatorChar + btsName;
            if (!System.IO.Directory.Exists(configHelper.ConfigInfo.BtsLocalCoverPicPath))
            {
                System.IO.Directory.CreateDirectory(configHelper.ConfigInfo.BtsLocalCoverPicPath);
            }

            //2.获取服务端图片下载路径
            List<string> picServerPathList = initPicPath(picNameList, configHelper.ConfigInfo.BtsServerCoverPicPath);

            //3.下载图片到本地
            DownloadStationAcceptPic downloadQuery = new DownloadStationAcceptPic(picServerPathList, configHelper.ConfigInfo.BtsLocalCoverPicPath);
            downloadQuery.Query();
        }

        private static List<string> initPicPath(List<string> coverPicResult, string serverDirectoryName)
        {
            List<string> picPathList = new List<string>();
            foreach (var coverPicData in coverPicResult)
            {
                string realPath = serverDirectoryName + System.IO.Path.DirectorySeparatorChar + coverPicData;
                picPathList.Add(realPath);
            }

            return picPathList;
        }

        public static string GetValidPic(string picPath, string picName)
        {
            string pic = $"{picPath}/{picName}";
            if (!System.IO.File.Exists(pic))
            {
                return "";
            }
            return pic;
        }

        public static void Clear(string curBtsPicPath)
        {
            System.Threading.Thread.Sleep(100);
            if (System.IO.Directory.Exists(curBtsPicPath))
            {
                System.IO.Directory.Delete(curBtsPicPath, true);
            }
        }

    }
}
