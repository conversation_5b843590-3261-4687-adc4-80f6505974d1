using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTLowSpeedSettingDlg : BaseDialog
    {
        public ZTLowSpeedSettingDlg()
        {
            InitializeComponent();
        }

        private void simpleButtonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleButtonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        public void SetSettingFilterRet(ConditionLowSpeedGSM SpeedCondition)
        {
            if (SpeedCondition == null) return;
            fillCondition(SpeedCondition);
        }

        private void fillCondition(ConditionLowSpeedGSM SpeedCondition)
        {
            spinEditSpeed_Max.Value = SpeedCondition.SpeedShold.Max;
            spinEditDistance_Max.Value = SpeedCondition.Distance.Max;
            spinEditDistanceDiff_Max.Value = SpeedCondition.DistanceDiff.Max;
            spinEditCV_BEP_Min.Value = SpeedCondition.CV_BEP.Min;
            spinEditCV_BEP_Max.Value = SpeedCondition.CV_BEP.Max;
            spinEditMEAN_BEP_Min.Value = SpeedCondition.MEAN_BEP.Min;
            spinEditMEAN_BEP_Max.Value = SpeedCondition.MEAN_BEP.Max;
            spinEditRxlev_Min.Value = SpeedCondition.Rxlev.Min;
            spinEditRxlev_Max.Value = SpeedCondition.Rxlev.Max;
            spinEditC2I_Min.Value = SpeedCondition.C2I.Min;
            spinEditC2I_Max.Value = SpeedCondition.C2I.Max;
            spinEditMCS_Min.Value = SpeedCondition.MCS.Min;
            spinEditMCS_Max.Value = SpeedCondition.MCS.Max;
            spinEditTS_Min.Value = SpeedCondition.TS.Min;
            spinEditTS_Max.Value = SpeedCondition.TS.Max;
            spinEditBLER_Min.Value = SpeedCondition.BLER.Min;
            spinEditBLER_Max.Value = SpeedCondition.BLER.Max;

            chk_speed.Checked = SpeedCondition.SpeedShold.IsCheck;
            chk_distance.Checked = SpeedCondition.Distance.IsCheck;
            chk_distanceDiff.Checked = SpeedCondition.DistanceDiff.IsCheck;
            chk_cv_bep.Checked = SpeedCondition.CV_BEP.IsCheck;
            chk_mean_bep.Checked = SpeedCondition.MEAN_BEP.IsCheck;
            chk_rxlev.Checked = SpeedCondition.Rxlev.IsCheck;
            chk_c2i.Checked = SpeedCondition.C2I.IsCheck;
            chk_mcs.Checked = SpeedCondition.MCS.IsCheck;
            chk_ts.Checked = SpeedCondition.TS.IsCheck;
            chk_bler.Checked = SpeedCondition.BLER.IsCheck;
        }

        public void GetSettingFilterRet(out ConditionLowSpeedGSM SpeedCondition)
        {
            SpeedCondition = new ConditionLowSpeedGSM();
            SpeedCondition.SpeedShold.fill("APP_Speed", (int)spinEditSpeed_Min.Value, (int)spinEditSpeed_Max.Value, chk_speed.Checked);
            SpeedCondition.Distance.fill("Distance", (int)spinEditDistance_Min.Value, (int)spinEditDistance_Max.Value, chk_distance.Checked);
            SpeedCondition.DistanceDiff.fill("DistanceDiff", (int)spinEditDistanceDiff_Min.Value, (int)spinEditDistanceDiff_Max.Value, chk_distanceDiff.Checked);
            SpeedCondition.CV_BEP.fill("EGPRS_BEP_Variance", (int)spinEditCV_BEP_Min.Value, (int)spinEditCV_BEP_Max.Value, chk_cv_bep.Checked);
            SpeedCondition.MEAN_BEP.fill("EGPRS_BEP_Mean", (int)spinEditMEAN_BEP_Min.Value, (int)spinEditMEAN_BEP_Max.Value, chk_mean_bep.Checked);
            SpeedCondition.Rxlev.fill("RxLevSub", (int)spinEditRxlev_Min.Value, (int)spinEditRxlev_Max.Value, chk_rxlev.Checked);
            SpeedCondition.C2I.fill("C_I", (int)spinEditC2I_Min.Value, (int)spinEditC2I_Max.Value, chk_c2i.Checked);
            SpeedCondition.MCS.fill("EDGE_DL_MCS", (int)spinEditMCS_Min.Value, (int)spinEditMCS_Max.Value, chk_mean_bep.Checked);
            SpeedCondition.TS.fill("NumberOfTSUsed_DL", (int)spinEditTS_Min.Value, (int)spinEditTS_Max.Value, chk_ts.Checked);
            SpeedCondition.BLER.fill("GPRS_BLER_DL", (int)spinEditBLER_Min.Value, (int)spinEditBLER_Max.Value, chk_bler.Checked);
        }
    }

    public class ConditionLowSpeedGSM
    {
        public ParamGroup SpeedShold { get; set; }
        public ParamGroup Distance{ get; set; }
        public ParamGroup DistanceDiff{ get; set; }
        public ParamGroup CV_BEP{ get; set; }
        public ParamGroup MEAN_BEP{ get; set; }
        public ParamGroup Rxlev{ get; set; }
        public ParamGroup C2I{ get; set; }
        public ParamGroup MCS{ get; set; }
        public ParamGroup TS{ get; set; }
        public ParamGroup BLER{ get; set; }
        public Dictionary<string, ParamGroup> nParamDic{ get; set; }

        public ConditionLowSpeedGSM()
        {
            SpeedShold = new ParamGroup("APP_Speed", 0, 20, false);
            Distance = new ParamGroup("Distance", 0, 50, false);
            DistanceDiff = new ParamGroup("DistanceDiff", 0, 20, false);
            CV_BEP = new ParamGroup("EGPRS_BEP_Variance", 0, 64, false);
            MEAN_BEP = new ParamGroup("EGPRS_BEP_Mean", 0, 64, false);
            Rxlev = new ParamGroup("RxLevSub", -140, -10, false);
            C2I = new ParamGroup("C_I", -30, 30, false);
            MCS = new ParamGroup("EDGE_DL_MCS", 1, 13, false);
            TS = new ParamGroup("NumberOfTSUsed_DL", 1, 8, false);
            BLER = new ParamGroup("GPRS_BLER_DL", 0, 100, false);
            nParamDic = new Dictionary<string, ParamGroup>();
            nParamDic.Add("APP_Speed", SpeedShold);
            nParamDic.Add("Distance", Distance);
            nParamDic.Add("DistanceDiff", DistanceDiff);
            nParamDic.Add("EGPRS_BEP_Variance", CV_BEP);
            nParamDic.Add("EGPRS_BEP_Mean", MEAN_BEP);
            nParamDic.Add("RxLevSub", Rxlev);
            nParamDic.Add("C_I", C2I);
            nParamDic.Add("EDGE_DL_MCS", MCS);
            nParamDic.Add("NumberOfTSUsed_DL", TS);
            nParamDic.Add("GPRS_BLER_DL", BLER);
        }

        public bool checkLegal(string name, double val)
        {
            if (nParamDic.ContainsKey(name))
            {
                return nParamDic[name].checkLegal(val);
            }
            return false;
        }
    }

    public class ParamGroup
    {
        public string name{ get; set; }
        public int Min{ get; set; }
        public int Max{ get; set; }
        public bool IsCheck{ get; set; }

        public ParamGroup(string name)
        {
            this.name = name;
            Min = int.MinValue;
            Max = int.MaxValue;
            IsCheck = false;
        }

        public ParamGroup(string name, int minValue, int maxValue,bool isCheck)
        {
            this.name = name;
            this.Min = minValue;
            this.Max = maxValue;
            this.IsCheck = isCheck;
        }

        public void fill(string name, int minValue, int maxValue, bool isCheck)
        {
            this.name = name;
            this.Min = minValue;
            this.Max = maxValue;
            this.IsCheck = isCheck;
        }

        public bool checkLegal(double val)
        {
            return val >= Min && val <= Max;
        }
    }
}