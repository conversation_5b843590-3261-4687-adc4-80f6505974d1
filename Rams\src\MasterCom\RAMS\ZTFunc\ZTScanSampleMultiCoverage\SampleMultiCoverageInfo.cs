using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class SampleMultiCoverageInfo
    {
        public SampleMultiCoverageInfo(int level, TestPoint testPoint, int coverLevel, List<BBCell> cellList, float maxRxlev, int sn)
        {
            this.level = level;
            this.testPoint = testPoint;
            this.cellList = cellList;
            this.coverLevel = coverLevel;
            this.maxRxlev = maxRxlev;
            this.sn = sn;
        }
        public int level { get; set; }
        public List<BBCell> cellList { get; set; }
        public TestPoint testPoint { get; set; }
        public int coverLevel { get; set; }
        public float maxRxlev { get; set; }
        public int sn { get; set; }
    }

    public class BBCell
    {
        public Cell cell { get; set; }
        public int bcch { get; set; }
        public byte bsic { get; set; }
        public TestPoint testPoint { get; set; }
        public int sn { get; set; }
        public float rxlev { get; set; }

        public BBCell(Cell cell, int bcch, byte bsic, TestPoint testPoint, int sn, float rxlev)
        {
            this.cell = cell;
            this.bcch = bcch;
            this.bsic = bsic;
            this.testPoint = testPoint;
            this.sn = sn;
            this.rxlev = rxlev;
        }

        public string CellName
        {
            get
            {
                if (cell != null)
                {
                    return cell.Name;
                }
                else
                {
                    return bcch + "_" + bsic;
                }
            }
        }

        public double Distance
        {
            get
            {
                double distance = 0;
                if (cell != null)
                {
                    distance = MathFuncs.GetDistance(testPoint.Longitude, testPoint.Latitude, cell.Longitude, cell.Latitude);
                }
                return distance;
            }
        }
    }
}