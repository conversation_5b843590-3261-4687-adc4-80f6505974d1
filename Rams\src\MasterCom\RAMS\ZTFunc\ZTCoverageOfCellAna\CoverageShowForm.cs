﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CoverageShowForm : MinCloseForm
    {
        private showSetting setting;
        public showSetting Setting
        {
            get { return setting; }
        }

        public bool isMultiCells { get; set; }
        public CoverageShowForm(MainModel mModel)
            : base(mModel)
        {
            InitializeComponent();

            setting = new showSetting();
        }

        internal void FillData()
        {
            try
            {
                listViewMultiCellColor_Tp.Items.Clear();
                listViewMultiCellColor_Fl.Items.Clear();

                if (isMultiCells)
                {
                    this.Size = new Size(455, 384);
                    xtraTabControl1.Size = new Size(434, 311);
                    btnOK.Location = new Point(99, 314);
                    btnCancel.Location = new Point(242, 314);
                    xtraTabPage2.PageVisible = true;
                    btnExportShapefile.Visible = false;

                    foreach (CoverageCellInfo cellInfo in MainModel.CoverageOfCellInfos)
                    {
                        fillCellListView(listViewMultiCellColor_Tp, cellInfo, Color.Green);
                        fillCellListView(listViewMultiCellColor_Fl, cellInfo, Color.Transparent);
                    }
                }
                else
                {
                    this.Size = new Size(239, 138);
                    xtraTabControl1.Size = new Size(212, 59);
                    btnOK.Location = new Point(12, 66);
                    btnCancel.Location = new Point(114, 66);
                    xtraTabPage2.PageVisible = false;
                    btnExportShapefile.Visible = true;
                }
            }
            catch
            {
                //continue
            }
        }

        public void clickOK()
        {
            this.btnOK_Click(null, null);
        }

        private void fillCellListView(ListView listview, CoverageCellInfo cellInfo,Color defaultColor)
        {
            if (cellInfo.cell != null)
            {
                ListViewItem lvi = null;

                if (cellInfo.cellType == 1)
                {
                    Cell cell = (Cell)cellInfo.cell;
                    lvi = new ListViewItem(cell.Name + ";" + cell.LAC + "_" + cell.CI);
                }
                else if (cellInfo.cellType == 2)
                {
                    TDCell cell = (TDCell)cellInfo.cell;
                    lvi = new ListViewItem(cell.Name + ";" + cell.LAC + "_" + cell.CI);
                }
                else if (cellInfo.cellType == 3)
                {
                    LTECell cell = (LTECell)cellInfo.cell;
                    lvi = new ListViewItem(cell.Name + ";" + cell.TAC + "_" + cell.ECI);
                }
                else if (cellInfo.cellType == 4)
                {
                    WCell cell = (WCell)cellInfo.cell;
                    lvi = new ListViewItem(cell.Name + ";" + cell.LAC + "_" + cell.CI);
                }

                if (lvi != null)
                {
                    lvi.UseItemStyleForSubItems = false;
                    lvi.Tag = cellInfo;
                    ListViewItem.ListViewSubItem subitem = new ListViewItem.ListViewSubItem();
                    subitem.BackColor = defaultColor;
                    lvi.SubItems.Add(subitem);
                    listview.Items.Add(lvi);
                }
            }

        }

        private void radioBtnTestpoint_CheckedChanged(object sender, EventArgs e)
        {
            chooseTpDisplayKind();
        }

        private void radioBtnCell_CheckedChanged(object sender, EventArgs e)
        {
            chooseTpDisplayKind();
        }

        private void chooseTpDisplayKind()
        {
            //
        }

        private void listViewMutilCellColor_Tp_DoubleClick(object sender, EventArgs e)
        {
            if (listViewMultiCellColor_Tp.SelectedItems.Count == 1)
            {
                ColorDialog colorDialog = new ColorDialog();
                colorDialog.Color = listViewMultiCellColor_Tp.SelectedItems[0].SubItems[1].BackColor;
                if (colorDialog.ShowDialog() == DialogResult.OK)
                {
                    listViewMultiCellColor_Tp.SelectedItems[0].SubItems[1].BackColor = colorDialog.Color;
                }
            }
        }


        private void listViewMutilCellColor_Fl_DoubleClick(object sender, EventArgs e)
        {
            if (listViewMultiCellColor_Fl.SelectedItems.Count == 1)
            {
                ColorDialog colorDialog = new ColorDialog();
                colorDialog.Color = listViewMultiCellColor_Fl.SelectedItems[0].SubItems[1].BackColor;
                if (colorDialog.ShowDialog() == DialogResult.OK)
                {
                    listViewMultiCellColor_Fl.SelectedItems[0].SubItems[1].BackColor = colorDialog.Color;
                }
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            setting = new showSetting();

            setting.isMarkNum = checkEditMarkNum.Checked;

            if (radioBtnTestpoint.Checked) //采样点显示模式
                setting.showKind = showSetting.TpKind.byParam;
            else
                setting.showKind = showSetting.TpKind.byCell;

            if (!isMultiCells)
            {
                setting.tpCellMode = showSetting.CellMode.bySingle;
                setting.lineCellMode = showSetting.CellMode.bySingle;

                if (MainModel.CoverageOfCellInfos.Count == 1)
                {
                    setting.tpSingleCell_maincell = MainModel.CoverageOfCellInfos[0];
                }
            }
            else
            {
                setting.tpCellMode = showSetting.CellMode.byMulti;
                setting.lineCellMode = showSetting.CellMode.byMulti;

                setting.tpMultiCellColorDic = new Dictionary<CoverageCellInfo, Color>();
                setting.lineMultiCellColorDic = new Dictionary<CoverageCellInfo, Color>();

                foreach (ListViewItem item in listViewMultiCellColor_Tp.Items)
                {
                    setting.tpMultiCellColorDic.Add((CoverageCellInfo)item.Tag, item.SubItems[1].BackColor);
                }

                foreach (ListViewItem item in listViewMultiCellColor_Fl.Items)
                {
                    setting.lineMultiCellColorDic.Add((CoverageCellInfo)item.Tag, item.SubItems[1].BackColor);
                }

            }

            MainModel.CovCellShowSetting = setting;

            MainModel.FireDTDataChanged(this);
            if (MainModel.SelectedCell!=null)
            {
                MainModel.GetInstance().MainForm.GetMapForm().GoToView(MainModel.SelectedCell.Longitude, MainModel.SelectedCell.Latitude, 15000);
            }
            if (MainModel.SelectedTDCell!=null)
            {
                MainModel.GetInstance().MainForm.GetMapForm().GoToView(MainModel.SelectedTDCell.Longitude, MainModel.SelectedTDCell.Latitude, 15000);
            }
            if (MainModel.SelectedLTECell != null)
            {
                MainModel.GetInstance().MainForm.GetMapForm().GoToView(MainModel.SelectedLTECell.Longitude, MainModel.SelectedLTECell.Latitude, 15000);
            }
            if (MainModel.SelectedWCell != null)
            {
                MainModel.GetInstance().MainForm.GetMapForm().GoToView(MainModel.SelectedWCell.Longitude, MainModel.SelectedWCell.Latitude, 15000);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            MainModel.CovCellShowSetting = null;
            //MapForm mapform = MainModel.MainForm.GetMapForm();
            //if (mapform != null)
            //{
            //    MapDTLayer dtLayer = mapform.GetDTLayer();
            //}
            this.Close();
        }

        protected override void MinCloseForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                e.Cancel = true;
                if (mModel.QuickWindowItemDic.ContainsKey(this.GetType().FullName))
                    mModel.QuickWindowItemDic.Remove(this.GetType().FullName);
                this.Visible = false;
                MainModel.CovCellShowSetting = null;
            }
        }

        private void miShowCell_Click(object sender, EventArgs e)
        {
            if (listViewMultiCellColor_Tp.Focus())
            {
                gotoView(listViewMultiCellColor_Tp, 5000);
            }
            else if (listViewMultiCellColor_Fl.Focus())
            {
                gotoView(listViewMultiCellColor_Fl, 10000);
            }
        }

        private void gotoView(ListView view, float zoom)
        {
            if (view.SelectedItems[0].Tag != null)
            {
                MapForm mapform = MainModel.MainForm.GetMapForm();
                if (mapform != null)
                {
                    CoverageCellInfo cellInfo = view.SelectedItems[0].Tag as CoverageCellInfo;
                    mapform.GoToView(cellInfo.cell.Longitude, cellInfo.cell.Latitude, zoom);
                }
            }
        }

        private void miEditColor_Click(object sender, EventArgs e)
        {
            if (listViewMultiCellColor_Tp.Focus())
            {
                this.listViewMutilCellColor_Tp_DoubleClick(null, null);
            }
            else if (listViewMultiCellColor_Fl.Focus())
            {
                this.listViewMutilCellColor_Fl_DoubleClick(null, null);
            }
        }

        private void btnExportShapefile_Click(object sender, EventArgs e)
        {
            SaveFileDialog saveFileDlg = new SaveFileDialog();
            saveFileDlg.Filter = FilterHelper.Shp;
            saveFileDlg.FilterIndex = 1;
            saveFileDlg.RestoreDirectory = true;
            saveFileDlg.Title = "另存为";
            if (saveFileDlg.ShowDialog() == DialogResult.OK)
            {
                string fileName = saveFileDlg.FileName;
                MapDTLayer mapFormDTLayer = mModel.MainForm.GetMapForm().GetDTLayer();
                int expRet = mapFormDTLayer.MakeSingleCovCellShpFile(fileName);//导出街道渗透信息
                if (expRet == 0)
                {
                    MessageBox.Show(this, "所选导出图层没有需要导出的数据！");
                }
                else if (expRet == 1)
                {
                    MessageBox.Show(this, "导出成功！");
                    this.DialogResult = DialogResult.OK;
                }
                else if (expRet == 2)
                {
                    //取消导出
                }
                else
                {
                    MessageBox.Show(this, "导出图层发生错误！");
                }
            }
        }
    }   
    
    public class showSetting
    {
        public bool isMarkNum { get; set; }

        public enum CellMode { bySingle, byDouble, byMulti };

 #region 采样点设置    
        public enum TpKind { byParam, byCell };
        /// <summary>
        /// 采样点着色方式
        /// </summary>
        public TpKind showKind { get; set; } = TpKind.byCell; 

        /// <summary>
        /// 采样点设置下的小区模式
        /// </summary>
        public CellMode tpCellMode { get; set; }

        public List<TestPoint> nbTplist { get; set; } = new List<TestPoint>();
        /// <summary>
        /// 采样点设置下的单小区主区
        /// </summary>
        public CoverageCellInfo tpSingleCell_maincell{ get; set; }
        /// <summary>
        /// 采样点设置下的单小区主区颜色
        /// </summary>
        public Color tpSingleCell_maincellColor{ get; set; }
        /// <summary>
        /// 采样点设置下的单小区邻区颜色
        /// </summary>
        public Color tpSingleCell_nbcellColor{ get; set; }

        /// <summary>
        /// 采样点设置下的双小区中的小区A
        /// </summary>
        public CoverageCellInfo tpDoubleCell_cellA{ get; set; }
        /// <summary>
        ///  采样点设置下的双小区中的小区B
        /// </summary>
        public CoverageCellInfo tpDoubleCell_cellB{ get; set; }
        /// <summary>
        ///  采样点设置下的双小区中的小区A颜色
        /// </summary>
        public Color tpDoubleCell_cellAColor{ get; set; }
        /// <summary>
        ///  采样点设置下的双小区中的小区B颜色
        /// </summary>
        public Color tpDoubleCell_cellBColor{ get; set; }
        /// <summary>
        ///  采样点设置下的双小区中，小区A与小区B交叉覆盖颜色
        /// </summary>
        public Color tpDoubleCell_cellBothColor{ get; set; }

        /// <summary>
        /// 采样点设置下的多小区中，小区与颜色字典
        /// </summary>
        public Dictionary<CoverageCellInfo, Color> tpMultiCellColorDic { get; set; } = new Dictionary<CoverageCellInfo, Color>();
        #endregion

 #region 飞线设置
        /// <summary>
        /// 飞线设置下的小区模式
        /// </summary>
        public CellMode lineCellMode{ get; set; }

        /// <summary>
        /// 飞线设置下的单小区主区
        /// </summary>
        public CoverageCellInfo lineSingleCell_maincell{ get; set; }
        /// <summary>
        /// 飞线设置下的单小区主区颜色
        /// </summary>
        public Color lineSingleCell_maincellColor{ get; set; }
        /// <summary>
        /// 飞线设置下的单小区邻区颜色
        /// </summary>
        public Color lineSingleCell_nbcellColor{ get; set; }

        /// <summary>
        /// 飞线设置下的双小区中的小区A
        /// </summary>
        public CoverageCellInfo lineDoubleCell_cellA{ get; set; }
        /// <summary>
        ///  飞线设置下的双小区中的小区B
        /// </summary>
        public CoverageCellInfo lineDoubleCell_cellB{ get; set; }
        /// <summary>
        ///  飞线设置下的双小区中的小区A颜色
        /// </summary>
        public Color lineDoubleCell_cellAColor{ get; set; }
        /// <summary>
        ///  飞线设置下的双小区中的小区B颜色
        /// </summary>
        public Color lineDoubleCell_cellBColor{ get; set; }
        /// <summary>
        ///  飞线设置下的双小区中，小区A与小区B交叉覆盖颜色
        /// </summary>
        public Color lineDoubleCell_cellBothColor{ get; set; }

        /// <summary>
        /// 飞线设置下的多小区中，小区与颜色字典
        /// </summary>
        public Dictionary<CoverageCellInfo, Color> lineMultiCellColorDic { get; set; } = new Dictionary<CoverageCellInfo, Color>();
        #endregion
    }

}
