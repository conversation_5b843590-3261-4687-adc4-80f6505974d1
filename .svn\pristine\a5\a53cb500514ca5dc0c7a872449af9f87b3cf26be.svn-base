﻿using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.Model.BaseInfo
{
    public class LoginManagerDBSelect : DIYSQLBase
    {
        public LoginManagerDBSelect(int dbId)
            : base(MainModel.GetInstance(), dbId)
        {
            MainDB = true;
        }
        public LoginManagerDBSelect()
            : base(MainModel.GetInstance())
        {
            MainDB = true;
        }

        /// <summary>
        /// 接受返回值类型
        /// </summary>
        /// <returns></returns>
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[13];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_Int;
            rType[4] = E_VType.E_Int;
            rType[5] = E_VType.E_Int;
            rType[6] = E_VType.E_Int;
            rType[7] = E_VType.E_Int;
            rType[8] = E_VType.E_Int;
            rType[9] = E_VType.E_Int;
            rType[10] = E_VType.E_Int;
            rType[11] = E_VType.E_Int;
            rType[12] = E_VType.E_Int;
            return rType;
        }

        /// <summary>
        /// 合成相应的数据库操作语句
        /// </summary>
        /// <returns></returns>
        protected override string getSqlTextString()
        {
            string str = "select staleDays,tryTimes,alarmDays,lockSeconds,exitMins,resetHour,lockUnUseDays,delUnLoadDays, pwdMinCharCount,pwdMinCharKindCount,pwdNotSameRecentTimes,pwdSuperLimit,noOpsType from tb_cfg_static_user_constraint";
            return str;
        }

        /// <summary>
        /// 接收服务端返回数据
        /// </summary>
        /// <param name="clientProxy"></param>
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    LoginValues loginValues = mainModel.LoginManageCfg;
                    loginValues.StaleDays = package.Content.GetParamInt();
                    loginValues.TryTimes = package.Content.GetParamInt();
                    loginValues.AlarmDays = package.Content.GetParamInt();
                    loginValues.LockSeconds = package.Content.GetParamInt();
                    loginValues.ExitMins = package.Content.GetParamInt();
                    loginValues.HourOfResetTryTimes = package.Content.GetParamInt();
                    loginValues.LockUnUseDays = package.Content.GetParamInt();
                    loginValues.DelUnLoadDays = package.Content.GetParamInt();
                    loginValues.PwdMinCharCount = package.Content.GetParamInt();
                    loginValues.PwdMinCharKindCount = package.Content.GetParamInt();
                    loginValues.PwdNotSameRecentTimes = package.Content.GetParamInt();
                    loginValues.PwdSuperLimit = package.Content.GetParamInt() == 1;
                    int noOperationType = package.Content.GetParamInt();
                    if (noOperationType == 0)
                    {
                        loginValues.NoOperationType = LoginValues.NoOpsType.LogOut;
                    }
                    else
                    {
                        loginValues.NoOperationType = LoginValues.NoOpsType.Lock;
                    }
                    break;
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class LoginManagerDBUpdate : DIYSQLBase
    {
        private readonly LoginValues loginValues;
        private int res = -1;

        /// <summary>
        /// 执行更新数据库后的结果 1:成功,其他:失败
        /// </summary>
        public int Res
        {
            get { return res; }
        }

        public LoginManagerDBUpdate(LoginValues funcValues)
            : base(MainModel.GetInstance())
        {
            this.loginValues = funcValues;
            MainDB = true;
        }

        /// <summary>
        /// 接受返回值类型
        /// </summary>
        /// <returns></returns>
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_Int;
            return rType;
        }

        /// <summary>
        /// 合成相应的数据库操作语句
        /// </summary>
        /// <returns></returns>
        protected override string getSqlTextString()
        {
            string str = string.Format(@"delete from tb_cfg_static_user_constraint;
insert into tb_cfg_static_user_constraint(staleDays,tryTimes,alarmDays,lockSeconds,exitMins,resetHour,lockUnUseDays
,delUnLoadDays, pwdMinCharCount, pwdMinCharKindCount, pwdNotSameRecentTimes, pwdSuperLimit, noOpsType)
values({0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12});"
                , loginValues.StaleDays, loginValues.TryTimes, loginValues.AlarmDays, loginValues.LockSeconds
                , loginValues.ExitMins, loginValues.HourOfResetTryTimes, loginValues.LockUnUseDays
                , loginValues.DelUnLoadDays, loginValues.PwdMinCharCount, loginValues.PwdMinCharKindCount
                , loginValues.PwdNotSameRecentTimes, getPwdSuperLimit(), getNoOperationType());

            return str;
        }

        private int getPwdSuperLimit()
        {
            if (loginValues.PwdSuperLimit)
            {
                return 1;
            }
            return 0;
        }

        private int getNoOperationType()
        {
            if (loginValues.NoOperationType == LoginValues.NoOpsType.LogOut)
            {
                return 0;
            }
            return 1;
        }

        /// <summary>
        /// 接收服务端返回数据
        /// </summary>
        /// <param name="clientProxy"></param>
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();

                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    res = 1;
                    break;
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    res = 0;
                    break;
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

}
