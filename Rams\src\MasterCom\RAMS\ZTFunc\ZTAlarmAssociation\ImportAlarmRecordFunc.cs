﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

using System.Data;
using System.Data.SqlClient;
using System.Data.OleDb;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.Util.UiEx;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ImportAlarmRecordFunc : QueryBase
    {
        public ImportAlarmRecordFunc(MainModel mainModel) : base(mainModel)
        {
        }

        public override bool IsNeedSetQueryCondition
        {
            get { return false; }
        }

        public override string IconName
        {
            get { return string.Empty; }
        }

        public override string Name
        {
            get { return "导入告警记录"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 25000, 25002, this.Name);
        }

        protected override bool isValidCondition()
        {
            ImportAlarmSettingForm form = new ImportAlarmSettingForm();
            if (form.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            this.cond = form.GetCondition();
            form.Dispose();
            return true;
        }

        protected override void query()
        {
            this.error = null;
            AlarmRecordImporter importer = new AlarmRecordImporter();
            WaitTextBox.Show("正在导入告警记录...", ImportInThread, importer);

            if (this.error != null)
            {
                MessageBox.Show(error.Message + Environment.NewLine + error.StackTrace, "导入错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            string title = string.Format("成功导入{0}记录，", importer.ImportedCount);
            if (importer.ErrorRows.Count == 0)
            {
                title += "未发生错误。";
            }
            else
            {
                title += string.Format("数据文件存在以下{0}行错误:", importer.ErrorRows.Count);
            }
            ImportAlarmResultForm resultForm = new ImportAlarmResultForm(title, importer.ErrorRows);
            resultForm.ShowDialog();
            resultForm.Dispose();
            this.error = null;
        }

        private void ImportInThread(object o)
        {
            AlarmRecordImporter importer = o as AlarmRecordImporter;
            try
            {
                importer.Import(AlarmRecordManager.Instance.SqlConnectionString, this.cond);
            }
            catch (System.Exception ex)
            {
                this.error = ex;
            }
            finally
            {
                System.Threading.Thread.Sleep(300);
                WaitTextBox.Close();
            }
        }

        private ImportAlarmCondition cond;
        private Exception error;
    }

    public class ImportAlarmCondition
    {
        public int CityID { get; set; }

        public bool IsClear { get; set; }

        public string FilePath { get; set; }

        public int NetType { get; set; } = 3;
    }

    public class AlarmRecordImporter
    {
        public AlarmRecordImporter()
        {
            ErrorRows = new List<string>();
        }

        public List<string> ErrorRows
        {
            get;
            private set;
        }

        public int ImportedCount
        {
            get;
            private set;
        }

        public void Import(string sqlConnStr, ImportAlarmCondition cond)
        {
            ErrorRows.Clear();

            List<DataTable> tables = ExcelOleDbReader.ReadTables(cond.FilePath);
            DataTable excelTable = tables[0];
            DataTable importTable = TransferTable(excelTable, cond.CityID, cond.NetType);

            using (SqlConnection sqlConn = new SqlConnection(sqlConnStr))
            {
                sqlConn.Open();
                if (cond.IsClear)
                {
                    string sql = string.Format("delete from {0} where [地市ID] = {1} and [网络类型] = {2}", tableName, cond.CityID, cond.NetType);
                    using (SqlCommand cmd = new SqlCommand(sql, sqlConn))
                    {
                        cmd.ExecuteNonQuery();
                    }
                }
                SqlBulkCopy bcp = new SqlBulkCopy(sqlConn);
                bcp.DestinationTableName = importTable.TableName;
                bcp.WriteToServer(importTable);
                bcp.Close();

                ImportedCount = importTable.Rows.Count;
            }
        }

        private DataTable CreateImportTable()
        {
            DataTable importTable = new DataTable(tableName);
            importTable.Columns.Add("地市ID", typeof(int));
            importTable.Columns.Add("发生时间", typeof(DateTime));
            importTable.Columns.Add("结束时间", typeof(DateTime));
            importTable.Columns.Add("网络类型", typeof(int));
            importTable.Columns.Add("基站名称", typeof(string));
            importTable.Columns.Add("小区名称", typeof(string));
            importTable.Columns.Add("告警ID", typeof(int));
            importTable.Columns.Add("告警名称", typeof(string));
            importTable.Columns.Add("告警级别", typeof(string));
            importTable.Columns.Add("网元类型", typeof(string));
            importTable.Columns.Add("告警源", typeof(string));
            importTable.Columns.Add("MO对象", typeof(string));
            importTable.Columns.Add("定位信息", typeof(string));
            importTable.Columns.Add("确认时间", typeof(DateTime));
            importTable.Columns.Add("确认用户", typeof(string));
            importTable.Columns.Add("所属子网", typeof(string));
            return importTable;
        }

        private void ReportErrorRow(int rowIndex, string column, string errMsg)
        {
            if (errMsg == null)
            {
                errMsg = "Invalid";
            }
            ErrorRows.Add(string.Format("Row {0}: [{1}] {2}", rowIndex + 2, column, errMsg));
        }

        private string GetCellName(string str)
        {
            int cellIndex = str.IndexOf("小区名称=", 0);
            if (cellIndex == -1)
            {
                return null;
            }

            string cellString = str.Substring(cellIndex);
            int startIndex = cellString.IndexOf("=");
            int endIndex = cellString.IndexOf(",");
            if (startIndex == -1 || endIndex == -1)
            {
                return null;
            }
            return cellString.Substring(startIndex + 1, endIndex - startIndex - 1);
        }

        private DataTable TransferTable(DataTable excelTable, int cityID, int netTypeID)
        {
            DataTable importTable = CreateImportTable();
            for (int i = 0; i < excelTable.Rows.Count; ++i)
            {
                DataRow excelRow = excelTable.Rows[i];
                DataRow importRow = importTable.NewRow();

                #region read row value
                importRow["地市ID"] = cityID;
                importRow["网络类型"] = netTypeID;

                DateTime sTime;
                if (!DataRowReader.GetDateTime(excelRow, "发生时间(NT)", out sTime))
                {
                    ReportErrorRow(i, "发生时间(NT)", null);
                    continue;
                }
                importRow["发生时间"] = sTime;

                DateTime eTime;
                if (!DataRowReader.GetDateTime(excelRow, "清除时间(NT)", out eTime))
                {
                    ReportErrorRow(i, "清除时间(NT)", null);
                    continue;
                }
                importRow["结束时间"] = eTime;

                string netType = null;
                if (!DataRowReader.GetString(excelRow, "网元类型", out netType))
                {
                    ReportErrorRow(i, "网元类型", null);
                    continue;
                }
                importRow["网元类型"] = netType;

                string moObject = null;
                if (!DataRowReader.GetString(excelRow, "MO对象", out moObject))
                {
                    ReportErrorRow(i, "MO对象", null);
                    continue;
                }
                importRow["MO对象"] = moObject;

                string alarmSource = setRowValue(i, excelRow, importRow);
                if (netType == "OSS")
                {
                    importRow["基站名称"] = moObject;
                }
                else
                {
                    importRow["基站名称"] = alarmSource;
                    string cellName = GetCellName(moObject);
                    if (cellName == null)
                    {
                        ReportErrorRow(i, "MO对象", "提取小区名称失败");
                        continue;
                    }
                    importRow["小区名称"] = cellName;
                }
                #endregion

                importTable.Rows.Add(importRow);
            }

            return importTable;
        }

        private string setRowValue(int i, DataRow excelRow, DataRow importRow)
        {
            int alarmId;
            if (!DataRowReader.GetInt(excelRow, "告警ID", out alarmId))
            {
                ReportErrorRow(i, "告警ID", null);
            }
            importRow["告警ID"] = alarmId;

            string alarmName = null;
            if (DataRowReader.GetString(excelRow, "名称", out alarmName))
            {
                importRow["告警名称"] = alarmName;
            }

            string alarmLevel = null;
            if (DataRowReader.GetString(excelRow, "级别", out alarmLevel))
            {
                importRow["告警级别"] = alarmLevel;
            }

            string alarmSource = null;
            if (!DataRowReader.GetString(excelRow, "告警源", out alarmSource))
            {
                ReportErrorRow(i, "告警源", null);
            }
            importRow["告警源"] = alarmSource;

            string locationMsg = null;
            if (DataRowReader.GetString(excelRow, "定位信息", out locationMsg))
            {
                importRow["定位信息"] = locationMsg;
            }

            DateTime confirmTime;
            if (DataRowReader.GetDateTime(excelRow, "确认时间(ST)", out confirmTime))
            {
                importRow["确认时间"] = confirmTime;
            }

            string user;
            if (DataRowReader.GetString(excelRow, "确认用户", out user))
            {
                importRow["确认用户"] = user;
            }

            string belongNet;
            if (DataRowReader.GetString(excelRow, "所属子网", out belongNet))
            {
                importRow["所属子网"] = belongNet;
            }

            return alarmSource;
        }

        private readonly string tableName = "tb_association_alarm";
    }
}
