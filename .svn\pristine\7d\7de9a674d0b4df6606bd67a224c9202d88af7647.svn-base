﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using DBDataViewer;

namespace MasterCom.RAMS.Net
{
    public class DIYStatByLog : DIYStatByAllRegion
    {
        public DIYStatByLog(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }
        public override string Name
        {
            get { return "按所选文件统计"; }
        }
        public override string IconName
        {
            get { return "Images/stat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11025, this.Name);
        }
        protected override StatTbToken getBranchToken()
        {
            return StatTbToken.log;
        }
        protected override void query()
        {
            loadReportFromFile();
            SelectReportDlg dlg = new SelectReportDlg();
            dlg.FillCurrentReports(ref rptStyleList);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            bool curSelStyleContentAllTmp;
            curSelStyle = dlg.GetSelectedReport(out curSelStyleContentAllTmp);
            curSelStyleContentAll = curSelStyleContentAllTmp;
            curEventStatFilter = dlg.GetEventStatFilter();
            //=
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = null;

            MainModel.CurChinaMobileStatReportData = null;
            MainModel.CurChinaUnicomStatReportData = null;
            MainModel.CurChinaTelecomStatReportData = null;

            MainModel.CurChinaMobileStatReportDataMo = null;
            MainModel.CurChinaUnicomStatReportDataMo = null;
            MainModel.CurChinaTelecomStatReportDataMo = null;

            MainModel.CurChinaMobileStatReportDataMt = null;
            MainModel.CurChinaUnicomStatReportDataMt = null;
            MainModel.CurChinaTelecomStatReportDataMt = null;

            foreach (int DistrictID in condition.DistrictIDs)
            {
                clientProxy = new ClientProxy();

                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    continue;
                }
                try
                {
                    WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
                }
                finally
                {
                    clientProxy.Close();
                }
            }
            MainModel.FireStatQueried(this, curSelStyle, "");

        }
        
#region MTR Special
        protected void MTRSpecial_prepareStatPackage_Message_FileFilter(Package package, FileInfo fileInfo)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_DIY_LOG_MSG;
            package.Content.PrepareAddParam();
            package.Content.AddParam(fileInfo.ID);
            package.Content.AddParam(fileInfo.LogTable);
        }
        protected void MTRSpecial_prepareStatPackage_Message_MessageFilter(Package package)
        {
            AddDIYEndOpFlag(package);//4.sample查询模式定义
        }
        protected void MTRSpecial_fillContentNeeded_Message(Package package, bool needL3Hex)
        {
            StringBuilder sbuilder = new StringBuilder();
            sbuilder.Append("0,1,45,");
            sbuilder.Append("0,2,45,");
            sbuilder.Append("0,3,45,");
            sbuilder.Append("0,4,45,");
            sbuilder.Append("0,5,45,");
            sbuilder.Append("0,6,45,");
            sbuilder.Append("0,7,45,");
            if (needL3Hex)
            {
                sbuilder.Append("1,8,45,");
            }
            sbuilder.Append("2,9,45");
            package.Content.AddParam(sbuilder.ToString());
        }
        protected void MTRSpecial_recieveInfo_Message(ClientProxy clientProxy, Dictionary<int, int> msgCountDic)
        {
            DTDataHeaderManager headerManager = DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            List<ColumnDefItem> curSampleColumnDef = new List<ColumnDefItem>();
            Package package = clientProxy.Package;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    DTDataHeader header = recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
                    headerManager.AddDTDataHeader(header);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_COLUMN_L0G_MSG)
                {
                    curSampleColumnDef.Clear();
                    string idpairs = package.Content.GetParamString();
                    parseToCurColumnDef(idpairs, curSampleColumnDef);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_MSG)
                {
                    MasterCom.RAMS.Model.Message msg = new MasterCom.RAMS.Model.Message();
                    fillMsgColumn(curSampleColumnDef, package, msg);
                    int count = 0;
                    if (msgCountDic.TryGetValue(msg.ID, out count))
                    {
                        msgCountDic[msg.ID] = count + 1;
                    }
                    else
                    {
                        msgCountDic[msg.ID] = 1;
                    }
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                #endregion
            }
        }

        private static void fillMsgColumn(List<ColumnDefItem> curSampleColumnDef, Package package, Model.Message msg)
        {
            foreach (ColumnDefItem cdf in curSampleColumnDef)
            {
                if (cdf.showName == "SeqID")
                {
                    msg.SN = package.Content.GetParamInt();
                }
                else if (cdf.showName == "Time")
                {
                    msg.Time = package.Content.GetParamInt();
                }
                else if (cdf.showName == "TimeMS")
                {
                    msg.Millisecond = package.Content.GetParamShort();
                }
                else if (cdf.showName == "FileID")
                {
                    msg.FileID = package.Content.GetParamInt();
                }
                else if (cdf.showName == "bms")
                {
                    msg.MS = package.Content.GetParamByte();
                }
                else if (cdf.showName == "MsgID")
                {
                    msg.ID = package.Content.GetParamInt();
                }
                else if (cdf.showName == "Direction")
                {
                    msg.Direction = package.Content.GetParamByte();
                }
                else if (cdf.showName == "HexCode")
                {
                    ((MasterCom.RAMS.Model.MessageWithSource)msg).Source = package.Content.GetParamBytes();
                }
                else if (cdf.showName == "Block1Code")
                {
                    msg.HandsetTime = DIYReplayFileQuery.ConvertToHandsetTime(package.Content.GetParamBytes());
                }
            }
        }

        #endregion
        private void queryInThread(object o)
        {
            ClientProxy clientProxy = (ClientProxy)o;
            Package package = clientProxy.Package;
            int idx = 1;
            WaitBox.CanCancel = true;
            WaitBox.Text = "开始查询...";
            int countTotal = Condition.FileInfos.Count;
            foreach (FileInfo finfo in Condition.FileInfos)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                if (finfo.DistrictID != clientProxy.DbID)
                {
                    continue;
                }
                List<DataUnitAreaKPIQuery> paraPeriodList = new List<DataUnitAreaKPIQuery>();
                WaitBox.Text = "统计("+idx++ +"/"+countTotal+") " + finfo.Name + "数据...";
                prepareSearchDIYLogPackage(package, finfo);
                clientProxy.Send();
                recieveInfo_ImgGrid(clientProxy, paraPeriodList, finfo.CarrierType, null);
                //event
                prepareSearchDIYLogEventPackage(package,finfo);
                AddDIYEndOpFlag(package);
                fillContentNeeded_Event(package);
                clientProxy.Send();
                recieveInfo_Event(clientProxy, paraPeriodList, finfo.CarrierType, null);
                //MTR 消息统计begin>>
                Dictionary<int, int> msgCountDic = null;
                if(finfo.ServiceType == 24)
                {
                    MTRSpecial_prepareStatPackage_Message_FileFilter(package, finfo);
                    MTRSpecial_prepareStatPackage_Message_MessageFilter(package);
                    MTRSpecial_fillContentNeeded_Message(package, false);
                    clientProxy.Send();
                    msgCountDic = new Dictionary<int, int>();
                    MTRSpecial_recieveInfo_Message(clientProxy, msgCountDic);
                }
                //================MTR msg end<<
                DataUnitAreaKPIQuery totle = new DataUnitAreaKPIQuery();
                foreach (DataUnitAreaKPIQuery sd in paraPeriodList)
                {
                    totle.addStatData(sd);
                }
                if (msgCountDic!=null)
                {
                    totle.mtrMsgCounterDic = msgCountDic;
                }
                if (finfo.CarrierType == 1)
                {
                    addMobileData(finfo, totle);
                }
                else if (finfo.CarrierType == 2)
                {
                    addUnicomData(finfo, totle);
                }
                else if (finfo.CarrierType == 3)
                {
                    addTelecomData(finfo, totle);
                }
            }
            WaitBox.Close();
        }

        private void addMobileData(FileInfo finfo, DataUnitAreaKPIQuery totle)
        {
            if (MainModel.CurChinaMobileStatReportData == null)
            {
                MainModel.CurChinaMobileStatReportData = new StatReportData(totle);
            }
            else
            {
                MainModel.CurChinaMobileStatReportData.AddData(totle);
            }

            if (finfo.Momt == (int)MoMtFile.MoFlag)
            {
                if (MainModel.CurChinaMobileStatReportDataMo == null)
                {
                    MainModel.CurChinaMobileStatReportDataMo = new StatReportData(totle);
                }
                else
                {
                    MainModel.CurChinaMobileStatReportDataMo.AddData(totle);
                }
            }
            else if (finfo.Momt == (int)MoMtFile.MtFlag)
            {
                if (MainModel.CurChinaMobileStatReportDataMt == null)
                {
                    MainModel.CurChinaMobileStatReportDataMt = new StatReportData(totle);
                }
                else
                {
                    MainModel.CurChinaMobileStatReportDataMt.AddData(totle);
                }
            }
        }

        private void addUnicomData(FileInfo finfo, DataUnitAreaKPIQuery totle)
        {
            if (MainModel.CurChinaUnicomStatReportData == null)
            {
                MainModel.CurChinaUnicomStatReportData = new StatReportData(totle);
            }
            else
            {
                MainModel.CurChinaUnicomStatReportData.AddData(totle);
            }

            if (finfo.Momt == (int)MoMtFile.MoFlag)
            {
                if (MainModel.CurChinaUnicomStatReportDataMo == null)
                {
                    MainModel.CurChinaUnicomStatReportDataMo = new StatReportData(totle);
                }
                else
                {
                    MainModel.CurChinaUnicomStatReportDataMo.AddData(totle);
                }
            }
            else if (finfo.Momt == (int)MoMtFile.MtFlag)
            {
                if (MainModel.CurChinaUnicomStatReportDataMt == null)
                {
                    MainModel.CurChinaUnicomStatReportDataMt = new StatReportData(totle);
                }
                else
                {
                    MainModel.CurChinaUnicomStatReportDataMt.AddData(totle);
                }
            }
        }

        private void addTelecomData(FileInfo finfo, DataUnitAreaKPIQuery totle)
        {
            if (MainModel.CurChinaTelecomStatReportData == null)
            {
                MainModel.CurChinaTelecomStatReportData = new StatReportData(totle);
            }
            else
            {
                MainModel.CurChinaTelecomStatReportData.AddData(totle);
            }

            if (finfo.Momt == (int)MoMtFile.MoFlag)
            {
                if (MainModel.CurChinaTelecomStatReportDataMo == null)
                {
                    MainModel.CurChinaTelecomStatReportDataMo = new StatReportData(totle);
                }
                else
                {
                    MainModel.CurChinaTelecomStatReportDataMo.AddData(totle);
                }
            }
            else if (finfo.Momt == (int)MoMtFile.MtFlag)
            {
                if (MainModel.CurChinaTelecomStatReportDataMt == null)
                {
                    MainModel.CurChinaTelecomStatReportDataMt = new StatReportData(totle);
                }
                else
                {
                    MainModel.CurChinaTelecomStatReportDataMt.AddData(totle);
                }
            }
        }

        protected void prepareSearchDIYLogPackage(Package package, FileInfo finfo)
        {
            //log file
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_DIY_LOG_KPI;
            package.Content.PrepareAddParam();
            TimePeriod period = new TimePeriod();
            if(finfo.BeginTime!=0)
            {
                DateTime fsTime = JavaDate.GetDateTimeFromMilliseconds(finfo.BeginTime * 1000L);
                DateTime startTime = new DateTime(fsTime.Year, fsTime.Month, 1);
                DateTime endTime = startTime.AddMonths(1).AddSeconds(-1);
                period = new TimePeriod(startTime, endTime);
            }
            else if(finfo.LogTable!=null)
            {
                DateTime startTime = extractFromLogTbName(finfo.LogTable);
                DateTime endTime = startTime.AddMonths(1).AddSeconds(-1);
                period = new TimePeriod(startTime, endTime);
            }
            AddDIYPeriod(package, period);
            AddDIYFileID(package, finfo.ID);
            AddDIYEndOpFlag(package);
            //img grid
            fillContentNeeded_ImgGrid(package, -1);
        }
        private DateTime extractFromLogTbName(string strname)
        {
            string[] vec = strname.Split('_');
            if (vec.Length == 5)
            {
                int year;
                int month;
                int.TryParse(vec[3], out year);
                int.TryParse(vec[4], out month);
                return new DateTime(year, month, 1);
            }
            throw (new Exception("格式错误，时间获取失败！"));
        }

        protected void prepareSearchDIYLogEventPackage(Package package, FileInfo finfo)
        {
            //log file
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_DIY_AREA_EVENT;
            package.Content.PrepareAddParam();
            TimePeriod period = new TimePeriod();
            if (finfo.BeginTime != 0)
            {
                DateTime fsTime = JavaDate.GetDateTimeFromMilliseconds(finfo.BeginTime * 1000L);
                DateTime startTime = new DateTime(fsTime.Year, fsTime.Month, 1);
                DateTime endTime = startTime.AddMonths(1).AddSeconds(-1);
                period = new TimePeriod(startTime, endTime);
            }
            else if (finfo.LogTable != null)
            {
                DateTime startTime = extractFromLogTbName(finfo.LogTable);
                DateTime endTime = startTime.AddMonths(1).AddSeconds(-1);
                period = new TimePeriod(startTime, endTime);
            }
            AddDIYPeriod(package, period);
            AddDIYFileID(package, finfo.ID);
            AddDIYEndOpFlag(package);
            AddDIYEndOpFlag(package);
            //img grid
            fillContentNeeded_Event(package);
        }
        protected void AddDIYFileID(Package package, int fileID)
        {
            package.Content.AddParam((byte)OpOptionDef.Equal);
            package.Content.AddParam("0,1,1");//fileid
            package.Content.AddParam("" + fileID);
        }
        
        protected override void prepareStatPackage_Event_EventFilter(Package package, TimePeriod period)
        {
            AddDIYEndOpFlag(package);
        }
        protected override void recieveInfo_ImgGrid(ClientProxy clientProxy, List<DataUnitAreaKPIQuery> paraRetList, int carrierId, TimePeriod period)
        {
            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
            DTDataHeaderManager headerManager = DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 25;
            WaitBox.ProgressPercent = progress;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    DTDataHeader header = recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
                    headerManager.AddDTDataHeader(header);
                }
                else if (package.Content.Type == ResponseType.COLUMN_DEFINE)
                {
                    curImgColumnDef.Clear();
                    string idpairs = package.Content.GetParamString();
                    parseToCurImgColumnDef(idpairs, curImgColumnDef);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_GSM
                || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_GPRS)
                {

                    DataGSM_NewImg newImg = new DataGSM_NewImg();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    retResult.addStatData(newImg);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_TDSCDMA_AMR
               || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_TDSCDMA_PS
               || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_TDSCDMA_VP)
                {
                    DataTDSCDMA_NewImg newImg = new DataTDSCDMA_NewImg();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    retResult.addStatData(newImg);

                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_AMR
           || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_PS
           || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_VP
           || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_WCDMA_PSHS)
                {
                    try
                    {

                        DataWCDMA_AMR newImg = new DataWCDMA_AMR();
                        foreach (StatImgDefItem cdf in curImgColumnDef)
                        {
                            byte[] imgBytes = package.Content.GetParamBytes();
                            Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                            foreach (string str in cellStatInfoDic.Keys)
                            {
                                newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                            }
                        }
                        retResult.addStatData(newImg);
                    }
                    catch (Exception ex)
                    {
                        log.Error(ex.StackTrace);
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_CDMA_V
               || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_CDMA_D)
                {
                    DataCDMA_Voice newImg = new DataCDMA_Voice();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    retResult.addStatData(newImg);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_CDMA2000_D)
                {
                    DataEVDO_Data newImg = new DataEVDO_Data();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    retResult.addStatData(newImg);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_GSM_MTR)
                {
                    DataMTR_GSM newImg = new DataMTR_GSM();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    retResult.addStatData(newImg);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_SCAN_LTETOPN
                  || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_LTE_FREQSPECTRUM)
                {
                    DataScan_LTE newImg = new DataScan_LTE();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    retResult.addStatData(newImg);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_SCAN_NR
                    || package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_NR_FREQSPECTRUM)
                {
                    DataScan_NR newImg = new DataScan_NR();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    retResult.addStatData(newImg);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_SCAN_TD)
                {
                    DataScan_TD newImg = new DataScan_TD();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    retResult.addStatData(newImg);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_SCAN_GSM)
                {
                    DataScan_GSM newImg = new DataScan_GSM();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.WInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    retResult.addStatData(newImg);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_WLAN)
                {
                    DataWLAN newImg = new DataWLAN();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.WInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    retResult.addStatData(newImg);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_LOG_KPI_LTE_AMR)
                {
                    DataLTE newImg = new DataLTE();
                    foreach (StatImgDefItem cdf in curImgColumnDef)
                    {
                        byte[] imgBytes = package.Content.GetParamBytes();
                        Dictionary<String, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                        foreach (string str in cellStatInfoDic.Keys)
                        {
                            newImg.WInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                        }
                    }
                    retResult.addStatData(newImg);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                #endregion

                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
            paraRetList.Add(retResult);
            WaitBox.ProgressPercent = 95;
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

    }
}
