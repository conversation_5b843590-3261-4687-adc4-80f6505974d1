﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util.UiEx;
using MasterCom.RAMS.Net;
using System.Windows.Forms;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    class LteHandOverPingPangAna : DIYAnalyseByCellBackgroundBaseByFile
    {
        protected List<DTFileDataManager> fileManagers = new List<DTFileDataManager>();//结果列表重新统计用到
        protected PingPangAnalyzer analyzer;

        protected static readonly object lockObj = new object();
        private static LteHandOverPingPangAna instance = null;
        public static LteHandOverPingPangAna GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteHandOverPingPangAna(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }

        protected LteHandOverPingPangAna(MainModel mModel)
            : base(mModel)
        {
            analyzer = new PingPangAnalyzer();
            ServiceTypes.Clear();
            ServiceTypes.AddRange(ServiceTypeManager.GetLteAllServiceTypes());
        }

        public override string Name
        {
            get { return "LTE乒乓切换"; }
        }

        public PingPangAnalyzer Analyzer
        {
            get { return analyzer; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22058, this.Name);
        }

        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                FilterEventByRegion = true;
                return true;
            }
            FilterEventByRegion = false;
            PingPangSettingForm dlg = new PingPangSettingForm();
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                this.Analyzer.PingPangCond = dlg.GetCondition();
                return true;
            }
            return false;
        }

        protected override void getReadyBeforeQuery()
        {
            fileManagers.Clear();
            analyzer.Init();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager dtFile in MainModel.DTDataManager.FileDataManagers)
            {
                if (!mainModel.IsBackground)
                {
                    fileManagers.Add(dtFile);
                }
                analyzer.Analyze(dtFile);
            }
        }

        protected override void getResultsAfterQuery()
        {
            analyzer.SetSN();
        }

        protected override void fireShowForm()
        {
            PingPangResultForm frm = MainModel.CreateResultForm(typeof(PingPangResultForm)) as PingPangResultForm;
            frm.FillData(analyzer.Results, this);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }

        // 在结果窗口里面点击重新统计
        public void DoQuery()
        {
            if (fileManagers == null || fileManagers.Count <= 0)
            {
                base.query();
                return;
            }

            WaitTextBox.Show("正在重新分析...", ReAnalyzeInThread);
            fireShowForm();
        }

        private void ReAnalyzeInThread()
        {
            try
            {
                analyzer.Analyze(fileManagers);
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show(ex.Message + Environment.NewLine + ex.StackTrace, this.Name,
                    System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
            }
            finally
            {
                System.Threading.Thread.Sleep(300);
                WaitTextBox.Close();
            }
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.切换; }
        }
        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Cell_Simple; }
        }
        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["TimeLimit"] = this.Analyzer.PingPangCond.TimeLimit;
                param["IsLimitSpeed"] = this.Analyzer.PingPangCond.IsLimitSpeed;
                param["SpeedLimitMin"] = this.Analyzer.PingPangCond.SpeedLimitMin;
                param["SpeedLimitMax"] = this.Analyzer.PingPangCond.SpeedLimitMax;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("TimeLimit"))
                {
                    this.Analyzer.PingPangCond.TimeLimit = (int)param["TimeLimit"];
                }
                if (param.ContainsKey("IsLimitSpeed"))
                {
                    this.Analyzer.PingPangCond.IsLimitSpeed = (bool)param["IsLimitSpeed"];
                }
                if (param.ContainsKey("SpeedLimitMin"))
                {
                    this.Analyzer.PingPangCond.SpeedLimitMin = (int)param["SpeedLimitMin"];
                }
                if (param.ContainsKey("SpeedLimitMax"))
                {
                    this.Analyzer.PingPangCond.SpeedLimitMax = (int)param["SpeedLimitMax"];
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new HandoverPingPangProperties_LTE(this);
            }
        }

        protected override void saveBackgroundData()
        {
            List<BackgroundResult> bgResultList = new List<BackgroundResult>();
            analyzer.SetSN();
            foreach (PingPangFile ppFile in analyzer.Results)
            {
                bgResultList.AddRange(ppFile.CellsConvertToBgResultList(curAnaFileInfo, GetSubFuncID()));

                foreach (PingPangPair ppPair in ppFile.Pairs)
                {
                    BackgroundResult result = ppPair.ConvertToBackgroundResult(curAnaFileInfo, ppFile.PairCount);
                    result.SubFuncID = GetSubFuncID();
                    result.ProjectString = BackgroundFuncBaseSetting.GetInstance().projectType;
                    bgResultList.Add(result);
                }
            }
            BackgroundFuncQueryManager.GetInstance().SaveResult_Cell(GetSubFuncID(), curAnaFileInfo, bgResultList);
            analyzer.Results.Clear();
        }
        protected override void initBackgroundImageDesc()
        {
            Dictionary<int, List<BackgroundResult>> handDetailDic = new Dictionary<int, List<BackgroundResult>>();
            Dictionary<int, List<BackgroundResult>> handCellDic = new Dictionary<int, List<BackgroundResult>>();
            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                List<BackgroundResult> bgList;
                if (bgResult.StrDesc == "切换序列")
                {
                    if (!handDetailDic.TryGetValue(bgResult.FileID, out bgList))
                    {
                        bgList = new List<BackgroundResult>();
                        handDetailDic[bgResult.FileID] = bgList;
                    }
                    bgList.Add(bgResult);
                }
                else if (bgResult.StrDesc == "切换小区")
                {
                    if (!handCellDic.TryGetValue(bgResult.FileID, out bgList))
                    {
                        bgList = new List<BackgroundResult>();
                        handCellDic[bgResult.FileID] = bgList;
                    }
                    bgList.Add(bgResult);
                }
            }

            this.BackgroundNPOIRowResultDic.Clear();
            this.BackgroundNPOIRowResultDic["切换序列"] = getHandDetailNPOIRows(handDetailDic);
            this.BackgroundNPOIRowResultDic["切换小区"] = getHandCellNPOIRows(handCellDic);
        }

        private List<NPOIRow> getHandDetailNPOIRows(Dictionary<int, List<BackgroundResult>> handDetailDic)
        {
            List<NPOIRow> handDetailNPOIRowList = new List<NPOIRow>();
            NPOIRow rowTitle = new NPOIRow();
            rowTitle.AddCellValue("文件序号");
            rowTitle.AddCellValue("文件名称");
            rowTitle.AddCellValue("文件日期");
            rowTitle.AddCellValue("乒乓切换组数");
            rowTitle.AddCellValue("切换序号");
            rowTitle.AddCellValue("切换小区");
            rowTitle.AddCellValue("切换间隔");
            rowTitle.AddCellValue("切换时间");
            rowTitle.AddCellValue("切换前小区");
            rowTitle.AddCellValue("切换后小区");
            rowTitle.AddCellValue("前场强");
            rowTitle.AddCellValue("后场强");
            rowTitle.AddCellValue("前质量");
            rowTitle.AddCellValue("后质量");
            handDetailNPOIRowList.Add(rowTitle);

            int fileIndex = 0;
            foreach (List<BackgroundResult> bgResultList in handDetailDic.Values)
            {
                fileIndex++;
                NPOIRow handDetailRow_1 = null;
                int handIndex = 0;
                foreach (BackgroundResult bgResult in bgResultList)
                {
                    handIndex++;
                    int pairCount = bgResult.GetImageValueInt();
                    string handOverCellDes = bgResult.GetImageValueString();
                    string pairInterval = bgResult.GetImageValueString();
                    int evtCount = bgResult.GetImageValueInt();

                    bgResult.StatType = BackgroundStatType.None;
                    bgResult.ImageDesc = string.Format("乒乓切换小区:{0}", handOverCellDes);

                    if (handDetailRow_1 == null)
                    {
                        handDetailRow_1 = new NPOIRow();
                        handDetailNPOIRowList.Add(handDetailRow_1);
                        handDetailRow_1.AddCellValue(fileIndex);
                        handDetailRow_1.AddCellValue(bgResult.FileName);
                        handDetailRow_1.AddCellValue(JavaDate.GetDateTimeFromMilliseconds(bgResult.ISTime * 1000L).ToShortDateString());
                        handDetailRow_1.AddCellValue(pairCount);

                    }
                    NPOIRow handDetailRow_2 = new NPOIRow();
                    handDetailRow_1.AddSubRow(handDetailRow_2);
                    handDetailRow_2.AddCellValue(handIndex);
                    handDetailRow_2.AddCellValue(handOverCellDes);
                    handDetailRow_2.AddCellValue(pairInterval);
                    for (int i = 0; i < evtCount; i++)
                    {
                        string time = bgResult.GetImageValueString();
                        string srcCellName = bgResult.GetImageValueString();
                        string tarCellName = bgResult.GetImageValueString();
                        float beforeRsrp = bgResult.GetImageValueFloat();
                        float afterRsrp = bgResult.GetImageValueFloat();
                        float beforeSinr = bgResult.GetImageValueFloat();
                        float afterSinr = bgResult.GetImageValueFloat();

                        NPOIRow handDetailRow_3 = new NPOIRow();
                        handDetailRow_2.AddSubRow(handDetailRow_3);
                        handDetailRow_3.AddCellValue(time);
                        handDetailRow_3.AddCellValue(srcCellName);
                        handDetailRow_3.AddCellValue(tarCellName);
                        handDetailRow_3.AddCellValue(beforeRsrp);
                        handDetailRow_3.AddCellValue(afterRsrp);
                        handDetailRow_3.AddCellValue(beforeSinr);
                        handDetailRow_3.AddCellValue(afterSinr);
                    }
                }
            }
            return handDetailNPOIRowList;
        }
        private List<NPOIRow> getHandCellNPOIRows(Dictionary<int, List<BackgroundResult>> handCellDic)
        {
            List<NPOIRow> handCellNPOIRowList = new List<NPOIRow>();
            NPOIRow rowTitle = new NPOIRow();
            rowTitle.AddCellValue("文件序号");
            rowTitle.AddCellValue("文件名称");
            rowTitle.AddCellValue("小区序号");
            rowTitle.AddCellValue("小区名称");
            rowTitle.AddCellValue("TAC");
            rowTitle.AddCellValue("ECI");
            rowTitle.AddCellValue("切换次数");
            handCellNPOIRowList.Add(rowTitle);

            int fileIndex = 0;
            foreach (List<BackgroundResult> bgResultList in handCellDic.Values)
            {
                fileIndex++;
                int cellIndex = 0;
                NPOIRow handDetailRow_1 = null;
                foreach (BackgroundResult bgResult in bgResultList)
                {
                    int cellCount = bgResult.GetImageValueInt();

                    if (handDetailRow_1 == null)
                    {
                        handDetailRow_1 = new NPOIRow();
                        handCellNPOIRowList.Add(handDetailRow_1);
                        handDetailRow_1.AddCellValue(fileIndex);
                        handDetailRow_1.AddCellValue(bgResult.FileName);
                    }
                    for (int i = 0; i < cellCount; i++)
                    {
                        cellIndex++;
                        string srcCellName = bgResult.GetImageValueString();
                        int lac = bgResult.GetImageValueInt();
                        int ci = bgResult.GetImageValueInt();
                        int handCount = bgResult.GetImageValueInt();
                        NPOIRow handDetailRow_2 = new NPOIRow();
                        handDetailRow_1.AddSubRow(handDetailRow_2);
                        handDetailRow_2.AddCellValue(cellIndex);
                        handDetailRow_2.AddCellValue(srcCellName);
                        handDetailRow_2.AddCellValue(lac);
                        handDetailRow_2.AddCellValue(ci);
                        handDetailRow_2.AddCellValue(handCount);
                    }
                }
            }
            return handCellNPOIRowList;
        }
        #endregion
    }
    class LteHandOverPingPangAna_FDD : LteHandOverPingPangAna
    {
        private static LteHandOverPingPangAna_FDD instance = null;
        public static new LteHandOverPingPangAna_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteHandOverPingPangAna_FDD(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }

        protected LteHandOverPingPangAna_FDD(MainModel mainModel)
            : base(mainModel)
        {

        }
        public override string Name
        {
            get { return "LTE_FDD乒乓切换"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26038, this.Name);
        }
    }
}
