﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;

using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.NOP
{
    public partial class DealTimeStatForm : MinCloseForm
    {
        public DealTimeStatForm()
        {
            InitializeComponent();
            miExportExcel.Click += MiExportExcel_Click;
            DisposeWhenClose = true;
            //init();
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            if (results == null)
            {
                return;
            }
           


                #region
                List<NPOIRow> summBroTables = new List<NPOIRow>();
                NPOIRow summBroTitleRow = new NPOIRow();
                summBroTitleRow.AddCellValue("工单名称");
                summBroTitleRow.AddCellValue("地市");
                summBroTitleRow.AddCellValue("工单组");
                summBroTitleRow.AddCellValue("工单派单时间");
                summBroTitleRow.AddCellValue("工单处理时间");
                summBroTitleRow.AddCellValue("工单处理状态");
                summBroTitleRow.AddCellValue("上级派单");
                summBroTitleRow.AddCellValue("下级派单");
                summBroTitleRow.AddCellValue("T1处理时长(小时)");
                summBroTitleRow.AddCellValue("T1处理个数");
                summBroTitleRow.AddCellValue("T2_1处理时长(小时)");
                summBroTitleRow.AddCellValue("T2_1处理个数");
                summBroTitleRow.AddCellValue("T2_2处理时长(小时)");
                summBroTitleRow.AddCellValue("T2_2处理个数");
                summBroTitleRow.AddCellValue("T2_3处理时长(小时)");
                summBroTitleRow.AddCellValue("T2_3处理个数");
                summBroTitleRow.AddCellValue("T2_4处理时长(小时)");
                summBroTitleRow.AddCellValue("T2_4处理个数");
                summBroTitleRow.AddCellValue("T2_5处理时长(小时)");
                summBroTitleRow.AddCellValue("T2_5处理个数");
                summBroTitleRow.AddCellValue("T3处理时长(小时)");
                summBroTitleRow.AddCellValue("T3处理个数");
                summBroTitleRow.AddCellValue("T0处理时长(小时)");
                summBroTitleRow.AddCellValue("T0处理个数");
                summBroTitleRow.AddCellValue("工程督办处理时长(小时)");
                summBroTables.Add(summBroTitleRow);
                if (results != null)
                {
                   foreach (DealTimeResult res in results)
                {
                    NPOIRow row = new NPOIRow();
                    row.AddCellValue(res.TaskName);
                    row.AddCellValue(res.District);
                    row.AddCellValue(res.GroupName);
                    row.AddCellValue(res.OrderTime);
                    row.AddCellValue(res.DealTime);
                    row.AddCellValue(res.Status);
                    row.AddCellValue(res.LastGroupName);
                    row.AddCellValue(res.NextGroupName);
                    row.AddCellValue(Math.Round(res.T1T,2));
                    row.AddCellValue(res.T1C);
                    row.AddCellValue(Math.Round(res.T21T,2));
                    row.AddCellValue(res.T21C);
                    row.AddCellValue(Math.Round(res.T22T,2));
                    row.AddCellValue(res.T22C);
                    row.AddCellValue(Math.Round(res.T23T,2));
                    row.AddCellValue(res.T23C);
                    row.AddCellValue(Math.Round(res.T24T,2));
                    row.AddCellValue(res.T24C);
                    row.AddCellValue(Math.Round(res.T25T,2));
                    row.AddCellValue(res.T25C);
                    row.AddCellValue(Math.Round(res.T3T,2));
                    row.AddCellValue(res.T3C);
                    row.AddCellValue(Math.Round(res.T0T,2));
                    row.AddCellValue(res.T0C);
                    row.AddCellValue(res.DuBan);
                    summBroTables.Add(row);
                }
            }

                #endregion


                ExcelNPOIManager.ExportToExcel(new List<List<NPOIRow>>() {  summBroTables },
                new List<string>() { "工单处理时间" });
        }

        
        List<DealTimeResult> results = null;
        public void FillData(object result)
        {
            results = result as List<DealTimeResult>;
            gcTask.DataSource = results;
            gcTask.RefreshDataSource();
            

            MainModel.RefreshLegend();
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GetCellLayer().Invalidate();
            }
        }
    }
}
