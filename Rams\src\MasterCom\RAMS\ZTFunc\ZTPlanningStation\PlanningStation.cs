﻿using System;
using System.Collections.Generic;
using System.Text;
using System.IO;
using System.Data.OleDb;
using System.Data;
using MasterCom.MTGis;
using System.Drawing;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class PlanningStationLaunchForm : ShowFuncForm
    {
        public PlanningStationLaunchForm(MainModel mm) : base(mm)
        {
        }

        public override string Name
        {
            get { return "Excel导入规划站"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 19000, 19024, this.Name);
        }

        protected override void showForm()
        {
            PlanningStationManager.Activate(MainModel);
        }
    }

    public static class PlanningStationManager
    {
        private static Dictionary<string, StationShowItem> fileStationDict = new Dictionary<string, StationShowItem>();
        private static MainModel mainModel;
        private static AxMapWinGIS.AxMap axMap;
        public static float PointSize { get; set; } = 7;

        public static void Activate(MainModel mm)
        {
            if (mainModel == null)
            {
                mainModel = mm;
                axMap = mainModel.MainForm.GetMapForm().GetMapFormControl();
            }

            object obj = mainModel.GetObjectFromBlackboard(typeof(PlanningStationImportForm).FullName);
            PlanningStationImportForm form = obj == null ? null : obj as PlanningStationImportForm;
            if (form == null || form.IsDisposed)
            {
                form = new PlanningStationImportForm(mainModel);
            }
            if (!form.Visible)
            {
                form.Show(mainModel.MainForm);
            }
        }

        public static List<string> GetFileNames()
        {
            return new List<string>(fileStationDict.Keys);
        }

        public static List<string> Select(MapOperation Map, MapOperation2 mop2)
        {
            List<string> retList = new List<string>();
            foreach (StationShowItem item in fileStationDict.Values)
            {
                if (!item.DrawStyle.Visible)
                {
                    continue;
                }
                retList.AddRange(item.StationInfo.SelectInfoString(Map, mop2 ,PointSize));
            }
            return retList;
        }

        public static bool Add(string fileName)
        {
            StationShowItem item = new StationShowItem();
            item.StationInfo = new StationInfo();
            if (!item.StationInfo.LoadFromExcel(fileName))
            {
                return false;
            }
            item.DrawStyle = new MapWinGIS.ShapeDrawingOptions();
            item.DrawStyle.Visible = true;
            item.DrawStyle.FillColor = (uint)ColorTranslator.ToOle(Color.Red);
            item.DrawStyle.PointSize = 7;
            item.DrawStyle.PointType = MapWinGIS.tkPointSymbolType.ptSymbolStandard;
            item.DrawStyle.PointShape = MapWinGIS.tkPointShapeType.ptShapeCross;
            item.DrawStyle.LineVisible = false;
            item.Shp = item.StationInfo.CreateDisplayShpfile();
            item.Shp.DefaultDrawingOptions = item.DrawStyle;
            item.layerHandle = axMap.AddLayer(item.Shp, true);
            fileStationDict.Add(fileName, item);
            return true;
        }

        public static bool Remove(string fileName)
        {
            if (!fileStationDict.ContainsKey(fileName))
            {
                return false;
            }
            StationShowItem item = fileStationDict[fileName];
            axMap.RemoveLayer(item.layerHandle);
            fileStationDict.Remove(fileName);
            return true;
        }

        public static bool Refresh(string fileName)
        {
            axMap.Redraw();
            return true;
        }

        public static StationShowItem GetItem(string fileName)
        {
            if (fileStationDict.ContainsKey(fileName))
            {
                return fileStationDict[fileName];
            }
            return null;
        }
    }
    
    public class StationShowItem
    {
        public StationInfo StationInfo { get; set; }
        public MapWinGIS.ShapeDrawingOptions DrawStyle { get; set; }
        public MapWinGIS.Shapefile Shp { get; set; }
        public int layerHandle { get; set; }

        public StationShowItem()
        {
            layerHandle = -1;
        }
    }

    public class StationInfo
    {
        private List<string> columnNames;
        private List<List<object>> values;

        public List<DbPoint> LngLatList { get; set; }

        public List<string> ColumnNames
        {
            get { return columnNames; }
        }

        public List<List<object>> Values
        {
            get { return values; }
        }

        public string GetFormatedInfo(int row)
        {
            if (row >= values.Count)
            {
                return "";
            }

            List<object> objs = values[row];
            StringBuilder sb = new StringBuilder("");
            for (int i = 0; i < columnNames.Count; ++i)
            {
                sb.Append(columnNames[i] + ": ");
                sb.Append(objs[i].ToString() + Environment.NewLine);
            }

            return sb.ToString();
        }

        public List<string> SelectInfoString(MapOperation Map, MapOperation2 mop2, float PointSize)
        {
            List<string> retList = new List<string>();
            for (int i = 0; i < LngLatList.Count; ++i)
            {
                DbPoint lngLat = LngLatList[i];
                if (lngLat == null)
                {
                    continue;
                }
                if (!isSelectedBTS(lngLat, Map, mop2, PointSize))
                {
                    continue;
                }
                retList.Add(GetFormatedInfo(i));
            }
            return retList;
        }

        private bool isSelectedBTS(DbPoint dPoint, MapOperation Map, MapOperation2 mop2, float PointSize)
        {
            PointF point;
            Map.ToDisplay(dPoint, out point);
            RectangleF rect;

            rect = new RectangleF(point.X - PointSize / 2, point.Y - PointSize / 2, PointSize, PointSize);

            DbRect dRect;
            Map.FromDisplay(rect, out dRect);
            if (mop2.CheckCenterInDRect(dRect))
            {
                return true;
            }
            return false;
        }

        public MapWinGIS.Shapefile CreateDisplayShpfile()
        {
            MapWinGIS.Shapefile shp = new MapWinGIS.Shapefile();
            shp.CreateNew("", MapWinGIS.ShpfileType.SHP_POINT);

            int shapeIndex = 0;
            foreach (DbPoint lngLat in LngLatList)
            {
                if (lngLat == null)
                {
                    continue;
                }

                MapWinGIS.Point pt = new MapWinGIS.Point();
                pt.x = lngLat.x;
                pt.y = lngLat.y;
                int pointIndex = 0;
                MapWinGIS.Shape shape = new MapWinGIS.Shape();
                shape.ShapeType = MapWinGIS.ShpfileType.SHP_POINT;
                shape.InsertPoint(pt, ref pointIndex);
                shp.EditInsertShape(shape, ref shapeIndex);
            }

            return shp;
        }

        public bool LoadFromExcel(string path)
        {
            DataSet ds = null;
            DataTable dt = null;

            // 读取dataset
            try
            {
                ds = ExcelNPOIManager.ImportFromExcel(path);
            }
            catch
            {
                return false;
            }

            // 查找第一个datatable
            foreach (DataTable tb in ds.Tables)
            {
                if (tb.Columns.Contains("经度") && tb.Columns.Contains("纬度"))
                {
                    dt = tb;
                    break;
                }
            }
            if (dt == null)
            {
                return false;
            }

            // 读入列名，值和经纬度
            columnNames = new List<string>();
            values = new List<List<object>>();
            LngLatList = new List<DbPoint>();
            int lngIndex = dt.Columns.IndexOf("经度");
            int latIndex = dt.Columns.IndexOf("纬度");
            foreach (DataColumn col in dt.Columns)
            {
                columnNames.Add(col.ColumnName);
            }
            foreach (DataRow row in dt.Rows)
            {
                List<object> objs = new List<object>(row.ItemArray);
                values.Add(objs);
                double x = 0, y = 0;
                if (!double.TryParse(row[lngIndex].ToString(), out x)
                    || !double.TryParse(row[latIndex].ToString(), out y))
                {
                    LngLatList.Add(null);
                }
                else
                {
                    LngLatList.Add(new DbPoint(x, y));
                }
            }

            return true;
        }
    }

    public static class ExcelImporterByOLEDB
    {
        public static DataSet ToDataTable(string filePath, string selectSql)
        {
            string fileType = System.IO.Path.GetExtension(filePath);
            if (string.IsNullOrEmpty(fileType))
            {
                throw (new Exception("Unknown file type: " + filePath));
            }

            string connStr = "";
            if (fileType == ".xls")
            {
                connStr = "Provider=Microsoft.Jet.OLEDB.4.0;" + "Data Source=" + filePath + ";" + ";Extended Properties=\"Excel 8.0;HDR=YES;IMEX=1\"";
            }
            else
            {
                connStr = "Provider=Microsoft.ACE.OLEDB.12.0;" + "Data Source=" + filePath + ";" + ";Extended Properties=\"Excel 12.0;HDR=YES;IMEX=1\"";
            }

            OleDbConnection conn = null;
            OleDbDataAdapter da = null;
            DataTable dtSheets = null;
            DataSet ds = new DataSet();
            try
            {
                conn = new OleDbConnection(connStr);
                conn.Open();

                dtSheets = conn.GetOleDbSchemaTable(OleDbSchemaGuid.Tables, new object[] { null, null, null, "TABLE" });
                da = new OleDbDataAdapter();
                for (int i = 0; i < dtSheets.Rows.Count; i++)
                {
                    string curSheet = (string)dtSheets.Rows[i]["TABLE_NAME"];
                    if (curSheet.Contains("$") && !curSheet.Replace("'", "").EndsWith("$"))
                    {
                        continue;
                    }

                    da.SelectCommand = new OleDbCommand(String.Format(selectSql, curSheet), conn);
                    DataSet dsItem = new DataSet();
                    try
                    {
                        da.Fill(dsItem, curSheet);
                    }
                    catch
                    {
                        continue;
                    }
                    dsItem.Tables[0].TableName = i.ToString();
                    ds.Tables.Add(dsItem.Tables[0].Copy());
                }
            }
            finally
            {
                if (conn != null && conn.State == ConnectionState.Open)
                {
                    conn.Close();
                    da.Dispose();
                    conn.Dispose();
                }
            }
            return ds;
        }
    }
}
