﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class MRDataCloudPictureSettingForm : BaseForm
    {
        public CellCloudPictureConfig Config { get; set; }
        public int SelectedIndex { get; set; }
        public MRDataCloudPictureSettingForm(List<string> columns)
        {
            InitializeComponent();
            cbxColumn.Items.Clear();
            foreach (string str in columns)
            {
                cbxColumn.Items.Add(str);
            }
            cbxColumn.SelectedIndex = 0;

            Config = cellCloudPictureCommonSetting1.Config;
            SelectedIndex = cbxColumn.SelectedIndex;

            cbxColumn.SelectedIndexChanged += CbxColumn_SelectedChanged;
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
        }

        private void CbxColumn_SelectedChanged(object sender, EventArgs e)
        {
            SelectedIndex = cbxColumn.SelectedIndex;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }
}
