﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NoXNearestCellInfoForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.dataGridView = new System.Windows.Forms.DataGridView();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.tsmiExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.ColumnSn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnDateTime = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnTpLongitude = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnTpLatitude = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnBcch = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnBsic = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnMainCell = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnMainLac = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnMainCi = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnMainDistance = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnMainLongitude = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnMainLatitude = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnSecCellName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnSecLac = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnCi = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnSecDistance = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnSecLongitude = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ColumnSecLatitude = new System.Windows.Forms.DataGridViewTextBoxColumn();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // dataGridView
            // 
            this.dataGridView.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.ColumnSn,
            this.ColumnDateTime,
            this.ColumnTpLongitude,
            this.ColumnTpLatitude,
            this.ColumnBcch,
            this.ColumnBsic,
            this.ColumnMainCell,
            this.ColumnMainLac,
            this.ColumnMainCi,
            this.ColumnMainDistance,
            this.ColumnMainLongitude,
            this.ColumnMainLatitude,
            this.ColumnSecCellName,
            this.ColumnSecLac,
            this.ColumnCi,
            this.ColumnSecDistance,
            this.ColumnSecLongitude,
            this.ColumnSecLatitude});
            this.dataGridView.ContextMenuStrip = this.contextMenuStrip;
            this.dataGridView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridView.Location = new System.Drawing.Point(0, 0);
            this.dataGridView.Name = "dataGridView";
            this.dataGridView.RowHeadersVisible = false;
            this.dataGridView.RowTemplate.Height = 23;
            this.dataGridView.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridView.Size = new System.Drawing.Size(906, 414);
            this.dataGridView.TabIndex = 0;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmiExport2Xls});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(175, 26);
            // 
            // tsmiExport2Xls
            // 
            this.tsmiExport2Xls.Name = "tsmiExport2Xls";
            this.tsmiExport2Xls.Size = new System.Drawing.Size(174, 22);
            this.tsmiExport2Xls.Text = "导出数据到Excel...";
            this.tsmiExport2Xls.Click += new System.EventHandler(this.tsmiExport2Xls_Click);
            // 
            // ColumnSn
            // 
            this.ColumnSn.DataPropertyName = "Sn";
            this.ColumnSn.HeaderText = "序号";
            this.ColumnSn.MinimumWidth = 20;
            this.ColumnSn.Name = "ColumnSn";
            this.ColumnSn.Width = 60;
            // 
            // ColumnDateTime
            // 
            this.ColumnDateTime.DataPropertyName = "TestPointTime";
            this.ColumnDateTime.HeaderText = "时间";
            this.ColumnDateTime.Name = "ColumnDateTime";
            this.ColumnDateTime.Width = 140;
            // 
            // ColumnTpLongitude
            // 
            this.ColumnTpLongitude.DataPropertyName = "TestPointLongitude";
            this.ColumnTpLongitude.HeaderText = "采样点经度";
            this.ColumnTpLongitude.Name = "ColumnTpLongitude";
            // 
            // ColumnTpLatitude
            // 
            this.ColumnTpLatitude.DataPropertyName = "TestPointLatitude";
            this.ColumnTpLatitude.HeaderText = "采样点纬度";
            this.ColumnTpLatitude.Name = "ColumnTpLatitude";
            // 
            // ColumnBcch
            // 
            this.ColumnBcch.DataPropertyName = "Bcch";
            this.ColumnBcch.HeaderText = "BCCH";
            this.ColumnBcch.Name = "ColumnBcch";
            this.ColumnBcch.Width = 75;
            // 
            // ColumnBsic
            // 
            this.ColumnBsic.DataPropertyName = "Bsic";
            this.ColumnBsic.HeaderText = "BSIC";
            this.ColumnBsic.Name = "ColumnBsic";
            this.ColumnBsic.Width = 76;
            // 
            // ColumnMainCell
            // 
            this.ColumnMainCell.DataPropertyName = "MainCellName";
            this.ColumnMainCell.HeaderText = "第一近小区";
            this.ColumnMainCell.Name = "ColumnMainCell";
            this.ColumnMainCell.Width = 140;
            // 
            // ColumnMainLac
            // 
            this.ColumnMainLac.DataPropertyName = "MainLac";
            this.ColumnMainLac.HeaderText = "LAC";
            this.ColumnMainLac.Name = "ColumnMainLac";
            this.ColumnMainLac.Width = 75;
            // 
            // ColumnMainCi
            // 
            this.ColumnMainCi.DataPropertyName = "MainCi";
            this.ColumnMainCi.HeaderText = "CI";
            this.ColumnMainCi.Name = "ColumnMainCi";
            this.ColumnMainCi.Width = 76;
            // 
            // ColumnMainDistance
            // 
            this.ColumnMainDistance.DataPropertyName = "MainDistance";
            this.ColumnMainDistance.HeaderText = "与采样点距离";
            this.ColumnMainDistance.Name = "ColumnMainDistance";
            this.ColumnMainDistance.Width = 140;
            // 
            // ColumnMainLongitude
            // 
            this.ColumnMainLongitude.DataPropertyName = "MainCellLongitude";
            this.ColumnMainLongitude.HeaderText = "经度";
            this.ColumnMainLongitude.Name = "ColumnMainLongitude";
            // 
            // ColumnMainLatitude
            // 
            this.ColumnMainLatitude.DataPropertyName = "MainCellLatitude";
            this.ColumnMainLatitude.HeaderText = "纬度";
            this.ColumnMainLatitude.Name = "ColumnMainLatitude";
            // 
            // ColumnSecCellName
            // 
            this.ColumnSecCellName.DataPropertyName = "SecNearestCellName";
            this.ColumnSecCellName.HeaderText = "第二近小区";
            this.ColumnSecCellName.Name = "ColumnSecCellName";
            this.ColumnSecCellName.Width = 140;
            // 
            // ColumnSecLac
            // 
            this.ColumnSecLac.DataPropertyName = "SecLac";
            this.ColumnSecLac.HeaderText = "LAC";
            this.ColumnSecLac.Name = "ColumnSecLac";
            this.ColumnSecLac.Width = 75;
            // 
            // ColumnCi
            // 
            this.ColumnCi.DataPropertyName = "SecCi";
            this.ColumnCi.HeaderText = "CI";
            this.ColumnCi.Name = "ColumnCi";
            this.ColumnCi.Width = 75;
            // 
            // ColumnSecDistance
            // 
            this.ColumnSecDistance.DataPropertyName = "SecDistance";
            this.ColumnSecDistance.HeaderText = "与采样点距离";
            this.ColumnSecDistance.Name = "ColumnSecDistance";
            this.ColumnSecDistance.Width = 140;
            // 
            // ColumnSecLongitude
            // 
            this.ColumnSecLongitude.DataPropertyName = "SecLongitude";
            this.ColumnSecLongitude.HeaderText = "经度";
            this.ColumnSecLongitude.Name = "ColumnSecLongitude";
            // 
            // ColumnSecLatitude
            // 
            this.ColumnSecLatitude.DataPropertyName = "SecLatitude";
            this.ColumnSecLatitude.HeaderText = "纬度";
            this.ColumnSecLatitude.Name = "ColumnSecLatitude";
            // 
            // NoXNearestCellInfoForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(906, 414);
            this.Controls.Add(this.dataGridView);
            this.Name = "NoXNearestCellInfoForm";
            this.Text = "小区信息";
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.DataGridView dataGridView;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem tsmiExport2Xls;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnSn;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnDateTime;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnTpLongitude;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnTpLatitude;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnBcch;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnBsic;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnMainCell;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnMainLac;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnMainCi;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnMainDistance;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnMainLongitude;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnMainLatitude;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnSecCellName;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnSecLac;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnCi;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnSecDistance;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnSecLongitude;
        private System.Windows.Forms.DataGridViewTextBoxColumn ColumnSecLatitude;

    }
}