﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class IndoorStationAcceptHelper
    {
        public string BtsNameByFileName { get; private set; }
        public string PCIByFileName { get; private set; }

        public string JudgeValidFileName(string fileName)
        {
            BtsNameByFileName = GetBtsNameByFileName(fileName);
            if (string.IsNullOrEmpty(BtsNameByFileName))
            {
                return $"文件{fileName}未找到目标基站,文件名格式不对;";
            }

            PCIByFileName = GetPCIByFileName(fileName);
            if (string.IsNullOrEmpty(PCIByFileName))
            {
                return $"文件{fileName}未找到目标PCI;";
            }
            return "";
        }

        public virtual string GetBtsNameByFileName(string fileName)
        {
            string fileBtsName = "";
            string[] names = fileName.Split('_');
            if (names.Length > 4 && fileName.ToUpper().Contains("PCI"))
            {
                fileBtsName = names[3];
            }
            return fileBtsName;
        }

        public virtual string GetPCIByFileName(string fileName)
        {
            int index = fileName.ToUpper().IndexOf("PCI") + 3;
            int endIndex = -1;
            string PCI = "";
            for (int i = index; i < fileName.Length; i++)
            {
                char ch = fileName[i];
                if (ch > '9' || ch < '0')
                {
                    endIndex = i;
                    break;
                }
            }

            int length = -1;
            if (endIndex > index)
            {
                length = endIndex - index;
            }
            if (length > 0)
            {
                PCI = fileName.Substring(index, length);
            }
            return PCI;
        }
    }
}
