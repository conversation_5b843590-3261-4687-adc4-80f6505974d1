﻿using MapWinGIS;
using MasterCom.MTGis;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class MgrsCoveLayerBase : LayerBase
    {
        public MgrsCoveLayerBase()
            : base("重叠覆盖栅格")
        { 
        }

        private List<ScanMultiCoverageGridInfo> gisDataList = null;

        public void iniData(List<ScanMultiCoverageGridInfo> gisDataList)
        {
            this.gisDataList = gisDataList;
        }

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible)
            {
                return;
            }
            if (gisDataList != null && gisDataList.Count > 0)
            {
                DrawRectangle(graphics);
            }
        }

        protected virtual void DrawRectangle(Graphics graphics)
        {
            foreach (var gisData in gisDataList)
            {
                Color rectColor = gisData.GridColor;

                DbPoint dbP = new DbPoint(gisData.TLLng, gisData.TLLat);
                PointF pointLeft, pointRight;
                gisAdapter.ToDisplay(dbP, out pointLeft);
                dbP = new DbPoint(gisData.BRLng, gisData.BRLat);
                gisAdapter.ToDisplay(dbP, out pointRight);
                RectangleF rectF = new RectangleF(pointLeft.X, pointLeft.Y,
                        pointRight.X - pointLeft.X, pointRight.Y - pointLeft.Y);
                //==================0<=Alpha值<=255，越小越透明======================
                graphics.FillRectangle(new SolidBrush(Color.FromArgb(200, rectColor)), rectF);

            }
            graphics.ResetTransform();
        }

        protected virtual int MakeShpFile(string fileName, bool fixColor, Color colorSelected, bool compareBTS)
        {
            try
            {
                if (gisDataList == null)
                {
                    MessageBox.Show("没有可导出数据");
                    return -1;
                }

                Shapefile shpFile = new Shapefile();
                bool result = shpFile.CreateNewWithShapeID("", ShpfileType.SHP_POLYGON);
                if (!result)
                {
                    MessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                    return -1;
                }
                int idIdx = 0;
                int fiMGRTIndex = idIdx++;
                int fiLongitude = idIdx++;
                int fiLatitude = idIdx++;
                int fiRSRP = idIdx++;
                int fiColor = idIdx;
                ShapeHelper.InsertNewField(shpFile, "MGRTIndex", FieldType.STRING_FIELD, 10, 30, ref fiMGRTIndex);
                ShapeHelper.InsertNewField(shpFile, "Longitude", FieldType.DOUBLE_FIELD, 10, 30, ref fiLongitude);
                ShapeHelper.InsertNewField(shpFile, "Latitude", FieldType.DOUBLE_FIELD, 10, 30, ref fiLatitude);
                ShapeHelper.InsertNewField(shpFile, "RSRP", FieldType.DOUBLE_FIELD, 10, 30, ref fiRSRP);
                ShapeHelper.InsertNewField(shpFile, "Color", FieldType.INTEGER_FIELD, 10, 30, ref fiColor);

                int numShp = 0;
                foreach (var data in gisDataList)
                {
                    numShp++;
                    shpFile.EditInsertShape(ShapeHelper.CreateRectShape(data.TLLng, data.TLLat, data.BRLng, data.BRLat), ref numShp);
                    shpFile.EditCellValue(fiMGRTIndex, numShp, data.MgrsString);
                    shpFile.EditCellValue(fiLongitude, numShp, data.CentLng);
                    shpFile.EditCellValue(fiLatitude, numShp, data.CentLat);
                    shpFile.EditCellValue(fiRSRP, numShp, data.CoverageCellList.Count > 0 ? data.CoverageCellList[0].Rsrp.Avg : 0);
                    shpFile.EditCellValue(fiColor, numShp, ColorTranslator.ToOle(data.GridColor));
                }
                ShapeHelper.DeleteShpFile(fileName);
                if (!shpFile.SaveAs(fileName, null))
                {
                    MessageBox.Show("保存文件失败！" + shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                    shpFile.Close();
                    return -1;
                }
                shpFile.Close();
                return 1;
            }
            catch (Exception ex)
            {
                MessageBox.Show("保存文件失败！" + ex.Message);
                return -1;
            }
        }
    }
}
