﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Collections;
using MasterCom.Util;

namespace MasterCom.RAMS.Model.RoadProtection
{
    public class RoadWarningEntity
    {
        public string 区域 { get; set; } = "";
        public string 严重程度 { get; set; } = "";
        public DateTime 首次发生时间 { get; set; } = new DateTime();
        public DateTime 最后发生时间 { get; set; } = new DateTime();
        public string 所属网格 { get; set; } = "";
        public string 位置信息 { get; set; } = "";
        public string 事件类型 { get; set; } = "";
        public ArrayList 占用小区array { get; set; } = new ArrayList();
        public string 占用小区 { get; set; } = "";
        public string 问题类型 { get; set; } = "";
        public string 原因描述 { get; set; } = "";
        public string 建议方案 { get; set; } = "";
        public string Istatnum { get; set; } = "";
        public string 权值 { get; set; } = "";
        public string 事件总数 { get; set; } = "";
        public string 问题天数 { get; set; } = "";
        public DateTime 发生时间 { get; set; } = new DateTime();
        public string LAC { get; set; } = "";
        public string CI { get; set; } = "";
        public double 经度 { get; set; } 
        public double 纬度 { get; set; } 
        public string Ifileid { get; set; } 
        public string Strfilename { get; set; } 
        public int Iprojecttype { get; set; }
        public int Iservicetype { get; set; }
        public int Ieventid { get; set; }
        public int Istatus { get; set; }
        public int Idistance { get; set; }

        private List<RoadWarningEntity> roadWarningEntityList = new List<RoadWarningEntity>();
        public List<RoadWarningEntity> getroadWarningEntityList()
        {
            return roadWarningEntityList;
        }

        public void setroadWarningEntityList(RoadWarningEntity roadwarning)
        {
            roadWarningEntityList.Add(roadwarning);
        }

        public void setroadWarningEntity(List<RoadWarningEntity> roadwarninglist)
        {
            roadWarningEntityList = roadwarninglist;
        }

        public Event ConvertToEvent()
        {
            Event e = new Event();
            e.Time = (int)(JavaDate.GetMilliseconds(发生时间) / 1000);

            DTDataHeader header = new DTDataHeader();
            header.ID = int.Parse(Ifileid);
            header.Name = Strfilename;
            header.ProjectID = Iprojecttype;
            header.ServiceType = Iservicetype;
            header.LogTable = string.Format("tb_log_file_{0}_{1:D2}", e.DateTime.Year, e.DateTime.Month);
            header.SampleTbName = string.Format("tb_dtgsm_sample6_{0}", e.DateTime.ToString("yyMMdd"));
            e.ApplyHeader(header);

            e.Longitude = 经度;
            e.Latitude = 纬度;
            e.ID = Ieventid;
            e["LAC"] = int.Parse(LAC);
            e["CI"] = int.Parse(CI);

            return e;
        }
    }

    public class RoadWarningManager
    {
        public string 区域 { get; set; } = "";
        public string 严重程度 { get; set; } = "";
        public DateTime 首次发生时间 { get; set; } = new DateTime();
        public DateTime 最后发生时间 { get; set; } = new DateTime();
        public string 所属网格 { get; set; } = "";
        public string 位置信息 { get; set; } = "";
        public string Istatnum { get; set; } = "";
        public string 权值 { get; set; } = "";
        public string 事件总数 { get; set; } = "";
        public string 问题天数 { get; set; } = "";
        public DateTime 发生时间 { get; set; } = new DateTime();
        public int Ieventid { get; set; }
        public int Istatus { get; set; }
        public int Idistance { get; set; }
        public List<RoadWarningEntity> RoadWarningEntityList { get; set; } = new List<RoadWarningEntity>();
    }
}
