<?xml version="1.0"?>
<Configs>
  <Config name="ExportCfg">
    <Item name="Templates" typeName="IList">
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">lte</Item>
        <Item typeName="IList" key="Columns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">SCell_Name</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">SCell_Name</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">EARFCN</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">EARFCN</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">PCI</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PCI</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">RSRP</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">SINR</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">SINR</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">APP_Speed_Mb</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">APP_Speed_Mb</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">Transmission_Mode</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">Transmission_Mode</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">Percent_QAM64_DLCode0</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">Percent_QAM64_DLCode0</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">Percent_QAM64_DLCode1</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">Percent_QAM64_DLCode1</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">MCSCode0_DL</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCSCode0_DL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">0</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">MCSCode1_DL</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">MCSCode1_DL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">0</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">Rank_Indicator</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">Rank_Indicator</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">PDSCH_RB_Number</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PDSCH_RB_Number</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">PDSCH_BLER</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PDSCH_BLER</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">PDCCH_DL_Grant_Count</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PDCCH_DL_Grant_Count</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">Ratio_DL_Code0_HARQ_ACK</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">Ratio_DL_Code0_HARQ_ACK</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">Ratio_DL_Code0_HARQ_NACK</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">Ratio_DL_Code0_HARQ_NACK</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">Ratio_DL_Code1_HARQ_ACK</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">Ratio_DL_Code1_HARQ_ACK</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">Ratio_DL_Code1_HARQ_NACK</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">Ratio_DL_Code1_HARQ_NACK</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">TAC</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">TAC</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">SCell_LAC</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">SCell_LAC</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">ECI</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">ECI</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">PDSCH_RSRP</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PDCCH_RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">PDSCH_SINR</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PDCCH_SINR</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">SCell_Distance</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">SCell_Distance</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">APP_ThroughputDL_Mb</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">APP_ThroughputDL_Mb</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">PDSCH_Code0_BLER</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PDSCH_Code0_BLER</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">PDSCH_Code1_BLER</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PDSCH_Code1_BLER</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">PDSCH_PRb_Num_s</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PDSCH_PRb_Num_s</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">Percent_MCS_DLCode0</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">Percent_MCS_DLCode0</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">Percent_MCS_DLCode1</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">Percent_MCS_DLCode1</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">Percent_QAM64_UL</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">Percent_QAM64_UL</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">lte scan</Item>
        <Item typeName="IList" key="Columns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">FileName</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">FileName</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">rsrp 0</Item>
            <Item typeName="String" key="SysName">LTE_SCAN</Item>
            <Item typeName="String" key="ParamName">TopN_CELL_Specific_RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">0</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">rsrp 1</Item>
            <Item typeName="String" key="SysName">LTE_SCAN</Item>
            <Item typeName="String" key="ParamName">TopN_CELL_Specific_RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">rsrp 2</Item>
            <Item typeName="String" key="SysName">LTE_SCAN</Item>
            <Item typeName="String" key="ParamName">TopN_CELL_Specific_RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">2</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">rsrp 3</Item>
            <Item typeName="String" key="SysName">LTE_SCAN</Item>
            <Item typeName="String" key="ParamName">TopN_CELL_Specific_RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">3</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">rsrp 4</Item>
            <Item typeName="String" key="SysName">LTE_SCAN</Item>
            <Item typeName="String" key="ParamName">TopN_CELL_Specific_RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">4</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">rsrp 5</Item>
            <Item typeName="String" key="SysName">LTE_SCAN</Item>
            <Item typeName="String" key="ParamName">TopN_CELL_Specific_RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">5</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">GSM</Item>
        <Item typeName="IList" key="Columns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">LAC</Item>
            <Item typeName="String" key="SysName">GSM</Item>
            <Item typeName="String" key="ParamName">LAC</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">CI</Item>
            <Item typeName="String" key="SysName">GSM</Item>
            <Item typeName="String" key="ParamName">CI</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">RxLev</Item>
            <Item typeName="String" key="SysName">GSM</Item>
            <Item typeName="String" key="ParamName">RxLevSub</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">Longitude</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Longitude</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">Latitude</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Latitude</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">VoLTE</Item>
        <Item typeName="IList" key="Columns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">FileName</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">FileName</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">DistrictID</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">DistrictID</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">FileType</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">FileType</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">PESQL</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PESQLQ</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">True</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">5</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">LTE弱覆盖采样点</Item>
        <Item typeName="IList" key="Columns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">日期</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Time</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">经度</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Longitude</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">纬度</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Latitude</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">小区名称</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">SCell_Name</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">RSRP</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">RSRP</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">True</Item>
            <Item typeName="Double" key="MinValue">-141</Item>
            <Item typeName="Double" key="MaxValue">-105</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">True</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">RSRQ</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">RSRQ</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">True</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">SINR</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">SINR</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">True</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">TAC</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">TAC</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">ECI</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">ECI</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">PCI</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">PCI</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="Name">LTE简单</Item>
        <Item typeName="IList" key="Columns">
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">Time</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Time</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">Latitude</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Latitude</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">longitude</Item>
            <Item typeName="String" key="SysName">Common Param</Item>
            <Item typeName="String" key="ParamName">Longitude</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">RTP-number</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">RTP_Sequence_Number</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="Caption">RTP-Direction</Item>
            <Item typeName="String" key="SysName">LTE_TDD</Item>
            <Item typeName="String" key="ParamName">RTP_Direction</Item>
            <Item typeName="Int32" key="ParamArrayIndex">-1</Item>
            <Item typeName="Boolean" key="CheckValue">False</Item>
            <Item typeName="Double" key="MinValue">0</Item>
            <Item typeName="Double" key="MaxValue">0</Item>
            <Item typeName="Boolean" key="IsIgnoreWhenNotInRange">False</Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>