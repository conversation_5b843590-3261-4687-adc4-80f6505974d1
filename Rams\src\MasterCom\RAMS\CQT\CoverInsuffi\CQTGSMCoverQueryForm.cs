﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTGSMCoverQueryForm : DevExpress.XtraEditors.XtraForm
    {
        public CQTGSMCoverQueryForm()
        {
            InitializeComponent();
        }

        public void getSelect(out int numfugai, out  double num90, out double num80)
        {
            numfugai = Convert.ToInt32(spinEdit1.Value);
            num90 = double.Parse(spinEdit2.Value.ToString());
            num80 = double.Parse(spinEdit3.Value.ToString());
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleButton2_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

       
    }
}