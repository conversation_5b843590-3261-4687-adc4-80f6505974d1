﻿using DevExpress.XtraPrinting.Export.Pdf;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRSameCarTestResult
    {
        public NRSameCarTestCellResult ResultHost { get; protected set; }
        public NRSameCarTestCellResult ResultGuest { get; protected set; }
        public List<NRSameCarTestCellResult> OccupyCellResults { get; protected set; }
        public TestPoint PrevPnt { get; set; }
        public string Name { get; protected set; }
        //public bool IsCheckDlAndUl { get; set; }
        public NRCellOccupySameCarTestType Type { get; set; }

        public NRSameCarTestResult(FileInfo fileHost, List<FileInfo> fileGuest, NRCellOccupySameCarTestType type)
        {
            ResultHost = new NRSameCarTestCellResult(new List<FileInfo> { fileHost }, true);
            ResultGuest = new NRSameCarTestCellResult(fileGuest, false);
            Type = type;
        }

        public void DealPoint(TestPoint tp, bool isHost, List<TestPoint> lstEndPnts)
        {
            double duration = calcDuration(tp, PrevPnt);

            int dataStatus = getDataStatus(tp, isHost);

            bool idle = isIdle(tp, dataStatus, isHost);//是否空闲

            NRSameCarTestCellResult curResult = getOccupyResult(isHost);//获取当前采样点所在的文件结果对象
            NRSameCarTestCellResult oppositeresult = getOccupyResult(!isHost);

            curResult.SetIdleFlag(idle);

            if (curResult.PrevPnt != null && oppositeresult.PrevPnt != null)
            {
                int? freq = null;
                ICell cell = getSrcCell(tp, ref freq);
                curResult.SetPrevCell(cell, freq);

                bool isSameCell = (curResult.PrevCell != null && oppositeresult.PrevCell != null &&
                        curResult.PrevCell.Name == oppositeresult.PrevCell.Name);//是否占用同一小区

                addTime(curResult, oppositeresult, duration, isSameCell);
            }
            curResult.CalcTestDuration(tp);

            curResult.SetPrevPnt(tp);

            PrevPnt = tp;

            if (isEnd(tp, lstEndPnts))
                curResult.SetEnd();
        }

        private bool isEnd(TestPoint tp, List<TestPoint> lstEndPnts)
        {
            foreach (TestPoint endPnt in lstEndPnts)
            {
                if (tp.FileID == endPnt.FileID &&
                    tp.lTimeWithMillsecond == endPnt.lTimeWithMillsecond)
                {
                    return true;
                }
            }

            return false;
        }

        private NRSameCarTestCellResult getOccupyResult(bool isHost)
        {
            if (isHost)
            {
                return ResultHost;
            }

            return ResultGuest;
        }

        private int getDataStatus(TestPoint tp, bool isHost)
        {
            if (Type == NRCellOccupySameCarTestType.DlAndUl)
            {
                if (isHost)
                {
                    return getDLDataStatus(tp);
                }
                else
                {
                    return getULDataStatus(tp);
                }
            }
            else
            {
                if (isHost)
                {
                    return 0;
                }
                else
                {
                    return getDLDataStatus(tp);
                }
            }
        }

        private static int getULDataStatus(TestPoint tp)
        {
            short? status = (short?)tp["NR_APP_DataStatus_UL"];
            if (status == null)
            {
                return 0;
            }
            return (int)status;
        }

        private static int getDLDataStatus(TestPoint tp)
        {
            short? status = (short?)tp["NR_APP_DataStatus_DL"];
            if (status == null)
            {
                return 0;
            }
            return (int)status;
        }

        private double calcDuration(TestPoint curTp, TestPoint lastTp)
        {
            if (lastTp == null)
                return 0;

            return curTp.lTimeWithMillsecond - lastTp.lTimeWithMillsecond;
        }

        private bool isIdle(TestPoint tp, int dataStatus, bool isHost)
        {
            if (Type == NRCellOccupySameCarTestType.DlAndUl)
            {
                short? appType = NRTpHelper.NrTpManager.GetAppType(tp);
                if (appType == null)
                {
                    return true;
                }

                bool isIdle = isHost ? isDownloadIdle((short)appType) : isUploadIdle((short)appType);
                return isIdle || dataStatus <= 0;
            }
            else
            {
                return judgeModeStatus(tp, dataStatus, isHost);
            }
        }

        private bool judgeModeStatus(TestPoint tp, int dataStatus, bool isHost)
        {
            if (isHost)
            {
                short? mode = (short?)tp["mode"];
                if (mode == (int)TestPoint.EMODE.MODE_NR_ENDC_ACTIVE || mode == (int)TestPoint.EMODE.LTE_DEDICATED)//Volte语音文件的通话态即为业务态，否则为空闲态
                {
                    return false;
                }
                else
                {
                    return true;
                }
            }
            else
            {
                short? appType = NRTpHelper.NrTpManager.GetAppType(tp);
                if (appType == null)
                {
                    return true;
                }

                bool isIdle = isDownloadIdle((short)appType);

                return isIdle || dataStatus <= 0;
            }
        }

        private bool isDownloadIdle(short appType)
        {
            return appType != (int)AppType.FTP_Download && appType != (int)AppType.Http_Download
                && appType != (int)AppType.Http_Video && appType != (int)AppType.Http;
        }
        private bool isUploadIdle(short appType)
        {
            return appType != (int)AppType.FTP_Upload;
        }

        private void addTime(NRSameCarTestCellResult curResult, NRSameCarTestCellResult oppositeResult
            , double duration, bool isSameCell)
        {
            if (curResult.IsOccupy && oppositeResult.IsOccupy)//均业务态
            {
                if (isSameCell)
                {
                    curResult.AddSameCellOccupy(duration);
                    oppositeResult.AddSameCellOccupy(duration);
                }
                else
                {
                    if (curResult.Freq != null && oppositeResult.Freq != null && curResult.Freq == oppositeResult.Freq)
                    {
                        curResult.AddDiffCellCoFreqOccupy(duration);
                        oppositeResult.AddDiffCellCoFreqOccupy(duration);
                    }
                    else
                    {
                        curResult.AddDiffCellDiFreqOccupy(duration);
                        oppositeResult.AddDiffCellDiFreqOccupy(duration);
                    }
                }
            }
            else if (curResult.IsIdle && oppositeResult.IsIdle)//均空闲
            {
                curResult.AddIdle(duration, isSameCell);
                oppositeResult.AddIdle(duration, isSameCell);
            }
            else
            {
                //判断主端NRSameCarTestCellResult是否处于业务态
                bool isHostOccupy = curResult.IsHost ? curResult.IsOccupy : oppositeResult.IsOccupy;

                curResult.AddSingleOccupy(duration, isHostOccupy, isSameCell);
                oppositeResult.AddSingleOccupy(duration, isHostOccupy, isSameCell);
            }
        }

        private ICell getSrcCell(TestPoint tp, ref int? freq)
        {
            NRCell cell = tp.GetMainCell_NR();
            if (cell != null)
            {
                freq = cell.SSBARFCN;
                return cell;
            }

            freq = (int?)NRTpHelper.NrTpManager.GetEARFCN(tp);
            return tp.GetMainCell();
        }

        public virtual void Calculate()
        {
            ResultHost.Calculate();
            ResultGuest.Calculate();

            setOccupyCellResults();
            setName();
        }

        protected virtual void setName()
        {
            if (Type == NRCellOccupySameCarTestType.DlAndUl)
            {
                Name = string.Format("下载：{0} 上传：{1}", ResultHost.Name, ResultGuest.Name);
            }
            else
            {
                Name = string.Format("VoLTE语音：{0} 下载：{1}", ResultHost.Name, ResultGuest.Name);
            }
        }

        protected virtual void setOccupyCellResults()
        {
            OccupyCellResults = new List<NRSameCarTestCellResult>();
            OccupyCellResults.Add(ResultHost);
            OccupyCellResults.Add(ResultGuest);
        }
    }

    public class NRSameCarTestCellResult
    {
        public List<FileInfo> FileList { get; private set; }
        public virtual string ID { get; private set; } = "";
        public virtual string Name { get; private set; } = "";
        public int? Freq { get; set; }
        public bool IsHost { get; set; } = false;
        public ICell PrevCell { get; private set; }
        public TestPoint PrevPnt { get; private set; }

        //public bool IsIdle { get; private set; } = true;
        //public bool IsOccupy { get; private set; } = false;

        private bool isIdle;
        public bool IsIdle
        {
            get { return isIdle && PrevPnt != null; }
        }

        public bool IsOccupy
        {
            get { return !isIdle && PrevPnt != null; }
        }

        private double test_Duration = 0;
        public double TestDuration { get; private set; }

        private double bothIdle_SameCell_Duration = 0;//均空闲且占同一小区的时间
        public double BothIdle_SameCell_Percent { get; private set; }

        private double bothIdle_DiffCell_Duration = 0;//均空闲且占不同小区的时间
        public double BothIdle_DiffCell_Percent { get; private set; }

        private double hostIdleGuestOccupy_SameCell_Duration = 0;//主端空闲，对端业务态，占同一小区的时间
        public double HostIdleGuestOccupy_SameCell_Percent { get; private set; }

        private double hostIdleGuestOccupy_DiffCell_Duration = 0;//主端空闲，对端业务态，占不同小区的时间
        public double HostIdleGuestOccupy_DiffCell_Percent { get; private set; }

        private double hostOccupyGuestIdle_SameCell_Duration = 0;//主端业务态，对端空闲，占同一小区的时间
        public double HostOccupyGuestIdle_SameCell_Percent { get; private set; }

        private double hostOccupyGuestIdle_DiffCell_Duration = 0;//主端业务态，对端空闲，占同一小区的时间
        public double HostOccupyGuestIdle_DiffCell_Percent { get; private set; }

        private double sameCellOccupy_Duration = 0;//均业务态且占同一小区的时间
        public double SameCellOccupyPercent { get; private set; }

        private double diffCellDiFreqOccupy_Duration = 0;//均业务态，占用异频不同小区的时间
        public double DiffCellDiFreqOccupyPercent { get; private set; }

        private double diffCellCoFreqOccupy_Duration = 0;//均业务态，占用同频不同小区的时间
        public double DiffCellCoFreqOccupyPercent { get; private set; }

        public NRSameCarTestCellResult(List<FileInfo> fileList, bool isHost)
        {
            FileList = fileList;
            this.IsHost = isHost;
        }

        public void SetIdleFlag(bool isIdle)
        {
            this.isIdle = isIdle;
        }

        public void SetPrevCell(ICell cell, int? freq)
        {
            this.PrevCell = cell;
            this.Freq = freq;
        }

        public void SetPrevPnt(TestPoint tp)
        {
            this.PrevPnt = tp;
        }

        public void SetEnd()
        {
            PrevCell = null;
            PrevPnt = null;
        }

        public void CalcTestDuration(TestPoint tp)
        {
            this.test_Duration += calcDuration(tp, PrevPnt);
        }

        public void AddIdle(double duration, bool isSameCell)
        {
            if (isSameCell)
            {
                this.bothIdle_SameCell_Duration += duration;
            }
            else
            {
                this.bothIdle_DiffCell_Duration += duration;
            }
        }

        public void AddSingleOccupy(double duration, bool hostOccupy, bool isSameCell)
        {
            if (hostOccupy)
            {
                addHostOccupyOnly(duration, isSameCell);
            }
            else
            {
                addGuestOccupyOnly(duration, isSameCell);
            }
        }

        private void addHostOccupyOnly(double duration, bool isSameCell)
        {
            if (isSameCell)
            {
                this.hostOccupyGuestIdle_SameCell_Duration += duration;
            }
            else
            {
                this.hostOccupyGuestIdle_DiffCell_Duration += duration;
            }
        }

        private void addGuestOccupyOnly(double duration, bool isSameCell)
        {
            if (isSameCell)
            {
                this.hostIdleGuestOccupy_SameCell_Duration += duration;
            }
            else
            {
                this.hostIdleGuestOccupy_DiffCell_Duration += duration;
            }
        }

        public void AddSameCellOccupy(double duration)
        {
            this.sameCellOccupy_Duration += duration;
        }

        public void AddDiffCellDiFreqOccupy(double duration)
        {
            this.diffCellDiFreqOccupy_Duration += duration;
        }
        public void AddDiffCellCoFreqOccupy(double duration)
        {
            this.diffCellCoFreqOccupy_Duration += duration;
        }

        private double calcDuration(TestPoint curTp, TestPoint lastTp)
        {
            if (lastTp == null)
                return 0;

            return curTp.lTimeWithMillsecond - lastTp.lTimeWithMillsecond;
        }

        public virtual void Merge(NRSameCarTestCellResult cellResult)
        {
            this.test_Duration += cellResult.test_Duration;
            this.bothIdle_SameCell_Duration += cellResult.bothIdle_SameCell_Duration;
            this.bothIdle_DiffCell_Duration += cellResult.bothIdle_DiffCell_Duration;
            this.hostIdleGuestOccupy_SameCell_Duration += cellResult.hostIdleGuestOccupy_SameCell_Duration;
            this.hostIdleGuestOccupy_DiffCell_Duration += cellResult.hostIdleGuestOccupy_DiffCell_Duration;
            this.hostOccupyGuestIdle_SameCell_Duration += cellResult.hostOccupyGuestIdle_SameCell_Duration;
            this.hostOccupyGuestIdle_DiffCell_Duration += cellResult.hostOccupyGuestIdle_DiffCell_Duration;
            this.sameCellOccupy_Duration += cellResult.sameCellOccupy_Duration;
            this.diffCellDiFreqOccupy_Duration += cellResult.diffCellDiFreqOccupy_Duration;
            this.diffCellCoFreqOccupy_Duration += cellResult.diffCellCoFreqOccupy_Duration;
        }

        public void Calculate()
        {
            if (FileList != null)
            {
                StringBuilder strID = new StringBuilder();
                StringBuilder strName = new StringBuilder();
                foreach (FileInfo file in FileList)
                {
                    strID.Append(file.ID + ",");
                    strName.Append(file.Name + ",");
                }
                if (strID.Length > 0)
                {
                    strID.Remove(strID.Length - 1, 1);
                    strName.Remove(strName.Length - 1, 1);
                }
                ID = strID.ToString();
                Name = strName.ToString();
            }

            if (test_Duration > 0)
            {
                TestDuration = Math.Round(test_Duration / 1000, 2);
                BothIdle_SameCell_Percent = Math.Round(100 * bothIdle_SameCell_Duration / test_Duration, 2);
                BothIdle_DiffCell_Percent = Math.Round(100 * bothIdle_DiffCell_Duration / test_Duration, 2);
                HostIdleGuestOccupy_SameCell_Percent = Math.Round(100 * hostIdleGuestOccupy_SameCell_Duration / test_Duration, 2);
                HostIdleGuestOccupy_DiffCell_Percent = Math.Round(100 * hostIdleGuestOccupy_DiffCell_Duration / test_Duration, 2);
                HostOccupyGuestIdle_SameCell_Percent = Math.Round(100 * hostOccupyGuestIdle_SameCell_Duration / test_Duration, 2);
                HostOccupyGuestIdle_DiffCell_Percent = Math.Round(100 * hostOccupyGuestIdle_DiffCell_Duration / test_Duration, 2);
                SameCellOccupyPercent = Math.Round(100 * sameCellOccupy_Duration / test_Duration, 2);
                DiffCellDiFreqOccupyPercent = Math.Round(100 * diffCellDiFreqOccupy_Duration / test_Duration, 2);
                DiffCellCoFreqOccupyPercent = Math.Round(100 * diffCellCoFreqOccupy_Duration / test_Duration, 2);
            }
        }
    }

    public class NRSameCarTestResSummary : NRSameCarTestResult
    {
        public NRSameCarTestResSummary(NRCellOccupySameCarTestType type)
            : base(null, null, type)
        {
            ResultHost = new NRSameCarTestCellResSummary();
        }

        protected override void setOccupyCellResults()
        {
            OccupyCellResults = new List<NRSameCarTestCellResult>();
            OccupyCellResults.Add(ResultHost);
        }

        protected override void setName()
        {
            Name = "文件汇总";
        }

        public void Merge(NRSameCarTestResult result)
        {
            ResultHost.Merge(result.ResultHost);
            ResultHost.Merge(result.ResultGuest);
        }
    }

    public class NRSameCarTestCellResSummary : NRSameCarTestCellResult
    {
        public NRSameCarTestCellResSummary()
            : base(null, true) { }

        public override string ID
        {
            get { return ""; }
        }

        public override string Name
        {
            get { return "汇总"; }
        }
    }
}
