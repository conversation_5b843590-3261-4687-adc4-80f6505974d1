﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LastRoadReportCustomForm : BaseForm
    {
        public LastRoadReportCustomForm():base()
        {
            InitializeComponent();
            initCbx();
            this.splitContainerControl1.Panel2.Enabled = false;
        }

        public LastRoadReportCustomForm(LastRoadReport selRpt)
            : this()
        {
            fillReportList(selRpt);
        }

        public LastRoadReport SelectedReport
        {
            get { return listAllReport.SelectedItem as LastRoadReport; }
        }

        private void initCbx()
        {
            txtDisSerial.Text = "";

            cbxLogicalType.Properties.Items.Clear();
            foreach (string item in Enum.GetNames(typeof(ELogicalType)))
            {
                cbxLogicalType.Properties.Items.Add(item);
            }
            cbxLogicalType.SelectedIndex = 0;

            cbxSys.Properties.Items.Clear();
            cbxSysDisplay.Properties.Items.Clear();
            cbxParam.Properties.Items.Clear();
            cbxParamDisplay.Properties.Items.Clear();
            cbxParamIdx.Properties.Items.Clear();
            cbxParamIdxDisplay.Properties.Items.Clear();
            cbxSys.Properties.SelectedIndexChanged -= sysCbx_SelectedIndexChanged;
            cbxSysDisplay.Properties.SelectedIndexChanged -= sysCbx_SelectedIndexChanged;
            foreach (DTDisplayParameterSystem system in DTDisplayParameterManager.GetInstance().Systems)
            {
                cbxSys.Properties.Items.Add(system);
                cbxSysDisplay.Properties.Items.Add(system);
            }
            cbxSys.Properties.SelectedIndexChanged += sysCbx_SelectedIndexChanged;
            cbxSysDisplay.Properties.SelectedIndexChanged += sysCbx_SelectedIndexChanged;
        }

        private TestPointValueCondition curValueCondition = null;
        private TestPointDisplayColumn curDisplayCol = null;
        void sysCbx_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (sender == cbxSys)
            {
                curValueCondition = null;
                fillCbxParams(cbxParam, cbxSys.SelectedItem);
            }
            else if (sender == cbxSysDisplay)
            {
                curDisplayCol = null;
                fillCbxParams(cbxParamDisplay, cbxSysDisplay.SelectedItem);
            }
        }

        private void fillCbxParams(ComboBoxEdit cbx, object sys)
        {
            cbx.Properties.SelectedIndexChanged -= paramCbx_SelectedIndexChanged;
            cbx.Properties.Items.Clear();
            DTDisplayParameterSystem paramSys = sys as DTDisplayParameterSystem;
            if (paramSys == null)
            {
                return;
            }
            foreach (DTDisplayParameterInfo param in paramSys.DisplayParamInfos)
            {
                cbx.Properties.Items.Add(param);
            }
            if (cbx.Properties.Items.Count > 0)
            {
                cbx.SelectedIndex = 0;
            }
            cbx.Properties.SelectedIndexChanged += paramCbx_SelectedIndexChanged;
        }

        private void paramCbx_SelectedIndexChanged(object sender, EventArgs e)
        {
            DTDisplayParameterInfo disParam = ((ComboBoxEdit)sender).SelectedItem as DTDisplayParameterInfo;
            if (disParam == null)
            {
                return;
            }
            if (sender == cbxParam)
            {
                curValueCondition = null;
                btnAddCondition.Enabled = true;
                numMinValue.ValueChanged -= numMinValue_ValueChanged;
                numMaxValue.ValueChanged -= numMaxValue_ValueChanged;
                numMinValue.Enabled = numMaxValue.Enabled = true;
                numMinValue.Properties.MinValue = numMaxValue.Properties.MinValue = (decimal)disParam.ValueMin;
                numMinValue.Properties.MaxValue = numMaxValue.Properties.MaxValue = (decimal)disParam.ValueMax;
                numMinValue.Value = numMinValue.Properties.MinValue;
                numMaxValue.Value = numMaxValue.Properties.MaxValue;
                numMinValue.ValueChanged += numMinValue_ValueChanged;
                numMaxValue.ValueChanged += numMaxValue_ValueChanged;
                fillCbxParamIdx(cbxParamIdx, disParam);
            }
            else if (sender == cbxParamDisplay)
            {
                curDisplayCol = null;
                btnAddDisCol.Enabled = true;
                radioShowValueType.Enabled = true;
                fillCbxParamIdx(cbxParamIdxDisplay, disParam);
            }
        }

        void numMaxValue_ValueChanged(object sender, EventArgs e)
        {
            if (curValueCondition != null)
            {
                changeConditionMinMaxValue(false, numMaxValue.Value);
            }
        }

        void numMinValue_ValueChanged(object sender, EventArgs e)
        {
            if (curValueCondition != null)
            {
                changeConditionMinMaxValue(true,numMinValue.Value);
            }
        }

        private void changeConditionMinMaxValue(bool isMinValue, decimal value)
        {
            if (numMinValue.Value > numMaxValue.Value)
            {
                return;
            }
            if (isMinValue)
            {
                curValueCondition.MinValue = (double)value;
            }
            else
            {
                curValueCondition.MaxValue = (double)value;
            }
            listCondition.Invalidate();
        }

        private void fillCbxParamIdx(ComboBoxEdit cbx, DTDisplayParameterInfo param)
        {
            cbx.Properties.Items.Clear();
            if (param != null)
            {
                if (param.IsArray)
                {
                    cbx.Enabled = true;
                    for (int i = 0; i < param.ArrayBounds; i++)
                    {
                        cbx.Properties.Items.Add(i);
                    }
                    cbx.SelectedIndex = 0;
                }
                else
                {
                    cbx.Enabled = false;
                }
                makeCaption();
            }
        }

        public void makeCaption()
        {
            DTDisplayParameterInfo param = cbxParamDisplay.SelectedItem as DTDisplayParameterInfo;
            if (param==null)
            {
                return;
            }
            string caption = TestPointDisplayColumn.MakeShortCaption(param, cbxParamIdxDisplay.Enabled ? (int)cbxParamIdxDisplay.SelectedItem : -1);
            if (radioShowValueType.SelectedIndex>-1)
            {
                caption += "_" + radioShowValueType.Properties.Items[radioShowValueType.SelectedIndex].Description;
            }
            txtCaption.Text = caption;
        }

        private void fillReportList(LastRoadReport selRpt)
        {
            listAllReport.SelectedIndexChanged -= listAllReport_SelectedIndexChanged;
            listAllReport.Items.Clear();
            listAllReport.SelectedIndexChanged += listAllReport_SelectedIndexChanged;
            foreach (LastRoadReport rpt in LastRoadReportManager.GetInstance().Reports)
            {
                listAllReport.Items.Add(rpt);
            }
            if (listAllReport.Items.Count > 0)
            {
                if (selRpt != null)
                {
                    listAllReport.SelectedItem = selRpt;
                }
                else
                {
                    listAllReport.SelectedIndex = 0;
                }
            }
        }

        LastRoadReport curReport = null;
        void listAllReport_SelectedIndexChanged(object sender, EventArgs e)
        {
            curReport = listAllReport.SelectedItem as LastRoadReport;
            btnRemoveReport.Enabled = this.splitContainerControl1.Panel2.Enabled = curReport != null;
            if (curReport != null)
            {
                numMinLastDis.Value = (decimal)curReport.Condition.MinLastDistance;
                num2TPMaxDis.Value = (decimal)curReport.Condition.Max2TesetPointDis;
                cbxLogicalType.SelectedItem = curReport.Condition.LogicalType.ToString();
                txtDisSerial.Text = curReport.GISDisplaySerialName;
                fillConditionView(curReport,null);
                fillDisplayColumnView(curReport,null);
            }
        }

        private void fillConditionView(LastRoadReport rpt, TestPointValueCondition selCnd)
        {
            listCondition.SelectedIndexChanged -= listCondition_SelectedIndexChanged;
            listCondition.Items.Clear();
            btnRemoveCondition.Enabled = false;
            foreach (TestPointValueCondition cnd in rpt.Condition.DetailItems)
            {
                listCondition.Items.Add(cnd);
            }
            listCondition.SelectedIndexChanged += listCondition_SelectedIndexChanged;
            if (listCondition.Items.Count > 0)
            {
                if (selCnd != null)
                {
                    listCondition.SelectedItem = selCnd;
                }
                else
                {
                    listCondition.SelectedIndex = 0;
                }
            }
        }

        void listCondition_SelectedIndexChanged(object sender, EventArgs e)
        {
            curValueCondition = null;
            TestPointValueCondition condition = listCondition.SelectedItem as TestPointValueCondition;
            if (condition == null)
            {
                btnRemoveCondition.Enabled = false;
                return;
            }
            btnRemoveCondition.Enabled = true;
            cbxSys.SelectedItem = DTDisplayParameterManager.GetInstance()[condition.SysName];
            cbxParam.SelectedItem = DTDisplayParameterManager.GetInstance()[condition.SysName][condition.ParamName];
            cbxParamIdx.SelectedItem = condition.ParamArrayIndex;
            numMinValue.Properties.MinValue = numMaxValue.Properties.MinValue = (decimal)DTDisplayParameterManager.GetInstance()[condition.SysName][condition.ParamName].ValueMin;
            numMinValue.Properties.MaxValue = numMaxValue.Properties.MaxValue = (decimal)DTDisplayParameterManager.GetInstance()[condition.SysName][condition.ParamName].ValueMax;
            numMinValue.Value = (decimal)condition.MinValue;
            numMaxValue.Value = (decimal)condition.MaxValue;
            curValueCondition = condition;
        }

        private void fillDisplayColumnView(LastRoadReport rpt, TestPointDisplayColumn selCol)
        {
            listDisplay.SelectedIndexChanged -= listDisplay_SelectedIndexChanged;
            listDisplay.Items.Clear();
            btnRemoveDisCol.Enabled = false;
            foreach (TestPointDisplayColumn cnd in rpt.DisplayColumns)
            {
                listDisplay.Items.Add(cnd);
            }
            listDisplay.SelectedIndexChanged += listDisplay_SelectedIndexChanged;
            if (listDisplay.Items.Count > 0)
            {
                if (selCol != null)
                {
                    listDisplay.SelectedItem = selCol;
                }
                else
                {
                    listDisplay.SelectedIndex = 0;
                }
            }
        }

        void listDisplay_SelectedIndexChanged(object sender, EventArgs e)
        {
            curDisplayCol = null;
            TestPointDisplayColumn col = listDisplay.SelectedItem as TestPointDisplayColumn;
            if (col == null)
            {
                btnRemoveDisCol.Enabled = false;
                return;
            }
            txtCaption.TextChanged -= txtCaption_TextChanged;
            btnRemoveDisCol.Enabled = true;
            cbxSysDisplay.SelectedItem = col.DisplayParam.System;
            cbxParamDisplay.SelectedItem = col.DisplayParam;
            cbxParamIdxDisplay.SelectedItem = col.ParamArrayIndex;
            radioShowValueType.SelectedIndex = (int)col.ValueType;
            txtCaption.Text = col.Caption;
            txtCaption.TextChanged += txtCaption_TextChanged;
            curDisplayCol = col;
        }

        void txtCaption_TextChanged(object sender, EventArgs e)
        {
            if (curDisplayCol!=null)
            {
                curDisplayCol.Caption = txtCaption.Text;
                listCondition.Invalidate();
            }
        }

        private void btnRemoveCondition_Click(object sender, EventArgs e)
        {
            curReport.Condition.DetailItems.Remove(curValueCondition);
            curValueCondition = null;
            fillConditionView(curReport, null);
        }

        private void btnAddCondition_Click(object sender, EventArgs e)
        {
            if (numMinValue.Value > numMaxValue.Value)
            {
                MessageBox.Show("指标的最小值不能大于最大值！请重新设置！");
                return;
            }
            TestPointValueCondition item = new TestPointValueCondition();
            item.SysName = cbxSys.SelectedItem.ToString();
            item.ParamName = cbxParam.SelectedItem.ToString();
            if (cbxParamIdx.Enabled)
            {
                item.ParamArrayIndex = int.Parse(cbxParamIdx.SelectedItem.ToString());
            }
            item.MinValue = (double)numMinValue.Value;
            item.MaxValue = (double)numMaxValue.Value;
            if (curReport.Condition.Add(item))
            {
                fillConditionView(curReport, item);
            }
            else
            {
                MessageBox.Show("已存在相同的条件！");
            }
        }

        private void btnRemoveDisCol_Click(object sender, EventArgs e)
        {
            curReport.DisplayColumns.Remove(curDisplayCol);
            curDisplayCol = null;
            fillDisplayColumnView(curReport, null);
        }

        private void btnAddDisCol_Click(object sender, EventArgs e)
        {
            DTDisplayParameterInfo param = cbxParamDisplay.SelectedItem as DTDisplayParameterInfo;
            int idx = -1;
            if (cbxParamIdxDisplay.Enabled)
            {
                int.TryParse(cbxParamIdxDisplay.SelectedItem.ToString(), out idx);
            }
            TestPointDisplayColumn col = new TestPointDisplayColumn(txtCaption.Text, param, idx, (ESummaryValueType)radioShowValueType.SelectedIndex);
       
            if (curReport.AddDisplayColumn(col))
            {
                fillDisplayColumnView(curReport, col);
            }
            else
            {
                MessageBox.Show("已存在相同的参数列！");
            }
        }

        private void radioShowValueType_SelectedIndexChanged(object sender, EventArgs e)
        {
            makeCaption();
            if (curDisplayCol != null)
            {
                curDisplayCol.Caption = txtCaption.Text;
                curDisplayCol.ValueType = (ESummaryValueType)radioShowValueType.SelectedIndex;
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            LastRoadReportManager.GetInstance().Save();
        }

        private void btnNewReport_Click(object sender, EventArgs e)
        {
            TextInputBox box = new TextInputBox("新建报表", "报表名称", "未命名报表");
            if (box.ShowDialog() == DialogResult.OK)
            {
                LastRoadReport rpt = new LastRoadReport(box.TextInput);
                LastRoadReportManager.GetInstance().Reports.Add(rpt);
                fillReportList(rpt);
            }
        }

        private void btnRemoveReport_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show(this, "确定删除该报表？删除后将不能恢复！", "确认", MessageBoxButtons.OKCancel) == DialogResult.Yes)
            {
                LastRoadReportManager.GetInstance().Reports.Remove(curReport);
                fillReportList(null);
            }
        }

        private void cbxLogicalType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (curReport!=null)
            {
                curReport.Condition.LogicalType = (ELogicalType)Enum.Parse(typeof(ELogicalType),cbxLogicalType.SelectedItem.ToString());
            }
        }

        private void num2TPMaxDis_EditValueChanged(object sender, EventArgs e)
        {
            if (curReport != null)
            {
                curReport.Condition.Max2TesetPointDis = (double)num2TPMaxDis.Value;
            }
        }

        private void numMinLastDis_EditValueChanged(object sender, EventArgs e)
        {
            if (curReport != null)
            {
                curReport.Condition.MinLastDistance = (double)numMinLastDis.Value;
            }
        }

        private void txtCaption_EditValueChanged(object sender, EventArgs e)
        {
            if (curDisplayCol!=null&&!curDisplayCol.Caption.Equals(txtCaption.Text))
            {
                curDisplayCol.Caption = txtCaption.Text;
                listDisplay.Invalidate();
            }
        }

        private void listCondition_DrawItem(object sender, DrawItemEventArgs e)
        {
            e.DrawBackground();
            e.DrawFocusRectangle();
            if (listCondition.Items.Count>0)
            {
                e.Graphics.DrawString(listCondition.Items[e.Index].ToString(), e.Font, new SolidBrush(e.ForeColor), e.Bounds);
            }
        }

        private void listDisplay_DrawItem(object sender, DrawItemEventArgs e)
        {
            e.DrawBackground();
            e.DrawFocusRectangle();
            if (listDisplay.Items.Count>0)
            {
                e.Graphics.DrawString(listDisplay.Items[e.Index].ToString(), e.Font, new SolidBrush(e.ForeColor), e.Bounds);
            }
        }

        private void BtnDisSerial_Click(object sender, EventArgs e)
        {
            PopSelectSerialsForm serialForm = new PopSelectSerialsForm();
            if (serialForm.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            txtDisSerial.Text = "";
            if (serialForm.SelectedSerials.Count > 0)
            {
                txtDisSerial.Text = DTLayerSerialManager.Instance.GetNameBySerial(serialForm.SelectedSerials[0]);
            }

            if (curReport != null)
            {
                curReport.GISDisplaySerialName = txtDisSerial.Text;
            }
        }

        private void cbxDisSerial_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (curReport!=null)
            {
                //curReport.GISDisplaySerialName = cbxDisSerial.SelectedItem.ToString();
            }
        }

        private void listAllReport_DoubleClick(object sender, EventArgs e)
        {
            if (listAllReport.SelectedItem is LastRoadReport)
            {
                LastRoadReport rpt=listAllReport.SelectedItem as LastRoadReport;
                TextInputBox box = new TextInputBox("更改报表名称", "名称", rpt.Name);
                if (box.ShowDialog()==DialogResult.OK)
                {
                    rpt.Name = box.TextInput;
                    listAllReport.Invalidate();
                }
            }
        }

        private void listAllReport_DrawItem(object sender, DrawItemEventArgs e)
        {
            e.DrawBackground();
            e.DrawFocusRectangle();
            if (listAllReport.Items.Count > 0)
            {
                e.Graphics.DrawString(listAllReport.Items[e.Index].ToString(), e.Font, new SolidBrush(e.ForeColor), e.Bounds);
            }
        }



    }
}
