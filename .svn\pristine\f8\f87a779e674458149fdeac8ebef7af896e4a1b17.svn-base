﻿using System;
using System.Collections.Generic;
using System.Text;


using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Net
{
    public class DIYCellStatQuerySingle : DIYCellStatQueryBase
    {
        public DIYCellStatQuerySingle(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get { return "KPI统计(按小区)"; }
        }
        public override string IconName
        {
            get { return "Images/gridcell.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 1300, 1303, this.Name);
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Cell;
        }

        protected override MasterCom.RAMS.Model.Interface.StatTbToken getBranchToken()
        {
            return MasterCom.RAMS.Model.Interface.StatTbToken.cell;
        }
        protected override void prepareStatPackage_ImgGrid_FileFilter(Package package, CellQueryCondUnit cond,byte carrierID,bool byRound)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.KPI_CELL;
            package.Content.PrepareAddParam();
            if(byRound)
            {
                AddDIYRound(package, condition.ByRoundYear, condition.ByRoundRound);
            }
            else
            {
                AddDIYPeriod(package, cond.period);
            }
            AddDIYRegionOfCell(package,cond);
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYCarrierType(package, carrierID);
            AddDIYFileFilter(package, condition);

            AddDIYMomt(package, condition.Momt);
            //
            AddDIYEndOpFlag(package);

            AddDIYLacCIOfCell(package,cond);
            AddDIYEndOpFlag(package);
            
        }

        protected override void prepareStatPackage_Event_FileFilter(Package package, CellQueryCondUnit period, byte carrierID, bool byRound)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_DIY_AREA_EVENT;
            package.Content.PrepareAddParam();
            if (byRound)
            {
                AddDIYRound(package, condition.ByRoundYear, condition.ByRoundRound);
            }
            else
            {
                AddDIYPeriod(package, period.period);
            }
            AddDIYRegionOfCell(package, period);
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYCarrierType(package, carrierID);
            AddDIYFileFilter(package, condition);
            AddDIYMomt(package, condition.Momt);
            //
            AddDIYEndOpFlag(package);
        }
       
        protected override void prepareStatPackage_Event_EventFilter(Package package,CellQueryCondUnit cond)
        {
            AddDIYRegionEventOfCell(package,cond);
            AddDIYEndOpFlag(package);

        }

        private void AddDIYRegionEventOfCell(Package package, CellQueryCondUnit cond)
        {
            package.Content.AddParam((byte)OpOptionDef.AreaSelectSample);
            package.Content.AddParam(cond.ltLongitude);
            package.Content.AddParam(cond.ltLatitude);
            package.Content.AddParam(cond.brLongitude);
            package.Content.AddParam(cond.brLatitude);
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.SelectedCell == null && searchGeometrys.SelectedTDCell == null && searchGeometrys.SelectedWCell ==null)
            {
                return false;
            }
            return true;
        }
        protected override bool isValidPoint(double jd, double wd)
        {
            return true;
        }
        protected override bool isValidPoint(double ltX,double ltY,double brX,double brY)
        {
            try{
                return Condition.Geometorys.GeoOp.ContainsRectCenter(new DbRect(ltX, ltY, brX, brY));
            }catch{
                return false;
            }
           
        }

    }
}
