﻿using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Stat;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class CellKpiAuotExportAna : DIYAnalyseByCellBackgroundBaseByFile
    {
        public CellKpiAutoSet FuncSet { get; set; } = new CellKpiAutoSet();
        ReporterTemplate selectTemplate = null;
        string curDistrictName = "";
        List<NPOIRow> sumRows = new List<NPOIRow>();

        protected static readonly object lockObj = new object();
        private static CellKpiAuotExportAna instance = null;
        public static CellKpiAuotExportAna GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new CellKpiAuotExportAna();
                    }
                }
            }
            return instance;
        }

        protected CellKpiAuotExportAna()
            : base(MainModel.GetInstance())
        {
            if (instance != null)
            {
                return;
            }
            this.isIgnoreExport = true;
            ServiceTypes.Clear();
        }
        public override string Name
        {
            get
            {
                return "小区指标统计导出";
            }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 11000, 11053, "查询");
        }
        public override void DealBeforeBackgroundQueryByCity()
        {
            selectTemplate = ReporterTemplateManager.LoadSingleReportFromFile(CellKpiAuotProperties_LTE.StrRptTitleHead + FuncSet.SelectedTmpName);
            if (selectTemplate == null)
            {
                reportBackgroundInfo("未加载到小区统计报表");
                return;
            }

            sumRows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            sumRows.Add(row);
            row.AddCellValue("ID");
            if (FuncSet.IsCheckCellBasicInfo)
            {
                row.AddCellValue("地市");
                row.AddCellValue("基站名称");
                row.AddCellValue("ENodeBID");
                row.AddCellValue("经度");
                row.AddCellValue("纬度");
                row.AddCellValue("地址");
                row.AddCellValue("小区名称");
                row.AddCellValue("TAC");
                row.AddCellValue("CellID");
                row.AddCellValue("ARFCN");
                row.AddCellValue("PCI");
                row.AddCellValue("是否找到文件");
            }
            for (int i = 0; i < selectTemplate.Columns.Count; i++)
            {
                ColumnSet column = selectTemplate.Columns[i];
                row.AddCellValue(column.Title);
            }

        }
        protected override bool getCondition()
        {
            if (FuncSet == null || string.IsNullOrEmpty(FuncSet.SelectedTmpName) || string.IsNullOrEmpty(FuncSet.ResultSavePath))
            {
                System.Windows.Forms.MessageBox.Show("小区统计自动导出设置错误！");
                return false;
            }
            if (selectTemplate == null)
            {
                reportBackgroundInfo("未加载到小区统计报表");
                return false;
            }

            condition = new QueryCondition();
            DateTime startTime = BackgroundFuncConfigManager.GetInstance().StartTime;
            DateTime endTime = BackgroundFuncConfigManager.GetInstance().EndTime;
            condition.Periods.Add(new TimePeriod(startTime, endTime));
            condition.Projects = BackgroundFuncConfigManager.GetInstance().ProjectTypeList;
            return true;
        }
        protected override void query()
        {
            if (MainModel.IsBackground && !MainModel.BackgroundStarted)
            {
                return;
            }
            if (!getCondition())
            {
                return;
            }
            getReadyBeforeQuery();

            List<CellKpiAutoExportInfo> cellKpiInfoList = new List<CellKpiAutoExportInfo>();
            try
            {
                curDistrictName = DistrictManager.GetInstance().getDistrictName(MainModel.DistrictID);

                CellKPIAutoQuery query = new CellKPIAutoQuery(selectTemplate);
                query.SetQueryCondition(condition);
                query.Query();
                foreach (var fileDataDicPair in query.CellFileDataDic)
                {
                    List<CellAutoStatInfo> infoList = new List<CellAutoStatInfo>(fileDataDicPair.Value.Values);
                    if (infoList.Count > 0)
                    {
                        ICell iCell = CellManager.GetInstance().GetICellByLACCI(infoList[0].LAC, infoList[0].CI);
                        if ((iCell is LTECell && !FuncSet.IsCheckLteCell)
                            || (iCell is TDCell && !FuncSet.IsCheckTdCell)
                            || (iCell is Cell && !FuncSet.IsCheckGsmCell)
                            || (iCell is UnknowCell && !FuncSet.IsCheckUnKownCell))
                        {
                            continue;
                        }

                        CellKpiAutoExportInfo cellKpiInfo = getCellKpiInfo(infoList, iCell);
                        cellKpiInfoList.Add(cellKpiInfo);
                    }
                    fileDataDicPair.Value.Clear();
                }

                saveToSumInfo(cellKpiInfoList);
            }
            catch (Exception ee)
            {
                reportBackgroundInfo(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
            finally
            {
                this.condition = null;
            }
        }

        private CellKpiAutoExportInfo getCellKpiInfo(List<CellAutoStatInfo> infoList, ICell iCell)
        {
            CellKpiAutoExportInfo cellKpiInfo = new CellKpiAutoExportInfo();
            CellAutoStatInfo statInfoMerge = new CellAutoStatInfo();
            if (infoList.Count > 1)
            {
                infoList.Sort();

                DateTime firstFiletime = new DateTime();
                if (infoList[0].FileHeader != null)
                {
                    firstFiletime = MasterCom.Util.JavaDate.GetDateTimeFromMilliseconds(infoList[0].FileHeader.BeginTime * 1000L);
                }

                setStatInfoMerge(infoList, statInfoMerge, firstFiletime);
            }
            else if (infoList.Count == 1)
            {
                statInfoMerge = infoList[0];
            }

            infoList.Clear();
            if (statInfoMerge.KPIData != null)
            {
                statInfoMerge.KPIData.FinalMtMoGroup();
            }

            cellKpiInfo.HasFoundFile = true;
            cellKpiInfo.DistrictName = curDistrictName;
            cellKpiInfo.StatInfo = statInfoMerge;
            if (FuncSet.IsCheckCellBasicInfo)
            {
                cellKpiInfo.SetCurCell(iCell);
            }

            return cellKpiInfo;
        }

        private void setStatInfoMerge(List<CellAutoStatInfo> infoList, CellAutoStatInfo statInfoMerge, DateTime firstFiletime)
        {
            foreach (CellAutoStatInfo item in infoList)
            {
                statInfoMerge.LAC = item.LAC;
                statInfoMerge.CI = item.CI;
                if (FuncSet.IsCheckRecentDay && item.FileHeader != null && infoList[0].FileHeader != null)
                {
                    DateTime curFieTime = MasterCom.Util.JavaDate.GetDateTimeFromMilliseconds(item.FileHeader.BeginTime * 1000L);
                    if ((firstFiletime - curFieTime).Days < FuncSet.RecentDays)
                    {
                        statInfoMerge.KPIData.Merge(item.KPIData);
                    }
                }
                else if (!FuncSet.IsCheckRecentDay)
                {
                    statInfoMerge.KPIData.Merge(item.KPIData);
                }
            }
        }

        private void saveToSumInfo(List<CellKpiAutoExportInfo> cellKpiInfoList)
        {
            if (cellKpiInfoList == null || cellKpiInfoList.Count <= 0)
            {
                return;
            }
            cellKpiInfoList.Sort(comparerWithCellToken);

            CellKPIAutoQuery curKpiQuery = new CellKPIAutoQuery(selectTemplate);
            sumRows.AddRange(curKpiQuery.CreateReport(selectTemplate, cellKpiInfoList, FuncSet.IsCheckCellBasicInfo));
        }
        public override void DealAfterBackgroundQueryByCity()
        {
            try
            {
                if (sumRows == null || sumRows.Count <= 1)
                {
                    curDistrictName = "";
                    reportBackgroundInfo("未查询到小区统计信息");
                    return;
                }
                reportBackgroundInfo("开始导出小区统计信息...");

                string folderPath = string.Format(FuncSet.ResultSavePath + "\\" + DateTime.Now.ToString("yyyyMMdd") + FuncSet.SelectedTmpName);
                if (FuncSet.ResultSaveTypeIndex == 0)
                {
                    ExcelNPOIManager.ExportToExcel(sumRows, folderPath + ".xls", "验收结果");
                }
                else
                {
                    CSVWriter.ExportToCsv(sumRows, folderPath + ".csv");
                }
            }
            catch (Exception ee)
            {
                reportBackgroundInfo(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
            finally
            {
                selectTemplate = null;
                sumRows = null;
            }
        }

        private readonly Comparer comparerWithCellToken = new Comparer();
        private class Comparer : IComparer<CellKpiAutoExportInfo>
        {
            public int Compare(CellKpiAutoExportInfo x, CellKpiAutoExportInfo y)
            {
                return x.CellToken.CompareTo(y.CellToken);
            }
        }

        #region Background
        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.Cell_Region; }
        }
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.其他; }
        }
        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["IsCheckLteCell"] = FuncSet.IsCheckLteCell;
                param["IsCheckTdCell"] = FuncSet.IsCheckTdCell;
                param["IsCheckGsmCell"] = FuncSet.IsCheckGsmCell;
                param["IsCheckUnKownCell"] = FuncSet.IsCheckUnKownCell;
                param["IsCheckRecentDay"] = FuncSet.IsCheckRecentDay;
                param["RecentDays"] = FuncSet.RecentDays;
                param["IsCheckCellBasicInfo"] = FuncSet.IsCheckCellBasicInfo;
                param["SelectedTmpName"] = FuncSet.SelectedTmpName;
                param["ExcelSavePath"] = FuncSet.ResultSavePath;
                param["ResultSaveTypeIndex"] = FuncSet.ResultSaveTypeIndex;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("IsCheckRecentDay"))
                {
                    FuncSet.IsCheckRecentDay = (bool)param["IsCheckRecentDay"];
                }
                if (param.ContainsKey("IsCheckLteCell"))
                {
                    FuncSet.IsCheckLteCell = (bool)param["IsCheckLteCell"];
                }
                if (param.ContainsKey("IsCheckTdCell"))
                {
                    FuncSet.IsCheckTdCell = (bool)param["IsCheckTdCell"];
                }
                if (param.ContainsKey("IsCheckGsmCell"))
                {
                    FuncSet.IsCheckGsmCell = (bool)param["IsCheckGsmCell"];
                }
                if (param.ContainsKey("IsCheckUnKownCell"))
                {
                    FuncSet.IsCheckUnKownCell = (bool)param["IsCheckUnKownCell"];
                }
                if (param.ContainsKey("RecentDays"))
                {
                    FuncSet.RecentDays = (int)param["RecentDays"];
                }
                if (param.ContainsKey("IsCheckCellBasicInfo"))
                {
                    FuncSet.IsCheckCellBasicInfo = (bool)param["IsCheckCellBasicInfo"];
                }
                if (param.ContainsKey("SelectedTmpName"))
                {
                    FuncSet.SelectedTmpName = (string)param["SelectedTmpName"];
                }
                if (param.ContainsKey("ExcelSavePath"))
                {
                    FuncSet.ResultSavePath = (string)param["ExcelSavePath"];
                }
                if (param.ContainsKey("ResultSaveTypeIndex"))
                {
                    FuncSet.ResultSaveTypeIndex = (int)param["ResultSaveTypeIndex"];
                }
            }
        }
        public override PropertiesControl Properties
        {
            get
            {
                return new CellKpiAuotProperties_LTE(this);
            }
        }
        #endregion
    }


}
