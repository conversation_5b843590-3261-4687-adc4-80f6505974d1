﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.Data.Helpers;
using DevExpress.XtraCharts;
using DevExpress.XtraGrid;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CsfbFailureCauseResultForm : MinCloseForm
    {
        public CsfbFailureCauseResultForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
#if LT
            miShowFlowChart.Visible = false;
            gridBand5.Caption = "WCDMA";
            gridColumn29.Caption = "平均Ec_Io";
            gridColumn30.Caption = "最大Ec_Io";
            gridColumn31.Caption = "最小Ec_Io";
#endif
            this.DisposeWhenClose = true;
        }

        private List<CsfbFailureEntity> failures = null; 
        internal void FillData(List<CsfbFailureEntity> failures)
        {
            this.failures = failures;
            gridDetail.DataSource = failures;
            gridDetail.RefreshDataSource();
            makeSummary();
        }

        private void makeSummary()
        {
            Dictionary<string,int> causeDic=new Dictionary<string , int>();
            foreach (string name in Enum.GetNames(typeof(CsfbFailureCause)))
            {
#if LT
                if (name.Contains("TD"))
                {
                    continue;
                }
#else
                if (name.Contains("W"))
                {
                    continue;
                }
#endif
                causeDic[name] = 0;
            }
            foreach(CsfbFailureEntity failure in failures)
            {
                causeDic[failure.Cause.ToString()]++;
            }
            DataTable tb=new DataTable();
            tb.Columns.Add("原因", typeof(string));
            tb.Columns.Add("个数", typeof(int));
            tb.Columns.Add("占比(%)" , typeof(double));

            Series mainSer = chartMain.Series[0];
            mainSer.Points.Clear();
            foreach(KeyValuePair<string , int> pair in causeDic)
            {
                DataRow row = tb.NewRow();
                row["原因"] = pair.Key;
                row["个数"] = pair.Value;
                double per = Math.Round(100.0*pair.Value/failures.Count , 2);
                row["占比(%)"] = per;
                tb.Rows.Add(row);
                SeriesPoint pnt = new SeriesPoint(pair.Key , per);
                mainSer.Points.Add(pnt);
            }
            DataRow srow = tb.NewRow();
            srow["原因"] = "汇总";
            srow["个数"] = failures.Count;
            srow["占比(%)"] = 100;
            tb.Rows.Add(srow);
            gridSummary.DataSource = tb;
            viewSummary.PopulateColumns();
            viewSummary.BestFitColumns();
        }

        private void viewDetails_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.Utils.DXMouseEventArgs dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo info = viewDetails.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }
            CsfbFailureEntity failure = viewDetails.GetRow(info.RowHandle) as CsfbFailureEntity;
            string legend;
#if LT
            legend = "lte_fdd_RSRP";
            switch (failure.Cause)
            {
                case CsfbFailureCause.GSM网络弱覆盖:
                    legend = "lte_fdd_gsm_DM_RxLevSub";
                    break;
                case CsfbFailureCause.GSM网络质差:
                    legend = "lte_fdd_gsm_DM_RxQualSub";
                    break;
                case CsfbFailureCause.无RRCConnRelease_LTE网络质差:
                    legend = "lte_fdd_SINR";
                    break;
                case CsfbFailureCause.W网络高质差:
                    legend = "lte_fdd_wcdma_TotalEc_Io";
                    break;
                case CsfbFailureCause.W网络弱覆盖:
                    legend = "lte_fdd_wcdma_TotalRSCP";
                    break;
            }
#else
            legend = "TD_LTE_RSRP";
            switch(failure.Cause)
            {
                case CsfbFailureCause.GSM网络弱覆盖:
                    legend = "LTE:gsm_DM_RxLevSub";
                    break;
                case CsfbFailureCause.GSM网络质差:
                    legend = "LTE:gsm_DM_RxQualSub";
                    break;
                case CsfbFailureCause.无RRCConnRelease_LTE网络质差:
                    legend = "LTE:TD_LTE_SINR";
                    break;
                case CsfbFailureCause.TD网络高BLER:
                    legend = "LTE:td_DM_BLER";
                    break;
                case CsfbFailureCause.TD网络弱覆盖:
                    legend = "LTE:td_DM_PCCPCH_RSCP";
                    break;
            }
#endif
            MainModel.ClearDTData();
            foreach(TestPoint tp in failure.TestPoints)
            {
                MainModel.DTDataManager.Add(tp);
            }
            foreach(Event evt in failure.Events)
            {
                MainModel.DTDataManager.Add(evt);
            }
            foreach (MasterCom.RAMS.Model.Message msg in failure.Messages)
            {
                MainModel.DTDataManager.Add(msg);
            }
            MainModel.FireSetDefaultMapSerialTheme(legend);
            MainModel.FireDTDataChanged(this);
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            GridControl ctrl = null;
            if(tabCtrl.SelectedTabPage==pageSummary)
            {
                ctrl = gridSummary;
            }
            else if (tabCtrl.SelectedTabPage==pageDetail)
            {
                ctrl = gridDetail;
            }
            SaveFileDialog dlg=new SaveFileDialog();
            dlg.Filter = MasterCom.Util.FilterHelper.ExcelX;
            dlg.RestoreDirectory = true;
            if(dlg.ShowDialog()==DialogResult.OK)
            {
                Cursor = Cursors.WaitCursor;
                if (ctrl != null)
                {
                    ctrl.ExportToXlsx(dlg.FileName);
                }
                Cursor = Cursors.Default;
            }
        }

        private void miShowFlowChart_Click(object sender, EventArgs e)
        {
            CsfbFailureCauseFlowchart chart =
                MainModel.CreateResultForm(typeof(CsfbFailureCauseFlowchart)) as CsfbFailureCauseFlowchart;
            chart.Visible = true;
            chart.BringToFront();
        }



    }
}
