﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class WCDMACellWrongDir : TDCellWrongDir
    {
        public WCDMACellWrongDir(WCell cell)
            : base(cell)
        {
        }
        public new WCell Cell
        {
            get { return (WCell)cell; }
        }

        public override string CellName
        {
            get
            {
                return Cell.Name;
            }
        }

        public override int Direction
        {
            get
            {
                return Cell.Direction;
            }
        }


        public override double Latitude
        {
            get
            {
                return Cell.Latitude;
            }
        }

        public override double Longitude
        {
            get
            {
                return Cell.Longitude;
            }
        }

        public override int LAC
        {
            get
            {
                return Cell.LAC;
            }
        }

        public override int CI
        {
            get
            {
                return Cell.CI;
            }
        }

        public override TDCellWrongDir Clone()
        {
            TDCellWrongDir cellWrong = new WCDMACellWrongDir(Cell);
            cellWrong.cellWrongBatch = this.cellWrongBatch;
            cellWrong.resultFirstBatch = this.resultFirstBatch;
            cellWrong.resultSecondBatch = this.resultSecondBatch;

            return cellWrong;
        }
    }
}
