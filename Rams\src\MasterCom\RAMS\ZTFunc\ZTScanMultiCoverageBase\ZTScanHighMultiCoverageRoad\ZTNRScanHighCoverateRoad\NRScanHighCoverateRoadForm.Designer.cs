﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRScanHighCoverateRoadForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode5 = new DevExpress.XtraGrid.GridLevelNode();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode6 = new DevExpress.XtraGrid.GridLevelNode();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode2 = new DevExpress.XtraGrid.GridLevelNode();
            this.gvPointRel = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcRel = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStripRelative = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportSummaryRel = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportRel = new System.Windows.Forms.ToolStripMenuItem();
            this.gvRoadRel = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gvCellRel = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gvPointAbs = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcAbs = new DevExpress.XtraGrid.GridControl();
            this.gvRoadAbs = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn44 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn45 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn46 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn47 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn48 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn49 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn50 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn51 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn52 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gvCellAbs = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn39 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.contextMenuStripAbsolute = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportSummaryAbs = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportAbs = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.gvPointRel)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcRel)).BeginInit();
            this.contextMenuStripRelative.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gvRoadRel)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvCellRel)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvPointAbs)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcAbs)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvRoadAbs)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvCellAbs)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            this.xtraTabPage2.SuspendLayout();
            this.contextMenuStripAbsolute.SuspendLayout();
            this.SuspendLayout();
            // 
            // gvPointRel
            // 
            this.gvPointRel.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16});
            this.gvPointRel.GridControl = this.gcRel;
            this.gvPointRel.Name = "gvPointRel";
            this.gvPointRel.OptionsBehavior.Editable = false;
            this.gvPointRel.OptionsDetail.ShowDetailTabs = false;
            this.gvPointRel.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "采样点编号";
            this.gridColumn14.FieldName = "SN";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 0;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "采样点经度";
            this.gridColumn15.FieldName = "Tp.Longitude";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 1;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "采样点纬度";
            this.gridColumn16.FieldName = "Tp.Latitude";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 2;
            // 
            // gcRel
            // 
            this.gcRel.ContextMenuStrip = this.contextMenuStripRelative;
            this.gcRel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcRel.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gcRel.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gcRel.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gcRel.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gcRel.EmbeddedNavigator.Buttons.Remove.Visible = false;
            gridLevelNode5.LevelTemplate = this.gvPointRel;
            gridLevelNode6.LevelTemplate = this.gvCellRel;
            gridLevelNode6.RelationName = "CellList";
            gridLevelNode5.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode6});
            gridLevelNode5.RelationName = "SampleList";
            this.gcRel.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode5});
            this.gcRel.Location = new System.Drawing.Point(0, 0);
            this.gcRel.MainView = this.gvRoadRel;
            this.gcRel.Name = "gcRel";
            this.gcRel.Size = new System.Drawing.Size(793, 420);
            this.gcRel.TabIndex = 6;
            this.gcRel.UseEmbeddedNavigator = true;
            this.gcRel.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvRoadRel,
            this.gvCellRel,
            this.gvPointRel});
            // 
            // contextMenuStripRelative
            // 
            this.contextMenuStripRelative.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportSummaryRel,
            this.miExportRel});
            this.contextMenuStripRelative.Name = "contextMenuStrip1";
            this.contextMenuStripRelative.Size = new System.Drawing.Size(190, 48);
            this.contextMenuStripRelative.Text = "导出概要信息到Excel";
            // 
            // miExportSummaryRel
            // 
            this.miExportSummaryRel.Name = "miExportSummaryRel";
            this.miExportSummaryRel.Size = new System.Drawing.Size(189, 22);
            this.miExportSummaryRel.Text = "导出概要信息到Excel";
            this.miExportSummaryRel.Click += new System.EventHandler(this.miExportSummaryRel_Click);
            // 
            // miExportRel
            // 
            this.miExportRel.Name = "miExportRel";
            this.miExportRel.Size = new System.Drawing.Size(189, 22);
            this.miExportRel.Text = "导出详细信息到Excel";
            this.miExportRel.Click += new System.EventHandler(this.miExportRel_Click);
            // 
            // gvRoadRel
            // 
            this.gvRoadRel.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13});
            this.gvRoadRel.GridControl = this.gcRel;
            this.gvRoadRel.Name = "gvRoadRel";
            this.gvRoadRel.OptionsBehavior.Editable = false;
            this.gvRoadRel.OptionsDetail.ShowDetailTabs = false;
            this.gvRoadRel.OptionsView.ColumnAutoWidth = false;
            this.gvRoadRel.OptionsView.ShowGroupPanel = false;
            this.gvRoadRel.DoubleClick += new System.EventHandler(this.gvRoadRel_DoubleClick);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "序号";
            this.gridColumn1.FieldName = "SN";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "道路名称";
            this.gridColumn2.FieldName = "RoadName";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "距离(米)";
            this.gridColumn3.FieldName = "Distance";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "高重叠覆盖点占比(%)";
            this.gridColumn4.FieldName = "Percent";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            this.gridColumn4.Width = 148;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "采样点数";
            this.gridColumn5.FieldName = "SampleCount";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 4;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "第一强最大值";
            this.gridColumn6.FieldName = "Rsrp.Max";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 5;
            this.gridColumn6.Width = 105;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "第一强最小值";
            this.gridColumn7.FieldName = "Rsrp.Min";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 6;
            this.gridColumn7.Width = 101;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "第一强平均值";
            this.gridColumn8.FieldName = "Rsrp.Avg";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 7;
            this.gridColumn8.Width = 105;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "路段中心经度";
            this.gridColumn9.FieldName = "LongitudeMid";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 8;
            this.gridColumn9.Width = 105;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "路段中心纬度";
            this.gridColumn10.FieldName = "LatitudeMid";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 9;
            this.gridColumn10.Width = 116;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "文件名";
            this.gridColumn11.FieldName = "FileName";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 10;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "开始时间";
            this.gridColumn12.FieldName = "FirstTime";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 11;
            this.gridColumn12.Width = 117;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "结束时间";
            this.gridColumn13.FieldName = "LastTime";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 12;
            this.gridColumn13.Width = 114;
            // 
            // gvCellRel
            // 
            this.gvCellRel.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn24,
            this.gridColumn25,
            this.gridColumn26});
            this.gvCellRel.GridControl = this.gcRel;
            this.gvCellRel.Name = "gvCellRel";
            this.gvCellRel.OptionsBehavior.Editable = false;
            this.gvCellRel.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "小区名称";
            this.gridColumn17.FieldName = "CellName";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 0;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "小区状态";
            this.gridColumn18.FieldName = "CellTypeDesc";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 1;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "TAC";
            this.gridColumn19.FieldName = "TAC";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 2;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "NCI";
            this.gridColumn20.FieldName = "NCI";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 3;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "ARFCN";
            this.gridColumn21.FieldName = "ARFCN";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 4;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "PCI";
            this.gridColumn22.FieldName = "PCI";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 5;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "RSRP";
            this.gridColumn23.FieldName = "Rsrp";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 6;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "与采样点距离（米）";
            this.gridColumn24.FieldName = "Distance";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 7;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "小区经度";
            this.gridColumn25.FieldName = "Longitude";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 8;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "小区纬度";
            this.gridColumn26.FieldName = "Latitude";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 9;
            // 
            // gvPointAbs
            // 
            this.gvPointAbs.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn27,
            this.gridColumn28,
            this.gridColumn29});
            this.gvPointAbs.GridControl = this.gcAbs;
            this.gvPointAbs.Name = "gvPointAbs";
            this.gvPointAbs.OptionsBehavior.Editable = false;
            this.gvPointAbs.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "采样点编号";
            this.gridColumn27.FieldName = "SN";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 0;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "采样点经度";
            this.gridColumn28.FieldName = "Longitude";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 1;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "采样点纬度";
            this.gridColumn29.FieldName = "Latitude";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 2;
            // 
            // gcAbs
            // 
            this.gcAbs.ContextMenuStrip = this.contextMenuStripRelative;
            this.gcAbs.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gcAbs.EmbeddedNavigator.Buttons.Append.Visible = false;
            this.gcAbs.EmbeddedNavigator.Buttons.CancelEdit.Visible = false;
            this.gcAbs.EmbeddedNavigator.Buttons.Edit.Visible = false;
            this.gcAbs.EmbeddedNavigator.Buttons.EndEdit.Visible = false;
            this.gcAbs.EmbeddedNavigator.Buttons.Remove.Visible = false;
            gridLevelNode1.LevelTemplate = this.gvPointAbs;
            gridLevelNode2.LevelTemplate = this.gvCellAbs;
            gridLevelNode2.RelationName = "CellList";
            gridLevelNode1.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode2});
            gridLevelNode1.RelationName = "SampleList";
            this.gcAbs.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.gcAbs.Location = new System.Drawing.Point(0, 0);
            this.gcAbs.MainView = this.gvRoadAbs;
            this.gcAbs.Name = "gcAbs";
            this.gcAbs.Size = new System.Drawing.Size(793, 420);
            this.gcAbs.TabIndex = 7;
            this.gcAbs.UseEmbeddedNavigator = true;
            this.gcAbs.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvRoadAbs,
            this.gvCellAbs,
            this.gvPointAbs});
            // 
            // gvRoadAbs
            // 
            this.gvRoadAbs.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn40,
            this.gridColumn41,
            this.gridColumn42,
            this.gridColumn43,
            this.gridColumn44,
            this.gridColumn45,
            this.gridColumn46,
            this.gridColumn47,
            this.gridColumn48,
            this.gridColumn49,
            this.gridColumn50,
            this.gridColumn51,
            this.gridColumn52});
            this.gvRoadAbs.GridControl = this.gcAbs;
            this.gvRoadAbs.Name = "gvRoadAbs";
            this.gvRoadAbs.OptionsBehavior.Editable = false;
            this.gvRoadAbs.OptionsView.ColumnAutoWidth = false;
            this.gvRoadAbs.OptionsView.ShowDetailButtons = false;
            this.gvRoadAbs.OptionsView.ShowGroupPanel = false;
            this.gvRoadAbs.DoubleClick += new System.EventHandler(this.gvRoadAbs_DoubleClick);
            // 
            // gridColumn40
            // 
            this.gridColumn40.Caption = "序号";
            this.gridColumn40.FieldName = "SN";
            this.gridColumn40.Name = "gridColumn40";
            this.gridColumn40.Visible = true;
            this.gridColumn40.VisibleIndex = 0;
            // 
            // gridColumn41
            // 
            this.gridColumn41.Caption = "道路名称";
            this.gridColumn41.FieldName = "RoadName";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.Visible = true;
            this.gridColumn41.VisibleIndex = 1;
            // 
            // gridColumn42
            // 
            this.gridColumn42.Caption = "距离(米)";
            this.gridColumn42.FieldName = "Distance";
            this.gridColumn42.Name = "gridColumn42";
            this.gridColumn42.Visible = true;
            this.gridColumn42.VisibleIndex = 2;
            // 
            // gridColumn43
            // 
            this.gridColumn43.Caption = "高重叠覆盖点占比(%)";
            this.gridColumn43.FieldName = "Percent";
            this.gridColumn43.Name = "gridColumn43";
            this.gridColumn43.Visible = true;
            this.gridColumn43.VisibleIndex = 3;
            // 
            // gridColumn44
            // 
            this.gridColumn44.Caption = "采样点数";
            this.gridColumn44.FieldName = "SampleCount";
            this.gridColumn44.Name = "gridColumn44";
            this.gridColumn44.Visible = true;
            this.gridColumn44.VisibleIndex = 4;
            // 
            // gridColumn45
            // 
            this.gridColumn45.Caption = "第一强最大值";
            this.gridColumn45.FieldName = "Rsrp.Max";
            this.gridColumn45.Name = "gridColumn45";
            this.gridColumn45.Visible = true;
            this.gridColumn45.VisibleIndex = 5;
            // 
            // gridColumn46
            // 
            this.gridColumn46.Caption = "第一强最小值";
            this.gridColumn46.FieldName = "RsrpMin";
            this.gridColumn46.Name = "gridColumn46";
            this.gridColumn46.Visible = true;
            this.gridColumn46.VisibleIndex = 6;
            // 
            // gridColumn47
            // 
            this.gridColumn47.Caption = "第一强平均值";
            this.gridColumn47.FieldName = "RsrpAvg";
            this.gridColumn47.Name = "gridColumn47";
            this.gridColumn47.Visible = true;
            this.gridColumn47.VisibleIndex = 7;
            // 
            // gridColumn48
            // 
            this.gridColumn48.Caption = "路段中心经度";
            this.gridColumn48.FieldName = "LongitudeMid";
            this.gridColumn48.Name = "gridColumn48";
            this.gridColumn48.Visible = true;
            this.gridColumn48.VisibleIndex = 8;
            // 
            // gridColumn49
            // 
            this.gridColumn49.Caption = "路段中心纬度";
            this.gridColumn49.FieldName = "LatitudeMid";
            this.gridColumn49.Name = "gridColumn49";
            this.gridColumn49.Visible = true;
            this.gridColumn49.VisibleIndex = 9;
            // 
            // gridColumn50
            // 
            this.gridColumn50.Caption = "文件名";
            this.gridColumn50.FieldName = "FileName";
            this.gridColumn50.Name = "gridColumn50";
            this.gridColumn50.Visible = true;
            this.gridColumn50.VisibleIndex = 10;
            // 
            // gridColumn51
            // 
            this.gridColumn51.Caption = "开始时间";
            this.gridColumn51.FieldName = "FirstTime";
            this.gridColumn51.Name = "gridColumn51";
            this.gridColumn51.Visible = true;
            this.gridColumn51.VisibleIndex = 11;
            // 
            // gridColumn52
            // 
            this.gridColumn52.Caption = "结束时间";
            this.gridColumn52.FieldName = "LastTime";
            this.gridColumn52.Name = "gridColumn52";
            this.gridColumn52.Visible = true;
            this.gridColumn52.VisibleIndex = 12;
            // 
            // gvCellAbs
            // 
            this.gvCellAbs.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn30,
            this.gridColumn31,
            this.gridColumn32,
            this.gridColumn33,
            this.gridColumn34,
            this.gridColumn35,
            this.gridColumn36,
            this.gridColumn37,
            this.gridColumn38,
            this.gridColumn39});
            this.gvCellAbs.GridControl = this.gcAbs;
            this.gvCellAbs.Name = "gvCellAbs";
            this.gvCellAbs.OptionsBehavior.Editable = false;
            this.gvCellAbs.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "小区名称";
            this.gridColumn30.FieldName = "CellName";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 0;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "小区状态";
            this.gridColumn31.FieldName = "CellTypeDesc";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 1;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "TAC";
            this.gridColumn32.FieldName = "TAC";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 2;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "NCI";
            this.gridColumn33.FieldName = "NCI";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 3;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "EARFCN";
            this.gridColumn34.FieldName = "EARFCN";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 4;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "PCI";
            this.gridColumn35.FieldName = "PCI";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 5;
            // 
            // gridColumn36
            // 
            this.gridColumn36.Caption = "RSRP";
            this.gridColumn36.FieldName = "Rsrp";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.Visible = true;
            this.gridColumn36.VisibleIndex = 6;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "与采样点距离（米）";
            this.gridColumn37.FieldName = "Distance";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.Visible = true;
            this.gridColumn37.VisibleIndex = 7;
            // 
            // gridColumn38
            // 
            this.gridColumn38.Caption = "小区经度";
            this.gridColumn38.FieldName = "Longitude";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.Visible = true;
            this.gridColumn38.VisibleIndex = 8;
            // 
            // gridColumn39
            // 
            this.gridColumn39.Caption = "小区纬度";
            this.gridColumn39.FieldName = "Latitude";
            this.gridColumn39.Name = "gridColumn39";
            this.gridColumn39.Visible = true;
            this.gridColumn39.VisibleIndex = 9;
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(800, 450);
            this.xtraTabControl1.TabIndex = 0;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.gcRel);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(793, 420);
            this.xtraTabPage1.Text = "相对重叠覆盖";
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.gcAbs);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(793, 420);
            this.xtraTabPage2.Text = "绝对重叠覆盖";
            // 
            // contextMenuStripAbsolute
            // 
            this.contextMenuStripAbsolute.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportSummaryAbs,
            this.miExportAbs});
            this.contextMenuStripAbsolute.Name = "contextMenuStrip1";
            this.contextMenuStripAbsolute.Size = new System.Drawing.Size(190, 70);
            this.contextMenuStripAbsolute.Text = "导出概要信息到Excel";
            // 
            // miExportSummaryAbs
            // 
            this.miExportSummaryAbs.Name = "miExportSummaryAbs";
            this.miExportSummaryAbs.Size = new System.Drawing.Size(189, 22);
            this.miExportSummaryAbs.Text = "导出概要信息到Excel";
            this.miExportSummaryAbs.Click += new System.EventHandler(this.miExportSummaryAbs_Click);
            // 
            // miExportAbs
            // 
            this.miExportAbs.Name = "miExportAbs";
            this.miExportAbs.Size = new System.Drawing.Size(189, 22);
            this.miExportAbs.Text = "导出详细信息到Excel";
            this.miExportAbs.Click += new System.EventHandler(this.miExportAbs_Click);
            // 
            // NRScanHighCoverateRoadForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 450);
            this.Controls.Add(this.xtraTabControl1);
            this.Name = "NRScanHighCoverateRoadForm";
            this.Text = "高重叠覆盖道路";
            ((System.ComponentModel.ISupportInitialize)(this.gvPointRel)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcRel)).EndInit();
            this.contextMenuStripRelative.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gvRoadRel)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvCellRel)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvPointAbs)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gcAbs)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvRoadAbs)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvCellAbs)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            this.xtraTabPage2.ResumeLayout(false);
            this.contextMenuStripAbsolute.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraGrid.GridControl gcRel;
        private DevExpress.XtraGrid.Views.Grid.GridView gvRoadRel;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Views.Grid.GridView gvPointRel;
        private DevExpress.XtraGrid.Views.Grid.GridView gvCellRel;
        private DevExpress.XtraGrid.GridControl gcAbs;
        private DevExpress.XtraGrid.Views.Grid.GridView gvPointAbs;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Views.Grid.GridView gvCellAbs;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn39;
        private DevExpress.XtraGrid.Views.Grid.GridView gvRoadAbs;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn40;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn44;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn45;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn46;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn47;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn48;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn49;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn50;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn51;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn52;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripRelative;
        private System.Windows.Forms.ToolStripMenuItem miExportSummaryRel;
        private System.Windows.Forms.ToolStripMenuItem miExportRel;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripAbsolute;
        private System.Windows.Forms.ToolStripMenuItem miExportSummaryAbs;
        private System.Windows.Forms.ToolStripMenuItem miExportAbs;
    }
}