﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public class AreaPKSearchByCountry : AreaKpiSearchByCountry
    {
        private PKHub pkHub;

        public Dictionary<AreaBase, CHubValue> PkValueDic
        {
            get;
            private set;
        }

        public AreaPKSearchByCountry()
        {
            PkValueDic = new Dictionary<AreaBase, CHubValue>();
        }

        public void SetHub(PKHub hub)
        {
            pkHub = hub;
        }

        protected override void afterRecieveOnePeriodData(params object[] reservedParams)
        {
            foreach (AreaKPIDataGroup<AreaBase> group in AreaKpiMap.Values)
            {
                group.FinalMtMoGroup();
            }

            foreach (Dictionary<int, AreaBase> idAreaDic in curTypePairMap.Values)
            {
                foreach (AreaBase area in idAreaDic.Values)
                {
                    CHubValue pk;
                    if (!PkValueDic.TryGetValue(area, out pk))
                    {
                        pk = new CHubValue(area);
                        PkValueDic[area] = pk;
                    }
                    AreaKPIDataGroup<AreaBase> group;
                    if (!AreaKpiMap.TryGetValue(area, out group))
                    {
                        group = new AreaKPIDataGroup<AreaBase>(area, IsStatLatestOnly);
                        AreaKpiMap[area] = group;
                    }
                    pk.CalcValue(group, pkHub);
                }
            }

            AreaKpiMap.Clear();
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            //
        }
    }

    public class CHubValue
    {
        public AreaBase Area
        {
            get;
            set;
        }

        public Dictionary<int, FileInfo> IdFileDic
        {
            get;
            set;
        }

        public double DValue
        {
            get;
            set;
        }

        public CHubValue(AreaBase area)
        {
            Area = area;
            IdFileDic = new Dictionary<int, FileInfo>();
            DValue = double.NaN;
        }

        public void CalcValue(AreaKPIDataGroup<AreaBase> group, PKHub hub)
        {
            IdFileDic = group.FileIDDic;

            DValue = group.CalcFormula((CarrierType)hub.Carrier, (int)MoMtFile.Unknow, hub.PkBase.FormulaExp);
        }
    }
}
