﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.KPI_Statistics;

namespace MasterCom.RAMS.ZTFunc.ZTRegionGridFilter
{
    public class RegionGridDataInfo
    {
        public RegionGridDataInfo(string name)
        {
            this.Name = name;
        }
        public string Name
        { get; set; }

        protected GridMatrix<GridDataUnit> gridMatrix = new GridMatrix<GridDataUnit>();

        private List<GridItem> totalGrids = new List<GridItem>();

        public int GridNum
        {
            get { return totalGrids.Count; }
        }

        public double FilteredRate
        {
            get
            {
                return Math.Round(100.0 * filteredGirds.Count / GridNum, 2);
            }
        }

        private readonly List<GridItem> filteredGirds = new List<GridItem>();
        public List<GridItem> FilteredGirds
        {
            get { return filteredGirds; }
        }

        public void AddGridData(KPIStatDataBase data)
        {
            GridDataUnit grid = new GridDataUnit(data.LTLng, data.LTLat);
            grid.AddStatData(data);
            int rAt, cAt;
            GridHelper.GetIndexOfDefaultSizeGrid(grid.CenterLng, grid.CenterLat, out rAt, out cAt);
            GridDataUnit old = gridMatrix[rAt, cAt];
            if (old == null)
            {
                gridMatrix[rAt, cAt] = grid;
            }
            else
            {
                old.Gather(grid);
            }
        }

        public void FilteredGrid(int tpNumMin, int durationMin, int distanceMin)
        {
            filteredGirds.Clear();
            foreach (GridItem grid in totalGrids)
            {
                if (grid.TestPointCount >= tpNumMin 
                    && grid.Duration >= durationMin 
                    && grid.Distance >= distanceMin)
                {
                    filteredGirds.Add(grid);
                }
            }
        }


        internal void MakeSummary()
        {
            totalGrids = new List<GridItem>();
            foreach (GridDataUnit grid in gridMatrix)
            {
                double num = grid.DataHub.CalcValueByFormula("{Nr_80040007}");
                if (num == double.NaN)
                {
                    num = grid.DataHub.CalcValueByFormula("{Lte_61210301}");
                }

                int duration = (int)Math.Ceiling(num / 4.5);
                var distance = grid.DataHub.CalcValueByFormula("{Nr_80040006}");
                if (distance == double.NaN)
                {
                    distance = grid.DataHub.CalcValueByFormula("{Lte_0806}");
                }

                GridItem item = new GridItem(grid);
                item.TestPointCount = (int)num;
                item.Duration = duration;
                item.Distance = (int)distance;
                totalGrids.Add(item);
            }
            this.gridMatrix = null;
        }
    }

    public class GridItem : GridUnitBase
    {
        public int TestPointCount { get; set; } 
        public double Duration { get; set; } 
        public double Distance { get; set; } 
        public double AppSpeedM { get; set; } 
        public double FtpSuccCount { get; set; } 
        public double RsrpAvg { get; set; } 
        private GridDataUnit grid { get; set; }

        public GridItem(GridDataUnit grid)
        {
            this.grid = grid;
            this.LTLng = Math.Round(grid.LTLng, 4);
            this.LTLat = Math.Round(grid.LTLat, 5);
            
            double totalByte = grid.DataHub.CalcValueByFormula("{Nr_BA040084}");
            if (totalByte == double.NaN)
            {
                totalByte = grid.DataHub.CalcValueByFormula("{Lte_052109020101}");
            }

            double totalSec = grid.DataHub.CalcValueByFormula("{Nr_BA040085}") * 1000;
            if (totalSec == double.NaN)
            {
                totalSec = grid.DataHub.CalcValueByFormula("{Lte_052109020102}") * 1000;
            }

            this.AppSpeedM = Math.Round(totalByte / 2048 / totalSec * 8000, 2);
            if (double.IsNaN(AppSpeedM))
            {
                AppSpeedM = 0;
            }

            this.FtpSuccCount = grid.DataHub.CalcValueByFormula("{evtIdCount[9057]}");
            if (FtpSuccCount == double.NaN)
            {
                FtpSuccCount = grid.DataHub.CalcValueByFormula("{evtIdCount[57]}");
            }

            if (double.IsNaN(FtpSuccCount))
            {
                FtpSuccCount = 0;
            }

            this.RsrpAvg = (int)grid.DataHub.CalcValueByFormula("{Nr_BA040002}");
            if (RsrpAvg == double.NaN)
            {
                RsrpAvg = (int)grid.DataHub.CalcValueByFormula("{Lte_61210309}");
            }
        }
    }
}
