﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.UserMng;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using System.Windows.Forms;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.ZTFunc;
using System.Drawing;
using System.Data;


namespace MasterCom.RAMS.Net
{
    public class ZTGridCompareCountMutCarriers : ZTGridCompareDownloadSpeed
    {
        public ZTGridCompareCountMutCarriers(MainModel mainModel)
            : base(mainModel)
        {
            this.mainModel = mainModel;
        }
        public override string Name
        {
            get { return "统计速率分段栅格数(与竞争对手对比)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22082, this.Name);
        }

        #region 全局变量
        string tableName = "";
        public bool isMonthForward { get; set; } = false;
        public string str主队RSRP采样点数 { get; set; } = "Lte_61212D0101";
        public string str主队RSRP采样点数_旧 { get; set; } = "Lte_61210301";
        public string str主队脱网时长 { get; set; } = "Lte_61210316/1000";
        public string str主队脱网标记 { get; set; } = "Lte_087C/1000";
        public string str主队占它网时长 { get; set; } = "(Lte_088C+Lte_088E+Lte_0890)/1000";
        public string str主队占它网时长_旧 { get; set; } = "(Lte_61212B0211+Lte_61212B0311+Lte_61212B0411)/1000";
        public string str客队RSRP采样点数 { get; set; } = "Lf_612D2D0201";
        public string str客队RSRP采样点数_旧 { get; set; } = "Lf_612D0301";
        public string str客队脱网时长 { get; set; } = "Lf_612D0316/1000";
        public string str客队脱网标记 { get; set; } = "Lf_087C/1000";
        public string str客队占它网时长 { get; set; } = "(Lf_088A+Lf_088E+Lf_0890)/1000";      
        public string str客队占它网时长_旧 { get; set; } = "(Lf_612D2B0311+Lf_612D2B0411)/1000";
        private List<GridDownLoadTimeSpeedInfo> gridDownLoadTimeSpeedInfoList = null;
        public Dictionary<string, List<GridDownLoadTimeSpeedInfo>> cityGridDownLoadTimeSpeedInfoListDic { get; set; }
        public Dictionary<string, Dictionary<GridTypeName, GridMutCarriersCountInfo>> cityMutCarriersCountInfoDic { get; set; }
        #endregion

        /// <summary>
        /// 设置查询指标
        /// </summary>
        /// <returns></returns>
        protected override bool getConditionBeforeQuery()
        {
            cityMutCarriersCountInfoDic = new Dictionary<string, Dictionary<GridTypeName, GridMutCarriersCountInfo>>();
            gridDownLoadTimeSpeedInfoList = new List<GridDownLoadTimeSpeedInfo>();
            cityGridDownLoadTimeSpeedInfoListDic = new Dictionary<string, List<GridDownLoadTimeSpeedInfo>>();
            if (!condition.CarrierTypes.Contains(1) || condition.CarrierTypes.Count != 2)
            {
                MessageBox.Show("运营商选择有误，请选择移动联通或者移动电信");
                return false;
            }
            strCarrName = "移动VS联通";
            if (condition.CarrierTypes.Contains(3))
            {
                str客队RSRP采样点数 = "Lf_612D2D0301";
                str客队RSRP采样点数_旧 = "Lf_612D0301";
                str客队占它网时长 = "(Lf_088A+Lf_088C+Lf_0890)/1000";
                str客队占它网时长_旧 = "(Lf_612D2B0211+Lf_612D2B0411)/1000";
                strCarrName = "移动VS电信";
            }
            this.evtIDSvrIDDic = new Dictionary<int, Dictionary<int, bool>>();
            this.imgCodeSvrIDDic = new Dictionary<string, Dictionary<int, bool>>();
            List<string> formulaSet = new List<string>();
            formulaSet.Add(strTDDDownTime);
            formulaSet.Add(strTDDDownSize);
            formulaSet.Add(strTDDSampleNum);
            formulaSet.Add(strFDDDownTime);
            formulaSet.Add(strFDDDownSize);
            formulaSet.Add(strFDDSampleNum);
            formulaSet.Add(str主队RSRP采样点数);
            formulaSet.Add(str主队RSRP采样点数_旧);
            formulaSet.Add(str主队脱网时长);
            formulaSet.Add(str主队脱网标记);
            formulaSet.Add(str主队占它网时长);
            formulaSet.Add(str主队占它网时长_旧);
            formulaSet.Add(str客队RSRP采样点数);
            formulaSet.Add(str客队RSRP采样点数_旧);
            formulaSet.Add(str客队脱网时长);
            formulaSet.Add(str客队脱网标记);
            formulaSet.Add(str客队占它网时长);
            formulaSet.Add(str客队占它网时长_旧);
            statImgIDSet = getTriadIDIgnoreServiceType(formulaSet);

            isShowSetForm = true;
            isNeedSearchFileInfo = false;
            isShowGridDataInfo = true;
            if (MainModel.User.DBID == -1 && mainModel.User.DBID == -1)
            {
                isInsertGridInfo = true;
                isAddInsetGridInfo = true;
                tableName = string.Format("gd_grid_downLoad_speed_Info_{0}", condition.Periods[0].BeginTime.ToString("yyyyMM"));
            }
            return true;
        }

        private class GridCompareData
        {
            public double dDownTime { get; set; } = 0;
            public double dDownSize { get; set; } = 0;
            public double dSampleNum { get; set; } = 0;
            public double dRSRP采样点数 { get; set; } = -1;
            public double d脱网标记 { get; set; } = 0;
            public double d占它网时长 { get; set; } = 0;
            public double dDownSpeed { get; set; } = 0;
        }

        /// <summary>
        /// 重写对比过程
        /// </summary>
        protected override void doCompare()
        {
            Dictionary<GridTypeName,GridMutCarriersCountInfo> gridMutCarriersCountInfoDic 
                = new Dictionary<GridTypeName,GridMutCarriersCountInfo>();
            List<GridDownLoadTimeSpeedInfo> gridDownLoadTimeSpeedInfoListTmp
                = new List<GridDownLoadTimeSpeedInfo>();
            foreach (ColorUnit cu in MainModel.CurGridColorUnitMatrix)
            {
                MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(cu.CenterLng, cu.CenterLat);
                GridTypeName gridName = strContainDbRect(grid.Bounds);
                if (gridName.strGridType != "" && gridName.strGridName != "") // 在区域内
                {
                    gridMutCarriersCountInfoDic = getGridMutCarriersCountInfoDic(gridMutCarriersCountInfoDic, gridDownLoadTimeSpeedInfoListTmp, cu, gridName);
                }
            }
            if (!cityMutCarriersCountInfoDic.ContainsKey(strCityName))
            {
                addCityMutCarriersCountInfoDic(gridMutCarriersCountInfoDic, gridDownLoadTimeSpeedInfoListTmp);
            }
        }

        private Dictionary<GridTypeName, GridMutCarriersCountInfo> getGridMutCarriersCountInfoDic(Dictionary<GridTypeName, GridMutCarriersCountInfo> gridMutCarriersCountInfoDic, List<GridDownLoadTimeSpeedInfo> gridDownLoadTimeSpeedInfoListTmp, ColorUnit cu, GridTypeName gridName)
        {
            if (!gridMutCarriersCountInfoDic.ContainsKey(gridName))
            {
                gridMutCarriersCountInfoDic[gridName] = new GridMutCarriersCountInfo();
                gridMutCarriersCountInfoDic[gridName].StrCity = strCityName;
                gridMutCarriersCountInfoDic[gridName].StrGridType = gridName.strGridType;
                gridMutCarriersCountInfoDic[gridName].StrGridName = gridName.strGridName;
            }
            gridMutCarriersCountInfoDic[gridName].IAllGrid++;
            StatDataLTE dataStatTDD = cu.DataHub.GetStatData(typeof(StatDataLTE)) as StatDataLTE;
            StatDataLTE_FDD dataStatFDD = cu.DataHub.GetStatData(typeof(StatDataLTE_FDD)) as StatDataLTE_FDD;
            if (dataStatTDD != null)
            {
                gridMutCarriersCountInfoDic[gridName].IHostGrid++;
            }
            if (dataStatFDD != null)
            {
                gridMutCarriersCountInfoDic[gridName].IGuestGrid++;
            }

            GridCompareData host = new GridCompareData();
            GridCompareData guset = new GridCompareData();
            if (dataStatTDD != null)
            {
                setBaseData(cu, host);
            }
            if (dataStatFDD != null)
            {
                setBaseData(cu, guset);
            }
            setDownSpeed(host, guset);
            addGridDownLoadTimeSpeedInfoListTmp(gridDownLoadTimeSpeedInfoListTmp, cu, gridName, host, guset);
            bool isValid = JudgeValidInfo(host, guset);
            if (isValid)
            {
                gridMutCarriersCountInfoDic = addFinalInfo(gridMutCarriersCountInfoDic, gridName, host, guset);
            }

            return gridMutCarriersCountInfoDic;
        }

        private void setBaseData(ColorUnit cu, GridCompareData gridCompareData)
        {
            gridCompareData.dDownTime = cu.DataHub.CalcValueByFormula(strFDDDownTime);
            gridCompareData.dDownSize = cu.DataHub.CalcValueByFormula(strFDDDownSize);
            gridCompareData.dSampleNum = cu.DataHub.CalcValueByFormula(strFDDSampleNum);
            gridCompareData.d脱网标记 = cu.DataHub.CalcValueByFormula(str客队脱网标记);
            if (condition.Periods[0].IBeginTime > 1430409599)//5月份之前的用旧公式
            {
                gridCompareData.dRSRP采样点数 = cu.DataHub.CalcValueByFormula(str客队RSRP采样点数);
                gridCompareData.d占它网时长 = cu.DataHub.CalcValueByFormula(str客队占它网时长);
            }
            else
            {
                gridCompareData.dRSRP采样点数 = cu.DataHub.CalcValueByFormula(str客队RSRP采样点数_旧);
                gridCompareData.d占它网时长 = cu.DataHub.CalcValueByFormula(str客队占它网时长_旧);
            }
        }

        private static void setDownSpeed(GridCompareData host, GridCompareData guset)
        {
            if (host.dDownTime > 0 && host.dDownSize >= 0)
            {
                host.dDownSpeed = Math.Round(host.dDownSize / host.dDownTime, 4);
            }
            if (guset.dDownTime > 0 && guset.dDownSize >= 0)
            {
                guset.dDownSpeed = Math.Round(guset.dDownSize / guset.dDownTime, 4);
            }
        }

        private void addGridDownLoadTimeSpeedInfoListTmp(List<GridDownLoadTimeSpeedInfo> gridDownLoadTimeSpeedInfoListTmp, ColorUnit cu, GridTypeName gridName, GridCompareData host, GridCompareData guset)
        {
            if (GetShowGridDataInfo || isInsertGridInfo)
            {
                GridDownLoadTimeSpeedInfo gridDownLoadTimeSpeedInfo = new GridDownLoadTimeSpeedInfo();
                gridDownLoadTimeSpeedInfo.StrCityInfo = strCityName;
                gridDownLoadTimeSpeedInfo.StrCarrName = strCarrName;
                gridDownLoadTimeSpeedInfo.StrGridType = gridName.strGridType;
                gridDownLoadTimeSpeedInfo.StrGridName = gridName.strGridName;
                gridDownLoadTimeSpeedInfo.StrGirdCenterInfo = cu.CenterLng + "_" + cu.CenterLat;
                gridDownLoadTimeSpeedInfo.Itllng = int.Parse((cu.LTLng * 10000000).ToString());
                gridDownLoadTimeSpeedInfo.Itllat = int.Parse((cu.LTLat * 10000000).ToString());
                gridDownLoadTimeSpeedInfo.Icentlng = int.Parse((cu.CenterLng * 10000000).ToString());
                gridDownLoadTimeSpeedInfo.Icentlat = int.Parse((cu.CenterLat * 10000000).ToString());
                gridDownLoadTimeSpeedInfo.DTDDDownTime = host.dDownTime;
                gridDownLoadTimeSpeedInfo.DTDDDownSize = host.dDownSize;
                gridDownLoadTimeSpeedInfo.DTDDDownSpeed = host.dDownSpeed * 1000;
                gridDownLoadTimeSpeedInfo.DTDDSampleNum = host.dSampleNum;
                gridDownLoadTimeSpeedInfo.D主队RSRP采样点数 = host.dRSRP采样点数;
                gridDownLoadTimeSpeedInfo.D主队脱网标记 = host.d脱网标记;
                gridDownLoadTimeSpeedInfo.D主队占它网时长 = host.d占它网时长;
                gridDownLoadTimeSpeedInfo.DFDDDownTime = guset.dDownTime;
                gridDownLoadTimeSpeedInfo.DFDDDownSize = guset.dDownSize;
                gridDownLoadTimeSpeedInfo.DFDDDownSpeed = guset.dDownSpeed * 1000;
                gridDownLoadTimeSpeedInfo.DFDDSampleNum = guset.dSampleNum;
                gridDownLoadTimeSpeedInfo.D客队RSRP采样点数 = guset.dRSRP采样点数;
                gridDownLoadTimeSpeedInfo.D客队脱网标记 = guset.d脱网标记;
                gridDownLoadTimeSpeedInfo.D客队占它网时长 = guset.d占它网时长;
                gridDownLoadTimeSpeedInfoListTmp.Add(gridDownLoadTimeSpeedInfo);
            }
        }

        private bool JudgeValidInfo(GridCompareData host, GridCompareData guset)
        {
            if (host.dRSRP采样点数 == -1 || guset.dRSRP采样点数 == -1)
            {
                //任意一方无信息
                return false;
            }
            if ((host.dDownTime <= 0 && guset.dDownTime <= 0) && (((host.d脱网标记 + host.d占它网时长) == 0 && host.dRSRP采样点数 > 0)
             && ((guset.d脱网标记 + guset.d占它网时长) == 0 && guset.dRSRP采样点数 > 0)))
            {
                //均无业务且均不脱网
                return false;
            }
            bool isValid = judgeNoServiceAndOffNet(host, guset);
            if(isValid)
            {
                return false;
            }

            if ((host.dDownTime > 0 && (guset.dDownTime <= 0 && (guset.d脱网标记 + guset.d占它网时长) == 0 && guset.dRSRP采样点数 > 0))
                || (guset.dDownTime > 0 && (host.dDownTime <= 0 && (host.d脱网标记 + host.d占它网时长) == 0 && host.dRSRP采样点数 > 0)))
            {
                //一方存在下载业务，另一方既无业务且不脱网
                return false;
            }
            return true;
        }

        private bool judgeNoServiceAndOffNet(GridCompareData host, GridCompareData guset)
        {
            if ((host.dDownTime <= 0 && guset.dDownTime <= 0) && ((host.dRSRP采样点数 == 0 && guset.dRSRP采样点数 == 0)
                || ((host.d脱网标记 + host.d占它网时长) > 0 && (guset.d脱网标记 + guset.d占它网时长) > 0)
                || ((host.d脱网标记 + host.d占它网时长) > 0 && guset.dRSRP采样点数 == 0)
                || ((guset.d脱网标记 + guset.d占它网时长) > 0 && host.dRSRP采样点数 == 0)))
            {
                //均无业务且均脱网
                return true;
            }
            return false;
        }

        private Dictionary<GridTypeName, GridMutCarriersCountInfo> addFinalInfo(Dictionary<GridTypeName, GridMutCarriersCountInfo> gridMutCarriersCountInfoDic, GridTypeName gridName, GridCompareData host, GridCompareData guset)
        {
            gridMutCarriersCountInfoDic[gridName].ICompareGrid++;

            if (host.dDownTime <= 0 && guset.dDownTime <= 0)
            {
                if ((host.d脱网标记 + host.d占它网时长) > 0 || host.dRSRP采样点数 == 0)
                {
                    gridMutCarriersCountInfoDic[gridName].I主队脱网栅格数++;
                    gridMutCarriersCountInfoDic[gridName].IHostWeakGrid++;
                    gridMutCarriersCountInfoDic[gridName].IHostWeakGridCurMonth++;
                    GridOff_NetSumInfo(ref gridMutCarriersCountInfoDic, gridName, "TDD", host.d脱网标记, host.d占它网时长, host.dRSRP采样点数);
                }
                else
                {
                    gridMutCarriersCountInfoDic[gridName].I客队脱网栅格数++;
                    gridMutCarriersCountInfoDic[gridName].IHostGoodGrid++;
                    gridMutCarriersCountInfoDic[gridName].IHostGoodGridCurMonth++;
                    GridOff_NetSumInfo(ref gridMutCarriersCountInfoDic, gridName, "FDD", guset.d脱网标记, guset.d占它网时长, guset.dRSRP采样点数);
                }
            }
            else if (host.dDownTime > 0 && guset.dDownTime <= 0)
            {
                gridMutCarriersCountInfoDic[gridName].I客队脱网栅格数++;
                gridMutCarriersCountInfoDic[gridName].IHostGoodGrid++;
                gridMutCarriersCountInfoDic[gridName].IHostGoodGridCurMonth++;
                GridOff_NetSumInfo(ref gridMutCarriersCountInfoDic, gridName, "FDD", guset.d脱网标记, guset.d占它网时长, guset.dRSRP采样点数);
            }
            else if (host.dDownTime <= 0 && guset.dDownTime > 0)
            {
                gridMutCarriersCountInfoDic[gridName].I主队脱网栅格数++;
                gridMutCarriersCountInfoDic[gridName].IHostWeakGrid++;
                gridMutCarriersCountInfoDic[gridName].IHostWeakGridCurMonth++;
                GridOff_NetSumInfo(ref gridMutCarriersCountInfoDic, gridName, "TDD", host.d脱网标记, host.d占它网时长, host.dRSRP采样点数);
            }
            else
            {
                setOtherSituations(gridMutCarriersCountInfoDic, gridName, host.dDownSpeed, guset.dDownSpeed);
            }

            return gridMutCarriersCountInfoDic;
        }

        private void setOtherSituations(Dictionary<GridTypeName, GridMutCarriersCountInfo> gridMutCarriersCountInfoDic, GridTypeName gridName, double dHostDownSpeed, double dGuestDownSpeed)
        {
            if (dHostDownSpeed > dSpeed && dGuestDownSpeed > dSpeed)
            {
                if (dHostDownSpeed >= dGuestDownSpeed * dPercent)
                {
                    gridMutCarriersCountInfoDic[gridName].IMoreGoodGrid++;
                }
                else
                {
                    gridMutCarriersCountInfoDic[gridName].IMoreWeakGrid++;
                }
            }
            else if (dHostDownSpeed > dSpeed && dGuestDownSpeed <= dSpeed)
            {
                gridMutCarriersCountInfoDic[gridName].IHostMoreGuestLess++;
                gridMutCarriersCountInfoDic[gridName].IHostGoodGrid++;
                gridMutCarriersCountInfoDic[gridName].IHostGoodGridCurMonth++;
            }
            else if (dHostDownSpeed <= dSpeed && dGuestDownSpeed > dSpeed)
            {
                addHostLessGuestMore(gridMutCarriersCountInfoDic, gridName, dHostDownSpeed, dGuestDownSpeed);
            }
            else if (dHostDownSpeed <= dSpeed && dGuestDownSpeed <= dSpeed)
            {
                addLessGrid(gridMutCarriersCountInfoDic, gridName, dHostDownSpeed, dGuestDownSpeed);
            }
        }

        private void addHostLessGuestMore(Dictionary<GridTypeName, GridMutCarriersCountInfo> gridMutCarriersCountInfoDic, GridTypeName gridName, double dHostDownSpeed, double dGuestDownSpeed)
        {
            if (dHostDownSpeed >= dGuestDownSpeed * dPercent)
            {
                gridMutCarriersCountInfoDic[gridName].IHostLessGuestMore++;
                gridMutCarriersCountInfoDic[gridName].IHostGoodGrid++;
                gridMutCarriersCountInfoDic[gridName].IHostGoodGridCurMonth++;
            }
            else
            {
                gridMutCarriersCountInfoDic[gridName].IHostLessGuestMoreWeak++;
                gridMutCarriersCountInfoDic[gridName].IHostWeakGrid++;
                gridMutCarriersCountInfoDic[gridName].IHostWeakGridCurMonth++;
            }
        }

        private void addLessGrid(Dictionary<GridTypeName, GridMutCarriersCountInfo> gridMutCarriersCountInfoDic, GridTypeName gridName, double dHostDownSpeed, double dGuestDownSpeed)
        {
            if (dHostDownSpeed >= dGuestDownSpeed * dPercent)
            {
                gridMutCarriersCountInfoDic[gridName].ILessGoodGrid++;
                gridMutCarriersCountInfoDic[gridName].IHostGoodGrid++;
                gridMutCarriersCountInfoDic[gridName].IHostGoodGridCurMonth++;
            }
            else
            {
                gridMutCarriersCountInfoDic[gridName].ILessWeakGrid++;
                gridMutCarriersCountInfoDic[gridName].IHostWeakGrid++;
                gridMutCarriersCountInfoDic[gridName].IHostWeakGridCurMonth++;
            }
        }

        private void addCityMutCarriersCountInfoDic(Dictionary<GridTypeName, GridMutCarriersCountInfo> gridMutCarriersCountInfoDic, List<GridDownLoadTimeSpeedInfo> gridDownLoadTimeSpeedInfoListTmp)
        {
            cityMutCarriersCountInfoDic.Add(strCityName, gridMutCarriersCountInfoDic);
            cityGridDownLoadTimeSpeedInfoListDic.Add(strCityName, gridDownLoadTimeSpeedInfoListTmp);
            if (isInsertGridInfo && gridDownLoadTimeSpeedInfoListTmp.Count > 0)
            {
                int iCityIDTmp = mainModel.DistrictID;
                mainModel.DistrictID = iCurCityID;
                try
                {
                    Dictionary<CenterLongLat, GridDownLoadTimeSpeedInfo> gridDownLoadTimeSpeedInfoDic
                        = new Dictionary<CenterLongLat, GridDownLoadTimeSpeedInfo>();
                    if (isAddInsetGridInfo)
                    {
                        DIYSQLGridDownloadInfo diySQLGridDownloadInfo
                         = new DIYSQLGridDownloadInfo(mainModel, condition.Periods[0].BeginTime.ToString("yyyyMM"), strCarrName
                             , gridDownLoadTimeSpeedInfoListTmp[0].StrGridType);
                        diySQLGridDownloadInfo.Query();
                        gridDownLoadTimeSpeedInfoDic = diySQLGridDownloadInfo.gridDownLoadTimeSpeedInfoDic;
                    }
                    WaitBox.Text = "正在BCP入库，当前地市= " + strCityName;
                    WaitBox.ProgressPercent = 78;
                    BackgroundFuncResultWriter.Instance.WritGridInfo(tableName
                        , GridDownLoadTimeSpeedInfoCompare(gridDownLoadTimeSpeedInfoListTmp, gridDownLoadTimeSpeedInfoDic));
                }
                catch (System.Exception ex)
                {
                    throw (new Exception("BCP入库异常，当前地市=" + strCityName + " 月表 " + tableName + " " + ex.ToString()));
                }
                finally
                {
                    mainModel.DistrictID = iCityIDTmp;
                }
            }
        }

        public List<GridDownLoadTimeSpeedInfo> GridDownLoadTimeSpeedInfoCompare(List<GridDownLoadTimeSpeedInfo> gridDownLoadTimeSpeedInfoListTmp
            ,Dictionary<CenterLongLat, GridDownLoadTimeSpeedInfo> gridDownLoadTimeSpeedInfoDic)
        {
            List<GridDownLoadTimeSpeedInfo> gridDownLoadTimeSpeedInfoCompare
                = new List<GridDownLoadTimeSpeedInfo>();

            foreach (GridDownLoadTimeSpeedInfo info in gridDownLoadTimeSpeedInfoListTmp)
            {
                if (!gridDownLoadTimeSpeedInfoDic.ContainsKey(info.centerLongLat))
                {
                    gridDownLoadTimeSpeedInfoDic.Add(info.centerLongLat, info);
                }
                else
                {
                    gridDownLoadTimeSpeedInfoDic[info.centerLongLat].DTDDDownTime += info.DTDDDownTime;
                    gridDownLoadTimeSpeedInfoDic[info.centerLongLat].DTDDDownSize += info.DTDDDownSize;
                    gridDownLoadTimeSpeedInfoDic[info.centerLongLat].DTDDDownSpeed *= 1000;
                    gridDownLoadTimeSpeedInfoDic[info.centerLongLat].DTDDDownSpeed += info.DTDDDownSpeed;
                    gridDownLoadTimeSpeedInfoDic[info.centerLongLat].DTDDSampleNum += info.DTDDSampleNum;
                    gridDownLoadTimeSpeedInfoDic[info.centerLongLat].D主队RSRP采样点数 += info.D主队RSRP采样点数;
                    gridDownLoadTimeSpeedInfoDic[info.centerLongLat].D主队脱网标记 += info.D主队脱网标记;
                    gridDownLoadTimeSpeedInfoDic[info.centerLongLat].D主队占它网时长 += info.D主队占它网时长;
                    gridDownLoadTimeSpeedInfoDic[info.centerLongLat].DFDDDownTime += info.DFDDDownTime;
                    gridDownLoadTimeSpeedInfoDic[info.centerLongLat].DFDDDownSize += info.DFDDDownSize;
                    gridDownLoadTimeSpeedInfoDic[info.centerLongLat].DFDDDownSpeed *= 1000;
                    gridDownLoadTimeSpeedInfoDic[info.centerLongLat].DFDDDownSpeed += info.DFDDDownSpeed;
                    gridDownLoadTimeSpeedInfoDic[info.centerLongLat].DFDDSampleNum += info.DFDDSampleNum;
                    gridDownLoadTimeSpeedInfoDic[info.centerLongLat].D客队RSRP采样点数 += info.D客队RSRP采样点数;
                    gridDownLoadTimeSpeedInfoDic[info.centerLongLat].D客队脱网标记 += info.D客队脱网标记;
                    gridDownLoadTimeSpeedInfoDic[info.centerLongLat].D客队占它网时长 += info.D客队占它网时长;
                }
            }
            gridDownLoadTimeSpeedInfoCompare.AddRange(gridDownLoadTimeSpeedInfoDic.Values);
            return gridDownLoadTimeSpeedInfoCompare;
        }

        protected override void doCombine()
        {
            // Method intentionally left empty.
        }
        /// <summary>
        /// 显示结果集
        /// </summary>
        protected override void fireShowResult()
        {
            if (cityMutCarriersCountInfoDic.Count == 0)
            {
                MessageBox.Show("没有结果");
                return;
            }
            GridCompareCountForm showForm = null;
            object obj = MainModel.GetObjectFromBlackboard(typeof(GridCompareCountForm).FullName);
            showForm = obj == null ? null : obj as GridCompareCountForm;
            if (showForm == null || showForm.IsDisposed)
            {
                showForm = new GridCompareCountForm(MainModel, isMonthForward);
            }

            foreach (string city in cityGridDownLoadTimeSpeedInfoListDic.Keys)
            {
                gridDownLoadTimeSpeedInfoList.AddRange(cityGridDownLoadTimeSpeedInfoListDic[city]);
            }

            List<GridMutCarriersCountInfo> gridInfoList = new List<GridMutCarriersCountInfo>();
            Dictionary<string, List<GridTypeName>> cityGridTypeNameDic = new Dictionary<string, List<GridTypeName>>();
            GridMutCarriersCountInfo sumInfo = new GridMutCarriersCountInfo();

            sumInfo.StrCity = "汇总";
            sumInfo.StrGridType = "汇总";
            sumInfo.StrGridName = "汇总";
            addGridNameSumInfo(gridInfoList, cityGridTypeNameDic, ref sumInfo);
            addGridTypeSumInfo(gridInfoList, cityGridTypeNameDic);
            addSumInfoCity(gridInfoList);
            sumInfo.ISN = gridInfoList.Count + 1;
            gridInfoList.Add(sumInfo);
            cityGridCompareInfoDic.Clear();
            showForm.FillData(gridInfoList, gridDownLoadTimeSpeedInfoList, GetShowGridDataInfo);
            showForm.Show(MainModel.MainForm);
        }

        private void addGridNameSumInfo(List<GridMutCarriersCountInfo> gridInfoList, Dictionary<string, List<GridTypeName>> cityGridTypeNameDic, ref GridMutCarriersCountInfo sumInfo)
        {
            foreach (string strCity in cityMutCarriersCountInfoDic.Keys)
            {
                if (!cityGridTypeNameDic.ContainsKey(strCity))
                {
                    cityGridTypeNameDic[strCity] = new List<GridTypeName>();
                }
                foreach (GridTypeName gridTypeName in cityMutCarriersCountInfoDic[strCity].Keys)
                {
                    GridTypeName gridType = new GridTypeName();
                    gridType.strGridType = gridTypeName.strGridType;
                    gridType.strGridName = "汇总";
                    if (!cityGridTypeNameDic[strCity].Contains(gridType))
                    {
                        cityGridTypeNameDic[strCity].Add(gridType);
                    }
                    GridKeySumInfo(ref sumInfo, strCity, gridTypeName);
                    cityMutCarriersCountInfoDic[strCity][gridTypeName].ISN = gridInfoList.Count + 1;
                    gridInfoList.Add(cityMutCarriersCountInfoDic[strCity][gridTypeName]);
                }
            }
        }

        private void addGridTypeSumInfo(List<GridMutCarriersCountInfo> gridInfoList, Dictionary<string, List<GridTypeName>> cityGridTypeNameDic)
        {
            foreach (string strCity in cityMutCarriersCountInfoDic.Keys)
            {
                foreach (GridTypeName gridKey in cityGridTypeNameDic[strCity])
                {
                    GridMutCarriersCountInfo sumInfoKey = new GridMutCarriersCountInfo();
                    sumInfoKey.StrCity = strCity;
                    sumInfoKey.StrGridType = gridKey.strGridType;
                    sumInfoKey.StrGridName = gridKey.strGridName;
                    foreach (GridTypeName gridTypeName in cityMutCarriersCountInfoDic[strCity].Keys)
                    {
                        GridTypeName gridTypeNameTmp = new GridTypeName();
                        gridTypeNameTmp.strGridType = gridTypeName.strGridType;
                        gridTypeNameTmp.strGridName = "汇总";
                        if (gridKey.Equals(gridTypeNameTmp))
                        {
                            GridKeySumInfo(ref sumInfoKey, strCity, gridTypeName);
                        }
                    }
                    sumInfoKey.ISN = gridInfoList.Count + 1;
                    gridInfoList.Add(sumInfoKey);
                }
            }
        }

        private void addSumInfoCity(List<GridMutCarriersCountInfo> gridInfoList)
        {
            foreach (string strCity in cityMutCarriersCountInfoDic.Keys)
            {
                GridMutCarriersCountInfo sumInfoCity = new GridMutCarriersCountInfo();
                sumInfoCity.StrCity = strCity;
                sumInfoCity.StrGridType = "汇总";
                sumInfoCity.StrGridName = "汇总";
                foreach (GridTypeName gridTypeName in cityMutCarriersCountInfoDic[strCity].Keys)
                {
                    GridKeySumInfo(ref sumInfoCity, strCity, gridTypeName);
                }
                sumInfoCity.ISN = gridInfoList.Count + 1;
                gridInfoList.Add(sumInfoCity);
            }
        }

        public void GridKeySumInfo(ref GridMutCarriersCountInfo gridSumInfo, string strCity, GridTypeName gridTypeName)
        {
            gridSumInfo.IAllGrid += cityMutCarriersCountInfoDic[strCity][gridTypeName].IAllGrid;
            gridSumInfo.IHostHistoryGrid += cityMutCarriersCountInfoDic[strCity][gridTypeName].IHostHistoryGrid;
            gridSumInfo.IGuestHistoryGrid += cityMutCarriersCountInfoDic[strCity][gridTypeName].IGuestHistoryGrid;
            for (int i = 0; i < cityMutCarriersCountInfoDic[strCity][gridTypeName].IHostHistoryGridList.Count;i++ )
            {
                if (gridSumInfo.IHostHistoryGridList.Count != cityMutCarriersCountInfoDic[strCity][gridTypeName].IHostHistoryGridList.Count)
                {
                    gridSumInfo.IHostHistoryGridList.Add(0);
                    gridSumInfo.IGuestHistoryGridList.Add(0);
                }
                gridSumInfo.IHostHistoryGridList[i] += cityMutCarriersCountInfoDic[strCity][gridTypeName].IHostHistoryGridList[i];
                gridSumInfo.IGuestHistoryGridList[i] += cityMutCarriersCountInfoDic[strCity][gridTypeName].IGuestHistoryGridList[i];
            }
            gridSumInfo.ICompareGrid += cityMutCarriersCountInfoDic[strCity][gridTypeName].ICompareGrid;
            gridSumInfo.I主队脱网栅格数 += cityMutCarriersCountInfoDic[strCity][gridTypeName].I主队脱网栅格数;
            gridSumInfo.I主队脱网栅格数_脱网标记 += cityMutCarriersCountInfoDic[strCity][gridTypeName].I主队脱网栅格数_脱网标记;
            gridSumInfo.I主队脱网栅格数_占它网时长 += cityMutCarriersCountInfoDic[strCity][gridTypeName].I主队脱网栅格数_占它网时长;
            gridSumInfo.I主队脱网栅格数_无RSRP采样点 += cityMutCarriersCountInfoDic[strCity][gridTypeName].I主队脱网栅格数_无RSRP采样点;
            gridSumInfo.I客队脱网栅格数 += cityMutCarriersCountInfoDic[strCity][gridTypeName].I客队脱网栅格数;
            gridSumInfo.I客队脱网栅格数_脱网标记 += cityMutCarriersCountInfoDic[strCity][gridTypeName].I客队脱网栅格数_脱网标记;
            gridSumInfo.I客队脱网栅格数_占它网时长 += cityMutCarriersCountInfoDic[strCity][gridTypeName].I客队脱网栅格数_占它网时长;
            gridSumInfo.I客队脱网栅格数_无RSRP采样点 += cityMutCarriersCountInfoDic[strCity][gridTypeName].I客队脱网栅格数_无RSRP采样点;
            gridSumInfo.IGuestGrid += cityMutCarriersCountInfoDic[strCity][gridTypeName].IGuestGrid;
            gridSumInfo.IHostGoodGrid += cityMutCarriersCountInfoDic[strCity][gridTypeName].IHostGoodGrid;
            gridSumInfo.IHostGoodGridCurMonth += cityMutCarriersCountInfoDic[strCity][gridTypeName].IHostGoodGridCurMonth;
            gridSumInfo.IHostGrid += cityMutCarriersCountInfoDic[strCity][gridTypeName].IHostGrid;
            gridSumInfo.IHostLessGuestMore += cityMutCarriersCountInfoDic[strCity][gridTypeName].IHostLessGuestMore;
            gridSumInfo.IHostLessGuestMoreWeak += cityMutCarriersCountInfoDic[strCity][gridTypeName].IHostLessGuestMoreWeak;
            gridSumInfo.IHostMoreGuestLess += cityMutCarriersCountInfoDic[strCity][gridTypeName].IHostMoreGuestLess;
            gridSumInfo.IHostWeakGrid += cityMutCarriersCountInfoDic[strCity][gridTypeName].IHostWeakGrid;
            gridSumInfo.IHostWeakGridCurMonth += cityMutCarriersCountInfoDic[strCity][gridTypeName].IHostWeakGridCurMonth;
            gridSumInfo.ILessGoodGrid += cityMutCarriersCountInfoDic[strCity][gridTypeName].ILessGoodGrid;
            gridSumInfo.ILessWeakGrid += cityMutCarriersCountInfoDic[strCity][gridTypeName].ILessWeakGrid;
            gridSumInfo.IMoreGoodGrid += cityMutCarriersCountInfoDic[strCity][gridTypeName].IMoreGoodGrid;
            gridSumInfo.IMoreWeakGrid += cityMutCarriersCountInfoDic[strCity][gridTypeName].IMoreWeakGrid;

            gridSumInfo.I主队本月下载栅格数 += cityMutCarriersCountInfoDic[strCity][gridTypeName].I主队本月下载栅格数;
            gridSumInfo.I客队本月下载栅格数 += cityMutCarriersCountInfoDic[strCity][gridTypeName].I客队本月下载栅格数;
            gridSumInfo.I本月对比总栅格数 += cityMutCarriersCountInfoDic[strCity][gridTypeName].I本月对比总栅格数;
            gridSumInfo.I含历史主队下载栅格数 += cityMutCarriersCountInfoDic[strCity][gridTypeName].I含历史主队下载栅格数;
            gridSumInfo.I含历史客队下载栅格数 += cityMutCarriersCountInfoDic[strCity][gridTypeName].I含历史客队下载栅格数;
        }

        public void GridOff_NetSumInfo(ref Dictionary<GridTypeName, GridMutCarriersCountInfo> gridSumInfoDic, GridTypeName gridName, string strNet
            , double d脱网标记, double d占它网时长, double dRSRP采样点数)
        {
            if (d脱网标记 > 0)
            {
                if (strNet.Equals("TDD"))
                {
                    gridSumInfoDic[gridName].I主队脱网栅格数_脱网标记++;
                }
                else
                {
                    gridSumInfoDic[gridName].I客队脱网栅格数_脱网标记++;
                }
            }
            else if (d占它网时长 > 0)
            {
                if (strNet.Equals("TDD"))
                {
                    gridSumInfoDic[gridName].I主队脱网栅格数_占它网时长++;
                }
                else
                {
                    gridSumInfoDic[gridName].I客队脱网栅格数_占它网时长++;
                }
            }
            else if (dRSRP采样点数 == 0)
            {
                if (strNet.Equals("TDD"))
                {
                    gridSumInfoDic[gridName].I主队脱网栅格数_无RSRP采样点++;
                }
                else
                {
                    gridSumInfoDic[gridName].I客队脱网栅格数_无RSRP采样点++;
                }
            }
        }
    }

    public class GridMutCarriersCountInfo
    {
        /// <summary>
        /// 序号
        /// </summary>
        public int ISN { get; set; }
        /// <summary>
        /// 地市
        /// </summary>
        public string StrCity { get; set; }
        /// <summary>
        /// 网格类型
        /// </summary>
        public string StrGridType { get; set; }
        /// <summary>
        /// 网格名称
        /// </summary>
        public string StrGridName { get; set; }
        /// <summary>
        /// 主队栅格数
        /// </summary>
        public int IHostGrid { get; set; }
        /// <summary>
        /// 客队栅格数
        /// </summary>
        public int IGuestGrid { get; set; }
        /// <summary>
        /// 总栅格数
        /// </summary>
        public int IAllGrid { get; set; }
        /// <summary>
        /// 本月主队有下载的栅格数
        /// </summary>
        public int I主队本月下载栅格数 { get; set; }
        /// <summary>
        /// 本月客队有下载的栅格数
        /// </summary>
        public int I客队本月下载栅格数 { get; set; }
        /// <summary>
        /// 本月对比总栅格数
        /// </summary>
        public int I本月对比总栅格数 { get; set; }
        /// <summary>
        /// 本月不含脱网对比总栅格数
        /// </summary>
        public int I本月不含脱网对比总栅格数
        {
            get
            {
                return I本月对比总栅格数 - I主队脱网栅格数 - I客队脱网栅格数;
            }
        }
        /// <summary>
        /// 含历史不含脱网对比总栅格数
        /// </summary>
        public int I含历史不含脱网对比总栅格数
        {
            get
            {
                return ICompareGrid - I主队脱网栅格数 - I客队脱网栅格数;
            }
        }

        /// <summary>
        /// 本月含脱网对比栅格数占总栅格比例
        /// </summary>
        public string Str本月含脱网对比栅格数占总栅格比例
        {
            get
            {
                return (100.0 * I本月对比总栅格数 / IAllGrid).ToString("0.00") + "%";
            }
        }

        /// <summary>
        /// 含历史对比栅格数占总栅格比例
        /// </summary>
        public string Str含历史对比栅格数占总栅格比例
        {
            get
            {
                return (100.0 * ICompareGrid / IAllGrid).ToString("0.00") + "%";
            }
        }
        
        /// <summary>
        /// 含历史主队有下载的栅格数
        /// </summary>
        public int I含历史主队下载栅格数 { get; set; }
        /// <summary>
        /// 含历史客队有下载的栅格数
        /// </summary>
        public int I含历史客队下载栅格数 { get; set; }
        /// <summary>
        /// 主队历史栅格占含历史对比总栅格比例
        /// </summary>
        public string str主队占含历史对比总数比例
        {
            get
            {
                string strRate = "-";
                if (ICompareGrid > 0)
                {
                    strRate = Math.Round(100.0 * IHostHistoryGrid / ICompareGrid,4).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        /// <summary>
        /// 客队历史栅格数占含历史对比总栅格比例
        /// </summary>
        public string str客队占含历史对比总数比例
        {
            get
            {
                string strRate = "-";
                if (ICompareGrid > 0)
                {
                    strRate = Math.Round(100.0 * iGuestHistoryGrid / ICompareGrid, 4).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        /// <summary>
        /// 客队脱网栅格占总栅格比例
        /// </summary>
        public string str客队脱网占总栅格数比例
        {
            get
            {
                string strRate = "-";
                if (IGuestGrid > 0)
                {
                    strRate = Math.Round(100.0 * I客队脱网栅格数 / IGuestGrid, 4).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        /// <summary>
        /// 客队脱网栅格站占含历史对比栅格比例
        /// </summary>
        public string str客队脱网占含历史总栅格数比例
        {
            get
            {
                string strRate = "-";
                if (ICompareGrid > 0)
                {
                    strRate = Math.Round(100.0 * I客队脱网栅格数 / ICompareGrid, 4).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        public int I本月主优栅格总数_不含脱网
        {
            get
            {
                return IHostGoodGridCurMonth - I客队脱网栅格数;
            }
        }
        public int I本月主劣栅格总数_不含脱网
        {
            get
            {
                return IHostWeakGridCurMonth - I主队脱网栅格数;
            }
        }
        public string Str本月栅格优胜率_不含脱网
        {
            get
            {
                if ((I本月主优栅格总数_不含脱网 + I本月主劣栅格总数_不含脱网) > 0)
                {
                    return Math.Round(100.0 * I本月主优栅格总数_不含脱网
                        / (I本月主优栅格总数_不含脱网 + I本月主劣栅格总数_不含脱网), 2).ToString() + "%";
                }
                return "-";
            }
        }
        
        /// <summary>
        /// 主历史栅格数
        /// </summary>
        public int IHostHistoryGrid { get; set; }
        /// <summary>
        /// 主各段历史栅格数
        /// </summary>
        public string StrHostHistoryGrid
        {
            get
            {
                return getResult(IHostHistoryGridList, "-->");
            }
        }
        /// <summary>
        /// 主各段历史栅格数占比
        /// </summary>
        public string StrHostHistoryGridPercent
        {
            get
            {
                return getPercentResult(IHostHistoryGridList, ICompareGrid, "-->");
            }
        }
        /// <summary>
        /// 主队本月有下载栅格数占总栅格数比例
        /// </summary>
        public string StrHostDwonloadGridPercent
        {
            get
            {
                string strHost = "";
                strHost = (100.0 * I主队本月下载栅格数 / IHostGrid).ToString("0.00") + "%";
                return strHost;
            }
        }
        /// <summary>
        /// 主队含历史有下载栅格数占总栅格比例
        /// </summary>
        public string StrHostHistoryDwonloadGridPercent
        {
            get
            {
                string strHost = "";
                strHost = (100.0 * I含历史主队下载栅格数 / IHostGrid).ToString("0.00") + "%";
                return strHost;
            }
        }
        
        /// <summary>
        /// 主各段历史栅格数列表
        /// </summary>
        public List<int> IHostHistoryGridList { get; set; } = new List<int>();
        /// <summary>
        /// 主队历史字典
        /// </summary>
        public Dictionary<string, int> IHostHistoryGridDic { get; set; } = new Dictionary<string, int>();
        private readonly int iGuestHistoryGrid = 0;
        /// <summary>
        /// 客历史栅格数
        /// </summary>
        public int IGuestHistoryGrid { get; set; }
        /// <summary>
        ///客各段历史栅格数 
        /// </summary>
        public string StrGuestHistoryGrid
        {
            get
            {
                return getResult(IGuestHistoryGridList, "-->");
            }
        }
        /// <summary>
        ///客各段历史栅格数占比
        /// </summary>
        public string StrGuestHistoryGridPercent
        {
            get
            {
                return getPercentResult(IGuestHistoryGridList, ICompareGrid, "-->");
            }
        }

        private string getResult(List<int> dataList, string splitStr)
        {
            StringBuilder str = new StringBuilder();
            foreach (int iCount in dataList)
            {
                if (str.Length == 0)
                {
                    str.Append("" + iCount);
                }
                else
                {
                    str.Append(splitStr + iCount);
                }
            }
            return str.ToString();
        }

        private string getPercentResult(List<int> dataList, int appendData, string splitStr)
        {
            StringBuilder str = new StringBuilder();
            foreach (int iCount in dataList)
            {
                if (str.Length == 0)
                {
                    str.Append((100.0 * iCount / appendData).ToString("0.00"));
                }
                else
                {
                    str.Append(splitStr + (100.0 * iCount / appendData).ToString("0.00"));
                }
            }
            return str.ToString();
        }

        /// <summary>
        /// 客队本月有下载栅格数占总栅格数比例
        /// </summary>
        public string StrGuestDwonloadGridPercent
        {
            get
            {
                string strGuest = "";
                strGuest = (100.0 * I客队本月下载栅格数 / IGuestGrid).ToString("0.00") + "%";
                return strGuest;
            }
        }
        /// <summary>
        /// 客队含历史有下载栅格数占总栅格比例
        /// </summary>
        public string StrGuestHistoryDwonloadGridPercent
        {
            get
            {
                string strGuest = "";
                strGuest = (100.0 * I含历史客队下载栅格数 / IGuestGrid).ToString("0.00") + "%";
                return strGuest;
            }
        }
        
        /// <summary>
        /// 客各段历史栅格数列表
        /// </summary>
        public List<int> IGuestHistoryGridList { get; set; } = new List<int>();
        /// <summary>
        /// 客队历史字典
        /// </summary>
        public Dictionary<string, int> IGuestHistoryGridDic { get; set; } = new Dictionary<string, int>();
        /// <summary>
        /// 主队脱网栅格数
        /// </summary>
        public int I主队脱网栅格数 { get; set; }
        /// <summary>
        /// 主队脱网(脱网标记)
        /// </summary>
        public int I主队脱网栅格数_脱网标记 { get; set; }
        /// <summary>
        /// 主队脱网(占它网时长)
        /// </summary>
        public int I主队脱网栅格数_占它网时长 { get; set; }
        /// <summary>
        /// 主队脱网(无RSRP采样点)
        /// </summary>
        public int I主队脱网栅格数_无RSRP采样点 { get; set; }
        /// <summary>
        /// 客队脱网栅格数
        /// </summary>
        public int I客队脱网栅格数 { get; set; }
        /// <summary>
        /// 客队脱网(脱网标记)
        /// </summary>
        public int I客队脱网栅格数_脱网标记 { get; set; }
        /// <summary>
        /// 客队脱网(占它网时长)
        /// </summary>
        public int I客队脱网栅格数_占它网时长 { get; set; }
        /// <summary>
        /// 客队脱网(无RSRP采样点)
        /// </summary>
        public int I客队脱网栅格数_无RSRP采样点 { get; set; }
        /// <summary>
        /// 对比栅格数
        /// </summary>
        public int ICompareGrid { get; set; }
        /// <summary>
        /// 同高优
        /// </summary>
        public int IMoreGoodGrid { get; set; }
        /// <summary>
        /// 同高劣
        /// </summary>
        public int IMoreWeakGrid { get; set; }
        /// <summary>
        /// 同高优胜率
        /// </summary>
        public string StrMoreGoodRate
        {
            get
            {
                string strRate = "-";
                if (IMoreGoodGrid + IMoreWeakGrid > 0)
                {
                    strRate = (100.0 * IMoreGoodGrid / (IMoreGoodGrid + IMoreWeakGrid)).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
        /// <summary>
        /// 同低优
        /// </summary>
        public int ILessGoodGrid { get; set; }
        /// <summary>
        /// 同低劣
        /// </summary>
        public int ILessWeakGrid { get; set; }
        /// <summary>
        /// 同低优胜率
        /// </summary>
        public string StrLessGoodRate
        {
            get
            {
                string strRate = "-";
                if (ILessGoodGrid + ILessWeakGrid > 0)
                {
                    strRate = (100.0 * ILessGoodGrid / (ILessGoodGrid + ILessWeakGrid)).ToString("0.00") + "%";
                }
                return strRate; 
            }
        }
        /// <summary>
        /// 主高客低
        /// </summary>
        public int IHostMoreGuestLess { get; set; }
        /// <summary>
        /// 主低客高优
        /// </summary>
        public int IHostLessGuestMore { get; set; }
        /// <summary>
        /// 主低客高劣
        /// </summary>
        public int IHostLessGuestMoreWeak { get; set; }
        /// <summary>
        /// 主优栅格总数
        /// </summary>
        public int IHostGoodGrid { get; set; }
        /// <summary>
        /// 本月主优栅格总数
        /// </summary>
        public int IHostGoodGridCurMonth { get; set; }
        /// <summary>
        /// 主劣栅格总数
        /// </summary>
        public int IHostWeakGrid { get; set; }
        /// <summary>
        /// 本月主劣栅格总数
        /// </summary>
        public int IHostWeakGridCurMonth { get; set; }
        /// <summary>
        /// 栅格优胜率
        /// </summary>
        public string StrHostGoodRate
        {
            get 
            {
                string strRate = "-";
                if (IHostGoodGrid + IHostWeakGrid > 0)
                {
                    strRate = (100.0 * IHostGoodGrid / (IHostGoodGrid + IHostWeakGrid)).ToString("0.00") + "%" ;
                }
                return strRate;
            }
        }
        /// <summary>
        /// 本月栅格优胜率
        /// </summary>
        public string StrHostGoodRateCurMonth
        {
            get
            {
                string strRate = "-";
                if (IHostGoodGridCurMonth + IHostWeakGridCurMonth > 0)
                {
                    strRate = (100.0 * IHostGoodGridCurMonth / (IHostGoodGridCurMonth + IHostWeakGridCurMonth)).ToString("0.00") + "%";
                }
                return strRate;
            }
        }
    }

    public class GridDownLoadTimeSpeedInfo
    {
        public string StrCityInfo { get; set; }
        public string StrCarrName { get; set; }
        public string StrGridType { get; set; }
        public string StrGridName { get; set; }
        public int Itllng { get; set; }
        public int Itllat { get; set; }
        public int Icentlng { get; set; }
        public int Icentlat { get; set; }
        public string StrGirdCenterInfo { get; set; }
        public string StrMonth { get; set; }

        public string StrGridTDDStatstatus
        {
           get
            {
                string statstatus = "";
                if (D主队RSRP采样点数 == -1)
                {
                    statstatus = "TDD无信息";
                }
                else if (DTDDDownTime > 0)
                {
                    statstatus = "TDD下载业务";
                } 
                else
                {
                    if ((D主队脱网标记 + D主队占它网时长) > 0
                        || D主队RSRP采样点数 == 0)
                    {
                        statstatus = "TDD脱网";
                    }
                    else
                    {
                        statstatus = "TDD无业务不脱网";
                    }
                }
                return statstatus;
            }
        }

        public string StrGridFDDStatstatus
        {
            get
            {
                string statstatus = "";
                if (D客队RSRP采样点数 == -1)
                {
                    statstatus = "FDD无信息";
                }
                else if (DFDDDownTime > 0)
                {
                    statstatus = "FDD下载业务";
                }
                else
                {
                    if ((D客队脱网标记 + D客队占它网时长) > 0
                        || D客队RSRP采样点数 == 0)
                    {
                        statstatus = "FDD脱网";
                    }
                    else
                    {
                        statstatus = "FDD无业务不脱网";
                    }
                }
                return statstatus;
            }
        }
        
        public double DTDDDownTime { get; set; }
        public double DTDDDownSize { get; set; }
        public double DTDDDownSpeed { get; set; }
        public double DTDDSampleNum { get; set; }
        public double D主队RSRP采样点数 { get; set; }
        public double D主队脱网时长 { get; set; }
        public double D主队脱网标记 { get; set; }
        public double D主队占它网时长 { get; set; }
        public double DFDDDownTime { get; set; }
        public double DFDDDownSize { get; set; }
        public double DFDDDownSpeed { get; set; }
        public double DFDDSampleNum { get; set; }
        public double D客队RSRP采样点数 { get; set; }
        public double D客队脱网时长 { get; set; }
        public double D客队脱网标记 { get; set; }
        public double D客队占它网时长 { get; set; }
        /// <summary>
        /// 主队使用的轮次数据
        /// </summary>
        public string Str主队数据源 { get; set; } = "";
        /// <summary>
        /// 客队使用的轮次数据
        /// </summary>
        public string Str客队数据源 { get; set; } = "";

        public bool 主队计算标记
        {
            get
            {
                if (this.DTDDDownTime > 0 //有业务
                    || (this.D主队RSRP采样点数 == 0 || (this.D主队脱网标记 + this.D主队占它网时长) > 0)) //或脱网
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
        }

        public bool 客队计算标记
        {
            get
            {
                if (this.DFDDDownTime > 0  //有业务
                    || (this.D客队RSRP采样点数 == 0 || (this.D客队脱网标记 + this.D客队占它网时长) > 0)) //或脱网
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
        }

        public CenterLongLat centerLongLat
        {
            get
            {
                return new CenterLongLat(1.0 * this.Icentlng / 10000000, 1.0 * this.Icentlat / 10000000);
            }
        }
    }
}
