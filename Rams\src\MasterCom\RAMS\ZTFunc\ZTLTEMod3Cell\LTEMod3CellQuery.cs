﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTEMod3CellQuery : QueryBase
    {
        public LTEMod3CellQuery(MainModel mm)
            : base(mm)
        {
        }

        public override bool IsNeedSetQueryCondition
        {
            get { return false; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        public override string Name
        {
            get { return "LTE模三干扰小区"; }
        }

        public override string IconName
        {
            get { return string.Empty; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 19000, 19037, this.Name);
        }

        private LTEMod3CellForm form { get; set; }
        protected override void query()
        {
            form = MainModel.GetObjectFromBlackboard(typeof(LTEMod3CellForm).FullName) as LTEMod3CellForm;
            if (form == null || form.IsDisposed)
            {
                form = new LTEMod3CellForm(MainModel);
            }
            form.Show(MainModel.MainForm);
        }
    }
}
