﻿namespace MasterCom.RAMS.Model.PerformanceParam
{
    partial class NebulaDetailForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.child = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.targetLAC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.targetCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.target_LAC_CI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControlRes = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.显示所有ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.导出数据ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.gvMain = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.Mon = new DevExpress.XtraGrid.Columns.GridColumn();
            this.LAC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.CI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcLAC_CI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridControlDetail = new DevExpress.XtraGrid.GridControl();
            this.gridViewDetail = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gcTargetLAC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcTargetCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            ((System.ComponentModel.ISupportInitialize)(this.child)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRes)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gvMain)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlDetail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewDetail)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            this.SuspendLayout();
            // 
            // child
            // 
            this.child.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.targetLAC,
            this.targetCI,
            this.target_LAC_CI});
            this.child.GridControl = this.gridControlRes;
            this.child.Name = "child";
            this.child.OptionsCustomization.AllowGroup = false;
            this.child.OptionsView.ColumnAutoWidth = false;
            this.child.OptionsView.ShowGroupPanel = false;
            // 
            // targetLAC
            // 
            this.targetLAC.Caption = "目标LAC";
            this.targetLAC.FieldName = "targetLAC";
            this.targetLAC.Name = "targetLAC";
            this.targetLAC.Visible = true;
            this.targetLAC.VisibleIndex = 0;
            // 
            // targetCI
            // 
            this.targetCI.Caption = "目标CI";
            this.targetCI.FieldName = "targetCI";
            this.targetCI.Name = "targetCI";
            this.targetCI.Visible = true;
            this.targetCI.VisibleIndex = 1;
            // 
            // target_LAC_CI
            // 
            this.target_LAC_CI.Caption = "LAC_CI";
            this.target_LAC_CI.FieldName = "LAC_CI";
            this.target_LAC_CI.Name = "target_LAC_CI";
            // 
            // gridControlRes
            // 
            this.gridControlRes.ContextMenuStrip = this.contextMenuStrip;
            this.gridControlRes.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlRes.Font = new System.Drawing.Font("Tahoma", 6.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.gridControlRes.Location = new System.Drawing.Point(0, 0);
            this.gridControlRes.MainView = this.gvMain;
            this.gridControlRes.Name = "gridControlRes";
            this.gridControlRes.Size = new System.Drawing.Size(958, 300);
            this.gridControlRes.TabIndex = 6;
            this.gridControlRes.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvMain,
            this.child});
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.显示所有ToolStripMenuItem,
            this.导出数据ToolStripMenuItem});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(119, 48);
            // 
            // 显示所有ToolStripMenuItem
            // 
            this.显示所有ToolStripMenuItem.Name = "显示所有ToolStripMenuItem";
            this.显示所有ToolStripMenuItem.Size = new System.Drawing.Size(118, 22);
            this.显示所有ToolStripMenuItem.Text = "显示所有";
            this.显示所有ToolStripMenuItem.Click += new System.EventHandler(this.显示所有ToolStripMenuItem_Click);
            // 
            // 导出数据ToolStripMenuItem
            // 
            this.导出数据ToolStripMenuItem.Name = "导出数据ToolStripMenuItem";
            this.导出数据ToolStripMenuItem.Size = new System.Drawing.Size(118, 22);
            this.导出数据ToolStripMenuItem.Text = "导出数据";
            this.导出数据ToolStripMenuItem.Click += new System.EventHandler(this.导出数据ToolStripMenuItem_Click);
            // 
            // gvMain
            // 
            this.gvMain.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gvMain.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gvMain.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gvMain.BestFitMaxRowCount = 50;
            this.gvMain.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.Mon,
            this.LAC,
            this.CI,
            this.gcLAC_CI,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn1,
            this.gridColumn17});
            this.gvMain.GridControl = this.gridControlRes;
            this.gvMain.Name = "gvMain";
            this.gvMain.OptionsBehavior.Editable = false;
            this.gvMain.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gvMain.OptionsView.ColumnAutoWidth = false;
            this.gvMain.OptionsView.ShowGroupPanel = false;
            this.gvMain.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.gvMain_FocusedRowChanged);
            this.gvMain.Click += new System.EventHandler(this.gvMain_Click);
            // 
            // Mon
            // 
            this.Mon.Caption = "轮次";
            this.Mon.FieldName = "IMon";
            this.Mon.Name = "Mon";
            this.Mon.Visible = true;
            this.Mon.VisibleIndex = 0;
            // 
            // LAC
            // 
            this.LAC.Caption = "源LAC";
            this.LAC.FieldName = "ILAC";
            this.LAC.Name = "LAC";
            this.LAC.Visible = true;
            this.LAC.VisibleIndex = 1;
            // 
            // CI
            // 
            this.CI.Caption = "源CI";
            this.CI.FieldName = "ICI";
            this.CI.Name = "CI";
            this.CI.Visible = true;
            this.CI.VisibleIndex = 2;
            // 
            // gcLAC_CI
            // 
            this.gcLAC_CI.Caption = "LAC_CI";
            this.gcLAC_CI.FieldName = "LAC_CI";
            this.gcLAC_CI.Name = "gcLAC_CI";
            // 
            // gridColumn15
            // 
            this.gridColumn15.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn15.Caption = "路测切换总次数";
            this.gridColumn15.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn15.FieldName = "MDTChangeCount";
            this.gridColumn15.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.SortMode = DevExpress.XtraGrid.ColumnSortMode.Custom;
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 3;
            this.gridColumn15.Width = 125;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "网络侧切换成功次数";
            this.gridColumn16.FieldName = "ChangeSuccessCount";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 4;
            this.gridColumn16.Width = 125;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "网络侧切换总次数";
            this.gridColumn1.FieldName = "ChangeCount";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 5;
            this.gridColumn1.Width = 125;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "网络侧切换成功率";
            this.gridColumn17.FieldName = "ChangeSuccessRadio";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 6;
            this.gridColumn17.Width = 125;
            // 
            // gridControlDetail
            // 
            this.gridControlDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlDetail.Font = new System.Drawing.Font("Tahoma", 6.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.gridControlDetail.Location = new System.Drawing.Point(2, 23);
            this.gridControlDetail.MainView = this.gridViewDetail;
            this.gridControlDetail.Name = "gridControlDetail";
            this.gridControlDetail.Size = new System.Drawing.Size(954, 175);
            this.gridControlDetail.TabIndex = 7;
            this.gridControlDetail.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewDetail,
            this.gridView3});
            // 
            // gridViewDetail
            // 
            this.gridViewDetail.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridViewDetail.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.gridViewDetail.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridViewDetail.BestFitMaxRowCount = 50;
            this.gridViewDetail.ColumnPanelRowHeight = 50;
            this.gridViewDetail.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gcTargetLAC,
            this.gcTargetCI,
            this.gridColumn18,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn24,
            this.gridColumn25,
            this.gridColumn26,
            this.gridColumn27,
            this.gridColumn28});
            this.gridViewDetail.GridControl = this.gridControlDetail;
            this.gridViewDetail.Name = "gridViewDetail";
            this.gridViewDetail.OptionsBehavior.Editable = false;
            this.gridViewDetail.OptionsSelection.InvertSelection = true;
            this.gridViewDetail.OptionsView.ColumnAutoWidth = false;
            this.gridViewDetail.OptionsView.ShowGroupPanel = false;
            this.gridViewDetail.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.gridViewDetail_FocusedRowChanged);
            this.gridViewDetail.Click += new System.EventHandler(this.gridViewDetail_Click);
            this.gridViewDetail.RowStyle += new DevExpress.XtraGrid.Views.Grid.RowStyleEventHandler(this.gvTest1_RowStyle);
            // 
            // gcTargetLAC
            // 
            this.gcTargetLAC.Caption = "目标LAC";
            this.gcTargetLAC.FieldName = "targetLAC";
            this.gcTargetLAC.Name = "gcTargetLAC";
            this.gcTargetLAC.Visible = true;
            this.gcTargetLAC.VisibleIndex = 0;
            this.gcTargetLAC.Width = 65;
            // 
            // gcTargetCI
            // 
            this.gcTargetCI.Caption = "目标CI";
            this.gcTargetCI.FieldName = "targetCI";
            this.gcTargetCI.Name = "gcTargetCI";
            this.gcTargetCI.Visible = true;
            this.gcTargetCI.VisibleIndex = 1;
            this.gcTargetCI.Width = 65;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "路测切换次数";
            this.gridColumn18.FieldName = "MDTChangeCount";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 2;
            this.gridColumn18.Width = 70;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "源小区切出占比";
            this.gridColumn22.FieldName = "outRadio";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 3;
            this.gridColumn22.Width = 70;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "目标小区切出占比";
            this.gridColumn23.FieldName = "targetOutRadio";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 4;
            this.gridColumn23.Width = 70;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "性能侧切换请求次数";
            this.gridColumn24.FieldName = "requestCout";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 5;
            this.gridColumn24.Width = 85;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "性能侧切换成功次数";
            this.gridColumn25.FieldName = "successCount";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 6;
            this.gridColumn25.Width = 85;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "性能侧切换成功率";
            this.gridColumn26.FieldName = "successRadio";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 7;
            this.gridColumn26.Width = 70;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "问题级别";
            this.gridColumn27.FieldName = "questionIndex";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 8;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "建议方案";
            this.gridColumn28.FieldName = "suggestInf";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 9;
            this.gridColumn28.Width = 200;
            // 
            // gridView3
            // 
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21});
            this.gridView3.GridControl = this.gridControlDetail;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsCustomization.AllowGroup = false;
            this.gridView3.OptionsView.ColumnAutoWidth = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "目标LAC";
            this.gridColumn19.FieldName = "targetLAC";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 0;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "目标CI";
            this.gridColumn20.FieldName = "targetCI";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 1;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "LAC_CI";
            this.gridColumn21.FieldName = "LAC_CI";
            this.gridColumn21.Name = "gridColumn21";
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem1});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(119, 26);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(118, 22);
            this.toolStripMenuItem1.Text = "导出数据";
            this.toolStripMenuItem1.Click += new System.EventHandler(this.toolStripMenuItem1_Click);
            // 
            // splitContainer1
            // 
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.Location = new System.Drawing.Point(0, 0);
            this.splitContainer1.Name = "splitContainer1";
            this.splitContainer1.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.gridControlRes);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.groupControl1);
            this.splitContainer1.Size = new System.Drawing.Size(958, 504);
            this.splitContainer1.SplitterDistance = 300;
            this.splitContainer1.TabIndex = 8;
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.gridControlDetail);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(958, 200);
            this.groupControl1.TabIndex = 8;
            this.groupControl1.Text = "详细信息";
            // 
            // NebulaDetailForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(958, 504);
            this.Controls.Add(this.splitContainer1);
            this.Name = "NebulaDetailForm";
            this.ShowIcon = false;
            this.Text = "象限分区详细";
            ((System.ComponentModel.ISupportInitialize)(this.child)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlRes)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gvMain)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlDetail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewDetail)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            this.splitContainer1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControlRes;
        private DevExpress.XtraGrid.Views.Grid.GridView child;
        private DevExpress.XtraGrid.Columns.GridColumn targetLAC;
        private DevExpress.XtraGrid.Views.Grid.GridView gvMain;
        private DevExpress.XtraGrid.Columns.GridColumn Mon;
        private DevExpress.XtraGrid.Columns.GridColumn LAC;
        private DevExpress.XtraGrid.Columns.GridColumn CI;
        private DevExpress.XtraGrid.Columns.GridColumn targetCI;
        private DevExpress.XtraGrid.Columns.GridColumn target_LAC_CI;
        private DevExpress.XtraGrid.GridControl gridControlDetail;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewDetail;
        private DevExpress.XtraGrid.Columns.GridColumn gcTargetLAC;
        private DevExpress.XtraGrid.Columns.GridColumn gcTargetCI;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gcLAC_CI;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private System.Windows.Forms.SplitContainer splitContainer1;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem 显示所有ToolStripMenuItem;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private System.Windows.Forms.ToolStripMenuItem 导出数据ToolStripMenuItem;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem1;
    }
}