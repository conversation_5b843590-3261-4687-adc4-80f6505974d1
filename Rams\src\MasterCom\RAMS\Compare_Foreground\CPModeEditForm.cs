﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func.CoverageCheck;
using MasterCom.RAMS.Chris.Util;
using DevExpress.XtraEditors;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS
{
    public partial class CPModeEditForm : BaseDialog
    {
        public enum ECarrier { 移动 = 1, 联通 = 2, 电信 = 3 }
        public enum ECompetitor { 主队 = 1, 客队 = 2 }
        public enum ECmpType { HOST_SUB_GUEST = 0, BOTH_STANDARD = 1 }
        public const string HOSTNULL = "只有客队";
        public const string GUESTNULL = "只有主队";
        public const string OTHERS = "其它";

        public const string HOST_SUB_GUEST = "主队-客队";
        public const string BOTH_STANDARD = "均达标(免比)";

        protected MapFormItemSelection ItemSelection;
        protected ItemSelectionPanel projPanelHost;
        protected ItemSelectionPanel projPanelGuest;
        protected ItemSelectionPanel servPanelHost;
        protected ItemSelectionPanel servPanelGuest;

        private Dictionary<string, string> paraDiscription = new Dictionary<string, string>();
        public CompareMode compareConfig { get; set; }
        public CompareMode2 compareConfig2 { get; set; }
        private CompareParam paramTemp = null;
        private CompareParam paramSelect = null;
        private CompareDisplayColumn curDisplayColHost = null;
        private CompareDisplayColumn curDisplayColGuest = null;

        public CPModeEditForm(MapFormItemSelection itemSelection)
        {
            InitializeComponent();
            this.ItemSelection = itemSelection;

            initSearchDateTime();
            initProjAndServ();
            initCarrier();
            initParaDiscription();
            initRangControl();
            enableControl();
        }

        private void enableControl()
        {
            cbxRangeHost.Checked = true;
            cbxRangeGuest.Checked = true;

            if (listBoxControlIndex.Items.Count <= 0)
            {
                btnUp.Enabled = false;
                btnDown.Enabled = false;
                btnDelete.Enabled = false;
                btnColorAdd.Enabled = false;
                btnColorModify.Enabled = false;
                btnColorDel.Enabled = false;
                labelColorHost.Enabled = false;
                labelColorGuest.Enabled = false;
                labelColorOthers.Enabled = false;
            }
        }

        private void initRangControl()
        {
            rangeSettingHost.NumericUpDownMin.Minimum = int.MinValue;
            rangeSettingHost.NumericUpDownMin.Maximum = int.MaxValue;
            rangeSettingHost.NumericUpDownMin.Value = -100;
            rangeSettingHost.NumericUpDownMin.Increment = 0.1M;
            rangeSettingHost.NumericUpDownMin.DecimalPlaces = 1;
            rangeSettingHost.NumericUpDownMax.Minimum = int.MinValue;
            rangeSettingHost.NumericUpDownMax.Maximum = int.MaxValue;
            rangeSettingHost.NumericUpDownMax.Value = -55;
            rangeSettingHost.NumericUpDownMax.Increment = 0.1M;
            rangeSettingHost.NumericUpDownMax.DecimalPlaces = 1;

            rangeSettingGuest.NumericUpDownMin.Minimum = int.MinValue;
            rangeSettingGuest.NumericUpDownMin.Maximum = int.MaxValue;
            rangeSettingGuest.NumericUpDownMin.Value = -100;
            rangeSettingGuest.NumericUpDownMin.Increment = 0.1M;
            rangeSettingGuest.NumericUpDownMin.DecimalPlaces = 1;
            rangeSettingGuest.NumericUpDownMax.Minimum = int.MinValue;
            rangeSettingGuest.NumericUpDownMax.Maximum = int.MaxValue;
            rangeSettingGuest.NumericUpDownMax.Value = -55;
            rangeSettingGuest.NumericUpDownMax.Increment = 0.1M;
            rangeSettingGuest.NumericUpDownMax.DecimalPlaces = 1;
        }

        private void initSearchDateTime()
        {
           //
        }

        private void initProjAndServ()
        {
            listViewServiceHost.Items.Clear();
            listViewServiceGuest.Items.Clear();
            if (mainModel.CategoryManager["ServiceType"] != null)
            {
                servPanelHost = new ItemSelectionPanel(toolStripDropDownServiceHost, listViewServiceHost, lbSvCountHost, ItemSelection, "ServiceType", true);
                servPanelGuest = new ItemSelectionPanel(toolStripDropDownServiceGuest, listViewServiceGuest, lbSvCountGuest, ItemSelection, "ServiceType", true);
                toolStripDropDownServiceHost.Items.Clear();
                toolStripDropDownServiceGuest.Items.Clear();
                servPanelHost.FreshItems();
                servPanelGuest.FreshItems();
                toolStripDropDownServiceHost.Items.Add(new ToolStripControlHost(servPanelHost));
                toolStripDropDownServiceGuest.Items.Add(new ToolStripControlHost(servPanelGuest));
            }
        }
        private void initCarrier()
        {
            radioGroupCarrierHost.Properties.Items.Clear();
            radioGroupCarrierGuest.Properties.Items.Clear();

            radioGroupCarrierHost.Properties.Items.Add(new DevExpress.XtraEditors.Controls.RadioGroupItem(ECarrier.移动, "移动"));
            radioGroupCarrierHost.Properties.Items.Add(new DevExpress.XtraEditors.Controls.RadioGroupItem(ECarrier.联通, "联通"));
            radioGroupCarrierHost.Properties.Items.Add(new DevExpress.XtraEditors.Controls.RadioGroupItem(ECarrier.电信, "电信"));
            radioGroupCarrierHost.SelectedIndex = 0;

            radioGroupCarrierGuest.Properties.Items.Add(new DevExpress.XtraEditors.Controls.RadioGroupItem(ECarrier.移动, "移动"));
            radioGroupCarrierGuest.Properties.Items.Add(new DevExpress.XtraEditors.Controls.RadioGroupItem(ECarrier.联通, "联通"));
            radioGroupCarrierGuest.Properties.Items.Add(new DevExpress.XtraEditors.Controls.RadioGroupItem(ECarrier.电信, "电信"));
            radioGroupCarrierGuest.SelectedIndex = 1;
        }

        private void initParaDiscription()
        {
            ConfigSetSwitch css = new ConfigSetSwitch();
            System.IO.DirectoryInfo directory = new System.IO.DirectoryInfo(string.Format(Application.StartupPath + "/config"));
            foreach (System.IO.FileInfo file in directory.GetFiles("statinitargs_kpi_*.xml"))
            {
                css.addData(ParamCfgItem.loadParamCfgFromFile(file.FullName));
            }
            if (css.paramNodesItems == null)
            {
                return;
            }
            paraDiscription.Clear();
            foreach (ParamCfgItem cfgItem in css.paramNodesItems)
            {
                ParamCfgItem.prepareDescriptionDic(paraDiscription, cfgItem);
            }
        }

        public void FillData(CompareMode compareConfig)
        {
            this.compareConfig = compareConfig;

            List<CompareParam> compareParamConfigList = compareConfig.CompareConfigList;
            foreach (CompareParam compareParamConfig in compareParamConfigList)
            {
                listBoxControlIndex.Items.Add(compareParamConfig);
            }
            if (listBoxControlIndex.Items.Count > 0)
            {
                listBoxControlIndex.SelectedIndex = 0;
            }
        }

        public void FillData(CompareMode2 compareConfig)
        {
            this.compareConfig2 = compareConfig;

            List<CompareParam2> compareParamConfigList = compareConfig.CompareConfigList;
            foreach (CompareParam2 compareParamConfig in compareParamConfigList)
            {
                listBoxControlIndex.Items.Add(compareParamConfig);
            }
            if (listBoxControlIndex.Items.Count > 0)
            {
                listBoxControlIndex.SelectedIndex = 0;
            }
        }

        private void buttonServHost_Click(object sender, EventArgs e)
        {
            Point pt = new Point(buttonServHost.Width, buttonServHost.Height);
            toolStripDropDownServiceHost.Show(buttonServHost, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void buttonServGueest_Click(object sender, EventArgs e)
        {
            Point pt = new Point(buttonServGuest.Width, buttonServGuest.Height);
            toolStripDropDownServiceGuest.Show(buttonServGuest, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void rtxtFormularCmpHost_MouseUp(object sender, MouseEventArgs e)
        {
            int pos = rtxtFormularCmpHost.SelectionStart;
            string tokenStr = getTokenStrFrom(pos, rtxtFormularCmpHost.Text);
            string desc;
            if (paraDiscription.TryGetValue(tokenStr, out desc))
            {
                lbDescriptionHost.Text = desc;
            }
            else
            {
                lbDescriptionHost.Text = "";
            }
        }

        private void rtxtFormularCmpGuest_MouseUp(object sender, MouseEventArgs e)
        {
            int pos = rtxtFormularCmpGuest.SelectionStart;
            string tokenStr = getTokenStrFrom(pos, rtxtFormularCmpGuest.Text);
            string desc;
            if (paraDiscription.TryGetValue(tokenStr, out desc))
            {
                lbDescriptionGuest.Text = desc;
            }
            else
            {
                lbDescriptionGuest.Text = "";
            }
        }

        private string getTokenStrFrom(int pos, string str)
        {
            if (pos < 0 || pos > str.Length - 1)
            {
                return "";
            }
            int start = pos;
            int end = pos;
            while (start >= 0)//向前找
            {
                char ch = str[start];
                if (ch == '+' || ch == '-' || ch == '*' || ch == '/' || ch == ' ' || ch == '{' || ch == '}' || ch == '(' || ch == ')')
                {
                    break;
                }
                start--;
            }
            while (end <= str.Length - 1)//向后找
            {
                char ch = str[end];
                if (ch == '+' || ch == '-' || ch == '*' || ch == '/' || ch == ' ' || ch == '{' || ch == '}' || ch == '(' || ch == ')')
                {
                    break;
                }
                end++;
            }
            if (end <= str.Length && end > start)
            {
                return str.Substring(start + 1, end - start - 1);
            }
            return "";
        }

        private void listBoxControlIndex_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listBoxControlIndex.SelectedItems.Count <= 0)
            {
                btnUp.Enabled = false;
                btnDown.Enabled = false;
                btnDelete.Enabled = false;
                dgvColorRange.Rows.Clear();
                btnColorAdd.Enabled = false;
                btnColorModify.Enabled = false;
                btnColorDel.Enabled = false;
                labelColorHost.Enabled = false;
                labelColorGuest.Enabled = false;
                labelColorOthers.Enabled = false;
                return;
            } 
            btnUp.Enabled = true;
            btnDown.Enabled = true;
            btnDelete.Enabled = true;
            btnColorAdd.Enabled = true;
            labelColorHost.Enabled = true;
            labelColorGuest.Enabled = true;
            labelColorOthers.Enabled = true;
            paramSelect = listBoxControlIndex.SelectedItem as CompareParam;
            if (paramTemp != null && paramSelect == paramTemp)
            {
                setCfgControlNull(paramSelect);
                return;
            }
            fillSettingControl(paramSelect);
            fillHostDisplayColumnView(paramSelect);
            fillGuestDisplayColumnView(paramSelect);
        }

        private void setCfgControlNull(CompareParam paramSelect)
        {
            listViewServiceHost.Items.Clear();
            listViewServiceGuest.Items.Clear();
            radioGroupCarrierHost.SelectedIndex = 0;
            radioGroupCarrierGuest.SelectedIndex = 1;
            rtxtFormularCmpHost.Text = "";
            rtxtFormularCmpGuest.Text = "";
            cbxRangeHost.Checked = true;
            cbxRangeGuest.Checked = true;
            showColorData(paramSelect.AlgorithmCfg);
        }

        private void fillSettingControl(CompareParam paramSelect)
        {
            listViewServiceHost.Items.Clear();
            CategoryEnum serviceCate = (CategoryEnum)CategoryManager.GetInstance()["ServiceType"];
            foreach (int servID in paramSelect.serviceList_A)
            {
                ListViewItem lvi = new ListViewItem();
                lvi.Text = serviceCate[servID].Name;
                lvi.Tag = serviceCate[servID].ID;
                listViewServiceHost.Items.Add(lvi);
            }
            listViewServiceGuest.Items.Clear();
            foreach (int servID in paramSelect.serviceList_B)
            {
                ListViewItem lvi = new ListViewItem();
                lvi.Text = serviceCate[servID].Name;
                lvi.Tag = serviceCate[servID].ID;
                listViewServiceGuest.Items.Add(lvi);
            }
            radioGroupCarrierHost.SelectedIndex = paramSelect.carrier_A - 1;
            radioGroupCarrierGuest.SelectedIndex = paramSelect.carrier_B - 1;
            rtxtFormularCmpHost.Text = paramSelect.formula_A;
            rtxtFormularCmpGuest.Text = paramSelect.formula_B;
            cbxRangeHost.Checked = paramSelect.isLimit_A;
            rangeSettingHost.Enabled = true;
            rangeSettingHost.Range = paramSelect.Range_A;
            cbxRangeGuest.Checked = paramSelect.isLimit_B;
            rangeSettingGuest.Enabled = true;
            rangeSettingGuest.Range = paramSelect.Range_B;
            showColorData(paramSelect.AlgorithmCfg);
            setSpecialLable(paramSelect.AlgorithmCfg);
            rangeSettingHost.Enabled = cbxRangeHost.Checked;
            rangeSettingGuest.Enabled = cbxRangeGuest.Checked;
            rabAnd.Checked = paramSelect.judgeByBndOr;

            if (rabAnd.Checked)
            {
                rabOr.Checked = false;
            } 
            else
            {
                rabOr.Checked = true;
            }
        }

        private void btnUp_Click(object sender, EventArgs e)
        {
            if (listBoxControlIndex.Items.Count <= 1)
            {
                return;
            }
            int idx = listBoxControlIndex.SelectedIndex;
            if (idx == 0)
            {
                return;
            }
            object item = listBoxControlIndex.SelectedItem;
            listBoxControlIndex.Items.RemoveAt(idx);
            listBoxControlIndex.Items.Insert(idx - 1, item);
            listBoxControlIndex.SelectedIndex = idx - 1;
        }

        private void btnDown_Click(object sender, EventArgs e)
        {
            if (listBoxControlIndex.Items.Count <= 1)
            {
                return;
            }
            int idx = listBoxControlIndex.SelectedIndex;
            if (idx == listBoxControlIndex.Items.Count - 1)
            {
                return;
            }
            object item = listBoxControlIndex.SelectedItem;
            listBoxControlIndex.Items.RemoveAt(idx);
            listBoxControlIndex.Items.Insert(idx + 1, item);
            listBoxControlIndex.SelectedIndex = idx + 1;
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (paramTemp != null && !compareConfig.compareConfigDic.ContainsKey(paramTemp.name))
            {
                MessageBox.Show("模式[" + paramTemp.name + "]还没保存,请保存!", "提示");
                return;
            }
            CPModeAddForm mAddForm = new CPModeAddForm(compareConfig);
            if (mAddForm.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            paramTemp = new CompareParam();
            paramTemp.name = mAddForm.ModeName;
            paramTemp.sn = findIndex();
            listBoxControlIndex.Items.Add(paramTemp);
            listBoxControlIndex.SelectedIndex += 1;
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (listBoxControlIndex.SelectedItem == null)
            {
                return;
            }
            if (MessageBox.Show("确定删除该模式?", "警告") == DialogResult.OK)
            {
                if (paramTemp != null && (paramSelect).name.Equals(paramTemp.name))
                {
                    paramTemp = null;
                }
                else if (compareConfig.compareConfigDic.ContainsKey(paramSelect.name))
                {
                    compareConfig.compareConfigDic.Remove(paramSelect.name);
                }
                listBoxControlIndex.Items.Remove(listBoxControlIndex.SelectedItem);
            }
        }

        private void simpleBtnSave_Click(object sender, EventArgs e)
        {
            rangeSettingHost.Enabled = true;
            rangeSettingGuest.Enabled = true;
            if (check())
            {
                if (paramTemp != null)
                {
                    fillSettingCfg(paramTemp);
                    compareConfig.compareConfigDic.Add(paramTemp.name, paramTemp);
                    paramTemp = null;
                }
                else if (listBoxControlIndex.SelectedItem != null)
                {
                    fillSettingCfg((CompareParam)listBoxControlIndex.SelectedItem);
                }
                if (compareConfig.saveConfig())
                {
                    MessageBox.Show("保存配置成功", "提示");
                }
                else { MessageBox.Show("保存配置失败", "提示"); }
            }
            rangeSettingHost.Enabled = cbxRangeHost.Checked;
            rangeSettingGuest.Enabled = cbxRangeGuest.Checked;
        }

        private bool check()
        {
            if (listBoxControlIndex.Items.Count <= 0)
            {
                MessageBox.Show("请添加竞对模式", "提示");
                return false;
            }
            else if (listViewServiceHost.Items.Count == 0 || listViewServiceGuest.Items.Count == 0)
            {
                MessageBox.Show("网络类型不能为空", "提示");
                return false;
            }
            else if (radioGroupCarrierHost.Properties.Items.Count == 0 ||
                radioGroupCarrierGuest.Properties.Items.Count == 0)
            {
                MessageBox.Show("竞对运营商不能为空", "提示");
                return false;
            }
            else if (rtxtFormularCmpHost.Text.Trim() == "" || rtxtFormularCmpGuest.Text.Trim() == "")
            {
                MessageBox.Show("竞对指标不能为空", "提示");
                return false;
            }
            return true;
        }

        Dictionary<string, object> settingCfgDic = new Dictionary<string, object>();
        private void fillSettingCfg(CompareParam param)
        {
            settingCfgDic.Clear();
            settingCfgDic["sn"] = param.sn;
            settingCfgDic["name"] = param.name;

            List<object> serviceAList = new List<object>();
            foreach (ListViewItem lviServ in listViewServiceHost.Items)
            {
                serviceAList.Add(lviServ.Tag);
            }

            List<object> dispalyList_A = new List<object>();
            foreach (CompareDisplayColumn col in param.displayColumnList_A)
            {
                dispalyList_A.Add(col.CfgParam);
            }

            settingCfgDic["serviceList_A"] = serviceAList;
            settingCfgDic["carrier_A"] = radioGroupCarrierHost.SelectedIndex + 1;
            settingCfgDic["formula_A"] = rtxtFormularCmpHost.Text.Trim();
            settingCfgDic["isLimit_A"] = cbxRangeHost.Checked;
            settingCfgDic["Range_A"] = rangeSettingHost.Range.Param;
            settingCfgDic["judgeByBndOr"] = rabAnd.Checked;
            settingCfgDic["displayColumnList_A"] = dispalyList_A;

            List<object> serviceBList = new List<object>();
            foreach (ListViewItem lviServ in listViewServiceGuest.Items)
            {
                serviceBList.Add(lviServ.Tag);
            }
            List<object> dispalyList_B = new List<object>();
            foreach (CompareDisplayColumn col in param.displayColumnList_B)
            {
                dispalyList_B.Add(col.CfgParam);
            }

            settingCfgDic["serviceList_B"] = serviceBList;
            settingCfgDic["carrier_B"] = radioGroupCarrierGuest.SelectedIndex + 1;
            settingCfgDic["formula_B"] = rtxtFormularCmpGuest.Text.Trim();
            settingCfgDic["isLimit_B"] = cbxRangeGuest.Checked;
            settingCfgDic["Range_B"] = rangeSettingGuest.Range.Param;
            settingCfgDic["displayColumnList_B"] = dispalyList_B;

            settingCfgDic["algorithmName"] = param.AlgorithmCfg.name;
            List<object> algorithmList = new List<object>();
            foreach (CPModeColorItem colorItem in param.AlgorithmCfg.colorItemList)
            {
                Dictionary<string, object> agthmDic;
                agthmDic = colorItem.Param;
                algorithmList.Add(agthmDic);
            }
            settingCfgDic["colorItems"] = algorithmList;

            List<object> standardList = new List<object>();
            foreach (CPModeColorItem standardItem in param.AlgorithmCfg.bothStandardList)
            {
                Dictionary<string, object> standardDic;
                standardDic = standardItem.Param;
                standardList.Add(standardDic);
            }
            settingCfgDic["bothStandards"] = standardList;

            List<object> specialList = new List<object>();
            foreach (TextColorRange tcRange in param.AlgorithmCfg.specialColorList)
            {
                Dictionary<string, object> specialDic;
                specialDic = tcRange.Param;
                specialDic["Visible"] = tcRange.Visible;
                specialList.Add(specialDic);
            }
            settingCfgDic["specials"] = specialList;
            param.Param = settingCfgDic;
        }

        private int findIndex()
        {
            int sn = 1;
            foreach (CompareParam cpParam in compareConfig.CompareConfigList)
            {
                if (cpParam.sn == sn)
                {
                    sn++;
                }
                else
                {
                    return sn;
                }
            }
            return sn + 1;
        }

        private void simpleBtnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleBtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void CPModeEditForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (DialogResult.Yes != MessageBox.Show("请确定是否已经保存了模板?", "提示", MessageBoxButtons.YesNo))
            {
                e.Cancel = true;
            }
        }

        private void cbxRangeHost_CheckedChanged(object sender, EventArgs e)
        {
            if (!cbxRangeHost.Checked)
            {
                rangeSettingHost.Enabled = false;
            }
            else
            {
                rangeSettingHost.Enabled = true;
            }
        }

        private void cbxRangeGuest_CheckedChanged(object sender, EventArgs e)
        {
            if (!cbxRangeGuest.Checked)
            {
                rangeSettingGuest.Enabled = false;
            }
            else
            {
                rangeSettingGuest.Enabled = true;
            }
        }

        private void showColorData(CPModeAlgorithmItem algorithmItem)
        {
            dgvColorRange.Rows.Clear();
            if (algorithmItem == null) return;
            dgvColorRange.RowCount = algorithmItem.colorItemList.Count + algorithmItem.bothStandardList.Count;
            for (int i = 0; i < algorithmItem.colorItemList.Count; i++)
            {
                DataGridViewRow row = dgvColorRange.Rows[i];
                row.Cells[0].Value = algorithmItem.colorItemList[i].colorRange.description;
                row.Cells[1].Value = algorithmItem.colorItemList[i].range.Min.ToString() +
                    (algorithmItem.colorItemList[i].range.MinIncluded ? "<=" : "<") + "X" +
                    (algorithmItem.colorItemList[i].range.MaxIncluded ? "<=" : "<") +
                    algorithmItem.colorItemList[i].range.Max.ToString();
                row.Cells[2].Style.BackColor = algorithmItem.colorItemList[i].colorRange.color;
                row.Cells[2].Style.SelectionBackColor = algorithmItem.colorItemList[i].colorRange.color;
                row.Tag = algorithmItem.colorItemList[i];
            }
            for (int i = 0; i < algorithmItem.bothStandardList.Count; i++)
            {
                DataGridViewRow row = dgvColorRange.Rows[i + algorithmItem.colorItemList.Count];
                row.Cells[0].Value = algorithmItem.bothStandardList[i].colorRange.description;
                row.Cells[1].Value = "主队" +
                    (algorithmItem.bothStandardList[i].bHostMinInclude ? "≥" : "＞") + 
                    algorithmItem.bothStandardList[i].fHostMin +
                    " && 客队" +
                    (algorithmItem.bothStandardList[i].bGuestMinInclude ? "≥" : "＞") +
                    algorithmItem.bothStandardList[i].fGuestMin;
                row.Cells[2].Style.BackColor = algorithmItem.bothStandardList[i].colorRange.color;
                row.Cells[2].Style.SelectionBackColor = algorithmItem.bothStandardList[i].colorRange.color;
                row.Tag = algorithmItem.bothStandardList[i];
            }
        }

        private void setSpecialLable(CPModeAlgorithmItem algorithmItem)
        {
            foreach (MasterCom.RAMS.Func.CoverageCheck.TextColorRange tcRange in algorithmItem.specialColorList)
            {
                if (tcRange.description.Equals(CPModeEditForm.GUESTNULL))
                {
                    labelColorHost.BackColor = tcRange.color;
                }
                else if (tcRange.description.Equals(CPModeEditForm.HOSTNULL))
                {
                    labelColorGuest.BackColor = tcRange.color;
                }
                else if (tcRange.description.Equals(CPModeEditForm.OTHERS))
                {
                    labelColorOthers.BackColor = tcRange.color;
                }
            }
        }

        private void btnColorAdd_Click(object sender, EventArgs e)
        {
            CPTextColorEditDlg dlg = new CPTextColorEditDlg();
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                switch (dlg.cpColorItem.CmpType)
                {
                    case (int)CPModeEditForm.ECmpType.HOST_SUB_GUEST:
                        paramSelect.AlgorithmCfg.colorItemList.Add(dlg.cpColorItem);
                        break;
                    case (int)CPModeEditForm.ECmpType.BOTH_STANDARD:
                        paramSelect.AlgorithmCfg.bothStandardList.Add(dlg.cpColorItem);
                        break;
                    default:
                        break;
                }
                showColorData(paramSelect.AlgorithmCfg);
                dgvColorRange.Invalidate();
            }
        }

        private void btnColorModify_Click(object sender, EventArgs e)
        {
            modifyColor();
        }

        private void btnColorDel_Click(object sender, EventArgs e)
        {
            CPModeColorItem colorSelect = dgvColorRange.SelectedRows[0].Tag as CPModeColorItem;
            //colorSelect = paramSelect.AlgorithmCfg.colorItemList[dgvColorRange.SelectedRows[0].Index];
            switch (colorSelect.CmpType)
            {
                case (int)CPModeEditForm.ECmpType.HOST_SUB_GUEST:
                    paramSelect.AlgorithmCfg.colorItemList.Remove(colorSelect);
                    break;
                case (int)CPModeEditForm.ECmpType.BOTH_STANDARD:
                    paramSelect.AlgorithmCfg.bothStandardList.Remove(colorSelect);
                    break;
                default:
                    break;
            }
            showColorData(paramSelect.AlgorithmCfg);
            dgvColorRange.Invalidate();
        }

        private void dgvColorRange_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvColorRange.SelectedRows.Count > 0)
            {
                btnColorModify.Enabled = true;
                btnColorDel.Enabled = true;
            }
            else
            {
                btnColorModify.Enabled = false;
                btnColorDel.Enabled = false;
            }
        }

        private void dgvColorRange_DoubleClick(object sender, EventArgs e)
        {
            if (dgvColorRange.SelectedRows.Count > 0)
            {
                modifyColor();
            }
        }

        private void modifyColor()
        {
            CPTextColorEditDlg dlg = new CPTextColorEditDlg();
            dlg.Text = "修改显示颜色";
            CPModeColorItem colorSelect = dgvColorRange.SelectedRows[0].Tag as CPModeColorItem;
            int prevCmpType = colorSelect.CmpType;
            dlg.SetColorItem(colorSelect);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                colorSelect = dlg.cpColorItem;
                if (colorSelect.CmpType != prevCmpType)
                {
                    switch (prevCmpType)
                    {
                        case (int)CPModeEditForm.ECmpType.HOST_SUB_GUEST:
                            paramSelect.AlgorithmCfg.colorItemList.Remove(colorSelect);
                            paramSelect.AlgorithmCfg.bothStandardList.Add(colorSelect);
                            break;
                        case (int)CPModeEditForm.ECmpType.BOTH_STANDARD:
                            paramSelect.AlgorithmCfg.bothStandardList.Remove(colorSelect);
                            paramSelect.AlgorithmCfg.colorItemList.Add(colorSelect);
                            break;
                        default:
                            break;
                    }
                }
                showColorData(paramSelect.AlgorithmCfg);
                dgvColorRange.Invalidate();
            }
        }

        ExpEditDlg expDlg = null;
        private string getExpEditDlgFormula(string formulaText)
        {
            string formula = "";
            if (expDlg == null)
            {
                expDlg = new ExpEditDlg();
                expDlg.SelectSwitchParaTree(3);
            }
            else
            {
                expDlg.ReSetForm(formulaText);
            }
            if (expDlg.ShowDialog() != DialogResult.OK)
            {
                return formulaText;
            }
            formula = expDlg.GetExpInput();
            if (formula.StartsWith("{"))
            {
                formula = formula.Remove(0, 1);
            }
            if (formula.EndsWith("}"))
            {
                formula = formula.Remove(formula.Length - 1, 1);
            }
            return formula;
        }
        private void btnFormulaEditHost_Click(object sender, EventArgs e)
        {
            string strFormula = getExpEditDlgFormula(rtxtFormularCmpHost.Text);
            if (strFormula != "")
            {
                rtxtFormularCmpHost.Text = strFormula;
            }
        }

        private void btnFormulaEditGuest_Click(object sender, EventArgs e)
        {
            string strFormula = getExpEditDlgFormula(rtxtFormularCmpGuest.Text);
              if (strFormula != "")
              {
                  rtxtFormularCmpGuest.Text = strFormula;
              }
        }

        private ColorDialog colorDialog = new ColorDialog();
        private void labelColorHost_Click(object sender, EventArgs e)
        {
            colorDialog.Color = labelColorHost.BackColor;
            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                TextColorRange tcRange = paramSelect.AlgorithmCfg.specialColorList[0];
                labelColorHost.BackColor = Color.FromArgb(colorDialog.Color.R, colorDialog.Color.G, colorDialog.Color.B);
                tcRange.color = labelColorHost.BackColor;
            }
        }

        private void labelColorGuest_Click(object sender, EventArgs e)
        {
            colorDialog.Color = labelColorGuest.BackColor;
            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                TextColorRange tcRange = paramSelect.AlgorithmCfg.specialColorList[1];
                labelColorGuest.BackColor = Color.FromArgb(colorDialog.Color.R, colorDialog.Color.G, colorDialog.Color.B);
                tcRange.color = labelColorGuest.BackColor;
            }
        }

        private void labelColorOthers_Click(object sender, EventArgs e)
        {
            colorDialog.Color = labelColorOthers.BackColor;
            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                TextColorRange tcRange = paramSelect.AlgorithmCfg.specialColorList[2];
                labelColorOthers.BackColor = Color.FromArgb(colorDialog.Color.R, colorDialog.Color.G, colorDialog.Color.B);
                tcRange.color = labelColorOthers.BackColor;
            }
        }

        private void CPModeEditForm_Shown(object sender, EventArgs e)
        {
            expDlg = new ExpEditDlg();//预加载窗体，提高点击编辑时的反应速度
            expDlg.SelectSwitchParaTree(3);
        }

        private void CPModeEditForm_Activated(object sender, EventArgs e)
        {
            this.Refresh();
        }

        #region dgvHost相关事件
        //正在编辑的行号
        private int editHostRowIndex = 0;
        //判断是否是新增
        private bool isHostAddedMode = false;
        //用于判断是否编辑完成,刷新dgv数据
        private bool isEndEditHost = false;
        //用于记录修改前选定的列标题数据
        private string lastHostCellData = "";
        //单元格数据是否有效
        private bool isHostCellDataValid = true;
        /// <summary>
        /// 新增
        /// </summary>
        private void btnAddDisColHost_Click(object sender, EventArgs e)
        {
            //新增一行
            dgvHost.Rows.Add();
            //焦点移到新增第一列的输入框
            editHostRowIndex = dgvHost.Rows.Count - 1;
            editingCell(dgvHost, dgvHost.Rows[editHostRowIndex].Cells[0]);
            isHostAddedMode = true;
            //禁用添加按钮
            btnAddDisColHost.Enabled = false;
        }

        /// <summary>
        /// 移除
        /// </summary>
        private void btnRemoveDisColHost_Click(object sender, EventArgs e)
        {
            if (curDisplayColHost == null)
            {
                return;
            }
            paramSelect.displayColumnList_A.Remove(curDisplayColHost);
            curDisplayColHost = null;
            isEndEditHost = true;
            dgvHost.Invalidate();
        }

        /// <summary>
        /// 编辑
        /// </summary>
        private void btnEditDisColHost_Click(object sender, EventArgs e)
        {
            btnRemoveDisColHost.Enabled = false;
            editCellData(dgvHost, paramSelect.displayColumnList_A, ref lastHostCellData, ref editHostRowIndex);
        }

        private void fillHostDisplayColumnView(CompareParam cmp)
        {
            #region 主队
            dgvHost.SelectionChanged -= dgvHost_SelectionChanged;
            dgvHost.Rows.Clear();
            btnRemoveDisColHost.Enabled = false;
            foreach (CompareDisplayColumn cnd in cmp.displayColumnList_A)
            {
                DataGridViewRow dr = new DataGridViewRow();
                dr.CreateCells(dgvHost);
                dr.Cells[0].Value = cnd.Caption;
                dr.Cells[1].Value = cnd.DisplayIndexName;
                dr.Tag = cnd;
                dgvHost.Rows.Add(dr);
            }
            dgvHost.SelectionChanged += dgvHost_SelectionChanged;
            lbDisDescriptionHost.Text = "描述";
            #endregion
        }

        private void dgvHost_CellEndEdit(object sender, DataGridViewCellEventArgs e)
        {
            if (dgvHost.Rows.Count >= editHostRowIndex && checkValid(dgvHost, editHostRowIndex, ref isHostCellDataValid))
            {
                if (isHostAddedMode)
                {
                    //新增时,编辑完第一列单元格后自动调用编辑
                    string strFormula = getExpEditDlgFormula(dgvHost.Rows[editHostRowIndex].Cells[1].FormattedValue.ToString());
                    if (strFormula == "")
                    {
                        //公式为空时                   
                    }
                    //向displayColumnList中添加数据
                    dgvHost.Rows[editHostRowIndex].Cells[1].Value = strFormula;
                    CompareDisplayColumn colHost = new CompareDisplayColumn(dgvHost.Rows[editHostRowIndex].Cells[0].FormattedValue.ToString(),
                    strFormula, "A");
                    paramSelect.AddHostDisplayColumn(colHost);
                    //添加完成,启用添加键,添加模式结束
                    btnAddDisColHost.Enabled = true;
                    isHostAddedMode = false;
                    //刷新界面
                    isEndEditHost = true;
                    dgvHost.Invalidate();
                }
                else
                {
                    endEditFstColumnCell(dgvHost, editHostRowIndex, lastHostCellData, paramSelect.displayColumnList_A);
                }
            }
        }

        private void dgvHost_CellLeave(object sender, DataGridViewCellEventArgs e)
        {
            dgvHost.EndEdit();
        }

        private void dgvHost_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvHost.SelectedCells.Count <= 0)
            {
                lbDisDescriptionHost.Text = "描述";
                btnRemoveDisColHost.Enabled = false;
                return;
            }
            
            DataGridViewCell cell = dgvHost.SelectedCells[0];
            if (!isHostCellDataValid)
            {
                lbDisDescriptionHost.Text = "描述";
                //列标题无效时输入焦点移回该单元格
                editingCell(dgvHost, dgvHost.Rows[editHostRowIndex].Cells[0]);
                btnRemoveDisColHost.Enabled = false;
                return;
            }

            //启用移除
            curDisplayColHost = dgvHost.Rows[cell.RowIndex].Tag as CompareDisplayColumn;
            if (curDisplayColHost == null)
            {
                btnRemoveDisColHost.Enabled = false;
            }
            else
            {
                btnRemoveDisColHost.Enabled = true;
            }

            //改变描述
            if (cell.ColumnIndex == 1)
            {
                int pos = 0;
                string tokenStr = getTokenStrFrom(pos, cell.FormattedValue.ToString());
                string desc;
                if (paraDiscription.TryGetValue(tokenStr, out desc))
                {
                    lbDisDescriptionHost.Text = desc;
                }
                else
                {
                    lbDisDescriptionHost.Text = "";
                }
            }
            else if (cell.ColumnIndex == 0)
            {
                lbDisDescriptionHost.Text = "";
            }
        }

        private void dgvHost_Paint(object sender, PaintEventArgs e)
        {
            if (isEndEditHost)
            {
                fillHostDisplayColumnView(paramSelect);
                isEndEditHost = false;
            }
        }
        #endregion



        #region dgvGuest相关事件
        private int editGuestRowIndex = 0;
        //是否为新增 (新增后自动跳到第一列单元输入,判断输入无误后跳到第二列打开公式编辑界面)
        private bool isGuestAddedMode = false;
        //是否编辑完成 (编辑完成后加载数据重绘dgv)
        private bool isEndEditGuest = false;
        private string lastGuestCellData = "";
        private bool isGuestCellDataValid = true;
        /// <summary>
        /// 新增
        /// </summary>
        private void btnAddDisColGuest_Click(object sender, EventArgs e)
        {
            //新增一行
            dgvGuest.Rows.Add();
            //焦点移到新增第一列的输入框
            editGuestRowIndex = dgvGuest.Rows.Count - 1;
            editingCell(dgvGuest, dgvGuest.Rows[editGuestRowIndex].Cells[0]);
            isGuestAddedMode = true;
            //禁用添加按钮
            btnAddDisColGuest.Enabled = false;
        }

        /// <summary>
        /// 移除
        /// </summary>
        private void btnRemoveDisColGuest_Click(object sender, EventArgs e)
        {
            if (curDisplayColGuest == null)
            {
                return;
            }
            paramSelect.displayColumnList_B.Remove(curDisplayColGuest);
            curDisplayColGuest = null;
            isEndEditGuest = true;
            dgvGuest.Invalidate();
        }

        /// <summary>
        /// 编辑
        /// </summary>
        private void btnEditDisColGuest_Click(object sender, EventArgs e)
        {
            btnRemoveDisColGuest.Enabled = false;
            editCellData(dgvGuest, paramSelect.displayColumnList_B, ref lastGuestCellData, ref editGuestRowIndex);
        }

        private void fillGuestDisplayColumnView(CompareParam cmp)
        {
            #region 客队
            dgvGuest.SelectionChanged -= dgvGuest_SelectionChanged;
            dgvGuest.Rows.Clear();
            btnRemoveDisColGuest.Enabled = false;
            foreach (CompareDisplayColumn cnd in cmp.displayColumnList_B)
            {
                DataGridViewRow dr = new DataGridViewRow();
                dr.CreateCells(dgvGuest);
                dr.Cells[0].Value = cnd.Caption;
                dr.Cells[1].Value = cnd.DisplayIndexName;
                dr.Tag = cnd;
                dgvGuest.Rows.Add(dr);
            }
            dgvGuest.SelectionChanged += dgvGuest_SelectionChanged;
            lbDisDescriptionGuest.Text = "描述";
            #endregion
        }

        private void dgvGuest_CellEndEdit(object sender, DataGridViewCellEventArgs e)
        {
            if (dgvGuest.Rows.Count > editGuestRowIndex && checkValid(dgvGuest, editGuestRowIndex, ref isGuestCellDataValid))
            {
                if (isGuestAddedMode)
                {
                    //新增时,编辑完第一列单元格后自动调用编辑
                    string strFormula = getExpEditDlgFormula(dgvGuest.Rows[editGuestRowIndex].Cells[1].FormattedValue.ToString());
                    dgvGuest.Rows[editGuestRowIndex].Cells[1].Value = strFormula;
                    CompareDisplayColumn colGuest = new CompareDisplayColumn(dgvGuest.Rows[editGuestRowIndex].Cells[0].FormattedValue.ToString(),
                    strFormula, "A");
                    paramSelect.AddGuestDisplayColumn(colGuest);

                    isEndEditGuest = true;
                    dgvGuest.Invalidate();
                    btnAddDisColGuest.Enabled = true;
                    isGuestAddedMode = false;
                }
                else
                {
                    endEditFstColumnCell(dgvGuest, editGuestRowIndex, lastGuestCellData, paramSelect.displayColumnList_A);
                }
            }
        }

        private void dgvGuest_CellLeave(object sender, DataGridViewCellEventArgs e)
        {
            dgvGuest.EndEdit();
        }

        private void dgvGuest_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvGuest.SelectedCells.Count <= 0)
            {
                lbDisDescriptionGuest.Text = "描述";
                btnRemoveDisColGuest.Enabled = false;
                return;
            }

            DataGridViewCell cell = dgvGuest.SelectedCells[0];
            if (!isGuestCellDataValid)
            {
                lbDisDescriptionGuest.Text = "描述";
                editingCell(dgvGuest, dgvGuest.Rows[editGuestRowIndex].Cells[0]);
                btnRemoveDisColGuest.Enabled = false;
                return;
            }

            //启用移除
            curDisplayColGuest = dgvGuest.Rows[cell.RowIndex].Tag as CompareDisplayColumn;
            if (curDisplayColGuest == null)
            {
                btnRemoveDisColGuest.Enabled = false;
            }
            else
            {
                btnRemoveDisColGuest.Enabled = true;
            }
            //改变描述
            if (cell.ColumnIndex == 1)
            {
                int pos = 0;
                string tokenStr = getTokenStrFrom(pos, cell.FormattedValue.ToString());
                string desc;
                if (paraDiscription.TryGetValue(tokenStr, out desc))
                {
                    lbDisDescriptionGuest.Text = desc;
                }
                else
                {
                    lbDisDescriptionGuest.Text = "";
                }
            }
            else if (cell.ColumnIndex == 0)
            {
                lbDisDescriptionGuest.Text = "";
            }
        }
      
        private void dgvGuest_Paint(object sender, PaintEventArgs e)
        {
            if (isEndEditGuest)
            {
                fillGuestDisplayColumnView(paramSelect);
                isEndEditGuest = false;
            }
        }
        #endregion

        /// <summary>
        /// 跳到指定单元格,并切换为输入状态
        /// </summary>
        /// <param name="dgv"></param>
        /// <param name="cell"></param>
        private void editingCell(DataGridView dgv, DataGridViewCell cell)
        {
            cell.ReadOnly = false;
            dgv.CurrentCell = cell;
            dgv.BeginEdit(true);
        }

        /// <summary>
        /// 检测列标题是否可用
        /// </summary>
        /// <param name="dgv"></param>
        /// <param name="rowIndex"></param>
        /// <returns></returns>
        private bool checkValid(DataGridView dgv, int rowIndex, ref bool isCellDataInvalid)
        {
            DataGridViewCell cell = dgv.Rows[rowIndex].Cells[0];
            if (cell.FormattedValue.ToString() == "")
            {
                MessageBox.Show("列标题不能为空！");
                isCellDataInvalid = false;
                return false;
            }

            foreach (DataGridViewRow dr in dgv.Rows)
            {
                if (dr.Cells[0].FormattedValue.ToString().Equals(cell.FormattedValue.ToString())
                    && dr.Index != cell.RowIndex)
                {
                    MessageBox.Show("已存在相同名的参数列！");
                    isCellDataInvalid = false;
                    return false;
                }
            }
            //设置readonly是为了避免F2和选定时单击就可以修改的情况,导致不会调用到editCellData()导致逻辑崩溃
            dgv.Columns[0].ReadOnly = true;
            isCellDataInvalid = true;
            return true;
        }
    
        /// <summary>
        /// 编辑单元格数据 第一列切换为输入状态,第二列调用公式编辑窗口
        /// </summary>
        /// <param name="dgv"></param>
        /// <param name="displayColumnList"></param>
        /// <param name="lastCellData"></param>
        /// <param name="isAddedMode"></param>
        /// <param name="rowIndex"></param>
        private void editCellData(DataGridView dgv, List<CompareDisplayColumn> displayColumnList,ref string lastCellData, ref int rowIndex)
        {
            DataGridViewCell cell = dgv.SelectedCells[0];
            rowIndex = cell.RowIndex;
            if (cell != null && cell.ColumnIndex == 0)
            {
                lastCellData = cell.FormattedValue.ToString();
                //编辑第一列 对应displayColumnList数据修改在endEditFstColumnCell中进行
                editingCell(dgv, dgv.Rows[rowIndex].Cells[0]);
            }
            else if (cell != null && cell.ColumnIndex == 1)
            {
                //编辑第二列 同时修改displayColumnList数据
                string strFormula = getExpEditDlgFormula(cell.FormattedValue.ToString());
                int existCol = displayColumnList.FindIndex(
                   delegate(CompareDisplayColumn c) { return c.DisplayIndexName.Equals(cell.FormattedValue); });
                if (existCol >= 0)
                {
                    displayColumnList[existCol].DisplayIndexName = strFormula;
                }
                dgv.Rows[rowIndex].Cells[1].Value = strFormula;
            }
        }

        /// <summary>
        /// 第一列数据编辑完成后 修改isplayColumnList中的数据
        /// </summary>
        /// <param name="dgv"></param>
        /// <param name="rowIndex"></param>
        /// <param name="lastCellData"></param>
        /// <param name="displayColumnList"></param>
        private void endEditFstColumnCell(DataGridView dgv, int rowIndex, string lastCellData, List<CompareDisplayColumn> displayColumnList)
        {
            //修改displayColumnList中列标题的数据
            DataGridViewCell cell = dgv.Rows[rowIndex].Cells[0];
            int existCol = displayColumnList.FindIndex(
                 delegate(CompareDisplayColumn c) { return c.Caption.Equals(lastCellData); });
            if (existCol >= 0)
            {
                displayColumnList[existCol].Caption = cell.FormattedValue.ToString();
            }
            lastCellData = "";
        }

        private void btnColorUp_Click(object sender, EventArgs e)
        {
            if (dgvColorRange.Rows.Count <= 1)
            {
                return;
            }
            int idx = dgvColorRange.SelectedRows[0].Index;
            if (idx == 0)
            {
                return;
            }
            object selectedItem = dgvColorRange.SelectedRows[0].Tag;
            int changedIdx = idx - 1;
            if (idx < paramSelect.AlgorithmCfg.colorItemList.Count)
            {
                changeItemIndex(idx, changedIdx, selectedItem, paramSelect.AlgorithmCfg.colorItemList);
            }
            else if (idx > paramSelect.AlgorithmCfg.colorItemList.Count)
            {
                int idxBoth = idx - paramSelect.AlgorithmCfg.colorItemList.Count;
                int changedIdxBoth = idxBoth - 1;
                changeItemIndex(idxBoth, changedIdxBoth, selectedItem, paramSelect.AlgorithmCfg.bothStandardList);
            }

            showColorData(paramSelect.AlgorithmCfg);
            dgvColorRange.Invalidate();
            dgvColorRange.CurrentCell = dgvColorRange.Rows[changedIdx].Cells[0];
        }

        private void changeItemIndex(int idx, int changedIdx, object selectedItem, List<CPModeColorItem> colorList)
        {
            foreach (var item in colorList)
            {
                if (item == selectedItem)
                {
                    colorList.RemoveAt(idx);
                    colorList.Insert(changedIdx, item);
                    break;
                }
            }
        }

        private void btnColorDown_Click(object sender, EventArgs e)
        {
            if (dgvColorRange.Rows.Count <= 1)
            {
                return;
            }
            int idx = dgvColorRange.SelectedRows[0].Index;
            if (idx == dgvColorRange.Rows.Count - 1)
            {
                return;
            }

            object selectedItem = dgvColorRange.SelectedRows[0].Tag;
            int changedIdx = idx + 1;
            if (idx < paramSelect.AlgorithmCfg.colorItemList.Count - 1)
            {
                changeItemIndex(idx, changedIdx, selectedItem, paramSelect.AlgorithmCfg.colorItemList);
            }
            else if (idx > paramSelect.AlgorithmCfg.colorItemList.Count
                && idx < paramSelect.AlgorithmCfg.colorItemList.Count + paramSelect.AlgorithmCfg.bothStandardList.Count - 1)
            {
                int idxBoth = idx - paramSelect.AlgorithmCfg.colorItemList.Count;
                int changedIdxBoth = idxBoth + 1;
                changeItemIndex(idxBoth, changedIdxBoth, selectedItem, paramSelect.AlgorithmCfg.bothStandardList);
            }

            showColorData(paramSelect.AlgorithmCfg);
            dgvColorRange.Invalidate();
            dgvColorRange.CurrentCell = dgvColorRange.Rows[changedIdx].Cells[0];
        }
    }
}
