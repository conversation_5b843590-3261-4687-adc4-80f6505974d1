﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.CQT
{
    public class CQTPoint
    {
        public int ID { get; set; }
        public string Name { get; set; }
        public string AddrAt { get; set; }
        public string Desc { get; set; }
        public double LTLongitude { get; set; }
        public double LTLatitude { get; set; }
        public double BRLongitude { get; set; }
        public double BRLatitude { get; set; }
        public double Longitude
        {
            get { return Math.Round((LTLongitude + BRLongitude) / 2, 5); }
        }
        public double Latitude
        {
            get { return Math.Round((LTLatitude + BRLatitude) / 2, 5); }
        }
        public int Altitude { get; set; }
        public string AliasName { get; set; }
        public CQTPointType PointType { get; set; }
        public string PointTypeDesc
        {
            get { return PointType.Name; }
        }
        public CQTDensityType DenstityType { get; set; }
        public string DensityTypeDesc
        {
            get { return DenstityType.Name; }
        }
        public CQTSpaceType SpaceType { get; set; }
        public string SpaceTypeDesc
        {
            get { return SpaceType.Name; }
        }
        public CQTCoverType CoverType { get; set; }
        public string CoverTypeDesc
        {
            get { return CoverType.Name; }
        }
        public List<CQTNetworkType> NetworkType { get; set; }
        public string NetworkTypeDesc
        {
            get
            {
                StringBuilder desc = new StringBuilder();
                foreach (CQTNetworkType item in NetworkType)
                {
                    desc.Append(item.Name + ";");
                }
                return desc.ToString();
            }
        }
        public string NetworkTypeIDs
        {
            get
            {
                StringBuilder ids = new StringBuilder();
                foreach (CQTNetworkType item in NetworkType)
                {
                    ids.Append(item.ID + ";");
                }
                return ids.ToString().TrimEnd(';');
            }
        }
        public int otherType1 { get; set; } = 0;
        public int otherType2 { get; set; } = 0;
        public int otherType3 { get; set; } = 0;
        public int otherType4 { get; set; } = 0;
        public string BelongArea { get; set; }
        public string BelongArea2 { get; set; }
        private List<CQTPointPicture> pictures;
        public List<CQTPointPicture> Pictures
        {
            set { pictures = value; }
            get
            {
                if (pictures == null)
                {
                    DIYUpdateCQTPicture cqtDIY = new DIYUpdateCQTPicture(MainModel.GetInstance(), SqlOperator.CQTPictureGet, null, null, this);
                    cqtDIY.Query();
                    pictures = cqtDIY.cqtPictureList;
                }
                return pictures;
            }
        }

        public int PictureCount
        {
            get
            {
                int cnt = 0;
                if (pictures != null)
                {
                    cnt = pictures.Count;
                }
                return cnt;
            }
        }

        private List<CQTPointCell> cells;
        public List<CQTPointCell> Cells
        {
            set { cells = value; }
            get
            {
                if (cells == null)
                {
                    DIYUpdateCQTPicture cqtDIY = new DIYUpdateCQTPicture(MainModel.GetInstance(), SqlOperator.CQTPointCellGet, null, null, this);
                    cqtDIY.Query();
                    cells = cqtDIY.cqtCellList;
                }
                return cells;
            }
        }

        public bool IsContainCell(int lac, int ci)
        {
            foreach (CQTPointCell cell in Cells)
            {
                if (cell.Lac == lac && cell.CI == ci)
                {
                    return true;
                }
            }
            return false;
        }

        public override string ToString()
        {
            return Name;
        }
    }
}
