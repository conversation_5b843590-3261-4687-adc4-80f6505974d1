﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.Func
{
    public partial class DataComparisonForm : BaseDialog
    {
        protected ItemSelectionPanel projPanelDT;
        protected ItemSelectionPanel projPanelScan;
        protected ItemSelectionPanel servPanelDT;
        protected ItemSelectionPanel servPanelScan;
        public QueryCondition CurConditionDT { get; set; }
        public QueryCondition CurConditionScan { get; set; }
        protected MapFormItemSelection ItemSelection;

        public DataComparisonForm(MainModel mModel, MapFormItemSelection itemSelection, QueryCondition condition)
        {
            InitializeComponent();

            mainModel = mModel;

            CurConditionDT = new QueryCondition();
            CurConditionDT.AgentIds = condition.AgentIds;
            CurConditionDT.Areas = condition.Areas;
            CurConditionDT.CarrierTypes = condition.CarrierTypes;
            CurConditionDT.DistrictID = condition.DistrictID;
            CurConditionDT.DistrictIDs = condition.DistrictIDs;
            CurConditionDT.Geometorys = condition.Geometorys;
            CurConditionDT.IsAllAgent = condition.IsAllAgent;
            CurConditionDT.FileNameOrNum = condition.FileNameOrNum;
            CurConditionDT.NameFilterType = condition.NameFilterType;
            CurConditionDT.Periods = new List<TimePeriod>();
            CurConditionDT.QueryType = condition.QueryType;
            CurConditionDT.WholeProvince = condition.WholeProvince;

            CurConditionScan = new QueryCondition();
            CurConditionScan.AgentIds = condition.AgentIds;
            CurConditionScan.Areas = condition.Areas;
            CurConditionScan.CarrierTypes = condition.CarrierTypes;
            CurConditionScan.DistrictID = condition.DistrictID;
            CurConditionScan.DistrictIDs = condition.DistrictIDs;
            CurConditionScan.Geometorys = condition.Geometorys;
            CurConditionScan.IsAllAgent = condition.IsAllAgent;
            CurConditionScan.FileNameOrNum = condition.FileNameOrNum;
            CurConditionScan.NameFilterType = condition.NameFilterType;
            CurConditionScan.FileInfos = condition.FileInfos;
            CurConditionScan.FileName = condition.FileName;
            CurConditionScan.Periods = new List<TimePeriod>();
            CurConditionScan.QueryType = condition.QueryType;
            CurConditionScan.WholeProvince = condition.WholeProvince;

            ItemSelection = itemSelection;

            dateTimePickerBeginTimeDT.Value = DateTime.Now.AddDays(-1);
            dateTimePickerBeginTimeScan.Value = DateTime.Now.AddDays(-1);
            dateTimePickerEndTimeDT.Value = DateTime.Now;
            dateTimePickerEndTimeScan.Value = DateTime.Now;

            setProject(itemSelection);
            setService(itemSelection);

            setRecorderCommon();
        }

        private void setProject(MapFormItemSelection itemSelection)
        {
            listViewProjectDT.Items.Clear();
            listViewProjectScan.Items.Clear();
            if (mainModel.CategoryManager["Project"] != null)
            {
                projPanelDT = new ItemSelectionPanel(toolStripDropDownProjectDT, listViewProjectDT, lbProjCountDT, itemSelection, "Project", true);
                projPanelScan = new ItemSelectionPanel(toolStripDropDownProjectScan, listViewProjectScan, lbProjCountScan, itemSelection, "Project", true);
                toolStripDropDownProjectDT.Items.Clear();
                toolStripDropDownProjectScan.Items.Clear();
                projPanelDT.FreshItems();
                projPanelScan.FreshItems();
                toolStripDropDownProjectDT.Items.Add(new ToolStripControlHost(projPanelDT));
                toolStripDropDownProjectScan.Items.Add(new ToolStripControlHost(projPanelScan));
            }
        }

        private void setService(MapFormItemSelection itemSelection)
        {
            listViewServiceDT.Items.Clear();
            listViewServiceScan.Items.Clear();
            if (mainModel.CategoryManager["ServiceType"] != null)
            {
                servPanelDT = new ItemSelectionPanel(toolStripDropDownServiceDT, listViewServiceDT, lbSvCountDT, itemSelection, "ServiceType", true);
                servPanelScan = new ItemSelectionPanel(toolStripDropDownServiceScan, listViewServiceScan, lbSvCountScan, itemSelection, "ServiceType", true);
                toolStripDropDownServiceDT.Items.Clear();
                toolStripDropDownServiceScan.Items.Clear();
                servPanelDT.FreshItems();
                servPanelScan.FreshItems();
                toolStripDropDownServiceDT.Items.Add(new ToolStripControlHost(servPanelDT));
                toolStripDropDownServiceScan.Items.Add(new ToolStripControlHost(servPanelScan));
            }
        }

        private void setRecorderCommon()
        {
            if (mainModel.ConditionRecorderCommon != null)
            {
                Func.conditionRecorder recorder = mainModel.ConditionRecorderCommon;
                this.dateTimePickerBeginTimeDT.Value = recorder.dateTimePickerBeginTimeDTValue;
                this.dateTimePickerBeginTimeScan.Value = recorder.dateTimePickerBeginTimeScanValue;
                this.dateTimePickerEndTimeDT.Value = recorder.dateTimePickerEndTimeDTValue;
                this.dateTimePickerEndTimeScan.Value = recorder.dateTimePickerEndTimeScanValue;
                this.listViewProjectDT.Items.Clear();
                this.listViewProjectScan.Items.Clear();
                this.listViewServiceDT.Items.Clear();
                this.listViewServiceScan.Items.Clear();
                foreach (saveItem item in recorder.listViewProjectDTItems)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = item.text;
                    lvi.Tag = item.tag;
                    this.listViewProjectDT.Items.Add(lvi);
                }
                foreach (saveItem item in recorder.listViewProjectScanItems)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = item.text;
                    lvi.Tag = item.tag;
                    this.listViewProjectScan.Items.Add(lvi);
                }
                foreach (saveItem item in recorder.listViewServiceDTItems)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = item.text;
                    lvi.Tag = item.tag;
                    this.listViewServiceDT.Items.Add(lvi);
                }
                foreach (saveItem item in recorder.listViewServiceScanItems)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = item.text;
                    lvi.Tag = item.tag;
                    this.listViewServiceScan.Items.Add(lvi);
                }
            }
        }

        public void setDefaultService()
        {
            ListViewItem lviDt = new ListViewItem();
            lviDt.Text = "GSM语音业务";
            lviDt.Tag = 1;
            this.listViewServiceDT.Items.Add(lviDt);

            ListViewItem lviScan = new ListViewItem();
            lviScan.Text = "扫频业务";
            lviScan.Tag = 12;
            this.listViewServiceScan.Items.Add(lviScan);
        }

        private void buttonServScan_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(buttonServScan.Width, buttonServScan.Height);
            toolStripDropDownServiceScan.Show(buttonServScan, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void buttonServDT_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(buttonServDT.Width, buttonServDT.Height);
            toolStripDropDownServiceDT.Show(buttonServDT, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void buttonProjDT_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(buttonProjDT.Width, buttonProjDT.Height);
            toolStripDropDownProjectDT.Show(buttonProjDT, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void buttonProjScan_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(buttonProjScan.Width, buttonProjScan.Height);
            toolStripDropDownProjectScan.Show(buttonProjScan, pt, ToolStripDropDownDirection.BelowLeft);
        }

        protected virtual void buttonQuery_Click(object sender, EventArgs e)
        {

        }

        /// <summary>
        /// 获取窗体右侧的数据源，一般为扫频数据
        /// </summary>
        /// <returns></returns>
        public bool GetPeriodProjServScan()
        {
            if (isValidCondition())
            {
                CurConditionScan.Periods.Clear();
                CurConditionScan.Periods.Add(new TimePeriod(dateTimePickerBeginTimeScan.Value.Date, dateTimePickerEndTimeScan.Value.Date.AddDays(1).AddMilliseconds(-1)));
                CurConditionScan.Projects.Clear();
                CurConditionScan.ServiceTypes.Clear();
                foreach (ListViewItem item in listViewProjectScan.Items)
                {
                    CurConditionScan.Projects.Add((byte)(int)item.Tag);
                }
                foreach (ListViewItem item in listViewServiceScan.Items)
                {
                    CurConditionScan.ServiceTypes.Add((byte)(int)item.Tag);
                }
                CurConditionScan.DistrictIDs.Clear();
                CurConditionScan.DistrictIDs.Add(mainModel.DistrictID);
                return true;
            }
            return false;
        }

        /// <summary>
        /// 获取窗体左侧的数据源，一般为路测数据
        /// </summary>
        /// <returns></returns>
        public bool GetPeriodProjServDT()
        {
            if (isValidCondition())
            {
                CurConditionDT.Periods.Clear();
                CurConditionDT.Periods.Add(new TimePeriod(dateTimePickerBeginTimeDT.Value.Date, dateTimePickerEndTimeDT.Value.Date.AddDays(1).AddMilliseconds(-1)));
                CurConditionDT.Projects.Clear();
                CurConditionDT.ServiceTypes.Clear();
                foreach (ListViewItem item in listViewProjectDT.Items)
                {
                    CurConditionDT.Projects.Add((byte)(int)item.Tag);
                }
                foreach (ListViewItem item in listViewServiceDT.Items)
                {
                    CurConditionDT.ServiceTypes.Add((byte)(int)item.Tag);
                }
                CurConditionDT.DistrictIDs.Clear();
                CurConditionDT.DistrictIDs.Add(mainModel.DistrictID);
                return true;
            }
            return false;
        }

        protected bool isValidCondition()
        {
            if (dateTimePickerBeginTimeDT.Value > dateTimePickerEndTimeDT.Value)
            {
                MessageBox.Show("查询路测数据，结束时间必须大于开始时间。");
            }
            if (dateTimePickerBeginTimeScan.Value > dateTimePickerEndTimeScan.Value)
            {
                MessageBox.Show("查询扫频数据，结束时间必须大于开始时间。");
            }
            else if (listViewProjectDT.Items.Count <= 0)
            {
                MessageBox.Show("查询路测数据，至少需要选择一个项目。");
            }
            else if (listViewServiceDT.Items.Count <= 0)
            {
                MessageBox.Show("查询路测数据，至少需要选择一个业务。");
            }
            else if (listViewProjectScan.Items.Count <= 0)
            {
                MessageBox.Show("查询扫频数据，至少需要选择一个项目。");
            }
            else if (listViewServiceScan.Items.Count <= 0)
            {
                MessageBox.Show("查询扫频数据，至少需要选择一个业务。");
            }
            else
                return true;
            return false;
        }

        private void DataComparisonForm_Load(object sender, EventArgs e)
        {
            setDefaultService();
        }
    }

    public class conditionRecorder
    {
        public DateTime dateTimePickerBeginTimeDTValue{ get; set; }
        public DateTime dateTimePickerBeginTimeScanValue{ get; set; }
        public DateTime dateTimePickerEndTimeDTValue{ get; set; }
        public DateTime dateTimePickerEndTimeScanValue{ get; set; }
        public int trackBarChannelValue{ get; set; }
        public int numUdMakeupRxlevValue{ get; set; }
        public Kind kind{ get; set; }
        public List<saveItem> listViewProjectDTItems { get; set; } = new List<saveItem>();
        public List<saveItem> listViewProjectScanItems { get; set; } = new List<saveItem>();
        public List<saveItem> listViewServiceDTItems { get; set; } = new List<saveItem>();
        public List<saveItem> listViewServiceScanItems { get; set; } = new List<saveItem>();
        public enum Kind
        {
            CellComp,
            RxlevComp,
            StrongCellDeficiencyByRegion,
            StrongCellDeficencyByCell,
            Common
        }

        /// <summary>
        /// 保存操作条件
        /// </summary>
        /// <param name="conditionKind"></param>
        /// <param name="beginTimeDTValue"></param>
        /// <param name="beginTimeScanValue"></param>
        /// <param name="endTimeDTValue"></param>
        /// <param name="endTimeScanValue"></param>
        /// <param name="projectDTItems"></param>
        /// <param name="projectScanItems"></param>
        /// <param name="serviceDTItems"></param>
        /// <param name="serviceScanItems"></param>
        /// <param name="channelValue"></param>
        public void fill( ListView.ListViewItemCollection projectDTItems, ListView.ListViewItemCollection projectScanItems, ListView.ListViewItemCollection serviceDTItems, ListView.ListViewItemCollection serviceScanItems, int channelValue)
        {
            foreach (ListViewItem lvi in projectDTItems)
            {
                saveItem i = new saveItem(lvi.Text, lvi.Tag);
                listViewProjectDTItems.Add(i);
            }
            foreach (ListViewItem lvi in projectScanItems)
            {
                saveItem i = new saveItem(lvi.Text, lvi.Tag);
                listViewProjectScanItems.Add(i);
            }
            foreach (ListViewItem lvi in serviceDTItems)
            {
                saveItem i = new saveItem(lvi.Text, lvi.Tag);
                listViewServiceDTItems.Add(i);
            }
            foreach (ListViewItem lvi in serviceScanItems)
            {
                saveItem i = new saveItem(lvi.Text, lvi.Tag);
                listViewServiceScanItems.Add(i);
            }
            trackBarChannelValue = channelValue;
        }

        /// <summary>
        /// 保存操作条件
        /// </summary>
        /// <param name="conditionKind"></param>
        /// <param name="beginTimeDTValue"></param>
        /// <param name="beginTimeScanValue"></param>
        /// <param name="endTimeDTValue"></param>
        /// <param name="endTimeScanValue"></param>
        /// <param name="projectDTItems"></param>
        /// <param name="projectScanItems"></param>
        /// <param name="serviceDTItems"></param>
        /// <param name="serviceScanItems"></param>
        /// <param name="channelValue"></param>
        /// <param name="makeupRxlevValue"></param>
        public void fill(ListView.ListViewItemCollection projectDTItems, ListView.ListViewItemCollection projectScanItems, ListView.ListViewItemCollection serviceDTItems, ListView.ListViewItemCollection serviceScanItems, int channelValue, int makeupRxlevValue)
        {
            foreach (ListViewItem lvi in projectDTItems)
            {
                saveItem i = new saveItem(lvi.Text, lvi.Tag);
                listViewProjectDTItems.Add(i);
            }
            foreach (ListViewItem lvi in projectScanItems)
            {
                saveItem i = new saveItem(lvi.Text, lvi.Tag);
                listViewProjectScanItems.Add(i);
            }
            foreach (ListViewItem lvi in serviceDTItems)
            {
                saveItem i = new saveItem(lvi.Text, lvi.Tag);
                listViewServiceDTItems.Add(i);
            }
            foreach (ListViewItem lvi in serviceScanItems)
            {
                saveItem i = new saveItem(lvi.Text, lvi.Tag);
                listViewServiceScanItems.Add(i);
            }
            trackBarChannelValue = channelValue;
            numUdMakeupRxlevValue = makeupRxlevValue;
        }

        /// <summary>
        /// 保存操作条件
        /// </summary>
        /// <param name="conditionKind"></param>
        /// <param name="beginTimeDTValue"></param>
        /// <param name="beginTimeScanValue"></param>
        /// <param name="endTimeDTValue"></param>
        /// <param name="endTimeScanValue"></param>
        /// <param name="projectDTItems"></param>
        /// <param name="projectScanItems"></param>
        /// <param name="serviceDTItems"></param>
        /// <param name="serviceScanItems"></param>
        public void fill(ListView.ListViewItemCollection projectDTItems, ListView.ListViewItemCollection projectScanItems, ListView.ListViewItemCollection serviceDTItems, ListView.ListViewItemCollection serviceScanItems)
        {
            foreach (ListViewItem lvi in projectDTItems)
            {
                saveItem i = new saveItem(lvi.Text, lvi.Tag);
                listViewProjectDTItems.Add(i);
            }
            foreach (ListViewItem lvi in projectScanItems)
            {
                saveItem i = new saveItem(lvi.Text, lvi.Tag);
                listViewProjectScanItems.Add(i);
            }
            foreach (ListViewItem lvi in serviceDTItems)
            {
                saveItem i = new saveItem(lvi.Text, lvi.Tag);
                listViewServiceDTItems.Add(i);
            }
            foreach (ListViewItem lvi in serviceScanItems)
            {
                saveItem i = new saveItem(lvi.Text, lvi.Tag);
                listViewServiceScanItems.Add(i);
            }
        }

        public class a
        {
            public Kind ConditionKind { get; set; }
            public DateTime BeginTimeDTValue{ get; set; }
            public DateTime BeginTimeScanValue{ get; set; }
            public DateTime EndTimeDTValue{ get; set; }
            public DateTime EndTimeScanValue{ get; set; }
            public ListView.ListViewItemCollection ProjectDTItems{ get; set; }
            public ListView.ListViewItemCollection ProjectScanItems{ get; set; }
            public ListView.ListViewItemCollection ServiceDTItems{ get; set; }
            public ListView.ListViewItemCollection ServiceScanItems{ get; set; }
        }
    }

    public class saveItem
    {
        public string text { get; set; }
        public object tag { get; set; }
        public saveItem(string text, object tag)
        {
            this.text = text;
            this.tag = tag;
        }
    }
}
