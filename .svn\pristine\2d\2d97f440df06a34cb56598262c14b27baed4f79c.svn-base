﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc;
using StationRoadDistanceUtil;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFuncZTAngleCalculate
{
    public partial class ConditionSettingForm : BaseForm
    {
        public string fileFullName { get; set; }
        public ConditionSettingForm()
        {
            InitializeComponent();
            this.DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }

        private void buttonSelectFile_Click(object sender, EventArgs e)
        {
            OpenFileDialog op = new OpenFileDialog();
            op.Filter = "Excel|*.xls;*.xlsx";
            if (op.ShowDialog() != System.Windows.Forms.DialogResult.OK) return;

            this.fileFullName = op.FileName;
            this.textBoxFileFullName.Text = fileFullName;
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(this.textBoxFileFullName.Text))
            {
                MessageBox.Show("请选择文件!");
                return;
            }
            this.DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }

        public Condition GetCondition()
        {
            Condition con = new Condition();
            con.FileFullName = this.textBoxFileFullName.Text;
            return con;
        }
    }
}
