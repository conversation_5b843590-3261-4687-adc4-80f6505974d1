﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using Microsoft.Office.Interop.Excel;
using System.Reflection;
using MasterCom.Util;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTProblemSummary4G : MinCloseForm
    {
        public CQTProblemSummary4G(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            this.mainModel = mainModel;
        }
        MainModel mainModel;

        List<ProblemPont4G> problemPointList = null;
        List<ProblemSummaryPont4G> problemPointSummarryList = null;
        List<List<NPOIRow>> nrDatasList;
        List<string> sheetNames;
        public void filldataSummary4G(List<ProblemPont4G> problemPointListTem, List<ProblemSummaryPont4G> problemPointSummarryListTem)
        {
            problemPointList = new List<ProblemPont4G>();
            problemPointSummarryList = new List<ProblemSummaryPont4G>();
            problemPointList.AddRange(problemPointListTem);
            this.dataGridXQ.DataSource = problemPointList;
            this.dataGridXQ.RefreshDataSource();

            problemPointSummarryList.AddRange(problemPointSummarryListTem);
            this.dataGridSum.DataSource = problemPointSummarryList;
            this.dataGridSum.RefreshDataSource();
        }

        private void dealMainUtranCellSample()
        {
            List<NPOIRow> datas = new List<NPOIRow>();
            List<object> cols = new List<object>();
            NPOIRow nr1 = new NPOIRow();

            List<NPOIRow> dataSummary = new List<NPOIRow>();
            List<object> colSummary = new List<object>();
            NPOIRow nr2 = new NPOIRow();

            #region EXCEL-SHEET1列表构造
            cols.Add("序号");
            cols.Add("城市");
            cols.Add("测试日期");
            cols.Add("测试点名称");
            cols.Add("测试点经度");
            cols.Add("测试点纬度");
            cols.Add("场所属性");
            cols.Add("覆盖属性");
            cols.Add("匹配站点信息");
            cols.Add("问题类型");

            nr1.cellValues = cols;
            datas.Add(nr1);

            #endregion
            #region EXCEL-SHEET2列表构造
            colSummary.Add("城市类别");
            colSummary.Add("城市");
            colSummary.Add("CSFB回落不达标");
            colSummary.Add("全程呼叫成功率不达标");
            colSummary.Add("语音未达标点数");
            colSummary.Add("下载速率低");
            colSummary.Add("下载掉线");
            colSummary.Add("数据未达标点数");
            colSummary.Add("问题点总数");
            colSummary.Add("语音测试总数");
            colSummary.Add("数据测试总数");
            colSummary.Add("测试总数");
            colSummary.Add("语音通过率");
            colSummary.Add("数据通过率");
            colSummary.Add("测试通过率");

            nr2.cellValues = colSummary;
            dataSummary.Add(nr2);
            #endregion

            #region 问题点详情
            foreach (ProblemPont4G pro in problemPointList)
            {
                NPOIRow nr7 = new NPOIRow();
                List<object> obj7s = new List<object>();
                obj7s.Add(pro.IID);
                obj7s.Add(pro.StrCity);
                obj7s.Add(pro.StrTestTime);
                obj7s.Add(pro.StrTestPoint);
                obj7s.Add(pro.DLongitude.ToString());
                obj7s.Add(pro.DLatitude.ToString());
                obj7s.Add(pro.Strcqttype);
                obj7s.Add(pro.StrCoverType);
                obj7s.Add(pro.StrValue8);
                obj7s.Add(pro.StrMainType);

                nr7.cellValues = obj7s;
                datas.Add(nr7);
            }
            #endregion

            #region 问题点汇总
            foreach (ProblemSummaryPont4G pro in problemPointSummarryList)
            {
                NPOIRow nr7 = new NPOIRow();
                List<object> obj7s = new List<object>();
                obj7s.Add(pro.StrCityType);
                obj7s.Add(pro.StrCity);
                obj7s.Add(pro.ICallCSFBLess);
                obj7s.Add(pro.ICallSuccessLess);
                obj7s.Add(pro.ILessThenColeVoice);
                obj7s.Add(pro.ILessDownDn);
                obj7s.Add(pro.IDownDrop);
                obj7s.Add(pro.ILessThenColeData);
                obj7s.Add(pro.ICityColePoint);
                obj7s.Add(pro.ITestColeVioce);
                obj7s.Add(pro.ITestColeData);
                obj7s.Add(pro.ITestCole);
                obj7s.Add(pro.StrTestSucessVioce);
                obj7s.Add(pro.StrTestSucessData);
                obj7s.Add(pro.StrTestSucess);

                nr7.cellValues = obj7s;
                dataSummary.Add(nr7);
            }
            #endregion

            nrDatasList = new List<List<NPOIRow>>();
            nrDatasList.Add(datas);
            nrDatasList.Add(dataSummary);

            sheetNames = new List<string>();
            sheetNames.Add("4G问题点详情");
            sheetNames.Add("4G问题点汇总");
        }

        private void miOutPutExcel_Click(object sender, EventArgs e)
        {
            dealMainUtranCellSample();
            ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames);
        }
    }
}
