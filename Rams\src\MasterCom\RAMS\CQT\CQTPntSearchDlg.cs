﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTPntSearchDlg : BaseDialog
    {
        public CQTPntSearchDlg()
            : base()
        {
            InitializeComponent();
        }

        private void selAndGo2Point(CQTPoint pnt)
        {
            mainModel.SelCQTPoint = pnt;
            mainModel.MainForm.GetMapForm().GoToView(pnt.Longitude, pnt.Latitude);
        }

        private void btnFind_Click(object sender, EventArgs e)
        {
            string text = txtPnt.Text;
            if (string.IsNullOrEmpty(text))
            {
                MessageBox.Show("搜索名称不能为空！");
                return;
            }
            listView.Items.Clear();
            List<CQTPoint> pnts = CQTPointManager.GetInstance().FindByName(text);
            foreach (CQTPoint pnt in pnts)
            {
                ListViewItem item = listView.Items.Add(pnt.Name);
                item.Tag = pnt;
            }
            if (pnts.Count > 0)
            {
                selAndGo2Point(pnts[0]);
            }
        }

        private void listView_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listView.SelectedItems.Count == 0)
            {
                return;
            }
            CQTPoint pnt = listView.SelectedItems[0].Tag as CQTPoint;
            selAndGo2Point(pnt);
        }

    }
}
