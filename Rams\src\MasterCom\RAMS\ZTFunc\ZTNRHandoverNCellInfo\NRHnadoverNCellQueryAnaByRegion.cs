﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRHnadoverNCellQueryAnaByRegion : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();
        private static NRHnadoverNCellQueryAnaByRegion instance = null;

        protected List<NRHandoverNCellInfo> handoverInfoList = new List<NRHandoverNCellInfo>();
        protected NRHnadoverNCellInfoCondition curCondition = new NRHnadoverNCellInfoCondition();

        protected List<int> statNREvtIDs = null;
        protected List<int> statLteEvtIDs = null;

        public static NRHnadoverNCellQueryAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new NRHnadoverNCellQueryAnaByRegion();
                    }
                }
            }
            return instance;
        }

        protected NRHnadoverNCellQueryAnaByRegion()
            : base(MainModel.GetInstance())
        {
            ServiceTypes = ServiceTypeManager.GetServiceTypesByServiceName(ServiceName.NR);
            Columns = NRTpHelper.InitBaseReplayParamBackground(true, true);

            statLteEvtIDs = NREventHelper.HandoverHelper.GetIncludeLTEHandover();
            statNREvtIDs = NREventHelper.HandoverHelper.GetIncludeNRHandover();
        }

        public override string Name
        {
            get
            {
                return "NR切换主邻区信息(按区域)";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 35000, 35024, this.Name);
        }

        protected override bool getCondition()
        {
            NRHnadoverNCellInfoDlg dlg = new NRHnadoverNCellInfoDlg();
            dlg.SetCondition(curCondition);
            if (DialogResult.OK != dlg.ShowDialog())
            {
                return false;
            }
            dlg.GetCondition(curCondition);
            return true;
        }

        protected override void doSomethingAfterAnalyseFiles()
        {
            handoverInfoList.Sort((a, b) =>
                a.FileName.CompareTo(b.FileName) * 2 +
                a.DateTime.CompareTo(b.DateTime)
            );
        }

        protected override void fireShowForm()
        {
            if (handoverInfoList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            NRHnadoverNCellInfoForm frm = MainModel.CreateResultForm(typeof(NRHnadoverNCellInfoForm)) as NRHnadoverNCellInfoForm;
            frm.FillData(handoverInfoList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
        protected override void clearDataBeforeAnalyseFiles()
        {
            handoverInfoList = new List<NRHandoverNCellInfo>();
        }

        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    dealEvts(file, statNREvtIDs, true);
                    if (curCondition.IsAnaLTE)
                    {
                        NREventHelper.HandoverHelper.FilterHandoverEvents(file.Events);
                        dealEvts(file, statLteEvtIDs, false);
                    }
                }
            }
            catch (Exception ee)
            {
                System.Windows.Forms.MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }

        private void dealEvts(DTFileDataManager file, List<int> statEvtIDs, bool isNREvt)
        {
            foreach (Event evt in file.Events)
            {
                if (!statEvtIDs.Contains(evt.ID))
                {
                    continue;
                }

                NRHandOverType type = NREventHelper.HandoverHelper.GetHandoverType(evt.ID, true);
                NRHandoverNCellInfo handoverInfo = new NRHandoverNCellInfo(file.FileName);
                addToResultList(evt, file.TestPoints, handoverInfo, type, isNREvt);


                //if (filterNREvt(evt))
                //{
                //    NRHandoverNCellInfo handoverInfo = new NRHandoverNCellInfo(file.FileName);
                //    addToResultList(evt, file.TestPoints, ref handoverInfo, true);
                //}
                //else if (filterLTEEvt(evt))
                //{
                //    NRHandoverNCellInfo handoverInfo = new NRHandoverNCellInfo(file.FileName);
                //    addToResultList(evt, file.TestPoints, ref handoverInfo, false);
                //}
            }
        }

        private void addToResultList(Event evt, List<TestPoint> tpList, NRHandoverNCellInfo curItem, NRHandOverType type, bool isNREvt)
        {
            setCellInfoByEvt(evt, curItem, type);
            curItem.Evt = evt;
            curItem.DateTime = evt.DateTime;
            curItem.Time = evt.DateTime.ToString();
            curItem.Longitude = evt.Longitude;
            curItem.Latitude = evt.Latitude;
            TestPoint lastTp;
            int tpIndex = getTestpointIndex(tpList, evt);
            for (int index = tpIndex; index > 0; index--)
            {
                lastTp = tpList[tpIndex];
                curItem.TestPonitList.Add(lastTp);
                bool isAdd = dealNBCellInfo(curItem, isNREvt, lastTp);
                //if (GetRSRP(lastTp, isNREvt) != null || (evt.DateTime - lastTp.DateTime).Seconds > 30)
                if(isAdd)
                {
                    curItem.Rsrp = GetRSRP(lastTp, isNREvt);
                    break;
                }
            }
            //curItem.SN = handoverInfoList.Count + 1;
            handoverInfoList.Add(curItem);
        }

        //private bool filterNREvt(Event evt)
        //{
        //    bool isValidEvt = NREventHelper.HandoverHelper.JudgeHandoverSuccess(evt.ID, false) 
        //        && evt.CellNameSrc != "-1_-1";
        //    return isValidEvt;
        //}

        //private bool filterLTEEvt(Event evt)
        //{
        //    bool isValidEvt = curCondition.IsAnaLTE 
        //        && NREventHelper.HandoverHelper.JudgeHandoverSuccess(evt.ID, true)
        //        && evt.CellNameSrc != "-1_-1";
        //    return isValidEvt;
        //}

        //private void addToResultList(Event evt, List<TestPoint> tpList, ref NRHandoverNCellInfo curItem, bool isNR)
        //{
        //    setCellInfoByEvt(evt, curItem, isNR);
        //    curItem.Evt = evt;
        //    curItem.Time = evt.DateTime.ToString();
        //    curItem.Longitude = evt.Longitude;
        //    curItem.Latitude = evt.Latitude;
        //    TestPoint lastTp;
        //    int tpIndex = getTestpointIndex(tpList, evt);
        //    for (int index = tpIndex; index > 0; index--)
        //    {
        //        lastTp = tpList[tpIndex];
        //        curItem.TestPonitList.Add(lastTp);
        //        dealNBCellInfo(curItem, isNR, lastTp);
        //        if (GetRSRP(lastTp, isNR) != null || (evt.DateTime - lastTp.DateTime).Seconds > 60)
        //        {
        //            curItem.Rsrp = GetRSRP(lastTp, isNR);
        //            break;
        //        }
        //    }
        //    curItem.SN = handoverInfoList.Count + 1;
        //    handoverInfoList.Add(curItem);
        //}

        private bool dealNBCellInfo(NRHandoverNCellInfo curItem, bool isNR, TestPoint lastTp)
        {
            bool isAdd = false;
            for (int i = 0; i < 16; i++)
            {
                bool isNCell = true;
                if (isNR)
                {
                    isNCell = NRTpHelper.NrTpManager.JudgeIsNCell(lastTp, i);
                }

                if (isNCell)
                {
                    bool flag = addNBCellInfo(curItem, isNR, lastTp, i);
                    if (!isAdd && flag)
                    {
                        isAdd = true;
                    }
                }
            }
            return isAdd;
        }

        private bool addNBCellInfo(NRHandoverNCellInfo curItem, bool isNR, TestPoint lastTp, int i)
        {
            if (isNR)
            {
                NRCell cell = lastTp.GetNBCell_NR(i);
                if (cell != null && cell.Name != "-1_-1")
                {
                    NRNCellInfo ncell = new NRNCellInfo();
                    ncell.SN = curItem.NcellList.Count + 1;
                    ncell.CellType = "NR";
                    ncell.CellName = cell.Name;
                    ncell.Earfcn = cell.SSBARFCN;
                    ncell.Pci = cell.PCI;
                    ncell.TAC = cell.TAC;
                    ncell.NCI = cell.NCI;
                    ncell.Longitude = cell.Longitude;
                    ncell.Latitude = cell.Latitude;
                    ncell.Rsrp = GetNRSRP(lastTp, i, isNR);
                    curItem.NcellList.Add(ncell);
                    return true;
                }
            }
            else
            {
                LTECell cell = lastTp.GetNBCell_LTE(i);
                if (cell != null && cell.Name != "-1_-1")
                {
                    NRNCellInfo ncell = new NRNCellInfo();
                    ncell.SN = curItem.NcellList.Count + 1;
                    ncell.CellType = "LTE";
                    ncell.CellName = cell.Name;
                    ncell.Earfcn = cell.EARFCN;
                    ncell.Pci = cell.PCI;
                    ncell.TAC = cell.TAC;
                    ncell.NCI = cell.ECI;
                    ncell.Longitude = cell.Longitude;
                    ncell.Latitude = cell.Latitude;
                    ncell.Rsrp = GetNRSRP(lastTp, i, isNR);
                    curItem.NcellList.Add(ncell);
                    return true;
                }
            }
            return false;
        }

        private void setCellInfoByEvt(Event evt, NRHandoverNCellInfo curItem, NRHandOverType type)
        {
            NRHandoverEventHelper.HandOverCellInfo cellInfo = NREventHelper.HandoverHelper.GetHandOverCellInfo(evt);
            if (NREventHelper.HandoverHelper.JudgeIncludeLTEHandover(type))
            {
                curItem.Earfcn = cellInfo.LTESrcCell.ARFCN.ToString();
                curItem.Pci = cellInfo.LTESrcCell.PCI.ToString();
                curItem.TAC = cellInfo.LTESrcCell.TAC.ToString();
                curItem.NCI = cellInfo.LTESrcCell.NCI.ToString();
                curItem.CellName = cellInfo.LTESrcCell.Cell?.Name;
            }
            else if (NREventHelper.HandoverHelper.JudgeIncludeNRHandover(type))
            {
                curItem.Earfcn = cellInfo.NRSrcCell.ARFCN.ToString();
                curItem.Pci = cellInfo.NRSrcCell.PCI.ToString();
                curItem.TAC = cellInfo.NRSrcCell.TAC.ToString();
                curItem.NCI = cellInfo.NRSrcCell.NCI.ToString();
                curItem.CellName = cellInfo.NRSrcCell.Cell?.Name;
            }

            curItem.CellType = NREventHelper.HandoverHelper.GetHandoverTypeDesc(type);

           //ICell cell;
           // if (isNR)
           // {
           //     HandOverEventBase.CellInfo info = NREventHelper.HandoverHelper.NSANR.GetSrcCellInfo(evt);
           //     curItem.CellType = "NR";
           //     curItem.Earfcn = info.ARFCN.ToString();
           //     curItem.Pci = info.PCI.ToString();
           //     cell = info.Cell;
           // }
           // else
           // {
           //     HandOverEventBase.CellInfo info = NREventHelper.HandoverHelper.NSALTE.GetSrcCellInfo(evt);
           //     curItem.CellType = "LTE";
           //     curItem.Earfcn = info.ARFCN.ToString();
           //     curItem.Pci = info.PCI.ToString();
           //     cell = info.Cell;
           // }

            // if (cell == null)
            // {
            //     curItem.CellType = "";
            // }
            // else
            // {
            //     curItem.CellName = cell.Name;
            // }
        }

        private int getTestpointIndex(List<TestPoint> tpList, Event e)
        {
            int index = -1;
            for (int i = 0; i < tpList.Count; i++)
            {
                if (tpList[i].SN > e.SN)
                {
                    index = i - 1;
                    break;
                }
                if (tpList[i].SN == e.SN)
                {
                    index = i;
                    break;
                }
            }

            return index;
        }

        protected virtual float? GetRSRP(TestPoint tp, bool isNR)
        {
            if (isNR)
            {
                return NRTpHelper.NrTpManager.GetSCellRsrp(tp);
            }
            return NRTpHelper.NrLteTpManager.GetSCellRsrp(tp);
        }

        protected virtual float? GetNRSRP(TestPoint tp, int index, bool isNR)
        {
            if (isNR)
            {
                return NRTpHelper.NrTpManager.GetNCellRsrp(tp, index);
            }
            return NRTpHelper.NrLteTpManager.GetNCellRsrp(tp, index);
        }
    }
}
