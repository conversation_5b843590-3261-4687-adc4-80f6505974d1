﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.NewBlackBlock
{
    public class QueryFusionPerfData : DIYSQLBase
    {
        protected override string getSqlTextString()
        {
            MainDB = true;
            return string.Format(@"SELECT [time],[lac],[ci],[extend] FROM {0}.[dbo].[tb_perf_cell_dd_{1}]
 where ({2}) and cityid={3}"
                , CellAlarmData.FusionDB
                , this.Time.ToString("yyMMdd")
                , CellCondition
                , this.DistrictID);
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            E_VType[] arr = new E_VType[4];
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i] = E_VType.E_VARYBIN;
            return arr;
        }

        public Event Event
        {
            get;
            set;
        }
        private DateTime time;
        public DateTime Time
        {
            get
            {
                if (Event != null)
                {
                    return Event.DateTime;
                }
                return time;
            }
            set
            {
                time = value;
            }
        }

        private int districtID;
        public int DistrictID
        {
            get
            {
                if (Event != null)
                {
                    return Event.DistrictID;
                }
                return districtID;
            }
            set
            {
                districtID = value;
            }
        }
        public string CellCondition
        {
            get;
            set;
        }

        public Dictionary<string, LTECell> CellDic
        {
            get;
            set;
        }

        public List<CellPerfData> CellPerfSet
        {
            get;
            set;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            if (CellPerfSet == null)
            {
                CellPerfSet = new List<CellPerfData>();
            }
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CellPerfData cellPerf = new CellPerfData();
                    cellPerf.BeginTime = DateTime.Parse(package.Content.GetParamString());
                    //if (Math.Abs((this.Time - cellPerf.BeginTime).TotalHours) > 1)
                    //{
                    //    continue;
                    //}
                    int tac = package.Content.GetParamInt();
                    int eci = package.Content.GetParamInt();
                    cellPerf.Cell = CellDic[string.Format("{0}_{1}", tac, eci)];
                    cellPerf.FillImg(package.Content.GetParamBytes());
                    cellPerf.DistrictId = this.DistrictID;
                    CellPerfSet.Add(cellPerf);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    break;
                }
            }
        }
    }
}
