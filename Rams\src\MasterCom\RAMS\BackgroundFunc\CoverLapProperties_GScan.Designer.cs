﻿namespace MasterCom.RAMS.Func.SystemSetting
{
    partial class CoverLapProperties_GScan
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.numAdjFreq = new DevExpress.XtraEditors.SpinEdit();
            this.numSameFreq = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.numDisFactor = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.numNearestCellCount = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.numMinRxLev = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl = new DevExpress.XtraEditors.GroupControl();
            this.chkBackgroundStat = new DevExpress.XtraEditors.CheckEdit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numAdjFreq.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSameFreq.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDisFactor.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numNearestCellCount.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinRxLev.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl)).BeginInit();
            this.groupControl.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkBackgroundStat.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.numAdjFreq);
            this.groupControl1.Controls.Add(this.numSameFreq);
            this.groupControl1.Controls.Add(this.labelControl7);
            this.groupControl1.Controls.Add(this.labelControl4);
            this.groupControl1.Controls.Add(this.numDisFactor);
            this.groupControl1.Controls.Add(this.labelControl3);
            this.groupControl1.Controls.Add(this.labelControl5);
            this.groupControl1.Controls.Add(this.numNearestCellCount);
            this.groupControl1.Controls.Add(this.labelControl6);
            this.groupControl1.Controls.Add(this.labelControl2);
            this.groupControl1.Controls.Add(this.numMinRxLev);
            this.groupControl1.Controls.Add(this.labelControl1);
            this.groupControl1.Location = new System.Drawing.Point(3, 62);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(448, 172);
            this.groupControl1.TabIndex = 5;
            this.groupControl1.Text = "条件设置";
            // 
            // numAdjFreq
            // 
            this.numAdjFreq.EditValue = new decimal(new int[] {
            9,
            0,
            0,
            -2147483648});
            this.numAdjFreq.Location = new System.Drawing.Point(151, 134);
            this.numAdjFreq.Name = "numAdjFreq";
            this.numAdjFreq.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numAdjFreq.Properties.IsFloatValue = false;
            this.numAdjFreq.Properties.Mask.EditMask = "N00";
            this.numAdjFreq.Size = new System.Drawing.Size(82, 21);
            this.numAdjFreq.TabIndex = 14;
            // 
            // numSameFreq
            // 
            this.numSameFreq.EditValue = new decimal(new int[] {
            9,
            0,
            0,
            0});
            this.numSameFreq.Location = new System.Drawing.Point(151, 107);
            this.numSameFreq.Name = "numSameFreq";
            this.numSameFreq.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSameFreq.Properties.IsFloatValue = false;
            this.numSameFreq.Properties.Mask.EditMask = "N00";
            this.numSameFreq.Size = new System.Drawing.Size(82, 21);
            this.numSameFreq.TabIndex = 13;
            // 
            // labelControl7
            // 
            this.labelControl7.Location = new System.Drawing.Point(76, 137);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(69, 14);
            this.labelControl7.TabIndex = 12;
            this.labelControl7.Text = "邻频保护比≥";
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(76, 110);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(69, 14);
            this.labelControl4.TabIndex = 11;
            this.labelControl4.Text = "同频保护比≥";
            // 
            // numDisFactor
            // 
            this.numDisFactor.EditValue = new decimal(new int[] {
            16,
            0,
            0,
            65536});
            this.numDisFactor.Location = new System.Drawing.Point(151, 80);
            this.numDisFactor.Name = "numDisFactor";
            this.numDisFactor.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDisFactor.Properties.Increment = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.numDisFactor.Size = new System.Drawing.Size(82, 21);
            this.numDisFactor.TabIndex = 10;
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(37, 83);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(108, 14);
            this.labelControl3.TabIndex = 9;
            this.labelControl3.Text = "理想覆盖半径系数：";
            // 
            // labelControl5
            // 
            this.labelControl5.Location = new System.Drawing.Point(239, 56);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(12, 14);
            this.labelControl5.TabIndex = 8;
            this.labelControl5.Text = "个";
            // 
            // numNearestCellCount
            // 
            this.numNearestCellCount.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.numNearestCellCount.Location = new System.Drawing.Point(151, 53);
            this.numNearestCellCount.Name = "numNearestCellCount";
            this.numNearestCellCount.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numNearestCellCount.Properties.IsFloatValue = false;
            this.numNearestCellCount.Properties.Mask.EditMask = "N00";
            this.numNearestCellCount.Size = new System.Drawing.Size(82, 21);
            this.numNearestCellCount.TabIndex = 7;
            // 
            // labelControl6
            // 
            this.labelControl6.Location = new System.Drawing.Point(25, 56);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(120, 14);
            this.labelControl6.TabIndex = 6;
            this.labelControl6.Text = "理想覆盖参考基站数：";
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(239, 29);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(24, 14);
            this.labelControl2.TabIndex = 2;
            this.labelControl2.Text = "dBm";
            // 
            // numMinRxLev
            // 
            this.numMinRxLev.EditValue = new decimal(new int[] {
            90,
            0,
            0,
            -2147483648});
            this.numMinRxLev.Location = new System.Drawing.Point(151, 26);
            this.numMinRxLev.Name = "numMinRxLev";
            this.numMinRxLev.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMinRxLev.Properties.IsFloatValue = false;
            this.numMinRxLev.Properties.Mask.EditMask = "N00";
            this.numMinRxLev.Size = new System.Drawing.Size(82, 21);
            this.numMinRxLev.TabIndex = 1;
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(88, 29);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(57, 14);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "信号强度≥";
            // 
            // groupControl
            // 
            this.groupControl.Controls.Add(this.chkBackgroundStat);
            this.groupControl.Location = new System.Drawing.Point(3, 3);
            this.groupControl.Name = "groupControl";
            this.groupControl.Size = new System.Drawing.Size(448, 53);
            this.groupControl.TabIndex = 4;
            this.groupControl.Text = "开关设置";
            // 
            // chkBackgroundStat
            // 
            this.chkBackgroundStat.Location = new System.Drawing.Point(15, 26);
            this.chkBackgroundStat.Name = "chkBackgroundStat";
            this.chkBackgroundStat.Properties.Caption = "启用";
            this.chkBackgroundStat.Size = new System.Drawing.Size(75, 19);
            this.chkBackgroundStat.TabIndex = 0;
            // 
            // CoverLapProperties_GScan
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.groupControl1);
            this.Controls.Add(this.groupControl);
            this.Name = "CoverLapProperties_GScan";
            this.Size = new System.Drawing.Size(454, 280);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numAdjFreq.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSameFreq.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDisFactor.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numNearestCellCount.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinRxLev.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl)).EndInit();
            this.groupControl.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.chkBackgroundStat.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.SpinEdit numDisFactor;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.SpinEdit numNearestCellCount;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SpinEdit numMinRxLev;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.GroupControl groupControl;
        private DevExpress.XtraEditors.CheckEdit chkBackgroundStat;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.SpinEdit numAdjFreq;
        private DevExpress.XtraEditors.SpinEdit numSameFreq;
        private DevExpress.XtraEditors.LabelControl labelControl7;
    }
}
