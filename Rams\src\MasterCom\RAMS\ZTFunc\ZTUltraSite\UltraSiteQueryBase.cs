﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Func.Voronoi;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.ZTUltraSite;
using MasterCom.Util;
using MasterCom.Util.UiEx;
using MasterCom.MTGis;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Func.SystemSetting;

namespace MasterCom.RAMS.ZTFunc
{
    public class UltraSiteQueryBase : BackgroundQueryBase
    {
        Dictionary<ICell, List<UltraSiteInfo>> cellDic = null;
        public Dictionary<ICell, List<UltraSiteInfo>> Result
        {
            get { return cellDic; }
        }

        public UltraSiteCondition UltraSiteCondition { get; set; } = new UltraSiteCondition();
        public bool ShowSettingDlg { get; set; } = true;

        public UltraSiteQueryBase()
            : base(MainModel.GetInstance())
        {
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override string Name
        {
            get { return "三超站点分析"; }
        }

        public override string IconName
        {
            get { return null; }
        }

        protected override bool isValidCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (ShowSettingDlg && !showFuncCondSetDlg())
            {
                return false;
            }
            return UltraSiteCondition != null;
        }

        protected bool showFuncCondSetDlg()
        {
            SettingDlg dlg = new SettingDlg();
            dlg.SetCondition(this.UltraSiteCondition);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            this.UltraSiteCondition = dlg.GetCondition();
            return true;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override void query()
        {
            if (!MainModel.IsBackground)
            {
                if (!MainModel.QueryFromBackground)
                {
                    queryFront();
                }
                else
                {
                    getBackgroundData();
                }
            }
            else
            {
                if (mainModel.BackgroundStarted)
                {
                    cellDic = new Dictionary<ICell, List<UltraSiteInfo>>();
                    reportBackgroundInfo("正在分析三超站点...");
                    judgeSites();
                    reportBackgroundInfo("三超站点分析结束。");
                }
            }
        }

        private void queryFront()
        {
            cellDic = new Dictionary<ICell, List<UltraSiteInfo>>();
            WaitTextBox.Show("正在分析三超站点...", judgeSites);
            if (ShowSettingDlg && cellDic.Count == 0)
            {
                MessageBox.Show("无符合条件的站点！");
            }
            else if (ShowSettingDlg)
            {
                showResultForm(cellDic);
            }
        }

        protected virtual void showResultForm(Dictionary<ICell, List<UltraSiteInfo>> cellDic)
        {

        }

        protected virtual void judgeSites()
        {
            List<ISite> allSites = getAllSites();
            if (allSites.Count < 3)
            {
                ErrorInfo += "室外站数量小于3个，无法计算站间距！";
                return;
            }

            try
            {
                List<ISite> inRegionSites = new List<ISite>(allSites);
                List<ISite> outRegionSites = new List<ISite>();

                getRegionSites(inRegionSites, outRegionSites);

                Dictionary<ISite, SiteDelaunayTri> delaunayDic = getSiteDelaunayTri(allSites, outRegionSites);
                //先计算站间距，后面超高，超近小区会有站间距信息。
                Dictionary<ICell, UltraSiteInfo> cellDisDic = getCellDisInfo(allSites, inRegionSites, delaunayDic);

                dealUltraFarSite(cellDisDic);
                dealUltraHighSite(cellDisDic);
                dealUltraNearSite(inRegionSites, outRegionSites, cellDisDic);

                dealResultAfterQuery();
            }
            finally
            {
                WaitTextBox.Close();
            }
        }

        protected List<UltraHighSiteInfo> highCells;
        protected List<UltraNearSiteInfo> nearSites;
        protected List<UltraFarSiteInfo> farCells;
        protected virtual void dealResultAfterQuery()
        {
            highCells = new List<UltraHighSiteInfo>();
            nearSites = new List<UltraNearSiteInfo>();
            farCells = new List<UltraFarSiteInfo>();
            foreach (List<UltraSiteInfo> uSites in cellDic.Values)
            {
                foreach (UltraSiteInfo uSite in uSites)
                {
                    if (uSite is UltraHighSiteInfo)
                    {
                        UltraHighSiteInfo site = uSite as UltraHighSiteInfo;
                        site.MakeSummary();
                        highCells.Add(site);
                    }
                    else if (uSite is UltraNearSiteInfo)
                    {
                        UltraNearSiteInfo site = uSite as UltraNearSiteInfo;
                        site.MakeSummary();
                        nearSites.Add(site);
                    }
                    else if (uSite is UltraFarSiteInfo)
                    {
                        UltraFarSiteInfo site = uSite as UltraFarSiteInfo;
                        site.MakeSummary();
                        farCells.Add(site);
                    }
                }
            }
        }

        /// <summary>
        /// 获取一定范围内的所有宏站
        /// </summary>
        /// <returns></returns>
        protected virtual List<ISite> getAllSites()
        {
            List<LTEBTS> tempSites = getCurrentLTEBTSs();
            List<ISite> allSites = new List<ISite>();
            foreach (LTEBTS item in tempSites)
            {
                if (item.Type == LTEBTSType.Indoor)
                {
                    continue;
                }
                allSites.Add(item);
            }

            return allSites;
        }

        /// <summary>
        /// 将基站分为选择区域内和选择区域外
        /// </summary>
        /// <param name="inRegionSites"></param>
        /// <param name="outRegionSites"></param>
        protected void getRegionSites(List<ISite> inRegionSites, List<ISite> outRegionSites)
        {
            if (ShowSettingDlg && MainModel.SearchGeometrys != null && MainModel.SearchGeometrys.IsSelectRegion())
            {
                for (int i = 0; i < inRegionSites.Count; i++)
                {
                    ISite site = inRegionSites[i];
                    if (!MainModel.SearchGeometrys.GeoOp.Contains(site.Longitude, site.Latitude))
                    {
                        outRegionSites.Add(site);
                        inRegionSites.RemoveAt(i);
                        i--;
                    }
                }
            }
        }

        #region 构建delaunay三角网
        protected Dictionary<ISite, SiteDelaunayTri> getSiteDelaunayTri(List<ISite> allSites, List<ISite> filterSites)
        {
            WaitBox.Text = "正在构建delaunay三角网...";
            Dictionary<int, List<ISite>> posSiteDic = new Dictionary<int, List<ISite>>();
            List<Vertex> pnts = new List<Vertex>();
            foreach (ISite site in allSites)
            {
                Vertex pnt = new Vertex(site.Longitude, site.Latitude);
                if (!pnts.Contains(pnt))
                {//过滤掉相同经纬度的点
                    pnts.Add(pnt);
                }
                int posKey = site.Longitude.GetHashCode() ^ site.Latitude.GetHashCode();
                List<ISite> samePosSites = null;
                if (!posSiteDic.TryGetValue(posKey, out samePosSites))
                {
                    samePosSites = new List<ISite>();
                    posSiteDic.Add(posKey, samePosSites);
                }
                samePosSites.Add(site);
            }

            CTriangle cTriangle = new CTriangle();
            List<Vertex> cTriangleResult;
            try
            {
                cTriangleResult = cTriangle.Triangulate(pnts);
            }
            catch (Exception e)
            {
                if (e is ArgumentException)
                {
                    throw;
                }
                else
                {
                    ErrorInfo += "缺少CGeometry动态库！";
                }
                return new Dictionary<ISite, SiteDelaunayTri>();
            }
            System.Diagnostics.Debug.Assert(cTriangleResult.Count % 3 == 0, "构建delaunay三角网失败");
            Dictionary<ISite, SiteDelaunayTri> delaunayDic = new Dictionary<ISite, SiteDelaunayTri>();
            //每3个点，为一个delaunay三角
            for (int i = 0; i + 2 < cTriangleResult.Count; i++)
            {
                Vertex pt1 = cTriangleResult[i];
                Vertex pt2 = cTriangleResult[++i];
                Vertex pt3 = cTriangleResult[++i];
                List<ISite> site1 = posSiteDic[pt1.GetHashCode()];
                List<ISite> site2 = posSiteDic[pt2.GetHashCode()];
                List<ISite> site3 = posSiteDic[pt3.GetHashCode()];

                addOtherSite(filterSites, delaunayDic, site1, site2, site3);
                addOtherSite(filterSites, delaunayDic, site2, site1, site3);
                addOtherSite(filterSites, delaunayDic, site3, site1, site2);
            }
            return delaunayDic;
        }

        protected void addOtherSite(List<ISite> filterSites, Dictionary<ISite, SiteDelaunayTri> delaunayDic,
           List<ISite> siteA, List<ISite> siteB, List<ISite> siteC)
        {
            foreach (ISite site in siteA)
            {
                if (!filterSites.Contains(site))//(LTEBTS)
                {
                    SiteDelaunayTri dTri = null;
                    if (!delaunayDic.TryGetValue(site, out dTri))
                    {
                        dTri = new SiteDelaunayTri(site);
                        delaunayDic.Add(site, dTri);
                    }
                    foreach (ISite item in siteB)
                    {
                        dTri.AddOtherSite(item);
                    }
                    foreach (ISite item in siteC)
                    {
                        dTri.AddOtherSite(item);
                    }
                }
            }
        }
        #endregion

        protected Dictionary<ICell, UltraSiteInfo> getCellDisInfo(List<ISite> allSites, List<ISite> inRegionSites,
           Dictionary<ISite, SiteDelaunayTri> delaunayDic)
        {
            Dictionary<ICell, UltraSiteInfo> cellDisDic = new Dictionary<ICell, UltraSiteInfo>();
            foreach (ISite bts in inRegionSites)
            {
                string gridName = GISManager.GetInstance().GetGridDesc(bts.Longitude, bts.Latitude);
                string areaName = GISManager.GetInstance().GetAreaPlaceDesc(bts.Longitude, bts.Latitude);
                SiteDelaunayTri siteDTri = delaunayDic[bts];
                List<UltraSiteInfo> tempSites = getCellAvgDistanceByDir(bts, allSites);
                foreach (UltraSiteInfo site in tempSites)
                {
                    site.GridName = gridName;
                    site.AreaName = areaName;

                    site.SetDelaunayInfo(siteDTri);
                    cellDisDic[site.Cell] = site;
                }
            }
            return cellDisDic;
        }

        protected virtual List<UltraSiteInfo> getCellAvgDistanceByDir(ISite site, List<ISite> otherSites)
        {
            LTEBTS bts = site as LTEBTS;
            List<UltraSiteInfo> cellDisList = new List<UltraSiteInfo>();
            foreach (LTECell cell in bts.LatestCells)
            {
                dealCellDis(otherSites, bts, cellDisList, cell);
            }
            return cellDisList;
        }

        protected void dealCellDis(List<ISite> otherSites, ISite bts, List<UltraSiteInfo> cellDisList, ICell cell)
        {
            List<ISite> twoKMSites = new List<ISite>();
            List<ISite> tenKMSites = new List<ISite>();
            List<ISite> twtyKMSites = new List<ISite>();
            addSitesDistance(otherSites, bts, twoKMSites, tenKMSites, twtyKMSites);
            List<SiteDistance> nearestCells = new List<SiteDistance>();
            addInDirSites(nearestCells, cell, twoKMSites);
            if (nearestCells.Count < 3)
            {
                addInDirSites(nearestCells, cell, tenKMSites);
            }
            if (nearestCells.Count < 3)
            {
                addInDirSites(nearestCells, cell, twtyKMSites);
            }
            nearestCells.Sort();
            double disTotal = 0;
            List<SiteDistance> nSites = new List<SiteDistance>();
            for (int i = 0; i < 3 && i < nearestCells.Count; i++)
            {
                nSites.Add(nearestCells[i]);
                disTotal += nearestCells[i].Distance;
            }
            double avgDis = Math.Round(disTotal / nSites.Count, 2);
            UltraSiteInfo info = new UltraSiteInfo(cell);
            info.DistanceByDir = avgDis;
            info.DirSites = nSites;
            cellDisList.Add(info);
        }

        protected void addSitesDistance(List<ISite> otherSites, ISite bts, List<ISite> twoKMSites, List<ISite> tenKMSites, List<ISite> twtyKMSites)
        {
            foreach (ISite other in otherSites)
            {
                if (other == bts)
                {
                    continue;
                }
                double lngDis = Math.Abs(bts.Longitude - other.Longitude);
                double latDis = Math.Abs(bts.Latitude - other.Latitude);
                if (lngDis > 0.2 || latDis > 0.2)
                {//约20KM
                }
                else if (lngDis > 0.1 || latDis > 0.1)
                {//约10KM
                    twtyKMSites.Add(other);
                }
                else if (lngDis > 0.02 || latDis > 0.02)
                {//约2KM
                    tenKMSites.Add(other);
                }
                else
                {
                    twoKMSites.Add(other);
                }
            }
        }

        protected void addInDirSites(List<SiteDistance> sites, ICell cell, List<ISite> siteSet)
        {
            foreach (ISite site in siteSet)
            {
                if (MathFuncs.JudgePoint(cell.Longitude, cell.Latitude,
                        site.Longitude, site.Latitude, (int)cell.Direction
                        , UltraSiteCondition.AvgSitesDistanceAngle / 2))
                {//在扇区范围内
                    double distance = MathFuncs.GetDistance(cell.Longitude, cell.Latitude
                    , site.Longitude, site.Latitude);
                    sites.Add(new SiteDistance(site, distance));
                }
            }
        }

        protected void dealUltraFarSite(Dictionary<ICell, UltraSiteInfo> cellDisDic)
        {
            foreach (var site in cellDisDic.Values)
            {
                double distance = Math.Min(site.DistanceByDelaunay, site.DistanceByDir);
                if (UltraSiteCondition.IsTooFar(distance))
                {
                    site.Distance = distance;
                    addUltraFarSite(cellDisDic, site.Cell);
                }
            }
        }

        protected void addUltraFarSite(Dictionary<ICell, UltraSiteInfo> cellDisDic, ICell cell)
        {
            UltraFarSiteInfo fSite = new UltraFarSiteInfo(cell);
            fSite.SetValue(cellDisDic[cell]);
  
            List<UltraSiteInfo> ultraSet;
            if (!cellDic.TryGetValue(cell, out ultraSet))
            {
                ultraSet = new List<UltraSiteInfo>();
                cellDic.Add(cell, ultraSet);
            }
            ultraSet.Add(fSite);
        }

        protected void dealUltraHighSite(Dictionary<ICell, UltraSiteInfo> cellDisDic)
        {
            foreach (var site in cellDisDic.Values)
            {
                if (UltraSiteCondition.IsTooHighSite(site.Cell))
                    addUltraHighSite(cellDisDic, site.Cell);
            }
        }

        protected void dealUltraNearSite(List<ISite> inRegionSites, List<ISite> outRegionSites, Dictionary<ICell, UltraSiteInfo> cellDisDic)
        {
            for (int i = 0; i < inRegionSites.Count; i++)
            {
                //LTEBTS bts = inRegionSites[i] as LTEBTS;
                //超近
                dealInRegionSites(inRegionSites, cellDisDic, i, inRegionSites[i]);
                dealOutRegionSites(outRegionSites, cellDisDic, inRegionSites[i]);
            }
        }

        protected void addUltraHighSite(Dictionary<ICell, UltraSiteInfo> cellDisDic, ICell cell)
        {
            UltraHighSiteInfo hSite = new UltraHighSiteInfo(cell);
            hSite.SetValue(cellDisDic[cell]);
            List<UltraSiteInfo> ultraSet;
            if (!cellDic.TryGetValue(cell, out ultraSet))
            {
                ultraSet = new List<UltraSiteInfo>();
                cellDic.Add(cell, ultraSet);
            }
            ultraSet.Add(hSite);
        }

        protected void dealInRegionSites(List<ISite> inRegionSites, Dictionary<ICell, UltraSiteInfo> cellDisDic, int i, ISite bts)
        {
            for (int j = i + 1; j < inRegionSites.Count; j++)
            {
                ISite other = inRegionSites[j];
                double dis;
                if (UltraSiteCondition.IsTooNearSite(bts, other, out dis))
                {
                    addValidBts(cellDisDic, other, bts, dis);
                    addValidBts(cellDisDic, bts, other, dis);
                }
            }
        }

        protected virtual void addValidBts(Dictionary<ICell, UltraSiteInfo> cellDisDic, ISite bts, ISite judgedBts, double dis)
        {
            LTEBTS lteBts = bts as LTEBTS;
            LTEBTS lteJudgedBts = judgedBts as LTEBTS;

            foreach (LTECell cell in lteJudgedBts.LatestCells)
            {
                bool diffBand = false;
                foreach (LTECell tmp in lteBts.LatestCells)
                {
                    if (tmp.BandType != cell.BandType)
                    {
                        diffBand = true;
                        break;
                    }
                }
                if (diffBand && dis <= UltraSiteCondition.DiffBandDistanceMin)
                {//异频，且距离很近有可能是共站情况，过滤。
                    continue;
                }

                addUltraNearSite(cellDisDic, bts, dis, cell);
            }
        }

        protected virtual void dealOutRegionSites(List<ISite> outRegionSites, Dictionary<ICell, UltraSiteInfo> cellDisDic, ISite bts)
        {
            foreach (ISite other in outRegionSites)
            {
                double dis = 0;
                if (UltraSiteCondition.IsTooNearSite(bts, other, out dis))
                {
                    LTEBTS lteBts = bts as LTEBTS;
                    foreach (LTECell cell in lteBts.LatestCells)
                    {
                        addUltraNearSite(cellDisDic, other, dis, cell);
                    }
                }
            }
        }

        protected void addUltraNearSite(Dictionary<ICell, UltraSiteInfo> cellDisDic, ISite site, double dis, ICell cell)
        {
            UltraNearSiteInfo nSite = new UltraNearSiteInfo(cell, site, dis);
            nSite.SetValue(cellDisDic[cell]);
            List<UltraSiteInfo> ultraSet;
            if (!cellDic.TryGetValue(cell, out ultraSet))
            {
                ultraSet = new List<UltraSiteInfo>();
                cellDic.Add(cell, ultraSet);
            }
            ultraSet.Add(nSite);
        }

        private List<LTEBTS> getCurrentLTEBTSs()
        {
            List<LTEBTS> validBts = CellManager.GetInstance().GetCurrentLTEBTSs();
            if (MainModel.SearchGeometrys != null && MainModel.SearchGeometrys.IsSelectRegion())
            {
                DbRect selectRect = MainModel.SearchGeometrys.RegionBounds;
                DbRect validRect = new DbRect(selectRect.x1 - 0.1, selectRect.y1 - 0.1, selectRect.x2 + 0.1, selectRect.y2 + 0.1);

                for (int i = 0; i < validBts.Count; i++)
                {
                    ISite site = validBts[i];
                    if (!validRect.IsPointInThisRect(site.Longitude, site.Latitude))
                    {
                        validBts.RemoveAt(i);
                        i--;
                    }
                }
            }
            return validBts;
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.NR业务专题; }
        }
        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.其他; }
        }
        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.None; }
        }
        #endregion
    }

    public class UltraSiteInfo
    {
        public UltraSiteInfo(ICell cell)
        {
            Cell = cell;
            Site = cell.Site;
        }

        public override string ToString()
        {
            return TypeName;
        }

        public virtual string TypeName { get; set; }
        public ICell Cell { get; set; }
        public string CellName { get; set; }
        public ISite Site { get; set; }
        public string SiteName { get; set; }
        public int Arfcn { get; set; }
        public int Pci { get; set; }
        public int TAC { get; set; }
        public long NCI { get; set; }
        public string GridName { get; set; }
        public string AreaName { get; set; }
        public double DistanceByDir { get; set; }
        public double DistanceByDelaunay { get; set; }
        public string ProbInfo { get; set; }

        public double Longitude { get; set; }
        public double Latitude { get; set; }

        public string OtherSites { get; set; }
        public string OtherCells { get; set; }

        public virtual void MakeSummary()
        {
            CellName = Cell.Name;
            SiteName = Site.Name;
            Longitude = Cell.Longitude;
            Latitude = Cell.Latitude;

            SetOtherInfo();
        }

        public virtual void SetOtherInfo()
        {
            if (Cell is NRCell)
            {
                NRCell nrCell = Cell as NRCell;
                Arfcn = nrCell.SSBARFCN;
                Pci = nrCell.PCI;
                TAC = nrCell.TAC;
                NCI = nrCell.NCI;
            }
        }

        public double Distance { get; set; }
        public List<SiteDistance> DirSites { get; set; }
        public List<ISite> DelaunaySites { get; set; }
        public void SetDelaunayInfo(SiteDelaunayTri dSite)
        {
            this.DistanceByDelaunay = dSite.Distance;
            this.DelaunaySites = dSite.Others;
        }
    }

    public class UltraHighSiteInfo : UltraSiteInfo
    {
        public override string TypeName
        {
            get { return "超高站"; }
        }

        public UltraHighSiteInfo(ICell cell)
            : base(cell)
        {

        }

        public void SetValue(UltraSiteInfo baseInfo)
        {
            DistanceByDelaunay = baseInfo.DistanceByDelaunay;
            DistanceByDir = baseInfo.DistanceByDir;
            AreaName = baseInfo.AreaName;
            GridName = baseInfo.GridName;
        }

        public override void MakeSummary()
        {
            base.MakeSummary();

            ProbInfo = Cell.Altitude.ToString();
        }
    }

    public class UltraNearSiteInfo : UltraSiteInfo
    {
        public override string TypeName
        {
            get { return "超近站"; }
        }

        public ISite OtherSite
        {
            get;
            private set;
        }

        public UltraNearSiteInfo(ICell cell, ISite otherSite, double distance)
            : base(cell)
        {
            OtherSite = otherSite;
            Distance = Math.Round(distance, 2);
        }

        public void SetValue(UltraSiteInfo baseInfo)
        {
            DistanceByDelaunay = baseInfo.DistanceByDelaunay;
            DistanceByDir = baseInfo.DistanceByDir;
            AreaName = baseInfo.AreaName;
            GridName = baseInfo.GridName;
        }

        public override void MakeSummary()
        {
            base.MakeSummary();

            OtherSites = OtherSite.Name;
            StringBuilder strCellName = new StringBuilder();
            getCellNames(OtherSite, strCellName);
            OtherCells = strCellName.ToString().TrimEnd('，');

            ProbInfo = Distance.ToString();
        }

        protected virtual void getCellNames(ISite iSite, StringBuilder strCellName)
        {
            if (iSite is NRBTS)
            {
                NRBTS bts = iSite as NRBTS;
                foreach (NRCell cell in bts.LatestCells)
                {
                    strCellName.Append(cell.Name + '，');
                }
            }
        }
    }

    public class UltraFarSiteInfo : UltraSiteInfo
    {
        public override string TypeName
        {
            get { return "超远站"; }
        }

        public UltraFarSiteInfo(ICell cell)
            : base(cell)
        {
            
        }

        public void SetValue(UltraSiteInfo baseInfo)
        {
            DistanceByDelaunay = baseInfo.DistanceByDelaunay;
            DistanceByDir = baseInfo.DistanceByDir;
            AreaName = baseInfo.AreaName;
            GridName = baseInfo.GridName;
            Distance = baseInfo.Distance;
            DirSites = baseInfo.DirSites;
            DelaunaySites = baseInfo.DelaunaySites;
        }

        public override void MakeSummary()
        {
            base.MakeSummary();

            StringBuilder strSiteName = new StringBuilder();
            StringBuilder strCellName = new StringBuilder();
            foreach (SiteDistance site in DirSites)
            {
                strSiteName.Append(site.Site.Name + "，");
                getCellNames(site.Site, strCellName);
            }
            OtherSites = strSiteName.ToString().TrimEnd('，');
            OtherCells = strCellName.ToString().TrimEnd('，');

            ProbInfo = Distance.ToString();
        }

        protected virtual void getCellNames(ISite iSite, StringBuilder strCellName)
        {
            if (iSite is NRBTS)
            {
                NRBTS bts = iSite as NRBTS;
                foreach (NRCell cell in bts.LatestCells)
                {
                    strCellName.Append(cell.Name + '，');
                }
            }
        }
    }
}
