﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using DevExpress.XtraEditors;
using System.Data.OleDb;
using System.Reflection;
using Microsoft.Office.Interop.Excel;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NonconformityConditionDlg : BaseDialog
    {
        public NonconformityConditionDlg()
        {
            InitializeComponent();
        }

        public List<ZTDIYNonconformity.DefineCell> DefineCellList { get; set; }
        private void chkCellDegree_CheckedChanged(object sender, EventArgs e)
        {
            spinEditCellRank.Enabled = chkCellDegree.Checked;
        }

        private void chkPccpch_Rscp_CheckedChanged(object sender, EventArgs e)
        {
            spinEditPccpchRscpThreshold.Enabled = chkPccpch_Rscp.Checked;
        }

        public void getCondition(out int cellRank, out double pccpchRscpThreshold, out double directionGapDegreeThreshold, out double distanceRangeThreshold,out int tpNum,out bool isDefineCell)
        {
            cellRank = (int)spinEditCellRank.Value;
            pccpchRscpThreshold = (double)spinEditPccpchRscpThreshold.Value;
            directionGapDegreeThreshold = (double)spinEditDirectionGapDegreeThreshold.Value;
            distanceRangeThreshold = (double)spinEditDistanceRangeThreshold.Value;
            tpNum = (int)spinEditTpNum.Value;
            isDefineCell = checkEdit1.Checked;
        }

        /// <summary>
        /// 是否勾选小区级别控制
        /// </summary>
        public bool EnableCellRank
        {
            get { return chkCellDegree.Checked; }
        }

        /// <summary>
        /// 是否勾选场强控制
        /// </summary>
        public bool EnablePccpch_Rscp
        {
            get { return chkPccpch_Rscp.Checked; }
        }

        private void spinEditDirectionGapDegreeThreshold_EditValueChanged(object sender, EventArgs e)
        {
            if ((double)spinEditDirectionGapDegreeThreshold.Value<0||(double)spinEditDirectionGapDegreeThreshold.Value>100)
            {
                XtraMessageBox.Show("数值无效，请输入0到100之间的数字！");
                spinEditDirectionGapDegreeThreshold.Value = 50;
            }
        }

        private void checkEdit1_CheckedChanged(object sender, EventArgs e)
        {
            btnImport.Enabled = checkEdit1.Checked;
        }

        List<string> columnList = null;
        CellParamColumn cellParamColumn = null;
        CellImportForm cellImportForm = null;
        private void btnImport_Click(object sender, EventArgs e)
        {
            bool isImportOK = false;
            ImportExcel(out isImportOK);
            if (isImportOK)
            {
                cellImportForm = CellImportForm.GetDlg();
                cellImportForm.fillComboBoxs(columnList);
                if (cellImportForm.ShowDialog() == DialogResult.OK)
                {
                    WaitBox.Show("正在导入工参...", import);
                }
            }
            else
            {
                XtraMessageBox.Show("读取EXCEL失败");
            }

        }

        private void import()
        {
            try
            {
                cellParamColumn = cellImportForm.cellParamColumn;
                DefineCellList = new List<ZTDIYNonconformity.DefineCell>();

                DefineCellList = GetDefineCells();
                WaitBox.ProgressPercent = 30;
                if (DefineCellList != null)
                    XtraMessageBox.Show("导入完成");
                else
                    XtraMessageBox.Show("导入中断");
            }
            finally
            {
                WaitBox.Close();
            }
        }

        string filename = "";
        private void ImportExcel(out bool isImportOK)
        {
            try
            {
                columnList = new List<string>();

                OpenFileDialog dlg = new OpenFileDialog();
                dlg.Filter = "Excel2003文件(*.xls)|*.xls|Excel2007，2010文件(*.xlsx)|*.xlsx";
                dlg.RestoreDirectory = true;
                if (dlg.ShowDialog(this) == DialogResult.OK)
                {
                    filename = dlg.FileName;

                    Microsoft.Office.Interop.Excel.Application _excelApp = new Microsoft.Office.Interop.Excel.Application();
                    Workbook objWB = _excelApp.Workbooks.Open(filename, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing);
                    Sheets sheets = objWB.Worksheets;
                    _Worksheet worksheet = (_Worksheet)sheets.get_Item(1);
                    int colCount = worksheet.UsedRange.Columns.Count;
                    for (int i = 0; i < colCount; i++)
                    {
                        Range range = worksheet.Cells[1, i + 1] as Range;
                        columnList.Add(range.Text.ToString());
                    }
                    objWB.Close(Type.Missing, Type.Missing, Type.Missing);
                    _excelApp.Quit();
                    ExcelHelper.KillCurrentExcel(_excelApp);

                    isImportOK = true;
                }
                else
                    isImportOK = false;
            }
            catch(Exception ex)
            {
                XtraMessageBox.Show(ex.ToString());
                isImportOK = false;
            }
        }


        /// <summary>
        /// 读取Excel表，生成小区工参
        /// </summary>
        private List<ZTDIYNonconformity.DefineCell> GetDefineCells()
        {
            List<ZTDIYNonconformity.DefineCell> defineCellList = new List<ZTDIYNonconformity.DefineCell>();
            string strConn = "";
            int index = filename.LastIndexOf(".");
            int lenFilename = filename.Length;
            if (filename.Substring(index + 1, lenFilename - index - 1) == "xls") //Excel2003版本引擎
            {
                strConn = "Provider=Microsoft.Jet.OLEDB.4.0;" + "Data Source=" + filename + ";" + "Extended Properties=Excel 8.0;";
            }
            else //Excel2007版本引擎
            {
                strConn = "Provider=Microsoft.ACE.OLEDB.12.0;" + "Data Source=" + filename + ";" + "Extended Properties=Excel 8.0;";
            }
            OleDbConnection con = new OleDbConnection(strConn);
            try
            {
                string importWorkSheetName = GetFirstSheetNameFromExcelFileName(filename);
                string cmdText = "select * from [" + importWorkSheetName.Replace('.', '#') + "$]";
                OleDbCommand command = new OleDbCommand(cmdText, con);
                con.Open();
                OleDbDataReader reader = command.ExecuteReader();

                ZTDIYNonconformity.DefineCell dcell = null;
                if (reader.HasRows)
                {
                    while (reader.Read())
                    {
                        WaitBox.ProgressPercent = 70;
                        dcell = new ZTDIYNonconformity.DefineCell();

                        dcell.Cellname = Convert.ToString(reader[cellParamColumn.col_cellname]).Trim();

                        if (reader[cellParamColumn.col_freq].ToString() == "")
                        {
                            XtraMessageBox.Show(dcell.Cellname + "等小区的主频为空，数据无效！");
                            defineCellList = null;
                            break;
                        }
                        else
                        {
                            short freq;
                            if (short.TryParse(reader[cellParamColumn.col_freq].ToString(), out freq))
                                dcell.Freq = freq;
                            else
                            {
                                defineCellList = null;
                                XtraMessageBox.Show(dcell.Cellname + "等小区的主频数据类型错误！");
                                break;
                            }
                        }


                        if (reader[cellParamColumn.col_cpi].ToString() == "")
                        {
                            XtraMessageBox.Show(dcell.Cellname + "等小区的扰码为空，数据无效！");
                            defineCellList = null;
                            break;
                        }
                        else
                        {
                            byte cpi;
                            if (byte.TryParse(reader[cellParamColumn.col_cpi].ToString(), out cpi))
                                dcell.Cpi = cpi;
                            else
                            {
                                defineCellList = null;
                                XtraMessageBox.Show(dcell.Cellname + "等小区的扰码数据类型错误！");
                                break;
                            }
                        }

                        if (reader[cellParamColumn.col_Lac].ToString() != "")
                        {
                            int lac;
                            if (Int32.TryParse(reader[cellParamColumn.col_Lac].ToString(), out lac))
                                dcell.Lac = lac;
                            else
                            {
                                XtraMessageBox.Show(dcell.Cellname + "等小区的LAC数据类型错误！");
                                defineCellList = null;
                                break;
                            }
                        }
                        else
                            dcell.Lac = -255;


                        if (reader[cellParamColumn.col_ci].ToString() != "")
                        {
                            int ci;
                            if (Int32.TryParse(reader[cellParamColumn.col_ci].ToString(), out ci))
                                dcell.Ci = ci;
                            else
                            {
                                XtraMessageBox.Show(dcell.Cellname + "等小区的CI数据类型错误！");
                                defineCellList = null;
                                break;
                            }
                        }
                        else
                            dcell.Ci = -255;

                        if (reader[cellParamColumn.col_Longitude].ToString() != "")
                        {
                            double longitude;
                            if (double.TryParse(reader[cellParamColumn.col_Longitude].ToString(), out longitude))
                                dcell.Longitude = longitude;
                            else
                            {
                                XtraMessageBox.Show(dcell.Cellname + "等小区的经度数据类型错误！");
                                defineCellList = null;
                                break;
                            }
                        }
                        else
                            dcell.Longitude = 0;

                        if (reader[cellParamColumn.col_latitude].ToString() != "")
                        {
                            double latitude;
                            if (double.TryParse(reader[cellParamColumn.col_latitude].ToString(), out latitude))
                                dcell.Latitude = latitude;
                            else
                            {
                                XtraMessageBox.Show(dcell.Cellname + "等小区的纬度数据类型错误！");
                                defineCellList = null;
                                break;
                            }
                        }
                        else
                            dcell.Latitude = 0;

                        if (reader[cellParamColumn.col_direction].ToString() == "")
                        {
                            dcell.Direction = -1;
                        }
                        else
                        {
                            int direction;
                            if (Int32.TryParse(reader[cellParamColumn.col_direction].ToString(), out direction))
                                dcell.Direction = direction;
                            else
                            {
                                XtraMessageBox.Show(dcell.Cellname + "等小区的方向角数据类型错误！");
                                defineCellList = null;
                                break;
                            }
                        }

                        if (reader[cellParamColumn.col_indoor].ToString() == null
                            || reader[cellParamColumn.col_indoor].ToString() == "")
                        {
                            XtraMessageBox.Show(dcell.Cellname + "等小区的是否室分字段为空！");
                            defineCellList = null;
                            break;
                        }
                        else
                        {
                            dcell.Indoor = reader[cellParamColumn.col_indoor].ToString();
                        }

                        defineCellList.Add(dcell);
                    }
                    reader.Close();
                    con.Close();
                    WaitBox.ProgressPercent = 99;
                    return defineCellList;
                }
                defineCellList = null;
                return defineCellList;
            }
            catch(Exception ex)
            {
                XtraMessageBox.Show(ex.ToString());
                con.Close();
                defineCellList = null;
                return defineCellList;
            }
        }

        /// <summary>
        /// 获取Excel中第一个Sheet名称
        /// </summary>
        /// <param name="filepath"></param>
        /// <param name="numberSheetID"></param>
        /// <returns></returns>
        /// <example>
        /// string sheetName = GetFirstSheetNameFromExcelFileName(strFileUpLoadPath;
        /// </example>
        public static string GetFirstSheetNameFromExcelFileName(string filepath)
        {
            try
            {
                string strFirstSheetName = null;
                Microsoft.Office.Interop.Excel.Application _excelApp = new Microsoft.Office.Interop.Excel.Application();
                Microsoft.Office.Interop.Excel.Workbook objWB = _excelApp.Workbooks.Open(filepath, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing);

                strFirstSheetName = ((Microsoft.Office.Interop.Excel.Worksheet)objWB.Worksheets[1]).Name;

                objWB.Close(Type.Missing, Type.Missing, Type.Missing);
                _excelApp.Quit();
                return strFirstSheetName;
            }
            catch (Exception Err)
            {
                return Err.Message;
            }
        }
    }
}
