﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class GSMComparePoorRxQualityRoadSettingDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.label7 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.numMaxDistance = new System.Windows.Forms.NumericUpDown();
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.numDistance = new System.Windows.Forms.NumericUpDown();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.endTime1 = new System.Windows.Forms.DateTimePicker();
            this.endTime2 = new System.Windows.Forms.DateTimePicker();
            this.beginTime2 = new System.Windows.Forms.DateTimePicker();
            this.beginTime1 = new System.Windows.Forms.DateTimePicker();
            this.label10 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.条件门限设置 = new DevExpress.XtraEditors.GroupControl();
            this.numRxQualMax = new System.Windows.Forms.NumericUpDown();
            this.label16 = new System.Windows.Forms.Label();
            this.label15 = new System.Windows.Forms.Label();
            this.numRoadRadius = new System.Windows.Forms.NumericUpDown();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.条件门限设置)).BeginInit();
            this.条件门限设置.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRxQualMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRoadRadius)).BeginInit();
            this.SuspendLayout();
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(312, 94);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(17, 12);
            this.label7.TabIndex = 39;
            this.label7.Text = "米";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(117, 96);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(101, 12);
            this.label6.TabIndex = 38;
            this.label6.Text = "相邻采样点距离≤";
            // 
            // numMaxDistance
            // 
            this.numMaxDistance.Location = new System.Drawing.Point(224, 92);
            this.numMaxDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numMaxDistance.Name = "numMaxDistance";
            this.numMaxDistance.Size = new System.Drawing.Size(80, 21);
            this.numMaxDistance.TabIndex = 37;
            this.numMaxDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMaxDistance.Value = new decimal(new int[] {
            20,
            0,
            0,
            0});
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(311, 64);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(17, 12);
            this.label4.TabIndex = 32;
            this.label4.Text = "米";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(328, 40);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(0, 12);
            this.label3.TabIndex = 31;
            // 
            // numDistance
            // 
            this.numDistance.Location = new System.Drawing.Point(224, 60);
            this.numDistance.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numDistance.Name = "numDistance";
            this.numDistance.Size = new System.Drawing.Size(80, 21);
            this.numDistance.TabIndex = 30;
            this.numDistance.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numDistance.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(153, 65);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 28;
            this.label2.Text = "持续距离≥";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(147, 36);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(71, 12);
            this.label1.TabIndex = 27;
            this.label1.Text = "RxQuality≥";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.endTime1);
            this.groupControl1.Controls.Add(this.endTime2);
            this.groupControl1.Controls.Add(this.beginTime2);
            this.groupControl1.Controls.Add(this.beginTime1);
            this.groupControl1.Controls.Add(this.label10);
            this.groupControl1.Controls.Add(this.label14);
            this.groupControl1.Controls.Add(this.label13);
            this.groupControl1.Controls.Add(this.label9);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(472, 93);
            this.groupControl1.TabIndex = 42;
            this.groupControl1.Text = "对比时间段设置";
            // 
            // endTime1
            // 
            this.endTime1.Location = new System.Drawing.Point(293, 33);
            this.endTime1.Name = "endTime1";
            this.endTime1.Size = new System.Drawing.Size(153, 21);
            this.endTime1.TabIndex = 28;
            // 
            // endTime2
            // 
            this.endTime2.Location = new System.Drawing.Point(293, 60);
            this.endTime2.Name = "endTime2";
            this.endTime2.Size = new System.Drawing.Size(153, 21);
            this.endTime2.TabIndex = 28;
            // 
            // beginTime2
            // 
            this.beginTime2.Location = new System.Drawing.Point(106, 60);
            this.beginTime2.Name = "beginTime2";
            this.beginTime2.Size = new System.Drawing.Size(152, 21);
            this.beginTime2.TabIndex = 28;
            // 
            // beginTime1
            // 
            this.beginTime1.Location = new System.Drawing.Point(105, 33);
            this.beginTime1.Name = "beginTime1";
            this.beginTime1.Size = new System.Drawing.Size(153, 21);
            this.beginTime1.TabIndex = 28;
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(22, 64);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(71, 12);
            this.label10.TabIndex = 27;
            this.label10.Text = "时间段2：从";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(268, 64);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(17, 12);
            this.label14.TabIndex = 27;
            this.label14.Text = "到";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(268, 37);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(17, 12);
            this.label13.TabIndex = 27;
            this.label13.Text = "到";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(22, 37);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(71, 12);
            this.label9.TabIndex = 27;
            this.label9.Text = "时间段1：从";
            // 
            // 条件门限设置
            // 
            this.条件门限设置.Controls.Add(this.label1);
            this.条件门限设置.Controls.Add(this.label2);
            this.条件门限设置.Controls.Add(this.numRxQualMax);
            this.条件门限设置.Controls.Add(this.numDistance);
            this.条件门限设置.Controls.Add(this.label16);
            this.条件门限设置.Controls.Add(this.label7);
            this.条件门限设置.Controls.Add(this.label3);
            this.条件门限设置.Controls.Add(this.label15);
            this.条件门限设置.Controls.Add(this.label6);
            this.条件门限设置.Controls.Add(this.label4);
            this.条件门限设置.Controls.Add(this.numRoadRadius);
            this.条件门限设置.Controls.Add(this.numMaxDistance);
            this.条件门限设置.Dock = System.Windows.Forms.DockStyle.Top;
            this.条件门限设置.Location = new System.Drawing.Point(0, 93);
            this.条件门限设置.Name = "条件门限设置";
            this.条件门限设置.Size = new System.Drawing.Size(472, 162);
            this.条件门限设置.TabIndex = 43;
            this.条件门限设置.Text = "条件门限设置";
            // 
            // numRxQualMax
            // 
            this.numRxQualMax.Location = new System.Drawing.Point(224, 31);
            this.numRxQualMax.Maximum = new decimal(new int[] {
            7,
            0,
            0,
            0});
            this.numRxQualMax.Name = "numRxQualMax";
            this.numRxQualMax.Size = new System.Drawing.Size(80, 21);
            this.numRxQualMax.TabIndex = 30;
            this.numRxQualMax.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRxQualMax.Value = new decimal(new int[] {
            5,
            0,
            0,
            0});
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(313, 126);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(17, 12);
            this.label16.TabIndex = 39;
            this.label16.Text = "米";
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(63, 126);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(155, 12);
            this.label15.TabIndex = 38;
            this.label15.Text = "时间段1测试路线栅格化大小";
            // 
            // numRoadRadius
            // 
            this.numRoadRadius.Location = new System.Drawing.Point(224, 124);
            this.numRoadRadius.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numRoadRadius.Name = "numRoadRadius";
            this.numRoadRadius.Size = new System.Drawing.Size(80, 21);
            this.numRoadRadius.TabIndex = 37;
            this.numRoadRadius.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRoadRadius.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(281, 276);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 45;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(371, 276);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 45;
            this.btnCancel.Text = "取消";
            // 
            // GSMComparePoorRxQualityRoadSettingDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(472, 315);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.条件门限设置);
            this.Controls.Add(this.groupControl1);
            this.Name = "GSMComparePoorRxQualityRoadSettingDlg";
            this.Text = "条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numMaxDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.条件门限设置)).EndInit();
            this.条件门限设置.ResumeLayout(false);
            this.条件门限设置.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRxQualMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRoadRadius)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.NumericUpDown numMaxDistance;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.NumericUpDown numDistance;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private System.Windows.Forms.DateTimePicker endTime1;
        private System.Windows.Forms.DateTimePicker endTime2;
        private System.Windows.Forms.DateTimePicker beginTime2;
        private System.Windows.Forms.DateTimePicker beginTime1;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Label label9;
        private DevExpress.XtraEditors.GroupControl 条件门限设置;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.NumericUpDown numRoadRadius;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private System.Windows.Forms.NumericUpDown numRxQualMax;
    }
}