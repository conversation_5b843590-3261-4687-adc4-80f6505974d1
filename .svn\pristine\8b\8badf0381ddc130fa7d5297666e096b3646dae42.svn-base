﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public enum WeakMosReasonCallType
    {
        Unknown,
        NR,
        LTE,
        GSM
    }

    public class WeakMosReasonManagerBase
    {
        public virtual void GetWeakMosReasonInfo(WeakMosReasonInfoBase callInfo, WeakMosReasonAnaCondBase cond)
        {
            judgeWeakReasonByEvt(callInfo, cond);
            judgeWeakReasonByMsg(callInfo, cond);
            judgeWeakReasonByTp(callInfo, cond);

            if (callInfo.ReasonList.Count <= 0)
            {
                callInfo.ReasonList.Add("其他");
            }
        }

        protected virtual void judgeWeakReasonByEvt(WeakMosReasonInfoBase callInfo, WeakMosReasonAnaCondBase cond)
        {
            if (isCsfb(callInfo))
            {
                callInfo.ReasonList.Add("回落(CSFB)");
            }
            if (isEsrvcc(callInfo))
            {
                callInfo.ReasonList.Add("Esrvcc");
            }
            if (isHandoverTooMuch(callInfo, cond))
            {
                callInfo.ReasonList.Add("频繁切换");
            }
        }

        protected virtual void judgeWeakReasonByMsg(WeakMosReasonInfoBase callInfo, WeakMosReasonAnaCondBase cond)
        {
            string rrcReestablishReason;
            if (isRrcReestablish(callInfo, out rrcReestablishReason))
            {
                callInfo.ReasonList.Add("RRC重建:" + rrcReestablishReason);
            }
        }

        protected virtual WeakMosReasonCallType judgeCallType(int? callBeginEvtID)
        {
            return WeakMosReasonCallType.Unknown;
        }

    protected virtual void judgeWeakReasonByTp(WeakMosReasonInfoBase callInfo, WeakMosReasonAnaCondBase cond)
        {
            bool isWeakRsrp = false;
            bool isWeakSinr = false;
            bool isLostNum = false;
            int weakRsrpCount = 0;
            int weakSinrCount = 0;
            int maxLostNum = -1;
            int minLostNum = -1;
            int LostNumCount = 0;
            WeakMosReasonCallType callType = judgeCallType(callInfo.CallBeginEvt?.ID);
            foreach (TestPoint tp in callInfo.MosTestPoints)
            {
                if (!isWeakRsrp)
                {
                    isWeakRsrp = isWeakRsrpReason(tp, cond, ref weakRsrpCount, callType);
                }

                if (!isWeakSinr)
                {
                    isWeakSinr = isWeakSinrReason(tp, cond, ref weakSinrCount, callType);
                }

                if (!isLostNum)
                {
                    isLostNum = isLostRtpPacketsReason(tp, cond, ref maxLostNum, ref minLostNum, ref LostNumCount);
                }
            }
            if (maxLostNum != -1 && maxLostNum == minLostNum && LostNumCount == 1 && maxLostNum > cond.RtpLostNumGate)
            {
                //时段内采样点只有一个点有丢包数,暂且算为丢包(还未触发过此种情况)
                isLostNum = true;
            }
            if (maxLostNum == minLostNum && LostNumCount != 1)
            {
                //时段内最大丢包和最小丢包相同时,丢包数没有变化暂时不算丢包
            }

            if (isWeakRsrp)
            {
                callInfo.ReasonList.Add("弱覆盖");
            }
            if (isWeakSinr)
            {
                callInfo.ReasonList.Add("质差");
            }
            if (isLostNum)
            {
                callInfo.ReasonList.Add("丢包");
            }
        }

        protected virtual bool isCsfb(WeakMosReasonInfoBase callInfo)
        {
            if (callInfo.CallBeginEvt != null && judgeCsfb(callInfo.CallBeginEvt.ID))
            {
                return true;
            }
            return false;
        }
        protected virtual bool isEsrvcc(WeakMosReasonInfoBase callInfo)
        {
            return callInfo.EsrvccEvt != null;
        }
        protected virtual bool isRrcReestablish(WeakMosReasonInfoBase callInfo, out string causeReason)
        {
            causeReason = "";
            if (callInfo.RRCReestablishMsg != null)
            {
                uint cause = MessageDecodeHelper.GetMsgSingleUInt(callInfo.RRCReestablishMsg, "lte-rrc.reestablishmentCause");
                switch (cause)
                {
                    case 0:
                        causeReason = "reconfigurationtionFailure";
                        break;
                    case 1:
                        causeReason = "handoverFailure";
                        break;
                    case 2:
                        causeReason = "otherFailure";
                        break;
                    default:
                        causeReason = "未解析原因值";
                        break;
                }
                return true;
            }
            return false;
        }
        protected virtual bool isHandoverTooMuch(WeakMosReasonInfoBase callInfo, WeakMosReasonAnaCondBase cond)
        {
            return callInfo.HandOverEventsCount > cond.HandoverCountGate;
        }

        protected bool judgeWeakData(float? data, float min, float max, int threshold, ref int weakCount)
        {
            if (data != null)
            {
                if (data >= min && data <= max)
                {
                    weakCount++;
                    if (weakCount >= threshold)
                    {
                        return true;
                    }
                }
                else
                {
                    weakCount = 0;
                }
            }
            return false;
        }

        protected virtual bool isWeakRsrpReason(TestPoint tp, WeakMosReasonAnaCondBase cond, ref int weakRsrpCount, WeakMosReasonCallType callType)
        {
            float? rsrp = getRsrp(tp);
            return judgeWeakData(rsrp, -141, cond.WeakRsrpGate, cond.WeakRsrpTpCount, ref weakRsrpCount);
        }

        protected virtual bool isWeakSinrReason(TestPoint tp, WeakMosReasonAnaCondBase cond, ref int weakSinrCount, WeakMosReasonCallType callType)
        {
            float? sinr = getSinr(tp);
            return judgeWeakData(sinr, -50, cond.WeakSinrGate, cond.WeakSinrTpCount, ref weakSinrCount);
        }

        /// <summary>
        /// 判断是否丢包 Mos前2秒到前10秒的所有采样点中,最大丢包减去最小丢包的值大于设定的丢包数则为丢包
        /// </summary>
        /// <param name="tp">Mos前2秒到前10秒的采样点</param>
        /// <param name="cond">设定条件</param>
        /// <param name="maxLostNum">最大丢包数</param>
        /// <param name="minLostNum">最小丢包数</param>
        /// <returns>是否丢包 true:丢包 false:不丢包</returns>
        protected virtual bool isLostRtpPacketsReason(TestPoint tp, WeakMosReasonAnaCondBase cond
            , ref int maxLostNum, ref int minLostNum, ref int LostNumCount)
        {
            int? tpLostNum = getRtpPacketsLostNum(tp);
            if (tpLostNum == null)
            {
                return false;
            }
            LostNumCount++;
            if (tpLostNum > maxLostNum)
            {
                maxLostNum = (int)tpLostNum;
            }
            if (tpLostNum < minLostNum || minLostNum == -1)
            {
                minLostNum = (int)tpLostNum;
            }
            if (maxLostNum != minLostNum && (maxLostNum - minLostNum) > cond.RtpLostNumGate)
            {
                return true;
            }
            return false;
        }


        #region 需重写
        protected virtual float? getRsrp(TestPoint tp)
        {
            throw (new NotImplementedException());
        }

        protected virtual float? getSinr(TestPoint tp)
        {
            throw (new NotImplementedException());
        }

        protected virtual int? getRtpPacketsLostNum(TestPoint tp)
        {
            throw (new NotImplementedException());
        }

        protected virtual bool judgeCsfb(int evtID)
        {
            throw (new NotImplementedException());
        }
        #endregion
    }
}
