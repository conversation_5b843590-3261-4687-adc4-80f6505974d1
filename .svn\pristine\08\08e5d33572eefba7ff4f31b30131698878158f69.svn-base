﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class VoiLayerStyleSetForm : BaseDialog
    {
        public VoiLayerStyleSetForm()
        {
            InitializeComponent();

            this.Load += Form_Load;
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            VoronoiLayer.GetInstance().LineWidth = (short)spinEdit.Value;
            VoronoiLayer.GetInstance().LineColor = colorEdit.Color;
            DialogResult = DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void Form_Load(object sender, EventArgs e)
        {
            spinEdit.Value = VoronoiLayer.GetInstance().LineWidth;
            colorEdit.Color = VoronoiLayer.GetInstance().LineColor;
        }
    }
}
