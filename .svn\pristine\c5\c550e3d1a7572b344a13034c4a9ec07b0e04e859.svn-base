﻿using DevExpress.XtraTreeList.Data;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class WeakMosReasonInfoBase
    {
        public WeakMosReasonInfoBase(string fileName, Event callBeginEvt)
        {
            FileName = fileName;
            CallBeginEvt = callBeginEvt;
        }

        public int SN { get; set; }

        public List<string> ReasonList { get; set; } = new List<string>();
        public string ReasonsDes { get; protected set; }

        public string GridName { get; set; }
        public string RoadName { get; set; }
        public string FileName { get; set; }

        public float? MosValue { get; protected set; }

        public TestPoint MosTestPoint { get; protected set; }
        public string MosTime { get; protected set; }
        public string MosLongitude { get; protected set; }
        public string MosLatitude { get; protected set; }

        public Event CallBeginEvt { get; set; }
        public string CallBeginEvtName { get; protected set; }

        public Event EsrvccEvt { get; set; }

        public List<TestPoint> TestPoints { get; set; } = new List<TestPoint>();

        public virtual void Calculate()
        {
            dealReasonsDes();

            MosTime = MosTestPoint?.DateTimeStringWithMillisecond ?? "";
            MosLongitude = MosTestPoint?.Longitude.ToString() ?? "";
            MosLatitude = MosTestPoint?.Latitude.ToString() ?? "";
            CallBeginEvtName = CallBeginEvt?.Name ?? "";
            if (CallBeginEvtName == "")
            {
                CallBeginEvtName = CallBeginEvt?.ID.ToString() ?? "";
            }
        }

        protected void dealReasonsDes()
        {
            StringBuilder strb = new StringBuilder();
            ReasonList.ForEach(str => strb.Append(str + ";"));
            if (strb.Length > 0)
            {
                strb.Remove(strb.Length - 1, 1);
            }
            ReasonsDes = strb.ToString();
        }

        #region Mos时段内信息
        /// <summary>
        /// //一个MOS区间(设x为MOS值时间点，采样点分析的时间段为【x-10,x-2】)
        /// </summary>
        public TimePeriod MosTimePeriod { get; protected set; } = new TimePeriod();
        /// <summary>
        /// 一个MOS区间的采样点信息
        /// </summary>
        public List<TestPoint> MosTestPoints { get; set; } = new List<TestPoint>();
        public Message RRCReestablishMsg { get; set; }
        public List<Event> HandOverEvents { get; set; } = new List<Event>();
        public int HandOverEventsCount { get; protected set; }
        //public float? RsrpAvg_MosPeriod { get; set; }
        //public float? SinrAvg_MosPeriod { get; set; }

        public DataInfo RsrpInfo { get; set; } = new DataInfo();
        public DataInfo SinrInfo { get; set; } = new DataInfo();

        public virtual void SetMosValue(TestPoint mosTestPoint, float mosValue)
        {
            MosTestPoint = mosTestPoint;
            MosValue = (float)Math.Round(mosValue, 2);
            MosTimePeriod = new TimePeriod(mosTestPoint.DateTime.AddSeconds(-10), mosTestPoint.DateTime.AddSeconds(-2));
            GridName = GISManager.GetInstance().GetGridDesc(mosTestPoint.Longitude, mosTestPoint.Latitude);
            RoadName = GISManager.GetInstance().GetRoadPlaceDesc(mosTestPoint.Longitude, mosTestPoint.Latitude);
            getValidInfo();
        }

        protected virtual void getValidInfo()//获取到mos信息后核查事件、信令是否在有效时间内
        {
            int tpIdx1 = 0;
            MosTestPoints = MOSAnaManager.getTestPoinsByPeriod(MosTimePeriod, TestPoints, ref tpIdx1);

            RemoveInvalidInfo(MosTimePeriod);

            GetTPInfo();
        }


        /// <summary>
        /// 去除不在时间段内的数据
        /// </summary>
        /// <param name="callInfo"></param>
        /// <param name="timePeriod"></param>
        public void RemoveInvalidInfo(TimePeriod timePeriod)
        {
            //呼叫起始事件时间点<= esrvcc事件时间点<= Mos时间段
            if (CallBeginEvt != null && CallBeginEvt.DateTime > timePeriod.BeginTime)
            {
                CallBeginEvt = null;
            }

            if (EsrvccEvt != null)
            {
                if (CallBeginEvt != null && CallBeginEvt.DateTime > EsrvccEvt.DateTime)
                {
                    EsrvccEvt = null;
                }
                if (EsrvccEvt.DateTime > timePeriod.BeginTime)
                {
                    EsrvccEvt = null;
                }
            }

            if (RRCReestablishMsg != null && !timePeriod.Contains(RRCReestablishMsg.DateTime))
            {
                RRCReestablishMsg = null;
            }
          
            for (int i = 0; i < HandOverEvents.Count; i++)
            {
                Event evt = HandOverEvents[i];
                if (!timePeriod.Contains(evt.DateTime))
                {
                    HandOverEvents.Remove(evt);
                    i--;
                }
            }

            HandOverEventsCount = HandOverEvents.Count;
        }

        public virtual void GetTPInfo()
        {
            foreach (TestPoint tp in MosTestPoints)
            {
                float? rsrp = getRsrp(tp);
                RsrpInfo.Add(rsrp);

                float? sinr = getSinr(tp);
                SinrInfo.Add(sinr);
            }
            RsrpInfo.GetAvg();
            SinrInfo.GetAvg();
            //RsrpAvg_MosPeriod = rsrpInfo.GetAvg();
            //SinrAvg_MosPeriod = sinrInfo.GetAvg();
        }

        protected virtual float? getRsrp(TestPoint tp)
        {
            throw (new NotImplementedException());
        }

        protected virtual float? getSinr(TestPoint tp)
        {
            throw (new NotImplementedException());
        }

        public class DataInfo
        {
            public int Count { get; set; }
            public float Sum { get; set; }

            public float? Min { get; set; } = null;
            public float? Max { get; set; } = null;
            public float? Avg { get; set; } = null;

            public void Add(float? data)
            {
                if (data != null)
                {
                    float value = (float)data;
                    Count++;
                    Sum += value;

                    if (Min == null || Max == null)
                    {
                        Min = data;
                        Max = data;
                    }
                    else if (data < Min)
                    {
                        Min = value;
                    }
                    else if(data > Max)
                    {
                        Max = value;
                    }
                }
            }

            public void GetAvg()
            {
                if (Count > 0)
                {
                    Avg=(float?)Math.Round(Sum / Count, 2);
                }
            }
        }
        #endregion
    }


    public class WeakMosSumReasonInfo
    {
        public WeakMosSumReasonInfo(string reasonName)
        {
            this.Reason = reasonName;
        }

        public string Reason { get; set; }
        public DataInfo WeakMosInfo { get; set; } = new DataInfo();
        public DataInfo ReasonMosInfo { get; set; } = new DataInfo();

        public class DataInfo
        {
            public int Count { get; set; }
            public int TotalCount { get; set; }
            public double Avg { get; set; }

            public void Calculate()
            {
                if (TotalCount != 0)
                {
                    Avg = Math.Round((double)100 * Count / TotalCount, 2);
                }
                else
                {
                    Avg = 0;
                }
            }
        }

        //public int MosTpCount { get; set; }
        //public int WeakMosTpCount { get; set; }
        //public double WeakMosTpPer
        //{
        //    get
        //    {
        //        if (MosTpCount != 0)
        //        {
        //            return Math.Round((double)100 * WeakMosTpCount / MosTpCount, 2);
        //        }
        //        return 0;
        //    }
        //}

        //
        //public int ReasonMosTpCount { get; set; }
        //public double ReasonMosTpRate
        //{
        //    get
        //    {
        //        if (WeakMosTpCount != 0)
        //        {
        //            return Math.Round((double)100 * ReasonMosTpCount / WeakMosTpCount, 2);
        //        }
        //        return 0;
        //    }
        //}
    }
}
