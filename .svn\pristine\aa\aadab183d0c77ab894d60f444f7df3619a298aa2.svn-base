﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.TestDepth
{
    public class TestDepthDetail
    {
        private readonly List<TestDepthTpGrid> historyGrids = null;
        public List<TestDepthTpGrid> HistoryGrids
        {
            get { return historyGrids; }
        }
        public int HistoryGridCount
        {
            get
            {
                return historyGrids.Count;
            }
        }
        private readonly List<TestDepthTpGrid> estimateGrids = null;
        public List<TestDepthTpGrid> EstimateGrids
        {
            get { return estimateGrids; }
        }
        public int EstimateGridCount
        {
            get { return estimateGrids.Count; }
        }
        private int newGridCount = -1;
        public int NewGridCount
        {
            get { return newGridCount; }
        }
        private readonly string regionName = null;
        public string RegionName
        {
            get { return regionName; }
        }
        public TestDepthDetail(string regionName, List<TestDepthTpGrid> historyGrids, List<TestDepthTpGrid> estimateGrids)
        {
            this.regionName = regionName;
            this.historyGrids = historyGrids;
            this.estimateGrids = estimateGrids;
        }
        double testDepth = -1;
        public double TestDepth
        {
            get { return testDepth; }
        }

        private readonly List<TestDepthTpGrid> newGrids = new List<TestDepthTpGrid>();
        public void MakeSummary()
        {
            newGridCount = 0;
            foreach (TestDepthTpGrid testGrid in estimateGrids)
            {
                TestDepthTpGrid grid = getExistGrid(testGrid);
                if (grid == null)
                {
                    newGrids.Add(testGrid);
                    newGridCount++;
                }
            }
            testDepth = Math.Round(EstimateGridCount * 1.0 / (HistoryGridCount + newGridCount), 2);
        }

        private TestDepthTpGrid getExistGrid(TestDepthTpGrid testGrid)
        {
            return historyGrids.Find(delegate (TestDepthTpGrid g) { return g.IsInSamePosition(testGrid); });
        }

    }
}
