using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.ES.Data;
using MasterCom.Util;

namespace MasterCom.ES.UI
{
    public partial class L3MsgParamDlg : BaseFormStyle
    {
        L3MsgParaOptionDlg dlg = new L3MsgParaOptionDlg();
        public L3MsgParamDlg()
        {
            InitializeComponent();
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            
            if(DialogResult.OK == dlg.ShowDialog(this))
            {
                L3MsgParam para = dlg.GetResult();
                if(para!=null)
                {
                    lstViewL3Params.Items.Add(makeListViewItemFrom(para));
                }
            }
        }
        private ListViewItem makeListViewItemFrom(L3MsgParam para)
        {
            ListViewItem lvi = new ListViewItem();
            lvi.Tag = para;
            lvi.Text = para.msgId.ToString();
            lvi.SubItems.Add(para.keyStr);
            lvi.SubItems.Add(para.GetDesc());
            return lvi;
        }

        internal void FillParams(Dictionary<int, List<L3MsgParam>> msgL3ParaDic)
        {
            lstViewL3Params.Items.Clear();
            foreach (List<L3MsgParam> list in msgL3ParaDic.Values)
            {
                foreach (L3MsgParam para in list)
                {
                    lstViewL3Params.Items.Add(makeListViewItemFrom(para));
                }
            }
        }

        internal Dictionary<int, List<L3MsgParam>> GetResultL3ParaDic()
        {
            Dictionary<int, List<L3MsgParam>> dic = new Dictionary<int, List<L3MsgParam>>();
            foreach (ListViewItem lvi in lstViewL3Params.Items)
            {
                L3MsgParam para = lvi.Tag as L3MsgParam;
                if(dic.ContainsKey(para.msgId))
                {
                    dic[para.msgId].Add(para);
                }
                else
                {
                    List<L3MsgParam> list = new List<L3MsgParam>();
                    list.Add(para);
                    dic[para.msgId] = list;
                }
            }
            return dic;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK; 
        }

        private void btnDel_Click(object sender, EventArgs e)
        {
            if(lstViewL3Params.SelectedItems.Count>0)
            {
                lstViewL3Params.Items.Remove(lstViewL3Params.SelectedItems[0]);
            }
        }
    }
}