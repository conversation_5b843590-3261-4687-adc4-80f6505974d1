﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.Model.Interface
{
    /// <summary>
    /// Written By WuJunHong 2012.8.10
    /// </summary>
    public class ZTDIYRxlevComparisonAnaByDTAndScanByRegion : QueryBase
    {
        public ZTDIYRxlevComparisonAnaByDTAndScanByRegion(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "场强对比分析(按区域)"; }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 12000, 12068, this.Name);
        }

        protected MapFormItemSelection ItemSelection;
        protected override void query()
        {
            SetQueryFormRxlevComp setQueryFormRxlevComp = new SetQueryFormRxlevComp(MainModel, ItemSelection, Condition);
            setQueryFormRxlevComp.Show(MainModel.MainForm);
        }
    }
}
