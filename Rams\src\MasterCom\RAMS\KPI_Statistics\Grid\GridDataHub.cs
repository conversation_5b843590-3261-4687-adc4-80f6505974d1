﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Grid;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class GridDataUnit : GridUnitBase
    {
        public GridDataUnit(double ltLng, double ltLat)
            : base(ltLng, ltLat)
        { }
        public GridDataUnit()
        { }

        public virtual GridDataUnit Clone()
        {
            GridDataUnit clone = new GridDataUnit(this.LTLng, this.LTLat);
            clone.dataHub = this.dataHub.Clone();
            return clone;
        }

        StatDataHubBase dataHub = new StatDataHubBase();
        public StatDataHubBase DataHub
        {
            get { return dataHub; }
        }
        public virtual void AddStatData(KPIStatDataBase data)
        {
            dataHub.AddStatData(data, false);
        }
        public virtual void Gather(GridDataUnit other)
        {
            dataHub.Merge(other.dataHub);
        }
        public KPIStatDataBase GetStatData(Type type)
        {
            return dataHub.GetStatData(type);
        }

    }
}
