﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LowSpeedCellDlg_LTE
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.numMinRssi = new DevExpress.XtraEditors.SpinEdit();
            this.chkSaveTestPoint = new DevExpress.XtraEditors.CheckEdit();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.numMaxRssi = new DevExpress.XtraEditors.SpinEdit();
            this.numMinSinr = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.numMaxSinr = new DevExpress.XtraEditors.SpinEdit();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.numProblemCount = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.numProblemRate = new DevExpress.XtraEditors.SpinEdit();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.radioPdcp = new System.Windows.Forms.RadioButton();
            this.radioApp = new System.Windows.Forms.RadioButton();
            this.grpPdcp = new System.Windows.Forms.GroupBox();
            this.numPdcpMin = new DevExpress.XtraEditors.SpinEdit();
            this.numPdcpMax = new DevExpress.XtraEditors.SpinEdit();
            this.label3 = new System.Windows.Forms.Label();
            this.grpApp = new System.Windows.Forms.GroupBox();
            this.label4 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.chkEmail = new System.Windows.Forms.CheckBox();
            this.chkFTPDownLoad = new System.Windows.Forms.CheckBox();
            this.numFTPDownLoadMax = new DevExpress.XtraEditors.SpinEdit();
            this.numFTPDownLoadMin = new DevExpress.XtraEditors.SpinEdit();
            this.numEmailMax = new DevExpress.XtraEditors.SpinEdit();
            this.numHTTPMin = new DevExpress.XtraEditors.SpinEdit();
            this.numHTTPMax = new DevExpress.XtraEditors.SpinEdit();
            this.chkHttp = new System.Windows.Forms.CheckBox();
            this.numEmailMin = new DevExpress.XtraEditors.SpinEdit();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.label5 = new System.Windows.Forms.Label();
            this.chkFTPUpLoad = new System.Windows.Forms.CheckBox();
            this.numFTPUpLoadMax = new DevExpress.XtraEditors.SpinEdit();
            this.numFTPUpLoadMin = new DevExpress.XtraEditors.SpinEdit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinRssi.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSaveTestPoint.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxRssi.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinSinr.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxSinr.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numProblemCount.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numProblemRate.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.grpPdcp.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numPdcpMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPdcpMax.Properties)).BeginInit();
            this.grpApp.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPDownLoadMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPDownLoadMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numEmailMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHTTPMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHTTPMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numEmailMin.Properties)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPUpLoadMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPUpLoadMin.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // numMinRssi
            // 
            this.numMinRssi.EditValue = new decimal(new int[] {
            125,
            0,
            0,
            -2147483648});
            this.numMinRssi.Location = new System.Drawing.Point(30, 24);
            this.numMinRssi.Name = "numMinRssi";
            this.numMinRssi.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numMinRssi.Properties.Appearance.Options.UseFont = true;
            this.numMinRssi.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMinRssi.Properties.IsFloatValue = false;
            this.numMinRssi.Properties.Mask.EditMask = "N00";
            this.numMinRssi.Properties.MaxValue = new decimal(new int[] {
            20,
            0,
            0,
            -2147483648});
            this.numMinRssi.Properties.MinValue = new decimal(new int[] {
            130,
            0,
            0,
            -2147483648});
            this.numMinRssi.Size = new System.Drawing.Size(73, 20);
            this.numMinRssi.TabIndex = 0;
            // 
            // chkSaveTestPoint
            // 
            this.chkSaveTestPoint.Location = new System.Drawing.Point(10, 442);
            this.chkSaveTestPoint.Name = "chkSaveTestPoint";
            this.chkSaveTestPoint.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.chkSaveTestPoint.Properties.Appearance.Options.UseFont = true;
            this.chkSaveTestPoint.Properties.Caption = "保留采样点";
            this.chkSaveTestPoint.Size = new System.Drawing.Size(118, 19);
            this.chkSaveTestPoint.TabIndex = 2;
            // 
            // btnOK
            // 
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(490, 476);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 3;
            this.btnOK.Text = "确定";
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(109, 30);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(60, 12);
            this.labelControl1.TabIndex = 20;
            this.labelControl1.Text = "≤ RSRP ≤";
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(254, 29);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(18, 12);
            this.labelControl3.TabIndex = 24;
            this.labelControl3.Text = "dBm";
            // 
            // numMaxRssi
            // 
            this.numMaxRssi.EditValue = new decimal(new int[] {
            25,
            0,
            0,
            -2147483648});
            this.numMaxRssi.Location = new System.Drawing.Point(175, 25);
            this.numMaxRssi.Name = "numMaxRssi";
            this.numMaxRssi.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numMaxRssi.Properties.Appearance.Options.UseFont = true;
            this.numMaxRssi.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMaxRssi.Properties.IsFloatValue = false;
            this.numMaxRssi.Properties.Mask.EditMask = "N00";
            this.numMaxRssi.Properties.MaxValue = new decimal(new int[] {
            20,
            0,
            0,
            -2147483648});
            this.numMaxRssi.Properties.MinValue = new decimal(new int[] {
            130,
            0,
            0,
            -2147483648});
            this.numMaxRssi.Size = new System.Drawing.Size(73, 20);
            this.numMaxRssi.TabIndex = 1;
            // 
            // numMinSinr
            // 
            this.numMinSinr.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            -2147483648});
            this.numMinSinr.Location = new System.Drawing.Point(337, 22);
            this.numMinSinr.Name = "numMinSinr";
            this.numMinSinr.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numMinSinr.Properties.Appearance.Options.UseFont = true;
            this.numMinSinr.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMinSinr.Properties.IsFloatValue = false;
            this.numMinSinr.Properties.Mask.EditMask = "N00";
            this.numMinSinr.Properties.MaxValue = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.numMinSinr.Properties.MinValue = new decimal(new int[] {
            60,
            0,
            0,
            -2147483648});
            this.numMinSinr.Size = new System.Drawing.Size(73, 20);
            this.numMinSinr.TabIndex = 2;
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(416, 27);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(60, 12);
            this.labelControl5.TabIndex = 28;
            this.labelControl5.Text = "≤ SINR ≤";
            // 
            // numMaxSinr
            // 
            this.numMaxSinr.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.numMaxSinr.Location = new System.Drawing.Point(482, 22);
            this.numMaxSinr.Name = "numMaxSinr";
            this.numMaxSinr.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numMaxSinr.Properties.Appearance.Options.UseFont = true;
            this.numMaxSinr.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMaxSinr.Properties.IsFloatValue = false;
            this.numMaxSinr.Properties.Mask.EditMask = "N00";
            this.numMaxSinr.Properties.MaxValue = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.numMaxSinr.Properties.MinValue = new decimal(new int[] {
            60,
            0,
            0,
            -2147483648});
            this.numMaxSinr.Size = new System.Drawing.Size(73, 20);
            this.numMaxSinr.TabIndex = 3;
            // 
            // btnCancel
            // 
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnCancel.Location = new System.Drawing.Point(584, 476);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 4;
            this.btnCancel.Text = "取消";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(561, 27);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(12, 12);
            this.labelControl2.TabIndex = 34;
            this.labelControl2.Text = "dB";
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControl7.Appearance.Options.UseFont = true;
            this.labelControl7.Location = new System.Drawing.Point(77, 30);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(72, 12);
            this.labelControl7.TabIndex = 0;
            this.labelControl7.Text = "问题点个数≥";
            // 
            // numProblemCount
            // 
            this.numProblemCount.EditValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numProblemCount.Location = new System.Drawing.Point(158, 27);
            this.numProblemCount.Name = "numProblemCount";
            this.numProblemCount.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numProblemCount.Properties.Appearance.Options.UseFont = true;
            this.numProblemCount.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numProblemCount.Properties.IsFloatValue = false;
            this.numProblemCount.Properties.Mask.EditMask = "N00";
            this.numProblemCount.Properties.MaxValue = new decimal(new int[] {
            500000,
            0,
            0,
            0});
            this.numProblemCount.Size = new System.Drawing.Size(73, 20);
            this.numProblemCount.TabIndex = 0;
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControl8.Appearance.Options.UseFont = true;
            this.labelControl8.Location = new System.Drawing.Point(318, 30);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(72, 12);
            this.labelControl8.TabIndex = 2;
            this.labelControl8.Text = "问题点占比≥";
            // 
            // numProblemRate
            // 
            this.numProblemRate.EditValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numProblemRate.Location = new System.Drawing.Point(398, 27);
            this.numProblemRate.Name = "numProblemRate";
            this.numProblemRate.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numProblemRate.Properties.Appearance.Options.UseFont = true;
            this.numProblemRate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numProblemRate.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numProblemRate.Size = new System.Drawing.Size(73, 20);
            this.numProblemRate.TabIndex = 1;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.groupBox3);
            this.groupBox1.Controls.Add(this.numMinRssi);
            this.groupBox1.Controls.Add(this.labelControl1);
            this.groupBox1.Controls.Add(this.labelControl3);
            this.groupBox1.Controls.Add(this.numMaxRssi);
            this.groupBox1.Controls.Add(this.labelControl2);
            this.groupBox1.Controls.Add(this.numMinSinr);
            this.groupBox1.Controls.Add(this.labelControl5);
            this.groupBox1.Controls.Add(this.numMaxSinr);
            this.groupBox1.Location = new System.Drawing.Point(12, 12);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(647, 306);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "采样点条件(且关系)";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.radioPdcp);
            this.groupBox3.Controls.Add(this.radioApp);
            this.groupBox3.Controls.Add(this.grpPdcp);
            this.groupBox3.Controls.Add(this.grpApp);
            this.groupBox3.Location = new System.Drawing.Point(30, 51);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(599, 232);
            this.groupBox3.TabIndex = 3;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "速率(Mbps)";
            // 
            // radioPdcp
            // 
            this.radioPdcp.AutoSize = true;
            this.radioPdcp.Location = new System.Drawing.Point(26, 182);
            this.radioPdcp.Name = "radioPdcp";
            this.radioPdcp.Size = new System.Drawing.Size(47, 16);
            this.radioPdcp.TabIndex = 0;
            this.radioPdcp.Text = "PDCP";
            this.radioPdcp.UseVisualStyleBackColor = true;
            // 
            // radioApp
            // 
            this.radioApp.AutoSize = true;
            this.radioApp.Checked = true;
            this.radioApp.Location = new System.Drawing.Point(30, 42);
            this.radioApp.Name = "radioApp";
            this.radioApp.Size = new System.Drawing.Size(41, 16);
            this.radioApp.TabIndex = 0;
            this.radioApp.TabStop = true;
            this.radioApp.Text = "App";
            this.radioApp.UseVisualStyleBackColor = true;
            this.radioApp.CheckedChanged += new System.EventHandler(this.radioApp_CheckedChanged);
            // 
            // grpPdcp
            // 
            this.grpPdcp.Controls.Add(this.numPdcpMin);
            this.grpPdcp.Controls.Add(this.numPdcpMax);
            this.grpPdcp.Controls.Add(this.label3);
            this.grpPdcp.Enabled = false;
            this.grpPdcp.Location = new System.Drawing.Point(83, 164);
            this.grpPdcp.Name = "grpPdcp";
            this.grpPdcp.Size = new System.Drawing.Size(501, 56);
            this.grpPdcp.TabIndex = 0;
            this.grpPdcp.TabStop = false;
            this.grpPdcp.Text = "PDCP";
            // 
            // numPdcpMin
            // 
            this.numPdcpMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numPdcpMin.Location = new System.Drawing.Point(66, 23);
            this.numPdcpMin.Name = "numPdcpMin";
            this.numPdcpMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numPdcpMin.Size = new System.Drawing.Size(73, 21);
            this.numPdcpMin.TabIndex = 0;
            // 
            // numPdcpMax
            // 
            this.numPdcpMax.EditValue = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numPdcpMax.Location = new System.Drawing.Point(307, 23);
            this.numPdcpMax.Name = "numPdcpMax";
            this.numPdcpMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numPdcpMax.Size = new System.Drawing.Size(73, 21);
            this.numPdcpMax.TabIndex = 1;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(152, 28);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(149, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "≤   Download Rate    ≤";
            // 
            // grpApp
            // 
            this.grpApp.Controls.Add(this.label5);
            this.grpApp.Controls.Add(this.chkFTPUpLoad);
            this.grpApp.Controls.Add(this.numFTPUpLoadMax);
            this.grpApp.Controls.Add(this.numFTPUpLoadMin);
            this.grpApp.Controls.Add(this.label4);
            this.grpApp.Controls.Add(this.label2);
            this.grpApp.Controls.Add(this.label1);
            this.grpApp.Controls.Add(this.chkEmail);
            this.grpApp.Controls.Add(this.chkFTPDownLoad);
            this.grpApp.Controls.Add(this.numFTPDownLoadMax);
            this.grpApp.Controls.Add(this.numFTPDownLoadMin);
            this.grpApp.Controls.Add(this.numEmailMax);
            this.grpApp.Controls.Add(this.numHTTPMin);
            this.grpApp.Controls.Add(this.numHTTPMax);
            this.grpApp.Controls.Add(this.chkHttp);
            this.grpApp.Controls.Add(this.numEmailMin);
            this.grpApp.Location = new System.Drawing.Point(83, 20);
            this.grpApp.Name = "grpApp";
            this.grpApp.Size = new System.Drawing.Size(501, 138);
            this.grpApp.TabIndex = 0;
            this.grpApp.TabStop = false;
            this.grpApp.Text = "App";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(148, 110);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(149, 12);
            this.label4.TabIndex = 6;
            this.label4.Text = "≤  EMail SMTP Rate   ≤";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(148, 83);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(149, 12);
            this.label2.TabIndex = 6;
            this.label2.Text = "≤ HTTP Download Rate ≤";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(148, 26);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(149, 12);
            this.label1.TabIndex = 6;
            this.label1.Text = "≤ FTP Download Rate  ≤";
            // 
            // chkEmail
            // 
            this.chkEmail.AutoSize = true;
            this.chkEmail.Checked = true;
            this.chkEmail.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkEmail.Location = new System.Drawing.Point(41, 110);
            this.chkEmail.Name = "chkEmail";
            this.chkEmail.Size = new System.Drawing.Size(15, 14);
            this.chkEmail.TabIndex = 5;
            this.chkEmail.UseVisualStyleBackColor = true;
            this.chkEmail.CheckedChanged += new System.EventHandler(this.chkEmail_CheckedChanged);
            // 
            // chkFTPDownLoad
            // 
            this.chkFTPDownLoad.AutoSize = true;
            this.chkFTPDownLoad.Checked = true;
            this.chkFTPDownLoad.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkFTPDownLoad.Location = new System.Drawing.Point(41, 23);
            this.chkFTPDownLoad.Name = "chkFTPDownLoad";
            this.chkFTPDownLoad.Size = new System.Drawing.Size(15, 14);
            this.chkFTPDownLoad.TabIndex = 5;
            this.chkFTPDownLoad.UseVisualStyleBackColor = true;
            this.chkFTPDownLoad.CheckedChanged += new System.EventHandler(this.chkFTP_CheckedChanged);
            // 
            // numFTPDownLoadMax
            // 
            this.numFTPDownLoadMax.EditValue = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numFTPDownLoadMax.Location = new System.Drawing.Point(303, 19);
            this.numFTPDownLoadMax.Name = "numFTPDownLoadMax";
            this.numFTPDownLoadMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numFTPDownLoadMax.Size = new System.Drawing.Size(73, 21);
            this.numFTPDownLoadMax.TabIndex = 1;
            // 
            // numFTPDownLoadMin
            // 
            this.numFTPDownLoadMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numFTPDownLoadMin.Location = new System.Drawing.Point(62, 20);
            this.numFTPDownLoadMin.Name = "numFTPDownLoadMin";
            this.numFTPDownLoadMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numFTPDownLoadMin.Size = new System.Drawing.Size(73, 21);
            this.numFTPDownLoadMin.TabIndex = 0;
            // 
            // numEmailMax
            // 
            this.numEmailMax.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.numEmailMax.Location = new System.Drawing.Point(303, 105);
            this.numEmailMax.Name = "numEmailMax";
            this.numEmailMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numEmailMax.Size = new System.Drawing.Size(73, 21);
            this.numEmailMax.TabIndex = 5;
            // 
            // numHTTPMin
            // 
            this.numHTTPMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numHTTPMin.Location = new System.Drawing.Point(62, 78);
            this.numHTTPMin.Name = "numHTTPMin";
            this.numHTTPMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numHTTPMin.Size = new System.Drawing.Size(73, 21);
            this.numHTTPMin.TabIndex = 2;
            // 
            // numHTTPMax
            // 
            this.numHTTPMax.EditValue = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numHTTPMax.Location = new System.Drawing.Point(303, 78);
            this.numHTTPMax.Name = "numHTTPMax";
            this.numHTTPMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numHTTPMax.Size = new System.Drawing.Size(73, 21);
            this.numHTTPMax.TabIndex = 3;
            // 
            // chkHttp
            // 
            this.chkHttp.AutoSize = true;
            this.chkHttp.Checked = true;
            this.chkHttp.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkHttp.Location = new System.Drawing.Point(41, 83);
            this.chkHttp.Name = "chkHttp";
            this.chkHttp.Size = new System.Drawing.Size(15, 14);
            this.chkHttp.TabIndex = 5;
            this.chkHttp.UseVisualStyleBackColor = true;
            this.chkHttp.CheckedChanged += new System.EventHandler(this.chkHttp_CheckedChanged);
            // 
            // numEmailMin
            // 
            this.numEmailMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numEmailMin.Location = new System.Drawing.Point(62, 105);
            this.numEmailMin.Name = "numEmailMin";
            this.numEmailMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numEmailMin.Size = new System.Drawing.Size(73, 21);
            this.numEmailMin.TabIndex = 4;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.labelControl9);
            this.groupBox2.Controls.Add(this.labelControl7);
            this.groupBox2.Controls.Add(this.numProblemCount);
            this.groupBox2.Controls.Add(this.numProblemRate);
            this.groupBox2.Controls.Add(this.labelControl8);
            this.groupBox2.Location = new System.Drawing.Point(12, 324);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(647, 100);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "过滤条件(且关系)";
            // 
            // labelControl9
            // 
            this.labelControl9.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelControl9.Appearance.Options.UseFont = true;
            this.labelControl9.Location = new System.Drawing.Point(473, 30);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(6, 12);
            this.labelControl9.TabIndex = 39;
            this.labelControl9.Text = "%";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(148, 57);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(149, 12);
            this.label5.TabIndex = 10;
            this.label5.Text = "≤  FTP Upload Rate   ≤";
            // 
            // chkFTPUpLoad
            // 
            this.chkFTPUpLoad.AutoSize = true;
            this.chkFTPUpLoad.Checked = true;
            this.chkFTPUpLoad.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkFTPUpLoad.Location = new System.Drawing.Point(41, 54);
            this.chkFTPUpLoad.Name = "chkFTPUpLoad";
            this.chkFTPUpLoad.Size = new System.Drawing.Size(15, 14);
            this.chkFTPUpLoad.TabIndex = 9;
            this.chkFTPUpLoad.UseVisualStyleBackColor = true;
            this.chkFTPUpLoad.CheckedChanged += new System.EventHandler(this.chkFTPUpLoad_CheckedChanged);
            // 
            // numFTPUpLoadMax
            // 
            this.numFTPUpLoadMax.EditValue = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numFTPUpLoadMax.Location = new System.Drawing.Point(303, 50);
            this.numFTPUpLoadMax.Name = "numFTPUpLoadMax";
            this.numFTPUpLoadMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numFTPUpLoadMax.Size = new System.Drawing.Size(73, 21);
            this.numFTPUpLoadMax.TabIndex = 8;
            // 
            // numFTPUpLoadMin
            // 
            this.numFTPUpLoadMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numFTPUpLoadMin.Location = new System.Drawing.Point(62, 51);
            this.numFTPUpLoadMin.Name = "numFTPUpLoadMin";
            this.numFTPUpLoadMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numFTPUpLoadMin.Size = new System.Drawing.Size(73, 21);
            this.numFTPUpLoadMin.TabIndex = 7;
            // 
            // LowSpeedCellDlg_LTE
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(671, 514);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.chkSaveTestPoint);
            this.Name = "LowSpeedCellDlg_LTE";
            this.Text = "LTE低速率小区条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numMinRssi.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSaveTestPoint.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxRssi.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinSinr.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxSinr.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numProblemCount.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numProblemRate.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.grpPdcp.ResumeLayout(false);
            this.grpPdcp.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numPdcpMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPdcpMax.Properties)).EndInit();
            this.grpApp.ResumeLayout(false);
            this.grpApp.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPDownLoadMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPDownLoadMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numEmailMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHTTPMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHTTPMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numEmailMin.Properties)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPUpLoadMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFTPUpLoadMin.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SpinEdit numMinRssi;
        private DevExpress.XtraEditors.CheckEdit chkSaveTestPoint;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.SpinEdit numMaxRssi;
        private DevExpress.XtraEditors.SpinEdit numMinSinr;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.SpinEdit numMaxSinr;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.SpinEdit numProblemCount;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.SpinEdit numProblemRate;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.RadioButton radioPdcp;
        private System.Windows.Forms.RadioButton radioApp;
        private System.Windows.Forms.GroupBox grpPdcp;
        private DevExpress.XtraEditors.SpinEdit numPdcpMax;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.GroupBox grpApp;
        private DevExpress.XtraEditors.SpinEdit numFTPDownLoadMax;
        private DevExpress.XtraEditors.SpinEdit numHTTPMax;
        private DevExpress.XtraEditors.SpinEdit numEmailMax;
        private DevExpress.XtraEditors.SpinEdit numFTPDownLoadMin;
        private DevExpress.XtraEditors.SpinEdit numHTTPMin;
        private DevExpress.XtraEditors.SpinEdit numEmailMin;
        private DevExpress.XtraEditors.SpinEdit numPdcpMin;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.CheckBox chkEmail;
        private System.Windows.Forms.CheckBox chkHttp;
        private System.Windows.Forms.CheckBox chkFTPDownLoad;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.CheckBox chkFTPUpLoad;
        private DevExpress.XtraEditors.SpinEdit numFTPUpLoadMax;
        private DevExpress.XtraEditors.SpinEdit numFTPUpLoadMin;
    }
}