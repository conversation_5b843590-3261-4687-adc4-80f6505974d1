﻿using MasterCom.RAMS.Func;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRDownloadSpeedAnaForm : MinCloseForm
    {
        public NRDownloadSpeedAnaForm()
        {
            InitializeComponent();
        }

        public void FillData(List<NRDownloadSpeedAnaRes> resList)
        {
            gridControl.DataSource = resList;
            gridControl.RefreshDataSource();
        }

        private void miExportXlsSum_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView);
        }
    }
}
