﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using System.Windows.Forms;
using CQTLibrary.PublicItem;
using CQTLibrary.CqtZTFunc;
using System.Reflection;
using System.Collections;

namespace MasterCom.RAMS.CQT
{
    public class DIYQueryCQTHandOverFrequentlyData : DIYStatQuery
    {
        public DIYQueryCQTHandOverFrequentlyData(MainModel mainModel, string netWoker)
            : base(mainModel)
        {
            this.mainModel = mainModel;
            this.netType = netWoker;
        }
        private readonly string netType;
        /// <summary>
        /// 标题名称
        /// </summary>
        public override string Name
        {
            get { return ""; }
        }
        /// <summary>
        /// 图标名称
        /// </summary>
        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }
        /// <summary>
        /// 查询条件是否有效
        /// </summary>
        protected override bool isValidCondition()
        {
            return true;
        }
        protected override StatTbToken getBranchToken()
        {
            return StatTbToken.log;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 21000, 21024, this.Name);//临时
        }

        #region 全局变量
        private readonly List<FileInfo> fileList = new List<FileInfo>();
        private readonly List<string> fileValueNameList = new List<string>();
        readonly Dictionary<string, List<FileInfo>> fileValueList = new Dictionary<string, List<FileInfo>>();
        Dictionary<int, List<Event>> condEventsDic = new Dictionary<int, List<Event>>();
        readonly List<HandOverItemCQT> cqtResultList = new List<HandOverItemCQT>();
        Dictionary<AreaKeyCQT, Dictionary<string, HoSubInfoCQT>> areaKeyStringHoSubInfo
            = new Dictionary<AreaKeyCQT, Dictionary<string, HoSubInfoCQT>>();
        #endregion
        /// <summary>
        /// 查询数据条件
        /// </summary>
        protected override void query()
        {
            fileList.Clear();
            fileValueNameList.Clear();
            fileValueList.Clear();
            cqtResultList.Clear();
            areaKeyStringHoSubInfo.Clear();
            // 查找全区域文件
            DIYFileInfoData diyFileInfoData = new DIYFileInfoData(mainModel);
            diyFileInfoData.SetQueryCondition(this.Condition);
            diyFileInfoData.Query();
            fileList.AddRange(diyFileInfoData.FlieInfoData);
            //找出所有文件中包含的地点名称
            foreach (FileInfo fileIn in fileList)
            {
                string[] name = fileIn.Name.Split('_');
                if (name.Length < 3)
                    continue;
                if (!fileValueNameList.Contains(name[2]))
                {
                    fileValueNameList.Add(name[2]);
                }
            }
            //处理事件
            PrepareEvents();
            List<int> eventId = new List<int>();
            eventId.AddRange(condEventsDic.Keys);
            //每个地点所涉及的文件
            addFileList(eventId);
            //各测试地点事件
            if (netType.Equals("GSM"))
            {
                WaitBox.Show("开始分析各个测试地点的切换事件", cqtHandOverEventAnaGSM);
            }
            else if (netType.Equals("TD"))
            {
                WaitBox.Show("开始分析各个测试地点的切换事件", cqtHandOverEventAnaTD);
            }

            CQTHandOverFrequentlyFormNew cqtShowForm = new CQTHandOverFrequentlyFormNew(MainModel, netType);
            cqtShowForm.setData(cqtResultList, areaKeyStringHoSubInfo);
            cqtShowForm.Show();

        }

        private void addFileList(List<int> eventId)
        {
            foreach (string nameL in fileValueNameList)
            {
                List<FileInfo> subFileList = new List<FileInfo>();
                foreach (FileInfo fileIn in fileList)
                {
                    string[] name = fileIn.Name.Split('_');
                    if (name.Length < 3)
                        continue;
                    if (nameL.Equals(name[2]) && eventId.Contains(fileIn.ID))
                    {
                        subFileList.Add(fileIn);
                    }
                }
                fileValueList.Add(nameL, subFileList);
            }
        }

        /// <summary>
        /// 查询全区域切换事件
        /// </summary>
        private void PrepareEvents()
        {
            DIYEventByAllRegion queryEvent = new DIYEventByAllRegion(MainModel);
            queryEvent.SetIsAddEventToDTDataManager(false);
            queryEvent.SetSaveAsFileEventsDic(true);
            queryEvent.showEventChooser = false;
            queryEvent.IsQueryAllEvents = false;
            List<int> eventIds = new List<int>();
            if (netType.Equals("GSM"))
            {
                eventIds.Add(17);
            }
            else if (netType.Equals("TD"))
            {
                eventIds.Add(142);
                eventIds.Add(145);
                eventIds.Add(148);
                eventIds.Add(151);
            }
            condition.EventIDs = eventIds;
            queryEvent.SetQueryCondition(condition);
            queryEvent.Query();
            condEventsDic = queryEvent.fileEventsDic;
        }
        /// <summary>
        /// 按文件分析切换事件（TD）
        /// </summary>
        private void cqtHandOverEventAnaTD()
        {
            WaitBox.CanCancel = true;
            int idx = 1;
            foreach (string cpn in fileValueNameList)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                WaitBox.Text = "正在分析 " + idx++ + "/" + fileValueNameList.Count + " " + cpn + " 的切换事件...";
                WaitBox.ProgressPercent = 30;
                foreach (FileInfo fileinfo in fileValueList[cpn])
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    addCqtResultList(cpn, fileinfo);
                }
                WaitBox.ProgressPercent = 90;
            }
            areaKeyStringHoSubInfo = getHo3FreqList(cqtResultList, 30, 4);
            WaitBox.Close();
        }

        private void addCqtResultList(string cpn, FileInfo fileinfo)
        {
            foreach (Event eve in condEventsDic[fileinfo.ID])
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                if (eve.ID % 3 != 1)
                    continue;
                HandOverItemCQT cqtResultTem = new HandOverItemCQT();
                cqtResultTem.Iareatype = eve.AreaTypeID;
                cqtResultTem.Iareaid = eve.AreaID;
                cqtResultTem.Ifileid = eve.FileID;
                cqtResultTem.Ilac = (int)eve["LAC"];
                cqtResultTem.Ici = (int)eve["CI"];
                cqtResultTem.Itime = (int)(JavaDate.GetMilliseconds(eve.DateTime) / 1000);
                cqtResultTem.Strcqtname = cpn;
                TDCell tdcell = CellManager.GetInstance().GetTDCell(eve.DateTime, (ushort)cqtResultTem.Ilac, (ushort)cqtResultTem.Ici);
                if (tdcell != null)
                    cqtResultTem.Strcellname = tdcell.Name;
                else
                    cqtResultTem.Strcellname = "";

                cqtResultList.Add(cqtResultTem);
            }
        }

        /// <summary>
        /// 按文件分析切换事件（GSM）
        /// </summary>
        private void cqtHandOverEventAnaGSM()
        {
            WaitBox.CanCancel = true;
            int idx = 1;
            foreach (string cpn in fileValueNameList)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                WaitBox.Text = "正在分析 " + idx++ + "/" + fileValueNameList.Count + " " + cpn + " 的切换事件...";
                WaitBox.ProgressPercent = 30;
                foreach (FileInfo fileinfo in fileValueList[cpn])
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    addGsmCqtResultList(cpn, fileinfo);
                }
                WaitBox.ProgressPercent = 90;
            }
            areaKeyStringHoSubInfo = getHo3FreqList(cqtResultList, 30, 4);
            WaitBox.Close();
        }

        private void addGsmCqtResultList(string cpn, FileInfo fileinfo)
        {
            foreach (Event eve in condEventsDic[fileinfo.ID])
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                if (eve.ID != 17)
                    continue;
                HandOverItemCQT cqtResultTem = new HandOverItemCQT();
                cqtResultTem.Iareatype = eve.AreaTypeID;
                cqtResultTem.Iareaid = eve.AreaID;
                cqtResultTem.Ifileid = eve.FileID;
                cqtResultTem.Ilac = (int)eve["LAC"];
                cqtResultTem.Ici = (int)eve["CI"];
                cqtResultTem.Itime = (int)(JavaDate.GetMilliseconds(eve.DateTime) / 1000);
                cqtResultTem.Strcqtname = cpn;
                Cell cell = CellManager.GetInstance().GetCell(eve.DateTime, (ushort)cqtResultTem.Ilac, (ushort)cqtResultTem.Ici);
                if (cell != null)
                    cqtResultTem.Strcellname = cell.Name;
                else
                    cqtResultTem.Strcellname = "";

                cqtResultList.Add(cqtResultTem);
            }
        }

        /// <summary>
        /// 获取切换频繁序列
        /// </summary>
        public Dictionary<AreaKeyCQT, Dictionary<string, HoSubInfoCQT>> getHo3FreqList(List<HandOverItemCQT> hoList, int iSecond, int itime)
        {
            Dictionary<AreaKeyCQT, Dictionary<int, List<HandOverItemCQT>>> areaFileDic = getAreaFileDic(hoList);
            Dictionary<AreaKeyCQT, Dictionary<string, HoSubInfoCQT>> handoverDic = new Dictionary<AreaKeyCQT, Dictionary<string, HoSubInfoCQT>>();
            foreach (AreaKeyCQT aKey in areaFileDic.Keys)
            {
                Dictionary<int, List<HandOverItemCQT>> areaHoDic = areaFileDic[aKey];
                Dictionary<string, HoSubInfoCQT> hoSubDic = new Dictionary<string, HoSubInfoCQT>();
                foreach (int ifileid in areaHoDic.Keys)
                {
                    List<HandOverItemCQT> hoListFile = areaHoDic[ifileid];
                    List<List<HandOverItemCQT>> hotmpList = new List<List<HandOverItemCQT>>();
                    HandOverItemCQT tmpHo = new HandOverItemCQT();
                    ReverserCQT<HandOverItemCQT> reverser = new ReverserCQT<HandOverItemCQT>(tmpHo.GetType(), "Itime", ReverserInfoCQT.Direction.ASC);
                    hoListFile.Sort(reverser);
                    if (hoListFile.Count < 3)
                        continue;
                    getHotmpList(iSecond, itime, hoListFile, hotmpList);
                    dealResult(hoSubDic, hotmpList);
                }
                if (hoSubDic.Count == 0)
                    continue;
                handoverDic.Add(aKey, hoSubDic);
            }
            return handoverDic;
        }

        private static Dictionary<AreaKeyCQT, Dictionary<int, List<HandOverItemCQT>>> getAreaFileDic(List<HandOverItemCQT> hoList)
        {
            Dictionary<AreaKeyCQT, Dictionary<int, List<HandOverItemCQT>>> areaFileDic = new Dictionary<AreaKeyCQT, Dictionary<int, List<HandOverItemCQT>>>();
            foreach (HandOverItemCQT ho in hoList)
            {
                AreaKeyCQT aKey = new AreaKeyCQT();
                aKey.Iareatypeid = ho.Iareatype;
                aKey.Iareaid = ho.Iareaid;
                if (areaFileDic.ContainsKey(aKey))
                {
                    Dictionary<int, List<HandOverItemCQT>> fileDic = areaFileDic[aKey];
                    if (fileDic.ContainsKey(ho.Ifileid))
                    {
                        List<HandOverItemCQT> tmpList = fileDic[ho.Ifileid];
                        tmpList.Add(ho);
                    }
                    else
                    {
                        List<HandOverItemCQT> tmpList = new List<HandOverItemCQT>();
                        tmpList.Add(ho);
                        fileDic[ho.Ifileid] = tmpList;
                    }
                }
                else
                {
                    Dictionary<int, List<HandOverItemCQT>> fileDic = new Dictionary<int, List<HandOverItemCQT>>();
                    List<HandOverItemCQT> tmpList = new List<HandOverItemCQT>();
                    tmpList.Add(ho);
                    fileDic.Add(ho.Ifileid, tmpList);
                    areaFileDic.Add(aKey, fileDic);
                }
            }

            return areaFileDic;
        }

        private void getHotmpList(int iSecond, int itime, List<HandOverItemCQT> hoListFile, List<List<HandOverItemCQT>> hotmpList)
        {
            int k = 0;
            for (int i = 0; i < hoListFile.Count - 1; i++)
            {
                if (i < k)
                    continue;
                List<HandOverItemCQT> hojList = new List<HandOverItemCQT>();
                HandOverItemCQT ho1 = hoListFile[i];
                int j = i + 1;
                while (j <= hoListFile.Count - 1)
                {
                    if (ho1.Itime + iSecond > hoListFile[j].Itime)
                        hojList.Add(hoListFile[j]);
                    else
                        break;
                    j++;
                }
                if (hojList.Count > itime)
                {
                    hotmpList.Add(hojList);
                    k = j;
                }
            }
        }

        private void dealResult(Dictionary<string, HoSubInfoCQT> hoSubDic, List<List<HandOverItemCQT>> hotmpList)
        {
            foreach (List<HandOverItemCQT> hoiList in hotmpList)
            {
                StringBuilder str = new StringBuilder();
                StringBuilder strDetail = new StringBuilder();
                int istime = 0;
                int ietime = 0;
                string strCQTNameTem = "";
                List<int> iciList = new List<int>();
                foreach (HandOverItemCQT hoi in hoiList)
                {
                    if (istime == 0)
                    {
                        istime = hoi.Itime;
                    }
                    strCQTNameTem = hoi.Strcqtname;
                    ietime = hoi.Itime;
                    if (!iciList.Contains(hoi.Ici))
                    {
                        iciList.Add(hoi.Ici);
                    }
                    if (str.Length == 0)
                    {
                        str.Append(hoi.Ici.ToString());
                        strDetail.Append(hoi.Strcellname + "[" + hoi.Ilac + "," + hoi.Ici + "]");
                    }
                    else
                    {
                        str.Append("->" + hoi.Ici.ToString());
                        strDetail.Append(" -> " + hoi.Strcellname + "[" + hoi.Ilac + "," + hoi.Ici + "]");
                    }
                }

                if (!hoSubDic.TryGetValue(str.ToString(), out HoSubInfoCQT hsi))
                {
                    //hsi.Isecond = ietime - istime;
                    //hsi.Itime = 1;
                    hsi.ICellNum = iciList.Count;
                    hsi.StrHandOver = strDetail.ToString();
                    hsi.StrCqtName = strCQTNameTem;
                    hoSubDic[str.ToString()] = hsi;
                }

                hsi.Isecond += ietime - istime;
                hsi.Itime += 1;
            }
        }
    }
}
