﻿using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class QueryKPIStatByWhiteListRegion : QueryKPIStatByRegion
    {
        Dictionary<int, FileInfo> fileInfoDic = null;
        ReportAndWhiteListCondition cond = new ReportAndWhiteListCondition();
        public override string Name
        {
            get
            {
                return "跨月指标统计";
            }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11055, this.Name);
        }
        protected override bool getConditionBeforeQuery()
        {
            List<TestRound> testRoundList = getTestRounds();
            if (testRoundList == null || testRoundList.Count <= 0)
            {
                if (XtraMessageBox.Show("未查询到测试时段信息，请先配置！") == DialogResult.OK)
                {
#if PermissionControl_Func || DEBUG
                    bool hasRight = MainModel.User.HasFunctionRight(TestRoundListForm.FuncId);
                    if (hasRight)
                    {
                        TestRoundListForm form = new TestRoundListForm(testRoundList);
                        form.ShowDialog();
                    }
#endif
                }
                return false;
            }

            ReportAndWhiteListSetDlg dlg = new ReportAndWhiteListSetDlg();
            dlg.SetCondition(cond, testRoundList);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            cond = dlg.GetConditon();
            curReportStyle = cond.Report;
            isQueryAllParams = cond.IsQueryAllParams;
            init();
            return true;
        }
        private void init()
        {
            KpiDataManager = new KPIDataManager();
            fileInfoDic = new Dictionary<int, FileInfo>();
        }
        protected override void queryInThread(object o)
        {
            try
            {
                WaitBox.Text = "开始查询KPI统计数据...";
                WaitBox.CanCancel = true;
                ClientProxy clientProxy = (ClientProxy)o;

                string imgTriadIDSet = getStatImgNeededTriadID();
                if (cond.GridStatSetting_LTE.IsChecked)
                {
                    queryStatInfoByGridsAndTestRounds(clientProxy, imgTriadIDSet, cond, cond.GridStatSetting_LTE);
                }

                if (cond.GridStatSetting_Other.IsChecked)
                {
                    queryStatInfoByGridsAndTestRounds(clientProxy, imgTriadIDSet, cond, cond.GridStatSetting_Other);
                }
            }
            catch (Exception e)
            {
                MessageBox.Show("Error" + e.StackTrace + e.ToString());
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void queryStatInfoByGridsAndTestRounds(ClientProxy clientProxy, string imgTriadIDSet
            , ReportAndWhiteListCondition cond, GridStatSetting gridSetting)
        {
            condition.ServiceTypes = gridSetting.ServiceList;

            queryStatInfoByNameFilterTestRounds(clientProxy, imgTriadIDSet, gridSetting.FileNameSuffixesList
                , cond.TestRoundsWhite, gridSetting.WhiteGridNameList, true);//先统计白名单网格

            queryStatInfoByNameFilterTestRounds(clientProxy, imgTriadIDSet, gridSetting.FileNameSuffixesList
                , cond.TestRoundsUnWhite, gridSetting.UnWhiteGridNameList, false);//再统计非白名单网格
        }
        private void queryStatInfoByNameFilterTestRounds(ClientProxy clientProxy, string imgTriadIDSet, List<string> fileNameSuffixesList
             , List<TestRound> testRounds, List<string> gridNameList, bool isWhiteGrid)
        {
            if (testRounds == null || testRounds.Count <= 0 || fileNameSuffixesList == null || fileNameSuffixesList.Count <= 0
                || gridNameList == null || gridNameList.Count <= 0)
            {
                return;
            }
            int splitTokenNum = 0;
            condition.NameFilterType = FileFilterType.ByFileName;

            StringBuilder strbFileNameKey = new StringBuilder();
            foreach (string gridName in gridNameList)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }

                if (strbFileNameKey.Length > 1000)
                {
                    queryStatInfoByTestRounds(clientProxy, imgTriadIDSet, testRounds, isWhiteGrid, ref strbFileNameKey, ref splitTokenNum);
                }
                foreach (string suffixes in fileNameSuffixesList)
                {
                    strbFileNameKey.Append(gridName + suffixes + "{,}");
                    splitTokenNum++;
                }
            }
            queryStatInfoByTestRounds(clientProxy, imgTriadIDSet, testRounds, isWhiteGrid, ref strbFileNameKey, ref splitTokenNum);
            
        }
        private void queryStatInfoByTestRounds(ClientProxy clientProxy, string imgTriadIDSet, List<TestRound> testRounds
            , bool isWhiteGrid, ref StringBuilder strbFileNameKey, ref int splitTokenNum)
        {
            if (strbFileNameKey.Length > 3)
            {
                string strTip = isWhiteGrid ? "白名单" : "非白名单";
                condition.FileName = strbFileNameKey.Remove(strbFileNameKey.Length - 3, 3).ToString();
                condition.FileNameOrNum = splitTokenNum;
                foreach (TestRound round in testRounds)
                {
                    WaitBox.Text = "正在统计" + strTip + "网格在时段[" + round.Period.GetShortString() + "]内的数据...";
                    WaitBox.ProgressPercent = 10;

                    queryPeriodInfo(round.Period, clientProxy, imgTriadIDSet);
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    //GC.Collect();
                }
                strbFileNameKey = new StringBuilder();
                splitTokenNum = 0;
            }
        }

        protected override void recieveAndHandleSpecificStatData(Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef
            , KPIStatDataBase singleStatData)
        {
            double lng = package.Content.GetParamDouble();
            double lat = package.Content.GetParamDouble();
            MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(lng, lat);
            if (!condition.Geometorys.GeoOp.Contains(grid.CenterLng, grid.CenterLat))
            {
                return;
            }
            fillStatData(package, curImgColumnDef, singleStatData);
            int fileID = (int)singleStatData[KPIStatDataBase.FileIDKey, KPIStatDataBase.NewGridFileIDKey, -1];
            MasterCom.RAMS.Model.FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(fileID);
            if (cond.IsShowFileInfoForm && !fileInfoDic.ContainsKey(fi.ID))
            {
                fileInfoDic.Add(fi.ID, fi);
            }

            List<ResvRegion> regs = getStatImgIntersectRegions(lng, lat);
            foreach (ResvRegion reg in regs)
            {
                KpiDataManager.AddStatData(reg.RegionName, reg, fi, singleStatData
                    , this.curReportStyle != null && this.curReportStyle.HasGridPerCell);
            }
            mergeRootNodeData(grid.CenterLng, grid.CenterLat, fi, singleStatData);
        }

        protected override void handleStatEvent(Event evt)
        {
            if (!condition.Geometorys.GeoOp.Contains(evt.Longitude, evt.Latitude))
            {
                return;
            }
            StatDataEvent eventData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
            FileInfo fi = DTDataHeaderManager.GetInstance().GetHeaderByFileID(evt.FileID);
            if (cond.IsShowFileInfoForm && !fileInfoDic.ContainsKey(fi.ID))
            {
                fileInfoDic.Add(fi.ID, fi);
            }

            List<ResvRegion> regs = getEventInRegions(evt);
            foreach (ResvRegion reg in regs)
            {
                KpiDataManager.AddStatData(reg.RegionName, reg, fi, eventData
                    , this.curReportStyle != null && this.curReportStyle.HasGridPerCell);
            }
            mergeRootNodeData(evt.Longitude, evt.Latitude, fi, eventData);
        }
        
        private List<TestRound> getTestRounds()
        {
            List<TestRound> roundList = null;
            QueryTestRound query = new QueryTestRound(mainModel);
            query.Query();
            roundList = query.TestRoundList;
            return roundList;
        }

        protected override void afterRecieveAllData(params object[] reservedParams)
        {
            base.afterRecieveAllData(reservedParams);
            fileInfoDic = null;
            condition = null;
            //GC.Collect();
        }
    }
}
