﻿using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTEHandOverEarfcnAnaByRegion : LTEHandOverEarfcnBase
    {
        public LTEHandOverEarfcnAnaByRegion(bool isVoLTE)
            : base(MainModel.GetInstance())
        {
            init(isVoLTE);
        }

        public override string Name
        {
            get { return "切换频段序列分析(按区域)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22085, this.Name);
        }
        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="testPoint"></param>
        /// <returns></returns>
        /// <summary>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            try
            {
                if (testPoint is LTETestPointDetail)
                {
                    return Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }

    public class LTEHandOverEarfcnAnaByRegion_FDD : LTEHandOverEarfcnBase_FDD
    {
        public LTEHandOverEarfcnAnaByRegion_FDD()
            : base(MainModel.GetInstance())
        {

        }
        public override string Name
        {
            get { return "LTE_FDD切换频段序列分析(按区域)"; }
        }
        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="testPoint"></param>
        /// <returns></returns>
        /// <summary>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            try
            {
                if (testPoint is LTEFddTestPoint)
                {
                    return Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }

    public class VOLTEHandOverEarfcnAnaByRegion_FDD : VOLTEHandOverEarfcnBase_FDD
    {
        public VOLTEHandOverEarfcnAnaByRegion_FDD()
            : base(MainModel.GetInstance())
        {

        }
        public override string Name
        {
            get { return "VOLTE_FDD切换频段序列分析(按区域)"; }
        }
        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="testPoint"></param>
        /// <returns></returns>
        /// <summary>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            try
            {
                if (testPoint is LTEFddTestPoint)
                {
                    return Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }
}