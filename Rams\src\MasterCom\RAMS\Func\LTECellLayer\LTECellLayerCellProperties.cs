﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Func
{
    public partial class MapLTECellLayerCellProperties : MTLayerPropUserControl
    {
        public MapLTECellLayerCellProperties()
        {
            InitializeComponent();
        }

        private MapLTECellLayer layer = null;
        public override void Setup(object obj)
        {
            layer = obj as MapLTECellLayer;
            if (layer == null)
            {
                return;
            }
            Text = "小区";
            checkBoxDisplay.Checked = layer.DrawCell;
            colorCell.Color = layer.ColorCell;
            colorA.Color = layer.ColorCellA;
            colorD.Color = layer.ColorCellD;
            colorE.Color = layer.ColorCellE;
            colorF.Color = layer.ColorCellF;

            colorCellMod3_0.Color = layer.ColorCellMod3_0;
            colorCellMod3_1.Color = layer.ColorCellMod3_1;
            colorCellMod3_2.Color = layer.ColorCellMod3_2;
            colorCellMod6_0.Color = layer.ColorCellMod6_0;
            colorCellMod6_1.Color = layer.ColorCellMod6_1;
            colorCellMod6_2.Color = layer.ColorCellMod6_2;
            colorCellMod6_3.Color = layer.ColorCellMod6_3;
            colorCellMod6_4.Color = layer.ColorCellMod6_4;
            colorCellMod6_5.Color = layer.ColorCellMod6_5;

            TrackBarOpacity.Value = layer.ColorCell.A;
            cbxDrawCellLabel.Checked = layer.DrawCellLabel;
            cbxCellCI.Checked = layer.DrawCellECI;
            this.cbxCellCode.Checked = layer.DrawCellCode;
            this.cbxCellCPI.Checked = layer.DrawCellPCI;
            this.cbxCellDes.Checked = layer.DrawCellDes;
            this.cbxCellFreq.Checked = layer.DrawCellEARFCN;
            this.cbxCellFreqList.Checked = layer.DrawCellFreqs;
            this.cbxCellLAC.Checked = layer.DrawCellTAC;
            this.cbxCellName.Checked = layer.DrawCellName;
            this.radioBtnDefault.Checked = true;
            colorLable.Color = layer.ColorCellLabel;

            checkBoxDisplay.CheckedChanged += new EventHandler(checkBoxDisplay_CheckedChanged);
            colorCell.ColorChanged += new EventHandler(colorCell_ColorChanged);
            colorA.ColorChanged += new EventHandler(colorA_ColorChanged);
            colorD.ColorChanged += new EventHandler(colorD_ColorChanged);
            colorE.ColorChanged += new EventHandler(colorE_ColorChanged);
            colorF.ColorChanged += new EventHandler(colorF_ColorChanged);
            colorCellMod3_0.ColorChanged += new EventHandler(colorCellMod3_0_ColorChanged);
            colorCellMod3_1.ColorChanged += new EventHandler(colorCellMod3_1_ColorChanged);
            colorCellMod3_2.ColorChanged += new EventHandler(colorCellMod3_2_ColorChanged);
            colorCellMod6_0.ColorChanged += new EventHandler(colorCellMod6_0_ColorChanged);
            colorCellMod6_1.ColorChanged += new EventHandler(colorCellMod6_1_ColorChanged);
            colorCellMod6_2.ColorChanged += new EventHandler(colorCellMod6_2_ColorChanged);
            colorCellMod6_3.ColorChanged += new EventHandler(colorCellMod6_3_ColorChanged);
            colorCellMod6_4.ColorChanged += new EventHandler(colorCellMod6_4_ColorChanged);
            colorCellMod6_5.ColorChanged += new EventHandler(colorCellMod6_5_ColorChanged);
            TrackBarOpacity.ValueChanged += new EventHandler(TrackBarOpacity_ValueChanged);
            cbxDrawCellLabel.CheckedChanged += new EventHandler(cbxDrawCellLabel_CheckedChanged);
            cbxCellCI.CheckedChanged += new EventHandler(cbxCellCI_CheckedChanged);
            cbxCellCode.CheckedChanged += new EventHandler(cbxCellCode_CheckedChanged);
            cbxCellCPI.CheckedChanged += new EventHandler(cbxCellCPI_CheckedChanged);
            cbxCellDes.CheckedChanged += new EventHandler(cbxCellDes_CheckedChanged);
            cbxCellFreq.CheckedChanged += new EventHandler(cbxCellFreq_CheckedChanged);
            cbxCellLAC.CheckedChanged += new EventHandler(cbxCellLAC_CheckedChanged);
            cbxCellFreqList.CheckedChanged += new EventHandler(cbxCellFreqList_CheckedChanged);
            cbxCellName.CheckedChanged += new EventHandler(cbxCellName_CheckedChanged);
            btnFont.Click += new EventHandler(btnFont_Click);
            radioBtnDefault.CheckedChanged += new EventHandler(radioBtnDefault_CheckedChanged);
            radioBtnMod3.CheckedChanged += new EventHandler(radioBtnMod3_CheckedChanged);
            radioBtnMod6.CheckedChanged += new EventHandler(radioBtnMod6_CheckedChanged);
            colorLable.ColorChanged += new EventHandler(colorLabel_ColorChanged);
        }

        FontDialog fontDialog = new FontDialog();
        void btnFont_Click(object sender, EventArgs e)
        {
            fontDialog.Font = layer.FontCellLabel;
            if (fontDialog.ShowDialog(this) == DialogResult.OK)
            {
                layer.FontCellLabel = fontDialog.Font;
            }
        }

        void cbxCellFreqList_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCellFreqs = cbxCellFreqList.Checked;
        }

        void cbxCellName_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCellName = cbxCellName.Checked;
        }

        void cbxCellLAC_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCellTAC = cbxCellLAC.Checked;
        }

        void cbxCellFreq_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCellEARFCN = cbxCellFreq.Checked;
        }

        void cbxCellDes_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCellDes = cbxCellDes.Checked;
        }

        void cbxCellCPI_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCellPCI = cbxCellCPI.Checked;
        }

        void cbxCellCode_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCellCode = cbxCellCode.Checked;
        }

        void cbxCellCI_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCellECI = cbxCellCI.Checked;
        }

        void cbxDrawCellLabel_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCellLabel = cbxDrawCellLabel.Checked;
        }

        void TrackBarOpacity_ValueChanged(object sender, EventArgs e)
        {
            layer.ColorCell = Color.FromArgb(TrackBarOpacity.Value, layer.ColorCell);
            layer.ColorCellMod3_0 = Color.FromArgb(TrackBarOpacity.Value, layer.ColorCellMod3_0);
            layer.ColorCellMod3_1 = Color.FromArgb(TrackBarOpacity.Value, layer.ColorCellMod3_1);
            layer.ColorCellMod3_2 = Color.FromArgb(TrackBarOpacity.Value, layer.ColorCellMod3_2);
            layer.ColorCellMod6_0 = Color.FromArgb(TrackBarOpacity.Value, layer.ColorCellMod6_0);
            layer.ColorCellMod6_1 = Color.FromArgb(TrackBarOpacity.Value, layer.ColorCellMod6_1);
            layer.ColorCellMod6_2 = Color.FromArgb(TrackBarOpacity.Value, layer.ColorCellMod6_2);
            layer.ColorCellMod6_3 = Color.FromArgb(TrackBarOpacity.Value, layer.ColorCellMod6_3);
            layer.ColorCellMod6_4 = Color.FromArgb(TrackBarOpacity.Value, layer.ColorCellMod6_4);
            layer.ColorCellMod6_5 = Color.FromArgb(TrackBarOpacity.Value, layer.ColorCellMod6_5);
        }

        void colorCell_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorCell = Color.FromArgb(TrackBarOpacity.Value, colorCell.Color);
        }

        void colorCellMod3_0_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorCellMod3_0 = Color.FromArgb(TrackBarOpacity.Value, colorCellMod3_0.Color);
        }

        void colorCellMod3_1_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorCellMod3_1 = Color.FromArgb(TrackBarOpacity.Value, colorCellMod3_1.Color);
        }

        void colorCellMod3_2_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorCellMod3_2 = Color.FromArgb(TrackBarOpacity.Value, colorCellMod3_2.Color);
        }

        void colorCellMod6_0_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorCellMod6_0 = Color.FromArgb(TrackBarOpacity.Value, colorCellMod6_0.Color);
        }

        void colorCellMod6_1_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorCellMod6_1 = Color.FromArgb(TrackBarOpacity.Value, colorCellMod6_1.Color);
        }

        void colorCellMod6_2_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorCellMod6_2 = Color.FromArgb(TrackBarOpacity.Value, colorCellMod6_2.Color);
        }

        void colorCellMod6_3_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorCellMod6_3 = Color.FromArgb(TrackBarOpacity.Value, colorCellMod6_3.Color);
        }

        void colorCellMod6_4_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorCellMod6_4 = Color.FromArgb(TrackBarOpacity.Value, colorCellMod6_4.Color);
        }

        void colorCellMod6_5_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorCellMod6_5 = Color.FromArgb(TrackBarOpacity.Value, colorCellMod6_5.Color);
        }

        void colorA_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorCellA = Color.FromArgb(255, colorA.Color);
        }

        void colorD_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorCellD = Color.FromArgb(255, colorD.Color);
        }

        void colorE_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorCellE = Color.FromArgb(255, colorE.Color);
        }

        void colorF_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorCellF = Color.FromArgb(255, colorF.Color);
        }

        void checkBoxDisplay_CheckedChanged(object sender, EventArgs e)
        {
            layer.DrawCell = checkBoxDisplay.Checked;
        }

        private void radioBtnDefault_CheckedChanged(object sender, EventArgs e)
        {
            if (radioBtnDefault.Checked)
            {
                layer.DrawCellMode = DrawCellMode.Default;
            }
        }

        private void radioBtnMod3_CheckedChanged(object sender, EventArgs e)
        {
            if (radioBtnMod3.Checked)
            {
                layer.DrawCellMode = DrawCellMode.PCI_Mod3;
            }
        }

        private void radioBtnMod6_CheckedChanged(object sender, EventArgs e)
        {
            if (radioBtnMod6.Checked)
            {
                layer.DrawCellMode = DrawCellMode.PCI_Mod6;
            }
        }

        void colorLabel_ColorChanged(object sender, EventArgs e)
        {
            layer.ColorCellLabel = colorLable.Color;
        }
    }
}
