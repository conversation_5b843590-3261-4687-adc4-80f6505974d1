﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MapWinGIS;
using MasterCom.RAMS.Net;
using AxMapWinGIS;
using System.Windows.Forms;
using MasterCom.Util;
using System.Drawing;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.FuncLayer
{     
    public class WeakQualReasonLayer
    {
        private readonly MapForm mapForm;
        private MapOperation mop { get; set; }
        Shapefile shapeFile_points = null;
        Shapefile shapeFile_lines = null;
        private bool needWaitBox = false;
        int fldReason = 0;
        int fldLongitude = 1;
        int fldLatitude = 2;
        int fldColor = 3;
        int fldColor_line = 0;

        public WeakQualReasonLayer(MapForm mapForm, MapOperation op)
        {
            this.mapForm = mapForm;
            this.mop = op;
        }

        internal void ApplyData(List<ZTDIYWeakQualAnaByRegion.WeakQualCellInfo.reasonTestpoint> WeakQualReasonTpList,Cell cell)
        {
            if (WeakQualReasonTpList.Count==0)
            {
                return;
            }
            cellTP cellTp = new cellTP(cell, WeakQualReasonTpList);

            AxMap mapMain = mapForm.GetMapFormControl();
            if (shapeFile_points == null) //画点
            {
                shapeFile_points = new Shapefile();
                bool result = shapeFile_points.CreateNewWithShapeID("", ShpfileType.SHP_POINT);
                if (!result)
                {
                    MessageBox.Show(shapeFile_points.get_ErrorMsg(shapeFile_points.LastErrorCode));
                    return;
                }
                Field field = new Field();
                field.Name = "Reason";
                field.Type = FieldType.STRING_FIELD;
                shapeFile_points.EditInsertField(field, ref fldReason, null);
                field = new Field();
                field.Name = "Longitude";
                field.Type = FieldType.DOUBLE_FIELD;
                field.Precision = 7;
                shapeFile_points.EditInsertField(field, ref fldLongitude, null);
                field = new Field();
                field.Name = "Latitude";
                field.Type = FieldType.DOUBLE_FIELD;
                field.Precision = 7;
                shapeFile_points.EditInsertField(field, ref fldLatitude, null);
                field = new Field();
                field.Name = "Color";
                field.Type = FieldType.INTEGER_FIELD;
                shapeFile_points.EditInsertField(field, ref fldColor, null);

                shapeFile_points.DefaultDrawingOptions.PointSize = 15; //点半径
                shapeFile_points.DefaultDrawingOptions.SetDefaultPointSymbol(tkDefaultPointSymbol.dpsCircle); //点样式

                mapMain.AddLayer(shapeFile_points, true);
            }

            if (shapeFile_lines==null) //画线
            {
                shapeFile_lines = new Shapefile();
                bool result = shapeFile_lines.CreateNewWithShapeID("", ShpfileType.SHP_POLYLINE);
                if (!result)
                {
                    MessageBox.Show(shapeFile_lines.get_ErrorMsg(shapeFile_points.LastErrorCode));
                    return;
                }
                Field field = new Field();
                field.Name = "Color";
                field.Type = FieldType.INTEGER_FIELD;
                shapeFile_lines.EditInsertField(field, ref fldColor_line, null);

                shapeFile_lines.DefaultDrawingOptions.LineColor = (uint)ColorTranslator.ToOle(Color.Red);

                mapMain.AddLayer(shapeFile_lines, true);
            }

            //=========to draw
            if (WeakQualReasonTpList.Count > 20)
            {
                needWaitBox = true;
                WaitBox.CanCancel = true;
                WaitBox.Show("准备绘制...", createWeakQualReasonTpFeaturesThread, cellTp);
            }
            else
            {
                needWaitBox = false;
                createWeakQualReasonTpFeaturesThread(cellTp);
            }
        }

        public int Size { get; set; } = 100 / 2; //单元半径
        private void createWeakQualReasonTpFeaturesThread(object param)
        {
            try
            {
                int wcount = shapeFile_points.NumShapes;
                for (int del = wcount - 1; del >= 0; del--)
                {
                    shapeFile_points.EditDeleteShape(del);
                }
                int wcount2 = shapeFile_lines.NumShapes;
                for (int del = wcount2 - 1; del >= 0; del--)
                {
                    shapeFile_lines.EditDeleteShape(del);
                }

                AxMap mapMain = mapForm.GetMapFormControl();
                List<ZTDIYWeakQualAnaByRegion.WeakQualCellInfo.reasonTestpoint> wqTplist = (param as cellTP).rtps;
                int counter = 0;
                int shpIdx_pts = 0;
                int shpIndx_line = 0;
                Cell cell = (param as cellTP).cell;
                foreach (ZTDIYWeakQualAnaByRegion.WeakQualCellInfo.reasonTestpoint wqTp in wqTplist)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    counter++;
                    if (needWaitBox && counter % 20 == 0)
                    {
                        WaitBox.Text = "正在进行质差点绘制...(" + counter + "/" + wqTplist.Count + ")";
                        WaitBox.ProgressPercent = (int)(100.0 * counter / (wqTplist.Count));
                    }
                    Color color = wqTp.color;
                    uint oleColor = (uint)ColorTranslator.ToOle(color);
                    MapWinGIS.Shape spBase = new MapWinGIS.Shape();
                    spBase.Create(ShpfileType.SHP_POINT);
                    MapWinGIS.Point pt = new MapWinGIS.Point();
                    pt.x = wqTp.tp.Longitude;
                    pt.y = wqTp.tp.Latitude;
                    int j = 0;
                    spBase.InsertPoint(pt, ref j);

                    shapeFile_points.EditInsertShape(spBase, ref shpIdx_pts);
                    shapeFile_points.EditCellValue(fldReason, shpIdx_pts, wqTp.reason);
                    shapeFile_points.EditCellValue(fldLongitude, shpIdx_pts, wqTp.tp.Longitude);
                    shapeFile_points.EditCellValue(fldLatitude, shpIdx_pts, wqTp.tp.Latitude);
                    shapeFile_points.EditCellValue(fldColor, shpIdx_pts, (int)oleColor);

                    shpIdx_pts++;

                    if (cell != null)
                    {
                        int pntIdx = 0;
                        MapWinGIS.Shape shp = new MapWinGIS.Shape();
                        shp.Create(ShpfileType.SHP_POLYLINE);
                        MapWinGIS.Point pt_p = new MapWinGIS.Point();
                        pt_p.x = pt.x;
                        pt_p.y = pt.y;
                        shp.InsertPoint(pt_p, ref pntIdx);
                        MapWinGIS.Point pt_c = new MapWinGIS.Point();
                        pt_c.x = cell.Longitude;
                        pt_c.y = cell.Latitude;
                        shp.InsertPoint(pt_c, ref shpIndx_line);
                        shapeFile_lines.EditInsertShape(shp, ref shpIndx_line);
                        shapeFile_lines.EditCellValue(fldColor_line, shpIndx_line, (int)oleColor);
                        shpIndx_line++;
                    }
                }

                Dictionary<uint, int> categoryByColor = getCategoryByColor();
                //================apply categories
                for (int i = 0; i < shapeFile_points.NumShapes; i++)
                {
                    object vx = shapeFile_points.get_CellValue(fldColor, i);
                    uint color = (uint)(int)vx;
                    uint clr = color;
                    int catIndex = categoryByColor[clr];
                    shapeFile_points.set_ShapeCategory(i, catIndex);
                }
                //=======
                mapForm.SetRedrawBuffFlag();
                mapMain.Redraw();

            }
            finally
            {
                if (needWaitBox)
                {
                    System.Threading.Thread.Sleep(2000);
                    WaitBox.Close();
                }
            }
        }

        private Dictionary<uint, int> getCategoryByColor()
        {
            //====Set Color
            Dictionary<uint, int> categoryByColor = new Dictionary<uint, int>();
            //=================区域着色渲染
            for (int i = 0; i < shapeFile_points.NumShapes; i++)
            {
                object vx = shapeFile_points.get_CellValue(fldColor, i);

                uint color = (uint)(int)vx;
                if (!categoryByColor.ContainsKey(color))
                {
                    string name = shapeFile_points.Categories.Count.ToString();
                    MapWinGIS.ShapefileCategory cat = shapeFile_points.Categories.Add(name);
                    if (cat != null)
                    {
                        cat.DrawingOptions.FillColor = color;
                        categoryByColor.Add(color, shapeFile_points.Categories.Count - 1);
                    }
                }
            }

            return categoryByColor;
        }

        public void ClearData()
        {
            shapeFile_points.EditClear();
            shapeFile_lines.EditClear();
            shapeFile_points = null;
            shapeFile_lines = null;
        }
    }


    public class cellTP
    {
        public cellTP(Cell cell, List<ZTDIYWeakQualAnaByRegion.WeakQualCellInfo.reasonTestpoint> rtps)
        {
            this.cell = cell;
            this.rtps = rtps;
        }
        public Cell cell { get; set; }
        public List<ZTDIYWeakQualAnaByRegion.WeakQualCellInfo.reasonTestpoint> rtps { get; set; }
    }
}
