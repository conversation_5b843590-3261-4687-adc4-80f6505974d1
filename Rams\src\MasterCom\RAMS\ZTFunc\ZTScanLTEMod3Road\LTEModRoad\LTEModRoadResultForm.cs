﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Grid;

using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LTEModRoadResultForm : MinCloseForm
    {
        public LTEModRoadResultForm(MainModel mainModel) : base(mainModel)
        {
            InitializeComponent();
            this.DisposeWhenClose = true;
            btnFind.Click += BtnFind_Click;
            gridView1.DoubleClick += GridView_DoubleClick;
            gridView2.DoubleClick += GridView_DoubleClick;
            gridView3.DoubleClick += GridView_DoubleClick;
            miExportExcel.Click += MiExportExcel_Click;
        }

        private void GridView_DoubleClick(object sender, EventArgs e)
        {
            GridView gv = sender as GridView;
            object row = gv.GetRow(gv.GetSelectedRows()[0]);
            if (row is LTEModRoadItem)
            {
                LTEModRoadItem road = row as LTEModRoadItem;
                List<TestPoint> tps = road.TestPoints;
                if (tps.Count == 0)
                {
                    return;
                }

                MainModel.DTDataManager.Clear();
                foreach (TestPoint tp in tps)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.FireDTDataChanged(MainModel.MainForm);

                TestPoint midTp = tps[tps.Count / 2];
                MainModel.MainForm.GetMapForm().GoToView(midTp.Longitude, midTp.Latitude);

                OutlineOfRoad outRoad = new OutlineOfRoad();
                outRoad.SetPoints(tps);
                TempLayer.Instance.Draw(outRoad.Drawer);
            }
            else if (row is LTEModCellItem)
            {
                LTEModCellItem cellItem = row as LTEModCellItem;
                if (cellItem.LteCell == null)
                {
                    return;
                }

                MainModel.SelectedLTECell = cellItem.LteCell;
                MainModel.MainForm.GetMapForm().GoToView(cellItem.LteCell.Longitude, cellItem.LteCell.Latitude);
            }
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            List<NPOIRow> contents = new List<NPOIRow>();
            NPOIRow titleRow = new NPOIRow();
            foreach (GridColumn col in gridView1.VisibleColumns)
            {
                titleRow.AddCellValue(col.Caption);
            }
            foreach (GridColumn col in gridView2.VisibleColumns)
            {
                titleRow.AddCellValue(col.Caption);
            }
            foreach (GridColumn col in gridView3.VisibleColumns)
            {
                titleRow.AddCellValue(col.Caption);
            }
            contents.Add(titleRow);

            List<LTEModRoadItem> roadList = gridControl1.DataSource as List<LTEModRoadItem>;
            foreach (LTEModRoadItem road in roadList)
            {
                NPOIRow roadRow = new NPOIRow();
                roadRow.AddCellValue(road.SN);
                roadRow.AddCellValue(road.RoadDesc);
                roadRow.AddCellValue(road.Length);
                roadRow.AddCellValue(road.SampleCount);
                roadRow.AddCellValue(road.MainCellCount);
                roadRow.AddCellValue(road.NbCellCount);
                roadRow.AddCellValue(road.InterMainCellCount);
                roadRow.AddCellValue(road.InterNbCellCount);
                roadRow.AddCellValue(road.InterSampleCount);
                roadRow.AddCellValue(road.InterSampleRate);
                roadRow.AddCellValue(road.FileName);

                foreach (LTEModCellItem tarCell in road.InterCellList)
                {
                    NPOIRow tarRow = new NPOIRow();
                    tarRow.cellValues.AddRange(roadRow.cellValues);
                    tarRow.AddCellValue(tarCell.CellName);
                    tarRow.AddCellValue(tarCell.Tac);
                    tarRow.AddCellValue(tarCell.Eci);
                    tarRow.AddCellValue(tarCell.CellID);
                    tarRow.AddCellValue(tarCell.Earfcn);
                    tarRow.AddCellValue(tarCell.Pci);
                    tarRow.AddCellValue(tarCell.NbCellCount);
                    tarRow.AddCellValue(tarCell.InterNbCellCount);
                    tarRow.AddCellValue(tarCell.InterNbCellRate);
                    tarRow.AddCellValue(tarCell.SampleCount);
                    tarRow.AddCellValue(tarCell.InterSampleCount);
                    tarRow.AddCellValue(tarCell.InterSampleRate);
                    tarRow.AddCellValue(tarCell.RsrpAvgStr);
                    tarRow.AddCellValue(tarCell.SinrAvgStr);
                    tarRow.AddCellValue(tarCell.PdcpUlAvgStr);
                    tarRow.AddCellValue(tarCell.PdcpDlAvgStr);

                    foreach (LTEModCellItem srcCell in tarCell.InterNbCellList)
                    {
                        NPOIRow srcRow = new NPOIRow();
                        srcRow.cellValues.AddRange(tarRow.cellValues);
                        srcRow.AddCellValue(srcCell.CellName);
                        srcRow.AddCellValue(srcCell.Tac);
                        srcRow.AddCellValue(srcCell.Eci);
                        srcRow.AddCellValue(srcCell.CellID);
                        srcRow.AddCellValue(srcCell.Earfcn);
                        srcRow.AddCellValue(srcCell.Pci);
                        srcRow.AddCellValue(srcCell.SampleCount);
                        srcRow.AddCellValue(srcCell.InterSampleCount);
                        srcRow.AddCellValue(srcCell.InterSampleRate);
                        srcRow.AddCellValue(srcCell.RsrpAvgStr);
                        srcRow.AddCellValue(srcCell.SinrAvgStr);
                        srcRow.AddCellValue(srcCell.Distance);
                        contents.Add(srcRow);
                    }
                }
            }
            ExcelNPOIManager.ExportToExcel(contents);
        }

        private void BtnFind_Click(object sender, EventArgs e)
        {
            FillData();
        }

        public override void ReleaseResources()
        {
            if (this.stater != null)
            {
                stater.Clear();
            }
            TempLayer.Instance.Clear();
            MainModel.DTDataManager.Clear();
            MainModel.FireDTDataChanged(MainModel.MainForm);
        }

        public void FillData(LTEModRoadStater stater)
        {
            if (this.stater != null)
            {
                this.stater.Clear();
            }
            this.stater = stater;
            FillData();
        }

        private void FillData()
        {
            LTEModInterfereCondition cond = GetFilterCondition();
            object result = stater.GetStatResult(cond);
            gridControl1.DataSource = result;
            gridControl1.RefreshDataSource();

            MainModel.DTDataManager.Clear();
            TempLayer.Instance.Clear();
            List<LTEModRoadItem> roadList = gridControl1.DataSource as List<LTEModRoadItem>;
            foreach (LTEModRoadItem road in roadList)
            {
                foreach (TestPoint tp in road.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            MainModel.FireDTDataChanged(MainModel.MainForm);
        }

        private LTEModInterfereCondition GetFilterCondition()
        {
            LTEModInterfereCondition cond = new LTEModInterfereCondition();
            cond.Angle = (double)numAngle.Value;
            cond.Distance = (double)numDistance.Value;
            cond.InterfereRate = (double)numRate.Value / 100;
            cond.RxlevDiff = (double)numRxlevDiff.Value;
            if (chkValue0.Checked) cond.Sids.Add(0);
            if (chkValue1.Checked) cond.Sids.Add(1);
            if (chkValue2.Checked) cond.Sids.Add(2);
            return cond;
        }

        private LTEModRoadStater stater;
    }
}
