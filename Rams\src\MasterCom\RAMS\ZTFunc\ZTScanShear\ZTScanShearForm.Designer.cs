﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTScanShearForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.contextMenuStripListView = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemPushOutGridInfo = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemPushOut = new System.Windows.Forms.ToolStripMenuItem();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.listViewTotal = new BrightIdeasSoftware.ObjectListView();
            this.olvColumnIndex = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCell1 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLAC1 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCI1 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBSIC1 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSampleCount1 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRxLevMean1 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCell2 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLAC2 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCI2 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBSIC2 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSampleCount2 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRxLevMean2 = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnBCCH = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDistance = new BrightIdeasSoftware.OLVColumn();
            this.lblConditionVisible = new System.Windows.Forms.LinkLabel();
            this.pnlCondition = new System.Windows.Forms.Panel();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.numSampleCountMin = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.numDistanceMax = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.numRxLevMin = new System.Windows.Forms.NumericUpDown();
            this.label11 = new System.Windows.Forms.Label();
            this.lblConditionDisappear = new System.Windows.Forms.LinkLabel();
            this.btnOK = new System.Windows.Forms.Button();
            this.contextMenuStripListView.SuspendLayout();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal)).BeginInit();
            this.pnlCondition.SuspendLayout();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCountMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistanceMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevMin)).BeginInit();
            this.SuspendLayout();
            // 
            // contextMenuStripListView
            // 
            this.contextMenuStripListView.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemPushOutGridInfo,
            this.ToolStripMenuItemPushOut});
            this.contextMenuStripListView.Name = "contextMenuStripListView";
            this.contextMenuStripListView.Size = new System.Drawing.Size(149, 48);
            // 
            // ToolStripMenuItemPushOutGridInfo
            // 
            this.ToolStripMenuItemPushOutGridInfo.Name = "ToolStripMenuItemPushOutGridInfo";
            this.ToolStripMenuItemPushOutGridInfo.Size = new System.Drawing.Size(148, 22);
            this.ToolStripMenuItemPushOutGridInfo.Text = "栅格信息列表";
            this.ToolStripMenuItemPushOutGridInfo.Visible = false;
            this.ToolStripMenuItemPushOutGridInfo.Click += new System.EventHandler(this.ToolStripMenuItemPushOutGridInfo_Click);
            // 
            // ToolStripMenuItemPushOut
            // 
            this.ToolStripMenuItemPushOut.Name = "ToolStripMenuItemPushOut";
            this.ToolStripMenuItemPushOut.Size = new System.Drawing.Size(148, 22);
            this.ToolStripMenuItemPushOut.Text = "导出excel";
            this.ToolStripMenuItemPushOut.Click += new System.EventHandler(this.ToolStripMenuItemPushOut_Click);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.listViewTotal);
            this.groupBox2.Controls.Add(this.lblConditionVisible);
            this.groupBox2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox2.Location = new System.Drawing.Point(0, 0);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(1009, 513);
            this.groupBox2.TabIndex = 13;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "筛选条件";
            // 
            // listViewTotal
            // 
            this.listViewTotal.AllColumns.Add(this.olvColumnIndex);
            this.listViewTotal.AllColumns.Add(this.olvColumnCell1);
            this.listViewTotal.AllColumns.Add(this.olvColumnLAC1);
            this.listViewTotal.AllColumns.Add(this.olvColumnCI1);
            this.listViewTotal.AllColumns.Add(this.olvColumnBSIC1);
            this.listViewTotal.AllColumns.Add(this.olvColumnSampleCount1);
            this.listViewTotal.AllColumns.Add(this.olvColumnRxLevMean1);
            this.listViewTotal.AllColumns.Add(this.olvColumnCell2);
            this.listViewTotal.AllColumns.Add(this.olvColumnLAC2);
            this.listViewTotal.AllColumns.Add(this.olvColumnCI2);
            this.listViewTotal.AllColumns.Add(this.olvColumnBSIC2);
            this.listViewTotal.AllColumns.Add(this.olvColumnSampleCount2);
            this.listViewTotal.AllColumns.Add(this.olvColumnRxLevMean2);
            this.listViewTotal.AllColumns.Add(this.olvColumnBCCH);
            this.listViewTotal.AllColumns.Add(this.olvColumnDistance);
            this.listViewTotal.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnIndex,
            this.olvColumnCell1,
            this.olvColumnLAC1,
            this.olvColumnCI1,
            this.olvColumnBSIC1,
            this.olvColumnSampleCount1,
            this.olvColumnRxLevMean1,
            this.olvColumnCell2,
            this.olvColumnLAC2,
            this.olvColumnCI2,
            this.olvColumnBSIC2,
            this.olvColumnSampleCount2,
            this.olvColumnRxLevMean2,
            this.olvColumnBCCH,
            this.olvColumnDistance});
            this.listViewTotal.ContextMenuStrip = this.contextMenuStripListView;
            this.listViewTotal.Cursor = System.Windows.Forms.Cursors.Default;
            this.listViewTotal.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewTotal.FullRowSelect = true;
            this.listViewTotal.GridLines = true;
            this.listViewTotal.HeaderWordWrap = true;
            this.listViewTotal.Location = new System.Drawing.Point(3, 18);
            this.listViewTotal.MultiSelect = false;
            this.listViewTotal.Name = "listViewTotal";
            this.listViewTotal.ShowGroups = false;
            this.listViewTotal.Size = new System.Drawing.Size(1003, 492);
            this.listViewTotal.TabIndex = 15;
            this.listViewTotal.UseCompatibleStateImageBehavior = false;
            this.listViewTotal.View = System.Windows.Forms.View.Details;
            this.listViewTotal.DoubleClick += new System.EventHandler(this.listViewTotal_DoubleClick);
            // 
            // olvColumnIndex
            // 
            this.olvColumnIndex.HeaderFont = null;
            this.olvColumnIndex.Text = "序号";
            this.olvColumnIndex.Width = 40;
            // 
            // olvColumnCell1
            // 
            this.olvColumnCell1.AspectName = "";
            this.olvColumnCell1.HeaderFont = null;
            this.olvColumnCell1.Text = "小区一";
            this.olvColumnCell1.Width = 100;
            // 
            // olvColumnLAC1
            // 
            this.olvColumnLAC1.HeaderFont = null;
            this.olvColumnLAC1.Text = "小区一LAC";
            this.olvColumnLAC1.Width = 80;
            // 
            // olvColumnCI1
            // 
            this.olvColumnCI1.HeaderFont = null;
            this.olvColumnCI1.Text = "小区一CI";
            this.olvColumnCI1.Width = 80;
            // 
            // olvColumnBSIC1
            // 
            this.olvColumnBSIC1.AspectName = "bsicMain";
            this.olvColumnBSIC1.HeaderFont = null;
            this.olvColumnBSIC1.Text = "小区一BSIC";
            // 
            // olvColumnSampleCount1
            // 
            this.olvColumnSampleCount1.AspectName = "sampleCountMain";
            this.olvColumnSampleCount1.HeaderFont = null;
            this.olvColumnSampleCount1.Text = "小区一采样点数";
            // 
            // olvColumnRxLevMean1
            // 
            this.olvColumnRxLevMean1.AspectName = "RxLevMeanMain";
            this.olvColumnRxLevMean1.HeaderFont = null;
            this.olvColumnRxLevMean1.Text = "小区一电平";
            this.olvColumnRxLevMean1.Width = 80;
            // 
            // olvColumnCell2
            // 
            this.olvColumnCell2.AspectName = "";
            this.olvColumnCell2.HeaderFont = null;
            this.olvColumnCell2.Text = "小区二";
            this.olvColumnCell2.Width = 100;
            // 
            // olvColumnLAC2
            // 
            this.olvColumnLAC2.HeaderFont = null;
            this.olvColumnLAC2.Text = "小区二LAC";
            this.olvColumnLAC2.Width = 80;
            // 
            // olvColumnCI2
            // 
            this.olvColumnCI2.HeaderFont = null;
            this.olvColumnCI2.Text = "小区二CI";
            this.olvColumnCI2.Width = 80;
            // 
            // olvColumnBSIC2
            // 
            this.olvColumnBSIC2.AspectName = "bsicOther";
            this.olvColumnBSIC2.HeaderFont = null;
            this.olvColumnBSIC2.Text = "小区二BSIC";
            // 
            // olvColumnSampleCount2
            // 
            this.olvColumnSampleCount2.AspectName = "sampleCountOther";
            this.olvColumnSampleCount2.HeaderFont = null;
            this.olvColumnSampleCount2.Text = "小区二采样点数";
            // 
            // olvColumnRxLevMean2
            // 
            this.olvColumnRxLevMean2.AspectName = "RxLevMeanOther";
            this.olvColumnRxLevMean2.HeaderFont = null;
            this.olvColumnRxLevMean2.Text = "小区二电平";
            this.olvColumnRxLevMean2.Width = 80;
            // 
            // olvColumnBCCH
            // 
            this.olvColumnBCCH.AspectName = "bcch";
            this.olvColumnBCCH.HeaderFont = null;
            this.olvColumnBCCH.Text = "BCCH";
            // 
            // olvColumnDistance
            // 
            this.olvColumnDistance.AspectName = "Distance";
            this.olvColumnDistance.HeaderFont = null;
            this.olvColumnDistance.Text = "距离";
            // 
            // lblConditionVisible
            // 
            this.lblConditionVisible.AutoSize = true;
            this.lblConditionVisible.Location = new System.Drawing.Point(7, 0);
            this.lblConditionVisible.Name = "lblConditionVisible";
            this.lblConditionVisible.Size = new System.Drawing.Size(55, 14);
            this.lblConditionVisible.TabIndex = 14;
            this.lblConditionVisible.TabStop = true;
            this.lblConditionVisible.Text = "筛选条件";
            this.lblConditionVisible.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.lblConditionVisible_LinkClicked);
            // 
            // pnlCondition
            // 
            this.pnlCondition.BackColor = System.Drawing.SystemColors.Control;
            this.pnlCondition.Controls.Add(this.groupBox1);
            this.pnlCondition.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlCondition.Location = new System.Drawing.Point(0, 0);
            this.pnlCondition.Name = "pnlCondition";
            this.pnlCondition.Size = new System.Drawing.Size(1009, 63);
            this.pnlCondition.TabIndex = 14;
            this.pnlCondition.Visible = false;
            // 
            // groupBox1
            // 
            this.groupBox1.BackColor = System.Drawing.SystemColors.Window;
            this.groupBox1.Controls.Add(this.numSampleCountMin);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.numDistanceMax);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.numRxLevMin);
            this.groupBox1.Controls.Add(this.label11);
            this.groupBox1.Controls.Add(this.lblConditionDisappear);
            this.groupBox1.Controls.Add(this.btnOK);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(1009, 63);
            this.groupBox1.TabIndex = 11;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "筛选条件";
            // 
            // numSampleCountMin
            // 
            this.numSampleCountMin.Location = new System.Drawing.Point(318, 23);
            this.numSampleCountMin.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numSampleCountMin.Name = "numSampleCountMin";
            this.numSampleCountMin.Size = new System.Drawing.Size(70, 22);
            this.numSampleCountMin.TabIndex = 30;
            this.numSampleCountMin.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(236, 29);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(64, 14);
            this.label3.TabIndex = 29;
            this.label3.Text = "采样点数≥";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(605, 29);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(19, 14);
            this.label2.TabIndex = 28;
            this.label2.Text = "米";
            // 
            // numDistanceMax
            // 
            this.numDistanceMax.Increment = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numDistanceMax.Location = new System.Drawing.Point(527, 23);
            this.numDistanceMax.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numDistanceMax.Name = "numDistanceMax";
            this.numDistanceMax.Size = new System.Drawing.Size(71, 22);
            this.numDistanceMax.TabIndex = 27;
            this.numDistanceMax.Value = new decimal(new int[] {
            3000,
            0,
            0,
            0});
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(430, 29);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(76, 14);
            this.label1.TabIndex = 26;
            this.label1.Text = "小区间距离≤";
            // 
            // numRxLevMin
            // 
            this.numRxLevMin.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numRxLevMin.Location = new System.Drawing.Point(129, 23);
            this.numRxLevMin.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numRxLevMin.Minimum = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.numRxLevMin.Name = "numRxLevMin";
            this.numRxLevMin.Size = new System.Drawing.Size(70, 22);
            this.numRxLevMin.TabIndex = 25;
            this.numRxLevMin.Value = new decimal(new int[] {
            80,
            0,
            0,
            -2147483648});
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(47, 29);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(64, 14);
            this.label11.TabIndex = 24;
            this.label11.Text = "小区电平≥";
            // 
            // lblConditionDisappear
            // 
            this.lblConditionDisappear.AutoSize = true;
            this.lblConditionDisappear.Location = new System.Drawing.Point(7, 0);
            this.lblConditionDisappear.Name = "lblConditionDisappear";
            this.lblConditionDisappear.Size = new System.Drawing.Size(55, 14);
            this.lblConditionDisappear.TabIndex = 23;
            this.lblConditionDisappear.TabStop = true;
            this.lblConditionDisappear.Text = "筛选条件";
            this.lblConditionDisappear.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.lblConditionDisappear_LinkClicked);
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(814, 23);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(94, 27);
            this.btnOK.TabIndex = 8;
            this.btnOK.Text = "立即筛选";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // ZTScanShearForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1009, 513);
            this.Controls.Add(this.pnlCondition);
            this.Controls.Add(this.groupBox2);
            this.MinimumSize = new System.Drawing.Size(787, 348);
            this.Name = "ZTScanShearForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "扫频同频错切";
            this.contextMenuStripListView.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal)).EndInit();
            this.pnlCondition.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCountMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistanceMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRxLevMin)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contextMenuStripListView;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemPushOut;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemPushOutGridInfo;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.LinkLabel lblConditionVisible;
        private System.Windows.Forms.Panel pnlCondition;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.NumericUpDown numRxLevMin;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.LinkLabel lblConditionDisappear;
        private System.Windows.Forms.Button btnOK;
        private BrightIdeasSoftware.ObjectListView listViewTotal;
        private BrightIdeasSoftware.OLVColumn olvColumnIndex;
        private BrightIdeasSoftware.OLVColumn olvColumnCell1;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC1;
        private BrightIdeasSoftware.OLVColumn olvColumnCI1;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLevMean1;
        private BrightIdeasSoftware.OLVColumn olvColumnCell2;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC2;
        private BrightIdeasSoftware.OLVColumn olvColumnCI2;
        private BrightIdeasSoftware.OLVColumn olvColumnBCCH;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLevMean2;
        private BrightIdeasSoftware.OLVColumn olvColumnSampleCount1;
        private BrightIdeasSoftware.OLVColumn olvColumnSampleCount2;
        private System.Windows.Forms.NumericUpDown numDistanceMax;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC1;
        private BrightIdeasSoftware.OLVColumn olvColumnBSIC2;
        private BrightIdeasSoftware.OLVColumn olvColumnDistance;
        private System.Windows.Forms.NumericUpDown numSampleCountMin;
        private System.Windows.Forms.Label label3;
    }
}