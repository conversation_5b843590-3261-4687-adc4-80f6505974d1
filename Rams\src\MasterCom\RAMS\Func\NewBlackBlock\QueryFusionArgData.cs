﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.NewBlackBlock
{
    public class QueryFusionArgData : DIYSQLBase
    {
        protected override string getSqlTextString()
        {
            MainDB = true;
            return string.Format(@"SELECT [lac],[ci],[mtime],[extend] FROM {0}.[dbo].[tb_args_cell_mm_{1}]
 where ({2}) and cityid={3}"
                , CellAlarmData.FusionDB
                , this.Time.ToString("yyMM")
                , CellCondition
                , this.DistrictID);
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            E_VType[] arr = new E_VType[4];
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i] = E_VType.E_VARYBIN;
            return arr;
        }

        public Event Event
        {
            get;
            set;
        }
        private DateTime time;
        public DateTime Time
        {
            get
            {
                if (Event != null)
                {
                    return Event.DateTime;
                }
                return time;
            }
            set
            {
                time = value;
            }
        }

        private int districtID;
        public int DistrictID
        {
            get
            {
                if (Event != null)
                {
                    return Event.DistrictID;
                }
                return districtID;
            }
            set
            {
                districtID = value;
            }
        }
        public string CellCondition
        {
            get;
            set;
        }

        public Dictionary<string, LTECell> CellDic
        {
            get;
            set;
        }

        public List<CellArgData> CellArgSet
        {
            get;
            set;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            if (CellArgSet == null)
            {
                CellArgSet = new List<CellArgData>();
            }
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CellArgData cellAlarm = new CellArgData();
                    int tac = package.Content.GetParamInt();
                    int eci = package.Content.GetParamInt();
                    string mtimeStr = package.Content.GetParamString();
                    DateTime mtime;
                    if (DateTime.TryParse(mtimeStr, out mtime))
                    {
                        cellAlarm.BeginTime = mtime;
                    }
                    cellAlarm.Cell = CellDic[string.Format("{0}_{1}", tac, eci)];
                    cellAlarm.FillImg(package.Content.GetParamBytes());
                    cellAlarm.DistrictId = this.DistrictID;
                    CellArgSet.Add(cellAlarm);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    break;
                }
            }
        }
    }
}
