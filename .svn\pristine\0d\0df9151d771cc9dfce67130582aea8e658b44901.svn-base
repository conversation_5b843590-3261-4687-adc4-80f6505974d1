﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.KPISheet
{
    public class KPIColumnOptions
    {
        public KPIColumnOptions()
        { }
        public KPIColumnOptions(string subTemplateName)
        {
            this.subTemplateName = subTemplateName;
        }

        public string subTemplateName
        {
            get;
            set;
        }

        public override string ToString()
        {
            return this.subTemplateName;
        }
        

        private List<string> columnsList = new List<string>();

        public List<string> ColumnsList
        {
            get { return columnsList; }
        }
        
        public Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["SubTemplateName"] = this.subTemplateName;
                List<string> displayParams = new List<string>();
                foreach (string col in this.columnsList)
                {
                    displayParams.Add(col);
                }
                paramDic["Columns"] = displayParams;
                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                this.subTemplateName = value["SubTemplateName"] as string;
                this.columnsList = new List<string>();
                List<object> list = value["Columns"] as List<object>;
                foreach (string item in list)
                {
                    this.columnsList.Add(item);
                }
            }
            
        }

        //public string ParamKey
        //{
        //    get
        //    {
        //        return string.Format("{0}", subTemplateName);
        //    }
        //}
    }
}
