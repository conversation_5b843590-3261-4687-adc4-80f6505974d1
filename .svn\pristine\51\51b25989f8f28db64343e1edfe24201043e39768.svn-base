﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func
{
    public partial class MapScaleSettingDlg : BaseDialog
    {
        public MapScaleSettingDlg()
        {
            InitializeComponent();
        }

        public new int Scale
        {
            get
            {
                return (int)numScale.Value;
            }
            set
            {
                if (value >= numScale.Minimum
                    && value <= numScale.Maximum)
                {
                    this.numScale.Value = value;
                }
            }
        }
    }
}
