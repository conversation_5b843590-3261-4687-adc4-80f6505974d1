﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LowSpeedCellDlg_LTE : BaseDialog
    {
        public LowSpeedCellDlg_LTE()
        {
            InitializeComponent();
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
        }

        public void SetCondition(LowSpeedCellCond_LTE cond)
        {
            if(cond == null)
            {
                return;
            }

            numMinSinr.Value = (decimal)cond.MinSinr;
            numMaxSinr.Value = (decimal)cond.MaxSinr;
            numMinRssi.Value = (decimal)cond.MinRsrp;
            numMaxRssi.Value = (decimal)cond.MaxRsrp;

            radioApp.Checked = cond.IsAppSpeed;
            radioPdcp.Checked = !radioApp.Checked;
            chkEmail.Checked = cond.CheckEmail;
            chkFTPDownLoad.Checked = cond.CheckFTPDownLoad;
            chkFTPUpLoad.Checked = cond.CheckFTPUpLoad;
            chkHttp.Checked = cond.CheckHTTP;

            numEmailMax.Value = (decimal)cond.EmailRateMax;
            numFTPDownLoadMax.Value = (decimal)cond.FTPDownLoadRateMax;
            numFTPUpLoadMax.Value = (decimal)cond.FTPUpLoadRateMax;
            numHTTPMax.Value = (decimal)cond.HTTPRateMax;
            numPdcpMax.Value = (decimal)cond.PdcpRateMax;

            numEmailMin.Value = (decimal)cond.EmailRateMin;
            numFTPDownLoadMin.Value = (decimal)cond.FTPDownLoadRateMin;
            numFTPUpLoadMin.Value = (decimal)cond.FTPUpLoadRateMin;
            numHTTPMin.Value = (decimal)cond.HTTPRateMin;
            numPdcpMin.Value = (decimal)cond.PdcpRateMin;

            chkSaveTestPoint.Checked = cond.IsSavePoint;
            numProblemCount.Value = (decimal)cond.MinProblemCount;
            numProblemRate.Value = (decimal)cond.MinProblemRate*100;
        }

        public LowSpeedCellCond_LTE GetCondition()
        {
            LowSpeedCellCond_LTE cond = new LowSpeedCellCond_LTE();
            cond.MinSinr = (double)numMinSinr.Value;
            cond.MaxSinr = (double)numMaxSinr.Value;
            cond.MinRsrp = (double)numMinRssi.Value;
            cond.MaxRsrp = (double)numMaxRssi.Value;
            cond.IsSavePoint = chkSaveTestPoint.Checked;

            cond.MinProblemCount = (int)numProblemCount.Value;
            cond.MinProblemRate = (double)numProblemRate.Value / 100;

            cond.IsAppSpeed = radioApp.Checked;
            cond.CheckEmail = chkEmail.Checked;
            cond.CheckFTPDownLoad = chkFTPDownLoad.Checked;
            cond.CheckFTPUpLoad = chkFTPUpLoad.Checked;
            cond.CheckHTTP = chkHttp.Checked;

            cond.EmailRateMax = (double)numEmailMax.Value;
            cond.FTPDownLoadRateMax = (double)numFTPDownLoadMax.Value;
            cond.FTPUpLoadRateMax = (double)numFTPUpLoadMax.Value;
            cond.HTTPRateMax = (double)numHTTPMax.Value;
            cond.PdcpRateMax = (double)numPdcpMax.Value;

            cond.EmailRateMin = (double)numEmailMin.Value;
            cond.FTPDownLoadRateMin = (double)numFTPDownLoadMin.Value;
            cond.FTPUpLoadRateMin = (double)numFTPUpLoadMin.Value;
            cond.HTTPRateMin = (double)numHTTPMin.Value;
            cond.PdcpRateMin = (double)numPdcpMin.Value;

            return cond;
        }

        private void BtnOK_Click(object sender , EventArgs e)
        {
            if(radioApp.Checked && 
                !(chkFTPDownLoad.Checked || chkHttp.Checked || chkEmail.Checked))
            {
                MessageBox.Show("请至少选择一种APP速率进行分析！");
                return;
            }

            if(numFTPDownLoadMin.Value > numFTPDownLoadMax.Value
               || numFTPUpLoadMin.Value > numFTPUpLoadMax.Value
               || numHTTPMin.Value > numHTTPMax.Value
               || numEmailMin.Value > numEmailMax.Value
               || numPdcpMin.Value > numPdcpMax.Value
               || numMinSinr.Value > numMaxSinr.Value
               || numMinRssi.Value > numMaxRssi.Value)
            {
                MessageBox.Show("最小值必须小于最大值" , "设置错误");
                return;
            }
            DialogResult = DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void chkFTP_CheckedChanged(object sender, EventArgs e)
        {
            numFTPDownLoadMin.Enabled = numFTPDownLoadMax.Enabled = chkFTPDownLoad.Checked;
        }

        private void chkHttp_CheckedChanged(object sender, EventArgs e)
        {
            numHTTPMin.Enabled = numHTTPMax.Enabled = chkHttp.Checked;
        }

        private void chkEmail_CheckedChanged(object sender, EventArgs e)
        {
            numEmailMin.Enabled = numEmailMax.Enabled = chkEmail.Checked;
        }

        private void radioApp_CheckedChanged(object sender, EventArgs e)
        {
            grpApp.Enabled = radioApp.Checked;
            grpPdcp.Enabled = !grpApp.Enabled;
        }

        private void chkFTPUpLoad_CheckedChanged(object sender, EventArgs e)
        {
            numFTPUpLoadMin.Enabled = numFTPUpLoadMax.Enabled = chkFTPUpLoad.Checked;
        }
    }

    public class LowSpeedCellCond_LTE
    {
        public double MinSpeed { get; set; }
        public double MaxSpeed { get; set; }
        public double MinSinr { get; set; }
        public double MaxSinr { get; set; }
        public double MinRsrp { get; set; }
        public double MaxRsrp { get; set; }
        public int MinProblemCount { get; set; }
        public double MinProblemRate { get; set; }
        public bool IsSavePoint { get; set; }
        /// <summary>
        /// 是否分析应用层速率
        /// </summary>
        public bool IsAppSpeed{ get; set; }
        public bool CheckFTPDownLoad { get; set; }
        public double FTPDownLoadRateMax { get; set; } = 5;
        public double FTPDownLoadRateMin { get; set; }
        public bool CheckFTPUpLoad { get; set; }
        public double FTPUpLoadRateMax { get; set; } = 5;
        public double FTPUpLoadRateMin { get; set; }
        public bool CheckHTTP { get; set; }
        public double HTTPRateMax { get; set; } = 5;
        public double HTTPRateMin { get; set; }
        public bool CheckEmail { get; set; }
        public double EmailRateMax { get; set; } = 2;
        public double EmailRateMin { get; set; }
        public double PdcpRateMax { get; set; } = 5;
        public double PdcpRateMin { get; set; }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tp"></param>
        /// <param name="speed">单位为Mb</param>
        /// <returns></returns>
        public bool IsValidSpeed(TestPoint tp, out double? speed)
        {
            speed = null;
            if(this.IsAppSpeed)
            {
                return getValidAppSpeed(tp, ref speed);
            }
            else
            {
                return getValidNotAppSpeed(tp, ref speed);
            }
        }

        private bool getValidAppSpeed(TestPoint tp, ref double? speed)
        {
            short? type = null;
            object obj = null;
            if (tp is LTEFddTestPoint)
            {
                type = (short?)tp["lte_fdd_APP_type"];
                obj = tp["lte_fdd_APP_Speed_Mb"];
            }
            else
            {
                type = (short?)tp["lte_APP_type"];
                obj = tp["lte_APP_Speed_Mb"];
            }
            if (type == null || obj == null)
            {
                return false;
            }
            speed = double.Parse(obj.ToString());
            if (speed < 0)
            {
                return false;
            }
            //增加判断，如果type和speed都是有效值，但不是设置中需要分析的类型，则返回false，且speed设置为null
            //如当前type为上传，speed有值，但当前需要分析的是下载业务
            if ((type == (int)AppType.FTP_Download && !CheckFTPDownLoad)
                || (type == (int)AppType.FTP_Upload && !CheckFTPUpLoad)
                || (type == (int)AppType.Http_Download && !CheckHTTP)
                || (type == (int)AppType.Email_SMTP && !CheckEmail))
            {
                speed = null;
                return false;
            }
            return judgeAppTypeSpeed(speed, type);
        }

        private bool judgeAppTypeSpeed(double? speed, short? type)
        {
            if (type == (int)AppType.FTP_Download && CheckFTPDownLoad)
            {
                return FTPDownLoadRateMin <= speed && speed <= FTPDownLoadRateMax;
            }
            else if (type == (int)AppType.FTP_Upload && CheckFTPUpLoad)
            {
                return FTPUpLoadRateMin <= speed && speed <= FTPUpLoadRateMax;
            }
            else if (type == (int)AppType.Http_Download && CheckHTTP)
            {
                return HTTPRateMin <= speed && speed <= HTTPRateMax;
            }
            else if (type == (int)AppType.Email_SMTP && CheckEmail)
            {
                return EmailRateMin <= speed && speed <= EmailRateMax;
            }
            return false;
        }

        private bool getValidNotAppSpeed(TestPoint tp, ref double? speed)
        {
            object obj = null;
            if (tp is LTEFddTestPoint)
            {
                obj = tp["lte_fdd_PDCP_DL_Mb"];
            }
            else
            {
                obj = tp["lte_PDCP_DL_Mb"];
            }
            if (obj == null)
            {
                return false;
            }
            speed = double.Parse(obj.ToString());
            if (speed < 0)
            {
                return false;
            }
            return PdcpRateMin <= speed && speed <= PdcpRateMax;
        }
    }
}
