﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ScanSampleMultiCoverageForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraCharts.XYDiagram xyDiagram1 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series1 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel1 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel2 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.ChartTitle chartTitle1 = new DevExpress.XtraCharts.ChartTitle();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ScanSampleMultiCoverageForm));
            this.chartControlC_IAvg = new DevExpress.XtraCharts.ChartControl();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemFoldAll = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemShowAll = new System.Windows.Forms.ToolStripMenuItem();
            this.treeListViewAll = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSNAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLevelAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellNameAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBCCHAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSICAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxlevAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDistanceAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.treeListViewRel = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSNRel = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongRel = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatRel = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLevelRel = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellNameRel = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBCCHRel = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSICRel = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxlevRel = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDistanceRel = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.treeListViewAbs = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSNAbs = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLongAbs = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLatAbs = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLevelAbs = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellNameAbs = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBCCHAbs = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnBSICAbs = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxlevAbs = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDistanceAbs = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.xtraTabPage3 = new DevExpress.XtraTab.XtraTabPage();
            this.olvColumnFileNameAbs = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFileNameRel = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFileNameAll = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            ((System.ComponentModel.ISupportInitialize)(this.chartControlC_IAvg)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListViewAll)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.treeListViewRel)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.treeListViewAbs)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            this.xtraTabPage2.SuspendLayout();
            this.xtraTabPage3.SuspendLayout();
            this.SuspendLayout();
            // 
            // chartControlC_IAvg
            // 
            xyDiagram1.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram1.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControlC_IAvg.Diagram = xyDiagram1;
            this.chartControlC_IAvg.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControlC_IAvg.EmptyChartText.Text = "没有数据！";
            this.chartControlC_IAvg.Legend.Visible = false;
            this.chartControlC_IAvg.Location = new System.Drawing.Point(6, 0);
            this.chartControlC_IAvg.Name = "chartControlC_IAvg";
            sideBySideBarSeriesLabel1.LineVisible = true;
            series1.Label = sideBySideBarSeriesLabel1;
            series1.Name = "Series 1";
            this.chartControlC_IAvg.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series1};
            sideBySideBarSeriesLabel2.LineVisible = true;
            this.chartControlC_IAvg.SeriesTemplate.Label = sideBySideBarSeriesLabel2;
            this.chartControlC_IAvg.Size = new System.Drawing.Size(404, 476);
            this.chartControlC_IAvg.TabIndex = 33;
            chartTitle1.Text = "下载速率均值";
            this.chartControlC_IAvg.Titles.AddRange(new DevExpress.XtraCharts.ChartTitle[] {
            chartTitle1});
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExpandAll,
            this.ToolStripMenuItemFoldAll,
            this.tsmiExport2Xls,
            this.ToolStripMenuItemShowAll});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(137, 92);
            // 
            // ToolStripMenuItemExpandAll
            // 
            this.ToolStripMenuItemExpandAll.Name = "ToolStripMenuItemExpandAll";
            this.ToolStripMenuItemExpandAll.Size = new System.Drawing.Size(136, 22);
            this.ToolStripMenuItemExpandAll.Text = "全部展开";
            this.ToolStripMenuItemExpandAll.Click += new System.EventHandler(this.ToolStripMenuItemExpandAll_Click);
            // 
            // ToolStripMenuItemFoldAll
            // 
            this.ToolStripMenuItemFoldAll.Name = "ToolStripMenuItemFoldAll";
            this.ToolStripMenuItemFoldAll.Size = new System.Drawing.Size(136, 22);
            this.ToolStripMenuItemFoldAll.Text = "全部折叠";
            this.ToolStripMenuItemFoldAll.Click += new System.EventHandler(this.ToolStripMenuItemFoldAll_Click);
            // 
            // tsmiExport2Xls
            // 
            this.tsmiExport2Xls.Name = "tsmiExport2Xls";
            this.tsmiExport2Xls.Size = new System.Drawing.Size(136, 22);
            this.tsmiExport2Xls.Text = "导出Excel";
            this.tsmiExport2Xls.Click += new System.EventHandler(this.tsmiExport2Xls_Click);
            // 
            // ToolStripMenuItemShowAll
            // 
            this.ToolStripMenuItemShowAll.Name = "ToolStripMenuItemShowAll";
            this.ToolStripMenuItemShowAll.Size = new System.Drawing.Size(136, 22);
            this.ToolStripMenuItemShowAll.Text = "显示全部点";
            this.ToolStripMenuItemShowAll.Click += new System.EventHandler(this.ToolStripMenuItemShowAll_Click);
            // 
            // treeListViewAll
            // 
            this.treeListViewAll.AllColumns.Add(this.olvColumnSNAll);
            this.treeListViewAll.AllColumns.Add(this.olvColumnLongAll);
            this.treeListViewAll.AllColumns.Add(this.olvColumnLatAll);
            this.treeListViewAll.AllColumns.Add(this.olvColumnLevelAll);
            this.treeListViewAll.AllColumns.Add(this.olvColumnCellNameAll);
            this.treeListViewAll.AllColumns.Add(this.olvColumnBCCHAll);
            this.treeListViewAll.AllColumns.Add(this.olvColumnBSICAll);
            this.treeListViewAll.AllColumns.Add(this.olvColumnRxlevAll);
            this.treeListViewAll.AllColumns.Add(this.olvColumnDistanceAll);
            this.treeListViewAll.AllColumns.Add(this.olvColumnFileNameAll);
            this.treeListViewAll.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSNAll,
            this.olvColumnLongAll,
            this.olvColumnLatAll,
            this.olvColumnLevelAll,
            this.olvColumnCellNameAll,
            this.olvColumnBCCHAll,
            this.olvColumnBSICAll,
            this.olvColumnRxlevAll,
            this.olvColumnDistanceAll,
            this.olvColumnFileNameAll});
            this.treeListViewAll.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListViewAll.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListViewAll.FullRowSelect = true;
            this.treeListViewAll.GridLines = true;
            this.treeListViewAll.Location = new System.Drawing.Point(0, 0);
            this.treeListViewAll.MultiSelect = false;
            this.treeListViewAll.Name = "treeListViewAll";
            this.treeListViewAll.OwnerDraw = true;
            this.treeListViewAll.ShowGroups = false;
            this.treeListViewAll.Size = new System.Drawing.Size(787, 270);
            this.treeListViewAll.TabIndex = 4;
            this.treeListViewAll.UseCompatibleStateImageBehavior = false;
            this.treeListViewAll.View = System.Windows.Forms.View.Details;
            this.treeListViewAll.VirtualMode = true;
            this.treeListViewAll.SelectedIndexChanged += new System.EventHandler(this.treeListViewAll_SelectedIndexChanged);
            // 
            // olvColumnSNAll
            // 
            this.olvColumnSNAll.HeaderFont = null;
            this.olvColumnSNAll.Text = "序号";
            // 
            // olvColumnLongAll
            // 
            this.olvColumnLongAll.HeaderFont = null;
            this.olvColumnLongAll.Text = "经度";
            this.olvColumnLongAll.Width = 118;
            // 
            // olvColumnLatAll
            // 
            this.olvColumnLatAll.HeaderFont = null;
            this.olvColumnLatAll.Text = "纬度";
            this.olvColumnLatAll.Width = 133;
            // 
            // olvColumnLevelAll
            // 
            this.olvColumnLevelAll.HeaderFont = null;
            this.olvColumnLevelAll.Text = "重叠覆盖度";
            this.olvColumnLevelAll.Width = 77;
            // 
            // olvColumnCellNameAll
            // 
            this.olvColumnCellNameAll.HeaderFont = null;
            this.olvColumnCellNameAll.Text = "小区名";
            this.olvColumnCellNameAll.Width = 80;
            // 
            // olvColumnBCCHAll
            // 
            this.olvColumnBCCHAll.HeaderFont = null;
            this.olvColumnBCCHAll.Text = "BCCH";
            // 
            // olvColumnBSICAll
            // 
            this.olvColumnBSICAll.HeaderFont = null;
            this.olvColumnBSICAll.Text = "BSIC";
            // 
            // olvColumnRxlevAll
            // 
            this.olvColumnRxlevAll.HeaderFont = null;
            this.olvColumnRxlevAll.Text = "场强";
            // 
            // olvColumnDistanceAll
            // 
            this.olvColumnDistanceAll.HeaderFont = null;
            this.olvColumnDistanceAll.Text = "距离(米)";
            this.olvColumnDistanceAll.Width = 74;
            // 
            // treeListViewRel
            // 
            this.treeListViewRel.AllColumns.Add(this.olvColumnSNRel);
            this.treeListViewRel.AllColumns.Add(this.olvColumnLongRel);
            this.treeListViewRel.AllColumns.Add(this.olvColumnLatRel);
            this.treeListViewRel.AllColumns.Add(this.olvColumnLevelRel);
            this.treeListViewRel.AllColumns.Add(this.olvColumnCellNameRel);
            this.treeListViewRel.AllColumns.Add(this.olvColumnBCCHRel);
            this.treeListViewRel.AllColumns.Add(this.olvColumnBSICRel);
            this.treeListViewRel.AllColumns.Add(this.olvColumnRxlevRel);
            this.treeListViewRel.AllColumns.Add(this.olvColumnDistanceRel);
            this.treeListViewRel.AllColumns.Add(this.olvColumnFileNameRel);
            this.treeListViewRel.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSNRel,
            this.olvColumnLongRel,
            this.olvColumnLatRel,
            this.olvColumnLevelRel,
            this.olvColumnCellNameRel,
            this.olvColumnBCCHRel,
            this.olvColumnBSICRel,
            this.olvColumnRxlevRel,
            this.olvColumnDistanceRel,
            this.olvColumnFileNameRel});
            this.treeListViewRel.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListViewRel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListViewRel.FullRowSelect = true;
            this.treeListViewRel.GridLines = true;
            this.treeListViewRel.Location = new System.Drawing.Point(0, 0);
            this.treeListViewRel.MultiSelect = false;
            this.treeListViewRel.Name = "treeListViewRel";
            this.treeListViewRel.OwnerDraw = true;
            this.treeListViewRel.ShowGroups = false;
            this.treeListViewRel.Size = new System.Drawing.Size(787, 270);
            this.treeListViewRel.TabIndex = 4;
            this.treeListViewRel.UseCompatibleStateImageBehavior = false;
            this.treeListViewRel.View = System.Windows.Forms.View.Details;
            this.treeListViewRel.VirtualMode = true;
            this.treeListViewRel.SelectedIndexChanged += new System.EventHandler(this.treeListViewRel_SelectedIndexChanged);
            // 
            // olvColumnSNRel
            // 
            this.olvColumnSNRel.HeaderFont = null;
            this.olvColumnSNRel.Text = "序号";
            // 
            // olvColumnLongRel
            // 
            this.olvColumnLongRel.HeaderFont = null;
            this.olvColumnLongRel.Text = "经度";
            this.olvColumnLongRel.Width = 102;
            // 
            // olvColumnLatRel
            // 
            this.olvColumnLatRel.HeaderFont = null;
            this.olvColumnLatRel.Text = "纬度";
            this.olvColumnLatRel.Width = 100;
            // 
            // olvColumnLevelRel
            // 
            this.olvColumnLevelRel.HeaderFont = null;
            this.olvColumnLevelRel.Text = "重叠覆盖度";
            this.olvColumnLevelRel.Width = 77;
            // 
            // olvColumnCellNameRel
            // 
            this.olvColumnCellNameRel.HeaderFont = null;
            this.olvColumnCellNameRel.Text = "小区名";
            this.olvColumnCellNameRel.Width = 80;
            // 
            // olvColumnBCCHRel
            // 
            this.olvColumnBCCHRel.HeaderFont = null;
            this.olvColumnBCCHRel.Text = "BCCH";
            // 
            // olvColumnBSICRel
            // 
            this.olvColumnBSICRel.HeaderFont = null;
            this.olvColumnBSICRel.Text = "BSIC";
            // 
            // olvColumnRxlevRel
            // 
            this.olvColumnRxlevRel.HeaderFont = null;
            this.olvColumnRxlevRel.Text = "场强";
            // 
            // olvColumnDistanceRel
            // 
            this.olvColumnDistanceRel.HeaderFont = null;
            this.olvColumnDistanceRel.Text = "距离(米)";
            this.olvColumnDistanceRel.Width = 79;
            // 
            // treeListViewAbs
            // 
            this.treeListViewAbs.AllColumns.Add(this.olvColumnSNAbs);
            this.treeListViewAbs.AllColumns.Add(this.olvColumnLongAbs);
            this.treeListViewAbs.AllColumns.Add(this.olvColumnLatAbs);
            this.treeListViewAbs.AllColumns.Add(this.olvColumnLevelAbs);
            this.treeListViewAbs.AllColumns.Add(this.olvColumnCellNameAbs);
            this.treeListViewAbs.AllColumns.Add(this.olvColumnBCCHAbs);
            this.treeListViewAbs.AllColumns.Add(this.olvColumnBSICAbs);
            this.treeListViewAbs.AllColumns.Add(this.olvColumnRxlevAbs);
            this.treeListViewAbs.AllColumns.Add(this.olvColumnDistanceAbs);
            this.treeListViewAbs.AllColumns.Add(this.olvColumnFileNameAbs);
            this.treeListViewAbs.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSNAbs,
            this.olvColumnLongAbs,
            this.olvColumnLatAbs,
            this.olvColumnLevelAbs,
            this.olvColumnCellNameAbs,
            this.olvColumnBCCHAbs,
            this.olvColumnBSICAbs,
            this.olvColumnRxlevAbs,
            this.olvColumnDistanceAbs,
            this.olvColumnFileNameAbs});
            this.treeListViewAbs.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListViewAbs.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListViewAbs.FullRowSelect = true;
            this.treeListViewAbs.GridLines = true;
            this.treeListViewAbs.Location = new System.Drawing.Point(0, 0);
            this.treeListViewAbs.MultiSelect = false;
            this.treeListViewAbs.Name = "treeListViewAbs";
            this.treeListViewAbs.OwnerDraw = true;
            this.treeListViewAbs.ShowGroups = false;
            this.treeListViewAbs.Size = new System.Drawing.Size(787, 270);
            this.treeListViewAbs.TabIndex = 3;
            this.treeListViewAbs.UseCompatibleStateImageBehavior = false;
            this.treeListViewAbs.View = System.Windows.Forms.View.Details;
            this.treeListViewAbs.VirtualMode = true;
            this.treeListViewAbs.SelectedIndexChanged += new System.EventHandler(this.treeListViewAbs_SelectedIndexChanged);
            // 
            // olvColumnSNAbs
            // 
            this.olvColumnSNAbs.HeaderFont = null;
            this.olvColumnSNAbs.Text = "序号";
            // 
            // olvColumnLongAbs
            // 
            this.olvColumnLongAbs.HeaderFont = null;
            this.olvColumnLongAbs.Text = "经度";
            this.olvColumnLongAbs.Width = 120;
            // 
            // olvColumnLatAbs
            // 
            this.olvColumnLatAbs.HeaderFont = null;
            this.olvColumnLatAbs.Text = "纬度";
            this.olvColumnLatAbs.Width = 107;
            // 
            // olvColumnLevelAbs
            // 
            this.olvColumnLevelAbs.HeaderFont = null;
            this.olvColumnLevelAbs.Text = "重叠覆盖度";
            this.olvColumnLevelAbs.Width = 77;
            // 
            // olvColumnCellNameAbs
            // 
            this.olvColumnCellNameAbs.HeaderFont = null;
            this.olvColumnCellNameAbs.Text = "小区名";
            this.olvColumnCellNameAbs.Width = 80;
            // 
            // olvColumnBCCHAbs
            // 
            this.olvColumnBCCHAbs.HeaderFont = null;
            this.olvColumnBCCHAbs.Text = "BCCH";
            // 
            // olvColumnBSICAbs
            // 
            this.olvColumnBSICAbs.HeaderFont = null;
            this.olvColumnBSICAbs.Text = "BSIC";
            // 
            // olvColumnRxlevAbs
            // 
            this.olvColumnRxlevAbs.HeaderFont = null;
            this.olvColumnRxlevAbs.Text = "场强";
            // 
            // olvColumnDistanceAbs
            // 
            this.olvColumnDistanceAbs.HeaderFont = null;
            this.olvColumnDistanceAbs.Text = "距离(米)";
            this.olvColumnDistanceAbs.Width = 72;
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(794, 300);
            this.xtraTabControl1.TabIndex = 20;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2,
            this.xtraTabPage3});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.treeListViewAbs);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(787, 270);
            this.xtraTabPage1.Text = "绝对重叠覆盖";
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.treeListViewRel);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(787, 270);
            this.xtraTabPage2.Text = "相对重叠覆盖";
            // 
            // xtraTabPage3
            // 
            this.xtraTabPage3.Controls.Add(this.treeListViewAll);
            this.xtraTabPage3.Name = "xtraTabPage3";
            this.xtraTabPage3.Size = new System.Drawing.Size(787, 270);
            this.xtraTabPage3.Text = "综合重叠覆盖";
            // 
            // olvColumnFileNameAbs
            // 
            this.olvColumnFileNameAbs.HeaderFont = null;
            this.olvColumnFileNameAbs.Text = "文件名";
            // 
            // olvColumnFileNameRel
            // 
            this.olvColumnFileNameRel.HeaderFont = null;
            this.olvColumnFileNameRel.Text = "文件名";
            // 
            // olvColumnFileNameAll
            // 
            this.olvColumnFileNameAll.HeaderFont = null;
            this.olvColumnFileNameAll.Text = "文件名";
            // 
            // ScanSampleMultiCoverageForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(794, 300);
            this.ContextMenuStrip = this.contextMenuStrip;
            this.Controls.Add(this.xtraTabControl1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ScanSampleMultiCoverageForm";
            this.Text = "采样点重叠覆盖度";
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControlC_IAvg)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.treeListViewAll)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.treeListViewRel)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.treeListViewAbs)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            this.xtraTabPage2.ResumeLayout(false);
            this.xtraTabPage3.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraCharts.ChartControl chartControlC_IAvg;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem tsmiExport2Xls;
        private BrightIdeasSoftware.TreeListView treeListViewAbs;
        private BrightIdeasSoftware.OLVColumn olvColumnSNAbs;
        private BrightIdeasSoftware.OLVColumn olvColumnLevelAbs;
        private BrightIdeasSoftware.OLVColumn olvColumnLongAbs;
        private BrightIdeasSoftware.OLVColumn olvColumnLatAbs;
        private BrightIdeasSoftware.OLVColumn olvColumnCellNameAbs;
        private BrightIdeasSoftware.OLVColumn olvColumnBCCHAbs;
        private BrightIdeasSoftware.OLVColumn olvColumnBSICAbs;
        private BrightIdeasSoftware.OLVColumn olvColumnDistanceAbs;
        private BrightIdeasSoftware.TreeListView treeListViewRel;
        private BrightIdeasSoftware.OLVColumn olvColumnSNRel;
        private BrightIdeasSoftware.OLVColumn olvColumnLevelRel;
        private BrightIdeasSoftware.OLVColumn olvColumnLongRel;
        private BrightIdeasSoftware.OLVColumn olvColumnLatRel;
        private BrightIdeasSoftware.OLVColumn olvColumnCellNameRel;
        private BrightIdeasSoftware.OLVColumn olvColumnBCCHRel;
        private BrightIdeasSoftware.OLVColumn olvColumnBSICRel;
        private BrightIdeasSoftware.OLVColumn olvColumnDistanceRel;
        private BrightIdeasSoftware.TreeListView treeListViewAll;
        private BrightIdeasSoftware.OLVColumn olvColumnSNAll;
        private BrightIdeasSoftware.OLVColumn olvColumnLevelAll;
        private BrightIdeasSoftware.OLVColumn olvColumnLongAll;
        private BrightIdeasSoftware.OLVColumn olvColumnLatAll;
        private BrightIdeasSoftware.OLVColumn olvColumnCellNameAll;
        private BrightIdeasSoftware.OLVColumn olvColumnBCCHAll;
        private BrightIdeasSoftware.OLVColumn olvColumnBSICAll;
        private BrightIdeasSoftware.OLVColumn olvColumnDistanceAll;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage3;
        private BrightIdeasSoftware.OLVColumn olvColumnRxlevAbs;
        private BrightIdeasSoftware.OLVColumn olvColumnRxlevAll;
        private BrightIdeasSoftware.OLVColumn olvColumnRxlevRel;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExpandAll;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemFoldAll;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemShowAll;
        private BrightIdeasSoftware.OLVColumn olvColumnFileNameAbs;
        private BrightIdeasSoftware.OLVColumn olvColumnFileNameAll;
        private BrightIdeasSoftware.OLVColumn olvColumnFileNameRel;
    }
}