﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func.OwnSampleAnalyse;
using MasterCom.RAMS.Model.Interface;
using System.Xml;
using MasterCom.RAMS.GeneralFuncDef.OwnResultDef;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.Func
{
    public partial class OwnResultFuncEditorDlg : BaseFormStyle
    {
        CryptionData cdata = new CryptionData();
        public OwnResultFuncEditorDlg()
        {
            InitializeComponent();
        }
        public void FillResultSetting(ResultDefine resDef)
        {
            if(resDef!=null)
            {
                tbxFuncName.Text = resDef.funcName;
                tbxDesc.Text = resDef.desc;
                tbxCodeInput.Text = cdata.DecryptionStringdata(resDef.codeString);
            }
            else
            {
                tbxFuncName.Text = "";
                tbxDesc.Text = "";
                tbxCodeInput.Text = "";
            }
        }

        private void btnApplyCommand_Click(object sender, EventArgs e)
        {
            checkInputValid();
                
        }
        private bool checkInputValid()
        {
            ResultDefine commander = new ResultDefine();
            commander.funcName = tbxFuncName.Text.Trim();
            commander.desc = tbxDesc.Text.Trim();
            commander.codeString = cdata.EncryptionStringData(tbxCodeInput.Text);
            commander._classReady = false;
            commander._hasError = false;
            string retStringErr = commander.initFuncClass_TestPoint();
            if (!commander._classReady)
            {
                XtraMessageBox.Show("Check Error" + retStringErr);
                return false;
            }
            return true;
        }

        internal ResultDefine GetResultDefineItem()
        {
            ResultDefine ret = new ResultDefine();
            ret.funcName = tbxFuncName.Text.Trim();
            ret.desc = tbxDesc.Text.Trim();
            ret.codeString = cdata.EncryptionStringData(tbxCodeInput.Text);
            return ret;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if(checkInputValid())
            {
                this.DialogResult = DialogResult.OK;
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }


        
    }
}