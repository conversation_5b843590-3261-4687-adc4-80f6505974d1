﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class FileProjectRecordSetForm : BaseDialog
    {
        public FileProjectRecordSetForm()
        {
            InitializeComponent();

            btnCancel.Click += BtnCancel_Click;
            btnOK.Click += BtnOK_Click;

            pickerSTime.Value = DateTime.Now.Date.AddDays(1 - DateTime.Now.Date.Day);
            pickerETime.Value = pickerSTime.Value.AddMonths(1).AddDays(-1);
        }

        public TimePeriod GetCondition()
        {
            TimePeriod tp = new TimePeriod(pickerSTime.Value, pickerETime.Value.AddDays(1).AddSeconds(-1));
            return tp;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (pickerSTime.Value > pickerETime.Value)
            {
                MessageBox.Show("开始时间不能大于结束时间", this.Text, MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = System.Windows.Forms.DialogResult.None;
                return;
            }
            DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }
    }
}
