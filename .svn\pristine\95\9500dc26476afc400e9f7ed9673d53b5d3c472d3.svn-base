﻿using System.Collections.Generic;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;


namespace MasterCom.RAMS.ZTFunc
{
    public class LteURLAnaQueryByFile : DIYAnalyseFilesOneByOneByRegion
    {
        public LteURLAnaQueryByFile(MainModel mainModel)
            : base(mainModel)
        {
            this.FilterEventByRegion = false;
            this.IncludeMessage = true;
            this.IncludeTestPoint = false;
            analyzer = new LteURLAnalyzerByFile();
        }

        private readonly LteURLAnalyzerByFile analyzer;
        protected List<DTFileDataManager> fileManagers = null;
        protected Dictionary<int, string> districtName = null;
        private static LteURLAnaQueryByFile instance;

        public static LteURLAnaQueryByFile GetInstance()
        {
            if (instance == null)
            {
                instance = new LteURLAnaQueryByFile(MainModel.GetInstance());
            }
            return instance;
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                if (districtName.ContainsKey(fileMng.FileID))
                {
                    continue;
                }
                DTFileDataManager file = new DTFileDataManager(fileMng.FileID, fileMng.FileName, fileMng.ProjectType, fileMng.TestType,
                    fileMng.CarrierType, fileMng.LogTable, fileMng.SampleTableName, fileMng.ServiceType, fileMng.MoMtFlag);
                addFileDTDatas(fileMng, file);
                foreach (FileInfo fileInfo in condition.FileInfos)
                {
                    if (fileMng.FileID == fileInfo.ID)
                    {
                        districtName.Add(fileMng.FileID, fileInfo.DistrictName);
                        break;
                    }
                }
                fileManagers.Add(file);
            }
        }

        private void addFileDTDatas(DTFileDataManager fileMng, DTFileDataManager file)
        {
            foreach (Event evt in fileMng.Events)
            {
                if (analyzer.IsInHttpID(evt.ID)
                    || analyzer.IsInVideoID(evt.ID)
                    || analyzer.IsInDownloadID(evt.ID))
                {
                    file.Add(evt);
                }
            }
            foreach (MasterCom.RAMS.Model.Message msg in fileMng.Messages)
            {
                if (msg.ID == (int)LteURLCheckMsg.HttpPageRequest
                    || msg.ID == (int)LteURLCheckMsg.HttpDownloadBegin
                    || msg.ID == (int)LteURLCheckMsg.VideoPlayRequest)
                {
                    file.Add(msg);
                }
            }
        }

        protected override void getResultsAfterQuery()
        {
            analyzer.Analyze(fileManagers, districtName);
        }

        protected override void fireShowForm()
        {
            LteURLAnaForm resultForm =  MainModel.CreateResultForm(typeof(LteURLAnaForm)) as LteURLAnaForm;
            resultForm.FillData(analyzer.GetResult(), false);
            resultForm.Visible = true;
            resultForm.BringToFront();
        }

        protected override void getReadyBeforeQuery()
        {
            if (fileManagers == null)
            {
                fileManagers = new List<DTFileDataManager>();
            }
            else
            {
                fileManagers.Clear();
            }
            if (districtName == null)
            {
                districtName = new Dictionary<int, string>();
            }
            else
            {
                districtName.Clear();
            }
        }

        public override string Name
        {
            get { return "URL统计分析(按文件)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22059, this.Name);
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

    }

    public class LteURLAnaQueryByFile_FDD : LteURLAnaQueryByFile
    {
        private static LteURLAnaQueryByFile_FDD instance = null;
        protected static readonly object lockObj = new object();
        public static new LteURLAnaQueryByFile_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteURLAnaQueryByFile_FDD(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }
        public LteURLAnaQueryByFile_FDD(MainModel mainModel)
            : base(mainModel)
        {
            
        }
        public override string Name
        {
            get { return "URL统计分析LTE_FDD(按文件)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26052, this.Name);
        }
    }
}
