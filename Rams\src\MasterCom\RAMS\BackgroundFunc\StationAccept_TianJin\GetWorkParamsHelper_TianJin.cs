﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc
{
    class GetWorkParamsHelper_TianJin : GetWorkParamsHelper<GetWorkParamsHelper_TianJin>
    {
        protected override StationAcceptWorkParams getWorkParams()
        {
            return new StationAcceptWorkParams_TianJin();
        }

        protected override void setWorkParams(System.Data.DataRow row, StationAcceptWorkParams workParams)
        {
            //根据工参模板读取
            CellWorkParam_TianJin info = new CellWorkParam_TianJin();
            info.FillDataByExcel(row);
            StationAcceptWorkParams_TianJin workParamSum = (StationAcceptWorkParams_TianJin)workParams;
            if (!workParamSum.WorkParamSumDic.ContainsKey(info.DistrictName))
            {
                workParamSum.WorkParamSumDic[info.DistrictName] = new Dictionary<int, BtsWorkParam_TianJin>();
            }
            if (!workParamSum.WorkParamSumDic[info.DistrictName].ContainsKey(info.ENodeBID))
            {
                workParamSum.WorkParamSumDic[info.DistrictName][info.ENodeBID] = new BtsWorkParam_TianJin(info);
            }
            workParamSum.WorkParamSumDic[info.DistrictName][info.ENodeBID].AddCellParamsInfo(info);
        }

        protected override bool upLoadWorkParams(StationAcceptWorkParams workParams, StationAcceptCondition condition)
        {
            UpLoadWorkParams_TianJin upLoadParams = new UpLoadWorkParams_TianJin(((StationAcceptWorkParams_TianJin)workParams).WorkParamSumDic);
            upLoadParams.Query();
            return upLoadParams.SaveInDBSuccess;
        }

        protected override StationAcceptWorkParams readWorkParamsFromDB(StationAcceptCondition condition)
        {
            WorkParamsQuery_TianJin query = new WorkParamsQuery_TianJin((StationAcceptCondition_TJ)condition);
            query.Query();
            StationAcceptWorkParams_TianJin workParams = new StationAcceptWorkParams_TianJin
            {
                WorkParamSumDic = query.WorkParamSumDic
            };
            return workParams;
        }
    }

    public class StationAcceptCondition_TJ: StationAcceptCondition
    {
        public enum StationAcceptType
        {
            //仅对未单验的工参进行单验
            NotAccept = 0,
            //仅对单验失败的工参进行单验
            FailedAccept = 1,
            //对未单验和单验失败的工参进行单验
            NotAndFailedAccept = 2
        }
        
        public StationAcceptType AcceptType { get; set; }

        public override Dictionary<string, object> Params
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>
                {
                    ["ExcelPath"] = this.ExcelPath,
                    ["FilePath"] = this.FilePath,
                    ["AcceptType"] = (int)AcceptType
                };
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                setParam(param);
            }
        }

        protected override void setParam(Dictionary<string, object> param)
        {
            ExcelPath = getValidValue(param, "ExcelPath", "");
            FilePath = getValidValue(param, "FilePath", "");
            AcceptType = (StationAcceptType)getValidValue(param, "AcceptType", 0);
        }
    }

    public class StationAcceptWorkParams_TianJin : StationAcceptWorkParams
    {
        public Dictionary<string, Dictionary<int, BtsWorkParam_TianJin>> WorkParamSumDic { get; set; } = new Dictionary<string, Dictionary<int, BtsWorkParam_TianJin>>();

        public void setWorkParam(Dictionary<string, Dictionary<int, BtsAcceptWorkParamBase<int>>> workParams)
        {
            foreach (var districtWorkParams in WorkParamSumDic)
            {
                Dictionary<int, BtsAcceptWorkParamBase<int>> curBtsWorkParams = new Dictionary<int, BtsAcceptWorkParamBase<int>>();
                workParams.Add(districtWorkParams.Key, curBtsWorkParams);
                foreach (var btsWorkParams in districtWorkParams.Value)
                {
                    curBtsWorkParams.Add(btsWorkParams.Key, btsWorkParams.Value);
                }
            }
        }
    }
    
    public enum AcceptType
    {
        //需要进行单验
        NeedAccept = -1,
        //单验不通过
        FailedAccept = 0,
        //单验通过
        SuccessAccept = 1
    }

    public class BtsWorkParam_TianJin : BtsAcceptWorkParamBase<int>
    {
        public BtsWorkParam_TianJin(CellWorkParam_TianJin cellParam)
            : base(cellParam)
        {
            this.Longitude = cellParam.Longitude;
            this.Latitude = cellParam.Latitude;

            Address = cellParam.Address;
            CompanyName = cellParam.CompanyName;
        }

        public LTEBTS Bts { get; set; }
        public string Address { get; set; }
        public string CompanyName { get; set; }
        public AcceptType WorkParamAcceptType { get; set; } = AcceptType.NeedAccept;

        public string DateTimeDes { get; set; }
        public string DateDes
        {
            get
            {
                DateTime time;
                if (!string.IsNullOrEmpty(DateTimeDes) && DateTime.TryParse(DateTimeDes.Trim(), out time))
                {
                    return time.ToString("yyyyMMdd");
                }
                return string.Empty;
            }
        }

        public void AddCellParamsInfo(CellWorkParam_TianJin info)
        {
            CellWorkParamDic[info.CellID] = info;
        }

        #region 处理工参
        /// <summary>
        /// 是否是从库里读取的待单验工参
        /// </summary>
        private bool isNewAdd;
        /// <summary>
        /// 待单验站点对应缓存的工参小区
        /// </summary>
        private List<LTECell> oldCells;
        #region 添加单验工参
        public override void LoadCurBtsWorkParam(CellManager cellManager)
        {
            //加载单验工参
            CellWorkParam_TianJin cellParam = CellWorkParams[0] as CellWorkParam_TianJin;
            int eci = cellParam.ENodeBID * 256 + cellParam.CellID;
            oldCells = cellManager.GetLTECellsByECI(eci);
            removeCell(oldCells, cellManager);
            LTEBTS bts = null;
            isNewAdd = addCellInfoToCellManager(ref bts, cellManager);
            Bts = bts;
        }

        /// <summary>
        /// 移除该小区的旧工参
        /// </summary>
        /// <param name="cells"></param>
        private void removeCell(List<LTECell> cells, CellManager cellManager)
        {
            if (cells == null)
            {
                return;
            }
            cells = new List<LTECell>(cells);
            foreach (LTECell oldCell in cells)
            {
                cellManager.Remove(oldCell.BelongBTS);
                foreach (LTECell lteCell in oldCell.BelongBTS.Cells)
                {
                    cellManager.Remove(lteCell);
                    foreach (LTEAntenna antenna in lteCell.Antennas)
                    {
                        cellManager.Remove(antenna);
                    }
                }
            }
        }

        /// <summary>
        /// 添加上传的工参
        /// </summary>
        /// <param name="btsParam"></param>
        /// <param name="bts"></param>
        /// <returns></returns>
        private bool addCellInfoToCellManager(ref LTEBTS bts, CellManager cellManager)
        {
            foreach (CellAcceptWorkParamBase info in CellWorkParams)
            {
                CellWorkParam_TianJin cellInfo = (CellWorkParam_TianJin)info;
                LTECell cell = CellManager.GetInstance().GetLTECellByECI(DateTime.Now, cellInfo.Eci);
                if (cell != null)
                {
                    bts = cell.BelongBTS;
                    return false;
                }
            }

            bts = new LTEBTS();
            int snapShotId = -1;

            #region 暂时动态添加工参到CellManager，稍后移除
            bts.Fill(snapShotId, 0, 2147483647);
            bts.Name = BtsNameFull;
            bts.BTSID = ENodeBID;
            bts.Type = IsOutDoor ? LTEBTSType.Outdoor : LTEBTSType.Indoor;

            foreach (CellAcceptWorkParamBase info in CellWorkParams)
            {
                CellWorkParam_TianJin cellParamInfo = (CellWorkParam_TianJin)info;
                bts.Longitude = cellParamInfo.Longitude;
                bts.Latitude = cellParamInfo.Latitude;

                snapShotId--;
                LTECell cell = new LTECell();
                cell.Fill(snapShotId, 0, 2147483647);
                cell.BelongBTS = bts;
                cell.Name = cellParamInfo.CellNameFull;
                cell.TAC = cellParamInfo.Tac;
                cell.ECI = cellParamInfo.ENodeBID * 256 + cellParamInfo.CellID;
                cell.CellID = cellParamInfo.CellID;
                cell.PCI = cellParamInfo.Pci;
                cell.EARFCN = cellParamInfo.Earfcn;
                bts.AddCell(cell);
                cellManager.Add(cell);

                LTEAntenna antenna = new LTEAntenna();
                snapShotId--;
                antenna.Fill(snapShotId, 0, 2147483647);
                antenna.Cell = cell;
                antenna.Longitude = cellParamInfo.Longitude;
                antenna.Latitude = cellParamInfo.Latitude;
                antenna.Direction = (short)cellParamInfo.Direction;
                antenna.Downward = (short)cellParamInfo.Downward;
                antenna.Altitude = cellParamInfo.Altitude;
            }
            cellManager.Add(bts);
            #endregion

            return true;
        }
        #endregion

        #region 移除单验工参
        public override void RemoveCurBtsWorkParam(CellManager cellManager)
        {
            //移除单验工参
            removeNedAddedCell(Bts, isNewAdd, cellManager);
            reCoverCells(oldCells, cellManager);
        }

        private void removeNedAddedCell(LTEBTS bts, bool isNewAdd, CellManager cellManager)
        {
            if (isNewAdd)//将动态添加的工参移除
            {
                foreach (LTECell cell in bts.Cells)
                {
                    cellManager.Remove(cell);
                    foreach (LTEAntenna ant in cell.Antennas)
                    {
                        cellManager.Remove(ant);
                    }
                }
                cellManager.Remove(bts);
            }
        }

        /// <summary>
        /// 恢复旧工参
        /// </summary>
        /// <param name="cells"></param>
        private void reCoverCells(List<LTECell> cells, CellManager cellManager)
        {
            if (cells != null)
            {
                List<LTEBTS> lteBtsList = cellManager.GetCurrentLTEBTSs();
                cells = new List<LTECell>(cells);
                foreach (LTECell oldCell in cells)
                {
                    foreach (LTECell lteCell in oldCell.BelongBTS.Cells)
                    {
                        cellManager.Add(lteCell);
                        foreach (LTEAntenna antenna in lteCell.Antennas)
                        {
                            cellManager.Add(antenna);
                        }
                    }

                    if (!lteBtsList.Contains(oldCell.BelongBTS))
                    {
                        cellManager.Add(oldCell.BelongBTS);
                    }
                }
            }
        }
        #endregion
        #endregion
    }

    public class CellWorkParam_TianJin : CellAcceptWorkParamBase
    {
        public string Token { get { return string.Format("{0}-{1}", Tac, Eci); } }
        public DateTime UpdateTime { get; set; }
        public int Downward { get; set; }

        public string CompanyName { get; set; }
        public string Address { get; set; }
        public string AntennaType { get; set; }
        /// <summary>
        /// 电气下倾角
        /// </summary>
        public int Downtilt { get; set; }
        /// <summary>
        /// 机械下倾角
        /// </summary>
        public int MechanicalTilt { get; set; }
        /// <summary>
        /// 天线合路情况
        /// </summary>
        public string AntennaSharedDesc { get; set; }
        /// <summary>
        /// 双工方式
        /// </summary>
        public string DuplexType { get; set; }

        public int Direction { get; set; }
        public int Altitude { get; set; }
        //public int Eci { get; set; }
        //public int Earfcn { get; set; }
        //public int Pci { get; set; }
        //public double Longitude { get; set; }
        //public double Latitude { get; set; }
      
        public AcceptType WorkParamAcceptType { get; set; } = AcceptType.NeedAccept;
        public string Remark { get; set; }

        public virtual void FillDataByExcel(System.Data.DataRow row)
        {
            //报告中不用输出,却需要用到的字段
            CellNameFull = row["小区网管名称"].ToString();
            CellID = Convert.ToInt32(row["小区标识码"]);
            CoverTypeDes = row["小区覆盖类型"].ToString();

            //地市名目前写死,仅用于天津,工参Excel中没有地市信息
            DistrictName = "天津";
            BtsNameFull = row["基站名称"].ToString();
            Address = row["位置信息"].ToString();
            CompanyName = row["分公司"].ToString();
            ENodeBID = Convert.ToInt32(row["基站编号"]);

            Altitude = Convert.ToInt32(row["挂高"]);
            Longitude = Convert.ToDouble(row["经度"]);
            Latitude = Convert.ToDouble(row["纬度"]);
            Direction = Convert.ToInt32(row["方位角"]);
            MechanicalTilt = Convert.ToInt32(row["机械下倾角"]);
            AntennaType = row["天线类型"].ToString();
            Downtilt = Convert.ToInt32(row["电子下倾角"]);
            AntennaSharedDesc = row["天线合路情况"].ToString();

            Pci = Convert.ToInt32(row["PCI"]);
            Earfcn = Convert.ToInt32(row["频点"]);
            Eci = Convert.ToInt32(row["ECI"]);
            Tac = Convert.ToInt32(row["TAC"]);
            DuplexType = row["双工方式"].ToString();

            UpdateTime = DateTime.Now;
        }

        public void FillDataByDB(Package package)
        {
            DistrictName = package.Content.GetParamString();
            BtsNameFull = package.Content.GetParamString();
            ENodeBID = package.Content.GetParamInt();
            CellNameFull = package.Content.GetParamString();
            CellID = package.Content.GetParamInt();
            Tac = package.Content.GetParamInt();
            Eci = package.Content.GetParamInt();
            Earfcn = package.Content.GetParamInt();
            Pci = package.Content.GetParamInt();
            int iLongitude = package.Content.GetParamInt();
            int iLatitude = package.Content.GetParamInt();
            Longitude = (double)iLongitude / 10000000;
            Latitude = (double)iLatitude / 10000000;
            CoverTypeDes = package.Content.GetParamString();
            Address = package.Content.GetParamString();
            CompanyName = package.Content.GetParamString();

            Altitude = package.Content.GetParamInt();
            Direction = package.Content.GetParamInt();
            MechanicalTilt = package.Content.GetParamInt();
            Downtilt = package.Content.GetParamInt();
            AntennaType = package.Content.GetParamString();
            AntennaSharedDesc = package.Content.GetParamString();
            DuplexType = package.Content.GetParamString();
        }
    }

    public class UpLoadWorkParams_TianJin : DiySqlMultiNonQuery
    {
        public bool SaveInDBSuccess { get; set; } = false;
        readonly Dictionary<string, Dictionary<int, BtsWorkParam_TianJin>> cellParamInfoDic;
        public UpLoadWorkParams_TianJin(Dictionary<string, Dictionary<int, BtsWorkParam_TianJin>> cellParamInfoDic)
            : base()
        {
            MainDB = true;
            this.cellParamInfoDic = cellParamInfoDic;
        }

        protected override string getSqlTextString()
        {
            StringBuilder strb = new StringBuilder();
            //地市
            foreach (var paramInfo in cellParamInfoDic.Values)
            {
                //基站
                foreach (var btsInfo in paramInfo.Values)
                {
                    //小区
                    foreach (var info in btsInfo.CellWorkParams)
                    {
                        CellWorkParam_TianJin cellInfo = info as CellWorkParam_TianJin;

                        strb.Append(string.Format("delete from tb_btscheck_tianjin_cfg_cell where cellName = '{0}';", cellInfo.CellNameFull));
                        strb.Append(string.Format(@"insert into [tb_btscheck_tianjin_cfg_cell] ([DistrictName],[BtsName],[ENodeBID],[CellName],[CellID],[Tac],[Eci],[Earfcn],[Pci],[Longitude],[Latitude],[CoverTypeDes],[Address],[CompanyName],[Altitude],[Direction],[MechanicalTilt],[Downtilt],[AntennaType],[AntennaSharedDesc],[DuplexType],[CellAcceptType],[StationAcceptType],[UpdateTime],[Remark]) values ('{0}','{1}',{2},'{3}',{4},{5},{6},{7},{8},{9},{10},'{11}','{12}','{13}',{14},{15},{16},{17},'{18}','{19}','{20}',{21},{22},'{23}','{24}')"
                          , cellInfo.DistrictName, cellInfo.BtsNameFull, cellInfo.ENodeBID, cellInfo.CellNameFull, cellInfo.CellID, cellInfo.Tac, cellInfo.Eci
                          , cellInfo.Earfcn, cellInfo.Pci, cellInfo.Longitude * 10000000, cellInfo.Latitude * 10000000, cellInfo.CoverTypeDes, cellInfo.Address
                          , cellInfo.CompanyName, cellInfo.Altitude, cellInfo.Direction, cellInfo.MechanicalTilt, cellInfo.Downtilt, cellInfo.AntennaType
                          , cellInfo.AntennaSharedDesc, cellInfo.DuplexType, (int)cellInfo.WorkParamAcceptType, (int)btsInfo.WorkParamAcceptType
                          , cellInfo.UpdateTime, cellInfo.Remark));
                    }
                }
            }
            return strb.ToString();
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    SaveInDBSuccess = true;
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }

    public class WorkParamsQuery_TianJin : DIYSQLBase
    {
        // Dictionary<地市, Dictionary<基站编号, NBIot_BtsWorkParam>> 
        public Dictionary<string, Dictionary<int, BtsWorkParam_TianJin>> WorkParamSumDic { get; set; }
        readonly StationAcceptCondition_TJ funcSet;

        public WorkParamsQuery_TianJin(StationAcceptCondition_TJ funcSet)
        {
            MainDB = true;
            this.funcSet = funcSet;
            WorkParamSumDic = new Dictionary<string, Dictionary<int, BtsWorkParam_TianJin>>();
        }

        protected override string getSqlTextString()
        {
            string type;
            if (funcSet.AcceptType == StationAcceptCondition_TJ.StationAcceptType.NotAccept)
            {
                type = ((int)AcceptType.NeedAccept).ToString();
            }
            else if (funcSet.AcceptType == StationAcceptCondition_TJ.StationAcceptType.FailedAccept)
            {
                type = ((int)AcceptType.FailedAccept).ToString();
            }
            else if (funcSet.AcceptType == StationAcceptCondition_TJ.StationAcceptType.NotAndFailedAccept)
            {
                type = ((int)AcceptType.FailedAccept).ToString() + "," + ((int)AcceptType.NeedAccept).ToString();
            }
            else
            {
                throw (new Exception("缺少对应的单验工参类型"));
            }

            string sql = string.Format(@"select [DistrictName],[BtsName],[ENodeBID],[CellName],[CellID],[Tac],[Eci],[Earfcn],[Pci],[Longitude],[Latitude],[CoverTypeDes],[Address],[CompanyName],[Altitude],[Direction],[MechanicalTilt],[Downtilt],[AntennaType],[AntennaSharedDesc],[DuplexType] from tb_btscheck_tianjin_cfg_cell where StationAcceptType in ({0})", type);

            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            E_VType[] arr = new E_VType[21];
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i] = E_VType.E_String;
            return arr;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            WorkParamSumDic.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CellWorkParam_TianJin cellInfo = new CellWorkParam_TianJin();
                    cellInfo.FillDataByDB(package);

                    LTECell nbiotCell = CellManager.GetInstance().GetLTECellLatest(cellInfo.CellNameFull);
                    if (nbiotCell != null)//待验收工参中没有的信息，关联客户端本地工参补全
                    {
                        cellInfo.SectorID = nbiotCell.SectorID;
                    }

                    AddWorkParamSum(cellInfo);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    break;
                }
            }
        }

        private void AddWorkParamSum(CellWorkParam_TianJin cellInfo)
        {
            Dictionary<int, BtsWorkParam_TianJin> btsInfoIDic;
            if (!WorkParamSumDic.TryGetValue(cellInfo.DistrictName, out btsInfoIDic))
            {
                btsInfoIDic = new Dictionary<int, BtsWorkParam_TianJin>();
                WorkParamSumDic.Add(cellInfo.DistrictName, btsInfoIDic);
            }
            BtsWorkParam_TianJin btsInfo;
            if (!btsInfoIDic.TryGetValue(cellInfo.ENodeBID, out btsInfo))
            {
                btsInfo = new BtsWorkParam_TianJin(cellInfo);
                btsInfoIDic.Add(cellInfo.ENodeBID, btsInfo);
            }
            //btsInfo.DateTimeDes = cellInfo.UpdateTime;
            btsInfo.AddCellParamsInfo(cellInfo);
        }
    }

    public class UpdateWorkParamDes_TianJin : DiySqlMultiNonQuery
    {
        readonly OutDoorBtsAcceptInfo_TianJin acceptInfo;
        public UpdateWorkParamDes_TianJin(OutDoorBtsAcceptInfo_TianJin acceptInfo)
        {
            MainDB = true;
            this.acceptInfo = acceptInfo;
        }

        protected override string getSqlTextString()
        {
            StringBuilder strb = new StringBuilder();

            int stationAcceptType = getAcceptType(acceptInfo.IsAccordAccept);
            foreach (OutDoorCellAcceptInfo_TianJin param in acceptInfo.CellsAcceptDic.Values)
            {
                int cellAcceptType = getAcceptType(param.IsAccord);

                strb.Append(string.Format("update tb_btscheck_tianjin_cfg_cell set CellAcceptType = '{0}', StationAcceptType = '{1}' where CellName = '{2}';", cellAcceptType, stationAcceptType, param.CellName));
            }
            return strb.ToString();
        }

        private int getAcceptType(bool isAccord)
        {
            int acceptType;
            if (isAccord)
            {
                acceptType = (int)AcceptType.SuccessAccept;
            }
            else
            {
                acceptType = (int)AcceptType.FailedAccept;
            }

            return acceptType;
        }
    }

    public class InsertAcceptResult_TianJin : DiySqlMultiNonQuery
    {
        readonly OutDoorBtsAcceptInfo_TianJin btsAcceptInfo;
        readonly BtsWorkParam_TianJin btsInfo;
        readonly string savePath;

        public InsertAcceptResult_TianJin(OutDoorBtsAcceptInfo_TianJin btsAcceptInfo, BtsWorkParam_TianJin btsInfo, string savePath)
            : base()
        {
            MainDB = true;
            this.btsAcceptInfo = btsAcceptInfo;
            this.btsInfo = btsInfo;
            this.savePath = savePath;
        }

        protected override string getSqlTextString()
        {
            StringBuilder strb = new StringBuilder();

            int stationAcceptType = getAcceptType(btsAcceptInfo.IsAccordAccept);
            foreach (OutDoorCellAcceptInfo_TianJin param in btsAcceptInfo.CellsAcceptDic.Values)
            {
                CellWorkParam_TianJin cellInfo = (CellWorkParam_TianJin)btsInfo.CellWorkParamDic[param.CellId];

                int cellAcceptType = getAcceptType(param.IsAccord);

                strb.Append(string.Format("delete from tb_lte_cell_acceptInfo where [eNodeBID] = {0} and [cellId] = {1};", cellInfo.ENodeBID, cellInfo.CellID));
                strb.Append(string.Format(@"insert into [tb_lte_cell_acceptInfo] ([distictName],[btsName],[eNodeBID],[cellId],[coverType],[isCellPassAccept],[isBtsPassAccept],[updateTime],[errorInfo],[strdesc]) values ('{0}','{1}',{2},{3},'{4}',{5},{6},'{7}','{8}','{9}')"
                  , cellInfo.DistrictName, cellInfo.BtsNameFull, cellInfo.ENodeBID, cellInfo.CellID, cellInfo.CoverTypeDes, cellAcceptType,
                  stationAcceptType, DateTime.Now, btsAcceptInfo.NotAccordKpiDes, savePath));
            }
            return strb.ToString();
        }

        private int getAcceptType(bool isAccord)
        {
            int acceptType = (int)AcceptType.FailedAccept;
            if (isAccord)
            {
                acceptType = (int)AcceptType.SuccessAccept;
            }

            return acceptType;
        }
    }
}
