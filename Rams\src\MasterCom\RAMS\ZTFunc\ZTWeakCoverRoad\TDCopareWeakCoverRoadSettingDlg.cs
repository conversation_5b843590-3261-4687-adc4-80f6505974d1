﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class TDCopareWeakCoverRoadSettingDlg : BaseForm
    {
        public TDCopareWeakCoverRoadSettingDlg(WeakCoverCondition_TD condition)
        {
            InitializeComponent();
            setUIValue(condition);
        }
        void setUIValue(WeakCoverCondition_TD condition)
        {
            if (condition==null)
            {
                DateTime now = DateTime.Now.Date;
                beginTime1.Value = now.AddDays(-15);
                endTime1.Value = now.AddDays(-8);
                beginTime2.Value = now.AddDays(-7);
                endTime2.Value = now;
                this.numPccpchRscpThreshold.Value = -95;
                this.numDpchC_I.Value = 3;
                this.numPccpchC_I.Value = 3;
                this.numDistance.Value = 50;
                this.numMaxDistance.Value = 50;
                this.numRoadRadius.Value = 50;
            }
            else
            {
                beginTime1.Value = condition.Period1.BeginTime;
                endTime1.Value = condition.Period1.EndTime.Date.AddDays(-1);
                beginTime2.Value = condition.Period2.BeginTime;
                endTime2.Value = condition.Period2.EndTime.Date.AddDays(-1);
                this.numPccpchRscpThreshold.Value = condition.RscpThresholdMax;
                this.numDpchC_I.Value = condition.DpchC_iThresholdMax;
                this.numPccpchC_I.Value = condition.PcchC_iThresholdMax;
                this.numDistance.Value = (decimal)condition.WeakCoverDistanceMin;
                this.numMaxDistance.Value = (decimal)condition.DistanceOf2TestpointMax;
                this.numRoadRadius.Value = (decimal)condition.RoadGridSpan;
            }
        }

        public WeakCoverCondition_TD GetCondition()
        {
            WeakCoverCondition_TD condition = new WeakCoverCondition_TD();
            condition.Period1 = p1;
            condition.Period2 = p2;
            condition.RscpThresholdMax = (int)this.numPccpchRscpThreshold.Value;
            condition.DpchC_iThresholdMax = (int)this.numDpchC_I.Value;
            condition.PcchC_iThresholdMax = (int)this.numPccpchC_I.Value;
            condition.WeakCoverDistanceMin = (double)this.numDistance.Value;
            condition.DistanceOf2TestpointMax = (double)this.numMaxDistance.Value;
            condition.RoadGridSpan = (double)this.numRoadRadius.Value;
            return condition;
        }
        private TimePeriod p1 = null;
        private TimePeriod p2 = null;

        private void btnOK_Click(object sender, EventArgs e)
        {
            p1 = new TimePeriod();
            if (!p1.SetPeriod(beginTime1.Value.Date, endTime1.Value.Date.AddDays(1).Date))
            {
                MessageBox.Show("时间段1，开始时间不能大于结束时间！请重新设置条件！");
                return;
            }
            p2 = new TimePeriod();
            if (!p2.SetPeriod(beginTime2.Value.Date, endTime2.Value.Date.AddDays(1).Date))
            {
                MessageBox.Show("时间段2，开始时间不能大于结束时间！请重新设置条件！");
                return;
            }
            bool notIntersect = p1.BeginTime >= p2.EndTime || p1.EndTime <= p2.BeginTime;
            if (!notIntersect)
            {
                MessageBox.Show("时间段2与时间段1有交叉！请重新设置条件！");
                return;
            }
            DialogResult = DialogResult.OK;
        }

        private void checkPccpchC2I_CheckedChanged(object sender, EventArgs e)
        {
            numPccpchC_I.Enabled = checkPccpchC2I.Checked;
        }

        private void checkDpchC2I_CheckedChanged(object sender, EventArgs e)
        {
            numDpchC_I.Enabled = checkDpchC2I.Checked;
        }

    }
}
