﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRStationSettingDlg_XJ : BaseDialog
    {
        public NRStationSettingDlg_XJ()
        {
            InitializeComponent();
        }
        public NRStationSettingDlg_XJ(NRStationSettingDlgConfigModel_XJ condition)
        {
            InitializeComponent();
            setCondition(condition);
        }

        public void setCondition(NRStationSettingDlgConfigModel_XJ condition)
        {
            if (condition == null)
            {
                return;
            }

            txtAccessRate.Value = Convert.ToDecimal(condition.AccessSuccessRate);
            txtPing.Value = Convert.ToDecimal(condition.PingTimeDelay);
            txtDownRsrp.Value = Convert.ToDecimal(condition.DownTestRSRP);
            txtDownAvgSINR.Value = Convert.ToDecimal(condition.DownTestAvgSINR);
            txtDownThrought.Value = Convert.ToDecimal(condition.DownThroughput);
            txtUploadRSRP.Value = Convert.ToDecimal(condition.UploadTestRSRP);
            txtUploadAvgSINR.Value = Convert.ToDecimal(condition.UploadAvgSINR);
            txtUploadThrought.Value = Convert.ToDecimal(condition.UploadThroughput);
            txtCallDelay.Value = Convert.ToDecimal(condition.CallTimeDelay);
            txtCallRate.Value = Convert.ToDecimal(condition.CallSuccessRate);
            txtInSwitch.Value = Convert.ToDecimal(condition.StationInSwitch);
            txtBtwSwitch.Value = Convert.ToDecimal(condition.StationBtwSwitch);
        }

        public NRStationSettingDlgConfigModel_XJ GetCondition()
        {
            NRStationSettingDlgConfigModel_XJ condition = new NRStationSettingDlgConfigModel_XJ();
            condition.AccessSuccessRate = txtAccessRate.Value.ToString();
            condition.PingTimeDelay = txtPing.Value.ToString();
            condition.DownTestRSRP = txtDownAvgSINR.Value.ToString();
            condition.DownTestAvgSINR = txtDownAvgSINR.Value.ToString();
            condition.DownThroughput = txtDownThrought.Value.ToString();
            condition.UploadTestRSRP = txtUploadRSRP.Value.ToString();
            condition.UploadAvgSINR = txtAccessRate.Value.ToString();
            condition.UploadThroughput = txtUploadThrought.Value.ToString();
            condition.CallTimeDelay = txtCallDelay.Value.ToString();
            condition.CallSuccessRate = txtCallRate.Value.ToString();
            condition.StationInSwitch = txtInSwitch.Value.ToString();
            condition.StationBtwSwitch = txtBtwSwitch.Value.ToString();
            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            var cond = GetCondition();
            if (cond != null)
            {
                NRStationSettingDlgConfig_XJ.Instance.SaveConfig(cond);
                DialogResult = DialogResult.OK;
            }
            else
            {
                MessageBox.Show("主库连接设置不能为空");
            }
        }
    }
}
