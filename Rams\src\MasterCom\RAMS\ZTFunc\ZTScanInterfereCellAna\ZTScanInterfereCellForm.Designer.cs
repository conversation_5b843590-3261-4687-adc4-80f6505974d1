﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTScanInterfereCellForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.listViewTotal = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSampleCount = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLAC = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCI = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLongitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLatitude = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnCellCount = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnRxLev = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnUnderCell = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnInterfereCount = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnUnderRxLev = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnUnderLac = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnUnderCi = new BrightIdeasSoftware.OLVColumn();
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal)).BeginInit();
            this.ctxMenu.SuspendLayout();
            this.SuspendLayout();
            // 
            // listViewTotal
            // 
            this.listViewTotal.AllColumns.Add(this.olvColumnSN);
            this.listViewTotal.AllColumns.Add(this.olvColumnCellName);
            this.listViewTotal.AllColumns.Add(this.olvColumnSampleCount);
            this.listViewTotal.AllColumns.Add(this.olvColumnLAC);
            this.listViewTotal.AllColumns.Add(this.olvColumnCI);
            this.listViewTotal.AllColumns.Add(this.olvColumnLongitude);
            this.listViewTotal.AllColumns.Add(this.olvColumnLatitude);
            this.listViewTotal.AllColumns.Add(this.olvColumnCellCount);
            this.listViewTotal.AllColumns.Add(this.olvColumnRxLev);
            this.listViewTotal.AllColumns.Add(this.olvColumnUnderCell);
            this.listViewTotal.AllColumns.Add(this.olvColumnInterfereCount);
            this.listViewTotal.AllColumns.Add(this.olvColumnUnderRxLev);
            this.listViewTotal.AllColumns.Add(this.olvColumnUnderLac);
            this.listViewTotal.AllColumns.Add(this.olvColumnUnderCi);
            this.listViewTotal.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnCellName,
            this.olvColumnLAC,
            this.olvColumnCI,
            this.olvColumnLongitude,
            this.olvColumnLatitude,
            this.olvColumnCellCount,
            this.olvColumnRxLev,
            this.olvColumnUnderCell,
            this.olvColumnInterfereCount,
            this.olvColumnUnderRxLev,
            this.olvColumnUnderLac,
            this.olvColumnUnderCi});
            this.listViewTotal.ContextMenuStrip = this.ctxMenu;
            this.listViewTotal.Cursor = System.Windows.Forms.Cursors.Default;
            this.listViewTotal.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewTotal.FullRowSelect = true;
            this.listViewTotal.GridLines = true;
            this.listViewTotal.HeaderWordWrap = true;
            this.listViewTotal.IsNeedShowOverlay = false;
            this.listViewTotal.Location = new System.Drawing.Point(0, 0);
            this.listViewTotal.Name = "listViewTotal";
            this.listViewTotal.OwnerDraw = true;
            this.listViewTotal.ShowGroups = false;
            this.listViewTotal.Size = new System.Drawing.Size(1030, 395);
            this.listViewTotal.TabIndex = 5;
            this.listViewTotal.UseCompatibleStateImageBehavior = false;
            this.listViewTotal.View = System.Windows.Forms.View.Details;
            this.listViewTotal.VirtualMode = true;
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名称";
            this.olvColumnCellName.Width = 120;
            // 
            // olvColumnSampleCount
            // 
            this.olvColumnSampleCount.DisplayIndex = 2;
            this.olvColumnSampleCount.HeaderFont = null;
            this.olvColumnSampleCount.IsVisible = false;
            this.olvColumnSampleCount.Text = "采样点个数";
            this.olvColumnSampleCount.Width = 70;
            // 
            // olvColumnLAC
            // 
            this.olvColumnLAC.HeaderFont = null;
            this.olvColumnLAC.Text = "LAC";
            // 
            // olvColumnCI
            // 
            this.olvColumnCI.HeaderFont = null;
            this.olvColumnCI.Text = "CI";
            // 
            // olvColumnLongitude
            // 
            this.olvColumnLongitude.HeaderFont = null;
            this.olvColumnLongitude.Text = "经度";
            this.olvColumnLongitude.Width = 80;
            // 
            // olvColumnLatitude
            // 
            this.olvColumnLatitude.HeaderFont = null;
            this.olvColumnLatitude.Text = "纬度";
            this.olvColumnLatitude.Width = 80;
            // 
            // olvColumnCellCount
            // 
            this.olvColumnCellCount.HeaderFont = null;
            this.olvColumnCellCount.Text = "干扰小区个数";
            this.olvColumnCellCount.Width = 80;
            // 
            // olvColumnRxLev
            // 
            this.olvColumnRxLev.HeaderFont = null;
            this.olvColumnRxLev.Text = "RxLevSub";
            // 
            // olvColumnUnderCell
            // 
            this.olvColumnUnderCell.HeaderFont = null;
            this.olvColumnUnderCell.Text = "受干扰小区";
            this.olvColumnUnderCell.Width = 120;
            // 
            // olvColumnInterfereCount
            // 
            this.olvColumnInterfereCount.HeaderFont = null;
            this.olvColumnInterfereCount.Text = "进入覆盖度次数";
            this.olvColumnInterfereCount.Width = 110;
            // 
            // olvColumnUnderRxLev
            // 
            this.olvColumnUnderRxLev.HeaderFont = null;
            this.olvColumnUnderRxLev.Text = "RxLevSub";
            // 
            // olvColumnUnderLac
            // 
            this.olvColumnUnderLac.HeaderFont = null;
            this.olvColumnUnderLac.Text = "LAC";
            // 
            // olvColumnUnderCi
            // 
            this.olvColumnUnderCi.HeaderFont = null;
            this.olvColumnUnderCi.Text = "CI";
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel,
            this.toolStripMenuItem1,
            this.miExpandAll,
            this.miCallapsAll});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(130, 76);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(126, 6);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(129, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCallapsAll
            // 
            this.miCallapsAll.Name = "miCallapsAll";
            this.miCallapsAll.Size = new System.Drawing.Size(129, 22);
            this.miCallapsAll.Text = "全部合并";
            this.miCallapsAll.Click += new System.EventHandler(this.miCallapsAll_Click);
            // 
            // ZTScanInterfereCellForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1030, 395);
            this.Controls.Add(this.listViewTotal);
            this.Name = "ZTScanInterfereCellForm";
            this.Text = "干扰小区分析列表";
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView listViewTotal;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnLAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCI;
        private BrightIdeasSoftware.OLVColumn olvColumnLongitude;
        private BrightIdeasSoftware.OLVColumn olvColumnLatitude;
        private BrightIdeasSoftware.OLVColumn olvColumnRxLev;
        private BrightIdeasSoftware.OLVColumn olvColumnUnderCell;
        private BrightIdeasSoftware.OLVColumn olvColumnInterfereCount;
        private BrightIdeasSoftware.OLVColumn olvColumnUnderRxLev;
        private BrightIdeasSoftware.OLVColumn olvColumnUnderLac;
        private BrightIdeasSoftware.OLVColumn olvColumnUnderCi;
        private BrightIdeasSoftware.OLVColumn olvColumnCellCount;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCallapsAll;
    }
}