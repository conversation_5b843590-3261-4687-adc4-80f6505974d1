﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public class HandoverCountStatByRegion_W : HandoverCountStatByRegion_TD
    {
        public HandoverCountStatByRegion_W(MainModel mainModel) : base(mainModel)
        {
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 14000, 14033, this.Name);
        }

        protected override void getResultAfterQuery()
        {
            infoList.Clear();
            Dictionary<string, HandoverCountInfo> keyInfoDict = new Dictionary<string, HandoverCountInfo>();
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                foreach (Event e in file.Events)
                {
                    if (!HandoverType.TDHandoverEventList.Contains(e.ID))
                    {
                        continue;
                    }

                    ICell srcCell = e.GetSrcCell();
                    ICell tarCell = e.GetTargetCell();

                    HandoverCountInfo info = new HandoverCountInfo_W(srcCell, tarCell, e);
                    if (!keyInfoDict.ContainsKey(info.Key))
                    {
                        keyInfoDict.Add(info.Key, info);
                    }
                    else
                    {
                        keyInfoDict[info.Key].Merge(info);
                    }
                }
            }

            foreach (HandoverCountInfo info in keyInfoDict.Values)
            {
                infoList.Add(info);
            }
        }
    }

    public class HandoverCountInfo_W : HandoverCountInfo
    {
        public HandoverCountInfo_W(object srcCell, object tarCell, Event evt)
            : base(srcCell, tarCell, evt)
        {
            InitLacCi();
            InitHandoverCount();
        }

        protected new void InitLacCi()
        {
            try
            {
                if (srcCell == null)
                {
                    SrcLac = (int)Evt["LAC"];
                    SrcCi = (int)Evt["CI"];
                }
                else if (srcCell is WCell)
                {
                    WCell cell = srcCell as WCell;
                    SrcLac = cell.LAC;
                    SrcCi = cell.CI;
                }

                if (tarCell != null && tarCell is Cell)
                {
                    Cell cell = tarCell as Cell;
                    TarLac = cell.LAC;
                    TarCi = cell.CI;
                }
                else if (tarCell != null && tarCell is WCell)
                {
                    WCell cell = tarCell as WCell;
                    TarLac = cell.LAC;
                    TarCi = cell.CI;
                }
            }
            catch (Exception ex)
            {
                throw (new Exception("Arguments Invalid: " + ex.Message));
            }

            SrcLacStr = SrcLac == -1000000 || SrcLac == -1 ? "" : SrcLac.ToString();
            SrcCiStr = SrcCi == -1000000 || SrcCi == -1 ? "" : SrcCi.ToString();
            TarLacStr = TarLac == -1000000 || TarLac == -1 ? "" : TarLac.ToString();
            TarCiStr = TarCi == -1000000 || TarCi == -1 ? "" : TarCi.ToString();
        }

        protected new void InitHandoverCount()
        {
            if (EventID == 541 || EventID == 544 || EventID == 547)
            {
                HandoverRequestCount = 1;
            }
            else if (EventID == 542 || EventID == 545 || EventID == 548)
            {
                HandoverSucceedCount = 1;
            }
            else if (EventID == 543 || EventID == 546 || EventID == 549)
            {
                HandoverFailedCount = 1;
            }
        }
    }
}
