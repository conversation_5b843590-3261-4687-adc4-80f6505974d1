﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class VolteWeakMosAnaByRegion : VolteWeakMosAnaBase
    {
        protected VolteWeakMosAnaByRegion()
            : base()
        {
            FilterSampleByRegion = true;
        }

        private static VolteWeakMosAnaByRegion instance = null;
        public static VolteWeakMosAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VolteWeakMosAnaByRegion();
                    }
                }
            }
            return instance;
        }

        public override string Name
        {
            get { return "弱MOS分析(按区域)"; }
        }
    }

    public class VolteWeakMosAnaByRegion_FDD : VolteWeakMosAnaBase_FDD
    {
        private static VolteWeakMosAnaByRegion_FDD instance = null;
        public static VolteWeakMosAnaByRegion_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VolteWeakMosAnaByRegion_FDD();
                    }
                }
            }
            return instance;
        }
        protected VolteWeakMosAnaByRegion_FDD()
            : base()
        {
            FilterSampleByRegion = true;
        }
        public override string Name
        {
            get { return "VOLTE_FDD弱MOS分析(按区域)"; }
        }
        
    }
}
