﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRSinrRangeValuesSetting : BaseDialog
    {
        public NRSinrRangeValuesSetting(string kpiname)
        {
            InitializeComponent();
            Items = new List<NRSinrConditionItem>();
            this.kpiname = kpiname;
        }

        public List<NRSinrConditionItem> Items { get; set; }
        private readonly string kpiname;

        protected void MakeRange()
        {
            int count = (int)numIntervals.Value;
            int min = (int)numMin.Value;
            int max = (int)numMax.Value;
            int IntervalValue  = (max - min) / (count - 2);

            NRSinrConditionItem temp = new NRSinrConditionItem(1, max, 0, kpiname);
            Items.Add(temp);
            for (int i = 1; i < count - 2; i++)
            {
                temp = new NRSinrConditionItem(2, max - IntervalValue, max, kpiname);
                Items.Add(temp);
                max -= IntervalValue;
            }
            temp = new NRSinrConditionItem(2, min, max, kpiname);
            Items.Add(temp);
            temp = new NRSinrConditionItem(3, min, 0, kpiname);
            Items.Add(temp);
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            if ((int)numMin.Value > (int)numMax.Value)
            {
                MessageBox.Show("最大值应大于最小值!", "错误");
                return;
            }
            MakeRange();
            DialogResult = DialogResult.OK;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }
    }
}
