﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class HandoverBehindTimeInfo
    {
        private readonly List<TestPoint> points = new List<TestPoint>();
        public List<TestPoint> TestPoints
        {
            get { return points; }
        }
        private float minSvrPccpch = float.MaxValue;
        public float MinSvrPccpch
        {
            get
            {
                return minSvrPccpch == float.MaxValue ? float.NaN : minSvrPccpch;
            }
        }
        private float maxSvrPccpch = float.MinValue;
        public float MaxSvrPccpch
        {
            get
            {
                return maxSvrPccpch == float.MinValue ? float.NaN : maxSvrPccpch;
            }
        }
        private float sumSvrPccpch = 0;
        public float AvgSvrPccpch
        {
            get { return (float)Math.Round(sumSvrPccpch / points.Count, 2); }
        }

        private int minPccpchC2I = int.MaxValue;
        public float MinPccpchC2I
        {
            get { return minPccpchC2I == int.MaxValue ? float.NaN : minPccpchC2I; }
        }
        private int maxPccpchC2I = int.MinValue;
        public float MaxPccpchC2I
        {
            get { return maxPccpchC2I == int.MinValue ? float.NaN : maxPccpchC2I; }
        }
        private float sumPccpchC2I = 0;
        private int pccpchC2ICnt = 0;
        public float AvgPccpchC2I
        {
            get { return (float)Math.Round(sumPccpchC2I / pccpchC2ICnt, 2); }
        }

        private int minDpchC2I = int.MaxValue;
        public float MinDpchC2I
        {
            get { return minDpchC2I == int.MaxValue ? float.NaN : (float)minDpchC2I; }
        }
        private int maxDpchC2I = int.MinValue;
        public float MaxDpchC2I
        {
            get { return maxDpchC2I == int.MinValue ? float.NaN : maxDpchC2I; }
        }
        private float sumDpchC2I = 0;
        private int dpchC2ICnt = 0;
        public float AvgDpchC2I
        {
            get { return (float)Math.Round(sumDpchC2I / dpchC2ICnt, 2); }
        }

        public double MidLng
        {
            get
            {
                double lng = 0;
                if (points.Count > 0)
                {
                    lng = points[(points.Count / 2) - 1].Longitude;
                }
                return lng;
            }
        }

        public double MidLat
        {
            get
            {
                double lat = 0;
                if (points.Count > 0)
                {
                    lat = points[(points.Count / 2) - 1].Latitude;
                }
                return lat;
            }
        }

        public int StaySeconds
        {
            get
            {
                int sec = 0;
                if (points.Count > 0)
                {
                    sec = (int)(points[points.Count - 1].DateTime - points[0].DateTime).TotalSeconds;
                }
                return sec;
            }
        }

        public int SN
        {
            get;
            set;
        }

        public string RoadName
        {
            get;
            set;
        }

        public void FindRoadName()
        {
            List<double> lngs = new List<double>();
            List<double> lats = new List<double>();
            TestPoint tp = points[0];
            lngs.Add(tp.Longitude);
            lats.Add(tp.Latitude);
            tp = points[(points.Count / 2)];
            lngs.Add(tp.Longitude);
            lats.Add(tp.Latitude);
            tp = points[points.Count - 1];
            lngs.Add(tp.Longitude);
            lats.Add(tp.Latitude);
            RoadName = MasterCom.Util.GISManager.GetInstance().GetRoadPlaceDesc(MidLng, MidLat);
        }

        public string FileName
        {
            get
            {
                string name = string.Empty;
                if (points.Count > 0)
                {
                    name = points[0].FileName;
                }
                return name;
            }
        }
        private double distance = -1;
        public double Distance
        {
            get
            {
                if (distance < 0)
                {
                    distance = 0;
                    for (int i = 1; i < points.Count; i++)
                    {
                        distance += MasterCom.Util.MathFuncs.GetDistance(points[i - 1].Longitude, points[i - 1].Latitude, points[i].Longitude, points[i].Latitude);
                    }
                    distance = Math.Round(distance, 2);
                }
                return distance;
            }
        }

        public int TestPointCount
        {
            get { return points.Count; }
        }

        public void AddTestPoint(TestPoint tp, float svrPccpch)
        {
            points.Add(tp);
            minSvrPccpch = Math.Min(minSvrPccpch, svrPccpch);
            maxSvrPccpch = Math.Max(maxSvrPccpch, svrPccpch);
            sumSvrPccpch += svrPccpch;

            int? pccpchC2IValue = (int?)tp["TD_PCCPCH_C2I"];
            if (pccpchC2IValue != null && pccpchC2IValue >= -20 && pccpchC2IValue <= 25)
            {
                minPccpchC2I = Math.Min(minPccpchC2I, (int)pccpchC2IValue);
                maxPccpchC2I = Math.Max(maxPccpchC2I, (int)pccpchC2IValue);
                sumPccpchC2I += (int)pccpchC2IValue;
                pccpchC2ICnt++;
            }

            int? dpchC2I = (int?)tp["TD_DPCH_C2I"];
            if (dpchC2I != null && dpchC2I >= -20 && dpchC2I <= 25)
            {
                minDpchC2I = Math.Min(minDpchC2I, (int)dpchC2I);
                maxDpchC2I = Math.Max(maxDpchC2I, (int)dpchC2I);
                sumDpchC2I += (int)dpchC2I;
                dpchC2ICnt++;
            }

        }

    }
}
