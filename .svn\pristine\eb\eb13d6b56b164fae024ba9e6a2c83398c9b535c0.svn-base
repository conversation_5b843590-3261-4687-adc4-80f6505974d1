﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Stat
{
    public class ContextEvent
    {
        public string Comand { get; set; }
        public object OneParame
        {
            get { return Parames == null || Parames.Length < 1 ? null : Parames[0]; }
        }
        public object[] Parames { get; set; }

        public ContextEvent(string comand, params object[] parames)
        {
            this.Comand = comand;
            this.Parames = parames;
        }
    }

    interface IMainFormProxy
    {
        AMainForm MainForm
        {
            get;
            set;
        }

        void Init();
    }

    public static partial class AMainEventType
    {
        public readonly static string Save = "_SaveStyle";
        public readonly static string SaveAs = "_SaveStyleAs";
        public readonly static string Delete = "_DelStyle";
        public readonly static string ExportXls = "_Export2Xls";
        public readonly static string ExportXlsSimple = "_Export2XlsSimple";
        public readonly static string FIREEXPORT2PDF = "_Export2PDF";
        public readonly static string FIREQUERYDATACHANGE = "_QueryDataChange";
        public readonly static string New = "_CreatReport";
        public readonly static string Active = "_ReportActive";
        public readonly static string Close = "_ReportColse";
        public readonly static string FIREDRAWREGIONGRID = "_DrawRegionGrid";
        public readonly static string Rename = "_Rename";
    }
}
