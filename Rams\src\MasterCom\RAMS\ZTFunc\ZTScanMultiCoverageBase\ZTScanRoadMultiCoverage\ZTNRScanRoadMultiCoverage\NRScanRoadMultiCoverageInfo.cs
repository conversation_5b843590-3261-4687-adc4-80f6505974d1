﻿using MasterCom.MControls;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRScanRoadMultiCoverageColorRanges : ScanMultiCoverageColorRanges
    {
        private static NRScanRoadMultiCoverageColorRanges instance = null;
        public static NRScanRoadMultiCoverageColorRanges Instance
        {
            get 
            {
                if (instance == null)
                {
                    instance = new NRScanRoadMultiCoverageColorRanges();
                }
                return instance;
            }
        }

        private NRScanRoadMultiCoverageColorRanges()
        {
            initialize();
        }

        public override string RangeName { get { return "NRScanRoadMultiCoverageColorRanges"; } }

        protected override void initialize()
        {
            ColorRanges = new List<ColorRange>();
            ColorRanges.Add(new ColorRange(0, 2, Color.Cyan));
            ColorRanges.Add(new ColorRange(2, 4, Color.Lime));
            ColorRanges.Add(new ColorRange(4, 6, Color.Yellow));
            ColorRanges.Add(new ColorRange(6, 10, Color.Orange));
            ColorRanges.Add(new ColorRange(10, 50, Color.Red));
        }
    }

    public class NRScanRoadMultiCoverageInfo : RoadMultiCoverageInfoBase
    {
        public List<NRCellRsrpInfo> LstRelatedCell { get; set; } = new List<NRCellRsrpInfo>();
        public List<NRCellRsrpInfo> LstAbsoluteCell { get; set; } = new List<NRCellRsrpInfo>();
        public List<NRCellRsrpInfo> LstComprehensive { get; set; } = new List<NRCellRsrpInfo>();

        public string StrRegionName { get; set; }

        public List<string> RelCoverCellsName { get; set; } = new List<string>();
        public List<string> AbsCoverCellsName { get; set; } = new List<string>();
        public List<string> RelANDAbsCoverCellsName { get; set; } = new List<string>();

        public string RelCoverCellsNameStr { get; private set; }
        public string AbsCoverCellsNameStr { get; private set; }
        public string RelORAbsCoverCellsNameStr { get; private set; }

        public void Calculate()
        {
            RelCoverCellsNameStr = getNameStr(RelCoverCellsName);
            AbsCoverCellsNameStr = getNameStr(AbsCoverCellsName);
            RelORAbsCoverCellsNameStr = getNameStr(RelANDAbsCoverCellsName);
        }
    }

    public class NRScanGridRoadMultiCoverageInfo : GridRoadMultiCoverageInfoBase
    {
        public NRScanGridRoadMultiCoverageInfo(double ltLongitude, double ltLatitude)
           : base(ltLongitude, ltLatitude)
        {
        }
    }

    public class NRCellRsrpInfo : CellRsrpInfoBase<NRCell>
    {
        public NRCellRsrpInfo(float rsrp, int arfcn, int pci, NRCell cell)
            : base(rsrp, arfcn, pci, cell)
        {
        }
    }
}
