﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.UserMng;
using MasterCom.RAMS.Compare;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Net
{
    public class QueryCompareHisGridByRegion : CompHisGridQuery
    {
        public QueryCompareHisGridByRegion(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get { return "竞对历史栅格分析(按区域)"; }
        }
        public override string IconName
        {
            get { return null; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 20000, 20007, this.Name);
        }
        protected override void prepareQueryPackage(Package package, TimePeriod period)
        {
            package.Command = Command.InfoQuery;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.REQTYPE_COMPBENCH_UNIT_INFO;
            package.Content.PrepareAddParam();
            addQueryPackageParamPeriod(package, period);
            addQueryPackageParamRegion(package);
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }
        protected override bool isValidPoint(CompUnit data)
        {
            try{
                if (data.ltlongitude > 10 && data.brlongitude < 160 && data.ltlatitude > 15 && data.brlatitude < 80)
                {
                    return Condition.Geometorys.GeoOp.ContainsRectCenter(new DbRect(data.ltlongitude, data.ltlatitude, data.brlongitude, data.brlatitude));
                }
                else
                {
                    return false;
                }
            }catch{
                return false;
            }
           
        }

    }
}
