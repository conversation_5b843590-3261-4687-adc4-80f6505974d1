﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 连续块查找器
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class LteMgrsBlockFinder<T> where T : ILteMgrsBlockItem
    {
        public LteMgrsBlockFinder(double blockItemSize, int blockItemCount)
        {
            this.blockItemSize = blockItemSize;
            this.blockItemCount = blockItemCount;
        }

        public LteMgrsBlockFinder(double blockItemSize, int blockItemCount, double blockGridDist)
        {
            this.blockItemSize = blockItemSize;
            this.blockItemCount = blockItemCount;
            this.blockGridDist = blockGridDist;
        }

        public List<T[]> FindBlocks(List<T> items)
        {
            // 初始化
            idItemsDic = new Dictionary<int, List<T>>();
            parents = new int[items.Count];
            for (int i = 0; i < parents.Length; ++i)
            {
                parents[i] = i;
            }

            // 相邻查找
            for (int i = 0; i < items.Count - 1; ++i)
            {
                for (int j = 0; j < items.Count; ++j)
                {
                    if (IsAdjacent(items[i], items[j]))
                    {
                        Union(i, j);
                    }
                }
            }

            // 同类合并
            for (int i = 0; i < parents.Length; ++i)
            {
                int par = Find(i);
                if (!idItemsDic.ContainsKey(par))
                {
                    idItemsDic.Add(par, new List<T>());
                }
                idItemsDic[par].Add(items[i]);
            }

            // 返回结果
            List<T[]> retList = new List<T[]>();
            foreach (List<T> values in idItemsDic.Values)
            {
                if (values.Count >= this.blockItemCount)
                {
                    retList.Add(values.ToArray());
                }
            }

            idItemsDic = null;
            parents = null;
            return retList;
        }

        private bool IsAdjacent(T x, T y)
        {
            double dis = MathFuncs.GetDistance(x.CentLng, x.CentLat, y.CentLng, y.CentLat);
            blockGridDist = blockGridDist <= 1.5 ? 1.5 : blockGridDist;
            return dis < blockItemSize * blockGridDist;
        }

        private int Find(int x)
        {
            if (x == parents[x])
            {
                return x;
            }
            return parents[x] = Find(parents[x]); // 路径压缩
        }

        private void Union(int x, int y)
        {
            int px = Find(x);
            int py = Find(y);
            parents[px] = py;
        }

        private readonly double blockItemSize;
        private readonly int blockItemCount;
        private double blockGridDist = 1.5d;//栅格距离
        private int[] parents = null;
        private Dictionary<int, List<T>> idItemsDic { get; set; }
    }

    public interface ILteMgrsBlockItem
    {
        double CentLng
        {
            get;
        }

        double CentLat
        {
            get;
        }
    }
}
