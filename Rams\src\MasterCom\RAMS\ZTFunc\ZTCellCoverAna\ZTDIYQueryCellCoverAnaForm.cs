﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using Microsoft.Office.Interop.Excel;
using System.Reflection;
using MasterCom.Util;
using MasterCom.MTGis;
using MasterCom.RAMS.CQT;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTDIYQueryCellCoverAnaForm : MinCloseForm
    {
        public ZTDIYQueryCellCoverAnaForm() : base()
        {
            InitializeComponent();
            this.mapForm = MainModel.MainForm.GetMapForm();
        }
        List<CellCoverAnaInfo> ResultList;
        public static CellGridKey gridKey { get; set; } = new CellGridKey(); 
        public void FillData(List<CellCoverAnaInfo> resultList)
        {            
            BindingSource bindingSource = new BindingSource();
            bindingSource.DataSource = resultList;
            cellCoverAnaGrid.DataSource = bindingSource;
            cellCoverAnaGrid.RefreshDataSource();

            ResultList = new List<CellCoverAnaInfo>();
            ResultList.AddRange(resultList);

        }
        /// <summary>
        /// 导出Excel
        /// </summary>
        private void outPutExcel_Click(object sender, EventArgs e)
        {
            ExportExcel();
        }
        MapForm mapForm;
        private void cellCoverAnaGrid_DoubleClick(object sender, EventArgs e)
        {
            int[] rows = gridView1.GetSelectedRows();
            if (rows.Length == 0)
                return;
            object o = gridView1.GetRow(rows[0]);
            CellCoverAnaInfo cellCoverAnaInfo = o as CellCoverAnaInfo;
            if (cellCoverAnaInfo != null )
            {                
                gridKey.ILongitude = cellCoverAnaInfo.DLongitude;
                gridKey.ILatitude = cellCoverAnaInfo.DLatitude;
                DbPoint center = new DbPoint(double.Parse(cellCoverAnaInfo.DLongitude.ToString()),
                    double.Parse(cellCoverAnaInfo.DLatitude.ToString()));
                mapForm.GoToView(center.x, center.y,8000);
            }
        }

        private void ExportExcel()
        {
            List<NPOIRow> datas = new List<NPOIRow>();
            NPOIRow nr1 = new NPOIRow();
            List<object> cols = new List<object>();
            cols.Add("栅格序号");
            cols.Add("经度");
            cols.Add("纬度");
            cols.Add("小区名");
            cols.Add("LAC");
            cols.Add("CI");
            cols.Add("BCCH");
            cols.Add("BSIC");
            cols.Add("场强值");
            cols.Add("采样点数");
            cols.Add("C/I平均值");
            cols.Add("RxQuery均值");
            cols.Add("路段名称");

            nr1.cellValues = cols;
            datas.Add(nr1);
    
            List<List<NPOIRow>> nrDatasList = new List<List<NPOIRow>>();
            List<string> sheetNames = new List<string>();

            foreach (CellCoverAnaInfo cellInfo in ResultList)
            {
                NPOIRow nr = new NPOIRow();
                List<object> objs = new List<object>();

                objs.Add(cellInfo.IGridNum.ToString());
                objs.Add(cellInfo.DLongitude.ToString());
                objs.Add(cellInfo.DLatitude.ToString());
                objs.Add(cellInfo.CellName.ToString());
                objs.Add(cellInfo.ILac.ToString());
                objs.Add(cellInfo.ICi.ToString());
                objs.Add(cellInfo.IBCCH.ToString());
                objs.Add(cellInfo.IBSIC.ToString());
                objs.Add(cellInfo.ISampleNum.ToString());
                objs.Add(cellInfo.IRxLevSub.ToString());
                objs.Add(cellInfo.strC_I.ToString());
                objs.Add(cellInfo.strRxqulity.ToString());
                if (cellInfo.StrStreetName == null)
                    objs.Add("");
                else
                    objs.Add(cellInfo.StrStreetName.ToString());

                nr.cellValues = objs;
                datas.Add(nr);
            }
            nrDatasList.Add(datas);
            sheetNames.Add("小区栅格覆盖分析");
            ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames);
        }
    }
}
