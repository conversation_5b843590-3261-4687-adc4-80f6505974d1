﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class DiyQueryFddDBCoverPic : DIYSQLBase
    {
        public List<FddIndoorDBCoverPic> DataList { get; private set; } = new List<FddIndoorDBCoverPic>();
        readonly int eci;
        readonly FddDatabaseSetting setting;
        public DiyQueryFddDBCoverPic(int eci, FddDatabaseSetting setting)
            : base()
        {
            MainDB = true;
            this.eci = eci;
            this.setting = setting;
        }

        public override string Name
        {
            get
            {
                return "查询FDD单验数据库配置";
            }
        }

        protected override string getSqlTextString()
        {
            string tableName = string.Format(@"{0}.{1}.[dbo].[{2}]", setting.ServerIp, setting.DbName, setting.TableNameHead);
            StringBuilder selectSQL = new StringBuilder();
            selectSQL.AppendFormat(@"select [工单流水号],[楼层],[rsrp测试图],[sinr测试图],[ftp上传图],[ftp下载图] from {0} where [工单流水号] = (select top 1 [工单流水号] from {0}where ECI = {1} order by[工单流水号] desc) order by [楼层]",
                tableName, eci);
            return selectSQL.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[6];
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Dictionary<string, FddIndoorDBCoverPic> dataDic = new Dictionary<string, FddIndoorDBCoverPic>();          
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    FddIndoorDBCoverPic data = new FddIndoorDBCoverPic();
                    data.FillData(package);
                    //相同楼层只保留一条数据
                    if (!string.IsNullOrEmpty(data.Floor) && !dataDic.ContainsKey(data.Floor))
                    {
                        dataDic.Add(data.Floor, data);
                    }
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
            DataList = new List<FddIndoorDBCoverPic>(dataDic.Values);
        }

        public void SetFullPath(string rootPath)
        {
            foreach (var data in DataList)
            {
                data.SetFullPath(rootPath);
            }
        }

        public string GetDirectoryPath(string rootPath)
        {
            if (DataList.Count > 0)
            {
                return DataList[0].GetDirectoryPath(rootPath);
            }
            return "";
        }

        public string GetOrderID()
        {
            if (DataList.Count > 0)
            {
                return DataList[0].OrderID;
            }
            return "";
        }
    }

    public class FddIndoorDBCoverPic
    {
        public string OrderID { get; set; }
        public string Floor { get; set; }
        public string RsrpPicPath { get; set; }
        public string SinrPicPath { get; set; }
        public string FtpULPicPath { get; set; }
        public string FtpDLPicPath { get; set; }

        public string RsrpName { get; set; }
        public string SinrName { get; set; }
        public string FtpULName { get; set; }
        public string FtpDLName { get; set; }

        public void FillData(Package package)
        {
            OrderID = package.Content.GetParamString();
            Floor = package.Content.GetParamString();
            RsrpPicPath = package.Content.GetParamString();
            SinrPicPath = package.Content.GetParamString();
            FtpULPicPath = package.Content.GetParamString();
            FtpDLPicPath = package.Content.GetParamString();

            RsrpName = setName("RSRP-");
            SinrName = setName("SINR-");
            FtpULName = setName("FTP上传-");
            FtpDLName = setName("FTP下载-");
        }

        private string setName(string name)
        {
            return name + Floor;
        }

        public string GetDirectoryPath(string rootPath)
        {
            return rootPath + System.IO.Path.DirectorySeparatorChar + OrderID;
        }

        public void SetFullPath(string rootPath)
        {
            RsrpPicPath = getFullPath(rootPath, RsrpPicPath);
            SinrPicPath = getFullPath(rootPath, SinrPicPath);
            FtpULPicPath = getFullPath(rootPath, FtpULPicPath);
            FtpDLPicPath = getFullPath(rootPath, FtpDLPicPath);
        }

        private string getFullPath(string rootPath, string picPath)
        {
            if (!string.IsNullOrEmpty(picPath))
            {
                return rootPath + System.IO.Path.DirectorySeparatorChar + OrderID + System.IO.Path.DirectorySeparatorChar + picPath;
            }
            return "";
        }
    }
}
