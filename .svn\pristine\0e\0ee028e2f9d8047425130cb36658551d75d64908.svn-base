﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.NOP
{
    public class ShowReasonStatForm : QueryBase
    {
        public ShowReasonStatForm()
            : base(MainModel.GetInstance())
        {
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override string Name
        {
            get { return "预处理主要原因"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 29000, 29003, this.Name);
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected ReasonStatForm statForm = null;

        protected override void query()
        {
            if (statForm == null || statForm.IsDisposed)
            {
                statForm = new ReasonStatForm(this);
            }
            statForm.Visible = true;
            statForm.Owner = MainModel.MainForm;
            statForm.BringToFront();
        }
    }
}
