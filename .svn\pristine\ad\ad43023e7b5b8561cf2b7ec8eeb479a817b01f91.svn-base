﻿namespace MasterCom.RAMS.Func
{
    partial class MapNRCellLayerAntennaProperties
    {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.grpFactor = new System.Windows.Forms.GroupBox();
            this.TrackBarOpacity = new System.Windows.Forms.TrackBar();
            this.colorAntenna = new DevExpress.XtraEditors.ColorEdit();
            this.label100 = new System.Windows.Forms.Label();
            this.LabelOpacity = new System.Windows.Forms.Label();
            this.label0 = new System.Windows.Forms.Label();
            this.checkBoxDisplay = new System.Windows.Forms.CheckBox();
            this.labelColor = new System.Windows.Forms.Label();
            this.cbxDrawAntennaLabel = new System.Windows.Forms.CheckBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.btnFont = new DevExpress.XtraEditors.SimpleButton();
            this.cbxAntennaDescription = new System.Windows.Forms.CheckBox();
            this.cbxAntennaAltitude = new System.Windows.Forms.CheckBox();
            this.cbxAntennaDownward = new System.Windows.Forms.CheckBox();
            this.cbxAntennaDirection = new System.Windows.Forms.CheckBox();
            this.cbxAntennaLatitude = new System.Windows.Forms.CheckBox();
            this.cbxAntennaLongitude = new System.Windows.Forms.CheckBox();
            this.cbxAntennaDirectionType = new System.Windows.Forms.CheckBox();
            this.grpFactor.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.TrackBarOpacity)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorAntenna.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // grpFactor
            // 
            this.grpFactor.Controls.Add(this.TrackBarOpacity);
            this.grpFactor.Controls.Add(this.colorAntenna);
            this.grpFactor.Controls.Add(this.label100);
            this.grpFactor.Controls.Add(this.LabelOpacity);
            this.grpFactor.Controls.Add(this.label0);
            this.grpFactor.Controls.Add(this.checkBoxDisplay);
            this.grpFactor.Controls.Add(this.labelColor);
            this.grpFactor.Location = new System.Drawing.Point(23, 17);
            this.grpFactor.Name = "grpFactor";
            this.grpFactor.Size = new System.Drawing.Size(281, 126);
            this.grpFactor.TabIndex = 88;
            this.grpFactor.TabStop = false;
            // 
            // TrackBarOpacity
            // 
            this.TrackBarOpacity.AutoSize = false;
            this.TrackBarOpacity.LargeChange = 32;
            this.TrackBarOpacity.Location = new System.Drawing.Point(71, 54);
            this.TrackBarOpacity.Maximum = 255;
            this.TrackBarOpacity.Name = "TrackBarOpacity";
            this.TrackBarOpacity.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.TrackBarOpacity.Size = new System.Drawing.Size(187, 28);
            this.TrackBarOpacity.TabIndex = 74;
            this.TrackBarOpacity.TickFrequency = 32;
            this.TrackBarOpacity.Value = 255;
            // 
            // colorAntenna
            // 
            this.colorAntenna.EditValue = System.Drawing.Color.Black;
            this.colorAntenna.Location = new System.Drawing.Point(75, 21);
            this.colorAntenna.Name = "colorAntenna";
            this.colorAntenna.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorAntenna.Properties.ShowWebColors = false;
            this.colorAntenna.Size = new System.Drawing.Size(183, 21);
            this.colorAntenna.TabIndex = 84;
            // 
            // label100
            // 
            this.label100.AutoSize = true;
            this.label100.Location = new System.Drawing.Point(205, 94);
            this.label100.Name = "label100";
            this.label100.Size = new System.Drawing.Size(53, 12);
            this.label100.TabIndex = 77;
            this.label100.Text = "100%透明";
            this.label100.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // LabelOpacity
            // 
            this.LabelOpacity.AutoSize = true;
            this.LabelOpacity.Location = new System.Drawing.Point(18, 60);
            this.LabelOpacity.Name = "LabelOpacity";
            this.LabelOpacity.Size = new System.Drawing.Size(47, 12);
            this.LabelOpacity.TabIndex = 73;
            this.LabelOpacity.Text = "透明度:";
            // 
            // label0
            // 
            this.label0.AutoSize = true;
            this.label0.Location = new System.Drawing.Point(73, 94);
            this.label0.Name = "label0";
            this.label0.Size = new System.Drawing.Size(41, 12);
            this.label0.TabIndex = 76;
            this.label0.Text = "不透明";
            this.label0.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // checkBoxDisplay
            // 
            this.checkBoxDisplay.AutoSize = true;
            this.checkBoxDisplay.Location = new System.Drawing.Point(13, 0);
            this.checkBoxDisplay.Name = "checkBoxDisplay";
            this.checkBoxDisplay.Size = new System.Drawing.Size(72, 16);
            this.checkBoxDisplay.TabIndex = 78;
            this.checkBoxDisplay.Text = "显示图元";
            this.checkBoxDisplay.UseVisualStyleBackColor = true;
            // 
            // labelColor
            // 
            this.labelColor.Location = new System.Drawing.Point(28, 23);
            this.labelColor.Name = "labelColor";
            this.labelColor.Size = new System.Drawing.Size(37, 20);
            this.labelColor.TabIndex = 72;
            this.labelColor.Text = "颜色:";
            this.labelColor.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // cbxDrawAntennaLabel
            // 
            this.cbxDrawAntennaLabel.AutoSize = true;
            this.cbxDrawAntennaLabel.Location = new System.Drawing.Point(13, 0);
            this.cbxDrawAntennaLabel.Name = "cbxDrawAntennaLabel";
            this.cbxDrawAntennaLabel.Size = new System.Drawing.Size(72, 16);
            this.cbxDrawAntennaLabel.TabIndex = 90;
            this.cbxDrawAntennaLabel.Text = "显示标签";
            this.cbxDrawAntennaLabel.UseVisualStyleBackColor = true;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.btnFont);
            this.groupBox1.Controls.Add(this.cbxDrawAntennaLabel);
            this.groupBox1.Controls.Add(this.cbxAntennaDescription);
            this.groupBox1.Controls.Add(this.cbxAntennaAltitude);
            this.groupBox1.Controls.Add(this.cbxAntennaDownward);
            this.groupBox1.Controls.Add(this.cbxAntennaDirection);
            this.groupBox1.Controls.Add(this.cbxAntennaDirectionType);
            this.groupBox1.Controls.Add(this.cbxAntennaLatitude);
            this.groupBox1.Controls.Add(this.cbxAntennaLongitude);
            this.groupBox1.Location = new System.Drawing.Point(23, 149);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(281, 116);
            this.groupBox1.TabIndex = 89;
            this.groupBox1.TabStop = false;
            // 
            // btnFont
            // 
            this.btnFont.Location = new System.Drawing.Point(183, 22);
            this.btnFont.Name = "btnFont";
            this.btnFont.Size = new System.Drawing.Size(75, 23);
            this.btnFont.TabIndex = 91;
            this.btnFont.Text = "字体...";
            // 
            // cbxAntennaDescription
            // 
            this.cbxAntennaDescription.AutoSize = true;
            this.cbxAntennaDescription.Location = new System.Drawing.Point(17, 88);
            this.cbxAntennaDescription.Name = "cbxAntennaDescription";
            this.cbxAntennaDescription.Size = new System.Drawing.Size(48, 16);
            this.cbxAntennaDescription.TabIndex = 10;
            this.cbxAntennaDescription.Text = "描述";
            this.cbxAntennaDescription.UseVisualStyleBackColor = true;
            // 
            // cbxAntennaAltitude
            // 
            this.cbxAntennaAltitude.AutoSize = true;
            this.cbxAntennaAltitude.Location = new System.Drawing.Point(87, 66);
            this.cbxAntennaAltitude.Name = "cbxAntennaAltitude";
            this.cbxAntennaAltitude.Size = new System.Drawing.Size(48, 16);
            this.cbxAntennaAltitude.TabIndex = 7;
            this.cbxAntennaAltitude.Text = "高度";
            this.cbxAntennaAltitude.UseVisualStyleBackColor = true;
            // 
            // cbxAntennaDownward
            // 
            this.cbxAntennaDownward.AutoSize = true;
            this.cbxAntennaDownward.Location = new System.Drawing.Point(17, 66);
            this.cbxAntennaDownward.Name = "cbxAntennaDownward";
            this.cbxAntennaDownward.Size = new System.Drawing.Size(60, 16);
            this.cbxAntennaDownward.TabIndex = 6;
            this.cbxAntennaDownward.Text = "下倾角";
            this.cbxAntennaDownward.UseVisualStyleBackColor = true;
            // 
            // cbxAntennaDirection
            // 
            this.cbxAntennaDirection.AutoSize = true;
            this.cbxAntennaDirection.Location = new System.Drawing.Point(17, 44);
            this.cbxAntennaDirection.Name = "cbxAntennaDirection";
            this.cbxAntennaDirection.Size = new System.Drawing.Size(60, 16);
            this.cbxAntennaDirection.TabIndex = 5;
            this.cbxAntennaDirection.Text = "方向角";
            this.cbxAntennaDirection.UseVisualStyleBackColor = true;
            // 
            // cbxAntennaLatitude
            // 
            this.cbxAntennaLatitude.AutoSize = true;
            this.cbxAntennaLatitude.Location = new System.Drawing.Point(87, 22);
            this.cbxAntennaLatitude.Name = "cbxAntennaLatitude";
            this.cbxAntennaLatitude.Size = new System.Drawing.Size(48, 16);
            this.cbxAntennaLatitude.TabIndex = 3;
            this.cbxAntennaLatitude.Text = "纬度";
            this.cbxAntennaLatitude.UseVisualStyleBackColor = true;
            // 
            // cbxAntennaLongitude
            // 
            this.cbxAntennaLongitude.AutoSize = true;
            this.cbxAntennaLongitude.Location = new System.Drawing.Point(17, 22);
            this.cbxAntennaLongitude.Name = "cbxAntennaLongitude";
            this.cbxAntennaLongitude.Size = new System.Drawing.Size(48, 16);
            this.cbxAntennaLongitude.TabIndex = 2;
            this.cbxAntennaLongitude.Text = "经度";
            this.cbxAntennaLongitude.UseVisualStyleBackColor = true;
            // 
            // cbxAntennaDirectionType
            // 
            this.cbxAntennaDirectionType.AutoSize = true;
            this.cbxAntennaDirectionType.Location = new System.Drawing.Point(87, 44);
            this.cbxAntennaDirectionType.Name = "cbxAntennaDirectionType";
            this.cbxAntennaDirectionType.Size = new System.Drawing.Size(72, 16);
            this.cbxAntennaDirectionType.TabIndex = 4;
            this.cbxAntennaDirectionType.Text = "天线类型";
            this.cbxAntennaDirectionType.UseVisualStyleBackColor = true;
            // 
            // MapNRCellLayerAntennaProperties
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.grpFactor);
            this.Name = "MapNRCellLayerAntennaProperties";
            this.Size = new System.Drawing.Size(498, 443);
            this.grpFactor.ResumeLayout(false);
            this.grpFactor.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.TrackBarOpacity)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorAntenna.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox grpFactor;
        private System.Windows.Forms.TrackBar TrackBarOpacity;
        private DevExpress.XtraEditors.ColorEdit colorAntenna;
        private System.Windows.Forms.Label label100;
        private System.Windows.Forms.Label LabelOpacity;
        private System.Windows.Forms.Label label0;
        private System.Windows.Forms.CheckBox checkBoxDisplay;
        private System.Windows.Forms.Label labelColor;
        private System.Windows.Forms.CheckBox cbxDrawAntennaLabel;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.CheckBox cbxAntennaDescription;
        private System.Windows.Forms.CheckBox cbxAntennaAltitude;
        private System.Windows.Forms.CheckBox cbxAntennaDownward;
        private System.Windows.Forms.CheckBox cbxAntennaDirection;
        private System.Windows.Forms.CheckBox cbxAntennaLatitude;
        private System.Windows.Forms.CheckBox cbxAntennaLongitude;
        private DevExpress.XtraEditors.SimpleButton btnFont;
        private System.Windows.Forms.CheckBox cbxAntennaDirectionType;
    }
}
