﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc;
using System.Windows.Forms;
using MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas;
using MasterCom.Util;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;
using DevExpress.XtraEditors;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc.CheckCellOccupation;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTCheckLteFddCellOccupyByRegion : ZTCheckCellOccupyByRegion
    {
        private static ZTCheckLteFddCellOccupyByRegion instance = null;
        public static new ZTCheckLteFddCellOccupyByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTCheckLteFddCellOccupyByRegion();
                    }
                }
            }
            return instance;
        }
        public ZTCheckLteFddCellOccupyByRegion()
            : base()
        {
            isLteFdd = true;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26013, this.Name);
        }
    }
    public class ZTCheckLteFddCellOccupyByFile : ZTCheckCellOccupyByFile
    {
        private static ZTCheckLteFddCellOccupyByFile instance = null;
        public static new ZTCheckLteFddCellOccupyByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTCheckLteFddCellOccupyByFile();
                    }
                }
            }
            return instance;
        }
        public ZTCheckLteFddCellOccupyByFile()
            : base()
        {
            isLteFdd = true;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26013, this.Name);
        }
    }

    public class ZTCheckLteFddCellOccupyByRegion_VOLTE : ZTCheckLteFddCellOccupyByRegion
    {
        public ZTCheckLteFddCellOccupyByRegion_VOLTE()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }
        public override string Name
        {
            get { return "VOLTE_FDD小区占用核查(按区域)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30005, this.Name);
        }
    }
    public class ZTCheckLteFddCellOccupyByFile_VOLTE : ZTCheckLteFddCellOccupyByFile
    {
        public ZTCheckLteFddCellOccupyByFile_VOLTE()
            : base()
        {
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_FDD_VOLTE);
            ServiceTypes.Add(ServiceType.SER_LTE_FDD_VIDEO_VOLTE);
        }
        public override string Name
        {
            get { return "VOLTE_FDD小区占用核查(按文件)"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 30000, 30005, this.Name);
        }
    }
}
