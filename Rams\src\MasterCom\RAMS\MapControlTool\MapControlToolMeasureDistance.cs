﻿using System;
using System.Collections.Generic;
using System.Text;
using AxMapWinGIS;
using System.Drawing;
using MapWinGIS;
using MasterCom.MTGis;
using System.Drawing.Drawing2D;
using MasterCom.RAMS.Func;
using GMap.NET;
using MasterCom.RAMS.ExMap;

namespace MasterCom.RAMS.MapControlTool
{
    public class MapControlToolMeasureDistance
    {
        private readonly AxMap mapControl;
        //经纬度坐标
        public List<DbPoint> MeasurePoints { get; set; }
        private bool isActive = false;
        public bool IsActive
        {
            get { return isActive; }
        }
        private readonly Font fontMeasure;
        private readonly Pen linePen;
        private readonly MapOperation mapOP;
        public bool MeasureStarted { get; set; } = false;
        public bool IsShowAngle { get; set; } = false;
        private readonly MapForm mf;
        public MapControlToolMeasureDistance(MapForm mapForm, AxMap map)
        {
            mapControl = map;
            mapOP = new MapOperation(mapControl);
            fontMeasure = new Font("宋体", 11, FontStyle.Bold);
            MeasurePoints = new List<DbPoint>();
            linePen = new Pen(Color.Green, 2);
            MasterCom.RAMS.Model.MainModel.GetInstance().ClearDataEvent += new EventHandler(mapForm_ClearDataEvent);
            mf = Model.MainModel.GetInstance().MainForm.GetMapForm();
        }

        void mapForm_ClearDataEvent(object sender, EventArgs e)
        {
            MeasurePoints.Clear();
        }


        public void Activate()
        {
            if (!isActive)
            {
                mapControl.MouseDownEvent += mapcontrol_MouseDownEvent;
                mapControl.DblClick += new EventHandler(mapControl_DblClick);
            }
            isActive = true;
            mapControl.CursorMode = tkCursorMode.cmNone;
            mapControl.MapCursor = tkCursor.crsrCross;
        }

        void mapControl_DblClick(object sender, EventArgs e)
        {
            finishDraw();
        }

        public void Deactivate()
        {
            if (!isActive) return;
            MeasureStarted = false;
            mapControl.MouseMoveEvent -= mapcontrol_MouseMoveEvent;
            mapControl.MouseDownEvent -= mapcontrol_MouseDownEvent;
            mapControl.DblClick -= mapControl_DblClick;
            isActive = false;
        }

        /// <summary>
        /// add measurepoint when mouse down
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        void mapcontrol_MouseDownEvent(object sender, _DMapEvents_MouseDownEvent e)
        {
            Console.WriteLine("Down");
            mapControl.MouseMoveEvent -= mapcontrol_MouseMoveEvent;
            if (e.button != 1)//非鼠标左键，开始新的测量点
            {
                finishDraw();
            }
            else
            {
                if (!MeasureStarted)
                {
                    MeasurePoints.Clear();
                    if (mf != null)
                    {
                        mf.updateMap();
                    }
                }
                mapControl.MouseMoveEvent += mapcontrol_MouseMoveEvent;
                MeasureStarted = true;
                double longi = 0;
                double lati = 0;
                mapControl.PixelToProj(e.x, e.y, ref longi, ref lati);
                MeasurePoints.Add(new DbPoint(longi, lati));
            }
        }

        void finishDraw()
        {
            mapControl.MouseMoveEvent -= mapcontrol_MouseMoveEvent;
            MasterCom.RAMS.Model.MainModel.GetInstance().MainForm.GetMapForm().updateMap(); //刷新地图
            MeasureStarted = false;
        }

        /// <summary>
        /// 供google map 调用，结束本次测量
        /// </summary>
        public void FinishMeasure()
        {
            MeasureStarted = false;
        }

        void mapcontrol_MouseMoveEvent(object sender, _DMapEvents_MouseMoveEvent e)
        {
            if (MeasurePoints.Count == 0)
            {
                return;
            }
            PointF[] tempArr;
            PointF[] pArr = new PointF[MeasurePoints.Count + 1];
            mapOP.ToDisplay(MeasurePoints.ToArray(), out tempArr);
            if (tempArr[tempArr.Length - 1].X == e.x && tempArr[tempArr.Length - 1].Y == e.y)
            {
                return;
            }
            tempArr.CopyTo(pArr, 0);
            pArr[MeasurePoints.Count] = new PointF(e.x, e.y);//当前鼠标位置点坐标
            GraphicsPath path = new GraphicsPath();
            path.AddLines(pArr);
            path.Widen(linePen);
            Region invRegion = new Region(mapControl.ClientRectangle);
            invRegion.Exclude(path);
            mapControl.Invalidate(invRegion);
            mapControl.Update();
            List<DbPoint> points = new List<DbPoint>();
            points.AddRange(MeasurePoints);
            double x = 0;
            double y = 0;
            mapControl.PixelToProj(e.x, e.y, ref x, ref y);
            points.Add(new DbPoint(x, y));
            Graphics g = mapControl.CreateGraphics();
            g.SmoothingMode = SmoothingMode.AntiAlias;
            drawMapWinGisMeasureLines(g, points);
            g.Dispose();
        }

        public void Draw(Graphics g)
        {
            drawMapWinGisMeasureLines(g, MeasurePoints);
        }

        /// <summary>
        /// 提供google map调用画测量线
        /// </summary>
        /// <param name="g"></param>
        /// <param name="googlePoints"></param>
        public void Draw(ExMap.MTExGMap exMap, Graphics g, List<PointLatLng> googlePoints)
        {
            if (googlePoints == null || googlePoints.Count < 2)
            {
                return;
            }
            g.SmoothingMode = SmoothingMode.AntiAlias;
            //System.Drawing.Point[] pntArr = new System.Drawing.Point[googlePoints.Count]
            MeasurePoints = new List<DbPoint>();//经纬度坐标
            double distanceTotal = 0;
            SolidBrush solidBrush = new SolidBrush(Color.FromArgb(200, Color.White));
            for (int i = 0; i < googlePoints.Count; i++)
            {
                PointLatLng p = googlePoints[i];
                GPoint gPnt = exMap.FromLatLngToLocalAdaptered(p);
                MeasurePoints.Add(new DbPoint(p.Lng, p.Lat));
                if (i >= 1)
                {
                    PointLatLng prePnt = googlePoints[i - 1];
                    GPoint preGPnt = exMap.FromLatLngToLocalAdaptered(prePnt);
                    g.DrawLine(linePen, preGPnt.X, preGPnt.Y, gPnt.X, gPnt.Y);
                    double distance = MasterCom.Util.MathFuncs.GetDistance(p.Lng, p.Lat, prePnt.Lng, prePnt.Lat);
                    distanceTotal += distance;
                    string str = "" + (int)distance + "m 总长:" + (int)distanceTotal + "m";
                    SizeF sizeF = g.MeasureString(str, fontMeasure);
                    g.FillRectangle(solidBrush, gPnt.X, gPnt.Y, sizeF.Width, sizeF.Height);
                    g.DrawString(str, fontMeasure, Brushes.Orange, gPnt.X, gPnt.Y);
                }

            }
        }

        private void drawMapWinGisMeasureLines(Graphics g, List<DbPoint> points)
        {
            if (points == null || points.Count < 2)
            {
                return;
            }
            SolidBrush solidBrush = new SolidBrush(Color.FromArgb(200, Color.White));

            PointF[] pArr;
            mapOP.ToDisplay(points.ToArray(), out pArr);
            g.DrawLines(linePen, pArr);
            double distanceTotal = 0;

            double distancePre = 0.0;
            double distanceTotalPre = 0.0;
            for (int i = 1; i < points.Count; i++)
            {
                if (IsShowAngle)
                {
                    //需要同时刷新2个点的数据
                    double angle = 0.0;
                    double distance = 0.0;

                    distance = MasterCom.Util.MathFuncs.GetDistance(points[i].x, points[i].y, points[i - 1].x, points[i - 1].y);
                    distanceTotal += distance;
                    if (i == 1)
                    {
                        angle = getAngle(points[i - 1], points[i]);
                        //绘制起始点
                        string strFir = string.Format("双击鼠标左键可以结束本次测量！\n角度:{0}°", angle);
                        SizeF sizeFir = g.MeasureString(strFir, fontMeasure);
                        g.FillRectangle(solidBrush, pArr[0].X, pArr[0].Y, sizeFir.Width, sizeFir.Height);
                        g.DrawString(strFir, fontMeasure, Brushes.Orange, pArr[0]);
                        //绘制第二个点
                        string strSec = string.Format("{0}m 总长:{1}m", (int)distance, (int)distanceTotal);
                        SizeF sizeSec = g.MeasureString(strSec, fontMeasure);
                        g.FillRectangle(solidBrush, pArr[i].X, pArr[i].Y, sizeSec.Width, sizeSec.Height);
                        g.DrawString(strSec, fontMeasure, Brushes.Orange, pArr[i]);
                    }
                    else
                    {
                        angle = getAngle(points[i - 1], points[i], points[i - 2]);
                        //绘制前一个点
                        string strPre = string.Format("{0}m 总长:{1}m\n角度:{2}°", (int)distancePre, (int)distanceTotalPre, angle);
                        SizeF sizePre = g.MeasureString(strPre, fontMeasure);
                        g.FillRectangle(solidBrush, pArr[i-1].X, pArr[i-1].Y, sizePre.Width, sizePre.Height);
                        g.DrawString(strPre, fontMeasure, Brushes.Orange, pArr[i-1]);
                        //绘制后一个点
                        string strNext = string.Format("{0}m 总长:{1}m", (int)distance, (int)distanceTotal);
                        SizeF sizeNext = g.MeasureString(strNext, fontMeasure);
                        g.FillRectangle(solidBrush, pArr[i].X, pArr[i].Y, sizeNext.Width, sizeNext.Height);
                        g.DrawString(strNext, fontMeasure, Brushes.Orange, pArr[i]);
                    }
                    distancePre = distance;
                    distanceTotalPre = distanceTotal;
                }
                else
                {
                    if (i == 1)
                    {
                        string strFir = "双击鼠标左键可以结束本次测量！";
                        SizeF sizeFir = g.MeasureString(strFir, fontMeasure);
                        g.FillRectangle(solidBrush, pArr[0].X, pArr[0].Y, sizeFir.Width, sizeFir.Height);
                        g.DrawString(strFir, fontMeasure, Brushes.Orange, pArr[0]);
                    }
                    double distance = MasterCom.Util.MathFuncs.GetDistance(points[i].x, points[i].y, points[i - 1].x, points[i - 1].y);
                    distanceTotal += distance;
                    string str = string.Empty;

                    str = string.Format("{0}m 总长:{1}m", (int)distance, (int)distanceTotal);
                    SizeF sizeF = g.MeasureString(str, fontMeasure);
                    g.FillRectangle(solidBrush, pArr[i].X, pArr[i].Y, sizeF.Width, sizeF.Height);
                    g.DrawString(str, fontMeasure, Brushes.Orange, pArr[i]);
                }
            }
        }

        /// <summary>
        /// 获取连线的角度
        /// </summary>
        /// <param name="lastPoint">上一个点</param>
        /// <param name="curPoint">当前的点</param>
        /// <param name="pointBeforeLast">上上个点</param>
        /// <returns></returns>
        private double getAngle(DbPoint lastPoint, DbPoint curPoint, DbPoint pointBeforeLast = null)
        {
            double angle;
            if (pointBeforeLast == null)
            {//如果只连了一条线则计算这条线与正北方向顺时针的夹角
                angle = MasterCom.Util.MathFuncs.getAngleFromPointToPoint_D(lastPoint.x, lastPoint.y, curPoint.x, curPoint.y);
            }
            else
            {//计算新连的线与上一次连线的夹角
                PointF o, a, b;
                o = new PointF((float)lastPoint.x, (float)lastPoint.y);
                a = new PointF((float)pointBeforeLast.x, (float)pointBeforeLast.y);
                b = new PointF((float)curPoint.x, (float)curPoint.y);
                angle = MasterCom.Util.MathFuncs.CalAngle(o, a, b);
            }
            return Math.Round(angle, 2);
        }
    }
}
