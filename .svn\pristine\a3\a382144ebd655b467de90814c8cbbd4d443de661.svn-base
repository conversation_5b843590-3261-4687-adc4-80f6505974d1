﻿using MasterCom.RAMS.Frame;
using MasterCom.RAMS.ZTFunc.KPISheet;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraGrid.Columns;

namespace MasterCom.RAMS.ZTFunc.KPISheet
{
    public partial class KPIForm : ChildForm
    {
        string GSM = "GSM";
        string TD = "TD";
        string LTE = "LTE";

        //各网络对应的测试名
        List<string> gsmTestNameList = new List<string>();
        List<string> tdTestNameList = new List<string>();
        List<string> lteTestNameList= new List<string>();

        //各个网络对应测试名称和模板的信息
        Dictionary<string, TemplateInfo> infoGSM = new Dictionary<string, TemplateInfo>();
        Dictionary<string, TemplateInfo> infoTD = new Dictionary<string, TemplateInfo>();
        Dictionary<string, TemplateInfo> infoLTE = new Dictionary<string, TemplateInfo>();

        /// <summary>
        /// 类型-测试名-模板信息
        /// </summary>
        Dictionary<string, Dictionary<string, TestNameInfo>> testNameDic = new Dictionary<string, Dictionary<string, TestNameInfo>>();
        Dictionary<string, TestNameInfo> testNameGSM_Dic = new Dictionary<string, TestNameInfo>();
        Dictionary<string, TestNameInfo> testNameTD_Dic = new Dictionary<string, TestNameInfo>();
        Dictionary<string, TestNameInfo> testNameLTE_Dic = new Dictionary<string, TestNameInfo>();

        string testName = "测试名称";
        string[] testNameArray; //多个测试名称
        string selectedNetType = "GSM";//当前选中的网络类型
        string tableName = ""; //要查询的数据库表名
        KPIColumnOptions selectedTemplate = null;//当前选中的模版

        //存放所有三个网络对应的所有模版信息（第一次加载时用于生成默认模板）
        Dictionary<string, Dictionary<string, TemplateInfo>> allColumnsDic = new Dictionary<string, Dictionary<string, TemplateInfo>>();

        DataTable dtShow = new DataTable(); //查询到的结果表

        public KPIForm()
        {
            InitializeComponent();

            init();
        }

        private void init()
        {
            DIYQueryKPIcfgRecordInfo query = new DIYQueryKPIcfgRecordInfo(MainModel);
            query.Query();//从【tb_kpimng_cfg_recordInfo】测试名称、类型、模板名
            testNameDic = query.testTypeDic;

            DIYQueryKPIcfgTemplateInfo queryTemp = new DIYQueryKPIcfgTemplateInfo(MainModel);
            queryTemp.Query();//从【tb_kpimng_cfg_templateInfo】模板、类型、表名、列名

            //初始化下拉列表框等
            comboBox_testName.Properties.Items.Clear();
            foreach (string type in testNameDic.Keys)
            {
                if (type == GSM)
                {
                    addTypeDic(queryTemp, type, out infoGSM, out testNameGSM_Dic);
                }
                else if (type == TD)
                {
                    addTypeDic(queryTemp, type, out infoTD, out testNameTD_Dic);
                }
                else
                {
                    addTypeDic(queryTemp, type, out infoLTE, out testNameLTE_Dic);
                }
            }

            //默认选中GSM
            foreach (string item in gsmTestNameList)
            {
                comboBox_testName.Properties.Items.Add(item);
            }
            if(comboBox_testName.Properties.Items.Count > 0)
            {
                //默认选中第一个（导入时间最近的）
                comboBox_testName.Text = gsmTestNameList[0];
            }

            //加载窗体完成后加载指标，生成文件
            KPITemplateForm templateForm = new KPITemplateForm(allColumnsDic);
            templateForm.Dispose();
            getTemplate(GSM);//刷新模版下拉框
        }

        private void addTypeDic(DIYQueryKPIcfgTemplateInfo queryTemp, string type, 
            out Dictionary<string, TemplateInfo> info, out Dictionary<string, TestNameInfo> testName_Dic)
        {
            queryTemp.testTypeDic.TryGetValue(type, out info);

            testNameDic.TryGetValue(type, out testName_Dic);
            foreach (string item in testName_Dic.Keys)
            {
                lteTestNameList.Add(item);
            }
            allColumnsDic.Add(type, info);
        }

        /// <summary>
        /// 根据选择的网络类型和测试名（下拉框）称获取相应的模板
        /// </summary>
        /// <param name="netType"></param>
        private void getTemplate(string netType)
        {
            selectedNetType = netType;
            comboBox_template.Properties.Items.Clear();

            Dictionary<string, TestNameInfo> testNameTempDic;
            TemplateInfo templateInfo;
            TestNameInfo testNameInfo = new TestNameInfo();
            if (testNameDic.TryGetValue(netType, out testNameTempDic)
                && comboBox_testName.Properties.Items.Count > 0 && testNameArray[0] != "")//类型、测试名与模板
            {
                testNameTempDic.TryGetValue(testNameArray[0], out testNameInfo);
            }
            if(testNameInfo != null && testNameInfo.TemplateName != null)
            {
                templateInfo = getTemplateInfo(netType, testNameInfo);
                tableName = templateInfo.TableName; //要查询的表名，与模板对应
                List<KPITemplate> templateList = KPITemplateManager.Instance.Templates;
                foreach (KPITemplate tempItem in templateList)
                {
                    addNetTypeTemplate(netType, templateInfo, tempItem);
                }
            }

            comboBox_template.SelectedIndex = 0;
            selectedTemplate = comboBox_template.SelectedItem as KPIColumnOptions;
            if(selectedTemplate != null)
            {
                NetTypeName.getInstance().ColItems.Clear();
                NetTypeName.getInstance().ColItems.Add(testName);
                foreach (string item in selectedTemplate.ColumnsList)
                {
                    //添加列名，查询用
                    NetTypeName.getInstance().ColItems.Add(item);
                }
            }
        }

        private TemplateInfo getTemplateInfo(string netType, TestNameInfo testNameInfo)
        {
            TemplateInfo templateInfo;
            if (netType == GSM)
            {
                infoGSM.TryGetValue(testNameInfo.TemplateName, out templateInfo);
            }
            else if (netType == TD)
            {
                infoTD.TryGetValue(testNameInfo.TemplateName, out templateInfo);
            }
            else
            {
                infoLTE.TryGetValue(testNameInfo.TemplateName, out templateInfo);
            }

            return templateInfo;
        }

        private void addNetTypeTemplate(string netType, TemplateInfo templateInfo, KPITemplate tempItem)
        {
            if (tempItem.NetType == netType)
            {
                foreach (KPISubTemplate subItem in tempItem.TemplateList)
                {
                    addTemplateCol(templateInfo, subItem);
                }
            }
        }

        private void addTemplateCol(TemplateInfo templateInfo, KPISubTemplate subItem)
        {
            if (subItem.templateName == templateInfo.TemplateName)
            {
                foreach (KPIColumnOptions colItem in subItem.SubTemplateList)
                {
                    comboBox_template.Properties.Items.Add(colItem);
                }
            }
        }

        private void btn_searchKPI_Click(object sender, EventArgs e)
        {
            if(comboBox_testName.Text == "")
            {
                MessageBox.Show("请选择测试名称");
                return;
            }

            //检查测试名称模板名是否相同
            if(!checkTestName())
            {
                return;
            }

            dtShow = null;

            if (selectedTemplate != null && selectedTemplate.ColumnsList.Count > 0)
            {
                dtShow = new DataTable();
                DataColumn dc = null;

                dc = new DataColumn(testName);
                dtShow.Columns.Add(dc);
                //添加列名
                foreach (string item in selectedTemplate.ColumnsList)
                {
                    dc = new DataColumn(item);
                    dtShow.Columns.Add(dc);
                }
                getTableInfo();
            }
            else
            {
                MessageBox.Show("请选择测试名称");
                gridViewKPI.Columns.Clear();//清空原有列名，否则重新绑定数据源列名是原来的列名
                gridControlKPI.DataSource = dtShow;
                gridViewKPI.BestFitColumns();
                gridControlKPI.RefreshDataSource();
                refreshViewColCaption();
            }

        }


        /// <summary>
        /// 显示条件下的查询结果
        /// </summary>
        private void getTableInfo()
        {
            DIYQueryKPItemplateTableInfo queryTableInfo = new DIYQueryKPItemplateTableInfo(MainModel, testNameArray, tableName);
            queryTableInfo.Query();//queryTableInfo.paramDic【测试轮次】和【所有列名List】

            DataRow dr = null;
            if (queryTableInfo.paramDic.Count > 0)
            {
                //遍历查询结果
                List<string> list = new List<string>();
                foreach (string item in queryTableInfo.paramDic.Keys)
	            {
                    if (queryTableInfo.paramDic.TryGetValue(item, out list))
                    {
                        break;
                    }
	            }
                int itemCount = list.Count;//获取记录数

                for (int i = 0; i < itemCount; i++)//遍历所有记录
                {
                    dr = dtShow.NewRow();
                    dr[testName] = queryTableInfo.paramDic[testName][i];//测试名单独加上，自定义模板中不一定包含测试名称
                    foreach (string item in selectedTemplate.ColumnsList)//根据模版显示出需要的列
                    {
                        dr[item] = queryTableInfo.paramDic[item][i];
                    }
                    dtShow.Rows.Add(dr);
                }
            }
            else
            {
                MessageBox.Show("查询不到数据，请检查条件");
                return;
            }
            gridViewKPI.Columns.Clear();//清空原有列名，否则重新绑定数据源列名是原来的列名
            gridControlKPI.DataSource = dtShow;
            gridViewKPI.BestFitColumns();
            gridControlKPI.RefreshDataSource();
            gridViewKPI.PopulateColumns();
            refreshViewColCaption();
        }

        private void refreshViewColCaption()
        {
            foreach (GridColumn col in gridViewKPI.Columns)
            {
                col.Caption = col.FieldName;
                col.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
                col.AppearanceHeader.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            }
        }

        private void gridViewKPI_CustomDrawRowIndicator(object sender, DevExpress.XtraGrid.Views.Grid.RowIndicatorCustomDrawEventArgs e)
        {
            if (e.Info.IsRowIndicator && e.RowHandle >= 0)
            {
                e.Info.DisplayText = (e.RowHandle + 1).ToString();
            }
        }

        private void btn_kpiTemplet_Click(object sender, EventArgs e)
        {
            CustomReportForm frm = new CustomReportForm(null);
            frm.ShowDialog();
        }

        private void radioBtn_GSM_MouseClick(object sender, MouseEventArgs e)
        {
            comboBox_testName.Properties.Items.Clear();
            foreach (string item in gsmTestNameList)
            {
                comboBox_testName.Properties.Items.Add(item);
            }
            comboBox_testName.Text = gsmTestNameList[0];//默认勾选第一项
            getTemplate(GSM);
        }

        private void radioBtn_TD_MouseClick(object sender, MouseEventArgs e)
        {
            comboBox_testName.Properties.Items.Clear();
            foreach (string item in tdTestNameList)
            {
                comboBox_testName.Properties.Items.Add(item);
            }
            comboBox_testName.Text = tdTestNameList[0];
            getTemplate(TD);
        }

        private void radioBtn_LTE_MouseClick(object sender, MouseEventArgs e)
        {
            comboBox_testName.Properties.Items.Clear();
            foreach (string item in lteTestNameList)
            {
                comboBox_testName.Properties.Items.Add(item);
            }
            comboBox_testName.Text = lteTestNameList[0];
            getTemplate(LTE);
        }

        private void comboBox_templet_SelectedIndexChanged(object sender, EventArgs e)
        {
            selectedTemplate = comboBox_template.SelectedItem as KPIColumnOptions;
        }

        private void ExportToExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridViewKPI);
        }

        private void comboBox_testName_EditValueChanged(object sender, EventArgs e)
        {
            string s = comboBox_testName.Text;//获取测试名称下拉框显示的字符串
            testNameArray = s.Split(new string[] { ", " }, StringSplitOptions.None);//分割
            checkTestName();
            getTemplate(selectedNetType);
        }
            
        /// <summary>
        /// 判断多个测试名称的模板是否相同
        /// </summary>
        /// <returns></returns>
        private bool checkTestName()
        {
            int flag = -1;//等于0表示模板冲突

            //选择多项时判断
            if (testNameArray.Length > 1)
            {
                int count = testNameArray.Length;
                if (selectedNetType == GSM)
                {
                    judgeSame(ref flag, count, testNameGSM_Dic);
                }
                else if (selectedNetType == TD)
                {
                    judgeSame(ref flag, count, testNameTD_Dic);
                }
                else
                {
                    judgeSame(ref flag, count, testNameLTE_Dic);
                }
            }

            if (flag == 0)
            {
                MessageBox.Show("测试名称使用了不同的模板，请重新选择");
                return false;
            }
            return true;
        }

        private void judgeSame(ref int flag, int count, Dictionary<string, TestNameInfo> dic)
        {
            string tempStr = "";
            TestNameInfo nameInfo;
            for (int i = 0; i < count; i++)
            {
                dic.TryGetValue(testNameArray[i], out nameInfo);
                if (tempStr != "")
                {
                    if (tempStr != nameInfo.TemplateName)
                    {
                        flag = 0;
                        break;
                    }
                    else
                    {
                        continue;
                    }
                }
                tempStr = nameInfo.TemplateName;
            }
        }
    }

    /// <summary>
    /// 网络类型枚举（包括类型枚举、类型列名）
    /// </summary>
    public class NetTypeName
    {
        private static NetTypeName instance;
        public static NetTypeName getInstance()
        {
            if(instance == null)
            {
                instance = new NetTypeName();
            }
            return instance;
        }

        public enum NetType
        {
            TABLE_GSM = 1,
            TABLE_TD = 2,
            TABLE_LTE = 3
        }

        public string defaultTemplateName { get; set; } = "默认模板";//默认模板名后缀，待定

        /// <summary>
        /// 选中子模板包含的列名
        /// </summary>
        public List<string> ColItems { get; set; } = new List<string>();
    }
}
