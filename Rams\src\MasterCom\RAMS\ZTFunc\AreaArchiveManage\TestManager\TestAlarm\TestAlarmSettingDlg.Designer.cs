﻿namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    partial class TestAlarmSettingDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.simpleButtonCancel = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonOK = new DevExpress.XtraEditors.SimpleButton();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.labelUnAchieve = new System.Windows.Forms.Label();
            this.labelAchieve = new System.Windows.Forms.Label();
            this.groupBoxPermeate = new System.Windows.Forms.GroupBox();
            this.spinEditPermeate = new DevExpress.XtraEditors.SpinEdit();
            this.label5 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.label1 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.spinEditSampleNum = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditTestDays = new DevExpress.XtraEditors.SpinEdit();
            this.label12 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.groupBox2.SuspendLayout();
            this.groupBoxPermeate.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPermeate.Properties)).BeginInit();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditSampleNum.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditTestDays.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // simpleButtonCancel
            // 
            this.simpleButtonCancel.Location = new System.Drawing.Point(197, 288);
            this.simpleButtonCancel.Name = "simpleButtonCancel";
            this.simpleButtonCancel.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonCancel.TabIndex = 25;
            this.simpleButtonCancel.Text = "取消";
            this.simpleButtonCancel.Click += new System.EventHandler(this.simpleButtonCancel_Click);
            // 
            // simpleButtonOK
            // 
            this.simpleButtonOK.Location = new System.Drawing.Point(107, 288);
            this.simpleButtonOK.Name = "simpleButtonOK";
            this.simpleButtonOK.Size = new System.Drawing.Size(75, 23);
            this.simpleButtonOK.TabIndex = 24;
            this.simpleButtonOK.Text = "确定";
            this.simpleButtonOK.Click += new System.EventHandler(this.simpleButtonOK_Click);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.labelUnAchieve);
            this.groupBox2.Controls.Add(this.labelAchieve);
            this.groupBox2.Location = new System.Drawing.Point(18, 197);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(254, 65);
            this.groupBox2.TabIndex = 27;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "颜色";
            // 
            // labelUnAchieve
            // 
            this.labelUnAchieve.BackColor = System.Drawing.Color.Red;
            this.labelUnAchieve.ForeColor = System.Drawing.Color.White;
            this.labelUnAchieve.Location = new System.Drawing.Point(159, 28);
            this.labelUnAchieve.Name = "labelUnAchieve";
            this.labelUnAchieve.Size = new System.Drawing.Size(60, 18);
            this.labelUnAchieve.TabIndex = 10;
            this.labelUnAchieve.Text = "预警";
            this.labelUnAchieve.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // labelAchieve
            // 
            this.labelAchieve.BackColor = System.Drawing.Color.Green;
            this.labelAchieve.ForeColor = System.Drawing.Color.White;
            this.labelAchieve.Location = new System.Drawing.Point(64, 28);
            this.labelAchieve.Name = "labelAchieve";
            this.labelAchieve.Size = new System.Drawing.Size(60, 18);
            this.labelAchieve.TabIndex = 10;
            this.labelAchieve.Text = "未预警";
            this.labelAchieve.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // groupBoxPermeate
            // 
            this.groupBoxPermeate.Controls.Add(this.spinEditPermeate);
            this.groupBoxPermeate.Controls.Add(this.label5);
            this.groupBoxPermeate.Controls.Add(this.label6);
            this.groupBoxPermeate.Location = new System.Drawing.Point(18, 112);
            this.groupBoxPermeate.Name = "groupBoxPermeate";
            this.groupBoxPermeate.Size = new System.Drawing.Size(254, 72);
            this.groupBoxPermeate.TabIndex = 28;
            this.groupBoxPermeate.TabStop = false;
            this.groupBoxPermeate.Text = "区域测试预警门限";
            // 
            // spinEditPermeate
            // 
            this.spinEditPermeate.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditPermeate.Location = new System.Drawing.Point(122, 35);
            this.spinEditPermeate.Name = "spinEditPermeate";
            this.spinEditPermeate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditPermeate.Properties.Mask.EditMask = "f0";
            this.spinEditPermeate.Size = new System.Drawing.Size(100, 21);
            this.spinEditPermeate.TabIndex = 11;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Font = new System.Drawing.Font("宋体", 9F);
            this.label5.Location = new System.Drawing.Point(21, 39);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(101, 12);
            this.label5.TabIndex = 10;
            this.label5.Text = "村庄测试预警率≥";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(226, 38);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(11, 12);
            this.label6.TabIndex = 9;
            this.label6.Text = "%";
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.label1);
            this.groupBox4.Controls.Add(this.label14);
            this.groupBox4.Controls.Add(this.spinEditSampleNum);
            this.groupBox4.Controls.Add(this.spinEditTestDays);
            this.groupBox4.Controls.Add(this.label12);
            this.groupBox4.Controls.Add(this.label13);
            this.groupBox4.Location = new System.Drawing.Point(18, 12);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(254, 91);
            this.groupBox4.TabIndex = 26;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "村庄测试预警门限";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("宋体", 9F);
            this.label1.Location = new System.Drawing.Point(51, 59);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(71, 12);
            this.label1.TabIndex = 10;
            this.label1.Text = "测试点数≤ ";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Font = new System.Drawing.Font("宋体", 9F);
            this.label14.Location = new System.Drawing.Point(51, 29);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(71, 12);
            this.label14.TabIndex = 10;
            this.label14.Text = "测试天数≤ ";
            // 
            // spinEditSampleNum
            // 
            this.spinEditSampleNum.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditSampleNum.Location = new System.Drawing.Point(122, 56);
            this.spinEditSampleNum.Name = "spinEditSampleNum";
            this.spinEditSampleNum.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditSampleNum.Properties.Mask.EditMask = "f0";
            this.spinEditSampleNum.Size = new System.Drawing.Size(100, 21);
            this.spinEditSampleNum.TabIndex = 6;
            // 
            // spinEditTestDays
            // 
            this.spinEditTestDays.EditValue = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.spinEditTestDays.Location = new System.Drawing.Point(122, 24);
            this.spinEditTestDays.Name = "spinEditTestDays";
            this.spinEditTestDays.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditTestDays.Properties.Mask.EditMask = "f0";
            this.spinEditTestDays.Size = new System.Drawing.Size(100, 21);
            this.spinEditTestDays.TabIndex = 7;
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(227, 59);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(17, 12);
            this.label12.TabIndex = 2;
            this.label12.Text = "个";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(227, 27);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(17, 12);
            this.label13.TabIndex = 3;
            this.label13.Text = "天";
            // 
            // TestAlarmSettingDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(291, 327);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBoxPermeate);
            this.Controls.Add(this.groupBox4);
            this.Controls.Add(this.simpleButtonCancel);
            this.Controls.Add(this.simpleButtonOK);
            this.MaximumSize = new System.Drawing.Size(476, 365);
            this.MinimumSize = new System.Drawing.Size(297, 225);
            this.Name = "TestAlarmSettingDlg";
            this.Text = "测试预警设置";
            this.groupBox2.ResumeLayout(false);
            this.groupBoxPermeate.ResumeLayout(false);
            this.groupBoxPermeate.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPermeate.Properties)).EndInit();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditSampleNum.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditTestDays.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton simpleButtonCancel;
        private DevExpress.XtraEditors.SimpleButton simpleButtonOK;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label labelUnAchieve;
        private System.Windows.Forms.Label labelAchieve;
        private System.Windows.Forms.GroupBox groupBoxPermeate;
        private DevExpress.XtraEditors.SpinEdit spinEditPermeate;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label14;
        private DevExpress.XtraEditors.SpinEdit spinEditSampleNum;
        private DevExpress.XtraEditors.SpinEdit spinEditTestDays;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label13;
    }
}