﻿using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class NRThirdPartyData
    {
        public string BtsName { get; set; }
        public int NodeBID { get; set; }
        public string Address { get; set; }
        public string District { get; set; }

        public double BtsLongitude { get; set; }
        public double BtsLatitude { get; set; }
        public int CellCount { get; set; }
        public int TAC { get; set; }

        public string CellName { get; set; }

        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public double Altitude { get; set; }
        public double Direction { get; set; }
        public double Downtilt { get; set; }
        public double MechanicalTilt { get; set; }
        public double Downward { get; set; }
        public int Channels { get; set; }

        public int CellID { get; set; }
        public int PCI { get; set; }
        public string FreqBand { get; set; }
        public int Freq { get; set; }
        public int SSBFreq { get; set; }
        public string Bandwidth { get; set; }
        public string PRACH { get; set; }
        public string SubFrameRatio { get; set; }
        public string AAUCount { get; set; }
        public string CoreMode { get; set; }
    }


    /// <summary>
    /// 站审数据
    /// </summary>
    class BtsCheck
    {
        /// <summary>
        /// 地址
        /// </summary>
        public string Address { get; set; }
        /// <summary>
        /// 区县
        /// </summary>
        public string District { get; set; }
        public double BtsLongitude { get; set; }
        public double BtsLatitude { get; set; }
        public int CellCount { get; set; }

        public List<BtsCheckCell> CellList { get; set; }
    }

    class BtsCheckCell
    {
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        /// <summary>
        /// 天线挂高
        /// </summary>
        public double Altitude { get; set; }
        /// <summary>
        /// 方位角
        /// </summary>
        public double Direction { get; set; }
        /// <summary>
        /// 电气下倾角
        /// </summary>
        public double Downtilt { get; set; }
        /// <summary>
        /// 机械下倾角
        /// </summary>
        public double MechanicalTilt { get; set; }
        /// <summary>
        /// 总下倾角
        /// </summary>
        public double Downward { get; set; }
        /// <summary>
        /// 收发通道数
        /// </summary>
        public int Channels { get; set; }

    }

    /// <summary>
    /// 无线规划
    /// </summary>
    class NRWirelessPlanning
    {
        public int TAC { get; set; }

        public List<NRWirelessPlanning> CellList { get; set; }
    }

    class NRWirelessPlanningCell
    {
        public int CellID { get; set; }
        public int PCI { get; set; }
        /// <summary>
        /// 频段
        /// </summary>
        public string FreqBand { get; set; }
        /// <summary>
        /// 频点
        /// </summary>
        public string Freq { get; set; }
        /// <summary>
        /// SSB频点
        /// </summary>
        public string SSBFreq { get; set; }
        /// <summary>
        /// 小区带宽（兆）
        /// </summary>
        public string Bandwidth { get; set; }
        /// <summary>
        /// 根序列(PRACH)
        /// </summary>
        public string PRACH { get; set; }
        /// <summary>
        /// 子帧配比
        /// </summary>
        public string SubFrameRatio { get; set; }
        /// <summary>
        /// AAU数量
        /// </summary>
        public string AAUCount { get; set; }
        /// <summary>
        /// 核心网接入模式
        /// </summary>
        public string CoreMode { get; set; }
    }

    /// <summary>
    /// 网管配置
    /// </summary>
    class NetworkConfiguration
    {
        public int NodeBID { get; set; }
        public int CellCount { get; set; }
        public int TAC { get; set; }

        public List<NetworkConfigurationCell> CellList { get; set; }
    }

    class NetworkConfigurationCell
    {
        public int CellID { get; set; }
        public int PCI { get; set; }
        /// <summary>
        /// 频段
        /// </summary>
        public string FreqBand { get; set; }
        /// <summary>
        /// 频点
        /// </summary>
        public string Freq { get; set; }
        /// <summary>
        /// SSB频点
        /// </summary>
        public string SSBFreq { get; set; }
        /// <summary>
        /// 小区带宽（兆）
        /// </summary>
        public string Bandwidth { get; set; }
        /// <summary>
        /// 根序列(PRACH)
        /// </summary>
        public string PRACH { get; set; }
        /// <summary>
        /// 子帧配比
        /// </summary>
        public string SubFrameRatio { get; set; }
        /// <summary>
        /// AAU数量
        /// </summary>
        public string AAUCount { get; set; }
        /// <summary>
        /// 核心网接入模式
        /// </summary>
        public string CoreMode { get; set; }
    }

    /// <summary>
    /// 天资平台
    /// </summary>
    class AntennaAttitude
    {
        public double BtsLongitude { get; set; }
        public double BtsLatitude { get; set; }

        public List<AntennaAttitudeCell> CellList { get; set; }
    }

    class AntennaAttitudeCell
    {
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        /// <summary>
        /// 天线挂高
        /// </summary>
        public double Altitude { get; set; }
        /// <summary>
        /// 方位角
        /// </summary>
        public double Direction { get; set; }
        /// <summary>
        /// 电气下倾角
        /// </summary>
        public double Downtilt { get; set; }
        /// <summary>
        /// 机械下倾角
        /// </summary>
        public double MechanicalTilt { get; set; }
        /// <summary>
        /// 总下倾角
        /// </summary>
        public double Downward { get; set; }
        /// <summary>
        /// 收发通道数
        /// </summary>
        public int Channels { get; set; }
    }
}
