﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRPingPangSettingForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.numSpeedLimitMax = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.numSpeedLimitMin = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.chkSpeedLimit = new DevExpress.XtraEditors.CheckEdit();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.numTimeLimit = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.chkAnaLTE = new System.Windows.Forms.CheckBox();
            ((System.ComponentModel.ISupportInitialize)(this.numSpeedLimitMax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSpeedLimitMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSpeedLimit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTimeLimit.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // numSpeedLimitMax
            // 
            this.numSpeedLimitMax.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numSpeedLimitMax.Location = new System.Drawing.Point(177, 94);
            this.numSpeedLimitMax.Name = "numSpeedLimitMax";
            this.numSpeedLimitMax.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSpeedLimitMax.Properties.Appearance.Options.UseFont = true;
            this.numSpeedLimitMax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSpeedLimitMax.Properties.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numSpeedLimitMax.Properties.IsFloatValue = false;
            this.numSpeedLimitMax.Properties.Mask.EditMask = "N00";
            this.numSpeedLimitMax.Properties.MaxValue = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.numSpeedLimitMax.Size = new System.Drawing.Size(78, 20);
            this.numSpeedLimitMax.TabIndex = 20;
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(261, 93);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(0, 12);
            this.labelControl3.TabIndex = 19;
            // 
            // numSpeedLimitMin
            // 
            this.numSpeedLimitMin.EditValue = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numSpeedLimitMin.Location = new System.Drawing.Point(28, 95);
            this.numSpeedLimitMin.Name = "numSpeedLimitMin";
            this.numSpeedLimitMin.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSpeedLimitMin.Properties.Appearance.Options.UseFont = true;
            this.numSpeedLimitMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numSpeedLimitMin.Properties.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numSpeedLimitMin.Properties.IsFloatValue = false;
            this.numSpeedLimitMin.Properties.Mask.EditMask = "N00";
            this.numSpeedLimitMin.Properties.MaxValue = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.numSpeedLimitMin.Size = new System.Drawing.Size(78, 20);
            this.numSpeedLimitMin.TabIndex = 18;
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(103, 98);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(66, 12);
            this.labelControl4.TabIndex = 17;
            this.labelControl4.Text = " ≤ 车速 ≤";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(182, 24);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(12, 12);
            this.labelControl2.TabIndex = 16;
            this.labelControl2.Text = "秒";
            // 
            // chkSpeedLimit
            // 
            this.chkSpeedLimit.Location = new System.Drawing.Point(26, 63);
            this.chkSpeedLimit.Name = "chkSpeedLimit";
            this.chkSpeedLimit.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkSpeedLimit.Properties.Appearance.Options.UseFont = true;
            this.chkSpeedLimit.Properties.Caption = "启用车速限制（单位：公里/小时）";
            this.chkSpeedLimit.Size = new System.Drawing.Size(203, 19);
            this.chkSpeedLimit.TabIndex = 15;
            this.chkSpeedLimit.CheckedChanged += new System.EventHandler(this.chkSpeedLimit_CheckedChanged);
            // 
            // btnCancel
            // 
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(185, 176);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 14;
            this.btnCancel.Text = "取消";
            // 
            // btnOK
            // 
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.Location = new System.Drawing.Point(94, 176);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 13;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // numTimeLimit
            // 
            this.numTimeLimit.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numTimeLimit.Location = new System.Drawing.Point(98, 21);
            this.numTimeLimit.Name = "numTimeLimit";
            this.numTimeLimit.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numTimeLimit.Properties.Appearance.Options.UseFont = true;
            this.numTimeLimit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numTimeLimit.Properties.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numTimeLimit.Properties.IsFloatValue = false;
            this.numTimeLimit.Properties.Mask.EditMask = "N00";
            this.numTimeLimit.Properties.MaxValue = new decimal(new int[] {
            60,
            0,
            0,
            0});
            this.numTimeLimit.Size = new System.Drawing.Size(78, 20);
            this.numTimeLimit.TabIndex = 12;
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(28, 24);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(66, 12);
            this.labelControl1.TabIndex = 11;
            this.labelControl1.Text = "时间间隔 ≤";
            // 
            // chkAnaLTE
            // 
            this.chkAnaLTE.AutoSize = true;
            this.chkAnaLTE.Location = new System.Drawing.Point(28, 140);
            this.chkAnaLTE.Name = "chkAnaLTE";
            this.chkAnaLTE.Size = new System.Drawing.Size(90, 16);
            this.chkAnaLTE.TabIndex = 26;
            this.chkAnaLTE.Text = "分析LTE切换";
            this.chkAnaLTE.UseVisualStyleBackColor = true;
            // 
            // NRPingPangSettingForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(286, 221);
            this.Controls.Add(this.chkAnaLTE);
            this.Controls.Add(this.numSpeedLimitMax);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.numSpeedLimitMin);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.chkSpeedLimit);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.numTimeLimit);
            this.Controls.Add(this.labelControl1);
            this.Name = "NRPingPangSettingForm";
            ((System.ComponentModel.ISupportInitialize)(this.numSpeedLimitMax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSpeedLimitMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSpeedLimit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTimeLimit.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.SpinEdit numSpeedLimitMax;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.SpinEdit numSpeedLimitMin;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.CheckEdit chkSpeedLimit;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SpinEdit numTimeLimit;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private System.Windows.Forms.CheckBox chkAnaLTE;
    }
}