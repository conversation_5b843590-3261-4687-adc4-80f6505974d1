﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using System.IO;
using System.Windows.Forms;
using System.Net;

namespace MasterCom.RAMS.Net
{
    public static partial class RequestType
    {
        public const byte REQTYPE_DIY_MODEL_SQL = 0xa1;	//REQUEST
        public const byte REQTYPE_DIY_MODEL_SQL_FORMAINDB = 0xa2;	//REQUEST
    }
    public static partial class ResponseType
    {
        public const byte RESTYPE_DIYSEARCH_SQL = 0x20;
        public const byte RESTYPE_DIYSEARCH_SQL_SUCCESS = 0x21;
        public const byte RESTYPE_DIYSEARCH_SQL_FAIL = 0x22;
    }

    public abstract class DIYSQLBase :QueryBase
    {
        protected bool MainDB = false;
        protected int dbid = -1;
        protected string userName { get; set; }
        protected string password { get; set; }
        protected DIYSQLBase()
        {
            userName = MainModel.User.LoginName;
            password = MainModel.User.Password;
        }

        protected DIYSQLBase(MainModel mainModel)
            : this()
        {
           
        }

        protected DIYSQLBase(MainModel mainModel, int DbId)
            : base(mainModel)
        {
            dbid = DbId;
            userName = MainModel.User.LoginName;
            password = MainModel.User.Password;
        }

        protected override bool isValidCondition()
        {
            return true;
        }
        protected abstract string getSqlTextString();
        protected abstract E_VType[] getSqlRetTypeArr();
        protected abstract void receiveRetData(ClientProxy clientProxy);
        protected override void query()
        {
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                queryInThread(clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
            
        }

        protected virtual void queryInThread(object o) 
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                string strsql = getSqlTextString();
                if (mainModel.IsBackground)
                {
                    BackgroundFunc.BackgroundFuncManager.GetInstance().ReportBackgroundLogInfo(strsql);
                }

                E_VType[] retArrDef = getSqlRetTypeArr();//获得枚举类型数组
                package.Command = Command.DIYSearch;//枚举类型：DIY接口
                package.SubCommand = SubCommand.Request;//枚举类型：请求
                if (MainDB)
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
                }
                else
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
                }
                package.Content.PrepareAddParam();
                package.Content.AddParam(strsql);
                StringBuilder sb = new StringBuilder();
                if (retArrDef != null)
                {
                    for (int i = 0; i < retArrDef.Length; i++)
                    {
                        sb.Append((int)retArrDef[i]);
                        sb.Append(",");
                    }
                }

                package.Content.AddParam(sb.ToString().TrimEnd(','));
                clientProxy.Send();
                receiveRetData(clientProxy);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
            finally
            {
                //注意！部分功能，SQLBASE query是在某个QueryInThread内进行的，即WaitBox不是在改类Show出来，如果在此SQLBASE关掉WaitBox
                //会跳出最上层的QueryInThread，导致代码执行不完整。所以不要在此关闭WaitBox
                //WaitBox.Close()
            }
        }

        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        public override string Name
        {
            get { return string.Empty; }
        }

        protected override void preparePackageCommand(Package package)
        {
            throw new NotImplementedException();
        }
    }

    public class DiySqlDemo : DIYSQLBase
    {
        public DiySqlDemo(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected override string getSqlTextString()
        {
            return "SELECT ifileid,iimporttime,strfilename FROM tb_log_file_2010_11";
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[3];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    package.Content.GetParamInt();//id
                    package.Content.GetParamInt();//status
                    package.Content.GetParamString();//name
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        public override string Name
        {
            get { return "DIYSqlDemo"; }
        }
    }

    public class DiySqlNonQuery : DIYSQLBase
    {
        protected string sql;
        public DiySqlNonQuery(MainModel mainModel, String sql)
            : base(mainModel)
        {
            this.sql = sql;
        }

        public DiySqlNonQuery(MainModel mainModel, String sql, int Dbid)
            : base(mainModel, Dbid)
        {
            this.sql = sql;
        }

        protected override string getSqlTextString()
        {
            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[3];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        public override string Name
        {
            get { return "DiySqlNonQuery"; }
        }
    };

    public class DiySqlNonQueryMainDB : DIYSQLBase
    {
        readonly string sql;
        public DiySqlNonQueryMainDB(MainModel mainModel, String sql)
            : base(mainModel)
        {
            MainDB = true;
            this.sql = sql;
        }

        protected override string getSqlTextString()
        {
            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[3];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        public override string Name
        {
            get { return "DiySqlNonQueryMainDB"; }
        }
    };

    public class DiySqlOneIntValueOnly : DIYSQLBase
    {
        readonly string sql;
        int intValue = 0;
        public int IntValue
        {
            get { return intValue; }
        }
        public DiySqlOneIntValueOnly(MainModel mainModel, string sql)
            : base(mainModel)
        {
            this.sql = sql;
        }
        protected override string getSqlTextString()
        {
            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_Int;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    intValue = package.Content.GetParamInt();
                    //do your code here
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        public override string Name
        {
            get { return "DiySqlOneIntValueOnly"; }
        }
    };

    public class DiySqlOneIntValueOnlyMainDB : DIYSQLBase
    {
        readonly string sql;
        int intValue = 0;
        public int IntValue
        {
            get { return intValue; }
        }
        public DiySqlOneIntValueOnlyMainDB(MainModel mainModel, string sql)
            : base(mainModel)
        {
            MainDB = true;
            this.sql = sql;
        }
        protected override string getSqlTextString()
        {
            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_Int;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    intValue = package.Content.GetParamInt();
                    //do your code here
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }

        public override string Name
        {
            get { return "DiySqlOneIntValueOnlyMainDB"; }
        }
    };
    public abstract class DiySqlMultiNonQuery : DIYSQLBase
    {
        //多条sql用“;”间隔组合成一条语句，太长时需分批次执行
        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;

                if (MainDB)
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
                }
                else
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
                }

                E_VType[] retArrDef = getSqlRetTypeArr();
                string strsql = getSqlTextString();
                if (string.IsNullOrEmpty(strsql))
                {
                    return;
                }
                string[] strArr = strsql.Split(new char[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
                int curIdx = 0;
                int index = 0;
                int progress = 0;
                int len = strArr.Length;
                while (curIdx < len)//分批次发送包
                {
                    setWaitBoxText(curIdx, len);
                    curIdx = addPackageContent(package, retArrDef, strArr, curIdx);
                    clientProxy.Send();
                    System.Threading.Thread.Sleep(MainModel.SystemConfigInfo.MultiSqlDelayMs);
                    setProgressPercent(ref index, ref progress);
                }
                receiveRetData(clientProxy);
            }
            catch (Exception ex)
            {
                ErrorInfo = ex.Message;
            }
        }

        protected virtual void setWaitBoxText(int curIdx, int len)
        { 
        
        }

        private int addPackageContent(Package package, E_VType[] retArrDef, string[] strArr, int curIdx)
        {
            StringBuilder strb = new StringBuilder();
            for (; curIdx < strArr.Length; curIdx++)
            {
                string curStr = strArr[curIdx];
                //发送时是判断的字节数不能大于8000,故此处也判断字节数,否则1个中文占2个字节会导致转为字节时超出长度
                int bufferLength = Encoding.Default.GetBytes(strb + curStr).Length;
                if (bufferLength > 7000)
                {
                    break;
                }
                strb.Append(curStr + ";");
            }
            package.Content.PrepareAddParam();
            package.Content.AddParam(strb.ToString());

            strb = new StringBuilder();
            if (retArrDef != null)
            {
                for (int i = 0; i < retArrDef.Length; i++)
                {
                    strb.Append((int)retArrDef[i]);
                    if (i < retArrDef.Length - 1)
                    {
                        strb.Append(",");
                    }
                }
            }
          
            package.Content.AddParam(strb.ToString());
            return curIdx;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_Int;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }
}
