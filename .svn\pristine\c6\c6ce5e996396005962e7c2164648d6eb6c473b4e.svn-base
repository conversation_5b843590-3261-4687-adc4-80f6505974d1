﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    [Serializable]
    public class NRQualCause : NRLowSpeedCauseBase
    {
        public NRQualCause()
        {
            AddSubReason(new NRPoorSINRCause());
        }

        public override string Name
        {
            get { return "质量"; }
        }

        public override string Desc
        {
            get { return "质量引起的低速率"; }
        }

        public override string Suggestion
        {
            get { return "未知质量原因"; }
        }

        public override void Judge(NRLowSpeedSeg segItem, List<Model.Event> evts, List<TestPoint> allTP, NRTpManagerBase nRCond)
        {
            foreach (NRLowSpeedCauseBase r in SubCauses)
            {
                if (segItem.NeedJudge)
                {
                    r.Judge(segItem, evts, allTP, nRCond);
                }
                else
                {
                    break;
                }
            }
        }

        public override Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["TypeName"] = this.GetType().FullName;
                List<object> list = new List<object>();
                foreach (NRLowSpeedCauseBase cause in SubCauses)
                {
                    list.Add(cause.CfgParam);
                }
                paramDic["SubCauseSet"] = list;

                return paramDic;

            }
            set
            {
                if (value == null)
                {
                    return;
                }

                SubCauses = new List<NRLowSpeedCauseBase>();
                List<object> list = value["SubCauseSet"] as List<object>;
                foreach (object item in list)
                {
                    Dictionary<string, object> dic = item as Dictionary<string, object>;
                    string typeName = dic["TypeName"].ToString();
                    System.Reflection.Assembly assembly = System.Reflection.Assembly.GetExecutingAssembly();
                    NRLowSpeedCauseBase cause = (NRLowSpeedCauseBase)assembly.CreateInstance(typeName);
                    cause.CfgParam = dic;
                    AddSubReason(cause);
                }
            }
        }
    }
}
