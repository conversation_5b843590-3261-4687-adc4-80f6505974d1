﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;

using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat.Data;

using MasterCom.RAMS.ZTQuery;
using System.Windows.Forms;

using DevExpress.XtraEditors;
using MasterCom.MTGis;
using MasterCom.RAMS.Grid;

namespace MasterCom.RAMS.Net
{
    /// <summary>
    /// 模拟测试路径
    /// </summary>
    public class InjectionGridQuery_SimuByTab :QueryBase
    {
        public InjectionGridQuery_SimuByTab(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get { return "道路测试渗透率路径模拟评估"; }
        }
        public override string IconName
        {
            get { return null; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18009, this.Name);
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        readonly InjectionSimuSetDlg dlg = new InjectionSimuSetDlg();
        protected override void query()
        {
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            string streetInjectMap;
            string streetInjectColumn;
            string testMap;
            dlg.GetMapSetting(out streetInjectMap, out streetInjectColumn, out testMap);
            if (streetInjectMap != null && streetInjectMap != "")
            {
                StreetInjectTableInfo streetInjTable = new StreetInjectTableInfo();
                streetInjTable.FilePath = streetInjectMap;
                streetInjTable.ColumnName = streetInjectColumn;
                MainModel.StreetInjectTablesList.Clear();
                MainModel.StreetInjectTablesList.Add(streetInjTable);
                MainModel.StreetInjectMultiTables = true;
            }
            try
            {
                WaitBox.Show("开始统计数据...", queryInThread, testMap);
            }
            finally
            {
                MainModel.FireStreetInjectQueried(this);
                MainModel.RefreshLegend();
            }
        }
        private void queryInThread(object o)
        {
            try{
                string filename = (string)o;
                WaitBox.Text = "开始进行模拟运算...";
                List<StreetInjectInfo> totalInfoResultList = new List<StreetInjectInfo>();
                GridMatrix<InjectGridUnit> injGridMatrix = recieveInfo(filename, totalInfoResultList);
                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;
                WaitBox.Text = "数据模拟完毕，进行显示预处理...";
                if(MainModel.CurGridCoverData!=null)
                {
                    MainModel.CurGridCoverData.Clear();
                }
                MainModel.CurStreetInjectMatrix = null;
                QueryCondition condition = this.GetQueryCondition();
                MapWinGIS.Shape geometryReg = condition.Geometorys.Region;
                MainModel.LastSearchGeometry = geometryReg;
                if (MainModel.MultiGeometrys && Condition.Geometorys.SelectedResvRegions != null && condition.Geometorys.SelectedResvRegions.Count > 0)//
                {
                    MainModel.StreetInjectResvRegions = condition.Geometorys.SelectedResvRegions;
                }
                else
                {
                    MainModel.StreetInjectResvRegions.Clear();
                    ResvRegion resvRegion = new ResvRegion();
                    resvRegion.RegionName = "区域";
                    resvRegion.Shape = Condition.Geometorys.Region;
                    MainModel.StreetInjectResvRegions.Add(resvRegion);
                }
                //glau
                MainModel.CurStreetInjectMatrix = injGridMatrix;
                MainModel.TotalStreetInjInfoResultList = totalInfoResultList;
            }catch(Exception e){
                log.Error("Error:"+e.Message);
            }finally{
                WaitBox.Close();
            }
            MapFormStreetInjectAnaLayer.NeedFreshFullImg = true;
        }

        protected virtual GridMatrix<InjectGridUnit> recieveInfo(string shpFileName, List<StreetInjectInfo> totalInfoResultList)
        {
            GridMatrix<InjectGridUnit> colorMatrix = new GridMatrix<InjectGridUnit>();
            totalInfoResultList.Clear();
            MapWinGIS.Shapefile table_Road = new MapWinGIS.Shapefile();
            table_Road.Open(shpFileName, null);
            int count = table_Road.NumShapes;
            for (int dx = 0; dx < count;dx++ )
            {
                try
                {
                    MapWinGIS.Shape street = table_Road.get_Shape(dx);
                    MapWinGIS.Shape roadMultiCurv = street;
                    if (roadMultiCurv != null)
                    {
                        for (int part = 0; part < roadMultiCurv.NumParts; part++)
                        {
                            List<DbPoint> pnts = ShapeHelper.GetPartShapePoints(roadMultiCurv, part);
                            if (pnts==null)
                            {
                                continue;
                            }
                            DbPoint[] pts = pnts.ToArray();
                            MapWinGIS.Shape curv = ShapeHelper.GetPartAsShape(roadMultiCurv, part);
                             for (int i = 0; i < pts.Length - 1; i++)
                             {
                                 DbPoint ptStart = pts[i];
                                 DbPoint ptEnd = pts[i + 1];
                                 //double dist = calcDistance(ptStart.x, ptStart.y, ptEnd.x, ptEnd.y);
                                 double xGap = Math.Abs(ptEnd.x - ptStart.x);
                                 double yGap = Math.Abs(ptEnd.y - ptStart.y);
                                 if (xGap == 0 && yGap == 0)
                                 {
                                     continue;
                                 }
                                 int xStepNum = (int)(xGap / CD.ATOM_SPAN_LONG) + 1;
                                 int yStepNum = (int)(yGap / CD.ATOM_SPAN_LAT) + 1;
                                 if (xStepNum == 1 && yStepNum == 1)
                                 {
                                     if (xGap >= yGap)
                                     {
                                         xStepNum = 2;
                                     }
                                     else
                                     {
                                         yStepNum = 2;
                                     }
                                 }
                                 if (xStepNum >= yStepNum)//X的跨度大些
                                 {
                                     double fromXX = 0;
                                     double fromYY = 0;
                                     int yFlag = 1;
                                     if (ptStart.x <= ptEnd.x)
                                     {
                                         fromXX = ptStart.x;
                                         fromYY = ptStart.y;
                                         if (ptStart.y >= ptEnd.y)
                                         {
                                             yFlag = -1;
                                         }
                                         else
                                         {
                                             yFlag = 1;
                                         }
                                     }
                                     else
                                     {
                                         fromXX = ptEnd.x;
                                         fromYY = ptEnd.y;
                                         if (ptEnd.y < ptStart.y)
                                         {
                                             yFlag = 1;
                                         }
                                         else
                                         {
                                             yFlag = -1;
                                         }
                                     }

                                     for (int p = 0; p < xStepNum; p++)
                                     {
                                         double xxMove = p * CD.ATOM_SPAN_LONG;
                                         double yyMove = xxMove * yGap / xGap;

                                         double xx = fromXX + xxMove;
                                         double yy = fromYY + yFlag * yyMove;
                                         if (p == xStepNum - 1)
                                         {
                                             //xx = toXX;
                                             //yy = toYY;
                                         }
                                         DbRect gridRect = Grid.GridHelper.GetDefaultSizeGridBoundsByLeftTopPoint(xx, yy);
                                         if (Condition.Geometorys.GeoOp.ContainsRectCenter(gridRect))
                                         {
                                             int rAt, cAt;
                                             Grid.GridHelper.GetIndexOfDefaultSizeGrid(xx, yy, out rAt, out cAt);
                                             InjectGridUnit cu = colorMatrix[rAt, cAt];
                                             if (cu == null)
                                             {
                                                 cu = new InjectGridUnit();
                                                 cu.LTLng = gridRect.x1;
                                                 cu.LTLat = gridRect.y2;
                                                 colorMatrix[rAt, cAt] = cu;
                                             }
                                             decideSimpleShow(cu);

                                             rAt += 0;
                                             cAt += yFlag;
                                             cu = colorMatrix[rAt, cAt];
                                             if (cu == null)
                                             {
                                                 double ltLng, ltLat;
                                                 Grid.GridHelper.GetLeftTopByCustomSizeGridIndex(1, rAt, cAt, out ltLng, out ltLat);
                                                 cu = new InjectGridUnit();
                                                 cu.LTLng = ltLng;
                                                 cu.LTLat = ltLat;
                                                 colorMatrix[rAt, cAt] = cu;
                                             }
                                             if (MapOperation.CheckIntersectRegion(curv, new DbRect(cu.LTLng, cu.LTLat, cu.LTLng + CD.ATOM_SPAN_LONG, cu.LTLat - CD.ATOM_SPAN_LAT)))
                                             {
                                                 decideSimpleShow(cu);
                                             }

                                             rAt += 1;
                                             cAt -= yFlag;
                                             cu = colorMatrix[rAt, cAt];
                                             if (cu == null)
                                             {
                                                 double ltLng, ltLat;
                                                 Grid.GridHelper.GetLeftTopByCustomSizeGridIndex(1, rAt, cAt, out ltLng, out ltLat);
                                                 cu = new InjectGridUnit();
                                                 cu.LTLng = ltLng;
                                                 cu.LTLat = ltLat;
                                                 colorMatrix[rAt, cAt] = cu;
                                             }
                                             if (MapOperation.CheckIntersectRegion(curv, new DbRect(cu.LTLng, cu.LTLat, cu.LTLng + CD.ATOM_SPAN_LONG, cu.LTLat - CD.ATOM_SPAN_LAT)))
                                             {
                                                 decideSimpleShow(cu);
                                             }
                                         }
                                     }
                                 }
                                 else //Y的跨度大些
                                 {
                                     double fromXX = 0;
                                     double fromYY = 0;
                                     int xFlag = 1;
                                     if (ptStart.y <= ptEnd.y)
                                     {
                                         fromXX = ptStart.x;
                                         fromYY = ptStart.y;
                                         if (ptStart.x >= ptEnd.x)
                                         {
                                             xFlag = -1;
                                         }
                                         else
                                         {
                                             xFlag = 1;
                                         }
                                     }
                                     else
                                     {
                                         fromXX = ptEnd.x;
                                         fromYY = ptEnd.y;
                                         if (ptEnd.x < ptStart.x)
                                         {
                                             xFlag = 1;
                                         }
                                         else
                                         {
                                             xFlag = -1;
                                         }
                                     }
                                     for (int p = 0; p < yStepNum; p++)
                                     {
                                         double yyMove = p * CD.ATOM_SPAN_LAT;
                                         double xxMove = yyMove * xGap / yGap;

                                         double yy = fromYY + yyMove;
                                         double xx = fromXX + xFlag * xxMove;
                                         if (p == yStepNum - 1)
                                         {
                                             //xx = toXX;
                                             //yy = toYY;
                                         }
                                         DbRect gridRect = Grid.GridHelper.GetDefaultSizeGridBoundsByLeftTopPoint(xx, yy);
                                         if (Condition.Geometorys.GeoOp.ContainsRectCenter(gridRect))
                                         {
                                             int rAt, cAt;
                                             Grid.GridHelper.GetIndexOfDefaultSizeGrid(xx, yy, out rAt, out cAt);
                                                 InjectGridUnit cu = colorMatrix[rAt, cAt];
                                                 if (cu == null)
                                                 {
                                                     cu = new InjectGridUnit();
                                                     cu.LTLng = gridRect.x1;
                                                     cu.LTLat = gridRect.y2;
                                                     colorMatrix[rAt, cAt] = cu;
                                                 }
                                                 decideSimpleShow(cu);

                                             rAt += xFlag;
                                             cAt += 0;
                                             if (cu == null)
                                             {
                                                 double ltLng, ltLat;
                                                 Grid.GridHelper.GetLeftTopByCustomSizeGridIndex(1, rAt, cAt, out ltLng, out ltLat);
                                                 cu = new InjectGridUnit();
                                                 cu.LTLng = ltLng;
                                                 cu.LTLat = ltLat;
                                                 colorMatrix[rAt, cAt] = cu;
                                             }
                                             if (MapOperation.CheckIntersectRegion(curv, new DbRect(cu.LTLng, cu.LTLat, cu.LTLng + CD.ATOM_SPAN_LONG, cu.LTLat - CD.ATOM_SPAN_LAT)))
                                             {
                                                 decideSimpleShow(cu);
                                             }

                                             rAt -= xFlag;
                                             cAt += 1;
                                             if (cu == null)
                                             {
                                                 double ltLng, ltLat;
                                                 Grid.GridHelper.GetLeftTopByCustomSizeGridIndex(1, rAt, cAt, out ltLng, out ltLat);
                                                 cu = new InjectGridUnit();
                                                 cu.LTLng = ltLng;
                                                 cu.LTLat = ltLat;
                                                 colorMatrix[rAt, cAt] = cu;
                                             }
                                             if (MapOperation.CheckIntersectRegion(curv, new DbRect(cu.LTLng, cu.LTLat, cu.LTLng + CD.ATOM_SPAN_LONG, cu.LTLat - CD.ATOM_SPAN_LAT)))
                                             {
                                                 decideSimpleShow(cu);
                                             }
                                         }
                                     }

                                 }
                             }
                         }
                     }
                }
                catch (System.Exception ex)
                {
                    System.Console.WriteLine(ex.Message);
                }
            }
            return colorMatrix;
        }

        /// <summary>
        /// 将GridPartParam的内容合并到InjectGridUnit中的GridUnit
        /// </summary>
        /// <param name="cu"></param>
        /// <param name="gpp"></param>
        private void decideSimpleShow(InjectGridUnit cu)
        {
            if (cu.Status == 1)//设置过，需要叠加
            {
                //cu.gu.doMergeIt(gpp);
                cu.repeatCount++;
            }
            else
            {
                cu.Status = 1;//标志为已经设置
                //cu.gu = new GridUnit(gpp);
                cu.repeatCount = 1;
            }

        }

        protected override bool isValidCondition()
        {
            return true;
        }
    }
}
