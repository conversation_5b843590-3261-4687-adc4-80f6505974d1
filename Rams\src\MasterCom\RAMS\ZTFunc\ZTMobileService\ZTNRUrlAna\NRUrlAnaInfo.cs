﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRUrlAnaInfo
    {
        public int SN { get; set; }

        public List<NRUrlBroUrl> Bros { get; set; } = new List<NRUrlBroUrl>();

        public List<NRUrlDowUrl> Downs { get; set; } = new List<NRUrlDowUrl>();

        public List<NRUrlVideoUrl> Videos { get; set; } = new List<NRUrlVideoUrl>();


        public int BroCount { get { return Bros.Count; } }

        public int DownCount { get { return Downs.Count; } }

        public int VideoCount { get { return Videos.Count; } }
    }

    public class NRUrlAnaInfoByFile : NRUrlAnaInfo
    {
        public NRUrlAnaInfoByFile(string fileName)
        {
            this.FileName = fileName;
        }

        public string FileName { get; set; }

        public string DistrictName { get; set; }
    }


    public class NRUrlAnaInfoByRegion : NRUrlAnaInfo
    {
        public NRUrlAnaInfoByRegion(string regionName, string gridName)
        {
            this.RegionName = regionName;
            this.GridName = gridName;
        }

        public string RegionName { get; set; }

        public string GridName { get; set; }
    }

    public class NRUrlDataBase
    {
        public NRUrlDataBase(string url)
        {
            this.Url = url;
        }

        public string Url { get; protected set; }

        public int SN { get; set; }

        public List<NRUrlEvent> Events { get; protected set; } = new List<NRUrlEvent>();
    }

    public class NRUrlVideoUrl : NRUrlDataBase
    {
        public NRUrlVideoUrl(string url) : base(url)
        {
        }

        public List<Event> EvtReq { get; private set; } = new List<Event>();

        public List<Event> EvtSuc { get; private set; } = new List<Event>();

        public List<Event> EvtPlayStart { get; private set; } = new List<Event>();

        public List<Event> EvtLastData { get; private set; } = new List<Event>();

        public List<Event> EvtRebufferEnd { get; private set; } = new List<Event>();

        public List<Event> EvtRebuffer { get; private set; } = new List<Event>();

        public List<Event> EvtFlvPlayFinished { get; private set; } = new List<Event>();

        public int ReqCount { get; private set; }

        public int SucCount { get; private set; }

        public double? SucRate { get; private set; }

        public double? Delay { get; set; }

        public double? Time { get; set; }

        public double? RebufferTime { get; set; } = 0;

        public double? PlayTime { get; set; }

        public int RebufferCount { get; private set; }

        public int RebufferEndCount { get; private set; }

        public int FlvPlayFinishedCount { get; private set; }

        public double? TimeoutRate { get; set; }

        public double? DownSpeed { get; set; }

        public double? LoadSpeed { get; set; }

        public double? value1Sum38 { get; set; } = 0;

        public double? value7Sum35 { get; set; } = 0;

        public double? value6Sum35 { get; set; } = 0;

        public double? value5Sum35 { get; set; } = 0;

        public double? value4Sum35 { get; set; } = 0;

        public double? value2Sum35 { get; set; } = 0;

        public double? value1Sum35 { get; set; } = 0;

        public double? value2Sum34 { get; set; } = 0;

        public double? value2Sum29 { get; set; } = 0;

        public void Calculate()
        {
            ReqCount = EvtReq.Count;
            SucCount = EvtSuc.Count;
            SucRate = Math.Round(((float)SucCount / (float)ReqCount), 4);
            RebufferCount = EvtRebuffer.Count;
            RebufferEndCount = EvtRebufferEnd.Count;
            FlvPlayFinishedCount = EvtFlvPlayFinished.Count;
        }
    }

    public class NRUrlDowUrl : NRUrlDataBase
    {
        public NRUrlDowUrl(string url) : base(url)
        {
        }

        public List<Event> EvtDowSuc { get; private set; } = new List<Event>();

        public List<Event> EvtFail { get; private set; } = new List<Event>();

        public List<Event> EvtDowFai { get; private set; } = new List<Event>();

        public int Dowcount { get; private set; }

        public int DowSuc { get; private set; }

        public double? DowSucRate { get; private set; }

        public int DowFail { get; private set; }

        public double? DowFaiRate { get; private set; }

        public double? SucSpeed { get; set; }

        public double? Speed { get; set; }

        public double? Value1Sum { get; set; } = 0;

        public double? Value2Sum { get; set; } = 0;

        public double? Value1Sum1 { get; set; } = 0;

        public double? Value2Sum1 { get; set; } = 0;

        public void Calculate()
        {
            Dowcount = EvtDowFai.Count + EvtDowSuc.Count + EvtFail.Count;
            DowSuc = EvtDowSuc.Count;
            DowFail = EvtDowFai.Count;
            DowSucRate = Math.Round(((float)DowSuc / (float)Dowcount), 4);
            DowFaiRate = Math.Round(((float)DowFail / (float)Dowcount), 4);
        }
    }

    public class NRUrlBroUrl : NRUrlDataBase
    {
        public NRUrlBroUrl(string url) : base(url)
        {
        }

        public List<Event> EvtDisSuc { get; set; } = new List<Event>();

        public List<Event> EvtDisFai { get; private set; } = new List<Event>();

        public List<Event> EvtComple { get; private set; } = new List<Event>();

        public int DisCount { get; private set; }

        public int DisSuc { get { return EvtDisSuc.Count; } }

        public int Complete { get { return EvtComple.Count; } }

        public double? DisSucRate { get; private set; }

        public double? DisDelay { get; set; }

        public double? SucRate { get; private set; }

        public double? Time { get; set; }

        public double? Speed { get; set; }

        public double? Value2Sum { get; set; } = 0;

        public double? Value1Sum { get; set; } = 0;

        public double? Value2Sum1 { get; set; } = 0;

        public void Calculate()
        {
            DisCount = EvtDisSuc.Count + EvtDisFai.Count;
            DisSucRate = Math.Round(((float)DisSuc / (float)DisCount), 4);
            SucRate = Math.Round(((float)Complete / (float)DisSuc), 4);
        }
    }

    public class NRUrlEvent
    {
        public NRUrlEvent(Event evtStart, Event evtEnd, Event evtError, string url)
        {
            this.EventStart = evtStart;
            if (evtStart != null)
            {
                this.FileName = evtStart.FileName;
                this.StartTime = evtStart.DateTime.ToString("yy-MM-dd HH:mm:ss.fff");
                this.StartName = evtStart.Name;
            }
            if (evtEnd != null)
            {
                this.EndTime = evtEnd.DateTime.ToString("yy-MM-dd HH:mm:ss.fff");
                this.EvtEndName = evtEnd.Name;
                this.FileName = evtEnd.FileName;
                if (evtStart != null)
                {
                    this.TimeSpan = (evtEnd.DateTime - evtStart.DateTime).TotalSeconds.ToString();
                }
            }
            this.Url = url;
            this.EventEnd = evtEnd;
            this.EventError = evtError;
            this.GetInfo();
        }

        public Event EventStart { get; private set; }

        public Event EventError { get; private set; }

        public Event EventEnd { get; private set; }

        public int SN { get; set; }

        public string Url { get; private set; }

        public string RegionName { get; private set; }

        public string GridName { get; private set; }

        public string IsFail { get; private set; }

        public string StartTime { get; private set; }

        public string EndTime { get; private set; }

        public string TimeSpan { get; private set; }

        public string StartName { get; private set; }

        public string EvtEndName { get; private set; }

        public string FailReason { get; private set; }

        public double? Bytes { get; private set; }

        public string FileName { get; private set; }

        private void GetInfo()
        {
            if (this.EventError != null)
            {
                IsFail = "失败";
                switch (this.EventError.ID)
                {
                    case (int)NREventManager.HttpFail:
                        this.FailReason = GetReason(int.Parse(this.EventError["Value3"].ToString()));
                        break;
                    case (int)NREventManager.DownloadFail:
                        this.FailReason = GetReason(int.Parse(this.EventError["Value3"].ToString()));
                        break;
                    case (int)NREventManager.VideoFail:
                        this.FailReason = GetReason(int.Parse(this.EventError["Value3"].ToString()));
                        break;
                    default:
                        this.FailReason = "s_APPNotDefined";
                        break;
                }
            }
            else if (EventEnd != null)
            {
                if (EventEnd.ID == (int)NREventManager.HttpComplete
                    || EventEnd.ID == (int)NREventManager.VideoFinish
                    || EventEnd.ID == (int)NREventManager.DownSuccess)
                {
                    IsFail = "成功";
                }
                else
                {
                    IsFail = "失败";
                }
                Bytes = (float)int.Parse(this.EventEnd["Value1"].ToString());
            }
        }

        private string GetReason(int Value)
        {
            switch (Value)
            {
                case 0:
                    return "s_APPNotDefined";
                case 1:
                    return "S_APPServiceRequest";
                case 2:
                    return "S_APPServiceConfirm";
                case 3:
                    return "S_APPServiceConnect";
                case 4:
                    return "S_APPServiceSuccess";
                case 5:
                    return "S_APPServiceReject";
                case 6:
                    return "S_APPServiceTimeout";
                case 7:
                    return "S_APPServiceFailure";
                case 8:
                    return "S_APPServiceDrop";
                default:
                    return "S_APPNone";
            }
        }
    }
}
