﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    class NRIndoorAcpGoodPointFtpDownload : NRIndoorStationAcceptThroughput
    {
        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("下载") && fileInfo.Name.Contains("好点");
        }

        protected override bool judgeNeedAna(FileInfo fileInfo, DTFileDataManager fileManager, NRIndoorBtsInfo bts, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            if (curCellServiceInfo.SampleDL.Rsrp.Data != 0)
            {
                //同时存在好点和差点文件时,先分析过差点文件,清除差点文件数据
                curCellServiceInfo.SampleDL = new FtpPointInfo();
            }
            return true;
        }

        protected override void addData(TestPoint tp, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            curCellServiceInfo.SampleDL.Add(tp, true);
        }

        protected override void verifyThroughput(NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition, ThroughputStandard standard)
        {
            curCellServiceInfo.SampleDL.Calculate();
            dealValidGoodThroughput(curCellServiceInfo.SampleDL, standard.DownThroughput);
        }
    }

    class NRIndoorAcpBadPointFtpDownload : NRIndoorStationAcceptThroughput
    {
        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("下载") && fileInfo.Name.Contains("差点");
        }

        protected override bool judgeNeedAna(FileInfo fileInfo, DTFileDataManager fileManager, NRIndoorBtsInfo bts, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            //同时存在好点和差点文件时,先分析过好点文件,差点文件不分析
            if (curCellServiceInfo.SampleDL.Rsrp.Data != 0)
            {
                return false;
            }
            return true;
        }

        protected override void addData(TestPoint tp, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            curCellServiceInfo.SampleDL.Add(tp, true);
        }

        protected override void verifyThroughput(NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition, ThroughputStandard standard)
        {
            curCellServiceInfo.SampleDL.Calculate();
            dealValidBadThroughput(curCellServiceInfo.SampleDL, standard.DownThroughput);
        }
    }

    class NRIndoorAcpGoodPointFtpUpload : NRIndoorStationAcceptThroughput
    {
        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("上传") && fileInfo.Name.Contains("好点");
        }

        protected override bool judgeNeedAna(FileInfo fileInfo, DTFileDataManager fileManager, NRIndoorBtsInfo bts, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            if (curCellServiceInfo.SampleUL.Rsrp.Data != 0)
            {
                curCellServiceInfo.SampleUL = new FtpPointInfo();
            }

            return true;
        }

        protected override void addData(TestPoint tp, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            curCellServiceInfo.SampleUL.Add(tp, false);
        }

        protected override void verifyThroughput(NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition, ThroughputStandard standard)
        {
            curCellServiceInfo.SampleUL.Calculate();
            dealValidGoodThroughput(curCellServiceInfo.SampleUL, standard.UpThroughput);
        }
    }

    class NRIndoorAcpBadPointFtpUpload : NRIndoorStationAcceptThroughput
    {
        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("上传") && fileInfo.Name.Contains("差点");
        }

        protected override bool judgeNeedAna(FileInfo fileInfo, DTFileDataManager fileManager, NRIndoorBtsInfo bts, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            if (curCellServiceInfo.SampleUL.Rsrp.Data != 0)
            {
                return false;
            }

            return true;
        }

        protected override void addData(TestPoint tp, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            curCellServiceInfo.SampleUL.Add(tp, false);
        }

        protected override void verifyThroughput(NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition, ThroughputStandard standard)
        {
            curCellServiceInfo.SampleUL.Calculate();
            dealValidBadThroughput(curCellServiceInfo.SampleUL, standard.UpThroughput);
        }
    }

    class NRIndoorAcpAccRate : NRIndoorStationAcceptEvent
    {
        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("接入") || fileInfo.Name.Contains("附着");
        }

        protected override SuccessRateKpiInfo initEventKpiInfo(NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            if (condition.NRServiceType == NRServiceName.SA)
            {
                evtRequList = new List<int> { (int)NREventManager.NR_Registration_Request };
                evtSuccList = new List<int> { (int)NREventManager.NR_Registration_Accept };
            }
            else if (condition.NRServiceType == NRServiceName.NSA)
            {
                evtRequList = new List<int> { (int)NREventManager.Attach_Request };
                evtSuccList = new List<int> { (int)NREventManager.Attach_Accept };
            }

            return curCellServiceInfo.AccessInfo;
        }

        protected override void verifyFileResult(FileInfo fileInfo, DTFileDataManager fileManager, NRIndoorBtsInfo bts, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            curCellServiceInfo.AccessInfo.Calculate();
            curCellServiceInfo.AccessInfo.Judge(10, 1);
        }
    }

    class NRIndoorAcpCellAddRate : NRIndoorStationAcceptEvent
    {
        public NRIndoorAcpCellAddRate()
        {
            evtRequList = new List<int> { (int)NREventManager.NR_Cell_Add_Request };
            evtSuccList = new List<int> { (int)NREventManager.NR_Cell_Add_Success };
        }

        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("建立成功");
        }

        protected override SuccessRateKpiInfo initEventKpiInfo(NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            if (condition.NRServiceType == NRServiceName.SA)
            {
                evtRequList = new List<int> { (int)NREventManager.NR_TAU_Request };
                evtSuccList = new List<int> { (int)NREventManager.NR_TAU_Success };
            }
            else if (condition.NRServiceType == NRServiceName.NSA)
            {
                evtRequList = new List<int> { (int)NREventManager.NR_Cell_Add_Request };
                evtSuccList = new List<int> { (int)NREventManager.NR_Cell_Add_Success };
            }

            return curCellServiceInfo.GNBAddInfo;
        }

        protected override void verifyFileResult(FileInfo fileInfo, DTFileDataManager fileManager, NRIndoorBtsInfo bts, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            curCellServiceInfo.GNBAddInfo.Calculate();
            curCellServiceInfo.GNBAddInfo.Judge(10, 1);
        }
    }


    class NRIndoorAcpEPSFBRate : NRIndoorStationAcceptEvent
    {
        public NRIndoorAcpEPSFBRate()
        {
            evtRequList = new List<int> { (int)NREventManager.EPSFB_Audio_MO_Call_Attempt, (int)NREventManager.EPSFB_Audio_MT_Call_Attempt };
            evtSuccList = new List<int> { (int)NREventManager.EPSFB_Audio_MO_Call_Setup, (int)NREventManager.EPSFB_Audio_MT_Call_Setup };
        }

        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("EPS语音");
        }

        protected override SuccessRateKpiInfo initEventKpiInfo(NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            return curCellServiceInfo.EPSFBInfo;
        }

        protected override void verifyFileResult(FileInfo fileInfo, DTFileDataManager fileManager, NRIndoorBtsInfo bts, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            curCellServiceInfo.EPSFBInfo.Calculate();
            curCellServiceInfo.EPSFBInfo.Judge(0, 0.98);
        }
    }

    class NRIndoorAcpEPSFBDelay : NRIndoorStationAcceptBase
    {
        protected List<int> evtSuccList;
        public NRIndoorAcpEPSFBDelay()
        {
            evtSuccList = new List<int> { (int)NREventManager.EPSFB_Audio_MO_Call_Setup, (int)NREventManager.EPSFB_Audio_MT_Call_Setup };
        }

        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.ToUpper().Contains("EPS语音");
        }

        protected override void analyzeNRFile(FileInfo fileInfo, DTFileDataManager fileManager, NRIndoorBtsInfo bts, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            foreach (Event evt in fileManager.Events)
            {
                dealEvtData(curCellServiceInfo, evt);
            }
        }

        protected void dealEvtData(NRIndoorCellServiceInfo cellTypeInfo, Event evt)
        {
            if (evtSuccList.Contains(evt.ID))
            {
                int delay = int.Parse(evt["Value1"].ToString());
                cellTypeInfo?.EPSFBDelay.Add(delay / 1000d);
            }
        }

        protected override void verifyFileResult(FileInfo fileInfo, DTFileDataManager fileManager, NRIndoorBtsInfo bts, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            curCellServiceInfo.EPSFBDelay.Calculate();
            if (curCellServiceInfo.EPSFBDelay.Data <= 3)
            {
                curCellServiceInfo.EPSFBDelay.IsValid = true;
            }
        }
    }

    class NRIndoorAcpPingDelay : NRIndoorStationAcceptBase
    {
        protected List<int> evtSuccList;
        public NRIndoorAcpPingDelay()
        {
            evtSuccList = new List<int> { (int)NREventManager.Ping_Success };
        }

        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.ToUpper().Contains("PING");
        }

        protected override void analyzeNRFile(FileInfo fileInfo, DTFileDataManager fileManager, NRIndoorBtsInfo bts, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            Event preEvt = null;
            foreach (Event evt in fileManager.Events)
            {
                dealEvtData(fileInfo.Name, curCellServiceInfo, evt, ref preEvt);
            }
        }

        protected void dealEvtData(string fileInfoName, NRIndoorCellServiceInfo cellTypeInfo, Event evt, ref Event preEvt)
        {
            if (evtSuccList.Contains(evt.ID))
            {
                if (preEvt == null || evt.DateTime.Subtract(preEvt.DateTime).TotalMilliseconds >= 2000)
                {
                    int delay = int.Parse(evt["Value1"].ToString());
                    if (fileInfoName.Contains("大包"))
                    {
                        cellTypeInfo?.BigPackageDelay.Add(delay);
                    }
                    else if (fileInfoName.Contains("小包"))
                    {
                        cellTypeInfo?.SmallPackageDelay.Add(delay);
                    }
                }
                preEvt = evt;
            }
        }

        protected override void verifyFileResult(FileInfo fileInfo, DTFileDataManager fileManager, NRIndoorBtsInfo bts, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            curCellServiceInfo.SmallPackageDelay.Calculate();
            if (curCellServiceInfo.SmallPackageDelay.Divisor >= 20 && curCellServiceInfo.SmallPackageDelay.Data <= 30)
            {
                curCellServiceInfo.SmallPackageDelay.IsValid = true;
            }

            curCellServiceInfo.BigPackageDelay.Calculate();
            if (curCellServiceInfo.BigPackageDelay.Divisor >= 20 && curCellServiceInfo.BigPackageDelay.Data <= 35)
            {
                curCellServiceInfo.BigPackageDelay.IsValid = true;
            }
        }
    }

    class NRIndoorAcpLeakOutLock : NRIndoorStationAcceptSample
    {
        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("锁频");
        }

        protected override void addData(TestPoint tp, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp, true);
            if (rsrp != null)
            {
                curCellServiceInfo.LeakOutLock.Divisor++;
                if (rsrp <= -115)
                {
                    curCellServiceInfo.LeakOutLock.Dividend++;
                }
            }
        }

        protected override void verifyFileResult(FileInfo fileInfo, DTFileDataManager fileManager, NRIndoorBtsInfo bts, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            if (curCellServiceInfo.LeakOutLock.Divisor == 0)
            {
                curCellServiceInfo.LeakOutLock.Data = 1;
                curCellServiceInfo.LeakOutLock.IsValid = true;
            }
            else
            {
                curCellServiceInfo.LeakOutLock.Calculate();
                if (curCellServiceInfo.LeakOutLock.Data >= 0.95)
                {
                    curCellServiceInfo.LeakOutLock.IsValid = true;
                }
            }
        }
    }

    class NRIndoorAcpLeakOutScan : NRIndoorStationAcceptSample
    {
        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("扫频");
        }

        protected override void analyzeNRFile(FileInfo fileInfo, DTFileDataManager fileManager, NRIndoorBtsInfo bts, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            DateTime time = DateTime.Now;
            if (fileManager.TestPoints.Count > 0)
            {
                time = fileManager.TestPoints[0].DateTime;
            }
            //获取文件名中频点PCI对应的测试小区
            NRCell targetCell = getLeakOutScanTestCell(fileManager.FileName, time);
            if (targetCell == null)
            {
                //设置结果默认合格
                return;
            }
            curCellServiceInfo.LeakOutLock = new DataKpiInfo();

            //计算主服为宏站小区,邻区为被测室分小区的采样点
            //主服场强大于邻区10db或邻区场强低于-110db或获取不到邻区场强
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                NRCell outDoorCell = StationAcceptCellHelper_XJ.Instance.GetNRCell(tp);
                if (outDoorCell != null && outDoorCell.Type == NRBTSType.Outdoor
                    && outDoorCell.Token != targetCell.Token)
                {
                    addData(tp, cell, condition);
                }
            }
        }

        #region 获取扫频被测小区
        protected NRCell getLeakOutScanTestCell(string fileName, DateTime time)
        {
            //根据文件名中的频点PCI获取目标室分小区
            string[] strArray = fileName.Split('_');
            if (strArray.Length == 7)
            {
                string cellName = strArray[3];
                int? pci = getValidData(strArray[4], "PCI");
                int? earfcn = getValidData(strArray[5], "频点");

                return getCellByArfcnPciName(time, cellName, pci, earfcn);
            }
            return null;
        }

        private NRCell getCellByArfcnPciName(DateTime time, string cellName, int? pci, int? earfcn)
        {
            List<NRCell> cells = CellManager.GetInstance().GetNRCellListByARFCNPCI(time, earfcn, pci);
            foreach (NRCell cell in cells)
            {
                if (cell.Name.Contains(cellName))
                {
                    return cell;
                }
            }
            return null;
        }
        #endregion

        #region 添加数据
        protected override void addData(TestPoint tp, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp, true);
            if (rsrp != null)
            {
                curCellServiceInfo.LeakOutLock.Divisor++;

                bool isValid = getValidScanTP((float)rsrp, tp, cell.Cell.Token);
                if (isValid)
                {
                    curCellServiceInfo.LeakOutLock.Dividend++;
                }
            }
        }

        private bool getValidScanTP(float rsrp, TestPoint tp, string token)
        {
            for (int i = 0; i < 20; i++)
            {
                NRTpHelper.NRNCellType type = NRTpHelper.NrTpManager.GetNCellType(tp, i);
                if (type != NRTpHelper.NRNCellType.NCELL)
                {
                    continue;
                }

                NRCell nCell = StationAcceptCellHelper_XJ.Instance.GetNBCell_NR(tp, i);
                if (nCell != null && nCell.Token == token)
                {
                    float? nRsrp = NRTpHelper.NrTpManager.GetNCellRsrp(tp, i);
                    if (nRsrp == null || nRsrp <= -110 || rsrp - nRsrp >= 10)
                    {
                        //邻区存在被测小区且场强满足条件
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
            }
            //邻区不存在被测小区
            return true;
        }

        private int? getValidData(string str, string type)
        {
            int? data = null;
            if (str.Contains(type))
            {
                string strData = str.Replace(type, "");
                int iData;
                if (int.TryParse(strData, out iData))
                {
                    data = iData;
                }
            }
            return data;
        }
        #endregion

        protected override void verifyFileResult(FileInfo fileInfo, DTFileDataManager fileManager, NRIndoorBtsInfo bts, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            if (curCellServiceInfo.LeakOutLock.Divisor == 0)
            {
                curCellServiceInfo.LeakOutLock.Data = 1;
                curCellServiceInfo.LeakOutLock.IsValid = true;
            }
            else
            {
                curCellServiceInfo.LeakOutLock.Calculate();
                if (curCellServiceInfo.LeakOutLock.Data >= 0.95)
                {
                    curCellServiceInfo.LeakOutLock.IsValid = true;
                }
            }
        }
    }

    class NRIndoorAcpHandoverRate : NRIndoorStationAcceptBase
    {
        protected List<int> evtSuccList;
        protected List<int> evtRequList;
        protected List<int> lteEvtSuccList;
        protected List<int> lteEvtRequList;

        public NRIndoorAcpHandoverRate()
        {
        }

        protected override bool isValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("切换");
        }

        protected void resetEvtList(NRServiceName type)
        {
            if (type == NRServiceName.NSA)
            {
                evtRequList = new List<int> { (int)NREventManager.SameLTEDiffNRHandoverRequest };
                evtSuccList = new List<int> { (int)NREventManager.SameLTEDiffNRHandoverSuccess };
                lteEvtRequList = new List<int> {
                (int)NREventManager.LTEInterHandoverRequest,
                (int)NREventManager.LTEIntraHandoverRequest };
                lteEvtSuccList = new List<int> {
                (int)NREventManager.LTEInterHandoverSuccess,
                (int)NREventManager.LTEIntraHandoverSuccess  };
            }
            else if (type == NRServiceName.SA)
            {
                evtRequList = new List<int> {
                (int)NREventManager.NRInterHandoverRequest,
                (int)NREventManager.NRIntraHandoverRequest };
                evtSuccList = new List<int> {
                (int)NREventManager.NRInterHandoverSuccess,
                (int)NREventManager.NRIntraHandoverSuccess  };
            }
        }

        protected override void analyzeNRFile(FileInfo fileInfo, DTFileDataManager fileManager, NRIndoorBtsInfo bts, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            if (condition.NRServiceType == NRServiceName.NSA)
            {
                resetEvtList(condition.NRServiceType);
                dealEvtCount(fileManager, cell.NSAInfo.HandOverInfo, evtSuccList, evtRequList);
                if (cell.NSAInfo.HandOverInfo.SucceedCnt == 0 && cell.NSAInfo.HandOverInfo.RequestCnt == 0)
                {
                    dealEvtCount(fileManager, cell.NSAInfo.HandOverInfo, lteEvtSuccList, lteEvtRequList);
                }

                cell.NSAInfo.HandOverInfo.CalculateFailEvt();
            }
            else if (condition.NRServiceType == NRServiceName.SA)
            {
                resetEvtList(condition.NRServiceType);
                dealEvtCount(fileManager, cell.SAInfo.HandOverInfo, evtSuccList, evtRequList);

                cell.SAInfo.HandOverInfo.CalculateFailEvt();
            }
            else if (condition.NRServiceType == NRServiceName.DM)
            {
                //双模需要分开保存SA和NSA数据
            }
        }

        private void dealEvtCount(DTFileDataManager fileManager, SuccessRateKpiInfo info
            , List<int> evtSuccList, List<int> evtRequList)
        {
            foreach (Event evt in fileManager.Events)
            {
                if (evtRequList.Contains(evt.ID))
                {
                    info.RequestCnt++;
                }
                else if (evtSuccList.Contains(evt.ID))
                {
                    info.SucceedCnt++;
                }
            }
        }

        protected override void verifyFileResult(FileInfo fileInfo, DTFileDataManager fileManager, NRIndoorBtsInfo bts, NRIndoorCellInfo cell, NRIndoorStationAcceptCondition condition)
        {
            curCellServiceInfo.HandOverInfo.Calculate();
            curCellServiceInfo.HandOverInfo.Judge(0, 1);
        }
    }

    class NRIndoorAcpCoverPic : NRIndoorStationAcceptBase
    {
        protected List<string> picNameList;

        protected override bool isValidFile(FileInfo fileInfo)
        {
            return true;
        }

        protected override void analyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, BtsInfoBase bts, CellInfoBase cell, StationAcceptConditionBase condition)
        {
            NRIndoorBtsInfo nrBtsInfo = bts as NRIndoorBtsInfo;
            if (nrBtsInfo.BtsInfo != null)
            {
                return;
            }

            nrBtsInfo.BtsInfo = new NRIndoorBtsServiceInfo();

            picNameList = new List<string>()
            {
                "5G覆盖效果图_RSRP_1F.jpg",
                "5G质量效果图_SINR_1F.jpg",
                "5GPCI轨迹图_1F.jpg",
                "5G单用户上传速率效果图_1F.jpg",
                "5G单用户下载速率效果图_1F.jpg",
                "5G室分信号泄漏效果图RSRP_1F.jpg"
            };

            StationAcceptDownloadPicHelper.DownloadPicFile(Singleton<NRIndoorStationAcceptConfigHelper>.Instance, nrBtsInfo.BtsName, picNameList);
            string curBtsPicPath = Singleton<NRIndoorStationAcceptConfigHelper>.Instance.ConfigInfo.BtsLocalCoverPicPath;
            nrBtsInfo.BtsInfo.RsrpPicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, picNameList[0]);
            nrBtsInfo.BtsInfo.SinrPicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, picNameList[1]);
            nrBtsInfo.BtsInfo.PciPicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, picNameList[2]);
            nrBtsInfo.BtsInfo.ULPicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, picNameList[3]);
            nrBtsInfo.BtsInfo.DLPicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, picNameList[4]);
            nrBtsInfo.BtsInfo.LeakOutPicInfo.PicPath = StationAcceptDownloadPicHelper.GetValidPic(curBtsPicPath, picNameList[5]);
        }

        public override void Clear()
        {
            StationAcceptDownloadPicHelper.Clear(Singleton<NRIndoorStationAcceptConfigHelper>.Instance.ConfigInfo.BtsLocalCoverPicPath);
        }
    }

    class NRIndoorAcpBtsAlarm : NRIndoorStationAcceptBase
    {
        protected override bool isValidFile(FileInfo fileInfo)
        {
            return true;
        }

        protected override void analyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, BtsInfoBase bts, CellInfoBase cell, StationAcceptConditionBase condition)
        {
            NRIndoorBtsInfo nrBtsInfo = bts as NRIndoorBtsInfo;
            if (nrBtsInfo.BtsAlarmList == null)
            {
                nrBtsInfo.BtsAlarmList = new List<NRAlarmInfo>();
            }
            else
            {
                return;
            }

            FddDatabaseSetting setting;
            if (!DiyQueryFddDBSetting.GetInstance().DatabaseSetting.TryGetValue("NR单验告警", out setting))
            {
                return;
            }

            DIYQueryNRAlarm query = new DIYQueryNRAlarm(nrBtsInfo.BtsName, setting);
            query.Query();
            if (query.DataList.Count > 0)
            {
                nrBtsInfo.BtsAlarmList = query.DataList;
            }
        }
    }

    class NRIndoorAcpBtsBaseConfig : NRIndoorStationAcceptBase
    {
        protected override bool isValidFile(FileInfo fileInfo)
        {
            return true;
        }

        protected override void analyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, BtsInfoBase bts, CellInfoBase cell, StationAcceptConditionBase condition)
        {
            NRIndoorBtsInfo nrBtsInfo = bts as NRIndoorBtsInfo;
            if (nrBtsInfo.BtsBaseInfo != null)
            {
                return;
            }
            NRIndoorBtsParameters btsParameters = new NRIndoorBtsParameters();
            nrBtsInfo.BtsBaseInfo = btsParameters;

            btsParameters.BtsName = nrBtsInfo.BtsName;

            addAntennaPlatform(btsParameters);

            addNetworkConfig(btsParameters);

            addAuditData(btsParameters);

            addWireless(btsParameters);

            verifyfileResult(nrBtsInfo);
        }

        private void addAntennaPlatform(NRIndoorBtsParameters btsParameters)
        {
            DIYQueryNRAntennaPlatform antQuery = new DIYQueryNRAntennaPlatform();
            antQuery.SetCondition(btsParameters.BtsName);
            antQuery.Query();

            foreach (var info in antQuery.NRAntennaPlatformDBInfoList)
            {
                if (!btsParameters.CellDic.TryGetValue(info.CellName, out var cellInfo))
                {
                    cellInfo = new NRIndoorCellParameters();
                    cellInfo.CellName = info.CellName;
                    btsParameters.CellDic.Add(info.CellName, cellInfo);
                }
                btsParameters.BtsLongutide.Real = info.BtsLongitude;
                btsParameters.BtsLatitude.Real = info.BtsLatitude;
                cellInfo.Longitude.Real = info.Longitude;
                cellInfo.Latitude.Real = info.Latitude;
            }
        }

        private void addNetworkConfig(NRIndoorBtsParameters btsParameters)
        {
            DIYQueryNRNetworkConfig netQuery = new DIYQueryNRNetworkConfig();
            netQuery.SetCondition(btsParameters.BtsName);
            netQuery.Query();

            foreach (var info in netQuery.NRNetworkConfigDBInfoList)
            {
                if (!btsParameters.CellDic.TryGetValue(info.CellName, out var cellInfo))
                {
                    cellInfo = new NRIndoorCellParameters();
                    cellInfo.CellName = info.CellName;
                    btsParameters.CellDic.Add(info.CellName, cellInfo);
                }
                btsParameters.ENodeBID = info.NodeBID;
                btsParameters.CellCount.Real = info.CellCount;
                btsParameters.TAC.Real = info.TAC;
                btsParameters.BBU.Real = info.BBU;
                btsParameters.RRU.Real = info.RRU;
                cellInfo.CellID.Real = info.CellID;
                cellInfo.PCI.Real = info.PCI;
                cellInfo.PRACH.Real = info.PRACH;
            }
        }

        private void addAuditData(NRIndoorBtsParameters btsParameters)
        {
            DIYQueryNRStationAuditData dataQuery = new DIYQueryNRStationAuditData();
            dataQuery.SetCondition(btsParameters.BtsName);
            dataQuery.Query();

            foreach (var info in dataQuery.NRStationAuditDataDBInfoList)
            {
                if (!btsParameters.CellDic.TryGetValue(info.CellName, out var cellInfo))
                {
                    cellInfo = new NRIndoorCellParameters();
                    cellInfo.CellName = info.CellName;
                    btsParameters.CellDic.Add(info.CellName, cellInfo);
                }

                btsParameters.Country = info.Country;
                btsParameters.BtsLongutide.Planing = info.BtsLongitude;
                btsParameters.BtsLatitude.Planing = info.BtsLatitude;
                btsParameters.CellCount.Planing = info.CellCount;
                btsParameters.BBU.Planing = info.BBU;
                btsParameters.RRU.Planing = info.RRU;
                cellInfo.Longitude.Planing = info.Longitude;
                cellInfo.Latitude.Planing = info.Latitude;
                //cellInfo.Channels.Planing = info.Channels;
            }
        }

        private void addWireless(NRIndoorBtsParameters btsParameters)
        {
            DIYQueryNRWirelessPlanningTable queryTable = new DIYQueryNRWirelessPlanningTable();
            queryTable.Query();
            if (string.IsNullOrEmpty(queryTable.TableName))
            {
                return;
            }

            DIYQueryNRWirelessPlanning wirelessQuery = new DIYQueryNRWirelessPlanning();
            wirelessQuery.SetCondition(btsParameters.BtsName);
            wirelessQuery.TableName = queryTable.TableName;
            wirelessQuery.Query();

            foreach (var info in wirelessQuery.NRWirelessPlanningDBInfoList)
            {
                if (!btsParameters.CellDic.TryGetValue(info.CellName, out var cellInfo))
                {
                    cellInfo = new NRIndoorCellParameters();
                    cellInfo.CellName = info.CellName;
                    btsParameters.CellDic.Add(info.CellName, cellInfo);
                }
                btsParameters.TAC.Planing = info.TAC;
                cellInfo.CellID.Planing = info.CellID;
                cellInfo.PCI.Planing = info.PCI;
                cellInfo.FreqBand.Planing = info.FreqBand;
                cellInfo.Freq.Planing = info.Freq.ToString();
                cellInfo.SSBFreq.Planing = info.SSBFreq.ToString();
                cellInfo.Bandwidth.Planing = info.Bandwidth;
                cellInfo.PRACH.Planing = info.PRACH;
                cellInfo.SubFrameRatio.Planing = info.SubFrameRatio;
            }
        }

        protected void verifyfileResult(NRIndoorBtsInfo bts)
        {
            NRIndoorBtsParameters btsParameters = bts.BtsBaseInfo as NRIndoorBtsParameters;
            btsParameters.Caluculate();
        }
    }
}
