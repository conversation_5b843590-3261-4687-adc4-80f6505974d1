﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func
{
    public partial class CellFusionKpiEditSettingBox : BaseDialog
    {
        public CellFusionKpiEditSettingBox()
        {
            InitializeComponent();
        }
        public void SetCond(FusionDataColumn fusionColumn)
        {
            if (fusionColumn != null)
            {
                textKpiName.Text = fusionColumn.ColumnName;
                textKpiKey.Text = fusionColumn.ColumnId.ToString();
                chkIsShow.Checked = fusionColumn.IsCheck;
            }
        }
        public FusionDataColumn GetCond()
        {
            FusionDataColumn fusionColumn = new FusionDataColumn();
            fusionColumn.ColumnName = textKpiName.Text;
            fusionColumn.ColumnId = (int)getIntValue(textKpiKey.Text);
            fusionColumn.IsCheck = chkIsShow.Checked;
            return fusionColumn;
        }
        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Retry;
            if (string.IsNullOrEmpty(textKpiName.Text))
            {
                MessageBox.Show("指标名称不能为空！");
                return;
            }

            if (getIntValue(textKpiKey.Text) == null)
            {
                MessageBox.Show("指标键值格式错误！");
                return;
            }
            this.DialogResult = DialogResult.OK;
        }

        private int? getIntValue(string strValue)
        {
            int? intValue = null;

            int kpiKey;
            if (strValue.ToLower().StartsWith("0x"))
            {
                if (int.TryParse(strValue.Remove(0, 2), System.Globalization.NumberStyles.AllowHexSpecifier
                    , null, out kpiKey))
                {
                    intValue = kpiKey;
                }
            }
            else if (int.TryParse(strValue, out kpiKey))
            {
                intValue = kpiKey;
            }
            return intValue;
        }
    }
}
