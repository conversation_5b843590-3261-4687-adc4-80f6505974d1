﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using BrightIdeasSoftware;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTRtpPacketsLostListForm : MinCloseForm
    {
        public ZTRtpPacketsLostListForm()
            : base()
        {
            InitializeComponent();
            mapForm = MainModel.MainForm.GetMapForm();
            Init();
            DisposeWhenClose = true;
        }

        private MapForm mapForm = null;
        private List<ZTRtpPacketsLostList> resultList = new List<ZTRtpPacketsLostList>();

        public void FillData(List<ZTRtpPacketsLostList> resultList)
        {
            this.resultList = new List<ZTRtpPacketsLostList>();
            this.resultList = resultList;

            objLv.RebuildColumns();
            objLv.ClearObjects();
            objLv.SetObjects(resultList);

            MainModel.RefreshLegend();
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GetCellLayer().Invalidate();
            }
        }

        public void SetcolEARFCNText(string text)
        {
            colEARFCN.Text = text;
        }

        private void Init()
        {
            #region 界面数据绑定

            this.objLv.CanExpandGetter += delegate (object row)
            {
                return row is ZTRtpPacketsLostList;
            };

            this.objLv.ChildrenGetter += delegate (object row)
            {
                if (row is ZTRtpPacketsLostList)
                {
                    return (row as ZTRtpPacketsLostList).LostList;
                }
                return null;
            };

            setZTRtpPacketsLostList();

            setZTRtpPacketsLost();

            #endregion
        }

        private void setZTRtpPacketsLost()
        {
            setZTRtpPacketsLostBaseInfo();

            this.colTime.AspectGetter += delegate (object row)
            {
                if (row is ZTRtpPacketsLost)
                {
                    ZTRtpPacketsLost lost = row as ZTRtpPacketsLost;
                    return lost.BeginTime;
                }
                return null;
            };

            this.colLostReason.AspectGetter += delegate (object row)
            {
                if (row is ZTRtpPacketsLost)
                {
                    ZTRtpPacketsLost lost = row as ZTRtpPacketsLost;
                    return lost.LostReason;
                }
                return null;
            };

            this.colRSRPBefore.AspectGetter += delegate (object row)
            {
                if (row is ZTRtpPacketsLost)
                {
                    ZTRtpPacketsLost lost = row as ZTRtpPacketsLost;
                    return lost.RSRPBefore;
                }
                return null;
            };

            this.colSINRBefore.AspectGetter += delegate (object row)
            {
                if (row is ZTRtpPacketsLost)
                {
                    ZTRtpPacketsLost lost = row as ZTRtpPacketsLost;
                    return lost.SINRBefore;
                }
                return null;
            };

            this.colMaxRSRPBefore.AspectGetter += delegate (object row)
            {
                if (row is ZTRtpPacketsLost)
                {
                    ZTRtpPacketsLost lost = row as ZTRtpPacketsLost;
                    return lost.MaxRSRPBefore;
                }
                return null;
            };

            this.colMaxSINRBefore.AspectGetter += delegate (object row)
            {
                if (row is ZTRtpPacketsLost)
                {
                    ZTRtpPacketsLost lost = row as ZTRtpPacketsLost;
                    return lost.MaxSINRBefore;
                }
                return null;
            };

            this.colRSRPAfter.AspectGetter += delegate (object row)
            {
                if (row is ZTRtpPacketsLost)
                {
                    ZTRtpPacketsLost lost = row as ZTRtpPacketsLost;
                    return lost.RSRPAfter;
                }
                return null;
            };

            this.colSINRAfter.AspectGetter += delegate (object row)
            {
                if (row is ZTRtpPacketsLost)
                {
                    ZTRtpPacketsLost lost = row as ZTRtpPacketsLost;
                    return lost.SINRAfter;
                }
                return null;
            };

            this.colMaxRSRPAfter.AspectGetter += delegate (object row)
            {
                if (row is ZTRtpPacketsLost)
                {
                    ZTRtpPacketsLost lost = row as ZTRtpPacketsLost;
                    return lost.MaxRSRPAfter;
                }
                return null;
            };

            this.colMaxSINRAfter.AspectGetter += delegate (object row)
            {
                if (row is ZTRtpPacketsLost)
                {
                    ZTRtpPacketsLost lost = row as ZTRtpPacketsLost;
                    return lost.MaxSINRAfter;
                }
                return null;
            };

            this.colDirection.AspectGetter += delegate (object row)
            {
                if (row is ZTRtpPacketsLost)
                {
                    ZTRtpPacketsLost lost = row as ZTRtpPacketsLost;
                    return lost.Direction;
                }
                return null;
            };

            this.colLostNum.AspectGetter += delegate (object row)
            {
                if (row is ZTRtpPacketsLost)
                {
                    ZTRtpPacketsLost lost = row as ZTRtpPacketsLost;
                    return lost.PacketsLostNum;
                }
                return null;
            };

            this.colNum.AspectGetter += delegate (object row)
            {
                if (row is ZTRtpPacketsLost)
                {
                    ZTRtpPacketsLost lost = row as ZTRtpPacketsLost;
                    return lost.PacketsNum;
                }
                return null;
            };

            this.colRate.AspectGetter += delegate (object row)
            {
                if (row is ZTRtpPacketsLost)
                {
                    ZTRtpPacketsLost lost = row as ZTRtpPacketsLost;
                    return lost.LossRate;
                }
                return null;
            };

            this.colType.AspectGetter += delegate (object row)
            {
                if (row is ZTRtpPacketsLost)
                {
                    ZTRtpPacketsLost lost = row as ZTRtpPacketsLost;
                    return lost.MediaType;
                }
                return null;
            };
        }

        private void setZTRtpPacketsLostBaseInfo()
        {
            this.colLongitude.AspectGetter += delegate (object row)
            {
                if (row is ZTRtpPacketsLost)
                {
                    ZTRtpPacketsLost lost = row as ZTRtpPacketsLost;
                    return lost.Longitude;
                }
                return null;
            };

            this.colLatitude.AspectGetter += delegate (object row)
            {
                if (row is ZTRtpPacketsLost)
                {
                    ZTRtpPacketsLost lost = row as ZTRtpPacketsLost;
                    return lost.Latitude;
                }
                return null;
            };

            this.colEARFCN.AspectGetter += delegate (object row)
            {
                if (row is ZTRtpPacketsLost)
                {
                    ZTRtpPacketsLost lost = row as ZTRtpPacketsLost;
                    return lost.EARFCN;
                }
                return null;
            };

            this.colPCI.AspectGetter += delegate (object row)
            {
                if (row is ZTRtpPacketsLost)
                {
                    ZTRtpPacketsLost lost = row as ZTRtpPacketsLost;
                    return lost.PCI;
                }
                return null;
            };
        }

        private void setZTRtpPacketsLostList()
        {
            this.colSN.AspectGetter += delegate (object row)
            {
                if (row is ZTRtpPacketsLostList)
                {
                    ZTRtpPacketsLostList list = row as ZTRtpPacketsLostList;
                    return list.SN;
                }
                return null;
            };

            this.colFileName.AspectGetter += delegate (object row)
            {
                if (row is ZTRtpPacketsLostList)
                {
                    ZTRtpPacketsLostList list = row as ZTRtpPacketsLostList;
                    return list.FileName;
                }
                return null;
            };

            this.colSSRC.AspectGetter += delegate (object row)
            {
                if (row is ZTRtpPacketsLostList)
                {
                    ZTRtpPacketsLostList list = row as ZTRtpPacketsLostList;
                    return list.SourceSSRC;
                }
                return null;
            };
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(objLv);
        }

        private void objLv_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            OlvListViewHitTestInfo info = this.objLv.OlvHitTest(e.X, e.Y);
            if (info.RowObject is ZTRtpPacketsLost)
            {
                MainModel.ClearDTData();
                ZTRtpPacketsLost lost = info.RowObject as ZTRtpPacketsLost;
                
                foreach (TestPoint tp in lost.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                foreach (Event evt in lost.Events)
                {
                    MainModel.DTDataManager.Add(evt);
                }
                foreach (Model.Message msg in lost.Messages)
                {
                    MainModel.DTDataManager.Add(msg);
                }

                MainModel.MainForm.GetMapForm().GoToView(lost.Longitude, lost.Latitude, 6000);

                this.MainModel.IsFileReplayByCompareMode = false;
                this.MainModel.FireDTDataChanged(this);
                this.MainModel.FireSetDefaultMapSerialTheme("LTE_TDD:RSRP");    //渲染图标
            }
            else if (info.RowObject is ZTRtpPacketsLostList)
            {
                MainModel.ClearDTData();
                
                ZTRtpPacketsLostList list = info.RowObject as ZTRtpPacketsLostList;
                foreach (TestPoint tp in list.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                foreach (Event evt in list.Events)
                {
                    MainModel.DTDataManager.Add(evt);
                }
                foreach (Model.Message msg in list.Messages)
                {
                    MainModel.DTDataManager.Add(msg);
                }

                MainModel.MainForm.GetMapForm().GoToView(list.LostList[0].Longitude, list.LostList[0].Latitude, 6000);

                this.MainModel.IsFileReplayByCompareMode = false;
                this.MainModel.FireDTDataChanged(this);
                this.MainModel.FireSetDefaultMapSerialTheme("LTE_TDD:RSRP");
            }
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            objLv.ExpandAll();
        }

        private void miCloseAll_Click(object sender, EventArgs e)
        {
            objLv.CollapseAll();
        }
    }
}
