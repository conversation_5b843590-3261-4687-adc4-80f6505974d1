<?xml version="1.0"?>
<Configs>
  <Config name="ReportSetting">
    <Item name="styles" typeName="IDictionary">
      <Item typeName="String" key="Name">public_24_联通_FDD LTE综合统计报表</Item>
      <Item typeName="IList" key="Cells">
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">文件ID</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_0801}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">LTE覆盖率（RSRP≥-100&amp;SINR&gt;=-3）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Lf_612D0103/Lf_612D0101 }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">LTE覆盖率（RSRP≥-110&amp;SINR&gt;=-3）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Lf_612D0107/Lf_612D0101}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">里程LTE覆盖率（RSRP≥-100&amp;SINR&gt;=-3）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Lf_612D011C/Lf_0806}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">里程LTE覆盖率（RSRP≥-110&amp;SINR&gt;=-3）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Lf_612D0120/Lf_0806}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">4</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">时长LTE覆盖率（RSRP≥-100&amp;SINR&gt;=-3）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Lf_612D011D/Lf_0805}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">5</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">时长LTE覆盖率（RSRP≥-110&amp;SINR&gt;=-3）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Lf_612D0121/Lf_0805}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">6</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">LTE覆盖率（RSRP≥-100&amp;SINR&gt;=0）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">7</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Lf_612D0109/Lf_612D0101 }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">7</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">LTE覆盖率（RSRP≥-110&amp;SINR&gt;=0）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">8</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Lf_612D0102/Lf_612D0101}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">8</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">平均RSRP</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">9</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D0309}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">9</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">边缘RSRP</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">10</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D1204}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">10</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RSRP连续弱覆盖比例</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">11</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{((value4[3188] / 1000) / Lf_0806) *100}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">11</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RSRP连续无覆盖比例</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">12</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{((value4[3191] / 1000) / Lf_0806) *100}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">12</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">连续UE高发射功率里程占比</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">13</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{((value4[3190] / 1000) / Lf_0806) *100}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">13</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">下载LTE驻网里程占比</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">14</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Lf_0875/Lf_0806 }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">14</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">LTE驻网时长占比</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">15</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Lf_0874/Lf_0805 }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">15</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">平均SINR</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">16</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D0403}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">16</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">边缘SINR</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">17</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D1201}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">17</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">平均RSRQ</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">18</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D0702}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">18</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">重叠覆盖度≥4比例</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">19</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D0F04*100/Lf_612D0F01 }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">19</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">重叠覆盖里程占比</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">20</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Lf_612D0F03/Lf_0806) *100}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">20</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">连续SINR质差里程占比</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">21</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{((value4[3188] / 1000) / Lf_0806) *100}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">21</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">Mod3冲突比例</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">22</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">MAC层上行平均BLER</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">23</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">MAC层下行平均BLER</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">24</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">上行初始HARQ重传比率</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">25</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">下行初始HARQ重传比率</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">26</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">下行平均每时隙调度PRB个数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">27</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value2[3557]*1000/(2*value3[3557])}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">27</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">上行平均每时隙调度PRB个数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">28</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value1[3560]*1000/(2*value4[3560])}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">28</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">下行子帧调度率（个/秒）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">29</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{((value2[3557]*1000000/(value4[3557])) / (value2[3557]*1000/(2*value3[3557]))) / 2}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">29</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">上行子帧调度率（个/秒）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">30</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{((value1[3560]*1000000/(value2[3560])) / (value1[3560]*1000/(2*value4[3560]))) / 2}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">30</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">下行平均每秒调度PRB个数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">31</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value2[3557]*1000000/(value4[3557])}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">31</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">上行平均每秒调度PRB个数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">32</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value1[3560]*1000000/(value2[3560])}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">32</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">应用层平均下载速率（含掉线）(Mbps)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">33</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{((value10[3557]+value10[3558]+value10[3591])*(1000*8)/((value4[3557]+value4[3558]+value4[3591])*1024))/1024 }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">33</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">应用层平均下载速率（不含掉线）(Mbps)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">34</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{((value10[3557])*(1000*8)/((value4[3557])*1024) )/1024}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">34</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">应用层平均上传速率（含掉线）(Mbps)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">35</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{((value10[3560]+value10[3561]+value10[3536])*(1000*8)/((value2[3560]+value2[3561]+value2[3536])*1024))/1024 }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">35</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">应用层平均上传速率（不含掉线）(Mbps)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">36</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{((value10[3560])*(1000*8)/((value2[3560])*1024))/1024 }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">36</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">FTP下载尝试次数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">37</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[3557]+evtIdCount[3558]+value9[3557]+value9[3558]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">37</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">FTP下载成功次数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">38</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[3557]+value9[3557]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">38</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">FTP下载掉线次数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">39</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[3558]+value9[3558]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">39</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">下载掉线率（%）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">40</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(evtIdCount[3558]+value9[3558])*100/(evtIdCount[3557]+value9[3557]+evtIdCount[3558]+value9[3558])}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">40</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">FTP上传尝试次数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">41</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[3559]+value9[3559]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">41</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">FTP上传成功次数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">42</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[3560]+value9[3560]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">42</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">FTP上传掉线次数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">43</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[3561]+value9[3561]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">43</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">上传掉线率%</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">44</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(evtIdCount[3561]+value9[3561])/(evtIdCount[3560]+evtIdCount[3561]+value9[3560]+value9[3561])}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">44</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">下行码字0MCS平均值</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">45</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D0A02}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">45</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">下行码字1MCS平均值</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">46</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D0B02}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">46</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">下行MCS平均统计</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">47</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Lf_612D0A01*Lf_612D0A02+Lf_612D0B01*Lf_612D0B02)/(Lf_612D0A01+Lf_612D0B01) }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">47</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">上行MCS平均统计</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">48</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D0C02}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">48</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">码字0CQI平均值</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">49</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D0D02}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">49</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">码字1CQI平均值</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">50</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D0D04}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">50</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">下行CQI平均统计</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">51</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Lf_612D0D01*Lf_612D0D02+Lf_612D0D03*Lf_612D0D04)/(Lf_612D0D01+Lf_612D0D03) }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">51</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">下行码字0 64QAM占比</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">52</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Lf_612D0E04/(Lf_612D0E02+Lf_612D0E03+Lf_612D0E04)) * 100}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">52</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">下行码字0 16QAM比例</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">53</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Lf_612D0E03/(Lf_612D0E02+Lf_612D0E03+Lf_612D0E04)) * 100}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">53</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">下行码字0 QPSK比例</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">54</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Lf_612D0E02/(Lf_612D0E02+Lf_612D0E03+Lf_612D0E04)) * 100}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">54</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">下行码字1 64QAM占比</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">55</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Lf_612D0E08/(Lf_612D0E06+Lf_612D0E07+Lf_612D0E08)) *100}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">55</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">下行码字1 16QAM比例</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">56</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Lf_612D0E07/(Lf_612D0E06+Lf_612D0E07+Lf_612D0E08)) *100}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">56</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">下行码字1 QPSK比例</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">57</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Lf_612D0E06/(Lf_612D0E06+Lf_612D0E07+Lf_612D0E08)) *100}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">57</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">下行64QAM占比</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">58</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Lf_612D0E04+Lf_612D0E08)/(Lf_612D0E02+Lf_612D0E03+Lf_612D0E04+Lf_612D0E06+Lf_612D0E07+Lf_612D0E08) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">58</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">上行64QAM占比</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">59</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Lf_612D0E0C/(Lf_612D0E0A+Lf_612D0E0B+Lf_612D0E0C) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">59</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">上报RANK1采样占比</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">60</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D1003 * 100/Lf_612D1001 }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">60</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">上报RANK2采样占比</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">61</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D1005 * 100/Lf_612D1001 }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">61</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">双流时长占比</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">62</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Lf_612D1006/Lf_612D1002) * 100 }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">62</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">网内切换次数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">63</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[3154]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">63</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">网内切换成功</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">64</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[3155]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">64</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">网内切换成功率（%）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">65</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[3155]*100/evtIdCount[3154]}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">65</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">网内切换平均时延（s）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">66</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value1[3155]/(evtIdCount[3155]*1000)}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">66</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TA更新次数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">67</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[3170]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">67</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TA更新成功</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">68</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[3171]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">68</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TA更新成功率（%）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">69</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[3171]*100/evtIdCount[3170]}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">69</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TA更新时延（s）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">70</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value1[3171]/(evtIdCount[3171]*1000)}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">70</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">attach尝试次数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">71</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[3521]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">71</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">attach成功</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">72</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[3522]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">72</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">attach成功率（%）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">73</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[3522]*100/evtIdCount[3521]}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">73</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">ATTACH平均时延（s）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">74</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value1[3522]/(evtIdCount[3522]*1000)}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">74</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">物理层平均下载速率（含掉线）(kbps)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">75</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D1312/1024}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">75</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">物理层平均上传载速率（含掉线）(kbps)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">76</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D1412/1024}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">76</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">Mac层平均下载速率（含掉线）(kbps)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">77</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D1512/1024}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">77</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">Mac层平均上传速率（含掉线）(kbps)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">78</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D1612/1024}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">78</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">测试里程（Km）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">79</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_0806 / 1000}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">79</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">测试总时长（分钟）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">80</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_0805/(60*1000)}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">80</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">脱网里程（KM）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">81</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D0317/1000}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">81</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">脱网时长（分钟）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">82</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D0316/(1000*60)}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">82</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">平均车速（km/h）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">83</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_0875 * 3600/Lf_0874 }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">83</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总采用点</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">84</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D0101 }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">84</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RSRP≥-100&amp;SINR&gt;=-3 采样点数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">85</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D0103 }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">85</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RSRP≥-110&amp;SINR&gt;=-3 采样点数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">86</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D0107 }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">86</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RSRP≥-100采样点数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">87</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D0302}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">87</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RSRP≥-110 采样点数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">88</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D0308+Lf_612D0307+Lf_612D0306+Lf_612D0305+Lf_612D0304 }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">88</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SINR&gt;=-3 采样点数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">89</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D0402}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">89</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SINR＜-3 采样点数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">90</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D0413}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">90</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SINR ≥ 0 采样点数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">91</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D0405+Lf_612D0406+Lf_612D0407+Lf_612D0408+Lf_612D0409+Lf_612D040A+Lf_612D040B }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">91</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">含掉线下载数据量（Kb）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">92</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(value10[3557]+value10[3558]+value10[3591])/1024}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">92</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">含掉线下载时间（s）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">93</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(value4[3557]+value4[3558]+value4[3591])/1000 }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">93</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">不含掉线下载数据量（Kb）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">94</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(value10[3557])/1024 }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">94</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">不含掉线下载时间（s）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">95</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value4[3557]/1000 }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">95</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">含掉线上传数据量（Kb）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">96</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(value10[3560]+value10[3561]+value10[3536])/1024}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">96</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">含掉线上传时间（s）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">97</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(value2[3560]+value2[3561]+value2[3536])/1000 }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">97</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">不含掉线上传数据量（Kb）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">98</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value10[3560]/1024 }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">98</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">不含掉线上传时间（s）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">99</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value2[3560]/1000}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">99</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">Service请求次数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">100</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[3185]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">100</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">Service成功次数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">101</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[3186]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">101</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">剔除重复后Service成功率</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">102</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(evtIdCount[3186]*100)/(evtIdCount[3186]+evtIdCount[3187]+value9[3187])}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">102</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">service失败次数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">103</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{evtIdCount[3187]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">103</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">弱覆盖service失败次数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">104</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value9[3187]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">104</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">下行平均每秒调度PRB个数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">105</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value2[3557]*1000000/(value4[3557])}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">105</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">下行平均每时隙调度PRB个数</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">106</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value2[3557]*1000/(2*value3[3557])}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">106</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">传输模式（TM=1）时长占比（ms）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">107</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D1102*100/Lf_612D1101 }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">107</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">传输模式（TM=2）时长占比（ms）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">108</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D1103*100/Lf_612D1101 }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">108</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">传输模式（TM=3）时长占比（ms）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">109</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D1104*100/Lf_612D1101 }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">109</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">传输模式（TM=4）时长占比（ms）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">110</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D1105*100/Lf_612D1101 }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">110</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">传输模式（TM=5）时长占比（ms）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">111</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D1106*100/Lf_612D1101 }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">111</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">传输模式（TM=6）时长占比（ms）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">112</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D1107*100/Lf_612D1101 }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">112</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">传输模式（TM=7）时长占比（ms）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">113</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D1108*100/Lf_612D1101 }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">113</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">传输模式（TM=8）时长占比（ms）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">114</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Lf_612D1109*100/Lf_612D1101 }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">114</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">单流时长占比</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">115</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Lf_612D1004/Lf_612D1002) * 100 }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">115</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">双流时长占比</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">116</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Lf_612D1006/Lf_612D1002) * 100 }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">116</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">每RB平均下载量（不含掉线）(bit/RB)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">117</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{((value10[3557])*8)/(value2[3557]*1000)}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">117</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">每RB平均下载量（含掉线）(bit/RB)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">118</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{((value10[3557]+value10[3558]+value10[3591])*8)/((value2[3557]+value2[3558]+value2[3591])*1000)}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">118</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">Service成功率(%)</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">119</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(evtIdCount[3186]*100)/(evtIdCount[3185])}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">119</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">D频段时长占比（%）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">120</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Lf_612D2811/(Lf_612D2911+Lf_612D2811+Lf_612D2A11) )*100 }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">120</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">E频段时长占比（%）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">121</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Lf_612D2911/(Lf_612D2911+Lf_612D2811+Lf_612D2A11)) *100}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">121</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">F频段时长占比（%）</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">122</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Lf_612D2A11/(Lf_612D2911+Lf_612D2811+Lf_612D2A11)) *100 }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">122</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">LTE网内应用层平均下载速率（含掉线）Mbps</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">123</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Lte_052109020101)*(1000*8)/((Lte_052109020102)*(1024*1024))}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">123</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">WCDMA网内下应用层平均载速率（含掉线）kbps</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">124</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Lte_052101020101+Lte_052102020101+Lte_052103020101+Lte_052105020101)*(1000*8)/((Lte_052101020102+Lte_052102020102+Lte_052103020102+Lte_052105020102)*(1024))}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">124</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">重选到2G/3G比例</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">125</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(100*evtIdCount[3299]+evtIdCount[3301])/(evtIdCount[3299]+evtIdCount[3301]+evtIdCount[3315])}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">125</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">切换到2G/3G比例</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">126</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(evtIdCount[3116]+evtIdCount[3128])/(evtIdCount[3155]+evtIdCount[3116]+evtIdCount[3128])}%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">126</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RSRP低于-100的平均下载速率-4G网络按采样点</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">127</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{((Lte_052109020181+Lte_05210902017F+Lte_05210902017D)*(1000*8))/((Lte_052109020182+Lte_052109020180+Lte_05210902017E) *1024)}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">127</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RSRP低于-105的平均下载速率-4G网络按采样点</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">128</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{((Lte_05210902017F+Lte_05210902017D)*(1000*8))/((Lte_052109020180+Lte_05210902017E) *1024)}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">128</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RSRP低于-110的平均下载速率-4G网络按采样点</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">129</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{((Lte_05210902017D)*(1000*8))/((Lte_05210902017E) *1024)}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">129</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RSRP低于-100或SINR低于-3的平均下载速率-4G网络按采样点</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">130</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{((Lte_0521090201E5)*(1000*8))/((Lte_0521090201E6) *1024)}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">130</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RSRP低于-105或SINR低于-3的平均下载速率-4G网络按采样点</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">131</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{((Lte_0521090201E7)*(1000*8))/((Lte_0521090201E8) *1024)}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">131</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RSRP低于-110或SINR低于-3的平均下载速率-4G网络按采样点</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">132</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{((Lte_0521090201E9)*(1000*8))/((Lte_0521090201EA) *1024)}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">132</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">SINR低于-3的平均下载速率-4G网络按采样点</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">133</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{((Lte_052109020101-(Lte_05210902014E+Lte_05210902014C+Lte_05210902014A+Lte_052109020148+Lte_052109020146+Lte_052109020144+Lte_052109020142+Lte_052109020140+Lte_05210902013E)) *(1000*8))/((Lte_052109020102-(Lte_05210902014F+Lte_05210902014D+Lte_05210902014B+Lte_052109020149+Lte_052109020147+Lte_052109020145+Lte_052109020143+Lte_052109020141+Lte_05210902013F))*1024) }</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">133</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">低于2M速率占比-234G网络按采样点</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">134</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Lte_052164020105+Lte_052164020106+Lte_052164020107)/Lte_052164020103 }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">134</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">低于4M速率占比-234G网络按采样点</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">135</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Lte_052164020105+Lte_052164020106+Lte_052164020107+Lte_052164020108+Lte_052164020109)/Lte_052164020103 }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">135</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">高于10M速率占比-234G网络按采样点</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">136</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Lte_05216402010D+Lte_05216402010E+Lte_05216402010F+Lte_052164020110)/Lte_052164020103 }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">136</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">高于20M速率占比-234G网络按采样点</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">137</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Lte_05216402010F+Lte_052164020110)/Lte_052164020103 }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">137</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">低于2M速率占比-234G网络按里程</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">138</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Lte_0521640201D9+Lte_0521640201DA+Lte_0521640201DB) /(Lte_0521640201D9+Lte_0521640201DA+Lte_0521640201DB+Lte_0521640201DC+Lte_0521640201DD+Lte_0521640201DE+Lte_0521640201DF+Lte_0521640201E0+Lte_0521640201E1+Lte_0521640201E2+Lte_0521640201E3+Lte_0521640201E4) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">138</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">低于4M速率占比-234G网络按里程</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">139</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Lte_0521640201D9+Lte_0521640201DA+Lte_0521640201DB+Lte_0521640201DC+Lte_0521640201DD) /(Lte_0521640201D9+Lte_0521640201DA+Lte_0521640201DB+Lte_0521640201DC+Lte_0521640201DD+Lte_0521640201DE+Lte_0521640201DF+Lte_0521640201E0+Lte_0521640201E1+Lte_0521640201E2+Lte_0521640201E3+Lte_0521640201E4) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">139</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">高于10M速率占比-234G网络按里程</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">140</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Lte_0521640201E0+Lte_0521640201E1+Lte_0521640201E2+Lte_0521640201E3+Lte_0521640201E4) /(Lte_0521640201D9+Lte_0521640201DA+Lte_0521640201DB+Lte_0521640201DC+Lte_0521640201DD+Lte_0521640201DE+Lte_0521640201DF+Lte_0521640201E0+Lte_0521640201E1+Lte_0521640201E2+Lte_0521640201E3+Lte_0521640201E4) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">140</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">高于20M速率占比-234G网络按里程</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">141</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Lte_0521640201E2+Lte_0521640201E3+Lte_0521640201E4) /(Lte_0521640201D9+Lte_0521640201DA+Lte_0521640201DB+Lte_0521640201DC+Lte_0521640201DD+Lte_0521640201DE+Lte_0521640201DF+Lte_0521640201E0+Lte_0521640201E1+Lte_0521640201E2+Lte_0521640201E3+Lte_0521640201E4) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">141</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">低于2M速率占比-4G网络按采样点</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">142</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Lte_052109020105+Lte_052109020106+Lte_052109020107)/Lte_052109020103 }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">142</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">低于4M速率占比-4G网络按采样点</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">143</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Lte_052109020105+Lte_052109020106+Lte_052109020107+Lte_052109020108+Lte_052109020109)/Lte_052109020103 }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">143</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">高于10M速率占比-4G网络按采样点</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">144</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Lte_05210902010D+Lte_05210902010E+Lte_05210902010F+Lte_052109020110)/Lte_052109020103 }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">144</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">高于20M速率占比-4G网络按采样点</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">145</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Lte_05210902010F+Lte_052109020110)/Lte_052109020103 }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">145</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">低于2M速率占比-4G网络按里程</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">146</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Lte_0521090201D9+Lte_0521090201DA+Lte_0521090201DB+Lte_0521090201DC+Lte_0521090201DD) /(Lte_0521090201D9+Lte_0521090201DA+Lte_0521090201DB+Lte_0521090201DC+Lte_0521090201DD+Lte_0521090201DE+Lte_0521090201DF+Lte_0521090201E0+Lte_0521090201E1+Lte_0521090201E2+Lte_0521090201E3+Lte_0521090201E4) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">146</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">低于4M速率占比-4G网络按里程</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">147</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Lte_0521090201D9+Lte_0521090201DA+Lte_0521090201DB+Lte_0521090201DC+Lte_0521090201DD) /(Lte_0521090201D9+Lte_0521090201DA+Lte_0521090201DB+Lte_0521090201DC+Lte_0521090201DD+Lte_0521090201DE+Lte_0521090201DF+Lte_0521090201E0+Lte_0521090201E1+Lte_0521090201E2+Lte_0521090201E3+Lte_0521090201E4) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">147</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">高于10M速率占比-4G网络按里程</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">148</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Lte_0521090201E0+Lte_0521090201E1+Lte_0521090201E2+Lte_0521090201E3+Lte_0521090201E4) /(Lte_0521090201D9+Lte_0521090201DA+Lte_0521090201DB+Lte_0521090201DC+Lte_0521090201DD+Lte_0521090201DE+Lte_0521090201DF+Lte_0521090201E0+Lte_0521090201E1+Lte_0521090201E2+Lte_0521090201E3+Lte_0521090201E4) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">148</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">高于20M速率占比-4G网络按里程</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">149</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*(Lte_0521090201E2+Lte_0521090201E3+Lte_0521090201E4) /(Lte_0521090201D9+Lte_0521090201DA+Lte_0521090201DB+Lte_0521090201DC+Lte_0521090201DD+Lte_0521090201DE+Lte_0521090201DF+Lte_0521090201E0+Lte_0521090201E1+Lte_0521090201E2+Lte_0521090201E3+Lte_0521090201E4) }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">149</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">FTP下载尝试次数-GSM</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">150</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value5[3557]+value5[3558]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">150</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">FTP下载掉线次数-GSM</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">151</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value5[3557]+value5[3558]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">151</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">FTP下载尝试次数-WCDMA</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">152</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value6[3557]+value6[3558]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">152</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">FTP下载掉线次数-WCDMA</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">153</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value6[3558]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">153</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">FTP下载尝试次数-LTE</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">154</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value8[3557]+value8[3558]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">154</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">FTP下载掉线次数-LTE</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">155</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value8[3558]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">155</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">FTP上传尝试次数-GSM</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">156</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value5[3560]+value5[3561]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">156</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">FTP上传掉线次数-GMS</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">157</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value5[3561]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">157</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">FTP上传尝试次数-WCDMA</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">158</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value6[3560]+value6[3561]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">158</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">FTP上传掉线次数-WCDMA</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">159</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value6[3561]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">159</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">FTP上传尝试次数-LTE</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">160</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value8[3560]+value8[3561]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">160</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">FTP上传掉线次数-LTE</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">161</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{value8[3561]}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">161</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{100*Lf_612D1B01/Lf_612D1B02 }%</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">22</Item>
          <Item key="Rcells" />
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
          <Item typeName="Int32" key="momt">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">False</Item>
          <Item typeName="IList" key="ServiceIDSet" />
          <Item typeName="Int32" key="DeciNum">4</Item>
          <Item key="FileNameFilter" />
        </Item>
      </Item>
      <Item typeName="IList" key="Graphs" />
      <Item typeName="IList" key="ColInfo" />
      <Item typeName="IList" key="ColWidth">
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
      </Item>
    </Item>
  </Config>
</Configs>