﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryLteComplaintAna : QueryBase
    {
        public QueryLteComplaintAna(MainModel mainModel)
            : base(mainModel)
        { 
        }

        public override string Name
        {
            get { return "网络投诉关联站点分析"; }
        }
        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22107, this.Name);
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.None;
        }
        protected override bool isValidCondition()
        {
            return true;
        }
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        List<LteComplaintCond> codList = null;
        Dictionary<string, List<ComplaitInfo>> complaitInfoDic = null;
        protected override void query()
        {
            codList = new List<LteComplaintCond>();
            LteComplaintSetForm complaintSetForm = new LteComplaintSetForm();
            if (complaintSetForm.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            if (complaintSetForm.StrExcelPath == "")
            {
                MessageBox.Show("请选择要分析的数据源...");
                return;
            }
            codList = complaintSetForm.CondList;
            WaitBox.CanCancel = true;
            WaitBox.Show("1.正在加载投诉数据源...", doWithComplaintData, complaintSetForm.StrExcelPath);
            fireShowForm();
        }

        private void doWithComplaintData(object obj)
        {
            string strPath = obj as string;
            DataSet dataSet = ExcelNPOIManager.ImportFromExcel(strPath);
            DataTable compData = dataSet.Tables["投诉分析"];
            complaitInfoDic = doImportCompByCity(compData);
            WaitBox.ProgressPercent = 23;
            WaitBox.Text = "2.正在加载全省工参...";
            Dictionary<string, Dictionary<string, List<NOPCellInfo>>> nopCellInfoDic = getNOPCellInfo();
            WaitBox.ProgressPercent = 46;
            WaitBox.Text = "3.开始按地市分析投诉数据...";
            anaCompInfo(nopCellInfoDic);
            WaitBox.Close();
        }

        private Dictionary<string, List<ComplaitInfo>> doImportCompByCity(DataTable compData)
        {
            Dictionary<string, List<ComplaitInfo>> complaitDic = new Dictionary<string, List<ComplaitInfo>>();
            foreach (DataRow row in compData.Rows)
            {
                ComplaitInfo cInfo = new ComplaitInfo();
                if (cInfo.FillData(row))
                {
                    List<ComplaitInfo> compList;
                    if (!complaitDic.TryGetValue(cInfo.StrCity, out compList))
                    {
                        compList = new List<ComplaitInfo>();
                    }
                    compList.Add(cInfo);
                    complaitDic[cInfo.StrCity] = compList;
                }
            }
            return complaitDic;
        }

        private Dictionary<string, Dictionary<string, List<NOPCellInfo>>> getNOPCellInfo()
        {
            DiyNopCellInfo queryNopCell = new DiyNopCellInfo(MainModel, "");
            queryNopCell.SetQueryCondition(condition);
            queryNopCell.LteComplaint = true;
            queryNopCell.Query();
            return queryNopCell.nopCellInfoDic;
        }

        private void anaCompInfo(Dictionary<string, Dictionary<string, List<NOPCellInfo>>> nopCellInfoDic)
        {
            foreach (string strCity in complaitInfoDic.Keys)
            {
                List<ComplaitInfo> tmpList = complaitInfoDic[strCity];
                Dictionary<string, List<NOPCellInfo>> nopCellDic = nopCellInfoDic[strCity];
                int indexNum = 1, iCount = tmpList.Count;
                foreach (ComplaitInfo cInfo in tmpList)
                {
                    if (indexNum++ % 100 == 0)
                    {
                        WaitBox.Text = string.Format("3.正在分析地市 {0},当前地市进度 {1}/{2}...", strCity, indexNum, iCount);
                    }
                    foreach (string strNet in nopCellDic.Keys)
                    {
                        NOPCellInfo nopTmpInfo = getNOPCellInfo(nopCellDic, cInfo, strNet);
                        setCell(cInfo, strNet, nopTmpInfo);
                    }
                }
            }
        }

        private NOPCellInfo getNOPCellInfo(Dictionary<string, List<NOPCellInfo>> nopCellDic, ComplaitInfo cInfo, string strNet)
        {
            NOPCellInfo nopTmpInfo = new NOPCellInfo();
            List<NOPCellInfo> cellList = nopCellDic[strNet];
            foreach (NOPCellInfo nopCell in cellList)
            {
                double dDist = LteComplaintHelper.GetPointDist(cInfo.FLongitude, cInfo.FLatitude, nopCell.Longitude, nopCell.Latitude, 500);
                if (dDist > 500)
                {
                    continue;
                }
                int iAngle = LteComplaintHelper.MrCellNopBtsAngle(nopCell.Longitude, nopCell.Latitude, cInfo.FLongitude, cInfo.FLatitude, nopCell.iDirection, dDist);
                if (iAngle <= 60)
                {
                    double dCellWeight = calCellWeight(iAngle, dDist);
                    if (nopTmpInfo.StrCellName == "" || dCellWeight > nopTmpInfo.DCellWeight
                        || (dCellWeight == nopTmpInfo.DCellWeight && nopTmpInfo.DDist > dDist))
                    {
                        nopTmpInfo = nopCell;
                        nopTmpInfo.DDist = (float)dDist;
                        nopTmpInfo.iDiffDirection = iAngle;
                        nopTmpInfo.DCellWeight = dCellWeight;
                    }
                }
            }

            return nopTmpInfo;
        }

        private static void setCell(ComplaitInfo cInfo, string strNet, NOPCellInfo nopTmpInfo)
        {
            if (strNet == "GSM")
                cInfo.gsmCell = nopTmpInfo;
            else if (strNet == "TD")
                cInfo.tdCell = nopTmpInfo;
            else if (strNet == "LTE")
                cInfo.lteCell = nopTmpInfo;
        }

        private double calCellWeight(int iAngle, double dDistance)
        {
            double dAngleWeight = 0.0;
            double dDistanceWeight = 0.0;
            foreach (LteComplaintCond cod in codList)
            {
                if (cod.IAngleMin >= iAngle && iAngle < cod.IAngleMax)
                {
                    dAngleWeight = cod.DAngleWeight;
                    break;
                }
            }
            foreach (LteComplaintCond cod in codList)
            {
                if (cod.IDistanceMin >= dDistance && dDistance < cod.IDistanceMax)
                {
                    dDistanceWeight = cod.DAngleWeight;
                    break;
                }
            }
            return dAngleWeight * dDistanceWeight;
        }

        protected void fireShowForm()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(LteComplaintForm).FullName);
            LteComplaintForm resultForm = obj == null ? null : obj as LteComplaintForm;
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new LteComplaintForm(MainModel);
            }
            resultForm.FillData(complaitInfoDic);
            if (!resultForm.Visible)
            {
                resultForm.Show(MainModel.MainForm);
            }
        }
    }
}
