﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class KPIListForm_HaiNan
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.gridControlKPI = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ExportToExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridViewKPI = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.label1 = new System.Windows.Forms.Label();
            this.btnCustom = new DevExpress.XtraEditors.SimpleButton();
            this.btnQuery = new DevExpress.XtraEditors.SimpleButton();
            this.cbxTemplate = new DevExpress.XtraEditors.ComboBoxEdit();
            this.chkCbxTestTag = new DevExpress.XtraEditors.CheckedComboBoxEdit();
            this.label2 = new System.Windows.Forms.Label();
            this.bkWorker = new System.ComponentModel.BackgroundWorker();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlKPI)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewKPI)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxTemplate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCbxTestTag.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControlKPI
            // 
            this.gridControlKPI.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.gridControlKPI.ContextMenuStrip = this.contextMenuStrip;
            this.gridControlKPI.Location = new System.Drawing.Point(12, 47);
            this.gridControlKPI.MainView = this.gridViewKPI;
            this.gridControlKPI.Name = "gridControlKPI";
            this.gridControlKPI.Size = new System.Drawing.Size(928, 255);
            this.gridControlKPI.TabIndex = 0;
            this.gridControlKPI.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewKPI});
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ExportToExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 26);
            // 
            // ExportToExcel
            // 
            this.ExportToExcel.Name = "ExportToExcel";
            this.ExportToExcel.Size = new System.Drawing.Size(129, 22);
            this.ExportToExcel.Text = "导出Excel";
            this.ExportToExcel.Click += new System.EventHandler(this.ExportToExcel_Click);
            // 
            // gridViewKPI
            // 
            this.gridViewKPI.Appearance.ViewCaption.Options.UseTextOptions = true;
            this.gridViewKPI.Appearance.ViewCaption.TextOptions.Trimming = DevExpress.Utils.Trimming.Word;
            this.gridViewKPI.Appearance.ViewCaption.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridViewKPI.ColumnPanelRowHeight = 50;
            this.gridViewKPI.GridControl = this.gridControlKPI;
            this.gridViewKPI.IndicatorWidth = 40;
            this.gridViewKPI.Name = "gridViewKPI";
            this.gridViewKPI.OptionsBehavior.Editable = false;
            this.gridViewKPI.OptionsBehavior.ReadOnly = true;
            this.gridViewKPI.OptionsView.ColumnAutoWidth = false;
            this.gridViewKPI.OptionsView.EnableAppearanceEvenRow = true;
            this.gridViewKPI.OptionsView.ShowGroupPanel = false;
            this.gridViewKPI.CustomDrawColumnHeader += new DevExpress.XtraGrid.Views.Grid.ColumnHeaderCustomDrawEventHandler(this.gridViewKPI_CustomDrawColumnHeader);
            this.gridViewKPI.CustomDrawRowIndicator += new DevExpress.XtraGrid.Views.Grid.RowIndicatorCustomDrawEventHandler(this.gridViewKPI_CustomDrawRowIndicator);
            this.gridViewKPI.RowCellStyle += new DevExpress.XtraGrid.Views.Grid.RowCellStyleEventHandler(this.gridViewKPI_RowCellStyle);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(12, 16);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(67, 14);
            this.label1.TabIndex = 1;
            this.label1.Text = "测试轮次：";
            // 
            // btnCustom
            // 
            this.btnCustom.Location = new System.Drawing.Point(644, 12);
            this.btnCustom.Name = "btnCustom";
            this.btnCustom.Size = new System.Drawing.Size(89, 22);
            this.btnCustom.TabIndex = 6;
            this.btnCustom.Text = "定制";
            this.btnCustom.Click += new System.EventHandler(this.btnCustom_Click);
            // 
            // btnQuery
            // 
            this.btnQuery.Location = new System.Drawing.Point(758, 12);
            this.btnQuery.Name = "btnQuery";
            this.btnQuery.Size = new System.Drawing.Size(89, 22);
            this.btnQuery.TabIndex = 7;
            this.btnQuery.Text = "查询";
            this.btnQuery.Click += new System.EventHandler(this.btnQuery_Click);
            // 
            // cbxTemplate
            // 
            this.cbxTemplate.Location = new System.Drawing.Point(378, 13);
            this.cbxTemplate.Name = "cbxTemplate";
            this.cbxTemplate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxTemplate.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxTemplate.Size = new System.Drawing.Size(222, 21);
            this.cbxTemplate.TabIndex = 14;
            // 
            // chkCbxTestTag
            // 
            this.chkCbxTestTag.Location = new System.Drawing.Point(71, 12);
            this.chkCbxTestTag.Name = "chkCbxTestTag";
            this.chkCbxTestTag.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.chkCbxTestTag.Size = new System.Drawing.Size(222, 21);
            this.chkCbxTestTag.TabIndex = 15;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(339, 16);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(43, 14);
            this.label2.TabIndex = 1;
            this.label2.Text = "报表：";
            // 
            // bkWorker
            // 
            this.bkWorker.DoWork += new System.ComponentModel.DoWorkEventHandler(this.bkWorker_DoWork);
            this.bkWorker.RunWorkerCompleted += new System.ComponentModel.RunWorkerCompletedEventHandler(this.bkWorker_RunWorkerCompleted);
            // 
            // KPIListForm_HaiNan
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(952, 314);
            this.ControlBox = false;
            this.Controls.Add(this.chkCbxTestTag);
            this.Controls.Add(this.cbxTemplate);
            this.Controls.Add(this.btnQuery);
            this.Controls.Add(this.btnCustom);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.gridControlKPI);
            this.Name = "KPIListForm_HaiNan";
            this.Text = "指标";
            this.Shown += new System.EventHandler(this.KPIListForm_Shown);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlKPI)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewKPI)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbxTemplate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCbxTestTag.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControlKPI;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewKPI;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.SimpleButton btnCustom;
        private DevExpress.XtraEditors.SimpleButton btnQuery;
        private DevExpress.XtraEditors.ComboBoxEdit cbxTemplate;
        private DevExpress.XtraEditors.CheckedComboBoxEdit chkCbxTestTag;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem ExportToExcel;
        private System.Windows.Forms.Label label2;
        private System.ComponentModel.BackgroundWorker bkWorker;
    }
}