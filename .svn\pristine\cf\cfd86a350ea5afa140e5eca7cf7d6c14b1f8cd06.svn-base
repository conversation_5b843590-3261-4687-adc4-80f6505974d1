﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NbIotMgrsWeakSinrSetting
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.numMinSINR = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.numGridCount = new System.Windows.Forms.NumericUpDown();
            this.label4 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.numMinSINR)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numGridCount)).BeginInit();
            this.SuspendLayout();
            // 
            // numMinSINR
            // 
            this.numMinSINR.Location = new System.Drawing.Point(275, 58);
            this.numMinSINR.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numMinSINR.Minimum = new decimal(new int[] {
            1000,
            0,
            0,
            -2147483648});
            this.numMinSINR.Name = "numMinSINR";
            this.numMinSINR.Size = new System.Drawing.Size(120, 21);
            this.numMinSINR.TabIndex = 6;
            this.numMinSINR.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numMinSINR.Value = new decimal(new int[] {
            3,
            0,
            0,
            -2147483648});
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(228, 62);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(41, 12);
            this.label1.TabIndex = 5;
            this.label1.Text = "SINR〈";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.numGridCount);
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Controls.Add(this.numMinSINR);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(620, 92);
            this.groupBox1.TabIndex = 12;
            this.groupBox1.TabStop = false;
            // 
            // numGridCount
            // 
            this.numGridCount.Location = new System.Drawing.Point(276, 15);
            this.numGridCount.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numGridCount.Name = "numGridCount";
            this.numGridCount.Size = new System.Drawing.Size(120, 21);
            this.numGridCount.TabIndex = 15;
            this.numGridCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numGridCount.Value = new decimal(new int[] {
            4,
            0,
            0,
            0});
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(180, 19);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(89, 12);
            this.label4.TabIndex = 14;
            this.label4.Text = "连续栅格个数≥";
            // 
            // NBIOTMgrsWeakSinrSetting
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.groupBox1);
            this.Name = "NBIOTMgrsWeakSinrSetting";
            ((System.ComponentModel.ISupportInitialize)(this.numMinSINR)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numGridCount)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.NumericUpDown numMinSINR;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.NumericUpDown numGridCount;
        private System.Windows.Forms.Label label4;
    }
}
