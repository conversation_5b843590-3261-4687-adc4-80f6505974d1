﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class FDDLittleStationSettingDlg_XJ : BaseDialog
    {
        public FDDLittleStationSettingDlg_XJ()
        {
            InitializeComponent();
        }
        public FDDLittleStationSettingDlg_XJ(FDDSmallStationSettingDlgConfigModel_XJ condition)
        {
            InitializeComponent();
            setCondition(condition);
        }

        public void setCondition(FDDSmallStationSettingDlgConfigModel_XJ condition)
        {
            if (condition == null)
            {
                return;
            }

            txtAccessSuccessRate1.Value = Convert.ToDecimal(condition.AccessSuccessRate);
            txtAccessTestCount1.Value = Convert.ToDecimal(condition.AccessTestCount);
            txtVolteTestCount1.Value = Convert.ToDecimal(condition.VOLTETestCount);
            txtVolteSuccessRate1.Value = Convert.ToDecimal(condition.VOLTESuccessRate);
            txtFTPDownSpeed1.Value = Convert.ToDecimal(condition.FTPDownloadThroughput);
            txtFTPUpSpeed1.Value = Convert.ToDecimal(condition.FTPUploadThroughput);
            txtDoorAvgRSRP.Value = Convert.ToDecimal(condition.InDoorAvgRSRP);
            txtDoorAvgSINR.Value = Convert.ToDecimal(condition.InDoorAvgSINR);
            txtDoorDownThroughput.Value = Convert.ToDecimal(condition.InDoorDownThroughput);
            txtDoorUpThroughput.Value = Convert.ToDecimal(condition.InDoorUpThroughput);
            txtAntAvgRSRP.Value = Convert.ToDecimal(condition.AntAvgRSRP);
            txtAntAvgSINR.Value = Convert.ToDecimal(condition.AntAvgSINR);
            txtStandard1.Text = condition.WeakCoverCheckStandard;
            txtSystemInSwitch1.Value = Convert.ToDecimal(condition.SystemInSwitch);
        }

        public FDDSmallStationSettingDlgConfigModel_XJ GetCondition()
        {
            FDDSmallStationSettingDlgConfigModel_XJ condition = new FDDSmallStationSettingDlgConfigModel_XJ();
            condition.AccessSuccessRate = txtAccessSuccessRate1.Value.ToString();
            condition.AccessTestCount = Convert.ToInt32(txtAccessTestCount1.Value);
            condition.VOLTETestCount = Convert.ToInt32(txtVolteTestCount1.Value);
            condition.VOLTESuccessRate = txtVolteSuccessRate1.Value.ToString();
            condition.FTPDownloadThroughput = txtFTPDownSpeed1.Value.ToString();
            condition.FTPUploadThroughput = txtFTPUpSpeed1.Value.ToString();
            condition.InDoorAvgRSRP = txtDoorAvgRSRP.Value.ToString();
            condition.InDoorAvgSINR = txtDoorAvgSINR.Value.ToString();
            condition.InDoorDownThroughput = txtDoorDownThroughput.Value.ToString();
            condition.InDoorUpThroughput = txtDoorUpThroughput.Value.ToString();
            condition.AntAvgRSRP = txtAntAvgRSRP.Value.ToString();
            condition.AntAvgSINR = txtAntAvgSINR.Value.ToString();
            condition.SystemInSwitch = txtSystemInSwitch1.Value.ToString();
            condition.WeakCoverCheckStandard = txtStandard1.Text;
            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            var cond = GetCondition();
            if (cond != null)
            {
                FDDSmallStationSettingDlgConfig_XJ.Instance.SaveConfig(cond);
                DialogResult = DialogResult.OK;
            }
            else
            {
                MessageBox.Show("主库连接设置不能为空");
            }
        }
    }
}
