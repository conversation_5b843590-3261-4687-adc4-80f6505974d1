﻿namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.CellCoverage
{
    partial class CellCoverResultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.lv = new BrightIdeasSoftware.TreeListView();
            this.colSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colItemName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colIsNearestCell = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colNearestDis = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colNearestSite = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.label1 = new System.Windows.Forms.Label();
            this.cbxCol = new System.Windows.Forms.ComboBox();
            this.lbxLegend = new System.Windows.Forms.ListBox();
            ((System.ComponentModel.ISupportInitialize)(this.lv)).BeginInit();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            this.SuspendLayout();
            // 
            // lv
            // 
            this.lv.AllColumns.Add(this.colSN);
            this.lv.AllColumns.Add(this.colItemName);
            this.lv.AllColumns.Add(this.colIsNearestCell);
            this.lv.AllColumns.Add(this.colDistance);
            this.lv.AllColumns.Add(this.colNearestDis);
            this.lv.AllColumns.Add(this.colNearestSite);
            this.lv.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.colSN,
            this.colItemName,
            this.colIsNearestCell,
            this.colDistance,
            this.colNearestDis,
            this.colNearestSite});
            this.lv.ContextMenuStrip = this.ctxMenu;
            this.lv.Cursor = System.Windows.Forms.Cursors.Default;
            this.lv.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lv.FullRowSelect = true;
            this.lv.GridLines = true;
            this.lv.HeaderWordWrap = true;
            this.lv.IsNeedShowOverlay = false;
            this.lv.Location = new System.Drawing.Point(0, 0);
            this.lv.Name = "lv";
            this.lv.OwnerDraw = true;
            this.lv.ShowGroups = false;
            this.lv.Size = new System.Drawing.Size(772, 362);
            this.lv.TabIndex = 4;
            this.lv.UseCompatibleStateImageBehavior = false;
            this.lv.View = System.Windows.Forms.View.Details;
            this.lv.VirtualMode = true;
            this.lv.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.lv_MouseDoubleClick);
            // 
            // colSN
            // 
            this.colSN.HeaderFont = null;
            this.colSN.Text = "序号";
            // 
            // colItemName
            // 
            this.colItemName.HeaderFont = null;
            this.colItemName.Text = "小区/区域";
            // 
            // colIsNearestCell
            // 
            this.colIsNearestCell.HeaderFont = null;
            this.colIsNearestCell.Text = "是否为最近小区";
            // 
            // colDistance
            // 
            this.colDistance.HeaderFont = null;
            this.colDistance.Text = "区域与覆盖小区距离(m)";
            // 
            // colNearestDis
            // 
            this.colNearestDis.HeaderFont = null;
            this.colNearestDis.Text = "区域与最近基站距离(m)";
            // 
            // colNearestSite
            // 
            this.colNearestSite.HeaderFont = null;
            this.colNearestSite.Text = "区域最近基站";
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExport2Xls});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(139, 26);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(138, 22);
            this.miExport2Xls.Text = "导出Excel...";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel2;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.Panel2;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Style3D;
            this.splitContainerControl1.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.lv);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.label1);
            this.splitContainerControl1.Panel2.Controls.Add(this.cbxCol);
            this.splitContainerControl1.Panel2.Controls.Add(this.lbxLegend);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1004, 362);
            this.splitContainerControl1.SplitterPosition = 228;
            this.splitContainerControl1.TabIndex = 5;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(4, 15);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(74, 14);
            this.label1.TabIndex = 68;
            this.label1.Text = "GIS渲染列：";
            // 
            // cbxCol
            // 
            this.cbxCol.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.cbxCol.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxCol.FormattingEnabled = true;
            this.cbxCol.Location = new System.Drawing.Point(84, 12);
            this.cbxCol.Name = "cbxCol";
            this.cbxCol.Size = new System.Drawing.Size(141, 22);
            this.cbxCol.TabIndex = 67;
            // 
            // lbxLegend
            // 
            this.lbxLegend.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.lbxLegend.DrawMode = System.Windows.Forms.DrawMode.OwnerDrawFixed;
            this.lbxLegend.FormattingEnabled = true;
            this.lbxLegend.IntegralHeight = false;
            this.lbxLegend.ItemHeight = 18;
            this.lbxLegend.Location = new System.Drawing.Point(7, 40);
            this.lbxLegend.Name = "lbxLegend";
            this.lbxLegend.SelectionMode = System.Windows.Forms.SelectionMode.None;
            this.lbxLegend.Size = new System.Drawing.Size(218, 319);
            this.lbxLegend.TabIndex = 66;
            this.lbxLegend.DrawItem += new System.Windows.Forms.DrawItemEventHandler(this.lbxLegend_DrawItem);
            // 
            // CellCoverResultForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1004, 362);
            this.Controls.Add(this.splitContainerControl1);
            this.Name = "CellCoverResultForm";
            this.Text = "小区覆盖统计";
            ((System.ComponentModel.ISupportInitialize)(this.lv)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView lv;
        private BrightIdeasSoftware.OLVColumn colSN;
        private BrightIdeasSoftware.OLVColumn colItemName;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private System.Windows.Forms.ListBox lbxLegend;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.ComboBox cbxCol;
        private BrightIdeasSoftware.OLVColumn colIsNearestCell;
        private BrightIdeasSoftware.OLVColumn colDistance;
        private BrightIdeasSoftware.OLVColumn colNearestDis;
        private BrightIdeasSoftware.OLVColumn colNearestSite;
    }
}