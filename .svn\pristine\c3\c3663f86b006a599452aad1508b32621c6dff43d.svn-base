﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.AnyStat
{
    public class AnyStatUnit
    {
        public List<AnyColumnDef> columnsDef { get; set; } = new List<AnyColumnDef>();
        public int keyColumnCount { get; set; } = 1;
        public string name { get; set; } = "";
        public override string ToString()
        {
            return name;
        }
        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["name"] = name;
                param["keyColumnCount"] = keyColumnCount;
                List<object> columnsParams = new List<object>();
                param["columnsDef"] = columnsParams;
                foreach (AnyColumnDef cr in columnsDef)
                {
                    columnsParams.Add(cr.Param);
                }
                return param;
            }
            set
            {
                name = (String)value["name"];
                keyColumnCount = (int)value["keyColumnCount"];
                columnsDef.Clear();
                List<object> columnsParams = (List<object>)value["columnsDef"];
                foreach (object o in columnsParams)
                {
                    Dictionary<string, object> rptParam = (Dictionary<string, object>)o;
                    AnyColumnDef cr = new AnyColumnDef();
                    cr.Param = rptParam;
                    columnsDef.Add(cr);
                }
            }
        }
        public static IComparer<AnyStatUnit> GetCompareByName()
        {
            if (comparerByName == null)
            {
                comparerByName = new ComparerByName();
            }
            return comparerByName;
        }

        private static IComparer<AnyStatUnit> comparerByName;

        public class ComparerByName : IComparer<AnyStatUnit>
        {
            public int Compare(AnyStatUnit x, AnyStatUnit y)
            {
                return x.name.CompareTo(y.name);
            }
        }

        public Dictionary<string, AnyResultKeyNode> AnyResultNode
        {
            get
            {
                return anyResultNodes;
            }
        }
        private readonly Dictionary<string, AnyResultKeyNode> anyResultNodes = new Dictionary<string, AnyResultKeyNode>();

        public void PushAnyStatDataOnce(TestPoint data)
        {
            AnyResultKeyNode tarNode = null;
            for (int i = 0; i < columnsDef.Count; i++)
            {
                AnyColumnDef columnDef = columnsDef[i];
                object retValue = columnDef.parameter != null ? data[columnDef.parameter] : parseValueFrom(columnDef.keyType, data);
                if (retValue == null)
                {
                    return;
                }
                if (columnDef.valueRangeCheck && !columnDef.CheckRangeOK(retValue))
                {
                    return;
                }
                if (i < keyColumnCount)
                {
                    tarNode = getAnyResultKeyNode(tarNode, i, columnDef, retValue);
                }
                else if (tarNode != null)
                {
                    tarNode.StatValue(retValue, columnDef, i - keyColumnCount);
                }
            }
        }

        private AnyResultKeyNode getAnyResultKeyNode(AnyResultKeyNode tarNode, int i, AnyColumnDef columnDef, object retValue)
        {
            string keyColumnStr = parseRetObjToString(columnDef, retValue);
            if (i == 0)
            {
                if (!anyResultNodes.TryGetValue(keyColumnStr, out tarNode))
                {
                    tarNode = initNode(i);
                    anyResultNodes[keyColumnStr] = tarNode;
                }
            }
            else
            {
                AnyResultKeyNode tmpNode = null;
                if (tarNode != null && !tarNode.anyResultNodes.TryGetValue(keyColumnStr, out tmpNode))
                {
                    tmpNode = initNode(i);
                    tarNode.anyResultNodes[keyColumnStr] = tmpNode;
                    tarNode = tmpNode;
                }
                else
                {
                    tarNode = tmpNode;
                }
            }

            return tarNode;
        }

        private AnyResultKeyNode initNode(int i)
        {
            AnyResultKeyNode node;
            if (i < keyColumnCount - 1)
            {
                node = new AnyResultKeyNode(0);
            }
            else
            {
                node = new AnyResultKeyNode(columnsDef.Count - keyColumnCount);
            }

            return node;
        }

        private string parseRetObjToString(AnyColumnDef columnDef, object retValue)
        {
            if (columnDef.retByRanges.Count > 0)
            {
                float v;
                bool isValid = getValidValue(retValue, out v);
                if (!isValid)
                {
                    return retValue.ToString();
                }
                
                foreach (ValueRange vr in columnDef.retByRanges)
                {
                    if (v >= vr.minValue && v < vr.maxValue)
                    {
                        return vr.ToString();
                    }
                }
            }
            if (retValue is float)
            {
                float v = (float)retValue;
                if (columnDef.decPlace != 2)
                {
                    return string.Format("{0:F" + columnDef.decPlace + "}", v);
                }
            }

            return retValue.ToString();
        }

        private bool getValidValue(object retValue, out float v)
        {
            v = 0;
            if (retValue is int)
            {
                v = (float)(int)retValue;
            }
            else if (retValue is short)
            {
                v = (float)(short)retValue;
            }
            else if (retValue is float)
            {
                v = (float)retValue;
            }
            else if (retValue is double)
            {
                v = (float)(double)retValue;
            }
            else if (retValue is byte)
            {
                v = (float)(byte)retValue;
            }
            else
            {
                return false;
            }

            return true;
        }

        private object parseValueFrom(AnyColumnDef.KeyType keyType, TestPoint data)
        {
            string retvalue = null;
            switch(keyType)
            {
                case AnyColumnDef.KeyType.TIME_yyyy_mm_dd_hh:
                    retvalue = data.DateTime.ToString("yyyy-MM-dd HH");
                    break;
                case AnyColumnDef.KeyType.TIME_yyyy_mm_dd:
                    retvalue = data.DateTime.ToString("yyyy-MM-dd");
                    break;
                case AnyColumnDef.KeyType.TIME_yyyy_mm:
                    retvalue=data.DateTime.ToString("yyy-MM");
                    break;
                case AnyColumnDef.KeyType.TIME_hh:
                    retvalue = data.DateTime.ToString("HH");
                    break;
                case AnyColumnDef.KeyType.GSM_CELL_BANDTYPE:
                    retvalue = getGSMCellType(data, -1);
                    break;
                case AnyColumnDef.KeyType.GSM_N_CELL_BANDTYPE_0:
                    retvalue = getGSMCellType(data, 0);
                    break;
                case AnyColumnDef.KeyType.RegionName:
                    retvalue = curRegionName;
                    break;
                default:
                    break;
            }
            return retvalue;
        }

        private string getGSMCellType(TestPoint data, int nCellNum)
        {
            short? bcch = -255;
            if (nCellNum < 0)
            {
                bcch = (short?)data["BCCH"];
            }
            else if (nCellNum == 0)
            {
                bcch = (short?)data["N_BCCH", 0];
            }
            if(bcch is short)
            {
                if(bcch>=1 && bcch<=200)
                {
                    return "900";
                }
                else if(bcch>=512 && bcch<=885)
                {
                    return "1800";
                }
                else if(bcch>=975 && bcch<=1023)
                {
                    return "E-GSM";
                }
            }
            return null;
            
        }

        private string curRegionName = "";

        private List<string> GetRegionNames(TestPoint tp)
        {
            bool isStatByRegion = false;
            foreach (AnyColumnDef columnDef in columnsDef)
            {
                if (columnDef.keyType == AnyColumnDef.KeyType.RegionName)
                {
                    isStatByRegion = true;
                    break;
                }
            }
            if (!isStatByRegion)    // 没有使用预存区域名分类
            {
                return new List<string>();
            }

            string unknow = "未知网格";
            SearchGeometrys sg = MainModel.GetInstance().SearchGeometrys;
            if (sg == null || sg.SelectedResvRegions == null || sg.SelectedResvRegions.Count == 0)
            {
                return new List<string>() { unknow };   // 使用了区域名分类，但没有加载预存区域
            }

            List<string> retList = new List<string>();
            foreach (ResvRegion rr in sg.SelectedResvRegions)
            {
                if (rr.GeoOp.CheckPointInRegion(tp.Longitude, tp.Latitude))
                {
                    retList.Add(rr.RegionName);
                }
            }
            if (retList.Count == 0)     // 该测试点不在任意一个预存区域中，按文件进行任意统计并且设置了预存区域，才会出现这种情况。
            {
                retList.Add(unknow);
            }
            return retList;
        }

        /*
         * 原本功能中对任意一个测试点，每个列定义都会返回唯一的列值
         * 加入预存区域名这个分类维度后，这个列定义可能会返回多个列值（区域重叠部分）
         * 这相当于给一个测试点的该列定义赋予多个值，然后对该测试点进行多次计算
         */
        public void PushAnyStatData(TestPoint data)
        {
            List<string> regionNames = GetRegionNames(data);
            if (regionNames == null || regionNames.Count == 0) // 不存在预存区域名分类
            {
                PushAnyStatDataOnce(data);
                return;
            }
            foreach (string regName in regionNames)
            {
                curRegionName = regName;
                PushAnyStatDataOnce(data);
            }
        }
    }
    public class AnyColumnDef
    {
        public enum KeyType
        {
            TIME_yyyy_mm_dd_hh = 0,
            TIME_yyyy_mm_dd =  1,
            TIME_yyyy_mm=2,
            TIME_hh = 3,
            GSM_CELL_BANDTYPE =4,
            GSM_N_CELL_BANDTYPE_0 = 5,
            RegionName = 6,
        };
        public enum StatType
        {
            TYPE_VALUE =0,
            TYPE_MIN = 1,
            TYPE_MAX = 2,
            TYPE_MEAN = 3,
            TYPE_SUM = 4,
            TYPE_COUNT = 5
        };
        public string name { get; set; }
        public DTParameter parameter { get; set; }
        public StatType statType { get; set; }
        public KeyType keyType { get; set; }
        public bool valueRangeCheck { get; set; } = false;
        public double minRange { get; set; }
        public double maxRange { get; set; }
        public int decPlace { get; set; } = 2;//默认小数点

        public List<ValueRange> retByRanges { get; set; } = new List<ValueRange>();

        public override string ToString()
        {
            string res = name;
            if (parameter != null)
            {
                res += "{" + parameter.Info.Name;
                if (parameter.ArrayIndex > 0)
                {
                    res += "[" + parameter.ArrayIndex + "]";
                }
                res += "{" + statType.ToString() + "}}";
            }
            else
            {
                res += "{" + keyType.ToString() + "}";
            }
            return res;
        }
        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["name"] = name;
                param["statType"] = (int)statType;
                param["keyType"] = (int)keyType;
                param["valueRangeCheck"] = valueRangeCheck;
                param["minRange"] = minRange;
                param["maxRange"] = maxRange;
                param["decPlace"] = decPlace;
                if(parameter!=null)
                {
                    param["param_name"] = parameter.Info.Name;
                    param["param_arg"] = parameter.ArrayIndex;
                }
                else
                {
                    param["param_name"] = "";
                    param["param_arg"] = -1;
                }
                List<object> rangesParams = new List<object>();
                param["retByRanges"] = rangesParams;
                foreach (ValueRange vr in retByRanges)
                {
                    rangesParams.Add(vr.Param);
                }
                return param;
            }
            set
            {
                name = (String)value["name"];
                statType = (StatType)(int)value["statType"];
                keyType = (KeyType)(int)value["keyType"];
                valueRangeCheck = (bool)value["valueRangeCheck"];
                minRange = (double)value["minRange"];
                maxRange = (double)value["maxRange"];
                if(value.ContainsKey("decPlace"))
                {
                    decPlace = (int)value["decPlace"];
                }
                string param_name = (String)value["param_name"];
                int param_arg = (int)value["keyType"];
                if(param_name != null &&param_name!="")
                {
                    if(param_arg!=-1)
                    {
                        parameter = DTParameterManager.GetInstance().GetParameter(param_name, param_arg);
                    }
                    else
                    {
                        parameter = DTParameterManager.GetInstance().GetParameter(param_name);
                    }
                }
                retByRanges.Clear();
                if(value.ContainsKey("retByRanges"))
                {
                    List<object> rangesParams = (List<object>)value["retByRanges"];
                    foreach (object o in rangesParams)
                    {
                        Dictionary<string, object> vrParam = (Dictionary<string, object>)o;
                        ValueRange vr = new ValueRange();
                        vr.Param = vrParam;
                        retByRanges.Add(vr);
                    }
                }
                
                
            }
        }
        internal bool CheckRangeOK(object retValue)
        {
            double dbvalue = 0;
            if (!double.TryParse(retValue.ToString(), out dbvalue))
            {
                return false;
            }
            return dbvalue >= minRange && dbvalue <= maxRange;
        }
        
    }
    public class ValueRange
    {
        public float minValue { get; set; }
        public float maxValue { get; set; }
        public override string ToString()
        {
            return "[" + minValue + "," + maxValue + ")";
        }
        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["minValue"] = minValue;
                param["maxValue"] = maxValue;
                return param;
            }
            set
            {
                minValue = (float)value["minValue"];
                maxValue = (float)value["maxValue"];
            }
        }

        public bool Contains(float a)
        {
            if (a >= minValue && a < maxValue)
            {
                return true;
            }
            return false;
        }
    };
    public class AnyResultKeyNode
    {
        public Dictionary<string, AnyResultKeyNode> anyResultNodes { get; set; } = new Dictionary<string, AnyResultKeyNode>();

        private readonly double?[] values = null;
        private readonly long[] counts = null;
        public AnyResultKeyNode(int valueCount)
        {
            values = new double?[valueCount];
            counts = new long[valueCount];
        }
        public object GetValueAt(int idx)
        {
            if (idx < values.Length)
                return values[idx];
            else
                return "";
        }

        internal void StatValue(object retValue, AnyColumnDef columnDef, int idx)
        {
            double dbvalue = 0;
            if (retValue is double)
            {
                dbvalue = (double)retValue;
            }
            else if (retValue is float)
            {
                float v = (float)retValue;
                dbvalue = v;
            }
            else if (retValue is int)
            {
                int v = (int)retValue;
                dbvalue = v;
            }
            else if (retValue is short)
            {
                short v = (short)retValue;
                dbvalue = v;
            }
            else if (retValue is byte)
            {
                byte v = (byte)retValue;
                dbvalue = v;
            }
            double? oldValue = values[idx];
            setValues(columnDef, idx, dbvalue, oldValue);
            counts[idx]++;
        }

        private void setValues(AnyColumnDef columnDef, int idx, double dbvalue, double? oldValue)
        {
            switch (columnDef.statType)
            {
                case AnyColumnDef.StatType.TYPE_COUNT:
                    setValidValue(idx, oldValue, dbvalue, 1, new GetValue(getCountValue));
                    break;
                case AnyColumnDef.StatType.TYPE_MAX:
                    setValidValue(idx, oldValue, dbvalue, dbvalue, new GetValue(getMaxValue));
                    break;
                case AnyColumnDef.StatType.TYPE_MIN:
                    setValidValue(idx, oldValue, dbvalue, dbvalue, new GetValue(getMinValue));
                    break;
                case AnyColumnDef.StatType.TYPE_MEAN:
                    setValidValue(idx, oldValue, dbvalue, dbvalue, new GetValue(getMeanValue));
                    break;
                case AnyColumnDef.StatType.TYPE_SUM:
                    setValidValue(idx, oldValue, dbvalue, dbvalue, new GetValue(getSumValue));
                    break;
                case AnyColumnDef.StatType.TYPE_VALUE:
                    values[idx] = dbvalue;
                    break;
                default:
                    break;
            }
        }

        delegate double GetValue(int idx, double dbvalue, double? oldValue);

        private double getCountValue(int idx, double dbvalue, double? oldValue)
        {
            return (double)oldValue + 1;
        }

        private double getMaxValue(int idx, double dbvalue, double? oldValue)
        {
            return Math.Max(dbvalue, (double)oldValue);
        }

        private double getMinValue(int idx, double dbvalue, double? oldValue)
        {
            return Math.Min(dbvalue, (double)oldValue);
        }

        private double getMeanValue(int idx, double dbvalue, double? oldValue)
        {
            return ((double)oldValue * counts[idx] + dbvalue) / (counts[idx] + 1);
        }

        private double getSumValue(int idx, double dbvalue, double? oldValue)
        {
            return Math.Min(dbvalue, (double)oldValue) + dbvalue;
        }

        private void setValidValue(int idx, double? oldValue, double value, double defaultValue, GetValue func)
        {
            if (oldValue.HasValue)
            {
                values[idx] = func(idx, value, oldValue);
            }
            else
            {
                values[idx] = defaultValue;
            }
        }
    };
}
