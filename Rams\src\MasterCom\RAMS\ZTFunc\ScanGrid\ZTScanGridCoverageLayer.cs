﻿using MapWinGIS;
using MasterCom.MControls;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.Text;
using System.Windows.Forms;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTScanGridCoverageLayer : LayerBase
    {
        public ZTScanGridCoverageLayer()
            : base("NBIOT扫频栅格重叠覆盖度图层")
        {

        }
        
        /// <summary>
        /// 本次选择的栅格
        /// </summary>
        public CoverageRegion SelectedGrid { get; set; }

        /// <summary>
        /// 需渲染的栅格数据合集
        /// </summary>
        public List<CoverageRegion> GridInfos
        {
            get;
            set;
        }
        /// <summary>
        /// 已选择栅格的边框画笔
        /// </summary>
        private readonly Pen penSelected = new Pen(Color.Red, 3);

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible || GridInfos == null || GridInfos.Count <= 0)
            {
                return;
            }
            DbRect dRect;
            GisAdapter.FromDisplay(updateRect, out dRect);

            //选中栅格包含的频点,PCI合集
            List<CoverageRegion> selectGridCellList = new List<CoverageRegion>();
            foreach (CoverageRegion grid in GridInfos)
            {
                drawGrid(grid, dRect, graphics);
                if (SelectedGrid != null && grid.MGRTIndex == SelectedGrid.MGRTIndex)
                {
                    selectGridCellList.Add(grid);
                }
            }
            drawSelGrid(dRect, graphics);
        }

        /// <summary>
        /// 绘制栅格
        /// </summary>
        /// <param name="grid"></param>
        /// <param name="dRect"></param>
        /// <param name="graphics"></param>
        private void drawGrid(CoverageRegion grid, DbRect dRect, Graphics graphics)
        {
            if (grid.Within(dRect))
            {
                Color color = GetColor(grid);
                if (color != Color.Empty)
                {
                    DbPoint ltPoint = new DbPoint(grid.TLLongitude, grid.TLLatitude);
                    PointF pointLt;
                    GisAdapter.ToDisplay(ltPoint, out pointLt);
                    DbPoint brPoint = new DbPoint(grid.BRLongitude, grid.BRLatitude);
                    PointF pointBr;
                    GisAdapter.ToDisplay(brPoint, out pointBr);
                    Brush brush = new SolidBrush(color);

                    graphics.FillRectangle(brush, pointLt.X, pointLt.Y, pointBr.X - pointLt.X, pointBr.Y - pointLt.Y);
                }
            }
        }

        /// <summary>
        /// 绘制选中的栅格
        /// </summary>
        /// <param name="graphics"></param>
        private void drawSelGrid(DbRect dRect, Graphics graphics)
        {
            if (SelectedGrid == null)
            {
                return;
            }
            //绘制选中栅格
            drawGrid(SelectedGrid, dRect, graphics);
            //绘制选中栅格边框
            DbPoint ltPoint = new DbPoint(SelectedGrid.TLLongitude, SelectedGrid.TLLatitude);
            PointF pointLt;
            GisAdapter.ToDisplay(ltPoint, out pointLt);
            DbPoint brPoint = new DbPoint(SelectedGrid.BRLongitude, SelectedGrid.BRLatitude);
            PointF pointBr;
            GisAdapter.ToDisplay(brPoint, out pointBr);
            graphics.DrawRectangle(penSelected, pointLt.X, pointLt.Y, pointBr.X - pointLt.X, pointBr.Y - pointLt.Y);
        }

        /// <summary>
        /// 获取栅格颜色
        /// </summary>
        /// <param name="grid"></param>
        /// <returns></returns>
        public Color GetColor(CoverageRegion grid)
        {
            Color? color = Ranges.GetColor(grid.OverlapCoverage);
            if (color == null)
            {
                return Color.Empty;
            }
            else
            {
                return (Color)color;
            }
        }

        public static NbIotMgrsCoverageColorRange Ranges { get; set; }

        public static bool IsActived { get; set; } = false;

        #region 导出图层
        internal int MakeShpFile_inject(string filename)
        {
            try
            {
                if (GridInfos == null || GridInfos.Count == 0)
                {
                    return 0;
                }
                Shapefile shpFile = new Shapefile();
                bool result = shpFile.CreateNewWithShapeID("", MapWinGIS.ShpfileType.SHP_POLYGON);
                if (!result)
                {
                    MessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                    return -1;
                }

                //列
                int idIdx = 0;
                int iGrid = idIdx++;
                int iGridSample = idIdx++;
                int iAvgRSRP = idIdx++;
                int iAvgSINR = idIdx++;
                int iGridSize = idIdx++;
                int iTLLongitude = idIdx++;
                int iTLLatitude = idIdx++;
                int iBRLongitude = idIdx++;
                int iBRLatitude = idIdx;
                ShapeHelper.InsertNewField(shpFile, "栅格号", FieldType.STRING_FIELD, 7, 0, ref iGrid);
                ShapeHelper.InsertNewField(shpFile, "栅格重叠覆盖度", FieldType.INTEGER_FIELD, 7, 0, ref iGridSample);
                ShapeHelper.InsertNewField(shpFile, "栅格平均RSRP", FieldType.DOUBLE_FIELD, 7, 0, ref iAvgRSRP);
                ShapeHelper.InsertNewField(shpFile, "栅格平均SINR", FieldType.DOUBLE_FIELD, 7, 0, ref iAvgSINR);
                ShapeHelper.InsertNewField(shpFile, "栅格经度", FieldType.INTEGER_FIELD, 7, 0, ref iGridSize);
                ShapeHelper.InsertNewField(shpFile, "栅格左上经度", FieldType.DOUBLE_FIELD, 7, 0, ref iTLLongitude);
                ShapeHelper.InsertNewField(shpFile, "栅格左上纬度", FieldType.DOUBLE_FIELD, 7, 0, ref iTLLatitude);
                ShapeHelper.InsertNewField(shpFile, "栅格右下经度", FieldType.DOUBLE_FIELD, 7, 0, ref iBRLongitude);
                ShapeHelper.InsertNewField(shpFile, "栅格右下纬度", FieldType.DOUBLE_FIELD, 7, 0, ref iBRLatitude);

                int shpIdx = 0;
                foreach (CoverageRegion info in GridInfos)
                {
                    shpFile.EditInsertShape(ShapeHelper.CreateRectShape(info.TLLongitude, info.TLLatitude, info.BRLongitude, info.BRLatitude), ref shpIdx);
                    shpFile.EditCellValue(iGrid, shpIdx, info.MGRTIndex);
                    shpFile.EditCellValue(iGridSample, shpIdx, info.OverlapCoverage);
                    shpFile.EditCellValue(iAvgRSRP, shpIdx, info.AvgRsrp);
                    shpFile.EditCellValue(iAvgSINR, shpIdx, info.AvgSinr);
                    shpFile.EditCellValue(iGridSize, shpIdx, info.GridSize);
                    shpFile.EditCellValue(iTLLongitude, shpIdx, info.TLLongitude);
                    shpFile.EditCellValue(iTLLatitude, shpIdx, info.TLLatitude);
                    shpFile.EditCellValue(iBRLongitude, shpIdx, info.BRLongitude);
                    shpFile.EditCellValue(iBRLatitude, shpIdx, info.BRLatitude);
                    shpIdx++;
                }
                shpFile.SaveAs(filename, null);
                shpFile.Close();
                return 1;
            }
            catch (Exception)
            {
                return -1;
            }
        }
        #endregion
    }

    public class NbIotMgrsCoverageColorRange
    {
        public float Minimum { get; set; }
        public float Maximum { get; set; }
        public Color InvalidColor { get; set; }
        public string InvalidDesc { get; set; }
        public List<ColorRange> ColorRanges { get; set; }

        public NbIotMgrsCoverageColorRange()
        {
            Minimum = -1000;
            Maximum = 1000;
            InvalidColor = Color.Black;
            InvalidDesc = "无效栅格";
            ColorRanges = new List<ColorRange>();

            XmlConfigFile configFile = new MyXmlConfigFile(NbIotMgrsBaseSettingManager.Instance.ConfigPath);
            if (configFile.Load())
            {
                loadConfig(configFile);
            }

            addInitData();
        }

        private void loadConfig(XmlConfigFile configFile)
        {
            XmlElement cfgCoverageColorRange = configFile.GetConfig("CoverageColorRange");
            if (cfgCoverageColorRange != null)
            {
                foreach (XmlNode node in cfgCoverageColorRange.ChildNodes)
                {
                    if (node.Attributes.Count == 0)
                    {
                        continue;
                    }
                    string des = node.Attributes["name"].InnerText;
                    string text = node.InnerText;
                    string[] s = text.Split(',');
                    float min, max;
                    int color;
                    if (s.Length == 3 && float.TryParse(s[0], out min) && float.TryParse(s[1], out max)
                        && int.TryParse(s[2], out color))
                    {
                        ColorRanges.Add(new ColorRange(min, max, Color.FromArgb(color), des));
                    }
                }
            }
        }

        private void addInitData()
        {
            if (ColorRanges.Count == 0)
            {
                ColorRanges.Add(new ColorRange(Minimum, 1, Color.Blue, "（-∞,1)"));
                ColorRanges.Add(new ColorRange(1, 2, Color.DarkSlateBlue, "[1,2)"));
                ColorRanges.Add(new ColorRange(2, 3, Color.DarkGreen, "[2,3)"));
                ColorRanges.Add(new ColorRange(3, 4, Color.MediumSeaGreen, "[3,4"));
                ColorRanges.Add(new ColorRange(4, 5, Color.Lime, "[4,5)"));
                ColorRanges.Add(new ColorRange(5, 6, Color.LawnGreen, "[5,6)"));
                ColorRanges.Add(new ColorRange(6, 7, Color.GreenYellow, "[6,7)"));
                ColorRanges.Add(new ColorRange(7, 8, Color.Yellow, "[7,8)"));
                ColorRanges.Add(new ColorRange(8, 9, Color.Orange, "[8,9)"));
                ColorRanges.Add(new ColorRange(9, 10, Color.DarkOrange, "[9,10)"));
                ColorRanges.Add(new ColorRange(10, Maximum, Color.Red, "[10,+∞)"));
            }
        }

        public virtual Color? GetColor(float value)
        {
            int idx = GetIndex(value);
            if (idx <= 0)
            {
                return InvalidColor;
            }
            else
            {
                if (ColorRanges[idx].visible)
                {
                    return ColorRanges[idx].color;
                }
                else
                {
                    return null;
                }
            }
        }

        public virtual int GetIndex(float value)
        {
            if (value < ColorRanges[0].maxValue)
            {
                return 0;
            }
            if (value >= ColorRanges[ColorRanges.Count - 1].minValue)
            {
                return ColorRanges.Count - 1;
            }
            for (int i = 1; i < ColorRanges.Count - 1; ++i)
            {
                if (value >= ColorRanges[i].minValue && value < ColorRanges[i].maxValue)
                {
                    return i;
                }
            }
            return -1;
        }

        public void SaveColorRange(string s)
        {
            XmlConfigFile configFile = new XmlConfigFile(NbIotMgrsBaseSettingManager.Instance.ConfigPath);
            XmlElement cfgColorRange = configFile.GetConfig(string.Format("{0}ColorRange", s));
            if (cfgColorRange != null)
            {
                cfgColorRange.RemoveAll();
                cfgColorRange.SetAttribute("name", string.Format("{0}ColorRange", s));
            }
            else
            {
                cfgColorRange = configFile.AddConfig(string.Format("{0}ColorRange", s));
            }

            foreach (ColorRange range in ColorRanges)
            {
                configFile.AddItem(cfgColorRange, range.desInfo,
                    string.Format("{0},{1},{2}", range.minValue, range.maxValue, range.color.ToArgb()));
            }
            configFile.Save(NbIotMgrsBaseSettingManager.Instance.ConfigPath);
        }
    }
}
