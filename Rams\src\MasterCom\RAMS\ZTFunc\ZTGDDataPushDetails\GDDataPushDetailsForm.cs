﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class GDDataPushDetailsForm : MinCloseForm
    {
        public GDDataPushDetailsForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
        }

        public void FillData(List<GDDataPushDetailsInfo> gdDataPushDetailsInfoList)
        {
            BindingSource source = new BindingSource();
            source.DataSource = gdDataPushDetailsInfoList;
            gridData.DataSource = source;
            gridData.RefreshDataSource();
        }

        private void outPutExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView1);
        }
    }
}
