﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.MTGis;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Grid;
using MapWinGIS;
using MasterCom.RAMS.Net;
using System.Xml;
using System.IO;

namespace MasterCom.Util
{
    public class GISManager : IConfigParams
    {
        public List<RoadRelevanceOption> RoadRelevanceOptions
        {
            get { return new List<RoadRelevanceOption>(districtRoadSettingDic.Values); }
        }
        private readonly Dictionary<int, RoadRelevanceOption> districtRoadSettingDic = new Dictionary<int, RoadRelevanceOption>();
        public RoadRelevanceOption GetRoadRelevanceOption(int districtID)
        {
            RoadRelevanceOption option = null;
            districtRoadSettingDic.TryGetValue(districtID, out option);
            return option;
        }

        private string getRoadMatrixFileName(int districtID)
        {
            return Application.StartupPath + "/userData/roadRelevance/roadNameMatrix_" + DistrictManager.GetInstance().getDistrictName(districtID) + ".xml";
        }

        public void Save(int districtID)
        {
            if (RoadNameMatrix.Count>0)
            {
                string fileName = getRoadMatrixFileName(districtID);
                XmlConfigFile xmlFile = new XmlConfigFile();
                XmlElement config = xmlFile.AddConfig("RoadNameMatrix");
                xmlFile.AddItem(config, "Matrix", RoadNameMatrix);
                xmlFile.Save(fileName);
            }
        }

        public bool Load(int districtID)
        {
            bool loaded = load(districtID);
            if (loaded)
            {
                GC.Collect();
                GC.WaitForPendingFinalizers();
            }
            return loaded && RoadNameMatrix.Count > 0;
        }

        private bool load(int districtID)
        {
            string fileName = getRoadMatrixFileName(districtID);
            if (File.Exists(fileName))
            {
                try
                {
                    XmlConfigFile file = new XmlConfigFile(fileName);
                    XmlElement config = file.GetConfig("RoadNameMatrix");
                    Dictionary<string, object> param = file.GetItemValue(config, "Matrix") as Dictionary<string, object>;
                    if (param == null)
                    {
                        return false;
                    }
                    RoadNameMatrix.Clear();
                    foreach (string str in param.Keys)
                    {
                        Dictionary<int, string> colNameDic = new Dictionary<int, string>();
                        Dictionary<string, object> colDic = param[str] as Dictionary<string, object>;
                        foreach (string col in colDic.Keys)
                        {
                            colNameDic.Add(int.Parse(col), string.Intern(colDic[col].ToString()));
                        }
                        RoadNameMatrix.Add(int.Parse(str), colNameDic);
                    }
                }
                catch
                {
                    return false;
                }
                return true;
            }
            else
            {
                return false;
            }
        }

        public void AddRoadSegmentName(double lng1, double lat1, double lng2, double lat2, string name)
        {//2点之间路段，从左往右计算(经度从小到大)
            double leftLng = lng1;
            double leftLat = lat1;
            double rightLng = lng2;
            double rightLat = lat2;
            if (lng1 > lng2)
            {
                leftLng = lng2;
                leftLat = lat2;
                rightLng = lng1;
                rightLat = lat1;
            }
            int lRowIdx, lColIdx, rRowIdx, rColIdx;//左右2点栅格矩阵对应的行列index
            getIndexInRoadMatrix(leftLng, leftLat, out lRowIdx, out lColIdx);
            getIndexInRoadMatrix(rightLng, rightLat, out rRowIdx, out rColIdx);
            Index idx = new Index(lRowIdx, lColIdx, rRowIdx, rColIdx);
            if (idx.LRowIdx == idx.RRowIdx && idx.LColIdx == idx.RColIdx)
            {//2点在同一个栅格
                addRoadName(idx.LRowIdx, idx.LColIdx, name);
            }
            else
            {//2点出现跨栅格情况
                bool isXMore1 = idx.RColIdx > idx.LColIdx;//x方向跨栅格
                bool isYMore1 = Math.Abs(idx.LRowIdx - idx.RRowIdx) > 0;//y方向跨栅格
                if (isXMore1)
                {//x方向跨栅格
                    deal(lng1, lat1, lng2, lat2, name, idx, isYMore1);
                }
                else
                {//仅y方向跨栅格
                    int minRow = idx.LRowIdx;
                    int maxRow = idx.RRowIdx;
                    if (idx.LRowIdx > idx.RRowIdx)
                    {
                        minRow = idx.RRowIdx;
                        maxRow = idx.LRowIdx;
                    }
                    for (int i = minRow; i <= maxRow; i++)
                    {
                        addRoadName(i, idx.LColIdx, name);
                    }
                }
            }
        }

        private void deal(double lng1, double lat1, double lng2, double lat2, string name, Index idx, bool isYMore1)
        {
            if (isYMore1)
            {//x,y方向皆跨栅格
                RoadLine line = new RoadLine(lng1, lat1, lng2, lat2);
                int c = idx.LColIdx;
                //从左端点所在行到右端点所在行，向右遍历
                if (idx.LRowIdx > idx.RRowIdx)
                {//左上->右下走向
                    for (int r = idx.LRowIdx; r >= idx.RRowIdx; r--)
                    {//上往下
                        scanFromLeft2Right(line, idx, r, ref c, name);
                    }
                }
                else
                {//左下->右上走向
                    for (int r = idx.LRowIdx; r <= idx.RRowIdx; r++)
                    {//上往下
                        scanFromLeft2Right(line, idx, r, ref c, name);
                    }
                }
            }
            else
            {//仅x方向跨栅格，保持rowIdx
                for (int c = idx.LColIdx; c <= idx.RColIdx; c++)
                {
                    addRoadName(idx.LRowIdx, c, name);
                }
            }
        }

        class Index
        {
            public Index(int lRowIdx, int lColIdx, int rRowIdx, int rColIdx)
            {
                LRowIdx = lRowIdx;
                LColIdx = lColIdx;
                RRowIdx = rRowIdx;
                RColIdx = rColIdx;
            }

            public int LRowIdx { get; set; }
            public int LColIdx { get; set; }
            public int RRowIdx { get; set; }
            public int RColIdx { get; set; }
        }

        /// <summary>
        /// 从左端点往右端点遍历
        /// </summary>
        private void scanFromLeft2Right(RoadLine line, Index idx, int curRowIdx, ref int curColIdx, string name)
        {
            /*由于线段是左右走向，下一行的相交列只可能出现在上一行相交列的同一列或右边列，
            *因此，从左往右遍历，每一行只要找到到最右边列（lastColIndex）后，即可跳转到下一行，
            *从lastColIndex开始进行遍历
            */
            while (curColIdx <= idx.RColIdx)
            {//左往右，下一行的相交列只可能出现在上一行相交列的同一列或右边列
                //即：相交列的index必会 大于等于 上一行相交列的index
                if (curRowIdx == idx.LRowIdx && curColIdx == idx.LColIdx || (curRowIdx == idx.RRowIdx && curColIdx == idx.RColIdx))
                {//左右端点，必相交
                    addRoadName(curRowIdx, curColIdx, name);
                }
                else if (line.IsIntersect((curColIdx) * 0.0004, (curRowIdx + 1) * 0.00036, 0.0004, 0.00036))
                {
                    addRoadName(curRowIdx, curColIdx, name);
                }
                else
                {//上一列为该行相交的最右边列，下一行从c-1位置开始往右遍历
                    break;
                }
                curColIdx++;
            }
            curColIdx--;
        }

        /// <summary>
        /// 斜截式直线方程 y=kx+b;
        /// </summary>
        private class RoadLine
        {
            private readonly double k;
            private readonly double b;
            public RoadLine(double x1, double y1, double x2, double y2)
            {
                if (x1 == x2)
                {
                    throw new InvalidProgramException("RoadLine类只支持斜截式直线方程，x1不能等于x2(不能为垂直x轴的线段)！");
                }
                k = (y1 - y2) / (x1 - x2);
                b = y1 - k * x1;
            }

            public bool IsIntersect(double ltLng, double ltLat, double width, double height)
            {//左->右，代入x，计算y，然后判断y是否在该矩形范围内
                double inLine = k * ltLng + b;
                if (inLine <= ltLat && inLine >= ltLat - height)
                {
                    return true;
                }
                inLine = k * (ltLng + width) + b;
                if (inLine <= ltLat && inLine >= ltLat - height)
                {
                    return true;
                }
                //上->下，代入y，计算x
                inLine = (ltLat - b) / k;
                if (ltLng <= inLine && inLine <= (ltLng + width))
                {
                    return true;
                }
                inLine = (ltLat - height - b) / k;
                return ltLng <= inLine && inLine <= (ltLng + width);
            }
        }

        private GISManager()
        {
            mainModel = MainModel.GetInstance();
            mainModel.DistrictChanged += ResetMap;
        }
        
        public Dictionary<int, Dictionary<int, string>> RoadNameMatrix { get; set; } = new Dictionary<int, Dictionary<int, string>>();
        private void addRoadName(int rowIdx, int colIdx, string name)
        {
            Dictionary<int, string> colDic = null;
            if (RoadNameMatrix.TryGetValue(rowIdx, out colDic))
            {
                string names;
                if (colDic.TryGetValue(colIdx, out names))
                {
                    if (names.IndexOf(name) == -1)
                    {
                        names += ";" + string.Intern(name);
                        colDic[colIdx] = names;
                    }
                }
                else
                {
                    names = name;
                    colDic.Add(colIdx, names);
                }
            }
            else
            {
                colDic = new Dictionary<int, string>();
                colDic.Add(colIdx, string.Intern(name));
                RoadNameMatrix.Add(rowIdx, colDic);
            }
        }

        public string GetRoadNamesByMatrix(List<double> lngs, List<double> lats)
        {
            string names = string.Empty;
            for (int i = 0; i < lngs.Count; i++)
            {
                double lng = lngs[i];
                double lat = lats[i];
                int row, col;
                getIndexInRoadMatrix(lng, lat, out row, out col);
                names = getRoadNamesByGridIdx(row, col);//取对应栅格的道路信息
                if (string.IsNullOrEmpty(names))
                {//对应栅格没有道路名称信息，以该栅格为中心，扩大一圈(3*3)获取道路名称
                    names = getNearRoads(names, row, col);
                }
            }
            return names;
        }

        private string getNearRoads(string names, int row, int col)
        {
            for (int r = 1; r >= -1; r--)
            {
                for (int c = -1; c <= 1; c++)
                {
                    if (r == 0 && c == 0)
                    {
                        continue;
                    }
                    names = getRoadNames(names, row, col, r, c);
                }
            }

            return names;
        }

        private string getRoadNames(string names, int row, int col, int r, int c)
        {
            StringBuilder sb = new StringBuilder(names);
            string nameSet = getRoadNamesByGridIdx(row + r, col + c);
            if (!string.IsNullOrEmpty(nameSet))
            {
                string[] nameArr = nameSet.Split(';');
                for (int len = 0; len < nameArr.Length; len++)
                {
                    string name = nameArr[len];
                    if (sb.Length == 0)
                    {
                        sb.Append(name);
                    }
                    else if (sb.ToString().IndexOf(name) == -1)
                    {
                        sb.Append(";" + name);
                    }
                }
            }

            return sb.ToString();
        }

        private string getRoadNamesByGridIdx(int rowIdx, int colIdx)
        {
            Dictionary<int, string> colDic = null;
            string name = null;
            if (RoadNameMatrix.TryGetValue(rowIdx, out colDic))
            {
                colDic.TryGetValue(colIdx, out name);
            }
            return name;
        }

        /// <summary>
        /// 根据经纬度，获取道路名称信息栅格行列位置
        /// </summary>
        /// <param name="longitude"></param>
        /// <param name="latitude"></param>
        /// <param name="rowIdx"></param>
        /// <param name="colIdx"></param>
        private void getIndexInRoadMatrix(double longitude, double latitude, out int rowIdx, out int colIdx)
        {
            rowIdx = (int)(latitude * 100000 / 36);
            colIdx = (int)(longitude * 10000 / 4);
        }


        readonly MainModel mainModel;
        private static GISManager instance = null;
        private static readonly object lockObj = new object();
        public static GISManager GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new GISManager();
                    }
                }
            }
            return instance;
        }
        public RoadRelevanceOption CurRoadRelevanceOption
        {
            get;
            set;
        }
        public void ResetMap(object sender, EventArgs e)
        {
            areaShapeFile = null;
            gridShapeFile = null;
            areaAgentShapeFile = null;
            streetTable = null;
            bInitStreetTable = false;
            InitStreetTableInfo();
            CurRoadRelevanceOption = GetRoadRelevanceOption(mainModel.DistrictID);
        }

        ResvShapeFile areaShapeFile = null;
        public ResvShapeFile AreaShapeFile
        {
            get
            {
                return getShapeFile(ref areaShapeFile, MapType.Area);
            }
        }

        /// <summary>
        /// 获取所在片区
        /// </summary>
        /// <param name="Longitude"></param>
        /// <param name="Latitude"></param>
        /// <returns></returns>
        public string GetAreaPlaceDesc(double Longitude, double Latitude)
        {
            if (AreaShapeFile == null)
            {
                return "";
            }
            return AreaShapeFile.GetRegionNameOfPoint(Longitude, Latitude);
        }

        ResvShapeFile gridShapeFile = null;
        public ResvShapeFile GridShapeFile
        {
            get
            {
                return getShapeFile(ref gridShapeFile, MapType.Grid);
            }
        }

        /// <summary>
        /// 获取所在网格
        /// </summary>
        /// <param name="Longitude"></param>
        /// <param name="Latitude"></param>
        /// <returns></returns>
        public string GetGridDesc(double Longitude, double Latitude)
        {
            if (GridShapeFile == null)
            {
                return "";
            }
            return GridShapeFile.GetRegionNameOfPoint(Longitude, Latitude);
        }

        ResvShapeFile areaAgentShapeFile = null;
        public ResvShapeFile AreaAgentShapeFile
        {
            get
            {
                return getShapeFile(ref areaAgentShapeFile, MapType.AreaAgent);
            }
        }

        private ResvShapeFile getShapeFile(ref ResvShapeFile shapeFile, MapType type)
        {
            if (shapeFile == null)
            {
                if (!mapsDic.ContainsKey(mainModel.DistrictID))
                {
                    return null;
                }
                if (!mapsDic[mainModel.DistrictID].ContainsKey(type))
                {
                    return null;
                }
                MapInfo mapInfo = mapsDic[mainModel.DistrictID][type];
                string mapPath = mapInfo.mapPath;
                if (string.IsNullOrEmpty(mapPath) || !File.Exists(mapPath))
                {
                    return null;
                }
                try
                {
                    shapeFile = getShapeFile(mapInfo, mapPath);
                }
                catch
                {
                    return null;
                }
            }
            return shapeFile;
        }

        private ResvShapeFile getShapeFile(MapInfo mapInfo, string mapPath)
        {
            Shapefile areaTable = new Shapefile();
            areaTable.Open(mapPath, null);
            int nmAreaFieldIdx = MapOperation.GetColumnFieldIndex(areaTable, mapInfo.columnName);
            ResvShapeFile shapeFile = new ResvShapeFile();
            for (int i = 0; i < areaTable.NumShapes; i++)
            {
                MapWinGIS.Shape geome = areaTable.get_Shape(i);
                if (geome.ShapeType == ShpfileType.SHP_POLYGON)
                {
                    string areaName = areaTable.get_CellValue(nmAreaFieldIdx, i) as string;
                    if (areaName != null && areaName.Trim() != "")
                    {
                        ResvRegion resvRegion = new ResvRegion();
                        resvRegion.RegionName = areaName;
                        resvRegion.Shape = geome;
                        shapeFile.AddRegion(resvRegion);
                    }
                }
            }
            return shapeFile;
        }

        /// <summary>
        /// 获取所在代维分区
        /// </summary>
        /// <param name="Longitude"></param>
        /// <param name="Latitude"></param>
        /// <returns></returns>
        public string GetAreaAgentDesc(double Longitude, double Latitude)
        {
            if (AreaAgentShapeFile == null)
            {
                return "";
            }
            return AreaAgentShapeFile.GetRegionNameOfPoint(Longitude, Latitude);
        }
        
        public string StreetTableName { get; set; } = "";
        public string StreetTableNameCol { get; set; } = "";
        public string StreetTableObjCol { get; set; } = "";
        public string StreetTablePath { get; set; } = "";

        bool bInitStreetTable = false;
        public void InitStreetTableInfo()
        {
            if (bInitStreetTable)
            {
                return;
            }
            int DistrictID = mainModel.DistrictID;
#if DEBUG
            Console.Write(DistrictID);
#endif
#if Shenzhen
            StreetTablePath = Application.StartupPath + "/GEOGRAPHIC/arterial_street_polyline.shp";
            StreetTableName = "arterial_street";
            StreetTableNameCol = "NAME";
            StreetTableObjCol = "Obj";
#elif SZLT
            StreetTablePath = Application.StartupPath + "/GEOGRAPHIC/深圳/道路街道_polyline.shp";
            StreetTableName = "道路街道";
            StreetTableNameCol = "PATHNAME";
            StreetTableObjCol = "Obj";
#elif Heilongjiang
            string districtName = DistrictManager.GetInstance().getDistrictName(DistrictID);
            StreetTablePath = Application.StartupPath + "/GEOGRAPHIC/" + districtName + "/干道_polyline.shp";
            StreetTableName = "干道";
            StreetTableNameCol = "NAME";
            StreetTableObjCol = "Obj";
#elif Dongguan
            StreetTablePath = Application.StartupPath + "/GEOGRAPHIC/东莞合并_polyline.shp";
            StreetTableName = "东莞合并";
            StreetTableNameCol = "NAME";
            StreetTableObjCol = "Obj";
#elif Xian
            StreetTablePath = Application.StartupPath + "/GEOGRAPHIC/西安市/Xa主要道路_polyline.shp";
            StreetTableName = "Xa主要道路";
            StreetTableNameCol = "名称";
            StreetTableObjCol = "Obj";
#elif Beijing
            StreetTablePath = Application.StartupPath + "/GEOGRAPHIC/公路_polyline.shp";
            StreetTableName = "公路";
            StreetTableNameCol = "NAME";
            StreetTableObjCol = "Obj";
#elif ShanxiJin
            string districtName = DistrictManager.GetInstance().getDistrictName(DistrictID);
            StreetTablePath = Application.StartupPath + "/GEOGRAPHIC/" + districtName + "/道路_polyline.shp";
            StreetTableName = "道路";
            StreetTableNameCol = "NAME";
            StreetTableObjCol = "Obj";
#elif Shanghai
            StreetTablePath = Application.StartupPath + "/GEOGRAPHIC/上海合并_polyline.shp";
            StreetTableName = "上海合并";
            StreetTableNameCol = "NAME";
            StreetTableObjCol = "Obj";
#elif NMG
            string districtName = DistrictManager.GetInstance().getDistrictName(DistrictID);
            StreetTablePath = Application.StartupPath + "/GEOGRAPHIC/" + districtName + "/道路测试渗透率图层_polyline.shp";
            StreetTableName = "道路测试渗透率图层";
            StreetTableNameCol = "NAME";
            StreetTableObjCol = "Obj";
#elif Ningxia
            string districtName = DistrictManager.GetInstance().getDistrictName(DistrictID);
            StreetTablePath = Application.StartupPath + "/GEOGRAPHIC/" + districtName + "/公路_polyline.shp";
            StreetTableName = "公路";
            StreetTableNameCol = "NAME";
            StreetTableObjCol = "Obj";
#elif Xinjiang
            string districtName = DistrictManager.GetInstance().getDistrictName(DistrictID);
            StreetTablePath = Application.StartupPath + "/GEOGRAPHIC/" + districtName + "/道路合并_polyline.shp";
            StreetTableName = "道路合并";
            StreetTableNameCol = "地物名称";
            StreetTableObjCol = "Obj";
#elif Wulumuqi
            string districtName = DistrictManager.GetInstance().getDistrictName(DistrictID);
            StreetTablePath = Application.StartupPath + "/GEOGRAPHIC/道路合并_polyline.shp";
            StreetTableName = "道路合并";
            StreetTableNameCol = "地物名称";
            StreetTableObjCol = "Obj";
#elif XJLT
            string districtName = DistrictManager.GetInstance().getDistrictName(DistrictID);
            StreetTablePath = Application.StartupPath + "/GEOGRAPHIC/" + districtName + "/道路合并_polyline.shp";
            StreetTableName = "道路合并";
            StreetTableNameCol = "地物名称";
            StreetTableObjCol = "Obj";
#elif Chongqing
            string districtName = DistrictManager.GetInstance().getDistrictName(DistrictID);
            StreetTablePath = Application.StartupPath + "/GEOGRAPHIC/道路合并_polyline.shp";
            StreetTableName = "道路合并";
            StreetTableNameCol = "NAME";
            StreetTableObjCol = "Obj";
#elif Chengdu
            string districtName = DistrictManager.GetInstance().getDistrictName(DistrictID);
            StreetTablePath = Application.StartupPath + "/GEOGRAPHIC/" + districtName + "/道路合并_polyline.shp";
            StreetTableName = "道路合并";
            StreetTableNameCol = "NAME";
            StreetTableObjCol = "Obj";
#elif Yunnan
            string districtName = DistrictManager.GetInstance().getDistrictName(DistrictID);
            StreetTablePath = Application.StartupPath + "/GEOGRAPHIC/" + districtName + "/道路合并_polyline.shp";
            StreetTableName = "道路合并";
            StreetTableNameCol = "NAME";
            StreetTableObjCol = "Obj";
#elif Shandong
            string districtName = DistrictManager.GetInstance().getDistrictName(DistrictID);
            StreetTablePath = Application.StartupPath + "/GEOGRAPHIC/" + districtName + "/道路_polyline.shp";
            StreetTableName = "道路";
            StreetTableNameCol = "NAME";
            StreetTableObjCol = "Obj";
#elif Guizhou
            string districtName = DistrictManager.GetInstance().getDistrictName(DistrictID);
            StreetTablePath = Application.StartupPath + "/GEOGRAPHIC/" + districtName + "/道路合并_polyline.shp";
            StreetTableName = "道路合并";
            StreetTableNameCol = "NAME";
            StreetTableObjCol = "Obj";
#elif Guangzhou
            string districtName = DistrictManager.GetInstance().getDistrictName(DistrictID);
            StreetTablePath = Application.StartupPath + "/GEOGRAPHIC/道路_polyline.shp";
            StreetTableName = "道路";
            StreetTableNameCol = "NAME";
            StreetTableObjCol = "Obj";
#elif Suzhou
            StreetTablePath = Application.StartupPath + "/GEOGRAPHIC/道路合并_polyline.shp";
            StreetTableName = "道路合并";
            StreetTableNameCol = "TEXTLABEL";
            StreetTableObjCol = "Obj";
#elif Guangdong
            string districtName = DistrictManager.GetInstance().getDistrictName(DistrictID);
            StreetTablePath = Application.StartupPath + "/GEOGRAPHIC/" + districtName + "/道路_polyline.shp";
            StreetTableName = "道路";
            StreetTableNameCol = "名称";
            StreetTableObjCol = "Obj";
#elif Hubei
            string districtName = DistrictManager.GetInstance().getDistrictName(DistrictID);
            StreetTablePath = Application.StartupPath + "/GEOGRAPHIC/" + districtName + "/道路合并_polyline.shp";
            StreetTableName = "道路合并";
            StreetTableNameCol = "NAME";
            StreetTableObjCol = "Obj";
#elif Shanxi
            string districtName = DistrictManager.GetInstance().getDistrictName(DistrictID);
            StreetTablePath = Application.StartupPath + "/GEOGRAPHIC/"+ districtName +"/道路_polyline.shp";
            StreetTableName = "道路";
            StreetTableNameCol = "NAME";
            StreetTableObjCol = "Obj";
#elif TianjinLT
            string districtName = DistrictManager.GetInstance().getDistrictName(DistrictID);
            StreetTablePath = Application.StartupPath + "/GEOGRAPHIC/" + districtName + "/基础道路_polyline.shp";
            StreetTableName = "道路";
            StreetTableNameCol = "NAME";//"PATHNAME";
            StreetTableObjCol = "Obj";
#else
            string districtName = DistrictManager.GetInstance().getDistrictName(DistrictID);
            StreetTablePath = Application.StartupPath + "/GEOGRAPHIC/" + districtName + "/道路_polyline.shp";
            StreetTableName = "道路";
            StreetTableNameCol = "NAME";
            StreetTableObjCol = "Obj";
#endif
            bInitStreetTable = true;
        }

        private int nmFieldIdx = 0;
        public int NmFieldIdx
        {
            get
            {
                return nmFieldIdx;
            }
        }
        private MapWinGIS.Shapefile streetTable = null;
        public MapWinGIS.Shapefile StreetTable
        {
            get
            {
                if (streetTable == null)
                {
                    if (!System.IO.File.Exists(StreetTablePath))
                    {
                        return null;
                    }
                    if (string.IsNullOrEmpty(StreetTablePath))
                    {
                        return null;
                    }
                    streetTable = new MapWinGIS.Shapefile();
                    if (!streetTable.Open(StreetTablePath, null))
                    {
                        XtraMessageBox.Show(streetTable.get_ErrorMsg(streetTable.LastErrorCode));
                    }
                    streetTable.FastMode = true;
                    nmFieldIdx = MapOperation.GetColumnFieldIndex(streetTable, StreetTableNameCol);
                    if (nmFieldIdx == -1)
                    {
                        return null;
                    }
                }
                return streetTable;
            }

        }

        /// <summary>
        /// 获取道路
        /// </summary>
        /// <param name="Longitude"></param>
        /// <param name="Latitude"></param>
        /// <returns></returns>
        public string GetRoadPlaceDesc(double longitude, double latitude)
        {
            List<double> longitudeLst = new List<double>();
            longitudeLst.Add(longitude);
            List<double> latitudeLst = new List<double>();
            latitudeLst.Add(latitude);
            if (CurRoadRelevanceOption != null && CurRoadRelevanceOption.RelevanceType == RoadRelevanceType.ByMatrix)
            {
                return GetRoadNamesByMatrix(longitudeLst, latitudeLst);
            }
            else
            {
                return getRoadPlaceDesc(StreetTable, nmFieldIdx, longitudeLst, latitudeLst, 1);
            }
        }


        /// <summary>
        /// 获取道路
        /// </summary>
        /// <param name="longitudeLst"></param>
        /// <param name="latitudeLst"></param>
        /// <returns></returns>
        public string GetRoadPlaceDesc(List<double> longitudeLst, List<double> latitudeLst)
        {
            if (CurRoadRelevanceOption != null 
                && CurRoadRelevanceOption.RelevanceType == RoadRelevanceType.ByMatrix)
            {
                return GetRoadNamesByMatrix(longitudeLst, latitudeLst);
            }
            else
            {
                return getRoadPlaceDesc(StreetTable, nmFieldIdx, longitudeLst, latitudeLst, 1);
            }
        }

        /// <summary>
        /// 获取道路名称
        /// </summary>
        /// <param name="longitudeLst"></param>
        /// <param name="latitudeLst"></param>
        /// <param name="findDepth"></param>
        /// <param name="roadCenterPointDic">道路对应的中心点</param>
        /// <returns></returns>
        public string GetRoadNames(List<double> longitudeLst, List<double> latitudeLst, int findDepth, out Dictionary<string, DbPoint> roadCenterPointDic)
        {
            roadCenterPointDic = new Dictionary<string, DbPoint>();
            List<string> uniqueRoadNameList = new List<string>();
            int curDepth = 0;
            int count = longitudeLst.Count;
            Dictionary<string, List<DbPoint>> roadPntsDic = new Dictionary<string, List<DbPoint>>();
            while (uniqueRoadNameList.Count == 0 && curDepth < findDepth)
            {//以（当前深度*0.0004）为半径的矩形循还获取
                double radius = (++curDepth) * 0.0004;
                for (int i = 0; i < count; i++)
                {
                    addUniqueRoadNameList(longitudeLst, latitudeLst, uniqueRoadNameList, curDepth, roadPntsDic, radius, i);
                }
            }
            StringBuilder sb = getUniqueRoadName(roadCenterPointDic, uniqueRoadNameList, curDepth, roadPntsDic);
            return sb.ToString();
        }

        private void addUniqueRoadNameList(List<double> longitudeLst, List<double> latitudeLst, List<string> uniqueRoadNameList, int curDepth, Dictionary<string, List<DbPoint>> roadPntsDic, double radius, int i)
        {
            double longi = longitudeLst[i];
            double lati = latitudeLst[i];
            foreach (string strTemp in MapFormOperHelper.GetIntersetArea(StreetTable, nmFieldIdx, longi, lati, radius))
            {
                string name = strTemp.Trim();
                if (string.IsNullOrEmpty(name))
                {
                    continue;
                }
                if (roadPntsDic.ContainsKey(name))
                {
                    if (curDepth == 1)
                    {//查找的深度大于1时，计算中心经纬度意义不大
                        roadPntsDic[name].Add(new DbPoint(longi, lati));
                    }
                }
                else
                {
                    uniqueRoadNameList.Add(name);
                    List<DbPoint> pnts = new List<DbPoint>();
                    pnts.Add(new DbPoint(longi, lati));
                    roadPntsDic.Add(name, pnts);
                }
            }
        }

        private StringBuilder getUniqueRoadName(Dictionary<string, DbPoint> roadCenterPointDic, 
            List<string> uniqueRoadNameList, int curDepth, Dictionary<string, List<DbPoint>> roadPntsDic)
        {
            StringBuilder sb = new StringBuilder();
            foreach (string str in uniqueRoadNameList)
            {
                sb.Append(str);
                sb.Append(";");
                double maxLng = double.MinValue;
                double minLng = double.MaxValue;
                double maxLat = double.MinValue;
                double minLat = double.MaxValue;
                foreach (DbPoint pnt in roadPntsDic[str])
                {
                    maxLng = Math.Max(pnt.x, maxLng);
                    minLng = Math.Min(pnt.x, minLng);
                    maxLat = Math.Max(pnt.y, maxLat);
                    minLat = Math.Min(pnt.y, minLat);
                }
                roadCenterPointDic.Add(str, new DbPoint(minLng + (maxLng - minLng) / 2, minLat + (maxLat - minLat) / 2));
            }
            if (sb.Length > 0)
            {
                sb.Remove(sb.Length - 1, 1);
            }
            if (curDepth > 1)
            {//遍历深度大于1
                sb.Append("（附近）");
            }

            return sb;
        }

        private string getRoadPlaceDesc(MapWinGIS.Shapefile table, int fldNmIdx, List<double> longitudeLst, List<double> latitudeLst, int depth)
        {
            if (table == null)
            {
                return string.Empty;
            }
            List<string> uniqueRoadNameList = new List<string>();
            int curDepth = 0;
            int count = longitudeLst.Count;
            while (uniqueRoadNameList.Count == 0 && curDepth < depth)
            {//以（当前深度*0.0004）为半径的矩形循还获取
                double radius = (++curDepth) * 0.0004;
                for (int i = 0; i < count; i++)
                {
                    double longi = longitudeLst[i];
                    double lati = latitudeLst[i];
                    foreach (string strTemp in MapFormOperHelper.GetIntersetArea(table, fldNmIdx, longi, lati, radius))
                    {
                        if (!string.IsNullOrEmpty(strTemp.Trim()) && !uniqueRoadNameList.Contains(strTemp))
                        {
                            uniqueRoadNameList.Add(strTemp.Trim());
                        }
                    }
                }
            }
            StringBuilder sb = getUniqueRoadName(uniqueRoadNameList, curDepth);
            return sb.ToString();
        }

        private StringBuilder getUniqueRoadName(List<string> uniqueRoadNameList, int curDepth)
        {
            StringBuilder sb = new StringBuilder();
            foreach (string str in uniqueRoadNameList)
            {
                sb.Append(str);
                sb.Append(";");
            }
            if (sb.Length > 0)
            {
                sb.Remove(sb.Length - 1, 1);
            }
            if (curDepth > 1)
            {//遍历深度大于1
                sb.Append("（附近）");
            }

            return sb;
        }

        public Dictionary<int, Dictionary<MapType, MapInfo>> mapsDic { get; set; } = new Dictionary<int, Dictionary<MapType, MapInfo>>();
        public Dictionary<string, object> ConfigParames
        {
            get
            {
                return getMapTypeInfoDic();
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                setMapTypeInfoDic(value);
            }
        }
     
        private Dictionary<string, object> getMapTypeInfoDic()
        {
            Dictionary<string, object> param = new Dictionary<string, object>();
            foreach (int districtID in mapsDic.Keys)
            {
                Dictionary<string, object> paramMap = new Dictionary<string, object>();
                param[districtID.ToString()] = paramMap;

                Dictionary<MapType, MapInfo> mapTypeInfoDic = mapsDic[districtID];
                foreach (MapType type in mapTypeInfoDic.Keys)
                {
                    paramMap[((int)type).ToString()] = mapTypeInfoDic[type].Param;
                }
            }
            //道路关联设置
            List<object> list = new List<object>();
            foreach (RoadRelevanceOption option in districtRoadSettingDic.Values)
            {
                list.Add(option.CfgParam);
            }
            param.Add("RoadRevanceOptions", list);
            return param;
        }

        private void setMapTypeInfoDic(Dictionary<string, object> param)
        {
            mapsDic.Clear();
            foreach (string key in param.Keys)
            {
                if (key.Equals("RoadRevanceOptions"))
                {
                    districtRoadSettingDic.Clear();
                    List<object> list = param["RoadRevanceOptions"] as List<object>;
                    foreach (object obj in list)
                    {
                        RoadRelevanceOption option = new RoadRelevanceOption(-1);
                        option.CfgParam = obj as Dictionary<string, object>;
                        districtRoadSettingDic.Add(option.DistrictID, option);
                    }
                    continue;
                }
                int districtID = int.Parse(key);
                Dictionary<MapType, MapInfo> mapTypeInfoDic = new Dictionary<MapType, MapInfo>();
                mapsDic[districtID] = mapTypeInfoDic;
                Dictionary<string, object> paramMap = param[key] as Dictionary<string, object>;
                foreach (string keyMap in paramMap.Keys)
                {
                    MapType type = (MapType)int.Parse(keyMap);
                    MapInfo info = new MapInfo();
                    info.Param = paramMap[keyMap] as Dictionary<string, object>;
                    mapTypeInfoDic[type] = info;
                }
            }
        }

        public string ConfigName
        {
            get { return "MapSetting"; }
        }

        public enum MapType
        {
            Road = 1,
            Area,
            AreaAgent,
            Grid
        }

        internal void ClearGridedRoadInfo()
        {
            foreach (Dictionary<int, string> dic in RoadNameMatrix.Values)
            {
                dic.Clear();
            }
            RoadNameMatrix.Clear();
        }

        internal void MakeRoadShapeGrid(MapWinGIS.Shape shape, string raodName)
        {
            int partCnt = shape.NumParts;
            for (int ps = 0; ps < partCnt; ps++)
            {
                int startIdx = shape.get_Part(ps);
                int endIdx = 0;
                if (ps == partCnt - 1)
                {
                    endIdx = shape.numPoints - 1;
                }
                else
                {
                    endIdx = shape.get_Part(ps + 1);
                }
                for (; startIdx < endIdx - 1; startIdx++)
                {
                    double x1 = 0, y1 = 0, x2 = 0, y2 = 0;
                    shape.get_XY(startIdx, ref x1, ref y1);
                    shape.get_XY(startIdx + 1, ref x2, ref y2);
                    AddRoadSegmentName(x1, y1, x2, y2, raodName);
                }
            }
        }

        internal void SetRoadRelevanceOption(RoadRelevanceOption option)
        {
            districtRoadSettingDic[option.DistrictID] = option;
        }
    }

    public class MapInfo
    {
        public string mapPath { get; set; } = "";
        public string mapName { get; set; } = "";
        public string columnName { get; set; } = "";

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["MapPath"] = mapPath;
                param["MapName"] = mapName;
                param["ColumnName"] = columnName;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("MapPath"))
                {
                    mapPath = param["MapPath"] == null ? "" : param["MapPath"].ToString();
                }
                if (param.ContainsKey("MapName"))
                {
                    mapName = param["MapName"] == null ? "" : param["MapName"].ToString();
                }
                if (param.ContainsKey("ColumnName"))
                {
                    columnName = param["ColumnName"] == null ? "" : param["ColumnName"].ToString();
                }
            }
        }
    }

    public enum RoadRelevanceType
    {
        /// <summary>
        /// 原始方式，以待关联坐标点为中心的80*80米矩形碰撞相交道路
        /// </summary>
        ByMapWinGis = 0,
        /// <summary>
        ///待关联坐标点对应既定的40*40米道路栅格，获取栅格对应的道路信息
        /// </summary>
        ByMatrix
    }

    /// <summary>
    /// 道路关联设置
    /// </summary>
    public class RoadRelevanceOption
    {
        public Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> dic = new Dictionary<string, object>();
                dic.Add("DistrictID", DistrictID);
                dic.Add("RoadRelevanceType", RelevanceType.ToString());
                if (RelevanceType == RoadRelevanceType.ByMatrix)
                {
                    dic.Add("IsAllRoadLayer", IsAllRoadLayer);
                    dic.Add("RoadLayerName", roadLayerFileName);
                    dic.Add("RoadNameFieldIdx", RoadLayerNameColIdx);
                }
                return dic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                DistrictID = int.Parse(value["DistrictID"].ToString());
                RelevanceType = (RoadRelevanceType)Enum.Parse(typeof(RoadRelevanceType), value["RoadRelevanceType"].ToString());
                if (RelevanceType == RoadRelevanceType.ByMatrix)
                {
                    IsAllRoadLayer = (bool)value["IsAllRoadLayer"];
                    roadLayerFileName = value["RoadLayerName"] as string;
                    RoadLayerNameColIdx = int.Parse(value["RoadNameFieldIdx"].ToString());
                }
            }
        }

        public RoadRelevanceOption(int districtID)
        {
            this.DistrictID = districtID;
        }
        public int DistrictID
        { get; set; }
        
        public bool IsAllRoadLayer { get; set; } = true;
        
        public RoadRelevanceType RelevanceType { get; set; } = RoadRelevanceType.ByMapWinGis;

        private string roadLayerFileName = null;
        /// <summary>
        /// 需要栅格化处理的道路图层文件，若为null则认为需要栅格化所有道路图层
        /// </summary>
        public string RoadLayerFileName
        {
            get { return roadLayerFileName; }
        }

        public void SetLayerFileName(string fileName)
        {
            roadLayerFileName = fileName;
            getLayerFields();
        }
        
        /// <summary>
        ///  需要栅格化处理的道路图层文件，对应的道路名称列索引
        /// </summary>
        public int RoadLayerNameColIdx { get; set; } = -1;

        private void getLayerFields()
        {
            layerFields = new List<string>();
            if (string.IsNullOrEmpty(roadLayerFileName))
            {
                RoadLayerNameColIdx = -1;
            }
            else
            {
                Shapefile sf = new Shapefile();
                if (sf.Open(roadLayerFileName, null))
                {
                    for (int i = 0; i < sf.NumFields; i++)
                    {
                        layerFields.Add(sf.get_Field(i).Name);
                    }
                    sf.Close();
                }
                else
                {
                    MessageBox.Show("打开图层：【" + roadLayerFileName + "】失败！" + Environment.NewLine + sf.get_ErrorMsg(sf.LastErrorCode));
                }
            }
        }

        private List<string> layerFields = null;
        public List<string> LayerFields
        {
            get
            {
                if (layerFields == null && !string.IsNullOrEmpty(roadLayerFileName))
                {
                    getLayerFields();
                }
                return layerFields;
            }
        }

    }

    /// <summary>
    /// 预存区域图层
    /// </summary>
    public class ResvShapeFile
    {
        public List<ResvRegion> regionList { get; set; } = new List<ResvRegion>();
        public void AddRegion(ResvRegion region)
        {
            regionList.Add(region);
        }

        public string GetRegionNameOfPoint(double x, double y)
        {
            foreach (ResvRegion region in regionList)
            {
                if (region.GeoOp.CheckPointInRegion(x, y))
                {
                    return region.RegionName;
                }
            }
            return "";
        }
    }

    public interface IRegionShape
    {
        bool JudgeRegionNotNull();
    }

    public class NullRegionShape : IRegionShape
    {
        public bool JudgeRegionNotNull()
        {
            return false;
        }
    }

    /// <summary>
    /// 预存区域
    /// </summary>
    public class ResvRegion : IRegionShape
    {
        public bool JudgeRegionNotNull()
        {
            return true;
        }
        public override string ToString()
        {
            return RegionName;
        }
        public ResvRegion()
        {
            id = Guid.NewGuid();
        }
        /// <summary>
        /// 该构造方法，只初始化图形的基本信息；
        /// 不包括MapOperation2
        /// </summary>
        /// <param name="shape"></param>
        /// <param name="name"></param>
        public ResvRegion(MapWinGIS.Shape shape, string name)
            : this()
        {
            this.shape = shape;
            this.RegionName = name;
        }
        private readonly Guid id;
        /// <summary>
        /// 区域名称可能有重复，这个ID就可以唯一标志
        /// </summary>
        public Guid ID
        {
            get { return id; }
        }
        public string RegionName { get; set; }//区域名称
        public string RootNodeName { get; set; }//根节点名称


        MTPolygon polygon = null;
        public MTPolygon GeoOp
        {
            get
            {
                return polygon;
            }
        }

        private MapWinGIS.Shape shape;
        public MapWinGIS.Shape Shape
        {
            get
            {
                return shape;
            }
            set
            {
                shape = value;
                polygon = new MTPolygon();
                polygon.Append(value);
            }
        }

        public double Area
        {
            get { return Math.Round(MTGis.RegionAreaCalculator.CalculateArea(shape), 2); }
        }
    }

    public class ResvStreetFile
    {
        readonly Dictionary<string, ResvStreet> streetDic = new Dictionary<string, ResvStreet>();

        public void AddStreet(string streetName, MapWinGIS.Shape shp)
        {
            if (!streetDic.ContainsKey(streetName))
            {
                ResvStreet resvStreet = new ResvStreet();
                resvStreet.streetName = streetName;
                streetDic[streetName] = resvStreet;
            }
            streetDic[streetName].AddStreet(shp);
        }

        public string GetStreetDesc(double x, double y)
        {
            List<double> longiList = new List<double>();
            longiList.Add(x);
            List<double> latiList = new List<double>();
            latiList.Add(y);
            return GetStreetDesc(longiList, latiList);
        }

        public string GetStreetDesc(List<double> longiList, List<double> latiList)
        {
            List<string> streetNames = new List<string>();
            for (int i = 0; i < longiList.Count; i++)
            {
                foreach (string streetName in streetDic.Keys)
                {
                    ResvStreet street = streetDic[streetName];
                    if (street.GeoOp.CheckPointInStreets(longiList[i], latiList[i]) && !streetNames.Contains(street.streetName))
                    {
                        streetNames.Add(street.streetName);
                    }
                }
            }

            StringBuilder sb = new StringBuilder();
            foreach (string str in streetNames)
            {
                sb.Append(str);
                sb.Append(";");
            }
            return sb.ToString();
        }
    }

    public class ResvStreet
    {
        public string streetName { get; set; }
        readonly List<MapWinGIS.Shape> shpList = new List<MapWinGIS.Shape>();
        public void AddStreet(MapWinGIS.Shape shp)
        {
            shpList.Add(shp);
        }

        private MapOperation2 geoOp = null;
        public MapOperation2 GeoOp
        {
            get
            {
                if (geoOp == null)
                {
                    geoOp = new MapOperation2();
                    try
                    {
                        geoOp.FillStreets(shpList);
                    }
                    catch
                    {
                        //continue
                    }
                }
                return geoOp;
            }
        }
    }

}
