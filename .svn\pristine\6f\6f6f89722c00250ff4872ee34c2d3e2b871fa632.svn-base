﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTRtpPacketsLostSetNRConditionForm : BaseDialog
    {
        public ZTRtpPacketsLostSetNRConditionForm()
        {
            InitializeComponent();
        }

        public void GetCondition(out ZTRtpPacketsLostSetCondition condition)
        {
            condition = new ZTRtpPacketsLostSetCondition();
            condition.RtpLossRate = (double)numSiteRate.Value;
            //condition.Radium = (int)numSiteRadium.Value;
            condition.TimeSpan = (int)numSiteTimeSpan.Value;
            condition.Rsrp = (double)numSiteRsrp.Value;
            condition.Sinr = (double)numSiteSinr.Value;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
