﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTKPIColumnPanel_PK : UserControl
    {
        public CQTKPIColumnPanel_PK()
        {
            InitializeComponent();
        }

        private CQTKPIReportColumn_PK column;
        public void SetColumn(CQTKPIReportColumn_PK column)
        {
            textEditColunmName.TextChanged -= textEditColunmName_EditValueChanged;
            seScoreRangeMin.EditValueChanged -= seScoreRangeMin_EditValueChanged;
            seScoreRangeMax.EditValueChanged -= seScoreRangeMax_EditValueChanged;
            txtFormula1.TextChanged -= txtFormula1_TextChanged;
            seKPIVlaueMin1.EditValueChanged -= seKPIVlaueMin1_EditValueChanged;
            seKPIValueMax1.EditValueChanged -= seKPIValueMax1_EditValueChanged;
            radioGroup1.SelectedIndexChanged -= radioGroup1_SelectedIndexChanged;
            txtFormula2.TextChanged -= txtFormula2_TextChanged;
            seKPIValueMin2.EditValueChanged -= seKPIValueMax2_EditValueChanged;
            seKPIValueMax2.EditValueChanged -= seKPIValueMax2_EditValueChanged;
            radioGroup2.SelectedIndexChanged -= radioGroup2_SelectedIndexChanged;
            
            this.column = column;
            if (column == null)
            {
                this.Enabled = false;
                return;
            }
            textEditColunmName.Text = column.Name;
            seScoreRangeMin.Value = (decimal)column.ScoreRangeMin;
            seScoreRangeMax.Value = (decimal)column.ScoreRangeMax;
            this.pkScoreColorRangeSettingPanel.SetScoreColorRanges(column.ScoreScheme_PK.PKScoreColorRanges,
                column.ScoreRangeMin - column.ScoreRangeMax, column.ScoreRangeMax * 2);

            txtFormula1.Text = column.Formula;
            seKPIVlaueMin1.Value = (decimal)column.KPIValueRangeMin;
            seKPIValueMax1.Value = (decimal)column.KPIValueRangeMax;
            radioGroup1.SelectedIndex = (int)column.ScoreScheme_PK.ScoreOrderType;
            this.scoreColorRangeSettingPanel1.SetRange(column.ScoreScheme_PK.ScoreColorRanges,
                column.KPIValueRangeMin, column.KPIValueRangeMax, column.ScoreRangeMin,
                column.ScoreRangeMax, column.ScoreScheme_PK.ScoreOrderType);

            txtFormula2.Text = column.Formula2;
            seKPIValueMin2.Value = (decimal)column.KPIValueRangeMin2;
            seKPIValueMax2.Value = (decimal)column.KPIValueRangeMax2;
            radioGroup2.SelectedIndex = (int)column.ScoreScheme_PK.ScoreOrderType2;
            this.scoreColorRangeSettingPanel2.SetRange(column.ScoreScheme_PK.ScoreColorRanges2,
           column.KPIValueRangeMin2, column.KPIValueRangeMax2, column.ScoreRangeMin,
           column.ScoreRangeMax, column.ScoreScheme_PK.ScoreOrderType2);

            textEditColunmName.TextChanged += textEditColunmName_EditValueChanged;
            seScoreRangeMin.EditValueChanged += seScoreRangeMin_EditValueChanged;
            seScoreRangeMax.EditValueChanged += seScoreRangeMax_EditValueChanged;
            txtFormula1.TextChanged += txtFormula1_TextChanged;
            seKPIVlaueMin1.EditValueChanged += seKPIVlaueMin1_EditValueChanged;
            seKPIValueMax1.EditValueChanged += seKPIValueMax1_EditValueChanged;
            radioGroup1.SelectedIndexChanged += radioGroup1_SelectedIndexChanged;
            txtFormula2.TextChanged += txtFormula2_TextChanged;
            seKPIValueMin2.EditValueChanged += seKPIValueMax2_EditValueChanged;
            seKPIValueMax2.EditValueChanged += seKPIValueMax2_EditValueChanged;
            radioGroup2.SelectedIndexChanged += radioGroup2_SelectedIndexChanged;
        }

        private void seScoreRangeMin_EditValueChanged(object sender, EventArgs e)
        {
            if (seScoreRangeMin.Value > seScoreRangeMax.Value)
            {
                seScoreRangeMin.Value = seScoreRangeMax.Value - 1;
            }
            column.ScoreRangeMin = (double)seScoreRangeMin.Value;
            this.pkScoreColorRangeSettingPanel.SetScoreColorRanges(column.ScoreScheme_PK.PKScoreColorRanges,
    column.ScoreRangeMin - column.ScoreRangeMax, column.ScoreRangeMax * 2);
        }

        private void seScoreRangeMax_EditValueChanged(object sender, EventArgs e)
        {
            if (seScoreRangeMin.Value > seScoreRangeMax.Value)
            {
                seScoreRangeMax.Value = seScoreRangeMin.Value + 1;
            }
            column.ScoreRangeMax = (double)seScoreRangeMax.Value;
            this.pkScoreColorRangeSettingPanel.SetScoreColorRanges(column.ScoreScheme_PK.PKScoreColorRanges,
column.ScoreRangeMin - column.ScoreRangeMax, column.ScoreRangeMax * 2);
        }

        private void textEditColunmName_EditValueChanged(object sender, EventArgs e)
        {
            if (column != null)
            {
                column.Name = textEditColunmName.Text;
            }
        }

        private void txtFormula1_TextChanged(object sender, EventArgs e)
        {
            if (column != null)
            {
                column.Formula = txtFormula1.Text;
            }
        }

        private void btnEditF1_Click(object sender, EventArgs e)
        {
            MasterCom.RAMS.Stat.ExpEditDlg expDlg = new MasterCom.RAMS.Stat.ExpEditDlg();
            expDlg.SelectSwitchParaTree(3);
            if (expDlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            txtFormula1.Text = expDlg.GetExpInput();
        }


        private void seKPIVlaueMin1_EditValueChanged(object sender, EventArgs e)
        {
            if (seKPIVlaueMin1.Value > seKPIValueMax1.Value)
            {
                seKPIVlaueMin1.Value = seKPIValueMax1.Value - 1;
            }
            column.KPIValueRangeMin = (float)seKPIVlaueMin1.Value;
            this.scoreColorRangeSettingPanel1.SetRange(column.ScoreScheme_PK.ScoreColorRanges,
             column.KPIValueRangeMin, column.KPIValueRangeMax, column.ScoreRangeMin,
             column.ScoreRangeMax, column.ScoreScheme_PK.ScoreOrderType);
        }

        private void seKPIValueMax1_EditValueChanged(object sender, EventArgs e)
        {
            if (seKPIVlaueMin1.Value > seKPIValueMax1.Value)
            {
                seKPIValueMax1.Value = seKPIVlaueMin1.Value + 1;
            }
            column.KPIValueRangeMax = (float)seKPIValueMax1.Value;
            this.scoreColorRangeSettingPanel1.SetRange(column.ScoreScheme_PK.ScoreColorRanges,
             column.KPIValueRangeMin, column.KPIValueRangeMax, column.ScoreRangeMin,
             column.ScoreRangeMax, column.ScoreScheme_PK.ScoreOrderType);
        }

        private void radioGroup1_SelectedIndexChanged(object sender, EventArgs e)
        {
            column.ScoreScheme_PK.ScoreOrderType = (ScoreOrderType)radioGroup1.SelectedIndex;
            this.scoreColorRangeSettingPanel1.SetRange(column.ScoreScheme_PK.ScoreColorRanges,
             column.KPIValueRangeMin, column.KPIValueRangeMax, column.ScoreRangeMin,
             column.ScoreRangeMax, column.ScoreScheme_PK.ScoreOrderType);
        }


        private void txtFormula2_TextChanged(object sender, EventArgs e)
        {
            if (column != null)
            {
                column.Formula2 = txtFormula2.Text;
            }
        }

        private void btnEidtF2_Click(object sender, EventArgs e)
        {
            MasterCom.RAMS.Stat.ExpEditDlg expDlg = new MasterCom.RAMS.Stat.ExpEditDlg();
            expDlg.SelectSwitchParaTree(3);
            if (expDlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            txtFormula2.Text = expDlg.GetExpInput();
        }

        private void seKPIValueMin2_EditValueChanged(object sender, EventArgs e)
        {
            if (seKPIValueMin2.Value > seKPIValueMax2.Value)
            {
                seKPIValueMin2.Value = seKPIValueMax2.Value - 1;
            }
            column.KPIValueRangeMin2 = (float)seKPIValueMin2.Value;
            this.scoreColorRangeSettingPanel2.SetRange(column.ScoreScheme_PK.ScoreColorRanges2,
    column.KPIValueRangeMin2, column.KPIValueRangeMax2, column.ScoreRangeMin,
    column.ScoreRangeMax, column.ScoreScheme_PK.ScoreOrderType2);
        }

        private void seKPIValueMax2_EditValueChanged(object sender, EventArgs e)
        {
            if (seKPIValueMin2.Value > seKPIValueMax2.Value)
            {
                seKPIValueMax2.Value = seKPIValueMin2.Value + 1;
            }
            column.KPIValueRangeMax2 = (float)seKPIValueMax2.Value;
            this.scoreColorRangeSettingPanel2.SetRange(column.ScoreScheme_PK.ScoreColorRanges2,
    column.KPIValueRangeMin2, column.KPIValueRangeMax2, column.ScoreRangeMin,
    column.ScoreRangeMax, column.ScoreScheme_PK.ScoreOrderType2);
        }

        private void radioGroup2_SelectedIndexChanged(object sender, EventArgs e)
        {
            column.ScoreScheme_PK.ScoreOrderType2 = (ScoreOrderType)radioGroup2.SelectedIndex;
            this.scoreColorRangeSettingPanel2.SetRange(column.ScoreScheme_PK.ScoreColorRanges2,
    column.KPIValueRangeMin2, column.KPIValueRangeMax2, column.ScoreRangeMin,
    column.ScoreRangeMax, column.ScoreScheme_PK.ScoreOrderType2);
        }

    }
}
