﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTSINR.WeakSINRReason
{
    public class ReasonHandOverUnTimely : ReasonBase
    {
        public ReasonHandOverUnTimely()
        {
            this.Name = "切换不及时";
        }

        public override bool IsValid(Model.TestPoint tp, params object[] resvParams)
        {
            if (ZTWeakSINRReason.isHandOverUnTimely)
            {
                return true;
            }
            return false;
        }
    }
}
