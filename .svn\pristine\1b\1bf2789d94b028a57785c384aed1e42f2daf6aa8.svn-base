using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.Model
{
    public class UpdateNote
    {
        public int timeValue { get; set; }
        public string desc { get; set; }
        public DateTime dateTime
        {
            get { return JavaDate.GetDateTimeFromMilliseconds(timeValue * 1000L); }
        }
        public String DateTimeString
        {
            get { return JavaDate.GetDateTimeFromMilliseconds(timeValue * 1000L).ToString("yyyy-MM-dd HH:mm"); }
        }
        internal void Fill(MasterCom.RAMS.Net.Content content)
        {
            content.GetParamInt();
            this.timeValue = content.GetParamInt();
            this.desc = content.GetParamString();
        }
    }
}
