﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using System.Windows.Forms;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Net
{
    public class ZTDIYQueryReselectionTooMuch_LTE : ZTDIYQueryHandoverTooMuch
    {        
        private static ZTDIYQueryReselectionTooMuch_LTE intance = null;
        public static Dictionary<string, Dictionary<string, MapOperation2>> mutRegionMopDic { get; set; }
        public new static ZTDIYQueryReselectionTooMuch_LTE GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTDIYQueryReselectionTooMuch_LTE();
                    }
                }
            }
            return intance;
        }

        protected ZTDIYQueryReselectionTooMuch_LTE()
            : base()
        {          
            MainModel.NeedType = true;
            ServiceTypes.Clear();
            carrierID = CarrierType.ChinaMobile;
        }

        public override string Name
        {
            get
            { return "选网过频繁_LTE";}
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
           
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22071, "分析");
        }

        private void InitRegionMop2()
        {
            Dictionary<string, List<ResvRegion>> resvRegionsDic = MainModel.SearchGeometrys.SelectedResvRegionDic;
            MapWinGIS.Shape gmt = MainModel.SearchGeometrys.Region;
            if (resvRegionsDic != null && resvRegionsDic.Count > 0)
            {
                foreach (string strGridType in resvRegionsDic.Keys)
                {
                    addRegionMap(resvRegionsDic, strGridType);
                }
            }
            else if (gmt != null)//单个区域
            {
                Dictionary<string, MapOperation2> regionMopDic =
                    new Dictionary<string, MapOperation2>();
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(gmt);
                regionMopDic.Add("当前区域", mapOp2);
                mutRegionMopDic.Add("无网格类型", regionMopDic);
            }
        }

        private void addRegionMap(Dictionary<string, List<ResvRegion>> resvRegionsDic, string strGridType)
        {
            if (!mutRegionMopDic.ContainsKey(strGridType))
            {
                Dictionary<string, MapOperation2> regionMop = new Dictionary<string, MapOperation2>();
                foreach (ResvRegion region in resvRegionsDic[strGridType])
                {
                    if (!regionMop.ContainsKey(region.RegionName))
                    {
                        MapOperation2 mapOp2 = new MapOperation2();
                        mapOp2.FillPolygon(region.Shape);
                        regionMop.Add(region.RegionName, mapOp2);
                    }
                }
                mutRegionMopDic.Add(strGridType, regionMop);
            }
        }

        /// <summary>
        /// 定位所在网格(点)
        /// </summary>
        public static string isContainPoint(double x, double y)
        {
            string gridTypeGrid = "";
            foreach (string gridType in mutRegionMopDic.Keys)
            {
                foreach (string grid in mutRegionMopDic[gridType].Keys)
                {
                    if (mutRegionMopDic[gridType][grid].CheckPointInRegion(x, y))
                    {
                        gridTypeGrid = gridType + "," + grid;
                        break;
                    }
                }
            }
            return gridTypeGrid;
        }

        ZTDIYQueryReselectionTooMuchSetForm conditionDlg = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (conditionDlg == null)
            {
                conditionDlg = new ZTDIYQueryReselectionTooMuchSetForm();
                conditionDlg.SetCondition(60, 500, 3);
            }
            if (conditionDlg.ShowDialog() == DialogResult.OK)
            {
                conditionDlg.GetCondition(ref timeLimit, ref distanceLimit, ref handoverCount);
                ZTDIYQueryScanAnalysis_LTE.ScanTestPointList = null;
                mutRegionMopDic = new Dictionary<string, Dictionary<string, MapOperation2>>();
                InitRegionMop2();
                ZTDIYQueryScanAnalysis_LTE.isLTEScan = false;
                return true;
            }
            return false;
        }

        protected override void initStatEventIDs()
        {            
            statEventIDs.Clear();
            statEventIDs.Add(1117);
            statEventIDs.Add(1120);
            statEventIDs.Add(1300);
            statEventIDs.Add(1302);
            statEventIDs.Add(1304);
            statEventIDs.Add(1306);
            statEventIDs.Add(1308);
            statEventIDs.Add(1310);
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                fileDataManager.Events = filterEvents(fileDataManager.Events);
                List<Event> curHandoverEvents = new List<Event>();
                HandoverFileDataManager curHandoverFile = ReselectionManager_LTE.GetHandoverTooMuchResult(fileDataManager,
                timeLimit, distanceLimit, handoverCount, curHandoverEvents);
                if (curHandoverFile.HandoverTimes > 0)
                {
                    curHandoverFile.Index = handoverFileList.Count + 1;
                    handoverFileList.Add(curHandoverFile);
                    handoverEvents.AddRange(curHandoverEvents);
                }

                if (!MainModel.IsBackground)
                {
                    fileDataManager.ClearDTDatas();
                    fileDataManager.ClearTestPoints();
                    dtFileDataBak.Add(fileDataManager);
                }
            }
        }

        protected override void fireShowForm()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(ZTDIYQueryReselectionTooMuchForm).FullName);
            ZTDIYQueryReselectionTooMuchForm networkSelectTooMuchForm = obj == null ? null : obj as ZTDIYQueryReselectionTooMuchForm;
            if (networkSelectTooMuchForm == null || networkSelectTooMuchForm.IsDisposed)
            {
                networkSelectTooMuchForm = new ZTDIYQueryReselectionTooMuchForm(MainModel);
            }
            networkSelectTooMuchForm.showAnalyseInfo(dtFileDataBak, handoverFileList, handoverEvents,
                timeLimit, distanceLimit, handoverCount);         
            if (!networkSelectTooMuchForm.Visible)
            {
                networkSelectTooMuchForm.Show(MainModel.MainForm);
            }
            networkSelectTooMuchForm.btnSampleLayer_Click(null, null);

            if (ZTDIYQueryScanAnalysis_LTE.ScanTestPointList.Count > 0)
            {
                TestPoint tp = ZTDIYQueryScanAnalysis_LTE.ScanTestPointList[0];
                FileInfo fileInfo = new FileInfo();
                fileInfo.Name = tp.FileName;
                fileInfo.ProjectID = tp.ProjectType;
                fileInfo.ID = tp.FileID;
                fileInfo.LogTable = tp.LogTable;
                fileInfo.ServiceType = tp.ServiceType;
                fileInfo.SampleTbName = tp.SampleTbName;
                QueryCondition condition = new QueryCondition();
                condition.FileInfos.Add(fileInfo);

                DateTime timeStart = tp.DateTime.AddMinutes(-3);
                DateTime timeEnd = tp.DateTime.AddMinutes(1);
                condition.Periods.Add(new TimePeriod(timeStart, timeEnd));
                try
                {
                    DIYQueryReplayFilePeriodScanLTE qb = new DIYQueryReplayFilePeriodScanLTE(MainModel);

                    qb.SetQueryCondition(condition);
                    qb.Query();

                    MainModel.MainForm.GetMapForm().GoToView(tp.Longitude, tp.Latitude);
                }
                catch
                {
                    //continue
                }
            }
            MainModel.DTDataManager.Clear();
            foreach (TestPoint tpp in ZTDIYQueryScanAnalysis_LTE.ScanTestPointList)
            {
                MainModel.DTDataManager.Add(tpp);
            }
            MainModel.FireDTDataChanged(this);
            foreach (MasterCom.RAMS.Func.MapSerialInfo serial in MainModel.MainForm.GetMapForm().GetDTLayer().SerialInfos)
            {
                //if (serial.Name.Contains("TopN_PSS_RSSI") || serial.Name.Contains("RSRP")
                //    || serial.Name.Contains("RSCP") || serial.Name.Contains("RxLev"))
                {
                    serial.Visible = true;
                }
            }
            MainModel.DrawFlyLines = true;
            MainModel.MainForm.GetMapForm().GetDTLayer().Invalidate();
        }
    }

    public class ZTDIYQueryReselectionTooMuch_LTE_FDD : ZTDIYQueryReselectionTooMuch_LTE
    {
        private static ZTDIYQueryReselectionTooMuch_LTE_FDD instance = null;
        public static new ZTDIYQueryReselectionTooMuch_LTE_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTDIYQueryReselectionTooMuch_LTE_FDD();
                    }
                }
            }
            return instance;
        }

        protected ZTDIYQueryReselectionTooMuch_LTE_FDD()
            : base()
        {
            MainModel.NeedType = true;
            ServiceTypes.Clear();
            carrierID = CarrierType.ChinaUnicom;
        }

        public override string Name
        {
            get
            { return "选网过频繁_LTE_FDD"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {

            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26047, "分析");
        }
        protected override void initStatEventIDs()
        {
            statEventIDs.Clear();
            statEventIDs.Add(3120);
            statEventIDs.Add(3300);
            statEventIDs.Add(3304);
            statEventIDs.Add(3306);
            statEventIDs.Add(3308);
            statEventIDs.Add(3310);
        }
    }
}
