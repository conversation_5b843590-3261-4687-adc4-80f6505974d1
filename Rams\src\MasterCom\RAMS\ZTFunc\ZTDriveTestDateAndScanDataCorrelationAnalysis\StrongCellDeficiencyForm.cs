﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class StrongCellDeficiencyForm : MinCloseForm
    {
        public StrongCellDeficiencyForm()
            :base()
        {
            InitializeComponent();

            init();
        }

        private void init()
        {
            olvColumnMaincellName.AspectGetter = delegate(object o)
            {
                if (o is Item_Maincell)
                {
                    Item_Maincell maincell = o as Item_Maincell;
                    return maincell.maincellName;
                }
                return null;
            };

            olvColumnMaincellLacCi.AspectGetter = delegate(object o)
            {
                if (o is Item_Maincell)
                {
                    Item_Maincell maincell = o as Item_Maincell;
                    return maincell.lacCi;
                }
                return null;
            };

            olvColumnMisscellName.AspectGetter = delegate(object o)
            {
                if (o is Childitem_MissStrongCell)
                {
                    Childitem_MissStrongCell mscell = o as Childitem_MissStrongCell;
                    return mscell.cellName;
                }
                return null;
            };

            olvColumnMisscellLacCi.AspectGetter = delegate(object o)
            {
                if (o is Childitem_MissStrongCell)
                {
                    Childitem_MissStrongCell mscell = o as Childitem_MissStrongCell;
                    return mscell.lacCi;
                }
                return null;
            };
            
            olvColumnAllSampleCount.AspectGetter = delegate(object o)
            {
                if (o is Item_Maincell)
                {
                    Item_Maincell maincell = o as Item_Maincell;
                    return maincell.allSampleCount;
                }
                return null;
            };

            olvColumnAbsampleCount.AspectGetter = delegate(object o)
            {
                if (o is Item_Maincell)
                {
                    Item_Maincell maincell = o as Item_Maincell;
                    return maincell.abnormalSampleCount;
                }
                return null;
            };

            olvColumnAbsamplePct.AspectGetter = delegate(object o)
            {
                if (o is Item_Maincell)
                {
                    Item_Maincell maincell = o as Item_Maincell;
                    return maincell.GetAbSamplePct();
                }
                return null;
            };

            treeListView.CanExpandGetter = delegate(object x)
            {
                return x is Item_Maincell;
            };
            treeListView.ChildrenGetter = delegate(object o)
            {
                return ((Item_Maincell)o).missStrongCellList;
            };
        }

        public void FillData()
        {
            treeListView.ClearObjects();
            treeListView.SetObjects(MainModel.Item_MaincellList);
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            List<NPOIRow> rowList = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.AddCellValue("主服小区名称");
            row.AddCellValue("主服小区LAC_CI");
            row.AddCellValue("总采样点数量");
            row.AddCellValue("异常采样点数量");
            row.AddCellValue("异常采样点比例");
            row.AddCellValue("缺失小区名称");
            row.AddCellValue("缺失小区LAC_CI");
            rowList.Add(row);

            foreach (Item_Maincell mc in treeListView.Roots)
            {
                row = new NPOIRow();
                row.AddCellValue(mc.maincellName);
                row.AddCellValue(mc.lacCi);
                row.AddCellValue(mc.allSampleCount);
                row.AddCellValue(mc.abnormalSampleCount);
                row.AddCellValue(mc.GetAbSamplePct());

                foreach (Childitem_MissStrongCell msCell in mc.missStrongCellList)
                {
                    NPOIRow subRow = new NPOIRow();
                    subRow.AddCellValue(msCell.cellName);
                    subRow.AddCellValue(msCell.lacCi);
                    row.AddSubRow(subRow);
                }
                rowList.Add(row);
            }
            ExcelNPOIManager.ExportToExcel(rowList);
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            this.treeListView.ExpandAll();
        }

        private void miCollapseAll_Click(object sender, EventArgs e)
        {
            this.treeListView.CollapseAll();
        }

        private void treeListView_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            ListViewItem lvi = treeListView.SelectedItem;
            if (lvi == null)
            {
                return;
            }
            Cell mainCell = null;
            Cell missStrongCell = null;
            if (lvi.SubItems[0].Text != null)
            {
                getSelectedMainCell(lvi.SubItems[0].Text, out mainCell, out missStrongCell);
            }
            if (lvi.SubItems[2].Text != "")
            {
                missStrongCell = getSelectedMissStrongCell(lvi.SubItems[2].Text);
            }
            if (mainCell != null)
            {
                MainModel.SelectedCells.Clear();
                MainModel.SelectedCells.Add(mainCell);
                MainModel.SelectedCells.Add(missStrongCell);
                MainModel.SelectedCell = mainCell;
                MainModel.MainForm.GetMapForm().GoToView(mainCell.Longitude, mainCell.Latitude);
                MainModel.MainForm.GetMapForm().GetCellLayer().Invalidate();
            }
            else if(missStrongCell!=null)
            {
                MainModel.SelectedCells.Clear();
                MainModel.SelectedCell = missStrongCell;
                MainModel.MainForm.GetMapForm().GoToView(missStrongCell.Longitude, missStrongCell.Latitude);
                MainModel.MainForm.GetMapForm().GetCellLayer().Invalidate();
            }
        }

        /// <summary>
        /// 按选择项的小区名，给主服务小区和缺失的强信号小区赋值
        /// </summary>
        /// <param name="cellName"></param>
        /// <param name="mainCell"></param>
        /// <param name="missStrongCell"></param>
        private void getSelectedMainCell(string cellName,out Cell mainCell,out Cell missStrongCell)
        {
            bool gotcell = false;
            Cell mc = null;
            Cell msc = null;
            foreach (Item_Maincell imc in MainModel.Item_MaincellList)
            {
                if (imc.maincellName == cellName)
                {
                    mc = imc.cell;
                    gotcell = true;
                    {
                        foreach (Childitem_MissStrongCell cm in imc.missStrongCellList)
                        {
                            msc = cm.cell;
                        }
                    }
                }
            }
            if (gotcell)
            {
                mainCell = mc;
                missStrongCell = msc;
            }
            else
            {
                mainCell = null;
                missStrongCell = null;
            }
        }

        private Cell getSelectedMissStrongCell(string cellName)
        {
            foreach (Item_Maincell imc in MainModel.Item_MaincellList)
            {
                foreach (Childitem_MissStrongCell cm in imc.missStrongCellList)
                {
                    if (cm.cellName == cellName)
                    {
                        return cm.cell;
                    }
                }
            }
            return null;
        }

        private void StrongCellDeficiencyForm_FormClosed(object sender, FormClosedEventArgs e)
        {
            //释放内存---------------
            MainModel.Item_MaincellList = null;
            //-----------------------
        }
    }
}
