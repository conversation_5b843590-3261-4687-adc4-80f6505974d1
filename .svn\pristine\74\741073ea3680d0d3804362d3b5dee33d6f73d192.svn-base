﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ScanWeakSinrSampleRoadForm : MinCloseForm
    {
        public ScanWeakSinrSampleRoadForm()
        {
            InitializeComponent();
            this.gridView1.DoubleClick += gv_DoubleClick;
            this.miExpandAll.Click += MiExpandAll_Click;
            this.miCollapseAll.Click += MiCollapseAll_Click;
        }

        public void FillData(List<ScanWeakSinrSampleRoad> list)
        {
            gridControl.DataSource = list;
            gridControl.RefreshDataSource();
            gv.BestFitColumns();
            MainModel.ClearDTData();
            foreach (WeakSINRRoad road in list)
            {
                foreach (TestPoint tp in road.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            MainModel.FireSetDefaultMapSerialTheme("LTE_SCAN", "TopN_CELL_Specific_RSRP");
            MainModel.FireDTDataChanged(this);
        }

        private void gv_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView curGv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            object row = curGv.GetRow(curGv.GetSelectedRows()[0]);
            if (row is ScanWeakSinrSampleRoad)
            {
                ScanWeakSinrSampleRoad road = row as ScanWeakSinrSampleRoad;
                if (road != null)
                {
                    MainModel.DTDataManager.Clear();
                    foreach (TestPoint tp in road.TestPoints)
                    {
                        MainModel.DTDataManager.Add(tp);
                    }
                    MainModel.FireDTDataChanged(this);
                    TestPoint midTp = road.TestPoints[road.TestPoints.Count / 2];
                    MainModel.MainForm.GetMapForm().GoToView(midTp.Longitude, midTp.Latitude, 6000);
                }
            }
            else if (row is ScanWeakSinrSampleInfo)
            {
                ScanWeakSinrSampleInfo sample = row as ScanWeakSinrSampleInfo;
                if (sample != null)
                {
                    MainModel.SelectedTestPoints.Clear();
                    MainModel.SelectedTestPoints.Add(sample.TestPoint);
                    MainModel.FireSelectedTestPointsChanged(this);
                }
            }
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.AddCellValue("序号");
            row.AddCellValue("道路");
            row.AddCellValue("持续距离（米）");
            row.AddCellValue("持续时间（秒）");
            row.AddCellValue("采样点个数");
            row.AddCellValue("最大SINR");
            row.AddCellValue("最小SINR");
            row.AddCellValue("平均SINR");
            row.AddCellValue("最大RSRP");
            row.AddCellValue("最小RSRP");
            row.AddCellValue("平均RSRP");
            row.AddCellValue("中心经度");
            row.AddCellValue("中心纬度");
            row.AddCellValue("文件名");

            row.AddCellValue("Time");
            row.AddCellValue("Longitude");
            row.AddCellValue("Latitude");
            row.AddCellValue("SCell ID");
            row.AddCellValue("SCell Name");
            row.AddCellValue("TAC");
            row.AddCellValue("ECI");
            row.AddCellValue("EARFCN");
            row.AddCellValue("PCI");
            row.AddCellValue("RSRP");
            row.AddCellValue("RSRQ");
            row.AddCellValue("SINR");
            rows.Add(row);

            List<ScanWeakSinrSampleRoad> roadList = gridControl.DataSource as List<ScanWeakSinrSampleRoad>;
            foreach (ScanWeakSinrSampleRoad road in roadList)
            {
                row = new NPOIRow();
                rows.Add(row);
                row.AddCellValue(road.SN);
                row.AddCellValue(road.RoadName);
                row.AddCellValue(road.Distance);
                row.AddCellValue(road.Second);
                row.AddCellValue(road.TestPointCount);
                row.AddCellValue(road.MaxSINR);
                row.AddCellValue(road.MinSINR);
                row.AddCellValue(road.AvgSINR);
                row.AddCellValue(road.MaxRSRP);
                row.AddCellValue(road.MinRSRP);
                row.AddCellValue(road.AvgRSRP);
                row.AddCellValue(road.MidLng);
                row.AddCellValue(road.MidLat);
                row.AddCellValue(road.FileName);

                foreach (ScanWeakSinrSampleInfo sample in road.SampleInfos)
                {
                    NPOIRow subRow = new NPOIRow();
                    row.AddSubRow(subRow);
                    subRow.AddCellValue(sample.TimeString);
                    subRow.AddCellValue(sample.Longitude);
                    subRow.AddCellValue(sample.Latitude);
                    subRow.AddCellValue(sample.CellID);
                    subRow.AddCellValue(sample.CellName);
                    subRow.AddCellValue(sample.Earfcn);
                    subRow.AddCellValue(sample.Pci);
                    subRow.AddCellValue(sample.Rsrp);
                    subRow.AddCellValue(sample.Rsrq);
                    subRow.AddCellValue(sample.Sinr);
                }
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }

        private void MiExpandAll_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < gv.RowCount; ++i)
            {
                gv.ExpandMasterRow(i);
            }
        }

        private void MiCollapseAll_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < gv.RowCount; ++i)
            {
                gv.CollapseMasterRow(i);
            }
        }
    }
}
