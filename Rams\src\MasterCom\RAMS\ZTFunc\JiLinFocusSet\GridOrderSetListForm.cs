﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using BrightIdeasSoftware;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.JiLinFocusSet
{
    public partial class GridOrderSetListForm : MinCloseForm
    {
        public GridOrderSetListForm()
            : base()
        {
            InitializeComponent();
            init();
        }

        private void init()
        {
            lv.MouseDoubleClick += lv_MouseDoubleClick;
            lv.CanExpandGetter += delegate(object row)
            {
                return row is GridOrder;
            };
            lv.ChildrenGetter += delegate(object row)
            {
                if (row is GridOrder)
                {
                    GridOrder item = row as GridOrder;
                    List<object> objs = new List<object>();
                    foreach (OrderGridItem evtItem in item.Grids)
                    {
                        objs.Add(evtItem);
                    }
                    return objs;
                }
                return null;
            };

            colDistrictName.AspectGetter += delegate(object row)
            {
                if (row is GridOrder)
                {
                    GridOrder item = row as GridOrder;
                    return item.DistrictName;
                }
                return null;
            };

            colSN.AspectGetter += delegate(object row)
            {
                if (row is GridOrder)
                {
                    GridOrder item = row as GridOrder;
                    return item.OrderID;
                }
                return null;
            };

            this.colOrderTypeName.AspectGetter += delegate(object row)
            {

                if (row is GridOrder)
                {
                    GridOrder item = row as GridOrder;
                    return item.SetOrderType;
                }
                return null;
            };

            colRoadNames.AspectGetter += delegate(object row)
            {
                if (row is GridOrder)
                {
                    GridOrder item = row as GridOrder;
                    return item.RoadNames;
                }
                return null;
            };

            colTAC.AspectGetter += delegate(object row)
            {
                if (row is OrderGridItem)
                {
                    OrderGridItem item = row as OrderGridItem;
                    return item.TAC;
                }
                return null;
            };

            colECI.AspectGetter += delegate(object row)
            {
                if (row is OrderGridItem)
                {
                    OrderGridItem item = row as OrderGridItem;
                    return item.ECI;
                }
                return null;
            };

            colLng.AspectGetter += delegate(object row)
            {
               if (row is OrderGridItem)
                {
                    OrderGridItem item = row as OrderGridItem;
                    return item.LTLng;
                }
                return null;
            };

            colAreaNames.AspectGetter += delegate(object row)
            {
                if (row is GridOrder)
                {
                    GridOrder item = row as GridOrder;
                    return item.AreaNames;
                }
                return null;
            };


            colItemCount.AspectGetter += delegate(object row)
            {
                if (row is GridOrder)
                {
                    GridOrder item = row as GridOrder;
                    return item.GridCount;
                }
                return null;
            };

            colLat.AspectGetter += delegate(object row)
            {
                if (row is OrderGridItem)
                {
                    OrderGridItem item = row as OrderGridItem;
                    return item.LTLat;
                }
                return null;
            };

            colStatusDesc.AspectGetter += delegate(object row)
            {
                if (row is GridOrder)
                {
                    GridOrder item = row as GridOrder;
                    return item.StatusDesc;
                }
                return null;
            };
        }

        void lv_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            OlvListViewHitTestInfo info = lv.OlvHitTest(e.X, e.Y);
            GridOrder order = null;
            OrderGridItem grid = null;
            if (info.RowObject is GridOrder)
            {
                order = info.RowObject as GridOrder;
            }
            else if (info.RowObject is OrderGridItem)
            {
                grid = info.RowObject as OrderGridItem;
                order = grid.Order;
            }
            makeSureLayerVisible();
            List<GridOrder> orders = new List<GridOrder>();
            if (order != null)
            {
                orders.Add(order);
                double minLng = double.MaxValue;
                double maxLng = double.MinValue;
                double minLat = double.MaxValue;
                double maxLat = double.MinValue;
                foreach (OrderGridItem g in order.Grids)
                {
                    minLng = Math.Min(minLng, g.CenterLng);
                    maxLng = Math.Max(maxLng, g.CenterLng);
                    minLat = Math.Min(minLat, g.CenterLat);
                    maxLat = Math.Max(maxLat, g.CenterLat);
                }
            }
            fillLayer(orders, grid);
        }

        private GridOrderLayer layer;
        public List<GridOrder> Orders { get; set; } = new List<GridOrder>();
        internal void FillData(List<GridOrder> list, DateTime dtFrom, DateTime dtTo,string orderID)
        {
            Orders = list;
            makeSureLayerVisible();
            lv.ClearObjects();
            lv.SetObjects(list);
            lv.ExpandAll();
            this.txtOrderID.Text = orderID;
            this.dtFrom.Value = dtFrom;
            this.dtTo.Value = dtTo;
            fillLayer(list, null);
        }

        public string orderIDLower
        {
            get { return txtOrderID.Text.Trim().ToLower(); }
        }

        private void fillLayer(List<GridOrder> orders, OrderGridItem grid)
        {
            makeSureLayerVisible();
            layer.Orders = orders;
            layer.Grid = grid;
            double minLng = double.MaxValue;
            double maxLng = double.MinValue;
            double minLat = double.MaxValue;
            double maxLat = double.MinValue;
            foreach (GridOrder order in orders)
            {
                foreach (OrderGridItem item in order.Grids)
                {
                    minLng = Math.Min(minLng, item.CenterLng);
                    maxLng = Math.Max(maxLng, item.CenterLng);
                    minLat = Math.Min(minLat, item.CenterLat);
                    maxLat = Math.Max(maxLat, item.CenterLat);
                }
            }
            MasterCom.MTGis.DbRect rect = new MTGis.DbRect(minLng - 0.002, minLat - 0.002, maxLng + 0.002, maxLat + 0.002);
            MainModel.MainForm.GetMapForm().GoToView(rect);
        }

        private void makeSureLayerVisible()
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            layer = mf.GetLayerBase(typeof(GridOrderLayer)) as GridOrderLayer;
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(lv);
        }

        private DateTime dateFrom
        {
            get
            {
                return dtFrom.Value.Date;
            }
        }

        public DateTime dateTo
        {
            get { return dtTo.Value.Date.AddDays(1).AddMilliseconds(-1); }
        }

        private void btnFilter_Click(object sender, EventArgs e)
        {
            List<GridOrder> filteredItems = new List<GridOrder>();
            foreach (GridOrder item in Orders)
            {
                if (dateFrom <= item.CreateDate && item.CreateDate <= dateTo)
                {
                    filteredItems.Add(item);
                }
            }
            lv.ClearObjects();
            lv.SetObjects(filteredItems);
            fillLayer(filteredItems, null);
        }

        private void btnQuery_Click(object sender, EventArgs e)
        {
            QueryGridOrderSet query = new QueryGridOrderSet();
            query.IsShowConditonDlg = false;
            query.DateFrom = dateFrom;
            query.DateTo = dateTo;
            query.OrderIDLower = orderIDLower;
            query.Query();
        }

        private void miCollapseAll_Click(object sender, EventArgs e)
        {
            lv.CollapseAll();
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            lv.ExpandAll();
        }

        private void miShowAllOrder_Click(object sender, EventArgs e)
        {
            makeSureLayerVisible();
            layer.Orders = this.lv.Roots as List<GridOrder>;
            layer.Invalidate();
        }

       
    }
}
