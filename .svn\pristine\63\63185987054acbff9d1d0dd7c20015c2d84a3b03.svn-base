﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model
{
    /*Need2BePerfect_Qiujianwei
     * 900 1800已不分开，可以移除该合并帮助类
     */ 
    public static class ScanDataManager
    {
        /// <summary>
        /// 合并900,1800文件
        /// </summary>
        /// <returns></returns>
        public static bool Combine9001800Files()
        {
            MainModel mainModel = MainModel.GetInstance();
            DTFileDataManager file900 = null;
            DTFileDataManager file1800 = null;
            foreach (DTFileDataManager dtFileDataManager in mainModel.DTDataManager.FileDataManagers)
            {
                if (dtFileDataManager.FileName.EndsWith("900)"))
                {
                    file900 = dtFileDataManager;
                }
                if (dtFileDataManager.FileName.EndsWith("1800)"))
                {
                    file1800 = dtFileDataManager;
                }
            }
            if (file900 == null || file1800 == null)
            {
                return false;
            }
            mainModel.DTDataManager.Clear();
            addValidTP(mainModel, file900, file1800);
            return true;
        }

        private static void addValidTP(MainModel mainModel, DTFileDataManager file900, DTFileDataManager file1800)
        {
            int index1800 = 0;
            for (int i = 0; i < file900.TestPoints.Count; i++)
            {
                TestPoint tp900 = file900.TestPoints[i];
                for (int j = index1800; j < file1800.TestPoints.Count; j++)
                {
                    TestPoint tp1800 = file1800.TestPoints[j];
                    if (tp1800.DateTime > tp900.DateTime || (tp1800.DateTime == tp900.DateTime && tp1800.Millisecond > tp900.Millisecond))
                    {
                        mainModel.DTDataManager.Add(tp900);
                        break;
                    }
                    if (tp1800.DateTime == tp900.DateTime && tp1800.Millisecond == tp900.Millisecond)
                    {
                        tp900 = Combine9001800TestPoints(tp900, tp1800);
                        mainModel.DTDataManager.Add(tp900);
                        index1800 = j + 1;
                        break;
                    }
                    tp1800.ApplyHeader(tp900.FileInfo);
                    mainModel.DTDataManager.Add(tp1800);
                    index1800 = j + 1;
                }
            }
        }

        /// <summary>
        /// 合并900、1800采样点
        /// </summary>
        /// <param name="tp900">900采样点</param>
        /// <param name="tp1800">1800采样点</param>
        /// <returns>合并后的采样点</returns>
        public static TestPoint Combine9001800TestPoints(TestPoint tp900, TestPoint tp1800)
        {
            if (!(tp900 is ScanTestPoint_G) || !(tp1800 is ScanTestPoint_G))
            {
                return null;
            }
            List<float> rxLevList = new List<float>();  //场强顺序列表
            List<int> indexList = new List<int>();  //序号列表
            List<int> indexTPList = new List<int>();    //对应序号列表所在采样点，1：tp900，2：tp1800
            for (int i = 0; i < 50; i++)
            {
                float? rxLev900 = (float?)tp900["GSCAN_RxLev", i];
                float? rxLev1800 = (float?)tp1800["GSCAN_RxLev", i];
                if (rxLev900 == null && rxLev1800 == null)
                {
                    break;
                }
                if (rxLev900 != null)
                {
                    int index = getInsertIndexAsc(rxLevList, (float)rxLev900);
                    rxLevList.Insert(index, (float)rxLev900);
                    indexList.Insert(index, i);
                    indexTPList.Insert(index, 1);
                }
                if (rxLev1800 != null)
                {
                    int index = getInsertIndexAsc(rxLevList, (float)rxLev1800);
                    rxLevList.Insert(index, (float)rxLev1800);
                    indexList.Insert(index, i);
                    indexTPList.Insert(index, 2);
                }
            }

            TestPoint newTP = new TestPoint();
            newTP.ApplyHeader(tp900.FileInfo);
            newTP.Longitude = tp900.Longitude;
            newTP.Latitude = tp900.Latitude;
            newTP.Time = tp900.Time;
            newTP.Millisecond = tp900.Millisecond;
            newTP.MS = tp900.MS;
            for (int i = 0; i < indexList.Count; i++)
            {
                TestPoint tp;
                if (indexTPList[i] == 1)
                {
                    tp = tp900;
                }
                else
                {
                    tp = tp1800;
                }
                int index = indexList[i];
                newTP["GSCAN_RxLev", i] = tp["GSCAN_RxLev", index];
                newTP["GSCAN_BCCH", i] = tp["GSCAN_BCCH", index];
                newTP["GSCAN_BSIC", i] = tp["GSCAN_BSIC", index];
            }
            //for (int i = 0; i < 50; i++)
            //{
            //    float? rxLev1800 = (float?)tp1800["GSCAN_RxLev", i];
            //    if (rxLev1800 == null)
            //    {
            //        break;
            //    }
            //    for (int j = 0; j < 50; j++)
            //    {
            //        float? rxLev900 = (float?)tp900["GSCAN_RxLev", j];
            //        if (rxLev900 == null)
            //        {
            //            tp900["GSCAN_RxLev", j] = rxLev1800;
            //            tp900["GSCAN_BCCH", j] = tp1800["GSCAN_BCCH", i];
            //            tp900["GSCAN_BSIC", j] = tp1800["GSCAN_BSIC", i];
            //            break;
            //        }
            //        else if (rxLev900 < rxLev1800)
            //        {
            //            for (int k = 49; k > j; k--)
            //            {
            //                if (tp900["GSCAN_RxLev", k - 1] != null)
            //                {
            //                    tp900["GSCAN_RxLev", k] = tp900["GSCAN_RxLev", k - 1];
            //                    tp900["GSCAN_BCCH", k] = tp900["GSCAN_BCCH", k - 1];
            //                    tp900["GSCAN_BSIC", k] = tp900["GSCAN_BSIC", k - 1];
            //                }
            //            }
            //            tp900["GSCAN_RxLev", j] = rxLev1800;
            //            tp900["GSCAN_BCCH", j] = tp1800["GSCAN_BCCH", i];
            //            tp900["GSCAN_BSIC", j] = tp1800["GSCAN_BSIC", i];
            //            break;
            //        }
            //    }
            //}
            return newTP;
        }

        private static int getInsertIndexAsc(List<float> rxLevList, float rxLev)
        {
            if (rxLevList.Count == 0)
            {
                return 0;
            }
            else
            {
                for (int j = rxLevList.Count - 1; j >= 0; j--)
                {
                    if (rxLev < rxLevList[j])
                    {
                        return j + 1;
                    }
                }
                return 0;
            }
        }
    }
}
