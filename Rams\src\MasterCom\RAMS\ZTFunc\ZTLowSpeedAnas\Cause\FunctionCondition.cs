﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using System.IO;
using MasterCom.Util;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc.ZTLowSpeedAnas.Cause
{
    public class FunctionCondition
    {
        public static readonly string CfgFileName = string.Format(System.Windows.Forms.Application.StartupPath + "/config/ztfunc/LTE低速率原因分析.xml");

        
        public Dictionary<string, object> CfgParam
        {
            get
            {
                Dictionary<string, object> paramDic = new Dictionary<string, object>();
                paramDic["sampleCntMin"] = this.TestPointCountMin;
                paramDic["distanceMin"] = this.DistanceMin;
                paramDic["secondMin"] = this.SecondMin;
                paramDic["checkFTP"] = this.CheckFTP;
                paramDic["ftpRateMax"] = this.FTPRateMax;
                paramDic["checkHTTP"] = this.CheckHTTP;
                paramDic["httpRateMax"] = this.HTTPRateMax;
                paramDic["checkEmail"] = this.CheckEmail;
                paramDic["emailRateMax"] = this.EmailRateMax;

                /* 获取子原因，将子原因Dictionary添加到lsit里，再保存到大的Dictionary */
                List<object> list = new List<object>();
                foreach (CauseBase cause in Causes)
                {
                    list.Add(cause.CfgParam);
                }
                paramDic["CauseSet"] = list;

                return paramDic;
            }
            set
            {
                if (value == null)
                {
                    return;
                }

                    this.TestPointCountMin = (int)value["sampleCntMin"];
                    this.DistanceMin = (double)value["distanceMin"];
                    this.SecondMin = (double)value["secondMin"];
                    this.CheckFTP = (bool)value["checkFTP"];
                    this.FTPRateMax = (double)value["ftpRateMax"];
                    this.CheckHTTP = (bool)value["checkHTTP"];
                    this.HTTPRateMax = (double)value["httpRateMax"];
                    this.CheckEmail = (bool)value["checkEmail"];
                    this.EmailRateMax = (double)value["emailRateMax"];

                    Causes = new List<CauseBase>();
                    List<object> list = value["CauseSet"] as List<object>;
                    foreach (object item in list)
                    {
                        Dictionary<string, object> dic = item as Dictionary<string, object>;
                        string typeName = dic["TypeName"].ToString();
                        System.Reflection.Assembly assembly = System.Reflection.Assembly.GetExecutingAssembly();
                        CauseBase cause = (CauseBase)assembly.CreateInstance(typeName);
                        cause.CfgParam = dic;
                        Causes.Add(cause);
                    }
                
                
            }
        }

        public FunctionCondition()
        {
            Causes = new List<CauseBase>();

            Causes.Add(new HoUpdateCause());
            Causes.Add(new CoverCause());
            Causes.Add(new QualCause());

        }
        
        public int TestPointCountMin { get; set; } = 5;
        public double DistanceMin { get; set; } = 50;
        public double SecondMin { get; set; } = 10;
        public List<CauseBase> Causes { get; set; }
        public bool CheckFTP { get; set; } = true;
        public double FTPRateMax { get; set; } = 5;
        public bool CheckHTTP { get; set; } = true;
        public double HTTPRateMax { get; set; } = 5;
        public bool CheckEmail { get; set; } = true;
        public double EmailRateMax { get; set; } = 2;

        internal bool IsValidSpeed(Model.TestPoint testPoint)
        {
            short? type = (short?)GetAppType(testPoint);
            if (type == null)
            {
                return false;
            }

            object obj = GetSpeed(testPoint);
            if (obj == null)
            {
                return false;
            }
            float speed = float.Parse(obj.ToString());
            if (speed < 0)
            {
                return false;
            }
            if (type == (int)AppType.FTP_Download && CheckFTP)
            {
                return speed < FTPRateMax;
            }
            if (type == (int)AppType.Http_Download && CheckHTTP)
            {
                return speed < HTTPRateMax;
            }
            if (type == (int)AppType.Email_SMTP && CheckEmail)
            {
                return speed < EmailRateMax;
            }
            return false;
        }
        protected object GetAppType(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_APP_type"];
            }
            return tp["lte_APP_type"];
        }
        protected object GetSpeed(TestPoint tp)
        {
            if (tp is LTEFddTestPoint)
            {
                return tp["lte_fdd_APP_Speed_Mb"];
            }
            return tp["lte_APP_Speed_Mb"];
        }

        internal void Judge(LowSpeedSeg segItem, List<Model.Event> evts, List<TestPoint> allTP)
        {
            foreach (CauseBase item in Causes)
            {
                if (!segItem.NeedJudge)
                {
                    return;
                }
                item.Judge(segItem, evts, allTP);
            }
            if (segItem.NeedJudge)
            {
                segItem.SetUnknowReason();
            }
        }

        internal bool IsValidSegment(LowSpeedSeg seg)
        {
            return seg.SampleCount >= this.TestPointCountMin
                && seg.Distance >= DistanceMin && seg.Second >= SecondMin;
        }

        public void LoadCfg()
        {
            if (File.Exists(CfgFileName))
            {
                XmlConfigFile configFile = new XmlConfigFile(CfgFileName);
                CfgParam = configFile.GetItemValue("ConditionCfg", "Condition") as Dictionary<string, object>;
            }
        }

        public void Save()
        {
            MasterCom.Util.XmlConfigFile xmlFile = new MasterCom.Util.XmlConfigFile();
            XmlElement cfgE = xmlFile.AddConfig("ConditionCfg");
            xmlFile.AddItem(cfgE, "Condition", this.CfgParam);
            xmlFile.Save(CfgFileName);
        }
    }
}
