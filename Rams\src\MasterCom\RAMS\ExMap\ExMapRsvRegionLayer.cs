﻿using System;
using System.Collections.Generic;
using System.Text;
using GMap.NET;
using MasterCom.MTGis;
using MasterCom.Util;

namespace MasterCom.RAMS.ExMap
{
    public class ExMapRsvRegionLayer : ExMapDrawBaseLayer
    {
        public ExMapRsvRegionLayer(MTExGMap mapCtrl)
            : base(mapCtrl)
        {

        }

        private List<List<PointLatLng>> regionPointSet = null;

        private readonly System.Drawing.Pen linePen = new System.Drawing.Pen(System.Drawing.Color.Green, 3);
        readonly System.Drawing.SolidBrush brush = new System.Drawing.SolidBrush(System.Drawing.Color.FromArgb(50, System.Drawing.Color.Green));
        internal void ApplyResvRegion(List<ResvRegion> list)
        {
            regionPointSet = new List<List<PointLatLng>>();
            if (list == null)
            {
                return;
            }
            foreach (ResvRegion reg in list)
            {
                for (int x = 0; x < reg.Shape.NumParts; x++)
                {

                    List<PointLatLng> pnts = ShapeHelper.GetPartShapePointsEx(reg.Shape, x);
                    if (pnts != null && pnts.Count > 0)
                    {
                        regionPointSet.Add(pnts);
                    }
                }
            }
        }

        public override void Draw(System.Drawing.Graphics g, PointLatLng ltPt, PointLatLng brPt)
        {
            if (regionPointSet == null)
            {
                return;
            }

            foreach (List<PointLatLng> regPnts in regionPointSet)
            {
                System.Drawing.Point[] pnts = new System.Drawing.Point[regPnts.Count];
                for (int i = 0; i < regPnts.Count; i++)
                {
                    GPoint gPnt = this.exMap.FromLatLngToLocalAdaptered(regPnts[i]);
                    pnts[i] = new System.Drawing.Point(gPnt.X, gPnt.Y);
                }
                g.DrawPolygon(linePen, pnts);
                g.FillPolygon(brush, pnts);
            }
        }

        public override string Alias
        {
            get { return "预存区域图层"; }
        }
    }
}
