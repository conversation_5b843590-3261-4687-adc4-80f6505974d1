﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class TdAntennaForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraCharts.XYDiagram xyDiagram1 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.SecondaryAxisY secondaryAxisY1 = new DevExpress.XtraCharts.SecondaryAxisY();
            DevExpress.XtraCharts.SecondaryAxisY secondaryAxisY2 = new DevExpress.XtraCharts.SecondaryAxisY();
            DevExpress.XtraCharts.Series series1 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel1 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesView sideBySideBarSeriesView1 = new DevExpress.XtraCharts.SideBySideBarSeriesView();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel2 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.XYDiagram xyDiagram2 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.SecondaryAxisY secondaryAxisY3 = new DevExpress.XtraCharts.SecondaryAxisY();
            DevExpress.XtraCharts.Series series2 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel1 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.SplineAreaSeriesView splineAreaSeriesView1 = new DevExpress.XtraCharts.SplineAreaSeriesView();
            DevExpress.XtraCharts.Series series3 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel2 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.SplineSeriesView splineSeriesView1 = new DevExpress.XtraCharts.SplineSeriesView();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel3 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.SplineAreaSeriesView splineAreaSeriesView2 = new DevExpress.XtraCharts.SplineAreaSeriesView();
            DevExpress.XtraCharts.XYDiagram xyDiagram3 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.SecondaryAxisY secondaryAxisY4 = new DevExpress.XtraCharts.SecondaryAxisY();
            DevExpress.XtraCharts.Series series4 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel4 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.SplineAreaSeriesView splineAreaSeriesView3 = new DevExpress.XtraCharts.SplineAreaSeriesView();
            DevExpress.XtraCharts.Series series5 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel5 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.SplineSeriesView splineSeriesView2 = new DevExpress.XtraCharts.SplineSeriesView();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel6 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.SplineAreaSeriesView splineAreaSeriesView4 = new DevExpress.XtraCharts.SplineAreaSeriesView();
            DevExpress.XtraCharts.RadarDiagram radarDiagram1 = new DevExpress.XtraCharts.RadarDiagram();
            DevExpress.XtraCharts.Series series6 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel1 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.SeriesPoint seriesPoint1 = new DevExpress.XtraCharts.SeriesPoint("360");
            DevExpress.XtraCharts.SeriesPoint seriesPoint2 = new DevExpress.XtraCharts.SeriesPoint("350");
            DevExpress.XtraCharts.SeriesPoint seriesPoint3 = new DevExpress.XtraCharts.SeriesPoint("340");
            DevExpress.XtraCharts.SeriesPoint seriesPoint4 = new DevExpress.XtraCharts.SeriesPoint("330");
            DevExpress.XtraCharts.SeriesPoint seriesPoint5 = new DevExpress.XtraCharts.SeriesPoint("320");
            DevExpress.XtraCharts.RadarLineSeriesView radarLineSeriesView1 = new DevExpress.XtraCharts.RadarLineSeriesView();
            DevExpress.XtraCharts.Series series7 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel2 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarLineSeriesView radarLineSeriesView2 = new DevExpress.XtraCharts.RadarLineSeriesView();
            DevExpress.XtraCharts.RadarPointSeriesLabel radarPointSeriesLabel3 = new DevExpress.XtraCharts.RadarPointSeriesLabel();
            DevExpress.XtraCharts.RadarLineSeriesView radarLineSeriesView3 = new DevExpress.XtraCharts.RadarLineSeriesView();
            this.xTabPageData = new DevExpress.XtraTab.XtraTabControl();
            this.cellTabPage = new DevExpress.XtraTab.XtraTabPage();
            this.btnNextpage = new System.Windows.Forms.Button();
            this.btnPrevpage = new System.Windows.Forms.Button();
            this.label5 = new System.Windows.Forms.Label();
            this.txtPage = new System.Windows.Forms.TextBox();
            this.labPage = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.btnGo = new System.Windows.Forms.Button();
            this.labNum = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.btnSearch = new System.Windows.Forms.Button();
            this.txtCellName = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.dataGridViewCell = new System.Windows.Forms.DataGridView();
            this.Column10 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column11 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column12 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column13 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column14 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column15 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column16 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column17 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column18 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column19 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column20 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column21 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column22 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column23 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column24 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miShwoChart = new System.Windows.Forms.ToolStripMenuItem();
            this.miShowGis = new System.Windows.Forms.ToolStripMenuItem();
            this.miShowSimulation = new System.Windows.Forms.ToolStripMenuItem();
            this.拆分导出CSVToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportWholeExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.btsTabPage = new DevExpress.XtraTab.XtraTabPage();
            this.dataGridViewBts = new System.Windows.Forms.DataGridView();
            this.dataGridViewTextBoxColumn29 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn30 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn31 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn32 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn33 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn36 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column26 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column28 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column29 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column30 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column31 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column32 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn40 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn41 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn42 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn43 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.sectionTabPage = new DevExpress.XtraTab.XtraTabPage();
            this.dataGridView = new System.Windows.Forms.DataGridView();
            this.colCellname = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colBcch = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column25 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.coldMaxPcc0_30_150_180 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colSection = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colRxlevSampleNum = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colSamplePect = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colAvgPccpch = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colMaxPccpch = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colDpchAvgRscp = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colDpchAvgC2I = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colAvgTdBler = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colAvgTA = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colAvgSampleDistance = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colAvgCellNum = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colSampleTotal = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colAnaType = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colIangle_ob = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colIaltitude = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colIangle_dir = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCellPccpAvgRscp = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCellDpchAvgRscp = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCellDpchAvgC2I = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCellAvgTdBler = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCellAvgSampleDistance = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCellOverNum = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCellCellNum = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colCellAvgTA = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.antRadioTabPage = new DevExpress.XtraTab.XtraTabPage();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.dataGridViewAngle = new System.Windows.Forms.DataGridView();
            this.cluCellName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.cluTarget = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.panel1 = new System.Windows.Forms.Panel();
            this.chartControl1 = new DevExpress.XtraCharts.ChartControl();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.cbbxSeries2 = new System.Windows.Forms.ComboBox();
            this.cbbxSeries1 = new System.Windows.Forms.ComboBox();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.chartTabPage = new DevExpress.XtraTab.XtraTabPage();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.chartRSCP = new DevExpress.XtraCharts.ChartControl();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.chartC2I = new DevExpress.XtraCharts.ChartControl();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.chartControl2 = new DevExpress.XtraCharts.ChartControl();
            this.probStatTabPage = new DevExpress.XtraTab.XtraTabPage();
            this.dataGridViewAbnormal = new System.Windows.Forms.DataGridView();
            this.dataGridViewTextBoxColumn28 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.colRegionname = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column3 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column4 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column5 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column6 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column7 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column8 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Column9 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            ((System.ComponentModel.ISupportInitialize)(this.xTabPageData)).BeginInit();
            this.xTabPageData.SuspendLayout();
            this.cellTabPage.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewCell)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.btsTabPage.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewBts)).BeginInit();
            this.sectionTabPage.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).BeginInit();
            this.antRadioTabPage.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewAngle)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.chartTabPage.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            this.groupBox6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartRSCP)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(splineSeriesView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView2)).BeginInit();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartC2I)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(splineSeriesView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView4)).BeginInit();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarDiagram1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView3)).BeginInit();
            this.probStatTabPage.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewAbnormal)).BeginInit();
            this.SuspendLayout();
            // 
            // xTabPageData
            // 
            this.xTabPageData.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xTabPageData.Location = new System.Drawing.Point(0, 0);
            this.xTabPageData.Name = "xTabPageData";
            this.xTabPageData.SelectedTabPage = this.cellTabPage;
            this.xTabPageData.Size = new System.Drawing.Size(1166, 646);
            this.xTabPageData.TabIndex = 0;
            this.xTabPageData.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.cellTabPage,
            this.btsTabPage,
            this.sectionTabPage,
            this.antRadioTabPage,
            this.chartTabPage,
            this.probStatTabPage});
            this.xTabPageData.SelectedPageChanged += new DevExpress.XtraTab.TabPageChangedEventHandler(this.xTabPageData_SelectedPageChanged);
            // 
            // cellTabPage
            // 
            this.cellTabPage.Controls.Add(this.btnNextpage);
            this.cellTabPage.Controls.Add(this.btnPrevpage);
            this.cellTabPage.Controls.Add(this.label5);
            this.cellTabPage.Controls.Add(this.txtPage);
            this.cellTabPage.Controls.Add(this.labPage);
            this.cellTabPage.Controls.Add(this.label4);
            this.cellTabPage.Controls.Add(this.btnGo);
            this.cellTabPage.Controls.Add(this.labNum);
            this.cellTabPage.Controls.Add(this.label3);
            this.cellTabPage.Controls.Add(this.label2);
            this.cellTabPage.Controls.Add(this.btnSearch);
            this.cellTabPage.Controls.Add(this.txtCellName);
            this.cellTabPage.Controls.Add(this.label1);
            this.cellTabPage.Controls.Add(this.dataGridViewCell);
            this.cellTabPage.Name = "cellTabPage";
            this.cellTabPage.Size = new System.Drawing.Size(1159, 616);
            this.cellTabPage.Text = "小区覆盖统计分析";
            // 
            // btnNextpage
            // 
            this.btnNextpage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnNextpage.Location = new System.Drawing.Point(845, 592);
            this.btnNextpage.Name = "btnNextpage";
            this.btnNextpage.Size = new System.Drawing.Size(33, 23);
            this.btnNextpage.TabIndex = 68;
            this.btnNextpage.Text = ">>";
            this.btnNextpage.UseVisualStyleBackColor = true;
            this.btnNextpage.Click += new System.EventHandler(this.btnNextpage_Click);
            // 
            // btnPrevpage
            // 
            this.btnPrevpage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnPrevpage.Location = new System.Drawing.Point(806, 592);
            this.btnPrevpage.Name = "btnPrevpage";
            this.btnPrevpage.Size = new System.Drawing.Size(33, 23);
            this.btnPrevpage.TabIndex = 67;
            this.btnPrevpage.Text = "<<";
            this.btnPrevpage.UseVisualStyleBackColor = true;
            this.btnPrevpage.Click += new System.EventHandler(this.btnPrevpage_Click);
            // 
            // label5
            // 
            this.label5.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(748, 594);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(19, 14);
            this.label5.TabIndex = 66;
            this.label5.Text = "页";
            // 
            // txtPage
            // 
            this.txtPage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txtPage.Location = new System.Drawing.Point(683, 591);
            this.txtPage.Name = "txtPage";
            this.txtPage.Size = new System.Drawing.Size(63, 22);
            this.txtPage.TabIndex = 65;
            this.txtPage.Text = "1";
            // 
            // labPage
            // 
            this.labPage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.labPage.AutoSize = true;
            this.labPage.Location = new System.Drawing.Point(583, 596);
            this.labPage.Name = "labPage";
            this.labPage.Size = new System.Drawing.Size(14, 14);
            this.labPage.TabIndex = 64;
            this.labPage.Text = "0";
            // 
            // label4
            // 
            this.label4.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(540, 595);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(43, 14);
            this.label4.TabIndex = 63;
            this.label4.Text = "个，共";
            // 
            // btnGo
            // 
            this.btnGo.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnGo.Location = new System.Drawing.Point(767, 591);
            this.btnGo.Name = "btnGo";
            this.btnGo.Size = new System.Drawing.Size(33, 23);
            this.btnGo.TabIndex = 62;
            this.btnGo.Text = "GO";
            this.btnGo.UseVisualStyleBackColor = true;
            this.btnGo.Click += new System.EventHandler(this.btnGo_Click);
            // 
            // labNum
            // 
            this.labNum.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.labNum.AutoSize = true;
            this.labNum.Location = new System.Drawing.Point(495, 596);
            this.labNum.Name = "labNum";
            this.labNum.Size = new System.Drawing.Size(14, 14);
            this.labNum.TabIndex = 61;
            this.labNum.Text = "0";
            // 
            // label3
            // 
            this.label3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(617, 595);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(67, 14);
            this.label3.TabIndex = 60;
            this.label3.Text = "页，跳转至";
            // 
            // label2
            // 
            this.label2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(419, 595);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(79, 14);
            this.label2.TabIndex = 59;
            this.label2.Text = "总计小区共：";
            // 
            // btnSearch
            // 
            this.btnSearch.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSearch.Location = new System.Drawing.Point(1113, 591);
            this.btnSearch.Name = "btnSearch";
            this.btnSearch.Size = new System.Drawing.Size(42, 23);
            this.btnSearch.TabIndex = 58;
            this.btnSearch.Text = "查找";
            this.btnSearch.UseVisualStyleBackColor = true;
            this.btnSearch.Click += new System.EventHandler(this.btnSearch_Click);
            // 
            // txtCellName
            // 
            this.txtCellName.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txtCellName.Location = new System.Drawing.Point(951, 592);
            this.txtCellName.Name = "txtCellName";
            this.txtCellName.Size = new System.Drawing.Size(157, 22);
            this.txtCellName.TabIndex = 57;
            // 
            // label1
            // 
            this.label1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(890, 596);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(67, 14);
            this.label1.TabIndex = 56;
            this.label1.Text = "小区名称：";
            // 
            // dataGridViewCell
            // 
            this.dataGridViewCell.AllowUserToAddRows = false;
            this.dataGridViewCell.AllowUserToDeleteRows = false;
            this.dataGridViewCell.BackgroundColor = System.Drawing.SystemColors.Window;
            this.dataGridViewCell.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewCell.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.Column10,
            this.Column11,
            this.Column12,
            this.Column13,
            this.Column14,
            this.Column15,
            this.Column16,
            this.Column17,
            this.Column18,
            this.Column19,
            this.Column20,
            this.Column21,
            this.Column22,
            this.Column23,
            this.Column24});
            this.dataGridViewCell.ContextMenuStrip = this.contextMenuStrip;
            this.dataGridViewCell.Location = new System.Drawing.Point(0, 0);
            this.dataGridViewCell.Name = "dataGridViewCell";
            this.dataGridViewCell.ReadOnly = true;
            this.dataGridViewCell.RowTemplate.Height = 23;
            this.dataGridViewCell.Size = new System.Drawing.Size(1159, 586);
            this.dataGridViewCell.TabIndex = 1;
            // 
            // Column10
            // 
            this.Column10.HeaderText = "时间";
            this.Column10.Name = "Column10";
            this.Column10.ReadOnly = true;
            // 
            // Column11
            // 
            this.Column11.HeaderText = "地市名称";
            this.Column11.Name = "Column11";
            this.Column11.ReadOnly = true;
            // 
            // Column12
            // 
            this.Column12.HeaderText = "网格号";
            this.Column12.Name = "Column12";
            this.Column12.ReadOnly = true;
            // 
            // Column13
            // 
            this.Column13.HeaderText = "BSC名";
            this.Column13.Name = "Column13";
            this.Column13.ReadOnly = true;
            // 
            // Column14
            // 
            this.Column14.HeaderText = "小区名";
            this.Column14.Name = "Column14";
            this.Column14.ReadOnly = true;
            // 
            // Column15
            // 
            this.Column15.HeaderText = "频段标示";
            this.Column15.Name = "Column15";
            this.Column15.ReadOnly = true;
            // 
            // Column16
            // 
            this.Column16.HeaderText = "分析结果";
            this.Column16.Name = "Column16";
            this.Column16.ReadOnly = true;
            // 
            // Column17
            // 
            this.Column17.HeaderText = "采样点总数";
            this.Column17.Name = "Column17";
            this.Column17.ReadOnly = true;
            // 
            // Column18
            // 
            this.Column18.HeaderText = "小区RXLEV均值";
            this.Column18.Name = "Column18";
            this.Column18.ReadOnly = true;
            // 
            // Column19
            // 
            this.Column19.HeaderText = "小区PCCHC2I<=-3占比(%)";
            this.Column19.Name = "Column19";
            this.Column19.ReadOnly = true;
            // 
            // Column20
            // 
            this.Column20.HeaderText = "小区C/I平均";
            this.Column20.Name = "Column20";
            this.Column20.ReadOnly = true;
            // 
            // Column21
            // 
            this.Column21.HeaderText = "小区平均通信距离";
            this.Column21.Name = "Column21";
            this.Column21.ReadOnly = true;
            // 
            // Column22
            // 
            this.Column22.HeaderText = "小区过覆盖指数";
            this.Column22.Name = "Column22";
            this.Column22.ReadOnly = true;
            // 
            // Column23
            // 
            this.Column23.HeaderText = "小区区域站点密集指数";
            this.Column23.Name = "Column23";
            this.Column23.ReadOnly = true;
            // 
            // Column24
            // 
            this.Column24.HeaderText = "小区平均TA";
            this.Column24.Name = "Column24";
            this.Column24.ReadOnly = true;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miShwoChart,
            this.miShowGis,
            this.miShowSimulation,
            this.拆分导出CSVToolStripMenuItem,
            this.miExportWholeExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(207, 114);
            // 
            // miShwoChart
            // 
            this.miShwoChart.Name = "miShwoChart";
            this.miShwoChart.Size = new System.Drawing.Size(206, 22);
            this.miShwoChart.Text = "显示天线辐射波形重建";
            this.miShwoChart.Click += new System.EventHandler(this.miShowChart_Click);
            // 
            // miShowGis
            // 
            this.miShowGis.Name = "miShowGis";
            this.miShowGis.Size = new System.Drawing.Size(206, 22);
            this.miShowGis.Text = "显示选择小区及其采样点";
            this.miShowGis.Click += new System.EventHandler(this.miShowGis_Click);
            // 
            // miShowSimulation
            // 
            this.miShowSimulation.Name = "miShowSimulation";
            this.miShowSimulation.Size = new System.Drawing.Size(206, 22);
            this.miShowSimulation.Text = "显示天线覆盖仿真";
            this.miShowSimulation.Click += new System.EventHandler(this.miShowSimulation_Click);
            // 
            // 拆分导出CSVToolStripMenuItem
            // 
            this.拆分导出CSVToolStripMenuItem.Name = "拆分导出CSVToolStripMenuItem";
            this.拆分导出CSVToolStripMenuItem.Size = new System.Drawing.Size(206, 22);
            this.拆分导出CSVToolStripMenuItem.Text = "拆分导出CSV";
            this.拆分导出CSVToolStripMenuItem.Click += new System.EventHandler(this.拆分导出CSVToolStripMenuItem_Click);
            // 
            // miExportWholeExcel
            // 
            this.miExportWholeExcel.Name = "miExportWholeExcel";
            this.miExportWholeExcel.Size = new System.Drawing.Size(206, 22);
            this.miExportWholeExcel.Text = "导出Excel";
            this.miExportWholeExcel.Click += new System.EventHandler(this.miExportWholeExcel_Click);
            // 
            // btsTabPage
            // 
            this.btsTabPage.Controls.Add(this.dataGridViewBts);
            this.btsTabPage.Name = "btsTabPage";
            this.btsTabPage.Size = new System.Drawing.Size(1159, 616);
            this.btsTabPage.Text = "基站覆盖统计分析";
            // 
            // dataGridViewBts
            // 
            this.dataGridViewBts.AllowUserToAddRows = false;
            this.dataGridViewBts.AllowUserToDeleteRows = false;
            this.dataGridViewBts.BackgroundColor = System.Drawing.SystemColors.Window;
            this.dataGridViewBts.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewBts.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.dataGridViewTextBoxColumn29,
            this.dataGridViewTextBoxColumn30,
            this.dataGridViewTextBoxColumn31,
            this.dataGridViewTextBoxColumn32,
            this.dataGridViewTextBoxColumn33,
            this.dataGridViewTextBoxColumn36,
            this.dataGridViewTextBoxColumn1,
            this.Column26,
            this.Column28,
            this.Column29,
            this.Column30,
            this.Column31,
            this.Column32,
            this.dataGridViewTextBoxColumn40,
            this.dataGridViewTextBoxColumn41,
            this.dataGridViewTextBoxColumn42,
            this.dataGridViewTextBoxColumn43});
            this.dataGridViewBts.ContextMenuStrip = this.contextMenuStrip;
            this.dataGridViewBts.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridViewBts.Location = new System.Drawing.Point(0, 0);
            this.dataGridViewBts.Name = "dataGridViewBts";
            this.dataGridViewBts.ReadOnly = true;
            this.dataGridViewBts.RowTemplate.Height = 23;
            this.dataGridViewBts.Size = new System.Drawing.Size(1159, 616);
            this.dataGridViewBts.TabIndex = 2;
            // 
            // dataGridViewTextBoxColumn29
            // 
            this.dataGridViewTextBoxColumn29.HeaderText = "序号";
            this.dataGridViewTextBoxColumn29.Name = "dataGridViewTextBoxColumn29";
            this.dataGridViewTextBoxColumn29.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn30
            // 
            this.dataGridViewTextBoxColumn30.HeaderText = "地市名称";
            this.dataGridViewTextBoxColumn30.Name = "dataGridViewTextBoxColumn30";
            this.dataGridViewTextBoxColumn30.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn31
            // 
            this.dataGridViewTextBoxColumn31.HeaderText = "网格号";
            this.dataGridViewTextBoxColumn31.Name = "dataGridViewTextBoxColumn31";
            this.dataGridViewTextBoxColumn31.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn32
            // 
            this.dataGridViewTextBoxColumn32.HeaderText = "基站名称";
            this.dataGridViewTextBoxColumn32.Name = "dataGridViewTextBoxColumn32";
            this.dataGridViewTextBoxColumn32.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn33
            // 
            this.dataGridViewTextBoxColumn33.HeaderText = "基站类型";
            this.dataGridViewTextBoxColumn33.Name = "dataGridViewTextBoxColumn33";
            this.dataGridViewTextBoxColumn33.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn36
            // 
            this.dataGridViewTextBoxColumn36.HeaderText = "采样点总数";
            this.dataGridViewTextBoxColumn36.Name = "dataGridViewTextBoxColumn36";
            this.dataGridViewTextBoxColumn36.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn1
            // 
            this.dataGridViewTextBoxColumn1.HeaderText = "覆盖率(PCCPCH RSCP>=-90 & C/I>=-3)(%)";
            this.dataGridViewTextBoxColumn1.Name = "dataGridViewTextBoxColumn1";
            this.dataGridViewTextBoxColumn1.ReadOnly = true;
            // 
            // Column26
            // 
            this.Column26.HeaderText = "覆盖率(PCCPCH RSCP>=-95 & C/I>=-3)(%)";
            this.Column26.Name = "Column26";
            this.Column26.ReadOnly = true;
            // 
            // Column28
            // 
            this.Column28.HeaderText = "覆盖率(DPCH RSCP>=-90 & C/I>=-3)(%)";
            this.Column28.Name = "Column28";
            this.Column28.ReadOnly = true;
            // 
            // Column29
            // 
            this.Column29.HeaderText = "覆盖率(DPCH RSCP>=-95 & C/I>=-3)(%)";
            this.Column29.Name = "Column29";
            this.Column29.ReadOnly = true;
            // 
            // Column30
            // 
            this.Column30.HeaderText = "基站PCCPCH RSCP均值";
            this.Column30.Name = "Column30";
            this.Column30.ReadOnly = true;
            // 
            // Column31
            // 
            this.Column31.HeaderText = "基站PCCPCH C2I<=-3占比(%)";
            this.Column31.Name = "Column31";
            this.Column31.ReadOnly = true;
            // 
            // Column32
            // 
            this.Column32.HeaderText = "基站PCCPCH C/I平均";
            this.Column32.Name = "Column32";
            this.Column32.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn40
            // 
            this.dataGridViewTextBoxColumn40.HeaderText = "基站平均通信距离";
            this.dataGridViewTextBoxColumn40.Name = "dataGridViewTextBoxColumn40";
            this.dataGridViewTextBoxColumn40.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn41
            // 
            this.dataGridViewTextBoxColumn41.HeaderText = "基站过覆盖指数";
            this.dataGridViewTextBoxColumn41.Name = "dataGridViewTextBoxColumn41";
            this.dataGridViewTextBoxColumn41.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn42
            // 
            this.dataGridViewTextBoxColumn42.HeaderText = "基站区域站点密集指数";
            this.dataGridViewTextBoxColumn42.Name = "dataGridViewTextBoxColumn42";
            this.dataGridViewTextBoxColumn42.ReadOnly = true;
            // 
            // dataGridViewTextBoxColumn43
            // 
            this.dataGridViewTextBoxColumn43.HeaderText = "基站平均TA";
            this.dataGridViewTextBoxColumn43.Name = "dataGridViewTextBoxColumn43";
            this.dataGridViewTextBoxColumn43.ReadOnly = true;
            // 
            // sectionTabPage
            // 
            this.sectionTabPage.Controls.Add(this.dataGridView);
            this.sectionTabPage.Name = "sectionTabPage";
            this.sectionTabPage.Size = new System.Drawing.Size(1159, 616);
            this.sectionTabPage.Text = "覆盖角度区间化统计分析";
            // 
            // dataGridView
            // 
            this.dataGridView.AllowUserToAddRows = false;
            this.dataGridView.AllowUserToDeleteRows = false;
            this.dataGridView.BackgroundColor = System.Drawing.Color.White;
            this.dataGridView.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.colCellname,
            this.colBcch,
            this.Column25,
            this.coldMaxPcc0_30_150_180,
            this.colSection,
            this.colRxlevSampleNum,
            this.colSamplePect,
            this.colAvgPccpch,
            this.colMaxPccpch,
            this.colDpchAvgRscp,
            this.colDpchAvgC2I,
            this.colAvgTdBler,
            this.colAvgTA,
            this.colAvgSampleDistance,
            this.colAvgCellNum,
            this.colSampleTotal,
            this.colAnaType,
            this.colIangle_ob,
            this.colIaltitude,
            this.colIangle_dir,
            this.colCellPccpAvgRscp,
            this.colCellDpchAvgRscp,
            this.colCellDpchAvgC2I,
            this.colCellAvgTdBler,
            this.colCellAvgSampleDistance,
            this.colCellOverNum,
            this.colCellCellNum,
            this.colCellAvgTA});
            this.dataGridView.ContextMenuStrip = this.contextMenuStrip;
            this.dataGridView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridView.Location = new System.Drawing.Point(0, 0);
            this.dataGridView.Name = "dataGridView";
            this.dataGridView.RowTemplate.Height = 23;
            this.dataGridView.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridView.Size = new System.Drawing.Size(1159, 616);
            this.dataGridView.TabIndex = 0;
            // 
            // colCellname
            // 
            this.colCellname.HeaderText = "小区名称";
            this.colCellname.Name = "colCellname";
            this.colCellname.ReadOnly = true;
            this.colCellname.Width = 250;
            // 
            // colBcch
            // 
            this.colBcch.HeaderText = "小区频段";
            this.colBcch.Name = "colBcch";
            this.colBcch.ReadOnly = true;
            // 
            // Column25
            // 
            this.Column25.HeaderText = "分析结果";
            this.Column25.Name = "Column25";
            // 
            // coldMaxPcc0_30_150_180
            // 
            this.coldMaxPcc0_30_150_180.HeaderText = "[0,30]区间与(150,180]区间最强PCCPCH的差值";
            this.coldMaxPcc0_30_150_180.Name = "coldMaxPcc0_30_150_180";
            this.coldMaxPcc0_30_150_180.ReadOnly = true;
            this.coldMaxPcc0_30_150_180.Width = 200;
            // 
            // colSection
            // 
            this.colSection.HeaderText = "方向角偏差区间";
            this.colSection.Name = "colSection";
            this.colSection.ReadOnly = true;
            // 
            // colRxlevSampleNum
            // 
            this.colRxlevSampleNum.HeaderText = "区间采样点数";
            this.colRxlevSampleNum.Name = "colRxlevSampleNum";
            this.colRxlevSampleNum.ReadOnly = true;
            // 
            // colSamplePect
            // 
            this.colSamplePect.HeaderText = "采样点占比";
            this.colSamplePect.Name = "colSamplePect";
            this.colSamplePect.ReadOnly = true;
            // 
            // colAvgPccpch
            // 
            this.colAvgPccpch.HeaderText = "平均DL PCCPCH(dBm)";
            this.colAvgPccpch.Name = "colAvgPccpch";
            this.colAvgPccpch.ReadOnly = true;
            // 
            // colMaxPccpch
            // 
            this.colMaxPccpch.HeaderText = "最大DL PCCPCH(dBm)";
            this.colMaxPccpch.Name = "colMaxPccpch";
            this.colMaxPccpch.ReadOnly = true;
            // 
            // colDpchAvgRscp
            // 
            this.colDpchAvgRscp.HeaderText = "区间DPCH RSCP平均";
            this.colDpchAvgRscp.Name = "colDpchAvgRscp";
            this.colDpchAvgRscp.ReadOnly = true;
            this.colDpchAvgRscp.Width = 120;
            // 
            // colDpchAvgC2I
            // 
            this.colDpchAvgC2I.HeaderText = "区间DPCH C/I平均";
            this.colDpchAvgC2I.Name = "colDpchAvgC2I";
            this.colDpchAvgC2I.ReadOnly = true;
            // 
            // colAvgTdBler
            // 
            this.colAvgTdBler.HeaderText = "区间TD_BLER平均";
            this.colAvgTdBler.Name = "colAvgTdBler";
            this.colAvgTdBler.ReadOnly = true;
            // 
            // colAvgTA
            // 
            this.colAvgTA.HeaderText = "区间平均TA";
            this.colAvgTA.Name = "colAvgTA";
            // 
            // colAvgSampleDistance
            // 
            this.colAvgSampleDistance.HeaderText = "区间平均通信距离";
            this.colAvgSampleDistance.Name = "colAvgSampleDistance";
            this.colAvgSampleDistance.ReadOnly = true;
            // 
            // colAvgCellNum
            // 
            this.colAvgCellNum.HeaderText = "区间过覆盖指数";
            this.colAvgCellNum.Name = "colAvgCellNum";
            this.colAvgCellNum.ReadOnly = true;
            // 
            // colSampleTotal
            // 
            this.colSampleTotal.HeaderText = "总采样点数";
            this.colSampleTotal.Name = "colSampleTotal";
            this.colSampleTotal.ReadOnly = true;
            // 
            // colAnaType
            // 
            this.colAnaType.HeaderText = "天线类型";
            this.colAnaType.Name = "colAnaType";
            this.colAnaType.ReadOnly = true;
            // 
            // colIangle_ob
            // 
            this.colIangle_ob.HeaderText = "下倾角";
            this.colIangle_ob.Name = "colIangle_ob";
            this.colIangle_ob.ReadOnly = true;
            // 
            // colIaltitude
            // 
            this.colIaltitude.HeaderText = "挂高";
            this.colIaltitude.Name = "colIaltitude";
            this.colIaltitude.ReadOnly = true;
            // 
            // colIangle_dir
            // 
            this.colIangle_dir.HeaderText = "方向角";
            this.colIangle_dir.Name = "colIangle_dir";
            this.colIangle_dir.ReadOnly = true;
            // 
            // colCellPccpAvgRscp
            // 
            this.colCellPccpAvgRscp.HeaderText = "小区PCCPCH RSCP平均";
            this.colCellPccpAvgRscp.Name = "colCellPccpAvgRscp";
            this.colCellPccpAvgRscp.ReadOnly = true;
            // 
            // colCellDpchAvgRscp
            // 
            this.colCellDpchAvgRscp.HeaderText = "小区DPCH RSCP平均";
            this.colCellDpchAvgRscp.Name = "colCellDpchAvgRscp";
            this.colCellDpchAvgRscp.ReadOnly = true;
            // 
            // colCellDpchAvgC2I
            // 
            this.colCellDpchAvgC2I.HeaderText = "小区DPCH C/I平均";
            this.colCellDpchAvgC2I.Name = "colCellDpchAvgC2I";
            this.colCellDpchAvgC2I.ReadOnly = true;
            // 
            // colCellAvgTdBler
            // 
            this.colCellAvgTdBler.HeaderText = "小区TD_BLER平均";
            this.colCellAvgTdBler.Name = "colCellAvgTdBler";
            this.colCellAvgTdBler.ReadOnly = true;
            // 
            // colCellAvgSampleDistance
            // 
            this.colCellAvgSampleDistance.HeaderText = "小区平均通信距离";
            this.colCellAvgSampleDistance.Name = "colCellAvgSampleDistance";
            this.colCellAvgSampleDistance.ReadOnly = true;
            // 
            // colCellOverNum
            // 
            this.colCellOverNum.HeaderText = "小区过覆盖指数";
            this.colCellOverNum.Name = "colCellOverNum";
            this.colCellOverNum.ReadOnly = true;
            // 
            // colCellCellNum
            // 
            this.colCellCellNum.HeaderText = "小区区域站点密集指数";
            this.colCellCellNum.Name = "colCellCellNum";
            this.colCellCellNum.ReadOnly = true;
            // 
            // colCellAvgTA
            // 
            this.colCellAvgTA.HeaderText = "小区平均TA";
            this.colCellAvgTA.Name = "colCellAvgTA";
            this.colCellAvgTA.ReadOnly = true;
            // 
            // antRadioTabPage
            // 
            this.antRadioTabPage.Controls.Add(this.groupControl2);
            this.antRadioTabPage.Controls.Add(this.groupControl1);
            this.antRadioTabPage.Name = "antRadioTabPage";
            this.antRadioTabPage.Size = new System.Drawing.Size(1159, 616);
            this.antRadioTabPage.Text = "天线角度级采样数据分析";
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.dataGridViewAngle);
            this.groupControl2.Location = new System.Drawing.Point(10, 390);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(1142, 218);
            this.groupControl2.TabIndex = 1;
            this.groupControl2.Text = "天线角度";
            // 
            // dataGridViewAngle
            // 
            this.dataGridViewAngle.AllowUserToAddRows = false;
            this.dataGridViewAngle.AllowUserToDeleteRows = false;
            this.dataGridViewAngle.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewAngle.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.cluCellName,
            this.cluTarget});
            this.dataGridViewAngle.Location = new System.Drawing.Point(12, 32);
            this.dataGridViewAngle.Name = "dataGridViewAngle";
            this.dataGridViewAngle.RowTemplate.Height = 23;
            this.dataGridViewAngle.Size = new System.Drawing.Size(1121, 181);
            this.dataGridViewAngle.TabIndex = 0;
            // 
            // cluCellName
            // 
            this.cluCellName.HeaderText = "小区名称";
            this.cluCellName.Name = "cluCellName";
            this.cluCellName.Width = 150;
            // 
            // cluTarget
            // 
            this.cluTarget.HeaderText = "指标项";
            this.cluTarget.Name = "cluTarget";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.groupBox2);
            this.groupControl1.Controls.Add(this.groupBox1);
            this.groupControl1.Location = new System.Drawing.Point(10, 5);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(1136, 358);
            this.groupControl1.TabIndex = 0;
            this.groupControl1.Text = "图表";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.panel1);
            this.groupBox2.Controls.Add(this.chartControl1);
            this.groupBox2.Location = new System.Drawing.Point(10, 75);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(1119, 265);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            // 
            // panel1
            // 
            this.panel1.Location = new System.Drawing.Point(1082, 21);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(33, 223);
            this.panel1.TabIndex = 3;
            // 
            // chartControl1
            // 
            xyDiagram1.AxisX.MinorCount = 1;
            xyDiagram1.AxisX.Range.Auto = false;
            xyDiagram1.AxisX.Range.MaxValueInternal = 3.4999999999999991D;
            xyDiagram1.AxisX.Range.MinValueInternal = -0.5D;
            xyDiagram1.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisX.Tickmarks.MinorLength = 1;
            xyDiagram1.AxisX.Title.Alignment = System.Drawing.StringAlignment.Near;
            xyDiagram1.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram1.AxisY.Color = System.Drawing.Color.DarkOrange;
            xyDiagram1.AxisY.Interlaced = true;
            xyDiagram1.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram1.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram1.AxisY.Thickness = 2;
            xyDiagram1.AxisY.VisibleInPanesSerializable = "-1";
            xyDiagram1.DefaultPane.BackColor = System.Drawing.Color.Transparent;
            xyDiagram1.EnableAxisXScrolling = true;
            xyDiagram1.EnableAxisXZooming = true;
            xyDiagram1.EnableAxisYScrolling = true;
            xyDiagram1.Margins.Bottom = 1;
            xyDiagram1.Margins.Left = 1;
            xyDiagram1.Margins.Right = 1;
            xyDiagram1.Margins.Top = 1;
            xyDiagram1.PaneDistance = 5;
            secondaryAxisY1.AxisID = 0;
            secondaryAxisY1.Color = System.Drawing.Color.Blue;
            secondaryAxisY1.GridLines.Color = System.Drawing.Color.White;
            secondaryAxisY1.Interlaced = true;
            secondaryAxisY1.Name = "Secondary AxisY 1";
            secondaryAxisY1.Range.Auto = false;
            secondaryAxisY1.Range.MaxValueSerializable = "1";
            secondaryAxisY1.Range.MinValueSerializable = "0";
            secondaryAxisY1.Range.ScrollingRange.SideMarginsEnabled = true;
            secondaryAxisY1.Range.SideMarginsEnabled = true;
            secondaryAxisY1.Thickness = 2;
            secondaryAxisY1.VisibleInPanesSerializable = "-1";
            secondaryAxisY2.AxisID = 1;
            secondaryAxisY2.Name = "Secondary AxisY 2";
            secondaryAxisY2.Range.ScrollingRange.SideMarginsEnabled = true;
            secondaryAxisY2.Range.SideMarginsEnabled = true;
            secondaryAxisY2.VisibleInPanesSerializable = "-1";
            xyDiagram1.SecondaryAxesY.AddRange(new DevExpress.XtraCharts.SecondaryAxisY[] {
            secondaryAxisY1,
            secondaryAxisY2});
            this.chartControl1.Diagram = xyDiagram1;
            this.chartControl1.Location = new System.Drawing.Point(9, 21);
            this.chartControl1.Name = "chartControl1";
            this.chartControl1.RefreshDataOnRepaint = false;
            this.chartControl1.RuntimeHitTesting = false;
            sideBySideBarSeriesLabel1.LineVisible = false;
            sideBySideBarSeriesLabel1.Visible = false;
            series1.Label = sideBySideBarSeriesLabel1;
            series1.Name = "Series 3";
            series1.ShowInLegend = false;
            sideBySideBarSeriesView1.AxisYName = "Secondary AxisY 2";
            sideBySideBarSeriesView1.BarWidth = 1D;
            sideBySideBarSeriesView1.Color = System.Drawing.Color.Red;
            series1.View = sideBySideBarSeriesView1;
            this.chartControl1.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series1};
            sideBySideBarSeriesLabel2.LineVisible = true;
            this.chartControl1.SeriesTemplate.Label = sideBySideBarSeriesLabel2;
            this.chartControl1.SideBySideBarDistanceVariable = 0.01D;
            this.chartControl1.Size = new System.Drawing.Size(1104, 223);
            this.chartControl1.TabIndex = 2;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.cbbxSeries2);
            this.groupBox1.Controls.Add(this.cbbxSeries1);
            this.groupBox1.Controls.Add(this.labelControl2);
            this.groupBox1.Controls.Add(this.labelControl1);
            this.groupBox1.Location = new System.Drawing.Point(10, 29);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(1121, 50);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "图例";
            // 
            // cbbxSeries2
            // 
            this.cbbxSeries2.FormattingEnabled = true;
            this.cbbxSeries2.Location = new System.Drawing.Point(365, 18);
            this.cbbxSeries2.Name = "cbbxSeries2";
            this.cbbxSeries2.Size = new System.Drawing.Size(112, 22);
            this.cbbxSeries2.TabIndex = 3;
            this.cbbxSeries2.SelectedIndexChanged += new System.EventHandler(this.cbbxSeries2_SelectedIndexChanged);
            // 
            // cbbxSeries1
            // 
            this.cbbxSeries1.FormattingEnabled = true;
            this.cbbxSeries1.Location = new System.Drawing.Point(133, 18);
            this.cbbxSeries1.Name = "cbbxSeries1";
            this.cbbxSeries1.Size = new System.Drawing.Size(116, 22);
            this.cbbxSeries1.TabIndex = 2;
            this.cbbxSeries1.SelectedIndexChanged += new System.EventHandler(this.cbbxSeries1_SelectedIndexChanged);
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(299, 21);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(60, 14);
            this.labelControl2.TabIndex = 1;
            this.labelControl2.Text = "折线图指标";
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(67, 21);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(60, 14);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "柱形图指标";
            // 
            // chartTabPage
            // 
            this.chartTabPage.Controls.Add(this.groupControl3);
            this.chartTabPage.Name = "chartTabPage";
            this.chartTabPage.Size = new System.Drawing.Size(1159, 616);
            this.chartTabPage.Text = "天线辐射波形重构";
            // 
            // groupControl3
            // 
            this.groupControl3.Controls.Add(this.groupBox6);
            this.groupControl3.Controls.Add(this.groupBox4);
            this.groupControl3.Controls.Add(this.groupBox3);
            this.groupControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl3.Location = new System.Drawing.Point(0, 0);
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(1159, 616);
            this.groupControl3.TabIndex = 1;
            this.groupControl3.Text = "全向分析";
            // 
            // groupBox6
            // 
            this.groupBox6.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox6.Controls.Add(this.chartRSCP);
            this.groupBox6.Location = new System.Drawing.Point(605, 26);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Size = new System.Drawing.Size(547, 272);
            this.groupBox6.TabIndex = 2;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "PCCPCH RSCP曲线图";
            // 
            // chartRSCP
            // 
            xyDiagram2.AxisX.Range.Auto = false;
            xyDiagram2.AxisX.Range.MaxValueSerializable = "-75";
            xyDiagram2.AxisX.Range.MinValueSerializable = "-140";
            xyDiagram2.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram2.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram2.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram2.AxisY.GridSpacing = 10D;
            xyDiagram2.AxisY.GridSpacingAuto = false;
            xyDiagram2.AxisY.Range.Auto = false;
            xyDiagram2.AxisY.Range.MaxValueSerializable = "100";
            xyDiagram2.AxisY.Range.MinValueSerializable = "0";
            xyDiagram2.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram2.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram2.AxisY.VisibleInPanesSerializable = "-1";
            secondaryAxisY3.AxisID = 0;
            secondaryAxisY3.GridSpacing = 2D;
            secondaryAxisY3.GridSpacingAuto = false;
            secondaryAxisY3.Name = "Rsrp0_PDF_Y";
            secondaryAxisY3.Range.Auto = false;
            secondaryAxisY3.Range.MaxValueSerializable = "25";
            secondaryAxisY3.Range.MinValueSerializable = "0";
            secondaryAxisY3.Range.ScrollingRange.SideMarginsEnabled = true;
            secondaryAxisY3.Range.SideMarginsEnabled = true;
            secondaryAxisY3.VisibleInPanesSerializable = "-1";
            xyDiagram2.SecondaryAxesY.AddRange(new DevExpress.XtraCharts.SecondaryAxisY[] {
            secondaryAxisY3});
            this.chartRSCP.Diagram = xyDiagram2;
            this.chartRSCP.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartRSCP.Location = new System.Drawing.Point(3, 18);
            this.chartRSCP.Name = "chartRSCP";
            pointSeriesLabel1.LineVisible = true;
            series2.Label = pointSeriesLabel1;
            series2.Name = "CDF";
            series2.View = splineAreaSeriesView1;
            pointSeriesLabel2.LineVisible = true;
            series3.Label = pointSeriesLabel2;
            series3.Name = "PDF";
            series3.View = splineSeriesView1;
            this.chartRSCP.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series2,
        series3};
            pointSeriesLabel3.LineVisible = true;
            this.chartRSCP.SeriesTemplate.Label = pointSeriesLabel3;
            splineAreaSeriesView2.Transparency = ((byte)(0));
            this.chartRSCP.SeriesTemplate.View = splineAreaSeriesView2;
            this.chartRSCP.Size = new System.Drawing.Size(541, 251);
            this.chartRSCP.TabIndex = 1;
            // 
            // groupBox4
            // 
            this.groupBox4.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox4.Controls.Add(this.chartC2I);
            this.groupBox4.Location = new System.Drawing.Point(599, 304);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(553, 305);
            this.groupBox4.TabIndex = 1;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "PCCPCH C/I曲线图";
            // 
            // chartC2I
            // 
            xyDiagram3.AxisX.Range.Auto = false;
            xyDiagram3.AxisX.Range.MaxValueSerializable = "25";
            xyDiagram3.AxisX.Range.MinValueSerializable = "-10";
            xyDiagram3.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram3.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram3.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram3.AxisY.GridSpacing = 10D;
            xyDiagram3.AxisY.GridSpacingAuto = false;
            xyDiagram3.AxisY.Range.Auto = false;
            xyDiagram3.AxisY.Range.MaxValueSerializable = "100";
            xyDiagram3.AxisY.Range.MinValueSerializable = "0";
            xyDiagram3.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram3.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram3.AxisY.VisibleInPanesSerializable = "-1";
            secondaryAxisY4.AxisID = 0;
            secondaryAxisY4.GridSpacing = 2D;
            secondaryAxisY4.GridSpacingAuto = false;
            secondaryAxisY4.Name = "Sinr0_PDF_Y";
            secondaryAxisY4.Range.Auto = false;
            secondaryAxisY4.Range.MaxValueSerializable = "25";
            secondaryAxisY4.Range.MinValueSerializable = "0";
            secondaryAxisY4.Range.ScrollingRange.SideMarginsEnabled = true;
            secondaryAxisY4.Range.SideMarginsEnabled = true;
            secondaryAxisY4.VisibleInPanesSerializable = "-1";
            xyDiagram3.SecondaryAxesY.AddRange(new DevExpress.XtraCharts.SecondaryAxisY[] {
            secondaryAxisY4});
            this.chartC2I.Diagram = xyDiagram3;
            this.chartC2I.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartC2I.Location = new System.Drawing.Point(3, 18);
            this.chartC2I.Name = "chartC2I";
            pointSeriesLabel4.LineVisible = true;
            series4.Label = pointSeriesLabel4;
            series4.Name = "CDF";
            splineAreaSeriesView3.MarkerOptions.Size = 2;
            series4.View = splineAreaSeriesView3;
            pointSeriesLabel5.LineVisible = true;
            series5.Label = pointSeriesLabel5;
            series5.Name = "PDF";
            series5.View = splineSeriesView2;
            this.chartC2I.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series4,
        series5};
            pointSeriesLabel6.LineVisible = true;
            this.chartC2I.SeriesTemplate.Label = pointSeriesLabel6;
            splineAreaSeriesView4.Transparency = ((byte)(0));
            this.chartC2I.SeriesTemplate.View = splineAreaSeriesView4;
            this.chartC2I.Size = new System.Drawing.Size(547, 284);
            this.chartC2I.TabIndex = 2;
            // 
            // groupBox3
            // 
            this.groupBox3.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.groupBox3.Controls.Add(this.chartControl2);
            this.groupBox3.Location = new System.Drawing.Point(10, 26);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(583, 583);
            this.groupBox3.TabIndex = 0;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "俯视面辐射图";
            // 
            // chartControl2
            // 
            this.chartControl2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.chartControl2.BorderOptions.Color = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            radarDiagram1.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram1.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            radarDiagram1.AxisY.Range.SideMarginsEnabled = true;
            radarDiagram1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.chartControl2.Diagram = radarDiagram1;
            this.chartControl2.Location = new System.Drawing.Point(6, 20);
            this.chartControl2.Name = "chartControl2";
            radarPointSeriesLabel1.LineVisible = true;
            series6.Label = radarPointSeriesLabel1;
            series6.Name = "AntSeries";
            series6.Points.AddRange(new DevExpress.XtraCharts.SeriesPoint[] {
            seriesPoint1,
            seriesPoint2,
            seriesPoint3,
            seriesPoint4,
            seriesPoint5});
            series6.ShowInLegend = false;
            series6.View = radarLineSeriesView1;
            radarPointSeriesLabel2.LineVisible = true;
            series7.Label = radarPointSeriesLabel2;
            series7.Name = "StandardSeries";
            series7.ShowInLegend = false;
            series7.View = radarLineSeriesView2;
            this.chartControl2.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series6,
        series7};
            radarPointSeriesLabel3.LineVisible = true;
            this.chartControl2.SeriesTemplate.Label = radarPointSeriesLabel3;
            this.chartControl2.SeriesTemplate.View = radarLineSeriesView3;
            this.chartControl2.Size = new System.Drawing.Size(571, 556);
            this.chartControl2.TabIndex = 0;
            // 
            // probStatTabPage
            // 
            this.probStatTabPage.Controls.Add(this.dataGridViewAbnormal);
            this.probStatTabPage.Name = "probStatTabPage";
            this.probStatTabPage.Size = new System.Drawing.Size(1159, 616);
            this.probStatTabPage.Text = "天线故障及覆盖异常(按网格)";
            // 
            // dataGridViewAbnormal
            // 
            this.dataGridViewAbnormal.AllowUserToAddRows = false;
            this.dataGridViewAbnormal.AllowUserToDeleteRows = false;
            this.dataGridViewAbnormal.AllowUserToOrderColumns = true;
            this.dataGridViewAbnormal.BackgroundColor = System.Drawing.SystemColors.Window;
            this.dataGridViewAbnormal.ColumnHeadersHeight = 50;
            this.dataGridViewAbnormal.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.dataGridViewTextBoxColumn28,
            this.colRegionname,
            this.Column1,
            this.Column2,
            this.Column3,
            this.Column4,
            this.Column5,
            this.Column6,
            this.Column7,
            this.Column8,
            this.Column9});
            this.dataGridViewAbnormal.ContextMenuStrip = this.contextMenuStrip;
            this.dataGridViewAbnormal.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridViewAbnormal.Location = new System.Drawing.Point(0, 0);
            this.dataGridViewAbnormal.Name = "dataGridViewAbnormal";
            this.dataGridViewAbnormal.RowTemplate.Height = 23;
            this.dataGridViewAbnormal.Size = new System.Drawing.Size(1159, 616);
            this.dataGridViewAbnormal.TabIndex = 1;
            // 
            // dataGridViewTextBoxColumn28
            // 
            this.dataGridViewTextBoxColumn28.HeaderText = "地市名称";
            this.dataGridViewTextBoxColumn28.Name = "dataGridViewTextBoxColumn28";
            // 
            // colRegionname
            // 
            this.colRegionname.HeaderText = "网格号";
            this.colRegionname.Name = "colRegionname";
            // 
            // Column1
            // 
            this.Column1.HeaderText = "主瓣弱覆盖（增益故障）问题";
            this.Column1.Name = "Column1";
            this.Column1.Width = 120;
            // 
            // Column2
            // 
            this.Column2.HeaderText = "后瓣强度异常（前后比故障）";
            this.Column2.Name = "Column2";
            this.Column2.Width = 120;
            // 
            // Column3
            // 
            this.Column3.HeaderText = "旁瓣信号泄露（旁瓣变形）";
            this.Column3.Name = "Column3";
            this.Column3.Width = 120;
            // 
            // Column4
            // 
            this.Column4.HeaderText = "后瓣过覆盖";
            this.Column4.Name = "Column4";
            // 
            // Column5
            // 
            this.Column5.HeaderText = "旁瓣过覆盖";
            this.Column5.Name = "Column5";
            // 
            // Column6
            // 
            this.Column6.HeaderText = "主瓣过覆盖";
            this.Column6.Name = "Column6";
            // 
            // Column7
            // 
            this.Column7.HeaderText = "下行质差小区（PCCHC2I <= -5,>=2%）";
            this.Column7.Name = "Column7";
            this.Column7.Width = 120;
            // 
            // Column8
            // 
            this.Column8.HeaderText = "下行质差区间（质差 <= -3,采样点比例>=10）";
            this.Column8.Name = "Column8";
            this.Column8.Width = 120;
            // 
            // Column9
            // 
            this.Column9.HeaderText = "过覆盖区间（过覆盖>=4,采样点比例>=10）";
            this.Column9.Name = "Column9";
            this.Column9.Width = 120;
            // 
            // TdAntennaForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1166, 646);
            this.Controls.Add(this.xTabPageData);
            this.Name = "TdAntennaForm";
            this.Text = "天线区间统计结果";
            ((System.ComponentModel.ISupportInitialize)(this.xTabPageData)).EndInit();
            this.xTabPageData.ResumeLayout(false);
            this.cellTabPage.ResumeLayout(false);
            this.cellTabPage.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewCell)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.btsTabPage.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewBts)).EndInit();
            this.sectionTabPage.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).EndInit();
            this.antRadioTabPage.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewAngle)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl1)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.chartTabPage.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            this.groupBox6.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(splineSeriesView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartRSCP)).EndInit();
            this.groupBox4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(secondaryAxisY4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(splineSeriesView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(splineAreaSeriesView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartC2I)).EndInit();
            this.groupBox3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(radarDiagram1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarPointSeriesLabel3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(radarLineSeriesView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl2)).EndInit();
            this.probStatTabPage.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewAbnormal)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl xTabPageData;
        private DevExpress.XtraTab.XtraTabPage sectionTabPage;
        private DevExpress.XtraTab.XtraTabPage antRadioTabPage;
        private System.Windows.Forms.DataGridView dataGridView;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miShwoChart;
        private System.Windows.Forms.ToolStripMenuItem miShowGis;
        private System.Windows.Forms.ToolStripMenuItem miShowSimulation;
        private System.Windows.Forms.ToolStripMenuItem miExportWholeExcel;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.ComboBox cbbxSeries2;
        private System.Windows.Forms.ComboBox cbbxSeries1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private System.Windows.Forms.DataGridView dataGridViewAngle;
        private System.Windows.Forms.DataGridViewTextBoxColumn cluCellName;
        private System.Windows.Forms.DataGridViewTextBoxColumn cluTarget;
        private DevExpress.XtraCharts.ChartControl chartControl1;
        private System.Windows.Forms.Panel panel1;
        private DevExpress.XtraTab.XtraTabPage probStatTabPage;
        private System.Windows.Forms.DataGridView dataGridViewAbnormal;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn28;
        private System.Windows.Forms.DataGridViewTextBoxColumn colRegionname;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column1;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column2;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column3;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column4;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column5;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column6;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column7;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column8;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column9;
        private DevExpress.XtraTab.XtraTabPage cellTabPage;
        private System.Windows.Forms.DataGridView dataGridViewCell;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column10;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column11;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column12;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column13;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column14;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column15;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column16;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column17;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column18;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column19;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column20;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column21;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column22;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column23;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column24;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellname;
        private System.Windows.Forms.DataGridViewTextBoxColumn colBcch;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column25;
        private System.Windows.Forms.DataGridViewTextBoxColumn coldMaxPcc0_30_150_180;
        private System.Windows.Forms.DataGridViewTextBoxColumn colSection;
        private System.Windows.Forms.DataGridViewTextBoxColumn colRxlevSampleNum;
        private System.Windows.Forms.DataGridViewTextBoxColumn colSamplePect;
        private System.Windows.Forms.DataGridViewTextBoxColumn colAvgPccpch;
        private System.Windows.Forms.DataGridViewTextBoxColumn colMaxPccpch;
        private System.Windows.Forms.DataGridViewTextBoxColumn colDpchAvgRscp;
        private System.Windows.Forms.DataGridViewTextBoxColumn colDpchAvgC2I;
        private System.Windows.Forms.DataGridViewTextBoxColumn colAvgTdBler;
        private System.Windows.Forms.DataGridViewTextBoxColumn colAvgTA;
        private System.Windows.Forms.DataGridViewTextBoxColumn colAvgSampleDistance;
        private System.Windows.Forms.DataGridViewTextBoxColumn colAvgCellNum;
        private System.Windows.Forms.DataGridViewTextBoxColumn colSampleTotal;
        private System.Windows.Forms.DataGridViewTextBoxColumn colAnaType;
        private System.Windows.Forms.DataGridViewTextBoxColumn colIangle_ob;
        private System.Windows.Forms.DataGridViewTextBoxColumn colIaltitude;
        private System.Windows.Forms.DataGridViewTextBoxColumn colIangle_dir;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellPccpAvgRscp;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellDpchAvgRscp;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellDpchAvgC2I;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellAvgTdBler;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellAvgSampleDistance;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellOverNum;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellCellNum;
        private System.Windows.Forms.DataGridViewTextBoxColumn colCellAvgTA;
        private System.Windows.Forms.Button btnNextpage;
        private System.Windows.Forms.Button btnPrevpage;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox txtPage;
        private System.Windows.Forms.Label labPage;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Button btnGo;
        private System.Windows.Forms.Label labNum;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button btnSearch;
        private System.Windows.Forms.TextBox txtCellName;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.ToolStripMenuItem 拆分导出CSVToolStripMenuItem;
        private DevExpress.XtraTab.XtraTabPage chartTabPage;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private System.Windows.Forms.GroupBox groupBox6;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.GroupBox groupBox3;
        private DevExpress.XtraCharts.ChartControl chartControl2;
        private DevExpress.XtraCharts.ChartControl chartRSCP;
        private DevExpress.XtraCharts.ChartControl chartC2I;
        private DevExpress.XtraTab.XtraTabPage btsTabPage;
        private System.Windows.Forms.DataGridView dataGridViewBts;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn29;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn30;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn31;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn32;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn33;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn36;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn1;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column26;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column28;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column29;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column30;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column31;
        private System.Windows.Forms.DataGridViewTextBoxColumn Column32;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn40;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn41;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn42;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn43;
    }
}