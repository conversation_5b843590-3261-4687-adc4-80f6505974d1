﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class VolteCallEndDelayAnaBase : DIYAnalyseByFileBackgroundBase
    {
        protected static readonly object lockObj = new object();

        float minDelaySeconds = 2.0f;
        readonly List<CallEndDelayInfo> callEndDelayInfoList = new List<CallEndDelayInfo>();

        protected VolteCallEndDelayAnaBase()
            : base(MainModel.GetInstance())
        {
            this.IncludeEvent = true;
            this.Columns = new List<string>();
            Columns.Add("lte_RSRP");
            Columns.Add("lte_SINR");
            Columns.Add("lte_TAC");
            Columns.Add("lte_ECI");
            Columns.Add("lte_EARFCN");
            Columns.Add("lte_PCI");
            Columns.Add("lte_Times_QAM16_UL");
            Columns.Add("lte_Times_QAM64_UL");
            Columns.Add("lte_Times_QPSK_UL");
        }
        public override string Name
        {
            get { return "挂机时延分析"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 27000, 27017, this.Name);
        }
        protected override void clearDataBeforeAnalyseFiles()
        {
            callEndDelayInfoList.Clear();
        }
        protected override bool getCondition()
        {
            CallEndConditionDlg dlg = new CallEndConditionDlg();
            dlg.SetCondition(minDelaySeconds);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            dlg.GetCondition(out minDelaySeconds);
            return true;
        }
        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
                {
                    CallEndDelayInfo info = null;
                    foreach (Event evt in file.Events)
                    {
                        if (WaitBox.CancelRequest)
                        {
                            return;
                        }

                        if (evt.ID == (int)EnumEvt.VoLteMOCallEnd || evt.ID == (int)EnumEvt.VoLTEVideoMOCallEnd
                            || evt.ID == (int)EnumEvt.VoLteMTCallEnd || evt.ID == (int)EnumEvt.VoLTEVideoMTCallEnd)
                        {
                            int milSec = int.Parse(evt["Value1"].ToString());
                            float timeDelay = milSec / 1000.0f;
                            if (timeDelay >= minDelaySeconds)
                            {
                                info = new CallEndDelayInfo(evt);
                                info.DelaySeconds = timeDelay;
                                getBeforeAndAfterInfo(info, file.TestPoints);

                                info.SN = callEndDelayInfoList.Count + 1;
                                callEndDelayInfoList.Add(info);
                            }
                        }
                    }
                }
            }
            catch (Exception ee)
            {
                MessageBox.Show(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
            }
        }

        private void getBeforeAndAfterInfo(CallEndDelayInfo item, List<TestPoint> fileTestPoints)
        {
            int endTpIndex = getChangeTestpointIndex(fileTestPoints, item.EvtCallEnd);
            for (int i = endTpIndex; i >= 0; i--)
            {
                TestPoint tp = fileTestPoints[i];
                double duration = (item.EvtCallEnd.DateTime - tp.DateTime).TotalSeconds;
                if (duration < 5 + item.DelaySeconds && duration >= item.DelaySeconds)//挂机前5秒
                {
                    item.TestPointBefore.Add(tp);
                }
                else if (duration < item.DelaySeconds && duration >= 0)//挂机后
                {
                    item.TestPointAfter.Add(tp);
                }
                else
                {
                    break;
                }
            }
            item.StatBeforeAndAfterInfo();
        }
        private static int getChangeTestpointIndex(List<TestPoint> tpList, Event e)
        {
            int index = -1;
            for (int i = 0; i < tpList.Count; i++)
            {
                if (tpList[i].SN > e.SN)
                {
                    index = i - 1;
                    break;
                }
                if (tpList[i].SN == e.SN)
                {
                    index = i;
                    break;
                }
            }

            return index;
        }

        protected override void fireShowForm()
        {
            if (callEndDelayInfoList.Count == 0)
            {
                System.Windows.Forms.MessageBox.Show("没有符合条件的信息！");
                return;
            }
            VolteCallEndDelayInfoForm frm = MainModel.CreateResultForm(typeof(VolteCallEndDelayInfoForm)) as VolteCallEndDelayInfoForm;
            frm.FillData(callEndDelayInfoList);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
            mainModel.FireSetDefaultMapSerialTheme("lte_RSRP");
        }
        protected enum EnumEvt
        {
            VoLteMOCallEnd = 1076,
            VoLteMTCallEnd = 1077,
            VoLTEVideoMOCallEnd = 1376,
            VoLTEVideoMTCallEnd = 1377,
        }

        //protected enum EnumMsg
        //{
        //    IMS_SIP_BYE = 0x42020000,
        //    IMS_SIP_BYE_OK = 0x420240C8,
        //}

    }

    public class VolteCallEndDelayAnaByRegion : VolteCallEndDelayAnaBase
    {
        private static VolteCallEndDelayAnaByRegion instance = null;
        public static VolteCallEndDelayAnaByRegion GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new VolteCallEndDelayAnaByRegion();
                    }
                }
            }
            return instance;
        }

        protected VolteCallEndDelayAnaByRegion()
            : base()
        {
            this.FilterSampleByRegion = true;
        }
        public override string Name
        {
            get { return "挂机时延分析(按区域)"; }
        }
    }
    public class VolteCallEndDelayAnaByFile : VolteCallEndDelayAnaBase
    {
        protected VolteCallEndDelayAnaByFile()
            : base()
        {
        }

        private static VolteCallEndDelayAnaByFile intance = null;
        public static VolteCallEndDelayAnaByFile GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new VolteCallEndDelayAnaByFile();
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "挂机时延分析(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

    }
}
