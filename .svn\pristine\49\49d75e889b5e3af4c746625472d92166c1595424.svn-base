﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc.ZTNRCheckCellOccupation;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRCheckCellOccupationDlg : BaseDialog
    {
        public NRCheckCellOccupationDlg()
        {
            InitializeComponent();
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }

        public void GetCondition(ref NRCheckCellOccupationCond condition)
        {
            condition.rsrp = (int)spinEditRSRP.Value;
            condition.radius = (int)spinEditRadius.Value;
            condition.rxlev = (int)spinEditRxlev.Value;
            condition.btsCount = (int)spinEditBTSCount.Value;
            condition.radiusFactor = (double)spinEditRadiusFactor.Value;
        }

        public void SetCondition(NRCheckCellOccupationCond condition)
        {
            this.spinEditRSRP.Value = (decimal)condition.rsrp;
            this.spinEditRadius.Value = (decimal)condition.radius;
            this.spinEditRxlev.Value = (decimal)condition.rxlev;
            this.spinEditBTSCount.Value = (decimal)condition.btsCount;
            this.spinEditRadiusFactor.Value = (decimal)condition.radiusFactor;
        }
    }
}
