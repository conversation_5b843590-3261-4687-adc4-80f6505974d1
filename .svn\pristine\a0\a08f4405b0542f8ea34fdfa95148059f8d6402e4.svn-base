﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYCellWeakCoverByCellDir_W : ZTDIYCellWeakCoverByCellDir_GScan
    {
        public ZTDIYCellWeakCoverByCellDir_W()
            : base()
        {
        }

        public override string Name
        {
            get { return "弱覆盖小区"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 14000, 14002, "查询");
        }

        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();

            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "W_TotalRSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)"WCDMA");
            tmpDic.Add("themeName", (object)"WCDMA_RSCP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override float? getBestRxLev(TestPoint tp)
        {
            if (tp is WCDMATestPointDetail)
            {
                float? rxLev = (float?)tp["W_TotalRSCP"];
                if (rxLev == null || rxLev < -140 || rxLev > 10)
                {
                    return null;
                }
                return rxLev;
            }
            return null;
        }

        protected override void getWeakCellDic(List<GridForCellWeakCover> weakGridList, Dictionary<ICell, CellWeakCoverByGridInfoBase> weakCellDic,
            double lonDiff, double latDiff)
        {
            List<WCell> cellList = getWCellsOfRegion();
            int iLoop = 1;
            WaitBox.Text = "开始获取弱覆盖小区...";
            foreach (WCell cell in cellList)
            {
                dealWeakCellByWeakGrid(weakGridList, weakCellDic, lonDiff, latDiff, cell);
                WaitBox.ProgressPercent = (int)(100.0 * iLoop++ / cellList.Count);
            }
        }

        protected override CellWeakCoverByGridInfoBase getCellWeakCoverByGrid(ICell cell)
        {
            if (cell is WCell)
            {
                WCell wCell = cell as WCell;
                CellWeakCoverByGridInfoWcdma weakCell = new CellWeakCoverByGridInfoWcdma();
                weakCell.FillCellData(wCell);
                //weakCell.Calculate();
                return weakCell;
            }
            return null;
        }

        private List<WCell> getWCellsOfRegion()
        {
            List<WCell> cellList = new List<WCell>();
            int index = 1;
            List<WCell> curCells = MainModel.CellManager.GetCurrentWCells();
            WaitBox.Text = "开始获取选择区域内小区...";
            foreach (WCell cell in curCells)
            {
                if (cell.Type == WNodeBType.Outdoor && condition.Geometorys.GeoOp.CheckPointInRegion(cell.Longitude, cell.Latitude))
                {
                    cellList.Add(cell);
                }
                WaitBox.ProgressPercent = (int)(100.0 * index++ / curCells.Count);
            }
            return cellList;
        }
    }
}