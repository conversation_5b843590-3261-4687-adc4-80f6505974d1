﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTTDLTECellCoverLapAnaListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.gridCtrlAll = new DevExpress.XtraGrid.GridControl();
            this.ctxMenuAll = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miMove2GridRemoved = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem3 = new System.Windows.Forms.ToolStripSeparator();
            this.miShowAllInGis = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripSeparator();
            this.miExp2XlsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.viewAll = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.gridCtrlRemoved = new DevExpress.XtraGrid.GridControl();
            this.ctxMenuRemoved = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miMove2AllGrid = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miShowRemovedInGis = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem5 = new System.Windows.Forms.ToolStripSeparator();
            this.miExp2XlsRemoved = new System.Windows.Forms.ToolStripMenuItem();
            this.viewRemoved = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlAll)).BeginInit();
            this.ctxMenuAll.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.viewAll)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlRemoved)).BeginInit();
            this.ctxMenuRemoved.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.viewRemoved)).BeginInit();
            this.SuspendLayout();
            // 
            // gridCtrlAll
            // 
            this.gridCtrlAll.AllowDrop = true;
            this.gridCtrlAll.ContextMenuStrip = this.ctxMenuAll;
            this.gridCtrlAll.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridCtrlAll.Location = new System.Drawing.Point(0, 0);
            this.gridCtrlAll.MainView = this.viewAll;
            this.gridCtrlAll.Name = "gridCtrlAll";
            this.gridCtrlAll.Size = new System.Drawing.Size(1228, 356);
            this.gridCtrlAll.TabIndex = 0;
            this.gridCtrlAll.UseEmbeddedNavigator = true;
            this.gridCtrlAll.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.viewAll});
            this.gridCtrlAll.DragDrop += new System.Windows.Forms.DragEventHandler(this.gridCtrlAll_DragDrop);
            this.gridCtrlAll.DragEnter += new System.Windows.Forms.DragEventHandler(this.gridCtrlAll_DragEnter);
            this.gridCtrlAll.MouseDown += new System.Windows.Forms.MouseEventHandler(this.gridCtrlAll_MouseDown);
            this.gridCtrlAll.MouseMove += new System.Windows.Forms.MouseEventHandler(this.gridCtrlAll_MouseMove);
            // 
            // ctxMenuAll
            // 
            this.ctxMenuAll.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miMove2GridRemoved,
            this.toolStripMenuItem3,
            this.miShowAllInGis,
            this.toolStripMenuItem2,
            this.miExp2XlsAll});
            this.ctxMenuAll.Name = "ctxMenuAll";
            this.ctxMenuAll.Size = new System.Drawing.Size(161, 82);
            // 
            // miMove2GridRemoved
            // 
            this.miMove2GridRemoved.Name = "miMove2GridRemoved";
            this.miMove2GridRemoved.Size = new System.Drawing.Size(160, 22);
            this.miMove2GridRemoved.Text = "添加到闭站列表";
            this.miMove2GridRemoved.Click += new System.EventHandler(this.miMove2GridRemoved_Click);
            // 
            // toolStripMenuItem3
            // 
            this.toolStripMenuItem3.Name = "toolStripMenuItem3";
            this.toolStripMenuItem3.Size = new System.Drawing.Size(157, 6);
            // 
            // miShowAllInGis
            // 
            this.miShowAllInGis.Name = "miShowAllInGis";
            this.miShowAllInGis.Size = new System.Drawing.Size(160, 22);
            this.miShowAllInGis.Text = "列表GIS呈现";
            this.miShowAllInGis.Click += new System.EventHandler(this.miShowAllInGis_Click);
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            this.toolStripMenuItem2.Size = new System.Drawing.Size(157, 6);
            // 
            // miExp2XlsAll
            // 
            this.miExp2XlsAll.Name = "miExp2XlsAll";
            this.miExp2XlsAll.Size = new System.Drawing.Size(160, 22);
            this.miExp2XlsAll.Text = "导出Excel...";
            this.miExp2XlsAll.Click += new System.EventHandler(this.miExp2XlsAll_Click);
            // 
            // viewAll
            // 
            this.viewAll.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.viewAll.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.viewAll.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.viewAll.ColumnPanelRowHeight = 50;
            this.viewAll.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn3,
            this.gridColumn2,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn16,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15});
            this.viewAll.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.viewAll.GridControl = this.gridCtrlAll;
            this.viewAll.Name = "viewAll";
            this.viewAll.OptionsBehavior.Editable = false;
            this.viewAll.OptionsSelection.MultiSelect = true;
            this.viewAll.OptionsView.ColumnAutoWidth = false;
            this.viewAll.OptionsView.EnableAppearanceEvenRow = true;
            this.viewAll.OptionsView.ShowDetailButtons = false;
            this.viewAll.OptionsView.ShowGroupPanel = false;
            this.viewAll.DoubleClick += new System.EventHandler(this.view_DoubleClick);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "小区";
            this.gridColumn1.FieldName = "CellName";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 119;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "CellID";
            this.gridColumn3.FieldName = "CellID";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 1;
            this.gridColumn3.Width = 104;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "最近若干基站";
            this.gridColumn2.FieldName = "NearestBtsNames";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 2;
            this.gridColumn2.Width = 82;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "总采样点数";
            this.gridColumn4.FieldName = "TestPointNum";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            this.gridColumn4.Width = 56;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "过覆盖点数";
            this.gridColumn5.FieldName = "OverPointNum";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 4;
            this.gridColumn5.Width = 51;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "过覆盖比例(%)";
            this.gridColumn6.FieldName = "OverPercent";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 5;
            this.gridColumn6.Width = 56;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "挂高";
            this.gridColumn17.FieldName = "CellAltitude";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 6;
            this.gridColumn17.Width = 40;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "下倾角";
            this.gridColumn18.FieldName = "CellDownDir";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 7;
            this.gridColumn18.Width = 50;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "建议下倾角";
            this.gridColumn19.FieldName = "CellDownDirSuggest";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 8;
            this.gridColumn19.Width = 48;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "理想覆盖距离(米)";
            this.gridColumn16.FieldName = "IdealCoverDistance";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 9;
            this.gridColumn16.Width = 58;
            // 
            // gridColumn7
            // 
            this.gridColumn7.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gridColumn7.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gridColumn7.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn7.Caption = "过覆盖最小距离(米)";
            this.gridColumn7.FieldName = "OverDistanceMin";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 10;
            this.gridColumn7.Width = 55;
            // 
            // gridColumn8
            // 
            this.gridColumn8.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gridColumn8.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gridColumn8.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn8.Caption = "过覆盖最大距离(米)";
            this.gridColumn8.FieldName = "OverDistanceMax";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 11;
            this.gridColumn8.Width = 55;
            // 
            // gridColumn9
            // 
            this.gridColumn9.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gridColumn9.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gridColumn9.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn9.Caption = "过覆盖平均距离(米)";
            this.gridColumn9.FieldName = "OverDistanceAvg";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 12;
            this.gridColumn9.Width = 55;
            // 
            // gridColumn10
            // 
            this.gridColumn10.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gridColumn10.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gridColumn10.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn10.Caption = "过覆盖最小电平";
            this.gridColumn10.FieldName = "OverDianpingMin";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 13;
            this.gridColumn10.Width = 55;
            // 
            // gridColumn11
            // 
            this.gridColumn11.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gridColumn11.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gridColumn11.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn11.Caption = "过覆盖最大电平";
            this.gridColumn11.FieldName = "OverDianpingMax";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 14;
            this.gridColumn11.Width = 55;
            // 
            // gridColumn12
            // 
            this.gridColumn12.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gridColumn12.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gridColumn12.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn12.Caption = "过覆盖平均电平";
            this.gridColumn12.FieldName = "OverDianpingAvg";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 15;
            this.gridColumn12.Width = 55;
            // 
            // gridColumn13
            // 
            this.gridColumn13.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(192)))), ((int)(((byte)(128)))));
            this.gridColumn13.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(192)))), ((int)(((byte)(128)))));
            this.gridColumn13.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn13.Caption = "过覆盖最小质量";
            this.gridColumn13.FieldName = "OverZhiliangMin";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 16;
            this.gridColumn13.Width = 55;
            // 
            // gridColumn14
            // 
            this.gridColumn14.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(192)))), ((int)(((byte)(128)))));
            this.gridColumn14.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(192)))), ((int)(((byte)(128)))));
            this.gridColumn14.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn14.Caption = "过覆盖最大质量";
            this.gridColumn14.FieldName = "OverZhiliangMax";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 17;
            this.gridColumn14.Width = 55;
            // 
            // gridColumn15
            // 
            this.gridColumn15.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(192)))), ((int)(((byte)(128)))));
            this.gridColumn15.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(192)))), ((int)(((byte)(128)))));
            this.gridColumn15.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn15.Caption = "过覆盖平均质量";
            this.gridColumn15.FieldName = "OverZhiliangAvg";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 18;
            this.gridColumn15.Width = 55;
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.gridCtrlAll);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.groupControl1);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(1228, 640);
            this.splitContainerControl1.SplitterPosition = 356;
            this.splitContainerControl1.TabIndex = 1;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.gridCtrlRemoved);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(1228, 278);
            this.groupControl1.TabIndex = 0;
            this.groupControl1.Text = "模拟闭站";
            // 
            // gridCtrlRemoved
            // 
            this.gridCtrlRemoved.AllowDrop = true;
            this.gridCtrlRemoved.ContextMenuStrip = this.ctxMenuRemoved;
            this.gridCtrlRemoved.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridCtrlRemoved.Location = new System.Drawing.Point(2, 23);
            this.gridCtrlRemoved.MainView = this.viewRemoved;
            this.gridCtrlRemoved.Name = "gridCtrlRemoved";
            this.gridCtrlRemoved.Size = new System.Drawing.Size(1224, 253);
            this.gridCtrlRemoved.TabIndex = 1;
            this.gridCtrlRemoved.UseEmbeddedNavigator = true;
            this.gridCtrlRemoved.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.viewRemoved});
            this.gridCtrlRemoved.DragDrop += new System.Windows.Forms.DragEventHandler(this.gridCtrlRemoved_DragDrop);
            this.gridCtrlRemoved.DragEnter += new System.Windows.Forms.DragEventHandler(this.gridCtrlRemoved_DragEnter);
            this.gridCtrlRemoved.MouseDown += new System.Windows.Forms.MouseEventHandler(this.gridCtrlRemoved_MouseDown);
            this.gridCtrlRemoved.MouseMove += new System.Windows.Forms.MouseEventHandler(this.gridCtrlRemoved_MouseMove);
            // 
            // ctxMenuRemoved
            // 
            this.ctxMenuRemoved.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miMove2AllGrid,
            this.toolStripMenuItem1,
            this.miShowRemovedInGis,
            this.toolStripMenuItem5,
            this.miExp2XlsRemoved});
            this.ctxMenuRemoved.Name = "ctxMenuAll";
            this.ctxMenuRemoved.Size = new System.Drawing.Size(197, 82);
            // 
            // miMove2AllGrid
            // 
            this.miMove2AllGrid.Name = "miMove2AllGrid";
            this.miMove2AllGrid.Size = new System.Drawing.Size(196, 22);
            this.miMove2AllGrid.Text = "还原到原始过覆盖列表";
            this.miMove2AllGrid.Click += new System.EventHandler(this.miMove2AllGrid_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(193, 6);
            // 
            // miShowRemovedInGis
            // 
            this.miShowRemovedInGis.Name = "miShowRemovedInGis";
            this.miShowRemovedInGis.Size = new System.Drawing.Size(196, 22);
            this.miShowRemovedInGis.Text = "列表GIS呈现";
            this.miShowRemovedInGis.Click += new System.EventHandler(this.miShowRemovedInGis_Click);
            // 
            // toolStripMenuItem5
            // 
            this.toolStripMenuItem5.Name = "toolStripMenuItem5";
            this.toolStripMenuItem5.Size = new System.Drawing.Size(193, 6);
            // 
            // miExp2XlsRemoved
            // 
            this.miExp2XlsRemoved.Name = "miExp2XlsRemoved";
            this.miExp2XlsRemoved.Size = new System.Drawing.Size(196, 22);
            this.miExp2XlsRemoved.Text = "导出Excel...";
            this.miExp2XlsRemoved.Click += new System.EventHandler(this.miExp2XlsRemoved_Click);
            // 
            // viewRemoved
            // 
            this.viewRemoved.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.viewRemoved.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;
            this.viewRemoved.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.viewRemoved.ColumnPanelRowHeight = 50;
            this.viewRemoved.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn24,
            this.gridColumn25,
            this.gridColumn26,
            this.gridColumn27,
            this.gridColumn28,
            this.gridColumn29,
            this.gridColumn30,
            this.gridColumn31,
            this.gridColumn32,
            this.gridColumn33,
            this.gridColumn34,
            this.gridColumn35,
            this.gridColumn36,
            this.gridColumn37,
            this.gridColumn38});
            this.viewRemoved.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.viewRemoved.GridControl = this.gridCtrlRemoved;
            this.viewRemoved.Name = "viewRemoved";
            this.viewRemoved.OptionsBehavior.Editable = false;
            this.viewRemoved.OptionsSelection.MultiSelect = true;
            this.viewRemoved.OptionsView.ColumnAutoWidth = false;
            this.viewRemoved.OptionsView.EnableAppearanceEvenRow = true;
            this.viewRemoved.OptionsView.ShowDetailButtons = false;
            this.viewRemoved.OptionsView.ShowGroupPanel = false;
            this.viewRemoved.DoubleClick += new System.EventHandler(this.view_DoubleClick);
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "小区";
            this.gridColumn20.FieldName = "CellName";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 0;
            this.gridColumn20.Width = 119;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "CellID";
            this.gridColumn21.FieldName = "CellID";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 1;
            this.gridColumn21.Width = 104;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "最近若干基站";
            this.gridColumn22.FieldName = "NearestBtsNames";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 2;
            this.gridColumn22.Width = 82;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "总采样点数";
            this.gridColumn23.FieldName = "TestPointNum";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 3;
            this.gridColumn23.Width = 56;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "过覆盖点数";
            this.gridColumn24.FieldName = "OverPointNum";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 4;
            this.gridColumn24.Width = 51;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "过覆盖比例(%)";
            this.gridColumn25.FieldName = "OverPercent";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 5;
            this.gridColumn25.Width = 56;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "挂高";
            this.gridColumn26.FieldName = "CellAltitude";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 6;
            this.gridColumn26.Width = 40;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "下倾角";
            this.gridColumn27.FieldName = "CellDownDir";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 7;
            this.gridColumn27.Width = 50;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "建议下倾角";
            this.gridColumn28.FieldName = "CellDownDirSuggest";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 8;
            this.gridColumn28.Width = 48;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "理想覆盖距离(米)";
            this.gridColumn29.FieldName = "IdealCoverDistance";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 9;
            this.gridColumn29.Width = 58;
            // 
            // gridColumn30
            // 
            this.gridColumn30.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gridColumn30.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gridColumn30.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn30.Caption = "过覆盖最小距离(米)";
            this.gridColumn30.FieldName = "OverDistanceMin";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 10;
            this.gridColumn30.Width = 55;
            // 
            // gridColumn31
            // 
            this.gridColumn31.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gridColumn31.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gridColumn31.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn31.Caption = "过覆盖最大距离(米)";
            this.gridColumn31.FieldName = "OverDistanceMax";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 11;
            this.gridColumn31.Width = 55;
            // 
            // gridColumn32
            // 
            this.gridColumn32.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gridColumn32.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gridColumn32.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn32.Caption = "过覆盖平均距离(米)";
            this.gridColumn32.FieldName = "OverDistanceAvg";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 12;
            this.gridColumn32.Width = 55;
            // 
            // gridColumn33
            // 
            this.gridColumn33.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gridColumn33.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gridColumn33.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn33.Caption = "过覆盖最小电平";
            this.gridColumn33.FieldName = "OverDianpingMin";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 13;
            this.gridColumn33.Width = 55;
            // 
            // gridColumn34
            // 
            this.gridColumn34.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gridColumn34.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gridColumn34.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn34.Caption = "过覆盖最大电平";
            this.gridColumn34.FieldName = "OverDianpingMax";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 14;
            this.gridColumn34.Width = 55;
            // 
            // gridColumn35
            // 
            this.gridColumn35.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gridColumn35.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gridColumn35.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn35.Caption = "过覆盖平均电平";
            this.gridColumn35.FieldName = "OverDianpingAvg";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 15;
            this.gridColumn35.Width = 55;
            // 
            // gridColumn36
            // 
            this.gridColumn36.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(192)))), ((int)(((byte)(128)))));
            this.gridColumn36.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(192)))), ((int)(((byte)(128)))));
            this.gridColumn36.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn36.Caption = "过覆盖最小质量";
            this.gridColumn36.FieldName = "OverZhiliangMin";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.Visible = true;
            this.gridColumn36.VisibleIndex = 16;
            this.gridColumn36.Width = 55;
            // 
            // gridColumn37
            // 
            this.gridColumn37.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(192)))), ((int)(((byte)(128)))));
            this.gridColumn37.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(192)))), ((int)(((byte)(128)))));
            this.gridColumn37.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn37.Caption = "过覆盖最大质量";
            this.gridColumn37.FieldName = "OverZhiliangMax";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.Visible = true;
            this.gridColumn37.VisibleIndex = 17;
            this.gridColumn37.Width = 55;
            // 
            // gridColumn38
            // 
            this.gridColumn38.AppearanceHeader.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(192)))), ((int)(((byte)(128)))));
            this.gridColumn38.AppearanceHeader.BackColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(192)))), ((int)(((byte)(128)))));
            this.gridColumn38.AppearanceHeader.Options.UseBackColor = true;
            this.gridColumn38.Caption = "过覆盖平均质量";
            this.gridColumn38.FieldName = "OverZhiliangAvg";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.Visible = true;
            this.gridColumn38.VisibleIndex = 18;
            this.gridColumn38.Width = 55;
            // 
            // ZTTDLTECellCoverLapAnaListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1228, 640);
            this.Controls.Add(this.splitContainerControl1);
            this.Name = "ZTTDLTECellCoverLapAnaListForm";
            this.Text = "过覆盖小区列表";
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlAll)).EndInit();
            this.ctxMenuAll.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.viewAll)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlRemoved)).EndInit();
            this.ctxMenuRemoved.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.viewRemoved)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridCtrlAll;
        private DevExpress.XtraGrid.Views.Grid.GridView viewAll;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraGrid.GridControl gridCtrlRemoved;
        private DevExpress.XtraGrid.Views.Grid.GridView viewRemoved;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private System.Windows.Forms.ContextMenuStrip ctxMenuAll;
        private System.Windows.Forms.ToolStripMenuItem miMove2GridRemoved;
        private System.Windows.Forms.ToolStripMenuItem miExp2XlsAll;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem3;
        private System.Windows.Forms.ContextMenuStrip ctxMenuRemoved;
        private System.Windows.Forms.ToolStripMenuItem miMove2AllGrid;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExp2XlsRemoved;
        private System.Windows.Forms.ToolStripMenuItem miShowAllInGis;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem2;
        private System.Windows.Forms.ToolStripMenuItem miShowRemovedInGis;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem5;
    }
}