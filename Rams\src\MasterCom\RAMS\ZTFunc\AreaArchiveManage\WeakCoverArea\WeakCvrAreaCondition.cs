﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage.WeakCoverArea
{
    public class WeakCvrAreaCondition
    {
        public readonly string RxLevAvgExp = "{Mx_640102}";
        public RelationalOperator WeakRxLevOp { get; set; } = RelationalOperator.Less;
        public RelationalOperator CellDisOp { get; set; } = RelationalOperator.Greater;
        public RelationalOperator AreaDisOp { get; set; } = RelationalOperator.Less;
        public RelationalOperator WeakAreaNumOp { get; set; } = RelationalOperator.GreaterEqual;

        public double RxLev { get; set; } = -85;
        public double CellDistance { get; set; } = 1500;
        public double AreaDistance { get; set; } = 1500;
        public int WeakAreaNum { get; set; } = 2;

        internal bool IsWeakCover(double rxLev)
        {
            return RelationalOperHelper.Evaluate(this.WeakRxLevOp, rxLev, this.RxLev);
        }

        internal bool IsFarCell(double cellDistance)
        {
            return RelationalOperHelper.Evaluate(this.CellDisOp, cellDistance, this.CellDistance);
        }

        internal bool IsNbArea(double areaDistance)
        {
            return RelationalOperHelper.Evaluate(this.AreaDisOp, areaDistance, this.AreaDistance);
        }

        internal void Recommend(WeakCoverArea area)
        {
            area.IsLackCell = area.IsFarCell && RelationalOperHelper.Evaluate(this.WeakAreaNumOp, area.NbWeakAreasNum, this.WeakAreaNum);
        }
    }
}
