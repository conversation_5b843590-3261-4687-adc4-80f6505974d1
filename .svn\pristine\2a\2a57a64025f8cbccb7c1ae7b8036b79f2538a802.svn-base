﻿namespace MasterCom.RAMS.Func.PopShow
{
    partial class ESTaskItemPanel
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.lbAnaTaskTitle = new System.Windows.Forms.Label();
            this.tChartESBranch = new Steema.TeeChart.TChart();
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.listViewTotal = new BrightIdeasSoftware.TreeListView();
            this.olvColumnBranch = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnEvtName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnTime = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnPreType = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLogfile = new BrightIdeasSoftware.OLVColumn();
            this.cbxContentType = new System.Windows.Forms.ComboBox();
            this.label3 = new System.Windows.Forms.Label();
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplayEvent = new System.Windows.Forms.ToolStripMenuItem();
            this.miAnalyseEvent = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem3 = new System.Windows.Forms.ToolStripSeparator();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal)).BeginInit();
            this.ctxMenu.SuspendLayout();
            this.SuspendLayout();
            // 
            // lbAnaTaskTitle
            // 
            this.lbAnaTaskTitle.AutoSize = true;
            this.lbAnaTaskTitle.Font = new System.Drawing.Font("宋体", 12F, ((System.Drawing.FontStyle)((System.Drawing.FontStyle.Bold | System.Drawing.FontStyle.Italic))), System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lbAnaTaskTitle.Location = new System.Drawing.Point(17, 6);
            this.lbAnaTaskTitle.Name = "lbAnaTaskTitle";
            this.lbAnaTaskTitle.Size = new System.Drawing.Size(110, 16);
            this.lbAnaTaskTitle.TabIndex = 1;
            this.lbAnaTaskTitle.Text = "分析类别描述";
            // 
            // tChartESBranch
            // 
            // 
            // 
            // 
            this.tChartESBranch.Aspect.ElevationFloat = 345;
            this.tChartESBranch.Aspect.RotationFloat = 345;
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartESBranch.Axes.Bottom.Automatic = true;
            // 
            // 
            // 
            this.tChartESBranch.Axes.Bottom.Grid.Style = System.Drawing.Drawing2D.DashStyle.Dash;
            this.tChartESBranch.Axes.Bottom.Grid.ZPosition = 0;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartESBranch.Axes.Bottom.Labels.Font.Shadow.Visible = false;
            this.tChartESBranch.Axes.Bottom.Labels.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartESBranch.Axes.Bottom.Labels.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartESBranch.Axes.Bottom.Title.Font.Shadow.Visible = false;
            this.tChartESBranch.Axes.Bottom.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartESBranch.Axes.Bottom.Title.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartESBranch.Axes.Depth.Automatic = true;
            // 
            // 
            // 
            this.tChartESBranch.Axes.Depth.Grid.Style = System.Drawing.Drawing2D.DashStyle.Dash;
            this.tChartESBranch.Axes.Depth.Grid.ZPosition = 0;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartESBranch.Axes.Depth.Labels.Font.Shadow.Visible = false;
            this.tChartESBranch.Axes.Depth.Labels.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartESBranch.Axes.Depth.Labels.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartESBranch.Axes.Depth.Title.Font.Shadow.Visible = false;
            this.tChartESBranch.Axes.Depth.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartESBranch.Axes.Depth.Title.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartESBranch.Axes.DepthTop.Automatic = true;
            // 
            // 
            // 
            this.tChartESBranch.Axes.DepthTop.Grid.Style = System.Drawing.Drawing2D.DashStyle.Dash;
            this.tChartESBranch.Axes.DepthTop.Grid.ZPosition = 0;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartESBranch.Axes.DepthTop.Labels.Font.Shadow.Visible = false;
            this.tChartESBranch.Axes.DepthTop.Labels.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartESBranch.Axes.DepthTop.Labels.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartESBranch.Axes.DepthTop.Title.Font.Shadow.Visible = false;
            this.tChartESBranch.Axes.DepthTop.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartESBranch.Axes.DepthTop.Title.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartESBranch.Axes.Left.Automatic = true;
            // 
            // 
            // 
            this.tChartESBranch.Axes.Left.Grid.Style = System.Drawing.Drawing2D.DashStyle.Dash;
            this.tChartESBranch.Axes.Left.Grid.ZPosition = 0;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartESBranch.Axes.Left.Labels.Font.Shadow.Visible = false;
            this.tChartESBranch.Axes.Left.Labels.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartESBranch.Axes.Left.Labels.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartESBranch.Axes.Left.Title.Font.Shadow.Visible = false;
            this.tChartESBranch.Axes.Left.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartESBranch.Axes.Left.Title.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartESBranch.Axes.Right.Automatic = true;
            // 
            // 
            // 
            this.tChartESBranch.Axes.Right.Grid.Style = System.Drawing.Drawing2D.DashStyle.Dash;
            this.tChartESBranch.Axes.Right.Grid.ZPosition = 0;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartESBranch.Axes.Right.Labels.Font.Shadow.Visible = false;
            this.tChartESBranch.Axes.Right.Labels.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartESBranch.Axes.Right.Labels.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartESBranch.Axes.Right.Title.Font.Shadow.Visible = false;
            this.tChartESBranch.Axes.Right.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartESBranch.Axes.Right.Title.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartESBranch.Axes.Top.Automatic = true;
            // 
            // 
            // 
            this.tChartESBranch.Axes.Top.Grid.Style = System.Drawing.Drawing2D.DashStyle.Dash;
            this.tChartESBranch.Axes.Top.Grid.ZPosition = 0;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartESBranch.Axes.Top.Labels.Font.Shadow.Visible = false;
            this.tChartESBranch.Axes.Top.Labels.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartESBranch.Axes.Top.Labels.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartESBranch.Axes.Top.Title.Font.Shadow.Visible = false;
            this.tChartESBranch.Axes.Top.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartESBranch.Axes.Top.Title.Shadow.Visible = false;
            this.tChartESBranch.BackColor = System.Drawing.Color.Transparent;
            this.tChartESBranch.Dock = System.Windows.Forms.DockStyle.Fill;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartESBranch.Footer.Font.Shadow.Visible = false;
            this.tChartESBranch.Footer.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartESBranch.Footer.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartESBranch.Header.Font.Shadow.Visible = false;
            this.tChartESBranch.Header.Font.Unit = System.Drawing.GraphicsUnit.World;
            this.tChartESBranch.Header.Lines = new string[] {
        "TeeChart"};
            // 
            // 
            // 
            this.tChartESBranch.Header.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartESBranch.Legend.Font.Shadow.Visible = false;
            this.tChartESBranch.Legend.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartESBranch.Legend.Title.Font.Bold = true;
            // 
            // 
            // 
            this.tChartESBranch.Legend.Title.Font.Shadow.Visible = false;
            this.tChartESBranch.Legend.Title.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartESBranch.Legend.Title.Pen.Visible = false;
            // 
            // 
            // 
            this.tChartESBranch.Legend.Title.Shadow.Visible = false;
            this.tChartESBranch.Location = new System.Drawing.Point(0, 0);
            this.tChartESBranch.Name = "tChartESBranch";
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartESBranch.Panel.Brush.Color = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            // 
            // 
            // 
            this.tChartESBranch.Panel.Shadow.Visible = false;
            this.tChartESBranch.Size = new System.Drawing.Size(333, 281);
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartESBranch.SubFooter.Font.Shadow.Visible = false;
            this.tChartESBranch.SubFooter.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartESBranch.SubFooter.Shadow.Visible = false;
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartESBranch.SubHeader.Font.Shadow.Visible = false;
            this.tChartESBranch.SubHeader.Font.Unit = System.Drawing.GraphicsUnit.World;
            // 
            // 
            // 
            this.tChartESBranch.SubHeader.Shadow.Visible = false;
            this.tChartESBranch.TabIndex = 3;
            // 
            // 
            // 
            // 
            // 
            // 
            this.tChartESBranch.Walls.Back.AutoHide = false;
            // 
            // 
            // 
            this.tChartESBranch.Walls.Back.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartESBranch.Walls.Bottom.AutoHide = false;
            // 
            // 
            // 
            this.tChartESBranch.Walls.Bottom.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartESBranch.Walls.Left.AutoHide = false;
            // 
            // 
            // 
            this.tChartESBranch.Walls.Left.Shadow.Visible = false;
            // 
            // 
            // 
            this.tChartESBranch.Walls.Right.AutoHide = false;
            // 
            // 
            // 
            this.tChartESBranch.Walls.Right.Shadow.Visible = false;
            // 
            // splitContainer1
            // 
            this.splitContainer1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.splitContainer1.Location = new System.Drawing.Point(-2, 25);
            this.splitContainer1.Name = "splitContainer1";
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.listViewTotal);
            this.splitContainer1.Panel1.Controls.Add(this.cbxContentType);
            this.splitContainer1.Panel1.Controls.Add(this.label3);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.tChartESBranch);
            this.splitContainer1.Size = new System.Drawing.Size(878, 281);
            this.splitContainer1.SplitterDistance = 541;
            this.splitContainer1.TabIndex = 5;
            // 
            // listViewTotal
            // 
            this.listViewTotal.AllColumns.Add(this.olvColumnBranch);
            this.listViewTotal.AllColumns.Add(this.olvColumnEvtName);
            this.listViewTotal.AllColumns.Add(this.olvColumnTime);
            this.listViewTotal.AllColumns.Add(this.olvColumnPreType);
            this.listViewTotal.AllColumns.Add(this.olvColumnLogfile);
            this.listViewTotal.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.listViewTotal.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnBranch,
            this.olvColumnEvtName,
            this.olvColumnTime,
            this.olvColumnPreType,
            this.olvColumnLogfile});
            this.listViewTotal.ContextMenuStrip = this.ctxMenu;
            this.listViewTotal.Cursor = System.Windows.Forms.Cursors.Default;
            this.listViewTotal.FullRowSelect = true;
            this.listViewTotal.GridLines = true;
            this.listViewTotal.HeaderWordWrap = true;
            this.listViewTotal.IsNeedShowOverlay = false;
            this.listViewTotal.Location = new System.Drawing.Point(5, 29);
            this.listViewTotal.Name = "listViewTotal";
            this.listViewTotal.OwnerDraw = true;
            this.listViewTotal.ShowGroups = false;
            this.listViewTotal.Size = new System.Drawing.Size(533, 249);
            this.listViewTotal.TabIndex = 9;
            this.listViewTotal.UseCompatibleStateImageBehavior = false;
            this.listViewTotal.View = System.Windows.Forms.View.Details;
            this.listViewTotal.VirtualMode = true;
            // 
            // olvColumnBranch
            // 
            this.olvColumnBranch.HeaderFont = null;
            this.olvColumnBranch.Text = "现象类型";
            this.olvColumnBranch.Width = 85;
            // 
            // olvColumnEvtName
            // 
            this.olvColumnEvtName.HeaderFont = null;
            this.olvColumnEvtName.Text = "事件";
            this.olvColumnEvtName.Width = 127;
            // 
            // olvColumnTime
            // 
            this.olvColumnTime.AspectName = "";
            this.olvColumnTime.HeaderFont = null;
            this.olvColumnTime.Text = "时间";
            this.olvColumnTime.Width = 104;
            // 
            // olvColumnPreType
            // 
            this.olvColumnPreType.HeaderFont = null;
            this.olvColumnPreType.Text = "预判类型描述";
            this.olvColumnPreType.Width = 87;
            // 
            // olvColumnLogfile
            // 
            this.olvColumnLogfile.HeaderFont = null;
            this.olvColumnLogfile.Text = "测试文件";
            this.olvColumnLogfile.Width = 335;
            // 
            // cbxContentType
            // 
            this.cbxContentType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxContentType.FormattingEnabled = true;
            this.cbxContentType.Location = new System.Drawing.Point(51, 3);
            this.cbxContentType.Name = "cbxContentType";
            this.cbxContentType.Size = new System.Drawing.Size(128, 20);
            this.cbxContentType.TabIndex = 6;
            this.cbxContentType.SelectedIndexChanged += new System.EventHandler(this.cbxContentType_SelectedIndexChanged);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(8, 8);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(41, 12);
            this.label3.TabIndex = 8;
            this.label3.Text = "内容：";
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplayEvent,
            this.miAnalyseEvent,
            this.toolStripMenuItem3,
            this.toolStripMenuItem1,
            this.miExpandAll,
            this.miCallapsAll});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(137, 104);
            // 
            // miReplayEvent
            // 
            this.miReplayEvent.Name = "miReplayEvent";
            this.miReplayEvent.Size = new System.Drawing.Size(136, 22);
            this.miReplayEvent.Text = "回放事件";
            this.miReplayEvent.Click += new System.EventHandler(this.miReplayEvent_Click);
            // 
            // miAnalyseEvent
            // 
            this.miAnalyseEvent.Name = "miAnalyseEvent";
            this.miAnalyseEvent.Size = new System.Drawing.Size(136, 22);
            this.miAnalyseEvent.Text = "智能分析...";
            this.miAnalyseEvent.Click += new System.EventHandler(this.miAnalyseEvent_Click);
            // 
            // toolStripMenuItem3
            // 
            this.toolStripMenuItem3.Name = "toolStripMenuItem3";
            this.toolStripMenuItem3.Size = new System.Drawing.Size(133, 6);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(133, 6);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(136, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCallapsAll
            // 
            this.miCallapsAll.Name = "miCallapsAll";
            this.miCallapsAll.Size = new System.Drawing.Size(136, 22);
            this.miCallapsAll.Text = "全部合并";
            this.miCallapsAll.Click += new System.EventHandler(this.miCallapsAll_Click);
            // 
            // ESTaskItemPanel
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.White;
            this.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.Controls.Add(this.splitContainer1);
            this.Controls.Add(this.lbAnaTaskTitle);
            this.Name = "ESTaskItemPanel";
            this.Size = new System.Drawing.Size(879, 309);
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel1.PerformLayout();
            this.splitContainer1.Panel2.ResumeLayout(false);
            this.splitContainer1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label lbAnaTaskTitle;
        private Steema.TeeChart.TChart tChartESBranch;
        private System.Windows.Forms.SplitContainer splitContainer1;
        private System.Windows.Forms.ComboBox cbxContentType;
        private System.Windows.Forms.Label label3;
        private BrightIdeasSoftware.TreeListView listViewTotal;
        private BrightIdeasSoftware.OLVColumn olvColumnEvtName;
        private BrightIdeasSoftware.OLVColumn olvColumnTime;
        private BrightIdeasSoftware.OLVColumn olvColumnBranch;
        private BrightIdeasSoftware.OLVColumn olvColumnLogfile;
        private BrightIdeasSoftware.OLVColumn olvColumnPreType;
        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miReplayEvent;
        private System.Windows.Forms.ToolStripMenuItem miAnalyseEvent;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem3;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCallapsAll;
    }
}
