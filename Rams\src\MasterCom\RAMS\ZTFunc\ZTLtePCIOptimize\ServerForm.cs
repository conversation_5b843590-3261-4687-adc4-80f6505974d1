﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ServerForm : BaseForm
    {
        public ServerForm(List<CDb> servers)
        {
            InitializeComponent();
            init(servers);
        }

        private void init(List<CDb> servers)
        {
            combxServerList.Items.Clear();
            foreach (CDb svr in servers)
            {
                combxServerList.Items.Add(svr);
            }
            combxServerList.SelectedIndex = 0;
        }

        public CDb SelServer
        {
            get { return combxServerList.SelectedItem as CDb; }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
