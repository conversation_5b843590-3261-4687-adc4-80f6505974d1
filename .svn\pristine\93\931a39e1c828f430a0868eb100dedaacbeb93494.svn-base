﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.Text;
using System.Web.UI.WebControls;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryDLSpeedPitAna : DIYAnalyseByFileBackgroundBase
    {
        public QueryDLSpeedPitAna() : base(MainModel.GetInstance())
        {
        }

        public override string Name
        {
            get { return "下载速率掉坑分析"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22109, this.Name);
        }

        DLSpeedPitCond pitCond = new DLSpeedPitCond();
        List<DLSpeedPitInfo> pitInfoList = new List<DLSpeedPitInfo>();
        protected override bool getCondition()
        {
            DLSpeedPitSet conForm = new DLSpeedPitSet();
            if (conForm.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            pitCond = conForm.GetCond();
            return true;
        }
        protected override void clearDataBeforeAnalyseFiles()
        {
            pitInfoList = new List<DLSpeedPitInfo>();
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager dtFile in MainModel.DTDataManager.FileDataManagers)
            {
                Dictionary<int, int> timeSNDic = new Dictionary<int, int>();
                DLSpeedPitInfo pitInfo = new DLSpeedPitInfo();
                int iCount = dtFile.TestPoints.Count;
                int iSpeedTime = -1;
                for (int i = 0; i < iCount; i++)
                {
                    try
                    {
                        TestPoint tp = dtFile.TestPoints[i];
                        double speed;
                        bool isValid = getValidSpeed(tp, out speed);
                        if (isValid)
                        {
                            setDLSpeedPitInfo(dtFile, timeSNDic, ref pitInfo, ref iSpeedTime, ref i, tp, speed);
                        }
                    }
                    catch
                    {
                        //continue
                    }
                }
            }
        }

        private bool getValidSpeed(TestPoint tp, out double speed)
        {
            bool ret = isValidTestPoint(tp);
            if (!ret)
            {
                object obj = tp["lte_APP_ThroughputDL_Mb"];
                if (obj != null)
                {
                    speed = double.Parse(obj.ToString());
                    if (speed >= 0)
                    {
                        return true;
                    }
                }
            }
            speed = 0;
            return false;
        }

        private void setDLSpeedPitInfo(DTFileDataManager dtFile, Dictionary<int, int> timeSNDic, ref DLSpeedPitInfo pitInfo, 
            ref int iSpeedTime, ref int i, TestPoint tp, double speed)
        {
            if (iSpeedTime != -1 && (tp.Time - iSpeedTime > 2))
            {
                pitInfo = new DLSpeedPitInfo();
            }
            iSpeedTime = tp.Time;
            if (!timeSNDic.ContainsKey(tp.Time))
            {
                timeSNDic[tp.Time] = i;
            }
            pitInfo.AddPoint(tp, speed, pitCond);
            if (!pitInfo.IsVailInfo)
            {
                int iDiff = timeSNDic[pitInfo.LPreTimeList[1]] == timeSNDic[pitInfo.LPreTimeList[0]] ? 0 : 1;
                i = timeSNDic[pitInfo.LPreTimeList[1]] - iDiff;
                pitInfo = new DLSpeedPitInfo();
            }
            if (pitInfo.IsOutPitLast)
            {
                pitInfo.FileInfoMsg = dtFile.GetFileInfo();
                i = timeSNDic[pitInfo.LAftTimeList[0]];
                pitInfo.Clear();
                pitInfo.ISN = pitInfoList.Count + 1;
                pitInfoList.Add(pitInfo);
                pitInfo = new DLSpeedPitInfo();
            }
        }

        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            bool ret = false;
            try
            {
                if (testPoint is LTETestPointDetail)
                {
                    short? type = (short?)testPoint["lte_APP_type"];
                    if (type == null || type != (int)AppType.FTP_Download)
                    {
                        return false;
                    }
                    ret = condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude);
                }
            }
            catch
            {
                //continue
            }
            return ret;
        }

        protected override void fireShowForm()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(DLSpeedPitForm).FullName);
            DLSpeedPitForm resultForm = obj == null ? null : obj as DLSpeedPitForm;
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new DLSpeedPitForm(MainModel);
            }
            resultForm.FillData(pitInfoList);
            if (!resultForm.Visible)
            {
                resultForm.Show(MainModel.MainForm);
            }
        }
    }
}