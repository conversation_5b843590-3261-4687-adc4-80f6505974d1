﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Drawing.Imaging;
using System.Text.RegularExpressions;
using MasterCom.RAMS.Model;
using Excel = Microsoft.Office.Interop.Excel;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.ZTLteTestAcceptance
{
    abstract class LteTestAcceptBase
    {
        public virtual void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string PCI, int sectorID)
        {
        }

        public virtual void FillResult(string btsName, Excel.Workbook eBook, Dictionary<string, int> btsDic)
        {
            FillResultToSheet(btsName, eBook, 2, new List<int>(btsDic.Values));
        }

        protected string GetBtsName(string fileName)
        {
            string btsNameTmp = "";
            string[] names = fileName.Split('_');
            if (names.Length > 3)
            {
                btsNameTmp = names[2];
            }
            return btsNameTmp;
        }

        public virtual bool IsValidFile(FileInfo fileInfo)
        {
            return false;
        }

        public virtual void Clear()
        {
            btsResultDic.Clear();
        }

        public virtual int GetSectorCount(string btsName)
        {
            return btsResultDic.ContainsKey(btsName) ? btsResultDic[btsName].SectorCount : 0;
        }

        public List<string> BtsNames
        {
            get
            {
                return new List<string>(btsResultDic.Keys);
            }
        }

        protected virtual void FillResultToSheet(string btsName, Excel.Workbook eBook, int sheetIndex, List<int> sectorIDs)
        {
            if (!btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            LteTestAcceptResult result = btsResultDic[btsName];
            for (int i = 0; i < sectorIDs.Count; ++i)
            {
                int sid = sectorIDs[i];
                for (int row = 0; row < resultGrid.GetLength(1); ++row)
                {
                    for (int col = 0; col < resultGrid.GetLength(2); ++col)
                    {
                        object value = result.GetValue(sid, row, col);
                        InsertExcelValue(eBook, sheetIndex, resultGrid[i, row, col], value);
                    }
                }
            }
        }

        protected void InsertExcelValue(Excel.Workbook eBook, int sheetIndex, string cell, object value)
        {
            if (value == null)
            {
                return;
            }
            if (value is double && (double)value == double.MinValue)
            {
                return;
            }

            Excel.Worksheet eSheet = (Excel.Worksheet)eBook.Sheets[sheetIndex];
            Excel.Range rng = eSheet.get_Range(cell, Type.Missing);
            rng.set_Value(Type.Missing, value);
        }

        protected void InsertExcelValue(Excel.Workbook eBook, int sheetIndex, string cell, object value, bool IsColorRed)
        {
            if (value == null)
            {
                return;
            }
            if (value is double && (double)value == double.MinValue)
            {
                return;
            }

            Excel.Worksheet eSheet = (Excel.Worksheet)eBook.Sheets[sheetIndex];
            Excel.Range rng = eSheet.get_Range(cell, Type.Missing);
            rng.set_Value(Type.Missing, value);
            if (IsColorRed)
            {
                rng.Font.ColorIndex = 3;
            }
        }

        /// <summary>
        /// 判断是否从文件分析出结果,无结果后续用APP数据填充
        /// </summary>
        /// <returns></returns>
        public virtual bool JudgeHaveResult()
        {
            return true;
        }

        public virtual void ResetResult<T>(T data, Dictionary<string, int> btsDic)
        {

        }

        public void Init(LTECell lteCell, string btsNameByFile)
        {
            this.lteCell = lteCell;
            this.btsName = btsNameByFile;
        }

        protected LTECell lteCell;

        protected string btsName = "";

        protected Dictionary<string, LteTestAcceptResult> btsResultDic = new Dictionary<string, LteTestAcceptResult>();

        protected string[,,] resultGrid = null; // 小区个数，行数，列数

        protected static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
    }

    // 分析下载文件
    class AcpFtpDownload : LteTestAcceptBase
    {
        private string pci = "";
        public AcpFtpDownload()
        {
            CellDic = new Dictionary<string, CellKPI>();

            resultGrid = new string[9, 5, 1];
            int idx = 15;
            int nameIdx = 6;
            int step = 18;
            for (int i = 0; i < 9; i++)
            {
                int row = idx + i * step;
                int nameRow = nameIdx + i * step;

                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 1, 0] = "p" + (++row).ToString();
                resultGrid[i, 2, 0] = "p" + (++row).ToString();
                resultGrid[i, 3, 0] = "p" + (++row).ToString();
                resultGrid[i, 4, 0] = "a" + nameRow.ToString();
            }
        }

        protected Dictionary<string, CellKPI> CellDic = null;

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string PCI, int sectorID)
        {
            pci = PCI;
            if (!IsValidFile(fileInfo) || string.IsNullOrEmpty(btsName))
            {
                return;
            }

            CellKPI kpiCell = AnaFile(fileInfo, fileManager, PCI);
            if (string.IsNullOrEmpty(PCI))
            {
                log.Info(string.Format("文件{0}获取PCI失败", fileInfo.Name));
                return;
            }
            if (kpiCell == null)
            {
                log.Info(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return;
            }
            kpiCell.CalcResult();

            int colIndex = GetColumnIndex(fileInfo);
            if (colIndex == -1)
            {
                log.Info(string.Format("文件{0}未发现PCI关键字", fileInfo.Name));
                return;
            }
            SaveResult(fileInfo, kpiCell, colIndex, sectorID);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("下载");
        }

        protected virtual double GetSpeed(CellKPI kpiCell)
        {
            return kpiCell.AvgDLSpeed;
        }

        protected virtual void SaveResult(FileInfo fileInfo, CellKPI kpiCell, int colIndex, int sectorID)
        {
            string btsName = GetBtsName(fileInfo.Name);
            LteTestAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }

            SetValue(result, sectorID, 0, colIndex, Math.Round(kpiCell.CoverRate, 4));
            SetValue(result, sectorID, 1, colIndex, kpiCell.AvgRsrp);
            SetValue(result, sectorID, 2, colIndex, kpiCell.AvgSinr);
            SetValue(result, sectorID, 3, colIndex, GetSpeed(kpiCell));
            SetValue(result, sectorID, 4, colIndex, "Cell-" + (sectorID + 1).ToString() + " PCI:" + pci);
        }

        protected CellKPI AnaFile(FileInfo fileInfo, DTFileDataManager fileManager, string PCI)
        {
            CellKPI targetKpiCell = null;

            if (!CellDic.TryGetValue(PCI, out targetKpiCell))
            {
                targetKpiCell = new CellKPI(PCI);
                CellDic[PCI] = targetKpiCell;
            }
            LTECell targeCell = null;
            int pciTmp = -255;
            int earfcn = -255;

            foreach (TestPoint tp in fileManager.TestPoints)
            {
                getValidTPInfo(ref targeCell, ref pciTmp, ref earfcn, tp);

                int? curPCI = (int?)(short?)tp["lte_PCI"];
                int? curEARFCN = (int?)tp["lte_EARFCN"];
                if ((curPCI != null && curEARFCN != null) && ((int)curPCI == pciTmp && (int)curEARFCN == earfcn))
                {
                    targetKpiCell.AddPoint(tp);
                }
            }
            targetKpiCell.CalcResult();
            return targetKpiCell;
        }

        private void getValidTPInfo(ref LTECell targeCell, ref int pciTmp, ref int earfcn, TestPoint tp)
        {
            if (targeCell == null)
            {
                List<LTECell> cells = StationAcceptCellHelper_XJ.Instance.GetLTECellListByEarfcnPci(tp);
                foreach (var cell in cells)
                {
                    if (cell.Name.Contains(btsName))
                    {
                        targeCell = cell;
                        pciTmp = (int)(short)tp["lte_PCI"];
                        earfcn = (int)tp["lte_EARFCN"];
                        break;
                    }
                }
            }
        }

        protected int GetColumnIndex(FileInfo fileInfo)
        {
            if (fileInfo.Name.ToUpper().Contains("PCI"))
            {
                return 0;
            }
            return -1;
        }

        protected void SetValue(LteTestAcceptResult result, int sectorID, int rowIdx, int colIdx, object value)
        {
            // 直接覆盖原有的值
            result.SetValue(sectorID, rowIdx, colIdx, value, true);
        }

        public override bool JudgeHaveResult()
        {
            if (btsResultDic.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override void ResetResult<T>(T data, Dictionary<string, int> btsDic)
        {
            //从文件没有分析出结果,利用APP数据填充
            if (data is AcpPerformanceByApp)
            {
                LteTestAcceptResult result = null;
                if (!btsResultDic.TryGetValue(btsName, out result))
                {
                    result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                    btsResultDic.Add(btsName, result);
                }

                AcpPerformanceByApp appData = data as AcpPerformanceByApp;
                foreach (var info in appData.AllCellPerformanceResult)
                {
                    AcceptPerformanceInfo performanceData = info.Value;
                    int sectorID;
                    if (btsDic.TryGetValue(info.Key.PCI.ToString(), out sectorID))
                    {
                        setResult(result, info, performanceData, sectorID);
                    }
                }
            }
        }

        protected virtual void setResult(LteTestAcceptResult result, KeyValuePair<LTECell, AcceptPerformanceInfo> info, AcceptPerformanceInfo performanceData, int sectorID)
        {
            SetValue(result, sectorID, 0, 0, performanceData.CoverRate);
            SetValue(result, sectorID, 1, 0, performanceData.Rsrp);
            SetValue(result, sectorID, 2, 0, performanceData.Sinr);
            SetValue(result, sectorID, 3, 0, performanceData.DLThroughput);
            SetValue(result, sectorID, 4, 0, "Cell-" + (sectorID + 1).ToString() + " PCI:" + info.Key.PCI);
        }

        protected class CellKPI
        {
            public CellKPI(string PCI)
            {
                this.PCI = PCI;
            }

            public string PCI
            {
                get;
                private set;
            }

            public int PointCount
            {
                get;
                private set;
            }

            public double AvgRsrp
            {
                get;
                private set;
            }

            public double AvgSinr
            {
                get;
                private set;
            }

            public double AvgULSpeed
            {
                get;
                private set;
            }

            public double AvgDLSpeed
            {
                get;
                private set;
            }

            public double CoverRate
            {
                get;
                private set;
            }

            public void AddPoint(TestPoint tp)
            {
                ++PointCount;

                float? rsrp = (float?)tp["lte_RSRP"];
                if (rsrp != null && rsrp != -10000000)
                {
                    ++cntRsrp;
                    sumRsrp += (float)rsrp;
                }

                float? sinr = (float?)tp["lte_SINR"];
                if (sinr != null && sinr != -10000000)
                {
                    ++cntSinr;
                    sumSinr += (float)sinr;
                }

                if (sinr != null && sinr != -10000000 && sinr >= 6 && rsrp != null && rsrp != -10000000 && rsrp >= -105)
                {
                    ++RSCount;
                }

                double? ulSpeed = (double?)tp["lte_PDCP_UL_Mb"];
                if (ulSpeed != null)
                {
                    ++cntULSpeed;
                    sumULSpeed += (double)ulSpeed;
                }

                double? dlSpeed = (double?)tp["lte_PDCP_DL_Mb"];
                if (dlSpeed != null)
                {
                    ++cntDLSpeed;
                    sumDLSpeed += (double)dlSpeed;
                }
            }

            public void CalcResult()
            {
                AvgRsrp = cntRsrp == 0 ? double.MinValue : Math.Round(sumRsrp / cntRsrp, 2);
                AvgSinr = cntSinr == 0 ? double.MinValue : Math.Round(sumSinr / cntSinr, 2);
                AvgULSpeed = cntULSpeed == 0 ? double.MinValue : Math.Round(sumULSpeed / cntULSpeed, 2);
                AvgDLSpeed = cntDLSpeed == 0 ? double.MinValue : Math.Round(sumDLSpeed / cntDLSpeed, 2);
                CoverRate = RSCount == 0 ? double.MinValue : Math.Round(RSCount / (double)PointCount, 4);
            }

            private double sumRsrp;
            private int cntRsrp;

            private double sumSinr;
            private int cntSinr;

            private double sumULSpeed;
            private int cntULSpeed;

            private double sumDLSpeed;
            private int cntDLSpeed;

            private int RSCount;
        }
    }

    // 分析上传文件
    class AcpFtpUpload : AcpFtpDownload
    {
        public AcpFtpUpload()
        {
            CellDic = new Dictionary<string, CellKPI>();

            resultGrid = new string[9, 1, 1];
            int idx = 19;
            int step = 18;
            for (int i = 0; i < 9; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("上传");
        }

        protected override double GetSpeed(CellKPI kpiCell)
        {
            return kpiCell.AvgULSpeed;
        }

        protected override void SaveResult(FileInfo fileInfo, CellKPI kpiCell, int colIndex, int sectorID)
        {
            string btsName = GetBtsName(fileInfo.Name);
            LteTestAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }
            SetValue(result, sectorID, 0, colIndex, GetSpeed(kpiCell));
        }

        public override bool JudgeHaveResult()
        {
            if (btsResultDic.Count > 0)
            {
                return true;
            }
            return false;
        }

        protected override void setResult(LteTestAcceptResult result, KeyValuePair<LTECell, AcceptPerformanceInfo> info, AcceptPerformanceInfo performanceData, int sectorID)
        {
            SetValue(result, sectorID, 0, 0, performanceData.ULThroughput);
        }
    }

    class AcpRrcRate : LteTestAcceptBase
    {
        public AcpRrcRate()
        {
            evtRequList = new List<int> { 855 };
            evtSuccList = new List<int> { 856 };

            resultGrid = new string[9, 1, 3];
            int idx = 7;
            int step = 18;
            for (int i = 0; i < 9; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 0, 1] = "w" + row.ToString();
                resultGrid[i, 0, 2] = "ac" + row.ToString();
            }
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string PCI, int sectorID)
        {
            if (!IsValidFile(fileInfo) || string.IsNullOrEmpty(btsName))
            {
                return;
            }
            CellKPI kpiCell = AnaFile(fileInfo, fileManager, PCI, sectorID);
            if (kpiCell == null)
            {
                log.Info(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return;
            }
            SaveResult(fileInfo, kpiCell, sectorID);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("附着");
        }

        protected virtual CellKPI AnaFile(FileInfo fileInfo, DTFileDataManager fileManager, string PCI, int sectorID)
        {
            CellKPI kpiCell = new CellKPI(PCI);
            foreach (Event evt in fileManager.Events)
            {
                if (evtRequList.Contains(evt.ID))
                {
                    ++kpiCell.RequestCnt;
                }
                if (evtSuccList.Contains(evt.ID))
                {
                    ++kpiCell.SucceedCnt;
                }
            }
            return kpiCell;
        }

        protected virtual void SaveResult(FileInfo fileInfo, CellKPI kpiCell, int sectorID)
        {
            string btsName = GetBtsName(fileInfo.Name);

            int rowIdx = 0;
            LteTestAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }
            result.SetValue(sectorID, rowIdx, 0, kpiCell.RequestCnt, false);
            result.SetValue(sectorID, rowIdx, 1, kpiCell.SucceedCnt, false);
            result.SetValue(sectorID, rowIdx, 2, kpiCell.FailedCnt, false);
        }    

        protected class CellKPI
        {
            public int RequestCnt
            {
                get;
                set;
            }

            public int SucceedCnt
            {
                get;
                set;
            }

            public int FailedCnt
            {
                get
                {
                    return RequestCnt - SucceedCnt;
                }
            }

            public string PCI
            {
                get;
                private set;
            }

            public CellKPI(string PCI)
            {
                this.PCI = PCI;
            }
        }

        protected List<int> evtSuccList;
        protected List<int> evtRequList;
    }

    class AcpCsfbRate : AcpRrcRate
    {
        public AcpCsfbRate()
        {
            resultGrid = new string[9, 1, 3];
            int idx = 11;
            int step = 18;
            for (int i = 0; i < 9; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 0, 1] = "w" + row.ToString();
                resultGrid[i, 0, 2] = "ac" + row.ToString();
            }
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string PCI, int sectorID)
        {
            if (!IsValidFile(fileInfo) || string.IsNullOrEmpty(btsName))
            {
                return;
            }

            AcpAutoCsfbRate.GetCsfbEventIdsByDevice(fileInfo.DeviceType, out evtRequList, out evtSuccList);

            CellKPI kpiCell = AnaFile(fileInfo, fileManager, PCI, sectorID);
            if (kpiCell == null)
            {
                log.Info(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return;
            }

            SaveResult(fileInfo, kpiCell, sectorID);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("语音");
        }

        public override bool JudgeHaveResult()
        {
            if (btsResultDic.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override void ResetResult<T>(T data, Dictionary<string, int> btsDic)
        {
            if (data is AcpPerformanceByApp)
            {
                LteTestAcceptResult result = null;
                if (!btsResultDic.TryGetValue(btsName, out result))
                {
                    result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                    btsResultDic.Add(btsName, result);
                }

                AcpPerformanceByApp appData = data as AcpPerformanceByApp;
                foreach (var info in appData.AllCellPerformanceResult)
                {
                    AcceptPerformanceInfo performanceData = info.Value;
                    int sectorID;
                    if (btsDic.TryGetValue(info.Key.PCI.ToString(), out sectorID))
                    {
                        setResult(result, info, performanceData, sectorID);
                    }
                }
            }
        }

        protected void setResult(LteTestAcceptResult result, KeyValuePair<LTECell, AcceptPerformanceInfo> info, AcceptPerformanceInfo performanceData, int sectorID)
        {
            result.SetValue(sectorID, 0, 0, performanceData.CsfbRequestCnt, true);
            result.SetValue(sectorID, 0, 1, performanceData.CsfbSuccessCnt, true);
            result.SetValue(sectorID, 0, 2, performanceData.CsfbSuccessRate, true);
        }
    }

    class AcpHandoverRate : AcpRrcRate
    {
        public AcpHandoverRate()
        {
            evtRequList = new List<int> { 850, 898 };
            evtSuccList = new List<int> { 851, 899 };

            resultGrid = new string[9, 1, 3];
            int idx = 23;
            int step = 18;
            for (int i = 0; i < 9; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 0, 1] = "w" + row.ToString();
                resultGrid[i, 0, 2] = "ac" + row.ToString();
            }
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string PCI, int sectorID)
        {
            if (!IsValidFile(fileInfo) || string.IsNullOrEmpty(btsName))
            {
                return;
            }

            CellKPI kpiCell = AnaFile(fileInfo, fileManager, PCI, sectorID);
            if (kpiCell == null)
            {
                log.Info(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return;
            }
            SaveResult(fileInfo, kpiCell, sectorID);
        }

        protected override CellKPI AnaFile(FileInfo fileInfo, DTFileDataManager fileManager, string PCI, int sectorID)
        {
            CellKPI kpiCell = new CellKPI(PCI);
            foreach (Event evt in fileManager.Events)
            {
                if (evtRequList.Contains(evt.ID))
                {
                    ++kpiCell.RequestCnt;
                }
                if (evtSuccList.Contains(evt.ID))
                {
                    ++kpiCell.SucceedCnt;
                }
            }
            return kpiCell;
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("切换");
        }

        protected int evtRequ1 = 0;
        protected int evtSucc1 = 0;
    }

    class AcpErabRate : AcpRrcRate
    {
        public AcpErabRate()
        {
            evtRequList = new List<int> { 858 };
            evtSuccList = new List<int> { 859 };

            resultGrid = new string[9, 1, 3];
            int idx = 8;
            int step = 18;
            for (int i = 0; i < 9; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 0, 1] = "w" + row.ToString();
                resultGrid[i, 0, 2] = "ac" + row.ToString();
            }
        }
    }

    class AcpAccRate : AcpRrcRate
    {
        public AcpAccRate()
        {
            evtRequList = new List<int> { 22 };
            evtSuccList = new List<int> { 23 };

            resultGrid = new string[9, 1, 3];
            int idx = 9;
            int step = 18;
            for (int i = 0; i < 9; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 0, 1] = "w" + row.ToString();
                resultGrid[i, 0, 2] = "ac" + row.ToString();
            }
        }
    }

    class AcpDivulgeRate : LteTestAcceptBase
    {
        public AcpDivulgeRate()
        {
            resultGrid = new string[9, 1, 1];
            int idx = 21;
            int step = 18;
            for (int i = 0; i < 9; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
            }
        }

        protected string getEARFCN(string fileName)
        {
            string[] names = fileName.Split('_');
            string name = "";
            foreach (string item in names)
            {
                if (item.Contains("频点"))
                {
                    int index = item.IndexOf("频点") + 2;
                    name = item.Substring(index);
                }
            }
            return name;
        }
        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string PCI, int sectorID)
        {
            if (!IsValidFile(fileInfo) || string.IsNullOrEmpty(btsName))
            {
                return;
            }

            CellKPI kpiCell = AnaFile(fileInfo, fileManager, PCI);
            if (string.IsNullOrEmpty(PCI))
            {
                log.Info(string.Format("文件{0}获取PCI失败", fileInfo.Name));
                return;
            }
            if (kpiCell == null)
            {
                log.Info(string.Format("文件{0}获取目标小区信息失败", fileInfo.Name));
                return;
            }

            int colIndex = GetColumnIndex(fileInfo);
            if (colIndex == -1)
            {
                log.Info(string.Format("文件{0}未发现PCI关键字", fileInfo.Name));
                return;
            }
            SaveResult(fileInfo, kpiCell, colIndex, sectorID);
        }

        protected void SaveResult(FileInfo fileInfo, CellKPI kpiCell, int colIndex, int sectorID)
        {
            string btsName = GetBtsName(fileInfo.Name);
            LteTestAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }

            SetValue(result, sectorID, 0, colIndex, kpiCell.DivulgeRate);

        }

        protected virtual CellKPI AnaFile(FileInfo fileInfo, DTFileDataManager fileManager, string PCI)
        {
            CellKPI targetKpiCell = new CellKPI(PCI);
            string EARFCN = getEARFCN(fileInfo.Name);
            if (string.IsNullOrEmpty(EARFCN))
            {
                log.Info(string.Format("文件{0}获取频点失败", fileInfo.Name));
            }

            LTECell targetCell = null;
            int pci = -255;
            int earfcn = -255;

            foreach (TestPoint tp in fileManager.TestPoints)
            {
                if (targetCell == null)
                {
                    getValidTPInfo(ref targetCell, ref pci, ref earfcn, tp);
                }
                if (targetCell != null)
                {
                    addVliadTPCount(targetKpiCell, pci, earfcn, tp);
                }
            }
            targetKpiCell.CalcResult();
            return targetKpiCell;
        }

        private void getValidTPInfo(ref LTECell targetCell, ref int pci, ref int earfcn, TestPoint tp)
        {
            List<LTECell> cells = StationAcceptCellHelper_XJ.Instance.GetLTECellListByEarfcnPci(tp);
            foreach (var cell in cells)
            {
                if (cell.Name.Contains(btsName))
                {
                    targetCell = cell;
                    pci = (int)(short)tp["lte_PCI"];
                    earfcn = (int)tp["lte_EARFCN"];
                    break;
                }
            }
        }

        private static void addVliadTPCount(CellKPI targetKpiCell, int pci, int earfcn, TestPoint tp)
        {
            int? curPCI = (int?)(short?)tp["lte_PCI"];
            int? curEARFCN = (int?)tp["lte_EARFCN"];
            if ((curPCI != null && curEARFCN != null) && ((int)curPCI == pci && (int)curEARFCN == earfcn))
            {
                float? rsrp = (float?)tp["lte_RSRP"];
                if (rsrp != null && rsrp != -10000000)
                {
                    ++targetKpiCell.TotalTpCount;
                    if (rsrp <= -115)
                    {
                        ++targetKpiCell.ValidTpCount;
                    }
                }
            }
        }

        protected int GetColumnIndex(FileInfo fileInfo)
        {
            if (fileInfo.Name.ToUpper().Contains("PCI"))
            {
                return 0;
            }
            return -1;
        }

        protected void SetValue(LteTestAcceptResult result, int sectorID, int rowIdx, int colIdx, double value)
        {
            // 直接覆盖原有的值
            result.SetValue(sectorID, rowIdx, colIdx, value, true);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("锁频");
        }
        protected class CellKPI
        {
            public CellKPI(string PCI)
            {
                this.PCI = PCI;
            }
            public string PCI
            {
                get;
                private set;
            }

            public int ValidTpCount = 0;
            public int TotalTpCount = 0;
            public double DivulgeRate
            {
                get;
                private set;
            }

            public void CalcResult()
            {
                DivulgeRate = TotalTpCount == 0 ? double.MinValue : Math.Round((double)ValidTpCount / TotalTpCount, 4);
            }
        }
    }

    class AcpDivulge2Rate : AcpDivulgeRate
    {
        public AcpDivulge2Rate()
        {
            resultGrid = new string[9, 1, 3];
            int idx = 21;
            int step = 18;
            for (int i = 0; i < 9; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "ac" + row.ToString();
            }
        }
        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("扫频");
        }
        protected override CellKPI AnaFile(FileInfo fileInfo, DTFileDataManager fileManager, string PCI)
        {
            string EARFCN = getEARFCN(fileInfo.Name);
            if (string.IsNullOrEmpty(EARFCN))
            {
                log.Info(string.Format("文件{0}获取频点失败", fileInfo.Name));
            }
            LTECell targetCell = GetLeakOutScanTestCell(fileManager);
            if (targetCell == null)
            {
                return null;
            }

            CellKPI targetKpiCell = new CellKPI(PCI);
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                addTPCount(targetCell, targetKpiCell, tp);
            }
            targetKpiCell.CalcResult();
            return targetKpiCell;
        }

        public LTECell GetLeakOutScanTestCell(DTFileDataManager fileManager)
        {
            //根据文件名中的频点PCI获取宏目标室分小区
            string[] strArray = fileManager.FileName.Split('_');
            if (strArray.Length == 6)
            {
                string cellName = strArray[2];
                int? pci = getValidData(strArray[3], "PCI");
                int? earfcn = getValidData(strArray[4], "频点");

                DateTime time = DateTime.Now;
                if (fileManager.TestPoints.Count > 0)
                {
                    time = fileManager.TestPoints[0].DateTime;
                }
                List<LTECell> cells = StationAcceptCellHelper_XJ.Instance.GetLTECellListByEarfcnPci(time, earfcn, pci);
                foreach (LTECell cell in cells)
                {
                    if (cell.Name.Contains(cellName))
                    {
                        return cell;
                    }
                }
            }
            return null;
        }

        private int? getValidData(string str, string type)
        {
            int? data = null;
            if (str.Contains(type))
            {
                string strData = str.Replace(type, "");
                int iData;
                if (int.TryParse(strData, out iData))
                {
                    data = iData;
                }
            }
            return data;
        }


        private void addTPCount(LTECell targetCell, CellKPI targetKpiCell, TestPoint tp)
        {
            LTECell cell = StationAcceptCellHelper_XJ.Instance.GetLTECell(tp);
            if (cell == null)
            {
                return;
            }
            float? rsrp = (float?)tp["lte_RSRP"];
            if (rsrp != null && rsrp != -10000000)
            {
                ++targetKpiCell.TotalTpCount;
                if (cell.Type == LTEBTSType.Outdoor && cell.Token != targetCell.Token)
                {
                    bool hasValidTP = hasValidTestPoint(tp, targetCell.Token, rsrp);
                    if (hasValidTP)
                    {
                        ++targetKpiCell.ValidTpCount;
                    }
                }


                //if (cell.Type != LTEBTSType.Indoor && cell.Token != targetCell.Token)
                //{
                //    //addVliadTPCount(targetCell, targetKpiCell, tp, rsrp);

                //    bool hasValidTP = hasValidTestPoint(tp, token, rsrp);
                //    if (hasValidTP)
                //    {
                //        ++targetKpiCell.ValidTpCount;
                //    }
                //}
            }
        }

        //private void addVliadTPCount(LTECell targetCell, CellKPI targetKpiCell, TestPoint tp, float? rsrp)
        //{
        //    for (int i = 0; i < 10; i++)
        //    {
        //        LTECell nCell = MultiStationAutoAcceptManager.GetLeakOutScanTpNCell(tp, i);
        //        if (nCell != null && nCell.Token == targetCell.Token)
        //        {
        //            float? nRsrp = (float?)tp["lte_NCell_RSRP", i];
        //            if (nRsrp != null && rsrp - nRsrp > 10)
        //            {
        //                ++targetKpiCell.ValidTpCount;
        //            }
        //            break;
        //        }
        //    }
        //}

        private bool hasValidTestPoint(TestPoint tp, string token, float? rsrp)
        {
            for (int i = 0; i < 10; i++)
            {
                //获取邻区为目标室分小区的采样点
                LTECell nCell = MultiStationAutoAcceptManager.GetLeakOutScanTpNCell(tp, i);
                if (nCell != null && nCell.Token == token)
                {
                    float? nRsrp = (float?)tp["lte_NCell_RSRP", i];
                    //宏站主控小区与目标室分小区场强在10db以内则为泄漏点
                    if (nRsrp != null && rsrp - nRsrp <= 10)
                    {
                        return false;
                    }
                }
            }
            return true;
        }
    }

    class AcpLeveling : LteTestAcceptBase
    {
        public int FileCount { get { return fileList.Count; } }
        private readonly List<string> fileList = new List<string>();
        private readonly Dictionary<int, string[]> resultDic = new Dictionary<int, string[]>();
        public AcpLeveling()
        {
            resultGrid = new string[1, 1, 14] {
                {
                    { "a2", "b2", "c2", "d2", "e2", "f2", "g2", "h2", "i2", "j2", "k2", "l2", "m2", "n2" },
                },
            };
        }

        public Dictionary<string, LteTestAcceptResult> GetBtsResultDic()
        {
            return btsResultDic;
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string PCI, int sectorID)
        {
            if (!IsValidFile(fileInfo) || string.IsNullOrEmpty(btsName))
            {
                return;
            }
            fileList.Add(fileInfo.Name);

            int count = fileList.Count;

            int index = count + 1;
            resultDic[count - 1] = new string[14] { "a" + index, "b" + index, "c" + index, "d" + index, "e" + index, "f" + index, "g" + index, "h" + index
                        , "i" + index, "j" + index, "k" + index, "l" + index, "m" + index, "n" + index };


            AnaFile(fileInfo, fileManager, PCI);
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("上传") || fileInfo.Name.Contains("下载") || fileInfo.Name.Contains("切换");
        }

        public override void FillResult(string btsName, Excel.Workbook eBook, Dictionary<string, int> btsDic)
        {
            if (!btsResultDic.ContainsKey(btsName))
            {
                return;
            }
            FillResultToSheet(btsName, eBook, 3, new List<int>(btsDic.Values));
        }

        private void AnaFile(FileInfo fileInfo, DTFileDataManager fileManager, string PCI)
        {
            string btsName = GetBtsName(fileInfo.Name);
            CellKPI targetKpiCell = new CellKPI(PCI);

            LteTestAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }

            LTECell targeCell = null;
            int pci = -255;
            int earfcn = -255;

            foreach (TestPoint tp in fileManager.TestPoints)
            {
                if (targeCell == null)
                {
                    getValidTPInfo(ref targeCell, ref pci, ref earfcn, tp);
                }
                if (targeCell != null)
                {
                    addValidTP(targetKpiCell, pci, earfcn, tp);
                }
            }
            foreach (Event evt in fileManager.Events)
            {
                targetKpiCell.AddEvent(evt);
            }

            targetKpiCell.CalcResult();

            SetValue(result, fileInfo.Name, targetKpiCell, fileList.Count - 1);
        }

        private void getValidTPInfo(ref LTECell targeCell, ref int pci, ref int earfcn, TestPoint tp)
        {
            List<LTECell> cells = StationAcceptCellHelper_XJ.Instance.GetLTECellListByEarfcnPci(tp);

            foreach (var cell in cells)
            {
                if (cell.Name.Contains(btsName))
                {
                    targeCell = cell;
                    pci = (int)(short)tp["lte_PCI"];
                    earfcn = (int)tp["lte_EARFCN"];
                    break;
                }
            }
        }

        private static void addValidTP(CellKPI targetKpiCell, int pci, int earfcn, TestPoint tp)
        {
            int? curPCI = (int?)(short?)tp["lte_PCI"];
            int? curEARFCN = (int?)tp["lte_EARFCN"];
            if ((curPCI != null && curEARFCN != null) && ((int)curPCI == pci && (int)curEARFCN == earfcn))
            {
                targetKpiCell.AddPoint(tp);
            }
        }

        private void SetValue(LteTestAcceptResult result, string fileName, CellKPI cellKPI, int sectorID)
        {
            if (fileName.Contains("下载"))
            {
                setDLRes(result, fileName, cellKPI, sectorID);
            }
            else if (fileName.Contains("上传"))
            {
                setULRes(result, fileName, cellKPI, sectorID);
            }
            else if (fileName.Contains("切换"))
            {
                setHandOverRes(result, fileName, cellKPI, sectorID);
            }
        }

        private void setDLRes(LteTestAcceptResult result, string fileName, CellKPI cellKPI, int sectorID)
        {
            result.SetValue(sectorID, 0, 0, fileName, true);
            result.SetValue(sectorID, 0, 1, cellKPI.BalanceRate, true);
            result.SetValue(sectorID, 0, 3, cellKPI.AvgRsrp, true);
            result.SetValue(sectorID, 0, 4, cellKPI.AvgSinr, true);
            result.SetValue(sectorID, 0, 5, cellKPI.SINR_Rate1, true);
            result.SetValue(sectorID, 0, 6, cellKPI.SINR_Rate2, true);
            if (cellKPI.RSRP_SINRRate1 < 0.95)
            {
                Dictionary<bool, double> value = new Dictionary<bool, double>();
                value[true] = cellKPI.RSRP_SINRRate1;

                result.SetValue(sectorID, 0, 7, value, true);
            }
            else
            {
                result.SetValue(sectorID, 0, 7, cellKPI.RSRP_SINRRate1, true);
            }
            result.SetValue(sectorID, 0, 8, cellKPI.RSRP_SINRRate2, true);
            result.SetValue(sectorID, 0, 9, cellKPI.RSRP_Rate, true);
            if ((cellKPI.AvgDLSpeed < 0.45 && fileName.Contains("双路")) || (cellKPI.AvgDLSpeed < 0.28 && fileName.Contains("单路")))
            {
                Dictionary<bool, double> value = new Dictionary<bool, double>();
                value[true] = cellKPI.AvgDLSpeed;

                result.SetValue(sectorID, 0, 11, value, true);
            }
            else
            {
                result.SetValue(sectorID, 0, 11, cellKPI.AvgDLSpeed, true);
            }

            result.SetValue(sectorID, 0, 13, cellKPI.MaxDLSpeed, true);
        }

        private void setULRes(LteTestAcceptResult result, string fileName, CellKPI cellKPI, int sectorID)
        {
            result.SetValue(sectorID, 0, 0, fileName, true);
            result.SetValue(sectorID, 0, 1, cellKPI.BalanceRate, true);
            result.SetValue(sectorID, 0, 3, cellKPI.AvgRsrp, true);
            result.SetValue(sectorID, 0, 4, cellKPI.AvgSinr, true);
            if (cellKPI.RSRP_SINRRate1 < 0.95)
            {
                Dictionary<bool, double> value = new Dictionary<bool, double>();
                value[true] = cellKPI.RSRP_SINRRate1;

                result.SetValue(sectorID, 0, 7, value, true);
            }
            else
            {
                result.SetValue(sectorID, 0, 7, cellKPI.RSRP_SINRRate1, true);
            }

            result.SetValue(sectorID, 0, 6, cellKPI.SINR_Rate2, true);
            result.SetValue(sectorID, 0, 5, cellKPI.SINR_Rate1, true);
            result.SetValue(sectorID, 0, 8, cellKPI.RSRP_SINRRate2, true);
            result.SetValue(sectorID, 0, 9, cellKPI.RSRP_Rate, true);
            if (cellKPI.AvgULSpeed < 0.06)
            {
                Dictionary<bool, double> value = new Dictionary<bool, double>();
                value[true] = cellKPI.AvgULSpeed;

                result.SetValue(sectorID, 0, 10, value, true);
            }
            else
            {
                result.SetValue(sectorID, 0, 10, cellKPI.AvgULSpeed, true);
            }
            if (cellKPI.MaxULSpeed < 0.09)
            {
                Dictionary<bool, double> value = new Dictionary<bool, double>();
                value[true] = cellKPI.MaxULSpeed;

                result.SetValue(sectorID, 0, 12, value, true);
            }
            else
            {
                result.SetValue(sectorID, 0, 12, cellKPI.MaxULSpeed, true);
            }
        }

        private void setHandOverRes(LteTestAcceptResult result, string fileName, CellKPI cellKPI, int sectorID)
        {
            result.SetValue(sectorID, 0, 0, fileName, true);
            result.SetValue(sectorID, 0, 1, cellKPI.BalanceRate, true);
            if (cellKPI.HandoverRate < 0.98)
            {
                Dictionary<bool, double> value = new Dictionary<bool, double>();
                value[true] = cellKPI.HandoverRate;

                result.SetValue(sectorID, 0, 2, value, true);
            }
            else
            {
                result.SetValue(sectorID, 0, 2, cellKPI.HandoverRate, true);
            }

            result.SetValue(sectorID, 0, 3, cellKPI.AvgRsrp, true);
            result.SetValue(sectorID, 0, 4, cellKPI.AvgSinr, true);
            result.SetValue(sectorID, 0, 5, cellKPI.SINR_Rate1, true);
            result.SetValue(sectorID, 0, 6, cellKPI.SINR_Rate2, true);
            if (cellKPI.RSRP_SINRRate1 < 0.95)
            {
                Dictionary<bool, double> value = new Dictionary<bool, double>();
                value[true] = cellKPI.RSRP_SINRRate1;

                result.SetValue(sectorID, 0, 7, value, true);
            }
            else
            {
                result.SetValue(sectorID, 0, 7, cellKPI.RSRP_SINRRate1, true);
            }
            result.SetValue(sectorID, 0, 8, cellKPI.RSRP_SINRRate2, true);
            result.SetValue(sectorID, 0, 9, cellKPI.RSRP_Rate, true);
        }

        public override void ResetResult<T>(T data, Dictionary<string, int> btsDic)
        {
            if (data is AcpLevelingByAppSP)
            {
                AcpLevelingByAppSP appData = data as AcpLevelingByAppSP;

                LteTestAcceptResult result = null;
                if (!btsResultDic.TryGetValue(btsName, out result))
                {
                    result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                    btsResultDic.Add(btsName, result);
                }

                int index;
                foreach (var levelingRes in appData.LevelingResultList)
                {
                    index = resultDic.Count + 2;
                    resultDic[resultDic.Count] = new string[14] { "a" + index, "b" + index, "c" + index, "d" + index, "e" + index, "f" + index, "g" + index, "h" + index, "i" + index, "j" + index, "k" + index, "l" + index, "m" + index, "n" + index };

                    int sectorID = resultDic.Count - 1;
                    result.SetValue(sectorID, 0, 0, levelingRes.Name, true);
                    //result.SetValue(sectorID 0, 1, levelingRes., true);
                    //result.SetValue(sectorID, 0, 2, levelingRes., true);
                    result.SetValue(sectorID, 0, 3, levelingRes.AvgRsrp, true);
                    result.SetValue(sectorID, 0, 4, levelingRes.AvgSinr, true);
                    result.SetValue(sectorID, 0, 5, levelingRes.SinrMoreThan6Rate, true);
                    result.SetValue(sectorID, 0, 6, levelingRes.SinrMoreThan9Rate, true);
                    result.SetValue(sectorID, 0, 7, levelingRes.RsrpMoreThan105Rate, true);
                    result.SetValue(sectorID, 0, 8, levelingRes.RsrpMoreThan95Rate, true);
                    result.SetValue(sectorID, 0, 9, levelingRes.RsrpMoreThan85Rate, true);
                    result.SetValue(sectorID, 0, 10, levelingRes.AvgULSpeed, true);
                    result.SetValue(sectorID, 0, 11, levelingRes.AvgDLSpeed, true);
                    result.SetValue(sectorID, 0, 12, levelingRes.MaxULSpeed, true);
                    result.SetValue(sectorID, 0, 13, levelingRes.MaxDLSpeed, true);
                }
            }
        }

        protected override void FillResultToSheet(string btsName, Excel.Workbook eBook, int sheetIndex, List<int> sectorIDs)
        {
            if (!btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            LteTestAcceptResult result = btsResultDic[btsName];
            for (int i = 0; i < resultDic.Count; ++i)
            {
                int sid = i;
                for (int row = 0; row < 1; ++row)
                {
                    for (int col = 0; col < 14; ++col)
                    {
                        object value = result.GetValue(sid, row, col);
                        if (value is Dictionary<bool, double>)
                        {
                            Dictionary<bool, double> dic = value as Dictionary<bool, double>;
                            InsertExcelValue(eBook, sheetIndex, resultDic[i][col], dic[true], true);
                        }
                        else
                        {
                            InsertExcelValue(eBook, sheetIndex, resultDic[i][col], value);
                        }
                    }
                }
            }
        }

        protected class CellKPI
        {
            public CellKPI(string PCI)
            {
                this.PCI = PCI;
            }

            public string PCI
            {
                get;
                private set;
            }

            public int PointCount
            {
                get;
                private set;
            }

            public double BalanceRate
            {
                get;
                private set;
            }


            public double AvgRsrp
            {
                get;
                private set;
            }

            public double AvgSinr
            {
                get;
                private set;
            }

            public double AvgULSpeed
            {
                get;
                private set;
            }

            public double AvgDLSpeed
            {
                get;
                private set;
            }

            public double HandoverRate
            {
                get;
                private set;
            }

            public double SINR_Rate1
            {
                get;
                private set;
            }

            public double SINR_Rate2
            {
                get;
                private set;
            }

            public double RSRP_SINRRate1
            {
                get;
                private set;
            }

            public double RSRP_SINRRate2
            {
                get;
                private set;
            }
            public double RSRP_Rate
            {
                get;
                private set;
            }

            public void AddPoint(TestPoint tp)
            {
                ++PointCount;
                setRsrp0AndRsrp1(tp);

                float? rsrp = setRsrp(tp);

                float? sinr = setSinr(tp);

                setRsrpAndSinr(rsrp, sinr);

                setSpeed(tp);
            }

            private void setRsrp0AndRsrp1(TestPoint tp)
            {
                //双通道
                float? rsrp0 = (float?)tp["lte_RSRP_Rx0"];
                float? rsrp1 = (float?)tp["lte_RSRP_Rx1"];
                if (rsrp0 != null && rsrp1 != null)
                {
                    ++cntTotalBalance;
                    float rsrpAbs = (float)rsrp1 - (float)rsrp0;
                    if (Math.Abs(rsrpAbs) <= 6)
                    {
                        cntBalance++;
                    }
                }
            }

            private float? setRsrp(TestPoint tp)
            {
                float? rsrp = (float?)tp["lte_RSRP"];
                if (rsrp != null && rsrp != -10000000)
                {
                    ++cntRsrp;
                    sumRsrp += (float)rsrp;

                    if (rsrp >= -85)
                    {
                        RSRPRate++;
                    }
                }

                return rsrp;
            }

            private float? setSinr(TestPoint tp)
            {
                float? sinr = (float?)tp["lte_SINR"];
                if (sinr != null && sinr != -10000000)
                {
                    ++cntSinr;
                    sumSinr += (float)sinr;

                    if (sinr >= 6)
                    {
                        SINRRate++;
                    }
                    if (sinr >= 9)
                    {
                        SINRRate2++;
                    }
                }

                return sinr;
            }

            private void setRsrpAndSinr(float? rsrp, float? sinr)
            {
                if (rsrp != null && sinr != null)
                {
                    cntRSRP_SINR++;
                    if (rsrp >= -105 && sinr >= 6)
                    {
                        RSRPRate2++;
                    }

                    if (rsrp >= -95 && sinr >= 9)
                    {
                        RSRPRate3++;
                    }
                }

                if (sinr != null && sinr != -10000000 && sinr >= 6 && rsrp != null && rsrp != -10000000 && rsrp >= -105)
                {
                    ++RSCount;
                }
            }

            private void setSpeed(TestPoint tp)
            {
                double? ulSpeed = (double?)tp["lte_PDCP_UL_Mb"];
                if (ulSpeed != null)
                {
                    ++cntULSpeed;
                    sumULSpeed += (double)ulSpeed;

                    if (ulSpeed > MaxULSpeed)
                    {
                        MaxULSpeed = (float)ulSpeed;
                    }
                }

                double? dlSpeed = (double?)tp["lte_PDCP_DL_Mb"];
                if (dlSpeed != null)
                {
                    ++cntDLSpeed;
                    sumDLSpeed += (double)dlSpeed;
                    if (dlSpeed > MaxDLSpeed)
                    {
                        MaxDLSpeed = (float)dlSpeed;
                    }
                }
            }

            public void AddEvent(Event evt)
            {
                cntEvent++;
                if (evt.ID == evtFailed || evt.ID == evtFailed1)
                {
                    cntFailed++;
                }
                if (evt.ID == evtSucc || evt.ID == evtSucc1)
                {
                    cntSucc++;
                }
            }

            public void CalcResult()
            {
                AvgRsrp = cntRsrp == 0 ? double.MinValue : Math.Round(sumRsrp / cntRsrp, 2);
                AvgSinr = cntSinr == 0 ? double.MinValue : Math.Round(sumSinr / cntSinr, 2);
                AvgULSpeed = cntULSpeed == 0 ? double.MinValue : Math.Round(sumULSpeed / cntULSpeed, 2);
                AvgDLSpeed = cntDLSpeed == 0 ? double.MinValue : Math.Round(sumDLSpeed / cntDLSpeed, 2);
                BalanceRate = cntTotalBalance == 0 ? double.MinValue : Math.Round(cntBalance / (double)cntTotalBalance, 4);
                HandoverRate = cntSucc + cntFailed == 0 ? double.MinValue : (double)Math.Round((decimal)(cntSucc) / (decimal)(cntSucc + cntFailed)
                    , 4) * 100;
                SINR_Rate1 = cntSinr == 0 ? double.MinValue : Math.Round(SINRRate / (double)cntSinr, 4);
                SINR_Rate2 = cntSinr == 0 ? double.MinValue : Math.Round(SINRRate2 / (double)cntSinr, 4);
                RSRP_SINRRate1 = cntRSRP_SINR == 0 ? double.MinValue : Math.Round(RSRPRate2 / (double)cntRSRP_SINR, 4);
                RSRP_SINRRate2 = cntRSRP_SINR == 0 ? double.MinValue : Math.Round(RSRPRate3 / (double)cntRSRP_SINR, 4);
                RSRP_Rate = cntRsrp == 0 ? double.MinValue : Math.Round(RSRPRate / (double)cntRsrp, 4);

            }

            private readonly int evtFailed = 870;
            private readonly int evtFailed1 = 1100;
            private readonly int evtSucc = 851;
            private readonly int evtSucc1 = 899;

            private int cntEvent;
            private int cntFailed;
            private int cntSucc;

            private int cntBalance;
            private int cntTotalBalance;

            private double sumRsrp;
            private int cntRsrp;

            private double sumSinr;
            private int cntSinr;

            private double sumULSpeed;
            private int cntULSpeed;

            private double sumDLSpeed;
            private int cntDLSpeed;

            private int RSCount;

            private int SINRRate;
            private int SINRRate2;

            private int RSRPRate;
            private int RSRPRate2;
            private int RSRPRate3;

            private int cntRSRP_SINR;

            public double MaxDLSpeed = 0;
            public double MaxULSpeed = 0;
        }
    }

    class AcpHomePage : LteTestAcceptBase
    {
        private string BtsName = "";
        private string BtsID = "";

        private readonly List<string> fileNames = new List<string>();
        private readonly List<int> floorList = new List<int>();
        private int maxFloor = 0;
        private int minFloor = 1;
        private string strMaxFloor = "";
        private string strMinFloor = "";

        public int MaxFloor
        {
            get { return maxFloor; }
        }

        public int MinFloor
        {
            get { return minFloor; }
        }

        public List<int> FloorList
        {
            get { return floorList; }
        }

        public List<string> FileNames
        {
            get { return fileNames; }
        }
        public AcpHomePage()
        {
            resultGrid = new string[1, 3, 2] {
                {
                    { "e3", "z3" },
                    { "e5", "z5"},
                    { "e7", "z9"},
                },
            };
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string PCI, int sectorID)
        {
            if (!IsValidFile(fileInfo) || string.IsNullOrEmpty(btsName))
            {
                return;
            }
            if (string.IsNullOrEmpty(BtsName))
            {
                BtsName = GetBtsName(fileInfo.Name);
            }

            if (string.IsNullOrEmpty(BtsID))
            {
                setBtsID(fileManager);
            }

            GetFloor(fileInfo.Name);

            fileNames.Add(fileInfo.Name);

            LteTestAcceptResult result = null;
            if (!btsResultDic.TryGetValue(BtsName, out result))
            {
                result = new LteTestAcceptResult(BtsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(BtsName, result);
            }
            result.SetValue(sectorID, 0, 0, BtsName, true);
            result.SetValue(sectorID, 0, 1, DateTime.Now.ToString("yyyy-MM-dd"), true);
            result.SetValue(sectorID, 1, 0, BtsID, true);
            result.SetValue(sectorID, 2, 0, BtsName, true);
            result.SetValue(sectorID, 2, 1, strMinFloor + "-" + strMaxFloor, true);
        }

        private void setBtsID(DTFileDataManager fileManager)
        {
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                List<LTECell> cells = StationAcceptCellHelper_XJ.Instance.GetLTECellListByEarfcnPci(tp);
                foreach (var cell in cells)
                {
                    if (cell.Name.Contains(btsName) && cell.Name.Contains(BtsName))
                    {
                        LTEBTS lteBts = cell.BelongBTS;
                        BtsID = lteBts.ID.ToString();
                        break;
                    }
                }
                if (!string.IsNullOrEmpty(BtsID))
                {
                    break;
                }
            }
        }

        private void GetFloor(string fileName)
        {
            string[] names = fileName.Split('_');
            string name = "";
            foreach (string item in names)
            {
                string strFloorNumber = Regex.Match(item.Replace("负", "-").Replace("B", "-")
                    , @"-?\d+楼", RegexOptions.None).Value.Replace("楼", "");// @"-?\d+楼" 用正则表达式判断是否包含"n楼"字段,n为整数
                int floorNumber;
                if (int.TryParse(strFloorNumber, out floorNumber))
                {
                    name = strFloorNumber.Replace("-", "B") + "F";
                    if (floorNumber <= minFloor)
                    {
                        minFloor = floorNumber;
                        strMinFloor = name;
                    }
                    if (floorNumber >= maxFloor)
                    {
                        maxFloor = floorNumber;
                        strMaxFloor = name;
                    }
                    floorList.Add(floorNumber);
                    break;
                }
            }
        }

        public override void FillResult(string btsName, Excel.Workbook eBook, Dictionary<string, int> btsDic)
        {
            List<int> sectorID = new List<int>(btsDic.Values);
            if (sectorID.Count == 0)
            {
                return;
            }
            FillResultToSheet(btsName, eBook, 1, new List<int> { sectorID[0] });
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return fileInfo.Name.Contains("下载") || fileInfo.Name.Contains("上传");
        }

    }

    class AcpLostFloor : LteTestAcceptBase
    {
        public AcpLostFloor()
        {
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return false;
        }

        public void SetValue(List<int> floorList, int fileCount, int maxFloor, int minFloor, FileInfo fileInfo)
        {
            resultGrid = new string[1, 1, 1] {
                {
                    { "a"+ (fileCount + 3) },
                },
            };
            string name = "";
            for (int i = minFloor; i <= maxFloor; i++)
            {
                name = getNameStr(floorList, name, i);
            }
            if (!string.IsNullOrEmpty(name))
            {
                name += "文件...";
            }


            string BtsName = GetBtsName(fileInfo.Name);
            LteTestAcceptResult result = null;
            if (!btsResultDic.TryGetValue(BtsName, out result))
            {
                result = new LteTestAcceptResult(BtsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(BtsName, result);
            }
            result.SetValue(0, 0, 0, name, true);
        }

        private static string getNameStr(List<int> floorList, string name, int i)
        {
            if (i != 0 && !floorList.Contains(i))
            {
                if (string.IsNullOrEmpty(name))
                {
                    name = "缺少" + i + "F";
                }
                else
                {
                    name += "、" + i + "F";
                }
            }

            return name;
        }

        public override void FillResult(string btsName, Excel.Workbook eBook, Dictionary<string, int> btsDic)
        {
            List<int> sectorID = new List<int>(btsDic.Values);
            if (sectorID.Count == 0)
            {
                return;
            }
            FillResultToSheet(btsName, eBook, 3, new List<int> { sectorID[0] });
        }

    }

    class AcpHomePCI : LteTestAcceptBase
    {
        public AcpHomePCI()
        {
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            return false;
        }

        public void SetValue(Dictionary<string, int> sectorIDDic, FileInfo fileInfo)
        {
            resultGrid = new string[9, 1, 2] {
                {
                    { "l38", "k26" },
                },
                {
                    { "q38", "s26" },
                },
                {
                    { "v38", "AA26" },
                },
                {
                    { "l46", "k35" },
                },
                {
                    { "q46", "s35" },
                },
                {
                    { "v46", "AA35" },
                },
                {
                    { "l54", "ZZ1" },
                },
                {
                    { "q54", "ZZ1" },
                },
                {
                    { "v54", "ZZ1" },
                },
            };
            string BtsName = GetBtsName(fileInfo.Name);
            LteTestAcceptResult result = null;
            if (!btsResultDic.TryGetValue(BtsName, out result))
            {
                result = new LteTestAcceptResult(BtsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(BtsName, result);
            }
            foreach (var item in sectorIDDic)
            {
                result.SetValue(item.Value, 0, 0, "Cell-" + (item.Value + 1).ToString() + "(PCI:" + item.Key + ")", true);
            }
            if (fileInfo.Name.Contains("下载") && fileInfo.Name.Contains("单路"))
            {
                result.SetValue(0, 0, 1, "单路", true);
            }
            else if (fileInfo.Name.Contains("下载") && fileInfo.Name.Contains("双路"))
            {
                result.SetValue(0, 0, 1, "双路", true);
            }
        }

        public override void FillResult(string btsName, Excel.Workbook eBook, Dictionary<string, int> btsDic)
        {
            FillResultToSheet(btsName, eBook, 1, new List<int>(btsDic.Values));
        }
    }



    /// <summary>
    /// 查询App平台推送的平层测试数据 当从测试log中没有获取到数据时用ECI匹配感知平台测试数据
    /// 一个工单只存在一个小区,一个小区可能存在多个工单对应,因此要获取最新的一个工单对应的小区信息
    /// 还需要将当前站对应的所有小区数据查询出来
    /// </summary>
    class AcpLevelingByApp : LteTestAcceptBase
    {
        protected Dictionary<int, string[]> resultDic = new Dictionary<int, string[]>();
        //App分析的平层结果
        Dictionary<string, FddIndoorDBLevelingResult> levelingResult = new Dictionary<string, FddIndoorDBLevelingResult>();
        //起始行 就是log平层分析结果的后一行
        int startRow;

        public AcpLevelingByApp()
        {
            resultGrid = new string[1, 1, 14] {
                {
                    { "a2", "b2", "c2", "d2", "e2", "f2", "g2", "h2", "i2", "j2", "k2", "l2", "m2", "n2" },
                },
            };
        }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string PCI, int sectorID)
        {
            if (lteCell == null)
            {
                return;
            }

            if (levelingResult.ContainsKey(btsName))
            {
                return;
            }

            FddDatabaseSetting setting;
            if (!DiyQueryFddDBSetting.GetInstance().DatabaseSetting.TryGetValue("单验平层", out setting))
            {
                return;
            }

            List<FddIndoorDBLeveling> allCellLevelingResult = new List<FddIndoorDBLeveling>();

            //获取基站所有应测小区的平层测试数据(可能存在共站)
            foreach (var cell in lteCell.BelongBTS.Cells)
            {
                DiyQueryFddDBLeveling query = new DiyQueryFddDBLeveling(cell.ECI, setting);
                query.Query();
                allCellLevelingResult.AddRange(query.DataList);
            }

            levelingResult = combineFloorAndType(allCellLevelingResult);
        }

        private void InitResultGrid(int count)
        {
            for (int i = 1; i <= count; i++)
            {
                int index = startRow + i + 1;
                resultDic[i - 1] = new string[14] { "a" + index, "b" + index, "c" + index, "d" + index, "e" + index, "f" + index, "g" + index,
                "h" + index, "i" + index, "j" + index, "k" + index, "l" + index, "m" + index, "n" + index  };
            }
        }

        private Dictionary<string, FddIndoorDBLevelingResult> combineFloorAndType(List<FddIndoorDBLeveling> dataList)
        {
            //<楼宇楼层,平层数据>
            Dictionary<string, FddIndoorDBLevelingResult> curLevelingResult = new Dictionary<string, FddIndoorDBLevelingResult>();
            foreach (FddIndoorDBLeveling data in dataList)
            {
                FddIndoorDBLevelingResult levelingData;
                if (!curLevelingResult.TryGetValue(data.Name, out levelingData))
                {
                    levelingData = new FddIndoorDBLevelingResult();
                    curLevelingResult.Add(data.Name, levelingData);
                }

                levelingData.LevelingDataList.Add(data);
            }

            foreach (var item in curLevelingResult.Values)
            {
                item.CalculateData();
            }
            return curLevelingResult;
        }

        private void SaveResult(Dictionary<string, FddIndoorDBLevelingResult> levelingResult)
        {
            LteTestAcceptResult result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
            btsResultDic.Add(btsName, result);

            int sectorID = startRow;
            int row = 0;
            foreach (var data in levelingResult)
            {
                int col = 0;
                result.SetValue(sectorID, row, col++, data.Key, true);
                col++;//双通道功率平衡率
                col++;//切换成功率
                result.SetValue(sectorID, row, col++, data.Value.AvgRsrp, true);
                result.SetValue(sectorID, row, col++, data.Value.AvgSinr, true);
                result.SetValue(sectorID, row, col++, data.Value.SinrMoreThan6Rate, true);
                result.SetValue(sectorID, row, col++, data.Value.SinrMoreThan9Rate, true);
                result.SetValue(sectorID, row, col++, data.Value.RsrpMoreThan105Rate, true);
                result.SetValue(sectorID, row, col++, data.Value.RsrpMoreThan95Rate, true);
                result.SetValue(sectorID, row, col++, data.Value.RsrpMoreThan85Rate, true);
                result.SetValue(sectorID, row, col++, data.Value.AvgULSpeed, true);
                result.SetValue(sectorID, row, col++, data.Value.AvgDLSpeed, true);
                result.SetValue(sectorID, row, col++, data.Value.MaxULSpeed, true);
                result.SetValue(sectorID, row, col, data.Value.MaxDLSpeed, true);
                sectorID++;
            }
        }

        public void SetResult(Dictionary<string, LteTestAcceptResult> btsResultDicByLog)
        {
            if (btsResultDicByLog.ContainsKey(btsName))
            {
                //根据log分析结果设置起始行
                startRow = btsResultDicByLog[btsName].SectorCount;
            }

            //设置App分析的平层结果
            InitResultGrid(levelingResult.Count);
            SaveResult(levelingResult);
        }

        public override void FillResult(string btsName, Excel.Workbook eBook, Dictionary<string, int> btsDic)
        {
            List<int> sectorID = new List<int>(btsDic.Values);
            if (sectorID.Count == 0)
            {
                return;
            }
            FillResultToSheet(btsName, eBook, 3, sectorID);
        }

        protected override void FillResultToSheet(string btsName, Excel.Workbook eBook, int sheetIndex, List<int> sectorIDs)
        {
            if (!btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            LteTestAcceptResult result = btsResultDic[btsName];
            for (int sid = 0; sid < resultDic.Count; ++sid)
            {
                for (int col = 0; col < resultGrid.GetLength(2); ++col)
                {
                    object value = result.GetValue(sid + startRow, 0, col);
                    InsertExcelValue(eBook, sheetIndex, resultDic[sid][col], value);
                }
            }
        }
    }

    /// <summary>
    /// 查询App平台推送的覆盖图 一个站只执行一次
    /// </summary>
    class AcpCoverePicByApp : LteTestAcceptBase
    {
        public AcpCoverePicByApp()
        {
            //名字+图片
            resultGrid = new string[1, 3, 8] {
                {
                    { "a17", "j17", "s17", "AB17", "a18", "j18", "s18", "AB18"},
                    { "a39", "j39", "s39", "AB39", "a40", "j40", "s40", "AB40"},
                    { "a61", "j61", "s61", "AB61", "a62", "j62", "s62", "AB62"},
                },
            };
        }

        string serverCoverPicPath;
        string localCoverPicPath;

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string PCI, int sectorID)
        {
            if (lteCell == null)
            {
                return;
            }
            if (btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            FddDatabaseSetting setting;
            if (!DiyQueryFddDBSetting.GetInstance().DatabaseSetting.TryGetValue("单验覆盖图", out setting))
            {
                return;
            }

            //读取配置文件,获取本地图片根目录
            if (ZTStationAcceptance_XJ.Singleton<ZTStationAcceptance_XJ
                .TddIndoorStationAcceptConfigHelper>.Instance.LoadConfig())
            {
                return;
            }

            serverCoverPicPath = ZTStationAcceptance_XJ.Singleton<ZTStationAcceptance_XJ
                .TddIndoorStationAcceptConfigHelper>.Instance.ConfigInfo.ServerCoverPicPath;
            localCoverPicPath = ZTStationAcceptance_XJ.Singleton<ZTStationAcceptance_XJ
                .TddIndoorStationAcceptConfigHelper>.Instance.ConfigInfo.LocalCoverPicPath;

            Dictionary<int, DiyQueryFddDBCoverPic> cellPicInfo = new Dictionary<int, DiyQueryFddDBCoverPic>();

            foreach (var cell in lteCell.BelongBTS.Cells)
            {
                //查询数据库中对应的图片名
                DiyQueryFddDBCoverPic query = new DiyQueryFddDBCoverPic(cell.ECI, setting);
                query.Query();
                cellPicInfo.Add(cell.ECI, query);
            }

            List<FddIndoorDBCoverPic> picResultList = getPicResultList(cellPicInfo);

            SaveResult(picResultList);
        }

        private List<FddIndoorDBCoverPic> getPicResultList(Dictionary<int, DiyQueryFddDBCoverPic> cellPicInfo)
        {
            List<FddIndoorDBCoverPic> picResultList = new List<FddIndoorDBCoverPic>();
            if (cellPicInfo.Count == 1)
            {
                dealSingleCell(cellPicInfo, picResultList);
            }
            else
            {
                dealMutiCells(cellPicInfo, picResultList);
            }

            return picResultList;
        }

        private void dealSingleCell(Dictionary<int, DiyQueryFddDBCoverPic> cellPicInfo, List<FddIndoorDBCoverPic> picResultList)
        {
            //如果只有一个小区,则取一个小区的多个楼层图片,最多3个楼层
            int maxCount = 3;
            foreach (DiyQueryFddDBCoverPic picInfo in cellPicInfo.Values)
            {
                foreach (FddIndoorDBCoverPic data in picInfo.DataList)
                {
                    if (maxCount <= 0)
                    {
                        break;
                    }

                    picResultList.Add(data);
                    maxCount--;
                }
                downLoadPic(picInfo, picResultList);
            }
        }

        private void dealMutiCells(Dictionary<int, DiyQueryFddDBCoverPic> cellPicInfo, List<FddIndoorDBCoverPic> picResultList)
        {
            //多个小区,每个小区取一个楼层图片
            foreach (DiyQueryFddDBCoverPic picInfo in cellPicInfo.Values)
            {
                if (picInfo.DataList.Count > 0)
                {
                    picResultList.Add(picInfo.DataList[0]);
                    downLoadPic(picInfo, new List<FddIndoorDBCoverPic>() { picInfo.DataList[0] });
                }
            }
        }

        private void downLoadPic(DiyQueryFddDBCoverPic picInfo, List<FddIndoorDBCoverPic> picResult)
        {
            string orderID = picInfo.GetOrderID();
            if (!string.IsNullOrEmpty(orderID))
            {
                string serverCoverPicFilePath = serverCoverPicPath + System.IO.Path.DirectorySeparatorChar + orderID;
                log.Info("serverCoverPicPath" + serverCoverPicFilePath);
                string localCoverPicFilePath = localCoverPicPath + System.IO.Path.DirectorySeparatorChar + orderID;
                log.Info("localCoverPicPath" + localCoverPicFilePath);
                if (!System.IO.Directory.Exists(localCoverPicFilePath))
                {
                    System.IO.Directory.CreateDirectory(localCoverPicFilePath);
                }

                //下载图片到本地
                List<string> picPathList = initPicPath(picResult, serverCoverPicFilePath, localCoverPicFilePath);
                DownloadStationAcceptPic downloadQuery = new DownloadStationAcceptPic(picPathList, localCoverPicFilePath);
                downloadQuery.Query();
            }
        }

        private List<string> initPicPath(List<FddIndoorDBCoverPic> coverPicResult, string serverDirectoryName, string localDirectoryName)
        {
            List<string> picPathList = new List<string>();
            foreach (var coverPicData in coverPicResult)
            {
                addRealPicPath(serverDirectoryName, coverPicData.RsrpPicPath, picPathList);
                addRealPicPath(serverDirectoryName, coverPicData.SinrPicPath, picPathList);
                addRealPicPath(serverDirectoryName, coverPicData.FtpULPicPath, picPathList);
                addRealPicPath(serverDirectoryName, coverPicData.FtpDLPicPath, picPathList);

                coverPicData.RsrpPicPath = getRealPicPath(localDirectoryName, coverPicData.RsrpPicPath);
                coverPicData.SinrPicPath = getRealPicPath(localDirectoryName, coverPicData.SinrPicPath);
                coverPicData.FtpULPicPath = getRealPicPath(localDirectoryName, coverPicData.FtpULPicPath);
                coverPicData.FtpDLPicPath = getRealPicPath(localDirectoryName, coverPicData.FtpDLPicPath);
            }

            return picPathList;
        }

        private void addRealPicPath(string directoryName, string picPath, List<string> picPathList)
        {
            string realPath = "";
            if (!string.IsNullOrEmpty(picPath))
            {
                realPath = directoryName + System.IO.Path.DirectorySeparatorChar + picPath;
                picPathList.Add(realPath);
            }
        }

        private string getRealPicPath(string directoryName, string picPath)
        {
            string realPath = "";
            if (!string.IsNullOrEmpty(picPath))
            {
                realPath = directoryName + System.IO.Path.DirectorySeparatorChar + picPath;
            }
            return realPath;
        }

        private void SaveResult(List<FddIndoorDBCoverPic> coverPicResult)
        {
            LteTestAcceptResult result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
            btsResultDic.Add(btsName, result);

            int sectorID = 0;
            int row = 0;
            foreach (var data in coverPicResult)
            {
                int col = 0;
                result.SetValue(sectorID, row, col++, data.RsrpName, true);
                result.SetValue(sectorID, row, col++, data.SinrName, true);
                result.SetValue(sectorID, row, col++, data.FtpDLName, true);
                result.SetValue(sectorID, row, col++, data.FtpULName, true);
                result.SetValue(sectorID, row, col++, getPicFilePath(data.RsrpPicPath), true);
                result.SetValue(sectorID, row, col++, getPicFilePath(data.SinrPicPath), true);
                result.SetValue(sectorID, row, col++, getPicFilePath(data.FtpDLPicPath), true);
                result.SetValue(sectorID, row, col, getPicFilePath(data.FtpULPicPath), true);
                row++;
            }
        }

        private string getPicFilePath(string picPath)
        {
            if (string.IsNullOrEmpty(picPath) || !System.IO.File.Exists(picPath))
            {
                return "";
            }
            return picPath;
        }

        public override void FillResult(string btsName, Excel.Workbook eBook, Dictionary<string, int> btsDic)
        {
            List<int> sectorID = new List<int>(btsDic.Values);
            if (sectorID.Count == 0)
            {
                return;
            }
            FillResultToSheet(btsName, eBook, 4, sectorID);
        }

        protected override void FillResultToSheet(string btsName, Excel.Workbook eBook, int sheetIndex, List<int> sectorIDs)
        {
            if (!btsResultDic.ContainsKey(btsName))
            {
                return;
            }

            LteTestAcceptResult result = btsResultDic[btsName];
            for (int row = 0; row < resultGrid.GetLength(1); ++row)
            {
                for (int col = 0; col < resultGrid.GetLength(2); ++col)
                {
                    object value = result.GetValue(0, row, col);

                    if (col < 4 || value == null || value.ToString() == "")
                    {
                        InsertExcelValue(eBook, sheetIndex, resultGrid[0, row, col], value);
                    }
                    else
                    {
                        AcpAutoCoverPicture.InsertExcelFDDPicture(eBook, resultGrid[0, row, col], value.ToString(), 4, 15.0, 9.0);
                    }
                }
            }
        }
    }

    class AcpPerformanceByApp : LteTestAcceptBase
    {
        public AcpPerformanceByApp()
        {
        }

        //从APP库获取的基站所有小区性能结果
        public Dictionary<LTECell, AcceptPerformanceInfo> AllCellPerformanceResult { get; private set; }

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string PCI, int sectorID)
        {
            if (lteCell == null)
            {
                return;
            }

            if (AllCellPerformanceResult != null)
            {
                return;
            }

            FddDatabaseSetting setting;
            if (!DiyQueryFddDBSetting.GetInstance().DatabaseSetting.TryGetValue("APP单验性能测试SP", out setting))
            {
                return;
            }

            AllCellPerformanceResult = new Dictionary<LTECell, AcceptPerformanceInfo>();
            foreach (var cell in lteCell.BelongBTS.Cells)
            {
                DiyQueryAcceptPerformance_XJ query = new DiyQueryAcceptPerformance_XJ(cell.ECI, setting);
                query.Query();
                AllCellPerformanceResult.Add(cell, query.Result);
            }
        }
    }

    class AcpLevelingByAppSP : LteTestAcceptBase
    {
        //App分析的平层结果
        public List<AcceptLevelingInfo> LevelingResultList { get; private set; } 

        public override void AnalyzeFile(FileInfo fileInfo, DTFileDataManager fileManager, string PCI, int sectorID)
        {
            if (lteCell == null)
            {
                return;
            }

            if (LevelingResultList != null)
            {
                return;
            }

            FddDatabaseSetting setting;
            if (!DiyQueryFddDBSetting.GetInstance().DatabaseSetting.TryGetValue("APP单验平层测试SP", out setting))
            {
                return;
            }
            
            LevelingResultList = new List<AcceptLevelingInfo>();
            //获取基站所有应测小区的平层测试数据(可能存在共站)
            foreach (var cell in lteCell.BelongBTS.Cells)
            {
                DiyQueryAcceptLeveling_XJ query = new DiyQueryAcceptLeveling_XJ(cell.ECI, setting);
                query.Query();
                LevelingResultList.AddRange(query.ResultList);
            }
        }
    }

    class AcpVolteVoiceMo : AcpRrcRate
    {
        public AcpVolteVoiceMo()
        {
            evtRequList = new List<int>() { 1070 };
            evtSuccList = new List<int>() { 1072 };

            resultGrid = new string[9, 1, 2];
            int idx = 12;
            int step = 18;
            for (int i = 0; i < 9; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "p" + row.ToString();
                resultGrid[i, 0, 1] = "w" + row.ToString();
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            if (fileInfo.Momt != 1)
            {
                return false;
            }
            return fileInfo.Name.ToUpper().Contains("VOLTE");
        }

        protected override void SaveResult(FileInfo fileInfo, CellKPI kpiCell, int sectorID)
        {
            string btsName = GetBtsName(fileInfo.Name);

            int rowIdx = 0;
            LteTestAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }
            result.SetValue(sectorID, rowIdx, 0, kpiCell.RequestCnt, false);
            result.SetValue(sectorID, rowIdx, 1, kpiCell.SucceedCnt, false);
        }
    }

    class AcpVolteVoiceMt : AcpRrcRate
    {
        public AcpVolteVoiceMt()
        {
            evtRequList = new List<int>() { 1071 };
            evtSuccList = new List<int>() { 1073 };

            resultGrid = new string[9, 1, 2];
            int idx = 13;
            int step = 18;
            for (int i = 0; i < 9; i++)
            {
                int row = idx + i * step;
                resultGrid[i, 0, 0] = "P" + row.ToString();
                resultGrid[i, 0, 1] = "W" + row.ToString();
            }
        }

        public override bool IsValidFile(FileInfo fileInfo)
        {
            if (fileInfo.Momt != 2)
            {
                return false;
            }
            return fileInfo.Name.ToUpper().Contains("VOLTE");
        }

        protected override void SaveResult(FileInfo fileInfo, CellKPI kpiCell, int sectorID)
        {
            string btsName = GetBtsName(fileInfo.Name);

            int rowIdx = 0;
            LteTestAcceptResult result = null;
            if (!btsResultDic.TryGetValue(btsName, out result))
            {
                result = new LteTestAcceptResult(btsName, resultGrid.GetLength(1), resultGrid.GetLength(2));
                btsResultDic.Add(btsName, result);
            }
            result.SetValue(sectorID, rowIdx, 0, kpiCell.RequestCnt, false);
            result.SetValue(sectorID, rowIdx, 1, kpiCell.SucceedCnt, false);
        }
    }
}
