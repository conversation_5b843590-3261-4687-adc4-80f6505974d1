﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Drawing.Text;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraTreeList;
using DevExpress.XtraTreeList.Nodes;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.Util.DevControlManager;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.Model.BaseInfo
{
    public partial class UserDataSrcRightsOptionForm : BaseForm
    {
        private List<User> modifiedUsers = new List<User>();
        private List<DataSourceRole> modifiedRoles = new List<DataSourceRole>();

        private void addModifiedRole(DataSourceRole role)
        {
            if (!modifiedRoles.Contains(role))
            {
                modifiedRoles.Add(role);
            }
        }
        private void addModifiedUser(User usr)
        {
            if (!modifiedUsers.Contains(usr))
            {
                modifiedUsers.Add(usr);
            }
        }
        public UserDataSrcRightsOptionForm(MainModel mm)
            : base(mm)
        {
            InitializeComponent();
            if (!mm.PermissionManager.HasQueriedUsers)
            {
                QueryUserPermission query = new QueryUserPermission(mm);
                query.Query();
            }
            TreeListHelper.ThreeStateControl(treeListDataSrc);
            TreeListHelper.ThreeStateControl(treeListRole);
            TreeListHelper.ThreeStateControl(treeListArea);
            fillViews();
        }

        private void fillViews()
        {
            gvUser.SelectionChanged -= gvUser_SelectionChanged;
            fillUserView();
            fillRoleView();
            gvUser.SelectionChanged += gvUser_SelectionChanged;
        }

        int lastDistrictID = -1;
        private void fillDataSrcView(int districtID)
        {
            if (lastDistrictID == districtID)
            {
                return;
            }
            lastDistrictID = districtID;
            try
            {
                treeListDataSrc.BeginUpdate();
                treeListDataSrc.AfterCheckNode -= treeListDataSrc_AfterCheckNode;
                treeListDataSrc.Nodes.Clear();
                treeListDataSrc.BeginUnboundLoad();
                makeCategoryNode(treeListDataSrc, MainModel.PermissionManager.GetDataSourceCategory(districtID, "Carrier"));
                makeCategoryNode(treeListDataSrc, MainModel.PermissionManager.GetDataSourceCategory(districtID, "Project"));
                makeCategoryNode(treeListDataSrc, MainModel.PermissionManager.GetDataSourceCategory(districtID, "ServiceType"));
                makeCategoryNode(treeListDataSrc, MainModel.PermissionManager.GetDataSourceCategory(districtID, "Agent"));
                treeListDataSrc.AfterCheckNode += treeListDataSrc_AfterCheckNode;
                treeListDataSrc.EndUnboundLoad();
                treeListDataSrc.Refresh();
                treeListDataSrc.EndUpdate();

                treeListArea.BeginUpdate();
                treeListArea.Nodes.Clear();
                treeListArea.BeginUnboundLoad();
                treeListArea.AfterCheckNode -= treeListArea_AfterCheckNode;
                List<CategoryEnumItem> areaTypeSet = mainModel.PermissionManager.GetAreaTypeItems(districtID);
                if (areaTypeSet != null)
                {
                    foreach (CategoryEnumItem item in areaTypeSet)
                    {
                        TreeListNode root = treeListArea.AppendNode(new object[] { item.Name, item.Description }, null);
                        root.Tag = item;
                        if (item.Childrents != null)
                        {
                            foreach (CategoryEnumItem itemArea in item.Childrents)
                            {
                                TreeListNode node = treeListArea.AppendNode(new object[] { itemArea.Name, itemArea.Description }, root);
                                node.Tag = itemArea;
                            }
                        }
                    }
                }
                treeListArea.AfterCheckNode += treeListArea_AfterCheckNode;
                treeListArea.EndUnboundLoad();
                treeListArea.EndUpdate();
                treeListArea.Refresh();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
        }

        void treeListArea_AfterCheckNode(object sender, NodeEventArgs e)
        {
            if (curSelRole != null)
            {
                updateRolePermission(e.Node);
            }
        }

        void treeListDataSrc_AfterCheckNode(object sender, NodeEventArgs e)
        {
            if (curSelRole == null)
            {
                return;
            }
            addModifiedRole(curSelRole);
            if (e.Node.ParentNode != null)
            {
                CategoryEnum cate = e.Node.ParentNode.Tag as CategoryEnum;
                CategoryEnumItem item = e.Node.Tag as CategoryEnumItem;
                if (item != null)
                {
                    curSelRole.UpdatePermission(cate.Name, item.ID, e.Node.Checked, true);
                }
            }
            else if (e.Node.HasChildren)
            {
                CategoryEnum cate = e.Node.Tag as CategoryEnum;
                foreach (TreeListNode subNode in e.Node.Nodes)
                {
                    CategoryEnumItem item = subNode.Tag as CategoryEnumItem;
                    if (item != null)
                    {
                        curSelRole.UpdatePermission(cate.Name, item.ID, subNode.Checked, true);
                    }
                }
            }
        }

        private void updateRolePermission(TreeListNode node)
        {
            addModifiedRole(curSelRole);

            if (node.ParentNode != null)
            {
                CategoryEnumItem cate = node.ParentNode.Tag as CategoryEnumItem;
                CategoryEnumItem item = node.Tag as CategoryEnumItem;
                if (item != null)
                {
                    curSelRole.UpdatePermission(cate.ID, item.ID, node.Checked, true);
                }
            }
            else
            {
                updateParentNode(node);
            }
        }

        private void updateParentNode(TreeListNode node)
        {
            CategoryEnumItem cate = node.Tag as CategoryEnumItem;
            if (cate.Childrents == null || cate.Childrents.Count == 0)
            {
                //修改不包含子节点的父节点
                curSelRole.UpdatePermission(cate.ID, -1, node.Checked, true);
            }
            else
            {
                //修改父节点下的子节点
                foreach (TreeListNode subNode in node.Nodes)
                {
                    CategoryEnumItem item = subNode.Tag as CategoryEnumItem;
                    List<int> areaDic;
                    if (item != null)
                    {
                        //如果子节点已添加则不更改
                        if (curSelRole.AreaDic.TryGetValue(cate.ID, out areaDic) && areaDic.Contains(item.ID))
                        {
                            continue;
                        }
                        curSelRole.UpdatePermission(cate.ID, item.ID, subNode.Checked, true);
                    }
                }
            }
        }

        private void makeCategoryNode(TreeList treeView, CategoryEnum cate)
        {
            if (cate==null)
            {
                return;
            }
            TreeListNode cateNode = treeView.AppendNode(new object[] { cate.Name, cate.Description }, null);
            cateNode.Tag = cate;
            cateNode.HasChildren = cate.Items.Length > 0;
            foreach (CategoryEnumItem item in cate.Items)
            {
                TreeListNode subNode = treeView.AppendNode(new object[] { item.Name, item.Description }, cateNode);
                subNode.Tag = item;
                subNode.HasChildren = false;
            }
        }

        private void fillUserView()
        {
            gridCtrlUser.BeginInit();
            gridCtrlUser.DataSource = MainModel.PermissionManager.Users;
            gridCtrlUser.RefreshDataSource();
            gridCtrlUser.EndInit();
        }

        private void fillRoleView()
        {
            try
            {
            treeListDataSrc.BeginUnboundLoad();
            treeListRole.FocusedNodeChanged -= treeListRole_FocusedNodeChanged;
            treeListRole.AfterCheckNode -= treeListRole_AfterCheckNode;
            treeListRole.Nodes.Clear();

            foreach (DataSourceRole role in MainModel.PermissionManager.DataSourceRoles)
            {
                TreeListNode root = treeListRole.AppendNode(new object[] {role.DistrictName, role.Name, role.Description }, null);
                root.Tag = role;
            }
            treeListRole.FocusedNodeChanged += treeListRole_FocusedNodeChanged;
            treeListRole.AfterCheckNode += treeListRole_AfterCheckNode;
            treeListDataSrc.EndUnboundLoad();
            treeListDataSrc.Refresh();
            if (treeListRole.Nodes.Count > 0)
            {
                TreeListNode node = treeListRole.Nodes[0];
                treeListRole.SetFocusedNode(node);
                fillDataSrcView(((DataSourceRole)node.Tag).DistrictID);
                setDataSrcViewCheckState((DataSourceRole)node.Tag);
            }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
        }

        private void setDataSrcViewCheckState(DataSourceRole role)
        {
            if (role == null)
            {
                treeListDataSrc.Enabled = false;
                treeListArea.Enabled = false;
                return;
            }
            else
            {
                treeListDataSrc.Enabled = true;
                treeListArea.Enabled = true;
            }

            curSelRole = role;

            treeListDataSrc.BeginUpdate();
            treeListArea.BeginUpdate();
            dealArea(role);

            dealDataSrc(role);
            treeListDataSrc.EndUpdate();
            treeListArea.EndUpdate();
        }

        private void dealArea(DataSourceRole role)
        {
            foreach (TreeListNode root in treeListArea.Nodes)
            {
                root.CheckState = CheckState.Unchecked;
                CategoryEnumItem cate = root.Tag as CategoryEnumItem;
                if (cate.Childrents == null || cate.Childrents.Count == 0)
                {
                    root.Checked = role.HasAreaRight(cate.ID, -1);
                }
                else
                {
                    dealCheckState(hasAreaRight, role, root, cate.ID);
                }

                chkAllArea.CheckedChanged -= chkAllArea_CheckedChanged;
                chkAllArea.Checked = role.HasAllAreaRightIncludeNew;
                if (role.HasAllAreaRightIncludeNew)
                {
                    this.treeListArea.Enabled = false;
                }
                chkAllArea.CheckedChanged += chkAllArea_CheckedChanged;
            }
        }

        private void dealDataSrc(DataSourceRole role)
        {
            foreach (TreeListNode root in treeListDataSrc.Nodes)
            {
                root.CheckState = CheckState.Unchecked;
                CategoryEnum cate = root.Tag as CategoryEnum;
                if (root.Nodes.Count > 0)
                {
                    dealCheckState(hasCategoryRight, role, root, cate.Name);
                }
                else
                {
                    root.Checked = role.HasCategoryRight(cate.Name, -1);
                }
            }
        }

        delegate bool Func(DataSourceRole role, object obj, int id);

        private bool hasAreaRight(DataSourceRole role, object obj, int id)
        {
            return role.HasAreaRight((int)obj, id);
        }

        private bool hasCategoryRight(DataSourceRole role, object obj, int id)
        {
            return role.HasCategoryRight(obj.ToString(), id);
        }

        private void dealCheckState(Func func, DataSourceRole role, TreeListNode root, object obj)
        {
            int checkCnt = 0;
            foreach (TreeListNode subNode in root.Nodes)
            {
                CategoryEnumItem sub = subNode.Tag as CategoryEnumItem;
                subNode.Checked = func(role, obj, sub.ID);
                if (subNode.Checked)
                {
                    checkCnt++;
                }
            }
            if (checkCnt != 0 && checkCnt == root.Nodes.Count)
            {
                root.CheckState = CheckState.Checked;
                root.Checked = true;
            }
            else if (checkCnt > 0)
            {
                root.CheckState = CheckState.Indeterminate;
            }
        }

        void chkAllArea_CheckedChanged(object sender, EventArgs e)
        {
            if (curSelRole==null)
            {
                return;
            }
            this.curSelRole.HasAllAreaRightIncludeNew = chkAllArea.Checked;
            this.treeListArea.Enabled = !chkAllArea.Checked;

            curSelRole.AreaDic.Clear();
            curSelRole.AreaModifyDic.Clear();
            if (chkAllArea.Checked)
            {
                this.addModifiedRole(this.curSelRole);
            }
            else
            {
                foreach (TreeListNode node in this.treeListArea.Nodes)
                {
                    updateRolePermission(node);
                }
            }
        }

        private void treeListRole_AfterCheckNode(object sender, DevExpress.XtraTreeList.NodeEventArgs e)
        {
            if (e.Node.Tag is DataSourceRole && curSelUser != null)
            {
                DataSourceRole role = e.Node.Tag as DataSourceRole;
                if (role != null)
                {
                    curSelUser.UpdateDataSourceRole(role.DistrictID, role.ID, e.Node.Checked);
                    gridCtrlUser.RefreshDataSource();
                    addModifiedUser(curSelUser);
                }
            }
        }
        DataSourceRole curSelRole = null;
        void treeListRole_FocusedNodeChanged(object sender, DevExpress.XtraTreeList.FocusedNodeChangedEventArgs e)
        {
            if (e.Node != null && e.Node.Tag is DataSourceRole)
            {
                grpDataSrc.Enabled = true;
                DataSourceRole role=e.Node.Tag as DataSourceRole;
                fillDataSrcView(role.DistrictID);
                setDataSrcViewCheckState(role);
            }
            else
            {
                grpDataSrc.Enabled = false;
                curSelRole = null;
            }
        }

        User curSelUser = null;
        private void gvUser_SelectionChanged(object sender, DevExpress.Data.SelectionChangedEventArgs e)
        {
            int[] rowsHandle = gvUser.GetSelectedRows();
            if (rowsHandle.Length > 0)
            {
                setRoleViewCheckState((User)gvUser.GetRow(rowsHandle[0]));
            }
        }
        private void setRoleViewCheckState(User selUser)
        {
            curSelUser = selUser;
            if (curSelUser == null)
            {
                return;
            }
            treeListRole.BeginUpdate();
            treeListRole.FocusedNodeChanged -= treeListRole_FocusedNodeChanged;
            treeListRole.AfterCheckNode -= treeListRole_AfterCheckNode;
            foreach (TreeListNode node in treeListRole.Nodes)
            {
                node.Visible = false;
                DataSourceRole r = node.Tag as DataSourceRole;
                if (selUser.CityID == -1 || selUser.CityID == r.DistrictID)
                {
                    node.Visible = true;
                }
                node.Checked = curSelUser.HasDataSrcRoleRight(r.DistrictID, r.ID);
            }
            treeListRole.FocusedNodeChanged += treeListRole_FocusedNodeChanged;
            treeListRole.AfterCheckNode += treeListRole_AfterCheckNode;
            treeListRole.EndUpdate();


            if (treeListRole.VisibleNodesCount == 0)
            {
                grpDataSrc.Enabled = false;
            }
            else
            {
                grpDataSrc.Enabled = true;
                setFocusedNode();
            }
        }

        private void setFocusedNode()
        {
            if (!treeListRole.FocusedNode.Visible)
            {
                foreach (TreeListNode node in treeListRole.Nodes)
                {
                    if (node.Visible)
                    {
                        treeListRole.SetFocusedNode(node);
                        break;
                    }
                }
            }
        }

        private void btnAddRole_Click(object sender, EventArgs e)
        {
            NewRoleDlg dlg = new NewRoleDlg(this.curSelUser == null ? -1 : this.curSelUser.CityID);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                DataSourceRole role = dlg.Role;
                addModifiedRole(role);
                MainModel.PermissionManager.AddRole(role);
                fillRoleView();
                setRoleViewCheckState(curSelUser);
            }
        }

        private void btnSubmit_Click(object sender, EventArgs e)
        {
            if (modifiedUsers.Count == 0 && modifiedRoles.Count == 0)
            {
                MessageBox.Show("当前无修改！");
                return;
            }
            WaitTextBox.Show("正在更新数据...", submit2DBInThread);
        }

        private void submit2DBInThread()
        {
            bool succ = true;
            try
            {
                if (modifiedUsers.Count > 0)
                {
                    UpdateUserDataSourceRole updateUser = new UpdateUserDataSourceRole(MainModel, modifiedUsers);
                    updateUser.Query();
                    modifiedUsers.Clear();
                }
                if (modifiedRoles.Count > 0)
                {
                    SubmitDataSourceRolesInfo submitRoles = new SubmitDataSourceRolesInfo(MainModel, modifiedRoles);
                    submitRoles.Query();
                    modifiedRoles.Clear();
                }
            }
            catch (Exception ex)
            {
                succ = false;
                MessageBox.Show("更新失败！" + ex.ToString());
            }
            finally
            {
                System.Threading.Thread.Sleep(100);
                WaitTextBox.Close();
                if (succ)
                {
                    MessageBox.Show(this, "更新完毕，将在下次登录系统时生效！");
                }
            }
        }


    }
}
