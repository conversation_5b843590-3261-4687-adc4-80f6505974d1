﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class FreqValueSettingBox
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.textBoxMaxValue = new System.Windows.Forms.TextBox();
            this.textBoxMinValue = new System.Windows.Forms.TextBox();
            this.textBoxValue = new System.Windows.Forms.TextBox();
            this.radioBtnRange = new System.Windows.Forms.RadioButton();
            this.radioBtnValue = new System.Windows.Forms.RadioButton();
            this.labelRange = new System.Windows.Forms.Label();
            this.buttonCancel = new System.Windows.Forms.Button();
            this.buttonOK = new System.Windows.Forms.Button();
            this.errorProvider = new System.Windows.Forms.ErrorProvider(this.components);
            this.label4 = new System.Windows.Forms.Label();
            this.textBoxBandName = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.cmbParentFreqBand = new System.Windows.Forms.ComboBox();
            ((System.ComponentModel.ISupportInitialize)(this.errorProvider)).BeginInit();
            this.SuspendLayout();
            // 
            // textBoxMaxValue
            // 
            this.textBoxMaxValue.Location = new System.Drawing.Point(345, 96);
            this.textBoxMaxValue.Name = "textBoxMaxValue";
            this.textBoxMaxValue.Size = new System.Drawing.Size(156, 22);
            this.textBoxMaxValue.TabIndex = 113;
            this.textBoxMaxValue.TextChanged += new System.EventHandler(this.textBoxMaxValue_TextChanged);
            this.textBoxMaxValue.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.textBox_KeyPress);
            // 
            // textBoxMinValue
            // 
            this.textBoxMinValue.Location = new System.Drawing.Point(116, 96);
            this.textBoxMinValue.Name = "textBoxMinValue";
            this.textBoxMinValue.Size = new System.Drawing.Size(156, 22);
            this.textBoxMinValue.TabIndex = 112;
            this.textBoxMinValue.TextChanged += new System.EventHandler(this.textBoxMinValue_TextChanged);
            this.textBoxMinValue.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.textBox_KeyPress);
            // 
            // textBoxValue
            // 
            this.textBoxValue.Location = new System.Drawing.Point(116, 56);
            this.textBoxValue.Name = "textBoxValue";
            this.textBoxValue.Size = new System.Drawing.Size(156, 22);
            this.textBoxValue.TabIndex = 111;
            this.textBoxValue.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.textBox_KeyPress);
            // 
            // radioBtnRange
            // 
            this.radioBtnRange.AutoSize = true;
            this.radioBtnRange.Location = new System.Drawing.Point(27, 98);
            this.radioBtnRange.Name = "radioBtnRange";
            this.radioBtnRange.Size = new System.Drawing.Size(73, 18);
            this.radioBtnRange.TabIndex = 110;
            this.radioBtnRange.Text = "设置范围";
            this.radioBtnRange.UseVisualStyleBackColor = true;
            // 
            // radioBtnValue
            // 
            this.radioBtnValue.AutoSize = true;
            this.radioBtnValue.Checked = true;
            this.radioBtnValue.Location = new System.Drawing.Point(27, 58);
            this.radioBtnValue.Name = "radioBtnValue";
            this.radioBtnValue.Size = new System.Drawing.Size(61, 18);
            this.radioBtnValue.TabIndex = 109;
            this.radioBtnValue.TabStop = true;
            this.radioBtnValue.Text = "设置值";
            this.radioBtnValue.UseVisualStyleBackColor = true;
            this.radioBtnValue.CheckedChanged += new System.EventHandler(this.radioBtnValue_CheckedChanged);
            // 
            // labelRange
            // 
            this.labelRange.AutoSize = true;
            this.labelRange.Location = new System.Drawing.Point(282, 100);
            this.labelRange.Name = "labelRange";
            this.labelRange.Size = new System.Drawing.Size(40, 14);
            this.labelRange.TabIndex = 108;
            this.labelRange.Text = "≤ X ≤";
            // 
            // buttonCancel
            // 
            this.buttonCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.buttonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.buttonCancel.Location = new System.Drawing.Point(427, 170);
            this.buttonCancel.Name = "buttonCancel";
            this.buttonCancel.Size = new System.Drawing.Size(87, 27);
            this.buttonCancel.TabIndex = 107;
            this.buttonCancel.Text = "取消";
            this.buttonCancel.UseVisualStyleBackColor = true;
            // 
            // buttonOK
            // 
            this.buttonOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.buttonOK.Location = new System.Drawing.Point(329, 170);
            this.buttonOK.Name = "buttonOK";
            this.buttonOK.Size = new System.Drawing.Size(87, 27);
            this.buttonOK.TabIndex = 106;
            this.buttonOK.Text = "确定";
            this.buttonOK.UseVisualStyleBackColor = true;
            this.buttonOK.Click += new System.EventHandler(this.buttonOK_Click);
            // 
            // errorProvider
            // 
            this.errorProvider.ContainerControl = this;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(27, 20);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(83, 14);
            this.label4.TabIndex = 117;
            this.label4.Text = "设置频段名称:";
            // 
            // textBoxBandName
            // 
            this.textBoxBandName.Location = new System.Drawing.Point(116, 17);
            this.textBoxBandName.Name = "textBoxBandName";
            this.textBoxBandName.Size = new System.Drawing.Size(156, 22);
            this.textBoxBandName.TabIndex = 116;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(24, 138);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(71, 14);
            this.label1.TabIndex = 118;
            this.label1.Text = "选择父频段:";
            // 
            // cmbParentFreqBand
            // 
            this.cmbParentFreqBand.FormattingEnabled = true;
            this.cmbParentFreqBand.Location = new System.Drawing.Point(116, 135);
            this.cmbParentFreqBand.Name = "cmbParentFreqBand";
            this.cmbParentFreqBand.Size = new System.Drawing.Size(156, 22);
            this.cmbParentFreqBand.TabIndex = 119;
            // 
            // FreqValueSettingBox
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(528, 211);
            this.Controls.Add(this.cmbParentFreqBand);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.textBoxBandName);
            this.Controls.Add(this.textBoxMaxValue);
            this.Controls.Add(this.textBoxMinValue);
            this.Controls.Add(this.textBoxValue);
            this.Controls.Add(this.radioBtnRange);
            this.Controls.Add(this.radioBtnValue);
            this.Controls.Add(this.labelRange);
            this.Controls.Add(this.buttonCancel);
            this.Controls.Add(this.buttonOK);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FreqValueSettingBox";
            this.Text = "设置";
            ((System.ComponentModel.ISupportInitialize)(this.errorProvider)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private System.Windows.Forms.TextBox textBoxMaxValue;
        private System.Windows.Forms.TextBox textBoxMinValue;
        private System.Windows.Forms.TextBox textBoxValue;
        private System.Windows.Forms.RadioButton radioBtnRange;
        private System.Windows.Forms.RadioButton radioBtnValue;
        private System.Windows.Forms.Label labelRange;
        protected System.Windows.Forms.Button buttonCancel;
        protected System.Windows.Forms.Button buttonOK;
        protected System.Windows.Forms.ErrorProvider errorProvider;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TextBox textBoxBandName;
        private System.Windows.Forms.ComboBox cmbParentFreqBand;
        private System.Windows.Forms.Label label1;
    }
}