﻿namespace MasterCom.RAMS
{
    partial class CPUnitShowForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.contextMenuStripWhole = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemExportCurrent = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemExportAll = new System.Windows.Forms.ToolStripMenuItem();
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemCellHost = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemCellGuest = new System.Windows.Forms.ToolStripMenuItem();
            this.splitContainerControl3 = new DevExpress.XtraEditors.SplitContainerControl();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.cbxEditRegion = new DevExpress.XtraEditors.ComboBoxEdit();
            this.listView = new System.Windows.Forms.ListView();
            this.columnHeader1 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader3 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.grpPointInfo = new DevExpress.XtraEditors.GroupControl();
            this.lvDetails = new BrightIdeasSoftware.TreeListView();
            this.colItemName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colBlockCnt = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colGridCnt = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colHostDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colGuestDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colBlockRoadDesc = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colRoadsCenterPntDesc = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLng = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colLat = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colHostValueMin = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colHostValueMax = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colHostValueAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colGuestValueMin = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colGuestValueMax = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.colGuestValueAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColHostGuestDiff = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.panel1 = new System.Windows.Forms.Panel();
            this.btnExportShp = new DevExpress.XtraEditors.SimpleButton();
            this.btnExportBriefTxt = new DevExpress.XtraEditors.SimpleButton();
            this.cbxDisplayMode = new System.Windows.Forms.ComboBox();
            this.btnExportBrief = new DevExpress.XtraEditors.SimpleButton();
            this.btnExport2Txt = new DevExpress.XtraEditors.SimpleButton();
            this.btnExport = new DevExpress.XtraEditors.SimpleButton();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.numCombineMinCnt = new System.Windows.Forms.NumericUpDown();
            this.contextMenuStripWhole.SuspendLayout();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl3)).BeginInit();
            this.splitContainerControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxEditRegion.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpPointInfo)).BeginInit();
            this.grpPointInfo.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lvDetails)).BeginInit();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numCombineMinCnt)).BeginInit();
            this.SuspendLayout();
            // 
            // contextMenuStripWhole
            // 
            this.contextMenuStripWhole.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemExportCurrent,
            this.ToolStripMenuItemExportAll});
            this.contextMenuStripWhole.Name = "contextMenuStripWhole";
            this.contextMenuStripWhole.Size = new System.Drawing.Size(149, 48);
            this.contextMenuStripWhole.Opening += new System.ComponentModel.CancelEventHandler(this.contextMenuStripWhole_Opening);
            // 
            // ToolStripMenuItemExportCurrent
            // 
            this.ToolStripMenuItemExportCurrent.Name = "ToolStripMenuItemExportCurrent";
            this.ToolStripMenuItemExportCurrent.Size = new System.Drawing.Size(148, 22);
            this.ToolStripMenuItemExportCurrent.Text = "导出当前区域";
            this.ToolStripMenuItemExportCurrent.Click += new System.EventHandler(this.ToolStripMenuItemExportCurrent_Click);
            // 
            // ToolStripMenuItemExportAll
            // 
            this.ToolStripMenuItemExportAll.Name = "ToolStripMenuItemExportAll";
            this.ToolStripMenuItemExportAll.Size = new System.Drawing.Size(148, 22);
            this.ToolStripMenuItemExportAll.Text = "导出所有区域";
            this.ToolStripMenuItemExportAll.Click += new System.EventHandler(this.ToolStripMenuItemExportAll_Click);
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll,
            this.miCollapseAll,
            this.ToolStripMenuItemCellHost,
            this.ToolStripMenuItemCellGuest});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(173, 92);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(172, 22);
            this.miExpandAll.Text = "展开所有节点";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCollapseAll
            // 
            this.miCollapseAll.Name = "miCollapseAll";
            this.miCollapseAll.Size = new System.Drawing.Size(172, 22);
            this.miCollapseAll.Text = "收缩所有节点";
            this.miCollapseAll.Click += new System.EventHandler(this.miCollapseAll_Click);
            // 
            // ToolStripMenuItemCellHost
            // 
            this.ToolStripMenuItemCellHost.Name = "ToolStripMenuItemCellHost";
            this.ToolStripMenuItemCellHost.Size = new System.Drawing.Size(172, 22);
            this.ToolStripMenuItemCellHost.Text = "查询主队小区覆盖";
            this.ToolStripMenuItemCellHost.Click += new System.EventHandler(this.ToolStripMenuItemCellHost_Click);
            // 
            // ToolStripMenuItemCellGuest
            // 
            this.ToolStripMenuItemCellGuest.Name = "ToolStripMenuItemCellGuest";
            this.ToolStripMenuItemCellGuest.Size = new System.Drawing.Size(172, 22);
            this.ToolStripMenuItemCellGuest.Text = "查询客队小区覆盖";
            this.ToolStripMenuItemCellGuest.Click += new System.EventHandler(this.ToolStripMenuItemCellGuest_Click);
            // 
            // splitContainerControl3
            // 
            this.splitContainerControl3.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl3.Horizontal = false;
            this.splitContainerControl3.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl3.LookAndFeel.SkinName = "Office 2007 Blue";
            this.splitContainerControl3.LookAndFeel.UseDefaultLookAndFeel = false;
            this.splitContainerControl3.Name = "splitContainerControl3";
            this.splitContainerControl3.Panel1.Controls.Add(this.groupControl3);
            this.splitContainerControl3.Panel1.Text = "Panel1";
            this.splitContainerControl3.Panel2.Controls.Add(this.grpPointInfo);
            this.splitContainerControl3.Panel2.Text = "Panel2";
            this.splitContainerControl3.Size = new System.Drawing.Size(1008, 657);
            this.splitContainerControl3.SplitterPosition = 176;
            this.splitContainerControl3.TabIndex = 2;
            this.splitContainerControl3.Text = "splitContainerControl3";
            // 
            // groupControl3
            // 
            this.groupControl3.Controls.Add(this.labelControl1);
            this.groupControl3.Controls.Add(this.cbxEditRegion);
            this.groupControl3.Controls.Add(this.listView);
            this.groupControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl3.Location = new System.Drawing.Point(0, 0);
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(1008, 176);
            this.groupControl3.TabIndex = 0;
            this.groupControl3.Text = "整体分析情况";
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(150, 4);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(52, 14);
            this.labelControl1.TabIndex = 3;
            this.labelControl1.Text = "当前区域:";
            // 
            // cbxEditRegion
            // 
            this.cbxEditRegion.Location = new System.Drawing.Point(209, 1);
            this.cbxEditRegion.Name = "cbxEditRegion";
            this.cbxEditRegion.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxEditRegion.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxEditRegion.Size = new System.Drawing.Size(138, 21);
            this.cbxEditRegion.TabIndex = 2;
            this.cbxEditRegion.SelectedIndexChanged += new System.EventHandler(this.cbxEditRegion_SelectedIndexChanged);
            // 
            // listView
            // 
            this.listView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader1,
            this.columnHeader2,
            this.columnHeader3});
            this.listView.ContextMenuStrip = this.contextMenuStripWhole;
            this.listView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listView.FullRowSelect = true;
            this.listView.Location = new System.Drawing.Point(2, 23);
            this.listView.Name = "listView";
            this.listView.Size = new System.Drawing.Size(1004, 151);
            this.listView.TabIndex = 1;
            this.listView.UseCompatibleStateImageBehavior = false;
            this.listView.View = System.Windows.Forms.View.Details;
            // 
            // columnHeader1
            // 
            this.columnHeader1.Text = "项";
            this.columnHeader1.Width = 97;
            // 
            // columnHeader2
            // 
            this.columnHeader2.Text = "数量";
            this.columnHeader2.Width = 109;
            // 
            // columnHeader3
            // 
            this.columnHeader3.Text = "占比";
            this.columnHeader3.Width = 103;
            // 
            // grpPointInfo
            // 
            this.grpPointInfo.Controls.Add(this.lvDetails);
            this.grpPointInfo.Controls.Add(this.panel1);
            this.grpPointInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.grpPointInfo.Location = new System.Drawing.Point(0, 0);
            this.grpPointInfo.Name = "grpPointInfo";
            this.grpPointInfo.Size = new System.Drawing.Size(1008, 477);
            this.grpPointInfo.TabIndex = 1;
            this.grpPointInfo.Text = "详细信息";
            // 
            // lvDetails
            // 
            this.lvDetails.AllColumns.Add(this.colItemName);
            this.lvDetails.AllColumns.Add(this.colBlockCnt);
            this.lvDetails.AllColumns.Add(this.colGridCnt);
            this.lvDetails.AllColumns.Add(this.colHostDistance);
            this.lvDetails.AllColumns.Add(this.colGuestDistance);
            this.lvDetails.AllColumns.Add(this.colBlockRoadDesc);
            this.lvDetails.AllColumns.Add(this.colRoadsCenterPntDesc);
            this.lvDetails.AllColumns.Add(this.colLng);
            this.lvDetails.AllColumns.Add(this.colLat);
            this.lvDetails.AllColumns.Add(this.colHostValueMin);
            this.lvDetails.AllColumns.Add(this.colHostValueMax);
            this.lvDetails.AllColumns.Add(this.colHostValueAvg);
            this.lvDetails.AllColumns.Add(this.colGuestValueMin);
            this.lvDetails.AllColumns.Add(this.colGuestValueMax);
            this.lvDetails.AllColumns.Add(this.colGuestValueAvg);
            this.lvDetails.AllColumns.Add(this.olvColHostGuestDiff);
            this.lvDetails.AutoArrange = false;
            this.lvDetails.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.colItemName,
            this.colBlockCnt,
            this.colGridCnt,
            this.colHostDistance,
            this.colGuestDistance,
            this.colBlockRoadDesc,
            this.colRoadsCenterPntDesc,
            this.colLng,
            this.colLat,
            this.colHostValueMin,
            this.colHostValueMax,
            this.colHostValueAvg,
            this.colGuestValueMin,
            this.colGuestValueMax,
            this.colGuestValueAvg,
            this.olvColHostGuestDiff});
            this.lvDetails.ContextMenuStrip = this.contextMenuStrip;
            this.lvDetails.Cursor = System.Windows.Forms.Cursors.Default;
            this.lvDetails.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lvDetails.FullRowSelect = true;
            this.lvDetails.GridLines = true;
            this.lvDetails.HeaderWordWrap = true;
            this.lvDetails.IsNeedShowOverlay = false;
            this.lvDetails.Location = new System.Drawing.Point(2, 61);
            this.lvDetails.Name = "lvDetails";
            this.lvDetails.OwnerDraw = true;
            this.lvDetails.ShowGroups = false;
            this.lvDetails.Size = new System.Drawing.Size(1004, 414);
            this.lvDetails.TabIndex = 6;
            this.lvDetails.UseCompatibleStateImageBehavior = false;
            this.lvDetails.View = System.Windows.Forms.View.Details;
            this.lvDetails.VirtualMode = true;
            // 
            // colItemName
            // 
            this.colItemName.HeaderFont = null;
            this.colItemName.Text = "项";
            this.colItemName.Width = 84;
            // 
            // colBlockCnt
            // 
            this.colBlockCnt.HeaderFont = null;
            this.colBlockCnt.Text = "汇聚块个数";
            this.colBlockCnt.Width = 82;
            // 
            // colGridCnt
            // 
            this.colGridCnt.HeaderFont = null;
            this.colGridCnt.Text = "栅格个数";
            this.colGridCnt.Width = 79;
            // 
            // colHostDistance
            // 
            this.colHostDistance.HeaderFont = null;
            this.colHostDistance.Text = "主队栅格里程(米)";
            // 
            // colGuestDistance
            // 
            this.colGuestDistance.HeaderFont = null;
            this.colGuestDistance.Text = "客队栅格里程(米)";
            // 
            // colBlockRoadDesc
            // 
            this.colBlockRoadDesc.HeaderFont = null;
            this.colBlockRoadDesc.Text = "道路";
            this.colBlockRoadDesc.Width = 224;
            // 
            // colRoadsCenterPntDesc
            // 
            this.colRoadsCenterPntDesc.HeaderFont = null;
            this.colRoadsCenterPntDesc.Text = "道路中心经纬度";
            // 
            // colLng
            // 
            this.colLng.HeaderFont = null;
            this.colLng.Text = "中心纬度";
            // 
            // colLat
            // 
            this.colLat.HeaderFont = null;
            this.colLat.Text = "中心纬度";
            // 
            // colHostValueMin
            // 
            this.colHostValueMin.HeaderFont = null;
            this.colHostValueMin.Text = "主队最小值";
            this.colHostValueMin.Width = 74;
            // 
            // colHostValueMax
            // 
            this.colHostValueMax.HeaderFont = null;
            this.colHostValueMax.Text = "主队最大值";
            this.colHostValueMax.Width = 70;
            // 
            // colHostValueAvg
            // 
            this.colHostValueAvg.HeaderFont = null;
            this.colHostValueAvg.Text = "主队平均值";
            this.colHostValueAvg.Width = 69;
            // 
            // colGuestValueMin
            // 
            this.colGuestValueMin.HeaderFont = null;
            this.colGuestValueMin.Text = "客队最小值";
            this.colGuestValueMin.Width = 70;
            // 
            // colGuestValueMax
            // 
            this.colGuestValueMax.HeaderFont = null;
            this.colGuestValueMax.Text = "客队最大值";
            this.colGuestValueMax.Width = 80;
            // 
            // colGuestValueAvg
            // 
            this.colGuestValueAvg.HeaderFont = null;
            this.colGuestValueAvg.Text = "客队平均值";
            this.colGuestValueAvg.Width = 80;
            // 
            // olvColHostGuestDiff
            // 
            this.olvColHostGuestDiff.HeaderFont = null;
            this.olvColHostGuestDiff.Text = "主客差值";
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.btnExportShp);
            this.panel1.Controls.Add(this.btnExportBriefTxt);
            this.panel1.Controls.Add(this.cbxDisplayMode);
            this.panel1.Controls.Add(this.btnExportBrief);
            this.panel1.Controls.Add(this.btnExport2Txt);
            this.panel1.Controls.Add(this.btnExport);
            this.panel1.Controls.Add(this.label2);
            this.panel1.Controls.Add(this.label1);
            this.panel1.Controls.Add(this.numCombineMinCnt);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Location = new System.Drawing.Point(2, 23);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(1004, 38);
            this.panel1.TabIndex = 17;
            // 
            // btnExportShp
            // 
            this.btnExportShp.Location = new System.Drawing.Point(428, 7);
            this.btnExportShp.Name = "btnExportShp";
            this.btnExportShp.Size = new System.Drawing.Size(70, 23);
            this.btnExportShp.TabIndex = 6;
            this.btnExportShp.Text = "导出图层...";
            this.btnExportShp.Click += new System.EventHandler(this.btnExportShp_Click);
            // 
            // btnExportBriefTxt
            // 
            this.btnExportBriefTxt.Location = new System.Drawing.Point(828, 7);
            this.btnExportBriefTxt.Name = "btnExportBriefTxt";
            this.btnExportBriefTxt.Size = new System.Drawing.Size(166, 23);
            this.btnExportBriefTxt.TabIndex = 6;
            this.btnExportBriefTxt.Text = "导出道路级别信息到Txt...";
            this.btnExportBriefTxt.Click += new System.EventHandler(this.btnExportBriefTxt_Click);
            // 
            // cbxDisplayMode
            // 
            this.cbxDisplayMode.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxDisplayMode.FormattingEnabled = true;
            this.cbxDisplayMode.Location = new System.Drawing.Point(248, 7);
            this.cbxDisplayMode.Name = "cbxDisplayMode";
            this.cbxDisplayMode.Size = new System.Drawing.Size(138, 22);
            this.cbxDisplayMode.TabIndex = 15;
            this.cbxDisplayMode.SelectedIndexChanged += new System.EventHandler(this.cbxDisplayMode_SelectedIndexChanged);
            // 
            // btnExportBrief
            // 
            this.btnExportBrief.Location = new System.Drawing.Point(656, 7);
            this.btnExportBrief.Name = "btnExportBrief";
            this.btnExportBrief.Size = new System.Drawing.Size(166, 23);
            this.btnExportBrief.TabIndex = 6;
            this.btnExportBrief.Text = "导出道路级别信息到Excel...";
            this.btnExportBrief.Click += new System.EventHandler(this.btnExportBrief_Click);
            // 
            // btnExport2Txt
            // 
            this.btnExport2Txt.Location = new System.Drawing.Point(580, 7);
            this.btnExport2Txt.Name = "btnExport2Txt";
            this.btnExport2Txt.Size = new System.Drawing.Size(70, 23);
            this.btnExport2Txt.TabIndex = 6;
            this.btnExport2Txt.Text = "导出Txt...";
            this.btnExport2Txt.Click += new System.EventHandler(this.btnExport2Txt_Click);
            // 
            // btnExport
            // 
            this.btnExport.Location = new System.Drawing.Point(504, 8);
            this.btnExport.Name = "btnExport";
            this.btnExport.Size = new System.Drawing.Size(70, 23);
            this.btnExport.TabIndex = 6;
            this.btnExport.Text = "导出Excel...";
            this.btnExport.Click += new System.EventHandler(this.btnExport_Click);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(201, 10);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(55, 14);
            this.label2.TabIndex = 14;
            this.label2.Text = "显示项：";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(12, 10);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(100, 14);
            this.label1.TabIndex = 14;
            this.label1.Text = "汇聚块栅格个数≥";
            // 
            // numCombineMinCnt
            // 
            this.numCombineMinCnt.Location = new System.Drawing.Point(118, 8);
            this.numCombineMinCnt.Name = "numCombineMinCnt";
            this.numCombineMinCnt.Size = new System.Drawing.Size(48, 22);
            this.numCombineMinCnt.TabIndex = 16;
            this.numCombineMinCnt.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // CPUnitShowForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1008, 657);
            this.Controls.Add(this.splitContainerControl3);
            this.Name = "CPUnitShowForm";
            this.Text = "竞争对比分析汇总";
            this.contextMenuStripWhole.ResumeLayout(false);
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl3)).EndInit();
            this.splitContainerControl3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            this.groupControl3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxEditRegion.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpPointInfo)).EndInit();
            this.grpPointInfo.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.lvDetails)).EndInit();
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numCombineMinCnt)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll;
        private System.Windows.Forms.ContextMenuStrip contextMenuStripWhole;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExportCurrent;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemExportAll;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemCellHost;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemCellGuest;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl3;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.ComboBoxEdit cbxEditRegion;
        private System.Windows.Forms.ListView listView;
        private System.Windows.Forms.ColumnHeader columnHeader1;
        private System.Windows.Forms.ColumnHeader columnHeader2;
        private System.Windows.Forms.ColumnHeader columnHeader3;
        private DevExpress.XtraEditors.GroupControl grpPointInfo;
        private BrightIdeasSoftware.TreeListView lvDetails;
        private BrightIdeasSoftware.OLVColumn colItemName;
        private BrightIdeasSoftware.OLVColumn colBlockCnt;
        private BrightIdeasSoftware.OLVColumn colGridCnt;
        private BrightIdeasSoftware.OLVColumn colHostDistance;
        private BrightIdeasSoftware.OLVColumn colGuestDistance;
        private BrightIdeasSoftware.OLVColumn colBlockRoadDesc;
        private BrightIdeasSoftware.OLVColumn colRoadsCenterPntDesc;
        private BrightIdeasSoftware.OLVColumn colLng;
        private BrightIdeasSoftware.OLVColumn colLat;
        private BrightIdeasSoftware.OLVColumn colHostValueMin;
        private BrightIdeasSoftware.OLVColumn colHostValueMax;
        private BrightIdeasSoftware.OLVColumn colHostValueAvg;
        private BrightIdeasSoftware.OLVColumn colGuestValueMin;
        private BrightIdeasSoftware.OLVColumn colGuestValueMax;
        private BrightIdeasSoftware.OLVColumn colGuestValueAvg;
        private BrightIdeasSoftware.OLVColumn olvColHostGuestDiff;
        private System.Windows.Forms.Panel panel1;
        private DevExpress.XtraEditors.SimpleButton btnExportShp;
        private DevExpress.XtraEditors.SimpleButton btnExportBriefTxt;
        private System.Windows.Forms.ComboBox cbxDisplayMode;
        private DevExpress.XtraEditors.SimpleButton btnExportBrief;
        private DevExpress.XtraEditors.SimpleButton btnExport2Txt;
        private DevExpress.XtraEditors.SimpleButton btnExport;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.NumericUpDown numCombineMinCnt;
    }
}