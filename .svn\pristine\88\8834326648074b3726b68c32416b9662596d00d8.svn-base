﻿using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model
{
    public enum NRAntennaDirectionType
    {
        Omni = 1,
        Beam
    }

    public class NRAntenna: Snapshot<NRAntenna>
    {
        public NRAntenna()
        {
            Value = this;
        }

        private NRCell cell;
        public NRCell Cell
        {
            get
            {
                return cell;
            }
            set
            {
                cell = value;
                value.Antennas.Add(this);
            }
        }

        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public short Direction { get; set; }
        public short Downward { get; set; }
        public int Altitude { get; set; }
        public string Description { get; set; }

        private NRAntennaDirectionType directionType;
        public NRAntennaDirectionType DirectionType
        {//应各地要求，360°时，显示为正北方向
            get
            {
                if (Type == NRBTSType.Indoor)
                {
                    directionType = NRAntennaDirectionType.Omni;
                }
                else
                {
                    directionType = NRAntennaDirectionType.Beam;
                }
                return directionType;
            }
            set { directionType = value; }
        }

        public double EndPointLongitude
        {
            get
            {
                double offset = 0;
                if (DirectionType == NRAntennaDirectionType.Beam)
                {
                    offset = Math.Cos(-(Direction - 90) * Math.PI / 180) * 0.000024
                    * Func.MapNRCellLayer.CellDefaultDisplayLength
                    * Func.MapNRCellLayer.ShapeLengthScale;
                }
                return Longitude + offset;
            }
        }

        public double EndPointLatitude
        {
            get
            {
                double offset = 0;
                if (DirectionType == NRAntennaDirectionType.Beam)
                {
                    offset = Math.Sin(-(Direction - 90) * Math.PI / 180) * 0.000024
                    * Func.MapNRCellLayer.CellDefaultDisplayLength
                    * Func.MapNRCellLayer.ShapeLengthScale;
                }
                return Latitude + offset;
            }
        }

        public NRBTSType Type
        {
            get { return Cell.Type; }
        }

        public string DetailInfo
        {
            get
            {
                StringBuilder info = new StringBuilder();
                info.Append(Cell.DetailInfo);
                info.Append("\r\nLongitude:").Append(Longitude);
                info.Append("\r\nLatitude:").Append(Latitude);
                info.Append("\r\nDirectionType:").Append(DirectionType);
                info.Append("\r\nDirection:").Append(Direction);
                info.Append("\r\nDownward:").Append(Downward);
                info.Append("\r\nAltitude:").Append(Altitude);
                info.Append("\r\nDescription:").Append(Description);
                info.Append("\r\n");
                return info.ToString();
            }
        }

        public bool Within(double x1, double y1, double x2, double y2)
        {
            if (Longitude < x1 || Longitude > x2 || Latitude < y1 || Latitude > y2)
            {
                return false;
            }
            return true;
        }

        public bool Within(MTGis.DbRect dRect)
        {
            if (Longitude < dRect.x1 || Longitude > dRect.x2 || Latitude < dRect.y1 || Latitude > dRect.y2)
            {
                return false;
            }
            return true;
        }

        public void Fill(Net.Content content, CellManager cellManager)
        {
            base.Fill(content.GetParamInt(), content.GetParamInt(), content.GetParamInt());
            int cellID = content.GetParamInt();
            var cells = cellManager.GetNRCells(cellID, ValidPeriod);
            foreach (NRCell nrCell in cells)
            {
                if (nrCell.ValidPeriod.Contains(ValidPeriod.BeginTime) 
                    || ValidPeriod.IsIntersect(nrCell.ValidPeriod))
                {
                    this.Cell = nrCell;
                }
            }
            Longitude = content.GetParamDouble();
            Latitude = content.GetParamDouble();
            Direction = (short)content.GetParamInt();
            Downward = (short)content.GetParamInt();
            Altitude = content.GetParamInt();
            Description = content.GetParamString();
            if ((Longitude < 60 || Latitude < 10) && this.Cell != null)
            {
                Longitude = this.Cell.Longitude;
                Latitude = this.Cell.Latitude;
            }
        }
    }
}
