﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.ZTNotHoTopLevCell;

namespace MasterCom.RAMS.ZTFunc
{
    public class QueryNotHoTopLevCell_NR : QueryNotHoTopLevCell
    {
        private static QueryNotHoTopLevCell_NR instance = null;
        public new static QueryNotHoTopLevCell_NR GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new QueryNotHoTopLevCell_NR();
                    }
                }
            }
            return instance;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 13050, this.Name);
        }

        protected override void doStatWithQuery()
        {
            foreach (DTFileDataManager file in MainModel.DTDataManager.FileDataManagers)
            {
                for (int i = 0; i < file.DTDatas.Count; i++)
                {
                    Event evt = file.DTDatas[i] as Event;
                    if (evt != null && 
                        (evt.ID == 9591 || evt.ID == 9594) &&
                        isDataInRegion(evt.Longitude, evt.Latitude))
                    {
                        TestPoint preTp = null;
                        for (int j = i - 1; j >= 0; j--)
                        {
                            //获取切换前采样点
                            TestPoint tp = file.DTDatas[j] as TestPoint;
                            if (tp == null || tp.Time > evt.Time)
                            {
                                continue;
                            }
                            preTp = tp;
                            break;
                        }
                        if (preTp == null)
                        {
                            continue;
                        }

                        TestPoint sufTp = null;
                        for (int j = i + 1; j < file.DTDatas.Count; j++)
                        {
                            //获取切换后采样点
                            TestPoint tp = file.DTDatas[j] as TestPoint;
                            if (tp == null || tp.Time < evt.Time)
                            {
                                continue;
                            }
                            sufTp = tp;
                            break;
                        }
                        if (sufTp == null)
                        {
                            continue;
                        }

                        ICell orgCell = preTp.GetMainCell();
                        if (orgCell == null)
                        {
                            continue;
                        }

                        float? orgRsrp = null;
                        if (preTp["NR_SS_RSRP"] != null)
                        {
                            orgRsrp = (float?)preTp["NR_SS_RSRP"];
                        }
                        else
                        {
                            orgRsrp = -97;
                        }
                        
                        ICell tarCell = sufTp.GetMainCell();
                        if (tarCell == null || tarCell.Name == orgCell.Name)
                        {
                            continue;
                        }

                        float? tarRsrp = null;
                        if (sufTp["NR_SS_RSRP"] != null)
                        {
                            tarRsrp = (float?)sufTp["NR_SS_RSRP"];
                        }
                        else
                        {
                            tarRsrp = -97;
                        }

                        dealTop(evt, preTp, sufTp, orgCell, orgRsrp, tarCell, tarRsrp);
                    }
                }
            }
        }

        private void dealTop(Event evt, TestPoint preTp, TestPoint sufTp, ICell orgCell, float? orgRsrp, ICell tarCell, float? tarRsrp)
        {
            float topRsrp = float.MinValue;
            int topIdx = -1;
            for (int x = 0; x < 10; x++)
            {
                float? nRxLev = (float?)preTp["NR_NCell_RSRP", x];

                if (nRxLev == null)
                {
                    break;
                }

                var nCell = preTp.GetNBCell(x);
                if (nCell == null || nCell.Name == orgCell.Name || nCell.Name == tarCell.Name)
                {
                    continue;
                }

                if (nRxLev > topRsrp)
                {
                    topRsrp = (float)nRxLev;
                    topIdx = x;
                }
            }

            if (topIdx == -1)
            {
                return;
            }

            var topNCell = preTp.GetNBCell(topIdx);
            if (topNCell != null && tarRsrp < topRsrp)
            {
                var info = new NotHoTopLevCellInfo(orgCell, (float)orgRsrp, 
                    tarCell, (float)tarRsrp, topNCell, topRsrp);

                info.HoSuccessEvt = evt;
                info.TestPoints.Add(preTp);
                info.TestPoints.Add(sufTp);
                resultSet.Add(info);
            }
        }

    }
}
