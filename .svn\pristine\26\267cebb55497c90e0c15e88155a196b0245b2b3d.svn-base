﻿using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class LteFddNBCellCheckAnaByRegion_WCDMA : LteFddNBCellCheckAnaBase_WCDMA
    {
        public LteFddNBCellCheckAnaByRegion_WCDMA(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected static readonly object lockObj = new object();
        private static LteFddNBCellCheckAnaByRegion_WCDMA intance = null;
        public static LteFddNBCellCheckAnaByRegion_WCDMA GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new LteFddNBCellCheckAnaByRegion_WCDMA(MainModel.GetInstance());
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "LTEFDD邻区核查(按区域)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="testPoint"></param>
        /// <returns></returns>
        /// <summary>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            try
            {
                if (testPoint is LTEFddTestPoint)
                {
                    return Condition.Geometorys.GeoOp.Contains(testPoint.Longitude, testPoint.Latitude);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }
}