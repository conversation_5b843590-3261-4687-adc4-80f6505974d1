﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ScanMultiCoverageCondition
    {
        public int CoverBandDiff { get; set; } = 6;
        public int AbsoluteValue { get; set; } = -105;
        public int ValidValue { get; set; } = -105;
        public bool SaveTestPoint { get; set; }
    }

    public enum ShowCoverage
    {
        Relative,
        Absolute,
        Synthesize
    }
}
