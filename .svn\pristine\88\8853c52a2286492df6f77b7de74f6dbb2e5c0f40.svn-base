﻿using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model
{
    public static class NRDTDataHelper
    {
        public static long AnalyseParamNCI(MasterCom.RAMS.Net.Content content)
        {
            long nci = content.GetParamInt64NoTransfer();
            if (nci > 1000000000000 || nci < -1)
            {
                content.ResetOffset(8);
                nci = content.GetParamInt();
            }

            return nci;
        }

        public static long AnalyseNCI(MasterCom.RAMS.Net.Content content)
        {
            long nci = content.GetParamInt64();
            if (nci > 1000000000000 || nci < -1)
            {
                content.ResetOffset(8);
                nci = content.GetParamInt();
            }

            return nci;
        }

        public static NRServiceName getServiceNameFromTypeID(int serviceType)
        {
            ServiceType typeEnum = (ServiceType)serviceType;
            switch (typeEnum)
            {
                case ServiceType.NR_NSA_TDD_IDLE:
                case ServiceType.NR_NSA_TDD_DATA:
                case ServiceType.NR_NSA_TDD_VOLTE:
                    return NRServiceName.NSA;
                case ServiceType.NR_SA_TDD_IDLE:
                case ServiceType.NR_SA_TDD_DATA:
                case ServiceType.NR_SA_TDD_VOLTE:
                case ServiceType.NR_SA_TDD_VONR:
                    return NRServiceName.SA;
                case ServiceType.NR_DM_TDD_IDLE:
                case ServiceType.NR_DM_TDD_DATA:
                case ServiceType.NR_DM_TDD_VOLTE:
                    return NRServiceName.DM;

                default:
                    return NRServiceName.NULL;
            }
        }
    }

    public enum NRServiceName
    { 
        NSA,
        SA,
        DM,
        NULL
    }

    public static class NRMsgHelper
    {
        /// <summary>
        /// 获取通话时段
        /// </summary>
        public static List<TimePeriod> GetCallTimePeriod(List<Message> msgList, List<int> startMsgList, List<int> endMsgList)
        {
            List<TimePeriod> timePeriodList = new List<TimePeriod>();

            Message startMsg = null;
            foreach (var msg in msgList)
            {
                if (startMsgList.Contains(msg.ID) && startMsg == null)
                {
                    startMsg = msg;
                }
                else if (endMsgList.Contains(msg.ID) && startMsg != null)
                {
                    TimePeriod timePeriod = new TimePeriod(startMsg.DateTime, msg.DateTime);
                    timePeriodList.Add(timePeriod);
                    startMsg = null;
                }
            }

            return timePeriodList;
        }

        /// <summary>
        /// 判断时间是否在通话时段内
        /// </summary>
        public static bool JudgeInTimePeriod(List<TimePeriod> timePeriodList, DateTime time)
        {
            foreach (var timePeriod in timePeriodList)
            {
                if (timePeriod.Contains(time))
                {
                    return true;
                }
            }
            return false;
        }
    }

    public enum NRMsgManager
    {
        Http_Page_Request = 2147424830, //0x7fff1A3E
        Http_Download_Begin = 2147421185,
        Video_Play_Request = 2147424513,

        Http_Page_Start = 2147424858,
        Http_Page_Socket_Connecting = 2147424849,
        Http_Page_Socket_Success = 2147424860,
        Http_Page_Send_Cmd = 2147424850,
        Http_Page_Url_Redirect = 2147424857,
        Http_Page_First_Data = 2147424851,
        Http_Page_MainPage_Success = 2147424861,
        Http_Page_Continue = 2147424862,
        Http_Page_Last_Data = 2147424852,
        Http_Page_Disconnect = 2147424853,

    }

    public class NRCallPeriodMsgHelper
    { 
        public string Desc { get; set; }
        public NRMsgManager CallBeginMsg { get; set; }
        public NRMsgManager CallEndMsg { get; set; }
    }
}
