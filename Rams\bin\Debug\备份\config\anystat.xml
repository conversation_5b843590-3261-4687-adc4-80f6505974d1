<?xml version="1.0"?>
<Configs>
  <Config name="AnyStatUnits">
    <Item name="units" typeName="IList">
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">新指标组</Item>
        <Item typeName="Int32" key="keyColumnCount">3</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">文件名称</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">False</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">0</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">FileName</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">小区</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">65535</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">LAC</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">小区2</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">65535</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">CI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">rxlev最大</Item>
            <Item typeName="Int32" key="statType">2</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">-120</Item>
            <Item typeName="Double" key="maxRange">-10</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">RxLevSub</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">经度</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">20000000000</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">Longitude</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">纬度</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">-999</Item>
            <Item typeName="Double" key="maxRange">9000000</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">Latitude</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">TA</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">63</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">TA</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">距离</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">100000</Item>
            <Item typeName="Int32" key="decPlace">0</Item>
            <Item typeName="String" key="param_name">Distance</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">小区RxQual点数</Item>
        <Item typeName="Int32" key="keyColumnCount">2</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">小区</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">False</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">0</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">CellName</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">RxQual</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">7.09999990463257</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">RxQualSub</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">新列</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">-999</Item>
            <Item typeName="Double" key="maxRange">9000000</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">Latitude</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">数量</Item>
            <Item typeName="Int32" key="statType">5</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">7.09999990463257</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">RxQualSub</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">新列</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">20000000000</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">Longitude</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">CbI</Item>
        <Item typeName="Int32" key="keyColumnCount">1</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">c_iValue</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">-20</Item>
            <Item typeName="Double" key="maxRange">40</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">C_I</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">count</Item>
            <Item typeName="Int32" key="statType">5</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">-20</Item>
            <Item typeName="Double" key="maxRange">40</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">C_I</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">test</Item>
        <Item typeName="Int32" key="keyColumnCount">2</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">speechcodec</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">False</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">0</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">SpeechCodecName</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">rxlev</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">-120</Item>
            <Item typeName="Double" key="maxRange">-10</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">RxLevSub</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges">
              <Item typeName="IDictionary">
                <Item typeName="Single" key="minValue">-120</Item>
                <Item typeName="Single" key="maxValue">-100</Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="Single" key="minValue">-100</Item>
                <Item typeName="Single" key="maxValue">-90</Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="Single" key="minValue">-90</Item>
                <Item typeName="Single" key="maxValue">-85</Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="Single" key="minValue">-85</Item>
                <Item typeName="Single" key="maxValue">-75</Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="Single" key="minValue">-75</Item>
                <Item typeName="Single" key="maxValue">-45</Item>
              </Item>
              <Item typeName="IDictionary">
                <Item typeName="Single" key="minValue">-45</Item>
                <Item typeName="Single" key="maxValue">-10</Item>
              </Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">count</Item>
            <Item typeName="Int32" key="statType">5</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">-120</Item>
            <Item typeName="Double" key="maxRange">-10</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">RxLevSub</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">RLT_MAX</Item>
        <Item typeName="Int32" key="keyColumnCount">1</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">carrier</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">200</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">Carrier</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">LAC</Item>
            <Item typeName="Int32" key="statType">5</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">65535</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">CI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">CI</Item>
            <Item typeName="Int32" key="statType">5</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">65535</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">CI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">RLTMAX</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">200</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">RLTMax</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">wj_mos_speechcodec</Item>
        <Item typeName="Int32" key="keyColumnCount">1</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">bcch</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">1</Item>
            <Item typeName="Double" key="maxRange">2000</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">BCCH</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">pesq</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">5</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">PESQ</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">speechcodec</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">20</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">SpeechCodec</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">新指标组</Item>
        <Item typeName="Int32" key="keyColumnCount">1</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">1</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">False</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">0</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name" />
            <Item typeName="Int32" key="param_arg">-1</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">3</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">1</Item>
            <Item typeName="Double" key="maxRange">2000</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">BCCH</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">4</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">1</Item>
            <Item typeName="Double" key="maxRange">2000</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">C_I_ARFCN</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">5</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">-125</Item>
            <Item typeName="Double" key="maxRange">166</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">ACCMIN</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">6</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">-999</Item>
            <Item typeName="Double" key="maxRange">9000000</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">Latitude</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">7</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">20000000000</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">Longitude</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">8</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">-120</Item>
            <Item typeName="Double" key="maxRange">-10</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">RxLevSub</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">9</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">7.09999990463257</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">RxQualSub</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">10</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">65535</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">CI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">12</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">1</Item>
            <Item typeName="Double" key="maxRange">3</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">CellType</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">11</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">65535</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">LAC</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">编码方式占比统计</Item>
        <Item typeName="Int32" key="keyColumnCount">1</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">CI</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">65535</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">CI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">编码方式</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">20</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">SpeechCodec</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">编码方式个数</Item>
            <Item typeName="Int32" key="statType">5</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">20</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">SpeechCodec</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">编码方式MOS均值统计</Item>
        <Item typeName="Int32" key="keyColumnCount">1</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">编码方式</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">20</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">SpeechCodec</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">MOS均值</Item>
            <Item typeName="Int32" key="statType">3</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">5</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">PESQLQ</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">半速率占比（小区）</Item>
        <Item typeName="Int32" key="keyColumnCount">1</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">小区LAC</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">65535</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">LAC</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">小区CI</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">65535</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">CI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">编码方式HR</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">20</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">SpeechCodec</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">小区高电平低质量</Item>
        <Item typeName="Int32" key="keyColumnCount">1</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">RxQual</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">7.09999990463257</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">RxQualSub</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">LAC</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">65535</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">LAC</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">CI</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">65535</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">CI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">Rxlev</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">-120</Item>
            <Item typeName="Double" key="maxRange">-10</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">RxLevSub</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">RxlevCount</Item>
            <Item typeName="Int32" key="statType">5</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">-120</Item>
            <Item typeName="Double" key="maxRange">-10</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">RxLevSub</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">SpeechCodec</Item>
        <Item typeName="Int32" key="keyColumnCount">1</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">SpeechCodec</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">20</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">SpeechCodec</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">SpeechCodecCount</Item>
            <Item typeName="Int32" key="statType">5</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">20</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">SpeechCodec</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">高电平质差</Item>
        <Item typeName="Int32" key="keyColumnCount">2</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">Rxqual</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">7.09999990463257</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">RxQualSub</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">Rxlev</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">-120</Item>
            <Item typeName="Double" key="maxRange">-10</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">RxLevSub</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">Count</Item>
            <Item typeName="Int32" key="statType">5</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">-120</Item>
            <Item typeName="Double" key="maxRange">-10</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">RxLevSub</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">SPEECH_MOS</Item>
        <Item typeName="Int32" key="keyColumnCount">1</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">SPEECHCODEC</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">20</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">SpeechCodec</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">MOS</Item>
            <Item typeName="Int32" key="statType">3</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">5</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">PESQ</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary">
        <Item typeName="String" key="name">LTE</Item>
        <Item typeName="Int32" key="keyColumnCount">3</Item>
        <Item typeName="IList" key="columnsDef">
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">FileName</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">False</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">0</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">FileName</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">TAC</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">65534</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">lte_TAC</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">ECI</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">4294967040</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">lte_ECI</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">Logitude</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">0</Item>
            <Item typeName="Double" key="maxRange">20000000000</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">Longitude</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">Latitude</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">-999</Item>
            <Item typeName="Double" key="maxRange">9000000</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">Latitude</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="name">RSRP</Item>
            <Item typeName="Int32" key="statType">0</Item>
            <Item typeName="Int32" key="keyType">0</Item>
            <Item typeName="Boolean" key="valueRangeCheck">True</Item>
            <Item typeName="Double" key="minRange">-141</Item>
            <Item typeName="Double" key="maxRange">25</Item>
            <Item typeName="Int32" key="decPlace">2</Item>
            <Item typeName="String" key="param_name">lte_RSRP</Item>
            <Item typeName="Int32" key="param_arg">0</Item>
            <Item typeName="IList" key="retByRanges" />
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>