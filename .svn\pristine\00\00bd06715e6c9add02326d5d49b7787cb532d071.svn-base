﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public class VolteMOSUnionAnaByRegion : VolteMOSUnionAnaBase
    {
        public VolteMOSUnionAnaByRegion(MainModel mm) : base(mm)
        {
            mainModel = mm;
        }

        public override string Name
        {
            get { return "VOLTE-MOS关联分析(按区域)"; }
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }
        protected override void query()
        {
            if (!getCondition())
            {
                return;
            }
            DIYQueryFileInfoByRegion queryFileByReg = new DIYQueryFileInfoByRegion(MainModel);
            queryFileByReg.IsShowFileInfoForm = false;
            queryFileByReg.SetQueryCondition(condition);
            queryFileByReg.Query();

            Condition.FileInfos.Clear();
            Condition.FileInfos.AddRange(MainModel.FileInfos);
            SetQueryCondition(condition);
            NeedJudgeTestPointByRegion = true;
            base.query();
            MainModel.DTDataManager.Clear();
        }
    }

    public class VolteMOSUnionAnaByRegion_FDD : VolteMOSUnionAnaBase_FDD
    {
        public VolteMOSUnionAnaByRegion_FDD(MainModel mainModel)
            : base(mainModel)
        {

        }
        public override string Name
        {
            get { return "VOLTE_FDD-MOS关联分析(按区域)"; }
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }
        protected override void query()
        {
            if (!getCondition())
            {
                return;
            }
            DIYQueryFileInfoByRegion queryFileByReg = new DIYQueryFileInfoByRegion(MainModel);
            queryFileByReg.IsShowFileInfoForm = false;
            queryFileByReg.SetQueryCondition(condition);
            queryFileByReg.Query();

            Condition.FileInfos.Clear();
            Condition.FileInfos.AddRange(MainModel.FileInfos);
            SetQueryCondition(condition);
            NeedJudgeTestPointByRegion = true;
            base.query();
            MainModel.DTDataManager.Clear();
        }
    }
}
