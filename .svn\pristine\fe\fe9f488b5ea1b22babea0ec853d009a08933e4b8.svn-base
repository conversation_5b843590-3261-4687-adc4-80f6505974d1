﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.Util;
using MasterCom.RAMS.Model.PerformanceParam.TD切换分析;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Model.PerformanceParam
{
    public partial class JointHandoverAnalysisTimeFrm : DevExpress.XtraEditors.XtraForm
    {
        MainModel mainModel;
        private bool IsTDAnalysis = false;
        public JointHandoverAnalysisTimeFrm(MainModel mainModel,bool IsTDAnalysis)
        {
            InitializeComponent();
            this.mainModel=mainModel;
            this.IsTDAnalysis = IsTDAnalysis;
        }

        /// <summary>
        /// 初始化 下拉选项
        /// </summary>
        public void comboBoxInit(List<string> iMonList)
        {
            this.comboBoxEdit_iMon.Properties.Items.AddRange(iMonList);
            if (iMonList.Count != 0)
            {
                this.comboBoxEdit_iMon.Text = iMonList[iMonList.Count-1];
            }
        }

        /// <summary>
        /// 点击确定开始查询数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Button_ok_Click(object sender, EventArgs e)
        {
            if (comboBoxEdit_iMon.Text == "")
            {
                XtraMessageBox.Show("请选择轮次！");
                return;
            }
            this.Hide();
            JoinHandoverAnalysisFrm analysisFrm = new JoinHandoverAnalysisFrm(mainModel, IsTDAnalysis);
            WaitBox.CanCancel = true;
            WaitBox.Show("正在查询联合切换分析数据.......", queryStatData);
            analysisFrm.Source.DataSource = analysisList;
            analysisFrm.Show();
            Dictionary<string, HandoverNebulaForm.DataXY> dataXYDic = new Dictionary<string, HandoverNebulaForm.DataXY>();
            Dictionary<int, List<JoinHandoverAnalysis>> handoverAnalysisDic = new Dictionary<int, List<JoinHandoverAnalysis>>();
            //归并坐标相同的点
            //滤除非选择区域内的点
            analysisList = VerificationRegion(analysisList);
            foreach (JoinHandoverAnalysis analysis in analysisList)
            {

                HandoverNebulaForm.DataXY data = new HandoverNebulaForm.DataXY();
                data.Yvalue = Convert.ToSingle(analysis.IHoNum);
                data.Xvalue = Convert.ToSingle(analysis.FSuccRate * 100);
                string key = data.Yvalue.ToString() + data.Xvalue.ToString();
                if (!dataXYDic.ContainsKey(key))
                {
                    dataXYDic.Add(key, data);
                }
                if (!handoverAnalysisDic.ContainsKey(analysis.ILevel))
                {
                    List<JoinHandoverAnalysis> handoverList = new List<JoinHandoverAnalysis>();
                    handoverList.Add(analysis);
                    handoverAnalysisDic.Add(analysis.ILevel, handoverList);
                }
                else
                {
                    handoverAnalysisDic[analysis.ILevel].Add(analysis);
                }
            }

            analysisFrm.dataxyList.AddRange(dataXYDic.Values);
            analysisFrm.HandoverAnalysisDic = handoverAnalysisDic;
            analysisFrm.LoadNebula();
        }
        /// <summary>
        /// 验证数据是否在所选区域内
        /// </summary>
        private List<JoinHandoverAnalysis> VerificationRegion(List<JoinHandoverAnalysis> gssList)
        {
            List<JoinHandoverAnalysis> newGsslist = new List<JoinHandoverAnalysis>();

            foreach (JoinHandoverAnalysis gss in gssList)
            {
                string date = gss.IMon.ToString().Substring(0, 4) + "-" + gss.IMon.ToString().Substring(4, 2);
                DateTime dateTime = Convert.ToDateTime(date);
                Cell cell = mainModel.CellManager.GetCell(dateTime, (ushort)gss.ILac, (ushort)gss.ICi);
                if (cell != null)
                {
                    if (mainModel.SearchGeometrys.GeoOp.Contains(cell.Longitude, cell.Latitude))
                    {
                        newGsslist.Add(gss);
                    }
                }
                else
                {
                    TDCell tdCell = mainModel.CellManager.GetTDCell(dateTime, (ushort)gss.ILac, (ushort)gss.ICi);
                    if (tdCell != null && mainModel.SearchGeometrys.GeoOp.Contains(tdCell.Longitude, tdCell.Latitude))
                    {
                        newGsslist.Add(gss);
                    }
                }
            }
            return newGsslist;
        }

        List<JoinHandoverAnalysis> analysisList = new List<JoinHandoverAnalysis>();
      
        private void Button_cel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// 查询统计数据
        /// </summary>
        protected void queryStatData()
        {
            try
            {
                this.analysisList.Clear();
                DiySqlQueryJointAwitchingAnalysisGetDate analysisDate;

                if(IsTDAnalysis)
                {
                    analysisDate = new DiySqlQueryJointHandoverAnalysisGetDataTD(mainModel);
                }
                else
                {
                    analysisDate = new DiySqlQueryJointAwitchingAnalysisGetDate(mainModel);
                }
                
                analysisDate.imon = this.comboBoxEdit_iMon.Text;
                analysisDate.Query();
                this.analysisList.AddRange(analysisDate.AnalysisList);
            }
            catch
            {
                //continue
            }
            finally
            {
                WaitBox.Close();
            }
        }
    }
}