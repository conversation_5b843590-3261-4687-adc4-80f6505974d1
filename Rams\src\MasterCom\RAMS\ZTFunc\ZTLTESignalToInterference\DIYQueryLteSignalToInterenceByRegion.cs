﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.src;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using MasterCom.RAMS.Grid;
using MasterCom.MTGis;
using MasterCom.RAMS.KPI_Statistics;

namespace MasterCom.RAMS.Net
{
    public class DIYQueryLteSignalToInterenceByRegion : DIYCellGridQueryBase
    {
        public DIYQueryLteSignalToInterenceByRegion(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get{ return "LTE干扰指数(按区域)";}
        }
        public override string IconName
        {
            get { return null; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 12191, this.Name);
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        protected override void AddGeographicFilter(Package package)
        {
            this.AddDIYRegion_Intersect(package);
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }

        protected override bool isValidStatImg(double lng, double lat)
        {
            MasterCom.RAMS.Grid.GridUnitBase grid = new Grid.GridUnitBase(lng, lat);
            return condition.Geometorys.GeoOp.ContainsRectCenter(grid.Bounds);
        }
        
        public static Dictionary<LTECell, List<string>> lteCellGridUnit { get; set; }
        public static Dictionary<string, List<GridCellInfos>> lteColorUnitGridCellInfos { get; set; }

        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                MainModel.DTDataManager.Clear();
                MainModel.SelectedTestPoints.Clear();
                MainModel.SelectedEvents.Clear();
                MainModel.SelectedMessage = null;
                MainModel.CurGridCoverData = null;
                MainModel.CurGridColorUnitMatrix = new GridMatrix<ColorUnit>();
                WaitBox.Text = "开始统计查询栅格数据...";
                WaitBox.CanCancel = true;
                setFormula();
                string statImgIDSet = "2,3,1084,6,7,1084";

                foreach (TimePeriod period in condition.Periods)
                {
                    List<QueryCondUnit> RegionChangedPeriods = parseChangePeriodsOfRegion(period);
                    foreach (QueryCondUnit cond in RegionChangedPeriods)
                    {
                        queryPeriodInfo(cond.period, clientProxy, cond, statImgIDSet, MainModel.CurGridColorUnitMatrix);
                    }
                }

                WaitBox.Text = "数据获取完毕，进行显示预处理...";
                if (MainModel.CurGridCoverData != null)
                {
                    MainModel.CurGridCoverData.Clear();
                }
            }
            catch (Exception e)
            {
                log.Error("Error:" + e.Message);
            }
            finally
            {
                System.Threading.Thread.Sleep(1000);
                WaitBox.Close();
            }
            MapGridLayer.NeedFreshFullImg = true;
        }

        private List<QueryCondUnit> parseChangePeriodsOfRegion(TimePeriod period)
        {
            List<QueryCondUnit> condPeriodList = new List<QueryCondUnit>();
            double ltlong = 0, ltlat = 0, brlong = 0, brlat = 0; 

            QueryCondUnit cond = new QueryCondUnit();
            TimePeriod tpseg = new TimePeriod();
            DbRect dbRect = condition.Geometorys.RegionBounds;
            ltlong = Math.Round(dbRect.x1, 4);
            ltlat = Math.Round(dbRect.y2, 4);

            brlong = dbRect.x2;
            brlat = dbRect.y1;

            tpseg.SetPeriod(period.BeginTime, period.EndTime);
            cond.period = tpseg;
            cond.ltLongitude = ltlong;
            cond.ltLatitude = ltlat;
            cond.brLongitude = brlong;
            cond.brLatitude = brlat;
            condPeriodList.Add(cond);

            return condPeriodList;
        }

        protected override void AddExtraCondition(Package package, params object[] reservedParams)
        {
            if (reservedParams != null && reservedParams.Length > 0)
            {
                QueryCondUnit cond = reservedParams[0] as QueryCondUnit;
                package.Content.AddParam((byte)OpOptionDef.AreaSelectIntersect);
                package.Content.AddParam(cond.ltLongitude);
                package.Content.AddParam(cond.ltLatitude);
                package.Content.AddParam(cond.brLongitude);
                package.Content.AddParam(cond.brLatitude);
                AddDIYEndOpFlag(package);
            }
        }

        protected override bool getConditionBeforeQuery()
        {
            lteCellGridUnit = new Dictionary<LTECell, List<string>>();
            lteColorUnitGridCellInfos = new Dictionary<string, List<GridCellInfos>>();
            return true;
        }

        Dictionary<string, string> strFormulaDic = null;
        private void setFormula()
        {
            strFormulaDic = new Dictionary<string, string>();
            strFormulaDic.Add("LTE栅格文件时间", "Lte_0804");
            strFormulaDic.Add("LTE栅格小区场强", "Lte_61210309");
            strFormulaDic.Add("LTE栅格邻区_1_EARFCN", "Lte_61215303");
            strFormulaDic.Add("LTE栅格邻区_1_PCI", "Lte_61215304");
            strFormulaDic.Add("LTE栅格邻区_1_RSRP", "Lte_61215305");
            strFormulaDic.Add("LTE栅格邻区_2_EARFCN", "Lte_61215309");
            strFormulaDic.Add("LTE栅格邻区_2_PCI", "Lte_6121530A");
            strFormulaDic.Add("LTE栅格邻区_2_RSRP", "Lte_6121530B");
            strFormulaDic.Add("LTE栅格邻区_3_EARFCN", "Lte_6121530F");
            strFormulaDic.Add("LTE栅格邻区_3_PCI", "Lte_61215310");
            strFormulaDic.Add("LTE栅格邻区_3_RSRP", "Lte_61215311");
            strFormulaDic.Add("LTE栅格邻区_4_EARFCN", "Lte_61215315");
            strFormulaDic.Add("LTE栅格邻区_4_PCI", "Lte_61215316");
            strFormulaDic.Add("LTE栅格邻区_4_RSRP", "Lte_61215317");
            strFormulaDic.Add("LTE栅格邻区_5_EARFCN", "Lte_6121531B");
            strFormulaDic.Add("LTE栅格邻区_5_PCI", "Lte_6121531C");
            strFormulaDic.Add("LTE栅格邻区_5_RSRP", "Lte_6121531D");
            strFormulaDic.Add("LTE栅格邻区_6_EARFCN", "Lte_61215321");
            strFormulaDic.Add("LTE栅格邻区_6_PCI", "Lte_61215322");
            strFormulaDic.Add("LTE栅格邻区_6_RSRP", "Lte_61215323");
        }

        protected override void recieveInfo_ImgGrid(ClientProxy clientProxy, params object[] reservedParams)
        {
            GridMatrix<ColorUnit> colorMatrix = reservedParams[0] as GridMatrix<ColorUnit>;
            Package package = clientProxy.Package;
            int counter = 0;
            bool recved = false;
            int curPercent = 11;
            DTDataHeaderManager.GetInstance();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (!recved)
                {
                    WaitBox.Text = "正在从服务器接收数据...";
                }
                recved = true;
                KPIStatDataBase singleStatData = null;
                if (isFileHeaderContentType(package.Content.Type))
                {
                    //DTDataHeader header = recieveFileHeader(package.Content, fileHeaderColumnDef)
                    //headerManager.AddDTDataHeader(header)
                }
                else if (isKPIDataContent(package, out singleStatData))
                {
                    fillData(colorMatrix, package, curImgColumnDef, singleStatData);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    MessageBox.Show("Unexpected type: " + package.Content.Type);
                    break;
                }
                int tmp = (int)(Math.Log(counter++) * 10);
                if (tmp < 95 && tmp > 0 && curPercent != tmp)
                {
                    WaitBox.ProgressPercent = tmp;
                }
                else if (tmp > 95)
                {
                    curPercent = 5;
                    counter = 0;
                }
            }
        }

        private void fillData(GridMatrix<ColorUnit> colorMatrix, Package package, List<StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            StatDataHubBase statData;
            GridCellInfos gridCellInfos = new GridCellInfos();
            gridCellInfos.ITac = package.Content.GetParamInt();
            gridCellInfos.IEci = package.Content.GetParamInt();
            gridCellInfos.DLng = package.Content.GetParamDouble();
            gridCellInfos.DLat = package.Content.GetParamDouble();
            //gridCellInfos.IFileTime = package.Content.GetParamInt()
            fillStatData(package, curImgColumnDef, singleStatData);

            if (gridCellInfos.LteCell != null)
            {
                ColorUnit cu = new ColorUnit();
                cu.LTLng = gridCellInfos.DLng;
                cu.LTLat = gridCellInfos.DLat;
                int rAt, cAt;
                GridHelper.GetIndexOfDefaultSizeGrid(cu.CenterLng, cu.CenterLat, out rAt, out cAt);
                cu = colorMatrix[rAt, cAt];
                if (cu == null)
                {
                    cu = new ColorUnit();
                    cu.LTLng = gridCellInfos.DLng;
                    cu.LTLat = gridCellInfos.DLat;
                    colorMatrix[rAt, cAt] = cu;
                }
                cu.Status = 1;
                statData = new StatDataHubBase();
                statData.AddStatData(singleStatData, false);
                gridCellInfos.CalRSRP(statData, strFormulaDic);

                string strLngLatKey = gridCellInfos.DLng + "" + gridCellInfos.DLat;
                if (!lteCellGridUnit.ContainsKey(gridCellInfos.LteCell))
                {
                    List<string> strGridLngLat = new List<string>();
                    strGridLngLat.Add(strLngLatKey);
                    lteCellGridUnit.Add(gridCellInfos.LteCell, strGridLngLat);
                }
                else
                {
                    if (!lteCellGridUnit[gridCellInfos.LteCell].Contains(strLngLatKey))
                    {
                        lteCellGridUnit[gridCellInfos.LteCell].Add(strLngLatKey);
                    }
                }
                addLteColorUnitGridCellInfos(singleStatData, gridCellInfos, strLngLatKey);
            }
        }

        private void addLteColorUnitGridCellInfos(KPIStatDataBase singleStatData, GridCellInfos gridCellInfos, string strLngLatKey)
        {
            if (!lteColorUnitGridCellInfos.ContainsKey(strLngLatKey))
            {
                List<GridCellInfos> gridCellListTmp = new List<GridCellInfos>();
                gridCellListTmp.Add(gridCellInfos);
                lteColorUnitGridCellInfos.Add(strLngLatKey, gridCellListTmp);
            }
            else
            {
                bool isExit = false;
                foreach (GridCellInfos gridCell in lteColorUnitGridCellInfos[strLngLatKey])
                {
                    if (gridCell.LteCell == gridCellInfos.LteCell)
                    {
                        gridCell.statData.AddStatData(singleStatData, false);
                        gridCell.CalRSRP(gridCell.statData, strFormulaDic);
                        isExit = true;
                        break;
                    }
                }
                if (!isExit)
                {
                    lteColorUnitGridCellInfos[strLngLatKey].Add(gridCellInfos);
                }
            }
        }

        protected override void fireShowResult()
        {
            MainModel.RefreshLegend();
            MainModel.IsPrepareWithoutGridPartParam = false;
            fireShowForm();
            MainModel.MainForm.FireLteSignalToInterenceQueried();
        }

        protected void fireShowForm()
        {
            LteSignalToInterenceDataForm frm = MainModel.GetInstance().CreateResultForm(typeof(LteSignalToInterenceDataForm)) as LteSignalToInterenceDataForm;
            frm.Owner = MainModel.MainForm;
            frm.FillAndCalData();
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class GridCellInfos
    {
        public int ITac { get; set; }
        public int IEci { get; set; }

        public LTECell LteCell
        {
            get
            {
                LTECell lteCell = CellManager.GetInstance().GetCurrentLTECell(ITac, IEci);
                return lteCell;
            }
        }
        
        public double DLng { get; set; }
        public double DLat { get; set; }
        public int IFileTime { get; set; }

        public DateTime dFileTime
        {
            get
            {
                if (IFileTime != 0)
                {
                    return JavaDate.GetDateTimeFromMilliseconds(1000L * IFileTime);
                } 
                else
                {
                    return DateTime.Now;
                }
            }
        }
        
        public float FRsrp { get; set; }
        public int INEARFCN_1 { get; set; }
        public int IPCI_1 { get; set; }
        public float FNRsrp_1 { get; set; }
        public int INEARFCN_2 { get; set; }
        public int IPCI_2 { get; set; }
        public float FNRsrp_2 { get; set; }
        public int INEARFCN_3 { get; set; }
        public int IPCI_3 { get; set; }
        public float FNRsrp_3 { get; set; }
        public int INEARFCN_4 { get; set; }
        public int IPCI_4 { get; set; }
        public float FNRsrp_4 { get; set; }
        public int INEARFCN_5 { get; set; }
        public int IPCI_5 { get; set; }
        public float FNRsrp_5 { get; set; }
        public int INEARFCN_6 { get; set; }
        public int IPCI_6 { get; set; }
        public float FNRsrp_6 { get; set; }

        public LTECell nLteCell_1
        {
            get{ return CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(dFileTime, INEARFCN_1, IPCI_1, DLng, DLat);}
        }
        public LTECell nLteCell_2
        {
            get{ return CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(dFileTime, INEARFCN_2, IPCI_2, DLng, DLat);}
        }
        public LTECell nLteCell_3
        {
            get{ return CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(dFileTime, INEARFCN_3, IPCI_3, DLng, DLat);}
        }
        public LTECell nLteCell_4
        {
            get{ return CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(dFileTime, INEARFCN_4, IPCI_4, DLng, DLat);}
        }
        public LTECell nLteCell_5
        {
            get{ return CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(dFileTime, INEARFCN_5, IPCI_5, DLng, DLat);}
        }
        public LTECell nLteCell_6
        {
            get{ return CellManager.GetInstance().GetNearestLTECellByEARFCNPCI(dFileTime, INEARFCN_6, IPCI_6, DLng, DLat);}
        }
        public void CalRSRP(StatDataHubBase statData, Dictionary<string, string> strFormulaDic)
        {
            IFileTime = (int)statData.CalcValueByFormula(strFormulaDic["LTE栅格文件时间"]);
            FRsrp = (float)statData.CalcValueByFormula(strFormulaDic["LTE栅格小区场强"]);
            INEARFCN_1 = (int)statData.CalcValueByFormula(strFormulaDic["LTE栅格邻区_1_EARFCN"]);
            IPCI_1 = (int)statData.CalcValueByFormula(strFormulaDic["LTE栅格邻区_1_PCI"]);
            FNRsrp_1 = (float)statData.CalcValueByFormula(strFormulaDic["LTE栅格邻区_1_RSRP"]);
            INEARFCN_2 = (int)statData.CalcValueByFormula(strFormulaDic["LTE栅格邻区_2_EARFCN"]);
            IPCI_2 = (int)statData.CalcValueByFormula(strFormulaDic["LTE栅格邻区_2_PCI"]);
            FNRsrp_2 = (float)statData.CalcValueByFormula(strFormulaDic["LTE栅格邻区_2_RSRP"]);
            INEARFCN_3 = (int)statData.CalcValueByFormula(strFormulaDic["LTE栅格邻区_3_EARFCN"]);
            IPCI_3 = (int)statData.CalcValueByFormula(strFormulaDic["LTE栅格邻区_3_PCI"]);
            FNRsrp_3 = (float)statData.CalcValueByFormula(strFormulaDic["LTE栅格邻区_3_RSRP"]);
            INEARFCN_4 = (int)statData.CalcValueByFormula(strFormulaDic["LTE栅格邻区_4_EARFCN"]);
            IPCI_4 = (int)statData.CalcValueByFormula(strFormulaDic["LTE栅格邻区_4_PCI"]);
            FNRsrp_4 = (float)statData.CalcValueByFormula(strFormulaDic["LTE栅格邻区_4_RSRP"]);
            INEARFCN_5 = (int)statData.CalcValueByFormula(strFormulaDic["LTE栅格邻区_5_EARFCN"]);
            IPCI_5 = (int)statData.CalcValueByFormula(strFormulaDic["LTE栅格邻区_5_PCI"]);
            FNRsrp_5 = (float)statData.CalcValueByFormula(strFormulaDic["LTE栅格邻区_5_RSRP"]);
            INEARFCN_6 = (int)statData.CalcValueByFormula(strFormulaDic["LTE栅格邻区_6_EARFCN"]);
            IPCI_6 = (int)statData.CalcValueByFormula(strFormulaDic["LTE栅格邻区_6_PCI"]);
            FNRsrp_6 = (float)statData.CalcValueByFormula(strFormulaDic["LTE栅格邻区_6_RSRP"]);
        }

        public Dictionary<LTECell, float> gridLteCellDic
        {
            get
            {
                Dictionary<LTECell, float> lteCellRsrpDic = new Dictionary<LTECell, float>();
                if (nLteCell_1 != null)
                {
                    lteCellRsrpDic.Add(nLteCell_1, FNRsrp_1);
                }
                if (nLteCell_2 != null)
                {
                    lteCellRsrpDic.Add(nLteCell_2, FNRsrp_2);
                }
                if (nLteCell_3 != null)
                {
                    lteCellRsrpDic.Add(nLteCell_3, FNRsrp_3);
                }
                if (nLteCell_4 != null)
                {
                    lteCellRsrpDic.Add(nLteCell_4, FNRsrp_4);
                }
                if (nLteCell_5 != null)
                {
                    lteCellRsrpDic.Add(nLteCell_5, FNRsrp_5);
                }
                if (nLteCell_6 != null)
                {
                    lteCellRsrpDic.Add(nLteCell_6, FNRsrp_6);
                }
                return lteCellRsrpDic;
            }
        }

        public int iGridLteCell(float fCurrRsrpDiff)
        {
            int iSignalCount = 0;
            foreach (float fR in gridLteCellDic.Values)
            {
                if (fR >= fCurrRsrpDiff)
                {
                    iSignalCount++;
                    break;
                }
            }
            return iSignalCount;
        }

        public StatDataHubBase statData { get; set; } = new StatDataHubBase();
    }

    public class LteSignalToInterferenceItem
    {
        public int ISN { get; set; }
        public LTECell LteCell { get; set; }
        public string StrCellName { get; set; }
        public int ITac { get; set; }
        public int IEci { get; set; }
        public float FRsrp { get; set; }
        public int ICellGridCount { get; set; }
        public int ISignalCellGridCount { get; set; }
        public double DSignalRate { get; set; }

        public string StrSignalRate
        {
            get
            {
                return DSignalRate.ToString("0.0000");
            }
        }

        public LteSignalToInterferenceItem()
        {
            LteCell = new LTECell();
            ISN = 0;
            StrCellName = "";
            ITac = 0;
            IEci = 0;
            ICellGridCount = 0;
            ISignalCellGridCount = 0;
            DSignalRate = 0;
        }
    }
}
