﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.Model.BaseInfo
{
    public class UpdateUserPwd : DIYSQLBase
    {
        readonly List<User> users2Update = new List<User>();
        readonly bool upModifyTimeOnly = false;
        bool dbHasModifyColumn = true;
        public UpdateUserPwd(List<User> roles2Update, bool upModifyTimeOnly)
            : base(MainModel.GetInstance())
        {
            this.users2Update = roles2Update;
            this.upModifyTimeOnly = upModifyTimeOnly;
            MainDB = true;
        }
        public UpdateUserPwd(User role2Update, bool upModifyTimeOnly, int dBid)
            : base(MainModel.GetInstance())
        {
            this.users2Update.Add(role2Update);
            this.upModifyTimeOnly = upModifyTimeOnly;
            this.dbid = dBid;
            MainDB = true;
        }
        //sql语句可能过长，需分包处理
        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;

                dbHasModifyColumn = checkHasModifyColumn(clientProxy);

                if (MainDB)
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
                }
                else
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
                }

                E_VType[] retArrDef = getSqlRetTypeArr();
                string strsql = getSqlTextString();
                if (string.IsNullOrEmpty(strsql))
                {
                    return;
                }

                string[] strArr = strsql.Split(';');
                int curIdx = 0;
                while (curIdx < strArr.Length)
                {
                    StringBuilder strb = getCurSql(package, retArrDef, ref strsql, strArr, ref curIdx);
                    package.Content.AddParam(strb.ToString());
                    clientProxy.Send();
                    System.Threading.Thread.Sleep(1000);
                }
                receiveRetData(clientProxy);
            }
            catch
            {
                ErrorInfo = "修改密码失败！";
            }
        }

        private static StringBuilder getCurSql(Package package, E_VType[] retArrDef, ref string strsql, string[] strArr, ref int curIdx)
        {
            StringBuilder strb = new StringBuilder();
            for (; curIdx < strArr.Length; curIdx++)
            {
                strsql = strArr[curIdx];
                if (string.IsNullOrEmpty(strsql))
                {
                    continue;
                }
                strb.Append(strsql + ";");
                if (strb.Length > 6000)
                {
                    break;
                }
            }
            package.Content.PrepareAddParam();
            package.Content.AddParam(strb.ToString());

            strb = new StringBuilder();
            if (retArrDef != null)
            {
                for (int i = 0; i < retArrDef.Length; i++)
                {
                    strb.Append((int)retArrDef[i]);
                    if (i < retArrDef.Length - 1)
                    {
                        strb.Append(",");
                    }
                }
            }

            return strb;
        }

        private bool checkHasModifyColumn(ClientProxy clientProxy)
        {
            bool hasModifyColumn = false;
            Package package = clientProxy.Package;

            if (MainDB)
            {
                package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
            }
            else
            {
                package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
            }

            package.Content.PrepareAddParam();
            string sqlCheck = @"if exists
(select 1 from sysobjects a join syscolumns b on a.id=b.id
   where a.id=object_id('tb_cfg_static_user') and b.name='modifypwd_time')
   select 1
else
   select 0";
            package.Content.AddParam(sqlCheck);
            package.Content.AddParam((E_VType.E_Int).ToString());
            clientProxy.Send();
            clientProxy.Recieve();
            if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
            {
                package.Content.PrepareGetParam();
                int hasColumn = package.Content.GetParamInt();
                hasModifyColumn = hasColumn == 1;
            }

            return hasModifyColumn;
        }
        protected override string getSqlTextString()
        {
            if (upModifyTimeOnly && !dbHasModifyColumn)
            {
                return "";
            }

            StringBuilder sb = new StringBuilder();
            string updateTxt = "";
            if (dbHasModifyColumn)
            {
                if (upModifyTimeOnly)
                {
                    updateTxt = "update tb_cfg_static_user set modifypwd_time=getdate() where iid={1};";
                }
                else
                {
                    updateTxt = "update tb_cfg_static_user set logon_pwd='{0}',modifypwd_time=getdate() where iid={1};";
                }
            }
            else
            {
                updateTxt = "update tb_cfg_static_user set logon_pwd='{0}' where iid={1};";
            }

            foreach (User usr in users2Update)
            {
                if (string.IsNullOrEmpty(usr.NewPassword) || usr.NewPassword == usr.Password)
                {
                    continue;
                }
                string md5Pw = ClientProxy.getMD5String(Encoding.Default.GetBytes(usr.NewPassword));
                sb.Append(string.Format(updateTxt, md5Pw, usr.ID));
            }
            return sb.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_Int;
            return rType;
        }

        public override string Name
        {
            get { return "更新用户密码信息"; }
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
        }
    }

    public class UpdateUserPwdRecord : DIYSQLBase
    {
        readonly StringBuilder strbTips = new StringBuilder();
        readonly List<User> users2Update = new List<User>();
        public UpdateUserPwdRecord(List<User> roles2Update)
            : base(MainModel.GetInstance())
        {
            this.users2Update = roles2Update;
            MainDB = true;
        }
        public UpdateUserPwdRecord(User role2Update, int dBid)
            : base(MainModel.GetInstance())
        {
            this.users2Update.Add(role2Update);
            this.dbid = dBid;
            MainDB = true;
        }

        protected override string getSqlTextString()
        {
            StringBuilder sb = new StringBuilder();
            StringBuilder err = new StringBuilder();
            foreach (User usr in users2Update)
            {
                if (!string.IsNullOrEmpty(usr.NewPassword))
                {
                    if (string.IsNullOrEmpty(usr.ModifyPasswordCause))
                    {
                        err.Append(usr.LoginName + "用户密码修改失败（密码修改原因不能为空）！\r\n");
                        continue;
                    }
                    string md5Pw = ClientProxy.getMD5String(Encoding.Default.GetBytes(usr.NewPassword));
                    sb.Append(string.Format("exec rd_sp_user_updatePwd_record {0},'{1}','{2}','{3}','{4}','{5}';"
                        , usr.ID, usr.LoginName, md5Pw, mainModel.User.LoginName, usr.ModifyPasswordCause, ""));
                }
                usr.ModifyPasswordCause = null;
            }
            if (err.Length > 0)
            {
                ErrorInfo += err.ToString();
            }
            return sb.ToString();
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[1];
            rType[0] = E_VType.E_String;
            return rType;
        }

        public override string Name
        {
            get { return "更新并记录用户密码信息"; }
        }
        protected override void queryInThread(object o)
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                package.Command = Command.DIYSearch;
                package.SubCommand = SubCommand.Request;

                if (MainDB)
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL_FORMAINDB;
                }
                else
                {
                    package.Content.Type = RequestType.REQTYPE_DIY_MODEL_SQL;
                }

                E_VType[] retArrDef = getSqlRetTypeArr();
                string strsql = getSqlTextString();
                if (string.IsNullOrEmpty(strsql))
                {
                    return;
                }

                string[] strArr = strsql.Split(';');
                int curIdx = 0;
                while (curIdx < strArr.Length)
                {
                    StringBuilder strb = getCurSql(package, retArrDef, ref strsql, strArr, ref curIdx);
                    package.Content.AddParam(strb.ToString());
                    clientProxy.Send();
                    System.Threading.Thread.Sleep(1000);
                }
                receiveRetData(clientProxy);
            }
            catch
            {
                ErrorInfo = "修改密码失败！";
            }
            finally
            {
                if (strbTips.Length > 0)
                {
                    ErrorInfo += strbTips.ToString();
                }
            }
        }

        private static StringBuilder getCurSql(Package package, E_VType[] retArrDef, ref string strsql, string[] strArr, ref int curIdx)
        {
            StringBuilder strb = new StringBuilder();
            for (; curIdx < strArr.Length; curIdx++)
            {
                strsql = strArr[curIdx];
                if (string.IsNullOrEmpty(strsql))
                {
                    continue;
                }
                strb.Append(strsql + ";");
                if (strb.Length > 6000)
                {
                    break;
                }
            }
            package.Content.PrepareAddParam();
            package.Content.AddParam(strb.ToString());

            strb = new StringBuilder();
            if (retArrDef != null)
            {
                for (int i = 0; i < retArrDef.Length; i++)
                {
                    strb.Append((int)retArrDef[i]);
                    if (i < retArrDef.Length - 1)
                    {
                        strb.Append(",");
                    }
                }
            }

            return strb;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    string strTip = package.Content.GetParamString();
                    if (!string.IsNullOrEmpty(strTip) && !strTip.Contains("成功"))
                    {
                        strbTips.AppendLine(strTip);
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                setProgressPercent(ref index, ref progress);
            }
        }
    }
}
