﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public class CreateLTESignalChartForm : CreateChildForm
    {
        public CreateLTESignalChartForm(MainModel mm)
            : base(mm)
        { 
        }
        public override string Description
        {
            get
            {
                return "创建TD_LTE扫频频点场强图窗口 LTESignalChartForm ";
            }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 20000, 20027, this.Name);
        }
        public override string Name
        {
            get
            {
                return "创建TD_LTE扫频频点场强图窗口";
            }
        }

        protected override void initAction()
        {
            Dictionary<string, object> actionParam = new Dictionary<string, object>();
            actionParam["MainForm"] = MainModel.MainForm;
            actionParam["AssemblyName"] = "RAMS.exe";
            actionParam["TypeName"] = "MasterCom.RAMS.Func.LTESignalChartForm";
            actionParam["Text"] = "TD_LTE频点场强图";
            actionParam["ImageFilePath"] = @"images\GSM频率核查\同邻频.png";
            action = new ActionCreateChildFrame();
            action.Param = actionParam;
        }
    }
}
