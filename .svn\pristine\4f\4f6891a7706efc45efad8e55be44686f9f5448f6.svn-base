﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func.Voronoi;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class GSMBTSVoiLaunchForm : ShowFuncForm
    {
        public GSMBTSVoiLaunchForm(MainModel mm)
            : base(mm)
        {
        }

        public override string Name
        {
            get { return "GSM900基站"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 19000, 19025, this.Name);
        }

        protected override void showForm()
        {
            GSMBTSVoiCoverManager manager = GSMBTSVoiCoverManager.GetInstance();
            switch (manager.Construct(true, true))
            {
                case VoiCoverResult.Succeed:
                    manager.Show();
                    break;
                case VoiCoverResult.Failed:
                    MessageBox.Show(manager.LastErrorText, "泰森多边形");
                    break;
            }
        }
    }

    public class DSCBTSVoiLaunchForm : ShowFuncForm
    {
        public DSCBTSVoiLaunchForm(MainModel mm)
            : base(mm)
        {
        }

        public override string Name
        {
            get { return "GSM900基站"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 19000, 19026, this.Name);
        }

        protected override void showForm()
        {
            DCSBTSVoiCoverManager manager = DCSBTSVoiCoverManager.GetInstance();
            switch (manager.Construct(true, true))
            {
                case VoiCoverResult.Succeed:
                    manager.Show();
                    break;
                case VoiCoverResult.Failed:
                    MessageBox.Show(manager.LastErrorText, "泰森多边形");
                    break;
            }
        }
    }

    public class TDNodeBVoiLaunchForm : ShowFuncForm
    {
        public TDNodeBVoiLaunchForm(MainModel mm)
            : base(mm)
        {
        }

        public override string Name
        {
            get { return "TD基站"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 19000, 19027, this.Name);
        }

        protected override void showForm()
        {
            TDNodeBVoiCoverManager manager = TDNodeBVoiCoverManager.GetInstance();
            switch (manager.Construct(true, true))
            {
                case VoiCoverResult.Succeed:
                    manager.Show();
                    break;
                case VoiCoverResult.Failed:
                    MessageBox.Show(manager.LastErrorText, "泰森多边形");
                    break;
            }
        }
    }

    public class LTEBTSVoiLaunchForm : ShowFuncForm
    {
        public LTEBTSVoiLaunchForm(MainModel mm)
            : base(mm)
        {
        }

        public override string Name
        {
            get { return "TD-LTE基站"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 19000, 19028, this.Name);
        }

        protected override void showForm()
        {
            LTEBTSVoiCoverManager manager = LTEBTSVoiCoverManager.GetInstance();
            switch (manager.Construct(true, true))
            {
                case VoiCoverResult.Succeed:
                    manager.Show();
                    break;
                case VoiCoverResult.Failed:
                    MessageBox.Show(manager.LastErrorText, "泰森多边形");
                    break;
            }
        }
    }

    public class WNodeBVoiLaunchForm : ShowFuncForm
    {
        public WNodeBVoiLaunchForm(MainModel mm)
            : base(mm)
        {
        }

        public override string Name
        {
            get { return "WCDMA基站"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 19000, 19029, this.Name);
        }

        protected override void showForm()
        {
            WNodeBVoiCoverManager manager = WNodeBVoiCoverManager.GetInstance();
            switch (manager.Construct(true, true))
            {
                case VoiCoverResult.Succeed:
                    manager.Show();
                    break;
                case VoiCoverResult.Failed:
                    MessageBox.Show(manager.LastErrorText, "泰森多边形");
                    break;
            }
        }
    }

    public class GSMCellVoiLaunchForm : ShowFuncForm
    {
        public GSMCellVoiLaunchForm(MainModel mm) : base(mm)
        {
        }

        public override string Name
        {
            get { return "GSM小区"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 19000, 19030, this.Name);
        }

        protected override void showForm()
        {
            GSMCellVoiCover gsmCellVoiCover = new GSMCellVoiCover();
            Dictionary<Cell, List<Vertex[]>> cellPolysDict = gsmCellVoiCover.Construct();
            if (cellPolysDict == null)
            {
                MessageBox.Show(gsmCellVoiCover.LastErrorText, "泰森多边形");
                return;
            }

            List<List<Vertex[]>> drawList = new List<List<Vertex[]>>();
            foreach (List<Vertex[]> lst in cellPolysDict.Values)
            {
                drawList.Add(lst);
            }
            VoronoiLayer.GetInstance().Draw(drawList);
        }
    }

    public class TDCellVoiLaunchForm : ShowFuncForm
    {
        public TDCellVoiLaunchForm(MainModel mm)
            : base(mm)
        {
        }

        public override string Name
        {
            get { return "TD小区"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 19000, 19031, this.Name);
        }

        protected override void showForm()
        {
            TDCellVoiCover tdCellVoiCover = new TDCellVoiCover();
            Dictionary<TDCell, List<Vertex[]>> cellPolysDict = tdCellVoiCover.Construct();
            if (cellPolysDict == null)
            {
                MessageBox.Show(tdCellVoiCover.LastErrorText, "泰森多边形");
                return;
            }

            List<List<Vertex[]>> drawList = new List<List<Vertex[]>>();
            foreach (List<Vertex[]> lst in cellPolysDict.Values)
            {
                drawList.Add(lst);
            }
            VoronoiLayer.GetInstance().Draw(drawList);
        }
    }

    public class LTECellVoiLaunchForm : ShowFuncForm
    {
        public LTECellVoiLaunchForm(MainModel mm)
            : base(mm)
        {
        }

        public override string Name
        {
            get { return "LTE小区"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 19000, 19031, this.Name);
        }

        protected override void showForm()
        {
            LTECellVoiCover lteCellVoiCover = new LTECellVoiCover();
            Dictionary<LTECell, List<Vertex[]>> cellPolysDict = lteCellVoiCover.Construct();
            if (cellPolysDict == null)
            {
                MessageBox.Show(lteCellVoiCover.LastErrorText, "泰森多边形");
                return;
            }

            List<List<Vertex[]>> drawList = new List<List<Vertex[]>>();
            foreach (List<Vertex[]> lst in cellPolysDict.Values)
            {
                drawList.Add(lst);
            }
            VoronoiLayer.GetInstance().Draw(drawList);
        }
    }

    public class GSMBSCVoiLaunchForm : ShowFuncForm
    {
        public GSMBSCVoiLaunchForm(MainModel mm)
            : base(mm)
        {
        }

        public override string Name
        {
            get { return "GSM-BSC"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 19000, 19032, this.Name);
        }

        protected override void showForm()
        {
            GSMBSCVoiCover gsmBscVoiCover = new GSMBSCVoiCover();
            Dictionary<BSC, List<Vertex[]>> bscPolysDict = gsmBscVoiCover.Construct();
            if (bscPolysDict == null || bscPolysDict.Count == 0)
            {
                MessageBox.Show(gsmBscVoiCover.LastErrorText, "泰森多边形");
                return;
            }

            List<string> labels = new List<string>();
            List<List<Vertex[]>> drawList = new List<List<Vertex[]>>();
            foreach (BSC bsc in bscPolysDict.Keys)
            {
                labels.Add(bsc.Name);
                drawList.Add(bscPolysDict[bsc]);
            }
            VoronoiLayer.GetInstance().Draw(drawList, labels);
        }
    }

    public class TDBSCVoiLaunchForm : ShowFuncForm
    {
        public TDBSCVoiLaunchForm(MainModel mm)
            : base(mm)
        {
        }

        public override string Name
        {
            get { return "TD-RNC"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 19000, 19033, this.Name);
        }

        protected override void showForm()
        {
            TDBSCVoiCover tdBscVoiCover = new TDBSCVoiCover();
            Dictionary<TDRNC, List<Vertex[]>> bscPolysDict = tdBscVoiCover.Construct();
            if (bscPolysDict == null || bscPolysDict.Count == 0)
            {
                MessageBox.Show(tdBscVoiCover.LastErrorText, "泰森多边形");
                return;
            }

            List<string> labels = new List<string>();
            List<List<Vertex[]>> drawList = new List<List<Vertex[]>>();
            foreach (TDRNC bsc in bscPolysDict.Keys)
            {
                labels.Add(bsc.Name);
                drawList.Add(bscPolysDict[bsc]);
            }
            VoronoiLayer.GetInstance().Draw(drawList, labels);
        }
    }

    public class VoronoiExportShp : ShowFuncForm
    {
        public VoronoiExportShp(MainModel mm)
            : base(mm)
        {
        }

        public override string Name
        {
            get { return "导出图层"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 19000, 19034, this.Name);
        }

        protected override void showForm()
        {
            VoronoiLayer.GetInstance().ExportToShp();
        }
    }

    public class SetVoiLayerStyleFunc : ShowFuncForm
    {
        public SetVoiLayerStyleFunc(MainModel mm) : base(mm)
        {
        }

        public override string Name
        {
            get { return "样式设置"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 19000, 19042, this.Name);
        }

        protected override void showForm()
        {
            VoiLayerStyleSetForm setForm = new VoiLayerStyleSetForm();
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                VoronoiLayer.GetInstance().Refresh();
            }
        }
    }
}
