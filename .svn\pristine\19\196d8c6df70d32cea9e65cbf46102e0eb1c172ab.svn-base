﻿using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Model
{
    public enum NRBTSType
    {
        Outdoor = 1,
        Indoor
    }

    public class NRBTS : Snapshot<NRBTS>, IComparable<NRBTS>, MasterCom.RAMS.Func.IVoronoi, ISite
    {
        public NRBTS()
        {
            Value = this;
        }

        public string Name { get; set; }
        public int BTSID { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public NRBTSType Type { get; set; }
        public string TypeStringDesc
        {
            get
            {
                if (Type == NRBTSType.Indoor)
                {
                    return "室内";
                }
                else if (Type == NRBTSType.Outdoor)
                {
                    return "室外";
                }
                return "";
            }
        }

        public string Description { get; set; }

        public double VertexX { get { return Longitude; } }
        public double VertexY { get { return Latitude; } }

        public List<NRCell> Cells { get; private set; } = new List<NRCell>();

        public List<NRCell> LatestCells
        {
            get
            {
                List<NRCell> ret = new List<NRCell>();
                foreach (NRCell cell in Cells)
                {
                    if (cell.ValidPeriod.IEndTime == int.MaxValue)
                    {
                        ret.Add(cell);
                    }
                }
                return ret;
            }
        }

        public string DetailInfo
        {
            get
            {
                StringBuilder info = new StringBuilder();
                info.Append("Name:").Append(Name);
                info.Append("\r\nLongitude:").Append(Longitude);
                info.Append("\r\nLatitude:").Append(Latitude);
                info.Append("\r\nType:").Append(TypeStringDesc);
                info.Append("\r\nDescription:").Append(Description);
                info.Append("\r\n");
                return info.ToString();
            }
        }

        public bool Within(double x1, double y1, double x2, double y2)
        {
            if (Longitude < x1 || Longitude > x2 || Latitude < y1 || Latitude > y2)
            {
                return false;
            }
            return true;
        }

        public bool Within(MTGis.DbRect dRect)
        {
            if (Longitude < dRect.x1 || Longitude > dRect.x2 || Latitude < dRect.y1 || Latitude > dRect.y2)
            {
                return false;
            }
            return true;
        }

        public int CompareTo(NRBTS other)
        {
            throw new NotImplementedException();
        }

        public void AddCell(NRCell cell)
        {
            Cells.Add(cell);
            cell.BelongBTS.Add(this);
        }

        public void Fill(MasterCom.RAMS.Net.Content content)
        {
            base.Fill(content.GetParamInt(), content.GetParamInt(), content.GetParamInt());
            Name = content.GetParamString();
            BTSID = content.GetParamInt();
            Longitude = content.GetParamDouble();
            Latitude = content.GetParamDouble();
            Type = parseType(content.GetParamString());
            Description = content.GetParamString();
        }

        private NRBTSType parseType(string s)
        {
            if ("是".Equals(s) || "室内".Equals(s))
            {
                return NRBTSType.Indoor;
            }
            else
            {
                return NRBTSType.Outdoor;
            }
        }
    }
}
