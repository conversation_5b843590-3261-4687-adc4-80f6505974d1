﻿using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Text;

namespace MasterCom.RAMS.BackgroundFunc.StationAccept_Shanxi
{
    public class UpLoadWorkParams_SX_NR : UpLoadWorkParams_SX
    {
        protected override string tableName { get { return "tb_btscheck_SX_cfg_cell_NR"; } }

        public UpLoadWorkParams_SX_NR(StationAcceptWorkParams workParams)
            : base(workParams)
        {
        }

        public void Save(StationAcceptWorkParams workParams, SqlConnectionStringBuilder sb)
        {
            SqlConnection sqlConn = new SqlConnection(sb.ToString());
            BCPStore bcp = new BCPStore();
            bcp.Init(sqlConn, tableName);

            var cellParamInfoDic = ((StationAcceptWorkParams_SX_NR)workParams).WorkParamSumDic;
            StringBuilder strb = new StringBuilder();
            foreach (var paramInfo in cellParamInfoDic.Values)
            {
                //基站
                foreach (var btsInfo in paramInfo.Values)
                {
                    strb.Append($"delete from {tableName} where BtsName = '{btsInfo.BtsNameFull}';");

                    //小区
                    foreach (var info in btsInfo.CellWorkParams)
                    {
                        CellAcceptWorkParam_SX_NR cellInfo = info as CellAcceptWorkParam_SX_NR;
                        object[] values = new object[] { cellInfo.DistrictName, cellInfo.BtsNameFull, cellInfo.ENodeBID, cellInfo.CellNameFull, cellInfo.CellID, cellInfo.Tac, cellInfo.Nci, cellInfo.Earfcn, cellInfo.Pci, cellInfo.SsbArfcn, cellInfo.Longitude * 10000000, cellInfo.Latitude * 10000000, cellInfo.CoverTypeDes, cellInfo.Altitude, cellInfo.Direction, cellInfo.Downward, (int)btsInfo.AnalysedType, cellInfo.UpdateTime, cellInfo.Remark, cellInfo.ImportTime };
                        bcp.AddData(values);
                        ValidCount++;
                    }
                }
            }

            try
            {
                bcp.Flush(sqlConn, strb.ToString());
                SaveInDBSuccess = true;
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo("单验工参入库成功！");
            }
            catch(Exception ex)
            {
                SaveInDBSuccess =  false;
                BackgroundFuncManager.GetInstance().ReportBackgroundInfo("单验工参入库失败！\n\t" + ex.Message);
            }
        }
    }

    public class WorkParamsQuery_SX_NR : DIYSQLBase
    {
        readonly StationAcceptCondition_SX_NR acceptCondition;
        public StationAcceptWorkParams_SX_NR WorkParams { get; set; }

        public WorkParamsQuery_SX_NR(StationAcceptCondition_SX_NR condition)
        {
            MainDB = true;
            acceptCondition = condition;
            WorkParams = new StationAcceptWorkParams_SX_NR();
        }

        protected override string getSqlTextString()
        {
            string type;
            if (acceptCondition.AcceptType == StationAcceptType.NotAccept)
            {
                type = ((int)AnalyseType.NeedAnalyse).ToString();
            }
            else if (acceptCondition.AcceptType == StationAcceptType.FailedAccept)
            {
                type = ((int)AnalyseType.FailedAnalyse).ToString();
            }
            else if (acceptCondition.AcceptType == StationAcceptType.NotAndFailedAccept)
            {
                type = ((int)AnalyseType.FailedAnalyse).ToString() + "," + ((int)AnalyseType.NeedAnalyse).ToString();
            }
            else
            {
                throw (new Exception("缺少对应的单验工参类型"));
            }

            string sql = string.Format(@"select [DistrictName],[BtsName],[ENodeBID],[CellName],[CellID],[Tac],[Nci],[Earfcn],[Pci],[SsbArfcn],[Longitude],[Latitude],[CoverTypeDes],[Altitude],[Direction],[Downward] from tb_btscheck_SX_cfg_cell_NR where AnalysedType in ({0})", type);

            if (acceptCondition.IsAnalysedNearest)
            {
                DateTime dt = DateTime.Now.Date.AddDays(-acceptCondition.NearestDay);
                sql += string.Format(@"and ImportTime >= {0}", dt);
            }

            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int i = 0;
            E_VType[] arr = new E_VType[16];
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int64;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_String;
            arr[i++] = E_VType.E_Int;
            arr[i++] = E_VType.E_Int;
            arr[i] = E_VType.E_Int;
            return arr;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CellAcceptWorkParam_SX_NR cellInfo = new CellAcceptWorkParam_SX_NR();
                    cellInfo.FillDataByDB(package);
                    WorkParams.AddWorkParamSum(cellInfo);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    break;
                }
            }
        }
    }
}
