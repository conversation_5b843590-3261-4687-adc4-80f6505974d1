﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public abstract class NRReasonBase
    {
        public string Name { get; set; }
        public override string ToString()
        {
            return Name ?? "";
        }
        public bool Enable { get; set; } = true;
        public abstract bool IsValid(TestPoint tp, NRTpManagerBase nRCond, params object[] resvParams);

        public virtual Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Enable"] = this.Enable;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey(this.Name))
                {
                    this.Enable = (bool)param["Enable"];
                }
            }
        }
    }

    public class NRReasonChangeFreq : NRReasonBase
    {
        public NRReasonChangeFreq()
        {
            this.Name = "切换频繁";
        }

        public override bool IsValid(Model.TestPoint tp, NRTpManagerBase nRCond, params object[] resvParams)
        {
            List<Event> evtList = NRWeakSINRReason.handOverTooMuchEvents;
            foreach (Event evt in evtList)
            {
                if (tp.DateTime >= evt.DateTime.AddSeconds(-NRWeakSINRReason.timeLimit) && tp.DateTime <= evt.DateTime)
                {
                    return true;
                }
            }
            return false;
        }
    }

    public class NRReasonHandOverProblem : NRReasonBase
    {
        public NRReasonHandOverProblem()
        {
            this.Name = "切换不合理";
        }

        public override bool IsValid(Model.TestPoint tp, NRTpManagerBase nRCond, params object[] resvParams)
        {
            if (NRWeakSINRReason.isHandOverProblem)
            {
                return true;
            }
            return false;
        }
    }

    public class NRReasonHandOverUnTimely : NRReasonBase
    {
        public NRReasonHandOverUnTimely()
        {
            this.Name = "切换不及时";
        }

        public override bool IsValid(Model.TestPoint tp, NRTpManagerBase nRCond, params object[] resvParams)
        {
            if (NRWeakSINRReason.isHandOverUnTimely)
            {
                return true;
            }
            return false;
        }
    }

    public class NRReasonMod3 : NRReasonBase
    {
        public NRReasonMod3()
        {
            this.Name = "模3干扰";
        }

        public float RSRPDiffMax { get; set; } = 6;

        public override bool IsValid(Model.TestPoint tp, NRTpManagerBase nRCond, params object[] resvParams)
        {
            int? nrEarfcn = (int?)nRCond.GetEARFCN(tp);
            int? nrPci = (int?)nRCond.GetPCI(tp);
            if (nrEarfcn == null || nrPci == null)
            {
                return false;
            }

            for (int i = 0; i < 10; i++)
            {
                int? nrNCellEarfcn = (int?)nRCond.GetNEARFCN(tp, i);
                if (nrNCellEarfcn != null && nrEarfcn == nrNCellEarfcn)//同频时才进行判断
                {
                    bool isValid = judgeValid(tp, nrPci, i, nRCond);
                    if (isValid)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        private bool judgeValid(TestPoint tp, int? nrPci, int i, NRTpManagerBase nRCond)
        {
            short? nrNCellPci = (short?)nRCond.GetNPCI(tp, i);
            if (nrNCellPci != null && nrPci % 3 == nrNCellPci % 3)
            {
                float? rsrp = nRCond.GetSCellRsrp(tp);
                float? nRsrp = nRCond.GetNCellRsrp(tp, i);
                if (rsrp != null && nRsrp != null)
                {
                    float diff = Math.Abs((float)(rsrp - nRsrp));
                    if (diff <= 6)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Enable"] = this.Enable;
                param["RSRPDiffMax"] = this.RSRPDiffMax;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("Enable"))
                {
                    this.Enable = (bool)param["Enable"];
                }
                if (param.ContainsKey("RSRPDiffMax"))
                {
                    this.RSRPDiffMax = (float)param["RSRPDiffMax"];
                }
            }
        }
    }

    public class NRReasonMultiCover : NRReasonBase
    {
        public NRReasonMultiCover()
        {
            this.Name = "重叠覆盖";
        }

        public int MultiNumMin { get; set; } = 4;
        public float RSRPDiffMax { get; set; } = 6;
        public float RSRSMin { get; set; } = -115;

        public override bool IsValid(Model.TestPoint tp, NRTpManagerBase nRCond, params object[] resvParams)
        {
            List<float> rsrpSet = new List<float>();
            object value = nRCond.GetSCellRsrp(tp);
            if (value != null)
            {
                rsrpSet.Add(float.Parse(value.ToString()));
            }
            for (int i = 0; i < 10; i++)
            {
                value = nRCond.GetNCellRsrp(tp, i);
                if (value == null)
                {
                    continue;
                }
                rsrpSet.Add(float.Parse(value.ToString()));
            }
            if (rsrpSet.Count == 0)
            {
                return false;
            }
            rsrpSet.Sort();
            int cnt = rsrpSet.Count;
            float max = rsrpSet[cnt - 1];
            if (cnt < MultiNumMin)
            {//有效的RSRP小于最新重叠覆盖个数，返回false
                return false;
            }
            for (int i = 1; i <= MultiNumMin; i++)
            {
                float rsrp = rsrpSet[cnt - i];
                if (rsrp < RSRSMin || max - rsrp > RSRPDiffMax)
                {
                    return false;
                }
            }
            return true;
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Enable"] = this.Enable;
                param["MultiNumMin"] = this.MultiNumMin;
                param["RSRPDiffMax"] = this.RSRPDiffMax;
                param["RSRSMin"] = this.RSRSMin;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("Enable"))
                {
                    this.Enable = (bool)param["Enable"];
                }
                if (param.ContainsKey("MultiNumMin"))
                {
                    this.MultiNumMin = (int)param["MultiNumMin"];
                }
                if (param.ContainsKey("RSRPDiffMax"))
                {
                    this.RSRPDiffMax = (float)param["RSRPDiffMax"];
                }
                if (param.ContainsKey("RSRSMin"))
                {
                    this.RSRSMin = (float)param["RSRSMin"];
                }
            }
        }
    }

    public class NRReasonsBackCover : NRReasonBase
    {
        public NRReasonsBackCover()
        {
            this.Name = "背向覆盖";
        }

        public double DirectionDif { get; set; } = 60;
        public double Cell2TpDis { get; set; } = 100;

        public override bool IsValid(Model.TestPoint tp, NRTpManagerBase nRCond, params object[] resvParams)
        {
            NRCell mainCell = tp.GetMainCell_NR();
            if (mainCell == null || mainCell.Direction > 360)
            {
                return false;
            }
            if (!isValidAngle(mainCell, tp.Longitude, tp.Latitude))
            {
                return true;
            }
            return false;
        }
        /// <summary>
        /// 采样点到小区的夹角是否属于正常角度
        /// </summary>
        private bool isValidAngle(NRCell cell, double longitude, double latitude)
        {
            double angleDiff = 0;
            double distance = cell.GetDistance(longitude, latitude);
            if (distance >= Cell2TpDis)
            {
                ///所有角度按正北方向算起始，顺时针算夹角，正北为0度
                double angle;
                double ygap = cell.GetDistance(cell.Longitude, latitude);
                double angleV = Math.Acos(ygap / distance);
                if (longitude >= cell.Longitude && latitude >= cell.Latitude)//1象限
                {
                    angle = angleV * 180 / Math.PI;
                }
                else if (longitude <= cell.Longitude && latitude >= cell.Latitude)//2象限
                {
                    angle = 360 - angleV * 180 / Math.PI;
                }
                else if (longitude <= cell.Longitude && latitude <= cell.Latitude)//3象限
                {
                    angle = 180 + angleV * 180 / Math.PI;
                }
                else//4象限
                {
                    angle = 180 - angleV * 180 / Math.PI;
                }

                angleDiff = Math.Abs(angle - cell.Direction);
                if (angleDiff > 180)
                {
                    angleDiff = 360 - angleDiff;
                }
                if (angleDiff >= DirectionDif)
                {
                    return false;
                }
            }
            return true;
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Enable"] = this.Enable;
                param["DirectionDif"] = this.DirectionDif;
                param["Cell2TpDis"] = this.Cell2TpDis;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("Enable"))
                {
                    this.Enable = (bool)param["Enable"];
                }
                if (param.ContainsKey("DirectionDif"))
                {
                    this.DirectionDif = (double)param["DirectionDif"];
                }
                if (param.ContainsKey("Cell2TpDis"))
                {
                    this.Cell2TpDis = (double)param["Cell2TpDis"];
                }
            }
        }
    }

    public class NRReasonsIndoorOutCover : NRReasonBase
    {
        public NRReasonsIndoorOutCover()
        {
            this.Name = "室分泄露";
        }

        public override bool IsValid(Model.TestPoint tp, NRTpManagerBase nRCond, params object[] resvParams)
        {
            NRCell mainCell = tp.GetMainCell_NR();
            if (mainCell != null && mainCell.Type == NRBTSType.Indoor)
            {
                return true;
            }
            //NR小区的室分小区判断
            //int? earfcn = (int?)nRCond.GetEARFCN(tp);
            //if (earfcn != null && LTECell.GetBandTypeByEarfcn((int)earfcn) == LTEBandType.E)//E频点的就是室内
            //{
            //    return true;
            //}

            return false;
        }
    }

    public class NRReasonsOverCover : NRReasonBase
    {
        public NRReasonsOverCover()
        {
            this.Name = "过覆盖";
        }

        public double OverRsrpMin { get; set; } = -100;
        public double CoverFactor { get; set; } = 1.1;

        public override bool IsValid(Model.TestPoint tp, NRTpManagerBase nRCond, params object[] resvParams)
        {
            float? rsrp = nRCond.GetSCellRsrp(tp);
            if (rsrp == null || rsrp < OverRsrpMin)
            {
                return false;
            }
            NRCell curCell= tp.GetMainCell_NR();
            if (curCell != null)
            {
                if (curCell.Type == NRBTSType.Indoor)
                {
                    return false;
                }
                if (curCell.Antennas == null || curCell.Antennas.Count == 0)
                {
                    return false;
                }
                double radiusOfCell = MasterCom.ES.Data.CfgDataProvider.CalculateRadius(curCell, 3);
                double rationalDistance = radiusOfCell * CoverFactor;

                double distanceToCell = MathFuncs.GetDistance(tp.Longitude, tp.Latitude, curCell.Longitude, curCell.Latitude);
                if (distanceToCell > rationalDistance)
                {
                    return true;
                }
            }
            return false;
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Enable"] = this.Enable;
                param["OverRsrpMin"] = this.OverRsrpMin;
                param["CoverFactor"] = this.CoverFactor;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("Enable"))
                {
                    this.Enable = (bool)param["Enable"];
                }
                if (param.ContainsKey("OverRsrpMin"))
                {
                    this.OverRsrpMin = (double)param["OverRsrpMin"];
                }
                if (param.ContainsKey("CoverFactor"))
                {
                    this.CoverFactor = (double)param["CoverFactor"];
                }
            }
        }
    }

    public class NRReasonSuddenWeak : NRReasonBase
    {
        public NRReasonSuddenWeak()
        {
            this.Name = "质量毛刺";
        }

        public override bool IsValid(Model.TestPoint tp, NRTpManagerBase nRCond, params object[] resvParams)
        {
            if (NRWeakSINRReason.isSuddenWeak)
            {
                return true;
            }
            return false;
        }
    }

    public class NRReasonUnknow : NRReasonBase
    {
        private static NRReasonUnknow instance = null;
        public static NRReasonUnknow Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new NRReasonUnknow();
                }
                return instance;
            }
        }
        protected NRReasonUnknow()
        {
            Name = "未知原因";
        }
        public override bool IsValid(Model.TestPoint tp, NRTpManagerBase nRCond, params object[] resvParams)
        {
            return true;
        }
        public override bool Equals(object obj)
        {
            if (obj == null)
            {
                return false;
            }
            return obj.GetType() == this.GetType();
        }

        private const int hash = -999;
        public override int GetHashCode()
        {
            return hash.GetHashCode();
        }
    }

    public class NRReasonWeakCover : NRReasonBase
    {
        public NRReasonWeakCover()
        {
            this.Name = "弱覆盖";
        }

        public float MaxRSRP { get; set; } = -105;
        public override bool IsValid(Model.TestPoint tp, NRTpManagerBase nRCond, params object[] resvParams)
        {
            object value = nRCond.GetSCellRsrp(tp);
            if (value == null)
            {
                return false;
            }
            float rsrp = float.Parse(value.ToString());
            return rsrp <= MaxRSRP && rsrp >= -141;
        }

        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Enable"] = this.Enable;
                param["MaxRSRP"] = this.MaxRSRP;
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("Enable"))
                {
                    this.Enable = (bool)param["Enable"];
                }
                if (param.ContainsKey("MaxRSRP"))
                {
                    this.MaxRSRP = (float)param["MaxRSRP"];
                }
            }
        }
    }
}
