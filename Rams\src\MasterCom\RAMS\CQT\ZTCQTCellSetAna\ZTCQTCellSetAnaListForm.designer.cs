﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTCQTCellSetAnaListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.treeListView = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCityName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAreaCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAreaType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAreaName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnIndoorPercent = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOutdoorPercent = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnUnknownPercent = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnIndoorSample = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOutdoorSample = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnUnknownSample = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportToExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.treeListViewDetail = new BrightIdeasSoftware.TreeListView();
            this.olvColumnCellSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxlevCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxlevMax = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxlevMin = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxlevAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxQualCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxQualMax = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxQualMin = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRxQualAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDistanceMax = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDistanceMin = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDistanceAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.olvColumnAreaCover = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.treeListViewDetail)).BeginInit();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            this.SuspendLayout();
            // 
            // treeListView
            // 
            this.treeListView.AllColumns.Add(this.olvColumnSN);
            this.treeListView.AllColumns.Add(this.olvColumnCityName);
            this.treeListView.AllColumns.Add(this.olvColumnAreaCount);
            this.treeListView.AllColumns.Add(this.olvColumnAreaType);
            this.treeListView.AllColumns.Add(this.olvColumnAreaCover);
            this.treeListView.AllColumns.Add(this.olvColumnAreaName);
            this.treeListView.AllColumns.Add(this.olvColumnIndoorPercent);
            this.treeListView.AllColumns.Add(this.olvColumnOutdoorPercent);
            this.treeListView.AllColumns.Add(this.olvColumnUnknownPercent);
            this.treeListView.AllColumns.Add(this.olvColumnIndoorSample);
            this.treeListView.AllColumns.Add(this.olvColumnOutdoorSample);
            this.treeListView.AllColumns.Add(this.olvColumnUnknownSample);
            this.treeListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnCityName,
            this.olvColumnAreaCount,
            this.olvColumnAreaType,
            this.olvColumnAreaCover,
            this.olvColumnAreaName,
            this.olvColumnIndoorPercent,
            this.olvColumnOutdoorPercent,
            this.olvColumnUnknownPercent,
            this.olvColumnIndoorSample,
            this.olvColumnOutdoorSample,
            this.olvColumnUnknownSample});
            this.treeListView.ContextMenuStrip = this.contextMenuStrip;
            this.treeListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListView.FullRowSelect = true;
            this.treeListView.GridLines = true;
            this.treeListView.Location = new System.Drawing.Point(0, 0);
            this.treeListView.MultiSelect = false;
            this.treeListView.Name = "treeListView";
            this.treeListView.OwnerDraw = true;
            this.treeListView.ShowGroups = false;
            this.treeListView.Size = new System.Drawing.Size(1061, 245);
            this.treeListView.TabIndex = 1;
            this.treeListView.UseCompatibleStateImageBehavior = false;
            this.treeListView.View = System.Windows.Forms.View.Details;
            this.treeListView.VirtualMode = true;
            this.treeListView.MouseClick += new System.Windows.Forms.MouseEventHandler(this.treeListView_MouseClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 105;
            // 
            // olvColumnCityName
            // 
            this.olvColumnCityName.HeaderFont = null;
            this.olvColumnCityName.Text = "地市名称";
            // 
            // olvColumnAreaCount
            // 
            this.olvColumnAreaCount.HeaderFont = null;
            this.olvColumnAreaCount.Text = "地点数量";
            // 
            // olvColumnAreaType
            // 
            this.olvColumnAreaType.HeaderFont = null;
            this.olvColumnAreaType.Text = "地点类型";
            // 
            // olvColumnAreaName
            // 
            this.olvColumnAreaName.HeaderFont = null;
            this.olvColumnAreaName.Text = "地点名称";
            this.olvColumnAreaName.Width = 100;
            // 
            // olvColumnIndoorPercent
            // 
            this.olvColumnIndoorPercent.HeaderFont = null;
            this.olvColumnIndoorPercent.Text = "室分采样点占比";
            this.olvColumnIndoorPercent.Width = 110;
            // 
            // olvColumnOutdoorPercent
            // 
            this.olvColumnOutdoorPercent.HeaderFont = null;
            this.olvColumnOutdoorPercent.Text = "非室分采样点占比";
            this.olvColumnOutdoorPercent.Width = 110;
            // 
            // olvColumnUnknownPercent
            // 
            this.olvColumnUnknownPercent.HeaderFont = null;
            this.olvColumnUnknownPercent.Text = "未知采样点占比";
            this.olvColumnUnknownPercent.Width = 110;
            // 
            // olvColumnIndoorSample
            // 
            this.olvColumnIndoorSample.HeaderFont = null;
            this.olvColumnIndoorSample.Text = "室分采样点数量";
            this.olvColumnIndoorSample.Width = 110;
            // 
            // olvColumnOutdoorSample
            // 
            this.olvColumnOutdoorSample.HeaderFont = null;
            this.olvColumnOutdoorSample.Text = "非室分采样点数量";
            this.olvColumnOutdoorSample.Width = 110;
            // 
            // olvColumnUnknownSample
            // 
            this.olvColumnUnknownSample.HeaderFont = null;
            this.olvColumnUnknownSample.Text = "未知采样点数量";
            this.olvColumnUnknownSample.Width = 110;
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll,
            this.miCollapseAll,
            this.miExportToExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 70);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(129, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCollapseAll
            // 
            this.miCollapseAll.Name = "miCollapseAll";
            this.miCollapseAll.Size = new System.Drawing.Size(129, 22);
            this.miCollapseAll.Text = "全部折叠";
            this.miCollapseAll.Click += new System.EventHandler(this.miCollapseAll_Click);
            // 
            // miExportToExcel
            // 
            this.miExportToExcel.Name = "miExportToExcel";
            this.miExportToExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportToExcel.Text = "导出Excel";
            this.miExportToExcel.Click += new System.EventHandler(this.miExportToExcel_Click);
            // 
            // treeListViewDetail
            // 
            this.treeListViewDetail.AllColumns.Add(this.olvColumnCellSN);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnCellName);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnCellType);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnRxlevCount);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnRxlevMax);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnRxlevMin);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnRxlevAvg);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnRxQualCount);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnRxQualMax);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnRxQualMin);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnRxQualAvg);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnDistanceMax);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnDistanceMin);
            this.treeListViewDetail.AllColumns.Add(this.olvColumnDistanceAvg);
            this.treeListViewDetail.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnCellSN,
            this.olvColumnCellName,
            this.olvColumnCellType,
            this.olvColumnRxlevCount,
            this.olvColumnRxlevMax,
            this.olvColumnRxlevMin,
            this.olvColumnRxlevAvg,
            this.olvColumnRxQualCount,
            this.olvColumnRxQualMax,
            this.olvColumnRxQualMin,
            this.olvColumnRxQualAvg,
            this.olvColumnDistanceMax,
            this.olvColumnDistanceMin,
            this.olvColumnDistanceAvg});
            this.treeListViewDetail.ContextMenuStrip = this.contextMenuStrip;
            this.treeListViewDetail.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListViewDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListViewDetail.FullRowSelect = true;
            this.treeListViewDetail.GridLines = true;
            this.treeListViewDetail.Location = new System.Drawing.Point(0, 0);
            this.treeListViewDetail.MultiSelect = false;
            this.treeListViewDetail.Name = "treeListViewDetail";
            this.treeListViewDetail.OwnerDraw = true;
            this.treeListViewDetail.ShowGroups = false;
            this.treeListViewDetail.Size = new System.Drawing.Size(1061, 297);
            this.treeListViewDetail.TabIndex = 2;
            this.treeListViewDetail.UseCompatibleStateImageBehavior = false;
            this.treeListViewDetail.View = System.Windows.Forms.View.Details;
            this.treeListViewDetail.VirtualMode = true;
            // 
            // olvColumnCellSN
            // 
            this.olvColumnCellSN.HeaderFont = null;
            this.olvColumnCellSN.Text = "序号";
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名称";
            this.olvColumnCellName.Width = 120;
            // 
            // olvColumnCellType
            // 
            this.olvColumnCellType.HeaderFont = null;
            this.olvColumnCellType.Text = "小区类别";
            // 
            // olvColumnRxlevCount
            // 
            this.olvColumnRxlevCount.HeaderFont = null;
            this.olvColumnRxlevCount.Text = "场强采样点数";
            this.olvColumnRxlevCount.Width = 100;
            // 
            // olvColumnRxlevMax
            // 
            this.olvColumnRxlevMax.HeaderFont = null;
            this.olvColumnRxlevMax.Text = "场强最大";
            // 
            // olvColumnRxlevMin
            // 
            this.olvColumnRxlevMin.HeaderFont = null;
            this.olvColumnRxlevMin.Text = "场强最小";
            // 
            // olvColumnRxlevAvg
            // 
            this.olvColumnRxlevAvg.HeaderFont = null;
            this.olvColumnRxlevAvg.Text = "场强平均";
            // 
            // olvColumnRxQualCount
            // 
            this.olvColumnRxQualCount.HeaderFont = null;
            this.olvColumnRxQualCount.Text = "质量采样点数";
            this.olvColumnRxQualCount.Width = 100;
            // 
            // olvColumnRxQualMax
            // 
            this.olvColumnRxQualMax.HeaderFont = null;
            this.olvColumnRxQualMax.Text = "质量最大";
            // 
            // olvColumnRxQualMin
            // 
            this.olvColumnRxQualMin.HeaderFont = null;
            this.olvColumnRxQualMin.Text = "质量最小";
            // 
            // olvColumnRxQualAvg
            // 
            this.olvColumnRxQualAvg.HeaderFont = null;
            this.olvColumnRxQualAvg.Text = "质量平均";
            // 
            // olvColumnDistanceMax
            // 
            this.olvColumnDistanceMax.HeaderFont = null;
            this.olvColumnDistanceMax.Text = "距离最大";
            // 
            // olvColumnDistanceMin
            // 
            this.olvColumnDistanceMin.HeaderFont = null;
            this.olvColumnDistanceMin.Text = "距离最小";
            // 
            // olvColumnDistanceAvg
            // 
            this.olvColumnDistanceAvg.HeaderFont = null;
            this.olvColumnDistanceAvg.Text = "距离平均";
            // 
            // splitContainer1
            // 
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.Location = new System.Drawing.Point(0, 0);
            this.splitContainer1.Name = "splitContainer1";
            this.splitContainer1.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.treeListView);
            this.splitContainer1.Panel1.RightToLeft = System.Windows.Forms.RightToLeft.No;
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.treeListViewDetail);
            this.splitContainer1.Panel2.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.splitContainer1.Size = new System.Drawing.Size(1061, 546);
            this.splitContainer1.SplitterDistance = 245;
            this.splitContainer1.TabIndex = 3;
            // 
            // olvColumnAreaCover
            // 
            this.olvColumnAreaCover.HeaderFont = null;
            this.olvColumnAreaCover.Text = "覆盖类型";
            // 
            // ZTCQTCellSetAnaListForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1061, 546);
            this.Controls.Add(this.splitContainer1);
            this.Name = "ZTCQTCellSetAnaListForm";
            this.Text = "小区集分析结果列表";
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.treeListViewDetail)).EndInit();
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            this.splitContainer1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView treeListView;
        private BrightIdeasSoftware.OLVColumn olvColumnAreaName;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcel;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.TreeListView treeListViewDetail;
        private System.Windows.Forms.SplitContainer splitContainer1;
        private BrightIdeasSoftware.OLVColumn olvColumnCityName;
        private BrightIdeasSoftware.OLVColumn olvColumnAreaCount;
        private BrightIdeasSoftware.OLVColumn olvColumnAreaType;
        private BrightIdeasSoftware.OLVColumn olvColumnIndoorPercent;
        private BrightIdeasSoftware.OLVColumn olvColumnOutdoorPercent;
        private BrightIdeasSoftware.OLVColumn olvColumnUnknownPercent;
        private BrightIdeasSoftware.OLVColumn olvColumnIndoorSample;
        private BrightIdeasSoftware.OLVColumn olvColumnOutdoorSample;
        private BrightIdeasSoftware.OLVColumn olvColumnUnknownSample;
        private BrightIdeasSoftware.OLVColumn olvColumnCellSN;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnCellType;
        private BrightIdeasSoftware.OLVColumn olvColumnRxlevCount;
        private BrightIdeasSoftware.OLVColumn olvColumnRxlevMax;
        private BrightIdeasSoftware.OLVColumn olvColumnRxlevMin;
        private BrightIdeasSoftware.OLVColumn olvColumnRxlevAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnRxQualCount;
        private BrightIdeasSoftware.OLVColumn olvColumnRxQualMax;
        private BrightIdeasSoftware.OLVColumn olvColumnRxQualMin;
        private BrightIdeasSoftware.OLVColumn olvColumnRxQualAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnDistanceMax;
        private BrightIdeasSoftware.OLVColumn olvColumnDistanceMin;
        private BrightIdeasSoftware.OLVColumn olvColumnDistanceAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnAreaCover;
    }
}