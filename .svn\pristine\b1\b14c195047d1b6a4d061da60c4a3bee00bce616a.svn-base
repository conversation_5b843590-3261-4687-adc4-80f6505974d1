﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRCellOfPilotFrequencyPolluteBlock : CellOfPilotFrequencyPolluteBlock
    {
        public NRCell NRCell { get; set; }

        public double Longitude { get; private set; }
        public double Latitude { get; private set; }
        public string CellName { get; private set; }
        public int ARFCN { get; private set; }
        public int PCI { get; private set; }
        public int TAC { get; private set; }
        public long NCI { get; private set; }

        public int sinrCnt { get; set; } = 0;
        private double totalSinr = 0;
        public NRCellOfPilotFrequencyPolluteBlock(int sn, NRCell cell, double rsrp, float? sinr)
        {
            SN = sn;
            NRCell = cell;
            PCCPCH_RSCP = rsrp;
            sampleCount = 1;
            if (sinr != null && -50 <= sinr && sinr <= 50)
            {
                sinrCnt++;
                totalSinr += (float)sinr;
            }

            CellName = NRCell.Name;
            Longitude = NRCell.Longitude;
            Latitude = NRCell.Latitude;
            ARFCN = NRCell.SSBARFCN;
            PCI = NRCell.PCI;
            TAC = NRCell.TAC;
            NCI = NRCell.NCI;
        }

        public void Join(NRCellOfPilotFrequencyPolluteBlock otherBlock)
        {
            if (NRCell != otherBlock.NRCell)
            {
                return;
            }
            PCCPCH_RSCP += otherBlock.PCCPCH_RSCP;
            sampleCount += otherBlock.SampleCount;
            sinrCnt += otherBlock.sinrCnt;
            totalSinr += otherBlock.totalSinr;
        }

        public object SINRAvg
        {
            get
            {
                string ret = "-";
                if (sinrCnt != 0)
                {
                    ret = Math.Round(totalSinr / sinrCnt, 2).ToString();
                }
                return ret;
            }
        }
    }
}
