﻿namespace MasterCom.RAMS.ZTFunc.ZTNRServiceDelay
{
    partial class ServiceDelayForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.bandedGridView = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand1 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand2 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn5 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn6 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn7 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn8 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn9 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl
            // 
            this.gridControl.ContextMenuStrip = this.ctxMenu;
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.Location = new System.Drawing.Point(0, 0);
            this.gridControl.MainView = this.bandedGridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.Size = new System.Drawing.Size(1147, 453);
            this.gridControl.TabIndex = 0;
            this.gridControl.UseEmbeddedNavigator = true;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView});
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplay,
            this.toolStripSeparator1,
            this.miExport2Xls});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(139, 54);
            // 
            // miReplay
            // 
            this.miReplay.Name = "miReplay";
            this.miReplay.Size = new System.Drawing.Size(138, 22);
            this.miReplay.Text = "回放文件...";
            this.miReplay.Click += new System.EventHandler(this.miReplay_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(135, 6);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(138, 22);
            this.miExport2Xls.Text = "导出Excel...";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // bandedGridView
            // 
            this.bandedGridView.BandPanelRowHeight = 4;
            this.bandedGridView.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand1,
            this.gridBand2});
            this.bandedGridView.ColumnPanelRowHeight = 4;
            this.bandedGridView.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn1,
            this.bandedGridColumn2,
            this.bandedGridColumn3,
            this.bandedGridColumn4,
            this.bandedGridColumn5,
            this.bandedGridColumn6,
            this.bandedGridColumn7,
            this.bandedGridColumn8,
            this.bandedGridColumn9});
            this.bandedGridView.GridControl = this.gridControl;
            this.bandedGridView.Name = "bandedGridView";
            this.bandedGridView.OptionsBehavior.Editable = false;
            this.bandedGridView.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView.OptionsView.ShowGroupPanel = false;
            // 
            // gridBand1
            // 
            this.gridBand1.Caption = "Call";
            this.gridBand1.Columns.Add(this.bandedGridColumn1);
            this.gridBand1.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            this.gridBand1.Name = "gridBand1";
            this.gridBand1.Width = 75;
            // 
            // bandedGridColumn1
            // 
            this.bandedGridColumn1.Caption = "序号";
            this.bandedGridColumn1.FieldName = "Sn";
            this.bandedGridColumn1.Name = "bandedGridColumn1";
            this.bandedGridColumn1.RowCount = 4;
            this.bandedGridColumn1.Visible = true;
            // 
            // gridBand2
            // 
            this.gridBand2.Caption = "接入时延分析";
            this.gridBand2.Columns.Add(this.bandedGridColumn2);
            this.gridBand2.Columns.Add(this.bandedGridColumn3);
            this.gridBand2.Columns.Add(this.bandedGridColumn4);
            this.gridBand2.Columns.Add(this.bandedGridColumn5);
            this.gridBand2.Columns.Add(this.bandedGridColumn6);
            this.gridBand2.Columns.Add(this.bandedGridColumn7);
            this.gridBand2.Columns.Add(this.bandedGridColumn8);
            this.gridBand2.Columns.Add(this.bandedGridColumn9);
            this.gridBand2.Name = "gridBand2";
            this.gridBand2.Width = 900;
            // 
            // bandedGridColumn2
            // 
            this.bandedGridColumn2.Caption = "log名称";
            this.bandedGridColumn2.FieldName = "FileName";
            this.bandedGridColumn2.Name = "bandedGridColumn2";
            this.bandedGridColumn2.RowCount = 4;
            this.bandedGridColumn2.Visible = true;
            // 
            // bandedGridColumn3
            // 
            this.bandedGridColumn3.Caption = "接入时延";
            this.bandedGridColumn3.FieldName = "ServiceDelayTime";
            this.bandedGridColumn3.Name = "bandedGridColumn3";
            this.bandedGridColumn3.RowCount = 4;
            this.bandedGridColumn3.Visible = true;
            // 
            // bandedGridColumn4
            // 
            this.bandedGridColumn4.Caption = "接入请求时间";
            this.bandedGridColumn4.DisplayFormat.FormatString = "HH:mm:ss.fff";
            this.bandedGridColumn4.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.bandedGridColumn4.FieldName = "ServiceRequestTime";
            this.bandedGridColumn4.Name = "bandedGridColumn4";
            this.bandedGridColumn4.RowCount = 4;
            this.bandedGridColumn4.Visible = true;
            // 
            // bandedGridColumn5
            // 
            this.bandedGridColumn5.Caption = "接入完成时间";
            this.bandedGridColumn5.DisplayFormat.FormatString = "HH:mm:ss.fff";
            this.bandedGridColumn5.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.bandedGridColumn5.FieldName = "ServiceSuccessTime";
            this.bandedGridColumn5.Name = "bandedGridColumn5";
            this.bandedGridColumn5.RowCount = 4;
            this.bandedGridColumn5.Visible = true;
            // 
            // bandedGridColumn6
            // 
            this.bandedGridColumn6.Caption = "经度";
            this.bandedGridColumn6.FieldName = "Lng";
            this.bandedGridColumn6.Name = "bandedGridColumn6";
            this.bandedGridColumn6.RowCount = 4;
            this.bandedGridColumn6.Visible = true;
            // 
            // bandedGridColumn7
            // 
            this.bandedGridColumn7.Caption = "纬度";
            this.bandedGridColumn7.FieldName = "Lat";
            this.bandedGridColumn7.Name = "bandedGridColumn7";
            this.bandedGridColumn7.RowCount = 4;
            this.bandedGridColumn7.Visible = true;
            // 
            // bandedGridColumn8
            // 
            this.bandedGridColumn8.Caption = "TAC";
            this.bandedGridColumn8.FieldName = "NrTac";
            this.bandedGridColumn8.Name = "bandedGridColumn8";
            this.bandedGridColumn8.RowCount = 4;
            this.bandedGridColumn8.Visible = true;
            // 
            // bandedGridColumn9
            // 
            this.bandedGridColumn9.Caption = "NCI";
            this.bandedGridColumn9.FieldName = "NrNci";
            this.bandedGridColumn9.Name = "bandedGridColumn9";
            this.bandedGridColumn9.RowCount = 4;
            this.bandedGridColumn9.Visible = true;
            
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1147, 453);
            this.Controls.Add(this.gridControl);
            this.Name = "ServiceDelayForm";
            this.Text = "接入时延分析";
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView)).EndInit();
            this.ResumeLayout(false);
        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView;

        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand1;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand2;

        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn1;    // 序号
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn2;    // log名称
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn3;    // 接入时延
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn4;    // ServiceRequestTime
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn5;    // ServiceSuccessTime
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn6;    // Lng
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn7;    // Lat
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn8;    // TAC
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn9;    // NCI


        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miReplay;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
    }
}