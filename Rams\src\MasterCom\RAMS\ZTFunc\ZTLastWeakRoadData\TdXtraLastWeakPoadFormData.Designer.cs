﻿namespace MasterCom.RAMS.ZTFunc.ZTLastWeakRoadData
{
    partial class TdXtraLastWeakPoadFormData
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraCharts.XYDiagram xyDiagram2 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series3 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel4 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.Series series4 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel5 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel6 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.tableLayoutPanel2 = new System.Windows.Forms.TableLayoutPanel();
            this.chartControl1 = new DevExpress.XtraCharts.ChartControl();
            this.gridControl5 = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItemDIYReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemClearFly = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemShowFly = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemToEXCEL = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItemLable = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView9 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn129 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn130 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn131 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn135 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn132 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn136 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn133 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn134 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.bandedGridColumn54 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn55 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn56 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn57 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn58 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn59 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn60 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn61 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn62 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn63 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn64 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn5 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn6 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn7 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn8 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn9 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn10 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn11 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn12 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn13 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn14 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn15 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn16 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn17 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn18 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn19 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn20 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn21 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn22 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn23 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn24 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn25 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn26 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn27 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn28 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn29 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn30 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn31 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn32 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn33 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn34 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn35 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn36 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn37 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn38 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn39 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn40 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn41 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn42 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn43 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn44 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn45 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn46 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn47 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn48 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn49 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn50 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn51 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn52 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn53 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn137 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn39 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn44 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn45 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn46 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn150 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn149 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn148 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn147 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn146 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn145 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn144 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn143 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn142 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn161 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn160 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn159 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn158 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn157 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn156 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn155 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn154 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn153 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn152 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn151 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn170 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn169 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn168 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn167 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn166 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn165 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn164 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn163 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn162 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn141 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn47 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn48 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn49 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn50 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn51 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn109 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn110 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn111 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn112 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn113 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn324 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.xtraTabPage3 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControl2 = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn52 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn53 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn54 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn55 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn56 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn57 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn58 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn59 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn60 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn61 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn62 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn63 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn64 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn65 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn66 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn67 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn68 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn69 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn70 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn71 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn72 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn73 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn74 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn75 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn76 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn77 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn78 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn79 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn80 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn81 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn82 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn83 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn84 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn85 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn86 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn87 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn88 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn89 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn90 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn325 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView4 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.xtraTabPage4 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControl3 = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridView5 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn91 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn92 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn93 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn94 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn95 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn96 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn97 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn98 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn99 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn100 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn101 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn102 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn103 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn104 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn105 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn106 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn107 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn108 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn114 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn115 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn116 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn117 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn118 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn119 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn120 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn121 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn122 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn123 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn124 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn125 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn126 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn127 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn128 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn138 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn139 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn140 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn171 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn172 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn173 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn174 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn175 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn176 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn177 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn178 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn179 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn180 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn181 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn182 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn183 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn184 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn185 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn186 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn187 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn188 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn189 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn190 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn191 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn192 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn193 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn194 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn195 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn196 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn197 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn326 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView6 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.xtraTabPage5 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControl4 = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridView7 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn198 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn199 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn200 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn201 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn202 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn203 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn204 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn205 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn206 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn207 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn208 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn209 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn210 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn211 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn212 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn213 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn214 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn215 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn216 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn217 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn218 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn219 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn220 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn221 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn222 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn223 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn224 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn225 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn226 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn227 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn228 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn229 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn230 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn231 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn232 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn233 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn234 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn235 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn236 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn237 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn238 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn239 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn240 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn241 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn242 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn243 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn244 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn245 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn246 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn247 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn248 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn249 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn250 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn251 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn252 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn253 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn254 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn255 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn256 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn257 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn258 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn259 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn260 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn327 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView8 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.xtraTabPage6 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControl6 = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView5 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridView10 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn261 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn262 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn263 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn264 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn265 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn266 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn267 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn268 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn269 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn270 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn271 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn272 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn273 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn274 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn275 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn276 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn277 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn278 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn279 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn280 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn281 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn282 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn283 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn284 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn285 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn286 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn287 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn288 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn289 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn290 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn291 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn292 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn293 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn294 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn295 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn296 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn297 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn298 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn299 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn300 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn301 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn302 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn303 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn304 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn305 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn306 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn307 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn308 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn309 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn310 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn311 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn312 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn313 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn314 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn315 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn316 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn317 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn318 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn319 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn320 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn321 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn322 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn323 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn328 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView11 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.btnClearFly = new System.Windows.Forms.Button();
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.panel1 = new System.Windows.Forms.Panel();
            this.label1 = new System.Windows.Forms.Label();
            this.btnNext1 = new System.Windows.Forms.Button();
            this.gridBand14 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand1 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand6 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand15 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand16 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand17 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand7 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand8 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand9 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand10 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand11 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand12 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand13 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn65 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn66 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn67 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn68 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn69 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn70 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn71 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn72 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn73 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn74 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn75 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn76 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn77 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn78 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn79 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn80 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn81 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn82 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn83 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn84 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn85 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn86 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn87 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn88 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn89 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn90 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn91 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn92 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn93 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn94 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn95 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn96 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn97 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn98 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn99 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn100 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn101 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn102 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn103 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn104 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn105 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn106 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn107 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn108 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn109 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn110 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn111 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn112 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn113 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn114 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn115 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn116 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn117 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn118 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn119 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn120 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn121 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn122 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn123 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn124 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn125 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn126 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn127 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn128 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand2 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand18 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand19 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand20 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand21 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand22 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand23 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand24 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand25 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand26 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand27 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand28 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand29 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn129 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn130 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn131 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn132 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn133 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn134 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn135 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn136 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn137 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn138 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn139 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn140 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn141 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn142 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn143 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn144 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn145 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn146 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn147 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn148 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn149 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn150 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn151 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn152 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn153 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn154 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn155 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn156 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn157 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn158 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn159 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn160 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn161 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn162 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn163 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn164 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn165 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn166 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn167 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn168 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn169 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn170 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn171 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn172 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn173 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn174 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn175 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn176 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn177 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn178 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn179 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn180 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn181 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn182 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn183 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn184 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn185 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn186 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn187 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn188 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn189 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn190 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn191 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn192 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand3 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand30 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand31 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand32 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand33 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand34 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand35 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand36 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand37 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand38 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand39 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand40 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand41 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn193 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn194 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn195 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn196 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn197 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn198 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn199 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn200 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn201 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn202 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn203 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn204 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn205 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn206 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn207 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn208 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn209 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn210 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn211 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn212 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn213 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn214 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn215 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn216 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn217 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn218 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn219 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn220 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn221 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn222 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn223 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn224 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn225 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn226 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn227 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn228 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn229 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn230 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn231 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn232 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn233 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn234 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn235 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn236 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn237 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn238 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn239 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn240 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn241 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn242 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn243 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn244 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn245 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn246 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn247 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn248 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn249 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn250 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn251 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn252 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn253 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn254 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn255 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn256 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand4 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand42 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand43 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand44 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand45 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand46 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand47 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand48 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand49 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand50 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand51 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand52 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand53 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn257 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn258 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn259 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn260 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn261 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn262 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn263 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn264 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn265 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn266 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn267 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn268 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn269 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn270 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn271 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn272 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn273 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn274 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn275 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn276 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn277 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn278 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn279 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn280 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn281 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn282 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn283 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn284 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn285 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn286 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn287 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn288 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn289 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn290 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn291 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn292 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn293 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn294 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn295 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn296 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn297 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn298 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn299 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn300 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn301 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn302 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn303 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn304 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn305 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn306 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn307 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn308 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn309 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn310 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn311 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn312 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn313 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn314 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn315 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn316 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn317 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn318 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn319 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn320 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand5 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand54 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand55 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand56 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand57 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand58 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand59 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand60 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand61 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand62 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand63 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand64 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand65 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            this.tableLayoutPanel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl5)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView9)).BeginInit();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            this.xtraTabPage3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).BeginInit();
            this.xtraTabPage4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).BeginInit();
            this.xtraTabPage5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView8)).BeginInit();
            this.xtraTabPage6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView11)).BeginInit();
            this.tableLayoutPanel1.SuspendLayout();
            this.panel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(3, 3);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(843, 411);
            this.xtraTabControl1.TabIndex = 0;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2,
            this.xtraTabPage3,
            this.xtraTabPage4,
            this.xtraTabPage5,
            this.xtraTabPage6});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.tableLayoutPanel2);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(836, 381);
            this.xtraTabPage1.Text = "概况";
            // 
            // tableLayoutPanel2
            // 
            this.tableLayoutPanel2.ColumnCount = 1;
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel2.Controls.Add(this.chartControl1, 0, 1);
            this.tableLayoutPanel2.Controls.Add(this.gridControl5, 0, 0);
            this.tableLayoutPanel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel2.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel2.Name = "tableLayoutPanel2";
            this.tableLayoutPanel2.RowCount = 2;
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 150F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel2.Size = new System.Drawing.Size(836, 381);
            this.tableLayoutPanel2.TabIndex = 3;
            // 
            // chartControl1
            // 
            xyDiagram2.AxisX.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram2.AxisX.Range.SideMarginsEnabled = true;
            xyDiagram2.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram2.AxisY.Range.ScrollingRange.SideMarginsEnabled = true;
            xyDiagram2.AxisY.Range.SideMarginsEnabled = true;
            xyDiagram2.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControl1.Diagram = xyDiagram2;
            this.chartControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.chartControl1.Location = new System.Drawing.Point(3, 153);
            this.chartControl1.Name = "chartControl1";
            sideBySideBarSeriesLabel4.LineVisible = true;
            series3.Label = sideBySideBarSeriesLabel4;
            series3.Name = "Series 1";
            sideBySideBarSeriesLabel5.LineVisible = true;
            series4.Label = sideBySideBarSeriesLabel5;
            series4.Name = "Series 2";
            this.chartControl1.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series3,
        series4};
            sideBySideBarSeriesLabel6.LineVisible = true;
            this.chartControl1.SeriesTemplate.Label = sideBySideBarSeriesLabel6;
            this.chartControl1.Size = new System.Drawing.Size(830, 225);
            this.chartControl1.TabIndex = 0;
            // 
            // gridControl5
            // 
            this.gridControl5.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl5.Location = new System.Drawing.Point(3, 3);
            this.gridControl5.MainView = this.gridView9;
            this.gridControl5.Name = "gridControl5";
            this.gridControl5.Size = new System.Drawing.Size(830, 144);
            this.gridControl5.TabIndex = 0;
            this.gridControl5.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView9});
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItemDIYReplay,
            this.ToolStripMenuItemClearFly,
            this.ToolStripMenuItemShowFly,
            this.ToolStripMenuItemToEXCEL,
            this.ToolStripMenuItemLable});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(135, 114);
            // 
            // ToolStripMenuItemDIYReplay
            // 
            this.ToolStripMenuItemDIYReplay.Name = "ToolStripMenuItemDIYReplay";
            this.ToolStripMenuItemDIYReplay.Size = new System.Drawing.Size(134, 22);
            this.ToolStripMenuItemDIYReplay.Text = "回放";
            this.ToolStripMenuItemDIYReplay.Click += new System.EventHandler(this.ToolStripMenuItemDIYReplay_Click);
            // 
            // ToolStripMenuItemClearFly
            // 
            this.ToolStripMenuItemClearFly.Name = "ToolStripMenuItemClearFly";
            this.ToolStripMenuItemClearFly.Size = new System.Drawing.Size(134, 22);
            this.ToolStripMenuItemClearFly.Text = "清除飞线";
            this.ToolStripMenuItemClearFly.Click += new System.EventHandler(this.ToolStripMenuItemClearFly_Click);
            // 
            // ToolStripMenuItemShowFly
            // 
            this.ToolStripMenuItemShowFly.Name = "ToolStripMenuItemShowFly";
            this.ToolStripMenuItemShowFly.Size = new System.Drawing.Size(134, 22);
            this.ToolStripMenuItemShowFly.Text = "显示飞线";
            this.ToolStripMenuItemShowFly.Click += new System.EventHandler(this.ToolStripMenuItemShowFly_Click);
            // 
            // ToolStripMenuItemToEXCEL
            // 
            this.ToolStripMenuItemToEXCEL.Name = "ToolStripMenuItemToEXCEL";
            this.ToolStripMenuItemToEXCEL.Size = new System.Drawing.Size(134, 22);
            this.ToolStripMenuItemToEXCEL.Text = "导出EXCEL";
            this.ToolStripMenuItemToEXCEL.Click += new System.EventHandler(this.ToolStripMenuItemToEXCEL_Click);
            // 
            // ToolStripMenuItemLable
            // 
            this.ToolStripMenuItemLable.Name = "ToolStripMenuItemLable";
            this.ToolStripMenuItemLable.Size = new System.Drawing.Size(134, 22);
            this.ToolStripMenuItemLable.Text = "显示标签";
            this.ToolStripMenuItemLable.Click += new System.EventHandler(this.ToolStripMenuItemLable_Click);
            // 
            // gridView9
            // 
            this.gridView9.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn129,
            this.gridColumn130,
            this.gridColumn131,
            this.gridColumn135,
            this.gridColumn132,
            this.gridColumn136,
            this.gridColumn133,
            this.gridColumn134});
            this.gridView9.GridControl = this.gridControl5;
            this.gridView9.Name = "gridView9";
            this.gridView9.OptionsBehavior.Editable = false;
            this.gridView9.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView9.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gridView9.OptionsView.ShowGroupPanel = false;
            this.gridView9.EndSorting += new System.EventHandler(this.gridView9_EndSorting);
            this.gridView9.RowCellClick += new DevExpress.XtraGrid.Views.Grid.RowCellClickEventHandler(this.gridView9_RowCellClick);
            // 
            // gridColumn129
            // 
            this.gridColumn129.Caption = "类型";
            this.gridColumn129.FieldName = "StrType";
            this.gridColumn129.Name = "gridColumn129";
            this.gridColumn129.Visible = true;
            this.gridColumn129.VisibleIndex = 0;
            this.gridColumn129.Width = 59;
            // 
            // gridColumn130
            // 
            this.gridColumn130.Caption = "数量";
            this.gridColumn130.FieldName = "INum";
            this.gridColumn130.Name = "gridColumn130";
            this.gridColumn130.Visible = true;
            this.gridColumn130.VisibleIndex = 1;
            this.gridColumn130.Width = 39;
            // 
            // gridColumn131
            // 
            this.gridColumn131.Caption = "时间(s)";
            this.gridColumn131.FieldName = "IDuration";
            this.gridColumn131.Name = "gridColumn131";
            this.gridColumn131.Visible = true;
            this.gridColumn131.VisibleIndex = 2;
            this.gridColumn131.Width = 49;
            // 
            // gridColumn135
            // 
            this.gridColumn135.Caption = "总里程(m)";
            this.gridColumn135.FieldName = "IAllDistance";
            this.gridColumn135.Name = "gridColumn135";
            this.gridColumn135.Visible = true;
            this.gridColumn135.VisibleIndex = 3;
            this.gridColumn135.Width = 66;
            // 
            // gridColumn132
            // 
            this.gridColumn132.Caption = "问题里程(m)";
            this.gridColumn132.FieldName = "IDistance";
            this.gridColumn132.Name = "gridColumn132";
            this.gridColumn132.Visible = true;
            this.gridColumn132.VisibleIndex = 4;
            this.gridColumn132.Width = 54;
            // 
            // gridColumn136
            // 
            this.gridColumn136.Caption = "差道路占比(%)";
            this.gridColumn136.DisplayFormat.FormatString = "00.00%";
            this.gridColumn136.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn136.FieldName = "FRate";
            this.gridColumn136.GroupFormat.FormatString = "00.00%";
            this.gridColumn136.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn136.Name = "gridColumn136";
            this.gridColumn136.Visible = true;
            this.gridColumn136.VisibleIndex = 5;
            this.gridColumn136.Width = 92;
            // 
            // gridColumn133
            // 
            this.gridColumn133.Caption = "小区数量";
            this.gridColumn133.FieldName = "ICellNum";
            this.gridColumn133.Name = "gridColumn133";
            this.gridColumn133.Visible = true;
            this.gridColumn133.VisibleIndex = 6;
            this.gridColumn133.Width = 65;
            // 
            // gridColumn134
            // 
            this.gridColumn134.Caption = "采样点";
            this.gridColumn134.FieldName = "ISampleId";
            this.gridColumn134.Name = "gridColumn134";
            this.gridColumn134.Visible = true;
            this.gridColumn134.VisibleIndex = 7;
            this.gridColumn134.Width = 62;
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.gridControl1);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(836, 381);
            this.xtraTabPage2.Text = "下载速率低";
            // 
            // gridControl1
            // 
            this.gridControl1.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl1.Location = new System.Drawing.Point(0, 0);
            this.gridControl1.MainView = this.bandedGridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.Size = new System.Drawing.Size(836, 381);
            this.gridControl1.TabIndex = 0;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView1,
            this.gridView1,
            this.gridView2});
            this.gridControl1.DoubleClick += new System.EventHandler(this.gridControl1_DoubleClick);
            // 
            // bandedGridView1
            // 
            this.bandedGridView1.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand14,
            this.gridBand1,
            this.gridBand6,
            this.gridBand15,
            this.gridBand16,
            this.gridBand17,
            this.gridBand7,
            this.gridBand8,
            this.gridBand9,
            this.gridBand10,
            this.gridBand11,
            this.gridBand12,
            this.gridBand13});
            this.bandedGridView1.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn1,
            this.bandedGridColumn2,
            this.bandedGridColumn3,
            this.bandedGridColumn4,
            this.bandedGridColumn5,
            this.bandedGridColumn6,
            this.bandedGridColumn7,
            this.bandedGridColumn8,
            this.bandedGridColumn9,
            this.bandedGridColumn10,
            this.bandedGridColumn11,
            this.bandedGridColumn12,
            this.bandedGridColumn13,
            this.bandedGridColumn14,
            this.bandedGridColumn15,
            this.bandedGridColumn16,
            this.bandedGridColumn17,
            this.bandedGridColumn18,
            this.bandedGridColumn19,
            this.bandedGridColumn20,
            this.bandedGridColumn21,
            this.bandedGridColumn22,
            this.bandedGridColumn23,
            this.bandedGridColumn24,
            this.bandedGridColumn25,
            this.bandedGridColumn26,
            this.bandedGridColumn27,
            this.bandedGridColumn28,
            this.bandedGridColumn29,
            this.bandedGridColumn30,
            this.bandedGridColumn31,
            this.bandedGridColumn32,
            this.bandedGridColumn33,
            this.bandedGridColumn34,
            this.bandedGridColumn35,
            this.bandedGridColumn36,
            this.bandedGridColumn37,
            this.bandedGridColumn38,
            this.bandedGridColumn39,
            this.bandedGridColumn40,
            this.bandedGridColumn41,
            this.bandedGridColumn42,
            this.bandedGridColumn43,
            this.bandedGridColumn44,
            this.bandedGridColumn45,
            this.bandedGridColumn46,
            this.bandedGridColumn47,
            this.bandedGridColumn48,
            this.bandedGridColumn49,
            this.bandedGridColumn50,
            this.bandedGridColumn51,
            this.bandedGridColumn52,
            this.bandedGridColumn53,
            this.bandedGridColumn54,
            this.bandedGridColumn55,
            this.bandedGridColumn56,
            this.bandedGridColumn57,
            this.bandedGridColumn58,
            this.bandedGridColumn59,
            this.bandedGridColumn60,
            this.bandedGridColumn61,
            this.bandedGridColumn62,
            this.bandedGridColumn63,
            this.bandedGridColumn64});
            this.bandedGridView1.GridControl = this.gridControl1;
            this.bandedGridView1.Name = "bandedGridView1";
            this.bandedGridView1.OptionsBehavior.Editable = false;
            this.bandedGridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.bandedGridView1.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView1.OptionsView.ShowGroupPanel = false;
            // 
            // bandedGridColumn54
            // 
            this.bandedGridColumn54.Caption = "TOP1小区";
            this.bandedGridColumn54.CustomizationCaption = "TOP1小区";
            this.bandedGridColumn54.FieldName = "Cell1";
            this.bandedGridColumn54.Name = "bandedGridColumn54";
            this.bandedGridColumn54.Visible = true;
            this.bandedGridColumn54.Width = 85;
            // 
            // bandedGridColumn55
            // 
            this.bandedGridColumn55.Caption = "TOP2小区";
            this.bandedGridColumn55.CustomizationCaption = "TOP2小区";
            this.bandedGridColumn55.FieldName = "Cell2";
            this.bandedGridColumn55.Name = "bandedGridColumn55";
            this.bandedGridColumn55.Visible = true;
            this.bandedGridColumn55.Width = 85;
            // 
            // bandedGridColumn56
            // 
            this.bandedGridColumn56.Caption = "TOP3小区";
            this.bandedGridColumn56.CustomizationCaption = "TOP3小区";
            this.bandedGridColumn56.FieldName = "Cell3";
            this.bandedGridColumn56.Name = "bandedGridColumn56";
            this.bandedGridColumn56.Visible = true;
            this.bandedGridColumn56.Width = 85;
            // 
            // bandedGridColumn57
            // 
            this.bandedGridColumn57.Caption = "所属网格";
            this.bandedGridColumn57.CustomizationCaption = "所属网格";
            this.bandedGridColumn57.FieldName = "Strgrid";
            this.bandedGridColumn57.Name = "bandedGridColumn57";
            this.bandedGridColumn57.Visible = true;
            // 
            // bandedGridColumn58
            // 
            this.bandedGridColumn58.Caption = "所属道路";
            this.bandedGridColumn58.CustomizationCaption = "所属道路";
            this.bandedGridColumn58.FieldName = "Strroad";
            this.bandedGridColumn58.Name = "bandedGridColumn58";
            this.bandedGridColumn58.Visible = true;
            // 
            // bandedGridColumn59
            // 
            this.bandedGridColumn59.Caption = "经度";
            this.bandedGridColumn59.CustomizationCaption = "经度";
            this.bandedGridColumn59.FieldName = "Imlongitude";
            this.bandedGridColumn59.Name = "bandedGridColumn59";
            this.bandedGridColumn59.Visible = true;
            // 
            // bandedGridColumn60
            // 
            this.bandedGridColumn60.Caption = "纬度";
            this.bandedGridColumn60.CustomizationCaption = "纬度";
            this.bandedGridColumn60.FieldName = "Imlatitude";
            this.bandedGridColumn60.Name = "bandedGridColumn60";
            this.bandedGridColumn60.Visible = true;
            // 
            // bandedGridColumn1
            // 
            this.bandedGridColumn1.Caption = "距离(米)";
            this.bandedGridColumn1.CustomizationCaption = "距离(米)";
            this.bandedGridColumn1.FieldName = "Idistance";
            this.bandedGridColumn1.Name = "bandedGridColumn1";
            this.bandedGridColumn1.Visible = true;
            // 
            // bandedGridColumn2
            // 
            this.bandedGridColumn2.Caption = "时长(秒)";
            this.bandedGridColumn2.CustomizationCaption = "时长(秒)";
            this.bandedGridColumn2.FieldName = "Iduration";
            this.bandedGridColumn2.Name = "bandedGridColumn2";
            this.bandedGridColumn2.Visible = true;
            // 
            // bandedGridColumn61
            // 
            this.bandedGridColumn61.Caption = "ifileid";
            this.bandedGridColumn61.CustomizationCaption = "ifileid";
            this.bandedGridColumn61.FieldName = "Ifileid";
            this.bandedGridColumn61.Name = "bandedGridColumn61";
            // 
            // bandedGridColumn62
            // 
            this.bandedGridColumn62.Caption = "istime";
            this.bandedGridColumn62.CustomizationCaption = "istime";
            this.bandedGridColumn62.FieldName = "Istime";
            this.bandedGridColumn62.Name = "bandedGridColumn62";
            // 
            // bandedGridColumn63
            // 
            this.bandedGridColumn63.Caption = "ietime";
            this.bandedGridColumn63.CustomizationCaption = "ietime";
            this.bandedGridColumn63.FieldName = "Ietime";
            this.bandedGridColumn63.Name = "bandedGridColumn63";
            // 
            // bandedGridColumn64
            // 
            this.bandedGridColumn64.Caption = "gridColumn324";
            this.bandedGridColumn64.CustomizationCaption = "gridColumn324";
            this.bandedGridColumn64.FieldName = "Iid";
            this.bandedGridColumn64.Name = "bandedGridColumn64";
            // 
            // bandedGridColumn3
            // 
            this.bandedGridColumn3.Caption = "0";
            this.bandedGridColumn3.CustomizationCaption = "下载速率0";
            this.bandedGridColumn3.FieldName = "APP_Speed0";
            this.bandedGridColumn3.Name = "bandedGridColumn3";
            this.bandedGridColumn3.Visible = true;
            // 
            // bandedGridColumn4
            // 
            this.bandedGridColumn4.Caption = "[0,500]";
            this.bandedGridColumn4.CustomizationCaption = "下载速率[0,500]";
            this.bandedGridColumn4.FieldName = "APP_Speed0_500";
            this.bandedGridColumn4.Name = "bandedGridColumn4";
            this.bandedGridColumn4.Visible = true;
            // 
            // bandedGridColumn5
            // 
            this.bandedGridColumn5.Caption = "[500,800]";
            this.bandedGridColumn5.CustomizationCaption = "下载速率[500,800]";
            this.bandedGridColumn5.FieldName = "APP_Speed500_800";
            this.bandedGridColumn5.Name = "bandedGridColumn5";
            this.bandedGridColumn5.Visible = true;
            // 
            // bandedGridColumn6
            // 
            this.bandedGridColumn6.Caption = "[800,900]";
            this.bandedGridColumn6.CustomizationCaption = "下载速率[800,900]";
            this.bandedGridColumn6.FieldName = "APP_Speed800_900";
            this.bandedGridColumn6.Name = "bandedGridColumn6";
            this.bandedGridColumn6.Visible = true;
            // 
            // bandedGridColumn7
            // 
            this.bandedGridColumn7.Caption = "[900,1100]";
            this.bandedGridColumn7.CustomizationCaption = "下载速率[900,1100]";
            this.bandedGridColumn7.FieldName = "APP_Speed900_1100";
            this.bandedGridColumn7.Name = "bandedGridColumn7";
            this.bandedGridColumn7.Visible = true;
            // 
            // bandedGridColumn8
            // 
            this.bandedGridColumn8.Caption = "1100";
            this.bandedGridColumn8.CustomizationCaption = "下载速率1100";
            this.bandedGridColumn8.FieldName = "APP_Speed1100";
            this.bandedGridColumn8.Name = "bandedGridColumn8";
            this.bandedGridColumn8.Visible = true;
            // 
            // bandedGridColumn9
            // 
            this.bandedGridColumn9.Caption = "均值";
            this.bandedGridColumn9.CustomizationCaption = "TD电平均值";
            this.bandedGridColumn9.FieldName = "Rscpmean";
            this.bandedGridColumn9.Name = "bandedGridColumn9";
            this.bandedGridColumn9.Visible = true;
            // 
            // bandedGridColumn10
            // 
            this.bandedGridColumn10.Caption = "小于75";
            this.bandedGridColumn10.CustomizationCaption = "TD电平小于75";
            this.bandedGridColumn10.FieldName = "Rscp75";
            this.bandedGridColumn10.Name = "bandedGridColumn10";
            this.bandedGridColumn10.Visible = true;
            // 
            // bandedGridColumn11
            // 
            this.bandedGridColumn11.Caption = "[76,80]";
            this.bandedGridColumn11.CustomizationCaption = "TD电平[76,80]";
            this.bandedGridColumn11.FieldName = "Rscp76_80";
            this.bandedGridColumn11.Name = "bandedGridColumn11";
            this.bandedGridColumn11.Visible = true;
            // 
            // bandedGridColumn12
            // 
            this.bandedGridColumn12.Caption = "[81,85]";
            this.bandedGridColumn12.CustomizationCaption = "TD电平[81,85]";
            this.bandedGridColumn12.FieldName = "Rscp81_85";
            this.bandedGridColumn12.Name = "bandedGridColumn12";
            this.bandedGridColumn12.Visible = true;
            // 
            // bandedGridColumn13
            // 
            this.bandedGridColumn13.Caption = "[86,90]";
            this.bandedGridColumn13.CustomizationCaption = "TD电平[86,90]";
            this.bandedGridColumn13.FieldName = "Rscp86_90";
            this.bandedGridColumn13.Name = "bandedGridColumn13";
            this.bandedGridColumn13.Visible = true;
            // 
            // bandedGridColumn14
            // 
            this.bandedGridColumn14.Caption = "[91,94]";
            this.bandedGridColumn14.CustomizationCaption = "TD电平[91,94]";
            this.bandedGridColumn14.FieldName = "Rscp91_94";
            this.bandedGridColumn14.Name = "bandedGridColumn14";
            this.bandedGridColumn14.Visible = true;
            // 
            // bandedGridColumn15
            // 
            this.bandedGridColumn15.Caption = "大于94";
            this.bandedGridColumn15.CustomizationCaption = "TD电平大于94";
            this.bandedGridColumn15.FieldName = "Rscp94";
            this.bandedGridColumn15.Name = "bandedGridColumn15";
            this.bandedGridColumn15.Visible = true;
            // 
            // bandedGridColumn16
            // 
            this.bandedGridColumn16.Caption = "均值";
            this.bandedGridColumn16.CustomizationCaption = "GSM电平均值";
            this.bandedGridColumn16.FieldName = "Rxlmean";
            this.bandedGridColumn16.Name = "bandedGridColumn16";
            this.bandedGridColumn16.Visible = true;
            // 
            // bandedGridColumn17
            // 
            this.bandedGridColumn17.Caption = "小于75";
            this.bandedGridColumn17.CustomizationCaption = "GSM电平小于75";
            this.bandedGridColumn17.FieldName = "Rxl75";
            this.bandedGridColumn17.Name = "bandedGridColumn17";
            this.bandedGridColumn17.Visible = true;
            // 
            // bandedGridColumn18
            // 
            this.bandedGridColumn18.Caption = "[76,80]";
            this.bandedGridColumn18.CustomizationCaption = "GSM电平[76,80]";
            this.bandedGridColumn18.FieldName = "Rxl76_80";
            this.bandedGridColumn18.Name = "bandedGridColumn18";
            this.bandedGridColumn18.Visible = true;
            // 
            // bandedGridColumn19
            // 
            this.bandedGridColumn19.Caption = "[81,85]";
            this.bandedGridColumn19.CustomizationCaption = "GSM电平[81,85]";
            this.bandedGridColumn19.FieldName = "Rxl81_85";
            this.bandedGridColumn19.Name = "bandedGridColumn19";
            this.bandedGridColumn19.Visible = true;
            // 
            // bandedGridColumn20
            // 
            this.bandedGridColumn20.Caption = "[86,90]";
            this.bandedGridColumn20.CustomizationCaption = "GSM电平[86,90]";
            this.bandedGridColumn20.FieldName = "Rxl86_90";
            this.bandedGridColumn20.Name = "bandedGridColumn20";
            this.bandedGridColumn20.Visible = true;
            // 
            // bandedGridColumn21
            // 
            this.bandedGridColumn21.Caption = "[91,94]";
            this.bandedGridColumn21.CustomizationCaption = "GSM电平[91,94]";
            this.bandedGridColumn21.FieldName = "Rxl91_94";
            this.bandedGridColumn21.Name = "bandedGridColumn21";
            this.bandedGridColumn21.Visible = true;
            // 
            // bandedGridColumn22
            // 
            this.bandedGridColumn22.Caption = "大于94";
            this.bandedGridColumn22.CustomizationCaption = "GSM电平大于94";
            this.bandedGridColumn22.FieldName = "Rxl94";
            this.bandedGridColumn22.Name = "bandedGridColumn22";
            this.bandedGridColumn22.Visible = true;
            // 
            // bandedGridColumn23
            // 
            this.bandedGridColumn23.Caption = "均值";
            this.bandedGridColumn23.CustomizationCaption = "DPCH C/I均值";
            this.bandedGridColumn23.FieldName = "Dc2imean";
            this.bandedGridColumn23.Name = "bandedGridColumn23";
            this.bandedGridColumn23.Visible = true;
            // 
            // bandedGridColumn24
            // 
            this.bandedGridColumn24.Caption = "小于-10";
            this.bandedGridColumn24.CustomizationCaption = "DPCH C/I小于-10";
            this.bandedGridColumn24.FieldName = "Dc2iF10";
            this.bandedGridColumn24.Name = "bandedGridColumn24";
            this.bandedGridColumn24.Visible = true;
            // 
            // bandedGridColumn25
            // 
            this.bandedGridColumn25.Caption = "[-10,-3]";
            this.bandedGridColumn25.CustomizationCaption = "DPCH C/I[-10,-3]";
            this.bandedGridColumn25.FieldName = "Dc2iF10TF3";
            this.bandedGridColumn25.Name = "bandedGridColumn25";
            this.bandedGridColumn25.Visible = true;
            // 
            // bandedGridColumn26
            // 
            this.bandedGridColumn26.Caption = "[-3,15]";
            this.bandedGridColumn26.CustomizationCaption = "DPCH C/I[-3,15]";
            this.bandedGridColumn26.FieldName = "Dc2iF3T15";
            this.bandedGridColumn26.Name = "bandedGridColumn26";
            this.bandedGridColumn26.Visible = true;
            // 
            // bandedGridColumn27
            // 
            this.bandedGridColumn27.Caption = "大于15";
            this.bandedGridColumn27.CustomizationCaption = "DPCH C/I大于15";
            this.bandedGridColumn27.FieldName = "Dc2i15";
            this.bandedGridColumn27.Name = "bandedGridColumn27";
            this.bandedGridColumn27.Visible = true;
            // 
            // bandedGridColumn28
            // 
            this.bandedGridColumn28.Caption = "均值";
            this.bandedGridColumn28.CustomizationCaption = "TxPower均值";
            this.bandedGridColumn28.FieldName = "Txpowermean";
            this.bandedGridColumn28.Name = "bandedGridColumn28";
            this.bandedGridColumn28.Visible = true;
            // 
            // bandedGridColumn29
            // 
            this.bandedGridColumn29.Caption = "小于-20";
            this.bandedGridColumn29.CustomizationCaption = "TxPower小于-20";
            this.bandedGridColumn29.FieldName = "TxpowerF20";
            this.bandedGridColumn29.Name = "bandedGridColumn29";
            this.bandedGridColumn29.Visible = true;
            // 
            // bandedGridColumn30
            // 
            this.bandedGridColumn30.Caption = "[-20,0]";
            this.bandedGridColumn30.CustomizationCaption = "TxPower[-20,0]";
            this.bandedGridColumn30.FieldName = "TxpowerF20T0";
            this.bandedGridColumn30.Name = "bandedGridColumn30";
            this.bandedGridColumn30.Visible = true;
            // 
            // bandedGridColumn31
            // 
            this.bandedGridColumn31.Caption = "[0,15]";
            this.bandedGridColumn31.CustomizationCaption = "TxPower[0,15]";
            this.bandedGridColumn31.FieldName = "Txpower0T15";
            this.bandedGridColumn31.Name = "bandedGridColumn31";
            this.bandedGridColumn31.Visible = true;
            // 
            // bandedGridColumn32
            // 
            this.bandedGridColumn32.Caption = "大于15";
            this.bandedGridColumn32.CustomizationCaption = "TxPower大于15";
            this.bandedGridColumn32.FieldName = "Txpower15";
            this.bandedGridColumn32.Name = "bandedGridColumn32";
            this.bandedGridColumn32.Visible = true;
            // 
            // bandedGridColumn33
            // 
            this.bandedGridColumn33.Caption = "成功次数";
            this.bandedGridColumn33.CustomizationCaption = "T至G切换成功次数";
            this.bandedGridColumn33.FieldName = "HO_SUCC_T2G";
            this.bandedGridColumn33.Name = "bandedGridColumn33";
            this.bandedGridColumn33.Visible = true;
            // 
            // bandedGridColumn34
            // 
            this.bandedGridColumn34.Caption = "失败次数";
            this.bandedGridColumn34.CustomizationCaption = "T至G切换失败次数";
            this.bandedGridColumn34.FieldName = "HO_FAIL_T2G";
            this.bandedGridColumn34.Name = "bandedGridColumn34";
            this.bandedGridColumn34.Visible = true;
            // 
            // bandedGridColumn35
            // 
            this.bandedGridColumn35.Caption = "时延";
            this.bandedGridColumn35.CustomizationCaption = "T至G切换时延";
            this.bandedGridColumn35.FieldName = "HO_Time_T2G";
            this.bandedGridColumn35.Name = "bandedGridColumn35";
            this.bandedGridColumn35.Visible = true;
            // 
            // bandedGridColumn36
            // 
            this.bandedGridColumn36.Caption = "成功次数";
            this.bandedGridColumn36.CustomizationCaption = "T网硬切换成功次数";
            this.bandedGridColumn36.FieldName = "HO_SUCC_T2T";
            this.bandedGridColumn36.Name = "bandedGridColumn36";
            this.bandedGridColumn36.Visible = true;
            // 
            // bandedGridColumn37
            // 
            this.bandedGridColumn37.Caption = "失败次数";
            this.bandedGridColumn37.CustomizationCaption = "T网硬切换失败次数";
            this.bandedGridColumn37.FieldName = "HO_FAIL_T2T";
            this.bandedGridColumn37.Name = "bandedGridColumn37";
            this.bandedGridColumn37.Visible = true;
            // 
            // bandedGridColumn38
            // 
            this.bandedGridColumn38.Caption = "时延";
            this.bandedGridColumn38.CustomizationCaption = "T网硬切换时延";
            this.bandedGridColumn38.FieldName = "HO_Time_T2T";
            this.bandedGridColumn38.Name = "bandedGridColumn38";
            this.bandedGridColumn38.Visible = true;
            // 
            // bandedGridColumn39
            // 
            this.bandedGridColumn39.Caption = "成功次数";
            this.bandedGridColumn39.CustomizationCaption = "T网接力切换成功次数";
            this.bandedGridColumn39.FieldName = "HO_SUCC_Baton";
            this.bandedGridColumn39.Name = "bandedGridColumn39";
            this.bandedGridColumn39.Visible = true;
            // 
            // bandedGridColumn40
            // 
            this.bandedGridColumn40.Caption = "失败次数";
            this.bandedGridColumn40.CustomizationCaption = "T网接力切换失败次数";
            this.bandedGridColumn40.FieldName = "HO_FAIL_Baton";
            this.bandedGridColumn40.Name = "bandedGridColumn40";
            this.bandedGridColumn40.Visible = true;
            // 
            // bandedGridColumn41
            // 
            this.bandedGridColumn41.Caption = "时延";
            this.bandedGridColumn41.CustomizationCaption = "T网接力切换时延";
            this.bandedGridColumn41.FieldName = "HO_Time_Baton";
            this.bandedGridColumn41.Name = "bandedGridColumn41";
            this.bandedGridColumn41.Visible = true;
            // 
            // bandedGridColumn42
            // 
            this.bandedGridColumn42.Caption = "成功次数";
            this.bandedGridColumn42.CustomizationCaption = "T网重选成功次数";
            this.bandedGridColumn42.FieldName = "CR_SUCC_T2T";
            this.bandedGridColumn42.Name = "bandedGridColumn42";
            this.bandedGridColumn42.Visible = true;
            // 
            // bandedGridColumn43
            // 
            this.bandedGridColumn43.Caption = "失败次数";
            this.bandedGridColumn43.CustomizationCaption = "T网重选失败次数";
            this.bandedGridColumn43.FieldName = "CR_FAIL_T2T";
            this.bandedGridColumn43.Name = "bandedGridColumn43";
            this.bandedGridColumn43.Visible = true;
            // 
            // bandedGridColumn44
            // 
            this.bandedGridColumn44.Caption = "成功次数";
            this.bandedGridColumn44.CustomizationCaption = "T至G重选成功次数";
            this.bandedGridColumn44.FieldName = "CR_SUCC_T2G";
            this.bandedGridColumn44.Name = "bandedGridColumn44";
            this.bandedGridColumn44.Visible = true;
            // 
            // bandedGridColumn45
            // 
            this.bandedGridColumn45.Caption = "失败次数";
            this.bandedGridColumn45.CustomizationCaption = "T至G重选失败次数";
            this.bandedGridColumn45.FieldName = "CR_FAIL_T2G";
            this.bandedGridColumn45.Name = "bandedGridColumn45";
            this.bandedGridColumn45.Visible = true;
            // 
            // bandedGridColumn46
            // 
            this.bandedGridColumn46.Caption = "成功次数";
            this.bandedGridColumn46.CustomizationCaption = "G至T重选成功次数";
            this.bandedGridColumn46.FieldName = "CR_SUCC_G2T";
            this.bandedGridColumn46.Name = "bandedGridColumn46";
            this.bandedGridColumn46.Visible = true;
            // 
            // bandedGridColumn47
            // 
            this.bandedGridColumn47.Caption = "失败次数";
            this.bandedGridColumn47.CustomizationCaption = "G至T重选失败次数";
            this.bandedGridColumn47.FieldName = "CR_FAIL_G2T";
            this.bandedGridColumn47.Name = "bandedGridColumn47";
            this.bandedGridColumn47.Visible = true;
            // 
            // bandedGridColumn48
            // 
            this.bandedGridColumn48.Caption = "Hsdpa下载量占比";
            this.bandedGridColumn48.CustomizationCaption = "Hsdpa下载量占比";
            this.bandedGridColumn48.FieldName = "HSDPA_Size";
            this.bandedGridColumn48.Name = "bandedGridColumn48";
            this.bandedGridColumn48.Visible = true;
            // 
            // bandedGridColumn49
            // 
            this.bandedGridColumn49.Caption = "Hsdpa时长占比";
            this.bandedGridColumn49.CustomizationCaption = "Hsdpa时长占比";
            this.bandedGridColumn49.FieldName = "HSDPA_Time";
            this.bandedGridColumn49.Name = "bandedGridColumn49";
            this.bandedGridColumn49.Visible = true;
            // 
            // bandedGridColumn50
            // 
            this.bandedGridColumn50.Caption = "R4下载量占比";
            this.bandedGridColumn50.CustomizationCaption = "R4下载量占比";
            this.bandedGridColumn50.FieldName = "R4_Size";
            this.bandedGridColumn50.Name = "bandedGridColumn50";
            this.bandedGridColumn50.Visible = true;
            // 
            // bandedGridColumn51
            // 
            this.bandedGridColumn51.Caption = "R4时长占比";
            this.bandedGridColumn51.CustomizationCaption = "R4时长占比";
            this.bandedGridColumn51.FieldName = "R4_Time";
            this.bandedGridColumn51.Name = "bandedGridColumn51";
            this.bandedGridColumn51.Visible = true;
            // 
            // bandedGridColumn52
            // 
            this.bandedGridColumn52.Caption = "Gsm下载量占比";
            this.bandedGridColumn52.CustomizationCaption = "Gsm下载量占比";
            this.bandedGridColumn52.FieldName = "EDGE_Size";
            this.bandedGridColumn52.Name = "bandedGridColumn52";
            this.bandedGridColumn52.Visible = true;
            // 
            // bandedGridColumn53
            // 
            this.bandedGridColumn53.Caption = "Gsm时长占比";
            this.bandedGridColumn53.CustomizationCaption = "Gsm时长占比";
            this.bandedGridColumn53.FieldName = "EDGE_Time";
            this.bandedGridColumn53.Name = "bandedGridColumn53";
            this.bandedGridColumn53.Visible = true;
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn33,
            this.gridColumn34,
            this.gridColumn35,
            this.gridColumn36,
            this.gridColumn37,
            this.gridColumn137,
            this.gridColumn38,
            this.gridColumn39,
            this.gridColumn40,
            this.gridColumn41,
            this.gridColumn42,
            this.gridColumn43,
            this.gridColumn44,
            this.gridColumn45,
            this.gridColumn46,
            this.gridColumn150,
            this.gridColumn149,
            this.gridColumn148,
            this.gridColumn147,
            this.gridColumn146,
            this.gridColumn145,
            this.gridColumn144,
            this.gridColumn143,
            this.gridColumn142,
            this.gridColumn161,
            this.gridColumn160,
            this.gridColumn159,
            this.gridColumn158,
            this.gridColumn157,
            this.gridColumn156,
            this.gridColumn155,
            this.gridColumn154,
            this.gridColumn153,
            this.gridColumn152,
            this.gridColumn151,
            this.gridColumn170,
            this.gridColumn169,
            this.gridColumn168,
            this.gridColumn167,
            this.gridColumn166,
            this.gridColumn165,
            this.gridColumn164,
            this.gridColumn163,
            this.gridColumn162,
            this.gridColumn141,
            this.gridColumn47,
            this.gridColumn48,
            this.gridColumn49,
            this.gridColumn50,
            this.gridColumn51,
            this.gridColumn109,
            this.gridColumn110,
            this.gridColumn111,
            this.gridColumn112,
            this.gridColumn113,
            this.gridColumn324});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView1.OptionsView.ColumnAutoWidth = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.DoubleClick += new System.EventHandler(this.gridControl1_DoubleClick);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "距离(米)";
            this.gridColumn1.FieldName = "Idistance";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "时长(秒)";
            this.gridColumn2.FieldName = "Iduration";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "下载速率0";
            this.gridColumn3.FieldName = "APP_Speed0";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 2;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "下载速率[0,500]";
            this.gridColumn4.FieldName = "APP_Speed0_500";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 3;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "下载速率[500,800]";
            this.gridColumn5.FieldName = "APP_Speed500_800";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 4;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "下载速率[800,900]";
            this.gridColumn6.FieldName = "APP_Speed800_900";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.VisibleIndex = 5;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "下载速率[900,1100]";
            this.gridColumn7.FieldName = "APP_Speed900_1100";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.VisibleIndex = 6;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "下载速率1100";
            this.gridColumn8.FieldName = "APP_Speed1100";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.VisibleIndex = 7;
            // 
            // gridColumn33
            // 
            this.gridColumn33.Caption = "TD电平均值";
            this.gridColumn33.FieldName = "Rscpmean";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.Visible = true;
            this.gridColumn33.VisibleIndex = 8;
            // 
            // gridColumn34
            // 
            this.gridColumn34.Caption = "TD电平小于75";
            this.gridColumn34.FieldName = "Rscp75";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.Visible = true;
            this.gridColumn34.VisibleIndex = 9;
            // 
            // gridColumn35
            // 
            this.gridColumn35.Caption = "TD电平[76,80]";
            this.gridColumn35.FieldName = "Rscp76_80";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.Visible = true;
            this.gridColumn35.VisibleIndex = 10;
            // 
            // gridColumn36
            // 
            this.gridColumn36.Caption = "TD电平[81,85]";
            this.gridColumn36.FieldName = "Rscp81_85";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.Visible = true;
            this.gridColumn36.VisibleIndex = 11;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "TD电平[86,90]";
            this.gridColumn37.FieldName = "Rscp86_90";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.Visible = true;
            this.gridColumn37.VisibleIndex = 13;
            // 
            // gridColumn137
            // 
            this.gridColumn137.Caption = "TD电平[91,94]";
            this.gridColumn137.FieldName = "Rscp91_94";
            this.gridColumn137.Name = "gridColumn137";
            this.gridColumn137.Visible = true;
            this.gridColumn137.VisibleIndex = 12;
            // 
            // gridColumn38
            // 
            this.gridColumn38.Caption = "TD电平大于94";
            this.gridColumn38.FieldName = "Rscp94";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.Visible = true;
            this.gridColumn38.VisibleIndex = 14;
            // 
            // gridColumn39
            // 
            this.gridColumn39.Caption = "GSM电平均值";
            this.gridColumn39.FieldName = "Rxlmean";
            this.gridColumn39.Name = "gridColumn39";
            this.gridColumn39.Visible = true;
            this.gridColumn39.VisibleIndex = 15;
            // 
            // gridColumn40
            // 
            this.gridColumn40.Caption = "GSM电平小于75";
            this.gridColumn40.FieldName = "Rxl75";
            this.gridColumn40.Name = "gridColumn40";
            this.gridColumn40.Visible = true;
            this.gridColumn40.VisibleIndex = 16;
            // 
            // gridColumn41
            // 
            this.gridColumn41.Caption = "GSM电平[76,80]";
            this.gridColumn41.FieldName = "Rxl76_80";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.Visible = true;
            this.gridColumn41.VisibleIndex = 17;
            // 
            // gridColumn42
            // 
            this.gridColumn42.Caption = "GSM电平[81,85]";
            this.gridColumn42.FieldName = "Rxl81_85";
            this.gridColumn42.Name = "gridColumn42";
            this.gridColumn42.Visible = true;
            this.gridColumn42.VisibleIndex = 18;
            // 
            // gridColumn43
            // 
            this.gridColumn43.Caption = "GSM电平[86,90]";
            this.gridColumn43.FieldName = "Rxl86_90";
            this.gridColumn43.Name = "gridColumn43";
            this.gridColumn43.Visible = true;
            this.gridColumn43.VisibleIndex = 19;
            // 
            // gridColumn44
            // 
            this.gridColumn44.Caption = "GSM电平[91,94]";
            this.gridColumn44.FieldName = "Rxl91_94";
            this.gridColumn44.Name = "gridColumn44";
            this.gridColumn44.Visible = true;
            this.gridColumn44.VisibleIndex = 20;
            // 
            // gridColumn45
            // 
            this.gridColumn45.Caption = "GSM电平大于94";
            this.gridColumn45.FieldName = "Rxl94";
            this.gridColumn45.Name = "gridColumn45";
            this.gridColumn45.Visible = true;
            this.gridColumn45.VisibleIndex = 21;
            // 
            // gridColumn46
            // 
            this.gridColumn46.Caption = "DPCH C/I均值";
            this.gridColumn46.FieldName = "Dc2imean";
            this.gridColumn46.Name = "gridColumn46";
            this.gridColumn46.Visible = true;
            this.gridColumn46.VisibleIndex = 22;
            // 
            // gridColumn150
            // 
            this.gridColumn150.Caption = "DPCH C/I小于-10";
            this.gridColumn150.FieldName = "Dc2iF10";
            this.gridColumn150.Name = "gridColumn150";
            this.gridColumn150.Visible = true;
            this.gridColumn150.VisibleIndex = 23;
            // 
            // gridColumn149
            // 
            this.gridColumn149.Caption = "DPCH C/I[-10,-3]";
            this.gridColumn149.FieldName = "Dc2iF10TF3";
            this.gridColumn149.Name = "gridColumn149";
            this.gridColumn149.Visible = true;
            this.gridColumn149.VisibleIndex = 24;
            // 
            // gridColumn148
            // 
            this.gridColumn148.Caption = "DPCH C/I[-3,15]";
            this.gridColumn148.FieldName = "Dc2iF3T15";
            this.gridColumn148.Name = "gridColumn148";
            this.gridColumn148.Visible = true;
            this.gridColumn148.VisibleIndex = 26;
            // 
            // gridColumn147
            // 
            this.gridColumn147.Caption = "DPCH C/I大于15";
            this.gridColumn147.FieldName = "Dc2i15";
            this.gridColumn147.Name = "gridColumn147";
            this.gridColumn147.Visible = true;
            this.gridColumn147.VisibleIndex = 28;
            // 
            // gridColumn146
            // 
            this.gridColumn146.Caption = "TxPower均值";
            this.gridColumn146.FieldName = "Txpowermean";
            this.gridColumn146.Name = "gridColumn146";
            this.gridColumn146.Visible = true;
            this.gridColumn146.VisibleIndex = 30;
            // 
            // gridColumn145
            // 
            this.gridColumn145.Caption = "TxPower小于-20";
            this.gridColumn145.FieldName = "TxpowerF20";
            this.gridColumn145.Name = "gridColumn145";
            this.gridColumn145.Visible = true;
            this.gridColumn145.VisibleIndex = 32;
            // 
            // gridColumn144
            // 
            this.gridColumn144.Caption = "TxPower[-20,0]";
            this.gridColumn144.FieldName = "TxpowerF20T0";
            this.gridColumn144.Name = "gridColumn144";
            this.gridColumn144.Visible = true;
            this.gridColumn144.VisibleIndex = 33;
            // 
            // gridColumn143
            // 
            this.gridColumn143.Caption = "TxPower[0,15]";
            this.gridColumn143.FieldName = "Txpower0T15";
            this.gridColumn143.Name = "gridColumn143";
            this.gridColumn143.Visible = true;
            this.gridColumn143.VisibleIndex = 34;
            // 
            // gridColumn142
            // 
            this.gridColumn142.Caption = "TxPower大于15";
            this.gridColumn142.FieldName = "Txpower15";
            this.gridColumn142.Name = "gridColumn142";
            this.gridColumn142.Visible = true;
            this.gridColumn142.VisibleIndex = 35;
            // 
            // gridColumn161
            // 
            this.gridColumn161.Caption = "T至G切换成功次数";
            this.gridColumn161.FieldName = "HO_SUCC_T2G";
            this.gridColumn161.Name = "gridColumn161";
            this.gridColumn161.Visible = true;
            this.gridColumn161.VisibleIndex = 36;
            // 
            // gridColumn160
            // 
            this.gridColumn160.Caption = "T至G切换失败次数";
            this.gridColumn160.FieldName = "HO_FAIL_T2G";
            this.gridColumn160.Name = "gridColumn160";
            this.gridColumn160.Visible = true;
            this.gridColumn160.VisibleIndex = 37;
            // 
            // gridColumn159
            // 
            this.gridColumn159.Caption = "T至G切换时延";
            this.gridColumn159.FieldName = "HO_Time_T2G";
            this.gridColumn159.Name = "gridColumn159";
            this.gridColumn159.Visible = true;
            this.gridColumn159.VisibleIndex = 39;
            // 
            // gridColumn158
            // 
            this.gridColumn158.Caption = "T网硬切换成功次数";
            this.gridColumn158.FieldName = "HO_SUCC_T2T";
            this.gridColumn158.Name = "gridColumn158";
            this.gridColumn158.Visible = true;
            this.gridColumn158.VisibleIndex = 40;
            // 
            // gridColumn157
            // 
            this.gridColumn157.Caption = "T网硬切换失败次数";
            this.gridColumn157.FieldName = "HO_FAIL_T2T";
            this.gridColumn157.Name = "gridColumn157";
            this.gridColumn157.Visible = true;
            this.gridColumn157.VisibleIndex = 41;
            // 
            // gridColumn156
            // 
            this.gridColumn156.Caption = "T网硬切换时延";
            this.gridColumn156.FieldName = "HO_Time_T2T";
            this.gridColumn156.Name = "gridColumn156";
            this.gridColumn156.Visible = true;
            this.gridColumn156.VisibleIndex = 42;
            // 
            // gridColumn155
            // 
            this.gridColumn155.Caption = "T网接力切换成功次数";
            this.gridColumn155.FieldName = "HO_SUCC_Baton";
            this.gridColumn155.Name = "gridColumn155";
            this.gridColumn155.Visible = true;
            this.gridColumn155.VisibleIndex = 43;
            // 
            // gridColumn154
            // 
            this.gridColumn154.Caption = "T网接力切换失败次数";
            this.gridColumn154.FieldName = "HO_FAIL_Baton";
            this.gridColumn154.Name = "gridColumn154";
            this.gridColumn154.Visible = true;
            this.gridColumn154.VisibleIndex = 44;
            // 
            // gridColumn153
            // 
            this.gridColumn153.Caption = "T网接力切换时延";
            this.gridColumn153.FieldName = "HO_Time_Baton";
            this.gridColumn153.Name = "gridColumn153";
            this.gridColumn153.Visible = true;
            this.gridColumn153.VisibleIndex = 45;
            // 
            // gridColumn152
            // 
            this.gridColumn152.Caption = "T网重选成功次数";
            this.gridColumn152.FieldName = "CR_SUCC_T2T";
            this.gridColumn152.Name = "gridColumn152";
            this.gridColumn152.Visible = true;
            this.gridColumn152.VisibleIndex = 46;
            // 
            // gridColumn151
            // 
            this.gridColumn151.Caption = "T网重选失败次数";
            this.gridColumn151.FieldName = "CR_FAIL_T2T";
            this.gridColumn151.Name = "gridColumn151";
            this.gridColumn151.Visible = true;
            this.gridColumn151.VisibleIndex = 47;
            // 
            // gridColumn170
            // 
            this.gridColumn170.Caption = "T至G重选成功次数";
            this.gridColumn170.FieldName = "CR_SUCC_T2G";
            this.gridColumn170.Name = "gridColumn170";
            this.gridColumn170.Visible = true;
            this.gridColumn170.VisibleIndex = 48;
            // 
            // gridColumn169
            // 
            this.gridColumn169.Caption = "T至G重选失败次数";
            this.gridColumn169.FieldName = "CR_FAIL_T2G";
            this.gridColumn169.Name = "gridColumn169";
            this.gridColumn169.Visible = true;
            this.gridColumn169.VisibleIndex = 49;
            // 
            // gridColumn168
            // 
            this.gridColumn168.Caption = "G至T重选成功次数";
            this.gridColumn168.FieldName = "CR_SUCC_G2T";
            this.gridColumn168.Name = "gridColumn168";
            this.gridColumn168.Visible = true;
            this.gridColumn168.VisibleIndex = 50;
            // 
            // gridColumn167
            // 
            this.gridColumn167.Caption = "G至T重选失败次数";
            this.gridColumn167.FieldName = "CR_FAIL_G2T";
            this.gridColumn167.Name = "gridColumn167";
            this.gridColumn167.Visible = true;
            this.gridColumn167.VisibleIndex = 51;
            // 
            // gridColumn166
            // 
            this.gridColumn166.Caption = "Hsdpa下载量占比";
            this.gridColumn166.FieldName = "HSDPA_Size";
            this.gridColumn166.Name = "gridColumn166";
            this.gridColumn166.Visible = true;
            this.gridColumn166.VisibleIndex = 52;
            // 
            // gridColumn165
            // 
            this.gridColumn165.Caption = "Hsdpa时长占比";
            this.gridColumn165.FieldName = "HSDPA_Time";
            this.gridColumn165.Name = "gridColumn165";
            this.gridColumn165.Visible = true;
            this.gridColumn165.VisibleIndex = 53;
            // 
            // gridColumn164
            // 
            this.gridColumn164.Caption = "R4下载量占比";
            this.gridColumn164.FieldName = "R4_Size";
            this.gridColumn164.Name = "gridColumn164";
            this.gridColumn164.Visible = true;
            this.gridColumn164.VisibleIndex = 54;
            // 
            // gridColumn163
            // 
            this.gridColumn163.Caption = "R4时长占比";
            this.gridColumn163.FieldName = "R4_Time";
            this.gridColumn163.Name = "gridColumn163";
            this.gridColumn163.Visible = true;
            this.gridColumn163.VisibleIndex = 55;
            // 
            // gridColumn162
            // 
            this.gridColumn162.Caption = "Gsm下载量占比";
            this.gridColumn162.FieldName = "EDGE_Size";
            this.gridColumn162.Name = "gridColumn162";
            this.gridColumn162.Visible = true;
            this.gridColumn162.VisibleIndex = 56;
            // 
            // gridColumn141
            // 
            this.gridColumn141.Caption = "Gsm时长占比";
            this.gridColumn141.FieldName = "EDGE_Time";
            this.gridColumn141.Name = "gridColumn141";
            this.gridColumn141.Visible = true;
            this.gridColumn141.VisibleIndex = 57;
            // 
            // gridColumn47
            // 
            this.gridColumn47.Caption = "小区1";
            this.gridColumn47.FieldName = "Cell1";
            this.gridColumn47.Name = "gridColumn47";
            this.gridColumn47.Visible = true;
            this.gridColumn47.VisibleIndex = 38;
            // 
            // gridColumn48
            // 
            this.gridColumn48.Caption = "小区2";
            this.gridColumn48.FieldName = "Cell2";
            this.gridColumn48.Name = "gridColumn48";
            this.gridColumn48.Visible = true;
            this.gridColumn48.VisibleIndex = 25;
            // 
            // gridColumn49
            // 
            this.gridColumn49.Caption = "小区3";
            this.gridColumn49.FieldName = "Cell3";
            this.gridColumn49.Name = "gridColumn49";
            this.gridColumn49.Visible = true;
            this.gridColumn49.VisibleIndex = 27;
            // 
            // gridColumn50
            // 
            this.gridColumn50.Caption = "所属网格";
            this.gridColumn50.FieldName = "Strgrid";
            this.gridColumn50.Name = "gridColumn50";
            this.gridColumn50.Visible = true;
            this.gridColumn50.VisibleIndex = 29;
            // 
            // gridColumn51
            // 
            this.gridColumn51.Caption = "所属道路";
            this.gridColumn51.FieldName = "Strroad";
            this.gridColumn51.Name = "gridColumn51";
            this.gridColumn51.Visible = true;
            this.gridColumn51.VisibleIndex = 31;
            // 
            // gridColumn109
            // 
            this.gridColumn109.Caption = "经度";
            this.gridColumn109.FieldName = "Imlongitude";
            this.gridColumn109.Name = "gridColumn109";
            // 
            // gridColumn110
            // 
            this.gridColumn110.Caption = "纬度";
            this.gridColumn110.FieldName = "Imlatitude";
            this.gridColumn110.Name = "gridColumn110";
            // 
            // gridColumn111
            // 
            this.gridColumn111.Caption = "ifileid";
            this.gridColumn111.FieldName = "Ifileid";
            this.gridColumn111.Name = "gridColumn111";
            // 
            // gridColumn112
            // 
            this.gridColumn112.Caption = "istime";
            this.gridColumn112.FieldName = "Istime";
            this.gridColumn112.Name = "gridColumn112";
            // 
            // gridColumn113
            // 
            this.gridColumn113.Caption = "ietime";
            this.gridColumn113.FieldName = "Ietime";
            this.gridColumn113.Name = "gridColumn113";
            // 
            // gridColumn324
            // 
            this.gridColumn324.Caption = "gridColumn324";
            this.gridColumn324.FieldName = "Iid";
            this.gridColumn324.Name = "gridColumn324";
            // 
            // gridView2
            // 
            this.gridView2.GridControl = this.gridControl1;
            this.gridView2.Name = "gridView2";
            // 
            // xtraTabPage3
            // 
            this.xtraTabPage3.Controls.Add(this.gridControl2);
            this.xtraTabPage3.Name = "xtraTabPage3";
            this.xtraTabPage3.Size = new System.Drawing.Size(836, 381);
            this.xtraTabPage3.Text = "弱覆盖";
            // 
            // gridControl2
            // 
            this.gridControl2.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl2.Location = new System.Drawing.Point(0, 0);
            this.gridControl2.MainView = this.bandedGridView2;
            this.gridControl2.Name = "gridControl2";
            this.gridControl2.Size = new System.Drawing.Size(836, 381);
            this.gridControl2.TabIndex = 1;
            this.gridControl2.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView2,
            this.gridView3,
            this.gridView4});
            this.gridControl2.DoubleClick += new System.EventHandler(this.gridControl2_DoubleClick);
            // 
            // bandedGridView2
            // 
            this.bandedGridView2.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand2,
            this.gridBand18,
            this.gridBand19,
            this.gridBand20,
            this.gridBand21,
            this.gridBand22,
            this.gridBand23,
            this.gridBand24,
            this.gridBand25,
            this.gridBand26,
            this.gridBand27,
            this.gridBand28,
            this.gridBand29});
            this.bandedGridView2.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn65,
            this.bandedGridColumn66,
            this.bandedGridColumn67,
            this.bandedGridColumn68,
            this.bandedGridColumn69,
            this.bandedGridColumn70,
            this.bandedGridColumn71,
            this.bandedGridColumn72,
            this.bandedGridColumn73,
            this.bandedGridColumn74,
            this.bandedGridColumn75,
            this.bandedGridColumn76,
            this.bandedGridColumn77,
            this.bandedGridColumn78,
            this.bandedGridColumn79,
            this.bandedGridColumn80,
            this.bandedGridColumn81,
            this.bandedGridColumn82,
            this.bandedGridColumn83,
            this.bandedGridColumn84,
            this.bandedGridColumn85,
            this.bandedGridColumn86,
            this.bandedGridColumn87,
            this.bandedGridColumn88,
            this.bandedGridColumn89,
            this.bandedGridColumn90,
            this.bandedGridColumn91,
            this.bandedGridColumn92,
            this.bandedGridColumn93,
            this.bandedGridColumn94,
            this.bandedGridColumn95,
            this.bandedGridColumn96,
            this.bandedGridColumn97,
            this.bandedGridColumn98,
            this.bandedGridColumn99,
            this.bandedGridColumn100,
            this.bandedGridColumn101,
            this.bandedGridColumn102,
            this.bandedGridColumn103,
            this.bandedGridColumn104,
            this.bandedGridColumn105,
            this.bandedGridColumn106,
            this.bandedGridColumn107,
            this.bandedGridColumn108,
            this.bandedGridColumn109,
            this.bandedGridColumn110,
            this.bandedGridColumn111,
            this.bandedGridColumn112,
            this.bandedGridColumn113,
            this.bandedGridColumn114,
            this.bandedGridColumn115,
            this.bandedGridColumn116,
            this.bandedGridColumn117,
            this.bandedGridColumn118,
            this.bandedGridColumn119,
            this.bandedGridColumn120,
            this.bandedGridColumn121,
            this.bandedGridColumn122,
            this.bandedGridColumn123,
            this.bandedGridColumn124,
            this.bandedGridColumn125,
            this.bandedGridColumn126,
            this.bandedGridColumn127,
            this.bandedGridColumn128});
            this.bandedGridView2.GridControl = this.gridControl2;
            this.bandedGridView2.Name = "bandedGridView2";
            this.bandedGridView2.OptionsBehavior.Editable = false;
            this.bandedGridView2.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.bandedGridView2.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView2.OptionsView.ShowGroupPanel = false;
            // 
            // gridView3
            // 
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn18,
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23,
            this.gridColumn24,
            this.gridColumn25,
            this.gridColumn26,
            this.gridColumn27,
            this.gridColumn28,
            this.gridColumn29,
            this.gridColumn30,
            this.gridColumn31,
            this.gridColumn32,
            this.gridColumn52,
            this.gridColumn53,
            this.gridColumn54,
            this.gridColumn55,
            this.gridColumn56,
            this.gridColumn57,
            this.gridColumn58,
            this.gridColumn59,
            this.gridColumn60,
            this.gridColumn61,
            this.gridColumn62,
            this.gridColumn63,
            this.gridColumn64,
            this.gridColumn65,
            this.gridColumn66,
            this.gridColumn67,
            this.gridColumn68,
            this.gridColumn69,
            this.gridColumn70,
            this.gridColumn71,
            this.gridColumn72,
            this.gridColumn73,
            this.gridColumn74,
            this.gridColumn75,
            this.gridColumn76,
            this.gridColumn77,
            this.gridColumn78,
            this.gridColumn79,
            this.gridColumn80,
            this.gridColumn81,
            this.gridColumn82,
            this.gridColumn83,
            this.gridColumn84,
            this.gridColumn85,
            this.gridColumn86,
            this.gridColumn87,
            this.gridColumn88,
            this.gridColumn89,
            this.gridColumn90,
            this.gridColumn325});
            this.gridView3.GridControl = this.gridControl2;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsBehavior.Editable = false;
            this.gridView3.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView3.OptionsView.ColumnAutoWidth = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            this.gridView3.DoubleClick += new System.EventHandler(this.gridControl2_DoubleClick);
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "距离(米)";
            this.gridColumn9.FieldName = "Idistance";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.VisibleIndex = 0;
            // 
            // gridColumn10
            // 
            this.gridColumn10.Caption = "时长(秒)";
            this.gridColumn10.FieldName = "Iduration";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.Visible = true;
            this.gridColumn10.VisibleIndex = 1;
            // 
            // gridColumn11
            // 
            this.gridColumn11.Caption = "下载速率0";
            this.gridColumn11.FieldName = "APP_Speed0";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Visible = true;
            this.gridColumn11.VisibleIndex = 2;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "下载速率[0,500]";
            this.gridColumn12.FieldName = "APP_Speed0_500";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.VisibleIndex = 3;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "下载速率[500,800]";
            this.gridColumn13.FieldName = "APP_Speed500_800";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.VisibleIndex = 4;
            // 
            // gridColumn14
            // 
            this.gridColumn14.Caption = "下载速率[800,900]";
            this.gridColumn14.FieldName = "APP_Speed800_900";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.Visible = true;
            this.gridColumn14.VisibleIndex = 5;
            // 
            // gridColumn15
            // 
            this.gridColumn15.Caption = "下载速率[900,1100]";
            this.gridColumn15.FieldName = "APP_Speed900_1100";
            this.gridColumn15.Name = "gridColumn15";
            this.gridColumn15.Visible = true;
            this.gridColumn15.VisibleIndex = 6;
            // 
            // gridColumn16
            // 
            this.gridColumn16.Caption = "下载速率1100";
            this.gridColumn16.FieldName = "APP_Speed1100";
            this.gridColumn16.Name = "gridColumn16";
            this.gridColumn16.Visible = true;
            this.gridColumn16.VisibleIndex = 7;
            // 
            // gridColumn17
            // 
            this.gridColumn17.Caption = "TD电平均值";
            this.gridColumn17.FieldName = "Rscpmean";
            this.gridColumn17.Name = "gridColumn17";
            this.gridColumn17.Visible = true;
            this.gridColumn17.VisibleIndex = 8;
            // 
            // gridColumn18
            // 
            this.gridColumn18.Caption = "TD电平小于75";
            this.gridColumn18.FieldName = "Rscp75";
            this.gridColumn18.Name = "gridColumn18";
            this.gridColumn18.Visible = true;
            this.gridColumn18.VisibleIndex = 9;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "TD电平[76,80]";
            this.gridColumn19.FieldName = "Rscp76_80";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.VisibleIndex = 10;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "TD电平[81,85]";
            this.gridColumn20.FieldName = "Rscp81_85";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.VisibleIndex = 11;
            // 
            // gridColumn21
            // 
            this.gridColumn21.Caption = "TD电平[86,90]";
            this.gridColumn21.FieldName = "Rscp86_90";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.Visible = true;
            this.gridColumn21.VisibleIndex = 13;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "TD电平[91,94]";
            this.gridColumn22.FieldName = "Rscp91_94";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 12;
            // 
            // gridColumn23
            // 
            this.gridColumn23.Caption = "TD电平大于94";
            this.gridColumn23.FieldName = "Rscp94";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.Visible = true;
            this.gridColumn23.VisibleIndex = 14;
            // 
            // gridColumn24
            // 
            this.gridColumn24.Caption = "GSM电平均值";
            this.gridColumn24.FieldName = "Rxlmean";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.Visible = true;
            this.gridColumn24.VisibleIndex = 15;
            // 
            // gridColumn25
            // 
            this.gridColumn25.Caption = "GSM电平小于75";
            this.gridColumn25.FieldName = "Rxl75";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.Visible = true;
            this.gridColumn25.VisibleIndex = 16;
            // 
            // gridColumn26
            // 
            this.gridColumn26.Caption = "GSM电平[76,80]";
            this.gridColumn26.FieldName = "Rxl76_80";
            this.gridColumn26.Name = "gridColumn26";
            this.gridColumn26.Visible = true;
            this.gridColumn26.VisibleIndex = 17;
            // 
            // gridColumn27
            // 
            this.gridColumn27.Caption = "GSM电平[81,85]";
            this.gridColumn27.FieldName = "Rxl81_85";
            this.gridColumn27.Name = "gridColumn27";
            this.gridColumn27.Visible = true;
            this.gridColumn27.VisibleIndex = 18;
            // 
            // gridColumn28
            // 
            this.gridColumn28.Caption = "GSM电平[86,90]";
            this.gridColumn28.FieldName = "Rxl86_90";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.Visible = true;
            this.gridColumn28.VisibleIndex = 19;
            // 
            // gridColumn29
            // 
            this.gridColumn29.Caption = "GSM电平[91,94]";
            this.gridColumn29.FieldName = "Rxl91_94";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.Visible = true;
            this.gridColumn29.VisibleIndex = 20;
            // 
            // gridColumn30
            // 
            this.gridColumn30.Caption = "GSM电平大于94";
            this.gridColumn30.FieldName = "Rxl94";
            this.gridColumn30.Name = "gridColumn30";
            this.gridColumn30.Visible = true;
            this.gridColumn30.VisibleIndex = 21;
            // 
            // gridColumn31
            // 
            this.gridColumn31.Caption = "DPCH C/I均值";
            this.gridColumn31.FieldName = "Dc2imean";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.Visible = true;
            this.gridColumn31.VisibleIndex = 22;
            // 
            // gridColumn32
            // 
            this.gridColumn32.Caption = "DPCH C/I小于-10";
            this.gridColumn32.FieldName = "Dc2iF10";
            this.gridColumn32.Name = "gridColumn32";
            this.gridColumn32.Visible = true;
            this.gridColumn32.VisibleIndex = 23;
            // 
            // gridColumn52
            // 
            this.gridColumn52.Caption = "DPCH C/I[-10,-3]";
            this.gridColumn52.FieldName = "Dc2iF10TF3";
            this.gridColumn52.Name = "gridColumn52";
            this.gridColumn52.Visible = true;
            this.gridColumn52.VisibleIndex = 24;
            // 
            // gridColumn53
            // 
            this.gridColumn53.Caption = "DPCH C/I[-3,15]";
            this.gridColumn53.FieldName = "Dc2iF3T15";
            this.gridColumn53.Name = "gridColumn53";
            this.gridColumn53.Visible = true;
            this.gridColumn53.VisibleIndex = 26;
            // 
            // gridColumn54
            // 
            this.gridColumn54.Caption = "DPCH C/I大于15";
            this.gridColumn54.FieldName = "Dc2i15";
            this.gridColumn54.Name = "gridColumn54";
            this.gridColumn54.Visible = true;
            this.gridColumn54.VisibleIndex = 28;
            // 
            // gridColumn55
            // 
            this.gridColumn55.Caption = "TxPower均值";
            this.gridColumn55.FieldName = "Txpowermean";
            this.gridColumn55.Name = "gridColumn55";
            this.gridColumn55.Visible = true;
            this.gridColumn55.VisibleIndex = 30;
            // 
            // gridColumn56
            // 
            this.gridColumn56.Caption = "TxPower小于-20";
            this.gridColumn56.FieldName = "TxpowerF20";
            this.gridColumn56.Name = "gridColumn56";
            this.gridColumn56.Visible = true;
            this.gridColumn56.VisibleIndex = 32;
            // 
            // gridColumn57
            // 
            this.gridColumn57.Caption = "TxPower[-20,0]";
            this.gridColumn57.FieldName = "TxpowerF20T0";
            this.gridColumn57.Name = "gridColumn57";
            this.gridColumn57.Visible = true;
            this.gridColumn57.VisibleIndex = 33;
            // 
            // gridColumn58
            // 
            this.gridColumn58.Caption = "TxPower[0,15]";
            this.gridColumn58.FieldName = "Txpower0T15";
            this.gridColumn58.Name = "gridColumn58";
            this.gridColumn58.Visible = true;
            this.gridColumn58.VisibleIndex = 34;
            // 
            // gridColumn59
            // 
            this.gridColumn59.Caption = "TxPower大于15";
            this.gridColumn59.FieldName = "Txpower15";
            this.gridColumn59.Name = "gridColumn59";
            this.gridColumn59.Visible = true;
            this.gridColumn59.VisibleIndex = 35;
            // 
            // gridColumn60
            // 
            this.gridColumn60.Caption = "T至G切换成功次数";
            this.gridColumn60.FieldName = "HO_SUCC_T2G";
            this.gridColumn60.Name = "gridColumn60";
            this.gridColumn60.Visible = true;
            this.gridColumn60.VisibleIndex = 36;
            // 
            // gridColumn61
            // 
            this.gridColumn61.Caption = "T至G切换失败次数";
            this.gridColumn61.FieldName = "HO_FAIL_T2G";
            this.gridColumn61.Name = "gridColumn61";
            this.gridColumn61.Visible = true;
            this.gridColumn61.VisibleIndex = 37;
            // 
            // gridColumn62
            // 
            this.gridColumn62.Caption = "T至G切换时延";
            this.gridColumn62.FieldName = "HO_Time_T2G";
            this.gridColumn62.Name = "gridColumn62";
            this.gridColumn62.Visible = true;
            this.gridColumn62.VisibleIndex = 39;
            // 
            // gridColumn63
            // 
            this.gridColumn63.Caption = "T网硬切换成功次数";
            this.gridColumn63.FieldName = "HO_SUCC_T2T";
            this.gridColumn63.Name = "gridColumn63";
            this.gridColumn63.Visible = true;
            this.gridColumn63.VisibleIndex = 40;
            // 
            // gridColumn64
            // 
            this.gridColumn64.Caption = "T网硬切换失败次数";
            this.gridColumn64.FieldName = "HO_FAIL_T2T";
            this.gridColumn64.Name = "gridColumn64";
            this.gridColumn64.Visible = true;
            this.gridColumn64.VisibleIndex = 41;
            // 
            // gridColumn65
            // 
            this.gridColumn65.Caption = "T网硬切换时延";
            this.gridColumn65.FieldName = "HO_Time_T2T";
            this.gridColumn65.Name = "gridColumn65";
            this.gridColumn65.Visible = true;
            this.gridColumn65.VisibleIndex = 42;
            // 
            // gridColumn66
            // 
            this.gridColumn66.Caption = "T网接力切换成功次数";
            this.gridColumn66.FieldName = "HO_SUCC_Baton";
            this.gridColumn66.Name = "gridColumn66";
            this.gridColumn66.Visible = true;
            this.gridColumn66.VisibleIndex = 43;
            // 
            // gridColumn67
            // 
            this.gridColumn67.Caption = "T网接力切换失败次数";
            this.gridColumn67.FieldName = "HO_FAIL_Baton";
            this.gridColumn67.Name = "gridColumn67";
            this.gridColumn67.Visible = true;
            this.gridColumn67.VisibleIndex = 44;
            // 
            // gridColumn68
            // 
            this.gridColumn68.Caption = "T网接力切换时延";
            this.gridColumn68.FieldName = "HO_Time_Baton";
            this.gridColumn68.Name = "gridColumn68";
            this.gridColumn68.Visible = true;
            this.gridColumn68.VisibleIndex = 45;
            // 
            // gridColumn69
            // 
            this.gridColumn69.Caption = "T网重选成功次数";
            this.gridColumn69.FieldName = "CR_SUCC_T2T";
            this.gridColumn69.Name = "gridColumn69";
            this.gridColumn69.Visible = true;
            this.gridColumn69.VisibleIndex = 46;
            // 
            // gridColumn70
            // 
            this.gridColumn70.Caption = "T网重选失败次数";
            this.gridColumn70.FieldName = "CR_FAIL_T2T";
            this.gridColumn70.Name = "gridColumn70";
            this.gridColumn70.Visible = true;
            this.gridColumn70.VisibleIndex = 47;
            // 
            // gridColumn71
            // 
            this.gridColumn71.Caption = "T至G重选成功次数";
            this.gridColumn71.FieldName = "CR_SUCC_T2G";
            this.gridColumn71.Name = "gridColumn71";
            this.gridColumn71.Visible = true;
            this.gridColumn71.VisibleIndex = 48;
            // 
            // gridColumn72
            // 
            this.gridColumn72.Caption = "T至G重选失败次数";
            this.gridColumn72.FieldName = "CR_FAIL_T2G";
            this.gridColumn72.Name = "gridColumn72";
            this.gridColumn72.Visible = true;
            this.gridColumn72.VisibleIndex = 49;
            // 
            // gridColumn73
            // 
            this.gridColumn73.Caption = "G至T重选成功次数";
            this.gridColumn73.FieldName = "CR_SUCC_G2T";
            this.gridColumn73.Name = "gridColumn73";
            this.gridColumn73.Visible = true;
            this.gridColumn73.VisibleIndex = 50;
            // 
            // gridColumn74
            // 
            this.gridColumn74.Caption = "G至T重选失败次数";
            this.gridColumn74.FieldName = "CR_FAIL_G2T";
            this.gridColumn74.Name = "gridColumn74";
            this.gridColumn74.Visible = true;
            this.gridColumn74.VisibleIndex = 51;
            // 
            // gridColumn75
            // 
            this.gridColumn75.Caption = "Hsdpa下载量占比";
            this.gridColumn75.FieldName = "HSDPA_Size";
            this.gridColumn75.Name = "gridColumn75";
            this.gridColumn75.Visible = true;
            this.gridColumn75.VisibleIndex = 52;
            // 
            // gridColumn76
            // 
            this.gridColumn76.Caption = "Hsdpa时长占比";
            this.gridColumn76.FieldName = "HSDPA_Time";
            this.gridColumn76.Name = "gridColumn76";
            this.gridColumn76.Visible = true;
            this.gridColumn76.VisibleIndex = 53;
            // 
            // gridColumn77
            // 
            this.gridColumn77.Caption = "R4下载量占比";
            this.gridColumn77.FieldName = "R4_Size";
            this.gridColumn77.Name = "gridColumn77";
            this.gridColumn77.Visible = true;
            this.gridColumn77.VisibleIndex = 54;
            // 
            // gridColumn78
            // 
            this.gridColumn78.Caption = "R4时长占比";
            this.gridColumn78.FieldName = "R4_Time";
            this.gridColumn78.Name = "gridColumn78";
            this.gridColumn78.Visible = true;
            this.gridColumn78.VisibleIndex = 55;
            // 
            // gridColumn79
            // 
            this.gridColumn79.Caption = "Gsm下载量占比";
            this.gridColumn79.FieldName = "EDGE_Size";
            this.gridColumn79.Name = "gridColumn79";
            this.gridColumn79.Visible = true;
            this.gridColumn79.VisibleIndex = 56;
            // 
            // gridColumn80
            // 
            this.gridColumn80.Caption = "Gsm时长占比";
            this.gridColumn80.FieldName = "EDGE_Time";
            this.gridColumn80.Name = "gridColumn80";
            this.gridColumn80.Visible = true;
            this.gridColumn80.VisibleIndex = 57;
            // 
            // gridColumn81
            // 
            this.gridColumn81.Caption = "小区1";
            this.gridColumn81.FieldName = "Cell1";
            this.gridColumn81.Name = "gridColumn81";
            this.gridColumn81.Visible = true;
            this.gridColumn81.VisibleIndex = 38;
            // 
            // gridColumn82
            // 
            this.gridColumn82.Caption = "小区2";
            this.gridColumn82.FieldName = "Cell2";
            this.gridColumn82.Name = "gridColumn82";
            this.gridColumn82.Visible = true;
            this.gridColumn82.VisibleIndex = 25;
            // 
            // gridColumn83
            // 
            this.gridColumn83.Caption = "小区3";
            this.gridColumn83.FieldName = "Cell3";
            this.gridColumn83.Name = "gridColumn83";
            this.gridColumn83.Visible = true;
            this.gridColumn83.VisibleIndex = 27;
            // 
            // gridColumn84
            // 
            this.gridColumn84.Caption = "所属网格";
            this.gridColumn84.FieldName = "Strgrid";
            this.gridColumn84.Name = "gridColumn84";
            this.gridColumn84.Visible = true;
            this.gridColumn84.VisibleIndex = 29;
            // 
            // gridColumn85
            // 
            this.gridColumn85.Caption = "所属道路";
            this.gridColumn85.FieldName = "Strroad";
            this.gridColumn85.Name = "gridColumn85";
            this.gridColumn85.Visible = true;
            this.gridColumn85.VisibleIndex = 31;
            // 
            // gridColumn86
            // 
            this.gridColumn86.Caption = "经度";
            this.gridColumn86.FieldName = "Imlongitude";
            this.gridColumn86.Name = "gridColumn86";
            // 
            // gridColumn87
            // 
            this.gridColumn87.Caption = "纬度";
            this.gridColumn87.FieldName = "Imlatitude";
            this.gridColumn87.Name = "gridColumn87";
            // 
            // gridColumn88
            // 
            this.gridColumn88.Caption = "ifileid";
            this.gridColumn88.FieldName = "Ifileid";
            this.gridColumn88.Name = "gridColumn88";
            // 
            // gridColumn89
            // 
            this.gridColumn89.Caption = "istime";
            this.gridColumn89.FieldName = "Istime";
            this.gridColumn89.Name = "gridColumn89";
            // 
            // gridColumn90
            // 
            this.gridColumn90.Caption = "ietime";
            this.gridColumn90.FieldName = "Ietime";
            this.gridColumn90.Name = "gridColumn90";
            // 
            // gridColumn325
            // 
            this.gridColumn325.Caption = "gridColumn325";
            this.gridColumn325.FieldName = "Iid";
            this.gridColumn325.Name = "gridColumn325";
            // 
            // gridView4
            // 
            this.gridView4.GridControl = this.gridControl2;
            this.gridView4.Name = "gridView4";
            // 
            // xtraTabPage4
            // 
            this.xtraTabPage4.Controls.Add(this.gridControl3);
            this.xtraTabPage4.Name = "xtraTabPage4";
            this.xtraTabPage4.Size = new System.Drawing.Size(836, 381);
            this.xtraTabPage4.Text = "DPCH频点干扰";
            // 
            // gridControl3
            // 
            this.gridControl3.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl3.Location = new System.Drawing.Point(0, 0);
            this.gridControl3.MainView = this.bandedGridView3;
            this.gridControl3.Name = "gridControl3";
            this.gridControl3.Size = new System.Drawing.Size(836, 381);
            this.gridControl3.TabIndex = 1;
            this.gridControl3.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView3,
            this.gridView5,
            this.gridView6});
            this.gridControl3.DoubleClick += new System.EventHandler(this.gridControl3_DoubleClick);
            // 
            // bandedGridView3
            // 
            this.bandedGridView3.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand3,
            this.gridBand30,
            this.gridBand31,
            this.gridBand32,
            this.gridBand33,
            this.gridBand34,
            this.gridBand35,
            this.gridBand36,
            this.gridBand37,
            this.gridBand38,
            this.gridBand39,
            this.gridBand40,
            this.gridBand41});
            this.bandedGridView3.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn129,
            this.bandedGridColumn130,
            this.bandedGridColumn131,
            this.bandedGridColumn132,
            this.bandedGridColumn133,
            this.bandedGridColumn134,
            this.bandedGridColumn135,
            this.bandedGridColumn136,
            this.bandedGridColumn137,
            this.bandedGridColumn138,
            this.bandedGridColumn139,
            this.bandedGridColumn140,
            this.bandedGridColumn141,
            this.bandedGridColumn142,
            this.bandedGridColumn143,
            this.bandedGridColumn144,
            this.bandedGridColumn145,
            this.bandedGridColumn146,
            this.bandedGridColumn147,
            this.bandedGridColumn148,
            this.bandedGridColumn149,
            this.bandedGridColumn150,
            this.bandedGridColumn151,
            this.bandedGridColumn152,
            this.bandedGridColumn153,
            this.bandedGridColumn154,
            this.bandedGridColumn155,
            this.bandedGridColumn156,
            this.bandedGridColumn157,
            this.bandedGridColumn158,
            this.bandedGridColumn159,
            this.bandedGridColumn160,
            this.bandedGridColumn161,
            this.bandedGridColumn162,
            this.bandedGridColumn163,
            this.bandedGridColumn164,
            this.bandedGridColumn165,
            this.bandedGridColumn166,
            this.bandedGridColumn167,
            this.bandedGridColumn168,
            this.bandedGridColumn169,
            this.bandedGridColumn170,
            this.bandedGridColumn171,
            this.bandedGridColumn172,
            this.bandedGridColumn173,
            this.bandedGridColumn174,
            this.bandedGridColumn175,
            this.bandedGridColumn176,
            this.bandedGridColumn177,
            this.bandedGridColumn178,
            this.bandedGridColumn179,
            this.bandedGridColumn180,
            this.bandedGridColumn181,
            this.bandedGridColumn182,
            this.bandedGridColumn183,
            this.bandedGridColumn184,
            this.bandedGridColumn185,
            this.bandedGridColumn186,
            this.bandedGridColumn187,
            this.bandedGridColumn188,
            this.bandedGridColumn189,
            this.bandedGridColumn190,
            this.bandedGridColumn191,
            this.bandedGridColumn192});
            this.bandedGridView3.GridControl = this.gridControl3;
            this.bandedGridView3.Name = "bandedGridView3";
            this.bandedGridView3.OptionsBehavior.Editable = false;
            this.bandedGridView3.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.bandedGridView3.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView3.OptionsView.ShowGroupPanel = false;
            // 
            // gridView5
            // 
            this.gridView5.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn91,
            this.gridColumn92,
            this.gridColumn93,
            this.gridColumn94,
            this.gridColumn95,
            this.gridColumn96,
            this.gridColumn97,
            this.gridColumn98,
            this.gridColumn99,
            this.gridColumn100,
            this.gridColumn101,
            this.gridColumn102,
            this.gridColumn103,
            this.gridColumn104,
            this.gridColumn105,
            this.gridColumn106,
            this.gridColumn107,
            this.gridColumn108,
            this.gridColumn114,
            this.gridColumn115,
            this.gridColumn116,
            this.gridColumn117,
            this.gridColumn118,
            this.gridColumn119,
            this.gridColumn120,
            this.gridColumn121,
            this.gridColumn122,
            this.gridColumn123,
            this.gridColumn124,
            this.gridColumn125,
            this.gridColumn126,
            this.gridColumn127,
            this.gridColumn128,
            this.gridColumn138,
            this.gridColumn139,
            this.gridColumn140,
            this.gridColumn171,
            this.gridColumn172,
            this.gridColumn173,
            this.gridColumn174,
            this.gridColumn175,
            this.gridColumn176,
            this.gridColumn177,
            this.gridColumn178,
            this.gridColumn179,
            this.gridColumn180,
            this.gridColumn181,
            this.gridColumn182,
            this.gridColumn183,
            this.gridColumn184,
            this.gridColumn185,
            this.gridColumn186,
            this.gridColumn187,
            this.gridColumn188,
            this.gridColumn189,
            this.gridColumn190,
            this.gridColumn191,
            this.gridColumn192,
            this.gridColumn193,
            this.gridColumn194,
            this.gridColumn195,
            this.gridColumn196,
            this.gridColumn197,
            this.gridColumn326});
            this.gridView5.GridControl = this.gridControl3;
            this.gridView5.Name = "gridView5";
            this.gridView5.OptionsBehavior.Editable = false;
            this.gridView5.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView5.OptionsView.ColumnAutoWidth = false;
            this.gridView5.OptionsView.ShowGroupPanel = false;
            this.gridView5.DoubleClick += new System.EventHandler(this.gridControl3_DoubleClick);
            // 
            // gridColumn91
            // 
            this.gridColumn91.Caption = "距离(米)";
            this.gridColumn91.FieldName = "Idistance";
            this.gridColumn91.Name = "gridColumn91";
            this.gridColumn91.Visible = true;
            this.gridColumn91.VisibleIndex = 0;
            // 
            // gridColumn92
            // 
            this.gridColumn92.Caption = "时长(秒)";
            this.gridColumn92.FieldName = "Iduration";
            this.gridColumn92.Name = "gridColumn92";
            this.gridColumn92.Visible = true;
            this.gridColumn92.VisibleIndex = 1;
            // 
            // gridColumn93
            // 
            this.gridColumn93.Caption = "下载速率0";
            this.gridColumn93.FieldName = "APP_Speed0";
            this.gridColumn93.Name = "gridColumn93";
            this.gridColumn93.Visible = true;
            this.gridColumn93.VisibleIndex = 2;
            // 
            // gridColumn94
            // 
            this.gridColumn94.Caption = "下载速率[0,500]";
            this.gridColumn94.FieldName = "APP_Speed0_500";
            this.gridColumn94.Name = "gridColumn94";
            this.gridColumn94.Visible = true;
            this.gridColumn94.VisibleIndex = 3;
            // 
            // gridColumn95
            // 
            this.gridColumn95.Caption = "下载速率[500,800]";
            this.gridColumn95.FieldName = "APP_Speed500_800";
            this.gridColumn95.Name = "gridColumn95";
            this.gridColumn95.Visible = true;
            this.gridColumn95.VisibleIndex = 4;
            // 
            // gridColumn96
            // 
            this.gridColumn96.Caption = "下载速率[800,900]";
            this.gridColumn96.FieldName = "APP_Speed800_900";
            this.gridColumn96.Name = "gridColumn96";
            this.gridColumn96.Visible = true;
            this.gridColumn96.VisibleIndex = 5;
            // 
            // gridColumn97
            // 
            this.gridColumn97.Caption = "下载速率[900,1100]";
            this.gridColumn97.FieldName = "APP_Speed900_1100";
            this.gridColumn97.Name = "gridColumn97";
            this.gridColumn97.Visible = true;
            this.gridColumn97.VisibleIndex = 6;
            // 
            // gridColumn98
            // 
            this.gridColumn98.Caption = "下载速率1100";
            this.gridColumn98.FieldName = "APP_Speed1100";
            this.gridColumn98.Name = "gridColumn98";
            this.gridColumn98.Visible = true;
            this.gridColumn98.VisibleIndex = 7;
            // 
            // gridColumn99
            // 
            this.gridColumn99.Caption = "TD电平均值";
            this.gridColumn99.FieldName = "Rscpmean";
            this.gridColumn99.Name = "gridColumn99";
            this.gridColumn99.Visible = true;
            this.gridColumn99.VisibleIndex = 8;
            // 
            // gridColumn100
            // 
            this.gridColumn100.Caption = "TD电平小于75";
            this.gridColumn100.FieldName = "Rscp75";
            this.gridColumn100.Name = "gridColumn100";
            this.gridColumn100.Visible = true;
            this.gridColumn100.VisibleIndex = 9;
            // 
            // gridColumn101
            // 
            this.gridColumn101.Caption = "TD电平[76,80]";
            this.gridColumn101.FieldName = "Rscp76_80";
            this.gridColumn101.Name = "gridColumn101";
            this.gridColumn101.Visible = true;
            this.gridColumn101.VisibleIndex = 10;
            // 
            // gridColumn102
            // 
            this.gridColumn102.Caption = "TD电平[81,85]";
            this.gridColumn102.FieldName = "Rscp81_85";
            this.gridColumn102.Name = "gridColumn102";
            this.gridColumn102.Visible = true;
            this.gridColumn102.VisibleIndex = 11;
            // 
            // gridColumn103
            // 
            this.gridColumn103.Caption = "TD电平[86,90]";
            this.gridColumn103.FieldName = "Rscp86_90";
            this.gridColumn103.Name = "gridColumn103";
            this.gridColumn103.Visible = true;
            this.gridColumn103.VisibleIndex = 13;
            // 
            // gridColumn104
            // 
            this.gridColumn104.Caption = "TD电平[91,94]";
            this.gridColumn104.FieldName = "Rscp91_94";
            this.gridColumn104.Name = "gridColumn104";
            this.gridColumn104.Visible = true;
            this.gridColumn104.VisibleIndex = 12;
            // 
            // gridColumn105
            // 
            this.gridColumn105.Caption = "TD电平大于94";
            this.gridColumn105.FieldName = "Rscp94";
            this.gridColumn105.Name = "gridColumn105";
            this.gridColumn105.Visible = true;
            this.gridColumn105.VisibleIndex = 14;
            // 
            // gridColumn106
            // 
            this.gridColumn106.Caption = "GSM电平均值";
            this.gridColumn106.FieldName = "Rxlmean";
            this.gridColumn106.Name = "gridColumn106";
            this.gridColumn106.Visible = true;
            this.gridColumn106.VisibleIndex = 15;
            // 
            // gridColumn107
            // 
            this.gridColumn107.Caption = "GSM电平小于75";
            this.gridColumn107.FieldName = "Rxl75";
            this.gridColumn107.Name = "gridColumn107";
            this.gridColumn107.Visible = true;
            this.gridColumn107.VisibleIndex = 16;
            // 
            // gridColumn108
            // 
            this.gridColumn108.Caption = "GSM电平[76,80]";
            this.gridColumn108.FieldName = "Rxl76_80";
            this.gridColumn108.Name = "gridColumn108";
            this.gridColumn108.Visible = true;
            this.gridColumn108.VisibleIndex = 17;
            // 
            // gridColumn114
            // 
            this.gridColumn114.Caption = "GSM电平[81,85]";
            this.gridColumn114.FieldName = "Rxl81_85";
            this.gridColumn114.Name = "gridColumn114";
            this.gridColumn114.Visible = true;
            this.gridColumn114.VisibleIndex = 18;
            // 
            // gridColumn115
            // 
            this.gridColumn115.Caption = "GSM电平[86,90]";
            this.gridColumn115.FieldName = "Rxl86_90";
            this.gridColumn115.Name = "gridColumn115";
            this.gridColumn115.Visible = true;
            this.gridColumn115.VisibleIndex = 19;
            // 
            // gridColumn116
            // 
            this.gridColumn116.Caption = "GSM电平[91,94]";
            this.gridColumn116.FieldName = "Rxl91_94";
            this.gridColumn116.Name = "gridColumn116";
            this.gridColumn116.Visible = true;
            this.gridColumn116.VisibleIndex = 20;
            // 
            // gridColumn117
            // 
            this.gridColumn117.Caption = "GSM电平大于94";
            this.gridColumn117.FieldName = "Rxl94";
            this.gridColumn117.Name = "gridColumn117";
            this.gridColumn117.Visible = true;
            this.gridColumn117.VisibleIndex = 21;
            // 
            // gridColumn118
            // 
            this.gridColumn118.Caption = "DPCH C/I均值";
            this.gridColumn118.FieldName = "Dc2imean";
            this.gridColumn118.Name = "gridColumn118";
            this.gridColumn118.Visible = true;
            this.gridColumn118.VisibleIndex = 22;
            // 
            // gridColumn119
            // 
            this.gridColumn119.Caption = "DPCH C/I小于-10";
            this.gridColumn119.FieldName = "Dc2iF10";
            this.gridColumn119.Name = "gridColumn119";
            this.gridColumn119.Visible = true;
            this.gridColumn119.VisibleIndex = 23;
            // 
            // gridColumn120
            // 
            this.gridColumn120.Caption = "DPCH C/I[-10,-3]";
            this.gridColumn120.FieldName = "Dc2iF10TF3";
            this.gridColumn120.Name = "gridColumn120";
            this.gridColumn120.Visible = true;
            this.gridColumn120.VisibleIndex = 24;
            // 
            // gridColumn121
            // 
            this.gridColumn121.Caption = "DPCH C/I[-3,15]";
            this.gridColumn121.FieldName = "Dc2iF3T15";
            this.gridColumn121.Name = "gridColumn121";
            this.gridColumn121.Visible = true;
            this.gridColumn121.VisibleIndex = 26;
            // 
            // gridColumn122
            // 
            this.gridColumn122.Caption = "DPCH C/I大于15";
            this.gridColumn122.FieldName = "Dc2i15";
            this.gridColumn122.Name = "gridColumn122";
            this.gridColumn122.Visible = true;
            this.gridColumn122.VisibleIndex = 28;
            // 
            // gridColumn123
            // 
            this.gridColumn123.Caption = "TxPower均值";
            this.gridColumn123.FieldName = "Txpowermean";
            this.gridColumn123.Name = "gridColumn123";
            this.gridColumn123.Visible = true;
            this.gridColumn123.VisibleIndex = 30;
            // 
            // gridColumn124
            // 
            this.gridColumn124.Caption = "TxPower小于-20";
            this.gridColumn124.FieldName = "TxpowerF20";
            this.gridColumn124.Name = "gridColumn124";
            this.gridColumn124.Visible = true;
            this.gridColumn124.VisibleIndex = 32;
            // 
            // gridColumn125
            // 
            this.gridColumn125.Caption = "TxPower[-20,0]";
            this.gridColumn125.FieldName = "TxpowerF20T0";
            this.gridColumn125.Name = "gridColumn125";
            this.gridColumn125.Visible = true;
            this.gridColumn125.VisibleIndex = 33;
            // 
            // gridColumn126
            // 
            this.gridColumn126.Caption = "TxPower[0,15]";
            this.gridColumn126.FieldName = "Txpower0T15";
            this.gridColumn126.Name = "gridColumn126";
            this.gridColumn126.Visible = true;
            this.gridColumn126.VisibleIndex = 34;
            // 
            // gridColumn127
            // 
            this.gridColumn127.Caption = "TxPower大于15";
            this.gridColumn127.FieldName = "Txpower15";
            this.gridColumn127.Name = "gridColumn127";
            this.gridColumn127.Visible = true;
            this.gridColumn127.VisibleIndex = 35;
            // 
            // gridColumn128
            // 
            this.gridColumn128.Caption = "T至G切换成功次数";
            this.gridColumn128.FieldName = "HO_SUCC_T2G";
            this.gridColumn128.Name = "gridColumn128";
            this.gridColumn128.Visible = true;
            this.gridColumn128.VisibleIndex = 36;
            // 
            // gridColumn138
            // 
            this.gridColumn138.Caption = "T至G切换失败次数";
            this.gridColumn138.FieldName = "HO_FAIL_T2G";
            this.gridColumn138.Name = "gridColumn138";
            this.gridColumn138.Visible = true;
            this.gridColumn138.VisibleIndex = 37;
            // 
            // gridColumn139
            // 
            this.gridColumn139.Caption = "T至G切换时延";
            this.gridColumn139.FieldName = "HO_Time_T2G";
            this.gridColumn139.Name = "gridColumn139";
            this.gridColumn139.Visible = true;
            this.gridColumn139.VisibleIndex = 39;
            // 
            // gridColumn140
            // 
            this.gridColumn140.Caption = "T网硬切换成功次数";
            this.gridColumn140.FieldName = "HO_SUCC_T2T";
            this.gridColumn140.Name = "gridColumn140";
            this.gridColumn140.Visible = true;
            this.gridColumn140.VisibleIndex = 40;
            // 
            // gridColumn171
            // 
            this.gridColumn171.Caption = "T网硬切换失败次数";
            this.gridColumn171.FieldName = "HO_FAIL_T2T";
            this.gridColumn171.Name = "gridColumn171";
            this.gridColumn171.Visible = true;
            this.gridColumn171.VisibleIndex = 41;
            // 
            // gridColumn172
            // 
            this.gridColumn172.Caption = "T网硬切换时延";
            this.gridColumn172.FieldName = "HO_Time_T2T";
            this.gridColumn172.Name = "gridColumn172";
            this.gridColumn172.Visible = true;
            this.gridColumn172.VisibleIndex = 42;
            // 
            // gridColumn173
            // 
            this.gridColumn173.Caption = "T网接力切换成功次数";
            this.gridColumn173.FieldName = "HO_SUCC_Baton";
            this.gridColumn173.Name = "gridColumn173";
            this.gridColumn173.Visible = true;
            this.gridColumn173.VisibleIndex = 43;
            // 
            // gridColumn174
            // 
            this.gridColumn174.Caption = "T网接力切换失败次数";
            this.gridColumn174.FieldName = "HO_FAIL_Baton";
            this.gridColumn174.Name = "gridColumn174";
            this.gridColumn174.Visible = true;
            this.gridColumn174.VisibleIndex = 44;
            // 
            // gridColumn175
            // 
            this.gridColumn175.Caption = "T网接力切换时延";
            this.gridColumn175.FieldName = "HO_Time_Baton";
            this.gridColumn175.Name = "gridColumn175";
            this.gridColumn175.Visible = true;
            this.gridColumn175.VisibleIndex = 45;
            // 
            // gridColumn176
            // 
            this.gridColumn176.Caption = "T网重选成功次数";
            this.gridColumn176.FieldName = "CR_SUCC_T2T";
            this.gridColumn176.Name = "gridColumn176";
            this.gridColumn176.Visible = true;
            this.gridColumn176.VisibleIndex = 46;
            // 
            // gridColumn177
            // 
            this.gridColumn177.Caption = "T网重选失败次数";
            this.gridColumn177.FieldName = "CR_FAIL_T2T";
            this.gridColumn177.Name = "gridColumn177";
            this.gridColumn177.Visible = true;
            this.gridColumn177.VisibleIndex = 47;
            // 
            // gridColumn178
            // 
            this.gridColumn178.Caption = "T至G重选成功次数";
            this.gridColumn178.FieldName = "CR_SUCC_T2G";
            this.gridColumn178.Name = "gridColumn178";
            this.gridColumn178.Visible = true;
            this.gridColumn178.VisibleIndex = 48;
            // 
            // gridColumn179
            // 
            this.gridColumn179.Caption = "T至G重选失败次数";
            this.gridColumn179.FieldName = "CR_FAIL_T2G";
            this.gridColumn179.Name = "gridColumn179";
            this.gridColumn179.Visible = true;
            this.gridColumn179.VisibleIndex = 49;
            // 
            // gridColumn180
            // 
            this.gridColumn180.Caption = "G至T重选成功次数";
            this.gridColumn180.FieldName = "CR_SUCC_G2T";
            this.gridColumn180.Name = "gridColumn180";
            this.gridColumn180.Visible = true;
            this.gridColumn180.VisibleIndex = 50;
            // 
            // gridColumn181
            // 
            this.gridColumn181.Caption = "G至T重选失败次数";
            this.gridColumn181.FieldName = "CR_FAIL_G2T";
            this.gridColumn181.Name = "gridColumn181";
            this.gridColumn181.Visible = true;
            this.gridColumn181.VisibleIndex = 51;
            // 
            // gridColumn182
            // 
            this.gridColumn182.Caption = "Hsdpa下载量占比";
            this.gridColumn182.FieldName = "HSDPA_Size";
            this.gridColumn182.Name = "gridColumn182";
            this.gridColumn182.Visible = true;
            this.gridColumn182.VisibleIndex = 52;
            // 
            // gridColumn183
            // 
            this.gridColumn183.Caption = "Hsdpa时长占比";
            this.gridColumn183.FieldName = "HSDPA_Time";
            this.gridColumn183.Name = "gridColumn183";
            this.gridColumn183.Visible = true;
            this.gridColumn183.VisibleIndex = 53;
            // 
            // gridColumn184
            // 
            this.gridColumn184.Caption = "R4下载量占比";
            this.gridColumn184.FieldName = "R4_Size";
            this.gridColumn184.Name = "gridColumn184";
            this.gridColumn184.Visible = true;
            this.gridColumn184.VisibleIndex = 54;
            // 
            // gridColumn185
            // 
            this.gridColumn185.Caption = "R4时长占比";
            this.gridColumn185.FieldName = "R4_Time";
            this.gridColumn185.Name = "gridColumn185";
            this.gridColumn185.Visible = true;
            this.gridColumn185.VisibleIndex = 55;
            // 
            // gridColumn186
            // 
            this.gridColumn186.Caption = "Gsm下载量占比";
            this.gridColumn186.FieldName = "EDGE_Size";
            this.gridColumn186.Name = "gridColumn186";
            this.gridColumn186.Visible = true;
            this.gridColumn186.VisibleIndex = 56;
            // 
            // gridColumn187
            // 
            this.gridColumn187.Caption = "Gsm时长占比";
            this.gridColumn187.FieldName = "EDGE_Time";
            this.gridColumn187.Name = "gridColumn187";
            this.gridColumn187.Visible = true;
            this.gridColumn187.VisibleIndex = 57;
            // 
            // gridColumn188
            // 
            this.gridColumn188.Caption = "小区1";
            this.gridColumn188.FieldName = "Cell1";
            this.gridColumn188.Name = "gridColumn188";
            this.gridColumn188.Visible = true;
            this.gridColumn188.VisibleIndex = 38;
            // 
            // gridColumn189
            // 
            this.gridColumn189.Caption = "小区2";
            this.gridColumn189.FieldName = "Cell2";
            this.gridColumn189.Name = "gridColumn189";
            this.gridColumn189.Visible = true;
            this.gridColumn189.VisibleIndex = 25;
            // 
            // gridColumn190
            // 
            this.gridColumn190.Caption = "小区3";
            this.gridColumn190.FieldName = "Cell3";
            this.gridColumn190.Name = "gridColumn190";
            this.gridColumn190.Visible = true;
            this.gridColumn190.VisibleIndex = 27;
            // 
            // gridColumn191
            // 
            this.gridColumn191.Caption = "所属网格";
            this.gridColumn191.FieldName = "Strgrid";
            this.gridColumn191.Name = "gridColumn191";
            this.gridColumn191.Visible = true;
            this.gridColumn191.VisibleIndex = 29;
            // 
            // gridColumn192
            // 
            this.gridColumn192.Caption = "所属道路";
            this.gridColumn192.FieldName = "Strroad";
            this.gridColumn192.Name = "gridColumn192";
            this.gridColumn192.Visible = true;
            this.gridColumn192.VisibleIndex = 31;
            // 
            // gridColumn193
            // 
            this.gridColumn193.Caption = "经度";
            this.gridColumn193.FieldName = "Imlongitude";
            this.gridColumn193.Name = "gridColumn193";
            // 
            // gridColumn194
            // 
            this.gridColumn194.Caption = "纬度";
            this.gridColumn194.FieldName = "Imlatitude";
            this.gridColumn194.Name = "gridColumn194";
            // 
            // gridColumn195
            // 
            this.gridColumn195.Caption = "ifileid";
            this.gridColumn195.FieldName = "Ifileid";
            this.gridColumn195.Name = "gridColumn195";
            // 
            // gridColumn196
            // 
            this.gridColumn196.Caption = "istime";
            this.gridColumn196.FieldName = "Istime";
            this.gridColumn196.Name = "gridColumn196";
            // 
            // gridColumn197
            // 
            this.gridColumn197.Caption = "ietime";
            this.gridColumn197.FieldName = "Ietime";
            this.gridColumn197.Name = "gridColumn197";
            // 
            // gridColumn326
            // 
            this.gridColumn326.Caption = "gridColumn326";
            this.gridColumn326.FieldName = "Iid";
            this.gridColumn326.Name = "gridColumn326";
            // 
            // gridView6
            // 
            this.gridView6.GridControl = this.gridControl3;
            this.gridView6.Name = "gridView6";
            // 
            // xtraTabPage5
            // 
            this.xtraTabPage5.Controls.Add(this.gridControl4);
            this.xtraTabPage5.Name = "xtraTabPage5";
            this.xtraTabPage5.Size = new System.Drawing.Size(836, 381);
            this.xtraTabPage5.Text = "H信道拥塞";
            // 
            // gridControl4
            // 
            this.gridControl4.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl4.Location = new System.Drawing.Point(0, 0);
            this.gridControl4.MainView = this.bandedGridView4;
            this.gridControl4.Name = "gridControl4";
            this.gridControl4.Size = new System.Drawing.Size(836, 381);
            this.gridControl4.TabIndex = 1;
            this.gridControl4.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView4,
            this.gridView7,
            this.gridView8});
            this.gridControl4.DoubleClick += new System.EventHandler(this.gridControl4_DoubleClick);
            // 
            // bandedGridView4
            // 
            this.bandedGridView4.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand4,
            this.gridBand42,
            this.gridBand43,
            this.gridBand44,
            this.gridBand45,
            this.gridBand46,
            this.gridBand47,
            this.gridBand48,
            this.gridBand49,
            this.gridBand50,
            this.gridBand51,
            this.gridBand52,
            this.gridBand53});
            this.bandedGridView4.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn193,
            this.bandedGridColumn194,
            this.bandedGridColumn195,
            this.bandedGridColumn196,
            this.bandedGridColumn197,
            this.bandedGridColumn198,
            this.bandedGridColumn199,
            this.bandedGridColumn200,
            this.bandedGridColumn201,
            this.bandedGridColumn202,
            this.bandedGridColumn203,
            this.bandedGridColumn204,
            this.bandedGridColumn205,
            this.bandedGridColumn206,
            this.bandedGridColumn207,
            this.bandedGridColumn208,
            this.bandedGridColumn209,
            this.bandedGridColumn210,
            this.bandedGridColumn211,
            this.bandedGridColumn212,
            this.bandedGridColumn213,
            this.bandedGridColumn214,
            this.bandedGridColumn215,
            this.bandedGridColumn216,
            this.bandedGridColumn217,
            this.bandedGridColumn218,
            this.bandedGridColumn219,
            this.bandedGridColumn220,
            this.bandedGridColumn221,
            this.bandedGridColumn222,
            this.bandedGridColumn223,
            this.bandedGridColumn224,
            this.bandedGridColumn225,
            this.bandedGridColumn226,
            this.bandedGridColumn227,
            this.bandedGridColumn228,
            this.bandedGridColumn229,
            this.bandedGridColumn230,
            this.bandedGridColumn231,
            this.bandedGridColumn232,
            this.bandedGridColumn233,
            this.bandedGridColumn234,
            this.bandedGridColumn235,
            this.bandedGridColumn236,
            this.bandedGridColumn237,
            this.bandedGridColumn238,
            this.bandedGridColumn239,
            this.bandedGridColumn240,
            this.bandedGridColumn241,
            this.bandedGridColumn242,
            this.bandedGridColumn243,
            this.bandedGridColumn244,
            this.bandedGridColumn245,
            this.bandedGridColumn246,
            this.bandedGridColumn247,
            this.bandedGridColumn248,
            this.bandedGridColumn249,
            this.bandedGridColumn250,
            this.bandedGridColumn251,
            this.bandedGridColumn252,
            this.bandedGridColumn253,
            this.bandedGridColumn254,
            this.bandedGridColumn255,
            this.bandedGridColumn256});
            this.bandedGridView4.GridControl = this.gridControl4;
            this.bandedGridView4.Name = "bandedGridView4";
            this.bandedGridView4.OptionsBehavior.Editable = false;
            this.bandedGridView4.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.bandedGridView4.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView4.OptionsView.ShowGroupPanel = false;
            // 
            // gridView7
            // 
            this.gridView7.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn198,
            this.gridColumn199,
            this.gridColumn200,
            this.gridColumn201,
            this.gridColumn202,
            this.gridColumn203,
            this.gridColumn204,
            this.gridColumn205,
            this.gridColumn206,
            this.gridColumn207,
            this.gridColumn208,
            this.gridColumn209,
            this.gridColumn210,
            this.gridColumn211,
            this.gridColumn212,
            this.gridColumn213,
            this.gridColumn214,
            this.gridColumn215,
            this.gridColumn216,
            this.gridColumn217,
            this.gridColumn218,
            this.gridColumn219,
            this.gridColumn220,
            this.gridColumn221,
            this.gridColumn222,
            this.gridColumn223,
            this.gridColumn224,
            this.gridColumn225,
            this.gridColumn226,
            this.gridColumn227,
            this.gridColumn228,
            this.gridColumn229,
            this.gridColumn230,
            this.gridColumn231,
            this.gridColumn232,
            this.gridColumn233,
            this.gridColumn234,
            this.gridColumn235,
            this.gridColumn236,
            this.gridColumn237,
            this.gridColumn238,
            this.gridColumn239,
            this.gridColumn240,
            this.gridColumn241,
            this.gridColumn242,
            this.gridColumn243,
            this.gridColumn244,
            this.gridColumn245,
            this.gridColumn246,
            this.gridColumn247,
            this.gridColumn248,
            this.gridColumn249,
            this.gridColumn250,
            this.gridColumn251,
            this.gridColumn252,
            this.gridColumn253,
            this.gridColumn254,
            this.gridColumn255,
            this.gridColumn256,
            this.gridColumn257,
            this.gridColumn258,
            this.gridColumn259,
            this.gridColumn260,
            this.gridColumn327});
            this.gridView7.GridControl = this.gridControl4;
            this.gridView7.Name = "gridView7";
            this.gridView7.OptionsBehavior.Editable = false;
            this.gridView7.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView7.OptionsView.ColumnAutoWidth = false;
            this.gridView7.OptionsView.ShowGroupPanel = false;
            this.gridView7.DoubleClick += new System.EventHandler(this.gridControl4_DoubleClick);
            // 
            // gridColumn198
            // 
            this.gridColumn198.Caption = "距离(米)";
            this.gridColumn198.FieldName = "Idistance";
            this.gridColumn198.Name = "gridColumn198";
            this.gridColumn198.Visible = true;
            this.gridColumn198.VisibleIndex = 0;
            // 
            // gridColumn199
            // 
            this.gridColumn199.Caption = "时长(秒)";
            this.gridColumn199.FieldName = "Iduration";
            this.gridColumn199.Name = "gridColumn199";
            this.gridColumn199.Visible = true;
            this.gridColumn199.VisibleIndex = 1;
            // 
            // gridColumn200
            // 
            this.gridColumn200.Caption = "下载速率0";
            this.gridColumn200.FieldName = "APP_Speed0";
            this.gridColumn200.Name = "gridColumn200";
            this.gridColumn200.Visible = true;
            this.gridColumn200.VisibleIndex = 2;
            // 
            // gridColumn201
            // 
            this.gridColumn201.Caption = "下载速率[0,500]";
            this.gridColumn201.FieldName = "APP_Speed0_500";
            this.gridColumn201.Name = "gridColumn201";
            this.gridColumn201.Visible = true;
            this.gridColumn201.VisibleIndex = 3;
            // 
            // gridColumn202
            // 
            this.gridColumn202.Caption = "下载速率[500,800]";
            this.gridColumn202.FieldName = "APP_Speed500_800";
            this.gridColumn202.Name = "gridColumn202";
            this.gridColumn202.Visible = true;
            this.gridColumn202.VisibleIndex = 4;
            // 
            // gridColumn203
            // 
            this.gridColumn203.Caption = "下载速率[800,900]";
            this.gridColumn203.FieldName = "APP_Speed800_900";
            this.gridColumn203.Name = "gridColumn203";
            this.gridColumn203.Visible = true;
            this.gridColumn203.VisibleIndex = 5;
            // 
            // gridColumn204
            // 
            this.gridColumn204.Caption = "下载速率[900,1100]";
            this.gridColumn204.FieldName = "APP_Speed900_1100";
            this.gridColumn204.Name = "gridColumn204";
            this.gridColumn204.Visible = true;
            this.gridColumn204.VisibleIndex = 6;
            // 
            // gridColumn205
            // 
            this.gridColumn205.Caption = "下载速率1100";
            this.gridColumn205.FieldName = "APP_Speed1100";
            this.gridColumn205.Name = "gridColumn205";
            this.gridColumn205.Visible = true;
            this.gridColumn205.VisibleIndex = 7;
            // 
            // gridColumn206
            // 
            this.gridColumn206.Caption = "TD电平均值";
            this.gridColumn206.FieldName = "Rscpmean";
            this.gridColumn206.Name = "gridColumn206";
            this.gridColumn206.Visible = true;
            this.gridColumn206.VisibleIndex = 8;
            // 
            // gridColumn207
            // 
            this.gridColumn207.Caption = "TD电平小于75";
            this.gridColumn207.FieldName = "Rscp75";
            this.gridColumn207.Name = "gridColumn207";
            this.gridColumn207.Visible = true;
            this.gridColumn207.VisibleIndex = 9;
            // 
            // gridColumn208
            // 
            this.gridColumn208.Caption = "TD电平[76,80]";
            this.gridColumn208.FieldName = "Rscp76_80";
            this.gridColumn208.Name = "gridColumn208";
            this.gridColumn208.Visible = true;
            this.gridColumn208.VisibleIndex = 10;
            // 
            // gridColumn209
            // 
            this.gridColumn209.Caption = "TD电平[81,85]";
            this.gridColumn209.FieldName = "Rscp81_85";
            this.gridColumn209.Name = "gridColumn209";
            this.gridColumn209.Visible = true;
            this.gridColumn209.VisibleIndex = 11;
            // 
            // gridColumn210
            // 
            this.gridColumn210.Caption = "TD电平[86,90]";
            this.gridColumn210.FieldName = "Rscp86_90";
            this.gridColumn210.Name = "gridColumn210";
            this.gridColumn210.Visible = true;
            this.gridColumn210.VisibleIndex = 13;
            // 
            // gridColumn211
            // 
            this.gridColumn211.Caption = "TD电平[91,94]";
            this.gridColumn211.FieldName = "Rscp91_94";
            this.gridColumn211.Name = "gridColumn211";
            this.gridColumn211.Visible = true;
            this.gridColumn211.VisibleIndex = 12;
            // 
            // gridColumn212
            // 
            this.gridColumn212.Caption = "TD电平大于94";
            this.gridColumn212.FieldName = "Rscp94";
            this.gridColumn212.Name = "gridColumn212";
            this.gridColumn212.Visible = true;
            this.gridColumn212.VisibleIndex = 14;
            // 
            // gridColumn213
            // 
            this.gridColumn213.Caption = "GSM电平均值";
            this.gridColumn213.FieldName = "Rxlmean";
            this.gridColumn213.Name = "gridColumn213";
            this.gridColumn213.Visible = true;
            this.gridColumn213.VisibleIndex = 15;
            // 
            // gridColumn214
            // 
            this.gridColumn214.Caption = "GSM电平小于75";
            this.gridColumn214.FieldName = "Rxl75";
            this.gridColumn214.Name = "gridColumn214";
            this.gridColumn214.Visible = true;
            this.gridColumn214.VisibleIndex = 16;
            // 
            // gridColumn215
            // 
            this.gridColumn215.Caption = "GSM电平[76,80]";
            this.gridColumn215.FieldName = "Rxl76_80";
            this.gridColumn215.Name = "gridColumn215";
            this.gridColumn215.Visible = true;
            this.gridColumn215.VisibleIndex = 17;
            // 
            // gridColumn216
            // 
            this.gridColumn216.Caption = "GSM电平[81,85]";
            this.gridColumn216.FieldName = "Rxl81_85";
            this.gridColumn216.Name = "gridColumn216";
            this.gridColumn216.Visible = true;
            this.gridColumn216.VisibleIndex = 18;
            // 
            // gridColumn217
            // 
            this.gridColumn217.Caption = "GSM电平[86,90]";
            this.gridColumn217.FieldName = "Rxl86_90";
            this.gridColumn217.Name = "gridColumn217";
            this.gridColumn217.Visible = true;
            this.gridColumn217.VisibleIndex = 19;
            // 
            // gridColumn218
            // 
            this.gridColumn218.Caption = "GSM电平[91,94]";
            this.gridColumn218.FieldName = "Rxl91_94";
            this.gridColumn218.Name = "gridColumn218";
            this.gridColumn218.Visible = true;
            this.gridColumn218.VisibleIndex = 20;
            // 
            // gridColumn219
            // 
            this.gridColumn219.Caption = "GSM电平大于94";
            this.gridColumn219.FieldName = "Rxl94";
            this.gridColumn219.Name = "gridColumn219";
            this.gridColumn219.Visible = true;
            this.gridColumn219.VisibleIndex = 21;
            // 
            // gridColumn220
            // 
            this.gridColumn220.Caption = "DPCH C/I均值";
            this.gridColumn220.FieldName = "Dc2imean";
            this.gridColumn220.Name = "gridColumn220";
            this.gridColumn220.Visible = true;
            this.gridColumn220.VisibleIndex = 22;
            // 
            // gridColumn221
            // 
            this.gridColumn221.Caption = "DPCH C/I小于-10";
            this.gridColumn221.FieldName = "Dc2iF10";
            this.gridColumn221.Name = "gridColumn221";
            this.gridColumn221.Visible = true;
            this.gridColumn221.VisibleIndex = 23;
            // 
            // gridColumn222
            // 
            this.gridColumn222.Caption = "DPCH C/I[-10,-3]";
            this.gridColumn222.FieldName = "Dc2iF10TF3";
            this.gridColumn222.Name = "gridColumn222";
            this.gridColumn222.Visible = true;
            this.gridColumn222.VisibleIndex = 24;
            // 
            // gridColumn223
            // 
            this.gridColumn223.Caption = "DPCH C/I[-3,15]";
            this.gridColumn223.FieldName = "Dc2iF3T15";
            this.gridColumn223.Name = "gridColumn223";
            this.gridColumn223.Visible = true;
            this.gridColumn223.VisibleIndex = 26;
            // 
            // gridColumn224
            // 
            this.gridColumn224.Caption = "DPCH C/I大于15";
            this.gridColumn224.FieldName = "Dc2i15";
            this.gridColumn224.Name = "gridColumn224";
            this.gridColumn224.Visible = true;
            this.gridColumn224.VisibleIndex = 28;
            // 
            // gridColumn225
            // 
            this.gridColumn225.Caption = "TxPower均值";
            this.gridColumn225.FieldName = "Txpowermean";
            this.gridColumn225.Name = "gridColumn225";
            this.gridColumn225.Visible = true;
            this.gridColumn225.VisibleIndex = 30;
            // 
            // gridColumn226
            // 
            this.gridColumn226.Caption = "TxPower小于-20";
            this.gridColumn226.FieldName = "TxpowerF20";
            this.gridColumn226.Name = "gridColumn226";
            this.gridColumn226.Visible = true;
            this.gridColumn226.VisibleIndex = 32;
            // 
            // gridColumn227
            // 
            this.gridColumn227.Caption = "TxPower[-20,0]";
            this.gridColumn227.FieldName = "TxpowerF20T0";
            this.gridColumn227.Name = "gridColumn227";
            this.gridColumn227.Visible = true;
            this.gridColumn227.VisibleIndex = 33;
            // 
            // gridColumn228
            // 
            this.gridColumn228.Caption = "TxPower[0,15]";
            this.gridColumn228.FieldName = "Txpower0T15";
            this.gridColumn228.Name = "gridColumn228";
            this.gridColumn228.Visible = true;
            this.gridColumn228.VisibleIndex = 34;
            // 
            // gridColumn229
            // 
            this.gridColumn229.Caption = "TxPower大于15";
            this.gridColumn229.FieldName = "Txpower15";
            this.gridColumn229.Name = "gridColumn229";
            this.gridColumn229.Visible = true;
            this.gridColumn229.VisibleIndex = 35;
            // 
            // gridColumn230
            // 
            this.gridColumn230.Caption = "T至G切换成功次数";
            this.gridColumn230.FieldName = "HO_SUCC_T2G";
            this.gridColumn230.Name = "gridColumn230";
            this.gridColumn230.Visible = true;
            this.gridColumn230.VisibleIndex = 36;
            // 
            // gridColumn231
            // 
            this.gridColumn231.Caption = "T至G切换失败次数";
            this.gridColumn231.FieldName = "HO_FAIL_T2G";
            this.gridColumn231.Name = "gridColumn231";
            this.gridColumn231.Visible = true;
            this.gridColumn231.VisibleIndex = 37;
            // 
            // gridColumn232
            // 
            this.gridColumn232.Caption = "T至G切换时延";
            this.gridColumn232.FieldName = "HO_Time_T2G";
            this.gridColumn232.Name = "gridColumn232";
            this.gridColumn232.Visible = true;
            this.gridColumn232.VisibleIndex = 39;
            // 
            // gridColumn233
            // 
            this.gridColumn233.Caption = "T网硬切换成功次数";
            this.gridColumn233.FieldName = "HO_SUCC_T2T";
            this.gridColumn233.Name = "gridColumn233";
            this.gridColumn233.Visible = true;
            this.gridColumn233.VisibleIndex = 40;
            // 
            // gridColumn234
            // 
            this.gridColumn234.Caption = "T网硬切换失败次数";
            this.gridColumn234.FieldName = "HO_FAIL_T2T";
            this.gridColumn234.Name = "gridColumn234";
            this.gridColumn234.Visible = true;
            this.gridColumn234.VisibleIndex = 41;
            // 
            // gridColumn235
            // 
            this.gridColumn235.Caption = "T网硬切换时延";
            this.gridColumn235.FieldName = "HO_Time_T2T";
            this.gridColumn235.Name = "gridColumn235";
            this.gridColumn235.Visible = true;
            this.gridColumn235.VisibleIndex = 42;
            // 
            // gridColumn236
            // 
            this.gridColumn236.Caption = "T网接力切换成功次数";
            this.gridColumn236.FieldName = "HO_SUCC_Baton";
            this.gridColumn236.Name = "gridColumn236";
            this.gridColumn236.Visible = true;
            this.gridColumn236.VisibleIndex = 43;
            // 
            // gridColumn237
            // 
            this.gridColumn237.Caption = "T网接力切换失败次数";
            this.gridColumn237.FieldName = "HO_FAIL_Baton";
            this.gridColumn237.Name = "gridColumn237";
            this.gridColumn237.Visible = true;
            this.gridColumn237.VisibleIndex = 44;
            // 
            // gridColumn238
            // 
            this.gridColumn238.Caption = "T网接力切换时延";
            this.gridColumn238.FieldName = "HO_Time_Baton";
            this.gridColumn238.Name = "gridColumn238";
            this.gridColumn238.Visible = true;
            this.gridColumn238.VisibleIndex = 45;
            // 
            // gridColumn239
            // 
            this.gridColumn239.Caption = "T网重选成功次数";
            this.gridColumn239.FieldName = "CR_SUCC_T2T";
            this.gridColumn239.Name = "gridColumn239";
            this.gridColumn239.Visible = true;
            this.gridColumn239.VisibleIndex = 46;
            // 
            // gridColumn240
            // 
            this.gridColumn240.Caption = "T网重选失败次数";
            this.gridColumn240.FieldName = "CR_FAIL_T2T";
            this.gridColumn240.Name = "gridColumn240";
            this.gridColumn240.Visible = true;
            this.gridColumn240.VisibleIndex = 47;
            // 
            // gridColumn241
            // 
            this.gridColumn241.Caption = "T至G重选成功次数";
            this.gridColumn241.FieldName = "CR_SUCC_T2G";
            this.gridColumn241.Name = "gridColumn241";
            this.gridColumn241.Visible = true;
            this.gridColumn241.VisibleIndex = 48;
            // 
            // gridColumn242
            // 
            this.gridColumn242.Caption = "T至G重选失败次数";
            this.gridColumn242.FieldName = "CR_FAIL_T2G";
            this.gridColumn242.Name = "gridColumn242";
            this.gridColumn242.Visible = true;
            this.gridColumn242.VisibleIndex = 49;
            // 
            // gridColumn243
            // 
            this.gridColumn243.Caption = "G至T重选成功次数";
            this.gridColumn243.FieldName = "CR_SUCC_G2T";
            this.gridColumn243.Name = "gridColumn243";
            this.gridColumn243.Visible = true;
            this.gridColumn243.VisibleIndex = 50;
            // 
            // gridColumn244
            // 
            this.gridColumn244.Caption = "G至T重选失败次数";
            this.gridColumn244.FieldName = "CR_FAIL_G2T";
            this.gridColumn244.Name = "gridColumn244";
            this.gridColumn244.Visible = true;
            this.gridColumn244.VisibleIndex = 51;
            // 
            // gridColumn245
            // 
            this.gridColumn245.Caption = "Hsdpa下载量占比";
            this.gridColumn245.FieldName = "HSDPA_Size";
            this.gridColumn245.Name = "gridColumn245";
            this.gridColumn245.Visible = true;
            this.gridColumn245.VisibleIndex = 52;
            // 
            // gridColumn246
            // 
            this.gridColumn246.Caption = "Hsdpa时长占比";
            this.gridColumn246.FieldName = "HSDPA_Time";
            this.gridColumn246.Name = "gridColumn246";
            this.gridColumn246.Visible = true;
            this.gridColumn246.VisibleIndex = 53;
            // 
            // gridColumn247
            // 
            this.gridColumn247.Caption = "R4下载量占比";
            this.gridColumn247.FieldName = "R4_Size";
            this.gridColumn247.Name = "gridColumn247";
            this.gridColumn247.Visible = true;
            this.gridColumn247.VisibleIndex = 54;
            // 
            // gridColumn248
            // 
            this.gridColumn248.Caption = "R4时长占比";
            this.gridColumn248.FieldName = "R4_Time";
            this.gridColumn248.Name = "gridColumn248";
            this.gridColumn248.Visible = true;
            this.gridColumn248.VisibleIndex = 55;
            // 
            // gridColumn249
            // 
            this.gridColumn249.Caption = "Gsm下载量占比";
            this.gridColumn249.FieldName = "EDGE_Size";
            this.gridColumn249.Name = "gridColumn249";
            this.gridColumn249.Visible = true;
            this.gridColumn249.VisibleIndex = 56;
            // 
            // gridColumn250
            // 
            this.gridColumn250.Caption = "Gsm时长占比";
            this.gridColumn250.FieldName = "EDGE_Time";
            this.gridColumn250.Name = "gridColumn250";
            this.gridColumn250.Visible = true;
            this.gridColumn250.VisibleIndex = 57;
            // 
            // gridColumn251
            // 
            this.gridColumn251.Caption = "小区1";
            this.gridColumn251.FieldName = "Cell1";
            this.gridColumn251.Name = "gridColumn251";
            this.gridColumn251.Visible = true;
            this.gridColumn251.VisibleIndex = 38;
            // 
            // gridColumn252
            // 
            this.gridColumn252.Caption = "小区2";
            this.gridColumn252.FieldName = "Cell2";
            this.gridColumn252.Name = "gridColumn252";
            this.gridColumn252.Visible = true;
            this.gridColumn252.VisibleIndex = 25;
            // 
            // gridColumn253
            // 
            this.gridColumn253.Caption = "小区3";
            this.gridColumn253.FieldName = "Cell3";
            this.gridColumn253.Name = "gridColumn253";
            this.gridColumn253.Visible = true;
            this.gridColumn253.VisibleIndex = 27;
            // 
            // gridColumn254
            // 
            this.gridColumn254.Caption = "所属网格";
            this.gridColumn254.FieldName = "Strgrid";
            this.gridColumn254.Name = "gridColumn254";
            this.gridColumn254.Visible = true;
            this.gridColumn254.VisibleIndex = 29;
            // 
            // gridColumn255
            // 
            this.gridColumn255.Caption = "所属道路";
            this.gridColumn255.FieldName = "Strroad";
            this.gridColumn255.Name = "gridColumn255";
            this.gridColumn255.Visible = true;
            this.gridColumn255.VisibleIndex = 31;
            // 
            // gridColumn256
            // 
            this.gridColumn256.Caption = "经度";
            this.gridColumn256.FieldName = "Imlongitude";
            this.gridColumn256.Name = "gridColumn256";
            // 
            // gridColumn257
            // 
            this.gridColumn257.Caption = "纬度";
            this.gridColumn257.FieldName = "Imlatitude";
            this.gridColumn257.Name = "gridColumn257";
            // 
            // gridColumn258
            // 
            this.gridColumn258.Caption = "ifileid";
            this.gridColumn258.FieldName = "Ifileid";
            this.gridColumn258.Name = "gridColumn258";
            // 
            // gridColumn259
            // 
            this.gridColumn259.Caption = "istime";
            this.gridColumn259.FieldName = "Istime";
            this.gridColumn259.Name = "gridColumn259";
            // 
            // gridColumn260
            // 
            this.gridColumn260.Caption = "ietime";
            this.gridColumn260.FieldName = "Ietime";
            this.gridColumn260.Name = "gridColumn260";
            // 
            // gridColumn327
            // 
            this.gridColumn327.Caption = "gridColumn327";
            this.gridColumn327.FieldName = "Iid";
            this.gridColumn327.Name = "gridColumn327";
            // 
            // gridView8
            // 
            this.gridView8.GridControl = this.gridControl4;
            this.gridView8.Name = "gridView8";
            // 
            // xtraTabPage6
            // 
            this.xtraTabPage6.Controls.Add(this.gridControl6);
            this.xtraTabPage6.Name = "xtraTabPage6";
            this.xtraTabPage6.Size = new System.Drawing.Size(836, 381);
            this.xtraTabPage6.Text = "EDGE覆盖";
            // 
            // gridControl6
            // 
            this.gridControl6.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl6.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl6.Location = new System.Drawing.Point(0, 0);
            this.gridControl6.MainView = this.bandedGridView5;
            this.gridControl6.Name = "gridControl6";
            this.gridControl6.Size = new System.Drawing.Size(836, 381);
            this.gridControl6.TabIndex = 1;
            this.gridControl6.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView5,
            this.gridView10,
            this.gridView11});
            this.gridControl6.DoubleClick += new System.EventHandler(this.gridControl6_DoubleClick);
            // 
            // bandedGridView5
            // 
            this.bandedGridView5.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand5,
            this.gridBand54,
            this.gridBand55,
            this.gridBand56,
            this.gridBand57,
            this.gridBand58,
            this.gridBand59,
            this.gridBand60,
            this.gridBand61,
            this.gridBand62,
            this.gridBand63,
            this.gridBand64,
            this.gridBand65});
            this.bandedGridView5.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn257,
            this.bandedGridColumn258,
            this.bandedGridColumn259,
            this.bandedGridColumn260,
            this.bandedGridColumn261,
            this.bandedGridColumn262,
            this.bandedGridColumn263,
            this.bandedGridColumn264,
            this.bandedGridColumn265,
            this.bandedGridColumn266,
            this.bandedGridColumn267,
            this.bandedGridColumn268,
            this.bandedGridColumn269,
            this.bandedGridColumn270,
            this.bandedGridColumn271,
            this.bandedGridColumn272,
            this.bandedGridColumn273,
            this.bandedGridColumn274,
            this.bandedGridColumn275,
            this.bandedGridColumn276,
            this.bandedGridColumn277,
            this.bandedGridColumn278,
            this.bandedGridColumn279,
            this.bandedGridColumn280,
            this.bandedGridColumn281,
            this.bandedGridColumn282,
            this.bandedGridColumn283,
            this.bandedGridColumn284,
            this.bandedGridColumn285,
            this.bandedGridColumn286,
            this.bandedGridColumn287,
            this.bandedGridColumn288,
            this.bandedGridColumn289,
            this.bandedGridColumn290,
            this.bandedGridColumn291,
            this.bandedGridColumn292,
            this.bandedGridColumn293,
            this.bandedGridColumn294,
            this.bandedGridColumn295,
            this.bandedGridColumn296,
            this.bandedGridColumn297,
            this.bandedGridColumn298,
            this.bandedGridColumn299,
            this.bandedGridColumn300,
            this.bandedGridColumn301,
            this.bandedGridColumn302,
            this.bandedGridColumn303,
            this.bandedGridColumn304,
            this.bandedGridColumn305,
            this.bandedGridColumn306,
            this.bandedGridColumn307,
            this.bandedGridColumn308,
            this.bandedGridColumn309,
            this.bandedGridColumn310,
            this.bandedGridColumn311,
            this.bandedGridColumn312,
            this.bandedGridColumn313,
            this.bandedGridColumn314,
            this.bandedGridColumn315,
            this.bandedGridColumn316,
            this.bandedGridColumn317,
            this.bandedGridColumn318,
            this.bandedGridColumn319,
            this.bandedGridColumn320});
            this.bandedGridView5.GridControl = this.gridControl6;
            this.bandedGridView5.Name = "bandedGridView5";
            this.bandedGridView5.OptionsBehavior.Editable = false;
            this.bandedGridView5.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.bandedGridView5.OptionsView.ColumnAutoWidth = false;
            this.bandedGridView5.OptionsView.ShowGroupPanel = false;
            // 
            // gridView10
            // 
            this.gridView10.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn261,
            this.gridColumn262,
            this.gridColumn263,
            this.gridColumn264,
            this.gridColumn265,
            this.gridColumn266,
            this.gridColumn267,
            this.gridColumn268,
            this.gridColumn269,
            this.gridColumn270,
            this.gridColumn271,
            this.gridColumn272,
            this.gridColumn273,
            this.gridColumn274,
            this.gridColumn275,
            this.gridColumn276,
            this.gridColumn277,
            this.gridColumn278,
            this.gridColumn279,
            this.gridColumn280,
            this.gridColumn281,
            this.gridColumn282,
            this.gridColumn283,
            this.gridColumn284,
            this.gridColumn285,
            this.gridColumn286,
            this.gridColumn287,
            this.gridColumn288,
            this.gridColumn289,
            this.gridColumn290,
            this.gridColumn291,
            this.gridColumn292,
            this.gridColumn293,
            this.gridColumn294,
            this.gridColumn295,
            this.gridColumn296,
            this.gridColumn297,
            this.gridColumn298,
            this.gridColumn299,
            this.gridColumn300,
            this.gridColumn301,
            this.gridColumn302,
            this.gridColumn303,
            this.gridColumn304,
            this.gridColumn305,
            this.gridColumn306,
            this.gridColumn307,
            this.gridColumn308,
            this.gridColumn309,
            this.gridColumn310,
            this.gridColumn311,
            this.gridColumn312,
            this.gridColumn313,
            this.gridColumn314,
            this.gridColumn315,
            this.gridColumn316,
            this.gridColumn317,
            this.gridColumn318,
            this.gridColumn319,
            this.gridColumn320,
            this.gridColumn321,
            this.gridColumn322,
            this.gridColumn323,
            this.gridColumn328});
            this.gridView10.GridControl = this.gridControl6;
            this.gridView10.Name = "gridView10";
            this.gridView10.OptionsBehavior.Editable = false;
            this.gridView10.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView10.OptionsView.ColumnAutoWidth = false;
            this.gridView10.OptionsView.ShowGroupPanel = false;
            this.gridView10.DoubleClick += new System.EventHandler(this.gridView10_DoubleClick);
            // 
            // gridColumn261
            // 
            this.gridColumn261.Caption = "距离(米)";
            this.gridColumn261.FieldName = "Idistance";
            this.gridColumn261.Name = "gridColumn261";
            this.gridColumn261.Visible = true;
            this.gridColumn261.VisibleIndex = 0;
            // 
            // gridColumn262
            // 
            this.gridColumn262.Caption = "时长(秒)";
            this.gridColumn262.FieldName = "Iduration";
            this.gridColumn262.Name = "gridColumn262";
            this.gridColumn262.Visible = true;
            this.gridColumn262.VisibleIndex = 1;
            // 
            // gridColumn263
            // 
            this.gridColumn263.Caption = "下载速率0";
            this.gridColumn263.FieldName = "APP_Speed0";
            this.gridColumn263.Name = "gridColumn263";
            this.gridColumn263.Visible = true;
            this.gridColumn263.VisibleIndex = 2;
            // 
            // gridColumn264
            // 
            this.gridColumn264.Caption = "下载速率[0,500]";
            this.gridColumn264.FieldName = "APP_Speed0_500";
            this.gridColumn264.Name = "gridColumn264";
            this.gridColumn264.Visible = true;
            this.gridColumn264.VisibleIndex = 3;
            // 
            // gridColumn265
            // 
            this.gridColumn265.Caption = "下载速率[500,800]";
            this.gridColumn265.FieldName = "APP_Speed500_800";
            this.gridColumn265.Name = "gridColumn265";
            this.gridColumn265.Visible = true;
            this.gridColumn265.VisibleIndex = 4;
            // 
            // gridColumn266
            // 
            this.gridColumn266.Caption = "下载速率[800,900]";
            this.gridColumn266.FieldName = "APP_Speed800_900";
            this.gridColumn266.Name = "gridColumn266";
            this.gridColumn266.Visible = true;
            this.gridColumn266.VisibleIndex = 5;
            // 
            // gridColumn267
            // 
            this.gridColumn267.Caption = "下载速率[900,1100]";
            this.gridColumn267.FieldName = "APP_Speed900_1100";
            this.gridColumn267.Name = "gridColumn267";
            this.gridColumn267.Visible = true;
            this.gridColumn267.VisibleIndex = 6;
            // 
            // gridColumn268
            // 
            this.gridColumn268.Caption = "下载速率1100";
            this.gridColumn268.FieldName = "APP_Speed1100";
            this.gridColumn268.Name = "gridColumn268";
            this.gridColumn268.Visible = true;
            this.gridColumn268.VisibleIndex = 7;
            // 
            // gridColumn269
            // 
            this.gridColumn269.Caption = "TD电平均值";
            this.gridColumn269.FieldName = "Rscpmean";
            this.gridColumn269.Name = "gridColumn269";
            this.gridColumn269.Visible = true;
            this.gridColumn269.VisibleIndex = 8;
            // 
            // gridColumn270
            // 
            this.gridColumn270.Caption = "TD电平小于75";
            this.gridColumn270.FieldName = "Rscp75";
            this.gridColumn270.Name = "gridColumn270";
            this.gridColumn270.Visible = true;
            this.gridColumn270.VisibleIndex = 9;
            // 
            // gridColumn271
            // 
            this.gridColumn271.Caption = "TD电平[76,80]";
            this.gridColumn271.FieldName = "Rscp76_80";
            this.gridColumn271.Name = "gridColumn271";
            this.gridColumn271.Visible = true;
            this.gridColumn271.VisibleIndex = 10;
            // 
            // gridColumn272
            // 
            this.gridColumn272.Caption = "TD电平[81,85]";
            this.gridColumn272.FieldName = "Rscp81_85";
            this.gridColumn272.Name = "gridColumn272";
            this.gridColumn272.Visible = true;
            this.gridColumn272.VisibleIndex = 11;
            // 
            // gridColumn273
            // 
            this.gridColumn273.Caption = "TD电平[86,90]";
            this.gridColumn273.FieldName = "Rscp86_90";
            this.gridColumn273.Name = "gridColumn273";
            this.gridColumn273.Visible = true;
            this.gridColumn273.VisibleIndex = 13;
            // 
            // gridColumn274
            // 
            this.gridColumn274.Caption = "TD电平[91,94]";
            this.gridColumn274.FieldName = "Rscp91_94";
            this.gridColumn274.Name = "gridColumn274";
            this.gridColumn274.Visible = true;
            this.gridColumn274.VisibleIndex = 12;
            // 
            // gridColumn275
            // 
            this.gridColumn275.Caption = "TD电平大于94";
            this.gridColumn275.FieldName = "Rscp94";
            this.gridColumn275.Name = "gridColumn275";
            this.gridColumn275.Visible = true;
            this.gridColumn275.VisibleIndex = 14;
            // 
            // gridColumn276
            // 
            this.gridColumn276.Caption = "GSM电平均值";
            this.gridColumn276.FieldName = "Rxlmean";
            this.gridColumn276.Name = "gridColumn276";
            this.gridColumn276.Visible = true;
            this.gridColumn276.VisibleIndex = 15;
            // 
            // gridColumn277
            // 
            this.gridColumn277.Caption = "GSM电平小于75";
            this.gridColumn277.FieldName = "Rxl75";
            this.gridColumn277.Name = "gridColumn277";
            this.gridColumn277.Visible = true;
            this.gridColumn277.VisibleIndex = 16;
            // 
            // gridColumn278
            // 
            this.gridColumn278.Caption = "GSM电平[76,80]";
            this.gridColumn278.FieldName = "Rxl76_80";
            this.gridColumn278.Name = "gridColumn278";
            this.gridColumn278.Visible = true;
            this.gridColumn278.VisibleIndex = 17;
            // 
            // gridColumn279
            // 
            this.gridColumn279.Caption = "GSM电平[81,85]";
            this.gridColumn279.FieldName = "Rxl81_85";
            this.gridColumn279.Name = "gridColumn279";
            this.gridColumn279.Visible = true;
            this.gridColumn279.VisibleIndex = 18;
            // 
            // gridColumn280
            // 
            this.gridColumn280.Caption = "GSM电平[86,90]";
            this.gridColumn280.FieldName = "Rxl86_90";
            this.gridColumn280.Name = "gridColumn280";
            this.gridColumn280.Visible = true;
            this.gridColumn280.VisibleIndex = 19;
            // 
            // gridColumn281
            // 
            this.gridColumn281.Caption = "GSM电平[91,94]";
            this.gridColumn281.FieldName = "Rxl91_94";
            this.gridColumn281.Name = "gridColumn281";
            this.gridColumn281.Visible = true;
            this.gridColumn281.VisibleIndex = 20;
            // 
            // gridColumn282
            // 
            this.gridColumn282.Caption = "GSM电平大于94";
            this.gridColumn282.FieldName = "Rxl94";
            this.gridColumn282.Name = "gridColumn282";
            this.gridColumn282.Visible = true;
            this.gridColumn282.VisibleIndex = 21;
            // 
            // gridColumn283
            // 
            this.gridColumn283.Caption = "DPCH C/I均值";
            this.gridColumn283.FieldName = "Dc2imean";
            this.gridColumn283.Name = "gridColumn283";
            this.gridColumn283.Visible = true;
            this.gridColumn283.VisibleIndex = 22;
            // 
            // gridColumn284
            // 
            this.gridColumn284.Caption = "DPCH C/I小于-10";
            this.gridColumn284.FieldName = "Dc2iF10";
            this.gridColumn284.Name = "gridColumn284";
            this.gridColumn284.Visible = true;
            this.gridColumn284.VisibleIndex = 23;
            // 
            // gridColumn285
            // 
            this.gridColumn285.Caption = "DPCH C/I[-10,-3]";
            this.gridColumn285.FieldName = "Dc2iF10TF3";
            this.gridColumn285.Name = "gridColumn285";
            this.gridColumn285.Visible = true;
            this.gridColumn285.VisibleIndex = 24;
            // 
            // gridColumn286
            // 
            this.gridColumn286.Caption = "DPCH C/I[-3,15]";
            this.gridColumn286.FieldName = "Dc2iF3T15";
            this.gridColumn286.Name = "gridColumn286";
            this.gridColumn286.Visible = true;
            this.gridColumn286.VisibleIndex = 26;
            // 
            // gridColumn287
            // 
            this.gridColumn287.Caption = "DPCH C/I大于15";
            this.gridColumn287.FieldName = "Dc2i15";
            this.gridColumn287.Name = "gridColumn287";
            this.gridColumn287.Visible = true;
            this.gridColumn287.VisibleIndex = 28;
            // 
            // gridColumn288
            // 
            this.gridColumn288.Caption = "TxPower均值";
            this.gridColumn288.FieldName = "Txpowermean";
            this.gridColumn288.Name = "gridColumn288";
            this.gridColumn288.Visible = true;
            this.gridColumn288.VisibleIndex = 30;
            // 
            // gridColumn289
            // 
            this.gridColumn289.Caption = "TxPower小于-20";
            this.gridColumn289.FieldName = "TxpowerF20";
            this.gridColumn289.Name = "gridColumn289";
            this.gridColumn289.Visible = true;
            this.gridColumn289.VisibleIndex = 32;
            // 
            // gridColumn290
            // 
            this.gridColumn290.Caption = "TxPower[-20,0]";
            this.gridColumn290.FieldName = "TxpowerF20T0";
            this.gridColumn290.Name = "gridColumn290";
            this.gridColumn290.Visible = true;
            this.gridColumn290.VisibleIndex = 33;
            // 
            // gridColumn291
            // 
            this.gridColumn291.Caption = "TxPower[0,15]";
            this.gridColumn291.FieldName = "Txpower0T15";
            this.gridColumn291.Name = "gridColumn291";
            this.gridColumn291.Visible = true;
            this.gridColumn291.VisibleIndex = 34;
            // 
            // gridColumn292
            // 
            this.gridColumn292.Caption = "TxPower大于15";
            this.gridColumn292.FieldName = "Txpower15";
            this.gridColumn292.Name = "gridColumn292";
            this.gridColumn292.Visible = true;
            this.gridColumn292.VisibleIndex = 35;
            // 
            // gridColumn293
            // 
            this.gridColumn293.Caption = "T至G切换成功次数";
            this.gridColumn293.FieldName = "HO_SUCC_T2G";
            this.gridColumn293.Name = "gridColumn293";
            this.gridColumn293.Visible = true;
            this.gridColumn293.VisibleIndex = 36;
            // 
            // gridColumn294
            // 
            this.gridColumn294.Caption = "T至G切换失败次数";
            this.gridColumn294.FieldName = "HO_FAIL_T2G";
            this.gridColumn294.Name = "gridColumn294";
            this.gridColumn294.Visible = true;
            this.gridColumn294.VisibleIndex = 37;
            // 
            // gridColumn295
            // 
            this.gridColumn295.Caption = "T至G切换时延";
            this.gridColumn295.FieldName = "HO_Time_T2G";
            this.gridColumn295.Name = "gridColumn295";
            this.gridColumn295.Visible = true;
            this.gridColumn295.VisibleIndex = 39;
            // 
            // gridColumn296
            // 
            this.gridColumn296.Caption = "T网硬切换成功次数";
            this.gridColumn296.FieldName = "HO_SUCC_T2T";
            this.gridColumn296.Name = "gridColumn296";
            this.gridColumn296.Visible = true;
            this.gridColumn296.VisibleIndex = 40;
            // 
            // gridColumn297
            // 
            this.gridColumn297.Caption = "T网硬切换失败次数";
            this.gridColumn297.FieldName = "HO_FAIL_T2T";
            this.gridColumn297.Name = "gridColumn297";
            this.gridColumn297.Visible = true;
            this.gridColumn297.VisibleIndex = 41;
            // 
            // gridColumn298
            // 
            this.gridColumn298.Caption = "T网硬切换时延";
            this.gridColumn298.FieldName = "HO_Time_T2T";
            this.gridColumn298.Name = "gridColumn298";
            this.gridColumn298.Visible = true;
            this.gridColumn298.VisibleIndex = 42;
            // 
            // gridColumn299
            // 
            this.gridColumn299.Caption = "T网接力切换成功次数";
            this.gridColumn299.FieldName = "HO_SUCC_Baton";
            this.gridColumn299.Name = "gridColumn299";
            this.gridColumn299.Visible = true;
            this.gridColumn299.VisibleIndex = 43;
            // 
            // gridColumn300
            // 
            this.gridColumn300.Caption = "T网接力切换失败次数";
            this.gridColumn300.FieldName = "HO_FAIL_Baton";
            this.gridColumn300.Name = "gridColumn300";
            this.gridColumn300.Visible = true;
            this.gridColumn300.VisibleIndex = 44;
            // 
            // gridColumn301
            // 
            this.gridColumn301.Caption = "T网接力切换时延";
            this.gridColumn301.FieldName = "HO_Time_Baton";
            this.gridColumn301.Name = "gridColumn301";
            this.gridColumn301.Visible = true;
            this.gridColumn301.VisibleIndex = 45;
            // 
            // gridColumn302
            // 
            this.gridColumn302.Caption = "T网重选成功次数";
            this.gridColumn302.FieldName = "CR_SUCC_T2T";
            this.gridColumn302.Name = "gridColumn302";
            this.gridColumn302.Visible = true;
            this.gridColumn302.VisibleIndex = 46;
            // 
            // gridColumn303
            // 
            this.gridColumn303.Caption = "T网重选失败次数";
            this.gridColumn303.FieldName = "CR_FAIL_T2T";
            this.gridColumn303.Name = "gridColumn303";
            this.gridColumn303.Visible = true;
            this.gridColumn303.VisibleIndex = 47;
            // 
            // gridColumn304
            // 
            this.gridColumn304.Caption = "T至G重选成功次数";
            this.gridColumn304.FieldName = "CR_SUCC_T2G";
            this.gridColumn304.Name = "gridColumn304";
            this.gridColumn304.Visible = true;
            this.gridColumn304.VisibleIndex = 48;
            // 
            // gridColumn305
            // 
            this.gridColumn305.Caption = "T至G重选失败次数";
            this.gridColumn305.FieldName = "CR_FAIL_T2G";
            this.gridColumn305.Name = "gridColumn305";
            this.gridColumn305.Visible = true;
            this.gridColumn305.VisibleIndex = 49;
            // 
            // gridColumn306
            // 
            this.gridColumn306.Caption = "G至T重选成功次数";
            this.gridColumn306.FieldName = "CR_SUCC_G2T";
            this.gridColumn306.Name = "gridColumn306";
            this.gridColumn306.Visible = true;
            this.gridColumn306.VisibleIndex = 50;
            // 
            // gridColumn307
            // 
            this.gridColumn307.Caption = "G至T重选失败次数";
            this.gridColumn307.FieldName = "CR_FAIL_G2T";
            this.gridColumn307.Name = "gridColumn307";
            this.gridColumn307.Visible = true;
            this.gridColumn307.VisibleIndex = 51;
            // 
            // gridColumn308
            // 
            this.gridColumn308.Caption = "Hsdpa下载量占比";
            this.gridColumn308.FieldName = "HSDPA_Size";
            this.gridColumn308.Name = "gridColumn308";
            this.gridColumn308.Visible = true;
            this.gridColumn308.VisibleIndex = 52;
            // 
            // gridColumn309
            // 
            this.gridColumn309.Caption = "Hsdpa时长占比";
            this.gridColumn309.FieldName = "HSDPA_Time";
            this.gridColumn309.Name = "gridColumn309";
            this.gridColumn309.Visible = true;
            this.gridColumn309.VisibleIndex = 53;
            // 
            // gridColumn310
            // 
            this.gridColumn310.Caption = "R4下载量占比";
            this.gridColumn310.FieldName = "R4_Size";
            this.gridColumn310.Name = "gridColumn310";
            this.gridColumn310.Visible = true;
            this.gridColumn310.VisibleIndex = 54;
            // 
            // gridColumn311
            // 
            this.gridColumn311.Caption = "R4时长占比";
            this.gridColumn311.FieldName = "R4_Time";
            this.gridColumn311.Name = "gridColumn311";
            this.gridColumn311.Visible = true;
            this.gridColumn311.VisibleIndex = 55;
            // 
            // gridColumn312
            // 
            this.gridColumn312.Caption = "Gsm下载量占比";
            this.gridColumn312.FieldName = "EDGE_Size";
            this.gridColumn312.Name = "gridColumn312";
            this.gridColumn312.Visible = true;
            this.gridColumn312.VisibleIndex = 56;
            // 
            // gridColumn313
            // 
            this.gridColumn313.Caption = "Gsm时长占比";
            this.gridColumn313.FieldName = "EDGE_Time";
            this.gridColumn313.Name = "gridColumn313";
            this.gridColumn313.Visible = true;
            this.gridColumn313.VisibleIndex = 57;
            // 
            // gridColumn314
            // 
            this.gridColumn314.Caption = "小区1";
            this.gridColumn314.FieldName = "Cell1";
            this.gridColumn314.Name = "gridColumn314";
            this.gridColumn314.Visible = true;
            this.gridColumn314.VisibleIndex = 38;
            // 
            // gridColumn315
            // 
            this.gridColumn315.Caption = "小区2";
            this.gridColumn315.FieldName = "Cell2";
            this.gridColumn315.Name = "gridColumn315";
            this.gridColumn315.Visible = true;
            this.gridColumn315.VisibleIndex = 25;
            // 
            // gridColumn316
            // 
            this.gridColumn316.Caption = "小区3";
            this.gridColumn316.FieldName = "Cell3";
            this.gridColumn316.Name = "gridColumn316";
            this.gridColumn316.Visible = true;
            this.gridColumn316.VisibleIndex = 27;
            // 
            // gridColumn317
            // 
            this.gridColumn317.Caption = "所属网格";
            this.gridColumn317.FieldName = "Strgrid";
            this.gridColumn317.Name = "gridColumn317";
            this.gridColumn317.Visible = true;
            this.gridColumn317.VisibleIndex = 29;
            // 
            // gridColumn318
            // 
            this.gridColumn318.Caption = "所属道路";
            this.gridColumn318.FieldName = "Strroad";
            this.gridColumn318.Name = "gridColumn318";
            this.gridColumn318.Visible = true;
            this.gridColumn318.VisibleIndex = 31;
            // 
            // gridColumn319
            // 
            this.gridColumn319.Caption = "经度";
            this.gridColumn319.FieldName = "Imlongitude";
            this.gridColumn319.Name = "gridColumn319";
            // 
            // gridColumn320
            // 
            this.gridColumn320.Caption = "纬度";
            this.gridColumn320.FieldName = "Imlatitude";
            this.gridColumn320.Name = "gridColumn320";
            // 
            // gridColumn321
            // 
            this.gridColumn321.Caption = "ifileid";
            this.gridColumn321.FieldName = "Ifileid";
            this.gridColumn321.Name = "gridColumn321";
            // 
            // gridColumn322
            // 
            this.gridColumn322.Caption = "istime";
            this.gridColumn322.FieldName = "Istime";
            this.gridColumn322.Name = "gridColumn322";
            // 
            // gridColumn323
            // 
            this.gridColumn323.Caption = "ietime";
            this.gridColumn323.FieldName = "Ietime";
            this.gridColumn323.Name = "gridColumn323";
            // 
            // gridColumn328
            // 
            this.gridColumn328.Caption = "gridColumn328";
            this.gridColumn328.FieldName = "Iid";
            this.gridColumn328.Name = "gridColumn328";
            // 
            // gridView11
            // 
            this.gridView11.GridControl = this.gridControl6;
            this.gridView11.Name = "gridView11";
            // 
            // btnClearFly
            // 
            this.btnClearFly.Location = new System.Drawing.Point(0, 0);
            this.btnClearFly.Name = "btnClearFly";
            this.btnClearFly.Size = new System.Drawing.Size(75, 23);
            this.btnClearFly.TabIndex = 0;
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.ColumnCount = 1;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.Controls.Add(this.panel1, 0, 0);
            this.tableLayoutPanel1.Controls.Add(this.xtraTabControl1, 0, 1);
            this.tableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 2;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 0F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(849, 417);
            this.tableLayoutPanel1.TabIndex = 1;
            // 
            // panel1
            // 
            this.panel1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.panel1.Controls.Add(this.label1);
            this.panel1.Controls.Add(this.btnNext1);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel1.Location = new System.Drawing.Point(3, 3);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(843, 1);
            this.panel1.TabIndex = 3;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(26, 20);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(66, 14);
            this.label1.TabIndex = 1;
            this.label1.Text = "1.道路汇聚";
            // 
            // btnNext1
            // 
            this.btnNext1.Enabled = false;
            this.btnNext1.Location = new System.Drawing.Point(101, 16);
            this.btnNext1.Name = "btnNext1";
            this.btnNext1.Size = new System.Drawing.Size(75, 23);
            this.btnNext1.TabIndex = 0;
            this.btnNext1.Text = "下一步";
            this.btnNext1.UseVisualStyleBackColor = true;
            this.btnNext1.Click += new System.EventHandler(this.btnNext1_Click);
            // 
            // gridBand14
            // 
            this.gridBand14.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand14.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand14.Caption = "基础信息";
            this.gridBand14.Columns.Add(this.bandedGridColumn57);
            this.gridBand14.Columns.Add(this.bandedGridColumn58);
            this.gridBand14.Columns.Add(this.bandedGridColumn54);
            this.gridBand14.Columns.Add(this.bandedGridColumn55);
            this.gridBand14.Columns.Add(this.bandedGridColumn56);
            this.gridBand14.Columns.Add(this.bandedGridColumn59);
            this.gridBand14.Columns.Add(this.bandedGridColumn60);
            this.gridBand14.Name = "gridBand14";
            this.gridBand14.Width = 555;
            // 
            // gridBand1
            // 
            this.gridBand1.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand1.Caption = "测试信息";
            this.gridBand1.Columns.Add(this.bandedGridColumn1);
            this.gridBand1.Columns.Add(this.bandedGridColumn2);
            this.gridBand1.Columns.Add(this.bandedGridColumn61);
            this.gridBand1.Columns.Add(this.bandedGridColumn62);
            this.gridBand1.Columns.Add(this.bandedGridColumn63);
            this.gridBand1.Columns.Add(this.bandedGridColumn64);
            this.gridBand1.Name = "gridBand1";
            this.gridBand1.Width = 150;
            // 
            // gridBand6
            // 
            this.gridBand6.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand6.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand6.Caption = "下载速率";
            this.gridBand6.Columns.Add(this.bandedGridColumn3);
            this.gridBand6.Columns.Add(this.bandedGridColumn4);
            this.gridBand6.Columns.Add(this.bandedGridColumn5);
            this.gridBand6.Columns.Add(this.bandedGridColumn6);
            this.gridBand6.Columns.Add(this.bandedGridColumn7);
            this.gridBand6.Columns.Add(this.bandedGridColumn8);
            this.gridBand6.Name = "gridBand6";
            this.gridBand6.Width = 450;
            // 
            // gridBand15
            // 
            this.gridBand15.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand15.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand15.Caption = "TD电平";
            this.gridBand15.Columns.Add(this.bandedGridColumn9);
            this.gridBand15.Columns.Add(this.bandedGridColumn10);
            this.gridBand15.Columns.Add(this.bandedGridColumn11);
            this.gridBand15.Columns.Add(this.bandedGridColumn12);
            this.gridBand15.Columns.Add(this.bandedGridColumn13);
            this.gridBand15.Columns.Add(this.bandedGridColumn14);
            this.gridBand15.Columns.Add(this.bandedGridColumn15);
            this.gridBand15.Name = "gridBand15";
            this.gridBand15.Width = 525;
            // 
            // gridBand16
            // 
            this.gridBand16.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand16.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand16.Caption = "GSM电平";
            this.gridBand16.Columns.Add(this.bandedGridColumn16);
            this.gridBand16.Columns.Add(this.bandedGridColumn17);
            this.gridBand16.Columns.Add(this.bandedGridColumn18);
            this.gridBand16.Columns.Add(this.bandedGridColumn19);
            this.gridBand16.Columns.Add(this.bandedGridColumn20);
            this.gridBand16.Columns.Add(this.bandedGridColumn21);
            this.gridBand16.Columns.Add(this.bandedGridColumn22);
            this.gridBand16.Name = "gridBand16";
            this.gridBand16.Width = 525;
            // 
            // gridBand17
            // 
            this.gridBand17.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand17.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand17.Caption = "DPCH C/I";
            this.gridBand17.Columns.Add(this.bandedGridColumn23);
            this.gridBand17.Columns.Add(this.bandedGridColumn24);
            this.gridBand17.Columns.Add(this.bandedGridColumn25);
            this.gridBand17.Columns.Add(this.bandedGridColumn26);
            this.gridBand17.Columns.Add(this.bandedGridColumn27);
            this.gridBand17.Name = "gridBand17";
            this.gridBand17.Width = 375;
            // 
            // gridBand7
            // 
            this.gridBand7.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand7.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand7.Caption = "TxPower";
            this.gridBand7.Columns.Add(this.bandedGridColumn28);
            this.gridBand7.Columns.Add(this.bandedGridColumn29);
            this.gridBand7.Columns.Add(this.bandedGridColumn30);
            this.gridBand7.Columns.Add(this.bandedGridColumn31);
            this.gridBand7.Columns.Add(this.bandedGridColumn32);
            this.gridBand7.Name = "gridBand7";
            this.gridBand7.Width = 375;
            // 
            // gridBand8
            // 
            this.gridBand8.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand8.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand8.Caption = "T至G切换";
            this.gridBand8.Columns.Add(this.bandedGridColumn33);
            this.gridBand8.Columns.Add(this.bandedGridColumn34);
            this.gridBand8.Columns.Add(this.bandedGridColumn35);
            this.gridBand8.Name = "gridBand8";
            this.gridBand8.Width = 225;
            // 
            // gridBand9
            // 
            this.gridBand9.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand9.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand9.Caption = "T网硬切换";
            this.gridBand9.Columns.Add(this.bandedGridColumn36);
            this.gridBand9.Columns.Add(this.bandedGridColumn37);
            this.gridBand9.Columns.Add(this.bandedGridColumn38);
            this.gridBand9.Name = "gridBand9";
            this.gridBand9.Width = 225;
            // 
            // gridBand10
            // 
            this.gridBand10.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand10.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand10.Caption = "T网接力切换";
            this.gridBand10.Columns.Add(this.bandedGridColumn39);
            this.gridBand10.Columns.Add(this.bandedGridColumn40);
            this.gridBand10.Columns.Add(this.bandedGridColumn41);
            this.gridBand10.Name = "gridBand10";
            this.gridBand10.Width = 225;
            // 
            // gridBand11
            // 
            this.gridBand11.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand11.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand11.Caption = "T网重选";
            this.gridBand11.Columns.Add(this.bandedGridColumn42);
            this.gridBand11.Columns.Add(this.bandedGridColumn43);
            this.gridBand11.Name = "gridBand11";
            this.gridBand11.Width = 150;
            // 
            // gridBand12
            // 
            this.gridBand12.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand12.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand12.Caption = "T至G重选";
            this.gridBand12.Columns.Add(this.bandedGridColumn44);
            this.gridBand12.Columns.Add(this.bandedGridColumn45);
            this.gridBand12.Columns.Add(this.bandedGridColumn46);
            this.gridBand12.Columns.Add(this.bandedGridColumn47);
            this.gridBand12.Name = "gridBand12";
            this.gridBand12.Width = 300;
            // 
            // gridBand13
            // 
            this.gridBand13.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand13.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand13.Caption = "其它";
            this.gridBand13.Columns.Add(this.bandedGridColumn48);
            this.gridBand13.Columns.Add(this.bandedGridColumn49);
            this.gridBand13.Columns.Add(this.bandedGridColumn50);
            this.gridBand13.Columns.Add(this.bandedGridColumn51);
            this.gridBand13.Columns.Add(this.bandedGridColumn52);
            this.gridBand13.Columns.Add(this.bandedGridColumn53);
            this.gridBand13.Name = "gridBand13";
            this.gridBand13.Width = 450;
            // 
            // bandedGridColumn65
            // 
            this.bandedGridColumn65.Caption = "距离(米)";
            this.bandedGridColumn65.CustomizationCaption = "距离(米)";
            this.bandedGridColumn65.FieldName = "Idistance";
            this.bandedGridColumn65.Name = "bandedGridColumn65";
            this.bandedGridColumn65.Visible = true;
            // 
            // bandedGridColumn66
            // 
            this.bandedGridColumn66.Caption = "时长(秒)";
            this.bandedGridColumn66.CustomizationCaption = "时长(秒)";
            this.bandedGridColumn66.FieldName = "Iduration";
            this.bandedGridColumn66.Name = "bandedGridColumn66";
            this.bandedGridColumn66.Visible = true;
            // 
            // bandedGridColumn67
            // 
            this.bandedGridColumn67.Caption = "0";
            this.bandedGridColumn67.CustomizationCaption = "下载速率0";
            this.bandedGridColumn67.FieldName = "APP_Speed0";
            this.bandedGridColumn67.Name = "bandedGridColumn67";
            this.bandedGridColumn67.Visible = true;
            // 
            // bandedGridColumn68
            // 
            this.bandedGridColumn68.Caption = "[0,500]";
            this.bandedGridColumn68.CustomizationCaption = "下载速率[0,500]";
            this.bandedGridColumn68.FieldName = "APP_Speed0_500";
            this.bandedGridColumn68.Name = "bandedGridColumn68";
            this.bandedGridColumn68.Visible = true;
            // 
            // bandedGridColumn69
            // 
            this.bandedGridColumn69.Caption = "[500,800]";
            this.bandedGridColumn69.CustomizationCaption = "下载速率[500,800]";
            this.bandedGridColumn69.FieldName = "APP_Speed500_800";
            this.bandedGridColumn69.Name = "bandedGridColumn69";
            this.bandedGridColumn69.Visible = true;
            // 
            // bandedGridColumn70
            // 
            this.bandedGridColumn70.Caption = "[800,900]";
            this.bandedGridColumn70.CustomizationCaption = "下载速率[800,900]";
            this.bandedGridColumn70.FieldName = "APP_Speed800_900";
            this.bandedGridColumn70.Name = "bandedGridColumn70";
            this.bandedGridColumn70.Visible = true;
            // 
            // bandedGridColumn71
            // 
            this.bandedGridColumn71.Caption = "[900,1100]";
            this.bandedGridColumn71.CustomizationCaption = "下载速率[900,1100]";
            this.bandedGridColumn71.FieldName = "APP_Speed900_1100";
            this.bandedGridColumn71.Name = "bandedGridColumn71";
            this.bandedGridColumn71.Visible = true;
            // 
            // bandedGridColumn72
            // 
            this.bandedGridColumn72.Caption = "1100";
            this.bandedGridColumn72.CustomizationCaption = "下载速率1100";
            this.bandedGridColumn72.FieldName = "APP_Speed1100";
            this.bandedGridColumn72.Name = "bandedGridColumn72";
            this.bandedGridColumn72.Visible = true;
            // 
            // bandedGridColumn73
            // 
            this.bandedGridColumn73.Caption = "均值";
            this.bandedGridColumn73.CustomizationCaption = "TD电平均值";
            this.bandedGridColumn73.FieldName = "Rscpmean";
            this.bandedGridColumn73.Name = "bandedGridColumn73";
            this.bandedGridColumn73.Visible = true;
            // 
            // bandedGridColumn74
            // 
            this.bandedGridColumn74.Caption = "小于75";
            this.bandedGridColumn74.CustomizationCaption = "TD电平小于75";
            this.bandedGridColumn74.FieldName = "Rscp75";
            this.bandedGridColumn74.Name = "bandedGridColumn74";
            this.bandedGridColumn74.Visible = true;
            // 
            // bandedGridColumn75
            // 
            this.bandedGridColumn75.Caption = "[76,80]";
            this.bandedGridColumn75.CustomizationCaption = "TD电平[76,80]";
            this.bandedGridColumn75.FieldName = "Rscp76_80";
            this.bandedGridColumn75.Name = "bandedGridColumn75";
            this.bandedGridColumn75.Visible = true;
            // 
            // bandedGridColumn76
            // 
            this.bandedGridColumn76.Caption = "[81,85]";
            this.bandedGridColumn76.CustomizationCaption = "TD电平[81,85]";
            this.bandedGridColumn76.FieldName = "Rscp81_85";
            this.bandedGridColumn76.Name = "bandedGridColumn76";
            this.bandedGridColumn76.Visible = true;
            // 
            // bandedGridColumn77
            // 
            this.bandedGridColumn77.Caption = "[86,90]";
            this.bandedGridColumn77.CustomizationCaption = "TD电平[86,90]";
            this.bandedGridColumn77.FieldName = "Rscp86_90";
            this.bandedGridColumn77.Name = "bandedGridColumn77";
            this.bandedGridColumn77.Visible = true;
            // 
            // bandedGridColumn78
            // 
            this.bandedGridColumn78.Caption = "[91,94]";
            this.bandedGridColumn78.CustomizationCaption = "TD电平[91,94]";
            this.bandedGridColumn78.FieldName = "Rscp91_94";
            this.bandedGridColumn78.Name = "bandedGridColumn78";
            this.bandedGridColumn78.Visible = true;
            // 
            // bandedGridColumn79
            // 
            this.bandedGridColumn79.Caption = "大于94";
            this.bandedGridColumn79.CustomizationCaption = "TD电平大于94";
            this.bandedGridColumn79.FieldName = "Rscp94";
            this.bandedGridColumn79.Name = "bandedGridColumn79";
            this.bandedGridColumn79.Visible = true;
            // 
            // bandedGridColumn80
            // 
            this.bandedGridColumn80.Caption = "均值";
            this.bandedGridColumn80.CustomizationCaption = "GSM电平均值";
            this.bandedGridColumn80.FieldName = "Rxlmean";
            this.bandedGridColumn80.Name = "bandedGridColumn80";
            this.bandedGridColumn80.Visible = true;
            // 
            // bandedGridColumn81
            // 
            this.bandedGridColumn81.Caption = "小于75";
            this.bandedGridColumn81.CustomizationCaption = "GSM电平小于75";
            this.bandedGridColumn81.FieldName = "Rxl75";
            this.bandedGridColumn81.Name = "bandedGridColumn81";
            this.bandedGridColumn81.Visible = true;
            // 
            // bandedGridColumn82
            // 
            this.bandedGridColumn82.Caption = "[76,80]";
            this.bandedGridColumn82.CustomizationCaption = "GSM电平[76,80]";
            this.bandedGridColumn82.FieldName = "Rxl76_80";
            this.bandedGridColumn82.Name = "bandedGridColumn82";
            this.bandedGridColumn82.Visible = true;
            // 
            // bandedGridColumn83
            // 
            this.bandedGridColumn83.Caption = "[81,85]";
            this.bandedGridColumn83.CustomizationCaption = "GSM电平[81,85]";
            this.bandedGridColumn83.FieldName = "Rxl81_85";
            this.bandedGridColumn83.Name = "bandedGridColumn83";
            this.bandedGridColumn83.Visible = true;
            // 
            // bandedGridColumn84
            // 
            this.bandedGridColumn84.Caption = "[86,90]";
            this.bandedGridColumn84.CustomizationCaption = "GSM电平[86,90]";
            this.bandedGridColumn84.FieldName = "Rxl86_90";
            this.bandedGridColumn84.Name = "bandedGridColumn84";
            this.bandedGridColumn84.Visible = true;
            // 
            // bandedGridColumn85
            // 
            this.bandedGridColumn85.Caption = "[91,94]";
            this.bandedGridColumn85.CustomizationCaption = "GSM电平[91,94]";
            this.bandedGridColumn85.FieldName = "Rxl91_94";
            this.bandedGridColumn85.Name = "bandedGridColumn85";
            this.bandedGridColumn85.Visible = true;
            // 
            // bandedGridColumn86
            // 
            this.bandedGridColumn86.Caption = "大于94";
            this.bandedGridColumn86.CustomizationCaption = "GSM电平大于94";
            this.bandedGridColumn86.FieldName = "Rxl94";
            this.bandedGridColumn86.Name = "bandedGridColumn86";
            this.bandedGridColumn86.Visible = true;
            // 
            // bandedGridColumn87
            // 
            this.bandedGridColumn87.Caption = "均值";
            this.bandedGridColumn87.CustomizationCaption = "DPCH C/I均值";
            this.bandedGridColumn87.FieldName = "Dc2imean";
            this.bandedGridColumn87.Name = "bandedGridColumn87";
            this.bandedGridColumn87.Visible = true;
            // 
            // bandedGridColumn88
            // 
            this.bandedGridColumn88.Caption = "小于-10";
            this.bandedGridColumn88.CustomizationCaption = "DPCH C/I小于-10";
            this.bandedGridColumn88.FieldName = "Dc2iF10";
            this.bandedGridColumn88.Name = "bandedGridColumn88";
            this.bandedGridColumn88.Visible = true;
            // 
            // bandedGridColumn89
            // 
            this.bandedGridColumn89.Caption = "[-10,-3]";
            this.bandedGridColumn89.CustomizationCaption = "DPCH C/I[-10,-3]";
            this.bandedGridColumn89.FieldName = "Dc2iF10TF3";
            this.bandedGridColumn89.Name = "bandedGridColumn89";
            this.bandedGridColumn89.Visible = true;
            // 
            // bandedGridColumn90
            // 
            this.bandedGridColumn90.Caption = "[-3,15]";
            this.bandedGridColumn90.CustomizationCaption = "DPCH C/I[-3,15]";
            this.bandedGridColumn90.FieldName = "Dc2iF3T15";
            this.bandedGridColumn90.Name = "bandedGridColumn90";
            this.bandedGridColumn90.Visible = true;
            // 
            // bandedGridColumn91
            // 
            this.bandedGridColumn91.Caption = "大于15";
            this.bandedGridColumn91.CustomizationCaption = "DPCH C/I大于15";
            this.bandedGridColumn91.FieldName = "Dc2i15";
            this.bandedGridColumn91.Name = "bandedGridColumn91";
            this.bandedGridColumn91.Visible = true;
            // 
            // bandedGridColumn92
            // 
            this.bandedGridColumn92.Caption = "均值";
            this.bandedGridColumn92.CustomizationCaption = "TxPower均值";
            this.bandedGridColumn92.FieldName = "Txpowermean";
            this.bandedGridColumn92.Name = "bandedGridColumn92";
            this.bandedGridColumn92.Visible = true;
            // 
            // bandedGridColumn93
            // 
            this.bandedGridColumn93.Caption = "小于-20";
            this.bandedGridColumn93.CustomizationCaption = "TxPower小于-20";
            this.bandedGridColumn93.FieldName = "TxpowerF20";
            this.bandedGridColumn93.Name = "bandedGridColumn93";
            this.bandedGridColumn93.Visible = true;
            // 
            // bandedGridColumn94
            // 
            this.bandedGridColumn94.Caption = "[-20,0]";
            this.bandedGridColumn94.CustomizationCaption = "TxPower[-20,0]";
            this.bandedGridColumn94.FieldName = "TxpowerF20T0";
            this.bandedGridColumn94.Name = "bandedGridColumn94";
            this.bandedGridColumn94.Visible = true;
            // 
            // bandedGridColumn95
            // 
            this.bandedGridColumn95.Caption = "[0,15]";
            this.bandedGridColumn95.CustomizationCaption = "TxPower[0,15]";
            this.bandedGridColumn95.FieldName = "Txpower0T15";
            this.bandedGridColumn95.Name = "bandedGridColumn95";
            this.bandedGridColumn95.Visible = true;
            // 
            // bandedGridColumn96
            // 
            this.bandedGridColumn96.Caption = "大于15";
            this.bandedGridColumn96.CustomizationCaption = "TxPower大于15";
            this.bandedGridColumn96.FieldName = "Txpower15";
            this.bandedGridColumn96.Name = "bandedGridColumn96";
            this.bandedGridColumn96.Visible = true;
            // 
            // bandedGridColumn97
            // 
            this.bandedGridColumn97.Caption = "成功次数";
            this.bandedGridColumn97.CustomizationCaption = "T至G切换成功次数";
            this.bandedGridColumn97.FieldName = "HO_SUCC_T2G";
            this.bandedGridColumn97.Name = "bandedGridColumn97";
            this.bandedGridColumn97.Visible = true;
            // 
            // bandedGridColumn98
            // 
            this.bandedGridColumn98.Caption = "失败次数";
            this.bandedGridColumn98.CustomizationCaption = "T至G切换失败次数";
            this.bandedGridColumn98.FieldName = "HO_FAIL_T2G";
            this.bandedGridColumn98.Name = "bandedGridColumn98";
            this.bandedGridColumn98.Visible = true;
            // 
            // bandedGridColumn99
            // 
            this.bandedGridColumn99.Caption = "时延";
            this.bandedGridColumn99.CustomizationCaption = "T至G切换时延";
            this.bandedGridColumn99.FieldName = "HO_Time_T2G";
            this.bandedGridColumn99.Name = "bandedGridColumn99";
            this.bandedGridColumn99.Visible = true;
            // 
            // bandedGridColumn100
            // 
            this.bandedGridColumn100.Caption = "成功次数";
            this.bandedGridColumn100.CustomizationCaption = "T网硬切换成功次数";
            this.bandedGridColumn100.FieldName = "HO_SUCC_T2T";
            this.bandedGridColumn100.Name = "bandedGridColumn100";
            this.bandedGridColumn100.Visible = true;
            // 
            // bandedGridColumn101
            // 
            this.bandedGridColumn101.Caption = "失败次数";
            this.bandedGridColumn101.CustomizationCaption = "T网硬切换失败次数";
            this.bandedGridColumn101.FieldName = "HO_FAIL_T2T";
            this.bandedGridColumn101.Name = "bandedGridColumn101";
            this.bandedGridColumn101.Visible = true;
            // 
            // bandedGridColumn102
            // 
            this.bandedGridColumn102.Caption = "时延";
            this.bandedGridColumn102.CustomizationCaption = "T网硬切换时延";
            this.bandedGridColumn102.FieldName = "HO_Time_T2T";
            this.bandedGridColumn102.Name = "bandedGridColumn102";
            this.bandedGridColumn102.Visible = true;
            // 
            // bandedGridColumn103
            // 
            this.bandedGridColumn103.Caption = "成功次数";
            this.bandedGridColumn103.CustomizationCaption = "T网接力切换成功次数";
            this.bandedGridColumn103.FieldName = "HO_SUCC_Baton";
            this.bandedGridColumn103.Name = "bandedGridColumn103";
            this.bandedGridColumn103.Visible = true;
            // 
            // bandedGridColumn104
            // 
            this.bandedGridColumn104.Caption = "失败次数";
            this.bandedGridColumn104.CustomizationCaption = "T网接力切换失败次数";
            this.bandedGridColumn104.FieldName = "HO_FAIL_Baton";
            this.bandedGridColumn104.Name = "bandedGridColumn104";
            this.bandedGridColumn104.Visible = true;
            // 
            // bandedGridColumn105
            // 
            this.bandedGridColumn105.Caption = "时延";
            this.bandedGridColumn105.CustomizationCaption = "T网接力切换时延";
            this.bandedGridColumn105.FieldName = "HO_Time_Baton";
            this.bandedGridColumn105.Name = "bandedGridColumn105";
            this.bandedGridColumn105.Visible = true;
            // 
            // bandedGridColumn106
            // 
            this.bandedGridColumn106.Caption = "成功次数";
            this.bandedGridColumn106.CustomizationCaption = "T网重选成功次数";
            this.bandedGridColumn106.FieldName = "CR_SUCC_T2T";
            this.bandedGridColumn106.Name = "bandedGridColumn106";
            this.bandedGridColumn106.Visible = true;
            // 
            // bandedGridColumn107
            // 
            this.bandedGridColumn107.Caption = "失败次数";
            this.bandedGridColumn107.CustomizationCaption = "T网重选失败次数";
            this.bandedGridColumn107.FieldName = "CR_FAIL_T2T";
            this.bandedGridColumn107.Name = "bandedGridColumn107";
            this.bandedGridColumn107.Visible = true;
            // 
            // bandedGridColumn108
            // 
            this.bandedGridColumn108.Caption = "成功次数";
            this.bandedGridColumn108.CustomizationCaption = "T至G重选成功次数";
            this.bandedGridColumn108.FieldName = "CR_SUCC_T2G";
            this.bandedGridColumn108.Name = "bandedGridColumn108";
            this.bandedGridColumn108.Visible = true;
            // 
            // bandedGridColumn109
            // 
            this.bandedGridColumn109.Caption = "失败次数";
            this.bandedGridColumn109.CustomizationCaption = "T至G重选失败次数";
            this.bandedGridColumn109.FieldName = "CR_FAIL_T2G";
            this.bandedGridColumn109.Name = "bandedGridColumn109";
            this.bandedGridColumn109.Visible = true;
            // 
            // bandedGridColumn110
            // 
            this.bandedGridColumn110.Caption = "成功次数";
            this.bandedGridColumn110.CustomizationCaption = "G至T重选成功次数";
            this.bandedGridColumn110.FieldName = "CR_SUCC_G2T";
            this.bandedGridColumn110.Name = "bandedGridColumn110";
            this.bandedGridColumn110.Visible = true;
            // 
            // bandedGridColumn111
            // 
            this.bandedGridColumn111.Caption = "失败次数";
            this.bandedGridColumn111.CustomizationCaption = "G至T重选失败次数";
            this.bandedGridColumn111.FieldName = "CR_FAIL_G2T";
            this.bandedGridColumn111.Name = "bandedGridColumn111";
            this.bandedGridColumn111.Visible = true;
            // 
            // bandedGridColumn112
            // 
            this.bandedGridColumn112.Caption = "Hsdpa下载量占比";
            this.bandedGridColumn112.CustomizationCaption = "Hsdpa下载量占比";
            this.bandedGridColumn112.FieldName = "HSDPA_Size";
            this.bandedGridColumn112.Name = "bandedGridColumn112";
            this.bandedGridColumn112.Visible = true;
            // 
            // bandedGridColumn113
            // 
            this.bandedGridColumn113.Caption = "Hsdpa时长占比";
            this.bandedGridColumn113.CustomizationCaption = "Hsdpa时长占比";
            this.bandedGridColumn113.FieldName = "HSDPA_Time";
            this.bandedGridColumn113.Name = "bandedGridColumn113";
            this.bandedGridColumn113.Visible = true;
            // 
            // bandedGridColumn114
            // 
            this.bandedGridColumn114.Caption = "R4下载量占比";
            this.bandedGridColumn114.CustomizationCaption = "R4下载量占比";
            this.bandedGridColumn114.FieldName = "R4_Size";
            this.bandedGridColumn114.Name = "bandedGridColumn114";
            this.bandedGridColumn114.Visible = true;
            // 
            // bandedGridColumn115
            // 
            this.bandedGridColumn115.Caption = "R4时长占比";
            this.bandedGridColumn115.CustomizationCaption = "R4时长占比";
            this.bandedGridColumn115.FieldName = "R4_Time";
            this.bandedGridColumn115.Name = "bandedGridColumn115";
            this.bandedGridColumn115.Visible = true;
            // 
            // bandedGridColumn116
            // 
            this.bandedGridColumn116.Caption = "Gsm下载量占比";
            this.bandedGridColumn116.CustomizationCaption = "Gsm下载量占比";
            this.bandedGridColumn116.FieldName = "EDGE_Size";
            this.bandedGridColumn116.Name = "bandedGridColumn116";
            this.bandedGridColumn116.Visible = true;
            // 
            // bandedGridColumn117
            // 
            this.bandedGridColumn117.Caption = "Gsm时长占比";
            this.bandedGridColumn117.CustomizationCaption = "Gsm时长占比";
            this.bandedGridColumn117.FieldName = "EDGE_Time";
            this.bandedGridColumn117.Name = "bandedGridColumn117";
            this.bandedGridColumn117.Visible = true;
            // 
            // bandedGridColumn118
            // 
            this.bandedGridColumn118.Caption = "TOP1小区";
            this.bandedGridColumn118.CustomizationCaption = "TOP1小区";
            this.bandedGridColumn118.FieldName = "Cell1";
            this.bandedGridColumn118.Name = "bandedGridColumn118";
            this.bandedGridColumn118.Visible = true;
            this.bandedGridColumn118.Width = 85;
            // 
            // bandedGridColumn119
            // 
            this.bandedGridColumn119.Caption = "TOP2小区";
            this.bandedGridColumn119.CustomizationCaption = "TOP2小区";
            this.bandedGridColumn119.FieldName = "Cell2";
            this.bandedGridColumn119.Name = "bandedGridColumn119";
            this.bandedGridColumn119.Visible = true;
            this.bandedGridColumn119.Width = 85;
            // 
            // bandedGridColumn120
            // 
            this.bandedGridColumn120.Caption = "TOP3小区";
            this.bandedGridColumn120.CustomizationCaption = "TOP3小区";
            this.bandedGridColumn120.FieldName = "Cell3";
            this.bandedGridColumn120.Name = "bandedGridColumn120";
            this.bandedGridColumn120.Visible = true;
            this.bandedGridColumn120.Width = 85;
            // 
            // bandedGridColumn121
            // 
            this.bandedGridColumn121.Caption = "所属网格";
            this.bandedGridColumn121.CustomizationCaption = "所属网格";
            this.bandedGridColumn121.FieldName = "Strgrid";
            this.bandedGridColumn121.Name = "bandedGridColumn121";
            this.bandedGridColumn121.Visible = true;
            // 
            // bandedGridColumn122
            // 
            this.bandedGridColumn122.Caption = "所属道路";
            this.bandedGridColumn122.CustomizationCaption = "所属道路";
            this.bandedGridColumn122.FieldName = "Strroad";
            this.bandedGridColumn122.Name = "bandedGridColumn122";
            this.bandedGridColumn122.Visible = true;
            // 
            // bandedGridColumn123
            // 
            this.bandedGridColumn123.Caption = "经度";
            this.bandedGridColumn123.CustomizationCaption = "经度";
            this.bandedGridColumn123.FieldName = "Imlongitude";
            this.bandedGridColumn123.Name = "bandedGridColumn123";
            this.bandedGridColumn123.Visible = true;
            // 
            // bandedGridColumn124
            // 
            this.bandedGridColumn124.Caption = "纬度";
            this.bandedGridColumn124.CustomizationCaption = "纬度";
            this.bandedGridColumn124.FieldName = "Imlatitude";
            this.bandedGridColumn124.Name = "bandedGridColumn124";
            this.bandedGridColumn124.Visible = true;
            // 
            // bandedGridColumn125
            // 
            this.bandedGridColumn125.Caption = "ifileid";
            this.bandedGridColumn125.CustomizationCaption = "ifileid";
            this.bandedGridColumn125.FieldName = "Ifileid";
            this.bandedGridColumn125.Name = "bandedGridColumn125";
            // 
            // bandedGridColumn126
            // 
            this.bandedGridColumn126.Caption = "istime";
            this.bandedGridColumn126.CustomizationCaption = "istime";
            this.bandedGridColumn126.FieldName = "Istime";
            this.bandedGridColumn126.Name = "bandedGridColumn126";
            // 
            // bandedGridColumn127
            // 
            this.bandedGridColumn127.Caption = "ietime";
            this.bandedGridColumn127.CustomizationCaption = "ietime";
            this.bandedGridColumn127.FieldName = "Ietime";
            this.bandedGridColumn127.Name = "bandedGridColumn127";
            // 
            // bandedGridColumn128
            // 
            this.bandedGridColumn128.Caption = "gridColumn324";
            this.bandedGridColumn128.CustomizationCaption = "gridColumn324";
            this.bandedGridColumn128.FieldName = "Iid";
            this.bandedGridColumn128.Name = "bandedGridColumn128";
            // 
            // gridBand2
            // 
            this.gridBand2.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand2.Caption = "基础信息";
            this.gridBand2.Columns.Add(this.bandedGridColumn121);
            this.gridBand2.Columns.Add(this.bandedGridColumn122);
            this.gridBand2.Columns.Add(this.bandedGridColumn118);
            this.gridBand2.Columns.Add(this.bandedGridColumn119);
            this.gridBand2.Columns.Add(this.bandedGridColumn120);
            this.gridBand2.Columns.Add(this.bandedGridColumn123);
            this.gridBand2.Columns.Add(this.bandedGridColumn124);
            this.gridBand2.MinWidth = 20;
            this.gridBand2.Name = "gridBand2";
            this.gridBand2.Width = 555;
            // 
            // gridBand18
            // 
            this.gridBand18.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand18.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand18.Caption = "测试信息";
            this.gridBand18.Columns.Add(this.bandedGridColumn65);
            this.gridBand18.Columns.Add(this.bandedGridColumn66);
            this.gridBand18.Columns.Add(this.bandedGridColumn125);
            this.gridBand18.Columns.Add(this.bandedGridColumn126);
            this.gridBand18.Columns.Add(this.bandedGridColumn127);
            this.gridBand18.Columns.Add(this.bandedGridColumn128);
            this.gridBand18.MinWidth = 20;
            this.gridBand18.Name = "gridBand18";
            this.gridBand18.Width = 150;
            // 
            // gridBand19
            // 
            this.gridBand19.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand19.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand19.Caption = "下载速率";
            this.gridBand19.Columns.Add(this.bandedGridColumn67);
            this.gridBand19.Columns.Add(this.bandedGridColumn68);
            this.gridBand19.Columns.Add(this.bandedGridColumn69);
            this.gridBand19.Columns.Add(this.bandedGridColumn70);
            this.gridBand19.Columns.Add(this.bandedGridColumn71);
            this.gridBand19.Columns.Add(this.bandedGridColumn72);
            this.gridBand19.MinWidth = 20;
            this.gridBand19.Name = "gridBand19";
            this.gridBand19.Width = 450;
            // 
            // gridBand20
            // 
            this.gridBand20.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand20.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand20.Caption = "TD电平";
            this.gridBand20.Columns.Add(this.bandedGridColumn73);
            this.gridBand20.Columns.Add(this.bandedGridColumn74);
            this.gridBand20.Columns.Add(this.bandedGridColumn75);
            this.gridBand20.Columns.Add(this.bandedGridColumn76);
            this.gridBand20.Columns.Add(this.bandedGridColumn77);
            this.gridBand20.Columns.Add(this.bandedGridColumn78);
            this.gridBand20.Columns.Add(this.bandedGridColumn79);
            this.gridBand20.MinWidth = 20;
            this.gridBand20.Name = "gridBand20";
            this.gridBand20.Width = 525;
            // 
            // gridBand21
            // 
            this.gridBand21.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand21.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand21.Caption = "GSM电平";
            this.gridBand21.Columns.Add(this.bandedGridColumn80);
            this.gridBand21.Columns.Add(this.bandedGridColumn81);
            this.gridBand21.Columns.Add(this.bandedGridColumn82);
            this.gridBand21.Columns.Add(this.bandedGridColumn83);
            this.gridBand21.Columns.Add(this.bandedGridColumn84);
            this.gridBand21.Columns.Add(this.bandedGridColumn85);
            this.gridBand21.Columns.Add(this.bandedGridColumn86);
            this.gridBand21.MinWidth = 20;
            this.gridBand21.Name = "gridBand21";
            this.gridBand21.Width = 525;
            // 
            // gridBand22
            // 
            this.gridBand22.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand22.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand22.Caption = "DPCH C/I";
            this.gridBand22.Columns.Add(this.bandedGridColumn87);
            this.gridBand22.Columns.Add(this.bandedGridColumn88);
            this.gridBand22.Columns.Add(this.bandedGridColumn89);
            this.gridBand22.Columns.Add(this.bandedGridColumn90);
            this.gridBand22.Columns.Add(this.bandedGridColumn91);
            this.gridBand22.MinWidth = 20;
            this.gridBand22.Name = "gridBand22";
            this.gridBand22.Width = 375;
            // 
            // gridBand23
            // 
            this.gridBand23.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand23.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand23.Caption = "TxPower";
            this.gridBand23.Columns.Add(this.bandedGridColumn92);
            this.gridBand23.Columns.Add(this.bandedGridColumn93);
            this.gridBand23.Columns.Add(this.bandedGridColumn94);
            this.gridBand23.Columns.Add(this.bandedGridColumn95);
            this.gridBand23.Columns.Add(this.bandedGridColumn96);
            this.gridBand23.MinWidth = 20;
            this.gridBand23.Name = "gridBand23";
            this.gridBand23.Width = 375;
            // 
            // gridBand24
            // 
            this.gridBand24.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand24.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand24.Caption = "T至G切换";
            this.gridBand24.Columns.Add(this.bandedGridColumn97);
            this.gridBand24.Columns.Add(this.bandedGridColumn98);
            this.gridBand24.Columns.Add(this.bandedGridColumn99);
            this.gridBand24.MinWidth = 20;
            this.gridBand24.Name = "gridBand24";
            this.gridBand24.Width = 225;
            // 
            // gridBand25
            // 
            this.gridBand25.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand25.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand25.Caption = "T网硬切换";
            this.gridBand25.Columns.Add(this.bandedGridColumn100);
            this.gridBand25.Columns.Add(this.bandedGridColumn101);
            this.gridBand25.Columns.Add(this.bandedGridColumn102);
            this.gridBand25.MinWidth = 20;
            this.gridBand25.Name = "gridBand25";
            this.gridBand25.Width = 225;
            // 
            // gridBand26
            // 
            this.gridBand26.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand26.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand26.Caption = "T网接力切换";
            this.gridBand26.Columns.Add(this.bandedGridColumn103);
            this.gridBand26.Columns.Add(this.bandedGridColumn104);
            this.gridBand26.Columns.Add(this.bandedGridColumn105);
            this.gridBand26.MinWidth = 20;
            this.gridBand26.Name = "gridBand26";
            this.gridBand26.Width = 225;
            // 
            // gridBand27
            // 
            this.gridBand27.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand27.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand27.Caption = "T网重选";
            this.gridBand27.Columns.Add(this.bandedGridColumn106);
            this.gridBand27.Columns.Add(this.bandedGridColumn107);
            this.gridBand27.MinWidth = 20;
            this.gridBand27.Name = "gridBand27";
            this.gridBand27.Width = 150;
            // 
            // gridBand28
            // 
            this.gridBand28.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand28.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand28.Caption = "T至G重选";
            this.gridBand28.Columns.Add(this.bandedGridColumn108);
            this.gridBand28.Columns.Add(this.bandedGridColumn109);
            this.gridBand28.Columns.Add(this.bandedGridColumn110);
            this.gridBand28.Columns.Add(this.bandedGridColumn111);
            this.gridBand28.MinWidth = 20;
            this.gridBand28.Name = "gridBand28";
            this.gridBand28.Width = 300;
            // 
            // gridBand29
            // 
            this.gridBand29.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand29.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand29.Caption = "其它";
            this.gridBand29.Columns.Add(this.bandedGridColumn112);
            this.gridBand29.Columns.Add(this.bandedGridColumn113);
            this.gridBand29.Columns.Add(this.bandedGridColumn114);
            this.gridBand29.Columns.Add(this.bandedGridColumn115);
            this.gridBand29.Columns.Add(this.bandedGridColumn116);
            this.gridBand29.Columns.Add(this.bandedGridColumn117);
            this.gridBand29.MinWidth = 20;
            this.gridBand29.Name = "gridBand29";
            this.gridBand29.Width = 450;
            // 
            // bandedGridColumn129
            // 
            this.bandedGridColumn129.Caption = "距离(米)";
            this.bandedGridColumn129.CustomizationCaption = "距离(米)";
            this.bandedGridColumn129.FieldName = "Idistance";
            this.bandedGridColumn129.Name = "bandedGridColumn129";
            this.bandedGridColumn129.Visible = true;
            // 
            // bandedGridColumn130
            // 
            this.bandedGridColumn130.Caption = "时长(秒)";
            this.bandedGridColumn130.CustomizationCaption = "时长(秒)";
            this.bandedGridColumn130.FieldName = "Iduration";
            this.bandedGridColumn130.Name = "bandedGridColumn130";
            this.bandedGridColumn130.Visible = true;
            // 
            // bandedGridColumn131
            // 
            this.bandedGridColumn131.Caption = "0";
            this.bandedGridColumn131.CustomizationCaption = "下载速率0";
            this.bandedGridColumn131.FieldName = "APP_Speed0";
            this.bandedGridColumn131.Name = "bandedGridColumn131";
            this.bandedGridColumn131.Visible = true;
            // 
            // bandedGridColumn132
            // 
            this.bandedGridColumn132.Caption = "[0,500]";
            this.bandedGridColumn132.CustomizationCaption = "下载速率[0,500]";
            this.bandedGridColumn132.FieldName = "APP_Speed0_500";
            this.bandedGridColumn132.Name = "bandedGridColumn132";
            this.bandedGridColumn132.Visible = true;
            // 
            // bandedGridColumn133
            // 
            this.bandedGridColumn133.Caption = "[500,800]";
            this.bandedGridColumn133.CustomizationCaption = "下载速率[500,800]";
            this.bandedGridColumn133.FieldName = "APP_Speed500_800";
            this.bandedGridColumn133.Name = "bandedGridColumn133";
            this.bandedGridColumn133.Visible = true;
            // 
            // bandedGridColumn134
            // 
            this.bandedGridColumn134.Caption = "[800,900]";
            this.bandedGridColumn134.CustomizationCaption = "下载速率[800,900]";
            this.bandedGridColumn134.FieldName = "APP_Speed800_900";
            this.bandedGridColumn134.Name = "bandedGridColumn134";
            this.bandedGridColumn134.Visible = true;
            // 
            // bandedGridColumn135
            // 
            this.bandedGridColumn135.Caption = "[900,1100]";
            this.bandedGridColumn135.CustomizationCaption = "下载速率[900,1100]";
            this.bandedGridColumn135.FieldName = "APP_Speed900_1100";
            this.bandedGridColumn135.Name = "bandedGridColumn135";
            this.bandedGridColumn135.Visible = true;
            // 
            // bandedGridColumn136
            // 
            this.bandedGridColumn136.Caption = "1100";
            this.bandedGridColumn136.CustomizationCaption = "下载速率1100";
            this.bandedGridColumn136.FieldName = "APP_Speed1100";
            this.bandedGridColumn136.Name = "bandedGridColumn136";
            this.bandedGridColumn136.Visible = true;
            // 
            // bandedGridColumn137
            // 
            this.bandedGridColumn137.Caption = "均值";
            this.bandedGridColumn137.CustomizationCaption = "TD电平均值";
            this.bandedGridColumn137.FieldName = "Rscpmean";
            this.bandedGridColumn137.Name = "bandedGridColumn137";
            this.bandedGridColumn137.Visible = true;
            // 
            // bandedGridColumn138
            // 
            this.bandedGridColumn138.Caption = "小于75";
            this.bandedGridColumn138.CustomizationCaption = "TD电平小于75";
            this.bandedGridColumn138.FieldName = "Rscp75";
            this.bandedGridColumn138.Name = "bandedGridColumn138";
            this.bandedGridColumn138.Visible = true;
            // 
            // bandedGridColumn139
            // 
            this.bandedGridColumn139.Caption = "[76,80]";
            this.bandedGridColumn139.CustomizationCaption = "TD电平[76,80]";
            this.bandedGridColumn139.FieldName = "Rscp76_80";
            this.bandedGridColumn139.Name = "bandedGridColumn139";
            this.bandedGridColumn139.Visible = true;
            // 
            // bandedGridColumn140
            // 
            this.bandedGridColumn140.Caption = "[81,85]";
            this.bandedGridColumn140.CustomizationCaption = "TD电平[81,85]";
            this.bandedGridColumn140.FieldName = "Rscp81_85";
            this.bandedGridColumn140.Name = "bandedGridColumn140";
            this.bandedGridColumn140.Visible = true;
            // 
            // bandedGridColumn141
            // 
            this.bandedGridColumn141.Caption = "[86,90]";
            this.bandedGridColumn141.CustomizationCaption = "TD电平[86,90]";
            this.bandedGridColumn141.FieldName = "Rscp86_90";
            this.bandedGridColumn141.Name = "bandedGridColumn141";
            this.bandedGridColumn141.Visible = true;
            // 
            // bandedGridColumn142
            // 
            this.bandedGridColumn142.Caption = "[91,94]";
            this.bandedGridColumn142.CustomizationCaption = "TD电平[91,94]";
            this.bandedGridColumn142.FieldName = "Rscp91_94";
            this.bandedGridColumn142.Name = "bandedGridColumn142";
            this.bandedGridColumn142.Visible = true;
            // 
            // bandedGridColumn143
            // 
            this.bandedGridColumn143.Caption = "大于94";
            this.bandedGridColumn143.CustomizationCaption = "TD电平大于94";
            this.bandedGridColumn143.FieldName = "Rscp94";
            this.bandedGridColumn143.Name = "bandedGridColumn143";
            this.bandedGridColumn143.Visible = true;
            // 
            // bandedGridColumn144
            // 
            this.bandedGridColumn144.Caption = "均值";
            this.bandedGridColumn144.CustomizationCaption = "GSM电平均值";
            this.bandedGridColumn144.FieldName = "Rxlmean";
            this.bandedGridColumn144.Name = "bandedGridColumn144";
            this.bandedGridColumn144.Visible = true;
            // 
            // bandedGridColumn145
            // 
            this.bandedGridColumn145.Caption = "小于75";
            this.bandedGridColumn145.CustomizationCaption = "GSM电平小于75";
            this.bandedGridColumn145.FieldName = "Rxl75";
            this.bandedGridColumn145.Name = "bandedGridColumn145";
            this.bandedGridColumn145.Visible = true;
            // 
            // bandedGridColumn146
            // 
            this.bandedGridColumn146.Caption = "[76,80]";
            this.bandedGridColumn146.CustomizationCaption = "GSM电平[76,80]";
            this.bandedGridColumn146.FieldName = "Rxl76_80";
            this.bandedGridColumn146.Name = "bandedGridColumn146";
            this.bandedGridColumn146.Visible = true;
            // 
            // bandedGridColumn147
            // 
            this.bandedGridColumn147.Caption = "[81,85]";
            this.bandedGridColumn147.CustomizationCaption = "GSM电平[81,85]";
            this.bandedGridColumn147.FieldName = "Rxl81_85";
            this.bandedGridColumn147.Name = "bandedGridColumn147";
            this.bandedGridColumn147.Visible = true;
            // 
            // bandedGridColumn148
            // 
            this.bandedGridColumn148.Caption = "[86,90]";
            this.bandedGridColumn148.CustomizationCaption = "GSM电平[86,90]";
            this.bandedGridColumn148.FieldName = "Rxl86_90";
            this.bandedGridColumn148.Name = "bandedGridColumn148";
            this.bandedGridColumn148.Visible = true;
            // 
            // bandedGridColumn149
            // 
            this.bandedGridColumn149.Caption = "[91,94]";
            this.bandedGridColumn149.CustomizationCaption = "GSM电平[91,94]";
            this.bandedGridColumn149.FieldName = "Rxl91_94";
            this.bandedGridColumn149.Name = "bandedGridColumn149";
            this.bandedGridColumn149.Visible = true;
            // 
            // bandedGridColumn150
            // 
            this.bandedGridColumn150.Caption = "大于94";
            this.bandedGridColumn150.CustomizationCaption = "GSM电平大于94";
            this.bandedGridColumn150.FieldName = "Rxl94";
            this.bandedGridColumn150.Name = "bandedGridColumn150";
            this.bandedGridColumn150.Visible = true;
            // 
            // bandedGridColumn151
            // 
            this.bandedGridColumn151.Caption = "均值";
            this.bandedGridColumn151.CustomizationCaption = "DPCH C/I均值";
            this.bandedGridColumn151.FieldName = "Dc2imean";
            this.bandedGridColumn151.Name = "bandedGridColumn151";
            this.bandedGridColumn151.Visible = true;
            // 
            // bandedGridColumn152
            // 
            this.bandedGridColumn152.Caption = "小于-10";
            this.bandedGridColumn152.CustomizationCaption = "DPCH C/I小于-10";
            this.bandedGridColumn152.FieldName = "Dc2iF10";
            this.bandedGridColumn152.Name = "bandedGridColumn152";
            this.bandedGridColumn152.Visible = true;
            // 
            // bandedGridColumn153
            // 
            this.bandedGridColumn153.Caption = "[-10,-3]";
            this.bandedGridColumn153.CustomizationCaption = "DPCH C/I[-10,-3]";
            this.bandedGridColumn153.FieldName = "Dc2iF10TF3";
            this.bandedGridColumn153.Name = "bandedGridColumn153";
            this.bandedGridColumn153.Visible = true;
            // 
            // bandedGridColumn154
            // 
            this.bandedGridColumn154.Caption = "[-3,15]";
            this.bandedGridColumn154.CustomizationCaption = "DPCH C/I[-3,15]";
            this.bandedGridColumn154.FieldName = "Dc2iF3T15";
            this.bandedGridColumn154.Name = "bandedGridColumn154";
            this.bandedGridColumn154.Visible = true;
            // 
            // bandedGridColumn155
            // 
            this.bandedGridColumn155.Caption = "大于15";
            this.bandedGridColumn155.CustomizationCaption = "DPCH C/I大于15";
            this.bandedGridColumn155.FieldName = "Dc2i15";
            this.bandedGridColumn155.Name = "bandedGridColumn155";
            this.bandedGridColumn155.Visible = true;
            // 
            // bandedGridColumn156
            // 
            this.bandedGridColumn156.Caption = "均值";
            this.bandedGridColumn156.CustomizationCaption = "TxPower均值";
            this.bandedGridColumn156.FieldName = "Txpowermean";
            this.bandedGridColumn156.Name = "bandedGridColumn156";
            this.bandedGridColumn156.Visible = true;
            // 
            // bandedGridColumn157
            // 
            this.bandedGridColumn157.Caption = "小于-20";
            this.bandedGridColumn157.CustomizationCaption = "TxPower小于-20";
            this.bandedGridColumn157.FieldName = "TxpowerF20";
            this.bandedGridColumn157.Name = "bandedGridColumn157";
            this.bandedGridColumn157.Visible = true;
            // 
            // bandedGridColumn158
            // 
            this.bandedGridColumn158.Caption = "[-20,0]";
            this.bandedGridColumn158.CustomizationCaption = "TxPower[-20,0]";
            this.bandedGridColumn158.FieldName = "TxpowerF20T0";
            this.bandedGridColumn158.Name = "bandedGridColumn158";
            this.bandedGridColumn158.Visible = true;
            // 
            // bandedGridColumn159
            // 
            this.bandedGridColumn159.Caption = "[0,15]";
            this.bandedGridColumn159.CustomizationCaption = "TxPower[0,15]";
            this.bandedGridColumn159.FieldName = "Txpower0T15";
            this.bandedGridColumn159.Name = "bandedGridColumn159";
            this.bandedGridColumn159.Visible = true;
            // 
            // bandedGridColumn160
            // 
            this.bandedGridColumn160.Caption = "大于15";
            this.bandedGridColumn160.CustomizationCaption = "TxPower大于15";
            this.bandedGridColumn160.FieldName = "Txpower15";
            this.bandedGridColumn160.Name = "bandedGridColumn160";
            this.bandedGridColumn160.Visible = true;
            // 
            // bandedGridColumn161
            // 
            this.bandedGridColumn161.Caption = "成功次数";
            this.bandedGridColumn161.CustomizationCaption = "T至G切换成功次数";
            this.bandedGridColumn161.FieldName = "HO_SUCC_T2G";
            this.bandedGridColumn161.Name = "bandedGridColumn161";
            this.bandedGridColumn161.Visible = true;
            // 
            // bandedGridColumn162
            // 
            this.bandedGridColumn162.Caption = "失败次数";
            this.bandedGridColumn162.CustomizationCaption = "T至G切换失败次数";
            this.bandedGridColumn162.FieldName = "HO_FAIL_T2G";
            this.bandedGridColumn162.Name = "bandedGridColumn162";
            this.bandedGridColumn162.Visible = true;
            // 
            // bandedGridColumn163
            // 
            this.bandedGridColumn163.Caption = "时延";
            this.bandedGridColumn163.CustomizationCaption = "T至G切换时延";
            this.bandedGridColumn163.FieldName = "HO_Time_T2G";
            this.bandedGridColumn163.Name = "bandedGridColumn163";
            this.bandedGridColumn163.Visible = true;
            // 
            // bandedGridColumn164
            // 
            this.bandedGridColumn164.Caption = "成功次数";
            this.bandedGridColumn164.CustomizationCaption = "T网硬切换成功次数";
            this.bandedGridColumn164.FieldName = "HO_SUCC_T2T";
            this.bandedGridColumn164.Name = "bandedGridColumn164";
            this.bandedGridColumn164.Visible = true;
            // 
            // bandedGridColumn165
            // 
            this.bandedGridColumn165.Caption = "失败次数";
            this.bandedGridColumn165.CustomizationCaption = "T网硬切换失败次数";
            this.bandedGridColumn165.FieldName = "HO_FAIL_T2T";
            this.bandedGridColumn165.Name = "bandedGridColumn165";
            this.bandedGridColumn165.Visible = true;
            // 
            // bandedGridColumn166
            // 
            this.bandedGridColumn166.Caption = "时延";
            this.bandedGridColumn166.CustomizationCaption = "T网硬切换时延";
            this.bandedGridColumn166.FieldName = "HO_Time_T2T";
            this.bandedGridColumn166.Name = "bandedGridColumn166";
            this.bandedGridColumn166.Visible = true;
            // 
            // bandedGridColumn167
            // 
            this.bandedGridColumn167.Caption = "成功次数";
            this.bandedGridColumn167.CustomizationCaption = "T网接力切换成功次数";
            this.bandedGridColumn167.FieldName = "HO_SUCC_Baton";
            this.bandedGridColumn167.Name = "bandedGridColumn167";
            this.bandedGridColumn167.Visible = true;
            // 
            // bandedGridColumn168
            // 
            this.bandedGridColumn168.Caption = "失败次数";
            this.bandedGridColumn168.CustomizationCaption = "T网接力切换失败次数";
            this.bandedGridColumn168.FieldName = "HO_FAIL_Baton";
            this.bandedGridColumn168.Name = "bandedGridColumn168";
            this.bandedGridColumn168.Visible = true;
            // 
            // bandedGridColumn169
            // 
            this.bandedGridColumn169.Caption = "时延";
            this.bandedGridColumn169.CustomizationCaption = "T网接力切换时延";
            this.bandedGridColumn169.FieldName = "HO_Time_Baton";
            this.bandedGridColumn169.Name = "bandedGridColumn169";
            this.bandedGridColumn169.Visible = true;
            // 
            // bandedGridColumn170
            // 
            this.bandedGridColumn170.Caption = "成功次数";
            this.bandedGridColumn170.CustomizationCaption = "T网重选成功次数";
            this.bandedGridColumn170.FieldName = "CR_SUCC_T2T";
            this.bandedGridColumn170.Name = "bandedGridColumn170";
            this.bandedGridColumn170.Visible = true;
            // 
            // bandedGridColumn171
            // 
            this.bandedGridColumn171.Caption = "失败次数";
            this.bandedGridColumn171.CustomizationCaption = "T网重选失败次数";
            this.bandedGridColumn171.FieldName = "CR_FAIL_T2T";
            this.bandedGridColumn171.Name = "bandedGridColumn171";
            this.bandedGridColumn171.Visible = true;
            // 
            // bandedGridColumn172
            // 
            this.bandedGridColumn172.Caption = "成功次数";
            this.bandedGridColumn172.CustomizationCaption = "T至G重选成功次数";
            this.bandedGridColumn172.FieldName = "CR_SUCC_T2G";
            this.bandedGridColumn172.Name = "bandedGridColumn172";
            this.bandedGridColumn172.Visible = true;
            // 
            // bandedGridColumn173
            // 
            this.bandedGridColumn173.Caption = "失败次数";
            this.bandedGridColumn173.CustomizationCaption = "T至G重选失败次数";
            this.bandedGridColumn173.FieldName = "CR_FAIL_T2G";
            this.bandedGridColumn173.Name = "bandedGridColumn173";
            this.bandedGridColumn173.Visible = true;
            // 
            // bandedGridColumn174
            // 
            this.bandedGridColumn174.Caption = "成功次数";
            this.bandedGridColumn174.CustomizationCaption = "G至T重选成功次数";
            this.bandedGridColumn174.FieldName = "CR_SUCC_G2T";
            this.bandedGridColumn174.Name = "bandedGridColumn174";
            this.bandedGridColumn174.Visible = true;
            // 
            // bandedGridColumn175
            // 
            this.bandedGridColumn175.Caption = "失败次数";
            this.bandedGridColumn175.CustomizationCaption = "G至T重选失败次数";
            this.bandedGridColumn175.FieldName = "CR_FAIL_G2T";
            this.bandedGridColumn175.Name = "bandedGridColumn175";
            this.bandedGridColumn175.Visible = true;
            // 
            // bandedGridColumn176
            // 
            this.bandedGridColumn176.Caption = "Hsdpa下载量占比";
            this.bandedGridColumn176.CustomizationCaption = "Hsdpa下载量占比";
            this.bandedGridColumn176.FieldName = "HSDPA_Size";
            this.bandedGridColumn176.Name = "bandedGridColumn176";
            this.bandedGridColumn176.Visible = true;
            // 
            // bandedGridColumn177
            // 
            this.bandedGridColumn177.Caption = "Hsdpa时长占比";
            this.bandedGridColumn177.CustomizationCaption = "Hsdpa时长占比";
            this.bandedGridColumn177.FieldName = "HSDPA_Time";
            this.bandedGridColumn177.Name = "bandedGridColumn177";
            this.bandedGridColumn177.Visible = true;
            // 
            // bandedGridColumn178
            // 
            this.bandedGridColumn178.Caption = "R4下载量占比";
            this.bandedGridColumn178.CustomizationCaption = "R4下载量占比";
            this.bandedGridColumn178.FieldName = "R4_Size";
            this.bandedGridColumn178.Name = "bandedGridColumn178";
            this.bandedGridColumn178.Visible = true;
            // 
            // bandedGridColumn179
            // 
            this.bandedGridColumn179.Caption = "R4时长占比";
            this.bandedGridColumn179.CustomizationCaption = "R4时长占比";
            this.bandedGridColumn179.FieldName = "R4_Time";
            this.bandedGridColumn179.Name = "bandedGridColumn179";
            this.bandedGridColumn179.Visible = true;
            // 
            // bandedGridColumn180
            // 
            this.bandedGridColumn180.Caption = "Gsm下载量占比";
            this.bandedGridColumn180.CustomizationCaption = "Gsm下载量占比";
            this.bandedGridColumn180.FieldName = "EDGE_Size";
            this.bandedGridColumn180.Name = "bandedGridColumn180";
            this.bandedGridColumn180.Visible = true;
            // 
            // bandedGridColumn181
            // 
            this.bandedGridColumn181.Caption = "Gsm时长占比";
            this.bandedGridColumn181.CustomizationCaption = "Gsm时长占比";
            this.bandedGridColumn181.FieldName = "EDGE_Time";
            this.bandedGridColumn181.Name = "bandedGridColumn181";
            this.bandedGridColumn181.Visible = true;
            // 
            // bandedGridColumn182
            // 
            this.bandedGridColumn182.Caption = "TOP1小区";
            this.bandedGridColumn182.CustomizationCaption = "TOP1小区";
            this.bandedGridColumn182.FieldName = "Cell1";
            this.bandedGridColumn182.Name = "bandedGridColumn182";
            this.bandedGridColumn182.Visible = true;
            this.bandedGridColumn182.Width = 85;
            // 
            // bandedGridColumn183
            // 
            this.bandedGridColumn183.Caption = "TOP2小区";
            this.bandedGridColumn183.CustomizationCaption = "TOP2小区";
            this.bandedGridColumn183.FieldName = "Cell2";
            this.bandedGridColumn183.Name = "bandedGridColumn183";
            this.bandedGridColumn183.Visible = true;
            this.bandedGridColumn183.Width = 85;
            // 
            // bandedGridColumn184
            // 
            this.bandedGridColumn184.Caption = "TOP3小区";
            this.bandedGridColumn184.CustomizationCaption = "TOP3小区";
            this.bandedGridColumn184.FieldName = "Cell3";
            this.bandedGridColumn184.Name = "bandedGridColumn184";
            this.bandedGridColumn184.Visible = true;
            this.bandedGridColumn184.Width = 85;
            // 
            // bandedGridColumn185
            // 
            this.bandedGridColumn185.Caption = "所属网格";
            this.bandedGridColumn185.CustomizationCaption = "所属网格";
            this.bandedGridColumn185.FieldName = "Strgrid";
            this.bandedGridColumn185.Name = "bandedGridColumn185";
            this.bandedGridColumn185.Visible = true;
            // 
            // bandedGridColumn186
            // 
            this.bandedGridColumn186.Caption = "所属道路";
            this.bandedGridColumn186.CustomizationCaption = "所属道路";
            this.bandedGridColumn186.FieldName = "Strroad";
            this.bandedGridColumn186.Name = "bandedGridColumn186";
            this.bandedGridColumn186.Visible = true;
            // 
            // bandedGridColumn187
            // 
            this.bandedGridColumn187.Caption = "经度";
            this.bandedGridColumn187.CustomizationCaption = "经度";
            this.bandedGridColumn187.FieldName = "Imlongitude";
            this.bandedGridColumn187.Name = "bandedGridColumn187";
            this.bandedGridColumn187.Visible = true;
            // 
            // bandedGridColumn188
            // 
            this.bandedGridColumn188.Caption = "纬度";
            this.bandedGridColumn188.CustomizationCaption = "纬度";
            this.bandedGridColumn188.FieldName = "Imlatitude";
            this.bandedGridColumn188.Name = "bandedGridColumn188";
            this.bandedGridColumn188.Visible = true;
            // 
            // bandedGridColumn189
            // 
            this.bandedGridColumn189.Caption = "ifileid";
            this.bandedGridColumn189.CustomizationCaption = "ifileid";
            this.bandedGridColumn189.FieldName = "Ifileid";
            this.bandedGridColumn189.Name = "bandedGridColumn189";
            // 
            // bandedGridColumn190
            // 
            this.bandedGridColumn190.Caption = "istime";
            this.bandedGridColumn190.CustomizationCaption = "istime";
            this.bandedGridColumn190.FieldName = "Istime";
            this.bandedGridColumn190.Name = "bandedGridColumn190";
            // 
            // bandedGridColumn191
            // 
            this.bandedGridColumn191.Caption = "ietime";
            this.bandedGridColumn191.CustomizationCaption = "ietime";
            this.bandedGridColumn191.FieldName = "Ietime";
            this.bandedGridColumn191.Name = "bandedGridColumn191";
            // 
            // bandedGridColumn192
            // 
            this.bandedGridColumn192.Caption = "gridColumn324";
            this.bandedGridColumn192.CustomizationCaption = "gridColumn324";
            this.bandedGridColumn192.FieldName = "Iid";
            this.bandedGridColumn192.Name = "bandedGridColumn192";
            // 
            // gridBand3
            // 
            this.gridBand3.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand3.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand3.Caption = "基础信息";
            this.gridBand3.Columns.Add(this.bandedGridColumn185);
            this.gridBand3.Columns.Add(this.bandedGridColumn186);
            this.gridBand3.Columns.Add(this.bandedGridColumn182);
            this.gridBand3.Columns.Add(this.bandedGridColumn183);
            this.gridBand3.Columns.Add(this.bandedGridColumn184);
            this.gridBand3.Columns.Add(this.bandedGridColumn187);
            this.gridBand3.Columns.Add(this.bandedGridColumn188);
            this.gridBand3.MinWidth = 20;
            this.gridBand3.Name = "gridBand3";
            this.gridBand3.Width = 555;
            // 
            // gridBand30
            // 
            this.gridBand30.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand30.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand30.Caption = "测试信息";
            this.gridBand30.Columns.Add(this.bandedGridColumn129);
            this.gridBand30.Columns.Add(this.bandedGridColumn130);
            this.gridBand30.Columns.Add(this.bandedGridColumn189);
            this.gridBand30.Columns.Add(this.bandedGridColumn190);
            this.gridBand30.Columns.Add(this.bandedGridColumn191);
            this.gridBand30.Columns.Add(this.bandedGridColumn192);
            this.gridBand30.MinWidth = 20;
            this.gridBand30.Name = "gridBand30";
            this.gridBand30.Width = 150;
            // 
            // gridBand31
            // 
            this.gridBand31.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand31.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand31.Caption = "下载速率";
            this.gridBand31.Columns.Add(this.bandedGridColumn131);
            this.gridBand31.Columns.Add(this.bandedGridColumn132);
            this.gridBand31.Columns.Add(this.bandedGridColumn133);
            this.gridBand31.Columns.Add(this.bandedGridColumn134);
            this.gridBand31.Columns.Add(this.bandedGridColumn135);
            this.gridBand31.Columns.Add(this.bandedGridColumn136);
            this.gridBand31.MinWidth = 20;
            this.gridBand31.Name = "gridBand31";
            this.gridBand31.Width = 450;
            // 
            // gridBand32
            // 
            this.gridBand32.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand32.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand32.Caption = "TD电平";
            this.gridBand32.Columns.Add(this.bandedGridColumn137);
            this.gridBand32.Columns.Add(this.bandedGridColumn138);
            this.gridBand32.Columns.Add(this.bandedGridColumn139);
            this.gridBand32.Columns.Add(this.bandedGridColumn140);
            this.gridBand32.Columns.Add(this.bandedGridColumn141);
            this.gridBand32.Columns.Add(this.bandedGridColumn142);
            this.gridBand32.Columns.Add(this.bandedGridColumn143);
            this.gridBand32.MinWidth = 20;
            this.gridBand32.Name = "gridBand32";
            this.gridBand32.Width = 525;
            // 
            // gridBand33
            // 
            this.gridBand33.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand33.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand33.Caption = "GSM电平";
            this.gridBand33.Columns.Add(this.bandedGridColumn144);
            this.gridBand33.Columns.Add(this.bandedGridColumn145);
            this.gridBand33.Columns.Add(this.bandedGridColumn146);
            this.gridBand33.Columns.Add(this.bandedGridColumn147);
            this.gridBand33.Columns.Add(this.bandedGridColumn148);
            this.gridBand33.Columns.Add(this.bandedGridColumn149);
            this.gridBand33.Columns.Add(this.bandedGridColumn150);
            this.gridBand33.MinWidth = 20;
            this.gridBand33.Name = "gridBand33";
            this.gridBand33.Width = 525;
            // 
            // gridBand34
            // 
            this.gridBand34.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand34.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand34.Caption = "DPCH C/I";
            this.gridBand34.Columns.Add(this.bandedGridColumn151);
            this.gridBand34.Columns.Add(this.bandedGridColumn152);
            this.gridBand34.Columns.Add(this.bandedGridColumn153);
            this.gridBand34.Columns.Add(this.bandedGridColumn154);
            this.gridBand34.Columns.Add(this.bandedGridColumn155);
            this.gridBand34.MinWidth = 20;
            this.gridBand34.Name = "gridBand34";
            this.gridBand34.Width = 375;
            // 
            // gridBand35
            // 
            this.gridBand35.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand35.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand35.Caption = "TxPower";
            this.gridBand35.Columns.Add(this.bandedGridColumn156);
            this.gridBand35.Columns.Add(this.bandedGridColumn157);
            this.gridBand35.Columns.Add(this.bandedGridColumn158);
            this.gridBand35.Columns.Add(this.bandedGridColumn159);
            this.gridBand35.Columns.Add(this.bandedGridColumn160);
            this.gridBand35.MinWidth = 20;
            this.gridBand35.Name = "gridBand35";
            this.gridBand35.Width = 375;
            // 
            // gridBand36
            // 
            this.gridBand36.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand36.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand36.Caption = "T至G切换";
            this.gridBand36.Columns.Add(this.bandedGridColumn161);
            this.gridBand36.Columns.Add(this.bandedGridColumn162);
            this.gridBand36.Columns.Add(this.bandedGridColumn163);
            this.gridBand36.MinWidth = 20;
            this.gridBand36.Name = "gridBand36";
            this.gridBand36.Width = 225;
            // 
            // gridBand37
            // 
            this.gridBand37.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand37.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand37.Caption = "T网硬切换";
            this.gridBand37.Columns.Add(this.bandedGridColumn164);
            this.gridBand37.Columns.Add(this.bandedGridColumn165);
            this.gridBand37.Columns.Add(this.bandedGridColumn166);
            this.gridBand37.MinWidth = 20;
            this.gridBand37.Name = "gridBand37";
            this.gridBand37.Width = 225;
            // 
            // gridBand38
            // 
            this.gridBand38.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand38.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand38.Caption = "T网接力切换";
            this.gridBand38.Columns.Add(this.bandedGridColumn167);
            this.gridBand38.Columns.Add(this.bandedGridColumn168);
            this.gridBand38.Columns.Add(this.bandedGridColumn169);
            this.gridBand38.MinWidth = 20;
            this.gridBand38.Name = "gridBand38";
            this.gridBand38.Width = 225;
            // 
            // gridBand39
            // 
            this.gridBand39.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand39.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand39.Caption = "T网重选";
            this.gridBand39.Columns.Add(this.bandedGridColumn170);
            this.gridBand39.Columns.Add(this.bandedGridColumn171);
            this.gridBand39.MinWidth = 20;
            this.gridBand39.Name = "gridBand39";
            this.gridBand39.Width = 150;
            // 
            // gridBand40
            // 
            this.gridBand40.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand40.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand40.Caption = "T至G重选";
            this.gridBand40.Columns.Add(this.bandedGridColumn172);
            this.gridBand40.Columns.Add(this.bandedGridColumn173);
            this.gridBand40.Columns.Add(this.bandedGridColumn174);
            this.gridBand40.Columns.Add(this.bandedGridColumn175);
            this.gridBand40.MinWidth = 20;
            this.gridBand40.Name = "gridBand40";
            this.gridBand40.Width = 300;
            // 
            // gridBand41
            // 
            this.gridBand41.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand41.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand41.Caption = "其它";
            this.gridBand41.Columns.Add(this.bandedGridColumn176);
            this.gridBand41.Columns.Add(this.bandedGridColumn177);
            this.gridBand41.Columns.Add(this.bandedGridColumn178);
            this.gridBand41.Columns.Add(this.bandedGridColumn179);
            this.gridBand41.Columns.Add(this.bandedGridColumn180);
            this.gridBand41.Columns.Add(this.bandedGridColumn181);
            this.gridBand41.MinWidth = 20;
            this.gridBand41.Name = "gridBand41";
            this.gridBand41.Width = 450;
            // 
            // bandedGridColumn193
            // 
            this.bandedGridColumn193.Caption = "距离(米)";
            this.bandedGridColumn193.CustomizationCaption = "距离(米)";
            this.bandedGridColumn193.FieldName = "Idistance";
            this.bandedGridColumn193.Name = "bandedGridColumn193";
            this.bandedGridColumn193.Visible = true;
            // 
            // bandedGridColumn194
            // 
            this.bandedGridColumn194.Caption = "时长(秒)";
            this.bandedGridColumn194.CustomizationCaption = "时长(秒)";
            this.bandedGridColumn194.FieldName = "Iduration";
            this.bandedGridColumn194.Name = "bandedGridColumn194";
            this.bandedGridColumn194.Visible = true;
            // 
            // bandedGridColumn195
            // 
            this.bandedGridColumn195.Caption = "0";
            this.bandedGridColumn195.CustomizationCaption = "下载速率0";
            this.bandedGridColumn195.FieldName = "APP_Speed0";
            this.bandedGridColumn195.Name = "bandedGridColumn195";
            this.bandedGridColumn195.Visible = true;
            // 
            // bandedGridColumn196
            // 
            this.bandedGridColumn196.Caption = "[0,500]";
            this.bandedGridColumn196.CustomizationCaption = "下载速率[0,500]";
            this.bandedGridColumn196.FieldName = "APP_Speed0_500";
            this.bandedGridColumn196.Name = "bandedGridColumn196";
            this.bandedGridColumn196.Visible = true;
            // 
            // bandedGridColumn197
            // 
            this.bandedGridColumn197.Caption = "[500,800]";
            this.bandedGridColumn197.CustomizationCaption = "下载速率[500,800]";
            this.bandedGridColumn197.FieldName = "APP_Speed500_800";
            this.bandedGridColumn197.Name = "bandedGridColumn197";
            this.bandedGridColumn197.Visible = true;
            // 
            // bandedGridColumn198
            // 
            this.bandedGridColumn198.Caption = "[800,900]";
            this.bandedGridColumn198.CustomizationCaption = "下载速率[800,900]";
            this.bandedGridColumn198.FieldName = "APP_Speed800_900";
            this.bandedGridColumn198.Name = "bandedGridColumn198";
            this.bandedGridColumn198.Visible = true;
            // 
            // bandedGridColumn199
            // 
            this.bandedGridColumn199.Caption = "[900,1100]";
            this.bandedGridColumn199.CustomizationCaption = "下载速率[900,1100]";
            this.bandedGridColumn199.FieldName = "APP_Speed900_1100";
            this.bandedGridColumn199.Name = "bandedGridColumn199";
            this.bandedGridColumn199.Visible = true;
            // 
            // bandedGridColumn200
            // 
            this.bandedGridColumn200.Caption = "1100";
            this.bandedGridColumn200.CustomizationCaption = "下载速率1100";
            this.bandedGridColumn200.FieldName = "APP_Speed1100";
            this.bandedGridColumn200.Name = "bandedGridColumn200";
            this.bandedGridColumn200.Visible = true;
            // 
            // bandedGridColumn201
            // 
            this.bandedGridColumn201.Caption = "均值";
            this.bandedGridColumn201.CustomizationCaption = "TD电平均值";
            this.bandedGridColumn201.FieldName = "Rscpmean";
            this.bandedGridColumn201.Name = "bandedGridColumn201";
            this.bandedGridColumn201.Visible = true;
            // 
            // bandedGridColumn202
            // 
            this.bandedGridColumn202.Caption = "小于75";
            this.bandedGridColumn202.CustomizationCaption = "TD电平小于75";
            this.bandedGridColumn202.FieldName = "Rscp75";
            this.bandedGridColumn202.Name = "bandedGridColumn202";
            this.bandedGridColumn202.Visible = true;
            // 
            // bandedGridColumn203
            // 
            this.bandedGridColumn203.Caption = "[76,80]";
            this.bandedGridColumn203.CustomizationCaption = "TD电平[76,80]";
            this.bandedGridColumn203.FieldName = "Rscp76_80";
            this.bandedGridColumn203.Name = "bandedGridColumn203";
            this.bandedGridColumn203.Visible = true;
            // 
            // bandedGridColumn204
            // 
            this.bandedGridColumn204.Caption = "[81,85]";
            this.bandedGridColumn204.CustomizationCaption = "TD电平[81,85]";
            this.bandedGridColumn204.FieldName = "Rscp81_85";
            this.bandedGridColumn204.Name = "bandedGridColumn204";
            this.bandedGridColumn204.Visible = true;
            // 
            // bandedGridColumn205
            // 
            this.bandedGridColumn205.Caption = "[86,90]";
            this.bandedGridColumn205.CustomizationCaption = "TD电平[86,90]";
            this.bandedGridColumn205.FieldName = "Rscp86_90";
            this.bandedGridColumn205.Name = "bandedGridColumn205";
            this.bandedGridColumn205.Visible = true;
            // 
            // bandedGridColumn206
            // 
            this.bandedGridColumn206.Caption = "[91,94]";
            this.bandedGridColumn206.CustomizationCaption = "TD电平[91,94]";
            this.bandedGridColumn206.FieldName = "Rscp91_94";
            this.bandedGridColumn206.Name = "bandedGridColumn206";
            this.bandedGridColumn206.Visible = true;
            // 
            // bandedGridColumn207
            // 
            this.bandedGridColumn207.Caption = "大于94";
            this.bandedGridColumn207.CustomizationCaption = "TD电平大于94";
            this.bandedGridColumn207.FieldName = "Rscp94";
            this.bandedGridColumn207.Name = "bandedGridColumn207";
            this.bandedGridColumn207.Visible = true;
            // 
            // bandedGridColumn208
            // 
            this.bandedGridColumn208.Caption = "均值";
            this.bandedGridColumn208.CustomizationCaption = "GSM电平均值";
            this.bandedGridColumn208.FieldName = "Rxlmean";
            this.bandedGridColumn208.Name = "bandedGridColumn208";
            this.bandedGridColumn208.Visible = true;
            // 
            // bandedGridColumn209
            // 
            this.bandedGridColumn209.Caption = "小于75";
            this.bandedGridColumn209.CustomizationCaption = "GSM电平小于75";
            this.bandedGridColumn209.FieldName = "Rxl75";
            this.bandedGridColumn209.Name = "bandedGridColumn209";
            this.bandedGridColumn209.Visible = true;
            // 
            // bandedGridColumn210
            // 
            this.bandedGridColumn210.Caption = "[76,80]";
            this.bandedGridColumn210.CustomizationCaption = "GSM电平[76,80]";
            this.bandedGridColumn210.FieldName = "Rxl76_80";
            this.bandedGridColumn210.Name = "bandedGridColumn210";
            this.bandedGridColumn210.Visible = true;
            // 
            // bandedGridColumn211
            // 
            this.bandedGridColumn211.Caption = "[81,85]";
            this.bandedGridColumn211.CustomizationCaption = "GSM电平[81,85]";
            this.bandedGridColumn211.FieldName = "Rxl81_85";
            this.bandedGridColumn211.Name = "bandedGridColumn211";
            this.bandedGridColumn211.Visible = true;
            // 
            // bandedGridColumn212
            // 
            this.bandedGridColumn212.Caption = "[86,90]";
            this.bandedGridColumn212.CustomizationCaption = "GSM电平[86,90]";
            this.bandedGridColumn212.FieldName = "Rxl86_90";
            this.bandedGridColumn212.Name = "bandedGridColumn212";
            this.bandedGridColumn212.Visible = true;
            // 
            // bandedGridColumn213
            // 
            this.bandedGridColumn213.Caption = "[91,94]";
            this.bandedGridColumn213.CustomizationCaption = "GSM电平[91,94]";
            this.bandedGridColumn213.FieldName = "Rxl91_94";
            this.bandedGridColumn213.Name = "bandedGridColumn213";
            this.bandedGridColumn213.Visible = true;
            // 
            // bandedGridColumn214
            // 
            this.bandedGridColumn214.Caption = "大于94";
            this.bandedGridColumn214.CustomizationCaption = "GSM电平大于94";
            this.bandedGridColumn214.FieldName = "Rxl94";
            this.bandedGridColumn214.Name = "bandedGridColumn214";
            this.bandedGridColumn214.Visible = true;
            // 
            // bandedGridColumn215
            // 
            this.bandedGridColumn215.Caption = "均值";
            this.bandedGridColumn215.CustomizationCaption = "DPCH C/I均值";
            this.bandedGridColumn215.FieldName = "Dc2imean";
            this.bandedGridColumn215.Name = "bandedGridColumn215";
            this.bandedGridColumn215.Visible = true;
            // 
            // bandedGridColumn216
            // 
            this.bandedGridColumn216.Caption = "小于-10";
            this.bandedGridColumn216.CustomizationCaption = "DPCH C/I小于-10";
            this.bandedGridColumn216.FieldName = "Dc2iF10";
            this.bandedGridColumn216.Name = "bandedGridColumn216";
            this.bandedGridColumn216.Visible = true;
            // 
            // bandedGridColumn217
            // 
            this.bandedGridColumn217.Caption = "[-10,-3]";
            this.bandedGridColumn217.CustomizationCaption = "DPCH C/I[-10,-3]";
            this.bandedGridColumn217.FieldName = "Dc2iF10TF3";
            this.bandedGridColumn217.Name = "bandedGridColumn217";
            this.bandedGridColumn217.Visible = true;
            // 
            // bandedGridColumn218
            // 
            this.bandedGridColumn218.Caption = "[-3,15]";
            this.bandedGridColumn218.CustomizationCaption = "DPCH C/I[-3,15]";
            this.bandedGridColumn218.FieldName = "Dc2iF3T15";
            this.bandedGridColumn218.Name = "bandedGridColumn218";
            this.bandedGridColumn218.Visible = true;
            // 
            // bandedGridColumn219
            // 
            this.bandedGridColumn219.Caption = "大于15";
            this.bandedGridColumn219.CustomizationCaption = "DPCH C/I大于15";
            this.bandedGridColumn219.FieldName = "Dc2i15";
            this.bandedGridColumn219.Name = "bandedGridColumn219";
            this.bandedGridColumn219.Visible = true;
            // 
            // bandedGridColumn220
            // 
            this.bandedGridColumn220.Caption = "均值";
            this.bandedGridColumn220.CustomizationCaption = "TxPower均值";
            this.bandedGridColumn220.FieldName = "Txpowermean";
            this.bandedGridColumn220.Name = "bandedGridColumn220";
            this.bandedGridColumn220.Visible = true;
            // 
            // bandedGridColumn221
            // 
            this.bandedGridColumn221.Caption = "小于-20";
            this.bandedGridColumn221.CustomizationCaption = "TxPower小于-20";
            this.bandedGridColumn221.FieldName = "TxpowerF20";
            this.bandedGridColumn221.Name = "bandedGridColumn221";
            this.bandedGridColumn221.Visible = true;
            // 
            // bandedGridColumn222
            // 
            this.bandedGridColumn222.Caption = "[-20,0]";
            this.bandedGridColumn222.CustomizationCaption = "TxPower[-20,0]";
            this.bandedGridColumn222.FieldName = "TxpowerF20T0";
            this.bandedGridColumn222.Name = "bandedGridColumn222";
            this.bandedGridColumn222.Visible = true;
            // 
            // bandedGridColumn223
            // 
            this.bandedGridColumn223.Caption = "[0,15]";
            this.bandedGridColumn223.CustomizationCaption = "TxPower[0,15]";
            this.bandedGridColumn223.FieldName = "Txpower0T15";
            this.bandedGridColumn223.Name = "bandedGridColumn223";
            this.bandedGridColumn223.Visible = true;
            // 
            // bandedGridColumn224
            // 
            this.bandedGridColumn224.Caption = "大于15";
            this.bandedGridColumn224.CustomizationCaption = "TxPower大于15";
            this.bandedGridColumn224.FieldName = "Txpower15";
            this.bandedGridColumn224.Name = "bandedGridColumn224";
            this.bandedGridColumn224.Visible = true;
            // 
            // bandedGridColumn225
            // 
            this.bandedGridColumn225.Caption = "成功次数";
            this.bandedGridColumn225.CustomizationCaption = "T至G切换成功次数";
            this.bandedGridColumn225.FieldName = "HO_SUCC_T2G";
            this.bandedGridColumn225.Name = "bandedGridColumn225";
            this.bandedGridColumn225.Visible = true;
            // 
            // bandedGridColumn226
            // 
            this.bandedGridColumn226.Caption = "失败次数";
            this.bandedGridColumn226.CustomizationCaption = "T至G切换失败次数";
            this.bandedGridColumn226.FieldName = "HO_FAIL_T2G";
            this.bandedGridColumn226.Name = "bandedGridColumn226";
            this.bandedGridColumn226.Visible = true;
            // 
            // bandedGridColumn227
            // 
            this.bandedGridColumn227.Caption = "时延";
            this.bandedGridColumn227.CustomizationCaption = "T至G切换时延";
            this.bandedGridColumn227.FieldName = "HO_Time_T2G";
            this.bandedGridColumn227.Name = "bandedGridColumn227";
            this.bandedGridColumn227.Visible = true;
            // 
            // bandedGridColumn228
            // 
            this.bandedGridColumn228.Caption = "成功次数";
            this.bandedGridColumn228.CustomizationCaption = "T网硬切换成功次数";
            this.bandedGridColumn228.FieldName = "HO_SUCC_T2T";
            this.bandedGridColumn228.Name = "bandedGridColumn228";
            this.bandedGridColumn228.Visible = true;
            // 
            // bandedGridColumn229
            // 
            this.bandedGridColumn229.Caption = "失败次数";
            this.bandedGridColumn229.CustomizationCaption = "T网硬切换失败次数";
            this.bandedGridColumn229.FieldName = "HO_FAIL_T2T";
            this.bandedGridColumn229.Name = "bandedGridColumn229";
            this.bandedGridColumn229.Visible = true;
            // 
            // bandedGridColumn230
            // 
            this.bandedGridColumn230.Caption = "时延";
            this.bandedGridColumn230.CustomizationCaption = "T网硬切换时延";
            this.bandedGridColumn230.FieldName = "HO_Time_T2T";
            this.bandedGridColumn230.Name = "bandedGridColumn230";
            this.bandedGridColumn230.Visible = true;
            // 
            // bandedGridColumn231
            // 
            this.bandedGridColumn231.Caption = "成功次数";
            this.bandedGridColumn231.CustomizationCaption = "T网接力切换成功次数";
            this.bandedGridColumn231.FieldName = "HO_SUCC_Baton";
            this.bandedGridColumn231.Name = "bandedGridColumn231";
            this.bandedGridColumn231.Visible = true;
            // 
            // bandedGridColumn232
            // 
            this.bandedGridColumn232.Caption = "失败次数";
            this.bandedGridColumn232.CustomizationCaption = "T网接力切换失败次数";
            this.bandedGridColumn232.FieldName = "HO_FAIL_Baton";
            this.bandedGridColumn232.Name = "bandedGridColumn232";
            this.bandedGridColumn232.Visible = true;
            // 
            // bandedGridColumn233
            // 
            this.bandedGridColumn233.Caption = "时延";
            this.bandedGridColumn233.CustomizationCaption = "T网接力切换时延";
            this.bandedGridColumn233.FieldName = "HO_Time_Baton";
            this.bandedGridColumn233.Name = "bandedGridColumn233";
            this.bandedGridColumn233.Visible = true;
            // 
            // bandedGridColumn234
            // 
            this.bandedGridColumn234.Caption = "成功次数";
            this.bandedGridColumn234.CustomizationCaption = "T网重选成功次数";
            this.bandedGridColumn234.FieldName = "CR_SUCC_T2T";
            this.bandedGridColumn234.Name = "bandedGridColumn234";
            this.bandedGridColumn234.Visible = true;
            // 
            // bandedGridColumn235
            // 
            this.bandedGridColumn235.Caption = "失败次数";
            this.bandedGridColumn235.CustomizationCaption = "T网重选失败次数";
            this.bandedGridColumn235.FieldName = "CR_FAIL_T2T";
            this.bandedGridColumn235.Name = "bandedGridColumn235";
            this.bandedGridColumn235.Visible = true;
            // 
            // bandedGridColumn236
            // 
            this.bandedGridColumn236.Caption = "成功次数";
            this.bandedGridColumn236.CustomizationCaption = "T至G重选成功次数";
            this.bandedGridColumn236.FieldName = "CR_SUCC_T2G";
            this.bandedGridColumn236.Name = "bandedGridColumn236";
            this.bandedGridColumn236.Visible = true;
            // 
            // bandedGridColumn237
            // 
            this.bandedGridColumn237.Caption = "失败次数";
            this.bandedGridColumn237.CustomizationCaption = "T至G重选失败次数";
            this.bandedGridColumn237.FieldName = "CR_FAIL_T2G";
            this.bandedGridColumn237.Name = "bandedGridColumn237";
            this.bandedGridColumn237.Visible = true;
            // 
            // bandedGridColumn238
            // 
            this.bandedGridColumn238.Caption = "成功次数";
            this.bandedGridColumn238.CustomizationCaption = "G至T重选成功次数";
            this.bandedGridColumn238.FieldName = "CR_SUCC_G2T";
            this.bandedGridColumn238.Name = "bandedGridColumn238";
            this.bandedGridColumn238.Visible = true;
            // 
            // bandedGridColumn239
            // 
            this.bandedGridColumn239.Caption = "失败次数";
            this.bandedGridColumn239.CustomizationCaption = "G至T重选失败次数";
            this.bandedGridColumn239.FieldName = "CR_FAIL_G2T";
            this.bandedGridColumn239.Name = "bandedGridColumn239";
            this.bandedGridColumn239.Visible = true;
            // 
            // bandedGridColumn240
            // 
            this.bandedGridColumn240.Caption = "Hsdpa下载量占比";
            this.bandedGridColumn240.CustomizationCaption = "Hsdpa下载量占比";
            this.bandedGridColumn240.FieldName = "HSDPA_Size";
            this.bandedGridColumn240.Name = "bandedGridColumn240";
            this.bandedGridColumn240.Visible = true;
            // 
            // bandedGridColumn241
            // 
            this.bandedGridColumn241.Caption = "Hsdpa时长占比";
            this.bandedGridColumn241.CustomizationCaption = "Hsdpa时长占比";
            this.bandedGridColumn241.FieldName = "HSDPA_Time";
            this.bandedGridColumn241.Name = "bandedGridColumn241";
            this.bandedGridColumn241.Visible = true;
            // 
            // bandedGridColumn242
            // 
            this.bandedGridColumn242.Caption = "R4下载量占比";
            this.bandedGridColumn242.CustomizationCaption = "R4下载量占比";
            this.bandedGridColumn242.FieldName = "R4_Size";
            this.bandedGridColumn242.Name = "bandedGridColumn242";
            this.bandedGridColumn242.Visible = true;
            // 
            // bandedGridColumn243
            // 
            this.bandedGridColumn243.Caption = "R4时长占比";
            this.bandedGridColumn243.CustomizationCaption = "R4时长占比";
            this.bandedGridColumn243.FieldName = "R4_Time";
            this.bandedGridColumn243.Name = "bandedGridColumn243";
            this.bandedGridColumn243.Visible = true;
            // 
            // bandedGridColumn244
            // 
            this.bandedGridColumn244.Caption = "Gsm下载量占比";
            this.bandedGridColumn244.CustomizationCaption = "Gsm下载量占比";
            this.bandedGridColumn244.FieldName = "EDGE_Size";
            this.bandedGridColumn244.Name = "bandedGridColumn244";
            this.bandedGridColumn244.Visible = true;
            // 
            // bandedGridColumn245
            // 
            this.bandedGridColumn245.Caption = "Gsm时长占比";
            this.bandedGridColumn245.CustomizationCaption = "Gsm时长占比";
            this.bandedGridColumn245.FieldName = "EDGE_Time";
            this.bandedGridColumn245.Name = "bandedGridColumn245";
            this.bandedGridColumn245.Visible = true;
            // 
            // bandedGridColumn246
            // 
            this.bandedGridColumn246.Caption = "TOP1小区";
            this.bandedGridColumn246.CustomizationCaption = "TOP1小区";
            this.bandedGridColumn246.FieldName = "Cell1";
            this.bandedGridColumn246.Name = "bandedGridColumn246";
            this.bandedGridColumn246.Visible = true;
            this.bandedGridColumn246.Width = 85;
            // 
            // bandedGridColumn247
            // 
            this.bandedGridColumn247.Caption = "TOP2小区";
            this.bandedGridColumn247.CustomizationCaption = "TOP2小区";
            this.bandedGridColumn247.FieldName = "Cell2";
            this.bandedGridColumn247.Name = "bandedGridColumn247";
            this.bandedGridColumn247.Visible = true;
            this.bandedGridColumn247.Width = 85;
            // 
            // bandedGridColumn248
            // 
            this.bandedGridColumn248.Caption = "TOP3小区";
            this.bandedGridColumn248.CustomizationCaption = "TOP3小区";
            this.bandedGridColumn248.FieldName = "Cell3";
            this.bandedGridColumn248.Name = "bandedGridColumn248";
            this.bandedGridColumn248.Visible = true;
            this.bandedGridColumn248.Width = 85;
            // 
            // bandedGridColumn249
            // 
            this.bandedGridColumn249.Caption = "所属网格";
            this.bandedGridColumn249.CustomizationCaption = "所属网格";
            this.bandedGridColumn249.FieldName = "Strgrid";
            this.bandedGridColumn249.Name = "bandedGridColumn249";
            this.bandedGridColumn249.Visible = true;
            // 
            // bandedGridColumn250
            // 
            this.bandedGridColumn250.Caption = "所属道路";
            this.bandedGridColumn250.CustomizationCaption = "所属道路";
            this.bandedGridColumn250.FieldName = "Strroad";
            this.bandedGridColumn250.Name = "bandedGridColumn250";
            this.bandedGridColumn250.Visible = true;
            // 
            // bandedGridColumn251
            // 
            this.bandedGridColumn251.Caption = "经度";
            this.bandedGridColumn251.CustomizationCaption = "经度";
            this.bandedGridColumn251.FieldName = "Imlongitude";
            this.bandedGridColumn251.Name = "bandedGridColumn251";
            this.bandedGridColumn251.Visible = true;
            // 
            // bandedGridColumn252
            // 
            this.bandedGridColumn252.Caption = "纬度";
            this.bandedGridColumn252.CustomizationCaption = "纬度";
            this.bandedGridColumn252.FieldName = "Imlatitude";
            this.bandedGridColumn252.Name = "bandedGridColumn252";
            this.bandedGridColumn252.Visible = true;
            // 
            // bandedGridColumn253
            // 
            this.bandedGridColumn253.Caption = "ifileid";
            this.bandedGridColumn253.CustomizationCaption = "ifileid";
            this.bandedGridColumn253.FieldName = "Ifileid";
            this.bandedGridColumn253.Name = "bandedGridColumn253";
            // 
            // bandedGridColumn254
            // 
            this.bandedGridColumn254.Caption = "istime";
            this.bandedGridColumn254.CustomizationCaption = "istime";
            this.bandedGridColumn254.FieldName = "Istime";
            this.bandedGridColumn254.Name = "bandedGridColumn254";
            // 
            // bandedGridColumn255
            // 
            this.bandedGridColumn255.Caption = "ietime";
            this.bandedGridColumn255.CustomizationCaption = "ietime";
            this.bandedGridColumn255.FieldName = "Ietime";
            this.bandedGridColumn255.Name = "bandedGridColumn255";
            // 
            // bandedGridColumn256
            // 
            this.bandedGridColumn256.Caption = "gridColumn324";
            this.bandedGridColumn256.CustomizationCaption = "gridColumn324";
            this.bandedGridColumn256.FieldName = "Iid";
            this.bandedGridColumn256.Name = "bandedGridColumn256";
            // 
            // gridBand4
            // 
            this.gridBand4.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand4.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand4.Caption = "基础信息";
            this.gridBand4.Columns.Add(this.bandedGridColumn249);
            this.gridBand4.Columns.Add(this.bandedGridColumn250);
            this.gridBand4.Columns.Add(this.bandedGridColumn246);
            this.gridBand4.Columns.Add(this.bandedGridColumn247);
            this.gridBand4.Columns.Add(this.bandedGridColumn248);
            this.gridBand4.Columns.Add(this.bandedGridColumn251);
            this.gridBand4.Columns.Add(this.bandedGridColumn252);
            this.gridBand4.MinWidth = 20;
            this.gridBand4.Name = "gridBand4";
            this.gridBand4.Width = 555;
            // 
            // gridBand42
            // 
            this.gridBand42.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand42.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand42.Caption = "测试信息";
            this.gridBand42.Columns.Add(this.bandedGridColumn193);
            this.gridBand42.Columns.Add(this.bandedGridColumn194);
            this.gridBand42.Columns.Add(this.bandedGridColumn253);
            this.gridBand42.Columns.Add(this.bandedGridColumn254);
            this.gridBand42.Columns.Add(this.bandedGridColumn255);
            this.gridBand42.Columns.Add(this.bandedGridColumn256);
            this.gridBand42.MinWidth = 20;
            this.gridBand42.Name = "gridBand42";
            this.gridBand42.Width = 150;
            // 
            // gridBand43
            // 
            this.gridBand43.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand43.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand43.Caption = "下载速率";
            this.gridBand43.Columns.Add(this.bandedGridColumn195);
            this.gridBand43.Columns.Add(this.bandedGridColumn196);
            this.gridBand43.Columns.Add(this.bandedGridColumn197);
            this.gridBand43.Columns.Add(this.bandedGridColumn198);
            this.gridBand43.Columns.Add(this.bandedGridColumn199);
            this.gridBand43.Columns.Add(this.bandedGridColumn200);
            this.gridBand43.MinWidth = 20;
            this.gridBand43.Name = "gridBand43";
            this.gridBand43.Width = 450;
            // 
            // gridBand44
            // 
            this.gridBand44.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand44.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand44.Caption = "TD电平";
            this.gridBand44.Columns.Add(this.bandedGridColumn201);
            this.gridBand44.Columns.Add(this.bandedGridColumn202);
            this.gridBand44.Columns.Add(this.bandedGridColumn203);
            this.gridBand44.Columns.Add(this.bandedGridColumn204);
            this.gridBand44.Columns.Add(this.bandedGridColumn205);
            this.gridBand44.Columns.Add(this.bandedGridColumn206);
            this.gridBand44.Columns.Add(this.bandedGridColumn207);
            this.gridBand44.MinWidth = 20;
            this.gridBand44.Name = "gridBand44";
            this.gridBand44.Width = 525;
            // 
            // gridBand45
            // 
            this.gridBand45.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand45.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand45.Caption = "GSM电平";
            this.gridBand45.Columns.Add(this.bandedGridColumn208);
            this.gridBand45.Columns.Add(this.bandedGridColumn209);
            this.gridBand45.Columns.Add(this.bandedGridColumn210);
            this.gridBand45.Columns.Add(this.bandedGridColumn211);
            this.gridBand45.Columns.Add(this.bandedGridColumn212);
            this.gridBand45.Columns.Add(this.bandedGridColumn213);
            this.gridBand45.Columns.Add(this.bandedGridColumn214);
            this.gridBand45.MinWidth = 20;
            this.gridBand45.Name = "gridBand45";
            this.gridBand45.Width = 525;
            // 
            // gridBand46
            // 
            this.gridBand46.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand46.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand46.Caption = "DPCH C/I";
            this.gridBand46.Columns.Add(this.bandedGridColumn215);
            this.gridBand46.Columns.Add(this.bandedGridColumn216);
            this.gridBand46.Columns.Add(this.bandedGridColumn217);
            this.gridBand46.Columns.Add(this.bandedGridColumn218);
            this.gridBand46.Columns.Add(this.bandedGridColumn219);
            this.gridBand46.MinWidth = 20;
            this.gridBand46.Name = "gridBand46";
            this.gridBand46.Width = 375;
            // 
            // gridBand47
            // 
            this.gridBand47.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand47.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand47.Caption = "TxPower";
            this.gridBand47.Columns.Add(this.bandedGridColumn220);
            this.gridBand47.Columns.Add(this.bandedGridColumn221);
            this.gridBand47.Columns.Add(this.bandedGridColumn222);
            this.gridBand47.Columns.Add(this.bandedGridColumn223);
            this.gridBand47.Columns.Add(this.bandedGridColumn224);
            this.gridBand47.MinWidth = 20;
            this.gridBand47.Name = "gridBand47";
            this.gridBand47.Width = 375;
            // 
            // gridBand48
            // 
            this.gridBand48.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand48.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand48.Caption = "T至G切换";
            this.gridBand48.Columns.Add(this.bandedGridColumn225);
            this.gridBand48.Columns.Add(this.bandedGridColumn226);
            this.gridBand48.Columns.Add(this.bandedGridColumn227);
            this.gridBand48.MinWidth = 20;
            this.gridBand48.Name = "gridBand48";
            this.gridBand48.Width = 225;
            // 
            // gridBand49
            // 
            this.gridBand49.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand49.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand49.Caption = "T网硬切换";
            this.gridBand49.Columns.Add(this.bandedGridColumn228);
            this.gridBand49.Columns.Add(this.bandedGridColumn229);
            this.gridBand49.Columns.Add(this.bandedGridColumn230);
            this.gridBand49.MinWidth = 20;
            this.gridBand49.Name = "gridBand49";
            this.gridBand49.Width = 225;
            // 
            // gridBand50
            // 
            this.gridBand50.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand50.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand50.Caption = "T网接力切换";
            this.gridBand50.Columns.Add(this.bandedGridColumn231);
            this.gridBand50.Columns.Add(this.bandedGridColumn232);
            this.gridBand50.Columns.Add(this.bandedGridColumn233);
            this.gridBand50.MinWidth = 20;
            this.gridBand50.Name = "gridBand50";
            this.gridBand50.Width = 225;
            // 
            // gridBand51
            // 
            this.gridBand51.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand51.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand51.Caption = "T网重选";
            this.gridBand51.Columns.Add(this.bandedGridColumn234);
            this.gridBand51.Columns.Add(this.bandedGridColumn235);
            this.gridBand51.MinWidth = 20;
            this.gridBand51.Name = "gridBand51";
            this.gridBand51.Width = 150;
            // 
            // gridBand52
            // 
            this.gridBand52.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand52.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand52.Caption = "T至G重选";
            this.gridBand52.Columns.Add(this.bandedGridColumn236);
            this.gridBand52.Columns.Add(this.bandedGridColumn237);
            this.gridBand52.Columns.Add(this.bandedGridColumn238);
            this.gridBand52.Columns.Add(this.bandedGridColumn239);
            this.gridBand52.MinWidth = 20;
            this.gridBand52.Name = "gridBand52";
            this.gridBand52.Width = 300;
            // 
            // gridBand53
            // 
            this.gridBand53.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand53.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand53.Caption = "其它";
            this.gridBand53.Columns.Add(this.bandedGridColumn240);
            this.gridBand53.Columns.Add(this.bandedGridColumn241);
            this.gridBand53.Columns.Add(this.bandedGridColumn242);
            this.gridBand53.Columns.Add(this.bandedGridColumn243);
            this.gridBand53.Columns.Add(this.bandedGridColumn244);
            this.gridBand53.Columns.Add(this.bandedGridColumn245);
            this.gridBand53.MinWidth = 20;
            this.gridBand53.Name = "gridBand53";
            this.gridBand53.Width = 450;
            // 
            // bandedGridColumn257
            // 
            this.bandedGridColumn257.Caption = "距离(米)";
            this.bandedGridColumn257.CustomizationCaption = "距离(米)";
            this.bandedGridColumn257.FieldName = "Idistance";
            this.bandedGridColumn257.Name = "bandedGridColumn257";
            this.bandedGridColumn257.Visible = true;
            // 
            // bandedGridColumn258
            // 
            this.bandedGridColumn258.Caption = "时长(秒)";
            this.bandedGridColumn258.CustomizationCaption = "时长(秒)";
            this.bandedGridColumn258.FieldName = "Iduration";
            this.bandedGridColumn258.Name = "bandedGridColumn258";
            this.bandedGridColumn258.Visible = true;
            // 
            // bandedGridColumn259
            // 
            this.bandedGridColumn259.Caption = "0";
            this.bandedGridColumn259.CustomizationCaption = "下载速率0";
            this.bandedGridColumn259.FieldName = "APP_Speed0";
            this.bandedGridColumn259.Name = "bandedGridColumn259";
            this.bandedGridColumn259.Visible = true;
            // 
            // bandedGridColumn260
            // 
            this.bandedGridColumn260.Caption = "[0,500]";
            this.bandedGridColumn260.CustomizationCaption = "下载速率[0,500]";
            this.bandedGridColumn260.FieldName = "APP_Speed0_500";
            this.bandedGridColumn260.Name = "bandedGridColumn260";
            this.bandedGridColumn260.Visible = true;
            // 
            // bandedGridColumn261
            // 
            this.bandedGridColumn261.Caption = "[500,800]";
            this.bandedGridColumn261.CustomizationCaption = "下载速率[500,800]";
            this.bandedGridColumn261.FieldName = "APP_Speed500_800";
            this.bandedGridColumn261.Name = "bandedGridColumn261";
            this.bandedGridColumn261.Visible = true;
            // 
            // bandedGridColumn262
            // 
            this.bandedGridColumn262.Caption = "[800,900]";
            this.bandedGridColumn262.CustomizationCaption = "下载速率[800,900]";
            this.bandedGridColumn262.FieldName = "APP_Speed800_900";
            this.bandedGridColumn262.Name = "bandedGridColumn262";
            this.bandedGridColumn262.Visible = true;
            // 
            // bandedGridColumn263
            // 
            this.bandedGridColumn263.Caption = "[900,1100]";
            this.bandedGridColumn263.CustomizationCaption = "下载速率[900,1100]";
            this.bandedGridColumn263.FieldName = "APP_Speed900_1100";
            this.bandedGridColumn263.Name = "bandedGridColumn263";
            this.bandedGridColumn263.Visible = true;
            // 
            // bandedGridColumn264
            // 
            this.bandedGridColumn264.Caption = "1100";
            this.bandedGridColumn264.CustomizationCaption = "下载速率1100";
            this.bandedGridColumn264.FieldName = "APP_Speed1100";
            this.bandedGridColumn264.Name = "bandedGridColumn264";
            this.bandedGridColumn264.Visible = true;
            // 
            // bandedGridColumn265
            // 
            this.bandedGridColumn265.Caption = "均值";
            this.bandedGridColumn265.CustomizationCaption = "TD电平均值";
            this.bandedGridColumn265.FieldName = "Rscpmean";
            this.bandedGridColumn265.Name = "bandedGridColumn265";
            this.bandedGridColumn265.Visible = true;
            // 
            // bandedGridColumn266
            // 
            this.bandedGridColumn266.Caption = "小于75";
            this.bandedGridColumn266.CustomizationCaption = "TD电平小于75";
            this.bandedGridColumn266.FieldName = "Rscp75";
            this.bandedGridColumn266.Name = "bandedGridColumn266";
            this.bandedGridColumn266.Visible = true;
            // 
            // bandedGridColumn267
            // 
            this.bandedGridColumn267.Caption = "[76,80]";
            this.bandedGridColumn267.CustomizationCaption = "TD电平[76,80]";
            this.bandedGridColumn267.FieldName = "Rscp76_80";
            this.bandedGridColumn267.Name = "bandedGridColumn267";
            this.bandedGridColumn267.Visible = true;
            // 
            // bandedGridColumn268
            // 
            this.bandedGridColumn268.Caption = "[81,85]";
            this.bandedGridColumn268.CustomizationCaption = "TD电平[81,85]";
            this.bandedGridColumn268.FieldName = "Rscp81_85";
            this.bandedGridColumn268.Name = "bandedGridColumn268";
            this.bandedGridColumn268.Visible = true;
            // 
            // bandedGridColumn269
            // 
            this.bandedGridColumn269.Caption = "[86,90]";
            this.bandedGridColumn269.CustomizationCaption = "TD电平[86,90]";
            this.bandedGridColumn269.FieldName = "Rscp86_90";
            this.bandedGridColumn269.Name = "bandedGridColumn269";
            this.bandedGridColumn269.Visible = true;
            // 
            // bandedGridColumn270
            // 
            this.bandedGridColumn270.Caption = "[91,94]";
            this.bandedGridColumn270.CustomizationCaption = "TD电平[91,94]";
            this.bandedGridColumn270.FieldName = "Rscp91_94";
            this.bandedGridColumn270.Name = "bandedGridColumn270";
            this.bandedGridColumn270.Visible = true;
            // 
            // bandedGridColumn271
            // 
            this.bandedGridColumn271.Caption = "大于94";
            this.bandedGridColumn271.CustomizationCaption = "TD电平大于94";
            this.bandedGridColumn271.FieldName = "Rscp94";
            this.bandedGridColumn271.Name = "bandedGridColumn271";
            this.bandedGridColumn271.Visible = true;
            // 
            // bandedGridColumn272
            // 
            this.bandedGridColumn272.Caption = "均值";
            this.bandedGridColumn272.CustomizationCaption = "GSM电平均值";
            this.bandedGridColumn272.FieldName = "Rxlmean";
            this.bandedGridColumn272.Name = "bandedGridColumn272";
            this.bandedGridColumn272.Visible = true;
            // 
            // bandedGridColumn273
            // 
            this.bandedGridColumn273.Caption = "小于75";
            this.bandedGridColumn273.CustomizationCaption = "GSM电平小于75";
            this.bandedGridColumn273.FieldName = "Rxl75";
            this.bandedGridColumn273.Name = "bandedGridColumn273";
            this.bandedGridColumn273.Visible = true;
            // 
            // bandedGridColumn274
            // 
            this.bandedGridColumn274.Caption = "[76,80]";
            this.bandedGridColumn274.CustomizationCaption = "GSM电平[76,80]";
            this.bandedGridColumn274.FieldName = "Rxl76_80";
            this.bandedGridColumn274.Name = "bandedGridColumn274";
            this.bandedGridColumn274.Visible = true;
            // 
            // bandedGridColumn275
            // 
            this.bandedGridColumn275.Caption = "[81,85]";
            this.bandedGridColumn275.CustomizationCaption = "GSM电平[81,85]";
            this.bandedGridColumn275.FieldName = "Rxl81_85";
            this.bandedGridColumn275.Name = "bandedGridColumn275";
            this.bandedGridColumn275.Visible = true;
            // 
            // bandedGridColumn276
            // 
            this.bandedGridColumn276.Caption = "[86,90]";
            this.bandedGridColumn276.CustomizationCaption = "GSM电平[86,90]";
            this.bandedGridColumn276.FieldName = "Rxl86_90";
            this.bandedGridColumn276.Name = "bandedGridColumn276";
            this.bandedGridColumn276.Visible = true;
            // 
            // bandedGridColumn277
            // 
            this.bandedGridColumn277.Caption = "[91,94]";
            this.bandedGridColumn277.CustomizationCaption = "GSM电平[91,94]";
            this.bandedGridColumn277.FieldName = "Rxl91_94";
            this.bandedGridColumn277.Name = "bandedGridColumn277";
            this.bandedGridColumn277.Visible = true;
            // 
            // bandedGridColumn278
            // 
            this.bandedGridColumn278.Caption = "大于94";
            this.bandedGridColumn278.CustomizationCaption = "GSM电平大于94";
            this.bandedGridColumn278.FieldName = "Rxl94";
            this.bandedGridColumn278.Name = "bandedGridColumn278";
            this.bandedGridColumn278.Visible = true;
            // 
            // bandedGridColumn279
            // 
            this.bandedGridColumn279.Caption = "均值";
            this.bandedGridColumn279.CustomizationCaption = "DPCH C/I均值";
            this.bandedGridColumn279.FieldName = "Dc2imean";
            this.bandedGridColumn279.Name = "bandedGridColumn279";
            this.bandedGridColumn279.Visible = true;
            // 
            // bandedGridColumn280
            // 
            this.bandedGridColumn280.Caption = "小于-10";
            this.bandedGridColumn280.CustomizationCaption = "DPCH C/I小于-10";
            this.bandedGridColumn280.FieldName = "Dc2iF10";
            this.bandedGridColumn280.Name = "bandedGridColumn280";
            this.bandedGridColumn280.Visible = true;
            // 
            // bandedGridColumn281
            // 
            this.bandedGridColumn281.Caption = "[-10,-3]";
            this.bandedGridColumn281.CustomizationCaption = "DPCH C/I[-10,-3]";
            this.bandedGridColumn281.FieldName = "Dc2iF10TF3";
            this.bandedGridColumn281.Name = "bandedGridColumn281";
            this.bandedGridColumn281.Visible = true;
            // 
            // bandedGridColumn282
            // 
            this.bandedGridColumn282.Caption = "[-3,15]";
            this.bandedGridColumn282.CustomizationCaption = "DPCH C/I[-3,15]";
            this.bandedGridColumn282.FieldName = "Dc2iF3T15";
            this.bandedGridColumn282.Name = "bandedGridColumn282";
            this.bandedGridColumn282.Visible = true;
            // 
            // bandedGridColumn283
            // 
            this.bandedGridColumn283.Caption = "大于15";
            this.bandedGridColumn283.CustomizationCaption = "DPCH C/I大于15";
            this.bandedGridColumn283.FieldName = "Dc2i15";
            this.bandedGridColumn283.Name = "bandedGridColumn283";
            this.bandedGridColumn283.Visible = true;
            // 
            // bandedGridColumn284
            // 
            this.bandedGridColumn284.Caption = "均值";
            this.bandedGridColumn284.CustomizationCaption = "TxPower均值";
            this.bandedGridColumn284.FieldName = "Txpowermean";
            this.bandedGridColumn284.Name = "bandedGridColumn284";
            this.bandedGridColumn284.Visible = true;
            // 
            // bandedGridColumn285
            // 
            this.bandedGridColumn285.Caption = "小于-20";
            this.bandedGridColumn285.CustomizationCaption = "TxPower小于-20";
            this.bandedGridColumn285.FieldName = "TxpowerF20";
            this.bandedGridColumn285.Name = "bandedGridColumn285";
            this.bandedGridColumn285.Visible = true;
            // 
            // bandedGridColumn286
            // 
            this.bandedGridColumn286.Caption = "[-20,0]";
            this.bandedGridColumn286.CustomizationCaption = "TxPower[-20,0]";
            this.bandedGridColumn286.FieldName = "TxpowerF20T0";
            this.bandedGridColumn286.Name = "bandedGridColumn286";
            this.bandedGridColumn286.Visible = true;
            // 
            // bandedGridColumn287
            // 
            this.bandedGridColumn287.Caption = "[0,15]";
            this.bandedGridColumn287.CustomizationCaption = "TxPower[0,15]";
            this.bandedGridColumn287.FieldName = "Txpower0T15";
            this.bandedGridColumn287.Name = "bandedGridColumn287";
            this.bandedGridColumn287.Visible = true;
            // 
            // bandedGridColumn288
            // 
            this.bandedGridColumn288.Caption = "大于15";
            this.bandedGridColumn288.CustomizationCaption = "TxPower大于15";
            this.bandedGridColumn288.FieldName = "Txpower15";
            this.bandedGridColumn288.Name = "bandedGridColumn288";
            this.bandedGridColumn288.Visible = true;
            // 
            // bandedGridColumn289
            // 
            this.bandedGridColumn289.Caption = "成功次数";
            this.bandedGridColumn289.CustomizationCaption = "T至G切换成功次数";
            this.bandedGridColumn289.FieldName = "HO_SUCC_T2G";
            this.bandedGridColumn289.Name = "bandedGridColumn289";
            this.bandedGridColumn289.Visible = true;
            // 
            // bandedGridColumn290
            // 
            this.bandedGridColumn290.Caption = "失败次数";
            this.bandedGridColumn290.CustomizationCaption = "T至G切换失败次数";
            this.bandedGridColumn290.FieldName = "HO_FAIL_T2G";
            this.bandedGridColumn290.Name = "bandedGridColumn290";
            this.bandedGridColumn290.Visible = true;
            // 
            // bandedGridColumn291
            // 
            this.bandedGridColumn291.Caption = "时延";
            this.bandedGridColumn291.CustomizationCaption = "T至G切换时延";
            this.bandedGridColumn291.FieldName = "HO_Time_T2G";
            this.bandedGridColumn291.Name = "bandedGridColumn291";
            this.bandedGridColumn291.Visible = true;
            // 
            // bandedGridColumn292
            // 
            this.bandedGridColumn292.Caption = "成功次数";
            this.bandedGridColumn292.CustomizationCaption = "T网硬切换成功次数";
            this.bandedGridColumn292.FieldName = "HO_SUCC_T2T";
            this.bandedGridColumn292.Name = "bandedGridColumn292";
            this.bandedGridColumn292.Visible = true;
            // 
            // bandedGridColumn293
            // 
            this.bandedGridColumn293.Caption = "失败次数";
            this.bandedGridColumn293.CustomizationCaption = "T网硬切换失败次数";
            this.bandedGridColumn293.FieldName = "HO_FAIL_T2T";
            this.bandedGridColumn293.Name = "bandedGridColumn293";
            this.bandedGridColumn293.Visible = true;
            // 
            // bandedGridColumn294
            // 
            this.bandedGridColumn294.Caption = "时延";
            this.bandedGridColumn294.CustomizationCaption = "T网硬切换时延";
            this.bandedGridColumn294.FieldName = "HO_Time_T2T";
            this.bandedGridColumn294.Name = "bandedGridColumn294";
            this.bandedGridColumn294.Visible = true;
            // 
            // bandedGridColumn295
            // 
            this.bandedGridColumn295.Caption = "成功次数";
            this.bandedGridColumn295.CustomizationCaption = "T网接力切换成功次数";
            this.bandedGridColumn295.FieldName = "HO_SUCC_Baton";
            this.bandedGridColumn295.Name = "bandedGridColumn295";
            this.bandedGridColumn295.Visible = true;
            // 
            // bandedGridColumn296
            // 
            this.bandedGridColumn296.Caption = "失败次数";
            this.bandedGridColumn296.CustomizationCaption = "T网接力切换失败次数";
            this.bandedGridColumn296.FieldName = "HO_FAIL_Baton";
            this.bandedGridColumn296.Name = "bandedGridColumn296";
            this.bandedGridColumn296.Visible = true;
            // 
            // bandedGridColumn297
            // 
            this.bandedGridColumn297.Caption = "时延";
            this.bandedGridColumn297.CustomizationCaption = "T网接力切换时延";
            this.bandedGridColumn297.FieldName = "HO_Time_Baton";
            this.bandedGridColumn297.Name = "bandedGridColumn297";
            this.bandedGridColumn297.Visible = true;
            // 
            // bandedGridColumn298
            // 
            this.bandedGridColumn298.Caption = "成功次数";
            this.bandedGridColumn298.CustomizationCaption = "T网重选成功次数";
            this.bandedGridColumn298.FieldName = "CR_SUCC_T2T";
            this.bandedGridColumn298.Name = "bandedGridColumn298";
            this.bandedGridColumn298.Visible = true;
            // 
            // bandedGridColumn299
            // 
            this.bandedGridColumn299.Caption = "失败次数";
            this.bandedGridColumn299.CustomizationCaption = "T网重选失败次数";
            this.bandedGridColumn299.FieldName = "CR_FAIL_T2T";
            this.bandedGridColumn299.Name = "bandedGridColumn299";
            this.bandedGridColumn299.Visible = true;
            // 
            // bandedGridColumn300
            // 
            this.bandedGridColumn300.Caption = "成功次数";
            this.bandedGridColumn300.CustomizationCaption = "T至G重选成功次数";
            this.bandedGridColumn300.FieldName = "CR_SUCC_T2G";
            this.bandedGridColumn300.Name = "bandedGridColumn300";
            this.bandedGridColumn300.Visible = true;
            // 
            // bandedGridColumn301
            // 
            this.bandedGridColumn301.Caption = "失败次数";
            this.bandedGridColumn301.CustomizationCaption = "T至G重选失败次数";
            this.bandedGridColumn301.FieldName = "CR_FAIL_T2G";
            this.bandedGridColumn301.Name = "bandedGridColumn301";
            this.bandedGridColumn301.Visible = true;
            // 
            // bandedGridColumn302
            // 
            this.bandedGridColumn302.Caption = "成功次数";
            this.bandedGridColumn302.CustomizationCaption = "G至T重选成功次数";
            this.bandedGridColumn302.FieldName = "CR_SUCC_G2T";
            this.bandedGridColumn302.Name = "bandedGridColumn302";
            this.bandedGridColumn302.Visible = true;
            // 
            // bandedGridColumn303
            // 
            this.bandedGridColumn303.Caption = "失败次数";
            this.bandedGridColumn303.CustomizationCaption = "G至T重选失败次数";
            this.bandedGridColumn303.FieldName = "CR_FAIL_G2T";
            this.bandedGridColumn303.Name = "bandedGridColumn303";
            this.bandedGridColumn303.Visible = true;
            // 
            // bandedGridColumn304
            // 
            this.bandedGridColumn304.Caption = "Hsdpa下载量占比";
            this.bandedGridColumn304.CustomizationCaption = "Hsdpa下载量占比";
            this.bandedGridColumn304.FieldName = "HSDPA_Size";
            this.bandedGridColumn304.Name = "bandedGridColumn304";
            this.bandedGridColumn304.Visible = true;
            // 
            // bandedGridColumn305
            // 
            this.bandedGridColumn305.Caption = "Hsdpa时长占比";
            this.bandedGridColumn305.CustomizationCaption = "Hsdpa时长占比";
            this.bandedGridColumn305.FieldName = "HSDPA_Time";
            this.bandedGridColumn305.Name = "bandedGridColumn305";
            this.bandedGridColumn305.Visible = true;
            // 
            // bandedGridColumn306
            // 
            this.bandedGridColumn306.Caption = "R4下载量占比";
            this.bandedGridColumn306.CustomizationCaption = "R4下载量占比";
            this.bandedGridColumn306.FieldName = "R4_Size";
            this.bandedGridColumn306.Name = "bandedGridColumn306";
            this.bandedGridColumn306.Visible = true;
            // 
            // bandedGridColumn307
            // 
            this.bandedGridColumn307.Caption = "R4时长占比";
            this.bandedGridColumn307.CustomizationCaption = "R4时长占比";
            this.bandedGridColumn307.FieldName = "R4_Time";
            this.bandedGridColumn307.Name = "bandedGridColumn307";
            this.bandedGridColumn307.Visible = true;
            // 
            // bandedGridColumn308
            // 
            this.bandedGridColumn308.Caption = "Gsm下载量占比";
            this.bandedGridColumn308.CustomizationCaption = "Gsm下载量占比";
            this.bandedGridColumn308.FieldName = "EDGE_Size";
            this.bandedGridColumn308.Name = "bandedGridColumn308";
            this.bandedGridColumn308.Visible = true;
            // 
            // bandedGridColumn309
            // 
            this.bandedGridColumn309.Caption = "Gsm时长占比";
            this.bandedGridColumn309.CustomizationCaption = "Gsm时长占比";
            this.bandedGridColumn309.FieldName = "EDGE_Time";
            this.bandedGridColumn309.Name = "bandedGridColumn309";
            this.bandedGridColumn309.Visible = true;
            // 
            // bandedGridColumn310
            // 
            this.bandedGridColumn310.Caption = "TOP1小区";
            this.bandedGridColumn310.CustomizationCaption = "TOP1小区";
            this.bandedGridColumn310.FieldName = "Cell1";
            this.bandedGridColumn310.Name = "bandedGridColumn310";
            this.bandedGridColumn310.Visible = true;
            this.bandedGridColumn310.Width = 85;
            // 
            // bandedGridColumn311
            // 
            this.bandedGridColumn311.Caption = "TOP2小区";
            this.bandedGridColumn311.CustomizationCaption = "TOP2小区";
            this.bandedGridColumn311.FieldName = "Cell2";
            this.bandedGridColumn311.Name = "bandedGridColumn311";
            this.bandedGridColumn311.Visible = true;
            this.bandedGridColumn311.Width = 85;
            // 
            // bandedGridColumn312
            // 
            this.bandedGridColumn312.Caption = "TOP3小区";
            this.bandedGridColumn312.CustomizationCaption = "TOP3小区";
            this.bandedGridColumn312.FieldName = "Cell3";
            this.bandedGridColumn312.Name = "bandedGridColumn312";
            this.bandedGridColumn312.Visible = true;
            this.bandedGridColumn312.Width = 85;
            // 
            // bandedGridColumn313
            // 
            this.bandedGridColumn313.Caption = "所属网格";
            this.bandedGridColumn313.CustomizationCaption = "所属网格";
            this.bandedGridColumn313.FieldName = "Strgrid";
            this.bandedGridColumn313.Name = "bandedGridColumn313";
            this.bandedGridColumn313.Visible = true;
            // 
            // bandedGridColumn314
            // 
            this.bandedGridColumn314.Caption = "所属道路";
            this.bandedGridColumn314.CustomizationCaption = "所属道路";
            this.bandedGridColumn314.FieldName = "Strroad";
            this.bandedGridColumn314.Name = "bandedGridColumn314";
            this.bandedGridColumn314.Visible = true;
            // 
            // bandedGridColumn315
            // 
            this.bandedGridColumn315.Caption = "经度";
            this.bandedGridColumn315.CustomizationCaption = "经度";
            this.bandedGridColumn315.FieldName = "Imlongitude";
            this.bandedGridColumn315.Name = "bandedGridColumn315";
            this.bandedGridColumn315.Visible = true;
            // 
            // bandedGridColumn316
            // 
            this.bandedGridColumn316.Caption = "纬度";
            this.bandedGridColumn316.CustomizationCaption = "纬度";
            this.bandedGridColumn316.FieldName = "Imlatitude";
            this.bandedGridColumn316.Name = "bandedGridColumn316";
            this.bandedGridColumn316.Visible = true;
            // 
            // bandedGridColumn317
            // 
            this.bandedGridColumn317.Caption = "ifileid";
            this.bandedGridColumn317.CustomizationCaption = "ifileid";
            this.bandedGridColumn317.FieldName = "Ifileid";
            this.bandedGridColumn317.Name = "bandedGridColumn317";
            // 
            // bandedGridColumn318
            // 
            this.bandedGridColumn318.Caption = "istime";
            this.bandedGridColumn318.CustomizationCaption = "istime";
            this.bandedGridColumn318.FieldName = "Istime";
            this.bandedGridColumn318.Name = "bandedGridColumn318";
            // 
            // bandedGridColumn319
            // 
            this.bandedGridColumn319.Caption = "ietime";
            this.bandedGridColumn319.CustomizationCaption = "ietime";
            this.bandedGridColumn319.FieldName = "Ietime";
            this.bandedGridColumn319.Name = "bandedGridColumn319";
            // 
            // bandedGridColumn320
            // 
            this.bandedGridColumn320.Caption = "gridColumn324";
            this.bandedGridColumn320.CustomizationCaption = "gridColumn324";
            this.bandedGridColumn320.FieldName = "Iid";
            this.bandedGridColumn320.Name = "bandedGridColumn320";
            // 
            // gridBand5
            // 
            this.gridBand5.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand5.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand5.Caption = "基础信息";
            this.gridBand5.Columns.Add(this.bandedGridColumn313);
            this.gridBand5.Columns.Add(this.bandedGridColumn314);
            this.gridBand5.Columns.Add(this.bandedGridColumn310);
            this.gridBand5.Columns.Add(this.bandedGridColumn311);
            this.gridBand5.Columns.Add(this.bandedGridColumn312);
            this.gridBand5.Columns.Add(this.bandedGridColumn315);
            this.gridBand5.Columns.Add(this.bandedGridColumn316);
            this.gridBand5.MinWidth = 20;
            this.gridBand5.Name = "gridBand5";
            this.gridBand5.Width = 555;
            // 
            // gridBand54
            // 
            this.gridBand54.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand54.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand54.Caption = "测试信息";
            this.gridBand54.Columns.Add(this.bandedGridColumn257);
            this.gridBand54.Columns.Add(this.bandedGridColumn258);
            this.gridBand54.Columns.Add(this.bandedGridColumn317);
            this.gridBand54.Columns.Add(this.bandedGridColumn318);
            this.gridBand54.Columns.Add(this.bandedGridColumn319);
            this.gridBand54.Columns.Add(this.bandedGridColumn320);
            this.gridBand54.MinWidth = 20;
            this.gridBand54.Name = "gridBand54";
            this.gridBand54.Width = 150;
            // 
            // gridBand55
            // 
            this.gridBand55.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand55.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand55.Caption = "下载速率";
            this.gridBand55.Columns.Add(this.bandedGridColumn259);
            this.gridBand55.Columns.Add(this.bandedGridColumn260);
            this.gridBand55.Columns.Add(this.bandedGridColumn261);
            this.gridBand55.Columns.Add(this.bandedGridColumn262);
            this.gridBand55.Columns.Add(this.bandedGridColumn263);
            this.gridBand55.Columns.Add(this.bandedGridColumn264);
            this.gridBand55.MinWidth = 20;
            this.gridBand55.Name = "gridBand55";
            this.gridBand55.Width = 450;
            // 
            // gridBand56
            // 
            this.gridBand56.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand56.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand56.Caption = "TD电平";
            this.gridBand56.Columns.Add(this.bandedGridColumn265);
            this.gridBand56.Columns.Add(this.bandedGridColumn266);
            this.gridBand56.Columns.Add(this.bandedGridColumn267);
            this.gridBand56.Columns.Add(this.bandedGridColumn268);
            this.gridBand56.Columns.Add(this.bandedGridColumn269);
            this.gridBand56.Columns.Add(this.bandedGridColumn270);
            this.gridBand56.Columns.Add(this.bandedGridColumn271);
            this.gridBand56.MinWidth = 20;
            this.gridBand56.Name = "gridBand56";
            this.gridBand56.Width = 525;
            // 
            // gridBand57
            // 
            this.gridBand57.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand57.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand57.Caption = "GSM电平";
            this.gridBand57.Columns.Add(this.bandedGridColumn272);
            this.gridBand57.Columns.Add(this.bandedGridColumn273);
            this.gridBand57.Columns.Add(this.bandedGridColumn274);
            this.gridBand57.Columns.Add(this.bandedGridColumn275);
            this.gridBand57.Columns.Add(this.bandedGridColumn276);
            this.gridBand57.Columns.Add(this.bandedGridColumn277);
            this.gridBand57.Columns.Add(this.bandedGridColumn278);
            this.gridBand57.MinWidth = 20;
            this.gridBand57.Name = "gridBand57";
            this.gridBand57.Width = 525;
            // 
            // gridBand58
            // 
            this.gridBand58.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand58.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand58.Caption = "DPCH C/I";
            this.gridBand58.Columns.Add(this.bandedGridColumn279);
            this.gridBand58.Columns.Add(this.bandedGridColumn280);
            this.gridBand58.Columns.Add(this.bandedGridColumn281);
            this.gridBand58.Columns.Add(this.bandedGridColumn282);
            this.gridBand58.Columns.Add(this.bandedGridColumn283);
            this.gridBand58.MinWidth = 20;
            this.gridBand58.Name = "gridBand58";
            this.gridBand58.Width = 375;
            // 
            // gridBand59
            // 
            this.gridBand59.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand59.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand59.Caption = "TxPower";
            this.gridBand59.Columns.Add(this.bandedGridColumn284);
            this.gridBand59.Columns.Add(this.bandedGridColumn285);
            this.gridBand59.Columns.Add(this.bandedGridColumn286);
            this.gridBand59.Columns.Add(this.bandedGridColumn287);
            this.gridBand59.Columns.Add(this.bandedGridColumn288);
            this.gridBand59.MinWidth = 20;
            this.gridBand59.Name = "gridBand59";
            this.gridBand59.Width = 375;
            // 
            // gridBand60
            // 
            this.gridBand60.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand60.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand60.Caption = "T至G切换";
            this.gridBand60.Columns.Add(this.bandedGridColumn289);
            this.gridBand60.Columns.Add(this.bandedGridColumn290);
            this.gridBand60.Columns.Add(this.bandedGridColumn291);
            this.gridBand60.MinWidth = 20;
            this.gridBand60.Name = "gridBand60";
            this.gridBand60.Width = 225;
            // 
            // gridBand61
            // 
            this.gridBand61.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand61.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand61.Caption = "T网硬切换";
            this.gridBand61.Columns.Add(this.bandedGridColumn292);
            this.gridBand61.Columns.Add(this.bandedGridColumn293);
            this.gridBand61.Columns.Add(this.bandedGridColumn294);
            this.gridBand61.MinWidth = 20;
            this.gridBand61.Name = "gridBand61";
            this.gridBand61.Width = 225;
            // 
            // gridBand62
            // 
            this.gridBand62.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand62.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand62.Caption = "T网接力切换";
            this.gridBand62.Columns.Add(this.bandedGridColumn295);
            this.gridBand62.Columns.Add(this.bandedGridColumn296);
            this.gridBand62.Columns.Add(this.bandedGridColumn297);
            this.gridBand62.MinWidth = 20;
            this.gridBand62.Name = "gridBand62";
            this.gridBand62.Width = 225;
            // 
            // gridBand63
            // 
            this.gridBand63.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand63.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand63.Caption = "T网重选";
            this.gridBand63.Columns.Add(this.bandedGridColumn298);
            this.gridBand63.Columns.Add(this.bandedGridColumn299);
            this.gridBand63.MinWidth = 20;
            this.gridBand63.Name = "gridBand63";
            this.gridBand63.Width = 150;
            // 
            // gridBand64
            // 
            this.gridBand64.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand64.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand64.Caption = "T至G重选";
            this.gridBand64.Columns.Add(this.bandedGridColumn300);
            this.gridBand64.Columns.Add(this.bandedGridColumn301);
            this.gridBand64.Columns.Add(this.bandedGridColumn302);
            this.gridBand64.Columns.Add(this.bandedGridColumn303);
            this.gridBand64.MinWidth = 20;
            this.gridBand64.Name = "gridBand64";
            this.gridBand64.Width = 300;
            // 
            // gridBand65
            // 
            this.gridBand65.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand65.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand65.Caption = "其它";
            this.gridBand65.Columns.Add(this.bandedGridColumn304);
            this.gridBand65.Columns.Add(this.bandedGridColumn305);
            this.gridBand65.Columns.Add(this.bandedGridColumn306);
            this.gridBand65.Columns.Add(this.bandedGridColumn307);
            this.gridBand65.Columns.Add(this.bandedGridColumn308);
            this.gridBand65.Columns.Add(this.bandedGridColumn309);
            this.gridBand65.MinWidth = 20;
            this.gridBand65.Name = "gridBand65";
            this.gridBand65.Width = 450;
            // 
            // TdXtraLastWeakPoadFormData
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(849, 417);
            this.Controls.Add(this.tableLayoutPanel1);
            this.MaximizeBox = false;
            this.Name = "TdXtraLastWeakPoadFormData";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "TD持续差道路详细信息";
            this.Deactivate += new System.EventHandler(this.TdXtraLastWeakPoadForm_Deactivate);
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            this.tableLayoutPanel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl5)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView9)).EndInit();
            this.xtraTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            this.xtraTabPage3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).EndInit();
            this.xtraTabPage4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).EndInit();
            this.xtraTabPage5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView8)).EndInit();
            this.xtraTabPage6.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView11)).EndInit();
            this.tableLayoutPanel1.ResumeLayout(false);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage3;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage4;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn39;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn40;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn44;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn45;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn46;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn47;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn48;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn49;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn50;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn51;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn109;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn110;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemDIYReplay;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn111;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn112;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn113;
        private DevExpress.XtraGrid.GridControl gridControl5;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn129;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn130;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn131;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn132;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn133;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn134;
        private DevExpress.XtraCharts.ChartControl chartControl1;
        private System.Windows.Forms.Button btnClearFly;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemClearFly;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn135;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn136;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemShowFly;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn137;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn150;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn149;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn148;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn147;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn146;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn145;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn144;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn143;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn142;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn141;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn161;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn160;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn159;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn158;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn157;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn156;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn155;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn154;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn153;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn152;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn151;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn170;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn169;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn168;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn167;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn166;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn165;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn164;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn163;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn162;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage6;
        private DevExpress.XtraGrid.GridControl gridControl2;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn52;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn53;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn54;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn55;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn56;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn57;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn58;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn59;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn60;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn61;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn62;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn63;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn64;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn65;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn66;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn67;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn68;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn69;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn70;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn71;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn72;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn73;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn74;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn75;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn76;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn77;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn78;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn79;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn80;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn81;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn82;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn83;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn84;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn85;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn86;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn87;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn88;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn89;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn90;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView4;
        private DevExpress.XtraGrid.GridControl gridControl3;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn91;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn92;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn93;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn94;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn95;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn96;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn97;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn98;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn99;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn100;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn101;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn102;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn103;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn104;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn105;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn106;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn107;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn108;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn114;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn115;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn116;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn117;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn118;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn119;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn120;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn121;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn122;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn123;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn124;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn125;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn126;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn127;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn128;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn138;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn139;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn140;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn171;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn172;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn173;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn174;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn175;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn176;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn177;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn178;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn179;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn180;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn181;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn182;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn183;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn184;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn185;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn186;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn187;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn188;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn189;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn190;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn191;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn192;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn193;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn194;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn195;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn196;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn197;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView6;
        private DevExpress.XtraGrid.GridControl gridControl4;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn198;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn199;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn200;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn201;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn202;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn203;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn204;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn205;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn206;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn207;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn208;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn209;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn210;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn211;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn212;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn213;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn214;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn215;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn216;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn217;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn218;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn219;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn220;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn221;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn222;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn223;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn224;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn225;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn226;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn227;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn228;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn229;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn230;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn231;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn232;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn233;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn234;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn235;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn236;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn237;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn238;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn239;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn240;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn241;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn242;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn243;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn244;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn245;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn246;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn247;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn248;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn249;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn250;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn251;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn252;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn253;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn254;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn255;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn256;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn257;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn258;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn259;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn260;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView8;
        private DevExpress.XtraGrid.GridControl gridControl6;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn261;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn262;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn263;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn264;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn265;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn266;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn267;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn268;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn269;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn270;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn271;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn272;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn273;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn274;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn275;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn276;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn277;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn278;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn279;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn280;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn281;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn282;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn283;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn284;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn285;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn286;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn287;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn288;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn289;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn290;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn291;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn292;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn293;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn294;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn295;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn296;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn297;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn298;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn299;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn300;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn301;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn302;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn303;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn304;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn305;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn306;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn307;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn308;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn309;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn310;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn311;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn312;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn313;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn314;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn315;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn316;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn317;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn318;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn319;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn320;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn321;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn322;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn323;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView11;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemToEXCEL;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btnNext1;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItemLable;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn324;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn325;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn326;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn327;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn328;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn5;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn6;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn7;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn8;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn9;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn10;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn11;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn12;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn13;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn14;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn15;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn16;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn17;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn18;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn19;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn20;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn21;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn22;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn23;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn24;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn25;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn26;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn27;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn28;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn29;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn30;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn31;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn32;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn33;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn34;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn35;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn36;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn37;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn38;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn39;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn40;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn41;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn42;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn43;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn44;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn45;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn46;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn47;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn48;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn49;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn50;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn51;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn52;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn53;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn54;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn55;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn56;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn57;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn58;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn59;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn60;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn61;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn62;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn63;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn64;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView5;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand14;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand1;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand6;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand15;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand16;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand17;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand7;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand8;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand9;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand10;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand11;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand12;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand13;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn121;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn122;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn118;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn119;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn120;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn123;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn124;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand18;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn65;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn66;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn125;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn126;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn127;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn128;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand19;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn67;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn68;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn69;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn70;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn71;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn72;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand20;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn73;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn74;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn75;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn76;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn77;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn78;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn79;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand21;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn80;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn81;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn82;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn83;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn84;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn85;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn86;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand22;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn87;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn88;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn89;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn90;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn91;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand23;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn92;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn93;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn94;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn95;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn96;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand24;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn97;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn98;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn99;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand25;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn100;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn101;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn102;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand26;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn103;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn104;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn105;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand27;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn106;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn107;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand28;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn108;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn109;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn110;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn111;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand29;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn112;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn113;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn114;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn115;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn116;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn117;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn185;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn186;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn182;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn183;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn184;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn187;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn188;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand30;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn129;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn130;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn189;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn190;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn191;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn192;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand31;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn131;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn132;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn133;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn134;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn135;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn136;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand32;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn137;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn138;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn139;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn140;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn141;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn142;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn143;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand33;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn144;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn145;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn146;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn147;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn148;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn149;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn150;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand34;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn151;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn152;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn153;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn154;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn155;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand35;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn156;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn157;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn158;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn159;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn160;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand36;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn161;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn162;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn163;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand37;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn164;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn165;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn166;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand38;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn167;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn168;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn169;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand39;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn170;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn171;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand40;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn172;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn173;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn174;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn175;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand41;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn176;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn177;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn178;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn179;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn180;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn181;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn249;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn250;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn246;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn247;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn248;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn251;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn252;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand42;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn193;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn194;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn253;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn254;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn255;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn256;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand43;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn195;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn196;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn197;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn198;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn199;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn200;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand44;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn201;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn202;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn203;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn204;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn205;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn206;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn207;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand45;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn208;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn209;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn210;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn211;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn212;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn213;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn214;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand46;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn215;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn216;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn217;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn218;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn219;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand47;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn220;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn221;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn222;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn223;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn224;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand48;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn225;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn226;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn227;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand49;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn228;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn229;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn230;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand50;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn231;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn232;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn233;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand51;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn234;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn235;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand52;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn236;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn237;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn238;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn239;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand53;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn240;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn241;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn242;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn243;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn244;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn245;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand5;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn313;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn314;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn310;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn311;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn312;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn315;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn316;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand54;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn257;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn258;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn317;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn318;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn319;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn320;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand55;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn259;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn260;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn261;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn262;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn263;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn264;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand56;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn265;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn266;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn267;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn268;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn269;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn270;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn271;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand57;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn272;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn273;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn274;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn275;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn276;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn277;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn278;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand58;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn279;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn280;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn281;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn282;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn283;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand59;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn284;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn285;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn286;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn287;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn288;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand60;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn289;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn290;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn291;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand61;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn292;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn293;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn294;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand62;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn295;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn296;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn297;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand63;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn298;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn299;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand64;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn300;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn301;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn302;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn303;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand65;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn304;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn305;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn306;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn307;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn308;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn309;
    }
}