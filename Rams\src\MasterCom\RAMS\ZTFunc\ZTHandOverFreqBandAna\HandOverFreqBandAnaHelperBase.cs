﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public abstract class HandOverFreqBandAnaHelperBase
    {
        protected List<int> handOverEvtList { get; set; } = new List<int>();

        protected abstract void intEvtList();

        public abstract void DealWithData(DTFileDataManager file);

        protected abstract int getHandOverSrcArfcn(Event e);

        protected abstract int getHandOverTargetArfcn(Event e);

        protected int getValidData(int? data)
        {
            if (data == null)
            {
                return 0;
            }
            return (int)data;
        }

        protected float getValidData(float? data)
        {
            if (data == null)
            {
                return 99;
            }
            return (float)data;
        }
    }
}
