﻿using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLteNBCellCheckDiffFreqSpanAnaByFile : ZTLteNBCellCheckDiffFreqSpanAnaBase
    {
        public ZTLteNBCellCheckDiffFreqSpanAnaByFile(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected static readonly object lockObj = new object();
        private static ZTLteNBCellCheckDiffFreqSpanAnaByFile intance = null;
        public static ZTLteNBCellCheckDiffFreqSpanAnaByFile GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTLteNBCellCheckDiffFreqSpanAnaByFile(MainModel.GetInstance());
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "LTE邻区核查(按文件)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22045, this.Name);//////
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }
    public class LteFddNBCellCheckDiffFreqSpanAnaByFile : ZTLteNBCellCheckDiffFreqSpanAnaByFile
    {
        public LteFddNBCellCheckDiffFreqSpanAnaByFile(MainModel mainModel)
            : base(mainModel)
        {
        }
        private static LteFddNBCellCheckDiffFreqSpanAnaByFile instance = null;
        public new static LteFddNBCellCheckDiffFreqSpanAnaByFile GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LteFddNBCellCheckDiffFreqSpanAnaByFile(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26019, this.Name);
        }
    }
}