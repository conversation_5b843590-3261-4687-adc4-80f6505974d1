using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.UserMng;

namespace MasterCom.RAMS.Model
{
    public class SubFunction : IUserInfoObject
    {
        public SubFunction()
        {

        }

        public int FuncID { get; set; }

        public int ID { get; set; }

        public string LoginName { get; set; }

        public string Description { get; set; }

        public override string ToString()
        {
            return LoginName;
        }

        public static SubFunction Fill(Content content)
        {
            SubFunction subFunc = new SubFunction();
            subFunc.FuncID = content.GetParamInt();
            subFunc.ID = content.GetParamInt();
            subFunc.LoginName = content.GetParamString();
            subFunc.Description = content.GetParamString();
            return subFunc;
        }

        public FuncExportPermit ExportPermit { get; set; }
    }
}
