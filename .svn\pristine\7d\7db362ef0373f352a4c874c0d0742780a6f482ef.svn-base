﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ;
using MasterCom.Util.UiEx;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Text;
using Excel = Microsoft.Office.Interop.Excel;

namespace MasterCom.RAMS.ZTFunc
{
    class LteFddStationAcceptManager : LteStationAcceptManager
    {
        public const int FddMaxCellCount = 9;
        //单验模板所在目录
        protected string workDir = "";

        public override void SetAcceptCond(LteStationAcceptCondition cond)
        {
            workDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "userdata/LteFddStationAcceptance");
            errMsg = "";
            acceptCond = cond;

            acceptorList = new List<LteStationAcceptBase>()
            {
                new FddAcpCellName(),
                new FddAcpFtpDownload(),
                new FddAcpFtpUpload(),
                new FddAcpInnerHandover(),
                new FddAcpInnerHandoverPic(),
                new FddAcpCsfbRate(),
                new FddAcpRrcRate(),
                new FddAcpErabRate(),
                new FddAcpAccRate(),
                new FddAcp24ReselectRate(),
                new FddAcpCoverPicture(),
                new FddAcpAntennaInfo(),
                new FddAcpCellActualParameter(),
                new FddAcpCellPlanParameter(),
                new FddAcpCellPerformanceParameter(),
                new FddAcpCellAlarm(),
                new FddAcpVolteVoiceMo(),
                new FddAcpVolteVoiceMt(),
            };

            DiyQueryFddDBSetting.GetInstance().Query();
        }

        protected override string getPath()
        {
            return Singleton<FddStationAcceptConfigHelper>.Instance.GetCurSavePath();
        }

        protected override void exportFile(StringBuilder exportedFiles, string bts, string targetFile, int cellCount)
        {
            Excel.Application xlApp = null;
            try
            {
                WaitTextBox.Text = "正在导出Excel...";
                xlApp = new Excel.Application();
                xlApp.Visible = false;
                Excel.Workbook eBook = xlApp.Workbooks.Open(targetFile,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing, Type.Missing, Type.Missing,
                    Type.Missing, Type.Missing);

                foreach (LteStationAcceptBase acp in acceptorList)
                {
                    if (acp is FddAcpInnerHandover)
                    {
                        (acp as FddAcpInnerHandover).ReSetResultGridValue(cellCount);
                    }
                    acp.FillResult(bts, eBook);
                }

                eBook.Save();
                eBook.Close(Type.Missing, Type.Missing, Type.Missing);
                exportedFiles.Append(bts);
                exportedFiles.Append(",");
            }
            finally
            {
                if (xlApp != null)
                {
                    xlApp.Quit();
                }
            }
        }

        protected override bool judgeValidFile(string btsName, int cellCount)
        {
            if (cellCount > FddMaxCellCount)
            {
                errMsg = $"基站{btsName}小区数超过{FddMaxCellCount}个，不支持报告导出";
                return false;
            }
            return true;
        }

        protected override void CreateFileForBts(Dictionary<string, int> btsDic)
        {
            StringBuilder exportedFiles = new StringBuilder();
            foreach (string bts in btsDic.Keys)
            {
                int cellCount = 0;
                foreach (var acp in acceptorList)
                {
                    LteFddStationAccept fddAcp = acp as LteFddStationAccept;
                    cellCount = Math.Max(cellCount, fddAcp.CurBtsCellCount);
                }
                bool isValid = judgeValidFile(bts, cellCount);
                if (isValid)
                {
                    string targetFile = GetTargetFile(bts, cellCount, acceptCond.SaveFolder);
                    if (!string.IsNullOrEmpty(targetFile))
                    {
                        exportFile(exportedFiles, bts, targetFile, cellCount);
                    }
                }
            }
            hasExportedFiles = exportedFiles.ToString().TrimEnd(',');
        }

        public new virtual string GetTargetFile(string btsName, int cellCount, string saveFolder)
        {
            string templateFile;
            if (cellCount <= 3)
            {
                templateFile = "LTEFDD新站验收模板_3扇区.xlsx";
            }
            else if (cellCount <= 6)
            {
                templateFile = "LTEFDD新站验收模板_6扇区.xlsx";
            }
            else
            {
                templateFile = "LTEFDD新站验收模板_9扇区.xlsx";
            }
            templateFile = Path.Combine(workDir, templateFile);

            if (!File.Exists(templateFile))
            {
                errMsg = string.Format("[{0}]路径下不存在报告模板文件", templateFile);
                return "";
            }

            string targetFile = string.Format("LTEFDD新站验收_{0}.xlsx", btsName);
            targetFile = Path.Combine(saveFolder, targetFile);
            if (File.Exists(targetFile))
            {
                File.Delete(targetFile);
            }
            File.Copy(templateFile, targetFile);
            return targetFile;
        }

        public override void AnalyzeFile(Model.FileInfo fileInfo, DTFileDataManager fileManager)
        {
            try
            {
                bool isHandoverFile = fileManager.FileName.Contains("切换");
                ICell targetCell = GetTargetCell(fileManager, isHandoverFile);
                if (targetCell == null || targetCell is UnknowCell)
                {
                    log.Info(string.Format("文件{0}未找到目标小区", fileInfo.Name));
                    return;
                }
                string btsName = GetFileBtsName((LTECell)targetCell, fileManager.FileName, isHandoverFile);
                if (string.IsNullOrEmpty(btsName))
                {
                    log.Info(string.Format("文件{0}未找到目标基站", fileInfo.Name));
                    return;
                }

                foreach (LteStationAcceptBase acp in acceptorList)
                {
                    LteFddStationAccept fddAcp = acp as LteFddStationAccept;
                    fddAcp.AnalyzeFile(fileInfo, fileManager, targetCell, btsName);
                }
            }
            catch
            {
                Clear();
                throw;
            }
        }

        /// <summary>
        /// 根据文件中的采样点TAC,ECI,EARFCN,PCI和测试LOG文件名获取对应的单验目标小区
        /// </summary>
        /// <param name="fileManager"></param>
        /// <returns></returns>
        protected LTECell GetTargetCell(DTFileDataManager fileManager, bool isHandoverFile)
        {
            LTECell targeCell = null;
            foreach (TestPoint tp in fileManager.TestPoints)
            {
                LTECell cell = StationAcceptCellHelper_XJ.Instance.GetLTECell(tp);
                if (cell != null && cell.BelongBTS.Type == LTEBTSType.Outdoor)
                {
                    string keyStr = cell.Name;
                    if (isHandoverFile)
                    {
                        keyStr = GetFileBtsName(cell, fileManager.FileName, isHandoverFile);
                    }
                    if (!string.IsNullOrEmpty(keyStr) && fileManager.FileName.Contains(keyStr.Trim()))
                    {
                        targeCell = cell;
                        break;
                    }
                }
            }
            return targeCell;
        }

        /// <summary>
        /// 根据测试文件名重新对应获取基站名
        /// </summary>
        /// <param name="lteCell"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        //protected virtual string GetBtsName(LTECell lteCell, string fileName)
        //{
        //    if (fileName.Contains("-FDD"))
        //    {
        //        //TDD共站就是原站名
        //        string btsName = lteCell.BelongBTS.Name;
        //        if (btsName.Contains("-FDD") || btsName.Contains("-NB"))
        //        {
        //            //NB,FDD,FDD900,FDD1800共站返回去除业务的前缀名
        //            btsName = btsName.Substring(0, btsName.LastIndexOf('-'));
        //        }

        //        //根据不同FDD测试文件修改对应的FDD站名
        //        if (fileName.Contains("-FDD900"))
        //        {
        //            btsName = btsName + "-FDD900";
        //        }
        //        else if (fileName.Contains("-FDD1800"))
        //        {
        //            btsName = btsName + "-FDD1800";
        //        }
        //        else
        //        {
        //            btsName = btsName + "-FDD";
        //        }

        //        return btsName;
        //    }
        //    return "";


        //    //string btsName = lteCell.BelongBTS.Name;
        //    //string btsPrefix = "";
        //    //if (btsName.Contains("-FDD") || btsName.Contains("-NB"))
        //    //{
        //    //    //由于存在NB共站,假如站名为NB站名时,替换为FDD
        //    //    btsPrefix = btsName.Substring(0, btsName.LastIndexOf('-'));

        //    //    //根据不同FDD测试文件修改对应的FDD站名
        //    //    if (fileName.Contains("-FDD900"))
        //    //    {
        //    //        btsName = btsPrefix + "-FDD900";
        //    //    }
        //    //    else if (fileName.Contains("-FDD1800"))
        //    //    {
        //    //        btsName = btsPrefix + "-FDD1800";
        //    //    }
        //    //    else
        //    //    {
        //    //        btsName = btsPrefix + "-FDD";
        //    //    }
        //    //}
        //    //else
        //    //{
        //    //    btsName = "";
        //    //}

        //    //return btsName;
        //}

        /// <summary>
        /// 根据命名规则获取文件名中的基站名
        /// </summary>
        public string GetFileBtsName(LTECell cell, string fileName, bool isHandoverFile)
        {
            string fileBtsName = "";
            string[] strs = fileName.Split('_');
            if (strs.Length < 3)
            {
                return "";
            }
            if (isHandoverFile)
            {
                fileBtsName = strs[2];
            }
            else
            {
                int idx = strs[2].LastIndexOf('-');
                if (idx > 0)
                {
                    fileBtsName = strs[2].Substring(0, idx);
                }
            }

            if (!cell.Name.Contains(fileBtsName))
            {
                fileBtsName = "";
            }

            return fileBtsName;
        }
    }
}
