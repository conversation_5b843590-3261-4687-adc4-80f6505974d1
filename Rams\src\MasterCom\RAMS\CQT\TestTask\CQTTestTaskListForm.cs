﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using DevExpress.XtraEditors.Controls;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTTestTaskListForm : MinCloseForm
    {
        public CQTTestTaskListForm(MainModel mm)
            : base(mm)
        {
            InitializeComponent();
            initValues();
        }

        private void initValues()
        {
            if (!MainModel.User.HasFunctionRight(406))
            {
                groupControlNewTask.Visible = false;
                btnDeleteTask.Visible = false;
            }
            dateEditBeginTime.DateTime = DateTime.Now.Date.AddDays(-7);
            dateEditEndTime.DateTime = dateEditBeginTime.DateTime.AddDays(7);
            fillCareerIDBox();
            fillAgentBox();
            fillCQTPointBox();
        }
        List<CQTTestTask> curTestTask = new List<CQTTestTask>();
        public void FillTestTask(List<CQTTestTask> list)
        {
            curTestTask = list;
            gridControl.DataSource = list;
            gridControl.RefreshDataSource();
        }

        public void fillCareerIDBox()
        {
            cbbxCareerID.Properties.Items.Clear();
            foreach (CategoryEnumItem item in ((CategoryEnum)CategoryManager.GetInstance()["Carrier"]).Items)
            {
                cbbxCareerID.Properties.Items.Add(item);
            }
            cbbxCareerID.SelectedIndex = cbbxCareerID.Properties.Items.Count > 0 ? 0 : -1;
        }

        private void fillServiceTypeBox()
        {
            int careeID = ((CategoryEnumItem)cbbxCareerID.SelectedItem).ID;
            cbbxServiceType.Properties.Items.Clear();
            CategoryEnumItem[] projItems = ((CategoryEnum)CategoryManager.GetInstance()["ServiceType"]).Items;
            foreach (CategoryEnumItem item in projItems)
            {
                if (ServiceTypeManager.getInstance().ServCarrierDic.ContainsKey(item.ID) && ServiceTypeManager.getInstance().ServCarrierDic[item.ID].Contains(careeID))
                {
                    cbbxServiceType.Properties.Items.Add(item);
                }
            }
            cbbxServiceType.SelectedIndex = cbbxServiceType.Properties.Items.Count > 0 ? 0 : -1;
        }

        private void fillAgentBox()
        {
            cbbxAgent.Properties.Items.Clear();
            CategoryEnumItem[] agentItems = ((CategoryEnum)CategoryManager.GetInstance()["Agent"]).Items;
            foreach (CategoryEnumItem item in agentItems)
            {
                cbbxAgent.Properties.Items.Add(item);
            }
            cbbxAgent.SelectedIndex = cbbxAgent.Properties.Items.Count > 0 ? 0 : -1;
        }

        private void fillCQTPointBox()
        {
            checkedListBoxPoints.Items.Clear();
            foreach (CQTPoint point in CQTPointManager.GetInstance().CQTPoints)
            {
                checkedListBoxPoints.Items.Add(point);
            }
            checkedListBoxPoints.SortOrder = SortOrder.Ascending;
        }

        private void buttonEditSearch_Properties_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            string findStr = buttonEditSearch.Text.Trim();
            if (findStr.Length == 0)
            {
                return;
            }
            findPointByString(findStr, e.Button.Index == 1);
        }

        private void findPointByString(string findStr, bool findNext)
        {
            if (findNext)//next
            {
                for (int i = checkedListBoxPoints.SelectedIndex + 1; i < checkedListBoxPoints.Items.Count; i++)
                {
                    string text = checkedListBoxPoints.GetDisplayItemValue(i).ToString();
                    if (text.Contains(findStr))
                    {
                        checkedListBoxPoints.SelectedIndex = i;
                        return;
                    }
                }
            }
            else//prev
            {
                for (int i = checkedListBoxPoints.SelectedIndex - 1; i >= 0; i--)
                {
                    string text = checkedListBoxPoints.GetDisplayItemValue(i).ToString();
                    if (text.Contains(findStr))
                    {
                        checkedListBoxPoints.SelectedIndex = i;
                        return;
                    }
                }
            }
        }

        private void buttonEditSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                string findStr = buttonEditSearch.Text.Trim();
                if (findStr.Length == 0)
                {
                    return;
                }
                findPointByString(findStr, true);
            }
        }

        private void dateEditBeginTime_EditValueChanged(object sender, EventArgs e)
        {
            if (dateEditBeginTime.DateTime.Date > dateEditEndTime.DateTime.Date)
            {
                errorProvider.SetError(dateEditBeginTime, "开始时间不能大于结束时间！");
            }
            else
            {
                makeDefaultName();
                errorProvider.Clear();
            }
        }

        private void dateEditEndTime_EditValueChanged(object sender, EventArgs e)
        {
            if (dateEditEndTime.DateTime.Date < dateEditBeginTime.DateTime.Date)
            {
                errorProvider.SetError(dateEditEndTime, "结束时间不能小于开始时间！");
            }
            else
            {
                makeDefaultName();
                errorProvider.Clear();
            }
        }

        private void makeDefaultName()
        {
            textEditTaskName.Text = string.Format("投诉测试_{0}_{1}", dateEditBeginTime.DateTime.Date.ToString("yyyyMMdd"), dateEditEndTime.DateTime.Date.ToString("yyyyMMdd"));
        }

        private void popupContainerEditPoint_QueryResultValue(object sender, DevExpress.XtraEditors.Controls.QueryResultValueEventArgs e)
        {
            StringBuilder checkPoints = new StringBuilder();
            foreach (object item in checkedListBoxPoints.CheckedItems)
            {
                checkPoints.Append(item.ToString() + ";");
            }
            e.Value = checkPoints;
        }

        private void btnNew_Click(object sender, EventArgs e)
        {
            int agenteId = -1;
            int serviceId = -1;
       
            if (cbbxAgent.SelectedItem is CategoryEnumItem)
            {
                agenteId = (cbbxAgent.SelectedItem as CategoryEnumItem).ID;
            }
            else
            {
                errorProvider.SetError(cbbxAgent, "请选择承担商！");
                return;
            }

            if (cbbxServiceType.SelectedItem is CategoryEnumItem)
            {
                serviceId = (cbbxServiceType.SelectedItem as CategoryEnumItem).ID;
            }
            else
            {
                errorProvider.SetError(cbbxServiceType, "请选择业务！");
                return;
            }

            string taskName = "";
            if (textEditTaskName.Text.Trim().Length == 0)
            {
                MessageBox.Show("任务名称不能为空！");
                return;
            }
            else
            {
                taskName = textEditTaskName.Text;
            }

            if (dateEditBeginTime.DateTime > dateEditEndTime.DateTime)
            {
                MessageBox.Show("开始时间不能大于结束时间！");
                return;
            }

            DateTime dt = (DateTime)timeSpan.EditValue;
            if (dt.TimeOfDay.TotalSeconds == 0)
            {
                MessageBox.Show("计划测试时长不能等于0！");
                return;
            }

            List<CQTPoint> points = new List<CQTPoint>();
            foreach (CheckedListBoxItem item in checkedListBoxPoints.Items)
            {
                if (item.CheckState == CheckState.Checked)
                {
                    points.Add((CQTPoint)item.Value);
                }
            }
            if (points.Count == 0)
            {
                MessageBox.Show("测试地点不能为空！");
                return;
            }

            List<CQTTestTask> tasks = new List<CQTTestTask>();
            foreach (CQTPoint point in points)
            {
                CQTTestTask testTask = new CQTTestTask();
                testTask.AgentID = agenteId;
                testTask.BeginTime = dateEditBeginTime.DateTime.Date;
                testTask.EndTime = dateEditEndTime.DateTime.Date.AddDays(1).AddMilliseconds(-1);
                testTask.Comment = textEditComment.Text;
                testTask.CQTPoint = point;
                testTask.CreatorID = MainModel.User.ID;
                testTask.ID = Guid.NewGuid();
                testTask.Name = taskName;
                testTask.CareerID = ((CategoryEnumItem)cbbxCareerID.SelectedItem).ID;
                testTask.ServiceID = serviceId;
                testTask.Target = TimeSpan.FromSeconds(dt.TimeOfDay.TotalSeconds);
                tasks.Add(testTask);
            }
            DIYSQLInsertCQTTestTask insertTask = new DIYSQLInsertCQTTestTask(MainModel,tasks);
            insertTask.Query();
            curTestTask.AddRange(tasks);
            gridControl.DataSource = curTestTask;
            gridControl.RefreshDataSource();
        }

        private void checkAllPoints_CheckedChanged(object sender, EventArgs e)
        {
            if (checkAllPoints.Checked)
            {
                checkedListBoxPoints.CheckAll();
            }
            else
            {
                checkedListBoxPoints.UnCheckAll();
            }
        }

        private void btnQueryTaskResult_Click(object sender, EventArgs e)
        {
            int[] rows = gridView.GetSelectedRows();
            if (rows.Length > 0)
            {
                List<CQTTestTask> tasks = new List<CQTTestTask>();
                for (int i = 0; i < rows.Length; i++)
                {
                    CQTTestTask task = gridView.GetRow(rows[i]) as CQTTestTask;
                    tasks.Add(task);
                }
                DIYQueryCQTTestTaskResult query = new DIYQueryCQTTestTaskResult(MainModel, tasks);
                query.Query();
                gridControl.RefreshDataSource();
                gridControl.Invalidate();
            }
            else
            {
                MessageBox.Show("请选择要查询的测试任务！");
            }
        }

        private void cbbxCareerID_SelectedIndexChanged(object sender, EventArgs e)
        {
            fillServiceTypeBox();
        }

        private void gridView_RowCellStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs e)
        {
            if (e.Column == null || e.RowHandle == -1)
            {
                return;
            }
            try
            {
                CQTTestTask task = gridView.GetRow(e.RowHandle) as CQTTestTask;
                if (task==null)
                {
                    return;
                }
                if (task.SubPointsTestInfo != null&& task.RestDays <= 3)
                {
                    if (task.ComplianceRate > 90)
                    {
                        e.Appearance.BackColor = Color.Lime;
                    }
                    else  if ( task.ComplianceRate > 80)
                    {
                        e.Appearance.BackColor = Color.Green;
                    }
                    else if (task.ComplianceRate>50)
                    {
                        e.Appearance.BackColor = Color.Orange;
                    }
                    else 
                    {
                        e.Appearance.BackColor = Color.Red;
                    }
                }
                if (e.Column.Equals(gcRestDays))
                {
                    setRestDaysColor(e);
                }
                else if (e.Column.Equals(gcComplianceRate))
                {
                    setRateColor(e);
                }
            }
            catch
            {
            	//continue
            }
        }

        private static void setRestDaysColor(DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs e)
        {
            int restDays = (int)e.CellValue;
            if (restDays > 7)
            {
                e.Appearance.BackColor = Color.Lime;
            }
            else if (restDays > 5)
            {
                e.Appearance.BackColor = Color.Green;
            }
            else if (restDays > 3)
            {
                e.Appearance.BackColor = Color.Orange;
            }
            else if (restDays > 0)
            {
                e.Appearance.BackColor = Color.OrangeRed;
            }
            else
            {
                e.Appearance.BackColor = Color.Red;
            }
        }

        private static void setRateColor(DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs e)
        {
            double rate = (double)e.CellValue;
            if (rate >= 100)
            {
                e.Appearance.BackColor = Color.White;
                e.Appearance.BackColor2 = Color.White;
            }
            else
            {
                e.Appearance.BackColor = Color.White;
                e.Appearance.BackColor2 = Color.Red;
            }
        }

        private void miReplayFiles_Click(object sender, EventArgs e)
        {
            if (gridControl.FocusedView == null)
            {
                return;
            }
            List<FileInfo> fis = new List<FileInfo>();
            if (gridControl.FocusedView.Name==gridView.Name)
            {
                getFileList(fis, dealGVFileInfo);
            }
            else if (gridControl.FocusedView.Name==gridView1.Name)
            {
                getFileList(fis, dealGV1FileInfo);
            }
            else if (gridControl.FocusedView.Name==gridView2.Name)
            {
                getFileList(fis, dealGV2FileInfo);
            }
            if (fis.Count > 0)
            {
                DIYReplayFileQuery query = new DIYReplayFileQuery(MainModel);
                query.SetQueryCondition(makeConditionByFiles(fis));
                query.Query();
            }
        }

        delegate void Func(List<FileInfo> fis, int[] rows);

        private void getFileList(List<FileInfo> fis, Func func)
        {
            int[] rows = ((DevExpress.XtraGrid.Views.Grid.GridView)gridControl.FocusedView).GetSelectedRows();
            if (rows.Length > 0)
            {
                func(fis, rows);
            }
        }

        private void dealGVFileInfo(List<FileInfo> fis, int[] rows)
        {
            List<CQTTestTask> tasks = new List<CQTTestTask>();
            for (int i = 0; i < rows.Length; i++)
            {
                CQTTestTask task = ((DevExpress.XtraGrid.Views.Grid.GridView)gridControl.FocusedView).GetRow(rows[i]) as CQTTestTask;
                tasks.Add(task);
            }
            foreach (CQTTestTask item in tasks)
            {
                if (item.SubPointsTestInfo != null && item.SubPointsTestInfo.Count > 0)
                {
                    foreach (SubPointTestInfo subTestItem in item.SubPointsTestInfo)
                    {
                        fis.AddRange(subTestItem.FileInfoList);
                    }
                }
            }
        }

        private void dealGV1FileInfo(List<FileInfo> fis, int[] rows)
        {
            for (int i = 0; i < rows.Length; i++)
            {
                SubPointTestInfo sub = ((DevExpress.XtraGrid.Views.Grid.GridView)gridControl.FocusedView).GetRow(rows[i]) as SubPointTestInfo;
                fis.AddRange(sub.FileInfoList);
            }
        }

        private void dealGV2FileInfo(List<FileInfo> fis, int[] rows)
        {
            for (int i = 0; i < rows.Length; i++)
            {
                FileInfo fi = ((DevExpress.XtraGrid.Views.Grid.GridView)gridControl.FocusedView).GetRow(rows[i]) as FileInfo;
                fis.Add(fi);
            }
        }

        private QueryCondition makeConditionByFiles(List<FileInfo> fis)
        {
            QueryCondition condition = new QueryCondition();
            foreach (FileInfo fileInfo in fis)
            {
                condition.FileInfos.Add(fileInfo);
                if (fileInfo.DistrictID > 0)
                {
                    condition.DistrictID = fileInfo.DistrictID;
                }
                if (!condition.DistrictIDs.Contains(fileInfo.DistrictID))
                {
                    condition.DistrictIDs.Add(fileInfo.DistrictID);
                }
            }
            return condition;
        }

        private void btnDeleteTask_Click(object sender, EventArgs e)
        {
            int[] rows = gridView.GetSelectedRows();
            if (rows.Length > 0)
            {
                List<CQTTestTask> tasks = new List<CQTTestTask>();
                for (int i = 0; i < rows.Length; i++)
                {
                    CQTTestTask task = gridView.GetRow(rows[i]) as CQTTestTask;
                    if (task==null)
                    {
                        continue;
                    }
                    tasks.Add(task);
                }
                DIYSQLDeleteCQTTestTask query = new DIYSQLDeleteCQTTestTask(MainModel, tasks);
                query.Query();
                foreach (CQTTestTask item in tasks)
                {
                    curTestTask.Remove(item);
                }
                gridControl.RefreshDataSource();
                gridControl.Invalidate();
            }
            else
            {
                MessageBox.Show("请选择要删除的测试任务！");
            }
        }






    }

}
