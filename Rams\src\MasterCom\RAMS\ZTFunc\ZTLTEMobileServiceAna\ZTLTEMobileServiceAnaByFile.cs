﻿using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTLTEMobileServiceAnayFile : ZTLTEMobileServiceAnaBase
    {
        public ZTLTEMobileServiceAnayFile(MainModel mainModel)
            : base(mainModel)
        {
            mEvents = new MobileServiceEvent(false);
        }

        protected static readonly object lockObj = new object();
        private static ZTLTEMobileServiceAnayFile intance = null;
        public static ZTLTEMobileServiceAnayFile GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new ZTLTEMobileServiceAnayFile(MainModel.GetInstance());
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "移动互联业务时长分析(按文件)"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22057, this.Name);//////
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }

    public class ZTLTEMobileServiceAnayFile_FDD : ZTLTEMobileServiceAnaBase_FDD
    {
        private static ZTLTEMobileServiceAnayFile_FDD instance = null;
        protected static readonly object lockObj = new object();
        public static ZTLTEMobileServiceAnayFile_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTLTEMobileServiceAnayFile_FDD(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }
        public ZTLTEMobileServiceAnayFile_FDD(MainModel mainModel)
            : base(mainModel)
        {
            mEvents = new MobileServiceEvent(true);
        }
        public override string Name
        {
            get { return "移动互联业务时长分析LTE_FDD(按文件)"; }
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }
}