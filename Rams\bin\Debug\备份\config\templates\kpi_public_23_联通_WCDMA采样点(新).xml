<?xml version="1.0"?>
<Configs>
  <Config name="ReportSetting">
    <Item name="styles" typeName="IDictionary">
      <Item typeName="String" key="Name">WCDMA采样点(新)</Item>
      <Item typeName="IList" key="Cells">
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">7</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_710A3C+Wx_710E3C+Wx_710B3C+Wx_710F3C+Wx_711C3C }</Item>
          <Item typeName="Int32" key="RowAt">7</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总采样点数</Item>
          <Item typeName="Int32" key="RowAt">7</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TotalRSCP</Item>
          <Item typeName="Int32" key="RowAt">7</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总采样点数</Item>
          <Item typeName="Int32" key="RowAt">21</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TxPower&lt;=-15采样点数</Item>
          <Item typeName="Int32" key="RowAt">22</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TxPower[-15,0)采样点数</Item>
          <Item typeName="Int32" key="RowAt">23</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TxPower[0,10)采样点数</Item>
          <Item typeName="Int32" key="RowAt">24</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TxPower[10,20)采样点数</Item>
          <Item typeName="Int32" key="RowAt">25</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总采样点数</Item>
          <Item typeName="Int32" key="RowAt">27</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[0,1.0)采样点数</Item>
          <Item typeName="Int32" key="RowAt">28</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_6C0A08+Wx_6C0E08+Wx_6C0B08+Wx_6C0F08+Wx_6C1C08 }</Item>
          <Item typeName="Int32" key="RowAt">21</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_6C0A01+Wx_6C0E01+Wx_6C0B01+Wx_6C0F01+Wx_6C1C01 }</Item>
          <Item typeName="Int32" key="RowAt">22</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_6C0A03+Wx_6C0E03+Wx_6C0B03+Wx_6C0F03+Wx_6C1C03 }</Item>
          <Item typeName="Int32" key="RowAt">24</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_6C0A04+Wx_6C0E04+Wx_6C0B04+Wx_6C0F04+Wx_6C1C04 }</Item>
          <Item typeName="Int32" key="RowAt">25</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_720A09+Wx_720E09 +Wx_720B09+Wx_720F09+Wx_721C09}</Item>
          <Item typeName="Int32" key="RowAt">27</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_720A01+Wx_720E01+Wx_720B01+Wx_720F01+Wx_721C01 }</Item>
          <Item typeName="Int32" key="RowAt">28</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">28</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_6C0A02+Wx_6C0E02+Wx_6C0B02+Wx_6C0F02+Wx_6C1C02 }</Item>
          <Item typeName="Int32" key="RowAt">23</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">BLER</Item>
          <Item typeName="Int32" key="RowAt">27</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">25</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">24</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">23</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">22</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TxPower</Item>
          <Item typeName="Int32" key="RowAt">21</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TotalRSCP&gt;=-80采样点数</Item>
          <Item typeName="Int32" key="RowAt">8</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TotalRSCP[-85,-80)采样点数</Item>
          <Item typeName="Int32" key="RowAt">9</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_710A36+Wx_710E36+Wx_710B36+Wx_710F36+Wx_711C36}</Item>
          <Item typeName="Int32" key="RowAt">9</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">8</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">9</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_710A33+Wx_710A34+Wx_710A35+Wx_710E33+Wx_710E34+Wx_710E35+Wx_710B33+Wx_710B34+Wx_710B35+Wx_710F33+Wx_710F34+Wx_710F35+Wx_711C33+Wx_711C34+Wx_711C35 }</Item>
          <Item typeName="Int32" key="RowAt">8</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TxPower&gt;=20采样点数</Item>
          <Item typeName="Int32" key="RowAt">26</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">26</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_6C0A05+Wx_6C0E05+Wx_6C0B05+Wx_6C0F05+Wx_6C1C05 }</Item>
          <Item typeName="Int32" key="RowAt">26</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[1.0,2.0)采样点数</Item>
          <Item typeName="Int32" key="RowAt">29</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[2.0,3.0)采样点数</Item>
          <Item typeName="Int32" key="RowAt">30</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">[3.0,5.0)采样点数</Item>
          <Item typeName="Int32" key="RowAt">31</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">29</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">30</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">31</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_720A02+Wx_720E02+Wx_720B02+Wx_720F02+Wx_721C02 }</Item>
          <Item typeName="Int32" key="RowAt">29</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_720A03+Wx_720E03+Wx_720B03+Wx_720F03+Wx_721C03 }</Item>
          <Item typeName="Int32" key="RowAt">30</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_720A04+Wx_720A05+Wx_720E04+Wx_720E05+Wx_720B04+Wx_720B05+Wx_720F04+Wx_720F05+Wx_721C04+Wx_721C05}</Item>
          <Item typeName="Int32" key="RowAt">31</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">指标分类</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">指标名称</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">值</Item>
          <Item typeName="Int32" key="RowAt">0</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">192</Item>
          <Item typeName="Int32" key="BkColorG">192</Item>
          <Item typeName="Int32" key="BkColorB">192</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">42</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">42</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">42</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">汇总</Item>
          <Item typeName="Int32" key="RowAt">38</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总里程</Item>
          <Item typeName="Int32" key="RowAt">38</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Wx_0828+Wx_082C+Wx_0822+Wx_081F+Wx_0820+Wx_0824 )/1000}公里</Item>
          <Item typeName="Int32" key="RowAt">38</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">10</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TotalRSCP[-90,-85)采样点数</Item>
          <Item typeName="Int32" key="RowAt">10</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_710A37+Wx_710E37+Wx_710B37+Wx_710F37+Wx_711C37 }</Item>
          <Item typeName="Int32" key="RowAt">10</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">11</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TotalRSCP[-95,-90)采样点数</Item>
          <Item typeName="Int32" key="RowAt">11</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_710A38+Wx_710E38+Wx_710B38+Wx_710F38+Wx_711C38}</Item>
          <Item typeName="Int32" key="RowAt">11</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">38</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">ActiveSet</Item>
          <Item typeName="Int32" key="RowAt">15</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总采样点数</Item>
          <Item typeName="Int32" key="RowAt">15</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_5D0A0401+Wx_5D0A0402+Wx_5D0A0403+Wx_5D0A0404+Wx_5D0A0405+Wx_5D0A0406+Wx_5D0E0401+Wx_5D0E0402+Wx_5D0E0403+Wx_5D0E0404+Wx_5D0E0405+Wx_5D0E0406+Wx_5D0B0401+Wx_5D0B0402+Wx_5D0B0403+Wx_5D0B0404+Wx_5D0B0405+Wx_5D0B0406+Wx_5D0F0401+Wx_5D0F0402+Wx_5D0F0403+Wx_5D0F0404+Wx_5D0F0405+Wx_5D0F0406+Wx_5D1C0401+Wx_5D1C0402+Wx_5D1C0403+Wx_5D1C0404+Wx_5D1C0405+Wx_5D1C0406 }</Item>
          <Item typeName="Int32" key="RowAt">15</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">16</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">17</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">=1的采样点数</Item>
          <Item typeName="Int32" key="RowAt">16</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">=2的采样点数</Item>
          <Item typeName="Int32" key="RowAt">17</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">=3的采样点数</Item>
          <Item typeName="Int32" key="RowAt">18</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">=4的采样点数</Item>
          <Item typeName="Int32" key="RowAt">19</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">=5的采样点数</Item>
          <Item typeName="Int32" key="RowAt">20</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">166</Item>
          <Item typeName="Int32" key="BkColorB">166</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_5D0A0401+Wx_5D0E0401+Wx_5D0B0401+Wx_5D0F0401+Wx_5D1C0401 }</Item>
          <Item typeName="Int32" key="RowAt">16</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_5D0A0402+Wx_5D0E0402+Wx_5D0B0402+Wx_5D0F0402+Wx_5D1C0402}</Item>
          <Item typeName="Int32" key="RowAt">17</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_5D0A0403+Wx_5D0E0403+Wx_5D0B0403+Wx_5D0F0403+Wx_5D1C0403}</Item>
          <Item typeName="Int32" key="RowAt">18</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_5D0A0404}</Item>
          <Item typeName="Int32" key="RowAt">19</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_5D0A0405+Wx_5D0E0405+Wx_5D0B0405+Wx_5D0F0405+Wx_5D1C0405}</Item>
          <Item typeName="Int32" key="RowAt">20</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">18</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">19</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">20</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">128</Item>
          <Item typeName="Int32" key="BkColorB">128</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">10</Item>
          <Item typeName="Int32" key="ColAt">3</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TotalEcIo</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总采样点数</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">2</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TotalEcIo&gt;=-8采样点数</Item>
          <Item typeName="Int32" key="RowAt">2</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TotalEcIo[-10,-8)采样点数</Item>
          <Item typeName="Int32" key="RowAt">3</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">3</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">4</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TotalEcIo[-12,-10)采样点数</Item>
          <Item typeName="Int32" key="RowAt">4</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_6A0A09+Wx_6A0E09+Wx_6A0B09+Wx_6A0F09+Wx_6A1C09}</Item>
          <Item typeName="Int32" key="RowAt">1</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_6A0A06+Wx_6A0E06+Wx_740B06+Wx_6A0F06+Wx_6A1C06}</Item>
          <Item typeName="Int32" key="RowAt">2</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_6A0A05+Wx_6A0E05+Wx_6A0B05+Wx_6A0F05+Wx_6A1C05}</Item>
          <Item typeName="Int32" key="RowAt">3</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_6A0A04+Wx_6A0A03+Wx_6A0E04+Wx_6A0E03+Wx_6A0B04+Wx_6A0B03+Wx_6A0F04+Wx_6A0F03+Wx_6A1C04+Wx_6A1C03 }</Item>
          <Item typeName="Int32" key="RowAt">4</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TotalEcIo[-14,-12)采样点数</Item>
          <Item typeName="Int32" key="RowAt">5</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_6A0A02+Wx_6A0E02+Wx_6A0B02+Wx_6A0F02+Wx_6A1C02}</Item>
          <Item typeName="Int32" key="RowAt">5</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">5</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">51</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TotalEcIo&lt;-14 采样点数</Item>
          <Item typeName="Int32" key="RowAt">6</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">6</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">0</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_6A0A01+Wx_6A0E01+Wx_6A0B01+Wx_6A0F01+Wx_6A1C01 }</Item>
          <Item typeName="Int32" key="RowAt">6</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TotalRSCP[-100,-95)采样点数</Item>
          <Item typeName="Int32" key="RowAt">12</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TotalRSCP[-105,-100)采样点数</Item>
          <Item typeName="Int32" key="RowAt">13</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">TotalRSCP&lt;-105 采样点数</Item>
          <Item typeName="Int32" key="RowAt">14</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">12</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">13</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">14</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">0</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_710A39+Wx_710E39+Wx_710B39+Wx_710F39+Wx_711C39}</Item>
          <Item typeName="Int32" key="RowAt">12</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_710A3A+Wx_710E3A+Wx_710B3A+Wx_710F3A+Wx_711C3A}</Item>
          <Item typeName="Int32" key="RowAt">13</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_710A3B+Wx_710E3B+Wx_710B3B+Wx_710F3B+Wx_711C3B}</Item>
          <Item typeName="Int32" key="RowAt">14</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">总时长</Item>
          <Item typeName="Int32" key="RowAt">39</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">39</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{(Wx_0827+Wx_082B+Wx_081E+Wx_081B+Wx_081C+Wx_0823)/3600000 }小时</Item>
          <Item typeName="Int32" key="RowAt">39</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">40</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">158</Item>
          <Item typeName="Int32" key="BkColorG">252</Item>
          <Item typeName="Int32" key="BkColorB">135</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">平均时速(公里/小时)</Item>
          <Item typeName="Int32" key="RowAt">40</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">196</Item>
          <Item typeName="Int32" key="BkColorG">253</Item>
          <Item typeName="Int32" key="BkColorB">181</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{((Wx_0828+Wx_082C +Wx_0822+Wx_081F+Wx_0820+Wx_0824 )/1000)/((Wx_0827+Wx_082B+Wx_081E+Wx_081B+Wx_081C+Wx_0823)/3600000)}公里/小时
</Item>
          <Item typeName="Int32" key="RowAt">40</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxPower</Item>
          <Item typeName="Int32" key="RowAt">32</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxPower总采样点数</Item>
          <Item typeName="Int32" key="RowAt">32</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_6B0A08+Wx_6B0E08+Wx_6B0B08+Wx_6B0F08+Wx_6B1C08 }</Item>
          <Item typeName="Int32" key="RowAt">32</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">255</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">179</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">33</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxPower&gt;=-75采样点数</Item>
          <Item typeName="Int32" key="RowAt">33</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxPower[-75,-80)采样点数</Item>
          <Item typeName="Int32" key="RowAt">34</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_6B0A05+Wx_6B0E05+Wx_6B0B05+Wx_6B0F05+Wx_6B1C05 }</Item>
          <Item typeName="Int32" key="RowAt">33</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_6B0A04+Wx_6B0E04+Wx_6B0B04+Wx_6B0F04+Wx_6B1C04 }</Item>
          <Item typeName="Int32" key="RowAt">34</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxPower[-80,-85)采样点数</Item>
          <Item typeName="Int32" key="RowAt">35</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_6B0A03+Wx_6B0E03+Wx_6B0B03+Wx_6B0F03+Wx_6B1C03 }</Item>
          <Item typeName="Int32" key="RowAt">35</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxPower[-85,-90)采样点数</Item>
          <Item typeName="Int32" key="RowAt">36</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_6B0A02+Wx_6B0E02+Wx_6B0B02+Wx_6B0F02+Wx_6B1C02 }</Item>
          <Item typeName="Int32" key="RowAt">36</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">RxPower&lt;=-90采样点数</Item>
          <Item typeName="Int32" key="RowAt">37</Item>
          <Item typeName="Int32" key="ColAt">1</Item>
          <Item typeName="Int32" key="BkColorR">187</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Exp">{Wx_6B0A01+Wx_6B0E01+Wx_6B0B01+Wx_6B0F01+Wx_6B1C01 }</Item>
          <Item typeName="Int32" key="RowAt">37</Item>
          <Item typeName="Int32" key="ColAt">2</Item>
          <Item typeName="Int32" key="BkColorR">180</Item>
          <Item typeName="Int32" key="BkColorG">219</Item>
          <Item typeName="Int32" key="BkColorB">254</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">2</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">34</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">35</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">36</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
        <Item typeName="IDictionary">
          <Item key="Exp" />
          <Item typeName="Int32" key="RowAt">37</Item>
          <Item typeName="Int32" key="ColAt">0</Item>
          <Item typeName="Int32" key="BkColorR">128</Item>
          <Item typeName="Int32" key="BkColorG">255</Item>
          <Item typeName="Int32" key="BkColorB">255</Item>
          <Item typeName="Int32" key="ForeColorR">0</Item>
          <Item typeName="Int32" key="ForeColorG">0</Item>
          <Item typeName="Int32" key="ForeColorB">0</Item>
          <Item typeName="Int32" key="CarrierID">3</Item>
        </Item>
      </Item>
      <Item typeName="IList" key="Graphs" />
      <Item typeName="IList" key="ColWidth">
        <Item typeName="Int32">141</Item>
        <Item typeName="Int32">392</Item>
        <Item typeName="Int32">137</Item>
        <Item typeName="Int32">123</Item>
        <Item typeName="Int32">114</Item>
        <Item typeName="Int32">108</Item>
        <Item typeName="Int32">104</Item>
        <Item typeName="Int32">102</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
        <Item typeName="Int32">100</Item>
      </Item>
    </Item>
  </Config>
</Configs>