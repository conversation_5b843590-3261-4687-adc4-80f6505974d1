﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;

namespace MasterCom.Util.GDI.LineChart
{
    public enum SataType
    {
        /// <summary>
        /// 按数据范围和坐标轴总长进行计算
        /// </summary>
        ByTotal,
        /// <summary>
        /// 有可能按长度的节点显示,目前暂无此需求,暂不实现,但保留该接口
        /// </summary>
        BySingleGap
    }

    public class ChartLine<U, V>
    {
        /// <summary>
        /// 多条折线数据列表
        /// </summary>
        public List<LineDataInfo<U, V>> LineDataInfoList { get; set; }

        #region 坐标轴
        public int Height { get; private set; }
        public int Width { get; private set; }

        /// <summary>
        /// X坐标轴
        /// </summary>
        public CoordinateAxis<U> XLine { get; private set; }
        /// <summary>
        /// Y坐标轴
        /// </summary>
        public CoordinateAxis<V> YLine { get; private set; }

        public void SetLength(int height, int width)
        {
            XLine.Value.Length = width;
            Width = width;
            YLine.Value.Length = height;
            Height = height;
        }
        #endregion

        #region 描述信息
        //目前固定在顶部的中间位置绘制描述信息
        protected DescColumn paramDesc = new DescColumn(Brushes.Black, 2);
        public void SetDrawParaDesc(bool isDrawParamDesc, string paramDesc
            , int margin_Top_ParamDesc = 20)
        {
            //由于该描述影响整体图片高度,所以不能外部设置,只能通过封装的函数修改
            if (this.paramDesc.IsDrawName == isDrawParamDesc)
            {
                return;
            }

            if (isDrawParamDesc)
            {
                Margin_top += margin_Top_ParamDesc;
            }
            else
            {
                Margin_top -= margin_Top_ParamDesc;
            }
            this.paramDesc.IsDrawName = isDrawParamDesc;
            this.paramDesc.Name = paramDesc;
        }

        public void SetDrawParaDesc(Font font, Brush brush, float width)
        {
            paramDesc.Font = font;
            paramDesc.Brush = brush;
            paramDesc.Pen = new Pen(brush, width);
        }
        #endregion

        #region 边缘位置留白
        public int Margin_top { get; private set; } = 60;
        public int Margin_right { get; private set; } = 60;
        public int Margin_bottom { get; private set; } = 60;
        public int Margin_left { get; private set; } = 60;

        PointF leftTop;
        PointF leftBottom;
        //PointF rightTop;
        PointF rightBottom;

        public void SetMargin(int margin_top, int margin_right, int margin_bottom, int margin_left)
        {
            Margin_top = margin_top;
            Margin_right = margin_right;
            Margin_bottom = margin_bottom;
            Margin_left = margin_left;
            setPointInfo();
        }

        private void setPointInfo()
        {
            leftTop = new PointF(Margin_left, Margin_top);
            leftBottom = new PointF(Margin_left, Height + Margin_top);
            //rightTop = new PointF(Width + Margin_left, Margin_top);
            rightBottom = new PointF(Width + Margin_left, Height + Margin_top);
        }
        #endregion

        #region 辅助线条 - 虚线
        public Pen SubLinePen { get; set; } = new Pen(Color.Gray, 1);
        public bool IsDrawSubLine { get; set; } = true;
        #endregion

        public ChartLine(CoordinateAxis<U> xLine, CoordinateAxis<V> yLine, List<LineDataInfo<U, V>> lineDataInfoList)
        {
            XLine = xLine;
            Width = XLine.Value.Length;
            YLine = yLine;
            Height = YLine.Value.Length;
            LineDataInfoList = lineDataInfoList;

            //设置虚线
            SubLinePen.DashStyle = System.Drawing.Drawing2D.DashStyle.Dash;
        }

        public void DrawLineChart(string path)
        {
            bool isValid = judgeValid();
            if (!isValid)
            {
                return;
            }
            setPointInfo();

            //生成图像对象
            Bitmap image = new Bitmap(Width + Margin_left + Margin_right
                , Height + Margin_top + Margin_bottom);
            //创建画布
            Graphics g = Graphics.FromImage(image);
            g.Clear(Color.White);

            drawParamDesc(g);

            drawCoordinateAxis(g);

            drawDatas(g);

            image.Save(path, System.Drawing.Imaging.ImageFormat.Jpeg);
        }

        private bool judgeValid()
        {
            if (XLine == null || YLine == null
                || !XLine.JudgeValid() || !YLine.JudgeValid())
            {
                //实际字段数要大于坐标轴最大允许字段数
                return false;
            }

            return true;
        }

        private void drawParamDesc(Graphics g)
        {
            if (!paramDesc.IsDrawName)
            {
                return;
            }

            int totalWidth = Width + Margin_left + Margin_right;
            int middleWidth = (int)Math.Floor(totalWidth / 2d);

            var size = g.MeasureString(paramDesc.Name, paramDesc.Font);
            //在顶部的中间位置绘制描述信息
            g.DrawString(paramDesc.Name, paramDesc.Font, paramDesc.Brush
                , new PointF(middleWidth - size.Width / 2, Margin_top - size.Height - 20));
        }

        private void drawCoordinateAxis(Graphics g)
        {
            //绘制x轴 (左下->右下)
            g.DrawLine(XLine.AxisColumn.Pen, leftBottom.X, leftBottom.Y, rightBottom.X, rightBottom.Y);
            //绘制x轴单位
            if (XLine.UintColumn.IsDrawName)
            {
                var size = g.MeasureString(XLine.UintColumn.Name, XLine.UintColumn.Font);
                //x轴右上方显示(目前没有长度判断,如果过长可能超出画布)
                g.DrawString(XLine.UintColumn.Name, XLine.UintColumn.Font
                    , XLine.UintColumn.Brush, new PointF(rightBottom.X + 3, rightBottom.Y - size.Height));
            }

            //绘制y轴 (左上->左下)
            g.DrawLine(YLine.AxisColumn.Pen, leftTop.X, leftTop.Y, leftBottom.X, leftBottom.Y);
            //绘制y轴单位
            if (YLine.UintColumn.IsDrawName)
            {
                var size = g.MeasureString(YLine.UintColumn.Name, YLine.UintColumn.Font);
                //y轴右上方显示
                g.DrawString(YLine.UintColumn.Name, YLine.UintColumn.Font, YLine.UintColumn.Brush
                    , new PointF(leftTop.X, leftTop.Y - size.Height - 3));
            }

            //绘制x轴坐标节点
            for (int i = 0; i < XLine.Value.FieldList.Count; i++)
            {
                if (i == 0 && !XLine.IsDrawOigin)
                {
                    continue;
                }

                var field = XLine.Value.FieldList[i];
                var fieldSize = g.MeasureString(field, XLine.AxisColumn.Font);
                // /2表示显示在文字中心
                float x = leftBottom.X + XLine.Value.Gap * i - fieldSize.Width / 2;
                //+5表示和x轴保持一点间隔
                float y = leftBottom.Y + 5;
                //坐标节点
                g.DrawString(field, XLine.AxisColumn.Font, XLine.AxisColumn.Brush, new PointF(x, y));
            }

            //绘制y轴坐标节点
            for (int i = 0; i < YLine.Value.FieldList.Count; i++)
            {
                if (i == 0 && !YLine.IsDrawOigin)
                {
                    continue;
                }

                var field = YLine.Value.FieldList[i];
                var fieldSize = g.MeasureString(field, YLine.AxisColumn.Font);
                float x = leftBottom.X - fieldSize.Width;
                float y = leftBottom.Y - YLine.Value.Gap * i;
                //坐标节点 /2表示显示在文字中心
                g.DrawString(field, YLine.AxisColumn.Font, YLine.AxisColumn.Brush, new PointF(x, y - fieldSize.Height / 2));
                //辅助虚线
                if (i > 0 && IsDrawSubLine)
                {
                    g.DrawLine(SubLinePen, leftBottom.X, y, rightBottom.X, y);
                }
            }
        }

        #region 绘制数据
        private void drawDatas(Graphics g)
        {
            foreach (var lineDataInfo in LineDataInfoList)
            {
                var pen = new Pen(lineDataInfo.LineColor, lineDataInfo.LineWidth);

                //根据数据计算点坐标
                List<PointF> points = new List<PointF>();
                foreach (var value in lineDataInfo.ValueDic)
                {
                    dealValue(g, pen, points, value);
                }

                drawLine(g, pen, points);
            }
        }

        private void dealValue(Graphics g, Pen pen, List<PointF> points, KeyValuePair<U, V> value)
        {
            if (value.Value != null)
            {
                var x = XLine.Value.GetCoordinate(value.Key) + leftTop.X;
                var y = YLine.Value.GetCoordinate(value.Value) + leftTop.Y;

                if (x != null && y != null)
                {
                    PointF point = new PointF((int)x, (int)y);
                    points.Add(point);
                }
                else
                {
                    drawLine(g, pen, points);
                }
            }
            else
            {
                drawLine(g, pen, points);
            }
        }

        private void drawLine(Graphics g, Pen pen, List<PointF> points)
        {
            if (points.Count > 0)
            {
                g.DrawLines(pen, points.ToArray());
                points.Clear();
            }
        }
        #endregion
    }

    /// <summary>
    /// 坐标轴
    /// </summary>
    public class CoordinateAxis<T>
    {
        /// <summary>
        /// 单位字段
        /// </summary>
        public DescColumn UintColumn { get; set; } = new DescColumn();
        /// <summary>
        /// 坐标轴字段
        /// </summary>
        public DescColumn AxisColumn { get; set; } = new DescColumn(Brushes.Black, 2);

        /// <summary>
        /// 是否绘制原点信息,x轴和y轴节点尽量只画一个,不然可能会重叠
        /// </summary>
        public bool IsDrawOigin { get; set; }

        /// <summary>
        /// 坐标轴数据
        /// </summary>
        public ValueBase<T> Value { get; private set; }

        public CoordinateAxis(ValueBase<T> value, string unitName)
        {
            Value = value;

            if (string.IsNullOrEmpty(unitName))
            {
                UintColumn.IsDrawName = false;
            }
            else
            {
                UintColumn.IsDrawName = true;
            }
            UintColumn.Name = unitName;
        }

        public bool JudgeValid()
        {
            if (Value.FieldList.Count == 0 || Value.FieldList.Count > Value.MaxFieldCount)
            {
                //实际字段数要大于坐标轴最大允许字段数
                return false;
            }

            return true;
        }
    }

    /// <summary>
    /// 描述字段
    /// </summary>
    public class DescColumn
    {
        public DescColumn()
        {

        }

        public DescColumn(Brush brush, float width)
        {
            Brush = brush;
            Pen = new Pen(brush, width);
        }

        public bool IsDrawName { get; set; }
        public string Name { get; set; }
        public Font Font { get; set; } = new Font("宋体", 12);
        public Brush Brush { get; set; } = Brushes.Black;
        public Pen Pen { get; set; } = new Pen(Brushes.Black, 1);
    }

    /// <summary>
    /// 单条折线的数据信息
    /// </summary>
    public class LineDataInfo<U, V>
    {
        public Color LineColor { get; set; } = Color.Blue;
        public int LineWidth { get; set; } = 2;

        //数据源 <横坐标值,纵坐标值>集合
        public Dictionary<U, V> ValueDic { get; set; } = new Dictionary<U, V>();
    }

    #region 数据
    public abstract class ValueBase<T>
    {
        public SataType CurSataType { get; protected set; }
        /// <summary>
        /// 数据范围
        /// </summary>
        protected double dataRange;
        /// <summary>
        /// 坐标轴长度
        /// </summary>
        protected double coordinateAxisLength;

        /// <summary>
        /// 实际字段列表 
        /// 超出最大字段数则后面的不显示 
        /// 小与最大字段数则后面留白 
        /// </summary>
        public List<string> FieldList { get; protected set; }
        /// <summary>
        /// 坐标轴最大允许的字段数
        /// </summary>
        public int MaxFieldCount { get; protected set; }
        /// <summary>
        /// 坐标轴长度
        /// </summary>
        public int Length { get; set; }
        /// <summary>
        /// 每个节点的间隔
        /// </summary>
        public int Gap { get; private set; }
        /// <summary>
        /// 间隔数(最大允许的字段数-1)
        /// </summary>
        public int GapCount { get; private set; }

        protected ValueBase(SataType curSataType, int length, int maxFieldCount)
        {
            CurSataType = curSataType;
            Length = length;
            MaxFieldCount = maxFieldCount;

            GapCount = MaxFieldCount - 1;
            Gap = (int)Math.Floor(Length * 1d / GapCount);
        }

        public void Init()
        {
            if (CurSataType == SataType.ByTotal)
            {
                dataRange = GetTotalRange();
                coordinateAxisLength = GetTotalLength();
            }
            else
            {
                dataRange = GetSingleGapRange();
                coordinateAxisLength = GetSingleGapLength();
            }
        }

        public virtual double GetTotalRange()
        {
            throw (new Exception("未实现"));
        }

        public virtual double GetSingleGapRange()
        {
            throw (new Exception("未实现"));
        }

        public virtual double GetTotalLength()
        {
            throw (new Exception("未实现"));
        }

        public virtual double GetSingleGapLength()
        {
            throw (new Exception("未实现"));
        }

        /// <summary>
        /// 根据数据获取坐标
        /// </summary>
        /// <param name="curData"></param>
        /// <returns></returns>
        public virtual int? GetCoordinate(T curData)
        {
            throw (new Exception("未实现"));
        }
    }

    public class DateTimeValue : ValueBase<DateTime>
    {
        public DateTime Data { get; set; }
        public DateTimeType DataType { get; set; }

        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }

        public DateTimeValue(SataType curSataType, DateTimeType dataType, int length
            , int maxFieldCount, Dictionary<DateTime, string> fieldDic)
            : base(curSataType, length, maxFieldCount)
        {
            DataType = dataType;
            var timeList = new List<DateTime>(fieldDic.Keys);
            timeList.Sort();

            FieldList = new List<string>();
            foreach (var time in timeList)
            {
                FieldList.Add(fieldDic[time]);
            }

            StartTime = timeList[0];
            EndTime = timeList[timeList.Count - 1];
        }

        public override double GetTotalRange()
        {
            var ts = EndTime - StartTime;
            double data = getTimeSpan(ts);
            return data;
        }

        //public override double GetSingleGapRange()
        //{
        //    var ts = EndTime - StartTime;
        //    double data = getTimeSpan(ts);
        //    data = Math.Floor(data / MaxFieldCount);
        //    return data;
        //}

        public override double GetTotalLength()
        {
            if (FieldList.Count < MaxFieldCount)
            {
                return Math.Floor((FieldList.Count - 1d) / GapCount * Length);
            }
            else
            {
                return Length;
            }
        }

        //public override double GetSingleGapLength()
        //{
        //    return Math.Floor(Length * 1d / MaxFieldCount);
        //}

        public enum DateTimeType
        {
            Second,
            Minute,
            Hour,
            Day,
            Month,
            Year
        }

        public override int? GetCoordinate(DateTime curData)
        {
            if (curData > EndTime || curData < StartTime)
            {
                return null;
            }
            var ts = curData - StartTime;

            double data = getTimeSpan(ts);

            int coordinate = (int)Math.Floor(data / dataRange * coordinateAxisLength);
            return coordinate;
        }

        private double getTimeSpan(TimeSpan ts)
        {
            double data = 0;
            switch (DataType)
            {
                case DateTimeType.Second:
                    data = ts.TotalSeconds;
                    break;
                case DateTimeType.Minute:
                    data = ts.TotalMinutes;
                    break;
                case DateTimeType.Hour:
                    data = ts.TotalHours;
                    break;
                case DateTimeType.Day:
                    data = ts.TotalDays;
                    break;
                case DateTimeType.Month:
                    int year = EndTime.Year - StartTime.Year;
                    data = 12 * year + EndTime.Month - StartTime.Month;
                    break;
                case DateTimeType.Year:
                    data = EndTime.Year - StartTime.Year;
                    break;
            }

            return data;
        }

        /// <summary>
        /// 根据起始时间获取对应坐标轴节点集合
        /// </summary>
        /// <param name="dt">起始时间</param>
        /// <param name="count">结点数</param>
        /// <param name="type">时间类型</param>
        /// <param name="gap">时间间隔</param>
        /// <param name="format">时间格式</param>
        /// <returns></returns>
        public static Dictionary<DateTime, string> GetDateTimeFields(DateTime dt, int count
            , DateTimeType type, int gap, string format = "")
        {
            Dictionary<DateTime, string> xDic = new Dictionary<DateTime, string>();

            for (int i = 0; i < count; i++)
            {
                int curGap = i * gap;
                DateTime curDt = getTime(type, curGap, dt, ref format);

                string curDtDesc = curDt.ToString(format);
                xDic[curDt] = curDtDesc;
            }

            return xDic;
        }

        private static DateTime getTime(DateTimeType type, int gap, DateTime dt, ref string format)
        {
            DateTime curDt = dt;
            switch (type)
            {
                case DateTimeType.Second:
                    curDt = dt.AddSeconds(gap);
                    if (string.IsNullOrEmpty(format))
                    {
                        format = "HH:mm:ss";
                    }
                    break;
                case DateTimeType.Minute:
                    curDt = dt.AddMinutes(gap);
                    if (string.IsNullOrEmpty(format))
                    {
                        format = "HH:mm:ss";
                    }
                    break;
                case DateTimeType.Hour:
                    curDt = dt.AddHours(gap);
                    if (string.IsNullOrEmpty(format))
                    {
                        format = "HH:mm:ss";
                    }
                    break;
                case DateTimeType.Day:
                    curDt = dt.AddDays(gap);
                    if (string.IsNullOrEmpty(format))
                    {
                        format = "yyyy-MM-dd";
                    }
                    break;
                case DateTimeType.Month:
                    curDt = dt.AddMonths(gap);
                    if (string.IsNullOrEmpty(format))
                    {
                        format = "yyyy-MM";
                    }
                    break;
                case DateTimeType.Year:
                    curDt = dt.AddYears(gap);
                    if (string.IsNullOrEmpty(format))
                    {
                        format = "yyyy";
                    }
                    break;
            }
            return curDt;
        }
    }

    public class DoubleValue : ValueBase<double?>
    {
        public double Data { get; set; }
        public double MinData { get; private set; }
        public double MaxData { get; private set; }

        public DoubleValue(SataType curSataType, int length, int maxFieldCount
            , Dictionary<double, string> fieldDic)
            : base(curSataType, length, maxFieldCount)
        {
            var dataList = new List<double>(fieldDic.Keys);
            dataList.Sort();

            FieldList = new List<string>();
            foreach (var data in dataList)
            {
                FieldList.Add(fieldDic[data]);
            }

            MinData = dataList[0];
            MaxData = dataList[dataList.Count - 1];
        }

        public override double GetTotalRange()
        {
            return Math.Floor(MaxData - MinData);
        }

        //public override double GetSingleGapRange()
        //{
        //    return Math.Floor((MaxData - MinData) / MaxFieldCount);
        //}

        public override double GetTotalLength()
        {
            if (FieldList.Count < MaxFieldCount)
            {
                return Math.Floor((FieldList.Count - 1d) / GapCount * Length);
            }
            else
            {
                return Length;
            }
        }

        //public override double GetSingleGapLength()
        //{
        //    return Math.Floor(Length * 1d / MaxFieldCount);
        //}

        public override int? GetCoordinate(double? curData)
        {
            if (curData == null || curData > MaxData || curData < MinData)
            {
                return null;
            }

            double data = (double)curData - MinData;
            data = dataRange - data;

            int coordinate = (int)Math.Floor(data / dataRange * coordinateAxisLength);
            return coordinate;
        }
    }
    #endregion
}
