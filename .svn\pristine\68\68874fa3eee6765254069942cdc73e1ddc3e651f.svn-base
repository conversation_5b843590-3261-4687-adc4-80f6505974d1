﻿using System;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;
using System.Collections.Generic;
using MasterCom.RAMS.NewBlackBlock;
using MasterCom.Util.UiEx;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Grid;

namespace MasterCom.RAMS.Func
{
    public partial class CellFusionDataForm : ChildForm
    {
        static readonly string fixedColumn_TAC = "TAC";//每种关联数据必有的固定列名
        static readonly string fixedColumn_ECI = "ECI";
        static readonly string fixedColumn_BeginTime = "日期";

        List<CellAlarmData> alarmDataSet = new List<CellAlarmData>();
        List<CellPerfData> perfDataSet = new List<CellPerfData>();
        List<CellArgData> argDataSet = new List<CellArgData>();

        Dictionary<string, int> alarmRowIndexDic = new Dictionary<string, int>();
        Dictionary<string, int> perfRowIndexDic = new Dictionary<string, int>();
        Dictionary<string, int> argRowIndexDic = new Dictionary<string, int>();
        public CellFusionDataForm()
            : base()
        {
            InitializeComponent();
            refreshView();
        }
       
        public override void Init()
        {
            MainModel.DistrictChanged += districtChanged;
            MainModel.DTDataChanged += dtDataChanged;
            MainModel.SelectedTestPointsChanged += selectedTestPointsChanged;
            MainModel.SelectedEventsChanged += selectedEventsChanged;
            Disposed += disposed;
            dtDataChanged(null, null);
        }
        private void disposed(object sender, EventArgs e)
        {
            MainModel.DistrictChanged -= districtChanged;
            MainModel.DTDataChanged -= dtDataChanged;
            MainModel.SelectedTestPointsChanged -= selectedTestPointsChanged;
            MainModel.SelectedEventsChanged -= selectedEventsChanged;
            clearData();
        }

        private void clearData()
        {
            alarmDataSet.Clear();
            perfDataSet.Clear();
            argDataSet.Clear();

            alarmRowIndexDic.Clear();
            perfRowIndexDic.Clear();
            argRowIndexDic.Clear();
        }
        private void districtChanged(object sender, EventArgs e)
        {
            dtDataChanged(null, null);
        }

        #region 查询关联数据

        bool isShow = false;
        private void dtDataChanged(object sender, EventArgs e)
        {
            if (!MainModel.IsFusionInclude)
            {
                return;
            }
            if (!isShow && MainModel.DTDataManager.FileDataManagers.Count > 0)
            {
                isShow = true;
                WaitTextBox.Show("开始关联分析小区...", doDtDataChangedThread);
                isShow = false;
            }
        }
        private void doDtDataChangedThread()
        {
            try
            {
                clearData();
                Dictionary<string, CommonCellItem> allCelldic = new Dictionary<string, CommonCellItem>();
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    foreach (TestPoint testPoint in fileDataManager.TestPoints)
                    {
                        LTECell cell = testPoint.GetMainCell_LTE();
                        if (cell == null)
                        {
                            continue;
                        }

                        CommonCellItem item = new CommonCellItem(testPoint);

                        CommonCellItem itemMerge;
                        if (!allCelldic.TryGetValue(item.Token, out itemMerge))
                        {
                            itemMerge = item;
                            allCelldic[item.Token] = itemMerge;
                        }
                        itemMerge.AddCell(cell);
                    }
                }

                if (allCelldic.Count > 0)
                {
                    string taskDBServerName = CellAlarmData.FusionDB;

                    setFusionDBName();

                    foreach (CommonCellItem item in allCelldic.Values)
                    {
                        queryFusionData(item);
                    }

                    CellAlarmData.FusionDB = taskDBServerName;
                }
                getRowIndex();
                refreshView();
            }
            catch
            {
                //continue
            }
            finally
            {
                System.Threading.Thread.Sleep(500);
                WaitTextBox.Close();
            }
        }

        private void setFusionDBName()
        {
            NopServer nopServer = MasterCom.RAMS.NOP.NopCfgMngr.Instance.NopServer;
            if (nopServer != null)
            {
                if (string.IsNullOrEmpty(nopServer.Ip))
                {
                    CellAlarmData.FusionDB = MasterCom.RAMS.NOP.NopCfgMngr.Instance.TaskDBServerName;
                }
                else
                {
                    string strIp;
                    if (nopServer.Port <= 0)
                    {
                        strIp = nopServer.Ip;
                    }
                    else
                    {
                        strIp = string.Format("{0},{1}", nopServer.Ip, nopServer.Port);
                    }
                    CellAlarmData.FusionDB = string.Format("[{0}].[{1}]", strIp
                        , MasterCom.RAMS.NOP.NopCfgMngr.Instance.TaskDBServerName);
                }
            }
        }

        private void queryFusionData(CommonCellItem item)
        {
            StringBuilder cellCond = new StringBuilder();
            foreach (LTECell cell in item.CellDic.Values)
            {
                if (cellCond.Length > 5000)
                {
                    queryFusionBatch(item, cellCond.ToString());//以防同一地市和日期的小区个数太多，分多次查询
                    cellCond = new StringBuilder();
                }

                if (cellCond.Length > 0)
                {
                    cellCond.Append(" or ");
                }
                cellCond.Append(string.Format(" (lac={0} and ci={1}) ", cell.TAC, cell.ECI));
            }

            if (cellCond.Length > 0)
            {
                queryFusionBatch(item, cellCond.ToString());
            }
        }
        private void queryFusionBatch(CommonCellItem item, string cellCond)
        {
            WaitTextBox.Text = "正在关联告警数据...";
            QueryFusionAlarmData qryAlarm = new QueryFusionAlarmData();
            qryAlarm.CellDic = item.CellDic;
            qryAlarm.CellCondition = cellCond;
            qryAlarm.Time = item.Time;
            qryAlarm.DistrictID = item.DistrictID;
            qryAlarm.Query();
            alarmDataSet.AddRange(qryAlarm.CellAlarmSet);

            WaitTextBox.Text = "正在关联性能数据...";
            QueryFusionPerfData qryPerf = new QueryFusionPerfData();
            qryPerf.CellDic = item.CellDic;
            qryPerf.CellCondition = cellCond;
            qryPerf.Time = item.Time;
            qryPerf.DistrictID = item.DistrictID;
            qryPerf.Query();
            perfDataSet.AddRange(qryPerf.CellPerfSet);

            WaitTextBox.Text = "正在关联参数数据...";
            QueryFusionArgData qryArg = new QueryFusionArgData();
            qryArg.CellDic = item.CellDic;
            qryArg.CellCondition = cellCond;
            qryArg.Time = item.Time;
            qryArg.DistrictID = item.DistrictID;
            qryArg.Query();
            argDataSet.AddRange(qryArg.CellArgSet);
        }
        #endregion

        private void getRowIndex()
        {
            for (int i = 0; i < alarmDataSet.Count; i++)
            {
                CellAlarmData item = alarmDataSet[i];
                alarmRowIndexDic[item.Token] = i;
                alarmRowIndexDic[getCellDateKeyStr(item.Cell, item.BeginTime)] = i;
            }
            for (int i = 0; i < perfDataSet.Count; i++)
            {
                CellPerfData item = perfDataSet[i];
                perfRowIndexDic[item.Token] = i;
                perfRowIndexDic[getCellDateKeyStr(item.Cell, item.BeginTime)] = i;
            }
            for (int i = 0; i < argDataSet.Count; i++)
            {
                CellArgData item = argDataSet[i];
                argRowIndexDic[item.Token] = i;
                argRowIndexDic[getCellDateKeyStr(item.Cell, item.BeginTime)] = i;
            }
        }
        private string getCellDateKeyStr(LTECell lteCell, DateTime time)
        {
            if (lteCell == null)
            {
                return "";
            }

            return string.Format("{0}_{1}", lteCell.Token, time.Date.ToString("yyMMdd"));
        }

        #region 关联数据联动显示
        private void selectedTestPointsChanged(object sender, EventArgs e)
        {
            if (sender != this && MainModel.SelectedTestPoints.Count > 0)
            {
                TestPoint testPoint = MainModel.SelectedTestPoints[0];
                selectedRelativeRow(testPoint.GetMainLTECell_TdOrFdd(), testPoint.DateTime);
            }
        }
        private void selectedEventsChanged(object sender, EventArgs e)
        {
            if (sender != this && MainModel.SelectedEvents.Count > 0)
            {
                Event evt = MainModel.SelectedEvents[0];
                selectedRelativeRow(evt.GetSrcCell(), evt.DateTime);
            }
        }
        private void selectedRelativeRow(ICell iCell, DateTime time)
        {
            changeSelectedRow(iCell, time, alarmRowIndexDic, gvAlarm);
            changeSelectedRow(iCell, time, perfRowIndexDic, gvPerf);
            changeSelectedRow(iCell, time, argRowIndexDic, gvArg);
        }
        private void changeSelectedRow(ICell iCell, DateTime time, Dictionary<string, int> rowIndexDic
            , DevExpress.XtraGrid.Views.Grid.GridView gridView)
        {
            LTECell lteCell = iCell as LTECell;
            if (lteCell == null)
            {
                gridView.OptionsSelection.EnableAppearanceFocusedRow = false;
            }
            else
            {
                string strCell_DateKey = getCellDateKeyStr(lteCell, time);

                int selectDataIndex;
                if (rowIndexDic.TryGetValue(strCell_DateKey, out selectDataIndex)//先根据小区Token和日期关键字匹配
                    || rowIndexDic.TryGetValue(lteCell.Token, out selectDataIndex))//以上关键字匹配不到，再根据小区Token匹配
                {
                    gridView.OptionsSelection.EnableAppearanceFocusedRow = true;

                    int rowHandle = gridView.GetRowHandle(selectDataIndex);
                    gridView.SelectRow(rowHandle);
                    gridView.FocusedRowHandle = rowHandle;
                }
                else
                {
                    gridView.OptionsSelection.EnableAppearanceFocusedRow = false;
                }
            }
        }

        private void gvFusion_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            DateTime time;
            LTECell cell = getCellBySelectedRow(sender, out time);
            selectedRelativeRow(cell, time);
        }
        private void gvFusion_DoubleClick(object sender, EventArgs e)
        {
            goToMapView(sender);
        }
        private void goToMapView(object sender)
        {
            DateTime time;
            LTECell lteCell = getCellBySelectedRow(sender, out time);
            if (lteCell != null)
            {
                MainModel.MainForm.GetMapForm().GoToView(lteCell.Longitude, lteCell.Latitude, 5000);
            }
        }
        private LTECell getCellBySelectedRow(object sender ,out DateTime time)
        {
            time = new DateTime();
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            if (gv == null)
            {
                return null;
            }
            DataRowView rowView = gv.GetRow(gv.GetSelectedRows()[0]) as DataRowView;
            if (rowView == null || rowView.Row == null)
            {
                return null;
            }

            LTECell selectLteCell = null;
            object tacObj = rowView.Row[fixedColumn_TAC];
            object eciObj = rowView.Row[fixedColumn_ECI];
            if (tacObj != null && eciObj != null)
            {
                int tac = (int)tacObj;
                int eci = (int)eciObj;
                selectLteCell = CellManager.GetInstance().GetCurrentLTECell(tac, eci);
            }

            object timeObj = rowView.Row[fixedColumn_BeginTime];
            if (timeObj != null)
            {
                DateTime.TryParse(timeObj.ToString(), out time);
            }

            return selectLteCell;
        }
        #endregion

        protected void refreshView()
        {
            refreshAlarmView();
            refreshPerfView();
            refreshArgView();
        }
        private void refreshAlarmView()
        {
            ColumnView view = gridCtrlAlarm.MainView as ColumnView;
            view.Columns.Clear();

            DataTable dataTable = new DataTable();
            dataTable.Columns.Add("小区", typeof(string));
            dataTable.Columns.Add(fixedColumn_TAC, typeof(int));
            dataTable.Columns.Add(fixedColumn_ECI, typeof(int));
            dataTable.Columns.Add("基站号", typeof(int));
            dataTable.Columns.Add("基站名", typeof(string));
            dataTable.Columns.Add("告警描述", typeof(string));
            dataTable.Columns.Add(fixedColumn_BeginTime, typeof(string));

            FusionColumnCfgManager cfg = FusionColumnCfgManager.Instance;

            FusionDataColumnCollection alarmCollection;
            if (cfg.ColumnCollections.TryGetValue(FusionDataType.Alarm, out alarmCollection))
            {
                List<FusionDataColumn> fusionColumns_Visible = new List<FusionDataColumn>();
                foreach (FusionDataColumn funsionColumn in alarmCollection.FusionDataColumns)
                {
                    if (funsionColumn.IsCheck)
                    {
                        fusionColumns_Visible.Add(funsionColumn);
                        dataTable.Columns.Add(funsionColumn.ColumnName);
                    }
                }

                addCellAlarmDataRow(dataTable, fusionColumns_Visible);
            }

            gridCtrlAlarm.DataSource = dataTable;
            gridCtrlAlarm.RefreshDataSource();
        }

        private void addCellAlarmDataRow(DataTable dataTable, List<FusionDataColumn> fusionColumns_Visible)
        {
            foreach (CellAlarmData cellData in alarmDataSet)
            {
                DataRow dataRow = dataTable.NewRow();
                dataRow["小区"] = cellData.CellName;
                dataRow[fixedColumn_TAC] = cellData.TAC;
                dataRow[fixedColumn_ECI] = cellData.ECI;
                dataRow["基站号"] = cellData.BTSID;
                dataRow["基站名"] = cellData.BTSName;
                dataRow["告警描述"] = cellData.TypeName;
                dataRow[fixedColumn_BeginTime] = cellData.BeginTime.ToString();

                foreach (FusionDataColumn funsionColumn in fusionColumns_Visible)
                {
                    string strValue = "-";
                    object ret;
                    if (cellData.DataDic != null && cellData.DataDic.TryGetValue((uint)funsionColumn.ColumnId, out ret))
                    {
                        strValue = ret.ToString();
                    }
                    dataRow[funsionColumn.ColumnName] = strValue;
                }
                dataTable.Rows.Add(dataRow);
            }
        }

        private void refreshPerfView()
        {
            ColumnView view = gridCtrlPerf.MainView as ColumnView;
            view.Columns.Clear();

            DataTable dataTable = new DataTable();
            dataTable.Columns.Add("小区", typeof(string));
            dataTable.Columns.Add(fixedColumn_TAC, typeof(int));
            dataTable.Columns.Add(fixedColumn_ECI, typeof(int));
            dataTable.Columns.Add(fixedColumn_BeginTime, typeof(string));

            FusionColumnCfgManager cfg = FusionColumnCfgManager.Instance;

            FusionDataColumnCollection perfCollection;
            if (cfg.ColumnCollections.TryGetValue(FusionDataType.Perf, out perfCollection))
            {
                List<FusionDataColumn> fusionColumns_Visible = new List<FusionDataColumn>();
                foreach (FusionDataColumn funsionColumn in perfCollection.FusionDataColumns)
                {
                    if (funsionColumn.IsCheck)
                    {
                        fusionColumns_Visible.Add(funsionColumn);
                        dataTable.Columns.Add(funsionColumn.ColumnName);
                    }
                }

                addCellPerfDataRow(dataTable, fusionColumns_Visible);
            }

            gridCtrlPerf.DataSource = dataTable;
            gridCtrlPerf.RefreshDataSource();
        }

        private void addCellPerfDataRow(DataTable dataTable, List<FusionDataColumn> fusionColumns_Visible)
        {
            foreach (CellPerfData cellData in perfDataSet)
            {
                DataRow dataRow = dataTable.NewRow();
                dataRow["小区"] = cellData.CellName;
                dataRow[fixedColumn_TAC] = cellData.TAC;
                dataRow[fixedColumn_ECI] = cellData.ECI;
                dataRow[fixedColumn_BeginTime] = cellData.BeginTimeDes;

                foreach (FusionDataColumn funsionColumn in fusionColumns_Visible)
                {
                    string strValue = "-";
                    object ret;
                    if (cellData.DataDic != null && cellData.DataDic.TryGetValue((uint)funsionColumn.ColumnId, out ret))
                    {
                        strValue = ret.ToString();
                    }
                    dataRow[funsionColumn.ColumnName] = strValue;
                }
                dataTable.Rows.Add(dataRow);
            }
        }

        private void refreshArgView()
        {
            ColumnView view = gridCtrlArg.MainView as ColumnView;
            view.Columns.Clear();

            DataTable dataTable = new DataTable();
            dataTable.Columns.Add("小区", typeof(string));
            dataTable.Columns.Add(fixedColumn_TAC, typeof(int));
            dataTable.Columns.Add(fixedColumn_ECI, typeof(int));
            dataTable.Columns.Add(fixedColumn_BeginTime, typeof(string));

            FusionColumnCfgManager cfg = FusionColumnCfgManager.Instance;

            FusionDataColumnCollection argCollection;
            if (cfg.ColumnCollections.TryGetValue(FusionDataType.Arg, out argCollection))
            {
                List<FusionDataColumn> fusionColumns_Visible = new List<FusionDataColumn>();
                foreach (FusionDataColumn funsionColumn in argCollection.FusionDataColumns)
                {
                    if (funsionColumn.IsCheck)
                    {
                        fusionColumns_Visible.Add(funsionColumn);
                        dataTable.Columns.Add(funsionColumn.ColumnName);
                    }
                }

                addCellArgDataRow(dataTable, fusionColumns_Visible);
            }

            gridCtrlArg.DataSource = dataTable;
            gridCtrlArg.RefreshDataSource();
        }

        private void addCellArgDataRow(DataTable dataTable, List<FusionDataColumn> fusionColumns_Visible)
        {
            foreach (CellArgData cellData in argDataSet)
            {
                DataRow dataRow = dataTable.NewRow();
                dataRow["小区"] = cellData.CellName;
                dataRow[fixedColumn_TAC] = cellData.TAC;
                dataRow[fixedColumn_ECI] = cellData.ECI;
                dataRow[fixedColumn_BeginTime] = cellData.BeginTimeDes;

                foreach (FusionDataColumn funsionColumn in fusionColumns_Visible)
                {
                    string strValue = "-";
                    object ret;
                    if (cellData.DataDic != null && cellData.DataDic.TryGetValue((uint)funsionColumn.ColumnId, out ret))
                    {
                        strValue = ret.ToString();
                    }
                    dataRow[funsionColumn.ColumnName] = strValue;
                }
                dataTable.Rows.Add(dataRow);
            }
        }

        private void gvAlarm_CustomDrawEmptyForeground(object sender, DevExpress.XtraGrid.Views.Base.CustomDrawEventArgs e)
        {
            string strTip = string.Empty;
            if (MainModel.DTDataManager.FileDataManagers.Count > 0)
            {
                strTip = "所回放数据主服，无告警信息";
            }
            else
            {
                strTip = "请选择测试数据回放";
            }
            DevGridControlManager.DrawNoRowCountMessage(gvAlarm, e, strTip);
        }

        private void gvPerf_CustomDrawEmptyForeground(object sender, DevExpress.XtraGrid.Views.Base.CustomDrawEventArgs e)
        {
            string strTip = string.Empty;
            if (MainModel.DTDataManager.FileDataManagers.Count > 0)
            {
                strTip = "所回放数据主服，无性能信息";
            }
            else
            {
                strTip = "请选择测试数据回放";
            }
            DevGridControlManager.DrawNoRowCountMessage(gvPerf, e, strTip);
        }

        private void gvArg_CustomDrawEmptyForeground(object sender, DevExpress.XtraGrid.Views.Base.CustomDrawEventArgs e)
        {
            string strTip = string.Empty;
            if (MainModel.DTDataManager.FileDataManagers.Count > 0)
            {
                strTip = "所回放数据主服，无参数信息";
            }
            else
            {
                strTip = "请选择测试数据回放";
            }
            DevGridControlManager.DrawNoRowCountMessage(gvArg, e, strTip);
        }

        private void TsMenuItemKpiSet_Click(object sender, EventArgs e)
        {
            CellFusionDataFormSettingBox setBox = new CellFusionDataFormSettingBox();
            if (setBox.ShowDialog() == DialogResult.OK)
            {
                refreshView();
            }
        }
        private void TsMenuItemTopFront_Click(object sender, EventArgs e)
        {
            this.BeforePopPos = this.Location;
            this.MdiParent = null;
            this.Owner = mainModel.MainForm;
            this.Opacity = 1.0;
            this.opacityValue = 1.0;
            this.Show();
        }
        private void TsMenuItemExportAll_Click(object sender, EventArgs e)
        {
            List<GridView> gvs = new List<GridView>();
            List<string> sheetNames = new List<string>();
            foreach (TabPage tp in tabCtrl.TabPages)
            {
                DevExpress.XtraGrid.GridControl gc = tp.Controls[0] as DevExpress.XtraGrid.GridControl;
                GridView gv = gc.MainView as GridView;

                gvs.Add(gv);
                sheetNames.Add(tp.Text);
            }
            ExcelNPOIManager.ExportToExcel(gvs, sheetNames);
        }

        private void TsMenuItemReQuery_Click(object sender, EventArgs e)
        {
            MainModel.IsFusionInclude = true;
            dtDataChanged(null, null);
        }
    }

    //同一地市和日期的小区信息集合
    public class CommonCellItem
    {
        public CommonCellItem(TestPoint tp)
        {
            this.DistrictID = tp.DistrictID;
            this.Time = tp.DateTime.Date;
        }
        public string Token
        {
            get
            {
                return string.Format("{0}_{1}", this.DistrictID, this.Time.Date);
            }
        }
        public int DistrictID { get; set; }
        public DateTime Time { get; set; }
        
        public Dictionary<string, LTECell> CellDic { get; set; } = new Dictionary<string, LTECell>();

        public void AddCell(LTECell cell)
        {
            this.CellDic[string.Format("{0}_{1}", cell.TAC, cell.ECI)] = cell;
        }
    }
}
