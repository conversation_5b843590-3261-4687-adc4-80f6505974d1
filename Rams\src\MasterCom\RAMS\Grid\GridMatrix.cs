﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Grid
{
    /* 全图栅格的位置是固定的，以经纬度（0,0）的点为原点，北半球地图时，是在屏幕的左下方。
     * 随着纬度的增大，对应行数增大；经度增加，对应列数增加。
     * 以屏幕来看，从左往右为列数增大，从下往上为行数增大。
     */
    /// <summary>
    /// 栅格矩阵。详细说明见类注释
    /// </summary>
    public class GridMatrix<G> : ICollection<G> where G : GridUnitBase, new()
    {
        public GridMatrix()
        { }
        protected int minRowIdx = int.MaxValue;
        public int MinRowIdx
        {
            get { return minRowIdx; }
        }
        protected int maxRowIdx = 0;
        public int MaxRowIdx
        {
            get { return maxRowIdx; }
        }
        protected int minColIdx = int.MaxValue;
        public int MinColIdx
        {
            get { return minColIdx; }
        }
        protected int maxColIdx = 0;
        public int MaxColIdx
        {
            get { return maxColIdx; }
        }
        /// <summary>
        /// 矩阵的行数，含空行
        /// </summary>
        public int RowCount
        {
            get
            {
                if (minRowIdx.Equals(int.MaxValue))
                {
                    return 0;
                }
                return maxRowIdx - minRowIdx + 1;
            }
        }
        /// <summary>
        /// 矩阵列数，含空列
        /// </summary>
        public int ColCount
        {
            get
            {
                if (minColIdx.Equals(int.MaxValue))
                {
                    return 0;
                }
                return maxColIdx - minColIdx + 1;
            }
        }
        /// <summary>
        /// 栅格单元集合
        /// </summary>
        protected List<G> gridUnitList = new List<G>();
        /// <summary>
        /// 不为null的栅格总数
        /// </summary>
        public int Length
        {
            get
            {
                if (gridUnitList == null)
                {
                    return 0;
                }
                return gridUnitList.Count;
            }
        }
        public List<G> Grids
        {
            get { return gridUnitList; }
        }
        /// <summary>
        /// 栅格行,列为key的栅格矩阵
        /// </summary>
        protected Dictionary<int, Dictionary<int, G>> matrix = new Dictionary<int, Dictionary<int, G>>();
        public G this[int row, int col]
        {
            get
            {
                G grid = null;
                if (matrix != null && matrix.ContainsKey(row))
                {
                    matrix[row].TryGetValue(col, out grid);
                }
                return grid;
            }
            set
            {
                makeSureCollectionNotNull();
                calcMinMaxIdx(row, col);
                if (matrix.ContainsKey(row))
                {
                    if (matrix[row].ContainsKey(col))
                    {//已存在，需remove List里面的对应grid，再添加
                        if (matrix[row][col] != null)
                        {
                            gridUnitList.Remove(matrix[row][col]);
                        }
                        matrix[row][col] = value;
                    }
                    else
                    {
                        matrix[row].Add(col, value);
                    }
                }
                else
                {
                    Dictionary<int, G> colGridDic = new Dictionary<int, G>();
                    colGridDic.Add(col, value);
                    matrix.Add(row, colGridDic);
                }
                gridUnitList.Add(value);
            }
        }

        private void calcMinMaxIdx(int rowIdx, int colIdx)
        {
            minRowIdx = Math.Min(rowIdx, minRowIdx);
            maxRowIdx = Math.Max(rowIdx, maxRowIdx);
            minColIdx = Math.Min(colIdx, minColIdx);
            maxColIdx = Math.Max(colIdx, maxColIdx);
        }

        public DbRect GetBounds()
        {
            if (gridUnitList == null || gridUnitList.Count == 0 || minRowIdx == int.MaxValue || minColIdx == int.MaxValue)
            {
                return null;
            }
            G grid = gridUnitList[0];
            double ltLng = (double)(minColIdx * (decimal)grid.LngSpan);
            double ltLat = (double)((maxRowIdx + 1) * (decimal)grid.LatSpan);
            double brLng = (double)((maxColIdx + 1) * (decimal)grid.LngSpan);
            double brLat = (double)(minRowIdx * (decimal)grid.LatSpan);
            return new DbRect(ltLng, brLat, brLng, ltLat);
        }

        private void makeSureCollectionNotNull()
        {
            if (gridUnitList == null)
            {
                gridUnitList = new List<G>();
            }
            if (matrix == null)
            {
                matrix = new Dictionary<int, Dictionary<int, G>>();
            }
        }

        public void Merge(GridMatrix<G> otherMatrix)
        {
            foreach (G grid in otherMatrix)
            {
                this[grid.RowIdx, grid.ColIdx] = grid;
            }
        }

        public static GridMatrix<G> RefactorMatrixBySize(int sizeFactor, GridMatrix<G> origMatrix)
        {
            if (origMatrix == null)
            {
                return new GridMatrix<G>();
            }
            if (sizeFactor == 1)
            {
                return origMatrix;
            }
            GridMatrix<G> tarMatrix = new GridMatrix<G>();
            foreach (G grid in origMatrix)
            {
                if (grid != null)
                {
                    int tarRow, tarCol;
                    GridHelper.GetIndexOfCustomSizeGrid(sizeFactor, grid.CenterLng, grid.CenterLat, out tarRow, out tarCol);
                    G tarGridUnit = tarMatrix[tarRow, tarCol];
                    if (tarGridUnit == null)
                    {
                        double tarLTLng, tarLTLat;
                        GridHelper.GetLeftTopByCustomSizeGridIndex(sizeFactor, tarRow, tarCol, out tarLTLng, out tarLTLat);
                        tarGridUnit = new G();
                        tarGridUnit.Status = 1;
                        tarGridUnit.SizeFactor = sizeFactor;
                        tarGridUnit.LTLng = tarLTLng;
                        tarGridUnit.LTLat = tarLTLat;
                        tarMatrix[tarRow, tarCol] = tarGridUnit;
                    }
                    tarGridUnit.Merge(grid);
                }
            }
            return tarMatrix;
        }


        #region ICollection<G> 成员

        void ICollection<G>.Add(G item)
        {
            makeSureCollectionNotNull();
            gridUnitList.Add(item);
        }

        void ICollection<G>.Clear()
        {
            makeSureCollectionNotNull();
            gridUnitList.Clear();
        }

        bool ICollection<G>.Contains(G item)
        {
            makeSureCollectionNotNull();
            return gridUnitList.Contains(item);
        }

        void ICollection<G>.CopyTo(G[] array, int arrayIndex)
        {
            makeSureCollectionNotNull();
            gridUnitList.CopyTo(array, arrayIndex);
        }

        int ICollection<G>.Count
        {
            get { throw new NotImplementedException(); }
        }

        bool ICollection<G>.IsReadOnly
        {
            get { return false; }
        }

        bool ICollection<G>.Remove(G item)
        {
            throw new NotImplementedException();
        }

        #endregion

        #region IEnumerable<G> 成员

        IEnumerator<G> IEnumerable<G>.GetEnumerator()
        {
            return gridUnitList.GetEnumerator();
        }

        #endregion

        #region IEnumerable 成员

        IEnumerator IEnumerable.GetEnumerator()
        {
            return gridUnitList.GetEnumerator();
        }

        #endregion
    }
}
