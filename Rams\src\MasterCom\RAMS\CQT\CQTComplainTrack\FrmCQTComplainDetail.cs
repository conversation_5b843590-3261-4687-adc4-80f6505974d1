﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using CQTLibrary.PublicItem;
using CQTLibrary.CqtZTFunc;
using DevExpress.XtraCharts;
using DevExpress.Utils;

namespace MasterCom.RAMS.CQT
{
    public partial class FrmCQTComplainDetail : Form
    {
        public FrmCQTComplainDetail(List<TimePeriod> tpList)
        {
            InitializeComponent();
            label1.Text = "";
            trackBarControl1.Properties.Minimum = 0;
            TimeSpan ts1 = new TimeSpan(tpList[0].BeginTime.Ticks);
            TimeSpan ts2 = new TimeSpan(tpList[0].EndTime.Ticks);
            TimeSpan ts=ts1.Subtract(ts2).Duration();
             dateDiff= ts.Days;
            trackBarControl1.Properties.Maximum = dateDiff;
            chartControl1.Series.Clear();
            chartControl2.Series.Clear();
            chartControl4.Series.Clear();
            chartControl4.Series.Clear();
        }

        int dateDiff = 0;
        public ComplainResult ComplainResult { get; set; }
        public CQTLibrary.RAMS.NET.MainModel CqtModel { get; set; }
        public Dictionary<string, AreaDetail> InitcqtDetailDic { get; set; }
        public List<TimePeriod> TpList { get; set; }
        public List<CQTLibrary.PublicItem.ParaColumnItem> Paraitem { get; set; }
        /// <summary>
        /// 测试信息
        /// </summary>
        public List<EvaluateCqtDetailResult> EvaluateDetailResultList { get; set; }
        /// <summary>
        /// 性能信息
        /// </summary>
        public List<EvaluateCqtDetailResult> ParaDetailResultList { get; set; }
        public ComplainDetailResult DetailResult { get; set; }

        public void Query()
        {

            label1.Text = ComplainResult.StrCqtName;
            WaitBox.Text = "准备查询...";
            WaitBox.CanCancel = true;
            WaitBox.ProgressPercent = 40;
            WaitBox.Show("开始接收统计数据...", queryStatData);
            List<ComplainCase> cpList = new List<ComplainCase>();
            DataTable testTable = GetTestTable();
            DataTable paraTable = GetParaTable();
            cpList.AddRange(DetailResult.cpList);
            cpList.AddRange(DetailResult.nearCpList);
            this.gridControl1.DataSource = cpList;
            this.gridControl1.RefreshDataSource();
            this.gridControl2.DataSource = testTable;
            this.gridView2.BestFitColumns();
            this.gridControl2.RefreshDataSource();
            this.gridControl3.DataSource = paraTable;
            this.gridView3.BestFitColumns();
            this.gridControl3.RefreshDataSource();
            GetTestChart();
            GetParaChart();
        }
            /// <summary>
        /// 查询统计数据
        /// </summary>
        protected void queryStatData()
        {
            try
            {
                 ComplainAna compAna = new ComplainAna();
                 WaitBox.Text = "正在查询投诉数据...";
                 WaitBox.ProgressPercent = 20;
                 DetailResult = compAna.getCpDetailCase(TpList[0].BeginTime, TpList[0].EndTime, CqtModel, 100, InitcqtDetailDic[ComplainResult.StrCqtName]);
                 WaitBox.Text = "正在查询测试数据...";
                 WaitBox.ProgressPercent = 40;
                 EvaluateDetailResultList = compAna.getTestKpi(TpList[0].BeginTime, TpList[0].EndTime, CqtModel, Paraitem, InitcqtDetailDic[ComplainResult.StrCqtName]);    
                 WaitBox.Text = "正在查询性能数据...";
                 WaitBox.ProgressPercent = 60;
                 ParaDetailResultList = compAna.getParaKpi(TpList[0].BeginTime, TpList[0].EndTime, CqtModel, Paraitem, InitcqtDetailDic[ComplainResult.StrCqtName]);
            }
            catch
            {
                //continue
            }
            finally
            {
                WaitBox.Close();
            }
        }

        int trackbarValue = 0;
        /// <summary>
        /// 刷新数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void simpleButton1_Click(object sender, EventArgs e)
        {
             trackbarValue = trackBarControl1.Value;
             WaitBox.Text = "准备查询...";
             WaitBox.CanCancel = true;
             WaitBox.Show("开始接收统计数据...", queryData);
             DataTable testTable = GetTestTable();
             DataTable paraTable = GetParaTable();
             List<ComplainCase> cpList = new List<ComplainCase>();
             cpList.AddRange(DetailResult.cpList);
             cpList.AddRange(DetailResult.nearCpList);
             this.gridControl1.DataSource = cpList;
             this.gridControl1.RefreshDataSource();
             this.gridControl2.DataSource = testTable;
             this.gridView2.BestFitColumns();
             this.gridControl2.RefreshDataSource();
             this.gridControl3.DataSource = paraTable;
             this.gridView3.BestFitColumns();
             this.gridControl3.RefreshDataSource();
             GetTestChart();
             GetParaChart();
        }

        /// <summary>
        /// 查询统计数据
        /// </summary>
        protected void queryData()
        {
            try
            {
                ComplainAna compAna = new ComplainAna();
                WaitBox.Text = "正在查询投诉数据...";
                WaitBox.ProgressPercent = 20;
                DetailResult = compAna.getCpDetailCase(TpList[0].BeginTime.AddDays(dateDiff-trackbarValue), TpList[0].EndTime, CqtModel, 500, InitcqtDetailDic[ComplainResult.StrCqtName]);
                WaitBox.Text = "正在查询测试数据...";
                WaitBox.ProgressPercent = 40;
                EvaluateDetailResultList = compAna.getTestKpi(TpList[0].BeginTime.AddDays(dateDiff-trackbarValue), TpList[0].EndTime, CqtModel, Paraitem, InitcqtDetailDic[ComplainResult.StrCqtName]);
                WaitBox.Text = "正在查询性能数据...";
                WaitBox.ProgressPercent = 60;
                ParaDetailResultList = compAna.getParaKpi(TpList[0].BeginTime.AddDays(dateDiff-trackbarValue), TpList[0].EndTime, CqtModel, Paraitem, InitcqtDetailDic[ComplainResult.StrCqtName]);
            }
            catch
            {
                //continue
            }
            finally
            {
                WaitBox.Close();
            }
        }

        /// <summary>
        /// 获取测试数据的列表
        /// </summary>
        /// <returns></returns>
        public DataTable GetTestTable()
        {
            DataTable testTable = new DataTable();
            testTable.Columns.Add("a");
            testTable.Columns.Add("b");
            testTable.Columns.Add("c");
            testTable.Columns.Add("d");
            testTable.Columns.Add("e");
            testTable.Columns.Add("f");
            testTable.Columns.Add("g");
            for (int j = 1; j <= 4; j++)
            {
                DataRow dr = testTable.NewRow();
                testTable.Rows.Add(dr);
            }
            int i = 0;
            if (EvaluateDetailResultList.Count > 0)
            {
                testTable.Rows[i][0] = "GSM指标项";
                testTable.Rows[i + 1][0] = "值";
                testTable.Rows[i + 2][0] = "TD指标项";
                testTable.Rows[i + 3][0] = "值";
                foreach (EvaluateCqtDetailResult testDetail in EvaluateDetailResultList)
                {
                    if (testDetail.StrKpiType.Contains("GSM"))
                    {
                        addGsmTestDetail(testTable, i, testDetail);
                    }
                    else if (testDetail.StrKpiType.Contains("TD"))
                    {
                        addTdTestDetail(testTable, i, testDetail);
                    }
                }
            }
            return testTable;
        }

        private void addGsmTestDetail(DataTable testTable, int i, EvaluateCqtDetailResult testDetail)
        {
            if (testDetail.StrKpiName.Contains("全程呼叫成功率"))
            {
                testTable.Rows[i][1] = testDetail.StrKpiName;
                testTable.Rows[i + 1][1] = testDetail.StrValue;
            }
            else if (testDetail.StrKpiName.Contains("网络覆盖率"))
            {
                testTable.Rows[i][2] = testDetail.StrKpiName;
                testTable.Rows[i + 1][2] = testDetail.StrValue;

            }
            else if (testDetail.StrKpiName.Contains("RxQuality占比"))
            {
                testTable.Rows[i][3] = testDetail.StrKpiName;
                testTable.Rows[i + 1][3] = testDetail.StrValue;
            }
            else if (testDetail.StrKpiName.Contains("半速率占比"))
            {
                testTable.Rows[i][4] = testDetail.StrKpiName;
                testTable.Rows[i + 1][4] = testDetail.StrValue;
            }
            else if (testDetail.StrKpiName.Contains("平均时延"))
            {
                testTable.Rows[i][5] = testDetail.StrKpiName;
                testTable.Rows[i + 1][5] = testDetail.StrValue;
            }
            else if (testDetail.StrKpiName.Contains("下载速率"))
            {
                testTable.Rows[i][6] = testDetail.StrKpiName;
                testTable.Rows[i + 1][6] = testDetail.StrValue;
            }
        }

        private void addTdTestDetail(DataTable testTable, int i, EvaluateCqtDetailResult testDetail)
        {
            if (testDetail.StrKpiName.Contains("全程呼叫成功率"))
            {
                testTable.Rows[i + 2][1] = testDetail.StrKpiName;
                testTable.Rows[i + 3][1] = testDetail.StrValue;
            }
            else if (testDetail.StrKpiName.Contains("网络覆盖率"))
            {
                testTable.Rows[i + 2][2] = testDetail.StrKpiName;
                testTable.Rows[i + 3][2] = testDetail.StrValue;
            }
            else if (testDetail.StrKpiName.Contains("BLER占比"))
            {
                testTable.Rows[i + 2][3] = testDetail.StrKpiName;
                testTable.Rows[i + 3][3] = testDetail.StrValue;
            }
            else if (testDetail.StrKpiName.Contains("平均时延"))
            {
                testTable.Rows[i + 2][4] = testDetail.StrKpiName;
                testTable.Rows[i + 3][4] = testDetail.StrValue;
            }
            else if (testDetail.StrKpiName.Contains("500k占比"))
            {
                testTable.Rows[i + 2][5] = testDetail.StrKpiName;
                testTable.Rows[i + 3][5] = testDetail.StrValue;
            }
            else if (testDetail.StrKpiName.Contains("下载速率"))
            {
                testTable.Rows[i + 2][6] = testDetail.StrKpiName;
                testTable.Rows[i + 3][6] = testDetail.StrValue;
            }
        }

        public DataTable GetParaTable()
        {
            DataTable paraTable = new DataTable();
            paraTable.Columns.Add("a");
            paraTable.Columns.Add("b");
            paraTable.Columns.Add("c");
            paraTable.Columns.Add("d");
            paraTable.Columns.Add("e");
            paraTable.Columns.Add("f");
            paraTable.Columns.Add("g");
            paraTable.Columns.Add("h");
            for (int j = 1; j <= 4; j++)
            {
                DataRow dr = paraTable.NewRow();
                paraTable.Rows.Add(dr);
            }
            int g = 0;
            if (ParaDetailResultList.Count > 0)
            {
                paraTable.Rows[g][0] = "GSM指标项";
                paraTable.Rows[g + 1][0] = "值";
                paraTable.Rows[g + 2][0] = "TD指标项";
                paraTable.Rows[g + 3][0] = "值";
                foreach (EvaluateCqtDetailResult paraDetail in ParaDetailResultList)
                {
                    if (paraDetail.StrKpiType.Contains("GSM"))
                    {
                        addGsmParaDetail(paraTable, g, paraDetail);
                    }
                    else if (paraDetail.StrKpiType.Contains("TD"))
                    {
                        addTdParaDetail(paraTable, g, paraDetail);
                    }
                }
            }
            return paraTable;
        }

        private void addGsmParaDetail(DataTable paraTable, int g, EvaluateCqtDetailResult paraDetail)
        {
            if (paraDetail.StrKpiName.Contains("无线接入性"))
            {
                paraTable.Rows[g][1] = paraDetail.StrKpiName;
                paraTable.Rows[g + 1][1] = paraDetail.StrValue + "%";

            }
            else if (paraDetail.StrKpiName.Contains("话音信道掉话率"))
            {
                paraTable.Rows[g][2] = paraDetail.StrKpiName;
                paraTable.Rows[g + 1][2] = paraDetail.StrValue;
            }
            else if (paraDetail.StrKpiName.Contains("话音信道拥塞率"))
            {
                paraTable.Rows[g][3] = paraDetail.StrKpiName;
                paraTable.Rows[g + 1][3] = paraDetail.StrValue;
            }
            else if (paraDetail.StrKpiName.Contains("TBF建立成功率"))
            {
                paraTable.Rows[g][4] = paraDetail.StrKpiName;
                paraTable.Rows[g + 1][4] = paraDetail.StrValue;
            }
            else if (paraDetail.StrKpiName.Contains("TBF掉线率"))
            {
                paraTable.Rows[g][5] = paraDetail.StrKpiName;
                paraTable.Rows[g + 1][5] = paraDetail.StrValue;

            }
            else if (paraDetail.StrKpiName.Contains("下行TBF无线拥塞率"))
            {
                paraTable.Rows[g][6] = paraDetail.StrKpiName;
                paraTable.Rows[g + 1][6] = paraDetail.StrValue;
            }
            else if (paraDetail.StrKpiName.Contains("无线利用率"))
            {
                paraTable.Rows[g][7] = paraDetail.StrKpiName;
                paraTable.Rows[g + 1][7] = paraDetail.StrValue;
            }
        }

        private void addTdParaDetail(DataTable paraTable, int g, EvaluateCqtDetailResult paraDetail)
        {
            if (paraDetail.StrKpiName.Contains("CS接通率"))
            {
                paraTable.Rows[g + 2][1] = paraDetail.StrKpiName;
                paraTable.Rows[g + 3][1] = paraDetail.StrValue;
            }
            else if (paraDetail.StrKpiName.Contains("CS掉话率"))
            {
                paraTable.Rows[g + 2][2] = paraDetail.StrKpiName;
                paraTable.Rows[g + 3][2] = paraDetail.StrValue;
            }
            else if (paraDetail.StrKpiName.Contains("PS接通率"))
            {
                paraTable.Rows[g + 2][3] = paraDetail.StrKpiName;
                paraTable.Rows[g + 3][3] = paraDetail.StrValue;
            }
            else if (paraDetail.StrKpiName.Contains("PS掉线率"))
            {
                paraTable.Rows[g + 2][4] = paraDetail.StrKpiName;
                paraTable.Rows[g + 3][4] = paraDetail.StrValue;
            }
            else if (paraDetail.StrKpiName.Contains("Csrab拥塞率"))
            {
                paraTable.Rows[g + 2][5] = paraDetail.StrKpiName;
                paraTable.Rows[g + 3][5] = paraDetail.StrValue;
            }
            else if (paraDetail.StrKpiName.Contains("误块率"))
            {
                paraTable.Rows[g + 2][6] = paraDetail.StrKpiName;
                paraTable.Rows[g + 3][6] = paraDetail.StrValue;
            }
            else if (paraDetail.StrKpiName.Contains("码资源利用率"))
            {
                paraTable.Rows[g + 2][7] = paraDetail.StrKpiName;
                paraTable.Rows[g + 3][7] = paraDetail.StrValue;
            }
        }

        /// <summary>
        /// 测试数据图表
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void GetTestChart()
        {
            if (gridView2.SelectedRowsCount > 0)
            {
                chartControl1.Series.Clear();
                chartControl2.Series.Clear();
                DataRow row0 = gridView2.GetDataRow(0);
                DataRow row2 = gridView2.GetDataRow(2);
                Series series = new Series("series", ViewType.RadarArea);
                Series series1 = new Series("series", ViewType.RadarArea);
                int i = 1;
                while (i < gridView2.Columns.Count)
                {
                    string caption = row0[i].ToString();
                    string captionTD = row2[i].ToString();
                    if ((caption != null && caption.Trim() != "") || (captionTD != null && captionTD.Trim() != ""))
                    {
                        setTestChartControlSeries(series, series1, caption, captionTD);
                    }
                    i++;
                }
            }
        }

        private void setTestChartControlSeries(Series series, Series series1, string caption, string captionTD)
        {
            for (int id = 0; id < EvaluateDetailResultList.Count; id++)
            {
                EvaluateCqtDetailResult detailResultList = EvaluateDetailResultList[id];
                if (EvaluateDetailResultList[id].StrKpiType.Contains("GSM"))
                {
                    addChartControlSeries(series, caption, detailResultList, chartControl1);
                }
                else if (EvaluateDetailResultList[id].StrKpiType.Contains("TD"))
                {
                    addChartControlSeries(series1, captionTD, detailResultList, chartControl2);
                }
            }
        }

        private void addChartControlSeries(Series series, string caption, EvaluateCqtDetailResult detailResultList, ChartControl chartControl)
        {
            if (detailResultList.StrKpiName.Contains(caption))
            {
                int value = int.Parse(detailResultList.FHealth.ToString());
                if (value <= 0)
                    value = 0;
                chartControl.Series.Add(series);
                SeriesPoint point = new SeriesPoint(caption, value);
                series.Points.Add(point);
            }
        }

        /// <summary>
        /// 性能数据图表
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void GetParaChart()
        {
            if (gridView3.SelectedRowsCount > 0)
            {
                chartControl3.Series.Clear();
                chartControl4.Series.Clear();
                DataRow row0 = gridView3.GetDataRow(0);
                DataRow row2 = gridView3.GetDataRow(2);
                Series series = new Series("series", ViewType.RadarArea);
                Series series1 = new Series("series", ViewType.RadarArea);
                int i = 1;
                while (i < gridView3.Columns.Count)
                {
                    string caption = row0[i].ToString();
                    string captionTD = row2[i].ToString();
                    if ((caption != null && caption.Trim() != "") || (captionTD != null && captionTD.Trim() != ""))
                    {
                        setParaChartControlSeries(series, series1, caption, captionTD);
                    }
                    i++;
                }                
            }
        }

        private void setParaChartControlSeries(Series series, Series series1, string caption, string captionTD)
        {
            for (int id = 0; id < ParaDetailResultList.Count; id++)
            {
                EvaluateCqtDetailResult detailResultList = ParaDetailResultList[id];
                if (ParaDetailResultList[id].StrKpiType.Contains("GSM"))
                {
                    addChartControlSeries(series, caption, detailResultList, chartControl3);
                }
                else if (ParaDetailResultList[id].StrKpiType.Contains("TD"))
                {
                    addChartControlSeries(series1, captionTD, detailResultList, chartControl4);
                }
            }
        }

        private void trackBarControl1_BeforeShowValueToolTip(object sender, DevExpress.XtraEditors.TrackBarValueToolTipEventArgs e)
        {
            e.ShowArgs.ToolTip = string.Format("最后{0}天", trackBarControl1.Value);
        }
    }
}
