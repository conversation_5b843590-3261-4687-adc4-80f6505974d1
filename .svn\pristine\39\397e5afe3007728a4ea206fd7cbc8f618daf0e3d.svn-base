﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTAbnormalMosInfoForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.objectListViewAbnormalMos = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLacCi = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMosCount = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnFileName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLong = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnLat = new BrightIdeasSoftware.OLVColumn();
            olvColumnMosType = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnMSTxPowerMean = new BrightIdeasSoftware.OLVColumn();
            ((System.ComponentModel.ISupportInitialize)(this.objectListViewAbnormalMos)).BeginInit();
            this.SuspendLayout();
            // 
            // objectListViewAbnormalMos
            // 
            this.objectListViewAbnormalMos.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnLacCi,
            this.olvColumnMosCount,
            this.olvColumnFileName,
            this.olvColumnLong,
            this.olvColumnLat,
            this.olvColumnMosType,
            this.olvColumnMSTxPowerMean});
            this.objectListViewAbnormalMos.Cursor = System.Windows.Forms.Cursors.Default;
            this.objectListViewAbnormalMos.Dock = System.Windows.Forms.DockStyle.Fill;
            this.objectListViewAbnormalMos.FullRowSelect = true;
            this.objectListViewAbnormalMos.GridLines = true;
            this.objectListViewAbnormalMos.Location = new System.Drawing.Point(0, 0);
            this.objectListViewAbnormalMos.Name = "objectListViewAbnormalMos";
            this.objectListViewAbnormalMos.OwnerDraw = true;
            this.objectListViewAbnormalMos.ShowGroups = false;
            this.objectListViewAbnormalMos.Size = new System.Drawing.Size(901, 360);
            this.objectListViewAbnormalMos.TabIndex = 1;
            this.objectListViewAbnormalMos.UseCompatibleStateImageBehavior = false;
            this.objectListViewAbnormalMos.View = System.Windows.Forms.View.Details;
            this.objectListViewAbnormalMos.VirtualMode = true;
            this.objectListViewAbnormalMos.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.objectListViewAbnormalMos_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 68;
            // 
            // olvColumnLacCi
            // 
            this.olvColumnLacCi.HeaderFont = null;
            this.olvColumnLacCi.Text = "小区LAC-CI";
            this.olvColumnLacCi.Width = 104;
            // 
            // olvColumnMosCount
            // 
            this.olvColumnMosCount.HeaderFont = null;
            this.olvColumnMosCount.Text = "MOS采样点周期数";
            this.olvColumnMosCount.Width = 107;
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名";
            this.olvColumnFileName.Width = 217;
            // 
            // olvColumnLong
            // 
            this.olvColumnLong.HeaderFont = null;
            this.olvColumnLong.Text = "经度";
            this.olvColumnLong.Width = 102;
            // 
            // olvColumnLat
            // 
            this.olvColumnLat.HeaderFont = null;
            this.olvColumnLat.Text = "纬度";
            this.olvColumnLat.Width = 98;
            // 
            // olvColumnMosType
            // 
            this.olvColumnMosType.HeaderFont = null;
            this.olvColumnMosType.Text = "网络类型";
            this.olvColumnMosType.Width = 98;
            // 
            // olvColumnMSTxPowerMean
            // 
            this.olvColumnMSTxPowerMean.HeaderFont = null;
            this.olvColumnMSTxPowerMean.Text = "MSTxPower平均值";
            this.olvColumnMSTxPowerMean.Width = 113;
            // 
            // ZTAbnormalMosInfoForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(901, 360);
            this.Controls.Add(this.objectListViewAbnormalMos);
            this.Name = "ZTAbnormalMosInfoForm";
            this.Text = "小区异常信息列表";
            ((System.ComponentModel.ISupportInitialize)(this.objectListViewAbnormalMos)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView objectListViewAbnormalMos;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnLacCi;
        private BrightIdeasSoftware.OLVColumn olvColumnMosCount;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnLong;
        private BrightIdeasSoftware.OLVColumn olvColumnLat;
        private BrightIdeasSoftware.OLVColumn olvColumnMosType;
        private BrightIdeasSoftware.OLVColumn olvColumnMSTxPowerMean;

    }
}