﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class PlanningStationImportForm : MinCloseForm
    {
        private MainModel mainModel; 

        public PlanningStationImportForm(MainModel mm) : base(mm)
        {
            InitializeComponent();
            mainModel = mm;

            foreach (string name in PlanningStationManager.GetFileNames())
            {
                cbxFileNames.Items.Add(name);
            }

            foreach (string str in Enum.GetNames(typeof(MapWinGIS.tkPointShapeType)))
            {
                cbxPointStyle.Items.Add(str);
            }
            int index = cbxPointStyle.Items.IndexOf(Enum.GetName(typeof(MapWinGIS.tkPointShapeType), MapWinGIS.tkPointShapeType.ptShapeCross));
            cbxPointStyle.SelectedIndex = index;

            btnOpen.Click += BtnOpen_Click;
            btnClose.Click += BtnClose_Click;
            btnDelete.Click += BtnDelete_Click;
            btnRefresh.Click += BtnRefresh_Click;
            pannelColor.Click += PanelColor_Click;
            cbxFileNames.SelectedIndexChanged += CbxFileNames_SelectedChanged;

            if (cbxFileNames.Items.Count != 0)
            {
                cbxFileNames.SelectedIndex = 0;
            }
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            //this.Visible = false;
            this.Close();
        }

        private void BtnOpen_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = FilterHelper.Excel;
            dlg.RestoreDirectory = true;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            if (!cbxFileNames.Items.Contains(dlg.FileName))
            {
                this.Cursor = Cursors.WaitCursor;
                if (PlanningStationManager.Add(dlg.FileName))
                {
                    cbxFileNames.SelectedIndex = cbxFileNames.Items.Add(dlg.FileName);
                }
                else
                {
                    MessageBox.Show("加载Excel文件信息失败", "提示");
                }
                this.Cursor = Cursors.Default;
            }
            else
            {
                cbxFileNames.SelectedIndex = cbxFileNames.Items.IndexOf(dlg.FileName);
            }
        }

        private void CbxFileNames_SelectedChanged(object sender, EventArgs e)
        {
            string fileName = cbxFileNames.SelectedItem as string;
            StationShowItem item = PlanningStationManager.GetItem(fileName);
            if (item == null)
            {
                return;
            }
            chkVisible.Checked = item.DrawStyle.Visible;
            numSize.Value = (decimal)item.DrawStyle.PointSize;
            pannelColor.BackColor = ColorTranslator.FromOle((int)item.DrawStyle.FillColor);
            cbxPointStyle.SelectedIndex = cbxPointStyle.Items.IndexOf(Enum.GetName(typeof(MapWinGIS.tkPointShapeType), item.DrawStyle.PointShape));
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (cbxFileNames.SelectedItem == null)
            {
                return;
            }
            string fileName = cbxFileNames.SelectedItem as string;
            PlanningStationManager.Remove(fileName);
            cbxFileNames.Items.Remove(fileName);
            if (cbxFileNames.Items.Count != 0)
            {
                cbxFileNames.SelectedIndex = 0;
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            if (cbxFileNames.SelectedItem == null)
            {
                return;
            }
            string fileName = cbxFileNames.SelectedItem as string;
            StationShowItem item = PlanningStationManager.GetItem(fileName);
            item.DrawStyle.Visible = chkVisible.Checked;
            item.DrawStyle.PointSize = PlanningStationManager.PointSize = (float)numSize.Value;
            item.DrawStyle.FillColor = (uint)ColorTranslator.ToOle(pannelColor.BackColor);
            item.DrawStyle.PointShape = (MapWinGIS.tkPointShapeType)Enum.Parse(typeof(MapWinGIS.tkPointShapeType), cbxPointStyle.SelectedItem as string);
            PlanningStationManager.Refresh(fileName);
        }

        private void PanelColor_Click(object sender, EventArgs e)
        {
            ColorDialog dlg = new ColorDialog();
            dlg.AllowFullOpen = false;
            dlg.FullOpen = false;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            pannelColor.BackColor = dlg.Color;
            dlg.Dispose();
        }
    }
}
