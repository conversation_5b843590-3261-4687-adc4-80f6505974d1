﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.Utils;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class XtraIPerformanceForm : DevExpress.XtraEditors.XtraForm
    {
        public XtraIPerformanceForm()
        {
            InitializeComponent();
        }

        public void setdata(DataTable datatable)
        {
            this.gridControl1.DataSource = datatable;

            for (int i = 0; i < 3; i++)
            {
                gridView1.VisibleColumns[i].Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;
            }

            gridView1.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;  //列头自动换行
            gridView1.Appearance.HeaderPanel.TextOptions.Trimming = DevExpress.Utils.Trimming.None;  //dev的bug  必须设置trimming为None

            gridView1.ColumnPanelRowHeight = 50;       //必须设置列头高度 否则不会换行
            gridView1.Columns[0].Width = 150;
            gridView1.Columns[0].DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            gridView1.Columns[0].DisplayFormat.FormatType = FormatType.DateTime;

            gridView1.Columns[0].GroupFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            gridView1.Columns[0].GroupFormat.FormatType = FormatType.DateTime;

        }

        //判断颜色
        private void gridView1_RowCellStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs e)
        {
            setColor(e, new ColumnInfo("信令信道拥塞率", 0.05, 1));
            setColor(e, new ColumnInfo("信令信道分配成功率", 0, 0.95));
            setColor(e, new ColumnInfo("话音信道拥塞率(不含切)", 0.03, 1));
            setColor(e, new ColumnInfo("话音信道拥塞率(含切)", 0.03, 1));
            setColor(e, new ColumnInfo("话音信道掉话率(不含切)", 0.04, 1));
            setColor(e, new ColumnInfo("话音信道掉话率(含切)", 0.05, 1));
            setColor(e, new ColumnInfo("话音信道分配成功率(不含切)", 0, 0.94));
            setColor(e, new ColumnInfo("随机接入成功率", 0, 0.3));
            setColor(e, new ColumnInfo("GSM拥塞率", 0.025, 1));
            setColor(e, new ColumnInfo("无线接通率", 0, 0.97));
            setColor(e, new ColumnInfo("无线利用率", 0.15, 0.80, false));
            setColor(e, new ColumnInfo("话音信道切换掉话次数", 400, 10000));
            setColor(e, new ColumnInfo("话务掉话比", 10, 100));
            setColor(e, new ColumnInfo("每线话务量", 0.7, 100));
            setColor(e, new ColumnInfo("半速率话务比例", 0.5, 1));
            setColor(e, new ColumnInfo("干扰频带3", 0.5, 100));
            setColor(e, new ColumnInfo("干扰频带4", 0.3, 100));
            setColor(e, new ColumnInfo("干扰频带5", 0.15, 100));
            setColor(e, new ColumnInfo("测量报告上行覆盖率", 0, 0.7));
            setColor(e, new ColumnInfo("测量报告下行覆盖率", 0, 0.75));
            setColor(e, new ColumnInfo("下行话音质量", 0, 0.88));
            setColor(e, new ColumnInfo("上行话音质量", 0, 0.85));
            setColor(e, new ColumnInfo("下行质差话务比例", 0.05, 1));
            setColor(e, new ColumnInfo("上行质差话务比例", 0.05, 1));
            setColor(e, new ColumnInfo("切换成功率", 0, 0.85));
            setColor(e, new ColumnInfo("切换出成功率", 0, 0.9));
            setColor(e, new ColumnInfo("切换入成功率", 0, 0.9));
            setColor(e, new ColumnInfo("BSC内切换成功率", 0, 0.8));
            setColor(e, new ColumnInfo("上行质差切换率", 0.05, 1));
            setColor(e, new ColumnInfo("下行质差切换率", 0.05, 1));
        }

        private void setColor(DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs e, ColumnInfo columnInfo)
        {
            if (e.Column.FieldName == columnInfo.Name)
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, gridView1.Columns[columnInfo.Name]);
                if ((columnInfo.InRange && columnInfo.JudgeInRange(aa))
                    || (!columnInfo.InRange && columnInfo.JudgeOutRange(aa)))
                {
                    e.Appearance.BackColor = Color.Red;
                }
            }
        }

        class ColumnInfo
        {
            public ColumnInfo(string name, double min, double max, bool inRange = true)
            {
                Name = name;
                Min = min;
                Max = max;
                InRange = inRange;
            }

            public string Name { get; set; }
            public double Min { get; set; }
            public double Max { get; set; }
            public bool InRange { get; set; }

            public virtual bool JudgeInRange(string str)
            {
                double dValue = Convert.ToDouble(str);
                if (dValue > Min && dValue < Max)
                {
                    return true;
                }
                return false;
            }

            public virtual bool JudgeOutRange(string str)
            {
                double dValue = Convert.ToDouble(str);
                if (dValue > Max || dValue < Min)
                {
                    return true;
                }
                return false;
            }
        }
    }
}