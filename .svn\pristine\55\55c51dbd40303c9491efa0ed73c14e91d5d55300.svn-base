﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.NOP;

namespace MasterCom.RAMS.NOP
{
    public partial class TaskFlowDiagramForm : MinCloseForm
    {
        public TaskFlowDiagramForm()
        {
            InitializeComponent();
            this.diagram.CurDisplayRelationChanged += diagram_CurDisplayRelationChanged;
            this.diagram.SelectProcChanged += diagram_SelectProcChanged;
            this.diagram.SelectNodeChanged += diagram_SelectNodeChanged;
        }
        public TaskFlowDiagramForm(string text)
            : this()
        {
            if (!string.IsNullOrEmpty(text))
            {
                this.Text = text;
            }
        }

        //public override String GetKeyTypeName()
        //{
        //    return this.GetType().FullName + "_" + this.Text;
        //}

        void diagram_SelectNodeChanged(object sender, EventArgs e)
        {
            NodeEntry node = diagram.CurSelNode;
            if (node == null)
            {
                return;
            }
            ESResultInfo result = esResultInfo;
            if (result == null)
            {
                tbxNodeInfo.Text = string.Empty;
                return;
            }
            NodeResultInfo nodeInfo = result.GetNodeInfo(CurPSI.Seq, node._Idx);
            if (nodeInfo != null)
            {
                StringBuilder sb = new StringBuilder();
                foreach (string item in nodeInfo.Details.Split(';'))
                {
                    sb.Append(item);
                    sb.AppendLine();
                }
                this.tbxNodeInfo.Text = sb.ToString();
            }
            else
            {
                tbxNodeInfo.Text = string.Empty;
            }
        }

        protected TaskEventItem taskEventItem = null;
        public TaskEventItem TaskEventItem
        {
            get { return taskEventItem; }
            set
            {
                taskEventItem = value;
                refresh();
            }
        }

        protected virtual ESResultInfo esResultInfo
        {
            get
            {
                if (taskEventItem == null)
                {
                    return null;
                }
                return taskEventItem.ESResultInfo;
            }
        }

        private void refresh()
        {
            tbxSummary.Text = string.Empty;
            tbxDetails.Text = string.Empty;
            if (esResultInfo != null)
            {
                tbxSummary.Text = string.Format("{0}{1}建议：{2}", taskEventItem.Name
                    , Environment.NewLine, esResultInfo.Suggest);

                StringBuilder sb = new StringBuilder();
                foreach (string item in esResultInfo.DetailSet)
                {
                    sb.Append(item);
                    sb.AppendLine();
                }
                tbxDetails.Text = sb.ToString();

                diagram.ProcRelation = getProcRelation(esResultInfo.RelationID);
                if (diagram.ProcRelation == null)
                {
                    MessageBox.Show("未能获取流程：" + esResultInfo.RelationID);
                    return;
                }
                CurPSI = new ProcSequenceInfo(diagram.ProcRelation.Proc);
                diagram.CurProcSeq = CurPSI;
                diagram.ESResultInfo = esResultInfo;
                updateText();
            }
            else
            {
                diagram.ProcRelation = null;
            }
        }

        protected virtual ProcRelation getProcRelation(int id)
        {
            return ProcRoutineManager.Instance.GetRelation(id);
        }

        private Stack<ProcSequenceInfo> queRoutineBack = new Stack<ProcSequenceInfo>();
        private Stack<ProcSequenceInfo> queRoutineFoword = new Stack<ProcSequenceInfo>();

        private ProcSequenceInfo CurPSI = null;


        void diagram_CurDisplayRelationChanged(object sender, EventArgs e)
        {
            queRoutineBack.Clear();
            queRoutineFoword.Clear();
            updateNaviBtnState();
        }

        private void btnBack_Click(object sender, EventArgs e)
        {
            if (queRoutineBack.Count == 0)
            {
                return;
            }
            ProcRoutine curProc = diagram.CurSelProc;
            ProcRoutine top = null;
            if (queRoutineFoword.Count > 0)
            {
                top = queRoutineFoword.Peek().Proc;
            }
            if (top != curProc)
            {
                queRoutineFoword.Push(CurPSI);
            }

            CurPSI = queRoutineBack.Pop();
            ProcRoutine routine = CurPSI.Proc;
            diagram.CurSelProc = routine;
            diagram.CurProcSeq = CurPSI;
            updateNaviBtnState();
            updateText();
        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            if (queRoutineFoword.Count == 0)
            {
                return;
            }
            ProcRoutine curProc = diagram.CurSelProc;

            ProcRoutine top = null;
            if (queRoutineBack.Count > 0)
            {
                top = queRoutineBack.Peek().Proc;
            }
            if (top != curProc)
            {
                queRoutineBack.Push(CurPSI);
            }
            CurPSI = queRoutineFoword.Pop();
            ProcRoutine routine = CurPSI.Proc;
            diagram.CurSelProc = routine;
            diagram.CurProcSeq = CurPSI;
            updateNaviBtnState();
            updateText();
        }


        private void updateNaviBtnState()
        {
            btnBack.Enabled = queRoutineBack.Count > 0;
            btnNext.Enabled = queRoutineFoword.Count > 0;
        }

        //Dictionary<string, ProcSequenceInfo> seqDic = new Dictionary<string, ProcSequenceInfo>();
        //private ProcSequenceInfo getProcSeq(int procID)
        //{
        //    string seq = this.CurPSI.CreateSeq(procID);
        //    ProcSequenceInfo psi = null;
        //    if (!seqDic.TryGetValue(seq, out psi))
        //    {

        //    }
        //    return psi;
        //}
        void diagram_SelectProcChanged(object sender, EventArgs e)
        {
            ProcChangedEventArgs evt = e as ProcChangedEventArgs;
            if (evt.CurProc != null && evt.OldProc != evt.CurProc)
            {
                ProcRoutine top = null;
                if (queRoutineBack.Count > 0)
                {
                    top = queRoutineBack.Peek().Proc;
                }
                if (top != evt.OldProc)
                {
                    queRoutineBack.Push(CurPSI);
                    CurPSI = CurPSI.CreateSeq(evt.CurProc);
                    //queRoutineBack.Push(evt.OldProc);
                }

                if (queRoutineFoword.Count > 0)
                {
                    top = queRoutineFoword.Peek().Proc;
                    if (top == evt.CurProc)
                    {
                        queRoutineFoword.Pop();
                    }
                }
            }
            diagram.CurProcSeq = CurPSI;
            updateNaviBtnState();
            updateText();
        }

        private void updateText()
        {
            this.Text = CurPSI.Name;
        }
    }

}

