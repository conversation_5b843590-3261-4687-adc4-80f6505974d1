﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class VoLteWeakCoverAnaSetForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.groupBox7 = new System.Windows.Forms.GroupBox();
            this.tbxSuggest = new System.Windows.Forms.TextBox();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.tbxDescription = new System.Windows.Forms.TextBox();
            this.simpleButtonDown = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonUp = new DevExpress.XtraEditors.SimpleButton();
            this.checkedListBoxControlReason = new DevExpress.XtraEditors.CheckedListBoxControl();
            this.simpleButtonCancel = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButtonOK = new DevExpress.XtraEditors.SimpleButton();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.numRsrpWeakGate = new DevExpress.XtraEditors.SpinEdit();
            this.label8 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label1 = new System.Windows.Forms.Label();
            this.numLteBtsDisGate = new DevExpress.XtraEditors.SpinEdit();
            this.label11 = new System.Windows.Forms.Label();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label14 = new System.Windows.Forms.Label();
            this.numMinSecondsMainLowerNear = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl18 = new DevExpress.XtraEditors.LabelControl();
            this.numRsrpMainLowerNear = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl38 = new DevExpress.XtraEditors.LabelControl();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.numLastSecondsBeforeWeak = new DevExpress.XtraEditors.SpinEdit();
            this.numRsrpGoodGate = new DevExpress.XtraEditors.SpinEdit();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.numIdealCoverRadFactor = new DevExpress.XtraEditors.SpinEdit();
            this.numIdealCoverBtsCount = new DevExpress.XtraEditors.SpinEdit();
            this.label12 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            this.groupBox7.SuspendLayout();
            this.groupBox6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.checkedListBoxControlReason)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpWeakGate.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numLteBtsDisGate.Properties)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMinSecondsMainLowerNear.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpMainLowerNear.Properties)).BeginInit();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numLastSecondsBeforeWeak.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpGoodGate.Properties)).BeginInit();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numIdealCoverRadFactor.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numIdealCoverBtsCount.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.groupBox7);
            this.groupControl1.Controls.Add(this.groupBox6);
            this.groupControl1.Controls.Add(this.simpleButtonDown);
            this.groupControl1.Controls.Add(this.simpleButtonUp);
            this.groupControl1.Controls.Add(this.checkedListBoxControlReason);
            this.groupControl1.Controls.Add(this.simpleButtonCancel);
            this.groupControl1.Controls.Add(this.simpleButtonOK);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(586, 249);
            this.groupControl1.TabIndex = 1;
            this.groupControl1.Text = "分析顺序";
            // 
            // groupBox7
            // 
            this.groupBox7.Controls.Add(this.tbxSuggest);
            this.groupBox7.Location = new System.Drawing.Point(236, 131);
            this.groupBox7.Name = "groupBox7";
            this.groupBox7.Size = new System.Drawing.Size(338, 76);
            this.groupBox7.TabIndex = 7;
            this.groupBox7.TabStop = false;
            this.groupBox7.Text = "优化建议";
            // 
            // tbxSuggest
            // 
            this.tbxSuggest.Location = new System.Drawing.Point(13, 20);
            this.tbxSuggest.Multiline = true;
            this.tbxSuggest.Name = "tbxSuggest";
            this.tbxSuggest.ReadOnly = true;
            this.tbxSuggest.Size = new System.Drawing.Size(312, 44);
            this.tbxSuggest.TabIndex = 5;
            // 
            // groupBox6
            // 
            this.groupBox6.Controls.Add(this.tbxDescription);
            this.groupBox6.Location = new System.Drawing.Point(236, 38);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Size = new System.Drawing.Size(338, 87);
            this.groupBox6.TabIndex = 6;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "分析场景说明";
            // 
            // tbxDescription
            // 
            this.tbxDescription.Location = new System.Drawing.Point(13, 20);
            this.tbxDescription.Multiline = true;
            this.tbxDescription.Name = "tbxDescription";
            this.tbxDescription.ReadOnly = true;
            this.tbxDescription.Size = new System.Drawing.Size(312, 56);
            this.tbxDescription.TabIndex = 5;
            // 
            // simpleButtonDown
            // 
            this.simpleButtonDown.Appearance.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.simpleButtonDown.Appearance.Options.UseFont = true;
            this.simpleButtonDown.Location = new System.Drawing.Point(155, 98);
            this.simpleButtonDown.Name = "simpleButtonDown";
            this.simpleButtonDown.Size = new System.Drawing.Size(64, 27);
            this.simpleButtonDown.TabIndex = 4;
            this.simpleButtonDown.Text = "↓";
            this.simpleButtonDown.Click += new System.EventHandler(this.simpleButtonDown_Click);
            // 
            // simpleButtonUp
            // 
            this.simpleButtonUp.Appearance.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.simpleButtonUp.Appearance.Options.UseFont = true;
            this.simpleButtonUp.Location = new System.Drawing.Point(155, 59);
            this.simpleButtonUp.Name = "simpleButtonUp";
            this.simpleButtonUp.Size = new System.Drawing.Size(64, 27);
            this.simpleButtonUp.TabIndex = 4;
            this.simpleButtonUp.Text = "↑";
            this.simpleButtonUp.Click += new System.EventHandler(this.simpleButtonUp_Click);
            // 
            // checkedListBoxControlReason
            // 
            this.checkedListBoxControlReason.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkedListBoxControlReason.Appearance.Options.UseFont = true;
            this.checkedListBoxControlReason.Location = new System.Drawing.Point(24, 38);
            this.checkedListBoxControlReason.Name = "checkedListBoxControlReason";
            this.checkedListBoxControlReason.Size = new System.Drawing.Size(112, 192);
            this.checkedListBoxControlReason.TabIndex = 3;
            this.checkedListBoxControlReason.SelectedIndexChanged += new System.EventHandler(this.checkedListBoxControlReason_SelectedIndexChanged);
            // 
            // simpleButtonCancel
            // 
            this.simpleButtonCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButtonCancel.Appearance.Options.UseFont = true;
            this.simpleButtonCancel.Location = new System.Drawing.Point(474, 213);
            this.simpleButtonCancel.Name = "simpleButtonCancel";
            this.simpleButtonCancel.Size = new System.Drawing.Size(87, 27);
            this.simpleButtonCancel.TabIndex = 2;
            this.simpleButtonCancel.Text = "取消";
            this.simpleButtonCancel.Click += new System.EventHandler(this.simpleButtonCancel_Click);
            // 
            // simpleButtonOK
            // 
            this.simpleButtonOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.simpleButtonOK.Appearance.Options.UseFont = true;
            this.simpleButtonOK.Location = new System.Drawing.Point(357, 213);
            this.simpleButtonOK.Name = "simpleButtonOK";
            this.simpleButtonOK.Size = new System.Drawing.Size(87, 27);
            this.simpleButtonOK.TabIndex = 2;
            this.simpleButtonOK.Text = "确定";
            this.simpleButtonOK.Click += new System.EventHandler(this.simpleButtonOK_Click);
            // 
            // splitContainerControl1
            // 
            this.splitContainerControl1.CollapsePanel = DevExpress.XtraEditors.SplitCollapsePanel.Panel1;
            this.splitContainerControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl1.Horizontal = false;
            this.splitContainerControl1.Location = new System.Drawing.Point(0, 0);
            this.splitContainerControl1.Name = "splitContainerControl1";
            this.splitContainerControl1.Panel1.Controls.Add(this.numRsrpWeakGate);
            this.splitContainerControl1.Panel1.Controls.Add(this.label8);
            this.splitContainerControl1.Panel1.Controls.Add(this.label9);
            this.splitContainerControl1.Panel1.Controls.Add(this.groupBox1);
            this.splitContainerControl1.Panel1.Controls.Add(this.groupBox2);
            this.splitContainerControl1.Panel1.Controls.Add(this.groupBox3);
            this.splitContainerControl1.Panel1.Controls.Add(this.groupBox4);
            this.splitContainerControl1.Panel1.Text = "Panel1";
            this.splitContainerControl1.Panel2.Controls.Add(this.groupControl1);
            this.splitContainerControl1.Panel2.Text = "Panel2";
            this.splitContainerControl1.Size = new System.Drawing.Size(586, 582);
            this.splitContainerControl1.SplitterPosition = 327;
            this.splitContainerControl1.TabIndex = 3;
            this.splitContainerControl1.Text = "splitContainerControl1";
            // 
            // numRsrpWeakGate
            // 
            this.numRsrpWeakGate.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numRsrpWeakGate.Location = new System.Drawing.Point(130, 16);
            this.numRsrpWeakGate.Name = "numRsrpWeakGate";
            this.numRsrpWeakGate.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numRsrpWeakGate.Properties.Appearance.Options.UseFont = true;
            this.numRsrpWeakGate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRsrpWeakGate.Properties.Mask.EditMask = "f";
            this.numRsrpWeakGate.Properties.MaxValue = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numRsrpWeakGate.Properties.MinValue = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numRsrpWeakGate.Size = new System.Drawing.Size(70, 20);
            this.numRsrpWeakGate.TabIndex = 35;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label8.Location = new System.Drawing.Point(33, 19);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(95, 12);
            this.label8.TabIndex = 33;
            this.label8.Text = "弱覆盖点场强 ≤";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label9.Location = new System.Drawing.Point(205, 19);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(23, 12);
            this.label9.TabIndex = 32;
            this.label9.Text = "dBm";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.numLteBtsDisGate);
            this.groupBox1.Controls.Add(this.label11);
            this.groupBox1.Location = new System.Drawing.Point(35, 51);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(480, 55);
            this.groupBox1.TabIndex = 30;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "缺少规划站";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(118, 23);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(95, 12);
            this.label1.TabIndex = 27;
            this.label1.Text = "米内没有LTE基站";
            // 
            // numLteBtsDisGate
            // 
            this.numLteBtsDisGate.EditValue = new decimal(new int[] {
            800,
            0,
            0,
            0});
            this.numLteBtsDisGate.Location = new System.Drawing.Point(54, 20);
            this.numLteBtsDisGate.Name = "numLteBtsDisGate";
            this.numLteBtsDisGate.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numLteBtsDisGate.Properties.Appearance.Options.UseFont = true;
            this.numLteBtsDisGate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numLteBtsDisGate.Properties.Mask.EditMask = "f0";
            this.numLteBtsDisGate.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numLteBtsDisGate.Size = new System.Drawing.Size(58, 20);
            this.numLteBtsDisGate.TabIndex = 26;
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(19, 23);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(29, 12);
            this.label11.TabIndex = 25;
            this.label11.Text = "距离";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.label14);
            this.groupBox2.Controls.Add(this.numMinSecondsMainLowerNear);
            this.groupBox2.Controls.Add(this.labelControl18);
            this.groupBox2.Controls.Add(this.numRsrpMainLowerNear);
            this.groupBox2.Controls.Add(this.labelControl38);
            this.groupBox2.Location = new System.Drawing.Point(35, 120);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(480, 55);
            this.groupBox2.TabIndex = 0;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "切换不合理";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label14.Location = new System.Drawing.Point(404, 24);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(23, 12);
            this.label14.TabIndex = 33;
            this.label14.Text = "dBm";
            // 
            // numMinSecondsMainLowerNear
            // 
            this.numMinSecondsMainLowerNear.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.numMinSecondsMainLowerNear.Location = new System.Drawing.Point(73, 20);
            this.numMinSecondsMainLowerNear.Name = "numMinSecondsMainLowerNear";
            this.numMinSecondsMainLowerNear.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numMinSecondsMainLowerNear.Properties.Appearance.Options.UseFont = true;
            this.numMinSecondsMainLowerNear.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numMinSecondsMainLowerNear.Properties.Mask.EditMask = "f0";
            this.numMinSecondsMainLowerNear.Properties.MaxValue = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numMinSecondsMainLowerNear.Size = new System.Drawing.Size(60, 20);
            this.numMinSecondsMainLowerNear.TabIndex = 9;
            // 
            // labelControl18
            // 
            this.labelControl18.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl18.Appearance.Options.UseFont = true;
            this.labelControl18.Location = new System.Drawing.Point(19, 24);
            this.labelControl18.Name = "labelControl18";
            this.labelControl18.Size = new System.Drawing.Size(48, 12);
            this.labelControl18.TabIndex = 8;
            this.labelControl18.Text = "弱覆盖前";
            // 
            // numRsrpMainLowerNear
            // 
            this.numRsrpMainLowerNear.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.numRsrpMainLowerNear.Location = new System.Drawing.Point(343, 20);
            this.numRsrpMainLowerNear.Name = "numRsrpMainLowerNear";
            this.numRsrpMainLowerNear.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numRsrpMainLowerNear.Properties.Appearance.Options.UseFont = true;
            this.numRsrpMainLowerNear.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRsrpMainLowerNear.Properties.Mask.EditMask = "f";
            this.numRsrpMainLowerNear.Properties.MaxValue = new decimal(new int[] {
            166,
            0,
            0,
            0});
            this.numRsrpMainLowerNear.Size = new System.Drawing.Size(55, 20);
            this.numRsrpMainLowerNear.TabIndex = 4;
            // 
            // labelControl38
            // 
            this.labelControl38.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl38.Appearance.Options.UseFont = true;
            this.labelControl38.Location = new System.Drawing.Point(139, 23);
            this.labelControl38.Name = "labelControl38";
            this.labelControl38.Size = new System.Drawing.Size(198, 12);
            this.labelControl38.TabIndex = 0;
            this.labelControl38.Text = "秒内邻服最大场强与主服场强的差 ≥";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.labelControl8);
            this.groupBox3.Controls.Add(this.labelControl9);
            this.groupBox3.Controls.Add(this.labelControl7);
            this.groupBox3.Controls.Add(this.numLastSecondsBeforeWeak);
            this.groupBox3.Controls.Add(this.numRsrpGoodGate);
            this.groupBox3.Location = new System.Drawing.Point(35, 190);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(480, 55);
            this.groupBox3.TabIndex = 3;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "覆盖不稳定";
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl8.Appearance.Options.UseFont = true;
            this.labelControl8.Location = new System.Drawing.Point(141, 23);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(66, 12);
            this.labelControl8.TabIndex = 2;
            this.labelControl8.Text = "秒内场强 ≥";
            // 
            // labelControl9
            // 
            this.labelControl9.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl9.Appearance.Options.UseFont = true;
            this.labelControl9.Location = new System.Drawing.Point(22, 23);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(48, 12);
            this.labelControl9.TabIndex = 1;
            this.labelControl9.Text = "弱覆盖前";
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl7.Appearance.Options.UseFont = true;
            this.labelControl7.Location = new System.Drawing.Point(289, 23);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(12, 12);
            this.labelControl7.TabIndex = 2;
            this.labelControl7.Text = "dB";
            // 
            // numLastSecondsBeforeWeak
            // 
            this.numLastSecondsBeforeWeak.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numLastSecondsBeforeWeak.Location = new System.Drawing.Point(76, 20);
            this.numLastSecondsBeforeWeak.Name = "numLastSecondsBeforeWeak";
            this.numLastSecondsBeforeWeak.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numLastSecondsBeforeWeak.Properties.Appearance.Options.UseFont = true;
            this.numLastSecondsBeforeWeak.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numLastSecondsBeforeWeak.Properties.Mask.EditMask = "f0";
            this.numLastSecondsBeforeWeak.Properties.MaxValue = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numLastSecondsBeforeWeak.Size = new System.Drawing.Size(59, 20);
            this.numLastSecondsBeforeWeak.TabIndex = 0;
            // 
            // numRsrpGoodGate
            // 
            this.numRsrpGoodGate.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numRsrpGoodGate.Location = new System.Drawing.Point(213, 19);
            this.numRsrpGoodGate.Name = "numRsrpGoodGate";
            this.numRsrpGoodGate.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numRsrpGoodGate.Properties.Appearance.Options.UseFont = true;
            this.numRsrpGoodGate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numRsrpGoodGate.Properties.Mask.EditMask = "f";
            this.numRsrpGoodGate.Properties.MaxValue = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numRsrpGoodGate.Properties.MinValue = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numRsrpGoodGate.Size = new System.Drawing.Size(70, 20);
            this.numRsrpGoodGate.TabIndex = 0;
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.numIdealCoverRadFactor);
            this.groupBox4.Controls.Add(this.numIdealCoverBtsCount);
            this.groupBox4.Controls.Add(this.label12);
            this.groupBox4.Controls.Add(this.label4);
            this.groupBox4.Location = new System.Drawing.Point(35, 260);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(480, 56);
            this.groupBox4.TabIndex = 2;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "过覆盖";
            // 
            // numIdealCoverRadFactor
            // 
            this.numIdealCoverRadFactor.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numIdealCoverRadFactor.Location = new System.Drawing.Point(394, 23);
            this.numIdealCoverRadFactor.Name = "numIdealCoverRadFactor";
            this.numIdealCoverRadFactor.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numIdealCoverRadFactor.Properties.Appearance.Options.UseFont = true;
            this.numIdealCoverRadFactor.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numIdealCoverRadFactor.Properties.Mask.EditMask = "f";
            this.numIdealCoverRadFactor.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numIdealCoverRadFactor.Size = new System.Drawing.Size(59, 20);
            this.numIdealCoverRadFactor.TabIndex = 24;
            // 
            // numIdealCoverBtsCount
            // 
            this.numIdealCoverBtsCount.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numIdealCoverBtsCount.Location = new System.Drawing.Point(134, 23);
            this.numIdealCoverBtsCount.Name = "numIdealCoverBtsCount";
            this.numIdealCoverBtsCount.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numIdealCoverBtsCount.Properties.Appearance.Options.UseFont = true;
            this.numIdealCoverBtsCount.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numIdealCoverBtsCount.Properties.Mask.EditMask = "f0";
            this.numIdealCoverBtsCount.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numIdealCoverBtsCount.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numIdealCoverBtsCount.Size = new System.Drawing.Size(70, 20);
            this.numIdealCoverBtsCount.TabIndex = 22;
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label12.Location = new System.Drawing.Point(15, 26);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(113, 12);
            this.label12.TabIndex = 20;
            this.label12.Text = "理想覆盖参考基站数";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.Location = new System.Drawing.Point(287, 26);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(101, 12);
            this.label4.TabIndex = 18;
            this.label4.Text = "理想覆盖半径系数";
            // 
            // VoLteWeakCoverAnaSetForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(586, 582);
            this.Controls.Add(this.splitContainerControl1);
            this.Name = "VoLteWeakCoverAnaSetForm";
            this.Text = "弱覆盖原因条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupBox7.ResumeLayout(false);
            this.groupBox7.PerformLayout();
            this.groupBox6.ResumeLayout(false);
            this.groupBox6.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.checkedListBoxControlReason)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpWeakGate.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numLteBtsDisGate.Properties)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMinSecondsMainLowerNear.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpMainLowerNear.Properties)).EndInit();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numLastSecondsBeforeWeak.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpGoodGate.Properties)).EndInit();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numIdealCoverRadFactor.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numIdealCoverBtsCount.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private System.Windows.Forms.GroupBox groupBox3;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.SpinEdit numRsrpGoodGate;
        private DevExpress.XtraEditors.SpinEdit numLastSecondsBeforeWeak;
        private System.Windows.Forms.GroupBox groupBox2;
        private DevExpress.XtraEditors.SpinEdit numRsrpMainLowerNear;
        private DevExpress.XtraEditors.LabelControl labelControl38;
        private DevExpress.XtraEditors.SimpleButton simpleButtonCancel;
        private DevExpress.XtraEditors.SimpleButton simpleButtonOK;
        private DevExpress.XtraEditors.CheckedListBoxControl checkedListBoxControlReason;
        private DevExpress.XtraEditors.SimpleButton simpleButtonUp;
        private DevExpress.XtraEditors.SimpleButton simpleButtonDown;
        private System.Windows.Forms.TextBox tbxDescription;
        private System.Windows.Forms.GroupBox groupBox6;
        private System.Windows.Forms.GroupBox groupBox7;
        private System.Windows.Forms.TextBox tbxSuggest;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label1;
        private DevExpress.XtraEditors.SpinEdit numLteBtsDisGate;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label9;
        private DevExpress.XtraEditors.SpinEdit numMinSecondsMainLowerNear;
        private DevExpress.XtraEditors.LabelControl labelControl18;
        private System.Windows.Forms.Label label14;
        private DevExpress.XtraEditors.SpinEdit numRsrpWeakGate;
        private System.Windows.Forms.GroupBox groupBox4;
        private DevExpress.XtraEditors.SpinEdit numIdealCoverBtsCount;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label4;
        private DevExpress.XtraEditors.SpinEdit numIdealCoverRadFactor;

    }
}