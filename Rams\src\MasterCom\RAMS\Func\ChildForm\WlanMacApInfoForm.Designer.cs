﻿namespace MasterCom.RAMS.Func
{
    partial class WlanMacApInfoForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(WlanMacApInfoForm));
            this.listViewMac = new BrightIdeasSoftware.TreeListView();
            this.columnHeaderSSID_Mac = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderBSSID_Mac = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderChannel_Mac = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderFreq_Mac = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderRSSI_Mac = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderCI_Mac = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderSFI_Mac = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderNFI_Mac = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderFirstSeen_Mac = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderLastSeen_Mac = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderActiveTimes_Mac = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderRx_Mac = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderTx_Mac = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderBand_Mac = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderABGN_Mac = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderType_Mac = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderEncrytion_Mac = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderSecurity_Mac = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderBridge_Mac = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderDCFPCF_Mac = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderBeaconInterval_Mac = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderSupportRate_Mac = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderMaxRate_Mac = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderPreamble_Max = new BrightIdeasSoftware.OLVColumn();
            this.columnHeaderNoise_Mac = new BrightIdeasSoftware.OLVColumn();
            ((System.ComponentModel.ISupportInitialize)(this.listViewMac)).BeginInit();
            this.SuspendLayout();
            // 
            // listViewMac
            // 
            this.listViewMac.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeaderSSID_Mac,
            this.columnHeaderBSSID_Mac,
            this.columnHeaderChannel_Mac,
            this.columnHeaderFreq_Mac,
            this.columnHeaderRSSI_Mac,
            this.columnHeaderCI_Mac,
            this.columnHeaderSFI_Mac,
            this.columnHeaderNFI_Mac,
            this.columnHeaderFirstSeen_Mac,
            this.columnHeaderLastSeen_Mac,
            this.columnHeaderActiveTimes_Mac,
            this.columnHeaderRx_Mac,
            this.columnHeaderTx_Mac,
            this.columnHeaderBand_Mac,
            this.columnHeaderABGN_Mac,
            this.columnHeaderType_Mac,
            this.columnHeaderEncrytion_Mac,
            this.columnHeaderSecurity_Mac,
            this.columnHeaderBridge_Mac,
            this.columnHeaderDCFPCF_Mac,
            this.columnHeaderBeaconInterval_Mac,
            this.columnHeaderSupportRate_Mac,
            this.columnHeaderMaxRate_Mac,
            this.columnHeaderPreamble_Max,
            this.columnHeaderNoise_Mac});
            this.listViewMac.Dock = System.Windows.Forms.DockStyle.Fill;
            this.listViewMac.FullRowSelect = true;
            this.listViewMac.GridLines = true;
            this.listViewMac.Location = new System.Drawing.Point(0, 0);
            this.listViewMac.Name = "listViewMac";
            this.listViewMac.OwnerDraw = true;
            this.listViewMac.ShowGroups = false;
            this.listViewMac.Size = new System.Drawing.Size(565, 192);
            this.listViewMac.TabIndex = 2;
            this.listViewMac.UseCompatibleStateImageBehavior = false;
            this.listViewMac.View = System.Windows.Forms.View.Details;
            this.listViewMac.VirtualMode = true;
            this.listViewMac.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewMac_MouseDoubleClick);
            // 
            // columnHeaderSSID_Mac
            // 
            this.columnHeaderSSID_Mac.HeaderFont = null;
            this.columnHeaderSSID_Mac.Text = "SSID";
            // 
            // columnHeaderBSSID_Mac
            // 
            this.columnHeaderBSSID_Mac.HeaderFont = null;
            this.columnHeaderBSSID_Mac.Text = "BSSID";
            // 
            // columnHeaderChannel_Mac
            // 
            this.columnHeaderChannel_Mac.HeaderFont = null;
            this.columnHeaderChannel_Mac.Text = "Channel";
            // 
            // columnHeaderFreq_Mac
            // 
            this.columnHeaderFreq_Mac.HeaderFont = null;
            this.columnHeaderFreq_Mac.Text = "Freqecncy";
            this.columnHeaderFreq_Mac.Width = 70;
            // 
            // columnHeaderRSSI_Mac
            // 
            this.columnHeaderRSSI_Mac.HeaderFont = null;
            this.columnHeaderRSSI_Mac.Text = "RSSI";
            // 
            // columnHeaderCI_Mac
            // 
            this.columnHeaderCI_Mac.HeaderFont = null;
            this.columnHeaderCI_Mac.Text = "C/I";
            // 
            // columnHeaderSFI_Mac
            // 
            this.columnHeaderSFI_Mac.HeaderFont = null;
            this.columnHeaderSFI_Mac.Text = "SFI";
            // 
            // columnHeaderNFI_Mac
            // 
            this.columnHeaderNFI_Mac.HeaderFont = null;
            this.columnHeaderNFI_Mac.Text = "NFI";
            // 
            // columnHeaderFirstSeen_Mac
            // 
            this.columnHeaderFirstSeen_Mac.HeaderFont = null;
            this.columnHeaderFirstSeen_Mac.Text = "First Seen";
            this.columnHeaderFirstSeen_Mac.Width = 76;
            // 
            // columnHeaderLastSeen_Mac
            // 
            this.columnHeaderLastSeen_Mac.HeaderFont = null;
            this.columnHeaderLastSeen_Mac.Text = "Last Seen";
            this.columnHeaderLastSeen_Mac.Width = 69;
            // 
            // columnHeaderActiveTimes_Mac
            // 
            this.columnHeaderActiveTimes_Mac.HeaderFont = null;
            this.columnHeaderActiveTimes_Mac.Text = "Active Times(s)";
            this.columnHeaderActiveTimes_Mac.Width = 108;
            // 
            // columnHeaderRx_Mac
            // 
            this.columnHeaderRx_Mac.HeaderFont = null;
            this.columnHeaderRx_Mac.Text = "Rx(kb/s)";
            this.columnHeaderRx_Mac.Width = 63;
            // 
            // columnHeaderTx_Mac
            // 
            this.columnHeaderTx_Mac.HeaderFont = null;
            this.columnHeaderTx_Mac.Text = "Tx(kb/s)";
            this.columnHeaderTx_Mac.Width = 72;
            // 
            // columnHeaderBand_Mac
            // 
            this.columnHeaderBand_Mac.HeaderFont = null;
            this.columnHeaderBand_Mac.Text = "Band(%)";
            // 
            // columnHeaderABGN_Mac
            // 
            this.columnHeaderABGN_Mac.HeaderFont = null;
            this.columnHeaderABGN_Mac.Text = "A\\B\\G\\N";
            // 
            // columnHeaderType_Mac
            // 
            this.columnHeaderType_Mac.HeaderFont = null;
            this.columnHeaderType_Mac.Text = "Type";
            // 
            // columnHeaderEncrytion_Mac
            // 
            this.columnHeaderEncrytion_Mac.HeaderFont = null;
            this.columnHeaderEncrytion_Mac.Text = "Encrytion";
            this.columnHeaderEncrytion_Mac.Width = 69;
            // 
            // columnHeaderSecurity_Mac
            // 
            this.columnHeaderSecurity_Mac.HeaderFont = null;
            this.columnHeaderSecurity_Mac.Text = "Security";
            this.columnHeaderSecurity_Mac.Width = 65;
            // 
            // columnHeaderBridge_Mac
            // 
            this.columnHeaderBridge_Mac.HeaderFont = null;
            this.columnHeaderBridge_Mac.Text = "Bridge";
            // 
            // columnHeaderDCFPCF_Mac
            // 
            this.columnHeaderDCFPCF_Mac.HeaderFont = null;
            this.columnHeaderDCFPCF_Mac.Text = "DCF\\PCF";
            // 
            // columnHeaderBeaconInterval_Mac
            // 
            this.columnHeaderBeaconInterval_Mac.HeaderFont = null;
            this.columnHeaderBeaconInterval_Mac.Text = "Beacon Interval(ms)";
            this.columnHeaderBeaconInterval_Mac.Width = 130;
            // 
            // columnHeaderSupportRate_Mac
            // 
            this.columnHeaderSupportRate_Mac.HeaderFont = null;
            this.columnHeaderSupportRate_Mac.Text = "Support Rate";
            this.columnHeaderSupportRate_Mac.Width = 89;
            // 
            // columnHeaderMaxRate_Mac
            // 
            this.columnHeaderMaxRate_Mac.HeaderFont = null;
            this.columnHeaderMaxRate_Mac.Text = "MaxRate(Mb)";
            this.columnHeaderMaxRate_Mac.Width = 83;
            // 
            // columnHeaderPreamble_Max
            // 
            this.columnHeaderPreamble_Max.HeaderFont = null;
            this.columnHeaderPreamble_Max.Text = "Preamble";
            this.columnHeaderPreamble_Max.Width = 66;
            // 
            // columnHeaderNoise_Mac
            // 
            this.columnHeaderNoise_Mac.HeaderFont = null;
            this.columnHeaderNoise_Mac.Text = "Noise";
            // 
            // WlanMacApInfoForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("WlanMacApInfoForm.Appearance.Image")));
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(565, 192);
            this.Controls.Add(this.listViewMac);
            this.Name = "WlanMacApInfoForm";
            this.Text = "MACApInfo";
            ((System.ComponentModel.ISupportInitialize)(this.listViewMac)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView listViewMac;
        private BrightIdeasSoftware.OLVColumn columnHeaderBSSID_Mac;
        private BrightIdeasSoftware.OLVColumn columnHeaderSSID_Mac;
        private BrightIdeasSoftware.OLVColumn columnHeaderChannel_Mac;
        private BrightIdeasSoftware.OLVColumn columnHeaderFreq_Mac;
        private BrightIdeasSoftware.OLVColumn columnHeaderRSSI_Mac;
        private BrightIdeasSoftware.OLVColumn columnHeaderCI_Mac;
        private BrightIdeasSoftware.OLVColumn columnHeaderSFI_Mac;
        private BrightIdeasSoftware.OLVColumn columnHeaderNFI_Mac;
        private BrightIdeasSoftware.OLVColumn columnHeaderFirstSeen_Mac;
        private BrightIdeasSoftware.OLVColumn columnHeaderLastSeen_Mac;
        private BrightIdeasSoftware.OLVColumn columnHeaderActiveTimes_Mac;
        private BrightIdeasSoftware.OLVColumn columnHeaderRx_Mac;
        private BrightIdeasSoftware.OLVColumn columnHeaderTx_Mac;
        private BrightIdeasSoftware.OLVColumn columnHeaderBand_Mac;
        private BrightIdeasSoftware.OLVColumn columnHeaderABGN_Mac;
        private BrightIdeasSoftware.OLVColumn columnHeaderType_Mac;
        private BrightIdeasSoftware.OLVColumn columnHeaderEncrytion_Mac;
        private BrightIdeasSoftware.OLVColumn columnHeaderSecurity_Mac;
        private BrightIdeasSoftware.OLVColumn columnHeaderBridge_Mac;
        private BrightIdeasSoftware.OLVColumn columnHeaderDCFPCF_Mac;
        private BrightIdeasSoftware.OLVColumn columnHeaderBeaconInterval_Mac;
        private BrightIdeasSoftware.OLVColumn columnHeaderSupportRate_Mac;
        private BrightIdeasSoftware.OLVColumn columnHeaderMaxRate_Mac;
        private BrightIdeasSoftware.OLVColumn columnHeaderPreamble_Max;
        private BrightIdeasSoftware.OLVColumn columnHeaderNoise_Mac;
    }
}