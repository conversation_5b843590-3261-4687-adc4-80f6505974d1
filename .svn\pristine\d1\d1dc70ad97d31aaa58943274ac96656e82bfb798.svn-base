﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTCellCoverLapSetForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.numFilter = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.cbxSampleCount = new System.Windows.Forms.CheckBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.cbxFreqBand = new DevExpress.XtraEditors.ComboBoxEdit();
            this.chkFreqBand = new DevExpress.XtraEditors.CheckEdit();
            this.label11 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.numPercent = new System.Windows.Forms.NumericUpDown();
            this.numSampleCount = new System.Windows.Forms.NumericUpDown();
            this.lblAdjFreq2 = new System.Windows.Forms.Label();
            this.chkDistance = new System.Windows.Forms.CheckBox();
            this.numAdjFreq = new System.Windows.Forms.NumericUpDown();
            this.lblAdjFreq = new System.Windows.Forms.Label();
            this.numDistanceMax = new System.Windows.Forms.NumericUpDown();
            this.numCoFreq = new System.Windows.Forms.NumericUpDown();
            this.numDistanceMin = new System.Windows.Forms.NumericUpDown();
            this.lblCoFreq = new System.Windows.Forms.Label();
            this.lblCoFreq2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.cbxPercent = new System.Windows.Forms.CheckBox();
            this.numDisFactor = new System.Windows.Forms.NumericUpDown();
            this.label4 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.numNearestCellCount = new System.Windows.Forms.NumericUpDown();
            this.label13 = new System.Windows.Forms.Label();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.chbNearBTS = new System.Windows.Forms.CheckBox();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.label7 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.numFilter)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxFreqBand.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkFreqBand.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPercent)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numAdjFreq)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistanceMax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCoFreq)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistanceMin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDisFactor)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numNearestCellCount)).BeginInit();
            this.groupBox2.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.SuspendLayout();
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.Location = new System.Drawing.Point(177, 368);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 0;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.Location = new System.Drawing.Point(257, 368);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 0;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // numFilter
            // 
            this.numFilter.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numFilter.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numFilter.Location = new System.Drawing.Point(125, 96);
            this.numFilter.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            -2147483648});
            this.numFilter.Minimum = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.numFilter.Name = "numFilter";
            this.numFilter.Size = new System.Drawing.Size(50, 21);
            this.numFilter.TabIndex = 2;
            this.numFilter.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numFilter.Value = new decimal(new int[] {
            90,
            0,
            0,
            -2147483648});
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label1.Location = new System.Drawing.Point(181, 101);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(23, 12);
            this.label1.TabIndex = 3;
            this.label1.Text = "dBm";
            // 
            // cbxSampleCount
            // 
            this.cbxSampleCount.AutoSize = true;
            this.cbxSampleCount.Location = new System.Drawing.Point(25, 21);
            this.cbxSampleCount.Name = "cbxSampleCount";
            this.cbxSampleCount.Size = new System.Drawing.Size(96, 16);
            this.cbxSampleCount.TabIndex = 1;
            this.cbxSampleCount.Text = "采样点数量≥";
            this.cbxSampleCount.UseVisualStyleBackColor = true;
            this.cbxSampleCount.CheckedChanged += new System.EventHandler(this.cbxSampleCount_CheckedChanged);
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.cbxFreqBand);
            this.groupBox1.Controls.Add(this.chkFreqBand);
            this.groupBox1.Controls.Add(this.label11);
            this.groupBox1.Controls.Add(this.label10);
            this.groupBox1.Controls.Add(this.numPercent);
            this.groupBox1.Controls.Add(this.numSampleCount);
            this.groupBox1.Controls.Add(this.lblAdjFreq2);
            this.groupBox1.Controls.Add(this.chkDistance);
            this.groupBox1.Controls.Add(this.numAdjFreq);
            this.groupBox1.Controls.Add(this.lblAdjFreq);
            this.groupBox1.Controls.Add(this.numDistanceMax);
            this.groupBox1.Controls.Add(this.numCoFreq);
            this.groupBox1.Controls.Add(this.numDistanceMin);
            this.groupBox1.Controls.Add(this.lblCoFreq);
            this.groupBox1.Controls.Add(this.lblCoFreq2);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.cbxPercent);
            this.groupBox1.Controls.Add(this.cbxSampleCount);
            this.groupBox1.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.groupBox1.Location = new System.Drawing.Point(12, 174);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(320, 182);
            this.groupBox1.TabIndex = 4;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "结果过滤";
            // 
            // cbxFreqBand
            // 
            this.cbxFreqBand.Anchor = System.Windows.Forms.AnchorStyles.Bottom;
            this.cbxFreqBand.EditValue = "D";
            this.cbxFreqBand.Enabled = false;
            this.cbxFreqBand.Location = new System.Drawing.Point(125, 151);
            this.cbxFreqBand.Name = "cbxFreqBand";
            this.cbxFreqBand.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbxFreqBand.Properties.Items.AddRange(new object[] {
            "D",
            "F"});
            this.cbxFreqBand.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbxFreqBand.Size = new System.Drawing.Size(50, 21);
            this.cbxFreqBand.TabIndex = 16;
            // 
            // chkFreqBand
            // 
            this.chkFreqBand.Anchor = System.Windows.Forms.AnchorStyles.Bottom;
            this.chkFreqBand.Location = new System.Drawing.Point(75, 153);
            this.chkFreqBand.Name = "chkFreqBand";
            this.chkFreqBand.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkFreqBand.Properties.Appearance.Options.UseFont = true;
            this.chkFreqBand.Properties.Caption = "频段";
            this.chkFreqBand.Size = new System.Drawing.Size(44, 19);
            this.chkFreqBand.TabIndex = 15;
            this.chkFreqBand.CheckedChanged += new System.EventHandler(this.chkFreqBand_CheckedChanged);
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(271, 75);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(17, 12);
            this.label11.TabIndex = 13;
            this.label11.Text = "米";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(181, 25);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(17, 12);
            this.label10.TabIndex = 12;
            this.label10.Text = "个";
            // 
            // numPercent
            // 
            this.numPercent.Enabled = false;
            this.numPercent.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numPercent.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numPercent.Location = new System.Drawing.Point(125, 43);
            this.numPercent.Maximum = new decimal(new int[] {
            999999999,
            0,
            0,
            0});
            this.numPercent.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numPercent.Name = "numPercent";
            this.numPercent.Size = new System.Drawing.Size(50, 21);
            this.numPercent.TabIndex = 2;
            this.numPercent.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numPercent.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // numSampleCount
            // 
            this.numSampleCount.Enabled = false;
            this.numSampleCount.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numSampleCount.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numSampleCount.Location = new System.Drawing.Point(125, 16);
            this.numSampleCount.Maximum = new decimal(new int[] {
            999999999,
            0,
            0,
            0});
            this.numSampleCount.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numSampleCount.Name = "numSampleCount";
            this.numSampleCount.Size = new System.Drawing.Size(50, 21);
            this.numSampleCount.TabIndex = 2;
            this.numSampleCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numSampleCount.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // lblAdjFreq2
            // 
            this.lblAdjFreq2.AutoSize = true;
            this.lblAdjFreq2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblAdjFreq2.Location = new System.Drawing.Point(181, 129);
            this.lblAdjFreq2.Name = "lblAdjFreq2";
            this.lblAdjFreq2.Size = new System.Drawing.Size(17, 12);
            this.lblAdjFreq2.TabIndex = 11;
            this.lblAdjFreq2.Text = "dB";
            // 
            // chkDistance
            // 
            this.chkDistance.AutoSize = true;
            this.chkDistance.Location = new System.Drawing.Point(35, 71);
            this.chkDistance.Name = "chkDistance";
            this.chkDistance.Size = new System.Drawing.Size(84, 16);
            this.chkDistance.TabIndex = 9;
            this.chkDistance.Text = "过覆盖距离";
            this.chkDistance.UseVisualStyleBackColor = true;
            this.chkDistance.CheckedChanged += new System.EventHandler(this.chkDistance_CheckedChanged);
            // 
            // numAdjFreq
            // 
            this.numAdjFreq.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numAdjFreq.Location = new System.Drawing.Point(125, 124);
            this.numAdjFreq.Maximum = new decimal(new int[] {
            120,
            0,
            0,
            0});
            this.numAdjFreq.Minimum = new decimal(new int[] {
            120,
            0,
            0,
            -2147483648});
            this.numAdjFreq.Name = "numAdjFreq";
            this.numAdjFreq.Size = new System.Drawing.Size(50, 21);
            this.numAdjFreq.TabIndex = 8;
            this.numAdjFreq.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numAdjFreq.Value = new decimal(new int[] {
            9,
            0,
            0,
            -2147483648});
            // 
            // lblAdjFreq
            // 
            this.lblAdjFreq.AutoSize = true;
            this.lblAdjFreq.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblAdjFreq.Location = new System.Drawing.Point(42, 128);
            this.lblAdjFreq.Name = "lblAdjFreq";
            this.lblAdjFreq.Size = new System.Drawing.Size(77, 12);
            this.lblAdjFreq.TabIndex = 6;
            this.lblAdjFreq.Text = "邻频保护比≥";
            // 
            // numDistanceMax
            // 
            this.numDistanceMax.Increment = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numDistanceMax.Location = new System.Drawing.Point(215, 70);
            this.numDistanceMax.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numDistanceMax.Name = "numDistanceMax";
            this.numDistanceMax.Size = new System.Drawing.Size(50, 21);
            this.numDistanceMax.TabIndex = 8;
            this.numDistanceMax.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numDistanceMax.Value = new decimal(new int[] {
            3000,
            0,
            0,
            0});
            // 
            // numCoFreq
            // 
            this.numCoFreq.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numCoFreq.Location = new System.Drawing.Point(125, 97);
            this.numCoFreq.Maximum = new decimal(new int[] {
            120,
            0,
            0,
            0});
            this.numCoFreq.Name = "numCoFreq";
            this.numCoFreq.Size = new System.Drawing.Size(50, 21);
            this.numCoFreq.TabIndex = 9;
            this.numCoFreq.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numCoFreq.Value = new decimal(new int[] {
            9,
            0,
            0,
            0});
            // 
            // numDistanceMin
            // 
            this.numDistanceMin.Increment = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numDistanceMin.Location = new System.Drawing.Point(125, 70);
            this.numDistanceMin.Maximum = new decimal(new int[] {
            100000,
            0,
            0,
            0});
            this.numDistanceMin.Name = "numDistanceMin";
            this.numDistanceMin.Size = new System.Drawing.Size(50, 21);
            this.numDistanceMin.TabIndex = 7;
            this.numDistanceMin.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numDistanceMin.Value = new decimal(new int[] {
            300,
            0,
            0,
            0});
            // 
            // lblCoFreq
            // 
            this.lblCoFreq.AutoSize = true;
            this.lblCoFreq.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCoFreq.Location = new System.Drawing.Point(42, 103);
            this.lblCoFreq.Name = "lblCoFreq";
            this.lblCoFreq.Size = new System.Drawing.Size(77, 12);
            this.lblCoFreq.TabIndex = 7;
            this.lblCoFreq.Text = "同频保护比≥";
            // 
            // lblCoFreq2
            // 
            this.lblCoFreq2.AutoSize = true;
            this.lblCoFreq2.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblCoFreq2.Location = new System.Drawing.Point(181, 103);
            this.lblCoFreq2.Name = "lblCoFreq2";
            this.lblCoFreq2.Size = new System.Drawing.Size(17, 12);
            this.lblCoFreq2.TabIndex = 10;
            this.lblCoFreq2.Text = "dB";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(180, 75);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(29, 12);
            this.label3.TabIndex = 3;
            this.label3.Text = "米至";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(181, 48);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(11, 12);
            this.label2.TabIndex = 3;
            this.label2.Text = "%";
            // 
            // cbxPercent
            // 
            this.cbxPercent.AutoSize = true;
            this.cbxPercent.Location = new System.Drawing.Point(25, 44);
            this.cbxPercent.Name = "cbxPercent";
            this.cbxPercent.Size = new System.Drawing.Size(96, 16);
            this.cbxPercent.TabIndex = 1;
            this.cbxPercent.Text = "过覆盖比例≥";
            this.cbxPercent.UseVisualStyleBackColor = true;
            this.cbxPercent.CheckedChanged += new System.EventHandler(this.cbxPercent_CheckedChanged);
            // 
            // numDisFactor
            // 
            this.numDisFactor.DecimalPlaces = 1;
            this.numDisFactor.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numDisFactor.Increment = new decimal(new int[] {
            5,
            0,
            0,
            65536});
            this.numDisFactor.Location = new System.Drawing.Point(110, 39);
            this.numDisFactor.Maximum = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numDisFactor.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            65536});
            this.numDisFactor.Name = "numDisFactor";
            this.numDisFactor.Size = new System.Drawing.Size(50, 21);
            this.numDisFactor.TabIndex = 2;
            this.numDisFactor.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numDisFactor.Value = new decimal(new int[] {
            16,
            0,
            0,
            65536});
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.Location = new System.Drawing.Point(15, 43);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(89, 12);
            this.label4.TabIndex = 5;
            this.label4.Text = "实际覆盖半径≥";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label12.Location = new System.Drawing.Point(41, 17);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(65, 12);
            this.label12.TabIndex = 13;
            this.label12.Text = "参考基站数";
            // 
            // numNearestCellCount
            // 
            this.numNearestCellCount.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numNearestCellCount.Location = new System.Drawing.Point(110, 12);
            this.numNearestCellCount.Maximum = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.numNearestCellCount.Minimum = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.numNearestCellCount.Name = "numNearestCellCount";
            this.numNearestCellCount.Size = new System.Drawing.Size(50, 21);
            this.numNearestCellCount.TabIndex = 12;
            this.numNearestCellCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numNearestCellCount.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label13.Location = new System.Drawing.Point(44, 101);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(77, 12);
            this.label13.TabIndex = 14;
            this.label13.Text = "采样点场强≥";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.chbNearBTS);
            this.groupBox2.Controls.Add(this.groupBox3);
            this.groupBox2.Controls.Add(this.label13);
            this.groupBox2.Controls.Add(this.numFilter);
            this.groupBox2.Controls.Add(this.label1);
            this.groupBox2.Location = new System.Drawing.Point(12, 12);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(320, 156);
            this.groupBox2.TabIndex = 17;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "分析条件";
            // 
            // chbNearBTS
            // 
            this.chbNearBTS.AutoSize = true;
            this.chbNearBTS.Location = new System.Drawing.Point(46, 127);
            this.chbNearBTS.Name = "chbNearBTS";
            this.chbNearBTS.Size = new System.Drawing.Size(120, 16);
            this.chbNearBTS.TabIndex = 16;
            this.chbNearBTS.Text = "匹配邻区过覆盖点";
            this.chbNearBTS.UseVisualStyleBackColor = true;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.numNearestCellCount);
            this.groupBox3.Controls.Add(this.label7);
            this.groupBox3.Controls.Add(this.label12);
            this.groupBox3.Controls.Add(this.label5);
            this.groupBox3.Controls.Add(this.label4);
            this.groupBox3.Controls.Add(this.numDisFactor);
            this.groupBox3.Location = new System.Drawing.Point(15, 21);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(273, 69);
            this.groupBox3.TabIndex = 15;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "覆盖半径";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(166, 17);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(17, 12);
            this.label7.TabIndex = 12;
            this.label7.Text = "个";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label5.Location = new System.Drawing.Point(166, 43);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(89, 12);
            this.label5.TabIndex = 5;
            this.label5.Text = "倍理想覆盖半径";
            // 
            // ZTCellCoverLapSetForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(345, 402);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.groupBox2);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.Name = "ZTCellCoverLapSetForm";
            this.Text = "过覆盖分析条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.numFilter)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cbxFreqBand.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkFreqBand.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numPercent)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numSampleCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numAdjFreq)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistanceMax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCoFreq)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistanceMin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDisFactor)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numNearestCellCount)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.NumericUpDown numFilter;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.CheckBox cbxSampleCount;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.CheckBox cbxPercent;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.NumericUpDown numDisFactor;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.NumericUpDown numDistanceMax;
        private System.Windows.Forms.NumericUpDown numDistanceMin;
        private System.Windows.Forms.CheckBox chkDistance;
        private System.Windows.Forms.NumericUpDown numAdjFreq;
        private System.Windows.Forms.NumericUpDown numCoFreq;
        private System.Windows.Forms.Label lblAdjFreq;
        private System.Windows.Forms.Label lblCoFreq;
        private System.Windows.Forms.Label lblCoFreq2;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label lblAdjFreq2;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.NumericUpDown numNearestCellCount;
        private System.Windows.Forms.Label label13;
        private DevExpress.XtraEditors.ComboBoxEdit cbxFreqBand;
        private DevExpress.XtraEditors.CheckEdit chkFreqBand;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.NumericUpDown numPercent;
        private System.Windows.Forms.NumericUpDown numSampleCount;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.CheckBox chbNearBTS;
    }
}