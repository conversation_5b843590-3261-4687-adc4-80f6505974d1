﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using MasterCom.RAMS.Frame;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.ES.UI;
using System.Threading;
using Microsoft.Win32;
using System.IO;
using System.Management;
using System.Text;
using System.Reflection;

namespace MasterCom.RAMS
{
    static class Program
    {

        static Program()
        {
            //设置应用程序处理异常方式：ThreadException处理
            Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
            Application.ThreadException += new System.Threading.ThreadExceptionEventHandler(Application_ThreadException);
            AppDomain.CurrentDomain.UnhandledException += new UnhandledExceptionEventHandler(CurrentDomain_UnhandledException);
#if NopTask

            #region 指定动态库路径
            //AppDomain.CurrentDomain.SetData("PRIVATE_BINPATH", "NOP")
            //AppDomain.CurrentDomain.SetData("BINPATH_PROBE_ONLY", "NOP")

            MethodInfo method = typeof(AppDomainSetup).GetMethod(
             "UpdateContextProperty", BindingFlags.NonPublic | BindingFlags.Static);
            MethodInfo context = typeof(AppDomain).GetMethod(
                "GetFusionContext", BindingFlags.NonPublic | BindingFlags.Instance);
            method.Invoke(null,
               new object[] { context.Invoke(AppDomain.CurrentDomain, null), "PRIVATE_BINPATH", "NOP" });
            #endregion
#endif

        }

        internal delegate int DllRegisterServerInvoker();

        /// <summary>  
        /// Gets OS address width.  
        /// </summary>  
        /// <returns>32 indicates 32-bit OS, and 64 indicates 64-bit OS.</returns>  
        public static UInt16 GetOSAddressWidth()
        {
            try
            {
                SelectQuery query = new SelectQuery("select AddressWidth from Win32_Processor");
                ManagementObjectSearcher searcher = new ManagementObjectSearcher(query);
                ManagementObjectCollection moCollection = searcher.Get();
                foreach (var mo in moCollection)
                {
                    foreach (PropertyData property in mo.Properties)
                    {
                        if (property.Name.Equals("AddressWidth"))
                        {
                            return Convert.ToUInt16(property.Value);
                        }
                    }
                }
            }
            catch
            {
                //continue
            }
            return 32;
        }

        public static int GetImageFileMachineType(string filename)
        {
            int Machine = 0;
            try
            {
                System.IO.FileStream sFile = new System.IO.FileStream(filename, System.IO.FileMode.Open, System.IO.FileAccess.Read, System.IO.FileShare.Read);
                Byte[] byteArray = new Byte[4];
                if (sFile.Length >= 0x40)
                {
                    sFile.Seek(60, System.IO.SeekOrigin.Begin);
                    sFile.Read(byteArray, 0, 4);
                    int e_lfanew = byteArray[1] * 256 + byteArray[0];
                    if (sFile.Length >= e_lfanew + 8)
                    {
                        sFile.Seek(e_lfanew + 4, System.IO.SeekOrigin.Begin);
                        sFile.Read(byteArray, 0, 4);
                        Machine = byteArray[1] * 256 + byteArray[0];
                    }
                }
                sFile.Close();
            }
            catch
            {
                return 0;
            }
            return Machine;
        }

        /// <summary>
        /// 检查指定的 COM 组件是否已注册到系统中
        /// </summary>
        public static string GetOcxPath(String clsid)
        {
            string path = string.Empty;
            //检查方法，查找注册表是否存在指定的clsid
            String key = String.Format(@"CLSID\{{{0}}}", clsid);
            RegistryKey regKey = Registry.ClassesRoot.OpenSubKey(key);
            if (regKey != null)
            {
                regKey = Registry.ClassesRoot.OpenSubKey(key + @"\InprocServer32");
                if (regKey != null && regKey.GetValue(string.Empty) != null)
                {
                    path = regKey.GetValue(string.Empty).ToString();
                }
            }
            return path;
        }

        public static bool IsRunningOn64Bit
        {
            get
            {
                return IntPtr.Size == 8;
            }
        }

        public static bool RegOcx(string ocxPath)
        {
            try
            {
                DllInvoke dllInvoke = new DllInvoke(ocxPath);
                DllRegisterServerInvoker regDelegate = dllInvoke.Invoke("DllRegisterServer", typeof(DllRegisterServerInvoker)) as DllRegisterServerInvoker;
                return regDelegate() >= 0;
            }
            catch
            {
                //continue
            }
            return false;
        }

        /**
        static bool CheckMapWinGis()
        {
            const string mwgClsid = "03A98C90-70FF-40C7-AD93-6BF8B41B170F";
            string regPath = GetOcxPath(mwgClsid);
            if (File.Exists(regPath))
            {
                regPath = Path.GetFullPath(regPath);
            }
            if (IsRunningOn64Bit)
            {
                string ocx64Path = Path.GetFullPath(Application.StartupPath + "\\MapWinGIS64\\MapWinGIS.ocx");
                if (regPath != ocx64Path)
                {
                    //64位exe，且注册路径不是我们要注册的路径
                    if (!File.Exists(ocx64Path))
                    {
                        MessageBox.Show("64位MapWinGIS组件不存在：" + ocx64Path);
                        return false;
                    }
                    if (GetImageFileMachineType(ocx64Path) != 0x8664)
                    {
                        MessageBox.Show(string.Format("{0}{1}不是有效的64位ocx，请联系管理员。", ocx64Path, Environment.NewLine));
                        return false;
                    }
                    return RegOcx(ocx64Path);
                }
            }
            else
            {
                string ocx32Path = Path.GetFullPath(Application.StartupPath + "\\MapWinGIS.ocx");
                if (ocx32Path != regPath)
                {//32位exe，且注册路径不是我们要注册的路径
                    if (!File.Exists(ocx32Path))
                    {
                        MessageBox.Show("MapWinGIS组件不存在：" + ocx32Path);
                        return false;
                    }
                    if (GetImageFileMachineType(ocx32Path) != 0x014C)
                    {
                        MessageBox.Show(string.Format("{0}{1}不是有效的32位ocx，请联系管理员。", ocx32Path, Environment.NewLine));
                        return false;
                    }
                    return RegOcx(ocx32Path);
                }
            }
            return true;
        }
        */

        static void changeSQLLite()
        {
            string dll32 = Application.StartupPath + @"\SQLite32.DLL";
            string dll64 = Application.StartupPath + @"\SQLite64.DLL";
            string dllpath = Application.StartupPath + @"\System.Data.SQLite.dll";
            if (IsRunningOn64Bit)
            {
                if (File.Exists(dll64))
                {
                    File.Copy(dll64, dllpath, true);
                }
            }
            else
            {
                if (File.Exists(dll32))
                {
                    File.Copy(dll32, dllpath, true);
                }
            }
        }

        static System.Threading.Mutex mutex2 { get; set; }
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main(string[] Args)
        {
#if DecodeInitMoveForward
            MastercomDecode.CDecodeError error = MastercomDecode.CDecode.CDecodeError;
            if (error != MastercomDecode.CDecodeError.Unknown)
            {
                MessageBox.Show("解码库加载失败", "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
#endif

            try
            {
                unZipFilesFor64OS();
                //CheckMapWinGis()
                changeSQLLite();
            }
            catch
            {
                //忽略
            }

            System.Threading.Thread.CurrentThread.CurrentUICulture = new System.Globalization.CultureInfo("zh-CHS");
            //Dev控件属性设置成中文
            DevExpress_CHS.GetInstance();
            DevExpress.UserSkins.OfficeSkins.Register();
            DevExpress.UserSkins.BonusSkins.Register();
            if (System.Windows.Forms.VisualStyles.VisualStyleRenderer.IsSupported)
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
            }
          
            //不同浏览器对URL编码有差异，需判断是否要Decode
            for (int i = 0; i < Args.Length; i++)
            {
                if (Args[i].Contains("%"))
                {
                    Args[i] = System.Web.HttpUtility.UrlDecode(Args[i]);
                }
            }
            bool created = true;
            try
            {
                mutex2 = new System.Threading.Mutex(false, "RAMSABCDEFGxx", out created);
            }
            catch
            {
                //continue
            }
            if (!created && Args.Length > 0)
            {
                CmdSender cmd = new CmdSender();
                if (cmd.DoSendMsg(Args))
                {
                    Application.Exit();
                    return;
                }
            }

            try
            {
                System.Threading.Thread.CurrentThread.Name = "Main";
                System.IO.Directory.SetCurrentDirectory(Application.StartupPath);
                Form.CheckForIllegalCrossThreadCalls = false;
                DevExpress.Data.CurrencyDataController.DisableThreadingProblemsDetection = true;
                Application.Run(new MainForm(Args));
            }
            catch (Exception ex)
            {
                string str = "";
                string strDateInfo = string.Format("出现应用程序未处理的异常：{0}\r\n", DateTime.Now);
                if (ex != null)
                {
                    str = string.Format(strDateInfo + "异常类型：{0}\r\n异常消息：{1}\r\n异常信息：{2}\r\n",
                    ex.GetType().Name, ex.Message, ex.StackTrace);
                }
                else
                {
                    str = string.Format("应用程序线程错误:{0}", ex);
                }
                MessageBox.Show(str, "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

        }

        /// <summary>
        /// 初始化运行环境，主要为解压64位程序所需动态库相关
        /// </summary>
        static void unZipFilesFor64OS()
        {
            string files64 = Application.StartupPath + "/FilesFor64OS.zip";
            if (File.Exists(files64) && GetOSAddressWidth() == 64)
            {
                UnZipClass unZip = new UnZipClass();

                unZip.UnZip(files64, Application.StartupPath + "/");
            }
        }

#region 未处理的程序异常

        static void Application_ThreadException(object sender, System.Threading.ThreadExceptionEventArgs e)
        {
            string str = GetExceptionMsg(e.Exception, e.ToString());
            MessageBox.Show(str, "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            string str = GetExceptionMsg(e.ExceptionObject as Exception, e.ToString());
            MessageBox.Show(str, "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        /// <summary>
        /// 生成自定义异常消息
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <param name="backStr">备用异常消息：当ex为null时有效</param>
        /// <returns>异常字符串文本</returns>
        static string GetExceptionMsg(Exception ex, string backStr)
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendLine("****************************异常文本****************************");
            sb.AppendLine("【出现时间】：" + DateTime.Now.ToString());
            if (ex != null)
            {
                sb.AppendLine("【异常类型】：" + ex.GetType().Name);
                sb.AppendLine("【异常信息】：" + ex.Message);
                sb.AppendLine("【堆栈调用】：" + ex.StackTrace);
            }
            else
            {
                sb.AppendLine("【未处理异常】：" + backStr);
            }
            sb.AppendLine("***************************************************************");
            return sb.ToString();
        }
#endregion

    }
}