<?xml version="1.0"?>
<Configs>
  <Config name="Template">
    <Item name="Options" typeName="IDictionary">
      <Item typeName="String" key="Name">GSM vs wcdma vs evdo</Item>
      <Item typeName="IDictionary" key="HubContext">
        <Item typeName="IDictionary" key="移动">
          <Item typeName="String" key="Name">移动</Item>
          <Item typeName="IList" key="ServVec">
            <Item typeName="Int32">1</Item>
          </Item>
          <Item typeName="String" key="FormulaName" />
          <Item typeName="String" key="FormulaExp">{(evtIdCount[0]+value9[0]) - (evtIdCount[7]+evtIdCount[9]+evtIdCount[81]+evtIdCount[87]+value9[7]+value9[9]+value9[81])}</Item>
          <Item typeName="IDictionary" key="ValueRange">
            <Item typeName="Double" key="Min">-100</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Double" key="Max">100</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="联通">
          <Item typeName="String" key="Name">联通</Item>
          <Item typeName="IList" key="ServVec">
            <Item typeName="Int32">10</Item>
          </Item>
          <Item typeName="String" key="FormulaName" />
          <Item typeName="String" key="FormulaExp">{Wx_710A3F}</Item>
          <Item typeName="IDictionary" key="ValueRange">
            <Item typeName="Double" key="Min">-10000</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Double" key="Max">10000</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
          </Item>
        </Item>
        <Item typeName="IDictionary" key="电信">
          <Item typeName="String" key="Name">电信</Item>
          <Item typeName="IList" key="ServVec">
            <Item typeName="Int32">9</Item>
          </Item>
          <Item typeName="String" key="FormulaName" />
          <Item typeName="String" key="FormulaExp">{Ex_5E09010B}</Item>
          <Item typeName="IDictionary" key="ValueRange">
            <Item typeName="Double" key="Min">-10000</Item>
            <Item typeName="Boolean" key="MinIncluded">True</Item>
            <Item typeName="Double" key="Max">10000</Item>
            <Item typeName="Boolean" key="MaxIncluded">True</Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IList" key="AlghirithmVec">
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">三网都没测</Item>
          <Item typeName="Boolean" key="IsSpecial">True</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">无数据</Item>
          <Item typeName="String" key="ENaNCT">无数据</Item>
          <Item typeName="Int32" key="Color">-65536</Item>
          <Item typeName="String" key="ColorRGB">255,0,0</Item>
          <Item typeName="IList" key="ValueRangeVec" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">移动和联通未测，电信有测</Item>
          <Item typeName="Boolean" key="IsSpecial">True</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">无数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-16181</Item>
          <Item typeName="String" key="ColorRGB">255,192,203</Item>
          <Item typeName="IList" key="ValueRangeVec" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">移动和电信未测，联通有测</Item>
          <Item typeName="Boolean" key="IsSpecial">True</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">无数据</Item>
          <Item typeName="Int32" key="Color">-2461482</Item>
          <Item typeName="String" key="ColorRGB">218,112,214</Item>
          <Item typeName="IList" key="ValueRangeVec" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">移动未测，联通和电信有测</Item>
          <Item typeName="Boolean" key="IsSpecial">True</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-23296</Item>
          <Item typeName="String" key="ColorRGB">255,165,0</Item>
          <Item typeName="IList" key="ValueRangeVec" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">移动有测，联通和电信未测</Item>
          <Item typeName="Boolean" key="IsSpecial">True</Item>
          <Item typeName="String" key="ENaNCM">有数据</Item>
          <Item typeName="String" key="ENaNCU">无数据</Item>
          <Item typeName="String" key="ENaNCT">无数据</Item>
          <Item typeName="Int32" key="Color">-8388480</Item>
          <Item typeName="String" key="ColorRGB">128,0,128</Item>
          <Item typeName="IList" key="ValueRangeVec" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">移动和电信有测，联通未测</Item>
          <Item typeName="Boolean" key="IsSpecial">True</Item>
          <Item typeName="String" key="ENaNCM">有数据</Item>
          <Item typeName="String" key="ENaNCU">无数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-8355712</Item>
          <Item typeName="String" key="ColorRGB">128,128,128</Item>
          <Item typeName="IList" key="ValueRangeVec" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">移动和联通有测，电信未测</Item>
          <Item typeName="Boolean" key="IsSpecial">True</Item>
          <Item typeName="String" key="ENaNCM">有数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">无数据</Item>
          <Item typeName="Int32" key="Color">-16776961</Item>
          <Item typeName="String" key="ColorRGB">0,0,255</Item>
          <Item typeName="IList" key="ValueRangeVec" />
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">移动最好，联通次之，电信最差</Item>
          <Item typeName="Boolean" key="IsSpecial">False</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-16711936</Item>
          <Item typeName="String" key="ColorRGB">0,255,0</Item>
          <Item typeName="IList" key="ValueRangeVec">
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">移动</Item>
              <Item typeName="String" key="GuestCarrier">联通</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">0</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">20</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">移动</Item>
              <Item typeName="String" key="GuestCarrier">电信</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">0</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">20</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">联通</Item>
              <Item typeName="String" key="GuestCarrier">电信</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">0</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">20</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">移动最好，电信次之，联通最差</Item>
          <Item typeName="Boolean" key="IsSpecial">False</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-12582848</Item>
          <Item typeName="String" key="ColorRGB">64,0,64</Item>
          <Item typeName="IList" key="ValueRangeVec">
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">移动</Item>
              <Item typeName="String" key="GuestCarrier">联通</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">0</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">20</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">移动</Item>
              <Item typeName="String" key="GuestCarrier">电信</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">0</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">20</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">电信</Item>
              <Item typeName="String" key="GuestCarrier">联通</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">0</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">20</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">联通最好，移动次之，电信最差</Item>
          <Item typeName="Boolean" key="IsSpecial">False</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-16256</Item>
          <Item typeName="String" key="ColorRGB">255,192,128</Item>
          <Item typeName="IList" key="ValueRangeVec">
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">联通</Item>
              <Item typeName="String" key="GuestCarrier">移动</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">0</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">20</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">联通</Item>
              <Item typeName="String" key="GuestCarrier">电信</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">0</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">20</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">移动</Item>
              <Item typeName="String" key="GuestCarrier">电信</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">0</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">20</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">联通最好，电信次之，移动最差</Item>
          <Item typeName="Boolean" key="IsSpecial">False</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-16760768</Item>
          <Item typeName="String" key="ColorRGB">0,64,64</Item>
          <Item typeName="IList" key="ValueRangeVec">
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">联通</Item>
              <Item typeName="String" key="GuestCarrier">移动</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">0</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">20</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">联通</Item>
              <Item typeName="String" key="GuestCarrier">电信</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">0</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">20</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">电信</Item>
              <Item typeName="String" key="GuestCarrier">移动</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">0</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">20</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">电信最好，移动次之，联通最差</Item>
          <Item typeName="Boolean" key="IsSpecial">False</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-8372160</Item>
          <Item typeName="String" key="ColorRGB">128,64,64</Item>
          <Item typeName="IList" key="ValueRangeVec">
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">电信</Item>
              <Item typeName="String" key="GuestCarrier">移动</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">0</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">20</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">电信</Item>
              <Item typeName="String" key="GuestCarrier">联通</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">0</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">20</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">移动</Item>
              <Item typeName="String" key="GuestCarrier">联通</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">0</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">20</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">电信最好，联通次之，移动最差</Item>
          <Item typeName="Boolean" key="IsSpecial">False</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-12566464</Item>
          <Item typeName="String" key="ColorRGB">64,64,64</Item>
          <Item typeName="IList" key="ValueRangeVec">
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">电信</Item>
              <Item typeName="String" key="GuestCarrier">移动</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">0</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">20</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">电信</Item>
              <Item typeName="String" key="GuestCarrier">联通</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">0</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">20</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">联通</Item>
              <Item typeName="String" key="GuestCarrier">移动</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">0</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">20</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Name">移动比联通好，不看电信</Item>
          <Item typeName="Boolean" key="IsSpecial">False</Item>
          <Item typeName="String" key="ENaNCM">无数据</Item>
          <Item typeName="String" key="ENaNCU">有数据</Item>
          <Item typeName="String" key="ENaNCT">有数据</Item>
          <Item typeName="Int32" key="Color">-16760832</Item>
          <Item typeName="String" key="ColorRGB">0,64,0</Item>
          <Item typeName="IList" key="ValueRangeVec">
            <Item typeName="IDictionary">
              <Item typeName="String" key="HostCarrier">移动</Item>
              <Item typeName="String" key="GuestCarrier">联通</Item>
              <Item typeName="IDictionary" key="Range">
                <Item typeName="Double" key="Min">0</Item>
                <Item typeName="Boolean" key="MinIncluded">True</Item>
                <Item typeName="Double" key="Max">20</Item>
                <Item typeName="Boolean" key="MaxIncluded">True</Item>
              </Item>
            </Item>
          </Item>
        </Item>
      </Item>
      <Item typeName="IDictionary" key="OtherAlghirithm">
        <Item typeName="String" key="Name">其他</Item>
        <Item typeName="Boolean" key="IsSpecial">False</Item>
        <Item typeName="String" key="ENaNCM">有数据</Item>
        <Item typeName="String" key="ENaNCU">有数据</Item>
        <Item typeName="String" key="ENaNCT">有数据</Item>
        <Item typeName="Int32" key="Color">-16711681</Item>
        <Item typeName="String" key="ColorRGB">0,255,255</Item>
        <Item typeName="IList" key="ValueRangeVec" />
      </Item>
    </Item>
  </Config>
</Configs>