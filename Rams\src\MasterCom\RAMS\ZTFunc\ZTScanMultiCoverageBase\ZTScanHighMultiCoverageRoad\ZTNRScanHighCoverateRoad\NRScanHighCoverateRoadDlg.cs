﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRScanHighCoverateRoadDlg : BaseDialog
    {
        public NRScanHighCoverateRoadDlg()
        {
            InitializeComponent();
            this.btnOK.Click += BtnOK_Click;
            this.btnCancel.Click += BtnCancel_Click;
        }

        public void SetCondition(NRScanHighCoverateRoadCond cond)
        {
            if (cond == null)
            {
                return;
            }
            this.spinEditMaxDiff.Value = cond.RsrpMaxDiff;
            this.spinEditRxlevMin.Value = cond.RsrpMin;
            this.spinEditCoverage.Value = cond.RelCoverate;
            this.spinEditRoadDistance.Value = cond.RoadDistance;
            this.spinEditRoadPercent.Value = (decimal)cond.RoadMinPercent;
            this.spinEditSampleDistance.Value = cond.SampleDistance;

            this.absValue.Value = cond.AbsValue;
            this.chbAbslute.Checked = cond.IsAbsCheck;
            this.chbRelative.Checked = cond.IsRelativeCheck;
            this.absCoverate.Value = cond.AbsCoverate;
        }
        public NRScanHighCoverateRoadCond GetCondition()
        {
            NRScanHighCoverateRoadCond cond = new NRScanHighCoverateRoadCond();
            cond.RsrpMaxDiff = (int)this.spinEditMaxDiff.Value;
            cond.RsrpMin = (int)this.spinEditRxlevMin.Value;
            cond.RelCoverate = (int)this.spinEditCoverage.Value;
            cond.RoadDistance = (int)this.spinEditRoadDistance.Value;
            cond.RoadMinPercent = (int)this.spinEditRoadPercent.Value;
            cond.SampleDistance = (int)this.spinEditSampleDistance.Value;

            cond.AbsValue = (int)this.absValue.Value;
            cond.IsAbsCheck = this.chbAbslute.Checked;
            cond.IsRelativeCheck = this.chbRelative.Checked;
            cond.AbsCoverate = (int)this.absCoverate.Value;

            return cond;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void chbRelative_CheckedChanged(object sender, EventArgs e)
        {
            if (!chbRelative.Checked && !chbAbslute.Checked)
            {
                chbRelative.Checked = true;
            }
            spinEditMaxDiff.Enabled = chbRelative.Checked;
            spinEditCoverage.Enabled = chbRelative.Checked;
        }

        private void chbAbslute_CheckedChanged(object sender, EventArgs e)
        {
            if (!chbRelative.Checked && !chbAbslute.Checked)
            {
                chbAbslute.Checked = true;
            }
            absValue.Enabled = chbAbslute.Checked;
            absCoverate.Enabled = chbAbslute.Checked;
        }
    }
}
