﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.CQT
{
    public partial class CQTBubbleXtraForm : DevExpress.XtraEditors.XtraForm
    {
        public CQTBubbleXtraForm()
        {
            InitializeComponent();
        }

        public void setData(CQTLibrary.PublicItem.EvaluateResult evaluate)
        {
            lblName.Text = evaluate.StrCqtName;
            lbljiankang.Text = evaluate.StrValue;
            lblzhishu.Text = evaluate.FHealthQuotiety.ToString();

            lblcolorfugai.Text = evaluate.StrCoverResult;
            lblcolorzhiliang.Text = evaluate.StrQualResult;
            lblcolorrongliang.Text = evaluate.StrVolumeResult;
            lblcolorganzhi.Text = evaluate.StrPerceptionResult;
            lblcolormanyidu.Text = evaluate.StrSatisfyResult;
            lblNo.Text = evaluate.StrNo;

            exColor(lblcolorfugai);
            exColor(lblcolorzhiliang);
            exColor(lblcolorrongliang);
            exColor(lblcolorganzhi);
            exColor(lblcolormanyidu);


            //
            List<CQTLibrary.PublicItem.EvaluateCqtDetailResult> listfugai=new List<CQTLibrary.PublicItem.EvaluateCqtDetailResult>();
            List<CQTLibrary.PublicItem.EvaluateCqtDetailResult> listzhiliang = new List<CQTLibrary.PublicItem.EvaluateCqtDetailResult>();
            List<CQTLibrary.PublicItem.EvaluateCqtDetailResult> listrongliang = new List<CQTLibrary.PublicItem.EvaluateCqtDetailResult>();
            List<CQTLibrary.PublicItem.EvaluateCqtDetailResult> listganzhi = new List<CQTLibrary.PublicItem.EvaluateCqtDetailResult>();
            List<CQTLibrary.PublicItem.EvaluateCqtDetailResult> listmanyidu = new List<CQTLibrary.PublicItem.EvaluateCqtDetailResult>();

            foreach (CQTLibrary.PublicItem.EvaluateCqtDetailResult item in evaluate.evaluateResultList)
	        {
                if (item.StrEvaluateType == "网络覆盖")
                    listfugai.Add(item);
                if (item.StrEvaluateType == "网络质量")
                    listzhiliang.Add(item);
                if (item.StrEvaluateType == "网络容量")
                    listrongliang.Add(item);
                if (item.StrEvaluateType == "网络感知")
                    listganzhi.Add(item);
                if (item.StrEvaluateType == "网络满意度")
                    listmanyidu.Add(item);
	        }

            gridControl1.DataSource = listfugai;
            gridControl1.RefreshDataSource();

            gridControl2.DataSource = listzhiliang;
            gridControl2.RefreshDataSource();

            gridControl3.DataSource = listrongliang;
            gridControl3.RefreshDataSource();

            gridControl4.DataSource = listganzhi;
            gridControl4.RefreshDataSource();

            gridControl5.DataSource = listmanyidu;
            gridControl5.RefreshDataSource();

            if (listmanyidu.Count == 0)
            {
                xtraTabControl1.TabPages[5].PageVisible = false;
            }
            else
            {
                xtraTabControl1.TabPages[5].PageVisible = true;
            }

            
        }

        public void exColor(Label lb)
        {
           
            if (lb.Text == "健康")
            {
                lb.ForeColor = Color.Green; 
                  
            }

            if (lb.Text == "合格")
            {
                lb.ForeColor = Color.Orange;
               
            }

            if (lb.Text == "不合格")
            {
                lb.ForeColor = Color.Red;
            }

            if (lb.Text == "满意")
            {
                lb.ForeColor = Color.Green;
            }
        }

        private void gridView1_RowCellStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs e)
        {
            if (e.Column.Caption == "StrResult")
            {
                string aa = gridView1.GetRowCellDisplayText(e.RowHandle, e.Column);
           

                if (aa == "合格")
                {
                    e.Appearance.BackColor = Color.Green;
                    //e.Appearance.BackColor2 = Color.LightCyan;
                }

                if (aa == "不合格")
                {
                    e.Appearance.BackColor = Color.Red;
                    //e.Appearance.BackColor2 = Color.LightCyan;
                }

                if (aa == "满意")
                {
                    e.Appearance.BackColor = Color.Green;
                    //e.Appearance.BackColor2 = Color.LightCyan;
                }
            }
        }
    }
}