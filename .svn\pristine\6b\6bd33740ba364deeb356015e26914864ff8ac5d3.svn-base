﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public class AreaKpiSearchPartByPart
    {
        private ArchiveCondition condition;

        private List<string> formulaVec;

        private bool isQueryAllParams { get; set; } = false;

        private bool isQueryEvents { get; set; } = true;

        private bool isStatLatestOnly = false;

        private AnaDealerBase anaDealer;

        private AreaTestCondition areaCondition;

        private Dictionary<int, Dictionary<int, AreaBase>> typePairMap;

        public Dictionary<AreaBase, CAreaSummary> AreaKpiMap
        {
            get;
            private set;
        }

        public AreaKpiSearchPartByPart()
        {
            typePairMap = new Dictionary<int, Dictionary<int, AreaBase>>();
            AreaKpiMap = new Dictionary<AreaBase, CAreaSummary>();
            anaDealer = new NoneAnaDealer(areaCondition);
        }

        public void SetCondition(ArchiveCondition cond)
        {
            condition = cond;
        }

        public void SetQueryAllParams(bool bQueryAll)
        {
            isQueryAllParams = bQueryAll;
        }

        public void SetQueryEvents(bool bQueryEvent)
        {
            isQueryEvents = bQueryEvent;
        }

        public void SetStatLatestOnly(bool statLastestOnly)
        {
            isStatLatestOnly = statLastestOnly;
        }

        public void SetFormula(List<string> formulas)
        {
            this.formulaVec = formulas;
        }

        public void SetTypes(Dictionary<int, Dictionary<int, AreaBase>> typeIdMap)
        {
            if (typeIdMap == null || typeIdMap.Count == 0) return;

            typePairMap = typeIdMap;
        }

        public void SetDealer(AnaDealerBase dealer, AreaTestCondition areaCond)
        {
            anaDealer = dealer;
            areaCondition = areaCond;
        }

        public void Query()
        {
            AreaKpiMap.Clear();
            upSumLst.Clear();

            Dictionary<int, Dictionary<int, AreaBase>> typeIdMap = new Dictionary<int, Dictionary<int, AreaBase>>();

            int idx = 1;
            foreach (int areaType in typePairMap.Keys)
            {
                foreach(AreaBase area in typePairMap[areaType].Values)
                {
                    checkSearch(area, ref typeIdMap, idx++);
                }
            }
            search(ref typeIdMap);

            finalMerge();
        }

        private void checkSearch(AreaBase area, ref Dictionary<int, Dictionary<int, AreaBase>> typeIdMap, int idx)
        {
            Dictionary<int, AreaBase> idDic;
            if (!typeIdMap.TryGetValue(area.AreaTypeID, out idDic))
            {
                idDic = new Dictionary<int, AreaBase>();
                typeIdMap[area.AreaTypeID] = idDic;
            }
            idDic[area.AreaID] = area;

            if (idx % 1000000 == 0)
            {
                search(ref typeIdMap);
            }
        }

        private void search(ref Dictionary<int, Dictionary<int, AreaBase>> typeIdMap)
        {
            if (typeIdMap.Count == 0)
                return;

            AreaKpiBaseQuery query = new AreaKpiBaseQuery();
            query.IsStatLatestOnly = isStatLatestOnly;
            QueryCondition searchCond = condition.GetBaseConditionBackUp();
            query.SetQueryCondition(searchCond);
            query.SetFormula(formulaVec);
            query.SetTypes(typeIdMap);
            query.Query();
            makeGroup(query.AreaKpiMap);
            typeIdMap.Clear();
        }

        protected void makeGroup(Dictionary<AreaBase, AreaKPIDataGroup<AreaBase>> areaKpiMap)
        {
            WaitBox.Show("开始进行村庄汇聚...", makeGroupWithWaitBox, areaKpiMap);
        }

        private void makeGroupWithWaitBox(object o)
        {
            try
            {
                int index = 0, progress = 0;
                Dictionary<AreaBase, AreaKPIDataGroup<AreaBase>> areaKpiMap = o as Dictionary<AreaBase, AreaKPIDataGroup<AreaBase>>;
                Dictionary<AreaBase, CAreaSummary> summaryMap = AreaKpiMap;

                foreach (AreaBase area in areaKpiMap.Keys)
                {
                    reportWaitBox(ref index, ref progress);
                    CAreaSummary village = createSummary(area, areaKpiMap);
                    anaDealer.AnaArea(village, areaCondition);

                    AreaBase parentArea = area.ParentArea;

                    if (parentArea != null)
                    {
                        CAreaSummary summary;
                        if (!summaryMap.TryGetValue(parentArea, out summary))
                        {
                            summary = new CAreaSummary(parentArea);
                            summaryMap[parentArea] = summary;

                            upSumLst.Add(summary);
                        }

                        addArea(summary, village);
                    }
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                System.Threading.Thread.Sleep(100);
                WaitBox.Close();
            }
        }

        readonly List<CAreaSummary> upSumLst = new List<CAreaSummary>();

        protected void finalMerge()
        {
            WaitBox.Show("最后往上级汇聚...", finalMergeWithWaitBox, null);
        }

        private void finalMergeWithWaitBox(object o)
        {
            try
            {
                int index = 0, progress = 0;
                while (upSumLst.Count > 0)
                {
                    reportWaitBox(ref index, ref progress);
                    CAreaSummary sum = upSumLst[0];
                    AreaBase parentArea = sum.Area.ParentArea;
                    if (parentArea != null)
                    {
                        CAreaSummary summary;
                        if (!AreaKpiMap.TryGetValue(parentArea, out summary))
                        {
                            summary = new CAreaSummary(parentArea);
                            AreaKpiMap[parentArea] = summary;

                            upSumLst.Add(summary);
                        }

                        addArea(summary, sum);
                    }
                    upSumLst.RemoveAt(0);
                }

                foreach (CAreaSummary summary in AreaKpiMap.Values)
                {
                    if (summary.Area.ParentArea == null)
                    {
                        foreach (CAreaSummary village in summary.VillageVec)
                        {
                            reportWaitBox(ref index, ref progress);
                            anaDealer.CheckAchieve(village);
                        }
                    }
                    anaDealer.CheckAchieve(summary);
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                System.Threading.Thread.Sleep(100);
                WaitBox.Close();
            }
        }

        private void reportWaitBox(ref int index, ref int progress)
        {
            if (Math.Log(index++) * (10) > WaitBox.ProgressPercent)
            {
                progress++;
                if (progress > 95)
                {
                    progress = 5;
                    index = 0;
                }
                if (progress % 5 == 0)
                {
                    WaitBox.ProgressPercent = progress;
                }
            }
        }

        private void addArea(CAreaSummary summary, CAreaSummary village)
        {
            summary.Merge(village.AreaKpiGroup);

            if (village.Area.Rank == ZTAreaManager.Instance.LowestRank)
            {
                summary.AddVillage(village);
                anaDealer.MergeArea(summary, village);
            }
            else
            {
                foreach (CAreaSummary sub in village.VillageVec)
                {
                    summary.AddVillage(sub);
                    anaDealer.MergeArea(summary, sub);
                }
            }
        }

        private CAreaSummary createSummary(AreaBase area, Dictionary<AreaBase, AreaKPIDataGroup<AreaBase>> areaKpiMap)
        {
            CAreaSummary summary = new CAreaSummary(area);

            AreaKPIDataGroup<AreaBase> grp;
            if (areaKpiMap.TryGetValue(area, out grp))
            {
                summary.Merge(grp);
            }

            return summary;
        }
    }
}
