﻿using MasterCom.MTGis;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;

namespace MasterCom.RAMS.Func
{
    class CqtFilterQuery : DIYSQLBase
    {
        public TimePeriod PeriodCondition { get; set; }
        public CqtFilterQuery(MainModel mainModel, TimePeriod period)
            : base(mainModel)
        {
            MainDB = true;
            PeriodCondition = period;
        }

        public override string Name
        {
            get { return "自定义"; }
        }

        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }

        readonly List<CqtFilterInfo> cqtFilterInfos = new List<CqtFilterInfo>();
        public List<CqtFilterInfo> CQTFilterInfos
        {
            get { return cqtFilterInfos; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22119, "");
        }

        protected override void query()
        {
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询时间段内的信息...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Show("正在查询时间段内的信息...", queryInThread, clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override string getSqlTextString()
        {
            if (PeriodCondition == null)
            {
                PeriodCondition = condition.Periods[0];
            }
            string sql = string.Format(@"Select * From tb_bt_floorTest Where CAST(testDate AS DATETIME) between '{0}' and '{1}'",
            PeriodCondition.BeginTime.ToShortDateString(), PeriodCondition.EndTime.AddDays(1).ToShortDateString());
            return sql;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[2];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CqtFilterInfo info = CqtFilterInfo.Fill(package.Content);
                    cqtFilterInfos.Add(info);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        protected override void queryInThread(object o)
        {
            cqtFilterInfos.Clear();
            base.queryInThread(o);
            WaitBox.Close();
        }
    }

    public class CqtFilterInfo
    {
        public string FloorName { get; set; } = "";
        public string TestDate { get; set; } = "";

        public static CqtFilterInfo Fill(Content content)
        {
            CqtFilterInfo info = new CqtFilterInfo();
            info.FloorName = content.GetParamString();
            info.TestDate = content.GetParamString();
            return info;
        }
    }
}
