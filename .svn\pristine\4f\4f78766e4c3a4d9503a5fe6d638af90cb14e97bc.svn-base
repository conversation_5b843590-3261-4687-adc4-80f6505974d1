﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTLTEMobileServiceAnaListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTLTEMobileServiceAnaListForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ListViewMobileServiceAna = new BrightIdeasSoftware.TreeListView();
            this.olvColumnStatSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFileName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTypeName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnURL = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnResult = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSignalFirstName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSignalFirstTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSignalSecondName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSignalSecondTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSignalSpanFirst2Second = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSignalLastName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSignalLastTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSignalSpanSecond2Last = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnFailReason = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnReBufferCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnReBufferTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnLoadSpeed = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTotalByte = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTotalTime = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAppSpeedAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAppSpeedMax = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRSRPAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSINRAvg = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellInfos = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewMobileServiceAna)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miReplay,
            this.miExportExcel,
            this.toolStripMenuItem1,
            this.miExpandAll,
            this.miCallapsAll});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(130, 98);
            this.ctxMenu.Opening += new System.ComponentModel.CancelEventHandler(this.ctxMenu_Opening);
            // 
            // miReplay
            // 
            this.miReplay.Name = "miReplay";
            this.miReplay.Size = new System.Drawing.Size(129, 22);
            this.miReplay.Text = "回放";
            this.miReplay.Click += new System.EventHandler(this.miReplay_Click);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(126, 6);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(129, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCallapsAll
            // 
            this.miCallapsAll.Name = "miCallapsAll";
            this.miCallapsAll.Size = new System.Drawing.Size(129, 22);
            this.miCallapsAll.Text = "全部合并";
            this.miCallapsAll.Click += new System.EventHandler(this.miCallapsAll_Click);
            // 
            // ListViewMobileServiceAna
            // 
            this.ListViewMobileServiceAna.AllColumns.Add(this.olvColumnStatSN);
            this.ListViewMobileServiceAna.AllColumns.Add(this.olvColumnFileName);
            this.ListViewMobileServiceAna.AllColumns.Add(this.olvColumnTypeName);
            this.ListViewMobileServiceAna.AllColumns.Add(this.olvColumnURL);
            this.ListViewMobileServiceAna.AllColumns.Add(this.olvColumnResult);
            this.ListViewMobileServiceAna.AllColumns.Add(this.olvColumnSignalFirstName);
            this.ListViewMobileServiceAna.AllColumns.Add(this.olvColumnSignalFirstTime);
            this.ListViewMobileServiceAna.AllColumns.Add(this.olvColumnSignalSecondName);
            this.ListViewMobileServiceAna.AllColumns.Add(this.olvColumnSignalSecondTime);
            this.ListViewMobileServiceAna.AllColumns.Add(this.olvColumnSignalSpanFirst2Second);
            this.ListViewMobileServiceAna.AllColumns.Add(this.olvColumnSignalLastName);
            this.ListViewMobileServiceAna.AllColumns.Add(this.olvColumnSignalLastTime);
            this.ListViewMobileServiceAna.AllColumns.Add(this.olvColumnSignalSpanSecond2Last);
            this.ListViewMobileServiceAna.AllColumns.Add(this.olvColumnFailReason);
            this.ListViewMobileServiceAna.AllColumns.Add(this.olvColumnReBufferCount);
            this.ListViewMobileServiceAna.AllColumns.Add(this.olvColumnReBufferTime);
            this.ListViewMobileServiceAna.AllColumns.Add(this.olvColumnLoadSpeed);
            this.ListViewMobileServiceAna.AllColumns.Add(this.olvColumnTotalByte);
            this.ListViewMobileServiceAna.AllColumns.Add(this.olvColumnTotalTime);
            this.ListViewMobileServiceAna.AllColumns.Add(this.olvColumnAppSpeedAvg);
            this.ListViewMobileServiceAna.AllColumns.Add(this.olvColumnAppSpeedMax);
            this.ListViewMobileServiceAna.AllColumns.Add(this.olvColumnSampleCount);
            this.ListViewMobileServiceAna.AllColumns.Add(this.olvColumnRSRPAvg);
            this.ListViewMobileServiceAna.AllColumns.Add(this.olvColumnSINRAvg);
            this.ListViewMobileServiceAna.AllColumns.Add(this.olvColumnCellInfos);
            this.ListViewMobileServiceAna.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.ListViewMobileServiceAna.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnStatSN,
            this.olvColumnFileName,
            this.olvColumnTypeName,
            this.olvColumnURL,
            this.olvColumnResult,
            this.olvColumnSignalFirstName,
            this.olvColumnSignalFirstTime,
            this.olvColumnSignalSecondName,
            this.olvColumnSignalSecondTime,
            this.olvColumnSignalSpanFirst2Second,
            this.olvColumnSignalLastName,
            this.olvColumnSignalLastTime,
            this.olvColumnSignalSpanSecond2Last,
            this.olvColumnFailReason,
            this.olvColumnReBufferCount,
            this.olvColumnReBufferTime,
            this.olvColumnLoadSpeed,
            this.olvColumnTotalByte,
            this.olvColumnTotalTime,
            this.olvColumnAppSpeedAvg,
            this.olvColumnAppSpeedMax,
            this.olvColumnSampleCount,
            this.olvColumnRSRPAvg,
            this.olvColumnSINRAvg,
            this.olvColumnCellInfos});
            this.ListViewMobileServiceAna.ContextMenuStrip = this.ctxMenu;
            this.ListViewMobileServiceAna.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewMobileServiceAna.FullRowSelect = true;
            this.ListViewMobileServiceAna.GridLines = true;
            this.ListViewMobileServiceAna.HeaderWordWrap = true;
            this.ListViewMobileServiceAna.IsNeedShowOverlay = false;
            this.ListViewMobileServiceAna.Location = new System.Drawing.Point(1, 1);
            this.ListViewMobileServiceAna.Name = "ListViewMobileServiceAna";
            this.ListViewMobileServiceAna.OwnerDraw = true;
            this.ListViewMobileServiceAna.ShowGroups = false;
            this.ListViewMobileServiceAna.Size = new System.Drawing.Size(1251, 501);
            this.ListViewMobileServiceAna.TabIndex = 7;
            this.ListViewMobileServiceAna.UseCompatibleStateImageBehavior = false;
            this.ListViewMobileServiceAna.View = System.Windows.Forms.View.Details;
            this.ListViewMobileServiceAna.VirtualMode = true;
            this.ListViewMobileServiceAna.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.ListViewCauseValueAna_MouseDoubleClick);
            // 
            // olvColumnStatSN
            // 
            this.olvColumnStatSN.AspectName = "";
            this.olvColumnStatSN.HeaderFont = null;
            this.olvColumnStatSN.Text = "序号";
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名称";
            this.olvColumnFileName.Width = 120;
            // 
            // olvColumnTypeName
            // 
            this.olvColumnTypeName.HeaderFont = null;
            this.olvColumnTypeName.Text = "业务类别";
            this.olvColumnTypeName.Width = 80;
            // 
            // olvColumnURL
            // 
            this.olvColumnURL.HeaderFont = null;
            this.olvColumnURL.Text = "URL";
            this.olvColumnURL.Width = 100;
            // 
            // olvColumnResult
            // 
            this.olvColumnResult.HeaderFont = null;
            this.olvColumnResult.Text = "结果";
            // 
            // olvColumnSignalFirstName
            // 
            this.olvColumnSignalFirstName.HeaderFont = null;
            this.olvColumnSignalFirstName.Text = "信令1名称";
            this.olvColumnSignalFirstName.Width = 120;
            // 
            // olvColumnSignalFirstTime
            // 
            this.olvColumnSignalFirstTime.HeaderFont = null;
            this.olvColumnSignalFirstTime.Text = "信令1时间";
            this.olvColumnSignalFirstTime.Width = 120;
            // 
            // olvColumnSignalSecondName
            // 
            this.olvColumnSignalSecondName.HeaderFont = null;
            this.olvColumnSignalSecondName.Text = "信令2名称";
            this.olvColumnSignalSecondName.Width = 120;
            // 
            // olvColumnSignalSecondTime
            // 
            this.olvColumnSignalSecondTime.HeaderFont = null;
            this.olvColumnSignalSecondTime.Text = "信令2时间";
            this.olvColumnSignalSecondTime.Width = 120;
            // 
            // olvColumnSignalSpanFirst2Second
            // 
            this.olvColumnSignalSpanFirst2Second.HeaderFont = null;
            this.olvColumnSignalSpanFirst2Second.Text = "信令1-2时间差(毫秒)";
            this.olvColumnSignalSpanFirst2Second.Width = 120;
            // 
            // olvColumnSignalLastName
            // 
            this.olvColumnSignalLastName.HeaderFont = null;
            this.olvColumnSignalLastName.Text = "信令3名称";
            this.olvColumnSignalLastName.Width = 120;
            // 
            // olvColumnSignalLastTime
            // 
            this.olvColumnSignalLastTime.HeaderFont = null;
            this.olvColumnSignalLastTime.Text = "信令3时间";
            this.olvColumnSignalLastTime.Width = 120;
            // 
            // olvColumnSignalSpanSecond2Last
            // 
            this.olvColumnSignalSpanSecond2Last.HeaderFont = null;
            this.olvColumnSignalSpanSecond2Last.Text = "信令2-3时间差(毫秒)";
            this.olvColumnSignalSpanSecond2Last.Width = 120;
            // 
            // olvColumnFailReason
            // 
            this.olvColumnFailReason.HeaderFont = null;
            this.olvColumnFailReason.Text = "失败原因";
            this.olvColumnFailReason.Width = 80;
            // 
            // olvColumnReBufferCount
            // 
            this.olvColumnReBufferCount.HeaderFont = null;
            this.olvColumnReBufferCount.Text = "视频业务卡顿次数";
            this.olvColumnReBufferCount.Width = 120;
            // 
            // olvColumnReBufferTime
            // 
            this.olvColumnReBufferTime.HeaderFont = null;
            this.olvColumnReBufferTime.Text = "视频业务卡顿时长(毫秒)";
            this.olvColumnReBufferTime.Width = 150;
            // 
            // olvColumnLoadSpeed
            // 
            this.olvColumnLoadSpeed.HeaderFont = null;
            this.olvColumnLoadSpeed.Text = "视频业务加载速率(Mbit/s)";
            this.olvColumnLoadSpeed.Width = 150;
            // 
            // olvColumnTotalByte
            // 
            this.olvColumnTotalByte.HeaderFont = null;
            this.olvColumnTotalByte.Text = "传输字节(Byte)";
            this.olvColumnTotalByte.Width = 100;
            // 
            // olvColumnTotalTime
            // 
            this.olvColumnTotalTime.HeaderFont = null;
            this.olvColumnTotalTime.Text = "传输时间(毫秒)";
            this.olvColumnTotalTime.Width = 100;
            // 
            // olvColumnAppSpeedAvg
            // 
            this.olvColumnAppSpeedAvg.HeaderFont = null;
            this.olvColumnAppSpeedAvg.Text = "平均速率(Mbit/s)";
            this.olvColumnAppSpeedAvg.Width = 100;
            // 
            // olvColumnAppSpeedMax
            // 
            this.olvColumnAppSpeedMax.HeaderFont = null;
            this.olvColumnAppSpeedMax.Text = "峰值速率(Mbit/s)";
            this.olvColumnAppSpeedMax.Width = 100;
            // 
            // olvColumnSampleCount
            // 
            this.olvColumnSampleCount.HeaderFont = null;
            this.olvColumnSampleCount.Text = "采样点数";
            this.olvColumnSampleCount.Width = 80;
            // 
            // olvColumnRSRPAvg
            // 
            this.olvColumnRSRPAvg.HeaderFont = null;
            this.olvColumnRSRPAvg.Text = "平均RSRP";
            this.olvColumnRSRPAvg.Width = 80;
            // 
            // olvColumnSINRAvg
            // 
            this.olvColumnSINRAvg.HeaderFont = null;
            this.olvColumnSINRAvg.Text = "平均SINR";
            this.olvColumnSINRAvg.Width = 80;
            // 
            // olvColumnCellInfos
            // 
            this.olvColumnCellInfos.HeaderFont = null;
            this.olvColumnCellInfos.Text = "小区信息";
            this.olvColumnCellInfos.Width = 120;
            // 
            // ZTLTEMobileServiceAnaListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1253, 502);
            this.Controls.Add(this.ListViewMobileServiceAna);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTLTEMobileServiceAnaListForm";
            this.Text = "移动互联业务分析结果";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewMobileServiceAna)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripMenuItem miReplay;
        private BrightIdeasSoftware.TreeListView ListViewMobileServiceAna;
        private BrightIdeasSoftware.OLVColumn olvColumnStatSN;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnTypeName;
        private BrightIdeasSoftware.OLVColumn olvColumnResult;
        private BrightIdeasSoftware.OLVColumn olvColumnSignalFirstName;
        private BrightIdeasSoftware.OLVColumn olvColumnSignalFirstTime;
        private BrightIdeasSoftware.OLVColumn olvColumnTotalByte;
        private BrightIdeasSoftware.OLVColumn olvColumnTotalTime;
        private BrightIdeasSoftware.OLVColumn olvColumnAppSpeedAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnRSRPAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnSINRAvg;
        private BrightIdeasSoftware.OLVColumn olvColumnSignalSecondName;
        private BrightIdeasSoftware.OLVColumn olvColumnSignalSecondTime;
        private BrightIdeasSoftware.OLVColumn olvColumnSignalLastName;
        private BrightIdeasSoftware.OLVColumn olvColumnSignalLastTime;
        private BrightIdeasSoftware.OLVColumn olvColumnSignalSpanFirst2Second;
        private BrightIdeasSoftware.OLVColumn olvColumnSampleCount;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCallapsAll;
        private BrightIdeasSoftware.OLVColumn olvColumnCellInfos;
        private BrightIdeasSoftware.OLVColumn olvColumnReBufferCount;
        private BrightIdeasSoftware.OLVColumn olvColumnReBufferTime;
        private BrightIdeasSoftware.OLVColumn olvColumnLoadSpeed;
        private BrightIdeasSoftware.OLVColumn olvColumnSignalSpanSecond2Last;
        private BrightIdeasSoftware.OLVColumn olvColumnFailReason;
        private BrightIdeasSoftware.OLVColumn olvColumnURL;
        private BrightIdeasSoftware.OLVColumn olvColumnAppSpeedMax;

    }
}