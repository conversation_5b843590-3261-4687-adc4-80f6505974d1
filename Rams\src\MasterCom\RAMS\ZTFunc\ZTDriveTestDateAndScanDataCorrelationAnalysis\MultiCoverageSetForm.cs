﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class MultiCoverageSetForm : DevExpress.XtraEditors.XtraForm
    {
        public MultiCoverageSetForm()
        {
            InitializeComponent();

            cbxCoFreqType.Properties.Items.Add("ARFCN & ARFCNList");
            cbxCoFreqType.Properties.Items.Add("ARFCN Only");
            cbxCoFreqType.Properties.Items.Add("ARFCNList Only");

            cbxMultiCoverType.Properties.Items.Add("相对重叠度");
            cbxMultiCoverType.Properties.Items.Add("绝对重叠度");
            cbxMultiCoverType.Properties.Items.Add("综合重叠度");

            if (cbxCoFreqType.Properties.Items.Count > 1)
            {
                cbxCoFreqType.SelectedIndex = 1;
            }
            if (cbxMultiCoverType.Properties.Items.Count>1)
            {
                cbxMultiCoverType.SelectedIndex = 0;
            }
        }

        public void GetSettingFilterRet(MultiCoverageCondition condition)
        {
            condition.SetRxlevDiff = (int)numRxLevDValue.Value;
            condition.SetRxlev = (int)numRxLevThreshold.Value;
            condition.CoFreq = chkCoFreq.Checked;
            condition.InterferenceType = (MapForm.DisplayInterferenceType)cbxCoFreqType.SelectedIndex;
            condition.CoverDegreeType = (CoverDegreeType)cbxMultiCoverType.SelectedIndex;
            condition.QualityDividingLine = (int)numQualityDividingLine.Value;
            condition.MultiCoverageDividingLine = (int)numMultiCoverageDividingLine.Value;
            condition.Channel = (string)radioGroupChannel.EditValue;
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleButton2_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void chkCoFreq_CheckedChanged(object sender, EventArgs e)
        {
            cbxCoFreqType.Enabled = chkCoFreq.Checked;
        }
    }

    public class MultiCoverageCondition
    {
        public int SetRxlevDiff { get; set; }
        public int SetRxlev { get; set; }
        public bool CoFreq { get; set; }
        public MapForm.DisplayInterferenceType InterferenceType { get; set; } = MapForm.DisplayInterferenceType.BCCH_CPI;
        public CoverDegreeType CoverDegreeType { get; set; } = CoverDegreeType.Relative;
        public int QualityDividingLine { get; set; }
        public int MultiCoverageDividingLine { get; set; }
        public string Channel { get; set; }
    }
}
