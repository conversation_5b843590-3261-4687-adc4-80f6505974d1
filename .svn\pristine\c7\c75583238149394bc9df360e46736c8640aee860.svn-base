﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using MasterCom.RAMS.Func;
namespace MasterCom.RAMS.Compare
{
    public enum TestResult
    {
        Better,//较好
        Worse,//较差
        AllGood,//都很好
        AllBad,//都很差
        Middle,//均等
        Unknown//未测出
    }
    public class DateResult
    {
        public int unit_id { get; set; }
        public int dateValue { get; set; }
        public string combineResult { get; set; }

        public static DateResult FillFrom(MasterCom.RAMS.Net.Content content)
        {
            DateResult dr = new DateResult();
            dr.unit_id = content.GetParamInt();
            dr.dateValue = content.GetParamInt();
            dr.combineResult = content.GetParamString();
            return dr;
        }

        public TestResult getTestResult(string carrierPair, string servPair, int kpiPos)
        {
            bool bNeedReverse = false;

            int Pos = combineResult.IndexOf(carrierPair + "_" + servPair + "_");
            if (Pos >= 0)
            {
                string str = "";
                if (combineResult.Length >= 11)
                {
                    str = combineResult.Substring(8, 3);       
                }
                else { str = combineResult.Substring(8, 1); }

                if (kpiPos > str.Length)
                {
                    return TestResult.Unknown;  //length error 
                }
                else
                {
                    str = str.Substring(kpiPos, 1);
                }

                return RevertStatus(str, bNeedReverse);

            }
            return TestResult.Unknown;
        }

        public TestResult RevertStatus(string status, bool bNeedReverse)
        {
            TestResult stat = (TestResult)Convert.ToInt32(status);

            if(bNeedReverse)
            {
                if(stat == TestResult.Better)
                {
                    return TestResult.Worse;
                }
                else if(stat == TestResult.Worse)
                {
                    return TestResult.Better;
                }
            }

            return stat;
        }
    }

    public class CompUnit
    {
        public int id { get; set; }
        /// <summary>
        /// 0 未计算
        /// 1 绝对优
        /// 2 比较优
        /// 3 绝对劣势
        /// 4 比较劣势
        /// 5 其它
        /// </summary>
        public int status { get; set; }
        /// <summary>
        /// 1 Good
        /// 2 Bad
        /// 0 else
        /// </summary>
        public string type { get; set; }
        public int GoodBadStatus
        {
            get
            {
                if (status == 1 || status == 2)
                {
                    return 1;
                }
                else if (status == 3 || status == 4)
                {
                    return 2;
                }
                else
                {
                    return 0;
                }
            }
        }
        public bool combined { get; set; } = false;
        public double ltlongitude { get; set; }
        public double ltlatitude { get; set; }
        public double brlongitude { get; set; }
        public double brlatitude { get; set; }
        public double MidLongitude
        {
            get { return (ltlongitude + brlongitude) / 2; }
        }
        public double MidLatitude
        {
            get { return (ltlatitude + brlatitude) / 2; }
        }

        public int betterdays { get; set; }
        public int worsedays { get; set; }
        public List<DateResult> testDates { get; set; } = new List<DateResult>();

        public Color color { get; set; }
        /// <summary>
        /// 0 其它
        /// 1 绝对优
        /// 2 比较优
        /// 3 比较劣势
        /// 4 绝对劣势
        /// 
        /// </summary>
        public int colorValue { get; set; }

        public bool top { get; set; } = false;
        public bool left { get; set; } = false;
        public bool right { get; set; } = false;
        public bool bottom { get; set; } = false;

        internal static CompUnit FillFrom(MasterCom.RAMS.Net.Content content)
        {
            CompUnit cu = new CompUnit();
            cu.id = content.GetParamInt();
            cu.status = content.GetParamInt();
            cu.status = 0;//used for calc location
            cu.ltlongitude = content.GetParamDouble();
            cu.ltlatitude = content.GetParamDouble();
            cu.brlongitude = content.GetParamDouble();
            cu.brlatitude = content.GetParamDouble();
            cu.betterdays = content.GetParamInt();
            cu.worsedays = content.GetParamInt();
            return cu;
        }
    }
}
