﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRMainCellLastOccupyInfo
    {
        public int Sn { get; set; }
        public FileInfo File { get; set; }
        public string FileName
        {
            get { return File.Name; }
        }
        public List<NRMainCellLastOccupyCellInfo> CellInfoList { get; set; } = new List<NRMainCellLastOccupyCellInfo>();

        public NRMainCellLastOccupyInfo(FileInfo file)
        {
            this.File = file;
        }

        public void Calculate()
        {
            foreach (var item in CellInfoList)
            {
                item.Calculate();
            }
        }
    }

    public class NRMainCellLastOccupyCellInfo
    {
        public int Sn { get; set; }

        public DataInfo RsrpInfo { get; set; } = new DataInfo();
        public DataInfo SinrInfo { get; set; } = new DataInfo();
        public int SmallRSRPNum { get; set; }
        public int SmallSINRNum { get; set; }
        public int? EARFCN { get; set; }
        public int? PCI { get; set; }
        public int? TAC { get; set; }
        public long? NCI { get; set; }

        public List<TestPoint> TestPntList { get; set; } = new List<TestPoint>();

        public string CellName { get; set; }
        public string Duration { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public string Time { get; set; }

        private NRMainCellLastOccupyCondition Condition { get; set; }

        public NRMainCellLastOccupyCellInfo(NRMainCellLastOccupyCondition condition)
        {
            this.Condition = condition;
        }

        public void AddPnt(TestPoint tp)
        {
            TestPntList.Add(tp);
        }

        public void Calculate()
        {
            foreach (TestPoint tp in TestPntList)
            {
                float? rsrp = NRTpHelper.NrTpManager.GetSCellRsrp(tp);
                RsrpInfo.Add(rsrp);

                if (rsrp != null && rsrp <= Condition.RSRP)
                {
                    SmallRSRPNum++;
                }

                float? sinr = NRTpHelper.NrTpManager.GetSCellSinr(tp);
                SinrInfo.Add(sinr);
                if (sinr != null && sinr <= Condition.SINR)
                {
                    SmallSINRNum++;
                }
            }
            RsrpInfo.Calculate();
            SinrInfo.Calculate();

            TestPoint tStart = TestPntList[0];
            TestPoint tEnd = TestPntList[TestPntList.Count - 1];
            Duration = Math.Round(tEnd.DateTime.Subtract(tStart.DateTime).TotalSeconds, 2).ToString();
            Longitude = tStart.Longitude;
            Latitude = tStart.Latitude;
            Time = tStart.DateTimeStringWithMillisecond;
            EARFCN = (int?)NRTpHelper.NrTpManager.GetEARFCN(tStart);
            PCI = (int?)NRTpHelper.NrTpManager.GetPCI(tStart);
            TAC = (int?)NRTpHelper.NrTpManager.GetTAC(tStart);
            NCI = (long?)NRTpHelper.NrTpManager.GetNCI(tStart);
        }

        public class DataInfo
        {
            public double Sum { get; set; }
            public int Count { get; set; }
            public double Avg { get; set; }

            public void Add(float? data)
            {
                if (data != null)
                {
                    Count++;
                    Sum += (double)data;
                }
            }

            public void Calculate()
            {
                if (Count > 0)
                {
                    Avg = Math.Round(Sum / Count, 2);
                }
            }
        }
    }
}
