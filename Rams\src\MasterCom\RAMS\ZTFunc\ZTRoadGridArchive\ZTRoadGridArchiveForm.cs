﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTRoadGridArchiveForm : MinCloseForm
    {
        public ZTRoadGridArchiveForm()
             : base(MainModel.GetInstance())
        {
            InitializeComponent();
            mapForm = MainModel.MainForm.GetMapForm();
            init();
        }

        ZTRoadGridArchiveCondition settingCondition;
        List<ZTRoadGridArchiveRes> resList;
        List<ZTRoadGridArchiveSumRes> roadSumInfoList;

        MapForm mapForm;
        RoadGridArchiveLayer layer;

        private void init()
        {
            cmbRendingIndex.SelectedIndexChanged -= cmbRendingIndex_SelectedIndexChanged;
            cmbRendingIndex.Properties.Items.Add(RoadGridArchiveColorRange.Instance.EventColorRange.RangeName);
            cmbRendingIndex.Properties.Items.Add(RoadGridArchiveColorRange.Instance.CellColorRange.RangeName);
            cmbRendingIndex.SelectedItem = RoadGridArchiveColorRange.Instance.EventColorRange.RangeName;
            cmbRendingIndex.SelectedIndexChanged += cmbRendingIndex_SelectedIndexChanged;
        }

        private void cmbRendingIndex_SelectedIndexChanged(object sender, EventArgs e)
        {
            RoadGridArchiveLayer.RenderingIndex curRangeType = RoadGridArchiveLayer.RenderingIndex.EventNum;
            if (cmbRendingIndex.SelectedItem.ToString() == RoadGridArchiveColorRange.Instance.EventColorRange.RangeName)
            {
                curRangeType = RoadGridArchiveLayer.RenderingIndex.EventNum;
                RoadGridArchiveColorRange.Instance.ChangeRange(RoadGridArchiveColorRange.Instance.EventColorRange);
            }
            else if (cmbRendingIndex.SelectedItem.ToString() == RoadGridArchiveColorRange.Instance.CellColorRange.RangeName)
            {
                curRangeType = RoadGridArchiveLayer.RenderingIndex.CellNum;
                RoadGridArchiveColorRange.Instance.ChangeRange(RoadGridArchiveColorRange.Instance.CellColorRange);
            }
            layer.InitColor(curRangeType);
            mapForm.updateMap();
            MainModel.RefreshLegend();
        }

        public void FillData(ZTRoadGridArchiveCondition settingCondition, List<ZTRoadGridArchiveRes> resList, List<ZTRoadGridArchiveSumRes> roadSumInfoList)
        {
            this.settingCondition = settingCondition;
            this.resList = resList;
            this.roadSumInfoList = roadSumInfoList;
            if (settingCondition.TypeContiodion == ZTRoadGridArchiveCondition.Type.ByRound)
            {
                gridColumnSTime.Visible = false;
                gridColumnETime.Visible = false;
                groupCur.Text = "当前轮次占用小区";
                groupHis.Text = "历史轮次占用小区";
            }
            else
            {
                gridColumnRound.Visible = false;
                gridColumnRoundCount.Visible = false;
                groupCur.Text = "当前时段占用小区";
                groupHis.Text = "对比时段占用小区";
            }

            gridControlSum.DataSource = roadSumInfoList;
            gridControlDetail.DataSource = resList;
            refreshLayer();
        }

        private void bandedGvRoad_Click(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView curGv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            int[] rows = curGv.GetSelectedRows();
            if (rows.Length == 0)
            {
                return;
            }

            object row = curGv.GetRow(rows[0]);
            if (row is ZTRoadGridArchiveRes)
            {
                ZTRoadGridArchiveRes res = row as ZTRoadGridArchiveRes;
                gridControlCur.DataSource = res.CurStatCellRoadInfo;
                gridControlHis.DataSource = res.HisStatCellRoadInfo;
                
                layer.CurSelectedGrid = res;
                gotoSelectedView(res);
            }
        }

        private void refreshLayer()
        {
            WaitBox.Show("正在绘制图层...", refreshLayerThread);
        }

        private void refreshLayerThread()
        {
            layer = mapForm.GetTempLayerBase(typeof(RoadGridArchiveLayer)) as RoadGridArchiveLayer;
            layer.SelectedGridChanged -= selectedGridChanged;
            RoadGridArchiveLayer.RoadGridInfos = resList;
            mapForm.updateMap();
            MainModel.RefreshLegend();
            layer.SelectedGridChanged += selectedGridChanged;

            System.Threading.Thread.Sleep(200);
            if (resList.Count > 0)
            {
                gotoSelectedView(resList[0]);
            }
            WaitBox.Close();
        }

        private void selectedGridChanged(object sender, EventArgs e)
        {
            if (layer.CurSelectedGrid != null)
            {
                int index = resList.IndexOf(layer.CurSelectedGrid);
                int realIndex = bandedGvRoad.DataController.GetControllerRow(index);
                if (realIndex >= 0)
                {
                    bandedGvRoad.ClearSelection();
                    bandedGvRoad.FocusedRowHandle = realIndex;
                    bandedGvRoad.SelectRow(realIndex);
                }
                gotoSelectedView(layer.CurSelectedGrid);
            }
        }

        /// <summary>
        /// 图层跳转到数据所在位置
        /// </summary>
        protected virtual void gotoSelectedView(ZTRoadGridArchiveRes roadGrid)
        {
            double fLong = roadGrid.CenterLongitude;
            double fLat = roadGrid.CenterLatitude;
            MainModel.MainForm.GetMapForm().GoToView(fLong, fLat);
        }

        #region 导出
        private void ToolStripExport_Click(object sender, EventArgs e)
        {
            List<string> sheetNames = new List<string>();
            sheetNames.Add("汇总");
            sheetNames.Add("详情");
            List<List<NPOIRow>> rowLists = new List<List<NPOIRow>>();
            List<NPOIRow> sumRowList = getSumNPOIRow();
            rowLists.Add(sumRowList);
            List<NPOIRow> detailRowList = getDetailNPOIRow();
            rowLists.Add(detailRowList);

            ExcelNPOIManager.ExportToExcel(rowLists, sheetNames);
        }

        protected List<NPOIRow> getSumNPOIRow()
        {
            List<NPOIRow> rowList = new List<NPOIRow>();
            //row为标题
            NPOIRow titleRow = new NPOIRow();
            titleRow.AddCellValue("道路名");
            titleRow.AddCellValue("道路长度");
            titleRow.AddCellValue("分公司");
            titleRow.AddCellValue("类型");
            if (settingCondition.TypeContiodion == ZTRoadGridArchiveCondition.Type.ByRound)
            {
                titleRow.AddCellValue("测试轮次");
                titleRow.AddCellValue("轮次数");
            }
            else
            {
                titleRow.AddCellValue("开始时间");
                titleRow.AddCellValue("结束时间");
            }
            titleRow.AddCellValue("占用小区数量");
            titleRow.AddCellValue("异常事件总次数");
            titleRow.AddCellValue("异常事件-未接通");
            titleRow.AddCellValue("异常事件-掉话");
            titleRow.AddCellValue("异常事件-MOS3.0差");
            titleRow.AddCellValue("异常事件-RSRP差");
            titleRow.AddCellValue("异常事件-SINR差");
            titleRow.AddCellValue("异常事件-RRC重建");
            titleRow.AddCellValue("异常事件-速率低");

            titleRow.AddCellValue("网络原因-弱覆盖");
            titleRow.AddCellValue("网络原因-模三干扰");
            titleRow.AddCellValue("网络原因-同频干扰");
            titleRow.AddCellValue("网络原因-重叠覆盖");
            titleRow.AddCellValue("网络原因-过覆盖");
            titleRow.AddCellValue("网络原因-小区故障");
            titleRow.AddCellValue("网络原因-断站");
            titleRow.AddCellValue("网络原因-拆站");
            titleRow.AddCellValue("网络原因-容量");

            rowList.Add(titleRow);

            List<ZTRoadGridArchiveSumRes> sumRes = gridControlSum.DataSource as List<ZTRoadGridArchiveSumRes>;
            AddSumRowInfo(rowList, sumRes);
            return rowList;
        }

        private void AddSumRowInfo(List<NPOIRow> rowList, List<ZTRoadGridArchiveSumRes> sumRes)
        {
            foreach (ZTRoadGridArchiveSumRes item in sumRes)
            {
                NPOIRow row = new NPOIRow();
                if (item != null)
                {
                    row.AddCellValue(item.RoadName);
                    row.AddCellValue(item.RoadLength);
                    row.AddCellValue(item.CompanyName);
                    row.AddCellValue(item.AreaTypeName);
                    if (settingCondition.TypeContiodion == ZTRoadGridArchiveCondition.Type.ByRound)
                    {
                        row.AddCellValue(item.Round);
                        row.AddCellValue(item.RoundCount);
                    }
                    else
                    {
                        row.AddCellValue(item.StartDate);
                        row.AddCellValue(item.EndDate);
                    }
                    row.AddCellValue(item.CellCount);
                    row.AddCellValue(item.EventRoadInfo.TotalEventNum);
                    row.AddCellValue(item.EventRoadInfo.NotConnected);
                    row.AddCellValue(item.EventRoadInfo.DroppedCalls);
                    row.AddCellValue(item.EventRoadInfo.WeakMos);
                    row.AddCellValue(item.EventRoadInfo.WeakRsrp);
                    row.AddCellValue(item.EventRoadInfo.WeakSinr);
                    row.AddCellValue(item.EventRoadInfo.RRCRebuild);
                    row.AddCellValue(item.EventRoadInfo.Downless);

                    row.AddCellValue(item.RoadReasonInfo.WeakCover);
                    row.AddCellValue(item.RoadReasonInfo.ModRoad);
                    row.AddCellValue(item.RoadReasonInfo.CoFreq);
                    row.AddCellValue(item.RoadReasonInfo.MultiCoverage);
                    row.AddCellValue(item.RoadReasonInfo.CoverLap);
                    row.AddCellValue(item.RoadReasonInfo.CellFailure);
                    row.AddCellValue(item.RoadReasonInfo.BtsInterruption);
                    row.AddCellValue(item.RoadReasonInfo.BtsTearDown);
                    row.AddCellValue(item.RoadReasonInfo.LoadProblem);
                    rowList.Add(row);
                }
            }
        }

        protected List<NPOIRow> getDetailNPOIRow()
        {
            List<NPOIRow> rowList = new List<NPOIRow>();
            //row为标题
            NPOIRow titleRow = new NPOIRow();
            titleRow.AddCellValue("道路名");
            titleRow.AddCellValue("路段标识");
            titleRow.AddCellValue("路段名");
            titleRow.AddCellValue("经度");
            titleRow.AddCellValue("纬度");
            titleRow.AddCellValue("分公司");
            titleRow.AddCellValue("类型");
            titleRow.AddCellValue("占用小区数量");

            titleRow.AddCellValue("异常事件总次数");
            titleRow.AddCellValue("异常事件-未接通");
            titleRow.AddCellValue("异常事件-掉话");
            titleRow.AddCellValue("异常事件-MOS3.0差");
            titleRow.AddCellValue("异常事件-RSRP差");
            titleRow.AddCellValue("异常事件-SINR差");
            titleRow.AddCellValue("异常事件-RRC重建");
            titleRow.AddCellValue("异常事件-速率低");

            titleRow.AddCellValue("网络原因-弱覆盖");
            titleRow.AddCellValue("网络原因-模三干扰");
            titleRow.AddCellValue("网络原因-同频干扰");
            titleRow.AddCellValue("网络原因-重叠覆盖");
            titleRow.AddCellValue("网络原因-过覆盖");
            titleRow.AddCellValue("网络原因-小区故障");
            titleRow.AddCellValue("网络原因-断站");
            titleRow.AddCellValue("网络原因-拆站");
            titleRow.AddCellValue("网络原因-容量");

            string curPrefix = "";
            string hisPrefix = "";
            if (settingCondition.TypeContiodion == ZTRoadGridArchiveCondition.Type.ByRound)
            {
                curPrefix = "当前轮次";
                hisPrefix = "历史轮次";
            }
            else
            {
                curPrefix = "当前时段";
                hisPrefix = "对比时段";
            }
            titleRow.AddCellValue(curPrefix + "小区序号");
            titleRow.AddCellValue(curPrefix + "占用小区");
            titleRow.AddCellValue(curPrefix + "占用次数");
            titleRow.AddCellValue(hisPrefix + "小区序号");
            titleRow.AddCellValue(hisPrefix + "占用小区");
            titleRow.AddCellValue(hisPrefix + "占用次数");

            rowList.Add(titleRow);

            List<ZTRoadGridArchiveRes> sumRes = gridControlDetail.DataSource as List<ZTRoadGridArchiveRes>;
            AddDetailRowInfo(rowList, sumRes);
            return rowList;
        }

        private void AddDetailRowInfo(List<NPOIRow> rowList, List<ZTRoadGridArchiveRes> sumRes)
        {
            foreach (ZTRoadGridArchiveRes item in sumRes)
            {
                NPOIRow row = new NPOIRow();
                if (item != null)
                {
                    row.AddCellValue(item.RoadName);
                    row.AddCellValue(item.AreaID);
                    row.AddCellValue(item.AreaName);
                    row.AddCellValue(item.CenterLongitude);
                    row.AddCellValue(item.CenterLatitude);
                    row.AddCellValue(item.CompanyName);
                    row.AddCellValue(item.AreaTypeName);
                    row.AddCellValue(item.CellCount);

                    row.AddCellValue(item.CurEventRoadInfo.TotalEventNum);
                    row.AddCellValue(item.CurEventRoadInfo.NotConnected);
                    row.AddCellValue(item.CurEventRoadInfo.DroppedCalls);
                    row.AddCellValue(item.CurEventRoadInfo.WeakMos);
                    row.AddCellValue(item.CurEventRoadInfo.WeakRsrp);
                    row.AddCellValue(item.CurEventRoadInfo.WeakSinr);
                    row.AddCellValue(item.CurEventRoadInfo.RRCRebuild);
                    row.AddCellValue(item.CurEventRoadInfo.Downless);

                    row.AddCellValue(item.CurRoadReasonInfo.WeakCover);
                    row.AddCellValue(item.CurRoadReasonInfo.ModRoad);
                    row.AddCellValue(item.CurRoadReasonInfo.CoFreq );
                    row.AddCellValue(item.CurRoadReasonInfo.MultiCoverage );
                    row.AddCellValue(item.CurRoadReasonInfo.CoverLap );
                    row.AddCellValue(item.CurRoadReasonInfo.CellFailure );
                    row.AddCellValue(item.CurRoadReasonInfo.BtsInterruption );
                    row.AddCellValue(item.CurRoadReasonInfo.BtsTearDown);
                    row.AddCellValue(item.CurRoadReasonInfo.LoadProblem);

                    int maxSubRow = 0;
                    if (item.CurStatCellRoadInfo.Count > item.HisStatCellRoadInfo.Count)
                    {
                        maxSubRow = item.CurStatCellRoadInfo.Count;
                    }
                    else
                    {
                        maxSubRow = item.HisStatCellRoadInfo.Count;
                    }

                    AddDetailSubRowInfo(item, row, maxSubRow);

                    rowList.Add(row);
                }
            }
        }

        private void AddDetailSubRowInfo(ZTRoadGridArchiveRes item, NPOIRow row, int maxSubRow)
        {
            if (maxSubRow <= 0)
            {
                return;
            }
            for (int i = 0; i < maxSubRow; i++)
            {
                NPOIRow subRow = new NPOIRow();
                if (i < item.CurStatCellRoadInfo.Count)
                {
                    StatCellRoadInfo curData = item.CurStatCellRoadInfo[i];
                    subRow.AddCellValue(curData.SN);
                    subRow.AddCellValue(curData.CellName);
                    subRow.AddCellValue(curData.Times);
                }
                else
                {
                    subRow.AddCellValue("");
                    subRow.AddCellValue("");
                    subRow.AddCellValue("");
                }

                if (i < item.HisStatCellRoadInfo.Count)
                {
                    StatCellRoadInfo hisData = item.HisStatCellRoadInfo[i];
                    subRow.AddCellValue(hisData.SN);
                    subRow.AddCellValue(hisData.CellName);
                    subRow.AddCellValue(hisData.Times);
                }
                else
                {
                    subRow.AddCellValue("");
                    subRow.AddCellValue("");
                    subRow.AddCellValue("");
                }
                row.AddSubRow(subRow);
            }
        }
        #endregion

        protected override void MinCloseForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            RoadGridArchiveLayer.RoadGridInfos = new List<ZTRoadGridArchiveRes>();
            base.MinCloseForm_FormClosing(sender, e);
        }

        private void btnRangeSetting_Click(object sender, EventArgs e)
        {
            MasterCom.Grid.ColorRangeMngDlg mngDlg = new MasterCom.Grid.ColorRangeMngDlg();
            mngDlg.FixMinMax(0, 999999);
            mngDlg.MakeRangeModeOnly();
            mngDlg.FillColorRanges(RoadGridArchiveColorRange.Instance.CurColorRanges);
            if (DialogResult.OK == mngDlg.ShowDialog(this))
            {
                resetColorRange(mngDlg);
                RoadGridArchiveColorRange.Instance.SaveConfig(false);
                MainModel.RefreshLegend();
            }
        }

        private void resetColorRange(MasterCom.Grid.ColorRangeMngDlg mngDlg)
        {
            if (cmbRendingIndex.SelectedItem.ToString() == RoadGridArchiveColorRange.Instance.EventColorRange.RangeName)
            {
                RoadGridArchiveColorRange.Instance.EventColorRange.ColorRanges = mngDlg.ColorRanges;
                RoadGridArchiveColorRange.Instance.ChangeRange(RoadGridArchiveColorRange.Instance.EventColorRange);
            }
            else if (cmbRendingIndex.SelectedItem.ToString() == RoadGridArchiveColorRange.Instance.CellColorRange.RangeName)
            {
                RoadGridArchiveColorRange.Instance.CellColorRange.ColorRanges = mngDlg.ColorRanges;
                RoadGridArchiveColorRange.Instance.ChangeRange(RoadGridArchiveColorRange.Instance.CellColorRange);
            }
        }
    }
}
