﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func.CqtAddressManagement;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public class ShowCQTAddrPlanMngForm : ShowFuncForm
    {
        public ShowCQTAddrPlanMngForm(MainModel mm)
            : base(mm)
        { }
        BatchImportCQTImgForm form = null;
        protected override void showForm()
        {
            if (form == null || form.IsDisposed)
            {
                form = new BatchImportCQTImgForm(MainModel);
            }
            if (!form.Visible)
            {
                form.Show(MainModel.MainForm);
            }
        }

        public override string Name
        {
            get { return "CQT地点图片管理窗口"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 21000, 21002, this.Name);
        }
    }
}
