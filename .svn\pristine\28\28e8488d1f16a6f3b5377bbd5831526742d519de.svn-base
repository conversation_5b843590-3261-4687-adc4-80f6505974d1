﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRWeakMosReasonForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlSum = new DevExpress.XtraGrid.GridControl();
            this.ctxMenuSum = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripSumExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridViewSum = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.gridControlDetail = new DevExpress.XtraGrid.GridControl();
            this.ctxMenuDetail = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripDetailExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.gridViewDetail = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand2 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn6 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand1 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn7 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn5 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn19 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand3 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn12 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn13 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn14 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn15 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn16 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn18 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn19 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn17 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn20 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn21 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand4 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn6 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn8 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn9 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn7 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn10 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn11 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn22 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn24 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn25 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn23 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn26 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn27 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlSum)).BeginInit();
            this.ctxMenuSum.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewSum)).BeginInit();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlDetail)).BeginInit();
            this.ctxMenuDetail.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewDetail)).BeginInit();
            this.SuspendLayout();
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(0, 0);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(1088, 658);
            this.xtraTabControl1.TabIndex = 2;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.gridControlSum);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(1081, 628);
            this.xtraTabPage1.Text = "原因分析概况";
            // 
            // gridControlSum
            // 
            this.gridControlSum.ContextMenuStrip = this.ctxMenuSum;
            this.gridControlSum.Dock = System.Windows.Forms.DockStyle.Fill;
            gridLevelNode1.RelationName = "Level1";
            this.gridControlSum.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.gridControlSum.Location = new System.Drawing.Point(0, 0);
            this.gridControlSum.MainView = this.gridViewSum;
            this.gridControlSum.Name = "gridControlSum";
            this.gridControlSum.Size = new System.Drawing.Size(1081, 628);
            this.gridControlSum.TabIndex = 0;
            this.gridControlSum.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewSum});
            // 
            // ctxMenuSum
            // 
            this.ctxMenuSum.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripSumExportExcel});
            this.ctxMenuSum.Name = "contextMenuStrip";
            this.ctxMenuSum.Size = new System.Drawing.Size(151, 26);
            // 
            // toolStripSumExportExcel
            // 
            this.toolStripSumExportExcel.Name = "toolStripSumExportExcel";
            this.toolStripSumExportExcel.Size = new System.Drawing.Size(150, 22);
            this.toolStripSumExportExcel.Text = "导出到Excel...";
            this.toolStripSumExportExcel.Click += new System.EventHandler(this.toolStripSumExportExcel_Click);
            // 
            // gridViewSum
            // 
            this.gridViewSum.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridViewSum.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewSum.Appearance.Row.Options.UseTextOptions = true;
            this.gridViewSum.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewSum.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn37,
            this.gridColumn2,
            this.gridColumn22,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5});
            this.gridViewSum.GridControl = this.gridControlSum;
            this.gridViewSum.Name = "gridViewSum";
            this.gridViewSum.OptionsBehavior.Editable = false;
            this.gridViewSum.OptionsView.AllowCellMerge = true;
            this.gridViewSum.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn37
            // 
            this.gridColumn37.Caption = "总Mos采样点数";
            this.gridColumn37.FieldName = "WeakMosInfo.TotalCount";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn37.Visible = true;
            this.gridColumn37.VisibleIndex = 0;
            this.gridColumn37.Width = 113;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "弱Mos采样点数";
            this.gridColumn2.FieldName = "WeakMosInfo.Count";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            this.gridColumn2.Width = 116;
            // 
            // gridColumn22
            // 
            this.gridColumn22.Caption = "弱Mos点占比(%)";
            this.gridColumn22.FieldName = "WeakMosInfo.Avg";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.Visible = true;
            this.gridColumn22.VisibleIndex = 2;
            this.gridColumn22.Width = 133;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "原因分类";
            this.gridColumn3.FieldName = "Reason";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 3;
            this.gridColumn3.Width = 131;
            // 
            // gridColumn4
            // 
            this.gridColumn4.Caption = "该类原因弱Mos点数";
            this.gridColumn4.FieldName = "ReasonMosInfo.Count";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn4.Visible = true;
            this.gridColumn4.VisibleIndex = 4;
            this.gridColumn4.Width = 131;
            // 
            // gridColumn5
            // 
            this.gridColumn5.Caption = "该类原因弱Mos点占比(%)";
            this.gridColumn5.FieldName = "ReasonMosInfo.Avg";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn5.Visible = true;
            this.gridColumn5.VisibleIndex = 5;
            this.gridColumn5.Width = 146;
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.gridControlDetail);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(1081, 628);
            this.xtraTabPage2.Text = "问题详情";
            // 
            // gridControlDetail
            // 
            this.gridControlDetail.ContextMenuStrip = this.ctxMenuDetail;
            this.gridControlDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlDetail.Location = new System.Drawing.Point(0, 0);
            this.gridControlDetail.MainView = this.gridViewDetail;
            this.gridControlDetail.Name = "gridControlDetail";
            this.gridControlDetail.Size = new System.Drawing.Size(1081, 628);
            this.gridControlDetail.TabIndex = 2;
            this.gridControlDetail.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewDetail});
            // 
            // ctxMenuDetail
            // 
            this.ctxMenuDetail.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripDetailExportExcel,
            this.toolStripReplay});
            this.ctxMenuDetail.Name = "contextMenuStripProblem";
            this.ctxMenuDetail.Size = new System.Drawing.Size(199, 48);
            // 
            // toolStripDetailExportExcel
            // 
            this.toolStripDetailExportExcel.Name = "toolStripDetailExportExcel";
            this.toolStripDetailExportExcel.Size = new System.Drawing.Size(198, 22);
            this.toolStripDetailExportExcel.Text = "导出到Excel...";
            this.toolStripDetailExportExcel.Click += new System.EventHandler(this.toolStripDetailExportExcel_Click);
            // 
            // toolStripReplay
            // 
            this.toolStripReplay.Name = "toolStripReplay";
            this.toolStripReplay.Size = new System.Drawing.Size(198, 22);
            this.toolStripReplay.Text = "回放该Mos点前后信息";
            this.toolStripReplay.Click += new System.EventHandler(this.toolStripReplay_Click);
            // 
            // gridViewDetail
            // 
            this.gridViewDetail.Appearance.BandPanel.Options.UseTextOptions = true;
            this.gridViewDetail.Appearance.BandPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewDetail.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridViewDetail.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewDetail.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridViewDetail.Appearance.Row.Options.UseTextOptions = true;
            this.gridViewDetail.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridViewDetail.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand2,
            this.gridBand1,
            this.gridBand3,
            this.gridBand4});
            this.gridViewDetail.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.gridColumn6,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn8,
            this.gridColumn7,
            this.gridColumn9,
            this.bandedGridColumn4,
            this.bandedGridColumn5,
            this.gridColumn19,
            this.gridColumn20,
            this.bandedGridColumn1,
            this.bandedGridColumn2,
            this.bandedGridColumn12,
            this.bandedGridColumn13,
            this.bandedGridColumn3,
            this.bandedGridColumn14,
            this.bandedGridColumn15,
            this.bandedGridColumn16,
            this.bandedGridColumn18,
            this.bandedGridColumn19,
            this.bandedGridColumn17,
            this.bandedGridColumn20,
            this.bandedGridColumn21,
            this.bandedGridColumn6,
            this.bandedGridColumn8,
            this.bandedGridColumn9,
            this.bandedGridColumn7,
            this.bandedGridColumn10,
            this.bandedGridColumn11,
            this.bandedGridColumn22,
            this.bandedGridColumn24,
            this.bandedGridColumn25,
            this.bandedGridColumn23,
            this.bandedGridColumn26,
            this.bandedGridColumn27});
            this.gridViewDetail.GridControl = this.gridControlDetail;
            this.gridViewDetail.Name = "gridViewDetail";
            this.gridViewDetail.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridViewDetail.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridViewDetail.OptionsBehavior.Editable = false;
            this.gridViewDetail.OptionsCustomization.AllowBandMoving = false;
            this.gridViewDetail.OptionsCustomization.AllowBandResizing = false;
            this.gridViewDetail.OptionsCustomization.AllowGroup = false;
            this.gridViewDetail.OptionsFilter.AllowColumnMRUFilterList = false;
            this.gridViewDetail.OptionsFilter.AllowFilterEditor = false;
            this.gridViewDetail.OptionsFilter.AllowMRUFilterList = false;
            this.gridViewDetail.OptionsFilter.ShowAllTableValuesInFilterPopup = true;
            this.gridViewDetail.OptionsFilter.UseNewCustomFilterDialog = true;
            this.gridViewDetail.OptionsSelection.MultiSelect = true;
            this.gridViewDetail.OptionsView.ColumnAutoWidth = false;
            this.gridViewDetail.OptionsView.ShowDetailButtons = false;
            this.gridViewDetail.OptionsView.ShowGroupPanel = false;
            this.gridViewDetail.DoubleClick += new System.EventHandler(this.gridViewDetail_DoubleClick);
            // 
            // gridBand2
            // 
            this.gridBand2.Caption = "基本信息";
            this.gridBand2.Columns.Add(this.gridColumn6);
            this.gridBand2.Columns.Add(this.gridColumn12);
            this.gridBand2.Columns.Add(this.gridColumn13);
            this.gridBand2.Columns.Add(this.gridColumn8);
            this.gridBand2.MinWidth = 20;
            this.gridBand2.Name = "gridBand2";
            this.gridBand2.Width = 412;
            // 
            // gridColumn6
            // 
            this.gridColumn6.Caption = "序号";
            this.gridColumn6.FieldName = "SN";
            this.gridColumn6.Name = "gridColumn6";
            this.gridColumn6.Visible = true;
            this.gridColumn6.Width = 49;
            // 
            // gridColumn12
            // 
            this.gridColumn12.Caption = "所属网格";
            this.gridColumn12.FieldName = "GridName";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Visible = true;
            this.gridColumn12.Width = 91;
            // 
            // gridColumn13
            // 
            this.gridColumn13.Caption = "所属路段";
            this.gridColumn13.FieldName = "RoadName";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Visible = true;
            this.gridColumn13.Width = 98;
            // 
            // gridColumn8
            // 
            this.gridColumn8.Caption = "文件名";
            this.gridColumn8.FieldName = "FileName";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.Visible = true;
            this.gridColumn8.Width = 174;
            // 
            // gridBand1
            // 
            this.gridBand1.Caption = "Mos相关信息";
            this.gridBand1.Columns.Add(this.gridColumn7);
            this.gridBand1.Columns.Add(this.gridColumn9);
            this.gridBand1.Columns.Add(this.bandedGridColumn4);
            this.gridBand1.Columns.Add(this.bandedGridColumn5);
            this.gridBand1.Columns.Add(this.gridColumn19);
            this.gridBand1.Columns.Add(this.gridColumn20);
            this.gridBand1.Columns.Add(this.bandedGridColumn1);
            this.gridBand1.MinWidth = 20;
            this.gridBand1.Name = "gridBand1";
            this.gridBand1.Width = 845;
            // 
            // gridColumn7
            // 
            this.gridColumn7.Caption = "Mos值";
            this.gridColumn7.FieldName = "MosValue";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.Visible = true;
            this.gridColumn7.Width = 105;
            // 
            // gridColumn9
            // 
            this.gridColumn9.Caption = "Mos采样点时间";
            this.gridColumn9.FieldName = "MosTime";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.Visible = true;
            this.gridColumn9.Width = 143;
            // 
            // bandedGridColumn4
            // 
            this.bandedGridColumn4.Caption = "Mos采样点经度";
            this.bandedGridColumn4.FieldName = "MosLongitude";
            this.bandedGridColumn4.Name = "bandedGridColumn4";
            this.bandedGridColumn4.Visible = true;
            this.bandedGridColumn4.Width = 110;
            // 
            // bandedGridColumn5
            // 
            this.bandedGridColumn5.Caption = "Mos采样点纬度";
            this.bandedGridColumn5.FieldName = "MosLatitude";
            this.bandedGridColumn5.Name = "bandedGridColumn5";
            this.bandedGridColumn5.Visible = true;
            this.bandedGridColumn5.Width = 110;
            // 
            // gridColumn19
            // 
            this.gridColumn19.Caption = "原因";
            this.gridColumn19.FieldName = "ReasonsDes";
            this.gridColumn19.Name = "gridColumn19";
            this.gridColumn19.Visible = true;
            this.gridColumn19.Width = 136;
            // 
            // gridColumn20
            // 
            this.gridColumn20.Caption = "主端起呼事件";
            this.gridColumn20.FieldName = "CallBeginEvtName";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.Visible = true;
            this.gridColumn20.Width = 136;
            // 
            // bandedGridColumn1
            // 
            this.bandedGridColumn1.Caption = "切换次数(主端Mos时段内)";
            this.bandedGridColumn1.FieldName = "HandOverEventsCount";
            this.bandedGridColumn1.Name = "bandedGridColumn1";
            this.bandedGridColumn1.Visible = true;
            this.bandedGridColumn1.Width = 105;
            // 
            // gridBand3
            // 
            this.gridBand3.Caption = "NR信息(主端Mos时段内)";
            this.gridBand3.Columns.Add(this.bandedGridColumn2);
            this.gridBand3.Columns.Add(this.bandedGridColumn12);
            this.gridBand3.Columns.Add(this.bandedGridColumn13);
            this.gridBand3.Columns.Add(this.bandedGridColumn3);
            this.gridBand3.Columns.Add(this.bandedGridColumn14);
            this.gridBand3.Columns.Add(this.bandedGridColumn15);
            this.gridBand3.Columns.Add(this.bandedGridColumn16);
            this.gridBand3.Columns.Add(this.bandedGridColumn18);
            this.gridBand3.Columns.Add(this.bandedGridColumn19);
            this.gridBand3.Columns.Add(this.bandedGridColumn17);
            this.gridBand3.Columns.Add(this.bandedGridColumn20);
            this.gridBand3.Columns.Add(this.bandedGridColumn21);
            this.gridBand3.Name = "gridBand3";
            this.gridBand3.Width = 1050;
            // 
            // bandedGridColumn2
            // 
            this.bandedGridColumn2.Caption = "平均SS_RSRP";
            this.bandedGridColumn2.FieldName = "RsrpInfo.Avg";
            this.bandedGridColumn2.Name = "bandedGridColumn2";
            this.bandedGridColumn2.Visible = true;
            this.bandedGridColumn2.Width = 100;
            // 
            // bandedGridColumn12
            // 
            this.bandedGridColumn12.Caption = "最大SS_RSRP";
            this.bandedGridColumn12.FieldName = "RsrpInfo.Max";
            this.bandedGridColumn12.Name = "bandedGridColumn12";
            this.bandedGridColumn12.Visible = true;
            this.bandedGridColumn12.Width = 100;
            // 
            // bandedGridColumn13
            // 
            this.bandedGridColumn13.Caption = "最小SS_RSRP";
            this.bandedGridColumn13.FieldName = "RsrpInfo.Min";
            this.bandedGridColumn13.Name = "bandedGridColumn13";
            this.bandedGridColumn13.Visible = true;
            this.bandedGridColumn13.Width = 100;
            // 
            // bandedGridColumn3
            // 
            this.bandedGridColumn3.Caption = "平均SS_SINR";
            this.bandedGridColumn3.FieldName = "SinrInfo.Avg";
            this.bandedGridColumn3.Name = "bandedGridColumn3";
            this.bandedGridColumn3.Visible = true;
            this.bandedGridColumn3.Width = 100;
            // 
            // bandedGridColumn14
            // 
            this.bandedGridColumn14.Caption = "最大SS_SINR";
            this.bandedGridColumn14.FieldName = "SinrInfo.Max";
            this.bandedGridColumn14.Name = "bandedGridColumn14";
            this.bandedGridColumn14.Visible = true;
            this.bandedGridColumn14.Width = 100;
            // 
            // bandedGridColumn15
            // 
            this.bandedGridColumn15.Caption = "最小SS_SINR";
            this.bandedGridColumn15.FieldName = "SinrInfo.Min";
            this.bandedGridColumn15.Name = "bandedGridColumn15";
            this.bandedGridColumn15.Visible = true;
            this.bandedGridColumn15.Width = 100;
            // 
            // bandedGridColumn16
            // 
            this.bandedGridColumn16.Caption = "对端平均SS_RSRP";
            this.bandedGridColumn16.FieldName = "OppositeRsrpInfo.Avg";
            this.bandedGridColumn16.Name = "bandedGridColumn16";
            this.bandedGridColumn16.Visible = true;
            // 
            // bandedGridColumn18
            // 
            this.bandedGridColumn18.Caption = "对端最大SS_RSRP";
            this.bandedGridColumn18.FieldName = "OppositeRsrpInfo.Max";
            this.bandedGridColumn18.Name = "bandedGridColumn18";
            this.bandedGridColumn18.Visible = true;
            // 
            // bandedGridColumn19
            // 
            this.bandedGridColumn19.Caption = "对端最小SS_RSRP";
            this.bandedGridColumn19.FieldName = "OppositeRsrpInfo.Min";
            this.bandedGridColumn19.Name = "bandedGridColumn19";
            this.bandedGridColumn19.Visible = true;
            // 
            // bandedGridColumn17
            // 
            this.bandedGridColumn17.Caption = "对端平均SS_SINR";
            this.bandedGridColumn17.FieldName = "OppositeSinrInfo.Avg";
            this.bandedGridColumn17.Name = "bandedGridColumn17";
            this.bandedGridColumn17.Visible = true;
            // 
            // bandedGridColumn20
            // 
            this.bandedGridColumn20.Caption = "对端最大SS_SINR";
            this.bandedGridColumn20.FieldName = "OppositeSinrInfo.Max";
            this.bandedGridColumn20.Name = "bandedGridColumn20";
            this.bandedGridColumn20.Visible = true;
            // 
            // bandedGridColumn21
            // 
            this.bandedGridColumn21.Caption = "对端最小SS_SINR";
            this.bandedGridColumn21.FieldName = "OppositeSinrInfo.Min";
            this.bandedGridColumn21.Name = "bandedGridColumn21";
            this.bandedGridColumn21.Visible = true;
            // 
            // gridBand4
            // 
            this.gridBand4.Caption = "LTE信息(主端Mos时段内)";
            this.gridBand4.Columns.Add(this.bandedGridColumn6);
            this.gridBand4.Columns.Add(this.bandedGridColumn8);
            this.gridBand4.Columns.Add(this.bandedGridColumn9);
            this.gridBand4.Columns.Add(this.bandedGridColumn7);
            this.gridBand4.Columns.Add(this.bandedGridColumn10);
            this.gridBand4.Columns.Add(this.bandedGridColumn11);
            this.gridBand4.Columns.Add(this.bandedGridColumn22);
            this.gridBand4.Columns.Add(this.bandedGridColumn24);
            this.gridBand4.Columns.Add(this.bandedGridColumn25);
            this.gridBand4.Columns.Add(this.bandedGridColumn23);
            this.gridBand4.Columns.Add(this.bandedGridColumn26);
            this.gridBand4.Columns.Add(this.bandedGridColumn27);
            this.gridBand4.Name = "gridBand4";
            this.gridBand4.Width = 900;
            // 
            // bandedGridColumn6
            // 
            this.bandedGridColumn6.Caption = "平均RSRP";
            this.bandedGridColumn6.FieldName = "LteRsrpInfo.Avg";
            this.bandedGridColumn6.Name = "bandedGridColumn6";
            this.bandedGridColumn6.Visible = true;
            // 
            // bandedGridColumn8
            // 
            this.bandedGridColumn8.Caption = "最大RSRP";
            this.bandedGridColumn8.FieldName = "LteRsrpInfo.Max";
            this.bandedGridColumn8.Name = "bandedGridColumn8";
            this.bandedGridColumn8.Visible = true;
            // 
            // bandedGridColumn9
            // 
            this.bandedGridColumn9.Caption = "最小RSRP";
            this.bandedGridColumn9.FieldName = "LteRsrpInfo.Min";
            this.bandedGridColumn9.Name = "bandedGridColumn9";
            this.bandedGridColumn9.Visible = true;
            // 
            // bandedGridColumn7
            // 
            this.bandedGridColumn7.Caption = "平均SINR";
            this.bandedGridColumn7.FieldName = "LteSinrInfo.Avg";
            this.bandedGridColumn7.Name = "bandedGridColumn7";
            this.bandedGridColumn7.Visible = true;
            // 
            // bandedGridColumn10
            // 
            this.bandedGridColumn10.Caption = "最大SINR";
            this.bandedGridColumn10.FieldName = "LteSinrInfo.Max";
            this.bandedGridColumn10.Name = "bandedGridColumn10";
            this.bandedGridColumn10.Visible = true;
            // 
            // bandedGridColumn11
            // 
            this.bandedGridColumn11.Caption = "最小SINR";
            this.bandedGridColumn11.FieldName = "LteSinrInfo.Min";
            this.bandedGridColumn11.Name = "bandedGridColumn11";
            this.bandedGridColumn11.Visible = true;
            // 
            // bandedGridColumn22
            // 
            this.bandedGridColumn22.Caption = "对端平均RSRP";
            this.bandedGridColumn22.FieldName = "OppositeLteRsrpInfo.Avg";
            this.bandedGridColumn22.Name = "bandedGridColumn22";
            this.bandedGridColumn22.Visible = true;
            // 
            // bandedGridColumn24
            // 
            this.bandedGridColumn24.Caption = "对端最大RSRP";
            this.bandedGridColumn24.FieldName = "OppositeLteRsrpInfo.Max";
            this.bandedGridColumn24.Name = "bandedGridColumn24";
            this.bandedGridColumn24.Visible = true;
            // 
            // bandedGridColumn25
            // 
            this.bandedGridColumn25.Caption = "对端最小RSRP";
            this.bandedGridColumn25.FieldName = "OppositeLteRsrpInfo.Min";
            this.bandedGridColumn25.Name = "bandedGridColumn25";
            this.bandedGridColumn25.Visible = true;
            // 
            // bandedGridColumn23
            // 
            this.bandedGridColumn23.Caption = "对端平均SINR";
            this.bandedGridColumn23.FieldName = "OppositeLteSinrInfo.Avg";
            this.bandedGridColumn23.Name = "bandedGridColumn23";
            this.bandedGridColumn23.Visible = true;
            // 
            // bandedGridColumn26
            // 
            this.bandedGridColumn26.Caption = "对端最大SINR";
            this.bandedGridColumn26.FieldName = "OppositeLteSinrInfo.Max";
            this.bandedGridColumn26.Name = "bandedGridColumn26";
            this.bandedGridColumn26.Visible = true;
            // 
            // bandedGridColumn27
            // 
            this.bandedGridColumn27.Caption = "对端最小SINR";
            this.bandedGridColumn27.FieldName = "OppositeLteSinrInfo.Min";
            this.bandedGridColumn27.Name = "bandedGridColumn27";
            this.bandedGridColumn27.Visible = true;
            // 
            // NRWeakMosReasonForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1088, 658);
            this.Controls.Add(this.xtraTabControl1);
            this.Name = "NRWeakMosReasonForm";
            this.Text = "弱MOS原因分析";
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlSum)).EndInit();
            this.ctxMenuSum.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewSum)).EndInit();
            this.xtraTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlDetail)).EndInit();
            this.ctxMenuDetail.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewDetail)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraGrid.GridControl gridControlSum;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewSum;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraGrid.GridControl gridControlDetail;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView gridViewDetail;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn6;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn12;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn13;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn8;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn7;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn9;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn5;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn19;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn20;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn3;
        private System.Windows.Forms.ContextMenuStrip ctxMenuSum;
        private System.Windows.Forms.ToolStripMenuItem toolStripSumExportExcel;
        private System.Windows.Forms.ContextMenuStrip ctxMenuDetail;
        private System.Windows.Forms.ToolStripMenuItem toolStripDetailExportExcel;
        private System.Windows.Forms.ToolStripMenuItem toolStripReplay;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn6;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn7;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn12;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn13;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn14;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn15;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn8;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn9;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn10;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn11;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand2;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand1;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn16;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn18;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn19;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn17;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn20;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn21;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn22;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn24;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn25;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn23;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn26;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn27;
    }
}