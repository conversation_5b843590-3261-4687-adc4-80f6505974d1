﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public class CreateWlanApInfoDetailForm : CreateChildForm
    {
        public CreateWlanApInfoDetailForm(MainModel mm)
            : base(mm)
        { 
        }
        public override string Description
        {
            get
            {
                return "创建WLAN Detail窗口 WlanApInfoDetailForm ";
            }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 20000, 20026, this.Name);
        }
        public override string Name
        {
            get
            {
                return "Detail";
            }
        }

        protected override void initAction()
        {
            Dictionary<string, object> actionParam = new Dictionary<string, object>();
            actionParam["MainForm"] = MainModel.MainForm;
            actionParam["AssemblyName"] = "RAMS.exe";
            actionParam["TypeName"] = "MasterCom.RAMS.Func.WlanApInfoDetailForm";
            actionParam["Text"] = "Detail";
            actionParam["ImageFilePath"] = @"images\frame_info.gif";
            action = new ActionCreateChildFrame();
            action.Param = actionParam;
        }
    }
}
