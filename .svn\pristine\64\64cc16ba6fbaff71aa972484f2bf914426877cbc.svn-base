﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRCellWrongDirForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.gridControl = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExport2Xls = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gcCell = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcARFCN = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcPCI = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcLongitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcLatitude = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcDirection = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcWrongCnt = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcGoodCnt = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcWrongPer = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcCompetedResult = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemMemoEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit1)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl
            // 
            this.gridControl.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl.Location = new System.Drawing.Point(0, 0);
            this.gridControl.MainView = this.gridView;
            this.gridControl.Name = "gridControl";
            this.gridControl.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemMemoEdit1});
            this.gridControl.Size = new System.Drawing.Size(1072, 491);
            this.gridControl.TabIndex = 2;
            this.gridControl.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView});
            this.gridControl.DoubleClick += new System.EventHandler(this.gridView_DoubleClick);
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExport2Xls});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(139, 26);
            // 
            // miExport2Xls
            // 
            this.miExport2Xls.Name = "miExport2Xls";
            this.miExport2Xls.Size = new System.Drawing.Size(138, 22);
            this.miExport2Xls.Text = "导出Excel...";
            this.miExport2Xls.Click += new System.EventHandler(this.miExport2Xls_Click);
            // 
            // gridView
            // 
            this.gridView.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gcCell,
            this.gcARFCN,
            this.gcPCI,
            this.gcLongitude,
            this.gcLatitude,
            this.gcDirection,
            this.gridColumn1,
            this.gcWrongCnt,
            this.gcGoodCnt,
            this.gcWrongPer,
            this.gcCompetedResult,
            this.gridColumn2,
            this.gridColumn3});
            this.gridView.GridControl = this.gridControl;
            this.gridView.GroupFooterShowMode = DevExpress.XtraGrid.Views.Grid.GroupFooterShowMode.Hidden;
            this.gridView.Name = "gridView";
            this.gridView.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView.OptionsBehavior.AutoPopulateColumns = false;
            this.gridView.OptionsBehavior.AutoUpdateTotalSummary = false;
            this.gridView.OptionsBehavior.Editable = false;
            this.gridView.OptionsView.AllowCellMerge = true;
            this.gridView.OptionsView.RowAutoHeight = true;
            this.gridView.OptionsView.ShowDetailButtons = false;
            this.gridView.OptionsView.ShowGroupExpandCollapseButtons = false;
            this.gridView.OptionsView.ShowGroupPanel = false;
            // 
            // gcCell
            // 
            this.gcCell.Caption = "小区";
            this.gcCell.FieldName = "CellName";
            this.gcCell.Name = "gcCell";
            this.gcCell.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gcCell.Visible = true;
            this.gcCell.VisibleIndex = 0;
            this.gcCell.Width = 116;
            // 
            // gcARFCN
            // 
            this.gcARFCN.Caption = "ARFCN";
            this.gcARFCN.FieldName = "FREQ";
            this.gcARFCN.Name = "gcARFCN";
            this.gcARFCN.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gcARFCN.Visible = true;
            this.gcARFCN.VisibleIndex = 1;
            this.gcARFCN.Width = 84;
            // 
            // gcPCI
            // 
            this.gcPCI.Caption = "PCI";
            this.gcPCI.FieldName = "CPI";
            this.gcPCI.Name = "gcPCI";
            this.gcPCI.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gcPCI.Visible = true;
            this.gcPCI.VisibleIndex = 2;
            this.gcPCI.Width = 84;
            // 
            // gcLongitude
            // 
            this.gcLongitude.Caption = "经度";
            this.gcLongitude.FieldName = "Longitude";
            this.gcLongitude.Name = "gcLongitude";
            this.gcLongitude.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gcLongitude.Visible = true;
            this.gcLongitude.VisibleIndex = 5;
            this.gcLongitude.Width = 87;
            // 
            // gcLatitude
            // 
            this.gcLatitude.Caption = "纬度";
            this.gcLatitude.FieldName = "Latitude";
            this.gcLatitude.Name = "gcLatitude";
            this.gcLatitude.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gcLatitude.Visible = true;
            this.gcLatitude.VisibleIndex = 6;
            this.gcLatitude.Width = 87;
            // 
            // gcDirection
            // 
            this.gcDirection.Caption = "方向角";
            this.gcDirection.FieldName = "Direction";
            this.gcDirection.Name = "gcDirection";
            this.gcDirection.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gcDirection.Visible = true;
            this.gcDirection.VisibleIndex = 7;
            this.gcDirection.Width = 70;
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "建议方向角";
            this.gridColumn1.FieldName = "CellDirSuggest";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 8;
            // 
            // gcWrongCnt
            // 
            this.gcWrongCnt.Caption = "角度异常采样点个数";
            this.gcWrongCnt.FieldName = "WrongTestPointCount";
            this.gcWrongCnt.Name = "gcWrongCnt";
            this.gcWrongCnt.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gcWrongCnt.Visible = true;
            this.gcWrongCnt.VisibleIndex = 9;
            this.gcWrongCnt.Width = 130;
            // 
            // gcGoodCnt
            // 
            this.gcGoodCnt.Caption = "角度正常采样点个数";
            this.gcGoodCnt.FieldName = "GoodTestPointCount";
            this.gcGoodCnt.Name = "gcGoodCnt";
            this.gcGoodCnt.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gcGoodCnt.Visible = true;
            this.gcGoodCnt.VisibleIndex = 10;
            this.gcGoodCnt.Width = 125;
            // 
            // gcWrongPer
            // 
            this.gcWrongPer.Caption = "异常百分比(%)";
            this.gcWrongPer.FieldName = "WrongPercentage";
            this.gcWrongPer.Name = "gcWrongPer";
            this.gcWrongPer.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gcWrongPer.Visible = true;
            this.gcWrongPer.VisibleIndex = 11;
            this.gcWrongPer.Width = 107;
            // 
            // gcCompetedResult
            // 
            this.gcCompetedResult.Caption = "对比结果";
            this.gcCompetedResult.FieldName = "CompetedResult";
            this.gcCompetedResult.Name = "gcCompetedResult";
            this.gcCompetedResult.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.gcCompetedResult.Visible = true;
            this.gcCompetedResult.VisibleIndex = 12;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "TAC";
            this.gridColumn2.FieldName = "TAC";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 3;
            // 
            // gridColumn3
            // 
            this.gridColumn3.Caption = "NCI";
            this.gridColumn3.FieldName = "NCI";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn3.Visible = true;
            this.gridColumn3.VisibleIndex = 4;
            // 
            // repositoryItemMemoEdit1
            // 
            this.repositoryItemMemoEdit1.Name = "repositoryItemMemoEdit1";
            // 
            // NRCellWrongDirForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1072, 491);
            this.Controls.Add(this.gridControl);
            this.Name = "NRCellWrongDirForm";
            this.Text = "小区覆盖情况";
            ((System.ComponentModel.ISupportInitialize)(this.gridControl)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView;
        private DevExpress.XtraGrid.Columns.GridColumn gcCell;
        private DevExpress.XtraGrid.Columns.GridColumn gcARFCN;
        private DevExpress.XtraGrid.Columns.GridColumn gcPCI;
        private DevExpress.XtraGrid.Columns.GridColumn gcLongitude;
        private DevExpress.XtraGrid.Columns.GridColumn gcLatitude;
        private DevExpress.XtraGrid.Columns.GridColumn gcDirection;
        private DevExpress.XtraGrid.Columns.GridColumn gcWrongCnt;
        private DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit repositoryItemMemoEdit1;
        private DevExpress.XtraGrid.Columns.GridColumn gcGoodCnt;
        private DevExpress.XtraGrid.Columns.GridColumn gcWrongPer;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem miExport2Xls;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gcCompetedResult;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
    }
}