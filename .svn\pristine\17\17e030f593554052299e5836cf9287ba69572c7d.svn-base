﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRReasonPnlChangeFreq
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.timePersist = new DevExpress.XtraEditors.SpinEdit();
            this.label5 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.numHandoverCount = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.numDistanceLimit = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.grp)).BeginInit();
            this.grp.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.timePersist.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHandoverCount.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistanceLimit.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // grp
            // 
            this.grp.Controls.Add(this.labelControl5);
            this.grp.Controls.Add(this.numHandoverCount);
            this.grp.Controls.Add(this.labelControl6);
            this.grp.Controls.Add(this.labelControl3);
            this.grp.Controls.Add(this.numDistanceLimit);
            this.grp.Controls.Add(this.labelControl4);
            this.grp.Controls.Add(this.timePersist);
            this.grp.Controls.Add(this.label5);
            this.grp.Controls.Add(this.label2);
            this.grp.Size = new System.Drawing.Size(560, 70);
            this.grp.Text = "切换频繁";
            // 
            // timePersist
            // 
            this.timePersist.EditValue = new decimal(new int[] {
            4,
            0,
            0,
            0});
            this.timePersist.Location = new System.Drawing.Point(107, 34);
            this.timePersist.Name = "timePersist";
            this.timePersist.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.timePersist.Properties.MaxValue = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.timePersist.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.timePersist.Size = new System.Drawing.Size(70, 21);
            this.timePersist.TabIndex = 1;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(183, 39);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(17, 12);
            this.label5.TabIndex = 0;
            this.label5.Text = "秒";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(36, 39);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 0;
            this.label2.Text = "持续时间≤";
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(516, 39);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(12, 12);
            this.labelControl5.TabIndex = 17;
            this.labelControl5.Text = "次";
            // 
            // numHandoverCount
            // 
            this.numHandoverCount.EditValue = new decimal(new int[] {
            3,
            0,
            0,
            0});
            this.numHandoverCount.Location = new System.Drawing.Point(438, 35);
            this.numHandoverCount.Name = "numHandoverCount";
            this.numHandoverCount.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numHandoverCount.Properties.Appearance.Options.UseFont = true;
            this.numHandoverCount.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numHandoverCount.Properties.IsFloatValue = false;
            this.numHandoverCount.Properties.Mask.EditMask = "N00";
            this.numHandoverCount.Size = new System.Drawing.Size(70, 20);
            this.numHandoverCount.TabIndex = 16;
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Location = new System.Drawing.Point(395, 39);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(36, 12);
            this.labelControl6.TabIndex = 15;
            this.labelControl6.Text = "切换≥";
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(347, 39);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(12, 12);
            this.labelControl3.TabIndex = 14;
            this.labelControl3.Text = "米";
            // 
            // numDistanceLimit
            // 
            this.numDistanceLimit.EditValue = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numDistanceLimit.Location = new System.Drawing.Point(271, 35);
            this.numDistanceLimit.Name = "numDistanceLimit";
            this.numDistanceLimit.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numDistanceLimit.Properties.Appearance.Options.UseFont = true;
            this.numDistanceLimit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDistanceLimit.Properties.IsFloatValue = false;
            this.numDistanceLimit.Properties.Mask.EditMask = "N00";
            this.numDistanceLimit.Size = new System.Drawing.Size(70, 20);
            this.numDistanceLimit.TabIndex = 13;
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(229, 39);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(36, 12);
            this.labelControl4.TabIndex = 12;
            this.labelControl4.Text = "距离≤";
            // 
            // ReasonPnlChangeFreq
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Name = "ReasonPnlChangeFreq";
            this.Size = new System.Drawing.Size(560, 70);
            ((System.ComponentModel.ISupportInitialize)(this.grp)).EndInit();
            this.grp.ResumeLayout(false);
            this.grp.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.timePersist.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHandoverCount.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistanceLimit.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SpinEdit timePersist;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label2;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.SpinEdit numHandoverCount;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.SpinEdit numDistanceLimit;
        private DevExpress.XtraEditors.LabelControl labelControl4;
    }
}
