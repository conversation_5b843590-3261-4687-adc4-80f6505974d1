﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class ZTLteNBCellCheckDiffFreqSpanAnaListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ZTLteNBCellCheckDiffFreqSpanAnaListForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.miReplay = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.ListViewNBCheckStat = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnFileName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnServCell = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnHOCell = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSpanReConfig2HO = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnSpanLastMR2HO = new BrightIdeasSoftware.OLVColumn();
            this.ctxMenu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ListViewNBCheckStat)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel,
            this.miReplay,
            this.toolStripMenuItem1,
            this.miExpandAll,
            this.miCallapsAll});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(130, 98);
            this.ctxMenu.Opening += new System.ComponentModel.CancelEventHandler(this.ctxMenu_Opening);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // miReplay
            // 
            this.miReplay.Name = "miReplay";
            this.miReplay.Size = new System.Drawing.Size(129, 22);
            this.miReplay.Text = "回放";
            this.miReplay.Click += new System.EventHandler(this.miReplay_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(126, 6);
            this.toolStripMenuItem1.Visible = false;
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(129, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCallapsAll
            // 
            this.miCallapsAll.Name = "miCallapsAll";
            this.miCallapsAll.Size = new System.Drawing.Size(129, 22);
            this.miCallapsAll.Text = "全部合并";
            this.miCallapsAll.Click += new System.EventHandler(this.miCallapsAll_Click);
            // 
            // ListViewNBCheckStat
            // 
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnSN);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnFileName);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnServCell);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnHOCell);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnSpanReConfig2HO);
            this.ListViewNBCheckStat.AllColumns.Add(this.olvColumnSpanLastMR2HO);
            this.ListViewNBCheckStat.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
                        | System.Windows.Forms.AnchorStyles.Left)
                        | System.Windows.Forms.AnchorStyles.Right)));
            this.ListViewNBCheckStat.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnFileName,
            this.olvColumnServCell,
            this.olvColumnHOCell,
            this.olvColumnSpanReConfig2HO,
            this.olvColumnSpanLastMR2HO});
            this.ListViewNBCheckStat.ContextMenuStrip = this.ctxMenu;
            this.ListViewNBCheckStat.Cursor = System.Windows.Forms.Cursors.Default;
            this.ListViewNBCheckStat.FullRowSelect = true;
            this.ListViewNBCheckStat.GridLines = true;
            this.ListViewNBCheckStat.HeaderWordWrap = true;
            this.ListViewNBCheckStat.IsNeedShowOverlay = false;
            this.ListViewNBCheckStat.Location = new System.Drawing.Point(1, 1);
            this.ListViewNBCheckStat.Name = "ListViewNBCheckStat";
            this.ListViewNBCheckStat.OwnerDraw = true;
            this.ListViewNBCheckStat.ShowGroups = false;
            this.ListViewNBCheckStat.Size = new System.Drawing.Size(887, 560);
            this.ListViewNBCheckStat.TabIndex = 7;
            this.ListViewNBCheckStat.UseCompatibleStateImageBehavior = false;
            this.ListViewNBCheckStat.View = System.Windows.Forms.View.Details;
            this.ListViewNBCheckStat.VirtualMode = true;
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            // 
            // olvColumnFileName
            // 
            this.olvColumnFileName.HeaderFont = null;
            this.olvColumnFileName.Text = "文件名称";
            this.olvColumnFileName.Width = 150;
            // 
            // olvColumnServCell
            // 
            this.olvColumnServCell.HeaderFont = null;
            this.olvColumnServCell.Text = "主服小区";
            this.olvColumnServCell.Width = 150;
            // 
            // olvColumnHOCell
            // 
            this.olvColumnHOCell.HeaderFont = null;
            this.olvColumnHOCell.Text = "切换小区";
            this.olvColumnHOCell.Width = 150;
            // 
            // olvColumnSpanReConfig2HO
            // 
            this.olvColumnSpanReConfig2HO.HeaderFont = null;
            this.olvColumnSpanReConfig2HO.Text = "下发异频测量到切换间时长(秒)";
            this.olvColumnSpanReConfig2HO.Width = 180;
            // 
            // olvColumnSpanLastMR2HO
            // 
            this.olvColumnSpanLastMR2HO.HeaderFont = null;
            this.olvColumnSpanLastMR2HO.Text = "最近MR到切换间时长(秒)";
            this.olvColumnSpanLastMR2HO.Width = 180;
            // 
            // ZTLteNBCellCheckDiffFreqSpanAnaListForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(889, 561);
            this.Controls.Add(this.ListViewNBCheckStat);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "ZTLteNBCellCheckDiffFreqSpanAnaListForm";
            this.Text = "LTE异频测量过长分析结果";
            this.ctxMenu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ListViewNBCheckStat)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCallapsAll;
        private BrightIdeasSoftware.TreeListView ListViewNBCheckStat;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnServCell;
        private BrightIdeasSoftware.OLVColumn olvColumnHOCell;
        private System.Windows.Forms.ToolStripMenuItem miReplay;
        private BrightIdeasSoftware.OLVColumn olvColumnFileName;
        private BrightIdeasSoftware.OLVColumn olvColumnSpanReConfig2HO;
        private BrightIdeasSoftware.OLVColumn olvColumnSpanLastMR2HO;

    }
}