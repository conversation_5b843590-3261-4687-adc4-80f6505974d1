﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.RAMS.ZTFunc;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTLTEHighCoverageRoadSetForm : BaseDialog
    {
        private Dictionary<string, bool> closeSimuDic = new Dictionary<string, bool>();
        public ZTLTEHighCoverageRoadSetForm(ServiceName serviceName)
        {
            InitializeComponent();
            this.btnOK.Click += BtnOK_Click;
            this.btnCancel.Click += BtnCancel_Click;
            this.btnSelect.Click += BtnSelect_Click;
            this.chkCellCheck.CheckedChanged += ChkCellCheck_CheckedChanged;
            this.chkCloseSimu.CheckedChanged += ChkCloseSimu_CheckedChanged;
            this.radioSingle.CheckedChanged += ChkCloseSimu_CheckedChanged;
            this.radioMulti.CheckedChanged += ChkCloseSimu_CheckedChanged;

            ChkCellCheck_CheckedChanged(chkCellCheck, new EventArgs());
            ChkCloseSimu_CheckedChanged(chkCloseSimu, new EventArgs());
            chkFreqBand_CheckedChanged(chkCloseSimu, new EventArgs());

            if (serviceName == ServiceName.NBIOTSCAN)
            {
                groupBoxRemove.Visible = false;
                groupBoxIndex.Location = new Point(17, 12);
                groupBoxConstancy.Location = new Point(17, 109);
                groupBoxIndoor.Location = new Point(17, 207);
                groupBoxClose.Location = new Point(17, 313);
                ClientSize = new System.Drawing.Size(584, 465);
            }
        }

        public void SetCondition(LTEScanHighCoverageRoadCondition cond)
        {
            if (cond == null)
            {
                return;
            }
            this.spinEditMaxDiff.Value = cond.RxLevMaxDiff;
            this.spinEditRxlevMin.Value = cond.RxLevMin;
            this.spinEditCoverage.Value = cond.RelCoverate;
            this.spinEditRoadDistance.Value = cond.RoadDistance;
            this.spinEditRoadPercent.Value = (decimal)cond.RoadMinPercent;
            this.spinEditSampleDistance.Value = cond.SampleDistance;

            this.numNearestBtsCount.Value = cond.NearestBtsCount;
            this.numRadiusFactor.Value = (decimal)cond.RadiusFactor;

            this.chkCloseSimu.Checked = cond.IsCloseSimuCheck;
            if (cond.CloseSimuDic != null)
            {
                this.closeSimuDic = new Dictionary<string, bool>(cond.CloseSimuDic);
            }

            this.chkCellCheck.Checked = cond.IsFarCoverCheck;
            this.chkShieldProblem.Checked = cond.IsShieldProblem;

            this.chkTwoEarfcn.Checked = cond.IsTwoEarfcn;
            freqBandControl1.chkFreqBand.Checked = cond.IsFreqBand;


            //设置频点条件
            if (cond.ListFreqPoint!=null &&cond.ListFreqPoint.Count > 0)
            {
                freqBandControl1.lvFreqBand.Items.Clear();
                foreach (FreqPoint fp in cond.ListFreqPoint)
                {
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text =  fp.Carrier + "_" + fp.FreqPointName;
                    lvi.Tag =fp;
                    freqBandControl1.lvFreqBand.Items.Add(lvi);
                }
            }

            this.absValue.Value = cond.AbsValue;
            this.chbAbslute.Checked = cond.IsAbsCheck;
            this.chbRelative.Checked = cond.IsRelativeCheck;
            this.absCoverate.Value = cond.AbsCoverate;
        }
        public LTEScanHighCoverageRoadCondition GetCondition()
        {
            LTEScanHighCoverageRoadCondition cond = new LTEScanHighCoverageRoadCondition();
            cond.RxLevMaxDiff = (int)this.spinEditMaxDiff.Value;
            cond.RxLevMin = (int)this.spinEditRxlevMin.Value;
            cond.RelCoverate = (int)this.spinEditCoverage.Value;
            cond.RoadDistance = (int)this.spinEditRoadDistance.Value;
            cond.RoadMinPercent = (int)this.spinEditRoadPercent.Value;
            cond.SampleDistance = (int)this.spinEditSampleDistance.Value;

            cond.IsFarCoverCheck = cond.IsLeakOutCheck = this.chkCellCheck.Checked;
            cond.NearestBtsCount = (int)this.numNearestBtsCount.Value;
            cond.RadiusFactor = (double)this.numRadiusFactor.Value;

            cond.IsCloseSimuCheck = this.chkCloseSimu.Checked;
            cond.CloseSimuDic = new Dictionary<string, bool>(this.closeSimuDic);

            cond.IsShieldProblem = this.chkCellCheck.Checked && this.chkShieldProblem.Checked;

            cond.IsTwoEarfcn = this.chkTwoEarfcn.Checked;
            cond.IsFreqBand = freqBandControl1.chkFreqBand.Checked;

            //得到频点集合
            cond.ListFreqPoint = freqBandControl1.GetListViewItems();

            cond.AbsValue = (int)this.absValue.Value;
            cond.IsAbsCheck = this.chbAbslute.Checked;
            cond.IsRelativeCheck = this.chbRelative.Checked;
            cond.AbsCoverate = (int)this.absCoverate.Value;

            return cond;
        }


        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (freqBandControl1.chkFreqBand.Checked && freqBandControl1.lvFreqBand.Items.Count <= 0)
            {
                XtraMessageBox.Show("请选择频点", "提示");
                DialogResult = DialogResult.None;
                return;
            }
            closeSimuDic.Clear();
            // 检查闭站条件设置
            int tac = 0, eci = 0;
            if (chkCloseSimu.Checked && radioSingle.Checked && int.TryParse(txtTac.Text, out tac) && int.TryParse(txtEci.Text, out eci))
            {
                closeSimuDic[string.Format("{0}_{1}", tac, eci)] = true;
            }
            else if (chkCloseSimu.Checked && radioSingle.Checked)
            {
                MessageBox.Show("TAC或者ECI设置不正常", "设置错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = DialogResult.None;
                return;
            }
            else if (chkCloseSimu.Checked && radioMulti.Checked && string.IsNullOrEmpty(txtFileName.Text))
            {
                MessageBox.Show("未选择批量小区导入文件", "设置错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = DialogResult.None;
                return;
            }
            else if (chkCloseSimu.Checked && radioMulti.Checked)
            {
                try
                {
                    setCloseSimuDic(ref tac, ref eci);
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message, "设置错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    DialogResult = DialogResult.None;
                    return;
                }
            }

            DialogResult = DialogResult.OK;
        }

        private void setCloseSimuDic(ref int tac, ref int eci)
        {
            DataSet ds = ExcelNPOIManager.ImportFromExcel(txtFileName.Text);
            DataTable dt = ds.Tables[0];

            foreach (DataRow dr in dt.Rows)
            {
                if (Convert.IsDBNull(dr[0]) || Convert.IsDBNull(dr[1]) || !int.TryParse(dr[0].ToString(), out tac) || !int.TryParse(dr[1].ToString(), out eci))
                {
                    continue;
                }
                closeSimuDic[string.Format("{0}_{1}", tac, eci)] = true;
            }
            dt.Dispose();
            ds.Dispose();
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void BtnSelect_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = FilterHelper.Excel;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            txtFileName.Text = dlg.FileName;
        }

        private void ChkCellCheck_CheckedChanged(object sender, EventArgs e)
        {
            numNearestBtsCount.Enabled = numRadiusFactor.Enabled = chkShieldProblem.Enabled = chkCellCheck.Checked;
        }

        private void ChkCloseSimu_CheckedChanged(object sender, EventArgs e)
        {
            radioSingle.Enabled = radioMulti.Enabled = chkCloseSimu.Checked;
            txtTac.Enabled = txtEci.Enabled = chkCloseSimu.Checked && radioSingle.Checked;
            btnSelect.Enabled = chkCloseSimu.Checked && radioMulti.Checked;
        }

        private void chkFreqBand_CheckedChanged(object sender, EventArgs e)
        {
            chkTwoEarfcn.Enabled = !freqBandControl1.chkFreqBand.Checked;
        }

        private void chbRelative_CheckedChanged(object sender, EventArgs e)
        {
            if (!chbRelative.Checked && !chbAbslute.Checked)
            {
                chbRelative.Checked = true;
            }
            spinEditMaxDiff.Enabled = chbRelative.Checked;
            spinEditCoverage.Enabled = chbRelative.Checked;
        }

        private void chbAbslute_CheckedChanged(object sender, EventArgs e)
        {
            if (!chbRelative.Checked && !chbAbslute.Checked)
            {
                chbAbslute.Checked = true;
            }
            absValue.Enabled = chbAbslute.Checked;
            absCoverate.Enabled = chbAbslute.Checked;
        }

        private void chkTwoEarfcn_CheckedChanged(object sender, EventArgs e)
        {
            freqBandControl1.chkFreqBand.Enabled = !chkTwoEarfcn.Checked;
            if (chkTwoEarfcn.Checked)
            {
                freqBandControl1.chkFreqBand.Checked =false;
            }
        }

        private void ZTLTEHighCoverageRoadSetForm_Load(object sender, EventArgs e)
        {
            freqBandControl1.ChkFreqBandChange_click += new FreqBandControl.ChkChangeDelegate(chkFreqBand_CheckedChanged);//把事件绑定到自定义的委托上
        }
    }
}
