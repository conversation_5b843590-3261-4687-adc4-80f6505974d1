﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func;
using MasterCom.MTGis;
using MapWinGIS;
using AxMapWinGIS;
using System.Windows.Forms;
using System.Drawing;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDIYCellAbnormalAngleLayer
    {
        private MapForm mapForm { get; set; }
        private MapOperation op { get; set; }
        Shapefile shapeFile_Point = null;
        Shapefile shapeFile_Line = null;
        bool needWaitBox = false;

        int fldLongitude = 0;
        int fldLatitude = 1;
        int fldColor_line = 0;

        public ZTDIYCellAbnormalAngleLayer(MapForm mapForm, MapOperation op)
        {
            this.mapForm = mapForm;
            this.op = op;
        }

        public void ClearData()
        {
            shapeFile_Point.EditClear();
            shapeFile_Line.EditClear();
            shapeFile_Point = null;
            shapeFile_Line = null;
        }

        internal void ApplyData(List<AbnormalAnglePointInfo> anglePointInfoList)
        {
            if (anglePointInfoList.Count <= 0)
            {
                return;
            }
            AxMap mapMain = mapForm.GetMapFormControl();

            if (shapeFile_Point == null)
            {
                shapeFile_Point = new Shapefile();
                bool result = shapeFile_Point.CreateNewWithShapeID("", ShpfileType.SHP_POINT);
                if (!result)
                {
                    MessageBox.Show(shapeFile_Point.get_ErrorMsg(shapeFile_Point.LastErrorCode));
                    return;
                }
                Field field = new Field();
                field.Name = "Longitude";
                field.Type = FieldType.DOUBLE_FIELD;
                field.Precision = 7;
                shapeFile_Point.EditInsertField(field, ref fldLongitude, null);
                field = new Field();
                field.Name = "Latitude";
                field.Type = FieldType.DOUBLE_FIELD;
                field.Precision = 7;
                shapeFile_Point.EditInsertField(field, ref fldLatitude, null);

                shapeFile_Point.DefaultDrawingOptions.PointSize = 12;
                shapeFile_Point.DefaultDrawingOptions.SetDefaultPointSymbol(tkDefaultPointSymbol.dpsCircle);
                mapMain.AddLayer(shapeFile_Point, true);
            }

            if (shapeFile_Line == null)
            {
                shapeFile_Line = new MapWinGIS.Shapefile();
                bool result = shapeFile_Line.CreateNewWithShapeID("", ShpfileType.SHP_POLYLINE);
                if (!result)
                {
                    MessageBox.Show(shapeFile_Line.get_ErrorMsg(shapeFile_Line.LastErrorCode));
                    return;
                }
                Field field = new Field();
                field.Name = "Color";
                field.Type = FieldType.INTEGER_FIELD;

                shapeFile_Line.EditInsertField(field, ref fldColor_line, null);
                shapeFile_Line.DefaultDrawingOptions.LineColor = (uint)ColorTranslator.ToOle(Color.Red);
                mapMain.AddLayer(shapeFile_Line, true);
            }
            if (anglePointInfoList.Count > 20)
            {
                needWaitBox = true;
                WaitBox.Show("正在绘制...", createAbnormalAngleFeatureThread, anglePointInfoList);
            }
            else
            {
                createAbnormalAngleFeatureThread(anglePointInfoList);
            }
        }

        private void createAbnormalAngleFeatureThread(object param)
        {
            try
            {
                for (int index = shapeFile_Point.NumShapes - 1; index >= 0; index--)
                {
                    shapeFile_Point.EditDeleteShape(index);
                }
                for (int idx = shapeFile_Line.NumShapes - 1; idx >= 0; idx--)
                {
                    shapeFile_Line.EditDeleteShape(idx);
                }

                AxMap mapMain = mapForm.GetMapFormControl();
                List<AbnormalAnglePointInfo> anglePointInfoList = param as List<AbnormalAnglePointInfo>;
                int count = 0;
                int shpIdx_pts = 0;
                int shpIdx_line = 0;
                foreach (AbnormalAnglePointInfo pointInfo in anglePointInfoList)
                {
                    count++;
                    if (needWaitBox)
                    {
                        WaitBox.Text = "正在绘制小区异常角度...(" + count + "/" + anglePointInfoList.Count + ")";
                        WaitBox.ProgressPercent = (100 * count / anglePointInfoList.Count);
                    }
                    setShapeInfo(ref shpIdx_pts, ref shpIdx_line, pointInfo);
                }
                mapForm.SetRedrawBuffFlag();
                mapMain.Redraw();
            }
            catch
            {
            	//continue
            }
            finally
            {
                if (needWaitBox)
                {
                    System.Threading.Thread.Sleep(2000);
                    WaitBox.Close();
                }
            }
        }

        private void setShapeInfo(ref int shpIdx_pts, ref int shpIdx_line, AbnormalAnglePointInfo pointInfo)
        {
            MapWinGIS.Point pnt = new MapWinGIS.Point();
            pnt.x = pointInfo.Longitude;
            pnt.y = pointInfo.Latitude;
            MapWinGIS.Shape spBase = new MapWinGIS.Shape();
            spBase.Create(ShpfileType.SHP_POINT);
            int idx = 0;
            spBase.InsertPoint(pnt, ref idx);

            shapeFile_Point.EditInsertShape(spBase, ref shpIdx_pts);
            shapeFile_Point.EditCellValue(fldLongitude, shpIdx_pts, pointInfo.Longitude);
            shapeFile_Point.EditCellValue(fldLatitude, shpIdx_pts, pointInfo.Latitude);
            shpIdx_pts++;

            if (pointInfo.cell != null || pointInfo.tdCell != null || pointInfo.wCell != null)
            {
                int pntIdx = 0;
                MapWinGIS.Point pnt1 = new MapWinGIS.Point();
                pnt1.x = pointInfo.Longitude;
                pnt1.y = pointInfo.Latitude;
                MapWinGIS.Point pnt2 = new MapWinGIS.Point();
                pnt2.x = getLongitude(pointInfo);
                pnt2.y = getLatitude(pointInfo);
                MapWinGIS.Shape shp = new MapWinGIS.Shape();
                shp.Create(ShpfileType.SHP_POLYLINE);
                shp.InsertPoint(pnt1, ref pntIdx);
                shp.InsertPoint(pnt2, ref shpIdx_line);
                shapeFile_Line.EditInsertShape(shp, ref shpIdx_line);
                shpIdx_line++;
            }
        }

        private double getLongitude(AbnormalAnglePointInfo pointInfo)
        {
            if (pointInfo.cell == null)
            {
                if (pointInfo.tdCell == null)
                {
                   return pointInfo.wCell.Longitude;
                }
                else
                {
                    return pointInfo.tdCell.Longitude;
                }
            }
            else
            {
                return pointInfo.cell.Longitude;
            }
        }

        private double getLatitude(AbnormalAnglePointInfo pointInfo)
        {
            if (pointInfo.cell == null)
            {
                if (pointInfo.tdCell == null)
                {
                    return pointInfo.wCell.Latitude;
                }
                else
                {
                    return pointInfo.tdCell.Latitude;
                }
            }
            else
            {
                return pointInfo.cell.Latitude;
            }
        }

    }
}
