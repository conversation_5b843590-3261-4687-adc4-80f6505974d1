<?xml version="1.0" encoding="UTF-8"?>
<Configs>
	<Config name="StatParamCfg">
		<Item name="configs" typeName="IList">
			<Item typeName="IDictionary">
				<Item typeName="String" key="Name">WCDMA参数</Item>
				<Item typeName="String" key="FName"/>
				<Item typeName="IList" key="children">
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">WCDMA语音业务参数</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">W网占用时长 waDurationWCDMA</Item>
								<Item typeName="String" key="FName">waDurationWCDMA</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">G网占用时长 waDurationGSM</Item>
								<Item typeName="String" key="FName">waDurationGSM</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">W网测试距离 waDistanceWCDMA</Item>
								<Item typeName="String" key="FName">waDistanceWCDMA</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">G网测试距离 waDistanceGSM</Item>
								<Item typeName="String" key="FName">waDistanceGSM</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalRSCP≥-90dBm TotalEc/Io≥-12dB的采样点数 waRSCP_F90_ECIO_F12</Item>
								<Item typeName="String" key="FName">waRSCP_F90_ECIO_F12</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalRSCP≥-85dBm TotalEc/Io≥-10dB的采样点数 waRSCP_F85_ECIO_F10</Item>
								<Item typeName="String" key="FName">waRSCP_F85_ECIO_F10</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalEc/Io小于-14.00的采样点个数 waTotalEc_Io_F14</Item>
								<Item typeName="String" key="FName">waTotalEc_Io_F14</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalEc/Io[-14.00,-12.00)的采样点个数 waTotalEc_Io_F14_F12</Item>
								<Item typeName="String" key="FName">waTotalEc_Io_F14_F12</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalEc/Io[-12.00,-11.00)的采样点个数 waTotalEc_Io_F12_F11</Item>
								<Item typeName="String" key="FName">waTotalEc_Io_F12_F11</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalEc/Io[-11.00,-10.00)的采样点个数  watotalEc_Io_F11_F10</Item>
								<Item typeName="String" key="FName">waTotalEc_Io_F11_F10</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalEc/Io[-10.00,-8.00)的采样点个数  waTotalEc_Io_F10_F8</Item>
								<Item typeName="String" key="FName">waTotalEc_Io_F10_F8</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalEc/Io>= -8.00的采样点个数 waTotalEc_Io_F8</Item>
								<Item typeName="String" key="FName">waTotalEc_Io_F8</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BestEc/Io小于-14.00采样点个数 waBestEc_Io_F14</Item>
								<Item typeName="String" key="FName">waBestEc_Io_F14</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BestEc/Io[-14.00,-12.00)采样点个数  waBestEc_Io_F14_F12</Item>
								<Item typeName="String" key="FName">waBestEc_Io_F14_F12</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BestEc/Io[-12.00,-11.00)采样点个数 waBestEc_Io_F12_F11</Item>
								<Item typeName="String" key="FName">waBestEc_Io_F12_F11</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BestEc/Io[-11.00,-10.00)采样点个数 waBestEc_Io_F11_F10</Item>
								<Item typeName="String" key="FName">waBestEc_Io_F11_F10</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BestEc/Io[-10.00,-8.00)采样点个数 waBestEc_Io_F10_F8</Item>
								<Item typeName="String" key="FName">waBestEc_Io_F10_F8</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BestEc/Io>= -8.00采样点个数 waBestEc_Io_F8</Item>
								<Item typeName="String" key="FName">waBestEc_Io_F8</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalRSCP小于-105.00 waTotalRSCP_F105</Item>
								<Item typeName="String" key="FName">waTotalRSCP_F105</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalRSCP[-105.00,-100.00) waTotalRSCP_F105_F100</Item>
								<Item typeName="String" key="FName">waTotalRSCP_F105_F100</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalRSCP[-100.00,-90.00) waTotalRSCP_F100_F90</Item>
								<Item typeName="String" key="FName">waTotalRSCP_F100_F90</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalRSCP[-90.00,-85.00) waTotalRSCP_F90_F85</Item>
								<Item typeName="String" key="FName">waTotalRSCP_F90_F85</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalRSCP[-85.00,-80.00) waTotalRSCP_F85_F80</Item>
								<Item typeName="String" key="FName">waTotalRSCP_F85_F80</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalRSCP>= -80.00 waTotalRSCP_F80</Item>
								<Item typeName="String" key="FName">waTotalRSCP_F80</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BestRSCP小于-105.00 waBestRSCP_F105</Item>
								<Item typeName="String" key="FName">waBestRSCP_F105</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BestRSCP[-105.00,-100.00) waBestRSCP_F105_F100</Item>
								<Item typeName="String" key="FName">waBestRSCP_F105_F100</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BestRSCP[-100.00,-90.00) waBestRSCP_F100_F90</Item>
								<Item typeName="String" key="FName">waBestRSCP_F100_F90</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BestRSCP[-90.00,-85.00) waBestRSCP_F90_F85</Item>
								<Item typeName="String" key="FName">waBestRSCP_F90_F85</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BestRSCP[-85.00,-80.00) waBestRSCP_F85_F80</Item>
								<Item typeName="String" key="FName">waBestRSCP_F85_F80</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BestRSCP>= -80.00 waBestRSCP_F80</Item>
								<Item typeName="String" key="FName">waBestRSCP_F80</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RxPower小于-90.00 waRxPower_F90</Item>
								<Item typeName="String" key="FName">waRxPower_F90</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RxPower[-90.00,-85.00) waRxPower_F90_F85</Item>
								<Item typeName="String" key="FName">waRxPower_F90_F85</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RxPower[-85.00,-80.00) waRxPower_F85_F80</Item>
								<Item typeName="String" key="FName">waRxPower_F85_F80</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RxPower[-80.00,-75.00) waRxPower_F80_F75</Item>
								<Item typeName="String" key="FName">waRxPower_F80_F75</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RxPower>= -75.00 waRxPower_F75</Item>
								<Item typeName="String" key="FName">waRxPower_F75</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TxPower小于-15.00 waTxPower_F15</Item>
								<Item typeName="String" key="FName">waTxPower_F15</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TxPower[-15.00,0.00) waTxPower_F15_0</Item>
								<Item typeName="String" key="FName">waTxPower_F15_0</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TxPower[0.00,10.00)  waTxPower_0_10</Item>
								<Item typeName="String" key="FName">waTxPower_0_10</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TxPower[10.00,20.00)waTxPower_10_20</Item>
								<Item typeName="String" key="FName">waTxPower_10_20</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TxPower>=20.00 waTxPower_20</Item>
								<Item typeName="String" key="FName">waTxPower_20</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RSCP≥-80dBm TxPower≤-10dBm的采样点数waTxPower_F10_RSCP_F80</Item>
								<Item typeName="String" key="FName">waTxPower_F10_RSCP_F80</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">SIR小于0.00 waSIR_0</Item>
								<Item typeName="String" key="FName">waSIR_0</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">SIR[0.00,6.00) waSIR_0_6</Item>
								<Item typeName="String" key="FName">waSIR_0_6</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">SIR[6.00,9.00) waSIR_6_9</Item>
								<Item typeName="String" key="FName">waSIR_6_9</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">SIR[9.00,12.00) waSIR_9_12</Item>
								<Item typeName="String" key="FName">waSIR_9_12</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">SIR>= 12.00 waSIR_12</Item>
								<Item typeName="String" key="FName">waSIR_12</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BLER[0, 1) waBLER_0_1</Item>
								<Item typeName="String" key="FName">waBLER_0_1</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BLER[1, 2) waBLER_1_2</Item>
								<Item typeName="String" key="FName">waBLER_1_2</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BLER[2, 3) waBLER_2_3</Item>
								<Item typeName="String" key="FName">waBLER_2_3</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BLER[3, 4) waBLER_3_4</Item>
								<Item typeName="String" key="FName">waBLER_3_4</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BLER[4, 5) waBLER_4_5</Item>
								<Item typeName="String" key="FName">waBLER_4_5</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BLER[5, 100] waBLER_5_100</Item>
								<Item typeName="String" key="FName">waBLER_5_100</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">ActiveSet1 waActiveSet_1</Item>
								<Item typeName="String" key="FName">waActiveSet_1</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">ActiveSet2 waActiveSet_2</Item>
								<Item typeName="String" key="FName">waActiveSet_2</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">ActiveSet3 waActiveSet_3</Item>
								<Item typeName="String" key="FName">waActiveSet_3</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">ActiveSet4 waActiveSet_4</Item>
								<Item typeName="String" key="FName">waActiveSet_4</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">ActiveSet5 waActiveSet_5</Item>
								<Item typeName="String" key="FName">waActiveSet_5</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">ActiveSet6 waActiveSet_6</Item>
								<Item typeName="String" key="FName">waActiveSet_6</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">ActiveSet其他 waActiveSet_other</Item>
								<Item typeName="String" key="FName">waActiveSet_other</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MOS总采样点数 waMOS_sample</Item>
								<Item typeName="String" key="FName">waMOS_sample</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MOS总值 waMOS_total</Item>
								<Item typeName="String" key="FName">waMOS_total</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MOS小于2 waMOS_20</Item>
								<Item typeName="String" key="FName">waMOS_20</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MOS[2,2.5)  waMOS_20_25</Item>
								<Item typeName="String" key="FName">waMOS_20_25</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MOS[2.5,3) waMOS_25_30</Item>
								<Item typeName="String" key="FName">waMOS_25_30</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MOS[3,3.5) waMOS_30_35</Item>
								<Item typeName="String" key="FName">waMOS_30_35</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MOS[3.5,4) waMOS_35_40</Item>
								<Item typeName="String" key="FName">waMOS_35_40</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MOS[4,4.5) waMOS_40_45</Item>
								<Item typeName="String" key="FName">waMOS_40_45</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MOS>=4.5 waMOS_45</Item>
								<Item typeName="String" key="FName">waMOS_45</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">waRscpTxNum_80_10</Item>
								<Item typeName="String" key="FName">waRscpTxNum_80_10</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">waRscpNum_80</Item>
								<Item typeName="String" key="FName">waRscpNum_80</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">WCDMA数据业务参数</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA测试采样点数</Item>
								<Item typeName="String" key="FName">gSampleTotleWCDMA</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">总测试时长</Item>
								<Item typeName="String" key="FName">gDuration</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA测试时长</Item>
								<Item typeName="String" key="FName">gDurationWCDMA</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">2G GPRS测试时长</Item>
								<Item typeName="String" key="FName">gDuration_GPRS</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">2G EDGE测试时长</Item>
								<Item typeName="String" key="FName">gDuration_EDGE</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">总测试距离</Item>
								<Item typeName="String" key="FName">gDistance</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA测试距离</Item>
								<Item typeName="String" key="FName">gDistanceWCDMA</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">2G GPRS测试距离</Item>
								<Item typeName="String" key="FName">gDistance_GPRS</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">2G EDGE测试距离</Item>
								<Item typeName="String" key="FName">gDistance_EDGE</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">Rxlev</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_020B0101</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_10_45</Item>
										<Item typeName="String" key="FName">Gx_020B0102</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_46_50</Item>
										<Item typeName="String" key="FName">Gx_020B0103</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_51_55</Item>
										<Item typeName="String" key="FName">Gx_020B0104</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_56_60</Item>
										<Item typeName="String" key="FName">Gx_020B0105</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_61_65</Item>
										<Item typeName="String" key="FName">Gx_020B0106</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_66_70</Item>
										<Item typeName="String" key="FName">Gx_020B0107</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_71_75</Item>
										<Item typeName="String" key="FName">Gx_020B0108</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_76_80</Item>
										<Item typeName="String" key="FName">Gx_020B0109</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_81</Item>
										<Item typeName="String" key="FName">Gx_020B010A</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_82</Item>
										<Item typeName="String" key="FName">Gx_020B010B</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_83</Item>
										<Item typeName="String" key="FName">Gx_020B010C</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_84</Item>
										<Item typeName="String" key="FName">Gx_020B010D</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_85</Item>
										<Item typeName="String" key="FName">Gx_020B010E</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_86</Item>
										<Item typeName="String" key="FName">Gx_020B010F</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_87</Item>
										<Item typeName="String" key="FName">Gx_020B0110</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_88</Item>
										<Item typeName="String" key="FName">Gx_020B0111</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_89</Item>
										<Item typeName="String" key="FName">Gx_020B0112</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_90</Item>
										<Item typeName="String" key="FName">Gx_020B0113</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_91</Item>
										<Item typeName="String" key="FName">Gx_020B0114</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_92</Item>
										<Item typeName="String" key="FName">Gx_020B0115</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_93</Item>
										<Item typeName="String" key="FName">Gx_020B0116</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_94</Item>
										<Item typeName="String" key="FName">Gx_020B0117</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_95</Item>
										<Item typeName="String" key="FName">Gx_020B0118</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_96</Item>
										<Item typeName="String" key="FName">Gx_020B0119</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_97</Item>
										<Item typeName="String" key="FName">Gx_020B011A</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_98</Item>
										<Item typeName="String" key="FName">Gx_020B011B</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_MAX</Item>
										<Item typeName="String" key="FName">Gx_020B011C</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GSM_RSCP_MIN</Item>
										<Item typeName="String" key="FName">Gx_020B011D</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_020B0401</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_10_45</Item>
										<Item typeName="String" key="FName">Gx_020B0402</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_46_50</Item>
										<Item typeName="String" key="FName">Gx_020B0403</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_51_55</Item>
										<Item typeName="String" key="FName">Gx_020B0404</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_56_60</Item>
										<Item typeName="String" key="FName">Gx_020B0405</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_61_65</Item>
										<Item typeName="String" key="FName">Gx_020B0406</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_66_70</Item>
										<Item typeName="String" key="FName">Gx_020B0407</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_71_75</Item>
										<Item typeName="String" key="FName">Gx_020B0408</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_76_80</Item>
										<Item typeName="String" key="FName">Gx_020B0409</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_81</Item>
										<Item typeName="String" key="FName">Gx_020B040A</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_82</Item>
										<Item typeName="String" key="FName">Gx_020B040B</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_83</Item>
										<Item typeName="String" key="FName">Gx_020B040C</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_84</Item>
										<Item typeName="String" key="FName">Gx_020B040D</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_85</Item>
										<Item typeName="String" key="FName">Gx_020B040E</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_86</Item>
										<Item typeName="String" key="FName">Gx_020B040F</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_87</Item>
										<Item typeName="String" key="FName">Gx_020B0410</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_88</Item>
										<Item typeName="String" key="FName">Gx_020B0411</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_89</Item>
										<Item typeName="String" key="FName">Gx_020B0412</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_90</Item>
										<Item typeName="String" key="FName">Gx_020B0413</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_91</Item>
										<Item typeName="String" key="FName">Gx_020B0414</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_92</Item>
										<Item typeName="String" key="FName">Gx_020B0415</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_93</Item>
										<Item typeName="String" key="FName">Gx_020B0416</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_94</Item>
										<Item typeName="String" key="FName">Gx_020B0417</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_95</Item>
										<Item typeName="String" key="FName">Gx_020B0418</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_96</Item>
										<Item typeName="String" key="FName">Gx_020B0419</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_97</Item>
										<Item typeName="String" key="FName">Gx_020B041A</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_98</Item>
										<Item typeName="String" key="FName">Gx_020B041B</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_MAX</Item>
										<Item typeName="String" key="FName">Gx_020B041C</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_WCDMA_RSCP_MIN</Item>
										<Item typeName="String" key="FName">Gx_020B041D</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RLC</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_PS384_RLC_DL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_030B0101</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_PS384_RLC_DL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_030B0102</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_PS384_RLC_UL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_030B0103</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_PS384_RLC_UL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_030B0104</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_PS128_RLC_DL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_030B0201</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_PS128_RLC_DL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_030B0202</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_PS128_RLC_UL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_030B0203</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_PS128_RLC_UL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_030B0204</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_PS64_RLC_DL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_030B0301</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_PS64_RLC_DL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_030B0302</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_PS64_RLC_UL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_030B0303</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_PS64_RLC_UL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_030B0304</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GPRS_RLC_DL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_030B0401</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GPRS_RLC_DL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_030B0402</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GPRS_RLC_UL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_030B0403</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GPRS_RLC_UL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_030B0404</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_HSPA_RLC_DL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_030B0501</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_HSPA_RLC_DL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_030B0502</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_HSPA_RLC_UL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_030B0503</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_HSPA_RLC_UL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_030B0504</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_EDGE_RLC_DL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_030B0801</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_EDGE_RLC_DL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_030B0802</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_EDGE_RLC_UL_THR_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_030B0803</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_EDGE_RLC_UL_THR_SUM</Item>
										<Item typeName="String" key="FName">Gx_030B0804</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BLER</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_PS384_RLC_BLER_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_040B0101</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_PS384_RLC_BLER_SUM</Item>
										<Item typeName="String" key="FName">Gx_040B0102</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_PS384_RLC_BLER_MAX</Item>
										<Item typeName="String" key="FName">Gx_040B0103</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_PS384_RLC_BLER_MIN</Item>
										<Item typeName="String" key="FName">Gx_040B0104</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_PS128_RLC_BLER_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_040B0201</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_PS128_RLC_BLER_SUM</Item>
										<Item typeName="String" key="FName">Gx_040B0202</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_PS128_RLC_BLER_MAX</Item>
										<Item typeName="String" key="FName">Gx_040B0203</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_PS128_RLC_BLER_MIN</Item>
										<Item typeName="String" key="FName">Gx_040B0204</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_PS64_RLC_BLER_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_040B0301</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_PS64_RLC_BLER_SUM</Item>
										<Item typeName="String" key="FName">Gx_040B0302</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_PS64_RLC_BLER_MAX</Item>
										<Item typeName="String" key="FName">Gx_040B0303</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_PS64_RLC_BLER_MIN</Item>
										<Item typeName="String" key="FName">Gx_040B0304</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GPRS_RLC_BLER_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_040B0401</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GPRS_RLC_BLER_SUM</Item>
										<Item typeName="String" key="FName">Gx_040B0402</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GPRS_RLC_BLER_MAX</Item>
										<Item typeName="String" key="FName">Gx_040B0403</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_GPRS_RLC_BLER_MIN</Item>
										<Item typeName="String" key="FName">Gx_040B0404</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_HSPA_RLC_BLER_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_040B0501</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_HSPA_RLC_BLER_SUM</Item>
										<Item typeName="String" key="FName">Gx_040B0502</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_HSPA_RLC_BLER_MAX</Item>
										<Item typeName="String" key="FName">Gx_040B0503</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_HSPA_RLC_BLER_MIN</Item>
										<Item typeName="String" key="FName">Gx_040B0504</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_EDGE_RLC_BLER_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_040B0801</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_EDGE_RLC_BLER_SUM</Item>
										<Item typeName="String" key="FName">Gx_040B0802</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_EDGE_RLC_BLER_MAX</Item>
										<Item typeName="String" key="FName">Gx_040B0803</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_DT_EDGE_RLC_BLER_MIN</Item>
										<Item typeName="String" key="FName">Gx_040B0804</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">APP</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS384_FTP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B01010201</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS384_FTP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B01010202</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS384_FTP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B01010203</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS384_FTP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B01010204</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS384_WAP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B01010601</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS384_WAP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B01010602</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS384_WAP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B01010603</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS384_WAP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B01010604</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS384_Http_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B01010C01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS384_Http_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B01010C02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS384_Http_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B01010C03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS384_Http_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B01010C04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS128_FTP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B01020201</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS128_FTP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B01020202</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS128_FTP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B01020203</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS128_FTP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B01020204</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS128_WAP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B01020601</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS128_WAP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B01020602</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS128_WAP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B01020603</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS128_WAP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B01020604</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS128_Http_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B01020C01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS128_Http_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B01020C02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS128_Http_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B01020C03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS128_Http_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B01020C04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS64_FTP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B01030201</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS64_FTP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B01030202</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS64_FTP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B01030203</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS64_FTP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B01030204</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS64_WAP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B01030601</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS64_WAP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B01030602</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS64_WAP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B01030603</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS64_WAP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B01030604</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS64_Http_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B01030C01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS64_Http_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B01030C02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS64_Http_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B01030C03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS64_Http_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B01030C04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_GPRS_FTP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B01040201</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_GPRS_FTP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B01040202</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_GPRS_FTP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B01040203</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_GPRS_FTP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B01040204</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_GPRS_WAP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B01040601</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_GPRS_WAP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B01040602</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_GPRS_WAP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B01040603</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_GPRS_WAP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B01040604</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_GPRS_Http_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B01040C01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_GPRS_Http_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B01040C02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_GPRS_Http_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B01040C03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_GPRS_Http_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B01040C04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_FTP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B01050201</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_FTP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B01050202</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_FTP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B01050203</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_FTP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B01050204</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_FTP_Download_APP_SPEED_0_1</Item>
										<Item typeName="String" key="FName">Gx_050B01050205</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_FTP_Download_APP_SPEED_1_2</Item>
										<Item typeName="String" key="FName">Gx_050B01050206</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_FTP_Download_APP_SPEED_2_3</Item>
										<Item typeName="String" key="FName">Gx_050B01050207</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_FTP_Download_APP_SPEED_3</Item>
										<Item typeName="String" key="FName">Gx_050B01050208</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_WAP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B01050601</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_WAP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B01050602</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_WAP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B01050603</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_WAP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B01050604</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_Http_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B01050C01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_Http_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B01050C02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_Http_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B01050C03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_Http_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B01050C04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_FTP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B01080201</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_FTP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B01080202</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_FTP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B01080203</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_FTP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B01080204</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_FTP_Download_APP_SPEED_0_1</Item>
										<Item typeName="String" key="FName">Gx_050B01080205</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_FTP_Download_APP_SPEED_1_2</Item>
										<Item typeName="String" key="FName">Gx_050B01080206</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_FTP_Download_APP_SPEED_2_3</Item>
										<Item typeName="String" key="FName">Gx_050B01080207</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_FTP_Download_APP_SPEED_3</Item>
										<Item typeName="String" key="FName">Gx_050B01080208</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_WAP_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B01080601</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_WAP_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B01080602</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_WAP_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B01080603</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_WAP_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B01080604</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_Http_Download_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B01080C01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_Http_Download_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B01080C02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_Http_Download_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B01080C03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_Http_Download_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B01080C04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS384_FTP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B02010301</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS384_FTP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B02010302</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS384_FTP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B02010303</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS384_FTP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B02010304</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS384_WAP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B02010701</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS384_WAP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B02010702</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS384_WAP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B02010703</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS384_WAP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B02010704</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS384_Http_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B02010D01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS384_Http_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B02010D02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS384_Http_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B02010D03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS384_Http_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B02010D04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS128_FTP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B02020301</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS128_FTP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B02020302</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS128_FTP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B02020303</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS128_FTP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B02020304</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS128_WAP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B02020701</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS128_WAP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B02020702</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS128_WAP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B02020703</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS128_WAP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B02020704</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS128_Http_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B02020D01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS128_Http_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B02020D02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS128_Http_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B02020D03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS128_Http_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B02020D04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS64_FTP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B02030301</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS64_FTP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B02030302</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS64_FTP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B02030303</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS64_FTP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B02030304</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS64_WAP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B02030701</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS64_WAP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B02030702</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS64_WAP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B02030703</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS64_WAP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B02030704</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS64_Http_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B02030D01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS64_Http_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B02030D02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS64_Http_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B02030D03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_PS64_Http_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B02030D04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_FTP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B02050301</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_FTP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B02050302</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_FTP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B02050303</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_FTP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B02050304</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_WAP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B02050701</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_WAP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B02050702</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_WAP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B02050703</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_WAP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B02050704</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_Http_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B02050D01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_Http_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B02050D02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_Http_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B02050D03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_HSPA_Http_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B02050D04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_FTP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B02080301</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_FTP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B02080302</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_FTP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B02080303</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_FTP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B02080304</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_WAP_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B02080701</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_WAP_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B02080702</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_WAP_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B02080703</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_WAP_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B02080704</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_Http_Upload_APP_BYTE</Item>
										<Item typeName="String" key="FName">Gx_050B02080D01</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_Http_Upload_APP_TIME</Item>
										<Item typeName="String" key="FName">Gx_050B02080D02</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_Http_Upload_APP_SAMPLE</Item>
										<Item typeName="String" key="FName">Gx_050B02080D03</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">WCDMA_EDGE_Http_Upload_APP_SPEED</Item>
										<Item typeName="String" key="FName">Gx_050B02080D04</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">PDU/SDU</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">DL_Sample 下行采样点个数</Item>
										<Item typeName="String" key="FName">Gx_5D0B0101</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">DL_TIME 下行传输时间总和</Item>
										<Item typeName="String" key="FName">Gx_5D0B0102</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">DL_PDU_BIT 下行PDU比特总数(Kbit)</Item>
										<Item typeName="String" key="FName">Gx_5D0B0103</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">DL_PDU_1000 下行PDU采样点个数(小于1Mbit/s)</Item>
										<Item typeName="String" key="FName">Gx_5D0B0104</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">DL_PDU_1000_2000 下行PDU采样点个数(大于等于1Mbit/s且小于2Mbit/s)</Item>
										<Item typeName="String" key="FName">Gx_5D0B0105</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">DL_PDU_2000_4000 下行PDU采样点个数(大于等于2Mbit/s且小于4Mbit/s)</Item>
										<Item typeName="String" key="FName">Gx_5D0B0106</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">DL_PDU_4000 下行PDU采样点个数(大于等于4Mbit/s)</Item>
										<Item typeName="String" key="FName">Gx_5D0B0107</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">DL_SDU_BIT 下行SDU比特总数(Kbit)</Item>
										<Item typeName="String" key="FName">Gx_5D0B0108</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">DL_SDU_1000 下行SDU采样点个数(小于1Mbit/s)</Item>
										<Item typeName="String" key="FName">Gx_5D0B0109</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">DL_SDU_1000_2000 下行SDU采样点个数(大于等于1Mbit/s且小于2Mbit/s)</Item>
										<Item typeName="String" key="FName">Gx_5D0B010A</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">DL_SDU_2000_4000 下行SDU采样点个数(大于等于2Mbit/s且小于4Mbit/s)</Item>
										<Item typeName="String" key="FName">Gx_5D0B010B</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">DL_SDU_4000 下行SDU采样点个数(大于等于4Mbit/s)</Item>
										<Item typeName="String" key="FName">Gx_5D0B010C</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">UL_Sample 上行采样点个数</Item>
										<Item typeName="String" key="FName">Gx_5D0B010D</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">UL_TIME 上行传输时间总和</Item>
										<Item typeName="String" key="FName">Gx_5D0B010E</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">UL_PDU_BIT 上行PDU比特总数(Kbit)</Item>
										<Item typeName="String" key="FName">Gx_5D0B010F</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">UL_PDU_500 上行PDU采样点个数(小于0.5Mbit/s)</Item>
										<Item typeName="String" key="FName">Gx_5D0B0110</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">UL_PDU_500_1000 上行PDU采样点个数(大于等于0.5Mbit/s且小于1Mbit/s)</Item>
										<Item typeName="String" key="FName">Gx_5D0B0111</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">UL_PDU_1000_1500 上行PDU采样点个数(大于等于1Mbit/s且小于1.5Mbit/s)</Item>
										<Item typeName="String" key="FName">Gx_5D0B0112</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">UL_PDU_1500 上行PDU采样点个数(大于等于1.5Mbit/s)</Item>
										<Item typeName="String" key="FName">Gx_5D0B0113</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">UL_SDU_BIT 上行SDU比特总数(Kbit)</Item>
										<Item typeName="String" key="FName">Gx_5D0B0114</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">UL_SDU_500 上行SDU采样点个数(小于0.5Mbit/s)</Item>
										<Item typeName="String" key="FName">Gx_5D0B0115</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">UL_SDU_500_1000 上行SDU采样点个数(大于等于0.5Mbit/s且小于1Mbit/s)</Item>
										<Item typeName="String" key="FName">Gx_5D0B0116</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">UL_SDU_1000_1500 上行SDU采样点个数(大于等于1Mbit/s且小于1.5Mbit/s)</Item>
										<Item typeName="String" key="FName">Gx_5D0B0117</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">UL_SDU_1500 上行SDU采样点个数(大于等于1.5Mbit/s)</Item>
										<Item typeName="String" key="FName">Gx_5D0B0118</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">CQI</Item>
								<Item typeName="String" key="FName"/>
								<Item typeName="IList" key="children">
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">Sample 采样点个数</Item>
										<Item typeName="String" key="FName">Gx_5D0B0201</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">Total 总值</Item>
										<Item typeName="String" key="FName">Gx_5D0B0202</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
									<Item typeName="IDictionary">
										<Item typeName="String" key="Name">Mid_Total 中值</Item>
										<Item typeName="String" key="FName">Gx_5D0B0203</Item>
										<Item typeName="Int32" key="FTag">-1</Item>
										<Item typeName="IList" key="children"/>
									</Item>
								</Item>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">WCDMA视频业务参数</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">W网占用时长 wvpDurationWCDMA</Item>
								<Item typeName="String" key="FName">wvpDurationWCDMA</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">G网占用时长 wvpDurationGSM</Item>
								<Item typeName="String" key="FName">wvpDurationGSM</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">W网测试距离 wvpDistanceWCDMA</Item>
								<Item typeName="String" key="FName">wvpDistanceWCDMA</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">G网测试距离 wvpDistanceGSM</Item>
								<Item typeName="String" key="FName">wvpDistanceGSM</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalRSCP≥-90dBm TotalEc/Io≥-12dB的采样点数 wvpRSCP_F90_ECIO_F12</Item>
								<Item typeName="String" key="FName">wvpRSCP_F90_ECIO_F12</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalRSCP≥-85dBm TotalEc/Io≥-10dB的采样点数 wvpRSCP_F85_ECIO_F10</Item>
								<Item typeName="String" key="FName">wvpRSCP_F85_ECIO_F10</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalEc/Io小于-14.00的采样点个数 wvpTotalEc_Io_F14</Item>
								<Item typeName="String" key="FName">wvpTotalEc_Io_F14</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalEc/Io[-14.00,-12.00)的采样点个数 wvpTotalEc_Io_F14_F12</Item>
								<Item typeName="String" key="FName">wvpTotalEc_Io_F14_F12</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalEc/Io[-12.00,-11.00)的采样点个数 wvpTotalEc_Io_F12_F11</Item>
								<Item typeName="String" key="FName">wvpTotalEc_Io_F12_F11</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalEc/Io[-11.00,-10.00)的采样点个数  wvpTotalEc_Io_F11_F10</Item>
								<Item typeName="String" key="FName">wvpTotalEc_Io_F11_F10</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalEc/Io[-10.00,-8.00)的采样点个数  wvpTotalEc_Io_F10_F8</Item>
								<Item typeName="String" key="FName">wvpTotalEc_Io_F10_F8</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalEc/Io>= -8.00的采样点个数 wvpTotalEc_Io_F8</Item>
								<Item typeName="String" key="FName">wvpTotalEc_Io_F8</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BestEc/Io小于-14.00采样点个数 wvpBestEc_Io_F14</Item>
								<Item typeName="String" key="FName">wvpBestEc_Io_F14</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BestEc/Io[-14.00,-12.00)采样点个数  wvpBestEc_Io_F14_F12</Item>
								<Item typeName="String" key="FName">wvpBestEc_Io_F14_F12</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BestEc/Io[-12.00,-11.00)采样点个数 wvpBestEc_Io_F12_F11</Item>
								<Item typeName="String" key="FName">wvpBestEc_Io_F12_F11</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BestEc/Io[-11.00,-10.00)采样点个数 wvpBestEc_Io_F11_F10</Item>
								<Item typeName="String" key="FName">wvpBestEc_Io_F11_F10</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BestEc/Io[-10.00,-8.00)采样点个数 wvpBestEc_Io_F10_F8</Item>
								<Item typeName="String" key="FName">wvpBestEc_Io_F10_F8</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BestEc/Io>= -8.00采样点个数 wvpBestEc_Io_F8</Item>
								<Item typeName="String" key="FName">wvpBestEc_Io_F8</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalRSCP小于-105.00 wvpTotalRSCP_F105</Item>
								<Item typeName="String" key="FName">wvpTotalRSCP_F105</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalRSCP[-105.00,-100.00) wvpTotalRSCP_F105_F100</Item>
								<Item typeName="String" key="FName">wvpTotalRSCP_F105_F100</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalRSCP[-100.00,-90.00) wvpTotalRSCP_F100_F90</Item>
								<Item typeName="String" key="FName">wvpTotalRSCP_F100_F90</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalRSCP[-90.00,-85.00) wvpTotalRSCP_F90_F85</Item>
								<Item typeName="String" key="FName">wvpTotalRSCP_F90_F85</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalRSCP[-85.00,-80.00) wvpTotalRSCP_F85_F80</Item>
								<Item typeName="String" key="FName">wvpTotalRSCP_F85_F80</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TotalRSCP>= -80.00 wvpTotalRSCP_F80</Item>
								<Item typeName="String" key="FName">wvpTotalRSCP_F80</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BestRSCP小于-105.00 wvpBestRSCP_F105</Item>
								<Item typeName="String" key="FName">wvpBestRSCP_F105</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BestRSCP[-105.00,-100.00) wvpBestRSCP_F105_F100</Item>
								<Item typeName="String" key="FName">wvpBestRSCP_F105_F100</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BestRSCP[-100.00,-90.00) wvpBestRSCP_F100_F90</Item>
								<Item typeName="String" key="FName">wvpBestRSCP_F100_F90</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BestRSCP[-90.00,-85.00) wvpBestRSCP_F90_F85</Item>
								<Item typeName="String" key="FName">wvpBestRSCP_F90_F85</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BestRSCP[-85.00,-80.00) wvpBestRSCP_F85_F80</Item>
								<Item typeName="String" key="FName">wvpBestRSCP_F85_F80</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BestRSCP>= -80.00 wvpBestRSCP_F80</Item>
								<Item typeName="String" key="FName">wvpBestRSCP_F80</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RxPower小于-90.00 wvpRxPower_F90</Item>
								<Item typeName="String" key="FName">wvpRxPower_F90</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RxPower[-90.00,-85.00) wvpRxPower_F90_F85</Item>
								<Item typeName="String" key="FName">wvpRxPower_F90_F85</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RxPower[-85.00,-80.00) wvpRxPower_F85_F80</Item>
								<Item typeName="String" key="FName">wvpRxPower_F85_F80</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RxPower[-80.00,-75.00) wvpRxPower_F80_F75</Item>
								<Item typeName="String" key="FName">wvpRxPower_F80_F75</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RxPower>= -75.00 wvpRxPower_F75</Item>
								<Item typeName="String" key="FName">wvpRxPower_F75</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TxPower小于-15.00 wvpTxPower_F15</Item>
								<Item typeName="String" key="FName">wvpTxPower_F15</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TxPower[-15.00,0.00) wvpTxPower_F15_0</Item>
								<Item typeName="String" key="FName">wvpTxPower_F15_0</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TxPower[0.00,10.00)  wvpTxPower_0_10</Item>
								<Item typeName="String" key="FName">wvpTxPower_0_10</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TxPower[10.00,20.00)wvpTxPower_10_20</Item>
								<Item typeName="String" key="FName">wvpTxPower_10_20</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">TxPower>=20.00 wvpTxPower_20</Item>
								<Item typeName="String" key="FName">wvpTxPower_20</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RSCP≥-80dBm TxPower≤-10dBm的采样点数wvpTxPower_F10_RSCP_F80</Item>
								<Item typeName="String" key="FName">wvpTxPower_F10_RSCP_F80</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">SIR小于0.00 wvpSIR_0</Item>
								<Item typeName="String" key="FName">wvpSIR_0</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">SIR[0.00,6.00) wvpSIR_0_6</Item>
								<Item typeName="String" key="FName">wvpSIR_0_6</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">SIR[6.00,9.00) wvpSIR_6_9</Item>
								<Item typeName="String" key="FName">wvpSIR_6_9</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">SIR[9.00,12.00) wvpSIR_9_12</Item>
								<Item typeName="String" key="FName">wvpSIR_9_12</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">SIR>= 12.00 wvpSIR_12</Item>
								<Item typeName="String" key="FName">wvpSIR_12</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BLER[0, 1) wvpBLER_0_1</Item>
								<Item typeName="String" key="FName">wvpBLER_0_1</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BLER[1, 2) wvpBLER_1_2</Item>
								<Item typeName="String" key="FName">wvpBLER_1_2</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BLER[2, 3) wvpBLER_2_3</Item>
								<Item typeName="String" key="FName">wvpBLER_2_3</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BLER[3, 4) wvpBLER_3_4</Item>
								<Item typeName="String" key="FName">wvpBLER_3_4</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BLER[4, 5) wvpBLER_4_5</Item>
								<Item typeName="String" key="FName">wvpBLER_4_5</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">BLER[5, 100] wvpBLER_5_100</Item>
								<Item typeName="String" key="FName">wvpBLER_5_100</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">ActiveSet1 wvpActiveSet_1</Item>
								<Item typeName="String" key="FName">wvpActiveSet_1</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">ActiveSet2 wvpActiveSet_2</Item>
								<Item typeName="String" key="FName">wvpActiveSet_2</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">ActiveSet3 wvpActiveSet_3</Item>
								<Item typeName="String" key="FName">wvpActiveSet_3</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">ActiveSet4 wvpActiveSet_4</Item>
								<Item typeName="String" key="FName">wvpActiveSet_4</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">ActiveSet5 wvpActiveSet_5</Item>
								<Item typeName="String" key="FName">wvpActiveSet_5</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">ActiveSet6 wvpActiveSet_6</Item>
								<Item typeName="String" key="FName">wvpActiveSet_6</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">ActiveSet其他 wvpActiveSet_other</Item>
								<Item typeName="String" key="FName">wvpActiveSet_other</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MOS总采样点数 wvpMOS_sample</Item>
								<Item typeName="String" key="FName">wvpMOS_sample</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MOS总值 wvpMOS_total</Item>
								<Item typeName="String" key="FName">wvpMOS_total</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MOS小于2 wvpMOS_20</Item>
								<Item typeName="String" key="FName">wvpMOS_20</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MOS[2,2.5)  wvpMOS_20_25</Item>
								<Item typeName="String" key="FName">wvpMOS_20_25</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MOS[2.5,3) wvpMOS_25_30</Item>
								<Item typeName="String" key="FName">wvpMOS_25_30</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MOS[3,3.5) wvpMOS_30_35</Item>
								<Item typeName="String" key="FName">wvpMOS_30_35</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MOS[3.5,4) wvpMOS_35_40</Item>
								<Item typeName="String" key="FName">wvpMOS_35_40</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MOS[4,4.5) wvpMOS_40_45</Item>
								<Item typeName="String" key="FName">wvpMOS_40_45</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">MOS>=4.5 wvpMOS_45</Item>
								<Item typeName="String" key="FName">wvpMOS_45</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">wvpRscpTxNum_80_10</Item>
								<Item typeName="String" key="FName">wvpRscpTxNum_80_10</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">wvpRscpNum_80</Item>
								<Item typeName="String" key="FName">wvpRscpNum_80</Item>
								<Item typeName="Int32" key="FTag">-1</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
					<Item typeName="IDictionary">
						<Item typeName="String" key="Name">WCDMA事件参数</Item>
						<Item typeName="String" key="FName"/>
						<Item typeName="IList" key="children">
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_MO_CallAttempt 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">500</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_MO_Established 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">501</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_MO_Setup 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">502</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_MO_Disconnect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">503</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_MO_CallFail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">504</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_MO_Drop 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">505</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_GSM_MO_CallAttempt 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">506</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_GSM_MO_Established 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">507</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_GSM_MO_Setup 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">508</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_GSM_MO_Disconnect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">509</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_GSM_MO_CallFail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">510</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_GSM_MO_Drop 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">511</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_MT_CallAttempt 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">512</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_MT_Established 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">513</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_MT_Setup 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">514</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_MT_Disconnect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">515</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_MT_CallFail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">516</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_MT_Drop 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">517</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_GSM_MT_CallAttempt 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">518</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_GSM_MT_Established 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">519</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_GSM_MT_Setup 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">520</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_GSM_MT_Disconnect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">521</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_GSM_MT_CallFail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">522</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_GSM_MT_Drop 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">523</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_LocationUpdate_Request 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">524</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_LocationUpdate_Success 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">525</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_LocationUpdate_Fail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">526</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_GSM_LocationUpdate_Request 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">527</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_GSM_LocationUpdate_Success 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">528</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_GSM_LocationUpdate_Fail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">529</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_RAU_Request 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">530</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_RAU_Success 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">531</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_RAU_Fail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">532</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_GSM_RAU_Request 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">533</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_GSM_RAU_Success 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">534</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_GSM_RAU_Fail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">535</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_CellReselection_W2G 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">536</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_CellReselection_Fail_W2G 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">537</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_CellReselection_G2W 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">538</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_CellReselection_Fail_G2W 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">539</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_HandoverRequest_W2G 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">540</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_HandoverSuccess_W2G 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">541</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_HandoverFail_W2G 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">542</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_GPRS_HandoverRequest 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">609</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_GPRS_HandoverSuccess 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">610</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_GPRS_HandoverFail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">611</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_HandoverRequest_IntraW 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">543</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_HandoverSuccess_IntraW 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">544</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_HandoverFail_IntraW 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">545</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_SoftSwitch_Request数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">546</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_SoftSwitch_Success 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">547</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_SoftSwitch_Fail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">548</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_HandoverRequest_IntraG 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">549</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_HandoverSuccess_IntraG 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">550</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_HandoverFail_IntraG 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">551</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_R4_DataDrop 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">552</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_GPRSEDGE_DataDrop 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">553</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_VP_MO_CallAttempt 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">554</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_VP_MO_Cir_Setup 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">555</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_VP_MO_Disonnect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">557</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_VP_MO_Drop 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">558</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_VP_MT_CallAttempt 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">559</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_VP_MT_CirConnect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">560</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_VP_MT_Disonnect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">562</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_VP_MT_Drop 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">563</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_PPP_Dial_Start 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">564</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_PPP_Dial_Success 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">565</Item>
								<Item typeName="IList" key="children">
                  <Item typeName="IDictionary">
                    <Item typeName="String" key="Name">建立时延</Item> 
                    <Item typeName="String" key="FName">value3</Item> 
                    <Item typeName="Int32" key="FTag">565</Item> 
                    <Item typeName="IList" key="children" /> 
                  </Item>
                </Item>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_FTP_Download_Connect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">566</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_FTP_Send_RETR 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">567</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_FTP_Download_First_Data 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">568</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_FTP_Download_Last_Data 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">569</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_FTP_Download_Disconnect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">570</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_FTP_Download_Drop 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">571</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RRC Connection Completed 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">572</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RAB Setup 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">573</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RAB Setup Completed 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">574</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">RAB Setup Fail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">575</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_CellUPdate 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">576</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_CellUPdateConfirm 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">577</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_CellReselection_W2W 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">578</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_CellReselection_Fail_W2W 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">579</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_CellReselection_G2G 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">580</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_CellReselection_Fail_G2G 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">581</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_VP_MO_Connect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">582</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_VP_MO_Failed 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">583</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_VP_MT_Connect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">584</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_VP_MT_Failed 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">585</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_WeakCoverage 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">586</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_WeakQuality 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">607</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_LowRate 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">587</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_MO_NOConnect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">588</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_MT_NOConnect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">589</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_VP_MO_NOConnect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">590</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_VP_MT_NOConnect 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">591</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_VP_MO_WeakCoverDrop 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">592</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_VP_MT_WeakCoverDrop 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">593</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_VP_MO_WeakCoverCallFail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">594</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_VP_MT_WeakCoverCallFail 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">595</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA-2G主叫接通 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">596</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA-2G被叫接通 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">597</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA-2G主叫掉话 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">598</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA-2G被叫掉话 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">599</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA-2G主叫呼叫失败 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">600</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA-2G被叫呼叫失败 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">601</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_MO_CallAttemptRetry 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">602</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_MT_CallAttemptRetry 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">603</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_M0_Drop_NotNormal 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">612</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_MT_Drop_NotNormal 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">613</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_M0_VP_Drop_NotNormal 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">614</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_MT_VP_Drop_NotNormal 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">615</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_GSM_M0_Drop_NotNormal 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">616</Item>
								<Item typeName="IList" key="children"/>
							</Item>
							<Item typeName="IDictionary">
								<Item typeName="String" key="Name">WCDMA_GSM_MT_Drop_NotNormal 数量</Item>
								<Item typeName="String" key="FName">evtIdCount</Item>
								<Item typeName="Int32" key="FTag">617</Item>
								<Item typeName="IList" key="children"/>
							</Item>
						</Item>
					</Item>
				</Item>
			</Item>
		</Item>
	</Config>
</Configs>
