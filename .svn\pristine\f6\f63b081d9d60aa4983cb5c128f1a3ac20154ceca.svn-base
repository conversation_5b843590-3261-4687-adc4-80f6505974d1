﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using System.IO;
using System.Windows.Forms;
using System.Net;
using MasterCom.MTGis;
using MasterCom.RAMS.BackgroundFunc;

namespace MasterCom.RAMS.Net
{
    public static partial class RequestType
    {
        public const byte REQTYPE_DIY_AREA_COVER_SAMPLE = 0x11;	//REQUEST
        public const byte REQTYPE_DIY_CELL_SAMPLE = 0x17;
    }
    public static partial class ResponseType
    {
        public const byte RESTYPE_DIY_AREA_COVER_SAMPLE_GSMV = 0xa1;

        public const byte RESTYPE_DIY_AREA_COVER_SAMPLE_GPRS = 0xa5;

        public const byte RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN = 0xa7;

        public const byte RESTYPE_DIY_AREA_COVER_SAMPLE_TDSCDMA_V = 0xa9;

        public const byte RESTYPE_DIY_AREA_COVER_SAMPLE_TDSCDMA_D = 0xab;

        public const byte RESTYPE_DIY_AREA_COVER_SAMPLE_WCDMA_V = 0xb0;

        public const byte RESTYPE_DIY_AREA_COVER_SAMPLE_WCDMA_D = 0xb2;

        public const byte RESTYPE_DIY_AREA_COVER_SAMPLE_CDMA_V = 0xb8;

        public const byte RESTYPE_DIY_AREA_COVER_SAMPLE_CDMA_D = 0xba;

        public const byte RESTYPE_DIY_AREA_COVER_SAMPLE_CDMA2000_D = 0xbc;

        public const byte RESTYPE_DIY_AREA_COVER_SAMPLE_CDMA2000_V = 0xbe;

        public const byte RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_GSM = 0xc0;

        public const byte RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_TD = 0xc2;

        public const byte RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_W = 0xc4;

        public const byte RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_FREQSPECTURM = 0xca;

        public const byte RESTYPE_DIY_AREA_COVER_SAMPLE__LTE = 0xc8;

        public const byte RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_LTE = 0xcc;

        public const byte RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_CW = 0xce;
        public const byte AREA_SAMPLE_LTE_UEP = 0xd0;
        public const byte RESTYPE_DIY_AREA_COVER_SAMPLE_LTE_FDD = 0xd1;
        public const byte RESTYPE_DIY_AREA_COVER_SAMPLE_SIGNAL = 0xd2;

        public const byte RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_NBIOT_TOPN = 0xd4;

        public const byte RESTYPE_DIY_AREA_COVER_SAMPLE_NR = 0xd5;
        public const byte RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_NR_TOPN = 0xd6;
    }

    public abstract class DIYSampleQuery :BackgroundQueryBase
    {
        protected bool isAddSampleToDTDataManager = true;//是否把采样点信息放到DTDataManager，默认为放到DTDataManager
        protected DIYSampleQuery(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.None; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.其他; }
        }

        public override BackgroundStatType StatType
        {
            get { return BackgroundStatType.None; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected virtual void AddDIYRegion_Intersect(Package package,CellQueryCondUnit cond)
        {
            package.Content.AddParam((byte)OpOptionDef.AreaSelectIntersect);
            package.Content.AddParam(cond.ltLongitude);
            package.Content.AddParam(cond.ltLatitude);
            package.Content.AddParam(cond.brLongitude);
            package.Content.AddParam(cond.brLatitude);
        }

        protected virtual void fillContentNeeded_Sample(Package package)
        {
            if (curSelDIYSampleGroup == null)
            {
                throw (new Exception("没有选择的参数指标！"));
            }
            List<ColumnDefItem> colDefList = getNeededColumnDefList(curSelDIYSampleGroup);
            StringBuilder sbuilder = new StringBuilder();
            sbuilder.Append("0,5,29,0,5,33,0,5,22,0,5,25,0,5,28,0,5,34,0,5,21,0,5,26,0,5,30,0,5,32,0,5,20,0,5,24,0,5,27,0,5,31,0,5,19,0,5,23,0,5,46,");//bms
            sbuilder.Append("0,4,29,0,4,33,0,4,22,0,4,25,0,4,28,0,4,34,0,4,21,0,4,26,0,4,30,0,4,32,0,4,20,0,4,24,0,4,27,0,4,31,0,4,19,0,4,23,0,4,46,");////wtimems
            sbuilder.Append("0,1,29,0,1,33,0,1,22,0,1,25,0,1,28,0,1,34,0,1,21,0,1,26,0,1,30,0,1,32,0,1,20,0,1,24,0,1,27,0,1,31,0,1,19,0,1,23,0,2,46,");//isampleid
            for (int i = 0; i < colDefList.Count; i++)
            {
                ColumnDefItem sid = colDefList[i];
                sbuilder.Append(sid.GetTriIdStr());
                if (i < colDefList.Count - 1)
                {
                    sbuilder.Append(",");
                }
            }
            package.Content.AddParam(sbuilder.ToString());
        }

        protected List<ColumnDefItem> getNeededColumnDefList(DIYSampleGroup group)
        {
            InterfaceManager manager = InterfaceManager.GetInstance();
            List<ColumnDefItem> retList = new List<ColumnDefItem>();
            Dictionary<string, ColumnDefItem> triIdTofixColumnsDic = new Dictionary<string, ColumnDefItem>();
            Dictionary<string, ColumnDefItem> tbIdAndImgIdToNonFixColumnsDic = new Dictionary<string, ColumnDefItem>();
            foreach (DIYSampleParamDef paraDef in group.ColumnsDefSet)
            {
                if (paraDef.parameter != null)
                {
                    addColumnDef(manager, triIdTofixColumnsDic, tbIdAndImgIdToNonFixColumnsDic, paraDef);
                }
            }
            foreach (ColumnDefItem col in triIdTofixColumnsDic.Values)
            {
                retList.Add(col);
            }
            foreach (ColumnDefItem col in tbIdAndImgIdToNonFixColumnsDic.Values)
            {
                retList.Add(col);
            }
            return retList;
        }

        private void addColumnDef(InterfaceManager manager, Dictionary<string, ColumnDefItem> triIdTofixColumnsDic, Dictionary<string, ColumnDefItem> tbIdAndImgIdToNonFixColumnsDic, DIYSampleParamDef paraDef)
        {
            List<ColumnDefItem> columnDefList = manager.GetColumnDefByShowName(paraDef.parameter.Info.Name);
            if (columnDefList != null)
            {
                foreach (ColumnDefItem columnDef in columnDefList)
                {
                    if (columnDef == null)
                    {
                        continue;
                    }
                    if (columnDef.fix)
                    {
                        triIdTofixColumnsDic[columnDef.GetTriIdStr()] = columnDef;
                    }
                    else
                    {
                        string colDicKey = columnDef.tableName + "," + columnDef.imgID;
                        tbIdAndImgIdToNonFixColumnsDic[colDicKey] = columnDef;
                    }
                }
            }
        }

        protected virtual DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            if (dlg == null)
            {
                dlg = new SelectSampleGroupDlg();
                dlg.InitLoadInfo();
            }
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return null;
            }
            return  dlg.GetSelectedSampleGroup();
        }


        protected SelectSampleGroupDlg dlg = null;
        protected DIYSampleGroup curSelDIYSampleGroup = null;
        //查询入口
        protected override void query()
        {
            if (!getConditionBeforeQuery())
            {
                return;
            }
            curSelDIYSampleGroup = getCurSelDIYSampleGroupPrepare();
            if (curSelDIYSampleGroup == null)
            {
                return;
            }
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                MainModel.ClearDTData();
                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);

                FireShowFormAfterQuery();//在该方法最后设置了默认的指标

                //加多一个对ThemeName内容的判断，避免默认指标被清空
                if (curSelDIYSampleGroup.ThemeName != null && curSelDIYSampleGroup.ThemeName != "")
                {
                    MainModel.FireSetDefaultMapSerialTheme(curSelDIYSampleGroup.ThemeName);
                }
                MainModel.FireDTDataChanged(this);
            }
            catch(Exception e)
            {
                MessageBox.Show(e.Source + Environment.NewLine + e.Message);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected virtual bool getConditionBeforeQuery()
        {
            return true;
        }

        protected virtual void FireShowFormAfterQuery()
        {

        }
//开始查询
        protected virtual void queryInThread(object o) 
        {
            try
            {
                doSomethingBeforeQueryInThread();
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                int periodCount = Condition.Periods.Count;
                int index = 0;
                if (condition.IsByRound)
                {
                    queryPeriodInfo(clientProxy, package, null, true);
                }
                else
                {
                    foreach (TimePeriod period in Condition.Periods)
                    {
                        if (WaitBox.CancelRequest)
                        {
                            break;
                        }
                        WaitBox.Text = "开始统计时段[" + period.GetShortString() + "]内的数据...";
                        WaitBox.ProgressPercent = index / periodCount + 10;
                        queryPeriodInfo(clientProxy, package, period, false);
                    }
                }
                getResultAfterQuery();
            }catch(Exception ex)
            {
                log.Error(ex.Message + Environment.NewLine + ex.StackTrace);
            }
            finally
            {
                WaitBox.Close();
            }
        }

        protected virtual void doSomethingBeforeQueryInThread()
        {

        }

        protected virtual void getResultAfterQuery()
        {

        }

        protected virtual void queryPeriodInfo(ClientProxy clientProxy, Package package, TimePeriod period,bool byround)
        {
            MainModel.IsFusionInclude = false;
            prepareStatPackage_Sample_FileFilter(package, period,byround);
            prepareStatPackage_Sample_SampleFilter(package, period);
            fillContentNeeded_Sample(package);
            clientProxy.Send();
            recieveInfo_Sample(clientProxy);
        }


        protected virtual void recieveInfo_Sample(ClientProxy clientProxy)
        {
            DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            List<ColumnDefItem> curSampleColumnDef = new List<ColumnDefItem>();
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    DTDataHeader fileInfo = recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
                    if (fileInfo != null)
                    {
                        DTDataHeaderManager.GetInstance().AddDTDataHeader(fileInfo);
                    }
                }
                else if (package.Content.Type == ResponseType.COLUMN_DEFINE)
                {
                    curSampleColumnDef.Clear();
                    parseToCurColumnDef(package.Content.GetParamString(), curSampleColumnDef);
                }
                else if (recieveAndSaveTestPoint(package, curSampleColumnDef))
                {//不能去掉else if ，recieveAndSaveTestPoint会进行采样点类型判断

                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                #endregion

                setProgressPercent(ref index, ref progress);
            }
        }

        protected virtual bool recieveAndSaveTestPoint(Package package, List<ColumnDefItem> curSampleColumnDef)
        {
            TestPoint tp = getSpecificTestPoint(package);
            if (tp == null)
            {
                return false;
            }
            fillTestPoint(package, curSampleColumnDef, tp);
            saveTestPoint(tp);
            return true;
        }

        protected virtual void saveTestPoint(TestPoint tp)
        {
            DTDataHeader header = DTDataHeaderManager.GetInstance().GetHeaderByFileID(tp.FileID);
            if (header == null) 
                return;
            if (!isValidTestPoint(tp)) 
                return;
            doForAttenuation(tp);
            if (doForLTEScanWorkMode(tp))
            {
                tp.ApplyHeader(header);
                if (isAddSampleToDTDataManager)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                doWithDTData(tp);
                FireDoWithDTData(tp);
            }
        }

        private bool setValidTpParam<T>(TestPoint tpPoint, ColumnDefItem cdf, string datav, T defaultData)
        {
            if (datav != cdf.nullValue)
            {
                if (cdf.ShowArrayIndex == -1)
                {
                    tpPoint[cdf.showName] = defaultData;
                    return true;
                }
                else
                {
                    tpPoint[cdf.ShowArrayName, cdf.ShowArrayIndex] = defaultData;
                }
            }
            return false;
        }

        protected virtual void fillTestPoint(Package package, List<ColumnDefItem> curSampleColumnDef, TestPoint tp)
        {
            //===固定 begin
            tp.Longitude = package.Content.GetParamDouble();
            tp.Latitude = package.Content.GetParamDouble();
            tp.FileID = package.Content.GetParamInt();
            tp.Time = package.Content.GetParamInt();
            //===end
            foreach (ColumnDefItem cdf in curSampleColumnDef)
            {
                if (cdf.fix)
                {
                    fillFixParam(package, tp, cdf);
                }
                else// non fix
                {
                    fillNonFixParam(package, tp, cdf);
                }
            }
        }

        private void fillFixParam(Package package, TestPoint tp, ColumnDefItem cdf)
        {
            switch (cdf.vType)
            {
                case E_VType.E_Int:
                    {
                        int datav = package.Content.GetParamInt();
                        bool isSet = setValidTpParam(tp, cdf, datav.ToString(), datav);
                        if (isSet && cdf.showName == "isampleid")
                        {
                            tp.SN = datav;
                        }
                    }
                    break;
                case E_VType.E_Short:
                    {
                        short datav = package.Content.GetParamShort();
                        bool isSet = setValidTpParam(tp, cdf, datav.ToString(), datav);
                        if (isSet && cdf.showName == "wtimems")
                        {
                            tp.Millisecond = datav;
                        }
                    }
                    break;
                case E_VType.E_Byte:
                    {
                        byte datav = package.Content.GetParamByte();
                        setValidTpParam(tp, cdf, datav.ToString(), datav);
                    }
                    break;
                case E_VType.E_Float:
                case E_VType.E_IntFloat:
                    {
                        int datav = package.Content.GetParamInt();
                        setValidTpParam(tp, cdf, datav.ToString(), datav / 1000.0f);
                    }
                    break;
                case E_VType.E_String:
                    {
                        string datav = package.Content.GetParamString();
                        if (datav != null && datav != "")
                        {
                            setValidTpParam(tp, cdf, datav, datav);
                        }
                    }
                    break;
                case E_VType.E_UShort:
                    {
                        ushort datav = package.Content.GetParamUShort();
                        setValidTpParam(tp, cdf, datav.ToString(), datav);
                    }
                    break;
                case E_VType.E_Int64:
                    {
                        long datav = package.Content.GetParamInt64();
                        setValidTpParam(tp, cdf, datav.ToString(), datav);
                    }
                    break;
                case E_VType.E_UInt64:
                    {
                        UInt64 datav = package.Content.GetParamUInt64();
                        long data = (long)datav;
                        setValidTpParam(tp, cdf, data.ToString(), data);
                    }
                    break;
                default:
                    package.Content.GetParamByte();//bt
                    break;
            }
        }

        private void fillNonFixParam(Package package, TestPoint tp, ColumnDefItem cdf)
        {
            byte[] imgBytes = package.Content.GetParamBytes();

            List<ColumnDefItem> nonFixInColumns = InterfaceManager.GetInstance().GetOtherDefInSameColumn(cdf);
            foreach (ColumnDefItem nfColDef in nonFixInColumns)
            {
                byte grpCount = imgBytes[0];
                byte grpAt = (byte)(nfColDef.paraID / 1000);
                if (grpAt > grpCount)
                {
                    continue;
                }
                int offsetx = initOffsetx(imgBytes, grpCount, grpAt);
                setImgStringParam(tp, imgBytes, nfColDef, offsetx);
            }
        }

        private int initOffsetx(byte[] imgBytes, byte grpCount, byte grpAt)
        {
            int offsetx = 1;
            for (byte c = 0; c < grpCount && c < grpAt - 1; c++)
            {
                byte countOfArr = imgBytes[offsetx++];
                byte eachArrSize = imgBytes[offsetx++];
                offsetx += eachArrSize * countOfArr;
            }

            return offsetx;
        }

        private void setImgStringParam(TestPoint tp, byte[] imgBytes, ColumnDefItem nfColDef, int offsetx)
        {
            byte ctOfNeib = imgBytes[offsetx++];
            byte szOfEachNeib = imgBytes[offsetx++];
            if (ctOfNeib > 0 && ctOfNeib <= nfColDef.maxArrCount)
            {
                for (int i = 0; i < ctOfNeib; i++)
                {
                    int startIdx = offsetx + i * szOfEachNeib + nfColDef.posFrom;
                    if (startIdx == imgBytes.Length)
                    {//一种可能情况：新加的参数，已入库的文件没有该参数。以新配置来解析image时，数组越界
                        break;
                    }
                    object arrObj = getArrObj(imgBytes, nfColDef, startIdx);
                    if (arrObj != null && arrObj.ToString() != nfColDef.nullValue)
                    {//有效值
                        tp[nfColDef.showName, i] = arrObj;
                    }
                }
            }
        }

        private object getArrObj(byte[] imgBytes, ColumnDefItem nfColDef, int startIdx)
        {
            object arrObj = null;
            switch (nfColDef.vType)
            {
                case E_VType.E_Byte:
                    if (imgBytes.Length - startIdx >= 1)
                    {
                        arrObj = imgBytes[startIdx];
                    }
                    break;
                case E_VType.E_Int:
                    if (imgBytes.Length - startIdx >= 4)
                    {
                        int valueInt = BitConverter.ToInt32(imgBytes, startIdx);
                        arrObj = IPAddress.NetworkToHostOrder(valueInt);
                    }
                    break;
                case E_VType.E_Short:
                    if (imgBytes.Length - startIdx >= 2)
                    {
                        short valueShort = BitConverter.ToInt16(imgBytes, startIdx);
                        arrObj = IPAddress.NetworkToHostOrder(valueShort);
                    }
                    break;
                case E_VType.E_UShort:
                    if (imgBytes.Length - startIdx >= 2)
                    {
                        short valShort = BitConverter.ToInt16(imgBytes, startIdx);
                        arrObj = (ushort)IPAddress.NetworkToHostOrder(valShort);
                    }
                    break;
                case E_VType.E_Float:
                case E_VType.E_IntFloat:
                    if (imgBytes.Length - startIdx >= 4)
                    {
                        int valueFloatInt = BitConverter.ToInt32(imgBytes, startIdx);
                        if (valueFloatInt.ToString() != nfColDef.nullValue)
                        {
                            arrObj = IPAddress.NetworkToHostOrder(valueFloatInt) / 1000.0f;
                        }
                    }
                    break;
                case E_VType.E_String:
                    break;
                default:
                    break;
            }

            return arrObj;
        }

        protected TestPoint getSpecificTestPoint(Package package)
        {
            TestPoint tp = null;
            switch (package.Content.Type)
            {
                case ResponseType.RESTYPE_DIY_AREA_COVER_SAMPLE_TDSCDMA_V:
                case ResponseType.RESTYPE_DIY_AREA_COVER_SAMPLE_TDSCDMA_D:
                    tp = new TDTestPointDetail();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_SAMPLE_GSMV:
                case ResponseType.RESTYPE_DIY_AREA_COVER_SAMPLE_GPRS:
                    tp = new TestPointDetail();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_SAMPLE_WCDMA_V: 
                case ResponseType.RESTYPE_DIY_AREA_COVER_SAMPLE_WCDMA_D:
                    tp = new WCDMATestPointDetail();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_SAMPLE_CDMA_V:
                case ResponseType.RESTYPE_DIY_AREA_COVER_SAMPLE_CDMA_D:
                    tp = new CDMATestPointDetail();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_SAMPLE_CDMA2000_V:
                case ResponseType.RESTYPE_DIY_AREA_COVER_SAMPLE_CDMA2000_D:
                    tp = new CDMATestPointDetail();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_SAMPLE__LTE:
                    tp = new LTETestPointDetail();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_GSM:
                    tp = new ScanTestPoint_G();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_TD:
                    tp = new ScanTestPoint_TD();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_W:
                    tp = new ScanTestPoint_W();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_LTE:
                    tp = new ScanTestPoint_LTE();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_CW:
                    tp = new ScanTestPoint_CW();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_FREQSPECTURM:
                    tp = new ScanTestPoint_FreqSpecturm();
                    break;
                case ResponseType.AREA_SAMPLE_LTE_UEP:
                    tp = new LTEUepTestPoint();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_SAMPLE_LTE_FDD:
                    tp = new LTEFddTestPoint();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_SAMPLE_SIGNAL:
                    tp = new SignalTestPoint();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_NBIOT_TOPN:
                    tp = new ScanTestPoint_NBIOT();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_SAMPLE_NR:
                    tp = new TestPoint_NR();
                    break;
                case ResponseType.RESTYPE_DIY_AREA_COVER_SAMPLE_SCAN_NR_TOPN:
                    tp = new ScanTestPoint_NR();
                    break;
                default:
                    break;
            }
            return tp;
        }

        protected virtual void doWithDTData(TestPoint tp)
        {
        }

        protected virtual void prepareStatPackage_Sample_FileFilter(Package package, TimePeriod period,bool byRound)
        {
        }
        protected virtual void prepareStatPackage_Sample_SampleFilter(Package package, TimePeriod period)
        {
        }
        protected virtual bool isValidTestPoint(TestPoint tp)
        {
            return true;
        }

    }
}
