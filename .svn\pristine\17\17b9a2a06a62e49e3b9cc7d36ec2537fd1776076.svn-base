﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.ES.Data;
using MasterCom.RAMS.Model.Interface;
using System.Windows.Forms;
using MasterCom.RAMS.AnaZT;
using MasterCom.MTGis;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.Net
{
    public class GatherScanOnIdleGridQuery : DIYSampleQuery
    {
        private readonly IdleCellsInGrid[,] idleMatrix = null;
        private readonly DbRect bounds;
        public GatherScanOnIdleGridQuery(MainModel mainModel, IdleCellsInGrid[,] matrix, DbRect bounds)
            : base(mainModel)
        {
            this.idleMatrix = matrix;
            this.bounds = bounds;
        }
        public override string Name
        {
            get { return "获取扫频数据进行缺失校验"; }
        }
        public override string IconName
        {
            get { return "Images/regionstat.gif"; }
        }
        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }
        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 1300, 1301, this.Name);
        }
        protected override void prepareStatPackage_Sample_FileFilter(Package package, TimePeriod period, bool byRound)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            if (!isPreSetted)
            {
                package.Content.Type = RequestType.REQTYPE_DIY_AREA_COVER_SAMPLE;
            }
            else
            {
                package.Content.Type = this.preSettedCommandType;
            }
            package.Content.PrepareAddParam();
            if (byRound)
            {
                AddDIYRound(package, condition.ByRoundYear, condition.ByRoundRound);
            }
            else
            {
                AddDIYPeriod(package, period);
            }
            AddDIYRegion_Intersect(package);
            AddDIYProject(package, condition.Projects);
            condition.ServiceTypes.Clear();
            condition.ServiceTypes.Add(12);//GSM Scan测试
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYCarrierType(package, condition.CarrierTypes);
            AddDIYFileFilter(package, condition);

            AddDIYStatStatus(package);
            AddDIYAreaTypeAndID(package, condition.Areas);
            //
            AddDIYEndOpFlag(package);

        }
        protected override void prepareStatPackage_Sample_SampleFilter(Package package, TimePeriod period)
        {
            AddDIYRegion_Sample(package);
            AddDIYEndOpFlag(package);
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }
        int scanDBOffset = 0;
        protected override bool isValidTestPoint(TestPoint tp)
        {
            try
            {
                bool inRegion = Condition.Geometorys.GeoOp.Contains(tp.Longitude, tp.Latitude);
                if (inRegion && tp is ScanTestPoint_G)//进行过覆盖算法运算
                {
                    IdleCellsInGrid cu = null;
                    int rAt, cAt;
                    getInPlace(tp.Longitude, tp.Latitude, bounds, out rAt, out cAt);
                    if (rAt >= 0 && cAt >= 0 && rAt < idleMatrix.GetLength(0) && cAt < idleMatrix.GetLength(1))
                    {
                        cu = idleMatrix[rAt, cAt];
                        if (cu == null)
                        {
                            return false;//改栅格没有找到数据，忽略掉
                        }
                        else
                        {
                            cu.updateMaxIdleRxlev();
                        }
                        return dealTPInfo(tp, cu);
                    }
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        private bool dealTPInfo(TestPoint tp, IdleCellsInGrid cu)
        {
            for (int i = 0; i < 16; i++)
            {
                short? bcch = (short?)(int?)tp["GSCAN_BCCH", i];
                byte? bsic = (byte?)(int?)tp["GSCAN_BSIC", i];
                if (bcch == null && bsic == null)
                {
                    return false;
                }
                else if (bcch != null && bsic != null)
                {
                    bool isContinue = setLostScannedCell(tp, cu, i, bcch, bsic);
                    if (!isContinue)
                    {
                        return false;
                    }
                }
            }
            return false;
        }

        private bool setLostScannedCell(TestPoint tp, IdleCellsInGrid cu, int i, short? bcch, byte? bsic)
        {
            Cell cell = CellManager.GetInstance().GetNearestCell(tp.DateTime, (short)bcch, (byte)bsic, tp.Longitude, tp.Latitude);
            if (cell != null && cell.GetDistance(tp.Longitude, tp.Latitude) < CD.MAX_COV_DISTANCE_GSM)
            {
                int? tm_rxlev = (int?)tp["GSCAN_RxLev", i];
                if (tm_rxlev != null)
                {
                    if (tm_rxlev - scanDBOffset > cu._MaxIdleRxlev)
                    {
                        if (!cu.idleCellsDic.ContainsKey(cell.Name))
                        {
                            cu.GatherScannedLost(cell.Name, (short)tm_rxlev);
                        }
                    }
                    else
                    {
                        return false;//忽略其余的，之后的不可能有比_MaxIdleRxlev大的了
                    }
                }
            }
            return true;
        }

        private void getInPlace(double longitude, double latitude, DbRect bounds, out int rAt, out int cAt)
        {
            double xDis = longitude - bounds.x1;
            cAt = (int)(Math.Round(xDis / IdleLeakNeighboursQuery.GRID_SPAN_LONG));
            double yDis = bounds.y2 - latitude;
            rAt = (int)(Math.Round(yDis / IdleLeakNeighboursQuery.GRID_SPAN_LAT));
        }

        protected override void query()
        {
            curSelDIYSampleGroup = makeScanSampleGroup();

            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }

                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
        }
        private DIYSampleGroup makeScanSampleGroup()
        {
            DIYSampleGroup group = new DIYSampleGroup();
            group.ColumnsDefSet.Add(new DIYSampleParamDef(DTParameterManager.GetInstance().GetParameter("GSCAN_BCCH")));
            group.ColumnsDefSet.Add(new DIYSampleParamDef(DTParameterManager.GetInstance().GetParameter("GSCAN_BSIC")));
            group.ColumnsDefSet.Add(new DIYSampleParamDef(DTParameterManager.GetInstance().GetParameter("GSCAN_RxLev")));
            return group;
        }


        internal void SetScanDBOffset(int offset)
        {
            this.scanDBOffset = offset;
        }
    }
}
