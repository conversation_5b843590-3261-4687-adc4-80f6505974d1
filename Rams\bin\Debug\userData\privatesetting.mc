<?xml version="1.0"?>
<Configs>
  <Config name="UserConfig">
    <Item name="UserPrivateConfigParams" typeName="IDictionary">
      <Item typeName="IDictionary" key="ItemSelection">
        <Item typeName="IList" key="ProjectGroups" />
        <Item typeName="IList" key="ServiceGroups">
          <Item typeName="IDictionary">
            <Item typeName="String" key="GroupName">LTE-TDD</Item>
            <Item typeName="IList" key="GroupItemIDs">
              <Item typeName="Int32">34</Item>
              <Item typeName="Int32">41</Item>
              <Item typeName="Int32">42</Item>
              <Item typeName="Int32">44</Item>
              <Item typeName="Int32">33</Item>
              <Item typeName="Int32">43</Item>
              <Item typeName="Int32">51</Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="GroupName">LTE-FDD</Item>
            <Item typeName="IList" key="GroupItemIDs">
              <Item typeName="Int32">46</Item>
              <Item typeName="Int32">47</Item>
              <Item typeName="Int32">48</Item>
              <Item typeName="Int32">45</Item>
              <Item typeName="Int32">49</Item>
              <Item typeName="Int32">52</Item>
              <Item typeName="Int32">51</Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="GroupName">GSM</Item>
            <Item typeName="IList" key="GroupItemIDs">
              <Item typeName="Int32">3</Item>
              <Item typeName="Int32">2</Item>
              <Item typeName="Int32">29</Item>
              <Item typeName="Int32">22</Item>
              <Item typeName="Int32">24</Item>
              <Item typeName="Int32">12</Item>
              <Item typeName="Int32">1</Item>
              <Item typeName="Int32">39</Item>
            </Item>
          </Item>
          <Item typeName="IDictionary">
            <Item typeName="String" key="GroupName">NB-IoT</Item>
            <Item typeName="IList" key="GroupItemIDs">
              <Item typeName="Int32">55</Item>
              <Item typeName="Int32">56</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IList" key="AgentGroups" />
      </Item>
    </Item>
  </Config>
</Configs>