﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using System.Xml;

namespace MasterCom.RAMS.AnyStat
{
    public partial class AnyStatParamDialog : BaseFormStyle
    {
        public AnyStatParamDialog()
        {
            InitializeComponent();
            InitEvent();
            intAllInfo();
        }

        public AnyStatUnit GetAnyStatUnit()
        {
            if (listBoxColumn.Items.Count <= numKeyCount.Value)
            {
                MessageBox.Show(this, "关键列数量必须小于总列数！", "条件错误");
                return null;
            }
            for (int i = (int)numKeyCount.Value; i < listBoxColumn.Items.Count; i++)
            {
                AnyColumnDef def = listBoxColumn.Items[i] as AnyColumnDef;
                if (def.parameter == null)
                {
                    MessageBox.Show(this, "第" + (i + 1) + "列设置为非关键列，必须取自参数！", "条件错误");
                    return null;
                }
            }
            AnyStatUnit anyUnit = new AnyStatUnit();
            for (int i = 0; i < listBoxColumn.Items.Count; i++)
            {
                anyUnit.columnsDef.Add(listBoxColumn.Items[i] as AnyColumnDef);
            }
            anyUnit.keyColumnCount = (int)numKeyCount.Value;
            return anyUnit;
        }

        private void btnStartAnyStat_Click(object sender, EventArgs e)
        {
            if (GetAnyStatUnit() != null)
            {
                this.DialogResult = DialogResult.OK;
            }
        }

        private void btnAddColumn_Click(object sender, EventArgs e)
        {
            if (listBoxColumn.Items.Count >= 50)
            {
                MessageBox.Show(this, "最多允许列数50，已经达到！", "列过多");
                return;
            }
            TextInputBox input = new TextInputBox("追加新列", "输入列标题", "新列");
            if (DialogResult.OK == input.ShowDialog(this))
            {
                string textInput = input.TextInput;
                AnyColumnDef columnDef = new AnyColumnDef();
                columnDef.name = textInput;
                listBoxColumn.Items.Add(columnDef);
                freshColumnToConfig();
            }
        }

        private void btnInsertColumn_Click(object sender, EventArgs e)
        {
            if (listBoxColumn.Items.Count >= 50)
            {
                MessageBox.Show(this, "最多允许列数50，已经达到！", "列过多");
                return;
            }
            TextInputBox input = new TextInputBox("插入新列", "输入列标题", "新列");
            if (DialogResult.OK == input.ShowDialog(this))
            {
                string textInput = input.TextInput;
                AnyColumnDef columnDef = new AnyColumnDef();
                columnDef.name = textInput;
                int curSelPos = listBoxColumn.SelectedIndex;
                if (curSelPos >= 0)
                {
                    listBoxColumn.Items.Insert(curSelPos, columnDef);
                }
                else
                {
                    listBoxColumn.Items.Add(columnDef);
                }
                listBoxColumn.SelectedItem = columnDef;
                freshColumnToConfig();
            }
        }

        private void btnDeleteColumn_Click(object sender, EventArgs e)
        {
            if (listBoxColumn.SelectedItems.Count > 0)
            {
                listBoxColumn.Items.Remove(listBoxColumn.SelectedItems[0]);
            }
            if (listBoxColumn.Items.Count > 0)
            {
                listBoxColumn.SelectedIndex = 0;
            }
            freshColumnToConfig();
        }

        private void btnMoveUp_Click(object sender, EventArgs e)
        {
            if (listBoxColumn.SelectedItem != null)
            {
                int selAt = listBoxColumn.SelectedIndex;
                if (selAt - 1 >= 0)
                {
                    swapColumn(selAt, selAt - 1);
                    freshColumnToConfig();
                }

            }
        }

        private void btnMoveDown_Click(object sender, EventArgs e)
        {
            if (listBoxColumn.SelectedItem != null)
            {
                int selAt = listBoxColumn.SelectedIndex;
                if (selAt + 1 < listBoxColumn.Items.Count)
                {
                    swapColumn(selAt, selAt + 1);
                    freshColumnToConfig();
                }
            }
        }

        private void btnApply_Click(object sender, EventArgs e)
        {
            if (listBoxColumn.SelectedItem is AnyColumnDef)
            {
                //check input valid
                if (rbFromParam.Checked)
                {
                    if (cbxParaInfomation.SelectedItem == null)
                    {
                        MessageBox.Show(this, "请选择列的参数！", "选择");
                        return;
                    }
                }
                else if (rbFromOther.Checked)
                {
                    //
                }
                else
                {
                    MessageBox.Show(this, "请选择列的取值类型！", "选择");
                    return;
                }
                if (chkSplitRange.Checked && listRanges.Items.Count == 0)
                {
                    MessageBox.Show(this, "请指定分区间累计的各个值域！", "选择");
                    return;
                }
                setAnyColumnDef();
                freshListView();
                freshColumnToConfig();
            }
        }

        private void setAnyColumnDef()
        {
            AnyColumnDef cs = listBoxColumn.SelectedItem as AnyColumnDef;
            cs.name = tbxColumnTitle.Text;
            if (rbFromParam.Checked)
            {
                string sysname = cbxParaSystem.SelectedItem.ToString();
                string paraname = cbxParaInfomation.SelectedItem.ToString();
                int argAt = -1;
                if (cbxParaArg.Enabled)
                {
                    argAt = cbxParaArg.SelectedIndex;
                }
                DTDisplayParameter displayParam = DTDisplayParameterManager.GetInstance()[sysname, paraname, argAt];
                DTParameter dtParameter = displayParam.Info.ParamInfo[displayParam.ArrayIndex];
                cs.parameter = dtParameter;
                cs.valueRangeCheck = false;
                cs.minRange = 0;
                cs.maxRange = 0;
                if ((displayParam.Info.Type & (int)DTDisplayParameterInfoType.Range) != 0
                    && displayParam.Info.ValueMin < displayParam.Info.ValueMax)
                {
                    cs.valueRangeCheck = true;
                    cs.minRange = displayParam.Info.ValueMin;
                    cs.maxRange = displayParam.Info.ValueMax;
                }
                cs.statType = (AnyColumnDef.StatType)cbxStatType.SelectedIndex;
                if (paraname == "Distance")
                {
                    cs.decPlace = 0;
                }
                else
                {
                    cs.decPlace = 2;
                }
            }
            else if (rbFromOther.Checked)
            {
                cs.keyType = (AnyColumnDef.KeyType)cbxOtherValue.SelectedIndex;
                cs.parameter = null;
                cs.valueRangeCheck = false;
                cs.minRange = 0;
                cs.maxRange = 0;
            }
            if (chkSplitRange.Checked)
            {
                cs.retByRanges.Clear();
                foreach (object obj in listRanges.Items)
                {
                    cs.retByRanges.Add(obj as ValueRange);
                }
            }
            else
            {
                cs.retByRanges.Clear();
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            saveCondSettings();
        }

        private void btnDeleteCond_Click(object sender, EventArgs e)
        {
            if (cbxAnyStatConds.SelectedItem != null)
            {
                cbxAnyStatConds.Items.Remove(cbxAnyStatConds.SelectedItem);
            }
            if (cbxAnyStatConds.Items.Count > 0)
            {
                cbxAnyStatConds.SelectedIndex = 0;
            }
        }

        private void btnNewCond_Click(object sender, EventArgs e)
        {
            TextInputBox input = new TextInputBox("新增指标组", "输入指标组名称", "新指标组");
            if (DialogResult.OK == input.ShowDialog(this))
            {
                string textInput = input.TextInput;
                AnyStatUnit asu = new AnyStatUnit();
                asu.name = textInput;
                cbxAnyStatConds.Items.Add(asu);
                cbxAnyStatConds.SelectedItem = asu;
            }
        }

        private void btnAddRange_Click(object sender, EventArgs e)
        {
            DlgAddRange dlgAdd = new DlgAddRange();
            if (dlgAdd.ShowDialog(this) == DialogResult.OK)
            {
                ValueRange vr = dlgAdd.getResult();
                listRanges.Items.Add(vr);
            }
        }

        private void btnDeleteRange_Click(object sender, EventArgs e)
        {
            if (listRanges.SelectedItem != null)
            {
                listRanges.Items.Remove(listRanges.SelectedItem);
            }
            if (listRanges.Items.Count > 0)
            {
                listRanges.SelectedIndex = 0;
            }
        }

        private void listBoxColumn_SelectedIndexChanged(object sender, EventArgs e)
        {
            curSelParam = "";
            curSelArg = -1;
            if (listBoxColumn.SelectedItem is AnyColumnDef)
            {
                AnyColumnDef cs = listBoxColumn.SelectedItem as AnyColumnDef;
                tbxColumnTitle.Text = cs.name;
                if (cs.parameter != null)
                {
                    rbFromParam.Checked = true;
                    curSelParam = cs.parameter.Info.Name;
                    curSelArg = cs.parameter.ArrayIndex;
                    DTDisplayParameterSystem sys = DTDisplayParameterManager.GetInstance().GetSystem(cs.parameter.Info.Name);
                    if (sys == null)
                    {
                        MessageBox.Show(string.Format("参数配置错误，{0}没有配置根节点属性。", cs.parameter.Info.Name));
                        return;
                    }
                    cbxParaSystem.SelectedItem = sys;
                    loadInfomation(cbxParaSystem, cbxParaInfomation, curSelParam);
                }
                else
                {
                    rbFromOther.Checked = true;
                }
                if (cs.retByRanges.Count > 0)
                {
                    chkSplitRange.Checked = true;
                    grpByRange.Visible = true;
                    listRanges.Items.Clear();
                    foreach (ValueRange vr in cs.retByRanges)
                    {
                        listRanges.Items.Add(vr);
                    }
                }
                else
                {
                    chkSplitRange.Checked = false;
                    grpByRange.Visible = false;
                }

                setSelectedIndex(cs);

                resetPropertyControls(true);
            }
            else
            {
                resetPropertyControls(false);
            }
        }

        private void setSelectedIndex(AnyColumnDef cs)
        {
            switch (cs.statType)
            {
                case AnyColumnDef.StatType.TYPE_VALUE:
                    cbxStatType.SelectedIndex = 0;
                    break;
                case AnyColumnDef.StatType.TYPE_MIN:
                    cbxStatType.SelectedIndex = 1;
                    break;
                case AnyColumnDef.StatType.TYPE_MAX:
                    cbxStatType.SelectedIndex = 2;
                    break;
                case AnyColumnDef.StatType.TYPE_MEAN:
                    cbxStatType.SelectedIndex = 3;
                    break;
                case AnyColumnDef.StatType.TYPE_SUM:
                    cbxStatType.SelectedIndex = 4;
                    break;
                case AnyColumnDef.StatType.TYPE_COUNT:
                    cbxStatType.SelectedIndex = 5;
                    break;
                default:
                    break;
            }
        }

        private void rbFromParam_CheckedChanged(object sender, EventArgs e)
        {
            resetRbRelated();
        }

        private void rbFromOther_CheckedChanged(object sender, EventArgs e)
        {
            resetRbRelated();
        }

        private void cbxParaSystem_SelectedIndexChanged(object sender, EventArgs e)
        {
            loadInfomation(cbxParaSystem, cbxParaInfomation, curSelParam);
            checkColorState();
        }

        private void cbxParaInfomation_SelectedIndexChanged(object sender, EventArgs e)
        {
            loadArgument(cbxParaSystem, cbxParaInfomation, cbxParaArg, curSelArg);
            checkColorState();
        }

        private void cbxAnyStatConds_SelectedIndexChanged(object sender, EventArgs e)
        {
            listBoxColumn.Items.Clear();
            AnyStatUnit statUnit = cbxAnyStatConds.SelectedItem as AnyStatUnit;
            btnInsertColumn.Enabled = statUnit != null;
            btnAddColumn.Enabled = statUnit != null;
            btnDeleteColumn.Enabled = statUnit != null;
            if (statUnit != null)
            {
                foreach (AnyColumnDef col in statUnit.columnsDef)
                {
                    listBoxColumn.Items.Add(col);
                }
                if (listBoxColumn.Items.Count > 0)
                {
                    listBoxColumn.SelectedIndex = 0;
                }
                numKeyCount.Value = statUnit.keyColumnCount;
            }
        }

        private void numKeyCount_ValueChanged(object sender, EventArgs e)
        {
            AnyStatUnit statUnit = cbxAnyStatConds.SelectedItem as AnyStatUnit;
            if (statUnit != null)
            {
                statUnit.keyColumnCount = (int)numKeyCount.Value;
            }
        }

        private void chkSplitRange_CheckedChanged(object sender, EventArgs e)
        {
            grpByRange.Visible = chkSplitRange.Checked;
        }

        private void InitEvent()
        {
            btnSave.Click += btnSave_Click;
            btnDeleteCond.Click += btnDeleteCond_Click;
            btnNewCond.Click += btnNewCond_Click;
            btnMoveUp.Click += btnMoveUp_Click;
            btnMoveDown.Click += btnMoveDown_Click;
            btnAddColumn.Click += btnAddColumn_Click;
            btnInsertColumn.Click += btnInsertColumn_Click;
            btnDeleteColumn.Click += btnDeleteColumn_Click;
            btnAddRange.Click += btnAddRange_Click;
            btnDeleteRange.Click += btnDeleteRange_Click;
            btnApply.Click += btnApply_Click;
            btnStartAnyStat.Click += btnStartAnyStat_Click;

            cbxAnyStatConds.SelectedIndexChanged += cbxAnyStatConds_SelectedIndexChanged;
            cbxParaSystem.SelectedIndexChanged += cbxParaSystem_SelectedIndexChanged;
            cbxParaInfomation.SelectedIndexChanged += cbxParaInfomation_SelectedIndexChanged;
            listBoxColumn.SelectedIndexChanged += listBoxColumn_SelectedIndexChanged;

            rbFromParam.CheckedChanged += rbFromParam_CheckedChanged;
            rbFromOther.CheckedChanged += rbFromOther_CheckedChanged;

            chkSplitRange.CheckedChanged += chkSplitRange_CheckedChanged;
            numKeyCount.ValueChanged += numKeyCount_ValueChanged;
        }

        private void intAllInfo()
        {
            loadSystem();
            initStatType();
            initKeyType();
            loadCondSettings();
        }

        private void saveCondSettings()
        {
            try
            {
                XmlConfigFile configFile = new XmlConfigFile();
                XmlElement cfg = configFile.AddConfig("AnyStatUnits");
                List<object> styles = new List<object>();
                foreach (AnyStatUnit rpt in cbxAnyStatConds.Items)
                {
                    styles.Add(rpt.Param);
                }
                configFile.AddItem(cfg, "units", styles);

                configFile.Save(string.Format(Application.StartupPath + "/config/anystat.xml"));
            }
            catch (Exception e)
            {
                MessageBox.Show("保存失败!" + e.Message);
            }
        }

        private void loadCondSettings()
        {
            try
            {
                XmlConfigFile configFile = new XmlConfigFile(string.Format(Application.StartupPath + "/config/anystat.xml"));
                List<Object> list = configFile.GetItemValue("AnyStatUnits", "units") as List<Object>;
                if (list != null)
                {
                    cbxAnyStatConds.Items.Clear();
                    foreach (object value in list)
                    {
                        AnyStatUnit unit = new AnyStatUnit();
                        unit.Param = value as Dictionary<string, object>;
                        cbxAnyStatConds.Items.Add(unit);
                    }
                    if (cbxAnyStatConds.Items.Count > 0)
                    {
                        cbxAnyStatConds.SelectedIndex = 0;
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        private void initKeyType()
        {
            cbxOtherValue.Items.Add("TIME_yyyy_mm_dd_hh");
            cbxOtherValue.Items.Add("TIME_yyyy_mm_dd");
            cbxOtherValue.Items.Add("TIME_yyyy_mm");
            cbxOtherValue.Items.Add("TIME_hh");
            cbxOtherValue.Items.Add("GSM_CELL_BANDTYPE");
            cbxOtherValue.Items.Add("GSM_N_CELL_BANDTYPE_0");
            cbxOtherValue.Items.Add("Reserved_Region_Name");
            cbxOtherValue.SelectedIndex = 0;
        }

        private void initStatType()
        {
            cbxStatType.Items.Add("简单取值");
            cbxStatType.Items.Add("最小值");
            cbxStatType.Items.Add("最大值");
            cbxStatType.Items.Add("平均值");
            cbxStatType.Items.Add("求和");
            cbxStatType.Items.Add("统计个数");
            cbxStatType.SelectedIndex = 3;
        }

        private void freshColumnToConfig()
        {
            AnyStatUnit asu = cbxAnyStatConds.SelectedItem as AnyStatUnit;
            if(asu!=null)
            {
                asu.keyColumnCount = (int)numKeyCount.Value;
                asu.columnsDef.Clear();
                for (int i = 0; i < listBoxColumn.Items.Count; i++)
                {
                    AnyColumnDef coldef = listBoxColumn.Items[i] as AnyColumnDef;
                    asu.columnsDef.Add(coldef);
                }
            }
        }

        private void swapColumn(int from, int to)
        {
            object fromObj = listBoxColumn.Items[from];
            listBoxColumn.Items.RemoveAt(from);
            listBoxColumn.Items.Insert(to, fromObj);
            listBoxColumn.SelectedIndex = to;
        }

        private void resetPropertyControls(bool enable)
        {
            tbxColumnTitle.Enabled = enable;
            rbFromParam.Enabled = enable;
            rbFromOther.Enabled = enable;
            cbxParaSystem.Enabled = enable;
            cbxParaInfomation.Enabled = enable;
            cbxOtherValue.Enabled = enable;
            btnApply.Enabled = enable;
            int sel = listBoxColumn.SelectedIndex;
            btnMoveUp.Enabled = enable && (sel - 1 >= 0);
            btnMoveDown.Enabled = enable && (sel + 1 < listBoxColumn.Items.Count);
            resetRbRelated();
        }

        private void resetRbRelated()
        {
            cbxParaSystem.Enabled = rbFromParam.Checked && rbFromParam.Enabled;
            cbxParaInfomation.Enabled = rbFromParam.Checked && rbFromParam.Enabled;
            cbxOtherValue.Enabled = rbFromOther.Checked && rbFromOther.Enabled;
        }

        // 当列被修改后，用于刷新显示
        private void freshListView()
        {
            List<AnyColumnDef> columns = new List<AnyColumnDef>();
            for (int i = 0; i < listBoxColumn.Items.Count; i++)
            {
                columns.Add(listBoxColumn.Items[i] as AnyColumnDef);
            }
            listBoxColumn.Items.Clear();
            for (int i = 0; i < columns.Count; i++)
            {
                listBoxColumn.Items.Add(columns[i]);
            }  
        }

        private void checkColorState()
        {
            cbxParaSystem.Enabled = rbFromParam.Checked && rbFromParam.Enabled && cbxParaSystem.Items.Count > 0;
            cbxParaInfomation.Enabled = rbFromParam.Checked && rbFromParam.Enabled && cbxParaInfomation.Items.Count > 0;
            cbxParaArg.Enabled = rbFromParam.Checked && rbFromParam.Enabled && cbxParaArg.Items.Count > 0;
        }

        private void loadArgument(ComboBox comboBoxSystem, ComboBox comboBoxInfomation, ComboBox comboBoxArgument, int arrayIndex)
        {
            comboBoxArgument.Items.Clear();
            DTDisplayParameterInfo displayInfo = DTDisplayParameterManager.GetInstance()[comboBoxSystem.SelectedItem.ToString()][comboBoxInfomation.SelectedItem.ToString()];
            if (displayInfo.ArrayBounds > 1)
            {
                for (int i = 0; i < displayInfo.ArrayBounds; i++)
                {
                    comboBoxArgument.Items.Add(i.ToString());
                }
            }
            if (comboBoxArgument.Items.Count > 0)
            {
                if (comboBoxArgument.Enabled)
                {
                    comboBoxArgument.SelectedIndex = arrayIndex;
                }
                else
                {
                    comboBoxArgument.SelectedIndex = 0;
                }
            }
        }

        private void loadInfomation(ComboBox comboBoxSystem, ComboBox comboBoxInfomation, string paramName)
        {
            comboBoxInfomation.Items.Clear();
            string systemName = comboBoxSystem.SelectedItem.ToString();
            if (systemName == null)
            {
                return;
            }
            DTDisplayParameterInfo selItem = null;
            foreach (DTDisplayParameterInfo displayInfo in DTDisplayParameterManager.GetInstance()[systemName].DisplayParamInfos)
            {
                if ((displayInfo.Type & (int)DTDisplayParameterInfoType.Range) != 0)
                {
                    if (displayInfo.ParamInfo.Name == paramName)
                    {
                        selItem = displayInfo;
                    }
                    comboBoxInfomation.Items.Add(displayInfo);
                }
            }
            if (selItem != null)
            {
                comboBoxInfomation.SelectedItem = selItem;
            }
            else
            {
                comboBoxInfomation.SelectedIndex = 0;
            }
        }

        private void loadSystem()
        {
            foreach (DTDisplayParameterSystem system in DTDisplayParameterManager.GetInstance().Systems)
            {
                cbxParaSystem.Items.Add(system);
            }
        }

        private string curSelParam = "";
        private int curSelArg = -1;
    }
}