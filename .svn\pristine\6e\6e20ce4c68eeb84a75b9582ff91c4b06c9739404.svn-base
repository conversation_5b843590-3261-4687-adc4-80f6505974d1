﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.Func.NebulaForm
{
    public partial class SettingAddItemForm : MinCloseForm
    {
        private float min, max;
        public SettingAddItemForm(float min, float max)
        {
            InitializeComponent();

            this.min = min;
            this.max = max;
            this.spinEditMin.Value = (decimal)this.min;
            this.spinEditMin.ValueChanged += spinEditMin_ValueChanged;
        }

        void spinEditMin_ValueChanged(object sender, EventArgs e)
        {
            DevExpress.XtraEditors.SpinEdit edit = sender as DevExpress.XtraEditors.SpinEdit;
            if (edit.Value > (decimal)this.max) edit.Value = (decimal)this.max;
            if (edit.Value < (decimal)this.min) edit.Value = (decimal)this.min;
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void buttonCancle_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }

        public ScaleItem GetResult()
        {
            return new ScaleItem((float)this.spinEditMin.Value); 
        }


    }
}
