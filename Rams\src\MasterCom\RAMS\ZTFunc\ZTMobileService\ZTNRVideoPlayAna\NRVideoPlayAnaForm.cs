﻿using DevExpress.XtraGrid.Views.Grid;
using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRVideoPlayAnaForm : MinCloseForm
    {
        public NRVideoPlayAnaForm() : base()
        {
            InitializeComponent();
        }

        public void FillData(List<NRVideoPlayAnaItem> rebufferList, List<NRVideoPlayAnaItem> loadList)
        {
            gcRebuffer.DataSource = rebufferList;
            gcLoad.DataSource = loadList;
            gcRebuffer.RefreshDataSource();
            gcLoad.RefreshDataSource();
            MainModel.ClearDTData();
            foreach (NRVideoPlayAnaItem item in loadList)
            {
                foreach (TestPoint tp in item.TpsList)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            MainModel.FireDTDataChanged(this);
        }

        private void gv_DoubleClick(object sender, EventArgs e)
        {
            GridView gv = (GridView)sender;
            NRVideoPlayAnaItem videoPlay = gv.GetFocusedRow() as NRVideoPlayAnaItem;
            if (videoPlay != null)
            {
                MainModel.ClearSelectedTestPoints();
                MainModel.DTDataManager.Clear();
                foreach (TestPoint tp in videoPlay.TpsList)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.FireDTDataChanged(MainModel.MainForm);
                OutlineOfRoad outRoad = new OutlineOfRoad();
                outRoad.SetPoints(videoPlay.TpsList);
                TempLayer.Instance.Draw(outRoad.Drawer);
            }
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            GridView gv = getCurGirdView(this.tabVideoPlay);
            ExcelNPOIManager.ExportToExcel(gv);
        }

        /// <summary>
        /// 根据选项卡确定当前GirdView
        /// </summary>
        /// <param name="tc"></param>
        /// <returns></returns>
        private GridView getCurGirdView(TabControl tc)
        {
            int index = tc.SelectedIndex;
            GridView gv;
            switch (index)
            {
                case 0: gv = gvLoad; break;
                case 1: gv = gvRebuffer; break;
                default: return null;
            }
            return gv;
        }

        private void miReplay_Click(object sender, EventArgs e)
        {
            GridView gv = getCurGirdView(this.tabVideoPlay);
            if (gv == null)
            {
                return;
            }
            NRVideoPlayAnaItem item = gv.GetRow(gv.FocusedRowHandle) as NRVideoPlayAnaItem;
            if (item == null)
            {
                return;
            }
            MainModel.MainForm.NeedChangeWorkSpace(false);
            PreNextMinutesPeriodForm preNextMinutePeriodForm = new PreNextMinutesPeriodForm();
            preNextMinutePeriodForm.Pre = 0;
            preNextMinutePeriodForm.Next = 0;
            if (preNextMinutePeriodForm.ShowDialog() == DialogResult.OK)
            {
                int pre = preNextMinutePeriodForm.Pre;
                int next = preNextMinutePeriodForm.Next;
                DateTime timeStart = item.BeginTime.AddMinutes(-pre);
                DateTime timeEnd = item.EndTime.AddMinutes(next);
                FileReplayer.Replay(item.TpsList[0].FileInfo, new MasterCom.Util.TimePeriod(timeStart, timeEnd));
            }
            else
            {
                MainModel.MainForm.CancelChange = true;
            }
            MainModel.MainForm.ChangeWorkSpace();
        }

        private void tabVideoPlay_TabSelectedIndexChanged(object sender, EventArgs e)
        {
            GridView gv = getCurGirdView(this.tabVideoPlay);
            if (gv == null)
            {
                return;
            }
            List<NRVideoPlayAnaItem> list = gv.DataSource as List<NRVideoPlayAnaItem>;

            if (list != null)
            {
                MainModel.ClearDTData();
                foreach (NRVideoPlayAnaItem item in list)
                {
                    foreach (TestPoint tp in item.TpsList)
                    {
                        MainModel.DTDataManager.Add(tp);
                    }
                }
                MainModel.FireDTDataChanged(this);
            }

        }

        private void miExportOtherTp2Csv_Click(object sender, EventArgs e)
        {   //导出非卡顿（缓冲）区间采样点到Csv
            //GridView gv = getCurGirdView(this.tabVideoPlay);
            //if (gv == null)
            //{
            //    return;
            //}
            //List<NRVideoPlayAnaItem> list = gv.DataSource as List<NRVideoPlayAnaItem>;
            //ExportAnaItems2Csv.ExportVideoAnaResult2Csv(list, true);
        }

        private void miExportTp2Csv_Click(object sender, EventArgs e)
        {   //导出卡顿（缓冲）区间采样点信息到Csv
            //GridView gv = getCurGirdView(this.tabVideoPlay);
            //if (gv == null)
            //{
            //    return;
            //}
            //List<NRVideoPlayAnaItem> list = gv.DataSource as List<NRVideoPlayAnaItem>;
            //ExportAnaItems2Csv.ExportVideoAnaResult2Csv(list, false);
        }

        private void contextMenuStrip_Opening(object sender, CancelEventArgs e)
        {
            int index = tabVideoPlay.SelectedIndex;
            string s;
            switch (index)
            {
                case 0: s = "缓冲"; break;
                case 1: s = "卡顿"; break;
                default: return;
            }
            miExportTp2Csv.Text = string.Format("导出{0}采样点到Csv...", s);
            miExportOtherTp2Csv.Text = string.Format("导出非{0}采样点到Csv...", s);
        }
    }
}
