﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRDownloadSpeedAnaDlg : BaseDialog
    {
        public NRDownloadSpeedAnaDlg()
        {
            InitializeComponent();
        }

        public void SetCondition(NRDownloadSpeedAnaCondition cond)
        {
            if (cond == null)
            {
                cond = new NRDownloadSpeedAnaCondition();
            }
            transferedTimeLimit.Value = (decimal)cond.TransferedTimeLimit;
        }

        public NRDownloadSpeedAnaCondition GetCondition()
        {
            NRDownloadSpeedAnaCondition cond = new NRDownloadSpeedAnaCondition();
            cond.TransferedTimeLimit = (int)transferedTimeLimit.Value;
            return cond;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }

    public class NRDownloadSpeedAnaCondition
    {
        public int TransferedTimeLimit { get; set; } = 50;
    }
}
