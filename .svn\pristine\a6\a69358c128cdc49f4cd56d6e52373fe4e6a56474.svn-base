﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using System.Drawing;

namespace MasterCom.RAMS.Func.Voronoi
{
    /// <summary>
    /// Delaunay三角剖分
    /// </summary>
    public class CTriangle
    {
        /// <summary>
        /// 三角剖分类型
        /// </summary>
        public enum DelaunayType
        {
            Constrained,
            Conforming,
            ConstrainedConforming,
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dType"></param>
        /// <param name="inList">输入点不能有重复</param>
        /// <returns>返回列表每三个点组成一个三角形</returns>
        public List<Vertex> Triangulate(DelaunayType dType, List<Vertex> inList)
        {
            if (inList == null || inList.Count < 3)
            {
                throw new System.ArgumentException("参数错误：端点数小于3个，不能构建泰森多边形！");
            }
            List<Vertex> outList = new List<Vertex>();
            string command = "";
            switch (dType)
            {
                case DelaunayType.Constrained:
                    command = "BezQ";
                    break;
                case DelaunayType.Conforming:
                    command = "BDezQ";
                    break;
                case DelaunayType.ConstrainedConforming:
                    command = "BqezQ";
                    break;
                default:
                    return outList;
            }

            CTrianglateIO input = new CTrianglateIO();
            CTrianglateIO output = new CTrianglateIO();
            CTrianglateIO vout = new CTrianglateIO();
            InitIOStruct(ref input);
            InitIOStruct(ref output);
            InitIOStruct(ref vout);

            SetDelaunayInput(ref input, inList);
            CTriangle.Triangulate(command, ref input, ref output, ref vout);
            GetDelaunayOutput(ref output, outList);
            FreeDelaunayIO(ref input, ref output, ref vout);

            return outList;
        }

        /// <summary>
        /// 返回inList所组成的delaunay三角
        /// </summary>
        /// <param name="inList"></param>
        /// <returns>每3个点，为一个delaunay三角</returns>
        public List<Vertex> Triangulate(List<Vertex> inList)
        {
            DelaunayType dType = DelaunayType.Constrained;
            return Triangulate(dType, inList);
        }

        private void SetDelaunayInput(ref CTrianglateIO input, List<Vertex> inList)
        {
            List<double> tmpVerticesList = new List<double>();

            foreach (Vertex vertex in inList)
            {
                tmpVerticesList.Add(vertex.X);
                tmpVerticesList.Add(vertex.Y);
            }
            input.PointCount = inList.Count;
            input.PointList = Marshal.AllocHGlobal(tmpVerticesList.Count * sizeof(double));
            Marshal.Copy(tmpVerticesList.ToArray(), 0, input.PointList, tmpVerticesList.Count);
        }

        private void GetDelaunayOutput(ref CTrianglateIO output, List<Vertex> outList)
        {
            double[] points = new double[output.PointCount * 2];
            int[] triangles = new int[output.TriangleCount * output.CornerCount];

            Marshal.Copy(output.PointList, points, 0, points.Length);
            Marshal.Copy(output.TriangleList, triangles, 0, triangles.Length);

            for (int i = 0; i < triangles.Length; ++i)
            {
                int ptIndex = triangles[i];
                outList.Add(new Vertex(points[ptIndex * 2], points[ptIndex * 2 + 1]));
            }
        }

        private void FreeDelaunayIO(ref CTrianglateIO input, ref CTrianglateIO output,
            ref CTrianglateIO vout)
        {
            Marshal.FreeHGlobal(input.PointList);
            FreeTri(ref output);
            FreeTri(ref vout);
        }

        private void InitIOStruct(ref CTrianglateIO io)
        {
            io.PointList = new IntPtr(0);
            io.PointAttrList = new IntPtr(0);
            io.PointMarkerList = new IntPtr(0);
            io.PointCount = 0;
            io.PointAttrCount = 0;

            io.TriangleList = new IntPtr(0);
            io.TriangleAttrList = new IntPtr(0);
            io.TriangleAreaList = new IntPtr(0);
            io.NeighborList = new IntPtr(0);
            io.TriangleCount = 0;
            io.CornerCount = 0;
            io.TriangleAttrCount = 0;

            io.SegmentList = new IntPtr(0);
            io.SegmentMarkerList = new IntPtr(0);
            io.SegmentCount = 0;

            io.HoleList = new IntPtr(0);
            io.HoleCount = 0;

            io.RegionList = new IntPtr(0);
            io.RegionCount = 0;

            io.EdgeList = new IntPtr(0);
            io.EdgeMarkerList = new IntPtr(0);
            io.NormList = new IntPtr(0);
            io.EdgeCount = 0;
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct CTrianglateIO
        {
            [MarshalAs(UnmanagedType.SysInt)]
            public IntPtr PointList;
            [MarshalAs(UnmanagedType.SysInt)]
            public IntPtr PointAttrList;
            [MarshalAs(UnmanagedType.SysInt)]
            public IntPtr PointMarkerList;
            [MarshalAs(UnmanagedType.I4)]
            public int PointCount;
            [MarshalAs(UnmanagedType.I4)]
            public int PointAttrCount;
            [MarshalAs(UnmanagedType.SysInt)]
            public IntPtr TriangleList;
            [MarshalAs(UnmanagedType.SysInt)]
            public IntPtr TriangleAttrList;
            [MarshalAs(UnmanagedType.SysInt)]
            public IntPtr TriangleAreaList;
            [MarshalAs(UnmanagedType.SysInt)]
            public IntPtr NeighborList;
            [MarshalAs(UnmanagedType.I4)]
            public int TriangleCount;
            [MarshalAs(UnmanagedType.I4)]
            public int CornerCount;
            [MarshalAs(UnmanagedType.I4)]
            public int TriangleAttrCount;
            [MarshalAs(UnmanagedType.SysInt)]
            public IntPtr SegmentList;
            [MarshalAs(UnmanagedType.SysInt)]
            public IntPtr SegmentMarkerList;
            [MarshalAs(UnmanagedType.I4)]
            public int SegmentCount;
            [MarshalAs(UnmanagedType.SysInt)]
            public IntPtr HoleList;
            [MarshalAs(UnmanagedType.I4)]
            public int HoleCount;
            [MarshalAs(UnmanagedType.SysInt)]
            public IntPtr RegionList;
            [MarshalAs(UnmanagedType.I4)]
            public int RegionCount;
            [MarshalAs(UnmanagedType.SysInt)]
            public IntPtr EdgeList;
            [MarshalAs(UnmanagedType.SysInt)]
            public IntPtr EdgeMarkerList;
            [MarshalAs(UnmanagedType.SysInt)]
            public IntPtr NormList;
            [MarshalAs(UnmanagedType.I4)]
            public int EdgeCount;
        }

        private static void Triangulate(string command,
            ref CTrianglateIO input,
            ref CTrianglateIO output,
            ref CTrianglateIO vout)
        {
            if (IntPtr.Size == 4)
            {
                Triangulate32(command, ref input, ref output, ref vout);
            }
            else
            {
                Triangulate64(command, ref input, ref output, ref vout);
            }
        }

        private static void FreeTri(ref CTrianglateIO io)
        {
            if (IntPtr.Size == 4)
            {
                FreeTri32(ref io);
            }
            else
            {
                FreeTri64(ref io);
            }
        }

        private const string dllPath32 = @"CGeometry32.dll";
        [DllImport(dllPath32, EntryPoint = "Triangulate", CharSet = CharSet.Ansi)]
        private extern static void Triangulate32(
            string command,
            ref CTrianglateIO input,
            ref CTrianglateIO output,
            ref CTrianglateIO vout);
        [DllImport(dllPath32, EntryPoint = "FreeTri", CharSet = CharSet.Ansi)]
        private extern static void FreeTri32(ref CTrianglateIO io);

        private const string dllPath64 = @"CGeometry64.dll";
        [DllImport(dllPath64, EntryPoint = "Triangulate", CharSet = CharSet.Ansi)]
        private extern static void Triangulate64(
            string command,
            ref CTrianglateIO input,
            ref CTrianglateIO output,
            ref CTrianglateIO vout);
        [DllImport(dllPath64, EntryPoint = "FreeTri", CharSet = CharSet.Ansi)]
        private extern static void FreeTri64(ref CTrianglateIO io);
    }

    /// <summary>
    /// 多边形切割
    /// </summary>
    public class CPolygonClipper
    {
        /// <summary>
        /// 切割类型
        /// </summary>
        public enum ClipOperation
        {
            Difference,
            Intersection,
            Exclusive,
            Union,
        }

        public List<Vertex[]> ClipPolygon(List<Vertex> subject, List<Vertex> clipper)
        {
            ClipOperation op = ClipOperation.Intersection;
            return ClipPolygon(op, subject, clipper);
        }

        public List<Vertex[]> ClipPolygon(List<Vertex[]> subject, List<Vertex[]> clipper)
        {
            ClipOperation op = ClipOperation.Intersection;
            return ClipPolygon(op, subject, clipper);
        }

        public List<Vertex[]> ClipPolygon(List<Vertex[]> subject)
        {
            ClipOperation op = ClipOperation.Intersection;
            return ClipPolygon(op, subject);
        }

        public List<Vertex[]> ClipPolygon(ClipOperation op, List<Vertex> subject,
            List<Vertex> clipper)
        {
            List<Vertex[]> sub = new List<Vertex[]>();
            sub.Add(subject.ToArray());
            List<Vertex[]> clip = new List<Vertex[]>();
            clip.Add(clipper.ToArray());
            return ClipPolygon(op, sub, clip);
        }

        public List<Vertex[]> ClipPolygon(ClipOperation op, List<Vertex[]> subject, List<Vertex[]> clipper)
        {
            CPolygonIO poly = new CPolygonIO();
            CPolygonIO clip = new CPolygonIO();
            CPolygonIO result = new CPolygonIO();
            InitIOStruct(subject, ref poly);
            InitIOStruct(clipper, ref clip);

            ClipPolygon(op, ref poly, ref clip, ref result);
            List<Vertex[]> retList = GetResultPolygon(ref result);

            FreePolyIO(ref poly, ref clip, ref result);
            return retList;
        }

        public List<Vertex[]> ClipPolygon(ClipOperation op, List<Vertex[]> subject)
        {
            if (!IsCache)
            {
                return new List<Vertex[]>();
            }

            CPolygonIO poly = new CPolygonIO();
            CPolygonIO result = new CPolygonIO();
            InitIOStruct(subject, ref poly);

            ClipPolygon(op, ref poly, ref clipperIO, ref result);
            List<Vertex[]> retList = GetResultPolygon(ref result);

            FreePolyIO(ref poly, ref result);
            return retList;
        }

        private CPolygonIO clipperIO;
        private bool IsCache = false;
        public void DoCacheForClipper(List<Vertex[]> clipper)
        {
            clipperIO = new CPolygonIO();
            InitIOStruct(clipper, ref clipperIO);
            IsCache = true;
        }

        public void ClearCache()
        {
            if (IsCache)
            {
                FreePolyIO(ref clipperIO);
                IsCache = false;
            }
        }

        private void FreePolyIO(ref CPolygonIO poly, ref CPolygonIO clip, ref CPolygonIO result)
        {
            FreePolyIO(ref poly);
            FreePolyIO(ref clip);
            FreeResult(ref result);
        }

        private void FreePolyIO(ref CPolygonIO poly, ref CPolygonIO result)
        {
            FreePolyIO(ref poly);
            FreeResult(ref result);
        }

        private void FreePolyIO(ref CPolygonIO poly)
        {
            Marshal.FreeHGlobal(poly.PointCount);
            Marshal.FreeHGlobal(poly.XList);
            Marshal.FreeHGlobal(poly.YList);
        }

        private List<Vertex[]> GetResultPolygon(ref CPolygonIO result)
        {
            List<Vertex[]> retList = new List<Vertex[]>();
            int[] tmpPolygonCount = new int[result.PolygonCount];

            int totalPoints = 0;
            Marshal.Copy(result.PointCount, tmpPolygonCount, 0, result.PolygonCount);
            for (int i = 0; i < result.PolygonCount; ++i)
            {
                totalPoints += tmpPolygonCount[i];
            }

            double[] xList = new double[totalPoints];
            double[] yList = new double[totalPoints];
            Marshal.Copy(result.XList, xList, 0, totalPoints);
            Marshal.Copy(result.YList, yList, 0, totalPoints);

            int loop = 0;
            for (int i = 0; i < result.PolygonCount; ++i)
            {
                Vertex[] points = new Vertex[tmpPolygonCount[i]];
                for (int j = 0; j < tmpPolygonCount[i]; ++j)
                {
                    points[j] = new Vertex(xList[loop], yList[loop]);
                    ++loop;
                }
                retList.Add(points);
            }

            return retList;
        }

        private void InitIOStruct(List<Vertex[]> vertexs, ref CPolygonIO polygon)
        {
            polygon.PolygonCount = vertexs.Count;
            polygon.PointCount = Marshal.AllocHGlobal(sizeof(int) * polygon.PolygonCount);
            int[] tmpPointCount = new int[polygon.PolygonCount];

            int total = 0;
            for (int i = 0; i < polygon.PolygonCount; ++i)
            {
                tmpPointCount[i] = vertexs[i].Length;
                total += vertexs[i].Length;
            }
            Marshal.Copy(tmpPointCount, 0, polygon.PointCount, polygon.PolygonCount);

            double[] xList = new double[total];
            double[] yList = new double[total];
            int idx = 0;
            foreach (Vertex[] vs in vertexs)
            {
                foreach (Vertex v in vs)
                {
                    xList[idx] = v.X;
                    yList[idx] = v.Y;
                    ++idx;
                }
            }

            polygon.XList = Marshal.AllocHGlobal(sizeof(double) * total);
            polygon.YList = Marshal.AllocHGlobal(sizeof(double) * total);
            Marshal.Copy(xList, 0, polygon.XList, total);
            Marshal.Copy(yList, 0, polygon.YList, total);
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct CPolygonIO
        {
            [MarshalAs(UnmanagedType.I4)]
            public int PolygonCount;
            [MarshalAs(UnmanagedType.SysInt)]
            public IntPtr PointCount;
            [MarshalAs(UnmanagedType.SysInt)]
            public IntPtr XList;
            [MarshalAs(UnmanagedType.SysInt)]
            public IntPtr YList;
        }

        private static void ClipPolygon(ClipOperation op, ref CPolygonIO subject,
            ref CPolygonIO clip,
            ref CPolygonIO result)
        {
            if (IntPtr.Size == 4)
            {
                ClipPolygon32(op, ref subject, ref clip, ref result);
            }
            else
            {
                ClipPolygon64(op, ref subject, ref clip, ref result);
            }
        }

        private static void FreeResult(ref CPolygonIO io)
        {
            if (IntPtr.Size == 4)
            {
                FreePoly32(ref io);
            }
            else
            {
                FreePoly64(ref io);
            }
        }

        private const string dllPath32 = @"CGeometry32.dll";
        [DllImport(dllPath32, EntryPoint = "ClipPolygon", CharSet = CharSet.Ansi)]
        private extern static void ClipPolygon32(ClipOperation op, ref CPolygonIO subject,
            ref CPolygonIO clip,
            ref CPolygonIO result);
        [DllImport(dllPath32, EntryPoint = "FreePoly", CharSet = CharSet.Ansi)]
        private extern static void FreePoly32(ref CPolygonIO io);

        private const string dllPath64 = @"CGeometry64.dll";
        [DllImport(dllPath64, EntryPoint = "ClipPolygon", CharSet = CharSet.Ansi)]
        private extern static void ClipPolygon64(ClipOperation op, ref CPolygonIO subject,
            ref CPolygonIO clip,
            ref CPolygonIO result);
        [DllImport(dllPath64, EntryPoint = "FreePoly", CharSet = CharSet.Ansi)]
        private extern static void FreePoly64(ref CPolygonIO io);
    }
}
