﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.Util;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Util;
using MasterCom.MTGis;
using MasterCom.RAMS.Model.Interface;
using MasterCom.MControls;
using MasterCom.Util.UiEx;

using DevExpress.XtraTab;
using DevExpress.XtraCharts;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.Func.ScanGridAnaExporter
{
    public partial class ScanGridAnaWordExportForm : Form
    {
        public string SavePath
        {
            get;
            set;
        }

        public MainModel MainModel
        {
            get;
            set;
        }

        public ScanGridAnaResult AnaResult
        {
            get;
            set;
        }

        public ScanGridAnaResult CmpResult
        {
            get;
            set;
        }

        public ScanGridAnaWordExportForm()
        {
            InitializeComponent();
        }

        public void Export()
        {
            try
            {
                stater = new ScanGridAnaStater(AnaResult, CmpResult);
                stater.Stat();
                giser = new ScanGridAnaGisColoring(MainModel, AnaResult, CmpResult);
                ranger = ScanGridAnaColorRanger.Instance;
                word = new ScanGridAnaWord(false);
                ExportFlow();
                word.ApplyGlobalStyle();
                word.SavedAsWord(SavePath);
                MessageBox.Show("导出成功!", "提示");
            }
            catch (Exception ex)
            {
                MessageBox.Show("导出失败: " + ex.Message, "错误");
            }
            finally
            {
                WaitTextBox.Close();
            }
        }

        private void ExportFlow()
        {
            WaitTextBox.Text = "正在导出Word...概述部分";
            if (ScanGridAnaSettingCondition.Instance.ServiceType == 1212 ||
                ScanGridAnaSettingCondition.Instance.ServiceType == 1919) //路测
            {
                word.InsertText("路测栅格分析报告", "标题");
            }
            else
            {
                word.InsertText("扫频栅格分析报告", "标题");
            }
            word.NewLine();

            word.InsertText("1 概述", "标题 1");
            word.NewLine();

            word.InsertText("1.1 测试数据说明", "标题 2");
            word.NewLine();
            TestDataExper testData = new TestDataExper();
            testData.Export(word);
            word.NewLine();

            word.InsertText("1.2 分析条件说明", "标题 2");
            word.NewLine();
            word.InsertText("相对覆盖度：道路上弱于最强信号设定值范围内的小区数（含最强信号小区）。", "正文");
            word.InsertBreak();

            WaitTextBox.Text = "正在导出Word...测试深度";
            ExportCompareStat(2);

            WaitTextBox.Text = "正在导出Word...覆盖强度";
            ExportRxlevGis(3);
            ExportRxlevData(3);
            ExportRxlevWeak(3);

            WaitTextBox.Text = "正在导出Word...重叠覆盖度";
            ExportCoverage(4);
        }

        /**
        private void ExportCurrentTimePeriod(ScanGridAnaGridType netType, int index)
        {
            word.InsertText(string.Format("{0}.1 {1}", index, "当前时间段"), "标题 2");
            word.NewLine();

            MapForm mf = MainModel.MainForm.GetMapForm();
            ranger.CurRangeType = ScanGridAnaRangeType.Coverage;

            ranger.CurCoverageType = 0;
            giser.Refresh(netType);
            List<DataTable> dts = stater.GetResult(ScanGridAnaStatType.Coverage, netType, ranger.CurCoverageType);
            word.InsertText(string.Format("{0}.1.1 相对覆盖度", index), "标题 3");
            word.NewLine();
            CountRateGisExper countRateGisExper = new CountRateGisExper();
            countRateGisExper.Export(word, this, new List<DataTable>() { dts[0], dts[1] }, new List<DataTable>() { dts[2], dts[3] }, mf);
            word.InsertBreak();

            
            ranger.CurCoverageType = 1;
            giser.Refresh(netType);
            dts = stater.GetResult(ScanGridAnaStatType.Coverage, netType, ranger.CurCoverageType);
            word.InsertText(string.Format("{0}.1.2 绝对覆盖度", index), "标题 3");
            word.NewLine();
            countRateGisExper = new CountRateGisExper();
            countRateGisExper.Export(word, this, new List<DataTable>() { dts[0], dts[1] }, new List<DataTable>() { dts[2], dts[3] }, mf);
            word.InsertBreak();

            ranger.CurCoverageType = 2;
            giser.Refresh(netType);
            dts = stater.GetResult(ScanGridAnaStatType.Coverage, netType, ranger.CurCoverageType);
            word.InsertText(string.Format("{0}.1.3 综合覆盖度", index), "标题 3");
            word.NewLine();
            countRateGisExper = new CountRateGisExper();
            countRateGisExper.Export(word, this, new List<DataTable>() { dts[0], dts[1] }, new List<DataTable>() { dts[2], dts[3] }, mf);
            word.InsertBreak();
             

            ranger.CurRangeType = ScanGridAnaRangeType.Rxlev;
            ranger.CurCoverageType = 0; // not used
            giser.Refresh(netType);
            dts = stater.GetResult(ScanGridAnaStatType.Rxlev, netType, ranger.CurCoverageType);
            word.InsertText(string.Format("{0}.1.2 场强统计", index), "标题 3");
            word.NewLine();
            countRateGisExper = new CountRateGisExper();
            countRateGisExper.Export(word, this, new List<DataTable>() { dts[0], dts[1] }, new List<DataTable>() { dts[2], dts[3] }, mf);
            word.InsertBreak();

            ranger.CurRangeType = ScanGridAnaRangeType.WeakRxlev;
            ranger.CurCoverageType = 0; // not used
            giser.Refresh(netType);
            word.InsertText(string.Format("{0}.1.3 弱覆盖区域", index), "标题 3");
            word.NewLine();
            ConsecutiveRegionExper regionExper = new ConsecutiveRegionExper();
            regionExper.Export(word, mf);
            word.NewLine();

            ranger.CurRangeType = ScanGridAnaRangeType.HighCoverage;
            ranger.CurCoverageType = 0; // not used
            giser.Refresh(netType);
            word.InsertText(string.Format("{0}.1.4 高重叠覆盖度区域", index), "标题 3");
            word.NewLine();
            regionExper.Export(word, mf);
        }
    

        private void ExportCompareTimePeriod(ScanGridAnaGridType netType, int index)
        {
            word.InsertText(string.Format("{0}.2 {1}", index, "对比时间段"), "标题 2");
            word.NewLine();

            MapForm mf = null;
            ranger.CurRangeType = ScanGridAnaRangeType.Coverage;

            ranger.CurCoverageType = 0;
            //giser.Refresh(netType);
            List<DataTable> dts = stater.GetResult(ScanGridAnaStatType.Coverage, netType, ranger.CurCoverageType);
            word.InsertText(string.Format("{0}.2.1 相对覆盖度", index), "标题 3");
            word.NewLine();
            CountRateGisExper countRateGisExper = new CountRateGisExper();
            countRateGisExper.Export(word, this, new List<DataTable>() { dts[0], dts[1] }, new List<DataTable>() { dts[2], dts[3] }, mf);
            word.InsertBreak();

            
            ranger.CurCoverageType = 1;
            //giser.Refresh(netType);
            dts = stater.GetResult(ScanGridAnaStatType.Coverage, netType, ranger.CurCoverageType);
            word.InsertText(string.Format("{0}.2.2 绝对覆盖度", index), "标题 3");
            word.NewLine();
            countRateGisExper = new CountRateGisExper();
            countRateGisExper.Export(word, this, new List<DataTable>() { dts[0], dts[1] }, new List<DataTable>() { dts[2], dts[3] }, mf);
            word.InsertBreak();

            ranger.CurCoverageType = 2;
            //giser.Refresh(netType);
            dts = stater.GetResult(ScanGridAnaStatType.Coverage, netType, ranger.CurCoverageType);
            word.InsertText(string.Format("{0}.2.3 综合覆盖度", index), "标题 3");
            word.NewLine();
            countRateGisExper = new CountRateGisExper();
            countRateGisExper.Export(word, this, new List<DataTable>() { dts[0], dts[1] }, new List<DataTable>() { dts[2], dts[3] }, mf);
            word.InsertBreak();
             

            ranger.CurRangeType = ScanGridAnaRangeType.Rxlev;
            ranger.CurCoverageType = 0; // not used
            //giser.Refresh(netType);
            dts = stater.GetResult(ScanGridAnaStatType.Rxlev, netType, ranger.CurCoverageType);
            word.InsertText(string.Format("{0}.2.2 场强统计", index), "标题 3");
            word.NewLine();
            countRateGisExper = new CountRateGisExper();
            countRateGisExper.Export(word, this, new List<DataTable>() { dts[0], dts[1] }, new List<DataTable>() { dts[2], dts[3] }, mf);
        }
    */

        private void ExportCompareStat(int index)
        {
            word.InsertText(string.Format("{0}. 测试栅格深度统计", index), "标题 1");
            word.NewLine();

            if (CmpResult == null)
            {
                word.InsertText("基准库时间段未设置。", "正文");
                word.NewLine();
                return;
            }

            MapForm mf = MainModel.MainForm.GetMapForm();
            ranger.CurRangeType = ScanGridAnaRangeType.Compare;
            ranger.CurCoverageType = 0; // not used

            if (ScanGridAnaSettingCondition.Instance.ServiceType == 12 ||
                ScanGridAnaSettingCondition.Instance.ServiceType == 1212) // gsm
            {
                giser.Refresh(ScanGridAnaGridType.最强信号);
                List<DataTable> dts = stater.GetResult(ScanGridAnaStatType.Compare, ScanGridAnaGridType.最强信号, ranger.CurCoverageType);
                word.InsertText("整体渲染效果如下：", "正文");
                string picFile = ExperMethod.GisScreenshot(mf);
                word.InsertPicture(picFile);
                word.NewLine();
                word.InsertText("分网格统计数据如下：", "正文");
                word.NewLine();
                GridView gv = ExperMethod.FillCountGridToForm(this, dts[0]);
                gv.Columns[gv.Columns.Count - 1].DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
                gv.Columns[gv.Columns.Count - 1].DisplayFormat.FormatString = "p";
                ExperMethod.GridViewToWord(word, gv, null);
                word.NewLine();

                /*
                word.InsertText(string.Format("{0}.2. GSM900", index), "标题 2");
                word.NewLine();
                giser.Refresh(ScanGridAnaGridType.GSM900);
                dts = stater.GetResult(ScanGridAnaStatType.Compare, ScanGridAnaGridType.GSM900, ranger.CurCoverageType);
                word.InsertText("整体渲染效果如下：", "正文");
                picFile = ExperMethod.GisScreenshot(mf);
                word.InsertPicture(picFile);
                word.NewLine();
                word.InsertText("分网格统计数据如下：", "正文");
                word.NewLine();
                gv = ExperMethod.FillCountGridToForm(this, dts[0]);
                gv.Columns[gv.Columns.Count - 1].DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
                gv.Columns[gv.Columns.Count - 1].DisplayFormat.FormatString = "p";
                ExperMethod.GridViewToWord(word, gv, null);
                word.NewLine();

                word.InsertText(string.Format("{0}.3. DCS1800", index), "标题 2");
                word.NewLine();
                giser.Refresh(ScanGridAnaGridType.GSM900);
                dts = stater.GetResult(ScanGridAnaStatType.Compare, ScanGridAnaGridType.DCS1800, ranger.CurCoverageType);
                word.InsertText("整体渲染效果如下：", "正文");
                picFile = ExperMethod.GisScreenshot(mf);
                word.InsertPicture(picFile);
                word.NewLine();
                word.InsertText("分网格统计数据如下：", "正文");
                word.NewLine();
                gv = ExperMethod.FillCountGridToForm(this, dts[0]);
                gv.Columns[gv.Columns.Count - 1].DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
                gv.Columns[gv.Columns.Count - 1].DisplayFormat.FormatString = "p";
                ExperMethod.GridViewToWord(word, gv, null);
                word.NewLine();
                 */
            }
            else
            {
                giser.Refresh(ScanGridAnaGridType.TD);
                List<DataTable> dts = stater.GetResult(ScanGridAnaStatType.Compare, ScanGridAnaGridType.TD, ranger.CurCoverageType);
                word.InsertText("整体渲染效果如下：", "正文");
                string picFile = ExperMethod.GisScreenshot(mf);
                word.InsertPicture(picFile);
                word.NewLine();
                word.InsertText("分网格统计数据如下：", "正文");
                word.NewLine();
                GridView gv = ExperMethod.FillCountGridToForm(this, dts[0]);
                gv.Columns[gv.Columns.Count - 1].DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
                gv.Columns[gv.Columns.Count - 1].DisplayFormat.FormatString = "p";
                ExperMethod.GridViewToWord(word, gv, null);
                word.NewLine();
            }
        }

        private void ExportRxlevGis(int index)
        {
            word.InsertText(string.Format("{0}. 覆盖强度统计", index), "标题 1");
            word.NewLine();
            word.InsertText(string.Format("{0}.1. 整体覆盖呈现", index), "标题 2");
            word.NewLine();

            ranger.CurRangeType = ScanGridAnaRangeType.Rxlev;
            ranger.CurCoverageType = 0; // not used
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (ScanGridAnaSettingCondition.Instance.ServiceType == 12 ||
                ScanGridAnaSettingCondition.Instance.ServiceType == 1212) // gsm
            {
                word.InsertText("按网络最强信号进行栅格渲染，效果如下：", "正文");
                giser.Refresh(ScanGridAnaGridType.最强信号);
                string picFile = ExperMethod.GisScreenshot(mf);
                word.InsertPicture(picFile);
                word.NewLine();

                word.InsertText("按GSM900进行栅格渲染，效果如下：", "正文");
                giser.Refresh(ScanGridAnaGridType.GSM900);
                picFile = ExperMethod.GisScreenshot(mf);
                word.InsertPicture(picFile);
                word.NewLine();

                word.InsertText("按DCS1800进行栅格渲染，效果如下：", "正文");
                giser.Refresh(ScanGridAnaGridType.DCS1800);
                picFile = ExperMethod.GisScreenshot(mf);
                word.InsertPicture(picFile);
                word.NewLine();
            }
            else
            {
                word.InsertText("栅格渲染效果如下：", "正文");
                giser.Refresh(ScanGridAnaGridType.TD);
                string picFile = ExperMethod.GisScreenshot(mf);
                word.InsertPicture(picFile);
                word.NewLine();
            }
        }

        private void ExportRxlevData(int index)
        {
            word.InsertText(string.Format("{0}.2. 网格级分布情况及占比", index), "标题 2");
            word.NewLine();

            ranger.CurRangeType = ScanGridAnaRangeType.Rxlev;
            ranger.CurCoverageType = 0; // not used
            List<ColorRange> curColorRanges = ScanGridAnaColorRanger.Instance.GetColorRanges();
            ScanGridAnaRangeType rangeType = ScanGridAnaColorRanger.Instance.CurRangeType;

            if (ScanGridAnaSettingCondition.Instance.ServiceType == 12 ||
                ScanGridAnaSettingCondition.Instance.ServiceType == 1212) // gsm
            {
                giser.Refresh(ScanGridAnaGridType.最强信号);
                List<DataTable> dts = stater.GetResult(ScanGridAnaStatType.Rxlev, ScanGridAnaGridType.最强信号, ranger.CurCoverageType);

                word.InsertText("最强信号按网格分段统计如下：", "正文");
                word.NewLine();
                GridView gv = ExperMethod.FillCountGridToForm(this, dts[0]);
                ExperMethod.GridViewToWord(word, gv, curColorRanges);
                gv.Dispose();
                word.NewLine();

                word.InsertText("最强信号网格占比如下：", "正文");
                word.NewLine();
                ChartControl cc = ExperMethod.FillCountChartToForm(this, dts[1], rangeType);
                string picFile = ExperMethod.ChartScreenshot(cc, dts[1].Rows.Count);
                cc.Dispose();
                word.InsertPicture(picFile);
                word.NewLine();
            }
            else if (ScanGridAnaSettingCondition.Instance.ServiceType == 19 ||
                ScanGridAnaSettingCondition.Instance.ServiceType == 1919) // TD
            {
                giser.Refresh(ScanGridAnaGridType.TD);
                List<DataTable> dts = stater.GetResult(ScanGridAnaStatType.Rxlev, ScanGridAnaGridType.TD, ranger.CurCoverageType);

                word.InsertText("按网格分段统计如下：", "正文");
                word.NewLine();
                GridView gv = ExperMethod.FillCountGridToForm(this, dts[0]);
                ExperMethod.GridViewToWord(word, gv, curColorRanges);
                gv.Dispose();
                word.NewLine();

                word.InsertText("网格占比如下：", "正文");
                word.NewLine();
                ChartControl cc = ExperMethod.FillCountChartToForm(this, dts[1], rangeType);
                string picFile = ExperMethod.ChartScreenshot(cc, dts[1].Rows.Count);
                cc.Dispose();
                word.InsertPicture(picFile);
                word.NewLine();
            }
        }

        private void ExportRxlevWeak(int index)
        {
            word.InsertText(string.Format("{0}.3. 连续弱覆盖分布情况", index), "标题 2");
            word.NewLine();
            MapForm mf = MainModel.MainForm.GetMapForm();
            ranger.CurRangeType = ScanGridAnaRangeType.WeakRxlev;
            ranger.CurCoverageType = 0; // not used

            if (ScanGridAnaSettingCondition.Instance.ServiceType == 12 ||
                ScanGridAnaSettingCondition.Instance.ServiceType == 1212)//GSM
            {
                word.InsertText("网络最强信号连续弱覆盖整体渲染如下：", "正文");
                giser.Refresh(ScanGridAnaGridType.最强信号);
            }
            else
            {
                word.InsertText("连续弱覆盖整体渲染如下：", "正文");
                giser.Refresh(ScanGridAnaGridType.TD);
            }
            string picFile = ExperMethod.GisScreenshot(mf);
            word.InsertPicture(picFile);
            word.NewLine();
        }

        private void ExportCoverage(int index)
        {
            word.InsertText(string.Format("{0}. 道路重叠覆盖度", index), "标题 1");
            word.NewLine();
            if (ScanGridAnaSettingCondition.Instance.ServiceType == 12 ||
                ScanGridAnaSettingCondition.Instance.ServiceType == 1212) // gsm
            {
                word.InsertText(string.Format("{0}.1. 网络最强信号重叠覆盖度", index), "标题 2");
                word.NewLine();
                ExportCoverageSub(string.Format("{0}.1", index), "标题 3", ScanGridAnaGridType.最强信号);

                word.InsertText(string.Format("{0}.2. GSM900重叠覆盖度", index), "标题 2");
                word.NewLine();
                ExportCoverageSub(string.Format("{0}.2", index), "标题 3", ScanGridAnaGridType.GSM900);

                word.InsertText(string.Format("{0}.3. DCS1800重叠覆盖度", index), "标题 2");
                word.NewLine();
                ExportCoverageSub(string.Format("{0}.3", index), "标题 3", ScanGridAnaGridType.DCS1800);
            }
            else
            {
                ExportCoverageSub(string.Format("{0}", index), "标题 2", ScanGridAnaGridType.TD);
            }
        }

        private void ExportCoverageSub(string title, string style, ScanGridAnaGridType netType)
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            ranger.CurRangeType = ScanGridAnaRangeType.Coverage;
            ranger.CurCoverageType = 0;

            word.InsertText(string.Format("{0}.1 整体重叠覆盖度", title), style);
            word.NewLine();
            word.InsertText("整体渲染效果如下：", "正文");
            word.NewLine();
            ranger.CurCoverageType = 0;
            giser.Refresh(netType);
            string picFile = ExperMethod.GisScreenshot(mf);
            word.InsertPicture(picFile);
            word.NewLine();

            word.InsertText(string.Format("{0}.2 网格级分布情况及占比", title), style);
            word.NewLine();
            word.InsertText("按网格分段统计如下：", "正文");
            word.NewLine();
            List<DataTable> dts = stater.GetResult(ScanGridAnaStatType.Coverage, netType, ranger.CurCoverageType);
            List<ColorRange> curColorRanges = ScanGridAnaColorRanger.Instance.GetColorRanges();
            ScanGridAnaRangeType rangeType = ScanGridAnaColorRanger.Instance.CurRangeType;
            GridView gv = ExperMethod.FillCountGridToForm(this, dts[0]);
            ExperMethod.GridViewToWord(word, gv, curColorRanges);
            gv.Dispose();
            word.NewLine();

            word.InsertText("网格占比如下：", "正文");
            word.NewLine();
            ChartControl cc = ExperMethod.FillCountChartToForm(this, dts[1], rangeType);
            picFile = ExperMethod.ChartScreenshot(cc, dts[1].Rows.Count);
            cc.Dispose();
            word.InsertPicture(picFile);
            word.NewLine();

            word.InsertText(string.Format("{0}.3 连续高重叠覆盖分布情况", title), style);
            word.NewLine();
            word.InsertText("整体渲染如下：", "正文");
            word.NewLine();
            ranger.CurRangeType = ScanGridAnaRangeType.HighCoverage;
            ranger.CurCoverageType = 0;
            giser.Refresh(netType);
            picFile = ExperMethod.GisScreenshot(mf);
            word.InsertPicture(picFile);
            word.NewLine();
        }

        private ScanGridAnaStater stater;
        private ScanGridAnaGisColoring giser;
        private ScanGridAnaColorRanger ranger;
        private ScanGridAnaWord word;
    }
}
