﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.MTGis;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.Net;
using System.Drawing;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public class AreaGridLayer : LayerBase
    {
        public GridMatrix<GridFormula> HostGrids { get; set; }

        public GridMatrix<GridFormula> GuestGrids { get; set; }

        public int XOffset { get; set; }
        public int YOffset { get; set; }

        readonly Pen pHost = new Pen(Color.Pink, 2);
        readonly Pen pGuest = new Pen(Color.Blue, 2);

        public AreaGridLayer()
            : base("竞比栅格图层")
        {
            HostGrids = new GridMatrix<GridFormula>();
            GuestGrids = new GridMatrix<GridFormula>();

            XOffset = 5;
            XOffset = 5;
        }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, System.Drawing.Graphics graphics)
        {
            if ((HostGrids == null && GuestGrids == null) || !IsVisible) return;

            Rectangle inflatedRect = new Rectangle(updateRect.X, updateRect.Y, updateRect.Width, updateRect.Height);
            inflatedRect.Inflate(50, 50);
            DbRect dRect;
            gisAdapter.FromDisplay(updateRect, out dRect);

            drawRangle(HostGrids, false, dRect, graphics);
            drawRangle(GuestGrids, true, dRect, graphics);
        }

        private void drawRangle(GridMatrix<GridFormula> grids, bool bGuest, DbRect rect, Graphics graphics)
        {
            if (grids == null) return;

            foreach (GridFormula grid in grids)
            {
                if (rect.Within(grid.Bounds))
                {
                    drawRangle(grid, bGuest, graphics);
                }
            }
        }

        private void drawRangle(GridFormula grid, bool bGuest, Graphics graphics)
        {
            bool bExist = HostGrids[grid.RowIdx, grid.ColIdx] != null;

            RectangleF desRectF;
            gisAdapter.ToDisplay(grid.Bounds, out desRectF);

            if (bGuest && bExist)
                graphics.DrawRectangle(pGuest, desRectF.Left + XOffset, desRectF.Top + YOffset, desRectF.Width, desRectF.Height);
            else
                graphics.DrawRectangle(bGuest ? pGuest : pHost, desRectF.Left, desRectF.Top, desRectF.Width, desRectF.Height);
        }
    }
}
