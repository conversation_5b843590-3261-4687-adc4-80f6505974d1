﻿using System;
using System.CodeDom.Compiler;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Runtime.Serialization;
using System.Windows.Forms;
using System.Xml;
using MapWinGIS;
using MasterCom.MControls;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.AssistLayer;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util;
using Microsoft.CSharp;
using System.Text;

namespace MasterCom.RAMS.Func
{
    [Serializable()]
    public class MapDTLayer : LayerBase, ISerializable, IKMLExport
    {
        /// <summary>
        /// 导出事件样式用到，code、color对
        /// </summary>
        public struct VectorCodeColor
        {
            public int Id
            {
                get { return id; }
            }
            public int Code
            {
                get { return code; }
            }
            public Color Color
            {
                get { return color; }
            }
            public VectorCodeColor(int id, int code, Color color)
            {
                this.id = id;
                this.code = code;
                this.color = color;
            }
            private readonly int id;
            private readonly int code;
            private readonly Color color;
        }

        public MapDTLayer(string name)
            : base(name)
        {
            neighbourPens[0] = new Pen(Color.LightGreen, 2);
            neighbourPens[1] = new Pen(Color.Orange, 2);
            neighbourPens[2] = new Pen(Color.Red, 2);
            neighbourPens[3] = new Pen(Color.Green, 2);
            neighbourPens[4] = new Pen(Color.Blue, 2);
            neighbourPens[5] = new Pen(Color.Brown, 2);
            neighbourPens[6] = new Pen(Color.Tomato, 2);
            neighbourPens[7] = new Pen(Color.Yellow, 2);
            neighbourPens[8] = new Pen(Color.Lime, 2);
            neighbourPens[9] = new Pen(Color.Maroon, 2);
            neighbourPens[10] = new Pen(Color.SpringGreen, 2);
            neighbourPens[11] = new Pen(Color.RoyalBlue, 2);
            neighbourPens[12] = new Pen(Color.BlueViolet, 2);
            neighbourPens[13] = new Pen(Color.HotPink, 2);
            neighbourPens[14] = new Pen(Color.Crimson, 2);
            for (int i = 0; i < neighbourPens.Length; i++)
            {
                neighbourPens[i].DashStyle = DashStyle.DashDotDot;
            }
        }
        public override int GetSupportedThemeOptions()
        {
            return 0;
        }
        public override double CalcThemeOption(int themeOption, object[] args)
        {
            return double.NaN;
        }
        public void SetArrowVisble(bool visible)
        {
            TestPointArrowManager.Visible = visible;
            foreach (MapEventInfo eventInfo in EventInfos)
            {
                eventInfo.DTArrowVisible = visible;
            }
        }

        [NonSerialized()]
        TestPointArrowManager testPointArrowManager;

        public TestPointArrowManager TestPointArrowManager
        {
            get
            {
                if (testPointArrowManager == null && mainModel != null)
                    testPointArrowManager = mainModel.SystemConfigInfo[typeof(TestPointArrowManager).FullName] as TestPointArrowManager;
                return testPointArrowManager;
            }
        }

        public bool allowMoveGE { get; set; } = false;//默认不联动到GE
        public override MainModel MainModel
        {
            get { return mainModel; }
            set
            {
                mainModel = value;
                if (mainModel != null)
                {
                    testPointArrowManager = mainModel.SystemConfigInfo[typeof(TestPointArrowManager).FullName] as TestPointArrowManager;
                }
                MasterCom.ES.ColorManager.EventColorDialog.GetOneInstance(this.MainModel);
            }
        }

        public ReadOnlyCollection<MapSerialInfo> SerialInfos
        {
            get { return DTLayerSerialManager.Instance.SelectedSerials; }
        }
        
        public SizeF EventImgSize { get; set; } = new SizeF(16.0f, 16.0f);
        
        public int PointSize { get; set; } = 20;

        public List<MapEventInfo> EventInfos { get; set; } = new List<MapEventInfo>();
        
        public Pen PenSelected { get; set; } = new Pen(Color.Red, 3);
        
        public bool NeedSample { get; set; } = true;

        public int SampleLevel { get; set; }
        public void ApplySampledTestPoints(LinkedList<TestPoint> list)
        {
            this.sampledTestPoints = list;
        }
        public LinkedList<TestPoint> SampledTestPoints
        {
            get { return sampledTestPoints; }
        }
        
        public MapSerialInfo CurFlyLinesSerialInfo //当前飞线的着色指标
        {
            get { return DTLayerSerialManager.Instance.FlyLineSerial; }
            set { DTLayerSerialManager.Instance.FlyLineSerial = value; }
        }

        private bool flyColorFromOrig = false;
        /*
         * 设置取飞线时的逻辑方式，若为true，则颜色从0位置开始取（借助flyCellColorIdxDic)
         * 
         */
        public bool FlyColorFromOrig
        {
            get { return flyColorFromOrig; }
            set
            {
                flyColorFromOrig = value;
                flyCellColorIdxDic.Clear();
            }
        }
        private Dictionary<int, int> flyCellColorIdxDic = new Dictionary<int, int>();

        public bool IsBySerials { get; set; } = true;//是否按指标着色飞线,true按指标着色，false主服小区来着色

        public bool IsScanByTop1 { get; set; } = true;//扫频是否对1强小区外小区连线

        public int ScanRxLevThreshold { get; set; } = -85;//扫频对满足条件小区飞线且信号强度>=该门限
        
        public bool DrawScaleBar{ get; set; } = true;//是否画比例尺

        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            base.GetObjectData(info, context);
            info.AddValue("EventInfos", EventInfos);
            info.AddValue("NeedSample", NeedSample);
            info.AddValue("SampleLevel", SampleLevel);
            List<OwnFuncCommander> cmdList = new List<OwnFuncCommander>();
            cmdList.AddRange(OwnFuncCommanderDic.Values);
            info.AddValue("OwnFuncCommanders", cmdList);
        }

        public void SetObjectData(SerializationInfo info, StreamingContext context)
        {
            EventInfos = (List<MapEventInfo>)info.GetValue("EventInfos", typeof(List<MapEventInfo>));
            NeedSample = (bool)info.GetValue("NeedSample", typeof(bool));
            SampleLevel = (int)info.GetValue("SampleLevel", typeof(int));
            List<OwnFuncCommander> cmdList = (List<OwnFuncCommander>)info.GetValue("OwnFuncCommanders", typeof(List<OwnFuncCommander>));
            OwnFuncCommanderDic.Clear();
            foreach (OwnFuncCommander cmd in cmdList)
            {
                cmd._classReady = false;//在MapinfoSetObject过程中，_clzz已经变为空，所以一定要重新置位
                cmd._hasError = false;
                OwnFuncCommanderDic[cmd.desc] = cmd;
            }
        }

        public override void Invalidate()
        {
            foreach (MapEventInfo eventInfo in EventInfos)
            {
                eventInfo.dtDataArrowManagerClear();
            }
            base.Invalidate();
        }

        private void getAbstractSampleList(LinkedList<TestPoint> sampledTestPoints)
        {
            int sampleLevel = this.SampleLevel;
            #region getSampleLevel
            if (sampleLevel >= 16 || sampleLevel <= 0)
            {
                sampleLevel = getSampleLevel(sampledTestPoints);
                if (curMapType == LayerMapType.MTGis)
                {
                    mainModel.FireTestPointSampleChanged(NeedSample, this.SampleLevel, sampleLevel);
                }
            }
            #endregion

            #region getSampleMinus and Group
            bool sampleMinus = true;
            int sampleGroupCount = 0;
            setSampleGroupCount(sampleLevel, ref sampleMinus, ref sampleGroupCount);
            #endregion

            #region getAbstractList
            if (sampleLevel > 0)
            {
                int i = 0;
                short? lastValue = 0;
                LinkedListNode<TestPoint> lastNode = null;
                LinkedListNode<TestPoint> node = sampledTestPoints.First;
                LinkedListNode<TestPoint> tempNode = null;
                while (node != null)
                {
                    if (i == 0)
                    {
                        lastNode = node;
                        lastValue = node.Value["RxLevSub"] as short?;
                        node = node.Next;
                    }
                    else
                    {
                        removeSampleMinus(sampledTestPoints, sampleMinus, ref lastValue, ref lastNode, ref node, ref tempNode);
                    }
                    i++;
                    removeLastSampleMinus(sampledTestPoints, sampleMinus, sampleGroupCount, ref i, lastNode, ref node);
                }
            }
            #endregion
        }

        private void removeSampleMinus(LinkedList<TestPoint> sampledTestPoints, bool sampleMinus, ref short? lastValue, ref LinkedListNode<TestPoint> lastNode, ref LinkedListNode<TestPoint> node, ref LinkedListNode<TestPoint> tempNode)
        {
            short? value = node.Value["RxLevSub"] as short?;
            if (sampleMinus)
            {
                if (lastValue <= value)
                {
                    tempNode = node.Next;
                    sampledTestPoints.Remove(node);
                    node = tempNode;
                }
                else
                {
                    sampledTestPoints.Remove(lastNode);
                    lastNode = node;
                    lastValue = node.Value["RxLevSub"] as short?;
                    node = node.Next;
                }
            }
            else
            {
                if (lastValue < value)
                {
                    lastNode = node;
                    lastValue = node.Value["RxLevSub"] as short?;
                }
            }
        }

        private void removeLastSampleMinus(LinkedList<TestPoint> sampledTestPoints, bool sampleMinus, int sampleGroupCount, ref int i, LinkedListNode<TestPoint> lastNode, ref LinkedListNode<TestPoint> node)
        {
            if (i >= sampleGroupCount)
            {
                if (!sampleMinus)
                {
                    sampledTestPoints.Remove(lastNode);
                    node = node.Next;
                }
                i = 0;
            }
        }

        private void setSampleGroupCount(int sampleLevel, ref bool sampleMinus, ref int sampleGroupCount)
        {
            if (sampleLevel == 1)
            {
                sampleMinus = false;
                sampleGroupCount = 5;
            }
            else if (sampleLevel == 2)
            {
                sampleMinus = false;
                sampleGroupCount = 3;
            }
            else if (sampleLevel == 3)
            {
                sampleGroupCount = 2;
            }
            else if (sampleLevel == 4)
            {
                sampleGroupCount = 3;
            }
            else if (sampleLevel == 5)
            {
                sampleGroupCount = 4;
            }
            else if (sampleLevel == 6)
            {
                sampleGroupCount = 5;
            }
            else if (sampleLevel == 7)
            {
                sampleGroupCount = 7;
            }
            else if (sampleLevel == 8)
            {
                sampleGroupCount = 10;
            }
            else if (sampleLevel == 9)
            {
                sampleGroupCount = 15;
            }
            else if (sampleLevel == 10)
            {
                sampleGroupCount = 20;
            }
            else if (sampleLevel == 11)
            {
                sampleGroupCount = 30;
            }
            else if (sampleLevel == 12)
            {
                sampleGroupCount = 40;
            }
            else if (sampleLevel == 13)
            {
                sampleGroupCount = 60;
            }
            else if (sampleLevel == 14)
            {
                sampleGroupCount = 80;
            }
            else if (sampleLevel == 15)
            {
                sampleGroupCount = 100;
            }
        }

        private int getSampleLevel(LinkedList<TestPoint> sampledTestPoints)
        {
            if (sampledTestPoints.Count < displayTestPointMaxCount)
            {
                return 0;
            }
            else if (sampledTestPoints.Count < displayTestPointMaxCount * 1.25)
            {
                return 1;
            }
            else if (sampledTestPoints.Count < displayTestPointMaxCount * 1.6)
            {
                return 2;
            }
            else if (sampledTestPoints.Count < displayTestPointMaxCount * 2.4)
            {
                return 3;
            }
            else if (sampledTestPoints.Count < displayTestPointMaxCount * 3.3)
            {
                return 4;
            }
            else if (sampledTestPoints.Count < displayTestPointMaxCount * 4)
            {
                return 5;
            }
            else if (sampledTestPoints.Count < displayTestPointMaxCount * 5.6)
            {
                return 6;
            }
            else if (sampledTestPoints.Count < displayTestPointMaxCount * 8)
            {
                return 7;
            }
            else if (sampledTestPoints.Count < displayTestPointMaxCount * 12)
            {
                return 8;
            }
            else if (sampledTestPoints.Count < displayTestPointMaxCount * 16)
            {
                return 9;
            }
            else if (sampledTestPoints.Count < displayTestPointMaxCount * 24)
            {
                return 10;
            }
            else if (sampledTestPoints.Count < displayTestPointMaxCount * 32)
            {
                return 11;
            }
            else if (sampledTestPoints.Count < displayTestPointMaxCount * 50)
            {
                return 12;
            }
            else if (sampledTestPoints.Count < displayTestPointMaxCount * 64)
            {
                return 13;
            }
            else if (sampledTestPoints.Count < displayTestPointMaxCount * 80)
            {
                return 14;
            }
            return 15;
        }

        private void drawCellCover(Graphics graphics)
        {
            showSetting setting = mainModel.CovCellShowSetting;
            switch (setting.lineCellMode)
            {
                case showSetting.CellMode.bySingle:
                    {
                        drawSingleCell(graphics, setting);
                    }
                    break;
                case showSetting.CellMode.byDouble:
                    {
                        drawDoubleCell(graphics, setting);
                    }
                    break;
                case showSetting.CellMode.byMulti:
                    {
                        drawMultiCell(graphics, setting);
                    }
                    break;
                default:
                    break;
            }
        }

        private void drawSingleCell(Graphics graphics, showSetting setting)
        {
            if (setting.lineSingleCell_maincell == null)
            {
                return;
            }
            foreach (TestPoint tp in setting.lineSingleCell_maincell.tpList)
            {
                drawFlyLinesForCovCell(graphics, tp, setting.lineSingleCell_maincell.cell, setting.lineSingleCell_maincellColor);
            }

            foreach (TestPoint tp in setting.lineSingleCell_maincell.tpListAsNbcell)
            {
                drawFlyLinesForCovCell(graphics, tp, setting.lineSingleCell_maincell.cell, setting.lineSingleCell_nbcellColor);
            }
        }

        private void drawDoubleCell(Graphics graphics, showSetting setting)
        {
            if (setting.lineDoubleCell_cellA == null)
            {
                return;
            }
            foreach (TestPoint tp in setting.lineDoubleCell_cellA.tpList)
            {
                drawFlyLinesForCovCell(graphics, tp, setting.lineDoubleCell_cellA.cell, setting.lineDoubleCell_cellAColor);
            }
            foreach (TestPoint tp in setting.lineDoubleCell_cellB.tpList)
            {
                drawFlyLinesForCovCell(graphics, tp, setting.lineDoubleCell_cellB.cell, setting.lineDoubleCell_cellBColor);
            }
        }

        private void drawMultiCell(Graphics graphics, showSetting setting)
        {
            if (setting.lineMultiCellColorDic.Count == 0)
            {
                return;
            }
            foreach (CoverageCellInfo cellinfo in setting.lineMultiCellColorDic.Keys)
            {
                foreach (TestPoint tp in cellinfo.tpList)
                {
                    drawFlyLinesForCovCell(graphics, tp, cellinfo.cell, setting.lineMultiCellColorDic[cellinfo]);
                }
            }
        }

        private void drawFlyLinesForCovCell(Graphics graphics, TestPoint sampleTestpoint, object cell, Color color)
        {
            DbPoint cellDPoint = null;
            if (cell is Cell)
            {
                cellDPoint = new DbPoint((cell as Cell).EndPointLongitude, (cell as Cell).EndPointLatitude);
            }
            else if (cell is TDCell)
            {
                cellDPoint = new DbPoint((cell as TDCell).EndPointLongitude, (cell as TDCell).EndPointLatitude);
            }

            PointF cellPointF;
            gisAdapter.ToDisplay(cellDPoint, out cellPointF);
            DbPoint testDPoint = new DbPoint(sampleTestpoint.Longitude, sampleTestpoint.Latitude);
            PointF testPointF;
            gisAdapter.ToDisplay(testDPoint, out testPointF);
            graphics.DrawLine(new Pen(color, 1), testPointF, cellPointF);
        }

        private void drawMeasurePoints(Graphics graphics)
        {
            PointF[] points;
            gisAdapter.ToDisplay(measurePoints.ToArray(), out points);
            graphics.DrawLines(measurePen, points);
            System.Drawing.Font fontMeasure = new System.Drawing.Font(new FontFamily("宋体"), 10, FontStyle.Bold);
            double distanceTotal = 0;
            for (int i = 1; i < measurePoints.Count; i++)
            {
                double longitudeDistance = (Math.Sin((90 - measurePoints[i].y) * 2 * Math.PI / 360) + Math.Sin((90 - measurePoints[i - 1].y) * 2 * Math.PI / 360)) / 2 * (measurePoints[i].x - measurePoints[i - 1].x) / 360 * 40075360;
                double latitudeDistance = (measurePoints[i].y - measurePoints[i - 1].y) / 360 * 39940670;
                double distance = Math.Sqrt(longitudeDistance * longitudeDistance + latitudeDistance * latitudeDistance);
                distanceTotal += distance;
                string towrite = "" + (int)distance + "m," + (int)distanceTotal + "m";
                SizeF sizeF = graphics.MeasureString(towrite, fontMeasure);
                graphics.FillRectangle(new SolidBrush(Color.Yellow), points[i].X, points[i].Y, sizeF.Width, sizeF.Height);
                graphics.DrawString(towrite, fontMeasure, Brushes.Red, points[i]);
            }
        }

        private void drawHandOverSeq(Graphics graphics)
        {
            List<Event> seqEvents = mainModel.HandOverSeqEvents;
            int i = 0;
            Dictionary<string, int> offsetDifDic = new Dictionary<string, int>();
            foreach (Event seqEvt in seqEvents)
            {
                ICell cell = seqEvt.GetSrcCell();
                ICell tarCell = seqEvt.GetTargetCell();
                if (cell != null && tarCell != null)
                {
                    DbPoint cellDPoint = new DbPoint(cell.EndPointLongitude, cell.EndPointLatitude);
                    DbPoint cellDPointTarget = new DbPoint(tarCell.EndPointLongitude, tarCell.EndPointLatitude);
                    PointF cellPointF;
                    gisAdapter.ToDisplay(cellDPoint, out cellPointF);
                    PointF cellPointFTarget;
                    gisAdapter.ToDisplay(cellDPointTarget, out cellPointFTarget);
                    Pen seqPen = new Pen(Color.FromArgb(150, Color.Orange), 4);
                    if (mainModel.SelectedEvents.Count > 0 && mainModel.SelectedEvents[0] == seqEvt)
                    {
                        seqPen.Color = Color.FromArgb(200, Color.Green);
                    }
                    seqPen.DashStyle = DashStyle.Solid;
                    seqPen.StartCap = LineCap.Flat;
                    System.Drawing.Drawing2D.AdjustableArrowCap lineCap = new System.Drawing.Drawing2D.AdjustableArrowCap(6, 6, true);
                    seqPen.CustomEndCap = lineCap;

                    int ctOffset = 0;
                    if (offsetDifDic.TryGetValue(cell.Name + ":" + tarCell.Name, out ctOffset))
                    {
                        //DO nothing
                    }
                    ctOffset++;
                    offsetDifDic[cell.Name + ":" + tarCell.Name] = ctOffset;
                    PointF posMid = new PointF(0.5f * (cellPointF.X + cellPointFTarget.X), 0.5f * (cellPointF.Y + cellPointFTarget.Y));
                    makeArcMiddleOffsetPoint(cellPointF, cellPointFTarget, ref posMid, ctOffset);

                    graphics.DrawEllipse(seqPen, cellPointF.X, cellPointF.Y, 2, 2);
                    graphics.DrawEllipse(seqPen, cellPointFTarget.X, cellPointFTarget.Y, 2, 2);

                    MathFuncs.drawTest(graphics, seqPen, cellPointF, posMid, cellPointFTarget);
                    DbPoint MiddleOffsetPoint;
                    gisAdapter.FromDisplay(new PointF(posMid.X, posMid.Y), out MiddleOffsetPoint);
                    drawHandOverSeqLabel(seqEvt, i.ToString(), graphics, MiddleOffsetPoint);
                    i++;
                }
            }
        }

        private void drawTDScanCellChange(Graphics graphics)
        {
            foreach (TDScanMainCellChangeCellItem cellItem in mainModel.CurSelTdScanMcChangeCellItems)
            {
                if (cellItem.MainCell != null)
                {
                    DbPoint cellDPoint = new DbPoint(cellItem.MainCell.EndPointLongitude, cellItem.MainCell.EndPointLatitude);
                    PointF cellPoint;
                    gisAdapter.ToDisplay(cellDPoint, out cellPoint);
                    Pen pen = new Pen(ColorSequenceSupplier.getVividColor(cellItem.MainCell.ID), 2);
                    foreach (TestPoint tp in cellItem.TestPoints)
                    {
                        DbPoint dPoint = new DbPoint(tp.Longitude, tp.Latitude);
                        PointF point;
                        gisAdapter.ToDisplay(dPoint, out point);
                        graphics.DrawLine(pen, point.X, point.Y, cellPoint.X, cellPoint.Y);
                    }
                }
            }
        }

        private void drawSimulationMapName(Graphics graphics)
        {
            foreach (SimulationRegionInfo sRInfo in mainModel.SimulationRegionInfoList)
            {
                string regionDesc = sRInfo.regionName;
                DbPoint dPoint = new DbPoint(sRInfo.shape.Center.x, sRInfo.shape.Center.y);
                PointF centerPointF;
                gisAdapter.ToDisplay(dPoint, out centerPointF);
                graphics.TranslateTransform(centerPointF.X, centerPointF.Y);
                System.Drawing.Font fontSlMText = new System.Drawing.Font(new FontFamily("宋体"), 12, FontStyle.Bold);
                SizeF size = graphics.MeasureString(regionDesc, fontSlMText);
                size.Height *= 0.8f;
                graphics.FillRectangle(Brushes.White, 3, -size.Height / 2, size.Width, size.Height);
                graphics.DrawString(regionDesc, fontSlMText, Brushes.Black, 3, -size.Height / 2);
                graphics.ResetTransform();
            }
        }

        public bool DrawInvalidPoint { get; private set; } = false;
        public Color InvalidPointColor { get; private set; } = Color.Black;
        public void SetDrawInvalidPoint(bool drawInvalidPoint, Color color)
        {
            DrawInvalidPoint = drawInvalidPoint;
            InvalidPointColor = color;
            foreach (MapSerialInfo serialInfo in DTLayerSerialManager.Instance.SelectedSerials)
            {
                serialInfo.DrawInvalidPoint = drawInvalidPoint;
                serialInfo.InvalidPointColor = color;
            }
        }

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            sampledTestPoints.Clear();
            float ratio = CustomDrawLayer.GetTestPointDisplayRatio(mapScale);
            updateRect.Inflate((int)(64 * ratio), (int)(64 * ratio));
            DbRect dRect;
            gisAdapter.FromDisplay(updateRect, out dRect);

#if QJW_GRID //绘固定栅格，用于调试显示
            if (mapScale <= 8000000)
            {
                double xSpan = dRect.x2 - dRect.x1;
                double ySpan = dRect.y2 - dRect.y1;
                int xNum = (int)(xSpan / (CD.ATOM_SPAN_LONG*100)) + 1;
                int yNum = (int)(ySpan / (CD.ATOM_SPAN_LAT*100)) + 1;
                int rowIdx = 0;
                int colIdx = 0;
                Grid.GridHelper.GetIndexOfCustomSizeGrid(100, dRect.x1, dRect.y2, out rowIdx, out colIdx);
                double y1 = rowIdx * (CD.ATOM_SPAN_LAT * 100);
                for (int i = 0; i < xNum; i++)
                {
                    double x = (colIdx + 1) * (CD.ATOM_SPAN_LONG * 100) + (i * (CD.ATOM_SPAN_LONG * 100));
                    PointF p1;
                    gisAdapter.ToDisplay(new DbPoint(x, y1), out p1);
                    PointF p2 = new PointF(p1.X, 700);
                    graphics.DrawLine(Pens.Red, p1, p2);
                }

                double x2 = colIdx * (CD.ATOM_SPAN_LONG * 100);
                for (int i = 0; i < yNum; i++)
                {
                    double y = (rowIdx + 1) * (CD.ATOM_SPAN_LAT * 100) - (i * (CD.ATOM_SPAN_LAT * 100));
                    PointF p1;
                    gisAdapter.ToDisplay(new DbPoint(x2, y), out p1);
                    PointF p2 = new PointF(1300, p1.Y);
                    graphics.DrawLine(Pens.Red, p1, p2);
                }
            }
#endif

            getValidSampledTestPoints(dRect);

            if (NeedSample)     ////设置了抽样
            {
                getAbstractSampleList(sampledTestPoints);
            }

            if (MainModel.DTDataManager.FileDataManagers.Count > 0)
            {
                drawTPs(graphics, ratio, dRect);
            }

            if (mainModel.DrawFlyLines)
            {
                drawFlyLines(sampledTestPoints, graphics, DTLayerSerialManager.Instance.FlyLineSerial);
            }

            if (DrawLocationPoint)
            {
                drawLocationCross(graphics);
            }

            if (DrawScaleBar)
            {
                drawScaleRuler(updateRect, graphics, mapScale);
            }

            if (curMapType == LayerMapType.MTGis)
            {
                drawOtherElements(graphics);
            }
        }

        private void getValidSampledTestPoints(DbRect dRect)
        {
            foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
            {
                if (MainModel.VisibleOffsetManager.IsFileVisble(fileDataManager.FileID)
                    && MainModel.VisibleOffsetManager.IsProjectVisble(fileDataManager.ProjectType)
                    && MainModel.VisibleOffsetManager.IsServiceVisble(fileDataManager.ServiceType))
                {
                    foreach (TestPoint testPoint in fileDataManager.TestPoints)
                    {
                        addValidSampledTestPoints(dRect, testPoint);
                    }
                }
            }
        }

        private void addValidSampledTestPoints(DbRect dRect, TestPoint testPoint)
        {
            if (testPoint.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2))
            {
                foreach (MapSerialInfo serialInfo in DTLayerSerialManager.Instance.SelectedSerials)
                {
                    Color? color;
                    int? size;
                    int? symbol;
                    if (serialInfo.getStyle(this, testPoint, out color, out size, out symbol)
                        || (color == null && DrawInvalidPoint))
                    {
                        sampledTestPoints.AddLast(testPoint);
                        break;
                    }
                }
            }
        }

        private void drawTPs(Graphics graphics, float ratio, DbRect dRect)
        {
            List<TestPoint> tpList = dealOffsetPoints(this, new List<TestPoint>(sampledTestPoints));
            testPointArrowManager.ResetMapElements(gisAdapter, tpList);
            //XmlElement DocumentNode;//<Document>结点
            //XmlDocument root = MakeXMLHead(out DocumentNode);//根节点
            foreach (MapEventInfo eventInfo in EventInfos)
            {
                try
                {
                    if (eventInfo.Visible || mainModel.LegendItem.ContainEventID(eventInfo.EventID))
                    {
                        eventInfo.Draw(this, ratio, dRect, graphics);
                    }
                }
                catch
                {
                    //continue
                }
            }
            foreach (MapSerialInfo serialInfo in DTLayerSerialManager.Instance.SelectedSerials)
            {
                serialInfo.Draw(this, ratio, dRect, graphics, mainModel);
            }
        }

        private void drawOtherElements(Graphics graphics)
        {
            if (mainModel.CellCvrRngItem != null && mainModel.IsDrawCoverRangeLine)
            {
                drawCoverRangeLine(sampledTestPoints, graphics);
            }

            if (mainModel.CovCellShowSetting != null) ////小区覆盖带
            {
                drawCellCover(graphics);
            }

            if (measurePoints.Count > 1)
            {
                drawMeasurePoints(graphics);
            }

            if (mainModel.DrawHandoverSeq)
            {
                drawHandOverSeq(graphics);
            }

            if (mainModel.CurFarCellCover != null && mainModel.CurFarCellCover.TestPointCount > 0)
            {
                drawFarCoverLines(sampledTestPoints, graphics, DTLayerSerialManager.Instance.FlyLineSerial);
            }

            if (mainModel.CurSelTdScanMcChangeCellItems.Count > 0)
            {
                drawTDScanCellChange(graphics);
            }

            if (mainModel.SimulationRegionInfoList.Count > 0)
            {
                drawSimulationMapName(graphics);
            }

            if (mainModel.DrawLinesPntToCells && mainModel.PntToCellsDic.Count > 0)
            {
                drawLinesPntToCells(sampledTestPoints, graphics, DTLayerSerialManager.Instance.FlyLineSerial);
            }

            drawOverlapLine(graphics);
        }

        private List<TestPoint> dealOffsetPoints(MapDTLayer layer, List<TestPoint> sampleList)
        {
            List<TestPoint> tpList = new List<TestPoint>();
            foreach (var tp in sampleList)
            {
                DbPoint dPoint = new DbPoint(tp.Longitude, tp.Latitude);
                PointF point;
                layer.GisAdapter.ToDisplay(dPoint, out point);
                int curOffsetX = 0;
                int curOffsetY = 0;
                layer.MainModel.VisibleOffsetManager.GetOffset(ref curOffsetX, ref curOffsetY, tp.FileID, tp.ProjectType, tp.ServiceType);
                point += new Size(curOffsetX, curOffsetY);
                DbPoint offSetPoint;
                layer.GisAdapter.FromDisplay(point, out offSetPoint);
                TestPoint curTP = new TestPoint();
                curTP.Longitude = offSetPoint.x;
                curTP.Latitude = offSetPoint.y;
                tpList.Add(curTP);
            }
            return tpList;
        }

        /// <summary>
        /// 扫频过覆盖拉线
        /// </summary>
        /// <param name="graphics"></param>
        private void drawOverlapLine(Graphics graphics)
        {
            foreach (ScanOverlapPoint pnt in MainModel.CurOverlapPointList)
            {
                if (!sampledTestPoints.Contains(pnt.Point))
                {
                    continue;
                }

                DbPoint dPoint = new DbPoint(pnt.Point.Longitude, pnt.Point.Latitude);
                PointF tpPnt;
                gisAdapter.ToDisplay(dPoint, out tpPnt);
                if (mainModel.ShowOverlapLine)
                {
                    dPoint = new DbPoint(pnt.CellMaxEndLng, pnt.CellMaxEndLat);
                    PointF cellPnt;
                    gisAdapter.ToDisplay(dPoint, out cellPnt);
                    graphics.DrawLine(new Pen(Color.Red, 2), cellPnt, tpPnt);
                }
                if (mainModel.ShowNearestLine)
                {
                    dPoint = new DbPoint(pnt.CellNearestEndLng, pnt.CellNearestEndLat);
                    PointF cellPnt;
                    gisAdapter.ToDisplay(dPoint, out cellPnt);
                    graphics.DrawLine(new Pen(Color.Lime, 2), cellPnt, tpPnt);
                }
            }
        }

        private void drawFarCoverLines(LinkedList<TestPoint> sampledTestPoints, Graphics graphics, MapSerialInfo serialInfo)
        {
            foreach (TestPoint testPoint in sampledTestPoints)
            {
                Color? color;
                int? size;
                int? symbol;
                if (serialInfo.getStyle(this, testPoint, out color, out size, out symbol))
                {
                    DbPoint testDPoint = new DbPoint(testPoint.Longitude, testPoint.Latitude);
                    PointF testPointF;
                    gisAdapter.ToDisplay(testDPoint, out testPointF);

                    DbPoint cellDPoint = new DbPoint(mainModel.CurFarCellCover.LongitudeCell, mainModel.CurFarCellCover.LatitudeCell);
                    PointF cellPointF;
                    gisAdapter.ToDisplay(cellDPoint, out cellPointF);
                    graphics.DrawLine(new Pen((Color)color, 1), testPointF, cellPointF);
                }
            }
        }

        private void drawLinesPntToCells(LinkedList<TestPoint> sampledTestPoints, Graphics graphics, MapSerialInfo serialInfo)
        {
            foreach (TestPoint testPoint in sampledTestPoints)
            {
                Color? color;
                int? size;
                int? symbol;
                if (serialInfo.getStyle(this, testPoint, out color, out size, out symbol))
                {
                    DbPoint testDPoint = new DbPoint(testPoint.Longitude, testPoint.Latitude);
                    PointF testPointF;
                    gisAdapter.ToDisplay(testDPoint, out testPointF);

                    if (!mainModel.PntToCellsDic.ContainsKey(testPoint)) continue;

                    foreach (LongLat ll in mainModel.PntToCellsDic[testPoint])
                    {
                        DbPoint cellDPoint = new DbPoint(ll.fLongitude, ll.fLatitude);
                        PointF cellPointF;
                        gisAdapter.ToDisplay(cellDPoint, out cellPointF);
                        graphics.DrawLine(new Pen((Color)color, 1), testPointF, cellPointF);
                    }
                }
            }
        }

        private void drawHandOverSeqLabel(Event ev, string txt, Graphics graphics, DbPoint point)
        {
            LabelElementEx le = new LabelElementEx();
            le.Text = txt;
            le.LocationM = point;
            le.GisAdapter = gisAdapter;
            le.IsDraw = true;
            le.MyFill.Color = Color.Yellow;
            le.MyFont.IsBold = true;
            le.OffsetV = 15;
            le.Tag = new Event[] { ev };
            le.Draw(graphics);
        }

        public void makeArcMiddleOffsetPoint(PointF fromPt, PointF toPt, ref PointF posMid, int ctOffset)
        {
            double CellDist = Math.Sqrt((fromPt.X - toPt.X) * (fromPt.X - toPt.X) + (fromPt.Y - toPt.Y) * (fromPt.Y - toPt.Y));
            double DIST = CellDist * 0.2;
            double angle = toPt.X == fromPt.X ? 0 : Math.Atan((double)(toPt.Y - fromPt.Y) / (toPt.X - fromPt.X));
            double XOffset = DIST * Math.Sin(angle);
            double YOffset = DIST * Math.Cos(angle);
            if (toPt.X > fromPt.X)
            {
                YOffset *= -1;
            }
            if (toPt.Y > fromPt.Y)
            {
                XOffset *= -1;
            }
            posMid.X = (float)(0.5f * (fromPt.X + toPt.X) + XOffset * ctOffset);
            posMid.Y = (float)(0.5f * (fromPt.Y + toPt.Y) + YOffset * ctOffset);
        }

        private void drawScaleRuler(Rectangle updateRect, Graphics graphics, double scale)
        {
            PointF pointRulerLeft = new PointF(updateRect.Left + 15, updateRect.Bottom - 30);
            PointF pointTitle = new PointF(updateRect.Left + 80, updateRect.Bottom - 50);
            DbPoint dpointRulerLeft;
            gisAdapter.FromDisplay(pointRulerLeft, out dpointRulerLeft);

            double scaleNeed = GetScaleStandard(scale);
            double _SPAN_LONG_EACH = 0.000009755;
            PointF pointRulerRight;
            DbPoint dpointRulerRight = new DbPoint(dpointRulerLeft.x + scaleNeed * _SPAN_LONG_EACH, dpointRulerLeft.y);
            gisAdapter.ToDisplay(dpointRulerRight, out pointRulerRight);

            System.Drawing.Font fontText = new System.Drawing.Font(new FontFamily("宋体"), 10, FontStyle.Regular);
            string strScale = scaleNeed.ToString() + "米";
            SizeF size = graphics.MeasureString(strScale, fontText);

            PointF pointMiddle = new PointF((pointRulerRight.X + pointRulerLeft.X) / 2 - size.Width / 2, pointTitle.Y);
            graphics.DrawString(strScale, fontText, new SolidBrush(Color.Gray), pointMiddle);//写文字
            graphics.DrawLine(new Pen(Color.Black, 2), pointRulerRight, pointRulerLeft);//画尺
            graphics.DrawLine(new Pen(Color.Black, 1), pointRulerRight, new PointF(pointRulerRight.X, pointRulerRight.Y - 8));//画尺的右边
            graphics.DrawLine(new Pen(Color.Black, 1), pointRulerLeft, new PointF(pointRulerLeft.X, pointRulerLeft.Y - 8));//画尺的左边
        }

        public double GetScaleStandard(double scale)//根据当前显示比例获取比例尺的单位显示距离
        {
            double distancePerCM = scale / 100.0;//图上1厘米所对应的实际距离(米)
            double scaleNeed = 0.0;
            if (distancePerCM > 0 && distancePerCM < scaleStandard[0])
            {
                return scaleNeed;
            }
            for (int i = 0; i < scaleStandard.Length; i++)
            {
                if (scaleStandard[i] > distancePerCM)
                {
                    scaleNeed = scaleStandard[i];
                    break;
                }
            }
            return scaleNeed;
        }

        private void drawLocationCross(Graphics graphics)
        {
            PointF pf;
            gisAdapter.ToDisplay(locationPoint, out pf);
            Pen penCrossYellow = new Pen(Color.Yellow, 2);
            Pen penCrossRed = new Pen(Color.Red, 2);
            penCrossRed.DashStyle = DashStyle.Dot;
            graphics.DrawLine(penCrossYellow, pf.X - 15, pf.Y, pf.X + 15, pf.Y);
            graphics.DrawLine(penCrossYellow, pf.X, pf.Y - 15, pf.X, pf.Y + 15);
            graphics.DrawLine(penCrossRed, pf.X - 15, pf.Y, pf.X + 15, pf.Y);
            graphics.DrawLine(penCrossRed, pf.X, pf.Y - 15, pf.X, pf.Y + 15);
        }

        public DbPoint GetGSMAntennaEndPoint(Cell cell, double x, double y)
        {
            if (cell.Antennas.Count == 0 || x <= 0 || y <= 0)
            {
                return new DbPoint(cell.Longitude, cell.Latitude);
            }
            else
            {
                int index = 0;
                double dDistance = double.MaxValue;
                for (int i = 0; i < cell.Antennas.Count; i++)
                {
                    double dDistanceTmp = MathFuncs.GetDistance(cell.Antennas[i].EndPointLongitude, cell.Antennas[i].EndPointLatitude, x, y);
                    if (dDistanceTmp < dDistance)
                    {
                        dDistance = dDistanceTmp;
                        index = i;
                    }
                }
                return new DbPoint(cell.Antennas[index].EndPointLongitude, cell.Antennas[index].EndPointLatitude);
            }
        }

        public DbPoint GetTDAntennaEndPoint(TDCell cell, DateTime tpDateTime)
        {
            return new DbPoint(cell.Antenna.EndPointLongitude, cell.Antenna.EndPointLatitude);
        }

        public DbPoint GetWAntennaEndPoint(WCell cell, DateTime tpDateTime)
        {
            DbPoint dbPoint = null;
            foreach (WAntenna antenna in cell.Antennas)
            {
                if (antenna.ValidPeriod.Contains(tpDateTime))
                {
                    dbPoint = new DbPoint(antenna.EndPointLongitude, antenna.EndPointLatitude);
                    return dbPoint;
                }
            }
            return null;
        }

        public DbPoint GetLTEAntennaEndPoint(LTECell cell, double x, double y)
        {
            if (cell.Antennas.Count == 0 || x <= 0 || y <= 0 || cell.DirectionType == LTEAntennaDirectionType.Omni || cell.Type == LTEBTSType.Indoor)
            {
                return new DbPoint(cell.Longitude, cell.Latitude);
            }
            else
            {
                int index = 0;
                double dDistance = double.MaxValue;
                getAntennasEndPointDistance(cell, x, y, ref index, ref dDistance);
                if (dDistance > 50000)
                {
                    return new DbPoint(cell.Longitude, cell.Latitude);
                }
                else
                {
                    return new DbPoint(cell.Antennas[index].EndPointLongitude, cell.Antennas[index].EndPointLatitude);
                }

            }
        }

        private void getAntennasEndPointDistance(LTECell cell, double x, double y, ref int index, ref double dDistance)
        {
            for (int i = 0; i < cell.Antennas.Count; i++)
            {
                if (cell.Antennas[i].EndPointLongitude > 60 && cell.Antennas[i].EndPointLatitude > 10)
                {
                    double dDistanceTmp = MathFuncs.GetDistance(cell.Antennas[i].EndPointLongitude, cell.Antennas[i].EndPointLatitude, x, y);
                    if (dDistanceTmp < dDistance)
                    {
                        dDistance = dDistanceTmp;
                        index = i;
                    }
                }
            }
        }

        public DbPoint GetCDAntennaEndPoint(CDCell cell, DateTime tpDateTime)
        {
            DbPoint dbPoint = null;
            foreach (CDAntenna antenna in cell.Antennas)
            {
                if (antenna.ValidPeriod.Contains(tpDateTime))
                {
                    dbPoint = new DbPoint(antenna.EndPointLongitude, antenna.EndPointLatitude);
                    return dbPoint;
                }
            }
            return null;
        }

        private void drawCoverRangeLine(LinkedList<TestPoint> tps, Graphics g)
        {
            if (tps.Count == 0)
            {
                return;
            }
            ZTCellCoverageRangeAnaItem crItem = mainModel.CellCvrRngItem;
            DbPoint cellPos = null;
            if (crItem.CurCell is Cell)
            {
                Cell cell = crItem.CurCell as Cell;
                cellPos = GetGSMAntennaEndPoint(cell, 0, 0);
            }
            else if (crItem.CurCell is TDCell)
            {
                TDCell cell = crItem.CurCell as TDCell;
                cellPos = GetTDAntennaEndPoint(cell, cell.ValidPeriod.BeginTime);
            }
            else if (crItem.CurCell is LTECell)
            {
                LTECell cell = crItem.CurCell as LTECell;
                cellPos = GetLTEAntennaEndPoint(cell, 0, 0);
            }
            if (cellPos == null)
            {
                return;
            }
            PointF cellPF;
            gisAdapter.ToDisplay(cellPos, out cellPF);
            Pen sPen = new Pen(Color.Red);
            Pen nPen = new Pen(Color.Blue);
            foreach (TestPoint tp in tps)
            {
                Pen line = null;
                if (crItem.ServTpItem.TpList.Contains(tp))
                {
                    line = sPen;
                }
                else if (crItem.NbTpItem.TpList.Contains(tp))
                {
                    line = nPen;
                }
                if (line == null)
                {
                    continue;
                }
                PointF tpPF;
                gisAdapter.ToDisplay(new DbPoint(tp.Longitude, tp.Latitude), out tpPF);
                g.DrawLine(line, tpPF, cellPF);
            }
        }

        #region drawFlyLines
        private void drawFlyLines(LinkedList<TestPoint> sampledTestPoints, Graphics graphics, MapSerialInfo serialInfo)
        {
            foreach (TestPoint testPoint in sampledTestPoints)
            {
                DbPoint antennaDPoint = null;
                int cellID = 0;
                bool isScanMultiCell = false;   //用于标注是否需要拉线多个扫频小区，如果是多个，在局部进行处理

                setcellIDDbPointByTP(graphics, serialInfo, testPoint, ref antennaDPoint, ref cellID, ref isScanMultiCell);
                if (!isScanMultiCell && antennaDPoint != null)   ////不涉及绘制多个小区
                {
                    drawValidDistaceFlyLine(graphics, serialInfo, testPoint, antennaDPoint, cellID);
                }
            }
        }

        private void setcellIDDbPointByTP(Graphics graphics, MapSerialInfo serialInfo, TestPoint testPoint, ref DbPoint antennaDPoint, ref int cellID, ref bool isScanMultiCell)
        {
            if (testPoint is TestPointScan)
            {
                setDataByTestPointScan(testPoint, ref antennaDPoint, ref cellID);
            }
            else if (testPoint is TDTestPointSummary || testPoint is TDTestPointDetail)
            {
                setDataByTDTestPoint(testPoint, ref antennaDPoint, ref cellID);
            }
            else if (testPoint is WCDMATestPointSummary || testPoint is WCDMATestPointDetail)
            {
                setDataByWCDMATestPoint(testPoint, ref antennaDPoint, ref cellID);
            }
            else if (testPoint is LTETestPointDetail || testPoint is LTEFddTestPoint)
            {
                setDataByLTETestPoint(testPoint, ref antennaDPoint, ref cellID);
            }
            else if (testPoint is TestPointDetail)
            {
                setDataByTestPoint(testPoint, ref antennaDPoint, ref cellID);
            }
            else if (testPoint is ScanTestPoint_LTE || testPoint is ScanTestPoint_NBIOT)
            {
                setDataByLTEScanTestPoint(graphics, serialInfo, testPoint, ref antennaDPoint, ref cellID, ref isScanMultiCell);
            }
            else if (testPoint is ScanTestPoint_TD)
            {
                setDataByTDScanTestPoint(graphics, serialInfo, testPoint, ref antennaDPoint, ref cellID, ref isScanMultiCell);
            }
            else if (testPoint is ScanTestPoint_G)
            {
                setDataByGSMScanTestPoint(graphics, serialInfo, testPoint, ref antennaDPoint, ref cellID, ref isScanMultiCell);
            }
            else if (testPoint is SignalTestPoint)
            {
                setDataBySignalTestPoint(testPoint, ref antennaDPoint, ref cellID);
            }
            else if (testPoint is TestPoint_NR)
            {
                setDataByNRTestPoint(graphics, serialInfo, testPoint, ref antennaDPoint, ref cellID, ref isScanMultiCell);
            }
            else if (testPoint is ScanTestPoint_NR)
            {
                setDataByNRScanTestPoint(graphics, serialInfo, testPoint, ref antennaDPoint, ref cellID, ref isScanMultiCell);
            }
        }

        private void setDataByNRTestPoint(Graphics graphics, MapSerialInfo serialInfo, TestPoint testPoint, ref DbPoint antennaDPoint, ref int cellID, ref bool isScanMultiCell)
        {
            isScanMultiCell = true;
            MapForm mapForm = MainModel.GetInstance().MainForm.GetMapForm();

            if (mapForm.GetLTECellLayer().IsVisible)
            {
                LTECell lteCell = testPoint.GetMainCell_LTE();
                if (lteCell != null)
                {
                    antennaDPoint = GetLTEAntennaEndPoint(lteCell, testPoint.Longitude, testPoint.Latitude);
                    cellID = lteCell.ID;
                    drawValidDistaceFlyLine(graphics, serialInfo, testPoint, antennaDPoint, cellID);
                }
            }

            if (mapForm.GetNRCellLayer().IsVisible)
            {
                NRCell nrCell = testPoint.GetMainCell_NR();
                if (nrCell != null)
                {
                    antennaDPoint = GetNRAntennaEndPoint(nrCell, testPoint.Longitude, testPoint.Latitude);
                    cellID = nrCell.ID;
                    drawValidDistaceFlyLine(graphics, serialInfo, testPoint, antennaDPoint, cellID);
                }
            }
        }

        private void drawValidDistaceFlyLine(Graphics graphics, MapSerialInfo serialInfo, TestPoint testPoint, DbPoint antennaDPoint, int cellID)
        {
            if (mainModel.FlyLineDistLimit > 0)
            {
                double distance = MathFuncs.GetDistance(antennaDPoint.x, antennaDPoint.y, testPoint.Longitude, testPoint.Latitude);
                if (distance > mainModel.FlyLineDistLimit)
                {
                    return;
                }
            }
            drawCommonFlyLines(antennaDPoint, testPoint, cellID, serialInfo, graphics);
        }

        public DbPoint GetNRAntennaEndPoint(NRCell cell, double x, double y)
        {
            if (cell.Antennas.Count == 0 || x <= 0 || y <= 0 || cell.Antennas[0].DirectionType == NRAntennaDirectionType.Omni || cell.Type == NRBTSType.Indoor)
            {
                return new DbPoint(cell.Longitude, cell.Latitude);
            }
            else
            {
                int index = 0;
                double dDistance = double.MaxValue;
                getAntennasEndPointDistance(cell, x, y, ref index, ref dDistance);
                if (dDistance > 50000)
                {
                    return new DbPoint(cell.Longitude, cell.Latitude);
                }
                else
                {
                    return new DbPoint(cell.Antennas[index].EndPointLongitude, cell.Antennas[index].EndPointLatitude);
                }

            }
        }

        private void getAntennasEndPointDistance(NRCell cell, double x, double y, ref int index, ref double dDistance)
        {
            for (int i = 0; i < cell.Antennas.Count; i++)
            {
                if (cell.Antennas[i].EndPointLongitude > 60 && cell.Antennas[i].EndPointLatitude > 10)
                {
                    double dDistanceTmp = MathFuncs.GetDistance(cell.Antennas[i].EndPointLongitude, cell.Antennas[i].EndPointLatitude, x, y);
                    if (dDistanceTmp < dDistance)
                    {
                        dDistance = dDistanceTmp;
                        index = i;
                    }
                }
            }
        }

        private void setDataBySignalTestPoint(TestPoint testPoint, ref DbPoint antennaDPoint, ref int cellID)
        {
            LTECell lteCell = testPoint.GetMainCell() as LTECell;
            if (lteCell != null)
            {
                antennaDPoint = GetLTEAntennaEndPoint(lteCell, testPoint.Longitude, testPoint.Latitude);
                cellID = lteCell.ID;
            }
        }

        private void setDataByGSMScanTestPoint(Graphics graphics, MapSerialInfo serialInfo, TestPoint testPoint, ref DbPoint antennaDPoint, ref int cellID, ref bool isScanMultiCell)
        {
            if (IsScanByTop1)
            {
                Cell scanTop1Cell = testPoint.GetCell_GSMScan(0);
                if (scanTop1Cell != null)
                {
                    antennaDPoint = GetGSMAntennaEndPoint(scanTop1Cell, testPoint.Longitude, testPoint.Latitude);
                    cellID = scanTop1Cell.ID;
                }
            }
            else
            {
                isScanMultiCell = true;
                for (int i = 0; i < 50; i++)
                {
                    float? rxLev = (float?)testPoint["GSCAN_RxLev", i];
                    if (rxLev == null || rxLev < -120 || rxLev > -10 || rxLev < (float)ScanRxLevThreshold)
                    {
                        break;
                    }

                    Cell scanCell = testPoint.GetCell_GSMScan(i);
                    if (scanCell != null)
                    {
                        antennaDPoint = GetGSMAntennaEndPoint(scanCell, testPoint.Longitude, testPoint.Latitude);
                        cellID = scanCell.ID;
                        drawCommonFlyLines(antennaDPoint, testPoint, cellID, serialInfo, graphics);
                    }
                }
            }
        }

        private void setDataByTDScanTestPoint(Graphics graphics, MapSerialInfo serialInfo, TestPoint testPoint, ref DbPoint antennaDPoint, ref int cellID, ref bool isScanMultiCell)
        {
            if (IsScanByTop1)
            {
                TDCell scanTop1Cell = testPoint.GetCell_TDScan(0);
                if (scanTop1Cell != null)
                {
                    antennaDPoint = GetTDAntennaEndPoint(scanTop1Cell, testPoint.DateTime);
                    cellID = scanTop1Cell.ID;
                }
            }
            else
            {
                isScanMultiCell = true;
                for (int i = 0; i < 50; i++)
                {
                    float? rxLev = (float?)testPoint["TDS_PCCPCH_RSCP", i];
                    if (rxLev == null || rxLev < -120 || rxLev > -10 || rxLev < (float)ScanRxLevThreshold)
                    {
                        break;
                    }

                    TDCell scanCell = testPoint.GetCell_TDScan(i);
                    if (scanCell != null)
                    {
                        antennaDPoint = GetTDAntennaEndPoint(scanCell, testPoint.DateTime);
                        cellID = scanCell.ID;
                        drawCommonFlyLines(antennaDPoint, testPoint, cellID, serialInfo, graphics);
                    }
                }
            }
        }

        private void setDataByLTEScanTestPoint(Graphics graphics, MapSerialInfo serialInfo, TestPoint testPoint, ref DbPoint antennaDPoint, ref int cellID, ref bool isScanMultiCell)
        {
            if (IsScanByTop1)
            {
                LTECell scanTop1Cell = testPoint.GetCell_LTEScan(0);
                if (scanTop1Cell != null)
                {
                    antennaDPoint = GetLTEAntennaEndPoint(scanTop1Cell, testPoint.Longitude, testPoint.Latitude);
                    cellID = scanTop1Cell.ID;
                }
            }
            else
            {
                isScanMultiCell = true;
                for (int i = 0; i < 50; i++)
                {
                    float? rxLev = (float?)testPoint["LTESCAN_TopN_PSS_RP", i];
                    if (rxLev == null || rxLev < -120 || rxLev > -10 || rxLev < (float)ScanRxLevThreshold)
                    {
                        break;
                    }

                    LTECell scanCell = testPoint.GetCell_LTEScan(i);
                    if (scanCell != null)
                    {
                        antennaDPoint = GetLTEAntennaEndPoint(scanCell, testPoint.Longitude, testPoint.Latitude);
                        cellID = scanCell.ID;
                        drawCommonFlyLines(antennaDPoint, testPoint, cellID, serialInfo, graphics);
                    }
                }
            }
        }

        private void setDataByNRScanTestPoint(Graphics graphics, MapSerialInfo serialInfo, TestPoint testPoint, ref DbPoint antennaDPoint, ref int cellID, ref bool isScanMultiCell)
        {
            if (IsScanByTop1)
            {
                var scanTop1Cell = testPoint.GetCell_NRScan(0);
                if (scanTop1Cell != null)
                {
                    antennaDPoint = GetNRAntennaEndPoint(scanTop1Cell, testPoint.Longitude, testPoint.Latitude);
                    cellID = scanTop1Cell.ID;
                }
            }
            else
            {
                isScanMultiCell = true;
                Dictionary<int, int> groupDic = NRTpHelper.NrScanTpManager.GetCellMaxBeam(testPoint);
                foreach (var i in groupDic.Values)
                {
                    float? rsrp = NRTpHelper.NrScanTpManager.GetCellRsrp(testPoint, i, true);
                    if (rsrp == null || rsrp < (float)ScanRxLevThreshold)
                    {
                        break;
                    }

                    var scanCell = testPoint.GetCell_NRScan(i);
                    if (scanCell != null)
                    {
                        antennaDPoint = GetNRAntennaEndPoint(scanCell, testPoint.Longitude, testPoint.Latitude);
                        cellID = scanCell.ID;
                        drawCommonFlyLines(antennaDPoint, testPoint, cellID, serialInfo, graphics);
                    }
                }
            }
        }

        private void setDataByTestPoint(TestPoint testPoint, ref DbPoint antennaDPoint, ref int cellID)
        {
            Cell cell = testPoint.GetMainCell_GSM();
            if (cell != null)
            {
                antennaDPoint = GetGSMAntennaEndPoint(cell, testPoint.Longitude, testPoint.Latitude);
                cellID = cell.ID;
            }
        }

        private void setDataByLTETestPoint(TestPoint testPoint, ref DbPoint antennaDPoint, ref int cellID)
        {
            ICell cell = testPoint.GetMainCell();
            if (cell is LTECell)
            {
                setDataByLTECell(testPoint, ref antennaDPoint, ref cellID, cell);
            }
            else if (cell is TDCell)
            {
                antennaDPoint = GetTDAntennaEndPoint(cell as TDCell, testPoint.DateTime);
                cellID = cell.ID;
            }
            else if (cell is WCell)
            {
                antennaDPoint = GetWAntennaEndPoint(cell as WCell, testPoint.DateTime);
                cellID = cell.ID;
            }
            else if (cell is Cell)
            {
                antennaDPoint = GetGSMAntennaEndPoint(cell as Cell, testPoint.Longitude, testPoint.Latitude);
                cellID = cell.ID;
            }
        }

        private void setDataByLTECell(TestPoint testPoint, ref DbPoint antennaDPoint, ref int cellID, ICell cell)
        {
            LTECell lCell = cell as LTECell;
            if (lCell.Antennas.Count > 1)
            {
                double dis = double.MaxValue;
                LTEAntenna nearestAnt = null;
                foreach (LTEAntenna ant in lCell.Antennas)
                {
                    double temp = MathFuncs.GetDistance(testPoint.Longitude, testPoint.Latitude
                        , ant.Longitude, ant.Latitude);
                    if (nearestAnt == null || temp < dis)
                    {
                        dis = temp;
                        nearestAnt = ant;
                    }
                }
                if (nearestAnt != null)
                {
                    antennaDPoint = new DbPoint(nearestAnt.EndPointLongitude, nearestAnt.EndPointLatitude);
                }
                cellID = lCell.ID;
            }
            else if (lCell.Antennas.Count == 1)
            {
                antennaDPoint = GetLTEAntennaEndPoint(lCell, testPoint.Longitude, testPoint.Latitude);
                cellID = lCell.ID;
            }
        }

        private void setDataByWCDMATestPoint(TestPoint testPoint, ref DbPoint antennaDPoint, ref int cellID)
        {
            WCell wCell = testPoint.GetMainCell_W();
            if (wCell != null)
            {
                antennaDPoint = GetWAntennaEndPoint(wCell, testPoint.DateTime);
                cellID = wCell.ID;
            }
        }

        private void setDataByTDTestPoint(TestPoint testPoint, ref DbPoint antennaDPoint, ref int cellID)
        {
            Cell cell = null;
            TDCell tdCell = null;
            testPoint.GetMainCell_TD(out tdCell, out cell);

            if (tdCell != null)
            {
                antennaDPoint = GetTDAntennaEndPoint(tdCell, testPoint.DateTime);
                cellID = tdCell.ID;
            }
            else if (cell != null)
            {
                antennaDPoint = GetGSMAntennaEndPoint(cell, testPoint.Longitude, testPoint.Latitude);
                cellID = cell.ID;
            }
        }

        private void setDataByTestPointScan(TestPoint testPoint, ref DbPoint antennaDPoint, ref int cellID)
        {
            Cell cell = testPoint.GetCell_TestPointScan(true, 0);
            if (cell != null)
            {
                antennaDPoint = GetGSMAntennaEndPoint(cell, testPoint.Longitude, testPoint.Latitude);
                cellID = cell.ID;
            }
        }
        #endregion

        public int Opacity { get; set; } = 255;
        private void drawCommonFlyLines(DbPoint antennaDPoint, TestPoint testPoint, int cellID, MapSerialInfo serialInfo, Graphics graphics)
        {
            if (antennaDPoint != null)
            {
                if (antennaDPoint.x < 60 || antennaDPoint.y < 10)
                {
                    return;
                }
                PointF antennaPointF;
                gisAdapter.ToDisplay(antennaDPoint, out antennaPointF);

                Color antennaColor = getAntennaColor(testPoint, cellID, serialInfo);

                DbPoint testDPoint = new DbPoint(testPoint.Longitude, testPoint.Latitude);
                PointF testPointF;
                gisAdapter.ToDisplay(testDPoint, out testPointF);
                graphics.DrawLine(new Pen(antennaColor, 1), testPointF, antennaPointF);
            }
        }

        private Color getAntennaColor(TestPoint testPoint, int cellID, MapSerialInfo serialInfo)
        {
            Color antennaColor = Color.Empty;
            if (this.IsBySerials)
            {
                Color? color;
                int? size;
                int? symbol;
                if (serialInfo != null && serialInfo.getStyle(this, testPoint, out color, out size, out symbol) && color != null)
                {
                    antennaColor = (Color)color;
                    antennaColor = Color.FromArgb(Opacity, antennaColor);
                }
            }
            else
            {
                if (FlyColorFromOrig)
                {
                    int clrIdx = 0;
                    if (!flyCellColorIdxDic.TryGetValue(cellID, out clrIdx))
                    {
                        flyCellColorIdxDic[cellID] = flyCellColorIdxDic.Count;
                    }
                    antennaColor = ColorSequenceSupplier.getColor(clrIdx);
                    antennaColor = Color.FromArgb(Opacity, antennaColor);
                }
                else
                {
                    antennaColor = ColorSequenceSupplier.getColor(cellID);
                    antennaColor = Color.FromArgb(Opacity, antennaColor);
                }
            }

            return antennaColor;
        }

        Pen measurePen = new Pen(new SolidBrush(Color.Red), 2);
        public Pen MeasurePen
        {
            get { return measurePen; }
        }

        public XmlDocument MakeXMLHead(out XmlElement DoumentNode)//生成KML文件头
        {
            XmlDocument doc = new XmlDocument();
            doc.AppendChild(doc.CreateXmlDeclaration("1.0", "UTF-8", null));
            XmlElement kmlElem = doc.CreateElement("kml");
            kmlElem.SetAttribute("xmlns", @"http://earth.google.com/kml/2.0");
            doc.AppendChild(kmlElem);
            XmlElement docElem = doc.CreateElement("Document");
            kmlElem.AppendChild(docElem);
            DoumentNode = docElem;
            return doc;
        }

        public override void mapForm_MapFeatureSelecting(object sender, EventArgs e)
        {
            MapForm mf = sender as MapForm;
            Select(((MapForm.MapEventArgs)e).MapOp2, mf.SelectedTestPoints, mf.SelectedEvents);
        }

        public void Select(MapOperation2 mop2, List<TestPoint> selectedTestPoints, List<Event> selectedEvents)
        {
            float ratio = CustomDrawLayer.GetTestPointDisplayRatio(mapScale);

            foreach (MapSerialInfo serialInfo in DTLayerSerialManager.Instance.SelectedSerials)
            {
                serialInfo.Select(this, ratio, mop2, selectedTestPoints);
            }
            foreach (MapEventInfo eventInfo in EventInfos)
            {
                eventInfo.Select(this, ratio, mop2, selectedEvents);
            }
        }

        public int MakeShpFile(string filename, List<string> selItems, MapSerialInfo fillSerial)
        {
            Shapefile shpFile = new Shapefile();
            bool result = shpFile.CreateNewWithShapeID("", MapWinGIS.ShpfileType.SHP_POINT);
            if (!result)
            {
                MessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                return -1;
            }
            shpFile.DefaultDrawingOptions.SetDefaultPointSymbol(tkDefaultPointSymbol.dpsCircle);

            //---列
            int idIdx = 0;
            int fiColor = idIdx++;
            int fiFileID = idIdx++;
            int fiFileName = idIdx++;
            int fiDateTime = idIdx++;
            int fiCellName = idIdx++;
            int fiLongitude = idIdx++;
            int fiLatitude = idIdx++;
            int fiSpeachCodec = idIdx++;
            int fiPESQ_output = idIdx++;
            ShapeHelper.InsertNewField(shpFile, "Color", FieldType.INTEGER_FIELD, 10, 30, ref fiColor);
            ShapeHelper.InsertNewField(shpFile, "FileID", FieldType.INTEGER_FIELD, 10, 30, ref fiFileID);
            ShapeHelper.InsertNewField(shpFile, "FileName", FieldType.STRING_FIELD, 10, 30, ref fiFileName);
            ShapeHelper.InsertNewField(shpFile, "DateTime", FieldType.STRING_FIELD, 10, 30, ref fiDateTime);
            ShapeHelper.InsertNewField(shpFile, "CellName", FieldType.STRING_FIELD, 10, 30, ref fiCellName);
            ShapeHelper.InsertNewField(shpFile, "Longitude", FieldType.DOUBLE_FIELD, 10, 30, ref fiLongitude);
            ShapeHelper.InsertNewField(shpFile, "Latitude", FieldType.DOUBLE_FIELD, 10, 30, ref fiLatitude);
            ShapeHelper.InsertNewField(shpFile, "SpeachCodec", FieldType.STRING_FIELD, 10, 30, ref fiSpeachCodec);
            ShapeHelper.InsertNewField(shpFile, "PESQ_output", FieldType.DOUBLE_FIELD, 10, 30, ref fiPESQ_output);

            int dpNameIndex = 1;
            int dpNameBeginFieldId = idIdx;
            Dictionary<string, DTDisplayParameter> dpDic = new Dictionary<string, DTDisplayParameter>();
            foreach (MapSerialInfo serialInfo in DTLayerSerialManager.Instance.SelectedSerials)
            {
                if (serialInfo.Visible)
                {
                    DTDisplayParameter dp = serialInfo.ColorDisplayParam;
                    if (dp != null)
                    {
                        string strName = getRightString(serialInfo.Name);
                        dpDic[strName] = dp;

                        string curFieldName = strName + dpNameIndex;
                        int curFieldId = idIdx++;
                        if (dp.Parameter.Info.ValueType == DTParameterValueType.String)
                        {
                            ShapeHelper.InsertNewField(shpFile, curFieldName, FieldType.STRING_FIELD, 10, 30, ref curFieldId);
                        }
                        else
                        {
                            ShapeHelper.InsertNewField(shpFile, curFieldName, FieldType.DOUBLE_FIELD, 10, 30, ref curFieldId);
                        }
                        dpNameIndex++;
                    }
                }
            }

            int itemBeginFieldId = idIdx;
            List<string> nbCellCol = new List<string>();
            for (int i = 0; i < selItems.Count; i++)
            {
                string item = selItems[i];
                if (item.StartsWith("N_"))
                {
                    nbCellCol.Add(item);
                    continue;
                }
                string curFieldName = item;
                int curFieldId = idIdx++;
                DTParameter dtParam = DTParameterManager.GetInstance().GetParameter(item);
                if (dtParam.Info.ValueType == DTParameterValueType.String)
                {
                    ShapeHelper.InsertNewField(shpFile, curFieldName, FieldType.STRING_FIELD, 10, 30, ref curFieldId);
                }
                else
                {
                    ShapeHelper.InsertNewField(shpFile, curFieldName, FieldType.DOUBLE_FIELD, 10, 30, ref curFieldId);
                }
            }
            foreach (string colName in nbCellCol)
            {
                selItems.Remove(colName);
            }
            int nbCellBeginFieldId = idIdx;
            if (nbCellCol.Count > 0)
            {
                for (int i = 0; i < 6; i++)
                {
                    string curFieldName = "N_CellName_" + i;
                    int curFieldId = idIdx++;
                    ShapeHelper.InsertNewField(shpFile, curFieldName, FieldType.STRING_FIELD, 10, 30, ref curFieldId);

                    int nbIndex = 1;
                    foreach (string nbItem in nbCellCol)
                    {
                        string curFdName = nbItem + "_" + nbIndex;
                        int curFdId = idIdx++;
                        DTParameter dtParam = DTParameterManager.GetInstance().GetParameter(nbItem);
                        if (dtParam.Info.ValueType == DTParameterValueType.String)
                        {
                            ShapeHelper.InsertNewField(shpFile, curFdName, FieldType.STRING_FIELD, 10, 30, ref curFdId);
                        }
                        else
                        {
                            ShapeHelper.InsertNewField(shpFile, curFdName, FieldType.DOUBLE_FIELD, 10, 30, ref curFdId);
                        }
                        nbIndex++;
                    }
                }
            }
            //---

            if (MainModel.DTDataManager.FileDataManagers.Count > 0)
            {
                //---行              
                int shpIdx = 0;
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    if (MainModel.VisibleOffsetManager.IsFileVisble(fileDataManager.FileID))
                    {
                        foreach (TestPoint tp in fileDataManager.TestPoints)
                        {
                            if (tp.Latitude == 0.0 && tp.Longitude == 0.0)
                            {
                                continue;
                            }
                            if (MainModel.VisibleOffsetManager.IsProjectVisble(tp.ProjectType) && MainModel.VisibleOffsetManager.IsServiceVisble(tp.ServiceType))
                            {
                                try
                                {
                                    Color? color;
                                    int? size;
                                    int? symbol;
                                    if (!fillSerial.getStyle(this, tp, out color, out size, out symbol))
                                    {
                                        continue;
                                    }

                                    MapWinGIS.Shape spBase = new MapWinGIS.Shape();
                                    spBase.Create(ShpfileType.SHP_POINT);
                                    MapWinGIS.Point pt = new MapWinGIS.Point();
                                    pt.x = tp.Longitude;
                                    pt.y = tp.Latitude;
                                    uint oleColor = (uint)ColorTranslator.ToOle((Color)color);
                                    int j = 0;
                                    spBase.InsertPoint(pt, ref j);
                                    shpFile.EditInsertShape(spBase, ref shpIdx);
                                    shpFile.EditCellValue(fiColor, shpIdx, (int)oleColor);
                                    shpFile.EditCellValue(fiFileID, shpIdx, tp.FileID);
                                    shpFile.EditCellValue(fiFileName, shpIdx, tp.FileName);
                                    shpFile.EditCellValue(fiDateTime, shpIdx, tp.DateTime.ToString("yyyy-MM-dd HH:mm:ss"));
                                    string cellname = "";
                                    if (tp is TestPointDetail && tp["LAC"] != null && tp["CI"] != null)
                                    {
                                        Cell cell = CellManager.GetInstance().GetCell(tp.DateTime, (ushort)(int)tp["LAC"], (ushort)(int)tp["CI"]);
                                        if (cell != null)
                                        {
                                            cellname = cell.Name;
                                        }
                                    }
                                    shpFile.EditCellValue(fiCellName, shpIdx, cellname);
                                    shpFile.EditCellValue(fiLongitude, shpIdx, tp.Longitude);
                                    shpFile.EditCellValue(fiLatitude, shpIdx, tp.Latitude);
                                    string speechCodeName = "";
                                    object objSpc = tp["SpeechCodecName"];
                                    if (objSpc != null)
                                    {
                                        speechCodeName = objSpc.ToString();
                                    }
                                    shpFile.EditCellValue(fiSpeachCodec, shpIdx, speechCodeName);
                                    object objMos = tp["PESQ"];
                                    float pesqv = 255;
                                    if (objMos != null)
                                    {
                                        float.TryParse(objMos.ToString(), out pesqv);
                                    }
                                    shpFile.EditCellValue(fiPESQ_output, shpIdx, pesqv);

                                    int dpNameFieldId = dpNameBeginFieldId;
                                    foreach (string dpName in dpDic.Keys)
                                    {
                                        DTDisplayParameter dp = dpDic[dpName];
                                        object obj = tp[dp.Parameter];
                                        if (dp.Parameter.Info.ValueType == DTParameterValueType.String)
                                        {
                                            if (obj != null)
                                            {
                                                shpFile.EditCellValue(dpNameFieldId, shpIdx, obj.ToString());
                                            }
                                            else
                                            {
                                                shpFile.EditCellValue(dpNameFieldId, shpIdx, "");
                                            }
                                        }
                                        else
                                        {
                                            double retv;
                                            if (obj != null && double.TryParse(obj.ToString(), out retv))
                                            {
                                                shpFile.EditCellValue(dpNameFieldId, shpIdx, retv);
                                            }
                                            else
                                            {
                                                shpFile.EditCellValue(dpNameFieldId, shpIdx, double.NaN);
                                            }
                                        }
                                        dpNameFieldId++;
                                    }

                                    int itemFieldId = itemBeginFieldId;
                                    foreach (string item in selItems)
                                    {
                                        string tpitem = tp[item] == null ? "" : tp[item].ToString();
                                        DTParameter dtParam = DTParameterManager.GetInstance().GetParameter(item);
                                        if (dtParam.Info.ValueType == DTParameterValueType.String)
                                        {
                                            shpFile.EditCellValue(itemFieldId, shpIdx, tpitem);
                                        }
                                        else
                                        {
                                            double dVal;
                                            if (double.TryParse(tpitem, out dVal))
                                            {
                                                shpFile.EditCellValue(itemFieldId, shpIdx, dVal);
                                            }
                                            else
                                            {
                                                shpFile.EditCellValue(itemFieldId, shpIdx, double.NaN);
                                            }
                                        }
                                        itemFieldId++;
                                    }

                                    int nbCellFieldId = nbCellBeginFieldId;
                                    if (nbCellCol.Count > 0)
                                    {
                                        for (int i = 0; i < 6; i++)
                                        {
                                            string nbcellname = tp["N_CellName", i] == null ? "" : tp["N_CellName", i].ToString();
                                            shpFile.EditCellValue(nbCellFieldId, shpIdx, nbcellname);
                                            foreach (string nbItem in nbCellCol)
                                            {
                                                string nbitemCol = tp[nbItem, i] == null ? "" : tp[nbItem, i].ToString();
                                                DTParameter dtParam = DTParameterManager.GetInstance().GetParameter(nbItem);
                                                if (dtParam.Info.ValueType == DTParameterValueType.String)
                                                {
                                                    shpFile.EditCellValue(nbCellFieldId, shpIdx, nbitemCol);
                                                }
                                                else
                                                {
                                                    double dVal;
                                                    if (double.TryParse(nbitemCol, out dVal))
                                                    {
                                                        shpFile.EditCellValue(nbCellFieldId, shpIdx, dVal);
                                                    }
                                                    else
                                                    {
                                                        shpFile.EditCellValue(nbCellFieldId, shpIdx, double.NaN);
                                                    }
                                                }
                                                nbCellFieldId++;
                                            }
                                            nbCellFieldId++;
                                        }
                                    }
                                    shpIdx++;

                                }
                                catch (Exception ect)
                                {
                                    MessageBox.Show(ect.ToString());
                                    return -1;
                                }
                            }
                        }
                    }
                }
                ShapeHelper.DeleteShpFile(filename);
                try
                {
                    shpFile.SaveAs(filename, null);
                }
                catch (System.Exception ex)
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show("保存文件失败！" + ex.Message);
                    return -1;
                }
                finally
                {
                    shpFile.Close();
                }
                return 1;
            }
            else
            {
                return 0;
            }
        }

        /// <summary>
        /// 导出路测数据的shp文件，跟MakeShpFile方法只有shp文件的形状类型不同
        /// </summary>
        /// <param name="filename"></param>
        /// <param name="selItems"></param>
        /// <param name="fillSerial"></param>
        /// <returns></returns>
        public int MakeDTShpFile(string filename, List<string> selItems, MapSerialInfo fillSerial)
        {
            if (!ShapeHelper.DeleteShpFile(filename))
            {
                MessageBox.Show("文件：" + filename + " 已被其他程序占用。");
                return -1;
            }
            Shapefile shpFile = new Shapefile();
            bool result = shpFile.CreateNewWithShapeID("", MapWinGIS.ShpfileType.SHP_POLYGON);
            if (!result)
            {
                MessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                return -1;
            }
            shpFile.DefaultDrawingOptions.SetDefaultPointSymbol(tkDefaultPointSymbol.dpsCircle);

            //---列
            int idIdx = 0;
            int fiColor = idIdx++;
            int fiFileID = idIdx++;
            int fiFileName = idIdx++;
            int fiDateTime = idIdx++;
            int fiCellName = idIdx++;
            int fiLongitude = idIdx++;
            int fiLatitude = idIdx++;
            int fiSpeachCodec = idIdx++;
            int fiPESQ_output = idIdx++;
            ShapeHelper.InsertNewField(shpFile, "Color", FieldType.INTEGER_FIELD, 10, 30, ref fiColor);
            ShapeHelper.InsertNewField(shpFile, "FileID", FieldType.INTEGER_FIELD, 10, 30, ref fiFileID);
            ShapeHelper.InsertNewField(shpFile, "FileName", FieldType.STRING_FIELD, 10, 30, ref fiFileName);
            ShapeHelper.InsertNewField(shpFile, "DateTime", FieldType.STRING_FIELD, 10, 30, ref fiDateTime);
            ShapeHelper.InsertNewField(shpFile, "CellName", FieldType.STRING_FIELD, 10, 30, ref fiCellName);
            ShapeHelper.InsertNewField(shpFile, "Longitude", FieldType.DOUBLE_FIELD, 10, 30, ref fiLongitude);
            ShapeHelper.InsertNewField(shpFile, "Latitude", FieldType.DOUBLE_FIELD, 10, 30, ref fiLatitude);
            ShapeHelper.InsertNewField(shpFile, "SpeachCodec", FieldType.STRING_FIELD, 10, 30, ref fiSpeachCodec);
            ShapeHelper.InsertNewField(shpFile, "PESQ_output", FieldType.DOUBLE_FIELD, 10, 30, ref fiPESQ_output);

            int dpNameIndex = 1;
            int dpNameBeginFieldId = idIdx;
            Dictionary<string, DTDisplayParameter> dpDic = new Dictionary<string, DTDisplayParameter>();
            foreach (MapSerialInfo serialInfo in DTLayerSerialManager.Instance.SelectedSerials)
            {
                if (serialInfo.Visible)
                {
                    DTDisplayParameter dp = serialInfo.ColorDisplayParam;
                    if (dp != null)
                    {
                        string strName = getRightString(serialInfo.Name);
                        dpDic[strName] = dp;

                        string curFieldName = strName + dpNameIndex;
                        int curFieldId = idIdx++;
                        if (dp.Parameter.Info.ValueType == DTParameterValueType.String)
                        {
                            ShapeHelper.InsertNewField(shpFile, curFieldName, FieldType.STRING_FIELD, 10, 30, ref curFieldId);
                        }
                        else
                        {
                            ShapeHelper.InsertNewField(shpFile, curFieldName, FieldType.DOUBLE_FIELD, 10, 30, ref curFieldId);
                        }
                        dpNameIndex++;
                    }
                }
            }

            int itemBeginFieldId = idIdx;
            List<string> nbCellCol = new List<string>();
            for (int i = 0; i < selItems.Count; i++)
            {
                string item = selItems[i];
                if (item.StartsWith("N_"))
                {
                    nbCellCol.Add(item);
                    continue;
                }
                string curFieldName = item;
                int curFieldId = idIdx++;
                DTParameter dtParam = DTParameterManager.GetInstance().GetParameter(item);
                if (dtParam.Info.ValueType == DTParameterValueType.String)
                {
                    ShapeHelper.InsertNewField(shpFile, curFieldName, FieldType.STRING_FIELD, 10, 30, ref curFieldId);
                }
                else
                {
                    ShapeHelper.InsertNewField(shpFile, curFieldName, FieldType.DOUBLE_FIELD, 10, 30, ref curFieldId);
                }
            }
            foreach (string colName in nbCellCol)
            {
                selItems.Remove(colName);
            }
            int nbCellBeginFieldId = idIdx;
            if (nbCellCol.Count > 0)
            {
                for (int i = 0; i < 6; i++)
                {
                    string curFieldName = "N_CellName_" + i;
                    int curFieldId = idIdx++;
                    ShapeHelper.InsertNewField(shpFile, curFieldName, FieldType.STRING_FIELD, 10, 30, ref curFieldId);

                    int nbIndex = 1;
                    foreach (string nbItem in nbCellCol)
                    {
                        string curFdName = nbItem + "_" + nbIndex;
                        int curFdId = idIdx++;
                        DTParameter dtParam = DTParameterManager.GetInstance().GetParameter(nbItem);
                        if (dtParam.Info.ValueType == DTParameterValueType.String)
                        {
                            ShapeHelper.InsertNewField(shpFile, curFdName, FieldType.STRING_FIELD, 10, 30, ref curFdId);
                        }
                        else
                        {
                            ShapeHelper.InsertNewField(shpFile, curFdName, FieldType.DOUBLE_FIELD, 10, 30, ref curFdId);
                        }
                        nbIndex++;
                    }
                }
            }
            //---

            if (MainModel.DTDataManager.FileDataManagers.Count > 0)
            {
                //---行              
                int shpIdx = 0;
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    if (MainModel.VisibleOffsetManager.IsFileVisble(fileDataManager.FileID))
                    {
                        foreach (TestPoint tp in fileDataManager.TestPoints)
                        {
                            if (tp.Latitude == 0.0 && tp.Longitude == 0.0)
                            {
                                continue;
                            }
                            if (MainModel.VisibleOffsetManager.IsProjectVisble(tp.ProjectType) && MainModel.VisibleOffsetManager.IsServiceVisble(tp.ServiceType))
                            {
                                try
                                {
                                    Color? color;
                                    int? size;
                                    int? symbol;
                                    if (!fillSerial.getStyle(this, tp, out color, out size, out symbol))
                                    {
                                        continue;
                                    }

                                    uint oleColor = (uint)ColorTranslator.ToOle((Color)color);
                                    MapWinGIS.Shape spBase = ShapeHelper.CreateCircleShape(tp.Longitude, tp.Latitude, 0.00048775);
                                    shpFile.EditInsertShape(spBase, ref shpIdx);
                                    shpFile.EditCellValue(fiColor, shpIdx, (int)oleColor);
                                    shpFile.EditCellValue(fiFileID, shpIdx, tp.FileID);
                                    shpFile.EditCellValue(fiFileName, shpIdx, tp.FileName);
                                    shpFile.EditCellValue(fiDateTime, shpIdx, tp.DateTime.ToString("yyyy-MM-dd HH:mm:ss"));
                                    string cellname = "";
                                    if (tp is TestPointDetail && tp["LAC"] != null && tp["CI"] != null)
                                    {
                                        Cell cell = CellManager.GetInstance().GetCell(tp.DateTime, (ushort)(int)tp["LAC"], (ushort)(int)tp["CI"]);
                                        if (cell != null)
                                        {
                                            cellname = cell.Name;
                                        }
                                    }
                                    shpFile.EditCellValue(fiCellName, shpIdx, cellname);
                                    shpFile.EditCellValue(fiLongitude, shpIdx, tp.Longitude);
                                    shpFile.EditCellValue(fiLatitude, shpIdx, tp.Latitude);
                                    string speechCodeName = "";
                                    object objSpc = tp["SpeechCodecName"];
                                    if (objSpc != null)
                                    {
                                        speechCodeName = objSpc.ToString();
                                    }
                                    shpFile.EditCellValue(fiSpeachCodec, shpIdx, speechCodeName);
                                    object objMos = tp["PESQ"];
                                    float pesqv = 255;
                                    if (objMos != null)
                                    {
                                        float.TryParse(objMos.ToString(), out pesqv);
                                    }
                                    shpFile.EditCellValue(fiPESQ_output, shpIdx, pesqv);

                                    int dpNameFieldId = dpNameBeginFieldId;
                                    foreach (string dpName in dpDic.Keys)
                                    {
                                        DTDisplayParameter dp = dpDic[dpName];
                                        object obj = tp[dp.Parameter];
                                        if (dp.Parameter.Info.ValueType == DTParameterValueType.String)
                                        {
                                            if (obj != null)
                                            {
                                                shpFile.EditCellValue(dpNameFieldId, shpIdx, obj.ToString());
                                            }
                                            else
                                            {
                                                shpFile.EditCellValue(dpNameFieldId, shpIdx, "");
                                            }
                                        }
                                        else
                                        {
                                            double retv;
                                            if (obj != null && double.TryParse(obj.ToString(), out retv))
                                            {
                                                shpFile.EditCellValue(dpNameFieldId, shpIdx, retv);
                                            }
                                            else
                                            {
                                                shpFile.EditCellValue(dpNameFieldId, shpIdx, double.NaN);
                                            }
                                        }
                                        dpNameFieldId++;
                                    }

                                    int itemFieldId = itemBeginFieldId;
                                    foreach (string item in selItems)
                                    {
                                        string tpitem = tp[item] == null ? "" : tp[item].ToString();
                                        DTParameter dtParam = DTParameterManager.GetInstance().GetParameter(item);
                                        if (dtParam.Info.ValueType == DTParameterValueType.String)
                                        {
                                            shpFile.EditCellValue(itemFieldId, shpIdx, tpitem);
                                        }
                                        else
                                        {
                                            double dVal;
                                            if (double.TryParse(tpitem, out dVal))
                                            {
                                                shpFile.EditCellValue(itemFieldId, shpIdx, dVal);
                                            }
                                            else
                                            {
                                                shpFile.EditCellValue(itemFieldId, shpIdx, double.NaN);
                                            }
                                        }
                                        itemFieldId++;
                                    }

                                    int nbCellFieldId = nbCellBeginFieldId;
                                    if (nbCellCol.Count > 0)
                                    {
                                        for (int i = 0; i < 6; i++)
                                        {
                                            string nbcellname = tp["N_CellName", i] == null ? "" : tp["N_CellName", i].ToString();
                                            shpFile.EditCellValue(nbCellFieldId, shpIdx, nbcellname);
                                            foreach (string nbItem in nbCellCol)
                                            {
                                                string nbitemCol = tp[nbItem, i] == null ? "" : tp[nbItem, i].ToString();
                                                DTParameter dtParam = DTParameterManager.GetInstance().GetParameter(nbItem);
                                                if (dtParam.Info.ValueType == DTParameterValueType.String)
                                                {
                                                    shpFile.EditCellValue(nbCellFieldId, shpIdx, nbitemCol);
                                                }
                                                else
                                                {
                                                    double dVal;
                                                    if (double.TryParse(nbitemCol, out dVal))
                                                    {
                                                        shpFile.EditCellValue(nbCellFieldId, shpIdx, dVal);
                                                    }
                                                    else
                                                    {
                                                        shpFile.EditCellValue(nbCellFieldId, shpIdx, double.NaN);
                                                    }
                                                }
                                                nbCellFieldId++;
                                            }
                                            nbCellFieldId++;
                                        }
                                    }
                                    shpIdx++;

                                }
                                catch (Exception ect)
                                {
                                    MessageBox.Show(ect.ToString());
                                    return -1;
                                }
                            }
                        }
                    }
                }

                try
                {
                    shpFile.SaveAs(filename, null);
                }
                catch (System.Exception ex)
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show("保存文件失败！" + ex.Message);
                    return -1;
                }
                finally
                {
                    shpFile.Close();
                }
                return 1;
                //---

            }
            else
            {
                return 0;
            }
        }


        /// <summary>
        /// 导出路测数据shp文件的测试方向
        /// </summary>
        /// <param name="fileName"></param>
        public void MakeDTDirShpFile(string fileName)
        {
            Shapefile shpFile = new Shapefile();
            bool openRet = shpFile.Open(fileName, null);
            if (!openRet)
            {
                return;
            }

            int shpIndex = shpFile.NumShapes;
            foreach (ArrowMapElement ame in testPointArrowManager.MapElements)
            {
                PointF[] points = ShapeConverter.LineToPolygon(ame);
                MapWinGIS.Shape shape = new MapWinGIS.Shape();
                shape.Create(ShpfileType.SHP_POLYGON);
                for (int i = 0; i < points.Length; ++i)
                {
                    DbPoint dbPoint;
                    MapWinGIS.Point point = new MapWinGIS.Point();
                    int pointIndex = i;
                    gisAdapter.FromDisplay(points[i], out dbPoint);
                    point.x = dbPoint.x;
                    point.y = dbPoint.y;
                    shape.InsertPoint(point, ref pointIndex);
                }
                shpFile.StartEditingShapes(true, null);
                shpFile.EditInsertShape(shape, ref shpIndex);
                shpFile.StopEditingShapes(true, true, null);
                ++shpIndex;
            }
            shpFile.Save(null);
            shpFile.Close();
        }

        public int MakeTestDirLineShpFile(string fileName)
        {
            fileName = fileName.Substring(0, fileName.IndexOf(".")) + "_测试方向.shp";
            Shapefile shpFile = new Shapefile();
            int shapeIndex = 0;
            bool result = shpFile.CreateNewWithShapeID("", MapWinGIS.ShpfileType.SHP_POLYLINE);
            if (!result)
            {
                MessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                return -1;
            }

            foreach (ArrowMapElement ame in testPointArrowManager.MapElements)
            {
                if (!ame.BaseElement.Visible)
                {
                    continue;
                }
                PointF[] points = ame.BaseElement.GraphicsPath.PathPoints;
                MapWinGIS.Shape shape = new MapWinGIS.Shape();
                shape.Create(ShpfileType.SHP_POLYLINE);

                for (int i = 0; i < points.Length; ++i)
                {
                    int pointIndex = i;
                    MapWinGIS.Point point = new MapWinGIS.Point();
                    DbPoint tmpPoint;
                    gisAdapter.FromDisplay(points[i], out tmpPoint);
                    point.x = tmpPoint.x;
                    point.y = tmpPoint.y;
                    shape.InsertPoint(point, ref pointIndex);
                }

                shapeIndex = shpFile.NumShapes;
                shpFile.EditInsertShape(shape, ref shapeIndex);
            } // end foreach (ArrowMapElement ame in

            shpFile.SaveAs(fileName, null);
            shpFile.Close();
            return 1;
        }

        class FlyLineShpFile
        {
            public void Init(Shapefile shpFile)
            {
                int idIdx = 0;
                int fiColor = idIdx++;
                int fiFileName = idIdx++;
                int fiDateTime = idIdx++;
                int fiTPLongitude = idIdx++;
                int fiTPLatitude = idIdx++;
                int fiCellLongitude = idIdx++;
                int fiCellLatitude = idIdx++;
                int fiCellName = idIdx++;
                int fiCellCode = idIdx++;
                int fiLAC = idIdx++;
                int fiCI = idIdx++;
                int fiRxLev = idIdx++;
                int fiRxQual = idIdx;

                ShapeHelper.InsertNewField(shpFile, "Color", FieldType.INTEGER_FIELD, 10, 30, ref fiColor);
                ShapeHelper.InsertNewField(shpFile, "FileName", FieldType.STRING_FIELD, 10, 30, ref fiFileName);
                ShapeHelper.InsertNewField(shpFile, "DateTime", FieldType.STRING_FIELD, 10, 30, ref fiDateTime);
                ShapeHelper.InsertNewField(shpFile, "TestPointLongitude", FieldType.DOUBLE_FIELD, 10, 30, ref fiTPLongitude);
                ShapeHelper.InsertNewField(shpFile, "TestPointLatitude", FieldType.DOUBLE_FIELD, 10, 30, ref fiTPLatitude);
                ShapeHelper.InsertNewField(shpFile, "CellLongitude", FieldType.DOUBLE_FIELD, 10, 30, ref fiCellLongitude);
                ShapeHelper.InsertNewField(shpFile, "CellLatitude", FieldType.DOUBLE_FIELD, 10, 30, ref fiCellLatitude);
                ShapeHelper.InsertNewField(shpFile, "CellName", FieldType.STRING_FIELD, 10, 30, ref fiCellName);
                ShapeHelper.InsertNewField(shpFile, "CellCode", FieldType.STRING_FIELD, 10, 30, ref fiCellCode);
                ShapeHelper.InsertNewField(shpFile, "LAC", FieldType.INTEGER_FIELD, 10, 30, ref fiLAC);
                ShapeHelper.InsertNewField(shpFile, "CI", FieldType.INTEGER_FIELD, 10, 30, ref fiCI);
                ShapeHelper.InsertNewField(shpFile, "场强", FieldType.DOUBLE_FIELD, 10, 30, ref fiRxLev);
                ShapeHelper.InsertNewField(shpFile, "质量", FieldType.DOUBLE_FIELD, 10, 30, ref fiRxQual);

                Color = fiColor;
                FileName = fiFileName;
                DateTime = fiDateTime;
                TPLongitude = fiTPLongitude;
                TPLatitude = fiTPLatitude;
                CellLongitude = fiCellLongitude;
                CellLatitude = fiCellLatitude;
                CellName = fiCellName;
                CellCode = fiCellCode;
                LAC = fiLAC;
                CI = fiCI;
                RxLev = fiRxLev;
                RxQual = fiRxQual;
            }

            public int Color { get; set; }
            public int FileName { get; set; }
            public int DateTime { get; set; }
            public int TPLongitude { get; set; }
            public int TPLatitude { get; set; }
            public int CellLongitude { get; set; }
            public int CellLatitude { get; set; }
            public int CellName { get; set; }
            public int CellCode { get; set; }
            public int LAC { get; set; }
            public int CI { get; set; }
            public int RxLev { get; set; }
            public int RxQual { get; set; }
        }

        public int MakeFlyLineShpFile(string filename)
        {
            if (sampledTestPoints.Count == 0)
            {
                return 0;
            }

            if (IsBySerials && DTLayerSerialManager.Instance.FlyLineSerial == null)
            {
                MessageBox.Show("请先在地图上右键菜单，设置飞线类型。");
                return -1;
            }

            Shapefile shpFile = new Shapefile();
            bool result = shpFile.CreateNewWithShapeID("", MapWinGIS.ShpfileType.SHP_POLYLINE);
            if (!result)
            {
                MessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                return -1;
            }

            FlyLineShpFile flyLineShpFile = new FlyLineShpFile();
            flyLineShpFile.Init(shpFile);

            try
            {
                int shpIdx = -1;
                int pntIdx = 0;
                foreach (TestPoint testPoint in sampledTestPoints)
                {
                    ICell cell = testPoint.GetMainCell();
                    if (cell == null || cell is UnknowCell)
                    {
                        continue;
                    }
                    dealTPFlyLine(shpFile, flyLineShpFile, ref shpIdx, ref pntIdx, testPoint, cell);
                }

                ShapeHelper.DeleteShpFile(filename);
                shpFile.SaveAs(filename, null);
            }
            catch
            {
                return -1;
            }
            finally
            {
                shpFile.Close();

            }
            return 1;
        }

        private void dealTPFlyLine(Shapefile shpFile, FlyLineShpFile flyLineShpFile, ref int shpIdx, ref int pntIdx, TestPoint testPoint, ICell cell)
        {
            object rxLev = null;
            object rxQual = null;

            Color? color;
            int? size;
            int? symbol;
            if (IsBySerials)
            {
                DTLayerSerialManager.Instance.FlyLineSerial.getStyle(this, testPoint, out color, out size, out symbol);
            }
            else
            {
                color = ColorSequenceSupplier.getColor(cell.ID);
            }

            getRsrpSinr(testPoint, cell, ref rxLev, ref rxQual);

            MapWinGIS.Shape shape = new MapWinGIS.Shape();
            shape.Create(ShpfileType.SHP_POLYLINE);
            MapWinGIS.Point cellPnt = new MapWinGIS.Point();
            cellPnt.x = cell.EndPointLongitude;
            cellPnt.y = cell.EndPointLatitude;
            shape.InsertPoint(cellPnt, ref pntIdx);
            MapWinGIS.Point samplePnt = new MapWinGIS.Point();
            samplePnt.x = testPoint.Longitude;
            samplePnt.y = testPoint.Latitude;

            //超过5公里不绘制，避免工参错误引起飞线错误 added by wj
            if (MathFuncs.GetDistance(cellPnt.x, cellPnt.y, samplePnt.x, samplePnt.y) <= 5000)
            {
                shape.InsertPoint(samplePnt, ref pntIdx);
                shpIdx++;
                shpFile.EditInsertShape(shape, ref shpIdx);

                shpFile.EditCellValue(flyLineShpFile.Color, shpIdx, ((Color)color).ToArgb());
                shpFile.EditCellValue(flyLineShpFile.FileName, shpIdx, testPoint.FileName);
                shpFile.EditCellValue(flyLineShpFile.DateTime, shpIdx, testPoint.DateTimeStringWithMillisecond);
                shpFile.EditCellValue(flyLineShpFile.TPLongitude, shpIdx, testPoint.Longitude);
                shpFile.EditCellValue(flyLineShpFile.TPLatitude, shpIdx, testPoint.Latitude);
                shpFile.EditCellValue(flyLineShpFile.CellLongitude, shpIdx, cell.EndPointLongitude);
                shpFile.EditCellValue(flyLineShpFile.CellLatitude, shpIdx, cell.EndPointLatitude);
                shpFile.EditCellValue(flyLineShpFile.CellName, shpIdx, cell.Name);
                shpFile.EditCellValue(flyLineShpFile.CellCode, shpIdx, cell.Code);
                setCellFlyLineValue(shpFile, flyLineShpFile, shpIdx, cell);
                shpFile.EditCellValue(flyLineShpFile.RxLev, shpIdx, rxLev == null ? double.NaN : double.Parse(rxLev.ToString()));
                shpFile.EditCellValue(flyLineShpFile.RxQual, shpIdx, rxQual == null ? double.NaN : double.Parse(rxQual.ToString()));
            }
        }

        private readonly Dictionary<Type, int> testPointTypeDic = new Dictionary<Type, int>
            {
                { typeof(TestPointScan), 0 },
                { typeof(TDTestPointSummary), 1 },
                { typeof(TDTestPointDetail), 2 },
                { typeof(LTETestPointDetail), 3 },
                { typeof(ScanTestPoint_LTE), 4 },
                { typeof(ScanTestPoint_NBIOT), 5 },
                { typeof(WCDMATestPointSummary), 6 },
                { typeof(WCDMATestPointDetail), 7 },
                { typeof(CDMATestPointDetail), 8 },
                { typeof(CDMATestPointSummary), 9 },
                { typeof(ScanTestPoint_G), 10 },
                { typeof(ScanTestPoint_TD), 11 },
                { typeof(TestPoint_NR), 12 }
            };

        private void getRsrpSinr(TestPoint testPoint, ICell cell, ref object rxLev, ref object rxQual)
        {
            Type type = testPoint.GetType();
            int index;
            if (testPointTypeDic.TryGetValue(type, out index))
            {
                switch (index)
                {
                    case 0:
                        rxLev = testPoint["SCAN_RxLev", 0];
                        rxQual = testPoint["SCAN_RxQual", 0];
                        break;
                    case 1:
                    case 2:
                        if (cell is TDCell)
                        {
                            rxLev = testPoint["TD_PCCPCH_RSCP"];
                        }
                        else if (cell is Cell)
                        {
                            rxLev = testPoint["TD_GSM_RxlevSub"];
                        }
                        break;
                    case 3:
                        switch (testPoint.NetworkType)
                        {
                            case TestPoint.ECurrNetType.GSM:
                                rxLev = testPoint["lte_gsm_DM_RxLevSub"];
                                rxQual = testPoint["lte_gsm_DM_RxQualSub"];
                                break;
                            case TestPoint.ECurrNetType.TD:
                                rxLev = testPoint["lte_td_DM_PCCPCH_RSCP"];
                                rxQual = testPoint["lte_td_DM_PCCPCH_SIR"];
                                break;
                            case TestPoint.ECurrNetType.LTE:
                                rxLev = testPoint["lte_RSRP"];
                                rxQual = testPoint["lte_SINR"];
                                break;
                            default:
                                break;
                        }
                        break;
                    case 4:
                    case 5:
                        rxLev = testPoint["LTESCAN_TopN_CELL_Specific_RSRP"];
                        rxQual = testPoint["LTESCAN_TopN_CELL_Specific_RSSINR"];
                        break;
                    case 6:
                    case 7:
                        rxLev = testPoint["W_Reference_RSCP"];
                        break;
                    case 8:
                    case 9:
                        rxLev = testPoint["CD_RX_Power"];
                        break;
                    case 10:
                        rxLev = testPoint["GSCAN_RxLev", 0];
                        rxQual = testPoint["GSCAN_RxQual", 0];
                        break;
                    case 11:
                        rxLev = testPoint["TDS_PCCPCH_RSCP"];
                        rxQual = testPoint["TDS_PCCPCH_C_I"];
                        break;
                    case 12:
                        rxLev = testPoint["NR_SS_RSRP"];
                        rxQual = testPoint["NR_SS_SINR"];
                        break;
                    default:
                        rxLev = testPoint["RxLevSub"];
                        rxQual = testPoint["RxQualSub"];
                        break;
                }
            }
        }

        private void setCellFlyLineValue(Shapefile shpFile, FlyLineShpFile flyLineShpFile, int shpIdx, ICell cell)
        {
            if (cell is Cell)
            {
                shpFile.EditCellValue(flyLineShpFile.LAC, shpIdx, (cell as Cell).LAC);
                shpFile.EditCellValue(flyLineShpFile.CI, shpIdx, (cell as Cell).CI);
            }
            else if (cell is TDCell)
            {
                shpFile.EditCellValue(flyLineShpFile.LAC, shpIdx, (cell as TDCell).LAC);
                shpFile.EditCellValue(flyLineShpFile.CI, shpIdx, (cell as TDCell).CI);
            }
            else if (cell is LTECell)
            {
                shpFile.EditCellValue(flyLineShpFile.LAC, shpIdx, (cell as LTECell).TAC);
                shpFile.EditCellValue(flyLineShpFile.CI, shpIdx, (cell as LTECell).ECI);
            }
            else if (cell is WCell)
            {
                shpFile.EditCellValue(flyLineShpFile.LAC, shpIdx, (cell as WCell).LAC);
                shpFile.EditCellValue(flyLineShpFile.CI, shpIdx, (cell as WCell).CI);
            }
            else if (cell is CDCell)
            {
                shpFile.EditCellValue(flyLineShpFile.LAC, shpIdx, (cell as CDCell).LAC);
                shpFile.EditCellValue(flyLineShpFile.CI, shpIdx, (cell as CDCell).CI);
            }
        }

        class EventShpFile
        {
            public void Init(Shapefile shpFile)
            {
                int idIdx = 0;
                int fFNId = idIdx++;
                int fSNId = idIdx++;
                int fEvtIdId = idIdx++;
                int fEvtNameId = idIdx++;
                int fLacId = idIdx++;
                int fCiId = idIdx++;
                int fTLacId = idIdx++;
                int fTCiId = idIdx++;
                int fLongId = idIdx++;
                int fLatId = idIdx++;
                int fDateTimeId = idIdx++;
                int fMsId = idIdx++;
                int fArea = idIdx++;
                int fRoad = idIdx;

                ShapeHelper.InsertNewField(shpFile, "FileName", FieldType.STRING_FIELD, 10, 30, ref fFNId);
                ShapeHelper.InsertNewField(shpFile, "SN", FieldType.INTEGER_FIELD, 10, 30, ref fSNId);
                ShapeHelper.InsertNewField(shpFile, "EventID", FieldType.INTEGER_FIELD, 10, 30, ref fEvtIdId);
                ShapeHelper.InsertNewField(shpFile, "EventName", FieldType.STRING_FIELD, 10, 30, ref fEvtNameId);
                ShapeHelper.InsertNewField(shpFile, "LAC", FieldType.INTEGER_FIELD, 10, 30, ref fLacId);
                ShapeHelper.InsertNewField(shpFile, "CI", FieldType.INTEGER_FIELD, 10, 30, ref fCiId);
                ShapeHelper.InsertNewField(shpFile, "TargetLAC", FieldType.INTEGER_FIELD, 10, 30, ref fTLacId);
                ShapeHelper.InsertNewField(shpFile, "TargetCI", FieldType.INTEGER_FIELD, 10, 30, ref fTCiId);
                ShapeHelper.InsertNewField(shpFile, "Longitude", FieldType.DOUBLE_FIELD, 10, 30, ref fLongId);
                ShapeHelper.InsertNewField(shpFile, "Latitude", FieldType.DOUBLE_FIELD, 10, 30, ref fLatId);
                ShapeHelper.InsertNewField(shpFile, "DateTime", FieldType.STRING_FIELD, 10, 30, ref fDateTimeId);
                ShapeHelper.InsertNewField(shpFile, "MS", FieldType.INTEGER_FIELD, 10, 30, ref fMsId);
                ShapeHelper.InsertNewField(shpFile, "AgentArea", FieldType.STRING_FIELD, 10, 30, ref fArea);
                ShapeHelper.InsertNewField(shpFile, "Road", FieldType.STRING_FIELD, 10, 30, ref fRoad);

                FNId = fFNId;
                SNId = fSNId;
                EvtIdId = fEvtIdId;
                EvtNameId = fEvtNameId;
                LacId = fLacId;
                CiId = fCiId;
                TLacId = fTLacId;
                TCiId = fTCiId;
                LongId = fLongId;
                LatId = fLatId;
                DateTimeId = fDateTimeId;
                MsId = fMsId;
                Area = fArea;
                Road = fRoad;
            }

            public int FNId { get; set; }
            public int SNId { get; set; }
            public int EvtIdId { get; set; }
            public int EvtNameId { get; set; }
            public int LacId { get; set; }
            public int CiId { get; set; }
            public int TLacId { get; set; }
            public int TCiId { get; set; }
            public int LongId { get; set; }
            public int LatId { get; set; }
            public int DateTimeId { get; set; }
            public int MsId { get; set; }
            public int Area { get; set; }
            public int Road { get; set; }
        }

        public int MakeEventShpFile(string filename)
        {
            if (this.EventInfos.Count > 0)
            {
                Shapefile shpFile = new Shapefile();
                bool result = shpFile.CreateNewWithShapeID("", MapWinGIS.ShpfileType.SHP_POINT);
                if (!result)
                {
                    MessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                    return -1;
                }
                shpFile.DefaultDrawingOptions.SetDefaultPointSymbol(tkDefaultPointSymbol.dpsCircle);

                EventShpFile evtShpFile = new EventShpFile();
                evtShpFile.Init(shpFile);

                try
                {
                    int shpIdx = 0;
                    foreach (MapEventInfo mapEventInfo in this.EventInfos)
                    {
                        if (!mapEventInfo.Visible)
                        {
                            continue;
                        }
                        shpIdx = dealEvtShpFile(shpFile, evtShpFile, shpIdx, mapEventInfo);
                    }
                    ShapeHelper.DeleteShpFile(filename);
                    shpFile.SaveAs(filename, null);
                    shpFile.Close();
                    return 1;
                }
                catch
                {
                    return -1;
                }
            }
            else
            {
                return 0;
            }
        }

        private int dealEvtShpFile(Shapefile shpFile, EventShpFile evtShpFile, int shpIdx, MapEventInfo mapEventInfo)
        {
            foreach (DTFileDataManager fileDataManager in this.MainModel.DTDataManager.FileDataManagers)
            {
                if (this.MainModel.VisibleOffsetManager.IsFileVisble(fileDataManager.FileID))
                {
                    foreach (Event e in fileDataManager.Events)
                    {
                        setShpFileCellValue(shpFile, evtShpFile, ref shpIdx, mapEventInfo, e);
                    }
                }
            }

            return shpIdx;
        }

        private void setShpFileCellValue(Shapefile shpFile, EventShpFile evtShpFile, ref int shpIdx, MapEventInfo mapEventInfo, Event e)
        {
            if (e.ID == mapEventInfo.EventID && (MainModel.GetInstance().CurMS == 0
                || e.MS == MainModel.GetInstance().CurMS))
            {
                bool isProjectVisble = MainModel.VisibleOffsetManager.IsProjectVisble(e.ProjectType);
                bool isServiceVisble = MainModel.VisibleOffsetManager.IsServiceVisble(e.ServiceType);
                if (isProjectVisble && isServiceVisble)
                {
                    MapWinGIS.Shape spBase = new MapWinGIS.Shape();
                    spBase.Create(ShpfileType.SHP_POINT);
                    MapWinGIS.Point pt = new MapWinGIS.Point();
                    pt.x = e.Longitude;
                    pt.y = e.Latitude;
                    int j = 0;
                    spBase.InsertPoint(pt, ref j);

                    shpFile.EditInsertShape(spBase, ref shpIdx);
                    shpFile.EditCellValue(evtShpFile.FNId, shpIdx, e.FileName);
                    shpFile.EditCellValue(evtShpFile.SNId, shpIdx, e.XHInFile);
                    shpFile.EditCellValue(evtShpFile.EvtIdId, shpIdx, mapEventInfo.EventID);
                    shpFile.EditCellValue(evtShpFile.EvtNameId, shpIdx, e.EventInfo.Name);
                    shpFile.EditCellValue(evtShpFile.LacId, shpIdx, e["LAC"]);
                    shpFile.EditCellValue(evtShpFile.CiId, shpIdx, e["CI"]);
                    shpFile.EditCellValue(evtShpFile.TLacId, shpIdx, e["TargetLAC"]);
                    shpFile.EditCellValue(evtShpFile.TCiId, shpIdx, e["TargetCI"]);
                    shpFile.EditCellValue(evtShpFile.LongId, shpIdx, e.Longitude);
                    shpFile.EditCellValue(evtShpFile.LatId, shpIdx, e.Latitude);
                    shpFile.EditCellValue(evtShpFile.DateTimeId, shpIdx, e.DateTimeStringWithMillisecond);
                    shpFile.EditCellValue(evtShpFile.MsId, shpIdx, e.MS);
                    shpFile.EditCellValue(evtShpFile.Area, shpIdx, e.AgentAreaName);
                    shpFile.EditCellValue(evtShpFile.Road, shpIdx, e.RoadPlaceDesc);
                    shpIdx++;
                }
            }
        }

        public int MakeSingleCovCellShpFile(string filename)
        {
            if (mainModel.CovCellShowSetting != null)
            {
                if (mainModel.CovCellShowSetting.tpCellMode != showSetting.CellMode.bySingle)
                    return -1;

                Shapefile shpFile = new Shapefile();
                bool result = shpFile.CreateNewWithShapeID("", MapWinGIS.ShpfileType.SHP_POINT);
                if (!result)
                {
                    MessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                    return -1;
                }
                shpFile.DefaultDrawingOptions.SetDefaultPointSymbol(tkDefaultPointSymbol.dpsCircle);

                //---列
                int idIdx = 0;
                int fiFileName = idIdx++;
                int fiDateTime = idIdx++;
                int fiCellName = idIdx++;
                int fiCellLongitude = idIdx++;
                int fiCellLatitude = idIdx++;
                int fiRxLev = idIdx++;
                int fiN_RxLev = idIdx;
                ShapeHelper.InsertNewField(shpFile, "FileName", FieldType.STRING_FIELD, 10, 30, ref fiFileName);
                ShapeHelper.InsertNewField(shpFile, "DateTime", FieldType.STRING_FIELD, 10, 30, ref fiDateTime);
                ShapeHelper.InsertNewField(shpFile, "CellName", FieldType.STRING_FIELD, 10, 30, ref fiCellName);
                ShapeHelper.InsertNewField(shpFile, "CellLongitude", FieldType.DOUBLE_FIELD, 10, 30, ref fiCellLongitude);
                ShapeHelper.InsertNewField(shpFile, "CellLatitude", FieldType.DOUBLE_FIELD, 10, 30, ref fiCellLatitude);
                ShapeHelper.InsertNewField(shpFile, "RxLev", FieldType.INTEGER_FIELD, 10, 30, ref fiRxLev);
                ShapeHelper.InsertNewField(shpFile, "n_RxLev", FieldType.INTEGER_FIELD, 10, 30, ref fiN_RxLev);
                try
                {
                    int shpIdx = -1;
                    foreach (TestPoint tp in mainModel.CovCellShowSetting.tpSingleCell_maincell.tpList)
                    {
                        int pntIdx = 0;
                        MapWinGIS.Shape shape = new MapWinGIS.Shape();
                        shape.Create(ShpfileType.SHP_POINT);
                        MapWinGIS.Point pt = new MapWinGIS.Point();
                        pt.x = tp.Longitude;
                        pt.y = tp.Latitude;
                        shape.InsertPoint(pt, ref pntIdx);
                        shpIdx++;
                        shpFile.EditInsertShape(shape, ref shpIdx);

                        shpFile.EditCellValue(fiFileName, shpIdx, tp.FileName);
                        shpFile.EditCellValue(fiDateTime, shpIdx, tp.DateTime);
                        shpFile.EditCellValue(fiCellName, shpIdx, mainModel.CovCellShowSetting.tpSingleCell_maincell.Name);
                        shpFile.EditCellValue(fiCellLongitude, shpIdx, tp.Longitude);
                        shpFile.EditCellValue(fiCellLatitude, shpIdx, tp.Latitude);

                        setMainCellRsrp(shpFile, fiRxLev, shpIdx, tp);
                    }

                    foreach (TestPoint tp in mainModel.CovCellShowSetting.tpSingleCell_maincell.tpListAsNbcell)
                    {
                        int pntIdx = 0;
                        MapWinGIS.Shape shape = new MapWinGIS.Shape();
                        shape.Create(ShpfileType.SHP_POINT);
                        MapWinGIS.Point pt = new MapWinGIS.Point();
                        pt.x = tp.Longitude;
                        pt.y = tp.Latitude;
                        shape.InsertPoint(pt, ref pntIdx);
                        shpIdx++;
                        shpFile.EditInsertShape(shape, ref shpIdx);

                        shpFile.EditCellValue(fiFileName, shpIdx, tp.FileName);
                        shpFile.EditCellValue(fiDateTime, shpIdx, tp.DateTime);
                        shpFile.EditCellValue(fiCellName, shpIdx, mainModel.CovCellShowSetting.tpSingleCell_maincell.Name);
                        shpFile.EditCellValue(fiCellLongitude, shpIdx, tp.Longitude);
                        shpFile.EditCellValue(fiCellLatitude, shpIdx, tp.Latitude);

                        setNBCellRsrp(shpFile, fiN_RxLev, shpIdx, tp);
                    }

                    shpFile.SaveAs(filename, null);
                    shpFile.Close();
                    return 1;
                }
                catch
                {
                    return -1;
                }
            }
            else
            {
                return 0;
            }
        }

        private void setMainCellRsrp(Shapefile shpFile, int fiRxLev, int shpIdx, TestPoint tp)
        {
            if (tp is TestPointDetail)
            {
                shpFile.EditCellValue(fiRxLev, shpIdx, tp["RxLevSub"]);
            }
            else if (tp is TDTestPointDetail)
            {
                shpFile.EditCellValue(fiRxLev, shpIdx, tp["TD_PCCPCH_RSCP"]);
            }
            else if (tp is LTETestPointDetail)
            {
                shpFile.EditCellValue(fiRxLev, shpIdx, tp["lte_RSRP"]);
            }
        }

        private void setNBCellRsrp(Shapefile shpFile, int fiN_RxLev, int shpIdx, TestPoint tp)
        {
            int nbI = -1;
            string snk = tp.SN.ToString() + tp.FileID.ToString() + tp.Time.ToString();
            if (mainModel.CovCellShowSetting.tpSingleCell_maincell.snNnIDic.ContainsKey(snk))
            {
                nbI = mainModel.CovCellShowSetting.tpSingleCell_maincell.snNnIDic[snk];
            }
            if (tp is TestPointDetail)
            {
                shpFile.EditCellValue(fiN_RxLev, shpIdx, tp["N_RxLev", nbI]);
            }
            else if (tp is TDTestPointDetail)
            {
                shpFile.EditCellValue(fiN_RxLev, shpIdx, tp["TD_NCell_PCCPCH_RSCP", nbI]);
            }
            else if (tp is LTETestPointDetail)
            {
                shpFile.EditCellValue(fiN_RxLev, shpIdx, tp["lte_NCell_RSRP", nbI]);
            }
        }

        Dictionary<int, VectorCodeColor> vectorStyleDic = new Dictionary<int, VectorCodeColor>();
        public Dictionary<int, VectorCodeColor> VectorStyleDic
        {
            get { return vectorStyleDic; }
        }
        Dictionary<string, int> vectorDic = new Dictionary<string, int>();
        public Dictionary<string, int> VectorDic
        {
            get { return vectorDic; }
        }
        public void GetVectorCode(string name, out int code, out Color color)
        {
            if (vectorDic.ContainsKey(name))
            {
                VectorCodeColor vectorStyle = vectorStyleDic[vectorDic[name]];
                code = vectorStyle.Code;
                color = vectorStyle.Color;
            }
            else
            {
                int codeTmp;
                int[] bad = { 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 54, 58, 59, 60, 61, 62, 63, 64 };
                List<int> badList = new List<int>();
                for (int i = 0; i < bad.Length; i++)
                {
                    badList.Add(bad[i]);
                }

                Random random = new Random(32);
                codeTmp = random.Next(32, 67);
                Color colorTmp = getColorByName(name);
                while (badList.Contains(codeTmp) || ExistsVectorStyle(codeTmp, colorTmp))
                {
                    codeTmp = random.Next(32, 67);
                }
                VectorCodeColor vectorCodeColor = new VectorCodeColor(vectorStyleDic.Count + 1, codeTmp, colorTmp);
                vectorStyleDic[vectorCodeColor.Id] = vectorCodeColor;
                vectorDic[name] = vectorCodeColor.Id;
                code = codeTmp;
                color = colorTmp;
            }
        }

        private Color getColorByName(string name)
        {
            Color colorTmp;
            if (name.Contains("_start") || name.Contains("_began"))
            {
                colorTmp = Color.Yellow;
            }
            else if (name.Contains("_fail") || name.Contains("_f") || name.Contains("_failure") || name.Contains("_timeout"))
            {
                colorTmp = Color.Red;
            }
            else if (name.Contains("_ok") || name.Contains("_success"))
            {
                colorTmp = Color.DeepSkyBlue;
            }
            else if (name.Contains("end") || name.Contains("ended"))
            {
                colorTmp = Color.DeepPink;
            }
            else
            {
                colorTmp = Color.Black;
            }

            return colorTmp;
        }

        private bool ExistsVectorStyle(int code, Color color)
        {
            foreach (VectorCodeColor Item in vectorStyleDic.Values)
            {
                if (code == Item.Code && color == Item.Color)
                {
                    return true;
                }
            }
            return false;
        }

        private string getRightString(string str) //将str中非字母数字下划线转成下划线
        {
            StringBuilder s = new StringBuilder();
            char[] resStr = new char[str.Length];
            for (int i = 0; i < str.Length; i++)
            {
                if ((str[i] >= 65 && str[i] <= 90) || (str[i] >= 97 && str[i] <= 122)
                    || (str[i] >= 48 && str[i] <= 57) || str[i].ToString().Equals("_"))
                {
                    resStr[i] = str[i];
                }
                else
                {
                    resStr[i] = (Char)95;
                }
            }
            for (int i = 0; i < resStr.Length; i++)
            {
                s.Append(resStr[i].ToString());
            }
            return s.ToString();
        }

        public void ExportKml(KMLExporter exporter, XmlElement parentElem)
        {
            XmlElement layerElement = exporter.CreateFolder(name, false);
            parentElem.AppendChild(layerElement);
            XmlElement serialInfosElement = exporter.CreateFolder("Serials", false);
            layerElement.AppendChild(serialInfosElement);
            foreach (MapSerialInfo serialInfo in DTLayerSerialManager.Instance.SelectedSerials)
            {
                serialInfo.ExportKml(this, exporter, serialInfosElement);
            }
            XmlElement eventInfosElement = exporter.CreateFolder("Events", false);
            layerElement.AppendChild(eventInfosElement);
            foreach (MapEventInfo eventInfo in EventInfos)
            {
                if (eventInfo.Event != null)
                {
                    eventInfo.ExportKml(this, exporter, eventInfosElement);
                }
            }
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                List<object> serialInfoParams = new List<object>();
                param["SerialInfos"] = serialInfoParams;
                foreach (MapSerialInfo serialInfo in DTLayerSerialManager.Instance.CustomSerials)
                {
                    serialInfoParams.Add(serialInfo.Param);
                }
                List<object> eventInfoParams = new List<object>();
                param["EventInfos"] = eventInfoParams;
                foreach (MapEventInfo eventInfo in EventInfos)
                {
                    eventInfoParams.Add(eventInfo.Param);
                }
                param["NeedSample"] = NeedSample;
                param["SampleLevel"] = SampleLevel;
                //own func define
                List<object> ownCommandersParams = new List<object>();
                param["ownFuncCommanders"] = ownCommandersParams;
                foreach (OwnFuncCommander commander in OwnFuncCommanderDic.Values)
                {
                    ownCommandersParams.Add(commander.Param);
                }
                return param;
            }
            set
            {
                try
                {
                    List<object> serialInfoParams = (List<object>)value["SerialInfos"];
                    List<MapSerialInfo> serialsList = new List<MapSerialInfo>();
                    foreach (object o in serialInfoParams)
                    {
                        Dictionary<string, object> serialInfoParam = (Dictionary<string, object>)o;
                        MapSerialInfo serialInfo = new MapSerialInfo();
                        serialInfo.Param = serialInfoParam;
                        if (serialInfo.OwnColorFuncDesc != null)
                        {
                            DTLayerSerialManager.Instance.AddCustomSerial(serialInfo);
                        }
                        serialsList.Add(serialInfo);
                    }
                    DTLayerSerialManager.Instance.AddSerialFinished(1);
                    FavoriteSerialConfig.Instance.AddOldSerials(serialsList);

                    List<object> eventInfoParams = (List<object>)value["EventInfos"];
                    EventInfos.Clear();
                    Dictionary<int, bool> eventIDDic = new Dictionary<int, bool>();
                    foreach (object o in eventInfoParams)
                    {
                        Dictionary<string, object> eventInfoParam = (Dictionary<string, object>)o;
                        MapEventInfo eventInfo = new MapEventInfo();
                        eventInfo.Param = eventInfoParam;
                        if (eventIDDic.ContainsKey(eventInfo.EventID))
                        {
                            continue;
                        }
                        EventInfos.Add(eventInfo);
                        eventIDDic[eventInfo.EventID] = true;
                    }
                    foreach (EventInfo evt in EventInfoManager.GetInstance().EventInfos)
                    {
                        if (evt.ID > 0 && !eventIDDic.ContainsKey(evt.ID))
                        {
                            MapEventInfo eventInfo = new MapEventInfo();
                            eventInfo.EventID = evt.ID;
                            EventInfos.Add(eventInfo);
                        }
                    }

                    NeedSample = (bool)value["NeedSample"];
                    SampleLevel = (int)value["SampleLevel"];
                    //own func define
                    OwnFuncCommanderDic = new Dictionary<string, OwnFuncCommander>();
                    object ownFuncParamObj;
                    if (value.TryGetValue("ownFuncCommanders", out ownFuncParamObj))
                    {
                        List<object> defParams = ownFuncParamObj as List<object>;
                        foreach (object o in defParams)
                        {
                            Dictionary<string, object> defParam = (Dictionary<string, object>)o;
                            OwnFuncCommander commander = new OwnFuncCommander();
                            commander.Param = defParam;
                            OwnFuncCommanderDic[commander.desc] = commander;
                        }
                    }
                }
                catch
                {
                    //continue
                }
            }
        }

        public string GetTestPointKMLDesctription(TestPoint testPoint)
        {
            System.Text.StringBuilder description = new System.Text.StringBuilder(); 
            if (testPoint != null)
            {
                description.Append("LAC:" + getTestPointParamString(testPoint, "GSM", "LAC", 0));
                description.Append("\n" + "CI:" + getTestPointParamString(testPoint, "GSM", "CI", 0));
                description.Append("\n" + "BCCH:" + getTestPointParamString(testPoint, "GSM", "BCCH", 0));
                description.Append("\n" + "BSIC:" + getTestPointParamString(testPoint, "GSM", "BSIC", 0));
                description.Append("\n" + "ModeName:" + getTestPointParamString(testPoint, "GSM", "ModeName", 0));
                description.Append("\n" + "SpeechCodecName:" + getTestPointParamString(testPoint, "GSM", "SpeechCodecName", 0));
                description.Append("\n" + "RxLevSub:" + getTestPointParamString(testPoint, "GSM", "RxLevSub", 0));
                description.Append("\n" + "RxQualSub:" + getTestPointParamString(testPoint, "GSM", "RxQualSub", 0));
                description.Append("\n" + "TA:" + getTestPointParamString(testPoint, "GSM", "TA", 0));
                description.Append("\n" + "FERSub:" + getTestPointParamString(testPoint, "GSM", "FERSub", 0));
                description.Append("\n" + "MsTxPower:" + getTestPointParamString(testPoint, "GSM", "MsTxPower", 0));
                for (int i = 0; i < 3; i++)
                {
                    description.Append("\n" + "C_I_ARFCN[" + i + "]:" + getTestPointParamString(testPoint, "GSM", "C_I_ARFCN", i));
                    description.Append("\n" + "C_I_Rxlev[" + i + "]:" + getTestPointParamString(testPoint, "GSM", "C_I_Rxlev", i));
                    description.Append("\n" + "C_I[" + i + "]:" + getTestPointParamString(testPoint, "GSM", "C_I", i));
                }
                for (int i = 0; i < 3; i++)
                {
                    description.Append("\n" + "N_BCCH[" + i + "]:" + getTestPointParamString(testPoint, "GSM", "N_BCCH", i));
                    description.Append("\n" + "N_BSIC[" + i + "]:" + getTestPointParamString(testPoint, "GSM", "N_BSIC", i));
                    description.Append("\n" + "N_RxLev[" + i + "]:" + getTestPointParamString(testPoint, "GSM", "N_RxLev", i));
                }
            }
            return description.ToString();
        }

        private string getTestPointParamString(TestPoint testPoint, string systemName, string paramName, int arrayIndex)
        {
            if (testPoint != null)
            {
                DTDisplayParameter displayParam = DTDisplayParameterManager.GetInstance()[systemName, paramName, arrayIndex];
                if (displayParam != null)
                {
                    DTParameter param = displayParam.Info.ParamInfo[displayParam.ArrayIndex];
                    object objectValue = testPoint[param];
                    if (objectValue != null)
                    {
                        return getValidParamString(displayParam, param, objectValue);
                    }
                }
            }
            return "";
        }

        private string getValidParamString(DTDisplayParameter displayParam, DTParameter param, object objectValue)
        {
            if (DTParameterManager.GetInstance().CanConvertToFloat(objectValue, param.Info.ValueType)
                && displayParam.Info.ValueMin != displayParam.Info.ValueMax)
            {
                float value = DTParameterManager.GetInstance().ConvertToFloat(objectValue, param.Info.ValueType);
                if (value < displayParam.Info.ValueMin || value > displayParam.Info.ValueMax)
                {
                    return "";
                }
            }
            return objectValue.ToString();
        }

        [NonSerialized()]
        private LinkedList<TestPoint> sampledTestPoints = new LinkedList<TestPoint>();

        public const int displayTestPointMaxCount = 10000;

        [NonSerialized()]
        private int[] scaleStandard = new int[] { 1, 2, 5, 10, 20, 50, 100, 200, 500, 1000, 2000, 5000, 10000, 20000, 50000, 100000, 200000, 500000, 1000000, 2000000, 5000000 };
        //比例尺标准值，单位米，从1米到5000公里

        [NonSerialized()]
        private Pen[] neighbourPens = new Pen[15];

        public Pen[] NeighbourPens
        {
            get { return neighbourPens; }
        }

        [NonSerialized()]
        private List<DbPoint> measurePoints = new List<DbPoint>();

        public List<DbPoint> MeasurePoints
        {
            get { return measurePoints; }
            set { measurePoints = value; }
        }

        [NonSerialized()]
        private DbPoint locationPoint = new DbPoint();
        public DbPoint LocationPoint
        {
            get { return locationPoint; }
            set { locationPoint = value; }
        }
        
        public bool DrawLocationPoint { get; set; } = false;

        internal Dictionary<string, OwnFuncCommander> OwnFuncCommanderDic { get; set; } = new Dictionary<string, OwnFuncCommander>();
        
        public Dictionary<Cell, Color> FlyCellsColorDic { get; set; } = new Dictionary<Cell, Color>();

        internal Color? GetColorFromOwnFunc(TestPoint testPoint, string ownColorFuncDesc, List<ColorRange> ownColorFuncRanges, string ownfuncParam)
        {
            if (ownColorFuncDesc == MultiParamColorTranslator.OwnFuncCommanderName)
            {
                return GetColorFromMultiParam(testPoint, ownColorFuncDesc, ownColorFuncRanges, ownfuncParam);
            }
            OwnFuncCommander commander = null;
            if (OwnFuncCommanderDic.TryGetValue(ownColorFuncDesc, out commander))
            {
                if (!commander._classReady && !commander._hasError)
                {
                    commander.initFuncClass_TestPoint();
                }
                if (commander._classReady)
                {
                    object[] paramObj = new object[2];
                    paramObj[0] = testPoint;
                    paramObj[1] = ownfuncParam;
                    object objClass = commander.clzzInst;
                    try
                    {
                        float fResult = (float)objClass.GetType().InvokeMember(
                                                               "GetFuncResult",
                                                               System.Reflection.BindingFlags.InvokeMethod, null, objClass,
                                                               paramObj);
                        return getColorFromRange(ownColorFuncRanges, fResult);
                    }
                    catch
                    {
                        return Color.Black;
                    }
                }
            }
            return null;
        }

        public Color? GetColorFromMultiParam(TestPoint testPoint, string ownColorFuncDesc, List<ColorRange> ownColorFuncRanges, string ownfuncParam)
        {
            float value = MultiParamColorTranslator.GetColorValue(testPoint, ownfuncParam);
            return float.IsNaN(value) ? Color.Empty : getColorFromRange(ownColorFuncRanges, value + 1); // value start from 0
        }

        public Color getColorFromRange(List<ColorRange> ranges, float value)
        {
            int count = ranges.Count;
            if (count == 0)
            {
                return Color.Empty;
            }
            for (int i = 0; i < count; i++)
            {
                ColorRange cr = ranges[i];
                if (value >= cr.minValue && value < cr.maxValue)
                {
                    if (cr.visible)
                    {
                        return cr.color;
                    }
                    else
                    {
                        return Color.Empty;
                    }

                }
            }
            if (value == ranges[count - 1].maxValue)
            {
                if (ranges[count - 1].visible)
                {
                    return ranges[count - 1].color;
                }
                else
                {
                    return Color.Empty;
                }
            }
            return Color.Empty;
        }

    }

    [Serializable]
    internal class OwnFuncCommander
    {
        public string funcName;
        public string desc;
        public string codeString;
        public string descriptionNote;
        public float minF;
        public float maxF;
        public string codeParamFormula;
        public override string ToString()
        {
            return desc;
        }
        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["funcName"] = funcName;
                param["desc"] = desc;
                param["codeString"] = codeString;
                param["descriptionNote"] = descriptionNote;
                param["minF"] = minF;
                param["maxF"] = maxF;
                param["codeParaFormula"] = codeParamFormula;
                return param;
            }
            set
            {
                funcName = (string)value["funcName"];
                desc = (string)value["desc"];
                codeString = (string)value["codeString"];
                descriptionNote = (string)value["descriptionNote"];
                minF = (float)value["minF"];
                maxF = (float)value["maxF"];
                if (value.ContainsKey("codeParaFormula"))
                {
                    codeParamFormula = (string)value["codeParaFormula"];
                }
            }
        }
        [NonSerialized]
        public bool _classReady = false;
        [NonSerialized]
        public bool _hasError = false;
        [NonSerialized]
        public object clzzInst;
        internal string initFuncClass_GridUnit()
        {
            string strErrorMsg = "";
            CryptionData cdata = new CryptionData();
            string strSourceCode = cdata.DecryptionStringdata(codeString);
            CSharpCodeProvider objCSharpCodePrivoder = new CSharpCodeProvider();
            CompilerParameters objCompilerParameters = new CompilerParameters();
            objCompilerParameters.ReferencedAssemblies.Add("RAMS.exe");
            objCompilerParameters.GenerateInMemory = true;
            CompilerResults cr = objCSharpCodePrivoder.CompileAssemblyFromSource(objCompilerParameters, strSourceCode);
            if (cr.Errors.HasErrors)
            {
                strErrorMsg = cr.Errors.Count.ToString() + " Errors:";
                StringBuilder sb = new StringBuilder(strErrorMsg);
                for (int x = 0; x < cr.Errors.Count; x++)
                {
                    sb.Append("\r\nLine: " + cr.Errors[x].Line.ToString() + " - " + cr.Errors[x].ErrorText);
                }
                strErrorMsg = sb.ToString();
                _hasError = true;
                _classReady = false;
            }
            else
            {
                System.Reflection.Assembly objAssembly = cr.CompiledAssembly;
                clzzInst = objAssembly.CreateInstance(funcName);
                if (clzzInst == null)
                {
                    strErrorMsg += "未能实例化类：" + funcName;
                    return strErrorMsg;
                }
                try
                {
                    object[] paramObj = new object[2];
                    MasterCom.RAMS.Grid.GridColorModeItem modeItem = new MasterCom.RAMS.Grid.GridColorModeItem();
                    paramObj[0] = modeItem;
                    paramObj[1] = new ColorUnit();
                    clzzInst.GetType().InvokeMember(
                                       "GetFuncResult",
                                       System.Reflection.BindingFlags.InvokeMethod, null, clzzInst,
                                       paramObj);
                    _hasError = false;
                    _classReady = true;
                }
                catch (MissingMethodException exx)
                {
                    strErrorMsg += "未找到函数或所制定的函数不满足要求！ " + exx.ToString();
                    _hasError = true;
                    _classReady = false;
                }
                catch (Exception ex)
                {
                    strErrorMsg += ex.ToString();
                    _hasError = true;
                    _classReady = false;
                }
            }
            return strErrorMsg;
        }

        internal string initFuncClass_TestPoint()
        {
            string strErrorMsg = "";
            CryptionData cdata = new CryptionData();
            string strSourceCode = cdata.DecryptionStringdata(codeString);
            CSharpCodeProvider objCSharpCodePrivoder = new CSharpCodeProvider();
            CompilerParameters objCompilerParameters = new CompilerParameters();
            objCompilerParameters.ReferencedAssemblies.Add("RAMS.exe");
            objCompilerParameters.GenerateInMemory = true;
            CompilerResults cr = objCSharpCodePrivoder.CompileAssemblyFromSource(objCompilerParameters, strSourceCode);
            if (cr.Errors.HasErrors)
            {
                strErrorMsg = cr.Errors.Count.ToString() + " Errors:";
                StringBuilder sb = new StringBuilder(strErrorMsg);
                for (int x = 0; x < cr.Errors.Count; x++)
                {
                    sb.Append("\r\nLine: " + cr.Errors[x].Line.ToString() + " - " + cr.Errors[x].ErrorText);
                }
                strErrorMsg = sb.ToString();
                _hasError = true;
                _classReady = false;
            }
            else
            {
                System.Reflection.Assembly objAssembly = cr.CompiledAssembly;
                clzzInst = objAssembly.CreateInstance(funcName);
                if (clzzInst == null)
                {
                    strErrorMsg += "未能实例化类：" + funcName;
                    return strErrorMsg;
                }
                try
                {
                    object[] paramObj = new object[2];
                    paramObj[0] = new TestPoint();
                    paramObj[1] = "";
                    clzzInst.GetType().InvokeMember(
                                       "GetFuncResult",
                                       System.Reflection.BindingFlags.InvokeMethod, null, clzzInst,
                                       paramObj);
                    _hasError = false;
                    _classReady = true;
                }
                catch (MissingMethodException exx)
                {
                    strErrorMsg += "未找到函数或所制定的函数不满足要求！ " + exx.ToString();
                    _hasError = true;
                    _classReady = false;
                }
                catch (Exception ex)
                {
                    strErrorMsg += ex.ToString();
                    _hasError = true;
                    _classReady = false;
                }
            }
            return strErrorMsg;
        }
    }

    [Serializable()]
    public class MapSerialInfo : ILegend
    {
        #region ILegend 成员
        public void DrawOnListBox(ListBox listBox, DrawItemEventArgs e)
        {
            string text = string.Format("[{0}] {1}", Visible ? "√" : "  ", Name);
            e.Graphics.DrawString(text, listBox.Font, Brushes.Black, e.Bounds.X, e.Bounds.Y);
        }
        #endregion

        /// <summary>
        /// 分类
        /// </summary>
        public string Type { get; set; } = "未分类";

        public string Name { get; set; } = "New Serial";

        public string IDName { get; set; }

        public bool Visible { get; set; } = true;

        private string colorParamName;

        public int OffsetX { get; set; }

        public int OffsetY { get; set; }

        public int MS { get; set; }

        public bool DisplayLine { get; set; } = false;

        public float LineWidth { get; set; } = 2.0f;

        public bool ColorParamEnabled { get; set; }
        /// <summary>
        /// 固定模式下的颜色
        /// </summary>
        public Color Color { get; set; } = Color.Green;

        public string ColorSystemName { get; set; }

        public bool DrawInvalidPoint { get; set; } = false;
        public Color InvalidPointColor { get; set; } = Color.Black;

        [NonSerialized()]
        private DTDisplayParameter colorDisplayParam;

     


        public string OwnColorFuncDesc { get; set; }

        public string OwnFuncParam { get; set; }

        public List<ColorRange> OwnColorFuncRanges { get; set; } = new List<ColorRange>();

        public string ColorParamName
        {
            get { return colorParamName; }
            set { colorParamName = value; colorDisplayParam = null; }
        }

        public int ColorArrayIndex { get; set; }

        public DTDisplayParameter ColorDisplayParam
        {
            get
            {
                if (colorDisplayParam == null && ColorSystemName != null && colorParamName != null)
                {
                    colorDisplayParam = DTDisplayParameterManager.GetInstance()[ColorSystemName, colorParamName, ColorArrayIndex];
                }
                return colorDisplayParam;
            }
        }

        public bool SizeParamEnabled { get; set; }

        public int Size { get; set; } = 20;

        private string sizeParamName;

        [NonSerialized()]
        private DTDisplayParameter sizeDisplayParam;

        private string symbolParamName;

        //[NonSerialized()]
        private DTDisplayParameter symbolDisplayParam;

        public string SizeSystemName { get; set; }

        public string SizeParamName
        {
            get { return sizeParamName; }
            set { sizeParamName = value; sizeDisplayParam = null; }
        }

        public int SizeArrayIndex { get; set; }

        public DTDisplayParameter SizeDisplayParam
        {
            get
            {
                if (sizeDisplayParam == null)
                {
                    sizeDisplayParam = DTDisplayParameterManager.GetInstance()[SizeSystemName, sizeParamName, SizeArrayIndex];
                }
                return sizeDisplayParam;
            }
        }

        public bool SymbolParamEnabled { get; set; }

        public int Symbol { get; set; }

        public string SymbolSystemName { get; set; }

        public string SymbolParamName
        {
            get { return symbolParamName; }
            set { symbolParamName = value; symbolDisplayParam = null; }
        }

        public int SymbolArrayIndex { get; set; }

        public DTDisplayParameter SymbolDisplayParam
        {
            get
            {
                if (symbolDisplayParam == null)
                {
                    symbolDisplayParam = DTDisplayParameterManager.GetInstance()[ColorSystemName, colorParamName, ColorArrayIndex];
                }
                return symbolDisplayParam;
            }
        }

        public override string ToString()
        {
            return this.Name;
        }

        public bool CheckVisible { get; set; } = true;

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Type"] = Type;
                param["Visible"] = Visible;
                param["Name"] = Name;
                param["OffsetX"] = OffsetX;
                param["OffsetY"] = OffsetY;
                param["MS"] = MS;
                param["DisplayLine"] = DisplayLine;
                param["LineWidth"] = LineWidth;
                getColor(param);
                getSize(param);
                getSymbol(param);
                return param;
            }
            set
            {
                object objValue;
                if (value.TryGetValue("LineWidth", out objValue))
                {
                    LineWidth = float.Parse(objValue.ToString());
                }
                else
                {
                    LineWidth = 2.0f;
                }

                if (value.TryGetValue("Type", out objValue))
                {
                    Type = objValue.ToString();
                }
                else
                {
                    Type = "未分类";
                }

                Visible = (bool)value["Visible"];
                Name = (String)value["Name"];
                OffsetX = (int)value["OffsetX"];
                OffsetY = (int)value["OffsetY"];
                MS = (int)value["MS"];
                DisplayLine = (bool)value["DisplayLine"];
                setColor(value);
                setSize(value);
                setSymbol(value);
            }
        }

        private void setColor(Dictionary<string, object> value)
        {
            ColorParamEnabled = (bool)value["ColorParamEnabled"];
            if (ColorParamEnabled)
            {
                object specialObj;
                if (!value.TryGetValue("AdvSpecialFuncName", out specialObj))
                {
                    ColorSystemName = (String)value["ColorSystemName"];
                    ColorParamName = (String)value["ColorParamName"];
                    ColorArrayIndex = (int)value["ColorArrayIndex"];
                }
                else
                {
                    if (specialObj == null || specialObj.ToString().Equals(""))
                    {
                        ColorSystemName = (String)value["ColorSystemName"];
                        ColorParamName = (String)value["ColorParamName"];
                        ColorArrayIndex = (int)value["ColorArrayIndex"];
                    }
                    else
                    {
                        OwnColorFuncDesc = (string)specialObj;
                        setcolorOwnFuncValueRanges(value);
                        setAdvOwnFuncParam(value);
                    }
                }
            }
            else
            {
                Color = Color.FromArgb((int)value["ColorR"], (int)value["ColorG"], (int)value["ColorB"]);
            }
        }

        private void setcolorOwnFuncValueRanges(Dictionary<string, object> value)
        {
            OwnColorFuncRanges = new List<ColorRange>();

            object colorParamObj;
            if (value.TryGetValue("colorOwnFuncValueRanges", out colorParamObj))
            {
                List<object> colorParams = colorParamObj as List<object>;
                foreach (object o in colorParams)
                {
                    Dictionary<string, object> colorParam = (Dictionary<string, object>)o;
                    ColorRange cr = new ColorRange();
                    cr.Param = colorParam;
                    OwnColorFuncRanges.Add(cr);
                }
            }
        }

        private void setAdvOwnFuncParam(Dictionary<string, object> value)
        {
            object ownfuncParamObj;
            if (value.TryGetValue("AdvOwnFuncParam", out ownfuncParamObj))
            {
                OwnFuncParam = (string)ownfuncParamObj;
            }
            else
            {
                OwnFuncParam = "";
            }
        }

        private void setSize(Dictionary<string, object> value)
        {
            SizeParamEnabled = (bool)value["SizeParamEnabled"];
            if (SizeParamEnabled)
            {
                SizeSystemName = (String)value["SizeSystemName"];
                SizeParamName = (String)value["SizeParamName"];
                SizeArrayIndex = (int)value["SizeArrayIndex"];
            }
            else
            {
                Size = (int)value["Size"];
            }
        }

        private void setSymbol(Dictionary<string, object> value)
        {
            SymbolParamEnabled = (bool)value["SymbolParamEnabled"];
            if (SymbolParamEnabled)
            {
                SymbolSystemName = (String)value["SymbolSystemName"];
                SymbolParamName = (String)value["SymbolParamName"];
                SymbolArrayIndex = (int)value["SymbolArrayIndex"];
            }
            else
            {
                Symbol = (int)value["Symbol"];
            }
        }

        private void getColor(Dictionary<string, object> param)
        {
            param["ColorParamEnabled"] = ColorParamEnabled;
            if (ColorParamEnabled)
            {
                if (OwnColorFuncDesc != null && OwnColorFuncDesc != "")
                {
                    param["AdvSpecialFuncName"] = OwnColorFuncDesc;
                    if (OwnFuncParam != null && OwnFuncParam != "")
                    {
                        param["AdvOwnFuncParam"] = OwnFuncParam;
                    }
                    List<object> colorParams = new List<object>();
                    param["colorOwnFuncValueRanges"] = colorParams;
                    foreach (ColorRange cr in OwnColorFuncRanges)
                    {
                        colorParams.Add(cr.Param);
                    }
                }
                else
                {
                    param["ColorSystemName"] = ColorSystemName;
                    param["ColorParamName"] = ColorParamName;
                    param["ColorArrayIndex"] = ColorArrayIndex;
                }
            }
            else
            {
                param["ColorR"] = (int)Color.R;
                param["ColorG"] = (int)Color.G;
                param["ColorB"] = (int)Color.B;
            }
        }

        private void getSize(Dictionary<string, object> param)
        {
            param["SizeParamEnabled"] = SizeParamEnabled;
            if (SizeParamEnabled)
            {
                param["SizeSystemName"] = SizeSystemName;
                param["SizeParamName"] = SizeParamName;
                param["SizeArrayIndex"] = SizeArrayIndex;
            }
            else
            {
                param["Size"] = Size;
            }
        }

        private void getSymbol(Dictionary<string, object> param)
        {
            param["SymbolParamEnabled"] = SymbolParamEnabled;
            if (SymbolParamEnabled)
            {
                param["SymbolSystemName"] = SymbolSystemName;
                param["SymbolParamName"] = SymbolParamName;
                param["SymbolArrayIndex"] = SymbolArrayIndex;
            }
            else
            {
                param["Symbol"] = Symbol;
            }
        }

        public void Draw(MapDTLayer layer, float ratio, DbRect updateRect, Graphics graphics, MainModel mainModel)
        {
            if (!Visible)
            {
                return;
            }

            if (!layer.MainModel.DrawHandoverSerialNum && layer.CurMapType == LayerBase.LayerMapType.MTGis || layer.CurMapType == LayerBase.LayerMapType.Google)
            {
                foreach (TestPoint testPoint in layer.SampledTestPoints)
                {
                    if (testPoint == null)
                    {
                        return;
                    }
                    drawSampledTPs(layer, ratio, graphics, mainModel, testPoint);
                }
            }
            graphics.ResetTransform();
            foreach (AMapElement ame in layer.TestPointArrowManager.MapElements)
            {
                if (ame.Visible)
                {
                    ame.BaseElement.IsDraw = true;
                    ame.Paint(graphics, 1, 1);
                }
            }
            bool multiSel = layer.MainModel.SelectedTestPoints.Count > 1;
            foreach (TestPoint testPoint in layer.MainModel.SelectedTestPoints)
            {
                if (testPoint == null)
                {
                    return;
                }
                drawSelectedTPs(layer, ratio, graphics, mainModel, multiSel, testPoint);
            }
        }

        private void drawSampledTPs(MapDTLayer layer, float ratio, Graphics graphics, MainModel mainModel, TestPoint testPoint)
        {
            if (testPoint.MS == MS || MS == 0)
            {
                DbPoint dPoint = new DbPoint(testPoint.Longitude, testPoint.Latitude);
                PointF point;
                layer.GisAdapter.ToDisplay(dPoint, out point);
                int curOffsetX = 0;
                int curOffsetY = 0;
                layer.MainModel.VisibleOffsetManager.GetCommonKPIOffset(ref curOffsetX, ref curOffsetY, this);
                layer.MainModel.VisibleOffsetManager.GetOffset(ref curOffsetX, ref curOffsetY, testPoint.FileID, testPoint.ProjectType, testPoint.ServiceType);
                point += new Size(curOffsetX, curOffsetY);
                Draw(layer, testPoint, ratio, point, graphics, mainModel, false);
            }
        }

        private void drawSelectedTPs(MapDTLayer layer, float ratio, Graphics graphics, MainModel mainModel, bool multiSel, TestPoint testPoint)
        {
            if (testPoint.MS == MS || MS == 0)
            {
                DbPoint dPoint = new DbPoint(testPoint.Longitude, testPoint.Latitude);
                PointF point;
                layer.GisAdapter.ToDisplay(dPoint, out point);
                int curOffsetX = OffsetX;
                int curOffsetY = OffsetY;
                layer.MainModel.VisibleOffsetManager.GetOffset(ref curOffsetX, ref curOffsetY,
                    testPoint.FileID, testPoint.ProjectType, testPoint.ServiceType);
                point += new Size(curOffsetX, curOffsetY);
                Draw(layer, testPoint, ratio * (multiSel ? 1 : 2), point, graphics, mainModel, multiSel);
            }
        }

        public void Select(MapDTLayer layer, float ratio, MapOperation2 mop2, List<TestPoint> selectedTestPoints)
        {
            if (!Visible)
            {
                return;
            }
            for (int i = layer.MainModel.SelectedTestPoints.Count - 1; i >= 0; i--)
            {
                TestPoint testPoint = layer.MainModel.SelectedTestPoints[i];
                if (testPoint == null)
                {
                    return;
                }
                if (selectedTestPoints.Count == 1)
                {
                    return;
                }
                if (testPoint.MS == MS || MS == 0)
                {
                    DbPoint dPoint = new DbPoint(testPoint.Longitude, testPoint.Latitude);
                    PointF point;
                    layer.GisAdapter.ToDisplay(dPoint, out point);
                    int curOffsetX = OffsetX;
                    int curOffsetY = OffsetY;
                    layer.MainModel.VisibleOffsetManager.GetOffset(ref curOffsetX, ref curOffsetY, testPoint.FileID, testPoint.ProjectType, testPoint.ServiceType);
                    point += new Size(curOffsetX, curOffsetY);
                    Select(layer, testPoint, ratio, point, mop2, selectedTestPoints);
                }
            }
            for (LinkedListNode<TestPoint> node = layer.SampledTestPoints.Last; node != null; node = node.Previous)
            {
                TestPoint testPoint = node.Value;
                if (selectedTestPoints.Count == 1)
                {
                    return;
                }
                if (testPoint.MS == MS || MS == 0)
                {
                    DbPoint dPoint = new DbPoint(testPoint.Longitude, testPoint.Latitude);
                    PointF point;
                    layer.GisAdapter.ToDisplay(dPoint, out point);
                    int curOffsetX = OffsetX;
                    int curOffsetY = OffsetY;
                    layer.MainModel.VisibleOffsetManager.GetOffset(ref curOffsetX, ref curOffsetY, testPoint.FileID, testPoint.ProjectType, testPoint.ServiceType);
                    point += new Size(curOffsetX, curOffsetY);
                    Select(layer, testPoint, ratio, point, mop2, selectedTestPoints);
                }
            }
        }

        public void ExportKml(MapDTLayer layer, KMLExporter exporter, XmlElement serialInfosElement)
        {
            if (Visible)
            {
                XmlElement serialInfoElement = exporter.CreateFolder(Name, false);
                serialInfosElement.AppendChild(serialInfoElement);
                foreach (DTFileDataManager fileDataManager in layer.MainModel.DTDataManager.FileDataManagers)
                {
                    if (layer.MainModel.VisibleOffsetManager.IsFileVisble(fileDataManager.FileID))
                    {
                        dealTestPointKml(layer, exporter, serialInfoElement, fileDataManager);
                    }
                }
            }
        }

        private void dealTestPointKml(MapDTLayer layer, KMLExporter exporter, XmlElement serialInfoElement, DTFileDataManager fileDataManager)
        {
            foreach (TestPoint testPoint in fileDataManager.TestPoints)
            {
                if (layer.MainModel.VisibleOffsetManager.IsProjectVisble(testPoint.ProjectType) && layer.MainModel.VisibleOffsetManager.IsServiceVisble(testPoint.ServiceType))
                {
                    DbPoint dPoint = new DbPoint(testPoint.Longitude, testPoint.Latitude);
                    PointF point;
                    layer.GisAdapter.ToDisplay(dPoint, out point);
                    int curOffsetX = OffsetX;
                    int curOffsetY = OffsetY;
                    layer.MainModel.VisibleOffsetManager.GetOffset(ref curOffsetX, ref curOffsetY, testPoint.FileID, testPoint.DeviceType, testPoint.ServiceType);
                    point += new Size(curOffsetX, curOffsetY);
                    exportKml(layer, testPoint, point, exporter, serialInfoElement);
                }
            }
        }

        private void Draw(MapDTLayer layer, TestPoint testPoint, float ratio, PointF point, Graphics graphics, MainModel mainModel, bool drawCellLineOnly)
        {
            Color? curColor;
            int? curSize;
            int? curSymbol;

            bool isGetStyle = getStyle(layer, testPoint, out curColor, out curSize, out curSymbol);
            if (DrawInvalidPoint && curColor == null)
            {
                curColor = InvalidPointColor;
            }
            else if (!isGetStyle)
            {
                return;
            }

            if (layer.CurMapType == LayerBase.LayerMapType.MTGis)
            {
                getExStyle(mainModel, testPoint, ref curSymbol);
                drawCellCoverBelt(layer, testPoint, graphics, mainModel, ref curColor, ref curSymbol);
                drawCellAntSample(testPoint, mainModel, ref curColor);
            }
            if (curColor == null)
            {
                return;
            }
            if (!drawCellLineOnly)
            {
                drawPoint(layer, testPoint, ratio, point, graphics, curColor, curSymbol);
            }
            drawPointCellLine(layer, testPoint, point, graphics, mainModel);
        }

        #region 绘制小区覆盖带
        private void drawCellCoverBelt(MapDTLayer layer, TestPoint testPoint, Graphics graphics, MainModel mainModel,
                            ref Color? color, ref int? symbol)
        {
            #region 小区覆盖带自定采样点样式，颜色
            int nbI = -1;
            if (mainModel.CovCellShowSetting != null)  //小区覆盖带自定采样点样式，颜色
            {
                if (mainModel.CovCellShowSetting.showKind == showSetting.TpKind.byCell)
                {
                    showSetting setting = mainModel.CovCellShowSetting;
                    switch (setting.tpCellMode)
                    {
                        case showSetting.CellMode.bySingle:
                            {
                                if (mainModel.CovCellShowSetting.tpSingleCell_maincell.tpList.Exists(delegate(TestPoint tp) { return (tp.SN == testPoint.SN) && (tp.FileID == testPoint.FileID) && (tp.Time == testPoint.Time); }))
                                {
                                    color = setting.tpSingleCell_maincellColor;
                                }
                                else if (mainModel.CovCellShowSetting.tpSingleCell_maincell.tpListAsNbcell.Exists(delegate(TestPoint tp) { return (tp.SN == testPoint.SN) && (tp.FileID == testPoint.FileID) && (tp.Time == testPoint.Time); }))
                                {
                                    color = setting.tpSingleCell_nbcellColor;
                                    symbol = 3;
                                    string snk = testPoint.SN.ToString() + testPoint.FileID.ToString() + testPoint.Time.ToString();
                                    if (mainModel.CovCellShowSetting.tpSingleCell_maincell.snNnIDic.ContainsKey(snk))
                                    {
                                        nbI = mainModel.CovCellShowSetting.tpSingleCell_maincell.snNnIDic[snk];
                                    }
                                }
                                else
                                    //  color = Color.Transparent; //非观察采样点不显示
                                    return;
                            }
                            break;
                        case showSetting.CellMode.byMulti:
                            {
                                if (setting.tpMultiCellColorDic.Count == 0)
                                {
                                    break;
                                }
                                bool find = false;
                                foreach (CoverageCellInfo covcell in setting.tpMultiCellColorDic.Keys)
                                {
                                    if (covcell.tpList.Exists(delegate(TestPoint tp) { return (tp.SN == testPoint.SN) && (tp.FileID == testPoint.FileID) && (tp.Time == testPoint.Time); }))
                                    {
                                        color = setting.tpMultiCellColorDic[covcell];
                                        find = true;
                                        break;
                                    }
                                    else if (covcell.tpListAsNbcell.Exists(delegate(TestPoint tp) { return (tp.SN == testPoint.SN) && (tp.FileID == testPoint.FileID) && (tp.Time == testPoint.Time); }))
                                    {
                                        color = setting.tpMultiCellColorDic[covcell];
                                        find = true;
                                        symbol = 3;
                                        string snk = testPoint.SN.ToString() + testPoint.FileID.ToString() + testPoint.Time.ToString();
                                        if (covcell.snNnIDic.ContainsKey(snk))
                                        {
                                            nbI = covcell.snNnIDic[snk];
                                        }
                                        break;
                                    }
                                    else
                                        continue;
                                }
                                if (!find)
                                {
                                    return;
                                }
                            }
                            break;
                        default:
                            break;
                    }
                }
                else if (mainModel.CovCellShowSetting.showKind == showSetting.TpKind.byParam)
                {
                    showSetting setting = mainModel.CovCellShowSetting;
                    switch (setting.tpCellMode)
                    {
                        case showSetting.CellMode.bySingle:
                            {
                                if (mainModel.CovCellShowSetting.tpSingleCell_maincell.tpListAsNbcell.Exists(delegate(TestPoint tp) { return (tp.SN == testPoint.SN) && (tp.FileID == testPoint.FileID) && (tp.Time == testPoint.Time); }))
                                {
                                    symbol = 3;
                                    string snk = testPoint.SN.ToString() + testPoint.FileID.ToString() + testPoint.Time.ToString();
                                    if (mainModel.CovCellShowSetting.tpSingleCell_maincell.snNnIDic.ContainsKey(snk))
                                    {
                                        nbI = mainModel.CovCellShowSetting.tpSingleCell_maincell.snNnIDic[snk];
                                    }

                                    //使用系统的场强着色
                                    color = ColorDisplayParam.Info.GetColor(Calvalue(testPoint, nbI));
                                    if (color == null)
                                        return;
                                }
                                else
                                    return;
                            }
                            break;
                        case showSetting.CellMode.byMulti:
                            {
                                if (setting.tpMultiCellColorDic.Count == 0)
                                {
                                    break;
                                }
                                bool find = false;
                                foreach (CoverageCellInfo covcell in setting.tpMultiCellColorDic.Keys)
                                {
                                    if (covcell.tpList.Exists(delegate(TestPoint tp) { return (tp.SN == testPoint.SN) && (tp.FileID == testPoint.FileID) && (tp.Time == testPoint.Time); }))
                                    {
                                        //使用系统的场强着色
                                        find = true;
                                        break;
                                    }
                                    else if (covcell.tpListAsNbcell.Exists(delegate(TestPoint tp) { return (tp.SN == testPoint.SN) && (tp.FileID == testPoint.FileID) && (tp.Time == testPoint.Time); }))
                                    {
                                        symbol = 3;
                                        string snk = testPoint.SN.ToString() + testPoint.FileID.ToString() + testPoint.Time.ToString();
                                        if (covcell.snNnIDic.ContainsKey(snk))
                                        {
                                            nbI = covcell.snNnIDic[snk];
                                        }

                                        color = ColorDisplayParam.Info.GetColor(Calvalue(testPoint, nbI));
                                        if (color == null)
                                        {
                                            return;
                                        }
                                        find = true;
                                        break;
                                    }
                                    else
                                        continue;
                                }
                                if (!find)
                                {
                                    return;
                                }
                            }
                            break;
                        default:
                            break;
                    }

                }
            }
            #endregion

            #region 小区覆盖带场强
            if (mainModel.CovCellShowSetting != null && color != Color.Transparent && mainModel.CovCellShowSetting.isMarkNum)
            {
                if (nbI == -1)
                {
                    if (testPoint["RxLevSub"] != null)
                    {
                        paintValueLabel(layer, testPoint, "RxLevSub", graphics);
                    }
                    else if (testPoint["TD_PCCPCH_RSCP"] != null)
                    {
                        paintValueLabel(layer, testPoint, "TD_PCCPCH_RSCP", graphics);
                    }
                }
                else
                {
                    if (testPoint["N_RxLev", nbI] != null)
                    {
                        paintValueLabel(layer, testPoint, "N_RxLev", nbI, graphics);
                    }
                    else if (testPoint["TD_NCell_PCCPCH_RSCP", nbI] != null)
                    {
                        paintValueLabel(layer, testPoint, "TD_NCell_PCCPCH_RSCP", nbI, graphics);
                    }
                    else if (testPoint["lte_NCell_RSRP", nbI] != null)
                    {
                        paintValueLabel(layer, testPoint, "lte_NCell_RSRP", nbI, graphics);
                    }
                }
            }
            #endregion
        }

        /// <summary>
        /// 绘画采样点的指定参数值文本
        /// </summary>
        private void paintValueLabel(MapDTLayer layer, TestPoint testpoint, string tarParam, Graphics graphics)
        {
            DbPoint dPoint = new DbPoint(testpoint.Longitude, testpoint.Latitude);
            PointF point;
            layer.GisAdapter.ToDisplay(dPoint, out point);
            graphics.TranslateTransform(point.X, point.Y);
            string cellDes = testpoint[tarParam].ToString();
            SizeF curSize = graphics.MeasureString(cellDes, fontCellLabel);
            curSize.Height *= 0.8f;
            graphics.FillRectangle(Brushes.White, 5, -curSize.Height / 2, curSize.Width, curSize.Height);
            graphics.DrawString(cellDes, fontCellLabel, Brushes.Black, 3, -curSize.Height / 2);

            graphics.ResetTransform();
        }

        /// <summary>
        /// 绘画采样点的指定参数值文本
        /// </summary>
        private void paintValueLabel(MapDTLayer layer, TestPoint testpoint, string tarParam, int nbi, Graphics graphics)
        {
            DbPoint dPoint = new DbPoint(testpoint.Longitude, testpoint.Latitude);
            PointF point;
            layer.GisAdapter.ToDisplay(dPoint, out point);
            graphics.TranslateTransform(point.X, point.Y);
            string cellDes = testpoint[tarParam, nbi].ToString();
            SizeF curSize = graphics.MeasureString(cellDes, fontCellLabel);
            curSize.Height *= 0.8f;
            graphics.FillRectangle(Brushes.White, 5, -curSize.Height / 2, curSize.Width, curSize.Height);
            graphics.DrawString(cellDes, fontCellLabel, Brushes.Black, 3, -curSize.Height / 2);

            graphics.ResetTransform();
        }
        #endregion

        /// <summary>
        /// 天线分析采样点打点定位
        /// </summary>
        private void drawCellAntSample(TestPoint testPoint, MainModel mainModel, ref Color? color)
        {
            if (mainModel.CellAntSampleIndexDic != null)
            {
                DisplayLine = false;
                string snk = testPoint.SN.ToString() + testPoint.FileID.ToString() + testPoint.Time.ToString();
                int nbI;
                if (!mainModel.CellAntSampleIndexDic.TryGetValue(snk, out nbI))
                {
                    nbI = -99;
                }

                if (nbI != -99)
                {
                    if (nbI == -1 && testPoint["lte_RSRP"] != null) //路测主服采样点
                    {
                        float? rsrp = (float?)testPoint["lte_RSRP"];
                        color = ColorDisplayParam.Info.GetColor(rsrp);
                    }
                    else if (testPoint["lte_NCell_RSRP", nbI] != null)//路测邻小区采样点
                    {
                        float? rsrp = (float?)testPoint["lte_NCell_RSRP", nbI];
                        color = ColorDisplayParam.Info.GetColor(rsrp);
                    }
                    else if (testPoint["LTESCAN_TopN_CELL_Specific_RSRP", nbI] != null)//扫频采样点
                    {
                        float? rsrp = (float?)testPoint["LTESCAN_TopN_CELL_Specific_RSRP", nbI];
                        color = ColorDisplayParam.Info.GetColor(rsrp);
                    }
                }
            }
        }

        /// <summary>
        /// 绘制采样点(选中时为选中状态)
        /// </summary>
        /// <param name="layer"></param>
        /// <param name="testPoint"></param>
        /// <param name="ratio"></param>
        /// <param name="point"></param>
        /// <param name="graphics"></param>
        /// <param name="drawCellLineOnly"></param>
        /// <param name="color"></param>
        /// <param name="size"></param>
        /// <param name="symbol"></param>
        private void drawPoint(MapDTLayer layer, TestPoint testPoint, float ratio, PointF point, Graphics graphics, Color? color, int? symbol)
        {
            //画采样点
            Brush brush = layer.Alpha == 255 ? new SolidBrush((Color)color) : new SolidBrush(Color.FromArgb(layer.Alpha, (Color)color));
            float radius = ratio * layer.PointSize;
            graphics.TranslateTransform(point.X, point.Y);
            graphics.ScaleTransform(radius, radius);
            GraphicsPath path = SymbolManager.GetInstance().Paths[(int)symbol];
            graphics.FillPath(brush, path);

            //对当前选中点，加外框
            if (testPoint.Selected)
            {
                graphics.DrawPath(layer.PenSelected, path);
            }

            graphics.ResetTransform();
            if (string.IsNullOrEmpty(OwnColorFuncDesc))
            {
                testPoint.Tag = ColorDisplayParam;
            }
        }

        #region 绘制小区和采样点的连线
        /// <summary>
        /// 绘制小区和采样点的连线
        /// </summary>
        /// <param name="layer"></param>
        /// <param name="testPoint"></param>
        /// <param name="point"></param>
        /// <param name="graphics"></param>
        /// <param name="mainModel"></param>
        private void drawPointCellLine(MapDTLayer layer, TestPoint testPoint, PointF point, Graphics graphics, MainModel mainModel)
        {
            if (layer.CurMapType == LayerBase.LayerMapType.MTGis && mainModel.IsShowCoCellLine
                && mainModel.CoCellTestPointDic != null
                && mainModel.CoCellTestPointDic.ContainsKey(testPoint))
            {
                foreach (ICell cell in mainModel.CoCellTestPointDic[testPoint])
                {
                    DbPoint pnt = null;
                    if (cell is Cell)
                    {
                        pnt = layer.GetGSMAntennaEndPoint(cell as Cell, testPoint.Longitude, testPoint.Latitude);
                    }
                    else if (cell is TDCell)
                    {
                        pnt = layer.GetTDAntennaEndPoint(cell as TDCell, testPoint.DateTime);
                    }
                    PointF antennaPointF;
                    layer.GisAdapter.ToDisplay(pnt, out antennaPointF);
                    Pen pen = new Pen(Color.DeepPink, 2);
                    graphics.DrawLine(pen, point.X, point.Y, antennaPointF.X, antennaPointF.Y);
                }
            }
            if (DisplayLine)
            {
                if (testPoint.Longitude <= 0 || testPoint.Latitude <= 0)
                {
                    return;
                }

                ////绘制邻区（只针对选中点，如果有多个选中点，只看第一个）
                drawPointCellLineNB(layer, testPoint, point, graphics, mainModel);

                ////绘制采样点和小区之间连线（当选中点或选中小区时，才绘制）
                drawPointCellLineServ(layer, testPoint, point, graphics);
            }
        }

        #region 绘制采样点和邻区的连线(虚线)
        /// <summary>
        /// 绘制采样点和邻区的连线(虚线)
        /// </summary>
        /// <param name="layer"></param>
        /// <param name="testPoint"></param>
        /// <param name="point"></param>
        /// <param name="graphics"></param>
        /// <param name="mainModel"></param>
        private void drawPointCellLineNB(MapDTLayer layer, TestPoint testPoint, PointF point, Graphics graphics, MainModel mainModel)
        {
            int iLoop = 50;
            if (layer.CurMapType == LayerBase.LayerMapType.MTGis)
            {
                iLoop = 16;
                scanNbCellPen.DashStyle = DashStyle.Dot;
            }
            if (layer.MainModel.SelectedTestPoints.Count == 1
                && layer.MainModel.SelectedTestPoints[0] == testPoint)
            {
                if (!layer.MainModel.SystemConfigInfo.isOnlyServLine)
                {
                    drawSelectedTPCellLineNB(layer, testPoint, point, graphics, mainModel, iLoop);
                }
            }
            else if (layer.CurMapType == LayerBase.LayerMapType.MTGis)
            {
                drawScanCellLineNB(layer, testPoint, point, graphics, mainModel);
            }
        }

        private void drawSelectedTPCellLineNB(MapDTLayer layer, TestPoint testPoint, PointF point, Graphics graphics, MainModel mainModel, int iLoop)
        {
            if (mainModel.SystemConfigInfo.bNBLineTopN)
            {
                for (int i = mainModel.SystemConfigInfo.nbLineTopN - 1; i >= 0; i--)
                {
                    drawNBCellLine(layer, graphics, point, testPoint, i);
                }
            }
            else
            {
                for (int i = 1; i < iLoop; i++)
                {
                    if (!drawNBCellLine(layer, graphics, point, testPoint, i))
                    {
                        break;
                    }
                }
            }
        }

        private bool drawNBCellLine(MapDTLayer layer, Graphics graphics, PointF point, TestPoint testPoint, int i)
        {
            ICell cell = null;
            if (!GetNBCell(testPoint, i, ref cell))
            {
                return false;
            }

            Pen curPen = layer.NeighbourPens[i % 15];
            DbPoint antennaPoint = null;
            if (cell is Cell)
            {
                antennaPoint = layer.GetGSMAntennaEndPoint(cell as Cell, testPoint.Longitude, testPoint.Latitude);
            }
            else if (cell is TDCell)
            {
                antennaPoint = layer.GetTDAntennaEndPoint(cell as TDCell, testPoint.DateTime);
            }
            else if (cell is WCell)
            {
                antennaPoint = layer.GetWAntennaEndPoint(cell as WCell, testPoint.DateTime);
            }
            else if (cell is CDCell)
            {
                antennaPoint = layer.GetCDAntennaEndPoint(cell as CDCell, testPoint.DateTime);
            }
            else if (cell is LTECell)
            {
                antennaPoint = layer.GetLTEAntennaEndPoint(cell as LTECell, testPoint.Longitude, testPoint.Latitude);
            }

            if (antennaPoint != null)
            {
                PointF antennaPointF;
                layer.GisAdapter.ToDisplay(antennaPoint, out antennaPointF);
                if (antennaPointF.X > -10000 && antennaPointF.X < 10000 && antennaPointF.Y > -10000 && antennaPointF.Y < 10000)
                {
                    graphics.DrawLine(curPen, point.X, point.Y, antennaPointF.X, antennaPointF.Y);
                }
            }
            return true;
        }

        private void drawScanCellLineNB(MapDTLayer layer, TestPoint testPoint, PointF point, Graphics graphics, MainModel mainModel)
        {
            if (testPoint is ScanTestPoint_G && (mainModel.SelectedCells.Count > 0
                || mainModel.SelectedCell != null))
            {
                drawScanCellLineNB(layer, testPoint, point, graphics, mainModel, getGSMScanCellLineNB);
            }
            else if (testPoint is ScanTestPoint_TD && (mainModel.SelectedTDCells.Count > 0
               || mainModel.SelectedTDCells != null))
            {
                drawScanCellLineNB(layer, testPoint, point, graphics, mainModel, getTDScanCellLineNB);
            }
            else if (testPoint is ScanTestPoint_LTE && (mainModel.SelectedLTECells.Count > 0
             || mainModel.SelectedLTECell != null))
            {
                drawScanCellLineNB(layer, testPoint, point, graphics, mainModel, getLTEScanCellLineNB);
            }
            else if (testPoint is ScanTestPoint_NR && (mainModel.SelectedNRCells.Count > 0
             || mainModel.SelectedNRCell != null))
            {
                drawNRScanCellLineNB(layer, testPoint, point, graphics, mainModel, getNRScanCellLineNB);
            }
        }

        private void drawNRScanCellLineNB(MapDTLayer layer, TestPoint testPoint, PointF point, Graphics graphics, MainModel mainModel, Func func)
        {
            Dictionary<int, int> groupDic = NRTpHelper.NrScanTpManager.GetCellMaxBeam(testPoint);
            foreach (var i in groupDic.Values)
            {
                ScanCellLineNB res = func(testPoint, i, layer, mainModel);

                if (res.IsValid)
                {
                    PointF antennaPointF;
                    layer.GisAdapter.ToDisplay(res.AntennaPoint, out antennaPointF);
                    if (antennaPointF.X > -10000 && antennaPointF.X < 10000 && antennaPointF.Y > -10000 && antennaPointF.Y < 10000)
                    {
                        graphics.DrawLine(scanNbCellPen, point.X, point.Y, antennaPointF.X, antennaPointF.Y);
                    }
                    break;
                }
            }
        }

        private void drawScanCellLineNB(MapDTLayer layer, TestPoint testPoint, PointF point, Graphics graphics, MainModel mainModel, Func func)
        {
            for (int i = 1; i < 16; i++)
            {
                ScanCellLineNB res = func(testPoint, i, layer, mainModel);

                if (res.IsValid)
                {
                    PointF antennaPointF;
                    layer.GisAdapter.ToDisplay(res.AntennaPoint, out antennaPointF);
                    if (antennaPointF.X > -10000 && antennaPointF.X < 10000 && antennaPointF.Y > -10000 && antennaPointF.Y < 10000)
                    {
                        graphics.DrawLine(scanNbCellPen, point.X, point.Y, antennaPointF.X, antennaPointF.Y);
                    }
                    break;
                }
            }
        }

        delegate ScanCellLineNB Func(TestPoint testPoint, int i, MapDTLayer layer, MainModel mainModel);

        private ScanCellLineNB getGSMScanCellLineNB(TestPoint testPoint, int i, MapDTLayer layer, MainModel mainModel)
        {
            ScanCellLineNB res = new ScanCellLineNB();
            Cell cell = testPoint.GetCell_GSMScan(i);
            if (cell != null && (cell == mainModel.SelectedCell || mainModel.SelectedCells.Contains(cell)))
            {
                DbPoint antennaPoint = layer.GetGSMAntennaEndPoint(cell, testPoint.Longitude, testPoint.Latitude);
                res.IsValid = true;
                res.AntennaPoint = antennaPoint;
            }
            return res;
        }

        private ScanCellLineNB getTDScanCellLineNB(TestPoint testPoint, int i, MapDTLayer layer, MainModel mainModel)
        {
            ScanCellLineNB res = new ScanCellLineNB();
            TDCell cell = testPoint.GetCell_TDScan(i);
            if (cell != null && (cell == mainModel.SelectedTDCell || mainModel.SelectedTDCells.Contains(cell)))
            {
                DbPoint antennaPoint = layer.GetTDAntennaEndPoint(cell, testPoint.DateTime);
                res.IsValid = true;
                res.AntennaPoint = antennaPoint;
            }
            return res;
        }

        private ScanCellLineNB getLTEScanCellLineNB(TestPoint testPoint, int i, MapDTLayer layer, MainModel mainModel)
        {
            ScanCellLineNB res = new ScanCellLineNB();
            LTECell cell = testPoint.GetCell_LTEScan(i);
            if (cell != null && (cell == mainModel.SelectedLTECell || mainModel.SelectedLTECells.Contains(cell)))
            {
                DbPoint antennaPoint = layer.GetLTEAntennaEndPoint(cell, testPoint.Longitude, testPoint.Latitude);
                res.IsValid = true;
                res.AntennaPoint = antennaPoint;
            }
            return res;
        }

        private ScanCellLineNB getNRScanCellLineNB(TestPoint testPoint, int i, MapDTLayer layer
            , MainModel mainModel)
        {
            ScanCellLineNB res = new ScanCellLineNB();
            var cell = testPoint.GetCell_NRScan(i);
            if (cell != null && (cell == mainModel.SelectedNRCell || mainModel.SelectedNRCells.Contains(cell)))
            {
                DbPoint antennaPoint = layer.GetNRAntennaEndPoint(cell, testPoint.Longitude, testPoint.Latitude);
                res.IsValid = true;
                res.AntennaPoint = antennaPoint;
            }
            return res;
        }

        class ScanCellLineNB
        {
            public bool IsValid { get; set; } = false;
            public DbPoint AntennaPoint { get; set; } = null;
        }
        #endregion

        #region 绘制采样点和主服小区之间连线(实线)
        /// <summary>
        /// 绘制采样点和主服小区之间连线(实线)
        /// </summary>
        /// <param name="layer"></param>
        /// <param name="testPoint"></param>
        /// <param name="point"></param>
        /// <param name="graphics"></param>
        private void drawPointCellLineServ(MapDTLayer layer, TestPoint testPoint, PointF point, Graphics graphics)
        {
            if (!testPoint.Selected
                && layer.MainModel.SelectedCells.Count == 0
                && layer.MainModel.SelectedTDCells.Count == 0
                && layer.MainModel.SelectedWCells.Count == 0
                && layer.MainModel.SelectedCDCells.Count == 0
                && layer.MainModel.SelectedLTECells == null
                && layer.MainModel.SelectedNRCells == null)
            {
                return;
            }

            ICell mainCell = testPoint.GetMainCell();
            if (mainCell is NRCell && testPoint is TestPoint_NR)//5G时双连接，同时连续4G、5G小区
            {
                LTECell lteCell = testPoint.GetMainCell_LTE();
                drawPointCellLineServByAntenna(layer, testPoint, point, graphics, lteCell);
            }

            drawPointCellLineServByAntenna(layer, testPoint, point, graphics, mainCell);
        }

        private void drawPointCellLineServByAntenna(MapDTLayer layer, TestPoint testPoint, PointF point
            , Graphics graphics, ICell icell)
        {
            if (!isNeedDrawCellLine(icell))
            {
                return;
            }

            DbPoint antennaDbPoint = getAntennaDbPoint(layer, testPoint, icell);
            if (antennaDbPoint != null)
            {
                PointF antennaPointF;
                layer.GisAdapter.ToDisplay(antennaDbPoint, out antennaPointF);

                if (antennaPointF.X > -10000 && antennaPointF.X < 10000 && antennaPointF.Y > -10000 && antennaPointF.Y < 10000)
                {
                    //绘制与主服小区的连线
                    Pen pen = new Pen(Color.Green, LineWidth);
                    if (layer.CurMapType == LayerBase.LayerMapType.Google)
                    {
                        //Google地图颜色偏暗,选用亮色连线
                        pen = new Pen(Color.Yellow, LineWidth);
                    }
                    graphics.DrawLine(pen, point.X, point.Y, antennaPointF.X, antennaPointF.Y);
                }
            }
        }

        private bool isNeedDrawCellLine(ICell icell)
        {
            if (icell == null)
            {
                return false;
            }

            MapForm mapForm = MainModel.GetInstance().MainForm.GetMapForm();

            if (icell is Cell)
            {
                return mapForm.GetCellLayer().IsVisible;
            }
            else if (icell is TDCell)
            {
                return mapForm.GetTDCellLayer().IsVisible;
            }
            else if (icell is WCell)
            {
                return mapForm.GetWCellLayer().IsVisible;
            }
            else if (icell is LTECell)
            {
                return mapForm.GetLTECellLayer().IsVisible;
            }
            else if (icell is NRCell)
            {
                return mapForm.GetNRCellLayer().IsVisible;
            }
            return true;
        }

        private DbPoint getAntennaDbPoint(MapDTLayer layer, TestPoint testPoint, ICell icell)
        {
            DbPoint antennaDbPoint = null;
            if (icell is Cell)
            {
                if (layer.MainModel.SelectedCells.Contains(icell as Cell)
                    || testPoint.Selected)
                {
                    antennaDbPoint = layer.GetGSMAntennaEndPoint(icell as Cell, testPoint.Longitude, testPoint.Latitude);
                }
            }
            else if (icell is TDCell)
            {
                if (layer.MainModel.SelectedTDCells.Contains(icell as TDCell)
                    || testPoint.Selected)
                {
                    antennaDbPoint = layer.GetTDAntennaEndPoint(icell as TDCell, testPoint.DateTime);
                }
            }
            else if (icell is WCell)
            {
                if (layer.MainModel.SelectedWCells.Contains(icell as WCell) || testPoint.Selected)
                {
                    antennaDbPoint = layer.GetWAntennaEndPoint(icell as WCell, testPoint.DateTime);
                }
            }
            else if (icell is LTECell)
            {
                antennaDbPoint = getAntennaDbPointLte(layer, testPoint, icell, antennaDbPoint);
            }
            else if (icell is NRCell)
            {
                antennaDbPoint = getAntennaDbPointNr(layer, testPoint, icell, antennaDbPoint);
            }

            return antennaDbPoint;
        }

        private DbPoint getAntennaDbPointLte(MapDTLayer layer, TestPoint testPoint, ICell icell, DbPoint antennaDbPoint)
        {
            //by klw 为啥会不一样？ 
            if (layer.MainModel.SelectedLTECells != null
                && (layer.MainModel.SelectedLTECells.Contains(icell as LTECell)
                    || (layer.MainModel.SelectedLTECells.Count == 1 && layer.MainModel.SelectedLTECells[0].ID == icell.ID))
                || testPoint.Selected)
            {
                antennaDbPoint = layer.GetLTEAntennaEndPoint(icell as LTECell, testPoint.Longitude, testPoint.Latitude);
            }

            return antennaDbPoint;
        }

        private DbPoint getAntennaDbPointNr(MapDTLayer layer, TestPoint testPoint, ICell icell, DbPoint antennaDbPoint)
        {
            if (layer.MainModel.SelectedNRCells != null && (layer.MainModel.SelectedNRCells.Contains(icell as NRCell)
                    || (layer.MainModel.SelectedNRCells.Count == 1 && layer.MainModel.SelectedNRCells[0].ID == icell.ID))
                || testPoint.Selected)
            {
                antennaDbPoint = layer.GetNRAntennaEndPoint(icell as NRCell, testPoint.Longitude, testPoint.Latitude);
            }

            return antennaDbPoint;
        }
        #endregion
        #endregion



        private void Select(MapDTLayer layer, TestPoint testPoint, float ratio, PointF point, MapOperation2 mop2, List<TestPoint> selectedTestPoints)
        {
            Color? curColor;
            int? curSize;
            int? curSymbol;
            if (!getStyle(layer, testPoint, out curColor, out curSize, out curSymbol))
            {
                return;
            }

            float radius = (float)(ratio * curSize);
            RectangleF rect = new RectangleF(point.X - radius * 8, point.Y - radius * 8, radius * 16, radius * 16);
            DbRect dRect;
            layer.GisAdapter.FromDisplay(rect, out dRect);
            if (mop2.CheckCenterInDRect(dRect))
            {
                selectedTestPoints.Add(testPoint);
            }
        }

        private void exportKml(MapDTLayer layer, TestPoint testPoint, PointF point, KMLExporter exporter, XmlElement serialInfoElement)
        {
            Color? curColor;
            int? curSize;
            int? curSymbol;
            if (!getStyle(layer, testPoint, out curColor, out curSize, out curSymbol))
            {
                return;
            }
            DbPoint dPoint;
            layer.GisAdapter.FromDisplay(point, out dPoint);

            exporter.AddTestPoint(serialInfoElement, (Color)curColor, dPoint, testPoint.DateTime, layer.GetTestPointKMLDesctription(testPoint));
        }

        public bool getStyle(MapDTLayer layer, TestPoint testPoint, out Color? color, out int? size, out int? symbol)
        {
            color = null; 
            size = null; 
            symbol = null;
            bool isValid = setColor(layer, testPoint, ref color, ref size, ref symbol);
            if (!isValid)
            {
                return false;
            }
            isValid = setSize(testPoint, ref color, ref size, ref symbol);
            if (!isValid)
            {
                return false;
            }
            isValid = setSymbol(testPoint, ref color, ref size, ref symbol);
            if (!isValid)
            {
                return false;
            }
            if (color == null || size == null || size <= 0 || symbol == null || symbol < 0 || symbol >= SymbolManager.GetInstance().Count)
            {
                return false;
            }
            return true;
        }

        private bool setColor(MapDTLayer layer, TestPoint testPoint, ref Color? color, ref int? size, ref int? symbol)
        {
            if (ColorParamEnabled)
            {
                if (OwnColorFuncDesc != null && OwnColorFuncDesc != "")
                {
                    color = layer.GetColorFromOwnFunc(testPoint, OwnColorFuncDesc, OwnColorFuncRanges, OwnFuncParam);
                    if (color == Color.Empty)
                    {
                        color = this.Color;
                        size = this.Size;
                        symbol = this.Symbol;
                        return false;
                    }
                }
                else
                {
                    if (ColorDisplayParam == null)
                    {
                        color = this.Color;
                        size = this.Size;
                        symbol = this.Symbol;
                        return false;
                    }
                    color = ColorDisplayParam.Info.GetColor(getValue(testPoint, ColorDisplayParam));
                }
            }
            else
            {
                color = this.Color;
            }

            return true;
        }

        private bool setSize(TestPoint testPoint, ref Color? color, ref int? size, ref int? symbol)
        {
            if (SizeParamEnabled)
            {
                if (SizeDisplayParam == null)
                {
                    color = this.Color;
                    size = this.Size;
                    symbol = this.Symbol;
                    return false;
                }
                size = SizeDisplayParam.Info.GetSize(getValue(testPoint, SizeDisplayParam));
            }
            else
            {
                size = this.Size;
            }

            return true;
        }

        private bool setSymbol(TestPoint testPoint, ref Color? color, ref int? size, ref int? symbol)
        {
            if (SymbolParamEnabled)
            {
                if (SymbolDisplayParam == null)
                {
                    color = this.Color;
                    size = this.Size;
                    symbol = this.Symbol;
                    return false;
                }
                symbol = SymbolDisplayParam.Info.GetSymbol(getValue(testPoint, SymbolDisplayParam));
            }
            else
            {
                symbol = this.Symbol;
            }

            return true;
        }

        private void getExStyle(MainModel mainModel, TestPoint tp, ref int? symbol)
        {
            if (mainModel.CellCvrRngItem != null)
            {
                ZTCellCoverageRangeAnaItem cvrItem = mainModel.CellCvrRngItem;
                if (cvrItem.NbTpItem.TpList.Contains(tp))
                {
                    symbol = 2;
                }
            }
        }

        Pen scanNbCellPen = new Pen(Color.Gray, 2);

        private float Calvalue(TestPoint testPoint, int nbI)
        {
            try
            {
                if (nbI == -1)
                {
                    object v = null;
                    if (testPoint is TestPointDetail)
                    {
                        v = testPoint["RxLevSub"];
                    }
                    else if (testPoint is TDTestPointDetail)
                    {
                        v = testPoint["TD_PCCPCH_RSCP"];
                    }
                    else if (testPoint is LTETestPointDetail)
                    {
                        v = testPoint["lte_RSRP"];
                    }
                    if (v != null)
                    {
                        return float.Parse(v.ToString());
                    }
                    return -999;
                }
                else
                {
                    object v = null;
                    if (testPoint is TestPointDetail)
                    {
                        v = testPoint["N_RxLev", nbI];
                    }
                    else if (testPoint is TDTestPointDetail)
                    {
                        v = testPoint["TD_NCell_PCCPCH_RSCP", nbI];
                    }
                    else if (testPoint is LTETestPointDetail)
                    {
                        v = testPoint["lte_NCell_RSRP", nbI];
                    }
                    if (v != null)
                    {
                        return float.Parse(v.ToString());
                    }
                }
                return -999;
            }
            catch { return -999; }
        }


        private Font fontCellLabel = new Font(new FontFamily("宋体"), 8);
        public bool GetNBCell(TestPoint testPoint, int i, ref ICell cell)
        {
            cell = testPoint.GetNBCell(i);
            MainModel mainModel = MainModel.GetInstance();
            if (mainModel.SystemConfigInfo.bNBLineTopN)
            {//主服相差Ndb小区连线
                return cell != null;
            }

            bool isValid = true;
            float? nRxlev;
            float? rxLev;
            switch (testPoint)
            {
                case TDTestPointSummary _:
                case TDTestPointDetail _:
                    nRxlev = (float?)(int?)testPoint["TD_NCell_PCCPCH_RSCP", i];
                    if (testPoint["TD_PCCPCH_RSCP"] is float)
                    {
                        rxLev = (float?)testPoint["TD_PCCPCH_RSCP"];
                    }
                    else
                    {
                        rxLev = (float?)(int?)testPoint["TD_PCCPCH_RSCP"];
                    }
                    if (rxLev == null || nRxlev == null || rxLev - mainModel.SystemConfigInfo.nbLineDValue > nRxlev)
                    {
                        return false;
                    }
                    break;
                case WCDMATestPointSummary _:
                case WCDMATestPointDetail _:
                    isValid = getValidNBCell(mainModel, testPoint, i, "W_SNeiRSCP", "W_TotalRSCP", false);
                    break;
                case CDMATestPointDetail _:
                case CDMATestPointSummary _:
                    isValid = getValidNBCell(mainModel, testPoint, i, "CD_NS_Ec", "CD_TotalEc", false);
                    break;
                case ScanTestPoint_G _:
                    isValid = getValidNBCell(mainModel, testPoint, i, "GSCAN_RxLev", "GSCAN_RxLev", true);
                    break;
                case ScanTestPoint_TD _:
                    isValid = getValidNBCell(mainModel, testPoint, i, "TDS_PCCPCH_RSCP", "TDS_PCCPCH_RSCP", true);
                    break;
                case LTETestPointDetail _:
                    isValid = getValidNBCell(mainModel, testPoint, i, "lte_NCell_RSRP", "lte_RSRP", false);
                    break;
                case ScanTestPoint_LTE _:
                case ScanTestPoint_NBIOT _:
                    isValid = getValidNBCell(mainModel, testPoint, i, "LTESCAN_TopN_PSS_RP", "LTESCAN_TopN_PSS_RP", true);
                    break;
                default:
                    nRxlev = (float?)(short?)testPoint["N_RxLev", i];
                    rxLev = (float?)(short?)testPoint["RxLevSub"];
                    if (rxLev == null || nRxlev == null || rxLev - mainModel.SystemConfigInfo.nbLineDValue > nRxlev)
                    {
                        return false;
                    }
                    break;
            }
            if (!isValid)
            {
                return false;
            }

            return cell != null;
        }

        private bool getValidNBCell(MainModel mainModel, TestPoint testPoint, int nIndex, string nRxlevStr, string rxLevStr, 
            bool isScan)
        {
            float? nRxlev = (float?)testPoint[nRxlevStr, nIndex];
            float? rxLev;
            if (isScan)
            {
                rxLev = (float?)testPoint[rxLevStr, 0];
            }
            else
            {
                rxLev = (float?)testPoint[rxLevStr];
            }
            if (rxLev == null || nRxlev == null || rxLev - mainModel.SystemConfigInfo.nbLineDValue > nRxlev)
            {
                return false;
            }
            return true;
        }

        private float? getValue(TestPoint testPoint, DTDisplayParameter displayParam)
        {
            if (displayParam == null)
            {
                return null;
            }
            if (DTParameterManager.GetInstance().CanConvertToFloat(testPoint[displayParam.Info.ParamInfo.Name, displayParam.ArrayIndex], displayParam.Info.ParamInfo.ValueType))
            {
                return DTParameterManager.GetInstance().ConvertToFloat(testPoint[displayParam.Info.ParamInfo.Name, displayParam.ArrayIndex], displayParam.Info.ParamInfo.ValueType);
            }
            return null;
        }

        internal DTParameterRange GetBelongRefRange(TestPoint tp)
        {
            float? value = getValue(tp, ColorDisplayParam);
            if (value != null)
            {
                return ColorDisplayParam.Info.GetParameterRange(value);
            }
            return null;
        }
    }

    [Serializable()]
    public class MapEventInfo
    {
        public bool Visible { get; set; } = true;

        public int EventID
        {
            get { return eventID; }
            set { eventID = value; e = null; }
        }


        public EventInfo Event
        {
            get
            {
                if (e == null)
                {
                    e = EventInfoManager.GetInstance()[eventID];
                }
                return e;
            }
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["Visible"] = Visible;
                param["EventID"] = EventID;
                return param;
            }
            set
            {
                Visible = (bool)value["Visible"];
                EventID = (int)value["EventID"];
            }
        }

        [NonSerialized]
        private DTDataArrowManager dtDataArrowManager;
        public void dtDataArrowManagerClear()
        {
            dtDataArrowManager = null;
        }

        public float getOffsetRatio(float ratio)
        {
            if (ratio < 0.02)
            {
                return 0.1f;
            }
            else if (ratio < 0.03)
            {
                return 0.3f;
            }
            else if (ratio < 0.035)
            {
                return 0.6f;
            }
            else if (ratio < 0.05)
            {
                return 0.8f;
            }
            return 1.0f;
        }

        public void Draw(MapDTLayer layer, float ratio, DbRect updateRect, Graphics graphics)
        {
            if (Visible)
            {
                if (dtDataArrowManager == null)
                {
                    dtDataArrowManager = new DTDataArrowManager();
                    dtDataArrowManager.Visible = DTArrowVisible;
                }
                foreach (DTFileDataManager fileDataManager in layer.MainModel.DTDataManager.FileDataManagers)
                {
                    dealValidFileData(layer, ratio, updateRect, graphics, fileDataManager);
                }
                if (layer.SampledTestPoints.Count < 1 &&
                    (!layer.MainModel.DrawHandoverSerialNum && layer.CurMapType == LayerBase.LayerMapType.MTGis) || layer.CurMapType == LayerBase.LayerMapType.Google)
                {
                    dtDataArrowManager.Draw(graphics);
                }
            }
        }

        private void dealValidFileData(MapDTLayer layer, float ratio, DbRect updateRect, Graphics graphics, DTFileDataManager fileDataManager)
        {
            List<Event> showEv = new List<Event>();
            if (layer.MainModel.VisibleOffsetManager.IsFileVisble(fileDataManager.FileID) &&
                layer.MainModel.VisibleOffsetManager.IsProjectVisble(fileDataManager.ProjectType) &&
                layer.MainModel.VisibleOffsetManager.IsServiceVisble(fileDataManager.ServiceType))
            {
                dealFileDataManagerEvents(layer, ratio, updateRect, graphics, fileDataManager, showEv);

                if (dtDataArrowManager.Visible && layer.SampledTestPoints.Count < 1)
                {
                    dtDataArrowManager.Add(fileDataManager, layer.GisAdapter);
                    dtDataArrowManager[fileDataManager.FileID].SetShowEvents(showEv);
                }
            }
            else
            {
                if (dtDataArrowManager.Visible && layer.SampledTestPoints.Count < 1)
                {
                    dtDataArrowManager.Remove(fileDataManager);
                }
            }
        }

        private void dealFileDataManagerEvents(MapDTLayer layer, float ratio, DbRect updateRect, Graphics graphics, DTFileDataManager fileDataManager, List<Event> showEv)
        {
            foreach (Event evt in fileDataManager.Events)
            {
                if (evt.ID == EventID && (MainModel.GetInstance().CurMS == 0 || evt.MS == MainModel.GetInstance().CurMS))
                {
                    bool inRegion = evt.Within(updateRect.x1, updateRect.y1, updateRect.x2, updateRect.y2);
                    if (inRegion)
                    {
                        PointF point = dealOffsetEvt(layer, evt);
                        //DbPoint dPoint = new DbPoint(evt.Longitude, evt.Latitude);
                        //PointF point;
                        //layer.GisAdapter.ToDisplay(dPoint, out point);
                        Draw(layer, evt, ratio, point, graphics);
                        showEv.Add(evt);
                    }
                }
            }
        }

        private PointF dealOffsetEvt(MapDTLayer layer, Event evt)
        {
            DbPoint dPoint = new DbPoint(evt.Longitude, evt.Latitude);
            PointF point;
            layer.GisAdapter.ToDisplay(dPoint, out point);
            int curOffsetX = 0;
            int curOffsetY = 0;
            layer.MainModel.VisibleOffsetManager.GetOffset(ref curOffsetX, ref curOffsetY, evt.FileID, evt.FileInfo.ProjectID, evt.FileInfo.ServiceType);
            point += new Size(curOffsetX, curOffsetY);
            return point;
        }

        public void Select(MapDTLayer layer, float ratio, MapOperation2 mop2, List<Event> selectedEvents)
        {
            if (Visible)
            {
                for (int i = layer.MainModel.DTDataManager.FileDataManagers.Count - 1; i >= 0; i--)
                {
                    DTFileDataManager fileDataManager = layer.MainModel.DTDataManager.FileDataManagers[i];
                    if (layer.MainModel.VisibleOffsetManager.IsFileVisble(fileDataManager.FileID))
                    {
                        for (int j = fileDataManager.Events.Count - 1; j >= 0; j--)
                        {
                            Event evt = fileDataManager.Events[j];
                            if (selectedEvents.Count == 1)
                            {
                                return;
                            }
                            selectValidEvt(layer, ratio, mop2, selectedEvents, evt);
                        }
                    }
                }
            }
        }

        private void selectValidEvt(MapDTLayer layer, float ratio, MapOperation2 mop2, List<Event> selectedEvents, Event evt)
        {
            if (evt.ID == EventID && (MainModel.GetInstance().CurMS == 0 || evt.MS == MainModel.GetInstance().CurMS))
            {
                bool isProjectVisble = layer.MainModel.VisibleOffsetManager.IsProjectVisble(evt.ProjectType);
                bool isServiceVisble = layer.MainModel.VisibleOffsetManager.IsServiceVisble(evt.ProjectType);
                if (isProjectVisble && isServiceVisble)
                {
                    DbPoint dPoint = new DbPoint(evt.Longitude, evt.Latitude);
                    PointF point;
                    layer.GisAdapter.ToDisplay(dPoint, out point);
                    Select(layer, evt, ratio, point, mop2, selectedEvents);
                }
            }
        }

        public void ExportKml(MapDTLayer layer, KMLExporter exporter, XmlElement eventInfosElement)
        {
            if (Visible)
            {
                XmlElement eventInfoElement = exporter.CreateFolder(Event.Name, false);
                eventInfosElement.AppendChild(eventInfoElement);
                exporter.AddEventStyle(eventInfoElement, "");
                foreach (DTFileDataManager fileDataManager in layer.MainModel.DTDataManager.FileDataManagers)
                {
                    if (layer.MainModel.VisibleOffsetManager.IsFileVisble(fileDataManager.FileID))
                    {
                        exportValidKml(layer, exporter, eventInfoElement, fileDataManager);
                    }
                }
            }
        }

        private void exportValidKml(MapDTLayer layer, KMLExporter exporter, XmlElement eventInfoElement, DTFileDataManager fileDataManager)
        {
            foreach (Event evt in fileDataManager.Events)
            {
                if (evt.ID == EventID)
                {
                    bool isProjectVisble = layer.MainModel.VisibleOffsetManager.IsProjectVisble(evt.ProjectType);
                    bool isServiceVisble = layer.MainModel.VisibleOffsetManager.IsServiceVisble(evt.ProjectType);
                    if (isProjectVisble && isServiceVisble)
                    {
                        DbPoint dPoint = new DbPoint(evt.Longitude, evt.Latitude);
                        PointF point;
                        layer.GisAdapter.ToDisplay(dPoint, out point);
                        exportKml(layer, evt, point, exporter, eventInfoElement);
                    }
                }
            }
        }

        private void exportKml(MapDTLayer layer, Event e, PointF point, KMLExporter exporter, XmlElement eventInfoElement)
        {
            DbPoint dPoint;
            layer.GisAdapter.FromDisplay(point, out dPoint);
            string description = EventInfoManager.GetInstance()[e.ID].Name;
            description += "\n" + layer.GetTestPointKMLDesctription(getTestPoint(layer.MainModel.DTDataManager, e));
            exporter.AddEvent(eventInfoElement, dPoint, "", e.DateTime, description);
        }

        private void Draw(MapDTLayer layer, Event e, float ratio, PointF point, Graphics graphics)
        {
            float radius = ratio * 40;
            RectangleF rect = new RectangleF(point.X - radius * layer.EventImgSize.Width / 2
                , point.Y - radius * layer.EventImgSize.Height / 2
                , radius * layer.EventImgSize.Width, radius * layer.EventImgSize.Height);

            if (layer.MainModel.IsDrawEventResult && e.Tag is EventResult)
            {
                drawEventByESResult(layer, e, ratio, point, graphics);
            }
            else
            {
                drawEventByImage(layer, e, graphics, rect);
            }

            if (layer.CurMapType == LayerBase.LayerMapType.MTGis)
            {
                drawEventCellLine(layer, e, point, graphics);
                drawMTGisEventDateLabel(layer, e, graphics, rect);
                drawEventWithHOSequence(layer, e, point, graphics, rect);
            }
            else if (layer.CurMapType == LayerBase.LayerMapType.Google)
            {
                drawGoogleEventDateLabel(layer, e, graphics, rect);
            }
        }

        private void drawEventByESResult(MapDTLayer layer, Event e, float ratio, PointF point, Graphics graphics)
        {
            EventResult er = e.Tag as EventResult;

            if (er == null || er.Tag == null || !(er.Tag is MasterCom.ES.ColorManager.EventColorItem))
            {
                return;
            }
            MasterCom.ES.ColorManager.EventColorItem eci = er.Tag as MasterCom.ES.ColorManager.EventColorItem;
            float radius = ratio * 40;
            RectangleF rect = new RectangleF(point.X - radius * eci.Size, point.Y - radius * eci.Size, radius * 2 * eci.Size, radius * 2 * eci.Size);
            Brush brush = new SolidBrush(eci.Color);
            graphics.FillEllipse(brush, rect);

            if (e.Selected)
            {
                graphics.DrawRectangle(layer.PenSelected, rect.X, rect.Y, rect.Width, rect.Height);
            }
        }

        private void drawEventByImage(MapDTLayer layer, Event e, Graphics graphics, RectangleF rect)
        {
            if (e.EventInfo == null || e.EventInfo.Image == null)
            {
                return;
            }

            graphics.DrawImage(e.EventInfo.Image, rect);

            if (e.Selected)
            {
                graphics.DrawRectangle(layer.PenSelected, rect.X, rect.Y, rect.Width, rect.Height);
            }
        }

        private void drawEventCellLine(MapDTLayer layer, Event e, PointF point, Graphics graphics)
        {
            if (e.Selected)
            {
                DbPoint srcAntennaPoint = null;
                DbPoint tarAntennaPoint = null;
                ICell srcCell = e.GetSrcCell();
                ICell tarCell = e.GetTargetCell();
                if (srcCell == null)
                {
                    return;
                }
                srcAntennaPoint = getSrcAntennaPoint(layer, e, srcAntennaPoint, srcCell);
                //===target cell
                tarAntennaPoint = getTarAntennaPoint(layer, e, tarAntennaPoint, tarCell);

                if (tarAntennaPoint != null)
                {
                    PointF pointCell;
                    layer.GisAdapter.ToDisplay(tarAntennaPoint, out pointCell);
                    Pen arrowPen = new Pen(Color.Lime, 3);
                    arrowPen.CustomEndCap = lineCap;
                    graphics.DrawLine(arrowPen, point, pointCell);
                }

                if (srcAntennaPoint != null)
                {
                    PointF pointCell;
                    layer.GisAdapter.ToDisplay(srcAntennaPoint, out pointCell);
                    Pen arrowPen = new Pen(Color.DarkOrange, 3);
                    if (tarAntennaPoint != null)
                    {
                        arrowPen.CustomEndCap = lineCap;
                    }
                    graphics.DrawLine(arrowPen, pointCell, point);
                }
            }
        }

        private DbPoint getSrcAntennaPoint(MapDTLayer layer, Event e, DbPoint srcAntennaPoint, ICell srcCell)
        {
            if (srcCell is LTECell)
            {
                srcAntennaPoint = layer.GetLTEAntennaEndPoint(srcCell as LTECell, e.Longitude, e.Latitude);
            }
#if LT
            else if (srcCell is WCell)
            {
                srcAntennaPoint = layer.GetWAntennaEndPoint(srcCell as WCell, e.DateTime);
            }
#else
            else if (srcCell is TDCell)
            {
                srcAntennaPoint = layer.GetTDAntennaEndPoint(srcCell as TDCell, e.DateTime);
            }
#endif
            else if (srcCell is Cell)
            {
                srcAntennaPoint = layer.GetGSMAntennaEndPoint(srcCell as Cell, e.Longitude, e.Latitude);
            }

            return srcAntennaPoint;
        }

        private DbPoint getTarAntennaPoint(MapDTLayer layer, Event e, DbPoint tarAntennaPoint, ICell tarCell)
        {
            if (tarCell != null)
            {
                if (tarCell is LTECell)
                {
                    tarAntennaPoint = layer.GetLTEAntennaEndPoint(tarCell as LTECell, e.Longitude, e.Latitude);
                }
#if LT
                else if (tarCell is WCell)
                {
                    tarAntennaPoint = layer.GetWAntennaEndPoint(tarCell as WCell, e.DateTime);
                }
#else
                else if (tarCell is TDCell)
                {
                    tarAntennaPoint = layer.GetTDAntennaEndPoint(tarCell as TDCell, e.DateTime);
                }
#endif
                else if (tarCell is Cell)
                {
                    tarAntennaPoint = layer.GetGSMAntennaEndPoint(tarCell as Cell, e.Longitude, e.Latitude);
                }
            }

            return tarAntennaPoint;
        }

        private void drawMTGisEventDateLabel(MapDTLayer layer, Event e, Graphics graphics, RectangleF rect)
        {
            if (!layer.MainModel.DrawEventDateLbl)//是否显示事件时间
            {
                return;
            }
            Color bgColor = Color.Empty;
            switch (e.DateTime.Month)
            {
                case 1:
                    bgColor = Color.FromArgb(255, Color.LightBlue);
                    break;
                case 2:
                    bgColor = Color.FromArgb(255, Color.LightCoral);
                    break;
                case 3:
                    bgColor = Color.FromArgb(255, Color.LightCyan);
                    break;
                case 4:
                    bgColor = Color.FromArgb(255, Color.LightGoldenrodYellow);
                    break;
                case 5:
                    bgColor = Color.FromArgb(255, Color.LightGray);
                    break;
                case 6:
                    bgColor = Color.FromArgb(255, Color.LightGreen);
                    break;
                case 7:
                    bgColor = Color.FromArgb(255, Color.LightPink);
                    break;
                case 8:
                    bgColor = Color.FromArgb(255, Color.LightSalmon);
                    break;
                case 9:
                    bgColor = Color.FromArgb(255, Color.Yellow);
                    break;
                case 10:
                    bgColor = Color.FromArgb(255, Color.LightSkyBlue);
                    break;
                case 11:
                    bgColor = Color.FromArgb(255, Color.LightSteelBlue);
                    break;
                case 12:
                    bgColor = Color.FromArgb(255, Color.LightYellow);
                    break;
                default:
                    break;
            }
            // bgColor = Color.FromArgb(100, 0, (int)(e.DateTime.Month / 12.0 * 255), (int)(e.DateTime.Month / 12.0 * 255))
            Color txtColor = Color.Black; //Color.FromArgb(255 - bgColor.R, 255 - bgColor.G, 255 - bgColor.B);//标签背景的反色
            System.Drawing.Font fontMeasure = new System.Drawing.Font(new FontFamily("宋体"), 10, FontStyle.Bold);
            string lblTxt = e.DateTime.ToString(layer.MainModel.EventDateLblTxtFormat);
            SizeF size = graphics.MeasureString(lblTxt, fontMeasure);
            graphics.FillRectangle(new SolidBrush(bgColor), rect.Right, rect.Top, size.Width, size.Height - 4);
            graphics.DrawString(lblTxt, fontMeasure, new SolidBrush(txtColor), rect.Right, rect.Top);
        }

        private void drawGoogleEventDateLabel(MapDTLayer layer, Event e, Graphics graphics, RectangleF rect)
        {
            if (!layer.MainModel.DrawEventDateLbl)//是否显示事件时间
            {
                return;
            }
            Color bgColor = Color.Empty;
            switch (e.DateTime.Month)
            {
                case 1:
                    bgColor = Color.FromArgb(150, Color.PeachPuff);
                    break;
                case 2:
                    bgColor = Color.FromArgb(150, Color.RosyBrown);
                    break;
                case 3:
                    bgColor = Color.FromArgb(150, Color.Yellow);
                    break;
                case 4:
                    bgColor = Color.FromArgb(150, Color.Chocolate);
                    break;
                case 5:
                    bgColor = Color.FromArgb(150, Color.YellowGreen);
                    break;
                case 6:
                    bgColor = Color.FromArgb(150, Color.Goldenrod);
                    break;
                case 7:
                    bgColor = Color.FromArgb(150, Color.Khaki);
                    break;
                case 8:
                    bgColor = Color.FromArgb(150, Color.SkyBlue);
                    break;
                case 9:
                    bgColor = Color.FromArgb(150, Color.Wheat);
                    break;
                case 10:
                    bgColor = Color.FromArgb(150, Color.Orange);
                    break;
                case 11:
                    bgColor = Color.FromArgb(150, Color.Silver);
                    break;
                case 12:
                    bgColor = Color.FromArgb(150, Color.SlateBlue);
                    break;
                default:
                    break;
            }
            // bgColor = Color.FromArgb(100, 0, (int)(e.DateTime.Month / 12.0 * 255), (int)(e.DateTime.Month / 12.0 * 255))
            Color txtColor = Color.Red; //Color.FromArgb(255 - bgColor.R, 255 - bgColor.G, 255 - bgColor.B);//标签背景的反色
            System.Drawing.Font fontMeasure = new System.Drawing.Font(new FontFamily("宋体"), 10, FontStyle.Bold);
            string lblTxt = e.DateTime.ToString(layer.MainModel.EventDateLblTxtFormat);
            SizeF size = graphics.MeasureString(lblTxt, fontMeasure);
            graphics.FillRectangle(new SolidBrush(bgColor), rect.Right, rect.Top, size.Width, size.Height - 4);
            graphics.DrawString(lblTxt, fontMeasure, new SolidBrush(txtColor), rect.Right, rect.Top);
        }

        private void drawEventWithHOSequence(MapDTLayer layer, Event e, PointF point, Graphics graphics, RectangleF rect)
        {
            if (layer.MainModel.DrawHandoverSerialNum && e.XHInFile != -1)
            {
                System.Drawing.Font fontMeasure = new System.Drawing.Font(new FontFamily("宋体"), 10, FontStyle.Bold);
                string lblTxt = e.XHInFile.ToString();
                SizeF size = graphics.MeasureString(lblTxt, fontMeasure);
                graphics.FillRectangle(new SolidBrush(Color.Yellow), rect.Right, rect.Top, size.Width, size.Height - 4);
                graphics.DrawString(lblTxt, fontMeasure, new SolidBrush(Color.Red), rect.Right, rect.Top);
            }

            if ((e.Selected) || (layer.MainModel.DrawHandoverSerialNum && e.XHInFile != -1))
            {
                if (e.Longitude <= 0 || e.Latitude <= 0)
                {
                    return;
                }
                drawCellSerialNumLine(layer, e, point, graphics);
                drawTargetCellSerialNumLine(layer, e, point, graphics);
            }
        }

        private void drawCellSerialNumLine(MapDTLayer layer, Event e, PointF point, Graphics graphics)
        {
            Cell cell = layer.MainModel.CellManager.GetNearestCell(e.DateTime, (ushort?)(int?)e["LAC"], (ushort?)(int?)e["CI"], (short?)e["BCCH"], (byte?)e["BSIC"], e.Longitude, e.Latitude);
            if (cell != null)
            {
                DbPoint dPoint = null;
                if (cell.Antennas.Count > 0)
                {
                    dPoint = new DbPoint(cell.EndPointLongitude, cell.EndPointLatitude);
                }
                else
                {
                    dPoint = new DbPoint(cell.Longitude, cell.Latitude);
                }
                PointF pointCell;
                layer.GisAdapter.ToDisplay(dPoint, out pointCell);
                if (layer.MainModel.ServerCells.Contains(cell))
                {
                    Pen pen = layer.MainModel.ServerCellPens[layer.MainModel.ServerCells.IndexOf(cell)];
                    if (pointCell.X > -10000 && pointCell.X < 10000 && pointCell.Y > -10000 && pointCell.Y < 10000)
                    {
                        graphics.DrawLine(pen, point.X, point.Y, pointCell.X, pointCell.Y);
                    }
                }
            }
        }

        private void drawTargetCellSerialNumLine(MapDTLayer layer, Event e, PointF point, Graphics graphics)
        {
            if (e["TargetLAC"] != null && e["TargetCI"] != null)
            {
                Cell cell = layer.MainModel.CellManager.GetCell(e.DateTime, (ushort)(int)e["TargetLAC"], (ushort)(int)e["TargetCI"]);
                if (cell != null && cell.GetDistance(e.Longitude, e.Latitude) < CD.MAX_COV_DISTANCE_GSM)
                {
                    DbPoint dPoint = new DbPoint(cell.EndPointLongitude, cell.EndPointLatitude);
                    PointF pointCell;
                    layer.GisAdapter.ToDisplay(dPoint, out pointCell);
                    if (pointCell.X > -10000 && pointCell.X < 10000 && pointCell.Y > -10000 && pointCell.Y < 10000)
                    {
                        graphics.DrawLine(layer.NeighbourPens[0], point.X, point.Y, pointCell.X, pointCell.Y);
                    }
                }
            }
        }

        private void Select(MapDTLayer layer, Event e, float ratio, PointF point, MapOperation2 mop2, List<Event> selectedEvents)
        {
            float radius = ratio * 40;
            RectangleF rect;
            if (layer.MainModel.IsDrawEventResult && e.Tag is EventResult && ((EventResult)e.Tag).Tag is MasterCom.ES.ColorManager.EventColorItem)
            {
                MasterCom.ES.ColorManager.EventColorItem eci = ((EventResult)e.Tag).Tag as MasterCom.ES.ColorManager.EventColorItem;
                rect = new RectangleF(point.X - radius * eci.Size, point.Y - radius * eci.Size, radius * 2 * eci.Size, radius * 2 * eci.Size);
            }
            else
            {
                rect = new RectangleF(point.X - radius * 8, point.Y - radius * 8, radius * 16, radius * 16);
            }
            DbRect dRect;
            layer.GisAdapter.FromDisplay(rect, out dRect);
            if (mop2.CheckCenterInDRect(dRect))
            {
                selectedEvents.Add(e);
            }
        }

        AdjustableArrowCap lineCap = new System.Drawing.Drawing2D.AdjustableArrowCap(6, 6, true);

        private TestPoint getTestPoint(DTDataManager dtDataManager, DTData dtData)
        {
            foreach (DTFileDataManager dtFileDataManager in dtDataManager.FileDataManagers)
            {
                int index = dtFileDataManager.DTDatas.IndexOf(dtData);
                if (index >= 0)
                {
                    DTData dtDataFind = null;
                    for (int i = index; i >= 0; i--)
                    {
                        dtDataFind = dtFileDataManager.DTDatas[i];
                        if (dtDataFind is TestPoint && dtDataFind.MS == dtData.MS)
                        {
                            return (TestPoint)dtDataFind;
                        }
                    }
                    return null;
                }
            }
            return null;
        }

        public bool DTArrowVisible { get; set; } = false;

        private int eventID;

        [NonSerialized()]
        private EventInfo e;
    }

    /// <summary>
    /// 导出路测数据的shp文件时用到的两个辅助方法
    /// </summary>
    static class ShapeConverter
    {
        // 线转换多边形只需将点数组闭合即可
        public static PointF[] LineToPolygon(ArrowMapElement ame)
        {
            PointF[] retPoints = new PointF[ame.BaseElement.GraphicsPath.PointCount + 2];
            PointF[] points = ame.BaseElement.GraphicsPath.PathPoints;
            for (int i = 0; i < points.Length; ++i)
            {
                retPoints[i] = new PointF(points[i].X, points[i].Y);
            }
            retPoints[5] = new PointF(retPoints[3].X, retPoints[3].Y);
            retPoints[6] = new PointF(retPoints[0].X, retPoints[0].Y);

            return retPoints;
        }
    }
}

