﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public class GSMComparePoorRxQualityRoadGrid : GSMPoorRxQualityRoadInfo
    {
        public GSMComparePoorRxQualityRoadGrid(double gridSpan, DbRect regionGrdiBounds)
        {
            this.gridSpan = gridSpan;
            this.regionGrdiBounds = regionGrdiBounds;
        }

        private List<DbRect> grids = null;
        public List<DbRect> Grids
        {
            get { return grids; }
        }
        private readonly DbRect regionGrdiBounds = null;
        private readonly double gridSpan = 0;
        public new void GetResult()
        {
            base.GetResult();
            makeGrid();
        }
        protected virtual void get_Param(TestPoint tp, out int? rxQual, out short? rxlev, out short? c2i)
        {
            rxQual = (int?)(byte?)tp["RxQualSub"];
            rxlev = (short?)tp["RxLevSub"];
            c2i = (short?)tp["C_I", 0];
        }
        public virtual void SetGridInfo(TestPoint tp, double dis)
        {
            //int? rxQual = (int?)(byte?)tp["RxQualSub"];
            //short? rxlev = (short?)tp["RxLevSub"];
            //short? C2I = (short?)tp["C_I", 0];
            int? rxQual;
            short? rxlev, C2I;
            this.get_Param(tp, out rxQual, out rxlev, out C2I);

            if (rxQual != null)
            {
                base.SetQuality((float)rxQual);
            }
            if ((rxlev != null) && (rxlev >= -140) && (rxlev <= -10))
            {
                base.SetRxlev((float)rxlev);
            }
            if ((C2I != null) && (C2I >= -20) && (C2I <= 25))
            {
                base.SetC2I((float)C2I);
            }
            base.distance += dis;
            base.sampleLst.Add(tp);
        }

        List<string> gridIdxList = null;
        private void makeGrid()
        {
            gridIdxList = new List<string>(sampleLst.Count);
            grids = new List<DbRect>();
            foreach (TestPoint tp in sampleLst)
            {
                int colIdx = 0;
                int rowIdx = 0;
                getGridIdx(tp, out colIdx, out rowIdx);
                string idxStr = rowIdx.ToString() + "_" + colIdx.ToString();
                if (!gridIdxList.Contains(idxStr))
                {
                    gridIdxList.Add(idxStr);
                    double ltX = regionGrdiBounds.x1 + colIdx * gridSpan;
                    double ltY = regionGrdiBounds.y2 - rowIdx * gridSpan;
                    double brX = ltX + gridSpan;
                    double brY = ltY - gridSpan;
                    grids.Add(new DbRect(ltX, brY, brX, ltY));
                }
            }
        }

        protected void getGridIdx(TestPoint tp, out int colIdx, out int rowIdx)
        {
            double xDis = tp.Longitude - regionGrdiBounds.x1;
            colIdx = (int)(xDis / gridSpan);
            double yDis = regionGrdiBounds.y2 - tp.Latitude;
            rowIdx = (int)(yDis / gridSpan);
        }

        public bool CanMerge(GSMComparePoorRxQualityRoadGrid otherGrid)
        {
            foreach (TestPoint tp in otherGrid.sampleLst)
            {
                if (isInGrid(tp))
                {
                    return true;
                }
            }
            return false;
        }

        private bool isInGrid(TestPoint tp)
        {
            bool ret = false;
            if (grids != null)
            {
                int colIdx = 0;
                int rowIdx = 0;
                getGridIdx(tp, out colIdx, out rowIdx);
                string idxStr = rowIdx.ToString() + "_" + colIdx.ToString();
                ret = gridIdxList.Contains(idxStr);
            }
            return ret;
        }

    }
}
