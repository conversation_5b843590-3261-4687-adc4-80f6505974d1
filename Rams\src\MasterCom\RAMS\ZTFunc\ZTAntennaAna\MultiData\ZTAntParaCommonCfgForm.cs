﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraCharts;
using MasterCom.RAMS.Net;
using System.Xml;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTAntParaCommonCfgForm : BaseDialog
    {
        public ZTAntParaCommonCfgForm()
        {
            InitializeComponent();
            intiData();
            initListView();
        }

        Dictionary<string, AntTimeCfg> timeCfgDic = new Dictionary<string, AntTimeCfg>();
        private void intiData()
        {
            DiyAntTimeCfg diyAntTimeCfg = new DiyAntTimeCfg(MainModel);
            diyAntTimeCfg.Query();
            timeCfgDic = diyAntTimeCfg.timeCfgDic;

            string strMaxTime = "";
            foreach (string strTime in timeCfgDic.Keys)
            {
                strMaxTime = strTime;
                cbTimeCfg.Items.Add(strTime);
            }
            cbTimeCfg.Text = strMaxTime;
        }

        private Dictionary<string, List<StatIndexProblem>> problemDic = new Dictionary<string, List<StatIndexProblem>>();
        private string curIndexName="";//当前方案名称
        public List<StatIndexProblem> StatList
        {
            get
            {
                if (!problemDic.ContainsKey(curIndexName))
                    problemDic[curIndexName] = new List<StatIndexProblem>();
                return problemDic[curIndexName];
            }
        }
        private void initListView()
        {
            ReadCfgXML();
            reflashAll();
        }

        public int iFunc { get; set; } = 1;
        public AntTimeCfg timeCfg { get; set; } = new AntTimeCfg();
        private void btnOK_Click(object sender, EventArgs e)
        {
            string strDate = cbTimeCfg.Text;
            AntTimeCfg timeCfgTmp;
            if (!timeCfgDic.TryGetValue(strDate, out timeCfgTmp))
            {
                this.DialogResult = DialogResult.Cancel;
                return;
            }
            timeCfg = timeCfgTmp;

            problemDic.Remove(curIndexName);
            problemDic[curIndexName] = addStatProblem();
            SaveCfgXML();
            if (rbEasy.Checked)
                iFunc = 2;
            else if (rbDetail.Checked)
                iFunc = 3;
            else
                iFunc = 1;
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void rbAna_Click(object sender, EventArgs e)
        {
            rbAna.Checked = true;
            rbEasy.Checked = false;
            rbDetail.Checked = false;
        }

        private void rbEasy_Click(object sender, EventArgs e)
        {
            rbEasy.Checked = true;
            rbAna.Checked = false;
            rbDetail.Checked = false;
        }

        private void rbDetail_Click(object sender, EventArgs e)
        {
            rbDetail.Checked = true;
            rbEasy.Checked = false;
            rbAna.Checked = false;
        }
        private void reflashProblem()
        {
            listViewP.Items.Clear();
            if (!problemDic.ContainsKey(curIndexName))
                return;
            foreach (StatIndexProblem statP in problemDic[curIndexName])
            {
                listViewP.BeginUpdate();
                ListViewItem listitem = new ListViewItem();
                listitem.Tag = statP;
                listitem.Checked = statP.pChecked;
                listitem.Text = statP.pName;
                listViewP.Items.Add(listitem);
                listViewP.EndUpdate();
            }
        }

        private void ReadCfgXML()
        {
            problemDic = new Dictionary<string, List<StatIndexProblem>>();
            string path = Application.StartupPath + "//userData//AntParaCommonProCfg.xml";
            try
            {
                XmlDocument xd = new XmlDocument();
                xd.Load(path);
                XmlNodeList xnl = xd.SelectNodes("/AntParaCommon");
                foreach (XmlNode xl in xnl)
                {
                    XmlNodeList xnl1 = xl.SelectNodes("StatProblemFangan");
                    foreach (XmlNode xl1 in xnl1)
                    {
                        string key = xl1.Attributes["name"].Value;
                        if (xl1.Attributes["select"].Value.ToLower().Contains("true"))
                            curIndexName = key;
                        if (!problemDic.ContainsKey(key))
                            problemDic[key] = new List<StatIndexProblem>();
                        XmlNodeList xnl2 = xl1.SelectNodes("StatProblem");
                        foreach (XmlNode xl2 in xnl2)
                        {
                            addStat(key, xl2);
                        }
                    }
                }
            }
            catch
            {
                MessageBox.Show("文件读取失败！");
            }
            
        }

        private void addStat(string key, XmlNode xl2)
        {
            StatIndexProblem statItem = new StatIndexProblem();
            XmlNode XL1 = xl2.SelectSingleNode("Name");
            statItem.pName = XL1.InnerText;
            XmlNode XL2 = xl2.SelectSingleNode("Type");
            statItem.ptype = XL2.InnerText;
            XmlNode XL3 = xl2.SelectSingleNode("Checked");
            statItem.pChecked = (XL3.InnerText.ToLower().Contains("true"));
            XmlNodeList xnl3 = xl2.SelectNodes("Formula");
            foreach (XmlNode xl3 in xnl3)
            {
                StatIndexCondition con = new StatIndexCondition();
                XmlNode XL33 = xl3.SelectSingleNode("SFormula");
                con.Formula = XL33.InnerText;
                XmlNode XL31 = xl3.SelectSingleNode("Tips");
                con.tip = XL31.InnerText;
                statItem.conditionList.Add(con);
            }
            problemDic[key].Add(statItem);
        }

        private void SaveCfgXML()
        {
            try
            {
                string path = Application.StartupPath + "//userData//AntParaCommonProCfg.xml";
                XmlDocument xd = new XmlDocument();
                xd.CreateXmlDeclaration("1.0", "utf-8", "yes");
                XmlNode first = xd.CreateElement("AntParaCommon");
                foreach (string key in problemDic.Keys)
                {
                    XmlNode root1 = xd.CreateElement("StatProblemFangan");
                    XmlAttribute xa = xd.CreateAttribute("name");
                    xa.Value = key;
                    root1.Attributes.Append(xa);
                    XmlAttribute xa1 = xd.CreateAttribute("select");
                    xa1.Value = (key == curIndexName).ToString();
                    root1.Attributes.Append(xa1);
                    foreach (StatIndexProblem statItem in problemDic[key])
                    {
                        XmlNode root = xd.CreateElement("StatProblem");
                        XmlNode xn3 = xd.CreateElement("Name");
                        xn3.InnerText = statItem.pName;
                        root.AppendChild(xn3);
                        XmlNode xn2 = xd.CreateElement("Type");
                        xn2.InnerText = statItem.ptype;
                        root.AppendChild(xn2);
                        XmlNode xn7 = xd.CreateElement("Checked");
                        xn7.InnerText = statItem.pChecked.ToString();
                        root.AppendChild(xn7);
                        foreach (StatIndexCondition statIndex in statItem.conditionList)
                        {
                            XmlNode root2 = xd.CreateElement("Formula");
                            XmlNode xn5 = xd.CreateElement("SFormula");
                            xn5.InnerText = statIndex.Formula;
                            root2.AppendChild(xn5);
                            XmlNode xn1 = xd.CreateElement("Tips");
                            xn1.InnerText = statIndex.tip;
                            root2.AppendChild(xn1);
                            root.AppendChild(root2);
                        }
                        root1.AppendChild(root);
                    }
                    first.AppendChild(root1);
                }
                xd.AppendChild(first);
                xd.Save(path);
            }
            catch
            {
                MessageBox.Show("文件写入失败！");
            }
        }

        private List<StatIndexProblem> addStatProblem()
        {
            List<StatIndexProblem> list = new List<StatIndexProblem>();
            foreach (ListViewItem item in listViewP.Items)
            {
                StatIndexProblem stat = item.Tag as StatIndexProblem;
                list.Add(stat);
            }
            return list;
        }

        private void 添加指标分析ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ZTAddProblemCfgForm addP = new ZTAddProblemCfgForm();
            if(addP.ShowDialog() ==System.Windows.Forms.DialogResult.OK)
            {
                StatIndexProblem tmp = addP.statIndexProblem;
                if (!problemDic.ContainsKey(curIndexName))
                    problemDic[curIndexName] = new List<StatIndexProblem>();
                problemDic[curIndexName].Add(tmp);
                reflashProblem();
            }
        }

        private void 删除指标分析ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (listViewP.SelectedItems.Count == 0)
                return;
            if (!problemDic.ContainsKey(curIndexName))
                return;
            StatIndexProblem statP = listViewP.SelectedItems[0].Tag as StatIndexProblem;
            problemDic[curIndexName].Remove(statP);
            reflashProblem();
        }

        private void 修改指标分析ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            Modity();
        }

        private void listViewP_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            Modity();
        }
        private void Modity()
        {
            if (listViewP.SelectedItems.Count == 0)
                return;
            ZTAddProblemCfgForm addP = new ZTAddProblemCfgForm();
            StatIndexProblem statP = listViewP.SelectedItems[0].Tag as StatIndexProblem;
            addP.Apply(statP);
            if (addP.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                StatIndexProblem tmp = addP.statIndexProblem;
                if (!problemDic.ContainsKey(curIndexName))
                    problemDic[curIndexName] = new List<StatIndexProblem>();
                int index = problemDic[curIndexName].IndexOf(statP);
                problemDic[curIndexName].Remove(statP);
                problemDic[curIndexName].Insert(index,tmp);
                reflashProblem();
            }
        }

        private void listViewP_ItemCheck(object sender, ItemCheckEventArgs e)
        {
            StatIndexProblem statP = listViewP.Items[e.Index].Tag as StatIndexProblem;
            statP.pChecked = !listViewP.Items[e.Index].Checked;
        }

        private void btnaddItem_Click(object sender, EventArgs e)
        {
            if (tbfangan.Text != "")
            {
                problemDic[tbfangan.Text] = addStatProblem();
                curIndexName = tbfangan.Text;
            }
            else
            {
                MessageBox.Show("请填写方案名称");
                return;
            }
            SaveCfgXML();
            initListView();
            tbfangan.Text = "";
        }

        private void reflashAll()
        {
            reflashCmb();
            reflashProblem();
        }

        private void reflashCmb()
        {
            cmbItem.Items.Clear();
            foreach (string key in problemDic.Keys)
                cmbItem.Items.Add(key);
            if (cmbItem.Items.Contains(curIndexName))
                cmbItem.SelectedIndex = cmbItem.Items.IndexOf(curIndexName);
        }

        private void btncutItem_Click(object sender, EventArgs e)
        {
            if (problemDic.ContainsKey(curIndexName))
            {
                problemDic.Remove(curIndexName);
                reflashAll();
            }
        }

        private void cmbItem_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (problemDic.ContainsKey(cmbItem.Text))
            {
                if (problemDic.ContainsKey(curIndexName))
                {
                    List<StatIndexProblem> statProblem = addStatProblem();
                    if (statProblem.Count > 0)
                        problemDic[curIndexName] = statProblem;
                }
                curIndexName = cmbItem.Text;
            }
            reflashProblem();
        }

        private void btndown_Click(object sender, EventArgs e)
        {
            if (listViewP.SelectedItems.Count == 0)
                return;
            listViewP.BeginUpdate();
            foreach (ListViewItem var in listViewP.SelectedItems)
            {
                int indexSelectedItem = var.Index;
                if (indexSelectedItem == listViewP.Items.Count-1)
                    break;
                listViewP.Items.RemoveAt(indexSelectedItem);
                listViewP.Items.Insert(indexSelectedItem + 1, var);
            }
            listViewP.EndUpdate();
        }

        private void btnup_Click(object sender, EventArgs e)
        {
            if (listViewP.SelectedItems.Count == 0)
                return;
            listViewP.BeginUpdate();
            foreach (ListViewItem var in listViewP.SelectedItems)
            {
                int indexSelectedItem = var.Index;
                if (indexSelectedItem == 0)
                    break;
                listViewP.Items.RemoveAt(indexSelectedItem);
                listViewP.Items.Insert(indexSelectedItem - 1, var);
            }
            listViewP.EndUpdate();
        }
    }

    public class StatIndexProblem
    {
        public string pName { get; set; } = "";
        public string ptype { get; set; } = "";
        public bool pChecked { get; set; } = false;
        public List<StatIndexCondition> conditionList { get; set; } = new List<StatIndexCondition>();
    }

    public class StatIndexCondition
    {
        public string Formula { get; set; } = "";
        public string tip { get; set; } = "";
    }
}
