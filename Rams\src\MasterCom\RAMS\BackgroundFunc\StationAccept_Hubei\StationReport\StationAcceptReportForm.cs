﻿using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.BackgroundFunc
{
    public partial class StationAcceptReportForm : MinCloseForm
    {
        private List<string> filesPath = new List<string>();
        public StationAcceptReportForm()
        {
            InitializeComponent();
        }

        public void FillData(List<StationAcceptReportInfo> reportInfo)
        {
            gridControlReportInfo.DataSource = reportInfo;
            gridControlReportInfo.RefreshDataSource();
        }

        private void miDownloadSelFiles_Click(object sender, EventArgs e)
        {
            downloadFile(false);
        }

        private void fillSelectedByList()
        {
            foreach (string filePath in getSelectedFiles())
            {
                filesPath.Add(filePath);
            }
        }

        private List<string> getSelectedFiles()
        {
            List<string> filePathList = new List<string>();
            foreach (int handle in gridViewReportInfo.GetSelectedRows())
            {
                //DataRow dr = gridViewReportInfo.GetDataRow(handle);
                string file = gridViewReportInfo.GetRowCellValue(handle, gridColReportPath) as string;
                if (!string.IsNullOrEmpty(file))
                {
                    filePathList.Add(file);
                }
            }
            return filePathList;
        }

        private void miDownloadAllFiles_Click(object sender, EventArgs e)
        {
            downloadFile(true);
        }

        private void fillAllByList()
        {
            for (int i = 0; i < gridViewReportInfo.RowCount; i++)
            {
                string file = gridViewReportInfo.GetRowCellValue(i, gridColReportPath) as string;
                if (!string.IsNullOrEmpty(file))
                {
                    filesPath.Add(file);
                }
            }
        }

        private void downloadFile(bool isDownloadAll)
        {
            filesPath.Clear();
            if (isDownloadAll)
            {
                fillAllByList();
            }
            else
            {
                fillSelectedByList();
            }
            if (filesPath.Count == 0)
            {
                MessageBox.Show("找不到选择的对应文件,请重新选择！");
                return;
            }
            DownloadStationAcceptFile query = new DownloadStationAcceptFile(MainModel, filesPath);
            query.Query();
        }
    }
}
