<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="toolTip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="columnHeader4.GenerateMember" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnMoveUp.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAAlwSFlzAAALDAAA
        CwwBP0AiyAAAAJdJREFUOE+d0qENBCEQBdB1SAQd0A2WQlBoLA5LKIAasHQ2lz/Jkr0Al1vED+rNZ5K5
        eu/XSbbIWku/Bi5ha42MMaS13uIJAtVaGSmlOKvmLwhUSqGUEgkhOFLKJR4QLTlnRiGEAe8BeJ/NDGOM
        5L0f2cEnnnYEwqBd0906QXz1CGLPI4hG7Pv6q845wtW8hv/e7dGBY/gHo86bEJG2um0AAAAASUVORK5C
        YII=
</value>
  </data>
  <data name="btnMoveDown.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAAlwSFlzAAALDAAA
        CwwBP0AiyAAAAKFJREFUOE+dkiEOBCEMRcdhUByBu3AHPHLkeByKA6CwWByam3XzJ2GzC+yGQfykCXn0
        97dHrfXY0RaERgNYSqGcM2mtSSlFQgiaORrAGCNBUkrinBNjbA0MIZBz7gaaljt675+DKSVaBjETAAh1
        bxUB9bbf4VhrbwBC/TnjbNavVM/zpOu6pmAf0LAOwNDjVPGzMebvKqaX0yz9Wnx7377VF+VKmwRRq4c4
        AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="btnDelHilight.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        R0lGODlhEAAQAIcAAAEAAAAAAIkiAI0jAI8kAJYlAJonAJwnAKAoAKUpAKkqALEsALQxBrs1CcI6DMY/
        EshDFtBKHdRRJtBUK9VWLNVYLtddNdhiO/9ONv9YPdpqRNxzUN13Vd14Vv9jRf9tTP9tTf93VN+AYP+B
        W/+CW/+MYv+Waf+Xav+hcf+seP+2f//Bh4IgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIwRID0AAPN88gAABT0HeD0AAPrSmIwQ+AAAAIwTOPNaNPeV
        gP////N88vNzs/Nz8wAAFgAAFgAAAQAAAPN8lwlV2PNn4QUTePNn+QAAAPNm5QAAAIwT/K9vAgAAOAAA
        JAAAAgoACPeukAAAAmoAaP3sAM1pW81p6AAAAAACGgAAAAAAaIwPQIwRXAlV4AACKIwPNM1pwIwUDPNa
        NPeucP////OwUvOxxgAAAYwR5IwSaAAAAAUAAP3sAAAARQGYEIwRUBAAgIwUDPNaNPeUWP////Nn+QAA
        APNm5eGYYAAAtwAAAQAAAowT/AAAGAAAAIwSaAAAQgAAAIwSTAAAAAAAAAAAAAAAAAAADAAAAvMBAf3s
        AAAAaAAAAAAAAxoAcAlV4AAAAgAAAAlV4AAABYwSpOGacQAC2AAAAAAAAAAAAAAAAgAAAAAAAIwT5EqI
        MvoUkAAAAAAAAIwT2AAAAgAAgAAAAIxVdKb17AAAAlw6RGtyb3lNXF8yRVx2ZUlOSURFSGVyXzQyXG9j
        X29ydGVkXwAABQAAAEUF1gAAAAAAJAAAAfrSmPM3feGTBQAC2AAAAAAAAAAAAIwTYPrSmAAAFgAAAOGT
        JQAAFowT/AAAAQAAIAAAFgAAAAAAFowTTAAAAIwUDOQKvOT+oP///+GTJUqIyQAC2PrSmAAAFowTsAAA
        ACH/C05FVFNDQVBFMi4wAwEBAAAh+QQBAAAAACwAAAAAEAAQAAAIWgABCBxIsKDBgwgTKlzIkGAHERs0
        WKhQgYKECBAcEOSwQkUKFCZKjAjxwQMDghc8ojghkqSHDAkITvjIkkQIEC8xDCD4oMECBQgOHDBQgAAL
        AQ2TKl3KtGnBgAA7
</value>
  </data>
  <data name="btnAddHilight.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        R0lGODlhEAAQAIcAAAEAAAAAAABcAABdAABhAABlAABpAABqAAB7AAJ6AgB8AACGAACHAAOFAwGJAQCK
        AAOLAwKNAgOOAwSMBAaPBgWRBQeQBw2UDQ+VDxSbFBWbFSunKy6pLjOxMzqxOjuwOzu1Oz2xPUS6RE2/
        TVa7Vle7V1i8WF2+XV6+XlXEVV7JXmfCZ2bOZmjCaGnDaW/Tb3jYeIDdgIjiiJDmkJnsmQBaAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIwRID0AAPN88gAABT0HeD0AAPtBeIwQ+AAAAIwTOPNaNPeV
        gP////N88vNzs/Nz8wAAFgAAFgAAAQAAAPN8lwlV2PNn4QUTePNn+QAAAPNm5QAAAIwT/K9vAgAAOAAA
        JAAAAgoACPeukAAAAmoAaP3sAM1pW81p6AAAAAACGgAAAAAAaIwPQIwRXAlV4AACKIwPNM1pwIwUDPNa
        NPeucP////OwUvOxxgAAAYwR5IwSaAAAAAUAAP3sAAAARQGYEIwRUBAAgIwUDPNaNPeUWP////Nn+QAA
        APNm5eGYYAAAtwAAAQAAAowT/AAAGAAAAIwSaAAAQgAAAIwSTAAAAAAAAAAAAAAAAAAADAAAAvMBAf3s
        AAAAaAAAAAAAAxoAcAlV4AAAAgAAAAlV4AAABYwSpOGacQABQAAAAAAAAAAAAAAAAgAAAAAAAIwT5EqI
        MvpiaAAAAAAAAIwT2AAAAgAAgAAAAIxVdKb17AAAAlw6RGtyb3lNXF8yRVx2ZUlOSURFSGVyXzMyXG9j
        X29ydGRhXwAABQAAAEUF1gAAAAAAJAAAAftBePM3feGTBQABQAAAAAAAAAAAAIwTYPtBeAAAFgAAAOGT
        JQAAFowT/AAAAQAAIAAAFgAAAAAAFowTTAAAAIwUDOQKvOT+oP///+GTJUqIyQABQPtBeAAAFowTsAAA
        ACH/C05FVFNDQVBFMi4wAwEBAAAh+QQBAAAAACwAAAAAEAAQAAAIgAABCBxIsKBBEyVCcNBgsOAKGjNk
        YGhIEEXEGBIoDnQhIwYMBw1NtDjRwoPHFxUWPJhgYSAJiB1hvGChIsUIEQ0Gfrgok6ZNESAUDNwQc2bN
        myA6HBiY4UIEBhWOJqhBYECBhgt+1tAo8AFSAlwBQADaQUBYCg0QGLgatq1bAAEBADs=
</value>
  </data>
  <metadata name="errProv.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>110, 17</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>30</value>
  </metadata>
  <data name="StatReportColumnDefDlg.Appearance.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        R0lGODlhEAAQAIcAAAEAAAAAAP///yZLgqGwxqfH6leX4EN4ukyFyv39/aW0y1iW4FaU3VeT3Pr6+uj1
        +Jmvyuz2+pywye32+leS2Pz8/JGsytzw+tzw+5Ksydzv+gAAAFeR2FeQ1tnr/Nnv/GeNvXup3gAAAFiR
        1liQ1I2qyo6qytXr+9Xs+wAAAFiP1FiP09Dq+4upyoypytLq+3mo3nio3liO0wAAAAAAAAAAAAAAAFeM
        0FuJwGCNwpK34szm+wAAAFiNz1GAvvX6/meUy8rl+1eMzvz+/2GQyFSFxU98t058twAAAKi20AAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        ACH/C05FVFNDQVBFMi4wAwEBAAAh+QQBAAAAACwAAAAAEAAQAAAIiQABCBxIsKDBgwYLGFi4cMABhggK
        DDSQoKLFiwkWDGSAsSODgQ0cgBhJEoZJBw0GUqhwoSUGlxouOOAwsEOFkjBC6AxRYcRAEglOCB16AgWK
        BCoGrkgQo6lTpwlkDLyRYIfVq1cF9Bjo44fJr08FCCHoYwgRAWjToi1S0MgRI3Djwk2CsK5dgwEBADs=
</value>
  </data>
  <metadata name="toolStripDropDownService.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>205, 17</value>
  </metadata>
</root>