﻿using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public static class LteComplaintHelper
    {
        public static double GetPointDist(double fALongitude, double fALatitude, double fBLongitude, double fBLatitude, double dDist)
        {
            if (fALongitude > fBLongitude - 0.006 && fALongitude < fBLongitude + 0.006
                && fALatitude > fBLatitude - 0.006 && fALatitude < fBLatitude + 0.006)
            {
                double distance = MathFuncs.GetDistance(fALongitude, fALatitude, fBLongitude, fBLatitude);
                return distance;
            }
            return 1000;
        }

        public static int MrCellNopBtsAngle(double longitude_X1, double latitude_Y1, double longitude_X2, double latitude_Y2, int X_方向角, double distance)
        {
            double angleDiff = 0, angle = 0, ygap = 0;
            ygap = MathFuncs.GetDistance(longitude_X1, latitude_Y1, longitude_X1, latitude_Y2);

            double angleV = Math.Acos(ygap / distance);
            if (longitude_X2 >= longitude_X1 && latitude_Y2 >= latitude_Y1)
                angle = angleV * 180 / Math.PI;
            else if (longitude_X2 <= longitude_X1 && latitude_Y2 >= latitude_Y1)
                angle = 360 - angleV * 180 / Math.PI;
            else if (longitude_X2 <= longitude_X1)// latitude_Y2 <= latitude_Y1
                angle = 180 + angleV * 180 / Math.PI;
            else
                angle = 180 - angleV * 180 / Math.PI;

            angleDiff = Math.Abs(angle - X_方向角);
            if (angleDiff > 180)
                angleDiff = 360 - angleDiff;

            return (int)angleDiff;
        }

    }

    public class LteComplaintCond
    {
        public int ISN { get; set; }
        public int IAngleMin { get; set; }
        public int IAngleMax { get; set; }
        public string StrAngle
        {
            get
            {
                return string.Format("[{0},{1})", IAngleMin, IAngleMax);
            }
        }
        public double DAngleWeight { get; set; }
        public int IDistanceMin { get; set; }
        public int IDistanceMax { get; set; }
        public string StrDistance
        {
            get
            {
                return string.Format("[{0},{1})", IDistanceMin, IDistanceMax);
            }
        }
        public double DDistanceWeight { get; set; }

        public LteComplaintCond()
        {

        }

        public LteComplaintCond(int iSn, int iAngleMin, int iAngleMax, double dAngleWeight
            , int iDistanceMin, int iDistanceMax, double dDistanceWeight)
        {
            ISN = iSn;
            IAngleMin = iAngleMin;
            IAngleMax = iAngleMax;
            DAngleWeight = dAngleWeight;
            IDistanceMin = iDistanceMin;
            IDistanceMax = iDistanceMax;
            DDistanceWeight = dDistanceWeight;
        }
    }

    public class ComplaitInfo
    {
        public int Idx { get; set; }
        public string StrNo { get; set; }
        public string StrID { get; set; }
        public string StrCity { get; set; }
        public string StrRegion { get; set; }
        public string StrAddr { get; set; }
        public string StrAddr2 { get; set; }
        public float FLongitude { get; set; }
        public float FLatitude { get; set; }

        public NOPCellInfo gsmCell { get; set; }
        public NOPCellInfo tdCell { get; set; }
        public NOPCellInfo lteCell { get; set; }

        public ComplaitInfo()
        {
            Idx = 0;
            StrNo = "";
            StrID = "";
            StrCity = "";
            StrRegion = "";
            StrAddr = "";
            StrAddr2 = "";
            FLongitude = 0;
            FLatitude = 0;
        }

        public bool FillData(DataRow row)
        {
            try
            {
                Idx = Convert.ToInt32(row["序号"].ToString());
                StrNo = Convert.ToString(row["编号"].ToString());
                StrID = Convert.ToString(row["ID"].ToString());
                StrCity = Convert.ToString(row["地市"].ToString());
                StrRegion = Convert.ToString(row["行政区"].ToString());
                StrAddr = Convert.ToString(row["地址"].ToString());
                StrAddr2 = Convert.ToString(row["整理后地址"].ToString());
                FLongitude = Convert.ToSingle(row["地址经度"].ToString());
                FLatitude = Convert.ToSingle(row["地址纬度"].ToString());
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
