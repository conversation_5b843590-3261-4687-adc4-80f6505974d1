﻿namespace MasterCom.RAMS.Model
{
    partial class TestRoundListForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.gridCtrlTestRound = new DevExpress.XtraGrid.GridControl();
            this.gvRound = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gcSN = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcLogonCode = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcCity = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcDesc = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gcPhone = new DevExpress.XtraGrid.Columns.GridColumn();
            this.btnAdd = new DevExpress.XtraEditors.SimpleButton();
            this.btnSubmit = new DevExpress.XtraEditors.SimpleButton();
            this.btnRemove = new DevExpress.XtraEditors.SimpleButton();
            this.btnModify = new DevExpress.XtraEditors.SimpleButton();
            this.btnInputAdminPw = new DevExpress.XtraEditors.SimpleButton();
            this.grpUser = new DevExpress.XtraEditors.GroupControl();
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlTestRound)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvRound)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpUser)).BeginInit();
            this.grpUser.SuspendLayout();
            this.SuspendLayout();
            // 
            // gridCtrlTestRound
            // 
            this.gridCtrlTestRound.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridCtrlTestRound.Location = new System.Drawing.Point(2, 2);
            this.gridCtrlTestRound.MainView = this.gvRound;
            this.gridCtrlTestRound.Name = "gridCtrlTestRound";
            this.gridCtrlTestRound.Size = new System.Drawing.Size(667, 432);
            this.gridCtrlTestRound.TabIndex = 2;
            this.gridCtrlTestRound.UseEmbeddedNavigator = true;
            this.gridCtrlTestRound.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gvRound});
            // 
            // gvRound
            // 
            this.gvRound.Appearance.SelectedRow.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.gvRound.Appearance.SelectedRow.BackColor2 = System.Drawing.Color.Lime;
            this.gvRound.Appearance.SelectedRow.Options.UseBackColor = true;
            this.gvRound.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gcSN,
            this.gcName,
            this.gcLogonCode,
            this.gcCity,
            this.gcDesc,
            this.gcPhone});
            this.gvRound.GridControl = this.gridCtrlTestRound;
            this.gvRound.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(DevExpress.Data.SummaryItemType.Count, "Name", null, "")});
            this.gvRound.Name = "gvRound";
            this.gvRound.OptionsBehavior.Editable = false;
            this.gvRound.OptionsDetail.EnableMasterViewMode = false;
            this.gvRound.OptionsDetail.ShowDetailTabs = false;
            this.gvRound.OptionsSelection.MultiSelect = true;
            this.gvRound.OptionsView.EnableAppearanceEvenRow = true;
            this.gvRound.OptionsView.EnableAppearanceOddRow = true;
            this.gvRound.OptionsView.ShowGroupPanel = false;
            this.gvRound.PaintStyleName = "Skin";
            this.gvRound.DoubleClick += new System.EventHandler(this.gvRound_DoubleClick);
            // 
            // gcSN
            // 
            this.gcSN.Caption = "序号";
            this.gcSN.FieldName = "SN";
            this.gcSN.Name = "gcSN";
            this.gcSN.Visible = true;
            this.gcSN.VisibleIndex = 0;
            // 
            // gcName
            // 
            this.gcName.Caption = "年份";
            this.gcName.FieldName = "Year";
            this.gcName.Name = "gcName";
            this.gcName.Visible = true;
            this.gcName.VisibleIndex = 1;
            this.gcName.Width = 80;
            // 
            // gcLogonCode
            // 
            this.gcLogonCode.Caption = "季度";
            this.gcLogonCode.FieldName = "Quarter";
            this.gcLogonCode.Name = "gcLogonCode";
            this.gcLogonCode.Visible = true;
            this.gcLogonCode.VisibleIndex = 2;
            this.gcLogonCode.Width = 80;
            // 
            // gcCity
            // 
            this.gcCity.Caption = "月份";
            this.gcCity.FieldName = "Month";
            this.gcCity.Name = "gcCity";
            this.gcCity.Visible = true;
            this.gcCity.VisibleIndex = 3;
            this.gcCity.Width = 80;
            // 
            // gcDesc
            // 
            this.gcDesc.Caption = "结束时间";
            this.gcDesc.FieldName = "EndTime";
            this.gcDesc.Name = "gcDesc";
            this.gcDesc.Visible = true;
            this.gcDesc.VisibleIndex = 5;
            this.gcDesc.Width = 143;
            // 
            // gcPhone
            // 
            this.gcPhone.Caption = "开始时间";
            this.gcPhone.FieldName = "BeginTime";
            this.gcPhone.Name = "gcPhone";
            this.gcPhone.Visible = true;
            this.gcPhone.VisibleIndex = 4;
            this.gcPhone.Width = 175;
            // 
            // btnAdd
            // 
            this.btnAdd.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnAdd.Location = new System.Drawing.Point(230, 457);
            this.btnAdd.Name = "btnAdd";
            this.btnAdd.Size = new System.Drawing.Size(87, 27);
            this.btnAdd.TabIndex = 7;
            this.btnAdd.Text = "添加时段";
            this.btnAdd.Visible = false;
            this.btnAdd.Click += new System.EventHandler(this.btnAdd_Click);
            // 
            // btnSubmit
            // 
            this.btnSubmit.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSubmit.Location = new System.Drawing.Point(576, 457);
            this.btnSubmit.Name = "btnSubmit";
            this.btnSubmit.Size = new System.Drawing.Size(87, 27);
            this.btnSubmit.TabIndex = 7;
            this.btnSubmit.Text = "提交修改";
            this.btnSubmit.Visible = false;
            this.btnSubmit.Click += new System.EventHandler(this.btnSubmit_Click);
            // 
            // btnRemove
            // 
            this.btnRemove.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnRemove.Location = new System.Drawing.Point(121, 457);
            this.btnRemove.Name = "btnRemove";
            this.btnRemove.Size = new System.Drawing.Size(87, 27);
            this.btnRemove.TabIndex = 7;
            this.btnRemove.Text = "删除时段";
            this.btnRemove.Visible = false;
            this.btnRemove.Click += new System.EventHandler(this.btnRemove_Click);
            // 
            // btnModify
            // 
            this.btnModify.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnModify.Location = new System.Drawing.Point(12, 457);
            this.btnModify.Name = "btnModify";
            this.btnModify.Size = new System.Drawing.Size(87, 27);
            this.btnModify.TabIndex = 7;
            this.btnModify.Text = "修改时段";
            this.btnModify.Visible = false;
            this.btnModify.Click += new System.EventHandler(this.btnModify_Click);
            // 
            // btnInputAdminPw
            // 
            this.btnInputAdminPw.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnInputAdminPw.Location = new System.Drawing.Point(409, 457);
            this.btnInputAdminPw.Name = "btnInputAdminPw";
            this.btnInputAdminPw.Size = new System.Drawing.Size(145, 27);
            this.btnInputAdminPw.TabIndex = 8;
            this.btnInputAdminPw.Text = "进入系统管理员模式";
            this.btnInputAdminPw.Click += new System.EventHandler(this.btnInputAdminPw_Click);
            // 
            // grpUser
            // 
            this.grpUser.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.grpUser.Controls.Add(this.gridCtrlTestRound);
            this.grpUser.Location = new System.Drawing.Point(3, 3);
            this.grpUser.Name = "grpUser";
            this.grpUser.ShowCaption = false;
            this.grpUser.Size = new System.Drawing.Size(671, 436);
            this.grpUser.TabIndex = 3;
            // 
            // TestRoundListForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(678, 498);
            this.Controls.Add(this.btnInputAdminPw);
            this.Controls.Add(this.grpUser);
            this.Controls.Add(this.btnSubmit);
            this.Controls.Add(this.btnRemove);
            this.Controls.Add(this.btnModify);
            this.Controls.Add(this.btnAdd);
            this.MinimumSize = new System.Drawing.Size(694, 536);
            this.Name = "TestRoundListForm";
            this.Text = "测试时段设置";
            ((System.ComponentModel.ISupportInitialize)(this.gridCtrlTestRound)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gvRound)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpUser)).EndInit();
            this.grpUser.ResumeLayout(false);
            this.ResumeLayout(false);

        }
        #endregion

        private DevExpress.XtraGrid.GridControl gridCtrlTestRound;
        private DevExpress.XtraGrid.Views.Grid.GridView gvRound;
        private DevExpress.XtraGrid.Columns.GridColumn gcName;
        private DevExpress.XtraGrid.Columns.GridColumn gcLogonCode;
        private DevExpress.XtraGrid.Columns.GridColumn gcCity;
        private DevExpress.XtraGrid.Columns.GridColumn gcDesc;
        private DevExpress.XtraGrid.Columns.GridColumn gcPhone;
        private DevExpress.XtraEditors.SimpleButton btnAdd;
        private DevExpress.XtraEditors.SimpleButton btnSubmit;
        private DevExpress.XtraEditors.SimpleButton btnRemove;
        private DevExpress.XtraEditors.SimpleButton btnModify;
        private DevExpress.XtraEditors.SimpleButton btnInputAdminPw;
        private DevExpress.XtraGrid.Columns.GridColumn gcSN;
        private DevExpress.XtraEditors.GroupControl grpUser;
    }
}