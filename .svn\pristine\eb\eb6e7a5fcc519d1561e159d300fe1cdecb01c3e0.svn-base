﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.Func
{
    public partial class CellGridDetailSetConditionForm : BaseDialog
    {
        public CellGridDetailSetConditionForm()
        {
            InitializeComponent();
            DIYSQLCellGridTable cgType = new DIYSQLCellGridTable();
            cgType.Query();

            foreach (string type in cgType.TableNames)
            {
                chkTypes.Items.Add(type);
            }

            if (chkTypes.Items.Count <= 0)
            {
                this.DialogResult = DialogResult.Cancel;
                return;
            }
            chkTypes.SetItemChecked(0, true);
        }

        public CellGridDetailCondition GetCondition()
        {
            CellGridDetailCondition con = new CellGridDetailCondition();
            for (int i = 0; i < chkTypes.Items.Count; i++)
            {
                if (chkTypes.CheckedItems.Contains(chkTypes.Items[i]))
                {
                    con.AddTableName(chkTypes.Items[i].ToString());
                }
            }
            return con;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancle_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
