﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.MTGis;
using System.Drawing;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.Func
{
    public class CellGridBlockLayer : LayerBase
    {
        public CellGridBlockLayer()
            : base("栅格问题点图层")
        {

        }

        SolidBrush brush { get; set; } = new SolidBrush(Color.FromArgb(200, Color.Orange));
        public List<CellGridBlockInfo> Blocks { get; set; } = new List<CellGridBlockInfo>();
        public CellGridCondition conn { get; set; }

        public override void Draw(Rectangle clientRect, Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible || Blocks == null || Blocks.Count == 0)
            {
                return;
            }

            Rectangle inflatedRect = new Rectangle(updateRect.X, updateRect.Y, updateRect.Width, updateRect.Height);
            //inflatedRect.Inflate(10, 10);
            DbRect dRect;
            this.gisAdapter.FromDisplay(inflatedRect, out dRect);

            foreach (CellGridBlockInfo block in Blocks)
            {
                if (block.CellGrids == null || block.CellGrids.Count <= 0 || block.CellGridAll == null || block.CellGridAll.Count <= 0)
                {
                    continue;
                }
                
                foreach (CellGridAllInfo all in block.CellGridAll)
                {
                    Color cl = Color.Orange;
                    
                    if (all.AvgRSRP < -110 && all.AvgRSRP >= -141)
                    {
                        cl = conn.RSRP141;
                    }
                    else if (all.AvgRSRP < -105 && all.AvgRSRP >= -110)
                    {
                        cl = conn.RSRP110;
                    }
                    else if (all.AvgRSRP < -100 && all.AvgRSRP >= -105)
                    {
                        cl = conn.RSRP105;
                    }
                    else if (all.AvgRSRP < -95 && all.AvgRSRP >= -100)
                    {
                        cl = conn.RSRP100;
                    }
                    else if (all.AvgRSRP < -80 && all.AvgRSRP >= -95)
                    {
                        cl = conn.RSRP95;
                    }
                    else if (all.AvgRSRP < -75 && all.AvgRSRP >= -80)
                    {
                        cl = conn.RSRP80;
                    }
                    else if (all.AvgRSRP < -40 && all.AvgRSRP >= -75)
                    {
                        cl = conn.RSRP75;
                    }
                    else if (all.AvgRSRP < 25 && all.AvgRSRP >= -40)
                    {
                        cl = conn.RSRP40;
                    }
                    bool isnext = true;
                    foreach (LabColorText lct in conn.LbList)
                    {
                        if (cl == lct.color && !lct.isCheck)
                        {
                            isnext = false;
                            break;
                        }
                    }
                    if (!isnext)
                    {
                        continue;
                    }
                    if (all.AvgRSRP == 0.0)
                    {
                        cl = Color.Black;
                    }
                    brush = new SolidBrush(Color.FromArgb(200, cl));
                    //if (all.IsProb == 0)
                    //{
                    //    brush = new SolidBrush(Color.FromArgb(200, Color.Green));
                    //}
                    //else
                    //{
                    //    brush = new SolidBrush(Color.FromArgb(200, Color.Orange));
                    //}
                    if (dRect.Within(all.Bounds))
                    {
                        RectangleF rect;
                        this.gisAdapter.ToDisplay(all.Bounds, out rect);
                        graphics.FillRectangle(brush, rect);
                        PointF gridCenter = new PointF(rect.X + rect.Width / 2, rect.Y + rect.Height / 2);
                        LTECell cell = CellManager.GetInstance().GetLTECell(block.Time, block.LAC, block.CI);
                        if (cell != null)
                        {
                            DbPoint cellPt = new DbPoint(cell.EndPointLongitude, cell.EndPointLatitude);
                            PointF cellPf;
                            gisAdapter.ToDisplay(cellPt, out cellPf);
                            graphics.DrawLine(Pens.Orange, cellPf, gridCenter);
                        }
                        Rectangle re = Rectangle.Ceiling(rect);
                        graphics.DrawRectangle(Pens.Black, re);
                    }
                }

                foreach (CellGridInfo cg in block.CellGrids)
                {
                    Color cl = Color.Orange;

                    if (cg.AvgRSRP < -110 && cg.AvgRSRP >= -141)
                    {
                        cl = conn.RSRP141;
                    }
                    else if (cg.AvgRSRP < -105 && cg.AvgRSRP >= -110)
                    {
                        cl = conn.RSRP110;
                    }
                    else if (cg.AvgRSRP < -100 && cg.AvgRSRP >= -105)
                    {
                        cl = conn.RSRP105;
                    }
                    else if (cg.AvgRSRP < -95 && cg.AvgRSRP >= -100)
                    {
                        cl = conn.RSRP100;
                    }
                    else if (cg.AvgRSRP < -80 && cg.AvgRSRP >= -95)
                    {
                        cl = conn.RSRP95;
                    }
                    else if (cg.AvgRSRP < -75 && cg.AvgRSRP >= -80)
                    {
                        cl = conn.RSRP80;
                    }
                    else if (cg.AvgRSRP < -40 && cg.AvgRSRP >= -75)
                    {
                        cl = conn.RSRP75;
                    }
                    else if (cg.AvgRSRP < 25 && cg.AvgRSRP >= -40)
                    {
                        cl = conn.RSRP40;
                    }
                    bool isnext = true;
                    foreach (LabColorText lct in conn.LbList)
                    {
                        if (cl == lct.color && !lct.isCheck)
                        {
                            isnext = false;
                            break;
                        }
                    }
                    if (!isnext)
                    {
                        continue;
                    }
                    //if (cg.IsProb == 0)
                    //{
                    //    brush = new SolidBrush(Color.FromArgb(200, Color.Blue));
                    //}
                    //else
                    //{
                    //    brush = new SolidBrush(Color.FromArgb(200, Color.Red));
                    //}
                    if (dRect.Within(cg.Bounds))
                    {
                        RectangleF rect;
                        this.gisAdapter.ToDisplay(cg.Bounds, out rect);
                        //graphics.FillRectangle(brush, rect);
                        Rectangle re = Rectangle.Ceiling(rect);
                        graphics.DrawRectangle(Pens.Red, re);
                    }
                }
            }
        }
    }
}
