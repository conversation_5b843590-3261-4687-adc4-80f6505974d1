﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class WeakSINRRoadNRForm : MinCloseForm
    {
        public string themeName { get; set; } = "NR:SS_SINR";
        public WeakSINRRoadNRForm()
        {
            InitializeComponent();
        }

        public void FillData(List<WeakSINRRoadNR> list)
        {
            gridControl.DataSource = list;
            gridControl.RefreshDataSource();
            MainModel.ClearDTData();
            foreach (WeakSINRRoadNR road in list)
            {
                foreach (TestPoint tp in road.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
            }
            MainModel.FireSetDefaultMapSerialTheme(themeName);
            MainModel.FireDTDataChanged(this);
        }

        private void gv_DoubleClick(object sender, EventArgs e)
        {
            WeakSINRRoadNR weakCover = gv.GetFocusedRow() as WeakSINRRoadNR;
            if (weakCover != null)
            {
                MainModel.DTDataManager.Clear();
                foreach (TestPoint tp in weakCover.TestPoints)
                {
                    MainModel.DTDataManager.Add(tp);
                }
                MainModel.FireDTDataChanged(this);
                TestPoint midTp = weakCover.TestPoints[weakCover.TestPoints.Count / 2];
                MainModel.MainForm.GetMapForm().GoToView(midTp.Longitude, midTp.Latitude, 6000);
            }
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            List<List<object>> exportList = GridViewTransfer.Transfer(this.gridControl);
            ExcelNPOIManager.ExportToExcel(exportList);
        }
    }
}
