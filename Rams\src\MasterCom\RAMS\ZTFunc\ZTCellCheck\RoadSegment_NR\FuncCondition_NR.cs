﻿using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTCellCheck.RoadSegment_NR
{
    public class FuncCondition_NR
    {
        public FuncCondition_NR()
        {
            WeakCover = new WeakCoverCondition_NR();
            MultiCover = new MultiCoverCondition_NR();
            PoorSINR = new PoorSINRCondition_NR();
            CoverLap = new CoverLapCondition_NR();
            OverHandover = new OverHandoverCondition_NR();
            UltraSiteCondition = new UltraSiteCondition();
            FarCover = new FarCoverCondition_NR();
            UltraCellByFile = true;
        }
        public UltraSiteCondition UltraSiteCondition
        {
            get;
            set;
        }
        public FarCoverCondition_NR FarCover
        { get; set; }

        public WeakCoverCondition_NR WeakCover
        {
            get;
            set;
        }
        public MultiCoverCondition_NR MultiCover
        {
            get;
            set;
        }
        public PoorSINRCondition_NR PoorSINR
        {
            get;
            set;
        }
        public CoverLapCondition_NR CoverLap
        {
            get;
            set;
        }
        public OverHandoverCondition_NR OverHandover
        {
            get;
            set;
        }


        public bool UltraCellByFile { get; set; }

        public string FileName { get; set; }
    }

    public abstract class SiglePointCondition_NR
    {
        public abstract string Name { get; }
        public abstract bool Filter(IProblemData_NR probData);
    }

    public class CoverLapCondition_NR : SiglePointCondition_NR
    {
        public override string Name
        {
            get { return "过覆盖"; }
        }
        public float CoverlapRSRPMin { get; set; } = -90;
        public double CvrDisFactorMax { get; set; } = 1.6f;
        public int CoverSiteNum { get; set; } = 3;
        internal bool IsCoverLap(float rsrp, double distance, double cellMaxDis)
        {
            return rsrp >= CoverlapRSRPMin && distance > cellMaxDis;
        }

        internal bool IsValid(CoverLapCell_NR cell)
        {
            return !cell.Filter(Rate);
        }

        public double Rate { get; set; }

        public override bool Filter(IProblemData_NR probData)
        {
            return false;
        }
    }

    public class OverHandoverCondition_NR : SiglePointCondition_NR
    {
        public override string Name
        {
            get { return "频繁切换"; }
        }
        public OverHandoverCondition_NR()
        {
            HoCount = 3;
            Second = 15;
            Distance = 150;
        }
        public int HoCount
        {
            get;
            set;
        }
        public int Second
        {
            get;
            set;
        }
        public double Distance
        {
            get;
            set;
        }

        public override bool Filter(IProblemData_NR probData)
        {
            return false;
        }
    }

    public class FarCoverCondition_NR : SiglePointCondition_NR
    {
        public override string Name
        {
            get { return "超远覆盖"; }
        }
        public FarCoverCondition_NR()
        {
            RSRP = -105;
            TestPointNum = 15;
            Distance = 1500;
        }
        public float RSRP
        {
            get;
            set;
        }

        public int TestPointNum
        {
            get;
            set;
        }

        public double Distance
        {
            get;
            set;
        }

        public bool IsValidDistance(double dis)
        {
            return dis >= Distance;
        }

        public bool IsValid(float rsrp)
        {
            return rsrp > RSRP;
        }

        public override bool Filter(IProblemData_NR probData)
        {
            if (!(probData is FarCoverCell_NR))
            {
                return true;
            }
            FarCoverCell_NR far = probData as FarCoverCell_NR;
            return far.FarCoverPointNum < TestPointNum;
        }
    }

}
