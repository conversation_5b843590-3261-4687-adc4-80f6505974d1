﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public partial class NRCellInfoForm : BaseForm
    {
        NRCell cell = null;
        public NRCellInfoForm(MainModel mm,NRCell cell):base(mm)
        {
            InitializeComponent();
            if (cell!=null)
            {
                this.cell = cell;
                fillInfo();
            }
        }

        private void fillInfo()
        {
            txtName.Text = cell.Name;
            txtID.Text = cell.ID.ToString();
            txtCode.Text = cell.Code;
            txtLng.Text = cell.Longitude.ToString("F7");
            txtLat.Text = cell.Latitude.ToString("F7");
            txtTAC.Text = cell.TAC.ToString();
            txtECI.Text = cell.NCI.ToString();
            txtPCI.Text = cell.PCI.ToString();
            txtEarfcn.Text = cell.SSBARFCN.ToString();
            txtDirection.Text = cell.Direction.ToString();
            txtSectorID.Text = cell.SectorID.ToString();
            txtBTS.Text = cell.BTSName;
            txtDesc.Text = cell.DESC;
        }

        private void btnBTSInfo_Click(object sender, EventArgs e)
        {
            new NRBTSInfoForm(MainModel, cell.BelongBTS).Show(Owner);
        }



    }
}
