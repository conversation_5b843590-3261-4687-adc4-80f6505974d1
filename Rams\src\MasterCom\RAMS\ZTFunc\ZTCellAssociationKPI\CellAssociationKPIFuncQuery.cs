﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using MasterCom.Util.UiEx;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
/*Need2BePerfect_Qiujianwei
     *应该用新结构的KPI统计
     */ 
    public class CellAssociationKPIFuncQuery : DIYStatQuery
    {
        public CellAssociationKPIFuncQuery(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "小区关联KPI分析"; }
        }

        public override string IconName
        {
            get { return string.Empty; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 25000, 25001, this.Name);
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }

        public override bool CanEnabled(Model.SearchGeometrys searchGeometrys)
        {
            return true;
        }

        protected override bool isValidCondition()
        {
            if (condition.FileInfos == null || condition.FileInfos.Count == 0)
            {
                return false;
            }

            CellAssociationKPISettingForm setForm = new CellAssociationKPISettingForm();
            if (setForm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }
            selectedReport = setForm.SelectedReport;

            if (periodChooser == null)
            {
                periodChooser = new MultiTimePeriodChooser();
            }
            if (periodChooser.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            selectedPeriods = new List<TimePeriod>(periodChooser.TimePeriods);

            return true;
        }

        protected override StatTbToken getBranchToken()
        {
            return StatTbToken.cell;
        }

        protected override void query()
        {
            getReadyBeforeQuery();
            queryByCarrier(queryInThread);
            if (cellDataDic.Count == 0)
            {
                MessageBox.Show("未能从选定文件匹配出任意小区", "匹配小区失败", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            WaitTextBox.Show("正在查询关联指标...", queryAssociationKPI);
            if (kpiResult.Error != null)
            {
                //MessageBox.Show(kpiResult.Error.Message + Environment.NewLine + kpiResult.KPISqlText, "查询错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                MessageBox.Show(kpiResult.Error.Message + Environment.NewLine + kpiResult.Error.StackTrace, "查询错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                /*
                using (System.IO.StreamWriter sw = new System.IO.StreamWriter("CellAssociationKPI.dbg"))
                {
                    sw.Write(kpiResult.KPISqlText);
                }
                 */
                return;
            }
            MergeQueryResult();
            FireShowResultForm();
        }

        protected override void getReadyBeforeQuery()
        {
            DateTime bTime = DateTime.MaxValue;
            DateTime eTime = DateTime.MinValue;
            StringBuilder sb = new StringBuilder();
            foreach (FileInfo fi in condition.FileInfos)
            {
                DateTime fBTime = DateTime.Parse(fi.BeginTimeString);
                DateTime fETime = DateTime.Parse(fi.EndTimeString);
                if (fBTime < bTime)
                {
                    bTime = fBTime;
                }
                if (fETime > eTime)
                {
                    eTime = fETime;
                }
                if (!condition.CarrierTypes.Contains(fi.CarrierType))
                {
                    condition.CarrierTypes.Add(fi.CarrierType);
                }
                if (!condition.ServiceTypes.Contains(fi.ServiceType))
                {
                    condition.ServiceTypes.Add(fi.ServiceType);
                }
                if (!condition.Projects.Contains(fi.ProjectID))
                {
                    condition.Projects.Add(fi.ProjectID);
                }
                sb.Append(fi.ID);
                sb.Append(",");
            }
            TimePeriod p = new TimePeriod(bTime, eTime);
            condition.Periods.Clear();
            condition.Periods.Add(p);
            condition.FileName = sb.ToString().TrimEnd(',');
            condition.NameFilterType = FileFilterType.ByMark_ID;
        }

        protected override void getResultAfterQuery()
        {
            //
        }

        protected void queryInThread(object o)
        {
            try
            {
                WaitBox.CanCancel = true;
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                int periodCount = Condition.Periods.Count;
                int index = 0;
                if (condition.IsByRound)
                {
                    queryPeriodInfo(clientProxy, package, null, true);
                }
                else
                {
                    foreach (TimePeriod period in Condition.Periods)
                    {
                        WaitBox.Text = "开始统计时段[" + period.GetShortString() + "]内的数据...";
                        WaitBox.ProgressPercent = index / periodCount + 10;
                        queryPeriodInfo(clientProxy, package, period, false);
                        if (WaitBox.CancelRequest)
                        {
                            break;
                        }
                    }
                }
                getResultAfterQuery();
            }
            finally
            {
                System.Threading.Thread.Sleep(300);
                WaitBox.Close();
            }
        }

        protected void queryPeriodInfo(ClientProxy clientProxy, Package package, TimePeriod period, bool byRound)
        {
            prepareStatPackage_ImgGrid_FileFilter(package, period, byRound);
            AddDIYEndOpFlag(package);
            package.Content.AddParam("-1,-1,-1");
            clientProxy.Send();
            recieveInfo_ImgGrid(clientProxy);
        }

        protected void prepareStatPackage_ImgGrid_FileFilter(Package package, TimePeriod period, bool byRound)
        {
            package.Command = Command.DIYSearch;
            package.SubCommand = SubCommand.Request;
            package.Content.Type = RequestType.KPI_CELL;
            package.Content.PrepareAddParam();
            if (byRound)
            {
                AddDIYRound(package, condition.ByRoundYear, condition.ByRoundRound);
            }
            else
            {
                AddDIYPeriod(package, period);
            }
            AddDIYProject(package, condition.Projects);
            AddDIYService(package, condition.ServiceTypes);
            if (!condition.IsAllAgent)
            {
                AddDIYAgent(package, condition.AgentIds);
            }
            AddDIYCarrierType(package, condition.CarrierTypes);
            AddDIYFileFilter(package, condition);
            AddDIYStatStatus(package);
            AddDIYAreaTypeAndID(package, condition.Areas);
            AddDIYMomt(package, condition.Momt);
            //
            AddDIYEndOpFlag(package);
        }

        protected void recieveInfo_ImgGrid(ClientProxy clientProxy)
        {
            DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> fileHeaderColumnDef = new List<ColumnDefItem>();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    DTDataHeader fileInfo = recieveFileHeader(clientProxy.DbID, package.Content, fileHeaderColumnDef);
                    if (fileInfo != null)
                    {
                        DTDataHeaderManager.GetInstance().AddDTDataHeader(fileInfo);
                    }
                }
                else if (package.Content.Type == ResponseType.COLUMN_DEFINE)
                {
                    curImgColumnDef.Clear();
                    string idpairs = package.Content.GetParamString();
                    parseToCurImgColumnDef(idpairs, curImgColumnDef);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_GSM ||
                    package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_GPRS)
                {
                    int lac = package.Content.GetParamInt();
                    int ci = package.Content.GetParamInt();
                    ICell cell = MainModel.CellManager.GetCurrentCell(lac, ci);
                    if (cell != null)
                    {
                        DataGSM_NewImg newImg = new DataGSM_NewImg();
                        foreach (StatImgDefItem cdf in curImgColumnDef)
                        {
                            byte[] imgBytes = package.Content.GetParamBytes();
                            Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                            foreach (string str in cellStatInfoDic.Keys)
                            {
                                newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                            }
                        }
                        SaveCellData(cell, newImg);
                    }
                }
                else if (package.Content.Type == ResponseType.KPI_LTE_AMR)
                {
                    int lac = package.Content.GetParamInt();
                    int ci = package.Content.GetParamInt();
                    ICell cell = MainModel.CellManager.GetCurrentLTECell(lac, ci);
                    if (cell != null)
                    {
                        DataLTE newImg = new DataLTE();
                        foreach (StatImgDefItem cdf in curImgColumnDef)
                        {
                            byte[] imgBytes = package.Content.GetParamBytes();
                            Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                            foreach (string str in cellStatInfoDic.Keys)
                            {
                                newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                            }
                        }
                        SaveCellData(cell, newImg);
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_TDSCDMA_AMR ||
                    package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_TDSCDMA_PS ||
                    package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_TDSCDMA_VP)
                {
                    int lac = package.Content.GetParamInt();
                    int ci = package.Content.GetParamInt();
                    ICell cell = MainModel.CellManager.GetCurrentTDCell(lac, ci);
                    if (cell != null || (cell = MainModel.CellManager.GetCurrentCell(lac, ci)) != null)
                    {
                        DataTDSCDMA_NewImg newImg = new DataTDSCDMA_NewImg();
                        foreach (StatImgDefItem cdf in curImgColumnDef)
                        {
                            byte[] imgBytes = package.Content.GetParamBytes();
                            Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                            foreach (string str in cellStatInfoDic.Keys)
                            {
                                newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                            }
                        }
                        SaveCellData(cell, newImg);
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_WCDMA_AMR ||
                    package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_WCDMA_PS ||
                    package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_WCDMA_VP ||
                    package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_WCDMA_PSHS)
                {
                    int lac = package.Content.GetParamInt();
                    int ci = package.Content.GetParamInt();
                    ICell cell = MainModel.CellManager.GetCurrentWCell(lac, ci);
                    if (cell != null)
                    {
                        DataWCDMA_AMR newImg = new DataWCDMA_AMR();
                        foreach (StatImgDefItem cdf in curImgColumnDef)
                        {
                            byte[] imgBytes = package.Content.GetParamBytes();
                            Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                            foreach (string str in cellStatInfoDic.Keys)
                            {
                                newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                            }
                        }
                        SaveCellData(cell, newImg);
                    }
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_CDMA_V ||
                         package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_CDMA_D ||
                         package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_CDMA2000_D)
                {
                    int lac = package.Content.GetParamInt();
                    int ci = package.Content.GetParamInt();
                    ICell cell = MainModel.CellManager.GetCurrentCDCell(lac, ci);
                    if (cell != null)
                    {
                        DataCDMA_Voice newImg = new DataCDMA_Voice();
                        foreach (StatImgDefItem cdf in curImgColumnDef)
                        {
                            byte[] imgBytes = package.Content.GetParamBytes();
                            Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                            foreach (string str in cellStatInfoDic.Keys)
                            {
                                newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                            }
                        }
                        SaveCellData(cell, newImg);
                    }
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }

                #endregion

                setProgressPercent(ref index, ref progress);
            }
        }

        protected void queryAssociationKPI()
        {
            try
            {
                //CellAssociationKPIQuery kpiQuery = new CellAssociationKPIQuery();
                //kpiResult = kpiQuery.QueryReport(selectedReport, selectedPeriods);

                List<ICell> cellList = new List<ICell>();
                foreach (CellAssociationData cellData in cellDataDic.Values)
                {
                    cellList.Add(cellData.Cell);
                }
                CellAssociationKPIQueryEx kpiQueryEx = new CellAssociationKPIQueryEx();
                kpiResult = kpiQueryEx.QueryReport(selectedReport, selectedPeriods, cellList);
            }
            finally
            {
                System.Threading.Thread.Sleep(300);
                WaitTextBox.Close();
            }
        }

        protected void MergeQueryResult()
        {
            // build columns
            resultTable = new DataTable("ResultTable");
            resultTable.Columns.Add("小区名", typeof(string));
            resultTable.Columns.Add("LAC", typeof(int));
            resultTable.Columns.Add("CI", typeof(int));
            List<DataColumn> columns = kpiResult.GetKPIColumns();
            foreach (DataColumn dc in columns)
            {
                resultTable.Columns.Add(new DataColumn(dc.ColumnName, dc.DataType));
            }

            // fill data
            foreach (DataRow dr in kpiResult.ResultTable.Rows)
            {
                ICell cell = kpiResult.GetCell(dr);
                if (cell == null || !cellDataDic.ContainsKey(cell.Name))
                {
                    continue;
                }

                List<object> values = new List<object>() { cell.Name };
                FillCellValues(values, cell);
                values.AddRange(kpiResult.GetKPIValues(dr));

                resultTable.Rows.Add(values.ToArray());
            }

            // 没有关联KPI值，显示文件中出现过的小区
            if (resultTable.Rows.Count == 0)
            {
                foreach (string cellName in cellDataDic.Keys)
                {
                    ICell cell = cellDataDic[cellName].Cell;
                    List<object> values = new List<object>() { cell.Name };
                    FillCellValues(values, cell);
                    for (int i = 0; i < columns.Count; ++i)
                    {
                        values.Add(DBNull.Value);
                    }
                    resultTable.Rows.Add(values.ToArray());
                }
            }
        }

        protected void FireShowResultForm()
        {
            CellAssociationKPIView view = new CellAssociationKPIView();
            view.ShowTable = resultTable.Copy();
            view.ColumnInfoMap = new Dictionary<string, object>(kpiResult.ColumnInfoMap);
            view.ColumnFormatMap = new Dictionary<string, string>(kpiResult.ColumnFormatMap);
            view.CellDataDic = new Dictionary<string, CellAssociationData>(cellDataDic);

            CellAssociationKPIResultForm resultForm = MainModel.GetObjectFromBlackboard(typeof(CellAssociationKPIResultForm).FullName) as CellAssociationKPIResultForm;
            if (resultForm == null || resultForm.IsDisposed)
            {
                resultForm = new CellAssociationKPIResultForm(MainModel);
            }
            resultForm.FillData(view, kpiResult.KPISqlText);
            if (!resultForm.Visible)
            {
                resultForm.Show(MainModel.MainForm);
            }

            selectedReport = null;
            kpiResult = null;
            resultTable = null;
            cellDataDic.Clear();
        }

        protected void SaveCellData(ICell cell, PartialData data)
        {
            CellAssociationData cellData = null;
            if (!cellDataDic.TryGetValue(cell.Name, out cellData))
            {
                cellData = new CellAssociationData(cell, new DataUnitAreaKPIQuery());
                cellDataDic.Add(cell.Name, cellData);
            }
            cellData.CellData.AddStatData(data);
        }

        protected void FillCellValues(List<object> values, ICell cell)
        {
            if (cell is LTECell)
            {
                LTECell lteCell = cell as LTECell;
                values.Add(lteCell.TAC);
                values.Add(lteCell.ECI);
            }
            else if (cell is TDCell)
            {
                TDCell tdCell = cell as TDCell;
                values.Add(tdCell.LAC);
                values.Add(tdCell.CI);
            }
            else if (cell is Cell)
            {
                Cell gsmCell = cell as Cell;
                values.Add(gsmCell.LAC);
                values.Add(gsmCell.CI);
            }
        }

        //protected Dictionary<ICell, DataUnitAreaKPIQuery> cellDataDic = new Dictionary<ICell, DataUnitAreaKPIQuery>();
        protected Dictionary<string, CellAssociationData> cellDataDic = new Dictionary<string, CellAssociationData>();
        protected CellAssociationKPIReport selectedReport = null;
        protected List<TimePeriod> selectedPeriods = null;
        protected MultiTimePeriodChooser periodChooser;
        protected CellAssociationKPIResult kpiResult = null;
        protected DataTable resultTable = null;
    }

    public class CellAssociationData
    {
        public CellAssociationData(ICell cell, DataUnitAreaKPIQuery cellData)
        {
            this.Cell = cell;
            this.CellData = cellData;
        }

        public ICell Cell
        {
            get;
            private set;
        }

        public DataUnitAreaKPIQuery CellData
        {
            get;
            private set;
        }
    }
}
