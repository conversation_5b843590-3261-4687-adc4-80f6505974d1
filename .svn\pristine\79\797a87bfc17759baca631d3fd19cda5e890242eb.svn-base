﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using BrightIdeasSoftware;
using MasterCom.RAMS.Func;

namespace  MasterCom.RAMS.ZTFunc.AreaArchiveManage.WeakCoverArea
{
    public partial class WeakCoverAreaListForm : MinCloseForm
    {
        public WeakCoverAreaListForm()
        {
            InitializeComponent();
            initLv();
        }

        private void initLv()
        {
            this.colIsLackCell.AspectGetter += delegate(object row)
            {
                if (row is WeakCoverArea)
                {
                    return (row as WeakCoverArea).IsLackCell ? "是" : "否";
                }
                return null;
            };

            this.colName.AspectGetter += delegate(object row)
            {
                if (row is WeakCoverArea)
                {
                    return (row as WeakCoverArea).Area.FullName;
                }
                return null;
            };

            this.colNbWeakAreaNames.AspectGetter += delegate(object row)
            {
                if (row is WeakCoverArea)
                {
                    return (row as WeakCoverArea).NbWeakAreaNames;
                }
                return null;
            };

            this.colNbWeakAreaNum.AspectGetter += delegate(object row)
            {
                if (row is WeakCoverArea)
                {
                    return (row as WeakCoverArea).NbWeakAreasNum;
                }
                return null;
            };

            this.colNearestSiteDis.AspectGetter += delegate(object row)
            {
                if (row is WeakCoverArea)
                {
                    return (row as WeakCoverArea).NearestCellDis;
                }
                return null;
            };

            this.colNearestSiteName.AspectGetter += delegate(object row)
            {
                if (row is WeakCoverArea)
                {
                    return (row as WeakCoverArea).NearestSite.Name;
                }
                return null;
            };

            this.colRxLevAvg.AspectGetter += delegate(object row)
            {
                if (row is WeakCoverArea)
                {
                    return (row as WeakCoverArea).RxLevAvg;
                }
                return null;
            };

            this.colSn.AspectGetter += delegate(object row)
            {
                if (row is WeakCoverArea)
                {
                    return (row as WeakCoverArea).Area.SN;
                }
                return null;
            };
        }

        public void FillData(ICollection<WeakCoverArea> weakAreaSet)
        {
            lv.ClearObjects();
            lv.SetObjects(weakAreaSet);
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            MasterCom.Util.ExcelNPOIManager.ExportToExcel(lv);
        }

        private void lv_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            OlvListViewHitTestInfo hitInfo = lv.OlvHitTest(e.X, e.Y);
            if (hitInfo.RowObject is WeakCoverArea)
            {
                WeakCoverArea wArea = hitInfo.RowObject as WeakCoverArea;
                makeSureLayerVisible();
                List<AreaBase> areas = new List<AreaBase>();
                areas.Add(wArea.Area);
                if (wArea.NbWeakAreas != null)
                {
                    foreach (WeakCoverArea item in wArea.NbWeakAreas)
                    {
                        areas.Add(item.Area);
                    }
                }
                List<AreaBase> selArea = new List<AreaBase>();
                selArea.Add(wArea.Area);
                areaLayer.SelectedAreas = selArea;
                areaLayer.Areas = areas;
                MasterCom.MTGis.DbRect bounds = null;
                foreach (AreaBase a in areas)
                {
                    if (bounds==null)
                    {
                        bounds = a.Bounds.Clone();
                    }
                    else
                    {
                        bounds.MergeRects(a.Bounds);
                    }
                }
                MapForm mf = MainModel.MainForm.GetMapForm();
                mf.GoToView(bounds);
            }
        }

        ZTAreaArchiveLayer areaLayer = null;
        private void makeSureLayerVisible()
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            areaLayer = mf.GetLayerBase(typeof(ZTAreaArchiveLayer)) as ZTAreaArchiveLayer;
            if (areaLayer == null)
            {
                areaLayer = new ZTAreaArchiveLayer();
                MainModel.MainForm.GetMapForm().AddLayerBase(areaLayer);
            }
        }


    }
}
