﻿namespace MasterCom.RAMS.NewBlackBlock
{
    partial class BlackBlockHandleForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.panel1 = new System.Windows.Forms.Panel();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.chkHandleAdvice = new System.Windows.Forms.CheckBox();
            this.chkHandleStatus = new System.Windows.Forms.CheckBox();
            this.chkReason = new System.Windows.Forms.CheckBox();
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.objectListView = new BrightIdeasSoftware.ObjectListView();
            this.olvColumnIndex = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnType = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnContent = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnUserName = new BrightIdeasSoftware.OLVColumn();
            this.olvColumnDateTime = new BrightIdeasSoftware.OLVColumn();
            this.txtContent = new System.Windows.Forms.TextBox();
            this.panel1.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).BeginInit();
            this.SuspendLayout();
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.groupBox1);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(783, 44);
            this.panel1.TabIndex = 0;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.chkHandleAdvice);
            this.groupBox1.Controls.Add(this.chkHandleStatus);
            this.groupBox1.Controls.Add(this.chkReason);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(783, 44);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "过滤";
            // 
            // chkHandleAdvice
            // 
            this.chkHandleAdvice.AutoSize = true;
            this.chkHandleAdvice.Location = new System.Drawing.Point(253, 20);
            this.chkHandleAdvice.Name = "chkHandleAdvice";
            this.chkHandleAdvice.Size = new System.Drawing.Size(72, 16);
            this.chkHandleAdvice.TabIndex = 5;
            this.chkHandleAdvice.Text = "处理建议";
            this.chkHandleAdvice.UseVisualStyleBackColor = true;
            this.chkHandleAdvice.CheckedChanged += new System.EventHandler(this.chkReason_CheckedChanged);
            // 
            // chkHandleStatus
            // 
            this.chkHandleStatus.AutoSize = true;
            this.chkHandleStatus.Location = new System.Drawing.Point(140, 20);
            this.chkHandleStatus.Name = "chkHandleStatus";
            this.chkHandleStatus.Size = new System.Drawing.Size(72, 16);
            this.chkHandleStatus.TabIndex = 4;
            this.chkHandleStatus.Text = "处理情况";
            this.chkHandleStatus.UseVisualStyleBackColor = true;
            this.chkHandleStatus.CheckedChanged += new System.EventHandler(this.chkReason_CheckedChanged);
            // 
            // chkReason
            // 
            this.chkReason.AutoSize = true;
            this.chkReason.Location = new System.Drawing.Point(39, 20);
            this.chkReason.Name = "chkReason";
            this.chkReason.Size = new System.Drawing.Size(72, 16);
            this.chkReason.TabIndex = 3;
            this.chkReason.Text = "原因描述";
            this.chkReason.UseVisualStyleBackColor = true;
            this.chkReason.CheckedChanged += new System.EventHandler(this.chkReason_CheckedChanged);
            // 
            // splitContainer1
            // 
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.Location = new System.Drawing.Point(0, 44);
            this.splitContainer1.Name = "splitContainer1";
            this.splitContainer1.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.objectListView);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.txtContent);
            this.splitContainer1.Size = new System.Drawing.Size(783, 376);
            this.splitContainer1.SplitterDistance = 261;
            this.splitContainer1.TabIndex = 1;
            // 
            // objectListView
            // 
            this.objectListView.AllColumns.Add(this.olvColumnIndex);
            this.objectListView.AllColumns.Add(this.olvColumnType);
            this.objectListView.AllColumns.Add(this.olvColumnContent);
            this.objectListView.AllColumns.Add(this.olvColumnUserName);
            this.objectListView.AllColumns.Add(this.olvColumnDateTime);
            this.objectListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnIndex,
            this.olvColumnType,
            this.olvColumnContent,
            this.olvColumnUserName,
            this.olvColumnDateTime});
            this.objectListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.objectListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.objectListView.FullRowSelect = true;
            this.objectListView.GridLines = true;
            this.objectListView.HeaderWordWrap = true;
            this.objectListView.Location = new System.Drawing.Point(0, 0);
            this.objectListView.MultiSelect = false;
            this.objectListView.Name = "objectListView";
            this.objectListView.ShowGroups = false;
            this.objectListView.Size = new System.Drawing.Size(783, 261);
            this.objectListView.TabIndex = 0;
            this.objectListView.UseCompatibleStateImageBehavior = false;
            this.objectListView.View = System.Windows.Forms.View.Details;
            this.objectListView.SelectionChanged += new System.EventHandler(this.objectListView_SelectionChanged);
            // 
            // olvColumnIndex
            // 
            this.olvColumnIndex.HeaderFont = null;
            this.olvColumnIndex.Text = "序号";
            this.olvColumnIndex.Width = 40;
            // 
            // olvColumnType
            // 
            this.olvColumnType.AspectName = "TypeName";
            this.olvColumnType.HeaderFont = null;
            this.olvColumnType.Text = "类型";
            // 
            // olvColumnContent
            // 
            this.olvColumnContent.AspectName = "content";
            this.olvColumnContent.HeaderFont = null;
            this.olvColumnContent.Text = "内容";
            this.olvColumnContent.Width = 450;
            // 
            // olvColumnUserName
            // 
            this.olvColumnUserName.AspectName = "userName";
            this.olvColumnUserName.HeaderFont = null;
            this.olvColumnUserName.Text = "维护人";
            this.olvColumnUserName.Width = 80;
            // 
            // olvColumnDateTime
            // 
            this.olvColumnDateTime.AspectName = "DateTimeString";
            this.olvColumnDateTime.HeaderFont = null;
            this.olvColumnDateTime.Text = "维护时间";
            this.olvColumnDateTime.Width = 120;
            // 
            // txtContent
            // 
            this.txtContent.Dock = System.Windows.Forms.DockStyle.Fill;
            this.txtContent.Location = new System.Drawing.Point(0, 0);
            this.txtContent.Multiline = true;
            this.txtContent.Name = "txtContent";
            this.txtContent.ReadOnly = true;
            this.txtContent.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.txtContent.Size = new System.Drawing.Size(783, 111);
            this.txtContent.TabIndex = 0;
            // 
            // BlackBlockHandleForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(783, 420);
            this.Controls.Add(this.splitContainer1);
            this.Controls.Add(this.panel1);
            this.MinimizeBox = false;
            this.Name = "BlackBlockHandleForm";
            this.Text = "历史处理进度";
            this.panel1.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            this.splitContainer1.Panel2.PerformLayout();
            this.splitContainer1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.objectListView)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.CheckBox chkHandleAdvice;
        private System.Windows.Forms.CheckBox chkHandleStatus;
        private System.Windows.Forms.CheckBox chkReason;
        private System.Windows.Forms.SplitContainer splitContainer1;
        private BrightIdeasSoftware.ObjectListView objectListView;
        private BrightIdeasSoftware.OLVColumn olvColumnIndex;
        private BrightIdeasSoftware.OLVColumn olvColumnType;
        private BrightIdeasSoftware.OLVColumn olvColumnContent;
        private System.Windows.Forms.TextBox txtContent;
        private BrightIdeasSoftware.OLVColumn olvColumnUserName;
        private BrightIdeasSoftware.OLVColumn olvColumnDateTime;
    }
}