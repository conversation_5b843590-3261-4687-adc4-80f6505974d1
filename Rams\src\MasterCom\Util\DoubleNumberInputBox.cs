using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.Util
{
    public partial class DoubleNumberInputBox : NumberInputBox
    {
        public DoubleNumberInputBox()
        {
            InitializeComponent();
        }

        public DoubleNumberInputBox(string text, string labelText)
            : this()
        {
            Text = text;
            LabelText = labelText;
        }

        public DoubleNumberInputBox(string text, string labelText, decimal value, decimal max, decimal min, decimal increment)
            : this(text, labelText)
        {
            numericUpDownInput.Maximum = max;
            numericUpDownInput.Minimum = min;
            numericUpDownInput.Value = value;
            numericUpDownInput.Increment = increment;
        }
    }
}

