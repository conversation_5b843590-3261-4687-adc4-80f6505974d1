﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.Data.Sql;
using System.Data.SqlClient;

using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Net
{
    public class BackgroundFuncResultWriter
    {
        public static BackgroundFuncResultWriter Instance
        {
            get
            {
                if (instance == null)
                {
                    lock(syncObj)
                    {
                        if (instance == null)
                        {
                            instance = new BackgroundFuncResultWriter();
                        }
                    }
                }
                return instance;
            }
        }
        
        public void WriteRoad(string tableName, List<BackgroundResult> resultList, int lastMaxID)
        {
            writer.WriteRoad(tableName, resultList, lastMaxID);
        }

        public void WriteRegion(string tableName, List<BackgroundResult> resultList, int lastMaxID)
        {
            writer.WriteRegion(tableName, resultList, lastMaxID);
        }

        public void WriteCell(string tableName, List<BackgroundResult> resultList, int lastMaxID)
        {
            writer.WriteCell(tableName, resultList, lastMaxID);
        }

        public void WritGridInfo(string tableName, List<GridDownLoadTimeSpeedInfo> gridDownLoadTimeSpeedInfo)
        {
            writer.WritGridInfo(tableName, gridDownLoadTimeSpeedInfo);
        }

        private BackgroundFuncResultWriter()
        {
#if ProblemCheck_BCP
            writer = new BackgroundFuncBcpWriter(MainModel.GetInstance());
#else
            writer = new BackgroundFuncSqlWriter(MainModel.GetInstance());
#endif
        }

        private readonly IBackgroundFuncWriter writer = null;

        private static BackgroundFuncResultWriter instance;

        private static object syncObj = new object();
    }

    interface IBackgroundFuncWriter
    {
        void WriteRoad(string tableName, List<BackgroundResult> resultList, int lastMaxID);
        void WriteRegion(string tableName, List<BackgroundResult> resultList, int lastMaxID);
        void WriteCell(string tableName, List<BackgroundResult> resultList, int lastMaxID);
        void WritGridInfo(string tableName, List<GridDownLoadTimeSpeedInfo> gridDownLoadTimeSpeedInfo);
    }

    class BackgroundFuncSqlWriter : IBackgroundFuncWriter
    {
        public BackgroundFuncSqlWriter(MainModel mainModel)
        {
            this.mainModel = mainModel;
        }

        public void WriteRoad(string tableName, List<BackgroundResult> resultList, int lastMaxID)
        {
            resultList.ForEach(result => Write(tableName, ++lastMaxID, result.SQLRoad));
        }

        public void WriteRegion(string tableName, List<BackgroundResult> resultList, int lastMaxID)
        {
            resultList.ForEach(result => Write(tableName, ++lastMaxID, result.SQLRegion));
        }

        public void WriteCell(string tableName, List<BackgroundResult> resultList, int lastMaxID)
        {
            resultList.ForEach(result => Write(tableName, ++lastMaxID, result.SQLCell));
        }

        public void WritGridInfo(string tableName, List<GridDownLoadTimeSpeedInfo> gridDownLoadTimeSpeedInfo)
        {
            //
        }

        private void Write(string tableName, int curMaxID, string resultSql)
        {
            string sql = string.Format("insert into {0} values ({1}, {2})", tableName, curMaxID, resultSql);
            DiySqlNonQuery queryNon = new DiySqlNonQuery(mainModel, sql);
            queryNon.Query();
        }

        private readonly MainModel mainModel; 
    }

    public class BackgroundFuncBcpWriter : IBackgroundFuncWriter
    {
        public BackgroundFuncBcpWriter(MainModel mainModel)
        {
            this.mainModel = mainModel;
        }

        public void WriteRoad(string tableName, List<BackgroundResult> resultList, int lastMaxID)
        {
            DataTable table = new DataTable(tableName);
            table.Columns.Add("iID", typeof(int));
            table.Columns.Add("iSubFuncID", typeof(int));
            table.Columns.Add("iFileID", typeof(int));
            table.Columns.Add("fileName", typeof(string));
            table.Columns.Add("iLongitudeStart", typeof(int));
            table.Columns.Add("iLatitudeStart", typeof(int));
            table.Columns.Add("iLongitudeMid", typeof(int));
            table.Columns.Add("iLatitudeMid", typeof(int));
            table.Columns.Add("iLongitudeEnd", typeof(int));
            table.Columns.Add("iLatitudeEnd", typeof(int));
            table.Columns.Add("istime", typeof(int));
            table.Columns.Add("ietime", typeof(int));
            table.Columns.Add("distanceLast", typeof(double));
            table.Columns.Add("sampleCount", typeof(int));
            table.Columns.Add("rxLevMean", typeof(double));
            table.Columns.Add("rxLevMin", typeof(double));
            table.Columns.Add("rxLevMax", typeof(double));
            table.Columns.Add("rxQualMean", typeof(double));
            table.Columns.Add("rxQualMin", typeof(double));
            table.Columns.Add("rxQualMax", typeof(double));
            table.Columns.Add("roadDesc", typeof(string));
            table.Columns.Add("areaDesc", typeof(string));
            table.Columns.Add("areaAgentDesc", typeof(string));
            table.Columns.Add("gridDesc", typeof(string));
            table.Columns.Add("cellIDDesc", typeof(string));
            table.Columns.Add("strDesc", typeof(string));
            table.Columns.Add("image1", typeof(byte[]));
            table.Columns.Add("strProject", typeof(string));

            foreach (BackgroundResult result in resultList)
            {
                object[] values = new object[]
                {
                    ++lastMaxID,
                    result.SubFuncID,
                    result.FileID, result.FileName,
                    result.iLongitudeStart, result.iLatitudeStart,
                    result.iLongitudeMid, result.iLatitudeMid,
                    result.iLongitudeEnd, result.iLatitudeEnd,
                    result.ISTime, result.IETime,
                    result.DistanceLast, result.SampleCount,
                    result.RxLevMean, result.RxLevMin, result.RxLevMax,
                    result.RxQualMean, result.RxQualMin, result.RxQualMax,
                    result.RoadDesc, result.AreaDesc, result.AreaAgentDesc, result.GridDesc, result.CellIDDesc, result.StrDesc,
                    DBDataViewer.ByteOpHelper.OXStrToByte(result.GetImageString().Substring(2)),
                    result.ProjectString,
                };
                table.Rows.Add(values);
            }
            Write(table);
        }

        public void WriteRegion(string tableName, List<BackgroundResult> resultList, int lastMaxID)
        {
            DataTable table = new DataTable(tableName);
            table.Columns.Add("iID", typeof(int));
            table.Columns.Add("iSubFuncID", typeof(int));
            table.Columns.Add("istime", typeof(int));
            table.Columns.Add("ietime", typeof(int));
            table.Columns.Add("iLongitudeMid", typeof(int));
            table.Columns.Add("iLatitudeMid", typeof(int));
            table.Columns.Add("sampleCount", typeof(int));
            table.Columns.Add("rxLevMean", typeof(double));
            table.Columns.Add("rxLevMin", typeof(double));
            table.Columns.Add("rxLevMax", typeof(double));
            table.Columns.Add("rxQualMean", typeof(double));
            table.Columns.Add("rxQualMin", typeof(double));
            table.Columns.Add("rxQualMax", typeof(double));
            table.Columns.Add("roadDesc", typeof(string));
            table.Columns.Add("areaDesc", typeof(string));
            table.Columns.Add("areaAgentDesc", typeof(string));
            table.Columns.Add("gridDesc", typeof(string));
            table.Columns.Add("cellIDDesc", typeof(string));
            table.Columns.Add("strDesc", typeof(string));
            table.Columns.Add("image1", typeof(byte[]));
            table.Columns.Add("strProject", typeof(string));

            foreach (BackgroundResult result in resultList)
            {
                object[] values = new object[]
                {
                    ++lastMaxID,
                    result.SubFuncID,
                    result.ISTime, result.IETime,
                    result.iLongitudeMid, result.iLatitudeMid,
                    result.SampleCount,
                    result.RxLevMean, result.RxLevMin, result.RxLevMax,
                    result.RxQualMean, result.RxQualMin, result.RxQualMax,
                    result.RoadDesc, result.AreaDesc, result.AreaAgentDesc, result.GridDesc, result.CellIDDesc, result.StrDesc,
                    DBDataViewer.ByteOpHelper.OXStrToByte(result.GetImageString().Substring(2)),
                    result.ProjectString,
                };
                table.Rows.Add(values);
            }
            Write(table);
        }

        public void WriteCell(string tableName, List<BackgroundResult> resultList, int lastMaxID)
        {
            DataTable table = new DataTable(tableName);
            table.Columns.Add("iID", typeof(int));
            table.Columns.Add("iSubFuncID", typeof(int));
            table.Columns.Add("iFileID", typeof(int));
            table.Columns.Add("fileName", typeof(string));
            table.Columns.Add("cellType", typeof(int));
            table.Columns.Add("LAC", typeof(int));
            table.Columns.Add("CI", typeof(int));
            table.Columns.Add("BCCH", typeof(int));
            table.Columns.Add("BSIC", typeof(int));
            table.Columns.Add("istime", typeof(int));
            table.Columns.Add("ietime", typeof(int));
            table.Columns.Add("iLongitudeStart", typeof(int));
            table.Columns.Add("iLatitudeStart", typeof(int));
            table.Columns.Add("iLongitudeMid", typeof(int));
            table.Columns.Add("iLatitudeMid", typeof(int));
            table.Columns.Add("iLongitudeEnd", typeof(int));
            table.Columns.Add("iLatitudeEnd", typeof(int));
            table.Columns.Add("distanceLast", typeof(double));
            table.Columns.Add("sampleCount", typeof(int));
            table.Columns.Add("rxLevMean", typeof(double));
            table.Columns.Add("rxLevMin", typeof(double));
            table.Columns.Add("rxLevMax", typeof(double));
            table.Columns.Add("rxQualMean", typeof(double));
            table.Columns.Add("rxQualMin", typeof(double));
            table.Columns.Add("rxQualMax", typeof(double));
            table.Columns.Add("roadDesc", typeof(string));
            table.Columns.Add("areaDesc", typeof(string));
            table.Columns.Add("areaAgentDesc", typeof(string));
            table.Columns.Add("gridDesc", typeof(string));
            table.Columns.Add("cellIDDesc", typeof(string));
            table.Columns.Add("strDesc", typeof(string));
            table.Columns.Add("image1", typeof(byte[]));
            table.Columns.Add("strProject", typeof(string));

            foreach (BackgroundResult result in resultList)
            {
                object[] values = new object[]
                {
                    ++lastMaxID,
                    result.SubFuncID,
                    result.FileID, result.FileName,
                    (int)result.CellType, result.LAC, result.CI, result.BCCH, result.BSIC,
                    result.ISTime, result.IETime,
                    result.iLongitudeStart, result.iLatitudeStart,
                    result.iLongitudeMid, result.iLatitudeMid,
                    result.iLongitudeEnd, result.iLatitudeEnd,
                    result.DistanceLast, result.SampleCount,
                    result.RxLevMean, result.RxLevMin, result.RxLevMax,
                    result.RxQualMean, result.RxQualMin, result.RxQualMax,
                    result.RoadDesc, result.AreaDesc, result.AreaAgentDesc, result.GridDesc, result.CellIDDesc, result.StrDesc,
                    DBDataViewer.ByteOpHelper.OXStrToByte(result.GetImageString().Substring(2)),
                    result.ProjectString,
                };
                table.Rows.Add(values);
            }
            Write(table);
        }

        public void WritGridInfo(string tableName, List<GridDownLoadTimeSpeedInfo> gridDownLoadTimeSpeedInfo)
        {
            string sql = string.Format("exec sp_gd_grid_info_create '{0}', '{1}','{2}'", tableName, gridDownLoadTimeSpeedInfo[0].StrCarrName
                , gridDownLoadTimeSpeedInfo[0].StrGridType);
            DiySqlNonQuery queryNon = new DiySqlNonQuery(mainModel, sql);
            queryNon.Query();

            DataTable table = new DataTable(tableName);
            table.Columns.Add("地市", typeof(string));
            table.Columns.Add("网格类型", typeof(string));
            table.Columns.Add("网格号", typeof(string));
            table.Columns.Add("项目", typeof(string));
            table.Columns.Add("业务", typeof(string));
            table.Columns.Add("运营商", typeof(string));
            table.Columns.Add("左上经度", typeof(string));
            table.Columns.Add("左上纬度", typeof(int));
            table.Columns.Add("中心经度", typeof(int));
            table.Columns.Add("中心纬度", typeof(int));
            table.Columns.Add("中心经纬度", typeof(string));
            table.Columns.Add("TDD_RSRP采样点数", typeof(int));
            table.Columns.Add("TDD_下载采样点数", typeof(int));
            table.Columns.Add("TDD下载时长", typeof(double));
            table.Columns.Add("TDD下载量", typeof(double));
            table.Columns.Add("TDD下载速率", typeof(double));
            table.Columns.Add("主队脱网标记", typeof(double));
            table.Columns.Add("主队占它网时长", typeof(double));
            table.Columns.Add("主队预留字段1", typeof(double));
            table.Columns.Add("主队预留字段2", typeof(double));
            table.Columns.Add("FDD_RSRP采样点数", typeof(int));
            table.Columns.Add("FDD_下载采样点数", typeof(int));
            table.Columns.Add("FDD下载时长", typeof(double));
            table.Columns.Add("FDD下载量", typeof(double));
            table.Columns.Add("FDD下载速率", typeof(double));
            table.Columns.Add("客队脱网标记", typeof(double));
            table.Columns.Add("客队占它网时长", typeof(double));
            table.Columns.Add("客队预留字段1", typeof(double));
            table.Columns.Add("客队预留字段2", typeof(double));

            foreach (GridDownLoadTimeSpeedInfo result in gridDownLoadTimeSpeedInfo)
            {
                object[] values = new object[]
                {
                    result.StrCityInfo,"","",result.StrCarrName,result.StrGridType,result.StrGridName, result.Itllng,
                    result.Itllat, result.Icentlng, result.Icentlat, result.StrGirdCenterInfo, 
                    result.D主队RSRP采样点数,result.DTDDSampleNum, result.DTDDDownTime, result.DTDDDownSize,
                    result.DTDDDownSpeed,result.D主队脱网标记, result.D主队占它网时长,0,0
                    ,result.D客队RSRP采样点数,result.DFDDSampleNum,result.DFDDDownTime, result.DFDDDownSize,
                    result.DFDDDownSpeed, result.D客队脱网标记, result.D客队占它网时长,0,0
                };
                table.Rows.Add(values);
            }
            Write(table);
        }

        private void Write(DataTable table)
        {
            string connStr = GetSqlConnStr();
            SqlBulkCopy bcp = null;
            try
            {
                bcp = new SqlBulkCopy(connStr);
                bcp.DestinationTableName = table.TableName;
                bcp.BatchSize = 2000;
                bcp.BulkCopyTimeout = 3600;
                bcp.WriteToServer(table);
            }
            finally
            {
                if (bcp != null)
                {
                    bcp.Close();
                }
            }
        }

        private string GetSqlConnStr()
        {
            string cityName = DistrictManager.GetInstance().getDistrictName(mainModel.DistrictID);
            if (cityName == null)
            {
                throw (new Exception("获取地市名错误, 地市ID=" + mainModel.DistrictID));
            }

            if (cityConnDic == null)
            {
                MasterCom.RAMS.ZTFunc.DIYQueryDBConect diyConn = new ZTFunc.DIYQueryDBConect(mainModel, "");
                diyConn.Query();
                cityConnDic = new Dictionary<string, string>(diyConn.strConnDic);
            }

            if (!cityConnDic.ContainsKey(cityName))
            {
                throw (new Exception("从dbsetting表中获取地市信息失败, 地市名=" + cityName));
            }
            return cityConnDic[cityName];
        }

        private Dictionary<string, string> cityConnDic;

        private readonly MainModel mainModel;
    }
}
