﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTCellSet
{
    public class CellServiceCondition
    {
        public CellServiceCondition(Cell cell, TestPoint testPoint)
        {
            this.cell = cell;
            AddTestPoint(testPoint);
        }

        public CellServiceCondition(string cellName, TestPoint testPoint)
        {
            this.cell = null;
            this.cellName = cellName;
            string[] s = cellName.Split(',');
            lac = s[0];
            ci = s[1];
            AddTestPoint(testPoint);
        }

        public Cell cell { get; set; }
        public string cellName { get; set; }
        public string lac { get; set; }
        public string ci { get; set; }
        public int testPointCount { get; set; }
        public int serviceTimes { get; set; }
        public int category { get; set; }

        public double? ServiceSeconds
        {
            get
            {
                if (testPointCount == 0)
                {
                    return null;
                }
                return (double?)Math.Round(testPointCount * 480.0 / 1000.0, 2);
            }
        }

        public string Category
        {
            get
            {
                switch (category)
                {
                    case 0:
                        return "A";
                    case 1:
                        return "B";
                    case 2:
                        return "C";
                    default:
                        return "";
                }
            }
        }

        public void AddTestPoint(TestPoint testPoint)
        {
            if (testPoint == null)
            {
                return;
            }
            testPointCount++;
        }
    }

    public class RegionCellServiceCondition
    {
        public RegionCellServiceCondition(string regionName, List<CellServiceCondition> cellList)
        {
            this.regionName = regionName;
            cellServiceConditions = cellList;
        }

        public string regionName { get; set; }
        public List<CellServiceCondition> cellServiceConditions { get; set; }
    }
}
