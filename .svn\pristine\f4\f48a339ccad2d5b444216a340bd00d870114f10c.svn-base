﻿using MasterCom.RAMS.BackgroundFunc;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class SmallBtsAcceptManager_XJ : MultiStationAutoAcceptManager
    {
        protected override List<AcpAutoKpiBase> getAcceptAnaList(LTEBTSType btsType)
        {
            List<AcpAutoKpiBase> acceptAnaList = new List<AcpAutoKpiBase>() 
                {
                    new AcpAutoIndoorRrcRate(),
                    new AcpAutoIndoorAccRate(),
                    new AcpAutoIndoorErabRate(),
                    new AcpAutoIndoorCsfbRate(),
                    new AcpAutoIndoorLeakOut_LockEarfcn(),
                    new AcpAutoIndoorLeakOut_Scan(),
                    new AcpAutoIndoorLevelTestKpi(),
                    new AcpAutoIndoorInnerHandover(),
                    new AcpAutoIndoorCoverFloor(),
                    new SmallBtsAcpAutoVolteVoiceMo(),
                    new SmallBtsAcpAutoVolteVoiceMt()
                };
            return acceptAnaList;
        }

        public void AddFileAcceptInfoToResult(Dictionary<int, SmallBtsAcceptInfo> btsAcceptInfoDic)
        {
            if (AcceptFileInfo == null || AcceptFileInfo.AcceptKpiDic == null
                || AcceptFileInfo.AcceptKpiDic.Count <= 0)
            {
                return;
            }

            int btsId = AcceptFileInfo.LteCell.BelongBTS.BTSID;
            SmallBtsAcceptInfo btsAcceptInfo;
            if (!btsAcceptInfoDic.TryGetValue(btsId, out btsAcceptInfo))
            {
                btsAcceptInfo = new SmallBtsAcceptInfo(AcceptFileInfo.LteCell.BelongBTS);
                btsAcceptInfoDic.Add(btsId, btsAcceptInfo);
            }
            InDoorCellAcceptInfo cellAcceptInfo;
            if (!btsAcceptInfo.CellsAcceptDic.TryGetValue(AcceptFileInfo.LteCell.CellID, out cellAcceptInfo))
            {
                cellAcceptInfo = new SmallCellAcceptInfo(AcceptFileInfo.LteCell);
                btsAcceptInfo.CellsAcceptDic.Add(AcceptFileInfo.LteCell.CellID, cellAcceptInfo);
            }
            cellAcceptInfo.AddAcceptKpiInfo(AcceptFileInfo.FileName, AcceptFileInfo.AcceptKpiDic);
        }
    }
}
