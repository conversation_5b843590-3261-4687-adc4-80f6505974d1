﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using System.IO;
using System.Windows.Forms;

namespace MasterCom.RAMS.Net
{
    public static partial class RequestType
    {
        public const byte KPI_CELL_GRID = 0x16;
        public const byte KPI_CELL = 0x15;
    }
    public static partial class ResponseType
    {
        public const byte RESTYPE_DIY_CELL_COVER_GRID_GSM = 0xa1;

        public const byte RESTYPE_DIY_CELL_COVER_GRID_GPRS = 0xa5;

        public const byte RESTYPE_DIY_CELL_COVER_GRID_SCAN = 0xa7;

        public const byte RESTYPE_DIY_CELL_COVER_GRID_TDSCDMA_AMR = 0xa9;

        public const byte RESTYPE_DIY_CELL_COVER_GRID_TDSCDMA_PS = 0xab;

        public const byte RESTYPE_DIY_CELL_COVER_GRID_TDSCDMA_VP = 0xad;

        public const byte RESTYPE_DIY_CELL_COVER_GRID_WCDMA_AMR = 0xb0;

        public const byte RESTYPE_DIY_CELL_COVER_GRID_WCDMA_PS = 0xb2;

        public const byte RESTYPE_DIY_CELL_COVER_GRID_WCDMA_VP = 0xb4;

        public const byte RESTYPE_DIY_CELL_COVER_GRID_WCDMA_PSHS = 0xb6;

        public const byte RESTYPE_DIY_CELL_COVER_GRID_CDMA_V = 0xb8;

        public const byte RESTYPE_DIY_CELL_COVER_GRID_CDMA_D = 0xba;

        public const byte RESTYPE_DIY_CELL_COVER_GRID_CDMA2000_D = 0xbc;

    }

    public abstract class DIYCellStatQueryBase :QueryBase
    {
        protected DIYCellStatQueryBase(MainModel mainModel)
            : base(mainModel)
        {
        }
        protected override bool isValidCondition()
        {
            return true;
        }
        protected abstract StatTbToken getBranchToken();
        
        protected void AddDIYRegionOfCell(Package package,CellQueryCondUnit cond)
        {
            package.Content.AddParam((byte)OpOptionDef.AreaSelectIntersect);
            package.Content.AddParam(cond.ltLongitude);
            package.Content.AddParam(cond.ltLatitude);
            package.Content.AddParam(cond.brLongitude);
            package.Content.AddParam(cond.brLatitude);
        }

        protected void AddDIYLacCIOfCell(Package package,CellQueryCondUnit cond)
        {
            package.Content.AddParam((byte)OpOptionDef.CellSelect);
            package.Content.AddParam(cond.LAC);
            package.Content.AddParam(cond.CI);
        }

        private void parseRectByLongLatDistance(double longitude, double latitude, int meter, out double ltlong, out double ltlat, out double brlong, out double brlat)//通过中心经纬度及其到边框的距离求经纬度（模糊值）
        {
            double longPerMeter = 0.00001;
            double latPerMeter = 0.000009;
            ltlong = longitude - meter * longPerMeter;
            ltlat = latitude + meter * latPerMeter;
            brlong = longitude + meter * longPerMeter;
            brlat = latitude - meter * latPerMeter;
        }

        private List<ReportStyle> rptStyleList = null;
        private ReportStyle curSelStyle = null;
        private bool curSelStyleContentAll = false;
        private void loadReportFromFile()
        {
            try
            {
                System.IO.DirectoryInfo directory = new DirectoryInfo(string.Format(Application.StartupPath + "/config/templates/"));
                System.IO.FileInfo[] files = directory.GetFiles("kpi_*.xml", SearchOption.TopDirectoryOnly);
                if (files.Length > 0)
                {
                    loadTemplates(files);
                }
                else
                {
                    loadReports();
                }
            }
            catch (Exception e)
            {
                log.Warn("读入报表模板文件发生错误:" + e.Message);
            }
            if (rptStyleList == null)
            {
                rptStyleList = new List<ReportStyle>();
            }
        }

        private void loadTemplates(System.IO.FileInfo[] files)
        {
            if (rptStyleList == null)
            {
                rptStyleList = new List<ReportStyle>();
            }
            rptStyleList.Clear();
            foreach (System.IO.FileInfo file in files)
            {
                XmlConfigFile configFile = new XmlConfigFile(file.FullName);
                Dictionary<string, object> dic = configFile.GetItemValue("ReportSetting", "styles") as Dictionary<string, object>;
                if (dic == null)
                {
                    continue;
                }
                ReportStyle rptstyle = new ReportStyle();
                rptstyle.Param = dic;
                int index = file.Name.IndexOf("kpi_") + 4;
                rptstyle.name = file.Name.Substring(index).Replace(".xml", "");
                rptStyleList.Add(rptstyle);
            }
            rptStyleList.Sort();
        }

        private void loadReports()
        {
            XmlConfigFile configFile = new XmlConfigFile(string.Format(Application.StartupPath + "/config/reports.xml"));
            List<Object> list = configFile.GetItemValue("ReportSetting", "styles") as List<Object>;
            if (list != null)
            {
                if (rptStyleList == null)
                {
                    rptStyleList = new List<ReportStyle>();
                }
                rptStyleList.Clear();
                foreach (object value in list)
                {
                    ReportStyle rptStyle = new ReportStyle();
                    rptStyle.Param = value as Dictionary<string, object>;
                    rptStyleList.Add(rptStyle);
                }
                rptStyleList.Sort();
            }
        }

        private List<string> getNeededImgDefs(int carrierId)
        {
            Dictionary<string, bool> retDic = new Dictionary<string, bool>();
            if(curSelStyle==null)
            {
                return new List<string>();
            }
            foreach (RptCell rpt in curSelStyle.rptCellList)
            {
                if (rpt.carrierID == carrierId && rpt.exp != null)
                {
                    extractIncludedParams(rpt.exp, retDic);
                }
            }
            return new List<string>(retDic.Keys);
        }
        private string getTokenStrFrom(string str, int pos, ref int end)
        {
            if (pos < 0 || pos > str.Length - 1)
            {
                return "";
            }
            int start = pos;
            end = pos;
            while (start >= 0)//向前找
            {
                char ch = str[start];
                if (ch == '+' || ch == '-' || ch == '*' || ch == '/' || ch == ' ' || ch == '{' || ch == '}' || ch == '(' || ch == ')')
                {
                    break;
                }
                start--;
            }
            while (end <= str.Length - 1)//向后找
            {
                char ch = str[end];
                if (ch == '+' || ch == '-' || ch == '*' || ch == '/' || ch == ' ' || ch == '{' || ch == '}' || ch == '(' || ch == ')')
                {
                    break;
                }
                end++;
            }
            if (end <= str.Length && end > start)
            {
                return str.Substring(start + 1, end - start - 1);
            }
            return "";
        }

        private void extractIncludedParams(string expstr, Dictionary<string, bool> retDic)
        {
            List<string> exps = new List<string>();
            int pos = 0;
            int length = expstr.Length;
            int start = pos; int end = pos;
            while (end < pos + length)
            {
                string str = getTokenStrFrom(expstr, start, ref end);
                if (!string.IsNullOrEmpty(str))
                    exps.Add(str);
                else
                    end++;
                start = end;
            }
            foreach (string str in exps)
            {
                string imgCode = getCodeFromStr(str);
                if(imgCode!= string.Empty)
                {
                    string id = InterfaceManager.GetInstance().GetRevisedStatImgTriadID(getBranchToken(),imgCode);
                    if (id != null)
                    {
                        retDic[id] = true;
                    }
                    
                }
            }
        }

        private string getCodeFromStr(string str)
        {
            int pos = str.IndexOf('_');
            if(pos!=-1)
            {
                return str.Substring(pos + 1);
            }
            else
            {
                return string.Empty;
            }
        }
        protected void fillContentNeeded_Event(Package package)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("0,1,43,");
            sb.Append("0,2,43,");
            sb.Append("0,3,43,");
            sb.Append("0,4,43,");
            sb.Append("0,5,43,");
            sb.Append("0,6,43,");
            sb.Append("0,7,43,");
            sb.Append("0,8,43,"); 
            sb.Append("0,9,43,");
            sb.Append("0,10,43,");
            sb.Append("0,11,43,");
            sb.Append("0,12,43,");
            sb.Append("0,13,43,");
            sb.Append("0,14,43,");
            sb.Append("0,15,43,");
            sb.Append("0,16,43,");
            sb.Append("0,17,43,");
            sb.Append("0,18,43,");
            sb.Append("0,19,43,");
            sb.Append("0,20,43,");
            sb.Append("0,21,43,");
            sb.Append("0,22,43,");
            sb.Append("0,23,43,");
            sb.Append("0,24,43,");
            sb.Append("0,25,43,");
            sb.Append("0,26,43");
            package.Content.AddParam(sb.ToString());
        }
        protected void fillContentNeeded_ImgGrid(Package package,int carrierId)
        {
            if (curSelStyleContentAll)
            {
                package.Content.AddParam("-1,-1,-1");
            }
            else
            {
                List<string> imgTriIDDefList = getNeededImgDefs(carrierId);
                StringBuilder sbuilder = new StringBuilder();
                for (int i = 0; i < imgTriIDDefList.Count; i++)
                {
                    sbuilder.Append(imgTriIDDefList[i]);
                    if (i < imgTriIDDefList.Count - 1)
                    {
                        sbuilder.Append(",");
                    }
                }
                package.Content.AddParam(sbuilder.ToString());
            }
        }
        protected override void query()
        {
            loadReportFromFile();
            SelectReportDlg dlg = new SelectReportDlg();
            dlg.FillCurrentReports(ref rptStyleList);
            if(dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            curSelStyle = dlg.GetSelectedReport(out curSelStyleContentAll);
            curEventStatFilter = dlg.GetEventStatFilter();
            //==
            MainModel.CurChinaMobileStatReportData = null;
            MainModel.CurChinaUnicomStatReportData = null;
            MainModel.CurChinaTelecomStatReportData = null;
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询...";
#if LT
            if (Condition.CarrierTypes.Contains((int)2))
            {
                if (!WaitBox.CancelRequest)
                {
                    queryByCarrier(queryChinaUnicomInThread);
                }
            }
#else
            if (Condition.CarrierTypes.Contains(1))
            {
                if (WaitBox.CancelRequest)
                {
                    //
                }
                else
                {
                    queryByCarrier(queryChinaMobileInThread);
                }
            }
#endif
            MainModel.FireStatQueried(this, curSelStyle, "");
        }

        private void queryByCarrier(CallBackMethodWithParams queryInThread)
        {
            ClientProxy clientProxy = new ClientProxy();

            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            try
            {
                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        private void queryChinaMobileInThread(object o) //查询中国移动KPI统计
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                int periodCount = Condition.Periods.Count;
                int index = 0;
                List<DataUnitAreaKPIQuery> paraPeriodList = new List<DataUnitAreaKPIQuery>();
                if(condition.IsByRound)
                {
                    queryPeriodInfo(clientProxy, package, paraPeriodList,1, null, true);
                }
                else
                {
                    foreach (TimePeriod period in Condition.Periods)
                    {
                        if (WaitBox.CancelRequest)
                        {
                            break;
                        }
                        WaitBox.Text = "开始统计时段[" + period.GetShortString() + "]内的数据...";
                        WaitBox.ProgressPercent = index / periodCount + 10;
                        queryPeriodInfo(clientProxy, package, paraPeriodList,1, period, false);
                    }
                }
                
                MainModel.CurChinaMobileStatReportData = new StatReportData(paraPeriodList);
            }catch(Exception ex)
            {
                log.Error(ex.Message);
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private void queryPeriodInfo(ClientProxy clientProxy, Package package, List<DataUnitAreaKPIQuery> paraPeriodList,int carrierId, TimePeriod period,bool byRound)
        {
            List<CellQueryCondUnit> cellLacCiChangedPeriods = parseChangeLACCIPeriodsOfCell(period);
            foreach(CellQueryCondUnit cond in cellLacCiChangedPeriods)
            {
                //img grid
                prepareStatPackage_ImgGrid_FileFilter(package, cond, (byte)carrierId, byRound);
                fillContentNeeded_ImgGrid(package, carrierId);
                clientProxy.Send();
                recieveInfo_ImgGrid(clientProxy, paraPeriodList);
                //event
                prepareStatPackage_Event_FileFilter(package, cond, (byte)carrierId, byRound);
                prepareStatPackage_Event_EventFilter(package, cond);
                fillContentNeeded_Event(package);
                clientProxy.Send();
                recieveInfo_Event(clientProxy, paraPeriodList,cond);
            }
        }

        private List<CellQueryCondUnit> parseChangeLACCIPeriodsOfCell(TimePeriod period)
        {
            List<CellQueryCondUnit> condPeriodList = new List<CellQueryCondUnit>();
            int meter = 5000;
            double ltlong = 0, ltlat = 0, brlong = 0, brlat = 0;
            if (condition.Geometorys.SelectedCell != null)
            {
                parseRectByLongLatDistance(condition.Geometorys.SelectedCell.Longitude, condition.Geometorys.SelectedCell.Latitude, meter, out ltlong, out ltlat, out brlong, out brlat);
                setCellSnaps(period, condPeriodList, ltlong, ltlat, brlong, brlat);
            }
            else if (condition.Geometorys.SelectedTDCell != null)
            {
                parseRectByLongLatDistance(condition.Geometorys.SelectedTDCell.Longitude, condition.Geometorys.SelectedTDCell.Latitude, meter, out ltlong, out ltlat, out brlong, out brlat);
                setTDCellSnaps(period, condPeriodList, ltlong, ltlat, brlong, brlat);
            }
            else if (condition.Geometorys.SelectedWCell != null)
            {
                parseRectByLongLatDistance(condition.Geometorys.SelectedWCell.Longitude, condition.Geometorys.SelectedWCell.Latitude, meter, out ltlong, out ltlat, out brlong, out brlat);
                setWCellSnaps(period, condPeriodList, ltlong, ltlat, brlong, brlat);
            }
            return condPeriodList;
        }

        private void setCellSnaps(TimePeriod period, List<CellQueryCondUnit> condPeriodList, double ltlong, double ltlat, double brlong, double brlat)
        {
            List<Cell> cellsnaps = condition.Geometorys.SelectedCell.GetAll(period);
            int formarLac = 0;
            int formarCi = 0;
            foreach (Cell snap in cellsnaps)
            {
                int lac = snap.LAC;
                int ci = snap.CI;
                if (lac != 0 && lac != formarLac && ci != 0 && ci != formarCi)
                {
                    CellQueryCondUnit cond = new CellQueryCondUnit();
                    cond.LAC = lac;
                    cond.CI = ci;
                    TimePeriod tpseg = new TimePeriod();
                    DateTime fromTime = snap.ValidPeriod.BeginTime;
                    DateTime toTime = snap.ValidPeriod.EndTime;
                    if (fromTime < period.BeginTime)
                    {
                        fromTime = period.BeginTime;
                    }
                    if (toTime > period.EndTime)
                    {
                        toTime = period.EndTime;
                    }
                    tpseg.SetPeriod(fromTime, toTime);
                    cond.period = tpseg;
                    cond.ltLongitude = ltlong;
                    cond.ltLatitude = ltlat;
                    cond.brLongitude = brlong;
                    cond.brLatitude = brlat;
                    condPeriodList.Add(cond);
                    formarLac = lac;
                    formarCi = ci;
                }
            }
        }

        private void setTDCellSnaps(TimePeriod period, List<CellQueryCondUnit> condPeriodList, double ltlong, double ltlat, double brlong, double brlat)
        {
            List<TDCell> cellsnaps = condition.Geometorys.SelectedTDCell.GetAll(period);
            int formarLac = 0;
            int formarCi = 0;
            foreach (TDCell snap in cellsnaps)
            {
                int lac = snap.LAC;
                int ci = snap.CI;
                if (lac != 0 && lac != formarLac && ci != 0 && ci != formarCi)
                {
                    CellQueryCondUnit cond = new CellQueryCondUnit();
                    cond.LAC = lac;
                    cond.CI = ci;
                    cond.period = snap.ValidPeriod;
                    cond.ltLongitude = ltlong;
                    cond.ltLatitude = ltlat;
                    cond.brLongitude = brlong;
                    cond.brLatitude = brlat;
                    condPeriodList.Add(cond);
                    formarLac = lac;
                    formarCi = ci;
                }
            }
        }

        private void setWCellSnaps(TimePeriod period, List<CellQueryCondUnit> condPeriodList, double ltlong, double ltlat, double brlong, double brlat)
        {
            List<WCell> cellsnaps = condition.Geometorys.SelectedWCell.GetAll(period);
            int formarLac = 0;
            int formarCi = 0;
            foreach (WCell snap in cellsnaps)
            {
                int lac = snap.LAC;
                int ci = snap.CI;
                if (lac != 0 && lac != formarLac && ci != 0 && ci != formarCi)
                {
                    CellQueryCondUnit cond = new CellQueryCondUnit();
                    cond.LAC = lac;
                    cond.CI = ci;
                    cond.period = snap.ValidPeriod;
                    cond.ltLongitude = ltlong;
                    cond.ltLatitude = ltlat;
                    cond.brLongitude = brlong;
                    cond.brLatitude = brlat;
                    condPeriodList.Add(cond);
                    formarLac = lac;
                    formarCi = ci;
                }
            }
        }
#if LT
        private void queryChinaUnicomInThread(object o) //查询中国联通KPI统计
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                int periodCount = Condition.Periods.Count;
                int index = 0;
                List<DataUnitAreaKPIQuery> paraPeriodList = new List<DataUnitAreaKPIQuery>();
                if (condition.IsByRound)
                {
                    queryPeriodInfo(clientProxy, package, paraPeriodList,2, null, true);
                }
                else
                {
                    foreach (TimePeriod period in Condition.Periods)
                    {
                        if (WaitBox.CancelRequest)
                        {
                            break;
                        }
                        WaitBox.Text = "开始统计时段[" + period.GetShortString() + "]内的数据...";
                        WaitBox.ProgressPercent = index / periodCount + 10;
                        queryPeriodInfo(clientProxy, package, paraPeriodList, 2, period, false);
                    }
                }
                MainModel.CurChinaUnicomStatReportData = new StatReportData(paraPeriodList);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
            }
            finally
            {
                WaitBox.Close();
            }
            
        }

        private void queryChinaTelecomInThread(object o) //查询中国电信KPI统计
        {
            try
            {
                ClientProxy clientProxy = (ClientProxy)o;
                Package package = clientProxy.Package;
                int periodCount = Condition.Periods.Count;
                int index = 0;
                List<DataUnitAreaKPIQuery> paraPeriodList = new List<DataUnitAreaKPIQuery>();
                if (condition.IsByRound)
                {
                    queryPeriodInfo(clientProxy, package, paraPeriodList,3, null, true);
                }
                else
                {
                    foreach (TimePeriod period in Condition.Periods)
                    {
                        if (WaitBox.CancelRequest)
                        {
                            break;
                        }
                        WaitBox.Text = "开始统计时段[" + period.GetShortString() + "]内的数据...";
                        WaitBox.ProgressPercent = index / periodCount + 10;
                        queryPeriodInfo(clientProxy, package, paraPeriodList, 3, period, false);
                    }
                }
                MainModel.CurChinaTelecomStatReportData = new StatReportData(paraPeriodList);
            }
            catch(Exception ex)
            {
                log.Error(ex.Message);
            }
            finally
            {
                WaitBox.Close();
            }
        }
#endif
        protected virtual void recieveInfo_Event(ClientProxy clientProxy, List<DataUnitAreaKPIQuery> paraRetList,CellQueryCondUnit cond)
        {
            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
            DTDataHeaderManager.GetInstance();
            List<ColumnDefItem> curDefColumnDef = new List<ColumnDefItem>();
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 0;
            bool isSeparateEvtByServiceID = this.curSelStyle.IsSeparateEvtByServiceID;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
#region Read Stat Data
                if (isFileHeaderContentType(package.Content.Type))
                {
                    //
                }
                else if (package.Content.Type == ResponseType.COLUMN_DEFINE)
                {
                    curDefColumnDef.Clear();
                    string idpairs = package.Content.GetParamString();
                    parseToCurColumnDef(idpairs, curDefColumnDef);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_EVENT)
                {
                    NREventHelper.ReSetIntCI(curDefColumnDef);
                    addValidRes(cond, retResult, curDefColumnDef, package, isSeparateEvtByServiceID);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_AREA_EVENT_NR)
                {
                    NREventHelper.SetLongCI(curDefColumnDef); 
                    addValidRes(cond, retResult, curDefColumnDef, package, isSeparateEvtByServiceID);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                #endregion

                setProgress(ref index, ref progress);
            }
            paraRetList.Add(retResult);
        }

        private void addValidRes(CellQueryCondUnit cond, DataUnitAreaKPIQuery retResult, List<ColumnDefItem> curDefColumnDef, 
            Package package, bool isSeparateEvtByServiceID)
        {
            DataEvent data = DataEvent.Create(package.Content, curDefColumnDef);
            if (data.LAC == cond.LAC && data.CI == cond.CI && isValidPoint(data.jd, data.wd))
            {
                bool hasFilter = checkEventFilter(data);
                if (hasFilter)
                {
                    retResult.addStatData(data, isSeparateEvtByServiceID);
                }
            }
        }

        public EventStatFilter curEventStatFilter { get; set; }
        /// <summary>
        /// 进行事件条件过滤
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        private bool checkEventFilter(DataEvent data)
        {
            if (curEventStatFilter != null)
            {
                return curEventStatFilter.CheckEventFilter(data);
            }
            return true;
        }
        protected virtual void recieveInfo_ImgGrid(ClientProxy clientProxy, List<DataUnitAreaKPIQuery> paraRetList)
        {
            DataUnitAreaKPIQuery retResult = new DataUnitAreaKPIQuery();
            DTDataHeaderManager.GetInstance();
            List<StatImgDefItem> curImgColumnDef = new List<StatImgDefItem>();
            int index = 0;
            Package package = clientProxy.Package;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                #region Read Stat Data
                if (package.Content.Type == ResponseType.COLUMN_DEFINE)
                {
                    curImgColumnDef.Clear();
                    string idpairs = package.Content.GetParamString();
                    parseToCurImgColumnDef(idpairs, curImgColumnDef);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_GSM
                    || package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_GPRS)
                {
                    DataGSM_NewImg newImg = new DataGSM_NewImg();
                    addWInfoDic(curImgColumnDef, package, newImg);
                    retResult.addStatData(newImg);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_TDSCDMA_AMR
                   || package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_TDSCDMA_PS
                   || package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_TDSCDMA_VP)
                {
                    DataTDSCDMA_NewImg newImg = new DataTDSCDMA_NewImg();
                    addWInfoDic(curImgColumnDef, package, newImg);
                    retResult.addStatData(newImg);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_WCDMA_AMR
                   || package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_WCDMA_PS
                   || package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_WCDMA_VP
                   || package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_WCDMA_PSHS)
                {
                    DataWCDMA_AMR newImg = new DataWCDMA_AMR();
                    addWInfoDic(curImgColumnDef, package, newImg);
                    retResult.addStatData(newImg);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_CDMA_V
                   || package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_CDMA_D
                   || package.Content.Type == ResponseType.RESTYPE_DIY_CELL_COVER_GRID_CDMA2000_D)
                {
                    DataCDMA_Voice newImg = new DataCDMA_Voice();
                    addWInfoDic(curImgColumnDef, package, newImg);
                    retResult.addStatData(newImg);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                #endregion

                setProgress(ref index, ref progress);
            }
            paraRetList.Add(retResult);
        }

        private void setProgress(ref int index, ref int progress)
        {
            if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
            {
                progress++;
                if (progress > 95)
                {
                    progress = 5;
                    index = 0;
                }
                if (progress % 5 == 0)
                {
                    WaitBox.ProgressPercent = progress;
                }
            }
        }

        private void addWInfoDic(List<StatImgDefItem> curImgColumnDef, Package package, PartialData newImg)
        {
            package.Content.GetParamInt();//lac
            package.Content.GetParamInt();//ci
            foreach (StatImgDefItem cdf in curImgColumnDef)
            {
                byte[] imgBytes = package.Content.GetParamBytes();
                Dictionary<string, DataItem> cellStatInfoDic = StatDataConverter.parseByte(imgBytes);
                foreach (string str in cellStatInfoDic.Keys)
                {
                    newImg.wInfoDic.Add(str, double.Parse(cellStatInfoDic[str].Value.ToString()));
                }
            }
        }

        protected virtual void prepareStatPackage_ImgGrid_FileFilter(Package package, CellQueryCondUnit cond, byte carrierID, bool byRound)
        {
        }
        protected virtual void prepareStatPackage_Event_FileFilter(Package package, CellQueryCondUnit period, byte carrierID, bool byRound)
        {
        }
        protected virtual void prepareStatPackage_Event_EventFilter(Package package,CellQueryCondUnit cond)
        {
        }
        protected virtual bool isValidPoint(double ltX,double ltY,double brX,double brY)
        {
            return true;
        }
        protected virtual bool isValidPoint(double jd, double wd)
        {
            return true;
        }

    }
    public class CellQueryCondUnit
    {
        public int LAC { get; set; }
        public int CI { get; set; }
        public TimePeriod period { get; set; }
        public double ltLongitude { get; set; }
        public double ltLatitude { get; set; }
        public double brLongitude { get; set; }
        public double brLatitude { get; set; }
    };
}
