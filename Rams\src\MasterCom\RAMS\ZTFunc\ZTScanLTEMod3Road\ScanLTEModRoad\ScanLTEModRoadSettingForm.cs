﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ScanLTEModRoadSettingForm : BaseForm
    {
        public ScanLTEModRoadSettingForm()
        {
            InitializeComponent();
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
        }

        public void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        public void BtnOK_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        public ScanLTEModRoadCondition GetCondition()
        {
            ScanLTEModRoadCondition cond = new ScanLTEModRoadCondition();
            cond.MaxRxlev = (double)numMaxRxlev.Value;
            cond.SampleInterval = (double)numSampleInterval.Value;
            cond.RoadLength = (double)numRoadLength.Value;
            cond.DiffRxlev = (double)numDiffRxlev.Value;
            return cond;
        }
    }
}
