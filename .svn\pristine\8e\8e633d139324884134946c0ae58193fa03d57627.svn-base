﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LTEAntennaMultiCoverageForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(LTEAntennaMultiCoverageForm));
            this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCallapsAll = new System.Windows.Forms.ToolStripMenuItem();
            this.panel1 = new System.Windows.Forms.Panel();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.labelControlType = new DevExpress.XtraEditors.LabelControl();
            this.btnColorSetting = new DevExpress.XtraEditors.SimpleButton();
            this.radioGroupType = new DevExpress.XtraEditors.RadioGroup();
            this.listViewTotal_LTE = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnECI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnEARFCN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnPCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCovType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSample3Count = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAbsLevel = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAbsConditionSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRelLevel = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRelConditionSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMulLevel = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMulConditionSampleCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRel4Rate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDistanceCov = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnIPCINum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnM3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnM6 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRSRPAVG = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSINRAVG = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRSRPDIFF6 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSINRDIFF6 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRSRPDT = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSINRDT = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRSRPMR = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSINRMR = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnConnectRare = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDropRte = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnHandRate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnERABRate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnERABDrop = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnHandNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnHandSuNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnHandFaNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherCellCoverType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnolvColumnOtherEARFCN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnISSame = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnolvColumnOtherPCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherM3 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherM6 = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherCellSample = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherRSRPDIFF = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherSINRDIFF = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherCellRscp = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherSinr = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherCellDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherRsrpCellDiff = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherRsrpAvgDT = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherSinrAvgDT = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherRsrpAvgMainDT = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherSinrAvgMainDT = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherRsrpRateMR = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherSinrAvgMR = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherConnectRate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherDropRate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherHandRate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherERABRate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherERABDropRate = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherMainCellHandNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherMainCellHandSUNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnOtherMainCellHandFANum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBMainSampleNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBMainSample3Num = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBAbsCover = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBAbsSampleNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBCover = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBSampleNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBMutCover = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBMutSampleNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBCoverSampleNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBRSRP6Diff = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBSINR6Diff = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBRSRPDiff = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBSINRDiff = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBCellRSRPDiff = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBCellSINRDiff = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBRSRPDT = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBSINRDT = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBHandNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBHandSuNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnNBHandFailNum = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.btnNextpage = new System.Windows.Forms.Button();
            this.btnPrevpage = new System.Windows.Forms.Button();
            this.label5 = new System.Windows.Forms.Label();
            this.txtPage = new System.Windows.Forms.TextBox();
            this.labPage = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.btnGo = new System.Windows.Forms.Button();
            this.labNum = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.btnSearch = new System.Windows.Forms.Button();
            this.txtCellName = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.ctxMenu.SuspendLayout();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroupType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal_LTE)).BeginInit();
            this.SuspendLayout();
            // 
            // ctxMenu
            // 
            this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel,
            this.toolStripMenuItem1,
            this.miExpandAll,
            this.miCallapsAll});
            this.ctxMenu.Name = "ctxMenu";
            this.ctxMenu.Size = new System.Drawing.Size(125, 76);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(124, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(121, 6);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(124, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCallapsAll
            // 
            this.miCallapsAll.Name = "miCallapsAll";
            this.miCallapsAll.Size = new System.Drawing.Size(124, 22);
            this.miCallapsAll.Text = "全部合并";
            this.miCallapsAll.Click += new System.EventHandler(this.miCallapsAll_Click);
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.groupControl1);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(1163, 73);
            this.panel1.TabIndex = 1;
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.labelControlType);
            this.groupControl1.Controls.Add(this.btnColorSetting);
            this.groupControl1.Controls.Add(this.radioGroupType);
            this.groupControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControl1.Location = new System.Drawing.Point(0, 0);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(1163, 73);
            this.groupControl1.TabIndex = 1;
            this.groupControl1.Text = "GIS显示设置";
            // 
            // labelControlType
            // 
            this.labelControlType.Location = new System.Drawing.Point(14, 37);
            this.labelControlType.Name = "labelControlType";
            this.labelControlType.Size = new System.Drawing.Size(72, 14);
            this.labelControlType.TabIndex = 2;
            this.labelControlType.Text = "覆盖度类别：";
            // 
            // btnColorSetting
            // 
            this.btnColorSetting.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnColorSetting.Location = new System.Drawing.Point(1070, 34);
            this.btnColorSetting.Name = "btnColorSetting";
            this.btnColorSetting.Size = new System.Drawing.Size(87, 27);
            this.btnColorSetting.TabIndex = 1;
            this.btnColorSetting.Text = "着色设置...";
            this.btnColorSetting.Click += new System.EventHandler(this.btnColorSetting_Click);
            // 
            // radioGroupType
            // 
            this.radioGroupType.EditValue = false;
            this.radioGroupType.Location = new System.Drawing.Point(92, 28);
            this.radioGroupType.Name = "radioGroupType";
            this.radioGroupType.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(false, "绝对覆盖度( > -80dBm)"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(true, "相对覆盖度(-12dB内)"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "综合覆盖度( -12dB内 并且 > -80dBm)")});
            this.radioGroupType.Size = new System.Drawing.Size(707, 33);
            this.radioGroupType.TabIndex = 1;
            this.radioGroupType.SelectedIndexChanged += new System.EventHandler(this.radioGroupType_SelectedIndexChanged);
            // 
            // listViewTotal_LTE
            // 
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnSN);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnCellName);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnTAC);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnECI);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnEARFCN);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnPCI);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnCovType);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnCellSampleCount);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnSampleCount);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnSample3Count);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnAbsLevel);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnAbsConditionSampleCount);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnRelLevel);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnRelConditionSampleCount);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnMulLevel);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnMulConditionSampleCount);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnRel4Rate);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnDistanceCov);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnIPCINum);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnM3);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnM6);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnRSRPAVG);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnSINRAVG);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnRSRPDIFF6);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnSINRDIFF6);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnRSRPDT);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnSINRDT);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnRSRPMR);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnSINRMR);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnConnectRare);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnDropRte);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnHandRate);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnERABRate);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnERABDrop);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnHandNum);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnHandSuNum);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnHandFaNum);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnOtherCellName);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnOtherCellCoverType);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnolvColumnOtherEARFCN);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnISSame);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnolvColumnOtherPCI);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnOtherM3);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnOtherM6);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnOtherCellSample);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnOtherRSRPDIFF);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnOtherSINRDIFF);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnOtherCellRscp);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnOtherSinr);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnOtherCellDistance);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnOtherRsrpCellDiff);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnOtherRsrpAvgDT);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnOtherSinrAvgDT);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnOtherRsrpAvgMainDT);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnOtherSinrAvgMainDT);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnOtherRsrpRateMR);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnOtherSinrAvgMR);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnOtherConnectRate);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnOtherDropRate);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnOtherHandRate);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnOtherERABRate);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnOtherERABDropRate);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnOtherMainCellHandNum);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnOtherMainCellHandSUNum);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnOtherMainCellHandFANum);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnNBMainSampleNum);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnNBMainSample3Num);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnNBAbsCover);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnNBAbsSampleNum);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnNBCover);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnNBSampleNum);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnNBMutCover);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnNBMutSampleNum);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnNBCoverSampleNum);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnNBRSRP6Diff);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnNBSINR6Diff);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnNBRSRPDiff);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnNBSINRDiff);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnNBCellRSRPDiff);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnNBCellSINRDiff);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnNBRSRPDT);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnNBSINRDT);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnNBHandNum);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnNBHandSuNum);
            this.listViewTotal_LTE.AllColumns.Add(this.olvColumnNBHandFailNum);
            this.listViewTotal_LTE.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.listViewTotal_LTE.BackColor = System.Drawing.SystemColors.Window;
            this.listViewTotal_LTE.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnCellName,
            this.olvColumnTAC,
            this.olvColumnECI,
            this.olvColumnEARFCN,
            this.olvColumnPCI,
            this.olvColumnCovType,
            this.olvColumnCellSampleCount,
            this.olvColumnSampleCount,
            this.olvColumnSample3Count,
            this.olvColumnAbsLevel,
            this.olvColumnAbsConditionSampleCount,
            this.olvColumnRelLevel,
            this.olvColumnRelConditionSampleCount,
            this.olvColumnMulLevel,
            this.olvColumnMulConditionSampleCount,
            this.olvColumnRel4Rate,
            this.olvColumnDistanceCov,
            this.olvColumnIPCINum,
            this.olvColumnM3,
            this.olvColumnM6,
            this.olvColumnRSRPAVG,
            this.olvColumnSINRAVG,
            this.olvColumnRSRPDIFF6,
            this.olvColumnSINRDIFF6,
            this.olvColumnRSRPDT,
            this.olvColumnSINRDT,
            this.olvColumnRSRPMR,
            this.olvColumnSINRMR,
            this.olvColumnConnectRare,
            this.olvColumnDropRte,
            this.olvColumnHandRate,
            this.olvColumnERABRate,
            this.olvColumnERABDrop,
            this.olvColumnHandNum,
            this.olvColumnHandSuNum,
            this.olvColumnHandFaNum,
            this.olvColumnOtherCellName,
            this.olvColumnOtherCellCoverType,
            this.olvColumnolvColumnOtherEARFCN,
            this.olvColumnISSame,
            this.olvColumnolvColumnOtherPCI,
            this.olvColumnOtherM3,
            this.olvColumnOtherM6,
            this.olvColumnOtherCellSample,
            this.olvColumnOtherRSRPDIFF,
            this.olvColumnOtherSINRDIFF,
            this.olvColumnOtherCellRscp,
            this.olvColumnOtherSinr,
            this.olvColumnOtherCellDistance,
            this.olvColumnOtherRsrpCellDiff,
            this.olvColumnOtherRsrpAvgDT,
            this.olvColumnOtherSinrAvgDT,
            this.olvColumnOtherRsrpAvgMainDT,
            this.olvColumnOtherSinrAvgMainDT,
            this.olvColumnOtherRsrpRateMR,
            this.olvColumnOtherSinrAvgMR,
            this.olvColumnOtherConnectRate,
            this.olvColumnOtherDropRate,
            this.olvColumnOtherHandRate,
            this.olvColumnOtherERABRate,
            this.olvColumnOtherERABDropRate,
            this.olvColumnOtherMainCellHandNum,
            this.olvColumnOtherMainCellHandSUNum,
            this.olvColumnOtherMainCellHandFANum,
            this.olvColumnNBMainSampleNum,
            this.olvColumnNBMainSample3Num,
            this.olvColumnNBAbsCover,
            this.olvColumnNBAbsSampleNum,
            this.olvColumnNBCover,
            this.olvColumnNBSampleNum,
            this.olvColumnNBMutCover,
            this.olvColumnNBMutSampleNum,
            this.olvColumnNBCoverSampleNum,
            this.olvColumnNBRSRP6Diff,
            this.olvColumnNBSINR6Diff,
            this.olvColumnNBRSRPDiff,
            this.olvColumnNBSINRDiff,
            this.olvColumnNBCellRSRPDiff,
            this.olvColumnNBCellSINRDiff,
            this.olvColumnNBRSRPDT,
            this.olvColumnNBSINRDT,
            this.olvColumnNBHandNum,
            this.olvColumnNBHandSuNum,
            this.olvColumnNBHandFailNum});
            this.listViewTotal_LTE.ContextMenuStrip = this.ctxMenu;
            this.listViewTotal_LTE.Cursor = System.Windows.Forms.Cursors.Default;
            this.listViewTotal_LTE.FullRowSelect = true;
            this.listViewTotal_LTE.GridLines = true;
            this.listViewTotal_LTE.HeaderWordWrap = true;
            this.listViewTotal_LTE.IsNeedShowOverlay = false;
            this.listViewTotal_LTE.Location = new System.Drawing.Point(0, 73);
            this.listViewTotal_LTE.Name = "listViewTotal_LTE";
            this.listViewTotal_LTE.OwnerDraw = true;
            this.listViewTotal_LTE.ShowGroups = false;
            this.listViewTotal_LTE.Size = new System.Drawing.Size(1163, 397);
            this.listViewTotal_LTE.TabIndex = 5;
            this.listViewTotal_LTE.UseCompatibleStateImageBehavior = false;
            this.listViewTotal_LTE.View = System.Windows.Forms.View.Details;
            this.listViewTotal_LTE.VirtualMode = true;
            this.listViewTotal_LTE.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.listViewTotal_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.AspectName = "";
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 50;
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名称";
            this.olvColumnCellName.Width = 80;
            // 
            // olvColumnTAC
            // 
            this.olvColumnTAC.HeaderFont = null;
            this.olvColumnTAC.Text = "小区TAC";
            this.olvColumnTAC.Width = 80;
            // 
            // olvColumnECI
            // 
            this.olvColumnECI.HeaderFont = null;
            this.olvColumnECI.Text = "小区ECI";
            this.olvColumnECI.Width = 80;
            // 
            // olvColumnEARFCN
            // 
            this.olvColumnEARFCN.HeaderFont = null;
            this.olvColumnEARFCN.Text = "小区EARFCN";
            // 
            // olvColumnPCI
            // 
            this.olvColumnPCI.HeaderFont = null;
            this.olvColumnPCI.Text = "小区PCI";
            // 
            // olvColumnCovType
            // 
            this.olvColumnCovType.HeaderFont = null;
            this.olvColumnCovType.Text = "覆盖类型";
            // 
            // olvColumnCellSampleCount
            // 
            this.olvColumnCellSampleCount.HeaderFont = null;
            this.olvColumnCellSampleCount.Text = "采样点总数";
            // 
            // olvColumnSampleCount
            // 
            this.olvColumnSampleCount.HeaderFont = null;
            this.olvColumnSampleCount.Text = "最强采样点数";
            // 
            // olvColumnSample3Count
            // 
            this.olvColumnSample3Count.HeaderFont = null;
            this.olvColumnSample3Count.Text = "前三强采样点数";
            // 
            // olvColumnAbsLevel
            // 
            this.olvColumnAbsLevel.HeaderFont = null;
            this.olvColumnAbsLevel.Text = "绝对重叠覆盖度";
            // 
            // olvColumnAbsConditionSampleCount
            // 
            this.olvColumnAbsConditionSampleCount.HeaderFont = null;
            this.olvColumnAbsConditionSampleCount.Text = "绝对条件样本点数";
            // 
            // olvColumnRelLevel
            // 
            this.olvColumnRelLevel.HeaderFont = null;
            this.olvColumnRelLevel.HeaderForeColor = System.Drawing.Color.Blue;
            this.olvColumnRelLevel.Text = "相对重叠覆盖度";
            // 
            // olvColumnRelConditionSampleCount
            // 
            this.olvColumnRelConditionSampleCount.HeaderFont = null;
            this.olvColumnRelConditionSampleCount.HeaderForeColor = System.Drawing.Color.Blue;
            this.olvColumnRelConditionSampleCount.Text = "相对条件样本点数";
            // 
            // olvColumnMulLevel
            // 
            this.olvColumnMulLevel.HeaderFont = null;
            this.olvColumnMulLevel.Text = "综合重叠覆盖度";
            // 
            // olvColumnMulConditionSampleCount
            // 
            this.olvColumnMulConditionSampleCount.HeaderFont = null;
            this.olvColumnMulConditionSampleCount.Text = "综合条件样本点数";
            // 
            // olvColumnRel4Rate
            // 
            this.olvColumnRel4Rate.HeaderFont = null;
            this.olvColumnRel4Rate.Text = "相对覆盖度大于等于4的采样点占比(%)";
            // 
            // olvColumnDistanceCov
            // 
            this.olvColumnDistanceCov.HeaderFont = null;
            this.olvColumnDistanceCov.Text = "覆盖距离(米)";
            this.olvColumnDistanceCov.Width = 80;
            // 
            // olvColumnIPCINum
            // 
            this.olvColumnIPCINum.HeaderFont = null;
            this.olvColumnIPCINum.Text = "PCI冲突次数";
            // 
            // olvColumnM3
            // 
            this.olvColumnM3.HeaderFont = null;
            this.olvColumnM3.Text = "模3干扰次数";
            // 
            // olvColumnM6
            // 
            this.olvColumnM6.HeaderFont = null;
            this.olvColumnM6.Text = "模6干扰次数";
            // 
            // olvColumnRSRPAVG
            // 
            this.olvColumnRSRPAVG.HeaderFont = null;
            this.olvColumnRSRPAVG.Text = "RSRP平均值";
            // 
            // olvColumnSINRAVG
            // 
            this.olvColumnSINRAVG.HeaderFont = null;
            this.olvColumnSINRAVG.Text = "SINR平均值";
            // 
            // olvColumnRSRPDIFF6
            // 
            this.olvColumnRSRPDIFF6.HeaderFont = null;
            this.olvColumnRSRPDIFF6.Text = "RSRP(diff6)";
            // 
            // olvColumnSINRDIFF6
            // 
            this.olvColumnSINRDIFF6.HeaderFont = null;
            this.olvColumnSINRDIFF6.Text = "SINR(diff6)";
            // 
            // olvColumnRSRPDT
            // 
            this.olvColumnRSRPDT.HeaderFont = null;
            this.olvColumnRSRPDT.Text = "平均信号强度（路测主服小区）";
            // 
            // olvColumnSINRDT
            // 
            this.olvColumnSINRDT.HeaderFont = null;
            this.olvColumnSINRDT.Text = "小区平均SINR（路测主服小区）";
            // 
            // olvColumnRSRPMR
            // 
            this.olvColumnRSRPMR.HeaderFont = null;
            this.olvColumnRSRPMR.Text = "小区-110覆盖率（统计数据）";
            // 
            // olvColumnSINRMR
            // 
            this.olvColumnSINRMR.HeaderFont = null;
            this.olvColumnSINRMR.Text = "小区平均SINR（统计数据）";
            // 
            // olvColumnConnectRare
            // 
            this.olvColumnConnectRare.HeaderFont = null;
            this.olvColumnConnectRare.Text = "无线接通率(统计)";
            // 
            // olvColumnDropRte
            // 
            this.olvColumnDropRte.HeaderFont = null;
            this.olvColumnDropRte.Text = "无线掉线率(统计)";
            // 
            // olvColumnHandRate
            // 
            this.olvColumnHandRate.HeaderFont = null;
            this.olvColumnHandRate.Text = "切换成功率(统计)";
            // 
            // olvColumnERABRate
            // 
            this.olvColumnERABRate.HeaderFont = null;
            this.olvColumnERABRate.Text = "ERAB建立成功率(统计)";
            // 
            // olvColumnERABDrop
            // 
            this.olvColumnERABDrop.HeaderFont = null;
            this.olvColumnERABDrop.Text = "ERAB掉线率(统计)";
            // 
            // olvColumnHandNum
            // 
            this.olvColumnHandNum.HeaderFont = null;
            this.olvColumnHandNum.Text = "切换申请次数（路测数据）";
            // 
            // olvColumnHandSuNum
            // 
            this.olvColumnHandSuNum.HeaderFont = null;
            this.olvColumnHandSuNum.Text = "切换成功次数（路测数据）";
            // 
            // olvColumnHandFaNum
            // 
            this.olvColumnHandFaNum.HeaderFont = null;
            this.olvColumnHandFaNum.Text = "切换失败次数（路测数据）";
            // 
            // olvColumnOtherCellName
            // 
            this.olvColumnOtherCellName.HeaderFont = null;
            this.olvColumnOtherCellName.HeaderForeColor = System.Drawing.Color.Blue;
            this.olvColumnOtherCellName.Text = "相对覆盖带内小区名称";
            this.olvColumnOtherCellName.Width = 100;
            // 
            // olvColumnOtherCellCoverType
            // 
            this.olvColumnOtherCellCoverType.HeaderFont = null;
            this.olvColumnOtherCellCoverType.Text = "覆盖类型";
            // 
            // olvColumnolvColumnOtherEARFCN
            // 
            this.olvColumnolvColumnOtherEARFCN.HeaderFont = null;
            this.olvColumnolvColumnOtherEARFCN.Text = "小区中心频点";
            // 
            // olvColumnISSame
            // 
            this.olvColumnISSame.HeaderFont = null;
            this.olvColumnISSame.Text = "是否同频干扰";
            // 
            // olvColumnolvColumnOtherPCI
            // 
            this.olvColumnolvColumnOtherPCI.HeaderFont = null;
            this.olvColumnolvColumnOtherPCI.Text = "PCI冲突次数";
            // 
            // olvColumnOtherM3
            // 
            this.olvColumnOtherM3.HeaderFont = null;
            this.olvColumnOtherM3.Text = "模3干扰次数";
            // 
            // olvColumnOtherM6
            // 
            this.olvColumnOtherM6.HeaderFont = null;
            this.olvColumnOtherM6.Text = "模6干扰次数";
            // 
            // olvColumnOtherCellSample
            // 
            this.olvColumnOtherCellSample.HeaderFont = null;
            this.olvColumnOtherCellSample.HeaderForeColor = System.Drawing.Color.Blue;
            this.olvColumnOtherCellSample.Text = "进入覆盖带采样点数";
            // 
            // olvColumnOtherRSRPDIFF
            // 
            this.olvColumnOtherRSRPDIFF.HeaderFont = null;
            this.olvColumnOtherRSRPDIFF.Text = "RSRP（diff6）";
            // 
            // olvColumnOtherSINRDIFF
            // 
            this.olvColumnOtherSINRDIFF.HeaderFont = null;
            this.olvColumnOtherSINRDIFF.Text = "SINR（diff6）";
            // 
            // olvColumnOtherCellRscp
            // 
            this.olvColumnOtherCellRscp.HeaderFont = null;
            this.olvColumnOtherCellRscp.HeaderForeColor = System.Drawing.Color.Blue;
            this.olvColumnOtherCellRscp.Text = "RSRP平均值";
            // 
            // olvColumnOtherSinr
            // 
            this.olvColumnOtherSinr.HeaderFont = null;
            this.olvColumnOtherSinr.Text = "SINR平均值";
            // 
            // olvColumnOtherCellDistance
            // 
            this.olvColumnOtherCellDistance.HeaderFont = null;
            this.olvColumnOtherCellDistance.HeaderForeColor = System.Drawing.Color.Blue;
            this.olvColumnOtherCellDistance.Text = "与主服小区距离(米)";
            this.olvColumnOtherCellDistance.Width = 80;
            // 
            // olvColumnOtherRsrpCellDiff
            // 
            this.olvColumnOtherRsrpCellDiff.HeaderFont = null;
            this.olvColumnOtherRsrpCellDiff.Text = "小区级RSRP差值";
            // 
            // olvColumnOtherRsrpAvgDT
            // 
            this.olvColumnOtherRsrpAvgDT.HeaderFont = null;
            this.olvColumnOtherRsrpAvgDT.Text = "平均信号强度（路测非主服小区）";
            // 
            // olvColumnOtherSinrAvgDT
            // 
            this.olvColumnOtherSinrAvgDT.HeaderFont = null;
            this.olvColumnOtherSinrAvgDT.Text = "小区平均SINR（路测非主服小区）";
            // 
            // olvColumnOtherRsrpAvgMainDT
            // 
            this.olvColumnOtherRsrpAvgMainDT.HeaderFont = null;
            this.olvColumnOtherRsrpAvgMainDT.Text = "平均信号强度（路测主服小区）";
            // 
            // olvColumnOtherSinrAvgMainDT
            // 
            this.olvColumnOtherSinrAvgMainDT.HeaderFont = null;
            this.olvColumnOtherSinrAvgMainDT.Text = "小区平均SINR（路测主服小区）";
            // 
            // olvColumnOtherRsrpRateMR
            // 
            this.olvColumnOtherRsrpRateMR.HeaderFont = null;
            this.olvColumnOtherRsrpRateMR.Text = "小区-110覆盖率（统计数据）";
            // 
            // olvColumnOtherSinrAvgMR
            // 
            this.olvColumnOtherSinrAvgMR.HeaderFont = null;
            this.olvColumnOtherSinrAvgMR.Text = "小区平均SINR（统计数据）";
            // 
            // olvColumnOtherConnectRate
            // 
            this.olvColumnOtherConnectRate.HeaderFont = null;
            this.olvColumnOtherConnectRate.Text = "无线接通率(统计)";
            // 
            // olvColumnOtherDropRate
            // 
            this.olvColumnOtherDropRate.HeaderFont = null;
            this.olvColumnOtherDropRate.Text = "无线掉线率(统计)";
            // 
            // olvColumnOtherHandRate
            // 
            this.olvColumnOtherHandRate.HeaderFont = null;
            this.olvColumnOtherHandRate.Text = "切换成功率(统计)";
            // 
            // olvColumnOtherERABRate
            // 
            this.olvColumnOtherERABRate.HeaderFont = null;
            this.olvColumnOtherERABRate.Text = "ERAB建立成功率(统计)";
            // 
            // olvColumnOtherERABDropRate
            // 
            this.olvColumnOtherERABDropRate.HeaderFont = null;
            this.olvColumnOtherERABDropRate.Text = "ERAB掉线率(统计)";
            // 
            // olvColumnOtherMainCellHandNum
            // 
            this.olvColumnOtherMainCellHandNum.HeaderFont = null;
            this.olvColumnOtherMainCellHandNum.Text = "最强小区切换申请次数（路测数据）";
            // 
            // olvColumnOtherMainCellHandSUNum
            // 
            this.olvColumnOtherMainCellHandSUNum.HeaderFont = null;
            this.olvColumnOtherMainCellHandSUNum.Text = "最强小区切换成功次数（路测数据）";
            // 
            // olvColumnOtherMainCellHandFANum
            // 
            this.olvColumnOtherMainCellHandFANum.HeaderFont = null;
            this.olvColumnOtherMainCellHandFANum.Text = "最强小区切换失败次数（路测数据）";
            // 
            // olvColumnNBMainSampleNum
            // 
            this.olvColumnNBMainSampleNum.HeaderFont = null;
            this.olvColumnNBMainSampleNum.Text = "最强采样点数";
            // 
            // olvColumnNBMainSample3Num
            // 
            this.olvColumnNBMainSample3Num.HeaderFont = null;
            this.olvColumnNBMainSample3Num.Text = "邻区前三强采样点数";
            // 
            // olvColumnNBAbsCover
            // 
            this.olvColumnNBAbsCover.HeaderFont = null;
            this.olvColumnNBAbsCover.Text = "绝对重叠覆盖度";
            // 
            // olvColumnNBAbsSampleNum
            // 
            this.olvColumnNBAbsSampleNum.HeaderFont = null;
            this.olvColumnNBAbsSampleNum.Text = "绝对条件样本点数";
            // 
            // olvColumnNBCover
            // 
            this.olvColumnNBCover.HeaderFont = null;
            this.olvColumnNBCover.Text = "相对重叠覆盖度";
            // 
            // olvColumnNBSampleNum
            // 
            this.olvColumnNBSampleNum.HeaderFont = null;
            this.olvColumnNBSampleNum.Text = "相对条件样本点数";
            // 
            // olvColumnNBMutCover
            // 
            this.olvColumnNBMutCover.HeaderFont = null;
            this.olvColumnNBMutCover.Text = "综合重叠覆盖度";
            // 
            // olvColumnNBMutSampleNum
            // 
            this.olvColumnNBMutSampleNum.HeaderFont = null;
            this.olvColumnNBMutSampleNum.Text = "综合条件样本点数";
            // 
            // olvColumnNBCoverSampleNum
            // 
            this.olvColumnNBCoverSampleNum.HeaderFont = null;
            this.olvColumnNBCoverSampleNum.Text = "进入覆盖带采样点数";
            // 
            // olvColumnNBRSRP6Diff
            // 
            this.olvColumnNBRSRP6Diff.HeaderFont = null;
            this.olvColumnNBRSRP6Diff.Text = "RSRP（diff6）";
            // 
            // olvColumnNBSINR6Diff
            // 
            this.olvColumnNBSINR6Diff.HeaderFont = null;
            this.olvColumnNBSINR6Diff.Text = "SINR（diff6）";
            // 
            // olvColumnNBRSRPDiff
            // 
            this.olvColumnNBRSRPDiff.HeaderFont = null;
            this.olvColumnNBRSRPDiff.Text = "RSRP平均值";
            // 
            // olvColumnNBSINRDiff
            // 
            this.olvColumnNBSINRDiff.HeaderFont = null;
            this.olvColumnNBSINRDiff.Text = "SINR平均值";
            // 
            // olvColumnNBCellRSRPDiff
            // 
            this.olvColumnNBCellRSRPDiff.HeaderFont = null;
            this.olvColumnNBCellRSRPDiff.Text = "小区级RSRP差值";
            // 
            // olvColumnNBCellSINRDiff
            // 
            this.olvColumnNBCellSINRDiff.HeaderFont = null;
            this.olvColumnNBCellSINRDiff.Text = "小区级SINR差值";
            // 
            // olvColumnNBRSRPDT
            // 
            this.olvColumnNBRSRPDT.HeaderFont = null;
            this.olvColumnNBRSRPDT.Text = "平均信号强度（路测非主服小区）";
            // 
            // olvColumnNBSINRDT
            // 
            this.olvColumnNBSINRDT.HeaderFont = null;
            this.olvColumnNBSINRDT.Text = "小区平均SINR（路测非主服小区）";
            // 
            // olvColumnNBHandNum
            // 
            this.olvColumnNBHandNum.HeaderFont = null;
            this.olvColumnNBHandNum.Text = "最强小区切换申请次数（路测数据）";
            // 
            // olvColumnNBHandSuNum
            // 
            this.olvColumnNBHandSuNum.HeaderFont = null;
            this.olvColumnNBHandSuNum.Text = "最强小区切换成功次数（路测数据）";
            // 
            // olvColumnNBHandFailNum
            // 
            this.olvColumnNBHandFailNum.HeaderFont = null;
            this.olvColumnNBHandFailNum.Text = "最强小区切换失败次数（路测数据）";
            // 
            // btnNextpage
            // 
            this.btnNextpage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnNextpage.Location = new System.Drawing.Point(846, 476);
            this.btnNextpage.Name = "btnNextpage";
            this.btnNextpage.Size = new System.Drawing.Size(33, 23);
            this.btnNextpage.TabIndex = 42;
            this.btnNextpage.Text = ">>";
            this.btnNextpage.UseVisualStyleBackColor = true;
            this.btnNextpage.Click += new System.EventHandler(this.btnNextpage_Click);
            // 
            // btnPrevpage
            // 
            this.btnPrevpage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnPrevpage.Location = new System.Drawing.Point(807, 476);
            this.btnPrevpage.Name = "btnPrevpage";
            this.btnPrevpage.Size = new System.Drawing.Size(33, 23);
            this.btnPrevpage.TabIndex = 41;
            this.btnPrevpage.Text = "<<";
            this.btnPrevpage.UseVisualStyleBackColor = true;
            this.btnPrevpage.Click += new System.EventHandler(this.btnPrevpage_Click);
            // 
            // label5
            // 
            this.label5.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(749, 478);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(19, 14);
            this.label5.TabIndex = 40;
            this.label5.Text = "页";
            // 
            // txtPage
            // 
            this.txtPage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txtPage.Location = new System.Drawing.Point(684, 475);
            this.txtPage.Name = "txtPage";
            this.txtPage.Size = new System.Drawing.Size(63, 22);
            this.txtPage.TabIndex = 39;
            // 
            // labPage
            // 
            this.labPage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.labPage.AutoSize = true;
            this.labPage.Location = new System.Drawing.Point(584, 480);
            this.labPage.Name = "labPage";
            this.labPage.Size = new System.Drawing.Size(14, 14);
            this.labPage.TabIndex = 38;
            this.labPage.Text = "0";
            // 
            // label4
            // 
            this.label4.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(541, 479);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(43, 14);
            this.label4.TabIndex = 37;
            this.label4.Text = "个，共";
            // 
            // btnGo
            // 
            this.btnGo.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnGo.Location = new System.Drawing.Point(768, 475);
            this.btnGo.Name = "btnGo";
            this.btnGo.Size = new System.Drawing.Size(33, 23);
            this.btnGo.TabIndex = 36;
            this.btnGo.Text = "GO";
            this.btnGo.UseVisualStyleBackColor = true;
            this.btnGo.Click += new System.EventHandler(this.btnGo_Click);
            // 
            // labNum
            // 
            this.labNum.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.labNum.AutoSize = true;
            this.labNum.Location = new System.Drawing.Point(496, 480);
            this.labNum.Name = "labNum";
            this.labNum.Size = new System.Drawing.Size(14, 14);
            this.labNum.TabIndex = 35;
            this.labNum.Text = "0";
            // 
            // label3
            // 
            this.label3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(618, 479);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(67, 14);
            this.label3.TabIndex = 34;
            this.label3.Text = "页，跳转至";
            // 
            // label2
            // 
            this.label2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(420, 479);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(79, 14);
            this.label2.TabIndex = 33;
            this.label2.Text = "总计小区共：";
            // 
            // btnSearch
            // 
            this.btnSearch.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSearch.Location = new System.Drawing.Point(1114, 475);
            this.btnSearch.Name = "btnSearch";
            this.btnSearch.Size = new System.Drawing.Size(42, 23);
            this.btnSearch.TabIndex = 32;
            this.btnSearch.Text = "查找";
            this.btnSearch.UseVisualStyleBackColor = true;
            this.btnSearch.Click += new System.EventHandler(this.btnSearch_Click);
            // 
            // txtCellName
            // 
            this.txtCellName.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txtCellName.Location = new System.Drawing.Point(952, 476);
            this.txtCellName.Name = "txtCellName";
            this.txtCellName.Size = new System.Drawing.Size(157, 22);
            this.txtCellName.TabIndex = 31;
            // 
            // label1
            // 
            this.label1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(891, 480);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(67, 14);
            this.label1.TabIndex = 30;
            this.label1.Text = "小区名称：";
            // 
            // LTEAntennaMultiCoverageForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1163, 502);
            this.Controls.Add(this.btnNextpage);
            this.Controls.Add(this.btnPrevpage);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.txtPage);
            this.Controls.Add(this.labPage);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.btnGo);
            this.Controls.Add(this.labNum);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.btnSearch);
            this.Controls.Add(this.txtCellName);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.listViewTotal_LTE);
            this.Controls.Add(this.panel1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "LTEAntennaMultiCoverageForm";
            this.Text = "LTE天线覆盖优化";
            this.ctxMenu.ResumeLayout(false);
            this.panel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroupType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.listViewTotal_LTE)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip ctxMenu;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCallapsAll;
        private System.Windows.Forms.Panel panel1;
        private BrightIdeasSoftware.TreeListView listViewTotal_LTE;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnTAC;
        private BrightIdeasSoftware.OLVColumn olvColumnSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnDistanceCov;
        private BrightIdeasSoftware.OLVColumn olvColumnRelLevel;
        private BrightIdeasSoftware.OLVColumn olvColumnAbsLevel;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.LabelControl labelControlType;
        private DevExpress.XtraEditors.SimpleButton btnColorSetting;
        private DevExpress.XtraEditors.RadioGroup radioGroupType;
        private BrightIdeasSoftware.OLVColumn olvColumnMulLevel;
        private BrightIdeasSoftware.OLVColumn olvColumnECI;
        private BrightIdeasSoftware.OLVColumn olvColumnEARFCN;
        private BrightIdeasSoftware.OLVColumn olvColumnPCI;
        private BrightIdeasSoftware.OLVColumn olvColumnAbsConditionSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnRelConditionSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnMulConditionSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherCellSample;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherCellRscp;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherCellDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnRel4Rate;
        private BrightIdeasSoftware.OLVColumn olvColumnCovType;
        private BrightIdeasSoftware.OLVColumn olvColumnCellSampleCount;
        private BrightIdeasSoftware.OLVColumn olvColumnIPCINum;
        private BrightIdeasSoftware.OLVColumn olvColumnM3;
        private BrightIdeasSoftware.OLVColumn olvColumnM6;
        private BrightIdeasSoftware.OLVColumn olvColumnRSRPAVG;
        private BrightIdeasSoftware.OLVColumn olvColumnSINRAVG;
        private BrightIdeasSoftware.OLVColumn olvColumnSINRDIFF6;
        private BrightIdeasSoftware.OLVColumn olvColumnRSRPDT;
        private BrightIdeasSoftware.OLVColumn olvColumnSINRDT;
        private BrightIdeasSoftware.OLVColumn olvColumnRSRPMR;
        private BrightIdeasSoftware.OLVColumn olvColumnSINRMR;
        private BrightIdeasSoftware.OLVColumn olvColumnConnectRare;
        private BrightIdeasSoftware.OLVColumn olvColumnDropRte;
        private BrightIdeasSoftware.OLVColumn olvColumnHandRate;
        private BrightIdeasSoftware.OLVColumn olvColumnERABRate;
        private BrightIdeasSoftware.OLVColumn olvColumnERABDrop;
        private BrightIdeasSoftware.OLVColumn olvColumnHandNum;
        private BrightIdeasSoftware.OLVColumn olvColumnHandSuNum;
        private BrightIdeasSoftware.OLVColumn olvColumnHandFaNum;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherCellCoverType;
        private BrightIdeasSoftware.OLVColumn olvColumnolvColumnOtherEARFCN;
        private BrightIdeasSoftware.OLVColumn olvColumnISSame;
        private BrightIdeasSoftware.OLVColumn olvColumnolvColumnOtherPCI;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherM3;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherM6;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherRSRPDIFF;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherSINRDIFF;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherSinr;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherRsrpCellDiff;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherRsrpAvgDT;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherSinrAvgDT;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherRsrpAvgMainDT;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherSinrAvgMainDT;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherRsrpRateMR;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherSinrAvgMR;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherConnectRate;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherDropRate;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherHandRate;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherERABRate;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherERABDropRate;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherMainCellHandNum;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherMainCellHandSUNum;
        private BrightIdeasSoftware.OLVColumn olvColumnOtherMainCellHandFANum;
        private BrightIdeasSoftware.OLVColumn olvColumnNBMainSampleNum;
        private BrightIdeasSoftware.OLVColumn olvColumnNBAbsCover;
        private BrightIdeasSoftware.OLVColumn olvColumnNBAbsSampleNum;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCover;
        private BrightIdeasSoftware.OLVColumn olvColumnNBSampleNum;
        private BrightIdeasSoftware.OLVColumn olvColumnNBMutCover;
        private BrightIdeasSoftware.OLVColumn olvColumnNBMutSampleNum;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCoverSampleNum;
        private BrightIdeasSoftware.OLVColumn olvColumnNBRSRP6Diff;
        private BrightIdeasSoftware.OLVColumn olvColumnNBSINR6Diff;
        private BrightIdeasSoftware.OLVColumn olvColumnNBRSRPDiff;
        private BrightIdeasSoftware.OLVColumn olvColumnNBSINRDiff;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCellRSRPDiff;
        private BrightIdeasSoftware.OLVColumn olvColumnNBRSRPDT;
        private BrightIdeasSoftware.OLVColumn olvColumnNBSINRDT;
        private BrightIdeasSoftware.OLVColumn olvColumnNBHandNum;
        private BrightIdeasSoftware.OLVColumn olvColumnNBHandSuNum;
        private BrightIdeasSoftware.OLVColumn olvColumnNBHandFailNum;
        private BrightIdeasSoftware.OLVColumn olvColumnRSRPDIFF6;
        private System.Windows.Forms.Button btnNextpage;
        private System.Windows.Forms.Button btnPrevpage;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox txtPage;
        private System.Windows.Forms.Label labPage;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Button btnGo;
        private System.Windows.Forms.Label labNum;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button btnSearch;
        private System.Windows.Forms.TextBox txtCellName;
        private System.Windows.Forms.Label label1;
        private BrightIdeasSoftware.OLVColumn olvColumnSample3Count;
        private BrightIdeasSoftware.OLVColumn olvColumnNBMainSample3Num;
        private BrightIdeasSoftware.OLVColumn olvColumnNBCellSINRDiff;

    }
}