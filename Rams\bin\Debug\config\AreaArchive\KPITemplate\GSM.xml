<?xml version="1.0"?>
<Configs>
  <Config name="Template">
    <Item name="Options" typeName="IDictionary">
      <Item typeName="String" key="Name">GSM</Item>
      <Item typeName="IList" key="Columns">
        <Item typeName="IDictionary">
          <Item typeName="String" key="Caption">RxLevSub Avg</Item>
          <Item typeName="String" key="Expression">{Mx_5A010202}</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="MoMtFlag">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">True</Item>
          <Item typeName="IList" key="ColorParam">
            <Item typeName="IDictionary">
              <Item typeName="Single" key="Min">-120</Item>
              <Item typeName="Single" key="Max">-90</Item>
              <Item typeName="Boolean" key="MinIncluded">True</Item>
              <Item typeName="Boolean" key="MaxIncluded">False</Item>
              <Item key="DescInfo" />
              <Item typeName="Int32" key="Value">-65536</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="Single" key="Min">-90</Item>
              <Item typeName="Single" key="Max">-80</Item>
              <Item typeName="Boolean" key="MinIncluded">True</Item>
              <Item typeName="Boolean" key="MaxIncluded">False</Item>
              <Item key="DescInfo" />
              <Item typeName="Int32" key="Value">-33024</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="Single" key="Min">-80</Item>
              <Item typeName="Single" key="Max">-70</Item>
              <Item typeName="Boolean" key="MinIncluded">True</Item>
              <Item typeName="Boolean" key="MaxIncluded">False</Item>
              <Item key="DescInfo" />
              <Item typeName="Int32" key="Value">-256</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="Single" key="Min">-70</Item>
              <Item typeName="Single" key="Max">-60</Item>
              <Item typeName="Boolean" key="MinIncluded">True</Item>
              <Item typeName="Boolean" key="MaxIncluded">False</Item>
              <Item key="DescInfo" />
              <Item typeName="Int32" key="Value">-8405248</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="Single" key="Min">-60</Item>
              <Item typeName="Single" key="Max">-10</Item>
              <Item typeName="Boolean" key="MinIncluded">True</Item>
              <Item typeName="Boolean" key="MaxIncluded">True</Item>
              <Item key="DescInfo" />
              <Item typeName="Int32" key="Value">-16744448</Item>
            </Item>
          </Item>
        </Item>
        <Item typeName="IDictionary">
          <Item typeName="String" key="Caption">RxQualSub Avg</Item>
          <Item typeName="String" key="Expression">{100*(Mx_5A010501+Mx_5A010502+Mx_5A010503+Mx_5A010504+Mx_5A010505)/Mx_5A01050C}%</Item>
          <Item typeName="Int32" key="CarrierID">1</Item>
          <Item typeName="Int32" key="MoMtFlag">0</Item>
          <Item typeName="Boolean" key="IsDynamicBKClr">True</Item>
          <Item typeName="IList" key="ColorParam">
            <Item typeName="IDictionary">
              <Item typeName="Single" key="Min">0</Item>
              <Item typeName="Single" key="Max">96</Item>
              <Item typeName="Boolean" key="MinIncluded">True</Item>
              <Item typeName="Boolean" key="MaxIncluded">False</Item>
              <Item typeName="String" key="DescInfo" />
              <Item typeName="Int32" key="Value">-65536</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="Single" key="Min">96</Item>
              <Item typeName="Single" key="Max">97</Item>
              <Item typeName="Boolean" key="MinIncluded">True</Item>
              <Item typeName="Boolean" key="MaxIncluded">False</Item>
              <Item typeName="String" key="DescInfo" />
              <Item typeName="Int32" key="Value">-33024</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="Single" key="Min">97</Item>
              <Item typeName="Single" key="Max">98</Item>
              <Item typeName="Boolean" key="MinIncluded">True</Item>
              <Item typeName="Boolean" key="MaxIncluded">False</Item>
              <Item typeName="String" key="DescInfo" />
              <Item typeName="Int32" key="Value">-256</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="Single" key="Min">98</Item>
              <Item typeName="Single" key="Max">99</Item>
              <Item typeName="Boolean" key="MinIncluded">True</Item>
              <Item typeName="Boolean" key="MaxIncluded">False</Item>
              <Item typeName="String" key="DescInfo" />
              <Item typeName="Int32" key="Value">-8405248</Item>
            </Item>
            <Item typeName="IDictionary">
              <Item typeName="Single" key="Min">99</Item>
              <Item typeName="Single" key="Max">100</Item>
              <Item typeName="Boolean" key="MinIncluded">True</Item>
              <Item typeName="Boolean" key="MaxIncluded">True</Item>
              <Item typeName="String" key="DescInfo" />
              <Item typeName="Int32" key="Value">-16744448</Item>
            </Item>
          </Item>
        </Item>
      </Item>
    </Item>
  </Config>
</Configs>