﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.NOP
{
    public static class KPIStater
    {
        public static DataTable StatLTEData(List<DTFileDataManager> files)
        {
            DataTable table = new DataTable();
            table.Columns.Add("文件", typeof(string));
            table.Columns.Add("采样点个数", typeof(int));
            table.Columns.Add("FTP平均下载速率(Mbps)", typeof(string));
            table.Columns.Add("FTP下载速率大于10Mbps占比(%)", typeof(string));
            table.Columns.Add("FTP下载速率大于3Mbps占比(%)", typeof(string));
            table.Columns.Add("FTP平均上传速率(Mbps)", typeof(string));
            table.Columns.Add("FTP上传速率小于512Kbps占比(%)", typeof(string));
            table.Columns.Add("HTTP平均下载速率(Mbps)", typeof(string));
            table.Columns.Add("PDCP平均下载速率(Mbps)", typeof(string));
            table.Columns.Add("PDCP层下载速率大于10Mbps占比(%)", typeof(string));
            table.Columns.Add("PDCP平均上传速率(Mbps)", typeof(string));
            table.Columns.Add("PDCP层上传速率小于512Kbps占比(%)", typeof(string));
            table.Columns.Add("平均RSRP(dBm)", typeof(string));
            table.Columns.Add("RSRP小于-105dBm占比", typeof(string));
            table.Columns.Add("平均SINR(dB)", typeof(string));
            table.Columns.Add("SINR小于-3dB占比", typeof(string));

            foreach (DTFileDataManager file in files)
            {
                DataRow row = table.NewRow();
                row["文件"] = file.FileName;
                row["采样点个数"] = file.TestPoints.Count;
                double ftpDLSum = 0;
                double ftpDLNum = 0;
                double ftpDLGreater10Num = 0;
                double ftpDLGreater3Num = 0;

                double ftpULSum = 0;
                double ftpULNum = 0;
                double ftpULLess512Num = 0;

                double httpDLSum = 0;
                double httpDLNum = 0;

                double pdcpDLSum = 0;
                double pdcpDLNum = 0;
                double pdcpGreater10Num = 0;

                double pdcpULSum = 0;
                double pdcpULNum = 0;
                double pdcpLess512Num = 0;

                double rsrpSum = 0;
                double rsrpNum = 0;
                double rsrpLowNum = 0;
                double sinrSum = 0;
                double sinrNum = 0;
                double sinrLowNum = 0;

                foreach (TestPoint tp in file.TestPoints)
                {
                    short? type = (short?)tp["lte_APP_type"];
                    object obj = tp["lte_APP_Speed_Mb"];
                    if (type != null && obj != null)
                    {
                        if (type == 2)
                        {//ftp dl
                            ftpDLNum++;
                            double speed = (double)obj;
                            ftpDLSum += speed;
                            if (speed > 10)
                            {
                                ftpDLGreater10Num++;
                                ftpDLGreater3Num++;
                            }
                            else if (speed > 3)
                            {
                                ftpDLGreater3Num++;
                            }
                        }
                        else if (type == 3)
                        {//ftp ul
                            ftpULNum++;
                            double speed = (double)obj;
                            ftpULSum += speed;
                            if (speed * 1000 < 512)
                            {
                                ftpULLess512Num++;
                            }
                        }
                        else if (type == 12)
                        {//http dl
                            httpDLNum++;
                            httpDLSum += (double)obj;
                        }
                    }

                    obj = tp["lte_PDCP_DL_Mb"];
                    if (obj != null)
                    {
                        pdcpDLNum++;
                        double speed = (double)obj;
                        pdcpDLSum += speed;
                        if (speed > 10)
                        {
                            pdcpGreater10Num++;
                        }
                    }

                    obj = tp["lte_PDCP_UL_Mb"];
                    if (obj != null)
                    {
                        pdcpULNum++;
                        double speed = (double)obj;
                        pdcpULSum += speed;
                        if (speed * 1000 < 512)
                        {
                            pdcpLess512Num++;
                        }
                    }

                    obj = tp["lte_RSRP"];
                    if (obj != null)
                    {
                        float rsrp = (float)obj;
                        rsrpNum++;
                        rsrpSum += rsrp;
                        if (rsrp < -105)
                        {
                            rsrpLowNum++;
                        }
                    }

                    obj = tp["lte_SINR"];
                    if (obj != null)
                    {
                        float sinr = (float)obj;
                        sinrNum++;
                        sinrSum += sinr;
                        if (sinr < -3)
                        {
                            sinrLowNum++;
                        }
                    }
                }
                row["FTP平均下载速率(Mbps)"] = ftpDLNum == 0 ? "" : Math.Round(ftpDLSum / ftpDLNum, 2).ToString();
                row["FTP下载速率大于10Mbps占比(%)"] = ftpDLNum == 0 ? "" : Math.Round(ftpDLGreater10Num * 100 / ftpDLNum, 2).ToString();
                row["FTP下载速率大于3Mbps占比(%)"] = ftpDLNum == 0 ? "" : Math.Round(ftpDLGreater3Num * 100 / ftpDLNum, 2).ToString();
                row["FTP平均上传速率(Mbps)"] = ftpULNum == 0 ? "" : Math.Round(ftpULSum / ftpULNum, 2).ToString();
                row["FTP上传速率小于512Kbps占比(%)"] = ftpULNum == 0 ? "" : Math.Round(ftpULLess512Num * 100 / ftpULNum, 2).ToString();
                row["HTTP平均下载速率(Mbps)"] = httpDLNum == 0 ? "" : Math.Round(httpDLSum * 100 / httpDLNum, 2).ToString();
                row["PDCP平均下载速率(Mbps)"] = pdcpDLNum == 0 ? "" : Math.Round(pdcpDLSum / pdcpDLNum, 2).ToString();
                row["PDCP层下载速率大于10Mbps占比(%)"] = pdcpDLNum == 0 ? "" : Math.Round(pdcpGreater10Num * 100 / pdcpDLNum, 2).ToString();
                row["PDCP平均上传速率(Mbps)"] = pdcpULNum == 0 ? "" : Math.Round(pdcpULSum / pdcpULNum, 2).ToString();
                row["PDCP层上传速率小于512Kbps占比(%)"] = pdcpULNum == 0 ? "" : Math.Round(pdcpLess512Num * 100 / pdcpULNum, 2).ToString();
                row["平均RSRP(dBm)"] = rsrpNum == 0 ? "" : Math.Round(rsrpSum / rsrpNum, 2).ToString().ToString();
                row["RSRP小于-105dBm占比"] = rsrpNum == 0 ? "" : Math.Round(rsrpLowNum / rsrpNum, 2).ToString();
                row["平均SINR(dB)"] = sinrNum == 0 ? "" : Math.Round(sinrSum / sinrNum, 2).ToString();
                row["SINR小于-3dB占比"] = sinrNum == 0 ? "" : Math.Round(sinrLowNum / sinrNum, 2).ToString();

                table.Rows.Add(row);
            }

            return table;
        }

        public static DataTable StatVoLTE(List<DTFileDataManager> files)
        {
            DataTable table = new DataTable();
            table.Columns.Add("文件", typeof(string));
            table.Columns.Add("采样点个数", typeof(int));
            table.Columns.Add("掉话事件", typeof(int));
            table.Columns.Add("未接通事件", typeof(int));
            table.Columns.Add("持续弱MOS事件", typeof(int));
            table.Columns.Add("弱MOS切换频繁事件", typeof(int));
            table.Columns.Add("呼叫时延4-5秒事件", typeof(int));
            table.Columns.Add("呼叫时延5-6秒事件", typeof(int));
            table.Columns.Add("呼叫时延大于6秒事件", typeof(int));
            table.Columns.Add("RTP丢包率事件", typeof(int));
            table.Columns.Add("ESRVCC切换事件", typeof(int));
            table.Columns.Add("IMS注册失败事件", typeof(int));
            table.Columns.Add("ESRVCC切换时延事件", typeof(int));
            table.Columns.Add("室分外泄事件", typeof(int));
            table.Columns.Add("MOS均值", typeof(string));
            table.Columns.Add("MOS大于3.0占比", typeof(string));
            table.Columns.Add("平均RSRP(dBm)", typeof(string));
            table.Columns.Add("RSRP小于-105dBm占比", typeof(string));
            table.Columns.Add("平均SINR(dB)", typeof(string));
            table.Columns.Add("SINR小于-3dB占比", typeof(string));

            foreach (DTFileDataManager file in files)
            {
                DataRow row = table.NewRow();
                row["文件"] = file.FileName;
                row["采样点个数"] = file.TestPoints.Count;
                double rsrpSum = 0;
                double rsrpNum = 0;
                double rsrpLowNum = 0;
                double sinrSum = 0;
                double sinrNum = 0;
                double sinrLowNum = 0;
                double mosSum = 0;
                double mosNum = 0;
                double mosL3Num = 0;

                int dropNum = 0, blockNum = 0, weakMosNum = 0, hoTooNum = 0, delay45 = 0, delay56 = 0
                    , delayG6 = 0, rtpLost = 0, esrvccFail = 0, imsFail = 0, esrvccDelay = 0, indoorLeak = 0;

                foreach (Event evt in file.Events)
                {
                    if (evt.ID == 1078 || evt.ID == 1079)
                    {
                        dropNum++;
                    }
                    else if (evt.ID == 1080)
                    {
                        blockNum++;
                    }
                    else if (evt.ID == 1401)
                    {
                        weakMosNum++;
                    }
                    else if (evt.ID == 1400)
                    {
                        hoTooNum++;
                    }
                    else if (evt.ID == 1432)
                    {
                        delay45++;
                    }
                    else if (evt.ID == 1453)
                    {
                        delay56++;
                    }
                    else if (evt.ID == 1467)
                    {
                        delayG6++;
                    }
                    else if (evt.ID == 1474)
                    {
                        rtpLost++;
                    }
                    else if (evt.ID == 1146)
                    {
                        esrvccFail++;
                    }
                    else if (evt.ID == 1167)
                    {
                        imsFail++;
                    }
                    else if (evt.ID == 1462)
                    {
                        esrvccDelay++;
                    }
                    else if (evt.ID == 10308)
                    {
                        indoorLeak++;
                    }
                }

                foreach (TestPoint tp in file.TestPoints)
                {
                    object obj = tp["lte_RSRP"];
                    if (obj != null)
                    {
                        float rsrp = (float)obj;
                        rsrpNum++;
                        rsrpSum += rsrp;
                        if (rsrp < -105)
                        {
                            rsrpLowNum++;
                        }
                    }

                    obj = tp["lte_SINR"];
                    if (obj != null)
                    {
                        float sinr = (float)obj;
                        sinrNum++;
                        sinrSum += sinr;
                        if (sinr < -3)
                        {
                            sinrLowNum++;
                        }
                    }

                    float? pesq = (float?)tp["lte_PESQMos"];
                    if (pesq != null && pesq > 0 && pesq <= 5)
                    {
                        mosNum++;
                        mosSum += (float)pesq;
                        if (pesq > 3)
                        {
                            mosL3Num++;
                        }
                    }
                    else
                    {
                        float? polqa = (float?)tp["lte_POLQA_Score_SWB"];
                        if (polqa != null && polqa > 0 && polqa <= 5)
                        {
                            mosNum++;
                            mosSum += (float)polqa;
                            if (polqa > 3)
                            {
                                mosL3Num++;
                            }
                        }
                    }
                }

                row["掉话事件"] = dropNum;
                row["未接通事件"] = blockNum;
                row["持续弱MOS事件"] =weakMosNum ;
                row["弱MOS切换频繁事件"] = hoTooNum;
                row["呼叫时延4-5秒事件"] = delay45;
                row["呼叫时延5-6秒事件"] = delay56;
                row["呼叫时延大于6秒事件"] = delayG6;
                row["RTP丢包率事件"] = rtpLost;
                row["ESRVCC切换事件"] = esrvccFail;
                row["IMS注册失败事件"] = imsFail;
                row["ESRVCC切换时延事件"] = esrvccDelay;
                row["室分外泄事件"] = indoorLeak;

                row["平均RSRP(dBm)"] = rsrpNum == 0 ? "" : Math.Round(rsrpSum / rsrpNum, 2).ToString().ToString();
                row["RSRP小于-105dBm占比"] = rsrpNum == 0 ? "" : Math.Round(rsrpLowNum / rsrpNum, 2).ToString();
                row["平均SINR(dB)"] = sinrNum == 0 ? "" : Math.Round(sinrSum / sinrNum, 2).ToString();
                row["SINR小于-3dB占比"] = sinrNum == 0 ? "" : Math.Round(sinrLowNum / sinrNum, 2).ToString();

                row["MOS均值"] = mosNum == 0 ? "" : Math.Round(mosSum / mosNum, 2).ToString();
                row["MOS大于3.0占比"] = mosNum == 0 ? "" : Math.Round(mosL3Num / mosNum, 2).ToString();
                table.Rows.Add(row);
            }

            return table;
        }

        public static DataTable StatCSFB(List<DTFileDataManager> files)
        {
            DataTable table = new DataTable();
            table.Columns.Add("文件", typeof(string));
            table.Columns.Add("采样点个数", typeof(int));
            table.Columns.Add("掉话事件", typeof(int));
            table.Columns.Add("未接通事件", typeof(int));
            table.Columns.Add("呼叫时延15到19秒事件", typeof(int));
            table.Columns.Add("呼叫时延大于19秒事件", typeof(int));
            table.Columns.Add("时延12到15秒事件", typeof(int));
            table.Columns.Add("时延大于15秒事件", typeof(int));
            table.Columns.Add("LTE弱覆盖事件", typeof(int));
            table.Columns.Add("LTE质差事件", typeof(int));
            table.Columns.Add("LTE过覆盖事件", typeof(int));
            table.Columns.Add("GSM弱覆盖事件", typeof(int));
            table.Columns.Add("GSM质差事件", typeof(int));
            table.Columns.Add("GSM过覆盖事件", typeof(int));

            foreach (DTFileDataManager file in files)
            {
                addRow(table, file);
            }
            return table;
        }

        private static void addRow(DataTable table, DTFileDataManager file)
        {
            DataRow row = table.NewRow();
            row["文件"] = file.FileName;
            row["采样点个数"] = file.TestPoints.Count;
            int dropNum = 0;
            int blockNum = 0;
            int lteWeakCvr = 0;
            int lteWeakSinr = 0;
            int lteOverCvr = 0;
            int gsmWeakCvr = 0;
            int gsmWeakQual = 0;
            int gsmOverCvr = 0;
            int delay1519Num = 0;
            int delayLarge19Num = 0;
            int delay1215Num = 0;
            int delayLarge15Num = 0;
            //int 
            foreach (Event evt in file.Events)
            {
                if (evt.ID == 1006 || evt.ID == 1007
                  || evt.ID == 1026 || evt.ID == 1027
                  || evt.ID == 1046 || evt.ID == 1047)
                {
                    dropNum++;
                }
                else if (evt.ID == 1008 || evt.ID == 1028 || evt.ID == 1048)
                {
                    blockNum++;
                }
                else if (evt.ID == 1188)
                {
                    lteWeakCvr++;
                }
                else if (evt.ID == 1189)
                {
                    lteWeakSinr++;
                }
                else if (evt.ID == 1190)
                {
                    gsmWeakCvr++;
                }
                else if (evt.ID == 1191)
                {
                    gsmWeakQual++;
                }
                else if (evt.ID == 1164)
                {
                    delay1519Num++;
                }
                else if (evt.ID == 1404)
                {
                    delayLarge19Num++;
                }
                else if (evt.ID == 1430)
                {
                    delay1215Num++;
                }
                else if (evt.ID == 1431)
                {
                    delayLarge15Num++;
                }
            }
            row["掉话事件"] = dropNum;
            row["未接通事件"] = blockNum;
            row["呼叫时延15到19秒事件"] = delay1519Num;
            row["呼叫时延大于19秒事件"] = delayLarge19Num;
            row["时延12到15秒事件"] = delay1215Num;
            row["时延大于15秒事件"] = delayLarge15Num;
            row["LTE弱覆盖事件"] = lteWeakCvr;
            row["LTE质差事件"] = lteWeakSinr;
            row["LTE过覆盖事件"] = lteOverCvr;
            row["GSM弱覆盖事件"] = gsmWeakCvr;
            row["GSM质差事件"] = gsmWeakQual;
            row["GSM过覆盖事件"] = gsmOverCvr;
            table.Rows.Add(row);
        }

        internal static DataTable StatLTEScan(List<DTFileDataManager> files)
        {
            DataTable table = new DataTable();
            table.Columns.Add("文件", typeof(string));
            table.Columns.Add("采样点个数", typeof(int));
            table.Columns.Add("高重叠覆盖路段事件", typeof(int));
            table.Columns.Add("弱覆盖路段事件", typeof(int));
            table.Columns.Add("模三干扰路段", typeof(int));
            table.Columns.Add("室分外泄小区", typeof(int));
            foreach (DTFileDataManager file in files)
            {
                DataRow row = table.NewRow();
                row["文件"] = file.FileName;
                row["采样点个数"] = file.TestPoints.Count;
                int evtNum = 0;
                int weakCvr = 0;
                int mod3Inter = 0;
                int indoorLeak = 0;
                foreach (Event evt in file.Events)
                {
                    if (evt.ID == 2027)
                    {
                        evtNum++;
                    }
                    else if (evt.ID == 2409)
                    {
                        weakCvr++;
                    }
                    else if (evt.ID == 2006)
                    {
                        mod3Inter++;
                    }
                    else if (evt.ID == 10102)
                    {
                        indoorLeak++;
                    }
                }
                row["高重叠覆盖路段事件"] = evtNum;
                row["弱覆盖路段事件"] = weakCvr;
                row["模三干扰路段"] = mod3Inter;
                row["室分外泄小区"] = indoorLeak;
                table.Rows.Add(row);
            }
            return table;
        }

    }

}
