﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using MasterCom.RAMS.Net;
using MasterCom.MControls;

namespace MasterCom.RAMS.ZTFunc
{
    public class RoadProblem
    {
        private int id;
        public int ID
        {
            get { return id; }
        }

        private string areaName;
        public string AreaName
        {
            get { return areaName; }
        }

        private int level;
        public int Level
        {
            get { return level; }
        }

        private string roadName;
        public string RoadName
        {
            get { return roadName; }
        }

        private double distance;
        public double Distance
        {
            get { return Math.Round(distance, 2); }
        }

        private int status;
        public int Status
        {
            get { return status; }
        }
        public string StatusString
        {
            get { return status == 0 ? "已创建" : "已关闭"; }
        }

        private int repeatLast;
        public int RepeatLast
        {
            get { return repeatLast; }
        }

        private int createdYear;
        public int CreatedYear
        {
            get { return createdYear; }
        }

        private int createdBatch;
        public int CreatedBatch
        {
            get { return createdBatch; }
        }

        public string CreatedMonth
        {
            get
            {
                return getMonthStringFromBatch(createdYear, createdBatch);
            }
        }

        private int beginYear;
        public int BeginYear
        {
            get { return beginYear; }
        }

        private int beginBatch;
        public int BeginBatch
        {
            get { return beginBatch; }
        }

        private int lastAbnormalYear;
        public int LastAbnormalYear
        {
            get { return lastAbnormalYear; }
        }

        private int lastAbnormalBatch;
        public int LastAbnormalBatch
        {
            get { return lastAbnormalBatch; }
        }

        public string LastAbnormalMonth
        {
            get
            {
                return getMonthStringFromBatch(lastAbnormalYear, lastAbnormalBatch);
            }
        }

        private int closedYear;
        public int ClosedYear
        {
            get { return closedYear; }
        }

        private int closedBatch;
        public int ClosedBatch
        {
            get { return closedBatch; }
        }

        public string ClosedMonth
        {
            get
            {
                if (status == 0)
                {
                    return "";
                }
                return getMonthStringFromBatch(closedYear, closedBatch);
            }
        }

        private int goodDaysCount;
        public int GoodDaysCount
        {
            get { return goodDaysCount; }
        }

        private int lastTestYear;
        public int LastTestYear
        {
            get { return lastTestYear; }
        }

        private int lastTestBatch;
        public int LastTestBatch
        {
            get { return lastTestBatch; }
        }

        public string LastTestMonth
        {
            get
            {
                return getMonthStringFromBatch(lastTestYear, lastTestBatch);
            }
        }

        private int validateStatus;
        public int ValidateStatus
        {
            get { return validateStatus; }
        }
        public string ValidateStatusString
        {
            get 
            {
                if (validateStatus == 0)
                {
                    return "未验证";
                }
                else if (validateStatus == 1)
                {
                    return "验证正常";
                }
                else
                {
                    return "验证异常";
                }
            }
        }

        private int lastValidateYear;
        public int LastValidateYear
        {
            get { return lastValidateYear; }
        }

        private int lastValidateBatch;
        public int LastValidateBatch
        {
            get { return lastValidateBatch; }
        }

        public string LastValidateMonth
        {
            get
            {
                return getMonthStringFromBatch(lastValidateYear, lastValidateBatch);
            }
        }

        private int gridRepeatCount;
        public int GridRepeatCount
        {
            get { return gridRepeatCount; }
        }

        public List<RoadKPIMonth> RoadKPIMonthList { get; set; } = new List<RoadKPIMonth>();

        public bool bQueryedKPI { get; set; } = false;

        public void Fill(Content content)
        {
            id = content.GetParamInt();
            areaName = content.GetParamString();
            level = content.GetParamInt();
            roadName = content.GetParamString();
            distance = content.GetParamFloat();
            status = content.GetParamInt();
            repeatLast = content.GetParamInt();
            createdYear = content.GetParamInt();
            createdBatch = content.GetParamInt();
            beginYear = content.GetParamInt();
            beginBatch = content.GetParamInt();
            lastAbnormalYear = content.GetParamInt();
            lastAbnormalBatch = content.GetParamInt();
            closedYear = content.GetParamInt();
            closedBatch = content.GetParamInt();
            lastTestYear = content.GetParamInt();
            lastTestBatch = content.GetParamInt();
            goodDaysCount = content.GetParamInt();
            validateStatus = content.GetParamInt();
            lastValidateYear = content.GetParamInt();
            lastValidateBatch = content.GetParamInt();
            gridRepeatCount = content.GetParamInt();
        }

        private string getMonthStringFromBatch(int year, int batch)
        {
            int month = batch / 2;
            if (batch % 2 == 1)
            {
                month++;
            }
            string sMonth = month.ToString();
            if (sMonth.Length == 1)
            {
                sMonth = "0" + sMonth;
            }
            return year + "年" + sMonth + "月";
        }
    }

    public class RoadKPIMonth
    {
        protected int year;
        public int Year
        {
            get { return year; }
        }

        protected int batch;
        public int Batch
        {
            get { return batch; }
        }

        public string Month
        {
            get
            {
                int batchTmp = batch;
                int month = batchTmp / 2;
                int monthBatch = 1;
                if (batchTmp % 2 == 1)
                {
                    month++;
                }
                else
                {
                    monthBatch = 2;
                }
                string sMonth = month.ToString();
                if (sMonth.Length == 1)
                {
                    sMonth = "0" + sMonth;
                }
                return year + "年" + sMonth + "月" + "第" + monthBatch + "轮";
            }
        }

        protected string areaName;
        public string AreaName
        {
            get { return areaName; }
        }

        protected int level;
        public int Level
        {
            get { return level; }
        }

        protected string roadName;
        public string RoadName
        {
            get { return roadName; }
        }

        protected int isPoor;
        public int IsPoor
        {
            get { return isPoor; }
        }
        public string IsPoorString
        {
            get { return isPoor == 1 ? "是" : "否"; }
        }
    }

    public class RoadKPIMonthGSM : RoadKPIMonth
    {
        
        private int rxQualTotal = 0;
        public int RxQualTotal
        {
            get { return rxQualTotal; }
        }
        private float rxQual0_4Pct;
        private float rxQual5Pct;
        private float rxQual6_7Pct;
        private float mos2d8Pct;
        private int mosTotal = 0;
        public int MosTotal
        {
            get { return mosTotal; }
        }

        public string RxQual0_4Pct
        {
            get
            {
                if (rxQualTotal == 0)
                {
                    return "-";
                }
                return Math.Round(rxQual0_4Pct, 2).ToString() + "%";
            }
        }

        public string RxQual5Pct
        {
            get
            {
                if (rxQualTotal == 0)
                {
                    return "-";
                }
                return Math.Round(rxQual5Pct, 2).ToString() + "%";
            }
        }

        public string RxQual6_7Pct
        {
            get
            {
                if (rxQualTotal == 0)
                {
                    return "-";
                }
                return Math.Round(rxQual6_7Pct, 2).ToString() + "%";
            }
        }

        public string Mos2d8Pct
        {
            get
            {
                if (mosTotal == 0)
                {
                    return "-";
                }
                return Math.Round(mos2d8Pct, 2).ToString() + "%";
            }
        }

        public void Fill(Content content)
        {
            year = content.GetParamInt();
            batch = content.GetParamInt();
            level = content.GetParamInt();
            isPoor = content.GetParamInt();
            rxQualTotal = content.GetParamInt();
            rxQual0_4Pct = content.GetParamFloat();
            rxQual5Pct = content.GetParamFloat();
            rxQual6_7Pct = content.GetParamFloat();
            mosTotal = content.GetParamInt();
            mos2d8Pct = content.GetParamFloat();
        }
    }

    public class RoadKPIMonthTD : RoadKPIMonth
    {
        private int rscpTotal;
        public int RSCPTotal
        {
            get { return rscpTotal; }
        }

        private float rscp85Pct;

        public string RSCP85Pct
        {
            get
            {
                if (rscpTotal == 0)
                {
                    return "-";
                }
                return Math.Round(rscp85Pct, 2).ToString() + "%";
            }
        }

        public void Fill(Content content)
        {
            year = content.GetParamInt();
            batch = content.GetParamInt();
            areaName = content.GetParamString();
            level = content.GetParamInt();
            roadName = content.GetParamString();
            isPoor = content.GetParamInt();
            rscpTotal = content.GetParamInt();
            rscp85Pct = content.GetParamFloat();
        }
    }

    public class MapFormRoadProblemRanges
    {

        public MapFormRoadProblemRanges()
        {
            initialize();
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                List<object> colorParams = new List<object>();
                param["RoadProblemColorRanges"] = colorParams;
                foreach (ColorRange cr in RoadProblemColorRanges)
                {
                    colorParams.Add(cr.Param);
                }
                return param;
            }
            set
            {
                RoadProblemColorRanges.Clear();
                List<object> colorParams = (List<object>)value["RoadProblemColorRanges"];
                foreach (object o in colorParams)
                {
                    Dictionary<string, object> colorParam = (Dictionary<string, object>)o;
                    ColorRange cr = new ColorRange();
                    cr.Param = colorParam;
                    RoadProblemColorRanges.Add(cr);
                }
            }
        }

        private void initialize()
        {
            RoadProblemColorRanges.Clear();
            RoadProblemColorRanges.Add(new ColorRange(0, 2, Color.Cyan));
            RoadProblemColorRanges.Add(new ColorRange(2, 4, Color.Lime));
            RoadProblemColorRanges.Add(new ColorRange(4, 6, Color.Yellow));
            RoadProblemColorRanges.Add(new ColorRange(6, 10, Color.Orange));
            RoadProblemColorRanges.Add(new ColorRange(10, 18, Color.Red));
        }
        public List<ColorRange> RoadProblemColorRanges { get; set; } = new List<ColorRange>();
    }
}
