﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.RAMS.Stat.Data;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model.Interface;
using DBDataViewer;
using System.Windows.Forms;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.KPI_Statistics;

namespace MasterCom.RAMS.Net
{
    public class ZTNBCellMissByRegion_T2G : ZTNBCellMissByRegion
    {
        public ZTNBCellMissByRegion_T2G(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "T2G邻区配置核查"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 16000, 16014, this.Name);
        }
        protected override bool getConditionBeforeQuery()
        {
            cellGridDic.Clear();
            MainModel.CellManager.GetTDNBCellInfo();
            return true;
        }

        protected override void fireShowResult()
        {
            ZTNBCellMissForm frm = MainModel.CreateResultForm(typeof(ZTNBCellMissForm)) as ZTNBCellMissForm;
            frm.FillDatas(cellGridDic, InspectType.T2G);
            frm.Visible = true;
            frm.BringToFront();
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            List<string> formulaSet = new List<string>();
            formulaSet.Add(DataScan_GSM.RxlevMeanValueID);
            formulaSet.Add(DataScan_GSM.RxlevMaxID);
            formulaSet.Add(DataScan_GSM.RxlevSampleCountID);
            formulaSet.Add(DataScan_TD.PCCPCHRSCPMeanValueID);
            formulaSet.Add(DataScan_TD.PCCPCHRSCPMaxID);
            formulaSet.Add(DataScan_TD.PCCPCHRSCPSampleNum);
            return getTriadIDIgnoreServiceType(formulaSet);
        }

        protected override void getGridCells()
        {
            foreach (GridDataUnit grid in CurScanGridUnitMatrix)
            {
                StatDataSCAN_TD dataScam_TD = grid.GetStatData(typeof(StatDataSCAN_TD)) as StatDataSCAN_TD;
                dealDataSCAN(grid, dataScam_TD, "TD", DataScan_TD.PCCPCHRSCPMeanValueID, DataScan_TD.PCCPCHRSCPMaxID, DataScan_TD.PCCPCHRSCPSampleNum);

                StatDataSCAN_GSM dataScam_GSM = grid.GetStatData(typeof(StatDataSCAN_GSM)) as StatDataSCAN_GSM;
                dealDataSCAN(grid, dataScam_GSM, "GSM", DataScan_GSM.RxlevMeanValueID, DataScan_GSM.RxlevMaxID, DataScan_GSM.RxlevSampleCountID);
            }
        }
    }
}
