﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.Net
{
    public class DIYQueryGridProblem : QueryBase
    {
        /// <summary>
        /// 栅格数据类型，0：GSM，1：TD
        /// </summary>
        int dataType = 0;
        /// <summary>
        /// 问题状态，0：全部，1：已创建，2：已关闭
        /// </summary>
        int status;
        public bool ShowConditionSetting { get; set; } 
        private DIYQueryGridProblem(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }
        private static DIYQueryGridProblem queryGridProblem = null;

        public static DIYQueryGridProblem GetInstance()
        {
            if (queryGridProblem == null)
            {
                queryGridProblem = new DIYQueryGridProblem(MainModel.GetInstance());
            }
            return queryGridProblem;
        }

        public override string Name
        {
            get { return "栅格问题点"; }
        }

        public override string IconName
        {
            get { return ""; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18002, this.Name);
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        GridProblemDlg conditionDlg = null;
        protected override void query()
        {
            if (ShowConditionSetting)
            {
                if (conditionDlg == null)
                {
                    conditionDlg = new GridProblemDlg();
                }
                if (conditionDlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    conditionDlg.GetCondition(out dataType, out status);
                }
                else
                {
                    return;
                }
            }
            WaitBox.Show("开始查询栅格问题点...", queryInThread);
            fireShowForm();
        }

        private void fireShowForm()
        {
            MainModel.MainForm.GetMapForm().FireAddGridProblemLayer();

            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(GridProblemForm).FullName);
            GridProblemForm gridProblemForm = obj == null ? null : obj as GridProblemForm;
            if (gridProblemForm == null || gridProblemForm.IsDisposed)
            {
                gridProblemForm = new GridProblemForm(MainModel);
            }
            gridProblemForm.FillData(dataType, status);
            if (!gridProblemForm.Visible)
            {
                gridProblemForm.Show(MainModel.MainForm);
            }
        }

        private void queryInThread()
        {
            try
            {
                MainModel.AllGridProblemsDic.Clear();
                string tableName = "tb_sz_netgrid_gsm_problem";
                if (dataType == 0)
                {
                    tableName = "tb_sz_netgrid_gsm_problem";
                }
                else if (dataType == 1)
                {
                    tableName = "tb_sz_netgrid_td_problem";
                }
                string sqlGridProblem = "select A.ID, A.netGridID, B.midLong, B.midLat, B.areaAgent, B.areaOpt, B.areaATUGridID," +
                    " A.status, A.weight, A.repeatSixBatch, A.createdYear, A.createdBatch, A.beginYear, A.beginBatch," +
                    " A.lastAbnormalYear, A.lastAbnormalBatch, A.closedYear, A.closedBatch, A.goodDaysCount," +
                    " A.lastTestYear, A.lastTestBatch, A.validateStatus, A.gridRepeatCount" +
                    " from " + tableName + " A " +
                    " left join tb_sz_netgrid_info B on B.netGridID = A.netGridID " +
                    " order by A.ID";
                WaitBox.Text = "开始获取栅格问题点...";
                DIYSQLGridProblem gridQuery = new DIYSQLGridProblem(MainModel, sqlGridProblem);
                gridQuery.Query();
                if (MainModel.AllGridProblemsDic.Count == 0)
                {
                    return;
                }
            }
            catch
            {
                //continue
            }
            finally
            {
                WaitBox.Close();
            }
        }

        private class DIYSQLGridProblem : DIYSQLBase
        {
            readonly string sql;
            public DIYSQLGridProblem(MainModel mainModel, string sql)
                : base(mainModel)
            {
                this.sql = sql;
            }
            protected override string getSqlTextString()
            {
                return sql;
            }

            protected override E_VType[] getSqlRetTypeArr()
            {
                E_VType[] rType = new E_VType[23];
                rType[0] = E_VType.E_Int;
                rType[1] = E_VType.E_Int;
                rType[2] = E_VType.E_Int;
                rType[3] = E_VType.E_Int;
                rType[4] = E_VType.E_String;
                rType[5] = E_VType.E_String;
                rType[6] = E_VType.E_String;
                rType[7] = E_VType.E_Int;
                rType[8] = E_VType.E_Int;
                rType[9] = E_VType.E_Int;
                rType[10] = E_VType.E_Int;
                rType[11] = E_VType.E_Int;
                rType[12] = E_VType.E_Int;
                rType[13] = E_VType.E_Int;
                rType[14] = E_VType.E_Int;
                rType[15] = E_VType.E_Int;
                rType[16] = E_VType.E_Int;
                rType[17] = E_VType.E_Int;
                rType[18] = E_VType.E_Int;
                rType[19] = E_VType.E_Int;
                rType[20] = E_VType.E_Int;
                rType[21] = E_VType.E_Int;
                rType[22] = E_VType.E_Int;
                return rType;
            }

            protected override void receiveRetData(ClientProxy clientProxy)
            {
                Package package = clientProxy.Package;
                int index = 0;
                int progress = 0;
                while (true)
                {
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                    clientProxy.Recieve();
                    package.Content.PrepareGetParam();
                    if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                    {
                        GridProblem grid = new GridProblem();
                        grid.Fill(package.Content);
                        MainModel.AllGridProblemsDic[grid.ID] = grid;
                        //do your code here
                    }
                    else if (package.Content.Type == ResponseType.END)
                    {
                        break;
                    }
                    else
                    {
                        log.Error("Unexpected type: " + package.Content.Type);
                        break;
                    }
                    if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                    {
                        progress++;
                        if (progress > 95)
                        {
                            progress = 5;
                            index = 0;
                        }
                        if (progress % 5 == 0)
                        {
                            WaitBox.ProgressPercent = progress;
                        }
                    }
                }
            }

            public override string Name
            {
                get { return "DIYSQLGridProblem"; }
            }
        }
    }
}
