﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Stat;
using MasterCom.Util;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.KPI_Statistics
{
    public class QueryOrderCheckFileKPI : QueryKpiStatByFiles
    {
        private List<CategoryEnumItem> areaTypes;
        protected override bool getConditionBeforeQuery()
        {
            bool valid = base.getConditionBeforeQuery();
            if (!valid)
            {
                return false;
            }
            OrderAreaTypeFilterDlg dlg = new OrderAreaTypeFilterDlg();
            dlg.AreaTypes = areaTypes;
            if (dlg.ShowDialog()!=DialogResult.OK)
            {
                return false;
            }
            areaTypes = dlg.AreaTypes;

            WaitTextBox.Show("正在获取质检文件信息...", getCheckFiles);
            if (cityFileDic == null || cityFileDic.Count == 0)
            {
                MessageBox.Show("无质检文件！");
                return false;
            }
            return valid;
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.None;
        }

        Dictionary<int, List<OrderCheckFileItem>> cityFileDic = null;
        private void getCheckFiles()
        {
            QueryOrderCheckFileInfo qry = new QueryOrderCheckFileInfo();
            qry.Query();
            List<OrderCheckFileItem> files = qry.OrderCheckFiles;
            cityFileDic = new Dictionary<int, List<OrderCheckFileItem>>();
            Dictionary<string, bool> areaTypeDic = new Dictionary<string, bool>();
            foreach (CategoryEnumItem item in areaTypes)
            {
                areaTypeDic[item.Name] = true;
            }
            foreach (OrderCheckFileItem file in files)
            {
                if (areaTypeDic.ContainsKey(file.AreaTypeName))
                {
                    List<OrderCheckFileItem> fis = null;
                    if (!cityFileDic.TryGetValue(file.cityID, out fis))
                    {
                        fis = new List<OrderCheckFileItem>();
                        cityFileDic[file.cityID] = fis;
                    }
                    fis.Add(file);
                }
            }
            WaitTextBox.Close();
        }

        Dictionary<FileInfo, List<OrderCheckFileItem>> curFileIDDic = null;
        protected override void queryDistrictData(int districtID)
        {
            List<OrderCheckFileItem> checkFiles = null;
            if (!cityFileDic.TryGetValue(districtID, out checkFiles))
            {
                return;
            }
            QueryCondition cond = new QueryCondition();
            cond.DistrictIDs.Add(districtID);
            cond.ServiceTypes = this.condition.ServiceTypes;
            cond.Projects = this.condition.Projects;
            cond.Periods = this.condition.Periods;
            cond.CarrierTypes = this.condition.CarrierTypes;
            cond.DistrictID = districtID;
            DIYQueryFileInfo queryAllFiles = new DIYQueryFileInfo(MainModel);
            queryAllFiles.IsShowFileInfoForm = false;
            queryAllFiles.SetQueryCondition(cond);
            queryAllFiles.Query();
            List<FileInfo> files = MainModel.FileInfos;

            curFileIDDic = new Dictionary<FileInfo, List<OrderCheckFileItem>>();
            foreach (OrderCheckFileItem chkFi in checkFiles)
            {
                foreach (FileInfo fi in files)
                {
                    if (fi.Name.Contains(chkFi.FileName))
                    {
                        List<OrderCheckFileItem> temp;
                        if (!curFileIDDic.TryGetValue(fi, out temp))
                        {
                            temp = new List<OrderCheckFileItem>();
                            curFileIDDic[fi] = temp;
                        }
                        temp.Add(chkFi);
                        break;
                    }
                }
            }

            if (curFileIDDic.Count == 0)
            {
                return;
            }

            condition.FileInfos = new List<FileInfo>(curFileIDDic.Keys);
            ClientProxy clientProxy = new ClientProxy();
            if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port
                , MainModel.User.LoginName, MainModel.User.Password, districtID) != ConnectResult.Success)
            {
                ErrorInfo = "连接服务器失败!";
                return;
            }
            try
            {
                WaitBox.Show("开始统计数据...", queryInThread, clientProxy);
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override void recieveAndHandleSpecificStatData(Net.Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            fillStatData(package, curImgColumnDef, singleStatData);
            List<OrderCheckFileItem> chkFiles = null;
            foreach (FileInfo fi in curFileIDDic.Keys)
            {
                if (fi.ID == curFile.ID)
                {
                    chkFiles = curFileIDDic[fi];
                    break;
                }
            }
            if (chkFiles == null)
            {
                return;
            }
            foreach (OrderCheckFileItem chkItem in chkFiles)
            {
                string groupName = string.Format("{0}${1}${2}${3}${4}", chkItem.OrderFlag, chkItem.Round
                   , chkItem.cityID, chkItem.AreaTypeName, chkItem.AreaName);
                KpiDataManager.AddStatData(string.Empty, groupName, curFile, singleStatData, false);
            }
        }

        protected override void handleStatEvent(Model.Event evt)
        {
            StatDataEvent eventData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
            List<OrderCheckFileItem> chkFiles = null;
            foreach (FileInfo fi in curFileIDDic.Keys)
            {
                if (fi.ID == curFile.ID)
                {
                    chkFiles = curFileIDDic[fi];
                    break;
                }
            }
            if (chkFiles == null)
            {
                return;
            }
            foreach (OrderCheckFileItem chkItem in chkFiles)
            {
                string groupName = string.Format("{0}${1}${2}${3}${4}", chkItem.OrderFlag, chkItem.Round
                   , chkItem.cityID, chkItem.AreaTypeName, chkItem.AreaName);
                KpiDataManager.AddStatData(string.Empty, groupName, curFile, eventData, false);
            }
        }


    }
}
