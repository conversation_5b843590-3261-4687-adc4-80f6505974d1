﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NRHandoverTooMuchDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.chkAnaLTE = new System.Windows.Forms.CheckBox();
            this.numDistanceLimitMin = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.numHandoverCount = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.numDistanceLimit = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.numTimeLimit = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.numDistanceLimitMin.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHandoverCount.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistanceLimit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTimeLimit.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // chkAnaLTE
            // 
            this.chkAnaLTE.AutoSize = true;
            this.chkAnaLTE.Location = new System.Drawing.Point(86, 98);
            this.chkAnaLTE.Name = "chkAnaLTE";
            this.chkAnaLTE.Size = new System.Drawing.Size(90, 16);
            this.chkAnaLTE.TabIndex = 25;
            this.chkAnaLTE.Text = "分析LTE切换";
            this.chkAnaLTE.UseVisualStyleBackColor = true;
            // 
            // numDistanceLimitMin
            // 
            this.numDistanceLimitMin.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.numDistanceLimitMin.Location = new System.Drawing.Point(86, 62);
            this.numDistanceLimitMin.Name = "numDistanceLimitMin";
            this.numDistanceLimitMin.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numDistanceLimitMin.Properties.Appearance.Options.UseFont = true;
            this.numDistanceLimitMin.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDistanceLimitMin.Properties.IsFloatValue = false;
            this.numDistanceLimitMin.Properties.Mask.EditMask = "N00";
            this.numDistanceLimitMin.Size = new System.Drawing.Size(72, 20);
            this.numDistanceLimitMin.TabIndex = 24;
            this.numDistanceLimitMin.Visible = false;
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(348, 28);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(12, 12);
            this.labelControl5.TabIndex = 23;
            this.labelControl5.Text = "次";
            // 
            // numHandoverCount
            // 
            this.numHandoverCount.EditValue = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.numHandoverCount.Location = new System.Drawing.Point(259, 24);
            this.numHandoverCount.Name = "numHandoverCount";
            this.numHandoverCount.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numHandoverCount.Properties.Appearance.Options.UseFont = true;
            this.numHandoverCount.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numHandoverCount.Properties.IsFloatValue = false;
            this.numHandoverCount.Properties.Mask.EditMask = "N00";
            this.numHandoverCount.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numHandoverCount.Properties.MinValue = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.numHandoverCount.Size = new System.Drawing.Size(82, 20);
            this.numHandoverCount.TabIndex = 22;
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(349, 65);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(12, 12);
            this.labelControl3.TabIndex = 21;
            this.labelControl3.Text = "米";
            // 
            // numDistanceLimit
            // 
            this.numDistanceLimit.EditValue = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.numDistanceLimit.Location = new System.Drawing.Point(259, 62);
            this.numDistanceLimit.Name = "numDistanceLimit";
            this.numDistanceLimit.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numDistanceLimit.Properties.Appearance.Options.UseFont = true;
            this.numDistanceLimit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numDistanceLimit.Properties.IsFloatValue = false;
            this.numDistanceLimit.Properties.Mask.EditMask = "N00";
            this.numDistanceLimit.Size = new System.Drawing.Size(82, 20);
            this.numDistanceLimit.TabIndex = 20;
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(182, 65);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(60, 12);
            this.labelControl4.TabIndex = 19;
            this.labelControl4.Text = "持续距离≤";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(163, 28);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(78, 12);
            this.labelControl2.TabIndex = 18;
            this.labelControl2.Text = "秒 切换次数≥";
            // 
            // btnCancel
            // 
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(270, 132);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(87, 27);
            this.btnCancel.TabIndex = 17;
            this.btnCancel.Text = "取消";
            // 
            // btnOK
            // 
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.Location = new System.Drawing.Point(163, 132);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(87, 27);
            this.btnOK.TabIndex = 16;
            this.btnOK.Text = "确定";
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // numTimeLimit
            // 
            this.numTimeLimit.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numTimeLimit.Location = new System.Drawing.Point(86, 24);
            this.numTimeLimit.Name = "numTimeLimit";
            this.numTimeLimit.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numTimeLimit.Properties.Appearance.Options.UseFont = true;
            this.numTimeLimit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.numTimeLimit.Properties.IsFloatValue = false;
            this.numTimeLimit.Properties.Mask.EditMask = "N00";
            this.numTimeLimit.Size = new System.Drawing.Size(72, 20);
            this.numTimeLimit.TabIndex = 15;
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(37, 28);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(36, 12);
            this.labelControl1.TabIndex = 14;
            this.labelControl1.Text = "时长≤";
            // 
            // NRHandoverTooMuchDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(401, 179);
            this.Controls.Add(this.chkAnaLTE);
            this.Controls.Add(this.numDistanceLimitMin);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.numHandoverCount);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.numDistanceLimit);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.numTimeLimit);
            this.Controls.Add(this.labelControl1);
            this.Name = "NRHandoverTooMuchDlg";
            this.Text = "切换过频繁";
            ((System.ComponentModel.ISupportInitialize)(this.numDistanceLimitMin.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHandoverCount.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numDistanceLimit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numTimeLimit.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.CheckBox chkAnaLTE;
        private DevExpress.XtraEditors.SpinEdit numDistanceLimitMin;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.SpinEdit numHandoverCount;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.SpinEdit numDistanceLimit;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SpinEdit numTimeLimit;
        private DevExpress.XtraEditors.LabelControl labelControl1;
    }
}