﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using System.Drawing;
using System.Drawing.Drawing2D;
using MasterCom.Util;
using System.Windows.Forms;
using System.Runtime.Serialization;
using System.Collections;
using MasterCom.MControls;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model.Interface;
using MasterCom.MTGis;
using System.Drawing.Imaging;

namespace MasterCom.RAMS.Func
{
    class CellWeightRegionLayer : CustomDrawLayer
    {
        static CellWeightRegionLayer()
        {

        }

        public CellWeightRegionLayer(MapOperation mp, string name)
            : base(mp, name)
        {

        }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            if (MainModel.cellWeightRegionInfoList.Count == 0) return;

            // 以地图中心判断重绘
            DbPoint mapCenter = Map.GetCenter();
            if (lastCenter == null || lastCenter.x != mapCenter.x || lastCenter.y != mapCenter.y || lastBitmap == null)
            {
                lastCenter = mapCenter;
            }
            else
            {
                graphics.DrawImage(lastBitmap, clientRect.X, clientRect.Y);
                return;
            }

            // 注意Bitmap像素格式为默认
            Bitmap bitMap = new Bitmap(clientRect.Width, clientRect.Height, graphics);
            Bitmap tmpMap = new Bitmap(clientRect.Width, clientRect.Height, graphics);
            Rectangle mapRect = new Rectangle();
            byte[] byteMap = new byte[clientRect.Height * clientRect.Width * 4];

            // 在tmpMap上绘制每个小区的椭圆
            DbRect dRect;
            Rectangle curRect = new Rectangle(clientRect.X, clientRect.Y, clientRect.Width, clientRect.Height);
            Map.FromDisplay(curRect, out dRect);

            unsafe
            {
                fixed (byte* bytePtr = byteMap)
                {
                    for (int i = 0; i < clientRect.Height * clientRect.Width; ++i)
                        bytePtr[i * 4] = 0;
                }
            }
            foreach (CellWeightRegionInfo cinfo in MainModel.cellWeightRegionInfoList)
            {
                // 不在当前显示范围
                Cell cell = cinfo.cell;
                if (cell.Longitude < dRect.x1 || cell.Longitude > dRect.x2 
                    || cell.Latitude < dRect.y1 || cell.Latitude > dRect.y2)
                    continue;
                // 半径长度为0像素的情况
                if (!FillCellRegion(cinfo, tmpMap, ref mapRect))
                    continue;

                // 将小区的椭圆区域的颜色值复制到byteMap数组，并清空刚绘制的tmpMap
                CopyRegionToArray(tmpMap, mapRect, byteMap);
            }
            ColorMapping(bitMap, byteMap); // 颜色映射
            (new AverageBlur(bitMap)).BlurRGBChannel(2); // 模糊处理
            graphics.DrawImage(bitMap, clientRect.X, clientRect.Y);
            tmpMap.Dispose();

            // 保存当前的bitMap，防止相同重绘的重复计算
            if (lastBitmap != null) lastBitmap.Dispose();
            lastBitmap = bitMap;
        } // end function

        public static void ClearLastLayerCache()
        {
            if (lastBitmap != null)
                lastBitmap.Dispose();
            lastBitmap = null;
        }

        // 将小区的理想覆盖半径转换为像素长度
        private double ConvertRadiusPixel(double meter)
        {
            double geoLong = meter * 0.0001951 / 20;
            DbPoint start = new DbPoint(0, 0);
            DbPoint end = new DbPoint(geoLong, geoLong);
            PointF startF, endF;
            Map.ToDisplay(start, out startF);
            Map.ToDisplay(end, out endF);
            float ofsX = endF.X - startF.X;
            float ofsY = endF.Y - startF.Y;
            return Math.Sqrt(ofsX * ofsX + ofsY * ofsY);
        }

        // 颜色映射，注意位图的像素格式
        private void ColorMapping(Bitmap bitMap, byte[] byteMap)
        {
            BitmapData bmData = bitMap.LockBits(new Rectangle(0, 0, bitMap.Width, bitMap.Height), 
                ImageLockMode.WriteOnly, bitMap.PixelFormat);
            int srcIx = 0;
            int bmDataWidth = bmData.Width; int bmDataHeight = bmData.Height;
            unsafe
            {
                int* dstPtr = (int*)bmData.Scan0;
                fixed (byte* srcPtr = byteMap)
                {
                    for (int y = 0; y < bmDataHeight; ++y)
                        for (int x = 0; x < bmDataWidth; ++x)
                        {
                            srcIx = bmDataWidth * 4 * y + x * 4;
                            // 这个语句是去噪，但从色带取值看，这没有必要
                            dstPtr[bmDataWidth * y + x] = (int)RAINBOW_MINE_TYPE[srcPtr[srcIx]];
                        }
                }
            }
            bitMap.UnlockBits(bmData);
        }

        // 将小区的椭圆范围的颜色值复制到数组，并应用BlendMode.Screen效果，注意像素格式和颜色通道
        private void CopyRegionToArray(Bitmap bitMap, Rectangle mapRect, byte[] byteMap)
        {
            BitmapData bitMapData = bitMap.LockBits(mapRect, ImageLockMode.ReadWrite, bitMap.PixelFormat);
            int bmStride = bitMapData.Stride;
            int srcIx = 0, dstIx = 0;

            int mapRectHeight = mapRect.Height; int mapRectWidth = mapRect.Width;
            int mapRectX = mapRect.X; int mapRectY = mapRect.Y;

            unsafe
            {
                byte* srcPtr = (byte*)bitMapData.Scan0;
                fixed (byte* dstPtr = byteMap)
                {
                    for (int y = 0; y < mapRectHeight; ++y)
                    {
                        for (int x = 0; x < mapRectWidth; ++x)
                        {
                            srcIx = y * bmStride + x * 4;
                            dstIx = (mapRectY + y) * bmStride + (mapRectX + x) * 4;
                            // BlendMode.Screen效果公式的化简
                            dstPtr[dstIx] += (byte)(srcPtr[srcIx] - dstPtr[dstIx] * srcPtr[srcIx] / 255);
                            srcPtr[srcIx] = 0;
                        }
                    }
                }
            }
            bitMap.UnlockBits(bitMapData);
        }

        // 径向渐变填充小区的椭圆区域，用于绘制阶段的回归测试
        private bool FillCellRegion(CellWeightRegionInfo cinfo, Bitmap bitmap, 
            ref Rectangle rect)
        {
            float radiusPixel = (float)ConvertRadiusPixel(cinfo.radius);
            if (radiusPixel == 0) return false;

            PointF centerPiexl;
            RectangleF cellRectF = new RectangleF();

            // 小区位置像素
            Cell cell = cinfo.cell;
            Map.ToDisplay(new DbPoint(cell.Longitude, cell.Latitude), out centerPiexl);

            // 小区矩形设置
            cellRectF.X = centerPiexl.X;
            cellRectF.Y = centerPiexl.Y;
            float longAxisRate = (float)2;
            float shortAxisRate = (float)1.4;
            float offsetRate = (float)0.25;
            cellRectF.Width = radiusPixel * longAxisRate;
            cellRectF.Height = radiusPixel * shortAxisRate;

            // 旋转矩阵
            Matrix matrix = new Matrix();
            matrix.RotateAt(cell.Direction - 90, centerPiexl);
            matrix.Translate(-offsetRate * radiusPixel, -shortAxisRate / 2 * radiusPixel);

            // 路径
            GraphicsPath path = new GraphicsPath();
            path.AddEllipse(cellRectF);

            // 画刷
            PathGradientBrush brush = new PathGradientBrush(path);
            brush.CenterPoint = new PointF(
                centerPiexl.X + offsetRate * radiusPixel,
                centerPiexl.Y + radiusPixel * shortAxisRate / 2);
            brush.CenterColor = Color.FromArgb(255, 0, 0, (int)(255 * cinfo.Ratio));
            brush.SurroundColors = new Color[] { Color.FromArgb(255, 0, 0, 0) };

            Graphics g = Graphics.FromImage(bitmap);
            g.CompositingMode = CompositingMode.SourceCopy;
            g.Transform = matrix;
            g.FillEllipse(brush, cellRectF);

            // 设置返回,应用变换后的椭圆矩阵
            path.Transform(matrix);
            int x1 = int.MaxValue, x2 = int.MinValue, y1 = int.MaxValue, y2 = int.MinValue;
            PointF[] points = path.PathPoints;
            int length = points.Length, x, y;
            for (int i = 0; i < length; ++i)
            {
                x = (int)points[i].X; 
                y = (int)points[i].Y;
                x1 = Math.Min(x1, x);
                x2 = Math.Max(x2, x);
                y1 = Math.Min(y1, y);
                y2 = Math.Max(y2, y);
            }
            rect.X = Math.Max(0, x1); rect.Y = Math.Max(0, y1);
            rect.Width = Math.Min(x2, bitmap.Width) - rect.X;
            rect.Height = Math.Min(y2, bitmap.Height) - rect.Y;

            // 清除
            matrix.Dispose();
            brush.Dispose();
            path.Dispose();
            g.Dispose();

            return true;
        }

        private static Bitmap lastBitmap { get; set; }
        private static DbPoint lastCenter { get; set; }
        private uint[] RAINBOW_MINE_TYPE2 { get; set; } = {
        3925868788,3925868788, 3925868788, 3925868788, 3925868788, 3925868788, 3925868788, 3925868788, 
        3925868788, 3925868788, 3925868788, 3925868788, 3925868788, 3925868788, 3925868788, 3925868788, 
        3925868788, 3925868788, 3925868788, 3925868788, 3925868788, 3925868788, 3925868788, 3925868788, 
        4093640949, 4278190326, 4278191862, 4278193398, 4278195190, 4278196726, 4278198518, 4278200054, 
        4278201590, 4278203382, 4278204918, 4278206710, 4278208246, 4278209782, 4278211574, 4278213110, 
        4278214902, 4278216438, 4278217974, 4278219766, 4278221302, 4278223094, 4278224630, 4278226166, 
        4278227958, 4278229494, 4278296822, 4278232053, 4278232821, 4278233588, 4278234356, 4278235124, 
        4278235891, 4278236659, 4278237426, 4278238194, 4278238962, 4278239729, 4278240497, 4278241264, 
        4278242032, 4278242800, 4278243567, 4278244335, 4278245102, 4278245870, 4278246638, 4278247405, 
        4278248173, 4278248940, 4278249708, 4278250732, 4278250722, 4278250969, 4278251215, 4278251462, 
        4278251452, 4278251699, 4278251945, 4278252192, 4278252183, 4278252429, 4278252676, 4278252922, 
        4278252913, 4278253159, 4278253406, 4278253652, 4278253643, 4278253890, 4278254136, 4278254383, 
        4278254373, 4278254620, 4278254866, 4278255113, 4278255360, 4278910720, 4279566080, 4280221440, 
        4280876800, 4281597696, 4282253056, 4282908416, 4283563776, 4284219136, 4284940032, 4285595392, 
        4286250752, 4286906112, 4287561472, 4288282368, 4288937728, 4289593088, 4290248448, 4290903808, 
        4291624704, 4292280064, 4292935424, 4293590784, 4294246144, 4294967040, 4294900736, 4294834432, 
        4294768384, 4294702080, 4294636032, 4294569728, 4294503680, 4294437376, 4294371328, 4294305024, 
        4294238976, 4294172672, 4294106624, 4294040320, 4293974272, 4293907968, 4293841920, 4293775616, 
        4293709568, 4293643264, 4293577216, 4293510912, 4293444864, 4293378560, 4293378048, 4293377536, 
        4293442560, 4293507584, 4293572608, 4293637632, 4293702656, 4293767680, 4293832704, 4293897728,
        4293962752, 4294027776, 4294092800, 4294158080, 4294223104, 4294288128, 4294353152, 4294418176, 
        4294483200, 4294548224, 4294613248, 4294678272, 4294743296, 4294808320, 4294873344, 4294938624, 
        4294937088, 4294935552, 4294934016, 4294932480, 4294931200, 4294929664, 4294928128, 4294926592, 
        4294925312, 4294923776, 4294922240, 4294920704, 4294919424, 4294917888, 4294916352, 4294914816, 
        4294913536, 4294912000, 4294910464, 4294908928, 4294907648, 4294906112, 4294904576, 4294903040, 
        4294901760, 4294903045, 4294904330, 4294905615, 4294906900, 4294908185, 4294909470, 4294910755, 
        4294912040, 4294913325, 4294914867, 4294916152, 4294917437, 4294918722, 4294920007, 4294921292, 
        4294922577, 4294923862, 4294925147, 4294926432, 4294927974, 4294929259, 4294930544, 4294931829, 
        4294933114, 4294934399, 4294935684, 4294936969, 4294938254, 4294939539, 4294941081, 4294942366, 
        4294943651, 4294944936, 4294946221, 4294947506, 4294948791, 4294950076, 4294951361, 4294952646, 
        4294954188, 4294955473, 4294956758, 4294958043, 4294959328, 4294960613, 4294961898, 4294963183, 
        4294964468, 4294965753, 4294967295, 4294967295, 4294967295, 4294967295, 4294967295, 4294967295 };

        private uint[] RAINBOW_MINE_TYPE { get; set; } = {
        4278190335, 4278191615, 4278192895, 4278194175, 4278195455, 4278196735, 4278198015, 4278199295, 
        4278200575, 4278201855, 4278203135, 4278204415, 4278205695, 4278206975, 4278208255, 4278209535, 
        4278210815, 4278212095, 4278213375, 4278214655, 4278215935, 4278217215, 4278218495, 4278219775, 
        4278221055, 4278222335, 4278223615, 4278224895, 4278225919, 4278227199, 4278228479, 4278229759, 
        4278231039, 4278232319, 4278233599, 4278234879, 4278236159, 4278237439, 4278238719, 4278239999, 
        4278241279, 4278242559, 4278243839, 4278245119, 4278246399, 4278247679, 4278248959, 4278250239, 
        4278251519, 4278252799, 4278254079, 4278255359, 4278255611, 4278255606, 4278255601, 4278255596, 
        4278255591, 4278255586, 4278255581, 4278255576, 4278255571, 4278255566, 4278255561, 4278255556, 
        4278255551, 4278255546, 4278255541, 4278255536, 4278255531, 4278255526, 4278255521, 4278255516, 
        4278255511, 4278255506, 4278255501, 4278255496, 4278255492, 4278255487, 4278255482, 4278255477, 
        4278255472, 4278255467, 4278255462, 4278255457, 4278255452, 4278255447, 4278255442, 4278255437, 
        4278255432, 4278255427, 4278255422, 4278255417, 4278255412, 4278255407, 4278255402, 4278255397, 
        4278255392, 4278255387, 4278255382, 4278255377, 4278255372, 4278255367, 4278255362, 4278451968, 
        4278779648, 4279107328, 4279435008, 4279762688, 4280090368, 4280418048, 4280745728, 4281073408, 
        4281401088, 4281728768, 4282056448, 4282384128, 4282711808, 4283039488, 4283367168, 4283694848, 
        4284022528, 4284350208, 4284677888, 4285005568, 4285333248, 4285660928, 4285988608, 4286316288, 
        4286643968, 4286971648, 4287233792, 4287561472, 4287889152, 4288216832, 4288544512, 4288872192, 
        4289199872, 4289527552, 4289855232, 4290182912, 4290510592, 4290838272, 4291165952, 4291493632, 
        4291821312, 4292148992, 4292476672, 4292804352, 4293132032, 4293459712, 4293787392, 4294115072, 
        4294442752, 4294770432, 4294966528, 4294965248, 4294963968, 4294962688, 4294961408, 4294960128, 
        4294958848, 4294957568, 4294956288, 4294955008, 4294953728, 4294952448, 4294951168, 4294949888, 
        4294948608, 4294947328, 4294946048, 4294944768, 4294943488, 4294942208, 4294940928, 4294939648, 
        4294938368, 4294937088, 4294936064, 4294934784, 4294933504, 4294932224, 4294930944, 4294929664, 
        4294928384, 4294927104, 4294925824, 4294924544, 4294923264, 4294921984, 4294920704, 4294919424, 
        4294918144, 4294916864, 4294915584, 4294914304, 4294913024, 4294911744, 4294910464, 4294909184, 
        4294907904, 4294906624, 4294905344, 4294904064, 4294902784, 4294836224, 4294574082, 4294311940, 
        4294049798, 4293787656, 4293591050, 4293328909, 4293066767, 4292804624, 4292542482, 4292280340, 
        4292018199, 4291690521, 4291428379, 4291166237, 4290904095, 4290641952, 4290445346, 4290183204, 
        4289921062, 4289658920, 4289396778, 4289134637, 4288872495, 4288610352, 4288348210, 4288086068, 
        4287823926, 4287496248, 4287299642, 4287037500, 4286775358, 4286513216, 4286251074, 4285988932, 
        4285726790, 4285464648, 4285202506, 4284940365, 4284678223, 4284416080, 4284153938, 4283891796, 
        4283629654, 4283367512, 4283105370, 4282843228, 4282581086, 4282318944, 4282056802, 4281794660};

    } // end class

} // end namespace
