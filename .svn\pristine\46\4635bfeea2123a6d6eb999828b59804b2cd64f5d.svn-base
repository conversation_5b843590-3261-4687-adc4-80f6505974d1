﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteMultiCompareSettingDlg : BaseForm
    {
        private MapFormItemSelection itemSelection;
        protected ItemSelectionPanel projPanel;
        protected ItemSelectionPanel servPanel;
        public LteMultiCompareAutoSet curSet { get; set; } = LteMultiCompareCfgManager.GetInstance().AutoSet;
        public static readonly string StrRptTitleHead = "MultiCompare_";
        public LteMultiCompareSettingDlg()
            : base()
        {
            InitializeComponent();
            this.itemSelection = MainModel.GetInstance().MainForm.ItemSelection;
            initService();
            initProjs();
            setValue();
        }
        
        private void initService()
        {
            servPanel = new ItemSelectionPanel(toolStripDropDownService, listViewService, lbSvCount, new MapFormItemSelection(), "ServiceType", false);
            servPanel.FreshItems();
            toolStripDropDownService.Items.Add(new ToolStripControlHost(servPanel));
        }
        private void initProjs()
        {
            listViewProject.Items.Clear();
            if (mainModel.CategoryManager["Project"] != null)
            {
                projPanel = new ItemSelectionPanel(toolStripDropDownProject, listViewProject, lbProjCount, itemSelection, "Project", true);
                toolStripDropDownProject.Items.Clear();
                projPanel.FreshItems();
                toolStripDropDownProject.Items.Add(new ToolStripControlHost(projPanel));
            }
        }

        private void setValue()
        {
            fillPeriodColumnView(null);
            txtFilePath.Text = curSet.ReprortSavePath;
            chkFilter.Checked = curSet.IsFilterFile;
            txtFileNameFilter.Text = curSet.StrFileNameFilter;
            numEvtDistance.Value = (decimal)curSet.DistanceEvt;
            chkAnaMessageCellInfo.Checked = curSet.IsAnaMessageCellInfo;

            #region 对比事件
            listViewEvent.Items.Clear();
            foreach (int evtId in curSet.SelectEvtIDSet)
            {
                EventInfo info = EventInfoManager.GetInstance()[evtId];
                if (info == null)
                {
                    continue;
                }
                ListViewItem lvi = new ListViewItem();
                lvi.Text = info.Name;
                lvi.Tag = info.ID;
                listViewEvent.Items.Add(lvi);
            }
            this.lblSelectEvtCount.Text = string.Format("[{0}]", this.listViewEvent.Items.Count);
            #endregion

            #region 项目类型
            listViewProject.Items.Clear();
            CategoryEnum projectCate = (CategoryEnum)CategoryManager.GetInstance()["Project"];
            foreach (int projID in curSet.ProjectIDSet)
            {
                ListViewItem lvi = new ListViewItem();
                lvi.Text = projectCate[projID].Name;
                lvi.Tag = projectCate[projID].ID;
                listViewProject.Items.Add(lvi);
            }
            this.lbProjCount.Text = string.Format("[{0}]", this.listViewProject.Items.Count);
            #endregion

            #region 业务类型
            this.listViewService.Items.Clear();
            CategoryEnumItem[] svItems = ((CategoryEnum)CategoryManager.GetInstance()["ServiceType"]).Items;
            foreach (int id in curSet.ServiceIDSet)
            {
                foreach (CategoryEnumItem item in svItems)
                {
                    if (item.ID == id)
                    {
                        ListViewItem lvItem = new ListViewItem(item.Description);
                        lvItem.Tag = item.ID;
                        this.listViewService.Items.Add(lvItem);
                        break;
                    }
                }
            }
            this.lbSvCount.Text = string.Format("[{0}]", this.listViewService.Items.Count);
            #endregion
        }

        private void btnSelectFilePath_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog folderDialog = new FolderBrowserDialog();
            if (folderDialog.ShowDialog() == DialogResult.OK)
            {
                txtFilePath.Text = folderDialog.SelectedPath;
            }
        }

        private void btnPopupService_Click(object sender, EventArgs e)
        {
            List<int> idSet = new List<int>();
            foreach (ListViewItem item in this.listViewService.Items)
            {
                idSet.Add((int)item.Tag);
            }
            this.servPanel.UpdateNodeState(idSet);
            System.Drawing.Point pt = new System.Drawing.Point(btnPopupService.Width, btnPopupService.Height);
            toolStripDropDownService.Show(btnPopupService, pt, ToolStripDropDownDirection.AboveLeft);
        }

        private void buttonProject_Click(object sender, EventArgs e)
        {
            System.Drawing.Point pt = new System.Drawing.Point(buttonProject.Width, buttonProject.Height);
            toolStripDropDownProject.Show(buttonProject, pt, ToolStripDropDownDirection.BelowLeft);
        }

        private void btnSelEvt_Click(object sender, EventArgs e)
        {
            EventChooserForm eventChooser = new EventChooserForm(MainModel, curSet.SelectEvtIDSet);
            if (eventChooser.ShowDialog() == DialogResult.OK)
            {
                listViewEvent.Items.Clear();
                curSet.SelectEvtIDSet = eventChooser.SelectedEventIDs;
                foreach (int id in eventChooser.SelectedEventIDs)
                {
                    EventInfo info = EventInfoManager.GetInstance()[id];
                    ListViewItem lvi = new ListViewItem();
                    lvi.Text = info.Name;
                    lvi.Tag = info.ID;
                    listViewEvent.Items.Add(lvi);
                }
                this.lblSelectEvtCount.Text = string.Format("[{0}]", this.listViewEvent.Items.Count);
            }
        }

        private void btnAddPeriod_Click(object sender, EventArgs e)
        {
            TimePeriod period = new TimePeriod();
            if (period.SetPeriod(timePickerBegin.Value.Date, timePickerEnd.Value.Date.AddDays(1).AddMilliseconds(-1)))
            {
                foreach (TimePeriod pd in curSet.Periods)
                {
                    if (pd.IsIntersect(period)) return;
                }
                curSet.Periods.Add(period);
                fillPeriodColumnView(period);
            }
        }

        private void btnUpdate_Click(object sender, EventArgs e)
        {
            if (curPeriod == null)
            {
                return;
            }

            TimePeriod period = new TimePeriod();
            if (period.SetPeriod(timePickerBegin.Value.Date, timePickerEnd.Value.Date.AddDays(1).AddMilliseconds(-1)))
            {
                curSet.Periods.Remove(curPeriod);
                bool isCanAdd = true;
                foreach (TimePeriod pd in curSet.Periods)
                {
                    if (pd.IsIntersect(period))//与已有时间段重叠
                    {
                        isCanAdd = false;
                        break;
                    }
                }
                if (isCanAdd)
                {
                    curSet.Periods.Add(period);
                    fillPeriodColumnView(period);
                }
                else
                {
                    curSet.Periods.Add(curPeriod);
                }
            }
        }
        private void btnRemovePeriod_Click(object sender, EventArgs e)
        {
            if (curPeriod == null)
            {
                return;
            }
            curSet.Periods.Remove(curPeriod);
            curPeriod = null;
            fillPeriodColumnView(null);
        }

        TimePeriod curPeriod;
        private void listPeriods_SelectedIndexChanged(object sender, EventArgs e)
        {
            curPeriod = listPeriods.SelectedItem as TimePeriod;
            if (curPeriod == null)
            {
                btnUpdate.Enabled = btnRemovePeriod.Enabled = false;
                return;
            }
            btnUpdate.Enabled = btnRemovePeriod.Enabled = true;
            timePickerBegin.Value = curPeriod.BeginTime;
            timePickerEnd.Value = curPeriod.EndTime;
        }
        private void fillPeriodColumnView(TimePeriod selPeriod)
        {
            #region
            listPeriods.SelectedIndexChanged -= listPeriods_SelectedIndexChanged;
            listPeriods.Items.Clear();
            btnUpdate.Enabled = btnRemovePeriod.Enabled = false;
            curSet.Periods.Sort(comparer);
            foreach (TimePeriod period in curSet.Periods)
            {
                period.showDayFormat = false;
                listPeriods.Items.Add(period);
            }
            listPeriods.SelectedIndexChanged += listPeriods_SelectedIndexChanged;
            if (listPeriods.Items.Count > 0)
            {
                if (selPeriod != null)
                {
                    listPeriods.SelectedItem = selPeriod;
                }
                else
                {
                    listPeriods.SelectedIndex = 0;
                }
            }
            #endregion
        }

        private void listPeriods_DrawItem(object sender, DrawItemEventArgs e)
        {
            e.DrawBackground();
            e.DrawFocusRectangle();
            if (listPeriods.Items.Count > 0)
            {
                e.Graphics.DrawString(listPeriods.Items[e.Index].ToString(), e.Font, new SolidBrush(e.ForeColor), e.Bounds);
            }
        }

        private Comparer comparer = new Comparer();
        private class Comparer : IComparer<TimePeriod>
        {
            public int Compare(TimePeriod x, TimePeriod y)
            {
                return x.IBeginTime - y.IBeginTime;
            }
        }

        private void btnDiyReportSet_Click(object sender, EventArgs e)
        {
            StatReportColumnDefDlg dlg = new StatReportColumnDefDlg(0, false);
            ReporterTemplate tpl = ReporterTemplateManager.LoadSingleReportFromFile(StrRptTitleHead);
            dlg.SetCurTemplate(tpl);
            if (DialogResult.OK == dlg.ShowDialog(this))
            {
                if (dlg.IsNewMode())
                {
                    tpl = dlg.GetCurNewTemplate();
                }
                ReporterTemplateManager.SaveSingleReportTemplate(StrRptTitleHead, tpl);
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            saveSet();
        }
        private void btnOK_Click(object sender, EventArgs e)
        {
            saveSet();
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void saveSet()
        {
            List<int> projectIDList = new List<int>();
            foreach (ListViewItem item in this.listViewProject.Items)
            {
                projectIDList.Add((int)item.Tag);
            }

            List<int> serviceIDList = new List<int>();
            foreach (ListViewItem item in this.listViewService.Items)
            {
                serviceIDList.Add((int)item.Tag);
            }

            string strError = "";
            if (projectIDList.Count == 0)
            {
                strError = "需至少选择一种项目类型！";
            }
            else if (serviceIDList.Count == 0)
            {
                strError = "需至少选择一种业务类型！";
            }
            else if (curSet.Periods.Count == 0)
            {
                strError = "需至少选择一个时间区间！";
            }
            else if (curSet.SelectEvtIDSet.Count == 0)
            {
                strError = "需至少选择一种问题类型！";
            }
            if (strError != "")
            {
                MessageBox.Show(strError);
                return;
            }
            curSet.ReprortSavePath = txtFilePath.Text;
            curSet.IsFilterFile = chkFilter.Checked;
            curSet.StrFileNameFilter = txtFileNameFilter.Text;
            curSet.DistanceEvt = (double)numEvtDistance.Value;
            curSet.IsAnaMessageCellInfo = chkAnaMessageCellInfo.Checked;
            curSet.ProjectIDSet = projectIDList;
            curSet.ServiceIDSet = serviceIDList;
            LteMultiCompareCfgManager.GetInstance().Save();
        }

        private void btnMsgParam_Click(object sender, EventArgs e)
        {
            List<MsgParamSetting> paramList = new List<MsgParamSetting>();
            if (curSet.MsgParamList.Count <= 0 || string.IsNullOrEmpty(curSet.MsgParamList[0].ParamRightValue))
            {
                paramList.Add(new MsgParamSetting(true, MsgParamSetting.ParamName_LongDrxCycle));
                paramList.Add(new MsgParamSetting(true, MsgParamSetting.ParamName_OnDurationTimer));
                paramList.Add(new MsgParamSetting(true, MsgParamSetting.ParamName_DrxInactivityTimer));
                paramList.Add(new MsgParamSetting(true, MsgParamSetting.ParamName_DrxRetransmissionTimer));
                paramList.Add(new MsgParamSetting(true, MsgParamSetting.ParamName_ShortDrxCycle));
                paramList.Add(new MsgParamSetting(true, MsgParamSetting.ParamName_DrxShortCycleTimer));
                paramList.Add(new MsgParamSetting(true, MsgParamSetting.ParamName_DefaultPagingCycle));
                paramList.Add(new MsgParamSetting(true, MsgParamSetting.ParamName_ReferenceSignalPower));
                paramList.Add(new MsgParamSetting(true, MsgParamSetting.ParamName_Pb));
                paramList.Add(new MsgParamSetting(true, MsgParamSetting.ParamName_Pa));
                paramList.Add(new MsgParamSetting(true, MsgParamSetting.ParamName_PreambleIRTargetPower));
                paramList.Add(new MsgParamSetting(true, MsgParamSetting.ParamName_PreambleTransMax));
                paramList.Add(new MsgParamSetting(true, MsgParamSetting.ParamName_PowerRampingStep));
                paramList.Add(new MsgParamSetting(true, MsgParamSetting.ParamName_P_max));
                paramList.Add(new MsgParamSetting(true, MsgParamSetting.ParamName_P0_NominalPUCCH));
                paramList.Add(new MsgParamSetting(true, MsgParamSetting.ParamName_Alpha));
                paramList.Add(new MsgParamSetting(true, MsgParamSetting.ParamName_P0NominalPusch));
                paramList.Add(new MsgParamSetting(true, MsgParamSetting.ParamName_QRxLevMin));
                paramList.Add(new MsgParamSetting(true, MsgParamSetting.ParamName_S_IntraSearch));
                paramList.Add(new MsgParamSetting(true, MsgParamSetting.ParamName_Q_OffsetCellPlusQ_Hyst));
                paramList.Add(new MsgParamSetting(true, MsgParamSetting.ParamName_A3offsetPlusHysteresis));
                paramList.Add(new MsgParamSetting(true, MsgParamSetting.ParamName_A3Time_to_trigger));
                paramList.Add(new MsgParamSetting(true, MsgParamSetting.ParamName_DifSysA2Gate));
            }
            else
            {
                paramList = curSet.MsgParamList;
            }
            MsgParamCheckDlg dlg = new MsgParamCheckDlg(paramList);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                curSet.MsgParamList = dlg.GetCondition();
            }
        }
    }
}
