﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    public class CreateWlanMacApInfoForm : CreateChildForm
    {
        public CreateWlanMacApInfoForm(MainModel mm)
            : base(mm)
        { 
        }
        public override string Description
        {
            get
            {
                return "创建WLAN Mac窗口 WlanMacApInfoForm ";
            }
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 20000, 20024, this.Name);
        }
        public override string Name
        {
            get
            {
                return "Mac";
            }
        }

        protected override void initAction()
        {
            Dictionary<string, object> actionParam = new Dictionary<string, object>();
            actionParam["MainForm"] = MainModel.MainForm;
            actionParam["AssemblyName"] = "RAMS.exe";
            actionParam["TypeName"] = "MasterCom.RAMS.Func.WlanMacApInfoForm";
            actionParam["Text"] = "Mac";
            actionParam["ImageFilePath"] = @"images\数据业务专题\参数分析.png";
            action = new ActionCreateChildFrame();
            action.Param = actionParam;
        }
    }
}
