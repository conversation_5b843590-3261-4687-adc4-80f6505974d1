﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Data;

using MapWinGIS;

using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.MTGis;
using DevExpress.XtraEditors;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc
{
    public class ChkMgrsGridItem
    {
        public ChkMgrsGridItem(string mgrsString)
        {
            this.MgrsString = mgrsString;
            SetLnglat();
        }

        public ChkMgrsGridStyle Style
        {
            get;
            set;
        }

        public string MgrsString
        {
            get;
            private set;
        }

        public DbRect DbRect
        {
            get;
            private set;
        }

        public override string ToString()
        {
            return this.MgrsString;
        }

        public double LtLng { get; private set; }
        public double LtLat { get; private set; }
        public double RtLng { get; private set; }
        public double RtLat { get; private set; }
        public double RbLng { get; private set; }
        public double RbLat { get; private set; }
        public double LbLng { get; private set; }
        public double LbLat { get; private set; }

        private void SetLnglat()
        {
            double tlLng, tlLat, brLng, brLat;
            GetGridLngLat(this.MgrsString, out tlLng, out tlLat, out brLng, out brLat);
            LtLng = tlLng;
            LtLat = tlLat;
            RtLng = brLng;
            RtLat = tlLat;
            RbLng = brLng;
            RbLat = brLat;
            LbLng = tlLng;
            LbLat = brLat;
            this.DbRect = new DbRect(new DbPoint(LbLng, LbLat), new DbPoint(RtLng, RtLat));
        }

        private void GetGridLngLat(string mgrsString, out double tlLng, out double tlLat, out double brLng, out double brLat)
        {
            string[] array = mgrsString.Split('-');
            int east = Convert.ToInt32(array[1]);
            int north = Convert.ToInt32(array[2]);
            int size = Convert.ToInt32(array[3]);

            StringBuilder blSb = new StringBuilder(array[0] + "-");
            blSb.Append((east * size).ToString().PadLeft(5, '0') + "-");
            blSb.Append((north * size).ToString().PadLeft(5, '0'));

            StringBuilder trSb = new StringBuilder(array[0] + "-");
            trSb.Append(((east + 1) * size).ToString().PadLeft(5, '0') + "-");
            trSb.Append(((north + 1) * size).ToString().PadLeft(5, '0'));

            MGRUTM2LatLon converter = new MGRUTM2LatLon();
            double[] blLatLong = converter.convertMGRUTMToLatLong(blSb.ToString());
            double[] trLatLong = converter.convertMGRUTMToLatLong(trSb.ToString());

            tlLng = blLatLong[1];
            tlLat = trLatLong[0];
            brLng = trLatLong[1];
            brLat = blLatLong[0];
        }
    }

    public class ChkMgrsGridStyle
    {
        public Color FillColor
        {
            get;
            set;
        }

        public bool Visible
        {
            get;
            set;
        }
    }

    public class ChkMgrsGridSet
    {
        public ChkMgrsGridSet()
        {
            MobileStyle = new ChkMgrsGridStyle();
            MobileStyle.FillColor = Color.FromArgb(100, Color.Red);
            MobileStyle.Visible = true;

            MasterStyle = new ChkMgrsGridStyle();
            MasterStyle.FillColor = Color.FromArgb(100, Color.Blue);
            MasterStyle.Visible = true;

            ShareStyle = new ChkMgrsGridStyle();
            ShareStyle.FillColor = Color.FromArgb(100, Color.Green);
            ShareStyle.Visible = true;
        }

        public void ReadFrom(string xlsFile)
        {
            // get table
            string mcColumn = "名通栅格";  // MasterCom
            string cmColumn = "集团栅格";  // ChinaMobile
            DataSet ds = ExcelNPOIManager.ImportFromExcel(xlsFile);
            DataTable dt = getDataTable(mcColumn, cmColumn, ds);
            if (dt == null)
            {
                XtraMessageBox.Show(string.Format("未找到包含'{0}'和'{1}'列名的工作表", mcColumn, cmColumn), "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // get row
            Dictionary<string, ChkMgrsGridItem> mcGridDic = new Dictionary<string, ChkMgrsGridItem>();
            Dictionary<string, ChkMgrsGridItem> cmGridDic = new Dictionary<string, ChkMgrsGridItem>();
            getGridDic(mcColumn, cmColumn, dt, mcGridDic, cmGridDic);

            // classify
            foreach (string cmKey in cmGridDic.Keys)  // ChinaMobile
            {
                if (mcGridDic.ContainsKey(cmKey))
                {
                    cmGridDic[cmKey].Style = ShareStyle;
                    ShareList.Add(cmGridDic[cmKey]);
                }
                else
                {
                    cmGridDic[cmKey].Style = MobileStyle;
                    MobileList.Add(cmGridDic[cmKey]);
                }
            }
            foreach (string mcKey in mcGridDic.Keys)  // MasterCom
            {
                if (!cmGridDic.ContainsKey(mcKey))
                {
                    mcGridDic[mcKey].Style = MasterStyle;
                    MasterList.Add(mcGridDic[mcKey]);
                }
            }
        }

        private static void getGridDic(string mcColumn, string cmColumn, DataTable dt, Dictionary<string, ChkMgrsGridItem> mcGridDic, Dictionary<string, ChkMgrsGridItem> cmGridDic)
        {
            foreach (DataRow row in dt.Rows)
            {
                object mcValue = row[mcColumn];
                object cmValue = row[cmColumn];
                if (!Convert.IsDBNull(mcValue) && !mcGridDic.ContainsKey(mcValue as string))
                {
                    mcGridDic.Add(mcValue as string, new ChkMgrsGridItem(mcValue as string));
                }
                if (!Convert.IsDBNull(cmValue) && !cmGridDic.ContainsKey(cmValue as string))
                {
                    cmGridDic.Add(cmValue as string, new ChkMgrsGridItem(cmValue as string));
                }
            }
        }

        private static DataTable getDataTable(string mcColumn, string cmColumn, DataSet ds)
        {
            DataTable dt = null;
            foreach (DataTable table in ds.Tables)
            {
                if (table.Columns.Contains(mcColumn) && table.Columns.Contains(cmColumn))
                {
                    dt = table;
                    break;
                }
            }

            return dt;
        }

        public void Draw()
        {
            TempLayer.Instance.Draw(this.Drawer);
        }

        public void Clear()
        {
            MobileList.Clear();
            MasterList.Clear();
            ShareList.Clear();
            TempLayer.Instance.Clear();
        }

        public void GetSelectedInfo(double x, double y, ref List<string> titles, ref List<string> infos)
        {
            //mop2.CheckRectIntersectWithRegion()
            foreach (ChkMgrsGridItem grid in MobileList)
            {
                if (grid.Style.Visible && grid.DbRect.IsPointInThisRect(x, y))
                {
                    titles.Add(grid.MgrsString);
                    infos.Add(grid.ToString());
                    return;
                }
            }
            foreach (ChkMgrsGridItem grid in MasterList)
            {
                if (grid.Style.Visible && grid.DbRect.IsPointInThisRect(x, y))
                {
                    titles.Add(grid.MgrsString);
                    infos.Add(grid.ToString());
                    return;
                }
            }
            foreach (ChkMgrsGridItem grid in ShareList)
            {
                if (grid.Style.Visible && grid.DbRect.IsPointInThisRect(x, y))
                {
                    titles.Add(grid.MgrsString);
                    infos.Add(grid.ToString());
                    return;
                }
            }
        }

        public ChkMgrsGridItem Find(string mgrsString)
        {
            if (string.IsNullOrEmpty(mgrsString))
            {
                return null;
            }
            foreach (ChkMgrsGridItem grid in MobileList)
            {
                if (grid.MgrsString == mgrsString)
                {
                    return grid;
                }
            }
            foreach (ChkMgrsGridItem grid in MasterList)
            {
                if (grid.MgrsString == mgrsString)
                {
                    return grid;
                }
            }
            foreach (ChkMgrsGridItem grid in ShareList)
            {
                if (grid.MgrsString == mgrsString)
                {
                    return grid;
                }
            }
            return null;
        }

        private void Drawer(Rectangle clientRect, Rectangle updateRect, Graphics graphics, MapOperation mop)
        {
            float ratio = CustomDrawLayer.GetTestPointDisplayRatio(mop.Scale);
            updateRect.Inflate((int)(64 * ratio), (int)(64 * ratio));
            DbRect dRect;
            mop.FromDisplay(updateRect, out dRect);

            Brush mobileBrush = new SolidBrush(MobileStyle.FillColor);
            foreach (ChkMgrsGridItem grid in MobileList)
            {
                if (grid.DbRect.Within(dRect) && MobileStyle.Visible)
                {
                    DrawGrid(grid, graphics, mobileBrush, mop);
                }
            }

            Brush masterBrush = new SolidBrush(MasterStyle.FillColor);
            foreach (ChkMgrsGridItem grid in MasterList)
            {
                if (grid.DbRect.Within(dRect) && MasterStyle.Visible)
                {
                    DrawGrid(grid, graphics, masterBrush, mop);
                }
            }

            Brush shareBrush = new SolidBrush(ShareStyle.FillColor);
            foreach (ChkMgrsGridItem grid in ShareList)
            {
                if (grid.DbRect.Within(dRect) && ShareStyle.Visible)
                {
                    DrawGrid(grid, graphics, shareBrush, mop);
                }
            }
        }

        private void DrawGrid(ChkMgrsGridItem grid, Graphics graphics, Brush brush, MapOperation mop)
        {
            PointF ltPoint, rtPoint, rbPoint, lbPoint;
            mop.ToDisplay(new DbPoint(grid.LtLng, grid.LtLat), out ltPoint);
            mop.ToDisplay(new DbPoint(grid.RtLng, grid.RtLat), out rtPoint);
            mop.ToDisplay(new DbPoint(grid.RbLng, grid.RbLat), out rbPoint);
            mop.ToDisplay(new DbPoint(grid.LbLng, grid.LbLat), out lbPoint);
            graphics.FillPolygon(brush, new PointF[] { ltPoint, rtPoint, rbPoint, lbPoint });
        }

        public List<ChkMgrsGridItem> MobileList { get; set; } = new List<ChkMgrsGridItem>();
        public List<ChkMgrsGridItem> MasterList { get; set; } = new List<ChkMgrsGridItem>();
        public List<ChkMgrsGridItem> ShareList { get; set; } = new List<ChkMgrsGridItem>();

        public ChkMgrsGridStyle MobileStyle { get; set; }
        public ChkMgrsGridStyle MasterStyle { get; set; }
        public ChkMgrsGridStyle ShareStyle { get; set; }
    }
}
