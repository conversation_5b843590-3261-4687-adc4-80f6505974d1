﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System.Windows.Forms;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.BackgroundFunc;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class CSFBCauseQueryAna : DIYAnalyseByFileBackgroundBase
    {
        public static int CauseTimeSpan { get; set; } = 5;//默认回看5s

        private readonly MainModel mModel;
        protected List<int> eCSFBRequestLst;
        protected List<int> eBlockCallLst;
        protected List<int> eCSFBFailureLst;

        protected List<CScene> sceneList;
        protected CScene scAnaNormal;

        private Dictionary<string, CSFBBlockCallSummaryInfo> nameSumaryDic;
        private int totalNum = 0;

        protected static readonly object lockObj = new object();
        private static CSFBCauseQueryAna instance = null;
        public static CSFBCauseQueryAna GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new CSFBCauseQueryAna(MainModel.GetInstance());
                    }
                }
            }
            return instance;
        }
        public CSFBCauseQueryAna(MainModel mModel)
            : base(mModel)
        {
            IncludeTestPoint = true;
            IncludeEvent = true;
            IncludeMessage = true;
            FilterSampleByRegion = false;
            FilterEventByRegion = true;

            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.LTE_TDD_VOICE);
            ServiceTypes.Add(ServiceType.LTE_FDD_VOICE);
            this.mModel = mModel;
            init();
        }

        public override string Name
        {
            get
            {
                return "CSFB未接通原因分析";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 22000, 22049, this.Name);
        }

        //初始12原因 + 6大场景
        private void init()
        {
            scAnaNormal = new CScene_Normal();

            sceneList = new List<CScene>();
            sceneList.Add(new CScene_CMoRequest2BlockCall());
            sceneList.Add(new CScene_CMtRequest2BlockCall());

            eCSFBRequestLst = new List<int>();
            foreach (ECSFBRequest ecsfbr in Enum.GetValues(typeof(ECSFBRequest)))
            {
                eCSFBRequestLst.Add((int)ecsfbr);
            }

            eBlockCallLst = new List<int>();
            foreach (EBlockCall ebc in Enum.GetValues(typeof(EBlockCall)))
            {
                eBlockCallLst.Add((int)ebc);
            }

            eCSFBFailureLst = new List<int>();
            foreach (ECSFBFailure ecsfbf in Enum.GetValues(typeof(ECSFBFailure)))
            {
                eCSFBFailureLst.Add((int)ecsfbf);
            }

            nameSumaryDic = new Dictionary<string, CSFBBlockCallSummaryInfo>();
        }

        private void reCatCause()
        {
            foreach (CScene sc in sceneList)
            {
                sc.ReCatCause(scAnaNormal.SceneCause);
            }
        }
        protected bool showFuncCondSetDlg()
        {
            CSFBCauseSettingDlg dlg = new CSFBCauseSettingDlg(scAnaNormal.SceneCause);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                dlg.RefreshData();
                scAnaNormal.SceneCause = dlg.GetCause();
                return true;
            }
            return false;
        }
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                reCatCause();
                return true;
            }
            if (showFuncCondSetDlg())
            {
                reCatCause();
                return true;
            }
            return false;
        }

        protected override void getReadyBeforeQuery()
        {
            nameSumaryDic.Clear();
            totalNum = 0;
        }

        protected override void analyseFiles()
        {
            try
            {
                List<FileInfo> files = MainModel.FileInfos;
                Dictionary<int, FileInfo> idFileInfoDic = new Dictionary<int, FileInfo>();
                Dictionary<FileInfo, FileInfo> fileMoMtDic = getFileMoMtDic(files, idFileInfoDic);

                if (MainModel.IsBackground)
                {
                    reportBackgroundInfo(string.Format("共读取待分析文件{0}对...", fileMoMtDic.Count));
                }

                int iloop = 0;
                foreach (KeyValuePair<FileInfo, FileInfo> filePair in fileMoMtDic)
                {
                    string fileNameOther = "";
                    condition.FileInfos.Clear();
                    condition.FileInfos.Add(filePair.Key);
                    if (filePair.Value != null)
                    {
                        fileNameOther = filePair.Value.Name;
                        condition.FileInfos.Add(filePair.Value);
                    }

                    if (MainModel.IsBackground)
                    {
                        reportBackgroundInfo(string.Format("正在分析 {0}{1} 类 {2}，当前文件 {3}/{4}对...文件名：{5};{6}"
                            , FuncType, SubFuncType, Name, ++iloop, fileMoMtDic.Count, filePair.Key.Name, fileNameOther));
                    }
                    replay();

                    if (MasterCom.Util.WaitBox.CancelRequest || mainModel.BackgroundStopRequest)
                    {
                        break;
                    }
                }
                MainModel.ClearDTData();
                dosomethingAfterAnalyse();
            }
            finally
            {
                System.Threading.Thread.Sleep(1000);
                MasterCom.Util.WaitBox.Close();
            }
        }

        private static Dictionary<FileInfo, FileInfo> getFileMoMtDic(List<FileInfo> files, Dictionary<int, FileInfo> idFileInfoDic)
        {
            Dictionary<FileInfo, FileInfo> fileMoMtDic = new Dictionary<FileInfo, FileInfo>();
            List<FileInfo> mtFiles = new List<FileInfo>();

            foreach (FileInfo file in files)
            {
                idFileInfoDic[file.ID] = file;
                if (file.Momt != (int)EMoMt.Mt)
                {
                    fileMoMtDic[file] = null;
                }
                else
                {
                    mtFiles.Add(file);
                }
            }
            foreach (FileInfo file in mtFiles)
            {
                if (idFileInfoDic.ContainsKey(file.EventCount))
                {
                    fileMoMtDic[idFileInfoDic[file.EventCount]] = file;
                }
                else
                {
                    fileMoMtDic[file] = null;
                }
            }

            return fileMoMtDic;
        }

        protected override void doStatWithQuery()
        {
            List<DTFileDataManager> files = new List<DTFileDataManager>();
            foreach (DTFileDataManager file in mModel.DTDataManager.FileDataManagers)
            {
                if (file.MoMtFlag == (int)EMoMt.Mt)
                {
                    files.Add(file);
                }
            }
            foreach (DTFileDataManager file in mModel.DTDataManager.FileDataManagers)
            {
                if (file.MoMtFlag == (int)EMoMt.Mo)
                {
                    files.Add(file);
                }
            }
            Dictionary<Event, CSFBBlockCallInfo> evtTCHInfoDic = new Dictionary<Event, CSFBBlockCallInfo>();

            try
            {
                foreach (DTFileDataManager file in files)
                {
                    List<CSFBBlockCallInfo> callSet = getCallSet(file);
                    dealCSFBBlockCallInfo(evtTCHInfoDic, file, callSet);
                }
            }
            catch (Exception ex)
            {
                showException(ex);
            }
        }

        private List<CSFBBlockCallInfo> getCallSet(DTFileDataManager file)
        {
            List<CSFBBlockCallInfo> callSet = new List<CSFBBlockCallInfo>();
            CSFBBlockCallInfo info = null;
            foreach (Event e in file.Events)
            {
                if (!isValidEvent(e))
                {
                    continue;
                }

                if (eCSFBRequestLst.Contains(e.ID))
                {
                    info = new CSFBBlockCallInfo(e);
                    callSet.Add(info);
                }
                else if (eBlockCallLst.Contains(e.ID) && info != null)
                {
                    info.Eblockcall = e;
                    info.EvtList.Add(e);
                }
                else if (eCSFBFailureLst.Contains(e.ID) && info != null)
                {
                    info.Ecsfbfailure = e;
                    info.EvtList.Add(e);
                }
            }

            return callSet;
        }

        private void dealCSFBBlockCallInfo(Dictionary<Event, CSFBBlockCallInfo> evtTCHInfoDic, DTFileDataManager file, List<CSFBBlockCallInfo> callSet)
        {
            foreach (CSFBBlockCallInfo call in callSet)
            {
                if (call.Eblockcall == null || call.Ecsfbfailure == null)
                {
                    continue;
                }
                CScene scAna = scAnaNormal;
                foreach (CScene sc in sceneList)
                {
                    if (sc.IsInScene(file, call.Eblockcall))
                    {
                        scAna = sc;
                        break;
                    }
                }
                call.GetCellInfo(file);
                call.Scene = scAna.Clone() as CScene;
                call.CCause = scAna.GetCause(file, call.Eblockcall).Clone() as CCauseBase;

                setEvtInfo(evtTCHInfoDic, file, call);

                CSFBBlockCallSummaryInfo summaryInfo;
                if (!nameSumaryDic.TryGetValue(call.CCause.GetCauseName(), out summaryInfo))
                {
                    summaryInfo = new CSFBBlockCallSummaryInfo(call.CCause.GetCauseName());
                    nameSumaryDic[call.CCause.GetCauseName()] = summaryInfo;
                }
                summaryInfo.AddBlockCall(call);
                totalNum++;
            }
        }

        private void setEvtInfo(Dictionary<Event, CSFBBlockCallInfo> evtTCHInfoDic, DTFileDataManager file, CSFBBlockCallInfo call)
        {
            if (file.MoMtFlag == (int)EMoMt.Mt)
            {
                evtTCHInfoDic[call.Eblockcall] = call;
            }
            else if (file.MoMtFlag == (int)EMoMt.Mo)
            {
                foreach (Event eMt in evtTCHInfoDic.Keys)
                {
                    if (Math.Abs(eMt.DateTime.Subtract(call.Eblockcall.DateTime).TotalSeconds) <= 10)
                    {
                        call.CCause = evtTCHInfoDic[eMt].CCause.Clone() as CCauseBase;
                        break;
                    }
                }
            }
        }

        private bool isValidEvent(Event e)
        {
            if (Condition.Geometorys == null || Condition.Geometorys.GeoOp.Contains(e.Longitude, e.Latitude))
            {
                return true;
            }
            return false;
        }

        protected virtual void dosomethingAfterAnalyse()
        {
            foreach (CSFBBlockCallSummaryInfo info in nameSumaryDic.Values)
            {
                info.Percent = (float)info.Num * 100 / totalNum;
            }
        }

        protected override void fireShowForm()
        {
            if (nameSumaryDic.Count == 0)
            {
                MessageBox.Show("没有符合条件的数据!", "提示");
                return;
            }
            CSFBCauseInfoForm form = mModel.CreateResultForm(typeof(CSFBCauseInfoForm)) as CSFBCauseInfoForm;
            form.FillData(nameSumaryDic, sceneList);
            form.Owner = MainModel.MainForm;
            form.Visible = true;
            form.BringToFront();
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.LTE业务专题; }
        }

        public override BackgroundSubFuncType SubFuncType
        {
            get { return BackgroundSubFuncType.其他; }
        }

        private Dictionary<string, object> getCondParam(CCauseBase cause)
        {
            Dictionary<string, object> param = new Dictionary<string, object>();
            CCauseBase cb = cause;
            while (cb != null)
            {
                param[cb.GetCauseName()] = cb.Cond.Param;
                cb = cb.NextCause;
            }
            return param;
        }
        private CCauseBase getCondByParam(CCauseBase cause, Dictionary<string, object> param)
        {
            if (param == null || param.Count <= 0)
            {
                return cause;
            }
            List<CCauseBase> causeList = new List<CCauseBase>();
            CCauseBase cb = cause;
            int lastInValidSN = param.Count;
            while (cb != null)
            {
                string causeName = cb.GetCauseName();

                cb.SN = getParamCauseSN(param, causeName, ref lastInValidSN);//获取在配置中的序号，分析时优先按该序号分析
                causeList.Add(cb);

                if (param.ContainsKey(causeName))
                {
                    cb.Cond.Param = param[causeName] as Dictionary<string, object>;
                }
                cb = cb.NextCause;
            }

            cause = getCCauseBase(cause, causeList);
            return cause;
        }

        private CCauseBase getCCauseBase(CCauseBase cause, List<CCauseBase> causeList)
        {
            if (causeList.Count > 0)
            {
                causeList.Sort(new ComparerByIndex());
                cause = causeList[0];
                for (int i = 0; i < causeList.Count; i++)
                {
                    CCauseBase ccause = causeList[i];
                    if (i == 0)
                    {
                        ccause.PrevCause = null;
                    }
                    else
                    {
                        ccause.PrevCause = causeList[i - 1];
                    }

                    if (i == causeList.Count - 1)
                    {
                        ccause.NextCause = null;
                    }
                    else
                    {
                        ccause.NextCause = causeList[i + 1];
                    }
                }
            }

            return cause;
        }

        private int getParamCauseSN(Dictionary<string, object> param, string causeName, ref int lastInValidSN)
        {
            int sn = 1;
            foreach (string strKey in param.Keys)
            {
                if (strKey == causeName)
                {
                    return sn;
                }
                sn++;
            }

            return ++lastInValidSN;
        }
        public class ComparerByIndex : IComparer<CCauseBase>
        {
            public int Compare(CCauseBase x, CCauseBase y)
            {
                return x.SN.CompareTo(y.SN);
            }
        }
        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BackgroundStat"] = BackgroundStat;
                param["SceneCauseCond"] = getCondParam(scAnaNormal.SceneCause);
                return param;
            }
            set
            {
                if (value == null || value.Count <= 0)
                {
                    return;
                }
                Dictionary<string, object> param = value;
                if (param.ContainsKey("BackgroundStat"))
                {
                    BackgroundStat = (bool)param["BackgroundStat"];
                }
                if (param.ContainsKey("SceneCauseCond"))
                {
                    scAnaNormal.SceneCause = getCondByParam(scAnaNormal.SceneCause, param["SceneCauseCond"] as Dictionary<string, object>);
                }
            }
        }

        public override PropertiesControl Properties
        {
            get
            {
                return new CommonSimpleProperties(this, showFuncCondSetDlg);
            }
        }
        protected override void getFilesForAnalyse()
        {
            BackgroundFuncQueryManager.GetInstance().GetFile_RoadNew(GetSubFuncID(), ServiceTypeString, ((int)carrierID).ToString());
        }
        protected override void saveBackgroundData()
        {
            dosomethingAfterAnalyse();

            int subFuncId = GetSubFuncID();
            Dictionary<int, List<BackgroundResult>> bgResultDic = new Dictionary<int, List<BackgroundResult>>();
            foreach (FileInfo file in condition.FileInfos)
            {
                bgResultDic[file.ID] = new List<BackgroundResult>();
            }

            foreach (CSFBBlockCallSummaryInfo item in nameSumaryDic.Values)
            {
                foreach (CSFBBlockCallInfo info in item.BlockCallInfoLst)
                {
                    BackgroundResult result = info.ConvertToBackgroundResult();
                    result.SubFuncID = subFuncId;
                    result.ProjectString = BackgroundFuncBaseSetting.GetInstance().projectType;

                    List<BackgroundResult> bgResults;
                    if (bgResultDic.TryGetValue(info.Ecsfbrequest.FileID, out bgResults))
                    {
                        bgResults.Add(result);
                    }
                }
            }

            foreach (FileInfo file in condition.FileInfos)
            {
                BackgroundFuncQueryManager.GetInstance().SaveResult_Road(subFuncId, file, bgResultDic[file.ID]);
            }
            nameSumaryDic.Clear();
            totalNum = 0;
        }
        protected override void initBackgroundImageDesc()
        {
            List<NPOIRow> resultRows = new List<NPOIRow>();

            #region 表头
            NPOIRow rowTitle = new NPOIRow();
            rowTitle.AddCellValue("文件名称");
            rowTitle.AddCellValue("CSFB Request时间");
            rowTitle.AddCellValue("异常事件发生时间");
            rowTitle.AddCellValue("中心经度");
            rowTitle.AddCellValue("中心纬度");
            rowTitle.AddCellValue("道路");
            rowTitle.AddCellValue("主叫小区中文名");
            rowTitle.AddCellValue("主叫小区CI");
            rowTitle.AddCellValue("主叫LAC/TAC");
            rowTitle.AddCellValue("主叫频点");
            rowTitle.AddCellValue("主叫PCI/BSIC");
            rowTitle.AddCellValue("主叫电平");
            rowTitle.AddCellValue("主叫质量");
            rowTitle.AddCellValue("被叫小区中文名");
            rowTitle.AddCellValue("被叫小区CI");
            rowTitle.AddCellValue("被叫LAC/TAC");
            rowTitle.AddCellValue("被叫频点");
            rowTitle.AddCellValue("被叫PCI/BSIC");
            rowTitle.AddCellValue("被叫电平");
            rowTitle.AddCellValue("被叫质量");
            rowTitle.AddCellValue("主/被叫");
            rowTitle.AddCellValue("事件名称");
            rowTitle.AddCellValue("原因");
            rowTitle.AddCellValue("原因描述");
            rowTitle.AddCellValue("整改建议");
            rowTitle.AddCellValue("现象");
            rowTitle.AddCellValue("备注");
            resultRows.Add(rowTitle);
            #endregion

            foreach (BackgroundResult bgResult in BackgroundResultList)
            {
                #region 填充表内容
                NPOIRow row = new NPOIRow();
                row.AddCellValue(bgResult.FileName);
                row.AddCellValue(bgResult.DateTimeBeginString);
                row.AddCellValue(bgResult.DateTimeEndString);
                row.AddCellValue(bgResult.LongitudeMid);
                row.AddCellValue(bgResult.LatitudeMid);
                row.AddCellValue(bgResult.RoadDesc);
                row.AddCellValue(bgResult.CellIDDesc);
                row.AddCellValue(bgResult.GetImageValueString());
                row.AddCellValue(bgResult.GetImageValueString());
                row.AddCellValue(bgResult.GetImageValueString());
                row.AddCellValue(bgResult.GetImageValueString());
                row.AddCellValue(bgResult.GetImageValueString());
                row.AddCellValue(bgResult.GetImageValueString());
                row.AddCellValue(bgResult.GetImageValueString());
                row.AddCellValue(bgResult.GetImageValueString());
                row.AddCellValue(bgResult.GetImageValueString());
                row.AddCellValue(bgResult.GetImageValueString());
                row.AddCellValue(bgResult.GetImageValueString());
                row.AddCellValue(bgResult.GetImageValueString());
                row.AddCellValue(bgResult.GetImageValueString());
                row.AddCellValue(bgResult.StrDesc);

                row.AddCellValue(bgResult.GetImageValueString());
                row.AddCellValue(bgResult.GetImageValueString());
                row.AddCellValue(bgResult.GetImageValueString());
                row.AddCellValue(bgResult.GetImageValueString());
                row.AddCellValue(bgResult.GetImageValueString());
                row.AddCellValue(bgResult.GetImageValueString());
                resultRows.Add(row);
                #endregion
            }

            BackgroundNPOIRowResultDic.Clear();
            BackgroundNPOIRowResultDic[""] = resultRows;
        }
        #endregion
    }

    public class CSFBCauseQueryAna_FDD : CSFBCauseQueryAna
    {
        public CSFBCauseQueryAna_FDD(MainModel mainModel)
            : base(mainModel)
        {

        }
        public override string Name
        {
            get
            {
                return "LTE_FDD CSFB未接通原因分析";
            }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 26000, 26072, this.Name);
        }
    }

    public class CSFBBlockCallSummaryInfo
    {
        public string Reason { get; set; }
        public int Num { get; set; }
        public float Percent { get; set; }
        public List<CSFBBlockCallInfo> BlockCallInfoLst { get; set; }

        public CSFBBlockCallSummaryInfo(string reasonName)
        {
            this.Reason = reasonName;
            this.Num = 0;
            this.Percent = 0;
            this.BlockCallInfoLst = new List<CSFBBlockCallInfo>();
        }

        public void AddBlockCall(CSFBBlockCallInfo info)
        {
            this.BlockCallInfoLst.Add(info);
            this.Num++;
        }
    }

    public class CSFBBlockCallInfo
    {
        public CSFBBlockCallInfo(Event csfbrequestEvt)
        {
            this.Ecsfbrequest = csfbrequestEvt;
            this.EvtList.Add(csfbrequestEvt);
        }
        public List<Event> EvtList { get; set; } = new List<Event>();
        public Event Ecsfbrequest { get; set; }
        public Event Eblockcall { get; set; }
        public Event Ecsfbfailure { get; set; }
        public CScene Scene { get; set; }
        public CCauseBase CCause { get; set; }

        private ICell cell = null;
        public ICell CurCell
        {
            get
            {
                if (cell == null)
                {
                    cell = Eblockcall.GetSrcCell();
                }
                return cell;
            }
        }

        public string RoadName
        {
            get { return Eblockcall.RoadPlaceDesc; }
        }

        public string MtMo
        {
            get
            {
                switch (Eblockcall.MoMtFlag)
                {
                    case (int)EMoMt.Mo:
                        return "主叫";
                    case (int)EMoMt.Mt:
                        return "被叫";
                    default:
                        break;
                }
                return "";
            }
        }

        public string ReasonName
        {
            get { return CCause.GetCauseName(); }
        }

        public string ReasonDesc
        {
            get { return CCause.Cond.GetCauseDesc(); }
        }

        public string Suggestion
        {
            get { return CCause.Cond.GetCauseSuguest(); }
        }

        public string CSFB_Failure_Time
        {
            get { return Ecsfbfailure.DateTime.ToString(); }
        }

        public string CSFB_Request_Time
        {
            get { return Ecsfbrequest.DateTime.ToString(); }
        }

        public double CenterLongitude
        {
            get { return Eblockcall.Longitude; }
        }

        public double CenterLatitude
        {
            get { return Eblockcall.Latitude; }
        }

        public string FileName
        {
            get { return Eblockcall.FileName; }
        }

        public string EventTime
        {
            get { return Eblockcall.DateTime.ToString(); }
        }

        public string EventName
        {
            get { return Eblockcall.Name; }
        }

        private EFall netType = EFall.GSM;
        private string cellName = "";
        public string MoCellName
        {
            get 
            {
                if (Eblockcall.MoMtFlag == (int)EMoMt.Mo)
                {
                    return cellName;
                }
                return ""; 
            }
        }

        private string ci;
        public string MoCI
        {
            get
            {
                if (Eblockcall.MoMtFlag == (int)EMoMt.Mo)
                {
                    return ci;
                }
                return "";
            }
        }

        private string lac;
        public string MoLAC
        {
            get
            {
                if (Eblockcall.MoMtFlag == (int)EMoMt.Mo)
                {
                    return lac;
                }
                return "";
            }
        }

        private string freq;
        public string MoFreq
        {
            get
            {
                if (Eblockcall.MoMtFlag == (int)EMoMt.Mo)
                {
                    return freq;
                }
                return "";
            }
        }

        private string pci = "";
        public string MoPCI
        {
            get
            {
                if (Eblockcall.MoMtFlag == (int)EMoMt.Mo)
                {
                    return pci;
                }
                return "";
            }
        }

        private string rsrp = "";
        public string MoRsrp
        {
            get
            {
                if (Eblockcall.MoMtFlag == (int)EMoMt.Mo)
                {
                    return rsrp;
                }
                return "";
            }
        }

        private string qual = "";
        public string MoQual
        {
            get
            {
                if (Eblockcall.MoMtFlag == (int)EMoMt.Mo)
                {
                    return qual;
                }
                return "";
            }
        }

        public string MtCellName
        {
            get
            {
                if (Eblockcall.MoMtFlag == (int)EMoMt.Mt)
                {
                    return cellName;
                }
                return "";
            }
        }

        public string MtCI
        {
            get
            {
                if (Eblockcall.MoMtFlag == (int)EMoMt.Mt)
                {
                    return ci;
                }
                return "";
            }
        }

        public string MtLAC
        {
            get
            {
                if (Eblockcall.MoMtFlag == (int)EMoMt.Mt)
                {
                    return lac;
                }
                return "";
            }
        }

        public string MtFreq
        {
            get
            {
                if (Eblockcall.MoMtFlag == (int)EMoMt.Mt)
                {
                    return freq;
                }
                return "";
            }
        }

        public string MtPCI
        {
            get
            {
                if (Eblockcall.MoMtFlag == (int)EMoMt.Mt)
                {
                    return pci;
                }
                return "";
            }
        }

        public string MtRsrp
        {
            get
            {
                if (Eblockcall.MoMtFlag == (int)EMoMt.Mt)
                {
                    return rsrp;
                }
                return "";
            }
        }

        public string MtQual
        {
            get
            {
                if (Eblockcall.MoMtFlag == (int)EMoMt.Mt)
                {
                    return qual;
                }
                return "";
            }
        }

        public string Phenomenon
        {
            get
            {
                return CCause.Cond.GetPhenomenon();
            }
        }

        private string remark;
        public string Remark
        {
            get
            {
                if (remark == null)
                {
                    remark = string.Format("FailureTime${0}$DistrictID${1}$FileID${2}$EvtID${3}$SeqID${4}$ServiceID${5}",
                        CSFB_Failure_Time, Eblockcall.DistrictID, Eblockcall.FileID, Eblockcall.ID, Eblockcall.SN, Eblockcall.ServiceType);
                }
                return remark;
            }
        }

        public void GetCellInfo(DTFileDataManager file)
        {
            ICell curCell = Eblockcall.GetSrcCell();
            if (curCell == null)
            {
                netType = EFall.GSM;
                lac = Convert.ToString(Eblockcall["LAC"]);
                ci = Convert.ToString(Eblockcall["CI"]);
                cellName = string.Format("{0}_{1}", lac, ci);
                freq = "";
                pci = "";
            }
            else if(curCell is Cell)
            {
                Cell gsmCell = curCell as Cell;
                netType = EFall.GSM;
                cellName = curCell.Name;
                lac = Convert.ToString(gsmCell.LAC);
                ci = Convert.ToString(gsmCell.CI);
                freq = Convert.ToString(gsmCell.BCCH);
                pci = Convert.ToString(gsmCell.BSIC);
            }
            else if (curCell is TDCell)
            {
                TDCell tdCell = curCell as TDCell;
                netType = EFall.TD;
                cellName = curCell.Name;
                lac = Convert.ToString(tdCell.LAC);
                ci = Convert.ToString(tdCell.CI);
                freq = Convert.ToString(tdCell.FREQ);
                pci = Convert.ToString(tdCell.CPI);
            }
            else if (curCell is WCell)
            {
                WCell wCell = curCell as WCell;
                netType = EFall.W;
                cellName = curCell.Name;
                lac = Convert.ToString(wCell.LAC);
                ci = Convert.ToString(wCell.CI);
                freq = Convert.ToString(wCell.UARFCN);
                pci = Convert.ToString(wCell.PSC);
            }
            else if (curCell is LTECell)
            {
                LTECell lteCell = curCell as LTECell;
                netType = EFall.LTE;
                cellName = curCell.Name;
                lac = Convert.ToString(lteCell.TAC);
                ci = Convert.ToString(lteCell.ECI);
                freq = Convert.ToString(lteCell.EARFCN);
                pci = Convert.ToString(lteCell.PCI);
            }
            getRsrpQual(file);
        }

        private void getRsrpQual(DTFileDataManager file)
        {
            foreach (TestPoint tp in file.TestPoints)
            {
                if (tp.DateTime < Eblockcall.DateTime)
                    continue;

                switch (netType)
                {
                    case EFall.GSM:
                        rsrp = Convert.ToString(tp["lte_gsm_DM_RxLevSub"]);
                        qual = Convert.ToString(tp["lte_gsm_DM_RxQualSub"]);
                        break;
                    case EFall.TD:
                        rsrp = Convert.ToString(tp["lte_td_DM_PCCPCH_RSCP"]);
                        qual = Convert.ToString(tp["lte_td_DM_PCCPCH_SIR"]);
                        break;
                    case EFall.LTE:
                        rsrp = Convert.ToString(tp["lte_RSRP"]);
                        qual = Convert.ToString(tp["lte_SINR"]);
                        break;
                    case EFall.W:
                        rsrp = Convert.ToString(tp["lte_fdd_wcdma_TotalRSCP"]);
                        qual = Convert.ToString(tp["lte_fdd_wcdma_TotalEc_Io"]);
                        break;
                    default:
                        break;
                }
                break;
            }
        }

        public BackgroundResult ConvertToBackgroundResult()
        {
            BackgroundResult bgResult = new BackgroundResult();
            bgResult.FileID = Eblockcall.FileID;
            bgResult.FileName = Eblockcall.FileName;
            bgResult.ISTime = Ecsfbrequest.Time;
            bgResult.IETime = Eblockcall.Time;
            bgResult.LongitudeStart = Ecsfbrequest.Longitude;
            bgResult.LatitudeStart = Ecsfbrequest.Latitude;
            bgResult.LongitudeMid = CenterLongitude;
            bgResult.LatitudeMid = CenterLatitude;

            bgResult.RoadDesc = RoadName;
            bgResult.StrDesc = MtMo;
            bgResult.CellIDDesc = MoCellName;

            bgResult.AddImageValue(MoCI);
            bgResult.AddImageValue(MoLAC);
            bgResult.AddImageValue(MoFreq);
            bgResult.AddImageValue(MoPCI);
            bgResult.AddImageValue(MoRsrp);
            bgResult.AddImageValue(MoQual);

            bgResult.AddImageValue(MtCellName);
            bgResult.AddImageValue(MtCI);
            bgResult.AddImageValue(MtLAC);
            bgResult.AddImageValue(MtFreq);
            bgResult.AddImageValue(MtPCI);
            bgResult.AddImageValue(MtRsrp);
            bgResult.AddImageValue(MtQual);

            bgResult.AddImageValue(EventName);
            bgResult.AddImageValue(ReasonName);
            bgResult.AddImageValue(ReasonDesc);
            bgResult.AddImageValue(Suggestion);
            bgResult.AddImageValue(Phenomenon);
            bgResult.AddImageValue(Remark);
            return bgResult;
        }
    }

    public enum EFall
    {
        GSM,
        TD,
        LTE,
        W,
    }

    public enum ECSFBRequest
    {
        MO_CSFB_Request = 877,
        MT_CSFB_request = 885,
        FDD_MO_CSFB_Request = 3070,
        FDD_MT_CSFB_Request = 3071,
    }

    public enum EBlockCall
    {
        CSFB_MO_Block_Call = 1008,
        CSFB_MT_Block_Call = 1009,
        FDD_CSFB_MO_Block_Call = 3008,
        FDD_CSFB_MT_Block_Call = 3009,
        //GSM_MO_Block_Call = 1028,
        //GSM_MT_Block_Call = 1029,
        //TD_MO_Block_Call = 1048,
        //TD_MT_Block_Call = 1049,
        //TD_GSM_MO_Block_Call = 1058,
        //TD_GSM_MT_Block_Call = 1059,
    }

    public enum ECSFBFailure
    {
        MO_CSFB_Failure = 882,
        MT_CSFB_Failure = 890,
        FDD_MO_CSFB_Failure = 3080,
        FDD_MT_CSFB_Failure = 3081,
    }

    public enum ECSFBProceeding
    {
        MO_CSFB_Proceeding = 880,
        MT_CSFB_Proceeding = 888,
        MO_CSFB_回落失败 = 1068,
        MT_CSFB_回落失败 = 1069,
        FDD_MO_CSFB_Proceeding = 3076,
        FDD_MT_CSFB_Proceeding = 3077,
        FDD_MO_CSFB_回落失败 = 3068,
        FDD_MT_CSFB_回落失败 = 3069,
    }

    public enum EMessage
    {
        CM_service_requestGSM = 1316,
        CM_service_requestTD = 1899627812,
        Authentication_RequestGSM = 1298,
        Authentication_RequestTD = 1899627794,
        Authentication_RequestLTE = 1097533266,

        Authentication_Response = 1300,
        Channel_Release = 1549,
        setupGSM = 773,
        setupLTE = 1899627269,
        Assignment_complete = 1577,
        AlertingGSM = 769,
        AlertingTD = 1899627265,

        Extended_Service_Request = 1097533260,
        RRCConnectionRelease = **********,


        Service_reject = **********,
        CM_Service_Abort = 1315,
        CM_Service_Abort_LTE = **********,
        IMMEDIATE_ASSIGNMENT_REJECT = 1594,

        Paging = **********,
        Paging_Response = 1575,
        No_Service = **********,

        Location_Updating_Accept = 1282,
        Location_Updating_Accept_LTE = **********,
        Location_Updating_Reject = 1284,
        Location_Updating_Reject_LTE = **********,
        Location_Updating_Request = 1288,
        Routing_area_update_Request = 2056,

        Call_Proceeding_GSM = 770,
        Call_Proceeding = **********,
        Tracking_area_update_request = **********,

        Disconnect = 805,
        Disconnect_LTE = **********,

        Release_Complete = 801,
        Release_Complete_LTE = **********,

        Release = 813,
        Release_LTE = **********,

        GMM_Service_Request = 2060,
        GMM_Service_Request_LTE = **********,
        Tracking_Area_Update_Reject = **********,
    }

    public enum EEvent
    {
        MO_CSFB_Proceeding = 880,
        MT_CSFB_Proceeding = 888,
        MT_CSFB_Coverage = 887,
        CSFB_MO_Block_Call = 1008,
        CSFB_MT_Block_Call = 1009,
        FDD_MO_CSFB_Proceeding = 3076,
        FDD_MT_CSFB_Proceeding = 3077,
        FDD_MT_CSFB_Coverage = 3075,
        FDD_CSFB_MO_Block_Call = 3008,
        FDD_CSFB_MT_Block_Call = 3009,
    }

    public enum EHandOverSuccess
    {
        Handover_Success = 17,
        TD_HandoverSuccess_T2G = 142,
        TD_HandoverSuccess_IntraT = 145,
        TD_HandoverSuccess_Baton = 148,
        TD_HandoverSuccess_IntraG = 151,
        TD_HandoverSuccess_RBReconfigT = 232,
        Intra_Handover_Success = 851,
        Inter_Handover_Success = 899,
        Handover_Success900_1800 = 981,
        Handover_Success1800_900 = 982,
        GSM_Handover_Success = 1039,
        WCDMA_HandoverSuccess_W2G=3141,
        WCDMA_HandoverSuccess_IntraW=3144,
        WCDMA_HandoverSuccess_Soft=3147,
        WCDMA_HandoverSuccess_IntraG=3150,
        FDD_Intra_Handover_Success = 3156,
        FDD_Inter_Handover_Success = 3159,
        FDD_GSM_Handover_Success = 3138,
    }

    public enum EMoMt
    {
        Unknow,
        Mo = 1,
        Mt = 2,
    }

    public enum ELocationUpdateSuccess
    {
        Location_Area_Update_Success = 20,
        TD_LocationUpdate_Success = 126,
        TD_GSM_LocationUpdate_Success = 129,
        LTE_TD_LocationUpdate_Success = 1111,
        LTE_GSM_LocationUpdate_Success = 1114,
        FDD_LTE_GSM_LocationUpdate_Success = 3114,
        WCDMA_LocationUpdate_Success = 3175
    }

    public class COutParam
    {
        private ICell cell = null;
        private int? lac = null;
        private int? ci = null;
        private float? rxlev = null;

        public void Fill(ICell cell, int? lac, int? ci, float? rxlev)
        {
            this.cell = cell;
            this.lac = lac;
            this.ci = ci;
            this.rxlev = rxlev;
        }

        public string CellName
        {
            get
            {
                if (cell != null)
                {
                    return cell.Name;
                }
                else if (lac == null || ci == null)
                {
                    return "";
                }
                return string.Format("{0}_{1}", lac, ci);
            }
        }

        public string Lac
        {
            get
            {
                if (lac != null)
                {
                    return Convert.ToString(lac);
                }
                return "";
            }
        }

        public string RxlevStr
        {
            get
            {
                if (rxlev != null)
                {
                    return Convert.ToString(rxlev);
                }
                return "";
            }
        }
    }
}
