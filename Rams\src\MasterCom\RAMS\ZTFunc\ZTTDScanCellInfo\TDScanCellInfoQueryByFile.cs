﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class TDScanCellInfoQueryByFile : DIYReplayFileQuery
    {
        private readonly TDScanCellInfoStater stater;
        public TDScanCellInfoQueryByFile(MainModel mainModel)
            : base(mainModel)
        {
            IsAddSampleToDTDataManager = false;
            IsAddMessageToDTDataManager = false;
            isAutoLoadCQTPicture = false;
            stater = new TDScanCellInfoStater();
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 16000, 16025, this.Name);
        }

        protected override DIYReplayContentOption getDIYReplayContent()
        {
            DIYReplayContentOption option = new DIYReplayContentOption();
            option.MessageInclude = false;
            option.EventInclude = false;
            option.DefaultSerialThemeName = "TDSCAN_PCCPCH_RSCP";

            addSampleColumns(option, "isampleid");
            addSampleColumns(option, "itime");
            addSampleColumns(option, "ilongitude");
            addSampleColumns(option, "ilatitude");
            addSampleColumns(option, "TDS_PCCPCH_Channel");
            addSampleColumns(option, "TDS_PCCPCH_CPI");
            addSampleColumns(option, "TDS_PCCPCH_RSCP");
            addSampleColumns(option, "TDS_PCCPCH_SIR");
            addSampleColumns(option, "TDS_PCCPCH_RSSI");
            addSampleColumns(option, "TDS_PCCPCH_C_I");
            return option;
        }

        protected void addSampleColumns(DIYReplayContentOption option, string name)
        {
            List<ColumnDefItem> columns = InterfaceManager.GetInstance().GetColumnDefByShowName(name);
            if (columns != null && columns.Count > 0)
            {
                option.SampleColumns.AddRange(columns);
            }
        }

        protected override void doWithDTData(TestPoint tp)
        {
            stater.StatTestPoint(tp);
        }

        protected override void doPostReplayAction()
        {
            List<TDScanCellInfo> cellInfoList = stater.GetStatResult();
            stater.Clear();

            TDScanCellInfoResultForm form = MainModel.GetObjectFromBlackboard(typeof(TDScanCellInfoResultForm).FullName) as TDScanCellInfoResultForm;
            if (form == null || form.IsDisposed)
            {
                form = new TDScanCellInfoResultForm(MainModel);
            }
            form.FillData(cellInfoList);
            form.Show(MainModel.MainForm);
        }

        protected override bool isValidPoint(double jd, double wd)
        {
            if (MainModel.SearchGeometrys.Region != null)
            {
                return MainModel.SearchGeometrys.GeoOp.Contains(jd, wd);
            }
            return true;
        }
    }

    public class WCDMAScanCellInfoQueryByFile : DIYReplayFileQuery
    {
        private readonly WScanCellInfoStater stater;
        public WCDMAScanCellInfoQueryByFile(MainModel mainModel)
            : base(mainModel)
        {
            IsAddSampleToDTDataManager = false;
            IsAddMessageToDTDataManager = false;
            isAutoLoadCQTPicture = false;
            stater = new WScanCellInfoStater();
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 32000, 32012, this.Name);
        }

        protected override DIYReplayContentOption getDIYReplayContent()
        {
            DIYReplayContentOption option = new DIYReplayContentOption();
            option.MessageInclude = false;
            option.EventInclude = false;
            option.DefaultSerialThemeName = "WS_CPICHTotalRSCP";

            addSampleColumns(option, "isampleid");
            addSampleColumns(option, "itime");
            addSampleColumns(option, "ilongitude");
            addSampleColumns(option, "ilatitude");
            addSampleColumns(option, "WS_CPICHChannel");
            addSampleColumns(option, "WS_CPICHPilot");
            addSampleColumns(option, "WS_CPICHTotalRSCP");
            addSampleColumns(option, "WS_CPICHSIR");
            return option;
        }

        protected void addSampleColumns(DIYReplayContentOption option, string name)
        {
            List<ColumnDefItem> columns = InterfaceManager.GetInstance().GetColumnDefByShowName(name);
            if (columns != null && columns.Count > 0)
            {
                option.SampleColumns.AddRange(columns);
            }
        }

        protected override void doWithDTData(TestPoint tp)
        {
            stater.StatTestPoint(tp);
        }

        protected override void doPostReplayAction()
        {
            List<WScanCellInfo> cellInfoList = stater.GetStatResult();
            stater.Clear();

            WScanCellInfoResultForm form = MainModel.GetObjectFromBlackboard(typeof(WScanCellInfoResultForm).FullName) as WScanCellInfoResultForm;
            if (form == null || form.IsDisposed)
            {
                form = new WScanCellInfoResultForm(MainModel);
            }
            form.FillData(cellInfoList);
            form.Show(MainModel.MainForm);
        }

        protected override bool isValidPoint(double jd, double wd)
        {
            if (MainModel.SearchGeometrys.Region != null)
            {
                return MainModel.SearchGeometrys.GeoOp.Contains(jd, wd);
            }
            return true;
        }
    }
}
