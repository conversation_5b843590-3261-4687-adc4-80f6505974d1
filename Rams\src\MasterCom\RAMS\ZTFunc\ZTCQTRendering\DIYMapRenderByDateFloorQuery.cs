﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Func
{
    class DiyMapRenderByDateFloorQuery : DIYSQLBase
    {
        readonly MapRenderBaseForm frm;
        readonly CqtSettingConditionInfos cqtCondition;

        public DiyMapRenderByDateFloorQuery(MainModel mainModel, CqtSettingConditionInfos condition, MapRenderBaseForm frm)
            : base(mainModel)
        {
            this.frm = frm;
            cqtCondition = condition;
            MainDB = true;
        }

        public override string Name
        {
            get { return "自定义"; }
        }

        public override string IconName
        {
            get { return "Images/regionq.gif"; }
        }

        readonly List<CqtRenderingBaseInfo> cqtRenderingInfos = new List<CqtRenderingBaseInfo>();
        public List<CqtRenderingBaseInfo> CQTRenderingInfos
        {
            get { return cqtRenderingInfos; }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override void query()
        {
            WaitBox.CanCancel = true;
            WaitBox.Text = "正在查询自定义...";
            ClientProxy clientProxy = new ClientProxy();
            try
            {
                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, userName, password, dbid > 0 ? dbid : MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }
                WaitBox.Show("正在查询自定义...", queryInThread, clientProxy);
                fireShowForm();
            }
            finally
            {
                clientProxy.Close();
            }
        }

        protected override string getSqlTextString()
        {
            StringBuilder sb = new StringBuilder(string.Format(@"select a.time,a.floorName,a.point_x,a.point_y,
a.sampleNum,a.rsrpAvg,a.sinrAvg from tb_bt_sample_stat_floor_dd a where a.time between '{0}' and '{1}' ",
            cqtCondition.Period.BeginTime, cqtCondition.Period.EndTime));

            bool isFirst = true;
            foreach (string filter in cqtCondition.RenderingFilter)
            {
                if (isFirst)
                {
                    sb.Append("and a.floorName = '");
                    isFirst = false;
                }
                else
                {
                    sb.Append("or a.floorName = '");
                }
                sb.Append(filter);
                sb.Append("'");
            }

            return sb.ToString();
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[7];
            rType[0] = E_VType.E_String;
            rType[1] = E_VType.E_String;
            rType[2] = E_VType.E_Float;
            rType[3] = E_VType.E_Float;
            rType[4] = E_VType.E_Int;
            rType[5] = E_VType.E_Float;
            rType[6] = E_VType.E_Float;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    CqtRenderByDateFloorInfo info = CqtRenderByDateFloorInfo.Fill(package.Content);
                    cqtRenderingInfos.Add(info);
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        protected override void queryInThread(object o)
        {
            cqtRenderingInfos.Clear();
            base.queryInThread(o);
            WaitBox.Close();
        }

        private void fireShowForm()
        {
            frm.DealDataAfterQuery(cqtRenderingInfos);
            frm.Visible = true;
            frm.BringToFront();
        }
    }

    public class CqtRenderByDateFloorInfo : CqtRenderingBaseInfo
    {
        public int SampleNum { get; set; }
        public float RSRPAvg { get; set; }
        public float SINRAvg { get; set; }

        public new static CqtRenderByDateFloorInfo Fill(Content content)
        {
            CqtRenderByDateFloorInfo info = new CqtRenderByDateFloorInfo();
            info.Time = content.GetParamString();
            info.FloorName = content.GetParamString();
            info.Point_x = content.GetParamFloat();
            info.Point_y = content.GetParamFloat();
            info.SampleNum = content.GetParamInt();
            info.RSRPAvg = content.GetParamFloat();
            info.SINRAvg = content.GetParamFloat();
            return info;
        }
    }
}
