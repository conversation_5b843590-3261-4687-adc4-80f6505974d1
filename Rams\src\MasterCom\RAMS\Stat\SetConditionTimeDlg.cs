﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.Stat
{
    public partial class SetConditionTimeDlg : BaseDialog
    {
        public SetConditionTimeDlg()
        {
            InitializeComponent();
        }

        public void SetConditionTime(string ElapseFirst, int ElapseDay)
        {
            dateTimeElapseFirst.Value = DateTime.Parse(ElapseFirst);
            numElapseDay.Value = ElapseDay;
        }

        public void GetConditionTime(out string ElapseFirst, out int ElapseDay)
        {
            ElapseFirst = dateTimeElapseFirst.Value.Date.ToString("yyyy/MM/dd");
            ElapseDay = int.Parse(numElapseDay.Value.ToString());
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}
