﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public partial class IntegritySettingDlg : BaseDialog
    {
        private IntegrityTestCondition cond;

        public IntegritySettingDlg()
        {
            InitializeComponent();

            cond = new IntegrityTestCondition();
        }

        public void SetCondition(IntegrityTestCondition condition)
        {
            this.cond = condition;
            this.spinEditPermeate.Value = condition.Permeate;
            this.spinEditTimes.Value = condition.TestTimes;
        }

        public IntegrityTestCondition GetCondition()
        {
            cond.SetContext((int)spinEditTimes.Value, (int)spinEditPermeate.Value, 
                labelAchieve.BackColor, labelUnAchieve.BackColor);

            return cond;
        }

        private void simpleButtonOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void simpleButtonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }

    public class IntegrityTestCondition : AreaTestCondition
    {
        public int TestTimes { get; set; } = 2;

        public void SetContext(int times, int permeate, Color colorAch, Color colorUnAch)
        {
            this.TestTimes = times;
            base.SetContext(permeate, colorAch, colorUnAch);
        }

        public override void CheckAchieve(CPermeate permeate, bool isVillage)
        {
            if (isVillage)
                permeate.BAchieve = permeate.IValidCnt > 0;
            else
                permeate.BAchieve = permeate.DPermeate >= Permeate;
        }
    }
}
