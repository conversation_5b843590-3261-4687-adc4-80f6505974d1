﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTRegionKPISQLQuery : ZTRegionKQISQLQuery
    {
        public ZTRegionKPISQLQuery(MainModel mainModel)
            : base(mainModel)
        { 
            
        }

        private readonly List<RegionKPIItem> regionkpis = new List<RegionKPIItem>();
        public List<RegionKPIItem> RegionKPIs
        {
            get { return regionkpis; }
        }
        protected override string getSqlTextString()
        {
            return "select  KPIName,KPIValue,KPIScore from Complain_Sys..tb_QoE_area_KPI_score where keyName='" + GridName + "' and beginTime='" + BeginDate + "' and endTime='" + EndDate + "' and reportName='" + ReportName + "'";
        }

        protected override MasterCom.RAMS.Model.Interface.E_VType[] getSqlRetTypeArr()
        {
            E_VType[] vtypes = new E_VType[3];
            vtypes[0] = E_VType.E_String;
            vtypes[1] = E_VType.E_Float;
            vtypes[2] = E_VType.E_Float;
            return vtypes;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            regionkpis.Clear();
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    RegionKPIItem kpiitem = new RegionKPIItem();
                    kpiitem.IndicatorName = package.Content.GetParamString();
                    kpiitem.IndicatorValue = package.Content.GetParamFloat().ToString();
                    kpiitem.Score = package.Content.GetParamFloat().ToString();
                    this.regionkpis.Add(kpiitem);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    break;
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    break;
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        public override string Name
        {
            get { return ""; }
        }
    }
}
