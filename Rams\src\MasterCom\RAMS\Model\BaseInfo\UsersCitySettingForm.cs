﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using DevExpress.XtraTreeList.Nodes;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Stat;
using MasterCom.Util.DevControlManager;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.Model.BaseInfo
{
    public partial class UsersCitySettingForm : BaseForm
    {
        public UsersCitySettingForm()
            : base()
        {
            InitializeComponent();
            TreeListHelper.ThreeStateControl(treeListCities);
            initData();
        }

        private List<User> users = null;
        private List<IDNamePair> citys = null;
        private void initData()
        {
            if (!MainModel.PermissionManager.HasQueriedUsers)
            {
                QueryUsers queryUser = new QueryUsers(MainModel);
                queryUser.Query();
                users = queryUser.UserList;
                MainModel.PermissionManager.Users = users;
            }
            else
            {
                users = MainModel.PermissionManager.Users;
            }

            QueryUserCityRight cityRight = new QueryUserCityRight(users);
            cityRight.Query();
            citys = DistrictManager.GetInstance().GetAvailableDistrict();

            treeListCities.Nodes.Clear();
            foreach(IDNamePair pair in citys)
            {
                TreeListNode root = treeListCities.AppendNode(new object[]
                {
                    pair.Name
                } , null);
                root.Tag = pair;
            }

            gvUser.SelectionChanged += gvUser_SelectionChanged;
            gridCtrlUser.DataSource = users;
            gridCtrlUser.RefreshDataSource();
            treeListCities.DataSource = citys;
            treeListCities.RefreshDataSource();
            treeListCities.AfterCheckNode += treeListCities_AfterCheckNode;
        }

        private List<User> modifiedUsers=new List<User>(); 

        private void treeListCities_AfterCheckNode(object sender , DevExpress.XtraTreeList.NodeEventArgs e)
        {
            if(curSelUser == null)
            {
                return;
            }
            IDNamePair city = e.Node.Tag as IDNamePair;
            if(city == null)
            {
                return;
            }
            curSelUser.UpdateCityRight(city.id , e.Node.Checked);
            if (!modifiedUsers.Contains(curSelUser))
            {
                modifiedUsers.Add(curSelUser);
            }
        }

        private User curSelUser = null;

        void gvUser_SelectionChanged(object sender, DevExpress.Data.SelectionChangedEventArgs e)
        {
            int[] rows = gvUser.GetSelectedRows();
            if(rows.Length == 0)
            {
                curSelUser = null;
                return;
            }

            curSelUser = gvUser.GetRow(rows[0]) as User;

            foreach(TreeListNode node in treeListCities.Nodes)
            {
                IDNamePair r = node.Tag as IDNamePair;
                if(r != null)
                {
                    node.Checked = curSelUser.CityIDs.Contains(r.id);
                }
            }
        }

        private void btnSubmit_Click(object sender, EventArgs e)
        {
            if(modifiedUsers.Count==0)
            {
                MessageBox.Show("无修改账号！");
                return;
            }
            WaitTextBox.Show("正在更新设置..." , submitInThread);
        }

        private void submitInThread()
        {
            try
            {
                UpdateUserCityRight update = new UpdateUserCityRight(modifiedUsers);
                update.Query();
            }
            finally
            {
                Thread.Sleep(1);
                WaitTextBox.Close();
            }
        }



    }
}
