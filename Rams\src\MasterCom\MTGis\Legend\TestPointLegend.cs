﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.MTGis
{
    public class TestPointLegend : LegendGroup<TestPoint>
    {
        public TestPointLegend()
        {
            this.Title = "采样点图例";
        }

        protected virtual bool getDisplayStyle(TestPoint tp, out Color color, out GraphicsPath shape)
        {
            color = Color.Empty;
            shape = null;
            if (Items == null)
            {
                return false;
            }
            foreach (LegendGroupSubItem<TestPoint> item in Items)
            {
                if (item.IsMatch(tp))
                {
                    color = item.Color;
                    shape = item.Path;
                    return item.Visible;
                }
            }
            return false;
        }

        static Pen PenSelected = new Pen(Color.Red, 2);
        public override void DrawOnLayer(MapOperation map, Graphics graphics, PointF location, TestPoint entity2Draw)
        {
            Color color;
            GraphicsPath path = null;

            if (!getDisplayStyle(entity2Draw, out color, out path))
            {
                return;
            }
            Brush brush = new SolidBrush(color);
            graphics.TranslateTransform(location.X, location.Y);
            graphics.FillPath(brush, path);
            if (entity2Draw.Selected)
            {
                graphics.DrawPath(PenSelected, path);
            }
            graphics.ResetTransform();
        }
    }

}
