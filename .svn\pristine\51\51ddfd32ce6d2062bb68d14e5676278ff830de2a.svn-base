﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using System.Drawing.Drawing2D;

namespace MasterCom.RAMS.NOP
{
    public partial class FlowDiagramPanel : UserControl
    {
        public event EventHandler SelectProcChanged;
        public event EventHandler CurDisplayRelationChanged;
        public event EventHandler SelectNodeChanged;

        private Font fontText = new Font(new FontFamily("宋体"), 10, FontStyle.Regular);
        public FlowDiagramPanel()
        {
            InitializeComponent();

            //设置双缓冲，减少闪烁
            this.SetStyle(ControlStyles.DoubleBuffer | ControlStyles.UserPaint
                | ControlStyles.AllPaintingInWmPaint, true);
        }

        ProcRelation procRelation = null;
        public ProcRelation ProcRelation
        {
            get { return procRelation; }
            set
            {
                procRelation = value;
                if (value == null)
                {
                    CurSelProc = null;
                }
                else
                {
                    CurSelProc = value.Proc;
                }
                if (CurDisplayRelationChanged != null)
                {
                    CurDisplayRelationChanged(this, EventArgs.Empty);
                }
                this.Invalidate();
            }
        }

        ProcRoutine curSelProc = null;
        public ProcRoutine CurSelProc
        {
            get
            {
                return curSelProc;
            }
            set
            {
                curSelProc = value;
                CurSelNode = null;
                updatePnlScrollSize();
                this.Invalidate();
            }
        }

        public ProcSequenceInfo CurProcSeq
        {
            get;
            set;
        }

        protected ProcRoutineManager curRoutineManager
        {
            get
            {
                if (this.ParentForm != null && this.ParentForm is TaskFlowDiagramForm)
                {
                    return ProcRoutineManager.Instance;
                }
                else
                {
                    return ProcRoutineManager2017.Instance;
                }
            }
        }

        public NodeEntry CurSelNode
        {
            get { return curSelNode; }
            set
            {
                curSelNode = value;
                if (SelectNodeChanged != null)
                {
                    SelectNodeChanged(this, EventArgs.Empty);
                }
            }
        }

        private void updatePnlScrollSize()
        {
            if (curSelProc != null)
            {
                AutoScrollMinSize = new Size((int)(curSelProc.RootNode.GetMaxSize().Width * zoomFactor)
                    , (int)(curSelProc.RootNode.GetMaxSize().Height * zoomFactor));
            }
            else
            {
                AutoScrollMinSize = new Size(100, 100);
            }
        }

        NodeEntry curSelNode = null;

        public ESResultInfo ESResultInfo
        {
            get;
            set;
        }
        Dictionary<NodeEntry, NodeEntry> passedNodeDic = new Dictionary<NodeEntry, NodeEntry>();


        #region this mouse event

        private void thisPanel_MouseClick(object sender, MouseEventArgs e)
        {
            /**
            int xx = (int)(e.X / zoomFactor);
            int yy = (int)(e.Y / zoomFactor);
            if (e.Button == MouseButtons.Right)
            {
                if (curSelNode != null)
                {
                    miNodePaste.Enabled = (clipboardNode != null);
                    ctxMenuNode.Show(this, e.X, e.Y);
                }
                else
                {
                    lastLocation = new Point(xx, yy);
                }
            }
            this.Focus();
            *///
        }

        private void thisPanel_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                if (curSelProc != null)
                {
                    Point absPnt = rel2Abs(new Point(e.X, e.Y));
                    CurSelNode = curSelProc.HitTest(absPnt);
                }
                this.Invalidate();
            }
        }

        private void thisPanel_MouseUp(object sender, MouseEventArgs e)
        {
            /**
            dragging = false;
            ctrlDrag = false;
            if (!btnModeAddConn.Checked)
            {
                updateEnvelope();

                curMouseHover = Point.Empty;
                this.Invalidate();
            }
            else//连接模式
            {
                if (curSelNode != null)
                {
                    NodeEntry tarNode = hitGetNodeEntry(e);
                    if (tarNode != null)
                    {
                        bool succ = connect2Nodes(curSelNode, tarNode);
                        if (succ)
                        {
                            curSelRoutine._HoverNodes.Remove(tarNode);
                        }
                    }
                    else
                    {
                        connTargetHoverPoint = Point.Empty;

                    }
                    this.Invalidate();
                }
            }
            *///
        }

        private void thisPanel_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (curSelNode == null)
            {
                return;
            }
            if (NodeType.OtherProc == curSelNode.Type)
            {
                ProcRoutine proc = getProc();
                //云南现场调试
                if (proc == null)
                {
                    MessageBox.Show(string.Format("未获取到流程{0}", curSelNode.DecideExp));
                }
                else if (proc.RootNode == null)
                {
                    StringBuilder sb = new StringBuilder();
                    sb.AppendFormat("流程{0}根节点为空，版本ID：{1}\n", curSelNode.DecideExp, proc.ID);
                    sb.AppendFormat("智能预判执行时间:{0}，流程图修改时间：{1}\n", ESResultInfo.CTime, proc.ModifyDateTime);
                    sb.AppendFormat("流程总数量:{0}\n", curRoutineManager.IdProcDic.Count);

                }
                ProcChangedEventArgs evt = new ProcChangedEventArgs();
                evt.OldProc = curSelProc;
                evt.CurProc = proc;
                this.CurSelProc = proc;
                if (SelectProcChanged != null)
                {
                    SelectProcChanged(this, evt);
                }
            }
        }

        private ProcRoutine getProc()
        {
            ProcRoutine proc = procRelation[curSelNode.DecideExp];
            if (proc == null)
            {
                List<ProcRoutine> pSet = null;
                if (curRoutineManager.NameProcDic.TryGetValue(curSelNode.DecideExp, out pSet))
                {
                    //在按照时间降序排序的集合中寻找第一个小于预判时间的版本即为当时运行的版本
                    proc = pSet.Find(r => r.ModifyDateTime.CompareTo(ESResultInfo.CTime) < 0);
                    if (proc == null)
                    {//没找到则默认取最新版本
                        proc = pSet[0];
                    }
                }
            }

            return proc;
        }

        private void thisPanel_MouseMove(object sender, MouseEventArgs e)
        {
            /**
            int xx = (int)(e.X / zoomFactor);
            int yy = (int)(e.Y / zoomFactor);
            if (dragging && curSelNode != null)
            {
                if (!btnModeAddConn.Checked)
                {
                    curSelNode.XPos = lastLocation.X + xx - clickLocation.X;
                    curSelNode.YPos = lastLocation.Y + yy - clickLocation.Y;
                    if ((Control.ModifierKeys & Keys.Control) == Keys.Control && ctrlDrag)
                    {
                        curSelNode.UpdateChildrenByOffsets(curSelNode.XPos, curSelNode.YPos);
                    }
                    this.Invalidate();
                }
                else//连接操作模式
                {
                    connTargetHoverPoint = new Point(xx, yy);
                    this.Invalidate();
                }
            }
            *///
        }

        #endregion

        float zoomFactor = 1.0f;

        /// <summary>
        /// 由相对坐标转换成绝对坐标点，绝对坐标为配置保存时的坐标
        /// </summary>
        /// <param name="pnt"></param>
        /// <returns></returns>
        public Point rel2Abs(Point pnt)
        {
            pnt.X = (int)((pnt.X - this.AutoScrollPosition.X) / zoomFactor);
            pnt.Y = (int)((pnt.Y - this.AutoScrollPosition.Y) / zoomFactor);
            return pnt;
        }

        public Rectangle rel2Abs(Rectangle rect)
        {
            rect.X = (int)((rect.X - this.AutoScrollPosition.X) / zoomFactor);
            rect.Y = (int)((rect.Y - this.AutoScrollPosition.Y) / zoomFactor);
            rect.Width = (int)(rect.Width / zoomFactor);
            rect.Height = (int)(rect.Height / zoomFactor);
            return rect;
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            passedNodeDic.Clear();
            if (curSelProc == null)
            {
                return;
            }

            try
            {
                Graphics g = e.Graphics;
                Point scrollPoint = this.AutoScrollPosition;
                g.TranslateTransform(scrollPoint.X, scrollPoint.Y);
                g.ScaleTransform(zoomFactor, zoomFactor);
                g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;

                Rectangle rect = rel2Abs(e.ClipRectangle);
                try
                {
                    drawEntryNode(rect, g, 0, 0, curSelProc.RootNode);
                }
                catch (Exception nodeEx)
                {
                    MessageBox.Show("流程图异常;" + nodeEx.ToString());
                }
                foreach (NodeEntry node in passedNodeDic.Keys)
                {
                    drawConnLine(g, 0, 0, node, passedNodeDic[node], "", true);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }

            base.OnPaint(e);
        }

        #region pens & brushs
        private Pen penPassed = new Pen(Color.OrangeRed, 2);
        private Pen penSelected = new Pen(Brushes.Lime, 2);
        private Pen penNormal = new Pen(Color.Black, 1.5f);
        private Pen penConnLine = new Pen(Color.FromArgb(49, 181, 214), 1.5f);

        private Brush brushCond = Brushes.LightBlue;
        private Brush brushOper = Brushes.Gold;
        private Brush brushOther = Brushes.LightPink;
        private Brush brushSubProc = Brushes.GreenYellow;

        private Brush brushArrowNormal = new SolidBrush(Color.FromArgb(49, 181, 214));
        private Brush brushArrowPassed = new SolidBrush(Color.OrangeRed);

        #endregion
        private void drawEntryNode(Rectangle absRect, Graphics g, int offsetX, int offsetY, NodeEntry node)
        {
            if (node == null)
            {
                return;
            }
            if (node.Type == NodeType.Condition)
            {
                 drawPolygon(absRect, g, offsetX, offsetY, node);
            }
            else if (node.Type == NodeType.Operate)
            {
                drawRectangle(absRect, g, offsetX, offsetY, node);
            }
            else if (node.Type == NodeType.PreProblemType)
            {
                drawEllipse(absRect, g, offsetX, offsetY, node);
            }
            else if (node.Type == NodeType.ReasonDesc)
            {
                drawRectangle(absRect, g, offsetX, offsetY, node, brushOper);
            }
            else if (node.Type == NodeType.SolveDesc)
            {
                drawRectangle(absRect, g, offsetX, offsetY, node, brushOther);
            }
            else if (node.Type == NodeType.OtherProc)
            {
                drawRectangle(absRect, g, offsetX, offsetY, node, brushSubProc);
            }

            drawNode(absRect, g, offsetX, offsetY, node);
        }

        private void drawPolygon(Rectangle absRect, Graphics g, int offsetX, int offsetY, NodeEntry node)
        {
            SizeF size = g.MeasureString(node.ExpString, fontText);
            node.Width = (int)size.Width + 30;
            node.Height = (int)size.Height + 30;
            Point[] pts = new Point[4];
            pts[0] = new Point((int)(node.XPos - 0.5 * node.Width) + offsetX, node.YPos + offsetY);
            pts[1] = new Point(node.XPos + offsetX, (int)(node.YPos - 0.5 * node.Height) + offsetY);
            pts[2] = new Point((int)(node.XPos + 0.5 * node.Width) + offsetX, node.YPos + offsetY);
            pts[3] = new Point(node.XPos + offsetX, (int)(node.YPos + 0.5 * node.Height) + offsetY);

            Rectangle rect = new Rectangle(pts[0].X, pts[1].Y, pts[2].X - pts[0].X, pts[3].Y - pts[1].Y);
            rect.Inflate(5, 5);
            if (absRect.IntersectsWith(rect))
            {
                Pen pen = penNormal;
                if (curSelNode == node)
                {
                    pen = penSelected;
                }
                else if (ESResultInfo != null
                    && ESResultInfo.GetNodeInfo(CurProcSeq.Seq, node._Idx) != null)
                {
                    pen = penPassed;
                }

                g.FillPolygon(brushCond, pts);
                g.DrawPolygon(pen, pts);
                g.DrawString(node.ExpString, fontText, Brushes.Black, node.XPos - 0.5f * size.Width + offsetX, node.YPos - 0.5f * size.Height + offsetY);
            }
        }

        private void drawRectangle(Rectangle absRect, Graphics g, int offsetX, int offsetY, NodeEntry node)
        {
            SizeF size = g.MeasureString(node.ExpString, fontText);
            node.Width = (int)size.Width + 2;
            node.Height = (int)size.Height + 2;
            Rectangle rect = new Rectangle((int)(node.XPos - 0.5 * node.Width) + offsetX, (int)(node.YPos - 0.5 * node.Height) + offsetY, node.Width, node.Height);
            rect.Inflate(5, 5);
            if (absRect.IntersectsWith(rect))
            {
                Pen pen = penNormal;
                if (curSelNode == node)
                {
                    pen = penSelected;
                }
                else if (ESResultInfo != null
                    && ESResultInfo.GetNodeInfo(CurProcSeq.Seq, node._Idx) != null)
                {
                    pen = penPassed;
                }

                g.FillRectangle(brushOper, (int)(node.XPos - 0.5 * node.Width) + offsetX, (int)(node.YPos - 0.5 * node.Height) + offsetY, node.Width, node.Height);
                g.DrawRectangle(pen, (int)(node.XPos - 0.5 * node.Width) + offsetX, (int)(node.YPos - 0.5 * node.Height) + offsetY, node.Width, node.Height);
                g.DrawString(node.ExpString, fontText, Brushes.Black, node.XPos - 0.5f * size.Width + offsetX, node.YPos - 0.5f * size.Height + offsetY);
            }
        }

        private void drawEllipse(Rectangle absRect, Graphics g, int offsetX, int offsetY, NodeEntry node)
        {
            SizeF size = g.MeasureString(node.ExpString, fontText);
            node.Width = (int)size.Width + 10;
            node.Height = (int)size.Height + 5;
            Rectangle rect = new Rectangle((int)(node.XPos - 0.5 * node.Width) + offsetX, (int)(node.YPos - 0.5 * node.Height) + offsetY, node.Width, node.Height);
            rect.Inflate(5, 5);
            if (absRect.IntersectsWith(rect))
            {
                Pen pen = penNormal;
                if (curSelNode == node)
                {
                    pen = penSelected;
                }
                else if (ESResultInfo != null
                    && ESResultInfo.GetNodeInfo(CurProcSeq.Seq, node._Idx) != null)
                {
                    pen = penPassed;
                }
                g.FillEllipse(brushOther, (int)(node.XPos - 0.5 * node.Width) + offsetX, (int)(node.YPos - 0.5 * node.Height) + offsetY, node.Width, node.Height);
                g.DrawEllipse(pen, (int)(node.XPos - 0.5 * node.Width) + offsetX, (int)(node.YPos - 0.5 * node.Height) + offsetY, node.Width, node.Height);
                g.DrawString(node.ExpString, fontText, Brushes.Black, node.XPos - 0.5f * size.Width + offsetX, node.YPos - 0.5f * size.Height + offsetY);
            }
        }

        private void drawRectangle(Rectangle absRect, Graphics g, int offsetX, int offsetY, NodeEntry node, Brush brush)
        {
            SizeF size = g.MeasureString(node.ExpString, fontText);
            node.Width = (int)size.Width + 10;
            node.Height = (int)size.Height + 5;
            Rectangle rect = new Rectangle((int)(node.XPos - 0.5 * node.Width) + offsetX, (int)(node.YPos - 0.5 * node.Height) + offsetY, node.Width, node.Height);
            rect.Inflate(5, 5);
            if (absRect.IntersectsWith(rect))
            {
                Pen pen = penNormal;
                if (curSelNode == node)
                {
                    pen = penSelected;
                }
                else if (ESResultInfo != null
                    && ESResultInfo.GetNodeInfo(CurProcSeq.Seq, node._Idx) != null)
                {
                    pen = penPassed;
                }
                g.FillRectangle(brush, (int)(node.XPos - 0.5 * node.Width) + offsetX, (int)(node.YPos - 0.5 * node.Height) + offsetY, node.Width, node.Height);
                g.DrawRectangle(pen, (int)(node.XPos - 0.5 * node.Width) + offsetX, (int)(node.YPos - 0.5 * node.Height) + offsetY, node.Width, node.Height);
                g.DrawString(node.ExpString, fontText, Brushes.Black, node.XPos - 0.5f * size.Width + offsetX, node.YPos - 0.5f * size.Height + offsetY);
            }
        }

        private void drawNode(Rectangle absRect, Graphics g, int offsetX, int offsetY, NodeEntry node)
        {
            if (node.YesNode != null)
            {
                drawEntryNode(absRect, g, offsetX, offsetY, node.YesNode);
                string lineTag = node.Type == NodeType.Condition ? "是" : "";

                NodeResultInfo nodeInfo = ESResultInfo.GetNodeInfo(CurProcSeq.Seq, node.YesNode._Idx);
                drawConnLine(g, offsetX, offsetY, node.YesConnection
                    , lineTag, nodeInfo != null && nodeInfo.PreNodeID == node._Idx);
            }
            if (node.NoNode != null)
            {
                drawEntryNode(absRect, g, offsetX, offsetY, node.NoNode);
                string lineTag = node.Type == NodeType.Condition ? "否" : "";
                NodeResultInfo nodeInfo = ESResultInfo.GetNodeInfo(CurProcSeq.Seq, node.NoNode._Idx);
                drawConnLine(g, offsetX, offsetY, node.NoConnection
                    , lineTag, nodeInfo != null && nodeInfo.PreNodeID == node._Idx);
            }
        }

        private void drawConnLine(Graphics g, int xoffpos, int yoffpos
           , NodeEntry fromNode, NodeEntry toNode, string tagStr, bool passed)
        {
            if (fromNode == null || toNode == null)
            {
                return;
            }

            Pen pen = penConnLine;
            Brush brush = brushArrowNormal;
            if (passed)
            {
                pen = penPassed;
                brush = brushArrowPassed;
            }

            bool downwardfirst = false;
            int fromX = fromNode.XPos;
            int fromY = fromNode.YPos;

            if (fromNode.Type == NodeType.Condition)
            {
                downwardfirst = false;
                if (toNode.XPos < fromNode.XPos - fromNode.Width / 2)//左侧
                {
                    fromX = fromNode.XPos - fromNode.Width / 2;
                    g.DrawString(tagStr, fontText, Brushes.Black, xoffpos + fromX - 15, yoffpos + fromY - 10);
                }
                else if (toNode.XPos > fromNode.XPos + fromNode.Width / 2)//右侧
                {
                    fromX = fromNode.XPos + fromNode.Width / 2;
                    g.DrawString(tagStr, fontText, Brushes.Black, xoffpos + fromX, yoffpos + fromY - 10);
                }
                else
                {
                    fromY += fromNode.Height / 2;
                    g.DrawString(tagStr, fontText, Brushes.Black, xoffpos + fromX - 8, yoffpos + fromY + 2);
                    downwardfirst = true;
                }
            }
            else if (fromNode.Type == NodeType.Operate || fromNode.Type == NodeType.PreProblemType
                || fromNode.Type == NodeType.ReasonDesc || fromNode.Type == NodeType.SolveDesc
                || fromNode.Type == NodeType.OtherProc)
            {
                fromY = fromNode.YPos + fromNode.Height / 2;
                downwardfirst = true;
            }
            int toX = toNode.XPos;
            int toY = toNode.YPos - toNode.Height / 2;

            if (downwardfirst)
            {
                g.DrawLine(pen, fromX + xoffpos, fromY + yoffpos, fromX + xoffpos, (fromY + toY) / 2 + yoffpos);
                g.DrawLine(pen, fromX + xoffpos, (fromY + toY) / 2 + yoffpos, toX + xoffpos, (fromY + toY) / 2 + yoffpos);
                g.DrawLine(pen, toX + xoffpos, (fromY + toY) / 2 + yoffpos, toX + xoffpos, toY + yoffpos);
                drawArrow(g, brush, toX + xoffpos, (fromY + toY) / 2 + yoffpos, toX + xoffpos, toY + yoffpos);
            }
            else
            {
                g.DrawLine(pen, fromX + xoffpos, fromY + yoffpos, toX + xoffpos, fromY + yoffpos);
                g.DrawLine(pen, toX + xoffpos, fromY + yoffpos, toX + xoffpos, toY + yoffpos);
                drawArrow(g, brush, toX + xoffpos, fromY + yoffpos, toX + xoffpos, toY + yoffpos);
            }
        }

        private void drawConnLine(Graphics g, int xoffpos, int yoffpos, NodeConnection connection, string tagStr, bool passed)
        {
            if (connection == null || connection.IsNodeNull())
            {
                return;
            }
            Pen pen = penConnLine;
            Brush brush = brushArrowNormal;
            if (passed)
            {
                pen = penPassed;
                brush = brushArrowPassed;
            }
            NodeEntry fromNode = connection.CurNode;
            int fromX = connection.FromX;
            int fromY = connection.FromY;
            int rformX = connection.RFromX;

            if (fromNode.Type == NodeType.Condition)
            {
                if (rformX == 0 - fromNode.Width / 2)//左侧
                {
                    g.DrawString(tagStr, fontText, Brushes.Black, xoffpos + fromX - 15, yoffpos + fromY - 10);
                }
                else if (rformX == fromNode.Width / 2)//右侧
                {
                    g.DrawString(tagStr, fontText, Brushes.Black, xoffpos + fromX, yoffpos + fromY - 10);
                }
                else
                {
                    g.DrawString(tagStr, fontText, Brushes.Black, xoffpos + fromX - 8, yoffpos + fromY + 2);
                }
            }
            List<ConnectLine> lines = connection.GetConnectLine(xoffpos, yoffpos);
            foreach (ConnectLine line in lines)
            {
                g.DrawLine(pen, line.P1, line.P2);
            }
            if (lines.Count > 0)
            {
                ConnectLine lastLine = lines[lines.Count - 1];
                drawArrow(g, brush, lastLine.P1.X, lastLine.P1.Y, lastLine.P2.X, lastLine.P2.Y);
            }
        }

        private void drawArrow(Graphics g, Brush brush, int x1, int y1, int x2, int y2)
        {
            int len = 10;
            int gap = 5;
            if (x1 == x2)//上下方向的箭头
            {
                Point[] pts = new Point[3];
                pts[0] = new Point(x2, y2);
                pts[1] = new Point(x2 + gap, (y2 - (len * (y2 > y1 ? 1 : (-1)))));
                pts[2] = new Point(x2 - gap, (y2 - (len * (y2 > y1 ? 1 : (-1)))));
                g.FillPolygon(brush, pts);
            }
            else //(y1==y2)左右方向的箭头
            {
                Point[] pts = new Point[3];
                pts[0] = new Point(x2, y2);
                pts[1] = new Point(x2 - len * (x2 > x1 ? 1 : (-1)), (y2 + gap));
                pts[2] = new Point(x2 - len * (x2 > x1 ? 1 : (-1)), (y2 - gap));
                g.FillPolygon(brush, pts);
            }
        }

    }

    public class ProcChangedEventArgs : EventArgs
    {
        public ProcRoutine OldProc { get; set; }
        public ProcRoutine CurProc { get; set; }
    }

}
