﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class NonconformityConditionDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.chkPccpch_Rscp = new DevExpress.XtraEditors.CheckEdit();
            this.spinEditPccpchRscpThreshold = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditDirectionGapDegreeThreshold = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditDistanceRangeThreshold = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.dropDownButtonParam = new DevExpress.XtraEditors.DropDownButton();
            this.btnOK = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            this.spinEditCellRank = new DevExpress.XtraEditors.SpinEdit();
            this.chkCellDegree = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditTpNum = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.btnImport = new DevExpress.XtraEditors.SimpleButton();
            this.checkEdit1 = new DevExpress.XtraEditors.CheckEdit();
            ((System.ComponentModel.ISupportInitialize)(this.chkPccpch_Rscp.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPccpchRscpThreshold.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDirectionGapDegreeThreshold.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDistanceRangeThreshold.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditCellRank.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCellDegree.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditTpNum.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEdit1.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // chkPccpch_Rscp
            // 
            this.chkPccpch_Rscp.EditValue = true;
            this.chkPccpch_Rscp.Location = new System.Drawing.Point(61, 53);
            this.chkPccpch_Rscp.Name = "chkPccpch_Rscp";
            this.chkPccpch_Rscp.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkPccpch_Rscp.Properties.Appearance.Options.UseFont = true;
            this.chkPccpch_Rscp.Properties.Caption = "PCCPCH_RSCP>";
            this.chkPccpch_Rscp.Size = new System.Drawing.Size(112, 19);
            this.chkPccpch_Rscp.TabIndex = 1;
            this.chkPccpch_Rscp.CheckedChanged += new System.EventHandler(this.chkPccpch_Rscp_CheckedChanged);
            // 
            // spinEditPccpchRscpThreshold
            // 
            this.spinEditPccpchRscpThreshold.EditValue = new decimal(new int[] {
            85,
            0,
            0,
            -2147483648});
            this.spinEditPccpchRscpThreshold.Location = new System.Drawing.Point(166, 52);
            this.spinEditPccpchRscpThreshold.Name = "spinEditPccpchRscpThreshold";
            this.spinEditPccpchRscpThreshold.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditPccpchRscpThreshold.Properties.Appearance.Options.UseFont = true;
            this.spinEditPccpchRscpThreshold.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditPccpchRscpThreshold.Properties.IsFloatValue = false;
            this.spinEditPccpchRscpThreshold.Properties.Mask.EditMask = "N00";
            this.spinEditPccpchRscpThreshold.Properties.MinValue = new decimal(new int[] {
            140,
            0,
            0,
            -2147483648});
            this.spinEditPccpchRscpThreshold.Size = new System.Drawing.Size(72, 20);
            this.spinEditPccpchRscpThreshold.TabIndex = 2;
            // 
            // labelControl1
            // 
            this.labelControl1.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl1.Appearance.Options.UseFont = true;
            this.labelControl1.Location = new System.Drawing.Point(244, 56);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(18, 12);
            this.labelControl1.TabIndex = 3;
            this.labelControl1.Text = "dBm";
            // 
            // labelControl2
            // 
            this.labelControl2.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl2.Appearance.Options.UseFont = true;
            this.labelControl2.Location = new System.Drawing.Point(16, 88);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(138, 12);
            this.labelControl2.TabIndex = 4;
            this.labelControl2.Text = "与天线主瓣覆盖偏差程度>";
            // 
            // spinEditDirectionGapDegreeThreshold
            // 
            this.spinEditDirectionGapDegreeThreshold.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.spinEditDirectionGapDegreeThreshold.Location = new System.Drawing.Point(166, 85);
            this.spinEditDirectionGapDegreeThreshold.Name = "spinEditDirectionGapDegreeThreshold";
            this.spinEditDirectionGapDegreeThreshold.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditDirectionGapDegreeThreshold.Properties.Appearance.Options.UseFont = true;
            this.spinEditDirectionGapDegreeThreshold.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditDirectionGapDegreeThreshold.Properties.IsFloatValue = false;
            this.spinEditDirectionGapDegreeThreshold.Properties.Mask.EditMask = "N00";
            this.spinEditDirectionGapDegreeThreshold.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.spinEditDirectionGapDegreeThreshold.Size = new System.Drawing.Size(72, 20);
            this.spinEditDirectionGapDegreeThreshold.TabIndex = 5;
            this.spinEditDirectionGapDegreeThreshold.EditValueChanged += new System.EventHandler(this.spinEditDirectionGapDegreeThreshold_EditValueChanged);
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl3.Appearance.Options.UseFont = true;
            this.labelControl3.Location = new System.Drawing.Point(247, 88);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(6, 12);
            this.labelControl3.TabIndex = 6;
            this.labelControl3.Text = "%";
            // 
            // labelControl4
            // 
            this.labelControl4.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl4.Appearance.Options.UseFont = true;
            this.labelControl4.Location = new System.Drawing.Point(100, 122);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(54, 12);
            this.labelControl4.TabIndex = 7;
            this.labelControl4.Text = "分析范围>";
            // 
            // spinEditDistanceRangeThreshold
            // 
            this.spinEditDistanceRangeThreshold.EditValue = new decimal(new int[] {
            6,
            0,
            0,
            65536});
            this.spinEditDistanceRangeThreshold.Location = new System.Drawing.Point(166, 119);
            this.spinEditDistanceRangeThreshold.Name = "spinEditDistanceRangeThreshold";
            this.spinEditDistanceRangeThreshold.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditDistanceRangeThreshold.Properties.Appearance.Options.UseFont = true;
            this.spinEditDistanceRangeThreshold.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditDistanceRangeThreshold.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.spinEditDistanceRangeThreshold.Size = new System.Drawing.Size(75, 20);
            this.spinEditDistanceRangeThreshold.TabIndex = 8;
            // 
            // labelControl5
            // 
            this.labelControl5.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl5.Appearance.Options.UseFont = true;
            this.labelControl5.Location = new System.Drawing.Point(247, 122);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(12, 12);
            this.labelControl5.TabIndex = 9;
            this.labelControl5.Text = "km";
            // 
            // labelControl6
            // 
            this.labelControl6.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl6.Appearance.Options.UseFont = true;
            this.labelControl6.Location = new System.Drawing.Point(106, 198);
            this.labelControl6.Name = "labelControl6";
            this.labelControl6.Size = new System.Drawing.Size(48, 12);
            this.labelControl6.TabIndex = 10;
            this.labelControl6.Text = "小区参数";
            // 
            // dropDownButtonParam
            // 
            this.dropDownButtonParam.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dropDownButtonParam.Appearance.Options.UseFont = true;
            this.dropDownButtonParam.Location = new System.Drawing.Point(166, 194);
            this.dropDownButtonParam.Name = "dropDownButtonParam";
            this.dropDownButtonParam.Size = new System.Drawing.Size(113, 23);
            this.dropDownButtonParam.TabIndex = 11;
            this.dropDownButtonParam.Text = "PCCPCH_RSCP";
            // 
            // btnOK
            // 
            this.btnOK.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnOK.Appearance.Options.UseFont = true;
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(214, 316);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 12;
            this.btnOK.Text = "确定";
            // 
            // btnCancel
            // 
            this.btnCancel.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.Appearance.Options.UseFont = true;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(306, 316);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 13;
            this.btnCancel.Text = "取消";
            // 
            // spinEditCellRank
            // 
            this.spinEditCellRank.EditValue = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.spinEditCellRank.Location = new System.Drawing.Point(166, 21);
            this.spinEditCellRank.Name = "spinEditCellRank";
            this.spinEditCellRank.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditCellRank.Properties.Appearance.Options.UseFont = true;
            this.spinEditCellRank.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditCellRank.Properties.IsFloatValue = false;
            this.spinEditCellRank.Properties.Mask.EditMask = "N00";
            this.spinEditCellRank.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditCellRank.Size = new System.Drawing.Size(72, 20);
            this.spinEditCellRank.TabIndex = 14;
            // 
            // chkCellDegree
            // 
            this.chkCellDegree.EditValue = true;
            this.chkCellDegree.Location = new System.Drawing.Point(61, 22);
            this.chkCellDegree.Name = "chkCellDegree";
            this.chkCellDegree.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkCellDegree.Properties.Appearance.Options.UseFont = true;
            this.chkCellDegree.Properties.Caption = "查询小区级别";
            this.chkCellDegree.Size = new System.Drawing.Size(99, 19);
            this.chkCellDegree.TabIndex = 0;
            this.chkCellDegree.CheckedChanged += new System.EventHandler(this.chkCellDegree_CheckedChanged);
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl7.Appearance.Options.UseFont = true;
            this.labelControl7.Location = new System.Drawing.Point(63, 157);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(90, 12);
            this.labelControl7.TabIndex = 15;
            this.labelControl7.Text = "问题采样点数目>";
            // 
            // spinEditTpNum
            // 
            this.spinEditTpNum.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spinEditTpNum.Location = new System.Drawing.Point(166, 154);
            this.spinEditTpNum.Name = "spinEditTpNum";
            this.spinEditTpNum.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.spinEditTpNum.Properties.Appearance.Options.UseFont = true;
            this.spinEditTpNum.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditTpNum.Properties.IsFloatValue = false;
            this.spinEditTpNum.Properties.Mask.EditMask = "N00";
            this.spinEditTpNum.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.spinEditTpNum.Size = new System.Drawing.Size(75, 20);
            this.spinEditTpNum.TabIndex = 16;
            // 
            // labelControl8
            // 
            this.labelControl8.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelControl8.Appearance.Options.UseFont = true;
            this.labelControl8.Location = new System.Drawing.Point(249, 158);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(132, 12);
            this.labelControl8.TabIndex = 17;
            this.labelControl8.Text = "时，显示查询结果的小区";
            // 
            // btnImport
            // 
            this.btnImport.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnImport.Appearance.Options.UseFont = true;
            this.btnImport.Enabled = false;
            this.btnImport.Image = global::MasterCom.RAMS.Properties.Resources.xls;
            this.btnImport.Location = new System.Drawing.Point(166, 247);
            this.btnImport.Name = "btnImport";
            this.btnImport.Size = new System.Drawing.Size(75, 23);
            this.btnImport.TabIndex = 18;
            this.btnImport.Text = "导入";
            this.btnImport.Click += new System.EventHandler(this.btnImport_Click);
            // 
            // checkEdit1
            // 
            this.checkEdit1.Location = new System.Drawing.Point(42, 251);
            this.checkEdit1.Name = "checkEdit1";
            this.checkEdit1.Properties.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkEdit1.Properties.Appearance.Options.UseFont = true;
            this.checkEdit1.Properties.Caption = "使用特定小区工参";
            this.checkEdit1.Size = new System.Drawing.Size(118, 19);
            this.checkEdit1.TabIndex = 19;
            this.checkEdit1.CheckedChanged += new System.EventHandler(this.checkEdit1_CheckedChanged);
            // 
            // NonconformityConditionDlg
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(409, 366);
            this.Controls.Add(this.checkEdit1);
            this.Controls.Add(this.btnImport);
            this.Controls.Add(this.labelControl8);
            this.Controls.Add(this.spinEditTpNum);
            this.Controls.Add(this.labelControl7);
            this.Controls.Add(this.spinEditCellRank);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.dropDownButtonParam);
            this.Controls.Add(this.labelControl6);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.spinEditDistanceRangeThreshold);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.spinEditDirectionGapDegreeThreshold);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.spinEditPccpchRscpThreshold);
            this.Controls.Add(this.chkPccpch_Rscp);
            this.Controls.Add(this.chkCellDegree);
            this.Name = "NonconformityConditionDlg";
            this.Text = "覆盖不符条件设置";
            ((System.ComponentModel.ISupportInitialize)(this.chkPccpch_Rscp.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditPccpchRscpThreshold.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDirectionGapDegreeThreshold.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditDistanceRangeThreshold.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditCellRank.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCellDegree.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditTpNum.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEdit1.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.CheckEdit chkPccpch_Rscp;
        private DevExpress.XtraEditors.SpinEdit spinEditPccpchRscpThreshold;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SpinEdit spinEditDirectionGapDegreeThreshold;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.SpinEdit spinEditDistanceRangeThreshold;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.DropDownButton dropDownButtonParam;
        private DevExpress.XtraEditors.SimpleButton btnOK;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
        private DevExpress.XtraEditors.SpinEdit spinEditCellRank;
        private DevExpress.XtraEditors.CheckEdit chkCellDegree;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.SpinEdit spinEditTpNum;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.SimpleButton btnImport;
        private DevExpress.XtraEditors.CheckEdit checkEdit1;
    }
}