﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class WeakCoverDropCallSettingDlg : BaseDialog
    {
        public WeakCoverDropCallSettingDlg()
        {
            InitializeComponent();
        }

        public void GetCondition(ref int weakCoverRxLev, ref int secondAna)
        {
            weakCoverRxLev = (int)edtWeakCoverRxLev.Value;
            secondAna = (int)edtSecondAna.Value;
        }
    }
}
