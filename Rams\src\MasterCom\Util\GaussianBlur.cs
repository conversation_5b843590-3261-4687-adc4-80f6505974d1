using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.ComponentModel;
using System.Diagnostics;

namespace Adrian.PhotoX.Lib
{
    [Serializable]
    public enum BlurType
    {
        Both,
        HorizontalOnly,
        VerticalOnly,
    }

    [Serializable]
    public class GaussianBlur //: StyleBase
    {
        private int _radius = 1;
        private int[] _kernel;
        private int _kernelSum;
        private int[,] _multable;

        public GaussianBlur()
        {
            PreCalculateSomeStuff();
        }

        public GaussianBlur(int radius)
        {
            _radius = radius;
            PreCalculateSomeStuff();
        }

        private void PreCalculateSomeStuff()
        {
            int sz = _radius * 2 + 1;
            _kernel = new int[sz];
            _multable = new int[sz, 256];
            for (int i = 1; i <= _radius; i++)
            {
                int szi = _radius - i;
                int szj = _radius + i;
                _kernel[szj] = _kernel[szi] = (szi + 1) * (szi + 1);
                _kernelSum += (_kernel[szj] + _kernel[szi]);
                for (int j = 0; j < 256; j++)
                {
                    _multable[szj, j] = _multable[szi, j] = _kernel[szj] * j;
                }
            }
            _kernel[_radius] = (_radius + 1) * (_radius + 1);
            _kernelSum += _kernel[_radius];
            for (int j = 0; j < 256; j++)
            {
                _multable[_radius, j] = _kernel[_radius] * j;
            }
        }

        public long t1 { get; set; }
        public long t2 { get; set; }
        public long t3 { get; set; }
        public long t4 { get; set; }

        public Bitmap ProcessImage(Image inputImage)
        {
            Bitmap origin = new Bitmap(inputImage);
            Bitmap blurred = new Bitmap(inputImage.Width, inputImage.Height);

            using (RawBitmap src = new RawBitmap(origin))
            {
                using (RawBitmap dest = new RawBitmap(blurred))
                {
                    Info info = new Info(src);
                    int offset = src.GetOffset();
                    int index = 0;
                    unsafe
                    {
                        byte* ptr = src.Begin;
                        for (int i = 0; i < src.Height; i++)
                        {
                            for (int j = 0; j < src.Width; j++)
                            {
                                info.Color1.b[index] = *ptr;
                                ptr++;
                                info.Color1.g[index] = *ptr;
                                ptr++;
                                info.Color1.r[index] = *ptr;
                                ptr++;

                                ++index;
                            }
                            ptr += offset;
                        }

                        if (BlurType != BlurType.VerticalOnly)
                        {
                            dealHorizontal(src, dest, info);
                        }
                        if (BlurType == BlurType.HorizontalOnly)
                        {
                            return blurred;
                        }

                        dealVertical(src, dest, info);
                    }
                }
            }

            return blurred;
        }

        private unsafe void dealHorizontal(RawBitmap src, RawBitmap dest, Info info)
        {
            int start = 0;
            int index = 0;
            for (int i = 0; i < src.Height; i++)
            {
                for (int j = 0; j < src.Width; j++)
                {
                    info.bsum = info.gsum = info.rsum = 0;
                    info.read = index - _radius;

                    for (int z = 0; z < _kernel.Length; z++)
                    {
                        dealSum(info, info.Color1, z, info.read < start, info.read > start + src.Width - 1, start, start + src.Width - 1);
                        ++info.read;
                    }

                    info.Color2.b[index] = (info.bsum / _kernelSum);
                    info.Color2.g[index] = (info.gsum / _kernelSum);
                    info.Color2.r[index] = (info.rsum / _kernelSum);

                    if (BlurType == BlurType.HorizontalOnly)
                    {
                        byte* pcell = dest[j, i];
                        *pcell = (byte)(info.bsum / _kernelSum);
                        pcell++;
                        *pcell = (byte)(info.gsum / _kernelSum);
                        pcell++;
                        *pcell = (byte)(info.rsum / _kernelSum);
                    }

                    ++index;
                }
                start += src.Width;
            }
        }

        private unsafe void dealVertical(RawBitmap src, RawBitmap dest, Info info)
        {
            int start;
            int tempy;
            for (int i = 0; i < src.Height; i++)
            {
                int y = i - _radius;
                start = y * src.Width;
                for (int j = 0; j < src.Width; j++)
                {
                    info.bsum = info.gsum = info.rsum = 0;
                    info.read = start + j;
                    tempy = y;
                    for (int z = 0; z < _kernel.Length; z++)
                    {
                        if (BlurType == BlurType.VerticalOnly)
                        {
                            dealSum(info, info.Color1, z, tempy < 0, tempy > src.Height - 1, j, info.pixelCount - (src.Width - j));
                        }
                        else
                        {
                            dealSum(info, info.Color2, z, tempy < 0, tempy > src.Height - 1, j, info.pixelCount - (src.Width - j));
                        }

                        info.read += src.Width;
                        ++tempy;
                    }

                    byte* pcell = dest[j, i];
                    pcell[0] = (byte)(info.bsum / _kernelSum);
                    pcell[1] = (byte)(info.gsum / _kernelSum);
                    pcell[2] = (byte)(info.rsum / _kernelSum);
                }
            }
        }

        private unsafe void dealSum(Info info, Info.ColorArray color, int z, bool judge1, bool judge2, int idx1, int idx2)
        {
            if (judge1)
            {
                info.bsum += _multable[z, color.b[idx1]];
                info.gsum += _multable[z, color.g[idx1]];
                info.rsum += _multable[z, color.r[idx1]];
            }
            else if (judge2)
            {
                info.bsum += _multable[z, color.b[idx2]];
                info.gsum += _multable[z, color.g[idx2]];
                info.rsum += _multable[z, color.r[idx2]];
            }
            else
            {
                info.bsum += _multable[z, color.b[info.read]];
                info.gsum += _multable[z, color.g[info.read]];
                info.rsum += _multable[z, color.r[info.read]];
            }
        }

        public class Info
        {
            public Info(RawBitmap src)
            {
                pixelCount = src.Width * src.Height;
                Color1 = new ColorArray(pixelCount);
                Color2 = new ColorArray(pixelCount);
            }

            public int bsum { get; set; }
            public int gsum { get; set; }
            public int rsum { get; set; }
            public int read { get; set; }

            public int pixelCount { get; set; }

            public ColorArray Color1 { get; set; }
            public ColorArray Color2 { get; set; }

            public class ColorArray
            {
                public ColorArray(int pixelCount)
                {
                    b = new int[pixelCount];
                    g = new int[pixelCount];
                    r = new int[pixelCount];
                }

                public int[] b { get; set; }
                public int[] g { get; set; }
                public int[] r { get; set; }
            }
        }

        public int Radius
        {
            get { return _radius; }
            set
            {
                if (value < 1)
                {
                    throw new InvalidOperationException("Radius must be greater then 0");
                }
                _radius = value;
                PreCalculateSomeStuff();
            }
        }

        public BlurType BlurType { get; set; }
    }
}
