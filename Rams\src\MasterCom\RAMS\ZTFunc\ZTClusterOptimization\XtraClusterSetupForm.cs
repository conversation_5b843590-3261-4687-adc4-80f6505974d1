﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.ZTFunc.ZTCellSplit;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.ZTCluster
{
    public partial class XtraClusterSetupForm : DevExpress.XtraEditors.XtraForm
    {
        MainModel mainModel;
        public XtraClusterSetupForm(MainModel mainmodel)
        {
            InitializeComponent();
            mainModel = mainmodel;
            loadData();
        }

        private void loadData()
        {
            DiySqlQueryClusterRound diysqlsetup = new DiySqlQueryClusterRound(mainModel);
            diysqlsetup.Query();
            List<ClusterRound> list = diysqlsetup.ClusterRoundList ;

            foreach (ClusterRound item in list)
            {
                
                comboBox1.Items.Add(item);
            }
        }

        public void getSelect(out int round)
        {
            ClusterRound cellsetup = comboBox1.SelectedItem as ClusterRound;
            round = cellsetup.Iroundid;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void button2_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}