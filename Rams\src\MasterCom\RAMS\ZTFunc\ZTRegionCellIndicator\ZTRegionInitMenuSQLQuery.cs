﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 查询指标统计时间数据
    /// </summary>
    public class ZTRegionInitMenuSQLQuery : DIYSQLBase
    {
        public ZTRegionInitMenuSQLQuery(MainModel mainModel)
            : base(mainModel)
        { 
            
        }

        public string ReportName { get; set; }

        private readonly List<string> menuItems = new List<string>();
        public List<string> MenuItems
        {
            get { return menuItems; }
        }

        protected override string getSqlTextString()
        {
            return "select distinct top 15 CONVERT(varchar(255),beginTime,23)+'至'+CONVERT(varchar(255),endTime,23),endTime from Complain_Sys..tb_QoE_area_KPI_score where reportName='" + ReportName + "' order by endTime desc";
        }

        protected override MasterCom.RAMS.Model.Interface.E_VType[] getSqlRetTypeArr()
        {
            E_VType[] vtypes = new E_VType[2];
            vtypes[0] = E_VType.E_String;
            vtypes[1] = E_VType.E_String;
            return vtypes;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            menuItems.Clear();
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    string menuitem = package.Content.GetParamString();
                    package.Content.GetParamString();//endTime
                    this.menuItems.Add(menuitem);
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    break;
                }
                else if (package.Content.Type == ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    break;
                }
                else if (package.Content.Type == ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        public override string Name
        {
            get { return ""; }
        }
    }
}
