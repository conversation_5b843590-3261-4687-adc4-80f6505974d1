﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CoverDistanceForm : MinCloseForm
    {
        List<CellCoverDistance> refList = new List<CellCoverDistance>();
        public CoverDistanceForm()
            : base()
        {
            InitializeComponent();
//#if LT
//            gridColumnBCCH.Caption = "UARFCN";
//            gridColumnBSIC.Caption = "PSC";
//#endif
        }

        public void FillData()
        {
            refList.Clear();
            foreach (CellCoverDistance coverDistance in MainModel.CellCoverDistanceList)
            {
                coverDistance.GetValue((float)edtRxLevMin.Value, (float)edtRxLevMax.Value);
                if (coverDistance.ValidSampleCount > 0)
                {
                    refList.Add(coverDistance);
                }
            }
            BindingSource source = new BindingSource();
            source.DataSource = refList;
            gridControl.DataSource = source;
            gridControl.RefreshDataSource();
        }

        private void edtRxLevThreshold_EditValueChanged(object sender, EventArgs e)
        {
            FillData();
        }

        private void miExportToExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView);
        }
    }
}
