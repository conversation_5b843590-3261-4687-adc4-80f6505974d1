﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class KPIReportForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.panel2 = new System.Windows.Forms.Panel();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.gvGridKPI = new System.Windows.Forms.DataGridView();
            this.gvckpiName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.gvcKPIValue = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.gvcKPIScore = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.gvGridKQI = new System.Windows.Forms.DataGridView();
            this.gvcKQIName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.gvcKQIScore = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.panel1 = new System.Windows.Forms.Panel();
            this.labelDatePick = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.labelGridName = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.dataGridViewTextBoxColumn1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn2 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn3 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn4 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dataGridViewTextBoxColumn5 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.panel2.SuspendLayout();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gvGridKPI)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gvGridKQI)).BeginInit();
            this.panel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // panel2
            // 
            this.panel2.Controls.Add(this.groupBox2);
            this.panel2.Controls.Add(this.groupBox1);
            this.panel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel2.Location = new System.Drawing.Point(0, 37);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(733, 336);
            this.panel2.TabIndex = 3;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.gvGridKPI);
            this.groupBox2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox2.Location = new System.Drawing.Point(0, 0);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Padding = new System.Windows.Forms.Padding(3, 3, 10, 3);
            this.groupBox2.Size = new System.Drawing.Size(394, 336);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "网格KPI";
            // 
            // gvGridKPI
            // 
            this.gvGridKPI.AllowUserToAddRows = false;
            this.gvGridKPI.AllowUserToDeleteRows = false;
            this.gvGridKPI.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.gvGridKPI.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.gvGridKPI.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.gvckpiName,
            this.gvcKPIValue,
            this.gvcKPIScore});
            this.gvGridKPI.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gvGridKPI.Location = new System.Drawing.Point(3, 17);
            this.gvGridKPI.Name = "gvGridKPI";
            this.gvGridKPI.ReadOnly = true;
            this.gvGridKPI.RowTemplate.Height = 23;
            this.gvGridKPI.Size = new System.Drawing.Size(381, 316);
            this.gvGridKPI.TabIndex = 0;
            // 
            // gvckpiName
            // 
            this.gvckpiName.DataPropertyName = "IndicatorName";
            this.gvckpiName.HeaderText = "指标名称";
            this.gvckpiName.Name = "gvckpiName";
            this.gvckpiName.ReadOnly = true;
            // 
            // gvcKPIValue
            // 
            this.gvcKPIValue.DataPropertyName = "IndicatorValue";
            this.gvcKPIValue.HeaderText = "指标值";
            this.gvcKPIValue.Name = "gvcKPIValue";
            this.gvcKPIValue.ReadOnly = true;
            // 
            // gvcKPIScore
            // 
            this.gvcKPIScore.DataPropertyName = "Score";
            this.gvcKPIScore.HeaderText = "指标得分";
            this.gvcKPIScore.Name = "gvcKPIScore";
            this.gvcKPIScore.ReadOnly = true;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.gvGridKQI);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Right;
            this.groupBox1.Location = new System.Drawing.Point(394, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Padding = new System.Windows.Forms.Padding(10, 3, 3, 3);
            this.groupBox1.Size = new System.Drawing.Size(339, 336);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "网格KQI";
            // 
            // gvGridKQI
            // 
            this.gvGridKQI.AllowUserToAddRows = false;
            this.gvGridKQI.AllowUserToDeleteRows = false;
            this.gvGridKQI.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.gvGridKQI.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.gvGridKQI.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.gvcKQIName,
            this.gvcKQIScore});
            this.gvGridKQI.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gvGridKQI.Location = new System.Drawing.Point(10, 17);
            this.gvGridKQI.Name = "gvGridKQI";
            this.gvGridKQI.ReadOnly = true;
            this.gvGridKQI.RowTemplate.Height = 23;
            this.gvGridKQI.Size = new System.Drawing.Size(326, 316);
            this.gvGridKQI.TabIndex = 1;
            // 
            // gvcKQIName
            // 
            this.gvcKQIName.DataPropertyName = "IndicatorName";
            this.gvcKQIName.HeaderText = "指标名称";
            this.gvcKQIName.Name = "gvcKQIName";
            this.gvcKQIName.ReadOnly = true;
            // 
            // gvcKQIScore
            // 
            this.gvcKQIScore.DataPropertyName = "Score";
            this.gvcKQIScore.HeaderText = "指标得分";
            this.gvcKQIScore.Name = "gvcKQIScore";
            this.gvcKQIScore.ReadOnly = true;
            // 
            // panel1
            // 
            this.panel1.BackColor = System.Drawing.Color.WhiteSmoke;
            this.panel1.Controls.Add(this.labelDatePick);
            this.panel1.Controls.Add(this.label5);
            this.panel1.Controls.Add(this.labelGridName);
            this.panel1.Controls.Add(this.label3);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(733, 37);
            this.panel1.TabIndex = 2;
            // 
            // labelDatePick
            // 
            this.labelDatePick.AutoSize = true;
            this.labelDatePick.Location = new System.Drawing.Point(373, 14);
            this.labelDatePick.Name = "labelDatePick";
            this.labelDatePick.Size = new System.Drawing.Size(17, 12);
            this.labelDatePick.TabIndex = 0;
            this.labelDatePick.Text = "无";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(271, 14);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(101, 12);
            this.label5.TabIndex = 0;
            this.label5.Text = "指标统计时间段：";
            // 
            // labelGridName
            // 
            this.labelGridName.AutoSize = true;
            this.labelGridName.Location = new System.Drawing.Point(69, 14);
            this.labelGridName.Name = "labelGridName";
            this.labelGridName.Size = new System.Drawing.Size(17, 12);
            this.labelGridName.TabIndex = 0;
            this.labelGridName.Text = "无";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(5, 14);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(65, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "当前对象：";
            // 
            // dataGridViewTextBoxColumn1
            // 
            this.dataGridViewTextBoxColumn1.DataPropertyName = "IndicatorName";
            this.dataGridViewTextBoxColumn1.HeaderText = "指标名称";
            this.dataGridViewTextBoxColumn1.Name = "dataGridViewTextBoxColumn1";
            this.dataGridViewTextBoxColumn1.Width = 84;
            // 
            // dataGridViewTextBoxColumn2
            // 
            this.dataGridViewTextBoxColumn2.DataPropertyName = "IndicatorValue";
            this.dataGridViewTextBoxColumn2.HeaderText = "指标值";
            this.dataGridViewTextBoxColumn2.Name = "dataGridViewTextBoxColumn2";
            this.dataGridViewTextBoxColumn2.Width = 85;
            // 
            // dataGridViewTextBoxColumn3
            // 
            this.dataGridViewTextBoxColumn3.DataPropertyName = "Score";
            this.dataGridViewTextBoxColumn3.HeaderText = "指标得分";
            this.dataGridViewTextBoxColumn3.Name = "dataGridViewTextBoxColumn3";
            this.dataGridViewTextBoxColumn3.Width = 84;
            // 
            // dataGridViewTextBoxColumn4
            // 
            this.dataGridViewTextBoxColumn4.DataPropertyName = "IndicatorName";
            this.dataGridViewTextBoxColumn4.HeaderText = "指标名称";
            this.dataGridViewTextBoxColumn4.Name = "dataGridViewTextBoxColumn4";
            this.dataGridViewTextBoxColumn4.Width = 184;
            // 
            // dataGridViewTextBoxColumn5
            // 
            this.dataGridViewTextBoxColumn5.DataPropertyName = "Score";
            this.dataGridViewTextBoxColumn5.HeaderText = "指标得分";
            this.dataGridViewTextBoxColumn5.Name = "dataGridViewTextBoxColumn5";
            this.dataGridViewTextBoxColumn5.Width = 184;
            // 
            // KPIReportForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(733, 373);
            this.Controls.Add(this.panel2);
            this.Controls.Add(this.panel1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.Name = "KPIReportForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "KPI指标结果";
            this.panel2.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gvGridKPI)).EndInit();
            this.groupBox1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gvGridKQI)).EndInit();
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.DataGridView gvGridKPI;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvckpiName;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcKPIValue;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcKPIScore;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.DataGridView gvGridKQI;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcKQIName;
        private System.Windows.Forms.DataGridViewTextBoxColumn gvcKQIScore;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Label labelDatePick;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label labelGridName;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn1;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn2;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn3;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn4;
        private System.Windows.Forms.DataGridViewTextBoxColumn dataGridViewTextBoxColumn5;
    }
}