﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public partial class PKBaseSettingDlg : BaseForm
    {
        private PKCondition condition = null;

        public PKBaseSettingDlg()
        {
            InitializeComponent();
        }

        public bool IsStatLastOptionVisible
        {
            get
            {
                return chkStatLatestOnly.Visible;
            }
            set
            {
                chkStatLatestOnly.Visible = value;
            }
        }

        private void refreshTemplate()
        {
            comboBoxEditReportTemplate.Properties.Items.Clear();
            foreach (PKModeTemplate rpt in PKModeTemplateMngr.Instance.Templates)
            {
                comboBoxEditReportTemplate.Properties.Items.Add(rpt);
            }
            if (comboBoxEditReportTemplate.Properties.Items.Count > 0)
            {
                comboBoxEditReportTemplate.SelectedIndex = 0;
            }
            if (condition != null && condition.SelTemplate != null)
            {
                comboBoxEditReportTemplate.SelectedItem = condition.SelTemplate;
            }
        }

        public void SetCondition(PKCondition condition)
        {
            this.condition = condition;

            refreshTemplate();
            chkStatLatestOnly.Checked = condition.IsStatLatestOnly;

            cmBase.SetCondition(condition.ConditionCM);
            cuBase.SetCondition(condition.ConditionCU);
            ctBase.SetCondition(condition.ConditionCT);
        }

        public PKCondition GetCondition()
        {
            condition.SelTemplate = comboBoxEditReportTemplate.SelectedItem as PKModeTemplate;
            condition.ConditionCM = cmBase.GetCondition();
            condition.ConditionCM.ServiceTypes.AddRange(condition.SelTemplate.CMHub.PkBase.ServVec);
            condition.ConditionCU = cuBase.GetCondition();
            condition.ConditionCU.ServiceTypes.AddRange(condition.SelTemplate.CUHub.PkBase.ServVec);
            condition.ConditionCT = ctBase.GetCondition();
            condition.ConditionCT.ServiceTypes.AddRange(condition.SelTemplate.CTHub.PkBase.ServVec);
            condition.IsStatLatestOnly = chkStatLatestOnly.Checked;
            return condition;
        }

        private void simpleButtonTemplateOption_Click(object sender, EventArgs e)
        {
            PKModelSettingDlg dlg = new PKModelSettingDlg();
            dlg.FillTemplate(comboBoxEditReportTemplate.SelectedItem as PKModeTemplate);
            dlg.ShowDialog();
            refreshTemplate();
        }

        private void simpleButtonOK_Click(object sender, EventArgs e)
        {
            if (!checkCondition())
                return;

            this.DialogResult = DialogResult.OK;
        }

        private bool checkCondition()
        {
            if (comboBoxEditReportTemplate.SelectedItem == null)
            {
                MessageBox.Show("尚未选择竞比模版，请选择...");
                return false;
            }
            return cmBase.CheckCondition() && cuBase.CheckCondition() && ctBase.CheckCondition();
        }

        private void simpleButtonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void linkLbl_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            PKModeTemplate t = this.comboBoxEditReportTemplate.SelectedItem as PKModeTemplate;
            if (!System.IO.File.Exists(PKModeTemplateMngr.PATH_NEW+t.Name+".xml"))
            {
                MessageBox.Show("模板配置不存在，请先点击【定制】按钮进行模板定制");
                return;
            }
            System.Diagnostics.Process.Start("Explorer", "/select,"
                + System.IO.Path.GetFullPath(PKModeTemplateMngr.PATH_NEW + t.Name + ".xml"));
        }
    }

    public class PKCondition
    {
        public PKModeTemplate SelTemplate { get; set; }

        public bool IsStatLatestOnly
        {
            get;
            set;
        }

        public QueryCondition ConditionCM { get; set; }
        public QueryCondition ConditionCU { get; set; }
        public QueryCondition ConditionCT { get; set; }

        public PKCondition()
        {
            SelTemplate = null;
            ConditionCM = new QueryCondition();
            ConditionCU = new QueryCondition();
            ConditionCT = new QueryCondition();

            ConditionCM.CarrierTypes.Add((int)ECarrier.移动);
            ConditionCU.CarrierTypes.Add((int)ECarrier.联通);
            ConditionCT.CarrierTypes.Add((int)ECarrier.电信);
        }

        public void Init()
        {
            ArchiveCondition archiCond = ArchiveSettingManager.GetInstance().Condition;
            ConditionCM.Periods.Clear();
            ConditionCM.Periods.Add(new MasterCom.Util.TimePeriod(archiCond.BaseCondition.Periods[0].BeginTime, archiCond.BaseCondition.Periods[0].EndTime));
            ConditionCU.Periods.Clear();
            ConditionCU.Periods.Add(new MasterCom.Util.TimePeriod(archiCond.BaseCondition.Periods[0].BeginTime, archiCond.BaseCondition.Periods[0].EndTime));
            ConditionCT.Periods.Clear();
            ConditionCT.Periods.Add(new MasterCom.Util.TimePeriod(archiCond.BaseCondition.Periods[0].BeginTime, archiCond.BaseCondition.Periods[0].EndTime));

            ConditionCM.Projects.Clear();
            ConditionCU.Projects.Clear();
            ConditionCT.Projects.Clear();
            foreach (int proj in archiCond.BaseCondition.Projects)
            {
                ConditionCM.Projects.Add(proj);
                ConditionCU.Projects.Add(proj);
                ConditionCT.Projects.Add(proj);
            }
        }

        public double CalcFormula(AreaKPIDataGroup<AreaBase> kpiGroup, ECarrier car)
        {
            return SelTemplate.CalcFormula(kpiGroup, car);
        }

        public Alghirithm GetAlghirithm(double dcm, double dcu, double dct)
        {
            return SelTemplate.GetAlghirithm(dcm, dcu, dct);
        }
    }
}
