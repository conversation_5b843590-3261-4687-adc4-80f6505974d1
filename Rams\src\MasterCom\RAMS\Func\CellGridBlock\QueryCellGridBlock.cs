﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.NewBlackBlock;
using System.Windows.Forms;
using MasterCom.Util;
using System.Data;
using System.Drawing;
using MasterCom.RAMS.Frame;

namespace MasterCom.RAMS.Func
{
    public class QueryCellGridBlock : QueryBase
    {
        private static readonly object lockObj = new object();
        private static QueryCellGridBlock instance = null;
        private new CellGridCondition condition = null;
        public List<CellGridBlockInfo> ResultList { get; set; }
        public List<LTECell> lteCells { get; set; }

        public static QueryCellGridBlock GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new QueryCellGridBlock();
                    }
                }
            }
            return instance;
        }
        public override string Name
        {
            get { return "天线分析"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.Region;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }
        protected QueryCellGridBlock()
            : base(MainModel.GetInstance())
        {
            
        }
        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 18000, 18039, this.Name);
        }

        CellGridBlockSetConditionForm setForm = null;
        protected override bool isValidCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new CellGridBlockSetConditionForm();
            }
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                ResultList = new List<CellGridBlockInfo>();
                this.condition = setForm.GetCondition();
                return true;
            }
            return false;
        }

        protected override void query()
        {
            WaitBox.Show(doStat);
            if (ResultList == null || ResultList.Count == 0)
            {
                MessageBox.Show("没有符合条件的数据");
                return;
            }

            lteCells = new List<LTECell>();
            showResultForm();
        }

        private void doStat()
        {
            try
            {
                DistrictManager mgr = DistrictManager.GetInstance();
                int iloop = 0;
                foreach (Stat.IDNamePair pair in mgr.GetAvailableDistrict())
                {
                    WaitBox.Text = "正在统计[" + pair.Name + "]的栅格问题点情况,统计进度(" + (++iloop) + "/" + mgr.GetAvailableDistrict().Count + ")...";
                    ClientProxy clientProxy = new ClientProxy();

                    if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, pair.id) != ConnectResult.Success)
                    {
                        ErrorInfo = "连接服务器失败";
                        continue;
                    }

                    statEachDB(pair);
                    break;
                }
                WaitBox.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
                throw;
            }
        }
        private void statEachDB(Stat.IDNamePair pair)
        {
            int num = 0;
            StringBuilder sb = new StringBuilder();
            foreach (string type in condition.Types)
            {
                DIYSQLCellGridBlock queryBlock = new DIYSQLCellGridBlock(MainModel, type, pair.id);
                DIYSQLCellGrid queryCg = new DIYSQLCellGrid(MainModel, type);
                DIYSQLCellGridAll queryAll = new DIYSQLCellGridAll(MainModel, type);
                List<CellGridBlockInfo> cgList;
                Dictionary<int, List<CellGridInfo>> cgDic;
                Dictionary<string, List<CellGridAllInfo>> allDic;
                try
                {
                    queryBlock.Query();
                    cgList = queryBlock.Blocks;

                    queryCg.Query();
                    cgDic = queryCg.CellGrids;

                    queryAll.Query();
                    allDic = queryAll.CellGridAll;

                    foreach (CellGridBlockInfo cg in cgList)
                    {
                        num++;
                        cg.SN = num;
                        cg.Type = type;
                        cg.CellGrids = cgDic[cg.BlockID];
                        LTECell cell = CellManager.GetInstance().GetLTECell(cg.Time, cg.LAC, cg.CI);
                        setCellGridAllInfo(cgDic, allDic, cg, cell);
                        cg.CellGridAll = allDic["0-" + cg.CI.ToString()];
                    }

                    ResultList.AddRange(cgList);
                }
                catch (Exception ex)
                {
                    sb.Append(string.Format("查询问题点类型{0}失败：{1}", type, ex.ToString()));
                }
            }
            ErrorInfo += sb.ToString();
        }

        private void setCellGridAllInfo(Dictionary<int, List<CellGridInfo>> cgDic, Dictionary<string, List<CellGridAllInfo>> allDic,
            CellGridBlockInfo cg, LTECell cell)
        {
            foreach (CellGridInfo info in cgDic[cg.BlockID])
            {
                foreach (CellGridAllInfo all in allDic["0-" + cg.CI.ToString()])
                {
                    if (info.TlLongitude == all.TlLongitude && info.TlLatitude == all.TlLatitude)
                    {
                        all.InBlock = 1;
                        all.Extend = info.Extend;
                    }
                    if (cell != null)
                    {
                        all.LteCell = cell;
                    }
                }
            }
        }

        private void showResultForm()
        {
            CellGridBlockResultForm fm = MainModel.CreateResultForm(typeof(CellGridBlockResultForm)) as CellGridBlockResultForm;
            fm.FillData(ResultList, condition);
            fm.Visible = true;
            fm.BringToFront();

            ResultList = null;
        }
    }

    public class CellGridCondition
    {
        private readonly List<string> types = new List<string>();
        public List<string> Types
        {
            get { return types; }
        }
        public void AddType(string type)
        {
            this.types.Add(type);
        }

        private readonly List<LabColorText> lbList = new List<LabColorText>();
        public List<LabColorText> LbList
        {
            get
            {
                return lbList;
            }
        }

        public void AddLb(LabColorText lb)
        {
            lbList.Add(lb);
        }

        public Color RSRP141
        {
            get;
            set;
        }
        public Color RSRP110
        {
            get;
            set;
        }
        public Color RSRP105
        {
            get;
            set;
        }
        public Color RSRP100
        {
            get;
            set;
        }
        public Color RSRP95
        {
            get;
            set;
        }
        public Color RSRP80
        {
            get;
            set;
        }
        public Color RSRP75
        {
            get;
            set;
        }
        public Color RSRP40
        {
            get;
            set;
        }
    }

    public class LabColorText
    {
        public LabColorText(string text, Color color)
        {
            this.text = checkTxt + " " + text;
            this.color = color;
        }
        public Color color
        {
            get;
            set;
        }
        public string text { get; set; }
        public bool isCheck { get; set; } = true;
        public string checkTxt
        {
            get
            {
                if (isCheck)
                {
                    return "[√]";
                }
                else
                {
                    return "[  ]";
                }
            }
        }
    }
}
