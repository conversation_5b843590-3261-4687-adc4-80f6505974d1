﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.Net
{
    class ZTDIYLeakOutCellSetScanNBIOT : ZTDIYLeakOutCellSetScanLTE
    {
        public ZTDIYLeakOutCellSetScanNBIOT(ServiceName serviceName)
            : base(serviceName)
        {
        }

        public override string Name
        {
            get { return "室分外泄分析_NBIOT扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 33000, 33007, this.Name);
        }
    }
}
