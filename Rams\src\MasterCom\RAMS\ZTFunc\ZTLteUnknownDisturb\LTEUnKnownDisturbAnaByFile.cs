﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTEUnKnownDisturbAnaByFile : LTEUnKnownDisturbAnaByRegion
    {
        private LTEUnKnownDisturbAnaByFile()
            : base()
        {
        }

        private static LTEUnKnownDisturbAnaByFile intance = null;
        public static new LTEUnKnownDisturbAnaByFile GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new LTEUnKnownDisturbAnaByFile();
                    }
                }
            }
            return intance;
        }

        public override string Name
        {
            get { return "不明干扰(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }

    }

    public class LTEUnKnownDisturbAnaByFile_FDD : LTEUnKnownDisturbAnaByRegion_FDD
    {
        public LTEUnKnownDisturbAnaByFile_FDD()
            : base()
        {

        }

        private static LTEUnKnownDisturbAnaByFile_FDD instance = null;
        public static new LTEUnKnownDisturbAnaByFile_FDD GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new LTEUnKnownDisturbAnaByFile_FDD();
                    }
                }
            }
            return instance;
        }
        public override string Name
        {
            get { return "不明干扰LTE_FDD(按文件)"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.File;
        }

        protected override void queryFileToAnalyse()
        {
            MainModel.FileInfos = Condition.FileInfos;
        }

        protected override bool isValidCondition()
        {
            if (Condition.FileInfos.Count > 0)
            {
                return true;
            }
            return false;
        }

        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            return true;
        }

        /// <summary>
        /// 判断是否在所选区域内
        /// </summary>
        /// <param name="tp"></param>
        /// <returns></returns>
        protected override bool isValidTestPoint(TestPoint testPoint)
        {
            return true;
        }
    }
}
