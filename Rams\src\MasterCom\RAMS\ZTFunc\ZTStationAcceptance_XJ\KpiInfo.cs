﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;

namespace MasterCom.RAMS.ZTFunc.ZTStationAcceptance_XJ
{
    public class DataKpiInfo
    {
        public bool IsValid { get; set; } = false;
        public int Divisor { get; set; } = 0;
        public double Dividend { get; set; } = 0;
        public double Data { get; set; }
        public double MaxData { get; set; } = -99999;
        public double MinData { get; set; } = 99999;

        public void Calculate()
        {
            if (Divisor > 0)
            {
                Data = Math.Round(Dividend / Divisor, 2);
            }
        }

        public void Add(double? value, bool getMaxMinData = false)
        {
            if (value == null)
            {
                return;
            }

            Divisor++;
            Dividend += (double)value;
            if (getMaxMinData)
            {
                MaxData = Math.Max(MaxData, value.Value);
                MinData = Math.Min(MinData, value.Value);
            }
        }
    }

    public class SuccessRateKpiInfo
    {
        public bool IsValid { get; set; } = false;
        //事件请求数
        public int RequestCnt { get; set; } = 0;
        //事件成功数
        public int SucceedCnt { get; set; } = 0;
        //事件失败数
        public int FailedCnt { get; set; } = 0;
        //成功率(没有乘100,有需要的话自行使用时乘100)
        public double SuccessRate { get; set; } = 0;

        public void Calculate()
        {
            if (RequestCnt != 0)
            {
                SuccessRate = SucceedCnt * 1d / RequestCnt;
            }
            else
            {
                SuccessRate = 0;
            }
        }

        public void Judge(int count, double rate)
        {
            if (RequestCnt >= count && SuccessRate >= rate)
            {
                IsValid = true;
            }
        }

        public void CalculateFailEvt()
        {
            FailedCnt = RequestCnt - SucceedCnt;
        }

        public void CalculateRequestEvt()
        {
            RequestCnt = SucceedCnt + FailedCnt;
        }
    }

    public class PicKpiInfo
    {
        //public PicInfo(int width, int height)
        //{
        //    Width = width;
        //    Height = height;
        //}

        //public int Width { get; set; }
        //public int Height { get; set; }

        public string PicPath { get; set; }
    }
}
