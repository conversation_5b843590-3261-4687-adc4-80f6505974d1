﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;

using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.NOP.WF;
using MasterCom.NOP.WF.Core;

namespace MasterCom.RAMS.NOP.BatchImport
{
    public class BatchImportItem
    {
        public BatchImportItem(string name, Task nopTask, DataRow fileRow)
        {
            Name = name;
            NopTask = nopTask;
            FileRow = fileRow;
        }

        public string Name
        {
            get;
            set;
        }

        public Task NopTask
        {
            get;
            set;
        }

        public DataRow FileRow
        {
            get;
            set;
        }

        public bool IsValid
        {
            get;
            set;
        }

        public string ResultDesc
        {
            get { return IsValid ? "成功" : "失败"; }
        }

        public string InvalidReason
        {
            get;
            set;
        }
    }

    public class BatchImportController
    {
        public BatchImportController(MainModel mainModel)
        {
            this.mainModel = mainModel;
        }

        public List<BatchImportItem> Load(string fileName, TaskOrderQuerier taskQuerier)
        {
            return Load(fileName, null, taskQuerier);
        }

        public List<BatchImportItem> Load(string fileName, string sheetName, TaskOrderQuerier taskQuerier)
        {
            DataTable fileTable = LoadFileTable(fileName, sheetName);
            if (fileTable == null || fileTable.Rows.Count == 0)
            {
                throw (new Exception("从文件中加载信息失败，请检查文件有效性"));
            }
            Dictionary<string, DataRow> fileRowDic = new Dictionary<string, DataRow>();
            foreach (DataRow dr in fileTable.Rows)
            {
                string rowName = dr["工单名称"] as string;
                if (!string.IsNullOrEmpty(rowName))
                {
                    fileRowDic[rowName] = dr;    
                }
            }

            ICollection<Task> tasks = taskQuerier.Query();
            List<BatchImportItem> retList = new List<BatchImportItem>();
            foreach (Task task in tasks)
            {
                if (fileRowDic.ContainsKey(task.Name))
                {
                    BatchImportItem item = new BatchImportItem(task.Name, task, fileRowDic[task.Name]);
                    retList.Add(item);
                }
            }

            return retList;
        }

        public void Import(List<BatchImportItem> importList)
        {
            string hostname = NopCfgMngr.Instance.NopServer.Ip;
            int port = NopCfgMngr.Instance.NopServer.Port;
            string username = this.mainModel.User.LoginName;
            string password = NopCfgMngr.Instance.Password;

            WFNetAdapter adapter = new WFNetAdapter(hostname, port, username, password);
            foreach (BatchImportItem item in importList)
            {
                if (!item.IsValid)
                {
                    continue;
                }

                Task retTask = adapter.ModifyTask(item.NopTask);
                if (retTask == null)
                {
                    item.IsValid = false;
                    item.InvalidReason = "提交失败";
                }
                else
                {
                    item.NopTask = retTask;
                }
            }
        }

        private DataTable LoadFileTable(string fileName, string sheetName)
        {
            List<DataTable> dts = ExcelOleDbReader.ReadTables(fileName);
            if (string.IsNullOrEmpty(sheetName))
            {
                return dts[0];
            }
            foreach (DataTable dt in dts)
            {
                if (dt.TableName == sheetName)
                {
                    return dt;
                }
            }
            throw (new Exception(string.Format("表格[{0}]不存在于文件中", sheetName)));
        }

        private readonly MainModel mainModel;
    }
}
