﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class XtraFormStatusToMustTest : DevExpress.XtraEditors.XtraForm
    {
        MainModel MainModel;
        List<string> roundid;
        public XtraFormStatusToMustTest(MainModel mainModel)
        {
            InitializeComponent();
            MainModel = mainModel;
            DiySqlQueryGridRoadRound diysql = new DiySqlQueryGridRoadRound(mainModel);
            diysql.Query();
            roundid = diysql.GridList;

            comboBox1.Items.Add("请选择");
            for (int i = 1; i < roundid.Count+1; i++)
            {
                comboBox1.Items.Add("第" + i + "轮 - " + roundid[i-1].Substring(12,7));
            }

            comboBox1.SelectedIndex = comboBox1.Items.Count-1;
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (nudType3.Value < nudType2.Value)
            {
                MessageBox.Show("持续差道路占比与已渗透道路占比有冲突,请检查~");
                return;
            }

            if (comboBox1.SelectedIndex == 0)
            {
                MessageBox.Show("请选择轮次~");
                return;
            }

            this.DialogResult = DialogResult.OK;

        }

        private void button1_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }


        public void getStatusSelect(out int type1,out double type2 ,out double type3,out string myroundid)
        {
            type1 = (int)nudType1.Value;
            type2 = (double)nudType2.Value;
            type3 = (double)nudType3.Value;
            myroundid = roundid[comboBox1.SelectedIndex-1];
        }
    }
}