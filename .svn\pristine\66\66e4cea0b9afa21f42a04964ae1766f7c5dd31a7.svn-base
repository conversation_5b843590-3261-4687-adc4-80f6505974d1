﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRScanModRoadQueryByFile : ModRoadQueryByFile
    {
        public NRScanModRoadQueryByFile(MainModel mm)
            : base(mm, new NRScanModRoadQueryQuery(mm))
        {
        }
    }

    public class NRScanModRoadQueryByRegion : ModRoadQueryByRegion
    {
        public NRScanModRoadQueryByRegion(MainModel mm)
            : base(mm, new NRScanModRoadQueryQuery(mm))
        {
        }
    }

    public class NRScanModRoadQueryQuery : ModRoadQueryBase
    {
        public NRScanModRoadQueryQuery(MainModel mm) : base(mm)
        {
        }

        public override List<string> Columns
        {
            get
            {
                return NRTpHelper.InitNrScanParamBackground();

                //return new List<string>()
                //{
                //    "isampleid",
                //    "itime",
                //    "ilongitude",
                //    "ilatitude",
                //    "LTESCAN_TopN_PSS_RP",
                //    "LTESCAN_TopN_EARFCN",
                //    "LTESCAN_TopN_PCI",
                //    "LTESCAN_TopN_CELL_Specific_RSRP",
                //};
            }
        }

        public override string Name
        {
            get { return "道路PCI模间干扰_NR扫频"; }
        }

        public override ModRoadStaterBase Stater
        {
            get { return this.stater; }
        }

        public override bool isValidCondition()
        {
            if (setForm == null || setForm.IsDisposed)
            {
                setForm = new NRScanModRoadSettingDlg();
            }
            if (setForm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
            {
                return false;
            }

            cond = setForm.GetCondition();
            stater = new NRScanModRoadQueryStater(mainModel, cond);
            curRoad = null;
            return true;
        }

        public override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 36000, 36013, Name);//////
        }

        public override void FireShowForm()
        {
            mainModel.FireSetDefaultMapSerialTheme(NRTpHelper.NrScanTpManager.RsrpFullThemeName);

            NRScanModRoadResultForm resultForm = MainModel.GetInstance().CreateResultForm(typeof(NRScanModRoadResultForm)) as NRScanModRoadResultForm;
            resultForm.FillData(stater, cond);
            resultForm.Visible = true;
            resultForm.BringToFront();
        }

        protected override ModRoadItemBase CreateNewRoad(TestPoint firstPoint, string fileName)
        {
            return new NRScanModRoadQueryExtent(firstPoint, fileName, cond);
        }

        private NRScanModRoadSettingDlg setForm;
        private NRScanModRoadCondition cond;
        private NRScanModRoadQueryStater stater;
    }
}
