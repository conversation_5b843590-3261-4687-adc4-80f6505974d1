﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.Util;
using System.Windows.Forms;
using MasterCom.RAMS.ZTFunc.ImportProjectParameterCond;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using System.IO;

namespace MasterCom.RAMS.ZTFunc
{
    class ZTImportProjectPermeter : QueryBase
    {
        private readonly Dictionary<string, int> dicColName2Index = null;
        private CondSet cond = new CondSet();
        private readonly ValueSet valueSet = new ValueSet();
        public string lastError = null;

        public ZTImportProjectPermeter(MainModel mainModel)
            :base(mainModel)
        {
            this.dicColName2Index = new Dictionary<string, int>();
            dicColName2Index[ProjectParameterColString.baseStationName] = -1;
            dicColName2Index[ProjectParameterColString.cellName] = -1;
            dicColName2Index[ProjectParameterColString.antennaName] = -1;
            dicColName2Index[ProjectParameterColString.lac] = -1;
            dicColName2Index[ProjectParameterColString.cid] = -1;
            dicColName2Index[ProjectParameterColString.Tac] = -1;
            dicColName2Index[ProjectParameterColString.cellId] = -1;
            dicColName2Index[ProjectParameterColString.eNodeBid] = -1;
            dicColName2Index[ProjectParameterColString.networkType] = -1;
            dicColName2Index[ProjectParameterColString.electricalDowntilt] = -1;
            dicColName2Index[ProjectParameterColString.azimuth] = -1;
            dicColName2Index[ProjectParameterColString.hangHeight] = -1;
            dicColName2Index[ProjectParameterColString.latitude_Bts] = -1;
            dicColName2Index[ProjectParameterColString.longitude_Bts] = -1;
            dicColName2Index[ProjectParameterColString.latitude_Ant] = -1;
            dicColName2Index[ProjectParameterColString.longitude_Ant] = -1;
        }

        private bool readFile_Csv(string fileName, ref string[] cols, ref List<string[]> listData)
        {
            CSVReader reader = new CSVReader(fileName);
            listData = reader.GetAllData();
            if (listData == null || listData.Count == 0)
            {
                this.lastError = reader.lastError;
                return false;
            }
            cols = listData[0];
            listData.RemoveAt(0);
            return true;
        }

        private bool readFile_Excel(string fileName, ref string[] cols, ref List<string[]> listData)
        {
            ExcelNPOIReader reader = new ExcelNPOIReader(fileName);
            cols = reader.GetColumns().ToArray();
            int colCount = cols.Length;
            string[] cells;
            ExcelNPOITable t = reader.GetTable();
            for (int i = 0; i < t.CellValues.Count; i++)
            {
                cells = new string[colCount];
                object[] obs = t.CellValues[i];
                for (int j = 0; j < colCount; j++)
                {
                    cells[j] = obs[j] == null ? null : obs[j].ToString();
                }
                listData.Add(cells);
            }
            return true;
        }

        private string getColumnIndex(string[] cols)
        {

            for (int i = 0; i < cols.Length; i++)
            {
                string col = cols[i];
                if (this.dicColName2Index.ContainsKey(col))
                {
                    this.dicColName2Index[col] = i;
                }
            }
            bool lackCol = false;
            StringBuilder sb = new StringBuilder();
            sb.Append("工参文件格式不正确，没有找到以下字段: \n");
            foreach (string col in this.dicColName2Index.Keys)
            {
                if (this.dicColName2Index[col] < 0)
                {
                    sb.Append(col + "   ");
                    lackCol = true;
                }
            }
            if (lackCol)
            {
                sb.Append("\n拒绝导入工参 !");
                return sb.ToString();
            }
            return null;
        }
        private List<string[]> readFile(string fileName) 
        {
            try
            {
                List<string[]> listData = new List<string[]>();
                string[] cols = new string[1];
                bool readSuccess;
                if (Path.GetExtension(fileName).ToUpper().Equals(".CSV"))
                {
                    readSuccess = readFile_Csv(fileName, ref cols, ref listData);
                }
                else
                {
                    readSuccess = readFile_Excel(fileName, ref cols, ref listData);
                }
                if (!readSuccess)
                {
                    return new List<string[]>();
                }

                string result = this.getColumnIndex(cols);
                if (result != null)
                {
                    MessageBox.Show(result);
                    return new List<string[]>();
                }
                return listData;
            }
            catch (Exception ex)
            {
                this.lastError = ex.Message;
                return new List<string[]>();
            }
        }

        private bool importDatar(ValueSet valueSet)
        {
            try
            {
                if (valueSet.networkType.Equals(NetTypeString.NT_GSM900)
                    || valueSet.networkType.Equals(NetTypeString.NT_DSC1800)
                    || valueSet.networkType.Equals(NetTypeString.NT_CoSite0)
                    || valueSet.networkType.Equals(NetTypeString.NT_2G))
                { 
                    return this.importData_2G(valueSet);
                }
                else if (valueSet.networkType.Equals(NetTypeString.NT_3G))
                {
                    return this.importData_3G(valueSet);
                }
                else if (valueSet.networkType.Equals(NetTypeString.NT_4G))
                {
                    return this.importData_4G(valueSet);
                }
                return false ;
            }
            catch (Exception ex)
            {
                this.lastError = ex.Message;
                return false;
            }
        }
        private int cellSN = 1;
        private int antSN = 1;
        private int btsSN = 1;
        private bool importData_4G(ValueSet valueSet)
        {
            int intTem = 0;
            double doubleTem = 0;
            CellManager cm = CellManager.GetInstance();

            LTEBTS bts = new LTEBTS();
            bts.Name = valueSet.baseStationName;

            if (!double.TryParse(valueSet.longitude_Bts, out doubleTem)) return false;
            bts.Longitude = doubleTem;

            if (!double.TryParse(valueSet.latitude_Bts, out doubleTem)) return false;
            bts.Latitude = doubleTem;

            bts.Type = LTEBTSType.Outdoor;
            bts.ID = this.btsSN++;
            bts.ValidPeriod = new TimePeriod(new DateTime(0), DateTime.MaxValue);

            LTECell cell = new LTECell();
            cell.BelongBTS = bts;

            if (!int.TryParse(valueSet.Tac, out intTem)) return false;
            cell.TAC = intTem;

            if (!int.TryParse(valueSet.cellId, out intTem)) return false;
            cell.CellID = intTem;

            //  ECI/256*10+SectorID = EnodeBID * 10 + sectorID = CellID
            //  sectorID = CellID - EnodeBID * 10
            //  ECI = EnodeBID * 256 + sectorID
            if (!int.TryParse(valueSet.eNodeBid, out intTem)) return false;
            //cell.ECI = intTem * 256 + (cell.CellID - intTem*10);
            cell.ECI = cell.CellID;//由于新疆现场工参文件提供的工参不全且无法保证稳定，2016.06.03 所能提供的cellID其实是eci来的，特此修改，后人小心，阿门！！
            cell.CellID = 0;

            cell.Name = valueSet.cellName;
            cell.ID = this.cellSN++;
            cell.ValidPeriod = new TimePeriod(new DateTime(0), DateTime.MaxValue);

            LTEAntenna ant = new LTEAntenna();
            ant.Cell = cell;
            if (!double.TryParse(valueSet.electricalDowntilt, out doubleTem)) return false;
            ant.Downward = (short)doubleTem;

            if (!double.TryParse(valueSet.azimuth, out doubleTem)) return false;
            ant.Direction = (short)doubleTem;

            if (!double.TryParse(valueSet.hangHeight, out doubleTem)) return false;
            ant.Altitude = (int)doubleTem;

            if (!double.TryParse(valueSet.latitude_Ant, out doubleTem)) return false;
            ant.Latitude = doubleTem;

            if (!double.TryParse(valueSet.longitude_Ant, out doubleTem)) return false;
            ant.Longitude = doubleTem;

            ant.ID = this.antSN++;
            ant.ValidPeriod = new TimePeriod(new DateTime(0), DateTime.MaxValue);

            cm.Add(bts);
            cm.Add(cell);
            cm.Add(ant);

            return true;
        }
        private bool importData_3G(ValueSet valueSet)
        {
            int intTem = 0;
            double doubleTem = 0;
            CellManager cm = CellManager.GetInstance();

            TDNodeB bts = new TDNodeB();
            bts.Name = valueSet.baseStationName;

            if (!double.TryParse(valueSet.longitude_Bts, out doubleTem)) return false;
            bts.Longitude = doubleTem;

            if (!double.TryParse(valueSet.latitude_Bts, out doubleTem)) return false;
            bts.Latitude = doubleTem;

            bts.Type = TDNodeBType.Outdoor;
            bts.ID = this.btsSN++;
            bts.ValidPeriod = new TimePeriod(new DateTime(0), DateTime.MaxValue);

            TDAntenna ant = new TDAntenna();
            if (!double.TryParse(valueSet.electricalDowntilt, out doubleTem)) return false;
            ant.Downward = (short)doubleTem;

            if (!double.TryParse(valueSet.azimuth, out doubleTem)) return false;
            ant.Direction = (short)doubleTem;

            if (!double.TryParse(valueSet.hangHeight, out doubleTem)) return false;
            ant.Altitude = (int)doubleTem;

            if (!double.TryParse(valueSet.latitude_Ant, out doubleTem)) return false;
            ant.Latitude = doubleTem;

            if (!double.TryParse(valueSet.longitude_Ant, out doubleTem)) return false;
            ant.Longitude = doubleTem;

            ant.ID = this.antSN++;
            ant.ValidPeriod = new TimePeriod(new DateTime(0), DateTime.MaxValue);

            TDCell cell = new TDCell();
            cell.BelongBTS = bts;
            cell.Antenna = ant;
            cell.LAC = -1;
            cell.CI = -1;
            if (!int.TryParse(valueSet.lac, out intTem)) return false;
            cell.LAC = intTem;

            if (!int.TryParse(valueSet.cid, out intTem)) return false;
            cell.CI = intTem;

            cell.Name = valueSet.cellName;
            cell.ID = this.cellSN++;
            cell.ValidPeriod = new TimePeriod(new DateTime(0), DateTime.MaxValue);

            cm.Add(bts);
            cm.Add(ant);
            cm.Add(cell);

            return true;
        }
        private bool importData_2G(ValueSet valueSet)
        {
            int intTem = 0;
            double doubleTem = 0;
            CellManager cm = CellManager.GetInstance();

            BTS bts = new BTS();
            bts.Name = valueSet.baseStationName;

            if (!double.TryParse(valueSet.longitude_Bts, out doubleTem)) return false;
            bts.Longitude = doubleTem;

            if (!double.TryParse(valueSet.latitude_Bts, out doubleTem)) return false;
            bts.Latitude = doubleTem;

            if (valueSet.networkType.Equals(NetTypeString.NT_GSM900))
            {
                bts.BandType = BTSBandType.GSM900;
            }
            else if (valueSet.networkType.Equals(NetTypeString.NT_DSC1800))
            {
                bts.BandType = BTSBandType.DSC1800;
            }
            else if (valueSet.networkType.Equals(NetTypeString.NT_CoSite0))
            {
                bts.BandType = BTSBandType.CoSite;
            }
            else
            {
                bts.BandType = BTSBandType.Other;
            }
            bts.Type = BTSType.Outdoor;
            bts.ID = this.btsSN++;
            bts.ValidPeriod = new TimePeriod(new DateTime(0), DateTime.MaxValue);


            Cell cell = new Cell();
            cell.BelongBTS = bts;
            cell.LAC = -1;
            cell.CI = -1;
            if (!int.TryParse(valueSet.lac, out intTem)) return false;
            cell.LAC = intTem;

            if (!int.TryParse(valueSet.cid, out intTem)) return false;
            cell.CI = intTem;

            cell.Name = valueSet.cellName;
            cell.ID = this.cellSN++;
            cell.ValidPeriod = new TimePeriod(new DateTime(0), DateTime.MaxValue);

            Antenna ant = new Antenna();
            ant.BelongCell = cell;
            if (!double.TryParse(valueSet.electricalDowntilt, out doubleTem)) return false;//下倾角
            ant.Downward = (short)doubleTem;

            if (!double.TryParse(valueSet.azimuth, out doubleTem)) return false;//方位角
            ant.Direction = (short)doubleTem;

            if (!double.TryParse(valueSet.hangHeight, out doubleTem)) return false;
            ant.Altitude = (int)doubleTem;

            if (!double.TryParse(valueSet.latitude_Ant, out doubleTem)) return false;
            ant.Latitude = doubleTem;

            if (!double.TryParse(valueSet.longitude_Ant, out doubleTem)) return false;
            ant.Longitude = doubleTem;

            ant.ID = this.antSN++;
            ant.ValidPeriod = new TimePeriod(new DateTime(0), DateTime.MaxValue);

            cm.Add(bts);
            cm.Add(cell);
            cm.Add(ant);

            return true;
        }
        protected override bool isValidCondition()
        {
            return true;
        }

        public override bool IsNeedSetQueryCondition
        {
            get { return false; }
        }
        public override string Name
        {
            get { return "导入工参"; }
        }

        protected override void query()
        {
            ZTImportProjectParameterCondSetting condForm = new ZTImportProjectParameterCondSetting();
            condForm.SetCond(this.cond);
            if (condForm.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            this.cond = condForm.GetCond();
            List<string[]> listRow = this.readFile(this.cond.fileName);
            if (listRow == null || listRow.Count == 0)
            {
                MessageBox.Show("读取工参失败!\n" + this.lastError);
                return;
            }

            this.cellSN = 1;
            this.antSN = 1;
            this.btsSN = 1;
            this.valueSet.dicColName2Index = this.dicColName2Index;
            int badItem = 0;
            int successItem = 0;
            CellManager cm = CellManager.GetInstance();
            cm.Clear();
            foreach(object[] row in listRow)
            {
                if (!this.valueSet.fillData(row))
                {
                    badItem++;
                    continue;
                }
                if (!this.importDatar(this.valueSet))
                {
                    badItem++;
                    continue;
                }
                successItem++;
            }
            string mesg = string.Format("导入工参成功!\n共有 {1} 条!\n成功导入 {0} 条!", successItem, badItem+successItem);
            if (badItem > 0)
            {
                mesg += string.Format("\n由于数据异常剔除 {0} 条!", badItem);
            }
            MessageBox.Show(mesg);
        }
    }

    static class ProjectParameterColString
    {
        public const string baseStationName = "基站名称";
        public const string cellName = "小区名称";
        public const string antennaName = "天线名称";
        public const string lac = "LAC";
        public const string cid = "CI";
        public const string Tac = "Tac";
        public const string networkType = "网络类型";
        public const string electricalDowntilt = "下倾角";
        public const string azimuth = "方位角";
        public const string hangHeight = "挂高";
        public const string latitude_Bts = "基站纬度";
        public const string longitude_Bts = "基站经度";
        public const string latitude_Ant = "天线纬度";
        public const string longitude_Ant = "天线经度";
        public const string cellId = "CellId";
        public const string eNodeBid = "eNodeBid";

    }

    static class NetTypeString
    {
        public const string NT_GSM900 = "GSM900";
        public const string NT_DSC1800 = "DSC1800";
        public const string NT_CoSite0 = "CoSite";
        public const string NT_2G = "2G";
        public const string NT_3G = "3G";
        public const string NT_4G = "4G";
    }

    class ValueSet
    {
        public Dictionary<string, int> dicColName2Index = null;

        public string baseStationName = null;
        public string cellName = null;
        public string antennaName = null;
        public string lac = null;
        public string cid = null;
        public string Tac = null;
        public string cellId = null;
        public string eNodeBid = null;
        public string networkType = null;
        public string electricalDowntilt = null;
        public string azimuth = null;
        public string hangHeight = null;
        public string latitude_Bts = null;
        public string longitude_Bts = null;
        public string latitude_Ant = null;
        public string longitude_Ant = null;
        public bool fillData(object[] row)
        {
            try
            {
                baseStationName = row[this.dicColName2Index[ProjectParameterColString.baseStationName]] as string;
                cellName = row[this.dicColName2Index[ProjectParameterColString.cellName]] as string;
                antennaName = row[this.dicColName2Index[ProjectParameterColString.antennaName]] as string;
                lac = row[this.dicColName2Index[ProjectParameterColString.lac]] as string;
                cid = row[this.dicColName2Index[ProjectParameterColString.cid]] as string;
                Tac = row[this.dicColName2Index[ProjectParameterColString.Tac]] as string;
                cellId = row[this.dicColName2Index[ProjectParameterColString.cellId]] as string;
                eNodeBid = row[this.dicColName2Index[ProjectParameterColString.eNodeBid]] as string;
                networkType = row[this.dicColName2Index[ProjectParameterColString.networkType]] as string;
                electricalDowntilt = row[this.dicColName2Index[ProjectParameterColString.electricalDowntilt]] as string;
                azimuth = row[this.dicColName2Index[ProjectParameterColString.azimuth]] as string;
                hangHeight = row[this.dicColName2Index[ProjectParameterColString.hangHeight]] as string;
                latitude_Bts = row[this.dicColName2Index[ProjectParameterColString.latitude_Bts]] as string;
                longitude_Bts = row[this.dicColName2Index[ProjectParameterColString.longitude_Bts]] as string;
                latitude_Ant = row[this.dicColName2Index[ProjectParameterColString.latitude_Ant]] as string;
                longitude_Ant = row[this.dicColName2Index[ProjectParameterColString.longitude_Ant]] as string;
                 
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
