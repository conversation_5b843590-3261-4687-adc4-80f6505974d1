using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.IO;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.Grid;
using MasterCom.MControls;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using MasterCom.RAMS.Net;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTDIYGSMCellOccupyListForm : MinCloseForm
    {
        public ZTDIYGSMCellOccupyListForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            mapForm = mainModel.MainForm.GetMapForm();
            init();
            DisposeWhenClose = true;
        }
        private MapForm mapForm = null;

        private void init()
        {
            this.olvColumnSN.AspectGetter = delegate(object row)
            {
                if (row is CellOccupyInfo)
                {
                    CellOccupyInfo item = row as CellOccupyInfo;
                    return item.SN;
                }
                return null;
            };

            this.olvColumnCellName.AspectGetter = delegate(object row)
            {
                if (row is CellOccupyInfo)
                {
                    CellOccupyInfo item = row as CellOccupyInfo;
                    return item.CellName;
                }
                return "";
            };

            this.olvColumnFileName.AspectGetter = delegate(object row)
            {
                if (row is CellOccupyInfo)
                {
                    CellOccupyInfo item = row as CellOccupyInfo;
                    return item.fileName;
                }
                return "";
            };

            this.colLastCi.AspectGetter = delegate(object row)
            {
                if (row is CellOccupyInfo)
                {
                    CellOccupyInfo item = row as CellOccupyInfo;
                    return item.LastCI;
                }
                return null;
            };

            this.colLastLac.AspectGetter = delegate(object row)
            {
                if (row is CellOccupyInfo)
                {
                    CellOccupyInfo item = row as CellOccupyInfo;
                    return item.LastLAC;
                }
                return null;
            };

            this.colNextCi.AspectGetter = delegate(object row)
            {
                if (row is CellOccupyInfo)
                {
                    CellOccupyInfo item = row as CellOccupyInfo;
                    return item.NextCI;
                }
                return null;
            };


            this.colNextLac.AspectGetter = delegate(object row)
            {
                if (row is CellOccupyInfo)
                {
                    CellOccupyInfo item = row as CellOccupyInfo;
                    return item.NextLAC;
                }
                return null;
            };

            this.olvColumnLAC.AspectGetter = delegate(object row)
            {
                if (row is CellOccupyInfo)
                {
                    CellOccupyInfo item = row as CellOccupyInfo;
                    return item.LAC;
                }
                return "";
            };

            this.olvColumnCI.AspectGetter = delegate(object row)
            {
                if (row is CellOccupyInfo)
                {
                    CellOccupyInfo item = row as CellOccupyInfo;
                    return item.CI;
                }
                return "";
            };

            this.olvColumnBeginTime.AspectGetter = delegate(object row)
            {
                if (row is CellOccupyInfo)
                {
                    CellOccupyInfo item = row as CellOccupyInfo;
                    return item.BeginTimeInString;
                }
                return "";
            };

            this.olvColumnEndTime.AspectGetter = delegate(object row)
            {
                if (row is CellOccupyInfo)
                {
                    CellOccupyInfo item = row as CellOccupyInfo;
                    return item.EndTimeInString;
                }
                return "";
            };

            this.olvColumnTime.AspectGetter = delegate(object row)
            {
                if (row is CellOccupyInfo)
                {
                    CellOccupyInfo item = row as CellOccupyInfo;
                    return item.totalTime;
                }
                return "";
            };

            this.olvColumnDistance.AspectGetter = delegate(object row)
            {
                if (row is CellOccupyInfo)
                {
                    CellOccupyInfo item = row as CellOccupyInfo;
                    return item.totalDistance;
                }
                return "";
            };
        }

        public void FillData(List<CellOccupyInfo> cellOccupyList)
        {
            ListViewCellOccupy.RebuildColumns();
            ListViewCellOccupy.ClearObjects();
            ListViewCellOccupy.SetObjects(cellOccupyList);
            if (cellOccupyList.Count > 0 && cellOccupyList[0].ServiceLst.Contains(ServiceType.GSM_VOICE))
            {
                MainModel.FireSetDefaultMapSerialTheme("GSM RxLevSub");
            }
            else if (cellOccupyList.Count > 0 && cellOccupyList[0].ServiceLst.Contains(ServiceType.TDSCDMA_VOICE))
            {
                MainModel.FireSetDefaultMapSerialTheme("TD_PCCPCH_RSCP");
            }
            MainModel.RefreshLegend();

            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GetCellLayer().Invalidate();
            }
        }

        private void listViewTotal_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (ListViewCellOccupy.SelectedObject is CellOccupyInfo)
            {
                CellOccupyInfo info = ListViewCellOccupy.SelectedObject as CellOccupyInfo;
                if (info.Cell is Cell)
                {
                    MainModel.SelectedCell = info.Cell as Cell;
                }
                else if (info.Cell is TDCell)
                {
                    MainModel.SelectedTDCell = info.Cell as TDCell;
                }

                mModel.DTDataManager.Clear();
                foreach (TestPoint tp in info.sampleLst)
                {
                    mModel.DTDataManager.Add(tp);
                }
                mModel.FireDTDataChanged(this);
            }
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            ListViewCellOccupy.ExpandAll();
        }

        private void miCallapsAll_Click(object sender, EventArgs e)
        {
            ListViewCellOccupy.CollapseAll();
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(ListViewCellOccupy);
        }
    }
}