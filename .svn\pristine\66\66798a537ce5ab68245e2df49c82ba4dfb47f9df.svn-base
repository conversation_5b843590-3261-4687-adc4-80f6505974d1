﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Func;
using System.IO;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class BackgroundFuncManager
    {
        private static string curDate;
        private static string backgroundLogSavePath;
        public static string BackgroundLogSavePath
        {
            get
            {
                string strDate = DateTime.Now.ToString("yyyyMMdd");
                if (curDate != strDate)
                {
                    backgroundLogSavePath = getLogPath();
                }
                return backgroundLogSavePath;
            }
            set {  backgroundLogSavePath = value; }
        }
        
        private BackgroundFuncManager()
        {
            initBackgroundFunc();
        }

        private static BackgroundFuncManager intance = null;

        public static BackgroundFuncManager GetInstance()
        {
            if (intance == null)
            {
                intance = new BackgroundFuncManager();
            }
            return intance;
        }

        public event EventHandler BackgroundInfoChanged;
        private void fireBackgroundInfoChanged()
        {
            if (BackgroundInfoChanged != null)
            {
                BackgroundInfoChanged(this, EventArgs.Empty);
            }
            writeLog(this.BackgroundInfo);
        }

        public string BackgroundInfo { get; set; } = "";
        public void ReportBackgroundLogInfo(string info)
        {
            BackgroundInfo = DateTime.Now.ToString() + "   " + info;
            writeLog(this.BackgroundInfo);
        }

        public void ReportBackgroundInfo(string info)
        {
            BackgroundInfo = DateTime.Now.ToString() + "   " + info;
            fireBackgroundInfoChanged();
        }

        public void ReportBackgroundError(string info)
        {
            BackgroundInfo = DateTime.Now.ToString() + "   错误：" + info;
            fireBackgroundInfoChanged();
        }
        public void ReportBackgroundError(Exception ee)
        {
            ReportBackgroundError(ee.Message + Environment.NewLine + ee.Source + Environment.NewLine + ee.StackTrace);
        }

        public List<BackgroundQueryBase> BackgroundQueryList { get; set; } = new List<BackgroundQueryBase>();
        List<BackgroundQueryBase> activeBackgroundQueryList { get; set; } = new List<BackgroundQueryBase>();//已开启的功能
        private void initBackgroundFunc()
        {
            BackgroundQueryList.Clear();
            #region GSM业务

            BackgroundQueryList.Add(ZTDIYGSMWeakCovRoadQueryByRegion.GetInstance());
            BackgroundQueryList.Add(ZTCellCoverLapByRegion.GetInstance());
            BackgroundQueryList.Add(ZTDIYLeakOutCellSetByRegion.GetInstance());
            BackgroundQueryList.Add(ZTDIYNoMainCellByRegion.GetInstance());
            BackgroundQueryList.Add(ZTQueryBadRxQualByRegion.GetInstance());
            BackgroundQueryList.Add(ZTDIYGSMPoorRxQualityRoadQueryByRegion.GetInstance());
            BackgroundQueryList.Add(ZTCellWrongDirQuery_GSM.GetInstance());
            BackgroundQueryList.Add(ZTGsmAntenna.GetInstance());

            BackgroundQueryList.Add(ZTDIYGSMCellOccupyQueryByRegion.GetInstance());

            BackgroundQueryList.Add(ZTDIYQueryHandoverPingPang.GetInstance());
            BackgroundQueryList.Add(ZTDIYQueryHandoverTooMuch.GetInstance());

            BackgroundQueryList.Add(ZTDIYCellSetByRegion.GetInstance());

            BackgroundQueryList.Add(ZTLowSpeedAnaByRegion_GSM.GetInstance());
            BackgroundQueryList.Add(ZTDIYLowSpeedCellByRegion.GetInstance());
            BackgroundQueryList.Add(CellSetByBackgroundQuery.GetInstance());
            #endregion

            #region TD业务

            BackgroundQueryList.Add(DIYNoCoverRoadByRegion_TDScan.GetInstance());
            BackgroundQueryList.Add(DIYWeakCoverTDPccpchRscpAnaByRegion.GetInstance());
            BackgroundQueryList.Add(ZTCellCoverLapByRegion_TD.GetInstance());
            BackgroundQueryList.Add(ZTDIYLeakOutCellSetByRegion_TD.GetInstance());
            BackgroundQueryList.Add(ZTDIYPilotFrequencyPolluteByRegion.GetInstance());
            BackgroundQueryList.Add(ZTDIYCellWrongDir_TD.GetInstance());
            BackgroundQueryList.Add(ZTTdAntenna.GetInstance());

            BackgroundQueryList.Add(ZTDIYQueryHandoverPingPangTD.GetInstance());
            BackgroundQueryList.Add(ZTDIYQueryHandoverTooMuchTD.GetInstance());
            BackgroundQueryList.Add(ZTDIYCellOccupyQueryByRegion_TD.GetInstance());

            BackgroundQueryList.Add(ZTDIYTDPoorBlerRoadQueryByRegion.GetInstance());
            BackgroundQueryList.Add(ZTDIYTDPoorBlerCellQueryByRegion.GetInstance());

            BackgroundQueryList.Add(ZTLowSpeedAnaByRegion_TD.GetInstance());
            BackgroundQueryList.Add(ZTDIYLowSpeedCellByRegion_TD.GetInstance());
            #endregion

            #region LTE业务
            BackgroundQueryList.Add(ZTLTEWeakCoverRoadQuery.GetInstance());//弱覆盖路段
            BackgroundQueryList.Add(OverCoverAnaByRegionFile_LTE.GetInstance());
            BackgroundQueryList.Add(ZTPilotFreqPolluteByRegion_LTE.GetInstance());
            BackgroundQueryList.Add(ZTLowSpeedAnaByRegion_LTE.GetInstance());
            BackgroundQueryList.Add(CellWrongDirQueryByRegion_LTE.GetInstance());//天馈分析
            BackgroundQueryList.Add(ZTLteAntenna.GetInstance());
            BackgroundQueryList.Add(FartherCoverQueryByRegion.GetInstance());//超远覆盖
            BackgroundQueryList.Add(LeakOutCellAnaByRegion_LTE.GetInstance());//室分外泄
            BackgroundQueryList.Add(ZTWeakSINRRoadQuery.GetInstance());//质差路段
            BackgroundQueryList.Add(ZTHightRSRPLowSINRQuery.GetInstance());//强信号sinr质差
            BackgroundQueryList.Add(ZTWeakSINRReason.GetInstance());//sinr质差原因分析
            BackgroundQueryList.Add(LTEModRoadQueryByRegion.GetInstance());//模三干扰...
            BackgroundQueryList.Add(ZTHandoverBehideTime_LTE.GetInstance());//切换不及时
            BackgroundQueryList.Add(ZTQueryHandoverFailInfo_LTE.GetInstance());//切换失败
            BackgroundQueryList.Add(ZTDIYQueryHandoverTooMuchLTE.GetInstance());//切换频繁
            BackgroundQueryList.Add(LteHandOverPingPangAna.GetInstance());//乒乓切换
            BackgroundQueryList.Add(ZTDIYQueryTAUHandoverTooMuchLTE.GetInstance());//跟踪区频繁更新
            BackgroundQueryList.Add(LteHandOverInReasonAnaByRegion.GetInstance());//切换不合理
            BackgroundQueryList.Add(LteHandOverTooSlowAnaByRegion.GetInstance());//切换过慢
            BackgroundQueryList.Add(ZTLTECauseValueAnaByRegion.GetInstance());//RRC重建分析
            BackgroundQueryList.Add(ZTLteNBCellCheckBothAnaByBegion.GetInstance());//邻区检测（信令）
            BackgroundQueryList.Add(LteLastRoadSceneQuery.GetInstance());//场景分析
            BackgroundQueryList.Add(CSFBCauseQueryAna.GetInstance());//CSFB未接通原因分析
            BackgroundQueryList.Add(UltraSiteQuery.GetInstance());//三超站点分析

#if StationAccept_HB
            BackgroundQueryList.Add(StationAcceptAna_HB.GetInstance());
#endif

#if StationAccept_SXJin
            BackgroundQueryList.Add(StationAcceptAna_SXJin.GetInstance());
#endif

#if StationAccept_XJ
            BackgroundQueryList.Add(MultiStationExportReportAna.GetInstance());
            BackgroundQueryList.Add(MultiStationAcceptAna.GetInstance());
#endif

#if StationAccept_SX
            BackgroundQueryList.Add(StationAcceptAna_SX.GetInstance());
            BackgroundQueryList.Add(StationAcceptAna_SX_NR.GetInstance());
#endif

#if StationAccept_App
            BackgroundQueryList.Add(StationKpiAccept_SX.GetInstance());
#endif

#if StationAccept_GX
            BackgroundQueryList.Add(StationAcceptAna_GX.GetInstance());
#endif

#if CellKpiAutoExport
            BackgroundQueryList.Add(CellKpiAuotExportAna.GetInstance());
#endif

#if StationAccept_GZ
            BackgroundQueryList.Add(StationAcceptAna_GZ.GetInstance());
#endif

#if StationAccept_QH
            BackgroundQueryList.Add(StationAcceptAna_QH.GetInstance());
#endif

#if StationAccept_TJ
            BackgroundQueryList.Add(StationAccept_TianJin.GetInstance());
#endif
            #endregion

            #region GSM扫频
            BackgroundQueryList.Add(DIYNoCoverRoadByRegion_GScan.GetInstance());
            BackgroundQueryList.Add(ZTDIYCellWeakCoverByCellDir_GScan.GetInstance());
            BackgroundQueryList.Add(ZTDIYLeakOutCellSetScanGsmRscpAnaByRegion.GetInstance());
            BackgroundQueryList.Add(ZTDIYCellWrongDir_GScan.GetInstance());
            BackgroundQueryList.Add(ZTCellCoverLapByRegion_GScan.GetInstance());
            BackgroundQueryList.Add(ZTGsmScanAntenna.GetInstance());

            BackgroundQueryList.Add(ZTDiyNoCellCoverQueryByRegion.GetInstance());
            BackgroundQueryList.Add(ZTDiyScanFarCellQueryByRegion.GetInstance());

            BackgroundQueryList.Add(ZTDiyCellMultiCoverageQueryByRegion.GetInstance());
            BackgroundQueryList.Add(ZTDiyScanCellRedundantCovQueryByRegion.GetInstance());
            #endregion

            #region TD扫频
            BackgroundQueryList.Add(DIYNoCoverRoadByRegion_TDScan.GetInstance());
            BackgroundQueryList.Add(ZTDIYCellWeakCoverByCellDir_TDScan.GetInstance());
            BackgroundQueryList.Add(ZTWeakCoverByRegion_TDScan.GetInstance());
            BackgroundQueryList.Add(ZTCellCoverLapByRegion_TDScan.GetInstance());
            BackgroundQueryList.Add(ZTDIYLeakOutCellSetScanTDRscpAnaByRegion.GetInstance());
            BackgroundQueryList.Add(ZTDIYPilotFrequencyPolluteByRegion_TDScan.GetInstance());
            BackgroundQueryList.Add(ZTDIYNonconformity.GetInstance());
            BackgroundQueryList.Add(ZTTdScanAntenna.GetInstance());
            
            BackgroundQueryList.Add(ZTDiyNoCellCoverQueryByRegion_TD.GetInstance());
            BackgroundQueryList.Add(ZTDiyScanFarCellQueryByRegion_TD.GetInstance());

            BackgroundQueryList.Add(DIYWeakC_IRoadByRegion.GetInstance());

            BackgroundQueryList.Add(ZTDiyCellMultiCoverageQueryByRegion_TD.GetInstance());
            BackgroundQueryList.Add(ZTDiyScanCellRedundantCovQueryByRegion_TD.GetInstance());
            #endregion

            #region LTE扫频
            BackgroundQueryList.Add(ZTCellMultiCoverage_LTE.GetInstance());
            BackgroundQueryList.Add(ZTDIYLeakOutCellSetScanLTE.GetInstance());
            BackgroundQueryList.Add(ZTCellWrongDirQuery_LScan.GetInstance());
            BackgroundQueryList.Add(ZTDiyNoCellCoverQueryByRegion_LTE.GetInstance());
            BackgroundQueryList.Add(ZTDiyScanFarCellQueryByRegion_LTE.GetInstance());
            BackgroundQueryList.Add(ZTLteScanAntenna.GetInstance());
            BackgroundQueryList.Add(ZTLteMiMoAntenna.GetInstance());
            BackgroundQueryList.Add(ZTLTEScanHighCoverateRoadQueryByRegion.GetInstance());//高重叠覆盖道路
            #endregion

            BackgroundQueryList.Add(LowTaskFileManage.GetInstance());
#if NBIOT
            BackgroundQueryList.Add(NbIotStationAcceptAna.GetInstance());
#if StationAccept_XJ
            BackgroundQueryList.Add(NbIotStationAcceptAnaXJ.GetInstance());
#endif
#endif
        }

        public void RuncBackgroundFunc()
        {
            try
            {
                //如果未读取配置，先读取
                BackgroundFuncConfigManager configManager = BackgroundFuncConfigManager.GetInstance();

                bool haveActiveQueryList = initActiveQueryList();
                if (!haveActiveQueryList)
                {
                    return;
                }

                MainModel mainModel = MainModel.GetInstance();
                int curDistrictID = mainModel.DistrictID;
#if !DEBUG
                if (!configManager.IsNeedLoadCurCityWorkParam)
                {//若切换地市时不需切换工参，需更新初始地市工参（初始地市一般为省公司）
                    QuerySqlDate querySqlDate = new QuerySqlDate(MainModel.GetInstance());
                    querySqlDate.Query();
                    if (mainModel.ModelConfig.LocalSaveDate < querySqlDate.TimeStamp)
                    {
                        string curDistrictName = DistrictManager.GetInstance().getDistrictName(curDistrictID);
                        ReportBackgroundInfo("开始读取地市【" + curDistrictName + "】的小区信息...");
                        mainModel.ModelConfig.Init(true);
                    }
                }
#endif
                dealDistrictData(configManager, mainModel);

                foreach (BackgroundQueryBase query in activeBackgroundQueryList)
                {
                    query.DealAfterBackgroundQueryByCity();
                }

                resetCellInfo(configManager, mainModel, curDistrictID);

                mainModel.BackgroundStopRequest = false;
                ReportBackgroundInfo("分析结束。");
            }
            catch (Exception ee)
            {
                ReportBackgroundError(ee);
            }
            finally
            {
                activeBackgroundQueryList.Clear();
            }
        }

        private void resetCellInfo(BackgroundFuncConfigManager configManager, MainModel mainModel, int curDistrictID)
        {
            //如果最后计算地市不是当前地市，初始化为当前地市片区图层和小区信息
            if (mainModel.DistrictID != curDistrictID)
            {
                mainModel.DistrictID = curDistrictID;
                GISManager.GetInstance().ResetMap(this, null);
                if (configManager.IsNeedLoadCurCityWorkParam)
                {
                    string curDistrictName = DistrictManager.GetInstance().getDistrictName(curDistrictID);
                    ReportBackgroundInfo("恢复当前地市【" + curDistrictName + "】的小区信息...");
                    mainModel.ModelConfig.Init(true);
                }
            }
        }

        private void dealDistrictData(BackgroundFuncConfigManager configManager, MainModel mainModel)
        {
            string[] districtNames = DistrictManager.GetInstance().DistrictNames;

            for (int i = 0; i < districtNames.Length; i++)
            {
                string districtName = districtNames[i];
                if (!string.IsNullOrEmpty(districtName) && configManager.strSelCity.Contains(districtName))
                {

                    ReportBackgroundInfo("开始处理地市【" + districtName + "】的数据...");

                    //如果计算地市不是当前地市，初始化地市片区图层和小区信息
                    loadDistrictCellInfo(configManager, mainModel, i, districtName);

                    bool loadSuccess = loadDistrictMap(configManager, districtName);
                    if (loadSuccess)
                    {
                        dealBackgroundFunc(mainModel);

                        exportResult(configManager, mainModel, districtName);
                    }
                }

                if (mainModel.BackgroundStopRequest)
                {
                    break;
                }
            }
        }

        private bool loadDistrictMap(BackgroundFuncConfigManager configManager, string districtName)
        {
            if (configManager.IsNeedResetCurCityMap)
            {
                bool hasReset = BackgroundFuncBaseSetting.GetInstance().ResetMap();
                //初始化地市区域边界
                if (!hasReset)
                {
                    ReportBackgroundInfo("地市【" + districtName + "】的边界图层配置有误，请先配置。");
                    return false;
                }
            }
            return true;
        }

        private void exportResult(BackgroundFuncConfigManager configManager, MainModel mainModel, string districtName)
        {
            if (configManager.AutoExportResult && !mainModel.BackgroundStopRequest)
            {
                ReportBackgroundInfo("开始查询导出地市【" + districtName + "】的网络体检结果......");
                DIYBackgroundResultExport exportQuery = DIYBackgroundResultExport.GetInstance();
                exportQuery.SetCond(districtName, activeBackgroundQueryList);
                exportQuery.Query();
            }
        }

        private void dealBackgroundFunc(MainModel mainModel)
        {
            BackgroundFuncResultConditionCheck resultCondCheck = BackgroundFuncResultConditionCheck.GetInstance();
            //处理配置了的专题功能
            foreach (BackgroundQueryBase query in activeBackgroundQueryList)
            {
                if (mainModel.BackgroundStopRequest)
                {
                    break;
                }
                if (query.BackgroundStat)
                {
                    resultCondCheck.SetCond(query);
                    resultCondCheck.Query();

                    //********这里是要固定所有专题汇聚半径和持续路段长度门限的，还要看看有没有必要***********
                    if (query is DIYAnalyseByPeriodBackgroundBase_Sample)
                    {
                        DIYAnalyseByPeriodBackgroundBase_Sample curQuery = query as DIYAnalyseByPeriodBackgroundBase_Sample;
                        BackgroundFuncAreaSetting areaSetting = BackgroundFuncAreaSetting.GetInstance();
                        curQuery.GatherRadius = areaSetting.gatherRedius;
                    }
                    else if (query is DIYAnalyseByFileBackgroundBase)
                    {
                        DIYAnalyseByFileBackgroundBase curQuery = query as DIYAnalyseByFileBackgroundBase;
                        BackgroundFuncRoadSetting roadSetting = BackgroundFuncRoadSetting.GetInstance();
                        curQuery.ValidDistance = roadSetting.ValidDistance;
                    }
                    ReportBackgroundInfo("开始分析" + query.FuncType.ToString() + query.SubFuncType.ToString() + "类" + query.Name + "...");
                    query.Query();
                }
            }
        }

        private void loadDistrictCellInfo(BackgroundFuncConfigManager configManager, MainModel mainModel, int i, string districtName)
        {
            if (mainModel.DistrictID != i)
            {
                mainModel.DistrictID = i;
                GISManager.GetInstance().ResetMap(this, null);
                if (configManager.IsNeedLoadCurCityWorkParam)
                {
                    ReportBackgroundInfo("开始读取地市【" + districtName + "】的小区信息...");
                    mainModel.ModelConfig.Init(true);
                }
            }
        }

        private bool initActiveQueryList()
        {
            foreach (BackgroundQueryBase query in BackgroundQueryList)
            {
                if (query.BackgroundStat)
                {
                    activeBackgroundQueryList.Add(query);
                    query.DealBeforeBackgroundQueryByCity();
                }
            }
            if (activeBackgroundQueryList.Count <= 0)
            {
                ReportBackgroundInfo("没有开启任何专题功能！");
                return false;
            }
            return true;
        }

        protected void writeLog(string strErr)
        {
            string path = BackgroundLogSavePath;
            if (!File.Exists(path))
            {
                File.Create(path).Close();
            }
            using (StreamWriter sw = File.AppendText(path))
            {
                sw.Write(strErr + "\r\n");
                sw.Flush();
                sw.Close();
            }
        }

        private static string getLogPath()
        {
            string strDate = DateTime.Now.ToString("yyyyMMdd");
            curDate = strDate;
            StringBuilder logPath = new StringBuilder(System.Windows.Forms.Application.StartupPath);
            logPath.Append(@"\BackGroundLog\");
            string directory = logPath.ToString();
            logPath.Append(strDate);
            logPath.Append("后台专题运行日志.txt");
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }
            return logPath.ToString();
        }
    }
}
