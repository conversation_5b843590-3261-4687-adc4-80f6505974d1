﻿using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.ZTFunc.ZTWirelessNetTest;
using MasterCom.RAMS.KPI_Statistics;
using Shape = MapWinGIS.Shape;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// 四川专用功能
    /// </summary>
    public class WirelessNetTest : QueryBase
    {
        /*
         * 1.选择日期生产对应报表
         *      条件 : 日期,是否合并统计联通,需选择场景小区Excel
         *      
         *      1).地市级 - 数据源:例行测试_网格(可配)
         *          匹配城区网格图层
         *          按[地市]统计,只统计在城区网格图层内的数据
         *      2).区县级 - 数据源:例行测试_区县(可配)
         *          匹配区县网格图层
         *          按[地市-区县]统计,只统计在区县网格图层内的数据
         *      3).场景级 - 数据源:例行测试_网格,例行测试_区县,例行测试_高铁,例行测试_地铁,例行测试_高速,例行测试_交通枢纽(可配)
         *          匹配城区网格图层,区县网格图层
         *          城区网格:按[地市-城区网格]维度统计,只统计在城区网格图层内的数据
         *          区县网格:按[地市-区县网格]维度统计,只统计在区县网格图层内的数据
         *          其他场景:按[地市-场景]维度统计,只统计文件名与导入的"场景Excel"匹配的文件数据
         *      4).子场景级 - 数据源:例行测试_高铁,例行测试_地铁,例行测试_高速,例行测试_交通枢纽(可配)
         *          高铁:按[地市-场景-子场景]维度统计,只统计文件名与导入的"场景Excel"中"高铁"场景的文件
         *          地铁:按[地市-场景-子场景]维度统计,只统计文件名与导入的"场景Excel"中"地铁"场景的文件
         *          高速:按[地市-场景-子场景]维度统计,只统计文件名与导入的"场景Excel"中"高速"场景的文件
         *          机场:按[地市-场景-子场景]维度统计,只统计文件名与导入的"场景Excel"中"交通枢纽"场景的文件
         *       
         * 2.因此可以得出关系
         *      1).先统计[子场景级]
         *      2).子场景级->按场景汇聚得到[场景级中其他场景]的数据
         *      3).统计[场景级中城区网格]
         *      4).城区网格->按地市汇聚得到[地市级]数据
         *      5).统计[场景级中区县网格]
         *      6).区县网格->按地市汇聚得到[区县级]数据
         */


        WirelessNetTestCond anaCondition;
        WirelessNetTestResult totalResult;
        Dictionary<string, WirelessNetTestResult> districtDataDic;

        public WirelessNetTest()
            : base(MainModel.GetInstance())
        {
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override string Name
        {
            get { return "无线网络测速分析"; }
        }

        protected override bool isValidCondition()
        {
            if (anaCondition == null)
            {
                anaCondition = WirelessNetTestConfig.Instance.LoadConfig();
                if (!string.IsNullOrEmpty(WirelessNetTestConfig.Instance.ErrMsg))
                {
                    WirelessNetTestConfig.Instance.WriteLogWithMsgBox(WirelessNetTestConfig.Instance.ErrMsg);
                }
            }
            WirelessNetTestDlg dlg = new WirelessNetTestDlg();
            dlg.SetCondition(anaCondition);
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return false;
            }
            anaCondition = dlg.GetCondition();
            WirelessNetTestConfig.Instance.SaveConfig(anaCondition);
            return true;
        }

        protected override void query()
        {
            try
            {
                initData();

                var success = WirelessNetTestReport.Instance.LoadReport();
                if (!success)
                {
                    string msg = $"加载报表异常:{WirelessNetTestReport.Instance.ErrMsg}";
                    WirelessNetTestConfig.Instance.WriteLogWithMsgBox(msg);
                    return;
                }

                foreach (int districtID in anaCondition.DistrictIDs)
                {
                    WaitBox.Show("开始统计分析数据...", queryInThread, districtID);
                }

                doAfterDealAllData();

                WirelessNetTestConfig.Instance.WriteLog($"{Name}处理完毕");

                fireShowForm();
            }
            catch (Exception ex)
            {
                WirelessNetTestConfig.Instance.WriteLogWithMsgBox(ex);
            }
        }

        protected void initData()
        {
            totalResult = new WirelessNetTestResult();
            districtDataDic = new Dictionary<string, WirelessNetTestResult>();
        }

        #region 按地市进行处理
        protected void queryInThread(object obj)
        {
            try
            {
                int districtID = (int)obj;
                dealSingleDistrictFiles(districtID);
            }
            catch (Exception ex)
            {
                WirelessNetTestConfig.Instance.WriteLogWithMsgBox(ex);
            }
            finally
            {
                System.Threading.Thread.Sleep(200);
                WaitBox.Close();
            }

            clearOneDistrictData();
        }

        private void dealSingleDistrictFiles(int districtID)
        {
            string districtName = DistrictManager.GetInstance().getDistrictName(districtID);
            WirelessNetTestConfig.Instance.WriteLog($"正在处理--[{districtName}]--地市");

            //加载地市图层
            WirelessNetTestMap.Instance.LoadMap(districtName, anaCondition.ShapePath);

            WaitBox.Text = $"正在查询[{districtName}]的测试文件...";
            var allProjects = anaCondition.GetAllProjects();
            var totalFileList = WirelessNetTestQueryHelper.GetFileList(anaCondition, districtID, allProjects);
            if (totalFileList.Count == 0)
            {
                WirelessNetTestConfig.Instance.WriteLog($"没有查询到符合格式的测试文件", "Error");
                return;
            }
            WirelessNetTestConfig.Instance.WriteLog($"查询到-[{districtName}]-中符合格式的测试文件共[{totalFileList.Count}]个");

            var carrierFileInfoDic = WirelessNetTestQueryHelper.ClassifyFiles(anaCondition, totalFileList, districtName);

            //var gridDic = anaCondition.InitProjectTypeDic<List<StatDataHubBase>>();

            dealFiles(districtID, districtName, carrierFileInfoDic);
        }

        private void dealFiles(int districtID, string districtName, Dictionary<WirelessNetTestProjectType, Dictionary<int, Dictionary<FormulaType, List<FileInfo>>>> carrierFileInfoDic)
        {
            WirelessNetTestConfig.Instance.WriteLogWithWaitBox($"开始统计[{districtName}]的测试文件...");
            var singleDistrictData = new WirelessNetTestResult();
            foreach (var carrierFiles in carrierFileInfoDic)
            {
                var type = carrierFiles.Key;
                foreach (var serviceFiles in carrierFiles.Value)
                {
                    List<KPIStatDataBase> curDataList = new List<KPIStatDataBase>();
                    List<KPIStatDataBase> curEventList = new List<KPIStatDataBase>();

                    var carrier = serviceFiles.Key;
                    //如果勾选了计算联通才统计联通数据
                    //if (carrier == 2 && !anaCondition.IsStatCU)
                    //{
                    //    continue;
                    //}

                    foreach (var files in serviceFiles.Value)
                    {
                        var service = files.Key;

                        //查询栅格数据
                        var kpiData = WirelessNetTestQueryHelper.QueryKpiData(anaCondition, districtID, type, carrier, service, files.Value);

                        //汇聚数据业务和语音业务数据
                        curDataList.AddRange(kpiData.DataList);
                        curEventList.AddRange(kpiData.EventList);
                    }

                    var carrierType = WirelessNetTestHelper.Instance.GetCarrierStatType(carrier);

                    //将查询出的栅格和事件数据按各个级别进行统计
                    var info = new WirelessNetTestStatInfo()
                    {
                        TestPeriod = anaCondition.TestPeriod,
                        DistrictID = districtID,
                        DistrictName = districtName,
                        Carrier = carrierType,
                        DataList = curDataList,
                        EventList = curEventList,
                        Type = type,
                        //FormulaType = service,
                        //Files = files.Value
                    };
                    WirelessNetTestStat.StatKpiData(info, singleDistrictData);
                }
            }

            districtDataDic.Add(districtName, singleDistrictData);
        }

        protected virtual void clearOneDistrictData()
        {
            WirelessNetTestMap.Instance.Clear();
            WirelessNetTestSceneCell.Instance.Clear();
        }
        #endregion

        protected virtual void doAfterDealAllData()
        {
            //处理完成后,按4种级别 分别汇聚所有地市的指标
            foreach (var data in districtDataDic.Values)
            {
                totalResult.Adddata(data);
            }
            totalResult.Sort();
        }

        protected void fireShowForm()
        {
            WirelessNetTestForm frm = MainModel.CreateResultForm(typeof(WirelessNetTestForm)) as WirelessNetTestForm;
            frm.FillData(totalResult);
            frm.Owner = MainModel.MainForm;
            frm.Visible = true;
            frm.BringToFront();
        }
    }
}
