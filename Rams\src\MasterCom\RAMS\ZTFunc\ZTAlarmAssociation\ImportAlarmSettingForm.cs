﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ImportAlarmSettingForm : BaseDialog
    {
        public ImportAlarmSettingForm()
        {
            InitializeComponent();
            InitCbxCitys();

            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
            btnSelect.Click += BtnSelect_Click;
        }

        public ImportAlarmCondition GetCondition()
        {
            ImportAlarmCondition cond = new ImportAlarmCondition();
            cond.CityID = DistrictManager.GetInstance().GetDistrictID(cbxCity.SelectedItem as string);
            cond.FilePath = txtFile.Text;
            cond.IsClear = chkClear.Checked;
            return cond;
        }

        private void BtnSelect_Click(object sender, EventArgs e)
        {
            OpenFileDialog dlg = new OpenFileDialog();
            dlg.Filter = FilterHelper.Excel;
            dlg.FilterIndex = 2;
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            txtFile.Text = dlg.FileName;
            dlg.Dispose();
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (cbxCity.SelectedItem == null)
            {
                MessageBox.Show("请选择地市", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = DialogResult.None;
                return;
            }
            else if (string.IsNullOrEmpty(txtFile.Text))
            {
                MessageBox.Show("请选择数据文件", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = DialogResult.None;
                return;
            }
            DialogResult = DialogResult.OK;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void InitCbxCitys()
        {
            cbxCity.Items.Clear();

            MainModel mainModel = MainModel.GetInstance();
            if (mainModel.User.DBID == -1)
            {
                string[] cityNames = DistrictManager.GetInstance().DistrictNames;
                foreach (string city in cityNames)
                {
                    if (string.IsNullOrEmpty(city))
                    {
                        continue;
                    }
                    cbxCity.Items.Add(city);
                }
                if (cbxCity.Items.Count > 0)
                {
                    cbxCity.SelectedIndex = 0;
                }
            }
            else
            {
                string cityName = DistrictManager.GetInstance().getDistrictName(mainModel.DistrictID);
                if (cityName != null)
                {
                    cbxCity.Items.Add(cityName);
                    cbxCity.SelectedIndex = 0;
                }
            }
        }
    }
}
