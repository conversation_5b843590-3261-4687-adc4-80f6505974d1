﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LTEWeakCoverRoadSettingDlg : BaseDialog
    {
        public LTEWeakCoverRoadSettingDlg(WeakCoverRoadCondition_LTE condition)
        {
            InitializeComponent();
            setCondition(condition);
        }

        private void setCondition(WeakCoverRoadCondition_LTE condition)
        {
            if (condition == null)
            {
                return;
            }
            numMinWeakPercent.Value = (decimal)condition.MinWeakPointPercent;
            numRSRP.Value = (decimal)condition.MaxRSRP;
            chkNbRSRP.Checked = condition.CheckNbMaxRSRP;
            numNbRSRP.Value = (decimal)condition.NbMaxRSRP;
            numMinDistance.Value = (decimal)condition.MinCoverRoadDistance;
            numMinDuration.Value = (decimal)condition.MinDuration;
            numMaxTPDistance.Value = (decimal)condition.MaxTPDistance;
            numSampleCellDistance.Value = condition.MaxSampleCellDistance;
            numSampleCellAngle.Value = condition.MaxSampleCellAngle;

            radAndOr.SelectedIndex = condition.CheckCondAnd ? 0 : 1;
            chkSINR.Checked = condition.CheckSINR;
            chkMinDistance.Checked = condition.CheckMinDistance;
            chkMinDuration.Checked = condition.CheckMinDuration;
            numSINRMax.Value = (decimal)condition.MaxSINR;

            cmbCompare.SelectedIndex = (int)condition.compareSymble;
            cbxAssocAnalysis.Checked = condition.IsAssocAnalysis;
        }

        public WeakCoverRoadCondition_LTE GetConditon()
        {
            WeakCoverRoadCondition_LTE condition = new WeakCoverRoadCondition_LTE();
            condition.MinWeakPointPercent = (double)numMinWeakPercent.Value;
            condition.MaxRSRP = (float)numRSRP.Value;
            condition.CheckNbMaxRSRP = chkNbRSRP.Checked;
            condition.NbMaxRSRP = (float)numNbRSRP.Value;
            condition.MinCoverRoadDistance = (double)numMinDistance.Value;
            condition.MinDuration = (double)numMinDuration.Value;
            condition.MaxTPDistance = (double)numMaxTPDistance.Value;
            condition.MaxSampleCellDistance = (int)numSampleCellDistance.Value;
            condition.MaxSampleCellAngle = (int)numSampleCellAngle.Value;

            condition.MaxSINR = (float)numSINRMax.Value;
            condition.CheckCondAnd = radAndOr.SelectedIndex == 0;
            condition.CheckMinDistance = chkMinDistance.Checked;
            condition.CheckMinDuration = chkMinDuration.Checked;
            condition.CheckSINR = chkSINR.Checked;
            condition.compareSymble = (CompareSymble)cmbCompare.SelectedIndex;
            condition.IsAssocAnalysis = cbxAssocAnalysis.Checked;
            return condition;
        }

        private void chkSINR_CheckedChanged(object sender, EventArgs e)
        {
            numSINRMax.Enabled = chkSINR.Checked;
            if (!chkSINR.Checked)
            {
                radAndOr.SelectedIndex = 0;
            }
            radAndOr.Enabled = chkSINR.Checked;
        }

        private void chkNbRSRP_CheckedChanged(object sender, EventArgs e)
        {
            numNbRSRP.Enabled = chkNbRSRP.Checked;
            cmbCompare.Enabled = chkNbRSRP.Checked;
        }

        private void chkMinDistance_CheckedChanged(object sender, EventArgs e)
        {
            numMinDistance.Enabled = chkMinDistance.Checked;
        }

        private void chkMinDuration_CheckedChanged(object sender, EventArgs e)
        {
            numMinDuration.Enabled = chkMinDuration.Checked;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (!chkMinDistance.Checked && !chkMinDuration.Checked)
            {
                MessageBox.Show(this, "请至少选择“持续距离”和“时长”中的一项！");
            }
            else
            {
                this.DialogResult = DialogResult.OK;
            }
        }
    }
}
