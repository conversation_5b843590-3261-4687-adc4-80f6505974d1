﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.MTGis;
using System.Drawing;

namespace MasterCom.RAMS.CQT
{
   public class MapFormCQTComplainTrackLayer : CustomDrawLayer
    {
        static MapFormCQTComplainTrackLayer()
        {
            
        }

        public MapFormCQTComplainTrackLayer(MapOperation mp, string name)
            : base(mp, name)

        {
           
        }
        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, Graphics graphics)
        {
           //-、、 DbPoint targetPoint = new DbPoint(targetCell.EndPointLongitude, targetCell.EndPointLatitude);
           // PointF point2;
           // Map.ToDisplay(targetPoint, out point2);
           // graphics = e.Graphics;
 
          // Pen pen = new Pen(new SolidBrush(Color.Red), 0.5f);
           // Rectangle rg = new Rectangle(50, 50, 80, 80);
           // graphics.DrawEllipse(pen, rg);
           // graphics.FillEllipse(new SolidBrush(Color.Red), rg);
           // graphics.Flush();
           // graphics.Dispose(); 

        }
    }
}
