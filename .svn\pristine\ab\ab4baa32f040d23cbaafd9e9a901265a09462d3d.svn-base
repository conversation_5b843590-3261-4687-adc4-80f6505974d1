﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Grid;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc.AreaArchiveManage
{
    public class Area2PeriodGrid
    {
        public AreaBase Area
        {
            get;
            private set;
        }
        public Area2PeriodGrid(AreaBase area)
        {
            this.Area = area;
        }

        public AreaKPIDataGroup<GridUnitBase> DataGrp1 { get; set; }
        public List<AreaKPIDataGroup<GridUnitBase>> Grids1
        {
            get;
            set;
        }

        public AreaKPIDataGroup<GridUnitBase> DataGrp2 { get; set; }
        public List<AreaKPIDataGroup<GridUnitBase>> Grids2
        {
            get;
            set;
        }

        internal void AddStatData(bool isPeriod1, AreaKPIDataGroup<GridUnitBase> grid)
        {
            if (isPeriod1)
            {
                if (Grids1 == null)
                {
                    Grids1 = new List<AreaKPIDataGroup<GridUnitBase>>();
                }
                Grids1.Add(grid);
                if (DataGrp1 == null)
                {
                    DataGrp1 = grid.Clone();
                }
                else
                {
                    DataGrp1.Merge(grid);
                }
            }
            else
            {
                if (Grids2 == null)
                {
                    Grids2 = new List<AreaKPIDataGroup<GridUnitBase>>();
                }
                Grids2.Add(grid);
                if (DataGrp2 == null)
                {
                    DataGrp2 = grid.Clone();
                }
                else
                {
                    DataGrp2.Merge(grid);
                }
            }
        }
    }

    public class GridStatData : GridUnitBase
    {
        public GridStatData(double lng,double lat)
            : base(lng,lat)
        { 
        }

        

    }

}
