﻿using MasterCom.MTGis;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTNrNBCellCheckAna
{
    public partial class NBCellCheckAnaForm : MinCloseForm
    {
        public NBCellCheckAnaForm()
        {
            InitializeComponent();
            mapForm = MainModel.MainForm.GetMapForm();
            init();
            DisposeWhenClose = true;
        }
        private MapForm mapForm = null;
        private List<NrNBBothCellInfo> resultList = new List<NrNBBothCellInfo>();

        private void init()
        {
            #region SatList
            this.olvColumnStatSN.AspectGetter = delegate (object row)
            {
                if (row is NrNBBothCellInfo)
                {
                    NrNBBothCellInfo item = row as NrNBBothCellInfo;
                    return item.SN;
                }
                return "";
            };

            this.olvColumnStatCellName.AspectGetter = delegate (object row)
            {
                if (row is NrNBBothCellInfo)
                {
                    NrNBBothCellInfo item = row as NrNBBothCellInfo;
                    return item.ServCell.Name;
                }
                else if (row is NrNBCellCheckBothNBItem)
                {
                    NrNBCellCheckBothNBItem item = row as NrNBCellCheckBothNBItem;
                    return item.NBCell.Name;
                }
                return "";
            };

            this.olvColumnStatNBCount.AspectGetter = delegate (object row)
            {
                if (row is NrNBBothCellInfo)
                {
                    NrNBBothCellInfo item = row as NrNBBothCellInfo;
                    return item.NBCount;
                }

                return "";
            };

            this.olvColumnStatCellTAC.AspectGetter = delegate (object row)
            {
                if (row is NrNBBothCellInfo)
                {
                    NrNBBothCellInfo item = row as NrNBBothCellInfo;
                    return item.ServCell.TAC;
                }
                else if (row is NrNBCellCheckBothNBItem)
                {
                    NrNBCellCheckBothNBItem item = row as NrNBCellCheckBothNBItem;
                    return item.NBCell.TAC;
                }
                return "";
            };

            this.olvColumnStatCellCellID.AspectGetter = delegate (object row)
            {
                if (row is NrNBBothCellInfo)
                {
                    NrNBBothCellInfo item = row as NrNBBothCellInfo;
                    return item.ServCell.CellID;
                }
                else if (row is NrNBCellCheckBothNBItem)
                {
                    NrNBCellCheckBothNBItem item = row as NrNBCellCheckBothNBItem;
                    return item.NBCell.CellID;
                }
                return "";
            };

            this.olvColumnNCI.AspectGetter = delegate (object row)
            {
                if (row is NrNBBothCellInfo)
                {
                    NrNBBothCellInfo item = row as NrNBBothCellInfo;
                    return item.ServCell.NCI;
                }
                else if (row is NrNBCellCheckBothNBItem)
                {
                    NrNBCellCheckBothNBItem item = row as NrNBCellCheckBothNBItem;
                    return item.NBCell.NCI;
                }
                return "";
            };

            this.olvColumnEnodeBID.AspectGetter = delegate (object row)
            {
                if (row is NrNBBothCellInfo)
                {
                    NrNBBothCellInfo item = row as NrNBBothCellInfo;
                    return item.ServCell.BelongBTS.BTSID;
                }
                else if (row is NrNBCellCheckBothNBItem)
                {
                    NrNBCellCheckBothNBItem item = row as NrNBCellCheckBothNBItem;
                    return item.NBCell.BelongBTS.BTSID;
                }
                return "";
            };

            this.olvColumnSectorID.AspectGetter = delegate (object row)
            {
                if (row is NrNBBothCellInfo)
                {
                    NrNBBothCellInfo item = row as NrNBBothCellInfo;
                    return item.ServCell.SectorID;
                }
                else if (row is NrNBCellCheckBothNBItem)
                {
                    NrNBCellCheckBothNBItem item = row as NrNBCellCheckBothNBItem;
                    return item.NBCell.SectorID;
                }
                return "";
            };

            this.olvColumnStatCellARFCN.AspectGetter = delegate (object row)
            {
                if (row is NrNBBothCellInfo)
                {
                    NrNBBothCellInfo item = row as NrNBBothCellInfo;
                    return item.ServCell.ARFCN;
                }
                else if (row is NrNBCellCheckBothNBItem)
                {
                    NrNBCellCheckBothNBItem item = row as NrNBCellCheckBothNBItem;
                    return item.NBCell.ARFCN;
                }
                return "";
            };

            this.olvColumnStatCellPCI.AspectGetter = delegate (object row)
            {
                if (row is NrNBBothCellInfo)
                {
                    NrNBBothCellInfo item = row as NrNBBothCellInfo;
                    return item.ServCell.PCI;
                }
                else if (row is NrNBCellCheckBothNBItem)
                {
                    NrNBCellCheckBothNBItem item = row as NrNBCellCheckBothNBItem;
                    return item.NBCell.PCI;
                }
                return "";
            };

            this.olvColumnStatSampleCount.AspectGetter = delegate (object row)
            {
                if (row is NrNBBothCellInfo)
                {
                    NrNBBothCellInfo item = row as NrNBBothCellInfo;
                    return item.SampleCount;
                }
                else if (row is NrNBCellCheckBothNBItem)
                {
                    NrNBCellCheckBothNBItem item = row as NrNBCellCheckBothNBItem;
                    return item.SampleCount;
                }
                return "";
            };

            this.olvColumnStatCellRSRP.AspectGetter = delegate (object row)
            {
                if (row is NrNBBothCellInfo)
                {
                    NrNBBothCellInfo item = row as NrNBBothCellInfo;
                    return item.AvgRSRP;
                }
                else if (row is NrNBCellCheckBothNBItem)
                {
                    NrNBCellCheckBothNBItem item = row as NrNBCellCheckBothNBItem;
                    return item.AvgRSRP;
                }
                return "";
            };

            this.olvColumnStatDistance.AspectGetter = delegate (object row)
            {
                if (row is NrNBCellCheckBothNBItem)
                {
                    NrNBCellCheckBothNBItem item = row as NrNBCellCheckBothNBItem;
                    return item.DistanceStr;
                }
                return "";
            };


            this.olvColumnStatScore.AspectGetter = delegate (object row)
            {
                if (row is NrNBCellCheckBothNBItem)
                {
                    NrNBCellCheckBothNBItem item = row as NrNBCellCheckBothNBItem;
                    return item.Score;
                }
                return "";
            };

            this.olvColumnStatOrder.AspectGetter = delegate (object row)
            {
                if (row is NrNBCellCheckBothNBItem)
                {
                    NrNBCellCheckBothNBItem item = row as NrNBCellCheckBothNBItem;
                    return item.Order;
                }
                return "";
            };


            this.olvColumnStatSource.AspectGetter = delegate (object row)
            {
                if (row is NrNBCellCheckBothNBItem)
                {
                    NrNBCellCheckBothNBItem item = row as NrNBCellCheckBothNBItem;
                    return item.Source;
                }
                return "";
            };

            this.olvColumnStatAdvice.AspectGetter = delegate (object row)
            {
                if (row is NrNBCellCheckBothNBItem)
                {
                    NrNBCellCheckBothNBItem item = row as NrNBCellCheckBothNBItem;
                    return item.Advice;
                }
                return "";
            };

            this.olvColumnStatStatusByMsg.AspectGetter = delegate (object row)
            {
                if (row is NrNBCellCheckBothNBItem)
                {
                    NrNBCellCheckBothNBItem item = row as NrNBCellCheckBothNBItem;
                    return item.StatusByMsg;
                }
                return "";
            };

            this.olvColumnStatStatusByParam.AspectGetter = delegate (object row)
            {
                if (row is NrNBCellCheckBothNBItem)
                {
                    NrNBCellCheckBothNBItem item = row as NrNBCellCheckBothNBItem;
                    return item.StatusByParam;
                }
                return "";
            };

            this.ListViewNBCheckStat.CanExpandGetter = delegate (object x)
            {
                return x is NrNBBothCellInfo;
            };

            this.ListViewNBCheckStat.ChildrenGetter = delegate (object x)
            {
                if (x is NrNBBothCellInfo)
                {
                    NrNBBothCellInfo statInfo = x as NrNBBothCellInfo;
                    return statInfo.NBDic.Values;
                }
                else
                {
                    return "";
                }
            };

            #endregion
        }

        public void FillData(List<NrNBBothCellInfo> resultList)
        {
            this.resultList = new List<NrNBBothCellInfo>();
            this.resultList = resultList;

            ListViewNBCheckStat.RebuildColumns();
            ListViewNBCheckStat.ClearObjects();
            ListViewNBCheckStat.SetObjects(resultList);

            MainModel.RefreshLegend();
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                mf.GetCellLayer().Invalidate();
            }
        }

        private void listViewTotal_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (ListViewNBCheckStat.SelectedObject is NrNBBothCellInfo)
            {
                NrNBBothCellInfo cellItem = ListViewNBCheckStat.SelectedObject as NrNBBothCellInfo;

                mapForm.MainModel.SelectedTestPoints.Clear();
                //mapForm.MainModel.SelectedTestPoints.AddRange(info.sampleLst);
                //GoToView(info.sampleLst);
                mapForm.GetDTLayer().Invalidate();
            }
        }

        private void GoToView(List<TestPoint> tpList)
        {
            double ltLong = 100000;
            double ltLat = -100000;
            double brLong = -100000;
            double brLat = 100000;

            foreach (TestPoint tp in tpList)
            {
                if (tp.Longitude < ltLong)
                {
                    ltLong = tp.Longitude;
                }
                if (tp.Longitude > brLong)
                {
                    brLong = tp.Longitude;
                }
                if (tp.Latitude < brLat)
                {
                    brLat = tp.Latitude;
                }
                if (tp.Latitude > ltLat)
                {
                    ltLat = tp.Latitude;
                }
            }
            mapForm.GoToView(new DbRect(ltLong, ltLat, brLong, brLat));
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            ListViewNBCheckStat.ExpandAll();
        }

        private void miCallapsAll_Click(object sender, EventArgs e)
        {
            ListViewNBCheckStat.CollapseAll();
        }

        private void miExportExcel_Click(object sender, EventArgs e)
        {
            exportStatListToXls();
        }

        private void exportStatListToXls()
        {
            if (!exportStatListToExcel())
            {
                exportStatListToTxt();
            }
        }

        #region ExportStatList
        private bool exportStatListToExcel()
        {
            List<NPOIRow> rows = ConvertToNPOIRows(resultList, false);
            if (rows == null)
            {
                return false;
            }
            ExcelNPOIManager.ExportToExcel(rows);
            return true;
        }

        public static List<NPOIRow> ConvertToNPOIRows(List<NrNBBothCellInfo> results
            , bool isContainsFileInfo)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.cellValues.Add("小区序号");
            row.cellValues.Add("小区名称");
            row.cellValues.Add("小区配置邻区数量");
            row.cellValues.Add("小区TAC");
            row.cellValues.Add("小区CellID");
            row.cellValues.Add("小区ECI");
            row.cellValues.Add("小区EnodeBID");
            row.cellValues.Add("小区SectorID");
            row.cellValues.Add("小区EARFCN");
            row.cellValues.Add("小区PCI");
            row.cellValues.Add("小区采样点数");
            row.cellValues.Add("小区平均RSRP");
            if (isContainsFileInfo)
            {
                row.cellValues.Add("文件ID");
                row.cellValues.Add("文件名");
            }

            row.cellValues.Add("邻区名称");
            row.cellValues.Add("邻区TAC");
            row.cellValues.Add("邻区CellID");
            row.cellValues.Add("邻区ECI");
            row.cellValues.Add("邻区EnodeBID");
            row.cellValues.Add("邻区SectorID");
            row.cellValues.Add("邻区EARFCN");
            row.cellValues.Add("邻区PCI");
            row.cellValues.Add("邻区采样点数");
            row.cellValues.Add("邻区平均RSRP");
            row.cellValues.Add("邻区得分");
            row.cellValues.Add("邻区排名");
            row.cellValues.Add("邻区数据来源");
            row.cellValues.Add("检测结果(与信令对比)");
            row.cellValues.Add("检测结果(与工参比对)");
            row.cellValues.Add("建议");
            row.cellValues.Add("与主服之间距离(米)");

            rows.Add(row);
            int rowCount = 1;

            foreach (NrNBBothCellInfo statItem in results)
            {
                foreach (NrNBCellCheckBothNBItem nbItem in statItem.NBDic.Values)
                {
                    rowCount++;
                    if (rowCount > 65535)
                    {
                        return null;
                    }
                    row = new NPOIRow();

                    row.cellValues.Add(statItem.SN);
                    row.cellValues.Add(statItem.ServCell.Name);
                    row.cellValues.Add(statItem.NBCount);
                    row.cellValues.Add(statItem.ServCell.TAC);
                    row.cellValues.Add(statItem.ServCell.CellID);
                    row.cellValues.Add(statItem.ServCell.NCI);
                    row.cellValues.Add(statItem.ServCell.BelongBTS.BTSID);
                    row.cellValues.Add(statItem.ServCell.SectorID);
                    row.cellValues.Add(statItem.ServCell.ARFCN);
                    row.cellValues.Add(statItem.ServCell.PCI);
                    row.cellValues.Add(statItem.SampleCount);
                    row.cellValues.Add(statItem.AvgRSRP);
                    if (isContainsFileInfo)
                    {
                        row.cellValues.Add(getListDes(statItem.FileIds));
                        row.cellValues.Add(getListDes(statItem.FileNames));
                    }

                    row.cellValues.Add(nbItem.NBCell.Name);
                    row.cellValues.Add(nbItem.NBCell.TAC);
                    row.cellValues.Add(nbItem.NBCell.CellID);
                    row.cellValues.Add(nbItem.NBCell.NCI);
                    row.cellValues.Add(nbItem.NBCell.BelongBTS.BTSID);
                    row.cellValues.Add(nbItem.NBCell.SectorID);
                    row.cellValues.Add(nbItem.NBCell.ARFCN);
                    row.cellValues.Add(nbItem.NBCell.PCI);
                    row.cellValues.Add(nbItem.SampleCount);
                    row.cellValues.Add(nbItem.AvgRSRP);
                    row.cellValues.Add(nbItem.Score);
                    row.cellValues.Add(nbItem.Order);
                    row.cellValues.Add(nbItem.Source);
                    row.cellValues.Add(nbItem.StatusByMsg);
                    row.cellValues.Add(nbItem.StatusByParam);
                    row.cellValues.Add(nbItem.Advice);
                    row.cellValues.Add(nbItem.DistanceStr);

                    rows.Add(row);
                }
            }
            return rows;
        }
        private static string getListDes(List<string> list)
        {
            StringBuilder strb = new StringBuilder();
            foreach (string str in list)
            {
                if (str != null)
                {
                    strb.Append(str + ";");
                }
            }
            if (strb.Length > 0)
            {
                strb.Remove(strb.Length - 1, 1);
            }
            return strb.ToString();
        }
        private static string getListDes(List<int> list)
        {
            StringBuilder strb = new StringBuilder();
            foreach (int intValue in list)
            {
                strb.Append(intValue + ";");
            }
            if (strb.Length > 0)
            {
                strb.Remove(strb.Length - 1, 1);
            }
            return strb.ToString();
        }
        private void exportStatListToTxt()
        {
            SaveFileDialog dlg = new SaveFileDialog();
            dlg.RestoreDirectory = false;
            dlg.Filter = "Text file (*.txt)|*.txt";
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    WaitBox.Show("正在导出到Txt...", doExportStatListToTxt, dlg.FileName);
                    MessageBox.Show("Txt导出完成！");
                }
                catch (System.Exception e)
                {
                    MessageBox.Show("导出到Txt出错：" + e.Message);
                }
            }
        }

        private void doExportStatListToTxt(object nameObj)
        {
            string fileName = nameObj.ToString();
            System.IO.FileStream fileStream = new System.IO.FileStream(fileName, System.IO.FileMode.Create, System.IO.FileAccess.Write, System.IO.FileShare.Read);
            System.IO.StreamWriter streamWriter = new System.IO.StreamWriter(fileStream, Encoding.Default);
            try
            {
                writeStatListContent(streamWriter);
            }
            finally
            {
                streamWriter.Close();
                fileStream.Close();
                streamWriter = null;
                fileStream = null;
                WaitBox.Close();
            }
        }

        private void writeStatListContent(System.IO.StreamWriter streamWriter)
        {
            StringBuilder sb = new StringBuilder();

            sb.Append("小区序号\t");
            sb.Append("小区名称\t");
            sb.Append("小区配置邻区数量\t");
            sb.Append("小区TAC\t");
            sb.Append("小区CellID\t");
            sb.Append("小区ECI\t");
            sb.Append("小区EnodeBID\t");
            sb.Append("小区SectorID\t");
            sb.Append("小区EARFCN\t");
            sb.Append("小区PCI\t");
            sb.Append("小区采样点数\t");
            sb.Append("小区平均RSRP\t");

            sb.Append("邻区名称\t");
            sb.Append("邻区TAC\t");
            sb.Append("邻区CellID\t");
            sb.Append("邻区ECI\t");
            sb.Append("邻区EnodeBID\t");
            sb.Append("邻区SectorID\t");
            sb.Append("邻区EARFCN\t");
            sb.Append("邻区PCI\t");
            sb.Append("邻区采样点数\t");
            sb.Append("邻区平均RSRP\t");
            sb.Append("邻区得分\t");
            sb.Append("邻区排名\t");
            sb.Append("邻区数据来源\t");
            sb.Append("检测结果(与信令比对)\t");
            sb.Append("检测结果(与工参比对)\t");
            sb.Append("建议\t");
            sb.Append("与主服之间距离(米)");

            streamWriter.WriteLine(sb.ToString());
            sb.Remove(0, sb.Length);
            int iLoop = 1;

            foreach (NrNBBothCellInfo statItem in resultList)
            {
                foreach (NrNBCellCheckBothNBItem nbItem in statItem.NBDic.Values)
                {
                    sb.Append(statItem.SN + "\t");
                    sb.Append(statItem.ServCell.Name + "\t");
                    sb.Append(statItem.NBCount + "\t");
                    sb.Append(statItem.ServCell.TAC + "\t");
                    sb.Append(statItem.ServCell.CellID + "\t");
                    sb.Append(statItem.ServCell.NCI + "\t");
                    sb.Append(statItem.ServCell.BelongBTS.BTSID + "\t");
                    sb.Append(statItem.ServCell.SectorID + "\t");
                    sb.Append(statItem.ServCell.ARFCN + "\t");
                    sb.Append(statItem.ServCell.PCI + "\t");
                    sb.Append(statItem.SampleCount + "\t");
                    sb.Append(statItem.AvgRSRP + "\t");

                    sb.Append(nbItem.NBCell.Name + "\t");
                    sb.Append(nbItem.NBCell.TAC + "\t");
                    sb.Append(nbItem.NBCell.CellID + "\t");
                    sb.Append(nbItem.NBCell.NCI + "\t");
                    sb.Append(nbItem.NBCell.BelongBTS.BTSID + "\t");
                    sb.Append(nbItem.NBCell.SectorID + "\t");
                    sb.Append(nbItem.NBCell.ARFCN + "\t");
                    sb.Append(nbItem.NBCell.PCI + "\t");
                    sb.Append(nbItem.SampleCount + "\t");
                    sb.Append(nbItem.AvgRSRP + "\t");
                    sb.Append(nbItem.Score + "\t");
                    sb.Append(nbItem.Order + "\t");
                    sb.Append(nbItem.Source + "\t");
                    sb.Append(nbItem.StatusByMsg + "\t");
                    sb.Append(nbItem.StatusByParam + "\t");
                    sb.Append(nbItem.Advice + "\t");
                    sb.Append(nbItem.DistanceStr + "\t");

                    streamWriter.WriteLine(sb.ToString() + "\t");
                    sb.Remove(0, sb.Length);

                    WaitBox.ProgressPercent = (int)(100.0 * iLoop++ / resultList.Count);
                }
            }
        }
        #endregion

        private void ctxMenu_Opening(object sender, CancelEventArgs e)
        {

        }

    }
}
