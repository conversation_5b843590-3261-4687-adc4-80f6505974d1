﻿using DevExpress.XtraEditors;
using MasterCom.RAMS.KPI_Statistics;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Net;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTReportEventMng
{
    public class ZTReportEventStatQueryByFile_GZ : QueryKpiStatByFiles
    {
        public override MainModel.NeedSearchType needSearchType()
        {
            return MainModel.NeedSearchType.File;
        }
        public override string Name
        {
            get { return "CQT文件异常事件统计"; }
        }
        public override string IconName
        {
            get { return "Images/stat.gif"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 18000, 18046, this.Name);
        }

        protected override bool getConditionBeforeQuery()
        {
            //查询所有文件类型信息
            isQueryAllParams = true;
            DIYQueryCQTFileTypeInfo query = new DIYQueryCQTFileTypeInfo(MainModel);
            query.Query();
            fileTypeDic = query.areaTypeIdDic;
            cqtWeakCoverList = new List<CQTWeakCoverByFiles>();
            cqtLowSpeedList = new List<CQTLowSpeedByFiles>();
            return true;
        }

        protected override void queryInThread(object o)
        {
            WaitBox.Text = "开始查询KPI统计数据...";
            WaitBox.CanCancel = true;
            ClientProxy clientProxy = (ClientProxy)o;
            try
            {
                string imgTriadIDSet = getStatImgNeededTriadID();
                int idx = 1;
                foreach (FileInfo file in condition.FileInfos)
                {
                    if (file.DistrictID != clientProxy.DbID)
                    {
                        continue;
                    }
                    curFile = file;
                    WaitBox.Text = "(" + (idx++) + "/" + condition.FileInfos.Count + ")正在统计文件[" + file.Name + "]...";
                    WaitBox.ProgressPercent = 10;
                    queryPeriodInfo(null, clientProxy, imgTriadIDSet, file);
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
            }
            catch (Exception e)
            {
                MessageBox.Show("Error" + e.StackTrace + e.ToString());
            }
            finally
            {
                curFile = null;
                WaitBox.Close();
            }
        }

        protected override string getStatImgNeededTriadID(params object[] paramSet)
        {
            List<string> formulaSet = new List<string>();

            formulaSet.Add("Mx_640117");
            formulaSet.Add("Mx_64010A");
            formulaSet.Add("Mx_640109");
            formulaSet.Add("Mx_640108");
            formulaSet.Add("Mx_640101");
            formulaSet.Add("Mx_640107");
            formulaSet.Add("Tx_5C040311");
            formulaSet.Add("Tx_5C040342");
            formulaSet.Add("Tx_5C04038A");
            formulaSet.Add("Lte_6121012D");
            formulaSet.Add("Lte_61210101");
            formulaSet.Add("Lte_61210104");
            formulaSet.Add("{(value1[57]+value1[58]+value1[429])*(8/1024)/((value4[57]+value4[58]+value4[429])/1000)}");
            formulaSet.Add("{(value10[57]+value10[58]+value10[91])*(8/(1024*1024))/((value4[57]+value4[58]+value4[91])/1000)}");
            return getTriadIDIgnoreServiceType(formulaSet);
        }

        Dictionary<int, Dictionary<int, AreaInfo>> fileTypeDic = null;

        protected override void preparePackageCondition(Package package, TimePeriod period, params object[] reservedParams)
        {
            FileInfo fi = reservedParams[1] as FileInfo;
            TimePeriod logTbPeriod;
            DateTime bTime = getFirstDateFromLogTbName(fi.LogTable);
            DateTime eTime = bTime.AddMonths(1).AddSeconds(-1);
            logTbPeriod = new TimePeriod(bTime, eTime);
            AddDIYPeriod(package, logTbPeriod);
            AddDIYFileID(package, fi.ID);
            AddDIYStatStatus(package);
            AddGeographicFilter(package);
        }

        private DateTime getFirstDateFromLogTbName(string strname)
        {
            string[] vec = strname.Split('_');
            if (vec.Length == 5)
            {
                int year;
                int month;
                int.TryParse(vec[3], out year);
                int.TryParse(vec[4], out month);
                return new DateTime(year, month, 1);
            }
            throw (new Exception("格式错误，时间获取失败！"));
        }

        /// <summary>
        /// GSM、TD、LTE 文件的弱覆盖和低速率事件计算
        /// </summary>
        protected void doCalculate()
        {
            dataGroup.FinalMtMoGroup();
            int serviceType = curFile.ServiceType;

            Dictionary<int, AreaInfo> areaDic = new Dictionary<int, AreaInfo>();
            AreaInfo type = null;
            for (int i = 0; i < fileTypeDic.Count; i++)
            {
                fileTypeDic.TryGetValue(24, out areaDic);
            }
            if (areaDic == null)
            {
                return;
            }
            else
            {
                areaDic.TryGetValue(curFile.AreaID, out type);
                if (type == null)
                {
                    type = new AreaInfo();
                }
            }

            setCqtDataByService(serviceType, type);
        }

        private void setCqtDataByService(int serviceType, AreaInfo type)
        {
            if (serviceType == 1)//GSM语音----弱覆盖
            {
                setGSMCqtWeakCover(type);
            }
            else if (serviceType == 4)//TD覆盖率
            {
                setTDCqtWeakCover(type);
            }
            else if (serviceType == 33)//LTE覆盖率
            {
                setLTECqtWeakCover(type);
            }
            else if (serviceType == 2 || serviceType == 3)//GSM数据----下载速率
            {
                setGSMCqtLowSpeed(type);
            }
            else if (serviceType == 5)//TD数据
            {
                setTDCqtLowSpeed(type);
            }
            else if (serviceType == 34)//LTE数据
            {
                setLTECqtLowSpeed(type);
            }
        }

        private void setGSMCqtWeakCover(AreaInfo type)
        {
            cqtWeakCover = new CQTWeakCoverByFiles();
            cqtWeakCover.FileName = curFile.Name;
            cqtWeakCover.TotalSampleCount = curFile.TestPointCount;
            CarrierType carrierType = (CarrierType)curFile.CarrierType;

            if (type.coverType == 0)
            {
                cqtWeakCover.Type = "室分";
                //GSM室分公式：{100*Mx_640117+Mx_64010A+Mx_640109+Mx_640108)/Mx_640101
                //单独计算每个参数后再计算覆盖率
                double param1 = dataGroup.CalcFormula(carrierType, curFile.Momt, "{Mx_640117}");
                double param2 = dataGroup.CalcFormula(carrierType, curFile.Momt, "{Mx_64010A}");
                double param3 = dataGroup.CalcFormula(carrierType, curFile.Momt, "{Mx_640109}");
                double param4 = dataGroup.CalcFormula(carrierType, curFile.Momt, "{Mx_640108}");
                double paramTotal = dataGroup.CalcFormula(carrierType, curFile.Momt, "{Mx_640101}");

                cqtWeakCover.Coverage = float.Parse((100 * (param1 + param2 + param3 + param4) / paramTotal).ToString("F2"));

                cqtWeakCover.GoodSampleCount = (int)(cqtWeakCover.TotalSampleCount * cqtWeakCover.Coverage / 100);

            }
            else
            {
                cqtWeakCover.Type = "非室分";
                //GSM非室分公式：{100*Mx_640117+Mx_64010A+Mx_640109+Mx_640108+Mx_640107)/Mx_640101
                //单独计算每个参数后再计算覆盖率
                double param1 = dataGroup.CalcFormula(carrierType, curFile.Momt, "{Mx_640117}");
                double param2 = dataGroup.CalcFormula(carrierType, curFile.Momt, "{Mx_64010A}");
                double param3 = dataGroup.CalcFormula(carrierType, curFile.Momt, "{Mx_640109}");
                double param4 = dataGroup.CalcFormula(carrierType, curFile.Momt, "{Mx_640108}");
                double param5 = dataGroup.CalcFormula(carrierType, curFile.Momt, "{Mx_640107}");
                double paramTotal = dataGroup.CalcFormula(carrierType, curFile.Momt, "{Mx_640101}");

                cqtWeakCover.Coverage = float.Parse((100 * (param1 + param2 + param3 + param4 + param5) / paramTotal).ToString("F2"));

                cqtWeakCover.GoodSampleCount = (int)(cqtWeakCover.TotalSampleCount * cqtWeakCover.Coverage / 100);
            }

            if (cqtWeakCover.Coverage < 95)
            {
                cqtWeakCover.CqtEvent = "弱覆盖";
                cqtWeakCover.NetWorkType = "GSM语音";
                cqtWeakCoverList.Add(cqtWeakCover);
            }
        }

        private void setTDCqtWeakCover(AreaInfo type)
        {
            cqtWeakCover = new CQTWeakCoverByFiles();
            cqtWeakCover.FileName = curFile.Name;
            cqtWeakCover.TotalSampleCount = curFile.TestPointCount;
            CarrierType carrierType = (CarrierType)curFile.CarrierType;

            if (type.coverType == 0)
            {
                cqtWeakCover.Type = "室分";
                //TD室分公式：100*Tx_5C040311/Tx_5C040342 
                double param1 = dataGroup.CalcFormula(carrierType, curFile.Momt, "{Tx_5C040311}");
                double paramTotal = dataGroup.CalcFormula(carrierType, curFile.Momt, "{Tx_5C040342}");

                cqtWeakCover.Coverage = float.Parse((100 * param1 / paramTotal).ToString("F2"));

                cqtWeakCover.GoodSampleCount = (int)(cqtWeakCover.TotalSampleCount * cqtWeakCover.Coverage / 100);

            }
            else
            {
                cqtWeakCover.Type = "非室分";
                //TD非室分公式：100*Tx_5C04038A/Tx_5C040342  
                double param1 = dataGroup.CalcFormula(carrierType, curFile.Momt, "{Tx_5C04038A}");
                double paramTotal = dataGroup.CalcFormula(carrierType, curFile.Momt, "{Tx_5C040342}");

                cqtWeakCover.Coverage = float.Parse((100 * param1 / paramTotal).ToString("F2"));

                cqtWeakCover.GoodSampleCount = (int)(cqtWeakCover.TotalSampleCount * cqtWeakCover.Coverage / 100);

            }

            if (cqtWeakCover.Coverage < 95)
            {
                cqtWeakCover.CqtEvent = "弱覆盖";
                cqtWeakCover.NetWorkType = "TD语音";
                cqtWeakCoverList.Add(cqtWeakCover);
            }
        }

        private void setLTECqtWeakCover(AreaInfo type)
        {
            cqtWeakCover = new CQTWeakCoverByFiles();
            cqtWeakCover.FileName = curFile.Name;
            cqtWeakCover.TotalSampleCount = curFile.TestPointCount;
            CarrierType carrierType = (CarrierType)curFile.CarrierType;

            if (type.coverType == 0)
            {
                cqtWeakCover.Type = "室分";
                //LTE室分公式：100*Lte_6121012D/Lte_61210101 
                double param1 = dataGroup.CalcFormula(carrierType, curFile.Momt, "{Lte_6121012D}");
                double paramTotal = dataGroup.CalcFormula(carrierType, curFile.Momt, "{Lte_61210101}");

                cqtWeakCover.Coverage = float.Parse((100 * param1 / paramTotal).ToString("F2"));

                cqtWeakCover.GoodSampleCount = (int)(cqtWeakCover.TotalSampleCount * cqtWeakCover.Coverage / 100);
            }
            else
            {
                cqtWeakCover.Type = "非室分";
                //LTE室分公式：100*Lte_61210104/Lte_61210101 
                double param1 = dataGroup.CalcFormula(carrierType, curFile.Momt, "{Lte_61210104}");
                double paramTotal = dataGroup.CalcFormula(carrierType, curFile.Momt, "{Lte_61210101}");

                cqtWeakCover.Coverage = float.Parse((100 * param1 / paramTotal).ToString("F2"));

                cqtWeakCover.GoodSampleCount = (int)(cqtWeakCover.TotalSampleCount * cqtWeakCover.Coverage / 100);
            }

            if (cqtWeakCover.Coverage < 95)
            {
                cqtWeakCover.CqtEvent = "弱覆盖";
                cqtWeakCover.NetWorkType = "LTE语音";
                cqtWeakCoverList.Add(cqtWeakCover);
            }
        }

        private void setGSMCqtLowSpeed(AreaInfo type)
        {
            cqtLowSpeed = new CQTLowSpeedByFiles();
            cqtLowSpeed.FileName = curFile.Name;
            CarrierType carrierType = (CarrierType)curFile.CarrierType;
            //GSM室速率公式：(value1[57]+value1[58]+value1[429])*(8/1024)/((value4[57]+value4[58]+value4[429])/1000)

            float averSpeed = float.Parse(dataGroup.CalcFormula(carrierType, curFile.Momt, "{(value1[57]+value1[58]+value1[429])*(8/1024)/((value4[57]+value4[58]+value4[429])/1000)}").ToString("F2"));

            float speedNume = float.Parse(dataGroup.CalcFormula(carrierType, curFile.Momt, "{(value1[57]+value1[58]+value1[429])*(8/1024)}").ToString("F2"));
            float speedDenomi = float.Parse(dataGroup.CalcFormula(carrierType, curFile.Momt, "{(value4[57]+value4[58]+value4[429])/1000}").ToString("F2"));

            if (type.coverType == 0)
            {
                cqtLowSpeed.Type = "室分";
                if (averSpeed < 80)
                {
                    cqtLowSpeed.AverSpeed = averSpeed + " kbps";
                    cqtLowSpeed.SpeedNume = speedNume + "";
                    cqtLowSpeed.SpeedDenomi = speedDenomi + "";
                    cqtLowSpeed.CqtEvent = "低速率";
                    cqtLowSpeed.NetWorkType = "GSM数据";
                    cqtLowSpeedList.Add(cqtLowSpeed);
                }
            }
            else
            {
                cqtLowSpeed.Type = "非室分";
                if (averSpeed < 60)
                {
                    cqtLowSpeed.AverSpeed = averSpeed + " kbps";
                    cqtLowSpeed.SpeedNume = speedNume + "";
                    cqtLowSpeed.SpeedDenomi = speedDenomi + "";
                    cqtLowSpeed.CqtEvent = "低速率";
                    cqtLowSpeed.NetWorkType = "GSM数据";
                    cqtLowSpeedList.Add(cqtLowSpeed);
                }
            }
        }

        private void setTDCqtLowSpeed(AreaInfo type)
        {
            cqtLowSpeed = new CQTLowSpeedByFiles();
            cqtLowSpeed.FileName = curFile.Name;
            CarrierType carrierType = (CarrierType)curFile.CarrierType;

            float averSpeed = float.Parse(dataGroup.CalcFormula(carrierType, curFile.Momt, "{(value1[57]+value1[58])*(8/1024)/((value4[57]+value4[58])/1000)}").ToString("F2"));

            float speedNume = float.Parse(dataGroup.CalcFormula(carrierType, curFile.Momt, "{(value1[57]+value1[58])*(8/1024)}").ToString("F2"));
            float speedDenomi = float.Parse(dataGroup.CalcFormula(carrierType, curFile.Momt, "{(value4[57]+value4[58])/1000}").ToString("F2"));

            if (type.coverType == 0)
            {
                cqtLowSpeed.Type = "室分";
                if (averSpeed < 1000)   //单位kbps
                {
                    cqtLowSpeed.AverSpeed = averSpeed + " kbps";
                    cqtLowSpeed.SpeedNume = speedNume + "";
                    cqtLowSpeed.SpeedDenomi = speedDenomi + "";
                    cqtLowSpeed.CqtEvent = "低速率";
                    cqtLowSpeed.NetWorkType = "TD数据";
                    cqtLowSpeedList.Add(cqtLowSpeed);
                }
            }
            else
            {
                cqtLowSpeed.Type = "非室分";
                if (averSpeed < 500)
                {
                    cqtLowSpeed.AverSpeed = averSpeed + " kbps";
                    cqtLowSpeed.SpeedNume = speedNume + "";
                    cqtLowSpeed.SpeedDenomi = speedDenomi + "";
                    cqtLowSpeed.CqtEvent = "低速率";
                    cqtLowSpeed.NetWorkType = "TD数据";
                    cqtLowSpeedList.Add(cqtLowSpeed);
                }
            }
        }

        private void setLTECqtLowSpeed(AreaInfo type)
        {
            cqtLowSpeed = new CQTLowSpeedByFiles();
            cqtLowSpeed.FileName = curFile.Name;
            CarrierType carrierType = (CarrierType)curFile.CarrierType;

            float averSpeed = float.Parse(dataGroup.CalcFormula(carrierType, curFile.Momt, "{(value10[57]+value10[58]+value10[91])*(8/(1024*1024))/((value4[57]+value4[58]+value4[91])/1000)}").ToString("F2"));
            float speedNume = float.Parse(dataGroup.CalcFormula(carrierType, curFile.Momt, "{(value10[57]+value10[58]+value10[91])*(8/(1024*1024))}").ToString("F2"));
            float speedDenomi = float.Parse(dataGroup.CalcFormula(carrierType, curFile.Momt, "{(value4[57]+value4[58]+value4[91])/1000}").ToString("F2"));

            if (type.coverType == 0)
            {
                cqtLowSpeed.Type = "室分";
                if (averSpeed < 25) //单位Mbps
                {
                    cqtLowSpeed.AverSpeed = averSpeed + " Mbps";
                    cqtLowSpeed.SpeedNume = speedNume + "";
                    cqtLowSpeed.SpeedDenomi = speedDenomi + "";
                    cqtLowSpeed.CqtEvent = "低速率";
                    cqtLowSpeed.NetWorkType = "LTE数据";
                    cqtLowSpeedList.Add(cqtLowSpeed);
                }
            }
            else
            {
                cqtLowSpeed.Type = "非室分";
                if (averSpeed < 10)
                {
                    cqtLowSpeed.AverSpeed = averSpeed + " Mbps";
                    cqtLowSpeed.SpeedNume = speedNume + "";
                    cqtLowSpeed.SpeedDenomi = speedDenomi + "";
                    cqtLowSpeed.CqtEvent = "低速率";
                    cqtLowSpeed.NetWorkType = "LTE数据";
                    cqtLowSpeedList.Add(cqtLowSpeed);
                }
            }
        }

        List<CQTWeakCoverByFiles> cqtWeakCoverList = null;
        List<CQTLowSpeedByFiles> cqtLowSpeedList = null;

        CQTWeakCoverByFiles cqtWeakCover = null;
        CQTLowSpeedByFiles cqtLowSpeed = null;
        KPIDataGroup dataGroup = null;


        protected override void recieveAndHandleSpecificStatData(Net.Package package, List<Model.Interface.StatImgDefItem> curImgColumnDef, KPIStatDataBase singleStatData)
        {
            //语音业务对应弱覆盖事件，数据业务对应低速率事件
            fillStatData(package, curImgColumnDef, singleStatData);
            dataGroup = new KPIDataGroup(curFile);
            dataGroup.AddStatData(curFile, singleStatData, false);

        }

        protected override void handleStatEvent(Model.Event evt)
        {
            StatDataEvent eventData = new StatDataEvent(evt, needSeparateByServiceID(evt), needSeparateByFileName(evt));
            dataGroup.AddStatData(curFile, eventData, false);
        }

        protected override void fireShowResult()
        {
            if(cqtWeakCoverList.Count == 0 && cqtLowSpeedList.Count == 0)
            {
                XtraMessageBox.Show(MainModel.MainForm, "没有符合条件的数据。");
                return;
            }
            ZTReportEventStatQueryByFileListForm_GZ cqtEventQueryByFileListForm =MainModel.CreateResultForm(
                typeof(ZTReportEventStatQueryByFileListForm_GZ)) as ZTReportEventStatQueryByFileListForm_GZ;
            cqtEventQueryByFileListForm.showCellSet(cqtWeakCoverList, cqtLowSpeedList);
            cqtEventQueryByFileListForm.Visible = true;
            cqtEventQueryByFileListForm.BringToFront();
            cqtWeakCoverList = null;
            cqtLowSpeedList = null;

        }

        protected override void afterRecieveOnePeriodData(params object[] reservedParams)
        {
            doCalculate();
        }
    }

    public class CQTLowSpeedByFiles
    {
        public string FileName { get; set; } = "";
        public string NetWorkType { get; set; } = "";    //网络类型
        public string Type { get; set; } = "";           //类型（室分、非室分）
        public string CqtEvent { get; set; } = "";       //事件
        public string AverSpeed { get; set; } = "";     //平均速率
        public string SpeedNume { get; set; } = "";     //总速率分子
        public string SpeedDenomi { get; set; } = ""; //总速率分母
    }

    public class CQTWeakCoverByFiles
    {
        public string FileName { get; set; } = "";
        public string NetWorkType { get; set; } = "";
        public string Type { get; set; } = "";
        public string CqtEvent { get; set; } = "";
        public float Coverage { get; set; }//覆盖率（室分90、非室分94）
        public int GoodSampleCount { get; set; } //达标采样点数
        public int TotalSampleCount { get; set; }//总采样点数
    }

    /// <summary>
    /// 查询文件室分类型
    /// </summary>
    public class DIYQueryCQTFileTypeInfo : DIYSQLBase
    {
        public DIYQueryCQTFileTypeInfo(MainModel mm)
            : base(mm)
        {
            mainModel = mm;
        }
        public override string Name
        {
            get { return "查询CQT文件室分类型"; }
        }
        protected override string getSqlTextString()
        {
            string Sql = "select iareatypeid,iareaid,strcover from tb_cfg_static_areainfo";
            return Sql;
        }
        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[3];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_String;

            return rType;
        }
        public Dictionary<int, Dictionary<int, AreaInfo>> areaTypeIdDic { get; set; } = new Dictionary<int, Dictionary<int, AreaInfo>>();
        Dictionary<int, AreaInfo> areaIdDic = new Dictionary<int, AreaInfo>();
        AreaInfo areaInfo;

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            areaTypeIdDic.Clear();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private void fillData(Package package)
        {
            int iAreaTypeID = package.Content.GetParamInt();
            int iAreaID = package.Content.GetParamInt();
            string strCover = package.Content.GetParamString();

            if (!areaTypeIdDic.ContainsKey(iAreaTypeID))
            {
                areaIdDic = new Dictionary<int, AreaInfo>();
                areaTypeIdDic.Add(iAreaTypeID, areaIdDic);
            }
            else
            {
                areaTypeIdDic.TryGetValue(iAreaTypeID, out areaIdDic);
            }

            if (!areaIdDic.ContainsKey(iAreaID))
            {
                areaInfo = new AreaInfo();
                areaInfo.areaTypeID = iAreaTypeID;
                areaInfo.areaID = iAreaID;
                if (strCover == "室分")
                {
                    areaInfo.coverType = (int)AreaInfo.strCover.indoor;
                }
                else
                {
                    areaInfo.coverType = (int)AreaInfo.strCover.outdoor;
                }
                areaIdDic.Add(iAreaID, areaInfo);
            }
        }
    }

    public class AreaInfo
    {
        public int areaTypeID { get; set; } = 24;
        public int areaID { get; set; } = 1;
        public int coverType { get; set; } = 1;   //室分类型(室内室外)

        public enum strCover
        {
            indoor = 0,
            outdoor = 1
        }

    }
}
