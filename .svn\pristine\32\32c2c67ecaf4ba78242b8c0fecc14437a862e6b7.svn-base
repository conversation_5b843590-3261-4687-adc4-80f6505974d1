﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class WeakSINRRoad_LteFdd
    {
        private readonly List<string> cellNames = new List<string>();
        public string CellName
        {
            get
            {
                StringBuilder cellNameStr = new StringBuilder();
                foreach (string name in cellNames)
                {
                    if (cellNameStr.Length > 0)
                    {
                        cellNameStr.Append(" | ");
                    }
                    cellNameStr.Append(name);
                }
                return cellNameStr.ToString();
            }
        }

        private readonly List<string> lacciList = new List<string>();
        public string LACCIs
        {
            get
            {
                StringBuilder laccis = new StringBuilder();
                foreach (string lacci in lacciList)
                {
                    if (laccis.Length > 0)
                    {
                        laccis.Append(" | ");
                    }
                    laccis.Append(lacci);
                }
                return laccis.ToString();
            }
        }
        public int SN
        {
            get;
            set;
        }
        public double Second
        {
            get
            {
                double sec = 0;
                if (testPoints.Count > 1)
                {
                    sec = (testPoints[testPoints.Count - 1].DateTime - testPoints[0].DateTime).TotalSeconds;
                }
                return sec;
            }
        }
        public string StartTime
        {
            get
            {
                return testPoints[0].DateTime.ToString();
            }
        }
        public double WeakPercent
        {
            get;
            set;
        }
        public double Distance
        {

            get
            {
                return Math.Round(distance, 2);
            }
            set
            {
                distance = value;
            }
        }

        private double dlCode0Qam64Rate = double.MinValue;
        public double DlCode0Qam64Rate
        {
            get
            {
                return dlCode0Qam64Rate;
            }
        }

        private double dlCode1Qam64Rate = double.MinValue;
        public double DlCode1Qam64Rate
        {
            get
            {
                return dlCode1Qam64Rate;
            }
        }
        public string FileName
        {
            get
            {
                string name = string.Empty;
                if (testPoints.Count > 0)
                {
                    name = testPoints[0].FileName;
                }
                return name;
            }
        }

        private double distance = 0;
        private readonly List<TestPoint> testPoints = new List<TestPoint>();
        public List<TestPoint> TestPoints
        {
            get { return testPoints; }
        }

        public float totalSINR { get; set; } = 0;
        public float totalRSRP { get; set; } = 0;

        public float weakSINRTestPoints { get; set; } = 0;

        private float minSINR = float.MaxValue;
        public float MinSINR
        {
            get { return minSINR; }
        }
        private float minRSPR = float.MaxValue;
        public float MinRSRP
        {
            get { return minRSPR; }
        }
        public float MaxSINR
        {
            get { return maxSINR; }
        }
        private float maxSINR = float.MinValue;
        public float MaxRSRP
        {
            get { return maxRSRP; }
        }
        private float maxRSRP = float.MinValue;

        private double avgSpeed = 0;
        public double AvgSpeed
        {
            get
            {
                return avgSpeed;
            }
        }
        public float AvgSINR
        {
            get { return (float)Math.Round(totalSINR / testPoints.Count, 2); }
        }
        public int rsrpNum { get; set; } = 0;
        public float AvgRSRP
        {
            get { return (float)Math.Round(totalRSRP / rsrpNum, 2); }
        }
        public int TestPointCount
        {
            get { return testPoints.Count; }
        }

        private double tra_ModeCount = 0;
        private double tra_Mode3Count = 0;
        private double transmission_Mode = double.MinValue;
        public double Transmission_Mode
        {
            get { return transmission_Mode; }
        }
        private double rank_Indicator = double.MinValue;
        public double Rank_Indicator
        {
            get
            {
                return rank_Indicator;
            }
        }
        
        public string RoadName { get; set; } = string.Empty;
        public double MidLng
        {
            get
            {
                double lng = double.NaN;
                if (testPoints.Count > 0)
                {
                    lng = testPoints[(testPoints.Count / 2)].Longitude;
                }
                return lng;
            }
        }
        public double MidLat
        {
            get
            {
                double lat = double.NaN;
                if (testPoints.Count > 0)
                {
                    lat = testPoints[(testPoints.Count / 2)].Latitude;
                }
                return lat;
            }
        }

        private double pdsch_Bler = double.MinValue;
        public double PDSCH_BLER
        {
            get { return pdsch_Bler; }
        }
        private double pdsch_PRb_Num_s = double.MinValue;
        public double PDSCH_PRb_Num_s
        {
            get { return pdsch_PRb_Num_s; }
        }

        internal void Add(float sinr, float? rsrp, double distance, TestPoint testPoint)
        {
            totalSINR += sinr;
            minSINR = Math.Min(minSINR, sinr);
            maxSINR = Math.Max(maxSINR, sinr);
            if (rsrp != null && rsrp >= -141 && rsrp <= -10)
            {
                rsrpNum++;
                totalRSRP += (float)rsrp;
                minRSPR = Math.Min(minRSPR, (float)rsrp);
                maxRSRP = Math.Max(maxRSRP, (float)rsrp);
            }
            this.distance += distance;
            testPoints.Add(testPoint);
        }
        internal void Add(float? sinr, float? rsrp, TestPoint testPoint)
        {
            int? lac = (int?)(ushort?)testPoint["lte_fdd_TAC"];
            LTECell lteCell = testPoint.GetMainCell_LTE_FDD();

            if (lteCell != null)
            {
                string lacci = lac.ToString() + "_" + lteCell.SCellID.ToString();
                if (!lacciList.Contains(lacci))
                {
                    lacciList.Add(lacci);
                }
                if (!cellNames.Contains(lteCell.Name))
                {
                    cellNames.Add(lteCell.Name);
                }
            }

            if (sinr != null)
            {
                totalSINR += (float)sinr;
                minSINR = Math.Min(minSINR, (float)sinr);
                maxSINR = Math.Max(maxSINR, (float)sinr);

            }

            if (rsrp != null && rsrp >= -141 && rsrp <= -10)
            {
                rsrpNum++;
                totalRSRP += (float)rsrp;
                minRSPR = Math.Min(minRSPR, (float)rsrp);
                maxRSRP = Math.Max(maxRSRP, (float)rsrp);
            }
            testPoints.Add(testPoint);
        }
        public void FindRoadName()
        {
            RoadName = GISManager.GetInstance().GetRoadPlaceDesc(MidLng, MidLat);
        }
        public void MakeSummary()
        {
            avgSpeed = this["lte_fdd_APP_Speed_Mb", SummaryType.Avg];

            double c0Qam64Sum = this["lte_fdd_Times_QAM64_DLCode0", SummaryType.Sum];
            double c0QpskSum = this["lte_fdd_Times_QPSK_DLCode0", SummaryType.Sum];
            double c0Qam16Sum = this["lte_fdd_Times_QAM16_DLCode0", SummaryType.Sum];
            dlCode0Qam64Rate = Math.Round(100.0 * c0Qam64Sum / (c0Qam64Sum + c0QpskSum + c0Qam16Sum), 2);

            double c1Qam64Sum = this["lte_fdd_Times_QAM64_DLCode1", SummaryType.Sum];
            double c1QpskSum = this["lte_fdd_Times_QPSK_DLCode1", SummaryType.Sum];
            double c1Qam16Sum = this["lte_fdd_Times_QAM16_DLCode1", SummaryType.Sum];
            dlCode1Qam64Rate = Math.Round(100.0 * c1Qam64Sum / (c1Qam64Sum + c1QpskSum + c1Qam16Sum), 2);

            pdsch_Bler = this["lte_fdd_PDSCH_BLER", SummaryType.Avg];//质差误块率

            pdsch_PRb_Num_s = this["lte_fdd_PDSCH_PRb_Num_s", SummaryType.Avg];//质差PRB调度数

            int time1Sum = 0;
            int time2Sum = 0;
            int lastRank = 0;
            int lastIdx = -1;
            for (int i = 0; i < TestPoints.Count; i++)
            {
                TestPoint tp = TestPoints[i];

                sumRankIndicator(ref time1Sum, ref time2Sum, ref lastRank, ref lastIdx, i, tp);
                int tra_Mode = 0;
                object obj1 = tp["lte_fdd_Transmission_Mode"];
                if (obj1 != null)
                {
                    int.TryParse(obj1.ToString(), out tra_Mode);

                    tra_ModeCount++;
                    if (tra_Mode == 3)
                    {
                        tra_Mode3Count++;
                    }
                }

            }
            rank_Indicator = Math.Round(time2Sum * 100.0 / (time1Sum + time2Sum), 2);
            transmission_Mode = Math.Round(tra_Mode3Count * 100.0 / tra_ModeCount, 2);
        }

        private void sumRankIndicator(ref int time1Sum, ref int time2Sum, ref int lastRank, ref int lastIdx, int i, TestPoint tp)
        {
            #region lte_Rank_Indicator质差双流比例
            bool added = false;
            int rank = 0;
            object obj = tp["lte_fdd_Rank_Indicator"];
            if (obj != null)
            {
                int.TryParse(obj.ToString(), out rank);
            }

            if (lastRank == 0 && (rank == 1 || rank == 2))
            {
                lastIdx = i;
                lastRank = rank;
            }

            if (rank != lastRank)
            {
                if (lastRank == 1)
                {
                    time1Sum += tp.Time - TestPoints[lastIdx].Time;
                    added = true;
                }
                else if (lastRank == 2)
                {
                    time2Sum += tp.Time - TestPoints[lastIdx].Time;
                    added = true;
                }
                lastIdx = i;
            }
            lastRank = rank;

            if (i == TestPoints.Count - 1 && !added)
            {//避免漏掉最后一段
                if (lastRank == 1)
                {
                    time1Sum += tp.Time - TestPoints[lastIdx].Time;
                }
                else if (lastRank == 2)
                {
                    time2Sum += tp.Time - TestPoints[lastIdx].Time;
                }
            }
            #endregion
        }

        public WeakSINRRoad_LteFdd()
        {
        }

        public WeakSINRRoad_LteFdd(WeakSINRRoad_LteFdd road)
        {
            testPoints.AddRange(road.TestPoints);
            SN = road.SN;
            distance = road.Distance;
            minSINR = road.MinSINR;
            minRSPR = road.MinRSRP;
            maxSINR = road.MaxSINR;
            maxRSRP = road.MaxRSRP;

            totalRSRP = road.totalRSRP;
            totalSINR = road.totalSINR;
            rsrpNum = road.rsrpNum;
            RoadName = road.RoadName;
        }

        public enum SummaryType
        {
            Num,
            Sum,
            Avg
        }
        public double this[string key, SummaryType type]
        {
            get
            {
                double sum = 0;
                int num = 0;
                foreach (TestPoint tp in TestPoints)
                {
                    object objV = tp[key];
                    if (objV == null)
                    {
                        continue;
                    }
                    double val;
                    if (double.TryParse(objV.ToString(), out val))
                    {
                        num++;
                        sum += val;
                    }
                }

                switch (type)
                {
                    case SummaryType.Num:
                        return num;
                    case SummaryType.Sum:
                        return Math.Round(sum, 2);
                    case SummaryType.Avg:
                        return Math.Round(sum / num, 2);
                    default:
                        break;
                }
                return double.NaN;
            }
        }

        public string MotorWay { get; set; } = "";
        public void SetMotorWay(int areaID, int areaTypeID)
        {
            MotorWay = AreaManager.GetInstance().GetAreaDesc(areaTypeID, areaID);
        }    
    }
}
