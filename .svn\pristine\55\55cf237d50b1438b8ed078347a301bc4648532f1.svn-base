﻿using System;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;
using MasterCom.Util;
using MasterCom.RAMS.ZTFunc.ZTLastWeakRoadAna;

namespace MasterCom.RAMS.CQT
{
    class DiySqlQueryReplaySampleInfoCQT : DIYSQLBase
    {

                /// <summary>
        /// SQL查询语句
        /// </summary>
        public static string strSQL { get; set; } = "select * from sysobjects where name like '%aa%'";

        public DiySqlQueryReplaySampleInfoCQT(MainModel mainModel)
            : base(mainModel)
        {
        }

        protected override string getSqlTextString()
        {
            return DiySqlQueryReplaySampleInfoCQT.strSQL;
        }

        /// <summary>
        /// 采样点信息表
        /// </summary>
        public ReplaySampleItem RsItem { get; set; } = new ReplaySampleItem();
        
        public static QueryItem QItem { get; set; } = new QueryItem();


        /// <summary>
        /// 根据QueryItem信息构造SQL，以查询ReplaySampleItem信息
        /// </summary>
        public static void mergeSql()
        {
            DateTime dTime = JavaDate.GetDateTimeFromMilliseconds(QItem.Istime * 1000L);
            string strFileName = string.Format("tb_log_file_{0:yyyy}_{0:MM}", dTime);
            strSQL = "select ifileid,iprojecttype,iservicetype,strfilename,strsampletbname,logtbname from " + strFileName + " where ifileid = " + QItem.IFileid;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            E_VType[] rType = new E_VType[6];
            rType[0] = E_VType.E_Int;
            rType[1] = E_VType.E_Int;
            rType[2] = E_VType.E_Int;
            rType[3] = E_VType.E_String;
            rType[4] = E_VType.E_String;
            rType[5] = E_VType.E_String;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            int index = 0;
            int progress = 0;
            while (true)
            {
                if (WaitBox.CancelRequest)
                {
                    break;
                }
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    RsItem.IFileid = package.Content.GetParamInt();
                    RsItem.IProjectType = package.Content.GetParamInt();
                    RsItem.IServicetType = package.Content.GetParamInt();

                    RsItem.StrFileName = package.Content.GetParamString();
                    RsItem.StrSampleName = package.Content.GetParamString();
                    RsItem.StrLogFileName = package.Content.GetParamString(); 
                    //do your code here
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
                if (Math.Log(index++) * (100000 / 10000) > WaitBox.ProgressPercent)
                {
                    progress++;
                    if (progress > 95)
                    {
                        progress = 5;
                        index = 0;
                    }
                    if (progress % 5 == 0)
                    {
                        WaitBox.ProgressPercent = progress;
                    }
                }
            }
        }

        public override string Name
        {
            get { return "DiySqlQueryReplaySampleInfo"; }
        }
    }
}

