﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraEditors;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class DIYBackgroundResultExport : DIYBackgroundResultQuery
    {
        protected DIYBackgroundResultExport()
            : base(MainModel.GetInstance())
        {
        }

        readonly List<BackgroundQueryBase> validQueryList = new List<BackgroundQueryBase>();
        readonly BackgroundFuncManager bgFuncManager = BackgroundFuncManager.GetInstance();
        readonly BackgroundFuncExportSetting exportSetting = BackgroundFuncExportSetting.GetInstance();
        DateTime beginSetTime = new DateTime();
        DateTime endSetTime = new DateTime();
        string curDistrictName = "";

        private static DIYBackgroundResultExport instance = null;
        public static DIYBackgroundResultExport GetInstance()
        {
            if (instance == null)
            {
                instance = new DIYBackgroundResultExport();
            }
            return instance;
        }
        public override string Name
        {
            get { return "网络体检结果导出"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 21000, 21040, this.Name);
        }

        public void SetCond(string districtName, List<BackgroundQueryBase> activeQueryList)
        {
            this.curDistrictName = districtName;
            this.validQueryList.Clear();
            foreach (BackgroundQueryBase queryFunc in activeQueryList)
            {
                if (!queryFunc.BackgroundStat || queryFunc.IsIgnoreExport)
                {
                    continue;
                }
                validQueryList.Add(queryFunc);
            }
        }
        protected void initBackgroundQueryCondition()
        {
            QueryCondition queryCond = new QueryCondition();
            SetQueryCondition(queryCond);

            Condition.Geometorys = new SearchGeometrys();
            Condition.Geometorys.Region = BackgroundFuncConfigManager.GetInstance().RegionBorder;

            Condition.Projects = BackgroundFuncBaseSetting.GetInstance().ProjectTypeList;
        }

        protected override void query()
        {
            if (validQueryList.Count <= 0)
            {
                bgFuncManager.ReportBackgroundInfo("没有开启需要导出体检结果的专题功能！");
                return;
            }

            ClientProxy clientProxy = new ClientProxy();
            try
            {
                bgFuncManager.ReportBackgroundInfo("开始查询网络体检结果...");

                BackgroundFuncConfigManager bgCfgManager = BackgroundFuncConfigManager.GetInstance();
                beginSetTime = bgCfgManager.StartTime;
                endSetTime = bgCfgManager.EndTime;

                if (clientProxy.connect(MainModel.Server.IP, MainModel.Server.Port, MainModel.User.LoginName, MainModel.User.Password, MainModel.DistrictID) != ConnectResult.Success)
                {
                    ErrorInfo = "连接服务器失败!";
                    return;
                }

                initBackgroundQueryCondition();
                if (exportSetting.IsExportByDay)
                {
                    queryInThread(TimeGatherMode.Day);
                }
                if (exportSetting.IsExportByWeek)
                {
                    queryInThread(TimeGatherMode.Week);
                }
                if (exportSetting.IsExportByMonth)
                {
                    queryInThread(TimeGatherMode.Month);
                }
                if (exportSetting.IsExportByDiyPeriod)
                {
                    queryInThread(TimeGatherMode.Period);
                }
                if (exportSetting.IsExportByAll)
                {
                    queryInThread(TimeGatherMode.AllSum);
                }
                //MainModel.FireDTDataChanged(this);
            }
            finally
            {
                clientProxy.Close();
                validQueryList.Clear();
            }
        }
        protected bool isPeriodResultCanExport(DateTime periodBeginTime, TimeGatherMode mode)
        {
            if (exportSetting.ExportNow)
            {
                return true;
            }
            else if (exportSetting.ExportFirstDay)
            {
                DateTime dateNow = DateTime.Now.Date;
                bool isCanExport = false;
                switch (mode)
                {
                    case TimeGatherMode.Day:
                        if (dateNow == periodBeginTime.AddDays(1).Date)
                        {
                            isCanExport = true;
                        }
                        break;
                    case TimeGatherMode.Week:
                        if (dateNow == periodBeginTime.AddDays(7).Date)
                        {
                            isCanExport = true;
                        }
                        break;
                    case TimeGatherMode.Month:
                        if (dateNow == periodBeginTime.AddMonths(1).Date)
                        {
                            isCanExport = true;
                        }
                        break;
                    case TimeGatherMode.Period:
                        break;
                    case TimeGatherMode.AllSum:
                        isCanExport = true;
                        break;
                    default:
                        break;
                }
                return isCanExport;
            }
            return false;
        }
        protected void queryInThread(TimeGatherMode mode)
        {
            string timeModeDes = getTimeGatherModeDes(mode);
            DateTime curCycleBeginTime = GetDateTime(mode, beginSetTime);
            DateTime endCycleBeginTime = GetDateTime(mode, endSetTime.AddMilliseconds(-1));

            while (true)
            {
                if (MainModel.BackgroundStopRequest)
                {
                    break;
                }

                DateTime nextCycleBeginTime;
                if (mode == TimeGatherMode.Period || mode == TimeGatherMode.AllSum)
                {
                    nextCycleBeginTime = endCycleBeginTime;
                }
                else
                {
                    nextCycleBeginTime = GetNextDateTime(mode, curCycleBeginTime);
                }

                if (isPeriodResultCanExport(curCycleBeginTime, mode))
                {
                    TimePeriod period = new TimePeriod(curCycleBeginTime, nextCycleBeginTime.AddMilliseconds(-1));
                    queryInThreadByTimePeriod(period, timeModeDes);
                }

                if (curCycleBeginTime >= endCycleBeginTime)
                {
                    break;
                }
                curCycleBeginTime = nextCycleBeginTime;
            }
        }
        private void queryInThreadByTimePeriod(TimePeriod period, string timeModeDes)
        {
            try
            {
                MainModel.ClearDTData();
                condition.Periods.Clear();
                condition.Periods.Add(period);

                MainModel.QueryFromBackground = true;
                MainModel.SystemConfigInfo.isBackground = false;
                foreach (BackgroundQueryBase queryFunc in validQueryList)
                {
                    if (mainModel.BackgroundStopRequest)
                    {
                        break;
                    }
                    bgFuncManager.ReportBackgroundInfo("开始" + timeModeDes + "查询" + queryFunc.FuncType.ToString()
                        + queryFunc.SubFuncType.ToString() + "类" + queryFunc.Name + "功能 在" + period.ToString() + "时段内的网络体检结果...");

                    queryFunc.SetQueryCondition(condition);
                    queryFunc.ResetServTypesAndCarrierID();
                    queryFunc.Query();
                    exportResult(queryFunc, timeModeDes, period);
                }
            }
            catch (Exception ex)
            {
                bgFuncManager.ReportBackgroundError(ex);
            }
            finally
            {
                MainModel.QueryFromBackground = false;
                MainModel.SystemConfigInfo.isBackground = true;
                System.Threading.Thread.Sleep(20);
            }
        }
        private void exportResult(BackgroundQueryBase queryFunc, string timeModeDes, TimePeriod period)
        {
            if (period == null)
            {
                return;
            }

            if ((queryFunc.BackgroundNPOIRowResultDic == null || queryFunc.BackgroundNPOIRowResultDic.Count <= 0)
                && (queryFunc.BackgroundResultList == null || queryFunc.BackgroundResultList.Count <= 0))
            {
                bgFuncManager.ReportBackgroundInfo("未查询到网络体检结果");
                return;
            }

            try
            {
                string strPeriod = string.Format("{0}_{1}", period.BeginTime.ToString("yyyyMMdd")
                    , period.EndTime.ToString("yyyyMMdd"));

                //与智能预判对接，文件名格式定义为"地市_功能名称_时段.后缀"
                string fieNameWithoutExtension = string.Format("{0}_{1}_{2}", curDistrictName, queryFunc.Name, strPeriod);
                List<string> sheetNames = new List<string>();
                List<string> csvFileNames = new List<string>();
                List<List<NPOIRow>> npoiRowsList = new List<List<NPOIRow>>();
                getExportResults(queryFunc, ref npoiRowsList, ref sheetNames, fieNameWithoutExtension, ref csvFileNames);

                string folderName = getSaveFolderName(timeModeDes, strPeriod);
                if (exportSetting.IsExportResultToXls)
                {
                    ExcelNPOIManager.ExportToExcel(npoiRowsList, sheetNames
                           , string.Format("{0}\\{1}.xlsx", folderName, fieNameWithoutExtension));

                    if (ExcelNPOIManager.error)
                    {
                        bgFuncManager.ReportBackgroundInfo("导出网络体检Excel结果出错,开始导出为Csv格式...");
                        exportResultToCsv(npoiRowsList, csvFileNames, folderName);
                    }
                }
                else
                {
                    exportResultToCsv(npoiRowsList, csvFileNames, folderName);
                }

                #region 导出结果图层
                try
                {
                    if (exportSetting.IsExportResultMapToWord && queryFunc.IsCanExportResultMapToWord)
                    {
                        string wordFolderName = string.Format("{0}\\结果图层", folderName);
                        if (!System.IO.Directory.Exists(wordFolderName))
                        {
                            System.IO.Directory.CreateDirectory(wordFolderName);
                        }

                        BackgroundResultToWord.ExportToWord(queryFunc.BackgroundResultList
                            , string.Format("{0}\\{1}.doc", wordFolderName, fieNameWithoutExtension));
                    }
                }
                catch (Exception ex)
                {
                    bgFuncManager.ReportBackgroundError(ex);
                }
                #endregion

                bgFuncManager.ReportBackgroundInfo("成功导出网络体检结果");
            }
            catch(Exception ex)
            {
                bgFuncManager.ReportBackgroundError("导出网络体检结果出错：" + ex.Message);
            }
            finally
            {
                queryFunc.BackgroundResultList.Clear();
                queryFunc.BackgroundNPOIRowResultDic.Clear();
                mainModel.DTDataManager.Clear();
            }
        }
        private string getSaveFolderName(string timeModeDes, string strPeriod)
        {
            string folderName;
            if (exportSetting.IsSaveResultSumByDiffFolder)
            {
                if (timeModeDes.Equals("汇总"))
                {
                    folderName = string.Format("{0}\\{1}\\{2}", exportSetting.ExportResultFolderPath
                        , timeModeDes, curDistrictName);
                }
                else
                {
                    folderName = string.Format("{0}\\{1}\\{2}\\{3}", exportSetting.ExportResultFolderPath
                        , timeModeDes, strPeriod, curDistrictName);// 总文件夹-汇聚方式-时段-地市格式
                }
            }
            else
            {
                folderName = exportSetting.ExportResultFolderPath;
            }

            if (!System.IO.Directory.Exists(folderName))
            {
                System.IO.Directory.CreateDirectory(folderName);
            }
            return folderName;
        }
        private void exportResultToCsv(List<List<NPOIRow>> npoiRowsList
            , List<string> csvFileNames, string folderName)
        {
            int i = 0;
            foreach (List<NPOIRow> rowList in npoiRowsList)
            {
                string filePath = string.Format("{0}\\{1}.csv", folderName, csvFileNames[i]);
                CSVWriter.ExportToCsv(rowList, filePath);
                i++;
            }
        }

        /// <summary>
        /// 获取导出的表信息
        /// </summary>
        /// <param name="queryFunc"></param>
        /// <param name="npoiRowsList">表内容</param>
        /// <param name="sheetNames">sheet表名</param>
        /// <param name="fieNameWithoutExtension"></param>
        /// <param name="csvFileNames">csv文件名（不包含后缀）</param>
        private void getExportResults(BackgroundQueryBase queryFunc, ref List<List<NPOIRow>> npoiRowsList
            , ref List<string> sheetNames, string fieNameWithoutExtension, ref List<string> csvFileNames)
        {
            //sheet表名不能超过31个字符
            if (queryFunc.BackgroundNPOIRowResultDic == null || queryFunc.BackgroundNPOIRowResultDic.Count <= 0)
            {
                npoiRowsList.Add(bgResultToNPOIRow(queryFunc.BackgroundResultList));
                sheetNames.Add(queryFunc.Name);
                csvFileNames.Add(fieNameWithoutExtension);
            }
            else
            {
                npoiRowsList.AddRange(queryFunc.BackgroundNPOIRowResultDic.Values);
                foreach (string strKey in queryFunc.BackgroundNPOIRowResultDic.Keys)
                {
                    if (string.IsNullOrEmpty(strKey))
                    {
                        sheetNames.Add(queryFunc.Name);
                        csvFileNames.Add(fieNameWithoutExtension);
                    }
                    else
                    {
                        sheetNames.Add(strKey);
                        if (npoiRowsList.Count > 1)//一个专题功能有多个csv时，csv文件名加上strKey
                        {
                            csvFileNames.Add(fieNameWithoutExtension + "_" + strKey);
                        }
                        else
                        {
                            csvFileNames.Add(fieNameWithoutExtension);
                        }
                    }
                }
            }
        }
        private List<NPOIRow> bgResultToNPOIRow(List<BackgroundResult> resultList)
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            if (resultList == null || resultList.Count <= 0)
            {
                return rows;
            }
            BackgroundStatType statType = resultList[0].StatType;
            BackgroundCellType cellType = resultList[0].CellType;
            fillRowColHeader(resultList, rows, statType, cellType);
            fillRowResult(resultList, rows, statType);
            return rows;
        }

        private string getLacDes(BackgroundCellType type)
        {
            if (type == BackgroundCellType.LTE)
            {
                return "TAC";
            }
            else
            {
                return "LAC";
            }
        }

        private string getCiDes(BackgroundCellType type)
        {
            if (type == BackgroundCellType.LTE)
            {
                return "ECI";
            }
            else
            {
                return "CI";
            }
        }

        private void fillRowColHeader(List<BackgroundResult> resultList, List<NPOIRow> rows, BackgroundStatType statType, BackgroundCellType cellType)
        {
            NPOIRow row = new NPOIRow();
            if (statType == BackgroundStatType.Road)
            {
                row.AddCellValue("文件名");
                row.AddCellValue("起始经度");
                row.AddCellValue("起始纬度");
                row.AddCellValue("终止经度");
                row.AddCellValue("终止纬度");
                row.AddCellValue("中心经度");
                row.AddCellValue("中心纬度");
                row.AddCellValue("起始时间");
                row.AddCellValue("终止时间");
                row.AddCellValue("持续时长(秒)");
                row.AddCellValue("持续距离(米)");
                row.AddCellValue("采样点数");
                row.AddCellValue("平均场强");
                row.AddCellValue("最小场强");
                row.AddCellValue("最大场强");
                row.AddCellValue("平均质量");
                row.AddCellValue("最小质量");
                row.AddCellValue("最大质量");
            }
            else if (statType == BackgroundStatType.Cell_Road)
            {
                row.AddCellValue("文件名");
                row.AddCellValue("小区名称");
                row.AddCellValue("小区类型");
                row.AddCellValue(getLacDes(cellType));
                row.AddCellValue(getCiDes(cellType));
                row.AddCellValue("频点");
                row.AddCellValue("色码(扰码)");
                row.AddCellValue("起始经度");
                row.AddCellValue("起始纬度");
                row.AddCellValue("终止经度");
                row.AddCellValue("终止纬度");
                row.AddCellValue("中心经度");
                row.AddCellValue("中心纬度");
                row.AddCellValue("起始时间");
                row.AddCellValue("终止时间");
                row.AddCellValue("持续时长(秒)");
                row.AddCellValue("持续距离(米)");
                row.AddCellValue("采样点数");
                row.AddCellValue("平均场强");
                row.AddCellValue("最小场强");
                row.AddCellValue("最大场强");
                row.AddCellValue("平均质量");
                row.AddCellValue("最小质量");
                row.AddCellValue("最大质量");
            }
            else if (statType == BackgroundStatType.Region)
            {
                row.AddCellValue("中心经度");
                row.AddCellValue("中心纬度");
                row.AddCellValue("起始时间");
                row.AddCellValue("终止时间");
                row.AddCellValue("采样点数");
                row.AddCellValue("平均场强");
                row.AddCellValue("最小场强");
                row.AddCellValue("最大场强");
                row.AddCellValue("平均质量");
                row.AddCellValue("最小质量");
                row.AddCellValue("最大质量");
            }
            else if (statType == BackgroundStatType.Cell_Region)
            {
                row.AddCellValue("小区名称");
                row.AddCellValue("小区类型");
                row.AddCellValue(getLacDes(cellType));
                row.AddCellValue(getCiDes(cellType));
                row.AddCellValue("频点");
                row.AddCellValue("色码(扰码)");
                row.AddCellValue("中心经度");
                row.AddCellValue("中心纬度");
                row.AddCellValue("起始时间");
                row.AddCellValue("终止时间");
                row.AddCellValue("采样点数");
                row.AddCellValue("平均场强");
                row.AddCellValue("最小场强");
                row.AddCellValue("最大场强");
                row.AddCellValue("平均质量");
                row.AddCellValue("最小质量");
                row.AddCellValue("最大质量");
            }
            else if (statType == BackgroundStatType.Cell_Simple)
            {
                row.AddCellValue("小区名称");
                row.AddCellValue("小区类型");
                row.AddCellValue(getLacDes(cellType));
                row.AddCellValue(getCiDes(cellType));
                row.AddCellValue("频点");
                row.AddCellValue("色码(扰码)");
                row.AddCellValue("采样点数");
            }

            addImgCol(resultList, row);

            if (statType != BackgroundStatType.Cell_Simple && statType != BackgroundStatType.None)
            {
                row.AddCellValue("道路");
                row.AddCellValue("片区");
                row.AddCellValue("网格");
                row.AddCellValue("代维片区");
                row.AddCellValue("涉及小区");
            }
            row.AddCellValue("备注");
            rows.Add(row);
        }

        private void addImgCol(List<BackgroundResult> resultList, NPOIRow row)
        {
            if (resultList.Count > 0)
            {
                string strBgResult = resultList[0].ImageDesc;
                string[] strBgResultList = strBgResult.Replace("\r\n", "^").Split('^');
                for (int i = 0; i < strBgResultList.Length; i++)
                {
                    row.AddCellValue(strBgResultList[i].Split('：')[0]);
                }
            }
        }

        private void fillRowResult(List<BackgroundResult> resultList, List<NPOIRow> rows, BackgroundStatType statType)
        {
            int sn = 1;
            foreach (BackgroundResult bgResult in resultList)
            {
                bgResult.SN = sn++;
                NPOIRow row = new NPOIRow();

                if (statType == BackgroundStatType.Road)
                {
                    row.AddCellValue(bgResult.FileName);
                    row.AddCellValue(bgResult.LongitudeStart);
                    row.AddCellValue(bgResult.LatitudeStart);
                    row.AddCellValue(bgResult.LongitudeEnd);
                    row.AddCellValue(bgResult.LatitudeEnd);
                    row.AddCellValue(bgResult.LongitudeMid);
                    row.AddCellValue(bgResult.LatitudeMid);
                    row.AddCellValue(bgResult.DateTimeBeginString);
                    row.AddCellValue(bgResult.DateTimeEndString);
                    row.AddCellValue(bgResult.TimeLast);
                    row.AddCellValue(bgResult.DistanceLast);
                    row.AddCellValue(bgResult.SampleCount);
                    row.AddCellValue(bgResult.RxLevMean);
                    row.AddCellValue(bgResult.RxLevMin);
                    row.AddCellValue(bgResult.RxLevMax);
                    row.AddCellValue(bgResult.RxQualMean);
                    row.AddCellValue(bgResult.RxQualMin);
                    row.AddCellValue(bgResult.RxQualMax);
                }
                else if (statType == BackgroundStatType.Cell_Road)
                {
                    row.AddCellValue(bgResult.FileName);
                    row.AddCellValue(bgResult.CellName);
                    row.AddCellValue(bgResult.CellType.ToString());
                    row.AddCellValue(bgResult.LAC);
                    row.AddCellValue(bgResult.CI);
                    row.AddCellValue(bgResult.BCCH);
                    row.AddCellValue(bgResult.BSIC);
                    row.AddCellValue(bgResult.LongitudeStart);
                    row.AddCellValue(bgResult.LatitudeStart);
                    row.AddCellValue(bgResult.LongitudeEnd);
                    row.AddCellValue(bgResult.LatitudeEnd);
                    row.AddCellValue(bgResult.LongitudeMid);
                    row.AddCellValue(bgResult.LatitudeMid);
                    row.AddCellValue(bgResult.DateTimeBeginString);
                    row.AddCellValue(bgResult.DateTimeEndString);
                    row.AddCellValue(bgResult.TimeLast);
                    row.AddCellValue(bgResult.DistanceLast);
                    row.AddCellValue(bgResult.SampleCount);
                    row.AddCellValue(bgResult.RxLevMean);
                    row.AddCellValue(bgResult.RxLevMin);
                    row.AddCellValue(bgResult.RxLevMax);
                    row.AddCellValue(bgResult.RxQualMean);
                    row.AddCellValue(bgResult.RxQualMin);
                    row.AddCellValue(bgResult.RxQualMax);
                }
                else if (statType == BackgroundStatType.Region)
                {
                    row.AddCellValue(bgResult.LongitudeMid);
                    row.AddCellValue(bgResult.LatitudeMid);
                    row.AddCellValue(bgResult.DateTimeBeginString);
                    row.AddCellValue(bgResult.DateTimeEndString);
                    row.AddCellValue(bgResult.SampleCount);
                    row.AddCellValue(bgResult.RxLevMean);
                    row.AddCellValue(bgResult.RxLevMin);
                    row.AddCellValue(bgResult.RxLevMax);
                    row.AddCellValue(bgResult.RxQualMean);
                    row.AddCellValue(bgResult.RxQualMin);
                    row.AddCellValue(bgResult.RxQualMax);
                }
                else if (statType == BackgroundStatType.Cell_Region)
                {
                    row.AddCellValue(bgResult.CellName);
                    row.AddCellValue(bgResult.CellType.ToString());
                    row.AddCellValue(bgResult.LAC);
                    row.AddCellValue(bgResult.CI);
                    row.AddCellValue(bgResult.BCCH);
                    row.AddCellValue(bgResult.BSIC);
                    row.AddCellValue(bgResult.LongitudeMid);
                    row.AddCellValue(bgResult.LatitudeMid);
                    row.AddCellValue(bgResult.DateTimeBeginString);
                    row.AddCellValue(bgResult.DateTimeEndString);
                    row.AddCellValue(bgResult.SampleCount);
                    row.AddCellValue(bgResult.RxLevMean);
                    row.AddCellValue(bgResult.RxLevMin);
                    row.AddCellValue(bgResult.RxLevMax);
                    row.AddCellValue(bgResult.RxQualMean);
                    row.AddCellValue(bgResult.RxQualMin);
                    row.AddCellValue(bgResult.RxQualMax);
                }
                else if (statType == BackgroundStatType.Cell_Simple)
                {
                    row.AddCellValue(bgResult.CellName);
                    row.AddCellValue(bgResult.CellType.ToString());
                    row.AddCellValue(bgResult.LAC);
                    row.AddCellValue(bgResult.CI);
                    row.AddCellValue(bgResult.BCCH);
                    row.AddCellValue(bgResult.BSIC);
                    row.AddCellValue(bgResult.SampleCount);
                }

                addImgRes(bgResult, row);

                if (statType != BackgroundStatType.Cell_Simple && statType != BackgroundStatType.None)
                {
                    row.AddCellValue(bgResult.RoadDesc);
                    row.AddCellValue(bgResult.AreaDesc);
                    row.AddCellValue(bgResult.GridDesc);
                    row.AddCellValue(bgResult.AreaAgentDesc);
                    row.AddCellValue(bgResult.CellIDDesc);
                }
                row.AddCellValue(bgResult.StrDesc);
                rows.Add(row);
            }
        }

        private void addImgRes(BackgroundResult bgResult, NPOIRow row)
        {
            if (!string.IsNullOrEmpty(bgResult.ImageDesc))
            {
                string[] strBgResultList = bgResult.ImageDesc.Replace("\r\n", "^").Split('^');
                if (strBgResultList[0] != "")
                {
                    for (int i = 0; i < strBgResultList.Length; i++)
                    {
                        row.AddCellValue(strBgResultList[i].Split('：')[1]);
                    }
                }
            }
        }

        protected string getTimeGatherModeDes(TimeGatherMode statMode)
        {
            string timeModeDes = "";
            switch (statMode)
            {
                case TimeGatherMode.Day:
                    timeModeDes = "按天汇聚";
                    break;
                case TimeGatherMode.Week:
                    timeModeDes = "按周汇聚";
                    break;
                case TimeGatherMode.Month:
                    timeModeDes = "按月汇聚";
                    break;
                case TimeGatherMode.Period:
                    timeModeDes = "按时段汇聚";
                    break;
                case TimeGatherMode.AllSum:
                    timeModeDes = "汇总";
                    break;
                default:
                    timeModeDes = statMode.ToString();
                    break;
            }
            return timeModeDes;
        }

        public static DateTime GetDateTime(TimeGatherMode statMode, DateTime dateTime)
        {
            if (statMode == TimeGatherMode.Day)
            {
                return dateTime.Date;
            }
            else if (statMode == TimeGatherMode.Week)
            {
                int beginDayOfWeek = (int)dateTime.DayOfWeek;
#if Jiangxi
                beginDayOfWeek = beginDayOfWeek + 1;//江西奇葩要求：周按照上周六至本周五来算
                if (beginDayOfWeek == 7)
                {
                    beginDayOfWeek = 0;
                }
#endif
                return dateTime.Date.AddDays(-1 * beginDayOfWeek);
            }
            else if (statMode == TimeGatherMode.Month)
            {
                return new DateTime(dateTime.Year, dateTime.Month, 1);
            }
            return dateTime;
        }

        public static DateTime GetNextDateTime(TimeGatherMode statMode, DateTime dateTime)
        {
            if (statMode == TimeGatherMode.Day)
            {
                return dateTime.AddDays(1);
            }
            else if (statMode == TimeGatherMode.Week)
            {
                return dateTime.AddDays(7);
            }
            else if (statMode == TimeGatherMode.Month)
            {
                return dateTime.AddMonths(1);
            }
            return dateTime;
        }
    }
}
