﻿using System;
using System.Collections.Generic;
using System.Text;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.NOP
{
    public class ShowTaskOrderStatForm : QueryBase
    {
        public ShowTaskOrderStatForm()
            : base(MainModel.GetInstance())
        {
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override string Name
        {
            get { return "工单统计汇总"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 29000, 29002, this.Name);
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        TaskOrderStatForm statForm = null;

        protected override void query()
        {
            if (statForm == null || statForm.IsDisposed)
            {
                statForm = new TaskOrderStatForm();
            }
            statForm.Visible = true;
            statForm.Owner = MainModel.MainForm;
            statForm.BringToFront();
        }
    }
}
