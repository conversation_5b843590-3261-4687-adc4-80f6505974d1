﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    class ZTWeakSINRReason_NB : ZTWeakSINRReason
    {
        public ZTWeakSINRReason_NB(ServiceName serviceName)
            : base(serviceName)
        {
        }

        public override string Name
        {
            get { return "质差原因分析"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 34000, 34010, this.Name);
        }
    }
}
