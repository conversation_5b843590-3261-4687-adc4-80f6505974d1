using System;
using System.Collections.Generic;
using System.Text;
using System.Resources;

namespace MasterCom.RAMS
{
    public class MtResourceMnger
    {
        private readonly ResourceManager locRM = new ResourceManager("MasterCom.RAMS.Res.FrameRes", System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Assembly);
        private static MtResourceMnger instance = new MtResourceMnger();
        public static MtResourceMnger GetInstance()
        {
            return instance;
        }
        public string GetString(string key)
        {
            return locRM.GetString(key);
        }
    }
}
