﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    class LteURLModel
    {
        public LteURLModel(DTFileDataManager fileManager)
        {
            this.Videos = new List<LteURLVideoURL>();
            this.Bros = new List<LteURLBroURL>();
            this.Downs = new List<LteURLDowURL>();
            this.FileName = fileManager.FileName;

        }

        public string FileName
        {
            get;
            private set;
        }

        public string DistrictName
        {
            get;
            set;
        }

        public List<LteURLBroURL> Bros
        {
            get;
            set;
        }

        public List<LteURLDowURL> Downs
        {
            get;
            set;
        }

        public List<LteURLVideoURL> Videos
        {
            get;
            set;
        }

        public int SN
        {
            get;
            set;
        }

        public int BroCount
        {
            get { return Bros.Count; }
        }

        public int DownCount
        {
            get { return Downs.Count; }
        }

        public int VideoCount
        {
            get { return Videos.Count; }
        }
    }

    class LteURLVideoURL
    {
        public LteURLVideoURL(string url)
        {
            this.URL = url;
            this.Events = new List<LteURLEvent>();
            this.EvtLastData = new List<Event>();
            this.EvtPlayStart = new List<Event>();
            this.EvtRebuffer = new List<Event>();
            this.EvtReq = new List<Event>();
            this.EvtSuc = new List<Event>();
            this.EvtRebufferEnd = new List<Event>();
            this.EvtFlvPlayFinished = new List<Event>();
            this.RebufferTime = 0;
            this.value1Sum35 = 0;
            this.value1Sum38 = 0;
            this.value2Sum35 = 0;
            this.value4Sum35 = 0;
            this.value5Sum35 = 0;
            this.value6Sum35 = 0;
            this.value7Sum35 = 0;
            this.value2Sum29 = 0;
            this.value2Sum34 = 0;
        }

        public string URL
        {
            get;
            private set;
        }

        public int SN
        {
            get;
            set;
        }

        public List<LteURLEvent> Events
        {
            get;
            private set;
        }

        /// <summary>
        /// 事件ID1231
        /// </summary>
        public List<Event> EvtReq
        {
            get;
            private set;
        }

        /// <summary>
        /// 事件ID1236
        /// </summary>
        public List<Event> EvtSuc
        {
            get;
            private set;
        }

        /// <summary>
        /// 事件ID1238
        /// </summary>
        public List<Event> EvtPlayStart
        {
            get;
            private set;
        }

        /// <summary>
        /// 事件ID1235
        /// </summary>
        public List<Event> EvtLastData
        {
            get;
            private set;
        }

        /// <summary>
        /// 事件ID1234
        /// </summary>
        public List<Event> EvtRebufferEnd
        {
            get;
            private set;
        }

        /// <summary>
        /// 事件ID1233
        /// </summary>
        public List<Event> EvtRebuffer
        {
            get;
            private set;
        }

        /// <summary>
        /// 事件ID1229
        /// </summary>
        public List<Event> EvtFlvPlayFinished
        {
            get;
            private set;
        }

        public int ReqCount
        {
            get { return EvtReq.Count; }
        }

        public int SucCount
        {
            get { return EvtSuc.Count; }
        }

        public double? SucRate
        {
            get { return Math.Round(((float)SucCount / (float)ReqCount), 4); }
        }

        public double? Delay
        {
            get;
            set;
        }

        public double? Time
        {
            get;
            set;
        }

        public double? RebufferTime
        {
            get;
            set;
        }

        public double? PlayTime
        {
            get;
            set;
        }

        public int RebufferCount
        {
            get { return EvtRebuffer.Count; }
        }

        public int RebufferEndCount
        {
            get { return EvtRebufferEnd.Count; }
        }

        public int FlvPlayFinishedCount
        {
            get { return EvtFlvPlayFinished.Count; }
        }

        public double? TimeoutRate
        {
            get;
            set;
        }

        public double? DownSpeed
        {
            get;
            set;
        }

        public double? LoadSpeed
        {
            get;
            set;
        }

        public double? value1Sum38
        {
            get;
            set;
        }

        public double? value7Sum35
        {
            get;
            set;
        }

        public double? value6Sum35
        {
            get;
            set;
        }

        public double? value5Sum35
        {
            get;
            set;
        }

        public double? value4Sum35
        {
            get;
            set;
        }

        public double? value2Sum35
        {
            get;
            set;
        }

        public double? value1Sum35
        {
            get;
            set;
        }

        public double? value2Sum34
        {
            get;
            set;
        }

        public double? value2Sum29
        {
            get;
            set;
        }

    }

    class LteURLDowURL
    {
        public LteURLDowURL(string url)
        {
            this.URL = url;
            this.Events = new List<LteURLEvent>();
            this.EvtDowFai = new List<Event>();
            this.EvtDowSuc = new List<Event>();
            this.EvtFail = new List<Event>();
            this.Value1Sum = 0;
            this.Value1Sum1 = 0;
            this.Value2Sum = 0;
            this.Value2Sum1 = 0;
        }

        public List<LteURLEvent> Events
        {
            get;
            private set;
        }

        public List<Event> EvtDowSuc
        {
            get;
            private set;
        }

        public List<Event> EvtFail
        {
            get;
            private set;
        }

        public List<Event> EvtDowFai
        {
            get;
            private set;
        }

        public int SN
        {
            get;
            set;
        }

        public string URL
        {
            get;
            private set;
        }

        public int Dowcount
        {
            get { return (EvtDowFai.Count + EvtDowSuc.Count + EvtFail.Count); }
        }

        public int DowSuc
        {
            get { return EvtDowSuc.Count; }
        }

        public double? DowSucRate
        {
            get { return Math.Round(((float)DowSuc / (float)Dowcount),4); }
        }

        public int DowFail
        {
            get { return EvtDowFai.Count; }
        }

        public double? DowFaiRate
        {
            get { return Math.Round(((float)DowFail / (float)Dowcount), 4); }
        }

        public double? SucSpeed
        {
            get;
            set;
        }

        public double? Speed
        {
            get;
            set;
        }

        public double? Value1Sum
        {
            get;
            set;
        }

        public double? Value2Sum
        {
            get;
            set;
        }

        public double? Value1Sum1
        {
            get;
            set;
        }

        public double? Value2Sum1
        {
            get;
            set;
        }
    }

    class LteURLBroURL
    {
        public LteURLBroURL(string url)
        {
            this.Events = new List<LteURLEvent>();
            this.EvtComple = new List<Event>();
            this.EvtDisFai = new List<Event>();
            this.EvtDisSuc = new List<Event>();
            this.URL = url;
            this.Value1Sum = 0;
            this.Value2Sum = 0;
            this.Value2Sum1 = 0;
        }

        public List<Event> EvtDisSuc
        {
            get;
            set;
        }

        public List<Event> EvtDisFai
        {
            get;
            private set;
        }

        public List<Event> EvtComple
        {
            get;
            private set;
        }

        public List<LteURLEvent> Events
        {
            get;
            private set;
        }

        public int SN
        {
            get;
            set;
        }

        public int DisCount
        {
            get { return EvtDisSuc.Count + EvtDisFai.Count; }
        }

        public int DisSuc
        {
           get { return EvtDisSuc.Count; }
        }

        public int Complete
        {
            get { return EvtComple.Count; }
        }

        public double? DisSucRate
        {
            get { return Math.Round(((float)DisSuc / (float)DisCount),4); }
        }

        public double? DisDelay
        {
            get;
            set;
        }

        public double? SucRate
        {
            get { return Math.Round(((float)Complete / (float)DisSuc),4); }
        }

        public double? Time
        {
            get;
            set;
        }

        public double? Speed
        {
            get;
            set;
        }

        public string URL
        {
            get;
            private set;
        }

        public double? Value2Sum
        {
            get;
            set;
        }

        public double? Value1Sum
        {
            get;
            set;
        }

        public double? Value2Sum1
        {
            get;
            set;
        }
    }

    class LteURLEvent
    {
        public LteURLEvent(Event evtStart, Event evtEnd, Event evtError, string url)
        {
            this.EventStart = evtStart;
            if(evtStart != null)
            {
                this.FileName = evtStart.FileName;
                this.StartTime = evtStart.DateTime.ToString("yy-MM-dd HH:mm:ss.fff");
                this.StartName = evtStart.Name;
            }            
            if (evtEnd != null)
            {
                this.EndTime = evtEnd.DateTime.ToString("yy-MM-dd HH:mm:ss.fff");
                this.EvtEndName = evtEnd.Name;
                this.FileName = evtEnd.FileName;
                if (evtStart != null)
                {
                    this.TimeSpan = (evtEnd.DateTime - evtStart.DateTime).TotalSeconds.ToString();
                }
            }
            this.URL = url;            
            this.EventEnd = evtEnd;
            this.EventError = evtError;
            this.GetInfo();
        }

        public Event EventStart
        { get; private set; }

        public Event EventError
        {
            get;
            private set;
        }

        public Event EventEnd
        {
            get;
            private set;
        }

        public int SN
        {
            get;
            set;
        }

        public string URL
        {
            get;
            private set;
        }

        public string RegionName
        {
            get;
            private set;
        }

        public string GridName
        {
            get;
            private set;
        }

        public string IsFail
        {
            get;
            private set;
        }

        public string StartTime
        {
            get;
            private set;
        }

        public string EndTime
        {
            get;
            private set;
        }

        public string TimeSpan
        {
            get;
            private set;
        }

        public string StartName
        {
            get;
            private set;
        }

        public string EvtEndName
        {
            get;
            private set;
        }

        public string FailReason
        {
            get;
            private set;
        }

        public double? Bytes
        {
            get;
            private set;
        }

        public string FileName
        {
            get;
            private set;
        }

        private void GetInfo()
        {
            if (this.EventError != null)
            {
                IsFail = "失败";
                switch (this.EventError.ID)
                {
                    case 1269:
                    case 3269:
                        this.FailReason = GetReason(int.Parse(this.EventError["Value3"].ToString()));
                        break;
                    case 1270:
                    case 3270:
                        this.FailReason = GetReason(int.Parse(this.EventError["Value3"].ToString()));
                        break;
                    case 1239:
                    case 3239:
                        this.FailReason = GetReason(int.Parse(this.EventError["Value3"].ToString()));
                        break;
                    default:
                        this.FailReason = "s_APPNotDefined";
                        break;
                }
            }
            else if (EventEnd != null)
            {
                if (EventEnd.ID == (int)LteURLCheckMsg.HttpCompleteID
                    || EventEnd.ID == (int)LteURLCheckMsg.VideoFinish
                    || EventEnd.ID == (int)LteURLCheckMsg.DownSuccessID
                    || EventEnd.ID == (int)LteURLCheckMsg.HttpCompleteID_FDD
                    || EventEnd.ID == (int)LteURLCheckMsg.VideoFinish_FDD
                    || EventEnd.ID == (int)LteURLCheckMsg.DownSuccessID_FDD)
                {
                    IsFail = "成功";
                }
                else
                {
                    IsFail = "失败";
                }
                Bytes = (float)int.Parse(this.EventEnd["Value1"].ToString());
            }
        }

        private string GetReason(int Value)
        {
            switch (Value)
            {
                case 0:
                    return "s_APPNotDefined";
                case 1:
                    return "S_APPServiceRequest";
                case 2:
                    return "S_APPServiceConfirm";
                case 3:
                    return "S_APPServiceConnect";
                case 4:
                    return "S_APPServiceSuccess";
                case 5:
                    return "S_APPServiceReject";
                case 6:
                    return "S_APPServiceTimeout";
                case 7:
                    return "S_APPServiceFailure";
                case 8:
                    return "S_APPServiceDrop";
                default:
                    return "S_APPNone";
            }
        }
    }

    class LteURLRegion
    {
        public LteURLRegion(string regionName, string gridName)
        {
            this.Bros = new List<LteURLBroURL>();
            this.Downs = new List<LteURLDowURL>();
            this.Videos = new List<LteURLVideoURL>();
            this.RegionName = regionName;
            this.GridName = gridName;
        }

        public string RegionName
        {
            get;
            set;
        }

        public string GridName
        {
            get;
            set;
        }

        public int SN
        {
            get;
            set;
        }

        public List<LteURLBroURL> Bros
        {
            get;
            set;
        }

        public List<LteURLDowURL> Downs
        {
            get;
            set;
        }

        public List<LteURLVideoURL> Videos
        {
            get;
            set;
        }

        public int BroCount
        {
            get { return Bros.Count; }
        }

        public int DownCount
        {
            get { return Downs.Count; }
        }

        public int VideoCount
        {
            get { return Videos.Count; }
        }
    }
}
