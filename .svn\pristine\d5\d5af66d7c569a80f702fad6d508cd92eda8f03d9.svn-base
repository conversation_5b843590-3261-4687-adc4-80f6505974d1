﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.MTGis;
using DevExpress.XtraEditors;
using MasterCom.RAMS.Func.SystemSetting;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class DIYNoCoverRoadByRegion_TDScan : DIYNoCoverRoadByRegion_GScan
    {
        private static DIYNoCoverRoadByRegion_TDScan intance = null;
        public new static DIYNoCoverRoadByRegion_TDScan GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new DIYNoCoverRoadByRegion_TDScan();
                    }
                }
            }
            return intance;
        }

        protected DIYNoCoverRoadByRegion_TDScan()
            : base()
        {
            FilterSampleByRegion = false;
            IncludeEvent = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.TD_SCAN);
            carrierID = CarrierType.ChinaMobile;
        }

        public override string Name
        {
            get { return "弱覆盖路段分析_TD扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 16000, 16001, this.Name);//////
        }

        protected override void fireSetDefaultMapSerialTheme()
        {
            MainModel.FireSetDefaultMapSerialTheme("TDSCAN_PCCPCH_RSCP");
        }

        protected override void analyseFiles()
        {
            try
            {
                List<FileInfo> files = new List<FileInfo>();

                foreach (FileInfo fileInfo in MainModel.FileInfos)
                {
                    if (ServiceTypes.Count > 0 && !ServiceTypes.Contains((ServiceType)fileInfo.ServiceType))
                    {
                        continue;
                    }
                    files.Add(fileInfo);
                }
                clearDataBeforeAnalyseFiles();

                int iloop = 0;
                foreach (FileInfo fileInfo in files)
                {
                    Condition.FileInfos.Clear();
                    Condition.FileInfos.Add(fileInfo);
                    MainModel.IsFileReplayByCompareMode = Condition.isCompareMode;
                    iloop++;
                    WaitBox.Text = "正在分析文件(" + (iloop) + "/" + files.Count + ")...";
                    replay();
                    if (WaitBox.CancelRequest)
                    {
                        break;
                    }
                }
                MainModel.ClearDTData();
                getResultsAfterQuery();
            }
            finally
            {
                System.Threading.Thread.Sleep(20);
                WaitBox.Close();
            }
        }

        private LTEScanNoCoverRoadSetDlg setForm = null;
        protected override bool getCondition()
        {
            if (MainModel.IsBackground || MainModel.QueryFromBackground)
            {
                return true;
            }
            if (setForm == null)
            {
                setForm = new LTEScanNoCoverRoadSetDlg();
            }
            if (setForm.ShowDialog() == DialogResult.OK)
            {
                int rxLev;
                int distance;
                setForm.GetFilterCondition(out rxLev, out distance);
                rxLevThreshold = rxLev;
                distanceLast = distance;
                return true;
            }
            return false;
        }

        protected override bool validTestPoint(TestPoint tp)
        {
            if (tp is ScanTestPoint_TD)
            {
                float? rxLev = (float?)tp["TDS_PCCPCH_RSCP", 0];
                if (rxLev <= rxLevThreshold)
                {
                    return true;
                }
            }
            return false;
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.TD扫频; }
        }
        #endregion
    }

    public class DIYNoCoverRoadByRegion_WCDMAScan : DIYNoCoverRoadByRegion_GScan
    {
        private static DIYNoCoverRoadByRegion_WCDMAScan intance = null;
        public new static DIYNoCoverRoadByRegion_WCDMAScan GetInstance()
        {
            if (intance == null)
            {
                lock (lockObj)
                {
                    if (intance == null)
                    {
                        intance = new DIYNoCoverRoadByRegion_WCDMAScan();
                    }
                }
            }
            return intance;
        }

        protected DIYNoCoverRoadByRegion_WCDMAScan()
            : base()
        {
            FilterSampleByRegion = false;
            IncludeEvent = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.WCDMA_SCAN);
            carrierID = CarrierType.ChinaUnicom;
        }

        public override string Name
        {
            get { return "弱覆盖路段分析_WCDMA扫频"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 32000, 32001, this.Name);//////
        }

        protected override void fireSetDefaultMapSerialTheme()
        {
            MainModel.FireSetDefaultMapSerialTheme("WS_CPICHTotalRSCP");
        }

        protected override bool validTestPoint(TestPoint tp)
        {
            if (tp is ScanTestPoint_W)
            {
                float? rxLev = (float?)tp["WS_CPICHTotalRSCP", 0];
                if (rxLev <= rxLevThreshold)
                {
                    return true;
                }
            }
            return false;
        }

        #region Background
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.WCDMA扫频; }
        }
        #endregion
    }
}
