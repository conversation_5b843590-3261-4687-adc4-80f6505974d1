﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using BrightIdeasSoftware;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.Util;

namespace  MasterCom.RAMS.ZTFunc.VoLTEAbnormalEvt
{
    public partial class AbnormalEvtListForm : MinCloseForm
    {
        public AbnormalEvtListForm()
            : base()
        {
            InitializeComponent();
            init();
        }

        private void init()
        {
            this.olvColumnIndex.AspectGetter = delegate(object row)
            {
                List<Event> eventList = objectListView.Objects as List<Event>;
                return eventList.IndexOf(row as Event) + 1;
            };
            this.olvColumnName.AspectGetter = delegate(object row)
            {
                Event e = row as Event;
                return e.Name;
            };
            this.olvColumnDateTime.AspectGetter = delegate(object row)
            {
                Event e = row as Event;
                return e.DateTime;
            };
            this.olvColumnLongitude.AspectGetter = delegate(object row)
            {
                Event e = row as Event;
                return e.Longitude;
            };
            this.olvColumnLatitude.AspectGetter = delegate(object row)
            {
                Event e = row as Event;
                return e.Latitude;
            };
            this.olvColumnCellName.AspectGetter = delegate(object row)
            {
                Event e = row as Event;
                return e.CellNameSrc;
            };
            this.olvColumnTAC.AspectGetter = delegate(object row)
            {
                Event e = row as Event;
                return e["LAC"];
            };
            this.olvColumnECI.AspectGetter = delegate(object row)
            {
                Event e = row as Event;
                return e["CI"];
            };
            this.colCellCode.AspectGetter = delegate(object row )
            {
                Event e = row as Event;
                return e.CellCodeSrc;
            };
            this.olvColumnRoadName.AspectGetter = delegate(object row)
            {
                Event e = row as Event;
                return e.RoadPlaceDesc;
            };
            this.olvColumnFileName.AspectGetter = delegate(object row)
            {
                Event e = row as Event;
                return e.FileName;
            };
        }

        private List<Event> events = null;
        public void FillData()
        {
            events = new List<Event>();
            foreach (DTFileDataManager fileMng in MainModel.DTDataManager.FileDataManagers)
            {
                events.AddRange(fileMng.Events);
            }
            this.objectListView.ClearObjects();
            this.objectListView.SetObjects(events);
        }

        private void miExportXlsx_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(this.objectListView);
        }

        private void objectListView_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            foreach (Event et in MainModel.SelectedEvents)
            {
                et.Selected = false;
            }
            MainModel.SelectedEvents.Clear();
            OlvListViewHitTestInfo info = objectListView.OlvHitTest(e.X, e.Y);
            Event evt = info.RowObject as Event;
            if (evt != null)
            {
                MainModel.SelectedEvents.Add(evt);
                evt.Selected = true;
                MainModel.MainForm.GetMapForm().GoToView(evt.Longitude, evt.Latitude, 6000);
            }
        }

    }
}
