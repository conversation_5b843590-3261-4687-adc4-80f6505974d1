﻿using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    /// <summary>
    /// Http浏览统计单元
    /// </summary>
    public class NRHttpPageAnaItem : NRMobileServiceInfoBase
    {
        public NRHttpPageAnaItem(string fileName)
        {
            FileName = fileName;
            TpsList = new List<TestPoint>();
        }

        public double PagingTime { get; set; }
        public string URL { get; set; }
        public string IsFailed { get; set; }
    }

    public class NRHttpPageCondion
    {
        /// <summary>
        /// Http浏览时间上限
        /// </summary>
        public int GreatPageTime { get; set; } = 5;
        /// <summary>
        /// Http浏览时间下限
        /// </summary>
        public int LessPageTime { get; set; }
        /// <summary>
        /// 不设上限
        /// </summary>
        public bool IsNoGreat { get; set; } = false;
        /// <summary>
        /// HTTP浏览发起前几秒
        /// </summary>
        public int PreLoadTime { get; set; }
    }
}
