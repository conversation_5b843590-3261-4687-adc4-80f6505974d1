﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Net;
using System.Windows.Forms;
using MasterCom.RAMS.Model;

using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.ZTFunc;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTDiyCellUpdateByRegion : DIYAnalyseFilesOneByOneByRegion
    {
        protected int weakCoverRxlev = -90;
        protected int secondAna = 4;
        readonly List<int> eventIDList = new List<int>();
        public ZTDiyCellUpdateByRegion(MainModel mainModel)
            : base(mainModel)
        {
            FilterSampleByRegion = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.TDSCDMA_VOICE);
            ServiceTypes.Add(ServiceType.TDSCDMA_DATA);
            eventIDList.Clear();
            eventIDList.Add(177);
        }

        public override string Name
        {
            get { return "小区更新(按区域)"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 13000, 13037, this.Name);//////
        }

        protected override void query()
        {
            if (!getCondition())
            {
                return;
            }
            getReadyBeforeQuery();
            IsShowFileInfoForm = false;
            MainModel.IsDrawEventResult = false;
            MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = false;

            DIYEventByRegion queryEvent = new DIYEventByRegion(MainModel);
            queryEvent.showEventChooser = false;
            queryEvent.IsQueryAllEvents = false;
            queryEvent.SetIsAddEventToDTDataManager(false);
            condition.EventIDs = eventIDList;
            queryEvent.SetQueryCondition(Condition);
            queryEvent.SetIsAddEventToDTDataManager(true);
            queryEvent.showForm = true;
            queryEvent.Query();
            //return; // 有个bug暂无法解决，改为显示事件列表窗口
            //bool drawServer = MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer;
            //condition.EventIDs = new List<int>();
            //Dictionary<int, FileInfo> filesDic = new Dictionary<int, FileInfo>();
            //foreach (Event e in queryEvent.Events)
            //{
            //    if (!filesDic.ContainsKey(e.FileID))
            //    {
            //        FileInfo file = new FileInfo();
            //        file.ID = e.FileID;
            //        file.Name = e.FileName;
            //        file.ServiceType = e.ServiceType;
            //        file.LogTable = e.LogTable;
            //        file.ProjectID = e.ProjectType;
            //        filesDic[file.ID] = file;
            //    }
            //}
            //MainModel.FileInfos = new List<FileInfo>(filesDic.Values);
            //WaitBox.CanCancel = true;
            //WaitBox.Show("开始分析文件...", analyseFiles);
            //MainModel.ClearDTData();
            //MainModel.MainForm.GetMapForm().GetCellLayer().DrawServer = drawServer;
            //getResultsAfterQuery();
            //MainModel.FireDTDataChanged(this);
            //fireShowForm();
        }

        protected override void fireShowForm()
        {
            object obj = MainModel.GetInstance().GetObjectFromBlackboard(typeof(CellUpdateEventForm).FullName);
            CellUpdateEventForm cellUpdateEventForm = obj == null ? null : obj as CellUpdateEventForm;
            if (cellUpdateEventForm == null || cellUpdateEventForm.IsDisposed)
            {
                cellUpdateEventForm = new CellUpdateEventForm(MainModel);
            }
            cellUpdateEventForm.FillData(weakCoverRxlev);
            if (!cellUpdateEventForm.Visible)
            {
                cellUpdateEventForm.Show(MainModel.MainForm);
            }
        }

        CellUpdateSettingDlg conditionDlg = null;
        protected override bool getCondition()
        {
            if (conditionDlg == null)
            {
                conditionDlg = new CellUpdateSettingDlg();
            }
            return true; // 有个bug暂无法解决，改为显示事件列表窗口
            //if (conditionDlg.ShowDialog() == DialogResult.OK)
            //{
            //    conditionDlg.GetCondition(ref weakCoverRxlev, ref secondAna);
            //    return true;
            //}
            //return false;
        }

        protected override void clearDataBeforeAnalyseFiles()
        {
            MainModel.CellUpdateEvents.Clear();
        }

        private int index = -1;
        protected override void doStatWithQuery()
        {
            try
            {
                foreach (DTFileDataManager fileDataManager in MainModel.DTDataManager.FileDataManagers)
                {
                    List<TestPoint> testPointList = fileDataManager.TestPoints;
                    List<Event> eventList = fileDataManager.Events;
                    foreach (Event e in eventList)
                    {
                        if (!EventChecked(e, testPointList))
                        {
                            continue;
                        }

                        WeakCoverEvent wcEvent = setWeakCoverEventParameters(e, testPointList);
                        if (wcEvent != null)
                        {
                            MainModel.CellUpdateEvents.Add(wcEvent);
                        }
                    }
                }
            }
            catch
            {
                //continue
            }
        }

        /// <summary>
        /// Event有效验证
        /// </summary>
        /// <param name="e"></param>
        /// <returns></returns>
        private bool EventChecked(Event e, List<TestPoint> testPointList)
        {
            bool isOK = true;

            if (!eventIDList.Contains(e.ID))
            {
                isOK = false;
            }
            index = -1;
            TestPoint tp;
            int tpCount = testPointList.Count;
            for (int i = 0; i < tpCount; i++)
            {
                tp = testPointList[i];
                if (tp.SN > e.SN)
                {
                    index = i - 1;
                    break;
                }
                if (tp.SN == e.SN)
                {
                    index = i;
                    break;
                }
            }
            if (index == -1)
            {
                isOK = false;
            }

            return isOK;
        }

        /// <summary>
        /// 计数 WeakCoverEvent 各参数
        /// </summary>
        /// <param name="e"></param>
        /// <param name="testPointList"></param>
        private WeakCoverEvent setWeakCoverEventParameters(Event e, List<TestPoint> testPointList)
        {
            try
            {
                WeakCoverEventParameter param = new WeakCoverEventParameter();
                int iLoop = 0;
                TestPoint tp;
                while (true)
                {
                    if (index - iLoop >= 0)
                    {
                        tp = testPointList[index - iLoop];
                        if ((tp.Time + secondAna) * 1000L + tp.Millisecond < e.Time * 1000L + e.Millisecond)
                        {
                            break;
                        }

                        setParamInfo(param, tp);
                    }
                    else
                    {
                        break;
                    }
                    iLoop++;
                }

                WeakCoverEvent wcEvent = new WeakCoverEvent(e);
                wcEvent.rxLev = getValidValue(param.rxLevSum, param.rxLevCount, 0);
                wcEvent.Pccpch_C2i = getValidValue(param.pccpch_C2i_Sum, param.rxLevCount, 0);
                wcEvent.Dpch_Rscp = getValidValue(param.dpch_Rscp_Sum, param.rxLevCount, 0);
                wcEvent.Dpch_C2i = getValidValue(param.dpch_C2i_Sum, param.rxLevCount, 0);
                wcEvent.Dpch_Iscp = getValidValue(param.dpch_Iscp_Sum, param.rxLevCount, 0);
                wcEvent.Bler = getValidValue(param.bler_Sum, param.blerCount, 0);
                wcEvent.TxPower = getValidValue(param.txPower_Sum, param.txPowerCount, 255);
                return wcEvent;
            }
            catch (Exception)
            {
                return null;
            }
        }

        private static void setParamInfo(WeakCoverEventParameter param, TestPoint tp)
        {
            param.rxLev = (float?)(tp["TD_PCCPCH_RSCP"]);

            param.pccpch_C2i = (float?)(int?)(tp["TD_PCCPCH_C2I"]);
            param.dpch_Rscp = (float?)(tp["TD_DPCH_RSCP"]);
            param.dpch_C2i = (float?)(int?)(tp["TD_DPCH_C2I"]);
            param.dpch_Iscp = (float?)(tp["TD_DPCH_ISCP"]);
            param.bler = (float?)(int?)(tp["TD_BLER"]);
            param.txPower = (int?)tp["TD_TxPower"];

            if (param.bler >= 0 && param.bler <= 100)
            {
                param.bler_Sum += Convert.ToDouble(param.bler);
                param.blerCount++;
            }

            if (param.txPower != null && param.txPower != 255)
            {
                param.txPower_Sum += Convert.ToDouble(param.txPower);
                ++param.txPowerCount;
            }
            if ((param.rxLev != null && param.rxLev >= -120 && param.rxLev <= -10)
                && (param.pccpch_C2i != null && param.pccpch_C2i >= -20 && param.pccpch_C2i <= 25)
                && (param.dpch_C2i != null && param.dpch_C2i >= -30 && param.dpch_C2i <= 40))
            {
                param.rxLevSum += Convert.ToDouble(param.rxLev);

                param.pccpch_C2i_Sum += Convert.ToDouble(param.pccpch_C2i);
                param.dpch_Rscp_Sum += Convert.ToDouble(param.dpch_Rscp);
                param.dpch_C2i_Sum += Convert.ToDouble(param.dpch_C2i);
                param.dpch_Iscp_Sum += Convert.ToDouble(param.dpch_Iscp);

                param.rxLevCount++;
            }
        }

        private double getValidValue(double data, int dataCount, double defaultValue)
        {
            double res = defaultValue;
            if (dataCount > 0)
            {
                res = Math.Round(data / dataCount, 2);
            }
            return res;
        }

        protected override void getResultsAfterQuery()
        {
            //
        }

        class WeakCoverEventParameter
        {
            public float? rxLev { get; set; }
            public float? pccpch_C2i { get; set; }
            public float? dpch_Rscp { get; set; }
            public float? dpch_C2i { get; set; }
            public float? dpch_Iscp { get; set; }
            public float? bler { get; set; }
            public int? txPower { get; set; }

            public double rxLevSum { get; set; }
            public double pccpch_C2i_Sum { get; set; }
            public double dpch_Rscp_Sum { get; set; }
            public double dpch_C2i_Sum { get; set; }
            public double dpch_Iscp_Sum { get; set; }
            public double bler_Sum { get; set; }
            public double txPower_Sum { get; set; }

            public int rxLevCount { get; set; }
            public int blerCount { get; set; }
            public int txPowerCount { get; set; }
        }
    }
}
