﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class CsfbFailureSettingDlg : BaseDialog
    {
        public CsfbFailureSettingDlg()
        {
            InitializeComponent();
#if LT
            btnShowFlowChart.Visible = false;
            label4.Text = "RRC Connection Release信令没有携带G/W邻区信息，视为“邻区配置”问题。";
            groupBox7.Text = "WCDMA网络原因";
            label9.Text = "Total RSCP ＜";
            //label8.Text = "Ec_Io≥";
            label8.Text = "Ec_Io≤";
#endif
        }

        public CsfbFailureCondition Condition
        {
            get
            {
                CsfbFailureCondition cond = new CsfbFailureCondition();
                cond.Rsrp = (float)numRsrp.Value;
                cond.ForwardSec = (int)numRsrpSecond.Value;
                cond.Sinr = (float)numSinr.Value;
                cond.RxLev = (int)numRxLev.Value;
                cond.Qual = (int)numRxQual.Value;
                cond.Pccpch_Rscp = (int)numRscp.Value;
                cond.Bler = (int)numBler.Value;
                return cond;
            }
            set
            {
                if(value == null)
                {
                    return;
                }
                numRsrp.Value = (decimal)value.Rsrp;
                numRsrpSecond.Value = (decimal)value.ForwardSec;
                numSinr.Value = (decimal)value.Sinr;

                numRxLev.Value = value.RxLev;
                numRxQual.Value = value.Qual;
                numRscp.Value = value.Pccpch_Rscp;
                numBler.Value = value.Bler;
            }
        }

        private void btnShowFlowChart_Click(object sender, EventArgs e)
        {
            CsfbFailureCauseFlowchart chart =
                this.mainModel.GetObjectFromBlackboard(typeof(CsfbFailureCauseFlowchart)) as CsfbFailureCauseFlowchart;
            if(chart==null||chart.IsDisposed)
            {
                chart=new CsfbFailureCauseFlowchart();
            }
            if(!chart.Visible)
            {
                chart.Show(mainModel.MainForm);
            }
        }

    }
}
