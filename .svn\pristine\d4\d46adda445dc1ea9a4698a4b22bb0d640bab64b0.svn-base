﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class TDLTENBAnaListForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.treeListView = new BrightIdeasSoftware.TreeListView();
            this.olvColumnSN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnRegionName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnTotalCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnMissedCount = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnResult = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSiteName = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellTAC = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellECI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCelleNodeBID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellCellID = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellEARFCN = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellPCI = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnSiteType = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellLong = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnCellLat = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnDistance = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.olvColumnAngle = ((BrightIdeasSoftware.OLVColumn)(new BrightIdeasSoftware.OLVColumn()));
            this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExpandAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miCollapseAll = new System.Windows.Forms.ToolStripMenuItem();
            this.miExportToExcel = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).BeginInit();
            this.contextMenuStrip.SuspendLayout();
            this.SuspendLayout();
            // 
            // treeListView
            // 
            this.treeListView.AllColumns.Add(this.olvColumnSN);
            this.treeListView.AllColumns.Add(this.olvColumnRegionName);
            this.treeListView.AllColumns.Add(this.olvColumnCellName);
            this.treeListView.AllColumns.Add(this.olvColumnTotalCount);
            this.treeListView.AllColumns.Add(this.olvColumnMissedCount);
            this.treeListView.AllColumns.Add(this.olvColumnResult);
            this.treeListView.AllColumns.Add(this.olvColumnSiteName);
            this.treeListView.AllColumns.Add(this.olvColumnSiteType);
            this.treeListView.AllColumns.Add(this.olvColumnCellTAC);
            this.treeListView.AllColumns.Add(this.olvColumnCellECI);
            this.treeListView.AllColumns.Add(this.olvColumnCelleNodeBID);
            this.treeListView.AllColumns.Add(this.olvColumnCellCellID);
            this.treeListView.AllColumns.Add(this.olvColumnCellEARFCN);
            this.treeListView.AllColumns.Add(this.olvColumnCellPCI);
            this.treeListView.AllColumns.Add(this.olvColumnCellLong);
            this.treeListView.AllColumns.Add(this.olvColumnCellLat);
            this.treeListView.AllColumns.Add(this.olvColumnDistance);
            this.treeListView.AllColumns.Add(this.olvColumnAngle);
            this.treeListView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.olvColumnSN,
            this.olvColumnRegionName,
            this.olvColumnCellName,
            this.olvColumnTotalCount,
            this.olvColumnMissedCount,
            this.olvColumnResult,
            this.olvColumnSiteName,
            this.olvColumnSiteType,
            this.olvColumnCellTAC,
            this.olvColumnCellECI,
            this.olvColumnCelleNodeBID,
            this.olvColumnCellCellID,
            this.olvColumnCellEARFCN,
            this.olvColumnCellPCI,
            this.olvColumnCellLong,
            this.olvColumnCellLat,
            this.olvColumnDistance,
            this.olvColumnAngle});
            this.treeListView.ContextMenuStrip = this.contextMenuStrip;
            this.treeListView.Cursor = System.Windows.Forms.Cursors.Default;
            this.treeListView.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeListView.FullRowSelect = true;
            this.treeListView.GridLines = true;
            this.treeListView.Location = new System.Drawing.Point(0, 0);
            this.treeListView.MultiSelect = false;
            this.treeListView.Name = "treeListView";
            this.treeListView.OwnerDraw = true;
            this.treeListView.ShowGroups = false;
            this.treeListView.Size = new System.Drawing.Size(1138, 578);
            this.treeListView.TabIndex = 2;
            this.treeListView.UseCompatibleStateImageBehavior = false;
            this.treeListView.View = System.Windows.Forms.View.Details;
            this.treeListView.VirtualMode = true;
            this.treeListView.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.treeListView_MouseDoubleClick);
            // 
            // olvColumnSN
            // 
            this.olvColumnSN.HeaderFont = null;
            this.olvColumnSN.Text = "序号";
            this.olvColumnSN.Width = 90;
            // 
            // olvColumnRegionName
            // 
            this.olvColumnRegionName.HeaderFont = null;
            this.olvColumnRegionName.Text = "区域名称";
            this.olvColumnRegionName.Width = 80;
            // 
            // olvColumnCellName
            // 
            this.olvColumnCellName.HeaderFont = null;
            this.olvColumnCellName.Text = "小区名称";
            this.olvColumnCellName.Width = 100;
            // 
            // olvColumnTotalCount
            // 
            this.olvColumnTotalCount.HeaderFont = null;
            this.olvColumnTotalCount.Text = "符合条件小区数量";
            this.olvColumnTotalCount.Width = 120;
            // 
            // olvColumnMissedCount
            // 
            this.olvColumnMissedCount.HeaderFont = null;
            this.olvColumnMissedCount.Text = "漏配邻区数量";
            this.olvColumnMissedCount.Width = 90;
            // 
            // olvColumnResult
            // 
            this.olvColumnResult.HeaderFont = null;
            this.olvColumnResult.Text = "分析结果";
            // 
            // olvColumnSiteName
            // 
            this.olvColumnSiteName.HeaderFont = null;
            this.olvColumnSiteName.Text = "基站名称";
            this.olvColumnSiteName.Width = 100;
            // 
            // olvColumnCellTAC
            // 
            this.olvColumnCellTAC.HeaderFont = null;
            this.olvColumnCellTAC.Text = "TAC";
            // 
            // olvColumnCellECI
            // 
            this.olvColumnCellECI.HeaderFont = null;
            this.olvColumnCellECI.Text = "ECI";
            // 
            // olvColumnCelleNodeBID
            // 
            this.olvColumnCelleNodeBID.HeaderFont = null;
            this.olvColumnCelleNodeBID.Text = "eNodeBID";
            // 
            // olvColumnCellCellID
            // 
            this.olvColumnCellCellID.HeaderFont = null;
            this.olvColumnCellCellID.Text = "CellID";
            // 
            // olvColumnCellEARFCN
            // 
            this.olvColumnCellEARFCN.HeaderFont = null;
            this.olvColumnCellEARFCN.Text = "EARFCN";
            // 
            // olvColumnCellPCI
            // 
            this.olvColumnCellPCI.HeaderFont = null;
            this.olvColumnCellPCI.Text = "PCI";
            // 
            // olvColumnSiteType
            // 
            this.olvColumnSiteType.HeaderFont = null;
            this.olvColumnSiteType.Text = "基站属性";
            // 
            // olvColumnCellLong
            // 
            this.olvColumnCellLong.HeaderFont = null;
            this.olvColumnCellLong.Text = "经度";
            this.olvColumnCellLong.Width = 100;
            // 
            // olvColumnCellLat
            // 
            this.olvColumnCellLat.HeaderFont = null;
            this.olvColumnCellLat.Text = "纬度";
            this.olvColumnCellLat.Width = 100;
            // 
            // olvColumnDistance
            // 
            this.olvColumnDistance.HeaderFont = null;
            this.olvColumnDistance.Text = "距离(米)";
            // 
            // olvColumnAngle
            // 
            this.olvColumnAngle.HeaderFont = null;
            this.olvColumnAngle.Text = "夹角";
            // 
            // contextMenuStrip
            // 
            this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExpandAll,
            this.miCollapseAll,
            this.miExportToExcel});
            this.contextMenuStrip.Name = "contextMenuStrip";
            this.contextMenuStrip.Size = new System.Drawing.Size(130, 70);
            // 
            // miExpandAll
            // 
            this.miExpandAll.Name = "miExpandAll";
            this.miExpandAll.Size = new System.Drawing.Size(129, 22);
            this.miExpandAll.Text = "全部展开";
            this.miExpandAll.Click += new System.EventHandler(this.miExpandAll_Click);
            // 
            // miCollapseAll
            // 
            this.miCollapseAll.Name = "miCollapseAll";
            this.miCollapseAll.Size = new System.Drawing.Size(129, 22);
            this.miCollapseAll.Text = "全部折叠";
            this.miCollapseAll.Click += new System.EventHandler(this.miCollapseAll_Click);
            // 
            // miExportToExcel
            // 
            this.miExportToExcel.Name = "miExportToExcel";
            this.miExportToExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportToExcel.Text = "导出Excel";
            this.miExportToExcel.Click += new System.EventHandler(this.miExportToExcel_Click);
            // 
            // TDLTENBAnaListForm
            // 
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.Appearance.Options.UseImage = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1138, 578);
            this.Controls.Add(this.treeListView);
            this.Name = "TDLTENBAnaListForm";
            this.Text = "邻区漏配分析结果列表";
            ((System.ComponentModel.ISupportInitialize)(this.treeListView)).EndInit();
            this.contextMenuStrip.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private BrightIdeasSoftware.TreeListView treeListView;
        private BrightIdeasSoftware.OLVColumn olvColumnSN;
        private BrightIdeasSoftware.OLVColumn olvColumnRegionName;
        private BrightIdeasSoftware.OLVColumn olvColumnCellName;
        private BrightIdeasSoftware.OLVColumn olvColumnMissedCount;
        private BrightIdeasSoftware.OLVColumn olvColumnSiteName;
        private BrightIdeasSoftware.OLVColumn olvColumnCellTAC;
        private BrightIdeasSoftware.OLVColumn olvColumnCellECI;
        private BrightIdeasSoftware.OLVColumn olvColumnCelleNodeBID;
        private BrightIdeasSoftware.OLVColumn olvColumnCellCellID;
        private BrightIdeasSoftware.OLVColumn olvColumnCellEARFCN;
        private BrightIdeasSoftware.OLVColumn olvColumnCellPCI;
        private BrightIdeasSoftware.OLVColumn olvColumnCellLong;
        private BrightIdeasSoftware.OLVColumn olvColumnCellLat;
        private BrightIdeasSoftware.OLVColumn olvColumnTotalCount;
        private BrightIdeasSoftware.OLVColumn olvColumnDistance;
        private BrightIdeasSoftware.OLVColumn olvColumnAngle;
        private BrightIdeasSoftware.OLVColumn olvColumnResult;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip;
        private System.Windows.Forms.ToolStripMenuItem miExpandAll;
        private System.Windows.Forms.ToolStripMenuItem miCollapseAll;
        private System.Windows.Forms.ToolStripMenuItem miExportToExcel;
        private BrightIdeasSoftware.OLVColumn olvColumnSiteType;

    }
}