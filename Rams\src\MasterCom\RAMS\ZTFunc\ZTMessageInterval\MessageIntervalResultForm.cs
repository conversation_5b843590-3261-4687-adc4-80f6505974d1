﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class MessageIntervalResultForm : MinCloseForm
    {
        public MessageIntervalResultForm(MainModel mainModel) : base(mainModel)
        {
            InitializeComponent();
            miExportExcel.Click += MiExportExcel_Click;
        }

        public void FillData(object data)
        {
            gridControl1.DataSource = data;
            gridControl1.RefreshDataSource();
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            List<List<object>> datas = GridViewTransfer.Transfer(gridControl1);
            ExcelNPOIManager.ExportToExcel(datas);
        }
    }
}
