﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;

namespace MasterCom.RAMS.CQT
{
    public class CQTPointPicture : IComparable
    {
        public CQTPoint BelongPoint { get; set; }
        public int pictureID { get; set; }
        public int pointID { get; set; }
        public string pictureName { get; set; }
        public string Name
        {
            get { return pictureName; }
        }
        public string pictureDesc { get; set; }
        public int bottomFloor { get; set; }
        public int topFloor { get; set; }
        public double tlLongitude { get; set; }
        public double tlLatitude { get; set; }
        public double brLongitude { get; set; }
        public double brLatitude { get; set; }
        public int altitude { get; set; }
        public string aliasName { get; set; }
        public int pictureLength { get; set; }
        public int pictureWidth { get; set; }
        public Image pictureImage { get; set; }
        public Image Photo
        {
            get { return pictureImage; }
        }
        public double ratio { get; set; } = 420;

        public CQTPointPicture()
        {

        }

        public CQTPointPicture(CQTPointPicture cqtPicture)
        {
            this.BelongPoint = cqtPicture.BelongPoint;
            this.aliasName = cqtPicture.aliasName;
            this.altitude = cqtPicture.altitude;
            this.bottomFloor = cqtPicture.bottomFloor;
            this.brLatitude = cqtPicture.brLatitude;
            this.brLongitude = cqtPicture.brLongitude;
            this.pictureDesc = cqtPicture.pictureDesc;
            this.pictureID = cqtPicture.pictureID;
            this.pictureImage = cqtPicture.pictureImage.Clone() as Image;
            this.pictureLength = cqtPicture.pictureLength;
            this.pictureName = cqtPicture.pictureName;
            this.pictureWidth = cqtPicture.pictureWidth;
            this.pointID = cqtPicture.pointID;
            this.tlLatitude = cqtPicture.tlLatitude;
            this.tlLongitude = cqtPicture.tlLongitude;
            this.topFloor = cqtPicture.topFloor;
        }

        #region IComparable 成员

        public int CompareTo(object obj)
        {
            CQTPointPicture picture = obj as CQTPointPicture;
            return this.pictureID.CompareTo(picture.pictureID);
        }

        #endregion

        public int FindNextID()
        {
            List<int> idList = new List<int>();
            foreach (CQTPointPicture cp in this.BelongPoint.Pictures)
            {
                idList.Add(cp.pictureID);
            }
            idList.Sort();
            if (idList.Count == 0)
            {
                return 1;
            }
            if (idList.Count == idList[idList.Count - 1])
            {
                return idList[idList.Count - 1] + 1;
            }
            else
            {
                for (int i = 1; i < idList[idList.Count - 1]; i++)
                {
                    if (!idList.Contains(i))
                    {
                        return i;
                    }
                }
            }
            return idList[idList.Count - 1] + 1;
        }
    }
}
