﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using MasterCom.MTGis;

namespace MasterCom.RAMS.ZTFunc.FindNearestSiteByPoints
{
    public class PointNearestSiteLayer : LayerBase
    {
        public PointNearestSiteLayer()
            : base("最近基站")
        {

        }

        public List<PointNearestSiteInfo> Points { get; set; }
        public PointNearestSiteInfo SelPoint { get; set; }
        public override void Draw(Rectangle clientRect
           , Rectangle updateRect, Graphics graphics)
        {
            if (Points == null || Points.Count == 0)
            {
                return;
            }
            updateRect.Inflate(200, 200);
            DbRect dRect;
            gisAdapter.FromDisplay(updateRect, out dRect);

            foreach (PointNearestSiteInfo pt in Points)
            {
                if (dRect.IsPointInThisRect(pt.Point.Longitude, pt.Point.Latitude))
                {
                    drawPointLine(graphics, pt);
                }
            }
        }

        Pen redP { get; set; } = new Pen(Color.OrangeRed, 2);
        Pen pen2 { get; set; } = new Pen(Color.Blue, 1);
        Pen pen3 { get; set; } = new Pen(Color.Violet, 1);
        Pen pen4 { get; set; } = new Pen(Color.RoyalBlue, 1);
        Pen penLine { get; set; } = new Pen(Color.Lime, 2);
        Pen penSel { get; set; } = new Pen(Color.Red, 2);

        public void drawPointLine(Graphics graphics, PointNearestSiteInfo pt)
        {
            PointF p;
            gisAdapter.ToDisplay(new DbPoint(pt.Point.Longitude, pt.Point.Latitude), out p);

            Pen pen = redP;
            Pen penSite = pen2;
            Pen penL = penLine;
            if (pt == SelPoint)
            {
                pen = penSel;
                penSite = penSel;
                penL = penSel;
            }
            graphics.DrawLine(pen, p.X - 10, p.Y, p.X + 10, p.Y);//—
            graphics.DrawLine(pen, p.X, p.Y - 10, p.X, p.Y + 10);//|


            foreach (NearestSiteInfo ns in pt.NearestSites)
            {
                PointF sP;
                gisAdapter.ToDisplay(new DbPoint(ns.Site.Longitude, ns.Site.Latitude), out sP);
                graphics.DrawRectangle(penSite, sP.X - 10, sP.Y - 10, 20, 20);

                graphics.DrawLine(penL, p, sP);
            }
        }

    }
}
