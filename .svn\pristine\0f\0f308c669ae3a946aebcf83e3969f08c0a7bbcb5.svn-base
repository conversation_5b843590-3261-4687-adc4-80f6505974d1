﻿using MasterCom.RAMS.Model;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class NRLastWeakMosByTPInfo
    {
        public string FileName { get; set; }
        public TimePeriod CallTimePeriod { get; set; }
        public List<TestPoint> MosTimePeriodTPs { get; set; } = new List<TestPoint>();
        public Dictionary<TestPoint, double> WeakMosTPs { get; set; } = new Dictionary<TestPoint, double>();
        public TestPoint StartTP { get; set; }
        public double Distance { get; set; }

        public void Calculate()
        {
            TestPoint lastTP = null;
            foreach (var tp in MosTimePeriodTPs)
            {
                if (tp.Longitude != 0 && tp.Latitude != 0)
                {
                    if (lastTP != null)
                    {
                        Distance += tp.Distance2(lastTP);
                    }
                    lastTP = tp;
                }
            }
        }
    }

    public class NRLastWeakMosByTPResult
    {
        public string FileName { get; set; }
        public string DateTime { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public string StartCallTime { get; set; }
        public string EndCallTime { get; set; }
        public int TpNum { get; set; }
        public double Distance { get; set; }

        public CellResInfo NRInfo { get; set; } = new CellResInfo();
        public CellResInfo LTEInfo { get; set; } = new CellResInfo();
        public DataInfo MOSInfo { get; set; } = new DataInfo();

        public List<TestPoint> MosTimePeriodTPs { get; set; }
        public List<NRLastWeakMosByTPMosInfo> WeakMosTPs { get; set; } = new List<NRLastWeakMosByTPMosInfo>();

        public NRLastWeakMosByTPResult(NRLastWeakMosByTPInfo info)
        {
            MosTimePeriodTPs = info.MosTimePeriodTPs;
            StartCallTime = info.CallTimePeriod.BeginTime.ToString("yy-MM-dd HH:mm:ss.fff");
            EndCallTime = info.CallTimePeriod.EndTime.ToString("yy-MM-dd HH:mm:ss.fff");
            TpNum = info.WeakMosTPs.Count;
            Distance = Math.Round(info.Distance, 2);
            TestPoint startTP = info.StartTP;
            DateTime = startTP.DateTimeStringWithMillisecond;
            Longitude = startTP.Longitude;
            Latitude = startTP.Latitude;
        }

        public void Calculate()
        {
            NRInfo.Calculate();
            LTEInfo.Calculate();
            MOSInfo.Calculate();
        }

        public class CellResInfo
        {
            public DataInfo RsrpInfo { get; set; } = new DataInfo();
            public DataInfo SinrInfo { get; set; } = new DataInfo();

            public void Calculate()
            {
                RsrpInfo.Calculate();
                SinrInfo.Calculate();
            }
        }

        public class DataInfo
        {
            public double Sum { get; set; }
            public int Count { get; set; }
            public string Avg { get; private set; } = "";

            public void Add(double? data)
            {
                if (data != null)
                {
                    Count++;
                    Sum += (double)data;
                }
            }

            public void Calculate()
            {
                if (Count != 0)
                {
                    Avg = Math.Round(Sum / Count, 2).ToString();
                }
            }
        }

        public class NRLastWeakMosByTPMosInfo
        {
            public TestPoint WeakMOSTP { get; set; }
            public double MOS { get; set; }

            public MosCellResInfo NRInfo { get; set; } = new MosCellResInfo();
            public MosCellResInfo LTEInfo { get; set; } = new MosCellResInfo();
        }

        public class MosCellResInfo
        {
            public string Earfcn { get; set; }
            public string Pci { get; set; }
            public string Rsrp { get; set; }
            public string Sinr { get; set; }
        }
    }

    public class NRLastWeakMosByTPCondition
    {
        public double MOS { get; set; } = 3;
        public int TpNum { get; set; } = 2;
        public double Distance { get; set; } = 50;
        public bool IsACall { get; set; }
    }
}
