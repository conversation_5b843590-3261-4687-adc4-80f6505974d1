﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.Func
{
    /// <summary>
    /// 采样点小区参数窗体,gridview的第一列为固定的小区列，其他列为参数列
    /// </summary>
    public partial class TPCellParamInfoForm : ChildForm
    {
        TestPoint selectedTestPoint = null;
        private List<CellParamColumn> paramCols = new List<CellParamColumn>();
        public TPCellParamInfoForm()
        {
            InitializeComponent();
            this.LogItemSrc = new UserMng.LogInfoItem(2, 20000, 20033, this.Text);
        }

        private void rebuildColumns()
        {
            dataGridView.Columns.Clear();
            DataGridViewColumn columnCell = new DataGridViewColumn(new DataGridViewTextBoxCell());
            columnCell.HeaderText = "小区";
            int colIdx = 0;
            columnCell.DisplayIndex = colIdx++;
            dataGridView.Columns.Add(columnCell);
            dataGridView.Columns[0].Frozen = true;
            dataGridView.Columns[0].Width = 200;
            foreach (CellParamColumn col in paramCols)
            {
                DataGridViewColumn viewCol = new DataGridViewColumn(new DataGridViewTextBoxCell());
                viewCol.DisplayIndex = colIdx++;
                viewCol.Tag = col;
                viewCol.HeaderText = col.Alias;
                dataGridView.Columns.Add(viewCol);
            }
            for (int i = 0; i < 7; i++)
            {
                dataGridView.Rows.Add(new DataGridViewRow());
            }
        }

        public override void Init()
        {
            MainModel.DistrictChanged += districtChanged;
            MainModel.DTDataChanged += dtDataChanged;
            MainModel.SelectedTestPointsChanged += selectedTestPointsChanged;
            MainModel.SelectedEventsChanged += selectedEventsChanged;
            MainModel.SelectedMessageChanged += selectedMessageChanged;
            Disposed += disposed;
            dtDataChanged(null, null);
            selectedTestPointsChanged(null, null);
            rebuildColumns();
        }

        private int rowCount;
        private int columnCount;
        private List<int> columnWidths;
        public override Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["IsCompareForm"] = IsCompareForm;
                param["RowCount"] = dataGridView.Rows.Count;
                param["ColumnCount"] = dataGridView.Columns.Count;
                List<int> columnWidthsTmp = new List<int>();
                for (int i = 0; i < dataGridView.Columns.Count; i++)
                {
                    columnWidthsTmp.Add(dataGridView.Columns[i].Width);
                }
                param["ColumnWidths"] = columnWidthsTmp;
                List<string> cols = new List<string>();
                foreach (CellParamColumn col in paramCols)
                {
                    cols.Add(col.FullName);
                }
                param["ParamCols"] = cols;
                return param;
            }
            set
            {
                if (value == null)
                {
                    return;
                }
                rowCount = (int)value["RowCount"];
                columnCount = (int)value["ColumnCount"];
                if (value.ContainsKey("IsCompareForm"))
                {
                    IsCompareForm = (bool)value["IsCompareForm"];
                }
                if (value.ContainsKey("ColumnWidths"))
                {
                    columnWidths = new List<int>();
                    foreach (object width in (List<object>)value["ColumnWidths"])
                    {
                        columnWidths.Add((int)width);
                    }
                }
                CellParamCfgManager cfgMng = CellParamCfgManager.GetInstance();
                foreach (object paramColName in (List<object>)value["ParamCols"])
                {
                    CellParamColumn col = cfgMng.GetParamColByFullName(paramColName.ToString());
                    if (col == null)
                    {
                        MessageBox.Show("参数错误：[" + paramColName.ToString() + "]不存在");
                    }
                }
            }
        }

        Dictionary<int, object> rowCellDic = new Dictionary<int, object>();
        Dictionary<int, CellSign> rowCellSignDic = new Dictionary<int, CellSign>();
        /// <summary>
        /// 更新数据
        /// </summary>
        /// <param name="tp">需要更新显示参数的采样点</param>
        private void refreashData(TestPoint tp)
        {
            if (selectedTestPoint == tp)
            {
                return;
            }
            selectedTestPoint = tp;
            rowCellDic.Clear();
            rowCellSignDic.Clear();
            if (tp == null)
            {
                selectedTestPoint = null;
            }
            else if (tp is TDTestPointDetail || tp is TDTestPointSummary)
            {
                //tp.GetMainCell_TD(out tdCell, out cell);
            }
            else if (tp is TestPointScan)
            {
                //
            }
            else if (tp is ScanTestPoint_G)
            {
                //cell = testPoint.GetCell_GSMScan(0);
            }
            else if (tp is LTETestPointDetail)
            {
                //lteCell = testPoint.GetLTEMainCell();
            }
            else
            {
                dealGsmTp(tp);
            }
            if (rowCellSignDic.Count > 0)
            {
                queryParams();
            }
            dataGridView.Invalidate();
        }

        private void dealGsmTp(TestPoint tp)
        {
            object cell = tp.GetMainCell_GSM();//主服
            if (cell == null)
            {
                cell = tp["CI"];
            }
            prepareRowCellDic(0, tp.DateTime, cell);
            for (int i = 0; i < 6; i++)//邻区
            {
                cell = tp.GetNBCell_GSM(i);
                if (cell == null)
                {
                    object bcch = tp["N_BCCH", i];
                    object bsic = tp["N_BSIC", i];
                    if (bcch != null && bsic != null)
                    {
                        cell = bcch.ToString() + "_" + bsic.ToString();
                    }
                }
                prepareRowCellDic(i + 1, tp.DateTime, cell);
            }
        }

        /// <summary>
        /// 如果cell不为null，加到rowCell字典，再关联CellSign(有可能关联不到小区标记)
        /// </summary>
        /// <param name="rowIdx"></param>
        /// <param name="time"></param>
        /// <param name="cell"></param>
        private void prepareRowCellDic(int rowIdx, DateTime time, object cell)
        {
            if (cell == null)
            {
                return;
            }
            rowCellDic.Add(rowIdx, cell);
            CellSign sign = null;
            if (cell is Cell)
            {
                sign = CellSignManager.GetInstance().GetCellSign(time, cell as Cell);
            }
            else if (cell is TDCell)
            {
                //sign = CellSignManager.GetInstance().GetCellSign(time, (TDCell)cell);
            }
            if (sign != null)
            {
                rowCellSignDic.Add(rowIdx, sign);
            }
        }

        private void districtChanged(object sender, EventArgs e)
        {
            dtDataChanged(sender, e);
        }

        private void dtDataChanged(object sender, EventArgs e)
        {
            selectedTestPoint = null;
            dataGridView.Invalidate();
        }

        private void selectedTestPointsChanged(object sender, EventArgs e)
        {
            selectedTestPoint = null;
            if (MainModel.SelectedTestPoints.Count > 0)
            {
                refreashData(MainModel.SelectedTestPoints[0]);
            }
        }

        private void selectedEventsChanged(object sender, EventArgs e)
        {
            if (MainModel.SelectedEvents.Count > 0)
            {
                Event evt = MainModel.SelectedEvents[0];
                foreach (DTFileDataManager fdm in MainModel.DTDataManager.FileDataManagers)
                {
                    if (evt.FileID.Equals(fdm.FileID))
                    {
                        refreashData(fdm.FindNearestTestPointPreForword(evt));
                      
                        break;
                    }
                }
            }
        }

        private void selectedMessageChanged(object sender, EventArgs e)
        {
            if (MainModel.SelectedMessage != null)
            {
                MasterCom.RAMS.Model.Message msg = MainModel.SelectedMessage;
                foreach (DTFileDataManager fdm in MainModel.DTDataManager.FileDataManagers)
                {
                    if (msg.FileID.Equals(fdm.FileID))
                    {
                        refreashData(fdm.FindNearestTestPointPreForword(msg));
                        break;
                    }
                }
            }
        }

        private void disposed(object sender, EventArgs e)
        {
            MainModel.DistrictChanged -= districtChanged;
            MainModel.SelectedTestPointsChanged -= selectedTestPointsChanged;
            MainModel.SelectedEventsChanged -= selectedEventsChanged;
            MainModel.SelectedMessageChanged -= selectedMessageChanged;
        }

        Dictionary<CellSign, Dictionary<CellParamColumn, object>> cellParamValueDic = new Dictionary<CellSign, Dictionary<CellParamColumn, object>>();
        private void queryParams()
        {
            List<CellSign> signList = new List<CellSign>();
            signList.AddRange(rowCellSignDic.Values);
            QueryCellSignParam query = new QueryCellSignParam(MainModel, selectedTestPoint.DateTime, signList, paramCols);
            query.Query();
            cellParamValueDic = query.CellParamValueDic;
        }

        private void miSetting_Click(object sender, EventArgs e)
        {
            CellParamSettingDlg dlg = new CellParamSettingDlg(paramCols);
            if (dlg.ShowDialog() == DialogResult.OK)
            {
                paramCols = dlg.DisplayCellParamCols;
                queryParams();
                rebuildColumns();
            }
        }

        private void dataGridView_CellValueNeeded(object sender, DataGridViewCellValueEventArgs e)
        {
            if (selectedTestPoint == null)
            {
                return;
            }
            if (e.ColumnIndex == 0)//小区名称列
            {
                object cell = null;
                if (rowCellDic.TryGetValue(e.RowIndex, out cell))
                {
                    if (cell is Cell)
                    {
                        e.Value = (cell as Cell).Name;
                    }
                    else if (cell is TDCell)
                    {
                        e.Value = (cell as TDCell).Name;
                    }
                    else
                    {
                        e.Value = cell.ToString();
                    }
                }
            }
            else if (dataGridView.Columns[e.ColumnIndex].Tag is CellParamColumn)//参数列
            {
                CellParamColumn param = dataGridView.Columns[e.ColumnIndex].Tag as CellParamColumn;
                CellSign sign = null;
                if (rowCellSignDic.TryGetValue(e.RowIndex,out sign))
                {
                    object value = null;
                    cellParamValueDic[sign].TryGetValue(param, out value);
                    e.Value = value;
                }
            }

        }

    }
}
