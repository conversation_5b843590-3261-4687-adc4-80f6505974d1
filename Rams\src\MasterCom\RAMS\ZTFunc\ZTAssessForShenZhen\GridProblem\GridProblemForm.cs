﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using DevExpress.XtraGrid.Columns;
using MasterCom.Util;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class GridProblemForm : MinCloseForm
    {
        MapForm mapForm;
        int dataType = 0;
        public GridProblemForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            this.mapForm = mainModel.MainForm.GetMapForm();
            DisposeWhenClose = true;

            cbxStatus.Properties.Items.Clear();
            cbxStatus.Properties.Items.Add("全部");
            cbxStatus.Properties.Items.Add("已创建");
            cbxStatus.Properties.Items.Add("已关闭");
            cbxStatus.SelectedIndex = 1;

            cbxAreaOpt.Properties.Items.Clear();
            cbxAreaOpt.Properties.Items.Add("全部");
            cbxAreaOpt.Properties.Items.Add("宝安东");
            cbxAreaOpt.Properties.Items.Add("宝安南");
            cbxAreaOpt.Properties.Items.Add("宝安西");
            cbxAreaOpt.Properties.Items.Add("宝安中");
            cbxAreaOpt.Properties.Items.Add("福田");
            cbxAreaOpt.Properties.Items.Add("龙岗北");
            cbxAreaOpt.Properties.Items.Add("龙岗南");
            cbxAreaOpt.Properties.Items.Add("龙岗西");
            cbxAreaOpt.SelectedIndex = 0;

            cbxAreaAgent.Properties.Items.Clear();
            cbxAreaAgent.Properties.Items.Add("全部");
            cbxAreaAgent.Properties.Items.Add("长实");
            cbxAreaAgent.Properties.Items.Add("润迅");
            cbxAreaAgent.Properties.Items.Add("省电信");
            cbxAreaAgent.Properties.Items.Add("铁通");
            cbxAreaAgent.Properties.Items.Add("怡创");
            cbxAreaAgent.Properties.Items.Add("宜通");
            cbxAreaAgent.SelectedIndex = 0;
        }

        public void FillData(int dataType, int status)
        {
            this.dataType = dataType;
            cbxStatus.SelectedIndex = status;
            initGridControlGridKPI();
            refreshData();
        }

        private void initGridControlGridKPI()
        {
            gridViewGridKPI.Columns.Clear();
            if (dataType == 0)  //GSM
            {
                GridColumn gridColumnMonth = new GridColumn();
                gridColumnMonth.Caption = "月份";
                gridColumnMonth.FieldName = "Month";
                gridColumnMonth.Name = "gridColumnMonth";
                gridColumnMonth.Visible = true;
                gridColumnMonth.VisibleIndex = 0;
                gridColumnMonth.Width = 120;

                GridColumn gridColumnIsPoor = new GridColumn();
                gridColumnIsPoor.Caption = "是否质差";
                gridColumnIsPoor.FieldName = "IsPoorString";
                gridColumnIsPoor.Name = "gridColumnIsPoor";
                gridColumnIsPoor.Visible = true;
                gridColumnIsPoor.VisibleIndex = 1;

                GridColumn gridColumnRxQualTotal = new GridColumn();
                gridColumnRxQualTotal.Caption = "RxQual采样点数";
                gridColumnRxQualTotal.FieldName = "RxQualTotal";
                gridColumnRxQualTotal.Name = "gridColumnRxQualTotal";
                gridColumnRxQualTotal.Visible = true;
                gridColumnRxQualTotal.VisibleIndex = 2;
                gridColumnRxQualTotal.Width = 120;

                GridColumn gridColumnRxQual0_4Pct = new GridColumn();
                gridColumnRxQual0_4Pct.Caption = "RxQual(0-4)占比";
                gridColumnRxQual0_4Pct.FieldName = "RxQual0_4Pct";
                gridColumnRxQual0_4Pct.Name = "gridColumnRxQual0_4Pct";
                gridColumnRxQual0_4Pct.Visible = true;
                gridColumnRxQual0_4Pct.VisibleIndex = 3;
                gridColumnRxQual0_4Pct.Width = 120;

                GridColumn gridColumnRxQual5Pct = new GridColumn();
                gridColumnRxQual5Pct.Caption = "RxQual(5)占比";
                gridColumnRxQual5Pct.FieldName = "RxQual5Pct";
                gridColumnRxQual5Pct.Name = "gridColumnRxQual5Pct";
                gridColumnRxQual5Pct.Visible = true;
                gridColumnRxQual5Pct.VisibleIndex = 4;
                gridColumnRxQual5Pct.Width = 100;

                GridColumn gridColumnRxQual6_7Pct = new GridColumn();
                gridColumnRxQual6_7Pct.Caption = "RxQual(6-7)占比";
                gridColumnRxQual6_7Pct.FieldName = "RxQual6_7Pct";
                gridColumnRxQual6_7Pct.Name = "gridColumnRxQual6_7Pct";
                gridColumnRxQual6_7Pct.Visible = true;
                gridColumnRxQual6_7Pct.VisibleIndex = 5;
                gridColumnRxQual6_7Pct.Width = 120;

                GridColumn gridColumnMosTotal = new GridColumn();
                gridColumnMosTotal.Caption = "Mos采样点数";
                gridColumnMosTotal.FieldName = "MosTotal";
                gridColumnMosTotal.Name = "gridColumnMosTotal";
                gridColumnMosTotal.Visible = true;
                gridColumnMosTotal.VisibleIndex = 6;
                gridColumnMosTotal.Width = 100;

                GridColumn gridColumnMos2d8Pct = new GridColumn();
                gridColumnMos2d8Pct.Caption = "Mos2.8占比";
                gridColumnMos2d8Pct.FieldName = "Mos2d8Pct";
                gridColumnMos2d8Pct.Name = "gridColumnMos2d8Pct";
                gridColumnMos2d8Pct.Visible = true;
                gridColumnMos2d8Pct.VisibleIndex = 7;
                gridColumnMos2d8Pct.Width = 100;

                gridViewGridKPI.Columns.AddRange(new GridColumn[] { gridColumnMonth, gridColumnIsPoor, 
                    gridColumnRxQualTotal, gridColumnRxQual0_4Pct, gridColumnRxQual5Pct, gridColumnRxQual6_7Pct, 
                    gridColumnMosTotal, gridColumnMos2d8Pct});
            }
            else if (dataType == 1)  //TD
            {
                GridColumn gridColumnMonth = new GridColumn();
                gridColumnMonth.Caption = "月份";
                gridColumnMonth.FieldName = "Month";
                gridColumnMonth.Name = "gridColumnMonth";
                gridColumnMonth.Visible = true;
                gridColumnMonth.VisibleIndex = 0;
                gridColumnMonth.Width = 120;

                GridColumn gridColumnIsPoor = new GridColumn();
                gridColumnIsPoor.Caption = "是否质差";
                gridColumnIsPoor.FieldName = "IsPoorString";
                gridColumnIsPoor.Name = "gridColumnIsPoor";
                gridColumnIsPoor.Visible = true;
                gridColumnIsPoor.VisibleIndex = 1;

                GridColumn gridColumnBLERTotal = new GridColumn();
                gridColumnBLERTotal.Caption = "BLER采样点数";
                gridColumnBLERTotal.FieldName = "BLERTotal";
                gridColumnBLERTotal.Name = "gridColumnBLERTotal";
                gridColumnBLERTotal.Visible = true;
                gridColumnBLERTotal.VisibleIndex = 2;
                gridColumnBLERTotal.Width = 120;

                GridColumn gridColumnBLER5Pct = new GridColumn();
                gridColumnBLER5Pct.Caption = "BLER(5)占比";
                gridColumnBLER5Pct.FieldName = "BLER5Pct";
                gridColumnBLER5Pct.Name = "gridColumnBLER5Pct";
                gridColumnBLER5Pct.Visible = true;
                gridColumnBLER5Pct.VisibleIndex = 3;
                gridColumnBLER5Pct.Width = 120;

                GridColumn gridColumnBLERAvg = new GridColumn();
                gridColumnBLERAvg.Caption = "BLER均值";
                gridColumnBLERAvg.FieldName = "BLERAvg";
                gridColumnBLERAvg.Name = "gridColumnBLERAvg";
                gridColumnBLERAvg.Visible = true;
                gridColumnBLERAvg.VisibleIndex = 4;
                gridColumnBLERAvg.Width = 100;

                gridViewGridKPI.Columns.AddRange(new GridColumn[] { gridColumnMonth, gridColumnIsPoor, 
                    gridColumnBLERTotal, gridColumnBLER5Pct, gridColumnBLERAvg});
            }
        }

        private void refreshData()
        {
            MainModel.CurGridProblemDic.Clear();
            BindingSource source = new BindingSource();
            foreach (GridProblem grid in MainModel.AllGridProblemsDic.Values)
            {
                if (filterCondtion(grid))
                {
                    MainModel.CurGridProblemDic[grid.ID] = grid;
                }
            }
            source.DataSource = MainModel.CurGridProblemDic.Values;
            gridControlGrid.DataSource = source;
            gridControlGrid.RefreshDataSource();
        }

        private bool filterCondtion(GridProblem grid)
        {
            if (cbxStatus.SelectedIndex != 0)
            {
                if (cbxStatus.SelectedIndex == 1 && grid.Status != 0)
                {
                    return false;
                }
                if (cbxStatus.SelectedIndex == 2 && grid.Status != 1)
                {
                    return false;
                }
            }
            if (edtNetGridID.Text.Trim() != "" && grid.NetGridID.ToString() != edtNetGridID.Text.Trim())
            {
                return false;
            }
            if (edtAreaATUGridID.Text.Trim() != "" && grid.AreaATUGridID != edtAreaATUGridID.Text.Trim())
            {
                return false;
            }
            if (cbxAreaOpt.Text.Trim() != "" && cbxAreaOpt.Text.Trim() != "全部" && grid.AreaOpt != cbxAreaOpt.Text.Trim())
            {
                return false;
            }
            if (cbxAreaAgent.Text.Trim() != "" && cbxAreaAgent.Text.Trim() != "全部" && grid.AreaAgent != cbxAreaAgent.Text.Trim())
            {
                return false;
            }
            return true;
        }

        private void btnQuery_Click(object sender, EventArgs e)
        {
            refreshData();
        }

        private void gridViewGrid_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            if (gridViewGrid.SelectedRowsCount > 0)
            {
                GridProblem grid = gridViewGrid.GetRow(gridViewGrid.GetSelectedRows()[0]) as GridProblem;
                if (grid == null)
                {
                    return;
                }
                BindingSource source = new BindingSource();
                if (!grid.bQueryedKPI)
                {
                    DIYQueryGridProblemInfo.GetInstance().dataType = dataType;
                    DIYQueryGridProblemInfo.GetInstance().year = grid.BeginYear;
                    DIYQueryGridProblemInfo.GetInstance().batch = grid.BeginBatch;
                    DIYQueryGridProblemInfo.GetInstance().netGridID = grid.NetGridID;
                    DIYQueryGridProblemInfo.GetInstance().Query();
                    grid.GridKPIMonthList = DIYQueryGridProblemInfo.GetInstance().GridKPIList;
                    grid.bQueryedKPI = true;
                }
                if (grid.GridKPIMonthList[0] is GridKPIMonthGSM)
                {
                    List<GridKPIMonthGSM> gridList = new List<GridKPIMonthGSM>();
                    foreach (GridKPIMonth gridKPI in grid.GridKPIMonthList)
                    {
                        gridList.Add(gridKPI as GridKPIMonthGSM);
                    }
                    source.DataSource = gridList;
                }
                else if (grid.GridKPIMonthList[0] is GridKPIMonthTD)
                {
                    List<GridKPIMonthTD> gridList = new List<GridKPIMonthTD>();
                    foreach (GridKPIMonth gridKPI in grid.GridKPIMonthList)
                    {
                        gridList.Add(gridKPI as GridKPIMonthTD);
                    }
                    source.DataSource = gridList;
                }
                gridControlGridKPI.DataSource = source;
                gridControlGridKPI.RefreshDataSource();
            }
        }

        private void miExportToExcel_Click(object sender, EventArgs e)
        {
            if (gridControlGrid.Focused)
            {
                ExcelNPOIManager.ExportToExcel(gridViewGrid);
            }
            else if (gridControlGridKPI.Focused)
            {
                ExcelNPOIManager.ExportToExcel(gridViewGridKPI);
            }
            //return;
            //List<NPOIRow> rows = new List<NPOIRow>();
            //NPOIRow row = new NPOIRow();
            //row.AddCellValue("问题点编号");
            //row.AddCellValue("栅格编号");
            //row.AddCellValue("状态");
            //row.AddCellValue("权重");
            //row.AddCellValue("创建年份");
            //row.AddCellValue("创建轮次");
            //row.AddCellValue("关闭年份");
            //row.AddCellValue("关闭轮次");
            //row.AddCellValue("最后异常年份");
            //row.AddCellValue("最后异常轮次");
            //row.AddCellValue("验证正常次数");
            //row.AddCellValue("验证测试状态");
            //row.AddCellValue("栅格问题次数");
            //row.AddCellValue("中心经度");
            //row.AddCellValue("中心纬度");
            //row.AddCellValue("网格");
            //row.AddCellValue("片区");
            //row.AddCellValue("代维分区");
            ////if (dataType == 0)
            ////{
            ////    row.AddCellValue("年份");
            ////    row.AddCellValue("轮次");
            ////    row.AddCellValue("是否质差");
            ////    row.AddCellValue("RxQual采样点数");
            ////    row.AddCellValue("RxQual(0-4)占比");
            ////    row.AddCellValue("RxQual(5)占比");
            ////    row.AddCellValue("RxQual(6-7)占比");
            ////    row.AddCellValue("Mos采样点数");
            ////    row.AddCellValue("Mos2.8占比");
            ////}
            ////else
            ////{

            ////}
            //rows.Add(row);
            //foreach (GridProblem grid in MainModel.CurGridProblemDic.Values)
            //{
            //    row = new NPOIRow();
            //    row.AddCellValue(grid.ID);
            //    row.AddCellValue(grid.NetGridID);
            //    row.AddCellValue(grid.StatusString);
            //    row.AddCellValue(grid.Weight);
            //    row.AddCellValue(grid.CreatedYear);
            //    row.AddCellValue(grid.CreatedBatch);
            //    row.AddCellValue(grid.ClosedYear);
            //    row.AddCellValue(grid.ClosedBatch);
            //    row.AddCellValue(grid.LastAbnormalYear);
            //    row.AddCellValue(grid.LastAbnormalBatch);
            //    row.AddCellValue(grid.GoodDaysCount);
            //    row.AddCellValue(grid.ValidateStatusString);
            //    row.AddCellValue(grid.GridRepeatCount);
            //    row.AddCellValue(grid.MidLong);
            //    row.AddCellValue(grid.MidLat); ;
            //    row.AddCellValue(grid.AreaATUGridID);
            //    row.AddCellValue(grid.AreaOpt);
            //    row.AddCellValue(grid.AreaAgent);
            //    //foreach (GridKPIMonth gridKPI in grid.GridKPIMonthList)
            //    //{
            //    //    NPOIRow subRow = new NPOIRow();
            //    //    subRow.AddCellValue(gridKPI.Year);
            //    //    subRow.AddCellValue(gridKPI.Batch);
            //    //    subRow.AddCellValue(gridKPI.IsPoorString);
            //    //    if (dataType == 0)
            //    //    {
            //    //        GridKPIMonthGSM gridKPIGSM = gridKPI as GridKPIMonthGSM;
            //    //        subRow.AddCellValue(gridKPIGSM.RxQualTotal);
            //    //        subRow.AddCellValue(gridKPIGSM.RxQual0_4Pct);
            //    //        subRow.AddCellValue(gridKPIGSM.RxQual5Pct);
            //    //        subRow.AddCellValue(gridKPIGSM.RxQual6_7Pct);
            //    //        subRow.AddCellValue(gridKPIGSM.MosTotal);
            //    //        subRow.AddCellValue(gridKPIGSM.Mos2d8Pct);
            //    //    }
            //    //    else
            //    //    {

            //    //    }
            //    //    row.AddSubRow(subRow);
            //    //}
            //    rows.Add(row);
            //}
            //ExcelNPOIManager.ExportToExcel(rows);
        }

        private void btnSetting_Click(object sender, EventArgs e)
        {
            MultiCvrColorDlg mngDlg = new MultiCvrColorDlg();
            mngDlg.FixMinMax(0, 50);
            mngDlg.MakeRangeModeOnly();
            mngDlg.FillColorRanges(MainModel.GridProblemColorRanges);
            if (DialogResult.OK == mngDlg.ShowDialog(this))
            {
                MapForm mf = MainModel.MainForm.GetMapForm();
                if (mf != null)
                {
                    MainModel.GridProblemColorRanges = mngDlg.ColorRanges;
                    mf.gridProblemRanges.GridProblemColorRanges = mngDlg.ColorRanges;
                    mf.GetGridProblemLayer().Invalidate();
                }
                MainModel.RefreshLegend();
            }
        }

        private void gridViewGrid_DoubleClick(object sender, EventArgs e)
        {
            if (gridViewGrid.SelectedRowsCount > 0)
            {
                GridProblem grid = gridViewGrid.GetRow(gridViewGrid.GetSelectedRows()[0]) as GridProblem;
                MainModel.CurGridProblem = grid;
                MapForm mf = MainModel.MainForm.GetMapForm();
                if (mf != null)
                {
                    mf.GoToView(grid.MidLong, grid.MidLat);
                }
            }
        }

        public void SelectGrid(GridProblem gridSel)
        {
            for (int i = 0; i < gridViewGrid.RowCount; i++)
            {
                object row = gridViewGrid.GetRow(i);
                if (row is GridProblem)
                {
                    GridProblem grid = row as GridProblem;
                    if (grid.Equals(gridSel))
                    {
                        gridViewGrid.SelectRow(i);
                        gridViewGrid.FocusedRowHandle = i;
                        break;
                    }
                }
            }
        }

    }
}
