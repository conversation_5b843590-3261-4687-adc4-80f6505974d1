﻿namespace MasterCom.RAMS.Net
{
    partial class MapRenderBaseForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.label7 = new System.Windows.Forms.Label();
            this.cmbBoxStyle = new System.Windows.Forms.ComboBox();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.label5 = new System.Windows.Forms.Label();
            this.btnOK = new System.Windows.Forms.Button();
            this.label4 = new System.Windows.Forms.Label();
            this.cqtRenderingFilterControl = new MasterCom.RAMS.Func.CQTRenderingFilterControl();
            this.dateTimePickerBeginDate = new System.Windows.Forms.DateTimePicker();
            this.label2 = new System.Windows.Forms.Label();
            this.dateTimePickerEndDate = new System.Windows.Forms.DateTimePicker();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.label3 = new System.Windows.Forms.Label();
            this.cmbBoxMode = new System.Windows.Forms.ComboBox();
            this.label1 = new System.Windows.Forms.Label();
            this.cmbBoxIndex = new System.Windows.Forms.ComboBox();
            this.panelControl2 = new DevExpress.XtraEditors.PanelControl();
            this.gridControlInfo = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.miExportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridViewInfo = new DevExpress.XtraGrid.Views.Grid.GridView();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).BeginInit();
            this.panelControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControlInfo)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridViewInfo)).BeginInit();
            this.SuspendLayout();
            // 
            // panelControl1
            // 
            this.panelControl1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.panelControl1.Controls.Add(this.groupControl3);
            this.panelControl1.Controls.Add(this.groupControl1);
            this.panelControl1.Controls.Add(this.groupControl2);
            this.panelControl1.Location = new System.Drawing.Point(0, 0);
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new System.Drawing.Size(1133, 100);
            this.panelControl1.TabIndex = 6;
            // 
            // groupControl3
            // 
            this.groupControl3.Controls.Add(this.label7);
            this.groupControl3.Controls.Add(this.cmbBoxStyle);
            this.groupControl3.Location = new System.Drawing.Point(1, 1);
            this.groupControl3.Name = "groupControl3";
            this.groupControl3.Size = new System.Drawing.Size(256, 100);
            this.groupControl3.TabIndex = 7;
            this.groupControl3.Text = "首选渲染条件";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(10, 39);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(55, 14);
            this.label7.TabIndex = 1;
            this.label7.Text = "渲染方式";
            // 
            // cmbBoxStyle
            // 
            this.cmbBoxStyle.FormattingEnabled = true;
            this.cmbBoxStyle.Items.AddRange(new object[] {
            "按采样点",
            "按天和楼层统计",
            "按天和楼层,TAC,ECI统计",
            "按楼宇天线统计"});
            this.cmbBoxStyle.Location = new System.Drawing.Point(78, 35);
            this.cmbBoxStyle.Name = "cmbBoxStyle";
            this.cmbBoxStyle.Size = new System.Drawing.Size(165, 22);
            this.cmbBoxStyle.TabIndex = 0;
            this.cmbBoxStyle.Text = "按采样点";
            this.cmbBoxStyle.SelectedIndexChanged += new System.EventHandler(this.cmbBoxStyle_SelectedIndexChanged);
            // 
            // groupControl1
            // 
            this.groupControl1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupControl1.Controls.Add(this.label5);
            this.groupControl1.Controls.Add(this.btnOK);
            this.groupControl1.Controls.Add(this.label4);
            this.groupControl1.Controls.Add(this.cqtRenderingFilterControl);
            this.groupControl1.Controls.Add(this.dateTimePickerBeginDate);
            this.groupControl1.Controls.Add(this.label2);
            this.groupControl1.Controls.Add(this.dateTimePickerEndDate);
            this.groupControl1.Location = new System.Drawing.Point(477, 1);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(656, 100);
            this.groupControl1.TabIndex = 6;
            this.groupControl1.Text = "查询条件";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(285, 39);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(55, 14);
            this.label5.TabIndex = 30;
            this.label5.Text = "结束时间";
            // 
            // btnOK
            // 
            this.btnOK.Location = new System.Drawing.Point(564, 51);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 5;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(10, 39);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(55, 14);
            this.label4.TabIndex = 29;
            this.label4.Text = "起始时间";
            // 
            // cqtRenderingFilterControl
            // 
            this.cqtRenderingFilterControl.Location = new System.Drawing.Point(82, 68);
            this.cqtRenderingFilterControl.Name = "cqtRenderingFilterControl";
            this.cqtRenderingFilterControl.Size = new System.Drawing.Size(461, 24);
            this.cqtRenderingFilterControl.TabIndex = 4;
            // 
            // dateTimePickerBeginDate
            // 
            this.dateTimePickerBeginDate.Anchor = System.Windows.Forms.AnchorStyles.Left;
            this.dateTimePickerBeginDate.Location = new System.Drawing.Point(82, 35);
            this.dateTimePickerBeginDate.Name = "dateTimePickerBeginDate";
            this.dateTimePickerBeginDate.Size = new System.Drawing.Size(186, 22);
            this.dateTimePickerBeginDate.TabIndex = 27;
            this.dateTimePickerBeginDate.ValueChanged += new System.EventHandler(this.dateTimePicker_ValueChanged);
            this.dateTimePickerBeginDate.Enter += new System.EventHandler(this.dateTimePickerBeginDate_Enter);
            this.dateTimePickerBeginDate.Leave += new System.EventHandler(this.dateTimePickerBeginDate_Leave);
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(10, 70);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(55, 14);
            this.label2.TabIndex = 3;
            this.label2.Text = "筛选条件";
            // 
            // dateTimePickerEndDate
            // 
            this.dateTimePickerEndDate.Anchor = System.Windows.Forms.AnchorStyles.Left;
            this.dateTimePickerEndDate.Location = new System.Drawing.Point(357, 35);
            this.dateTimePickerEndDate.Name = "dateTimePickerEndDate";
            this.dateTimePickerEndDate.Size = new System.Drawing.Size(186, 22);
            this.dateTimePickerEndDate.TabIndex = 28;
            this.dateTimePickerEndDate.ValueChanged += new System.EventHandler(this.dateTimePicker_ValueChanged);
            this.dateTimePickerEndDate.Enter += new System.EventHandler(this.dateTimePickerEndDate_Enter);
            this.dateTimePickerEndDate.Leave += new System.EventHandler(this.dateTimePickerEndDate_Leave);
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.label3);
            this.groupControl2.Controls.Add(this.cmbBoxMode);
            this.groupControl2.Controls.Add(this.label1);
            this.groupControl2.Controls.Add(this.cmbBoxIndex);
            this.groupControl2.Location = new System.Drawing.Point(257, 1);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(220, 100);
            this.groupControl2.TabIndex = 3;
            this.groupControl2.Text = "次选渲染条件";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(10, 70);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(55, 14);
            this.label3.TabIndex = 6;
            this.label3.Text = "渲染类别";
            // 
            // cmbBoxMode
            // 
            this.cmbBoxMode.FormattingEnabled = true;
            this.cmbBoxMode.Items.AddRange(new object[] {
            "按打点位置信息",
            "按蓝牙定位信息"});
            this.cmbBoxMode.Location = new System.Drawing.Point(78, 66);
            this.cmbBoxMode.Name = "cmbBoxMode";
            this.cmbBoxMode.Size = new System.Drawing.Size(130, 22);
            this.cmbBoxMode.TabIndex = 5;
            this.cmbBoxMode.Text = "按打点位置信息";
            this.cmbBoxMode.SelectedIndexChanged += new System.EventHandler(this.cmbBoxMode_SelectedIndexChanged);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(10, 39);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(55, 14);
            this.label1.TabIndex = 1;
            this.label1.Text = "渲染指标";
            // 
            // cmbBoxIndex
            // 
            this.cmbBoxIndex.FormattingEnabled = true;
            this.cmbBoxIndex.Items.AddRange(new object[] {
            "LTE_TDD:RSRP",
            "LTE_TDD:SINR",
            "LTE_TDD:RSSI"});
            this.cmbBoxIndex.Location = new System.Drawing.Point(78, 35);
            this.cmbBoxIndex.Name = "cmbBoxIndex";
            this.cmbBoxIndex.Size = new System.Drawing.Size(130, 22);
            this.cmbBoxIndex.TabIndex = 0;
            this.cmbBoxIndex.Text = "LTE_TDD:RSRP";
            this.cmbBoxIndex.SelectedIndexChanged += new System.EventHandler(this.cmbBoxIndex_SelectedIndexChanged);
            // 
            // panelControl2
            // 
            this.panelControl2.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.panelControl2.Controls.Add(this.gridControlInfo);
            this.panelControl2.Location = new System.Drawing.Point(0, 99);
            this.panelControl2.Name = "panelControl2";
            this.panelControl2.Size = new System.Drawing.Size(1133, 464);
            this.panelControl2.TabIndex = 7;
            // 
            // gridControlInfo
            // 
            this.gridControlInfo.ContextMenuStrip = this.contextMenuStrip1;
            this.gridControlInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControlInfo.Location = new System.Drawing.Point(2, 2);
            this.gridControlInfo.MainView = this.gridViewInfo;
            this.gridControlInfo.Name = "gridControlInfo";
            this.gridControlInfo.Size = new System.Drawing.Size(1129, 460);
            this.gridControlInfo.TabIndex = 2;
            this.gridControlInfo.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridViewInfo});
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.miExportExcel});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(130, 26);
            // 
            // miExportExcel
            // 
            this.miExportExcel.Name = "miExportExcel";
            this.miExportExcel.Size = new System.Drawing.Size(129, 22);
            this.miExportExcel.Text = "导出Excel";
            this.miExportExcel.Click += new System.EventHandler(this.miExportExcel_Click);
            // 
            // gridViewInfo
            // 
            this.gridViewInfo.GridControl = this.gridControlInfo;
            this.gridViewInfo.GroupPanelText = " ";
            this.gridViewInfo.Name = "gridViewInfo";
            this.gridViewInfo.OptionsBehavior.Editable = false;
            this.gridViewInfo.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridViewInfo.OptionsSelection.MultiSelect = true;
            this.gridViewInfo.OptionsView.ShowGroupPanel = false;
            this.gridViewInfo.SelectionChanged += new DevExpress.Data.SelectionChangedEventHandler(this.gridViewInfo_SelectionChanged);
            // 
            // MapRenderBaseForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1133, 563);
            this.Controls.Add(this.panelControl2);
            this.Controls.Add(this.panelControl1);
            this.Name = "MapRenderBaseForm";
            this.Text = "渲染结果";
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            this.groupControl3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.groupControl2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).EndInit();
            this.panelControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControlInfo)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridViewInfo)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.PanelControl panelControl2;
        private DevExpress.XtraGrid.GridControl gridControlInfo;
        private DevExpress.XtraGrid.Views.Grid.GridView gridViewInfo;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.ComboBox cmbBoxMode;
        private Func.CQTRenderingFilterControl cqtRenderingFilterControl;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.ComboBox cmbBoxIndex;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.DateTimePicker dateTimePickerEndDate;
        private System.Windows.Forms.DateTimePicker dateTimePickerBeginDate;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.ComboBox cmbBoxStyle;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem miExportExcel;
    }
}