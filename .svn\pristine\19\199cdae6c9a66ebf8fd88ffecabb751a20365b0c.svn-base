﻿using MasterCom.MTGis;
using System;
using System.Collections.Generic;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTRoadGridArchiveDataVerify
    {
        public int CityID { get; set; }
        public string Type { get; set; }
        public string DataSuffix { get; set; }
    }

    public class StatCellRoadInfo : IComparable<StatCellRoadInfo>
    {
        public int SN { get; set; }
        public int LAC { get; set; }
        public int CI { get; set; }
        public int RoadID { get; set; }
        public int AreaID { get; set; }
        public int Times { get; set; }
        public string CellName { get; set; }

        public string Token { get { return LAC + "_" + CI; } }

        public int CompareTo(StatCellRoadInfo other)
        {
            if (other == null)
            {
                return -1;
            }
            //倒序
            return other.Times.CompareTo(Times);
        }
    }

    /// <summary>
    /// 数据库-事件信息
    /// </summary>
    public class EventRoadInfo
    {
        public int RoadID { get; set; }
        public int AreaID { get; set; }
        public string EventType { get; set; }
        public int EventNum { get; set; }
    }

    /// <summary>
    /// 汇聚的事件信息
    /// </summary>
    public class EventConverageRes
    {
        public EventConverageRes()
        {
        }

        public EventConverageRes(EventRoadInfo info)
        {
            RoadID = info.RoadID;
            AreaID = info.AreaID;
        }

        public int RoadID { get; set; }
        public int AreaID { get; set; }
        public int TotalEventNum { get; private set; }

        public int NotConnected { get; set; }
        public int DroppedCalls { get; set; }
        public int WeakMos { get; set; }
        public int WeakRsrp { get; set; }
        public int WeakSinr { get; set; }
        public int RRCRebuild { get; set; }
        public int Downless { get; set; }

        public void ConverageEventSum(EventConverageRes res)
        {
            if (res != null)
            {
                TotalEventNum += res.TotalEventNum;
                NotConnected += res.NotConnected;
                DroppedCalls += res.DroppedCalls;
                WeakMos += res.WeakMos;
                WeakRsrp += res.WeakRsrp;
                WeakSinr += res.WeakSinr;
                RRCRebuild += res.RRCRebuild;
                Downless += res.Downless;
            }
        }

        public void ConverageEvent(EventRoadInfo info)
        {
            switch (info.EventType)
            {
                case "未接通":
                    NotConnected += info.EventNum;
                    break;
                case "掉话":
                    DroppedCalls += info.EventNum;
                    break;
                case "弱MOS":
                    WeakMos += info.EventNum;
                    break;
                case "弱覆盖":
                    WeakRsrp += info.EventNum;
                    break;
                case "质差":
                    WeakSinr += info.EventNum;
                    break;
                case "RRC重建":
                    RRCRebuild += info.EventNum;
                    break;
                case "低速率":
                    Downless += info.EventNum;
                    break;
                default:
                    break;
            }
        }

        public void SetTotalEventNum()
        {
            TotalEventNum = NotConnected + DroppedCalls + WeakMos + WeakRsrp + WeakSinr + RRCRebuild + Downless;
        }
    }

    /// <summary>
    /// 数据库-网络原因信息
    /// </summary>
    public class ReasonRoadInfo
    {
        public int RoadID { get; set; }
        public int AreaID { get; set; }
        public string Netreason { get; set; }
        public int NetreasonNum { get; set; }
    }

    public class ReasonConverageRes
    {
        public int RoadID { get; set; }
        public int AreaID { get; set; }

        /// <summary>
        /// 弱覆盖
        /// </summary>
        public int WeakCover { get; set; }
        /// <summary>
        /// 模三干扰
        /// </summary>
        public int ModRoad { get; set; }
        /// <summary>
        /// 同频干扰
        /// </summary>
        public int CoFreq { get; set; }
        /// <summary>
        /// 重叠覆盖
        /// </summary>
        public int MultiCoverage { get; set; }
        /// <summary>
        /// 过覆盖
        /// </summary>
        public int CoverLap { get; set; }
        /// <summary>
        /// 小区故障
        /// </summary>
        public int CellFailure { get; set; }
        /// <summary>
        /// 断站
        /// </summary>
        public int BtsInterruption { get; set; }
        /// <summary>
        /// 拆站
        /// </summary>
        public int BtsTearDown { get; set; }
        /// <summary>
        /// 容量
        /// </summary>
        public int LoadProblem { get; set; }

        public void ConverageReasonSum(ReasonConverageRes res)
        {
            if (res != null)
            {
                WeakCover += res.WeakCover;
                ModRoad += res.ModRoad;
                CoFreq += res.CoFreq;
                MultiCoverage += res.MultiCoverage;
                CoverLap += res.CoverLap;
                CellFailure += res.CellFailure;
                BtsInterruption += res.BtsInterruption;
                BtsTearDown += res.BtsTearDown;
                LoadProblem += res.LoadProblem;
            }
        }

        public void ConverageReason(ReasonRoadInfo info)
        {
            switch (info.Netreason)
            {
                case "弱覆盖":
                    WeakCover += info.NetreasonNum;
                    break;
                case "模三干扰":
                    ModRoad += info.NetreasonNum;
                    break;
                case "同频干扰":
                    CoFreq += info.NetreasonNum;
                    break;
                case "重叠覆盖":
                    MultiCoverage += info.NetreasonNum;
                    break;
                case "过覆盖":
                    CoverLap += info.NetreasonNum;
                    break;
                case "小区故障":
                    CellFailure += info.NetreasonNum;
                    break;
                case "断站":
                    BtsInterruption += info.NetreasonNum;
                    break;
                case "拆站":
                    BtsTearDown += info.NetreasonNum;
                    break;
                case "容量":
                    LoadProblem += info.NetreasonNum;
                    break;
                default:
                    break;
            }
        }
    }

    /// <summary>
    /// 道路详细数据
    /// </summary>
    public class ZTRoadGridArchiveRes
    {
        public int CityID { get; set; }
        public int AreaTypeID { get; set; }
        public string AreaTypeName { get; set; }
        public int RoadID { get; set; }
        public string RoadName { get; set; }
        public int AreaID { get; set; }
        public string AreaName { get; set; }

        public double TLLongitude { get; set; }
        public double TLLatitude { get; set; }
        public double BRLongitude { get; set; }
        public double BRLatitude { get; set; }
        public double CenterLongitude { get; set; }
        public double CenterLatitude { get; set; }
        public List<DbPoint> DbPointList { get; set; } = new List<DbPoint>();

        public string CompanyName { get; set; }
        public int CellCount { get { return CurStatCellRoadInfo.Count; } }

        public List<StatCellRoadInfo> CurStatCellRoadInfo { get; set; } = new List<StatCellRoadInfo>();
        public List<StatCellRoadInfo> HisStatCellRoadInfo { get; set; } = new List<StatCellRoadInfo>();
        public EventConverageRes CurEventRoadInfo { get; set; } = new EventConverageRes();
        public ReasonConverageRes CurRoadReasonInfo { get; set; } = new ReasonConverageRes();

        public void SetSN()
        {
            for (int i = 0; i < CurStatCellRoadInfo.Count; i++)
            {
                CurStatCellRoadInfo[i].SN = i + 1;
            }

            for (int i = 0; i < HisStatCellRoadInfo.Count; i++)
            {
                HisStatCellRoadInfo[i].SN = i + 1;
            }
        }

        /// <summary>
        /// 经纬度倍率(10000000.0)
        /// </summary>
        protected const double DRatio = 10000000;
        public List<DbPoint> BytesToPoints(byte[] buffer)
        {
            int length = buffer.Length / 8;
            List<DbPoint> pts = new List<DbPoint>();

            int offset = 0;
            for (int i = 0; i < length; i++)
            {
                int x = BitConverter.ToInt32(buffer, offset);
                offset += 4;
                int y = BitConverter.ToInt32(buffer, offset);
                offset += 4;

                pts.Add(new DbPoint(x / DRatio, y / DRatio));
            }
            return pts;
        }


        public bool Within(DbRect dRect)
        {
            if (CenterLongitude < dRect.x1 || CenterLongitude > dRect.x2 || CenterLatitude < dRect.y1 || CenterLatitude > dRect.y2)
            {
                return false;
            }
            return true;
        }
    }

    /// <summary>
    /// 道路汇总数据
    /// </summary>
    public class ZTRoadGridArchiveSumRes
    {
        public int RoadID { get; set; }
        public string RoadName { get; set; }
        public double RoadLength { get; set; }
        public string CompanyName { get; set; }
        public string AreaTypeName { get; set; }

        public string StartDate { get; set; }
        public string EndDate { get; set; }
        public string Round { get; set; }
        public int RoundCount { get; set; }

        public int CellCount { get { return StatCellRoadInfoDic.Count; } }
        public Dictionary<string, StatCellRoadInfo> StatCellRoadInfoDic { get; set; } = new Dictionary<string, StatCellRoadInfo>();

        public EventConverageRes EventRoadInfo { get; set; } = new EventConverageRes();
        public ReasonConverageRes RoadReasonInfo { get; set; } = new ReasonConverageRes();
    }
}
