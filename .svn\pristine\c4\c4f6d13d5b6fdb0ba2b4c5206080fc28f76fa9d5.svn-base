﻿namespace MasterCom.RAMS.ZTFunc
{
    partial class LteMgrsWeakRsrpSetting
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.numGridCount = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.numRsrpMax = new System.Windows.Forms.NumericUpDown();
            this.label1 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.cbxFreqType = new System.Windows.Forms.ComboBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            ((System.ComponentModel.ISupportInitialize)(this.numGridCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpMax)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // numGridCount
            // 
            this.numGridCount.Location = new System.Drawing.Point(287, 66);
            this.numGridCount.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numGridCount.Name = "numGridCount";
            this.numGridCount.Size = new System.Drawing.Size(120, 21);
            this.numGridCount.TabIndex = 9;
            this.numGridCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numGridCount.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(191, 69);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(89, 12);
            this.label3.TabIndex = 8;
            this.label3.Text = "连续栅格个数≥";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(407, 32);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(23, 12);
            this.label2.TabIndex = 7;
            this.label2.Text = "dBm";
            // 
            // numRsrpMax
            // 
            this.numRsrpMax.Location = new System.Drawing.Point(287, 29);
            this.numRsrpMax.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numRsrpMax.Minimum = new decimal(new int[] {
            1000,
            0,
            0,
            -2147483648});
            this.numRsrpMax.Name = "numRsrpMax";
            this.numRsrpMax.Size = new System.Drawing.Size(120, 21);
            this.numRsrpMax.TabIndex = 6;
            this.numRsrpMax.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.numRsrpMax.Value = new decimal(new int[] {
            95,
            0,
            0,
            -2147483648});
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(214, 33);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 5;
            this.label1.Text = "最强信号≤";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(220, 106);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(59, 12);
            this.label4.TabIndex = 11;
            this.label4.Text = "频段设置:";
            // 
            // cbxFreqType
            // 
            this.cbxFreqType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbxFreqType.FormattingEnabled = true;
            this.cbxFreqType.Location = new System.Drawing.Point(287, 102);
            this.cbxFreqType.Name = "cbxFreqType";
            this.cbxFreqType.Size = new System.Drawing.Size(143, 20);
            this.cbxFreqType.TabIndex = 10;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.numRsrpMax);
            this.groupBox1.Controls.Add(this.label4);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Controls.Add(this.cbxFreqType);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.numGridCount);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupBox1.Location = new System.Drawing.Point(0, 0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(620, 150);
            this.groupBox1.TabIndex = 12;
            this.groupBox1.TabStop = false;
            // 
            // LteMgrsWeakRsrpSetting
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.groupBox1);
            this.Name = "LteMgrsWeakRsrpSetting";
            ((System.ComponentModel.ISupportInitialize)(this.numGridCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRsrpMax)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.NumericUpDown numGridCount;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.NumericUpDown numRsrpMax;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.ComboBox cbxFreqType;
        private System.Windows.Forms.GroupBox groupBox1;
    }
}
