﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteStationSettingForm : BaseDialog
    {
        public LteStationSettingForm()
        {
            InitializeComponent();
            this.btnOK.Click += BtnOK_Click;
            this.btnCancel.Click += BtnCancel_Click;
            this.btnFolder.Click += BtnFolder_Click;
        }

        public void setCondition(LteStationSettingFormConfigModel_XJ condition)
        {
            if (condition == null)
            {
                return;
            }

            txtAccessRate.Value = Convert.ToDecimal(condition.AccessSuccessRate);
            txtAccessTestCount.Value = Convert.ToDecimal(condition.AccessTestCount);
            txtCSFBTestCount.Value = Convert.ToDecimal(condition.CSFBTestCount);
            txtCSFBSuccessRate.Value = Convert.ToDecimal(condition.CSFBSuccessRate);
            txtVolteTestCount.Value = Convert.ToDecimal(condition.VOLTETestCount);
            txtVolteSuccessRate.Value = Convert.ToDecimal(condition.VOLTESuccessRate);
            txtFTPDownSpeed.Value = Convert.ToDecimal(condition.FTPDownloadThroughput);
            txtFTPUpSpeed.Value = Convert.ToDecimal(condition.FTPUploadThroughput);
            txtSwitchCount.Value = Convert.ToDecimal(condition.SwitchCount);
            txtSwitchSuccessRate.Value = Convert.ToDecimal(condition.SwtichSuccessRate);
        }

        public LteStationAcceptCondition GetCondition()
        {
            LteStationAcceptCondition cond = new LteStationAcceptCondition();
            cond.IsByFile = radioByFile.Checked;
            cond.SaveFolder = txtFolder.Text;
            return cond;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = System.Windows.Forms.DialogResult.Cancel;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtFolder.Text))
            {
                MessageBox.Show("请选择导出文件保存目录!", this.Text,
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                DialogResult = System.Windows.Forms.DialogResult.None;
                return;
            }

            DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void BtnFolder_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog dlg = new FolderBrowserDialog();
            if (dlg.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                txtFolder.Text = dlg.SelectedPath;
            }
        }

        public LteStationSettingFormConfigModel_XJ getInfoData()
        {
            LteStationSettingFormConfigModel_XJ condition = new LteStationSettingFormConfigModel_XJ();
            condition.AccessSuccessRate = txtAccessRate.Value.ToString();
            condition.AccessTestCount = Convert.ToInt32(txtAccessTestCount.Value);
            condition.CSFBTestCount = Convert.ToInt32(txtCSFBTestCount.Value);
            condition.CSFBSuccessRate = txtCSFBSuccessRate.Value.ToString();
            condition.VOLTETestCount = Convert.ToInt32(txtVolteTestCount.Value);
            condition.VOLTESuccessRate = txtVolteSuccessRate.Value.ToString();
            condition.FTPDownloadThroughput = txtFTPDownSpeed.Value.ToString();
            condition.FTPUploadThroughput = txtFTPUpSpeed.Value.ToString();
            condition.SwitchCount = Convert.ToInt32(txtSwitchCount.Value);
            condition.SwtichSuccessRate = txtSwitchSuccessRate.Value.ToString();
            return condition;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            var cond = getInfoData();
            if (cond != null)
            {
                LteStationSettingFormConfig_XJ.Instance.SaveConfig(cond);
                DialogResult = DialogResult.OK;
            }
            else
            {
                MessageBox.Show("主库连接设置不能为空");
            }
        }
    }

    public class LteStationAcceptCondition
    {
        public bool IsByFile
        {
            get;
            set;
        }

        public string SaveFolder
        {
            get;
            set;
        }
    }
}
