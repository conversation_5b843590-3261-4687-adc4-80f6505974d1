﻿namespace MasterCom.RAMS.ZTFunc.ZTLteLastRoadScene
{
    partial class ConditionDlg
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.label6 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.numMultiCellNum = new System.Windows.Forms.NumericUpDown();
            this.numMultiCellDistance = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.label15 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.numMultiBand = new System.Windows.Forms.NumericUpDown();
            this.numMultiRsrp = new System.Windows.Forms.NumericUpDown();
            this.numMultiDistance = new System.Windows.Forms.NumericUpDown();
            this.numMultiLev = new System.Windows.Forms.NumericUpDown();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label7 = new System.Windows.Forms.Label();
            this.numWeakCvrRsrp = new System.Windows.Forms.NumericUpDown();
            this.numWeakCvrCellDis = new System.Windows.Forms.NumericUpDown();
            this.numWeakCvrDistance = new System.Windows.Forms.NumericUpDown();
            this.label9 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.label23 = new System.Windows.Forms.Label();
            this.label24 = new System.Windows.Forms.Label();
            this.numMultiCellNum2 = new System.Windows.Forms.NumericUpDown();
            this.numMultiCellDistance2 = new System.Windows.Forms.NumericUpDown();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.numFastFadeRsrpDiff = new System.Windows.Forms.NumericUpDown();
            this.numFastFadeRsrp = new System.Windows.Forms.NumericUpDown();
            this.label16 = new System.Windows.Forms.Label();
            this.numFastFadeDistance = new System.Windows.Forms.NumericUpDown();
            this.label18 = new System.Windows.Forms.Label();
            this.label17 = new System.Windows.Forms.Label();
            this.label20 = new System.Windows.Forms.Label();
            this.label19 = new System.Windows.Forms.Label();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.numSiteAltitude = new System.Windows.Forms.NumericUpDown();
            this.label21 = new System.Windows.Forms.Label();
            this.label22 = new System.Windows.Forms.Label();
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiCellNum)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiCellDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiBand)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiRsrp)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiDistance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiLev)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCvrRsrp)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCvrCellDis)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCvrDistance)).BeginInit();
            this.groupBox3.SuspendLayout();
            this.groupBox6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiCellNum2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiCellDistance2)).BeginInit();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numFastFadeRsrpDiff)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFastFadeRsrp)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFastFadeDistance)).BeginInit();
            this.groupBox5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteAltitude)).BeginInit();
            this.SuspendLayout();
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.label6);
            this.groupBox1.Controls.Add(this.label5);
            this.groupBox1.Controls.Add(this.numMultiCellNum);
            this.groupBox1.Controls.Add(this.numMultiCellDistance);
            this.groupBox1.Location = new System.Drawing.Point(6, 100);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(399, 67);
            this.groupBox1.TabIndex = 4;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "高密度站间距场景";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(230, 32);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(65, 12);
            this.label6.TabIndex = 1;
            this.label6.Text = "米，个数≥";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(30, 32);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(113, 12);
            this.label5.TabIndex = 1;
            this.label5.Text = "涉及小区之间距离≤";
            // 
            // numMultiCellNum
            // 
            this.numMultiCellNum.Location = new System.Drawing.Point(301, 26);
            this.numMultiCellNum.Name = "numMultiCellNum";
            this.numMultiCellNum.Size = new System.Drawing.Size(75, 21);
            this.numMultiCellNum.TabIndex = 1;
            this.numMultiCellNum.Value = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.numMultiCellNum.ValueChanged += new System.EventHandler(this.numMultiCellNum_ValueChanged);
            // 
            // numMultiCellDistance
            // 
            this.numMultiCellDistance.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numMultiCellDistance.Location = new System.Drawing.Point(149, 26);
            this.numMultiCellDistance.Maximum = new decimal(new int[] {
            10000000,
            0,
            0,
            0});
            this.numMultiCellDistance.Name = "numMultiCellDistance";
            this.numMultiCellDistance.Size = new System.Drawing.Size(75, 21);
            this.numMultiCellDistance.TabIndex = 0;
            this.numMultiCellDistance.Value = new decimal(new int[] {
            300,
            0,
            0,
            0});
            this.numMultiCellDistance.ValueChanged += new System.EventHandler(this.numMultiCellDistance_ValueChanged);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(30, 61);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(53, 12);
            this.label3.TabIndex = 1;
            this.label3.Text = "覆盖带：";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(18, 34);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 1;
            this.label2.Text = "最强信号≥";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(236, 61);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(65, 12);
            this.label4.TabIndex = 1;
            this.label4.Text = "持续距离≥";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(171, 61);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(17, 12);
            this.label14.TabIndex = 1;
            this.label14.Text = "dB";
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(388, 61);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(17, 12);
            this.label15.TabIndex = 1;
            this.label15.Text = "米";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(170, 34);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(23, 12);
            this.label13.TabIndex = 1;
            this.label13.Text = "dBm";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(224, 34);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(77, 12);
            this.label1.TabIndex = 1;
            this.label1.Text = "重叠覆盖度≥";
            // 
            // numMultiBand
            // 
            this.numMultiBand.Location = new System.Drawing.Point(89, 56);
            this.numMultiBand.Name = "numMultiBand";
            this.numMultiBand.Size = new System.Drawing.Size(75, 21);
            this.numMultiBand.TabIndex = 2;
            this.numMultiBand.Value = new decimal(new int[] {
            6,
            0,
            0,
            0});
            // 
            // numMultiRsrp
            // 
            this.numMultiRsrp.Location = new System.Drawing.Point(89, 29);
            this.numMultiRsrp.Maximum = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numMultiRsrp.Minimum = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numMultiRsrp.Name = "numMultiRsrp";
            this.numMultiRsrp.Size = new System.Drawing.Size(75, 21);
            this.numMultiRsrp.TabIndex = 0;
            this.numMultiRsrp.Value = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            // 
            // numMultiDistance
            // 
            this.numMultiDistance.Location = new System.Drawing.Point(307, 56);
            this.numMultiDistance.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.numMultiDistance.Name = "numMultiDistance";
            this.numMultiDistance.Size = new System.Drawing.Size(75, 21);
            this.numMultiDistance.TabIndex = 3;
            this.numMultiDistance.Value = new decimal(new int[] {
            20,
            0,
            0,
            0});
            // 
            // numMultiLev
            // 
            this.numMultiLev.Location = new System.Drawing.Point(307, 29);
            this.numMultiLev.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numMultiLev.Name = "numMultiLev";
            this.numMultiLev.Size = new System.Drawing.Size(75, 21);
            this.numMultiLev.TabIndex = 1;
            this.numMultiLev.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.label7);
            this.groupBox2.Controls.Add(this.numWeakCvrRsrp);
            this.groupBox2.Controls.Add(this.numWeakCvrCellDis);
            this.groupBox2.Controls.Add(this.numWeakCvrDistance);
            this.groupBox2.Controls.Add(this.label9);
            this.groupBox2.Controls.Add(this.label12);
            this.groupBox2.Controls.Add(this.label11);
            this.groupBox2.Controls.Add(this.label10);
            this.groupBox2.Controls.Add(this.label8);
            this.groupBox2.Location = new System.Drawing.Point(440, 13);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(433, 126);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "持续衰落场景";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(42, 34);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(41, 12);
            this.label7.TabIndex = 1;
            this.label7.Text = "信号≤";
            // 
            // numWeakCvrRsrp
            // 
            this.numWeakCvrRsrp.Location = new System.Drawing.Point(89, 29);
            this.numWeakCvrRsrp.Maximum = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numWeakCvrRsrp.Minimum = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numWeakCvrRsrp.Name = "numWeakCvrRsrp";
            this.numWeakCvrRsrp.Size = new System.Drawing.Size(75, 21);
            this.numWeakCvrRsrp.TabIndex = 0;
            this.numWeakCvrRsrp.Value = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            // 
            // numWeakCvrCellDis
            // 
            this.numWeakCvrCellDis.Location = new System.Drawing.Point(197, 85);
            this.numWeakCvrCellDis.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.numWeakCvrCellDis.Name = "numWeakCvrCellDis";
            this.numWeakCvrCellDis.Size = new System.Drawing.Size(75, 21);
            this.numWeakCvrCellDis.TabIndex = 2;
            this.numWeakCvrCellDis.Value = new decimal(new int[] {
            300,
            0,
            0,
            0});
            // 
            // numWeakCvrDistance
            // 
            this.numWeakCvrDistance.Location = new System.Drawing.Point(89, 56);
            this.numWeakCvrDistance.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.numWeakCvrDistance.Name = "numWeakCvrDistance";
            this.numWeakCvrDistance.Size = new System.Drawing.Size(75, 21);
            this.numWeakCvrDistance.TabIndex = 1;
            this.numWeakCvrDistance.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(18, 90);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(173, 12);
            this.label9.TabIndex = 1;
            this.label9.Text = "主服与相关采样点的平均距离≤";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(168, 34);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(23, 12);
            this.label12.TabIndex = 1;
            this.label12.Text = "dBm";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(170, 61);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(17, 12);
            this.label11.TabIndex = 1;
            this.label11.Text = "米";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(278, 90);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(17, 12);
            this.label10.TabIndex = 1;
            this.label10.Text = "米";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(18, 61);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(65, 12);
            this.label8.TabIndex = 1;
            this.label8.Text = "持续距离≥";
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.label3);
            this.groupBox3.Controls.Add(this.groupBox6);
            this.groupBox3.Controls.Add(this.groupBox1);
            this.groupBox3.Controls.Add(this.label1);
            this.groupBox3.Controls.Add(this.numMultiLev);
            this.groupBox3.Controls.Add(this.label2);
            this.groupBox3.Controls.Add(this.numMultiDistance);
            this.groupBox3.Controls.Add(this.label4);
            this.groupBox3.Controls.Add(this.numMultiRsrp);
            this.groupBox3.Controls.Add(this.label14);
            this.groupBox3.Controls.Add(this.numMultiBand);
            this.groupBox3.Controls.Add(this.label15);
            this.groupBox3.Controls.Add(this.label13);
            this.groupBox3.Location = new System.Drawing.Point(12, 12);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(422, 259);
            this.groupBox3.TabIndex = 0;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "扫频路段场景(重叠覆盖)";
            // 
            // groupBox6
            // 
            this.groupBox6.Controls.Add(this.label23);
            this.groupBox6.Controls.Add(this.label24);
            this.groupBox6.Controls.Add(this.numMultiCellNum2);
            this.groupBox6.Controls.Add(this.numMultiCellDistance2);
            this.groupBox6.Location = new System.Drawing.Point(6, 173);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Size = new System.Drawing.Size(399, 67);
            this.groupBox6.TabIndex = 5;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "多小区竞争覆盖场景";
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(230, 35);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(65, 12);
            this.label23.TabIndex = 1;
            this.label23.Text = "米，个数≥";
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Location = new System.Drawing.Point(30, 35);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(113, 12);
            this.label24.TabIndex = 1;
            this.label24.Text = "涉及小区之间距离＞";
            // 
            // numMultiCellNum2
            // 
            this.numMultiCellNum2.Location = new System.Drawing.Point(301, 30);
            this.numMultiCellNum2.Name = "numMultiCellNum2";
            this.numMultiCellNum2.Size = new System.Drawing.Size(75, 21);
            this.numMultiCellNum2.TabIndex = 1;
            this.numMultiCellNum2.Value = new decimal(new int[] {
            2,
            0,
            0,
            0});
            this.numMultiCellNum2.ValueChanged += new System.EventHandler(this.numMultiCellNum2_ValueChanged);
            // 
            // numMultiCellDistance2
            // 
            this.numMultiCellDistance2.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numMultiCellDistance2.Location = new System.Drawing.Point(149, 30);
            this.numMultiCellDistance2.Maximum = new decimal(new int[] {
            10000000,
            0,
            0,
            0});
            this.numMultiCellDistance2.Name = "numMultiCellDistance2";
            this.numMultiCellDistance2.Size = new System.Drawing.Size(75, 21);
            this.numMultiCellDistance2.TabIndex = 0;
            this.numMultiCellDistance2.Value = new decimal(new int[] {
            300,
            0,
            0,
            0});
            this.numMultiCellDistance2.ValueChanged += new System.EventHandler(this.numMultiCellDistance2_ValueChanged);
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.numFastFadeRsrpDiff);
            this.groupBox4.Controls.Add(this.numFastFadeRsrp);
            this.groupBox4.Controls.Add(this.label16);
            this.groupBox4.Controls.Add(this.numFastFadeDistance);
            this.groupBox4.Controls.Add(this.label18);
            this.groupBox4.Controls.Add(this.label17);
            this.groupBox4.Controls.Add(this.label20);
            this.groupBox4.Controls.Add(this.label19);
            this.groupBox4.Location = new System.Drawing.Point(440, 145);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(433, 126);
            this.groupBox4.TabIndex = 2;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "间断性快衰场景";
            // 
            // numFastFadeRsrpDiff
            // 
            this.numFastFadeRsrpDiff.Location = new System.Drawing.Point(118, 73);
            this.numFastFadeRsrpDiff.Maximum = new decimal(new int[] {
            200,
            0,
            0,
            0});
            this.numFastFadeRsrpDiff.Name = "numFastFadeRsrpDiff";
            this.numFastFadeRsrpDiff.Size = new System.Drawing.Size(75, 21);
            this.numFastFadeRsrpDiff.TabIndex = 3;
            this.numFastFadeRsrpDiff.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // numFastFadeRsrp
            // 
            this.numFastFadeRsrp.Location = new System.Drawing.Point(118, 45);
            this.numFastFadeRsrp.Maximum = new decimal(new int[] {
            25,
            0,
            0,
            0});
            this.numFastFadeRsrp.Minimum = new decimal(new int[] {
            141,
            0,
            0,
            -2147483648});
            this.numFastFadeRsrp.Name = "numFastFadeRsrp";
            this.numFastFadeRsrp.Size = new System.Drawing.Size(75, 21);
            this.numFastFadeRsrp.TabIndex = 1;
            this.numFastFadeRsrp.Value = new decimal(new int[] {
            105,
            0,
            0,
            -2147483648});
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(11, 50);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(101, 12);
            this.label16.TabIndex = 1;
            this.label16.Text = "主服、邻区信号≥";
            // 
            // numFastFadeDistance
            // 
            this.numFastFadeDistance.Location = new System.Drawing.Point(240, 45);
            this.numFastFadeDistance.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.numFastFadeDistance.Name = "numFastFadeDistance";
            this.numFastFadeDistance.Size = new System.Drawing.Size(75, 21);
            this.numFastFadeDistance.TabIndex = 2;
            this.numFastFadeDistance.Value = new decimal(new int[] {
            200,
            0,
            0,
            0});
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(199, 50);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(35, 12);
            this.label18.TabIndex = 1;
            this.label18.Text = "dBm，";
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(321, 50);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(41, 12);
            this.label17.TabIndex = 1;
            this.label17.Text = "米内，";
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(199, 79);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(17, 12);
            this.label20.TabIndex = 1;
            this.label20.Text = "dB";
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(23, 79);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(89, 12);
            this.label19.TabIndex = 1;
            this.label19.Text = "出现信号波动≥";
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.numSiteAltitude);
            this.groupBox5.Controls.Add(this.label21);
            this.groupBox5.Controls.Add(this.label22);
            this.groupBox5.Location = new System.Drawing.Point(12, 277);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(422, 67);
            this.groupBox5.TabIndex = 3;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "路面塔场景";
            // 
            // numSiteAltitude
            // 
            this.numSiteAltitude.Location = new System.Drawing.Point(155, 27);
            this.numSiteAltitude.Maximum = new decimal(new int[] {
            50000,
            0,
            0,
            0});
            this.numSiteAltitude.Name = "numSiteAltitude";
            this.numSiteAltitude.Size = new System.Drawing.Size(75, 21);
            this.numSiteAltitude.TabIndex = 0;
            this.numSiteAltitude.Value = new decimal(new int[] {
            35,
            0,
            0,
            0});
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(108, 33);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(41, 12);
            this.label21.TabIndex = 1;
            this.label21.Text = "站高≥";
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(236, 33);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(125, 12);
            this.label22.TabIndex = 1;
            this.label22.Text = "米，该站小区间有切换";
            // 
            // btnOK
            // 
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Location = new System.Drawing.Point(717, 368);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(75, 23);
            this.btnOK.TabIndex = 4;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            // 
            // btnCancel
            // 
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.Location = new System.Drawing.Point(798, 368);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 5;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // ConditionDlg
            // 
            this.AcceptButton = this.btnOK;
            this.Appearance.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(885, 403);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.groupBox4);
            this.Controls.Add(this.groupBox5);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox2);
            this.Name = "ConditionDlg";
            this.Text = "场景条件设置";
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiCellNum)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiCellDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiBand)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiRsrp)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiDistance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiLev)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCvrRsrp)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCvrCellDis)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numWeakCvrDistance)).EndInit();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.groupBox6.ResumeLayout(false);
            this.groupBox6.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiCellNum2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMultiCellDistance2)).EndInit();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numFastFadeRsrpDiff)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFastFadeRsrp)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFastFadeDistance)).EndInit();
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numSiteAltitude)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.NumericUpDown numMultiCellNum;
        private System.Windows.Forms.NumericUpDown numMultiCellDistance;
        private System.Windows.Forms.NumericUpDown numMultiBand;
        private System.Windows.Forms.NumericUpDown numMultiRsrp;
        private System.Windows.Forms.NumericUpDown numMultiDistance;
        private System.Windows.Forms.NumericUpDown numMultiLev;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.NumericUpDown numWeakCvrRsrp;
        private System.Windows.Forms.NumericUpDown numWeakCvrCellDis;
        private System.Windows.Forms.NumericUpDown numWeakCvrDistance;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.GroupBox groupBox6;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.NumericUpDown numMultiCellNum2;
        private System.Windows.Forms.NumericUpDown numMultiCellDistance2;
        private System.Windows.Forms.NumericUpDown numFastFadeRsrpDiff;
        private System.Windows.Forms.NumericUpDown numFastFadeRsrp;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.NumericUpDown numFastFadeDistance;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.NumericUpDown numSiteAltitude;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Label label18;
    }
}