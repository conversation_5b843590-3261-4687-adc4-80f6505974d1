﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Stat;
using MasterCom.MTGis;

namespace MasterCom.RAMS.Net
{
    public class DIYEventByRegion : DIYEventQuery
    {
        public DIYEventByRegion(MainModel mainModel)
            : base(mainModel)
        {
        }
        public override string Name
        {
            get { return "事件查询(按区域)"; }
        }
        public override string IconName
        {
            get { return "Images/regionevt.gif"; }
        }

        public override MainModel.NeedSearchType needSearchType()
        {
            return MasterCom.RAMS.Model.MainModel.NeedSearchType.Region;
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 11000, 11006, this.Name);
        }
        protected override void prepareOtherEventFilter(Package package)
        {
            AddDIYRegion_Sample(package);
        }
        protected override void prepareTimeSpanIntersect(Package package, TimePeriod period)
        {
            AddDIYPeriod_Between(package, period);
        }
        public override bool CanEnabled(SearchGeometrys searchGeometrys)
        {
            if (searchGeometrys.Region == null)
            {
                return false;
            }
            return searchGeometrys.IsValidRegion;
        }
        protected override bool isValidPoint(double ltX, double ltY, double brX, double brY)
        {
            try
            {
                return Condition.Geometorys == null || Condition.Geometorys.GeoOp2.CheckRectCenterInRegion(new DbRect(ltX, ltY, brX, brY));
            }
            catch
            {
                return false;
            }

        }
        protected override bool isValidPoint(double jd, double wd)
        {
            try
            {
                return Condition.Geometorys == null || Condition.Geometorys.GeoOp2.CheckPointInRegion(jd, wd);
            }
            catch(Exception ex)
            {
                System.Windows.Forms.MessageBox.Show(ex.Source + Environment.NewLine + ex.Message);
                return false;
            }
        }
        
    }
}
