﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using AxMapWinGIS;
using GMap.NET;
using GMap.NET.WindowsForms;
using MasterCom.RAMS.ExMap;
using MasterCom.RAMS.Model;

namespace MasterCom.MTGis
{
    public interface IGisAdapter
    {
        void ToDisplay(DbPoint pntSrc, out PointF pntDest);

        void ToDisplay(DbRect srcRect, out RectangleF desRect);

        void ToDisplay(IList<DbPoint> pntSrc, out PointF[] pntDest);

        void FromDisplay(PointF pntSrc, out DbPoint pntDest);

        void FromDisplay(RectangleF srcRect, out DbRect destRect);

        void FromDisplay(IList<PointF> pntSrc, out DbPoint[] pntDest);

        DbPoint MapCenter
        {
            get;
        }

        double DistancePer50Pixel
        {
            get;
        }
        double GetScale();
    }

    public class MtGisAdapter : IGisAdapter
    {
        protected AxMap mapCtrl = null;

        public MtGisAdapter(AxMap mapCtrl)
        {
            this.mapCtrl = mapCtrl;
            mapCtrl_ExtentsChanged(null, EventArgs.Empty);
            mapCtrl.ExtentsChanged += mapCtrl_ExtentsChanged;
        }

        double pixelPerProjX = 0;
        double pixelPerProjY = 0;
        DbRect bounds = new DbRect();
        void mapCtrl_ExtentsChanged(object sender, EventArgs e)
        {
            MapWinGIS.Extents ext = mapCtrl.Extents as MapWinGIS.Extents;
            pixelPerProjX = mapCtrl.Width / (ext.xMax - ext.xMin);
            pixelPerProjY = mapCtrl.Height / (ext.yMax - ext.yMin);
            bounds = new DbRect(ext.xMin, ext.yMin, ext.xMax, ext.yMax);
        }

        public void ToDisplay(MasterCom.MTGis.DbPoint pntSrc, out System.Drawing.PointF pntDest)
        {
            double pixX = 0;
            double pixY = 0;
            pixX = (pntSrc.x - bounds.x1) * pixelPerProjX;
            pixY = (bounds.y2 - pntSrc.y) * pixelPerProjY;
            pntDest = new PointF((float)pixX, (float)pixY);
        }

        public void ToDisplay(IList<DbPoint> pntSrc, out System.Drawing.PointF[] pntDest)
        {
            int count = pntSrc.Count;
            pntDest = new PointF[count];
            for (int i = 0; i < count; i++)
            {
                double pixX = 0;
                double pixY = 0;
                pixX = (pntSrc[i].x - bounds.x1) * pixelPerProjX;
                pixY = (bounds.y2 - pntSrc[i].y) * pixelPerProjY;
                pntDest[i] = new PointF((float)pixX, (float)pixY);
            }
        }

        public void ToDisplay(DbRect srcRect, out RectangleF desRect)
        {
            double pX1 = 0, pX2 = 0, pY1 = 0, pY2 = 0;
            mapCtrl.ProjToPixel(srcRect.x1, srcRect.y1, ref pX1, ref pY1);
            mapCtrl.ProjToPixel(srcRect.x2, srcRect.y2, ref pX2, ref pY2);

            desRect = new RectangleF((float)pX1, (float)pY2, (float)(pX2 - pX1), (float)(pY1 - pY2));
        }

        public void FromDisplay(System.Drawing.PointF pntSrc, out MasterCom.MTGis.DbPoint pntDest)
        {
            double projX = 0, projY = 0;
            projX = bounds.x1 + pntSrc.X / pixelPerProjX;
            projY = bounds.y2 - pntSrc.Y / pixelPerProjY;
            pntDest = new MasterCom.MTGis.DbPoint(projX, projY);
        }

        public void FromDisplay(System.Drawing.RectangleF srcRect, out DbRect destRect)
        {
            double x1 = 0;
            double x2 = 0;
            double y1 = 0;
            double y2 = 0;
            
            x1 = bounds.x1 + srcRect.Left / pixelPerProjX;
            y1 = bounds.y2 - srcRect.Top / pixelPerProjY;
            x2 = bounds.x1 + srcRect.Right / pixelPerProjX;
            y2 = bounds.y2 - srcRect.Bottom / pixelPerProjY;
            destRect = new DbRect(x1, y1, x2, y2);
        }

        public void FromDisplay(IList<PointF> pntSrc, out DbPoint[] pntDest)
        {
            int count = pntSrc.Count;
            pntDest = new DbPoint[count];

            for (int idx = 0; idx < count; idx++)
            {
                DbPoint rtPnt;
                FromDisplay(pntSrc[idx], out rtPnt);
                pntDest[idx] = rtPnt;
            }
        }

        #region IGisAdapter 成员


        public DbPoint MapCenter
        {
            get
            {
                MapWinGIS.Extents ext = mapCtrl.Extents as MapWinGIS.Extents;
                double xmin = ext.xMin;
                double xmax = ext.xMax;
                double ymin = ext.yMin;
                double ymax = ext.yMax;
                double centerX = (xmin + xmax) / 2;
                double centerY = (ymin + ymax) / 2;
                return new DbPoint(centerX, centerY);
            }
        }

        public double DistancePer50Pixel
        {
            get
            {
                PointF ltPf = new PointF(0, 0);
                PointF ltPf50 = new PointF(50, 0);
                DbPoint ltPt;
                DbPoint ltPt50;
                this.FromDisplay(ltPf, out ltPt);
                this.FromDisplay(ltPf50, out ltPt50);
                double value = Math.Round(MasterCom.Util.MathFuncs.GetDistance(ltPt.x, ltPt.y, ltPt50.x, ltPt50.y), 2);
                return value;
            }
        }

        #endregion


        public double GetScale()
        {
            return mapCtrl.CurrentScale;
        }
    }

    public class GMapGisAdapter : IGisAdapter
    {
        protected GMapControl mapCtrl = null;

        public GMapGisAdapter(GMapControl mapCtrl)
        {
            this.mapCtrl = mapCtrl;
        }

        #region 内部适配（纠偏）
        protected GPoint FromLatLngToLocalAdaptered(PointLatLng point)
        {
            return FromLatLngToLocalAdaptered(point.Lng, point.Lat);
        }

        protected GPoint FromLatLngToLocalAdaptered(double lng, double lat)
        {
            double marsLng = lng;
            double marsLat = lat;
            if (mapCtrl.MapType != MapType.MT_2DMap && mapCtrl.MapType != MapType.MT_Satellite)
            {
                EvilTransform.Wgs2Mars(lat, lng, out marsLat, out marsLng);
            }
            PointLatLng tmp = new PointLatLng(marsLat, marsLng);
            return mapCtrl.FromLatLngToLocal(tmp);
        }

        protected PointLatLng FromLocalToLatLngAdaptered(int x, int y)
        {
            PointLatLng tmp = mapCtrl.FromLocalToLatLng(x, y);
            if (mapCtrl.MapType != MapType.MT_2DMap && mapCtrl.MapType != MapType.MT_Satellite)
            {
                double lngO = 0;
                double latO = 0;

                //反推法
                EvilTransform.Mars2Wgs(tmp.Lat, tmp.Lng, out latO, out lngO);
                tmp.Lat = latO;
                tmp.Lng = lngO;
            }
            return tmp;
        }

        protected PointLatLng FromLocalToLatLngAdaptered(GPoint point)
        {
            return FromLocalToLatLngAdaptered(point.X, point.Y);
        }
        #endregion

        public void ToDisplay(MasterCom.MTGis.DbPoint pntSrc, out System.Drawing.PointF pntDest)
        {
            GPoint gPnt = this.FromLatLngToLocalAdaptered(pntSrc.x, pntSrc.y);
            pntDest = new System.Drawing.PointF(gPnt.X, gPnt.Y);
        }

        public void ToDisplay(IList<DbPoint> pntSrc, out System.Drawing.PointF[] pntDest)
        {
            int count = pntSrc.Count;
            pntDest = new System.Drawing.PointF[count];
            for (int idx = 0; idx < count; idx++)
            {
                System.Drawing.PointF rtPntF;
                ToDisplay(pntSrc[idx], out rtPntF);
                pntDest[idx] = rtPntF;
            }
        }

        public void ToDisplay(DbRect srcRect, out RectangleF desRect)
        {
            GPoint pntTL = FromLatLngToLocalAdaptered(srcRect.x1, srcRect.y2);
            GPoint pntBR = FromLatLngToLocalAdaptered(srcRect.x2, srcRect.y1);

            desRect = new RectangleF(pntTL.X, pntTL.Y, pntBR.X - pntTL.X, pntBR.Y - pntTL.Y);
        }

        public void FromDisplay(System.Drawing.PointF pntSrc, out MasterCom.MTGis.DbPoint pntDest)
        {
            PointLatLng llPnt = FromLocalToLatLngAdaptered((int)pntSrc.X, (int)pntSrc.Y);
            pntDest = new MasterCom.MTGis.DbPoint(llPnt.Lng, llPnt.Lat);
        }

        public void FromDisplay(System.Drawing.RectangleF srcRect, out MasterCom.MTGis.DbRect destRect)
        {
            PointLatLng llPnt = FromLocalToLatLngAdaptered((int)srcRect.Left, (int)srcRect.Top);
            PointLatLng brPnt = FromLocalToLatLngAdaptered((int)srcRect.Right, (int)srcRect.Bottom);
            destRect = new MasterCom.MTGis.DbRect(llPnt.Lng, llPnt.Lat, brPnt.Lng, brPnt.Lat);
        }

        public void FromDisplay(IList<PointF> pntSrc, out MasterCom.MTGis.DbPoint[] pntDest)
        {
            int count = pntSrc.Count;
            pntDest = new MasterCom.MTGis.DbPoint[count];
            for (int idx = 0; idx < count; idx++)
            {
                DbPoint rtPnt;
                FromDisplay(pntSrc[idx], out rtPnt);
                pntDest[idx] = rtPnt;
            }
        }

        #region IGisAdapter 成员


        public DbPoint MapCenter
        {
            get
            {
                return new DbPoint(mapCtrl.Position.Lng, mapCtrl.Position.Lat);
            }
        }

        public double DistancePer50Pixel
        {
            get
            {
                PointF ltPf = new PointF(0, 0);
                PointF ltPf50 = new PointF(50, 0);
                DbPoint ltPt;
                DbPoint ltPt50;
                this.FromDisplay(ltPf, out ltPt);
                this.FromDisplay(ltPf50, out ltPt50);
                double value = Math.Round(MasterCom.Util.MathFuncs.GetDistance(ltPt.x, ltPt.y, ltPt50.x, ltPt50.y), 2);
                return value;
            }
        }

        #endregion

        public double GetScale()
        {
            return RAMS.ExMap.ExMapUtil.parseScaleFromZoomLevel(mapCtrl.Zoom);
        }
    }


}
