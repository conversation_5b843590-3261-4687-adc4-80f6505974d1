﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using DevExpress.XtraCharts;
using System.Reflection;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class TDAntMRAnaForm : MinCloseForm
    {
        MapForm mapForm;
        public TDAntMRAnaForm(MainModel mainModel)
            : base(mainModel)
        {
            InitializeComponent();
            rtbDesc.Text = WriteMRDesc();
            this.mapForm = MainModel.MainForm.GetMapForm();
        }

        public List<List<NPOIRow>> nrDatasList { get; set; }
        public List<string> sheetNames { get; set; }
        protected int iPointIndex = 0;
        protected int iTagKey = 0;
        private Dictionary<string, ZTTDAntMRAna.TdCellMRData> DicCellParaData = new Dictionary<string, ZTTDAntMRAna.TdCellMRData>();
        private Dictionary<string, List<string>> btsDic = new Dictionary<string, List<string>>();

        /// <summary>
        /// 数据初始化，加载前200个小区
        /// </summary>
        public void FillData(Dictionary<LaiKey, ZTTDAntMRAna.TdCellMRData> tdCellParaData)
        {
            iTagKey = 2;//小区名称
            foreach (LaiKey lKey in tdCellParaData.Keys)
            {
                ZTTDAntMRAna.TdCellMRData tdMrData = tdCellParaData[lKey];
                if (!DicCellParaData.ContainsKey(tdMrData.cellname))
                    DicCellParaData.Add(tdMrData.cellname, tdMrData);

                string strBtsName = tdMrData.tdCellInfo.基站BTS名称;
                List<string> cellList;
                if (!btsDic.TryGetValue(strBtsName, out cellList))
                    cellList = new List<string>();
                if (!cellList.Contains(tdMrData.cellname))
                    cellList.Add(tdMrData.cellname);
                btsDic[strBtsName] = cellList;
            }

            labNum.Text = DicCellParaData.Count.ToString();
            int iPage = DicCellParaData.Count % 200 > 0 ? DicCellParaData.Count / 200 + 1 : DicCellParaData.Count / 200;
            labPage.Text = iPage.ToString();

            dataGridViewTdCell.Rows.Clear();//小区级
            int rowCellAt = 0;
            foreach (NPOIRow row in nrDatasList[0])
            {
                if (rowCellAt == 0)
                {
                    intiDataViewColumn(dataGridViewTdCell, row.cellValues);
                    rowCellAt++;
                    continue;
                }
                if (rowCellAt > 200)
                    break;
                initDataRow(dataGridViewTdCell, row);
                rowCellAt++;
            }

            txtPage.Text = "1";
        }

        /// <summary>
        /// 按小区模糊查找，前200个小区
        /// </summary>
        protected void FillData(string strCellName)
        {
            dataGridViewTdCell.Rows.Clear();//小区级
            int rowCellAt = 0;
            foreach (NPOIRow row in nrDatasList[0])
            {
                if (nrDatasList[0][0].cellValues[0].ToString() == row.cellValues[0].ToString())
                    continue;

                if (strCellName != "" && row.cellValues[iTagKey].ToString().IndexOf(strCellName) < 0)
                    continue;

                if (rowCellAt >= 200)
                    break;

                initDataRow(dataGridViewTdCell, row);
                rowCellAt++;
            }
        }

        /// <summary>
        /// 按页数查找
        /// </summary>
        protected void FillData(int iPage)
        {
            dataGridViewTdCell.Rows.Clear();//小区级
            int iCount = -1;
            int rowCellAt = 0;
            foreach (NPOIRow row in nrDatasList[0])
            {
                if (nrDatasList[0][0].cellValues[0].ToString() == row.cellValues[0].ToString())
                    continue;

                iCount++;
                if (iCount / 200 != iPage)
                    continue;

                initDataRow(dataGridViewTdCell, row);
                rowCellAt++;
            }
        }

        /// <summary>
        /// 初始化数据列头名称
        /// </summary>
        protected void intiDataViewColumn(DataGridView dataGridView, List<object> objs)
        {
            dataGridView.Columns.Clear();
            int idx = 1;
            foreach (object obj in objs)
            {
                dataGridView.Columns.Add(idx++.ToString(), obj.ToString());
            }
        }

        /// <summary>
        /// 数据赋赋值
        /// </summary>
        protected void initDataRow(DataGridView datatGridView, NPOIRow nop)
        {
            DataGridViewRow row = new DataGridViewRow();
            row.Tag = nop.cellValues[iTagKey];//键值
            foreach (object obj in nop.cellValues)
            {
                DataGridViewTextBoxCell boxcell = new DataGridViewTextBoxCell();
                boxcell.Value = obj.ToString();
                row.Cells.Add(boxcell);
            }
            datatGridView.Rows.Add(row);
        }

        ZTTDAntMRAna.TdCellMRData data;
        private void miShowChart_Click(object sender, EventArgs e)
        {
            string strCellName = dataGridViewTdCell.SelectedRows[0].Tag as string;
            if (!DicCellParaData.TryGetValue(strCellName, out data))
                return;
            
            if (data != null)
            {
                fillCheckBox(data.tdCellInfo.基站BTS名称);
                groupControl4.Text = string.Format("MR测量项数据图表({0})", data.cellname);
                this.xtraTabControl1.SelectedTabPageIndex = 2;

                drawChartControl();
                //小区级雷达图
                drawCellMRRadarSeries();
                drawCellCoverRadarSeries(data.maxTaArray);
                //站点级雷达图
                drawEnodeBCoverRadarSeries();
                drawEnodeBMRRadarSeries();
                //多小区匹配
                macthCell(500);
            }
        }

        private void drawChartControl()
        {
            #region 图表数据整理
            Dictionary<string, object> txPowerDic = new Dictionary<string, object>();
            for (int i = 0; i < 28; i++)
            {
                int j = -50 + i * 3;
                string iKey = j.ToString();
                int iValue = data.mrData.tdMRTxPowerItem.dataValue[i];
                if (!txPowerDic.ContainsKey(iKey))
                    txPowerDic.Add(iKey, iValue);
            }
            DrawTableSeries(txPowerDic, chartControlPower);

            Dictionary<string, object> rscpDic = new Dictionary<string, object>();
            for (int i = 0; i < 55; i++)
            {
                int j = i - 101;
                string iKey = j.ToString();
                int iValue = data.mrData.tdMRRscpItem.dataValue[i];
                if (!rscpDic.ContainsKey(iKey))
                    rscpDic.Add(iKey, iValue);
            }
            DrawTableSeries(rscpDic, chartControlRsrp);

            Dictionary<string, object> sirDic = new Dictionary<string, object>();
            for (int i = 0; i < 64; i++)
            {
                string iKey = Math.Round((i - 23.0) / 2, 1).ToString();
                int iValue = data.mrData.tdMRSirUlItem.dataValue[i];
                if (!sirDic.ContainsKey(iKey))
                    sirDic.Add(iKey, iValue);
            }
            DrawTableSeries(sirDic, chartControlSinr);

            Dictionary<string, object> aoaDic = new Dictionary<string, object>();
            for (int i = 0; i < 72; i++)
            {
                int j = (i + 1) * 5;
                string iKey = j.ToString();
                int iValue = data.mrData.tdMRAoaItem.dataValue[i];
                if (!aoaDic.ContainsKey(iKey))
                    aoaDic.Add(iKey, iValue);
            }
            DrawTableSeries(aoaDic, chartControlAoa);

            Dictionary<string, object> taDic = new Dictionary<string, object>();
            for (int i = 0; i < 37; i++)
            {
                string iKey = ZTAntFuncHelper.calcDistByTdMrTa(i).ToString();
                int iValue = data.mrData.tdMRTaItem.dataValue[i];
                if (!taDic.ContainsKey(iKey))
                    taDic.Add(iKey, iValue);
            }
            DrawTableSeries(taDic, chartControlTA);
            #endregion
        }

        private void miShowSimulation_Click(object sender, EventArgs e)
        {
            string strCellName = dataGridViewTdCell.SelectedRows[0].Tag as string;
            if (!DicCellParaData.TryGetValue(strCellName, out data))
                return;

            if (data != null)
            {
                TDCell tdCell = CellManager.GetInstance().GetTDCellByName(data.cellname);
                Dictionary<int, List<LongLat>> gisSampleDic = getGisSampleDic(tdCell);

                MainModel.SelectedTDCell = tdCell;
                AntPointLayer antLayer = mapForm.GetLayerBase(typeof(AntPointLayer)) as AntPointLayer;
                if (antLayer != null)
                {
                    if (tdCell != null && tdCell.Antenna != null)
                        MainModel.MainForm.GetMapForm().GoToView(tdCell.Antenna.Longitude, tdCell.Antenna.Latitude);
                    else
                        MainModel.MainForm.GetMapForm().GoToView(data.dLongitude, data.dLatitude);

                    antLayer.iFunc = 1;
                    antLayer.gisSampleDic = gisSampleDic;
                    antLayer.Invalidate();
                }
            }
        }

        private Dictionary<int, List<LongLat>> getGisSampleDic(TDCell tdCell)
        {
            Dictionary<int, List<LongLat>> gisSampleDic = new Dictionary<int, List<LongLat>>();
            if (tdCell == null)
            {
                return gisSampleDic;
            }

            foreach (int iColor in data.aoaTaDic.Keys)
            {
                List<LongLat> longLatList = new List<LongLat>();
                foreach (ZTLteAntMRAna.AntMRAoaTa aoaTa in data.aoaTaDic[iColor])
                {
                    int iDist = ZTAntFuncHelper.calcDistByLteMrTa(aoaTa.iTaId);
                    int iAngle = (aoaTa.iAoaId * 5 + tdCell.Direction) % 360;

                    LongLat cellLongLat = new LongLat();
                    if (tdCell.Antenna != null)
                    {
                        cellLongLat.fLongitude = (float)tdCell.Antenna.Longitude;
                        cellLongLat.fLatitude = (float)tdCell.Antenna.Latitude;
                    }
                    else
                    {
                        cellLongLat.fLongitude = (float)data.dLongitude;
                        cellLongLat.fLatitude = (float)data.dLatitude;
                    }
                    LongLat tmpLongLat = ZTAntFuncHelper.calcPointX(iAngle, iDist, cellLongLat);
                    longLatList.Add(tmpLongLat);
                }
                gisSampleDic.Add(iColor, longLatList);
            }

            return gisSampleDic;
        }

        private void miExportWholeExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(nrDatasList, sheetNames);
        }

        private void 导出CSVToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ZTAntFuncHelper.OutputCsvFile(nrDatasList, sheetNames);
        }

        private void xtraTabControl1_SelectedPageChanged(object sender, DevExpress.XtraTab.TabPageChangedEventArgs e)
        {
            if (xtraTabControl1.SelectedTabPageIndex == 0 || xtraTabControl1.SelectedTabPageIndex == 1)
            {
                miShowChart.Visible = true;
                miShowSimulation.Visible = true;
            }
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            string strCellName = txtCellName.Text;
            FillData(strCellName);
        }

        private void btnGo_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            int iCount = DicCellParaData.Count;
            iCount = iCount % 200 > 0 ? iCount / 200 + 1 : iCount / 200;
            if (iPage < 0)
                iPage = 0;
            else if (iPage > iCount - 1)
                iPage = iCount - 1;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnPrevpage_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            iPage = iPage - 1 >= 0 ? iPage - 1 : iPage;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        private void btnNextpage_Click(object sender, EventArgs e)
        {
            int iPage = 0;
            if (!int.TryParse(txtPage.Text.ToString(), out iPage))
            {
                iPage = 0;
            }
            iPage -= 1;
            int iCount = DicCellParaData.Count;
            iCount = iCount % 200 > 0 ? iCount / 200 + 1 : iCount / 200;

            iPage = iPage + 1 >= iCount ? iPage : iPage + 1;
            txtPage.Text = (iPage + 1).ToString();
            FillData(iPage);
        }

        #region MR统计图表绘制

        /// <summary>
        /// 绘制柱状率图表
        /// </summary>
        private void DrawTableSeries(Dictionary<string,object> dataDic,ChartControl chartCtrl)
        {
            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            SideBySideBarSeriesView sideBySideBarSeriesView = new SideBySideBarSeriesView();
            sideBySideBarSeriesView.BarWidth = 0.1;
            sideBySideBarSeriesView.Border.Visible = false;
            series.View = sideBySideBarSeriesView;

            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;

            foreach (string strKey in dataDic.Keys)
            {
                series.Points.Add(new SeriesPoint(strKey, dataDic[strKey].ToString()));
            }
            iPointIndex = 0;

            chartCtrl.Series.Clear();
            chartCtrl.Series.Insert(0, series);
            ((XYDiagram)chartCtrl.Diagram).AxisY.Range.MinValue = 0;
            ((XYDiagram)chartCtrl.Diagram).AxisY.Range.MaxValue = ZTAntFuncHelper.getMaxValue(dataDic);

            chartCtrl.Focus();
        }

        /// <summary>
        /// 按MR二维数据绘雷达图-小区级
        /// </summary>
        private void drawCellMRRadarSeries()
        {
            chartCellPoint.Series.Clear();
            int idx = 0;
            int iMaxValue = 0;
            string strCellPart = cbPart.Text;
            int iDir = data.tdCellInfo.天线方向角 < 0 ? 0 : data.tdCellInfo.天线方向角;

            foreach (int iColor in data.aoaTaDic.Keys)
            {
                if (ZTAntFuncHelper.checkLegendIsVaild(iColor, strCellPart))
                {
                    AntLegend antLegend = ZTAntFuncHelper.GetMRDataAnaLegend(iColor);
                    Series series = new Series();
                    series.ShowInLegend = true;
                    series.LegendText = antLegend.strLegend;
                    series.PointOptions.PointView = PointView.Values;

                    RadarPointSeriesView pointSeriesView = new RadarPointSeriesView();
                    pointSeriesView.Color = antLegend.colorType;
                    pointSeriesView.PointMarkerOptions.Size = 3;

                    series.View = pointSeriesView;
                    series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
                    series.Label.Visible = false;

                    foreach (ZTLteAntMRAna.AntMRAoaTa aoaTa in data.aoaTaDic[iColor])
                    {
                        int iDist = ZTAntFuncHelper.calcDistByLteMrTa(aoaTa.iTaId);
                        int n = (aoaTa.iAoaId * 5 + iDir) % 360;

                        if (iDist > iMaxValue)
                            iMaxValue = iDist;
                        series.Points.Add(new SeriesPoint(n.ToString(), iDist.ToString()));
                    }

                    chartCellPoint.Series.Insert(idx, series);
                    idx++;
                }
            }

            
            //增加天线方位角连线
            Series seriesCell = ZTAntFuncHelper.DrawRadarLine(Color.Red, "工参", iDir, iMaxValue);
            chartCellPoint.Series.Insert(idx, seriesCell);
            idx++;

            int iMainDir = (data.iMainDir + iDir) % 360;
            Series seriesMR = ZTAntFuncHelper.DrawRadarLine(Color.DarkBlue, "MR", iMainDir, iMaxValue);
            chartCellPoint.Series.Insert(idx, seriesMR);

            if (data.aoaTaDic.Count > 0)
            {
                ((RadarDiagram)chartCellPoint.Diagram).AxisY.Range.MinValue = -1;
                ((RadarDiagram)chartCellPoint.Diagram).AxisY.Range.MaxValue = iMaxValue + 300;
                ((RadarDiagram)chartCellPoint.Diagram).AxisX.GridSpacing = 20;
                ((RadarDiagram)chartCellPoint.Diagram).AxisX.Range.SetInternalMinMaxValues(0, 340);
                ((RadarDiagram)chartCellPoint.Diagram).RotationDirection = RadarDiagramRotationDirection.Clockwise;
            }
            else
            {
                Series series = new Series();
                series.ShowInLegend = false;
                series.PointOptions.PointView = PointView.Values;

                RadarLineSeriesView lineSeriesView = new RadarLineSeriesView();
                lineSeriesView.Color = Color.Blue;
                lineSeriesView.LineMarkerOptions.Size = 2;

                series.View = lineSeriesView;
                series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
                series.Label.Visible = false;
                chartCellPoint.Series.Insert(0, series);
            }
            chartCellPoint.Focus();
        }

        /// <summary>
        /// 按MR二维数据绘雷达图-站点级
        /// </summary>
        private void drawEnodeBMRRadarSeries()
        {
            chartEnodebPoint.Series.Clear();
            int idx = 0;
            int iMaxValue = 0;
            string strCellPart = cbBtsPart.Text;

            List<string> cellNameList = new List<string>();
            if (cbCellName.Text == "全部")
            {
                if (data != null && btsDic.ContainsKey(data.tdCellInfo.基站BTS名称))
                    cellNameList = btsDic[data.tdCellInfo.基站BTS名称];
            }
            else
                cellNameList.Add(cbCellName.Text);

            cellNameList.Sort();
            dealEnodeBMRCellNameList(ref idx, ref iMaxValue, strCellPart, cellNameList);

            if (idx > 0)
            {
                ((RadarDiagram)chartEnodebPoint.Diagram).AxisY.Range.MinValue = -1;
                ((RadarDiagram)chartEnodebPoint.Diagram).AxisY.Range.MaxValue = iMaxValue + 300;
                ((RadarDiagram)chartEnodebPoint.Diagram).AxisX.GridSpacing = 20;
                ((RadarDiagram)chartEnodebPoint.Diagram).AxisX.Range.SetInternalMinMaxValues(0, 340);
                ((RadarDiagram)chartEnodebPoint.Diagram).RotationDirection = RadarDiagramRotationDirection.Clockwise;
            }
            else
            {
                Series series = new Series();
                series.ShowInLegend = false;
                series.PointOptions.PointView = PointView.Values;

                RadarLineSeriesView lineSeriesView = new RadarLineSeriesView();
                lineSeriesView.Color = Color.Blue;
                lineSeriesView.LineMarkerOptions.Size = 2;

                series.View = lineSeriesView;
                series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
                series.Label.Visible = false;
                chartEnodebPoint.Series.Insert(0, series);
            }
            chartEnodebPoint.Focus();
        }

        private void dealEnodeBMRCellNameList(ref int idx, ref int iMaxValue, string strCellPart, List<string> cellNameList)
        {
            foreach (string cellName in cellNameList)
            {
                if (DicCellParaData.ContainsKey(cellName))
                {
                    ZTTDAntMRAna.TdCellMRData tmpData = DicCellParaData[cellName];
                    int iDir = tmpData.tdCellInfo.天线方向角 < 0 ? 0 : tmpData.tdCellInfo.天线方向角;

                    Series series = new Series();
                    series.ShowInLegend = true;
                    series.LegendText = string.Format("CI={0}", tmpData.tdCellInfo.小区CI);
                    series.PointOptions.PointView = PointView.Values;

                    RadarPointSeriesView pointSeriesView = new RadarPointSeriesView();
                    pointSeriesView.Color = ZTAntFuncHelper.getCellColor(tmpData.tdCellInfo.小区CI);
                    pointSeriesView.PointMarkerOptions.Size = 3;
                    series.View = pointSeriesView;
                    series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
                    series.Label.Visible = false;

                    foreach (int iColor in tmpData.aoaTaDic.Keys)
                    {
                        addSeriesPoints(ref iMaxValue, strCellPart, tmpData, iDir, series, iColor);
                    }
                    chartEnodebPoint.Series.Insert(idx, series);
                    idx++;
                }
            }
        }

        private void addSeriesPoints(ref int iMaxValue, string strCellPart, ZTTDAntMRAna.TdCellMRData tmpData, int iDir, Series series, int iColor)
        {
            if (ZTAntFuncHelper.checkLegendIsVaild(iColor, strCellPart))
            {
                foreach (ZTLteAntMRAna.AntMRAoaTa aoaTa in tmpData.aoaTaDic[iColor])
                {
                    int iDist = ZTAntFuncHelper.calcDistByLteMrTa(aoaTa.iTaId);
                    int n = (aoaTa.iAoaId * 5 + iDir) % 360;

                    if (iDist > iMaxValue)
                        iMaxValue = iDist;
                    series.Points.Add(new SeriesPoint(n.ToString(), iDist.ToString()));
                }
            }
        }

        /// <summary>
        /// 按MR模拟覆盖图-小区级
        /// </summary>
        private void drawCellCoverRadarSeries(double[] seriesValues)
        {
            chartCellLine.Series.Clear();

            Series series = new Series();
            series.ShowInLegend = false;
            series.PointOptions.PointView = PointView.Values;

            RadarLineSeriesView lineSeriesView = new RadarLineSeriesView();
            lineSeriesView.Color = Color.Blue;
            lineSeriesView.LineMarkerOptions.Size = 2;

            series.View = lineSeriesView;
            series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            series.Label.Visible = false;
            int iDir = data.tdCellInfo.天线方向角 < 0 ? 0 : data.tdCellInfo.天线方向角;

            for (int i = 0; i < 72; i++)
            {
                int j = (i * 5 + iDir) % 360;
                series.Points.Add(new SeriesPoint(j, seriesValues[i]));
            }
            chartCellLine.Series.Insert(0, series);

            ((RadarDiagram)chartCellLine.Diagram).AxisY.Range.MinValue = -1;
            ((RadarDiagram)chartCellLine.Diagram).AxisY.Range.MaxValue = ZTAntFuncHelper.getMaxValue(seriesValues) + 300;
            ((RadarDiagram)chartCellLine.Diagram).AxisX.GridSpacing = 20;
            ((RadarDiagram)chartCellLine.Diagram).AxisX.Range.SetInternalMinMaxValues(0, 340);
            ((RadarDiagram)chartCellLine.Diagram).RotationDirection = RadarDiagramRotationDirection.Clockwise;

            chartCellLine.Focus();
        }

        /// <summary>
        /// 按MR模拟覆盖图-基站级
        /// </summary>
        private void drawEnodeBCoverRadarSeries()
        {
            chartEnodebLine.Series.Clear();

            List<string> cellNameList = new List<string>();
            if (cbCellName.Text == "全部")
            {
                if (data != null && btsDic.ContainsKey(data.tdCellInfo.基站BTS名称))
                    cellNameList = btsDic[data.tdCellInfo.基站BTS名称];
            }
            else
                cellNameList.Add(cbCellName.Text);

            int idx = 0;
            double dMaxDist = 0;
            cellNameList.Sort();
            dealEnodeBCoverCellNameList(cellNameList, ref idx, ref dMaxDist);

            if (idx > 0)
            {
                ((RadarDiagram)chartEnodebLine.Diagram).AxisY.Range.MinValue = -1;
                ((RadarDiagram)chartEnodebLine.Diagram).AxisY.Range.MaxValue = dMaxDist + 300;
                ((RadarDiagram)chartEnodebLine.Diagram).AxisX.GridSpacing = 20;
                ((RadarDiagram)chartEnodebLine.Diagram).AxisX.Range.SetInternalMinMaxValues(0, 340);
                ((RadarDiagram)chartEnodebLine.Diagram).RotationDirection = RadarDiagramRotationDirection.Clockwise;
            }
            else
            {
                Series series = new Series();
                series.ShowInLegend = false;
                series.PointOptions.PointView = PointView.Values;

                RadarLineSeriesView lineSeriesView = new RadarLineSeriesView();
                lineSeriesView.Color = Color.Blue;
                lineSeriesView.LineMarkerOptions.Size = 2;

                series.View = lineSeriesView;
                series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
                series.Label.Visible = false;
                chartEnodebPoint.Series.Insert(0, series);
            }

            chartEnodebLine.Focus();
        }

        private void dealEnodeBCoverCellNameList(List<string> cellNameList, ref int idx, ref double dMaxDist)
        {
            foreach (string cellName in cellNameList)
            {
                if (DicCellParaData.ContainsKey(cellName))
                {
                    ZTTDAntMRAna.TdCellMRData tmpData = DicCellParaData[cellName];
                    int iDir = tmpData.tdCellInfo.天线方向角 < 0 ? 0 : tmpData.tdCellInfo.天线方向角;

                    Series series = new Series();
                    series.ShowInLegend = true;
                    series.LegendText = string.Format("CI={0}", tmpData.tdCellInfo.小区CI);
                    series.PointOptions.PointView = PointView.Values;

                    RadarLineSeriesView lineSeriesView = new RadarLineSeriesView();
                    lineSeriesView.Color = ZTAntFuncHelper.getCellColor(tmpData.tdCellInfo.小区CI);
                    lineSeriesView.LineMarkerOptions.Size = 2;

                    series.View = lineSeriesView;
                    series.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
                    series.Label.Visible = false;

                    string strPart = cbBtsPart.Text;
                    double[] seriesValues = getMaxTaArray(tmpData, strPart);

                    for (int i = 0; i < 72; i++)
                    {
                        int j = (i * 5 + iDir) % 360;
                        series.Points.Add(new SeriesPoint(j, seriesValues[i]));
                    }
                    if (dMaxDist < ZTAntFuncHelper.getMaxValue(seriesValues))
                        dMaxDist = ZTAntFuncHelper.getMaxValue(seriesValues);

                    chartEnodebLine.Series.Insert(idx, series);
                    idx++;
                }
            }
        }

        /// <summary>
        /// 添加MR测量项描述
        /// </summary>
        public  static string WriteMRDesc()
        {
            string strDesc = "";
            strDesc += "1.时间提前量\n";
            strDesc += "(1)定义：表示时间差异，其取值公式为：Tadv = TRX - TTX，其中TRX 表示：依照某个下行时隙的接收，计算得到的UE "+
                       "某个上行时隙的开始时间(子帧内时隙按照3GPP TS 25.221 6.1章所描述的帧结构)；TTX 表示UE上同样的上行时隙的开始时间。"+
                       "本测量数据表示OMC-R统计周期内满足取值范围按照分区间统计时间提前量的样本个数。\n";
            strDesc += "(2)取值范围：如0-4个chip一个区间，步长1/2chip；5-32个chip一个区间，步长1个chip；大于32个chip的1个区间小于0.5chip为一个区间，"+
                       "对应MR.TimingAdvance.00；4chip到小于5chip为一个区间，对应MR.TimingAdvance.08；大于32chip为一个区间，对应MR.TimingAdvance.36，依此类推。\n";
            strDesc += "(3)作用：该测量数据可用于确定UE距离基站的远近实现TD小区的覆盖分析，辅助P-CCPCH RSCP及话务统计等信息判断是否需要对小区天线做出调整，"+
                       "考察基站的覆盖区域是否合理，是否存在过覆盖和覆盖阴影区等问题，还可以利用TA辅助提供位置服务。。\n";
            strDesc += "\n";

            strDesc += "2.UE发射功率\n";
            strDesc += "(1)定义：特定时隙内的一个载波上的总的UE发射功率。本测量数据表示OMC-R统计周期内满足取值范围按照分区间统计UE发射功率的样本个数。\n";
            strDesc += "(2)取值范围：如-50dBm到小于-47dBm为一个区间，对应MR.UeTxPower.00；31dBm到小于34dBm为一个区间，对应MR.UeTxPower.27，依此类推。";
            strDesc += "(3)作用：该测量数据可用于UE发射功率、小区覆盖分析、盲区分析，进行网络覆盖质量分析。考量系统上、下行功率控制和功率预算切换的执行情况，"+
                       "以及基站或UE之间的互干扰情况。在不考虑强制减小覆盖的前提下，发射功率越小越好。\n";
            strDesc += "\n";

            strDesc += "3.P-CCPCH的接收信号码功率\n";
            strDesc += "(1)定义：反映UE收到服务小区的广播信道功率的大小，也是反映服务小区覆盖的主要指标。"+
                       "本测量数据表示OMC-R统计周期内满足取值范围条件的按照分区间统计UE接收信号码功率的样本个数。\n";
            strDesc += "(2)取值范围：如从-∞到-100dBm一个区间，对应MR.PccpchRscp.00；从-100dBm到-48dBm每1db一个区间，对应MR.PccpchRscp.01到MR.PccpchRscp.53；"+
                       "大于-47dBm一个区间，对应MR.PccpchRscp.54，依此类推。\n";
            strDesc += "(3)作用：该数据数据可用于评估TD小区的覆盖情况,根据不同场强区间分布比例可判断该小区的大致覆盖范围。天线遮挡及硬件故障会造成信号弱，"+
                       "容易产生掉话及降低接通率，用于检查小区覆盖盲点/弱覆盖区域，通过源小区和邻区PccpchRscp可进行导频污染分析。\n";
            strDesc += "\n";


            strDesc += "4.天线到达角\n";
            strDesc += "(1)定义：AOA定义了天线处以正北为初始方向的用户信号到达角度，一般每200ms测量一次。AOA是TD-SCDMA特有的测量数据。"+
                       "可以辅助确定用户所处的方位，提供TD特色定位服务，精度为1度。AOA定义一个用户关于参考方向的估计角度。测量参考方向应为正北，"+
                       "顺时针方向。本测量数据表示OMC-R统计周期内满足取值范围按照分区间统计天线到达角的样本个数。\n";
            strDesc += "(2)取值范围：如0度到小于5度为一个区间，对应MR.AOA.00；355度到小于360度为一个区间，对应MR.AOA.71，依此类推。\n";
            strDesc += "(3)作用：该测量数据可用于确定用户所处的方位、进行覆盖分析等。\n";
            strDesc += "\n";

            strDesc += "5.上行信噪比\n";
            strDesc += "(1)定义：SIR是反映信号质量最重要的指标，根据实际接收值与业务设定值的关系，可以判定出接收信号是否能够被正确的解码。"+
                       "同时在计算链路覆盖时，SIR是个重要的输入参数。信噪比定义为：(RSCP/Interference)xSF，其中：RSCP表示接收信号码功率，"+
                       "即特定DPCH或PDSCH码的接收功率。干扰表示同一时隙内接收信号中不能被接收器消除的干扰；SF表示所使用的扩频因子。\n";
            strDesc += "(2)取值范围：如小于-11dB为一个区间，对应MR.UtranSir.00；-11dB到小于-10.5dB为一个区间，对应MR.UtranSir.01；大于20dB为一个区间，对应MR.UtranSir.63，依此类推。\n";
            strDesc += "(3)作用：该测量数据可用于计算链路覆盖，判断接收信号质量。\n";
            strDesc += "\n";

            return strDesc;
        }
        #endregion

        #region 分级呈现图层
        /// <summary>
        /// 下拉选择框填值
        /// </summary>
        private void fillCheckBox(string strBtsName)
        {
            cbPart.Items.Clear();
            cbPart.Text = "全部";
            cbPart.Items.Add("全部");
            cbPart.Items.Add("(0%,50%]");
            cbPart.Items.Add("(0%,70%]");
            cbPart.Items.Add("(0%,80%]");

            cbNPart.Items.Clear();
            cbNPart.Text = "全部";
            cbNPart.Items.Add("全部");
            cbNPart.Items.Add("(0%,50%]");
            cbNPart.Items.Add("(0%,70%]");
            cbNPart.Items.Add("(0%,80%]");

            cbBtsPart.Items.Clear();
            cbBtsPart.Text = "全部";
            cbBtsPart.Items.Add("全部");
            cbBtsPart.Items.Add("(0%,50%]");
            cbBtsPart.Items.Add("(0%,70%]");
            cbBtsPart.Items.Add("(0%,80%]");

            cbCellName.Items.Clear();
            cbCellName.Text = "全部";
            cbCellName.Items.Add("全部");
            if (btsDic.ContainsKey(strBtsName))
            {
                List<string> cellNameList = btsDic[strBtsName];
                foreach (string cellName in cellNameList)
                {
                    cbCellName.Items.Add(cellName);
                }
            }
        }

        /// <summary>
        /// 重新计算覆盖曲线
        /// </summary>
        private double[] getMaxTaArray(ZTTDAntMRAna.TdCellMRData mrData, string cellPart)
        {
            List<ZTLteAntMRAna.AntMRAoaTa> mrList = new List<ZTLteAntMRAna.AntMRAoaTa>();
            foreach (int iColor in mrData.aoaTaDic.Keys)
            {
                if (iColor == 2 && cellPart == "(0%,50%]")
                    continue;

                if (iColor == 3 && (cellPart == "(0%,50%]" || cellPart == "(0%,70%]"))
                    continue;

                if (iColor == 4 && (cellPart == "(0%,50%]" || cellPart == "(0%,70%]" || cellPart == "(0%,80%]"))
                    continue;

                mrList.AddRange(mrData.aoaTaDic[iColor]);
            }

            int[,] AnaRttdAoaTmp = new int[44, 72];
            int iNum = mrList.Count;
            for (int i = 0; i < iNum; i++)
            {
                ZTLteAntMRAna.AntMRAoaTa at = mrList[i];
                AnaRttdAoaTmp[at.iTaId, at.iAoaId] = at.iCount;
            }
            ZTLteAntMRAna.AnaRttdAoaArray ary = new ZTLteAntMRAna.AnaRttdAoaArray();
            ary.AnaRttdAoa90 = AnaRttdAoaTmp;
            double[] maxTaArray = ZTLteAntMRAna.getDirMaxTa(ary);

            return maxTaArray;
        }

        private void cbPart_SelectedIndexChanged(object sender, EventArgs e)
        {
            drawCellMRRadarSeries();
            string cellPart = cbPart.Text;
            double[] maxTaArray = getMaxTaArray(data,cellPart);
            drawCellCoverRadarSeries(maxTaArray);
        }

        private void cbCellName_SelectedIndexChanged(object sender, EventArgs e)
        {
            indexChanged();
        }

        private void cbBtsPart_SelectedIndexChanged(object sender, EventArgs e)
        {
            indexChanged();
        }

        private void indexChanged()
        {
            drawEnodeBCoverRadarSeries();
            drawEnodeBMRRadarSeries();
        }

        #endregion

        #region 多小区GIS覆盖曲线绘制

        List<matchCellInfo> mcList = null;
        ColorComboBox cmb_Temp = null;
        //选择行index
        int iDataGridViewRow = 0;
        //选中单元格index
        int iDataGridViewCell = 5;
        private void macthCell(double MaxDistance)
        {
            mcList = new List<matchCellInfo>();
            int idx = 1;
            foreach (string strNCell in DicCellParaData.Keys)
            {
                double distance = MathFuncs.GetDistance(data.dLongitude, data.dLatitude, DicCellParaData[strNCell].dLongitude, DicCellParaData[strNCell].dLatitude);
                if (distance <= MaxDistance && data.cellname != strNCell)
                {
                    matchCellInfo mcInfo = new matchCellInfo();
                    mcInfo.iSN = idx;
                    mcInfo.strCellName = strNCell;
                    mcInfo.dDistance = Math.Round(distance, 2);
                    mcInfo.fDiffAngle = ZTAntFuncHelper.CalcAntDir(data.tdCellInfo.天线方向角, DicCellParaData[strNCell].tdCellInfo.天线方向角);
                    CellRelate info = new CellRelate();
                    info.DPCellLongitude = data.dLongitude;
                    info.DPCellLatitude = data.dLatitude;
                    info.FPCell_方位角 = data.antCfg.方向角;
                    info.DNCellLongitude = DicCellParaData[strNCell].dLongitude;
                    info.DNCellLatitude = DicCellParaData[strNCell].dLatitude;
                    info.FNCell_方位角 = DicCellParaData[strNCell].antCfg.方向角;
                    info.Flag = 1;
                    mcInfo.strRelation = info.JudgeCellRel(info);
                    mcList.Add(mcInfo);
                    idx++;
                }
            }
            setControl();
            foreach(matchCellInfo mcInfo in mcList)
            {
                DataGridViewRow row = new DataGridViewRow();
                List<object> objs = mcInfo.getRowObjectList();
                foreach (object obj in objs)
                {
                    DataGridViewTextBoxCell boxcell1 = new DataGridViewTextBoxCell();
                    boxcell1.Value = obj.ToString();
                    row.Cells.Add(boxcell1);
                }
                dataGridViewLeft.Rows.Add(row);
            }
            
        }
        #endregion

        #region 多小区设置控件操作
        private void btnOneIn_Click(object sender, EventArgs e)
        {
            if (dataGridViewLeft.SelectedRows.Count == 0)
            {
                MessageBox.Show("请选择小区。", "提示");
                return;
            }
            moveDataGridViewData(dataGridViewLeft, dataGridViewRight, 1, 1, 20);
        }

        private void btnAllIn_Click(object sender, EventArgs e)
        {
            if (dataGridViewLeft.Rows.Count > 0)
            {
                moveDataGridViewData(dataGridViewLeft, dataGridViewRight, 1, 2, 20);
            }
        }

        private void btnOneOut_Click(object sender, EventArgs e)
        {
            if (dataGridViewRight.SelectedRows.Count == 0)
            {
                MessageBox.Show("请选择小区。", "提示");
                return;
            }
            moveDataGridViewData(dataGridViewRight, dataGridViewLeft, 1, 1, 100);

        }

        private void btnAllOut_Click(object sender, EventArgs e)
        {
            if (dataGridViewRight.Rows.Count > 0)
            {
                moveDataGridViewData(dataGridViewRight, dataGridViewLeft, 1, 2, 100);
            }
        }

        private void serBtn_Click(object sender, EventArgs e)
        {
            double maxDis;
            if (DicCellParaData.Count == 0)
                return;
            if (!double.TryParse(tbDistance.Text.ToString(), out maxDis))
            {
                MessageBox.Show("不是有效数字");
                return;
            }
            if (maxDis > 5000)
            {
                maxDis = 500;
                tbDistance.Text = maxDis.ToString();
                MessageBox.Show("最大距离不能超过5000米");
            }
            macthCell(maxDis);
        }

        private void btnDraw_Click(object sender, EventArgs e)
        {
            string strPart = cbNPart.Text;
            Dictionary<string, List<LongLat>> cellLongLatDic = new Dictionary<string, List<LongLat>>();
            Dictionary<string, Color> cellColorDic = new Dictionary<string, Color>();
            int iRowNum = dataGridViewRight.Rows.Count;
            for (int i = 0; i < iRowNum; i++)
            {
                string strCellName = dataGridViewRight.Rows[i].Cells[1].Value.ToString();
                Color color = dataGridViewRight.Rows[i].Cells[5].Style.BackColor;
                if (!cellColorDic.ContainsKey(strCellName))
                    cellColorDic.Add(strCellName, color);
            }

            foreach (string strCellName in cellColorDic.Keys)
            {
                ZTTDAntMRAna.TdCellMRData mrData;
                if (!DicCellParaData.TryGetValue(strCellName, out mrData))
                    return;

                addCellLongLatDic(strPart, cellLongLatDic, strCellName, mrData);
            }

            string cellname = dataGridViewTdCell.SelectedRows[0].Tag as string;
            if (!DicCellParaData.TryGetValue(cellname, out data))
                return;

            setLayerData(strPart, cellLongLatDic, cellColorDic);
        }

        private void setLayerData(string strPart, Dictionary<string, List<LongLat>> cellLongLatDic, Dictionary<string, Color> cellColorDic)
        {
            if (data != null)
            {
                LongLat mainLongLat = getCellLongLat(data.cellname);

                double[] maxTaArray = getMaxTaArray(data, strPart);
                int iCount = maxTaArray.Length;
                List<LongLat> longLatList = new List<LongLat>();
                for (int i = 0; i < iCount; i++)
                {
                    int iAngle = i * 5;
                    int iDist = (int)maxTaArray[i];
                    LongLat tmpLongLat = ZTAntFuncHelper.calcPointX(iAngle, iDist, mainLongLat);
                    longLatList.Add(tmpLongLat);
                }
                cellLongLatDic.Add(data.cellname, longLatList);
                cellColorDic.Add(data.cellname, Color.DarkViolet);

                MainModel.SelectedCell = CellManager.GetInstance().GetCellByName(data.cellname);
                MainModel.MainForm.GetMapForm().GoToView(mainLongLat.fLongitude, mainLongLat.fLatitude);

                AntRegionLayer antLayer = mapForm.GetLayerBase(typeof(AntRegionLayer)) as AntRegionLayer;
                if (antLayer != null)
                {
                    antLayer.cellLongLatDic = cellLongLatDic;
                    antLayer.cellColorDic = cellColorDic;
                    antLayer.Invalidate();
                }
            }
        }

        private void addCellLongLatDic(string strPart, Dictionary<string, List<LongLat>> cellLongLatDic, string strCellName, ZTTDAntMRAna.TdCellMRData mrData)
        {
            if (mrData != null)
            {
                LongLat cellLongLat = getCellLongLat(strCellName);

                double[] maxTaArray = getMaxTaArray(mrData, strPart);
                int iCount = maxTaArray.Length;
                List<LongLat> longLatList = new List<LongLat>();
                for (int i = 0; i < iCount; i++)
                {
                    int iAngle = i * 5;
                    int iDist = (int)maxTaArray[i];
                    LongLat tmpLongLat = ZTAntFuncHelper.calcPointX(iAngle, iDist, cellLongLat);
                    longLatList.Add(tmpLongLat);
                }
                cellLongLatDic.Add(strCellName, longLatList);
            }
        }

        private LongLat getCellLongLat(string strCellName)
        {
            LongLat cellLongLat = new LongLat();
            LTECell lteCell = CellManager.GetInstance().GetLTECellLatest(strCellName);

            if (lteCell != null && lteCell.Antennas != null)
            {
                cellLongLat.fLongitude = (float)lteCell.Antennas[0].Longitude;
                cellLongLat.fLatitude = (float)lteCell.Antennas[0].Latitude;
            }
            else
            {
                cellLongLat.fLongitude = (float)data.dLongitude;
                cellLongLat.fLatitude = (float)data.dLatitude;
            }

            return cellLongLat;
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void setControl()
        {
            this.dataGridViewLeft.Columns.Clear();
            this.dataGridViewLeft.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewLeft.Columns.Add("序号", "序号");
            this.dataGridViewLeft.Columns.Add("小区名", "小区名");
            this.dataGridViewLeft.Columns.Add("距离", "距离");
            this.dataGridViewLeft.Columns.Add("夹角", "夹角");
            this.dataGridViewLeft.Columns.Add("关系", "关系");
            this.dataGridViewLeft.Columns[0].Width = 60;
            this.dataGridViewLeft.Columns[1].Width = 150;
            this.dataGridViewLeft.Columns[2].Width = 60;
            this.dataGridViewLeft.Columns[3].Width = 60;
            this.dataGridViewLeft.Columns[4].Width = 60;
            this.dataGridViewLeft.Tag = "dataGridViewLeft";

            this.dataGridViewRight.Columns.Clear();
            this.dataGridViewRight.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewRight.Columns.Add("序号", "序号");
            this.dataGridViewRight.Columns.Add("小区名", "小区名");
            this.dataGridViewRight.Columns.Add("距离", "距离");
            this.dataGridViewRight.Columns.Add("夹角", "夹角");
            this.dataGridViewRight.Columns.Add("关系", "关系");
            this.dataGridViewRight.Columns.Add("着色方案", "着色方案");
            this.dataGridViewRight.Columns[0].Width = 60;
            this.dataGridViewRight.Columns[1].Width = 150;
            this.dataGridViewRight.Columns[2].Width = 60;
            this.dataGridViewRight.Columns[3].Width = 60;
            this.dataGridViewRight.Columns[4].Width = 60;
            this.dataGridViewRight.Columns[5].Width = 100;
            this.dataGridViewRight.Tag = "dataGridViewRight";

            cmb_Temp = new ColorComboBox();
            cmb_Temp.Visible = false;
            cmb_Temp.SelectedValueChanged += new EventHandler((obj, even) =>
            {
                dataGridViewRight.Rows[iDataGridViewRow].Cells[iDataGridViewCell].Style.BackColor = cmb_Temp.SelectedColor;
            });
            this.dataGridViewRight.Controls.Add(cmb_Temp);
            #region 颜色设置
            listStaticColor = new List<Color>();
            listTmpColor = new List<Color>();
            listStaticColor.Add(Color.Red);
            listStaticColor.Add(Color.Orange);
            listStaticColor.Add(Color.Yellow);
            listStaticColor.Add(Color.GreenYellow);
            listStaticColor.Add(Color.Green);
            listStaticColor.Add(Color.SlateBlue);
            listStaticColor.Add(Color.Cyan);
            listStaticColor.Add(Color.Violet);
            listStaticColor.Add(Color.DeepPink);
            listStaticColor.Add(Color.BlueViolet);
            listStaticColor.Add(Color.Brown);
            listStaticColor.Add(Color.BurlyWood);
            listStaticColor.Add(Color.Blue);
            listStaticColor.Add(Color.DarkRed);
            listStaticColor.Add(Color.DarkOrchid);
            listStaticColor.Add(Color.DarkSeaGreen);
            listStaticColor.Add(Color.DarkSlateBlue);
            listStaticColor.Add(Color.DarkSlateGray);
            listStaticColor.Add(Color.DarkOliveGreen);
            listStaticColor.Add(Color.Black);
            listStaticColor.Add(Color.SteelBlue);
            #endregion
            listTmpColor.AddRange(listStaticColor);
        }

        /// <summary>
        /// dataGridView之间传值
        /// </summary>
        /// <param name="dgvSource">源dataGridView</param>
        /// <param name="dgvTarget">目标dataGridView</param>
        /// <param name="command">1.移动  2.复制</param>
        /// <param name="type">1.单个  2.全部</param>
        ///  <param name="iMax">目标 dataGridView 接收的最大值</param>
        private void moveDataGridViewData(DataGridView dgvSource, DataGridView dgvTarget, int command, int type ,int iMax)
        {
            if (mcList != null)
            {
                if (type == 1)
                {
                    if (dgvSource.SelectedRows.Count > 0)
                    {
                        string cellName = dgvSource.SelectedRows[0].Cells[1].Value.ToString();
                        foreach (matchCellInfo mcInfo in mcList)
                        {
                            if (mcInfo.strCellName == cellName)
                            {
                                if (dataGridViewRight.Rows.Count > iMax-1)
                                {
                                    MessageBox.Show("最多选择20个小区");
                                    break;
                                }
                                DataGridViewRow row = new DataGridViewRow();
                                List<object> objs = mcInfo.getRowObjectList();
                                foreach (object obj in objs)
                                {
                                    DataGridViewTextBoxCell boxcell1 = new DataGridViewTextBoxCell();
                                    boxcell1.Value = obj.ToString();
                                    row.Cells.Add(boxcell1);
                                }
                                dgvTarget.Rows.Add(row);

                                if (dgvTarget.Tag.ToString() == "dataGridViewRight")
                                {
                                    //设置默认色
                                    if (listTmpColor.Count == 0)
                                        listTmpColor.AddRange(listStaticColor);
                                    row.Cells[5].Style.BackColor = listTmpColor[0]; //getColor(indexColor);
                                    listTmpColor.RemoveAt(0);
                                }
                                if (dgvSource.Tag.ToString() == "dataGridViewRight")
                                {
                                    listTmpColor.Add(dgvSource.SelectedRows[0].Cells[5].Style.BackColor);
                                }
                                if (command == 1)
                                {
                                    dgvSource.Rows.Remove(dgvSource.SelectedRows[0]);
                                }
                            }
                        }
                        dgvSource.Refresh();
                        dgvTarget.Refresh();
                    }
                }
                else
                {
                    for (int iRow = dgvSource.Rows.Count-1; iRow >= 0; iRow--)
                    {
                        if (dataGridViewRight.Rows.Count > iMax-1)
                        {
                            MessageBox.Show("最多选择20个小区");
                            break;
                        }
                        if (dgvSource.Rows[iRow].Cells[1].Value == null)
                            continue;
                        string cellName = dgvSource.Rows[iRow].Cells[1].Value.ToString();
                        foreach (matchCellInfo mcInfo in mcList)
                        {
                            if (mcInfo.strCellName == cellName)
                            {
                                DataGridViewRow row = new DataGridViewRow();
                                List<object> objs = mcInfo.getRowObjectList();
                                foreach (object obj in objs)
                                {
                                    DataGridViewTextBoxCell boxcell1 = new DataGridViewTextBoxCell();
                                    boxcell1.Value = obj.ToString();
                                    row.Cells.Add(boxcell1);
                                }
                                dgvTarget.Rows.Add(row);

                                if (dgvTarget.Tag.ToString() == "dataGridViewRight")
                                {
                                    //设置默认色
                                    if (listTmpColor.Count == 0)
                                        listTmpColor.AddRange(listStaticColor);
                                    row.Cells[5].Style.BackColor = listTmpColor[0]; //getColor(indexColor);
                                    listTmpColor.RemoveAt(0);
                                }
                                if (dgvSource.Tag.ToString() == "dataGridViewRight")
                                {
                                    listTmpColor.Clear();
                                    listTmpColor.AddRange(listStaticColor);
                                }
                                if (command == 1)
                                {
                                    dgvSource.Rows.RemoveAt(iRow);
                                }
                            }
                        }
                    }
                    dgvSource.Refresh();
                    dgvTarget.Refresh();
                }
            }
        }

        private List<Color> listStaticColor = null;
        private List<Color> listTmpColor = null;

        private void dataGridViewRight_CurrentCellChanged(object sender, EventArgs e)
        {
            try
            {
                if (this.dataGridViewRight.CurrentCell.ColumnIndex == iDataGridViewCell)
                {
                    Rectangle rec = this.dataGridViewRight.GetCellDisplayRectangle(dataGridViewRight.CurrentCell.ColumnIndex, dataGridViewRight.CurrentCell.RowIndex, false);
                    cmb_Temp.Left = rec.Left;
                    cmb_Temp.Top = rec.Top;
                    cmb_Temp.Width = rec.Width;
                    cmb_Temp.Height = rec.Height;
                    cmb_Temp.Visible = true;
                    
                    iDataGridViewRow = dataGridViewRight.CurrentCell.RowIndex;
                }
                else
                {
                    cmb_Temp.Visible = false;
                }
            }
            catch
            {
                //continue
            }  
        }

        private void dataGridViewLeft_SortCompare(object sender, DataGridViewSortCompareEventArgs e)
        {
            if (e.Column.Name == "序号" || e.Column.Name == "距离" || e.Column.Name == "夹角")
            {
                if (Convert.ToDouble(e.CellValue1) - Convert.ToDouble(e.CellValue2) > 0)
                {
                    e.SortResult = 1;
                }
                else if (Convert.ToDouble(e.CellValue1) - Convert.ToDouble(e.CellValue2) < 0)
                {
                    e.SortResult = -1;
                }
                else
                {
                    e.SortResult = 0;
                }
            }
            else
            {
                e.SortResult = System.String.Compare(Convert.ToString(e.CellValue1), Convert.ToString(e.CellValue2));
            }
            e.Handled = true;
        }
        #endregion
    }
}
