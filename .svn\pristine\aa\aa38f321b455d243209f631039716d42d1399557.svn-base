﻿using System.Collections.Generic;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTTdScanAntenna : ZTTdAntenna
    {
        private static ZTTdScanAntenna instance = null;
        public new static ZTTdScanAntenna GetInstance()
        {
            if (instance == null)
            {
                lock (lockObj)
                {
                    if (instance == null)
                    {
                        instance = new ZTTdScanAntenna();
                    }
                }
            }
            return instance;
        }
        public ZTTdScanAntenna() : base()
        {
            init();
        }

        protected override void init()
        {
            isAddSampleToDTDataManager = false;
            ServiceTypes.Clear();
            ServiceTypes.Add(ServiceType.TD_SCAN);
            carrierID = CarrierType.ChinaMobile;
            if (Condition != null)
            {
                Condition.ServiceTypes.Clear();
                Condition.ServiceTypes.Add((int)ServiceType.TD_SCAN);
            }
        }

        public override string Name
        {
            get { return "TD天线分析_Scan"; }
        }

        protected override UserMng.LogInfoItem getRecLogItem()
        {
            return new UserMng.LogInfoItem(2, 16000, 16027, this.Name);
        }
        #region 统计流程
        protected override bool getConditionBeforeQuery()
        {
            ZTGsmAntenna.isScanStat = true;
            return true;
        }
        protected override DIYSampleGroup getCurSelDIYSampleGroupPrepare()
        {
            List<object> columnsDef = new List<object>();
            Dictionary<string, object> param = new Dictionary<string, object>();
            param["param_name"] = "TDS_PCCPCH_RSCP";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TDS_PCCPCH_Channel";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TDS_PCCPCH_CPI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TDS_PCCPCH_RSSI";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TDS_PCCPCH_C_I";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TDS_PCCPCH_Ec_Io";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            param = new Dictionary<string, object>();
            param["param_name"] = "TDS_PCCPCH_SIR";
            param["param_arg"] = 0;
            columnsDef.Add((object)param);

            Dictionary<string, object> tmpDic = new Dictionary<string, object>();
            tmpDic.Add("name", (object)">TD扫频");
            tmpDic.Add("themeName", (object)"TDSCAN_PCCPCH_RSCP");
            tmpDic.Add("columnsDef", (object)columnsDef);

            DIYSampleGroup group = new DIYSampleGroup();
            group.Param = tmpDic;
            return group;
        }

        protected override void doWithDTData(TestPoint tp)
        {
            try
            {
                if (mapOp2.CheckPointInRegion(tp.Longitude, tp.Latitude) && tp is ScanTestPoint_TD)
                {
                    dealTP(tp);
                }
            }
            catch (System.Exception ex)
            {
                log.Error(ex.Message);
            }
        }

        private void dealTP(TestPoint tp)
        {
            for (int i = 0; i < 30; i++)
            {
                if (tp["TDS_PCCPCH_Channel", i] == null || tp["TDS_PCCPCH_CPI", i] == null
                    || tp["TDS_PCCPCH_Channel", i].ToString() == "" || tp["TDS_PCCPCH_CPI", i].ToString() == "")
                {
                    return;
                }
                short uarfcn = (short)(int)tp["TDS_PCCPCH_Channel", i];
                byte cpi = (byte)(int)tp["TDS_PCCPCH_CPI", i];
                TDCell mainCell = CellManager.GetInstance().GetNearestTDCell(tp.DateTime
                    , uarfcn, (short)cpi, tp.Longitude, tp.Latitude);
                if (mainCell == null || mainCell.Direction > 360)
                {
                    return;
                }
                double dAngleDiffRel = 0;
                double dAngLeDiffAbs = 0;
                double utranCellDistance = 0;
                ZTAntFuncHelper.CellCollection col = new ZTAntFuncHelper.CellCollection()
                {
                    Cell = null,
                    TDCell = mainCell,
                    LteCell = null
                };
                ZTAntFuncHelper.calcSampleAngle(col, tp.Longitude, tp.Latitude
                    , out dAngleDiffRel, out dAngLeDiffAbs, out utranCellDistance);
                int iAngleDiffRel = (int)dAngleDiffRel;
                int iAngleDiffAbs = (int)dAngLeDiffAbs;

                if (utranCellDistance > CD.MAX_COV_DISTANCE_TD)
                    return;

                float? pccpchRscp = (float?)tp["TDS_PCCPCH_RSCP", i];
                if (judgeValid(pccpchRscp, -140, -10))
                    return;
                int? ppchc2i = (int?)(float?)tp["TDS_PCCPCH_C_I", i];
                if (judgeValid(ppchc2i, -24, 40))
                    return;
                float? ppch_ecio = (float?)tp["TDS_PCCPCH_Ec_Io", i];
                if (judgeValid(ppch_ecio, -24, 40))
                    return;

                CellAngleData utranCellAngleData = getCellAngleData(tp, mainCell);

                utranCellAngleData.calcValue((int)pccpchRscp, (int)ppchc2i);
                calcCellInfo(ref utranCellAngleData, pccpchRscp, (float?)-500, (int?)-500, ppchc2i
                    , (int?)-500, (int?)-500, utranCellDistance, 0, tp, iAngleDiffAbs, ppch_ecio);
                calcSectionInfo(ref utranCellAngleData, pccpchRscp, (float?)-500, (int?)-500, ppchc2i
                    , (int?)-500, (int?)-500, utranCellDistance, 0, iAngleDiffAbs, ppch_ecio);
                calcAngleInfo(ref utranCellAngleData, pccpchRscp, (float?)-500, (int?)-500, ppchc2i
                    , (int?)-500, (int?)-500, utranCellDistance, 0, iAngleDiffRel, ppch_ecio);
            }
        }

        private bool judgeValid(int? data, int min, int max)
        {
            if (data == null || data < min || data > max)
            {
                return false;
            }
            return true;
        }

        private bool judgeValid(float? data, int min,int max)
        {
            if (data == null || data < min || data > max)
            {
                return false;
            }
            return true;
        }

        private CellAngleData getCellAngleData(TestPoint tp, TDCell mainCell)
        {
            CellAngleData utranCellAngleData;
            if (dicUtranCellAngelData.ContainsKey(mainCell.Name))
                utranCellAngleData = dicUtranCellAngelData[mainCell.Name];
            else
            {
                utranCellAngleData = new CellAngleData();
                utranCellAngleData.tdCell = mainCell;
                utranCellAngleData.strbscname = mainCell.BelongBTS.BelongBSC.Name;
                utranCellAngleData.strbtsname = mainCell.BelongBTS.Name;
                utranCellAngleData.strTime = tp.DateTime.ToString("yyyy-MM-dd");
                string strGridTypeName = "";
                utranCellAngleData.strcityname = strCityTypeName;
                utranCellAngleData.strgridname = "";
                isContainPoint(mainCell.Longitude, mainCell.Latitude, ref strGridTypeName);
                if (strGridTypeName == "")
                    strGridTypeName = "无网格号";
                utranCellAngleData.strgridname = strGridTypeName;
                utranCellAngleData.cellname = mainCell.Name;
                utranCellAngleData.lac = mainCell.LAC;
                utranCellAngleData.ci = mainCell.CI;
                utranCellAngleData.uarfcn = mainCell.FREQ;
                utranCellAngleData.strCoverType = mainCell.BelongBTS.Type == TDNodeBType.Indoor ? "室内" : "室外";
                utranCellAngleData.anaType = "";
                utranCellAngleData.ialtitude = mainCell.Altitude;
                utranCellAngleData.iangle_dir = (int)(mainCell.Direction);
                utranCellAngleData.iangle_ob = (int)(mainCell.Downword);
                utranCellAngleData.cellLongitude = mainCell.Longitude;
                utranCellAngleData.cellLatitude = mainCell.Latitude;
                dicUtranCellAngelData[mainCell.Name] = utranCellAngleData;
            }
            utranCellAngleData.sampleTotal++;
            utranCellAngleData.tpList.Add(tp);
            return utranCellAngleData;
        }

        #endregion

        #region background处理
        public override BackgroundFuncType FuncType
        {
            get { return BackgroundFuncType.TD扫频; }
        }
        #endregion
    }
}
