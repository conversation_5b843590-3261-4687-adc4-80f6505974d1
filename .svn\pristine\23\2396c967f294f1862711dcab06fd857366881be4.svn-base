﻿using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Stat;
using MasterCom.Util;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace MasterCom.RAMS.ZTFunc.ZTNRDominantAreaAna
{
    public partial class NRDominantAreaAnaDlg : BaseDialog
    {
        Dictionary<string, Dictionary<string, NRDominantAreaSceneInfo>> sceneInfoDic;
        Dictionary<string, Dictionary<string, NRDominantAreaCellInfo>> sceneCellDic;
        Dictionary<string, Dictionary<string, ResvRegion>> sceneRegionDic;
        Dictionary<string, Dictionary<string, MapWinGIS.Shapefile>> roadRegionDic;

        public NRDominantAreaAnaDlg()
        {
            InitializeComponent();
            sceneInfoDic = new Dictionary<string, Dictionary<string, NRDominantAreaSceneInfo>>();
            sceneCellDic = new Dictionary<string, Dictionary<string, NRDominantAreaCellInfo>>();
            sceneRegionDic = new Dictionary<string, Dictionary<string, ResvRegion>>();
            roadRegionDic = new Dictionary<string, Dictionary<string, MapWinGIS.Shapefile>> ();

            chkLstDistrict.Items.Clear();
            foreach (IDNamePair item in DistrictManager.GetInstance().GetAvailableDistrict())
            {
                if (mainModel.User.DBID == -1)
                {
                    chkLstDistrict.Items.Add(item, false);
                }
                else if (item.id == mainModel.User.DBID)
                {
                    chkLstDistrict.Items.Add(item, false);
                    break;
                }
            }

            //从数据库加载场景类型关系
            //DiyQuerySceneInfo query = new DiyQuerySceneInfo();
            //query.Query();
            //sceneInfoList = query.SceneInfoList;
        }

        #region 获取条件
        public NRDominantAreaAnaCondition GetCondition()
        {
            NRDominantAreaAnaCondition cond = new NRDominantAreaAnaCondition();
            cond.SetCondition(dPSTime.Value, dPETime.Value, (int)numTimeThreshold.Value);
            foreach (IDNamePair chkItem in chkLstDistrict.CheckedItems)
            {
                cond.DistrictIDs.Add(chkItem.id);
            }

            cond.DistrictConfigDic = getDistrictConfigDic();

            cond.SceneExcelPath = txtPath.Text;
            cond.ShapePath = txtShpPath.Text;
            cond.RoadPath = txtRoadPath.Text;

            cond.TestPointRadio = (int)numTPRadio.Value;

            return cond;
        }

        private Dictionary<string, NRDominantAreaDistrictInfo> getDistrictConfigDic()
        {
            var districtDic = new Dictionary<string, NRDominantAreaDistrictInfo>();
            foreach (var district in sceneInfoDic)
            {
                if (!districtDic.TryGetValue(district.Key, out var districtInfo))
                {
                    districtInfo = new NRDominantAreaDistrictInfo();
                    districtInfo.DistrictName = district.Key;
                    districtDic.Add(district.Key, districtInfo);
                }

                if (roadRegionDic.TryGetValue(district.Key, out var roadNames))
                {
                    districtInfo.RoadShpNames = roadNames;
                }

                foreach (var scene in district.Value.Values)
                {
                    if (!districtInfo.SceneConfigDic.TryGetValue(scene.SceneName, out var info))
                    {
                        info = new NRDominantAreaConfigInfo();
                        districtInfo.SceneConfigDic.Add(scene.SceneName, info);
                    }

                    addNRDominantAreaConfigInfo(district.Key, scene, info);
                }
            }
            return districtDic;
        }

        private void addNRDominantAreaConfigInfo(string district, NRDominantAreaSceneInfo scene, NRDominantAreaConfigInfo info)
        {
            info.SceneInfo = scene;
            if (sceneCellDic.TryGetValue(district, out var sceneCell)
             && sceneCell.TryGetValue(scene.SceneName, out var cell))
            {
                info.SceneCell = cell;
            }

            if (sceneRegionDic.TryGetValue(district, out var sceneRegion)
                && sceneRegion.TryGetValue(scene.SceneName, out var region))
            {
                info.SceneRegion = region;
            }
        }
        #endregion

        #region 设置条件
        public void SetCondition(NRDominantAreaAnaCondition cond)
        {
            WaitBox.Show("正在加载配置信息...", loadConfigFile, cond);

            dPSTime.Value = cond.FilePeriod.BeginTime;
            dPETime.Value = cond.FilePeriod.EndTime;
            numTimeThreshold.Value = cond.TimeThreshold;
            if (cond.TestPointRadio > 0)
            {
                numTPRadio.Value = cond.TestPointRadio;
            }
            else
            {
                cond.TestPointRadio = (int)numTPRadio.Value;
            }

            for (int i = 0; i < chkLstDistrict.Items.Count; i++)
            {
                IDNamePair p = chkLstDistrict.Items[i] as IDNamePair;
                chkLstDistrict.SetItemChecked(i, cond.DistrictIDs.Contains(p.id));
            }
        }

        private void loadConfigFile(object obj)
        {
            try
            {
                txtPath.Text = "";
                txtShpPath.Text = "";
                txtRoadPath.Text = "";

                NRDominantAreaAnaCondition cond = obj as NRDominantAreaAnaCondition;
                readExcel(cond.SceneExcelPath);
                if (sceneInfoDic.Count > 0 && sceneCellDic.Count > 0)
                {
                    txtPath.Text = cond.SceneExcelPath;
                }

                getRegionByFile(cond.ShapePath);
                if (sceneRegionDic.Count > 0)
                {
                    txtShpPath.Text = cond.ShapePath;
                }

                addRoadRegionDic(cond.RoadPath);
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message + e.StackTrace);
            }
            finally
            {
                System.Threading.Thread.Sleep(200);
                WaitBox.Close();
            }
        }
        #endregion

        #region 读取Excel
        private void btnSearchPath_Click(object sender, EventArgs e)
        {
            OpenFileDialog openDlg = new OpenFileDialog();
            openDlg.Filter = "Excel|*.xlsx;*.xls";
            openDlg.Title = "请选择场景类型关系表";
            if (openDlg.ShowDialog() == DialogResult.OK)
            {
                bool isValid = false;
                try
                {
                     isValid = readExcel(openDlg.FileName);
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message + ex.StackTrace);
                }

                if (isValid)
                {
                    txtPath.Text = openDlg.FileName;
                }
            }
        }

        private bool readExcel(string fileName)
        {
            if (string.IsNullOrEmpty(fileName) || !File.Exists(fileName))
            {
                return false;
            }

            WaitBox.ProgressPercent = 10;
            using (DataSet ds = ExcelNPOIManager.ImportFromExcel(fileName))
            {
                if (ds == null || ds.Tables == null || ds.Tables.Count < 3)
                {
                    throw (new Exception("Excel中缺少数据或读取失败！格式请参见模板"));
                }

                sceneInfoDic.Clear();
                sceneCellDic.Clear();

                DataTable senceTB = ds.Tables[0];
                loadSenceConfig(senceTB);
                WaitBox.ProgressPercent += 20;

                DataTable nrCellTB = ds.Tables[1];
                loadNRCellConfig(nrCellTB);
                WaitBox.ProgressPercent += 20;

                DataTable LteCellTB = ds.Tables[2];
                loadLTECellConfig(LteCellTB);
                WaitBox.ProgressPercent += 20;
            }
            return true;
        }

        private void loadSenceConfig(DataTable senceTB)
        {
            try
            {
                foreach (DataRow dr in senceTB.Rows)
                {
                    NRDominantAreaSceneInfo sceneInfo = new NRDominantAreaSceneInfo();
                    sceneInfo.FillDataByExcel(dr);

                    if (!sceneInfoDic.TryGetValue(sceneInfo.DistrictName, out Dictionary<string, NRDominantAreaSceneInfo> dic))
                    {
                        dic = new Dictionary<string, NRDominantAreaSceneInfo>();
                        sceneInfoDic[sceneInfo.DistrictName] = dic;
                    }
                    dic[sceneInfo.SceneName] = sceneInfo;
                }
                if (sceneInfoDic.Count == 0)
                {
                    throw (new Exception("场景sheet无数据"));
                }
            }
            catch
            {
                throw (new Exception("加载场景sheet出错"));
            }
        }

        private void loadNRCellConfig(DataTable nrCellTB)
        {
            try
            {
                foreach (DataRow dr in nrCellTB.Rows)
                {
                    string districtName = dr["地市"].ToString();
                    string sceneName = dr["场景名称"].ToString();
                    string cgi = dr["CGI"].ToString();

                    if (!sceneCellDic.TryGetValue(districtName, out Dictionary<string, NRDominantAreaCellInfo> dic))
                    {
                        dic = new Dictionary<string, NRDominantAreaCellInfo>();
                        sceneCellDic[districtName] = dic;
                    }
                    if (!dic.TryGetValue(sceneName, out NRDominantAreaCellInfo cellInfo))
                    {
                        cellInfo = new NRDominantAreaCellInfo();
                        dic.Add(sceneName, cellInfo);
                    }
                    if (!cellInfo.NrCellDic.ContainsKey(cgi))
                    {
                        cellInfo.NrCellDic.Add(cgi, null);
                    }
                }

                if (sceneCellDic.Count == 0)
                {
                    throw (new Exception("NR小区sheet无数据"));
                }
            }
            catch
            {
                throw (new Exception("加载NR小区sheet出错"));
            }
        }

        private void loadLTECellConfig(DataTable LteCellTB)
        {
            try
            {
                foreach (DataRow dr in LteCellTB.Rows)
                {
                    string districtName = dr["地市"].ToString();
                    string sceneName = dr["场景名称"].ToString();
                    string cgi = dr["CGI"].ToString();

                    if (!sceneCellDic.TryGetValue(districtName, out Dictionary<string, NRDominantAreaCellInfo> dic))
                    {
                        dic = new Dictionary<string, NRDominantAreaCellInfo>();
                        sceneCellDic[districtName] = dic;
                    }
                    if (!dic.TryGetValue(sceneName, out NRDominantAreaCellInfo cellInfo))
                    {
                        cellInfo = new NRDominantAreaCellInfo();
                        dic.Add(sceneName, cellInfo);
                    }
                    if (!cellInfo.LteCellDic.ContainsKey(cgi))
                    {
                        cellInfo.LteCellDic.Add(cgi, null);
                    }
                }

                if (sceneCellDic.Count == 0)
                {
                    throw (new Exception("LTE小区sheet无数据"));
                }
            }
            catch
            {
                throw (new Exception("加载LTE小区sheet出错"));
            }
        }
        #endregion

        #region 下载模板
        private void btnDownLoad_Click(object sender, EventArgs e)
        {
            List<ExportToExcelModel> lsData = new List<ExportToExcelModel>();
            initSenceModel(lsData);

            initNrCellModel(lsData);

            initLteCellModel(lsData);

            ExcelNPOIManager.ExportToExcelMore(lsData);
        }

        private static void initSenceModel(List<ExportToExcelModel> lsData)
        {
            ExportToExcelModel sheet = new ExportToExcelModel();
            sheet.SheetName = "场景类型关系表";

            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow nr = new NPOIRow();
            nr.AddCellValue("地市");
            nr.AddCellValue("区县");
            nr.AddCellValue("场景名称");
            nr.AddCellValue("场景类型");
            rows.Add(nr);

            nr = new NPOIRow();
            nr.AddCellValue("广安");
            nr.AddCellValue("广安区");
            nr.AddCellValue("中共广安市委员会");
            nr.AddCellValue("室内");
            rows.Add(nr);

            sheet.Data = rows;
            lsData.Add(sheet);
        }

        private static void initNrCellModel(List<ExportToExcelModel> lsData)
        {
            ExportToExcelModel sheet = new ExportToExcelModel();
            sheet.SheetName = "NR小区表";

            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow nr = new NPOIRow();
            nr.AddCellValue("地市");
            nr.AddCellValue("场景名称");
            nr.AddCellValue("CGI");
            rows.Add(nr);

            nr = new NPOIRow();
            nr.AddCellValue("广安");
            nr.AddCellValue("中共广安市委员会");
            nr.AddCellValue("460-00-9450607-1");
            rows.Add(nr);

            sheet.Data = rows;
            lsData.Add(sheet);
        }

        private static void initLteCellModel(List<ExportToExcelModel> lsData)
        {
            ExportToExcelModel sheet = new ExportToExcelModel();
            sheet.SheetName = "LTE小区表";

            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow nr = new NPOIRow();
            nr.AddCellValue("地市");
            nr.AddCellValue("场景名称");
            nr.AddCellValue("CGI");
            rows.Add(nr);

            nr = new NPOIRow();
            nr.AddCellValue("广安");
            nr.AddCellValue("中共广安市委员会");
            nr.AddCellValue("460-00-955994-23");
            rows.Add(nr);

            sheet.Data = rows;
            lsData.Add(sheet);
        }
        #endregion

        //private void btnImport_Click(object sender, EventArgs e)
        //{
        //    string file = txtPath.Text;
        //    if (!File.Exists(file))
        //    {
        //        MessageBox.Show($"文件[{file}不存在！]", "提示");
        //        return;
        //    }

        //    WaitBox.Show("正在读取并导入场景类型关系信息...", ImportSceneInfo, file);
        //}

        //private void ImportSceneInfo(object obj)
        //{
        //    //读取Excel
        //    List<NRDominantAreaSceneInfo> importSceneInfolist = readExcel(obj);
        //    //插入到数据库
        //    DiyInsertSceneInfo query = new DiyInsertSceneInfo(importSceneInfolist);
        //    query.Query();

        //    sceneInfoList = importSceneInfolist;
        //    WaitBox.Close();
        //}

        //delegate void Func<in T>(T content, DataRow dr);

        //private T readExcel<T>(string fileName, T content, Func<T> func)
        //     where T : new()
        //{
        //    WaitBox.ProgressPercent = 10;
        //    DataSet ds = ExcelNPOIManager.ImportFromExcel(fileName);
        //    if (ds == null || ds.Tables == null || ds.Tables.Count == 0)
        //    {
        //        MessageBox.Show("Excel中没有数据或读取失败！");
        //        return content;
        //    }

        //    try
        //    {
        //        foreach (DataTable dt in ds.Tables)
        //        {
        //            WaitBox.Text = "正在读取Excel信息..." + dt.TableName;
        //            WaitBox.ProgressPercent += 40;
        //            foreach (DataRow dr in dt.Rows)
        //            {
        //                func(content, dr);
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        MessageBox.Show(ex.Message + Environment.NewLine + ex.Source + Environment.NewLine + ex.StackTrace);
        //        return new T();
        //    }
        //    return content;
        //}

        #region 读取图层文件
        private void btnShp_Click(object sender, EventArgs e)
        {
            OpenFileDialog openDlg = new OpenFileDialog();
            openDlg.Filter = "Shape|*.shp";
            openDlg.Title = "请选择5G优势区域图层";
            if (openDlg.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    getRegionByFile(openDlg.FileName);
                    txtShpPath.Text = openDlg.FileName;
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                }
            }
        }

        private void getRegionByFile(string fileName)
        {
            if (string.IsNullOrEmpty(fileName) || !File.Exists(fileName))
            {
                return;
            }

            sceneRegionDic.Clear();
            string fileNameWithoutType = getWithoutTypeName(fileName);
            MapWinGIS.Shapefile table = new MapWinGIS.Shapefile();
            try
            {
                if (!table.Open(fileName, null))
                {
                    return;
                }

                getShapeFromFile(sceneRegionDic, fileNameWithoutType, table);
                if (sceneRegionDic.Count == 0)
                {
                    throw (new Exception("图层无效"));
                }
            }
            catch
            {
                sceneRegionDic.Clear();
                throw (new Exception($"加载图层[{fileName}]出错"));
            }
            finally
            {
                table.Close();
            }
        }

        private void getShapeFromFile(Dictionary<string, Dictionary<string, ResvRegion>> districtSceneNameDic, string typeName, MapWinGIS.Shapefile table)
        {
            string district = "地市";
            string scene = "场景名称";

            int districtIndex = MTGis.MapOperation.GetColumnFieldIndex(table, district);
            int sceneIndex = MTGis.MapOperation.GetColumnFieldIndex(table, scene);
            for (int i = 0; i < table.NumShapes; i++)
            {
                MapWinGIS.Shape geome = table.get_Shape(i);
                if (geome == null)
                {
                    continue;
                }

                string curDistrictName = table.get_CellValue(districtIndex, i).ToString();
                if (!string.IsNullOrEmpty(curDistrictName))
                {
                    Dictionary<string, ResvRegion> sceneDic;
                    if (!districtSceneNameDic.TryGetValue(curDistrictName, out sceneDic))
                    {
                        sceneDic = new Dictionary<string, ResvRegion>();
                        districtSceneNameDic.Add(curDistrictName, sceneDic);
                    }

                    string sceneName = table.get_CellValue(sceneIndex, i).ToString();
                    if (!string.IsNullOrEmpty(sceneName))
                    {
                        addValidRegion(sceneDic, typeName, geome, sceneName);
                    }
                }
            }
        }

        private static void addValidRegion(Dictionary<string, ResvRegion> regionDic, string typeName, MapWinGIS.Shape geome, string sceneName)
        {
            if (geome.ShapeType == MapWinGIS.ShpfileType.SHP_POLYGON
                || geome.ShapeType == MapWinGIS.ShpfileType.SHP_POLYGONZ
                || geome.ShapeType == MapWinGIS.ShpfileType.SHP_POLYLINE
                || geome.ShapeType == MapWinGIS.ShpfileType.SHP_POLYLINEZ)
            {
                ResvRegion region;
                if (!regionDic.TryGetValue(sceneName, out region))
                {
                    region = new ResvRegion();
                    region.RootNodeName = typeName;
                    region.RegionName = sceneName;
                    region.Shape = geome;
                    regionDic.Add(sceneName, region);
                }
                else
                {
                    MapWinGIS.Shape tempShp = region.Shape.Clip(geome, MapWinGIS.tkClipOperation.clUnion);
                    if (tempShp != null)
                    {
                        regionDic[sceneName].Shape = tempShp;
                    }
                }
            }
        }

        private string getWithoutTypeName(string fileName)
        {
            string fileNameWithoutType = fileName;
            if (fileNameWithoutType.LastIndexOf('.') >= 0)
            {
                fileNameWithoutType = fileNameWithoutType.Substring(0, fileNameWithoutType.LastIndexOf('.'));
            }
            return fileNameWithoutType;
        }
        #endregion

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (sceneInfoDic.Count == 0)
            {
                MessageBox.Show("场景信息为空,请选择有效的Excel文件...");
                return;
            }
            if (sceneCellDic.Count == 0)
            {
                MessageBox.Show("小区信息为空,请选择有效的Excel文件...");
                return;
            }
            if (sceneRegionDic.Count == 0)
            {
                MessageBox.Show("场景图层信息为空,请选择有效的图层文件...");
                return;
            }

            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void chkAllDistrict_CheckedChanged(object sender, EventArgs e)
        {
            for (int i = 0; i < chkLstDistrict.Items.Count; i++)
            {
                chkLstDistrict.SetItemChecked(i, chkAllDistrict.Checked);
            }
        }

        private void btnRoad_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog folderDlg = new FolderBrowserDialog();
            if (folderDlg.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            string path = folderDlg.SelectedPath;
            addRoadRegionDic(path);
        }

        #region 加载道路图层文件路径
        private void addRoadRegionDic(string path)
        {
            roadRegionDic.Clear();
            if (string.IsNullOrEmpty(path) || !Directory.Exists(path))
            {
                return;
            }
            DirectoryInfo dir = new DirectoryInfo(path);
            DirectoryInfo[] dis = dir.GetDirectories();
            //结构 : 选择的文件夹/地市文件夹/道路图层.shp
            foreach (DirectoryInfo diInfo in dis)
            {
                System.IO.FileInfo[] fis = diInfo.GetFiles("*.shp");
                foreach (System.IO.FileInfo fi in fis)
                {
                    if (!roadRegionDic.TryGetValue(diInfo.Name, out Dictionary<string, MapWinGIS.Shapefile> filePathList))
                    {
                        filePathList = new Dictionary<string, MapWinGIS.Shapefile>();
                        roadRegionDic.Add(diInfo.Name, filePathList);
                    }
                    filePathList.Add(fi.FullName, null);
                }
            }
            if (roadRegionDic.Count > 0)
            {
                txtRoadPath.Text = path;
            }
        }
        #endregion

        private void btnRevert_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show(this, "确认重置条件信息吗?", "确认", MessageBoxButtons.OKCancel) == DialogResult.OK)
            {
                NRDominantAreaAnaCondition cond = new NRDominantAreaAnaCondition();
                SetCondition(cond);
            }
        }
    }
}

