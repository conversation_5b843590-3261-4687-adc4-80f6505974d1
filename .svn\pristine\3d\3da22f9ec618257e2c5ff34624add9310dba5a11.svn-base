﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;
using MasterCom.Util;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;
using MasterCom.RAMS.Func.CoverageCheck;
using MasterCom.RAMS.Grid;
using MasterCom.MTGis;
using MasterCom.Util.UiEx;
using MapWinGIS;

namespace MasterCom.RAMS
{
    public partial class GDCPUnitShowForm : MinCloseForm
    {
        private QueryCondition condition;
        private QueryCondition conditionTPHost;
        private QueryCondition condtionTPGuest;
        private MainModel mainModel;
        protected MapFormItemSelection ItemSelection;
        private CompareMode compareConfig;

        public GDCPUnitShowForm(MainModel mainModel, MapFormItemSelection ItemSelection, QueryCondition condition,
            QueryCondition conditionTPHost, QueryCondition conditionTPGuest) : base(mainModel)
        {
            InitializeComponent();
            listView.ListViewItemSorter = new ListViewSorter(listView);
            this.mainModel = mainModel;
            this.ItemSelection = ItemSelection;
            this.condition = condition;
            this.conditionTPHost = conditionTPHost;
            this.condtionTPGuest = conditionTPGuest;
            compareConfig = new CompareMode(Application.StartupPath + @"\config\comparemode.xml");
            compareConfig.loadConfig();
            freshCbxCompareMode();
            initUI();
            btnMutCityAna.Visible = mainModel.User.DBID == -1;
        }

        private void initUI()
        {
            colItemName.AspectGetter += delegate (object row)
            {
                if (row is CompareResult)
                {
                    return "全部";
                }
                else if (row is CompareGridLevel)
                {
                    return (row as CompareGridLevel).LevelName;
                }
                return null;
            };

            colRoadsCenterPntDesc.AspectGetter += delegate (object row)
            {
                if (row is CompareGridBlock)
                {
                    return (row as CompareGridBlock).RoadsCenterPntDesc;
                }
                return null;
            };

            colLng.AspectGetter += delegate (object row)
            {
                if (row is CompareResultGrid)
                {
                    return (row as CompareResultGrid).CenterLng;
                }
                return null;
            };

            colLat.AspectGetter += delegate (object row)
            {
                if (row is CompareResultGrid)
                {
                    return (row as CompareResultGrid).CenterLat;
                }
                return null;
            };

            colGridCnt.AspectGetter += delegate (object row)
            {
                if (row is CompareGridLevel)
                {
                    return (row as CompareGridLevel).GridCnt;
                }
                else if (row is CompareGridBlock)
                {
                    return (row as CompareGridBlock).GridCnt;
                }
                return null;
            };

            setColGuestValue();

            setColHostValue();

            olvColHostGuestDiff.AspectGetter += delegate (object row)
            {
                if (row is CompareResultGrid)
                {
                    return (row as CompareResultGrid).HMGValue;
                }
                return null;
            };

            setColHostDistance();

            setColGuestDistance();

            lvDetails.CanExpandGetter += delegate (object row)
            {
                if (row is CompareResult || row is CompareGridLevel || row is CompareGridBlock)
                {
                    return true;
                }
                return false;
            };

            setLvDetails();
        }

        private void setColGuestValue()
        {
            colGuestValueAvg.AspectGetter += delegate (object row)
            {
                if (row is CompareGridBlock)
                {
                    double v = (row as CompareGridBlock).GuestValueAvg;
                    return getValidData(v);
                }
                else if (row is CompareResultGrid)
                {
                    return (row as CompareResultGrid).ValueGuest;
                }
                return null;
            };

            colGuestValueMax.AspectGetter += delegate (object row)
            {
                if (row is CompareGridBlock)
                {
                    double v = (row as CompareGridBlock).GuestValueMax;
                    return getValidData(v);
                }
                else if (row is CompareResultGrid)
                {
                    return (row as CompareResultGrid).ValueGuest;
                }
                return null;
            };

            colGuestValueMin.AspectGetter += delegate (object row)
            {
                if (row is CompareGridBlock)
                {
                    double v = (row as CompareGridBlock).GuestValueMin;
                    return getValidData(v);
                }
                else if (row is CompareResultGrid)
                {
                    return (row as CompareResultGrid).ValueGuest;
                }
                return null;
            };
        }

        private void setColHostValue()
        {
            colHostValueAvg.AspectGetter += delegate (object row)
            {
                if (row is CompareGridBlock)
                {
                    double v = (row as CompareGridBlock).HostValueAvg;
                    return getValidData(v);
                }
                else if (row is CompareResultGrid)
                {
                    return (row as CompareResultGrid).ValueHost;
                }
                return null;
            };

            colHostValueMax.AspectGetter += delegate (object row)
            {
                if (row is CompareGridBlock)
                {
                    double v = (row as CompareGridBlock).HostValueMax;
                    return getValidData(v);
                }
                else if (row is CompareResultGrid)
                {
                    return (row as CompareResultGrid).ValueHost;
                }
                return null;
            };

            colHostValueMin.AspectGetter += delegate (object row)
            {
                if (row is CompareGridBlock)
                {
                    double v = (row as CompareGridBlock).HostValueMin;
                    return getValidData(v);
                }
                else if (row is CompareResultGrid)
                {
                    return (row as CompareResultGrid).ValueHost;
                }
                return null;
            };
        }

        private void setColHostDistance()
        {
            colHostDistance.AspectGetter += delegate (object row)
            {
                if (row is CompareGridLevel)
                {
                    double v = (row as CompareGridLevel).HostDistance;
                    return getValidData(v);
                }
                else if (row is CompareGridBlock)
                {
                    double v = (row as CompareGridBlock).HostDistance;
                    return getValidData(v);
                }
                else if (row is CompareResultGrid)
                {
                    double v = (row as CompareResultGrid).HostDistance;
                    return getValidData(v);
                }
                return null;
            };
        }

        private void setColGuestDistance()
        {
            colGuestDistance.AspectGetter += delegate (object row)
            {
                if (row is CompareGridLevel)
                {
                    double v = (row as CompareGridLevel).GuestDistance;
                    return getValidData(v);
                }
                else if (row is CompareGridBlock)
                {
                    double v = (row as CompareGridBlock).GuestDistance;
                    return getValidData(v);
                }
                else if (row is CompareResultGrid)
                {
                    double v = (row as CompareResultGrid).GuestDistance;
                    return getValidData(v);
                }
                return null;
            };
        }

        private void setLvDetails()
        {
            lvDetails.ChildrenGetter = delegate (object row)
            {
                if (row is CompareResult)
                {
                    return (row as CompareResult).FilteredLevelBlocks;
                }
                else if (row is CompareGridLevel)
                {
                    return (row as CompareGridLevel).FilteredBlocks;
                }
                else if (row is CompareGridBlock)
                {
                    return (row as CompareGridBlock).Grids;
                }
                return null;
            };
        }

        private static object getValidData(double v)
        {
            if (double.IsNaN(v))
            {
                return "-";
            }
            else
            {
                return v;
            }
        }

        private void freshCbxCompareMode()
        {
            cbxCompareMode.Items.Clear();
            List<CompareParam> compareParamList = compareConfig.CompareConfigList;
            foreach (CompareParam param in compareParamList)
            {
                cbxCompareMode.Items.Add(param);
            }
            if (cbxCompareMode.Items.Count > 0)
            {
                cbxCompareMode.SelectedIndex = 0;
            }
        }

        private void tsbEdit_Click(object sender, EventArgs e)
        {
            try
            {
                CompareMode compareConfigTemp = new CompareMode(Application.StartupPath + @"\config\comparemode.xml");
                compareConfigTemp.Param = compareConfig.Param;

                CPModeEditForm cpModeEditForm = new CPModeEditForm(ItemSelection);
                cpModeEditForm.FillData(compareConfigTemp);
                if (DialogResult.OK == cpModeEditForm.ShowDialog())
                {
                    compareConfig = cpModeEditForm.compareConfig;
                    freshCbxCompareMode();
                }
            }
            catch
            {
                //continue
            }
        }

        private CompareResult compareResult = null;
        private void btnFreshVSType_Click(object sender, EventArgs e)
        {
            if (cbxCompareMode.SelectedItem == null)
            {
                MessageBox.Show("尚未选择竞对模式", "提示");
                return;
            }
            CompareParam cpParam = cbxCompareMode.SelectedItem as CompareParam;
            if (cpParam.AlgorithmCfg.colorItemList.Count <= 0 &&
                cpParam.AlgorithmCfg.bothStandardList.Count <= 0)
            {
                MessageBox.Show("此竞对模式尚未设置竞对算法,请设置", "提示");
            }
            StartAnalysisData(cpParam, chkRoadName.Checked);
        }

        public void StartAnalysisData(CompareParam compareTemplate, bool isChkRoad)
        {
            chkRoadName.Checked = isChkRoad;
            CompareParam cpParam = compareTemplate;
            clear();
            doSearch(cpParam);

            WaitTextBox.Show(doCompare, cpParam);

            listView.Items.Clear();
            if (regionCPResultDic.Count <= 0)
            {
                numCombineMinCnt.Enabled = false;
                MessageBox.Show("主队客队均未发现栅格数据", "提示");
                return;
            }
            numCombineMinCnt.Enabled = true;
            if (chkCombine.Checked)
            {
                foreach (CompareResultArea cpRegion in regionCPResultDic.Values)
                {
                    cpRegion.Combine((int)numCombineMinCnt.Value, cpParam, isChkRoad, cbxDisplayMode.SelectedItem);
                }
            }
            fillLayer();
            fillComboxRegion();
            mainModel.RefreshLegend();
        }

        private void fillComboxRegion()
        {
            cbxEditRegion.Properties.Items.Clear();
            foreach (CompareResultArea cpRegion in regionCPResultDic.Values)
            {
                if (cpRegion.AreaRegion.Equals("全部汇总"))
                    cbxEditRegion.Properties.Items.Insert(0, cpRegion);
                else
                    cbxEditRegion.Properties.Items.Add(cpRegion);
            }
            if (cbxEditRegion.Properties.Items.Count > 0)
            {
                cbxEditRegion.SelectedIndex = 0;
            }
        }

        private void fillLayer()
        {
            MapForm mf = MainModel.MainForm.GetMapForm();
            if (mf != null)
            {
                MasterCom.MTGis.CustomDrawLayer cLayer = mf.GetCustomLayer(typeof(CPGridUnitLayer));
                if (cLayer == null)
                {
                    layer = new CPGridUnitLayer(mf.GetMapOperation(), "栅格对比图层");
                    mf.AddTempCustomLayer(layer);
                }
                else
                {
                    layer = cLayer as CPGridUnitLayer;
                }
            }
            layer.Invalidate();
            layer.FillData(compareResult);
        }
        CPGridUnitLayer layer = null;

        private void fillLvDetails()
        {
            lvDetails.Items.Clear();
            if (compareResult == null) return;
            List<CompareResult> dataSrc = new List<CompareResult>();
            dataSrc.Add(compareResult);
            lvDetails.BeginUpdate();
            try
            {
                lvDetails.ClearObjects();
            }
            catch
            {
                //continue
            }
            lvDetails.SetObjects(dataSrc);
            lvDetails.RefreshObjects(dataSrc);
            foreach (CompareGridLevel level in compareResult.FilteredLevelBlocks)
            {
                lvDetails.RefreshObject(level);
                lvDetails.RefreshObjects(level.FilteredBlocks);
                foreach (CompareGridBlock blk in level.FilteredBlocks)
                {
                    lvDetails.RefreshObject(blk);
                    lvDetails.RefreshObjects(blk.Grids);
                }
            }
            lvDetails.ExpandAll();
            lvDetails.EndUpdate();
        }

        private void clear()
        {
            cbxEditRegion.Properties.Items.Clear();
            cbxEditRegion.ResetText();
            compareResult = null;
            regionCPResultDic.Clear();
            listView.Items.Clear();
            lvDetails.Items.Clear();
        }

        private void doSearch(CompareParam cpParam)
        {
            QueryCondition conditionHost;
            QueryCondition conditionGuest;
            getCondition(cpParam, out conditionHost, out conditionGuest);
            searchHostFormula(cpParam, conditionHost);
            searchGuestFormula(cpParam, conditionGuest);
        }
        GridCountInfo gridCountInfo = null;
        Dictionary<string, CompareResultArea> regionCPResultDic = new Dictionary<string, CompareResultArea>();
        private void doCompare(object o)
        {
            gridCountInfo = new GridCountInfo();
            gridCountInfo.IHostGridCount = hostGridMatrix.Grids.Count;
            gridCountInfo.IGuestGridCount = guestGridMatrix.Grids.Count;
            List<CenterLongLat> gridCenterLongLat = new List<CenterLongLat>();
            CompareParam cpParam = o as CompareParam;

            Dictionary<string, MapOperation2> resvMopDic = RegionMop.GetResvRegionMop();
            dealHostGridMatrix(gridCenterLongLat, cpParam, resvMopDic);
            dealGuestGridMatrix(gridCenterLongLat, cpParam, resvMopDic);
            gridCountInfo.IAllGridCount = gridCenterLongLat.Count;
            gridCenterLongLat.Clear();
            System.Threading.Thread.Sleep(100);
            WaitTextBox.Close();
        }

        private void dealHostGridMatrix(List<CenterLongLat> gridCenterLongLat, CompareParam cpParam, Dictionary<string, MapOperation2> resvMopDic)
        {
            foreach (GridFormula gridHost in hostGridMatrix)
            {
                CenterLongLat ccl = new CenterLongLat(gridHost.CenterLng, gridHost.CenterLat);
                if (!gridCenterLongLat.Contains(ccl))
                {
                    gridCenterLongLat.Add(ccl);
                }
                string regionName = RegionMop.GetResvRegionName(resvMopDic, new DbPoint(gridHost.CenterLng, gridHost.CenterLat));
                if (regionName != null)
                {
                    GridFormula gridGuest = guestGridMatrix[gridHost.RowIdx, gridHost.ColIdx];
                    double dHost = double.NaN;
                    double dGuest = double.NaN;
                    TextColorRange rtColor = cpParam.GetTextColorRange(gridHost, gridGuest, ref dHost, ref dGuest);
                    if (rtColor != null && rtColor.description == CPModeEditForm.GUESTNULL)
                    {
                        CompareResultGrid info = new CompareResultGrid();
                        info.HostDistance = getValidValue(dHost, double.NaN, Math.Round(gridHost.formulaValueDic["0806"], 2));
                        info.GuestDistance = getValidValue(dGuest, double.NaN, Math.Round(gridGuest.formulaValueDic["0806"], 2));
                        info.FillData(gridHost, rtColor, getValidValue(dHost, dGuest, "-", Convert.ToString(dHost - dGuest)),
                             getValidValue(dHost, "-", Convert.ToString(dHost)), getValidValue(dGuest, "-", Convert.ToString(dGuest)));
                        addCPResult(regionName, rtColor.description, info);
                        addCPResult("全部汇总", rtColor.description, info);
                    }
                }
            }
        }

        private double getValidValue(double value, double result1, double result2)
        {
            if (value.Equals(double.NaN))
            {
                return result1;
            }
            else
            {
                return result2;
            }
        }

        private string getValidValue(double value, string result1, string result2)
        {
            if (double.IsNaN(value))
            {
                return result1;
            }
            else
            {
                return result2;
            }
        }

        private string getValidValue(double value1,double value2, string result1, string result2)
        {
            if (double.IsNaN(value1) || double.IsNaN(value2))
            {
                return result1;
            }
            else
            {
                return result2;
            }
        }

        private void dealGuestGridMatrix(List<CenterLongLat> gridCenterLongLat, CompareParam cpParam, Dictionary<string, MapOperation2> resvMopDic)
        {
            foreach (GridFormula gridGuest in guestGridMatrix)
            {
                CenterLongLat ccl = new CenterLongLat(gridGuest.CenterLng, gridGuest.CenterLat);
                if (!gridCenterLongLat.Contains(ccl))
                {
                    gridCenterLongLat.Add(ccl);
                }
                string regionName = RegionMop.GetResvRegionName(resvMopDic, new DbPoint(gridGuest.CenterLng, gridGuest.CenterLat));
                if (regionName != null)
                {
                    GridFormula gridHost = hostGridMatrix[gridGuest.RowIdx, gridGuest.ColIdx];

                    double dHost = double.NaN;
                    double dGuest = double.NaN;
                    TextColorRange rtColor = cpParam.GetTextColorRange(gridHost, gridGuest, ref dHost, ref dGuest);
                    if (rtColor != null && rtColor.description == CPModeEditForm.GUESTNULL)
                    {
                        if (!cpParam.AlgorithmCfg.specialColorList.Contains(rtColor))
                            gridCountInfo.ICompareGridCount++;

                        CompareResultGrid info = new CompareResultGrid();
                        info.HostDistance = getValidValue(dHost, double.NaN, Math.Round(gridHost.formulaValueDic["0806"], 2));
                        info.GuestDistance = getValidValue(dGuest, double.NaN, Math.Round(gridGuest.formulaValueDic["0806"], 2));
                        info.FillData(gridGuest, rtColor, getValidValue(dHost, dGuest, "-", Convert.ToString(dHost - dGuest)),
                             getValidValue(dHost, "-", Convert.ToString(dHost)), getValidValue(dGuest, "-", Convert.ToString(dGuest)));
                        addCPResult(regionName, rtColor.description, info);
                        addCPResult("全部汇总", rtColor.description, info);
                    }
                }
            }
        }

        private void addCPResult(string regionName, string level, CompareResultGrid cpResult)
        {
            if (!regionCPResultDic.ContainsKey(regionName))
            {
                regionCPResultDic.Add(regionName, new CompareResultArea(regionName));
            }
            regionCPResultDic[regionName].AddCompareResultGrid(level, cpResult);
        }

        private void fireListView(CompareResultArea cpRegion)
        {
            listView.Items.Clear();
            foreach (string level in cpRegion.CompareResultGridDic.Keys)
            {
                string[] array = new string[7];
                array[0] = level;
                array[1] = cpRegion.CompareResultGridDic[level].Count.ToString();
                array[2] = Math.Round(100.0 * cpRegion.CompareResultGridDic[level].Count / cpRegion.CpResultGridCount, 2).ToString();
                array[3] = gridCountInfo.IHostGridCount.ToString();
                array[4] = gridCountInfo.IGuestGridCount.ToString();
                array[5] = gridCountInfo.IAllGridCount.ToString();
                array[6] = gridCountInfo.ICompareGridCount.ToString();
                ListViewItem lvi = new ListViewItem(array);
                lvi.Tag = cpRegion.CompareResultGridDic[level];
                listView.Items.Add(lvi);
            }
        }

        private void getCondition(CompareParam param, out QueryCondition conditionHost, out QueryCondition conditionGuest)
        {
            initHostCondition(param, out conditionHost);
            initGuestCondition(param, out conditionGuest);
        }

        private void initHostCondition(CompareParam param, out QueryCondition conditionHost)
        {
            conditionHost = new QueryCondition();
            conditionHost.AgentIds = condition.AgentIds;
            conditionHost.Areas = condition.Areas;
            conditionHost.DistrictID = condition.DistrictID;
            conditionHost.DistrictIDs = condition.DistrictIDs;
            conditionHost.EventIDs = condition.EventIDs;
            conditionHost.FileInfos = condition.FileInfos;
            conditionHost.FileName = conditionTPHost.FileName;
            conditionHost.FileNameOrNum = conditionTPHost.FileNameOrNum;
            conditionHost.NameFilterType = condition.NameFilterType;
            conditionHost.Geometorys = condition.Geometorys;
            conditionHost.IsAllAgent = condition.IsAllAgent;
            conditionHost.QueryType = condition.QueryType;

            conditionHost.Periods.Clear();
            conditionHost.Periods = conditionTPHost.Periods;
            conditionHost.Projects.Clear();
            foreach (int projID in conditionTPHost.Projects)
            {
                conditionHost.Projects.Add(projID);
            }
            conditionHost.ServiceTypes.Clear();
            foreach (int servID in param.serviceList_A)
            {
                conditionHost.ServiceTypes.Add(servID);
            }
            conditionHost.CarrierTypes.Clear();
            conditionHost.CarrierTypes.Add(param.carrier_A);
        }

        private void initGuestCondition(CompareParam param, out QueryCondition conditionGuest)
        {
            conditionGuest = new QueryCondition();
            conditionGuest.AgentIds = condition.AgentIds;
            conditionGuest.Areas = condition.Areas;
            conditionGuest.DistrictID = condition.DistrictID;
            conditionGuest.DistrictIDs = condition.DistrictIDs;
            conditionGuest.EventIDs = condition.EventIDs;
            conditionGuest.FileInfos = condition.FileInfos;
            conditionGuest.FileName = condtionTPGuest.FileName;
            conditionGuest.FileNameOrNum = condtionTPGuest.FileNameOrNum;
            conditionGuest.NameFilterType = condition.NameFilterType;
            conditionGuest.Geometorys = condition.Geometorys;
            conditionGuest.IsAllAgent = condition.IsAllAgent;
            conditionGuest.QueryType = condition.QueryType;

            conditionGuest.Periods.Clear();
            conditionGuest.Periods = condtionTPGuest.Periods;
            conditionGuest.Projects.Clear();
            foreach (int projID in condtionTPGuest.Projects)
            {
                conditionGuest.Projects.Add(projID);
            }
            conditionGuest.ServiceTypes.Clear();
            foreach (int servID in param.serviceList_B)
            {
                conditionGuest.ServiceTypes.Add(servID);
            }
            conditionGuest.CarrierTypes.Clear();
            conditionGuest.CarrierTypes.Add(param.carrier_B);
        }

        GridMatrix<GridFormula> hostGridMatrix;
        private void searchHostFormula(CompareParam param, QueryCondition conditionHost)
        {
            DIYQueryFormulaInGridByRegion queryHost = new DIYQueryFormulaInGridByRegion(mainModel);
            queryHost.setQueryFormulas(new List<string>(), param.formula_A);
            queryHost.SetQueryCondition(conditionHost);
            queryHost.Query();
            hostGridMatrix = queryHost.formulaGridMatrix;
        }

        GridMatrix<GridFormula> guestGridMatrix;
        private void searchGuestFormula(CompareParam param, QueryCondition conditionGuest)
        {
            DIYQueryFormulaInGridByRegion queryGuest = new DIYQueryFormulaInGridByRegion(mainModel);
            queryGuest.setQueryFormulas(new List<string>(), param.formula_B);
            queryGuest.SetQueryCondition(conditionGuest);
            queryGuest.Query();
            guestGridMatrix = queryGuest.formulaGridMatrix;
        }

        private void listView_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (listView.SelectedItems.Count <= 0)
            {
                return;
            }
            List<CompareResultGrid> gcrInfoList = listView.SelectedItems[0].Tag as List<CompareResultGrid>;
            mainModel.MainForm.GetMapForm().GoToView(gcrInfoList[0].LTLng, gcrInfoList[0].LTLat, 1800);
        }

        private void numCombineMinCnt_ValueChanged(object sender, EventArgs e)
        {
            if (compareResult==null)
            {
                return;
            }
            compareResult.Filter((int)numCombineMinCnt.Value,cbxCompareMode.SelectedItem);
            fillLvDetails();
            layer.Invalidate();
        }

        private void btnExport_Click(object sender, EventArgs e)
        {
            lvDetails.ExpandAll();
            if (lvDetails.Items.Count > 65535)
            {
                TxtExporter.Export(lvDetails, true);
            }
            else
            {
                ExcelNPOIManager.ExportToExcel(lvDetails);
            }
        }

        private void lvDetails_DoubleClick(object sender, EventArgs e)
        {
            if (layer==null)
            {
                return;
            }
            object row = lvDetails.SelectedObject;
            if (row is CompareGridBlock)
            {
                mainModel.MainForm.GetMapForm().GoToView(((row as CompareGridBlock).GetBounds()));
            }
            else if (row is CompareResultGrid)
            {
                CompareResultGrid g = row as CompareResultGrid;
                mainModel.MainForm.GetMapForm().GoToView(g.CenterLng, g.CenterLat, 6000);
            }
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            lvDetails.ExpandAll();
        }

        private void miCollapseAll_Click(object sender, EventArgs e)
        {
            lvDetails.CollapseAll();
        }

        private void btnExportShp_Click(object sender, EventArgs e)
        {
            if (compareResult==null)
            {
                return;
            }
            SaveFileDialog saveFileDlg = new SaveFileDialog();
            saveFileDlg.Filter = FilterHelper.Shp;
            saveFileDlg.FilterIndex = 1;
            saveFileDlg.RestoreDirectory = true;
            saveFileDlg.Title = "保存shp图层文件";
            if (saveFileDlg.ShowDialog()==DialogResult.OK)
            {
                exportShpFile(saveFileDlg.FileName);
            }
        }

        public void exportShpFile(string filename)
        {
            Shapefile shpFile = new Shapefile();
            try
            {
                bool result = shpFile.CreateNewWithShapeID("", MapWinGIS.ShpfileType.SHP_POLYGON);
                if (!result)
                {
                    System.Windows.Forms.MessageBox.Show(shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                    return;
                }
                int idIdx = 0;
                int fiColor = idIdx++;
                int fiLongitude = idIdx++;
                int fiLatitude = idIdx++;
                int fiValue = idIdx++;
                int fiHost = idIdx++;
                int fiGuest = idIdx++;
                int fiDes = idIdx++;
                int fiDisHost = idIdx++;
                int fiDisGuest = idIdx;
                ShapeHelper.InsertNewField(shpFile, "Color", FieldType.INTEGER_FIELD, 10, 30, ref fiColor);
                ShapeHelper.InsertNewField(shpFile, "中心经度", FieldType.DOUBLE_FIELD, 10, 30, ref fiLongitude);
                ShapeHelper.InsertNewField(shpFile, "中心纬度", FieldType.DOUBLE_FIELD, 10, 30, ref fiLatitude);
                ShapeHelper.InsertNewField(shpFile, "Value", FieldType.STRING_FIELD, 10, 30, ref fiValue);
                ShapeHelper.InsertNewField(shpFile, "ValueHost", FieldType.STRING_FIELD, 10, 30, ref fiHost);
                ShapeHelper.InsertNewField(shpFile, "ValueGuest", FieldType.STRING_FIELD, 10, 30, ref fiGuest);
                ShapeHelper.InsertNewField(shpFile, "描述", FieldType.STRING_FIELD, 10, 30, ref fiDes);
                ShapeHelper.InsertNewField(shpFile, "DistanceHost", FieldType.DOUBLE_FIELD, 10, 30, ref fiDisHost);
                ShapeHelper.InsertNewField(shpFile, "DistanceGuest", FieldType.DOUBLE_FIELD, 10, 30, ref fiDisGuest);
                int numShp = 0;
                foreach (CompareGridLevel levelBlk in compareResult.FilteredLevelBlocks)
                {
                    foreach (CompareGridBlock blk in levelBlk.FilteredBlocks)
                    {
                        foreach (CompareResultGrid grid in blk.Grids)
                        {
                            shpFile.EditInsertShape(ShapeHelper.CreateRectShape(grid.LTLng, grid.LTLat, grid.BRLng, grid.BRLat), ref numShp);
                            shpFile.EditCellValue(fiColor, numShp, ColorTranslator.ToOle(levelBlk.TextColorRange.color));
                            shpFile.EditCellValue(fiLongitude, numShp,grid.CenterLng);
                            shpFile.EditCellValue(fiLatitude, numShp, grid.CenterLat);
                            shpFile.EditCellValue(fiValue, numShp, grid.HMGValue);
                            shpFile.EditCellValue(fiHost, numShp, grid.ValueHost);
                            shpFile.EditCellValue(fiGuest, numShp, grid.ValueGuest);
                            shpFile.EditCellValue(fiDes, numShp, levelBlk.TextColorRange.description);
                            shpFile.EditCellValue(fiDisHost, numShp, grid.HostDistance);
                            shpFile.EditCellValue(fiDisGuest, numShp, grid.GuestDistance);
                        }
                    }
                }
                ShapeHelper.DeleteShpFile(filename);
                if (!shpFile.SaveAs(filename, null))
                {
                    MessageBox.Show("保存文件失败！" + shpFile.get_ErrorMsg(shpFile.LastErrorCode));
                }
            }
            catch (System.Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
            finally
            {
                shpFile.Close();
            }
        }

        private void btnExportBrief_Click(object sender, EventArgs e)
        {
            if (compareResult != null)
            {
                lvDetails.Expand(compareResult);
                foreach (CompareGridLevel level in compareResult.FilteredLevelBlocks)
                {
                    lvDetails.Expand(level);
                    foreach (CompareGridBlock blk in level.FilteredBlocks)
                    {
                        lvDetails.Collapse(blk);
                    }
                }
            }
            if (lvDetails.Items.Count > 65535)
            {
                TxtExporter.Export(lvDetails, true);
            }
            else
            {
                ExcelNPOIManager.ExportToExcel(lvDetails);
            }
        }

        private void btnExportBriefTxt_Click(object sender, EventArgs e)
        {
            if (compareResult != null)
            {
                lvDetails.Expand(compareResult);
                foreach (CompareGridLevel level in compareResult.FilteredLevelBlocks)
                {
                    lvDetails.Expand(level);
                    foreach (CompareGridBlock blk in level.FilteredBlocks)
                    {
                        lvDetails.Collapse(blk);
                    }
                }
            }
            TxtExporter.Export(lvDetails, true);
        }

        private void btnExport2Txt_Click(object sender, EventArgs e)
        {
            lvDetails.ExpandAll();
            TxtExporter.Export(lvDetails, true);
        }

        private void cbxCompareMode_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cbxCompareMode.SelectedItem is CompareParam)
            {
                fillDisplayItem(cbxCompareMode.SelectedItem as CompareParam);
            }
        }

        private void fillDisplayItem(CompareParam mode)
        {
            cbxDisplayMode.Items.Clear();
            cbxDisplayMode.Items.Add("全部");
            foreach (TextColorRange range in mode.AlgorithmCfg.specialColorList)
            {
                cbxDisplayMode.Items.Add(range);
            }
            foreach (CPModeColorItem item in mode.AlgorithmCfg.colorItemList)
            {
                cbxDisplayMode.Items.Add(item.colorRange);
            }
            foreach (CPModeColorItem item in mode.AlgorithmCfg.bothStandardList)
            {
                cbxDisplayMode.Items.Add(item.colorRange);
            }
            if (cbxDisplayMode.Items.Count > 0)
            {
                cbxDisplayMode.SelectedIndex = 0;
            }
        }

        private void cbxDisplayMode_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (compareResult == null)
            {
                return;
            }
            compareResult.Filter((int)numCombineMinCnt.Value, cbxDisplayMode.SelectedItem);
            fillLvDetails();
            layer.FillData(compareResult);
            layer.Invalidate();
        }
        
        private void cbxEditRegion_SelectedIndexChanged(object sender, EventArgs e)
        {
            CompareResultArea cpRegion = cbxEditRegion.SelectedItem as CompareResultArea;
            if (cpRegion == null) return;
            fireListView(cpRegion);
            compareResult = cpRegion.CompareResultCombinded;
            cbxDisplayMode_SelectedIndexChanged(null, null);
        }

        private void ToolStripMenuItemExportCurrent_Click(object sender, EventArgs e)
        {
            try
            {
                ExcelNPOIManager.ExportToExcel(listView);
            }
            catch
            {
                MessageBox.Show("导出到Excel失败...", "提示");
            }
        }

        private void ToolStripMenuItemExportAll_Click(object sender, EventArgs e)
        {
            try
            {
                List<NPOIRow> rowList = new List<NPOIRow>();
                NPOIRow row = new NPOIRow();
                for (int idx = 0; idx < listView.Columns.Count; idx++ )
                {
                    row.AddCellValue(listView.Columns[idx].Text);
                }
                rowList.Add(row);
                int num = 1;
                foreach (string region in regionCPResultDic.Keys)
                {
                    if (region.Equals("全部汇总")) continue;
                    if(num != 1)
                        rowList.Add(new NPOIRow());
                    num++;
                    row = new NPOIRow();
                    row.AddCellValue(region);
                    rowList.Add(row);
                    foreach (string level in regionCPResultDic[region].CompareResultGridDic.Keys)
                    {
                        row = new NPOIRow();
                        row.AddCellValue(level);
                        row.AddCellValue(regionCPResultDic[region].CompareResultGridDic[level].Count);
                        row.AddCellValue(Math.Round(100.0 * regionCPResultDic[region].CompareResultGridDic[level].Count / regionCPResultDic[region].CpResultGridCount, 2));
                        rowList.Add(row);
                    }
                }
                if (regionCPResultDic.ContainsKey("全部汇总"))
                {
                    string region = "全部汇总";
                    rowList.Add(new NPOIRow());
                    row = new NPOIRow();
                    row.AddCellValue(region);
                    rowList.Add(row);
                    foreach (string level in regionCPResultDic[region].CompareResultGridDic.Keys)
                    {
                        row = new NPOIRow();
                        row.AddCellValue(level);
                        row.AddCellValue(regionCPResultDic[region].CompareResultGridDic[level].Count);
                        row.AddCellValue(Math.Round(100.0 * regionCPResultDic[region].CompareResultGridDic[level].Count / regionCPResultDic[region].CpResultGridCount, 2));
                        rowList.Add(row);
                    }
                }
                ExcelNPOIManager.ExportToExcel(rowList);
            }
            catch
            {
                MessageBox.Show("导出到Excel失败...", "提示");
            }
        }

        private void contextMenuStripWhole_Opening(object sender, CancelEventArgs e)
        {
            ToolStripMenuItemExportCurrent.Enabled = listView.Items.Count > 0;
            ToolStripMenuItemExportAll.Enabled = regionCPResultDic.Count > 0;
        }

        public static Dictionary<string, Dictionary<string, MapOperation2>> mutRegionMopDic { get; set; }
        /// <summary>
        /// 获取预存或圈选的区域
        /// </summary>
        private void InitRegionMop2()
        {
            GDCPUnitShowForm.mutRegionMopDic = new Dictionary<string,Dictionary<string,MapOperation2>>();
            Dictionary<string, List<ResvRegion>> resvRegionsDic = MainModel.SearchGeometrys.SelectedResvRegionDic;
            MapWinGIS.Shape gmt = MainModel.SearchGeometrys.Region;
            if (resvRegionsDic != null && resvRegionsDic.Count > 0)
            {
                foreach (string strGridType in resvRegionsDic.Keys)
                {
                    addRegionMop(resvRegionsDic, strGridType);
                }
            }
            else if (gmt != null)//单个区域
            {
                Dictionary<string, MapOperation2> regionMopDic =
                    new Dictionary<string, MapOperation2>();
                MapOperation2 mapOp2 = new MapOperation2();
                mapOp2.FillPolygon(gmt);
                regionMopDic.Add("当前区域", mapOp2);
                GDCPUnitShowForm.mutRegionMopDic.Add("无网格类型", regionMopDic);
            }
        }

        private static void addRegionMop(Dictionary<string, List<ResvRegion>> resvRegionsDic, string strGridType)
        {
            if (!GDCPUnitShowForm.mutRegionMopDic.ContainsKey(strGridType))
            {
                Dictionary<string, MapOperation2> regionMop = new Dictionary<string, MapOperation2>();
                foreach (ResvRegion region in resvRegionsDic[strGridType])
                {
                    if (!regionMop.ContainsKey(region.RegionName))
                    {
                        MapOperation2 mapOp2 = new MapOperation2();
                        mapOp2.FillPolygon(region.Shape);
                        regionMop.Add(region.RegionName, mapOp2);
                    }
                }
                GDCPUnitShowForm.mutRegionMopDic.Add(strGridType, regionMop);
            }
        }

        /// <summary>
        /// 定位所在网格
        /// </summary>
        public static bool isContainDbRect(DbRect dRect)
        {
            foreach (string gridType in GDCPUnitShowForm.mutRegionMopDic.Keys)
            {
                foreach (string grid in GDCPUnitShowForm.mutRegionMopDic[gridType].Keys)
                {
                    if (!(gridType + grid).Contains(GDCPUnitShowForm.StrCityName))
                    {
                        continue;
                    }
                    if (mutRegionMopDic[gridType][grid].CheckRectIntersectWithRegion(dRect))
                    {
                        return true;
                    }
                }
            }
            return false;
        }
        public static string StrCityName { get; set; } = "";
        private void btnMutCityAna_Click(object sender, EventArgs e)
        {
            if (cbxCompareMode.SelectedItem == null)
            {
                MessageBox.Show("尚未选择竞对模式", "提示");
                return;
            }
            FolderBrowserDialog dlg = new FolderBrowserDialog();
            if (dlg.ShowDialog() != DialogResult.OK)
            {
                MessageBox.Show("尚未选择结果保存路径", "提示");
                return;
            }
            CompareParam cpParam = cbxCompareMode.SelectedItem as CompareParam;
            if (cpParam.AlgorithmCfg.colorItemList.Count <= 0 &&
                cpParam.AlgorithmCfg.bothStandardList.Count <= 0)
            {
                MessageBox.Show("此竞对模式尚未设置竞对算法,请设置", "提示");
            }
            
            InitRegionMop2();
            int iCityId = mainModel.DistrictID;
            foreach (int id in condition.DistrictIDs)
            {
                clear();
                hostGridMatrix = new GridMatrix<GridFormula>();
                guestGridMatrix = new GridMatrix<GridFormula>();
                mainModel.DistrictID = id;
                condition.DistrictID = id;
                GDCPUnitShowForm.StrCityName = DistrictManager.GetInstance().getDistrictName(id);
                doSearch(cpParam);
                bool isDown = compareCity(cpParam);
                if(!isDown)
                {
                    listView.Items.Clear();
                    anaRegion(dlg, cpParam);
                }
            }
            mainModel.DistrictID = iCityId;
            condition.DistrictID = iCityId;
            GDCPUnitShowForm.StrCityName = "";
            GDCPUnitShowForm.mutRegionMopDic = null;
        }

        private bool compareCity(CompareParam cpParam)
        {
            try
            {
                WaitTextBox.Show(doCompare, cpParam);
            }
            catch
            {
                return false;
            }
            return true;
        }

        private void anaRegion(FolderBrowserDialog dlg, CompareParam cpParam)
        {
            if (regionCPResultDic.Count > 0)
            {
                numCombineMinCnt.Enabled = true;
                if (chkCombine.Checked)
                {
                    foreach (CompareResultArea cpRegion in regionCPResultDic.Values)
                    {
                        cpRegion.Combine(1, cpParam, chkRoadName.Checked, cbxDisplayMode.SelectedItem);
                    }
                }
                fillLayer();
                fillComboxRegion();
                mainModel.RefreshLegend();
                string strCity = DistrictManager.GetInstance().getDistrictName(mainModel.DistrictID);
                ExcelNPOIManager.ExportToExcel(listView, false, string.Format(dlg.SelectedPath + "\\" + strCity + "_栅格竞对分析_汇总_" + DateTime.Now.ToString("yyyyMMdd") + ".xlsx"), "栅格竞对分析");
                if (chkCombine.Checked)
                {
                    lvDetails.ExpandAll();
                    ExcelNPOIManager.ExportToExcel(lvDetails, false, string.Format(dlg.SelectedPath + "\\" + strCity + "_栅格竞对分析_详情_" + DateTime.Now.ToString("yyyyMMdd") + ".xlsx"), "栅格竞对分析");
                }
            }
        }
    }
}
