﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Frame;
using MasterCom.RAMS.Model;
using Microsoft.Office.Interop.Excel;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;

namespace MasterCom.RAMS.Func
{
    public partial class FindContainCellsFrm : MinCloseForm
    {
        private List<RegionCellsInfo> allCellsInfos;
        private List<RegionCellsInfo> cellsOf2GInfos;
        private List<RegionCellsInfo> cellsOf3GInfos;
        private List<RegionCellsInfo> cellsOf4GInfos;
        private List<RegionCellsInfo> cellsOf5GInfos;
        List<RegionCellsInfo> curCellInfos;
        List<RegionBTSsInfo> allBTSsInfos;
        List<RegionBTSsInfo> btssOf2GInfos;
        List<RegionBTSsInfo> btssOf3GInfos;
        List<RegionBTSsInfo> btssOf4GInfos;
        List<RegionBTSsInfo> btssOf5GInfos;
        List<RegionBTSsInfo> curBTSInfos;
        public FindContainCellsFrm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
            initTLV();
        }
        private void initTLV()
        {
            this.olvColumnRegion.AspectGetter = delegate (object row)
            {
                if (row is RegionCellsInfo)
                {
                    RegionCellsInfo item = row as RegionCellsInfo;
                    return item.regionName;
                }
                else
                {
                    return "";
                }
            };
            this.olvColumnArea.AspectGetter += delegate (object row)
            {
                if (row is RegionCellsInfo)
                {
                    RegionCellsInfo item = row as RegionCellsInfo;
                    return item.Area;
                }
                return string.Empty;
            };
            this.colArea.AspectGetter += delegate (object row)
            {
                if (row is RegionBTSsInfo)
                {
                    RegionBTSsInfo item = row as RegionBTSsInfo;
                    return item.Area;
                }
                return string.Empty;
            };
            initCell();
            this.olvColumnCellCount.AspectGetter = delegate (object row)
            {
                if (row is RegionCellsInfo)
                {
                    RegionCellsInfo item = row as RegionCellsInfo;
                    return item.cellInfos.Count;
                }
                else
                {
                    return "";
                }
            };
            initCellInfo();

            this.tlvCellsInfo.CanExpandGetter = delegate (object o)
            {
                return o is RegionCellsInfo;
            };
            this.tlvCellsInfo.ChildrenGetter = delegate (object o)
            {
                RegionCellsInfo info = o as RegionCellsInfo;
                return info.cellInfos;
            };

            this.olvColumnRegionBTS.AspectGetter = delegate (object row)
            {
                if (row is RegionBTSsInfo)
                {
                    RegionBTSsInfo item = row as RegionBTSsInfo;
                    return item.regionName;
                }
                else
                {
                    return "";
                }
            };
            this.olvColumnBTSCount.AspectGetter = delegate (object row)
            {
                if (row is RegionBTSsInfo)
                {
                    RegionBTSsInfo item = row as RegionBTSsInfo;
                    return item.btsInfos.Count;
                }
                else
                {
                    return "";
                }
            };
            this.olvColumnBTSName.AspectGetter = delegate (object row)
            {
                if (row is BTSInfo)
                {
                    BTSInfo bts = row as BTSInfo;
                    return bts.BTSName;
                }
                else
                {
                    return "";
                }
            };
            initBTS();
            initBTSInfo();
            this.lvBTS.CanExpandGetter = delegate (object o)
            {
                return o is RegionBTSsInfo;
            };
            this.lvBTS.ChildrenGetter = delegate (object o)
            {
                RegionBTSsInfo info = o as RegionBTSsInfo;
                return info.btsInfos;
            };
        }

        private void initCell()
        {
            this.olvColumnNetType.AspectGetter += delegate (object row)
            {
                if (row is CellInfo)
                {
                    CellInfo cellInfo = row as CellInfo;
                    string netType = getNetTypeByCell(cellInfo);
                    return netType;
                }
                return string.Empty;
            };
        }

        private void initBTS()
        {
            this.colBTSNetType.AspectGetter += delegate (object row)
            {
                if (row is BTSInfo)
                {
                    BTSInfo btsInfo = row as BTSInfo;
                    string netType = getNetTypeByBts(btsInfo);
                    return netType;
                }
                return string.Empty;
            };
        }

        private void initBTSInfo()
        {
            this.olvColumnBTSBSC.AspectGetter = delegate (object row)
            {
                if (row is BTSInfo)
                {
                    BTSInfo bts = row as BTSInfo;
                    return bts.Bsc;
                }
                else
                {
                    return "";
                }
            };
            this.olvColumnBTSMSC.AspectGetter = delegate (object row)
            {
                if (row is BTSInfo)
                {
                    BTSInfo bts = row as BTSInfo;
                    return bts.Msc;
                }
                else
                {
                    return "";
                }
            };
            this.olvColumnBTSLongitude.AspectGetter = delegate (object row)
            {
                if (row is BTSInfo)
                {
                    BTSInfo bts = row as BTSInfo;
                    return bts.Longitude;
                }
                else
                {
                    return "";
                }
            };
            this.olvColumnBTSLatitude.AspectGetter = delegate (object row)
            {
                if (row is BTSInfo)
                {
                    BTSInfo bts = row as BTSInfo;
                    return bts.Latitude;
                }
                else
                {
                    return "";
                }
            };
            this.olvColumnBTSDoor.AspectGetter = delegate (object row)
            {
                if (row is BTSInfo)
                {
                    BTSInfo bts = row as BTSInfo;
                    return bts.Door;
                }
                else
                {
                    return "";
                }
            };
            this.olvColumnBandType.AspectGetter = delegate (object row)
            {
                if (row is BTSInfo)
                {
                    BTSInfo bts = row as BTSInfo;
                    return bts.BandType;
                }
                else
                {
                    return "";
                }
            };
            this.olvColumnBTSAltitude.AspectGetter = delegate (object row)
            {
                if (row is BTSInfo)
                {
                    BTSInfo bts = row as BTSInfo;
                    return bts.MaxAltitude;
                }
                else
                {
                    return "";
                }
            };
        }

        delegate string Func(CellInfo info);
        private string getValidCellData(object row, Func func)
        {
            if (row is CellInfo cell)
            {
                return func(cell);
            }
            else
            {
                return "";
            }
        }

        private void initCellInfo()
        {
            this.olvColumnCellName.AspectGetter = delegate (object row)
            {
                return getValidCellData(row, (info) => { return info.CellName; });
            };
            this.olvColumnCellBtsName.AspectGetter = delegate (object row)
            {
                return getValidCellData(row, (info) => { return info.BtsName; });
            };
            this.olvColumnCode.AspectGetter = delegate (object row)
            {
                return getValidCellData(row, (info) => { return info.Code; });
            };
            this.olvColumnLAC.AspectGetter = delegate (object row)
            {
                return getValidCellData(row, (info) => { return info.Lac.ToString(); });
            };
            this.olvColumnCI.AspectGetter = delegate (object row)
            {
                return getValidCellData(row, (info) => { return info.Ci.ToString(); });
            };
            this.olvColumnBCCH.AspectGetter = delegate (object row)
            {
                return getValidCellData(row, (info) => { return info.BCCH.ToString(); });
            };
            this.olvColumnBSIC.AspectGetter = delegate (object row)
            {
                return getValidCellData(row, (info) => { return info.BSIC.ToString(); });
            };
            this.olvColumnTCH.AspectGetter = delegate (object row)
            {
                return getValidCellData(row, (info) => { return info.TCH; });
            };
            this.olvColumnBSC.AspectGetter = delegate (object row)
            {
                return getValidCellData(row, (info) => { return info.Bsc; });
            };
            this.olvColumnMSC.AspectGetter = delegate (object row)
            {
                return getValidCellData(row, (info) => { return info.Msc; });
            };
            this.olvColumnLongitude.AspectGetter = delegate (object row)
            {
                return getValidCellData(row, (info) => { return info.Longitude.ToString(); });
            };
            this.olvColumnLatitude.AspectGetter = delegate (object row)
            {
                return getValidCellData(row, (info) => { return info.Latitude.ToString(); });
            };
            this.olvColumnDoor.AspectGetter = delegate (object row)
            {
                return getValidCellData(row, (info) => { return info.Door; });
            };
            this.olvColumnType.AspectGetter = delegate (object row)
            {
                return getValidCellData(row, (info) => { return info.BandType; });
            };
            this.olvColumnAngle_Dir.AspectGetter = delegate (object row)
            {
                return getValidCellData(row, (info) => { return info.Angle_Dir.ToString(); });
            };
            this.olvColumnAngle_Ob.AspectGetter = delegate (object row)
            {
                return getValidCellData(row, (info) => { return info.Angle_Ob.ToString(); });
            };
            this.olvColumnAltitude.AspectGetter = delegate (object row)
            {
                return getValidCellData(row, (info) => { return info.Altitude.ToString(); });
            };
            this.olvColumnBTSComment.AspectGetter = delegate (object row)
            {
                return getValidCellData(row, (info) => { return info.BTSDescription; });
            };
            this.olvColumnCGI.AspectGetter = delegate (object row)
            {
                return getValidCellData(row, (info) => { return info.CGI; });
            };
        }

        public void FillData(RegionInfo info)
        {
            this.allCellsInfos = info.AllCellsInfos;
            this.cellsOf2GInfos = info.CellsOf2GInfos;
            this.cellsOf3GInfos = info.CellsOf3GInfos;
            this.cellsOf4GInfos = info.CellsOf4GInfos;
            this.cellsOf5GInfos = info.CellsOf5GInfos;
            this.allBTSsInfos = info.AllBTSsInfos;
            this.btssOf2GInfos = info.BtssOf2GInfos;
            this.btssOf3GInfos = info.BtssOf3GInfos;
            this.btssOf4GInfos = info.BtssOf4GInfos;
            this.btssOf5GInfos = info.BtssOf5GInfos;
            fillData();
        }

        public class RegionInfo
        {
            public List<RegionCellsInfo> AllCellsInfos { get; set; }
            public List<RegionCellsInfo> CellsOf2GInfos { get; set; }
            public List<RegionCellsInfo> CellsOf3GInfos { get; set; }
            public List<RegionCellsInfo> CellsOf4GInfos { get; set; }
            public List<RegionCellsInfo> CellsOf5GInfos { get; set; }
            public List<RegionBTSsInfo> AllBTSsInfos { get; set; }
            public List<RegionBTSsInfo> BtssOf2GInfos { get; set; }
            public List<RegionBTSsInfo> BtssOf3GInfos { get; set; }
            public List<RegionBTSsInfo> BtssOf4GInfos { get; set; }
            public List<RegionBTSsInfo> BtssOf5GInfos { get; set; }
        }

        private void rb_CheckedChanged(object sender, EventArgs e)
        {
            tlvCellsInfo.ClearObjects();
            fillData();
        }

        private void fillData()
        {
            curCellInfos = new List<RegionCellsInfo>();
            curBTSInfos = new List<RegionBTSsInfo>();
            if (rbAll.Checked)
            {
                curCellInfos = allCellsInfos;
                curBTSInfos = allBTSsInfos;
            }
            else if (rb2G.Checked)
            {
                curCellInfos = cellsOf2GInfos;
                curBTSInfos = btssOf2GInfos;
            }
            else if (rb3G.Checked)
            {
                curCellInfos = cellsOf3GInfos;
                curBTSInfos = btssOf3GInfos;
            }
            else if (rb4G.Checked)
            {
                curCellInfos = cellsOf4GInfos;
                curBTSInfos = btssOf4GInfos;
            }
            else if (rb5G.Checked)
            {
                curCellInfos = cellsOf5GInfos;
                curBTSInfos = btssOf5GInfos;
            }
            tlvCellsInfo.SetObjects(curCellInfos);
            tlvCellsInfo.Tag = curCellInfos;
            tlvCellsInfo.ExpandAll();

            lvBTS.SetObjects(curBTSInfos);
            lvBTS.Tag = curBTSInfos;
            lvBTS.ExpandAll();
        }

        private void miExpandAll_Click(object sender, EventArgs e)
        {
            if (tabControl1.SelectedIndex == 0)
            {
                tlvCellsInfo.ExpandAll();
            }
            else
            {
                lvBTS.ExpandAll();
            }
        }

        private void miCloseAll_Click(object sender, EventArgs e)
        {
            if (tabControl1.SelectedIndex == 0)
            {
                tlvCellsInfo.CollapseAll();
            }
            else
            {
                lvBTS.CollapseAll();
            }
        }

        private void miExport2Xls_Click(object sender, EventArgs e)
        {
            if (tabControl1.SelectedIndex == 0)
            {
                export2Excel();
            }
            else
            {
                exportBTS2Excel();
            }
        }

        private void export2Excel()
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.AddCellValue("区域");
            row.AddCellValue("面积（km²）");
            row.AddCellValue("小区个数");
            row.AddCellValue("小区名称");
            row.AddCellValue("基站名称");
            row.AddCellValue("网络类型");
            row.AddCellValue("Code");
            row.AddCellValue("LAC");
            row.AddCellValue("CI");
            row.AddCellValue("频点");
            row.AddCellValue("扰码");
            row.AddCellValue("BSC");
            row.AddCellValue("MSC");
            row.AddCellValue("TCH");
            row.AddCellValue("经度");
            row.AddCellValue("纬度");
            row.AddCellValue("室内/室外");
            row.AddCellValue("频带类型");
            row.AddCellValue("方向角");
            row.AddCellValue("下倾角");
            row.AddCellValue("挂高");
            row.AddCellValue("基站描述");
            row.AddCellValue("CGI");
            rows.Add(row);
            foreach (RegionCellsInfo cellInfo in curCellInfos)
            {
                row = new NPOIRow();
                row.AddCellValue(cellInfo.regionName);
                row.AddCellValue(cellInfo.Area);
                row.AddCellValue(cellInfo.cellInfos.Count);
                foreach (CellInfo cell in cellInfo.cellInfos)
                {
                    NPOIRow subRow = new NPOIRow();
                    subRow.AddCellValue(cell.CellName);
                    subRow.AddCellValue(cell.BtsName);
                    string netType = getNetTypeByCell(cell);
                    subRow.AddCellValue(netType);

                    subRow.AddCellValue(cell.Code);
                    subRow.AddCellValue(cell.Lac);
                    subRow.AddCellValue(cell.Ci);
                    subRow.AddCellValue(cell.BCCH);
                    subRow.AddCellValue(cell.BSIC);
                    subRow.AddCellValue(cell.Bsc);
                    subRow.AddCellValue(cell.Msc);
                    subRow.AddCellValue(cell.TCH);
                    subRow.AddCellValue(cell.Longitude);
                    subRow.AddCellValue(cell.Latitude);
                    subRow.AddCellValue(cell.Door);
                    subRow.AddCellValue(cell.BandType);
                    subRow.AddCellValue(cell.Angle_Dir);
                    subRow.AddCellValue(cell.Angle_Ob);
                    subRow.AddCellValue(cell.Altitude);
                    subRow.AddCellValue(cell.BTSDescription);
                    subRow.AddCellValue(cell.CGI);
                    row.AddSubRow(subRow);
                }
                rows.Add(row);
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }

        private void exportBTS2Excel()
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.AddCellValue("区域");
            row.AddCellValue("面积（km²）");
            row.AddCellValue("基站个数");
            row.AddCellValue("基站名称");
            row.AddCellValue("网络类型");
            row.AddCellValue("BSC");
            row.AddCellValue("MSC");
            row.AddCellValue("经度");
            row.AddCellValue("纬度");
            row.AddCellValue("室内/室外");
            row.AddCellValue("频段");
            row.AddCellValue("基站高度");
            rows.Add(row);
            foreach (RegionBTSsInfo btsInfo in curBTSInfos)
            {
                row = new NPOIRow();
                row.AddCellValue(btsInfo.regionName);
                row.AddCellValue(btsInfo.Area);
                row.AddCellValue(btsInfo.btsInfos.Count);
                foreach (BTSInfo bts in btsInfo.btsInfos)
                {
                    NPOIRow subRow = new NPOIRow();
                    subRow.AddCellValue(bts.BTSName);

                    string netType = getNetTypeByBts(bts);
                    subRow.AddCellValue(netType);
                    subRow.AddCellValue(bts.Bsc);
                    subRow.AddCellValue(bts.Msc);
                    subRow.AddCellValue(bts.Longitude);
                    subRow.AddCellValue(bts.Latitude);
                    subRow.AddCellValue(bts.Door);
                    subRow.AddCellValue(bts.BandType);
                    subRow.AddCellValue(bts.MaxAltitude);
                    row.AddSubRow(subRow);
                }
                rows.Add(row);
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }

        private void miExport2XlsWithNoMerge_Click(object sender, EventArgs e)
        {
            if (tabControl1.SelectedIndex == 0)
            {
                export2ExcelWithNoMerge();
            }
            else
            {
                exportBTS2ExcelWithNoMerge();
            }
        }

        private void export2ExcelWithNoMerge()
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.AddCellValue("区域");
            row.AddCellValue("面积（km²）");
            row.AddCellValue("小区个数");
            row.AddCellValue("小区名称");
            row.AddCellValue("基站名称");
            row.AddCellValue("网络类型");
            row.AddCellValue("Code");
            row.AddCellValue("LAC");
            row.AddCellValue("CI");
            row.AddCellValue("频点");
            row.AddCellValue("扰码");
            row.AddCellValue("BSC");
            row.AddCellValue("MSC");
            row.AddCellValue("TCH");
            row.AddCellValue("经度");
            row.AddCellValue("纬度");
            row.AddCellValue("室内/室外");
            row.AddCellValue("频带类型");
            row.AddCellValue("方向角");
            row.AddCellValue("下倾角");
            row.AddCellValue("挂高");
            row.AddCellValue("基站描述");
            row.AddCellValue("CGI");
            rows.Add(row);
            foreach (RegionCellsInfo cellInfo in curCellInfos)
            {
                foreach (CellInfo cell in cellInfo.cellInfos)
                {
                    string netType = getNetTypeByCell(cell);

                    NPOIRow subRow = new NPOIRow();
                    subRow.AddCellValue(cellInfo.regionName);
                    subRow.AddCellValue(cellInfo.Area);
                    subRow.AddCellValue(cellInfo.cellInfos.Count);
                    subRow.AddCellValue(cell.CellName);
                    subRow.AddCellValue(cell.BtsName);
                    subRow.AddCellValue(netType);
                    subRow.AddCellValue(cell.Code);
                    subRow.AddCellValue(cell.Lac);
                    subRow.AddCellValue(cell.Ci);
                    subRow.AddCellValue(cell.BCCH);
                    subRow.AddCellValue(cell.BSIC);
                    subRow.AddCellValue(cell.Bsc);
                    subRow.AddCellValue(cell.Msc);
                    subRow.AddCellValue(cell.TCH);
                    subRow.AddCellValue(cell.Longitude);
                    subRow.AddCellValue(cell.Latitude);
                    subRow.AddCellValue(cell.Door);
                    subRow.AddCellValue(cell.BandType);
                    subRow.AddCellValue(cell.Angle_Dir);
                    subRow.AddCellValue(cell.Angle_Ob);
                    subRow.AddCellValue(cell.Altitude);
                    subRow.AddCellValue(cell.BTSDescription);
                    subRow.AddCellValue(cell.CGI);
                    rows.Add(subRow);
                }
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }

        private void exportBTS2ExcelWithNoMerge()
        {
            List<NPOIRow> rows = new List<NPOIRow>();
            NPOIRow row = new NPOIRow();
            row.AddCellValue("区域");
            row.AddCellValue("面积（km²）");
            row.AddCellValue("基站个数");
            row.AddCellValue("基站名称");
            row.AddCellValue("网络类型");
            row.AddCellValue("BSC");
            row.AddCellValue("MSC");
            row.AddCellValue("经度");
            row.AddCellValue("纬度");
            row.AddCellValue("室内/室外");
            row.AddCellValue("频段");
            row.AddCellValue("基站高度");
            rows.Add(row);
            foreach (RegionBTSsInfo btsInfo in curBTSInfos)
            {
                foreach (BTSInfo bts in btsInfo.btsInfos)
                {
                    NPOIRow subRow = new NPOIRow();
                    subRow.AddCellValue(btsInfo.regionName);
                    subRow.AddCellValue(btsInfo.Area);
                    subRow.AddCellValue(btsInfo.btsInfos.Count);
                    subRow.AddCellValue(bts.BTSName);
                    string netType = getNetTypeByBts(bts);
                    subRow.AddCellValue(netType);
                    subRow.AddCellValue(bts.Bsc);
                    subRow.AddCellValue(bts.Msc);
                    subRow.AddCellValue(bts.Longitude);
                    subRow.AddCellValue(bts.Latitude);
                    subRow.AddCellValue(bts.Door);
                    subRow.AddCellValue(bts.BandType);
                    subRow.AddCellValue(bts.MaxAltitude);
                    rows.Add(subRow);
                }
            }
            ExcelNPOIManager.ExportToExcel(rows);
        }

        private static string getNetTypeByCell(CellInfo cell)
        {
            string netType = string.Empty;
            if (cell.Cell is Cell)
            {
                netType = "GSM";
            }
            else if (cell.Cell is TDCell)
            {
                netType = "TDSCDMA";
            }
            else if (cell.Cell is LTECell)
            {
                netType = "LTE";
            }
            else if (cell.Cell is WCell)
            {
                netType = "WCDMA";
            }
            else if (cell.Cell is CDCell)
            {
                netType = "CDMA";
            }
            else if (cell.Cell is NRCell)
            {
                netType = "NR";
            }

            return netType;
        }

        private static string getNetTypeByBts(BTSInfo bts)
        {
            string netType = string.Empty;
            if (bts.BTS is BTS)
            {
                netType = "GSM";
            }
            else if (bts.BTS is TDNodeB)
            {
                netType = "TDSCDMA";
            }
            else if (bts.BTS is LTEBTS)
            {
                netType = "LTE";
            }
            else if (bts.BTS is WNodeB)
            {
                netType = "WCDMA";
            }
            else if (bts.BTS is CDNodeB)
            {
                netType = "CDMA";
            }
            else if (bts.BTS is NRBTS)
            {
                netType = "NR";
            }

            return netType;
        }

        private void tlvCellsInfo_DoubleClick(object sender, EventArgs e)
        {
            object row = tlvCellsInfo.SelectedObject;

            CellInfo cellInfo = row as CellInfo;
            if (cellInfo == null)
            {
                return;
            }
            if (cellInfo.Cell is Cell)
            {
                MainModel.SelectedCell = cellInfo.Cell as Cell;
            }
            else if (cellInfo.Cell is TDCell)
            {
                MainModel.SelectedTDCell = cellInfo.Cell as TDCell;
            }
            else if (cellInfo.Cell is LTECell)
            {
                MainModel.SelectedLTECell = cellInfo.Cell as LTECell;
            }
            else if (cellInfo.Cell is WCell)
            {
                MainModel.SelectedWCell = cellInfo.Cell as WCell;
            }
            else if (cellInfo.Cell is CDCell)
            {
                MainModel.SelectedCDCell = cellInfo.Cell as CDCell;
            }
            else if (cellInfo.Cell is NRCell)
            {
                MainModel.SetSelectedNRCell(cellInfo.Cell as NRCell);
            }
            MainModel.MainForm.GetMapForm().GoToView(cellInfo.Cell.Longitude, cellInfo.Cell.Latitude);
        }
    }
}

