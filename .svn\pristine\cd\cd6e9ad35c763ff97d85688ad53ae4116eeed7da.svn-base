﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.Model;
using MasterCom.RAMS.Net;

namespace MasterCom.RAMS.NOP
{
    public class ShowTaskOrderForm : QueryBase
    {
        public ShowTaskOrderForm()
            : base(MainModel.GetInstance())
        {
        }

        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        public override string Name
        {
            get { return "查看工单"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 29000, 29001, this.Name);
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        TaskOrderForm orderForm = null;
        protected override void query()
        {
            if (orderForm == null || orderForm.IsDisposed)
            {
                orderForm = new TaskOrderForm();
            }
            orderForm.Visible = true;
            orderForm.Owner = MainModel.MainForm;
            orderForm.BringToFront();
        }
    }
}
