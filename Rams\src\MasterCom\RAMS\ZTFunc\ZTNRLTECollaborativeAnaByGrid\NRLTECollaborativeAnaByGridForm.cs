﻿using MasterCom.RAMS.Func;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.ZTFunc.ZTNRLTECollaborativeAnaByGrid;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class NRLTECollaborativeAnaByGridForm : MinCloseForm
    {
        MapForm mf = null;
        NRLTECollaborativeAnaGridLayer layer = null;
        Dictionary<ZTNRLTECollaborativeAnaType, List<Result>> typeResultDic;
        List<SerialResult> serialResultList;

        public NRLTECollaborativeAnaByGridForm()
        {
            InitializeComponent();
        }

        public void FillData(List<SerialResult> serialResultList
            , Dictionary<ZTNRLTECollaborativeAnaType, List<Result>> typeResultDic)
        {
            this.typeResultDic = typeResultDic;
            this.serialResultList = serialResultList;
            gcDetail.DataSource = serialResultList;
            gcDetail.RefreshDataSource();

            mf = MainModel.MainForm.GetMapForm();
            //设置图层
            MasterCom.MTGis.LayerBase clayer = mf.GetTempLayerBase(typeof(NRLTECollaborativeAnaGridLayer));
            layer = clayer as NRLTECollaborativeAnaGridLayer;
            NRLTECollaborativeAnaGridLayer.SerialGridInfos = serialResultList;
            layer.DealGridRangeCount();
            layer.SelectedGridChanged -= selectedGridChanged;
            layer.SelectedGridChanged += selectedGridChanged;
            //Disposed += disposed;
            MainModel.RefreshLegend();
        }

        private void gv_DoubleClick(object sender, EventArgs e)
        {
            var view = this.gcDetail.FocusedView as DevExpress.XtraGrid.Views.Grid.GridView;
            if (view == null)
            {
                return;
            }
            var dxEvt = e as DevExpress.Utils.DXMouseEventArgs;
            if (dxEvt == null)
            {
                return;
            }
            var info = view.CalcHitInfo(dxEvt.Location);
            if (info == null || !info.InRow)
            {
                return;
            }
            Result selectedData = view.GetRow(info.RowHandle) as Result;
            layer.SelectedGrid = selectedData;
            mModel.MainForm.GetMapForm().GoToView(selectedData.CentLng, selectedData.CentLat);
        }

        private void selectedGridChanged(object sender, EventArgs e)
        {
            if (layer.SelectedGrid == null)
            {
                return;
            }

            foreach (var serialResult in serialResultList)
            {
                var grids = serialResult.ResultList;
                int index = grids.IndexOf(layer.SelectedGrid);
                int realIndex = gvGrid.DataController.GetControllerRow(index);
                if (realIndex >= 0)
                {
                    int rowIdx = gvSerialGrid.DataController.FindRowByRowValue(serialResult);
                    gvSerialGrid.ClearSelection();
                    gvSerialGrid.FocusedRowHandle = rowIdx;
                    gvSerialGrid.SelectRow(rowIdx);
                    //gvSerialGrid.SetRowExpanded(rowIdx, true);

                    //gvGrid.ClearSelection();
                    //gvGrid.FocusedRowHandle = realIndex;
                    //gvGrid.SelectRow(realIndex);

                    //int rowIdx1 = gvGrid.DataController.FindRowByRowValue(layer.SelectedGrid);
                    //gvGrid.ClearSelection();
                    //gvGrid.FocusedRowHandle = rowIdx1;
                    //gvGrid.SelectRow(rowIdx1);

                    //gvSerialGrid.DataController.GetControllerRow(index);
                    break;
                }
            }

            var grid = layer.SelectedGrid.Grid;
            MainModel.MainForm.GetMapForm().GoToView(grid.CenterLng, grid.CenterLat);
        }

        private void spEditSerialNum_EditValueChanged(object sender, EventArgs e)
        {
            int count = (int)spEditSerialNum.Value;
            serialResultList = SerialGridHelper.GetSerialGrid(count, typeResultDic);

            gcDetail.DataSource = serialResultList;
            gcDetail.RefreshDataSource();

            NRLTECollaborativeAnaGridLayer.SerialGridInfos = serialResultList;
            mf.updateMap();
        }
    }
}
