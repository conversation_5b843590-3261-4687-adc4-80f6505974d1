﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.RAMS.ExMap;
using MasterCom.MTGis;
using GMap.NET;
using System.Drawing;
using System.Drawing.Drawing2D;

namespace MasterCom.RAMS.MapControlTool
{
    public class ExMapToolAddPolygon
    {
        private readonly MTExGMap mapControl;
        private readonly Font font;
        private readonly Pen linePen;
        private List<PointLatLng> points;//经纬度坐标
        public List<DbPoint> Points
        {
            get
            {
                List<DbPoint> pnt = new List<DbPoint>();
                foreach (PointLatLng p in points)
                {
                    pnt.Add(new DbPoint(p.Lng, p.Lat));
                }
                return pnt;
            }
            set
            {
                points = new List<PointLatLng>();
                foreach (DbPoint p in value)
                {
                    points.Add(new PointLatLng(p.y, p.x));
                }
            }
        }

        private bool drawStarted = false;
        public ExMapToolAddPolygon(MTExGMap map)
        {
            mapControl = map;
            points = new List<PointLatLng>();
            font = new Font("宋体", 11, FontStyle.Bold);
            linePen = new Pen(Color.Green, 3);
        }

        private bool isActive = false;
        public void Activate()
        {
            if (!isActive)
            {
                mapControl.MouseDown += mapControl_MouseDown;
                mapControl.MouseDoubleClick+=new System.Windows.Forms.MouseEventHandler(mapControl_MouseDoubleClick);
            }
            isActive = true;
        }

        public void Deactivate()
        {
            if (!isActive) return;
            mapControl.MouseDown -= mapControl_MouseDown;
            mapControl.MouseDoubleClick -= new System.Windows.Forms.MouseEventHandler(mapControl_MouseDoubleClick);
            isActive = false;
        }

        void mapControl_MouseDoubleClick(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            drawFinished();
        }

        public void Clear()
        {
            points.Clear();
        }

        void mapControl_MouseDown(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            mapControl.MouseMove -= new System.Windows.Forms.MouseEventHandler(mapControl_MouseMove);
            if (e.Button == System.Windows.Forms.MouseButtons.Left)
            {
                mapControl.MouseMove += new System.Windows.Forms.MouseEventHandler(mapControl_MouseMove);
                if (!drawStarted)
                {
                    points.Clear();
                    drawStarted = true;
                }
                PointLatLng newPoint = mapControl.FromLocalToLatLngAdaptered(e.X, e.Y);
                if (points.Count > 0 && points[points.Count - 1].Lat == newPoint.Lat && points[points.Count - 1].Lng == newPoint.Lng)
                {
                    return;
                }
                points.Add(newPoint);
            }
            else
            {
                drawFinished();
            }
        }

        public event EventHandler PolygonCreated;
        private void drawFinished()
        {
            drawStarted = false;
            if (points.Count < 3)
            {
                points.Clear();
            }
            mapControl.MouseMove -= mapControl_MouseMove;
            PolygonCreated(this, EventArgs.Empty);
        }

        void mapControl_MouseMove(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            PointLatLng newPoint = mapControl.FromLocalToLatLngAdaptered(e.X, e.Y);
            if (points.Count > 0 && points[points.Count - 1].Lat == newPoint.Lat && points[points.Count - 1].Lng == newPoint.Lng)
            {
                return;
            }
            List<PointLatLng> tempPoints = new List<PointLatLng>();
            tempPoints.AddRange(points);
            tempPoints.Add(newPoint);
            mapControl.Invalidate();
            mapControl.Update();
            Graphics g = mapControl.CreateGraphics();
            g.SmoothingMode = SmoothingMode.AntiAlias;
            drawPolygon(g, tempPoints);
        }

        public void Draw(Graphics g)
        {
            drawPolygon(g,points);
        }

        private void drawPolygon(Graphics g,List<PointLatLng> pnts)
        {
            if (pnts.Count < 2)
            {
                return;
            }
            Point[] pArr = new Point[pnts.Count];
            for (int i = 0; i < pnts.Count; i++)
            {
                GPoint gp = mapControl.FromLatLngToLocalAdaptered(pnts[i]);
                pArr[i] = new Point(gp.X, gp.Y);
            }
            if (drawStarted)
            {
                SolidBrush solidBrush = new SolidBrush(Color.FromArgb(200, Color.White));
                string str = "双击鼠标左键可以完成多边形绘画！";
                SizeF sizeF = g.MeasureString(str, font);
                g.FillRectangle(solidBrush, pArr[0].X, pArr[0].Y, sizeF.Width, sizeF.Height);
                g.DrawString(str, font, Brushes.Red, pArr[0]);
            }
            g.DrawPolygon(linePen, pArr);
            g.FillPolygon(new SolidBrush(Color.FromArgb(50, Color.Green)), pArr);
        }

    }
}
