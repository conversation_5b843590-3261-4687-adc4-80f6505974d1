using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.Drawing;
using MasterCom.RAMS.Model;
using MasterCom.MTGis;
namespace MasterCom.RAMS.Func
{
    [System.ComponentModel.ToolboxItem(false)]
    public class MapWCellLayerPlanningProperties : MTLayerPropUserControl
    {
        public MapWCellLayerPlanningProperties()
        {
            InitializeComponent();
        }

        public override void Setup(Object obj)
        {
            if (obj == null)
            {
                return;
            }
            Text = "Planning";
            layer = (MapWCellLayer)obj;
            labelColorNeighbour.BackColor = layer.ColorNeighbour;
            TrackBarOpacity.Value = labelColorNeighbour.BackColor.A;
        }

        private void labelColorNeighbour_Click(object sender, EventArgs e)
        {
            colorDialog.Color = labelColorNeighbour.BackColor;
            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                labelColorNeighbour.BackColor = Color.FromArgb(TrackBarOpacity.Value, colorDialog.Color);
                layer.ColorNeighbour = labelColorNeighbour.BackColor;
                layer.RefreshBrushes();
            }
        }

        private void TrackBarOpacity_Scroll(object sender, EventArgs e)
        {
            labelColorNeighbour.BackColor = Color.FromArgb(TrackBarOpacity.Value, labelColorNeighbour.BackColor);
            layer.ColorNeighbour = labelColorNeighbour.BackColor;
            layer.RefreshBrushes();
        }

        private void InitializeComponent()
        {
            System.Windows.Forms.Label label12;
            System.Windows.Forms.Label LabelOpacity;
            System.Windows.Forms.Label labelColor;
            System.Windows.Forms.Label label0;
            System.Windows.Forms.Label label100;
            this.TrackBarOpacity = new System.Windows.Forms.TrackBar();
            this.labelColorNeighbour = new System.Windows.Forms.Label();
            label12 = new System.Windows.Forms.Label();
            LabelOpacity = new System.Windows.Forms.Label();
            labelColor = new System.Windows.Forms.Label();
            label0 = new System.Windows.Forms.Label();
            label100 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.TrackBarOpacity)).BeginInit();
            this.SuspendLayout();
            // 
            // label12
            // 
            label12.AutoSize = true;
            label12.Location = new System.Drawing.Point(8, 62);
            label12.Name = "label12";
            label12.Size = new System.Drawing.Size(59, 12);
            label12.TabIndex = 66;
            label12.Text = "Neighbour";
            label12.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // LabelOpacity
            // 
            LabelOpacity.Location = new System.Drawing.Point(7, 109);
            LabelOpacity.Name = "LabelOpacity";
            LabelOpacity.Size = new System.Drawing.Size(56, 16);
            LabelOpacity.TabIndex = 8;
            LabelOpacity.Text = "Opacity: ";
            // 
            // labelColor
            // 
            labelColor.Location = new System.Drawing.Point(3, 23);
            labelColor.Name = "labelColor";
            labelColor.Size = new System.Drawing.Size(37, 20);
            labelColor.TabIndex = 5;
            labelColor.Text = "Color:";
            labelColor.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label0
            // 
            label0.AutoSize = true;
            label0.Location = new System.Drawing.Point(58, 130);
            label0.Name = "label0";
            label0.Size = new System.Drawing.Size(17, 12);
            label0.TabIndex = 40;
            label0.Text = "0%";
            label0.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label100
            // 
            label100.AutoSize = true;
            label100.Location = new System.Drawing.Point(214, 130);
            label100.Name = "label100";
            label100.Size = new System.Drawing.Size(29, 12);
            label100.TabIndex = 41;
            label100.Text = "100%";
            label100.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // TrackBarOpacity
            // 
            this.TrackBarOpacity.LargeChange = 32;
            this.TrackBarOpacity.Location = new System.Drawing.Point(53, 98);
            this.TrackBarOpacity.Maximum = 255;
            this.TrackBarOpacity.Name = "TrackBarOpacity";
            this.TrackBarOpacity.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.TrackBarOpacity.Size = new System.Drawing.Size(187, 45);
            this.TrackBarOpacity.TabIndex = 9;
            this.TrackBarOpacity.TickFrequency = 32;
            this.TrackBarOpacity.Value = 255;
            this.TrackBarOpacity.Scroll += new System.EventHandler(this.TrackBarOpacity_Scroll);
            // 
            // labelColorNeighbour
            // 
            this.labelColorNeighbour.BackColor = System.Drawing.Color.Red;
            this.labelColorNeighbour.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.labelColorNeighbour.Location = new System.Drawing.Point(73, 57);
            this.labelColorNeighbour.Name = "labelColorNeighbour";
            this.labelColorNeighbour.Size = new System.Drawing.Size(25, 25);
            this.labelColorNeighbour.TabIndex = 58;
            this.labelColorNeighbour.Click += new System.EventHandler(this.labelColorNeighbour_Click);
            // 
            // MapFormCellTDLayerPlanningProperties
            // 
            this.Controls.Add(label12);
            this.Controls.Add(this.labelColorNeighbour);
            this.Controls.Add(label100);
            this.Controls.Add(label0);
            this.Controls.Add(this.TrackBarOpacity);
            this.Controls.Add(LabelOpacity);
            this.Controls.Add(labelColor);
            this.Name = "MapFormCellTDLayerPlanningProperties";
            this.Size = new System.Drawing.Size(383, 204);
            ((System.ComponentModel.ISupportInitialize)(this.TrackBarOpacity)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        private MapWCellLayer layer;

        private Label labelColorNeighbour;

        private TrackBar TrackBarOpacity;

        private readonly ColorDialog colorDialog = new ColorDialog();
    }
}
