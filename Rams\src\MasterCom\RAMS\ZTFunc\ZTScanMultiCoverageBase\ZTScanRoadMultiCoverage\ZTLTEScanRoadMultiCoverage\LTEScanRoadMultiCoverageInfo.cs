﻿using MasterCom.MControls;
using MasterCom.RAMS.Model;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;

namespace MasterCom.RAMS.ZTFunc
{
    public class LTEMultiCoverageTpColorRanges : MultiCoverageTpColorRangesBase
    {
        public LTEMultiCoverageTpColorRanges()
        {
            initialize();
        }

        public override string RangeName { get { return "MultiCoverageTpColorRanges_LTE"; } }

        protected override void initialize()
        {
            ColorRanges = new List<ColorRange>();
            ColorRanges.Add(new ColorRange(0, 2, Color.Cyan));
            ColorRanges.Add(new ColorRange(2, 4, Color.Lime));
            ColorRanges.Add(new ColorRange(4, 6, Color.Yellow));
            ColorRanges.Add(new ColorRange(6, 10, Color.Orange));
            ColorRanges.Add(new ColorRange(10, 50, Color.Red));
        }
    }

    public class LTEScaLTEoadMultiCoverageInfo : RoadMultiCoverageInfoBase
    {
        public List<LTECellRsrpInfo> LstRelatedCell { get; set; } = new List<LTECellRsrpInfo>();
        public List<LTECellRsrpInfo> LstAbsoluteCell { get; set; } = new List<LTECellRsrpInfo>();
        public List<LTECellRsrpInfo> LstComprehensive { get; set; } = new List<LTECellRsrpInfo>();

        public string StrRegionName { get; set; }

        public List<string> RelCoverCellsName { get; set; } = new List<string>();
        public List<string> AbsCoverCellsName { get; set; } = new List<string>();
        public List<string> RelANDAbsCoverCellsName { get; set; } = new List<string>();

        public string RelCoverCellsNameStr { get; private set; }
        public string AbsCoverCellsNameStr { get; private set; }
        public string RelORAbsCoverCellsNameStr { get; private set; }

        public void Calculate()
        {
            RelCoverCellsNameStr = getNameStr(RelCoverCellsName);
            AbsCoverCellsNameStr = getNameStr(AbsCoverCellsName);
            RelORAbsCoverCellsNameStr = getNameStr(RelANDAbsCoverCellsName);
        }
    }

    public class LTEScanGridRoadMultiCoverageInfo : GridRoadMultiCoverageInfoBase
    {
        public LTEScanGridRoadMultiCoverageInfo(double ltLongitude, double ltLatitude)
           : base(ltLongitude, ltLatitude)
        {
        }
    }

    public class LTECellRsrpInfo : CellRsrpInfoBase<LTECell>
    {
        public LTECellRsrpInfo(float rsrp, int earfcn, int pci, LTECell cell)
            : base(rsrp, earfcn, pci, cell)
        {
        }
    }
}
