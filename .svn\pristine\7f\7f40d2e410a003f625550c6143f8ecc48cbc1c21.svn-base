﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing.Drawing2D;
using System.Drawing;
using MapWinGIS;

namespace MasterCom.MTGis
{
    public class MapOperation2
    {
        public RegionExtern GetRegion()
        {
            return geo;
        }
        private RegionExtern geo = new RegionExtern();
        public void FillPolygon(MapWinGIS.Shape shape)
        {
            geo = new RegionExtern();
            if (shape == null)
            {
                return;
            }
            appendPolyon(shape, geo);
        }

        public void FillMultiPolygeon(List<MapWinGIS.Shape> shapeSet)
        {
            geo = new RegionExtern();
            if (shapeSet == null)
            {
                return;
            }
            foreach (MapWinGIS.Shape shape in shapeSet)
            {
                appendPolyon(shape, geo);
            }
        }

        /// <summary>
        /// 仅处理不含岛，不相交的多边形s
        /// </summary>
        /// <param name="multiPoly"></param>
        public void FillPolygon(List<PointF[]> multiPoly)
        {
            geo = new RegionExtern();
            if (multiPoly.Count == 0)
            {
                return;
            }
            foreach (PointF[] pfs in multiPoly)
            {
                PointF[] newPfs = new PointF[pfs.Length];
                pfs.CopyTo(newPfs, 0);
                geo.SetSimplePolygon(newPfs);
            }
        }

        public void FillPolygon(List<DbPoint> polygon)
        {
            geo = new RegionExtern();
            List<List<DbPoint>> multiPolygonPnts = new List<List<DbPoint>>();
            multiPolygonPnts.Add(polygon);
            geo.SetMultiPolygon(multiPolygonPnts);
        }

        /// <summary>
        ///遍历part shape，按part追加polygon
        /// </summary>
        /// <param name="shape"></param>
        /// <param name="exRegion"></param>
        private void appendPolyon(MapWinGIS.Shape shape, RegionExtern exRegion)
        {
            if (shape.ShapeType == MapWinGIS.ShpfileType.SHP_POLYGON)
            {
                List<List<DbPoint>> multiPolygonPnts = new List<List<DbPoint>>();
                for (int x = 0; x < shape.NumParts; x++)
                {
                    multiPolygonPnts.Add(ShapeHelper.GetPartShapePoints(shape, x));
                }
                exRegion.SetMultiPolygon(multiPolygonPnts);
            }
        }

        public void FillSelectPoint(double x, double y, double radius)
        {
            geo = new RegionExtern();
            geo.SetPointAndRadius(x, y, radius);
        }

        /// <summary>
        /// 道路两边扩大40米形成一个封闭的区域。
        /// </summary>
        /// <param name="street"></param>
        public void FillStreets(List<MapWinGIS.Shape> streets)
        {
            if (streets == null || streets.Count == 0)
            {
                return;
            }
            geo = new RegionExtern();
            for (int s = 0; s < streets.Count; s++)
            {
                MapWinGIS.Shape street = streets[s];
                appendStreet(street);
            }
        }

        /// <summary>
        /// 道路两边扩大40米形成一个封闭的区域。
        /// </summary>
        /// <param name="street"></param>
        public void appendStreet(MapWinGIS.Shape street)
        {
            double roadWidth = 0.0004;//约为40米
            for (int part = 0; part < street.NumParts; part++)
            {
                List<DbPoint> ptListOne = new List<DbPoint>();
                List<DbPoint> ptListTwo = new List<DbPoint>();
                List<DbPoint> pnts = ShapeHelper.GetPartShapePoints(street, part);
                int numPointsOfPart = pnts.Count;
                double lastXDiff = 0;
                double lastYDiff = 0;
                for (int px = 0; px < numPointsOfPart - 1; px++)
                {
                    DbPoint ptThis = pnts[px];
                    DbPoint ptNext = pnts[px + 1];
                    double atan = Math.Atan2(ptNext.y - ptThis.y, ptNext.x - ptThis.x);
                    double xDiff = roadWidth * Math.Sin(atan);
                    double yDiff = roadWidth * Math.Cos(atan);
                    ptListOne.Add(new DbPoint(ptThis.x - xDiff, ptThis.y + yDiff));
                    ptListTwo.Add(new DbPoint(ptThis.x + xDiff, ptThis.y - yDiff));
                    if (px == numPointsOfPart - 2)
                    {
                        lastXDiff = xDiff;
                        lastYDiff = yDiff;
                    }
                }

                DbPoint lastPoint = pnts[pnts.Count - 1];
                ptListOne.Add(new DbPoint(lastPoint.x - lastXDiff, lastPoint.y + lastYDiff));
                ptListTwo.Add(new DbPoint(lastPoint.x + lastXDiff, lastPoint.y - lastYDiff));
                PointF[] points = new PointF[ptListOne.Count * 2];
                for (int i = 0; i < ptListOne.Count; i++)
                {
                    points[i].X = (float)ptListOne[i].x;
                    points[i].Y = (float)ptListOne[i].y;
                }
                int pos = 0;
                for (int i = ptListTwo.Count - 1; i >= 0; i--)
                {
                    points[pos + ptListOne.Count].X = (float)ptListTwo[i].x;
                    points[pos + ptListOne.Count].Y = (float)ptListTwo[i].y;
                    pos++;
                }
                if (points.Length > 2)
                {
                    geo.SetSimplePolygon(points);
                }
            }
        }

        /// <summary>
        /// 道路两边扩大40米形成一个封闭的区域。
        /// </summary>
        /// <param name="street"></param>
        public void FillStreet(MapWinGIS.Shape street)
        {
            if (street == null)
            {
                return;
            }
            geo = new RegionExtern();
            appendStreet(street);
        }


        public bool CheckPointInRegion(double x,double y)
        {
            if (geo == null)
            {
                return false;
            }
            DbRect bound = geo.Bounds;
            if(x<bound.x1 || x>bound.x2 || y<bound.y1 || y>bound.y2)
            {
                return false;
            }
            return geo.PtInRegion((float)x, (float)y);
        }

        /// <summary>
        /// 注意，只是判断这个矩形的中心点是否落在区域内哦。如果要判断是否相交，建议用CheckRectIntersectWithRegion
        /// </summary>
        /// <param name="dbRect"></param>
        /// <returns></returns>
        internal bool CheckRectCenterInRegion(DbRect dbRect)
        {
            if (geo == null)
            {
                return false;
            }
            DbPoint ptcenter = dbRect.Center();
            return geo.PtInRegion((float)ptcenter.x, (float)ptcenter.y);
        }

        internal bool CheckRectIntersectWithRegion(DbRect dRect)
        {
            if (geo == null)
            {
                return false;
            }
            return geo.IsIntersectWith(dRect);
        }

        internal bool CheckStreetInRegion(MapWinGIS.Shape roadMultiCurv)
        {
            for (int part = 0; part < roadMultiCurv.NumParts; part++)
            {
                List<DbPoint> pnts = ShapeHelper.GetPartShapePoints(roadMultiCurv, part);
                foreach (DbPoint pnt in pnts)
                {
                    if (geo.PtInRegion((float)pnt.x, (float)pnt.y))
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        internal bool CheckPointInStreets(double longitude, double latitude)
        {
            if (geo == null)
            {
                return false;
            }
            return geo.PtInRegion((float)longitude, (float)latitude);
        }

        internal bool CheckCenterInDRect(DbRect dRect)
        {
            DbPoint pt = geo.Bounds.Center();
            return pt.x > dRect.x1 && pt.x<dRect.x2 && pt.y>dRect.y1 && pt.y<dRect.y2;
        }
        internal bool CheckCenterInDbPoints(DbPoint[] dpoints)
        {
            DbPoint pt = geo.Bounds.Center();
            PointF[] points = new PointF[dpoints.Length];
            for (int i = 0; i < dpoints.Length; i++)
            {

                points[i].X = (float)(dpoints[i].x * RegionExtern.Zoom_factor);
                points[i].Y = (float)(dpoints[i].y * RegionExtern.Zoom_factor);
            }
            GraphicsPath path = new GraphicsPath();
            path.AddPolygon(points);
            return path.IsVisible((float)(pt.x * RegionExtern.Zoom_factor), (float)(pt.y * RegionExtern.Zoom_factor));
        }

        internal bool Contains(double x, double y)
        {
            return CheckPointInRegion(x, y);
        }

        internal bool ContainsRectCenter(DbRect dbRect)
        {
            return CheckRectCenterInRegion(dbRect);
        }
    }

    public class RegionExtern
    {
        // 坐标缩放因子[因为graphics的 IsVisible 方法在判定经纬度时,需要放大计算]
        public const float Zoom_factor = 100000;
        /// <summary>
        /// 当前仅在单区块和单点有效
        /// </summary>
        public DbRect Bounds { get; set; } = new DbRect();
        public Region GetRealRegion()
        {
            return realRegion.Clone();
        }
        private Region realRegion = null;
        /// <summary>
        /// 添加一个圆到Region
        /// </summary>
        /// <param name="x">圆心x</param>
        /// <param name="y">圆心y</param>
        /// <param name="radius">半径</param>
        public void SetPointAndRadius(double x, double y, double radius)
        {
            float centerX = (float)(x * Zoom_factor);
            float centerY = (float)(y * Zoom_factor);
            float rad = (float)(radius * Zoom_factor);
            GraphicsPath regionPath = new GraphicsPath();
            regionPath.StartFigure();
            regionPath.AddEllipse(centerX - rad, centerY - rad, 2.0f * rad, 2.0f * rad);
            regionPath.CloseFigure();
            if (realRegion == null)
            {
                realRegion = new Region(regionPath);
            }
            else
            {
                realRegion.Union(regionPath);
            }

            Bounds = new DbRect(x - radius, y - radius, x + radius, y + radius);
        }

        /// <summary>
        /// 添加一个多边形（该多边形必须是简单，即“非环形”多边形）到Region
        /// </summary>
        /// <param name="points">多边形顶点</param>
        public void SetSimplePolygon(PointF[] points)
        {
            float minX = float.MaxValue;
            float maxX = float.MinValue;
            float minY = float.MaxValue;
            float maxY = float.MinValue;
            for (int w = 0; w < points.Length; w++)
            {
                PointF pt = points[w];
                minX = Math.Min(minX, pt.X);
                maxX = Math.Max(maxX, pt.X);
                minY = Math.Min(minY, pt.Y);
                maxY = Math.Max(maxY, pt.Y);
            }
            if (Bounds.x1 == 0 && Bounds.x2 == 0 && Bounds.y1 == 0 && Bounds.y2 == 0) //首个添加的Polygon
            {
                Bounds = new DbRect(minX, minY, maxX, maxY);
            }
            else  //添加到现有的
            {
                minX = Math.Min(minX, (float)Bounds.x1);
                maxX = Math.Max(maxX, (float)Bounds.x2);
                minY = Math.Min(minY, (float)Bounds.y1);
                maxY = Math.Max(maxY, (float)Bounds.y2);
                Bounds = new DbRect(minX, minY, maxX, maxY);
            }

            for (int i = 0; i < points.Length; i++)
            {
                points[i].X *= Zoom_factor;
                points[i].Y *= Zoom_factor;
            }
            GraphicsPath regionPath = new GraphicsPath();
            regionPath.StartFigure();
            regionPath.AddPolygon(points);
            regionPath.CloseFigure();
            if (realRegion == null)
            {
                realRegion = new Region(regionPath);
            }
            else
            {
                realRegion.Union(regionPath);
            }
        }

        public bool PtInRegion(float x, float y)
        {
            bool ret = realRegion.IsVisible(x * Zoom_factor, y * Zoom_factor);
            return ret;
        }

        public bool IsIntersectWith(DbRect rect)
        {
            //屏幕坐标，left为最小的x坐标，upper为最小的y坐标，所以屏幕的左上角对于地图坐标的左下角
            RectangleF rectF = new RectangleF((float)rect.x1 * Zoom_factor
                , (float)rect.y1 * Zoom_factor, (float)(rect.x2 - rect.x1) * Zoom_factor
                , (float)(rect.y2 - rect.y1) * Zoom_factor);
            bool ret = realRegion.IsVisible(rectF);
            return ret;
        }

        /// <summary>
        /// 添加一个多边形（该多边形可以是环形多边形）到Region
        /// </summary>
        /// <param name="multiPolygonPnts">多重多边形顶底（应由shape分解而成）</param>
        internal void SetMultiPolygon(List<List<DbPoint>> multiPolygonPnts)
        {
            double minX = double.MaxValue;
            double maxX = double.MinValue;
            double minY = double.MaxValue;
            double maxY = double.MinValue;
            GraphicsPath path = new GraphicsPath();
            path.StartFigure();
            foreach (List<DbPoint> pnts in multiPolygonPnts)
            {
                PointF[] points = new PointF[pnts.Count];
                for (int i = 0; i < pnts.Count; i++)
                {
                    double x = pnts[i].x;
                    double y = pnts[i].y;
                    minX = Math.Min(minX, x);
                    maxX = Math.Max(maxX, x);
                    minY = Math.Min(minY, y);
                    maxY = Math.Max(maxY, y);
                    points[i].X = (float)(x * RegionExtern.Zoom_factor);
                    points[i].Y = (float)(y * RegionExtern.Zoom_factor);
                }
                path.AddPolygon(points);
            }
            path.CloseFigure();
            if (realRegion == null)
            {
                realRegion = new Region(path);
            }
            else
            {
                realRegion.Union(path);
            }

            if (Bounds.x1 == 0 && Bounds.x2 == 0 && Bounds.y1 == 0 && Bounds.y2 == 0) //首个添加的Polygon
            {
                Bounds = new DbRect(minX, minY, maxX, maxY);
            }
            else  //添加到现有的
            {
                minX = Math.Min(minX, (float)Bounds.x1);
                maxX = Math.Max(maxX, (float)Bounds.x2);
                minY = Math.Min(minY, (float)Bounds.y1);
                maxY = Math.Max(maxY, (float)Bounds.y2);
                Bounds = new DbRect(minX, minY, maxX, maxY);
            }

        }
    }
}
