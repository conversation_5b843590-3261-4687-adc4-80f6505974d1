﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using MasterCom.RAMS.Model;
using MasterCom.RAMS.Func;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    partial class NRPingPangResultForm : MinCloseForm
    {
        NRHandOverPingPangAna pingPangAna;
        public NRPingPangResultForm()
            : base(MainModel.GetInstance())
        {
            InitializeComponent();
            DisposeWhenClose = true;

            gvSeqPair.FocusedRowChanged += GvSeqPair_FocusedRowChanged;
            gvSeqPair.DoubleClick += GvSeqPair_DoubleClick;
            btnAnalyze.Click += BtnAnalyze_Click;
            miExportExcel.Click += MiExportExcel_Click;

            this.layer = new NRPingPangLayer();
        }

        public override void ReleaseResources()
        {
            if (layer != null)
            {
                layer.Clear();
            }
        }

        public void FillData(object result, NRHandOverPingPangAna anaFunc)
        {
            this.pingPangAna = anaFunc;
            resultFiles = result as List<NRPingPangFile>;
            gcSequence.DataSource = resultFiles;
            gcSequence.RefreshDataSource();
            gcCell.DataSource = resultFiles;
            gcCell.RefreshDataSource();
        }

        private void GvSeqPair_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            // object v = gvSeqPair.GetFocusedRow();
            object v = gv.GetFocusedRow();
            NRPingPangPair pair = v as NRPingPangPair;
            if (pair != null)
            {
                gcDetail.DataSource = pair.Events;
                gcDetail.RefreshDataSource();
            }
        }

        private void GvSeqPair_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.GridView gv = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            object v = gv.GetRow(gv.GetSelectedRows()[0]);
            NRPingPangPair pair = v as NRPingPangPair;
            if (pair != null)
            {
                double sLng = pair.Events[0].SrcCell.EndPointLongitude;
                double sLat = pair.Events[0].SrcCell.EndPointLatitude;
                double eLng = pair.Events[0].TarCell.EndPointLongitude;
                double eLat = pair.Events[0].TarCell.EndPointLatitude;
                if (!double.IsNaN(sLng) && !double.IsNaN(sLat)
                    && !double.IsNaN(eLng) && !double.IsNaN(eLat))
                {
                    layer.DrawPingPangLines(sLng, sLat, eLng, eLat);
                    MainModel.MainForm.GetMapForm().GoToView((sLng + eLng) / 2 , (sLat + eLat) / 2);
                }
            }
        }

        private void BtnAnalyze_Click(object sender, EventArgs e)
        {
            NRPingPangCondition cond = new NRPingPangCondition();
            cond.TimeLimit = (int)numTimeLimit.Value;
            cond.IsLimitSpeed = chkSpeedLimit.Checked;
            cond.SpeedLimitMin = (int)numSpeedMin.Value;
            cond.SpeedLimitMax = (int)numSpeedMax.Value;
            if (cond.IsLimitSpeed && cond.SpeedLimitMin >= cond.SpeedLimitMax)
            {
                MessageBox.Show("最小速度必须小于最大速度", this.Text, MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            gcDetail.DataSource = null;
            gcDetail.RefreshDataSource();
            pingPangAna.Analyzer.PingPangCond = cond;
            //pingPangAna.DoQuery();
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            if (resultFiles == null)
            {
                return;
            }

            #region 切换序列
            List<NPOIRow> seqTables = new List<NPOIRow>();
            NPOIRow seqTitleRow = new NPOIRow();
            seqTitleRow.AddCellValue("文件序号");
            seqTitleRow.AddCellValue("文件名称");
            seqTitleRow.AddCellValue("乒乓切换组数");
            seqTitleRow.AddCellValue("切换序号");
            seqTitleRow.AddCellValue("切换间隔");
            seqTitleRow.AddCellValue("事件序号");
            seqTitleRow.AddCellValue("源小区");
            seqTitleRow.AddCellValue("目标小区");
            seqTitleRow.AddCellValue("切换前场强");
            seqTitleRow.AddCellValue("切换后场强");
            seqTitleRow.AddCellValue("切换前质量");
            seqTitleRow.AddCellValue("切换后质量");
            seqTables.Add(seqTitleRow);
            foreach (NRPingPangFile ppFile in resultFiles)
            {
                List<object> fileValues = new List<object>();
                fileValues.Add(ppFile.SN);
                fileValues.Add(ppFile.FileName);
                fileValues.Add(ppFile.PairCount);
                foreach (NRPingPangPair ppPair in ppFile.Pairs)
                {
                    List<object> pairValues = new List<object>(fileValues);
                    pairValues.Add(ppPair.SN);
                    pairValues.Add(ppPair.Interval);
                    foreach (NRPingPangEvent ppEvt in ppPair.Events)
                    {
                        List<object> evtValues = new List<object>(pairValues);
                        evtValues.Add(ppEvt.SN);
                        evtValues.Add(ppEvt.SrcCellName);
                        evtValues.Add(ppEvt.TarCellName);
                        evtValues.Add(ppEvt.BeforeRsrp);
                        evtValues.Add(ppEvt.AfterRsrp);
                        evtValues.Add(ppEvt.BeforeSinr);
                        evtValues.Add(ppEvt.AfterSinr);

                        NPOIRow seqRow = new NPOIRow();
                        seqRow.cellValues.AddRange(evtValues);
                        seqTables.Add(seqRow);
                    }
                }
            }
            #endregion

            #region 相关小区
            List<NPOIRow> cellTable = new List<NPOIRow>();
            List<List<object>> objs = GridViewTransfer.Transfer(gcCell);
            foreach (List<object> row in objs)
            {
                NPOIRow cellRow = new NPOIRow();
                cellRow.cellValues.AddRange(row);
                cellTable.Add(cellRow);
            }
            #endregion

            ExcelNPOIManager.ExportToExcel(new List<List<NPOIRow>>() { seqTables, cellTable },
                new List<string>() { "切换序列", "切换小区" });
        }

        private List<NRPingPangFile> resultFiles = null;

        private NRPingPangLayer layer;
    }
}
