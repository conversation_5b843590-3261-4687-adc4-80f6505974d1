﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Net;
using System.Windows.Forms;
using MasterCom.RAMS.Model;
using MasterCom.Util;
using MasterCom.RAMS.Model.Interface;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Net;
using MasterCom.MTGis;
using MasterCom.RAMS.Func.SystemSetting;
using MapWinGIS;
using MasterCom.RAMS.ZTFunc;

namespace MasterCom.RAMS.ZTFunc
{
    public class ZTGDProbTaskMng : DIYStatQuery
    {
        public ZTGDProbTaskMng(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "工单指派及查询管理"; }
        }

        protected override MasterCom.RAMS.UserMng.LogInfoItem getRecLogItem()
        {
            return new MasterCom.RAMS.UserMng.LogInfoItem(2, 20000, 20040, "1+N派单管理");
        }
        public override bool IsNeedSetQueryCondition
        {
            get
            {
                return false;
            }
        }

        protected override bool isValidCondition()
        {
            return true;
        }

        protected override StatTbToken getBranchToken()
        {
            return StatTbToken.log;
        }

        ZTGDProbTaskMngForm ZTGDProbTaskMngForm = null;
        Dictionary<string, CityGridInfo> dicCityGrid = new Dictionary<string,CityGridInfo>();
        Dictionary<string, RoundInfo> RoundDic = new Dictionary<string,RoundInfo>();
        protected override void query()
        {
            if (dicCityGrid == null)
                dicCityGrid = new Dictionary<string, CityGridInfo>();
            if (RoundDic == null)
                RoundDic = new Dictionary<string, RoundInfo>();
            dicCityGrid.Clear();
            RoundDic.Clear();
            if (ZTGDProbTaskMngForm == null)
            {
                ZTGDProbTaskMngForm = new ZTGDProbTaskMngForm(MainModel);
            }
            WaitBox.Show("正在查询...", doStat);
            ZTGDProbTaskMngForm.fillData(dicCityGrid, RoundDic);
            ZTGDProbTaskMngForm.ShowDialog();
        }

        private void doStat()
        {
            WaitBox.Text = "正在获取网格信息...";
            WaitBox.ProgressPercent = 35;
            if (dicCityGrid == null || dicCityGrid.Count == 0)
            {
                dicCityGrid = new Dictionary<string, CityGridInfo>();
                DIVQueryCityGrid queryGird = new DIVQueryCityGrid(MainModel);
                queryGird.Query();
                dicCityGrid = queryGird.dicCityGrid;
            }

            WaitBox.Text = "正在获取轮次信息...";
            WaitBox.ProgressPercent = 65;
            if (RoundDic == null || RoundDic.Count == 0)
            {
                RoundDic = new Dictionary<string, RoundInfo>();
                DIVQueryRound queryRound = new DIVQueryRound(MainModel);
                queryRound.Query();
                RoundDic = queryRound.RoundDic;
            }
            WaitBox.Close();
        }
    }

    #region 网格查询
    /// <summary>
    /// 城市网格信息
    /// </summary>
    public class CityGridInfo
    {
        /// <summary>
        /// 城市名
        /// </summary>
        public string strCity { get; set; }
        /// <summary>
        /// 城市名
        /// </summary>
        public string strGridType { get; set; }
        /// <summary>
        /// 获取格式化后的网格A
        /// </summary>
        public Dictionary<string, int> getFormatGridADic { get; set; }
        /// <summary>
        /// 获取格式化后的网格B
        /// </summary>
        public Dictionary<string, int> getFormatGridBDic { get; set; }
        /// <summary>
        /// 获取格式化后的网格C
        /// </summary>
        public Dictionary<string, int> getFormatGridCDic { get; set; }
        public List<string> getAllGridList
        {
            get
            {
                List<string> list = new List<string>();               
                list.AddRange(getFormatGridADic.Keys);
                list.AddRange(getFormatGridBDic.Keys);
                return list;
            }
        }
        public CityGridInfo()
        {
            this.getFormatGridADic = new Dictionary<string, int>();
            getFormatGridADic.Add("全部", 0);
            this.getFormatGridBDic = new Dictionary<string, int>();
            this.getFormatGridCDic = new Dictionary<string, int>();
            this.strCity = "";
            this.strGridType = "";
        }

        public string getGridDicValue(string iGridID,string type)
        {
            string str = "";
            if (type == "key")
            {
                str = getKey(iGridID, str);
            }
            if (type == "value")
            {
                str = getValue(iGridID, str);
            }
            return str;
        }

        private string getKey(string iGridID, string str)
        {
            foreach (string key in getFormatGridADic.Keys)
            {
                if (getFormatGridADic[key].ToString() == iGridID)
                {
                    str = key;
                    break;
                }
            }
            if (str == "")
            {
                foreach (string key in getFormatGridBDic.Keys)
                {
                    if (getFormatGridBDic[key].ToString() == iGridID)
                    {
                        str = key;
                        break;
                    }
                }
            }

            return str;
        }

        private string getValue(string iGridID, string str)
        {
            foreach (string key in getFormatGridADic.Keys)
            {
                if (key == iGridID)
                {
                    str = getFormatGridADic[key].ToString();
                    break;
                }
            }
            if (str == "")
            {
                foreach (string key in getFormatGridBDic.Keys)
                {
                    if (key == iGridID)
                    {
                        str = getFormatGridBDic[key].ToString();
                        break;
                    }
                }
            }

            return str;
        }
    }

    /// <summary>
    /// 查询网格
    /// </summary>
    public class DIVQueryCityGrid : DIYSQLBase
    {
        public DIVQueryCityGrid(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "查询地市网格表"; }
        }

        protected override string getSqlTextString()
        {
            string SQL = "exec [DTASYSTEM].[dbo].[proc_tb_auto_n_gridinfo]";
            return SQL;
        }

        public Dictionary<string, CityGridInfo> dicCityGrid { get; set; }
        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[4];
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx] = E_VType.E_Int;

            return rType;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            dicCityGrid = new Dictionary<string, CityGridInfo>();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    string strCity = package.Content.GetParamString();
                    string strGridType = package.Content.GetParamString();
                    string gridTypeID = package.Content.GetParamString();
                    int gridNum = package.Content.GetParamInt();

                    CityGridInfo grid;
                    if (!dicCityGrid.TryGetValue(strCity, out grid))
                    {
                        dicCityGrid[strCity] = new CityGridInfo();
                    }
                    switch (strGridType)
                    {
                        case "A":
                            dicCityGrid[strCity].getFormatGridADic[gridTypeID] = gridNum;
                            break;
                        case "B":
                            dicCityGrid[strCity].getFormatGridBDic[gridTypeID] = gridNum;
                            break;
                        case "C":
                            dicCityGrid[strCity].getFormatGridCDic[gridTypeID] = gridNum;
                            break;
                    }
                    dicCityGrid[strCity].strCity = strCity;
                    dicCityGrid[strCity].strGridType = strGridType;
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }
    #endregion

    #region 轮次查询
    /// <summary>
    /// 轮次信息
    /// </summary>
    public class RoundInfo
    {
        public int iid { get; set; }
        public string strRound { get; set; }
        public int isTime { get; set; }
        public int ieTime { get; set; }
        public string strcomment { get; set; }

        public DateTime getDateTimeByStartTime
        {
            get
            {
                return JavaDate.GetDateTimeFromMilliseconds(isTime * 1000L);
            }
        }
        public DateTime getDateTimeByEndTime
        {
            get
            {
                return JavaDate.GetDateTimeFromMilliseconds(ieTime * 1000L);
            }
        }
    }

    /// <summary>
    /// 查询轮次
    /// </summary>
    public class DIVQueryRound : DIYSQLBase
    {
        public DIVQueryRound(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "查询轮次表"; }
        }

        protected override string getSqlTextString()
        {
            string SQL = "EXEC [DTASYSTEM].[dbo].proc_tb_auto_n_round";
            return SQL;
        }

        public Dictionary<string, RoundInfo> RoundDic { get; set; }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[5];
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx] = E_VType.E_String;

            return rType;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            RoundDic = new Dictionary<string, RoundInfo>();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    RoundInfo round = new RoundInfo();
                    round.iid = package.Content.GetParamInt();
                    round.strRound = package.Content.GetParamString();
                    round.isTime = package.Content.GetParamInt();
                    round.ieTime = package.Content.GetParamInt();
                    round.strcomment = package.Content.GetParamString();

                    if (!RoundDic.ContainsKey(round.strRound))
                        RoundDic.Add(round.strRound, round);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    }
    #endregion

    #region 网格工单查询
    /// <summary>
    /// 网格工单信息
    /// </summary>
    public class BlockInfo
    {
        public int iid { get; set; }
        public string strcity { get; set; }
        public string strgrid { get; set; }
        public string strround { get; set; }
        public string strNo { get; set; }
        public string strState { get; set; }
        public string strKpiType { get; set; }
        public string strKpiName { get; set; }
        public string strKpiDesc { get; set; }
        public int iCaseDate { get; set; }
        public int iUpdDate { get; set; }
        public string strcomment { get; set; }

        public DateTime getDateTimeByCaseDate
        {
            get
            {
                return JavaDate.GetDateTimeFromMilliseconds(iCaseDate * 1000L);
            }
        }
        public DateTime getDateTimeByUpdDate
        {
            get
            {
                return JavaDate.GetDateTimeFromMilliseconds(iUpdDate * 1000L);
            }
        }
        public string getFormatGridAName
        {
            get
            {
                int iGrid = Convert.ToInt32(strgrid);
                string strGrids = "A" + iGrid.ToString();
                if (iGrid < 10)
                    strGrids = "A0" + iGrid;
                return strGrids;
            }
        }

    }

    public class BlockKey
    {
        public string strCity { get; set; }
        public string strGrid { get; set; }

        public int iSum
        {
            get
            {
                return iDoing + iWaiting + iQualified;
            }
        }

        /// <summary>
        /// 工单状态:正在处理
        /// </summary>
        public int iDoing { get; set; }

        /// <summary>
        /// 工单状态:待评估
        /// </summary>
        public int iWaiting { get; set; }

        /// <summary>
        /// 工单状态:已达标
        /// </summary>
        public int iQualified { get; set; }

        public BlockKey(string strCity, string strGrid)
        {
            this.strCity = strCity;
            this.strGrid = strGrid;
            this.iDoing = 0;
            this.iWaiting = 0;
            this.iQualified = 0;
        }

        /// <summary>
        /// 统计工单状态
        /// </summary>
        /// <param name="strState"></param>
        public void statiSheetState(string strState)
        {
            if (strState.Contains("正在处理"))
            {
                this.iDoing++;
            }
            if (strState.Contains("待评估"))
            {
                this.iWaiting++;
            }
            if (strState.Contains("评估达标"))
            {
                this.iQualified++;
            }
        }

        /// <summary>
        /// 获取格式化网格名称（格式化:A01）
        /// </summary>
        public string getFormatGridAName
        {
            get
            {
                int iGrid = Convert.ToInt32(strGrid);
                string strGrids = "A" + iGrid.ToString();
                if (iGrid < 10)
                    strGrids = "A0" + iGrid;
                return strGrids;
            }
        }
        public override bool Equals(object obj)
        {
            BlockKey other = obj as BlockKey;
            if (other == null)
                return false;

            if (!base.GetType().Equals(obj.GetType()))
                return false;

            return (this.strCity.Equals(other.strCity)
                 && this.strGrid.Equals(other.strGrid));
        }
        public override int GetHashCode()
        {
            return (this.strCity + "," + this.strGrid ).GetHashCode();
        }
    }
    /// <summary>
    /// 网格工单查询
    /// </summary>
    public class DIVQueryBlock : DIYSQLBase
    {
        public DIVQueryBlock(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "查询网格工单信息"; }
        }

        public void setCondition(string strCity, string strGrid, string strNo, int iSRound, int iERound)
        {
            this.strCity = strCity == "全部" ? "''" : "'''" + strCity + "'''";
            this.strGrid = strGrid == "全部" ? "''" : "'''" + strGrid + "'''";
            this.strNo = strNo == "" ? "''" : "'''" + strNo + "'''";
            this.iSRound = iSRound;
            this.iERound = iERound;
        }
        protected override string getSqlTextString()
        {

            string strSQL = string.Format("exec DTASYSTEM.dbo.proc_tb_auto_n_block {0},{1},{2},{3},{4}",
                strCity, strGrid, strNo, iSRound,iERound);
            return strSQL;
        }

        private string strCity;
        private string strGrid;
        private string strNo;
        private int iSRound;
        private int iERound;
        public Dictionary<BlockKey, List<BlockInfo>> blockDic { get; set; }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[12];
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;

            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;

            rType[idx++] = E_VType.E_Int;
            rType[idx] = E_VType.E_String;

            return rType;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            blockDic = new Dictionary<BlockKey, List<BlockInfo>>();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private void fillData(Package package)
        {
            BlockInfo blockInfo = new BlockInfo();
            blockInfo.iid = package.Content.GetParamInt();
            blockInfo.strcity = package.Content.GetParamString();
            blockInfo.strgrid = package.Content.GetParamString();
            blockInfo.strround = package.Content.GetParamString();
            blockInfo.strNo = package.Content.GetParamString();

            blockInfo.strState = package.Content.GetParamString();
            blockInfo.strKpiType = package.Content.GetParamString();
            blockInfo.strKpiName = package.Content.GetParamString();
            blockInfo.strKpiDesc = package.Content.GetParamString();
            blockInfo.iCaseDate = package.Content.GetParamInt();

            blockInfo.iUpdDate = package.Content.GetParamInt();
            blockInfo.strcomment = package.Content.GetParamString();

            BlockKey blockKey = new BlockKey(blockInfo.strcity, blockInfo.getFormatGridAName);

            if (!blockDic.ContainsKey(blockKey))
            {
                blockDic[blockKey] = new List<BlockInfo>();
                blockDic[blockKey].Add(blockInfo);
                blockKey.statiSheetState(blockInfo.strState);
            }
            else
            {
                foreach (BlockKey Tkey in blockDic.Keys)
                {
                    if (Tkey.Equals(blockKey))
                    {
                        Tkey.statiSheetState(blockInfo.strState);
                        blockDic[Tkey].Add(blockInfo);
                        break;
                    }
                }
            }
        }
    }
    #endregion

    #region 工单指派查询
    public class CaseMngInfo
    {
        public string strCity { get; set; }
        public string strGrid { get; set; }
        public string strround { get; set; }
        public string strState { get; set; }
        public int iTestTime { get; set; }
        public int iSendTime { get; set; }
        public string strresult { get; set; }
        public string strcomment { get; set; }
        public string getDateTimeByTestTime
        {
            get
            {
                if (iTestTime == 0)
                    return "";
                return JavaDate.GetDateTimeFromMilliseconds(iTestTime * 1000L).ToString();
            }
        }
        public string getDateTimeBySendTime
        {
            get
            {
                if (iSendTime == 0)
                    return "";
                return JavaDate.GetDateTimeFromMilliseconds(iSendTime * 1000L).ToString();
            }
        }
    }

    public class DIVQueryCaseMng : DIYSQLBase
    {
        public DIVQueryCaseMng(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "查询工单派发信息"; }
        }

        public void setCondition(string strCity, string strround)
        {
            string[] cityTmp = strCity.Split(',');
            for (int idx = 0; idx < cityTmp.Length; idx++)
            {
                if (strCity.Contains("全部"))
                {
                    this.strCity = "全部";
                    break;
                }
                else
                {
                    if (idx != 0)
                        strCity = addStrByStringBuilder(strCity, "'',''");
                    strCity = addStrByStringBuilder(strCity, cityTmp[idx]);
                }
            }
            string[] roundTmp = strround.Split(',');
            for (int idx = 0; idx < roundTmp.Length; idx++)
            {
                if (idx != 0)
                    strround = addStrByStringBuilder(strround, "'',''");
                strround = addStrByStringBuilder(strround, roundTmp[idx]);
            }
        }

        protected string addStrByStringBuilder(string str, string addedStr)
        {
            StringBuilder sb = new StringBuilder(str);
            sb.Append(addedStr);
            return sb.ToString();
        }

        protected override string getSqlTextString()
        {
            string strSQL = "";
            if (!strCity.Contains("全部"))
            {
                strSQL = string.Format("exec DTASYSTEM.dbo.proc_tb_auto_n_casemng '{0}','{1}'", strCity, strround);
            }
            else
            {
                strSQL = string.Format("exec DTASYSTEM.dbo.proc_tb_auto_n_casemng '{0}','{1}'", "", strround);
            }
            return strSQL;
        }

        private string strCity = "";
        private readonly string strround = "";
        public List<CaseMngInfo> CaseMngList { get; set; }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[15];
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_Int;

            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_String;
            rType[idx++] = E_VType.E_String;

            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx++] = E_VType.E_Int;
            rType[idx] = E_VType.E_Int;
            return rType;
        }
        protected override void receiveRetData(ClientProxy clientProxy)
        {
            CaseMngList = new List<CaseMngInfo>();
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    fillData(package);
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }

        private void fillData(Package package)
        {
            CaseMngInfo caseMngInfo = new CaseMngInfo();
            caseMngInfo.strCity = package.Content.GetParamString();
            caseMngInfo.strGrid = package.Content.GetParamString();
            caseMngInfo.strround = package.Content.GetParamString();
            caseMngInfo.strState = package.Content.GetParamString();
            caseMngInfo.iTestTime = package.Content.GetParamInt();

            caseMngInfo.iSendTime = package.Content.GetParamInt();
            caseMngInfo.strresult = package.Content.GetParamString();
            caseMngInfo.strcomment = package.Content.GetParamString();

            int iLTESpeed = package.Content.GetParamInt();
            int iLTEVoice = package.Content.GetParamInt();
            int iLTESynCov = package.Content.GetParamInt();
            int iLTERoadCov = package.Content.GetParamInt();
            int iLTECNT = package.Content.GetParamInt();
            int iLTESinr = package.Content.GetParamInt();
            int iLTECSFB = package.Content.GetParamInt();

            /*特殊处理：B网格不需统计道道路重叠覆盖度，初始化为1，为显示正确状态，故置为0*/
            if (Convert.ToInt32(caseMngInfo.strGrid) > 60
                && iLTESpeed == 0 && iLTEVoice == 0 && iLTESynCov == 0
                && iLTERoadCov == 1
                && iLTECNT == 0 && iLTESinr == 0 && iLTECSFB == 0)
                iLTERoadCov = 0;
            List<int> listTmp = new List<int>() { iLTESpeed, iLTEVoice, iLTESynCov, iLTERoadCov, iLTECNT, iLTESinr, iLTECSFB };

            int i0 = 0, i1 = 0, i2 = 0;
            for (int i = 0; i < listTmp.Count; i++)
            {
                if (listTmp[i] == 0)
                    i0++;
                if (listTmp[i] == 1)
                    i1++;
                if (listTmp[i] == 2)
                    i2++;
            }
            if (listTmp.Count == i0)
                caseMngInfo.strresult += "等待统计指标";
            else if (listTmp.Count == i1)
                caseMngInfo.strresult += "统计指标完成，未派单";
            else if (listTmp.Count == i2)
                caseMngInfo.strresult += "派单完成";
            else if (listTmp.Count == i1 + i2)
                caseMngInfo.strresult += "派单中";
            else if (listTmp.Count == i1 + i0)
                caseMngInfo.strresult += "正在统计指标";
            CaseMngList.Add(caseMngInfo);
        }
    } 
    #endregion

    #region 工单指派 状态更新
    public class DIVUpdateCaseMng : DIYSQLBase
    {
        public DIVUpdateCaseMng(MainModel mainModel)
            : base(mainModel)
        {
        }

        public override string Name
        {
            get { return "工单指派信息更新"; }
        }

        /// <summary>
        /// 条件设置
        /// </summary>
        public void setCondition(string strCity, string strround, string strGrid
            , string strState, string strcomment,int gridType)
        {
            this.strCity = strCity;
            this.strround = strround;
            this.strGrid = strGrid;
            this.gridType = gridType;
          
            this.strState = strState;
            this.strcomment = strcomment;
            this.returnCode = -1;
        }

        public void SetDateTime(string dateTimeTest, string dateTimesSend)
        {
            if (dateTimeTest == "")
            {
                this.itesttime = 0;
                this.isendtime = 0;
            }
            else
            {
                string strTest = dateTimeTest;
                if (dateTimeTest.Contains("年") || dateTimeTest.Contains("月") || dateTimeTest.Contains("日"))
                {
                    strTest = dateTimeTest.Replace("年", @"/").Replace("月", @"/").Replace("日", "") + " 00:00:00";
                }
                else if (dateTimeTest.Length == 8)
                {
                    strTest = string.Format("{0}/{1}/{2}", dateTimeTest.Substring(0, 4), dateTimeTest.Substring(4, 2), dateTimeTest.Substring(6, 2));
                }
                DateTime Testtime;
                if (!DateTime.TryParse(strTest, out Testtime))
                {
                    return;
                }
                this.itesttime = (int)(JavaDate.GetMilliseconds(Testtime) / 1000);
                this.isendtime = (int)(JavaDate.GetMilliseconds(Convert.ToDateTime(dateTimesSend)) / 1000);
            }
        }

        protected override string getSqlTextString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append(string.Format("exec DTASYSTEM.dbo.proc_update_tb_auto_n_round '{0}','{1}','{2}',{3},{4},'{5}','{6}',{7}", strCity, strround, strGrid
                , itesttime, isendtime, strState, strcomment, gridType));
            return sb.ToString();
        }

        private string strCity;
        private string strround;
        private string strGrid;
        private int itesttime;
        private int isendtime;
        private string strState;
        private string strcomment;
        private int gridType;
        /// <summary>
        ///更新状态 
        /// </summary>
        public int returnCode
        {
            get;
            set;
        }

        protected override E_VType[] getSqlRetTypeArr()
        {
            int idx = 0;
            E_VType[] rType = new E_VType[1];
            rType[idx] = E_VType.E_Int;
            return rType;
        }

        protected override void receiveRetData(ClientProxy clientProxy)
        {
            Package package = clientProxy.Package;
            while (true)
            {
                clientProxy.Recieve();
                package.Content.PrepareGetParam();
                if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL)
                {
                    returnCode = package.Content.GetParamInt();
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL_SUCCESS)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.RESTYPE_DIYSEARCH_SQL_FAIL)
                {
                    //
                }
                else if (package.Content.Type == MasterCom.RAMS.Net.ResponseType.END)
                {
                    break;
                }
                else
                {
                    log.Error("Unexpected type: " + package.Content.Type);
                    break;
                }
            }
        }
    } 
    #endregion
}
