﻿namespace MasterCom.RAMS.Func
{
    partial class MapLTECellLayerBaseProperties
    {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.Label labelFeatrueScale;
            System.Windows.Forms.Label labelColor;
            System.Windows.Forms.Label label1;
            System.Windows.Forms.Label label2;
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.radioButtonDrawCurrent = new System.Windows.Forms.RadioButton();
            this.dateTimePickerTime = new System.Windows.Forms.DateTimePicker();
            this.radioButtonDrawSnapshotTime = new System.Windows.Forms.RadioButton();
            this.checkBoxDrawIndoor = new System.Windows.Forms.CheckBox();
            this.checkBoxDrawOutdoor = new System.Windows.Forms.CheckBox();
            this.checkBoxDrawServer = new System.Windows.Forms.CheckBox();
            this.colorSelected = new DevExpress.XtraEditors.ColorEdit();
            this.numMaxScale = new System.Windows.Forms.NumericUpDown();
            this.colorSvrCell = new DevExpress.XtraEditors.ColorEdit();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.panelAntenna = new System.Windows.Forms.Panel();
            this.pictureAntenna = new System.Windows.Forms.PictureBox();
            this.panelCell = new System.Windows.Forms.Panel();
            this.pictureCell = new System.Windows.Forms.PictureBox();
            this.numShapeLength = new System.Windows.Forms.NumericUpDown();
            this.numShapeWidth = new System.Windows.Forms.NumericUpDown();
            labelFeatrueScale = new System.Windows.Forms.Label();
            labelColor = new System.Windows.Forms.Label();
            label1 = new System.Windows.Forms.Label();
            label2 = new System.Windows.Forms.Label();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.colorSelected.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxScale)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorSvrCell.Properties)).BeginInit();
            this.groupBox2.SuspendLayout();
            this.panelAntenna.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureAntenna)).BeginInit();
            this.panelCell.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureCell)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numShapeLength)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numShapeWidth)).BeginInit();
            this.SuspendLayout();
            // 
            // labelFeatrueScale
            // 
            labelFeatrueScale.AutoSize = true;
            labelFeatrueScale.Location = new System.Drawing.Point(11, 27);
            labelFeatrueScale.Name = "labelFeatrueScale";
            labelFeatrueScale.Size = new System.Drawing.Size(107, 12);
            labelFeatrueScale.TabIndex = 82;
            labelFeatrueScale.Text = "图元最大放大比例:";
            labelFeatrueScale.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // labelColor
            // 
            labelColor.AutoSize = true;
            labelColor.Location = new System.Drawing.Point(35, 68);
            labelColor.Name = "labelColor";
            labelColor.Size = new System.Drawing.Size(83, 12);
            labelColor.TabIndex = 80;
            labelColor.Text = "图元选中颜色:";
            labelColor.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new System.Drawing.Point(6, 21);
            label1.Name = "label1";
            label1.Size = new System.Drawing.Size(59, 12);
            label1.TabIndex = 80;
            label1.Text = "图元长度:";
            label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new System.Drawing.Point(143, 21);
            label2.Name = "label2";
            label2.Size = new System.Drawing.Size(59, 12);
            label2.TabIndex = 80;
            label2.Text = "图元宽度:";
            label2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.radioButtonDrawCurrent);
            this.groupBox1.Controls.Add(this.dateTimePickerTime);
            this.groupBox1.Controls.Add(this.radioButtonDrawSnapshotTime);
            this.groupBox1.Location = new System.Drawing.Point(13, 200);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(200, 84);
            this.groupBox1.TabIndex = 91;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "工参时间设置";
            // 
            // radioButtonDrawCurrent
            // 
            this.radioButtonDrawCurrent.AutoSize = true;
            this.radioButtonDrawCurrent.Checked = true;
            this.radioButtonDrawCurrent.Location = new System.Drawing.Point(28, 19);
            this.radioButtonDrawCurrent.Name = "radioButtonDrawCurrent";
            this.radioButtonDrawCurrent.Size = new System.Drawing.Size(47, 16);
            this.radioButtonDrawCurrent.TabIndex = 44;
            this.radioButtonDrawCurrent.TabStop = true;
            this.radioButtonDrawCurrent.Text = "当前";
            this.radioButtonDrawCurrent.UseVisualStyleBackColor = true;
            // 
            // dateTimePickerTime
            // 
            this.dateTimePickerTime.Enabled = false;
            this.dateTimePickerTime.Location = new System.Drawing.Point(82, 45);
            this.dateTimePickerTime.Name = "dateTimePickerTime";
            this.dateTimePickerTime.Size = new System.Drawing.Size(112, 21);
            this.dateTimePickerTime.TabIndex = 43;
            // 
            // radioButtonDrawSnapshotTime
            // 
            this.radioButtonDrawSnapshotTime.AutoSize = true;
            this.radioButtonDrawSnapshotTime.Location = new System.Drawing.Point(28, 47);
            this.radioButtonDrawSnapshotTime.Name = "radioButtonDrawSnapshotTime";
            this.radioButtonDrawSnapshotTime.Size = new System.Drawing.Size(47, 16);
            this.radioButtonDrawSnapshotTime.TabIndex = 45;
            this.radioButtonDrawSnapshotTime.TabStop = true;
            this.radioButtonDrawSnapshotTime.Text = "快照";
            this.radioButtonDrawSnapshotTime.UseVisualStyleBackColor = true;
            // 
            // checkBoxDrawIndoor
            // 
            this.checkBoxDrawIndoor.AutoSize = true;
            this.checkBoxDrawIndoor.Location = new System.Drawing.Point(22, 139);
            this.checkBoxDrawIndoor.Name = "checkBoxDrawIndoor";
            this.checkBoxDrawIndoor.Size = new System.Drawing.Size(96, 16);
            this.checkBoxDrawIndoor.TabIndex = 85;
            this.checkBoxDrawIndoor.Text = "显示室内小区";
            this.checkBoxDrawIndoor.UseVisualStyleBackColor = true;
            // 
            // checkBoxDrawOutdoor
            // 
            this.checkBoxDrawOutdoor.AutoSize = true;
            this.checkBoxDrawOutdoor.Location = new System.Drawing.Point(22, 169);
            this.checkBoxDrawOutdoor.Name = "checkBoxDrawOutdoor";
            this.checkBoxDrawOutdoor.Size = new System.Drawing.Size(96, 16);
            this.checkBoxDrawOutdoor.TabIndex = 84;
            this.checkBoxDrawOutdoor.Text = "显示室外小区";
            this.checkBoxDrawOutdoor.UseVisualStyleBackColor = true;
            // 
            // checkBoxDrawServer
            // 
            this.checkBoxDrawServer.AutoSize = true;
            this.checkBoxDrawServer.Location = new System.Drawing.Point(22, 109);
            this.checkBoxDrawServer.Name = "checkBoxDrawServer";
            this.checkBoxDrawServer.Size = new System.Drawing.Size(96, 16);
            this.checkBoxDrawServer.TabIndex = 83;
            this.checkBoxDrawServer.Text = "显示主服小区";
            this.checkBoxDrawServer.UseVisualStyleBackColor = true;
            // 
            // colorSelected
            // 
            this.colorSelected.EditValue = System.Drawing.Color.Red;
            this.colorSelected.Location = new System.Drawing.Point(124, 63);
            this.colorSelected.Name = "colorSelected";
            this.colorSelected.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorSelected.Properties.ShowWebColors = false;
            this.colorSelected.Size = new System.Drawing.Size(89, 21);
            this.colorSelected.TabIndex = 93;
            // 
            // numMaxScale
            // 
            this.numMaxScale.Location = new System.Drawing.Point(124, 25);
            this.numMaxScale.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.numMaxScale.Name = "numMaxScale";
            this.numMaxScale.Size = new System.Drawing.Size(89, 21);
            this.numMaxScale.TabIndex = 94;
            this.numMaxScale.Value = new decimal(new int[] {
            6000,
            0,
            0,
            0});
            // 
            // colorSvrCell
            // 
            this.colorSvrCell.EditValue = System.Drawing.Color.Red;
            this.colorSvrCell.Location = new System.Drawing.Point(124, 105);
            this.colorSvrCell.Name = "colorSvrCell";
            this.colorSvrCell.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorSvrCell.Properties.ShowWebColors = false;
            this.colorSvrCell.Size = new System.Drawing.Size(89, 21);
            this.colorSvrCell.TabIndex = 93;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.panelAntenna);
            this.groupBox2.Controls.Add(this.panelCell);
            this.groupBox2.Controls.Add(this.numShapeWidth);
            this.groupBox2.Controls.Add(this.numShapeLength);
            this.groupBox2.Controls.Add(label2);
            this.groupBox2.Controls.Add(label1);
            this.groupBox2.Location = new System.Drawing.Point(228, 25);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(303, 259);
            this.groupBox2.TabIndex = 95;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "图元形状";
            // 
            // panelAntenna
            // 
            this.panelAntenna.AutoScroll = true;
            this.panelAntenna.Controls.Add(this.pictureAntenna);
            this.panelAntenna.Location = new System.Drawing.Point(8, 149);
            this.panelAntenna.Name = "panelAntenna";
            this.panelAntenna.Size = new System.Drawing.Size(289, 100);
            this.panelAntenna.TabIndex = 83;
            // 
            // pictureAntenna
            // 
            this.pictureAntenna.Location = new System.Drawing.Point(6, 7);
            this.pictureAntenna.Name = "pictureAntenna";
            this.pictureAntenna.Size = new System.Drawing.Size(249, 100);
            this.pictureAntenna.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureAntenna.TabIndex = 1;
            this.pictureAntenna.TabStop = false;
            // 
            // panelCell
            // 
            this.panelCell.AutoScroll = true;
            this.panelCell.Controls.Add(this.pictureCell);
            this.panelCell.Location = new System.Drawing.Point(8, 43);
            this.panelCell.Name = "panelCell";
            this.panelCell.Size = new System.Drawing.Size(289, 100);
            this.panelCell.TabIndex = 82;
            // 
            // pictureCell
            // 
            this.pictureCell.Location = new System.Drawing.Point(6, 4);
            this.pictureCell.Name = "pictureCell";
            this.pictureCell.Size = new System.Drawing.Size(249, 100);
            this.pictureCell.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureCell.TabIndex = 0;
            this.pictureCell.TabStop = false;
            // 
            // numShapeLength
            // 
            this.numShapeLength.DecimalPlaces = 2;
            this.numShapeLength.Increment = new decimal(new int[] {
            5,
            0,
            0,
            65536});
            this.numShapeLength.Location = new System.Drawing.Point(71, 18);
            this.numShapeLength.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numShapeLength.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numShapeLength.Name = "numShapeLength";
            this.numShapeLength.Size = new System.Drawing.Size(47, 21);
            this.numShapeLength.TabIndex = 81;
            this.numShapeLength.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // numShapeWidth
            // 
            this.numShapeWidth.DecimalPlaces = 2;
            this.numShapeWidth.Increment = new decimal(new int[] {
            5,
            0,
            0,
            65536});
            this.numShapeWidth.Location = new System.Drawing.Point(199, 18);
            this.numShapeWidth.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numShapeWidth.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numShapeWidth.Name = "numShapeWidth";
            this.numShapeWidth.Size = new System.Drawing.Size(47, 21);
            this.numShapeWidth.TabIndex = 81;
            this.numShapeWidth.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // LTECellLayerBaseProperties
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.numMaxScale);
            this.Controls.Add(this.colorSvrCell);
            this.Controls.Add(this.colorSelected);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(labelFeatrueScale);
            this.Controls.Add(this.checkBoxDrawIndoor);
            this.Controls.Add(this.checkBoxDrawOutdoor);
            this.Controls.Add(this.checkBoxDrawServer);
            this.Controls.Add(labelColor);
            this.Name = "LTECellLayerBaseProperties";
            this.Size = new System.Drawing.Size(546, 324);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.colorSelected.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxScale)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorSvrCell.Properties)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.panelAntenna.ResumeLayout(false);
            this.panelAntenna.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureAntenna)).EndInit();
            this.panelCell.ResumeLayout(false);
            this.panelCell.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureCell)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numShapeLength)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numShapeWidth)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.RadioButton radioButtonDrawCurrent;
        private System.Windows.Forms.DateTimePicker dateTimePickerTime;
        private System.Windows.Forms.RadioButton radioButtonDrawSnapshotTime;
        private System.Windows.Forms.CheckBox checkBoxDrawIndoor;
        private System.Windows.Forms.CheckBox checkBoxDrawOutdoor;
        private System.Windows.Forms.CheckBox checkBoxDrawServer;
        private DevExpress.XtraEditors.ColorEdit colorSelected;
        private System.Windows.Forms.NumericUpDown numMaxScale;
        private DevExpress.XtraEditors.ColorEdit colorSvrCell;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.NumericUpDown numShapeLength;
        private System.Windows.Forms.PictureBox pictureCell;
        private System.Windows.Forms.PictureBox pictureAntenna;
        private System.Windows.Forms.Panel panelAntenna;
        private System.Windows.Forms.Panel panelCell;
        private System.Windows.Forms.NumericUpDown numShapeWidth;
    }
}
