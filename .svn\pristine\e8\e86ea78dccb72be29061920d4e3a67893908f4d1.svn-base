﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

using MasterCom.Util;
using MasterCom.MTGis;

using DevExpress.XtraCharts;
using MasterCom.Util.UiEx;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class LteMgrsCoverageRangeResult : LteMgrsResultControlBase
    {
        LteMgrsFuncItem funcItem = null;
        List<LteMgrsDrawItem> drawList = new List<LteMgrsDrawItem>();

        public LteMgrsCoverageRangeResult()
        {
            InitializeComponent();
            InitCbxFreqType();
            cbxFreqType.SelectedIndexChanged += CbxFreqType_SelectedChanged;
            miExportExcel.Click += MiExportExcel_Click;
            miEditRange.Click += MiEditRange_Click;
            miShowChart.Click += MiShowChart_Click;

            miExportWord.Click += base.MiExportWord_Click;
            miExportAllExcel.Click += base.MiExportExcelAll_Click;
        }

        public override string Desc
        {
            get { return "重叠覆盖度"; }
        }


        public override void DrawOnLayer()
        {
            if (funcItem == null)
            {
                return;
            }

            LteMgrsCoverageCondition cond = funcItem.FuncCondtion as LteMgrsCoverageCondition;
            cond.FreqType = (LteMgrsCoverageBandType)EnumDescriptionAttribute.Parse(typeof(LteMgrsCoverageBandType), cbxFreqType.SelectedItem as string);
            LteMgrsCoverageRangeStater stater = funcItem.Stater as LteMgrsCoverageRangeStater;
            //List<LteMgrsDrawItem> drawList = stater.GetDrawList(funcItem.CurQueryCitys[funcItem.SelectedCityIndex], cond);//避免重复计算.by JYH
            LteMgrsLayer.DrawList = drawList;
            DbRect rect = LteMgrsLayer.GetDrawBound(drawList);
            if (rect != null)
            {
                MainModel.MainForm.GetMapForm().GoToView(rect);
                SetNormalMapScale();
            }
            LteMgrsLayer.LegendGroup = stater.GetLegend();
            MainModel.RefreshLegend();
        }

        public void FillData(LteMgrsFuncItem funcItem)
        {
            this.funcItem = funcItem;
            LteMgrsCoverageCondition cond = funcItem.FuncCondtion as LteMgrsCoverageCondition;
            cbxFreqType.SelectedItem = EnumDescriptionAttribute.GetText(cond.FreqType);
            RefreshResult();
        }

        protected override void ExportAllExcel(string savePath)
        {
            for (int i = 0; i < cbxFreqType.Items.Count; ++i)
            {
                cbxFreqType.SelectedIndex = i;
                string sheetName = cbxFreqType.SelectedItem.ToString() + Desc;
                string fileName = System.IO.Path.Combine(savePath, sheetName + ".xlsx");
                ExcelNPOIManager.ExportToExcel(gridView1, fileName, sheetName);
            }
        }

        private void CbxFreqType_SelectedChanged(object sender, EventArgs e)
        {
            RefreshResult();
            DrawOnLayer();
        }

        private void MiExportExcel_Click(object sender, EventArgs e)
        {
            ExcelNPOIManager.ExportToExcel(gridView1);
        }

        private void MiShowChart_Click(object sender, EventArgs e)
        {
            LteMgrsChartForm chartForm = MainModel.GetObjectFromBlackboard(typeof(LteMgrsChartForm).FullName) as LteMgrsChartForm;
            if (chartForm == null || chartForm.IsDisposed)
            {
                chartForm = new LteMgrsChartForm(MainModel);
            }

            LteMgrsCoverageRangeStater stater = funcItem.Stater as LteMgrsCoverageRangeStater;
            ChartControl chart = stater.GetChart(gridControl1.DataSource as DataTable);
            chartForm.FillChart(chart, "场强分布占比图表");
            if (!chartForm.Visible)
            {
                chartForm.Show(MainModel.MainForm);
            }
        }

        private void MiEditRange_Click(object sender, EventArgs e)
        {
            LteMgrsColorRangeSettingDlg dlg = new LteMgrsColorRangeSettingDlg();
            dlg.FixMinMax(LteMgrsCoverageRangeStater.Ranges.Minimum, LteMgrsCoverageRangeStater.Ranges.Maximum);
            dlg.MakeRangeModeOnly();
            dlg.FillColorRanges(LteMgrsCoverageRangeStater.Ranges.ColorRanges);
            dlg.InvalidatePointColor = LteMgrsCoverageRangeStater.Ranges.InvalidColor;

            if (DialogResult.OK != dlg.ShowDialog(this))
            {
                return;
            }
            LteMgrsCoverageRangeStater.Ranges.ColorRanges = new List<MasterCom.MControls.ColorRange>(dlg.ColorRanges);
            LteMgrsCoverageRangeStater.Ranges.InvalidColor = dlg.InvalidatePointColor;
            LteMgrsCoverageRangeStater.Ranges.SaveColorRange("Coverage");

            RefreshResult();
            LteMgrsCoverageRangeStater stater = funcItem.Stater as LteMgrsCoverageRangeStater;
            LteMgrsLayer.LegendGroup = stater.GetLegend();
            DrawOnLayer();
        }

        private void InitCbxFreqType()
        {
            cbxFreqType.Items.Clear();
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.All));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.Top));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleD));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleD1));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleD2));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleD_38098));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleF));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleF1));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleF2));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.TopEarfcn));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleD_40936));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleD_40940));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleE_38950));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.SingleE_39148));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.FDD1_3683));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.FDD1_3692));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.FDD2_1259));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.FDD2_1300));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.FDD2_1309));
            cbxFreqType.Items.Add(EnumDescriptionAttribute.GetText(LteMgrsCoverageBandType.FDD2_1359));
            cbxFreqType.SelectedIndex = 0;
        }

        private void RefreshResult()
        {
            drawList.Clear();
            LteMgrsCoverageCondition cond = funcItem.FuncCondtion as LteMgrsCoverageCondition;
            cond.FreqType = (LteMgrsCoverageBandType)EnumDescriptionAttribute.Parse(typeof(LteMgrsCoverageBandType), cbxFreqType.SelectedItem as string);
            LteMgrsCoverageRangeStater stater = funcItem.Stater as LteMgrsCoverageRangeStater;
            DataTable table = stater.GetTable(funcItem.CurQueryCitys[funcItem.SelectedCityIndex], cond, ref drawList);
            gridControl1.DataSource = table;
            gridControl1.RefreshDataSource();
            gridView1.PopulateColumns();

            LteMgrsChartForm chartForm = MainModel.GetObjectFromBlackboard(typeof(LteMgrsChartForm).FullName) as LteMgrsChartForm;
            if (chartForm != null && !chartForm.IsDisposed)
            {
                miShowChart.PerformClick();
            }
        }

        protected override bool ExportWord(MasterCom.RAMS.Model.WordControl word, string title)
        {
            if (title == "不分段重叠覆盖度渲染" || title == "最强归属段重叠覆盖度渲染" || title == "D频段重叠覆盖度渲染" || title == "F频段重叠覆盖度渲染")
            {
                WaitTextBox.Text = "正在导出 " + title + "...";

                setSelectIndex(title);

                this.DrawOnLayer();
                string text = cbxFreqType.SelectedItem.ToString() + "重叠覆盖度";

                word.InsertText("整体渲染效果如下：", "正文");
                word.NewLine();
                word.InsertPicture(LteMgrsScreenShoter.GisScreenshot("LTE_" + text));
                word.NewLine();
                return true;
            }
            else if (title == "不分段重叠覆盖度统计" || title == "最强归属段重叠覆盖度统计"
                    || title == "D频段重叠覆盖度统计" || title == "F频段重叠覆盖度统计")
            {
                WaitTextBox.Text = "正在导出 " + title + "...";

                setSelectIndex(title);

                string text = "按照网格进行汇总统计结果如下：";
                word.InsertText(text, "正文");
                word.NewLine();
                (word as LteMgrsWordControl).InsertGridView(gridView1, LteMgrsCoverageRangeStater.Ranges.ColorRanges);
                word.NewLine();

                LteMgrsCoverageRangeStater stater = funcItem.Stater as LteMgrsCoverageRangeStater;
                DataTable dt = gridControl1.DataSource as DataTable;
                text = "网格级占比图表如下：";
                word.InsertText(text, "正文");
                word.NewLine();
                ChartControl chart = stater.GetChart(dt);
                chart.Dock = DockStyle.Fill;
                Form tmpForm = new Form();
                tmpForm.Size = new Size(600, 400);
                tmpForm.Controls.Add(chart);

                string fileName = "LTE_" + title;
                string picFile = LteMgrsScreenShoter.ChartScreenshot(chart, dt.Rows.Count, fileName);
                word.InsertPicture(picFile);
                tmpForm.Dispose();
                word.NewLine();

                return true;
            }
            return false;
        }

        private void setSelectIndex(string title)
        {
            if (title == "不分段重叠覆盖度统计") cbxFreqType.SelectedIndex = 0;
            else if (title == "最强归属段重叠覆盖度统计") cbxFreqType.SelectedIndex = 1;
            else if (title == "D频段重叠覆盖度统计") cbxFreqType.SelectedIndex = 2;
            else if (title == "F频段重叠覆盖度统计") cbxFreqType.SelectedIndex = 5;
            else if (title == "最强信号频点重叠覆盖度渲染") cbxFreqType.SelectedIndex = 6;
        }
    }
}
