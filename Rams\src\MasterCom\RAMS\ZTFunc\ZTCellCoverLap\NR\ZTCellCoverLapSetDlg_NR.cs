﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using MasterCom.RAMS.Func;

namespace MasterCom.RAMS.ZTFunc
{
    public partial class ZTCellCoverLapSetDlg_NR : BaseDialog
    {
        public ZTCellCoverLapSetDlg_NR()
        {
            InitializeComponent();
            numDistanceMin.Enabled = false;
            numDistanceMax.Enabled = false;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        internal bool GetSettingFilterRet(CellCoverLapCondition_NR settingCondition)
        {
            settingCondition.CurFilterRxlev = (int)numFilter.Value;
            if (!cbxSampleCount.Checked)
            {
                settingCondition.CurMinSampleCount = 0;
            }
            else
            {
                settingCondition.CurMinSampleCount = (int)numSampleCount.Value;
            }
            if (!cbxPercent.Checked)
            {
                settingCondition.CurMinPercent = 0;
            }
            else
            {
                settingCondition.CurMinPercent = (int)numPercent.Value * 0.01f;
            }

            if (!chkDistance.Checked)
            {
                settingCondition.CurMinDistance = 0;
                settingCondition.CurMaxDistance = 1000000;
            }
            else
            {
                int minDistance;
                int maxDistance;
                int.TryParse(numDistanceMin.Value.ToString(), out minDistance);
                int.TryParse(numDistanceMax.Value.ToString(), out maxDistance);
                settingCondition.CurMinDistance = minDistance;
                settingCondition.CurMaxDistance = maxDistance;
            }
            settingCondition.NearestCellCount = (int)numNearestCellCount.Value;
            settingCondition.DisFactor = (float)numDisFactor.Value;
            return true;
        }

        private void cbxSampleCount_CheckedChanged(object sender, EventArgs e)
        {
            numSampleCount.Enabled = cbxSampleCount.Checked;
        }

        private void cbxPercent_CheckedChanged(object sender, EventArgs e)
        {
            numPercent.Enabled = cbxPercent.Checked;
        }

        private void chkDistance_CheckedChanged(object sender, EventArgs e)
        {
            numDistanceMin.Enabled = chkDistance.Checked;
            numDistanceMax.Enabled = chkDistance.Checked;
        }
    }

    public class CellCoverLapCondition_NR
    {
        public CellCoverLapCondition_NR()
        {

        }

        public int CurFilterRxlev { get; set; } = -90;
        public int CurMinSampleCount { get; set; } = 0;
        public float CurMinPercent { get; set; } = 0;
        public int CurMinDistance { get; set; } = 0;
        public int CurMaxDistance { get; set; } = 1000000;
        public float DisFactor { get; set; } = 1.6f;
        public int NearestCellCount { get; set; } = 3;
    }
}