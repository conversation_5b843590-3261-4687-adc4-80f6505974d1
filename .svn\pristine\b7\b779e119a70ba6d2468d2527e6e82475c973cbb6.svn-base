﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.Xml;
using System.IO;
using MasterCom.RAMS.ZTFunc;
using MasterCom.Util;
using MasterCom.RAMS.Net;
using MasterCom.MTGis;
using MasterCom.RAMS.Model;

namespace MasterCom.RAMS.BackgroundFunc
{
    public class BackgroundFuncConfigManager
    {
        private BackgroundFuncConfigManager()
        {
            load();
        }

        private static BackgroundFuncConfigManager intance = null;

        public static BackgroundFuncConfigManager GetInstance()
        {
            if (intance == null)
            {
                intance = new BackgroundFuncConfigManager();
            }
            return intance;
        }

        BackgroundFuncBaseSetting baseSetting { get; set; } = BackgroundFuncBaseSetting.GetInstance();
        BackgroundFuncExportSetting exportSetting { get; set; } = BackgroundFuncExportSetting.GetInstance();
        BackgroundFuncRoadSetting roadSetting { get; set; } = BackgroundFuncRoadSetting.GetInstance();
        BackgroundFuncAreaSetting areaSetting { get; set; } = BackgroundFuncAreaSetting.GetInstance();
        BackgroundFuncManager bgFuncManager { get; set; } = BackgroundFuncManager.GetInstance();
        public bool IsNeedLoadCurCityWorkParam
        {
            get { return baseSetting.IsNeedLoadCurCityWorkParam; }
        }
        public bool IsNeedResetCurCityMap
        {
            get { return baseSetting.IsNeedResetCurCityMap; }
        }
        public int ValidUnit
        {
            get { return baseSetting.validUnit; }
        }

        public int ISTime
        {
            get
            {
                return (int)(JavaDate.GetMilliseconds(StartTime) / 1000);
            }
        }

        public int IETime
        {
            get
            {
                return (int)(JavaDate.GetMilliseconds(EndTime) / 1000);
            }
        }
        public DateTime StartTime
        {
            get
            {
                if (baseSetting.queryTimeType == 2)
                {
                    DateTime dateNow = DateTime.Now.Date;
                    return dateNow.AddDays(-1 * (ValidUnit - 1));
                }
                else if (baseSetting.queryTimeType == 1)
                {
                    DateTime dateNow = DateTime.Now.Date;
                    return new DateTime(dateNow.Year, dateNow.Month, 1).AddMonths(-1 * (ValidUnit - 1));
                }
                else
                {
                    return baseSetting.dSTimeSet;
                }
            }
        }
        public DateTime EndTime
        {
            get
            {
                if (baseSetting.queryTimeType != 0)
                {
                    DateTime dateNow = DateTime.Now.Date;
                    return dateNow.AddDays(1);
                }
                else
                {
                    return baseSetting.dETimeSet;
                }
            }
        }
        public string strSelCity
        {
            get
            {
                return baseSetting.strCityNames;
            }
        }
        public List<int> ProjectTypeList
        {
            get 
            {
                List<int> projectTypeList = new List<int>();
                if (baseSetting.IsCheckPrjId)
                {
                    if (!string.IsNullOrEmpty(baseSetting.ProjectIdString))
                    {
                        addProjectTypeList(projectTypeList);
                    }
                }
                else
                {
                    projectTypeList = baseSetting.ProjectTypeList;
                }
                return projectTypeList;
            }
        }

        private void addProjectTypeList(List<int> projectTypeList)
        {
            string[] strArr = baseSetting.ProjectIdString.Split('|');
            foreach (string str in strArr)
            {
                string[] prjIdArr = str.Split(',');
                foreach (string strPrj in prjIdArr)
                {
                    int prjId;
                    if (int.TryParse(strPrj, out prjId) && !projectTypeList.Contains(prjId))
                    {
                        projectTypeList.Add(prjId);
                    }
                }
            }
        }

        public string ProjectType
        {
            get { return baseSetting.projectType; }
        }

        public int QueryTimeType
        {
            get { return baseSetting.queryTimeType; }
        }

        /// <summary>
        /// 按路段专题路段合法长度
        /// </summary>
        public int ValidDistance
        {
            get { return roadSetting.ValidDistance; }
        }

        /// <summary>
        /// 汇聚专题汇聚半径
        /// </summary>
        public int GatherRadius
        {
            get { return areaSetting.gatherRedius; }
        }

        /// <summary>
        /// 汇聚专题统计时间单元
        /// </summary>
        public TimeGatherMode AreaStatTimeMode
        {
            get { return areaSetting.areaStatTimeMode; }
        }

        /// <summary>
        /// 汇聚专题重复统计时间单元数
        /// </summary>
        public int StatTimeUnitCount
        {
            get { return areaSetting.statTimeUnitCount; }
        }

        /// <summary>
        /// 边界区域
        /// </summary>
        public MapWinGIS.Shape RegionBorder
        {
            get { return baseSetting.RegionBorder; }
        }

        public Dictionary<string,List<MapWinGIS.Shape>> regionNameShapeDic
        {
            get { return baseSetting.regionNameShapeDic; }
        }
        public bool AutoExportResult
        {
            get { return exportSetting.AutoExportResult; }
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["BaseSetting"] = baseSetting.Param;
                param["FuncSetting"] = getFuncParam();
                return param;
            }
            set
            {
                if (value != null && value.Count > 0)
                {
                    Dictionary<string, object> param = value;
                    if (param.ContainsKey("BaseSetting"))
                    {
                        baseSetting.Param = (Dictionary<string, object>)param["BaseSetting"];
                    }
                    if (param.ContainsKey("FuncSetting"))
                    {
                        Dictionary<string, object> paramFunc = (Dictionary<string, object>)param["FuncSetting"];
                        setFuncParam(paramFunc);
                    }
                }
            }
        }

        private Dictionary<string, object> getFuncParam()
        {
            Dictionary<string, object> param = new Dictionary<string, object>();
            foreach (BackgroundQueryBase query in bgFuncManager.BackgroundQueryList)
            {
                string strFuncId = query.GetSubFuncIDString();
                param[strFuncId] = query.Param;
            }
            return param;
        }

        private void setFuncParam(Dictionary<string, object> param)
        {
            foreach (BackgroundQueryBase query in bgFuncManager.BackgroundQueryList)
            {
                if (param.ContainsKey(query.GetSubFuncIDString()))
                {
                    string strFuncId = query.GetSubFuncIDString();
                    query.Param = (Dictionary<string, object>)param[strFuncId];
                }
            }
        }

        private enum EDesType
        {
            None = 0,       //不做处理
            Encode = 1,     //加密
            Decode = 2      //解密
        }
        private Dictionary<string, object> getDesDic(Dictionary<string, object> paramDic, EDesType eDesType)
        {
            Dictionary<string, object> decodeDic = new Dictionary<string, object>();
            if (paramDic == null || eDesType == EDesType.None)
            {
                return paramDic;
            }

            foreach (string strKey in paramDic.Keys)
            {
                object objValue = paramDic[strKey];

                string strValue = objValue as string;
                if (strValue != null)
                {
                    strValue = getDesString(strKey, strValue, eDesType);
                    decodeDic[strKey] = strValue;
                }
                else if (objValue is Dictionary<string, object>)
                {
                    Dictionary<string, object> dicValue = objValue as Dictionary<string, object>;
                    decodeDic[strKey] = getDesDic(dicValue, eDesType);
                }
                else
                {
                    decodeDic[strKey] = objValue;
                }
            }
            return decodeDic;
        }

        private string getDesString(string strKey, string strValue, EDesType eDesType)
        {
            string strKeyLower = strKey.ToLower();
            if (strKeyLower.Contains("username") || strKeyLower.Contains("password") 
                || strKeyLower.Contains("pwd"))
            {
                try
                {
                    if (eDesType == EDesType.Decode)
                    {
                        strValue = DES.Decode(strValue);
                    }
                    else if (eDesType == EDesType.Encode)
                    {
                        strValue = DES.Encode(strValue);
                    }
                }
                catch
                {
                    //暂忽略
                }
            }

            return strValue;
        }

        private void load()
        {
            string filePath = Application.StartupPath + @"\config\BackgroundSetting.xml";
            if (!File.Exists(filePath))
            {
                return;
            }
            XmlConfigFile configFile = new XmlConfigFile(filePath);
            XmlElement element = configFile.GetConfig("BackgroundSetting");
            Dictionary < string, object> paramDic = configFile.GetItemValue(element, "BackgroundFunc") as Dictionary<string, object>;
            Param = getDesDic(paramDic, EDesType.Decode);
        }

        public bool Save()
        {
            XmlConfigFile configFile = new XmlConfigFile();
            XmlElement element = configFile.AddConfig("BackgroundSetting");
            Dictionary<string, object> paramDic = getDesDic(Param, EDesType.Encode);
            configFile.AddItem(element, "BackgroundFunc", paramDic);
            configFile.Save(Application.StartupPath + @"\config\BackgroundSetting.xml");
            return true;
        }
    }

    public class BackgroundFuncBaseSetting
    {
        private BackgroundFuncBaseSetting()
        {
        }

        private static BackgroundFuncBaseSetting intance = null;

        public static BackgroundFuncBaseSetting GetInstance()
        {
            if (intance == null)
            {
                intance = new BackgroundFuncBaseSetting();
            }
            return intance;
        }

        public bool IsNeedLoadCurCityWorkParam { get; set; } = true;//切换地市时是否重新加载对应地市工参
        public bool IsNeedResetCurCityMap { get; set; } = true;//统计地市是否需配置边界图层
        public int validUnit { get; set; } = 3;

        /// <summary>
        /// 后台分析时用到的项目ID（get by Project ID or Name）
        /// </summary>
        public string projectType
        {
            get
            {
                if (IsCheckPrjId)
                {
                    return ProjectIdString;
                }
                else
                {
                    if (ProjectTypeList != null && ProjectTypeList.Count > 0)
                    {
                        StringBuilder strb = new StringBuilder();
                        foreach (int prjId in ProjectTypeList)
                        {
                            strb.Append(prjId + ",");
                        }
                        if (strb.Length > 1)
                        {
                            return strb.Remove(strb.Length - 1, 1).ToString();
                        }
                    } 
                }
                return "";
            }
        }

        public bool IsCheckPrjId { get; set; } = true;
        /// <summary>
        /// 设置的项目ID（get by Project ID,可能包含“|”）
        /// </summary>
        public string ProjectIdString { get; set; } = "";

        /// <summary>
        /// 设置的项目ID（get by Project Name）
        /// </summary>
        public List<int> ProjectTypeList { get; set; } = new List<int>();
        public int queryTimeType { get; set; } = 1;//0：按指定时段分析  1：按月分析  2：按天分析
        public DateTime dSTimeSet { get; set; } = DateTime.Now;
        public DateTime dETimeSet { get; set; } = DateTime.Now;
        public string strCityNames { get; set; } = "";
        public Dictionary<int, MapInfo> mapsDic { get; set; } = new Dictionary<int, MapInfo>();
        List<MapWinGIS.Shape> shapeList = new List<MapWinGIS.Shape>();

        public bool IsDoByPeriod { get; set; } = false;//是否每隔几分钟执行一次
        public int DoByEveryDay_Hour { get; set; } = 1;//每天几点定时执行一次
        public float DoByPeriod_Minutes { get; set; } = 10;//每隔多少分钟执行一次

        BackgroundFuncExportSetting exportSetting { get; set; } = BackgroundFuncExportSetting.GetInstance();
        BackgroundFuncAreaSetting areaSetting { get; set; } = BackgroundFuncAreaSetting.GetInstance();
        BackgroundFuncRoadSetting roadSetting { get; set; } = BackgroundFuncRoadSetting.GetInstance();

        public MapWinGIS.Shape RegionBorder
        {
            get { return ShapeHelper.CombineMultiRegions(shapeList); }
        }

        public Dictionary<string, List<MapWinGIS.Shape>> regionNameShapeDic
        {
            get { return ShapeHelper.ShapeAndRegionNameDic; }
        }

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["IsNeedLoadCurCityWorkParam"] = IsNeedLoadCurCityWorkParam;
                param["IsNeedResetCurCityMap"] = IsNeedResetCurCityMap;
                param["ValidUnit"] = validUnit;
                param["IsCheckPrjId"] = IsCheckPrjId;
                param["ProjectType"] = ProjectIdString;
                param["ProjectTypeList"] = ProjectTypeList;
                param["QueryTimeType"] = queryTimeType;
                param["DSTimeSet"] = dSTimeSet.ToString("yyyy-MM-dd");
                param["DETimeSet"] = dETimeSet.ToString("yyyy-MM-dd");
                param["StrCityNames"] = strCityNames;
                param["MapInfoRegion"] = getMapParam();
                param["IsDoByPeriod"] = IsDoByPeriod;
                param["DoByEveryDay_Hour"] = DoByEveryDay_Hour;
                param["DoByPeriod_Minutes"] = DoByPeriod_Minutes;
                param["ExportSetting"] = exportSetting.Param;
                param["RoadSetting"] = roadSetting.Param;
                param["AreaSetting"] = areaSetting.Param;
                return param;
            }
            set
            {
                if (value != null && value.Count > 0)
                {
                    Dictionary<string, object> param = value;
                    setParam(param);
                }
            }
        }

        private void setParam(Dictionary<string, object> param)
        {
            if (param.ContainsKey("IsNeedLoadCurCityWorkParam"))
            {
                IsNeedLoadCurCityWorkParam = (bool)param["IsNeedLoadCurCityWorkParam"];
            }
            if (param.ContainsKey("IsNeedResetCurCityMap"))
            {
                IsNeedResetCurCityMap = (bool)param["IsNeedResetCurCityMap"];
            }
            if (param.ContainsKey("ValidUnit"))
            {
                validUnit = int.Parse(param["ValidUnit"].ToString());
            }
            if (param.ContainsKey("IsCheckPrjId") && param["IsCheckPrjId"] != null)
            {
                IsCheckPrjId = (bool)param["IsCheckPrjId"];
            }
            setProject(param);
            if (param.ContainsKey("QueryTimeType"))
            {
                queryTimeType = int.Parse(param["QueryTimeType"].ToString());
            }
            setTime(param);
            setCityName(param);
            if (param.ContainsKey("MapInfoRegion"))
            {
                setMapParam(param["MapInfoRegion"] as Dictionary<string, object>);
            }
            setPeriod(param);
            if (param.ContainsKey("ExportSetting"))
            {
                exportSetting.Param = param["ExportSetting"] as Dictionary<string, object>;
            }
            if (param.ContainsKey("RoadSetting"))
            {
                roadSetting.Param = param["RoadSetting"] as Dictionary<string, object>;
            }
            if (param.ContainsKey("AreaSetting"))
            {
                areaSetting.Param = param["AreaSetting"] as Dictionary<string, object>;
            }
        }

        private void setPeriod(Dictionary<string, object> param)
        {
            if (param.ContainsKey("IsDoByPeriod") && param["IsDoByPeriod"] != null)
            {
                IsDoByPeriod = (bool)param["IsDoByPeriod"];
            }
            if (param.ContainsKey("DoByEveryDay_Hour") && param["DoByEveryDay_Hour"] != null)
            {
                DoByEveryDay_Hour = (int)param["DoByEveryDay_Hour"];
            }
            if (param.ContainsKey("DoByPeriod_Minutes") && param["DoByPeriod_Minutes"] != null)
            {
                float minutes;
                float.TryParse(param["DoByPeriod_Minutes"].ToString(), out minutes);
                DoByPeriod_Minutes = minutes;
            }
        }

        private void setProject(Dictionary<string, object> param)
        {
            if (param.ContainsKey("ProjectType"))
            {
                ProjectIdString = "";
                if (param["ProjectType"] != null)
                    ProjectIdString = param["ProjectType"].ToString();
            }
            if (param.ContainsKey("ProjectTypeList"))
            {
                object objProjects = param["ProjectTypeList"];
                if (objProjects is List<object>)
                {
                    ProjectTypeList.Clear();
                    foreach (int id in objProjects as List<object>)
                    {
                        ProjectTypeList.Add(id);
                    }
                }
                else if (objProjects is List<int>)
                {
                    ProjectTypeList = param["ProjectTypeList"] as List<int>;
                }
            }
        }

        private void setCityName(Dictionary<string, object> param)
        {
            if (param.ContainsKey("StrCityNames"))
            {
                strCityNames = "";
                if (param["StrCityNames"] != null)
                    strCityNames = param["StrCityNames"].ToString();
            }
        }

        private void setTime(Dictionary<string, object> param)
        {
            if (param.ContainsKey("DSTimeSet"))
            {
                if (param["DSTimeSet"] == null || param["DSTimeSet"].ToString() == "")
                    dSTimeSet = DateTime.Now.AddMonths(-1);
                else
                {
                    dSTimeSet = DateTime.Parse(param["DSTimeSet"].ToString());
                }
            }
            if (param.ContainsKey("DETimeSet"))
            {
                if (param["DETimeSet"] == null || param["DETimeSet"].ToString() == "")
                    dETimeSet = DateTime.Now;
                else
                {
                    dETimeSet = DateTime.Parse(param["DETimeSet"].ToString());
                }
            }
        }

        private Dictionary<string, object> getMapParam()
        {
            Dictionary<string, object> param = new Dictionary<string, object>();
            foreach (int districtID in mapsDic.Keys)
            {
                param[districtID.ToString()] = mapsDic[districtID].Param;
            }
            return param;
        }

        private void setMapParam(Dictionary<string, object> param)
        {
            foreach (string key in param.Keys)
            {
                MapInfo mapInfo = new MapInfo();
                mapInfo.Param = param[key] as Dictionary<string, object>;
                mapsDic[int.Parse(key)] = mapInfo;
            }
        }

        public bool ResetMap()
        {
            shapeList.Clear();
            int districtID = MainModel.GetInstance().DistrictID;
            if (!mapsDic.ContainsKey(districtID))
            {
                return false;
            }
            MapInfo mapInfo = mapsDic[districtID];
            string mapPathRegion = mapInfo.mapPath;
            string columnNameRegion = mapInfo.columnName;

            shapeList = ShapeHelper.GetShapesFromTable(mapPathRegion, columnNameRegion);
            if (shapeList.Count > 0)
            {
                return true;
            }
            return false;
        }
    }

    public class BackgroundFuncRoadSetting
    {
        private BackgroundFuncRoadSetting()
        {
        }

        private static BackgroundFuncRoadSetting intance = null;

        public static BackgroundFuncRoadSetting GetInstance()
        {
            if (intance == null)
            {
                intance = new BackgroundFuncRoadSetting();
            }
            return intance;
        }

        public int ValidDistance { get; set; } = 50;

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["ValidDistance"] = ValidDistance;
                return param;
            }
            set
            {
                if (value != null && value.Count > 0)
                {
                    Dictionary<string, object> param = value;
                    if (param.ContainsKey("ValidDistance"))
                    {
                        ValidDistance = int.Parse(param["ValidDistance"].ToString());
                    }
                }
            }
        }
    }

    public class BackgroundFuncAreaSetting
    {
        private BackgroundFuncAreaSetting()
        {
        }

        private static BackgroundFuncAreaSetting intance = null;

        public static BackgroundFuncAreaSetting GetInstance()
        {
            if (intance == null)
            {
                intance = new BackgroundFuncAreaSetting();
            }
            return intance;
        }

        public TimeGatherMode areaStatTimeMode { get; set; } = TimeGatherMode.Month;
        public int gatherRedius { get; set; } = 200;
        public int statTimeUnitCount { get; set; } = 2;

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["AreaStatTimeMode"] = (int)areaStatTimeMode;
                param["GatherRedius"] = gatherRedius;
                param["StatTimeUnitCount"] = statTimeUnitCount;
                return param;
            }
            set
            {
                if (value != null && value.Count > 0)
                {
                    Dictionary<string, object> param = value;
                    if (param.ContainsKey("AreaStatTimeMode"))
                    {
                        areaStatTimeMode = (TimeGatherMode)int.Parse(param["AreaStatTimeMode"].ToString());
                    }
                    if (param.ContainsKey("GatherRedius"))
                    {
                        gatherRedius = int.Parse(param["GatherRedius"].ToString());
                    }
                    if (param.ContainsKey("StatTimeUnitCount"))
                    {
                        statTimeUnitCount = int.Parse(param["StatTimeUnitCount"].ToString());
                    }
                }
            }
        }
    }

    public class BackgroundFuncExportSetting
    {
        private BackgroundFuncExportSetting()
        {
        }

        private static BackgroundFuncExportSetting intance = null;

        public static BackgroundFuncExportSetting GetInstance()
        {
            if (intance == null)
            {
                intance = new BackgroundFuncExportSetting();
            }
            return intance;
        }

        public bool AutoExportResult { get; set; } = false;
        public bool IsExportByDay { get; set; } = false;
        public bool IsExportByWeek { get; set; } = true;
        public bool IsExportByMonth { get; set; } = true;
        public bool IsExportByDiyPeriod { get; set; } = false;
        public bool IsExportByAll { get; set; } = false;
        public bool ExportNow { get; set; } = true;
        public bool ExportFirstDay { get; set; } = false;
        public bool IsExportResultMapToWord { get; set; } = false;
        public bool IsExportResultToXls { get; set; } = true;
        public bool IsSaveResultSumByDiffFolder { get; set; } = true;//是否按 “汇聚方式\\时段\\地市” 的路径格式，分文件夹保存结果        
        public string ExportResultFolderPath { get; set; } = "D://网络体检结果";

        public Dictionary<string, object> Param
        {
            get
            {
                Dictionary<string, object> param = new Dictionary<string, object>();
                param["AutoExportResult"] = AutoExportResult;
                param["IsExportByDay"] = IsExportByDay;
                param["IsExportByWeek"] = IsExportByWeek;
                param["IsExportByMonth"] = IsExportByMonth;
                param["IsExportByDiyPeriod"] = IsExportByDiyPeriod;
                param["IsExportByAll"] = IsExportByAll;
                param["ExportNow"] = ExportNow;
                param["ExportFirstDay"] = ExportFirstDay;
                param["ExportResultFolderPath"] = ExportResultFolderPath;
                param["IsExportResultToXls"] = IsExportResultToXls;
                param["IsExportResultMapToWord"] = IsExportResultMapToWord;
                param["IsSaveResultSumByDiffFolder"] = IsSaveResultSumByDiffFolder;
                return param;
            }
            set
            {
                if (value != null && value.Count > 0)
                {
                    Dictionary<string, object> param = value;
                    setParam(param);
                }
            }
        }

        private void setParam(Dictionary<string, object> param)
        {
            if (param.ContainsKey("AutoExportResult"))
            {
                AutoExportResult = (bool)param["AutoExportResult"];
            }
            if (param.ContainsKey("IsExportByDay"))
            {
                IsExportByDay = (bool)param["IsExportByDay"];
            }
            if (param.ContainsKey("IsExportByWeek"))
            {
                IsExportByWeek = (bool)param["IsExportByWeek"];
            }
            if (param.ContainsKey("IsExportByMonth"))
            {
                IsExportByMonth = (bool)param["IsExportByMonth"];
            }
            if (param.ContainsKey("IsExportByDiyPeriod"))
            {
                IsExportByDiyPeriod = (bool)param["IsExportByDiyPeriod"];
            }
            if (param.ContainsKey("IsExportByAll"))
            {
                IsExportByAll = (bool)param["IsExportByAll"];
            }
            if (param.ContainsKey("ExportNow"))
            {
                ExportNow = (bool)param["ExportNow"];
            }
            if (param.ContainsKey("ExportFirstDay"))
            {
                ExportFirstDay = (bool)param["ExportFirstDay"];
            }
            if (param.ContainsKey("ExportResultFolderPath"))
            {
                ExportResultFolderPath = (string)param["ExportResultFolderPath"];
            }
            if (param.ContainsKey("IsExportResultMapToWord"))
            {
                IsExportResultMapToWord = (bool)param["IsExportResultMapToWord"];
            }
            if (param.ContainsKey("IsExportResultToXls"))
            {
                IsExportResultToXls = (bool)param["IsExportResultToXls"];
            }
            if (param.ContainsKey("IsSaveResultSumByDiffFolder"))
            {
                IsSaveResultSumByDiffFolder = (bool)param["IsSaveResultSumByDiffFolder"];
            }
        }
    }

    public enum TimeGatherMode
    {
        Day = 1,
        Week,
        Month,
        Period,
        AllSum
    }
}
