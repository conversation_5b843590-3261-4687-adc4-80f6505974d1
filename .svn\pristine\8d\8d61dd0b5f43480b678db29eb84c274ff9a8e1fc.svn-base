﻿using System;
using System.Collections.Generic;
using System.Text;
using MasterCom.MTGis;
using System.Runtime.Serialization;
using MasterCom.RAMS.Func;
using MasterCom.RAMS.Model;
using System.Drawing;
using System.Drawing.Drawing2D;
using MasterCom.Util;

namespace MasterCom.RAMS.ZTFunc
{
    public class ScanCellCoverLayer : CustomDrawLayer
    {
        static ScanCellCoverLayer()
        {
        }

        public ScanCellCoverLayer(MapOperation mp, string name)
            : base(mp, name)
        {
        }

        public DbPoint GetGSMAntennaEndPoint(Cell cell, DateTime tpDateTime)
        {
            return new DbPoint(cell.Antennas[0].EndPointLongitude, cell.Antennas[0].EndPointLatitude);
        }

        public DbPoint GetTDAntennaEndPoint(TDCell cell, DateTime tpDateTime)
        {
            return new DbPoint(cell.Antenna.EndPointLongitude, cell.Antenna.EndPointLatitude);
        }
        
        private void drawPointLine(float ratio, DbRect dRect, Graphics graphics, TestPoint tp, Cell cell, TDCell tdCell)
        {
            if (tp.Within(dRect.x1, dRect.y1, dRect.x2, dRect.y2))
            {
                Color? color;
                int? size;
                int? symbol;
                if (MainModel.MainForm.GetMapForm().GetDTLayer().CurFlyLinesSerialInfo == null ||
                    !MainModel.MainForm.GetMapForm().GetDTLayer().CurFlyLinesSerialInfo.getStyle(MainModel.MainForm.GetMapForm().GetDTLayer(), tp, out color, out size, out symbol))
                {
                    return;
                }
                if (color != null)
                {
                    float radius = ratio * 20;
                    Brush brush = new SolidBrush((Color)color);
                    DbPoint dPoint = new DbPoint(tp.Longitude, tp.Latitude);
                    PointF point;
                    this.Map.ToDisplay(dPoint, out point);
                    graphics.TranslateTransform(point.X, point.Y);
                    graphics.ScaleTransform(radius, radius);
                    GraphicsPath gp = SymbolManager.GetInstance().Paths[0];
                    graphics.FillPath(brush, gp);
                    graphics.ResetTransform();

                    DbPoint antennaDPoint = null;
                    if (cell != null)
                    {
                        antennaDPoint = GetGSMAntennaEndPoint(cell, tp.DateTime);
                    }
                    else if (tdCell != null)
                    {
                        antennaDPoint = GetTDAntennaEndPoint(tdCell, tp.DateTime);
                    }
                    PointF antennaPointF;
                    this.Map.ToDisplay(antennaDPoint, out antennaPointF);
                    graphics.DrawLine(new Pen((Color)color, 1), point, antennaPointF);
                }
            }
        }

        public override void Draw(System.Drawing.Rectangle clientRect, System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            if (!IsVisible || MainModel.ScanCellCoverInfoList.Count <= 0)
            {
                return;
            }
            
            drawScanCellCover(updateRect, graphics);
        }

        private void drawScanCellCover(System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            float ratio = CustomDrawLayer.GetTestPointDisplayRatio(Map.Scale);
            DbRect dRect;
            Map.FromDisplay(updateRect, out dRect);
            foreach (ScanCellCoverInfo cellInfo in MainModel.ScanCellCoverInfoList)
            {
                if (cellInfo is ScanCellCoverInfo_GSM)
                {
                    ScanCellCoverInfo_GSM cellInfo_GSM = cellInfo as ScanCellCoverInfo_GSM;
                    foreach (ScanCellCoverPointInfo_GSM pntInfo in cellInfo_GSM.TpInfoList)
                    {
                        drawPointLine(ratio, dRect, graphics, pntInfo.TP, cellInfo_GSM.CoverCell, null);
                    }
                }
                else if (cellInfo is ScanCellCoverInfo_TD)
                {
                    ScanCellCoverInfo_TD cellInfo_TD = cellInfo as ScanCellCoverInfo_TD;
                    foreach (ScanCellCoverPointInfo_TD pntInfo in cellInfo_TD.TpInfoList)
                    {
                        drawPointLine(ratio, dRect, graphics, pntInfo.TP, null, cellInfo_TD.CoverTDCell);
                    }
                }
            }
        }
    }

    public class RectGraphiObj
    {
        public System.Drawing.Rectangle UpdateRect { get; set; }
        public Graphics Graphics { get; set; }

        public RectGraphiObj(System.Drawing.Rectangle updateRect, Graphics graphics)
        {
            this.UpdateRect = updateRect;
            this.Graphics = graphics;
        }
    }
}
